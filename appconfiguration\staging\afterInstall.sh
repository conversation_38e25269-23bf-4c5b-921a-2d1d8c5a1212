`chown -R daemon:daemon /opt/bitnami/apps/hrapp`
`find /opt/bitnami/apps/hrapp -type d -exec chmod 755 {} +`
`find /opt/bitnami/apps/hrapp -type f -exec chmod 644 {} +`
`touch /opt/bitnami/apache2/var/cache/mod_pagespeed/cache.flush`
`rm -rf /opt/bitnami/DeploymentQueries`
`mkdir /opt/bitnami/DeploymentQueries`
`cp /opt/bitnami/apps/hrapp/Queries/hrapp.sql /opt/bitnami/DeploymentQueries/`
`cp /opt/bitnami/apps/hrapp/Queries/hrapproles.sql /opt/bitnami/DeploymentQueries/`
`cp /opt/bitnami/apps/hrapp/Queries/appmanager.sql /opt/bitnami/DeploymentQueries/`
`cp /opt/bitnami/apps/hrapp/Queries/events.sql /opt/bitnami/DeploymentQueries/`
`cp /opt/bitnami/apps/hrapp/Queries/STAGING/dbupdate.sh /opt/bitnami/DeploymentQueries/`
`cp /opt/bitnami/apps/hrapp/Queries/STAGING/dbupdate.config /opt/bitnami/DeploymentQueries/`
`chmod 777 -R /opt/bitnami/DeploymentQueries/`
echo "service codedeploy-agent restart" | at -M now + 2 minute;
atq;
