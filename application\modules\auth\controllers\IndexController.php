<?php
//=========================================================================================
//=========================================================================================
/* Program        : IndexController.php 												 *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description    : Contains login, reset password and forget password form				 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions      :                                                                    	 *
 *  Version    Date           Authors                  Description                       *
 *  0.1        30-May-2013    Narmadha,Sandhosh        Initial Version       	         *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Prasanth                 Changes in file for mobiles app   *
 *                                                     1) mobileLoginAction(),           *
 *                                                     2) mobileChangePwdAction()        */
//=========================================================================================
//=========================================================================================

include APPLICATION_PATH."/validations/Validations.php"; 
class Auth_IndexController extends Zend_Controller_Action
{
	protected $_validation          = null;
    protected $_isMobile = null;
    protected $_isDevice = null;
    protected $_hrappMobile = null;
    protected $_checkSession = null;
    protected $_userSession = null;
    protected $_dbPersonal = null;
    protected $_dbCommonFunction = null;

    public function init()
    {
        $this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
        $this->_dbPersonal        = new Employees_Model_DbTable_Personal();
        $this->_dbCommonFunction = new Application_Model_DbTable_CommonFunction();
		
        $session = Zend_Session::getId();
        $switchView = new Zend_Session_Namespace('Switch_View_'.$session);
        $switchView->Switch_View = '';
		$this->_ehrTables      = new Application_Model_DbTable_Ehr();
        
        $this->_isDevice = Zend_Registry::get('DeviceType');
        $this->_isMobile = $this->_hrappMobile->checkDevice();
        $this->_checkSession = $this->_hrappMobile->checkAuth ();
		$this->_validation 	        = new Validations();
        $this->_userSession = $this->_hrappMobile->getUserDetails ();
        
        if ($this->_checkSession && !empty($this->_userSession))
        {
            $this->_logEmpId = $this->_userSession['logUserId'];
        }
    }
    
    /**
     *  Login form
     */
    public function indexAction()
    {
        $checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();
        // get org code
        $orgCode = $this->_ehrTables->getOrgCode();
        // function to get organisation subscribe plan
        $orgSubscribedPlan = $this->_hrappMobile->checkOrganisationSubscribedPlan($orgCode);
        // Check value exists or not
        if(isset($orgSubscribedPlan)){
            $orgSubscribedPlan = $orgSubscribedPlan;
            setcookie('orgSubscribedPlan',$orgSubscribedPlan,time() +(8640000 * 30),'/','', true);
        }
        $expiryTimestamp = time() +(8640000 * 30);
        //Set partner integration value in the cookie
        $partnerId = Zend_Registry::get('partnerid');
        setcookie('partnerid',$partnerId,$expiryTimestamp,'/','', true);
        $orgDetails = Zend_Registry::get('orgDetails');
        $partnerBaseUrl = $orgDetails['Camu_Base_Url'];
        setcookie('partnerbaseurl',$partnerBaseUrl,$expiryTimestamp,'/','', true);
        // get inviteData from query params if exists
        $inviteData = $this->_getParam('inviteData', null);
        if (!$this->_checkSession || empty($this->_userSession) || !$checkSessionAuth)
        {
            $this->_helper->layout->disableLayout();
            setcookie('HrappDepartmentClassificationId',0,time() +(8640000 * 30),'/','', true);
        }
        else
        {
            if ($this->_isMobile)
            {
                $this->view->isMobile = $this->_hrappMobile->findMobile($this->_helper->layout);
            }
            // Check inviteData is exist or not. This if condition execute when the employee accepts the invites from mail
            // They will be redirected to corresponding instance auth page if sesstion not valid otherwise they will be redirected to employee monitoring dashboard
            // This can be happen when the user uses employee monitoring module seprately and employee monitoring with whole HRMS
            if($inviteData) {
                $this->_helper->layout->disableLayout();
                $this->_helper->layout->setLayout('admin_layout');
                $protocol = stripos($_SERVER['SERVER_PROTOCOL'],'https') === 0 ? 'https://' : 'http://';
                if (Zend_Registry::get('Production')) {
                    $redirectionurl = $protocol.$_SERVER['HTTP_HOST'].'/in/productivity-monitoring/activity-dashboard?inviteData='.$inviteData;
                } else {
                    $redirectionurl = 'http://localhost/hrapponline/in/productivity-monitoring/activity-dashboard?inviteData='.$inviteData;
                }
                $front = Zend_Controller_Front::getInstance();
                $response = new Zend_Controller_Response_Http();
                $response->setRedirect($redirectionurl);
                $front->setResponse($response);

            } 
            // If the organization subscribed only Employee monitoring module then during login we have to
            // redirect them to employee monitoring dashboard
            else if($orgSubscribedPlan === 'EMPLOYEEMONITORINGDASHBOARD'){
                $this->_helper->layout->disableLayout();
                $this->_helper->layout->setLayout('admin_layout');
                $protocol = stripos($_SERVER['SERVER_PROTOCOL'],'https') === 0 ? 'https://' : 'http://';
                if (Zend_Registry::get('Production')) {
                    $redirectionurl = $protocol.$_SERVER['HTTP_HOST'].'/in/productivity-monitoring/activity-dashboard';
                } else {
                    $redirectionurl = 'http://localhost/hrapponline/in/productivity-monitoring/activity-dashboard';
                }
                $front = Zend_Controller_Front::getInstance();
                $response = new Zend_Controller_Response_Http();
                $response->setRedirect($redirectionurl);
                $front->setResponse($response);
            }
            // If the organization subscribe whole Recruitment Dashboard plan then they will be redirected to recruitment dashboard
            else if($orgSubscribedPlan === 'RECRUITMENTDASHBOARD'){
                $this->_helper->layout->disableLayout();
                $this->_helper->layout->setLayout('admin_layout');
                $protocol = stripos($_SERVER['SERVER_PROTOCOL'],'https') === 0 ? 'https://' : 'http://';
                if (Zend_Registry::get('Production')) {
                    $redirectionurl = $protocol.$_SERVER['HTTP_HOST'].'/v3/recruitment/dashboard';
                } else {
                    $redirectionurl = 'http://localhost/hrapponline/v3/recruitment/dashboard';
                }
                $front = Zend_Controller_Front::getInstance();
                $response = new Zend_Controller_Response_Http();
                $response->setRedirect($redirectionurl);
                $front->setResponse($response);
            }
            // If the organization subscribe whole HRMS plan then they will be redirected to main dashboard
            else {
                $this->_helper->layout->disableLayout();
                $this->_helper->layout->setLayout('admin_layout');
                $protocol = stripos($_SERVER['SERVER_PROTOCOL'],'https') === 0 ? 'https://' : 'http://';
                if (Zend_Registry::get('Production')) {
                    $redirectionurl = $protocol.$_SERVER['HTTP_HOST'].'/in/';
                } else {
                    $redirectionurl = 'http://localhost/hrapponline/in/';
                }
                $front = Zend_Controller_Front::getInstance();
                $response = new Zend_Controller_Response_Http();
                $response->setRedirect($redirectionurl);
                $front->setResponse($response);
            }
            setcookie('HrappDepartmentClassificationId',0,time() +(8640000 * 30),'/','', true);
        }
        /* If the user comes to the auth page by clicking the email link then we should show the auth page even the multi company setup is enabled */
        $isSigninWithEmailLink = $this->_getParam('signinEmailLink', null);

        /* Get the instance details to present or hide the landing page */
        $instancesDetails = $this->_ehrTables->getChildInstance();
        $showAuth = 0;

        if(isset($_COOKIE['showAuth'])){
            $showAuth = $_COOKIE['showAuth'];
        }
        /* If the child instance is not exist or the child instance is chosen then present the auth page. */
        if(count($instancesDetails) == 1 || $showAuth || $isSigninWithEmailLink) {
            $isProduction = Zend_Registry::get('Production');
            setcookie('isProduction',$isProduction,time() +(8640000 * 30),'/','', true);
            $this->getHelper('viewRenderer')->setViewSuffix('html');
        } else {
            $this->view->instancesDetails = $instancesDetails;
        }
        
        $this->view->isMobile = $this->_hrappMobile->checkDevice();
    }

    /**
     *  to expire the user's session
     */
    public function logoutAction()
    {
        if (isset($_SERVER['HTTP_REFERER']))
        {
            if ($this->_checkSession)
            {
                $users = new Auth_Model_DbTable_EmpUser();
                $users->clearSessionList($this->_logEmpId, 'User Logout');
            }
            
            Zend_Auth::getInstance()->clearIdentity();
            
            Zend_Session::namespaceUnset('Zend_Auth');
        }
        
        $this->_redirect('auth');
    }

    /**
     * To Forget Password in mobile app
     */
    public function lostAction()
    {
        if (isset($_SERVER['HTTP_REFERER']))
        {
            if ($this->_checkSession)
            {
                $this->_redirect('');
            }
            else
            {
                $this->_helper->layout()->disableLayout();
                
                $dbUsers = new Auth_Model_DbTable_EmpUser();
                $lostForm = new Auth_Form_ForgotPassword();
                $ehrTables = new Application_Model_DbTable_Ehr();
		
				$isDomain = Zend_Registry::get('Domain');
				$isDomainArray = explode(".",$isDomain);
                
                $this->view->lostFrm = $lostForm;
                
                if ($this->getRequest()->getPost())
                {
                    $formData = $this->getRequest()->getPost();
                    
                    $formData = $this->_hrappMobile->updateDetailsForm($formData, $lostForm, $this->_helper->layout, 'No');
                    
                    if ($lostForm->isValid($formData))
                    {
                        $lostFormData = $lostForm->getValues();
                        
                        $dbUsers = new Auth_Model_DbTable_EmpUser();
                        
                        $loginDetails = $dbUsers->getLoginDetailsByMail($lostFormData['forgotPwd']);//$loginDetails['Username']=>admin,$loginDetails['Random_Salt'] => e9e933abcfffb8b642dd59823416362e
                        
                        if (!empty($loginDetails))
                        {
                            $basePath = new Zend_View_Helper_BaseUrl();
                            
                            if ($this->_isMobile)
                            {
                                $urlStr = 'http://'.$_SERVER['HTTP_HOST'].$basePath->baseUrl('auth/index/reset').'/user/'.$loginDetails['Username'].'/url/';
                            }
                            else
                            {
                                $urlStr = $_SERVER['HTTP_HOST'].$basePath->baseUrl('auth/index/reset').'/user/'.$loginDetails['Username'].'/url/';
                            }
                            
                            $concatStr = strrev(substr($loginDetails['Random_Salt'],3,8));//$concatStr = e26361432895dd246b8bfffcba339e9e
                            $concatStr .= strrev($loginDetails['Username']);// $concatStr = e26361432895dd246b8bfffcba339e9enimda
                            $date = new DateTime();
                            $concatStr .= strrev($date->getTimestamp());//$date->getTimestamp() => 1369119289 ; $concatStr = e26361432895dd246b8bfffcba339e9enimda9829119631
                            $orgName = $ehrTables->organizationName();
                            $encrypted = $this->encryptDecrypt( $concatStr, 0);
                            
                            try
                            {
//                                <tr><td>Please click on the link below to reset your password:</td></tr>
//											<tr style="font-size:24px;"><td style = "text-align:center;">
//												<h4><a style="text-decoration:none;" href="'.$urlStr.$encrypted.'">Reset Password</a></h4>
//											</td></tr>
											
                                $body = '<table align="center;font-size:16px;">
											<tr><td>You recently requested a new password for '.$orgName.'</td></tr>
											<tr><td>Please note if you did not request a new password, someone may have been trying to access your account without your permission. As long as you do not click the link contained in the email, no action will be taken and your account will remain secure.</td></tr>
											<tr><td>Sincerely,</td></tr>
											<tr><td>'.ucfirst($isDomainArray[0]). ' team</td></tr>
										</table>';
                                $ehrTables = new Application_Model_DbTable_Ehr();
                                
                                $urlstring = $urlStr.$encrypted;
                                
                                $msgDesc = $ehrTables->mailLayout($body, $loginDetails['employee'],$urlstring);
                                
                                if (!empty($msgDesc))
                                {
                                    $sendMail = new Zend_Mail('utf-8');
                                    $sendMail->addTo($lostFormData['forgotPwd'], $loginDetails['employee'])
                                             ->setSubject('Password Reset Notification from '.$orgName);
                                    $sendMail->setBodyHtml($msgDesc);
                                    $sendMail->send();
                                    
									$this->view->result = array('success' => true, 'msg' => 'Message has been sent to your email to reset password', 'type' => 'success');
                                }
                                else
                                {
									$this->view->result = array('success' => false, 'msg' => 'Unable to send message to your email. Try Again', 'type' => 'warning');
                                }
                            }
                            catch(Zend_Mail_Exception $mailEx)
                            {
								$this->view->result = array('success' => false, 'msg' => 'Unable to send message to your email. Try Again', 'type' => 'warning');
                            }
                        }
						else
						{
							$this->view->result = array('success' => false, 'msg' => 'Invalid email', 'type' => 'danger');
                        }
                    }
                    else
                    {
                        $this->view->result = array('success' => false, 'msg' => 'Invalid email', 'type' => 'danger');
                    }
                }
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'danger');
				}
            }
        }
        else
        {
            $this->_redirect('auth');
        }
    }

    /**
     * Reset password action for mobile app
    */
    public function resetAction()
    {
        if ($this->_checkSession)
        {
            $this->_redirect('index');
        }
        else
        {
            //$this->view->isMobile = $this->_hrappMobile->findMobile($this->_helper->layout);            
			$this->_helper->layout->disableLayout();
			
            $users = new Auth_Model_DbTable_EmpUser();
            
            $userName = $this->_getParam('user', null);
            $userName = filter_var($userName, FILTER_SANITIZE_STRING);
			
            $urlStr1 = $this->_getParam('url', null);
            $urlStr = $this->_getParam('url', null);
            $urlStr = filter_var($urlStr, FILTER_SANITIZE_STRIPPED);
            $urlStr = $this->encryptDecrypt($urlStr, 1);
            
            if (!empty($userName) && !empty($urlStr))
            {
                $reversedStr = explode(strrev($userName), $urlStr);//$reversedStr[0] => e26361432895dd246b8bfffcba339e9e, $reversedStr[1] = 9829119631
                
                $lnkTimeStamp = strrev($reversedStr[1]);
                $date = new DateTime();
                $currTimeStamp = $date->getTimestamp();
                $diff = abs($currTimeStamp - $lnkTimeStamp);
                
                $years = floor($diff / (365*60*60*24));
                $months = floor(($diff - $years * 365*60*60*24) / (30*60*60*24));
                $days = floor(($diff - $years * 365*60*60*24 - $months*30*60*60*24)/ (60*60*24));
                
                $lnkSalt = strrev($reversedStr[0]);
                $userNameCount = $users->checkUserDetails($userName, $lnkSalt);
                
                if ($userNameCount > 0 && $days < 15)
                {
                    $resetForm = new Auth_Form_ResetPassword();
                    $resetForm->heading->setValue('Reset Password?');
                    $resetForm->User->setValue($userName);
                    $this->view->resetFrm = $resetForm;
                    $this->view->userName = $userName;
					$this->view->actionurl = $urlStr1;
					
                    //if ($this->getRequest()->getPost())
                    //{						
                    //    if ($resetForm->isValid($this->getRequest()->getPost()))
                    //    {
                    //        $resetFormData = $resetForm->getValues();
                    //        $encryptedPwd = md5($resetFormData['newPwd']);
                    //        //$pwdUpdated = $users->updatePwd(array('Hrapp_Password'=>$encryptedPwd),$userName);
                    //        $pwdUpdated = 0;
                    //        if($pwdUpdated)
                    //        {
                    //            $this->_helper->redirector('index', 'index', 'auth', array('_r'=>1));					
                    //        }
                    //        else
                    //        {								
                    //            $this->view->result = array('a'=>'Unable to reset password!','b'=> 'warning', 'c'=>'warningboxid','d'=>260,'g'=>40);
                    //        }
                    //    }
                    //    else
                    //    {							
                    //        $this->view->result = array('a'=>'Invalid Data!','b'=> 'warning', 'c'=>'warningboxid','d'=>160,'g'=>40);
                    //    }
                    //}
                }
                else
                {
                    $this->_redirect('auth');
                }
            }
            else
            {
                $this->_redirect('auth');
            }
        }
    }
    
    /**
     * To encrypt / decrypt given password
     */ 
    private function encryptDecrypt($string, $decrypt)
    {
        if($decrypt)
        {
            $string = urldecode($string);
            $decrypted = base64_decode($string);
            return $decrypted;
        }else{
            $encrypted = base64_encode($string);
            return urlencode($encrypted);
        }
    }

//    /**
//     * Change password action
//     */
//    public function changePasswordAction()
//    {
//        if ($this->_checkSession)
//        {
//            $users = new Auth_Model_DbTable_EmpUser();
//            $resetForm = new Auth_Form_ResetPassword();
//            $logEmpId = $users->employeeId($this->_loginUsername);
//            $dbAccessRights = new Default_Model_DbTable_AccessRights();
//            $dbAccessRights->refreshUserSessionTimestamp($logEmpId);
//            $resetForm->heading->setValue('Change Password');
//            
//			$employeeId = $this->_getParam('uId', null);
//			$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
//			
//			$sameUserId = $this->_getParam('pId', null);
//			$sameUserId = filter_var($sameUserId, FILTER_SANITIZE_NUMBER_INT);
//			
//            if(empty($employeeId)) // for logged in employee
//            {
//                $userName = $this->_loginUsername;
//                $resetForm->User->setValue($userName);
//            }
//            elseif(!empty($sameUserId) && !empty($employeeId) && $sameUserId == $employeeId && $sameUserId == $logEmpId) //logged in employee whose username is same as password
//            {
//                $userName = $this->_loginUsername;
//                $resetForm->User->setValue($userName);
//                $tdDecorator = array('ViewHelper',
//                array('Description', array('escape' => false, 'tag' => false),array('placement' => Zend_Form_Decorator_Abstract::APPEND, 'tag' => 'em')),
//                array(array('data'=>'HtmlTag'), array('tag' => 'td' )),
//                array('Label', array('escape'=>false,'tag' => 'td')),
//                array(array('row'=>'HtmlTag'),array('tag'=>'tr')));
//                
//                $oldPwd = $resetForm->createElement('password','oldPwd');
//                $oldPwd->setLabel('Old Password <label class="short_explanation">*</label>')
//                ->setAttribs(array('class'=>'validate[required,minSize[1],maxSize[30],custom[pwdChar]] text-input text_fullname','placeholder'=>"Enter Old Password"))
//                ->addFilters(array('StringTrim', 'StripTags'))->setOrder(4)
//                ->addValidator('regex',false,array('/^[\w\#\.\/\-\!\@\$\%\*\&\_\+\=\?]+$/'))
//                ->addValidator(new Zend_Validate_StringLength(array('min' => 6, 'max' => 30)))
//                ->setRequired(true)->setDecorators($tdDecorator);
//                $confirmOldPwd = $resetForm->createElement('password','confirmOldPwd');
//                $confirmOldPwd->setLabel('Confirm Old Password <label class="short_explanation">*</label>')
//                ->setAttribs(array('class'=>'validate[required,equals[oldPwd],minSize[6],maxSize[30],custom[pwdChar]] text-input text_fullname','placeholder'=>"Retype Old Password"))
//                ->addFilters(array('StringTrim', 'StripTags'))->setOrder(5)
//                ->addValidator('regex',false,array('/^[\w\#\.\/\-\!\@\$\%\*\&\_\+\=\?]+$/'))
//                ->addValidator(new Zend_Validate_StringLength(array('min' => 6, 'max' => 30)))
//                ->addValidator(new Zend_Validate_Identical(array('token' => 'oldPwd')))
//                ->setRequired(true)->setDecorators($tdDecorator);
//                $resetForm->addElements(array($oldPwd, $confirmOldPwd));
//            }
//            elseif(!empty($employeeId)) // change password from employee form
//            {
//                $userName = $users->getUserName($employeeId);
//                $resetForm->User->setValue($userName);
//            }
//            elseif(empty($sameUserId))
//            {
//                $this->_helper->redirector('index', 'index', 'default');
//            }
//            
//            $this->view->resetFrm = $resetForm;
//            
//            if ($this->getRequest()->isPost())
//            {
//                $formData = $this->getRequest()->getPost();
//                
//                if ($this->_isMobile)
//                {
//                    $this->view->isMobile = true;
//                    $formData = $this->_hrappMobile->updateDetailsForm($formData, $resetForm, $this->_helper->layout);
//                }
//                
//                if ($resetForm->isValid($formData) && !empty($userName))
//                {
//                    $resetFormData = $resetForm->getValues();
//                    $encryptedPwd = md5($resetFormData['newPwd']);
//                    
//                    if (!empty($sameUserId) && !empty($employeeId) && $sameUserId == $employeeId && $sameUserId == $logEmpId &&
//                        isset($resetFormData['confirmOldPwd']) && isset($resetFormData['oldPwd']))
//                    {
//                        if(md5($userName) == md5($resetFormData['oldPwd']))
//                        {
//                            $pwdUpdated = $users->updatePwd(array('Hrapp_Password'=>$encryptedPwd),$userName);
//                            
//                            if ($pwdUpdated)
//                            {
//                                $this->view->result = array('a'=>'Password has been changed successfully!','b'=> 'success', 'c'=>'','d'=>360,'g'=>40);
//                            }
//                            else
//                            {
//								$this->view->result = array('a'=>"You can't reset the new password as current password",'b'=> 'warning', 'c'=>'','d'=>400,'g'=>40);
//                                //$this->view->result = array('a'=>'Unable to reset password!','b'=> 'warning', 'c'=>'','d'=>260,'g'=>40);
//                            }
//                        }
//                        else
//                        {
//                            $this->view->result = array('a'=>'Invalid Data!','b'=> 'warning', 'c'=>'','d'=>160,'g'=>40);
//                        }
//                    }
//                    else
//                    {
//                        $pwdUpdated = $users->updatePwd(array('Hrapp_Password'=>$encryptedPwd),$userName);
//                        
//                        if ($pwdUpdated)
//                        {
//                            $this->view->result = array('a'=>'Password has been changed successfully!','b'=> 'success', 'c'=>'','d'=>360,'g'=>40);
//                        }
//                        else
//                        {
//							$this->view->result = array('a'=>"You can't reset the new password as current password",'b'=> 'warning', 'c'=>'','d'=>400,'g'=>40);
//                            //$this->view->result = array('a'=>'Unable to reset password!','b'=> 'warning', 'c'=>'','d'=>260,'g'=>40);
//                        }
//                    }
//                }
//                else
//                {
//                    $this->view->result = array('a'=>'Invalid Data!','b'=> 'warning', 'c'=>'','d'=>160,'g'=>40);
//                }
//            }
//        }
//        else
//        {
//            if(Zend_Session::namespaceIsset('lastRequest'))
//            {
//                Zend_Session:: namespaceUnset('lastRequest');
//            }
//            $session = new Zend_Session_Namespace('lastRequest');
//            $session->lastRequestUri = 'employees/employees';
//            
//            $this->_redirect('auth');
//        }
//    }
    
    /**
     * Login action for mobile app
     */
    public function mobileLoginAction()
    {
        if (isset($_SERVER['HTTP_REFERER']) || $this->_dbCommonFunction->isRequestFromMobileApp())
        {
            $this->_helper->layout->disableLayout();

            $formData = null;
            if ($this->_dbCommonFunction->isRequestFromMobileApp()) {
                $body = $this->getRequest()->getRawBody();
                $formData = Zend_Json::decode($body);
            } else {
                $formData = $this->getRequest()->getPost();
            }
            
            if (!$this->_checkSession)
            {
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
                $ajaxContext->addActionContext('mobile-login', 'json')->initContext();
                
                $users = new Auth_Model_DbTable_EmpUser();
                $ehrTables = new Application_Model_DbTable_Ehr();

                if ( $this->getRequest()->isPost() )
                {
                    $checkBillingStatus = $users->checkBillingStatus();
                    
                    if($checkBillingStatus === 'Active' || $checkBillingStatus === 'Onhold')
                    {
                        $auth = Zend_Auth::getInstance();
                        $empEmail = new stdClass; 
                        $empEmail->Emp_Uid = $formData['uid'];
                        $storage = new Zend_Auth_Storage_Session();
                        $storage->write($empEmail);
                        $sessionData = $storage->read();
                        
                        if (!$sessionData)
                        {
                            $this->view->result = array('success' => true, 'msg'=>'Login again','type'=>'info', 'redirect' => 'auth', 'allowUserSignin' => array(), 'email'=>'' , 'loginEmpId'=> '' , 'orgCode'=> '');
                        }
                        else {
                            $allowUserSignin = $this->_hrappMobile->checkAllowUserSignin($formData['email']);
                            
                            if(!empty($allowUserSignin)){
                                if($allowUserSignin['Emp_Status'] === 'Active') {
                                    if($allowUserSignin['Allow_User_Signin']){
                                        if($allowUserSignin['Enable_Sign_In_With_Mobile_No'] == 0){
                                            $employeeId = $allowUserSignin['Employee_Id'];
                                            // get org code
                                            $orgCode = $this->_ehrTables->getOrgCode();
                                            /* Update last logged in time and user name */
                                            $users->updateLastLoggedIn($employeeId, $formData['uid'], $formData['email'], $allowUserSignin['Invitation_Status']);
        
                                            $sessionUrl = new Zend_Session_Namespace('lastRequest');
                                            $redirectUrl = $sessionUrl->lastRequestUri;
                                            $remberSeconds  = 60 * 60 * 24 * 7; // 7 days
        
                                            $users->clearSessionList($employeeId, 'User Login');
                                            
                                            if ($formData['remember_me']==1){
                                                Zend_Session::RememberMe($remberSeconds);
                                            }
                                            else
                                            {
                                                Zend_Session::ForgetMe();
                                            }
                                            
                                            $encrypted = '';
                                            
                                                if($formData['remember_me'] == 'true')
                                                {
                                                    $key = 'Peacock,Mango/07@03';//secret key
        
                                                    if(isset($_COOKIE['AppIdentity']))
                                                    {
                                                        $data = base64_decode($_COOKIE['AppIdentity']);
                                                        
                                                        $iv = substr($data, 0, mcrypt_get_iv_size(MCRYPT_RIJNDAEL_128, MCRYPT_MODE_CBC));
                        
                                                        if(!empty($iv))
                                                        {
                                                            $decrypted = rtrim(
                                                                mcrypt_decrypt(
                                                                    MCRYPT_RIJNDAEL_128,
                                                                    hash('sha256', $key, true),
                                                                    substr($data, mcrypt_get_iv_size(MCRYPT_RIJNDAEL_128, MCRYPT_MODE_CBC)),
                                                                    MCRYPT_MODE_CBC,
                                                                    $iv
                                                                ),
                                                                "\0"
                                                            );
                                                            $decrypt = explode('###',$decrypted);
                                                            
                                                            if($decrypt[0] != $formData['email'] || $decrypt[1] != $formData['password'])
                                                            {
                                                                $loged = 1;
                                                            }
                                                            else
                                                            {
                                                                $loged = 0;
                                                            }
                                                        }
                                                        else
                                                        {
                                                            $loged = 1;
                                                        }
                                                    }
                                                    else
                                                    {
                                                        $loged = 1;
                                                    }
                                                    if($loged)
                                                    {
                                                        $string = $formData['email'].'###'.$formData['password'];
                                                        
                                                        $iv = mcrypt_create_iv(
                                                            mcrypt_get_iv_size(MCRYPT_RIJNDAEL_128, MCRYPT_MODE_CBC),
                                                            MCRYPT_DEV_URANDOM
                                                        );
                                                        
                                                        $encrypted = base64_encode(
                                                            $iv .
                                                            mcrypt_encrypt(
                                                                MCRYPT_RIJNDAEL_128,
                                                                hash('sha256', $key, true),
                                                                $string,
                                                                MCRYPT_MODE_CBC,
                                                                $iv
                                                            )
                                                        );
                                                        
                                                        if($this->_ehrTables->_isProduction)
                                                            setcookie('AppIdentity',$encrypted,time() +(8640000 * 30),'/','', true);
                                                        else
                                                            setcookie('AppIdentity',$encrypted,time() +(8640000 * 30),'/','', true);
                                                    }
                                                }
        
                                            $passKey = "PeacockMango/07@";
                                            $encryptedEmail = $formData['email'];
                                            //remove existing value and assign new one in cookie
                                            setcookie("accessToken", "", time() - 3600,'/','', true);
                                            setcookie("empUid", "", time() - 3600,'/','', true);
                                            setcookie("partnerid", "", time() - 3600,'/', '', true);
                                            setcookie("d_code", "", time() - 3600,'/', '', true);
                                            setcookie("b_code", "", time() - 3600,'/', '', true);

                                            // Get the next day following the current date. Example: 2021-02-17
                                            $nextDayDate = date('Y-m-d',strtotime(date('Y-m-d').'+1 days'));

                                            /** Convert the next day from the 'Y-m-d' format to the 'Y-m-d H:i:s'. By converting this, the time will be 
                                             * considered as '00:00:00'. Example: 2021-02-17 00:00:00 */
                                            $nextDayDateCreate = date_create($nextDayDate);
                                            $nextDayDateTime = date_format($nextDayDateCreate,"Y-m-d H:i:s");

                                            /** Add 90 days to the next day date-time. Example: 2021-05-18 00:00:00  */
                                            $empUidExpiryDateTime = date('Y-m-d H:i:s',strtotime($nextDayDateTime.'+90 days'));

                                            /** Convert it into a unix timestamp using strtotime. Example: 1621276200 */
                                            $empUidExpiryUTCTimestamp = strtotime($empUidExpiryDateTime);

                                            /* for mobile app session we need to set cookie storage accesstoken & empUid */
                                            setcookie('accessToken',$formData['AuthenticationToken'],time() +(3540),'/','', true); //Expiry = 59mins = 3540
                                            // Expiry = 90days. The expiry will be calculated by considering the next day following the current date as start date                               
										    setcookie('empUid',$formData['uid'],$empUidExpiryUTCTimestamp,'/','', true);
                                            //Set partner integration value in the cookie
                                            setcookie("partnerid", Zend_Registry::get('partnerid'),$empUidExpiryUTCTimestamp,'/','', true);
                                            //Set data region value in the cookie
                                            setcookie("d_code", Zend_Registry::get('d_code'),$empUidExpiryUTCTimestamp,'/','', true);
                                            setcookie("b_code", Zend_Registry::get('b_code'),$empUidExpiryUTCTimestamp,'/','', true);
                                            setcookie("showAuth", 0, $empUidExpiryUTCTimestamp,'/','', true);

                                            $timezoneDt = $ehrTables->gettimezoneDateTime(); 

                                            if ( ! $redirectUrl )
                                            {
                                                $this->view->result = array('success' => true, 'msg'=>'Login success', 'type'=>'success', 'redirect' => '','appIdentity'=>$encrypted, 'email'=>$encryptedEmail, 'accessToken' => $formData['AuthenticationToken'], 'empUid'=>$formData['uid'] , 'loginEmpId'=>$employeeId , 'orgCode'=> $orgCode, 'TZDate'=>date('m/d/Y'), 'TZTime'=>$timezoneDt['Time']);
                                            }
                                            else
                                            {
                                                $this->view->result = array('success' => true, 'msg'=>'Login success', 'type'=>'success', 'redirect' => $redirectUrl,'appIdentity'=>$encrypted, 'email'=>$encryptedEmail, 'accessToken' => $formData['AuthenticationToken'], 'empUid'=>$formData['uid'] , 'loginEmpId'=>$employeeId , 'orgCode'=> $orgCode, 'TZDate'=>date('m/d/Y'), 'TZTime'=>$timezoneDt['Time']);
                                            }
                                        } else {
                                            $this->view->result = array('success' => false, 'msg'=>'Sorry, you are not allowed to sign in with email. Please try with mobile number','type'=>'warning', 'redirect' => 'auth', 'allowUserSignin' => array(), 'email'=>'' , 'loginEmpId'=> '' , 'orgCode'=> '');
                                        }
                                    } else {
                                        $this->view->result = array('success' => false, 'msg'=>'Sorry, you are not allowed to access the application. Please contact your admin','type'=>'warning', 'redirect' => 'auth', 'allowUserSignin' => array(), 'email'=>'' , 'loginEmpId'=> '' , 'orgCode'=> '');
                                    }
                                } else {
                                    $this->view->result = array('success' => false, 'msg'=>'Sorry, your account is deactivated. Please contact your admin','type'=>'warning', 'redirect' => 'auth', 'allowUserSignin' => array(), 'email'=>'' , 'loginEmpId'=> '' , 'orgCode'=> '');
                                }
                            } else {
                                $this->view->result = array('success' => false, 'msg'=>'Email address you have provided is not exists in the organization. Please contact your admin','type'=>'warning', 'redirect' => 'auth', 'allowUserSignin' => array(), 'email'=>'' , 'loginEmpId'=> '' , 'orgCode'=> '');
                            }           
                        }
                    } else {
                        if($checkBillingStatus === 'NonComplaint' || $checkBillingStatus === 'Expired') {
                            /* If the login employee is admin/super admin we should present the payment URL,
                             otherwise we should not present the payment URL. */
                            $allowUserSignin = $this->_hrappMobile->checkAllowUserSignin($formData['email']);
                            $isAdmin = 0; 
                            if(!empty($allowUserSignin) && !empty($allowUserSignin['Employee_Id'])) {
                                $dbAccessRights     = new Default_Model_DbTable_AccessRights();
                                $empAccessRights    = $dbAccessRights->employeeAccessRights($allowUserSignin['Employee_Id'], 'Attendance');
                                $isAdmin = $empAccessRights['Admin'] === 'admin' ? 1 : 0;
                            }
                            if($isAdmin) {
                                $getPaymentUrl = $users->getPaymentUrl();
                                $this->view->result = array('success' => false, 'msg'=>"Your payment is overdue. Please complete the payment <a href = '".$getPaymentUrl."'> here</a> to have your account activated", 'type'=>'info' , 'loginEmpId'=> '' , 'orgCode'=> '');
                            } else {
                                $this->view->result = array('success' => false, 'msg'=>"Your organization account is not active. Please contact your administrator", 'type'=>'info' , 'loginEmpId'=> '' , 'orgCode'=> '');
                            }
                        } else {
                            $this->view->result = array('success' => false, 'msg'=>"Your organization account is not active. Please contact your administrator", 'type'=>'info' , 'loginEmpId'=> '' , 'orgCode'=> '');
                        }
                        
                    }
                } else {
                    $this->view->result = array('success' => false, 'msg'=>"Something went wrong. Please contact system admin", 'type'=>'warning' , 'loginEmpId'=> '' , 'orgCode'=> '');
                }
            }
			else{
                  //remove existing value and assign new one in cookie
                  setcookie("accessToken", "", time() - 3600,'/','', true);
                  setcookie("empUid", "", time() - 3600,'/','', true);
                  setcookie("partnerid", "", time() - 3600,'/', '', true);
                  setcookie("d_code", "", time() - 3600,'/', '', true);
                  setcookie("b_code", "", time() - 3600,'/', '', true);

                  // Get the next day following the current date. Example: 2021-02-17
                  $nextDayDate = date('Y-m-d',strtotime(date('Y-m-d').'+1 days'));

                  /** Convert the next day from the 'Y-m-d' format to the 'Y-m-d H:i:s'. By converting this, the time will be 
                   * considered as '00:00:00'. Example: 2021-02-17 00:00:00 */
                  $nextDayDateCreate = date_create($nextDayDate);
                  $nextDayDateTime = date_format($nextDayDateCreate,"Y-m-d H:i:s");

                  /** Add 90 days to the next day date-time. Example: 2021-05-18 00:00:00  */
                  $empUidExpiryDateTime = date('Y-m-d H:i:s',strtotime($nextDayDateTime.'+90 days'));

                  /** Convert it into a unix timestamp using strtotime. Example: 1621276200 */
                  $empUidExpiryUTCTimestamp = strtotime($empUidExpiryDateTime);

                  /* for mobile app session we need to set cookie storage accesstoken & empUid */
                  setcookie('accessToken',$formData['AuthenticationToken'],time() +(3540),'/','', true); //Expiry = 59mins = 3540
                  // Expiry = 90days. The expiry will be calculated by considering the next day following the current date as start date                               
                  setcookie('empUid',$formData['uid'],$empUidExpiryUTCTimestamp,'/','', true);
                  //Set partner integration value in the cookie
                  setcookie("partnerid", Zend_Registry::get('partnerid'),$empUidExpiryUTCTimestamp,'/','', true);
                  //Set data region value in the cookie
                  setcookie("d_code", Zend_Registry::get('d_code'),$empUidExpiryUTCTimestamp,'/','', true);
                  setcookie("b_code", Zend_Registry::get('b_code'),$empUidExpiryUTCTimestamp,'/','', true);
				$this->view->result = array('success' => true, 'msg'=>'Login success', 'type'=>'success', 'redirect' => '', 'email'=>'' , 'loginEmpId'=> '' , 'orgCode'=> '');
			}
        } else {
            $this->view->result = array('success' => false, 'msg'=>"Something went wrong. Please contact system admin", 'type'=>'warning' , 'loginEmpId'=> '' , 'orgCode'=> '');
        }
    }

    /**
     * Login action for mobile app
     */
    public function loginWithMobileNumberAction()
    {
        if (isset($_SERVER['HTTP_REFERER']) || $this->_dbCommonFunction->isRequestFromMobileApp())
        {
            $this->_helper->layout->disableLayout();

            $formData = null;
            if ($this->_dbCommonFunction->isRequestFromMobileApp()) {
                $body = $this->getRequest()->getRawBody();
                $formData = Zend_Json::decode($body);
            } else {
                $formData = $this->getRequest()->getPost();
            }
            
            if (!$this->_checkSession)
            {
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
                $ajaxContext->addActionContext('login-with-mobile-number', 'json')->initContext();
                
                $users = new Auth_Model_DbTable_EmpUser();
                $ehrTables = new Application_Model_DbTable_Ehr();

                if ( $this->getRequest()->isPost() )
                {
                    $checkBillingStatus = $users->checkBillingStatus();

                    if($checkBillingStatus === 'Active' || $checkBillingStatus === 'Onhold')
                    {    
                        $auth = Zend_Auth::getInstance();
                        $empMobileNumber = new stdClass; 
                        $empMobileNumber->Emp_Uid = $formData['uid'];
                        $storage = new Zend_Auth_Storage_Session();
                        $storage->write($empMobileNumber);
                        $sessionData = $storage->read();
                        
                        if (!$sessionData)
                        {
                            $this->view->result = array('success' => true, 'msg'=>'Login again','type'=>'info', 'redirect' => 'auth', 'allowUserSignin' => array() , 'loginEmpId'=> '' , 'orgCode'=> '');
                        }
                        else {
                            $allowUserSignin = $this->_hrappMobile->checkSigninWithMobileNumber($formData['mobileNumber']);
                            
                            if(!empty($allowUserSignin)){
                                if($allowUserSignin['Emp_Status'] === 'Active') {
                                    if($allowUserSignin['Allow_User_Signin']){
                                        if($allowUserSignin['Enable_Sign_In_With_Mobile_No']){
                                            $employeeId = $allowUserSignin['Employee_Id'];
                                            // get org code
                                            $orgCode = $this->_ehrTables->getOrgCode();
                                            
                                            /* Update last logged in time and user name */
                                            $users->updateLastLoggedIn($employeeId, $formData['uid'], $formData['mobileNumber'],  $allowUserSignin['Invitation_Status']);
                                            $users->clearSessionList($employeeId, 'User Login');
                                            $sessionUrl = new Zend_Session_Namespace('lastRequest');
                                            $redirectUrl = $sessionUrl->lastRequestUri;
                                            
                                            //remove existing value and assign new one in cookie
                                            setcookie("accessToken", "", time() - 3600,'/','', true);
                                            setcookie("empUid", "", time() - 3600,'/','', true);
                                            setcookie("partnerid", "", time() - 3600,'/','', true);
                                            setcookie("d_code", "", time() - 3600,'/','', true);
                                            setcookie("b_code", "", time() - 3600,'/','', true);
                                            
                                            // Get the next day following the current date. Example: 2021-02-17
                                            $nextDayDate = date('Y-m-d',strtotime(date('Y-m-d').'+1 days'));

                                            /** Convert the next day from the 'Y-m-d' format to the 'Y-m-d H:i:s'. By converting this, the time will be 
                                             * considered as '00:00:00'. Example: 2021-02-17 00:00:00 */
                                            $nextDayDateCreate = date_create($nextDayDate);
                                            $nextDayDateTime = date_format($nextDayDateCreate,'Y-m-d H:i:s');

                                            /** Add 90 days to the next day date-time. Example: 2021-05-18 00:00:00  */
                                            $empUidExpiryDateTime = date('Y-m-d H:i:s',strtotime($nextDayDateTime.'+90 days'));

                                            /** Convert it into a unix timestamp using strtotime. Example: 1621276200 */
                                            $empUidExpiryUTCTimestamp = strtotime($empUidExpiryDateTime);

                                            /* for mobile app session we need to set cookie storage accesstoken & empUid */
                                            setcookie('accessToken',$formData['AuthenticationToken'],time() +(3540),'/','', true); //Expiry=59mins = 3540 secs
                                            //Expiry = 90days. The expiry will be calculated by considering the next day following the current date as start dates
										    setcookie('empUid',$formData['uid'],$empUidExpiryUTCTimestamp,'/','', true);
                                            //Set partner integration value in the cookie
                                            setcookie("partnerid", Zend_Registry::get('partnerid'),$empUidExpiryUTCTimestamp,'/','', true);
                                            setcookie("d_code", Zend_Registry::get('d_code'),$empUidExpiryUTCTimestamp,'/','', true);
                                            setcookie("b_code", Zend_Registry::get('b_code'),$empUidExpiryUTCTimestamp,'/','', true);
                                            setcookie("showAuth", 0, $empUidExpiryUTCTimestamp,'/','', true);

                                            $timezoneDt = $ehrTables->gettimezoneDateTime(); 
                                            
                                            if ( ! $redirectUrl )
                                            {
                                                $this->view->result = array('success' => true, 'msg'=>'Login success', 'type'=>'success', 'redirect' => '' , 'loginEmpId'=> $employeeId , 'orgCode'=> $orgCode, 'TZDate'=>date('m/d/Y'), 'TZTime'=>$timezoneDt['Time']);
                                            }
                                            else
                                            {
                                                $this->view->result = array('success' => true, 'msg'=>'Login success', 'type'=>'success', 'redirect' => $redirectUrl , 'loginEmpId'=> $employeeId , 'orgCode'=> $orgCode, 'TZDate'=>date('m/d/Y'), 'TZTime'=>$timezoneDt['Time']);
                                            }
                                        } else {
                                            $this->view->result = array('success' => false, 'msg'=>'Sorry, you are not allowed to signin with mobile number. Please try with email address','type'=>'warning', 'redirect' => 'auth', 'allowUserSignin' => array() , 'loginEmpId'=> '' ,'orgCode'=> '');
                                        }
                                    } else {
                                        $this->view->result = array('success' => false, 'msg'=>'Sorry, you are not allowed to access the application. Please contact your admin','type'=>'warning', 'redirect' => 'auth', 'allowUserSignin' => array() , 'loginEmpId'=> '' , 'orgCode'=> '');
                                    }
                                } else {
                                    $this->view->result = array('success' => false, 'msg'=>'Sorry, your account is deactivated. Please contact your admin','type'=>'warning', 'redirect' => 'auth', 'allowUserSignin' => array() , 'loginEmpId'=> '' , 'orgCode'=> '');
                                }
                            } else {
                                $this->view->result = array('success' => false, 'msg'=>'Mobile number you have provided is not exists in the organization. Please contact your admin','type'=>'warning', 'redirect' => 'auth', 'allowUserSignin' => array() , 'loginEmpId'=> '' , 'orgCode'=> '');
                            }           
                        }
                    } else {
                        if($checkBillingStatus === 'NonComplaint' || $checkBillingStatus === 'Expired') {
                            /* If the login employee is admin/super admin we should present the payment URL,
                             otherwise we should not present the payment URL. */
                            $allowUserSignin = $this->_hrappMobile->checkSigninWithMobileNumber($formData['mobileNumber']);
                            $isAdmin = 0; 
                            if(!empty($allowUserSignin) && !empty($allowUserSignin['Employee_Id'])) {
                                $dbAccessRights     = new Default_Model_DbTable_AccessRights();
                                $empAccessRights    = $dbAccessRights->employeeAccessRights($allowUserSignin['Employee_Id'], 'Attendance');
                                $isAdmin = $empAccessRights['Admin'] === 'admin' ? 1 : 0;
                            }
                            if($isAdmin) {
                                $getPaymentUrl = $users->getPaymentUrl();
                                $this->view->result = array('success' => false, 'msg'=>"Your payment is overdue. Please complete the payment <a href = '".$getPaymentUrl."'> here</a> to have your account activated", 'type'=>'info' , 'loginEmpId'=> '' , 'orgCode'=> '');
                            } else {
                                $this->view->result = array('success' => false, 'msg'=>"Your organization account is not active. Please contact your administrator", 'type'=>'info' , 'loginEmpId'=> '' , 'orgCode'=> '');
                            }
                        } else {
                            $this->view->result = array('success' => false, 'msg'=>"Your organization account is not active. Please contact your administrator", 'type'=>'info' , 'loginEmpId'=> '' , 'orgCode'=> '');
                        }
                    }
                } else {
                    $this->view->result = array('success' => false, 'msg'=>"Something went wrong. Please contact system admin", 'type'=>'warning' , 'loginEmpId'=> '' , 'orgCode'=> '');
                }
            }
			else{
                //remove existing value and assign new one in cookie
                setcookie("accessToken", "", time() - 3600,'/','', true);
                setcookie("empUid", "", time() - 3600,'/','', true);
                setcookie("partnerid", "", time() - 3600,'/', '', true);
                setcookie("d_code", "", time() - 3600,'/', '', true);
                setcookie("b_code", "", time() - 3600,'/', '', true);

                // Get the next day following the current date. Example: 2021-02-17
                $nextDayDate = date('Y-m-d',strtotime(date('Y-m-d').'+1 days'));

                /** Convert the next day from the 'Y-m-d' format to the 'Y-m-d H:i:s'. By converting this, the time will be 
                 * considered as '00:00:00'. Example: 2021-02-17 00:00:00 */
                $nextDayDateCreate = date_create($nextDayDate);
                $nextDayDateTime = date_format($nextDayDateCreate,"Y-m-d H:i:s");

                /** Add 90 days to the next day date-time. Example: 2021-05-18 00:00:00  */
                $empUidExpiryDateTime = date('Y-m-d H:i:s',strtotime($nextDayDateTime.'+90 days'));

                /** Convert it into a unix timestamp using strtotime. Example: 1621276200 */
                $empUidExpiryUTCTimestamp = strtotime($empUidExpiryDateTime);

                /* for mobile app session we need to set cookie storage accesstoken & empUid */
                setcookie('accessToken',$formData['AuthenticationToken'],time() +(3540),'/','', true); //Expiry = 59mins = 3540
                // Expiry = 90days. The expiry will be calculated by considering the next day following the current date as start date                               
                setcookie('empUid',$formData['uid'],$empUidExpiryUTCTimestamp,'/','', true);
                //Set partner integration value in the cookie
                setcookie("partnerid", Zend_Registry::get('partnerid'),$empUidExpiryUTCTimestamp,'/','', true);
                //Set data region value in the cookie
                setcookie("d_code", Zend_Registry::get('d_code'),$empUidExpiryUTCTimestamp,'/','', true);
                setcookie("b_code", Zend_Registry::get('b_code'),$empUidExpiryUTCTimestamp,'/','', true);
				$this->view->result = array('success' => true, 'msg'=>'success', 'type'=>'success', 'redirect' => '' , 'loginEmpId'=> '' , 'orgCode'=> '');
			}
        } else {
            $this->view->result = array('success' => false, 'msg'=>"Something went wrong. Please contact system admin", 'type'=>'warning' , 'loginEmpId'=> '' , 'orgCode'=> '');
        }
    }
	
	
    /**
     * Change password action for mobile app
    */
    public function mobileChangePwdAction()
    {
        if (isset($_SERVER['HTTP_REFERER']))
        {
            if ($this->_checkSession)
            {
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
                $ajaxContext->addActionContext('mobile-change-pwd', 'json')->initContext();
                
                $this->view->isMobile = $this->_hrappMobile->findMobile($this->_helper->layout);
            }
            else
            {
                $this->_redirect('auth');
            }
        }
    }
    
    /**
     * mobileResetAction for reset password.
    */
    public function mobileResetAction()
    {
        if (isset($_SERVER['HTTP_REFERER']))
        {			
            if (!$this->_checkSession)
            {                
                $this->_helper->layout->disableLayout();
                
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
                $ajaxContext->addActionContext('mobile-reset', 'json')->initContext();
                
                $users = new Auth_Model_DbTable_EmpUser();
                
                $userName = $this->_getParam('user', null);
                $userName = filter_var($userName, FILTER_SANITIZE_STRING);
                
                $urlStr = $this->_getParam('reseturl', null);
                $urlStr = filter_var($urlStr, FILTER_SANITIZE_STRIPPED);
                $urlStr = $this->encryptDecrypt($urlStr, 1);
         
                if (!empty($userName) && !empty($urlStr))
                {
                    $reversedStr = explode(strrev($userName), $urlStr);//$reversedStr[0] => e26361432895dd246b8bfffcba339e9e, $reversedStr[1] = 9829119631
                    
                    $lnkTimeStamp = strrev($reversedStr[1]);
                    $date = new DateTime();
                    $currTimeStamp = $date->getTimestamp();
                    $diff = abs($currTimeStamp - $lnkTimeStamp);
                    
                    $years = floor($diff / (365*60*60*24));
                    $months = floor(($diff - $years * 365*60*60*24) / (30*60*60*24));
                    $days = floor(($diff - $years * 365*60*60*24 - $months*30*60*60*24)/ (60*60*24));
                    
                    $lnkSalt = strrev($reversedStr[0]);
                    $userNameCount = $users->checkUserDetails($userName, $lnkSalt);
                    
                    if ($userNameCount > 0 && $days < 15)
                    {
                        if ($this->getRequest()->getPost())
                        {
                            $formData = $this->getRequest()->getPost();
         
							$newPassword['value'] = $this->_validation->commonFilters($formData['newPassword']);
							$newPassword['valid'] = $this->_validation->lengthValidation($newPassword, 6, 30, true);
							
                            if ($newPassword['valid'] && !empty($newPassword['value']))
                            {                                
                                $encryptedPwd = md5($newPassword['value']);
								
                                $pwdUpdated = $users->updatePwd(array('Hrapp_Password'=>$encryptedPwd),$userName);
                                
                                if ($pwdUpdated)
                                {
                                    $this->view->result = array('success' => true, 'msg'=>'Password changed successfully', 'type'=>'success', 'redirect' => 'auth');									
                                }
                                else
                                {                                   
									$this->view->result = array('success'=> false, 'msg' => 'Unable to reset password!', 'type' => 'warning');
                                }
                            }
                            else
                            {
								$this->view->result = array('success'=> false, 'msg' => 'Invalid data!', 'type' => 'warning');
                            }
                        }
                    }
                    else
                    {
                        $this->_redirect('auth');
                    }
                }
                else
                {
                    $this->_redirect('auth');
                }		
            }			
        }
    }
    
    /**
     *  mobileLogoutAction action used to logout current session from mobile.
    */
    public function mobileLogoutAction()
    {
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $this->_helper->layout->disableLayout();
            
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('mobile-logout', 'json')->initContext();
            
            Zend_Auth::getInstance()->clearIdentity();
            
            Zend_Session::namespaceUnset('Zend_Auth');

            $this->view->result = array('success' => true, 'msg' => 'Success', 'redirect' => 'auth');
        }
        else
        {
            $this->_redirect('auth');
        }
    }
    
    /* Function to check if the email id is exists, emp status is active and the allow user signup flag is enabled */
    public function checkAllowUserSigninAction()
    {
        if (isset($_SERVER['HTTP_REFERER']) || $this->_dbCommonFunction->isRequestFromMobileApp())
        {
            $this->_helper->layout->disableLayout();

            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('check-allow-user-signin', 'json')->initContext();

            $email = null;
            if ($this->_dbCommonFunction->isRequestFromMobileApp()) {
                $body = $this->getRequest()->getRawBody();
                $formData = Zend_Json::decode($body);
                $email = $formData['email'];
            } else {
               $email = $this->_getParam('email', null);
            }

            $email = filter_var($email, FILTER_SANITIZE_STRING);

            $email  = $this->_validation->emailValidation($email);

            if(!empty($email['value']) && $email['valid'])
            {
                $response = $this->_hrappMobile->checkAllowUserSignin($email['value']);
                if($response == '' || $response == null)
                {
                    $this->view->result = array('success' => false, 'msg' => 'Email address requires to be pre-registered and approved by your organization. Please discuss with your support team', 'orgCode' => '', 'employeeId' => '', 'emMemberStatus' => '','attendanceEnforcedGeoLocation' =>0);
                }
                else
                {
                    if($response['Emp_Status'] != 'Active')
                    {
                        $this->view->result = array('success' => false, 'msg' => 'Sorry, your account is deactivated. Please contact your admin', 'orgCode' => '', 'employeeId' => '', 'emMemberStatus' => '','attendanceEnforcedGeoLocation' =>0);
                    }
                    else if($response['Allow_User_Signin'] == 0)
                    {
                        $this->view->result = array('success' => false, 'msg' => 'Sorry, you are not allowed to access the application. Please contact your admin', 'orgCode' => '', 'employeeId' => '', 'emMemberStatus' => '','attendanceEnforcedGeoLocation' =>0);
                    }
                    else if($response['Enable_Sign_In_With_Mobile_No'] == 1 )
                    {
                        $this->view->result = array('success' => false, 'msg' => 'Sorry, you are not allowed to sign in with email. Please try again with mobile number.', 'orgCode' => '', 'employeeId' => '', 'emMemberStatus' => '','attendanceEnforcedGeoLocation' =>0 );
                    }
                    else
                    {
                        // get org code
                        $orgCode = $this->_ehrTables->getOrgCode();

                        //get the productivity monitoring member status
                        $emMemberStatus = $this->_hrappMobile->getEMMemberStatus($response['Employee_Id']);

                        $this->view->result = array('success' => true, 'msg' => '', 'orgCode' => $orgCode, 'employeeId' => $response['Employee_Id'], 'emMemberStatus' => $emMemberStatus,'attendanceEnforcedGeoLocation' => $response['Attendance_Enforced_GeoLocation']);
                    }
                }
            } else{
                $this->view->result = array('success' => false, 'msg' => 'Please provide valid address', 'orgCode' => '', 'employeeId' => '', 'emMemberStatus' => '');
            }
        }
        else
        {
            $this->_redirect('auth');
        }
    }


    public function mobileNumberExistAction()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']) || $this->_dbCommonFunction->isRequestFromMobileApp())
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('mobile-number-exist', 'html')->initContext();
			
            $mobileNumber = $this->_getParam('MobileNumber', null);
            $mobileNumber = filter_var($mobileNumber, FILTER_SANITIZE_STRIPPED);
			if (!empty($mobileNumber))
            {
                $result = $this->_hrappMobile->checkSigninWithMobileNumber ($mobileNumber);

                if ($result == '' || $result == null)
                {
                    $this->view->result = array('success' => false, 'msg' => 'Mobile number requires to be pre-registered and approved by your organization. Please discuss with your support team','attendanceEnforcedGeoLocation' =>0);
                }
                else
                {
                    
                    if($result['Emp_Status'] != 'Active')
                    {
                        $this->view->result = array('success' => false, 'msg' => 'Sorry, your account is deactivated. Please contact your admin','attendanceEnforcedGeoLocation' =>0);
                    }
                    else if($result['Allow_User_Signin'] == 0)
                    {
                        $this->view->result = array('success' => false, 'msg' => 'Sorry, you are not allowed to access the application. Please contact your admin','attendanceEnforcedGeoLocation' =>0);
                    }
                    else if($result['Enable_Sign_In_With_Mobile_No'] == 0)
                    {
                        $this->view->result = array('success' => false, 'msg' => 'Sorry, you are not allowed to signin with mobile number. Please try again with email.','attendanceEnforcedGeoLocation' =>0);
                    }
                    else{
                        $this->view->result = array('success' => true, 'msg' => '', 'type' => 'info','attendanceEnforcedGeoLocation' => $result['Attendance_Enforced_GeoLocation']);
                    }
                }
            }
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
	}

    /* Function to get the email and password stored in Cookie */
    public function getAppIdentityAction()
    {
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $this->_helper->layout->disableLayout();
            
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('get-app-identity', 'json')->initContext();

            if(isset($_COOKIE['AppIdentity']))
            {    
                $key = 'Peacock,Mango/07@03';//secret key
                
                $data = base64_decode($_COOKIE['AppIdentity']);
                
                $iv = substr($data, 0, mcrypt_get_iv_size(MCRYPT_RIJNDAEL_128, MCRYPT_MODE_CBC));
                
                $decrypted = rtrim(
                    mcrypt_decrypt(
                        MCRYPT_RIJNDAEL_128,
                        hash('sha256', $key, true),
                        substr($data, mcrypt_get_iv_size(MCRYPT_RIJNDAEL_128, MCRYPT_MODE_CBC)),
                        MCRYPT_MODE_CBC,
                        $iv
                    ),
                    "\0"
                );
                
                $decrypt = explode('###',$decrypted);
                
                $email = $decrypt[0];
                $pwd = $decrypt[1];
            }
            else
            {
                $email = '';
                $pwd = '';
            }
            $this->view->result = array('success' => true, 'email' => $email, 'password' => $pwd);
        }
        else
        {
            $this->_redirect('auth');
        }
    }
    
    /* Function to get the firebase credentials */
    public function getFirebaseCredentialsAction()
    {
        if (isset($_SERVER['HTTP_REFERER']) || $this->_dbCommonFunction->isRequestFromMobileApp())
        {
            $this->_helper->layout->disableLayout();
            
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('get-firebase-credentials', 'json')->initContext();
            
            /* get the IP address restriction flag value and whitelisted IP address */
            $ipAddressDetails = $this->_dbCommonFunction->getWhitelistedIpAddress();

            /* If the IP address restriction is valid then fetch all the details otherwise fetch the required details */
            if($ipAddressDetails['IP_Address_Restriction'] == 0 || ($ipAddressDetails['IP_Address_Restriction'] == 1 &&
                !empty($ipAddressDetails['Whitelisted_IP_Address'] ))) {
                
                    $authenticationDetails = array ('firebaseApiKey' => Zend_Registry::get('firebaseApiKey'),
                        'firebaseAuthDomain' => Zend_Registry::get('firebaseAuthDomain'),
                        'firebaseDatabaseURL' => Zend_Registry::get('firebaseDatabaseURL'),
                        'firebaseProjectId' => Zend_Registry::get('firebaseProjectId'),
                        'firebaseStorageBucket' => Zend_Registry::get('firebaseStorageBucket'),
                        'firebaseMessagingSenderId' => Zend_Registry::get('firebaseMessagingSenderId'),
                        'firebaseAppId' => Zend_Registry::get('firebaseAppId'),
                        'domainName' => Zend_Registry::get('Domain'),
                        'domainDetails' => $this->_ehrTables->domainDetails(),
                        'organizationName' => $this->_ehrTables->organizationName(),
                        'isProduction' => Zend_Registry::get('Production'),
                        'authenticationMethods' => $this->_ehrTables->getAuthenticationMethods(),
                        'ipAddressRestriction' => $ipAddressDetails['IP_Address_Restriction'],
                        'whitelistedIPAddress' => $ipAddressDetails['Whitelisted_IP_Address'],
                        'ipAddressAPI' => Zend_Registry::get('clientipUrl'));
                    
            } else {
                $authenticationDetails = array('ipAddressRestriction' => $ipAddressDetails['IP_Address_Restriction'],
                        'whitelistedIPAddress' => $ipAddressDetails['Whitelisted_IP_Address'],
                        'domainName' => Zend_Registry::get('Domain'),
                        'domainDetails' => $this->_ehrTables->domainDetails(),
                        'organizationName' => $this->_ehrTables->organizationName(),
                        'isProduction' => Zend_Registry::get('Production'),
                        'ipAddressRestriction' => 0,
                        'ipAddressAPI' => Zend_Registry::get('clientipUrl') );
            }   
            
            $this->view->result = $authenticationDetails;
        }
    }

     /**
     *  clearSessionThrowLockAction action used to clear the lock flag set by the user.
    */
    public function clearSessionThrowLockAction()
    {
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $this->_helper->layout->disableLayout();
            
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('clear-session-throw-lock', 'json')->initContext();
            
            if ($this->_checkSession)
            {
                $users = new Auth_Model_DbTable_EmpUser();
                
                $users->clearSessionList($this->_logEmpId, 'User Logout');
            }
            
            $this->view->result = array('success' => true, 'msg' => 'Success', 'redirect' => 'auth');
        }
        else
        {
            $this->_redirect('auth');
        }
    }

    public function fnEncrypt($sValue, $sSecretKey)
    {
        $iv = mcrypt_create_iv(
            mcrypt_get_iv_size(MCRYPT_RIJNDAEL_128, MCRYPT_MODE_CBC),
            MCRYPT_DEV_URANDOM
        );
        
        $encrypted = base64_encode(
            $iv .
            mcrypt_encrypt(
                MCRYPT_RIJNDAEL_128,
                hash('sha256', $sSecretKey, true),
                $sValue,
                MCRYPT_MODE_CBC,
                $iv
            )
        );
        return $encrypted;
    }


    public function childInstanceListAction() {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']) || $this->_dbCommonFunction->isRequestFromMobileApp()) {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('child-instance-list', 'json')->initContext();

            if ($this->getRequest()->isPost()) {
                $orgCode = null;
                if ($this->_dbCommonFunction->isRequestFromMobileApp()) {
                    $body = $this->getRequest()->getRawBody();
                    $formData = Zend_Json::decode($body);
                    $orgCode = $formData['orgCode'];
                } else {
                    $orgCode = $this->_getParam('orgCode', null);
                }

                /* Get the instance details to present or hide the landing page */
                if (!empty($orgCode))
                {
                    $instancesDetails = $this->_ehrTables->getChildInstance($orgCode);
                    $this->view->result = $instancesDetails;
                } else {
                    $this->view->result = array();
                }
            }
        }
        else
        {
            $this->_redirect('auth');
        }
    }
   
    public function getAuthenticationMethodsAction() {
        if (isset($_SERVER['HTTP_REFERER']) || $this->_dbCommonFunction->isRequestFromMobileApp()) {
            $this->_helper->layout->disableLayout();
        
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('get-authentication-methods', 'json')->initContext();

            $orgCode = null;
            $isOrgCodeValid = true;
            /* If the request is from the mobile app then get the org code from the request body */
            if ($this->_dbCommonFunction->isRequestFromMobileApp()) {
                $body = $this->getRequest()->getRawBody();
                $formData = Zend_Json::decode($body);
                $orgCode = $formData['orgCode'];
                $orgCode = filter_var($orgCode, FILTER_SANITIZE_STRIPPED);
                if(empty($orgCode)){
                    $isOrgCodeValid = false;
                }
            }  

			if ($isOrgCodeValid)
            {
                $getAuthenticationMethods = $this->_ehrTables->getAuthenticationMethods($orgCode);
                $this->view->result = $getAuthenticationMethods;
            } else {
                $this->view->result = array();
            }
        }
    }

    /* Function to get the whitelisted ip address */
    public function listWhitelistedIpaddressAction () 
    {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']) || $this->_dbCommonFunction->isRequestFromMobileApp())
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-whitelisted-ipaddress', 'json')->initContext();

            $orgCode = null;
            $isOrgCodeValid = true;

            /* org code is required if the request is received from the mobile app */
            if ($this->_dbCommonFunction->isRequestFromMobileApp()) {
                $body = $this->getRequest()->getRawBody();
                $formData = Zend_Json::decode($body);
                $orgCode = $formData['orgCode'];
                $orgCode = filter_var($orgCode, FILTER_SANITIZE_STRIPPED);
                if(empty($orgCode)){
                    $isOrgCodeValid = false;
                }
            }  

			if ($isOrgCodeValid)
            {
                $dbCommonFunction = new Application_Model_DbTable_CommonFunction();

                $this->view->result = $dbCommonFunction->getWhitelistedIpAddress($orgCode); 
            } else {
                $this->view->result = array();
            }
        }
        else
        {
            $this->_redirect('auth');
        }

    }
}