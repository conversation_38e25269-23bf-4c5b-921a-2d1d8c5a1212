<?php
//==========================================================================================
//==========================================================================================
/* Program : Employee.php											   			          *
 * Property of Caprice Technologies Pvt Ltd,                                              *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                       *
 * Coimbatore, Tamilnadu, India.														  *
 * All Rights Reserved.            														  *
 * Use of this material without the express consent of Caprice Technologies               *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law.  *
 *                                                                                    	  *
 * Description : MQL Query to retrive, add, update employee details and also to update    *
 * employee access rights.																  *
 *                                                                                   	  *
 *                                                                                    	  *
 * Revisions :                                                                    	      *
 *  Version    Date           Author                  Description                         *
 *  0.1        30-May-2013    Devirani            	  Initial Version        	          *
 *  0.2        14-May-2014    Mahesh                  Modified Function					  *
 *                                                    1.updateEmp                         *
 *																						  *
 *  0.3		   18-Sep-2014    Mahesh				  Modified Functions				  *
 *													  1.searchEmpDetails 				  *
 *																						  *
 *  1.0        02-Feb-2015    Sandhosh                Changes in file for mobile app      *
 *                                                    1.Extra fields are added in         *
 *                                                    field list of list query.           *
 *                                                                                        *
 *  1.4        05-Mar-2016    Nivethitha              Changes in file for Bootstrap       *
 *                                                                                        *
 *  1.5        12-Aug-2016     Shobana                 Added PAN and TAN                  */	
//==========================================================================================
//==========================================================================================

class Employees_Model_DbTable_Employee extends Zend_Db_Table_Abstract
{
    protected $_dbPersonal    = null;
    protected $_db            = null;
    protected $_ehrTables     = null;
    protected $_salesDb       = null;
    protected $_orgDF         = null;
    protected $_dbJob         = null;
    protected $_dbLeave       = null;
	protected $_isMobile      = null;
    protected $_dbFinancialYr = null;
	protected $_dbCommonFun   = null;
	protected $_basePath      = null;
	protected $_orgDetails    = null;
	protected $_partnerId     = null;
    public function init()
    {
        $this->_ehrTables     = new Application_Model_DbTable_Ehr();
        $this->_db            = Zend_Registry::get('subHrapp');
        $this->_salesDb       = Zend_Registry::get('Hrapp');
        $this->_dbPersonal    = new Employees_Model_DbTable_Personal();
        $this->_dbJob         = new Employees_Model_DbTable_JobDetail();
		$this->_dbLeave       = new Employees_Model_DbTable_Leave();
	    $this->_dbFinancialYr = new Default_Model_DbTable_FinancialYear();
		$this->_hrappMobile   = new Application_Model_DbTable_HrappMobile();
		$this->_isMobile      = $this->_hrappMobile->checkDevice();
		$this->_orgDF         = $this->_ehrTables->orgDateformat('php');
		$this->_dbCommonFun   = new Application_Model_DbTable_CommonFunction();
		$this->_basePath      = new Zend_View_Helper_BaseUrl();
		$this->_orgDateFormat = $this->_ehrTables->orgDateformat();
		$this->_orgDetails    = Zend_Registry::get('orgDetails');
		$this->_partnerId     = Zend_Registry::get('partnerid');

    }
	
	public function getFormId()
    {
    	$formId = $this->_db->fetchCol($this->_db->select()->from(array('empAccessRights'=>$this->_ehrTables->forms),
						       array('empAccessRights.Form_Id')));
     	return $formId;
    }
    
    //version: 0.3 => manager can view his record and his reportees record only, admin can view all records, employee(not a manager) can view his record only
    /**
     *
     * This function is to list employee details in a grid
     * based on user access rights
     */
    public function listEmployeeDetails ($page, $rows, $sortField, $sortOrder, $searchAll=null, $searchArr, $empUser/*, $selfUser*/)
    {
		$employeeName = $searchArr['employeeName'];
		$managerName  = $searchArr['managerName'];
		$designation  = $searchArr['designation'];
		$department   = $searchArr['department'];
        $location     = $searchArr['location'];
		$empType      = $searchArr['empType'];
		$status       = $searchArr['status'];
        $category     = $searchArr['category'];
		$serviceProviderId = $searchArr['serviceProviderId'];
		
		switch($sortField)
        {
		    case 1: $sortField = 'job.User_Defined_EmpId'; break;
			case 2: $sortField = 'personal.Emp_First_Name'; break;
			case 3: $sortField = 'designation.Designation_Name'; break;
			case 4: $sortField = 'department.Department_Name'; break;
            case 5: $sortField = 'loc.Location_Name'; break;
			case 6: $sortField = 'job.Emp_Status'; break;
		}
		
		$qryEmployee = $this->_db->select()->distinct()
							->from(array('personal'=>$this->_ehrTables->empPersonal),
								   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS personal.Employee_Id as count'),
										 'Employee_Name'=>new Zend_Db_Expr("CONCAT(personal.Emp_First_Name,' ',personal.Emp_Last_Name)"),
										 'DT_RowClass' => new Zend_Db_Expr('"employees"'), 
										 'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', personal.Employee_Id)"),
										 'Log_Id'=>new Zend_Db_Expr("'".$empUser['LogId']."'"),
										 'SuperAdmin'=>new Zend_Db_Expr("'".$empUser['SuperAdmin']."'"),
										 'View'=>new Zend_Db_Expr("'".$empUser['View']."'"),
										 'Update'=>new Zend_Db_Expr("'".$empUser['Update']."'"),
										 'Delete'=>new Zend_Db_Expr("'".$empUser['Delete']."'"),
										 'User1'=>new Zend_Db_Expr("'".$empUser['Admin']."'"),'personal.Form_Status','personal.Employee_Id',
										 'personal.Is_Manager','personal.PAN','personal.Aadhaar_Card_Number','personal.UAN','personal.Allow_User_Signin',
										 'personal.Work_Email','personal.Enable_Sign_In_With_Mobile_No','personal.Sign_In_Mobile_Number',
										 'personal.Sign_In_Mobile_No_Country_Code'))
							
							->joinLeft(array('job'=>$this->_ehrTables->empJob),'personal.Employee_Id=job.Employee_Id',array('job.Employee_Id As empId','job.Designation_Id',
											'job.Department_Id','job.Location_Id','job.Date_Of_Join','job.Probation_Date','job.Emp_Email','job.Manager_Id','job.Confirmed','job.Confirmation_Date','job.Emp_Profession','job.Emp_Status',
											new Zend_Db_Expr("DATE_FORMAT(personal.DOB,'".$this->_orgDateFormat['sql']."') as DOB"),
											 new Zend_Db_Expr("DATE_FORMAT(job.Date_Of_Join,'".$this->_orgDateFormat['sql']."') as Date_Of_Join"),
											 new Zend_Db_Expr("DATE_FORMAT(job.Probation_Date,'".$this->_orgDateFormat['sql']."') as Probation_Date"),
											 new Zend_Db_Expr("DATE_FORMAT(job.Confirmation_Date,'".$this->_orgDateFormat['sql']."') as Confirmation_Date"),
											'job.Emp_InActive_Date','Clone_Min_Active_Date'=>new Zend_Db_Expr("(job.Emp_InActive_Date + INTERVAL 1 DAY)"),
											'job.EmpType_Id','job.Commission_Employee','job.Work_Schedule','job.Trigger_Regime_Change','job.Tax_Regime',
											'Previous_Employee_Experience_Years' => new Zend_Db_Expr('FLOOR(CASE WHEN job.Previous_Employee_Experience IS NULL THEN 0 ELSE job.Previous_Employee_Experience/12 END)'),
											'Previous_Employee_Experience_Months' => new Zend_Db_Expr('CASE WHEN job.Previous_Employee_Experience IS NULL THEN 0 ELSE job.Previous_Employee_Experience%12 END'),
										    'job.External_EmpId','job.Pf_PolicyNo','job.TDS_Exemption','job.Reason_Id','User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN job.User_Defined_EmpId IS NULL THEN personal.Employee_Id ELSE job.User_Defined_EmpId END')))

							->joinLeft(array('SP' => $this->_ehrTables->serviceProvider),'job.Service_Provider_Id = SP.Service_Provider_Id',array('job.Service_Provider_Id','SP.Service_Provider_Name'))
						
							->joinLeft(array('work' => $this->_ehrTables->workSchedule),'job.Work_Schedule = work.WorkSchedule_Id',array('Title'))
                            ->joinLeft(array('profession' => $this->_ehrTables->empProfession),'job.Emp_Profession = profession.Profession_Id',array('Profession_Name'))
							->joinLeft(array('empPersonal'=>$this->_ehrTables->empPersonal),'job.Manager_Id=empPersonal.Employee_Id',
									   array('Manager_Name'=>new Zend_Db_Expr("CONCAT(empPersonal.Emp_First_Name,' ',empPersonal.Emp_Last_Name)")))
							
							->joinLeft(array('designation'=>$this->_ehrTables->designation), 'job.Designation_Id=designation.Designation_Id',
									   array('Designation_Name', 'Designation_Name as Desig_Name','Designation_Id'))
							
							->joinLeft(array('department'=>$this->_ehrTables->dept),'job.Department_Id=department.Department_Id',
									   array('Department_Name'))
							
							->joinLeft(array('empType'=>$this->_ehrTables->empType),'job.EmpType_Id=empType.EmpType_Id',
									   array('Employee_Type'))
							
							->joinLeft(array('loc'=>$this->_ehrTables->location),'loc.Location_Id=job.Location_Id',
									   array('Location_Name','loc.Country_Code as Location_Country_Code'))
							 ->joinLeft(array('con'=>$this->_ehrTables->empContacts), 'personal.Employee_Id=con.Employee_Id',
						      array('Mobile_No'))
							
							->joinLeft(array('DLA' =>$this->_ehrTables->departmentLevel),'personal.Employee_Id = DLA.Employee_Id',array(''))
							
							->joinLeft(array('D1'=>$this->_ehrTables->dept),'DLA.Department_Id=D1.Department_Id',
									   array('Mapped_Department'=>new Zend_Db_Expr('GROUP_CONCAT(DISTINCT D1.Department_Name order by D1.Department_Name Asc)'),
									  		 'deptLevelId'=>new Zend_Db_Expr('GROUP_CONCAT(DISTINCT DLA.Department_Id order by DLA.Department_Id Asc)')))
							->group('personal.Employee_Id')	
						
							->order("$sortField $sortOrder")
							->limit($rows, $page);
							
		
		/**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
            $conditions = $this->_db->quoteInto(new Zend_Db_Expr('Concat(personal.Emp_First_Name," ",personal.Emp_Last_Name) Like ?'),"%$searchAll%");
			$conditions .= $this->_db->quoteInto('or job.User_Defined_EmpId Like ?', "%$searchAll%");
            $conditions .= $this->_db->quoteInto('or designation.Designation_Name Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or department.Department_Name Like ?', "%$searchAll%");
            $conditions .= $this->_db->quoteInto('or loc.Location_Name Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or job.Emp_Status Like ?', "%$searchAll%");
		    
            if(substr_count("yes",strtolower($searchAll)) > 0)
			{
		        $conditions .= $this->_db->quoteInto('or personal.Is_Manager Like ?','1');
                $conditions .= $this->_db->quoteInto('or job.Commission_Employee Like ?', '1');
	            $conditions .= $this->_db->quoteInto('or job.TDS_Exemption Like ?','1');  
			}
			
			if(substr_count("no",strtolower($searchAll)) > 0)
			{
                $conditions .= $this->_db->quoteInto('or personal.Is_Manager Like ?','0');
                $conditions .= $this->_db->quoteInto('or job.Commission_Employee Like ?', '0');
	            $conditions .= $this->_db->quoteInto('or job.TDS_Exemption Like ?','0');
			}
            
			$conditions .= $this->_db->quoteInto(new Zend_Db_Expr('or Concat(empPersonal.Emp_First_Name," ",empPersonal.Emp_Last_Name) Like ?'),"%$searchAll%");
		    $conditions .= $this->_db->quoteInto('or empType.Employee_Type Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or job.Emp_Email Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or job.Date_Of_Join = ?',date('Y-m-d',strtotime($searchAll)));
			$conditions .= $this->_db->quoteInto('or work.Title Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or job.External_EmpId Like ?', "%$searchAll%"); 
			$conditions .= $this->_db->quoteInto('or profession.Profession_Name Like ?', "%$searchAll%");  
			$conditions .= $this->_db->quoteInto('or job.User_Defined_EmpId Like ?', "%$searchAll%");    
			$conditions .= $this->_db->quoteInto('or job.Probation_Date  = ?',date('Y-m-d',strtotime($searchAll)));
			$conditions .= $this->_db->quoteInto('or job.Confirmed Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or job.Confirmation_Date = ?',date('Y-m-d',strtotime($searchAll)));
			$conditions .= $this->_db->quoteInto('or con.Mobile_No Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or personal.Aadhaar_Card_Number Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or personal.PAN Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or personal.UAN Like ?', "%$searchAll%");
            $conditions .= $this->_db->quoteInto('or job.Pf_PolicyNo Like ?', "%$searchAll%");

            $qryEmployee->where($conditions);
		}
							
        if(empty($empUser['Admin']))
        {
			/*
            To get employee access for manager. If it returns 1, then they can't access employees bank account details.
            */
			$restrictEmpAccessForManager = $this->_orgDetails['Restrict_Emp_Access_For_Manager'];
			
        	if ( $empUser['Is_Manager'] == 1 && $restrictEmpAccessForManager != 1) 
        	{
        		$qryEmployeeId = $this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
										->where('Manager_Id = ?', $empUser['LogId']);
        		
        		$qryEmployee->where('personal.Employee_Id = :EmpId or personal.Employee_Id IN (?)', $qryEmployeeId)
							->bind(array('EmpId'=>$empUser['LogId']));
        	}
        	else 
        	{
        		$qryEmployee->where('personal.Employee_Id = ?', $empUser['LogId']);
        	}
        }
        
		if (!empty($department))
        {
			/** Single level hierarchy - check that the filtered department id is parent type id or not **/
			$IsDepParentId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->dept,array('Parent_Type_Id'))
                                                    ->where('Department_Id = ?',$department));
				
			/** If the filtered department is parent id **/
			if(empty($IsDepParentId))
			{
				/** get all the child department ids for the filtered parent department **/
				$getDepChildIds = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->dept,
																   array('Department_Id'))
															->where('Parent_Type_Id = ?',$department));
				
				/** If child department exists **/
				if(!empty($getDepChildIds))
				{
					/** include parent type id **/
					array_push($getDepChildIds,$department);
					
					$qryEmployee->where('job.Department_Id IN (?)',$getDepChildIds);
				}
				else
				{
					$qryEmployee->where('job.Department_Id = ?', $department);
				}
			}
			else
			{
				/** If the filtered department is not parent type id**/
				$qryEmployee->where('job.Department_Id = ?', $department);
			}
        }
		
        if (!empty($designation))
        {
            $qryEmployee->where('job.Designation_Id = ?', $designation);
        }
    
        if (!empty($location))
        {
            $qryEmployee->where('loc.Location_Id = ?', $location);
        }
            
		if (!empty($empType))
        {
            $qryEmployee->where('job.EmpType_Id = ?', $empType);
        }
        
		if(!empty($status) && preg_match('/^[a-zA-Z]+$/', $status))
        {
			$qryEmployee->where('job.Emp_Status = ?', "$status");			
        }
		
        if(!empty($employeeName)/* && preg_match('/^[a-zA-Z]+$/', $employeeName)*/)
        {
			//$qryEmployee->where($this->_db->quoteInto('personal.Emp_First_Name Like ? or ', "%$employeeName%").
			//			$this->_db->quoteInto('personal.Emp_Last_Name Like ?', "%$employeeName%"));
            $qryEmployee->where($this->_db->quoteInto(new Zend_Db_Expr('Concat(personal.Emp_First_Name," ",personal.Emp_Last_Name) Like ?'),"%$employeeName%"));
            
		}
		
        if(!empty($managerName) /*&& preg_match('/^[a-zA-Z]+$/', $managerName)*/)
        {
			//$qryEmployee->where($this->_db->quoteInto('empPersonal.Emp_First_Name Like ? or ', "%$managerName%").
			//			$this->_db->quoteInto('empPersonal.Emp_Last_Name Like ?', "%$managerName%"));
            $qryEmployee->where($this->_db->quoteInto(new Zend_Db_Expr('Concat(empPersonal.Emp_First_Name," ",empPersonal.Emp_Last_Name) Like ?'),"%$managerName%"));
		}
        
        if(!empty($category) && $category == "Manager")
        {
            $qryEmployee->where('personal.Is_Manager = ?', 1);
        }
        else if(!empty($category) && $category == "NonManager")
        {
            $qryEmployee->where('personal.Is_Manager = ?', 0);
        }
        
		if(!empty($serviceProviderId)&& $this->_orgDetails['Field_Force']==1)
        {
            $qryEmployee->where('job.Service_Provider_Id = ?',$serviceProviderId);
        }

        $qryEmployee = $this->_dbCommonFun->getDivisionDetails($qryEmployee,'job.Department_Id');
		
		if(!empty($empUser['Admin']))
		{
			$qryEmployee = $this->_dbCommonFun->formServiceProviderQuery($qryEmployee,'job.Service_Provider_Id',$empUser['LogId']);
		}
		/**
		 * SQL queries
		 * Get data to display
		*/
		$employee = $this->_db->fetchAll($qryEmployee);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		/* Total data set length */
		$qryEmployeeCnt = $this->_db->select()->from($this->_ehrTables->empPersonal, new Zend_Db_Expr('COUNT(Employee_Id)'));
		
		if(empty($empUser['Admin']))
        {
        	if ( $empUser['Is_Manager'] == 1) 
        	{
        		$qryEmployeeId = $this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
										->where('Manager_Id = ?', $empUser['LogId']);
        		
        		$qryEmployeeCnt->where('Employee_Id = :EmpId or Employee_Id IN (?)', $qryEmployeeId)
							->bind(array('EmpId'=>$empUser['LogId']));
        	}
        	else 
        	{
        		$qryEmployeeCnt->where('Employee_Id = ?', $empUser['LogId']);
        	}
        }
		
		$iTotal = $this->_db->fetchOne($qryEmployeeCnt);
	
		$dbAccessRights = new Default_Model_DbTable_AccessRights();	
		foreach ($employee as $key => $row)
		{
			$employeeAdmin = $dbAccessRights->employeeAccessRights($employee[$key]['Employee_Id'],'Employees');
			$payrollAdmin = $dbAccessRights->employeeAccessRights($employee[$key]['Employee_Id'],'Salary');
			if(($employeeAdmin['Admin']==='admin' || $payrollAdmin['Admin']==='admin'))
			{	

				$employee[$key]['Employee_Role'] = 'admin';
			}
			else 
			{
				$employee[$key]['Employee_Role'] ='';
			}
			
		}
		/**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $employee);
	}
	
	/**
     * This function is to fetch employee inactive reason (ESIC Reason)
     */
    public function getESICReason()
    {
		return $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->esicReason, array('Reason_Id as value', 'ESIC_Reason as text'))
											->where('Form_Id = ?', 19)
											->group('Reason_Id')
											->order('ESIC_Reason ASC'));
    }
	
	public function getESICReasonPair()
    {
        return $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->esicReason, array('Reason_Id', 'ESIC_Reason'))
									  ->where('Form_Id = ?', 19)
									  ->group('Reason_Id')
									  ->order('ESIC_Reason ASC'));
    }
	
	/**
	 * Check whether login details exists or not for a given employeeId
	 */
    public  function ckLoginExists ($employeeId)
    {
        return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empLogin, new Zend_Db_Expr('Count(User_Name)'))
									->where('Employee_Id = ?',$employeeId));
    }
    public function addEmployees ($formData,$sessionId)
    {
	
		$dbAllowance   = new Payroll_Model_DbTable_Allowances();
		$dbInsurance   = new Payroll_Model_DbTable_Insurance();
		$dbDesignation = new Employees_Model_DbTable_Designation();
		$dbEmpType     = new Employees_Model_DbTable_EmployeeType();
		$dbGrade       = new Employees_Model_DbTable_Grade();
			
		$isUpdated = 0;
		$msg = '';
	
	
	
		$personalData = $formData['personalDataArr'];

		$updated = $this->_db->insert ($this->_ehrTables->empPersonal, $personalData);
		
		/**
		 *	update employee hobby details
		*/
		$employeeId =$this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empPersonal, array(new Zend_Db_Expr('Max(Employee_Id)'))));
		
		$isHobbiesExist = $this->_db->fetchOne($this->_db->select()
											   ->from($this->_ehrTables->empHobbies, new Zend_Db_Expr('Count(Employee_Id)'))
											   ->where('Employee_Id = ?', $employeeId));
		
		if ($isHobbiesExist)
		{
			if (array_key_exists('hobbyDataArr', $formData))
			{
			        $formData['hobbyDataArr']['Employee_Id']=$employeeId;
				$updated = $this->_db->update ($this->_ehrTables->empHobbies, $formData['hobbyDataArr'], array('Employee_Id = '.$employeeId));
			}
			else
			{
				$updated = $this->_db->delete($this->_ehrTables->empHobbies, 'Employee_Id = '.(int)$employeeId);
			}
			
			if ($updated)
				++$isUpdated;
		}
		else
		{
			if (array_key_exists('hobbyDataArr', $formData))
			{
				$formData['hobbyDataArr']['Employee_Id']=$employeeId;
				$updated = $this->_db->insert ($this->_ehrTables->empHobbies, $formData['hobbyDataArr']);
				
				if ($updated)
					++$isUpdated;
			}
		}
		
		/**
		 *	update employee languages known details
		*/
		$this->_db->delete($this->_ehrTables->empLang, 'Employee_Id='.(int)$employeeId);
		
		if (array_key_exists('languageDataArr', $formData))
		{
			$langKnown = $formData['languageDataArr'];
			
			if (!empty($langKnown) && count ($langKnown) > 0)
			{
				$langCount = count($langKnown);
				$langData = array();
				
				foreach ($langKnown as $key => $row)
				{
					$langData[$key] = array('Employee_Id' => $employeeId, 'Lang_Known' => $row);
				}
				
				if (!empty($langData))
				{
					$updated = $this->_ehrTables->insertMultiple ($this->_ehrTables->empLang, $langData);
					
					if ($updated)
						++$isUpdated;
				}
			}
		}
		
		/**
		 *	Check license is already exist or not before Update or insert license details
		*/
		$isLicenseExist = $this->_db->fetchOne($this->_db->select()
											   ->from($this->_ehrTables->empDrivingLicense, new Zend_Db_Expr('Count(Employee_Id)'))
											   ->where('Employee_Id = ?', $employeeId));
		
		if ($isLicenseExist)
		{
			if (array_key_exists('licenseDataArr', $formData))
			{			        
				$updated = $this->_db->update ($this->_ehrTables->empDrivingLicense, $formData['licenseDataArr'], array('Employee_Id = '.$employeeId));
			}
			else
			{
				$updated = $this->_db->delete($this->_ehrTables->empDrivingLicense, 'Employee_Id='.(int)$employeeId);
			}
			
			if ($updated)
				++$isUpdated;
		}
		else
		{
			if (array_key_exists('licenseDataArr', $formData))
			{
			    $formData['licenseDataArr']['Employee_Id']=$employeeId;
				$updated = $this->_db->insert ($this->_ehrTables->empDrivingLicense, $formData['licenseDataArr']);
				
				if ($updated)
					++$isUpdated;
			}
		}
		
		/**
		 *	Check passport is already exist or not before Update or insert passport details
		*/
		$isPassportExist = $this->_db->fetchOne($this->_db->select()
											   ->from($this->_ehrTables->empPassport, new Zend_Db_Expr('Count(Employee_Id)'))
											   ->where('Employee_Id = ?', $employeeId));
		
		if ($isPassportExist)
		{
			if (array_key_exists('passportDataArr', $formData))
			{
			    $formData['passportDataArr']['Employee_Id']=$employeeId;
				$updated = $this->_db->update ($this->_ehrTables->empPassport, $formData['passportDataArr'], array('Employee_Id = '.$employeeId));
			}
			else
			{
				$updated = $this->_db->delete($this->_ehrTables->empPassport, 'Employee_Id='.(int)$employeeId);
			}
			
			if ($updated)
				++$isUpdated;
		}
		else
		{
			if (array_key_exists('passportDataArr', $formData))
			{
			    $formData['passportDataArr']['Employee_Id']=$employeeId;
				$updated = $this->_db->insert ($this->_ehrTables->empPassport, $formData['passportDataArr']);
				
				if ($updated)
					++$isUpdated;
			}
		}
		
		//To Update Employee Login Details
        if (array_key_exists('loginDataArr', $formData))
        {
            $logcnt = $this->ckLoginExists ($employeeId);
            
			if ($logcnt > 0)
            {		
                $loginInsert = $this->_db->update($this->_ehrTables->empLogin, $formData['loginDataArr'], 'Employee_Id = '.(int)$employeeId);
            }
            else
            {
				$formData['loginDataArr']['Employee_Id']=$employeeId;
                $loginInsert = $this->_db->insert($this->_ehrTables->empLogin, $formData['loginDataArr']);
            }
			
			if ($updated)
				++$isUpdated;
        }
	
		$result = $this->_dbCommonFun->updateResult (array('updated'        => $isUpdated,
														   'action'         => 'Add',
														   'trackingColumn' => $employeeId,
														   'formName'       => 'Employees',
														   'sessionId'      => $sessionId,
														   'tableName'      => $this->_ehrTables->empPersonal));
		
		if ($result['success'])
		{
			$result['info'] = $msg;
			$result['employeeId'] = $employeeId;
		}
		
		return $result;
    }
	/** Update the employee details when the 'finish' button is clicked in the employees form */
	public function updateEmployeesInfo($formData, $employeeId, $sessionId, $mode, $formName, $btnClick)
	{
		/** We will generate the daily report which contains the newly added employees and also
		 * the employees whose details are updated. So we should maintain a log whenever employee detail is added / updated
		 * get the employee details before update to check whether the details are updated or not */
		$oldEmpDetails = $this->_db->fetchRow($this->_db->select()->from(array('EPI'=>$this->_ehrTables->empPersonal),
				array('EPI.Emp_First_Name','EPI.Emp_Middle_Name','EPI.Emp_Last_Name',
				'EJ.Designation_Id','EJ.Location_Id','EJ.Department_Id', 'EJ.Service_Provider_Id',
				'EJ.Date_Of_Join','EPI.Personal_Email','EJ.Emp_Email','CD.Mobile_No','CD.Work_No',
				'EPI.Aadhaar_Card_Number','EPI.PAN','EPI.UAN','CD.pApartment_Name',
				'CD.pStreet_Name','CD.pCity','CD.pState','CD.pCountry','CD.pPincode','CD.cApartment_Name',
				'CD.cStreet_Name','CD.cCity','CD.cState','CD.cCountry','CD.cPincode','EPI.Form_Status'))
					->joinLeft(array('EJ'=>$this->_ehrTables->empJob),'EPI.Employee_Id=EJ.Employee_Id',array(''))
					->joinLeft(array('CD'=>$this->_ehrTables->empContacts),'EPI.Employee_Id=CD.Employee_Id',array(''))
					->where('EPI.Employee_Id = ?',$employeeId));
				
		$empNewDetails = Array(
			'Emp_First_Name' => $this->fnCheckNull($formData['personalDataArr']['Emp_First_Name']),
			'Emp_Middle_Name' => $this->fnCheckNull($formData['personalDataArr']['Emp_Middle_Name']),
			'Emp_Last_Name' => $this->fnCheckNull($formData['personalDataArr']['Emp_Last_Name']),
			'Designation_Id' => $this->fnCheckNull($formData['jobInfoDataArr']['Designation_Id']),
			'Location_Id' => $this->fnCheckNull($formData['jobInfoDataArr']['Location_Id']),
			'Department_Id' => $this->fnCheckNull($formData['jobInfoDataArr']['Department_Id']),
			'Service_Provider_Id' => $this->fnCheckNull($formData['jobInfoDataArr']['Service_Provider_Id']),
			'Date_Of_Join' => $this->fnCheckNull($formData['jobInfoDataArr']['Date_Of_Join']),
			'Personal_Email' => $this->fnCheckNull($formData['personalDataArr']['Personal_Email']),
			'Emp_Email' => $this->fnCheckNull($formData['jobInfoDataArr']['Emp_Email']),
			'Mobile_No' => $this->fnCheckNull($formData['contactInfoDataArr']['Mobile_No']),
			'Work_No' => $this->fnCheckNull($formData['contactInfoDataArr']['Work_No']),
			'Aadhaar_Card_Number' => $this->fnCheckNull($this->fnCheckNull($formData['personalDataArr']['Aadhaar_Card_Number'])	),
			'PAN' => $this->fnCheckNull($formData['personalDataArr']['PAN']),
			'UAN' => $this->fnCheckNull($formData['personalDataArr']['UAN']),
			'pApartment_Name' => $this->fnCheckNull($formData['contactInfoDataArr']['pApartment_Name']),
			'pStreet_Name' => $this->fnCheckNull($formData['contactInfoDataArr']['pStreet_Name']),
			'pCity' => $this->fnCheckNull($formData['contactInfoDataArr']['pCity']),
			'pState' => $this->fnCheckNull($formData['contactInfoDataArr']['pState']),
			'pCountry' => $this->fnCheckNull($formData['contactInfoDataArr']['pCountry']),
			'pPincode' => $this->fnCheckNull($formData['contactInfoDataArr']['pPincode']),
			'cApartment_Name' => $this->fnCheckNull($formData['contactInfoDataArr']['cApartment_Name']),
			'cStreet_Name' => $this->fnCheckNull($formData['contactInfoDataArr']['cStreet_Name']),
			'cCity' => $this->fnCheckNull($formData['contactInfoDataArr']['cCity']),
			'cState' => $this->fnCheckNull($formData['contactInfoDataArr']['cState']),
			'cCountry' => $this->fnCheckNull($formData['contactInfoDataArr']['cCountry']),
			'cPincode' => $this->fnCheckNull($formData['contactInfoDataArr']['cPincode']),
		);

		/** Flag to identify whether we should add employee info log or not */
		$addEmployeeInfoLog = 0;
		$logData = array();
		$logAction = 'Add';
		/** We should add the log only when the Form_Status becomes 1
		 * So before updating the emp details check whether the Form_Status was 1, 
		 */
		if($oldEmpDetails['Form_Status'] == 1){
			$logAction = 'update';
			foreach($empNewDetails as $key=>$value)
			{
				if($value != $oldEmpDetails[$key]){
					$addEmployeeInfoLog = 1;
				}
			}
		}
		
		/**
		 *	Declare datable object for intelly use
		*/
		$dbAllowance   = new Payroll_Model_DbTable_Allowances();
        $dbInsurance   = new Payroll_Model_DbTable_Insurance();
        $dbDesignation = new Employees_Model_DbTable_Designation();
        $dbEmpType     = new Employees_Model_DbTable_EmployeeType();
        $dbGrade       = new Employees_Model_DbTable_Grade();
		
		$isUpdated = 0;
		$isEmpDetUpdatedInSalary = 1;
		$salDetailsArr = array(); //array to update the location id/grade id in the employee salary
		$msg = $action  = '';
        
        $desHistoryArr = $locHistoryArr = $managerHistoryArr = $depHistoryArr = array();        
		
		$personalDetailsBeforeUpdate = $this->_db->fetchRow($this->_db->select()
										->from($this->_ehrTables->empPersonal, array('Allow_User_Signin', 'Enable_Sign_In_With_Mobile_No',
										'Sign_In_Mobile_Number','Sign_In_Mobile_No_Country_Code'))
										->where('Employee_Id = ?', $employeeId));
		
		/**
		 *	Update Employee Personal Information
		*/
		$personalData = $formData['personalDataArr'];
		
		if($mode != 'Draft')
		{
			$oldFormStatus = $this->getFormStatus($employeeId);
			$updated = $this->_db->update ($this->_ehrTables->empPersonal, $personalData, 'Employee_Id = '.(int)$employeeId);
		}
		else
		{
			$oldFormStatus =0;
			$action = 'add';
			
				$updated = $this->_db->insert($this->_ehrTables->empPersonal, $personalData);
			
			if($updated)
				$employeeId = $employeeId;
		}
		
		++$isUpdated;
		//		if ($updated)
		//		{
		//			++$isUpdated;
		//			
		//			/**
		//			 *	Update organization contact details table when if only employee id already exist
		//			*/
		//			$isExistOrgContact = $this->_db->fetchOne($this->_db->select()
		//													  ->from($this->_ehrTables->orgContact, new Zend_Db_Expr('COUNT(Employee_Id)'))
		//													  ->where('Employee_Id = ?', $employeeId));
		//			
		//			if ($isExistOrgContact > 0)
		//            {
		//				$nameArr = array('First_Name' => $personalData['Emp_First_Name'],
		//								 'Last_Name'  => $personalData['Emp_Last_Name']);
		//				
		//                $updated = $this->_db->update ($this->_ehrTables->orgContact, $nameArr, 'Employee_Id = '. (int)$employeeId);
		//                
		//				if ($updated)
		//				{
		//					$whereOrgCode['Org_Code = ?'] = $this->_ehrTables->getOrgCode();
		//					
		//					$this->_salesDb->update ($this->_ehrTables->regUser, $nameArr, $whereOrgCode);
		//				}
		//            }
		//		}
		
		/**
		 *	update employee hobby details
		*/
		$isHobbiesExist = $this->_db->fetchOne($this->_db->select()
											   ->from($this->_ehrTables->empHobbies, new Zend_Db_Expr('Count(Employee_Id)'))
											   ->where('Employee_Id = ?', $employeeId));
		
		if ($isHobbiesExist)
		{
			if (array_key_exists('hobbyDataArr', $formData))
			{
				$updated = $this->_db->update ($this->_ehrTables->empHobbies, $formData['hobbyDataArr'], array('Employee_Id = '.$employeeId));
			}
			else
			{
				$updated = $this->_db->delete($this->_ehrTables->empHobbies, 'Employee_Id = '.(int)$employeeId);
			}
			
			if ($updated)
				++$isUpdated;
		}
		else
		{
			if (array_key_exists('hobbyDataArr', $formData))
			{
				/** Employee Id differs while add hobby details. It will take lastinserted+1 value to insert. But sometimes may differ due to deletion of records.
				* So last inserted employee id in personal info table is used here **/
				$formData['hobbyDataArr']['Employee_Id'] = $employeeId;				
				
				$updated = $this->_db->insert ($this->_ehrTables->empHobbies, $formData['hobbyDataArr']);
				
				if ($updated)
					++$isUpdated;
			}
		}
		
		/**
		 *	update employee languages known details
		*/
		$this->_db->delete($this->_ehrTables->empLang, 'Employee_Id='.(int)$employeeId);
		
		if (array_key_exists('languageDataArr', $formData))
		{
			$langKnown = $formData['languageDataArr'];
			
			if (!empty($langKnown) && count ($langKnown) > 0)
			{
				$langCount = count($langKnown);
				$langData = array();
				
				foreach ($langKnown as $key => $row)
				{
					$langData[$key] = array('Employee_Id' => $employeeId, 'Lang_Known' => $row);
				}
				
				if (!empty($langData))
				{
					$updated = $this->_ehrTables->insertMultiple ($this->_ehrTables->empLang, $langData);
					
					if ($updated)
						++$isUpdated;
				}
			}
		}
		
		
		/**
		 *	Check license is already exist or not before Update or insert license details
		*/
		$isLicenseExist = $this->_db->fetchOne($this->_db->select()
											   ->from($this->_ehrTables->empDrivingLicense, new Zend_Db_Expr('Count(Employee_Id)'))
											   ->where('Employee_Id = ?', $employeeId));
		
		if ($isLicenseExist)
		{
			if (array_key_exists('licenseDataArr', $formData))
			{
				$updated = $this->_db->update ($this->_ehrTables->empDrivingLicense, $formData['licenseDataArr'], array('Employee_Id = '.$employeeId));
			}
			else
			{
				$updated = $this->_db->delete($this->_ehrTables->empDrivingLicense, 'Employee_Id='.(int)$employeeId);
			}
			
			if ($updated)
				++$isUpdated;
		}
		else
		{			
			if (array_key_exists('licenseDataArr', $formData))
			{
				/** Employee Id differs while add license details. It will take lastinserted+1 value to insert. But sometimes may differ due to deletion of records.
				* So last inserted employee id in personal info table is used here **/				
				$formData['licenseDataArr']['Employee_Id'] = $employeeId;
				
				$updated = $this->_db->insert ($this->_ehrTables->empDrivingLicense, $formData['licenseDataArr']);
				
				if ($updated)
					++$isUpdated;
			}
		}
		
		
		
		/**
		 *	Check passport is already exist or not before Update or insert passport details
		*/
		$isPassportExist = $this->_db->fetchOne($this->_db->select()
											   ->from($this->_ehrTables->empPassport, new Zend_Db_Expr('Count(Employee_Id)'))
											   ->where('Employee_Id = ?', $employeeId));
		
		if ($isPassportExist)
		{
			if (array_key_exists('passportDataArr', $formData))
			{
				$updated = $this->_db->update ($this->_ehrTables->empPassport, $formData['passportDataArr'], array('Employee_Id = '.$employeeId));
			}
			else
			{
				$updated = $this->_db->delete($this->_ehrTables->empPassport, 'Employee_Id='.(int)$employeeId);
			}
			
			if ($updated)
				++$isUpdated;
		}
		else
		{
			if (array_key_exists('passportDataArr', $formData))
			{
				/** Employee Id differs while add passport details. It will take lastinserted+1 value to insert. But sometimes may differ due to deletion of records.
				* So last inserted employee id in personal info table is used here **/				
				$formData['passportDataArr']['Employee_Id'] = $employeeId;
				
				$updated = $this->_db->insert ($this->_ehrTables->empPassport, $formData['passportDataArr']);
				
				if ($updated)
					++$isUpdated;
			}
		}
		
		/**
		 *	job information details
		*/
		$jobData = $formData['jobInfoDataArr'];

		$employeeLeaveBalUpdateDetails = array(
			'Employee_Id' => $employeeId,
			'Date_Of_Join' => $jobData['Date_Of_Join'],
			'Probation_Date' => $jobData['Probation_Date'],
			'Form_Status' => $personalData['Form_Status'],
			'Is_Update' => true
		);

		if($oldFormStatus==0 && $personalData['Form_Status']==1)
		{
			$updateEmployeeLeaveBalance = 1;
		}
		else
		{
			$updateEmployeeLeaveBalance = 0;
		}
		/* Update the employee leave type balance */
		$eligibleForLeaveBalance = $this->_dbLeave->updateNewJoineeLeaveTypeBalance($employeeLeaveBalUpdateDetails,$updateEmployeeLeaveBalance);
			
		/**
		 *	Get Location id, desingation id, grade id from empJob table
		*/
		$detailsBeforeUpdate = $this->_db->fetchRow($this->_db->select()
													->from(array('EJ'=>$this->_ehrTables->empJob),
														   array('EJ.Location_Id', 'EJ.Designation_Id', 'D.Grade_Id','EJ.Emp_Email','EJ.Emp_Status'))
													
													->joinInner(array('D'=>$this->_ehrTables->designation),
																'EJ.Designation_Id = D.Designation_Id', array())
													
													->where('Employee_Id = ?', $employeeId));
		
		$affectedGradeAllowanceEmp = $affectedAllowanceEmp = $affectedLocationAllowanceEmp = $changedLocationAllowanceEmp = array();
		
		//before update we are getting any allowance associated with this emp for his grade/location
		//if grade or location changed then salary recalc has to be set
		if ($detailsBeforeUpdate['Grade_Id'] > 0)
		{
			//allowance_id or location_ids
			$gradeAllowance = $dbAllowance->getGradeAllowances($detailsBeforeUpdate['Grade_Id']);
			$affectedGradeAllowanceEmp = $dbDesignation->getAllowanceCoverageEmp($detailsBeforeUpdate['Grade_Id'], $detailsBeforeUpdate['Designation_Id'], $gradeAllowance);
		}
		
		if ($detailsBeforeUpdate['Location_Id'] > 0)
		{
			$locationAllowance = $dbAllowance->getGradeAllowances($detailsBeforeUpdate['Grade_Id'],'LOC');
			
			if ($locationAllowance[0] == "GRADE_LOC")
			{
				$affectedLocationAllowanceEmp = $dbDesignation->getAllowanceCoverageEmp($detailsBeforeUpdate['Grade_Id'],
																						$detailsBeforeUpdate['Designation_Id'],
																						$locationAllowance);
			}	
		}
		
		$isJobInfoExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
											   ->where('Employee_Id =?', $employeeId));
		
		$benefit = 0;
		if (!empty($jobData['EmpType_Id']))
		{
			$viewEmployeeType = $dbEmpType->viewEmployeeType($jobData['EmpType_Id']);
			$benefit = $viewEmployeeType['Benefits_Applicable'];
		}
		
		//check benefit employee type
		if (!($benefit != 0 && array_key_exists('Pf_PolicyNo', $jobData)))
		{
			$jobData['Pf_PolicyNo'] = new Zend_Db_Expr('NULL');
		}

        /**Designation, Location & Manager History.**/
        $getExistRec = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->empJob, array('Designation_Id','Location_Id','Manager_Id','Department_Id','Work_Schedule','Service_Provider_Id','EmpType_Id',))
									->where('Employee_Id = ?',$employeeId));
        							
        //Designation History.
        if($formData['jobInfoDataArr']['Designation_Id'] != $getExistRec['Designation_Id'] && $getExistRec['Designation_Id'] != NULL && $getExistRec['Designation_Id'] > 0)
        {
            $maxHistoryId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empDesHistory,array(new Zend_Db_Expr('Max(History_Id)')))->where('Employee_Id = ?',$employeeId));
            
            //get already existing Max To_Date for the employee
            $getExistDesHistory = $this->_db->select()->from($this->_ehrTables->empDesHistory,
                                                                array('To_Date'))
                                                                ->where('Employee_Id = ?',$employeeId);
            if(!empty($maxHistoryId))                                
                    $getExistDesHistory->where('History_Id = ?',($this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empDesHistory,array(new Zend_Db_Expr('Max(History_Id)')))->where('Employee_Id = ?',$employeeId))));
                    
            $getExistDesHistory = $this->_db->fetchOne($getExistDesHistory);
            
            //$fromDate = (!empty($getExistDesHistory) ? (date('Y-m-d', strtotime($getExistDesHistory . ' +1 day'))) : $formData['jobInfoDataArr']['Date_Of_Join']);
            //If the To_Date exists for the employee then the from date will be To_Date+1day, else it will date of join
            $fromDate = (!empty($getExistDesHistory) ? (($getExistDesHistory == date('Y-m-d')) ? $getExistDesHistory : (date('Y-m-d', strtotime($getExistDesHistory . ' +1 day')))) : $formData['jobInfoDataArr']['Date_Of_Join']);
            
            $desHistoryArr = array('Employee_Id' => $employeeId,
                                   'Previous_Designation_Id' => $getExistRec['Designation_Id'],
                                   //'From_Date' => $formData['jobInfoDataArr']['Date_Of_Join'],
                                   'From_Date' => $fromDate,
                                   'To_Date' => date('Y-m-d'),
                                   'Modified_By' => $sessionId,
                                   'Modified_On' => date('Y-m-d H:i:s'));
		}

		$depHistoryArr = $this->getEmployeeDepartmentHistory($formData,$getExistRec,$employeeId,$sessionId);

		//Manager History.
        if($formData['jobInfoDataArr']['Manager_Id'] != $getExistRec['Manager_Id'] && $getExistRec['Manager_Id'] != NULL )
        {
            $maxHistoryId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empManagerHistory,array(new Zend_Db_Expr('Max(History_Id)')))->where('Employee_Id = ?',$employeeId));
            //get already existing Max To_Date for the employee
            $getExistManagerHistory = $this->_db->select()->from($this->_ehrTables->empManagerHistory,
                                                                array('To_Date'))
                                                                ->where('Employee_Id = ?',$employeeId);
            if(!empty($maxHistoryId))                                
                $getExistManagerHistory->where('History_Id = ?',$maxHistoryId);
                
                    
            $getExistManagerHistory = $this->_db->fetchOne($getExistManagerHistory);
            
            //If the To_Date exists for the employee then the from date will be To_Date+1day, else it will date of join
            $fromDate = (!empty($getExistManagerHistory) ? (($getExistManagerHistory == date('Y-m-d')) ? $getExistManagerHistory : (date('Y-m-d', strtotime($getExistManagerHistory . ' +1 day')))) : $formData['jobInfoDataArr']['Date_Of_Join']);
			
			//When the manager id is zero we need to update the employee id as manager id
			if($getExistRec['Manager_Id']==0)
			{
				$getExistRec['Manager_Id'] = $employeeId;
			}
            $managerHistoryArr = array('Employee_Id' => $employeeId,
                                       'Previous_Manager_Id' => $getExistRec['Manager_Id'],
                                       'From_Date' => $fromDate,
                                       //'From_Date' => $formData['jobInfoDataArr']['Date_Of_Join'],
                                       'To_Date' => date('Y-m-d'),
                                       'Modified_By' => $sessionId,
                                       'Modified_On' => date('Y-m-d H:i:s'));
        }
        
        //Location History
        if($formData['jobInfoDataArr']['Location_Id'] != $getExistRec['Location_Id'] && $getExistRec['Location_Id'] != NULL && $getExistRec['Location_Id'] > 0)
        {
            $maxHistoryId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empLocHistory,array(new Zend_Db_Expr('Max(History_Id)')))->where('Employee_Id = ?',$employeeId));
            
            //get already existing Max To_Date for the employee
            $getExistLocHistory = $this->_db->select()->from($this->_ehrTables->empLocHistory,
                                                                array('To_Date'))
                                                                ->where('Employee_Id = ?',$employeeId);
            if(!empty($maxHistoryId))                                
                $getExistLocHistory->where('History_Id = ?',$maxHistoryId);
                //$getExistManagerHistory->where('History_Id = ?',($this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empManagerHistory,array(new Zend_Db_Expr('Max(History_Id)')))->where('Employee_Id = ?',$employeeId))));
                    
            $getExistLocHistory = $this->_db->fetchOne($getExistLocHistory);
            
            //$fromDate = (!empty($getExistLocHistory) ? (date('Y-m-d', strtotime($getExistLocHistory . ' +1 day'))) : $formData['jobInfoDataArr']['Date_Of_Join']);
            //If the To_Date exists for the employee then the from date will be To_Date+1day, else it will date of join
            $fromDate = (!empty($getExistLocHistory) ? (($getExistLocHistory == date('Y-m-d')) ? $getExistLocHistory : (date('Y-m-d', strtotime($getExistLocHistory . ' +1 day')))) : $formData['jobInfoDataArr']['Date_Of_Join']);
            
            $locHistoryArr = array('Employee_Id' => $employeeId,
                                   'Previous_Location_Id' => $getExistRec['Location_Id'],
                                   //'From_Date' => $formData['jobInfoDataArr']['Date_Of_Join'],
                                   'From_Date' => $fromDate,
                                   'To_Date' => date('Y-m-d'),
                                   'Modified_By' => $sessionId,
                                   'Modified_On' => date('Y-m-d H:i:s'));
        }
		
		/**
		 *	Check job details exist for that employee id,
		 *	If exist then update form details, otherwise insert form details
		*/
		if (!empty($isJobInfoExist))
		{
			$jobUpdated = $this->_db->update($this->_ehrTables->empJob, $jobData, 'Employee_Id='.(int)$employeeId);
			if($jobUpdated && ($formData['jobInfoDataArr']['Designation_Id'] != $getExistRec['Designation_Id'] || $formData['jobInfoDataArr']['Location_Id'] != $getExistRec['Location_Id'] || $formData['jobInfoDataArr']['Department_Id'] !=  $getExistRec['Department_Id'] || $formData['jobInfoDataArr']['Work_Schedule'] !=  $getExistRec['Work_Schedule'] || $formData['jobInfoDataArr']['EmpType_Id'] !=  $getExistRec['EmpType_Id']))
			{
				$employeeIds = array();
				array_push($employeeIds,$employeeId);
				$this->callCustomGroupRefreshApi($employeeIds,$sessionId);
			}
			$empUserName = '';
			
			/** The User_Name will be email id if the email authentication is enabled.
			 * The User_Name will be mobile no if the mobile number authentication is enabled
			 * User_Name field is set as primary so User_Name should be removed if the employee status is Inactive,
			 * Then only the same credentials can be used by another employee. */

			if((int)$personalData['Allow_User_Signin'] === 1 && $jobData['Emp_Status'] === 'Active') {
				if((int)$personalData['Enable_Sign_In_With_Mobile_No'] === 1 ) {
					$empUserName = $personalData['Sign_In_Mobile_No_Country_Code'].$personalData['Sign_In_Mobile_Number'];
				} else {
					$empUserName = $jobData['Emp_Email'];
				}
			}

			/* 	When the user is signed in we will get the firebase uid and set in the cookie.
				During every action we will get the employee id associated with the firebase uid, 
				if the firebase uid is not associtaed with the employee id then the user will be redirected to the auth page.
				So to remove the user session we should remove the firebase uid associated with the employee id.
				Remove the firebase uid when the Allow_User_Siginin or Enable_Sign_In_With_Mobile_No or
				signin mobile number or signin email id is changed or the employee status is changed. */

				if( ((int)$personalDetailsBeforeUpdate['Allow_User_Signin'] !== (int)$personalData['Allow_User_Signin']) ||
				((int)$personalDetailsBeforeUpdate['Enable_Sign_In_With_Mobile_No'] !== (int)$personalData['Enable_Sign_In_With_Mobile_No']) ||
				((int)$personalData['Enable_Sign_In_With_Mobile_No'] === 1 && $personalDetailsBeforeUpdate['Sign_In_Mobile_No_Country_Code'].$personalDetailsBeforeUpdate['Sign_In_Mobile_Number'] !== $personalData['Sign_In_Mobile_No_Country_Code'].$personalData['Sign_In_Mobile_Number']) ||
				((int)$personalData['Enable_Sign_In_With_Mobile_No'] === 0 && $detailsBeforeUpdate['Emp_Email'] !== $jobData['Emp_Email']) ||
				($detailsBeforeUpdate['Emp_Status'] !== $jobData['Emp_Status'])) {
					$empLoginDetails = array('User_Name' => $empUserName ? $empUserName : '', 'Firebase_Uid' => NULL);
					$updateEmpUser = $this->_db->update($this->_ehrTables->empLogin, $empLoginDetails, 'Employee_Id='.(int)$employeeId);
				}
		}
		else
		{
			/** Employee Id differs while add emp job details. It will take lastinserted+1 value to insert. But sometimes may differ due to deletion of records.
			 * So last inserted employee id in personal info table is used here **/
			$jobData['Employee_Id'] = $employeeId;
				
			$jobUpdated = $this->_db->insert($this->_ehrTables->empJob, $jobData);
		}  
		
		if ($jobUpdated)
		{
			++$isUpdated;
			
            
            if(!empty($desHistoryArr)&&$desHistoryArr['From_Date']!='NULL')
            {
                //If History already exists for $desHistoryArr['To_Date'] we should not add it again.
                $dtExists =  $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empDesHistory,
																array('History_Id'))
																->where('Employee_Id = ?',$desHistoryArr['Employee_Id'])
                                                                ->where('To_Date = ?',$desHistoryArr['To_Date']));
                if(!$dtExists)
                    $updated = $this->_db->insert($this->_ehrTables->empDesHistory, $desHistoryArr);
			}
			
			$updated = $this->updateEmployeeDeparmentHistory($depHistoryArr);
	
			if(!empty($managerHistoryArr)&&$managerHistoryArr['From_Date']!='NULL')
            {
                //If History already exists for $managerHistoryArr['To_Date'] we should not add it again.
                $dtExists =  $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empManagerHistory,
																array('History_Id'))
																->where('Employee_Id = ?',$managerHistoryArr['Employee_Id'])
                                                                ->where('To_Date = ?',$managerHistoryArr['To_Date']));
                if(!$dtExists)
                    $updated = $this->_db->insert($this->_ehrTables->empManagerHistory, $managerHistoryArr);
            }
            
            if(!empty($locHistoryArr)&&$locHistoryArr['From_Date']!='NULL')
            {
                //If History already exists for $locHistoryArr['To_Date'] we should not add it again.
                $dtExists =  $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empLocHistory,
																array('History_Id'))
																->where('Employee_Id = ?',$locHistoryArr['Employee_Id'])
                                                                ->where('To_Date = ?',$locHistoryArr['To_Date']));
                if(!$dtExists)                    
                    $updated = $this->_db->insert($this->_ehrTables->empLocHistory, $locHistoryArr);
            }
            
			//$isExistOrgContact = $this->_db->fetchOne($this->_db->select()
			//										  ->from($this->_ehrTables->orgContact, new Zend_Db_Expr('COUNT(Employee_Id)'))
			//										  ->where('Employee_Id = ?', $employeeId));
			//
			////Update employee mail details in organization table
			//if ($isExistOrgContact > 0)
			//{
			//	$orgUpdateData = array('Email_Id' => $jobData['Emp_Email']);
			//	
			//	$this->_db->update($this->_ehrTables->orgContact, $orgUpdateData, 'Employee_Id = '. (int)$employeeId);
			//	
			//	$whereOrgCode['Org_Code = ?'] = $this->_ehrTables->getOrgCode();
			//	$this->_salesDb->update($this->_ehrTables->regUser, $orgUpdateData, $whereOrgCode);
			//}
			
			$getEmpGrade = $this->_db->fetchRow($this->_db->select()
												->from(array('D'=>$this->_ehrTables->designation),array(''))
												
												->joinInner(array('G'=>$this->_ehrTables->empGrade),'G.Grade_Id = D.Grade_Id',
															array('G.Grade_Id', 'G.Min_AnnualSalary', 'G.Max_AnnualSalary',
																  'G.Min_HourlyWages', 'G.Max_HourlyWages', 'G.Min_OvertimeWages',
																  'G.Max_OvertimeWages'))
												
												->where('D.Designation_Id = ?', $jobData['Designation_Id']));
			
			//if location has changed
			if ($detailsBeforeUpdate['Location_Id'] != $jobData['Location_Id'] && $personalData['Form_Status'] == 1)
			{
				/* Check the allowance exists in the grade-location level */
				$changedLocationAllowance = $dbAllowance->getGradeAllowances($detailsBeforeUpdate['Grade_Id'],'LOC');
				
				if ($changedLocationAllowance[0] == "GRADE_LOC")
				{
					/* Prerequisites for setting the salary recalculation flag is not set correctly. It has to be changed. */
					$changedLocationAllowanceEmp = $dbDesignation->getAllowanceCoverageEmp($detailsBeforeUpdate['Grade_Id'],
																						   $detailsBeforeUpdate['Designation_Id'],
																						   $changedLocationAllowance);
				}
				
				$affectedLocEmp = array_unique(array_merge($affectedLocationAllowanceEmp, $changedLocationAllowanceEmp));
				$allowanceReset = in_array($employeeId, $affectedLocEmp);
				
				$salDetailsArr['Location_Id']=$jobData['Location_Id'];

				if ($allowanceReset)
				{
					$dbAllowance->setSalRecal(array($employeeId), 1);
					
					$msg = 'Please update the salary for this employee';
				}
			}	 
			
			//if designation has changed
			if ($detailsBeforeUpdate['Grade_Id'] != $getEmpGrade['Grade_Id'] && $personalData['Form_Status'] == 1)
			{
				/** If previous grade and new grade differs, set the salary recalculation flag for the employees based on designation **/
				$dbAllowance->setSalRecal(array($employeeId), 1);
				
				/** while edit message has to be updated **/
				if(!empty($action) && $action != 'add' && empty($msg))
				{	
					$msg = 'Please update the salary for this employee';
				}
				
				$salDetailsArr['Grade_Id']=$getEmpGrade['Grade_Id'];
			}

			/* If the employee location or grade is changed then it has to be updated in the employee salary */
			if(!empty($salDetailsArr)){
				$isEmpDetUpdatedInSalary = $this->updateEmpLocGradeInSalary($salDetailsArr, $employeeId, $formName, $sessionId);

				/* If the employee location or grade not updated in the employee salary */
				if(empty($isEmpDetUpdatedInSalary)){
					$msg = (empty($msg)) ? 'Please update the salary for this employee' : $msg;
				}
			}
		}
		
		/**
		 *	contact information details
		 */
		$contactData = $formData['contactInfoDataArr'];
		
		$isContactInfoExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empContacts, array('Employee_Id'))
											   ->where('Employee_Id = ?', $employeeId));
		
		if (!empty($isContactInfoExist))
		{
			$contactUpdated = $this->_db->update($this->_ehrTables->empContacts, $contactData, 'Employee_Id='.(int)$employeeId);
		}
		else
		{
			/** Employee Id differs while add emp contact details. It will take lastinserted+1 value to insert. But sometimes may differ due to deletion of records.
			* So last inserted employee id in personal info table is used here **/				
			$contactData['Employee_Id'] = $employeeId;
			
			$contactUpdated = $this->_db->insert($this->_ehrTables->empContacts, $contactData);
		}
		++$isUpdated;
		//if ($contactUpdated)
		//{
		//	++$isUpdated;
		//	
		//	/**
		//	 *	Update contact information in organization table
		//	*/
		//	$orgEmpId = $this->_db->fetchOne($this->_db->select()
		//								 ->from($this->_ehrTables->orgContact, new Zend_Db_Expr('COUNT(Employee_Id)'))
		//								 ->where('Employee_Id = ?', $employeeId));
		//	
		//	if ($orgEmpId > 0)
		//	{
		//		$orgContactData = array('Landline_No' => $contactData['Land_Line_No'],
		//								'Mobile_No'   => $contactData['Mobile_No']);
		//		
		//		$this->_db->update($this->_ehrTables->orgContact, $orgContactData, 'Employee_Id = '. (int)$employeeId);
		//		
		//		$whereOrgCode['Org_Code = ?'] = $this->_ehrTables->getOrgCode();
		//		
		//		$this->_salesDb->update($this->_ehrTables->regUser, $orgContactData, $whereOrgCode);
		//	}
		//}
		/** Add employee info log*/
		if($logAction == 'Add'){
			if (strtolower($this->_partnerId) === 'camu') 
			{
				/** Add employee info log */
				$logData = array('Employee_Id'=>$employeeId, 'Action'=>'Add', 'Log_Timestamp'=>date('Y-m-d H:i:s'),'Camu_Push_Status'=>'Open');
				$this->_db->insert($this->_ehrTables->employeeInfoTimestampLog, $logData);
				$camuCreateStaff=$this->callCamuCreateApi($employeeId);
				$this->callTrstScoreCreateApi($employeeId,$jobData['Service_Provider_Id']);	
			}
			else{
				/** Add employee info log */
				$logData = array('Employee_Id'=>$employeeId, 'Action'=>'Add', 'Log_Timestamp'=>date('Y-m-d H:i:s'));
				$this->_db->insert($this->_ehrTables->employeeInfoTimestampLog, $logData);
				$this->callTrstScoreCreateApi($employeeId,$jobData['Service_Provider_Id']);
			}
		} else if($addEmployeeInfoLog == 1) {
			$logData = array('Employee_Id'=>$employeeId, 'Action'=>'Update', 'Log_Timestamp'=>date('Y-m-d H:i:s'));
			$this->_db->insert($this->_ehrTables->employeeInfoTimestampLog, $logData);
		}
		
		/**
		 *	Career details
		*/
		$isCareerInfoExist = $this->_db->fetchOne($this->_db->select()
												  ->from($this->_ehrTables->empSkillset, array('Employee_Id'))
												  ->where('Employee_Id = ?', $employeeId));
		
		if (array_key_exists('careerInfoDataArr', $formData))
		{
			$careerData = $formData['careerInfoDataArr'];
			
			if (!empty($isCareerInfoExist))
			{
				$careerUpdated = $this->_db->update($this->_ehrTables->empSkillset, $careerData, 'Employee_Id='.(int)$employeeId);
			}
			else
			{
				/** Employee Id differs while add career details. It will take lastinserted+1 value to insert. But sometimes may differ due to deletion of records.
				* So last inserted employee id in personal info table is used here **/				
				$careerData['Employee_Id'] = $employeeId;
			
				$careerUpdated = $this->_db->insert($this->_ehrTables->empSkillset, $careerData);
			}
			
			if ($careerUpdated)
				++$isUpdated;
		}
		else
		{
			if (!empty($isCareerInfoExist))
			{
				$careerUpdated = $this->_db->delete($this->_ehrTables->empSkillset, 'Employee_Id = '.(int)$employeeId);
				
				if ($careerUpdated)
					++$isUpdated;
			}
		}
		
		/**
		 *	Bank Details
		*/
		$isBankInfoExist = $this->_db->fetchOne($this->_db->select()
												  ->from($this->_ehrTables->empBank, array('Employee_Id'))
												  ->where('Employee_Id = ?', $employeeId));
		
		if (array_key_exists('bankInfoDataArr', $formData))
		{
			$bankData = $formData['bankInfoDataArr'];					
			
			if (!empty($isBankInfoExist))
			{
				$bankUpdated = $this->_db->update($this->_ehrTables->empBank, $bankData, 'Employee_Id = '.(int)$employeeId);
			}
			else
			{
				/** Employee Id differs while add bank details. It will take lastinserted+1 value to insert. But sometimes may differ due to deletion of records.
				*So last inserted employee id in personal info table is used here **/				
				$bankData['Employee_Id'] = $employeeId;
				
				$bankUpdated = $this->_db->insert($this->_ehrTables->empBank, $bankData);
			}
			
			if ($bankUpdated)
				++$isUpdated;
		}
		else
		{
			if (!empty($isBankInfoExist))
			{
			//	$bankUpdated = $this->_db->delete($this->_ehrTables->empBank, 'Employee_Id = '.(int)$employeeId);
				$bankUpdated = 1;
				if ($bankUpdated)
					++$isUpdated;
			}
		}
		
        if($isUpdated)
        {
            /** Update the DataSetup status **/
            if($btnClick == 'draft')
            {
                $isUpdated = $this->_dbCommonFun->updateDataSetupDashboard('Inprogress','18');            
            }
            else if($btnClick == 'finish')
            {
                $isUpdated = $this->_dbCommonFun->updateDataSetupDashboard('Completed','18');            
            }
        }
        
		if(!empty($eligibleForLeaveBalance))
		{
			$leaveBalanceUpdated = $this->_dbLeave->empDOJUpdateEligibleDays($eligibleForLeaveBalance['Employee_Id'],'update-employee-details');

			
		}
		/**
		 *	this function will handle
		 *		update system log function
		 *		clear submit lock fucntion
		 *		return success/failure array
	    */
		$result = $this->_dbCommonFun->updateResult (array('updated'        => $isUpdated,
														   'action'         => 'Edit',
														   'trackingColumn' => $employeeId,
														   'formName'       => $formName,
														   'sessionId'      => $sessionId,
														   'tableName'      => $this->_ehrTables->empPersonal));
		
		if ($result['success'])
		{
			$result['info'] = $msg;
			
			/* If the designation is changed and previous grade and new grade differs means or if the employee location is 
			changed and the salary need to be updated, salary recalculation will be set. If salary recalculation is set, $msg will be shown along with success message. 
			Or if the employee location or grade is not updated in the employee salary table then this msg will be shown */
			if($msg != '')
			{
				$result['msg'] = $result['msg'].'. '.$msg;	
			}
			
			$result['employeeId'] = $employeeId;
					
			if(isset($personalData['Photo_Path']))
				$result['photoPath'] = $personalData['Photo_Path'];
		}
		
		return $result;
	}
	/**
	 *	Update the employee details when the employee is in the draft status 
	*/
	public function updateEmployees ($formData, $employeeId, $sessionId, $mode, $formName, $btnClick,$formInfo , $empId)
	{		
		/**
		 *	Declare datable object for intelly use
		*/
		$dbAllowance   = new Payroll_Model_DbTable_Allowances();
        $dbInsurance   = new Payroll_Model_DbTable_Insurance();
        $dbDesignation = new Employees_Model_DbTable_Designation();
        $dbEmpType     = new Employees_Model_DbTable_EmployeeType();
        $dbGrade       = new Employees_Model_DbTable_Grade();
		
		$isUpdated = 0;
		$isEmpDetUpdatedInSalary = 1;
		$salDetailsArr = array(); //array to update the location id/grade id in the employee salary
		$msg = $action  = '';
        
        $desHistoryArr = $locHistoryArr = $managerHistoryArr = $depHistoryArr = array();        
        
		/**
		 *	Update Employee Personal Information
		*/
		$personalData = $formData['personalDataArr'];
		$personalInfo = $formInfo['personalInfoArr'];
		
		if($mode != 'Draft')
		{
			$oldFormStatus = $this->getFormStatus($employeeId);
			$updated = $this->_db->update ($this->_ehrTables->empPersonal, $personalData, 'Employee_Id = '.(int)$employeeId);
		}
		else
		{
			$oldFormStatus = 0;
			$action = 'add';
			
			$empExists = $this->_db->select()->from(array('e'=>$this->_ehrTables->empPersonal),
													new Zend_Db_Expr('count(e.Employee_Id)'))
													->where('e.Employee_Id = ?', $employeeId);
			$isExistEmp= $this->_db->fetchOne($empExists);
			
			
			
			if($isExistEmp == 0)
			{
				$updated = $this->_db->insert($this->_ehrTables->empPersonal, $personalData);
				if($updated)
				{
					$employeeId = $employeeId;
				}
			}
			else if($empId ==1 && $isExistEmp !=0)
			{
				$updated = $this->_db->update ($this->_ehrTables->empPersonal, $personalInfo, 'Employee_Id = '.(int)$employeeId);
			}
			else if($isExistEmp != 0)
			{
				return array('success' => false, 'msg'=>'Employee Id already exists', 'type'=>'info');
			}
			
		}
		++$isUpdated;
		//		if ($updated)
		//		{
		//			++$isUpdated;
		//			
		//			/**
		//			 *	Update organization contact details table when if only employee id already exist
		//			*/
		//			$isExistOrgContact = $this->_db->fetchOne($this->_db->select()
		//													  ->from($this->_ehrTables->orgContact, new Zend_Db_Expr('COUNT(Employee_Id)'))
		//													  ->where('Employee_Id = ?', $employeeId));
		//			
		//			if ($isExistOrgContact > 0)
		//            {
		//				$nameArr = array('First_Name' => $personalData['Emp_First_Name'],
		//								 'Last_Name'  => $personalData['Emp_Last_Name']);
		//				
		//                $updated = $this->_db->update ($this->_ehrTables->orgContact, $nameArr, 'Employee_Id = '. (int)$employeeId);
		//                
		//				if ($updated)
		//				{
		//					$whereOrgCode['Org_Code = ?'] = $this->_ehrTables->getOrgCode();
		//					
		//					$this->_salesDb->update ($this->_ehrTables->regUser, $nameArr, $whereOrgCode);
		//				}
		//            }
		//		}
		
		/**
		 *	update employee hobby details
		*/
		$isHobbiesExist = $this->_db->fetchOne($this->_db->select()
											   ->from($this->_ehrTables->empHobbies, new Zend_Db_Expr('Count(Employee_Id)'))
											   ->where('Employee_Id = ?', $employeeId));
		
		if ($isHobbiesExist)
		{
			if (array_key_exists('hobbyDataArr', $formData))
			{
				$updated = $this->_db->update ($this->_ehrTables->empHobbies, $formData['hobbyDataArr'], array('Employee_Id = '.$employeeId));
			}
			else
			{
				$updated = $this->_db->delete($this->_ehrTables->empHobbies, 'Employee_Id = '.(int)$employeeId);
			}
			
			if ($updated)
				++$isUpdated;
		}
		else
		{
			if (array_key_exists('hobbyDataArr', $formData))
			{
				/** Employee Id differs while add hobby details. It will take lastinserted+1 value to insert. But sometimes may differ due to deletion of records.
				So last inserted employee id in personal info table is used here **/
				$formData['hobbyDataArr']['Employee_Id'] = $employeeId;				
				
				$updated = $this->_db->insert ($this->_ehrTables->empHobbies, $formData['hobbyDataArr']);
				
				if ($updated)
					++$isUpdated;
			}
		}
		
		/**
		 *	update employee languages known details
		*/
		$this->_db->delete($this->_ehrTables->empLang, 'Employee_Id='.(int)$employeeId);
		
		if (array_key_exists('languageDataArr', $formData))
		{
			$langKnown = $formData['languageDataArr'];
			
			if (!empty($langKnown) && count ($langKnown) > 0)
			{
				$langCount = count($langKnown);
				$langData = array();
				
				foreach ($langKnown as $key => $row)
				{
					$langData[$key] = array('Employee_Id' => $employeeId, 'Lang_Known' => $row);
				}
				
				if (!empty($langData))
				{
					$updated = $this->_ehrTables->insertMultiple ($this->_ehrTables->empLang, $langData);
					
					if ($updated)
						++$isUpdated;
				}
			}
		}
		
		
		/**
		 *	Check license is already exist or not before Update or insert license details
		*/
		$isLicenseExist = $this->_db->fetchOne($this->_db->select()
											   ->from($this->_ehrTables->empDrivingLicense, new Zend_Db_Expr('Count(Employee_Id)'))
											   ->where('Employee_Id = ?', $employeeId));
		
		if ($isLicenseExist)
		{
			if (array_key_exists('licenseDataArr', $formData))
			{
				$updated = $this->_db->update ($this->_ehrTables->empDrivingLicense, $formData['licenseDataArr'], array('Employee_Id = '.$employeeId));
			}
			else
			{
				$updated = $this->_db->delete($this->_ehrTables->empDrivingLicense, 'Employee_Id='.(int)$employeeId);
			}
			
			if ($updated)
				++$isUpdated;
		}
		else
		{			
			if (array_key_exists('licenseDataArr', $formData))
			{
				/** Employee Id differs while add license details. It will take lastinserted+1 value to insert. But sometimes may differ due to deletion of records.
				So last inserted employee id in personal info table is used here **/				
				$formData['licenseDataArr']['Employee_Id'] = $employeeId;
				
				$updated = $this->_db->insert ($this->_ehrTables->empDrivingLicense, $formData['licenseDataArr']);
				
				if ($updated)
					++$isUpdated;
			}
		}
		
		
		
		/**
		 *	Check passport is already exist or not before Update or insert passport details
		*/
		$isPassportExist = $this->_db->fetchOne($this->_db->select()
											   ->from($this->_ehrTables->empPassport, new Zend_Db_Expr('Count(Employee_Id)'))
											   ->where('Employee_Id = ?', $employeeId));
		
		if ($isPassportExist)
		{
			if (array_key_exists('passportDataArr', $formData))
			{
				$updated = $this->_db->update ($this->_ehrTables->empPassport, $formData['passportDataArr'], array('Employee_Id = '.$employeeId));
			}
			else
			{
				$updated = $this->_db->delete($this->_ehrTables->empPassport, 'Employee_Id='.(int)$employeeId);
			}
			
			if ($updated)
				++$isUpdated;
		}
		else
		{
			if (array_key_exists('passportDataArr', $formData))
			{
				/** Employee Id differs while add passport details. It will take lastinserted+1 value to insert. But sometimes may differ due to deletion of records.
				So last inserted employee id in personal info table is used here **/				
				$formData['passportDataArr']['Employee_Id'] = $employeeId;
				
				$updated = $this->_db->insert ($this->_ehrTables->empPassport, $formData['passportDataArr']);
				
				if ($updated)
					++$isUpdated;
			}
		}
		
		//To Update Employee Login Details
		if (array_key_exists('loginDataArr', $formData))
        {
            $logcnt = $this->ckLoginExists ($employeeId);
            /* If the login details exists in the emp_user table then update the values otherwise insert the values. */
			if ($logcnt > 0)
            {
                $loginInsert = $this->_db->update($this->_ehrTables->empLogin, $formData['loginDataArr'], 'Employee_Id = '.(int)$employeeId);
            }
            else
            {
				$formData['loginDataArr']['Employee_Id'] = $employeeId;
				
                $loginInsert = $this->_db->insert($this->_ehrTables->empLogin, $formData['loginDataArr']);
            }
			
			if ($updated)
				++$isUpdated;
        }
		
		/**
		 *	job information details
		*/
		$jobData = $formData['jobInfoDataArr'];

		$employeeLeaveBalUpdateDetails = array(
			'Employee_Id' => $employeeId,
			'Date_Of_Join' => $jobData['Date_Of_Join'],
			'Probation_Date' => $jobData['Probation_Date'],
			'Form_Status' => $personalData['Form_Status'],
			'Is_Update' => true
		);

		if($oldFormStatus==0 && $personalData['Form_Status']==1)
		{
			$updateEmployeeLeaveBalance = 1;
		}
		else
		{
			$updateEmployeeLeaveBalance = 0;
		}
		/* Update the employee leave type balance */
		$eligibleForLeaveBalance = $this->_dbLeave->updateNewJoineeLeaveTypeBalance($employeeLeaveBalUpdateDetails,$updateEmployeeLeaveBalance);	
		
		/**
		 *	Get Location id, desingation id, grade id from empJob table
		*/
		$detailsBeforeUpdate = $this->_db->fetchRow($this->_db->select()
													->from(array('EJ'=>$this->_ehrTables->empJob),
														   array('EJ.Location_Id', 'EJ.Designation_Id', 'D.Grade_Id'))
													
													->joinInner(array('D'=>$this->_ehrTables->designation),
																'EJ.Designation_Id = D.Designation_Id', array())
													
													->where('Employee_Id = ?', $employeeId));
		
		$affectedGradeAllowanceEmp = $affectedAllowanceEmp = $affectedLocationAllowanceEmp = $changedLocationAllowanceEmp = array();
		
		//before update we are getting any allowance associated with this emp for his grade/location
		//if grade or location changed then salary recalc has to be set
		if ($detailsBeforeUpdate['Grade_Id'] > 0)
		{
			//allowance_id or location_ids
			$gradeAllowance = $dbAllowance->getGradeAllowances($detailsBeforeUpdate['Grade_Id']);
			$affectedGradeAllowanceEmp = $dbDesignation->getAllowanceCoverageEmp($detailsBeforeUpdate['Grade_Id'], $detailsBeforeUpdate['Designation_Id'], $gradeAllowance);
		}
		
		if ($detailsBeforeUpdate['Location_Id'] > 0)
		{
			$locationAllowance = $dbAllowance->getGradeAllowances($detailsBeforeUpdate['Grade_Id'],'LOC');
			
			if ($locationAllowance[0] == "GRADE_LOC")
			{
				$affectedLocationAllowanceEmp = $dbDesignation->getAllowanceCoverageEmp($detailsBeforeUpdate['Grade_Id'],
																						$detailsBeforeUpdate['Designation_Id'],
																						$locationAllowance);
			}	
		}
		
		$isJobInfoExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
											   ->where('Employee_Id =?', $employeeId));
		
		$benefit = 0;
		if (!empty($jobData['EmpType_Id']))
		{
			$viewEmployeeType = $dbEmpType->viewEmployeeType($jobData['EmpType_Id']);
			$benefit = $viewEmployeeType['Benefits_Applicable'];
		}
		
		//check benefit employee type
		if (!($benefit != 0 && array_key_exists('Pf_PolicyNo', $jobData)))
		{
			$jobData['Pf_PolicyNo'] = new Zend_Db_Expr('NULL');
		}

        /**Designation, Location & Manager History.**/
        $getExistRec = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->empJob, array('Designation_Id','Location_Id','Manager_Id','Department_Id'))
									->where('Employee_Id = ?',$employeeId));
        
        //Designation History.
        if($formData['jobInfoDataArr']['Designation_Id'] != $getExistRec['Designation_Id'] && $getExistRec['Designation_Id'] != NULL && $getExistRec['Designation_Id'] > 0)
        {
            $maxHistoryId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empDesHistory,array(new Zend_Db_Expr('Max(History_Id)')))->where('Employee_Id = ?',$employeeId));
            
            //get already existing Max To_Date for the employee
            $getExistDesHistory = $this->_db->select()->from($this->_ehrTables->empDesHistory,
                                                                array('To_Date'))
                                                                ->where('Employee_Id = ?',$employeeId);
            if(!empty($maxHistoryId))                                
                    $getExistDesHistory->where('History_Id = ?',($this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empDesHistory,array(new Zend_Db_Expr('Max(History_Id)')))->where('Employee_Id = ?',$employeeId))));
                    
            $getExistDesHistory = $this->_db->fetchOne($getExistDesHistory);
            
            //$fromDate = (!empty($getExistDesHistory) ? (date('Y-m-d', strtotime($getExistDesHistory . ' +1 day'))) : $formData['jobInfoDataArr']['Date_Of_Join']);
            //If the To_Date exists for the employee then the from date will be To_Date+1day, else it will date of join
            $fromDate = (!empty($getExistDesHistory) ? (($getExistDesHistory == date('Y-m-d')) ? $getExistDesHistory : (date('Y-m-d', strtotime($getExistDesHistory . ' +1 day')))) : $formData['jobInfoDataArr']['Date_Of_Join']);
            
            $desHistoryArr = array('Employee_Id' => $employeeId,
                                   'Previous_Designation_Id' => $getExistRec['Designation_Id'],
                                   //'From_Date' => $formData['jobInfoDataArr']['Date_Of_Join'],
                                   'From_Date' => $fromDate,
                                   'To_Date' => date('Y-m-d'),
                                   'Modified_By' => $sessionId,
                                   'Modified_On' => date('Y-m-d H:i:s'));
		}

		$depHistoryArr = $this->getEmployeeDepartmentHistory($formData,$getExistRec,$employeeId,$sessionId);
		
		//Manager History.
        if($formData['jobInfoDataArr']['Manager_Id'] != $getExistRec['Manager_Id'] && $getExistRec['Manager_Id'] != NULL)
        {
            $maxHistoryId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empManagerHistory,array(new Zend_Db_Expr('Max(History_Id)')))->where('Employee_Id = ?',$employeeId));
            //get already existing Max To_Date for the employee
            $getExistManagerHistory = $this->_db->select()->from($this->_ehrTables->empManagerHistory,
                                                                array('To_Date'))
                                                                ->where('Employee_Id = ?',$employeeId);
            if(!empty($maxHistoryId))                                
                $getExistManagerHistory->where('History_Id = ?',$maxHistoryId);
                    
            $getExistManagerHistory = $this->_db->fetchOne($getExistManagerHistory);
            
            //If the To_Date exists for the employee then the from date will be To_Date+1day, else it will date of join
            $fromDate = (!empty($getExistManagerHistory) ? (($getExistManagerHistory == date('Y-m-d')) ? $getExistManagerHistory : (date('Y-m-d', strtotime($getExistManagerHistory . ' +1 day')))) : $formData['jobInfoDataArr']['Date_Of_Join']);
			
			//When the manager id is zero we need to update the employee id as manager id
			if($getExistRec['Manager_Id']==0)
			{
				$getExistRec['Manager_Id'] = $employeeId;
			}

            $managerHistoryArr = array('Employee_Id' => $employeeId,
                                       'Previous_Manager_Id' => $getExistRec['Manager_Id'],
                                       'From_Date' => $fromDate,
                                       //'From_Date' => $formData['jobInfoDataArr']['Date_Of_Join'],
                                       'To_Date' => date('Y-m-d'),
                                       'Modified_By' => $sessionId,
                                       'Modified_On' => date('Y-m-d H:i:s'));
        }
        
        //Location History
        if($formData['jobInfoDataArr']['Location_Id'] != $getExistRec['Location_Id'] && $getExistRec['Location_Id'] != NULL && $getExistRec['Location_Id'] > 0)
        {
            $maxHistoryId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empLocHistory,array(new Zend_Db_Expr('Max(History_Id)')))->where('Employee_Id = ?',$employeeId));
            
            //get already existing Max To_Date for the employee
            $getExistLocHistory = $this->_db->select()->from($this->_ehrTables->empLocHistory,
                                                                array('To_Date'))
                                                                ->where('Employee_Id = ?',$employeeId);
            if(!empty($maxHistoryId))                                
                $getExistLocHistory->where('History_Id = ?',$maxHistoryId);
                //$getExistManagerHistory->where('History_Id = ?',($this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empManagerHistory,array(new Zend_Db_Expr('Max(History_Id)')))->where('Employee_Id = ?',$employeeId))));
                    
            $getExistLocHistory = $this->_db->fetchOne($getExistLocHistory);
            
            //$fromDate = (!empty($getExistLocHistory) ? (date('Y-m-d', strtotime($getExistLocHistory . ' +1 day'))) : $formData['jobInfoDataArr']['Date_Of_Join']);
            //If the To_Date exists for the employee then the from date will be To_Date+1day, else it will date of join
            $fromDate = (!empty($getExistLocHistory) ? (($getExistLocHistory == date('Y-m-d')) ? $getExistLocHistory : (date('Y-m-d', strtotime($getExistLocHistory . ' +1 day')))) : $formData['jobInfoDataArr']['Date_Of_Join']);
            
            $locHistoryArr = array('Employee_Id' => $employeeId,
                                   'Previous_Location_Id' => $getExistRec['Location_Id'],
                                   //'From_Date' => $formData['jobInfoDataArr']['Date_Of_Join'],
                                   'From_Date' => $fromDate,
                                   'To_Date' => date('Y-m-d'),
                                   'Modified_By' => $sessionId,
                                   'Modified_On' => date('Y-m-d H:i:s'));
        }
        
        
		
		/**
		 *	Check job details exist for that employee id,
		 *	If exist then update form details, otherwise insert form details
		*/
		if (!empty($isJobInfoExist))
		{
			$jobUpdated = $this->_db->update($this->_ehrTables->empJob, $jobData, 'Employee_Id='.(int)$employeeId);
		}
		else
		{
			/** Employee Id differs while add emp job details. It will take lastinserted+1 value to insert. But sometimes may differ due to deletion of records.
				So last inserted employee id in personal info table is used here **/
			$jobData['Employee_Id'] = $employeeId;
				
			$jobUpdated = $this->_db->insert($this->_ehrTables->empJob, $jobData);
		}        		
		
		if ($jobUpdated)
		{
			++$isUpdated;
			
            
            if(!empty($desHistoryArr)&&$desHistoryArr['From_Date']!='NULL')
            {
                //If History already exists for $desHistoryArr['To_Date'] we should not add it again.
                $dtExists =  $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empDesHistory,
																array('History_Id'))
																->where('Employee_Id = ?',$desHistoryArr['Employee_Id'])
                                                                ->where('To_Date = ?',$desHistoryArr['To_Date']));
                if(!$dtExists)
                    $updated = $this->_db->insert($this->_ehrTables->empDesHistory, $desHistoryArr);
			}
	
			$updated = $this->updateEmployeeDeparmentHistory($depHistoryArr);
			
			$empExists = $this->_db->select()->from(array('e'=>$this->_ehrTables->empPersonal),
													new Zend_Db_Expr('count(e.Employee_Id)'))
													->where('e.Employee_Id = ?', $employeeId);
													
			$isExistEmp= $this->_db->fetchOne($empExists);
			
            if($isExistEmp == 0)
			{
				if(!empty($managerHistoryArr)&&$managerHistoryArr['From_Date']!='NULL')
				{
					//If History already exists for $managerHistoryArr['To_Date'] we should not add it again.
					$dtExists =  $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empManagerHistory,
																	array('History_Id'))
																	->where('Employee_Id = ?',$managerHistoryArr['Employee_Id'])
																	->where('To_Date = ?',$managerHistoryArr['To_Date']));
					if(!$dtExists)
						$updated = $this->_db->insert($this->_ehrTables->empManagerHistory, $managerHistoryArr);
				}
			
             
            if(!empty($locHistoryArr)&&!$locHistoryArr['From_Date']!='NULL')
            {
                //If History already exists for $locHistoryArr['To_Date'] we should not add it again.
                $dtExists =  $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empLocHistory,
																array('History_Id'))
																->where('Employee_Id = ?',$locHistoryArr['Employee_Id'])
                                                                ->where('To_Date = ?',$locHistoryArr['To_Date']));
                if(!$dtExists)                    
                    $updated = $this->_db->insert($this->_ehrTables->empLocHistory, $locHistoryArr);
            }
			}
			//$isExistOrgContact = $this->_db->fetchOne($this->_db->select()
			//										  ->from($this->_ehrTables->orgContact, new Zend_Db_Expr('COUNT(Employee_Id)'))
			//										  ->where('Employee_Id = ?', $employeeId));
			//
			////Update employee mail details in organization table
			//if ($isExistOrgContact > 0)
			//{
			//	$orgUpdateData = array('Email_Id' => $jobData['Emp_Email']);
			//	
			//	$this->_db->update($this->_ehrTables->orgContact, $orgUpdateData, 'Employee_Id = '. (int)$employeeId);
			//	
			//	$whereOrgCode['Org_Code = ?'] = $this->_ehrTables->getOrgCode();
			//	$this->_salesDb->update($this->_ehrTables->regUser, $orgUpdateData, $whereOrgCode);
			//}
			
			$getEmpGrade = $this->_db->fetchRow($this->_db->select()
												->from(array('D'=>$this->_ehrTables->designation),array(''))
												
												->joinInner(array('G'=>$this->_ehrTables->empGrade),'G.Grade_Id = D.Grade_Id',
															array('G.Grade_Id', 'G.Min_AnnualSalary', 'G.Max_AnnualSalary',
																  'G.Min_HourlyWages', 'G.Max_HourlyWages', 'G.Min_OvertimeWages',
																  'G.Max_OvertimeWages'))
												
												->where('D.Designation_Id = ?', $jobData['Designation_Id']));
			
			//if location has changed
			if ($detailsBeforeUpdate['Location_Id'] != $jobData['Location_Id'] && $personalData['Form_Status'] == 1)
			{
				$changedLocationAllowance = $dbAllowance->getGradeAllowances($detailsBeforeUpdate['Grade_Id'],'LOC');
				
				if ($changedLocationAllowance[0] == "GRADE_LOC")
				{
					/* Prerequisites for setting the salary recalculation flag is not set correctly. It has to be changed. */
					$changedLocationAllowanceEmp = $dbDesignation->getAllowanceCoverageEmp($detailsBeforeUpdate['Grade_Id'],
																						   $detailsBeforeUpdate['Designation_Id'],
																						   $changedLocationAllowance);
				}
				
				$affectedLocEmp = array_unique(array_merge($affectedLocationAllowanceEmp, $changedLocationAllowanceEmp));
				$allowanceReset = in_array($employeeId, $affectedLocEmp);
				
				$salDetailsArr['Location_Id'] = $jobData['Location_Id'];

				if ($allowanceReset)
				{
					$dbAllowance->setSalRecal(array($employeeId), 1);
					
					$msg = (empty($msg)) ? 'Please update the salary for this employee' : $msg;
				}
			}	 
			
			//if designation has changed
			if ($detailsBeforeUpdate['Grade_Id'] != $getEmpGrade['Grade_Id'] && $personalData['Form_Status'] == 1)
			{
				/** If previous grade and new grade differs, set the salary recalculation flag for the employees based on designation **/
				$dbAllowance->setSalRecal(array($employeeId), 1);
				
				/** while edit message has to be updated **/
				if(!empty($action) && $action != 'add' && empty($msg))
				{	
					$msg = 'Please update the salary for this employee';
				}

				$salDetailsArr['Grade_Id']=$getEmpGrade['Grade_Id'];
			}

			/* If the employee location or grade is changed then it has to be updated in the employee salary */
			if(!empty($salDetailsArr)){
				$isEmpGraUpdatedInSalary = $this->updateEmpLocGradeInSalary($salDetailsArr, $employeeId, $formName, $sessionId);

				if(empty($isEmpGraUpdatedInSalary)){
					$msg = (empty($msg)) ? 'Please update the salary for this employee' : $msg;
				}
			}
		}
		
		/**
		 *	contact information details
		 */
		$contactData = $formData['contactInfoDataArr'];
		
		$isContactInfoExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empContacts, array('Employee_Id'))
											   ->where('Employee_Id = ?', $employeeId));
		
		if (!empty($isContactInfoExist))
		{
			$contactUpdated = $this->_db->update($this->_ehrTables->empContacts, $contactData, 'Employee_Id='.(int)$employeeId);
		}
		else
		{
			/** Employee Id differs while add emp contact details. It will take lastinserted+1 value to insert. But sometimes may differ due to deletion of records.
				So last inserted employee id in personal info table is used here **/				
			$contactData['Employee_Id'] = $employeeId;
			
			$contactUpdated = $this->_db->insert($this->_ehrTables->empContacts, $contactData);
		}
		++$isUpdated;
		//if ($contactUpdated)
		//{
		//	++$isUpdated;
		//	
		//	/**
		//	 *	Update contact information in organization table
		//	*/
		//	$orgEmpId = $this->_db->fetchOne($this->_db->select()
		//								 ->from($this->_ehrTables->orgContact, new Zend_Db_Expr('COUNT(Employee_Id)'))
		//								 ->where('Employee_Id = ?', $employeeId));
		//	
		//	if ($orgEmpId > 0)
		//	{
		//		$orgContactData = array('Landline_No' => $contactData['Land_Line_No'],
		//								'Mobile_No'   => $contactData['Mobile_No']);
		//		
		//		$this->_db->update($this->_ehrTables->orgContact, $orgContactData, 'Employee_Id = '. (int)$employeeId);
		//		
		//		$whereOrgCode['Org_Code = ?'] = $this->_ehrTables->getOrgCode();
		//		
		//		$this->_salesDb->update($this->_ehrTables->regUser, $orgContactData, $whereOrgCode);
		//	}
		//}
		
		/**
		 *	Career details
		*/
		$isCareerInfoExist = $this->_db->fetchOne($this->_db->select()
												  ->from($this->_ehrTables->empSkillset, array('Employee_Id'))
												  ->where('Employee_Id = ?', $employeeId));
		
		if (array_key_exists('careerInfoDataArr', $formData))
		{
			$careerData = $formData['careerInfoDataArr'];
			
			if (!empty($isCareerInfoExist))
			{
				$careerUpdated = $this->_db->update($this->_ehrTables->empSkillset, $careerData, 'Employee_Id='.(int)$employeeId);
			}
			else
			{
				/** Employee Id differs while add career details. It will take lastinserted+1 value to insert. But sometimes may differ due to deletion of records.
				So last inserted employee id in personal info table is used here **/				
				$careerData['Employee_Id'] = $employeeId;
			
				$careerUpdated = $this->_db->insert($this->_ehrTables->empSkillset, $careerData);
			}
			
			if ($careerUpdated)
				++$isUpdated;
		}
		else
		{
			if (!empty($isCareerInfoExist))
			{
				$careerUpdated = $this->_db->delete($this->_ehrTables->empSkillset, 'Employee_Id = '.(int)$employeeId);
				
				if ($careerUpdated)
					++$isUpdated;
			}
		}
		
		/**
		 *	Bank Details
		*/
		// $isBankInfoExist = $this->_db->fetchOne($this->_db->select()
		// 										  ->from($this->_ehrTables->empBank, array('Employee_Id'))
		// 										  ->where('Employee_Id = ?', $employeeId));
		
		// if (array_key_exists('bankInfoDataArr', $formData))
		// {
		// 	$bankData = $formData['bankInfoDataArr'];					
			
		// 	if (!empty($isBankInfoExist))
		// 	{
		// 		$bankUpdated = $this->_db->update($this->_ehrTables->empBank, $bankData, 'Employee_Id = '.(int)$employeeId);
		// 	}
		// 	else
		// 	{
		// 		/** Employee Id differs while add bank details. It will take lastinserted+1 value to insert. But sometimes may differ due to deletion of records.
		// 		So last inserted employee id in personal info table is used here **/				
		// 		$bankData['Employee_Id'] = $employeeId;
				
		// 		$bankUpdated = $this->_db->insert($this->_ehrTables->empBank, $bankData);
		// 	}
			
		// 	if ($bankUpdated)
		// 		++$isUpdated;
		// }
		// else
		// {
		// 	if (!empty($isBankInfoExist))
		// 	{
		// 		$bankUpdated = $this->_db->delete($this->_ehrTables->empBank, 'Employee_Id = '.(int)$employeeId);
				
		// 		if ($bankUpdated)
		// 			++$isUpdated;
		// 	}
		// }
		
        if($isUpdated)
        {
            /** Update the DataSetup status **/
            if($btnClick == 'draft')
            {
                $isUpdated = $this->_dbCommonFun->updateDataSetupDashboard('Inprogress','18');            
            }
            else if($btnClick == 'finish')
            {
                $isUpdated = $this->_dbCommonFun->updateDataSetupDashboard('Completed','18');            
            }
        }
        
		if(!empty($eligibleForLeaveBalance))
		{
			$leaveBalanceUpdated = $this->_dbLeave->empDOJUpdateEligibleDays($eligibleForLeaveBalance['Employee_Id'],'update-employee-details-draft');
		}
		/**
		 *	this function will handle
		 *		update system log function
		 *		clear submit lock fucntion
		 *		return success/failure array
	    */
		$result = $this->_dbCommonFun->updateResult (array('updated'        => $isUpdated,
														   'action'         => 'Edit',
														   'trackingColumn' => $employeeId,
														   'formName'       => $formName,
														   'sessionId'      => $sessionId,
														   'tableName'      => $this->_ehrTables->empPersonal));
		
		if ($result['success'])
		{
			$result['info'] = $msg;
			
			/** If the designation is changed and previous grade and new grade differs means, salary recalculation
			will be set. If salary recalculation is set, $msg will be shown along with success message **/
			if($msg != '')
			{
				$result['msg'] = $result['msg'].'. '.$msg;	
			}
						
			$result['employeeId'] = $employeeId;
					
			if(isset($personalData['Photo_Path']))
				$result['photoPath'] = $personalData['Photo_Path'];
		}
		
		return $result;
	}
	
	/*get the employee department history details based on the above values*/
	public function getEmployeeDepartmentHistory($formData,$getExistRec,$employeeId,$sessionId)
	{
		$depHistoryArr = array();
		//when the department is changed we need to update the department history.
		if($formData['jobInfoDataArr']['Department_Id'] != $getExistRec['Department_Id'] && $getExistRec['Department_Id'] != NULL && $getExistRec['Department_Id'] > 0)
        {
	        $maxHistoryId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empDepHistory,array(new Zend_Db_Expr('Max(History_Id)')))->where('Employee_Id = ?',$employeeId));
            
			//get the latest to date for the department history
		    $getLastToDate = $this->_db->select()->from($this->_ehrTables->empDepHistory,array('To_Date'))
                                                                ->where('Employee_Id = ?',$employeeId);
            if(!empty($maxHistoryId))                                
                    $getLastToDate->where('History_Id = ?',$maxHistoryId);
                    
            $getLastToDate = $this->_db->fetchOne($getLastToDate);
            
            //If the To_Date exists for the employee then the from date will be To_Date+1day, else it will date of join
            $fromDate = (!empty($getLastToDate) ? (($getLastToDate == date('Y-m-d')) ? $getLastToDate : (date('Y-m-d', strtotime($getLastToDate . ' +1 day')))) : $formData['jobInfoDataArr']['Date_Of_Join']);
	 
		    $depHistoryArr = array('Employee_Id' => $employeeId,
                                   'Previous_Department_Id' => $getExistRec['Department_Id'],
                                   'From_Date' => $fromDate,
                                   'To_Date' => date('Y-m-d'),
                                   'Modified_By' => $sessionId,
                                   'Modified_On' => date('Y-m-d H:i:s'));
		}

		return $depHistoryArr;
	}

	/*Insert the employee department history details based on the value*/
	public function updateEmployeeDeparmentHistory($depHistoryArr)
	{
		$updated = 0;	
		if(!empty($depHistoryArr) && !empty($depHistoryArr['From_Date']))
		{
			$historyId =  $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empDepHistory,array('History_Id'))
															->where('To_Date = ?',$depHistoryArr['To_Date'])
															->where('Employee_Id = ?',$depHistoryArr['Employee_Id']));

			// if the department history is updated multiple times for same date we should not update the history												
			if(!$historyId)
				$updated = $this->_db->insert($this->_ehrTables->empDepHistory, $depHistoryArr);
			
		}
		return $updated;
	}
	/**
	 * Delete employee
	 */
    public function deleteEmployee ($employeeId, $sessionId, $formName)
    {
		$deleted = 0;
		
        $checkLock = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empPersonal, 'Lock_Flag')
										->where('Employee_Id = ?', $employeeId));
        
        $salaryEmployeeId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->salary, 'Employee_Id')
										->where('Employee_Id = ?', $employeeId));
        
        $attendanceEmployeeId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendance, 'Employee_Id')
										->where('Employee_Id = ?', $employeeId));
        
        $leavesEmployeeId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empLeaves, 'Employee_Id')
										->where('Employee_Id = ?', $employeeId));

        /** Validate the leave balance is imported for the employee */
		$isLeaveBalanceExist = $this->_db->fetchOne($this->_db->select()->from(array('L'=>$this->_ehrTables->leaveImport),array(new Zend_Db_Expr('COUNT(L.Leave_Import_Id)')))
								->joinInner(array('EJ'=>$this->_ehrTables->empJob),'EJ.User_Defined_EmpId=L.User_Defined_EmpId',array(''))
								->where('EJ.Employee_Id = ?', $employeeId));

        $resignationEmployeeId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->resignation, 'Employee_Id')
										->where('Employee_Id = ?', $employeeId));
		
		$emMembersEmployeeId   = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->emMembers, 'Employee_Id')
										->where('Employee_Id = ?', $employeeId));								
		
		$isReporteesExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob, new Zend_Db_Expr('Count(Employee_Id)'))
							->where('Manager_Id = ?', $employeeId));

		if(empty($salaryEmployeeId) && empty($attendanceEmployeeId) && empty($leavesEmployeeId) && empty($resignationEmployeeId) && empty($emMembersEmployeeId) && empty($isLeaveBalanceExist))
		{
			if($isReporteesExist > 0){
				return array('success'=>false, 'msg'=> 'Employee record cannot be deleted as the reportees are associated.', 'type'=>'info');
			}else{
				if ($checkLock == 0)
				{			
					$tableArr = array('empHobbies', 'empLang', 'empContacts', 'empEducation', 'empCertifications', 'empExperience', 'empAssets',
									'empDependent', 'empPassport', 'empSkillset', 'empDrivingLicense', 'empAward', 'empTraining', 'empProject',
									'empBank', 'empLogin', 'empPersonal', 'empEligbleLeave','empAccessRights','customEmployeeGroupEmployees');
					
					foreach ($tableArr as $row)
					{
						$this->checkEmployeeDetails ($this->_ehrTables->$row, $employeeId);
					}
					
					$deleted = $this->checkEmployeeDetails ($this->_ehrTables->empJob, $employeeId);
				}
				
				return $this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
																'lockFlag'       => $checkLock,
																'formName'       => $formName,
																'trackingColumn' => $employeeId,
																'sessionId'      => $sessionId));
			}
		}
		else
		{
			return array('success'=>false, 'msg'=>'<div>Employee record cannot be deleted at the moment as the employee has got one or more of the following exists.
			<li>	1) Salary has been added for this employee.</li>
			<li>	2) Attendance has been added for this employee.</li>
			<li>	3) Leaves has been added for this employee.</li>
			<li>	4) Resignation has been added for this employee.</li>
			<li>	5) Employee monitoring member details has been added for this employee.</li>
			<li>	6) Leave balance has been imported for this employee.</li>

			</div>
			Please delete the above record before deleting the employee record', 'type'=>'info');
		}
    }
	
	/**
	 *	List employee dependent details
	*/
	public function listEmployeeDependent ($page, $rows, $sortField, $sortOrder, $searchAll, $employeeId)
	{
		if (!empty($employeeId))
		{
			/**
			 *	Define sort fields based on grid column index
			*/
			switch($sortField)
			{
				case 0: $sortField = 'Dependent_First_Name'; break;
				case 1: $sortField = 'Relationship'; break;
				case 2: $sortField = 'Dependent_DOB'; break;
			}
			
			/**
			 *	Query to get employee dependent details
			*/
			$qryDependent = $this->_db->select()
									->from($this->_ehrTables->empDependent,
										   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS Dependent_Id as count'), 'Dependent_Id','Employee_Id',
										   'Dependent_First_Name','Dependent_Last_Name','Gender','Relationship','Dependent_DOB as DOB',
												 new Zend_Db_Expr("CONCAT(Dependent_First_Name, ' ',Dependent_Last_Name) as Dependent_Name"),
												 new Zend_Db_Expr("DATE_FORMAT(Dependent_DOB,'".$this->_orgDF['sql']."') as Date_Of_Birth"),
												 'DT_RowClass' => new Zend_Db_Expr('"dependent"'),
												 'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', Dependent_Id)")))
									
									->where('Employee_Id = ? ', $employeeId)
									->order("$sortField $sortOrder")
									->limit($rows, $page);
			
			/**
			 *	Search All columns using single input
			*/
			if (!empty($searchAll) && $searchAll != null)
			{
				$conditions = $this->_db->quoteInto('Dependent_First_Name Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Dependent_Last_Name Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Relationship Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Dependent_DOB Like ?', "%$searchAll%");
				
				$qryDependent->where($conditions);
			}
			
			/**
			 * SQL queries
			 * Get data to display
			*/
			$dependent = $this->_db->fetchAll($qryDependent);
			
			/* Data set length after filtering */
			$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
			
			/* Total data set length */
			$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empDependent, new Zend_Db_Expr('COUNT(Dependent_Id)'))
										   ->where('Employee_Id = ? ', $employeeId));
			
			/**
			 * Output array
			*/
			return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $dependent);
		}
		else
		{
			/**
			 * Output array
			*/
			return array("iTotalRecords" => 0, "iTotalDisplayRecords" => 0, "aaData" => array());
		}
	}
	
	/**
	 *	add, edit employee dependent details
	*/
	public function updateDependent ($dependentData, $dependentId, $sessionId)
	{ 
									 
		$qryDependent = $this->_db->select()
									->from($this->_ehrTables->empDependent, array(new Zend_Db_Expr('COUNT(Dependent_Id)')))
									->where('Employee_Id = ?', $dependentData['Employee_Id'])
									->where('Dependent_First_Name = ?', $dependentData['Dependent_First_Name'])
									->where('Dependent_Last_Name = ?', $dependentData['Dependent_Last_Name'])
									->where('Relationship = ?', $dependentData['Relationship'])
									->where('Dependent_DOB = ?', $dependentData['Dependent_DOB']);
        
		if (!empty($dependentId))
        {
            $qryDependent->where('Dependent_Id != ?', $dependentId);
        }
        
		$checkExists = $this->_db->fetchOne($qryDependent);
		
		/**
		 *	Check dependent details if exists return error message or process add/update function
		*/
		if ($checkExists == 0)
		{
			/**
			 *	Check dependent id is greater than zero for run update process
			*/
		
					  
			if ($dependentId > 0)
			{
                $dependentEmployeeId = $this->_db->fetchOne($this->_db->select()->from(array('TC'=>$this->_ehrTables->taxConfiguration),array('Form16_Signatory'))
                                                            		->where('Form16_Signatory = ?', $dependentData['Employee_Id']));
            
                if(!empty($dependentEmployeeId))
                {
                    $previousRelationShip = $this->_db->fetchOne($this->_db->select()->from(array('ED'=>$this->_ehrTables->empDependent),array('Relationship'))
                                                                                                                     ->where('Relationship = ?', 'Father')
																													 ->where('Dependent_Id = ?', $dependentId));
																													
																												
                    $currentRelationShip = $dependentData['Relationship'];
                    if(!empty($previousRelationShip) && $previousRelationShip != $currentRelationShip)
                    {
                        return array('success' => false, 'msg' => 'You cant change the Relationship Because Father Name Is Tagged With Tax Configuration', 'type' => 'info');
                    }
                    else
                    {
                        $action = 'Edit';
						$updated = $this->_db->update($this->_ehrTables->empDependent, $dependentData, array('Dependent_Id = ?' => $dependentId));
						$updatedeligible= $this->updateEligible($dependentData);
	                }
				}
				else
				{
					$action = 'Edit';
					$updated = $this->_db->update($this->_ehrTables->empDependent, $dependentData, array('Dependent_Id = ?' => $dependentId));
					$updatedeligible= $this->updateEligible($dependentData);
					
				}
                
			}
			/**
			 *	If dependent id is empty then we process insertion
			*/
			else
			{				
		
				$action = 'Add';
				
				$updated = $this->_db->insert($this->_ehrTables->empDependent, $dependentData);
				
				if ($updated)
					$dependentId = $this->_db->lastInsertId();
					$updatedeligible= $this->updateEligible($dependentData);
			}

			
			/**
			 *	this function will handle
			 *		update system log function
			 *		clear submit lock fucntion
			 *		return success/failure array
			*/
			return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
															'action'         => $action,
															'trackingColumn' => $dependentId,
															'formName'       => 'Employee dependent',
															'sessionId'      => $sessionId));
		}
		else
		{
			return array('success' => false, 'msg' => 'Dependent details already exists', 'type' => 'info');
		}
	} 

  	public function updateEligible($dependentData)
  	{  
		$employeeEligibleLeave 	= array();
		$dbMaternityleave 		= new Employees_Model_DbTable_MaternitySlabs();  
		$dbLeave          		= new Employees_Model_DbTable_Leave();
	 	$employeeId       		= $dependentData['Employee_Id'];
		$employeeGender  		= $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empPersonal, array('Gender'))
		 																->where('Employee_Id = ?', $dependentData['Employee_Id']));
		$leaveTypeGender 		= array('All',$employeeGender);
		$leaveTypeIds			= $this->_db->fetchCol($this->_db->select()->from(array('LT'=>$this->_ehrTables->leavetype),array('LeaveType_Id'))
																								->where('LT.Leave_Status =?','Active')
																								->where('LT.Gender IN (?)', $leaveTypeGender)
																								->where('LT.Leave_Enforcement_Configuration =?',4));

		 if(!empty($dependentData['Employee_Id']) && !empty($leaveTypeIds))
		 {
			 $updated     = $dbLeave->empDOJUpdateEligibleDays($dependentData['Employee_Id'],'update-dependent-details',NULL,$leaveTypeIds,0);
		 }
		 else
		 {
			 $updated =  0;
		 }
		 return $updated;
   	}
	
	/**
	 *	Clone Employee 
	*/
	public function cloneEmployee ($cloneEmployeeData, $OldEmpId, $userIpAddress, $sessionId)
	{
		$existEmployeeId = 0;
		
		//clone employee in personal_info
		$empPersonalData = $this->_db->fetchRow($this->_db->select()
									->from($this->_ehrTables->empPersonal)
									->where('Employee_Id = ?', $OldEmpId));

		/** To check that employee data are already cloned or not **/
		if(!empty($empPersonalData['Emp_First_Name']) && !empty($empPersonalData['Emp_Last_Name'])
		&& !empty($empPersonalData['Nationality']) && !empty($empPersonalData['DOB']) &&
		!empty($empPersonalData['Blood_Group']) && !empty($empPersonalData['Marital_Status']))
		{
			$existEmployeeId = $this->checkEmployeeExists(0,$empPersonalData['Emp_First_Name'],
																	   $empPersonalData['Emp_Last_Name'],
																	   $empPersonalData['Nationality'],
																	   $empPersonalData['DOB'],
																	   $empPersonalData['Blood_Group'],
																	   $empPersonalData['Marital_Status']);
		}
		
		/** If employee data are not cloned already, then clone the employee data **/	
		if(empty($existEmployeeId))
		{
			$maxEmployeeId 						= $this->getMaxEmployeeId();
			$cloneEmployeeData['Employee_Id']   = $maxEmployeeId+1;

			$empPersonalData['Employee_Id']=$cloneEmployeeData['Employee_Id'];
			$updated = $this->_db->insert($this->_ehrTables->empPersonal, $empPersonalData);			
			
			//clone employee in emp_job
			$empJobData = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->empJob,array('*'))
																   		 ->where('Employee_Id = ?', $OldEmpId));
			
			$empJobData['Employee_Id']=$cloneEmployeeData['Employee_Id'];
			$empJobData['Date_Of_Join']=$cloneEmployeeData['DOJ'];
			$empJobData['Department_Id']=$cloneEmployeeData['Department_Id'];
			$empJobData['Designation_Id']=$cloneEmployeeData['Designation_Id'];
			$empJobData['EmpType_Id']=$cloneEmployeeData['EmpType_Id'];
			$empJobData['Location_Id']=$cloneEmployeeData['Location_Id'];
			$empJobData['Work_Schedule']=$cloneEmployeeData['Work_Schedule'];
			$empJobData['External_EmpId']=$cloneEmployeeData['External_EmpId'];
			$empJobData['Service_Provider_Id']=$cloneEmployeeData['Service_Provider_Id'];
			$empJobData['Confirmed']=0;
			$empJobData['Confirmation_Date']=NULL;
			//Get Probation date calculated based on Designation
			$probationPeriod = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->designation,array('Probation_Days'))
													->where('Designation_Id = ?',$cloneEmployeeData['Designation_Id']));

			if($probationPeriod >= 0)
			{
				$empJobData['Probation_Date'] = date('Y-m-d',strtotime('+'.$probationPeriod.' days',strtotime($cloneEmployeeData['DOJ'])));
			}
			else
			{
				$empJobData['Probation_Date'] =$cloneEmployeeData['DOJ'];
			}
			//$empJobData['Probation_Date']=NULL;
			$empJobData['Emp_Status']='Active';
			$empJobData['Emp_InActive_Date']=NULL;
			$empJobData['User_Defined_EmpId']=$cloneEmployeeData['User_Defined_EmpId'];
			
			$updated = $this->_db->insert($this->_ehrTables->empJob, $empJobData);
			
			/** If job details updated, then update employee eligible leave days **/
			if ($updated == 1 && !empty($cloneEmployeeData['DOJ']))
			{
				/* Refresh Custom Group Employees List */
				//$refreshCustomGroupList = $this->refreshCustomGroupEmployees(array($cloneEmployeeData['Employee_Id']),$userIpAddress,$sessionId,0);
				$refreshCustomGroupList = $this->callCustomGroupRefreshApi(array($cloneEmployeeData['Employee_Id']),$sessionId);
				$employeeLeaveBalUpdateDetails = array(
					'Employee_Id' => $cloneEmployeeData['Employee_Id'],
					'Date_Of_Join' => $cloneEmployeeData['DOJ'],
					'Probation_Date' => $empJobData['Probation_Date'],
					'Form_Status' => $empPersonalData['Form_Status'],
					'Is_Update' => true
				);
		
				/* Update the employee leave type balance */
				$eligibleForLeaveBalance = $this->_dbLeave->updateNewJoineeLeaveTypeBalance($employeeLeaveBalUpdateDetails,1);

				if(!empty($eligibleForLeaveBalance))
				{
					$leaveBalanceUpdated = $this->_dbLeave->empDOJUpdateEligibleDays($eligibleForLeaveBalance['Employee_Id'],'clone-employee-details');
				}
			}
			
			//clone employee in emp_language  
			$empLangData = $this->_db->fetchAll($this->_db->select()
										->from($this->_ehrTables->empLang)
										->where('Employee_Id = ?', $OldEmpId));
			
			$countLang	=	count($empLangData);
			for($i=0;$i<$countLang;$i++)
			{
				$empLangData[$i]['Employee_Id']	=	$cloneEmployeeData['Employee_Id'];
			}
			if(!empty($empLangData))
			{
				$updated = $this->_ehrTables->insertMultiple($this->_ehrTables->empLang, $empLangData);
			}
			
			
			//clone employee in emp_drivinglicense
			$empDrivingLicenseData = $this->_db->fetchRow($this->_db->select()
										->from($this->_ehrTables->empDrivingLicense)
										->where('Employee_Id = ?', $OldEmpId));
			
			if(!empty($empDrivingLicenseData))
			{
				$empDrivingLicenseData['Employee_Id']	=	$cloneEmployeeData['Employee_Id'];
				$updated = $this->_db->insert($this->_ehrTables->empDrivingLicense, $empDrivingLicenseData);
			}
			
			
			//clone employee in emp_passport
			$empPassportData = $this->_db->fetchRow($this->_db->select()
										->from($this->_ehrTables->empPassport)
										->where('Employee_Id = ?', $OldEmpId));
			
			if(!empty($empPassportData))
			{
				$empPassportData['Employee_Id']	=	$cloneEmployeeData['Employee_Id'];
				$updated = $this->_db->insert($this->_ehrTables->empPassport, $empPassportData);
			
			}
			
			//clone employee in emp_dependent
			$empDependentData = $this->_db->fetchAll($this->_db->select()
										->from($this->_ehrTables->empDependent)
										->where('Employee_Id = ?', $OldEmpId));
			
			
			$countDependent	=	count($empDependentData);
			for($i=0;$i<$countDependent;$i++)
			{
				$empDependentData[$i]['Employee_Id']	=	$cloneEmployeeData['Employee_Id'];
				//array_splice($empDependentData[$]);
				unset($empDependentData[$i]['Dependent_Id']);
			}
			if(!empty($empDependentData))
			{
				$updated = $this->_ehrTables->insertMultiple($this->_ehrTables->empDependent, $empDependentData);
			}
			
			
			//clone employee in emp_user
			$empUserName = '';

			/** The User_Name will be email id if the email authentication is enabled.
			 * The User_Name will be mobile no if the mobile number authentication is enabled*/
			if((int)$empPersonalData['Allow_User_Signin'] === 1) {
				if((int)$empPersonalData['Enable_Sign_In_With_Mobile_No'] === 1 ) {
					$empUserName = $empPersonalData['Sign_In_Mobile_No_Country_Code'].$empPersonalData['Sign_In_Mobile_Number'];
				} else {
					$empUserName = $empJobData['Emp_Email'];
				}
			}

			$empLoginData  = array( "Employee_Id" => $cloneEmployeeData['Employee_Id'],
									"User_Name" => $empUserName ? $empUserName : '',
									"Created_Date" => date('Y-m-d H:i:s'));
			/* Insert the login details in the emp_user table */
			$updated = $this->_db->insert($this->_ehrTables->empLogin, $empLoginData);

			//clone employee in emp_project
			$empProjectData = $this->_db->fetchAll($this->_db->select()
										->from($this->_ehrTables->empProject)
										->where('Employee_Id = ?', $OldEmpId));
			
			
			$countProject	=	count($empProjectData);
			for($i=0;$i<$countProject;$i++)
			{
				$empProjectData[$i]['Employee_Id']	=	$cloneEmployeeData['Employee_Id'];
			}
			if(!empty($empProjectData))
			{
				$updated = $this->_ehrTables->insertMultiple($this->_ehrTables->empProject, $empProjectData);
			}
			
			
			//clone employee in emp_experience
			$empExperienceData = $this->_db->fetchAll($this->_db->select()
										->from($this->_ehrTables->empExperience)
										->where('Employee_Id = ?', $OldEmpId));
			
			$countExperience	=	count($empExperienceData);
			for($i=0;$i<$countExperience;$i++)
			{
				$empExperienceData[$i]['Employee_Id']	=	$cloneEmployeeData['Employee_Id'];
				unset($empExperienceData[$i]['Experience_Id']);
			}
			if(!empty($empExperienceData))
			{
				$updated = $this->_ehrTables->insertMultiple($this->_ehrTables->empExperience, $empExperienceData);
			}
			
			
			//clone employee in emp_assets
			$empAssetsData = $this->_db->fetchAll($this->_db->select()
										->from($this->_ehrTables->empAssets)
										->where('Employee_Id = ?', $OldEmpId));
			
			$countAsset	=	count($empAssetsData);
			for($i=0;$i<$countAsset;$i++)
			{
				$empAssetsData[$i]['Employee_Id']	=	$cloneEmployeeData['Employee_Id'];
				unset($empAssetsData[$i]['Asset_Id']);
			}
			if(!empty($empAssetsData))
			{
				$updated = $this->_ehrTables->insertMultiple($this->_ehrTables->empAssets, $empAssetsData);
			}
			
			
			//clone employee in contact_details
			$empContactsData = $this->_db->fetchRow($this->_db->select()
										->from($this->_ehrTables->empContacts)
										->where('Employee_Id = ?', $OldEmpId));
			
			if(!empty($empContactsData))
			{
				$empContactsData['Employee_Id']	=	$cloneEmployeeData['Employee_Id'];
				$updated = $this->_db->insert($this->_ehrTables->empContacts, $empContactsData);
			}
			
			
			//clone employee in emp_education
			$empEducationData = $this->_db->fetchAll($this->_db->select()
										->from($this->_ehrTables->empEducation)
										->where('Employee_Id = ?', $OldEmpId));
			
			
			$countEducation	=	count($empEducationData);
			for($i=0;$i<$countEducation;$i++)
			{
				$empEducationData[$i]['Employee_Id']	=	$cloneEmployeeData['Employee_Id'];
				unset($empEducationData[$i]['Education_Id']);
			}
			if(!empty($empEducationData))
			{
				$updated = $this->_ehrTables->insertMultiple($this->_ehrTables->empEducation, $empEducationData);
			}
			
			
			//clone employee in emp_certifications
			$empCertificationsData = $this->_db->fetchAll($this->_db->select()
										->from($this->_ehrTables->empCertifications)
										->where('Employee_Id = ?', $OldEmpId));
			
			
			$countCertification	=	count($empCertificationsData);
			for($i=0;$i<$countCertification;$i++)
			{
				$empCertificationsData[$i]['Employee_Id']	=	$cloneEmployeeData['Employee_Id'];
				unset($empCertificationsData[$i]['Certification_Id']);
			}
			if(!empty($empCertificationsData))
			{
				$updated = $this->_ehrTables->insertMultiple($this->_ehrTables->empCertifications, $empCertificationsData);
			}
			
			
			//clone employee in emp_training
			$empTrainingData = $this->_db->fetchAll($this->_db->select()
										->from($this->_ehrTables->empTraining)
										->where('Employee_Id = ?', $OldEmpId));
			
			$countTraining	=	count($empTrainingData);
			for($i=0;$i<$countTraining;$i++)
			{
				$empTrainingData[$i]['Employee_Id']	=	$cloneEmployeeData['Employee_Id'];
				unset($empTrainingData[$i]['Training_Id']);
			}
			if(!empty($empTrainingData))
			{
				$updated = $this->_ehrTables->insertMultiple($this->_ehrTables->empTraining, $empTrainingData);
			}
			
			
			//clone employee in emp_awards
			$empAwardData = $this->_db->fetchAll($this->_db->select()
										->from($this->_ehrTables->empAward)
										->where('Employee_Id = ?', $OldEmpId));
			
			$countAward	=	count($empAwardData);
			for($i=0;$i<$countAward;$i++)
			{
				$empAwardData[$i]['Employee_Id']	=	$cloneEmployeeData['Employee_Id'];
				unset($empAwardData[$i]['Award_Id']);
			}
			if(!empty($empAwardData))
			{
				$updated = $this->_ehrTables->insertMultiple($this->_ehrTables->empAward, $empAwardData);
			}
			
			
			//clone employee in emp_skillset
			$empSkillsetData = $this->_db->fetchRow($this->_db->select()
										->from($this->_ehrTables->empSkillset)
										->where('Employee_Id = ?', $OldEmpId));
			
			if(!empty($empSkillsetData))
			{
				$empSkillsetData['Employee_Id']	=	$cloneEmployeeData['Employee_Id'];
				$updated = $this->_db->insert($this->_ehrTables->empSkillset, $empSkillsetData);
			}
			
			
			//clone employee in emp_bankdetails
			$empBankData = $this->_db->fetchRow($this->_db->select()
										->from($this->_ehrTables->empBank)
										->where('Employee_Id = ?', $OldEmpId));
			
			if(!empty($empBankData))
			{
				$empBankData['Employee_Id']	= $cloneEmployeeData['Employee_Id'];
				$empBankData['Bank_Id']		= 0;
				$updated = $this->_db->insert($this->_ehrTables->empBank, $empBankData);
			}
			
			
			//clone employee in emp_insurancepolicyno
			$empInsurancePolicyNoData = $this->_db->fetchAll($this->_db->select()
										->from($this->_ehrTables->empInsurancePolicyNo)
										->where('Employee_Id = ?', $OldEmpId));
			
			
			$countInsurance	=	count($empInsurancePolicyNoData);
			for($i=0;$i<$countInsurance;$i++)
			{
				$empInsurancePolicyNoData[$i]['Employee_Id']	=	$cloneEmployeeData['Employee_Id'];
				unset($empInsurancePolicyNoData[$i]['Policy_Id']);
			}
			
			if(!empty($empInsurancePolicyNoData))
			{
				$updated = $this->_ehrTables->insertMultiple($this->_ehrTables->empInsurancePolicyNo, $empInsurancePolicyNoData);
			}
			
			if(!empty($eligibleForLeaveBalance))
			{
				$leaveBalanceUpdated = $this->_dbLeave->empDOJUpdateEligibleDays($eligibleForLeaveBalance['Employee_Id'],'clone-employee-details');
			}
			
			if($updated)
			{
				if (strtolower($this->_partnerId) === 'camu') 
				{
					/** Add employee info log */
					$logData = array('Employee_Id'=>$cloneEmployeeData['Employee_Id'], 'Action'=>'Add', 'Log_Timestamp'=>date('Y-m-d H:i:s'),'Camu_Push_Status'=>'Open');
					$this->_db->insert($this->_ehrTables->employeeInfoTimestampLog, $logData);
					$this->callTrstScoreCreateApi($cloneEmployeeData['Employee_Id'],$cloneEmployeeData['Service_Provider_Id']);	
					$camuCreateStaff=$this->callCamuCreateApi($cloneEmployeeData['Employee_Id']);
				}
				else{
					/** Add employee info log */
					$logData = array('Employee_Id'=>$cloneEmployeeData['Employee_Id'], 'Action'=>'Add', 'Log_Timestamp'=>date('Y-m-d H:i:s'));
					$this->_db->insert($this->_ehrTables->employeeInfoTimestampLog, $logData);
					$this->callTrstScoreCreateApi($cloneEmployeeData['Employee_Id'],$cloneEmployeeData['Service_Provider_Id']);	
				}
				/** To Track who clone the employee data **/
				$this->_ehrTables->trackEmpSystemAction('Clone Employee Data - '.$cloneEmployeeData['Employee_Id'], $sessionId);
				
				return array('success' => true, 'msg'=>'Employee Cloned successfully','type'=>'success');
			}
			else
			{
				return array('success' => false, 'msg' => 'Unable to Clone Employee', 'type' => 'info');
			}
		}
		else
		{
			return array('success' => false, 'msg' => 'Duplicate employee details', 'type' => 'warning');
		}
	}
	
	//to generate user-name for clone employee
	 public function createUserName ($firstName, $lastName, $empId = NULL)
    {
        if (!empty($firstName) && !empty($lastName))
        {
            if (strlen($firstName) >= 7) //first name >= 7
            {
                // get first letter of the lastname and first 7 letter of first name
                $username = substr($lastName, 0, 1).substr($firstName, 0, 7);
            }
            else
            {
                $fnameLen = strlen($firstName);
                $remain = 8 - $fnameLen - 1; // username total val (8) - firtname length - 1
                // get first letter of username and firstname and last name from first position  to $remain position
                $username = substr($lastName, 0, 1).$firstName.substr($lastName, 0, $remain);
            }
			
            if (!is_null($empId))
            {
            	$loginDetails = $this->_dbPersonal->getEmpUsername($username, $empId); // check if username exist
            }
            else
            {
            	$loginDetails = $this->_dbPersonal->getEmpUsername($username); // check if username exist
            }
			
			if ($loginDetails > 0) //if username exists
            {
				$newUserName = substr($username, 0, 7); //get first 7 letter of username
                $newUserName = $newUserName.'1'; // add 1 as 8th letter
                $digitCount = 0;
                
				//checking weather the username already exist
                if (!is_null($empId))
                {
                	$loginDetails = $this->_dbPersonal->getEmpUsername ($username, $empId); 
                }
                else
                {
                	$loginDetails = $this->_dbPersonal->getEmpUsername ($username);
                }
                
                //$loginDetails = $this->_dbPersonal->getEmpUsername($newUserName);// check if username exist
                if ($loginDetails > 0)//if username exists
                {
                    return $this->createUserNameRecursive ($newUserName, $digitCount, $empId); // create new username
                }
                else
                {
                    return $newUserName;
                }
            }
            else
            {
                return $username;
            }
        }
    }
	
	public function createUserNameRecursive($userName, $digitCount, $empId = NULL)
    {
        // check if username has a number by each letter
        for($i =0;$i < 8;$i++)
        {
            $rest = substr($userName, $i, 1); //
            if (ctype_digit($rest)) // if letter is a number
            {
                if ($digitCount == 0)
                {
                    $noInUname = $rest;
                    $digitCount = $digitCount+1;
                }
                else
                {
                    $noInUname = $noInUname.$rest;
                    $digitCount = $digitCount+1;
                }
            }
        }
        if ($digitCount > 0) // if integer exists in username
        {
            $noAppended = $noInUname + 1; //
            $noCount = strlen($noAppended);
            $newUserName = substr($userName, 0, (8-$noCount)).$noAppended;
        }
        else
        {
            $newUserName = substr($userName, 0, 7).'1';
        }

        //checking weather the username already exist
        if (!is_null($empId))
        {
        	$loginDetails = $this->_dbPersonal->getEmpUsername($newUserName,$empId);
        }
        else
        {	 	
        	$loginDetails = $this->_dbPersonal->getEmpUsername($newUserName);
        }	
        $digitCount = 0;
        if ($loginDetails > 0) // if created username name exists, create another username
        {
            $newUname = $this->createUserNameRecursive($newUserName,$digitCount);
        }
        else
        {
            $newUname = $newUserName;
        }
        return $newUname;
    }
	
	//Generate password used while clone employee
	public function generatePassword($createUsername)
	{
		$emppwd = $createUsername;
		//$randomchars = '@1!$%234567890abcdefgh#&ijkmnopqr#stuvwxyz&ABCDEFGHIJK*LMNOPQ!RSTUVWXYZ';
		// 
		//$length = 8;
		//$emppwd = '';
		// 
		//for ($pcnt = 0; $pcnt<$length; $pcnt++) {
		//	$emppwd .= $randomchars[mt_rand(0, (strlen($randomchars)-1))];
		//}
		
		$password = md5($emppwd);
		
		$dynamicSalt = '';
		for ($cnt = 50; $cnt < 100; $cnt++)
		{
			$dynamicSalt .= chr(rand(33, 126));
		}
		$dynamicSalt = md5($dynamicSalt);
		$encPassword = $password.$dynamicSalt;
		$loginData = array(
			'Hrapp_Password'  => $encPassword,
			'Random_Salt'  => $dynamicSalt,
			'Created_Date'  => date('Y-m-d H:i:s'),
			);
		return $loginData;
	}
	
	/**
	 *	Delete Employee Dependent details
	*/
	public function deleteEmployeeDependent ($dependentId,$employeeId,$loginEmployeeId)
	{
		$dependentDetails = $this->_db->fetchRow($this->_db->select()->from(array('ED'=>$this->_ehrTables->empDependent),array('Employee_Id','Relationship'))
											  ->where('Dependent_Id = ?', $dependentId));
		if(!empty($dependentDetails))
		{
			$dependentEmployeeId = $this->_db->fetchOne($this->_db->select()->from(array('TC'=>$this->_ehrTables->taxConfiguration),array('Form16_Signatory'))
																						->where('Form16_Signatory = ?', $dependentDetails['Employee_Id']));
		}								  
		
		$maternityLeaveTypeQry = $this->_db->select()->from(array('leave'=>$this->_ehrTables->leavetype),array('leave.LeaveType_Id'))
																->where('leave.Leave_Status = ?','Active')
																->where('leave.Leave_Enforcement_Configuration = ?',4);
		$maternityLeaveType = $this->_db->fetchCol($maternityLeaveTypeQry);

		//If maternity leave type exist
		if(!empty($maternityLeaveType) && count($maternityLeaveType) > 0){
			//Get maternity leave exist count for the employee
			$employeeLeaves = $this->_db->fetchOne($this->_db->select()->from(array('leave'=>$this->_ehrTables->empLeaves), new Zend_Db_Expr('Count(LeaveType_Id)'))
									->where('leave.LeaveType_Id IN (?)', $maternityLeaveType)
									->where('leave.Employee_Id = ?',$employeeId));
		}else{
			$employeeLeaves = 0;
		}

		if($employeeLeaves == 0)
		{
           	if(empty($dependentEmployeeId) || (!empty($dependentEmployeeId) && $dependentDetails['Relationship']!='Father'))
            {
               	$deleted = $this->_db->delete ($this->_ehrTables->empDependent, 'Dependent_Id = '.$dependentId);
            
				if ($deleted)
                {    
					$updatedeligible= $this->updateEligible($dependentDetails); 
					
					$this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
															'lockFlag'       => 0,
															'formName'       => 'Employee dependent',
															'trackingColumn' => $dependentId,
															'sessionId'      => $loginEmployeeId));

					return array('success' => true, 'msg' => 'Employee dependent deleted successfully', 'type' => 'success');
			    }
              	else
               	{
                	return array('success' => false, 'msg' => 'Unable to delete employee dependent', 'type' => 'warning');
               	}
            }
            else
            {
              	return array('success' => false, 'msg' => 'Father details are tagged with tax configuration.please remove that before deleting the dependent record', 'type' => 'warning');
            }
		}
		else
		{
         	return array('success'=>false,'msg' => 'Employee already added a leave for the maternity leave type. Please delete that record','type' => 'warning');
		}
	}	
	
	/**
	 *	Delete dependent details based on marital status
	*/
	public function deleteEmployeeDependentByMaritalStatus ($employeeId, $maritalStatusId,$loginEmployeeId)
	{
		/**
		 *	If marital Status exist then we have to delete other than marital status with dependents
		*/
		$maritalStatus = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->maritalRelation, 'Dependent_Relationship')
											  ->where('Marital_Status_Id = ?', (int)$maritalStatusId));
        
        $maritalStatus = implode('","',$maritalStatus);
        $conditions = 'Employee_Id = '.$employeeId.' AND RelationShip NOT IN ("'. $maritalStatus .'")';
        
		$deleted = $this->_db->delete ($this->_ehrTables->empDependent, $conditions);
		
		if ($deleted)
		{
			$this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
												'lockFlag'       => 0,
												'formName'       => 'Employee dependent - Employee id',
												'trackingColumn' => $employeeId,
												'sessionId'      => $loginEmployeeId));

			return array('success' => true, 'msg' => 'Employee Dependent(s) deleted successfully', 'type' => 'success');
		}
		else
		{
			return array('success' => false, 'msg' => 'Unable to delete employee dependent(s)', 'type' => 'warning');
		}
	}
	
	
	
	/**
	 *	List employee experience details
	*/
	public function listEmployeeExperiences ($page, $rows, $sortField, $sortOrder, $searchAll, $employeeId, $isJobCandidateView)
	{
		if (!empty($employeeId))
		{
			/**
			 *	Define sort fields based on grid column index
			*/
			switch($sortField)
			{
				case 1: $sortField = 'Prev_Company_Name'; break;
				case 2: $sortField = 'Prev_Company_Location'; break;
				case 3: $sortField = 'Designation'; break;
				case 4: $sortField = 'Start_Date_Join'; break;
				case 5: $sortField = 'End_Date'; break;
				case 6: $sortField = 'Duration'; break;
			}
			
			/**
			 *	Query to get employee experience details
			*/
			$qryExperience = $this->_db->select()
									->from($this->_ehrTables->empExperience,
										   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS Experience_Id as count'), '*',
												 new Zend_Db_Expr("DATE_FORMAT(Start_Date_Join,'".$this->_orgDF['sql']."') as StartDateJoin"),
												 new Zend_Db_Expr("DATE_FORMAT(End_Date,'".$this->_orgDF['sql']."') as EndDate"),
												 'DT_RowClass' => new Zend_Db_Expr('"experience"'),
												 'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', Experience_Id)")))
									
									->where('Employee_Id = ? ', $employeeId)
									->limit($rows, $page);
			
			if($isJobCandidateView == 0)
				$qryExperience->order("$sortField $sortOrder");
			/**
			 *	Search All columns using single input
			*/
			if (!empty($searchAll) && $searchAll != null)
			{
				$conditions = $this->_db->quoteInto('Prev_Company_Name Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Prev_Company_Location Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Designation Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Start_Date_Join Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or End_Date Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Duration Like ?', "%$searchAll%");
				
				$qryExperience->where($conditions);
			}
			
			/**
			 * SQL queries
			 * Get data to display
			*/
			$experience = $this->_db->fetchAll($qryExperience);
			
			/* Data set length after filtering */
			$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
			
			/* Total data set length */
			$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empExperience, new Zend_Db_Expr('COUNT(Experience_Id)'))
										   ->where('Employee_Id = ? ', $employeeId));
			
		if(!empty($iTotal))
		{
			for($l=0;$l<count($experience);$l++)
			{
				/** get uploaded declaration uploaded files **/				
				$employeesDocumentFiles =  $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->empExperienceDocuments,array('File_Name','File_Size'))
					->where('Experience_Id = ?', $experience[$l]['Experience_Id']));
					
				$experience[$l]['Employees_Document_File_Path'] = $employeesDocumentFiles;
																  
			}
		}
			/**
			 * Output array
			*/
			return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $experience);
		}
		else
		{
			/**
			 * Output array
			*/
			return array("iTotalRecords" => 0, "iTotalDisplayRecords" => 0, "aaData" => array());
		}
	}
	
	/**
	 *	update, add employee experience details
	*/
	public function updateExperience ($experienceData, $experienceId, $sessionId)
	{
		/**
		 *	Check experience is already exists or not in database
		*/
		$qryExperience = $this->_db->select()
									->from($this->_ehrTables->empExperience, array(new Zend_Db_Expr('COUNT(Experience_Id)')))
									->where('Employee_Id = ?', $experienceData['Employee_Id'])
									->where('Start_Date_Join >= ?', $experienceData['Start_Date_Join'])
									->where('End_Date <= ?', $experienceData['End_Date']);
        
		if (!empty($experienceId))
        {
            $qryExperience->where('Experience_Id != ?', $experienceId);
        }
        
		$checkExists = $this->_db->fetchOne($qryExperience);
		
		/**
		 *	Check experience details if exists return error message or process add/update function
		*/
		if ($checkExists == 0)
		{
			/**
			 *	Check experience id is greater than zero for run update process
			*/
			if ($experienceId > 0)
			{
				$action = 'Edit';
				
				$updated = $this->_db->update($this->_ehrTables->empExperience, $experienceData, array('Experience_Id = ?' => $experienceId));
			}

			/**
			 *	If experience id is empty then we process insertion
			*/
			else
			{
				$action = 'Add';
				
				$updated = $this->_db->insert($this->_ehrTables->empExperience, $experienceData);
				
				if ($updated)
					$experienceId = $this->_db->lastInsertId();
			}
			$updated=1;

			
			/**
			 *	this function will handle
			 *		update system log function
			 *		clear submit lock fucntion
			 *		return success/failure array
			*/
			$result = $this->_dbCommonFun->updateResult (array('updated'        => $updated,
															'action'         => $action,
															'trackingColumn' => $experienceId,
															'formName'       => 'Employee experience',
															'sessionId'      => $sessionId));
			
			$result['Experience_Id']      = $experienceId;
			 
			return $result;
		}
		else
		{
			return array('success' => false, 'msg' => 'Date range already exists', 'type' => 'info');
		}
	}
	

//	Experience details-Document Upload
		/** add/edit employee documents**/
    public function updateExperienceFiles ($employeesDocumentDetails, $employeesDocumentFilesArr, $experienceId, $logEmpId, $formName)
    {
			
            if($experienceId >0)
            {
               $action = 'Edit';
			   
               //$updated = $this->_db->update($this->_ehrTables->empExperienceDocuments,$employeesDocumentDetails,'Experience_Id = ' .$experienceId);

			   $updated = 1;
			   
               $employeesDocumentDetails['Experience_Id'] = $experienceId;
            }
            else
            {
                $action = 'Add';
				$updated=1;
                //if ($updated)
                    $employeesDocumentDetails['Experience_Id'] = $this->_db->lastInsertId();
            }
            
			if ($updated)
			{
				if(!empty($employeesDocumentFilesArr))
				{
					foreach($employeesDocumentFilesArr as $key=>$row)
					{
						$employeesDocumentFilesArr[$key]['Experience_Id'] = $employeesDocumentDetails['Experience_Id'];	
					}
					
					$fUpdated =$this->_ehrTables->insertMultiple($this->_ehrTables->empExperienceDocuments, $employeesDocumentFilesArr);				
				}
			}
            
            $trackSysLog = $this->_dbCommonFun->updateResult (array('updated'        => $updated,
                                                                    'action'         => $action,
                                                                    'trackingColumn' => $employeesDocumentDetails['Experience_Id'],
                                                                    'formName'       => $formName,
                                                                    'sessionId'      => $logEmpId,
                                                                    'tableName'      => $this->_ehrTables->empExperienceDocuments));
            
            
            $trackSysLog['Experience_Id']    = $employeesDocumentDetails['Experience_Id'];
            
            return $trackSysLog;                   
    }
	
	public function deleteEmployeesDocumentFiles($experienceId,$employeesDocumentFileName)
	{

		$deleted = 0;
		
		if(!empty($experienceId))
		{
			$where = $this->_db->quoteInto('Experience_Id = ? AND ', $experienceId).
								 $this->_db->quoteInto('File_Name = ?', $employeesDocumentFileName);
			
			$deleted = $this->_db->delete($this->_ehrTables->empExperienceDocuments, $where);
		}
		
		if($deleted)
		{
			return array('success'=> true, 'msg' => 'Document deleted successfully', 'comboPair' => '', 'type' => 'info');
		}
		else
		{
			return array('success'=> false, 'msg' => 'Unable to delete Files', 'type' => 'warning');	
		}
	}
	
//	delete experience
	public function deleteEmployeeExperienceDetails($experienceId)
	{
		$deleted = $this->_db->delete ($this->_ehrTables->empExperience, 'Experience_Id = '. $experienceId);
		
		if ($deleted)
		{
			return array('success' => true, 'msg' => 'Employee experience deleted successfully', 'type' => 'success');
		}
		else
		{
			return array('success' => false, 'msg' => 'Unable to delete employee experience', 'type' => 'warning');
		}
	}
	
	
	/**
	 *	Delete Employee Experience documents
	*/
	public function deleteEmployeeExperience ($experienceId, $docExists, $deleteFiles)
	{
				if ($docExists)
				{
					/** If files are deleted from s3 bucket it has to deleted from table **/
					if(count($deleteFiles) > 0)
					{
						/** delete employees documents **/
						foreach($deleteFiles as $key=>$file)
						{
							$where = '';				
							$where = array('Experience_Id = ?' => $experienceId,'File_Name = ?' => $file['Name']);
							
							$this->_db->delete($this->_ehrTables->empExperienceDocuments, $where);	
						}
					}
					
					$documentExists = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empExperienceDocuments, new Zend_Db_Expr('count(Experience_Id)'))
												->where('Experience_Id = ?', $experienceId));
					
					/** If all the documents are deleted for the document Id from s3 bucket**/
					if(empty($documentExists))
					{
						$deleted = $this->_db->delete($this->_ehrTables->empExperience,'Experience_Id ='.$experienceId);
					}
				}
				else
				{
					/** delete the document rec **/
					$deleted = $this->_db->delete($this->_ehrTables->empExperience,'Experience_Id ='.$experienceId);
				}
			
			if ($deleted)
			{
				return array('success' => true, 'msg' => 'Employee experience deleted successfully', 'type' => 'success');
			}
			else
			{
				return array('success' => false, 'msg' => 'Unable to delete employee experience', 'type' => 'warning');
			}
		
	}
	
	/**
	 *	List employees assets details
	*/
	public function listEmployeeAssets ($page, $rows, $sortField, $sortOrder, $searchAll, $employeeId)
	{
		if (!empty($employeeId))
		{
			/**
			 *	Define sort fields based on grid column index
			*/
			switch($sortField)
			{
				case 1: $sortField = 'Asset_Name'; break;
				case 2: $sortField = 'Serial_No'; break;
				case 3: $sortField = 'Receive_Date'; break;
				case 4: $sortField = 'Return_Date'; break;
			}
			
			/**
			 *	Query to get employee asset details
			*/
			$qryAsset = $this->_db->select()
									->from($this->_ehrTables->empAssets,
										   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS Asset_Id as count'), '*',
												 new Zend_Db_Expr("DATE_FORMAT(Receive_Date,'".$this->_orgDF['sql']."') as ReceiveDate"),
												 new Zend_Db_Expr("DATE_FORMAT(Return_Date,'".$this->_orgDF['sql']."') as ReturnDate"),
												 'DT_RowClass' => new Zend_Db_Expr('"asset"'),
												 'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', Asset_Id)")))
									
									->where('Employee_Id = ? ', $employeeId)
									->order("$sortField $sortOrder")
									->limit($rows, $page);
			
			/**
			 *	Search All columns using single input
			*/
			if (!empty($searchAll) && $searchAll != null)
			{
				$conditions = $this->_db->quoteInto('Asset_Name Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Serial_No Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Receive_Date Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Return_Date Like ?', "%$searchAll%");
				
				$qryAsset->where($conditions);
			}
			
			/**
			 * SQL queries
			 * Get data to display
			*/
			$assets = $this->_db->fetchAll($qryAsset);
			
            for($i=0;$i<count($assets);$i++)
            {
                if($assets[$i]['Return_Date'] == "0000-00-00")
                {
                    $assets[$i]['ReturnDate'] = "-";
                }
            }
            
			/* Data set length after filtering */
			$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
			
			/* Total data set length */
			$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empAssets, new Zend_Db_Expr('COUNT(Asset_Id)'))
										   ->where('Employee_Id = ? ', $employeeId));
			
			/**
			 * Output array
			*/
			return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $assets);
		}
		else
		{
			/**
			 * Output array
			*/
			return array("iTotalRecords" => 0, "iTotalDisplayRecords" => 0, "aaData" => array());
		}
	}
	
	/**
	 *	update, add employee asset details
	*/
	public function updateAsset ($assetData, $assetId, $sessionId)
	{
		/**
		 *	Check asset name and serial no are unique for that employee
		*/
		$qryAsset = $this->_db->select()
									->from($this->_ehrTables->empAssets, array(new Zend_Db_Expr('COUNT(Asset_Id)')))
									->where('Employee_Id = ?', $assetData['Employee_Id'])
									->where('Asset_Name = ?', $assetData['Asset_Name'])
									->where('Serial_No = ?', $assetData['Serial_No']);
        
		if (!empty($assetId))
        {
            $qryAsset->where('Asset_Id != ?', $assetId);
        }
        
		$checkExists = $this->_db->fetchOne($qryAsset);
		
		if ($checkExists == 0)
		{
			/**
			 *	Check asset id is greater than zero for run update process
			*/
			if ($assetId > 0)
			{
				$action = 'Edit';
				
				$updated = $this->_db->update($this->_ehrTables->empAssets, $assetData, array('Asset_Id = ?' => $assetId));
			}
			/**
			 *	If asset id is empty then we process insertion
			*/
			else
			{
				$action = 'Add';
				
				$updated = $this->_db->insert($this->_ehrTables->empAssets, $assetData);
				
				if ($updated)
					$assetId = $this->_db->lastInsertId();
			}
			
			/**
			 *	this function will handle
			 *		update system log function
			 *		clear submit lock fucntion
			 *		return success/failure array
			*/
			return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
															'action'         => $action,
															'trackingColumn' => $assetId,
															'formName'       => 'Employee asset',
															'sessionId'      => $sessionId));
		}
		else
		{
			return array('success' => false, 'msg' => 'Asset details already exist', 'type' => 'info');
		}
	}
	
	/**
	 *	Delete Employee Asset details
	*/
	public function deleteEmployeeAsset ($assetId)
	{
		$deleted = $this->_db->delete ($this->_ehrTables->empAssets, 'Asset_Id = '. $assetId);
		
		if ($deleted)
		{
			return array('success' => true, 'msg' => 'Employee asset deleted successfully', 'type' => 'success');
		}
		else
		{
			return array('success' => false, 'msg' => 'Unable to delete employee asset', 'type' => 'warning');
		}
	}
	
	/**
	 *	List employees Education details
	*/
	public function listEmployeeEducation ($page, $rows, $sortField, $sortOrder, $searchAll, $employeeId, $isJobCandidateView)
	{
		if (!empty($employeeId))
		{
            if(empty($sortOrder))
               $sortOrder = 'ASC';
               
			/**
			 *	Define sort fields based on grid column index
			*/
			switch($sortField)
			{
				case 2: $sortField = 'Specialisation'; break;
				case 3: $sortField = 'Institute_Name'; break;
				case 4: $sortField = 'University'; break;
				case 5: $sortField = 'Year_Of_Passing'; break;
				case 6: $sortField = 'Percentage'; break;
				case 7: $sortField = 'Grade'; break;
				default: $sortField = 'Education_Type'; break;
			}
			
			/**
			 *	Query to get employee education details
			*/
			$qryEducation = $this->_db->select()
									->from($this->_ehrTables->empEducation,
										   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS Education_Id as count'), '*',
												 'DT_RowClass' => new Zend_Db_Expr('"Education"'),
												 'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', Education_Id)")))
									->joinLeft(array('c'=>$this->_ehrTables->courseDetails),'Education_Type=c.Course_Id ',
												array('c.Course_Name'))
									->where('Employee_Id = ? ', $employeeId)
									->limit($rows, $page);
			
			if($isJobCandidateView == 0)
				$qryEducation->order("$sortField $sortOrder");
			
			/**
			 *	Search All columns using single input
			*/
			if (!empty($searchAll) && $searchAll != null)
			{
				$conditions = $this->_db->quoteInto('Education_Type Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Specialisation Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Institute_Name Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or University Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Year_Of_Passing Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Percentage Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Grade Like ?', "%$searchAll%");
				
				$qryEducation->where($conditions);
			}
			
			/**
			 * SQL queries
			 * Get data to display
			*/
			$Education = $this->_db->fetchAll($qryEducation);
			
			/* Data set length after filtering */
			$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
			
			/* Total data set length */
			$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empEducation, new Zend_Db_Expr('COUNT(Education_Id)'))
										   ->where('Employee_Id = ? ', $employeeId));
			
			/**
			 * Output array
			*/
			return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $Education);
		}
		else
		{
			/**
			 * Output array
			*/
			return array("iTotalRecords" => 0, "iTotalDisplayRecords" => 0, "aaData" => array());
		}
	}
	
	/**
	 *	update, add employee award details
	*/
	public function updateEducation ($educationData, $educationId, $sessionId)
	{
		/**
		 *	Check education id is greater than zero for run update process
		*/
		if ($educationId > 0)
		{
			$action = 'Edit';
			
			$updated = $this->_db->update($this->_ehrTables->empEducation, $educationData, array('Education_Id = ?' => $educationId));
		}
		/**
		 *	If education id is empty then we process insertion
		*/
		else
		{
			$action = 'Add';
			
			$updated = $this->_db->insert($this->_ehrTables->empEducation, $educationData);
			
			if ($updated)
				$educationId = $this->_db->lastInsertId();
		}
		
		/**
		 *	this function will handle
		 *		update system log function
		 *		clear submit lock fucntion
		 *		return success/failure array
		*/
		return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
														'action'         => $action,
														'trackingColumn' => $educationId,
														'formName'       => 'Employee education',
														'sessionId'      => $sessionId));
	}
	
	/**
	 *	Delete Employee Education details
	*/
	public function deleteEmployeeEducation ($educationId)
	{
		$deleted = $this->_db->delete ($this->_ehrTables->empEducation, 'Education_Id = '. $educationId);
		
		if ($deleted)
		{
			return array('success' => true, 'msg' => 'Employee education deleted successfully', 'type' => 'success');
		}
		else
		{
			return array('success' => false, 'msg' => 'Unable to delete employee education', 'type' => 'warning');
		}
	}
	
	/**
	 *	List employees certification details
	*/
	public function listEmployeeCertification ($page, $rows, $sortField, $sortOrder, $searchAll, $employeeId)
	{
		if (!empty($employeeId))
		{
			/**
			 *	Define sort fields based on grid column index
			*/
			switch($sortField)
			{
				case 1: $sortField = 'Certification_Name'; break;
				case 2: $sortField = 'Received_Date'; break;
				case 3: $sortField = 'Certificate_Received_From'; break;
			}
			
			/**
			 *	Query to get employee awards details
			*/
			$qryCertification = $this->_db->select()
									->from($this->_ehrTables->empCertifications,
										   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS Certification_Id as count'), '*',
												 new Zend_Db_Expr("DATE_FORMAT(Received_Date,'".$this->_orgDF['sql']."') as ReceivedDate"),
												 'DT_RowClass' => new Zend_Db_Expr('"certification"'),
												 'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', Certification_Id)")))
									
									->where('Employee_Id = ? ', $employeeId)
									->order("$sortField $sortOrder")
									->limit($rows, $page);
			
			/**
			 *	Search All columns using single input
			*/
			if (!empty($searchAll) && $searchAll != null)
			{
				$conditions = $this->_db->quoteInto('Certification_Name Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Received_Date Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Certificate_Received_From Like ?', "%$searchAll%");
				
				$qryCertification->where($conditions);
			}
			
			/**
			 * SQL queries
			 * Get data to display
			*/
			$certification = $this->_db->fetchAll($qryCertification);
			
			/* Data set length after filtering */
			$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
			
			/* Total data set length */
			$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empCertifications, new Zend_Db_Expr('COUNT(Certification_Id)'))
										   ->where('Employee_Id = ? ', $employeeId));
			
			/**
			 * Output array
			*/
			return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $certification);
		}
		else
		{
			/**
			 * Output array
			*/
			return array("iTotalRecords" => 0, "iTotalDisplayRecords" => 0, "aaData" => array());
		}
	}
	
	/**
	 *	update, add employee award details
	*/
	public function updateCertification ($certificationData, $certificationId, $sessionId)
	{
		/**
		 *	Check certification id is greater than zero for run update process
		*/
		if ($certificationId > 0)
		{
			$action = 'Edit';
			
			$updated = $this->_db->update($this->_ehrTables->empCertifications, $certificationData, array('Certification_Id = ?' => $certificationId));
		}
		/**
		 *	If certification id is empty then we process insertion
		*/
		else
		{
			$action = 'Add';
			
			$updated = $this->_db->insert($this->_ehrTables->empCertifications, $certificationData);
			
			if ($updated)
				$certificationId = $this->_db->lastInsertId();
		}
		
		/**
		 *	this function will handle
		 *		update system log function
		 *		clear submit lock fucntion
		 *		return success/failure array
		*/
		return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
														'action'         => $action,
														'trackingColumn' => $certificationId,
														'formName'       => 'Employee certification',
														'sessionId'      => $sessionId));
	}
	
	/**
	 *	Delete Employee Certification details
	*/
	public function deleteEmployeeCertification ($certificationId)
	{
		$deleted = $this->_db->delete ($this->_ehrTables->empCertifications, 'Certification_Id = '. $certificationId);
		
		if ($deleted)
		{
			return array('success' => true, 'msg' => 'Employee certification deleted successfully', 'type' => 'success');
		}
		else
		{
			return array('success' => false, 'msg' => 'Unable to delete employee certification', 'type' => 'warning');
		}
	}
	
	/**
	 *	List employees training details
	*/
	public function listEmployeeTraining ($page, $rows, $sortField, $sortOrder, $searchAll, $employeeId)
	{
		if (!empty($employeeId))
		{
			/**
			 *	Define sort fields based on grid column index
			*/
			switch($sortField)
			{
				case 1: $sortField = 'Training_Name'; break;
				case 2: $sortField = 'Training_Start_Date'; break;
				case 3: $sortField = 'Training_End_Date'; break;
				case 4: $sortField = 'Training_Duration'; break;
				case 5: $sortField = 'Trainer'; break;
				case 6: $sortField = 'Center'; break;
			}
			
			/**
			 *	Query to get employee awards details
			*/
			$qryTraining = $this->_db->select()
									->from($this->_ehrTables->empTraining,
										   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS Training_Id as count'), '*',
												 new Zend_Db_Expr("DATE_FORMAT(Training_Start_Date,'".$this->_orgDF['sql']."') as TrainingStartDate"),
												 new Zend_Db_Expr("DATE_FORMAT(Training_End_Date,'".$this->_orgDF['sql']."') as TrainingEndDate"),
												 'DT_RowClass' => new Zend_Db_Expr('"training"'),
												 'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', Training_Id)")))
									
									->where('Employee_Id = ? ', $employeeId)
									->order("$sortField $sortOrder")
									->limit($rows, $page);
			
			/**
			 *	Search All columns using single input
			*/
			if (!empty($searchAll) && $searchAll != null)
			{
				$conditions = $this->_db->quoteInto('Training_Name Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Training_Start_Date Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Training_End_Date Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Training_Duration Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Trainer Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Center Like ?', "%$searchAll%");
				
				$qryTraining->where($conditions);
			}
			
			/**
			 * SQL queries
			 * Get data to display
			*/
			$training = $this->_db->fetchAll($qryTraining);
			
			/* Data set length after filtering */
			$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
			
			/* Total data set length */
			$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empTraining, new Zend_Db_Expr('COUNT(Training_Id)'))
										   ->where('Employee_Id = ? ', $employeeId));
			
			/**
			 * Output array
			*/
			return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $training);
		}
		else
		{
			/**
			 * Output array
			*/
			return array("iTotalRecords" => 0, "iTotalDisplayRecords" => 0, "aaData" => array());
		}
	}
	
	/**
	 *	update, add employee training details
	*/
	public function updateTraining ($trainingData, $trainingId, $sessionId)
	{
		/**
		 *	Check training id is greater than zero for run update process
		*/
		if ($trainingId > 0)
		{
			$action = 'Edit';
			
			$updated = $this->_db->update($this->_ehrTables->empTraining, $trainingData, array('Training_Id = ?' => $trainingId));
		}
		/**
		 *	If training id is empty then we process insertion
		*/
		else
		{
			$action = 'Add';
			
			$updated = $this->_db->insert($this->_ehrTables->empTraining, $trainingData);
			
			if ($updated)
				$trainingId = $this->_db->lastInsertId();
		}
		
		/**
		 *	this function will handle
		 *		update system log function
		 *		clear submit lock fucntion
		 *		return success/failure array
		*/
		return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
														'action'         => $action,
														'trackingColumn' => $trainingId,
														'formName'       => 'Employee training',
														'sessionId'      => $sessionId));
	}
	
	/**
	 *	Delete Employee training details
	*/
	public function deleteEmployeeTraining ($trainingId)
	{
		$deleted = $this->_db->delete ($this->_ehrTables->empTraining, 'Training_Id = '. $trainingId);
		
		if ($deleted)
		{
			return array('success' => true, 'msg' => 'Employee training deleted successfully', 'type' => 'success');
		}
		else
		{
			return array('success' => false, 'msg' => 'Unable to delete employee training', 'type' => 'warning');
		}
	}
	
	/**
	 *	List employees award details
	*/
	public function listEmployeeAwards ($page, $rows, $sortField, $sortOrder, $searchAll, $employeeId)
	{
		if (!empty($employeeId))
		{
			/**
			 *	Define sort fields based on grid column index
			*/
			switch($sortField)
			{
				case 1: $sortField = 'Award_Name'; break;
				case 2: $sortField = 'Received_On'; break;
				case 3: $sortField = 'Received_From'; break;
				case 4: $sortField = 'Received_For'; break;
			}
			
			/**
			 *	Query to get employee awards details
			*/
			$qryAwards = $this->_db->select()
									->from($this->_ehrTables->empAward,
										   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS Award_Id as count'), '*',
												 new Zend_Db_Expr("DATE_FORMAT(Received_On,'".$this->_orgDF['sql']."') as ReceivedOn"),
												 'DT_RowClass' => new Zend_Db_Expr('"award"'),
												 'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', Award_Id)")))
									
									->where('Employee_Id = ? ', $employeeId)
									->order("$sortField $sortOrder")
									->limit($rows, $page);
			
			/**
			 *	Search All columns using single input
			*/
			if (!empty($searchAll) && $searchAll != null)
			{
				$conditions = $this->_db->quoteInto('Award_Name Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Received_On Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Received_From Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or Received_For Like ?', "%$searchAll%");
				
				$qryAwards->where($conditions);
			}
			
			/**
			 * SQL queries
			 * Get data to display
			*/
			$awards = $this->_db->fetchAll($qryAwards);
			
			/* Data set length after filtering */
			$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
			
			/* Total data set length */
			$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empAward, new Zend_Db_Expr('COUNT(Award_Id)'))
										   ->where('Employee_Id = ? ', $employeeId));
			
			/**
			 * Output array
			*/
			return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $awards);
		}
		else
		{
			/**
			 * Output array
			*/
			return array("iTotalRecords" => 0, "iTotalDisplayRecords" => 0, "aaData" => array());
		}
	}
	
	/**
	 *	update, add employee award details
	*/
	public function updateAward ($awardData, $awardId, $sessionId)
	{
		/**
		 *	Check award id is greater than zero for run update process
		*/
		if ($awardId > 0)
		{
			$action = 'Edit';
			
			$updated = $this->_db->update($this->_ehrTables->empAward, $awardData, array('Award_Id = ?' => $awardId));
		}
		/**
		 *	If award id is empty then we process insertion
		*/
		else
		{
			$action = 'Add';
			
			$updated = $this->_db->insert($this->_ehrTables->empAward, $awardData);
			
			if ($updated)
				$awardId = $this->_db->lastInsertId();
		}
		
		/**
		 *	this function will handle
		 *		update system log function
		 *		clear submit lock fucntion
		 *		return success/failure array
		*/
		return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
														'action'         => $action,
														'trackingColumn' => $awardId,
														'formName'       => 'Employee award',
														'sessionId'      => $sessionId));
	}
	
	/**
	 *	Delete Employee Asset details
	*/
	public function deleteEmployeeAward ($awardId)
	{
		$deleted = $this->_db->delete ($this->_ehrTables->empAward, 'Award_Id = '. $awardId);
		
		if ($deleted)
		{
			return array('success' => true, 'msg' => 'Employee award deleted successfully', 'type' => 'success');
		}
		else
		{
			return array('success' => false, 'msg' => 'Unable to delete employee award', 'type' => 'warning');
		}
	}
	
	/**
	 *	List employees insurance details
	*/
	public function listEmployeeInsurances ($page, $rows, $sortField, $sortOrder, $searchAll, $employeeId)
	{
		if (!empty($employeeId))
		{
			/**
			 *	Define sort fields based on grid column index
			*/
			switch($sortField)
			{
				case 0: $sortField = 'Insurance_Type_Name'; break;
				case 1: $sortField = 'ip.Policy_No'; break;
			}
			
			/**
			 *	Query to get employee insurance details
			*/
			$qryInsurancePolicy = $this->_db->select()
									->from(array('ip' => $this->_ehrTables->empInsurancePolicyNo),
										   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS ip.Policy_Id as count'), '*',
												 'DT_RowClass' => new Zend_Db_Expr('"insurance-policy"'),
												 'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', ip.Policy_Id)"),
												 'Insurance_Type_Name'=>new Zend_Db_Expr('CASE WHEN it.Insurance_Name IS NULL THEN fhi.Title else it.Insurance_Name END')))
									->joinLeft(array('it' => $this->_ehrTables->insuranceType), 
												'it.InsuranceType_Id=ip.InsuranceType_Id AND ip.Insurance_Type = "insurance"',
												array('it.Insurance_Name '))
									->joinLeft(array('fhi' => $this->_ehrTables->fixedHealthInsType), 
												'fhi.Insurance_Type_Id=ip.InsuranceType_Id AND ip.Insurance_Type = "fixedHealthInsurance"',
												array('fhi.Title'))

									->where('ip.Employee_Id = ? ', $employeeId)
									->order("$sortField $sortOrder")
									->limit($rows, $page);
			
			/**
			 *	Search All columns using single input
			*/
			if (!empty($searchAll) && $searchAll != null)
			{
				$conditions = $this->_db->quoteInto('fhi.Title Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or it.Insurance_Name Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or ip.Policy_No Like ?', "%$searchAll%");
				
				$qryInsurancePolicy->where($conditions);
			}
			
			/**
			 * SQL queries
			 * Get data to display
			*/
			$insurancePolicy = $this->_db->fetchAll($qryInsurancePolicy);
			
			/* Data set length after filtering */
			$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
			
			/* Total data set length */
			$iTotal = $this->_db->fetchOne($this->_db->select()
										   ->from($this->_ehrTables->empInsurancePolicyNo, new Zend_Db_Expr('COUNT(Policy_Id)'))
										   ->where('Employee_Id = ? ', $employeeId));
			
			/**
			 * Output array
			*/
			return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $insurancePolicy);
		}
		else
		{
			/**
			 * Output array
			*/
			return array("iTotalRecords" => 0, "iTotalDisplayRecords" => 0, "aaData" => array());
		}
	}
	
	/**
	 *	update, add employee insurance policy details
	*/
	public function updateInsurancePolicy ($policyData, $policyId, $sessionId)
	{
		/**
		 *	Check insurance type name already exist for this employee
		*/
		$qryPolicy = $this->_db->select()
									->from($this->_ehrTables->empInsurancePolicyNo, array(new Zend_Db_Expr('COUNT(Policy_Id)')))
									->where('Employee_Id = ?', $policyData['Employee_Id'])
									->where('Insurance_Type = ?', $policyData['Insurance_Type'])
									->where('InsuranceType_Id = ?', $policyData['InsuranceType_Id']);
        
		if (!empty($policyId))
        {
            $qryPolicy->where('Policy_Id != ?', $policyId);
        }
        
		$checkExists = $this->_db->fetchOne($qryPolicy);
		
		if ($checkExists == 0)
		{
			/**
			 *	Check insurance policy id is greater than zero for run update process
			*/
			if ($policyId > 0)
			{
				$action = 'Edit';
				
				$updated = $this->_db->update($this->_ehrTables->empInsurancePolicyNo, $policyData, array('Policy_Id = ?' => $policyId));
			}
			/**
			 *	If insurance policy id is empty then we process insertion
			*/
			else
			{
				$action = 'Add';
				
				$updated = $this->_db->insert($this->_ehrTables->empInsurancePolicyNo, $policyData);
				
				if ($updated)
					$policyId = $this->_db->lastInsertId();
			}

			if($updated){
				$esiInsuranceTypeId = array();
				if($policyData['Insurance_Type'] == 'insurance') {
					/** check whether ESI insuarance details are updated */
					$esiInsuranceTypeId = $this->_db->fetchOne($this->_db->select()
						->from($this->_ehrTables->insuranceType, array('InsuranceType_Id'))
						->where('InsuranceType_Id = ?', $policyData['InsuranceType_Id'])
						->where('Employee_State_Insurance = ?', 1));
				}
				
				if(!empty($esiInsuranceTypeId)){
					/** Add employee info log */
					$logData = array('Employee_Id'=>$policyData['Employee_Id'], 'Action'=>'Update', 'Log_Timestamp'=>date('Y-m-d H:i:s'));
					$this->_db->insert($this->_ehrTables->employeeInfoTimestampLog, $logData);
				}
			}
			
			/**
			 *	this function will handle
			 *		update system log function
			 *		clear submit lock fucntion
			 *		return success/failure array
			*/
			return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
															'action'         => $action,
															'trackingColumn' => $policyId,
															'formName'       => 'Employee insurance policy',
															'sessionId'      => $sessionId));
		}
		else
		{
			return array('success' => false, 'msg' => 'Insurance policy details already exist', 'type' => 'info');
		}
	}
	
	/**
	 *	Delete Employee Asset details
	*/
	public function deleteEmployeeInsurance ($policyId)
	{
		/** check whether the ESI insurance is deleted or not */
		$esiEmployeeId = $this->_db->fetchOne($this->_db->select()
						->from(array('EI'=>$this->_ehrTables->empInsurancePolicyNo), array('EI.Employee_Id'))
						->joinInner(array('IT'=>$this->_ehrTables->insuranceType),
									'IT.InsuranceType_Id = EI.InsuranceType_Id AND 
									IT.Employee_State_Insurance = "1"', array(''))
						->where('EI.Insurance_Type = ?', 'insurance')
						->where('EI.Policy_Id = ?', $policyId));
									
		
		$deleted = $this->_db->delete ($this->_ehrTables->empInsurancePolicyNo, 'Policy_Id = '. $policyId);
		
		if ($deleted)
		{
			if(!empty($esiEmployeeId)){
				/** Add employee info log */
				$logData = array('Employee_Id'=>$esiEmployeeId, 'Action'=>'Update', 'Log_Timestamp'=>date('Y-m-d H:i:s'));
				$this->_db->insert($this->_ehrTables->employeeInfoTimestampLog, $logData);
			}
			return array('success' => true, 'msg' => 'Employee insurance policy deleted successfully', 'type' => 'success');
		}
		else
		{
			return array('success' => false, 'msg' => 'Unable to delete employee insurance policy', 'type' => 'warning');
		}
	}
	
	/**
	 *	Delete Employee insurance by desingaiton details
	*/
	public function deleteEmployeeInsuranceByDesingaiton ($employeeId, $insuranceTypeData)
	{
		/**
		 *	If marital Status exist then we have to delete other than marital status with dependents
		*/
		$conditions = 'Employee_Id = '.$employeeId;
		
		if (!empty($insuranceTypeData))
		{
			$conditions .= ' AND InsuranceType_Id NOT IN ('. $insuranceTypeData .')';
		}
		
		$deleted = $this->_db->delete ($this->_ehrTables->empInsurancePolicyNo, $conditions);
		
		if ($deleted)
		{
			return array('success' => true, 'msg' => 'Employee insurance(s) deleted successfully', 'type' => 'success');
		}
		else
		{
			return array('success' => false, 'msg' => 'Unable to delete employee insurance(s)', 'type' => 'warning');
		}
	}
	
	
	/**
	 *	Check employee details and delete records
	*/
	public function checkEmployeeDetails ($table, $employeeId)
	{
		$deleted = 1;
		
		$checkExist = $this->_db->fetchOne ($this->_db->select()->from($table, new Zend_Db_Expr('Count(Employee_Id)'))
											->where('Employee_Id = ?', $employeeId));
		
		if ($checkExist)
		{
			$deleted = $this->_db->delete ($table, 'Employee_Id ='.(int)$employeeId);
		}
		
		return $deleted;
	}
	
	/**
	 * Get Form status by employeeId
	 */
    public function getFormStatus($empId)
    {
        return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empPersonal, 'Form_Status')
									->where('Employee_Id = ?', $empId));
    }
	
	/**
     *
     * This function is to find employee personal details af an employee.
     */
    public function getEmpDetails($employeeId)
    {
        return $this->_db->fetchRow($this->_db->select()
									->from(array('p'=>$this->_ehrTables->empPersonal), array('*'))
									
									->joinLeft(array('ms'=>$this->_ehrTables->maritalStatus), 'p.Marital_Status=ms.Marital_Status_Id',
											   array('ms.Marital_Status_Id', 'ms.Marital_Status'))
									
									->where('p.Employee_Id = ?', $employeeId));
	}
    
	/**
     * This function is to find employee module access rights for an employee.
     */
    public function getEmpAccessRights($employeeId)
    {
        $empAccRightsQry = $this->_db->select()
        ->from(array('A' => $this->_ehrTables->empAccessRights), new Zend_Db_Expr('COUNT(Employee_Id)'))
        ->where('A.Employee_Id = ?', $employeeId)
        ->group('A.Form_Id');
        $empAccRights = $this->_db->fetchOne($empAccRightsQry);
        return $empAccRights;

    }
    
	/**
     *This function is to fetch employee designation details which are in roles table
     *
     */
    public function getEmpDesignation()
    {
        $desAccRightsQry = $this->_db->select()
        ->from(array('D' => $this->_ehrTables->designation))
        ->joinInner(array('R'=>$this->_ehrTables->roles), 'R.Designation_Id=D.Designation_Id', array('View'=>'Role_View', 'Add'=>'Role_Add', 'Update'=>'Role_Update',
    			'Optional_Choice'=>'Role_Optional_Choice', 'Delete'=>'Role_Delete', 'R.Form_Id', 'R.Designation_Id'))
        ->joinInner(array('F'=>$this->_ehrTables->forms), 'F.Form_Id=R.Form_Id')
        ->group('D.Designation_Id');
        $desAccRights = $this->_db->fetchAll($desAccRightsQry);
        return $desAccRights;
    }
    
	
    
	/**
     * to list all forms roles for an employee without subform
     */
    public function formEmpRoles($employeeId)
    {
        if ($employeeId != "")
        {
            $roleFormsQry = $this->_db->select()
            ->from(array('empAccRights'=>$this->_ehrTables->empAccessRights),array('View'=>'Role_View', 'Add'=>'Role_Add', 'Update'=>'Role_Update',
    			'Optional_Choice'=>'Role_Optional_Choice', 'Delete'=>'Role_Delete', 'empAccRights.Form_Id', 'empAccRights.Employee_Id',
            	'Hr_Group'=>'Role_Hr_Group','Payroll_Group'=>'Role_Payroll_Group'))
            ->joinInner(array('f'=>$this->_ehrTables->forms),'f.Form_Id=empAccRights.Form_Id',array('f.Sub_Form'))
            ->where('empAccRights.Employee_Id = ?', $employeeId)
            ->where('f.Sub_Form = ?', 0)
            ->order("f.Form_Id Asc");
             
            $roles = $this->_db->fetchAll($roleFormsQry);
            return $roles;
        }
    }
    
	/**
     * to list all sub forms roles for an employee
     */
    public function subFormEmpRoles($employeeId)
    {
        if ($employeeId != "")
        {
            $roleSubFormsQry = $this->_db->select()
            ->from(array('empAccRights'=>$this->_ehrTables->empAccessRights),array('View'=>'Role_View', 'Add'=>'Role_Add', 'Update'=>'Role_Update',
    			'Optional_Choice'=>'Role_Optional_Choice', 'Delete'=>'Role_Delete','Hr_Group'=>'Role_Hr_Group', 'Payroll_Group'=>'Role_Payroll_Group',
                'empAccRights.Form_Id', 'empAccRights.Employee_Id'))
            ->joinInner(array('f'=>$this->_ehrTables->forms),'f.Form_Id=empAccRights.Form_Id',array('f.Sub_Form'))
            ->where('empAccRights.Employee_Id = ?', $employeeId)
            ->where('f.Sub_Form > ?', 0)
            ->order("f.Form_Id Asc");
             
            $roles = $this->_db->fetchAll($roleSubFormsQry);
            return $roles;
        }
    }
    
	/**
     * to list all forms roles for an employee
     */
    public function employeeRoles($employeeId)
    {
        if ($employeeId != "")
        {
            $roleFormsQry = $this->_db->select()
            ->from(array('empAccRights'=>$this->_ehrTables->empAccessRights), array('View'=>'Role_View', 'Add'=>'Role_Add', 'Update'=>'Role_Update',
    			'Optional_Choice'=>'Role_Optional_Choice', 'Delete'=>'Role_Delete', 'empAccRights.Form_Id', 'empAccRights.Employee_Id',
            	'Hr_Group'=>'Role_Hr_Group','Payroll_Group'=>'Role_Payroll_Group'))
            ->joinInner(array('f'=>$this->_ehrTables->forms),'f.Form_Id=empAccRights.Form_Id',array('f.Sub_Form'))
            ->where('empAccRights.Employee_Id = ?', $employeeId)
            ->order("f.Form_Id Asc");
            $roles = $this->_db->fetchAll($roleFormsQry);
            return $roles;
        }
    }
    
	/**  Get super admin, admin, employee admin, payroll admin, employee and designation form Id **/
    public function getAdminAccessFormIds()
	{
		/** Get super admin form Id **/
		$getSuperAdminFormId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->forms,'Form_Id')
									->where('Form_Name = ?', 'Super Admin'));
		
		/** Get admin form Id **/
		$getAdminFormId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->forms,'Form_Id')
									->where('Form_Name = ?', 'Admin'));
		
		/** Get employee admin form Id **/
		$getEmployeeAdminFormId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->forms,'Form_Id')
									->where('Form_Name = ?', 'Employee Admin'));
		
		/** Get payroll admin Id **/
		$getPayrollAdminFormId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->forms,'Form_Id')
									->where('Form_Name = ?', 'Payroll Admin'));
		
		/** Get employee form Id **/
		$getEmployeeFormId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->forms,'Form_Id')
									->where('Form_Name = ?', 'Employees'));
		
		/** Get designation form Id **/
		$getDesignationFormId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->forms,'Form_Id')
									->where('Form_Name = ?', 'Designations'));
		
		return (array($getSuperAdminFormId,$getAdminFormId,$getEmployeeAdminFormId,
					  $getPayrollAdminFormId,$getEmployeeFormId,$getDesignationFormId));
	}
	
	/** get admin roles module access and validate it **/
	public function getSelAdminRolesAccess($rolesData)
	{
		/** get super admin, admin, employee admin, payroll admin, employees and designation form id **/
		$adminAccessFormIds = $this->getAdminAccessFormIds();
		
		$isSuperAdmin = $isEmpViewRolesExists = $isDesViewRolesExists =0;
		$isPayrollAdmin = $isEmployeeAdmin = $isAdmin = 0;
		$adminAccessUpdate = $empAdminAccessUpdate = $payrollAdminAccessUpdate = 0;
		
		if(count($rolesData) > 0)
		{
			/** get super admin, employee, designation access for the employee **/
			for($er=0;$er<count($rolesData);$er++)
			{
				/** Get only the super admin roles data from the roles array, by comparing the super admin
				form Id in each array **/
				if (in_array($adminAccessFormIds[0], $rolesData[$er]))
				{
					/** Check whether that the employee is super admin or not **/
					if($rolesData[$er]['Role_Optional_Choice'] == 1)
					{
						$isSuperAdmin = 1;
					}
				}
				
				/** Get only the admin roles data from the roles array, by comparing the admin
				form Id in each array **/
				if (in_array($adminAccessFormIds[1], $rolesData[$er]))
				{
					/** Check whether that the employee is admin or not **/
					if($rolesData[$er]['Role_Update'] == 1)
					{
						$isAdmin = 1;
					}
				}
				
				/** Get only the employee admin roles data from the roles array, by comparing the employee admin
				form Id in each array **/
				if (in_array($adminAccessFormIds[2], $rolesData[$er]))
				{
					/** Check whether that the employee is employee admin or not **/
					if($rolesData[$er]['Role_Update'] == 1)
					{
						$isEmployeeAdmin = 1;
					}
				}
				
				/** Get only the payroll admin roles data from the roles array, by comparing the payroll admin
				form Id in each array **/
				if (in_array($adminAccessFormIds[3], $rolesData[$er]))
				{
					/** Check whether that the employee is payroll admin or not **/
					if($rolesData[$er]['Role_Update'] == 1)
					{
						$isPayrollAdmin = 1;
					}
				}
				
				/** Get only the employee form roles data from the roles array, by comparing the employee form Id in each array **/
				if (in_array($adminAccessFormIds[4], $rolesData[$er]))
				{
					/** Check whether that the employee has all rights for employee form **/
					if($rolesData[$er]['Role_View'] == 1 && $rolesData[$er]['Role_Add'] == 1
					   && $rolesData[$er]['Role_Update'] == 1 && $rolesData[$er]['Role_Delete'] == 1)
					{
						$isEmpViewRolesExists = 1;
					}
				}
				
				/** Get only the designation form roles data from the roles array, by comparing the designation form Id in each array **/
				if (in_array($adminAccessFormIds[5], $rolesData[$er]))
				{
					/** Check whether that the employee has all rights for designation form**/
					if($rolesData[$er]['Role_View'] == 1 && $rolesData[$er]['Role_Add'] == 1
					   && $rolesData[$er]['Role_Update'] == 1 && $rolesData[$er]['Role_Delete'] == 1)
					{
						$isDesViewRolesExists = 1;
					}
				}
			}
			
			/** If employee has admin access, then he should not have employee admin and payroll admin roles **/
			if($isAdmin == 1)
			{
				if(empty($isPayrollAdmin) && empty($isEmployeeAdmin))
				{
					$adminAccessUpdate = 1; // allow to update
				}
			}
			else
			{
				$adminAccessUpdate = 1; // allow to update
			}
			
			/** If employee has employee admin access,then he should not have admin and payroll admin roles **/
			if($isEmployeeAdmin == 1)
			{
				if(empty($isPayrollAdmin) && empty($isAdmin))
				{
					$empAdminAccessUpdate = 1; // allow to update
				}
			}
			else
			{
				$empAdminAccessUpdate = 1; // allow to update
			}
			
			/** If employee has payroll admin access, then he should not have admin and employee admin roles **/
			if($isPayrollAdmin == 1)
			{
				if(empty($isAdmin) && empty($isEmployeeAdmin))
				{
					$payrollAdminAccessUpdate = 1; // allow to update
				}
			}
			else
			{
				$payrollAdminAccessUpdate = 1; // allow to update
			}
			
			$superAdminAccessArr = array('isSuperAdmin'=>$isSuperAdmin,
										 'isEmpViewRolesExists'=>$isEmpViewRolesExists,
										 'isDesViewRolesExists'=>$isDesViewRolesExists,
										 'adminAccessUpdate' => $adminAccessUpdate,
										 'empAdminAccessUpdate' => $empAdminAccessUpdate,
										 'payrollAdminAccessUpdate' => $payrollAdminAccessUpdate);
			
			return $superAdminAccessArr;
		}
		else
		{
			return array();
		}
	}
	
    public function updateEmpAccessRights($employeeRoleData,$operation,$sessionId)
    {
		$isSuperAdmin = $isEmpViewRolesExists = $isDesViewRolesExists =0;
		$isPayrollAdmin = $isEmployeeAdmin = $isAdmin = 0;
		$isEmpDesFormRoleExists = $allowUpdate = $invalidData = $selfSuperAdminUpdate = 0;
		
		$adminAccessUpdate = $empAdminAccessUpdate = $payrollAdminAccessUpdate = 0;
		
		/** get super admin, admin, employee admin, payroll admin, employees and designation form id **/
		$selAdminRolesAccess = $this->getSelAdminRolesAccess($employeeRoleData);
		
		if(!empty($selAdminRolesAccess))
		{
			$isSuperAdmin             = $selAdminRolesAccess['isSuperAdmin'];
			$isEmpViewRolesExists     = $selAdminRolesAccess['isEmpViewRolesExists'];
			$isDesViewRolesExists     = $selAdminRolesAccess['isDesViewRolesExists'];
			$adminAccessUpdate        = $selAdminRolesAccess['adminAccessUpdate'];
			$empAdminAccessUpdate     = $selAdminRolesAccess['empAdminAccessUpdate'];
			$payrollAdminAccessUpdate = $selAdminRolesAccess['payrollAdminAccessUpdate'];
			
			/** If the employee has super admin access **/
			if($isSuperAdmin == 1)
			{
				/** If the super admin has employee and designation form view access **/
				if($isEmpViewRolesExists == 1 && $isDesViewRolesExists == 1)
				{
					$isEmpDesFormRoleExists = $allowUpdate = 1;
				}
				else
				{
					$isEmpDesFormRoleExists = $allowUpdate = 0;
				}
			}
			/** If the employee is not super admin **/
			else
			{
				/** Get the total super admin count **/
				$superAdminCntQry = $this->_db->select()->from(array('EAR'=>$this->_ehrTables->empAccessRights),new Zend_Db_Expr('COUNT(EAR.Employee_Id)'))
											->joinInner(array('F'=>$this->_ehrTables->forms), 'F.Form_Id=EAR.Form_Id',array())
											->where('F.Form_Name = ?', 'Super Admin')
											->where('EAR.Employee_Id != ?', $employeeRoleData[0]['Employee_Id'])
											->where('EAR.Role_Optional_Choice = ?','1');
				$superAdminCnt = $this->_db->fetchOne($superAdminCntQry);
				
				/** Get the super admin role for the employee **/
				$isEmpSuperAdminQry = $this->_db->select()->from(array('EA'=>$this->_ehrTables->empAccessRights),'EA.Role_Optional_Choice')
										->joinInner(array('FO'=>$this->_ehrTables->forms), 'FO.Form_Id=EA.Form_Id',array())
										->where('FO.Form_Name = ?', 'Super Admin')
										->where('EA.Employee_Id = ?', $employeeRoleData[0]['Employee_Id'])
										->where('EA.Role_Optional_Choice = ?','1');
				$isEmpSuperAdmin = $this->_db->fetchOne($isEmpSuperAdminQry);
				
				/** If employee has super admin access **/
				if($isEmpSuperAdmin)
				{
					/** Not allow to remove self super admin access **/
					if($sessionId == $employeeRoleData[0]['Employee_Id'])
					{
						$selfSuperAdminUpdate = 1;
					}
					else if($superAdminCnt >= 1)
					{
						$allowUpdate = $isEmpDesFormRoleExists = 1;
					}
				}
				else
				{
					/** check atleast one super admin exists **/
					if($superAdminCnt >= 1)
					{
						$allowUpdate = $isEmpDesFormRoleExists = 1;
					}
				}
			}
		}
		else
		{
			$invalidData = 1;
		}		
		
		/** 1. if super admin has employee/designation view access
			2. If employee does not have any of the admin roles
		Then roles will be updated **/
		if(!empty($allowUpdate) && !empty($isEmpDesFormRoleExists) && !empty($adminAccessUpdate) && empty($invalidData))
		{
			for($i=0;$i<count($employeeRoleData);$i++)
			{
				if($operation=='Add')
				{
					$employeeRoleData[$i]['Added_On']=date('Y-m-d H:i:s');
					$employeeRoleData[$i]['Added_By']=$sessionId;
				}
				else
				{
					$employeeRoleData[$i]['Updated_On']=date('Y-m-d H:i:s');
					$employeeRoleData[$i]['Updated_By']=$sessionId;
					$where = array('Employee_Id= ?' => $employeeRoleData[$i]['Employee_Id'], 'Form_Id = ?' => $employeeRoleData[$i]['Form_Id']);
					$updated = $this->_db->update($this->_ehrTables->empAccessRights,$employeeRoleData[$i], $where);
					
					$empAccessRightsQry = $this->_db->select()
										->from(array('empAccessRights'=>$this->_ehrTables->empAccessRights),new Zend_Db_Expr('COUNT(Employee_Id)'))
										->where('empAccessRights.Employee_Id = ?', $employeeRoleData[$i]['Employee_Id'])
										->where('empAccessRights.Form_Id = ?', $employeeRoleData[$i]['Form_Id']);
					
					$empAccessRights = $this->_db->fetchOne($empAccessRightsQry);
					if($empAccessRights > 0)
					{
						$updated = 1;
					}
					else
					{
						$updated = $this->_db->insert($this->_ehrTables->empAccessRights,$employeeRoleData[$i]);
					}
				}
			}
			
			if($operation=='Add')
			{        
				$updated = $this->_ehrTables->insertMultiple($this->_ehrTables->empAccessRights,$employeeRoleData);
			}
			
			return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
										'action'         => $operation,
										'trackingColumn' => $sessionId,
										'formName'       => 'Employee Roles',
										'sessionId'      => $sessionId,
										'tableName'      => $this->_ehrTables->empAccessRights));
		}
		else
		{
			if($invalidData == 1)
			{
				return array('success' => false, 'msg'=>'Invalid Data', 'type'=>'warning');
			}
			else if(empty($adminAccessUpdate))
			{
				return array('success' => false, 'msg'=>'Admin should not have employee admin and payroll admin roles', 'type'=>'warning');
			}
			else if($isSuperAdmin == 1)
			{
				return array('success' => false,
						 'msg'=>'You cannot remove employee and designation access to super admin',
						 'type'=>'warning');
			}
			else if(empty($superAdminCnt))
			{
				return array('success' => false,
						 'msg'=>'You cannot remove super admin access to this employee. You should have atleast one super admin in the system',
						 'type'=>'warning');
			}
			else
			{
				return array('success' => false,
						 'msg'=>'You cannot remove your super admin access',
						 'type'=>'warning');
			}
		}
    }
	
    public function updateDesignationAccessRights($designationRoleData,$operation,$sessionId)
    {
		$isSuperAdmin = $isEmpViewRolesExists = $isDesViewRolesExists =0;		
		$isEmpDesFormRoleExists = $allowUpdate = $invalidData = 0;
		$adminAccessUpdate = $empAdminAccessUpdate = $payrollAdminAccessUpdate = 0;
		$selfSuperAdminUpdate = 0;
		
		/** get super admin, admin, employee admin, payroll admin, employees and designation form access **/
		$selAdminRolesAccess = $this->getSelAdminRolesAccess($designationRoleData);
		
		if(!empty($selAdminRolesAccess))
		{
			$isSuperAdmin             = $selAdminRolesAccess['isSuperAdmin'];
			$isEmpViewRolesExists     = $selAdminRolesAccess['isEmpViewRolesExists'];
			$isDesViewRolesExists     = $selAdminRolesAccess['isDesViewRolesExists'];
			$adminAccessUpdate        = $selAdminRolesAccess['adminAccessUpdate'];
			$empAdminAccessUpdate     = $selAdminRolesAccess['empAdminAccessUpdate'];
			$payrollAdminAccessUpdate = $selAdminRolesAccess['payrollAdminAccessUpdate'];
			
			/** Get the total super admin count **/
			$superAdminCntQry = $this->_db->select()->from(array('ER'=>$this->_ehrTables->roles),new Zend_Db_Expr('COUNT(ER.Designation_Id)'))
										->joinInner(array('F'=>$this->_ehrTables->forms), 'F.Form_Id=ER.Form_Id',array())
										->where('F.Form_Name = ?', 'Super Admin')
										->where('ER.Designation_Id != ?', $designationRoleData[0]['Designation_Id'])
										->where('ER.Role_Optional_Choice = ?','1');
			$superAdminCnt = $this->_db->fetchOne($superAdminCntQry);
			
			/** If the designation has super admin access **/
			if($isSuperAdmin == 1)
			{	
				/** If the super admin has employee and designation form view access **/
				if($isEmpViewRolesExists == 1 && $isDesViewRolesExists == 1)
				{
					$isEmpDesFormRoleExists = $allowUpdate = 1;
				}
			}
			/** If the designation is not having super admin access **/
			else
			{
				/** Get the super admin role for this designation **/
				$isDesSuperAdminQry = $this->_db->select()->from(array('ROLES'=>$this->_ehrTables->roles),'ROLES.Role_Optional_Choice')
										->joinInner(array('FO'=>$this->_ehrTables->forms), 'FO.Form_Id=ROLES.Form_Id',array())
										->where('FO.Form_Name = ?', 'Super Admin')
										->where('ROLES.Designation_Id = ?', $designationRoleData[0]['Designation_Id'])
										->where('ROLES.Role_Optional_Choice = ?','1');
				$isDesSuperAdmin = $this->_db->fetchOne($isDesSuperAdminQry);				
				
				/** If selected designation has super admin access **/
				if($isDesSuperAdmin)
				{
					/**  get the employees for the super admin designation **/
					$getSuperAdminDesignationsEmployees = $this->_db->fetchCol($this->_db->select()->from(array('EJ'=>$this->_ehrTables->empJob),'EJ.Employee_Id')
															->joinInner(array('D'=>$this->_ehrTables->designation), 'D.Designation_Id=EJ.Designation_Id',array())
															->where('D.Designation_Id = ?',$designationRoleData[0]['Designation_Id']));
					
					/** If employees exists for the super admin designation **/
					if(count($getSuperAdminDesignationsEmployees) > 0)
					{
						/** Not allow to remove super admin access if super admin access is removed by employee for his designation **/
						if(in_array($sessionId,$getSuperAdminDesignationsEmployees))
						{
							$selfSuperAdminUpdate = 1;
						}
						else
						{
							/** Allow to update roles, if super admin role exists other than selected designation  **/
							if($superAdminCnt >= 1)
							{
								/** allow to remove super admin access **/
								$allowUpdate = $isEmpDesFormRoleExists = 1;
							}
						}
					}
					else
					{
						/** Allow to update roles, if super admin role exists other than selected one designation  **/
						if($superAdminCnt >= 1)
						{
							/** allow to remove super admin access **/
							$allowUpdate = $isEmpDesFormRoleExists = 1;
						}
					}
				}
				else
				{					
					/** Allow to update roles, if super admin role exists other than selected one designation  **/
					if($superAdminCnt >= 1)
					{
						$allowUpdate = $isEmpDesFormRoleExists = 1;
					}
				}
			}
		}
		else
		{
			$invalidData = 1;
		}
		
		/** 1. if super admin has employee/designation view access
			2. If employee has admin roles, he should not have employee admin and payroll admin roles
			3. If employee has employee admin roles, he should not have admin and payroll admin roles
			4. If employee has payroll admin roles, he should not have admin and employee admin roles
			5. If employee does not have any of the admin roles
			6. If super admin access is not removed by employee for his designation
		Then roles will be updated **/
		if(!empty($allowUpdate) && !empty($isEmpDesFormRoleExists) && !empty($adminAccessUpdate) && empty($invalidData) &&  empty($selfSuperAdminUpdate))
		{
			for($i=0;$i<count($designationRoleData);$i++)
			{
				if($operation=='Edit')
				{
					$where = array('Designation_Id= ?' => $designationRoleData[$i]['Designation_Id'], 'Form_Id = ?' => $designationRoleData[$i]['Form_Id']);
					$updated = $this->_db->update($this->_ehrTables->roles,$designationRoleData[$i], $where);
					 $roleFormsQry = $this->_db->select()
								->from(array('roles'=>$this->_ehrTables->roles),new Zend_Db_Expr('COUNT(Designation_Id)'))
								->where('roles.Designation_Id = ?', $designationRoleData[$i]['Designation_Id'])
								->where('roles.Form_Id = ?', $designationRoleData[$i]['Form_Id']);
					 $roles = $this->_db->fetchOne($roleFormsQry);
								if($roles > 0)
					{
					$updated = 1;
					}
					else
					{
					$updated = $this->_db->insert($this->_ehrTables->roles,$designationRoleData[$i]);
					}
				}
			}
			
			if($operation=='Add')
			{
				$updated = $this->_ehrTables->insertMultiple($this->_ehrTables->roles,$designationRoleData);
			}
			
			return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
										'action'         => $operation,
										'trackingColumn' => $sessionId,
										'formName'       => 'Designation Roles',
										'sessionId'      => $sessionId,
										'tableName'      => $this->_ehrTables->roles));
		}
		else
		{
			if($invalidData == 1)
			{
				return array('success' => false, 'msg'=>'Invalid Data', 'type'=>'warning');
			}
			else if(empty($adminAccessUpdate))
			{
				return array('success' => false, 'msg'=>'Admin should not have employee admin and payroll admin roles', 'type'=>'warning');
			}
			else if($isSuperAdmin == 1)
			{
				return array('success' => false,
						 'msg'=>'You cannot remove employee and designation access to super admin',
						 'type'=>'warning');
			}
			else if(empty($superAdminCnt))
			{
				return array('success' => false,
						 'msg'=>'You cannot remove super admin access to this designation. You should have atleast one super admin in the system',
						 'type'=>'warning');
			}
			else
			{
				return array('success' => false,
						 'msg'=>'You cannot remove super admin access to your designation',
						 'type'=>'warning');
			}
		}
    }
    
	/**
     * to list all forms roles for a designation with no subform
     */
    public function designationRoles($designationId)
    {
        if ($designationId != "")
        {
            $roleFormsQry = $this->_db->select()
                                    ->from(array('role'=>$this->_ehrTables->roles), array('View'=>'Role_View', 'Add'=>'Role_Add', 'Update'=>'Role_Update',
                                        'Optional_Choice'=>'Role_Optional_Choice', 'Delete'=>'Role_Delete', 'role.Form_Id', 'role.Designation_Id',
                                        'Hr_Group'=>'Role_Hr_Group','Payroll_Group'=>'Role_Payroll_Group'))
                                    ->joinInner(array('f'=>$this->_ehrTables->forms),'f.Form_Id=role.Form_Id',array('f.Sub_Form'))
                                    ->where('role.Designation_Id = ?', $designationId)
                                    ->order("f.Form_Id Asc");
            $roles = $this->_db->fetchAll($roleFormsQry);
            return $roles;
        }
    }

    /**
     * This function is to copy designation roles for obtained designationId to employee access rights table.
     */
    public function copyRoles($fetchedData,$designationRoles, $employeeId,$addedBy,$task)
    {
        $formId = array();
        $roleId = array();
        $formCount = count($fetchedData);
        $formCnt = 0;
        $empName = $this->_dbPersonal->employeeName($addedBy);
         
        $where = array();
        $updateData = array();
        if($task == "insert")
        {
            foreach ($designationRoles as $roles)
            {
                foreach ($fetchedData as $form)
                {
                    if ($form['Form_Id'] == $roles['Form_Id'])
                    {
                        $data[$formCnt] = array(
	  					    					'Role_View' => $roles['View'],
	  					    					'Role_Add'=>$roles['Add'],
	  					    					'Role_Update'=>$roles['Update'],
	  					    					'Role_Delete'=>$roles['Delete'],
	  					    					'Role_Optional_Choice'=> $roles['Optional_Choice'],
                        		                'Role_Hr_Group'=> $roles['Hr_Group'],
                        						'Role_Payroll_Group'=> $roles['Payroll_Group'],
	  					    					'Form_Id'=>$form['Form_Id'],
	  					    					'Employee_Id'=>$employeeId,
	  					    					'Added_By'=>$addedBy,'Lock_Flag'=>$addedBy,
	  					    					'Added_On'=>date('Y-m-d H:i:s'));

                        array_push($updateData,$data);
                        $formCnt++;
                    }
                }
            }
            $insertResult = $this->_ehrTables->insertMultiple($this->_ehrTables->empAccessRights,$data);
            if($insertResult)
            {
                $this->_ehrTables->trackEmpSystemAction('Edit Employee Roles - '.$empName['Employee_Name'], $addedBy);

            }
        }
        	
        else if($task == "update")
        {
            $roleExistsQry = $this->_db->select()
            ->from(array('empAccessRights'=>$this->_ehrTables->empAccessRights), array('empAccessRights.Form_Id'))
            ->where('empAccessRights.Employee_Id = ?',$employeeId);
            $roleForms = $this->_db->fetchCol($roleExistsQry);
            $formForms = array();
             
            foreach ($designationRoles as $roles)
            {
                foreach ($fetchedData as $form)
                {
                    array_push($formForms,$form['Form_Id']);
                    if ($form['Form_Id'] == $roles['Form_Id'])
                    {
                        $data = array(
    			  					    					'Role_View' => $roles['View'],
    			  					    					'Role_Add'=>$roles['Add'],
    			  					    					'Role_Update'=>$roles['Update'],
    			  					    					'Role_Delete'=>$roles['Delete'],
    			  					    					'Role_Optional_Choice'=> $roles['Optional_Choice'],
                        									'Role_Hr_Group'=> $roles['Hr_Group'],
                        									'Role_Payroll_Group'=> $roles['Payroll_Group'],
                        		 	    					'Form_Id'=>$form['Form_Id'],
    			  					    					'Employee_Id'=>$employeeId,
    			  					    					'Added_By'=>$addedBy,
    			  					    					'Lock_Flag'=>$addedBy,
    			  					    					'Added_On'=>date('Y-m-d H:i:s'));
                        	
                        if (in_array($form['Form_Id'], $roleForms))
                        {
                            $where = array('Employee_Id= ?' => (int)$employeeId, 'Form_Id = ?' => $form['Form_Id']);
                            $updated = $this->_db->update($this->_ehrTables->empAccessRights,$data, $where);
                            array_push($updateData,$data);
                        }
                        else if (!in_array($form['Form_Id'], $roleForms))
                        {
                            $updated = $this->_db->insert($this->_ehrTables->empAccessRights,$data);
                            array_push($updateData,$data);
                        }
                    }
                }
            }
            foreach ($roleForms as $role)
            {
                if (!in_array($role, $formForms))
                {
                    $where = array('Employee_Id = ?' => (int)$employeeId, 'Form_Id = ?' => $role);
                    $this->_db->delete($this->_ehrTables->empAccessRights, $where);
                }
            }

            if(!empty($updateData))
            {
                $this->_ehrTables->trackEmpSystemAction('Edit Employee Roles - '.$empName['Employee_Name'], $addedBy);
            }
        }
        return $updateData;

    }
    
	/**
     * This function is to fetch maximium employeeId.
     *
     */
    public function getMaxEmployeeId()
    {
         
        $qrymaxId = $this->_db->select()->from($this->_ehrTables->empPersonal, array(new Zend_Db_Expr('Max(Employee_Id) as MaxId')));
        $maxId = $this->_db->fetchOne($qrymaxId);
        return $maxId;

    }
    
	/**
     *
     * This function is to find employee personal details af an employee.
     */
    public function checkEmpExist($employeeId)
    {
        //This function is to find employee personal details.
        $srEmpQry = $this->_db->select()
        ->from(array('personal'=>$this->_ehrTables->empPersonal),array(new Zend_Db_Expr('count(Employee_Id)')))
        ->where('personal.Employee_Id = ?', $employeeId);
        $srEmpresult = $this->_db->fetchOne($srEmpQry);
        return $srEmpresult;

    }
    
	/**
     *
     * This function is to find dependent details for an employee ...
     *
     */
    public function getDependent($employeeId)
    {
        $employeeId = (int) $employeeId;
        $srDepQry = $this->_db->select()
        ->from(array('dep'=>$this->_ehrTables->empDependent),array('*'))
        ->where('dep.Employee_Id = ?', $employeeId);
        $srDepresult = $this->_db->fetchAll($srDepQry);
        return $srDepresult;

    }
    
	//to list Country field in employee import inline edit    
	public function getCountry()
    {
		return $this->_db->fetchPairs($this->_db ->select()->from(array('cou'=>$this->_ehrTables->country),
								array('value'=> 'cou.Country_Name','text'=>'cou.Country_Name'))
    	->order('cou.Country_Name ASC'));
    }
	
	/**
     *
     * This function is to find country details.
     */
    public function getCountries()
    {
        return $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->country, array('Country_Code', 'Country_Name'))
									  ->order('Country_Name ASC'));
    }
	
	public function getCountriesPPair()
    {
        return $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->country, array('Country_Name')));
    }

    /**
     *
     * This function is to find marital status details.
     */
    public function getMaritalStatus()
    {
        $maritalStatusQry = $this->_db->select()
        ->from(array('m'=>$this->_ehrTables->maritalStatus),array('m.Marital_Status_Id','m.Marital_Status'));
        $maritalStatus = $this->_db->fetchPairs($maritalStatusQry);
        return $maritalStatus;
    }
    
	//list service provider details based on session id
	public function getEmployeeServiceProviderList($sessionId)
    {
		$serviceProviderQry = $this->_db->select()->from($this->_ehrTables->serviceProvider,array('Service_Provider_Id','Service_Provider_Name'));
		$dbAccessRights    	= new Default_Model_DbTable_AccessRights();
		$employeeAccess  	= $dbAccessRights->employeeAccessRights($sessionId,'Employees');
		if(!empty($employeeAccess['Admin']) || !empty($employeeAccess['Employee']['Is_Manager']))
		{
			$isEmployee = 0;   
		}
		else 
		{
			$isEmployee = 1;   
		}

		$serviceProviderId = $this->_dbCommonFun->getEmployeeServiceProviderId($sessionId,$isEmployee);
		if(!empty($serviceProviderId))
		{
			$serviceProviderQry->where('Service_Provider_Id = ?', $serviceProviderId);
		}

		return $this->_db->fetchPairs($serviceProviderQry);
    }

	/**
     *
     * This function is to find employee project details for an employee ...
     *
     */
    public function getProjectDetails($employeeId)
    {
        $empProjectQry = $this->_db->select()
        ->from(array('emp_project'=>$this->_ehrTables->empProject),array('emp_project.Project_Id'))
        ->joinInner(array('project'=>$this->_ehrTables->project),'emp_project.Project_Id=project.Project_Id ',array('Manager_Id','Project_Name as projectName'))
        ->joinInner(array('personal'=>$this->_ehrTables->empPersonal),'personal.Employee_Id=project.Manager_Id ',array(new Zend_Db_Expr("CONCAT(personal.Emp_First_Name, ' ',personal.Emp_Last_Name) as managerName")))
        ->where('emp_project.Employee_Id = ?', $employeeId);
        $empProjectDetails = $this->_db->fetchAll($empProjectQry);
        return $empProjectDetails;

    }
    
	/**
     *
     * This function is to find employee experience details for an employee ...
     *
     */
    public function getExperience($employeeId)
    {
        $employeeId = (int) $employeeId;
        $srExpQry = $this->_db->select()
        ->from(array('exp'=>$this->_ehrTables->empExperience),array('*'))
        ->where('exp.Employee_Id = ?', $employeeId);
        $srExpResult = $this->_db->fetchAll($srExpQry);
        return $srExpResult;
    }
    
	/**
     *
     * This function is to find employee educational details for an employee ...
     *
     */
    public function getEducation($employeeId)
    {
        $employeeId = (int) $employeeId;
        $srEduQry = $this->_db->select()
        ->from(array('edu'=>$this->_ehrTables->empEducation),array('*'))
        ->where('edu.Employee_Id = ?', $employeeId);
        $srEduResult = $this->_db->fetchAll($srEduQry);
        return $srEduResult;
    }
    
	/**
     *
     * This function is to find employee personal details af an employee.
     */
    public function getEmp($employeeId)
    {
        $srEmpQry = $this->_db->select()
        ->from(array('personal'=>$this->_ehrTables->empPersonal),array('*'))
        ->where('personal.Employee_Id = ?', $employeeId);
        $srEmpresult = $this->_db->fetchAll($srEmpQry);
        return $srEmpresult;

    }
     
    /**
     *
     * This function is to find project details ...
     *
     */
    public function getProjById($projectId)
    {
        return $this->_db->fetchAll($this->_db->select()
									->from(array('p'=>$this->_ehrTables->empPersonal),
										   array('p.Employee_Id',
												 new Zend_Db_Expr("CONCAT(p.Emp_First_Name, ' ',p.Emp_Last_Name) as employeeName")))
									
									->joinInner(array('empProject'=>$this->_ehrTables->project),'p.Employee_Id=empProject.Manager_Id',
												array(''))
									
									->where('empProject.Project_Id = ?', $projectId));

    }
    
	/**
     *This function is to fetch employee manager details
     *
     */
    public function getEmpManagerNames()
    {
        $empMgrQry = $this->_db->select()
        ->from(array('t1'=>$this->_ehrTables->empPersonal),array(new Zend_Db_Expr("CONCAT(t1.Emp_First_Name, ' ',t1.Emp_Last_Name) as Emp_Name")))
        ->joinInner(array('t2'=>$this->_ehrTables->empJob),'t1.Employee_Id=t2.Manager_Id',array('*'));
        $empMgrResult = $this->_db->fetchAll($empMgrQry);
        return $empMgrResult;

    }
    
	/**
     *
     * This is for fetch employee login details.
     */
    public function getLoginDetails($id)
    {
        $empLoginQry = $this->_db->select()
        ->from(array('login'=>$this->_ehrTables->empLogin),array('Username'=>'User_Name', 'Created_Date', 'Employee_Id'))
        ->where('login.Employee_Id = ?', $id);
        $empLoginDet = $this->_db->fetchRow($empLoginQry);
        return $empLoginDet;
    }

    /**
     *
     * This function is to find employee certification details for an employee ...
     *
     */
    public function getCertificate($employeeId)
    {
        $employeeId = (int) $employeeId;
        $srCertQry = $this->_db->select()
        ->from(array('cert'=>$this->_ehrTables->empCertifications),array('*'))
        ->where('cert.Employee_Id = ?', $employeeId);
        $srCertResult = $this->_db->fetchAll($srCertQry);
        return $srCertResult;
    }
    
	/**
     *
     * This function is to find employee training details for an employee ...
     *
     */
    public function getTraining($employeeId)
    {
        $employeeId = (int) $employeeId;
        $srTrainQry = $this->_db->select()
        ->from(array('train'=>$this->_ehrTables->empTraining),array('*'))
        ->where('train.Employee_Id = ?', $employeeId);
        $srTrainResult = $this->_db->fetchAll($srTrainQry);
        return $srTrainResult;
    }
    
	/**
     *
     * This function is to find employee award details for an employee ...
     *
     */
    public function getAward($employeeId)
    {
        $srAwardQry = $this->_db->select()
        ->from(array('award'=>$this->_ehrTables->empAward),array('*'))
        ->where('award.Employee_Id = ?', $employeeId);
        $srAwardResult = $this->_db->fetchAll($srAwardQry);
        return $srAwardResult;
    }
    
	/*
     *get photo path for employeeId
    *@param int $empId - Employee_Id
    *@return string $path - upload/79.rtf
    */
    public function getEmpPhotoPath($empId)
    {
        return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empPersonal, 'Photo_Path')
									->where('Employee_Id = ?',$empId));
    }

    /**
     * This function is to check this employee already exists or not
     */
    public function checkEmployeeExists($empId,$empFirstName,$empLastName,$nationality,$dob,$bloodGroup,$maritalStatus,$isImport = null,$userEmpId=null)
    {
    	if(is_null($isImport))
    	{
    		$dob = date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($dob)));
    	}
        
        if(!is_null($userEmpId))
        {
           $empId = substr($userEmpId,9);
        }
        
        $srEmpQry = $this->_db->select()
		->from(array('emp'=>$this->_ehrTables->empPersonal),array('COUNT(emp.Employee_Id)'))
		->joinLeft(array('job'=>$this->_ehrTables->empJob),'emp.Employee_Id=job.Employee_Id',array(''))					   
		->where('emp.Emp_First_Name = ?',"$empFirstName")
        ->where('emp.Emp_Last_Name = ?',"$empLastName")
        ->where('emp.Nationality = ?',"$nationality")
        ->where('emp.DOB = ?',"$dob")
        ->where('emp.Blood_Group = ?',"$bloodGroup")
		->where('emp.Marital_Status = ?',$maritalStatus)
		->where('job.Emp_Status LIKE ?','Active');
        if(!empty($empId) && $empId!=0)
        {
            $srEmpQry->where('emp.Employee_Id != ?',$empId);
        }
		
		return $this->_db->fetchOne($srEmpQry);
    }
	
    /*
     *@param int $empId - Employee_Id
    *@return assoc array - asset details
    */
    public function getMuliptleAsset($empId)
	{
    	$assets = $this->_db->fetchAll($this->_db->select()
									->from(array('A' => $this->_ehrTables->empAssets),
										   array('Asset_Name', 'Serial_No', 'Receive_Date', 'Return_Date',
												 'ReceiveDate' => new Zend_Db_Expr("date_format(Receive_Date, '".$this->_orgDF['sql']."')"),
												 'ReturnDate' => new Zend_Db_Expr("date_format(Return_Date, '".$this->_orgDF['sql']."')")))
									
									->where('A.Employee_Id = ?', $empId)
									->order("Asset_Id asc"));
        
        for($i=0;$i<count($assets);$i++)
        {
            if($assets[$i]['Return_Date'] == "0000-00-00")
            {
                $assets[$i]['ReturnDate'] = "-";
            }
        }
        
        return $assets;
    }
    
	/**
     * remove image path of employee
     * @param str $empPhotoPath
     * @param int $empId
     */
    public function deletePhotoPath($empPhotoPath, $empId)
    {
    	$where = array('Employee_Id = ?' => $empId);
		
		$photoPath = $getEmpSett = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empPersonal,
                                                                        array('Photo_Path'))
														->where('Employee_Id = ?', $empId));
    	$deleted = $this->_db->update($this->_ehrTables->empPersonal,array('Photo_Path' => NULL),$where);
		
		return array('success'=> true, 'msg' => 'Image deleted successfully', 'ImageExists'=>0, 'PhotoPath'=>$photoPath, 'type' => 'info');
    }
    	
	/**
	 * Import employee details from leaf to master
	 */
    public function empLeafToMasterImport($userIpAddress, $logEmpId) 
    {
		$returnVal = 0;
		$newEmployeeIds = array();

		$msg = '';
    	$leafRecQry = $this->_db->select()->distinct()->from(array('Imp'=>$this->_ehrTables->employeeImport),
    													array('Emp_Import_Id','Salutation','Emp_First_Name','Emp_Middle_Name','Emp_Last_Name',
                                                              'Nick_Name','Gender','Nationality','DOB' => new Zend_Db_Expr('Birth_Date'),
                                                              'Place_Of_Birth','Marital_Status', 'Languages_Known','Blood_Group', 'Emp_Hobby',
                                                              'Military_Service','Ethnic_Race','Religion','Caste','Is_Manager','Personal_Email',
                                                              'Physically_Challenged','Smokerasof','Driving_License_No','License_Issue_Date',
                                                              'License_Expiry_Date','License_Issuing_Authority','License_Issuing_Country',
                                                              'Vehicle_Type','Passport_No','Passport_Issue_Date','Passport_Expiry_Date','License_Issuing_State',
                                                              'Employee_Type','Designation_Name','Department_Name','Location_Name','Emp_Email',
                                                              'Emp_Profession','TDS_Exemption','Job_Code','Date_Of_Join','Manager_Name',
                                                              'Emp_Status','Emp_InActive_Date','Work_Schedule','Commission_Employee','Confirmation_Date',
    														  'Per_Apartment_Name','Per_Street_Name', 'Per_City', 'Per_State', 'Per_Country', 'Per_Pincode', 
    														  'Curr_Apartment_Name','Curr_Street_Name', 'Curr_City', 'Curr_State', 'Curr_Country', 'Current_Pincode', 
    														  'Land_Line_No','Mobile_No','Fax_No','Work_No','Is_Illiterate',
                                                              'Education_Type' => 'Deg_Dep_School', 'Specialisation', 'Institute_Name', 
    														  'University', 'Year_Of_Passing', 'Percentage','Primary_Skill','Secondary_Skill','Known_Skills',
                                                              'Hands_On','Grade','External_EmpId','User_Defined_EmpId','Attendance_Enforced_Payment',
                                                              'Bank_Account_Number','Account_Type','Bank_IFSC_Code','Bank_Name','Branch_Name',
                                                              'Bank_Street','Bank_City','Bank_State','Bank_Pincode','Bank_Credit_Account','Bank_Beneficiary_Id','PAN',
                                                              'Aadhaar_Card_Number','UAN','Pf_PolicyNo','Import_Status','Policy_No','Allow_User_Signin',
															  'Send_Email_Invitation','Enable_Sign_In_With_Mobile_No',
															  'Sign_In_Mobile_No_Country_Code','Previous_Employee_Experience'))
						->joinInner(array('m'=>$this->_ehrTables->maritalStatus), 'm.Marital_Status = Imp.Marital_Status', array('Marital_Status' => new Zend_Db_Expr('Marital_Status_Id')))
						->joinLeft(array('SP' => $this->_ehrTables->serviceProvider),'Imp.Service_Provider_Name = SP.Service_Provider_Name',array('SP.Service_Provider_Id'))
						->joinLeft(array('Desi' => $this->_ehrTables->designation),'Imp.Designation_Name = Desi.Designation_Name','Designation_Id')
						->joinLeft(array('Dept' => $this->_ehrTables->dept),'Imp.Department_Name = Dept.Department_Name','Department_Id')
						->joinLeft(array('Loc' => $this->_ehrTables->location),'Imp.Location_Name = Loc.Location_Name','Location_Id')
						->joinLeft(array('emp'=>$this->_ehrTables->empPersonal)," Imp.Manager_Name = CONCAT(emp.Emp_First_Name,' ',emp.Emp_Last_Name)",array('Manager_Id' => 'Employee_Id'))
						->joinLeft(array('WS' => $this->_ehrTables->workSchedule),'Imp.Work_Schedule = WS.Title','WorkSchedule_Id')
						->joinLeft(array('empType' => $this->_ehrTables->empType),'Imp.Employee_Type = empType.Employee_Type','EmpType_Id')
						->joinLeft(array('AT' => $this->_ehrTables->accountType),'Imp.Account_Type = AT.Account_Type','Account_Type_Id')
						->joinLeft(array('BD' => $this->_ehrTables->bankDetails),'Imp.Bank_Name = BD.Bank_Name','BD.Bank_Id')
    					->joinLeft(array('pCountry'=>$this->_ehrTables->country),'Imp.Per_Country = pCountry.Country_Name',array('pCountry' => 'pCountry.Country_Code'))
    					->joinLeft(array('cCountry'=>$this->_ehrTables->country),'Imp.Curr_Country = cCountry.Country_Name',array('cCountry' => 'cCountry.Country_Code'))
						
						->joinLeft(array('c'=>$this->_ehrTables->courseDetails),'Imp.Deg_Dep_School=c.Course_Name ',
												array('c.Course_Id'))
						->joinLeft(array('EP'=>$this->_ehrTables->empProfession), 'EP.Profession_Name = Imp.Emp_Profession',
											array('Profession_Id'))
						->joinLeft(array('ER'=>$this->_ehrTables->esicReason),'Imp.Reason_Id = ER.ESIC_Reason',
                                            array('ER.Reason_Id'))
                                    					
    					//taking only the unprocessed record from the leaf table 
    	                //because records with status as already exist and invalid record will remain same after processing also.
    					->group('Imp.Emp_Import_Id')
                        ->where('Imp.Import_Status = 4');

			//Filtering to import records for a particular service provider incase of service provider admin
			$leafRecQry = $this->_dbCommonFun->formServiceProviderQuery($leafRecQry,'SP.Service_Provider_Id',$logEmpId);        	
    	
        	$leafRec = $this->_db->fetchAll($leafRecQry);
			$sendMailList = array();
            if(!empty($leafRec) && count($leafRec) > 0)
            {
                foreach ($leafRec as $key => $rec)
                {
                    if($rec['Smokerasof'] == NULL || $rec['Smokerasof'] == '')
                        $smoker = 0;
                    else
                        $smoker = 1;
                        
                    if($rec['Manager_Id'] == NULL)
                        $rec['Manager_Id'] = 0;
                        
					/**Validate the salutation based on the employee gender and the marital status */
					$newSalutation = $this->_dbCommonFun->getEmpSalutationBasedGenderMaritalStatus($rec['Salutation'],$rec['Gender'],$rec['Marital_Status']);
					
					$personalData = array(
                            'Emp_First_Name'  	=> $rec['Emp_First_Name'],
                            'Emp_Middle_Name'  	=> $rec['Emp_Middle_Name'],
                            'Emp_Last_Name' 	=> $rec['Emp_Last_Name'],
                            'Nick_Name' 		=> $rec['Nick_Name'],
                            'Gender'  			=> $rec['Gender'],
                            'Personal_Email'  	=> $rec['Personal_Email'],
                            'Physically_Challenged' => $rec['Physically_Challenged'],
                            'DOB'  				=> $rec['DOB'],
                            'Place_Of_Birth'  	=> $rec['Place_Of_Birth'],
                            'Marital_Status' 	=> $rec['Marital_Status'],
                            'Smoker'  			=> $smoker,
                            'Smokerasof' 		=> $rec['Smokerasof'],
                            'Military_Service'  => $rec['Military_Service'],
                            'Ethnic_Race'  		=> $rec['Ethnic_Race'],
                            'Nationality'  		=> $rec['Nationality'],
                            'Blood_Group'  		=> $rec['Blood_Group'],
                            'Religion'  		=> $rec['Religion'],
                            'Caste'  			=> $rec['Caste'],
                            'Is_Manager' 		=> $rec['Is_Manager'],
                            'Salutation' 		=> $newSalutation,
                            'PAN' 				=> $rec['PAN'],
                            'Aadhaar_Card_Number' => $rec['Aadhaar_Card_Number'],
                            'UAN' 				=> $rec['UAN'],
                            'Form_Status'	 	=> 1,
                            // 'UserName_OverWrite'=> 0,
							'Is_Illiterate' 	=> $rec['Is_Illiterate'],
							'Allow_User_Signin' => $rec['Allow_User_Signin'],
							'Enable_Sign_In_With_Mobile_No' =>$rec['Enable_Sign_In_With_Mobile_No'],
							'Sign_In_Mobile_Number'=>$rec['Mobile_No'],
							'Sign_In_Mobile_No_Country_Code'=>$rec['Sign_In_Mobile_No_Country_Code'],
							'Hobbies'				=>$rec['Emp_Hobby']);

                    $employeeExists = $this->checkEmployeeExists(null,$personalData['Emp_First_Name'],$personalData['Emp_Last_Name'],$rec['Nationality'],$rec['DOB'],$rec['Blood_Group'],$rec['Marital_Status'],'import');
                    
                    if(empty($employeeExists))
                    {
                       $maxEmployeeId = $this->getMaxEmployeeId();
					   $personalData['Employee_Id'] =  $maxEmployeeId + 1;
					   $userDefinedEmployeeIdExist = $this->_dbCommonFun->checkUserDefinedEmployeeIdExist ($rec['User_Defined_EmpId'],0);
                    
                        if(!empty($userDefinedEmployeeIdExist))
                        {
                            $validUpdated = $this->_db->update($this->_ehrTables->employeeImport, array('Import_Status'=> -1), array('Emp_Import_Id = ?'=> $rec['Emp_Import_Id']));
                        }
                        else
                        {
							$jobDetailsArray = array(
								'designationId'  => $rec['Designation_Id'] ? $rec['Designation_Id'] : 0,
								'employeeTypeId'  => $rec['EmpType_Id'] ? $rec['EmpType_Id'] : 0,
								'departmentId'  => $rec['Department_Id'] ? $rec['Department_Id'] : 0,
								'locationId'  => $rec['Location_Id'] ? $rec['Location_Id'] : 0,
								'workScheduleId'  => $rec['WorkSchedule_Id'] ? $rec['WorkSchedule_Id'] : 0);
							$isInactiveDeletedJobInfoUsed = $this->validateInActiveDeletedJobDetails($jobDetailsArray);

							if(count($isInactiveDeletedJobInfoUsed) == 0){
								$insertPer = $this->_db->insert($this->_ehrTables->empPersonal, array_filter($personalData));
							
								if($insertPer)
								{
									//check if email id is exist or not
									$isMailExist = $this->_dbPersonal->countEmpEmailId ($rec['Emp_Email'], '');
									if($isMailExist == 0)
									{
										if(!is_null($rec['Languages_Known']))
										{
											$langList = explode(',', $rec['Languages_Known']);
											if(count($langList) > 0 && is_array($langList))
											{
												foreach ($langList as $lang)
												{
													$lang = trim($lang);
													$langID = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->languages,'Lang_Id')->where('Language_Name = ?',"$lang"));
													if($langID)
													{
														$langData[] = array('Employee_Id' => $personalData['Employee_Id'],'Lang_Known' => $langID);
													}
												}

												if(!empty($langData))
												{
													$langInserted = $this->_ehrTables->insertMultiple($this->_ehrTables->empLang,array_filter($langData));
													if($langInserted)
													{
														unset($langData);
													}
												}
											}
										}
										if($rec['Confirmation_Date'] == NULL || $rec['Confirmation_Date'] == '')
											$confirmed = 0;
										else
											$confirmed = 1;
										
										if($rec['Manager_Id'] == NULL || $rec['Manager_Id'] == '')
											$rec['Manager_Id'] = 0;
											
										$jobData = array(
												'Employee_Id' => $personalData['Employee_Id'],
												'Designation_Id'  => $rec['Designation_Id'],
												'EmpType_Id'  => $rec['EmpType_Id'],
												'Department_Id'  => $rec['Department_Id'],
												'Location_Id'  => $rec['Location_Id'],
												'Date_Of_Join'  => $rec['Date_Of_Join'],
												'Job_Code' => $rec['Job_Code'],
												'Emp_Email' => $rec['Emp_Email'],
												'Manager_Id'  => $rec['Manager_Id'],
												'Confirmed'  => $confirmed,
												'Emp_Profession'  => $rec['Profession_Id'],
												'Emp_Status'  => $rec['Emp_Status'],
												'Emp_InActive_Date'  => $rec['Emp_InActive_Date'],
												'Confirmation_Date'  => $rec['Confirmation_Date'],
												'EmpType_Id'  => $rec['EmpType_Id'],
												'Commission_Employee'  => $rec['Commission_Employee'],
												'Work_Schedule'  => $rec['WorkSchedule_Id'],
												'External_EmpId'=> new Zend_Db_Expr('NULL'),
												'User_Defined_EmpId'=>$rec['User_Defined_EmpId'],
												'Pf_PolicyNo'=>$rec['Pf_PolicyNo'],
												'TDS_Exemption'=>$rec['TDS_Exemption'],
												'Attendance_Enforced_Payment'=>$rec['Attendance_Enforced_Payment'],
												'Reason_Id'=>$rec['Reason_Id'],
												'Previous_Employee_Experience'=>$rec['Previous_Employee_Experience'],
												'Service_Provider_Id'  => $rec['Service_Provider_Id'],
												'Global_Resource_Id' => $rec['User_Defined_EmpId'],
												//'Roles_Id'=>
												'Designation_Id_Effective_Date'=>$rec['Date_Of_Join'],
												'Department_Id_Effective_Date'=>$rec['Date_Of_Join'],
												'Location_Id_Effective_Date'=>$rec['Date_Of_Join'],
												'Manager_Id_Effective_Date'=>$rec['Date_Of_Join'],
												'EmpType_Id_Effective_Date'=>$rec['Date_Of_Join'],
												'Work_Schedule_Effective_Date'=>$rec['Date_Of_Join'],
												//'Business_Unit_Id'=>
												//'Business_Unit_Id_Effective_Date'=>$rec['Date_Of_Join'],
												'Added_On'=>date('Y-m-d H:i:s'),
												'Added_By'=>$logEmpId);
										if($rec['Commission_Employee'] > 0)
										{
											$jobData['Commission_Employee']  = 1;
										}
										else
										{
											$jobData['Commission_Employee'] = 0;
										}
										
										if(!is_null($rec['Confirmation_Date']))
										{
											$jobData['Confirmed']  = 1;
											$jobData['Confirmation_Date']  = $rec['Confirmation_Date'];
										}
										
										$probationPeriod = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->designation,
																										array('Probation_Days'))
																		->where('Designation_Id = ?',$rec['Designation_Id']));
										
										if($probationPeriod >= 0)
										{
											$jobData['Probation_Date'] = date('Y-m-d',strtotime('+'.$probationPeriod.' days',strtotime($rec['Date_Of_Join'])));
										}
										else
										{
											$jobData['Probation_Date'] = $rec['Date_Of_Join'];
										}
										
										$jobUpdated = $this->_db->insert($this->_ehrTables->empJob,array_filter($jobData));
										
										if($jobUpdated)
										{
											if (!empty($rec['Date_Of_Join']))
											{
												$employeeLeaveBalUpdateDetails = array(
													'Employee_Id' => $personalData['Employee_Id'],
													'Date_Of_Join' => $rec['Date_Of_Join'],
													'Probation_Date' => $jobData['Probation_Date'],
													'Form_Status' => $personalData['Form_Status'],
													'Is_Update' => false
												);
										
												/* Update the employee leave type balance */
												$eligibleForLeaveBalance = $this->_dbLeave->updateNewJoineeLeaveTypeBalance($employeeLeaveBalUpdateDetails,1);
												if(!empty($eligibleForLeaveBalance))
												{
													$leaveBalanceUpdated = $this->_dbLeave->empDOJUpdateEligibleDays($eligibleForLeaveBalance['Employee_Id'],'employee-data-import');
												}
											}
											
											if(!is_null($rec['Location_Id']) && !is_null($rec['Manager_Id']) && !is_null($rec['WorkSchedule_Id']) && 
											!is_null($rec['Designation_Id']) && !is_null($rec['Department_Id']) && !is_null($rec['EmpType_Id']))
											{
												$locationQry = $this->_db->select()->from(
														array('loc'=>$this->_ehrTables->location),array('Street1', 'Street2', 'Pincode','Country_Code'))
												->joinLeft(array('S'=>$this->_ehrTables->state),'S.State_Id=loc.State_Id', array('S.State_Name as State'))
												->joinLeft(array('C'=>$this->_ehrTables->city),'C.City_Id=loc.City_Id', array('C.City_Name as City'))
												
														//->joinLeft(array('country'=>$this->_ehrTables->country),'loc.Country = country.Country_Name',array('country.Country_Code'))
														->where('loc.Location_Id = ?', $rec['Location_Id']);
												$locationRow = $this->_db->fetchRow($locationQry);
												
												$contactData = array(
														'Employee_Id' => $personalData['Employee_Id'],
														'pApartment_Name'  => $rec['Per_Apartment_Name'],
														'pStreet_Name'  => $rec['Per_Street_Name'],
														'pCity'  => $rec['Per_City'],
														'pState' => $rec['Per_State'],
														'pCountry'  => $rec['pCountry'],
														'pPincode'  => $rec['Per_Pincode'],
														'Mobile_No'  => $rec['Mobile_No'],
														'Land_Line_No'  => $rec['Land_Line_No'],
														'Fax_No'  => $rec['Fax_No'],
												);
												$contactData['oApartment_Name'] = $locationRow['Street1'];
												$contactData['oStreet_Name'] = $locationRow['Street2'];
												$contactData['oCity'] = $locationRow['City'];
												$contactData['oState'] = $locationRow['State'];
												$contactData['oCountry'] = $locationRow['Country_Code'];
												$contactData['oPincode'] = $locationRow['Pincode'];
												
												$contactData['cApartment_Name'] =  $rec['Curr_Apartment_Name'];
												$contactData['cStreet_Name'] =  $rec['Curr_Street_Name'];
												$contactData['cCity'] =  $rec['Curr_City'];
												$contactData['cState'] =  $rec['Curr_State'];
												$contactData['cCountry'] =  $rec['cCountry'];
												$contactData['cPincode'] =  $rec['Current_Pincode'];
												
												$contactInsert = $this->_db->insert($this->_ehrTables->empContacts,array_filter($contactData));
												
												if($contactInsert)
												{
													if($rec['Is_Illiterate'] == 0 && $rec['Course_Id'] != '' || $rec['Specialisation'] != '' || $rec['Institute_Name'] != '' ||
													$rec['University'] != '' || $rec['Year_Of_Passing'] != '' || $rec['Percentage'] != '' ||
													$rec['Grade'] != '')
													{
														$eduData = array('Employee_Id' => $personalData['Employee_Id'],
																		'Education_Type' => $rec['Course_Id'],
																		'Specialisation' => $rec['Specialisation'],
																		'Institute_Name' => $rec['Institute_Name'],
																		'University' => $rec['University'],
																		'Year_Of_Passing' => $rec['Year_Of_Passing'],
																		'Percentage' => $rec['Percentage'],
																		'Grade' => $rec['Grade']);
														
														$eduInsert = $this->_db->insert($this->_ehrTables->empEducation,array_filter($eduData));
													}
													
													if($rec['Primary_Skill'] != NULL || $rec['Primary_Skill'] !='')
													{
														$skillData = array(
																'Employee_Id' => $personalData['Employee_Id'],
																'Primary_Skill' => $rec['Primary_Skill'],
																'Secondary_Skill' => $rec['Secondary_Skill'],
																'Known_Skills' => $rec['Known_Skills'],
																'Hands_On' => $rec['Hands_On']
																);
														
														$skillInsert = $this->_db->insert($this->_ehrTables->empSkillset,array_filter($skillData));
													}
													
													if($rec['Bank_Account_Number'] != NULL || $rec['Bank_Account_Number'] !='')
													{
														$bankData = array(
															'Employee_Id' => $personalData['Employee_Id'],
															'Bank_Account_Number' => $rec['Bank_Account_Number'],
															'Emp_Bank_Id' => $rec['Bank_Id'],
															'Branch_Name' => $rec['Branch_Name'],
															'IFSC_Code' => $rec['Bank_IFSC_Code'],
															'Street' => $rec['Bank_Street'],
															'City' => $rec['Bank_City'],
															'State' => $rec['Bank_State'],
															'Zip' => $rec['Bank_Pincode'],
															'Account_Type_Id' => $rec['Account_Type_Id'],
															'Credit_Account' => $rec['Bank_Credit_Account'],
															'Beneficiary_Id' => $rec['Bank_Beneficiary_Id'],
															'Status' => "Active"
															);
														
														$bankInsert = $this->_db->insert($this->_ehrTables->empBank,array_filter($bankData));
													}
													
													if($rec['Policy_No'])
													{
														$insTypeId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->insuranceType,array('InsuranceType_Id'))
																						->where('Employee_State_Insurance = ?',1));
														if(!empty($insTypeId))
														{
															$insData = array('Employee_Id' 	=> $personalData['Employee_Id'],
																		'InsuranceType_Id' 	=> $insTypeId,
																		'Insurance_Type'	=>'insurance',
																		'Policy_No' 		=> $rec['Policy_No']);
															$insInsert = $this->_db->insert($this->_ehrTables->empInsurancePolicyNo,array_filter($insData));
														}
													}

													if (strtolower($this->_partnerId) === 'camu') 
													{
														/** Add employee info log */
														$logData = array('Employee_Id'=>$personalData['Employee_Id'], 'Action'=>'Add', 'Log_Timestamp'=>date('Y-m-d H:i:s'),'Camu_Push_Status'=>'Open');
														$this->_db->insert($this->_ehrTables->employeeInfoTimestampLog, $logData);
														$camuCreateStaff=$this->callCamuCreateApi($personalData['Employee_Id']);
														$this->callTrstScoreCreateApi($personalData['Employee_Id'],$jobData['Service_Provider_Id']);	
													}
													else{
														/** Add employee info log */
														$logData = array('Employee_Id'=>$personalData['Employee_Id'], 'Action'=>'Add', 'Log_Timestamp'=>date('Y-m-d H:i:s'));
														$this->_db->insert($this->_ehrTables->employeeInfoTimestampLog, $logData);
														$this->callTrstScoreCreateApi($personalData['Employee_Id'],$jobData['Service_Provider_Id']);
													}

													/* insert values into emp_user table */
													$empUserName = '';

													/** The User_Name will be email id if the email authentication is enabled.
														* The User_Name will be mobile no if the mobile number authentication is enabled*/
													if((int)$personalData['Allow_User_Signin'] === 1) {
														if((int)$personalData['Enable_Sign_In_With_Mobile_No'] === 1 ) {
															$empUserName = $personalData['Sign_In_Mobile_No_Country_Code'].$personalData['Sign_In_Mobile_Number'];
														} else {
															$empUserName = $jobData['Emp_Email'];
														}
													}
														
														$loginData = array(
																'Employee_Id' => $personalData['Employee_Id'],
																'User_Name'  => $empUserName ? $empUserName : '',
																'Created_Date'  => date('Y-m-d H:i:s')
														);
														
														/* Insert the login details in the emp_user table */
														$loginInsert = $this->_db->insert($this->_ehrTables->empLogin,$loginData);

														$employeeSalaryConfiguration = array('Employee_Id'         => $personalData['Employee_Id'],
																							'Eligible_For_Pension' => 1,
																							'Eligible_For_PT'      => 1,
																							'Added_On'             => date('Y-m-d H:i:s'),
																							'Added_By'             => $logEmpId);

														$employeeSalaryConfiguration = $this->_db->insert($this->_ehrTables->employeeSalaryConfiguration,$employeeSalaryConfiguration);
														
														if($loginInsert)
														{
															$biometricIntegrationIdExist = $this->_dbCommonFun->checkBiometricIntegrationIdExist($rec['External_EmpId'], 0);
															if(empty($biometricIntegrationIdExist)) {
																//if no ExternalEmpId exists
																$validUpdated = $this->_db->update($this->_ehrTables->employeeImport, array('Import_Status'=> 1), array('Emp_Import_Id = ?'=> $rec['Emp_Import_Id']));

																/** if Biometric Integration Id is valid then update the Biometric Integration Id in empjob table **/
																$this->_db->update($this->_ehrTables->empJob,array('External_EmpId'=>$rec['External_EmpId']),array('Employee_Id = ' . $personalData['Employee_Id']));
																$returnVal += 1;

																/* Push the new Employee ids to refresh the custom group list */
																array_push($newEmployeeIds,$personalData['Employee_Id']);
																
																/** Send invitation mail to the employee if the value of Send_Email_Invitation is true */
																if($rec['Send_Email_Invitation'] == '1') {
																
																	if(!empty($jobData['Emp_Email'])){

																		array_push($sendMailList, $jobData['Emp_Email']);

																	}											
																}
															}
															else {
																/** If Biometric Integration Id already exists in employee form then status will be updated as invalid record  **/
																$validUpdated = $this->_db->update($this->_ehrTables->employeeImport, array('Import_Status'=> -1), array('Emp_Import_Id = ?'=> $rec['Emp_Import_Id']));
																$this->deleteEmployee($personalData['Employee_Id'], $logEmpId, 1);
															}

														}
														else
														{
															/** If login data not inserted, status will be updated as invalid record  **/
															$validUpdated = $this->_db->update($this->_ehrTables->employeeImport, array('Import_Status'=> -1), array('Emp_Import_Id = ?'=> $rec['Emp_Import_Id']));
															$this->deleteEmployee($personalData['Employee_Id'], $logEmpId, 1);
														}
												}
												else
												{
													/** If contact data not inserted, status will be updated as invalid record  **/
													$validUpdated = $this->_db->update($this->_ehrTables->employeeImport, array('Import_Status'=> -1), array('Emp_Import_Id = ?'=> $rec['Emp_Import_Id']));
													$this->deleteEmployee($personalData['Employee_Id'], $logEmpId, 1);
												}
												
											}
											else
											{
												/** If location,designation,dep,workschedule,manager,emptype does not exists, status will be updated as invalid record  **/
												$validUpdated = $this->_db->update($this->_ehrTables->employeeImport, array('Import_Status'=> -1), array('Emp_Import_Id = ?'=> $rec['Emp_Import_Id']));
												$this->deleteEmployee($personalData['Employee_Id'], $logEmpId, 1);
											}
										}
										else
										{
											/** If job data not inserted, status will be updated as invalid record  **/
											$validUpdated = $this->_db->update($this->_ehrTables->employeeImport, array('Import_Status'=> -1), array('Emp_Import_Id = ?'=> $rec['Emp_Import_Id']));
											$this->deleteEmployee($personalData['Employee_Id'], $logEmpId, 1);
										}
									}
									else
									{
										/** If Email is already exist, status will be updated as invalid record  **/
										$validUpdated = $this->_db->update($this->_ehrTables->employeeImport, array('Import_Status'=> -1), array('Emp_Import_Id = ?'=> $rec['Emp_Import_Id']));
										$this->deleteEmployee($personalData['Employee_Id'], $logEmpId, 1);
									}
								}
								else
								{
									/** If personal data not inserted, status will be updated as invalid record  **/
									$validUpdated = $this->_db->update($this->_ehrTables->employeeImport, array('Import_Status'=> -1), array('Emp_Import_Id = ?'=> $rec['Emp_Import_Id']));
									$this->deleteEmployee($personalData['Employee_Id'], $logEmpId, 1);
								}
							}else{
								$validUpdated = $this->_db->update($this->_ehrTables->employeeImport, array('Import_Status'=> -1), array('Emp_Import_Id = ?'=> $rec['Emp_Import_Id']));
							}
                        }
                    }
                    else
                    {
                        $validUpdated = $this->_db->update($this->_ehrTables->employeeImport, array('Import_Status'=> 3), array('Emp_Import_Id = ?'=> $rec['Emp_Import_Id']));
                    }
				}

				/* If the employees are imported */
				if(!empty($newEmployeeIds) && count($newEmployeeIds) > 0){
					/* Refresh Custom Group Employees List */
					// $refreshCustomGroupList = $this->refreshCustomGroupEmployees($newEmployeeIds,$userIpAddress,$logEmpId,1);
					$refreshCustomGroupList = $this->callCustomGroupRefreshApi($newEmployeeIds,$logEmpId);
					

					/* If the employees is added in the custom group. But the leave balance is not updated for the custom group leave type */
					if(!empty($refreshCustomGroupList['isCustomGroupUpdated']) && empty($refreshCustomGroupList['isLeaveBalUpdated'])){
						$msg = $msg.' Leave Balance is not updated for the custom group leave type';
					}
				}
            }
            else
            { 
                return array('processedCount' => -2, 'sendMailList' => $sendMailList,'success' => false, 'msg'=>'There are no valid records present to process', 'type'=>'info');
            }
        
		return array('processedCount' => $returnVal,'sendMailList' => $sendMailList, 'success' => true, 'msg' => $msg, 'type'=>'info');
    }
    
	
	/**
     *
     * This function is to list imported employee details in a grid
     * 
     */
    public function searchImportedEmpDetails($page,$rows, $sortField, $sortOrder,$empFirstName,$empLastName,$managerFirstName,
											 $managerLastName, $designation,$department,$type,$status)
    {
    	$leafRecQry = $this->_db->select()->distinct()->from(array('Imp'=>$this->_ehrTables->employeeImport),
    			array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS Emp_Import_Id as count'),'Imp.Emp_Import_Id',
    			'Employee_Name'=>new Zend_Db_Expr("CONCAT(Imp.Emp_First_Name,' ',Imp.Emp_Last_Name)"),
    			'Man_First_Name'=>new Zend_Db_Expr("SUBSTRING_INDEX(SUBSTRING_INDEX(Manager_Name, ' ', 1), ' ', -1)"),
    			'Man_Last_Name'=>new Zend_Db_Expr("SUBSTRING_INDEX(SUBSTRING_INDEX(Manager_Name, ' ', 2), ' ', -1)"),
    					'Gender','Birth_Date',
    					'Nationality','Blood_Group', 'Languages_Known','Date_Of_Join','Emp_Status',
    					'Per_Street_Name', 'Per_City', 'Per_State', 'Per_Country', 'Per_Pincode',
    					/*'Curr_Street_Name',*/ /*'Curr_City',*/ /*'Curr_State',*/ /*'Curr_Country',*/ /*'Current_Pincode',*/
    					'Mobile_No','Education_Type' => 'Deg_Dep_School', 'Specialisation', 'Institute_Name',
    					'University', 'Year_Of_Passing', 'Percentage', 'Grade','External_EmpId',  //,'User_Name'
    					'Imp.Designation_Name', 'Imp.Department_Name', 'Imp.Location_Name','Imp.Manager_Name',
    					'Imp.Work_Schedule','Imp.Commission_Employee', 'Imp.Confirmation_Date','Imp.Import_Status',
						'Commission_Employee'=>new Zend_Db_Expr('CASE WHEN Imp.Commission_Employee = "1" THEN "Yes" ELSE "No" END'),
						'Import_Status'=>new Zend_Db_Expr('CASE WHEN Imp.Import_Status = "0" THEN "Unprocessed"
																WHEN Imp.Import_Status = "2" THEN "Manual Process"
																WHEN Imp.Import_Status = "3" THEN "Already Exist"
																WHEN Imp.Import_Status = "-1" THEN "Invalid Record"
															END'),
						'Curr_Street_Name'=>new Zend_Db_Expr('CASE WHEN Imp.Curr_Street_Name = "" THEN Imp.Per_Street_Name
															 else Imp.Curr_Street_Name
															END'),
						'Curr_City'=>new Zend_Db_Expr('CASE WHEN Imp.Curr_City = "" THEN Imp.Per_City
															 else Imp.Curr_City
															END'),
						'Curr_State'=>new Zend_Db_Expr('CASE WHEN Imp.Curr_State = "" THEN Imp.Per_State
															 else Imp.Curr_State
															END'),
						'Curr_Country'=>new Zend_Db_Expr('CASE WHEN Imp.Curr_Country = "" THEN Imp.Per_Country
															 else Imp.Curr_Country
															END'),
						'Current_Pincode'=>new Zend_Db_Expr('CASE WHEN Imp.Current_Pincode = "" THEN Imp.Per_Pincode
															 else Imp.Current_Pincode
															END')))
						
						->joinInner(array('m'=>$this->_ehrTables->maritalStatus), 'm.Marital_Status = Imp.Marital_Status', 'Marital_Status')
    					->joinLeft(array('Desi' => $this->_ehrTables->designation),'Imp.Designation_Name = Desi.Designation_Name','Designation_Id')
    					->joinLeft(array('Dept' => $this->_ehrTables->dept),'Imp.Department_Name = Dept.Department_Name','Department_Id')
    					->joinLeft(array('Loc' => $this->_ehrTables->location),'Imp.Location_Name = Loc.Location_Name','Location_Id')
    					->joinLeft(array('emp'=>$this->_ehrTables->empPersonal)," Imp.Manager_Name = CONCAT(emp.Emp_First_Name,' ',emp.Emp_Last_Name)",array('Manager_Id' => 'Employee_Id'))
    					->joinLeft(array('WS' => $this->_ehrTables->workSchedule),'Imp.Work_Schedule = WS.Title','WorkSchedule_Id')
    					->joinLeft(array('empType' => $this->_ehrTables->empType),'Imp.Employee_Type = empType.Employee_Type',array('EmpType_Id','Employee_Type'))
    					->joinLeft(array('pCountry'=>$this->_ehrTables->country),'Imp.Per_Country = pCountry.Country_Name',array('pCountry' => 'pCountry.Country_Code'))
    					->joinLeft(array('cCountry'=>$this->_ehrTables->country),'Imp.Curr_Country = cCountry.Country_Name',array('cCountry' => 'cCountry.Country_Code'))
    					->where('Import_Status != 1')
    					->limit($rows,($page-1)*$rows);
    	
    	if(!empty($empFirstName) && preg_match('/^[a-zA-Z]+$/', $empFirstName))
    	{
    		$leafRecQry->where('Imp.Emp_First_Name Like ?', "$empFirstName%");
    	}
    	if(!empty($empLastName) && preg_match('/^[a-zA-Z]+$/', $empLastName))
    	{
    		$leafRecQry->where('Imp.Emp_Last_Name Like ?', "$empLastName%");
    	}
    	if(!empty($managerFirstName) && preg_match('/^[a-zA-Z]+$/', $managerFirstName))
    	{
    		//$leafRecQry->where();
    		$leafRecQry->having('Man_First_Name Like ?', "$managerFirstName%");
    	}
    	if(!empty($managerLastName) && preg_match('/^[a-zA-Z]+$/', $managerLastName))
    	{
    		//$leafRecQry->where('Imp.Manager_Name Like ?', "%$managerLastName%");
    		$leafRecQry->having('Man_Last_Name Like ?', "$managerLastName%");
    	}
		if(!empty($department) && preg_match('/^[0-9]*$/', $department))
    	{
			$leafRecQry->having('Dept.Department_Id = ?', $department);
    	}
		if(!empty($designation) && preg_match('/^[0-9]*$/', $designation))
    	{
			$leafRecQry->having('Desi.Designation_Id = ?', $designation);
    	}
		if(!empty($type) && preg_match('/^[0-9]*$/', $type))
    	{
			$leafRecQry->having('empType.EmpType_Id = ?', $type);
    	}
		if(!empty($status) && preg_match('/^[a-zA-Z]*$/', $status))
    	{
			$leafRecQry->having('Imp.Emp_Status = ?', $status);
    	}
		
    	$srEmpresult = $this->_db->fetchAll($leafRecQry);
    	$rowCount = $this->_db->fetchOne('select FOUND_ROWS()');
    	$employeeList = array("total"=>$rowCount,"rows"=>$srEmpresult);
    	return Zend_Json::encode($employeeList);
    }
	
	/**
     * This function is to fetch employee leave reason (ESIC Reason)
     */
    public function getEmpLeaveReason()
    {
		return $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->esicReason, array('Reason_Id', 'ESIC_Reason'))
									  ->where('Form_Id = ?', 31)
									  ->group('Reason_Id')
									  ->order('ESIC_Reason ASC'));
    }
	
	
	/**
     * This function is to fetch bank account type
     */
    public function getBankAccountType()
    {
		return $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->accountType, array('Account_Type_Id', 'Account_Type'))
									  ->order('Account_Type ASC'));
    }

	/**
     * This function is to fetch global resource id
     */
    public function getGlobalResourceId()
    {
		return $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Global_Resource_Id'))
									  ->group('Global_Resource_Id')
									  ->order('Global_Resource_Id ASC'));
    }
	
	
	public function updateEmployeesAsDraft($formDataArr, $employeeId, $sessionId)						
	{
		/**
		 *	Update Employee Personal Information
		*/
		$personalData = $formData['personalDataArr'];
		
		if($employeeId > 0)
		{
			$updated = $this->_db->update ($this->_ehrTables->empPersonal, $personalData, 'Employee_Id = '.(int)$employeeId);	
		}
		else
		{
			$updated = $this->_db->insert($this->_ehrTables->empPersonal, $personalData);	
		}
		
		
		/**
		 *	update employee hobby details
		*/
		$isHobbiesExist = $this->_db->fetchOne($this->_db->select()
											   ->from($this->_ehrTables->empHobbies, new Zend_Db_Expr('Count(Employee_Id)'))
											   ->where('Employee_Id = ?', $employeeId));
		
		if ($isHobbiesExist)
		{
			if (array_key_exists('hobbyDataArr', $formData))
			{
				$updated = $this->_db->update ($this->_ehrTables->empHobbies, $formData['hobbyDataArr'], array('Employee_Id = '.$employeeId));
			}
			else
			{
				$updated = $this->_db->delete($this->_ehrTables->empHobbies, 'Employee_Id = '.(int)$employeeId);
			}
			
			if ($updated)
				++$isUpdated;
		}
		else
		{
			if (array_key_exists('hobbyDataArr', $formData))
			{
				/** Employee Id differs while add hobbies. So last inserted employee id in personal info table is used here **/
				$formData['hobbyDataArr']['Employee_Id'] = $employeeId;
				
				$updated = $this->_db->insert ($this->_ehrTables->empHobbies, $formData['hobbyDataArr']);
				
				if ($updated)
					++$isUpdated;
			}
		}
		
		/**
		 *	update employee languages known details
		*/
		$this->_db->delete($this->_ehrTables->empLang, 'Employee_Id='.(int)$employeeId);
		
		if (array_key_exists('languageDataArr', $formData))
		{
			$langKnown = $formData['languageDataArr'];
			
			if (!empty($langKnown) && count ($langKnown) > 0)
			{
				$langCount = count($langKnown);
				$langData = array();
				
				foreach ($langKnown as $key => $row)
				{
					$langData[$key] = array('Employee_Id' => $employeeId, 'Lang_Known' => $row);
				}
				
				if (!empty($langData))
				{
					$updated = $this->_ehrTables->insertMultiple ($this->_ehrTables->empLang, $langData);
					
					if ($updated)
						++$isUpdated;
				}
			}
		}        
	}

	/*get previous experience in months*/

	public function getPreviousExperienceInMonths($previousEmployeeExperienceYears,$previousEmployeeExperienceMonths)
	{
		if($previousEmployeeExperienceYears > 0 || $previousEmployeeExperienceMonths > 0)
		{
			$previousEmployeeExperience = ($previousEmployeeExperienceYears*12) + $previousEmployeeExperienceMonths;
		}
		else 
		{
			$previousEmployeeExperience = new Zend_Db_Expr('NULL');
		}

		return $previousEmployeeExperience;
	}

    public function getGradeByDesgination($desgId)
    {
       if(!empty($desgId)){
			$desgGrade =  $this->_db->fetchCol($this->_db->select()->from(array('des'=>$this->_ehrTables->designation),array())
                                        ->joinInner(array('gra'=>$this->_ehrTables->empGrade),'gra.Grade_Id=des.Grade_Id',
                                                    'ParentGrade_Id')
                                        
                                        ->where('des.Designation_Id IN (?)', $desgId));
			return $desgGrade;
	   } else{
			return array();
	   }
                                        
        
    }
    
	//Get Employee pan,uan and aadhaar details
	public function getEmpAdditionalPersonalDetais($employeeId)
	{
		return $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->empPersonal,array('PAN', 'Aadhaar_Card_Number', 'UAN'))
                                        ->where('Employee_Id = ?',$employeeId));
    }
	

	public function getCoursesList(){
		$courseDetailsQry = $this->_db->select()
								->from(array('c'=>$this->_ehrTables->courseDetails),array('c.Course_Id','c.Course_Name'));
        $courseDetails = $this->_db->fetchPairs($courseDetailsQry);
        return $courseDetails;
	}

	//Get employee id based on user defined employee id
	public function getEmployeeIdBasedOnUserEmpId($userDefinedEmpId)
	{
		return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob,array('Employee_Id'))
                                        ->where('User_Defined_EmpId = ?',$userDefinedEmpId));
	}
	
	//Get employee id and name based on user defined emp id --- leave and salary data import
	public function getUserDefinedEmployeeId($salaryCondition='',$userDetails=NULL, $employeeId = null)
    {
       $qryUserDefinedEmpId = $this->_db->select()->from(array('EP'=>$this->_ehrTables->empPersonal),
															  array(new Zend_Db_Expr("CONCAT(EP.Emp_First_Name, ' ',EP.Emp_Last_Name) as Employee_Name"),
																	'Marital_Status'))
             
			                            ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'EJ.Employee_Id=EP.Employee_Id',
                                                    array('User_Defined_EmpId','EJ.Employee_Id'))
                                        
										->where('EP.Form_Status = 1')
										->where('EJ.Emp_Status Like ?', 'Active');
		
		if($salaryCondition == "listSalaryAddedEmployee")
		{
			$getSalaryWageEmpId = $this->_db->fetchCol($this->_db->select()
                            ->union(array($this->_db->select()->from($this->_ehrTables->salary, array('Employee_Id')),
                            $this->_db->select()->from($this->_ehrTables->hourlyWages, array('Employee_Id')))));

			$qryUserDefinedEmpId->where('EJ.Employee_Id IN (?)', !empty($getSalaryWageEmpId)?$getSalaryWageEmpId:array(0));				
		}

		if(!empty($userDetails['Admin']))
		{
			$qryUserDefinedEmpId = $this->_dbCommonFun->formServiceProviderQuery($qryUserDefinedEmpId,'EJ.Service_Provider_Id',$userDetails['LogId']);
		}

		if(!empty($employeeId)) {
			$qryUserDefinedEmpId->where('EJ.Employee_Id IN (?)', $employeeId);				
		}
		
		return $this->_db->fetchAll($qryUserDefinedEmpId);	
    }
	
    public function getEmpProfession()
    {
        return $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->empProfession,array('Profession_Id','Profession_Name')));
    }
	
	/**
	 *	Clone Roles Designation Level
	*/
	public function cloneRoles ($designationIds, $currentDesigId, $sessionId)
	{
		foreach($designationIds as $key)
		{
			$deleteRoles = $this->_db->delete($this->_ehrTables->roles, 'Designation_Id = '.$key);
		}
		
		$roles = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->roles)
														  ->where('Designation_Id = ?', $currentDesigId));
		
		$count	=	count($roles);
		foreach($designationIds as $key)
		{
			for($i=0;$i<$count;$i++)
			{
				$roles[$i]['Designation_Id'] = $key;
			}
	
			$updated = $this->_ehrTables->insertMultiple($this->_ehrTables->roles, $roles);
		}
		
		if($updated)
		{
			//return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
			//												'action'         => 'Cloned',
			//												'trackingColumn' => $currentDesigId,
			//												'formName'       => 'Roles',
			//												'sessionId'      => $sessionId));
		
			return array('success' => true, 'msg'=>'Roles Cloned successfully','type'=>'success');
		}
		else
		{
			return array('success' => false, 'msg' => 'Unable to Clone Roles', 'type' => 'info');
		}
	}
	
	/**
	 *	Get employee names for multi select combo in clone roles employee level
	*/
	public function getEmployeeNames ($employeeId)
	{

		$qryEmployeeRoles= $this->_db->select()->from(array('A'=>$this->_ehrTables->empAccessRights),
															array('A.Employee_Id'))	
														->where('A.Form_Id=?','147')
														->where('A.Role_Optional_Choice =?','1');		
																		
		$getEmployeeRoles=$this->_db->fetchCol($qryEmployeeRoles);
		
		$qryEmployeePairs = $this->_db->select()
						   ->from(array('P'=>$this->_ehrTables->empPersonal),
								  array('P.Employee_Id','Employee_Name'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name)")))
					       ->joinLeft(array('J'=>$this->_ehrTables->empJob),'P.Employee_Id=J.Employee_Id ')
				           ->where('J.Emp_Status = ?', 'Active')
						   ->where('P.Employee_Id != ?', $employeeId);
		
		if(!empty($getEmployeeRoles))
		{
            $qryEmployeePairs->where('P.Employee_Id NOT IN (?)',$getEmployeeRoles);
		
		}				
		       
	      return $this->_db->fetchPairs($qryEmployeePairs);
	}
	
	/**
	 *	Clone Roles Employee Level
	*/
	public function cloneRolesEmployee ($employeeIds, $currentEmpId, $sessionId)
	{
		foreach($employeeIds as $key)
		{
			$deleteRoles = $this->_db->delete($this->_ehrTables->empAccessRights, 'Employee_Id = '.$key);
		}
		
		$roles = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->empAccessRights)
														  ->where('Employee_Id = ?', $currentEmpId));
		
		$count	=	count($roles);
		foreach($employeeIds as $key)
		{
			for($i=0;$i<$count;$i++)
			{
				$roles[$i]['Employee_Id'] = $key;
			}
	
			$updated = $this->_ehrTables->insertMultiple($this->_ehrTables->empAccessRights, $roles);
		}
		
		if($updated)
		{
			//return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
			//												'action'         => 'Cloned',
			//												'trackingColumn' => $currentEmpId,
			//												'formName'       => 'Roles',
			//												'sessionId'      => $sessionId));
		
			return array('success' => true, 'msg'=>'Roles Cloned successfully','type'=>'success');
		}
		else
		{
			return array('success' => false, 'msg' => 'Unable to Clone Roles', 'type' => 'info');
		}
	} 
	   

	public function departmentLevelAccess($parentdepartmentId, $currentEmpId, $sessionId)
	{    
		$dbDept           = new Organization_Model_DbTable_Department();
		$departmentDetail = $dbDept->getParentDepartmentNull();
		$departmentCount  = count($departmentDetail);
		$dbAccessRights = new Default_Model_DbTable_AccessRights();	
		$superAdminRole = $dbAccessRights->employeeAccessRights($currentEmpId, 'Super Admin');
	
		$superAdmin = $superAdminRole['Employee']['Optional_Choice'];
		$currentDepartmentCount = count($parentdepartmentId);
		
		if((!empty($superAdmin)&&($departmentCount==$currentDepartmentCount))||empty($superAdmin))
		{
			$dept=array();
			for($i=0;$i<$currentDepartmentCount;$i++)
			{
				$dept[$i]['Department_Id'] = $parentdepartmentId[$i] ;
				$dept[$i]['Employee_Id'] = $currentEmpId;
			}
			$isExistDepartment=$this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->departmentLevel,array('Department_Id'))
									->where('Employee_Id=?',$currentEmpId));
								
			for($i=0;$i<count($isExistDepartment);$i++)	
			{
				for($j=0;$j< count($dept);$j++ )
				{
						$whereCondition['Employee_Id = ?']  = $currentEmpId;
						$whereCondition['Department_Id = ?'] = $isExistDepartment[$i];
						$deleteDepartment = $this->_db->delete($this->_ehrTables->departmentLevel,$whereCondition);
				}
			}					  
			$updated = $this->_ehrTables->insertMultiple($this->_ehrTables->departmentLevel, $dept);
			if($updated)
			{
				return array('success' => true, 'msg'=>'Department Tagged successfully','type'=>'success');
			}
			else
			{
				return array('success' => false, 'msg' => 'Unable to Tag', 'type' => 'info');
			}
		}
		else 
		{
			return array('success' => false, 'msg' => 'For super admin role, all the department should be associated with department level access', 'type' => 'info');
		}
    } 

	  
	
	/** update employee photo **/
	public function updateEmpPhoto($photoPath, $employeeId)
	{
		if(!empty($employeeId))
		{			
			$formData['Photo_Path']=$photoPath;
			return $this->_db->update ($this->_ehrTables->empPersonal, $formData, array('Employee_Id = '.$employeeId));			
		}
		else
		{
			return 1;	
		}
	}
    public function getDivisionMaxEmployeeId($department)
	{
           $qryEmployee = $this->_db->select()->from(array('EP'=>$this->_ehrTables->empPersonal),
															  array(new Zend_Db_Expr('Max(EP.Employee_Id) as MaxId')))
             
			                            ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'EJ.Employee_Id=EP.Employee_Id',
                                                    array(''));
                                        
									
        //
           $IsDepParentId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->dept,array('Parent_Type_Id'))
                                                    ->where('Department_Id = ?',$department));
                
            /** If the filtered department is parent id **/
            if(empty($IsDepParentId))
            {
                /** get all the child department ids for the filtered parent department **/
                $getDepChildIds = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->dept,
                                                                   array('Department_Id'))
                                                            ->where('Parent_Type_Id = ?',$department));
                
                /** If child department exists **/
                if(!empty($getDepChildIds))
                {
                    /** include parent type id **/
                    array_push($getDepChildIds,$department);
                    $qryEmployee->where('EJ.Department_Id IN (?)',$getDepChildIds);
                }
                else
                {
                      $qryEmployee->where('EJ.Department_Id = ?', $department);
                }
            }
            
            $employeeId = $this->_db->fetchOne($qryEmployee);
     
    
           if(!empty($employeeId) && $employeeId > 0)
           {
                $employeeId = $employeeId+1;
                return $employeeId;
           }
           else
           {
                $maxEmployeeId = $this->getMaxEmployeeId ();
                $employeeId = $maxEmployeeId+1;
	            return $employeeId;
           }
	}
	
	/* Function to update the employee location,grade id in the salary details table */
	public function updateEmpLocGradeInSalary($updateArr, $employeeId, $formName, $sessionId){
		$dbPayslip = new Payroll_Model_DbTable_Payslip();
		$empsalaryType = $dbPayslip->getEmpSalaryType($employeeId);// get the employee salary type

		if($empsalaryType == 'Monthly'){
			$tableName = $this->_ehrTables->salary;
		}else{
			$tableName = $this->_ehrTables->hourlyWages;
		}

		$updateArr['Updated_On'] = date('Y-m-d H:i:s');
		$updateArr['Updated_By'] = $sessionId;

		$isSalaryExists = $this->_db->fetchOne($this->_db->select()->from($tableName, array('Employee_Id'))
								 ->where('Employee_Id = ?',$employeeId));
								 
		/* If salary exists update the location or grade id */
		if(!empty($isSalaryExists)){
			$updateEmpSalaryDetails = $this->_db->update($tableName,$updateArr,'Employee_Id ='.$employeeId);

			if(!empty($updateEmpSalaryDetails)){
				/* Insert this change in the system log table */
				$this->_dbCommonFun->updateResult (array('action' => 'update',
														'updated'    => $updateEmpSalaryDetails,
														'trackingMsg'    => 'Updated employee details in salary for the employee id-',
														'trackingColumn' => $employeeId,
														'formName'       => $formName,
														'sessionId'      => $sessionId));
			}

			return $updateEmpSalaryDetails;
		}else{
			return 1;
		}
	}
	/** Call the API from employee form, employee clone, employee import, employee onboard form, 
	 * to insert the new employees in all the custom group and update	the elgibility days for the custom group leave Type */
	public function refreshCustomGroupEmployees($newEmployeeIds,$userIpAddress,$loginEmpId,$isUpdateEligDaysForEmployees){
		$responseArray = array('isCustomGroupUpdated' => 0,
								'isLeaveBalUpdated' => 0);

		$orgCode = $this->_ehrTables->getOrgCode();

		/* Check the required fields are empty or not */
		if(!empty($newEmployeeIds) && !empty($loginEmpId) && !empty($orgCode)){
			$curl = curl_init();
			$method = "POST";
			$apiBaseUrl = Zend_Registry::get('atsBaseURL');  

			if(!empty($apiBaseUrl))
			{
				$url = $apiBaseUrl;
			}	
			else
			{
				curl_close($curl);
				
				return $responseArray;
			}

			/* Create an object */
			$refreshGroupVar = new \stdClass();

			$refreshGroupVar->employeeId = $newEmployeeIds;
			$refreshGroupVar->logInEmpId = (int)$loginEmpId;
			
			/* Convert the object to JSON */
			$refreshGroupVarJSON = json_encode($refreshGroupVar);

			$requestBody = '{
				"variables" : '.$refreshGroupVarJSON.',
				"query":"mutation CommentQuery($employeeId:[Int], $logInEmpId: Int!) { refreshCustomEmpGroups (employeeId:$employeeId, logInEmpId:$logInEmpId) { errorCode message }}"
			}';

			curl_setopt($curl, CURLOPT_POST, 1);
			curl_setopt($curl, CURLOPT_POSTFIELDS, $requestBody);
			curl_setopt($curl, CURLOPT_URL, $url);
			
			//Set API headers
			$apiHeaders = $this->_dbCommonFun->getApiHeaders($orgCode);
        	curl_setopt($curl, CURLOPT_HTTPHEADER, $apiHeaders);

			curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

			// EXECUTE:
			$result = curl_exec($curl);
			
			if(!$result)
			{
				return $responseArray;
			}

			curl_close($curl);

			//Handle API response
			$refreshCustomGroupResponseData = json_decode($result,true);
			if (!isset($refreshCustomGroupResponseData['data'])) {
				return $responseArray;
			}else{
				/* If the custom group refreshed */
				if(empty($refreshCustomGroupResponseData['data']['refreshCustomEmpGroups']['errorCode'])){
					$responseArray['isCustomGroupUpdated'] = 1;

					/* If the eligible days has to be updated for the leave type from employee form, employee import*/
					if(!empty($isUpdateEligDaysForEmployees)){
						/* Validate and update the leave balance for custom group leave type */
						$responseArray['isLeaveBalUpdated']	= $this->_dbLeave->validateUpdateCustomGroupLTEligibleDays('refreshCustomGroup',NULL,$newEmployeeIds);

						return $responseArray;
					}else{
						return $responseArray;
					}
				}else{
					return $responseArray;
				}
			}
		}else{
			return $responseArray;
		}
	}

	/** Get the employee inactive date, reason id */
	public function getJobEmpInactiveDetail($employeeId){
		return $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->empJob, array('Emp_InActive_Date','Reason_Id'))
				->where('Employee_Id =?', $employeeId));
	}

	/** Validate the DOJ is already used in the salary or attendance or leaves when the employee form is updated */
	public function validateEmployeeDojUsed($employeeId, $employeeDoj){
		$isEmployeeDojUsed = 0;

		/** validate the monthly salary exist or not for the employee*/
		$isMonthlySalaryExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->salary, array('Employee_Id'))
				->where('Employee_Id =?', $employeeId));
				
		/** If the monthly salary not exist validate the hourly salary exist or not for the employee */
		if(empty($isMonthlySalaryExist)){
			$isHourlySalaryExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->hourlyWages, array('Employee_Id'))
				->where('Employee_Id =?', $employeeId));
		}else{
			$isHourlySalaryExist = 0;
		}

		/** Validate the leave exist for an employee. Leave status should not be considered here as we have calculated the leave balance from this DOJ */
		$isLeaveExist = $this->_db->fetchOne($this->_db->select()->from(array('L'=>$this->_ehrTables->empLeaves),array(new Zend_Db_Expr('COUNT(L.Leave_Id)')))
									->where('L.Employee_Id = ?', $employeeId));

		/** Validate the leave balance is imported for the employee */
		$isLeaveBalanceExist = $this->_db->fetchOne($this->_db->select()->from(array('L'=>$this->_ehrTables->leaveImport),array(new Zend_Db_Expr('COUNT(L.Leave_Import_Id)')))
							->joinInner(array('EJ'=>$this->_ehrTables->empJob),'EJ.User_Defined_EmpId=L.User_Defined_EmpId',array(''))
							->where('EJ.Employee_Id = ?', $employeeId));

		/** Check the attendance exist for the employee with the below status except the 'Rejected' status */
		$attendanceExistQry = $this->_db->select()->from(array('A'=>$this->_ehrTables->attendance),array(new Zend_Db_Expr('COUNT(A.Attendance_Id)')))
								->where('Employee_Id = ?', $employeeId)
								->where('Approval_Status IN (?)',array('Draft','Applied','Approved','Returned'));
		$isAttendanceExist = $this->_db->fetchOne($attendanceExistQry);

		if($isMonthlySalaryExist || $isHourlySalaryExist || $isLeaveExist || $isAttendanceExist || $isLeaveBalanceExist){
			$isEmployeeDojUsed = 1;
		}else{
			$isEmployeeDojUsed = 0;
		}

		$responseArray = array('Is_Emp_Doj_Used' => $isEmployeeDojUsed);

		return $responseArray;
	}

	/** Function to return the enable rewards flag value from the redeem rewards details table. */
	public function getRedeemRewardsEnableFlag(){
		return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->redeemRewardsDetails, array('Enable_Rewards')));
	}

	/** function to return emplty value if the value is 'NULL' */
	public function fnCheckNull($value){
		return $value == 'NULL' ? "" : $value;
	}

	public function __destruct()
    {
        
    }

	//function to call camu api located at integration
	public function callCamuCreateApi($employeeId)
	{
		$orgCode = $this->_ehrTables->getOrgCode();
		if(!empty($employeeId))
		{
			$curl = curl_init();
			$method = "POST";
			$apiBaseUrl = Zend_Registry::get('integrationBaseURL');  

			if(!empty($apiBaseUrl))
			{
				$url = $apiBaseUrl;
			}	
			else
			{
				curl_close($curl);
				
				return "Failed";
			}

			/* Create an object */
			$refreshGroupVar = new \stdClass();

			$refreshGroupVar->employeeId = [(int)$employeeId];
			
			/* Convert the object to JSON */
			$refreshGroupVarJSON = json_encode($refreshGroupVar);

			$requestBody = '{
				"variables" : '.$refreshGroupVarJSON.',
				"query":"query CommentQuery($employeeId: [Int]!) { camuCreateStaff (employeeId:$employeeId) { errorCode message }}"
			}';

			curl_setopt($curl, CURLOPT_POST, 1);
			curl_setopt($curl, CURLOPT_POSTFIELDS, $requestBody);
			curl_setopt($curl, CURLOPT_URL, $url);
			
			//Set API headers
			$apiHeaders = $this->_dbCommonFun->getApiHeaders($orgCode);
        	curl_setopt($curl, CURLOPT_HTTPHEADER, $apiHeaders);

			curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

			// EXECUTE:
			$result = curl_exec($curl);
			if(!$result)
			{
				return "Failed";
			}

			curl_close($curl);
			return "Success";	
		}
		else{
			return "Failed";
		}
	}

	//function to call camu api located at integration
	public function callTrstScoreCreateApi($employeeId,$serviceProviderId)
	{
		$companyId=$this->getCompanyId($serviceProviderId);
		if(!empty($companyId))
		{
			$orgCode = $this->_ehrTables->getOrgCode();
			if(!empty($employeeId))
			{
				$curl = curl_init();
				$method = "POST";
				$apiBaseUrl = Zend_Registry::get('trstscoreBaseURL');  
	
				if(!empty($apiBaseUrl))
				{
					$url = $apiBaseUrl;
				}	
				else
				{
					curl_close($curl);
					return "Failed";
				}
	
				/* Create an object */
				$refreshGroupVar = new \stdClass();
	
				$refreshGroupVar->employeeId = (int)$employeeId;
				$refreshGroupVar->companyId = $companyId;
				
				/* Convert the object to JSON */
				$refreshGroupVarJSON = json_encode($refreshGroupVar);
	
				$requestBody = '{
					"variables" : '.$refreshGroupVarJSON.',
					"query":"query CommentQuery($employeeId:Int!,$companyId:String!) { addEmployee (employeeId:$employeeId,companyId:$companyId) { errorCode message }}"
				}';
	
				curl_setopt($curl, CURLOPT_POST, 1);
				curl_setopt($curl, CURLOPT_POSTFIELDS, $requestBody);
				curl_setopt($curl, CURLOPT_URL, $url);
				
				//Set API headers
				$apiHeaders = $this->_dbCommonFun->getApiHeaders($orgCode);
				curl_setopt($curl, CURLOPT_HTTPHEADER, $apiHeaders);
	
				curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
	
				// EXECUTE:
				$result = curl_exec($curl);
				curl_close($curl);
				if(!$result)
				{
					return "Failed";
				}
				return "Success";	
			}
			else{
				return "Failed";
			}
		}
		else{
			return 'Failed';
		}	
	}

	//function to get companyId
	public function getCompanyId($serviceProviderId)
	{
		$fieldForce = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->orgDetails, array('Field_Force')));
		if($fieldForce)
		{
			$companyId = $this->_db->fetchOne($this->_db->select()->from(array('TC'=>$this->_ehrTables->taxConfiguration),
			array('TCD.Company_Id'))
				->joinLeft(array('TCD'=>$this->_ehrTables->trstscoreContactDetails),'TC.Tax_Configuration_Id=TCD.Tax_Configuration_Id',array(''))
				->where('TC.Service_Provider_Id = ?',$serviceProviderId));
			return $companyId;
		}
		else{
			$companyId= $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->trstscoreContactDetails, array('Company_Id')));
			return $companyId;
		}
	}

	//fuction to call custom group refresh api
	public function callCustomGroupRefreshApi($employeeId,$logInEmpId)
	{
		$orgCode = $this->_ehrTables->getOrgCode();
		$curl = curl_init();
		$method = "POST";
		$apiBaseUrl = Zend_Registry::get('coreHrRoBaseUrl');
		if(!empty($apiBaseUrl))
		{
			$url = $apiBaseUrl;
		}	
		else
		{
			curl_close($curl);
			return "Failed";
		}
		/* Create an object */
		$refreshGroupVar = new \stdClass();
		$refreshGroupVar->employeeId = $employeeId;
		$refreshGroupVar->logInEmpId = (int)$logInEmpId;
		$refreshGroupVar->isCustomGroupRefresh=1;
		$refreshGroupVar->orgCode=$orgCode;
		
		/* Convert the object to JSON */
		$refreshGroupVarJSON = json_encode($refreshGroupVar);

		$requestBody = '{
			"variables" : '.$refreshGroupVarJSON.',
			"query":"query CommentQuery($employeeId:[Int],$logInEmpId:Int!,$isCustomGroupRefresh:Int,$orgCode:String!) { initiateRefreshCustomEmpGroups (employeeId:$employeeId,logInEmpId:$logInEmpId,isCustomGroupRefresh:$isCustomGroupRefresh,orgCode:$orgCode) { errorCode message }}"
		}';

		curl_setopt($curl, CURLOPT_POST, 1);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $requestBody);
		curl_setopt($curl, CURLOPT_URL, $url);
		
		//Set API headers
		$apiHeaders = $this->_dbCommonFun->getApiHeaders($orgCode);
		curl_setopt($curl, CURLOPT_HTTPHEADER, $apiHeaders);

		curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

		// EXECUTE:
		$result = curl_exec($curl);
		curl_close($curl);
		if(!$result)
		{
			return "Failed";
		}
		return "Success";
	}

	//Function to validate the employee job info is inactive or deleted while using it in employee import
	public function validateInActiveDeletedJobDetails($jobDetails){
		$inActiveDeletedJobDetailsArr = array();
		//Get designation status
		$designationStatus = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->designation, array('Designation_Status'))
							->where('Designation_Id = ?', $jobDetails['designationId']));
		if (empty($designationStatus) || $designationStatus == 'InActive') { 
			array_push($inActiveDeletedJobDetailsArr,'designation');
		}
		//Get employee type status
		$empTypeStatus = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empType, array('EmployeeType_Status'))
											->where('EmpType_Id = ?', $jobDetails['employeeTypeId']));
		if (empty($empTypeStatus) || $empTypeStatus == 'InActive') { 
			array_push($inActiveDeletedJobDetailsArr,'employee type');
		}
		//Get department status
		$departmentStatus = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->dept, array('Department_Status'))
							->where('Department_Id = ?', $jobDetails['departmentId']));
		if (empty($departmentStatus) || $departmentStatus == 'InActive') { 
			array_push($inActiveDeletedJobDetailsArr,'department');
		}
		//Get location status
		$locationStatus = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->location, array('Location_Status'))
											->where('Location_Id = ?', $jobDetails['locationId']));
		if (empty($locationStatus) || $locationStatus == 'InActive') { 
			array_push($inActiveDeletedJobDetailsArr,'location');
		}

		//Get work schedule status
		$workScheduleStatus = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->workSchedule, array('WorkSchedule_Status'))
											->where('WorkSchedule_Id = ?', $jobDetails['workScheduleId']));
		if (empty($workScheduleStatus) || $workScheduleStatus == 'InActive') { 
			array_push($inActiveDeletedJobDetailsArr,'work schedule');
		}
		return $inActiveDeletedJobDetailsArr;
	}
}

