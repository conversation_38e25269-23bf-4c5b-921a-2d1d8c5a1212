<?php
//=========================================================================================
//=========================================================================================
/* Program        : HrappMobile.php			 											 *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description    : HrappMobile file contains for all common functions for Mobile Apps   *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions      :                                                                    	 *
 *   Version         Date           Author                  Description                  *
 *     1.0        02-Feb-2015     Prasanth             Changed in file for mobile app    *
 *                                                                                    	 */
//=========================================================================================
//=========================================================================================
use Kreait\Firebase\Factory;
class Application_Model_DbTable_HrappMobile extends Zend_Db_Table_Abstract
{
	protected $_isMobile = null;
	protected $_basePath = null;
	protected $_ehrTables = null;
	protected $_isProduction = null;
	protected $_sessions = null;
	protected $_loginEmpUser = null;
	
	public function init()
	{
		$this->_basePath = new Zend_View_Helper_BaseUrl();
		$this->_ehrTables = new Application_Model_DbTable_Ehr();
		
		if (Zend_Registry::isRegistered('subHrapp'))
		{
			$this->_db = Zend_Registry::get('subHrapp');
			$this->_loginEmpUser = new Auth_Model_DbTable_EmpUser();
		}
		
		$this->_isProduction = Zend_Registry::get('Production');
	}
	
	/**
	 *	checkDevice function for check device type.
	*/
	public function checkDevice()
	{
		$isMobile = '';
		
		$session = Zend_Session::getId();
		
		$switchView = new Zend_Session_Namespace('Switch_View_'.$session);
		
		$isMobile = $switchView->Switch_View;
		
		$device = Zend_Registry::get('DeviceType');
		
		$mobile = ($device == 'Desktop') ? 0 : 1;
		
		$mobile = empty($isMobile) ? $mobile : ($isMobile == 'Desktop' ? 0 : 1);
		
		//if ($this->_isProduction == 0)
		//{
		//	//Development
		//	$ipAddress = $_SERVER['REMOTE_ADDR'];
		//	
		//	if ($ipAddress == '************') //Deepak
		//		$mobile = 0;
		//	
		//	if ($ipAddress == '************') // Prasanth
		//		$mobile = 0;
		//	
		//	if ($ipAddress == '***********')  //sudhev
		//		$mobile = 0;
		//}
		
		//$mobile = 0;
		
		$switchView->isMobile = $mobile;
		
		return $switchView->isMobile;
	}
	
	/**
	 *	createUserSession function used to concate user details and create new string
	*/
	public function createUserSession ($userId, $userName)
	{
		return base64_encode('sid:'.$userId);
	}
	
	/**
	 *	 getUserDetails used to get session user id and user name.
	 */
	public function getUserDetails ()
	{
		$userDetails = array();
		
		if(isset($_COOKIE['empUid'])) {
			$userDetails['logUserName'] = $_COOKIE['empUid'];
			$userDetails['logUserId'] = $this->_loginEmpUser->employeeId($userDetails['logUserName']);
			$userDetails['domain'] = $this->_ehrTables->getOrgCode();
		}

		return $userDetails;
	}

	/**
	 *	checkAuth function used to check authentication in APK
	*/
	public function checkAuth ()
	{
		if(isset($_COOKIE['empUid']) && (isset($_COOKIE['accessToken']) || isset($_COOKIE['refreshToken']))) {
			if(!empty($this->_loginEmpUser->employeeId($_COOKIE['empUid'])))
				return 1;
			else
				return 0;
        } else {
            return 0;
		}
	}
	
	public function checkSessionAuth ()
	{
		if (Zend_Registry::isRegistered('partnerid')) {
			$partnerid = Zend_Registry::get('partnerid');
		}else if (isset($_COOKIE['partnerid'])){
			$partnerid = $_COOKIE['partnerid'];
		}else{
			$partnerid = '-';
		}

		//If partner id is entomo
		if($partnerid == 'entomo'){
			$xAuthTokenInput = isset($_COOKIE['accessToken']) ? $_COOKIE['accessToken'] : '';
			$xRefreshTokenInput = isset($_COOKIE['refreshToken']) ? $_COOKIE['refreshToken'] : '';
			// This code is commented because we do not have the logic to get the user name in custom authorizer lambda.
			// So this code can be enabled after it is implemented in authorizer
			// $loginUserName = isset($_COOKIE['empUid']) ? $_COOKIE['empUid'] : ''; //user id in entomo
			$loginUserName = '';
			
			$entomoLoginDetails = [];
			//If x-auth-token exist
			if($xAuthTokenInput){
				$xAuthTokenExpiry = isset($_COOKIE['accessTokenExpiry']) ? date('Y-m-d H:i:s',strtotime($_COOKIE['accessTokenExpiry'])) : '';
				if($xAuthTokenExpiry){
					//If xAuthToken is expired
					if (strtotime($xAuthTokenExpiry) <=  time()) {
						return 0;
					}else{
						return 1;
					}
				}else{
					return 1;
				}
			}else{
				// If the refresh token exists get the x-auth token using refresh token
				// If the refresh token does not exist get the x-auth token from email
				$entomoLoginDetails = $this->getLoginDetailsUsingRefreshToken($xAuthTokenInput, $xRefreshTokenInput, $loginUserName);

				if($entomoLoginDetails && count($entomoLoginDetails) > 0 && isset($entomoLoginDetails['xAuthToken'])
				&& isset($entomoLoginDetails['xRefreshToken'])){
					if($loginResponse['isLoginApiCalled'] == 1){
						//Current UTC Date Time
						$dateTime = new DateTime('now', new DateTimeZone('UTC'));
						//Format the date and time
						$currentDateTimeUTC = $dateTime->format('Y-m-d H:i:s');
						//Add 90 days to the date-time.
						$refreshTokenExpiryDateTime = date('Y-m-d H:i:s',strtotime($currentDateTimeUTC.'+90 days'));
						//Convert datetime to timestamp
						$refreshTokenExpiryUtcTimestamp = strtotime($refreshTokenExpiryDateTime);
						/* Set new token and login user id in the cookie */
						setcookie('refreshToken',$entomoLoginDetails['xRefreshToken'],$refreshTokenExpiryUtcTimestamp,'/','', true);
						setcookie('empUid',$loginUserName,$refreshTokenExpiryUtcTimestamp,'/','', true);
					}
					/* Set new token in the cookie */
					setcookie('accessToken',$entomoLoginDetails['xAuthToken'],$refreshTokenExpiryUtcTimestamp,'/','', true); //Expiry = refresh token expiry
					return 1;
				}else{
					/** If the old token exist then the new token will take sometime to set in the cookie. 
					* So, set expiry time one hour ago to remove expired token from cookie. */
					setcookie("accessToken", "", time() - 3600, "/", '', true);
					setcookie("empUid", "", time() - 3600,"/",'', true);
					if(isset($loginResponse['isLoginApiCalled']) && $loginResponse['isLoginApiCalled'] == 1){
						setcookie("refreshToken", "", time() - 3600, "/", '', true);
					}
					return 0; 
				}
			}
		}else{
			/* if the access token exists in the cookie then validate the access token */
			if(isset($_COOKIE['accessToken'])) {
				try {
					//If the partner id is 'trulead'
					if($partnerid === 'trulead'){
						$factory = (new Factory)
						->withServiceAccount(APPLICATION_PATH.'/trulead_firebase_credentials.json');
					}else{
						$factory = (new Factory)
						->withServiceAccount(APPLICATION_PATH.'/firebase_credentials.json');
					}
					
					$auth = $factory->createAuth();
					
					try {
						/* Validate the id token. To verify that ID tokens are revoked, 
							use Auth::verifyIdToken() with the second parameter set to true. */
						$verifiedIdToken = $auth->verifyIdToken($_COOKIE['accessToken'], $checkIfRevoked = true);
						
						return 1;
					} catch (Exception $e) {
						/** If the old token exist then the new token will take sometime to set in the cookie. 
						* So, set expiry time one hour ago to remove expired token from cookie. */
						setcookie("accessToken", "", time() - 3600, "/", '', true);
						setcookie("empUid", "", time() - 3600,"/",'', true);
						return 0; 
					}
				} catch(Exception $e) {
					if (Zend_Registry::isRegistered('orgDetails') || Zend_Registry::isRegistered('Domain')) {
						$orgDetails = Zend_Registry::get('orgDetails');
						$isDomain     = Zend_Registry::get('Domain');
						/* Print the Exception only in demo instance if any error occurs*/
						if($orgDetails['Org_Code'] === 'infotech' || $orgDetails['Org_Code'] === 'demo' || $isDomain === 'hrapp.co.in') {
							print_r($e->getMessage());
							return 0;
						} else {
							return 1;
						}
					} else {
						return 1;
					}
				}
			} else if(isset($_COOKIE['refreshToken'])) { 
				/* If the access token does not exist then check the refresh token exits. 
				If the refresh token exist then generate id token using the refresh token*/
				try {
					$curl = curl_init();
					$method = "POST";

					$refreshTokenAPI = Zend_Registry::get('refreshTokenAPIUrl');
					$firebaseApiKey = Zend_Registry::get('firebaseApiKey');

					$url = $refreshTokenAPI.'token?key='.$firebaseApiKey;

					curl_setopt($curl, CURLOPT_POST, 1);
					curl_setopt($curl, CURLOPT_POSTFIELDS,
					"grant_type=refresh_token&refresh_token=".$_COOKIE['refreshToken']);
					curl_setopt($curl, CURLOPT_URL, $url);
					curl_setopt($curl, CURLOPT_HTTPHEADER, array('content-type:application/x-www-form-urlencoded' ));
					
					curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

					// EXECUTE:
					$result = curl_exec($curl);

					if(!$result)
					{
						return 0;
					}

					curl_close($curl);

					//Handle API response
					$responseData = json_decode($result,true);

					if($responseData && isset($responseData['id_token'])){
						/* Set new token in the cookie */
						setcookie('accessToken',$responseData['id_token'],time() +(3540),'/','', true); //Expiry = 59mins = 3540
						return 1;
					} else {
						return 0;
					}
				} catch(Exception $e) {
					if (Zend_Registry::isRegistered('orgDetails') || Zend_Registry::isRegistered('Domain')) {
						$orgDetails = Zend_Registry::get('orgDetails');
						$isDomain     = Zend_Registry::get('Domain');
						
						/* Print the Exception only in demo instance if any error occurs*/
						if($orgDetails['Org_Code'] === 'infotech' || $orgDetails['Org_Code'] === 'demo' || $isDomain === 'hrapp.co.in') {
							print_r($e->getMessage());
							return 0;
						} else {
							return 1;
						}
					} else {
						return 1;
					}
				}

			} else {
				/* return 0 if both access token and refresh token does not exist */
				return 0;
			}
		}
    }


	/**
	 *	updateDetailsForm function used to update data in zend form and
	 *	get data from zend form
	*/
	public function updateDetailsForm($formData, $formName, $layout, $isEncode = null)
	{
		if ($formData)
    	{
    		if ($this->checkDevice())//mobile
    		{
    		    $layout->disableLayout();
				
				if ($isEncode != 'No')
					$formData = json_decode($formData['formData'],true);
				
				$formName->populate($formData);
    			$formData = $formName->getValues();
    		}
    	}
    	return $formData;
	}
	
	/**
	 *	findMobile function used to set layout for mobile.
	*/
	public function findMobile($layout)
	{
		if ($this->checkDevice())//mobile
		{
			$layout->disableLayout();
			$layout->setLayout('touch_layout');
			return true;
		}
	}
	
	/**
	 *	alertDetails function used to send ajax call response in encoded format.
	*/
	public function alertDetails($access,$message)
	{
		if (!empty($access))
		{
			echo Zend_Json::encode(array('a'=>$this->access,'b'=> 'warning', 'c'=>'','d'=>350));
		}
		else
		{
			if ($message['b'] == "success")
			{
				echo Zend_Json::encode($message);
			}
			else if ($message['b'] == "fatal" || $message['b'] == "info" || $message['b'] == "warning")
			{
				echo Zend_Json::encode($message);
			}
		}
	}
	
	/**
	 *	alertDetails function used to send ajax call response in encoded format.
	*/
	public function alertDetailMsg($returnMsg, $showMessage, $access)
	{
		if (!empty($access))
		{
			echo Zend_Json::encode(array('a'=>$this->access,'b'=> 'warning', 'c'=>'','d'=>350));
		}	
		else 
		{	
			if ($returnMsg[0] == 'x')
			{
				echo Zend_Json::encode(array('a'=>$returnMsg[2],'b'=> 'success', 'c'=>'','d'=>300));
			}
			elseif($returnMsg[0] == 'y')
			{
				echo Zend_Json::encode(array('a'=>$returnMsg[1],'b'=> 'fatal', 'c'=>'errboxid','d'=>350));
			}
			elseif ( $returnMsg[0] == 'z' || $returnMsg[0] == 'k' || $returnMsg[0] == 'xz' )
			{
				echo Zend_Json::encode(array('a'=>$returnMsg[1],'b'=> 'warning', 'c'=>'warningboxid','d'=>355));
			}
			else
			{
				if (!empty($showMessage))
				{
					echo Zend_Json::encode(array('a'=>$showMessage,'b'=> 'warning', 'c'=>'warningboxid','d'=>535));
				}
			}
		}	
	}
	
	/**
	 *	To get Activity details for mobile touch select field
	 */
	public function getActivityName($projectId)
	{
		if (!empty($projectId))
		{
			return $this->_db->fetchAll($this->_db->select()->from(array('TA'=>$this->_ehrTables->timesheetActivity),array('Project_Activity_Id as value', 'AM.Activity_Name as text'))
								->joinInner(array('AM'=>$this->_ehrTables->activitiesMaster),'TA.Activity_Id = AM.Activity_Id', array())
								->where('Project_Id = ?', $projectId)
								->order('AM.Activity_Name ASC'));
		}
	}
	
	/**
	 *	getAllPaymentModes function used for get all payment modes
	 */
	public function getAllPaymentModes ()
	{
		return $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->payment, array('PaymentMode_Id as value', 'Payment_Type as text'))
				->order('Payment_Type ASC'));
	}
	
	/**
	 *	getTouchComboExpenseTypes function used to list all expense types in combo
	*/
	public function getTouchComboExpenseTypes()
	{	
		$expenseHead = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->expenseTypes, array('Expense_Id','Expense_Title','Max_Amount')));
		
		array_push($expenseHead, array('Expense_Id' =>'0','Expense_Title'=>'None of the above'));
		
		return Zend_Json::encode($expenseHead);
	}

	/* We should allow the user to access the Hrapp only if the email id is exists, emp status is active and the allow user signup flag is enabled */
	public function checkAllowUserSignin($email){
		
		$activeEmpDetails = $this->_db->fetchRow($this->_db->select()->from(array('EP'=>$this->_ehrTables->empPersonal),
			array('EP.Allow_User_Signin','EJ.Emp_Status','EJ.Employee_Id','EP.Enable_Sign_In_With_Mobile_No','EJ.Invitation_Status'))
			->joinInner(array('EJ'=>$this->_ehrTables->empJob), 'EP.Employee_Id = EJ.Employee_Id', array('Emp_Email'))
			->joinLeft(array('D'=>$this->_ehrTables->designation), 'EJ.Designation_Id=D.Designation_Id', array('D.Attendance_Enforced_GeoLocation'))
			->where('EJ.Emp_Status = ?', 'Active')
			->where('EJ.Emp_Email = ?', $email));
		if(!empty($activeEmpDetails)) {
			return $activeEmpDetails;
		} else {
			$empDetails = $this->_db->fetchRow($this->_db->select()->from(array('EP'=>$this->_ehrTables->empPersonal),
			array('EP.Allow_User_Signin','EJ.Emp_Status','EJ.Employee_Id','EP.Enable_Sign_In_With_Mobile_No','EJ.Invitation_Status'))
			->joinInner(array('EJ'=>$this->_ehrTables->empJob), 'EP.Employee_Id = EJ.Employee_Id', array('Emp_Email'))
			->joinLeft(array('D'=>$this->_ehrTables->designation), 'EJ.Designation_Id=D.Designation_Id', array('D.Attendance_Enforced_GeoLocation'))
			->where('EJ.Emp_Email = ?', $email));

			return $empDetails;
		}
	}
	
	/* We should allow the user to sign in with mobile number if emp status is active, the allow user signup and Enable_Sign_In_With_Mobile_No flag is enabled, */
	public function checkSigninWithMobileNumber($mobileNumber){
	
		$activeEmpDetails = $this->_db->fetchRow($this->_db->select()->from(array('EP'=>$this->_ehrTables->empPersonal),
			array('EP.Allow_User_Signin','EJ.Emp_Status','EJ.Employee_Id','EP.Enable_Sign_In_With_Mobile_No','CD.Mobile_No', 'EJ.Invitation_Status'))
			->joinInner(array('EJ'=>$this->_ehrTables->empJob), 'EP.Employee_Id = EJ.Employee_Id')
			->joinInner(array('CD'=>$this->_ehrTables->empContacts), 'CD.Employee_Id = EP.Employee_Id')
			->joinLeft(array('D'=>$this->_ehrTables->designation), 'EJ.Designation_Id=D.Designation_Id', array('D.Attendance_Enforced_GeoLocation'))
			->where('EJ.Emp_Status = ?', 'Active')
			->where('CD.Mobile_No = ?', $mobileNumber));

			if(!empty($activeEmpDetails)) {
				return $activeEmpDetails;
			} else {
				$empDetails =  $this->_db->fetchRow($this->_db->select()->from(array('EP'=>$this->_ehrTables->empPersonal),
					array('EP.Allow_User_Signin','EJ.Emp_Status','EJ.Employee_Id','EP.Enable_Sign_In_With_Mobile_No','CD.Mobile_No','EJ.Invitation_Status'))
					->joinInner(array('EJ'=>$this->_ehrTables->empJob), 'EP.Employee_Id = EJ.Employee_Id')
					->joinInner(array('CD'=>$this->_ehrTables->empContacts), 'CD.Employee_Id = EP.Employee_Id')
					->joinLeft(array('D'=>$this->_ehrTables->designation), 'EJ.Designation_Id=D.Designation_Id', array('D.Attendance_Enforced_GeoLocation'))
					->where('CD.Mobile_No = ?', $mobileNumber));
				
				return $empDetails;
			}
	}
	// function to get the organisation subscribed plans
	public function checkOrganisationSubscribedPlan($orgCode){
		$this->_appManagerDb = Zend_Registry::get('Hrapp');
		// get the Dashboard column from hrapp_plan_details table
		$orgSubscribedPlanDetails = $this->_appManagerDb->fetchOne($this->_appManagerDb->select()->from(array('RS'=>$this->_ehrTables->regUser),
		array('PD.Dashboard'))
		->joinInner(array('BR'=>$this->_ehrTables->billingRate),'BR.Billing_Id = RS.Billing_Id')
		->joinInner(array('PD'=>$this->_ehrTables->hrappPlanDetails),'PD.Plan_Id = BR.Plan_Id')
		->where('RS.Org_Code = ?',$orgCode));
		// return response back to function
		return $orgSubscribedPlanDetails;
	}

	// Function to get the productivity monitoring member status
	public function getEMMemberStatus($employeeId){
		$employeeMemberStatus = $this->_db->fetchOne($this->_db->select()->from(array('EM'=>$this->_ehrTables->emMembers),'EM.Member_Status')
														->where('EM.Employee_Id = ?', $employeeId));
		if(!empty($employeeMemberStatus)){
			return $employeeMemberStatus;
		}else{
			$dbAccessRights    = new Default_Model_DbTable_AccessRights();
			//Get the super admin form access for the employee id
			$employeeSuperAdminRole = $dbAccessRights->employeeAccessRights($employeeId, 'Super Admin');
			/** When the plan is switched from the HRMS dashboard to the Employee monitoring dashboard then the employee id which is used in the 
			 * registration will not be added to the member table. This logic is implemented only for handling customer queries. In this 
			 * situation, in order to allow the employee to log in, validate that the login employee is super admin or not. If super admin, return
			 * the member status as 'Active' and allow the employee to login.*/
			if(count($employeeSuperAdminRole) > 0 && (Int)$employeeSuperAdminRole['Employee']['Optional_Choice'] === 1){
				return 'Active';
			}else{
				return '';
			}
		}
	}

	//Function to get x-auth token and refresh token
	public function getLoginDetailsUsingRefreshToken($xAuthTokenInput, $xRefreshTokenInput, $loginUserName) {
		$loginResponse = [];

		// If refresh token exists
		if ($xAuthTokenInput && $xRefreshTokenInput) {
			$entomoGetRefreshTokenUrl = $_COOKIE['partnerbaseurl'].Zend_Registry::get('entomoRefreshTokenUrl');
			
			// Get the x-auth token
			$authTokens = $this->getAuthTokenUsingRefreshToken($entomoGetRefreshTokenUrl, $xAuthTokenInput, $xRefreshTokenInput);
			
			// If x-auth-token exists
			if ($authTokens && isset($authTokens['xAuthToken']) && isset($authTokens['xRefreshToken']) && isset($authTokens['xAuthTokenExpiry']) && isset($authTokens['xRefreshTokenExpiry'])) {
				$loginResponse['xAuthToken'] = $authTokens['xAuthToken'];
				$loginResponse['xRefreshToken'] = $authTokens['xRefreshToken'];
				$loginResponse['xAuthTokenExpiry'] = $authTokens['xAuthTokenExpiry'];
				$loginResponse['xRefreshTokenExpiry'] = $authTokens['xRefreshTokenExpiry'];
			}
		}
		if (!empty($loginResponse)) {
			return $loginResponse;
		} else {
			if($loginUserName){
				//If the refresh token does not exist or refresh token expired get the login details using the entomo user name
				$loginResponse = $this->getEntomoLoginDetailsFromEmail($loginUserName);
				$loginResponse['isLoginApiCalled'] = 1;
			}
			return $loginResponse;
		}
	}

	public function getAuthTokenUsingRefreshToken($requestUrl,$xAuthToken,$refreshToken){
		$accessTokenResponse = [];

		// Prepare request headers
		$headers = array(
			"X-REFRESH-TOKEN: " . $refreshToken,
			"X-AUTH-TOKEN: " . $xAuthToken
		);

		// Initialize cURL session
		$ch = curl_init();

		// Set cURL options
		curl_setopt($ch, CURLOPT_URL, $requestUrl);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_HEADER, true);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

		// Execute cURL request
		$response = curl_exec($ch);

		// Check if response is empty or not
		if (!empty($response)) {
			// Parse response headers
			$headerSize = curl_getinfo( $ch , CURLINFO_HEADER_SIZE );
			$headerStr = substr( $response , 0 , $headerSize );
			$bodyStr = substr( $response , $headerSize );
			$decodeBodyStr = json_decode($bodyStr,true);
			
			//If there is an error or token invalid return empty response
			if(isset($decodeBodyStr['error']) && !empty($decodeBodyStr['error'])){
				//Close cURL session after the headers are retrieved.Otherwise the headers will not be returned
				curl_close($ch);
				return $accessTokenResponse;
			}else{
				// convert headers to array
				$loginDetails = $this->responseHeadersToArray( $headerStr );
				// Check if required headers exist
				if (isset($loginDetails['X-AUTH-TOKEN']) && isset($loginDetails['X-REFRESH-TOKEN']) && isset($loginDetails['X-AUTH-TOKEN-EXPIRY']) && isset($loginDetails['X-REFRESH-TOKEN-EXPIRY'])) {
					$accessTokenResponse = array(
						'xAuthToken' => $loginDetails['X-AUTH-TOKEN'],
						'xRefreshToken' => $loginDetails['X-REFRESH-TOKEN'],
						'xAuthTokenExpiry' => $loginDetails['X-AUTH-TOKEN-EXPIRY'],
						'xRefreshTokenExpiry' => $loginDetails['X-REFRESH-TOKEN-EXPIRY']
					);
				}
				//Close cURL session after the headers are retrieved.Otherwise the headers will not be returned
				curl_close($ch);

				return $accessTokenResponse;
			}
		} else {
			//Close cURL session after the headers are retrieved.Otherwise the headers will not be returned
			curl_close($ch);

			// Return empty response in order to get the access token and x-auth-token
			return $accessTokenResponse;	
		}
	}

	//Function to get the entomo login details from email id
	public function getEntomoLoginDetailsFromEmail($loginUserName){
		$entomoClientId = Zend_Registry::get('EntomoClientId');
		$entomoClientSecret = Zend_Registry::get('EntomoClientSecret');

		if ($entomoClientId && $entomoClientSecret) {
			$entomoGetAccessTokenUrl = $_COOKIE['partnerbaseurl'].Zend_Registry::get('entomoAccessTokenUrl');
			
			//Get the access token using client id, client secret and entomo user name
			$entomoAccessTokenResponse = $this->getEntomoAccessToken($entomoGetAccessTokenUrl, $loginUserName, $entomoClientId, $entomoClientSecret);
			//If access token exist
			if($entomoAccessTokenResponse){
				$entomoAccessToken = $entomoAccessTokenResponse['entomoAccessToken'];

				//Get the x-auth-token and x-refresh-token
				$entomoGetLoginUrl = $_COOKIE['partnerbaseurl'].Zend_Registry::get('entomoLoginUrl');
				$entomoLoginDetails = $this->getEntomoLoginDetailsFromAccessToken($entomoGetLoginUrl, $entomoAccessToken);
				return $entomoLoginDetails;
			}else{
				return [];
			}
		} else {
			return [];
		}
	}

	//Function to get the access token using client id,secret and username
	public function getEntomoAccessToken($requestUrl,$loginUserName, $clientId,$clientSecret) {
		$requestBody = http_build_query(array(
			'clientId' => $clientId,
			'clientSecret' => $clientSecret,
			'username' => $loginUserName
		));

		// Initialize cURL session
		$ch = curl_init();

		// Set cURL options
		curl_setopt($ch, CURLOPT_URL, $requestUrl);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $requestBody);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_HTTPHEADER, array(
			'Content-Type: application/x-www-form-urlencoded'
		));

		// Execute cURL request
		$response = curl_exec($ch);

		// Close cURL session
		curl_close($ch);
		
		// Check if response is empty or not
		if (empty($response)) {
			return [];
		} else {
			// Parse response
			$responseData = json_decode($response, true);
			if(isset($responseData['error']) && !empty($responseData['error'])){
				return [];
			}else{
				// Check if accessToken exists
				if (isset($responseData['accessToken'])) {
					$accessTokenResponse = array(
						'entomoAccessToken' => $responseData['accessToken']
					);
					return $accessTokenResponse;
				} else {
					return [];
				}
			}
		}
	}

	//Function to login using entomo authorizer
	public function getEntomoLoginDetailsFromAccessToken($requestUrl,$accessToken){
		$requestBody = http_build_query(array(
			'accessToken' => $accessToken
		));

		// Initialize cURL session
		$ch = curl_init();

		// Set cURL options
		curl_setopt($ch, CURLOPT_URL, $requestUrl);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $requestBody);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_HTTPHEADER, array(
			'Content-Type: application/x-www-form-urlencoded'
		));
		curl_setopt($ch, CURLOPT_HEADER, true);
	
		// Execute cURL request
		$response = curl_exec($ch);

		// Check if response is empty or not
		if (!empty($response)) {
			// Parse response headers
			$headerSize = curl_getinfo( $ch , CURLINFO_HEADER_SIZE );
			$headerStr = substr( $response , 0 , $headerSize );
			$bodyStr = substr( $response , $headerSize );
			$decodeBodyStr = json_decode($bodyStr,true);
			
			//If there is an error return empty response
			if(isset($decodeBodyStr['error']) && !empty($decodeBodyStr['error'])){
				//Close cURL session after the headers are retrieved.Otherwise the headers will not be returned
				curl_close($ch);
				return [];
			}else{
				//convert response headers to array
				$loginDetails = $this->responseHeadersToArray( $headerStr );
				
				// Check if required headers exist
				if (isset($loginDetails['X-AUTH-TOKEN'], $loginDetails['X-REFRESH-TOKEN'], $loginDetails['X-AUTH-TOKEN-EXPIRY'], $loginDetails['X-REFRESH-TOKEN-EXPIRY'])) {
					$loginResponse = array(
						'xAuthToken' => $loginDetails['X-AUTH-TOKEN'],
						'xRefreshToken' => $loginDetails['X-REFRESH-TOKEN'],
						'xAuthTokenExpiry' => $loginDetails['X-AUTH-TOKEN-EXPIRY'],
						'xRefreshTokenExpiry' => $loginDetails['X-REFRESH-TOKEN-EXPIRY']
					);
				}
				//Close cURL session after the headers are retrieved.Otherwise the headers will not be returned
				curl_close($ch);
				//Return response
				return $loginResponse;
			}
		} else {
			//Close cURL session after the headers are retrieved.Otherwise the headers will not be returned
			curl_close($ch);
			//Return response
			return [];	
		}
	}

	//Function to get the header response from an API in array
	public function responseHeadersToArray( $str )
	{
		$headers = array();
		$headersTmpArray = explode( "\r\n" , $str );
		for ( $i = 0 ; $i < count( $headersTmpArray ) ; ++$i )
		{
			// we dont care about the two \r\n lines at the end of the headers
			if ( strlen( $headersTmpArray[$i] ) > 0 )
			{
				// the headers start with HTTP status codes, which do not contain a colon so we can filter them out too
				if ( strpos( $headersTmpArray[$i] , ":" ) )
				{
					$headerName = substr( $headersTmpArray[$i] , 0 , strpos( $headersTmpArray[$i] , ":" ) );
					$headerValue = substr( $headersTmpArray[$i] , strpos( $headersTmpArray[$i] , ":" )+1 );
					$headers[$headerName] = ltrim($headerValue);
				}
			}
		}
		return $headers;
	}

	public function __destruct()
    {
        
    }
}