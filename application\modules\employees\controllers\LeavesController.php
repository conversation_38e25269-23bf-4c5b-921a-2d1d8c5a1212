<?php
//=========================================================================================
//=========================================================================================
/* Program : LeavesController.php												         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : Organizations and companies struggle for hours in finding the total     *
 * number of leaves an employee has taken, to calculate his monthly salary and deduct    *
 * any amount if necessary. The leave types used for apply leaves are also maintained    *
 * here. The leave types are privilege, casual, sick, maternity etc. 					 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Shobana			      Initial Version        	         *
 *  0.2        04-Jan-2014    Mahesh                  Modified Actions                   *
 *                                                    1.encashLeavesAction               *
 *                                                    2.carryForwardAction               *
 *  0.3		   14-Apr -2014   Sandhosh                Modified Functions:                *
 *                                        		      addLeaveAction,updateLeaveAction   *
 *																						 *
 *  0.4        06-Jul- 2014   Mahesh 				  Modified mail coding and used      *                                                                              	
 *                                                    the common mail function           *
 *																						 *
 *  1.0        02-Feb-2015    Saranya                 Changed in file for mobile app     *
 *                                                                                       *
 *  1.5        16-Feb-2016    Suganya               Changed in file for Bootstrap        *
 *							  				               	     	                     */ 
//=========================================================================================
//=========================================================================================
include APPLICATION_PATH."/validations/Validations.php";
use PhpOffice\PhpSpreadsheet\Helper\Sample;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
class Employees_LeavesController extends Zend_Controller_Action
{
    protected $_dbLeave = null;
    protected $_dbPersonal = null;
    protected $_basePath = null;
    protected $_dbAccessRights = null;
    protected $_leaveAccessRights = null;
    protected $_approvalManagementAccessRights = null;
    protected $_leaveTypeAccessRights = null;
    protected $_freezeAccessRights  = null;
    protected $_dbFinancialYr = null;
    protected $_logEmpId = null;
    protected $_dbComment = null;
    protected $_dbInbox = null;
    protected $_dbManager = null;
    protected $_ehrTables = null;
    protected $_dbJobDetail = null;
    protected $_dbAttendance = null;
    protected $_orgDateFormat = null;
	protected $_dbEmployee = null;
    protected $_orgName = null;
    protected $_dbOrgSettings = null;
	protected $_leaveAccess = null;
	protected $_approvalManagementAccess = null;
	protected $_leaveTypeAccess = null;
	protected $_validation           = null;
	protected $_leaveEditAccess       = null;
	protected $_freezeAccess       = null;
	protected $_inboxName = 'Inbox';
	protected $_formNameA = 'Leaves';
    protected $_formNameB = 'Leave Types';
    protected $_formNameC = 'Leave Encashment';
    protected $_formNameD = 'Leave Freeze';
    protected $_formNameE = 'Leave Enforcement Configuration';
    protected $_formNameF = 'Approval Management';
    protected $_dbServiceLeave = null;
    protected $_dbQuaterLeave = null;
    protected $_dbMaternityLeave = null;
    protected $_dbQuarterWiseLeave = null;
    protected $_dbExperienceLeave = null;
    protected $_hrappMobile = null;
    protected $_orgDetails = null;
    protected $_dbLocation = null;
    protected $_dbWorkSchedule     = null;
    protected $_dbDept = null;
    protected $_leaveClosureDetails = null;
    protected $_leaveSettings = null;
    protected $_dbDesignation     =null;
    protected $_customFormNameA = null;
    protected $_myTeamAccess = null;
    protected $_selfServiceAccess = null;
    protected $_myTeamLeaveFormId = 332;
    protected $_selfServiceLeaveFormId = 333;
    public function init()
    {
        $this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
        // if ($this->_hrappMobile->checkAuth())
        // {
			$this->_dbCommonFun    = new Application_Model_DbTable_CommonFunction();
            $this->_basePath       = new Zend_View_Helper_BaseUrl();
            $this->_dbLeave        = new Employees_Model_DbTable_Leave();
            $this->_dbPersonal     = new Employees_Model_DbTable_Personal();
            $this->_dbAccessRights = new Default_Model_DbTable_AccessRights();
            $this->_dbJobDetail    = new Employees_Model_DbTable_JobDetail();
            $this->_dbComment      = new Payroll_Model_DbTable_PayrollComment();
            $this->_dbInbox        = new Employees_Model_DbTable_Inbox();
            $this->_dbManager      = new Default_Model_DbTable_Manager();
            $this->_dbFinancialYr  = new Default_Model_DbTable_FinancialYear();
            $this->_ehrTables      = new Application_Model_DbTable_Ehr();
            $this->_dbAttendance   = new Employees_Model_DbTable_Attendance();
            $this->_dbOrgSettings  = new Organization_Model_DbTable_OrgSettings();
            $this->_dbEmployee     = new Employees_Model_DbTable_Employee();
            $this->_dbServiceLeave = new Employees_Model_DbTable_ServiceBasedLeave();
            $this->_dbQuaterLeave = new Employees_Model_DbTable_QuarterWiseLeave();
            $this->_dbMaternityLeave = new Employees_Model_DbTable_MaternitySlabs();
            $this->_dbExperienceLeave = new Employees_Model_DbTable_ExperienceBasedLeave();
            $this->_dbLocation        = new Organization_Model_DbTable_Location();
            $this->_dbWorkSchedule      = new Organization_Model_DbTable_WorkSchedule();
            $this->_dbDept            = new Organization_Model_DbTable_Department();
            $this->_dbDesignation     = new Employees_Model_DbTable_Designation();
			$this->_validation 	   = new Validations();
			
			$userSession                   = $this->_dbCommonFun->getUserDetails ();
            $this->_logEmpId               = $userSession?$userSession['logUserId']:1;
			$this->_orgName                = $this->_ehrTables->organizationName();
            $this->_orgDateFormat          = $this->_ehrTables->orgDateformat();
			$this->_leaveAccessRights      = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameA);
            $this->_leaveTypeAccessRights  = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameB);
            $this->_encashmentAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameC);
    	    $this->_freezeAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameD);
            $this->_enforcementAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameE);
			$this->_approvalManagementAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameF);
            $this->_myTeamAccessRights      = $this->_dbAccessRights->employeeAccessRightsBasedOnFormId($this->_logEmpId,$this->_myTeamLeaveFormId);
            $this->_selfServiceAccessRights = $this->_dbAccessRights->employeeAccessRightsBasedOnFormId($this->_logEmpId,$this->_selfServiceLeaveFormId);

            $this->_myTeamAccess           = $this->_myTeamAccessRights['Employee'];
            $this->_selfServiceAccess      = $this->_selfServiceAccessRights['Employee'];
			
			$this->_leaveAccess            = $this->_leaveAccessRights['Employee'];
			$this->_leaveTypeAccess        = $this->_leaveTypeAccessRights['Employee'];
			$this->_freezeAccess           = $this->_freezeAccessRights['Employee'];
            $this->_enforcementAccess      = $this->_enforcementAccessRights['Employee'];
            $this->_approvalManagementAccess = $this->_approvalManagementAccessRights['Employee'];
          	$this->_leaveEditAccess        = $this->_dbLeave->editOption($this->_logEmpId);
            $this->_dbQuarterWiseLeave     = new Employees_Model_DbTable_QuarterWiseLeave();
            if (Zend_Registry::isRegistered('orgDetails'))
			    $this->_orgDetails = Zend_Registry::get('orgDetails');

            $this->_leaveClosureDetails =  $this->_dbLeave->getLeaveClosureAndLeaveEncashmentDetails();    

            $this->_leaveSettings = $this->_dbCommonFun->getLeaveSettings();
            
        // }
        // else
        // {
        //     if (Zend_Session::namespaceIsset('lastRequest'))
        //         Zend_Session:: namespaceUnset('lastRequest');
            
        //     $session = new Zend_Session_Namespace('lastRequest');
        //     $session->lastRequestUri = 'employees/leaves';
        //     $this->_redirect('auth');
        // }
    }

    public function indexAction()
    {
        $checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

        if ($checkSessionAuth)
        {
            $this->_helper->layout()->disableLayout()->setLayout('admin_layout');
		
            $this->view->formNameA       = $this->_formNameA;
            $this->view->formNameB       = $this->_formNameB;
            $this->view->formNameC       = $this->_formNameC;
            $this->view->formNameD       = $this->_formNameD;
            $this->view->formNameE       = $this->_formNameE;
            
            $this->view->customFormNameA = $this->_ehrTables->getCustomForms($this->_formNameA);
            $this->view->customFormNameB = $this->_ehrTables->getCustomForms($this->_formNameB);
            $this->view->customFormNameC = $this->_ehrTables->getCustomForms($this->_formNameC);
            $this->view->customFormNameD = $this->_ehrTables->getCustomForms($this->_formNameD);
            $this->view->customFormNameE = $this->_ehrTables->getCustomForms($this->_formNameE);
            
            $this->view->deptHierarchy   = $this->_dbDept->getDepartmentTypes();
            $this->view->leaveTypes                 = $this->_dbLeave->getLeaveType();
			$this->view->empLocation     = $this->_dbLocation->getLocationPair();
            $this->view->workSchedule = $this->_dbWorkSchedule-> getWorkSchedulePair();
            $this->view->leaveEnforcementTypes      = $this->_dbLeave->getLeaveEnforcementType();
            $this->view->monthTypes      = $this->_dbLeave->getMonths();
            $this->view->employeeDetails = $this->_dbCommonFun->listEmployeesDetails ('Leaves', '', $this->_logEmpId);
            $this->view->empESICReason   = $this->_dbEmployee->getEmpLeaveReason();

            $this->view->leaveSettings   = $this->_leaveSettings;
            $this->view->leaveClosureYear= $this->_dbLeave->getLeaveClosureYear();
            $dbGrade                     = new Employees_Model_DbTable_Grade();
            $this->view->leaveAccrualDetails= $this->_dbLeave->getEmpLeaveAccrualDetails();
            $this->view->entitlementLeaveExist= $this->_dbLeave->getEntitlementLeaveExist();
            
            if ($this->_leaveAccess['Optional_Choice'] == 1 )
            {
                /**
                 *	Leave Closure
                */
                $this->view->getLeaveClosureDetails   = $this->_leaveClosureDetails;
                $this->view->carryOverLeaves          = $this->_leaveClosureDetails['Carry_Over_Leaves'];
                
                /**
                 *	Leave Encashment
                */
                $this->view->autoEncashedLeave          = $this->_leaveClosureDetails['Auto_Encashed_Leaves'];
                $this->view->nonAutoEncashedLeave       = $this->_leaveClosureDetails['Non_Auto_Encashed_Leaves'];;
            }
            
            $this->view->leaveUser = array('Is_Manager'    => $this->_leaveAccess['Is_Manager'],
                                           'View'          => $this->_leaveAccess['View'],
                                           'Add'           => $this->_leaveAccess['Add'],
                                           'Update'        => $this->_leaveAccess['Update'],
                                           'Delete'        => $this->_leaveAccess['Delete'],
                                           'Op_Choice'     => $this->_leaveAccess['Optional_Choice'],
                                           'Admin'         => $this->_leaveAccessRights['Admin'],
                                           'Encashment'    => $this->_encashmentAccessRights['Employee']['Add'],
                                           'LE_IsManager'  => $this->_encashmentAccessRights['Employee']['Is_Manager'],
                                           'LE_IsAdmin'    => $this->_encashmentAccessRights['Admin'],
                                           'Session_Id'    => $this->_logEmpId,
                                           'LEdit'         => $this->_leaveEditAccess,
                                           'Employee_Name' => $this->_dbPersonal->employeeId($this->_logEmpId));
                                    
            $this->view->leaveEncashment = array('Is_Manager' => $this->_encashmentAccessRights ['Employee']['Is_Manager'],
                                           'View'       => $this->_encashmentAccessRights['Employee']['View'],
                                           'Add'        => $this->_encashmentAccessRights['Employee']['Add'],
                                           'Admin'      => $this->_encashmentAccessRights['Admin']);
    
            $this->view->leaveTypeUser = array('Is_Manager' => $this->_leaveTypeAccess['Is_Manager'],
                                               'View'       => $this->_leaveTypeAccess['View'],
                                               'Add'        => $this->_leaveTypeAccess['Add'],
                                               'Update'     => $this->_leaveTypeAccess['Update'],
                                               'Delete'     => $this->_leaveTypeAccess['Delete'],
                                               'Admin'      => $this->_leaveTypeAccessRights['Admin'],
                                               'Login_Emp_Id'    => $this->_logEmpId);
            
            $this->view->leaveFreezeUser = array('View'       => $this->_freezeAccess['View'],
                                               'Add'        => $this->_freezeAccess['Add'],
                                               'Update'     => $this->_freezeAccess['Update'],
                                               'Delete'     => $this->_freezeAccess['Delete']);
            
            $this->view->leaveEnforcementUser = array('View'       => $this->_enforcementAccess['View'],
                                               'Add'        => $this->_enforcementAccess['Add'],
                                               'Update'     => $this->_enforcementAccess['Update'],
                                               'Delete'     => $this->_enforcementAccess['Delete']);
                                               
            $this->view->approvalManagementUser = array('Is_Manager' => $this->_approvalManagementAccess['Is_Manager'],
                                                'View'       => $this->_approvalManagementAccess['View']);
                                               
            
            $this->view->dateformat = $this->_ehrTables->orgDateformat();
            
            $this->view->gradesPair = $dbGrade->getEmployeeAssoiciatedGrade();

            $this->view->serviceProvider = $this->_dbEmployee->getEmployeeServiceProviderList($this->_logEmpId);
            $this->view->managerNames = $this->_dbManager->managerName('', '', 'Emp_First_Name', 'ASC', '', '', '', '', '', 'Employees', 0);
            $this->view->orgDetails = $this->_orgDetails;
            $this->view->listMonth = $this->_dbCommonFun->getMonth();
            /** to print the employeejoinfrom & employeejointo month automatically in quarter wise leave form **/
            /** by this way we can use the function in index.phtml to display the month **/
            $this->view->getEmployeeJoinFromTo = $this->_dbQuarterWiseLeave->getEmployeeJoinFromTo();

            $this->view->designation     = $this->_dbDesignation->getDesignationPairs();
            $this->view->newImg          = $this->_basePath->baseUrl('images/new.png');
        } else {
			$this->_redirect('auth');
		}
    }
	
	/*************************************************** Leaves **********************************************************/
	
	/**
	 * Get leave details to show in a grid
	 */
    public function listLeavesAction()
    {
        $this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-leaves', 'json')->initContext();
			
            if ($this->_leaveAccess['View'] == 1 )
            {
                $sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
				
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$employeeName = $this->_getParam('Employee_Name', null);
				$employeeName = filter_var($employeeName, FILTER_SANITIZE_STRIPPED);
				
				$leaveName = $this->_getParam('Leave_Name', null);
				$leaveName = filter_var($leaveName, FILTER_SANITIZE_STRIPPED);
				
				$leaveStartDate = $this->_getParam('Leave_Start_Date', null);
				$leaveStartDate = filter_var($leaveStartDate, FILTER_SANITIZE_STRIPPED);
				
				$leaveEndDate = $this->_getParam('Leave_End_Date', null);
				$leaveEndDate = filter_var($leaveEndDate, FILTER_SANITIZE_STRIPPED);
                
                $lateAttendance = $this->_getParam('Late_Attendance', null);
                $lateAttendance = filter_var($lateAttendance, FILTER_SANITIZE_NUMBER_INT);

				$status = $this->_getParam('Status', null);
				$status = filter_var($status, FILTER_SANITIZE_STRIPPED);

                $serviceProviderId = $this->_getParam('Service_Provider_Id', null);
				$serviceProviderId = filter_var($serviceProviderId, FILTER_SANITIZE_NUMBER_INT);

                $location = $this->_getParam('Location',null);
                $location = filter_var($location, FILTER_SANITIZE_NUMBER_INT);

                $department = $this->_getParam('Department',null);
                $department = filter_var($department, FILTER_SANITIZE_NUMBER_INT);

                $designation = $this->_getParam('Designation',null);
                $designation = filter_var($designation, FILTER_SANITIZE_NUMBER_INT);

                $workSchedule = $this->_getParam('Work_Schedule',null);
                $workSchedule = filter_var($workSchedule, FILTER_SANITIZE_NUMBER_INT);
                
                $managerId = $this->_getParam('Manager_Id', null);
				$managerId = filter_var($managerId, FILTER_SANITIZE_NUMBER_INT);

                $earlyCheckoutLeave = $this->_getParam('Early_Checkout_Leave', null);
				$earlyCheckoutLeave = filter_var($earlyCheckoutLeave, FILTER_SANITIZE_NUMBER_INT);
				
				$leaveUser = array('Admin'      => $this->_leaveAccessRights['Admin'],
								   'Is_Manager' => $this->_leaveAccess['Is_Manager'],
								   'Update'     => $this->_leaveAccess['Update'],
								   'SessionId'  => $this->_logEmpId,
								   'Form_Name'  => $this->_formNameA);
				
                $searchArr = array('Employee_Name'    => $employeeName,
                                   'Leave_Name'       => $leaveName,
                                   'Late_Attendance'  => $lateAttendance,
								   'Status'           => $status,
								   'Leave_Start_Date' => $leaveStartDate,
								   'Leave_End_Date'   => $leaveEndDate,
                                   'Location_Id'      => $location,
                                   'Department_Id'    => $department,
                                   'Designation_Id'   => $designation,
                                   'Work_Schedule'    => $workSchedule,
                                   'Manager_Id'       => $managerId,
                                   'Service_Provider_Id' => $serviceProviderId,
                                   'Early_Checkout_Leave' => $earlyCheckoutLeave);
				
				$this->view->result = $this->_dbLeave->listLeaves ($page, $rows, $sortField, $sortOrder, $searchAll, $searchArr, $leaveUser);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }
	
	/**
	 *	to list available leave type for employee in Apply Encashment Form
	*/
    public function empAvailLeaveTypeAction()
    {
    	$this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('emp-avail-leave-type', 'json')->initContext();
			
    		$employeeId = $this->_getParam('Employee_Id', null);
    		$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
            
			$this->view->result = $this->_dbLeave->getEmpAvailLeaveType ($employeeId,'',$this->_logEmpId);
    	}
		else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }
	
	/**
	 *	Check Leave Type for show/hide reason drop down
	*/
	public function checkLeaveTypeAction()
    {
	    $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('check-leave-type', 'json')->initContext();
             
            $leaveTypeId = $this->_getParam('LeaveType_Id', null);
            $leaveTypeId = filter_var($leaveTypeId, FILTER_SANITIZE_NUMBER_INT);
			
			$employeeId = $this->_getParam('Employee_Id', null);
            $employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
            
            $this->view->result = $this->_dbLeave->checkEmpLeaveType ($leaveTypeId , $employeeId);			
		}
    }
	
	/**
	 *	setting the min date for datePicker based on the activation days for the leave type
	 *	In leave form, when choosing leave type, 
    */
    public function leaveTypeActivationDateAction()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('leave-type-activation-date', 'json')->initContext();
			
            $leaveTypeId = $this->_getParam('LeaveType_Id', null);
            $leaveTypeId = filter_var($leaveTypeId, FILTER_SANITIZE_NUMBER_INT);
			
            $employeeId = $this->_getParam('Employee_Id', null);
            $employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
            
			$this->view->result = $this->_dbLeave->getLeaveTypeActivationDate($employeeId, $leaveTypeId, $this->_logEmpId);
        }
    }
	
   
	/**
	 * Check leave frequency
	 */
    public function frequencyCheckAction()
    {
        $this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('frequency-check', 'json')->initContext();
            
			if ( $this->getRequest()->isPost() )
			{
				$formData = $this->getRequest()->getPost();
				
				$leaveId     = filter_var($formData['leaveId'], FILTER_SANITIZE_NUMBER_INT);
				$employeeId  = filter_var($formData['employeeId'], FILTER_SANITIZE_NUMBER_INT);
				$leaveTypeId = filter_var($formData['leaveTypeId'], FILTER_SANITIZE_NUMBER_INT);
				$leaveFrom   = filter_var($formData['leaveFrom'], FILTER_SANITIZE_STRIPPED);
                $leaveTo     = filter_var($formData['leaveTo'], FILTER_SANITIZE_STRIPPED);
                $leavePeriod = filter_var($formData['leavePeriod'], FILTER_SANITIZE_STRIPPED);
				$duration    = $this->_validation->amountValidation($formData['duration']);
				
                $validationDetails = array(
                    'leaveTypeId' => $leaveTypeId,
                    'leaveId' => $leaveId,
                    'leaveFrom' => $leaveFrom,
                    'leaveTo' => $leaveTo,
                    'employeeId' => $employeeId,
                    'duration' => $duration['value'],
                    'leavePeriod' => $leavePeriod
                );

                $result = $this->_dbLeave->validateEmployeeLeave($validationDetails,$formData['documentUpload']);
				$this->view->result = $result;
			}
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }
	
	/**
	 *	Add/Update Leave for that employee
	*/
    public function updateLeaveAction ()
    {
        $this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('update-leave', 'json')->initContext();
            
			$leaveId = $this->_getParam('Leave_Id', null);
			$leaveId = filter_var($leaveId, FILTER_SANITIZE_NUMBER_INT);
			$leaveId = $this->_validation->intValidation($leaveId);
			
			if ((empty($leaveId['value']) && $this->_leaveAccess['Add'] == 1 || $this->_myTeamAccess['Add'] == 1 || $this->_selfServiceAccess['Add'] == 1) ||
				(!empty($leaveId['value']) && $leaveId['valid'] && ($this->_leaveAccess['Update'] == 1 || $this->_myTeamAccess['Update'] == 1 || $this->_selfServiceAccess['Update'] == 1 || $this->_leaveEditAccess > 0)))
			{
				$action   = 'Add';
				$result   = array('success' => true);
				$leaveRec = '';
				
				if ($leaveId['value'] > 0)
				{
					$action   = 'Edit';
					$leaveRec = $this->_dbLeave->viewLeaveRequest ($leaveId['value']);
					
                    if(empty($this->_leaveAccessRights['Admin']))
                    {
                        if (!((($leaveRec['Employee_Id'] == $this->_logEmpId || $leaveRec['Added_By'] == $this->_logEmpId) &&
                            $leaveRec['Approval_Status'] == 'Returned') || (($leaveRec['Approver_Id'] == $this->_logEmpId ||
                                $leaveRec['Employee_Id'] == $this->_logEmpId || $leaveRec['Added_By'] == $this->_logEmpId) &&
                                ($leaveRec['Approval_Status'] == 'Applied' || $leaveRec['Approval_Status'] == 'Approved'))))
                        {
                            $result = array('success' => false, 'msg' => 'Sorry, Access denied', 'type' => 'warning');
                        }
                    }
				}
				
				if ($result['success'])
				{
					if ($this->getRequest()->isPost())
					{
						$formData = $this->getRequest()->getPost();
						
						$employeeId        = $this->_validation->intValidation($formData['Employee_Id']);
                        $isEmpFinalSettlementInitiated = $this->_dbCommonFun->empFinalSettlementInitiated($employeeId['value']);

                        if($isEmpFinalSettlementInitiated == 1){
                            $this->view->result = array('success' => false, 'msg' => 'Leave cannot be added or updated as the full and final settlement is initiated or settled for the employee.Kindly delete the F & F settlement in order to make the necessary modifications.', 'type' => 'warning');
                        }else{
						$forwardTo         = $this->_validation->intValidation($formData['Forward_To']);
						$leaveTypeId       = $this->_validation->intValidation($formData['LeaveType_Id']);
						$reasonType        = $this->_validation->intValidation($formData['Reason_Type']);
                        $reason            = $this->_validation->alphaNumSpCDotHySlashNewLineValidation($formData['Reason']);
						$reason['valid']   = $this->_validation->lengthValidation($reason,2, 600, true);
						$duration          = $this->_validation->amountValidation($formData['Duration']);
						$leavePeriod       = $this->_validation->alphaValidation($formData['Leave_Period']);
						$leaveFrom         = $this->_validation->dateValidation($formData['Leave_From']);
						$leaveTo           = $this->_validation->dateValidation($formData['Leave_To']);
						$totalDays         = $this->_validation->amountValidation($formData['Total_Days']);
						
                        if((empty($formData['Hours']) || empty($formData['Contact_No'])) && !empty($employeeId['value']))
                        {
                            $employeeDetails = $this->_dbCommonFun->getRegularHoursAndContactDetails($employeeId['value']); 
                            if(!empty($employeeDetails))
                            {
                                if(!empty($duration['value']))
                                {
                                    $formData['Hours'] = $duration['value']*$employeeDetails['Regular_Hours'];
                                }
                                $formData['Contact_No'] = $employeeDetails['Mobile_No'];
                            }
                        }

                        $hours             = $this->_validation->amountValidation($formData['Hours']);
						$contactNo         = $this->_validation->phoneValidation($formData['Contact_No']);
                        $contactNo['valid'] = $this->_validation->maxLengthValidation($contactNo, 15, true);
						$comments           = $this->_validation->alphaNumSpCDotHySlashNewLineValidation($formData['Comment']);
						$comments['valid']  = $this->_validation->lengthValidation($comments, 5, 3000, false);
                        $esicReason = array_key_exists("ESIC_Reason",$formData) ? (!empty($formData['ESIC_Reason']) ? $formData['ESIC_Reason']: "") : "";

						if (!empty($employeeId['value']) && $employeeId['valid'] && ($formData['Org_Enable_Workflow'] == "Yes" || (!empty($forwardTo['value']) && $forwardTo['valid'])) &&
							!empty($leaveTypeId['value']) && $leaveTypeId['valid'] && !empty($reason['value']) && $reason['valid'] &&
							!empty($duration['value']) && $duration['valid'] && (($duration['value'] == 1 && empty($leavePeriod['value'])) ||
							(($duration['value'] == 0.5 || $duration['value'] == 0.25) && !empty($leavePeriod['value']) && $leavePeriod['valid'])) &&
							!empty($leaveFrom['value']) && $leaveFrom['valid'] && !empty($leaveTo['value']) && $leaveTo['valid'] &&
							($leaveFrom['value'] <= $leaveTo['value']) && !empty($totalDays['value']) && $totalDays['valid'] && !empty($hours['value']) &&
							$hours['valid'] && 
                            ($comments['valid']  &&
                                (
                                    ($formData['Org_Enforce_Comment_For_Leave'] == "Yes" && !empty($comments['value'])) || 
                                    $formData['Org_Enforce_Comment_For_Leave'] == "No"
                                )
                            )&&!empty($contactNo['value']) && $contactNo['valid'])
						{
                                $alternatePersonDetails = (isset($formData['Alternate_Person']) && is_array($formData['Alternate_Person']))
                                                            ? array_filter($formData['Alternate_Person'])
                                                            : [];

                                if(($this->_leaveSettings['Enforce_Alternate_Person_For_Leave'] == "Yes" && (count($alternatePersonDetails) > 0)) || 
                                    ($this->_leaveSettings['Enforce_Alternate_Person_For_Leave'] == "No"))
                                {
                                    if($formData['Org_Enable_Workflow'] === "Yes") {
                                        $managerId = $this->_dbCommonFun->getManagerOfEmployee($employeeId['value']);
                                        if($managerId == 0 && empty($managerId)) {
                                            $adminIds = $this->_dbCommonFun->listAdmins();
                                            $forwardTo = $adminIds[0];
                                        } else {
                                            $forwardTo = $managerId;
                                        }
                                    } else {
                                        $forwardTo = $forwardTo['value'];
                                    }
                                    $leaveId         = $leaveId['value'];
                                    $employeeId      = $employeeId['value'];
                                    $leaveTypeId     = $leaveTypeId['value'];
                                    $reasonType      = $reasonType['value'];
                                    $leaveFrom       = $leaveFrom['value'];
                                    $leaveTo         = $leaveTo['value'];
                                    $duration        = $duration['value'];
                                    $alternatePerson = $alternatePersonDetails;
                                    $leavePeriod      = $leavePeriod['value'];
                                    
                                    $empAvailLeaveType = $this->_dbLeave->getEmpAvailLeaveType ($employeeId,'',$this->_logEmpId);
                                    $empCheckLeaveType = $this->_dbLeave->getLeaveTypeRow ($leaveTypeId);
                                    $empDOJ            = $this->_dbJobDetail->getDateOfJoin ($employeeId);
                                    $empDOJValid       = (($empDOJ <= $leaveFrom) && ($empDOJ <= $leaveTo));
                                    
                                    $leaveValidate     = $this->_dbLeave->leaveValidate ($leaveTypeId, $leaveId, $leaveFrom, $leaveTo, $employeeId, $duration, $leavePeriod);
                                    
                                    /** If leave freeze period exists in leave from and leave to. **/
                                    $checkLeaveFreeze  = $this->_dbLeave->checkLeaveFreeze ($leaveTypeId, $leaveFrom, $leaveTo);
                                    
                                    /** If advance notification is not empty, leave can be applied for future dates only. Otherwise leave can be applied 
                                     * for both future and past dates */
                                    $checkadvanceNotification = $this->_dbLeave->getLeaveTypeActivationDate($employeeId,$leaveTypeId,$this->_logEmpId);							
                                    $advanceNotificateMinDate = $checkadvanceNotification['futureLeaveStartDate'];
                                    $resignationDate 		  = $checkadvanceNotification['resignationDate'];
                                    $leaveActivationDate 	  = $checkadvanceNotification['activationDate'];
                                    $maxPayslipMnth 		  = $checkadvanceNotification['maxPayslipMnth'];
                                    
                                    /** check the coverage level leave types **/
                                    $empleaveType= array();
                                    foreach ($empAvailLeaveType as $key => $row){								
                                        array_push($empleaveType,$key);							
                                    }							
                                    if(in_array($leaveTypeId, $empleaveType)){
                                        $empAvailLeaveTypes = 1;
                                    }
                                    else{
                                        $empAvailLeaveTypes = 0;
                                    }
                                    
                                    if ($empCheckLeaveType['Leave_Type'] == "Paid Leave" || $empCheckLeaveType['Leave_Type']=="On Duty")
                                        $reasonType = 0;
                                    
                                    if ($leaveId > 0)
                                    {
                                        if ($leaveRec['Approval_Status'] == "Approved")
                                            $leaveTypeId = $leaveRec['LeaveType_Id'];
                                    }
                                    
                                    $isForwardToValid = false;
                                    if($formData['Org_Enable_Workflow'] == "Yes") {
                                        $isForwardToValid = true;
                                    } else {
                                        /** Get the approver details for the employee */
                                        $leaveApproverDetails = $this->_dbCommonFun->listApproverDetails ($employeeId, '', $this->_logEmpId,'leaves',1);
                                        $employeeValidApproverDet = $leaveApproverDetails['validApproverDetails'];
                                        if(!empty($employeeValidApproverDet)){
                                            $employeeValidApproverIds = array_column($employeeValidApproverDet,'Manager_Id');
                                        }else{
                                            $employeeValidApproverIds = array();
                                        }
                                        $isForwardToValid = in_array($forwardTo, $employeeValidApproverIds);
                                    }
        
                                    
                                    if ( $isForwardToValid && $empDOJValid && $leaveValidate['frequency']['startMsg'] == '' &&
                                        $leaveValidate['maxlimit']['endMsg'] == '' && $checkLeaveFreeze == 0 && !empty($empAvailLeaveTypes) &&
                                        ((($advanceNotificateMinDate != null && !empty($advanceNotificateMinDate)) && $leaveFrom >= $advanceNotificateMinDate
                                        && $leaveTo >= $advanceNotificateMinDate) || $advanceNotificateMinDate == null || empty($advanceNotificateMinDate)))
                                    {
                                        //leave from date leave end date should be less than the resignation date
                                        if(empty($resignationDate) || ( $leaveFrom <= $resignationDate && $leaveTo <= $resignationDate))
                                        {
                                            //leave from date leave end date should be greater than the last payslip month + 1
                                            if($leaveFrom >= $maxPayslipMnth && $leaveTo >= $maxPayslipMnth)
                                            {
                                                
                                                //checking whether attendance has been applied/drafted on leave dates
                                                $checkExists = $this->_dbAttendance->getAttendanceInLeaveRange ($employeeId, $leaveFrom, $leaveTo, $duration, $leavePeriod);
                                                
                                                if ($checkExists <= 0)
                                                {
                                                    $leaveData = array('Reason'           => $reason['value'],
                                                                    'Duration'         => $duration,
                                                                    'Start_Date'       => $leaveFrom,
                                                                    'End_Date'         => $leaveTo,
                                                                    'Total_Days'       => $totalDays['value'],
                                                                    'Hours'            => $hours['value'],
                                                                    'Contact_Details'  => $contactNo['value'],
                                                                    'Employee_Id'      => $employeeId,
                                                                    'Approver_Id'      => $forwardTo,
                                                                    'LeaveType_Id'     => $leaveTypeId,
                                                                    'Leave_Period'     => $leavePeriod,
                                                                    'Alternate_Person' => $alternatePerson,
                                                                    'Reason_Id'        => $reasonType,
                                                                    'Approval_Status'  => "Applied");
                                                    
                                                    if (!empty($leavePeriod['value']))
                                                    {
                                                        $leaveData['Leave_Period'] = $leavePeriod['value'];
                                                    }
                                                    
                                                    $customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
                                                    $customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
                                                    
                                                    $leaveClosureDetails = $this->_dbLeave->getLeaveClosureDetails($employeeId,$leaveTypeId);
                                                    
                                                    if($leaveData['Start_Date'] <= $leaveClosureDetails['finend'] && $leaveData['End_Date'] <= $leaveClosureDetails['finend']
                                                    && $leaveData['Start_Date'] <= $leaveClosureDetails['leaveEncashmentEndDate'] && $leaveData['End_Date'] <= $leaveClosureDetails['leaveEncashmentEndDate'])
                                                    {
                                                        // if leave documents is not empty we will consider the leave record has document(s)
                                                        // else we check if the employee has document(s) by calling the function checkEmpUploadFilesExists
                                                        if(!empty($formData['leaveDocuments']))
                                                        {
                                                            $uploadPathExists = 1;
                                                        }
                                                        else
                                                        {
                                                            $checkEmpUploadFilesExists = $this->_dbLeave->checkEmpUploadFilesExists($leaveId);
                                                            $uploadPathExists = !empty($checkEmpUploadFilesExists) ? 1 : 0;
                                                        }
        
                                                        $employeesDocumentUploadFilesArr = array();				
                                                        $successCount =0;
        
                                                        // if the leave type configuration for the document upload is disabled then we will allow the user to submit
                                                        if($formData['documentUpload'] == 0)
                                                        {
                                                            $successCount++;
                                                            $uploadPathExists ++;
                                                        }
                                                        // if the document upload in the leave type configuration is enabled and the total days is greater than or equal to the threshold limit and the employee has document(s) we will allow to submit
                                                        else if(($formData['documentUpload']==1 && $totalDays['value']>=$formData['maxDaysForDocumentUpload'] && $uploadPathExists >0 ) || ($formData['documentUpload']==1 && $totalDays['value'] < $formData['maxDaysForDocumentUpload'] ))
                                                        {
                                                            $successCount++;
                                                            if(!empty($formData['leaveDocuments']))
                                                            {
                                                                // pushing the leave documents for inserting it to DB
                                                                foreach($formData['leaveDocuments'] as $key=>$row)
                                                                {
                                                                    $employeesDocumentUploadFilesArr[$key]['File_Name'] = $row['Name'];
                                                                    $employeesDocumentUploadFilesArr[$key]['File_Size'] = $row['Size'];
                                                                }	
                                                            }						
                                                        }
        
                                                        // if the document upload in the leave type configuration is enabled and the total days is lesser than the threshold limit document upload is non-mandatory
                                                        if(!($uploadPathExists) && !($formData['documentUpload']==1 && $totalDays['value'] < $formData['maxDaysForDocumentUpload']))
                                                        {
                                                            $result = array('success' => false, 'msg' => 'Please upload the document(s) for the leave type', 'type' => 'info');
                                                        }
                                                        else if($successCount>0 && (($uploadPathExists > 0) || ($formData['documentUpload']==1 && $totalDays['value'] < $formData['maxDaysForDocumentUpload'])))
                                                        {
        
                                                            $leaveBalance = $leaveClosureDetails['Leave_Balance'];		
        
                                                            if($leaveBalance < $leaveData['Total_Days'])
                                                            {
                                                                $result = array('success' => false, 'msg' => 'Employee available leave balance is less than applied leave', 'type' => 'info');
                                                            }
                                                            else 
                                                            {
                                                                $result = $this->_dbLeave->updateLeave ($leaveData,$employeesDocumentUploadFilesArr, $this->_logEmpId, $this->_formNameA, $leaveId,
                                                                            $comments['value'], $leaveRec, $customFormNamee,$leaveBalance, $esicReason,'Yes');
                                                            }
        
                                                        }                                               
        
                                                        if ($result['success'] && empty($result['event_id']))
                                                        {
                                                            $mailCommunicationData = array(
                                                                'sessionId'         => $this->_logEmpId,
                                                                'employeeId'        => $employeeId,
                                                                'customFormName'    => $customFormNamee,
                                                                'moduleName'        => 'Employees',
                                                                'formName'          => $this->_formNameA,
                                                                'formUrl'           => '/employees/leaves',
                                                                'empIdToBeMailed'   => $forwardTo,
                                                                'action'            => $leaveId != 0 ? 'update' : 'add',
                                                                'alternatePerson'   => $alternatePerson,
                                                                'leaveFrom'         => date($this->_orgDateFormat['php'],strtotime($leaveFrom)),
                                                                'leaveTo'           => date($this->_orgDateFormat['php'],strtotime($leaveTo)),
                                                                'record'            => $leaveRec,
                                                            );
                                                            $result = $this->_dbCommonFun->addUpdateActionMailCommunication($mailCommunicationData);
                                                        }
                                                        
                                                        $this->view->result = $result;
                                                        
                                                    }
                                                    else
                                                    {
                                                        $this->view->result = array('success' => false, 'msg' => 'You cant apply leave until the carry over and encashment process has been done', 'type' => 'info');   
                                                    }
                                                }
                                                else
                                                {
                                                    $this->view->result = array('success' => false, 'msg' => 'You cannot apply leave because you have attendance or compensatory off added for this date', 'type' => 'info');
                                                }
                                            } else{
                                                $msg = 'Payslip is already generated for this month';
                                                $this->view->result = array('success' => false, 'msg' => $msg, 'type' => 'info');
                                            }
                                        } else{
                                                $msg = 'Leave start date should be less than or equal to resignation date';
                                                $this->view->result = array('success' => false, 'msg' => $msg, 'type' => 'info');
                                        }
                                    }
                                    else
                                    {
                                        if(empty($empAvailLeaveTypes)){
                                            $msg = 'The selected leave type is not applicable. Please try again by choosing another leave type';
                                        }else{
                                            if(!$isForwardToValid){
                                                $msg = "The 'forward to' employee is not valid. Please contact your HR Administrator";
                                            }else{
                                                if($checkLeaveFreeze){
                                                    $msg = 'The selected leave dates are frozen. Try again by choosing any other dates.';
                                                }else{
                                                    if (!empty($leaveValidate['frequency']['startMsg']) || !empty($leaveValidate['maxlimit']['endMsg'])){
                                                        $msg = $leaveValidate['frequency']['startMsg'] .' '.$leaveValidate['maxlimit']['endMsg'];
                                                    }else{
                                                        if (!$empDOJValid){
                                                            $msg = 'Leave cannot be applied as the leave date should be greater than the date of joining';
                                                        }else{
                                                            if (!IS_NULL($advanceNotificateMinDate) && !empty($advanceNotificateMinDate))
                                                            {
                                                                $msg = 'Leave start date should be greater than or equal to '.$advanceNotificateMinDate.' for this leave type';
                                                            }
                                                            else{
                                                                $msg = 'Something went wrong while validating the leave request. Please contact the system administrator';
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        
                                        $this->view->result = array('success' => false, 'msg' => $msg, 'type' => 'info');
                                    }
                                }
                                else
                                {
                                    $this->view->result = array('success' => false, 'msg' => 'alternate person is required', 'type' => 'info');
                                }
						}
						else
						{
							$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
						}
                        }
					}
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Sorry, Access denied', 'type' => 'warning');
				}
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Sorry, Access denied', 'type' => 'warning');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }

    public function getLeaveDocumentsAction()
    {
        $this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('get-leave-documents', 'json')->initContext();
            
            $leaveId = $this->_getParam('leaveId', null);
			$leaveId = filter_var($leaveId, FILTER_SANITIZE_NUMBER_INT);
            $leaveId = $this->_validation->intValidation($leaveId);
            
            if(!empty($leaveId['value']))
            {
                $getLeaveDocuments = $this->_dbLeave->getLeaveDocuments($leaveId['value']);

                if(!empty($getLeaveDocuments))
                {
                    $this->view->result = array('Document_File_Path' => $getLeaveDocuments);
                }
                else
                {
                    $this->view->result = array('Document_File_Path' => "");
                }
            }
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }
	
	/**
	 * Get Leave audit details to show in a grid
	 */
    public function listLeaveHistoryAction()
    {
        $this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-leave-history', 'json')->initContext();
            
			if ($this->_leaveAccess['View'] == 1 || $this->_myTeamAccess['View'] == 1 || $this->_selfServiceAccess['View'] == 1)
			{
				$employeeId = $this->_getParam('Employee_Id', $this->_logEmpId);
				$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
				
				if (!empty($employeeId) && $employeeId > 0)
				{
					$page = $this->_getParam('iDisplayStart', null);
					$page = isset($page) ? intval($page) : 0;
					$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
					
					$rows = $this->_getParam('iDisplayLength', null);
					$rows = isset($rows) ? intval($rows) : 10;
					$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
					
					$searchAll = $this->_getParam('sSearch', null);
					$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
					
					$historyArr = array('Employee_Id' => $employeeId);
					
					$this->view->result = $this->_dbLeave->empLeaveHistory ($page, $rows, $searchAll, $historyArr);
				}
			}
			else
			{
				$this->view->result = array("iTotalRecords" => 0, "iTotalDisplayRecords" => 0, "aaData" => '');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }
	
	/**
	 *	check whether carry over and leave encashment process is executed
	*/
    public function checkCarryOverAction()
    {
        $this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('check-carry-over', 'json')->initContext();
			
			$leaveExists   = $this->_dbLeave->leavetypeCount();
			
			if ($leaveExists)
			{
			    $this->view->result = array('success' => true);
                
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'You cant apply leave until leave type is added', 'type' => 'info');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }
	
	/**
	 *	to run the carry forward process
	*/
    public function carryForwardAction()
    {
        $this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('carry-forward', 'json')->initContext();
            
            if ($this->_leaveAccess['Optional_Choice'] == 1 || $this->_myTeamAccess['Optional_Choice'] == 1)
            {
                $leaveClosureDetails = $this->_leaveClosureDetails;
                if(!empty($leaveClosureDetails['Leave_Closure_Exist'])){
                    /** If the leave encashment is run for the current leave year*/
                    if(empty($leaveClosureDetails['Leave_Encashment_Exist'])){
                        //get the pending leave request
                        $pendingLeaveRequest = $this->_dbLeave->getPendingLeaveRequest($leaveClosureDetails);
                        $coleaves      = $leaveClosureDetails['Carry_Over_Leaves'];
                        //if no carry over leave types exist
                        if (count($coleaves) == 0)
                        {
                            
                            if ($pendingLeaveRequest > 0)
                            {
                                $this->view->result = array('success' => false, 'msg' => 'Please update the pending leave request', 'type' => 'warning');
                            }
                            else
                            {
                                $this->view->result = $this->_dbLeave->updateCOLeaveYear($this->_logEmpId,$leaveClosureDetails);
                            }
                        }
                        else
                        {
                            if ($pendingLeaveRequest > 0)
                            {
                                $this->view->result = array('success' => false, 'msg' => 'Please update the pending leave request', 'type' => 'warning');
                            }
                            else
                            {
                                $this->view->result = $this->_dbLeave->empLeaveCarryOver($this->_logEmpId,$leaveClosureDetails);
                            }
                        }
                    }else{
                        $this->view->result = array('success' => false, 'msg' => 'Please run the leave encashment closure process before the leave closure process', 'type' => 'warning');
                    }
                }else{
                    $this->view->result = array('success' => false, 'msg' => 'Something went wrong while processing the leave closure process. Please contact system administrator', 'type' => 'warning');
                }
            }else{
                $this->view->result = array('success' => false, 'msg' => "Sorry, you don't have rights to run the leave closure process. Please contact system administrator", 'type' => 'warning');
            }
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }
	
	/**
	 *	to load the grid with the selected year
	*/
    public function carryOverAction()
    {
        $this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('carry-over', 'json')->initContext();
            
			if ($this->_leaveAccess['View'] == 1 || $this->_myTeamAccess['View'] == 1)
			{
				$coleave = $this->_getParam('coleavetype', null);
				$coleave = filter_var($coleave, FILTER_SANITIZE_NUMBER_INT);
				
				if (!empty($coleave))
				{
					$page = $this->_getParam('iDisplayStart', null);
					$page = isset($page) ? intval($page) : 0;
					$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
					
					$rows = $this->_getParam('iDisplayLength', null);
					$rows = isset($rows) ? intval($rows) : 10;
					$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
					
					$searchAll = $this->_getParam('sSearch', null);
					$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
					
					$searchArr = array ('CarryOver_Leave' => $coleave);
					$leaveClosureDetails = $this->_leaveClosureDetails;
					$this->view->result = $this->_dbLeave->carryoverLeave ($page, $rows,$searchAll, $searchArr,$leaveClosureDetails);
				}else{
                    return array("iTotalRecords" => 0, "iTotalDisplayRecords" => 0, "aaData" => array());
                }
			}else{
                return array("iTotalRecords" => 0, "iTotalDisplayRecords" => 0, "aaData" => array());
            }
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }
	
/**
 *	to run the encashment process
*/
public function encashLeavesAction()
{
    $this->_helper->layout()->disableLayout();
    
    if (isset($_SERVER['HTTP_REFERER']))
    {
        $ajaxContext = $this->_helper->getHelper('AjaxContext');
        $ajaxContext->addActionContext('encash-leaves', 'json')->initContext();
        
        if ($this->_leaveAccess['Optional_Choice'] == 1  || $this->_myTeamAccess['Optional_Choice'] == 1)
        {
            $leaveClosureDetails = $this->_leaveClosureDetails;
            if(!empty($leaveClosureDetails['Leave_Encashment_Exist'])){
                //get the pending leave request
                $pendingLeaveRequest = $this->_dbLeave->getPendingLeaveRequest($leaveClosureDetails);
                $autoEncashedLeaves = $leaveClosureDetails['Auto_Encashed_Leaves'];
                //added the below condition to update the encashment year in leavetype table eventhough there is no encashment leavetype
                if (count($autoEncashedLeaves) == 0)
                {
                    if ($pendingLeaveRequest > 0)
                    {
                        $this->view->result = array('success' => false, 'msg' => 'Please update the pending leave request', 'type' => 'danger');
                    }
                    else
                    {
                        //leave type table will be updated with the current year
                        $this->view->result = $this->_dbLeave->updateLeaveEncashmentYear($this->_logEmpId,$leaveClosureDetails);
                    }
                }
                else
                {
                    if ($pendingLeaveRequest > 0)
                    {
                        $this->view->result = array('success' => false, 'msg' => 'Please update the pending leave request', 'type' => 'warning');
                    }
                    else
                    {
                        $this->view->result = $this->_dbLeave->empLeaveEncashment ($this->_logEmpId,$leaveClosureDetails);
                    }
                }
            }else{
                $this->view->result = array('success' => false, 'msg' => 'Something went wrong while processing the leave encashment process. Please contact system administrator', 'type' => 'warning');
            }
        }else{
            $this->view->result = array('success' => false, 'msg' => "Sorry, you don't have rights to run the leave encashment process. Please contact system administrator", 'type' => 'warning');
        }
    }
    else
    {
        $this->_helper->redirector('index', 'leaves', 'employees');
    }
}
//List the encashmet Leaves in grid to do
public function listEncashmentLeavesAction()
{
    $this->_helper->layout()->disableLayout();
    
    if (isset($_SERVER['HTTP_REFERER']))
    {
        $ajaxContext = $this->_helper->getHelper('AjaxContext');
        $ajaxContext->addActionContext('list-encashment-leaves', 'json')->initContext();
        
        if ($this->_encashmentAccessRights['Employee']['View'] == 1 )
        {	
            $sortField = $this->_getParam('iSortCol_0', null);
            $sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
            
            $sortOrder = $this->_getParam('sSortDir_0', null);
            $sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
            
            $page = $this->_getParam('iDisplayStart', null);
            $page = isset($page) ? intval($page) : 0;
            $page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
            
            $rows = $this->_getParam('iDisplayLength', null);
            $rows = isset($rows) ? intval($rows) : 10;
            $rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
            
            $searchAll = $this->_getParam('sSearch', null);
            $searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
            
            $empName = $this->_getParam('Employee_Name', null);
            $empName = filter_var($empName, FILTER_SANITIZE_STRIPPED);
            
            $leaveType = $this->_getParam('Leave_Type', null);
            $leaveType = filter_var($leaveType, FILTER_SANITIZE_STRIPPED);
            
            $encashStartDate = $this->_getParam('Encash_Start_Date', null);
            $encashStartDate = filter_var($encashStartDate, FILTER_SANITIZE_STRIPPED);
            
            $encashEndDate = $this->_getParam('Encash_End_Date', null);
            $encashEndDate = filter_var($encashEndDate, FILTER_SANITIZE_STRIPPED);
            
            $leaveEncashmentDate= $this->_getParam('Encashed_Date', null);
            $leaveEncashmentDate = filter_var($leaveEncashmentDate, FILTER_SANITIZE_STRIPPED);
            
            $leaveEncashmentStatus = $this->_getParam('Encashed_Status', null);
            $leaveEncashmentStatus = filter_var($leaveEncashmentStatus, FILTER_SANITIZE_STRIPPED);
            
            $searchArr = array('Employee_Name'      => $empName,
                                'Leave_Type'        => $leaveType,
                                'Encash_Start_Date' => $encashStartDate,
                                'Encash_End_Date'   => $encashEndDate,
                                'Encashed_Status'   => $leaveEncashmentStatus
                            );
                               
            $leaveEncashmentAccess = array(  'Is_Manager' => $this->_encashmentAccessRights ['Employee']['Is_Manager'],
                                            'View'       => $this->_encashmentAccessRights['Employee']['View'],
                                            'Add'        => $this->_encashmentAccessRights['Employee']['Add'],
                                            'Admin'      => $this->_encashmentAccessRights['Admin'],
                                            'SessionId'  => $this->_logEmpId,
                                            'Form_Name'  => $this->_formNameC);
           
            $this->view->result = $this->_dbLeave->listLeaveEncashment($page, $rows, $sortField, $sortOrder, $searchAll, $searchArr, $leaveEncashmentAccess);
        }
    }
    else
    {
        $this->_helper->redirector('index', 'leaves', 'employees');
    }
}
    /**
	 *	to load the encashment grid
	*/
    public function listEncashmentsAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-encashments', 'json')->initContext();
			
            $leleave = $this->_getParam('leleavetype', null);
            $leleave = filter_var($leleave, FILTER_SANITIZE_NUMBER_INT);
			
            if (!empty($leleave) && ($this->_leaveAccess['View']==1 || $this->_myTeamAccess['View'] == 1))
            {
                
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
				
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$encashmentArr = array('Leave_Type'      => $leleave);
				$leaveClosureDetails = $this->_leaveClosureDetails;
				$this->view->result = $this->_dbLeave->encashedLeaves ($page, $rows,$searchAll, $encashmentArr,$leaveClosureDetails);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }
	
    /**
	*	to apply leave encashment
    */
    public function applyEncashmentAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('apply-encashment', 'json')->initContext();
			
            if (($this->_encashmentAccessRights['Employee']['Add'] == 1||
                 $this->_encashmentAccessRights['Employee']['Is_Manager'] == 1 || $this->_encashmentAccessRights['Admin'] == 'admin'))
			{
				if ( $this->getRequest()->isPost() )
				{
					$formData = $this->getRequest()->getPost();
					
					$employeeId     = $this->_validation->intValidation($formData['Employee_Id']);
					$leaveType      = $this->_validation->intValidation($formData['Leave_Type']);
					$encashmentDays = $this->_validation->daysValidation($formData['Encashment_Days']);
					
					if (!empty($employeeId['value']) && $employeeId['valid'] && !empty($leaveType['value']) && $leaveType['valid'] &&
						!empty($encashmentDays['value']) && $encashmentDays['valid'])
					{
						$employeeId     = $employeeId['value'];
						$leaveType      = $leaveType['value'];
                        $encashmentDays = $encashmentDays['value'];

                        $historyArr = array('Employee_Id' => $employeeId,
                                            'LeaveType_Id' => $leaveType);

                        $availEncahDays = $this->_dbLeave->empLeaveHistory(NULL,NULL,NULL, $historyArr,'listEmpEncashmentLimit');

                        if ($availEncahDays[0]['Emp_Encashment_Remaining_Eligible_Days'] > 0 && $encashmentDays <= $availEncahDays[0]['Emp_Encashment_Remaining_Eligible_Days'])
                        {						
							$encashmentDetails = array('LeaveType_Id'    => $leaveType,
													'Employee_Id'     => $employeeId,
													'Encashed_Days'   => $encashmentDays,
													'Encashed_Status' => 'Applied',
													'Encashed_Date'   => date('Y-m-d H:i:s'));
							
							$this->view->result = $this->_dbLeave->addEncashment ($encashmentDetails, $this->_logEmpId);
						}
						else
						{
                            /** If the remaining encashable days is negative then employee has taken the eligible leaves already */
                            if($availEncahDays[0]['Emp_Encashment_Remaining_Eligible_Days'] <= 0 ){
                                $this->view->result = array('success' => false, 'msg' => 'You have already applied leave encashment for the available days', 'type' => 'info');
                            }else{
                                $this->view->result = array('success' => false, 'msg' => 'You can\'t apply more than '.$availEncahDays[0]['Emp_Encashment_Remaining_Eligible_Days'] .' Days', 'type' => 'info');
                            }
						}
					}
					else
					{
						$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
					}
				}
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Sorry, Access Denied', 'type' => 'danger');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }
	
	/**
	 *	to get Avail Days for leave type in Apply Encashment Form
    */
    public function encashAvailLeaveAction()
    {
        $this->_helper->layout->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('encash-avail-leave', 'json')->initContext();

			if ($this->_encashmentAccessRights['Employee']['Add'] == 1 || $this->_encashmentAccessRights['Employee']['Is_Manager'] == 1 || $this->_encashmentAccessRights['Admin'] == 'admin')
			{
				$empId = $this->_getParam('Employee_Id', null);
				$empId = filter_var($empId, FILTER_SANITIZE_NUMBER_INT);
				
				if ($empId > 0)
				{
                    $historyArr = array('Employee_Id' => $empId);

                    $encashLeaves = $this->_dbLeave->empLeaveHistory (NULL,NULL,NULL,$historyArr,'listLeaveEncashmentLeaveTypes');
					
					if (count($encashLeaves) > 0)
					{
						$this->view->result = $encashLeaves;
					}
					else
					{
						$this->view->result = array('success' => false, 'msg' => 'No leaves available', 'type' => 'warning');
					}
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'warning');
				}
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Sorry, Access denied', 'type' => 'danger');
			}
        }
		else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }
	
	/**
	 * Get Leave enhancement limit
	 */
    public function encashLimitAction()
    {
        $this->_helper->layout->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('encash-limit', 'json')->initContext();
            
            $leavetype = $this->_getParam('leavetype', null);
            $leavetype = filter_var($leavetype, FILTER_SANITIZE_NUMBER_INT);
            
			$employee = $this->_getParam('employee', null);
            $employee = filter_var($employee, FILTER_SANITIZE_NUMBER_INT);
        
            $historyArr = array('Employee_Id' => $employee,
                                'LeaveType_Id' => $leavetype);

            $this->view->result = $this->_dbLeave->empLeaveHistory (NULL,NULL,NULL,$historyArr,'listEmpEncashmentLimit');
        }
		else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }
	
	/**
	 *	Status update
	 */
    public function leaveStatusAction()
    {
        $this->_helper->layout->disableLayout();
		
		// if (isset($_SERVER['HTTP_REFERER']))
        if(1)
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('leave-status', 'json')->initContext();
			
            $leaveId = $this->_getParam('Leave_Id', null);
            $leaveId = filter_var($leaveId, FILTER_SANITIZE_NUMBER_INT);
			$leaveId = $this->_validation->intValidation($leaveId);
            
			$leaveAction = $this->_getParam('isAction', null);
            $leaveAction = filter_var($leaveAction, FILTER_SANITIZE_STRING);
			$leaveAction = $this->_validation->alphaValidation($leaveAction);
			
            $approverId = $this->_getParam('Approver_Id', null);
            $approverId = filter_var($approverId, FILTER_SANITIZE_STRING);
            $approverId = $this->_validation->intValidation($approverId);
            
            $addedBy = $this->_getParam('Added_By', null);
            $addedBy = filter_var($addedBy, FILTER_SANITIZE_STRING);
            $addedBy = $this->_validation->intValidation($addedBy);
            
            $employeeId = $this->_getParam('Employee_Id', null);
            $employeeId = filter_var($employeeId, FILTER_SANITIZE_STRING);
            $employeeId = $this->_validation->intValidation($employeeId); 

            $workflowStatus = $this->_getParam('Workflow_Status', null);
            $workflowStatus = filter_var($workflowStatus, FILTER_SANITIZE_STRING);
			$workflowStatus = $this->_validation->alphaValidation($workflowStatus);
            
            $esicReason = $this->_getParam('esicReason', null);
            $esicReason = filter_var($esicReason, FILTER_SANITIZE_STRING);;

            $documentList = $this->_getParam('documentList', null);
            
            /** If the approval is done from workflow approval management then we can use the approver id as login emp id*/
            if($workflowStatus['value'] == "Completed") {
                $this->_logEmpId = $approverId['value'];
            }
            
            if ( (!empty($leaveId['value']) && $leaveId['valid']) && ( $workflowStatus['value'] == "Completed"  
                || ((in_array($leaveAction['value'], array('CancelApply', 'CancelAppliedLeave'))
                && ((in_array($this->_logEmpId, array($addedBy['value'],$employeeId['value'])) && ($this->_leaveAccess['Add'] 
                || $this->_leaveAccess['Update']|| $this->_myTeamAccess['Add'] == 1 || $this->_myTeamAccess['Update'] == 1 || $this->_selfServiceAccess['Add'] == 1 || $this->_selfServiceAccess['Update'] == 1)) || (($this->_leaveAccess['Is_Manager'] == 1 || $this->_myTeamAccess['Is_Manager'] == 1) && $approverId['value'] == $this->_logEmpId) 
                || ($this->_leaveAccessRights['Admin']==='admin' || $this->_myTeamAccessRights['Admin']==='admin'))) || (($this->_leaveAccess['Is_Manager'] == 1 || $this->_myTeamAccess['Is_Manager'] == 1) 
                && in_array($leaveAction['value'], array('Approve', 'CancelApprove')) &&
                ($approverId['value'] == $this->_logEmpId ||  ($this->_leaveAccessRights['Admin']=='admin' || $this->_myTeamAccessRights['Admin']==='admin')) )) ))            
            {
                if ( $this->getRequest()->isPost() )
				{
					$formData          = $this->getRequest()->getPost();
                    $status            = $this->_validation->alphaValidation($formData['Status']);
                    $comments          = $this->_validation->alphaNumSpCDotHySlashNewLineValidation($formData['Comments']);
					
                    $isEmpFinalSettlementInitiated = $this->_dbCommonFun->empFinalSettlementInitiated($employeeId['value']);

                    if($isEmpFinalSettlementInitiated == 1){
                        $this->view->result = array('success' => false, 'msg' => 'Leave cannot be updated as the full and final settlement is initiated or settled for the employee.Kindly delete the F & F settlement in order to make the necessary modifications.', 'type' => 'warning');
                    }else{
					if (in_array($status['value'], array('Returned', 'Rejected', 'Cancel Applied')))
					{
						$comments['valid'] = $this->_validation->lengthValidation($comments, 5, 3000, true);
					}
					else
					{
						$comments['valid'] = $this->_validation->lengthValidation($comments, 5, 3000, false);
					}
					
					if (!empty($status['value']) && $status['valid'] && $comments['valid'] && 
                        ((!empty($comments['value']) && (in_array($status['value'], array('Returned', 'Rejected', 'Cancel Applied')) ||
                        ($leaveAction['value'] === 'CancelAppliedLeave' && $status['value'] === 'Cancelled'))) ||
						 in_array($status['value'], array('Approved', 'Cancelled'))))
					{
						$leaveData = array('Leave_Id'     => $leaveId['value'],
										   'Approval_Id'  => $approverId['value'],
										   'Leave_Action' => $leaveAction['value'],
										   'Status'       => $status['value'],
										   'Comment'      => $comments['value'],
                                           'Workflow_Status'=>$workflowStatus['value'] ? $workflowStatus['value'] : NULL,
                                            'ESIC_Reason' => $esicReason,
                                            'documentList' => $documentList);
						
						$customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
						$customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
						
                        $this->view->result = $this->_dbLeave->leaveStatusUpdate ($leaveData, $this->_logEmpId, $this->_formNameA, $customFormNamee);
					}
					else
					{
						$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
					}
                    }
				}
            }
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Sorry, Access denied', 'type' => 'warning');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }
	
	/**
	 * Approving status for multiple rows at a time
	 */
	public function multiStatusApprovalAction()
    {
        $this->_helper->layout->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('multi-status-approval', 'json')->initContext();
			
			if ($this->_leaveAccess['Is_Manager'] == 1 || $this->_myTeamAccess['Is_Manager'] == 1)
			{
				if ( $this->getRequest()->isPost() )
				{
					$formData = $this->getRequest()->getPost();
					
					$leaveIdArr = filter_var($formData['Leave_Ids'], FILTER_SANITIZE_NUMBER_INT, FILTER_REQUIRE_ARRAY);

                    if(!empty($this->_leaveAccessRights['Admin']) || !empty($this->_myTeamAccessRights['Admin']))
                    {
                        $leaveIdArr['Admin']='admin';    
                    }
                    
					if (count($leaveIdArr) > 0)
					{
						$customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
						$customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
						
						$this->view->result = $this->_dbLeave->multiStatusApproval ($leaveIdArr, $this->_logEmpId, $this->_formNameA, $customFormNamee);
					}
					else
					{
						$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'danger');
					}
				}
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Sorry, Access denied', 'type' => 'danger');
			}
		}
		else
		{
			$this->_helper->redirector('index', 'leaves', 'employees');
		}
    }
	
	/**
	 * Delete Leave
	 */
    public function deleteLeaveAction()
    {
        $this->_helper->layout->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('delete-leave-type', 'json')->initContext();
			
			if ($this->_leaveAccess['Delete'] == 1 || $this->_myTeamAccess['Delete'] == 1)
            {
                $formData = $this->getRequest()->getPost();
                $leaveId = filter_var($formData['leaveId'], FILTER_SANITIZE_NUMBER_INT, FILTER_REQUIRE_ARRAY);
                $attendanceSummarySelectedDetails = [];

                if(count($leaveId)>0)
				{
                    $customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
                    $customFormName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
                    $leaveId = $this->_dbLeave->lateAttendanceShortageExcludedLeaveId($leaveId);
                    if(!empty($leaveId))
                    {
                        $updatedCount = 0;
                        foreach($leaveId as $val)
                        {
                            $result = $this->_dbLeave->deleteLeave($val, $this->_logEmpId, $this->_formNameA, $customFormName);
                            if($result['success']){
                                $updatedCount++;
                                $attendanceSummarySelectedDetails[] = $result['leaveDetails'];
                            }
                        }
                        $this->_dbCommonFun->triggerAttendanceSummaryStepFunction($attendanceSummarySelectedDetails,'leaves');

                        if(count($leaveId) == $updatedCount)
                            $this->view->result = array('success' => true, 'msg'=>$customFormName." record deleted successfully", 'type'=>'success');
                        elseif($updatedCount >= 1)
                            $this->view->result = array('success' => true, 'msg'=>$customFormName." record deleted partially.", 'type'=>'success');
                        else
                            $this->view->result = array('success' => true, 'msg'=>"Unable to delete ".$customFormName." record.", 'type'=>'success');
                    }
                    else
                    {
                        $this->view->result = array('success' => true, 'msg'=>"Unable to delete ".$customFormName." record.Either attendance shortage or late attendance or early checkout is associated with this leave record", 'type'=>'success');
                    }
				}
                else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
				}
            }
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Access denied', 'type' => 'warning');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }

    /** delete only the documents in edit form **/
	public function removeUploadedFilesAction()
    {
		$this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('remove-uploaded-files', 'json')->initContext();
			
            $leaveId = $this->_getParam('leaveId');
    	    $leaveId = $this->_validation->intValidation($leaveId);
			
			$employeesDocumentUploadFileName = $this->_getParam('_leaveDocumentsUploadFileName');
			
			if(!empty($leaveId['value']) && !empty($employeesDocumentUploadFileName)){
				$this->view->result = $this->_dbLeave->deleteLeaveDocumentUploadFiles($leaveId['value'],$employeesDocumentUploadFileName);
			}
			else{
				$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
			}	
        }
		else{
			$this->_helper->redirector('index', 'leaves', 'employees');
		}
    }
	
	/**
	 * Get Leave type details to show in a grid
	 */
    public function listLeaveTypeAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-leave-type', 'json')->initContext();
			
            if ($this->_leaveTypeAccess['View'] == 1 )
            {	
                $sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
				
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$leaveName = $this->_getParam('Leave_Name', null);
				$leaveName = filter_var($leaveName, FILTER_SANITIZE_STRIPPED);
				
				$leaveType = $this->_getParam('Leave_Type', null);
				$leaveType = filter_var($leaveType, FILTER_SANITIZE_STRIPPED);
				
				$carryOver = $this->_getParam('Carry_Over', null);
				$carryOver = filter_var($carryOver, FILTER_SANITIZE_STRIPPED);
				
				$leaveEncashmente = $this->_getParam('Leave_Encashment', null);
				$leaveEncashmente = filter_var($leaveEncashmente, FILTER_SANITIZE_STRIPPED);
				
				$leaveTypeStatus = $this->_getParam('LeaveTypeStatus', null);
				$leaveTypeStatus = filter_var($leaveTypeStatus, FILTER_SANITIZE_STRIPPED);
				
                $searchArr = array('Leave_Name'       => $leaveName,
								   'Leave_Type'       => $leaveType,
								   'Carry_Over'       => $carryOver,
								   'Leave_Encashment' => $leaveEncashmente,
								   'LeaveTypeStatus' => $leaveTypeStatus);
				
				$this->view->result = $this->_dbLeave->listLeaveType ($page, $rows, $sortField, $sortOrder, $searchAll, $searchArr);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }
	
	/**
	 * Add leave type
	 */
    public function updateLeaveTypeAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('update-leave-type', 'json')->initContext();
			
			$leaveTypeId = $this->_getParam('LeaveType_Id', null);
			$leaveTypeId = filter_var($leaveTypeId, FILTER_SANITIZE_NUMBER_INT);
			
			if ((empty($leaveTypeId) && $this->_leaveTypeAccess['Add'] == 1) ||
				(!empty($leaveTypeId) && $leaveTypeId > 0 && $this->_leaveTypeAccess['Update'] == 1))
			{
				if ( $this->getRequest()->isPost() )
				{
					$formData = $this->getRequest()->getPost();
					
					$leaveName            = $this->_validation->alphaNumSpCDotHySlashValidation($formData['Leave_Name']);
					$leaveName['valid']   = $this->_validation->lengthValidation($leaveName, 3, 50, true);
                    $leaveType            = $this->_validation->alphaValidation($formData['Leave_Type']);
					$enablePersonalChoice = $this->_validation->alphaValidation($formData['Enable_Personal_Choice']);
                    $parentLeave          = $this->_validation->alphaValidation($formData['Parent_Leave']);
                    $parentLeaveTypeId    = $this->_validation->intValidation($formData['Parent_LeaveTypeId']);
                    
                    $leaveEnforcement     = $this->_validation->intValidation($formData['Leave_Enforcement_Configuration']);
                    $leaveTypeCustomGroup = $this->_validation->intValidation($formData['Leave_Type_Custom_Group']);
                    $minLimit             = $this->_validation->daysValidation($formData['Minimum_Limit']);
                    $totalDays            = $this->_validation->daysValidation($formData['Total_Days']);
                    $maximumLimit         = $this->_validation->daysValidation($formData['Maximum_Limit'],1500);
					$applicableOnProbation = $this->_validation->onlyLetterSpaceValidation($formData['Applicable_During_Probation']);
                    $applicableOnNoticePeriod = $this->_validation->alphaValidation($formData['Applicable_During_Notice_Period']);
                    $enableProration      = $this->_validation->checkboxValidation($formData['Enable_Proration']);
					$carryOver            = $this->_validation->alphaValidation($formData['Carry_Over']);
                    $carryOverLimit       = $this->_validation->daysValidation($formData['Carry_Over_Limit']);
                    $carryOverAccumulationLimit = $this->_validation->daysValidation($formData['Carry_Over_Accumulation_Limit'],1500);
					$leaveEncashment      = $this->_validation->alphaValidation($formData['Leave_Encashment']);
                    $leaveEncashmentLimit = $this->_validation->daysValidation($formData['Leave_Encashment_Limit']);
                    $autoEncashment      = $this->_validation->alphaValidation($formData['Auto_Encashment']);
					$frequency            = $this->_validation->daysValidation($formData['Frequency']);
					$displayInPayslip     = $this->_validation->alphaValidation($formData['Display_In_Payslip']);
                    $period               = $this->_validation->intValidation($formData['Period']);
                    $eligibleBasedPeriod  = $this->_validation->daysValidation($formData['Eligible_Days_Based_On_Period']);
					$gender               = $this->_validation->alphaValidation($formData['Gender']);
                    $leaveActivationDays      = $this->_validation->daysValidation($formData['Leave_Activation_Days'], 5000);
					$coverage             = $this->_validation->alphaValidation($formData['Coverage']);
                    $showStatisticsInDashboard = $this->_validation->checkboxValidation($formData['Show_Statistics_In_Dashboard']);
                    $documentUpload         = $this->_validation->daysValidation($formData['Document_Upload']);
                    $maxDaysForDocumentUpload  = $this->_validation->daysValidation($formData['Max_days_for_Document_Upload']);
					$leaveApprovalCutoff  = $this->_validation->daysValidation($formData['Leave_Approval_Cutoff']);
					$workflowId           = $this->_validation->intValidation($formData['Workflow_Id']);
                    
					if(!empty($leaveApprovalCutoff['value'])){
						$leaveApprovalCutoff  = $this->_validation->minMaxValueValidation($leaveApprovalCutoff['value'],1,720);
					}
                    $advanceNotification  = $this->_validation->intValidation($formData['Advance_Notification']);
					if(!empty($advanceNotification['value'])){
						$advanceNotification  = $this->_validation->minMaxValueValidation($advanceNotification['value'],1,30);	
					}
					$description['value'] = $this->_validation->commonFilters($formData['Description']);
                    $description['valid'] = $this->_validation->lengthValidation($description, 5, 500, false);
                    
                    $accumulateEligibleDays = $this->_validation->intValidation($formData['Accumulate_Eligible_Days']);
                    
                    $maxDaysLimit = $totalDays['value'] + $carryOverLimit['value'] +  $carryOverAccumulationLimit['value'];

                    $prorateLeaveBalanceFrom = $this->_validation->onlyLetterSpaceValidation($formData['Prorate_Leave_Balance_From']);
                    $prorateAfter            = $this->_validation->daysValidation($formData['Prorate_After'], 5000);

                    $leaveEncashmentForFF          = $this->_validation->alphaValidation($formData['Leave_Encashment_For_FF']);
                    $leaveDeductionForFF           = $this->_validation->alphaValidation($formData['Leave_Deduction_For_FF']);

                    $accumulateFrom = $this->_validation->onlyLetterSpaceValidation($formData['Accumulate_From']);
                    $accumulateAfter = $this->_validation->daysValidation($formData['Accumulate_After']);

                    $leaveAccrual  = $this->_validation->onlyLetterSpaceValidation($formData['leaveAccrual']);

                    $restrictEmployeeToApply = $this->_validation->checkboxValidation($formData['Restrict_Employee_To_Apply']);

                    $enableLeaveException   = $this->_validation->alphaValidation($formData['Enable_Leave_Exception']);
                    if($enableLeaveException['value']=='Yes')
                    {
                        $hpMinimumLimit         = $this->_validation->daysValidation($formData['HP_Minimum_Limit']);
                        $halfPaidLeaveDeduction = $this->_validation->alphaValidation($formData['Half_Paid_Leave_Deduction']);
                        $leaveUnit              = $this->_validation->daysValidation($formData['Leave_Unit']);
                    }
                    else
                    {
                        $hpMinimumLimit['value']         = '';
                        $halfPaidLeaveDeduction['value'] = '';
                        $leaveUnit['value']              = ''; 
                    }

                    $leaveClosureBasedOn = $this->_validation->onlyLetterSpaceValidation($formData['Leave_Closure_Based_On']);
                    $leaveClosureMonth   = $this->_validation->intValidation($formData['Leave_Closure_Month']);
                    $leaveClosureYear    = $this->_validation->intValidation($formData['Leave_Closure_Year']);
                    $leaveAccrualWithoutCustomConfig = ['From the beginning of the leave period','After the end of the leave period'];

                    $prorationDateRoundUp = $this->_validation->onlyLetterSpaceValidation($formData['Proration_Threshold_Date']);
                    $specificDay          = $this->_validation->daysValidation($formData['Day_Of_The_Month'],28);

                    $accrualRestrictionPeriod = $this->_validation->onlyLetterSpaceValidation($formData['Accrual_Restriction_Period']);
                    $accrualUntil             = $this->_validation->daysValidation($formData['Accrual_Until'],5000);

                    if(!empty($carryOverAccumulationLimit['value']) && $carryOverAccumulationLimit['value'] > 0)
                    {
                        $actualMaximumLimit = $carryOverAccumulationLimit['value'];
                    }
                    else
                    {
                        $actualMaximumLimit = $totalDays['value'];
                    }

                    
                    $orgLeavePeriodEligibleDays = [];
                    if ($leaveAccrual['value'] === 'Based on the custom configuration' && $period['value'] == 1) {
                        for ($i = 1; $i <= 12; $i++) {
                            $monthKey = 'Month_'.$i;
                            $month = $this->_validation->daysValidation($formData[$monthKey]);
                    
                            $orgLeavePeriodEligibleDays[] = ['LeaveType_Id'  => $leaveTypeId,
                                                            'Month_Id'      => $i,
                                                            'Eligible_Days' => $month['value']];
                        }
                    }
                    
                    $replenishmentLimit = $this->_validation->daysValidation($formData['Replenishment_Limit'],127);

                    if (!empty($leaveName['value']) && $leaveName['valid'] && !empty($leaveType['value']) && $leaveType['valid'] &&
                        !empty($enablePersonalChoice['value']) && $enablePersonalChoice['valid'] &&
                        (($parentLeave['value']=='Yes' && !empty($parentLeaveTypeId['value']) && $parentLeaveTypeId['value']!=$leaveTypeId) || ($parentLeave['value']=='No' && empty($parentLeaveTypeId['value']))) &&
                        !empty($totalDays['value']) && $totalDays['valid'] && !empty($maximumLimit['value']) && $maximumLimit['valid'] &&
                        $frequency['valid'] && !empty($carryOver['value']) && $carryOver['valid'] && !empty($leaveEncashment['value']) &&
						$leaveEncashment['valid'] && !empty($leaveEncashmentForFF['value']) && $leaveEncashmentForFF['valid'] 
                        && !empty($leaveDeductionForFF['value']) && $leaveDeductionForFF['valid'] &&
                        (($leaveType['value']=='Paid Leave' && in_array($leaveEncashmentForFF['value'],array('Yes','No')) && 
                        in_array($leaveDeductionForFF['value'],array('Yes','No'))) || (($leaveType['value']=='Unpaid Leave' || $leaveType['value']=='On Duty') &&
                         $leaveEncashmentForFF['value']=='No' && $leaveDeductionForFF['value']=='No')) && !empty($displayInPayslip['value']) && $displayInPayslip['valid'] &&
						$leaveActivationDays['valid'] && !empty($gender['value']) && $gender['valid'] && $showStatisticsInDashboard['valid'] && $restrictEmployeeToApply['valid'] &&
                        (($coverage['value']=='ORG' && empty($formData['Grade']) && empty($leaveTypeCustomGroup['value'])) || ($coverage['value']=='GRA' && 
                        !empty($formData['Grade']) && empty($leaveTypeCustomGroup['value'])) || ($coverage['value']=='CUSTOMGROUP' && 
                        empty($formData['Grade']) && $leaveTypeCustomGroup['valid'] && !empty($leaveTypeCustomGroup['value']))) &&
						((!empty($leaveApprovalCutoff['value']) && $leaveApprovalCutoff['valid']) || empty($leaveApprovalCutoff['value'])) &&
						((!empty($advanceNotification['value']) && $advanceNotification['valid']) || empty($advanceNotification['value'])) &&
						$description['valid'] && in_array($formData['Leave_Calculation_Days'], array('0', '1')) && in_array($formData['Leave_Status'], array('Active', 'Inactive'))
						&& $applicableOnProbation['valid'] && !empty($applicableOnProbation['value']) && ($minLimit['value'] <= $totalDays['value'])
						&& (($maximumLimit['value'] >= $minLimit['value']) && $maximumLimit['value'] <= $actualMaximumLimit)
                         && (( $carryOver['value'] == 'Yes' && $carryOverLimit['value'] <= $totalDays['value'] ) || empty($carryOverLimit['value']))
                         && (( $documentUpload['value'] == '1' && $maxDaysForDocumentUpload['value'] <= $maxDaysLimit) || empty($documentUpload['value']))
                         && (( $leaveEncashment['value'] == 'Yes' && $leaveEncashmentLimit['value'] <= $totalDays['value'] ) || empty($leaveEncashmentLimit['value']))
                         && $accumulateEligibleDays['valid'] && $enableProration['valid'] && $period['valid']
                         && in_array($period['value'],array(1,3,6,12)) && (((int)$period['value'] === 12)
                         || ($leaveAccrual['valid'] && (in_array($leaveAccrual['value'],$leaveAccrualWithoutCustomConfig))
                        //  || ($leaveAccrual['value'] === 'Based on the custom configuration' && (!empty($eligibleBasedPeriod['value']) && $eligibleBasedPeriod['valid'] 
                        //  && ($eligibleBasedPeriod['value'] >= 0.5 && $eligibleBasedPeriod['value'] <= $totalDays['value']))))

                         || (in_array($period['value'],array(3,6,12)) && $leaveAccrual['value'] === 'Based on the custom configuration' && (!empty($eligibleBasedPeriod['value']) && $eligibleBasedPeriod['valid'] 
                         && ($eligibleBasedPeriod['value'] >= 0.25 && $eligibleBasedPeriod['value'] <= $totalDays['value']))) 
                         || ($leaveAccrual['value'] === 'Based on the custom configuration' && $period['value'] == 1 && !empty($orgLeavePeriodEligibleDays)))) 
                         
                         && (($formData['Org_Enable_Workflow'] == "Yes" && !empty($workflowId['value'] && $workflowId['valid'])) || empty($workflowId['value']))
                         && (($enableLeaveException['value']=='Yes' && (($hpMinimumLimit['value'] > 0) 
                        && ($hpMinimumLimit['value'] <= 365)) && in_array($halfPaidLeaveDeduction['value'],array('Yes','No')) 
                        && !empty($leaveUnit['value']) && $leaveUnit['valid']) || ($enableLeaveException['value']=='No' 
                        && empty($hpMinimumLimit['value']) && empty($halfPaidLeaveDeduction['value']) && empty($leaveUnit['value']))) &&
                        $leaveClosureBasedOn['valid'] && !empty($leaveClosureBasedOn['value']) && 
                        (($leaveClosureBasedOn['value']=='Selected Month' && !empty($leaveClosureMonth['value']) && $leaveClosureYear['valid'] && !empty($leaveClosureYear['value'])) 
                        || ((in_array($leaveClosureBasedOn['value'], ['Employee Date Of Join','Employee Service','Limited Replenishment on Approval', 'Unlimited Replenishment on Approval'])) && empty($leaveClosureMonth['value']) && empty($leaveClosureYear['value']))))
                        {
                        $countOfTotalDaysAndCarryOverLimit = $totalDays['value'] + $carryOverLimit['value'];
                        if(($carryOver['value'] == 'Yes' && $carryOverAccumulationLimit['value'] >= $countOfTotalDaysAndCarryOverLimit) ||
                        ($carryOver['value'] == 'No' && (empty($carryOverAccumulationLimit['value']) || $carryOverAccumulationLimit['value']==0))) 
                        {
                            /** Leaves will be carried forward to the next year only when the carryover flag and the 
                             * accumulate eligible days is enabled. If the period is 'Annual' then accumulate eligible days should be updated as 1
                             * as this flag will be presented only for the leave period 'quarterly','halfyearly','monthly'
                            */
                            if((int)$period['value'] === 12){
                                $eligibleBasedPeriod['value'] = 0;
                                $accumulateEligibleDays['value'] = 0;
                                
                            }

                            /* If the coverage is 'Organization'/'Grade' then leave enforcement configuration can be 'Normal Leave' or special leaves.
                            If the coverage is 'Custom Group' then leave enforcement configuration should be 'Normal Leave' only */                        
                            if(($coverage['value'] == 'ORG' || $coverage['value'] == 'GRA') || 
                                (($leaveEnforcement['value'] == 1 || $leaveEnforcement['value'] == 2) && $coverage['value'] == 'CUSTOMGROUP')){
                                if (($actualMaximumLimit >= $maximumLimit['value']) && ($leaveType['value'] == "Paid Leave" ||
                                (($leaveType['value'] == "Unpaid Leave" || $leaveType['value'] == "On Duty") && $carryOver['value'] == 'No' && $leaveEncashment['value'] == 'No')))
                                {
                                    $isValid = $this->_dbLeave->isValidProrataValue($leaveTypeId,$enableProration['value']);
                                    if($isValid)
                                    {
                                        if(empty($leaveApprovalCutoff['value'])){
                                            $leaveApprovalCutoff['value'] = new Zend_Db_Expr('NULL');
                                        }
                                        
                                        if(empty($advanceNotification['value'])){
                                            $advanceNotification['value'] = new Zend_Db_Expr('NULL');								
                                        }

                                        /** If the leave enforcement configuration is Service Based Leave I, Service Based Leave II,
                                         * Quarter wise leave, Maternity leave, proration flag value should be zero
                                         */
                                        if(in_array($leaveEnforcement['value'],array(2,3,4))){
                                            $enableProration['value'] = 0;
                                        }

                                        if(((in_array($enableProration['value'],array(1,5))) &&((in_array($prorateLeaveBalanceFrom['value'], array('Date Of Join','After Probation')) && empty($prorateAfter['value']))
                                        ||($prorateLeaveBalanceFrom['value']=='Custom Configuration' && !empty($prorateAfter['value']))))
                                        || ($enableProration['value']==0 && empty($prorateLeaveBalanceFrom['value'] && empty($prorateAfter['value']))))
                                        {
                                            if(((in_array($enableProration['value'],array(1,5))) && (($prorationDateRoundUp['value']=='Any day of the month' && empty($specificDay['value']))
                                                ||($prorationDateRoundUp['value']=='Before specific day of the month' && !empty($specificDay['value']))))
                                                || ($enableProration['value']==0 && empty($prorationDateRoundUp['value'] && empty($specificDay['value']))))
                                            {
                                                if(empty($prorationDateRoundUp['value'])){
                                                    $prorationDateRoundUp['value'] = new Zend_Db_Expr('NULL');								
                                                }
    
                                                if(empty($specificDay['value'])){
                                                    $specificDay['value'] = new Zend_Db_Expr('NULL');								
                                                }

                                                if(empty($prorateLeaveBalanceFrom['value'])){
                                                    $prorateLeaveBalanceFrom['value'] = new Zend_Db_Expr('NULL');								
                                                }
    
                                                if(empty($prorateAfter['value'])){
                                                    $prorateAfter['value'] = new Zend_Db_Expr('NULL');								
                                                }
    
                                                if(($accumulateEligibleDays['value']==1 &&((in_array($accumulateFrom['value'], array('Date Of Join','After Probation')) && empty($accumulateAfter['value']))
                                                ||($accumulateFrom['value']=='Custom Configuration' && !empty($accumulateAfter['value']))))
                                                || ($accumulateEligibleDays['value']==0 && empty($accumulateFrom['value'] && empty($accumulateAfter['value']))))
                                                {
                                                    if(empty($accumulateFrom['value'])){
                                                        $accumulateFrom['value'] = new Zend_Db_Expr('NULL');
                                                    }
        
                                                    if(empty($accumulateAfter['value'])){
                                                        $accumulateAfter['value'] = 0;
                                                    }
    
                                                    if($enableLeaveException['value']=='No')
                                                    {
                                                        $hpMinimumLimit['value']         = new Zend_Db_Expr('NULL');
                                                        $halfPaidLeaveDeduction['value'] = new Zend_Db_Expr('NULL');
                                                        $leaveUnit['value']              = new Zend_Db_Expr('NULL');
                                                    }
                                                    
                                                    if((in_array($period['value'],array(1,3,6)) && $accrualRestrictionPeriod['value']=='Yes' && !empty($accrualUntil['value']))
                                                    || (in_array($period['value'],array(1,3,6,12)) && $accrualRestrictionPeriod['value']=='No' && empty($accrualUntil['value'])))
                                                    {
                                                        if (($leaveAccrual['value'] === 'Based on the custom configuration' && $period['value'] == 1 && !empty($orgLeavePeriodEligibleDays)) 
                                                        || ($period['value'] != 1 && empty($orgLeavePeriodEligibleDays))
                                                        || ($leaveAccrual['value'] != 'Based on the custom configuration' && empty($orgLeavePeriodEligibleDays))) 
                                                        {
                                                             
                                                            if(($leaveClosureBasedOn['value'] === 'Limited Replenishment on Approval' && $replenishmentLimit['value']=='') || ($leaveClosureBasedOn['value'] != 'Limited Replenishment on Approval' && !empty($replenishmentLimit['value']))) 
                                                            {
                                                                $this->view->result = array('success' => false, 'msg' => 'Invalid Limited Replenishment on Approval configuration', 'type' => 'info');  
                                                            }
                                                            else
                                                            {
                                                                $leaveTypeData = array('Leave_Name'         => $leaveName['value'],
                                                                                'Leave_Type'                => $leaveType['value'],
                                                                                'Parent_Leave'              => $parentLeave['value'],
                                                                                'Parent_LeaveTypeId'        => $parentLeaveTypeId['value'],
                                                                                'Enable_Personal_Choice'    => $enablePersonalChoice['value'],
                                                                                'Leave_Enforcement_Configuration' => $leaveEnforcement['value'],
                                                                                'Minimum_Total_Days'    => $minLimit['value'],
                                                                                'Total_Days'            => $totalDays['value'],
                                                                                'Maximum_Limit'         => $maximumLimit['value'],
                                                                                'Applicable_During_Probation' => $applicableOnProbation['value'],
                                                                                'Applicable_During_Notice_Period' => (($applicableOnNoticePeriod['value'] == 'Yes') ? 1 : 0),
                                                                                'Enable_Proration'      => $enableProration['value'],
                                                                                'Prorate_Leave_Balance_From'  => $prorateLeaveBalanceFrom['value'],
                                                                                'Prorate_After'               => $prorateAfter['value'],
                                                                                'Proration_Threshold_Date'  => $prorationDateRoundUp['value'],
                                                                                'Day_Of_The_Month'             => $specificDay['value'],
                                                                                'Carry_Over'            => $carryOver['value'],
                                                                                'CarryOver_Limit'       => ($carryOver['value'] == 'Yes' ? $carryOverLimit['value'] : 0),
                                                                                'Carry_Over_Accumulation_Limit' => ($carryOver['value'] == 'Yes' ? $carryOverAccumulationLimit['value'] : 0),
                                                                                'Encashment'            => $leaveEncashment['value'],
                                                                                'Encashment_Limit'      => ($leaveEncashment['value'] == 'Yes' ? $leaveEncashmentLimit['value'] : 0),
                                                                                'Auto_Encashment'       => (($leaveEncashment['value'] == 'Yes') ? 1 : 0),
                                                                                'Leave_Encashment_For_FF'           => $leaveEncashmentForFF['value'], 
                                                                                'Leave_Deduction_For_FF'            => $leaveDeductionForFF['value'],
                                                                                'Frequency'             => $frequency['value'],
                                                                                'Show_In_Payslip'       => (($displayInPayslip['value'] == 'Yes') ? 1 : 0),
                                                                                'Period'                => $period['value'],
                                                                                'Accrual'  => $leaveAccrual['value'],
                                                                                'Eligible_Days_Based_On_Period' => $eligibleBasedPeriod['value'],
                                                                                'Accrual_Restriction_Period'    =>  $accrualRestrictionPeriod['value'],
                                                                                'Accrual_Until'                 =>  $accrualUntil['value'],
                                                                                'Accumulate_Eligible_Days' => $accumulateEligibleDays['value'],
                                                                                'Accumulate_From' => $accumulateFrom['value'],
                                                                                'Accumulate_After' => $accumulateAfter['value'],
                                                                                'Gender'                => $gender['value'],
                                                                                'Leave_Activation_Days' => $leaveActivationDays['value'],
                                                                                'Coverage'			   => $coverage['value'],
                                                                                'Leave_Approval_Cutoff' => $leaveApprovalCutoff['value'],
                                                                                'Advance_Notification'  => $advanceNotification['value'],
                                                                                'Leave_Calculation_Days'=> $formData['Leave_Calculation_Days'],
                                                                                'Show_Statistics_In_Dashboard' => $showStatisticsInDashboard['value'],
                                                                                'Restrict_Employee_To_Apply' => $restrictEmployeeToApply['value'],
                                                                                'Leave_Status'          => $formData['Leave_Status'],
                                                                                'Document_Upload'       => $documentUpload['value'],
                                                                                'Max_Days_For_Document_Upload' => $maxDaysForDocumentUpload['value'],
                                                                                'Custom_Group_Id'       => $leaveTypeCustomGroup['value'],
                                                                                'Workflow_Id'           => $workflowId['value'],
                                                                                'Enable_Leave_Exception'=> $enableLeaveException['value'],
                                                                                'Minimum_Limit'         => $hpMinimumLimit['value'],
                                                                                'Half_Paid_Leave_Deduction'=> $halfPaidLeaveDeduction['value'],
                                                                                'Leave_Unit'            => $leaveUnit['value'],
                                                                                'Leave_Closure_Based_On' => $leaveClosureBasedOn['value'],
                                                                                'Replenishment_Limit'    => $replenishmentLimit['value'],
                                                                                'Leave_Closure_Month'    => $leaveClosureMonth['value'],
                                                                                'Leave_Closure_End_Year' => (strtolower($leaveClosureBasedOn['value']) === 'selected month' ? $leaveClosureYear['value'] : new Zend_Db_Expr('NULL')),
                                                                                'Description'            => htmlentities ($description['value']));

                                                                $customFormName = $this->_ehrTables->getCustomForms($this->_formNameB);
                                                                $customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameB);
                                                                $formId = $this->_dbComment->getFormId($this->_formNameB);
                                                                $formData['Grade'] = (isset($formData['Grade']) && !empty($formData['Grade'])) ? $formData['Grade'] : '';
                                                                $this->view->result = $this->_dbLeave->updateLeaveType ($leaveTypeData, $leaveTypeId, $formData['Grade'], $this->_logEmpId,$customFormNamee,$formId,$orgLeavePeriodEligibleDays);
                                                            }
                                                        }
                                                        else
                                                        {
                                                            $this->view->result = array('success' => false, 'msg' => 'Invalid accrual period configuration', 'type' => 'info');       
                                                        }
                                                    }
                                                    else
                                                    {
                                                        $this->view->result = array('success' => false, 'msg' => 'Invalid accrual restriction period configuration', 'type' => 'info');       
                                                    }
                                                }else{
                                                    $this->view->result = array('success' => false, 'msg' => 'Invalid accumulate eligible days data', 'type' => 'info');   
                                                }
                                            }
                                            else 
                                            {
                                                $this->view->result = array('success' => false, 'msg' => 'Invalid proration round up configuration', 'type' => 'info');
                                            }
                                        }
                                        else
                                        {
                                            $this->view->result = array('success' => false, 'msg' => 'Invalid proration data', 'type' => 'info');
                                        }
                                    }
                                    else
                                    {
                                        $this->view->result = array('success' => false, 'msg' => "Proration flag should not be changed as leave(s) exists or leave balance is imported for this leave type", 'type' => 'info');
                                    }
                                }
                                else
                                {
                                    $this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
                                }
                            }else{
                                $this->view->result = array('success' => false, 'msg' => 'Custom group coverage is applicable to normal leave types only. Please contact system administrator for more info.', 'type' => 'info');
                            }
                        } else {						
                            $this->view->result = array('success' => false, 'msg' => 'Accumulation limit should be greater than or equal to the count of carry-over limit and leave eligibility days.', 'type' => 'info');
                        }
					}
					else
					{						
						$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
					}
				}
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Sorry, Access Denied', 'type' => 'danger');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }
	
	/**
	 * Delete Leave Type
	 */
    public function deleteLeaveTypeAction()
    {
        $this->_helper->layout->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('delete-leave-type', 'json')->initContext();
			
			if ($this->_leaveTypeAccess['Delete'] == 1)
            {
				$leaveTypeId = $this->_getParam('leaveTypeId', null);
				$leaveTypeId = filter_var($leaveTypeId, FILTER_SANITIZE_NUMBER_INT);
				
				if (!empty($leaveTypeId) && $leaveTypeId > 0)
				{
                    $customFormName = $this->_ehrTables->getCustomForms($this->_formNameB);
                    $customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameB);
                    $formId = $this->_dbComment->getFormId($this->_formNameB);
                    
					$this->view->result = $this->_dbLeave->deleteLeaveType ($leaveTypeId, $this->_logEmpId, $customFormNamee, $formId);
				}
                else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
				}
            }
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Access denied', 'type' => 'warning');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }
      public function listLeaveFreezeAction()
    {
         $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {			
            if ($this->_freezeAccessRights['Employee']['View'] == 1)
            {
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
                $ajaxContext->addActionContext('list-leave-freeze', 'json')->initContext();
                
                $leaveType         = $this->_getParam('Leave_Type', null);
                $leaveType         = filter_var($leaveType, FILTER_SANITIZE_STRIPPED);
				
		$freezeFrom    = $this->_getParam('Freeze_From', null);
		$freezeFrom    = filter_var($freezeFrom, FILTER_SANITIZE_STRIPPED);
		
    	
		$freezeTo    = $this->_getParam('Freeze_To', null);
		$freezeTo    = filter_var($freezeTo, FILTER_SANITIZE_STRIPPED);
		
		
                
		$sortField  = $this->_getParam('iSortCol_0', null);
                $sortField  = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
                
                $sortOrder  = $this->_getParam('sSortDir_0', null);
                $sortOrder  = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
                
                $page       = $this->_getParam('iDisplayStart', null);
                $page       = isset($page) ? intval($page) : 0;
                $page       = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
                
                $rows       = $this->_getParam('iDisplayLength', null);
                $rows       = isset($rows) ? intval($rows) : 10;
                $rows       = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);

                $searchAll  = $this->_getParam('sSearch', null);
                $searchAll  = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$searchArray  = array('LeaveType_Id'=>$leaveType,
									  'Freeze_From'=>$freezeFrom,
									  'Freeze_To'=>$freezeTo);
		
				$userDetailsArray = array('Is_Manager' => $this->_freezeAccessRights['Employee']['Is_Manager'],
											'Admin'      => $this->_freezeAccessRights['Admin'],
											'Session_Id' => $this->_logEmpId,
											'Form_Name'=>$this->_formNameD);
				
				
                
				$this->view->result = $this->_dbLeave->searchLeaveFreeze($page,$rows,$sortField,$sortOrder,$searchAll,$searchArray,$userDetailsArray);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }
     public function updateLeaveFreezeAction()
    {
         $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('update-leave-freeze', 'json')->initContext();
			
            $leaveFreezeId = $this->_getParam('LeaveFreeze_Id');
    	    $leaveFreezeId = $this->_validation->intValidation($leaveFreezeId);
            
            if ((!empty($leaveFreezeId['value']) && $this->_freezeAccessRights['Employee']['Update'] == 1 ) ||
				(empty($leaveFreezeId['value']) && $this->_freezeAccessRights['Employee']['Add'] == 1 ))
            {
				if ($this->getRequest()->isPost())
                {
                    $formData = $this->getRequest()->getPost();
     
                    $leaveType    = $this->_validation->intValidation($formData['LeaveType_Id']);
			
					$freezeFrom         = $this->_validation->dateValidation($formData['Freeze_From']);
					$freezeTo           = $this->_validation->dateValidation($formData['Freeze_To']);
	                if ($leaveType['valid'] && $freezeFrom['valid'] && $freezeTo['valid'] && $freezeFrom['value'] <= $freezeTo['value'])
                    {
						$leaveFreezeDetails  = array('LeaveFreeze_Id'		=> $leaveFreezeId['value'],
						 'LeaveType_Id'      	=> $leaveType['value'],
						 'Freeze_From'   	        => $freezeFrom['value'],	
						 'Freeze_To'   	        => $freezeTo['value']);
                                
                        $customFormName = $this->_ehrTables->getCustomForms($this->_formNameD);
                        $customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameD);
                                
						$this->view->result = $this->_dbLeave->updateLeaveFreeze($leaveFreezeDetails,$this->_logEmpId,$customFormNamee);
					}
					else
					{
						$this->view->result = array('success' => false, 'msg'=>'Invalid data', 'type'=>'info');
					}
                }				
            }
            else
            {
				$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'info');
            }
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }
    
     public function deleteLeaveFreezeAction()
    {
        $this->_helper->layout->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-leave-freeze', 'json')->initContext();
            
            $leaveFreezeId = $this->_getParam('LeaveFreeze_Id', null);
            $leaveFreezeId = filter_var($leaveFreezeId, FILTER_SANITIZE_NUMBER_INT);
            
            if (!empty($leaveFreezeId) && $leaveFreezeId > 0 && $this->_freezeAccessRights['Employee']['Delete'] == 1)
            {
                $customFormName = $this->_ehrTables->getCustomForms($this->_formNameD);
                $customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameD);
                        
                $this->view->result = $this->_dbLeave->deleteLeaveFreeze($leaveFreezeId, $this->_logEmpId,$customFormNamee);
            }
	    else
	    {
		$this->view->result = array('success'=>false, 'msg'=>'Invalid data','type'=>'info');
	    }
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }
   
    public function listLeaveEnforcementAction()
    {
        // action body
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-leave-enforcement', 'json')->initContext();
			
            if ($this->_enforcementAccess['View'] == 1 )
            {	
                $sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
				
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$this->view->result = $this->_dbLeave->listLeaveEnforcement($page, $rows, $sortField, $sortOrder, $searchAll);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }
   
    public function updateLeaveEnforcementAction()
    {
        // action body
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('update-leave-enforcement', 'json')->initContext();
			
            $leaveTypeId = $this->_getParam('LeaveType_Id');
    	    $leaveTypeId = $this->_validation->intValidation($leaveTypeId);
            
            if ((!empty($leaveTypeId['value']) && $this->_enforcementAccess['Update'] == 1 ) ||
				(empty($leaveTypeId['value']) && $this->_enforcementAccess['Add'] == 1 ))
            {
                if ($this->getRequest()->isPost())
                {
                    $formData = $this->getRequest()->getPost();
                    $enforcementConfiguration   = $this->_validation->intValidation($formData['Enforcement_Configuration']);
                    
                    if($enforcementConfiguration['value']==2)
                    {
                        $serviceId                  = $this->_validation->intValidation($formData['Service_Id']);
                        $serviceFrom                = $this->_validation->intValidation($formData['Service_From']);
                        $serviceTo                  = $this->_validation->intValidation($formData['Service_To']);
                        $serviceDays                = $this->_validation->daysValidation($formData['Service_Days']);
                                           
                        if($serviceTo['value'] == 0 || empty($serviceTo['value']))
                        {
                            $empserviceTo = new Zend_Db_Expr('NULL');
                        }
                        else
                        {
                            $empserviceTo = $serviceTo['value'];
                        }
                        
                        $employeeService  =   array('LeaveType_Id'		    => $leaveTypeId['value'],
                                                    'Service_Id'   	        => $serviceId['value'],
                                                    'Service_From'          => $serviceFrom['value'],
                                                    'Service_To'            => $empserviceTo,
                                                    'Total_Days'      	=> $serviceDays['value']);
                        
                        if ($leaveTypeId['valid'] && $leaveTypeId['value'] >= 0)
                        {
                            if ($serviceId['valid'] && $serviceId['value'] >= 0)
                            {
                                if ($serviceFrom['valid'] && $serviceFrom['value'] >= 0)
                                {
                                    if ($serviceTo['valid'] && $serviceTo['value'] >= 0)
                                    {
                                        if ($serviceDays['valid'] && $serviceDays['value'] >= 0)
                                        {
                                            $this->view->result = $this->_dbServiceLeave->updateEmployeeService($employeeService,$this->_logEmpId,'Employee Service');
                                        }
                                        else
                                        {
                                                $this->view->result = array('success' => false, 'msg'=>'Invalid Service days data', 'type'=>'info');
                                        }
                                    }
                                    else
                                    {
                                            $this->view->result = array('success' => false, 'msg'=>'Invalid Service To data', 'type'=>'info');
                                    }
                                }
                                else
                                {
                                        $this->view->result = array('success' => false, 'msg'=>'Invalid Service From data', 'type'=>'info');
                                }
                            }
                        }
                        else
                        {
                                $this->view->result = array('success' => false, 'msg'=>'Please Select valid Leave Type', 'type'=>'info');
                        }
                    }
                    

                    
                    if($enforcementConfiguration['value']==3)
                    {
                        $leaveQuarterId             = $this->_validation->intValidation($formData['Leave_Quarter_Id']);
                        $zeroLeaveFrom              = $this->_validation->intValidation($formData['Employment_Year_From']);
                        $zeroLeaveTo                = $this->_validation->intValidation($formData['Employment_Year_To']);
                        $period                     = $this->_validation->intValidation($formData['Period']);
                        
                        /**to save the value as null in table if value is not given**/ 
                        if($zeroLeaveTo['value'] == 0 || empty($zeroLeaveTo['value']))
                        {
                            $empzeroLeaveTo = new Zend_Db_Expr('NULL');
                        }
                        else
                        {
                            $empzeroLeaveTo = $zeroLeaveTo['value'];
                        }


                        $leaveQuarter = array('LeaveType_Id'		   => $leaveTypeId['value'],
                                                'Leave_Quarter_Id'	   => $leaveQuarterId['value'],
                                                'Period'                => $period['value'],
                                                'Employment_Year_From'  => $zeroLeaveFrom['value'],
                                                'Employment_Year_To'    => $empzeroLeaveTo);

                            $leaveJoinQuarter =             $formData['Emp_Join_Details'];
                        
                        
                        if ($leaveTypeId['valid'] && $leaveTypeId['value'] >= 0)
                        {
                            if ($leaveQuarterId['valid'] && $leaveQuarterId['value'] >= 0)
                            {
                                if ( $period['valid'] && $period['value'] >= 0)
                                {
                                    if ($zeroLeaveFrom['valid'] && $zeroLeaveFrom['value'] >= 0 )
                                    {
                                        if ($zeroLeaveTo['valid'] && $zeroLeaveTo['value'] >= 0)
                                        {
                                            $this->view->result = $this->_dbQuaterLeave->updateLeaveQuarter($leaveQuarter,$leaveJoinQuarter,$this->_logEmpId,'Leave Quarter');
                                        }
                                        else
                                        {
                                                $this->view->result = array('success' => false, 'msg'=>'Invalid Employment Year To data', 'type'=>'info');
                                        }
                                    }
                                    else
                                    {
                                            $this->view->result = array('success' => false, 'msg'=>'Invalid Employment Year From data', 'type'=>'info');
                                    }
                                }
                            }
                        }
                        else
                        {
                                $this->view->result = array('success' => false, 'msg'=>'Please Select valid Leave Type', 'type'=>'info');
                        }
                    }
                    
                    if($enforcementConfiguration['value']==4)
                    {
                        $maternitySlabId            = $this->_validation->intValidation($formData['Maternity_Slab_Id']);
                        $childFrom                  = $this->_validation->intValidation($formData['Child_From']);
                        $childTo                    = $this->_validation->intValidation($formData['Child_To']);
                        $maternityDays              = $this->_validation->daysValidation($formData['Maternity_Days']);

                        
                                                                        
                        if($childTo['value'] == 0 || empty($childTo['value']))
                        {
                            /**to save the value as null in table if value is not given**/ 
                            $empChildTo = new Zend_Db_Expr('NULL');
                        }
                        else
                        {
                            $empChildTo = $childTo['value'];
                        }
                        
                        
                        $maternitySlab    =   array('LeaveType_Id'		    => $leaveTypeId['value'],
                                                    'Maternity_Slab_Id'		=> $maternitySlabId['value'],
                                                    'Child_From'   	        => $childFrom['value'],
                                                    'Child_To'              => $empChildTo,
                                                    'Total_Days'        => $maternityDays['value']);
                            
                        if ($leaveTypeId['valid'] && $leaveTypeId['value'] >= 0)
                        {
                            if ($maternitySlabId['valid'] && $maternitySlabId['value'] >= 0)
                            {
                                if ($childFrom['valid'] && $childFrom['value'] >= 0)
                                {
                                    if ($childTo['valid'] && $childTo['value'] >= 0)
                                    {
                                        if ($maternityDays['valid'] && $maternityDays['value'] >= 0)
                                        {
                                            $this->view->result = $this->_dbMaternityLeave->updateMaternitySlab($maternitySlab,$this->_logEmpId,'Maternity Slab');
                                        }
                                        else
                                        {
                                                $this->view->result = array('success' => false, 'msg'=>'Invalid Maternity Days Data', 'type'=>'info');
                                        }
                                    }
                                    else
                                    {
                                            $this->view->result = array('success' => false, 'msg'=>'Invalid Child To data', 'type'=>'info');
                                    }
                                }
                                else
                                {
                                        $this->view->result = array('success' => false, 'msg'=>'Invalid Child From data', 'type'=>'info');
                                }
                            }
                        }
                        else
                        {
                                $this->view->result = array('success' => false, 'msg'=>'Please Select valid Leave Type', 'type'=>'info');
                        }
                        
                    }

                    /** Update experience based leave */
                    if($enforcementConfiguration['value']==5)
                    {
                        $experienceId                  = $this->_validation->intValidation($formData['Experience_Id']);
                        $experienceFrom                = $this->_validation->intValidation($formData['Experience_From']);
                        $experienceTo                  = $this->_validation->intValidation($formData['Experience_To']);
                        $experienceDays                = $this->_validation->daysValidation($formData['Experience_Days']);
                            
                        /**to save the value as null in table if value is not given**/ 
                        if($experienceTo['value'] == 0 || empty($experienceTo['value']))
                        {
                            $empExperienceTo = new Zend_Db_Expr('NULL');
                        }
                        else
                        {
                            $empExperienceTo = $experienceTo['value'];
                        }
                        
                        $employeeExperience  =   array('LeaveType_Id'		    => $leaveTypeId['value'],
                                                        'Experience_Id'   	    => $experienceId['value'],
                                                        'Experience_From'       => $experienceFrom['value'],
                                                        'Experience_To'         => $empExperienceTo,
                                                        'Total_Days'      	    => $experienceDays['value']);
                        
                        if ($leaveTypeId['valid'] && $leaveTypeId['value'] >= 0)
                        {
                            if ($experienceId['valid'] && $experienceId['value'] >= 0)
                            {
                                if ($experienceFrom['valid'] && $experienceFrom['value'] >= 0)
                                {
                                    if ($experienceTo['valid'] && $experienceTo['value'] >= 0)
                                    {
                                        /** If the experience to is given */
                                        if(!is_null($experienceTo['value']) && !empty($experienceTo['value'])){
                                            $empExpMaxLeaveDays = $experienceTo['value'] - $experienceFrom['value']; // maximum leave days
                                        }else{
                                            $empExpMaxLeaveDays = 365; // maximum leave days
                                        }

                                        if ($experienceDays['valid'] && $experienceDays['value'] >= 0
                                            && $experienceDays['value'] <= $empExpMaxLeaveDays)
                                        {

                                            $formName = 'Employee Experience';
                                            $isLvTypeNtExistInLeaves = $this->_dbLeave->getLeaveTypeUsed($employeeExperience['LeaveType_Id'],NULL);
    
                                            /** Validate the leaves are applied by the employees/leave balance imported/co leave exists 
                                             * for the selected experience record in the current leave closure year
                                             */
                                            if($isLvTypeNtExistInLeaves){
                                                $this->view->result = $this->_dbExperienceLeave->updateEmployeeExperience($employeeExperience,$this->_logEmpId,$formName);
                                            }else{
                                                $this->view->result = array('success'=>false, 'msg'=>$formName.' cannot be updated as the employees already added an leave for this leave type for the current leave year or deferred leaves may exist or leave balance may be imported.', 'type'=>'info');
                                            }
                                        }
                                        else
                                        {
                                            /** If the maximum leave days is valid */
                                            if(!$experienceDays['valid']){
                                                $expmaxLeaveDaysErrMsg = 'Invalid maximum leave days';
                                            }else{
                                                $expmaxLeaveDaysErrMsg = 'Maximum leave days should be greater than or equal to 0 and less than '.$empExpMaxLeaveDays;
                                            }

                                            $this->view->result = array('success' => false, 'msg'=>$expmaxLeaveDaysErrMsg, 'type'=>'info');
                                        }
                                    }
                                    else
                                    {
                                        $this->view->result = array('success' => false, 'msg'=>'Invalid Experience to days', 'type'=>'info');
                                    }
                                }
                                else
                                {
                                    $this->view->result = array('success' => false, 'msg'=>'Invalid Experience from days', 'type'=>'info');
                                }
                            }else{
                                $this->view->result = array('success' => false, 'msg'=>'Something went wrong. Please contact system admin', 'type'=>'warning');
                            }
                        }
                        else
                        {
                            $this->view->result = array('success' => false, 'msg'=>'Please Select valid Leave type', 'type'=>'info');
                        }
                    }
                }
            }
            else
            {
				$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'info');
            }
        }
        else
        {
                 $this->_helper->redirector('index', 'leaves', 'employees');
       }
    }
   
    public function deleteLeaveEnforcementAction()
    {
        $this->_helper->layout->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-leave-enforcement', 'json')->initContext();
            
            $uniqueId = $this->_getParam('Unique_Id', null);
            $uniqueId = filter_var($uniqueId, FILTER_SANITIZE_NUMBER_INT);
            
            $enforcementConfiguration = $this->_getParam('Enforcement_Configuration', null);
            $enforcementConfiguration = filter_var($enforcementConfiguration, FILTER_SANITIZE_NUMBER_INT);
            
            if (!empty($uniqueId) && $uniqueId > 0 && !empty($enforcementConfiguration) && $enforcementConfiguration > 0 && $this->_enforcementAccess['Delete'] == 1)
            {
                $this->view->result = $this->_dbLeave->deleteLeaveEnforcement($uniqueId,$enforcementConfiguration,$this->_logEmpId);
            }
            else
            {
                $this->view->result = array('success'=>false, 'msg'=>'Invalid data','type'=>'info');
            }
        }
        else
        {
           $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }

    public function listEmployeeServiceAction()
    {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            if ($this->_enforcementAccess['View'] == 1)
            {
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
                $ajaxContext->addActionContext('list-employee-service', 'json')->initContext();
		
                $leaveTypeId  	    = $this->_getParam('LeaveType_Id', null);
                $leaveTypeId        = filter_var($leaveTypeId, FILTER_SANITIZE_NUMBER_INT);
                        
                $this->view->result = $this->_dbServiceLeave->searchEmployeeService($leaveTypeId);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }
	
    public function listLeaveQuarterAction()
    {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            if ($this->_enforcementAccess['View'] == 1)
            {
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
                $ajaxContext->addActionContext('list-leave-quarter', 'json')->initContext();
        
                $leaveTypeId  	    = $this->_getParam('LeaveType_Id', null);
                $leaveTypeId        = filter_var($leaveTypeId, FILTER_SANITIZE_NUMBER_INT);
                        
                $this->view->result = $this->_dbQuaterLeave->searchLeaveQuarter($leaveTypeId);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
       
    }
    
    public function listMaternityLeaveAction()
    {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            if ($this->_enforcementAccess['View'] == 1)
            {
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
                $ajaxContext->addActionContext('list-maternity-leave', 'json')->initContext();
		
                $leaveTypeId  	    = $this->_getParam('LeaveType_Id', null);
                $leaveTypeId        = filter_var($leaveTypeId, FILTER_SANITIZE_NUMBER_INT);
                        
                $this->view->result = $this->_dbMaternityLeave->searchMaternityLeave($leaveTypeId);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }

    /** the list action for Employee Experience leave **/       
    public function listEmployeeExperienceAction()
    {
       $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            if ($this->_enforcementAccess['View'] == 1)
            {
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
                $ajaxContext->addActionContext('list-employee-experience', 'json')->initContext();
		
                $leaveTypeId  	    = $this->_getParam('LeaveType_Id', null);
                $leaveTypeId        = filter_var($leaveTypeId, FILTER_SANITIZE_NUMBER_INT);
                        
                $this->view->result = $this->_dbExperienceLeave->searchEmployeeExperience($leaveTypeId);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }
    
    public function listEnforcementTypeAction()
    {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            if ($this->_enforcementAccess['View'] == 1)
            {
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
                $ajaxContext->addActionContext('list-enforcement-type', 'json')->initContext();
		
                $leaveTypeId  	    = $this->_getParam('LeaveType_Id', null);
                $leaveTypeId        = filter_var($leaveTypeId, FILTER_SANITIZE_NUMBER_INT);
                        
                $this->view->result = $this->_dbLeave->searchEnforcementType($leaveTypeId);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }

    }
     public function listEnforcementLeaveAction()
    {
          $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            if ($this->_enforcementAccess['View'] == 1)
            {
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
                $ajaxContext->addActionContext('list-enforcement-type', 'json')->initContext();
		                
                $this->view->result = $this->_dbLeave->getLeaveEnforcementType();
            }
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }
    
    public function getEmployeeMonthFromAction()
    {
       $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('get-employee-month-from', 'json')->initContext();
			
            $leaveTypeId = $this->_getParam('LeaveType_Id', null);
            $leaveTypeId = filter_var($leaveTypeId, FILTER_SANITIZE_NUMBER_INT);

            $serviceFrom = $this->_getParam('Service_From', null);
            $serviceFrom = filter_var($serviceFrom, FILTER_SANITIZE_NUMBER_INT);
			
            $serviceTo = $this->_getParam('Service_To', null);
            $serviceTo = filter_var($serviceTo, FILTER_SANITIZE_NUMBER_INT);
            
            $leaveType=$this->_getParam('leaveType', null);
            $leaveType = filter_var($leaveType, FILTER_SANITIZE_STRIPPED);

            $childFrom = $this->_getParam('Child_From', null);
            $childFrom = filter_var($childFrom, FILTER_SANITIZE_NUMBER_INT);
			
            $childTo = $this->_getParam('Child_To', null);
            $childTo = filter_var($childTo, FILTER_SANITIZE_NUMBER_INT);

            $Employment_Year_From = $this->_getParam('Employment_Year_From', null);
            $Employment_Year_From   = filter_var($Employment_Year_From, FILTER_SANITIZE_NUMBER_INT);

            $Employment_Year_To = $this->_getParam('Employment_Year_To', null);
            $Employment_Year_To = filter_var($Employment_Year_To, FILTER_SANITIZE_NUMBER_INT);

            $Experience_From = $this->_getParam('Experience_From', null);
            $Experience_From   = filter_var($Experience_From, FILTER_SANITIZE_NUMBER_INT);

            $Experience_To = $this->_getParam('Experience_To', null);
            $Experience_To = filter_var($Experience_To, FILTER_SANITIZE_NUMBER_INT);

            $leaveType=$this->_getParam('leaveType', null);
            $leaveType = filter_var($leaveType, FILTER_SANITIZE_STRIPPED);
            
            if($leaveType=='EmployeeService')
            {
             $this->view->result = $this->_dbServiceLeave->getEmployeeServiceStartFrom($leaveTypeId, $serviceFrom, $serviceTo);
            }
            if($leaveType=='MaternityLeave')
            {
                $this->view->result = $this->_dbServiceLeave->getEmployeeMaternityStartFrom($leaveTypeId, $childFrom, $childTo);
            }
            if($leaveType=='QuarterLeave')
            {
                $this->view->result = $this->_dbServiceLeave->getEmployeeQuarterStartFrom($leaveTypeId, $Employment_Year_From, $Employment_Year_To);
            }

            if($leaveType=='ExperienceLeave')
            {
                $this->view->result = $this->_dbServiceLeave->getEmployeeExperienceStartFrom($leaveTypeId, $Experience_From, $Experience_To);
            }
        }
    }	

   
    public function listLeaveJoinQuarterAction()
    {
       
    }


    public function updateCustomgroupempLeaveBalanceAction()
    {
        $this->_helper->layout()->disableLayout();

		$ajaxContext = $this->_helper->getHelper('AjaxContext');
		$ajaxContext->addActionContext('update-customgroupemp-leave-balance', 'json')->initContext();
        
        if ($this->getRequest()->isPost()){
            $formData = $this->getRequest()->getPost();

            $body = $this->getRequest()->getRawBody();
            $formData = Zend_Json::decode($body);

            $employeeIds = $formData['employeeIds'];

            $loginEmpId = $formData['loginEmpId'];
            $loginEmpId = filter_var($loginEmpId, FILTER_SANITIZE_NUMBER_INT);
            
            if(!empty($loginEmpId)){
                /* Get the custom employee group access rights */
                $leaveAccessRights = $this->_dbAccessRights->employeeAccessRights($loginEmpId, 'Custom Employee Groups');

                if(!empty($leaveAccessRights)){
                    if($leaveAccessRights['Employee']['Update'] == 1){
                
                        if(!empty($employeeIds) && is_array($employeeIds)){
                            /* Validate and update the leave balance for custom group leave type */
                            $updateCustomGroupLeaveBalance	= $this->_dbLeave->validateUpdateCustomGroupLTEligibleDays('refreshCustomGroup',NULL,$employeeIds,'CustomEmployeeGroup');

                            if(!empty($updateCustomGroupLeaveBalance) && count($updateCustomGroupLeaveBalance) > 0){
                                $this->view->result = array('Success'=> true, 'updateResult' => $updateCustomGroupLeaveBalance['eligDaysDaysResult'], 'updateDetails'=>$updateCustomGroupLeaveBalance);
                            }else{
                                $this->view->result = array('Success'=> false, 'updateResult' => 0, 'updateDetails' => array());
                            }
                            
                        }else{
                            $this->view->result = array('Success'=> false, 'updateResult' => 0, 'updateDetails' => array());
                        }
                    }else{
                        $this->view->result = array('Success'=> false, 'updateResult' => 0, 'updateDetails' => array());
                    }
                }else{
                    $this->view->result = array('Success'=> false, 'updateResult' => 0, 'updateDetails' => array());
                }
            }else{
                $this->view->result = array('Success'=> false, 'updateResult' => 0, 'updateDetails' => array());
            }
        }else{
            $this->view->result = array('Success'=> false, 'updateResult' => 0, 'updateDetails' => array());
        }
    }

    public function validateCustomgroupempLeavesAction()
    {
        $this->_helper->layout()->disableLayout();

		$ajaxContext = $this->_helper->getHelper('AjaxContext');
		$ajaxContext->addActionContext('validate-customgroupemp-leaves', 'json')->initContext();
        
        if ($this->getRequest()->isPost()){
            $formData = $this->getRequest()->getPost();

            $body = $this->getRequest()->getRawBody();
            $formData = Zend_Json::decode($body);

            $customGroupId = $formData['groupId'];
            $customGroupId = filter_var($customGroupId, FILTER_SANITIZE_NUMBER_INT);

            $loginEmpId = $formData['loginEmpId'];
            $loginEmpId = filter_var($loginEmpId, FILTER_SANITIZE_NUMBER_INT);
            
            if(!empty($loginEmpId)){
                /* Get the custom employee group access rights */
                $leaveAccessRights = $this->_dbAccessRights->employeeAccessRights($loginEmpId, 'Custom Employee Groups');

                if(!empty($leaveAccessRights)){
                    if($leaveAccessRights['Employee']['Update'] == 1){
                        if(!empty($customGroupId)){
                            /* Validate and update the leave balance for custom group leave type */
                            $validateCustomGroupLeaves	= $this->_dbLeave->validateCustomGroupEmpLeaves($customGroupId);

                            if(!empty($validateCustomGroupLeaves) && count($validateCustomGroupLeaves)>0){
                                $this->view->result = array('Success'=> $validateCustomGroupLeaves['success'], 'validationResult' => $validateCustomGroupLeaves);
                            }else{
                                $this->view->result = array('Success'=> false, 'validationResult' => array());
                            }
                        }else{
                            $this->view->result = array('Success'=> false, 'validationResult' => array());
                        }
                    }else{
                        $this->view->result = array('Success'=> false, 'validationResult' => array());
                    }
                }else{
                    $this->view->result = array('Success'=> false, 'validationResult' => array());
                }
            }else{
                $this->view->result = array('Success'=> false, 'validationResult' => array());
            }
        }else{
            $this->view->result = array('Success'=> false, 'validationResult' => array());
        }
    }

    public function updateEmployeeLeaveBalanceAction()
    {
        $this->_helper->layout()->disableLayout();

		$ajaxContext = $this->_helper->getHelper('AjaxContext');
		$ajaxContext->addActionContext('update-employee-leave-balance', 'json')->initContext();
        
        if ($this->getRequest()->isPost()){
            $formData = $this->getRequest()->getPost();

            $body = $this->getRequest()->getRawBody();
            $formData = Zend_Json::decode($body);

            $employeeId = $formData['employeeId'];
            $employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);

            $updateProbationEmployeeLeaveBalance = isset($formData['updateProbationEmployeeLeaveBalance']) ? $formData['updateProbationEmployeeLeaveBalance'] : 0;
            $dateOfJoin = $formData['dateOfJoin'];
            $probationDate = $formData['probationDate'];
            
            if(!empty($employeeId) && !empty($dateOfJoin) && !empty($probationDate) && (in_array($updateProbationEmployeeLeaveBalance,[0,1]))){
                //Call the function to update the employee leave balance
                $leaveBalanceUpdated = $this->_dbLeave->empDOJUpdateEligibleDays($employeeId,'update-employee-leave-balance',NULL,NULL,$updateProbationEmployeeLeaveBalance);

                //If the leave balance is updated
                if(!empty($leaveBalanceUpdated)){
                    $this->view->result = array('Success'=> true, 'message' => 'Leave balance updated successfully.');
                }else{
                    $this->view->result = array('Success'=> false, 'message' => 'Unable to update the leave balance.');
                }
                
            }else{
                $this->view->result = array('Success'=> false, 'message' => 'Invalid input.');
            }
        }else{
            $this->view->result = array('Success'=> false,'message' => 'Invalid request.');
        }
    }

    public function getLeaveClosureDatesAction()
    {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('get-leave-closure-dates', 'json')->initContext();

            $leaveClosureMonth      = $this->_getParam('Leave_Closure_Month', null);
            $leaveClosureMonth      = filter_var($leaveClosureMonth, FILTER_SANITIZE_STRIPPED);
            $leaveClosureYear       = $this->_getParam('Leave_Closure_Year', null);
            $leaveClosureYear       = filter_var($leaveClosureYear, FILTER_SANITIZE_STRIPPED);
            if(!empty($leaveClosureMonth) && !empty($leaveClosureYear))
            {
                $leaveClosureDates    = $this->_dbLeave->getSelectedMonthLeaveClosureDates($leaveClosureMonth,$leaveClosureYear,'add-leave-type');
                $orgDF                = $this->_ehrTables->orgDateformat();
                $leaveClosureDates['finstart'] = date($orgDF['php'],strtotime($leaveClosureDates['finstart']));
				$leaveClosureDates['finend']   = date($orgDF['php'],strtotime($leaveClosureDates['finend']));
                $this->view->result = array('success'=> true, 'Leave_Closure_Date' => $leaveClosureDates);
            }
            else
            {
                $this->view->result = array('success'=>false, 'msg'=>'Invalid data','type'=>'info');
            }
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }

    public function getWorkflowAssociatedWithLeaveAction()
    {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('get-workflow-associated-with-leave', 'json')->initContext();
            $workflowList   = $this->_dbCommonFun->getWorkflowsBasedOnForm("31"); // leave form id is 31
            $defaultWorkflow = $this->_dbCommonFun->getDefaultWorkflowBasedOnForm("31");
            $this->view->result = array('success'=> true, 'workflows' => $workflowList, 'defaultWorkflow' => $defaultWorkflow);
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }

    public function exportLeaveHistoryAction()
    {
        $this->_helper->layout()->disableLayout();
        if(isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('export-leave-history', 'json')->initContext();
            $data = $this->_dbLeave->callLeaveBalanceApi();
            if(!empty($data))
            {
                $reportHeader               = array('Employee Id','Name','Department','Leave Name','Date Of Join','Total Eligibility(Per Annum)','Leaves Taken','Leave Balance(Per Annum)','Current Year Eligibility','Carry Over Days','Carry Over Balance','Leave Eligiblity Based On Period','Leave Balance Based On Period','Total Leave Balance Based On Period','Leave Closure Start Date','Leave Closure End Date');
                $organizationDetail 		= $this->_orgDetails;
                $showReportCreator 			= $organizationDetail['Show_Report_Creator'];
                $organizationName 			= strtoupper($organizationDetail['Org_Name']);
                $dbHRReport 				= new Reports_Model_DbTable_HrReports();
                $startColumnName 			= 0;
                $endColumnNamePosition   	= count($reportHeader);
                $startColumnName 			= $dbHRReport->getNameFromNumber($startColumnName);
                $endColumnName   			= $dbHRReport->getNameFromNumber($endColumnNamePosition-1);
                $autoResizeColumnName   	= $dbHRReport->getNameFromNumber($endColumnNamePosition);
                $freezeFrom  			  	= 2;   
                $freezePositionColumnName 	= $dbHRReport->getNameFromNumber($freezeFrom);

                $rowStart 						= 1;
                $organizationNameStartPosition 	= $startColumnName.$rowStart;
                $organizationNameEndPosition 	= $endColumnName.$rowStart;
                $rowStart++;
                $reportNameStartPosition 		= $startColumnName.$rowStart;
                $reportNameEndPosition 		    = $endColumnName.$rowStart;
                $rowStart++;

                $titleStyle = array(
                    'font' => array(
                        'bold' => true
                    ),
                    'borders' => array(
                        'allBorders' => array(
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                            'color' => array('argb' => '00000000'),
                        ),
                    ),
                );

                $dataBorder = array(
                    'borders' => array(
                        'allBorders' => array(
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                            'color' => array('argb' => '00000000'),
                        ),
                    )
                );

                $headerStyle = array(
                    'font' => array(
                        'bold' => true
                    ),
                    'alignment' => array(
                        'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,	
                    ),
                    'borders' => array(
                        'allBorders' => array(
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                            'color' => array('argb' => '00000000'),
                        ),
                    ),
                    'fill' => array(
                        'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                        'startColor' => array('argb' => '92CDDC')
                    )
                );

                $reportTitle = 'Leave Balance';
                $spreadsheet = new Spreadsheet();
                $activeSheet = $spreadsheet->getActiveSheet();
                $fontSize = array('font'  => array('size'  => 10));
                $spreadsheet->getDefaultStyle()->applyFromArray($fontSize);
                $spreadsheet->setActiveSheetIndex(0)->setCellValue("$organizationNameStartPosition", $organizationName)->setCellValue("$reportNameStartPosition", $reportTitle);
                $activeSheet->mergeCells("$organizationNameStartPosition:$organizationNameEndPosition");
                $activeSheet->mergeCells("$reportNameStartPosition:$reportNameEndPosition");

                if($showReportCreator==1)
                {
                    $createdBy  = $this->_dbPersonal->employeeName($this->_logEmpId);
                    $createdBy 	= 'Created by - '.$createdBy['Employee_Name'];
                    $createdOn 	= 'Created On - '.date($this->_orgDateFormat['php'] .' \a\t H:i:s');

                    $createdByStartPosition = $startColumnName.$rowStart;
                    $createdByEndPosition 	= $endColumnName.$rowStart;
                    $rowStart++;
                    $createdOnStartPosition = $startColumnName.$rowStart;
                    $createdOnEndPosition 	= $endColumnName.$rowStart;
                    $rowStart++;
                    $spreadsheet->setActiveSheetIndex(0)->setCellValue("$createdByStartPosition", $createdBy)->setCellValue("$createdOnStartPosition", $createdOn);
                    $activeSheet->mergeCells("$createdByStartPosition:$createdByEndPosition");
                    $activeSheet->mergeCells("$createdOnStartPosition:$createdOnEndPosition");
                    $activeSheet->getStyle("$createdByStartPosition:$createdByEndPosition")->applyFromArray($titleStyle);
                    $activeSheet->getStyle("$createdOnStartPosition:$createdOnEndPosition")->applyFromArray($titleStyle);
                }
                $rowStart++;
                $header	= array();
                foreach($reportHeader as $keys)
                {
                    $keyVal = str_replace(' ', '_', $keys);
                    $header[] = $keyVal; 	
                }
                $activeSheet->fromArray($reportHeader, null, $startColumnName.$rowStart);	
                $headerFrom = $startColumnName.$rowStart;
                $headerTo   = $endColumnName.$rowStart; 
                $activeSheet->getStyle("$headerFrom:$headerTo")->applyFromArray($headerStyle);
                $activeSheet->getStyle("$organizationNameStartPosition:$organizationNameEndPosition")->applyFromArray($titleStyle);
                $activeSheet->getStyle("$reportNameStartPosition:$reportNameStartPosition")->applyFromArray($titleStyle);
                $rowStart++;
                $freezePosition 		  = $freezePositionColumnName.$rowStart;
                $activeSheet->freezePane($freezePosition); 

                foreach ($data as $row)
                {
                    $newRow = array();
                    foreach($header as $k)
                    {
                        if(isset($row[$k]))
                        {
                            if($row[$k] == '0')
                            {
                                $newRow[$k] = '0.00';
                            }
                            elseif($row[$k] == '-')
                            {
                                $newRow[$k] = '--';
                            }
                            else
                            {
                                $row[$k] = str_replace("\n", '', $row[$k]);
                                $newRow[$k] = $row[$k];
                            }
                        }
                        else 
                        {
                            $newRow[$k] = '--';
                        }
                        $dataFrom = $startColumnName.$rowStart;
                        $dataTo   = $endColumnName.$rowStart; 
                        $activeSheet->getStyle("$dataFrom:$dataTo")->applyFromArray($dataBorder);
                    }
                    $activeSheet->fromArray($newRow, null, $startColumnName.$rowStart);
                    $dbHRReport->convertExcelSheetNumberFormatToString($reportHeader,$activeSheet,$rowStart);
                    $rowStart++;	
                }

                $activeSheetTitleLength = strlen($reportTitle);
                if($activeSheetTitleLength > 31)
                {
                    $activeSheetTitle = substr($reportTitle, 0, 31);
                }
                else
                {
                    $activeSheetTitle = $reportTitle;
                }

                $activeSheet->setTitle($activeSheetTitle);
                // Set active sheet index to the first sheet, so Excel opens this as the first sheet
                $spreadsheet->setActiveSheetIndex(0);

                for($col = $startColumnName ; $col !== $autoResizeColumnName; $col++)
                {
                    $activeSheet->getColumnDimension($col)->setAutoSize(true);
                }

                ob_end_clean();
                // Redirect output to a client’s web browser (Xlsx)
                header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
                header('Content-Disposition: attachment;filename='.$reportTitle.'.xlsx');
                header('Cache-Control: max-age=0');
                // If you're serving to IE 9, then the following may be needed
                header('Cache-Control: max-age=1');

                // If you're serving to IE over SSL, then the following may be needed
                header('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // Date in the past
                header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // always modified
                header('Cache-Control: cache, must-revalidate'); // HTTP/1.1
                header('Pragma: public'); // HTTP/1.0
                ob_end_clean();
                $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
                $writer->save('php://output');
                exit;
            }else{
                $this->view->result = array('success'=> false, 'message' => 'There seem to be some technical difficulties. Please try after some time');
            }
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
    }

    public function monthlyLeaveAccrualAction()
	{
		$this->_helper->layout()->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            if($this->_leaveAccessRights['Admin']==='admin' || $this->_myTeamAccessRights['Admin']==='admin')
            {
                $this->_dbPayslip  = new Payroll_Model_DbTable_Payslip();
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
                $ajaxContext->addActionContext('monthly-leave-accrual', 'json')->initContext();
                
                $leaveAccrualMonth = $this->_getParam('leaveAccrualMonth', 0);
                $leaveAccrualMonth = filter_var($leaveAccrualMonth, FILTER_SANITIZE_STRIPPED);
                
                if(!empty($leaveAccrualMonth))
                {
                    $month 						   		= explode(',', $leaveAccrualMonth);
                    $paycyleDate 				    	= $this->_dbPayslip->getSalaryDay($month[0], $month[1]);
                    $startDate                          = $paycyleDate['Salary_Date'];
                    $endDate                            = $paycyleDate['Last_SalaryDate'];
                    $actualSubTab                       = 'noAttTab';
                    $filterArray['employeeType'] 		= '';
                    $filterArray['location'] 			= '';
                    $filterArray['department'] 			= '';
                    $filterArray['payslipEmployeeIds'] 	= '';
                    $filterArray['employeeStatus'] 		= array('Active','InActive');
                    $filterArray['startDate'] 			= $startDate;
                    $filterArray['endDate'] 			= $endDate;
                    $finalizationMethod 				='noAttendance';
                    $employeeDetails 					= $this->_dbCommonFun->listEmployeesDetails($finalizationMethod, '', $this->_logEmpId, '', null,$filterArray);
                    if(count($employeeDetails) > 0)
                    {
                        $attendanceEnforcedEmployeeId 		= array_column($employeeDetails,'Employee_Id');
                        $attendanceEnforcedEmployeeDetails 	= $this->_dbPayslip->getAttendanceEnforcePaymentEnabledEmployeeId($attendanceEnforcedEmployeeId,$startDate,$endDate,$finalizationMethod);
                        if(count($attendanceEnforcedEmployeeDetails)>0)
                        {
                            $empAbsentDetails = $this->_dbPayslip->getAbsentEmployeeDetails($attendanceEnforcedEmployeeDetails,$startDate,$endDate,$finalizationMethod,$actualSubTab);
                            $empAbsentDetails = array_reduce($empAbsentDetails, 'array_merge', array());
                            if(count($empAbsentDetails) > 0)
                            {
                                $this->view->result = array('success'=>false, 'msg'=>'Please complete the attendance finalization before doing the monthly leave accrual.','type'=>'info');
                            }
                            else
                            {
                                $this->view->result = $this->_dbLeave->updateMonthlyLeaveAccrual($month[0],$month[1]);
                            }
                        }
                        else
                        {
                            $this->view->result = array('success'=>false, 'msg'=>'Unable to retrive attendance enforce payment enabled employee details','type'=>'info');
                        }
                    }
                    else
                    {
                        $this->view->result = array('success'=>false, 'msg'=>'Unable to retrive employee details','type'=>'info');
                    }
                }
                else
                {
                    $this->view->result = array('success'=>false, 'msg'=>'Please select leave accrual month','type'=>'info');
                }
            }
            else
            {
                $this->view->result = array('success' => false, 'msg' => 'Sorry, Access denied', 'type' => 'warning');
            }
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
        }
	}


    //Function to validate earnings and deduction title is a reserved keyword or not
	public function callUpdateLeavesAction()
    {
        $this->_helper->layout()->disableLayout();

        //We do not have an option to run this block in the batch with the automatic sign in so comment this till the solution is implemented
        // if (isset($_SERVER['HTTP_REFERER']))
        // {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('call-update-leaves', 'json')->initContext();
            
            $leaveData = $this->_getParam('leaveData', null);
            $source = $this->_getParam('source', null);
            $leaveId = $this->_getParam('leaveId', 0);
            $earlyCheckoutLeave = $this->_getParam('earlyCheckoutLeave', 0);
            $earlyCheckoutHours = $this->_getParam('earlyCheckoutHours', '');
            $earlyCheckoutHoursFromGrace = $this->_getParam('earlyCheckoutHoursFromGrace', '');
            $earlyCheckoutAlwaysGraceHours = $this->_getParam('earlyCheckoutAlwaysGraceHours', '');
            if(!empty($leaveData)){
                $additionalDetails = array(   
                    'earlyCheckoutLeave' => $earlyCheckoutLeave,
                    'earlyCheckoutHours' => $earlyCheckoutHours,
                    'earlyCheckoutHoursFromGrace' => $earlyCheckoutHoursFromGrace,
                    'earlyCheckoutAlwaysGraceHours' => $earlyCheckoutAlwaysGraceHours
                );

                $result = $this->_dbLeave->updateLeave ($leaveData, array(), 1, "", $leaveId, "", array(),"","", "",'No',$source,$additionalDetails);
                $this->view->result = array('result' => $result,'message'=>'');
            } else{
                $this->view->result = array('result' => '','message'=>'Invalid inputs');
            }
        // }
        // else
        // {
        //     $this->_helper->redirector('index', 'leaves', 'employees');
		// }    
    }


    //Function to validate earnings and deduction title is a reserved keyword or not
	public function callDeleteLeavesAction()
    {
        $this->_helper->layout()->disableLayout();

        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('call-delete-leaves', 'json')->initContext();
            
            $leaveIds = $this->_getParam('leaveIds', 0);
            $deleteLeaveIds = array();
            if(!empty($leaveIds)){
                $allLeaveDetails =  $this->_dbLeave->getLeaveDetails($leaveIds);

                if($allLeaveDetails && count($allLeaveDetails) > 0){
                    $dbHRReport = new Reports_Model_DbTable_HrReports();
                    $organizeLeaveDetails = $dbHRReport->organizeDataByEmployeeIdAndDate($allLeaveDetails,'Leave_Id');
                    foreach($leaveIds as $val)
                    {
                        $leaveArray = $dbHRReport->ensureArray($organizeLeaveDetails, $val);
                        if($leaveArray && isset($leaveArray[0])){
                            $inputLeaveDetails = $leaveArray[0];
                            if($inputLeaveDetails['Approval_Status'] === 'Approved' || $inputLeaveDetails['Approval_Status'] === 'Cancel Applied'){
                                $this->_dbLeave->cancelLeaveBalance($inputLeaveDetails['Total_Days'], $inputLeaveDetails['Employee_Id'], $inputLeaveDetails['LeaveType_Id']);
                            }
                            $result = $this->_dbLeave->deleteLeave($val, $this->_logEmpId, $this->_formNameA, $this->_customFormNameA);
                            if($result['success']){
                                array_push($deleteLeaveIds,$val);
                            }
                        }
                    }
                    $this->view->result = array('message'=>'','deleteLeaveIds' => $deleteLeaveIds);
                }else{
                    $this->view->result = array('message'=>'Leave details not found','deleteLeaveIds' => $deleteLeaveIds);
                }
            } else{
                $this->view->result = array('message'=>'Invalid inputs', 'deleteLeaveIds' => $deleteLeaveIds);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'leaves', 'employees');
		}    
    }

    public function __destruct()
    {
        
    }
}