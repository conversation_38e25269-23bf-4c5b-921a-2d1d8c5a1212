<?php
//=========================================================================================
//=========================================================================================
/* Program : Leave.php									                                 *		   				
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.							                             *								
 * All Rights Reserved.            							                             *							
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MQL Query to retrive, add, update employee leaves and also to           *
 * update status reports.								                                 *								
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Shobana	              Initial Version        	         *
 *  0.2        04-Jan-2014    Mahesh                  Added Function                     *
 *                                                    1.getPendingLeaveRequest           *
 *                                                    Modified Function                  *
 *                                                    1.coEncashmentExists               *
 *                                                    2.encashLeaveTypes                 *
 *  0.3        10-Apr-2014    Mahesh                  Added Display in payslip field     *
 *                                                    in add,update and view           	 *
 *											                                             *
 *  1.0        02-Feb-2015    Saranya                 Changes in file for mobile app     *
 *                                                    1.Extra fields are added in        *
 *                                                    field list of list query.          *
 *                                                                                       *
  *  1.5        16-Feb-2016    Suganya               Changed in file for Bootstrap       *
 *                                                                                       */
//=========================================================================================
//=========================================================================================
class Employees_Model_DbTable_Leave extends Zend_Db_Table_Abstract
{
    protected $_dbPersonal    = null;
    protected $_dbFinancialYr = null;
    protected $_dbComment     = null;
    protected $_orgDF         = null;
    protected $_db            = null;
    protected $_ehrTables     = null;
    protected $_dbJob         = null;
	protected $_dbCommonFun   = null;
	protected $_dbDept        = null;
	protected $_dbLocation    = null;
	protected $_dbEmpType     = null;
	protected $_dbServiceLeave   = null;
	protected $_dbQuaterLeave    = null;
	protected $_dbExperienceLeave = null;
	protected $_orgDetails 		  = null;
	protected $_logEmpId          = null;
	
    public function init()
    {
	    $this->_ehrTables     = new Application_Model_DbTable_Ehr();
        $this->_db            = Zend_Registry::get('subHrapp');
        $this->_dbPersonal    = new Employees_Model_DbTable_Personal();
        $this->_dbFinancialYr = new Default_Model_DbTable_FinancialYear();
        $this->_dbComment     = new Payroll_Model_DbTable_PayrollComment();
        $this->_dbJob         = new Employees_Model_DbTable_JobDetail();
		$this->_dbCommonFun   = new Application_Model_DbTable_CommonFunction();		
		$this->_dbServiceLeave = new Employees_Model_DbTable_ServiceBasedLeave();
		$this->_dbQuaterLeave = new Employees_Model_DbTable_QuarterWiseLeave();
		$this->_dbExperienceLeave = new Employees_Model_DbTable_ExperienceBasedLeave();
		$this->_dbDept     	  = new Organization_Model_DbTable_Department();
		$this->_dbLocation 	  = new Organization_Model_DbTable_Location();
		$this->_dbEmpType  	  = new Employees_Model_DbTable_EmployeeType();
		$this->_orgDF         = $this->_ehrTables->orgDateformat();
		$userSession          = $this->_dbCommonFun->getUserDetails ();
		$this->_logEmpId      = $userSession?$userSession['logUserId']:1;
		if (Zend_Registry::isRegistered('orgDetails'))
			$this->_orgDetails = Zend_Registry::get('orgDetails');
    }
	
	/********************************************** Leaves ********************************************************/
	
	/**
	 *	List Leave Details
	 */
	public function listLeaves ($page, $rows, $sortField, $sortOrder, $searchAll, $searchArr, $leaveUser)
    {
		$sessionId      = $leaveUser['SessionId'];
		$employeeName   = $searchArr['Employee_Name'];
		$leaveName      = $searchArr['Leave_Name'];
		$leaveStartDate = $searchArr['Leave_Start_Date'];
		$leaveEndDate   = $searchArr['Leave_End_Date'];
		$status         = $searchArr['Status'];
		$lateAttendance = $searchArr['Late_Attendance'];
		$serviceProviderId = $searchArr['Service_Provider_Id'];
		$locationId = $searchArr['Location_Id'];
		$departmentId = $searchArr['Department_Id'];
		$designationId = $searchArr['Designation_Id'];
		$workScheduleId = $searchArr['Work_Schedule'];
		$managerId = $searchArr['Manager_Id'];
		$earlyCheckoutLeave = $searchArr['Early_Checkout_Leave'];

		if (empty($leaveStartDate) && empty($leaveEndDate)) {
			$leaveStartDate = date('Y-01-01');
			$leaveEndDate   = date('Y-12-31');
		}
		
        /**
		 *	Sorting columns based on display column order in grid
		*/
		switch ($sortField)
		{
			case 1: $sortField = 'EJ.User_Defined_EmpId';break;
            case 2: $sortField = 'emp.Emp_First_Name'; break;
			case 3: $sortField = 'leave.Leave_Name'; break;
			case 4: $sortField = 'L.Start_Date'; break;
			case 5: $sortField = 'L.End_Date'; break;
			case 6: $sortField = 'L.Total_Days'; break;
			case 7: $sortField = 'L.Approval_Status'; break;
		}
		
		$qryLeaves = $this->_db->select()
							->from(array('L'=>$this->_ehrTables->empLeaves),
								   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS L.Leave_Id as count'), 'L.Leave_Id', 'L.Employee_Id',
										 'L.Reason', 'L.Approval_Status', 'L.Duration as Leave_Duration', 'L.Approver_Id', 'L.Total_Days', 
										 'L.Leave_Period', 'L.Hours', 'L.Added_By', 'L.Start_Date', 'L.End_Date','L.Attendance_Shortage',
										 'L.Contact_Details', 'L.Approval_Status', 'L.LeaveType_Id', 'L.Reason_Id', 'L.Added_By','L.Approved_By',
										 'L.Late_Attendance','L.Late_Attendance_Hours','L.Late_Attendance_Hours_From_Grace','L.Always_Grace',
										 new Zend_Db_Expr("DATE_FORMAT(L.Start_Date,'".$this->_orgDF['sql']."') as StartDate"),
										 new Zend_Db_Expr("DATE_FORMAT(L.End_Date,'".$this->_orgDF['sql']."') as EndDate"),
										 new Zend_Db_Expr("DATE_FORMAT(L.Added_On,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
										 new Zend_Db_Expr("DATE_FORMAT(L.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On"),
                                          new Zend_Db_Expr("DATE_FORMAT(L.Approved_On,'".$this->_orgDF['sql']." %H:%i:%s') as Approved_On"),
										 'Log_Id'=>new Zend_Db_Expr("'".$sessionId."'"),
										 'Role'=>new Zend_Db_Expr("'".$leaveUser['Admin']."'"),
										 'Update'=> new Zend_Db_Expr("'".$leaveUser['Update']."'"),
										 'Curr_Year'=>new Zend_Db_Expr('(CASE WHEN (`L`.`Start_Date` >= `EL`.`Leave_Closure_Start_Date` AND `L`.`Start_Date` <= `EL`.`Leave_Closure_End_Date`) THEN \'Current\' ELSE \'\' END)'),
										 'DT_RowClass' => new Zend_Db_Expr('"leaveType"'),
										 'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', L.LeaveType_Id)")))
							
							->joinInner(array('leave'=>$this->_ehrTables->leavetype),'leave.LeaveType_Id=L.LeaveType_Id',
										array('leave.Enable_Leave_Exception','leave.Leave_Name', 'leave.Leave_Type', 'leave.Show_Statistics_In_Dashboard','leave.Restrict_Employee_To_Apply','leave.Leave_Unit'))

							->joinLeft(array('EAS'=>$this->_ehrTables->employeeAttendanceSummary),'L.Leave_Id=EAS.Early_Checkout_Leave_Id',
										array('EAS.Early_Checkout_Hours','Early_Checkout' => new Zend_Db_Expr('CASE WHEN EAS.Early_Checkout_Leave_Id > 0 THEN "Yes" ELSE "No" END')))
							
							->joinInner(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=L.Employee_Id',
										array(new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as Employee_Name"),
											  'emp.Employee_Id'))
                            
                            ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'emp.Employee_Id=EJ.Employee_Id',
                                            array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN emp.Employee_Id ELSE EJ.User_Defined_EmpId END'),'EJ.Department_Id'))

							->joinLeft(array('EL'=>$this->_ehrTables->empEligbleLeave),"EL.Employee_Id = L.Employee_Id AND leave.LeaveType_Id = EL.LeaveType_Id",array('EL.Leave_Closure_Start_Date','EL.Leave_Closure_End_Date'))				
							
							->joinInner(array('emp1'=>$this->_ehrTables->empPersonal),'emp1.Employee_Id=L.Approver_Id',
										array(new Zend_Db_Expr("CONCAT(emp1.Emp_First_Name, ' ', emp1.Emp_Last_Name) as ApproverName")))

							->joinLeft(array('esicReason'=>$this->_ehrTables->esicReason),'esicReason.Reason_Id=L.Reason_Id',
									   array('esicReason.ESIC_Reason'))
							
							->joinLeft(array('EB'=>$this->_ehrTables->empPersonal),'EB.Employee_Id=L.Added_By',
									   array('Added_By_Name'=>new Zend_Db_Expr("CONCAT(EB.Emp_First_Name, ' ', EB.Emp_Last_Name)")))
							
							->joinLeft(array('UE'=>$this->_ehrTables->empPersonal),'UE.Employee_Id=L.Updated_By',
									   array('Updated_By_Name'=>new Zend_Db_Expr("CONCAT(UE.Emp_First_Name, ' ', UE.Emp_Last_Name)")))
							
							->joinInner(array('des'=>$this->_ehrTables->designation),'des.Designation_Id = EJ.Designation_Id',
										array('des.Designation_Name'))
							
							->joinLeft(array('hours'=>$this->_ehrTables->timesheetHrs), 'hours.Grade_Id = des.Grade_Id',
									   array('hours.Regular_Hours'))
							
							->joinInner(array('u'=>$this->_ehrTables->unit),'L.Duration=u.Target_Value AND u.Target_Unit="day"',array('u.Unit_Tag as Duration'))
                            
                            ->joinLeft(array('emp3'=>$this->_ehrTables->empPersonal),'emp3.Employee_Id=L.Approved_By',
									   array(new Zend_Db_Expr("CONCAT(emp3.Emp_First_Name, ' ', emp3.Emp_Last_Name) as Approved_By_Name")))

							->joinInner(array('DP'=>$this->_ehrTables->dept), 'EJ.Department_Id = DP.Department_Id', array('DP.Department_Name'))
							
							->joinLeft(array('AP'=>$this->_ehrTables->alternatePerson),'AP.Leave_Id=L.Leave_Id',
                                                       array('Alternate_Person_Id'=>new Zend_Db_Expr('GROUP_CONCAT(DISTINCT AP.Alternate_Person order by AP.Alternate_Person Asc)')))

							->joinLeft(array('AEP'=>$this->_ehrTables->empPersonal),'AEP.Employee_Id=AP.Alternate_Person',
									array('Alternate_Person_Name'=>new Zend_Db_Expr('GROUP_CONCAT(DISTINCT CONCAT(AEP.Emp_First_Name," ", AEP.Emp_Last_Name))')))						   
							->group('L.Employee_Id')
							->group('L.Leave_Id')
							->order("$sortField $sortOrder")
							->limit($rows, $page);
		
		if (empty($leaveUser['Admin']))
        {
            $getEmployeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
												  ->where('Manager_Id = ?', $sessionId));
            
			if ( $leaveUser['Is_Manager'] == 1 && !empty($getEmployeeId))
			{
				if($this->_orgDetails['Immediate_Reportees_View_Only']==0)
				{
					$getEmployeeId = $this->_dbCommonFun->getMultiLevelManagerIds($sessionId,1);
					array_push($getEmployeeId,$sessionId);
					$qryLeaves->where('L.Employee_Id IN (?)', $getEmployeeId);
				}
				else
				{
					$qryLeaves->where('L.Employee_Id = :EmpId or L.Approver_Id = :EmpId or L.Employee_Id IN (?)', $getEmployeeId)
																							   ->bind(array('EmpId'=>$sessionId));
				}
            }
            else
			{
                $qryLeaves->where('L.Employee_Id = ?', $sessionId);
            }
        }
        
		/**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			$conditions = $this->_db->quoteInto(new Zend_Db_Expr('Concat(emp.Emp_First_Name," ",emp.Emp_Last_Name) Like ?'),"%$searchAll%");
			$conditions .= $this->_db->quoteInto('or leave.Leave_Name Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or L.Start_Date Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or L.End_Date Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or L.Total_Days Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or L.Approval_Status Like ?', "%$searchAll%");
            $conditions .= $this->_db->quoteInto('or EJ.User_Defined_EmpId Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or DP.Department_Name Like ?', "%$searchAll%");
            
			$qryLeaves->where($conditions);
		}
		
		if (! empty($employeeName) && preg_match('/^[a-zA-Z]/', $employeeName))
		{
            $qryLeaves
				->where(/*$this->_db->quoteInto('emp.Emp_First_Name Like ? or ', "%$employeeName%").
						$this->_db->quoteInto('emp.Emp_Last_Name Like ? or ', "%$employeeName%").*/
                        $this->_db->quoteInto(new Zend_Db_Expr('Concat(emp.Emp_First_Name," ",emp.Emp_Last_Name) Like ?'),"%$employeeName%"));
		}
        
        if (! empty($leaveName) && preg_match('/^[0-9]*$/', $leaveName))
		    $qryLeaves->where('L.LeaveType_Id = ?', $leaveName);
        
		if(!empty($leaveStartDate) && !empty($leaveEndDate)){
			$qryLeaves
				->where($this->_db->quoteInto(new Zend_Db_Expr('L.Start_Date BETWEEN ?'), $leaveStartDate)
				.$this->_db->quoteInto(new Zend_Db_Expr('AND ?'), $leaveEndDate).' OR '.
			$this->_db->quoteInto(new Zend_Db_Expr('L.End_Date BETWEEN ?'), $leaveStartDate)
			.$this->_db->quoteInto(new Zend_Db_Expr('AND ?'), $leaveEndDate).' OR '.
			$this->_db->quoteInto(new Zend_Db_Expr('? BETWEEN L.Start_Date and L.End_Date'), $leaveStartDate).' OR '.
			$this->_db->quoteInto(new Zend_Db_Expr('? BETWEEN L.Start_Date and L.End_Date'), $leaveEndDate)
		);

		} else if (!empty($leaveStartDate)){
			$qryLeaves->where('L.Start_Date = ?', $leaveStartDate);
		} else if (!empty($leaveEndDate)){
			$qryLeaves->where('L.End_Date = ?', $leaveEndDate);
		}
			
		if (!empty($lateAttendance))
		{
			if($lateAttendance == 2)
			{
				$lateAttendance = 0;
			}
			$qryLeaves->where('L.Late_Attendance = ?', $lateAttendance);
		} 

		if (!empty($earlyCheckoutLeave))
		{
			if($earlyCheckoutLeave == 2){
				$qryLeaves->where('EAS.Early_Checkout_Leave_Id IS NULL or EAS.Early_Checkout_Leave_Id = ?', 0);
			}else{
				$qryLeaves->where('EAS.Early_Checkout_Leave_Id > ?', 0);
			}
		} 

        if (!empty($status))
        {
			if ($status == 'AlertsApplied')
			{
				$qryLeaves->where('L.Approval_Status = ?', 'Applied')
				               ->where('L.Approver_Id = ?', $sessionId);
			}
			else
			{
				$qryLeaves->where('L.Approval_Status = ?', $status);
			}
        }
		
		if(!empty($serviceProviderId)&& $this->_orgDetails['Field_Force']==1)
        {
            $qryLeaves->where('EJ.Service_Provider_Id = ?',$serviceProviderId);
        }

		if(!empty($locationId))
        {
            $qryLeaves->where('EJ.Location_Id = ?',$locationId);
        }
		
		if(!empty($departmentId))
        {
            $qryLeaves->where('EJ.Department_Id = ?',$departmentId);
        }

		if(!empty($designationId))
        {
            $qryLeaves->where('EJ.Designation_Id = ?',$designationId);
        }
		
		if(!empty($workScheduleId))
        {
            $qryLeaves->where('EJ.Work_Schedule = ?',$workScheduleId);
        }

		if (!empty($managerId))
		{
            $qryLeaves->where('emp1.Employee_Id = ?',$managerId);
		}

        $qryLeaves = $this->_dbCommonFun->getDivisionDetails($qryLeaves,'EJ.Department_Id');
	
		if(!empty($leaveUser['Admin']))
		{
			$qryLeaves = $this->_dbCommonFun->formServiceProviderQuery($qryLeaves,'EJ.Service_Provider_Id',$sessionId);
		}
		/**
		 * SQL queries
		 * Get data to display
		*/
		$leaves = $this->_db->fetchAll($qryLeaves);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		/* Total data set length */
		$qryTotal = $this->_db->select()
								->from($this->_ehrTables->empLeaves, new Zend_Db_Expr('COUNT(Leave_Id)'));
								
		if (empty($leaveUser['Admin']))
        {
            $getEmployeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
												  ->where('Manager_Id = ?', $sessionId));
            
			if ($leaveUser['Is_Manager'] == 1 && !empty($getEmployeeId))
			{
				if($this->_orgDetails['Immediate_Reportees_View_Only']==0)
				{
					$getEmployeeId = $this->_dbCommonFun->getMultiLevelManagerIds($sessionId, 1);
					array_push($getEmployeeId,$sessionId);
					$qryTotal->where('Employee_Id IN (?)', $getEmployeeId);
					
				}
				else
				{
					$qryTotal->where('Employee_Id = :EmpId or Approver_Id = :EmpId or Employee_Id IN (?)', $getEmployeeId)
																						->bind(array('EmpId'=>$sessionId));
				}
            }
            else
			{
                $qryTotal->where('Employee_Id = ?', $sessionId);
            }
        }
		
		$iTotal = $this->_db->fetchOne($qryTotal);
	
		$leaveDocumentDetails = array();
		$commentDetails       = array();
		$maxPayslipMonthDetails = array();
		if(!empty($leaves))
		{
			$leaveId 				= array_unique(array_column($leaves,'Leave_Id'));
			$employeeId 		    = array_unique(array_column($leaves,'Employee_Id'));
			$leaveDocumentDetails 	= $this->_dbCommonFun->getLeaveDocumentDetails($leaveId);
			$commentDetails 	    = $this->_dbCommonFun->countCommentDetails($leaveId, $leaveUser['Form_Name']);
			$maxPayslipMonthDetails = $this->_dbCommonFun->getMaxPayslipMonthByEmployeeId($employeeId);
		}
		$dbPayslip = new Payroll_Model_DbTable_Payslip();
		foreach ($leaves as $key => $row)
		{
			$maxPayslipMonth ='';
			/**
			*	Add comments count for each leave record
			*/
			if(!empty($leaveDocumentDetails))
			{
				$empDocumentFiles 					= $this->_dbCommonFun->ensureArray($leaveDocumentDetails, $row['Leave_Id']);
				$leaves[$key]['Document_File_Path'] = $empDocumentFiles;
			}
			else
			{
				$leaves[$key]['Document_File_Path'] = '';
			}

			if(!empty($commentDetails))
			{
				$commentByLeaveId 					= $this->_dbCommonFun->ensureArray($commentDetails, $row['Leave_Id']);
				$leaves[$key]['Comment_Cnt'] 		= count($commentByLeaveId);     
			}
			else
			{
				$leaves[$key]['Comment_Cnt'] 		= 0;
			}
			
			if(!empty($maxPayslipMonthDetails))
			{
				$maxPayslipMonthByEmployeeId 		= $this->_dbCommonFun->ensureArray($maxPayslipMonthDetails, $row['Employee_Id']);
				if(!empty($maxPayslipMonthByEmployeeId))
				{
					$maxPayslipMonth 					= $maxPayslipMonthByEmployeeId[0]['Salary_Month'];     
				}
			}

			if(!empty($maxPayslipMonth))
			{
				$maxPayslipMonth = explode('-',$maxPayslipMonth);
				$lastSalaryDay = $dbPayslip->getSalaryDay($maxPayslipMonth[1],$maxPayslipMonth[0], 31);
				
				$lastPayslipMonth = date('Y-m-d', strtotime($lastSalaryDay['Last_SalaryDate']));
				/** Checked that last generated payslip month should be greater than or equal to the leave start date. If it is zero, leave record
				will be allowed to delete **/
				if( $lastPayslipMonth >= $row['Start_Date'] )
				{
					$leaves[$key]['M'] = 1;	
					$leaves[$key]['H'] = 1;	
				}
				else
				{
					$leaves[$key]['M'] = 0;
					$leaves[$key]['H'] = 0;	
				}
			}
			else
			{
				$leaves[$key]['M'] = 0;
				$leaves[$key]['H'] = 0;	
			}

			if(!empty($row['Late_Attendance_Hours_From_Grace']))
			{
				$leaves[$key]['Late_Attendance_Time'] = "The employee checked in late by ".$row['Late_Attendance_Hours_From_Grace']."(HH:MM:SS) excluding the grace period of ".$row['Always_Grace']."(HH:MM:SS).Including the grace period, the employee was delayed by ".$row['Late_Attendance_Hours']."(HH:MM:SS)";
			}
			else
			{
				$leaves[$key]['Late_Attendance_Time'] = '';
			}

			if(!empty($row['Alternate_Person_Id']))
			{
				$leaves[$key]['Alternate_Person_Id'] = explode(",",$row['Alternate_Person_Id']);
			}
			else
			{
				$leaves[$key]['Alternate_Person_Id'] = '';
			}
		}

		/**
		 * Output array with Json encode
		*/
		
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $leaves);
    }



	/**
	 *	to list the leavetype in dropdown
	*/
    public function getLeaveType()
    {
		return $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->leavetype, array('LeaveType_Id', 'Leave_Name'))
									->where('Leave_Status =?', 'Active')
									->order('Leave_Name ASC'));
    }

	/**
	 *	to list the available leavetype based on gender in dropdown
	 *  Return all the unpaid leave for an employee to no attendance-validate fn
	*/
    public function getEmpAvailLeaveType ($empId, $source=NULL,$loginEmployeeId=0)
    {
		$empLevelLeaves = $this->_db->fetchCol($this->_db->select()->from(array('leavegrade'=>$this->_ehrTables->leavetypegrade),array('leavegrade.LeaveType_Id'))
						 	->joinInner(array('empjob'=>$this->_ehrTables->empJob),"empjob.Employee_Id = $empId",array())
							->joinInner(array('designation'=>$this->_ehrTables->designation),"designation.Designation_Id = empjob.Designation_Id",array())
						  	->where('leavegrade.Grade_Id = designation.Grade_Id'));
						
		if(!empty($empLevelLeaves))
		{
			$normalCoverageLeaveQry= $this->_db->select()
									->from(array('leave'=>$this->_ehrTables->leavetype),array('leave.LeaveType_Id','leave.Leave_Name'))
									->joinInner(array('emp'=>$this->_ehrTables->empPersonal),"emp.Employee_Id = $empId",array())
									->joinInner(array('el'=>$this->_ehrTables->empEligbleLeave),"el.Employee_Id = $empId AND leave.LeaveType_Id = el.LeaveType_Id",array())
									->where('leave.Leave_Enforcement_Configuration = ?', 1)
									->where('leave.Leave_Status =?', 'Active')
									->where('leave.Gender = \'ALL\' OR leave.Gender = emp.Gender');

			/**Get the where condition for the custom group coverage. If the leave balance deos not exist in the
			 * emp eligible leaves for the custom group employees then that leave type should not be considered for the
			 * custom group employee */
			$customGroupWhere = $this->customGroupWhere('leave',0,'el.Employee_Id',$empLevelLeaves);

			if(!empty($customGroupWhere)){
				$normalCoverageLeaveQry->where($customGroupWhere);
			}else{
				$normalCoverageLeaveQry->where('leave.Coverage = \'ORG\' OR (leave.Coverage = \'GRA\' AND leave.LeaveType_Id IN (?))',$empLevelLeaves);
			}

			$normalCoverageLeaveQry->order('leave.Leave_Name ASC');

			if($source == "noAttendance"){
				$normalCoverageLeaveQry->where('leave.Leave_Type = ?', 'Unpaid Leave');
				$normalCoverageLeave = $this->_db->fetchAll($normalCoverageLeaveQry);

				return $normalCoverageLeave;	
			}else{
				if($loginEmployeeId > 0){
					$dbAccessRights = new Default_Model_DbTable_AccessRights();
					$leaveAccessRights = $dbAccessRights->employeeAccessRights($loginEmployeeId, 'Leaves');
					$isAdmin = $leaveAccessRights['Admin'] == 'admin' ? 1 : 0;
				}else{
					$isAdmin = 1;
				}
				if(intval($isAdmin) <= 0){
					$normalCoverageLeaveQry->where('leave.Restrict_Employee_To_Apply =?',0);
				}
				$normalCoverageLeave = $this->_db->fetchPairs($normalCoverageLeaveQry);

				$specialCoverageLeaveQuery = $this->_db->select()
										->from(array('leave'=>$this->_ehrTables->leavetype),array('leave.LeaveType_Id','leave.Leave_Name'))
										->joinInner(array('emp'=>$this->_ehrTables->empPersonal),"emp.Employee_Id = $empId",array())
										->joinInner(array('eligible'=>$this->_ehrTables->empEligbleLeave),"eligible.Employee_Id = $empId AND leave.LeaveType_Id = eligible.LeaveType_Id",array())
										->where('leave.Leave_Enforcement_Configuration != ?',1)
										->where('leave.Leave_Status =?', 'Active')
										->where('leave.Gender = \'ALL\' OR leave.Gender = emp.Gender')
										// ->where('leave.LeaveType_Id IN (?)',$empLevelLeaves)
										->order('leave.Leave_Name ASC');
				$customGroupWhere = $this->customGroupWhere('leave',0,'eligible.Employee_Id',$empLevelLeaves);

				if(!empty($customGroupWhere)){
					$specialCoverageLeaveQuery->where($customGroupWhere);
				}else{
					$specialCoverageLeaveQuery->where('leave.Coverage = \'ORG\' OR (leave.Coverage = \'GRA\' AND leave.LeaveType_Id IN (?))',$empLevelLeaves);
				}						
				if(intval($isAdmin) <= 0){
					$specialCoverageLeaveQuery->where('leave.Restrict_Employee_To_Apply =?',0);
				}
				$specialCoverageLeave= $this->_db->fetchPairs($specialCoverageLeaveQuery);
				foreach($specialCoverageLeave as $key=>$value)
				{
					$normalCoverageLeave[$key]=$value;
				}
				return $normalCoverageLeave;
			}
		}
		else
		{	
			$normalLeaveQry = $this->_db->select()
									  ->from(array('leave'=>$this->_ehrTables->leavetype),array('leave.LeaveType_Id','leave.Leave_Name'))
									  ->joinInner(array('emp'=>$this->_ehrTables->empPersonal),"emp.Employee_Id = $empId",array())
									  ->joinInner(array('el'=>$this->_ehrTables->empEligbleLeave),"el.Employee_Id = $empId AND leave.LeaveType_Id = el.LeaveType_Id",array())
									  ->where('leave.Leave_Status =?', 'Active')									 
									  ->where('leave.Gender = \'ALL\' OR leave.Gender = emp.Gender')
									  ->where('leave.Leave_Enforcement_Configuration = ?',1);

			/**Get the where condition for the custom group coverage. If the leave balance deos not exist in the
			 * emp eligible leaves for the custom group employees then that leave type should not be considered for the
			 * custom group employee */
			$customGroupWhere = $this->customGroupWhere('leave',0,'el.Employee_Id');

			if(!empty($customGroupWhere)){
				$normalLeaveQry ->where($customGroupWhere);
			}else{
				$normalLeaveQry->where('leave.Coverage = \'ORG\'');
			}
			$normalLeaveQry ->order('leave.Leave_Name ASC');

			if($source == "noAttendance"){
				$normalLeaveQry->where('leave.Leave_Type = ?','Unpaid Leave');
				$normalLeave = $this->_db->fetchAll($normalLeaveQry);

				return $normalLeave;	
			}else{
				if($loginEmployeeId > 0){
					$dbAccessRights = new Default_Model_DbTable_AccessRights();
					$leaveAccessRights = $dbAccessRights->employeeAccessRights($loginEmployeeId, 'Leaves');
					$isAdmin = $leaveAccessRights['Admin'] == 'admin' ? 1 : 0;
				}else{
					$isAdmin = 1;
				}
				
				if(intval($isAdmin) <= 0){
					$normalLeaveQry->where('leave.Restrict_Employee_To_Apply =?',0);
				}
				$normalLeave = $this->_db->fetchPairs($normalLeaveQry);

				$specialCoverageLeaveQuery = $this->_db->select()
											->from(array('leave'=>$this->_ehrTables->leavetype),array('leave.LeaveType_Id','leave.Leave_Name'))
											->joinInner(array('emp'=>$this->_ehrTables->empPersonal),"emp.Employee_Id = $empId",array())
											->joinInner(array('eligible'=>$this->_ehrTables->empEligbleLeave),"eligible.Employee_Id = $empId AND leave.LeaveType_Id = eligible.LeaveType_Id",array())
											->where('leave.Leave_Enforcement_Configuration != ?', 1)
											->where('leave.Leave_Status =?', 'Active')
											->where('leave.Gender = \'ALL\' OR leave.Gender = emp.Gender')
											->order('leave.Leave_Name ASC');					 
			
				if(intval($isAdmin) <= 0){
					$specialCoverageLeaveQuery->where('leave.Restrict_Employee_To_Apply =?',0);
				}
				$specialLeave= $this->_db->fetchPairs($specialCoverageLeaveQuery);							
				foreach($specialLeave as $key=>$value)
				{
					$normalLeave[$key]=$value;
				}

				return $normalLeave;
			}
		}  
    }

	/**
	 *	to insert/update leave request
	*/
    public function updateLeave ($leaveData, $employeesDocumentUploadFilesArr, $sessionId, $formName, $leaveId, $comments, $leaveRec,$customFormName,$leaveBalance=0, $esicReason="",$notify='No',$source=null,$additionalDetails=array())
    {	
		$leaveTypeDetails = $this->getLeaveTypeRow($leaveData['LeaveType_Id'],'Yes');
		
		$eventId = $leaveTypeDetails['Event_Id'];
		if(!empty($leaveData['Alternate_Person']))
		{
			$alternatePerson = array_values($leaveData['Alternate_Person']);
		}
		unset($leaveData['Alternate_Person']);
		if($eventId)
		{
			/* Create an object */
			$instanceData = new \stdClass();
			$instanceData->formId = 31;
			if($leaveData['Employee_Id']==$sessionId)
			{
				$instanceData->isSelfApply=1;
			}
			else{
				$instanceData->isSelfApply=0;
			}
			$instanceData->formName = "Leaves";
			$instanceData->employeeId =(int)$leaveData['Employee_Id'];
			$instanceData->initiatorId =(int)$sessionId;
			$instanceData->reason=$leaveData['Reason'];
			$instanceData->duration=strval($leaveData['Duration']);
			$instanceData->startDate=$leaveData['Start_Date'];
			$instanceData->endDate=$leaveData['End_Date'];
			$instanceData->totalDays=strval($leaveData['Total_Days']);
			$instanceData->hours=strval($leaveData['Hours']);
			$instanceData->leaveTypeId=(int)$leaveData['LeaveType_Id'];
			$instanceData->approvalStatus=$leaveData['Approval_Status'];
			$instanceData->contactNo=$leaveData['Contact_Details'];
			$instanceData->notify=$notify;

			if(!empty($alternatePerson))
			{
				$instanceData->alternatePerson=$alternatePerson;
			}
			else{
				$instanceData->alternatePerson=0;
			}
			if(empty($leaveData['Leave_Period']))
			{
				$instanceData->leavePeriod="";
			}
			else{
				$instanceData->leavePeriod=strval($leaveData['Leave_Period']);
			}
			if(empty($leaveData['Reason_Id']))
			{
				$instanceData->reasonId=0;
			}
			else{
				$instanceData->reasonId=(int)$leaveData['Reason_Id'];
			}

			$instanceData->comment=$comments;
			$instanceData->esicReason=!empty($esicReason) ? $esicReason : "";
		}
		if (empty($leaveId))
		{
			$action = 'Add';
			
			$leaveData['Added_By'] = $sessionId;
			$leaveData['Added_On'] = date('Y-m-d H:i:s');

			$updated = $this->_db->insert($this->_ehrTables->empLeaves, $leaveData);

			if ($updated)
			{
				$leaveId = $this->_db->lastInsertId();
				if($leaveTypeDetails['Enable_Leave_Exception']=='Yes' && $leaveTypeDetails['Half_Paid_Leave_Deduction']=='Yes' && empty($employeesDocumentUploadFilesArr) && $leaveTypeDetails['Leave_Unit']=='0.5')
				{
					$leaveData['Leave_Id'] 				= $leaveId;
					$unpaidLeaveOverrideDetails 		= $this->getUnpaidLeaveOverrideDetails($leaveData);
					$insertUnpaidLeaveOverrideDetails 	= $this->insertUnpaidLeaveOverrideDetails($unpaidLeaveOverrideDetails);
				}

				if($eventId)
				{
					$documentList = new stdClass();
					if(!empty($employeesDocumentUploadFilesArr))
					{
						foreach($employeesDocumentUploadFilesArr as $key=>$row)
						{
							$documentList->$key = $employeesDocumentUploadFilesArr[$key]['File_Name'];
						}
					}

					$instanceData->documentList=$documentList;

					$instanceData->leaveId =(int)$leaveId;
					$instanceData->addedOn=$leaveData['Added_On'];
					$instanceData->addedBy=(int)$leaveData['Added_By'];
					$instanceData->updatedOn="";
					$instanceData->updatedBy=0;
					$instanceData->lateAttendance=array_key_exists("Late_Attendance",$leaveData) ? (int)$leaveData['Late_Attendance'] : 0;
					$instanceData->lateAttendanceHours=array_key_exists("Late_Attendance_Hours",$leaveData) ? $leaveData['Late_Attendance_Hours'] :"";
					$instanceData->lateAttendanceHoursFromGrace=array_key_exists("Late_Attendance_Hours_From_Grace",$leaveData) ? $leaveData['Late_Attendance_Hours_From_Grace'] :"";
					$instanceData->alwaysGrace=array_key_exists("Always_Grace",$leaveData) ? $leaveData['Always_Grace'] :"";
					$instanceData->earlyCheckoutLeave=array_key_exists("earlyCheckoutLeave",$additionalDetails) ? (int)$additionalDetails['earlyCheckoutLeave'] : 0;
					$instanceData->earlyCheckoutHours=array_key_exists("earlyCheckoutHours",$additionalDetails) ? (int)$additionalDetails['earlyCheckoutHours'] : "";
					$instanceData->earlyCheckoutHoursFromGrace=array_key_exists("earlyCheckoutHoursFromGrace",$additionalDetails) ? (int)$additionalDetails['earlyCheckoutHoursFromGrace'] : "";
					$instanceData->earlyCheckoutAlwaysGraceHours=array_key_exists("earlyCheckoutAlwaysGraceHours",$additionalDetails) ? (int)$additionalDetails['earlyCheckoutAlwaysGraceHours'] : "";
					$instanceData->Attendance_Shortage = (array_key_exists("Attendance_Shortage",$leaveData) && $leaveData['Attendance_Shortage'] == 1) ? 'Yes' : 'No';
					$instanceData->No_Attendance = 'No';//Since no-attendance leaves are added with an approved status, set the 'No_Attendance' flag to 'No' when adding such leaves


					$workFlowInitiatedId=$this->initiateWorkflowEngine($eventId,$instanceData,$sessionId);
					$checkWorkflowUpdate=0;
					if($workFlowInitiatedId)
					{
						$workFlowData['Workflow_Status']= "Waiting for Approval";
						$workFlowData['Workflow_Instance_Id']=$workFlowInitiatedId;
						$updatedWorkFlow = $this->_db->update($this->_ehrTables->empLeaves, $workFlowData, 'Leave_Id='.(int)$leaveId);
						if($updatedWorkFlow)
						{
							$checkWorkflowUpdate=1;
						}
						else {
							$this->_db->delete($this->_ehrTables->taUserTask, array('process_instance_id = ?'=>$workFlowInitiatedId));
							$this->_db->delete($this->_ehrTables->taProcessInstance, array('process_instance_id = ?'=>$workFlowInitiatedId));
							$updated = 0;
						}
					}
					if($checkWorkflowUpdate==0)
					{
						$this->_db->delete($this->_ehrTables->empLeaves,'Leave_Id='.(int)$leaveId);
						$processInstanceId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->taProcessInstance,array('process_instance_id'))
														->where('instance_data like ?','%"leaveId":'.$leaveId.',%'));
						if(!empty($processInstanceId)){
							$this->_db->delete($this->_ehrTables->taProcessInstance, array('process_instance_id = ?'=>$processInstanceId));
							$this->_db->delete($this->_ehrTables->taUserTask, array('process_instance_id = ?'=>$processInstanceId));
						}
						$updated = 0;
					}
				}
			}
		}
		else
		{
			$action = 'Edit';
			$leaveData['Updated_By'] = $sessionId;
			$leaveData['Updated_On'] = date('Y-m-d H:i:s');
			if($eventId)
			{
				$instanceData->leaveId =(int)$leaveId;
				$workFlowAndLeaveData=$this->_db->fetchRow($this->_db->select()->from(array('EL'=>$this->_ehrTables->empLeaves), 
										array('EL.Workflow_Instance_Id','EL.Late_Attendance','EL.Late_Attendance_Hours','EL.Late_Attendance_Hours_From_Grace','EL.Always_Grace','EL.Added_On','EL.Added_By','EL.Attendance_Shortage','EL.Reason'))
										->joinLeft(array('EAS'=>$this->_ehrTables->employeeAttendanceSummary),'EL.Leave_Id = EAS.Early_Checkout_Leave_Id',
										array('Early_Checkout' => new Zend_Db_Expr('CASE WHEN EAS.Early_Checkout_Leave_Id > 0 THEN 1 ELSE 0 END'),
										'EAS.Early_Checkout_Hours','EAS.Early_Checkout_Hours_From_Grace','Early_Checkout_Always_Grace_Hours')) 
										->where('Leave_Id = ?', (int)$leaveId));

				$leaveDocuments = $this->getLeaveDocuments($leaveId);

				$workFlowInitiatedId=$workFlowAndLeaveData['Workflow_Instance_Id'];
				$instanceData->lateAttendance=(int)$workFlowAndLeaveData['Late_Attendance'];
				$instanceData->lateAttendanceHours=$workFlowAndLeaveData['Late_Attendance_Hours'];
				$instanceData->lateAttendanceHoursFromGrace=$workFlowAndLeaveData['Late_Attendance_Hours_From_Grace'];
				$instanceData->alwaysGrace=$workFlowAndLeaveData['Always_Grace'];
				$instanceData->earlyCheckoutLeave=$workFlowAndLeaveData['Early_Checkout'];
				$instanceData->earlyCheckoutHours=$workFlowAndLeaveData['Early_Checkout_Hours'];
				$instanceData->earlyCheckoutHoursFromGrace=$workFlowAndLeaveData['Early_Checkout_Hours_From_Grace'];
				$instanceData->earlyCheckoutAlwaysGraceHours=$workFlowAndLeaveData['Early_Checkout_Always_Grace_Hours'];
				$instanceData->Attendance_Shortage = ($workFlowAndLeaveData['Attendance_Shortage'] == 1) ? 'Yes' : 'No';
				$instanceData->No_Attendance = ($workFlowAndLeaveData['Reason'] == "Auto LOP") ? 'Yes' : 'No';

				$instanceData->addedOn=$workFlowAndLeaveData['Added_On'];
				$instanceData->addedBy=(int)$workFlowAndLeaveData['Added_By'];
				$instanceData->updatedOn=$leaveData['Updated_On'];
				$instanceData->updatedBy=(int)$leaveData['Updated_By'];

				$documentList = new stdClass();
				$docKey = 0;
				if(!empty($employeesDocumentUploadFilesArr))
				{
					foreach($employeesDocumentUploadFilesArr as $key=>$row)
					{
						$documentList->$docKey = $employeesDocumentUploadFilesArr[$key]['File_Name'];
						$docKey++;
					}
				}

				if(!empty($leaveDocuments))
				{
					foreach($leaveDocuments as $key=>$row)
					{
						$documentList->$docKey = $leaveDocuments[$key]['File_Name'];
						$docKey++;
					}
				}

				$instanceData->documentList=$documentList;

				$newWorkFlowInitiatedId=$this->initiateWorkflowEngine($eventId,$instanceData,$sessionId);
			}
			if(!$eventId || ($eventId && $newWorkFlowInitiatedId))
			{
				if ($leaveRec['Approval_Status'] == "Approved")
				{
					$employeeId = $leaveData['Employee_Id'];
					$leaveType  = $leaveRec['LeaveType_Id'];
					
					$availedLeaves = $this->getLeaveClosureDetails($employeeId, $leaveType);
					
					if ($availedLeaves)
					{					
						// to update the availed leave table
						
						//$leaveRec['Total_Days'] is old total days which will be get from table
						/** availed leave taken - old total days **/
						$leavesTaken  = $availedLeaves['Leaves_Taken'] - $leaveRec['Total_Days'];
						
						/** sum of availed leave balance and old total days **/
						$leaveBalance = $availedLeaves['Leave_Balance'] + $leaveRec['Total_Days'];
						
						$this->updateLeaveBalance ($availedLeaves['Eligible_Leave_Id'],$leavesTaken, $leaveBalance);

						$leaveTypeDetails = $this->getLeaveTypeRow($leaveType);
						
						$leaveTotalDays = $leaveTypeDetails['Total_Days'];
						
						$carryOverLimit = $leaveTypeDetails['CarryOver_Limit'];
						
						$isCarryOverApplicable = $leaveTypeDetails['Carry_Over'];
						
						if($isCarryOverApplicable == 'Yes'){
							
							/** current year leaves will be considered first **/
							if( $carryOverLimit < $leaveTotalDays )
							{
								/** Check that employee has last year leave balance or not **/
								if ($leaveBalance > $leaveTotalDays)
								{
									/** Leaves taken = availed leaves taken - old total days **/
									if($leavesTaken > $leaveTotalDays)
									{
										$codiff = $leavesTaken - $leaveTotalDays;
									}
									else{
										$codiff = $availedLeaves['No_Of_Days'];
									}
								}
								else{
									$codiff = $availedLeaves['No_Of_Days'];
								}
							}
							else{
								/** old code - to update the deferred leave table **/
								if ($leaveBalance > $leaveTotalDays)
								{
									$codiff = $leaveBalance - $leaveTotalDays;
								}
							}
							$this->updateCo ($leaveType, $employeeId, $codiff);
						}
					}
				}
				
				$updated = $this->_db->update($this->_ehrTables->empLeaves, $leaveData, 'Leave_Id='.(int)$leaveId);
				if($eventId && $updated)
				{
					if($workFlowInitiatedId)
					{
						$deleteUserTaskCount=$this->_db->delete($this->_ehrTables->taUserTask, array('process_instance_id = ?'=>$workFlowInitiatedId));
						$countUserTaskHistory=$this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->taUserTaskHistory,new Zend_Db_Expr('count(process_instance_id)'))
						->where("process_instance_id = ?",$workFlowInitiatedId));
	
						if($countUserTaskHistory==0)
						{
							$workflowApprovalStatus="Waiting for approval";
						}
						else if($deleteUserTaskCount==0)
						{
							$workflowApprovalStatus="Approved";
						}
						else{
							$workflowApprovalStatus="Partially approved";
						}
						$this->_db->insert($this->_ehrTables->leaveWorkflowHistory, array('Leave_Id'=>(int)$leaveId,'Workflow_Instance_Id'=>$workFlowInitiatedId,'Workflow_Approval_Status'=>$workflowApprovalStatus));
					}
					$workFlowData['Workflow_Status']= "Waiting for Approval";
					$workFlowData['Workflow_Instance_Id']=$newWorkFlowInitiatedId;
					$this->_db->update($this->_ehrTables->empLeaves, $workFlowData, 'Leave_Id='.(int)$leaveId);
				}
				if($eventId && !$updated && $newWorkFlowInitiatedId)
				{
					$this->_db->delete($this->_ehrTables->taUserTask, array('process_instance_id = ?'=>$newWorkFlowInitiatedId));
					$this->_db->delete($this->_ehrTables->taProcessInstance, array('process_instance_id = ?'=>$newWorkFlowInitiatedId));
				}
			} else {
				$updated = 0;
			}
		}

		if ($updated){
			// inserting the documents array
			if(!empty($employeesDocumentUploadFilesArr))
			{
				foreach($employeesDocumentUploadFilesArr as $key=>$row)
				{
					$employeesDocumentUploadFilesArr[$key]['Leave_Id'] = $leaveId;	
				}
				$fUpdated =$this->_ehrTables->insertMultiple($this->_ehrTables->empLeaveDocuments, $employeesDocumentUploadFilesArr);				
			}			

			$this->_db->delete($this->_ehrTables->alternatePerson, array('Leave_Id = ?'=>$leaveId));
			if(!empty($alternatePerson))
			{
				$alternatePersonDetails = array();
				foreach($alternatePerson as $key=>$row)
				{
					$alternatePersonDetails[$key]['Leave_Id'] = $leaveId;
					$alternatePersonDetails[$key]['Alternate_Person'] = $row;	
				}

				if(!empty($alternatePersonDetails))
				{
					$fUpdated =$this->_ehrTables->insertMultiple($this->_ehrTables->alternatePerson, $alternatePersonDetails);
				}
			}

			if($source !== 'earlycheckoutleave'){
				$attendanceSummarySelectedDetails = [];
				$attendanceSummarySelectedDetails[] = $leaveData;
				$this->_dbCommonFun->triggerAttendanceSummaryStepFunction($attendanceSummarySelectedDetails,'leaves');
			}

			//If the approved leave records edited get the old details and push the leave details in the syntrum for cancellation
			if (!empty($leaveRec) && $leaveRec['Approval_Status'] == "Approved"){
				$integrationLeaveDetails = array(
					'Employee_Id' => $leaveRec['Employee_Id'],
					'LeaveType_Id' =>  $leaveRec['LeaveType_Id'],
					'Added_By' => $sessionId,
					'FromDate' =>  $leaveRec['Start_Date'],
					'ToDate' =>  $leaveRec['End_Date'],
					'Total_Days' =>  $leaveRec['Total_Days']
				);
				$action = 'Cancel';
				$elementHeader  = '';
				$leaveTypeCode = '';
				//If the cancel leave type code exist in the leave type table
				if(!empty($leaveTypeDetails['Cancel_Leave_Type_Code'])){
					$elementHeader = $leaveTypeDetails['Cancel_Leave_Type_Code'];
				}else{
					$leaveTypeCode = $this->getLeaveTypeCodeFromSlab($leaveTypeDetails['LeaveType_Id'],$action);
				}

				if(!empty($leaveTypeCode) || !empty($elementHeader)){
					$syntrumInputDetails = array(
						'uniqueIds' => array((int)$leaveId),
						'elementHeader' => $elementHeader,
						'action' => $action,
						'leaveTypeCode'=>$leaveTypeCode,
						'uniqueIdDetails'=>$integrationLeaveDetails
					);
				
					$this->_dbCommonFun->triggerSyntrumExecutionRequest($syntrumInputDetails,$sessionId);
				}
			}
		}
		
		if ($updated && !empty($comments))
		{
			$this->_db->insert($this->_ehrTables->comment, array('Form_Id'         => $this->_dbComment->getFormId($formName),
																 'Emp_Comment'     => $comments,
																 'Parent_Id'       => $leaveId,
																 'Approval_Status' => "Applied",
																 'Employee_Id'     => $sessionId,
																 'Added_On'        => date('Y-m-d H:i:s')));
		}

		/**
		 *	this function will handle
		 *		update system log function
		 *		clear submit lock fucntion
		 *		return success/failure array
		 */
		$updateResult = $this->_dbCommonFun->updateResult (array('updated'        => $updated,
														'action'         => $action,
														'trackingColumn' => $leaveId,
														'formName'       => $customFormName,
														'sessionId'      => $sessionId,
														'tableName'      => $this->_ehrTables->empLeaves));
		$updateResult['event_id'] = $eventId;
		$updateResult['leaveId'] = $leaveId;
		return $updateResult;
    }
	
	// check if the employee has documents
	public function checkEmpUploadFilesExists($leaveId){
		return $this->_db->fetchone($this->_db->select()->from($this->_ehrTables->empLeaveDocuments,'COUNT(Leave_Id)')
														->where('Leave_Id   = ?', $leaveId));
	}

	/**
	 *	to list the applied leaves and remaining leave balance in grid
	*/
    public function empLeaveHistory ($page=null, $rows=null, $searchAll=null, $historyArr, $view=null)
	{
		$employeeId=array();
		if(!(is_array($historyArr['Employee_Id']))){
			array_push($employeeId,$historyArr['Employee_Id']);
		}else{
        	$employeeId = $historyArr['Employee_Id'];
		}
		
		$eligLvCond = 'el.LeaveType_Id = L.LeaveType_Id AND el.Employee_Id = P.Employee_Id';
    	
		$enOnCond 	= 'en.LeaveType_Id = L.LeaveType_Id  AND en.EN_Year=el.CO_Year AND en.Employee_Id = el.Employee_Id';
	
		/** If the employee leave balance does not exist in the emp eligible leaves then we need consider that zero else we need to consider employee eligible days*/

		$eligDays = "(CASE WHEN el.Eligible_Days is NULL THEN 0.0 ELSE el.Eligible_Days END)";

		$lvBal    = "(((IFNULL(el.No_Of_Days, 0) + ($eligDays)) - ((IFNULL(SUM(en.Encashed_Days), 0)) + IFNULL(SUM(el.Leaves_Taken), 0))))";
	
		if($view === 'paidLeaveDeduction' || $view === 'paidLeaveEncashment' || $view === 'listLeaveEncashmentLeaveTypes' 
		|| $view === 'listEmpEncashmentLimit' || $view === 'showInPayslip'){
			$qryHistory = $this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal),
				   array('P.Employee_Id','L.LeaveType_Id','L.Leave_Name','L.Enable_Proration','L.Encashment_Limit','L.Leave_Enforcement_Configuration',
				   	'Encashed_Days'=>new Zend_Db_Expr('SUM(en.Encashed_Days)'),'L.Total_Days',
					'Elig_Days' => new Zend_Db_Expr($eligDays),'Leave_Balance'=>new Zend_Db_Expr($lvBal)))
					->joinInner(array('L'=>$this->_ehrTables->leavetype),"L.Leave_Status = 'Active'", array());
		}else{
			$qryHistory = $this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal),
				   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS L.LeaveType_Id as count'), 'L.LeaveType_Id','L.Leave_Enforcement_Configuration',
					'L.Leave_Name', 'L.Total_Days', 'Elig_Days' => new Zend_Db_Expr($eligDays),'Leave_Balance'=>new Zend_Db_Expr($lvBal)))
					->joinInner(array('L'=>$this->_ehrTables->leavetype),"L.Leave_Status = 'Active'", array());
		}
									
		$qryHistory->joinInner(array('el'=>$this->_ehrTables->empEligbleLeave),$eligLvCond,array('el.Leave_Closure_Start_Date','el.Leave_Closure_End_Date','el.Leaves_Taken','el.Last_CO_Balance','el.No_Of_Days'))
					
					->joinLeft(array('en'=>$this->_ehrTables->encashedLeave),$enOnCond,array());

		if($view === 'paidLeaveDeduction' || $view === 'paidLeaveEncashment' || $view === 'listEmpEncashmentLimit'){
			$qryHistory->joinInner(array('J'=>$this->_ehrTables->empJob),'P.Employee_Id = J.Employee_Id',array('J.Date_Of_Join'));
		}

		if($view === 'paidLeaveDeduction' || $view === 'paidLeaveEncashment'){
			$qryHistory->joinInner(array('R'=>$this->_ehrTables->resignation),"R.Employee_Id = P.Employee_Id AND R.Approval_Status = 'Approved'",array('R.Resignation_Date'));
		}
			
		if($view === 'listEmpEncashmentLimit'){
			/** Based on the employee resignation date calculate the available days in the encashment form */
			$qryHistory->joinLeft(array('R'=>$this->_ehrTables->resignation),"R.Employee_Id = P.Employee_Id AND R.Approval_Status = 'Approved'",array('R.Resignation_Date'));
		}

		$qryHistory->where('P.Employee_Id IN (?)', $employeeId);

		/** get leave balance in leave form **/									
		if($view == 'LeaveForm')
		{
			$leaveTypeId   = $historyArr['LeaveType_Id'];
			
			/** When the leave type is not applicable for the employee then the record is returned. To avoid that, we need
			 *  to group leave type id and the employee id
			 */
			$qryHistory ->where('L.LeaveType_Id = ?', $leaveTypeId)
						->group('L.LeaveType_Id')
						->group('P.Employee_Id');

			$rowLeaveHistory = $this->_db->fetchRow($qryHistory);

			return $rowLeaveHistory;
		}else if($view === 'paidLeaveDeduction'){
			/** Payslip generation - Paid Leave Deduction details */
			$qryHistory->where('P.Form_Status = 1')
						->where('L.Leave_Deduction_For_FF = ?', 'Yes')
						->where('L.Leave_Type = ?', 'Paid Leave')
						->group('L.LeaveType_Id');

			$leaveDetails = $this->_db->fetchAll($qryHistory);
			
			return $leaveDetails;
		}else if($view === 'paidLeaveEncashment'){
			/** Payslip generation - Paid Leave Encashment details */
			$qryHistory->where('L.Leave_Type = ?', 'Paid Leave')
						->where('L.Leave_Encashment_For_FF = ?', 'Yes')
						->group('L.LeaveType_Id')
						->group('P.Employee_Id');
			$leaveDetails = $this->_db->fetchAll($qryHistory);

			return $leaveDetails;			
		}else if($view == 'listLeaveEncashmentLeaveTypes'){
			/** Apply encashment form - Paid Leave Encashment leave type list */
			$qryHistory->where('L.Encashment = ?', 'Yes')
						->group('L.LeaveType_Id');

			$leaveDetails = $this->_db->fetchAll($qryHistory);

			/** To list the leave encashment types in the apply leave encashment form */
			$leaveEncashmentLeaveTypes = array();
			foreach ($leaveDetails as $row)
			{
				$leaveEncashmentLeaveTypes[$row['LeaveType_Id']] = $row['Leave_Name'];
			}
			return $leaveEncashmentLeaveTypes;
		}else if($view === 'listEmpEncashmentLimit'){
			/** Apply encashment form - Paid Leave Encashment details */
			$leaveTypeId   = $historyArr['LeaveType_Id'];

			$qryHistory->where('L.Encashment = ?', 'Yes')
						->where('L.LeaveType_Id = ?', $leaveTypeId)
						->group('L.LeaveType_Id');

			$leaveDetails = $this->_db->fetchAll($qryHistory);

			/** Calculate the employee encashment applicable days for the leave type and append it to the $leaveDetails array */
			$historyEncashmentRes = $this->getEmpLeaveTypeEncashmentRemainingDays($leaveDetails);

			return $historyEncashmentRes;
		}
		else if($view == 'showInPayslip')
		{
			$qryHistory->where('L.Show_In_Payslip = ?',1)
						->where('L.Leave_Type = ?',$historyArr['Leave_Type']);
			$leaveDetails = $this->_db->fetchAll($qryHistory);
			return $leaveDetails;
		}
		else{
			/** Condition to return the leave types to dashboard & leave history modal in leaves form based on the below flag */
			$qryHistory->where('L.Show_Statistics_In_Dashboard = ?', 1);

			$qryHistory ->group('L.LeaveType_Id','P.Employee_Id')
						->order("L.Leave_Name ASC");
			
			if($page!=null&&$page>0)
			{
				$qryHistory->limit($rows, $page);
			}
				
			/**
			 *	Search All columns using single input
			*/
			if (!empty($searchAll) && $searchAll != null)
			{
				$conditions = $this->_db->quoteInto('L.Leave_Name Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or L.Total_Days Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or el.Leaves_Taken Like ?', "%$searchAll%");
				
				$qryHistory->where($conditions);
			}
			/**
			 * SQL queries
			 * Get data to display
			*/
			$leaveHistory = $this->_db->fetchAll($qryHistory);
		
			/* Data set length after filtering */
			$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
			
			/* Total data set length */
			$iTotalQry = $this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal), array(new Zend_Db_Expr('COUNT(L.LeaveType_Id)')))
										   ->joinInner(array('L'=>$this->_ehrTables->leavetype),'L.Leave_Status = \'Active\'', array())
										   ->where('L.Gender = \'ALL\' OR L.Gender = P.Gender')
										   ->group('L.LeaveType_Id','P.Employee_Id');
								   
			$iTotal = $this->_db->fetchAll($iTotalQry);
			/**
			 * Output array with Json encode
			*/
			return array("iTotalRecords" => count($iTotal), "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $leaveHistory);
		}
	}
	
	/**
	 *	getting the pending leave request (applied or returned)
	*/
    public function getPendingLeaveRequest($leaveClosureDetails)
    {  
		$leaveClosureDetails 		= $leaveClosureDetails['Leave_Closure_Details'];
		$leaveTypeId 				= array_unique(array_column($leaveClosureDetails,'LeaveType_Id'));
        return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empLeaves, new Zend_Db_Expr('count(Leave_Id)'))
									->where('Approval_Status IN (?)', array('Applied', 'Returned', 'Cancel Applied'))
									->where('LeaveType_Id IN (?)',$leaveTypeId));
    }
	
	/**
	 *	to run the carry over process
	*/
    public function empLeaveCarryOver ($sessionId,$leaveClosureDetails)
    {
		$leaveTypeClosureDetails 	= $leaveClosureDetails['Leave_Closure_Details'];
		$leaveTypeId 				= array_unique(array_column($leaveTypeClosureDetails,'LeaveType_Id'));
		/** Get the carryover related query */
		$qryLeaveCarryOverDetails = $this->getEncashmentLeaveTypeDetails('carryover','Yes',$leaveTypeId);
		if(!empty($qryLeaveCarryOverDetails)){    	
			$historyRes = $this->_db->fetchAll($qryLeaveCarryOverDetails);
			$updateAllLeaves = $this->updateCOLeaveYear($sessionId,$leaveClosureDetails);
			if($updateAllLeaves['success'])
			{
				$leaveCarryForward=0;
				foreach ($historyRes as $r)
				{
					if($r['Carry_Over_Accumulation_Limit'] > 0)
					{
						$eligibleLeaveDetails = $this->getLeaveClosureDetails($r['Employee_Id'],$r['LeaveType_Id']);

						if(!empty($eligibleLeaveDetails))
						{
							$previousYearEmployeeLeaveEligiblity = $r['Eligible_Days'];
							$totalLeavesTaken = $r['Leaves_Taken']+$r['Encashed_Days'];
							if($totalLeavesTaken >= $previousYearEmployeeLeaveEligiblity)
							{
								$carryOverDays = 0;
							}
							else
							{
								$previousYearBalance = $previousYearEmployeeLeaveEligiblity-$totalLeavesTaken;
								$carryOverDays 	 	= Min($previousYearBalance,$r['CarryOver_Limit']);
							}

							//when the last year carry over balance exist we need to add with current year carryover balance
							if($r['Last_CO_Balance'] > 0)
							{
								$lastCoBalance = $r['Last_CO_Balance'];
							}
							else
							{
								$lastCoBalance = 0;
							}
							$carryOverDays 	 	= $lastCoBalance+$carryOverDays;

							$currentYearEmployeeLeaveEligiblity = $eligibleLeaveDetails['Eligible_Days'];
							$accumulationLimit 					= $r['Carry_Over_Accumulation_Limit']-$currentYearEmployeeLeaveEligiblity;
							if($accumulationLimit > $carryOverDays)
							{
								$accumulationLapsedDays = 0;
								$carryOverDays 			= $carryOverDays;
							}
							else 
							{
								$accumulationLapsedDays = $carryOverDays-$accumulationLimit;
								$carryOverDays 			= $accumulationLimit;
							}
						}
						else
						{
							$carryOverDays 			= 0;
							$accumulationLapsedDays = 0;
						}
					}
					else 
					{
						$carryOverDays = 0;
						$accumulationLapsedDays = 0;
					}

					$empEligibleLeave = array('Last_CO_Balance' 		=> $carryOverDays,
											  'Accumulation_Lapsed_Days'=> $accumulationLapsedDays,
											  'No_Of_Days'      		=> $carryOverDays);

					$where  = array('LeaveType_Id = ?' => $r['LeaveType_Id'],'Employee_Id = ?' => $r['Employee_Id']);
					$updated = $this->_db->update($this->_ehrTables->empEligbleLeave,$empEligibleLeave, $where);
					if(!empty($updated))
					{
						$leaveCarryForward++;
					}
				}

				if(!empty($leaveCarryForward))
				{
					return array('success'=>true, 'msg' => 'Leaves Carried over successfully', 'type' => 'success');
				}
				else
				{
					$trackSysLog = $this->_dbCommonFun->updateResult (array('updated'        => 1,
																			'action'         => 'Edit',
																			'trackingColumn' => date('Y')-1,
																			'trackingMsg'    => 'Carry forward leaves added and deferred leaves deleted for the year-',
																			'formName'       => 'Leave Closure',
																			'sessionId'      => $sessionId,
																			'tableName'      => $this->_ehrTables->leavetype));

					$trackSysLog['msg'] = 'Leave closure process was not completed. Please contact system admin to execute the leave closure process further.';
					$trackSysLog['type'] = 'warning';

					return $trackSysLog;
				}
			}
			else
			{
				/** The success param is send as true here to close the leave closure popup. Because the process is completed partially */
				return array('success'=>true, 'msg' => $updateAllLeaves['msg'], 'type' => 'warning');
			}
		}else{
			return array('success' => false, 'msg' => 'Something went wrong while executing the leave carry over process. Please contact system admin.', 'type' => 'warning');
		}
    }
	
	/**
	 *	this will update the CO_Year column in the leave type table if the carry over or leave closure process is done
	*/
	public function updateCOLeaveYear($sessionId,$leaveClosureDetails)
    {
		$eligibleLeaveDetails = $leaveClosureDetails['Leave_Closure_Details'];
		if(!empty($eligibleLeaveDetails))
		{
			$employeeId  				= array_unique(array_column($eligibleLeaveDetails,'Employee_Id'));
			$leaveTypeId 				= array_unique(array_column($eligibleLeaveDetails,'LeaveType_Id'));
			$coYear      				= array_unique(array_column($eligibleLeaveDetails,'CO_Year'));
			$eligibleLeaveId  	  		= array_unique(array_column($eligibleLeaveDetails,'Eligible_Leave_Id'));
			$auditEmployeeEligibleLeave = array();
			foreach($eligibleLeaveDetails as $eligibleLeave)
			{
				$auditEmployeeEligibleLeave[] = array('LeaveType_Id'             => $eligibleLeave['LeaveType_Id'],
													  'Employee_Id'   		     => $eligibleLeave['Employee_Id'],
													  'Eligible_Days' 		     => $eligibleLeave['Eligible_Days'],
													  'Leaves_Taken'			 => $eligibleLeave['Leaves_Taken'],
													  'Leave_Balance'			 => $eligibleLeave['Leave_Balance'],
													  'No_Of_Days'				 => $eligibleLeave['No_Of_Days'],
													  'Last_CO_Balance'			 => $eligibleLeave['Last_CO_Balance'],
													  'CO_Year'      			 => $eligibleLeave['CO_Year'],
													  'LE_Year'       		     => $eligibleLeave['CO_Year'],
													  'Leave_Closure_Start_Date' => $eligibleLeave['Leave_Closure_Start_Date'],
													  'Leave_Closure_End_Date'   => $eligibleLeave['Leave_Closure_End_Date'],
													  'Audit_Reason' 			 => 'leave-closure',
													  'Added_On'   				 => date('Y-m-d H:i:s'),
													  'Added_By' 				 => $sessionId);
			}

			if(!empty($auditEmployeeEligibleLeave))
			{
				$inserted 					= $this->_ehrTables->insertMultiple($this->_ehrTables->auditEmpEligbleLeave, $auditEmployeeEligibleLeave);
				if($inserted)
				{
					$deleteEligibleLeaves = $this->_db->quoteInto('Eligible_Leave_Id IN (?)', $eligibleLeaveId);
					$deleted			  = $this->_db->delete($this->_ehrTables->empEligbleLeave,$deleteEligibleLeaves);
				}
			}
			$leaveTypeIds     	 = $this->_db->quoteInto('LeaveType_Id IN (?)', $leaveTypeId);
			$leaveClosureYear 	 = array('Leave_Closure_Year'=>'Current Year');
			$updated 		  	 = $this->_db->update($this->_ehrTables->leavetype,$leaveClosureYear, $leaveTypeIds);
			$leaveBalanceUpdated = $this->empDOJUpdateEligibleDays(NULL,'leave-closure',$sessionId,$leaveTypeId); 
			return $leaveBalanceUpdated;
		}
    }

	public function empDOJUpdateEligibleDays($employeeId=NULL,$action,$sessionId=NULL,$leaveTypeId=NULL,$updateProbationEmployeeLeaveBalance=0,$leaveTypeUpdation=NULL) 
	{
		$updatedEmp 			= 0;
		$eligibleEmployeesNotFound = 0;
		$employeeEligibleLeave 	= array();
		$dbMaternityleave 		= new Employees_Model_DbTable_MaternitySlabs();
		$leaveTypeDetails 		= $this->getLeaveTypeDetails($updateProbationEmployeeLeaveBalance,$leaveTypeId);
		$leaveTypeMinimumDurationDetails = $this->getLeaveTypeMinimumDuration($leaveTypeId);
		$serviceBasedLeaveDetails  = $this->_dbServiceLeave->getEmployeeServiceLeave();
		$allLeaveClosureDates = array();
		foreach ($leaveTypeDetails as $row)
		{
			//roster leave we should not add the balance in emp_eligible leave during leave type and employee import record creation
			if($row['Leave_Enforcement_Configuration']!=6)
			{
				$leaveTypeMinimumDuration = (!empty($leaveTypeMinimumDurationDetails) && isset($leaveTypeMinimumDurationDetails[$row['LeaveType_Id']])) ? $leaveTypeMinimumDurationDetails[$row['LeaveType_Id']][0] : '';
				$leaveClosureDates = array();
				if($row['Leave_Closure_Based_On']=='Selected Month'){
					$leaveClosureDates['finstart'] = $row['Leave_Closure_Start_Date'];
					$leaveClosureDates['finend'] = $row['Leave_Closure_End_Date'];
					$leaveClosureDates['coyear']   = date('Y',strtotime($leaveClosureDates['finstart']));
				}

				if($action=='leave-closure')
				{
					$isLeaveTypeNotUsed = true;
				}
				else
				{
					if(empty($employeeId))
					{
						$isLeaveTypeNotUsed = $this->getLeaveTypeUsed($row['LeaveType_Id']); 
					}
					else
					{
						$validEmployeeId = $this->getLeaveTypeNotUsedEmployeeIds($employeeId,$row['LeaveType_Id']); 
						if(!empty($validEmployeeId))
						{
							$isLeaveTypeNotUsed = true;
						}
						else
						{
							$isLeaveTypeNotUsed = false;
						}
					}
				}
				if($isLeaveTypeNotUsed)
				{
					//During leave closure get the next year leave closure date for a selected month leave type
					if($action==='leave-closure')
					{
						if($row['Leave_Closure_Based_On']=='Selected Month')
						{
							$getLeaveClosureDates = $this->getSelectedMonthLeaveClosureDates($row['Leave_Closure_Month'],$row['Leave_Closure_End_Year'],'leave-closure');
							$leaveClosureDates = array(
								'finstart' => $getLeaveClosureDates['finstart'],
								'finend' => $getLeaveClosureDates['finend'],
								'leaveTypeId' => $row['LeaveType_Id'],
								'coyear' => date('Y',strtotime($getLeaveClosureDates['finstart'])),
								'leaveclosureendyear' => $getLeaveClosureDates['leaveclosureendyear']
							);
							array_push($allLeaveClosureDates,$leaveClosureDates);
						}
					}
					$activeEmployeeDetails = $this->getActiveEmployeesDetails($row,$employeeId,$action,$leaveClosureDates);
					if(!empty($activeEmployeeDetails))
					{
						foreach($activeEmployeeDetails as $activeEmployee)
						{
							if($row['Leave_Enforcement_Configuration']==1)
							{
								if (in_array($row['Leave_Closure_Based_On'], ['Limited Replenishment on Approval', 'Unlimited Replenishment on Approval'])) 
								{
									$eligibleDays = $activeEmployee['Total_Days'];
								}
								else
								{
									if(!empty($activeEmployee['Proration_Date']))
									{
										$activeEmployee['leaveTypeMinimumDuration'] = $leaveTypeMinimumDuration;
										$eligibleDays = $this->calculateEligibleDays($activeEmployee);
									}
									else
									{
										$eligibleDays = $activeEmployee['Total_Days'];
									}
								}
							}

							if($updateProbationEmployeeLeaveBalance === 0)
							{
								if($row['Leave_Enforcement_Configuration']==2)
								{
									if($row['Leave_Closure_Based_On']=='Employee Service')
									{
										$leaveDetails  	= $this->_dbServiceLeave->getEmployeeServiceLeaveBasedOnExperience($serviceBasedLeaveDetails,$activeEmployee['Employment_Year'],$row['LeaveType_Id']);
										if(!empty($leaveDetails))
										{
											$eligibleDays  				= $leaveDetails['Total_Days'];
											//when the employee service is not falling in the  
											if(empty($leaveDetails['Service_From']))
											{
                                      			$leaveClosureStartDate = $activeEmployee['Date_Of_Join'];
											}
											else
											{
												$leaveClosureDates 	   = $this->replenishmentLeaveClosureDate($activeEmployee['Date_Of_Join'],$leaveDetails['Service_From']);
												$leaveClosureStartDate = date('Y-m-d', strtotime("+1 day", strtotime($leaveClosureDates['finend'])));
											}
											
											$leaveClosureDates 			= $this->replenishmentLeaveClosureDate($leaveClosureStartDate,$leaveDetails['Service_To']);
											$activeEmployee['coyear'] 	= $leaveClosureDates['coyear'];
											$activeEmployee['finstart']	= $leaveClosureDates['finstart'];
											$activeEmployee['finend']	= $leaveClosureDates['finend'];
										}
										else
										{
											$eligibleDays  				= 0;
										}
									}
									else
									{
										$eligibleDays  	= $this->_dbServiceLeave->getServiceLeaveTotalDays($row['LeaveType_Id'],$activeEmployee['Employment_Year']);
									}
								}
								else if($row['Leave_Enforcement_Configuration']==3 && $row['Coverage']!='CUSTOMGROUP')
								{
									$eligibleDays  	= $this->_dbServiceLeave->getQuarterWiseLeaveTotalDays($activeEmployee['Employee_Id'],$row['LeaveType_Id'],$activeEmployee['Employment_Year'],$activeEmployee['Date_Of_Join']);
								}
								else if($row['Leave_Enforcement_Configuration']==4 && $row['Coverage']!='CUSTOMGROUP')
								{
									$childRange	   = $activeEmployee['Child_Count'];
									$eligibleDays  = $dbMaternityleave->maternityLeaveSlab($activeEmployee['Employee_Id'],$row['LeaveType_Id'],$childRange);
								}
								else if($row['Leave_Enforcement_Configuration']==5 && $row['Coverage']!='CUSTOMGROUP')
								{
									$eligibleDays 	 = $this->getExperienceLeave($activeEmployee['Employee_Id'],$row['LeaveType_Id'],$activeEmployee['Proration_Date'],$activeEmployee);
								}
							}

							
							// Check if leave closure is based on limited replenishment on approval and if replenishment limit is set
							if (in_array($row['Leave_Closure_Based_On'], ['Limited Replenishment on Approval']) && !empty($row['Replenishment_Limit'])) 
							{
								$replenishmentLimit = 1;
							}
							else
							{
								$replenishmentLimit = 0;
							}

							$employeeEligibleLeave[] = array('LeaveType_Id'   			=> $activeEmployee['LeaveType_Id'],
															'Employee_Id'   			=> $activeEmployee['Employee_Id'],
															'Eligible_Days' 			=> $eligibleDays,
															'Replenishment_Limit'		=> $replenishmentLimit,
															'CO_Year'      				=> $activeEmployee['coyear'],
															'LE_Year'       			=> $activeEmployee['coyear'],
															'Leave_Closure_Start_Date'  => $activeEmployee['finstart'],
															'Leave_Closure_End_Date'    => $activeEmployee['finend']);
						}	
					}
				}
			}
		}

		if(!empty($employeeEligibleLeave))
		{
			$insertEmployeeEligibleLeave 	= $this->insertEmployeeEligibleLeave($employeeEligibleLeave,$leaveTypeUpdation,$action);
			$updatedEmp 					= $insertEmployeeEligibleLeave;
		}else{
			$eligibleEmployeesNotFound = 1;
		}
		
		if($action=='leave-closure') {
			foreach($allLeaveClosureDates as $leaveClosureDetails){
				$updateLeaveClosureDetails = array('Leave_Closure_Start_Date' => $leaveClosureDetails['finstart'],
										'Leave_Closure_End_Date' => $leaveClosureDetails['finend'],
										'Leave_Closure_End_Year' => $leaveClosureDetails['leaveclosureendyear']);
				$whereLeaveTypeId = $this->_db->quoteInto('LeaveType_Id = ?', $leaveClosureDetails['leaveTypeId']);
				//Update leave closure start date and end date
				$updated = $this->_db->update($this->_ehrTables->leavetype,$updateLeaveClosureDetails, $whereLeaveTypeId);
			}
			$updated = 1;
			if(!empty($updatedEmp) ){
				$isLeaveBalanceUpdated = 1;
				$message = 'Leave balance is updated for the eligible employees';
			}else{
				$isLeaveBalanceUpdated = 0;
				$message = 'Leave balance not updated completely for the eligible employees';
			}

			/** Log the leave balance update result */
			$this->_dbCommonFun->updateResult (array('updated'        => $updated,
													'action'         => 'Add',
													'trackingMsg'    => $message.' during leave closure- ',
													'trackingColumn' => date('Y')-1,
													'formName'       => 'Leave Closure',
													'sessionId'      => $sessionId,
													'tableName'      => $this->_ehrTables->leavetype));

			$message = (empty($isLeaveBalanceUpdated) ? ($message.'.Please contact the system admin to update the leave balance') : $message);

			/**
			 *	this function will handle
			*		update system log function
			*		clear submit lock fucntion
			*		return success/failure array
			*/
			$trackSysLog = $this->_dbCommonFun->updateResult (array('updated'        => $updated,
																	'action'         => 'Edit',
																	'trackingMsg'    => 'Leave Closure for the year- ',
																	'trackingColumn' => date('Y')-1,
																	'formName'       => 'Leave Closure',
																	'sessionId'      => $sessionId,
																	'tableName'      => $this->_ehrTables->leavetype));


			if(!empty($updated)){
				// If the process is completed partially then leave closure modal has to be closed if required and warning message has to be presented
				$trackSysLog['success'] = !empty($isLeaveBalanceUpdated) ? true : false;
				$trackSysLog['msg'] = 'Leaves Carried over successfully.'.$message;
				$trackSysLog['type'] = ($trackSysLog['success']) ? 'success' : 'warning';
			}else{
				$trackSysLog['msg'] = 'Leaves carried over process is not completed. Please contact the system admin to execute the leave closure process further.';
			}
			return $trackSysLog;
		}else if($action=='add-leave-type') {
			//If eligible employees not found while adding the leave type we should not consider it as error
			return ($eligibleEmployeesNotFound == 1 ) ? 1 : $updatedEmp;
		} else {
			return $updatedEmp;
		}
	}
	/**
	 *	to list the carryover leaves in grid
	*/
	public function carryoverLeave ($page, $rows,$searchAll, $searchArr,$leaveClosureDetails)
    {
		$leaveClosureDetails 		= $leaveClosureDetails['Leave_Closure_Details'];
		$leaveTypeId 				= array_unique(array_column($leaveClosureDetails,'LeaveType_Id'));
		$coleave = $searchArr['CarryOver_Leave'];
		/** Get the leave encashment query */
		$qryLeaves = $this->getEncashmentLeaveTypeDetails('carryover','Yes',$leaveTypeId);

		if(!empty($qryLeaves)){
			$qryLeaves ->order("P.Employee_Id ASC")
									->limit($rows, $page);
        
			if (!empty($coleave) && preg_match('/^[0-9]/', $coleave))
			{
				$qryLeaves->where('L.LeaveType_Id = ?',$coleave);
			}
			/**
			 *	Search All columns using single input
			*/
			if (!empty($searchAll) && $searchAll != null)
			{
				$conditions = $this->_db->quoteInto(new Zend_Db_Expr('Concat(P.Emp_First_Name," ",P.Emp_Last_Name) Like ?'),"%$searchAll%");		
				$conditions .= $this->_db->quoteInto('or J.User_Defined_EmpId Like ?', "%$searchAll%");
				$conditions .= $this->_db->quoteInto('or en.Encashed_Days Like ?', "%$searchAll%");
				$qryLeaves->where($conditions);
			}
			
			/**
			 * SQL queries
			 * Get data to display
			*/
			$leaves = $this->_db->fetchAll($qryLeaves);
			
			foreach ($leaves as $key => $carryOverLeaveDetails)
			{
				/* Encashed Days */
				$leaves[$key]['Encashed_Days'] = 0;
				if(isset($carryOverLeaveDetails['Encashed_Days']) && !IS_NULL($carryOverLeaveDetails['Encashed_Days'])){
					$leaves[$key]['Encashed_Days'] = $carryOverLeaveDetails['Encashed_Days'];
				}

				$leaves[$key]['Leaves_Taken'] = 0;
				if(isset($carryOverLeaveDetails['Leaves_Taken']) && !IS_NULL($carryOverLeaveDetails['Leaves_Taken'])){
					$leaves[$key]['Leaves_Taken'] = $carryOverLeaveDetails['Leaves_Taken'];
				}

				$leaves[$key]['Last_CO_Balance'] = 0;
				if(isset($carryOverLeaveDetails['Last_CO_Balance']) && !IS_NULL($carryOverLeaveDetails['Last_CO_Balance'])){
					$leaves[$key]['Last_CO_Balance'] = $carryOverLeaveDetails['Last_CO_Balance'];
				}
				
				$totalLeaveTaken = $leaves[$key]['Leaves_Taken']+$leaves[$key]['Encashed_Days'];
				$currentYearEmployeeLeaveEligiblity = $leaves[$key]['Eligible_Days'];
				if( $currentYearEmployeeLeaveEligiblity > $totalLeaveTaken)
				{
					$currentYearLeaveBalance 	= $currentYearEmployeeLeaveEligiblity-$totalLeaveTaken;
					$leavesForward 				= min(array($currentYearLeaveBalance, $carryOverLeaveDetails['CarryOver_Limit']));
				}
				else
				{
					$currentYearLeaveBalance 	= 0;
					$leavesForward 				= 0;
				}

				$totalCarryOverBalance = $leaves[$key]['Last_CO_Balance']+$leavesForward;
			
				$leaves[$key]['Current_Year_Leave_Balance'] 	= number_format((float)$currentYearLeaveBalance,1, '.', '');
				$leaves[$key]['Leaves_Forward'] 				= number_format((float)$leavesForward,1, '.', '');
				$leaves[$key]['CarryOver_Limit'] 				= number_format((float)$carryOverLeaveDetails['CarryOver_Limit'],1, '.', '');
				$leaves[$key]['Last_CO_Balance'] 				= number_format((float)$leaves[$key]['Last_CO_Balance'],1, '.', '');
				$leaves[$key]['Total_Carry_Over_Balance'] 		= number_format((float)$totalCarryOverBalance,1, '.', '');
			}
			
			/* Data set length after filtering */
			$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
			
			/* Total data set length */
			$iTotal = $iTotalDisplay;
												
			/**
			 * Output array with Json encode
			*/
			return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $leaves);
		}else{
			return array("iTotalRecords" => 0, "iTotalDisplayRecords" => 0, "aaData" => 0);
		}
	}
	
	/**
	 *	to update the LE_Year column in the leave type table if the leave encashment process is done
	*/
    public function updateLeaveEncashmentYear($sessionId,$leaveClosureDetails)
    {
		$eligibleLeaveDetails = $leaveClosureDetails['Leave_Closure_Details'];
		if(!empty($eligibleLeaveDetails))
		{
			$eligibleLeaveId  = array_unique(array_column($eligibleLeaveDetails,'Eligible_Leave_Id'));
			$whereCondition = $this->_db->quoteInto('Eligible_Leave_Id IN (?)', $eligibleLeaveId);
			$leaveEncashmentYear = array('LE_Year' => new Zend_DB_Expr('LE_Year+1'));
			$updated = $this->_db->update($this->_ehrTables->empEligbleLeave,$leaveEncashmentYear, $whereCondition);
		}
		else
		{
            $updated = 1;
		}

		/**
		 *	this function will handle
		 *		update system log function
		 *		clear submit lock fucntion
		 *		return success/failure array
		*/
		return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
														'action'         => 'Edit',
														'trackingColumn' => date('Y')-1,
														'trackingMsg'    => 'Leave Encashment for the year - ',
														'formName'       => 'Leave Encashment',
														'sessionId'      => $sessionId,
														'tableName'      => $this->_ehrTables->leavetype));
    }
	
	/**
	 *	to run the leave encashment process //add this in applyencashment table
	*/
    public function empLeaveEncashment ($sessionId,$leaveClosureDetails)
    {
		$leaveTypeClosureDetails 		= $leaveClosureDetails['Leave_Closure_Details'];
		$leaveTypeId 				= array_unique(array_column($leaveTypeClosureDetails,'LeaveType_Id'));
		$qryLeaveEncashmentDetails = $this->getEncashmentLeaveTypeDetails('leaveEncashment','Yes',$leaveTypeId);

		if(!empty($qryLeaveEncashmentDetails)){
			$leaveEncashmentDetails = $this->_db->fetchAll($qryLeaveEncashmentDetails);

			/** Calculate the employee encashment applicable days for the leave type and append it to the $historyRes array */
			$leaveEncashmentDetails = $this->getEmpLeaveTypeEncashmentRemainingDays($leaveEncashmentDetails);
			$insertEncashment = $insertle = array();
			
			foreach ($leaveEncashmentDetails as $r)
			{
				$remEncashmentDays = $r['Emp_Encashment_Remaining_Eligible_Days'];
				if($remEncashmentDays > 0){
					$insertEncashment[] = array('LeaveType_Id'    => $r['LeaveType_Id'],
												'Employee_Id'     => $r['Employee_Id'],
												'Encashed_Days'   => $remEncashmentDays,
												'Encashed_Status' => 'Applied',
												'EN_Year'         => $r['LE_Year'],
												'Encashed_Date'   => date('Y-m-d H:i:s'));
				}
			}

			/** If the employee encashment record exists */
			if(count($insertEncashment) > 0){
				$inserted = $this->_ehrTables->insertMultiple($this->_ehrTables->encashedLeave, $insertEncashment);

				/** If the employee encashment are applied */
				if ($inserted)
				{
					/** Update the leave encashment year in the table */
					return $this->updateLeaveEncashmentYear($sessionId,$leaveClosureDetails);
				}
				else
				{
					$trackSysLog = $this->_dbCommonFun->updateResult (array('updated'        => 1,
																			'action'         => 'Edit',
																			'trackingColumn' => date('Y')-1,
																			'trackingMsg'    => 'Leave Encashment added in the encashed leaves only for the year-',
																			'formName'       => 'Leave Encashment',
																			'sessionId'      => $sessionId,
																			'tableName'      => $this->_ehrTables->leavetype));

					$trackSysLog['msg'] = 'Leave encashment process was not completed. Please contact system admin to execute the leave encashment process further.';
					$trackSysLog['type'] = 'warning';
					return $trackSysLog;
				}
			}else{
				/** Update the leave encashment year in the table if there is no necessity to insert the employee encashment record
				 * to reflect in the payslip
				*/
				return $this->updateLeaveEncashmentYear($sessionId,$leaveClosureDetails);
			}
		}else{
			return array('success' => false, 'msg' => 'Something went wrong while executing the leave encashment process. Please contact system admin.', 'type' => 'warning');
		}
    }
	
	/**
	* list ehcashment Leaves and auto encashment leaves
	*/
	public function listLeaveEncashment($page, $rows, $sortField, $sortOrder, $searchAll, $searchArr, $leaveEncashmentAccess)
	{
		$sessionId				= $leaveEncashmentAccess['SessionId'];
		$employeeName 			= $searchArr['Employee_Name'];
		$leaveType 				= $searchArr['Leave_Type'];
		$encashStartDate	 	= $searchArr['Encash_Start_Date'];
		$encashEndDate      	= $searchArr['Encash_End_Date'];
		$leaveEncashmentStatus 	= $searchArr['Encashed_Status'];
		
		/**
		 *	Sorting columns based on display column order in grid
		*/
		switch ($sortField)
		{
			case 1: $sortField = 'J.User_Defined_EmpId'; break;
			case 2: $sortField = 'LE.LeaveType_Id'; break;
			case 3: $sortField = 'LE.Encashed_Days'; break;
			case 4: $sortField = 'LE.EN_Year'; break;
			case 5: $sortField = 'LE.Encashed_Date'; break;
			case 6: $sortField = 'LE.Encashed_Status'; break;
		}

		/**
		 *	Query to fetch data from various tables
		*/
		$qryLeaveEncashment = $this->_db->select()->from(array('LE'=>$this->_ehrTables->encashedLeave),
								array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS LE.Encashment_Id as count'),'LE.Encashment_Id',
								'LE.Employee_Id','LE.LeaveType_Id','LE.Encashed_Days','LE.EN_Year',
								new Zend_Db_Expr("DATE_FORMAT(LE.Encashed_Date,'".$this->_orgDF['sql']."') as Encashed_Date"),'LE.Encashed_Status'))
								->joinInner(array('leave'=>$this->_ehrTables->leavetype),'leave.LeaveType_Id=LE.LeaveType_Id',
											array('leave.Leave_Name'))
								->joinInner(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=LE.Employee_Id',
											array(new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as Employee_Name"),
												  'emp.Employee_Id'))
								->joinInner(array('J'=>$this->_ehrTables->empJob),'emp.Employee_Id = J.Employee_Id',
											array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN J.User_Defined_EmpId IS NULL THEN emp.Employee_Id ELSE J.User_Defined_EmpId END')))				  
								->order("$sortField $sortOrder")
								->limit($rows, $page);

		if (empty($leaveEncashmentAccess['Admin']))
		{
			$getEmployeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
													->where('Manager_Id = ?', $sessionId));
			
			if ( $leaveEncashmentAccess['Is_Manager'] == 1 && !empty($getEmployeeId))
			{
				$qryLeaveEncashment
					->where('LE.Employee_Id = :EmpId or LE.Employee_Id IN (?)', $getEmployeeId)
					->bind(array('EmpId'=>$sessionId));
			}
			else
			{
				$qryLeaveEncashment->where('LE.Employee_Id = ?', $sessionId);
			}
		}
	    /**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			$conditions = $this->_db->quoteInto(new Zend_Db_Expr('Concat(emp.Emp_First_Name," ",emp.Emp_Last_Name) Like ?'),"%$searchAll%");
			$conditions .= $this->_db->quoteInto('or leave.Leave_Name Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or LE.Encashed_Days Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or J.User_Defined_EmpId Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or LE.Encashed_Date Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or LE.Encashed_Status Like ?', "%$searchAll%");
			
			$qryLeaveEncashment->where($conditions);
		}
		if (! empty($employeeName) && preg_match('/^[a-zA-Z]/', $employeeName))
		{
            $qryLeaveEncashment->where($this->_db->quoteInto(new Zend_Db_Expr('Concat(emp.Emp_First_Name," ",emp.Emp_Last_Name) Like ?'),"%$employeeName%"));
		}
		if (! empty($leaveType) && preg_match('/^[0-9]*$/', $leaveType))
            $qryLeaveEncashment->where('LE.LeaveType_Id =?',$leaveType);
        
		if (!empty($encashStartDate) && $encashStartDate != null)
			$qryLeaveEncashment->where('LE.Encashed_Date >= ?', $encashStartDate);
		
		if (!empty($encashEndDate) && $encashEndDate != null)
			$qryLeaveEncashment->where('LE.Encashed_Date <= ?', $encashEndDate);
		
		
		if (! empty($leaveEncashmentStatus) && preg_match('/^[a-zA-Z]/', $leaveEncashmentStatus))
            $qryLeaveEncashment->where('LE.Encashed_Status Like?', "$leaveEncashmentStatus%");
	
    	
		$leaveEncashment= $this->_db->fetchAll($qryLeaveEncashment);	
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
			/* Total data set length */
	    $iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->encashedLeave, new Zend_Db_Expr('COUNT(Encashment_Id)')));
	
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $leaveEncashment);
    
	}

	/**
	 *	to list the encashed leaves in grid
	*/ 
    public function encashedLeaves ($page, $rows,$searchAll, $encashmentArr,$leaveClosureDetails)
    {
		$leleave      		 = $encashmentArr['Leave_Type'];
		$leaveClosureDetails = $leaveClosureDetails['Leave_Closure_Details'];
		$leaveTypeId 		 = array_unique(array_column($leaveClosureDetails,'LeaveType_Id'));
		/** Get the leave encashment query */
		$qryLeaveEncashment = $this->getEncashmentLeaveTypeDetails('leaveEncashment','Yes',$leaveTypeId);

		if(!empty($qryLeaveEncashment)){
			$qryLeaveEncashment ->order("P.Employee_Id ASC")
									->limit($rows, $page);
			
			if (!empty($leleave) && preg_match('/^[0-9]/', $leleave))
			{
				$qryLeaveEncashment->where('L.LeaveType_Id = ?',$leleave);
			}
		
			/**
			 * SQL queries
			 * Get data to display
			*/
			$leaveEncashment = $this->_db->fetchAll($qryLeaveEncashment);
			
			foreach ($leaveEncashment as $key => $encashmentLeaveDetails)
			{
				/* Encashed Days */
				$leaveEncashment[$key]['Encashed_Days'] = 0;
				if(isset($encashmentLeaveDetails['Encashed_Days']) && !IS_NULL($encashmentLeaveDetails['Encashed_Days'])){
					$leaveEncashment[$key]['Encashed_Days'] = $encashmentLeaveDetails['Encashed_Days'];
				}

				$leaveEncashment[$key]['Leaves_Taken'] = 0;
				if(isset($encashmentLeaveDetails['Leaves_Taken']) && !IS_NULL($encashmentLeaveDetails['Leaves_Taken'])){
					$leaveEncashment[$key]['Leaves_Taken'] = $encashmentLeaveDetails['Leaves_Taken'];
				}
				
				$totalLeaveTaken = $leaveEncashment[$key]['Leaves_Taken']+$leaveEncashment[$key]['Encashed_Days'];
				$currentYearEmployeeLeaveEligiblity = $leaveEncashment[$key]['Eligible_Days'];
				if( $currentYearEmployeeLeaveEligiblity > $totalLeaveTaken)
				{
					$currentYearLeaveBalance 	= $currentYearEmployeeLeaveEligiblity-$totalLeaveTaken;
					$encashmentDays 			= min(array($currentYearLeaveBalance, $encashmentLeaveDetails['Encashment_Limit']));
				}
				else
				{
					$currentYearLeaveBalance 	= 0;
					$encashmentDays 			= 0;
				}
				$leaveEncashment[$key]['Current_Year_Leave_Balance'] = number_format((float)$currentYearLeaveBalance,1, '.', '');
				$leaveEncashment[$key]['Encashment_Days'] 			 = number_format((float)$encashmentDays,1, '.', '');
				$leaveEncashment[$key]['Encashment_Limit'] 			 = number_format((float)$encashmentLeaveDetails['Encashment_Limit'],1, '.', '');
			}

			/* Data set length after filtering */
			$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
			
			/* Total data set length */
			$iTotal = $iTotalDisplay;
			/**
			 * Output array with Json encode
			*/
			return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $leaveEncashment);
		}else{
			return array("iTotalRecords" => 0, "iTotalDisplayRecords" => 0, "aaData" => array());
		}
    }
	
	/**
	 *	to update the carry over leave balance if the employee cancels the leave which was applied from carry over balance
	*/
    public function updateCo ($leaveTypeId, $employeeId, $balance)
    {
        $where  = array('LeaveType_Id = ?' => $leaveTypeId,'Employee_Id = ?' => $employeeId);
        
        $updated = $this->_db->update($this->_ehrTables->empEligbleLeave, array('Last_CO_Balance' => $balance), $where);
		
		return $updated;
    }
	
	/**
     * Add enhancement
     */
    public function addEncashment ($encashmentDetails, $sessionId)
    {
		//get leave closure start and end date
		$startEndDate = $this->getLeaveClosureDetails($encashmentDetails['Employee_Id'],$encashmentDetails['LeaveType_Id']);

        $encashmentDetails['EN_Year'] = date("Y", strtotime($startEndDate['finstart']));
		
		$updated = $this->_db->insert($this->_ehrTables->encashedLeave, $encashmentDetails);

		/**
		 *	this function will handle
		 *		update system log function
		 *		clear submit lock fucntion
		 *		return success/failure array
		*/
		return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
														'action'         => 'Add',
														'trackingMsg'    => 'Apply Leave Encashment for the year-'.$encashmentDetails['EN_Year'].' for the employee id-',
														'trackingColumn' => $encashmentDetails['Employee_Id'],
														'formName'       => 'Leave Encashment',
														'sessionId'      => $sessionId,
														'tableName'      => $this->_ehrTables->encashedLeave));
    }
	
	/**
     * Check whether selected leave type already exists or not for an employee
     * with given status
     */
    public function ckLeaveStatus ($leaveId, $sessionId, $status)
    {
        return $this->_db->fetchOne($this->_db->select()
									->from($this->_ehrTables->empLeaves, new Zend_Db_Expr('count(Employee_Id)'))
									->where('Approval_Status = ?', $status)
									->where('Leave_Id = ?', $leaveId)
									->where('Approver_Id = ?', $sessionId));
    }
	
	/**
	 *	get the leave balance for employee and leavetype. same logic in leave history
	*/
	public function getEmpLeaveTypeBalance ($employeeId, $leaveTypeId) 
	{
	    $eligLvCond = $this->_db->quoteInto('el.LeaveType_Id = L.LeaveType_Id AND el.Employee_Id = ?', $employeeId);
		$enOnCond 	= $this->_db->quoteInto('en.LeaveType_Id = L.LeaveType_Id  AND en.EN_Year=el.CO_Year AND en.Employee_Id = ?', $employeeId);
		$lvBal 		= "((IFNULL(el.No_Of_Days, 0) + (CASE WHEN el.Eligible_Days is NULL THEN 0.0 ELSE el.Eligible_Days END)) - ((IFNULL(SUM(en.Encashed_Days), 0)) + IFNULL(SUM(el.Leaves_Taken), 0)))";
		$leaveTypeBalanceQuery = $this->_db->select()->from(array('L'=>$this->_ehrTables->leavetype), array('Leave_Balance'=>new Zend_Db_Expr($lvBal)))
									->joinInner(array('P'=>$this->_ehrTables->empPersonal),"P.Employee_Id = $employeeId", array())
									->joinInner(array('el'=>$this->_ehrTables->empEligbleLeave),$eligLvCond,array())
									->joinLeft(array('en'=>$this->_ehrTables->encashedLeave),$enOnCond,array())
									->where('L.LeaveType_Id = ?', $leaveTypeId)
									->group('L.LeaveType_Id');

		$leaveTypeBalance = $this->_db->fetchOne($leaveTypeBalanceQuery);
        return $leaveTypeBalance;
    }
	
	/**
	 *	to fetch the eligible days of the employee based on passed emloyeeIds for a particular leave type
	*/
	public function getCurrentYearLeaveEligiblityBasedOnLeave($employeeId, $leaveTypeId) 
	{
		$totalDays = 0;
        $eligibleDays = $this->_db->fetchAll($this->_db->select()->from(
					array('EL'=>$this->_ehrTables->empEligbleLeave),array('EL.Eligible_Days', 'EL.Employee_Id', new Zend_Db_Expr("$totalDays as Total_Days")))
					->where('EL.Employee_Id IN (?)',$employeeId)
					->where('EL.LeaveType_Id =?',$leaveTypeId));		
		return $eligibleDays;
	}

	/**
	 *	to fetch the encashed days record of an employee and a particular leave type
	*/
    public function getEncashedDays($leaveTypeId,$employeeId)
    {
		$leaveClosureDetails = $this->getLeaveClosureDetails($employeeId,$leaveTypeId);
	 	$encashedDays = $this->_db->fetchOne($this->_db->select()->from(array('EL'=>$this->_ehrTables->encashedLeave),new Zend_Db_Expr('SUM(Encashed_Days)'))
							->where('EL.EN_Year = ?', $leaveClosureDetails['coyear'])
							->where('EL.LeaveType_Id = ?', $leaveTypeId)
							->where('EL.Employee_Id = ?', $employeeId)); 

		if($encashedDays > 0)
		{
			$encashedDays = $encashedDays;
		}
		else
		{
			$encashedDays = 0;
		}					
        return $encashedDays;
    }
	
	/**
	 *	to update the availed leave table whenever an employee's leaverequest is approved and
	 *	he already has record for that leave type in this table
	*/
    public function updateLeaveBalance ($eligibleLeaveId,$leavesTaken, $leaveBalance)
    {
	    $updateData = array('Leaves_Taken'  => $leavesTaken,
    						'Leave_Balance' => $leaveBalance);
		
        $updated = $this->_db->update($this->_ehrTables->empEligbleLeave, $updateData, 'Eligible_Leave_Id='.(int)$eligibleLeaveId);

		$runReplinishmentClosure = $this->replenishmentDuringLeaveApproval($eligibleLeaveId);
		return $updated;
    }
	
	/**
     * Update status and insert comment
     */
    public function statusReport ($leaveData, $sessionId, $formName, $customFormName, $oldApprovalStatus)
    {
		$leaveId     		= $leaveData['Leave_Id'];
		$status      		= $leaveData['Status'];
		$comment     		= $leaveData['Comment'];
		$esicReason 		= $leaveData['ESIC_Reason'];
		$leaveDocuments		= $leaveData['documentList'];
		
		$eventId = "";
		$workFlowInitiatedId = "";
		$newWorkFlowInitiatedId="";
		$result =array();
		if($status === 'Cancel Applied' || $status==='Cancelled'){
			$workFlowAndLeaveData=$this->_db->fetchRow($this->_db->select()->from(array('EL'=>$this->_ehrTables->empLeaves), 
										array('EL.Reason','EL.Duration','EL.Start_Date','EL.End_Date','EL.Total_Days','EL.Hours','EL.Contact_Details','EL.Employee_Id','EL.LeaveType_Id','EL.Added_By','EL.Added_On','EL.Leave_Period','EL.Reason_Id','EL.Late_Attendance','EL.Late_Attendance_Hours','EL.Late_Attendance_Hours_From_Grace','EL.Always_Grace','EL.Workflow_Instance_Id'))
									->joinLeft(array('EAS'=>$this->_ehrTables->employeeAttendanceSummary),'EL.Leave_Id = EAS.Early_Checkout_Leave_Id',
										array('Early_Checkout' => new Zend_Db_Expr('CASE WHEN EAS.Early_Checkout_Leave_Id > 0 THEN 1 ELSE 0 END'),
										'EAS.Early_Checkout_Hours','EAS.Early_Checkout_Hours_From_Grace','EAS.Early_Checkout_Always_Grace_Hours'))
									->where('Leave_Id = ?', (int)$leaveId));

			$alternatePersonDetails=$this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->alternatePerson, array('Alternate_Person'))
			->where('Leave_Id = ?', (int)$leaveId));

			$leaveType=$workFlowAndLeaveData['LeaveType_Id'];

			$eventId=$this->_db->fetchOne($this->_db->select()
			->from(array('leave'=>$this->_ehrTables->leavetype),array('workflows.Event_Id'))
			->joinLeft(array('workflows'=>$this->_ehrTables->workflows),"leave.Workflow_Id = workflows.Workflow_Id",array())
			->where('leave.LeaveType_Id =?', $leaveType));

			if($eventId && $status==='Cancelled' && $workFlowAndLeaveData['Workflow_Instance_Id'])
			{
				$this->_db->delete($this->_ehrTables->taUserTask, array('process_instance_id = ?'=>$workFlowAndLeaveData['Workflow_Instance_Id']));
			}
			if($eventId && $status === 'Cancel Applied')
			{
				$instanceData = new \stdClass();
				$instanceData->formId = 31;
				if($workFlowAndLeaveData['Employee_Id']==$sessionId)
				{
					$instanceData->isSelfApply=1;
				}
				else{
					$instanceData->isSelfApply=0;
				}
				$instanceData->formName = "Leaves";
				$instanceData->employeeId =(int)$workFlowAndLeaveData['Employee_Id'];
				$instanceData->initiatorId =(int)$sessionId;
				$instanceData->leaveId =(int)$leaveId;
				$instanceData->reason=$workFlowAndLeaveData['Reason'];
				$instanceData->duration=strval($workFlowAndLeaveData['Duration']);
				$instanceData->startDate=$workFlowAndLeaveData['Start_Date'];
				$instanceData->endDate=$workFlowAndLeaveData['End_Date'];
				$instanceData->totalDays=strval($workFlowAndLeaveData['Total_Days']);
				$instanceData->hours=strval($workFlowAndLeaveData['Hours']);
				$instanceData->leaveTypeId=(int)$workFlowAndLeaveData['LeaveType_Id'];
				$instanceData->approvalStatus=$status;
				$instanceData->contactNo=$workFlowAndLeaveData['Contact_Details'];
				$instanceData->earlyCheckoutLeave=$workFlowAndLeaveData['Early_Checkout'];
				$instanceData->earlyCheckoutHours=$workFlowAndLeaveData['Early_Checkout_Hours'];
				$instanceData->earlyCheckoutHoursFromGrace=$workFlowAndLeaveData['Early_Checkout_Hours_From_Grace'];
				$instanceData->earlyCheckoutAlwaysGraceHours=$workFlowAndLeaveData['Early_Checkout_Always_Grace_Hours'];
				$instanceData->Attendance_Shortage = ($workFlowAndLeaveData['Attendance_Shortage'] == 1) ? 'Yes' : 'No';
				$instanceData->No_Attendance = ($workFlowAndLeaveData['Reason'] == "Auto LOP") ? 'Yes' : 'No';

				$documentList = new stdClass();

				if(!empty($leaveDocuments))
				{
					foreach($leaveDocuments as $key=>$row)
					{
						$documentList->$key = [$row][0]['File_Name'];
					}
				}
				
				$instanceData->documentList=$documentList;
				$instanceData->comment=$comment;
				$instanceData->esicReason=$esicReason;

				if(!empty($alternatePersonDetails))
				{
					$instanceData->alternatePerson=$alternatePersonDetails;
				}
				else{
					$instanceData->alternatePerson=0;				
				}
				if(empty($workFlowAndLeaveData['Leave_Period']))
				{
					$instanceData->leavePeriod="";
				}
				else{
					$instanceData->leavePeriod=strval($workFlowAndLeaveData['Leave_Period']);
				}
				if(empty($workFlowAndLeaveData['Reason_Id']))
				{
					$instanceData->reasonId=0;
				}
				else{
					$instanceData->reasonId=(int)$workFlowAndLeaveData['Reason_Id'];
				}
				if(empty($workFlowAndLeaveData['Late_Attendance']))
				{
					$instanceData->lateAttendance=0;
				}
				else{
					$instanceData->lateAttendance=(int)$workFlowAndLeaveData['Late_Attendance'];
				}
				$instanceData->lateAttendanceHours=$workFlowAndLeaveData['Late_Attendance_Hours'];
				$instanceData->lateAttendanceHoursFromGrace=$workFlowAndLeaveData['Late_Attendance_Hours_From_Grace'];
				$instanceData->alwaysGrace=$workFlowAndLeaveData['Always_Grace'];
				$instanceData->addedOn=$workFlowAndLeaveData['Added_On'];
				$instanceData->addedBy=(int)$workFlowAndLeaveData['Added_By'];
				$instanceData->updatedOn=date('Y-m-d H:i:s');;
				$instanceData->updatedBy=(int)$sessionId;

				$workFlowInitiatedId=$workFlowAndLeaveData['Workflow_Instance_Id'];
			

				$checkWorkflowUpdate=0;
				$newWorkFlowInitiatedId=$this->initiateWorkflowEngine($eventId,$instanceData,$sessionId);
			}
		}

		if(!$eventId || ($eventId && $newWorkFlowInitiatedId || $status != 'Cancel Applied'))
		{
			$updLvStatusArr['Approval_Status'] = $status;
			$empLvlStatus = array('Cancel Applied','Cancelled');
	
			/** Approved on and approved by can be updated for the leave status 'Approved', 'Returned', 'Rejected', 'Cancel Approved' status only.
			 * If the employee cancel the applied status then approved by and approved on should not be updated
			 */
			if($status != 'Cancel Applied' && ($status != 'Cancelled' && $oldApprovalStatus === 'Applied')){
				$updLvStatusArr['Approved_By'] = $sessionId;
				$updLvStatusArr['Approved_On'] = date('Y-m-d H:i:s');
			}else{
				/** 1. If the status is Cancel Applied then 'updated by' and 'updated on' can be updated.
				 * 2. If the employee cancel the applied status leave then 'updated by' and 'updated on' can be updated.
				*/
				$updLvStatusArr['Updated_By'] = $sessionId;
				$updLvStatusArr['Updated_On'] = date('Y-m-d H:i:s');
			}
	
			$updated = $this->_db->update($this->_ehrTables->empLeaves, $updLvStatusArr , 'leave_Id = ' . $leaveId);
			
			if ($updated && !empty($comment))
			{
				$this->_db->insert($this->_ehrTables->comment, array('Form_Id'         => $this->_dbComment->getFormId($formName),
																	 'Emp_Comment'     => $comment,
																	 'Approval_Status' => $status,
																	 'Parent_Id'       => $leaveId,
																	 'Employee_Id'     => $sessionId,
																	 'Added_On'        => date('Y-m-d H:i:s')));
			}
			
			/**
			 *	this function will handle
			 *		update system log function
			 *		clear submit lock fucntion
			 *		return success/failure array
			*/
			$result = $this->_dbCommonFun->updateResult (array('updated'        => $updated,
															'action'         => 'status updated',
															'trackingMsg'    => 'Update Leave Status - ',
															'trackingColumn' => $leaveId.', '.$status,
															'formName'       => $customFormName,
															'sessionId'      => $sessionId,
															'tableName'      => $this->_ehrTables->empLeaves));
		
		}
		else
		{
			/**
			 *	failure array based on add/update action
			*/
			return array('success' => false, 'msg'=>'Unable to update ' .strtolower($customFormName).' status', 'type'=>'warning', 'eventId'=> $eventId);
		}
		// Update the response message if the leave status is cancel applied
		if($status === 'Cancel Applied'){
			if($updated){
				if($eventId)
				{
					if($workFlowInitiatedId)
					{
						$deleteUserTaskCount=$this->_db->delete($this->_ehrTables->taUserTask, array('process_instance_id = ?'=>$workFlowInitiatedId));
						$countUserTaskHistory=$this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->taUserTaskHistory,new Zend_Db_Expr('count(process_instance_id)'))
						->where("process_instance_id = ?",$workFlowInitiatedId));
	
						if($countUserTaskHistory==0)
						{
							$workflowApprovalStatus="Waiting for approval";
						}
						else if($deleteUserTaskCount==0)
						{
							$workflowApprovalStatus="Approved";
						}
						else{
							$workflowApprovalStatus="Partially approved";
						}
						$this->_db->insert($this->_ehrTables->leaveWorkflowHistory, array('Leave_Id'=>(int)$leaveId,'Workflow_Instance_Id'=>$workFlowInitiatedId,'Workflow_Approval_Status'=>$workflowApprovalStatus));
					}
					$workFlowData['Workflow_Status']= "Waiting for Approval";
					$workFlowData['Workflow_Instance_Id']=$newWorkFlowInitiatedId;
					$this->_db->update($this->_ehrTables->empLeaves, $workFlowData, 'Leave_Id='.(int)$leaveId);
				}
				$responseMsg = $customFormName.' cancel request applied successfully';
			}else{
				if($eventId && $newWorkFlowInitiatedId)
				{
					$this->_db->delete($this->_ehrTables->taUserTask, array('process_instance_id = ?'=>$newWorkFlowInitiatedId));
					$this->_db->delete($this->_ehrTables->taUserTaskHistory, array('process_instance_id = ?'=>$newWorkFlowInitiatedId));
					$this->_db->delete($this->_ehrTables->taProcessInstance, array('process_instance_id = ?'=>$newWorkFlowInitiatedId));
				}
				$responseMsg = 'Unable to apply the '.$customFormName.' cancel request';
			}

			$result['msg'] = $responseMsg;
		}
		$result['eventId']=$eventId;
		return $result;
    }

	// function to revert the leave balance for the leave type
	public function cancelLeaveBalance($totaldays, $employeeId, $leavetypeId){
		$availedLeaves 	 	= $this->getLeaveClosureDetails ($employeeId, $leavetypeId);
		if ($availedLeaves)
		{
			$currentYearEmployeeLeaveEligiblity = $availedLeaves['Eligible_Days'];
			$leaveTypeDetails  					= $this->getLeaveTypeRow($leavetypeId);
			$carryOverLeaveExist  				= $leaveTypeDetails['Carry_Over'];
			$encashedDays 						= $this->getEncashedDays ($leavetypeId, $employeeId);
			$totalLeavesTaken = $availedLeaves['Leaves_Taken']+$encashedDays;
			$coLeave = $availedLeaves['Last_CO_Balance'];
			/** check that carry over applicable or not 
			 * carryover leave should be presented in the deferred leave table
			 * total leave taken should be greater than current year leave eligiblity**/	
			if(!empty($carryOverLeaveExist) && $totalLeavesTaken > $currentYearEmployeeLeaveEligiblity && $coLeave){	
				$currentYearLeaveBalance 	= $totalLeavesTaken-$currentYearEmployeeLeaveEligiblity;
				$carryOverBalance 			= min(array($totaldays, $currentYearLeaveBalance));
				$lastCoBalance 				= $coLeave+$carryOverBalance;
				$this->updateCo ($leavetypeId, $employeeId, $lastCoBalance);
			}
				// to update the availed leave table
			$leavesTaken  = $availedLeaves['Leaves_Taken'] - $totaldays;
			$leaveBalance = $availedLeaves['Leave_Balance'] + $totaldays;
			
			$this->updateLeaveBalance ($availedLeaves['Eligible_Leave_Id'],$leavesTaken, $leaveBalance);
		}
		return 1;
	}
	
	/**
	 *	to update the status as cancelled while employee cancels the leave
	*/
    public function cancelLeave ($totaldays, $status, $employeeId, $leaveId, $leavetypeId, $sessionId, $comment, $formName, $customFormName)
    {
        // if the cancelled leave's status is approved the availed leaves table need to updated
        if ($status == "Cancel Applied")
        {
			$this->cancelLeaveBalance($totaldays, $employeeId, $leavetypeId);
        }

		$leaveDetails = array();
		$leaveDetails['Approved_By'] = $sessionId;
		$leaveDetails['Approved_On'] = date('Y-m-d H:i:s');
		$leaveDetails['Approval_Status'] = 'Cancelled';

		// this details will be updated only when the approved status record is getting cancelled
		// Update the leave approval status as cancelled and approved by and approved on details.
	    $updated = $this->_db->update($this->_ehrTables->empLeaves,$leaveDetails, 'Leave_Id='.(int)$leaveId);
        
        if ($updated)
        {
			$deleteUnpaidLeaveOverrideDetails = $this->deleteUnpaidLeaveOverrideDetails('Leave',(int)$leaveId);
			if(!empty($comment))
			{
				$this->_db->insert($this->_ehrTables->comment,array('Form_Id'         => $this->_dbComment->getFormId($formName),
																'Emp_Comment'     => $comment,
																'Approval_Status' => 'Cancelled',
																'Parent_Id'       => $leaveId,
																'Employee_Id'     => $sessionId,
																'Added_On'        => date('Y-m-d H:i:s')/*new Zend_Db_Expr('NOW()')*/));
			}
        }
		
        /**
		 *	this function will handle
		 *		update system log function
		 *		clear submit lock fucntion
		 *		return success/failure array
		*/
		return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
														'action'         => 'status updated',
														'trackingMsg'    => 'Cancelled the Leave',
														'trackingColumn' => '',
														'formName'       => $customFormName,
														'sessionId'      => $sessionId,
														'tableName'      => $this->_ehrTables->empLeaves));
    }
	
	/**
	 *	to fetch leave request record based on leaverequestid
	*/
    public function viewLeaveRequest ($leaveId)
    {
		return $this->_db->fetchRow($this->_db->select()
									->from(array('L'=>$this->_ehrTables->empLeaves),
										   array('L.Leave_Id', 'L.Employee_Id', 'L.Approver_Id', 'L.Added_By', 'L.LeaveType_Id',
												 'L.Total_Days', 'L.Start_Date', 'L.End_Date', 'L.Approval_Status', 'L.Duration'))
									
									->joinLeft(array('LT'=>$this->_ehrTables->leavetype), 'LT.LeaveType_Id=L.LeaveType_Id',
												 array('LT.Leave_Type','LT.Approve_Leave_Type_Code','LT.Cancel_Leave_Type_Code'))
											 
									->joinInner(array('E'=>$this->_ehrTables->empPersonal), 'E.Employee_Id=L.Employee_Id',
												array(new Zend_Db_Expr("CONCAT(E.Emp_First_Name, ' ', E.Emp_Last_Name) as Employee_Name")))
									
									->where('L.Leave_Id = ?', $leaveId));
    }
	
	/**
	 *	Status Approval for leave
	*/
	public function leaveStatusUpdate ($leaveData, $sessionId, $formName, $customFormName)
	{
		/**
		 *	Declare array values
		*/
		
		
		$approvalId  = $leaveData['Approval_Id'];
		$leaveAction = $leaveData['Leave_Action'];
		$status      = $leaveData['Status'];
		$comment     = $leaveData['Comment'];
		$leaveId     = $leaveData['Leave_Id'];
		$eventId	 = "";
		$asyncApiResult = '';
		/**
		 *	Check leave status is already exist or not in leave table
		*/
		$checkExist = $this->ckLeaveStatus ($leaveId, $sessionId, $status);
		
		if ($checkExist == 0)
		{
			$record = $this->viewLeaveRequest ($leaveId);
			
			$recEmployeeId  = $record['Employee_Id'];
			$recLeaveTypeId = $record['LeaveType_Id'];
			$recTotalDays   = $record['Total_Days'];
			$oldApprovalStatus = $record['Approval_Status'];
			
			/**
			 *	Leave Status is Approved
			*/
			if ($leaveAction == 'Approve' && $status == 'Approved')
			{
				$result = $this->approveLeaveRequest($record,$leaveData,$sessionId,$formName,$customFormName);
			}
			elseif (($leaveAction == 'Approve' && (in_array($status, array('Rejected', 'Returned')) && !empty($comment))) ||
					($leaveAction == 'CancelApply' && ($status == 'Cancel Applied') && !empty($comment)) ||
					($leaveAction == 'CancelApprove' && $status == 'Approved'))
			{
				$result = $this->statusReport ($leaveData, $sessionId, $formName, $customFormName, $oldApprovalStatus);
				$eventId = $result['eventId'];
			}
			elseif ($leaveAction == 'CancelApprove' && $status == 'Cancelled')
			{
				$result = $this->cancelLeave ($record['Total_Days'], $oldApprovalStatus, $recEmployeeId, $record['Leave_Id'],
											  $record['LeaveType_Id'], $sessionId, $comment, $formName, $customFormName);
			}elseif ($leaveAction == 'CancelAppliedLeave' && $status == 'Cancelled')
			{
				if($oldApprovalStatus === 'Applied'){
					/** If the employee cancel the applied status leave */
					$result = $this->statusReport ($leaveData, $sessionId, $formName, $customFormName, $oldApprovalStatus);
					$eventId = $result['eventId'];
				}else{
					$result = array('success' => false, 'msg' => 'Unable to cancel the leave as the status is already updated', 'type' => 'warning');	
				}
			}
			else
			{
				$result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
			}

			if ($result['success']){
				$attendanceSummaryTriggerStatus = array('Rejected','Cancelled');
				if(in_array($status,$attendanceSummaryTriggerStatus)){
					//Update the early checkout ignore status for the attendance summary if the leave request is early checkout leave
					$inputArray = array(
						'leaveId' => (int)$record['Leave_Id'],
						'summaryDate' => $record['Start_Date'],
						'employeeId' => $recEmployeeId,
						'approvalStatus' => $status,
						'sessionId' => $sessionId
					);
					$earlyCheckoutIgnoreResponse = $this->updateEarlyCheckoutIgnoreStatus($inputArray);
					//if the leave request is early checkout leave then attendance summary cannot be triggered
					if($earlyCheckoutIgnoreResponse['earlyCheckout']){
						$asyncApiResult = 'The early checkout leave request is cancelled or rejected. So early checkout is ignored and the attendance summary cannot be triggered.';
					} else{
						$attendanceSummarySelectedDetails=[];
						$attendanceSummarySelectedDetails[]=$record;
						$attendanceSummaryResult = $this->_dbCommonFun->triggerAttendanceSummaryStepFunction($attendanceSummarySelectedDetails,'leaves');
						$asyncApiResult = json_encode($attendanceSummaryResult);
					}
				}else{
					$asyncApiResult = 'Attendance summary cannot be triggered as the leave status is not rejected or cancelled';
				}

				$syntrumIntegrationTriggerStatus = array('Approved','Cancelled');
				if(in_array($status,$syntrumIntegrationTriggerStatus)){
					//Initiate Syntrum integration only upon leave approval or when a previously approved leave is cancelled.
					if(($leaveAction == 'Approve' && $status == 'Approved') || ($leaveAction == 'CancelApprove' && $status == 'Cancelled')){
						$leaveTypeCode = '';
						if($status == 'Approved'){
							$syntrumLeaveAction = 'Approve';
							$elementHeader = $record['Approve_Leave_Type_Code'];
						}else{
							$syntrumLeaveAction = 'Cancel';
							$elementHeader = $record['Cancel_Leave_Type_Code'];
						}
						if(empty($elementHeader)){
							$leaveTypeCode = $this->getLeaveTypeCodeFromSlab($recLeaveTypeId,$syntrumLeaveAction);
						}

						if(!empty($leaveTypeCode) || !empty($elementHeader)){
							$syntrumInputDetails = array(
								'uniqueIds' => array((int)$record['Leave_Id']),
								'elementHeader' => $elementHeader,
								'action' => $syntrumLeaveAction,
								'leaveTypeCode'=>$leaveTypeCode,
								'uniqueIdDetails'=> array()
							);
							$this->_dbCommonFun->triggerSyntrumExecutionRequest($syntrumInputDetails,$sessionId);
						}
					}
				}
			}else{
				$asyncApiResult = 'Attendance summary and syntrum integration trigger failed';
			}

			if ($result['success'] && !$leaveData['Workflow_Status'] && !$eventId ) {
				$mailCommunicationData = array(
					'sessionId'         => $sessionId,
					'addedBy'           => $record['Added_By'],
					'employeeId'        => $record['Employee_Id'],
					'customFormName'    => $customFormName,
					'status'            => $status,
					'employeeName'      => $record['Employee_Name'],
					'formName'          => $formName,
					'actionName'        => $status === 'Cancel Applied' ? 'cancel request applied' : 'status updated',
					'approverEmpId'     => $record['Approver_Id'],
					'moduleName'        => 'Employees',
					'formUrl'           => '/employees/leaves',
					'leaveAction'       => $leaveAction,
				);
				$result = $this->_dbCommonFun->statusUpdateMailCommunication($mailCommunicationData);
			}
			$result['asyncApiResult']= $asyncApiResult;
			return $result;
		}
		else
		{
			return array('success' => false, 'msg' => 'You have already updated the status for '.$customFormName, 'type' => 'info');
		}
	}
	
	/**
	 *	Multiple Status approval
	*/
	public function multiStatusApproval ($leaveIdArr, $sessionId, $formName, $customFormName)
	{
		$statusUpdated = 0;
		$status        = 'Approved';
		$comment       = '';
		$addedByArr    = array();
		$employeeIdArr = array();
		
		foreach ($leaveIdArr as $row)
		{
			/**
			 *	Check Leave status is already updated or not
			*/
			$checkExist = $this->ckLeaveStatus ($row, $sessionId, $status);
			
			if ($checkExist == 0)
			{
				$record = $this->viewLeaveRequest ($row);
				
				$recEmployeeId  = $record['Employee_Id'];
				$recLeaveTypeId = $record['LeaveType_Id'];
				$recTotalDays   = $record['Total_Days'];
				$oldApprovalStatus = $record['Approval_Status'];

				if ($record['Approval_Status'] == 'Applied' && ($record['Approver_Id'] == $sessionId  || $leaveIdArr['Admin']=='admin'))
				{  
					$leaveData = array('Leave_Id'=> $row,'Status'=> $status,'Comment'=> $comment);
					$result = $this->approveLeaveRequest($record,$leaveData,$sessionId, $formName, $customFormName);
					
					if ($result['success'])
					{
						$addedBy      = $record['Added_By'];
						$employeeId   = $record['Employee_Id'];
						$employeeName = $record['Employee_Name'];
						
						if ($sessionId != $addedBy)
						{
							if (!(in_array($addedBy, $addedByArr)))
							{
								array_push($addedByArr, $addedBy);
								
								if ($addedBy == $employeeId)
									$msgDescA = "<p>Your ".$customFormName." request has been ".strtolower($status).".</p>";
								else
									$msgDescA = "<p>Your ".$customFormName." request for the employee ". $employeeName ." has been ".strtolower($status).".</p>";
								
								$result = $this->_dbCommonFun->communicateMail (array('employeeId'  => $addedBy,
																					  'ModuleName'  => 'Employees',
																					  'formName'    => $formName,
																					  'successMsg'  => $customFormName,
																					  'customFormName' => $customFormName,
																					  'formUrl'     => '/employees/leaves',
																					  'inboxTitle'  => $customFormName.' Notification',
																					  'mailContent' => $msgDescA,
																					  'action'      => 'status updated'));
                               
							}
						}
						
						if ($status == 'Approved' && $sessionId != $employeeId && $employeeId != $addedBy)
						{
							if (!(in_array($employeeId, $employeeIdArr)))
							{
								array_push($employeeIdArr, $employeeId);
								
								$msgDescA = "<p>Your ".$customFormName." request(s) has been ".strtolower($status)."</p>";
								
								$result = $this->_dbCommonFun->communicateMail (array('employeeId'  => $employeeId,
																					  'ModuleName'  => 'Employees',
																					  'formName'    => $formName,
																					  'successMsg'  => $customFormName,
																					  'customFormName' => $customFormName,
																					  'formUrl'     => '/employees/leaves',
																					  'inboxTitle'  => $customFormName.' Notification',
																					  'mailContent' => $msgDescA,
																					  'action'      => 'status updated'));
							}
                           
						}
					}
					
					if ($result['success'])
						++$statusUpdated;
				}
			}
		}
		
		if ($statusUpdated)
		{
			return array('success' => true, 'msg' => $customFormName.' Status(s) updated successfully', 'type' => 'success');
		}
		else
		{
			return array('success' => false, 'msg' => 'Unable to update status.Please contact the system admin', 'type' => 'info');
		}
	}


	public function approveLeaveRequest($record,$leaveData,$sessionId, $formName, $customFormName)
	{
		$recEmployeeId  	= $record['Employee_Id'];
		$recLeaveTypeId 	= $record['LeaveType_Id'];
		$recTotalDays   	= $record['Total_Days'];
		$oldApprovalStatus 	= $record['Approval_Status'];
		$availedLeaves      = $this->getLeaveClosureDetails($recEmployeeId,$recLeaveTypeId);
		//get the leave balance same way like leave history
		$empLeaveTypeBalance = $availedLeaves['Leave_Balance'];
		/** If the remaining leave balance is greater than or equal to the applying leave record total days */
		if(!empty($availedLeaves) && $empLeaveTypeBalance >= $recTotalDays)
		{
			$leaveBalance        	= $empLeaveTypeBalance - $recTotalDays;
			$leaveTypeDetails  		= $this->getLeaveTypeRow($recLeaveTypeId);
			$carryOverLeaveExist  	= $leaveTypeDetails['Carry_Over'];

			/** When the carry over is enabled we need to update carry over balance in deferred leaves table **/
			if(!empty($carryOverLeaveExist))
			{
				$leavesTaken = $availedLeaves['Leaves_Taken'];
				$currentYearEmployeeLeaveEligiblity = $availedLeaves['Eligible_Days'];
				$encashedDays = $this->getEncashedDays ($recLeaveTypeId, $recEmployeeId);
				//total leaves taken is sum of existing leave taken(availed)+currentleaveRequest+encashedDays
				$totalLeaveTaken = $leavesTaken+$recTotalDays+$encashedDays;
				$coLeave = $availedLeaves['Last_CO_Balance'];

				// when the total leave taken is greater than currentYearEmployeeLeaveEligiblity and 
				// carryover balance leave exist in that time only we need to update the carryover balance
				if( $totalLeaveTaken > $currentYearEmployeeLeaveEligiblity && $coLeave)
				{
					$existingLeavesTaken = $leavesTaken+$encashedDays;
					//existing leave taken is greater than currentYearEmployeeLeaveEligiblity we need to subtract the total days as is
					if($existingLeavesTaken > $currentYearEmployeeLeaveEligiblity)
					{
						$carryOverBalance 			= min(array($recTotalDays, $coLeave));
						$lastCoBalance 				= $coLeave-$carryOverBalance;
					}
					else
					{
						$currentYearLeaveBalance 	= $totalLeaveTaken-$currentYearEmployeeLeaveEligiblity;
						$carryOverBalance 			= min(array($currentYearLeaveBalance, $coLeave));
						$lastCoBalance 				= $coLeave-$carryOverBalance;
					}
					$this->updateCo ($recLeaveTypeId,$recEmployeeId,$lastCoBalance);
					
				}
			}
			$leavesTaken  = $availedLeaves['Leaves_Taken'] + $recTotalDays;
			$this->updateLeaveBalance ($availedLeaves['Eligible_Leave_Id'],$leavesTaken,$leaveBalance);
			$result = $this->statusReport($leaveData, $sessionId, $formName, $customFormName, $oldApprovalStatus);
		}
		else
		{
			$result = array('success' => false, 'msg' => "Unable to update status as the employee's available leave balance is less than applied leave", 'type' => 'warning');
		}
		return $result;
	}

	/** Function to delete the leave and worflow details */
	public function deleteLeaveRecord($leaveId, $formName){
		/**
		 *	Delete Leave record
		*/
		$workFlowInitiatedId=$this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empLeaves,'Workflow_Instance_Id')
		->where('Leave_Id = ?', (int)$leaveId));

		$historyWorkflowInstanceId=$this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->leaveWorkflowHistory,'Workflow_Instance_Id')
		->where('Leave_Id = ?', (int)$leaveId));

		$deleted = $this->_db->delete($this->_ehrTables->empLeaves, 'Leave_Id ='.(int)$leaveId);

		$deleteUnpaidLeaveOverrideDetails = $this->deleteUnpaidLeaveOverrideDetails('Leave',(int)$leaveId);
		// deleting the leave documents associated with the employee
		if($deleted)
		{
			if($workFlowInitiatedId)
			{
				array_push($historyWorkflowInstanceId,$workFlowInitiatedId);
				$whereWorkFlowInitiatedId['Workflow_Instance_Id IN (?)'] = $historyWorkflowInstanceId;
				$whereProcessInitiatedId['process_instance_id IN (?)'] = $historyWorkflowInstanceId;
				$this->_db->delete($this->_ehrTables->taUserTask,$whereProcessInitiatedId);
				$this->_db->delete($this->_ehrTables->taUserTaskHistory,$whereProcessInitiatedId);
				$this->_db->delete($this->_ehrTables->taProcessInstance,$whereProcessInitiatedId);
				$this->_db->delete($this->_ehrTables->taProcessInstanceHistory,$whereProcessInitiatedId);
				$this->_db->delete($this->_ehrTables->leaveWorkflowHistory,$whereWorkFlowInitiatedId);
			}
			$this->_db->delete($this->_ehrTables->empLeaveDocuments, 'Leave_Id ='.(int)$leaveId);
		}

		if ($deleted)
		{
			/**
			 *	Delete Comments
			*/
			$this->_dbComment->deleteComment($leaveId, $formName);
		}

		return $deleted;

	}

	public function lateAttendanceShortageExcludedLeaveId($leaveId)
	{
		// Handle empty input
		if (empty($leaveId)) {
			return array();
		}
		
		// If $leaveId is not an array, convert it to an array
		if (!is_array($leaveId)) {
			$leaveId = array($leaveId);
		}
		
		// Single query to get leaves that either:
		// 1. Have early checkout OR
		// 2. Have no late attendance AND no attendance shortage
		$result = $this->_db->fetchCol(
			$this->_db->select()
				->from(array('L' => $this->_ehrTables->empLeaves), array('L.Leave_Id'))
				->joinLeft(
					array('EAS' => $this->_ehrTables->employeeAttendanceSummary),
					'L.Leave_Id = EAS.Early_Checkout_Leave_Id',
					array('')
				)
				->where('L.Leave_Id IN (?)', $leaveId)
				->where('(EAS.Early_Checkout_Leave_Id IS NOT NULL OR (L.Late_Attendance = 0 AND L.Attendance_Shortage = 0))')
		);
		
		return $result ?: array();
	}
	
	/**
	 *	Delete leave details
	*/
    public function deleteLeave ($leaveId, $sessionId, $formName, $customFormName)
    {
		$deleted = 0;
		/**
		 *	get lock flag
		*/
        $leaveRow = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->empLeaves, array('*'))
										  ->where('Leave_Id = ?', $leaveId));

		

		if($leaveRow['Approval_Status'] == 'Approved' || $leaveRow['Approval_Status'] == 'Cancel Applied')
		{
			return array('success' => false, 'msg' => 'The leave request cannot be deleted because it has already been approved and a cancellation has been applied.', 'type' => 'warning');
		}
		else
		{
			$leaveRow['Deleted_On'] = date('Y-m-d H:i:s');
			$leaveRow['Deleted_By'] = $sessionId;
			$inserted = $this->_db->insert($this->_ehrTables->archiveLeaves, $leaveRow);
			$deleted = $this->deleteLeaveRecord($leaveId, $formName);

			/**
		 *	delete activity for common function
		 *		1)check lock is exist or not.
		 *			If lock is exist then show error message like employee is open record for update.
		 *		2)If No lockflag then process delete activity
		 *		3)Update delete activity in system log
		 *		4)return success/failure message
		*/
			$result = $this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
			'tableName'      => $this->_ehrTables->empLeaves,
			'lockFlag'       => $leaveRow['Lock_Flag'],
			'formName'       => $customFormName,
			'trackingColumn' => $leaveId,
			'sessionId'      => $sessionId));

			$result['leaveDetails']=$leaveRow;
			return $result;
		}
		
		
	}

	public function getLeaveDocuments($leaveId)
	{
		$getDocumentsForLeaveId =  $this->_db->fetchAll($this->_db->select()
										->from($this->_ehrTables->empLeaveDocuments, array('File_Name','File_Size'))
										->where('Leave_Id =?',$leaveId));

		return $getDocumentsForLeaveId;
	}	

		/** delete file in edit form **/
		public function deleteLeaveDocumentUploadFiles($documentId,$employeesDocumentUploadFileName)
		{
			$deleted = 0;
			
			if(!empty($documentId))
			{
				$where = $this->_db->quoteInto('Leave_Id = ? AND ', $documentId).
									 $this->_db->quoteInto('File_Name = ?', $employeesDocumentUploadFileName);
				
				$deleted = $this->_db->delete($this->_ehrTables->empLeaveDocuments, $where);
			}
			
			if($deleted)
			{
				return array('success'=> true, 'msg' => 'Document deleted successfully', 'comboPair' => '', 'type' => 'info');
			}
			else{
				return array('success'=> false, 'msg' => 'Unable to delete Document', 'type' => 'warning');	
			}
		}	
	
	/*************************************** Leave Type ************************************************/

	public function getLeaveTakenDetails($leaveTypeId,$employeeId=NULL)
	{
		$leaveTakenDetails    = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->empLeaves, array('*'))
																				->where('LeaveType_Id = ?',$leaveTypeId));

		return $leaveTakenDetails;
	}

	/**
	 *	check whether leave type used in defer leave or emp leave
	*/
    public function getLeaveTypeUsed($leaveTypeId,$customGroupLeaveEmpDetails=NULL) 
    {
		if(IS_NULL($customGroupLeaveEmpDetails)){		
			$eligibleLeaveDetails = $this->getEmployeeEligibleLeaveDetails(NULL,$leaveTypeId);
			if(!empty($eligibleLeaveDetails))
			{
				$isLeaveClosureCompleted = $this->isLeaveClosureCompleted($leaveTypeId);
				if(!empty($isLeaveClosureCompleted))	
				{
					// when the leave closure completed we should not allow the user to update the leave configuration
					// if we allow previous year policy and current policy configuration cannot be captured
					return false;
				}

				$carryOverLeaves       = array_sum(array_column($eligibleLeaveDetails,'Last_CO_Balance'));
				$leavesTaken 		   = array_sum(array_column($eligibleLeaveDetails,'Leaves_Taken'));
				if (!empty($carryOverLeaves) || !empty($leavesTaken))
				{
					return false;
				}
				else
				{
					$leaveTakenDetails = $this->getLeaveTakenDetails($leaveTypeId);
					if(!empty($leaveTakenDetails))	
					{
						foreach($eligibleLeaveDetails as $row)
						{
							foreach($leaveTakenDetails as $leaveTaken)
							{
								if($leaveTaken['Employee_Id']==$row['Employee_Id'] && $row['Leave_Closure_Start_Date']<= $leaveTaken['Start_Date'] 
								&& $row['Leave_Closure_End_Date'] >= $leaveTaken['Start_Date'])
								{
									return false;
								}
							}
						}
						return true;
					}
					else
					{
						return true; 
					}
				}
			}
			else
			{
				return true;
			}
		}else{
			$customGroupEmpLeaveDetails = array();

			/** If Employee Id and leave Type id is sent */
			if(isset($customGroupLeaveEmpDetails['Employee_Ids']) && isset($customGroupLeaveEmpDetails['LeaveType_Ids'])){
				$customEmpIds = $customGroupLeaveEmpDetails['Employee_Ids'];
			$customGroupLeaveTypeIds = $customGroupLeaveEmpDetails['LeaveType_Ids'];

			if(!empty($customEmpIds) && !empty($customGroupLeaveTypeIds)){
				/* Get the custom group leave details from leaves table */
				$availedLeavesDetails 		= $this->getEmployeeEligibleLeaveDetails($customEmpIds,$customGroupLeaveTypeIds);
				$deferredLeavesDetails 		= array_column($eligibleLeaveDetails,'Employee_Id');

				$customGroupEmpLeaveDetails = array('availedLeavesDetails' => $availedLeavesDetails,
											'leavesTakenDetails' => $availedLeavesDetails,
											'deferredLeavesDetails' => $deferredLeavesDetails);

				return $customGroupEmpLeaveDetails;
			}else{
				return $customGroupEmpLeaveDetails;
			}
		}else{
				return $customGroupEmpLeaveDetails;
			}
		}
    }

	/**
	 *	get total days for an leave type based DOJ and DOR
	*/
    public function eligibleDays($row,$lvTypeTotDays, $isProrata,$calculationType='',$externalIntegration=0)
    {
		if($isProrata == 1)
		{
			if(is_array($row))
			{
				$endDate = date('Y-m-d',strtotime($row['Leave_Closure_End_Date']));

				//if the Date of join is greater than the leave closure end date then eligible days is zero for a leave type for that employee
				if(strtotime($row['Date_Of_Join']) > strtotime($endDate)){
					return 0;
				}else{
					$startDate = date('Y-m-d',strtotime($row['Leave_Closure_Start_Date']));
					if(!empty($row['Date_Of_Join']) && strtotime($row['Date_Of_Join']) > strtotime($startDate))
					{
						$startDate = $row['Date_Of_Join'];
					}
					if(isset($row['Resignation_Date']) )
					{
						if(!empty($row['Resignation_Date']) && strtotime($row['Resignation_Date']) <= strtotime($endDate))
						{
							$endDate = $row['Resignation_Date'];
						}
					}

					$strStart=date_create($startDate);
					$strEnd=date_create($endDate);
					$numOfDays=date_diff($strStart,$strEnd);
					$numOfDays = $numOfDays->format("%a")+1;
		
					$semiValue = $this->getSemiValue($row['LeaveType_Id']);

					$eligDays 	= ($lvTypeTotDays / 365) * $numOfDays;
					if($externalIntegration==0){
						$eligDays 	= $this->_dbCommonFun->getRoundOffValue('Leave Eligiblity',$eligDays,'',$semiValue);
					}else{
						$eligDays = round($eligDays, 2);
					}

					return $eligDays;
				}
			}else{
				return 0;
			}
		}
		elseif(empty($isProrata)&& $calculationType==='paidLeaveEncashment' || $calculationType==='paidLeaveDeduction')
		{
			if(is_array($row))
			{
				$endDate = date('Y-m-d',strtotime($row['Leave_Closure_End_Date']));
				$leaveClosureEndDate = date_create($endDate);

				//if the Date of join is greater than the leave closure end date then eligible days is zero for a leave type for that employee
				if(strtotime($row['Date_Of_Join']) > strtotime($endDate))
				{
					return 0;
				}
				else
				{
					$startDate = date('Y-m-d',strtotime($row['Leave_Closure_Start_Date']));

					if(!empty($row['Date_Of_Join']) && strtotime($row['Date_Of_Join']) > strtotime($startDate))
					{
						$startDate = $row['Date_Of_Join'];
					}

					if(isset($row['Resignation_Date']) )
					{
						if(!empty($row['Resignation_Date']) && strtotime($row['Resignation_Date']) <= strtotime($endDate))
						{
							$endDate = $row['Resignation_Date'];
						}
					}

					$strStart=date_create($startDate);
					$strEnd=date_create($endDate);
					$numOfDays=date_diff($strStart,$strEnd);
					$numOfDays = $numOfDays->format("%a")+1;
					$totalDays = date_diff($strStart,$leaveClosureEndDate);
					$totalDays = $totalDays->format("%a")+1;

					$semiValue = $this->getSemiValue($row['LeaveType_Id']);

					$eligDays 	= ($lvTypeTotDays /$totalDays) * $numOfDays;
					$eligDays 	= $this->_dbCommonFun->getRoundOffValue('Leave Eligiblity',$eligDays,'',$semiValue);
					return $eligDays;
				}
			}
			else
			{
				return 0;
			}
		}
		else
		{
			return $lvTypeTotDays;
		}
    }
	
	/**
	 *	List leave type details
	*/
    public function listLeaveType ($page, $rows, $sortField, $sortOrder, $searchAll, $searchArr)
    {
		$leaveName  = $searchArr['Leave_Name'];
		$leaveType  = $searchArr['Leave_Type'];
		$carryOver  = $searchArr['Carry_Over'];
		$encashment = $searchArr['Leave_Encashment'];
		$leaveTypeStatus = $searchArr['LeaveTypeStatus'];
		
        /**
		 *	Sorting columns based on display column order in grid
		*/
		switch ($sortField)
		{
			case 1: $sortField = 'L.Leave_Name'; break;
			case 2: $sortField = 'L.Leave_Type'; break;
			case 3: $sortField = 'L.Total_Days'; break;
			case 4: $sortField = 'L.Encashment'; break;
			case 5: $sortField = 'L.Carry_Over'; break;
			case 6: $sortField = 'L.Leave_Status'; break;
		}

		/**
		 *	Query to fetch data from various tables
		*/
        $qryLeaveType = $this->_db->select()
								->from(array('L'=>$this->_ehrTables->leavetype),
									   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS L.LeaveType_Id as count'), 'L.LeaveType_Id',
											 'L.Leave_Name', 'L.Leave_Type', 'L.Frequency', 'L.Maximum_Limit', 'L.Carry_Over','L.Enable_Proration','L.Enable_Personal_Choice',
											 'L.Encashment', 'L.Encashment_Limit', 'L.Leave_Encashment_For_FF', 'L.Leave_Deduction_For_FF',
											 'L.Gender', 'L.Leave_Activation_Days','L.Show_Statistics_In_Dashboard',
											 'L.Coverage', 'Leave_Accrual',  'Leave_Approval_Cutoff', 'Advance_Notification','L.Accrual_Restriction_Period','L.Accrual_Until',
											 'L.CarryOver_Limit', 'L.Description', 'L.Total_Days','L.Leave_Status','L.Prorate_Leave_Balance_From','L.Prorate_After',
                                             'L.Minimum_Total_Days','L.Period','L.Eligible_Days_Based_On_Period','L.Proration_Threshold_Date','L.Day_Of_The_Month',
                                             'L.Applicable_During_Notice_Period','L.Applicable_During_Probation','L.Accumulate_Eligible_Days',
											 'L.Carry_Over_Accumulation_Limit','L.Auto_Encashment','L.Document_Upload','L.Max_Days_For_Document_Upload',
											 'L.Custom_Group_Id','L.Accumulate_From','L.Accumulate_After','L.Accrual','L.Restrict_Employee_To_Apply','L.Workflow_Id',
											 new Zend_Db_Expr("DATE_FORMAT(L.Added_On,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
											 new Zend_Db_Expr("DATE_FORMAT(L.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On"),'L.Leave_Enforcement_Configuration',
											 'DT_RowClass' => new Zend_Db_Expr('"leaveType"'),
											 'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', L.LeaveType_Id)"),
											 'Leave_Calculation_Days'=>new Zend_Db_Expr('CASE WHEN L.Leave_Calculation_Days = "1" THEN "All Days Of Month"
																		   ELSE "Business Working Days"
																		   END'),
                                              'Leave_Enforcement'=>new Zend_Db_Expr('CASE WHEN L.Leave_Enforcement_Configuration = "1" THEN "Normal Leave"
																		   WHEN L.Leave_Enforcement_Configuration = "2" THEN "Service Based Leave I"
                                                                           WHEN L.Leave_Enforcement_Configuration = "3" THEN "Quarter Wise Leave"
																		   WHEN L.Leave_Enforcement_Configuration = "4" THEN "Maternity Leave Slabs"
																		   WHEN L.Leave_Enforcement_Configuration = "5" THEN "Service Based Leave II"
																		   WHEN L.Leave_Enforcement_Configuration = "6" THEN "Roster Leave"
																		   END'),
                                             'Show_In_Payslip'=>new Zend_Db_Expr('CASE
																					WHEN L.Show_In_Payslip = "1" THEN "Yes"
																					ELSE "No"
																				END'),
											 'D_Coverage'=>new Zend_Db_Expr('CASE WHEN L.Coverage = "ORG" THEN "Organization"
											 								WHEN L.Coverage = "GRA" THEN "Grade"
																		   	ELSE "Custom Group"
																		   	END'),
											'L.Enable_Leave_Exception','L.Minimum_Limit','L.Half_Paid_Leave_Deduction','L.Leave_Unit','L.Replenishment_Limit',
											'L.Leave_Closure_Based_On','L.Leave_Closure_Month','L.Leave_Closure_Year','L.Leave_Closure_End_Year','L.Parent_Leave','L.Parent_LeaveTypeId',
											new Zend_Db_Expr("DATE_FORMAT(L.Leave_Closure_Start_Date,'".$this->_orgDF['sql']."') as Leave_Closure_Start_Date"),
											new Zend_Db_Expr("DATE_FORMAT(L.Leave_Closure_End_Date,'".$this->_orgDF['sql']."') as Leave_Closure_End_Date")
								))

								->joinLeft(array('PL'=>$this->_ehrTables->leavetype),'PL.LeaveType_Id=L.Parent_LeaveTypeId',array('PL.Leave_Name as Parent_Leave_Name'))

								->joinLeft(array('CEG'=>$this->_ehrTables->customEmployeeGroup),'CEG.Group_Id=L.Custom_Group_Id',
											array('CEG.Group_Name as Custom_Group_Name'))

								->joinLeft(array('M'=>$this->_ehrTables->month),"M.Month_Id = L.Leave_Closure_Month", array('M.Month_Name as LeaveClosureMonth'))			

								->joinLeft(array('WF'=>$this->_ehrTables->workflows),'WF.Workflow_Id=L.Workflow_Id',
											array('WF.Workflow_Name'))

								->joinLeft(array('AE'=>$this->_ehrTables->empPersonal),'AE.Employee_Id=L.Added_By',
										   array(new Zend_Db_Expr("CONCAT(AE.Emp_First_Name, ' ', AE.Emp_Last_Name) as Added_By_Name")))
								
								->joinLeft(array('UE'=>$this->_ehrTables->empPersonal),'UE.Employee_Id=L.Updated_By',
										   array(new Zend_Db_Expr("CONCAT(UE.Emp_First_Name, ' ', UE.Emp_Last_Name) as Updated_By_Name")))

								->joinLeft(array('EL'=>$this->_ehrTables->empEligbleLeave),"EL.LeaveType_Id = L.LeaveType_Id AND L.Leave_Closure_Based_On='Selected Month'",array(''))
								
								->group('L.LeaveType_Id')
								->order("$sortField $sortOrder")
								->limit($rows, $page);
		
        /**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			$conditions = $this->_db->quoteInto('L.Leave_Name Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or L.Leave_Type Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or L.Total_Days Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or L.Carry_Over Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or L.Encashment Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or L.Leave_Status Like ?', "%$searchAll%");
			
			$qryLeaveType->where($conditions);
		}
		
		if (! empty($leaveName))
            $qryLeaveType->where('L.Leave_Name Like?', "%$leaveName%");
        
        if (! empty($leaveType) && preg_match('/^[a-zA-Z]/', $leaveType))
            $qryLeaveType->where('L.Leave_Type = ?', $leaveType);
        
        if (! empty($carryOver) && preg_match('/^[a-zA-Z]/', $carryOver))
            $qryLeaveType->where('L.Carry_Over = ?', $carryOver);
        
        if (! empty($encashment) && preg_match('/^[a-zA-Z]/', $encashment))
            $qryLeaveType->where('L.Encashment = ?', $encashment);
		
		if (! empty($leaveTypeStatus) && preg_match('/^[a-zA-Z]/', $leaveTypeStatus))
            $qryLeaveType->where('L.Leave_Status Like?', "$leaveTypeStatus%");
    		
		/**
		 * SQL queries
		 * Get data to display
		*/
		$leaveType = $this->_db->fetchAll($qryLeaveType);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		foreach ($leaveType as $key => $row)
		{
			$viewGrade = array();
			$dbGrade = new Employees_Model_DbTable_Grade();
			$gradePair  = $dbGrade->getGrade();
			
			$gradeLevelLeavesArray = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->leavetypegrade,array('Grade_Id'))
								  ->where('LeaveType_Id =?', $row['LeaveType_Id']));
			
			foreach ( $gradeLevelLeavesArray as $gradeLevelLeaves)
			{				
				array_push($viewGrade, $gradePair[$gradeLevelLeaves]);
			}
			
			/* Get the custom group employees */
			$customGroupEmployees = '';
			if($row['Coverage']=='CUSTOMGROUP'){
				$customGroupEmployeeIds = $this->getLeaveTypeCustomGroupEmployees($row['Custom_Group_Id']);
				if(!empty($customGroupEmployeeIds)){
					$customGroupEmployees = $this->_dbCommonFun->getEmpName($customGroupEmployeeIds);
				}
			}

			$leaveType[$key]['Edit_Grade'] = $gradeLevelLeavesArray;
			$leaveType[$key]['View_Grade'] = implode(",", $viewGrade);			
			$leaveType[$key]['Custom_Group_Employees'] = $customGroupEmployees;
		}
		
		/* Total data set length */
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->leavetype, new Zend_Db_Expr('COUNT(LeaveType_Id)')));
		
		$orgLeavePeriodEligibleDays = $this->getOrgLeavePeriodEligibleDays();
		foreach ($leaveType as $key => $row)
		{
			$leavePeriodEligibleDays = $orgLeavePeriodEligibleDays[$row['LeaveType_Id']]?? null;

			$leaveType[$key]['Used_Leave_Type'] = $this->getLeaveTypeUsed ($row['LeaveType_Id'],NULL);

			$leaveType[$key]['Leave_Period_Eligible_Days'] = $leavePeriodEligibleDays;
			if(!empty($leavePeriodEligibleDays))
			{
				foreach ($leavePeriodEligibleDays as $leave) {
					$leaveType[$key]['Month_' . $leave['Month_Id']] = $leave['Eligible_Days'];
				}
			}
		}
	
		/**
		 * Output array with Json encode
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $leaveType);
    }

	public function getOrgLeavePeriodEligibleDays()
	{
		$getOrgLeavePeriodEligibleDays = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->orgLeavePeriodEligibleDays,array('*')));

		$leavePeriodEligibleDays      = $this->_dbCommonFun->organizeDataByEmployeeIdAndDate($getOrgLeavePeriodEligibleDays,'LeaveType_Id');

		return $leavePeriodEligibleDays;
	}
	
	public function isValidProrataValue($leaveTypeId,$newProrataValue)
	{
		if(!empty($leaveTypeId)) // if leaveTypeId is not empty then it is edit form
		{
			$isLeaveTypeUsed = $this->getLeaveTypeUsed($leaveTypeId); // return false if leave type is used

			if($isLeaveTypeUsed) // if leave type is not used 
			{
				return true;
			}
			else
			{
				$oldProrataValue = $this->_db->fetchOne($this->_db->select()
											->from($this->_ehrTables->leavetype,array('Enable_Proration'))
											->where('LeaveType_Id = ?',$leaveTypeId));
				
				if($oldProrataValue == $newProrataValue)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
		}
		else
		{
			return true;
		}
	}
	/**
	 *	Add/Update Leave Type
	*/
    public function updateLeaveType ($leaveTypeData, $leaveTypeId, $grade, $sessionId,$customFormName, $formId,$orgLeavePeriodEligibleDays)
    {
		$eligDaysDaysResult = 1; // Variable to check whether the eligibility days is updated or not
		$leaveClosureStartDate = $leaveClosureEndDate = $COYear = '';
		$updated = 0;
		
		/**
		 * validate whether the leave type is used in the emp_leaves form for the current leave closure year or in the leave balance import for the 
		 * current leave closure year or in the deferred leaves.If the response from the function is false then the leave type is used. 
		 * Or if the response is true then the leave type is not used.
		 */
		$isLeaveTypeNotUsed = $this->getLeaveTypeUsed($leaveTypeId);
		/**
		 *	Check Leave Name already exist or not
		*/
		$qryCheckExist = $this->_db->select()
										->from($this->_ehrTables->leavetype, new Zend_Db_Expr('Count(LeaveType_Id)'))
										->where('Leave_Name = ?', $leaveTypeData['Leave_Name']);

		if (!empty($leaveTypeId))
        {
            $qryCheckExist->where('LeaveType_Id != ?', $leaveTypeId);
        }
        
		$checkExists = $this->_db->fetchOne ($qryCheckExist);
		
		/**
		 *	If Leave Name didn't exist in leave type table
		*/
		if ($checkExists == 0)
		{
			/**
			 *	Check Leave Type details already exist or not
			*/
			$qryLeaveTypeExist = $this->_db->select()
												->from($this->_ehrTables->leavetype, new Zend_Db_Expr('Count(LeaveType_Id)'))
												->where('Leave_Name = ?', $leaveTypeData['Leave_Name'])
												->where('Leave_Type = ?', $leaveTypeData['Leave_Type'])
												->where('Total_Days = ?', $leaveTypeData['Total_Days'])
												->where('Maximum_Limit = ?', $leaveTypeData['Maximum_Limit'])
												->where('Carry_Over = ?', $leaveTypeData['Carry_Over'])
												->where('Frequency = ?', $leaveTypeData['Frequency'])
												->where('Encashment = ?', $leaveTypeData['Encashment'])
												->where('Custom_Group_Id = ?', $leaveTypeData['Custom_Group_Id']);
			
			if ($leaveTypeData['Encashment'] == "Yes")
			{
				$qryLeaveTypeExist->where('Encashment_Limit = ?',$leaveTypeData['Encashment_Limit']);
			}
			
			if ($leaveTypeData['Carry_Over'] == "Yes")
			{
				$qryLeaveTypeExist->where('CarryOver_Limit = ?',$leaveTypeData['CarryOver_Limit']);
			}
			
			if (!empty($leaveTypeId))
			{
				$qryLeaveTypeExist->where('LeaveType_Id != ?', $leaveTypeId);
			}
			
			$leaveTypeExists = $this->_db->fetchOne ($qryLeaveTypeExist);
			
			/**
			 *	If Leave Type Details didn't exist in leave type table
			*/
			if ($leaveTypeExists == 0)
			{
				if($leaveTypeData['Leave_Closure_Based_On']=='Selected Month') {
					$leaveClosureDates = $this->getSelectedMonthLeaveClosureDates($leaveTypeData['Leave_Closure_Month'],$leaveTypeData['Leave_Closure_End_Year'],'add-leave-type');
				} else {
					$leaveClosureDates['finstart'] = NULL;
					$leaveClosureDates['finend'] = NULL;
				}
				$leaveTypeData['Leave_Closure_Start_Date'] = $leaveClosureDates['finstart'];
				$leaveTypeData['Leave_Closure_End_Date'] = $leaveClosureDates['finend'];
				/**
				 *	If Leave Type Id is equal to 0 than perform add
				*	If Leave Type Id is greater than 0 than perform update
				*/
				if ($leaveTypeId == 0)
				{
					$action = 'Add';
					$leaveTypeData['Added_On'] = date('Y-m-d H:i:s');
					$leaveTypeData['Added_By'] = $sessionId;
					
					$updated = $this->_db->insert($this->_ehrTables->leavetype, $leaveTypeData);
					
					if ($updated) 
					{
						$leaveTypeId = $this->_db->lastInsertId();
						if($leaveTypeData['Coverage'] == 'GRA'){
							if(!empty($grade)){
								$arrMulti = array();
								foreach ($grade as $row)
								{	
									$arrMulti[] = array('LeaveType_Id'  => $leaveTypeId,
														'Grade_Id'   => $row);
								}
								
								if(!empty($arrMulti)){
									$updated = $this->_ehrTables->insertMultiple($this->_ehrTables->leavetypegrade, $arrMulti);
								}
							}
						}
						else{
							$this->_db->delete($this->_ehrTables->leavetypegrade,'LeaveType_Id='.$leaveTypeId);
						}
						
						$leaveTypeDurationResult = array();
						//Full Day Duration
						$leaveTypeFullDayDurationList = array(
							'LeaveType_Id' => $leaveTypeId,
							'Leave_Duration' => 'Full Day',
							'Duration_Value' => '1'
						);
						array_push($leaveTypeDurationResult,$leaveTypeFullDayDurationList);
						//Half Day Duration
						$leaveTypeHalfDayDurationList = array(
							'LeaveType_Id' => $leaveTypeId,
							'Leave_Duration' => 'Half Day',
							'Duration_Value' => '0.5'
						);
						array_push($leaveTypeDurationResult,$leaveTypeHalfDayDurationList);
						//Insert the leave type duration
						$updated = $this->_ehrTables->insertMultiple($this->_ehrTables->leaveTypesDuration, $leaveTypeDurationResult);

						if($leaveTypeData['Leave_Enforcement_Configuration']==1 || $leaveTypeData['Leave_Enforcement_Configuration']==2)
						{
							$leaveTypeData['LeaveType_Id']  = $leaveTypeId;
							if($leaveTypeData['Coverage']=='CUSTOMGROUP')
							{
								$this->_dbCommonFun->mapCustomGroup($leaveTypeId, $formId, $leaveTypeData['Custom_Group_Id'],'insert');
							}
						
							if($leaveTypeData['Leave_Enforcement_Configuration']==1)
							{
								$updated  = $this->empDOJUpdateEligibleDays(NULL,'add-leave-type',NULL,$leaveTypeId,0,'Yes');
							}
						}
						else
						{
							$updated=1;
						}
					}
				}
				else
				{
					/* Get the leave type old coverage */
					$oldLeaveTypeDetails = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->leavetype, array('*'))
												->where('LeaveType_Id = ?', $leaveTypeId));

					$leaveTypeOldCoverage = $oldLeaveTypeDetails['Coverage'];
					
					if($leaveTypeData['Leave_Status']=='Inactive' && $oldLeaveTypeDetails['Leave_Status']=='Active')
					{
						$attendanceSettings = $this->_db->fetchOne($this->_db->select()->from(array('AS'=>$this->_ehrTables->lateAttendanceLeaveTypes), new Zend_Db_Expr('Count(LeaveType_Id)'))
											->where('AS.LeaveType_Id = ?',$leaveTypeId));
					}
					else
					{
						$attendanceSettings = '';
					}

					if(!empty($attendanceSettings))
					{
						return array('success' => false, 'msg' => 'Unable to update the leave type. this leave should be unassociated from the late attendance/attendance shortage/early checkout configuration', 'type' => 'info');
					}
					else
					{
						$action    = 'Edit';
						$totalDays = $leaveTypeData['Total_Days'];
						
						// if the leave type is used in the emp_leaves form or leave balance import or deferred leaves.
						if (!$isLeaveTypeNotUsed)
						{
							//remove this fields as this fields should not be updated
							unset ($leaveTypeData['Leave_Type']);
							unset ($leaveTypeData['Total_Days']);
							unset ($leaveTypeData['Carry_Over']);
							unset ($leaveTypeData['CarryOver_Limit']);
							unset ($leaveTypeData['Carry_Over_Accumulation_Limit']);
							unset ($leaveTypeData['Encashment']);
							unset ($leaveTypeData['Encashment_Limit']);
							unset ($leaveTypeData['Gender']);
							unset ($leaveTypeData['Coverage']);
							unset ($leaveTypeData['Custom_Group_Id']);
							unset ($leaveTypeData['Leave_Calculation_Days']);
							unset ($leaveTypeData['Leave_Enforcement_Configuration']);
							unset ($leaveTypeData['Applicable_During_Probation']);
							unset ($leaveTypeData['Enable_Proration']);
							unset ($leaveTypeData['Leave_Closure_Start_Date']);
							unset ($leaveTypeData['Leave_Closure_End_Date']);
						}
						
						$leaveTypeData['Updated_On'] = date('Y-m-d H:i:s');
						$leaveTypeData['Updated_By'] = $sessionId;
						$leaveTypeData['Lock_Flag']  = 0;

						$updated = $this->_db->update($this->_ehrTables->leavetype, $leaveTypeData, 'LeaveType_Id='.(int)$leaveTypeId);
					
						// If the leave type updated and if the leave type is not used
						if ($updated && $isLeaveTypeNotUsed)
						{ 
							$leaveTypeData = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->leavetype, array('*'))
												->where('LeaveType_Id = ?', $leaveTypeId));
							if((!empty($leaveTypeData['Prorate_After']) || !empty($oldLeaveTypeDetails['Prorate_After'])))
							{
								if($oldLeaveTypeDetails['Prorate_After']!=$leaveTypeData['Prorate_After'])
								{
									$prorateAfterUpdated = 1;
								}
								else
								{
									$prorateAfterUpdated = 0;
								}
							}
							else
							{
								$prorateAfterUpdated = 0;
							}

							if((!empty($leaveTypeData['Day_Of_The_Month']) || !empty($oldLeaveTypeDetails['Day_Of_The_Month'])))
							{
								if($oldLeaveTypeDetails['Day_Of_The_Month']!=$leaveTypeData['Day_Of_The_Month'])
								{
									$dayOfTheMonth = 1;
								}
								else
								{
									$dayOfTheMonth = 0;
								}
							}
							else
							{
								$dayOfTheMonth = 0;
							}

							if($oldLeaveTypeDetails['Total_Days']!=$leaveTypeData['Total_Days'] ||
								$oldLeaveTypeDetails['Gender']!=$leaveTypeData['Gender'] ||
								$oldLeaveTypeDetails['Coverage']!=$leaveTypeData['Coverage'] ||
								$oldLeaveTypeDetails['Custom_Group_Id']!=$leaveTypeData['Custom_Group_Id'] ||
								$oldLeaveTypeDetails['Enable_Proration']!=$leaveTypeData['Enable_Proration'] ||
								(!empty($prorateAfterUpdated)) || (!empty($dayOfTheMonth)) || 
								$oldLeaveTypeDetails['Prorate_Leave_Balance_From']!=$leaveTypeData['Prorate_Leave_Balance_From'] ||
								$oldLeaveTypeDetails['Proration_Threshold_Date']!=$leaveTypeData['Proration_Threshold_Date'] ||
								$oldLeaveTypeDetails['Leave_Enforcement_Configuration']!=$leaveTypeData['Leave_Enforcement_Configuration'] ||
								$oldLeaveTypeDetails['Leave_Closure_Based_On']!=$leaveTypeData['Leave_Closure_Based_On'] ||
								$oldLeaveTypeDetails['Leave_Closure_Month']!=$leaveTypeData['Leave_Closure_Month'] ||
								$oldLeaveTypeDetails['Leave_Closure_End_Year']!=$leaveTypeData['Leave_Closure_End_Year'])
							{
								if($leaveTypeData['Coverage'] == 'GRA')
								{
									if(!empty($grade))
									{		
										$deleted=$this->_db->delete($this->_ehrTables->leavetypegrade,'LeaveType_Id='.$leaveTypeId);							
										
										/* If leave type grade is deleted from the table or if the old coverage is Org */
										if($deleted || $leaveTypeOldCoverage == "ORG")
										{
											$arrMulti = array();
											foreach ($grade as $row)
											{
												$arrMulti[] = array('LeaveType_Id'  => $leaveTypeId,
																	'Grade_Id'   => $row);
											}
											
											if(!empty($arrMulti)){
												$updated = $this->_ehrTables->insertMultiple($this->_ehrTables->leavetypegrade, $arrMulti);
											}
										}
									}
								}
								else
								{
									$this->_db->delete($this->_ehrTables->leavetypegrade,'LeaveType_Id='.$leaveTypeId);
								}

								if($leaveTypeData['Leave_Enforcement_Configuration']==1 || $leaveTypeData['Leave_Enforcement_Configuration']==2)
								{
									// If the leave type coverage is changed from CUSTOMGROUP to ORG/GRADE then delete the connection between leave type and customgroup 
									if( $leaveTypeOldCoverage == 'CUSTOMGROUP' && $leaveTypeData['Coverage'] != 'CUSTOMGROUP' )
									{
										$this->_dbCommonFun->mapCustomGroup($leaveTypeId, $formId, '','delete');
									}
									$leaveTypeData['LeaveType_Id'] 	= $leaveTypeId;
									if($leaveTypeData['Coverage']=='CUSTOMGROUP'){
										if( $leaveTypeOldCoverage != 'CUSTOMGROUP' && $leaveTypeData['Coverage'] == 'CUSTOMGROUP' )
										{
											$this->_dbCommonFun->mapCustomGroup($leaveTypeId, $formId, $leaveTypeData['Custom_Group_Id'],'insert');
										}
										else
										{
											$this->_dbCommonFun->mapCustomGroup($leaveTypeId, $formId, $leaveTypeData['Custom_Group_Id'],'update');
										}
									}
								}
								
								if($leaveTypeData['Leave_Enforcement_Configuration']!=6)
								{
									$updated  = $this->empDOJUpdateEligibleDays(NULL,'add-leave-type',NULL,$leaveTypeId,0,'Yes');
									$updated  = 1;
								}
							}	
							else
							{
								$updated  = 1;
							}
						}
						else
						{
							$updated=1;
						}
					}
				}

				if(!empty($orgLeavePeriodEligibleDays))
				{
					$this->_db->delete($this->_ehrTables->orgLeavePeriodEligibleDays,'LeaveType_Id='.$leaveTypeId);
					foreach ($orgLeavePeriodEligibleDays as $key => $row)
					{
						$orgLeavePeriodEligibleDays[$key]['LeaveType_Id'] = $leaveTypeId;
					}
					$updated = $this->_ehrTables->insertMultiple($this->_ehrTables->orgLeavePeriodEligibleDays, $orgLeavePeriodEligibleDays);
				}

				/** Update the DataSetup status **/
				if($updated)
				{
					$updated = $this->_dbCommonFun->updateDataSetupDashboard('Completed','32');                    
				}
								
				
				/**
				 *	this function will handle
				*		update system log function
				*		clear submit lock fucntion
				*		return success/failure array
				*/
				$trackSysLog =  $this->_dbCommonFun->updateResult (array('updated'        => $updated,
										'action'         => $action,
										'trackingColumn' => $leaveTypeData['Leave_Name'],
										'formName'       => $customFormName,
										'sessionId'      => $sessionId,
										'comboPair'      => $this->getLeaveType(),
										'tableName'      => $this->_ehrTables->leavetype));
										
				/* If the leave balance is not updated for the custom group employees */
				if(empty($eligDaysDaysResult)){
					$trackSysLog['msg'] .= '. Leave balance is not updated for the custom group employees. Please contact system administrator';
				}
				
				return $trackSysLog;
			}
			else
			{
				return array('success' => false, 'msg' => 'Leave Type details already exists', 'type' => 'info');
			}
		}
        else
		{
			return array('success' => false, 'msg' => 'Leave Name already exists', 'type' => 'info');
		}
    }
	
	//to delete leave type record
    public function deleteLeaveType ($leaveTypeId, $sessionId,$customFormName, $formId)
    {
		/**
		 *	check leave Type is exist in employee leave's table
		*/
        $employeeLeaves = $this->_db->fetchOne($this->_db->select()
										  ->from(array('leave'=>$this->_ehrTables->empLeaves), new Zend_Db_Expr('Count(LeaveType_Id)'))
										  ->where('leave.LeaveType_Id = ?',$leaveTypeId));


        /*check whether encashment is done for that leave type or not*/
		$encashedLeave = $this->_db->fetchOne($this->_db->select()
										->from(array('leave'=>$this->_ehrTables->encashedLeave), new Zend_Db_Expr('Count(LeaveType_Id)'))
										->where('leave.LeaveType_Id = ?',$leaveTypeId));
		
	    /**
		 *	Check Leave Type is exist in leave freeze table
		*/
		$leaveFreeze = $this->_db->fetchOne($this->_db->select()
											   ->from(array('F'=>$this->_ehrTables->leaveFreeze), new Zend_Db_Expr('Count(LeaveType_Id)'))
											   ->where('F.LeaveType_Id = ?',$leaveTypeId));
		/**
		 *	Check Leave Type is exist in attendance settings table
		*/
		$attendanceSettings = $this->_db->fetchOne($this->_db->select()
											   ->from(array('AS'=>$this->_ehrTables->lateAttendanceLeaveTypes), new Zend_Db_Expr('Count(LeaveType_Id)'))
											   ->where('AS.LeaveType_Id = ?',$leaveTypeId));
		
		/**
		 *	If Leave Type id didn't mapped with any other tablt then we allow to delete
		*/
        if ($employeeLeaves == 0 && $leaveFreeze == 0 && $attendanceSettings == 0 && $encashedLeave == 0)
        {
			/**
			 *	Get Leave Name & Lock flag from leave type id
			*/
            $leaveTypeRow = $this->getLeaveTypeRow($leaveTypeId);
			/**
			 *  Check the leave enforcement configuration leave types
			 */
			
			$checkLeaveEnforcement=$this->deleteEligibleLeaveType($leaveTypeRow);
			if($checkLeaveEnforcement==1)
			{
					/**
				 *	If Lock flag is empty then wonly we allow to delete
				*/
				if ($leaveTypeRow['Lock_Flag'] == 0)
				{
					/**
					 *	Delete Leave Type
					*/
					$deleted = $this->_db->delete($this->_ehrTables->leavetype,'LeaveType_Id='.(int)$leaveTypeId);

					if ($deleted)
					{
						//once leave type is deleted then middle join employee total days and availed leaves 
					
						$this->_db->delete($this->_ehrTables->empEligbleLeave,'LeaveType_Id='.(int)$leaveTypeId);
						
						$this->_db->delete($this->_ehrTables->leavetypegrade,'LeaveType_Id='.(int)$leaveTypeId);

						$this->_db->delete($this->_ehrTables->leaveImport,'Leave_Type_Id='.(int)$leaveTypeId);

						$this->_db->delete($this->_ehrTables->leavesImport,'LeaveType_Id='.(int)$leaveTypeId);

						$this->_db->delete($this->_ehrTables->leavesImport,'LeaveType_Id='.(int)$leaveTypeId);

						$this->_db->delete($this->_ehrTables->leaveTypesDuration,'LeaveType_Id='.(int)$leaveTypeId);

						$this->_dbCommonFun->mapCustomGroup($leaveTypeId, $formId, '','delete');
					}
				}

			/**
			 *	delete activity for common function
			 *		1)check lock is exist or not.
			 *			If lock is exist then show error message like employee is open record for update.
			 *		2)If No lockflag then process delete activity
			 *		3)Update delete activity in system log
			 *		4)return success/failure message
			 */
				return $this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
																'tableName'      => $this->_ehrTables->leavetype,
																'lockFlag'       => $leaveTypeRow['Lock_Flag'],
																'formName'       => $customFormName,
																'trackingColumn' => $leaveTypeRow['Leave_Name'],
																'sessionId'      => $sessionId,
																'comboPair'      => $this->getLeaveType()));
			}
			else
			{
				return array('success'=>false, 'msg'=>$checkLeaveEnforcement, 'type'=>'info');
			}												
        }
        else
        {
			return array('success'=>false, 'msg'=>'Unable to delete '.$customFormName.'. Please, contact system admin', 'type'=>'info');
        }
	}
	

	public function deleteEligibleLeaveType ($leaveTypeRow)
	{
		$serviceLeaveType= $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empServiceLeave,new Zend_Db_Expr('Count(LeaveType_Id)'))
						   ->where('LeaveType_Id = ?', $leaveTypeRow['LeaveType_Id']));
	
		 $quarterLeaveType = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->leaveQuarter, new Zend_Db_Expr('Count(LeaveType_Id)'))
						    ->where('LeaveType_Id = ?', $leaveTypeRow['LeaveType_Id']));
		
		$maternityLeaveType = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->maternitySlab,new Zend_Db_Expr('Count(LeaveType_Id)'))
							->where('LeaveType_Id = ?', $leaveTypeRow['LeaveType_Id']));
		
		$experienceLeaveType= $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empExperienceLeave,new Zend_Db_Expr('Count(LeaveType_Id)'))
							->where('LeaveType_Id = ?', $leaveTypeRow['LeaveType_Id']));
							
		if($serviceLeaveType > 0 && $leaveTypeRow['Leave_Enforcement_Configuration']==2)
		{
			return 'Unable to delete the experience leave type configuration with leave enforcement, So delete this leave type in leave enforcement configuration';
		}
		elseif($quarterLeaveType  > 0 && $leaveTypeRow['Leave_Enforcement_Configuration']==3)
	    {
			return 'Unable to delete the quarter leave type configuration with leave enforcement, So delete this leave type in leave enforcement configuration';
		}
		elseif($maternityLeaveType  > 0 && $leaveTypeRow['Leave_Enforcement_Configuration']==4)
		{
		   return 'Unable to delete the maternity leave type configuration with leave enforcement, So delete this leave type in leave enforcement configuration';
		}
		elseif($experienceLeaveType > 0 && $leaveTypeRow['Leave_Enforcement_Configuration']==5)
		{
			return 'Unable to delete the experience leave type configuration with leave enforcement, So delete this leave type in leave enforcement configuration';
		}
		else
		{
			return 1;
		}
	}

	
    /**
     * Get Leave requests count send for approval to logged in employee
     */
    public function editOption($sessionId)
    {
        $qryEmpCount = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empLeaves, new Zend_Db_Expr('count(Leave_Id)'))
        ->where('Approver_Id = ?', $sessionId));
        return $qryEmpCount;
    }
    
    /**
     * Get gradeId by logged in employeeId
     */
    public function getGradeId($sessionId)
    {
        $getGrade = $this->_db->select()->from(array('J'=>$this->_ehrTables->empJob), array())
        ->joinInner(array('D'=>$this->_ehrTables->designation), 'J.Designation_Id = D.Designation_Id', 'Grade_Id')
        ->where('J.Employee_Id = ?', $sessionId);
        $getGradeId = $this->_db->fetchOne($getGrade);
        return $getGradeId;
    }
    
    /**
     * Get unit details to store in combo
     */
    public function duration()
    {
        $durationQry = $this->_db->select()
        ->from(array('u'=>$this->_ehrTables->unit),array('u.Target_Value','u.Unit_Tag'))->order('u.Unit_Tag asc')
        ->where('u.Target_Unit =?',"day");

        return $this->_db->fetchPairs($durationQry);
    }
	
	//if the "leave activation type" in leave type is "Days" then add the joining date and 
	public function getLeaveActivationDate($empId, $leaveTypeId)
	{
		$leaveActivationDays = $this->_db->fetchOne($this->_db->select()
									->from(array('leavetype'=>$this->_ehrTables->leavetype),array('Leave_Activation_Days'))
									->where('leavetype.LeaveType_Id = ?', $leaveTypeId)
									->where('leavetype.Applicable_During_Probation = ?','Custom Configuration'));

		if(empty($leaveActivationDays))
		{
			$leaveActivationDays = 0;
		}

		$leaveActivationDate = $this->_db->fetchOne($this->_db->select()
									->from($this->_ehrTables->empJob,array(new Zend_Db_Expr("(Date_Of_Join + INTERVAL $leaveActivationDays DAY)")))
									->where('Employee_Id = ?', $empId));

		return $leaveActivationDate;
	}
    
    //get remaining days to apply leave for an employee
    public function getLeaveActivateDays($empId, $startDate, $leaveTypeId)
    {
		$startDate = date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($startDate)));
		
		$leaveActivationDate = $this->getLeaveActivationDate($empId, $leaveTypeId);
		
		$leaveActivationDate = date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($leaveActivationDate)));

		$datediff = (strtotime($startDate) - strtotime($leaveActivationDate));
		$totalDateDifference = floor($datediff / (60 * 60 * 24));

    	return $totalDateDifference;
    }
    
    //getting the date from which the leavetype is active for the employee
    public function getLeaveTypeActivationDate($empId, $leaveTypeId, $loginEmpId)
    {	
		$accumulateEligibleDaysEnabled = 0;
		$currentQuarterStartDate = '';

		$empLeaveDetails = $this->_db->fetchRow($this->_db->select()
								->from(array('leavetype'=>$this->_ehrTables->leavetype),array('leavetype.Leave_Accrual','leavetype.Advance_Notification','leavetype.Total_Days', 'leavetype.Accumulate_Eligible_Days','leavetype.Period'))
								->joinInner(array('Job'=>$this->_ehrTables->empJob),"Job.Employee_Id = $empId", array('Date_Of_Join'))
								->where('leavetype.LeaveType_Id = ?', $leaveTypeId));
		
		$futureLeaveStartDate = null;
		
		/** Get the advance notification date when the advance notification days exist for a leave type and when the login employee is not a super admin.*/
		if(!empty($empLeaveDetails['Advance_Notification'])){
			$calculateFutureLeaveStartDate=1;

			if(!empty($loginEmpId)){
				$dbAccessRights = new Default_Model_DbTable_AccessRights();
				$loginEmployeeSuperAdminAccessRights = $dbAccessRights->employeeAccessRights($loginEmpId, 'Super Admin');
				/** If the login employee is a 'Super Admin' then we should not calculate the leave minimum start date from the advance notification days. */
				if(count($loginEmployeeSuperAdminAccessRights) > 0 && (Int)$loginEmployeeSuperAdminAccessRights['Employee']['Optional_Choice'] === 1){
					$calculateFutureLeaveStartDate=0;
				}
			}

			if((Int)$calculateFutureLeaveStartDate === 1){
				//we need to allow the user to apply the leave from current date to future date.earlier its allow only future date.
				$advanceNotification = $empLeaveDetails['Advance_Notification']-1;
				$futureLeaveStartDate = date('Y-m-d',strtotime("+".$advanceNotification."days"));
			}
		}
		
		$freezeTo = $freezeFrom ='';
		
		$freezeDateRange=array();
		
		/** check leave freeze exists for the leave type **/
		$leaveFreezeDetails = $this->_db->fetchAll($this->_db->select()
								->from(array('leaveFreeze'=>$this->_ehrTables->leaveFreeze),array('Freeze_From','Freeze_To'))							
								->where('leaveFreeze.LeaveType_Id = ?', $leaveTypeId));		
		
		if(!empty($leaveFreezeDetails)){
			foreach($leaveFreezeDetails as $leaveFreeze)
			{
				if ($leaveFreeze['Freeze_From']<=$leaveFreeze['Freeze_To'])
				{
					array_push($freezeDateRange,date('Y-m-d',strtotime($leaveFreeze['Freeze_From'])));
					while ($leaveFreeze['Freeze_From']<$leaveFreeze['Freeze_To'])
					{					
						$leaveFreeze['Freeze_From'] = date('Y-m-d',strtotime($leaveFreeze['Freeze_From'] ." +1days"));
						array_push($freezeDateRange,date('Y-m-d',strtotime($leaveFreeze['Freeze_From'])));
					}
				}
			}
		}		

		$leaveActivationDate = $this->getLeaveActivationDate($empId, $leaveTypeId);
		
		$dbPayslip = new Payroll_Model_DbTable_Payslip();

		//To get employee salary type
		$salaryType = $dbPayslip->getEmpSalaryType($empId);

		$maxPayslipMonth = $dbPayslip->maxPayslipMonth($empId,$salaryType);
        if(!empty($maxPayslipMonth)){
            $maxPayslipMonth = explode(',',$maxPayslipMonth);
            $lastSalaryDay = $dbPayslip->getSalaryDay($maxPayslipMonth[0],$maxPayslipMonth[1], 31);
            $finalMnth = date('Y-m-d', strtotime('+1 day', strtotime($lastSalaryDay['Last_SalaryDate'])));
        }else{
            /* If payslip not exists, get the date of join */
            $finalMnth = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob,array('Date_Of_Join'))->where('Employee_Id = ?',$empId));
        }
		
		$resignationDate = $this->_dbCommonFun->getEmployeeResignationDate($empId);

		$accumulateEligibleDays = $empLeaveDetails['Accumulate_Eligible_Days'];
		$leaveTypePeriod = $empLeaveDetails['Period'];		

	
		$eligibleLeaveDetails  =  $this->getEmployeeEligibleLeaveDetails($empId,$leaveTypeId);
		$leaveClosureStartDate =  $eligibleLeaveDetails[0]['Leave_Closure_Start_Date'];
		$leaveClosureEndDate   =  $eligibleLeaveDetails[0]['Leave_Closure_End_Date'];

		return array("activationDate" => $leaveActivationDate,'leaveClosureStartDate'=>$leaveClosureStartDate,'leaveClosureEndDate'=>$leaveClosureEndDate,
					 "futureLeaveStartDate" => $futureLeaveStartDate, 'freezeDateRange'=> $freezeDateRange,
					 'orgDateFormat'=>$this->_orgDF['jq'],"maxPayslipMnth"=>$finalMnth, "resignationDate"=>$resignationDate,
					 'currentQuarterStartDate'=>$currentQuarterStartDate,
					 'accumulateEligibleDaysEnabled' => $accumulateEligibleDaysEnabled);
    }
	
    //to fetch the frequency of applying leave for a leave type
    public function frequency($employeeId, $leaveType, $fromDate, $leaveId)
    {
    	$startEndDate  = $this->getLeaveClosureDetails($employeeId, $leaveType);
	    if (!empty($employeeId) && !empty($leaveType) && !empty($fromDate))
        {
            $changedFromDate = $this->_ehrTables->changeDateformat($fromDate);//replace / with -
		    
			//get days difference between leave start date and DOJ+Leave_Activation_Days
            $leaveActivateDays = $this->getLeaveActivateDays($employeeId, $changedFromDate, $leaveType);
            
			$leaveActivateDays = intval($leaveActivateDays);
            
			$frequency = array();
            
			if ($leaveActivateDays >= 0)
            {
            	$strFinStart = strtotime($startEndDate['finstart']);
            	$strFinEnd = strtotime($startEndDate['finend']);
            	
            	$finStart = date('Y-m-d',$strFinStart);
            	$finEnd = date('Y-m-d', $strFinEnd);
            	 
            	$fromDate = date('Y-m-d',strtotime($changedFromDate));

            	$exists = $this->empExists($employeeId,$leaveType);

            	if($exists > 0)
            	{
            		$frequencyQry = $this->_db->select()
            		->from(array('leavetype'=>$this->_ehrTables->leavetype),array('leavetype.Frequency'))
            		->joinInner(array('leave'=>$this->_ehrTables->empLeaves),'leavetype.LeaveType_Id=leave.LeaveType_Id',
    												array(new Zend_Db_Expr("DATE_FORMAT(MAX(leave.End_Date),'".$this->_orgDF['sql']."') as End_Date")))
	                ->where('leave.Employee_Id =?', (int)$employeeId)
	                ->where('leave.LeaveType_Id =?', (int)$leaveType)
	                ->where('leave.End_Date <= ? ', $fromDate)
	                ->where('leave.End_Date >= ? ', $finStart)
	                ->where('leave.End_Date <= ? ', $finEnd)
	                ->where('leave.Approval_Status IN (?)', array('Applied', 'Approved', 'Cancel Applied'));

            		if (!empty($leaveId))
            		{
            			$frequencyQry->where('leave.Leave_Id != ?',$leaveId);
            		}
            		$frequency = $this->_db->fetchRow($frequencyQry);
            		
            		$frequency['balance'] = null;
            		if (!empty($frequency['End_Date']))//last applied or approved date
            		{
            			//$freqDate = date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($frequency['Start_Date'])));
            			$chgedFreqDate = strtotime($this->_ehrTables->changeDateformat($frequency['End_Date']));
            			//if max end date is 1-1-2014 and applied date is 2-1-2014 with frequency 0,
            			//then diff of business working days between max end date +1 and applied date
            			//diff = 0, hence leave can be applied 
            			$freqDateNxtDay = strtotime('+1 day', $chgedFreqDate);
 
            			$freqDate = date('Y-m-d',$freqDateNxtDay);
            			$payslipDb = new Payroll_Model_DbTable_Payslip();
						
            			$difference = $this->getLeaveDuration($leaveType,$freqDate,$fromDate,$employeeId);
						
            			if ($difference != 0 && ($difference <= $frequency['Frequency']))//if frequency=>3,diffence =>4
            			{
            					$frequency['balance'] = $frequency['Frequency'];
            			}
            		}
            		$frequency['startMsg'] = null;
            		if (count($frequency) > 0)//to check emplyee has leave request before?
            		{
            			//to check from date is between financial year?
            			if (($strFinStart > strtotime($changedFromDate) ||
							 $strFinEnd < strtotime($changedFromDate)))
            			{
							$finstart = date($this->_orgDF['php'],strtotime($startEndDate['finstart']));
							$finend   = date($this->_orgDF['php'],strtotime($startEndDate['finend']));

            				$frequency['startMsg'] = "You can apply leave from ".$finstart." to ".$finend;
            			}
            		}
            		if($frequency['balance'] != null) 
            		{
            			if($frequency['startMsg'] != null) 
            			{
            				$frequency['startMsg'] .= "<br>Leave type cannot be used within".$frequency['Frequency']. "day(s)";
            			}
            			else 
            			{
            				$frequency['startMsg'] = "Leave type cannot be used within".$frequency['Frequency']. " day(s)";
            			}
            		}
            		return $frequency;
            	}
            }
            else 
            {
				$frequency['ActivationDays'] = abs($leaveActivateDays);
            	$frequency['startMsg'] = "* You can apply this leave after ". abs($leaveActivateDays) ." days";
            	return $frequency;
            }
        }
    }
	
    //to fetch the frequency of applying leave for a leave type
    public function leaveLimit($employeeId, $leaveType, $leaveId, $leavefromDate, $leavetoDate, $startEndDates, $leaveDuration=NULL,$documentUpload=NULL)
    {
		if (!empty($employeeId) && !empty($leaveType))
        {

			$loggedInEmployeeIsSuperAdmin='No';
			if(!empty($this->_logEmpId)){
				$dbAccessRights = new Default_Model_DbTable_AccessRights();
				$loginEmployeeSuperAdminAccessRights = $dbAccessRights->employeeAccessRights($this->_logEmpId, 'Super Admin');
				/** If the login employee is a 'Super Admin' then we should not calculate the leave minimum start date from the advance notification days. */
				if(count($loginEmployeeSuperAdminAccessRights) > 0 && (Int)$loginEmployeeSuperAdminAccessRights['Employee']['Optional_Choice'] === 1){
					$loggedInEmployeeIsSuperAdmin='Yes';
				}
			}

			$empTotCoDays =0;

			$leaveClosureStartDate = date('Y-m-d',strtotime($startEndDates['finstart']));
			$leaveClosureEndDate = date('Y-m-d',strtotime($startEndDates['finend']));
	    	$finStartYear = date("Y",strtotime($startEndDates['finstart']));
	    
	    	$payslipDb = new Payroll_Model_DbTable_Payslip();
			$limit = array('balance' => null);
	    
            $finStart = date('Y-m-d', strtotime($startEndDates['finstart']));
            $finEnd = date('Y-m-d', strtotime($startEndDates['finend']));
			
		    //same logic like leave history
            $eligLvCond = $this->_db->quoteInto('el.LeaveType_Id = L.LeaveType_Id AND el.Employee_Id = ? AND ', $employeeId).
            			  $this->_db->quoteInto('el.CO_Year =? ', $finStartYear);

            $enOnCond = $this->_db->quoteInto('en.LeaveType_Id = L.LeaveType_Id AND en.Employee_Id = ? AND ', $employeeId).
            			$this->_db->quoteInto('en.EN_Year = ? ', $finStartYear);
            
			$lvBal = "((IFNULL(el.No_Of_Days, 0) + (CASE WHEN el.Eligible_Days is NULL THEN 0.0 ELSE el.Eligible_Days END)) - ((IFNULL(SUM(en.Encashed_Days), 0)) + IFNULL(el.Leaves_Taken, 0)))";

			$lvBalWithoutAppliedDays = "((IFNULL(el.No_Of_Days, 0) + (CASE WHEN el.Eligible_Days is NULL THEN 0.0 ELSE el.Eligible_Days END)) - ((IFNULL(SUM(en.Encashed_Days), 0)) + IFNULL(el.Leaves_Taken, 0)))";

            $historyQry = $this->_db->select()
            ->from(array('L'=>$this->_ehrTables->leavetype),array('L.LeaveType_Id','el.Eligible_Days',
			'SUM(en.Encashed_Days) as Encashed_Days','SUM(el.Leaves_Taken) as Leave_Taken','L.Leave_Name','L.Total_Days','L.Maximum_Limit','el.No_Of_Days',
			'Leave_Balance'=>new Zend_Db_Expr($lvBal),'Leave_Balance_Without_Applied_Days'=>new Zend_Db_Expr($lvBalWithoutAppliedDays)))
            ->joinInner(array('P'=>$this->_ehrTables->empPersonal),"P.Employee_Id = $employeeId", array())
            ->joinInner(array('el'=>$this->_ehrTables->empEligbleLeave),$eligLvCond,array())
            ->joinLeft(array('en'=>$this->_ehrTables->encashedLeave),$enOnCond,array())
			->where('L.Gender = \'ALL\' OR L.Gender = P.Gender');

			/**Get the where condition for the custom group coverage. If the leave balance deos not exist in the
			 * emp eligible leaves for the custom group employees then that leave type should not be considered for the
			 * custom group employee */
			$customGroupWhere = $this->customGroupWhere('L',$leaveType,'el.Employee_Id');
			
			if(!empty($customGroupWhere)){
				$historyQry ->where($customGroupWhere);
			}else{
				$historyQry->where('(L.Coverage = \'ORG\' OR L.Coverage = \'GRA\') AND L.LeaveType_Id = ?',$leaveType);
			}

			$historyQry->group('L.LeaveType_Id');
            $historyRes = $this->_db->fetchRow($historyQry);

			/** This condition is used while fetching the leave history for the leave type and in leave taken fetch query */
            $bwLvDates = $this->_db->quoteInto('(lv.Start_Date BETWEEN ? AND ', $finStart).
				         $this->_db->quoteInto('? )', $finEnd).
				         $this->_db->quoteInto(' AND Approval_Status IN (?)',array('Applied', 'Returned'));
	    	//if it is update shouldn't take current record leave days				 
            if(!empty($leaveId))
            {
            	$bwLvDates .= " AND lv.Leave_Id != $leaveId ";
            }

			$qryLeaveAppliedDays = $this->_db->select()->from(array('lv'=>$this->_ehrTables->empLeaves), array('SUM(lv.Total_Days) as Leave_Applied_Days'))
				->where('lv.Employee_Id =?',$employeeId)
				->where('lv.LeaveType_Id =?',$leaveType)
				->where($bwLvDates);
			$historyRes['Leave_Applied_Days'] = $this->_db->fetchOne($qryLeaveAppliedDays);
			
			
			$historyRes['Leave_Taken'] = (!empty($historyRes['Leave_Taken'])) ? $historyRes['Leave_Taken'] : 0;
			$historyRes['Leave_Applied_Days'] = (!empty($historyRes['Leave_Applied_Days'])) ? $historyRes['Leave_Applied_Days'] : 0;			
			$leaveTakenWithAppliedDays = $historyRes['Leave_Taken'] + $historyRes['Leave_Applied_Days'];

			//we need to subtract the applied day from the existing leave balance
			$historyRes['Leave_Balance'] = $historyRes['Leave_Balance']-$historyRes['Leave_Applied_Days'];
			

			$empTotCoDays = $historyRes['No_Of_Days'];//employee carryforward days for the current leave year

			$empJoinDate = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob,array('Date_Of_Join'))    	
								->where('Employee_Id = ?', $employeeId));

			$leaveTypeDetails = $this->getLeaveTypeRow($leaveType);

			$accrualLeave ='';
			$leavePeriodStartEndDates=array();
			$leaveAccrualWithoutCustomConfig = ['From the beginning of the leave period','After the end of the leave period'];
			if((in_array($leaveTypeDetails['Accrual'],$leaveAccrualWithoutCustomConfig))
			|| ($leaveTypeDetails['Accrual'] == 'Based on the custom configuration' 
			&& (!empty($leaveTypeDetails['Period']) && ($leaveTypeDetails['Period'] == 1 ||
			$leaveTypeDetails['Period'] == 3 || $leaveTypeDetails['Period'] == 6)))){
				if($leaveTypeDetails['Accrual_Restriction_Period']=='Yes' && !empty($leaveTypeDetails['Accrual_Until']))
				{
					$accrualUntil        = $leaveTypeDetails['Accrual_Until'];
					$restrictionFromDate = $this->calculateLeaveApplicableFromDate($leaveTypeDetails,$employeeId);
					$restrictionToDate   = date('Y-m-d', strtotime('+'.$accrualUntil.' day', strtotime($restrictionFromDate)));
					//when the leave From date should be fall in between in this period then period based restriction will be applicable
					if(strtotime($restrictionFromDate) <= strtotime($leavefromDate) && strtotime($restrictionToDate) >= strtotime($leavefromDate))
					{
						$leavePeriodStartEndDates = $this->getLeavePeriodStartEndDates($leaveTypeDetails['Period'],$employeeId, $leaveType, $leaveTypeDetails['Leave_Closure_Based_On'],$startEndDates);
					}
				}
				else
				{
					/* Get the leave period(month/quarter/half year) - start and end date in an array based on the paycycle type */
					$leavePeriodStartEndDates = $this->getLeavePeriodStartEndDates($leaveTypeDetails['Period'],$employeeId, $leaveType, $leaveTypeDetails['Leave_Closure_Based_On'],$startEndDates);
				}
			}
			 
			if(in_array($leaveTypeDetails['Accrual'],$leaveAccrualWithoutCustomConfig) && !empty($leavePeriodStartEndDates)){
				$accumulateFromDate = $this->calculateFromDate($leaveTypeDetails,$employeeId);
				/* If date of join lies in between leave closure start and end date */
				if(strtotime($accumulateFromDate) >= strtotime($leaveClosureStartDate) && 
				strtotime($accumulateFromDate) <= strtotime($leaveClosureEndDate)){
					$startDate = $accumulateFromDate;
				}
				else if(strtotime($accumulateFromDate) < strtotime($leaveClosureStartDate)){
					/* If date of join is less than the leave closure start date */
					$startDate = $leaveClosureStartDate;
				}
				$leaveEndDateQuarterDates = '';
				$currentDate = date('Y-m-d');
				if(!empty($leavePeriodStartEndDates)){
					for($i=0;$i<count($leavePeriodStartEndDates);$i++){
						if((strtotime($currentDate) >= strtotime($leavePeriodStartEndDates[$i][0]) 
						&& strtotime($currentDate) <= strtotime($leavePeriodStartEndDates[$i][1]))){
							$leaveEndDateQuarterDates = $leavePeriodStartEndDates[$i];
							break;
						}
					}

					//when the values are not fall in current date picked the max record in that array
					if(empty($leaveEndDateQuarterDates))
					{
						$leaveEndDateQuarterDates   = end($leavePeriodStartEndDates);
					}

					//If leave quarter date is not empty
					if(!empty($leaveEndDateQuarterDates)){
						if($leaveTypeDetails['Accrual']==='From the beginning of the leave period'){
							//Calculate the leave accrual till the current month end date
							$currentDateVal = $leaveEndDateQuarterDates[1];
						}else{
							$currentDateVal = date('Y-m-d', strtotime($leaveEndDateQuarterDates[0]. '-1 days'));
						}
					}
				}

				// if(intval($leaveTypeDetails['Leave_Enforcement_Configuration']) === 5){
				// 	if(strtotime($currentDateVal) >= strtotime($accumulateFromDate)){
				// 		$accrualLeave = $this->getExperienceLeave($employeeId,$leaveType,$accumulateFromDate,$startEndDates,$currentDateVal);
				// 	}else{
						
				// 		$accrualLeave = 0;
				// 	}
				// }else{
					$strStart=date_create($startDate);
					$strEnd=date_create($leaveClosureEndDate);
				
					$totalDays = date_diff($strStart,$strEnd);
					$totalDays = $totalDays->format("%a");

					if(strtotime($currentDateVal) >= strtotime($startDate)){
						$workStart=date_create($startDate);
						$currentDate=date_create($currentDateVal);
						$employeeWorkedDays = date_diff($workStart,$currentDate);
						$employeeWorkedDays = $employeeWorkedDays->format("%a");
						$currentYearEmployeeLeaveEligiblity = $historyRes['Eligible_Days'];
						$leaveEligibilityPerDay  = number_format(($currentYearEmployeeLeaveEligiblity/$totalDays),3,'.','');
						$accrualLeave = ($employeeWorkedDays*$leaveEligibilityPerDay);
					}else{
						$accrualLeave = 0;
					}
				// }

				//Get the current eligiblity
				$calcNoOfDays = (!empty($historyRes['No_Of_Days']) && $historyRes['No_Of_Days'] > 0) ? $historyRes['No_Of_Days'] : 0;
				$calcleaveTaken = (!empty($historyRes['Leave_Taken']) && $historyRes['Leave_Taken'] > 0) ? $historyRes['Leave_Taken'] : 0;
				$calcEncashedDays = (!empty($historyRes['Encashed_Days']) && $historyRes['Encashed_Days'] > 0) ? $historyRes['Encashed_Days'] : 0;
				$calcAppliedDays = (!empty($historyRes['Leave_Applied_Days']) && $historyRes['Leave_Applied_Days'] > 0) ? $historyRes['Leave_Applied_Days'] : 0;

				$accrualLeave = (($accrualLeave + $calcNoOfDays) - $calcleaveTaken - $calcEncashedDays - $calcAppliedDays);
				$semiValue = $this->getSemiValue($leaveTypeDetails['LeaveType_Id']);
				//Get accrual days
				$accrualLeave = $this->_dbCommonFun->getRoundOffValue('Leave Eligiblity',$accrualLeave,'',$semiValue);
				$limit['Maximum_Limit'] = min(array($accrualLeave, $historyRes['Maximum_Limit']));
				$limit['Leave_Accrual'] = $leaveTypeDetails['Leave_Accrual'];
				$limit['Leave_Accrual_Display_Start_Date'] = date('d M Y',strtotime($startDate));
				$limit['Leave_Accrual_Display_End_Date'] = date('d M Y',strtotime($currentDateVal));
				$limit['History_Leave_Balance'] = $historyRes['Leave_Balance'];
			}
			else{
				$limit['Maximum_Limit'] = min(array($historyRes['Leave_Balance'], $historyRes['Maximum_Limit']));
				$limit['Leave_Accrual'] = $leaveTypeDetails['Leave_Accrual'];
				$limit['Leave_Accrual_Display_Start_Date'] = $limit['Leave_Accrual_Display_End_Date'] = '';
				$limit['History_Leave_Balance'] = 0;
			}
			
            $strToDate = strtotime($this->_ehrTables->changeDateformat($leavetoDate));
            $chgedToDate = date('Y-m-d', $strToDate);            

			$duration = $this-> getLeaveDuration($leaveType,date('Y-m-d', strtotime($this->_ehrTables->changeDateformat($leavefromDate))),$chgedToDate,$employeeId);
			
			/* If half day leave is applied and duration is greater than zero, then duration should be 0.5 */
			if(!is_null($leaveDuration) && $leaveDuration == '0.5' && $duration > 0){
				$duration = $leaveDuration;
			}

			if(!is_null($leaveDuration) && $leaveDuration == '0.25' && $duration > 0){
				$duration = $leaveDuration;
			}

	        if($duration >= 0) {
                $limit['duration'] = $duration;
            }
            else {
                $limit['duration'] = null;
            }
			
            if(!empty($leavetoDate))
            {
            	$frequencyQry = $this->_db->select()
            	->from(array('leavetype'=>$this->_ehrTables->leavetype),array('leavetype.Frequency'))
            	->joinInner(array('leave'=>$this->_ehrTables->empLeaves),'leavetype.LeaveType_Id=leave.LeaveType_Id',
            				array(new Zend_Db_Expr('MIN(leave.Start_Date) as Start_Date')))
            	->where('leave.Employee_Id =?', (int)$employeeId)
            	->where('leave.LeaveType_Id =?', (int)$leaveType)
            	->where('leave.Start_Date >= ? ', $chgedToDate)
            	//need to check end date for reverse frequency check
            	->where('leave.End_Date >= ? ', $finStart)
            	->where('leave.End_Date <= ? ', $finEnd)
            	->where('leave.Approval_Status IN (?)', array('Applied', 'Approved', 'Cancel Applied'));
            	
            	if (!empty($leaveId))
            	{
            		$frequencyQry->where('leave.Leave_Id != ?',$leaveId);
            	}
            	$frequency = $this->_db->fetchRow($frequencyQry);
            	if (!empty($frequency['Start_Date']))//last applied or approved date
            	{
            		//$freqDate = date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($frequency['Start_Date'])));
            		//$chgedFreqDate = strtotime($this->_ehrTables->changeDateformat($chgedToDate));
            		//if max end date is 1-1-2014 and applied date is 2-1-2014 with frequency 0,
            		//then diff of business working days between max end date +1 and applied date
            		//diff = 0, hence leave can be applied
            		$strToDateNxtDay = strtotime('+1 day', $strToDate);
            	
            		$toDateNxtDay = date('Y-m-d', $strToDateNxtDay);
            		$payslipDb = new Payroll_Model_DbTable_Payslip();
					
					$difference = $this->getLeaveDuration($leaveType,$toDateNxtDay,$frequency['Start_Date'],$employeeId);
					
            		if ($difference != 0 && ($difference <= $frequency['Frequency']))//if frequency=>3,diffence =>4
            		{
            			$limit['balance'] = $frequency['Frequency'];
            		}
            	}
		
				
				/** get the minimum total no of days that is applicable for that leave type**/
				$minimumTotalDays = $this->getMinimumLimitForHalfPaidLeaveDeduction($leaveTypeDetails,$documentUpload);
				/** Leave Duration should be greater than minimum total no of days **/
				if(!is_null( $duration ) && $duration > 0) {
					if($duration < $minimumTotalDays)
					{
						$limit['minDurationLimit'] = $minimumTotalDays;
					}
					else{
						$limit['minDurationLimit'] = null;
					}
				}
				else{
					$limit['minDurationLimit'] = null;
				}
				
				$empDateOfJoin = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob,array('Date_Of_Join'))
								->where('Employee_Id =?',$employeeId));

				/** get applicable during notice period flag that is applicable for that leave type**/
				$isLeaveApplicableDuringNotice = $leaveTypeDetails['Applicable_During_Notice_Period'];
				/** If Applicable_During_Notice_Period is 1, employee can apply leave till his resignation.
				** If not,leave start and end dates has to be in between of date of join and notice date **/
				if($isLeaveApplicableDuringNotice == 1)
				{
					$limit['IsLeaveApplicableDuringNoticePeriod'] = null;
				}
				else
				{			
					$empNoticeDate = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->resignation,array('Notice_Date'))
								->where('Employee_Id =?',$employeeId)
								->where('Approval_Status IN (?)',array('Applied','Approved')));
        			
					if(!empty($empNoticeDate)){
						if(((strtotime($leavefromDate) > strtotime($empDateOfJoin)) && (strtotime($leavefromDate) < strtotime($empNoticeDate))) &&
					   ((strtotime($leavetoDate) > strtotime($empDateOfJoin)) && (strtotime($leavetoDate) < strtotime($empNoticeDate))))
						{
							$limit['IsLeaveApplicableDuringNoticePeriod'] = null;
						}
						else{
							$limit['IsLeaveApplicableDuringNoticePeriod'] = 1;
						}
					}
					else{
						$limit['IsLeaveApplicableDuringNoticePeriod'] = null;
					}
				}
				
				/** get applicable during probation flag that is applicable for that leave type**/
				$isLeaveApplicableDuringProbation = $leaveTypeDetails['Applicable_During_Probation'];
				
				$empConfirmationDate = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob,array('Confirmation_Date'))
								->where('Employee_Id =?',$employeeId)
								->where('Confirmed = ?',1));
				
				/** If Applicable_During_Probation is 1, probationary employee can availed this leave.
				** And leave start and end dates has to be in between of date of join and confirmation date. **/
				//if($isLeaveApplicableDuringProbation == 1)
				if($isLeaveApplicableDuringProbation == 'During Probation')
				{
					$empProbationDate = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob,array('Probation_Date'))
								->where('Employee_Id =?',$employeeId));
					
					/** Probation Employee **/
					if(((strtotime($leavefromDate) > strtotime($empDateOfJoin)) && (strtotime($leavefromDate) <= strtotime($empProbationDate))) &&
						((strtotime($leavetoDate) > strtotime($empDateOfJoin)) && (strtotime($leavetoDate) <= strtotime($empProbationDate))))
					{
						$limit['IsLeaveApplicableDuringProbation'] = null;
					}
					else
					{
						/** Non Probationary Employee **/
						$limit['IsLeaveApplicableDuringProbation'] = 1;
					}
				}
				else if($isLeaveApplicableDuringProbation == 'After Probation')
				{
					/** Non Probation employee only are eligible for this leave type **/
					
					/** Probation Employee **/
					if(empty($empConfirmationDate))
					{
						$limit['IsLeaveApplicableDuringProbation'] = 2;
					}
					else{						
						if((strtotime($leavefromDate) >= strtotime($empConfirmationDate)) && (strtotime($leavetoDate) >= strtotime($empConfirmationDate)))
						{
							/** Probation employee whose confirmation date is in future **/
							$limit['IsLeaveApplicableDuringProbation'] = null;
						}
						else{
							/** Non Probationary Employee **/
							$limit['IsLeaveApplicableDuringProbation'] = 2;	
						}
					}
				}
				else{
					$limit['IsLeaveApplicableDuringProbation'] = null;
				}
				
				$limit['Eligible_Days_Based_On_Period'] = null;
				$limit['Period'] = 'Annum';
				$limit['Leave_Period_Lapsed_Days'] = null;

				$leavePeriodArray = array(1,3,6);
				/** If the leave period is monthly, quarterly, halfyearly then calculate the eligible days */
				if($leaveTypeDetails['Accrual'] == 'Based on the custom configuration' && (!empty($leaveTypeDetails['Period']) && 
				in_array($leaveTypeDetails['Period'],$leavePeriodArray)) && !empty($leavePeriodStartEndDates))
				{
					$totalLeavesTaken = $currentLeaveExistsInSameQuarter = 0;
					$currentQuarterStartEndDates = $totalLeavesTakenArr = array();
					$leaveEligibilityResponseMessage = null;

					$currentDate =  date('Y-m-d');
					$applicablePeriodForEligibleDaysAccumulation = array(1,3,6);
					$accumulateFromDate = $this->calculateFromDate($leaveTypeDetails,$employeeId);
					
					$currentQuarterIndex = 0;
					/* iterate the month/quarter/halfyear start and end date to find the leave from and leave to date exists in
					which quarter and push those quarters in an array. Also if leave from and  leave to date does not fall in
					any quarter push the 'in-between' quarter in an array */
					for($i=0;$i<count($leavePeriodStartEndDates);$i++){
						//If leave from and leave to date exists in the same leave period(month/quarter/half-year)
						if(strtotime($leavefromDate) >= strtotime($leavePeriodStartEndDates[$i][0]) &&  strtotime($leavefromDate) <= strtotime($leavePeriodStartEndDates[$i][1]) &&
						(strtotime($leavetoDate) >= strtotime($leavePeriodStartEndDates[$i][0]) && strtotime($leavetoDate) <= strtotime($leavePeriodStartEndDates[$i][1]))){
							$currentLeaveExistsInSameQuarter = 1;
							$currentQuarterStartEndDates[$currentQuarterIndex] = $leavePeriodStartEndDates[$i];
							break;
						}else if((strtotime($leavefromDate) >= strtotime($leavePeriodStartEndDates[$i][0]) && 
						strtotime($leavefromDate) <= strtotime($leavePeriodStartEndDates[$i][1]))){
							// If only the leave from date in this leave period (month/quarter/half-year) and leave to date does not exists in this leave period
							$currentQuarterStartEndDates[$currentQuarterIndex] = $leavePeriodStartEndDates[$i];
							$currentQuarterIndex += 1;
						}
						else if(strtotime($leavefromDate) < strtotime($leavePeriodStartEndDates[$i][0]) &&
						strtotime($leavetoDate) > strtotime($leavePeriodStartEndDates[$i][1])){
							// If leave from and to date does not exist in this leave period(month/quarter/half-year)
							$currentQuarterStartEndDates[$currentQuarterIndex] = $leavePeriodStartEndDates[$i];
							$currentQuarterIndex += 1;
						}else if(strtotime($leavetoDate) >= strtotime($leavePeriodStartEndDates[$i][0]) &&
						strtotime($leavetoDate) <= strtotime($leavePeriodStartEndDates[$i][1])){
							// If only leave to date in this leave period(month/quarter/half-year) and leave from date does not exists in this leave period
							$currentQuarterStartEndDates[$currentQuarterIndex] = $leavePeriodStartEndDates[$i];
							break;
						}
					}
					$leaveQuarterStartDate = '';
					$leaveQuarterEndDate = '';
					/* If leave from and to date leave period and the in-between leave period start and end dates are fetched */
					if(!empty($currentQuarterStartEndDates)){
						$leaveQuarterStartDate = $currentQuarterStartEndDates[0][0];
						$leaveQuarterEndDate = $currentQuarterStartEndDates[0][1];					

						/* Get the total leave taken and leave request duration for the leave from and to date quarters and in-between quarters */
						for($dq=0;$dq<count($currentQuarterStartEndDates);$dq++){
							/* get total leaves taken for a month */
							$quarterTotalLeavesTaken = $this->getTotalLeaves($employeeId,$currentQuarterStartEndDates[$dq][0],$currentQuarterStartEndDates[$dq][1],$leaveType,$leaveId, 1);
							$totalLeavesTaken += $quarterTotalLeavesTaken;

							/* consider leave from date to calculate leave request duration for the leave start month
							and quarter start date for the remaining leave request months */
							$startDateForQuarter = ($dq == 0) ? $leavefromDate : $currentQuarterStartEndDates[$dq][0];

							/* consider leave to date to calculate leave request duration for the leave end month
							and quarter end date for the remaining leave request months  */
							$endDateForQuarter = ($dq == (count($currentQuarterStartEndDates)-1)) ? $leavetoDate : $currentQuarterStartEndDates[$dq][1];

							/* If haly day or quarter day leave is applied, then duration should be 0.5 */
							if(!is_null($leaveDuration) && ($leaveDuration == '0.5' || $leaveDuration == '0.25')){
								$quarterCurrentLeaveDays = $leaveDuration;
							}else{
								/* get leave request duration from leave from date / leave period start date to leave period end date/ leave to date */
								$quarterCurrentLeaveDays = $this-> getLeaveDuration($leaveType,$startDateForQuarter,$endDateForQuarter,$employeeId);
							}

							/* Get the quarter start and end date in this format 'd M Y'(Ex: 09 Aug 2019). */
							$leavePeriodDisplayDates = array('leavePeriodStartDate' => date('d M Y',strtotime($currentQuarterStartEndDates[$dq][0])),
															'leavePeriodEndDate' => date('d M Y',strtotime($currentQuarterStartEndDates[$dq][1])));

							/* Push the leaves taken and leave request duration for each month in an array */
							$leaveTakenAndRequestDays = array('leavesTaken'=>$quarterTotalLeavesTaken, 'leaveRequestDays'=>$quarterCurrentLeaveDays,
																'leavePeriodDates'=>$leavePeriodDisplayDates);
							$totalLeavesTakenArr[$dq] = $leaveTakenAndRequestDays;
						}

						if(!is_null( $duration ) && $duration > 0) {
							$totalDays = $duration + $totalLeavesTaken;
						}else{
							$totalDays = $totalLeavesTaken;
						}

						//If the leave period is monthly get all the months - total eligible days from the config table
						if($leaveTypeDetails['Period'] == 1){
							$monthlyEligibleDaysDetails = $this->getEligibleDaysForLeavePeriod($leaveType);
						}else{
							$monthlyEligibleDaysDetails = array();
						}

						/* If Accumulate Eligible days is not enabled */
						if(empty($leaveTypeDetails['Accumulate_Eligible_Days'])){
							
							$iscurrentLeavePeriodAppliedDaysValid = $empTotalLeavesTaken = 0;
							
							//Find the total number of months based on the leave period monthly, quarterly and halfyearly
							$previousLeavePeriodDetails = $this->getPreviousCurrentLeavePeriodDetails($leavePeriodStartEndDates,$startEndDates['finstart'],$startEndDates['finend'],$currentDate,$empDateOfJoin,$leaveTypeDetails['Period'],$monthlyEligibleDaysDetails);

							if($loggedInEmployeeIsSuperAdmin=='No' && strtotime($previousLeavePeriodDetails['currentLeavePeriodStartDate']) > strtotime($leavefromDate))
							{
								$leaveEligibilityResponseMessage = 'Accumulated leave is configured with "no accumulation," thus, you are unable to request leave for past dates. Please contact your HR admin for support and assistance.';
								$limit['Eligible_Days_Based_On_Period'] = $leaveEligibilityResponseMessage;
							}
							else
							{
								//If the current leave period(monthly,quarterly and halfyearly) is not the first leave period
								if($previousLeavePeriodDetails['numberOfPreviousLeavePeriod'] > 0){
									$employeeStartDate=$previousLeavePeriodDetails['employeeStartDate'];
									$currentDateToCompare=$previousLeavePeriodDetails['currentDateToCompare'];
									$currentDateToCompareTimeStamp=strtotime($previousLeavePeriodDetails['currentDateToCompare']);

									//Previous leave period total eligible days
									$eachLeavePeriodTotalEligibleDays = min($leaveTypeDetails['Eligible_Days_Based_On_Period'],$historyRes['Eligible_Days']);

									//Iterate a loop to find the previous perid lapsed days
									$previousPeriodTotalApplicableDays=0;
									$allPreviousPeriodLeaveTaken =0;
									
									for($i=0;$i<count($leavePeriodStartEndDates);$i++){
										$leavePeriodStartDateTimeStamp = strtotime($leavePeriodStartEndDates[$i][0]);
										$leavePeriodEndDateTimeStamp = strtotime($leavePeriodStartEndDates[$i][1]);
										
										if(($currentDateToCompareTimeStamp >= $leavePeriodStartDateTimeStamp) && ($currentDateToCompareTimeStamp <= $leavePeriodEndDateTimeStamp)){
											break;
										}else if((strtotime($employeeStartDate) > $leavePeriodEndDateTimeStamp)){
											//If the employee DOJ does not fall in the input 
											$previousPeriodTotalApplicableDays +=0;
											$allPreviousPeriodLeaveTaken += 0;
										}else{						
											//Leave taken on the previous leave period
											// $eachLeavePeriodLeavesTaken = $this->getLeaveTakenAppliedOnLeavePeriod($employeeId,$leaveType,$leavePeriodStartEndDates[$i][0],$leavePeriodStartEndDates[$i][1],$leaveId);										
											$eachLeavePeriodLeavesTaken = $this->getTotalLeaves($employeeId,$leavePeriodStartEndDates[$i][0],$leavePeriodStartEndDates[$i][1],$leaveType,$leaveId,1);
											$allPreviousPeriodLeaveTaken += $eachLeavePeriodLeavesTaken;

											//Calculate previous leave period total eligible days if leave period is monthly
											if($leaveTypeDetails['Period'] == 1){
												$inputMonthNumber = $month = date("n", strtotime($leavePeriodStartEndDates[$i][1]));
												$eachLeavePeriodTotalEligibleDays = min($monthlyEligibleDaysDetails[$inputMonthNumber],$historyRes['Eligible_Days']);
											}
											
											$eachLeavePeriodLapsedDays = $eachLeavePeriodTotalEligibleDays - $eachLeavePeriodLeavesTaken;
											if($eachLeavePeriodLapsedDays<=0){
												$eachLeavePeriodLapsedDays =0;
											}

											//when the logged in employee is super admin try to apply the leave for past date we should not check lapsed days for previous period
											if($loggedInEmployeeIsSuperAdmin=='Yes' && strtotime($previousLeavePeriodDetails['currentLeavePeriodStartDate']) > strtotime($leavefromDate))
											{
												$leavePeriodLapsedDays 			 		= $eachLeavePeriodLapsedDays;
												if($leavePeriodLapsedDays > 0)
												{
													$leaveEligibilityResponseMessage 	= 'The leave accumulation is set to "no accumulation." As an admin, you will be able to use the lapsed days('.$leavePeriodLapsedDays.') while submitting the leave application on behalf of employees.  Are you sure to initiate the leave application?';
													$limit['Leave_Period_Lapsed_Days']  = $leaveEligibilityResponseMessage;
													$eachLeavePeriodLapsedDays 		    = 0;
												}
											}
											$leavePeriodApplicableDays = $eachLeavePeriodTotalEligibleDays - $eachLeavePeriodLapsedDays;
											$previousPeriodTotalApplicableDays += $leavePeriodApplicableDays;
										}
									}

								}else{
									$previousPeriodTotalApplicableDays = $allPreviousPeriodLeaveTaken = 0;
								}
								//Calculate previous leave period total eligible days if leave period is monthly
								if($leaveTypeDetails['Period'] == 1){
									$currentPeriodMonthNumber = $month = date("n", strtotime($previousLeavePeriodDetails['currentLeavePeriodEndDate']));
									$currentPeriodTotalEligibileDays = $monthlyEligibleDaysDetails[$currentPeriodMonthNumber];
								}else{
									$currentPeriodTotalEligibileDays = $leaveTypeDetails['Eligible_Days_Based_On_Period'];
								}

								//Current leave period eligible days
								$currentLeavePeriodTotalEligibleDays = min($historyRes['Eligible_Days'],$currentPeriodTotalEligibileDays);
								$currentLeavePeriodEligibileDays = $currentLeavePeriodTotalEligibleDays + $previousPeriodTotalApplicableDays;

								$currentLeavePeriodEligibility = min($historyRes['Eligible_Days'],$currentLeavePeriodEligibileDays);

								//Current leave period leaves taken
								//	$currentLeavePeriodLeavesTaken = $this->getLeaveTakenAppliedOnLeavePeriod($employeeId,$leaveType,$previousLeavePeriodDetails['currentLeavePeriodStartDate'],$previousLeavePeriodDetails['currentLeavePeriodEndDate'],$leaveId);
								$currentLeavePeriodLeavesTaken = $this->getTotalLeaves($employeeId,$previousLeavePeriodDetails['currentLeavePeriodStartDate'],$previousLeavePeriodDetails['currentLeavePeriodEndDate'],$leaveType,$leaveId,1);

								//when the future period exist and future period leaves are taken based on the current eligiblity so we need to find that and subtract it from the currentLeavePeriodApplicableDays.
								if(!empty($previousLeavePeriodDetails['futurePeriodStartDate'])&& !empty($previousLeavePeriodDetails['futurePeriodEndDate']))
								{
									$futureLeavePeriodLeavesTaken  = $this->getTotalLeaves($employeeId,$previousLeavePeriodDetails['futurePeriodStartDate'],$previousLeavePeriodDetails['futurePeriodEndDate'],$leaveType,$leaveId,1);
								}
								else
								{
									$futureLeavePeriodLeavesTaken  = 0;
								}
								
								//Current leave period applicable days
								$currentLeavePeriodApplicableDays = ($currentLeavePeriodEligibility + $empTotCoDays) - $currentLeavePeriodLeavesTaken - $allPreviousPeriodLeaveTaken-$futureLeavePeriodLeavesTaken;
								if($currentLeavePeriodApplicableDays < 0)
								{
									$currentLeavePeriodApplicableDays = 0;
								}
								if($currentLeavePeriodApplicableDays >= $duration){
									$leaveEligibilityResponseMessage = null;
								}else{
									$leaveEligibilityResponseMessage = "You have only ".$currentLeavePeriodApplicableDays." days available for the current leave period.";
								}
								$limit['Eligible_Days_Based_On_Period'] = $leaveEligibilityResponseMessage;
							}
						}
						else{
							$employeeLeaveDetails = array(
								'accumulateFromDate' => $accumulateFromDate,
								'currentDate' => $currentDate,
								'leavePeriodStartEndDates' => $leavePeriodStartEndDates,
								'leaveClosureStartDate' => $leaveClosureStartDate,
								'leaveClosureEndDate' => $leaveClosureEndDate,
								'empTotCoDays' => $empTotCoDays,
								'leaveTakenWithAppliedDays' => $leaveTakenWithAppliedDays,
								'dateOfJoin' => $empDateOfJoin,
								'monthlyEligibleDaysDetails' => $monthlyEligibleDaysDetails
							);
							//Calculate the total eligible days till the current leave period
							$totalEligibleDaysTillCurrentPeriod = $this->getAccumulationEnabledLeaveTypeEligibleDays($leaveTypeDetails,$historyRes,$employeeLeaveDetails);
							//If total eligibility exist
							if($totalEligibleDaysTillCurrentPeriod  > 0){
								if($totalEligibleDaysTillCurrentPeriod >= $duration){
									$leaveEligibilityResponseMessage = null;
								}else{
									$leaveEligibilityResponseMessage = "You have only ".$totalEligibleDaysTillCurrentPeriod." days available for the current leave period.";
								}
							}else{
								$leaveEligibilityResponseMessage = "Your leave quota for the current leave period has been completed.";
							}
							$limit['Eligible_Days_Based_On_Period'] = $leaveEligibilityResponseMessage;
						}
					}
				}
			}
            
	    	return $limit;
        }
    }


    //to check whether an employee already applied leave for a leave type
    public function empExists($employeeId,$leaveType)
    {
        $leaveQry = $this->_db->select()
        ->from(array('leave'=>$this->_ehrTables->empLeaves), new Zend_Db_Expr('Count(Leave_Id)'))
        ->where('leave.Employee_Id =?',$employeeId)
        ->where('leave.LeaveType_Id =?',$leaveType)
        ->where('leave.Approval_Status IN (?)', array('Applied', 'Approved', 'Cancel Applied'));
        return $this->_db->fetchOne($leaveQry);

    }
    //to fetch leave type record based on its id
    public function getLeaveTypeRow($leaveTypeId,$workFlowEvent=null)
    {
        $qryLeaveType=$this->_db->select()->from(array('L'=>$this->_ehrTables->leavetype),array('*'));

		if(!empty($workFlowEvent))
		{
			$qryLeaveType->joinLeft(array('WF'=>$this->_ehrTables->workflows),"L.Workflow_Id = WF.Workflow_Id",array('WF.Event_Id'));
		}

		$qryLeaveType->where('L.LeaveType_Id =?', $leaveTypeId);

		$leaveTypeDetails = $this->_db->fetchRow($qryLeaveType);

		return $leaveTypeDetails;
    }
    
    /**
     *
     * @param int $leaveType - applied leave type id
     * @param int $leaveId - edit leave unique id
     * @param date(dd/mm/yyyy) $fromDate - selected fromdate
     * @param date(dd/mm/yyyy) $todate - selected todate
     * @param int $employeeId - employee unique id
     * @param string $condition - either 'limt' or 'frequency'
     * @return array(fromdatevalidation:enddatevalidation)
     */
	public function leaveValidate ($leaveType, $leaveId, $fromDate, $toDate, $employeeId,$duration, $leavePeriod =null,$documentUpload=null,$calledFrom=null)
    {
		$validationMessageArray = array();
        $leaveStartClosureDate = $this->getLeaveClosureDetails($employeeId,$leaveType);
		
	    $result1 = $this->frequency($employeeId,$leaveType,$fromDate,$leaveId);
        $result = $this->leaveLimit($employeeId,$leaveType,$leaveId,$fromDate,$toDate, $leaveStartClosureDate, $duration,$documentUpload);//return max leave limit, holidays				

		$isLeaveAppliedInPreviousOrNextYear = 0;

        $formstDate = date_create($this->_ehrTables->changeDateformat($fromDate));
        $formenDate = date_create($this->_ehrTables->changeDateformat($toDate));
        $difference = date_diff($formstDate, $formenDate);
		
        $result['endMsg'] = null;
        
		if ($duration == "0.5" && $result['duration'] > 0)
        {			
            $result['duration'] = 0.5;
        }

		if ($duration == "0.25" && $result['duration'] > 0)
        {			
            $result['duration'] = 0.25;
        }
		
		$shortTimeOff = $this->checkShortTimeOff($fromDate,$toDate,$employeeId);	

        if ($difference->invert == 1)//if two date are asending it'l be 0
        {
            $result['endMsg'] = " End date should be greater than or equal to start date";
        }
        
		$alreadyapplied = $this->leaveExists($employeeId, $leaveId,$fromDate, $toDate, $leaveType, $leavePeriod, $duration);

        $result['AlreadyApplied'] = $alreadyapplied;

		$leaveTypeDetails = $this->getLeaveTypeRow($leaveType);
		if($leaveTypeDetails['Parent_Leave']=='Yes' && !empty($leaveTypeDetails['Parent_LeaveTypeId']))
		{
			$parentLeaveStartClosureDate = $this->getLeaveClosureDetails($employeeId,$leaveTypeDetails['Parent_LeaveTypeId']);

			if($parentLeaveStartClosureDate['Leave_Balance'] > 0)
			{
				$parentLeaveTypeDetails = $this->getLeaveTypeRow($leaveTypeDetails['Parent_LeaveTypeId']);
				$result['endMsg'] = "You are eligible to apply for ".$leaveTypeDetails['Leave_Name']." only after you have exhausted your available balance of ".$parentLeaveTypeDetails['Leave_Name'].".";
			}
		}

		if($calledFrom !== 'leavesdataimport'){
			$restrictedAdjacentLeaveTypesExist = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->restrictedAdjacentLeaveTypes,array('Primary_Leave_Type_Id','Secondary_Leave_Type_Id'))
											->where('Primary_Leave_Type_Id =?',$leaveType)
											->orwhere('Secondary_Leave_Type_Id =?',$leaveType));
			if (!empty($restrictedAdjacentLeaveTypesExist)) {
				$primaryLeaveTypeIdGroup = [];
				$secondaryLeaveTypeIdGroup = [];

				$primaryLeaveTypeIdGroup = $this->_dbCommonFun->organizeDataByEmployeeIdAndDate($restrictedAdjacentLeaveTypesExist, 'Primary_Leave_Type_Id');
				$secondaryLeaveTypeIdGroup = $this->_dbCommonFun->organizeDataByEmployeeIdAndDate($restrictedAdjacentLeaveTypesExist, 'Secondary_Leave_Type_Id');

				$restrictedSecondaryLeaveTypeIds = [];
				$restrictedPrimaryLeaveTypeIds = [];

				if (isset($primaryLeaveTypeIdGroup[$leaveType])) {
					foreach ($primaryLeaveTypeIdGroup[$leaveType] as $item) {
						$restrictedSecondaryLeaveTypeIds[] = $item['Secondary_Leave_Type_Id'];
					}
				}

				if (isset($secondaryLeaveTypeIdGroup[$leaveType])) {
					foreach ($secondaryLeaveTypeIdGroup[$leaveType] as $item) {
						$restrictedPrimaryLeaveTypeIds[] = $item['Primary_Leave_Type_Id'];
					}
				}
				// Merge both sets of restricted IDs
				$restrictedAdjacentLeaveTypeIds = array_merge($restrictedSecondaryLeaveTypeIds, $restrictedPrimaryLeaveTypeIds);
				//If the combined leaves restriction exist call the endpoint to validate it
				if (!empty($restrictedAdjacentLeaveTypeIds)) {
					//If it is called from leaves, payslip related validation, attendance shortage leaves, late attendance leaves this block will be executed
					$leaveValidationInputs = array(
						'employeeId' => $employeeId,
						'leaveType' => $leaveType,
						'leaveId' => $leaveId,
						'leaveFrom' => $fromDate,
						'leaveTo' => $toDate
					);
					$leavesValidationResult = $this->validateRestrictedAdjacentLeaves($leaveValidationInputs);
					if (!empty($leavesValidationResult) && $leavesValidationResult)
					{
						if(!empty($leavesValidationResult['message'])){
							array_push($validationMessageArray,$leavesValidationResult['message']);
						}else{
							if(!empty($leavesValidationResult['validationResult'])){
								$validationResponseMessages = $leavesValidationResult['validationResult'];
								if(!empty($validationResponseMessages['restrictedAdjacentLeaveExist'])){
									array_push($validationMessageArray,'Adjacent leaves are restricted, the leave request cannot be processed.');
								}
							}
						}

						$validationMessageAsString = implode(", ",$validationMessageArray);

						if ($result['endMsg'] != null)
						{
							$result['endMsg'] .= "<br> ".$validationMessageAsString;
						}
						else
						{
							$result['endMsg'] = $validationMessageAsString;
						}
					}
				}
			}
		}

		if($leaveTypeDetails['Enable_Personal_Choice']=='Yes')
		{
			$payslipDb = new Payroll_Model_DbTable_Payslip();
			$orgHolidayDates = $payslipDb->orgHolidayDates($employeeId,$fromDate,$toDate,'fetchCol',$leaveTypeDetails['Enable_Personal_Choice']);
			$personalChoiceHolidayDuration = count($orgHolidayDates);
			//personal choice holiday duration is not equal to original leave duration then employee is chosen the business working day
			//$result['duration']!=$personalChoiceHolidayDuration
			//employee applied leave is duration should be greater than personal choice holiday duration we need to throw an error message
			if($result['duration'] > 0 && $personalChoiceHolidayDuration > 0 && $result['duration'] > $personalChoiceHolidayDuration)
			{
				$personalChoiceHolidayDetails = $payslipDb->orgHolidayDates($employeeId,$leaveStartClosureDate['finstart'],$leaveStartClosureDate['finend'],'fetchCol','listOnlyPersonalChoice');
				if(!empty($personalChoiceHolidayDetails))
				{
					$personalChoiceHolidayDateArr = array();
					foreach($personalChoiceHolidayDetails as $personalChoiceHoliday)
					{
						$personalChoiceHolidayDate = date($this->_orgDF['php'],strtotime($personalChoiceHoliday));
						array_push($personalChoiceHolidayDateArr,$personalChoiceHolidayDate);
					}
					$personalChoiceHolidayDates = implode(", ",$personalChoiceHolidayDateArr);
				}
				else
				{
					$personalChoiceHolidayDates = '';
				}
				
				if(!empty($personalChoiceHolidayDates))
				{
					$result['endMsg'] = "Please choose one of the following personal choice holidays ".$personalChoiceHolidayDates;	
				}
				else
				{
					$result['endMsg'] = "There are no personal choice holidays for this employee";
				}
			}
			else if($personalChoiceHolidayDuration == 0)
			{
				$result['endMsg'] = "There are no personal choice holidays for the selected duration";
			}
		}

		if (!empty($alreadyapplied) && $alreadyapplied)
        {
			if ($result['endMsg'] != null)
			{
                $result['endMsg'] .= "<br> You have already applied for this duration..'";
			}
            else
			{
                $result['endMsg'] = " You have already applied for this duration..'";
            }
        }
		
		//$documentUpload
		$leaveBalance 		= $leaveStartClosureDate['Leave_Balance'];		
		$result['duration'] = $this->getLeaveUnitCalculation($leaveTypeDetails,$documentUpload,$result['duration']);

		$leaveTotalDays = $this->getPendingApprovalLeaveDays($employeeId,$leaveType);
		if($leaveTotalDays > 0)
		{
			$leaveTotalDaysMessage ='Currently, there are '.$leaveTotalDays.' day(s) either pending for approval or returned for your action.';
		}
		else
		{
			$leaveTotalDaysMessage ='';
		}

		if($leaveBalance < $result['duration'])
		{
			if($result['endMsg'] != null)
			{
                $result['endMsg'] .= "<br> Employee available leave balance is less than applied leave.".$leaveTotalDaysMessage;		
            }
            else
			{
                $result['endMsg'] = " Employee available leave balance is less than applied leave.".$leaveTotalDaysMessage;				
            }
		}
        if (((strtotime($leaveStartClosureDate['finstart']) > strtotime($this->_ehrTables->changeDateformat($toDate)) ||
			 strtotime($leaveStartClosureDate['finend']) < strtotime($this->_ehrTables->changeDateformat($toDate))) &&
			!empty($toDate)) ||
			((strtotime($leaveStartClosureDate['finstart']) > strtotime($this->_ehrTables->changeDateformat($fromDate)) ||
			 strtotime($leaveStartClosureDate['finend']) < strtotime($this->_ehrTables->changeDateformat($fromDate)))  &&
			!empty($fromDate)))
        {
			$isLeaveAppliedInPreviousOrNextYear = 1;
			$finstart = date($this->_orgDF['php'],strtotime($leaveStartClosureDate['finstart']));
			$finend   = date($this->_orgDF['php'],strtotime($leaveStartClosureDate['finend']));

            if($result['endMsg'] != null)
			{
          		$result['endMsg'] = "<br> You can apply leave from ".$finstart." to ".$finend."";
            }
            else
			{
          		$result['endMsg'] = "You can apply leave from ".$finstart." to ".$finend."";
            }
        }

     	if($result['duration'] == 'shiftnotscheduled' && gettype($result['duration']) =='string') 
		{
			if($result['endMsg'] != null)
			{
                $result['endMsg'] .= "<br> Shift is not scheduled to apply the leave, please contact your reporting manager / administrator";		
            }
            else
			{
                $result['endMsg'] = " Shift is not scheduled to apply the leave, please contact your reporting manager / administrator";				
            }
		}
		else if ($result['duration'] == 0)
        {
	      	if($result['endMsg'] != null)
			{
                $result['endMsg'] .= "<br> You have selected the holiday or weekoff";		
            }
            else
			{
                $result['endMsg'] = " You have selected the holiday or weekoff";				
            }
		}
        else if ($result['duration'] > $result['Maximum_Limit'])
        {			
            if ($result['Maximum_Limit'] <= 0)
            {
				/* If leave accrual enabled, the leave balance exists and the available days till the current date is zero or less than zero */
				if( $result['Leave_Accrual'] == 1 && $result['History_Leave_Balance'] > 0){
					if($result['endMsg'] != null)
					{
						$result['endMsg'] .= "<br> You have only 0 day(s) available from ".$result['Leave_Accrual_Display_Start_Date']." to ".$result['Leave_Accrual_Display_End_Date'].".".$leaveTotalDaysMessage;
					}
					else
					{
						$result['endMsg'] = " You have only 0 day(s) available from ".$result['Leave_Accrual_Display_Start_Date']." to ".$result['Leave_Accrual_Display_End_Date'].".".$leaveTotalDaysMessage;
					}
				}
				else{
					if($result['endMsg'] != null)
					{
						$result['endMsg'] .= "<br> Your leave quota for this leave type has been completed.".$leaveTotalDaysMessage;
					}
					else
					{
						$result['endMsg'] = " Your leave quota for this leave type has been completed.".$leaveTotalDaysMessage;
					}
				}
            }
            else
            {
				/* If leave accrual enabled, the available days till the current date is greater than zero and the leave balance is 
				greater than the applied leave duration. */
				if( $result['Leave_Accrual'] == 1 && $result['History_Leave_Balance'] > $result['duration']){
					if($result['endMsg'] != null)
					{
						$result['endMsg'] .= "<br> You have only ".$result['Maximum_Limit']." day(s) available from ".$result['Leave_Accrual_Display_Start_Date']." to ".$result['Leave_Accrual_Display_End_Date'].".".$leaveTotalDaysMessage;
					}
					else
					{
						$result['endMsg'] = " You have only ".$result['Maximum_Limit']." day(s) available from ".$result['Leave_Accrual_Display_Start_Date']." to ".$result['Leave_Accrual_Display_End_Date'].".".$leaveTotalDaysMessage;
					}
				}
				else if ($result['Eligible_Days_Based_On_Period'] != null 
				&& empty($isLeaveAppliedInPreviousOrNextYear))
				{
					if ($result['endMsg'] != null)
					{
						/* append the message returned from the function along with the previous messages */
						$result['endMsg'] .= "<br> ".$result['Eligible_Days_Based_On_Period'].$leaveTotalDaysMessage;
					}
					else
					{
						/* append the message returned from the function */
						$result['endMsg'] = $result['Eligible_Days_Based_On_Period'].$leaveTotalDaysMessage;
					}
				}else{
					
					if ($result['endMsg'] != null)
					{
						$result['endMsg'] .= "<br> You can't apply more than ".$result['Maximum_Limit']." day(s).".$leaveTotalDaysMessage;
					}
					else
					{
						$result['endMsg'] = " You can't apply more than ".$result['Maximum_Limit']." day(s).".$leaveTotalDaysMessage;
					}
				}
            }
        }
		else if ($result['Eligible_Days_Based_On_Period'] != null &&
		$result['duration'] <= $result['Maximum_Limit']	&& empty($isLeaveAppliedInPreviousOrNextYear))
        {
        	if ($result['endMsg'] != null)
        	{
				/* append the message returned from the function along with the previous messages */
				$result['endMsg'] .= "<br> ".$result['Eligible_Days_Based_On_Period'].$leaveTotalDaysMessage;
        	}
        	else
			{
				/* append the message returned from the function */
				$result['endMsg'] = $result['Eligible_Days_Based_On_Period'].$leaveTotalDaysMessage;
        	}
        }

        if ($result['balance'] != null)
        {
        	if ($result['endMsg'] != null)
        	{
        		$result['endMsg'] .= "<br> Leave type cannot be used within $result[balance] day(s)";
        	}
        	else
			{
        		$result['endMsg'] = " Leave type cannot be used within $result[balance] day(s)";
        	}
        }
        
		if ($result['minDurationLimit'] != null)
        {
        	if ($result['endMsg'] != null)
        	{
        		$result['endMsg'] .= "<br> Leave Duration should be minimum of ".$result['minDurationLimit']." day(s)";
        	}
        	else
			{
        		$result['endMsg'] = " Leave Duration should be minimum of ".$result['minDurationLimit']." day(s)";
        	}
        }
		
		if ($result['IsLeaveApplicableDuringNoticePeriod'] != null)
        {
        	if ($result['endMsg'] != null)
        	{
        		$result['endMsg'] .= "<br> Leave type can be used only before notice period";
        	}
        	else
			{
        		$result['endMsg'] = " Leave type can be used only before notice period";
        	}
        }
		
		if ($result['IsLeaveApplicableDuringProbation'] != null)
        {
			/** If leave types are applicable during probation  **/
			if ($result['IsLeaveApplicableDuringProbation'] == 1)
			{
				if ($result['endMsg'] != null)
				{
					$result['endMsg'] .= "<br> Leave date is greater than the employee probation date";
				}
				else
				{
					$result['endMsg'] = " Leave date is greater than the employee probation date";
				}
			}
			else
			{
				/** If leave types are not applicable during probation  **/
				
				if ($result['endMsg'] != null)
				{
					$result['endMsg'] .= "<br> Leave type cannot be used for probationary employee";
				}
				else
				{
					$result['endMsg'] = " Leave type cannot be used for probationary employee";
				}	
			}        	
        }
		
       return array('frequency'=>$result1,'maxlimit'=>$result,'shortTimeOffExist'=>$shortTimeOff);
    }

	//to avoid the overlapping between leaves
    public function leaveExists($empId, $leaveId, $startDate, $endDate, $leaveType = null, $leavePeriod = null, $duration = null)
    {
      	$dateConditions = $this->_db->quoteInto(new Zend_Db_Expr('leave.Start_Date BETWEEN ? '),$startDate).
					$this->_db->quoteInto(new Zend_Db_Expr('AND ? '), $endDate).' OR '.
					$this->_db->quoteInto(new Zend_Db_Expr('leave.End_Date BETWEEN ? '), $startDate).
					$this->_db->quoteInto(new Zend_Db_Expr('AND ?'), $endDate);

		$dateConditions .=' OR '.$this->_db->quoteInto('?',$startDate);
		$dateConditions .=$this->_db->quoteInto('?',new Zend_Db_Expr(' BETWEEN leave.Start_Date AND leave.End_Date'));
		$dateConditions .=' OR '.$this->_db->quoteInto('?',$endDate);
		$dateConditions .= $this->_db->quoteInto('?',new Zend_Db_Expr(' BETWEEN leave.Start_Date AND leave.End_Date'));
	

		$approvalStaus = array('Cancelled','Rejected');

		$qryLeaveExist = $this->_db->select()
		->from(array('leave'=>$this->_ehrTables->empLeaves),array('Leave_Id','Leave_Period','Duration'))
		->where('leave.Employee_Id = ?',$empId)
		->where('leave.Approval_Status NOT IN (?)',$approvalStaus)
		->where($dateConditions);
		
		if (!empty($leaveId))
		{
			$qryLeaveExist->where('leave.Leave_Id != ?',$leaveId);
		}
		
		$leaveExistDetails = $this->_db->fetchRow($qryLeaveExist);

		if(!empty($leaveExistDetails))
		{
			//this block will work only when the current leave is half day and already exist leave also half day leave
			if(!empty($duration) && $duration == "0.5" && !empty($leaveExistDetails['Leave_Period']) 
			&& !empty($leaveExistDetails['Duration']) && $leaveExistDetails['Duration'] == "0.5")
			{
				if($leavePeriod==$leaveExistDetails['Leave_Period'])
				{
					$leaveExist = $leaveExistDetails['Leave_Id'];
				}
				else
				{
					$leaveExist = 0;
				}
			}
			else if(!empty($duration) && $duration == "0.25" && !empty($leaveExistDetails['Leave_Period']) 
			&& !empty($leaveExistDetails['Duration']) && $leaveExistDetails['Duration'] == "0.25")
			{
				//this block will work only when the current leave is quarter day and already exist leave also quarter day leave
				if($leavePeriod==$leaveExistDetails['Leave_Period']){
					$leaveExist = $leaveExistDetails['Leave_Id'];
				}
				else
				{
					$leaveExist = 0;
				}
			}else if(!empty($duration) && $duration == "0.5" && !empty($leaveExistDetails['Leave_Period']) 
			&& !empty($leaveExistDetails['Duration']) && $leaveExistDetails['Duration'] == "0.25"){
				$firstHalfQuartersArray = ['First Quarter','Second Quarter'];
				$secondHalfQuartersArray = ['Third Quarter','Fourth Quarter'];

				if($leavePeriod == 'First Half' && (in_array($leaveExistDetails['Leave_Period'],$firstHalfQuartersArray)))
				{
					$leaveExist = $leaveExistDetails['Leave_Id'];
				}
				else if($leavePeriod == 'Second Half' && (in_array($leaveExistDetails['Leave_Period'],$secondHalfQuartersArray)))
				{
					$leaveExist = $leaveExistDetails['Leave_Id'];
				}
				else
				{
					$leaveExist = 0;
				}
			}else if(!empty($duration) && $duration == "0.25" && !empty($leaveExistDetails['Leave_Period']) 
			&& !empty($leaveExistDetails['Duration']) && $leaveExistDetails['Duration'] == "0.5")
			{
				$firstHalfQuartersArray = ['First Quarter','Second Quarter'];
				$secondHalfQuartersArray = ['Third Quarter','Fourth Quarter'];

				if(in_array($leavePeriod,$firstHalfQuartersArray) && $leaveExistDetails['Leave_Period'] == 'First Half')
				{
					$leaveExist = $leaveExistDetails['Leave_Id'];
				}
				else if(in_array($leavePeriod,$secondHalfQuartersArray) && $leaveExistDetails['Leave_Period'] == 'Second Half')
				{
					$leaveExist = $leaveExistDetails['Leave_Id'];
				}
				else
				{
					$leaveExist = 0;
				}
			}
			else
			{
				//this block will work in following scenarios
				//i).when the current leave is half day leave and already exist leave also full day leave
				//ii).when the current leave is full day leave and already exist leave also half day leave
				//iii).when the current leave is full day leave and already exist leave also full day leave
				$leaveExist =  $leaveExistDetails['Leave_Id'];
			}
		}
		else
		{
			$leaveExist = 0;
		}
		
		return $leaveExist;
    }
    
    /**
     * Get leave type count
     */
    public function leavetypeCount()
    {
        return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->leavetype, new Zend_Db_Expr('count(LeaveType_Id)')));
    }
    
	/**
     * Check leave type is paid/unpaid for leave id
     */
    public function checkEmpLeaveType($leaveTypeId, $employeeId)
    {
		$leaveBalance = 0;
		$checkLeaveType = $this->getLeaveTypeRow($leaveTypeId);
		$leaveDurationDetails = array();

		/** get Leave Balance in leave form **/
		if(!empty($employeeId)&& !empty($leaveTypeId))
		{
			$leaveBalance 	 = $this->getEmpLeaveTypeBalance ($employeeId, $leaveTypeId);
			if(!empty($leaveBalance))
			{
				$leaveBalance = $leaveBalance;
				$leaveBalance = number_format((float)$leaveBalance,1, '.', '');
			}

			//Get the leave duration for a leave type
			$leaveDurationDetails = $this->getLeaveTypeDurationDetails(array($leaveTypeId),1);
		}

		$checkLeaveType['leaveBalance'] = $leaveBalance;
		$checkLeaveType['leaveTypeDuration'] = $leaveDurationDetails;
		return $checkLeaveType;	
    }
	
	/**
	 *	to list the unpaid leavetype in dropdown
	*/
    public function getUnpaidLeaveType()
    {
        return $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->leavetype, array('LeaveType_Id', 'Leave_Name'))
									  ->where('Leave_Type =?', "Unpaid Leave")
									  ->where('Leave_Status =?', 'Active')
									  ->order('Leave_Name ASC'));
    }

	/**
	 *	List leave type details
	*/	
    public function searchLeaveFreeze ($page, $rows, $sortField, $sortOrder, $searchAll, $searchArr,$userDetails)
    {
        /**
		 *	Sorting columns based on display column order in grid
		*/
		switch ($sortField)
		{
			case 1: $sortField = 'LT.Leave_Name'; break;
			case 2: $sortField = 'LF.Freeze_From'; break;
			case 3: $sortField = 'LF.Freeze_To'; break;
		}
		
		/**
		 *	Query to fetch data from various tables
		*/
        $qryLeaveFreeze = $this->_db->select()
								->from(array('LF'=>$this->_ehrTables->leaveFreeze),
									   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS LF.LeaveFreeze_Id as count'), 'LF.LeaveType_Id',
											 'LF.Added_By','LF.Updated_By','LF.LeaveFreeze_Id','Freeze_From','Freeze_To',
											 new Zend_Db_Expr("date_format(LF.Freeze_From, '".$this->_orgDF['sql']."') as vFreeze_From"),
											 new Zend_Db_Expr("date_format(LF.Freeze_To, '".$this->_orgDF['sql']."') as vFreeze_To"),
											 new Zend_Db_Expr("DATE_FORMAT(LF.Added_On,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
											 new Zend_Db_Expr("DATE_FORMAT(LF.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On")))
								
								->joinInner(array('LT'=>$this->_ehrTables->leavetype),'LT.LeaveType_Id=LF.LeaveType_Id',
										   array('Leave_Name'))
								
								->joinLeft(array('AE'=>$this->_ehrTables->empPersonal),'AE.Employee_Id=LF.Added_By',
										   array(new Zend_Db_Expr("CONCAT(AE.Emp_First_Name, ' ', AE.Emp_Last_Name) as Added_By")))
								
								->joinLeft(array('UE'=>$this->_ehrTables->empPersonal),'UE.Employee_Id=LF.Updated_By',
										   array(new Zend_Db_Expr("CONCAT(UE.Emp_First_Name, ' ', UE.Emp_Last_Name) as Updated_By")))
								
								->order("$sortField $sortOrder")
								->limit($rows, $page);
		
        /**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			$conditions = $this->_db->quoteInto('LT.Leave_Name Like ?', "%$searchAll%");			
			$conditions .= $this->_db->quoteInto('or LF.Freeze_From Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or LF.Freeze_To Like ?', "%$searchAll%");
			
			$qryLeaveFreeze->where($conditions);
		}
		
		if (! empty($searchArr['LeaveType_Id']))
		{
		       $qryLeaveFreeze->where('LF.LeaveType_Id Like?', $searchArr['LeaveType_Id']);
		}
		
		if ($searchArr['Freeze_From'] != '')
		{
			$qryLeaveFreeze->where($this->_db->quoteInto('LF.Freeze_From >= ?', $searchArr['Freeze_From']));
		}
		
		if ($searchArr['Freeze_To'] != '')
		{
			$qryLeaveFreeze->where($this->_db->quoteInto('LF.Freeze_To <= ?', $searchArr['Freeze_To']));
		}
			
			
			
		
		/**
		 * SQL queries
		 * Get data to display
		*/
		$leaveFreeze = $this->_db->fetchAll($qryLeaveFreeze);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		/* Total data set length */
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->leaveFreeze, new Zend_Db_Expr('COUNT(LeaveType_Id)')));		

		
		/**
		 * Output array with Json encode
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $leaveFreeze);
    }
    public function updateLeaveFreeze($leaveFreezeDetails,$sessionId,$customFormName)
    {
	$Conditions = $this->_db->quoteInto(new Zend_Db_Expr('Freeze_From BETWEEN ? '),$leaveFreezeDetails['Freeze_From']).
					$this->_db->quoteInto(new Zend_Db_Expr('AND ? '), $leaveFreezeDetails['Freeze_To']).' OR '.
					$this->_db->quoteInto(new Zend_Db_Expr('Freeze_To BETWEEN ? '), $leaveFreezeDetails['Freeze_From']).
					$this->_db->quoteInto(new Zend_Db_Expr('AND ?'), $leaveFreezeDetails['Freeze_To']);
		$Conditions .=' OR '.$this->_db->quoteInto('?',$leaveFreezeDetails['Freeze_From']);
		$Conditions .=$this->_db->quoteInto('?',new Zend_Db_Expr(' BETWEEN Freeze_From AND Freeze_To'));
		$Conditions .=' OR '.$this->_db->quoteInto('?',$leaveFreezeDetails['Freeze_To']);
		$Conditions .= $this->_db->quoteInto('?',new Zend_Db_Expr(' BETWEEN Freeze_From AND Freeze_To'));
		
		$qryLeaveFreeze = $this->_db->select()->from($this->_ehrTables->leaveFreeze, new Zend_Db_Expr('count(LeaveFreeze_Id)'))
                                        ->where("LeaveType_Id = ?",$leaveFreezeDetails['LeaveType_Id'])
					->where($Conditions);
					//->where("Freeze_From = ?",$leaveFreezeDetails['Freeze_From'])
					//->where("Freeze_To = ?",$leaveFreezeDetails['Freeze_To']);


         if (!empty($leaveFreezeDetails['LeaveFreeze_Id']))
                $qryLeaveFreeze->where('LeaveFreeze_Id != ?', $leaveFreezeDetails['LeaveFreeze_Id']);

        $leaveFreezeExists = $this->_db->fetchOne($qryLeaveFreeze);
        if(empty($leaveFreezeExists))
        {
                if(!empty($leaveFreezeDetails['LeaveFreeze_Id']))
                {
                        $action = 'Edit';
                        $leaveFreezeDetails['Updated_On'] = date('Y-m-d H:i:s');
                        $leaveFreezeDetails['Updated_By'] = $sessionId;
                        $leaveFreezeDetails['Lock_Flag']  = 0;
                        $updated = $this->_db->update($this->_ehrTables->leaveFreeze, $leaveFreezeDetails, array('LeaveFreeze_Id = '.$leaveFreezeDetails['LeaveFreeze_Id']));
                        $leaveFreezeId = $leaveFreezeDetails['LeaveFreeze_Id'];
                        
                }
                else
                {
                        $action = 'Add';
                        $leaveFreezeDetails['Added_On'] = date('Y-m-d H:i:s');
                        $leaveFreezeDetails['Added_By'] = $sessionId;
                        $updated =  $this->_db->insert($this->_ehrTables->leaveFreeze, $leaveFreezeDetails);
						$leaveFreezeId = $this->_db->lastInsertId();
                }
                return $this->_dbCommonFun->updateResult (array('updated'    => $updated,
								'action'         => $action,
								'trackingColumn' => $leaveFreezeId,
								'formName'       => $customFormName,
								'sessionId'      => $sessionId,
								//'comboPair'      => $this->getLeaveFreezePairs(),
								'tableName'      => $this->_ehrTables->leaveFreeze));
        }
        else
        {
            return array('success' => false, 'msg'=>'LeaveFreeze Already Exist', 'type'=>'info');
        } 
    }
    
    public function deleteLeaveFreeze($leaveFreezeId, $logEmpId,$customFormName)
    {
        
	
        if (!empty($leaveFreezeId))
        {
      	    $leaveFreezeLock = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->leaveFreeze, array('Lock_Flag', 'LeaveFreeze_Id'))
                                         ->where('LeaveFreeze_Id = ?', $leaveFreezeId));
			
		    if ($leaveFreezeLock['Lock_Flag'] == 0)
		    {
			    $deleted = $this->_db->delete($this->_ehrTables->leaveFreeze, 'LeaveFreeze_Id='.(int)$leaveFreezeId);
			    
                            return $this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
								    'tableName'      => $this->_ehrTables->leaveFreeze,
								    'lockFlag'       => $leaveFreezeLock['Lock_Flag'],
								    'formName'       => $customFormName,
								    'trackingColumn' => $leaveFreezeLock['LeaveFreeze_Id'],
								    'sessionId'      => $logEmpId,
							//		'comboPair'      => $this->getLeaveFreezePairs()
							));
		    }
	}
        else
        {
            return array('success'=>false, 'msg'=>'Unable to delete Leave Freeze. Please, contact system admin', 'type'=>'info');
        }
    }
	
	public function checkLeaveFreeze ($leaveTypeId, $leaveFrom, $leaveTo){
		$leaveFreezeDetails = $this->_db->fetchOne($this->_db->select()
								->from(array('leaveFreeze'=>$this->_ehrTables->leaveFreeze),array(new Zend_Db_Expr('COUNT(LeaveType_Id)')))							
								->where('leaveFreeze.LeaveType_Id = ?', $leaveTypeId));
		
		if(!empty($leaveFreezeDetails)){
			$Conditions = $this->_db->quoteInto(new Zend_Db_Expr('Freeze_From BETWEEN ? '),$leaveFrom).
					$this->_db->quoteInto(new Zend_Db_Expr('AND ? '), $leaveTo).' OR '.
					$this->_db->quoteInto(new Zend_Db_Expr('Freeze_To BETWEEN ? '), $leaveFrom).
					$this->_db->quoteInto(new Zend_Db_Expr('AND ?'), $leaveTo);
			$Conditions .=' OR '.$this->_db->quoteInto('?',$leaveFrom);
			$Conditions .=$this->_db->quoteInto('?',new Zend_Db_Expr(' BETWEEN Freeze_From AND Freeze_To'));
			$Conditions .=' OR '.$this->_db->quoteInto('?',$leaveTo);
			$Conditions .= $this->_db->quoteInto('?',new Zend_Db_Expr(' BETWEEN Freeze_From AND Freeze_To'));
												   
			$leaveFreezeDateExists = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->leaveFreeze, new Zend_Db_Expr('count(LeaveType_Id)'))							
							->where('LeaveType_Id = ?',$leaveTypeId)
                            ->where($Conditions));
							
			if(!empty($leaveFreezeDateExists)){				
				return 1;
			}
			else{
				return 0;
			}							
		}
		else{
			return 0;
		}
	}
	
	//to list leave type in leave import
    public function getEmpLeaveTypes()
    {
    	return $this->_db->fetchAll($this->_db ->select()->from($this->_ehrTables->leavetype,
																array('value'=> 'LeaveType_Id','text'=>'Leave_Name')));
    }
	
    public function checkShortTimeOff($fromDate,$toDate,$employeeId)
    {
		$dbHrReports   = new Reports_Model_DbTable_HrReports();
		$leavefromDate = date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($fromDate)));
		$leavetoDate   = date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($toDate)));
		$shortTimeOffExist = $dbHrReports->getShortTimeOffDetails($employeeId,$leavefromDate,$leavetoDate,'dashboardNoAttendance');
		if(!empty($shortTimeOffExist) && count($shortTimeOffExist) > 0)
		{
			return 1;
		}
		else
		{
			return 0;
		}
	
    }
	
	public function getTotalLeaves($employeeId,$salaryDate,$lastSalaryDate,$getLeaveType,$leaveId, $eligibleDaysCalc=NULL)
    {
		if(is_null($eligibleDaysCalc)){
			$approvalStatus = array('Applied', 'Approved', 'Cancel Applied');
		}else{			
			/* Leave eligible days calculation when eligible days based on period is monthly, quarterly, halfyearly */
			$approvalStatus = array('Applied', 'Approved', 'Cancel Applied', 'Returned');
		}
        /** If End date lies in next salary month **/
        $qryOverLapStartDate = $this->_db->select()->from(array('L'=>$this->_ehrTables->empLeaves), array('Start_Date','LeaveType_Id'))
                                                                                ->where('L.Approval_Status IN (?)', $approvalStatus)
                                                                               ->where('Employee_Id = ?', $employeeId)
                                                                                ->where('Start_Date >= ?', $salaryDate)
                                                                               ->where('Start_Date <= ?', $lastSalaryDate)
                                                                               ->where('End_Date > ?', $lastSalaryDate)
																			   ->where('LeaveType_Id in (?)', $getLeaveType);
		/** while edit leaves **/
		if(!empty($leaveId))
		{
			$qryOverLapStartDate ->where('Leave_Id != ?', $leaveId);
		}
        $overLapStartDate = $this->_db->fetchRow($qryOverLapStartDate);
        
        /** If start date lies in previous salary month **/        
        $qryOverLapEndDate = $this->_db->select()->from(array('L'=>$this->_ehrTables->empLeaves), array('End_Date','LeaveType_Id'))
                                                                        ->where('L.Approval_Status IN (?)', $approvalStatus)
                                                                       ->where('Employee_Id = ?', $employeeId)
                                                                        ->where('Start_Date < ?', $salaryDate)
                                                                        ->where('End_Date >= ?', $salaryDate)
                                                                       ->where('End_Date <= ?', $lastSalaryDate)
                                                                       ->where('LeaveType_Id in (?)', $getLeaveType);
		
		/** while edit leaves **/
		if(!empty($leaveId))
		{
			$qryOverLapEndDate ->where('Leave_Id != ?', $leaveId);
		}
		$overLapEndDate = $this->_db->fetchRow($qryOverLapEndDate);
                
        $qryTotalNumberOfDays = $this->_db->select()->from(array('L'=>$this->_ehrTables->empLeaves), array(new Zend_Db_Expr('SUM(Total_Days)')))
                                                                       ->where('L.Approval_Status IN (?)', $approvalStatus)
                                                                       ->where('Employee_Id = ?', $employeeId)
                                                                       ->where('Start_Date >= ?', $salaryDate)
                                                                       ->where('End_Date <= ?', $lastSalaryDate)
                                                                       ->where('LeaveType_Id in (?)', $getLeaveType);
        /** while edit leaves **/
		if(!empty($leaveId))
		{
			$qryTotalNumberOfDays ->where('Leave_Id != ?', $leaveId);
		}

        $totalNumberOfDays = $this->_db->fetchOne($qryTotalNumberOfDays);
		
		$empLeaveCalculationDays = $this->_db->fetchOne($this->_db->select()
								->from(array('leavetype'=>$this->_ehrTables->leavetype),array('Leave_Calculation_Days'))								
								->where('leavetype.LeaveType_Id = ?', $getLeaveType));		
		
		/* If start date overlapped */
		if(!empty($overLapStartDate))
        {
			/* Get the last date of overlapped start month based on the paycycle type */
			$monthOfOverlapStartDate = date('m',strtotime($overLapStartDate['Start_Date']));
			$yearOfOverlapStartDate = date('Y',strtotime($overLapStartDate['Start_Date']));
			
			$dbPayslip = new Payroll_Model_DbTable_Payslip();
			$overlapStartMonthPaycycleDates = $dbPayslip->getSalaryDateRange($monthOfOverlapStartDate, $yearOfOverlapStartDate,strtotime($overLapStartDate['Start_Date']));

			$endDate = $overlapStartMonthPaycycleDates['Last_SalaryDate'];

			$totalDays = $this->getLeaveDuration($getLeaveType,date('Y-m-d', strtotime($this->_ehrTables->changeDateformat($overLapStartDate['Start_Date']))),$endDate,$employeeId);
			$totalNumberOfDays += $totalDays;
		}
		/* If end date overlapped */
        if(!empty($overLapEndDate))
        {
			/* Get the start date of overlapped end month based on the paycycle type */
			$monthOfOverlapEndDate = date('m',strtotime($overLapEndDate['End_Date']));
			$yearOfOverlapEndDate = date('Y',strtotime($overLapEndDate['End_Date']));
			
			$dbPayslip = new Payroll_Model_DbTable_Payslip();		
			$overlapEndMonthPaycycleDates = $dbPayslip->getSalaryDateRange($monthOfOverlapEndDate, $yearOfOverlapEndDate,strtotime($overLapEndDate['End_Date']));

			$startDate = $overlapEndMonthPaycycleDates['Salary_Date'];
			
			$totalDays = $this->getLeaveDuration($getLeaveType,date('Y-m-d', strtotime($this->_ehrTables->changeDateformat($startDate))), $overLapEndDate['End_Date'],$employeeId);

			$totalNumberOfDays += $totalDays;
        }
		
        return $totalNumberOfDays;
    }

	/**
	 *	to get LeaveType_Id based Leave_Name
	*/
    public function getLeaveTypeId($leaveName)
    {
        if(!empty($leaveName)){
			return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->leavetype, 'LeaveType_Id')
														   ->where('Leave_Name = ?',$leaveName));
		} else{
			return NULL;
		}
		
    }
	
	/**
	 *	to get Reason_Id based ESIC_reason 
	*/
    public function getReasonId($esicReason)
    {
        return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->esicReason, 'Reason_Id')
														   ->where('ESIC_Reason LIKE ?', "%$esicReason%"));
    }
	
     public function listLeaveEnforcement ($page, $rows, $sortField, $sortOrder, $searchAll)
    {
        
         $existingLeaveEnforcementType = $this->getExistingLeaveEnforcementType();
         
         if(!empty($existingLeaveEnforcementType))
         {
            
         
            /**
              *	Sorting columns based on display column order in grid
             */
             switch ($sortField)
             {
                 case 0: $sortField = 'L.Leave_Name'; break;
             }
             
             /**
              *	Query to fetch data from various tables
             */
             $qryLeaveType = $this->_db->select()
                                     ->from(array('L'=>$this->_ehrTables->leavetype),
                                            array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS L.LeaveType_Id as count'), 'L.LeaveType_Id','L.Leave_Enforcement_Configuration',
                                                  'L.Leave_Name'))
                                     ->where('L.Leave_Enforcement_Configuration != ?', 1)
                                     ->group('L.LeaveType_Id')
                                     ->order("$sortField $sortOrder")
                                     ->limit($rows, $page);
             
             
              if(!empty($existingLeaveEnforcementType))
             {
                 $qryLeaveType->where('LeaveType_Id IN (?)',$existingLeaveEnforcementType);
             }
             
             /**
              *	Search All columns using single input
             */
             if (!empty($searchAll) && $searchAll != null)
             {
                 $conditions = $this->_db->quoteInto('L.Leave_Name Like ?', "%$searchAll%");
                 $qryLeaveType->where($conditions);
             }
             
             
             /**
              * SQL queries
              * Get data to display
             */
             $leaveType = $this->_db->fetchAll($qryLeaveType);
             
             /* Data set length after filtering */
             $iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
             
             /* Total data set length */
             $iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->leavetype, new Zend_Db_Expr('COUNT(LeaveType_Id)')));

			 foreach ($leaveType as $key => $row)
			{
				$leaveType[$key]['Used_Leave_Type'] = $this->getLeaveTypeUsed ($row['LeaveType_Id'],NULL);
			}
             /**
              * Output array with Json encode
             */
          return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $leaveType);
        }
        else
        {
            return array("iTotalRecords" => 0 , "iTotalDisplayRecords" => 0, "aaData" => array() );
    
		}
		
	}
	
	/** Find the total eligible days in a prorata basis for Service Based Leave II till the current leave closure year
	 * based on the employee experience */
	public function getExperienceLeave($employeeId,$leaveTypeId,$prorateFromDate,$startEndDate,$leaveAccrualEndDate=null)
	{
		/** Get the experience from and to date for an employee based on the experience slab */
		$experienceLeaveDate = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->empExperienceLeave,
								array(new Zend_Db_Expr("DATE_ADD('$prorateFromDate', INTERVAL Experience_From DAY) as ExperienceFrom"),
								new Zend_Db_Expr("DATE_ADD('$prorateFromDate', INTERVAL Experience_To DAY) as ExperienceTo"),'Total_Days',
								'Leave_Period' => new Zend_Db_Expr('CASE WHEN ABS(Experience_To - Experience_From) IS NULL 
								THEN 365 ELSE ABS(Experience_To - Experience_From) END')))
								->where('LeaveType_Id = ?',$leaveTypeId));	
	
	    $expLeaveDetails = array();
	
		$leaveStartDate = date('Y-m-d',strtotime($startEndDate['finstart']));
		$leaveEndDate   = date('Y-m-d',strtotime($startEndDate['finend']));
		foreach ($experienceLeaveDate as $key => $row)
		{
			/** If the experience to not exists then leave closure end date can be considered as the experience to */
			if($leaveStartDate > $row['ExperienceFrom'] && empty($row['ExperienceTo']))
			{
				$row['ExperienceTo'] = $leaveEndDate;
			}

			/** Validate the experience from and to date fall in between the leave closure start and end date */
			$expFrom = $this->isDateInRange($leaveStartDate,$leaveEndDate,$row['ExperienceFrom']);
			$expTo   = $this->isDateInRange($leaveStartDate,$leaveEndDate,$row['ExperienceTo']);
			
			if($expFrom==true && $expTo==true)
			{
				$expLeaveDetails[$key]['Start_Date'] = $row['ExperienceFrom'];
				$expLeaveDetails[$key]['End_Date'] = $row['ExperienceTo'];
				$expLeaveDetails[$key]['TotalDays'] = $row['Total_Days'];
				$expLeaveDetails[$key]['LeavePeriod'] = $row['Leave_Period'];
			}
			elseif($expFrom==true && $expTo==false)
			{
				$expLeaveDetails[$key]['Start_Date'] = $row['ExperienceFrom'];
				$expLeaveDetails[$key]['End_Date'] = $leaveEndDate;
				$expLeaveDetails[$key]['TotalDays'] = $row['Total_Days'];
				$expLeaveDetails[$key]['LeavePeriod'] = $row['Leave_Period'];
			}
			elseif($expFrom==false && $expTo==true)
			{
				$expLeaveDetails[$key]['Start_Date'] = $leaveStartDate;
				$expLeaveDetails[$key]['End_Date'] = $row['ExperienceTo'];
				$expLeaveDetails[$key]['TotalDays'] = $row['Total_Days'];
				$expLeaveDetails[$key]['LeavePeriod'] = $row['Leave_Period'];
			}
		}
		/** Calculate the leave accrual days for the service based leaveII if the leave accrual end date is given */
		if(!is_null($leaveAccrualEndDate) && !empty($leaveAccrualEndDate)){			
			$expNewLeaveDetails = array();
			
			/** Loop the experience details to calculate the accrual days till the given month end date */
			foreach ($expLeaveDetails as $key => $row)
			{
				/** If the leave accrual end date is greater than or equal to the experience from date.
				 *  Ex: While calculating the leave accrual for May,2020, 
				 * we will check 31 May 2020 >= emp experience start date (based on the exp slab)
				*/
				if(strtotime($leaveAccrualEndDate) >= strtotime($row['Start_Date'])){
					/** If the leave accrual end date is lesser than or equal to the experience to date.
					 *  Ex: While calculating the leave accrual for May,2020, 
					 * we will check 31 May 2020 <= emp experience to date (based on the exp slab) then experience to
					 * is the leave accrual end date
					*/
					if(strtotime($leaveAccrualEndDate) <= strtotime($row['End_Date'])){
						$newAccCalEndDate = $leaveAccrualEndDate;
					}else{
						$newAccCalEndDate = $row['End_Date'];
					}

					$expNewLeaveDetails[] = array('Start_Date' => $row['Start_Date'],
													'End_Date' => $newAccCalEndDate,
													'TotalDays' => $row['TotalDays'],
													'LeavePeriod' => $row['LeavePeriod']);
				}
			}
		}else{
			$expNewLeaveDetails = $expLeaveDetails;
		}
		$days = 0;
		/** Iterate in a loop to calculate leave eligible days for the employee experience */
		foreach ($expNewLeaveDetails as $tempLeaveDetails)
		{
			/** Calculate the leave eligible days based on the prorata basis */
			$days += $this->getExperienceBasedEligityDays($tempLeaveDetails['Start_Date'],$tempLeaveDetails['End_Date'],$tempLeaveDetails['TotalDays'],$tempLeaveDetails['LeavePeriod']);
		}
		return $days; 
	}

	public function getExperienceBasedEligityDays($startDate,$endDate,$lvTypeTotDays,$leavePeriod)
	{
		$strStart=date_create($startDate);
		$strEnd=date_create($endDate);
		$numOfDays=date_diff($strStart,$strEnd);
		$numOfDays = $numOfDays->format("%a"); // we should not include the end date in the diff

		$eligDays = ($lvTypeTotDays /$leavePeriod) * $numOfDays;

		return intval(($eligDays*2)+0.5) / 2;
	}

	function isDateInRange($leaveStartDate,$leaveEndDate,$experienceDate)
    {
		$startT = strtotime($leaveStartDate);
		$endT = strtotime($leaveEndDate);
		$userT = strtotime($experienceDate);
		return (($userT >= $startT)&&($userT <= $endT));
    }
 
    public function getMonths()
    {
      return $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->month,
                                                array('Month_Id','Month_Name')));
    }
    
    /**
	 *	to list the leavetype in dropdown
	*/
    public function getLeaveEnforcementType()
    {
		$enforcementConfiguration = array(2,3,4,5);
        $qryLeaveType =  $this->_db->select()->from($this->_ehrTables->leavetype, array('LeaveType_Id', 'Leave_Name'))
								      ->where('Leave_Enforcement_Configuration IN (?)',$enforcementConfiguration)
                                      ->where('Leave_Status = ?', 'Active')
                                      ->order('Leave_Name ASC');
        
        $existingLeaveEnforcementType = $this->getExistingLeaveEnforcementType();
        
        if(!empty($existingLeaveEnforcementType))
        {
            $qryLeaveType->where('LeaveType_Id NOT IN (?)',$existingLeaveEnforcementType);
        }
       
        $leaveEnforcementType = $this->_db->fetchPairs($qryLeaveType); 
     
        return $leaveEnforcementType;
    }
    
    public function searchEnforcementType($leaveTypeId)
    {
        $qryLeaveType = $this->_db->select()->from(array('L'=>$this->_ehrTables->leavetype),
									   array('L.LeaveType_Id','L.Leave_Enforcement_Configuration'))
							    ->where('L.LeaveType_Id = ?',$leaveTypeId);
                                      	
        $leaveTypeDetails = $this->_db->fetchRow($qryLeaveType);
        
        return $leaveTypeDetails; 
    }
    public function getExistingLeaveEnforcementType()
    {
        $serviceLeaveTypeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empServiceLeave, array('LeaveType_Id'))
                                            ->group('LeaveType_Id'));
        
        $quarterLeaveTypeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->leaveQuarter, array('LeaveType_Id'))
                                            ->group('LeaveType_Id'));
        
        $slabLeaveTypeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->maternitySlab, array('LeaveType_Id'))
											->group('LeaveType_Id'));
		
		/**to load leave type in the grid table when added,edited and deleted**/
		$experienceLeaveTypeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empExperienceLeave, array('LeaveType_Id'))
                                            ->group('LeaveType_Id'));
        
        $array = array_merge($serviceLeaveTypeId,$quarterLeaveTypeId);
		$existingLeaveEnforcement = array_merge($array,$slabLeaveTypeId);
		$existingLeaveEnforcementType = array_merge($existingLeaveEnforcement,$experienceLeaveTypeId);
		
       
       return $existingLeaveEnforcementType;
    }
    
    public function deleteLeaveEnforcement($uniqueId,$enforcementConfiguration,$logEmpId)
    {
		$dbMaternityleave = new Employees_Model_DbTable_MaternitySlabs();  
		$dbExperienceLeave = new Employees_Model_DbTable_ExperienceBasedLeave();  

        if($enforcementConfiguration == 2)
        {
            $leaveTypeId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empServiceLeave,array('LeaveType_Id'))
                                                                            ->where("Service_Id = ?",$uniqueId));
             
            
            $leaveTypeIdCount = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empServiceLeave,new Zend_Db_Expr('count(Service_Id)'))
																			->where("LeaveType_Id = ?",$leaveTypeId));
																			
            $fromName = 'Employee Service'; 
            $tableName = $this->_ehrTables->empServiceLeave;
     
			//delete the eligible days of serviced based leave types
			$employeeLeaves = $this->_db->fetchOne($this->_db->select()
                             ->from(array('leave'=>$this->_ehrTables->empLeaves), new Zend_Db_Expr('Count(LeaveType_Id)'))
                           	 ->where('leave.LeaveType_Id = ?',$leaveTypeId));

			if($employeeLeaves==0)
			{
				$deleted=$this->_dbServiceLeave->deleteEligibleServiceLeave($uniqueId,$leaveTypeId);
				if($deleted == 0)
				{
					return array('success'=>false, 'msg'=>'Please delete the maximum service range for this leave type', 'type'=>'info');
				}
			}
			else
			{	
				return array('success'=>false, 'msg'=>'Employee already added a leave for this leave type. Please delete that record', 'type'=>'info');
			}	
		}
        
        if($enforcementConfiguration == 3)
        {
            $leaveTypeId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->leaveQuarter,array('LeaveType_Id'))
                                                                            ->where("Leave_Quarter_Id = ?",$uniqueId));
             
            
            $leaveTypeIdCount = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->leaveQuarter,new Zend_Db_Expr('count(Leave_Quarter_Id)'))
                                                                            ->where("LeaveType_Id = ?",$leaveTypeId));
           
            
            $fromName = 'Leave Quarter'; 
            $tableName = $this->_ehrTables->leaveQuarter;
			
			$employeeLeaves = $this->_db->fetchOne($this->_db->select()
                             ->from(array('leave'=>$this->_ehrTables->empLeaves), new Zend_Db_Expr('Count(LeaveType_Id)'))
                           	 ->where('leave.LeaveType_Id = ?',$leaveTypeId));

			if($employeeLeaves==0)
			{
				$deleted=$this->_dbQuaterLeave->deleteEligibleQuarterLeave($uniqueId,$leaveTypeId);
				if($deleted == 0)
				{
					return array('success'=>false, 'msg'=>'Please delete the maximum quarter range for this leave type', 'type'=>'info');
				}
			}	
			else
			{	
				return array('success'=>false, 'msg'=>'Employee already added a leave for this leave type. Please delete that record', 'type'=>'info');
			}

        }
        
        if($enforcementConfiguration == 4)
        {
             $leaveTypeId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->maternitySlab,array('LeaveType_Id'))
                                                                            ->where("Maternity_Slab_Id = ?",$uniqueId));
             
            
            $leaveTypeIdCount = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->maternitySlab,new Zend_Db_Expr('count(Maternity_Slab_Id)'))
                                                                            ->where("LeaveType_Id = ?",$leaveTypeId));
           
            
            $fromName = 'Maternity Slab';
			$tableName = $this->_ehrTables->maternitySlab;
			//delete the eligible days of maternitySlab based leave types
			$employeeLeaves = $this->_db->fetchOne($this->_db->select()
                             ->from(array('leave'=>$this->_ehrTables->empLeaves), new Zend_Db_Expr('Count(LeaveType_Id)'))
                           	 ->where('leave.LeaveType_Id = ?',$leaveTypeId));

			if($employeeLeaves==0)
			{
				$deleted=$dbMaternityleave->deleteEligibleMaternityLeave($uniqueId,$leaveTypeId);
				if($deleted == 0)
				{
					return array('success'=>false, 'msg'=>'Please delete the maximum maternity range for this leave type', 'type'=>'info');
				}
			}	
			else
			{	
				return array('success'=>false, 'msg'=>'Employee already added a leave for this leave type. Please delete that record', 'type'=>'info');
			}
			
		}
		if($enforcementConfiguration == 5)
        {
            $leaveTypeId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empExperienceLeave,array('LeaveType_Id'))
                                                                            ->where("Experience_Id = ?",$uniqueId));
             
            
            $leaveTypeIdCount = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empExperienceLeave,new Zend_Db_Expr('count(Experience_Id)'))
																			->where("LeaveType_Id = ?",$leaveTypeId));
																			
            $fromName = 'Employee Experience'; 
            $tableName = $this->_ehrTables->empExperienceLeave;
     
			//delete the eligible days of Experience based leave types
			$employeeLeaves = $this->_db->fetchOne($this->_db->select()
                             ->from(array('leave'=>$this->_ehrTables->empLeaves), new Zend_Db_Expr('Count(LeaveType_Id)'))
                           	 ->where('leave.LeaveType_Id = ?',$leaveTypeId));

			if($employeeLeaves==0)
			{
				$deleted=$dbExperienceLeave->deleteEligibleExperienceLeave($uniqueId,$leaveTypeId);
				if($deleted == 0)
				{
					return array('success'=>false, 'msg'=>'Please delete the maximum Experience range for this leave type', 'type'=>'info');
				}
			}
			else
			{	
				return array('success'=>false, 'msg'=>'Employee already added a leave for this leave type. Please delete that record', 'type'=>'info');
			}	
		}
            $result = $this->_dbCommonFun->deleteRecord (array( 'deleted'        => $deleted,
                                                                'tableName'      => $tableName,
                                                                'lockFlag'       => 0,
                                                                'formName'       => $fromName,
                                                                'trackingColumn' => $uniqueId,
                                                                'sessionId'      => $logEmpId));
		  
          $result['count'] = $leaveTypeIdCount;
          return $result;
	}

	/* Calculate the maximum eligibility days an employee can apply 
	1. if the eligible days for a leave type is monthly or quarterly or halfyearly
	2. if the accumulate eligible days is enabled 
	3. if the leaves lies in same months/quarters */
	public function calcEligibleDaysForSamePeriodLeaves($eligibleDaysForaPeriod, $employeeId, $leaveType, $leaveId, 
		$leaveReqQuarterStartDate, $leavePeriod,$totalDays, $totalLeavesTaken, $totalLeaveTakenRequestDays,$empTotCoDays,$accumulateFromDate,$leaveQuarterEndDate,$leaveClosureEndDate,$leavePeriodStartEndDates,$currentDate, $leaveClosureStartDate,$yearlyLeaveBalance){
		if(strtotime(date('Y-m-d')) >= strtotime($accumulateFromDate)){
			/* Get the previous month accumulated days */
			$previousPeriodAccumulatedDays = $this->previousPeriodAccumulatedDays(
				$eligibleDaysForaPeriod, $employeeId,$leaveType, $leaveId, 
				$leaveReqQuarterStartDate, $leavePeriod, $accumulateFromDate,$currentDate,$leavePeriodStartEndDates);
		}else{
			$previousPeriodAccumulatedDays = 0;
		}

		//Calculate leave applying quarter month eligible days.
		$leaveAppplicableForLeaveAppliedQuarter = $this->isEligibleDaysAppliedForLeaveAppliedPeriod($leaveQuarterEndDate,$currentDate,$leavePeriodStartEndDates,$leaveClosureStartDate,$leaveClosureEndDate);
		
		if($leaveAppplicableForLeaveAppliedQuarter !== 1){
			$currentQuarterApplicableDays = 0;
		}else{
			$currentQuarterApplicableDays = $eligibleDaysForaPeriod;
		}
		$futureMonthsRemainingLeaves = $this->calculateFutureMonthEligibleLeaves($employeeId,$leaveQuarterEndDate,$leaveClosureEndDate,$leaveType,$leaveId,$leavePeriodStartEndDates,$eligibleDaysForaPeriod,$leaveClosureStartDate);
		
		/* Sum the previous months eligible days and Eligible days applicable for current month and the employee carryover balance*/
		$totalEligibleDaysForQuarter = ($currentQuarterApplicableDays + $previousPeriodAccumulatedDays + $empTotCoDays) + $futureMonthsRemainingLeaves;
		
		//If the total eligibility days exceed the employee actual leave balance including remaining CO Days
		if($totalEligibleDaysForQuarter > $yearlyLeaveBalance){
			$totalEligibleDaysForQuarter = $yearlyLeaveBalance;
		}
		/* Total Days = total days for current leave request + leaves taken in that leave request month
		totalEligibleDaysForQuarter = previous quarter eligible days + Eligible days applicable for current quarter */
		if($totalDays > $totalEligibleDaysForQuarter){
			$leavePeriodinWords = ($leavePeriod == 1) ? 'month' : (($leavePeriod == 3) ? ('quarter') : ('half-year'));

			$leavePeriodStartDate = $totalLeaveTakenRequestDays[0]['leavePeriodDates']['leavePeriodStartDate'];
			$leavePeriodEndDate = $totalLeaveTakenRequestDays[0]['leavePeriodDates']['leavePeriodEndDate'];

			/* If total eligibility days for a leave period is greater than or equal to totalLeavesTaken in the leave period,
			then calculate the remaining days that the employee can apply for this leave type in the leave period. */
			if($totalEligibleDaysForQuarter > $totalLeavesTaken){
				$availableDays = $totalEligibleDaysForQuarter-$totalLeavesTaken;
				$responseMessage = "You have only ".$availableDays." days available from ".$leavePeriodStartDate." to ".$leavePeriodEndDate.".";
			}else{
				/* Employee have already taken eligible days in the leave period */				
				$responseMessage = "Your leave quota from ".$leavePeriodStartDate." to ".$leavePeriodEndDate." has been completed.";
			}

			return $responseMessage;
		}else{
			return null;
		}
	}

	/* Calculate the maximum eligibility days an employee can apply 
	1. if the eligible days for a leave type is monthly or quarterly or halfyearly
	2. if the accumulate eligible days is enabled 
	3. if the leaves lies in different months/quarters */
	public function calcEligibleDaysForDiffPeriodLeaves($eligibleDaysForaPeriod, $employeeId, $leaveType, $leaveId, 
	$leaveReqQuarterStartDate, $leavePeriod, $totalLeaveTakenRequestDays,$duration,$empTotCoDays,$accumulateFromDate,$leaveClosureEndDate,$leavePeriodStartEndDates,$currentQuarterStartEndDates,$currentDate,$leaveClosureStartDate,$yearlyLeaveBalance
	){
		$isQuarterAppliedDaysValid = $leaveQuarterMonths = $allQuarterLeavesTaken = $previousQuarterLeaveRequestDays = 0;
		$leavePeriodinWords = ($leavePeriod == 1) ? 'month' : (($leavePeriod == 3) ? 'quarter' : 'half-year');
		$responseMessage = null;
		if(strtotime(date('Y-m-d')) >= strtotime($accumulateFromDate)){
			/* Get the accumulated days till the previous quarter(previous quarter of the leave from date) */
			$accumulatedDaysTillPreviousQuarter = $this->previousPeriodAccumulatedDays($eligibleDaysForaPeriod, $employeeId,			
				$leaveType, $leaveId, $leaveReqQuarterStartDate, $leavePeriod, $accumulateFromDate,$currentDate,$leavePeriodStartEndDates);	
		}else{
			$accumulatedDaysTillPreviousQuarter = 0;
		}
		$leavePeriodActualEndDateArray = $currentQuarterStartEndDates[count($currentQuarterStartEndDates)-1];
		$leavePeriodActualEndDate = $leavePeriodActualEndDateArray[count($leavePeriodActualEndDateArray)-1];
		
		$futureMonthsRemainingLeaves = $this->calculateFutureMonthEligibleLeaves($employeeId,$leavePeriodActualEndDate,$leaveClosureEndDate,$leaveType,$leaveId,$leavePeriodStartEndDates,$eligibleDaysForaPeriod,$leaveClosureStartDate);

		$quarterCount = count($totalLeaveTakenRequestDays);
		for($i=0; $i<$quarterCount; $i++){
			$leaveQuarterMonths += 1;

			$leavePeriodStartDate = $totalLeaveTakenRequestDays[$i]['leavePeriodDates']['leavePeriodStartDate'];
			$totEligDlLeavePeriodEndDate = $leavePeriodEndDate = $totalLeaveTakenRequestDays[$i]['leavePeriodDates']['leavePeriodEndDate'];

			//Calculate leave applying quarter month eligible days.
			$leaveAppplicableForLeaveAppliedQuarter = $this->isEligibleDaysAppliedForLeaveAppliedPeriod($leavePeriodEndDate,$currentDate,$leavePeriodStartEndDates,$leaveClosureStartDate,$leaveClosureEndDate);
			if($leaveAppplicableForLeaveAppliedQuarter !== 1){
				$currentQuarterApplicableDays = 0;
			}else{
				$currentQuarterApplicableDays = $eligibleDaysForaPeriod;
			}

			if($i == 0){
				$totEligDlLeavePeriodStartDate = $leavePeriodStartDate;
				$quarterEligibleDays = $currentQuarterApplicableDays;
			}else{
				$quarterEligibleDays += $currentQuarterApplicableDays;
			}

			/* Sum of the leaves taken of the quarters */
			$allQuarterLeavesTaken += $totalLeaveTakenRequestDays[$i]['leavesTaken'];

			if($i == ($quarterCount-1)){
				if(!is_null( $duration ) && $duration > 0){
					$empEligibleDaysCOBalTillCurQuarter = $accumulatedDaysTillPreviousQuarter+$quarterEligibleDays + $empTotCoDays;
					//If the total eligibility days exceed the employee actual leave balance including remaining CO Days
					if($empEligibleDaysCOBalTillCurQuarter > $yearlyLeaveBalance){
						$empEligibleDaysCOBalTillCurQuarter = $yearlyLeaveBalance;
					}
					/** Each quarter total eligible days + carryover balance - Each quarter total leaves taken */
					$empAvailableEligibleDays = ($empEligibleDaysCOBalTillCurQuarter - $allQuarterLeavesTaken) + $futureMonthsRemainingLeaves;
					
					/* If the actual available days till the current quarter is less than the applied duration */
					if($empAvailableEligibleDays < $duration){
						if($empAvailableEligibleDays <= 0){
							$responseMessage = "Your leave quota from ".$totEligDlLeavePeriodStartDate." to ".$totEligDlLeavePeriodEndDate." has been completed.";
						}else{
							/* message for the available days in a quarter */
							$responseMessage = "You have only ".$empAvailableEligibleDays." days available from ".$totEligDlLeavePeriodStartDate." to ".$totEligDlLeavePeriodEndDate.".";
						}
					}
				}
			}
		}
		return $responseMessage;
	}

	public function isEligibleDaysAppliedForLeaveAppliedPeriod($leaveAppliedQuarterEndDate,$currentDate,$leavePeriodStartEndDates,$leaveClosureStartDate,$leaveClosureEndDate){
		$leaveAppplicable = 0;
		$currentDateTimeStamp = strtotime($currentDate);
		$leaveClosureEndDateTimestamp=strtotime($leaveClosureEndDate);
		$leaveClosureStartDateTimestamp=strtotime($leaveClosureStartDate);

		if( $currentDateTimeStamp >  $leaveClosureEndDateTimestamp){
			$currentDateTimeStamp = $leaveClosureEndDateTimestamp;
		} else if( $currentDateTimeStamp <  $leaveClosureStartDateTimestamp){
			$currentDateTimeStamp = $leaveClosureStartDateTimestamp;
		}
		$currentQuarterDates = array_filter($leavePeriodStartEndDates, function($value) use ($currentDateTimeStamp){
			return ($currentDateTimeStamp >= strtotime($value["0"]) && $currentDateTimeStamp <= strtotime($value["1"]));
		},ARRAY_FILTER_USE_BOTH);
		$currentQuarterLeavePeriodKey = array_keys($currentQuarterDates);
		$currentQuarterLeavePeriodKey = $currentQuarterLeavePeriodKey[0];
		
		//Validate the leave applying month exceeds the current month or not
		if(!empty($currentQuarterDates) && count($currentQuarterDates) >0){
			$currentQuarterDates=$currentQuarterDates[$currentQuarterLeavePeriodKey];
			if(strtotime($leaveAppliedQuarterEndDate) <= strtotime($currentQuarterDates[1])){
				$leaveAppplicable = 1;
			}
		}
		return $leaveAppplicable;
	}
	
	/* Calculate eligible days till the previous month */
	public function previousPeriodAccumulatedDays($eligibleDaysForaPeriod, $employeeId, $leaveType, $leaveId, $leavefromDate, $leavePeriod, $accumulateFromDate,$currentDate,$leavePeriodStartEndDates){
		$startEndDate = $this->getLeaveClosureDetails($employeeId, $leaveType);

		/* Leave closure start date */
		$leaveClosureStartDate = date('Y-m-d',strtotime($startEndDate['finstart']));
		/* Leave closure end date */
		$leaveClosureEndDate   = date('Y-m-d', strtotime($startEndDate['finend']));
		
		/*Get the previous quarter end date from the leave applying quarter start date. */
		$prevMonLastDate =  date('Y-m-d', strtotime("-1 day", strtotime($leavefromDate)));

		/* If the leave is applied on the leave closure start month, previous months eligible days is 0 */
		if(strtotime($prevMonLastDate) < strtotime($leaveClosureStartDate)){
			return 0;	
		}else{
			$previousLeavesStartMonth = '';
			/* If date of join lies in between leave closure start and end date */
			if(strtotime($accumulateFromDate) >= strtotime($leaveClosureStartDate) && 
			strtotime($accumulateFromDate) <= strtotime($leaveClosureEndDate)){
				$previousLeavesStartMonth = $accumulateFromDate;
			}
			else if(strtotime($accumulateFromDate) < strtotime($leaveClosureStartDate)){
				/* If date of join is less than the leave closure start date */
				$previousLeavesStartMonth = $leaveClosureStartDate;
			}
			/* If previous start month is not empty */
			if(!empty($previousLeavesStartMonth)){
				/* prevleaveperiodlastdate should be greater than or equal to 
				previous leaves start month(date of join / leave closure start date) */
				if(strtotime($prevMonLastDate) >= strtotime($previousLeavesStartMonth)){
					/* Get the total days from leave closure start date/DOJ to previous month of the leave requisition start date. */
					$prevTotalNumberOfLeavesTaken = $this->getTotalLeaves($employeeId,$previousLeavesStartMonth,$prevMonLastDate,$leaveType,$leaveId, 1);
					
					/* Get the total number of months from the first day of the (leave closure start date/ date of join) to 
					last day of the previous quarter end date */
					$totalNoOfPrevMonths =0;
					$previousLeavesStartMonthTimeStamp = strtotime($previousLeavesStartMonth);
					$currentDateTimeStamp = strtotime($currentDate);
                    for($i=0;$i<=count($leavePeriodStartEndDates);$i++){
						$leavePeriodStartDateTimeStamp = strtotime($leavePeriodStartEndDates[$i][0]);
						$leavePeriodEndDateTimeStamp = strtotime($leavePeriodStartEndDates[$i][1]);
						if($totalNoOfPrevMonths > 0 || (($previousLeavesStartMonthTimeStamp >= $leavePeriodStartDateTimeStamp) &&
						($previousLeavesStartMonthTimeStamp <= $leavePeriodEndDateTimeStamp))){
							if(($currentDateTimeStamp >= $leavePeriodStartDateTimeStamp) && ($currentDateTimeStamp <= $leavePeriodEndDateTimeStamp)){
								break;
							}else{
								$totalNoOfPrevMonths += 1;
							}
						}
					}

					/* If leave period is monthly */
					if($leavePeriod == 1){
						/* Total Eligibility days from the leave closure start date/ date of join to 
						previous month of the leave requisition start date */
						$totalEligibilityDaysOfPrevMonths = $eligibleDaysForaPeriod * $totalNoOfPrevMonths;
					}else if($leavePeriod == 3){
						/* Get the number of previous quarters */

						if(in_array($totalNoOfPrevMonths,array(1,2,3))){
							$totalNoOfPrevQuarters = 1;
						}else if(in_array($totalNoOfPrevMonths,array(4,5,6))){
							$totalNoOfPrevQuarters = 2;
						}else if(in_array($totalNoOfPrevMonths,array(7,8,9))){
							$totalNoOfPrevQuarters = 3;
						}else{
							$totalNoOfPrevQuarters = 4;
						}

						/* Total Eligibility days from the leave closure start date/ date of join to 
						previous quarter */
						$totalEligibilityDaysOfPrevMonths = $eligibleDaysForaPeriod * $totalNoOfPrevQuarters;
					}else if($leavePeriod == 6){
						/* Get the number of previous quarters */
						if(in_array($totalNoOfPrevMonths,array(1,2,3,4,5,6))){
							$totalNoOfPrevQuarters = 1;
						}else{
							$totalNoOfPrevQuarters = 2;
						}

						/* Total Eligibility days from the leave closure start date/ date of join to 
						previous quarter */
						$totalEligibilityDaysOfPrevMonths = $eligibleDaysForaPeriod * $totalNoOfPrevQuarters;
					}

					//Get the applied, approved, returned, canceled applied leaves taken for the preivous leave period
					$prevTotalNumberOfLeavesTaken = empty($prevTotalNumberOfLeavesTaken) ? 0 : $prevTotalNumberOfLeavesTaken;
					$previousLeavePeriodActualLeavesTaken = $prevTotalNumberOfLeavesTaken;
					
				
					/* If total number of leaves taken for previous months is zero */
					if(empty($previousLeavePeriodActualLeavesTaken)){
						return $totalEligibilityDaysOfPrevMonths;
					}else{
						/** User may also have taken leaves more than prev months eligible days incase if the 
						 * carryover balance exist. So previous leave quarter accumulated days may also be in negative here.
						 */
						return $totalEligibilityDaysOfPrevMonths - $previousLeavePeriodActualLeavesTaken;
					}
				}else{
					return 0;
				}
			}else{
				return 0;
			}
		}
	}

	/** Get the remaining co days when the leave period restriction is enabled and accumulation disabled */
	public function getRemCODaysWithoutAccumulation($valDetails){
		$payslipDb = new Payroll_Model_DbTable_Payslip();

		$startEndDate = $valDetails['Leave_Closure_StartEndDate'];
		$curQuarterStartDate = $valDetails['Current_Quarter_Start_Date'];
		$dateOfJoin = $valDetails['DOJ'];
		$leaveTypeId = $valDetails['LeaveType_Id'];
		$leaveId = $valDetails['Leave_Id'];
		$eligibleDaysForaPeriod = $valDetails['Eligible_Days_Based_On_Period'];
		$empTotCoDays = $valDetails['Tot_CO_Days'];
		$employeeId = $valDetails['Employee_Id'];

		/* Leave closure start date */
		$leaveClosureStartDate = date('Y-m-d',strtotime($startEndDate['finstart']));
		/* Leave closure end date */
		$leaveClosureEndDate   = date('Y-m-d', strtotime($startEndDate['finend']));
		
		/*Get the previous quarter end date from the leave applying quarter start date. */
		$prevMonLastDate =  date('Y-m-d', strtotime("-1 day", strtotime($curQuarterStartDate)));

		/* If the leave is applied on the leave closure start month, availed co days is 0 */
		if(strtotime($prevMonLastDate) < strtotime($leaveClosureStartDate)){
			$empAvailedCODays =  0;	
		}else{
			$actLeavesStartMonth = '';

			/* If date of join lies in between leave closure start and end date */
			if(strtotime($dateOfJoin) >= strtotime($leaveClosureStartDate) && 
			strtotime($dateOfJoin) <= strtotime($leaveClosureEndDate)){
				$dateOfJoinStr = strtotime($dateOfJoin);
				$dateOfJoinYear = date('Y',$dateOfJoinStr);
				$dateOfJoinMonth = date('m',$dateOfJoinStr);

				$dojPaycycleDates = $payslipDb->getSalaryDateRange($dateOfJoinMonth, $dateOfJoinYear, $dateOfJoinStr);
				$actLeavesStartMonth = $dojPaycycleDates['Salary_Date'];
			}
			else if(strtotime($dateOfJoin) < strtotime($leaveClosureStartDate)){
				/* If date of join is less than the leave closure start date */
				$actLeavesStartMonth = $leaveClosureStartDate;
			}

			/* If previous start month is not empty */
			if(!empty($actLeavesStartMonth)){
				$actLeavesStartMonthStr = strtotime($actLeavesStartMonth);
				$curQuarterStartDateStr = strtotime($curQuarterStartDate);

				$empAvailedCODays = 0;

				/** Iterate the loop from the employee DOJ paycycle month or leave closure start month
				 * till the previous quarter to find the availed co days. We should not use the table to 
				 * get the carryover balance as the balance will be updated only when the leave is approved
				 */
				while($actLeavesStartMonthStr < $curQuarterStartDateStr){
					//Next leave month start date
					$nextLeaveMonStartDate =  date('Y-m-d', strtotime("+1 month", $actLeavesStartMonthStr));
					//Actual leave month last date
					$leaveMonLastDate =  date('Y-m-d', strtotime("-1 day", strtotime($nextLeaveMonStartDate)));

					/* Get the total number of leaves taken for the actual leave month */
					$LvMonthTotNoLvTaken = $this->getTotalLeaves($employeeId,$actLeavesStartMonth,
														$leaveMonLastDate,$leaveTypeId,$leaveId, 1);

					/** If the leaves taken is greater than actual eligible days */
					if($eligibleDaysForaPeriod < $LvMonthTotNoLvTaken){
						$totExcessLvTaken = $LvMonthTotNoLvTaken - $eligibleDaysForaPeriod;
						$empAvailedCODays += $totExcessLvTaken;
					}
					//assign next month start date to the actual leave start date
					$actLeavesStartMonthStr = strtotime($nextLeaveMonStartDate); 
				}
			}else{
				$empAvailedCODays = 0;
			}
		}

		/** Get the current year remaining co days.
		 * If employee total co days is greater than the availed co days
		 */
		if($empTotCoDays > $empAvailedCODays){
			$empRemCoDays = $empTotCoDays - $empAvailedCODays;
		}else{
			$empRemCoDays = 0;
		}

		return $empRemCoDays;
	}

	
	//Function to calculate future month leave taken when accumulate eligible days is enabled.
	public function calculateFutureMonthEligibleLeaves($employeeId,$leaveAppliedQuarterEndDate,$leaveClosureEndDate,$leaveType,$leaveId,$leavePeriodStartEndDates,$eligibleDaysForaPeriod,$leaveClosureStartDate){
		$leaveAppliedQuarterDates = array_filter($leavePeriodStartEndDates, function($value) use ($leaveAppliedQuarterEndDate){
			return $value["1"] == $leaveAppliedQuarterEndDate;
		},ARRAY_FILTER_USE_BOTH);$leaveAppliedQuarterDates = array_filter($leavePeriodStartEndDates, function($value) use ($leaveAppliedQuarterEndDate){
			return $value["1"] == $leaveAppliedQuarterEndDate;
		},ARRAY_FILTER_USE_BOTH);
		$leavePeriodKey = array_keys($leaveAppliedQuarterDates);
		$leavePeriodKey = (!empty($leavePeriodKey)) ? $leavePeriodKey[0] : '';
		$nextLeavePeriodStartDate =  date('Y-m-d', strtotime("+1 day", strtotime($leaveAppliedQuarterEndDate)));
		//If the next leave period(monthly,quarterly,halfyearly) start date does not fall in the next leave year
		if(strtotime($nextLeavePeriodStartDate) < strtotime($leaveClosureEndDate)){
			$currentDateTimeStamp=strtotime(date('Y-m-d'));
			$leaveClosureEndDateTimestamp=strtotime($leaveClosureEndDate);
			$leaveClosureStartDateTimestamp=strtotime($leaveClosureStartDate);
	
			if( $currentDateTimeStamp >  $leaveClosureEndDateTimestamp){
				$currentDateTimeStamp = $leaveClosureEndDateTimestamp;
			} else if( $currentDateTimeStamp <  $leaveClosureStartDateTimestamp){
				$currentDateTimeStamp = $leaveClosureStartDateTimestamp;
			}
			$currentLeavePeriodDates = array_filter($leavePeriodStartEndDates, function($value) use ($currentDateTimeStamp){
				$leavePeriodStartDateTimeStamp = strtotime($value[0]);
				$leavePeriodEndDateTimeStamp = strtotime($value[1]);
				return (($currentDateTimeStamp >= $leavePeriodStartDateTimeStamp) 
				&& ($currentDateTimeStamp <= $leavePeriodEndDateTimeStamp));
			},ARRAY_FILTER_USE_BOTH);		
			//If the current month falls in the current leave closure year, get the future leave period date range till current month
			if(!empty($currentLeavePeriodDates)){
				$currentLeavePeriodKey = array_keys($currentLeavePeriodDates);
				$currentLeavePeriodKey = $currentLeavePeriodKey[0];
				
				$futureLeavePeriodDates= array_slice($leavePeriodStartEndDates,$leavePeriodKey+1,null,true);
				
				foreach($futureLeavePeriodDates as $key=>$row){
					if($key > $currentLeavePeriodKey){
						unset($futureLeavePeriodDates[$key]);
					}
				}
			}else{
				$futureLeavePeriodDates= array_slice($leavePeriodStartEndDates,$leavePeriodKey+1);
			}
			if(!empty($futureLeavePeriodDates)){
				$totalFutureMonths = count($futureLeavePeriodDates);
				$totalEligibleDaysForFutureMonths = $eligibleDaysForaPeriod * $totalFutureMonths;
			}else{
				$totalEligibleDaysForFutureMonths = 0;
			}
			$totalNumberOfLeavesTaken = $this->getTotalLeaves($employeeId,$nextLeavePeriodStartDate,$leaveClosureEndDate,$leaveType,$leaveId, 1);
			$totalNumberOfLeavesTaken = ($totalNumberOfLeavesTaken > 0) ? $totalNumberOfLeavesTaken : 0;
			$futureMonthsRemainingLeaves = $totalEligibleDaysForFutureMonths - $totalNumberOfLeavesTaken;
		}else{
			$futureMonthsRemainingLeaves = 0;
		}
		return $futureMonthsRemainingLeaves;
	}

	/* Calculate the total number of months between two dates.
	startDate - first day of the month,
	endDate  - last day of the month */
	public function calcTotMonthsBetweenDates($startDate, $endDate){
		$monthsCount = 0;

		$nextMonthStartDate = $startDate;

		/* Get the total number of loan months between the start and the end date */	
		while(strtotime($nextMonthStartDate) <= strtotime($endDate)){
			$monthsCount++;
			$nextMonthStartDate = date('Y-m-d', strtotime('+1 month', strtotime($nextMonthStartDate)));
		}

		return $monthsCount;
	}


	public function getLeaveDuration($leaveTypeId,$leaveFromDate,$leaveToDate,$employeeId)
	{
		$empLeaveCalculationDays = $this->_db->fetchOne($this->_db->select()
								->from(array('leavetype'=>$this->_ehrTables->leavetype),array('Leave_Calculation_Days'))								
								->where('leavetype.LeaveType_Id = ?', $leaveTypeId));

		if($empLeaveCalculationDays == 0) //Get only  BusinessWorkingDays
		{
			$payslipDb = new Payroll_Model_DbTable_Payslip();
			$duration  = $payslipDb-> getBusinessWorkingDays(date('Y-m-d', strtotime($this->_ehrTables->changeDateformat($leaveFromDate))), $leaveToDate,$employeeId,NULL,1,'leaves');
		}
		else //All the days in month - no week off
		{
			$date1 = new DateTime(date('Y-m-d', strtotime($this->_ehrTables->changeDateformat($leaveFromDate))));
			$date2 = new DateTime($leaveToDate);
			
			$duration = ($date2->diff($date1)->format("%a"))+1;
		}
		return $duration;
	}

	/* Get all the quarters start date and end date if the leave period is quarterly or halfyearly for a leave type */
	public function getLeavePeriodStartEndDates($period,$employeeId, $leaveType, $leaveClosureBasedOn, $leaveClosureStartEndDate){
		if (Zend_Registry::isRegistered('orgDetails'))
            $this->_orgDetails = Zend_Registry::get('orgDetails');

		$startEndDates = $this->getLeaveClosureDetails($employeeId, $leaveType);

		$payslipDb = new Payroll_Model_DbTable_Payslip();

		/* Leave closure start date */
		$leaveClosureStartDate = date('Y-m-d',strtotime($leaveClosureStartEndDate['finstart']));
		
		// Using leave period(quarterly/halfyearly), set the max iteration count in a loop to get the quarter dates
		$iMax = ($period == 3) ? 4 : (($period == 6) ? 2 : 12);

		$quarterArray = array();

		if($leaveClosureBasedOn == 'Selected Month'){
			/* If the paycycle type is 'calendar' we can get the quarter end date by adding 2 months or 5 months or 0 month to the quarter start date */
			if($this->_orgDetails['Paycycle'] == "Calendar Month"){
				$monthCount = ($period == 3) ? ("+2 month"): (($period == 6) ? ("+5 month") : ("+0 month"));
			}else{
				/* If the paycycle type is 'non-calendar' we can get the quarter end date by adding 3 months or 6 months or 1 month to the quarter start date */
				$monthCount = ($period == 3) ? ("+3 month"): (($period == 6) ? ("+6 month") : ("+1 month"));
			}

			/* iteration to get the quarter start and end date for a leave period */
			for ($i=0; $i<12; $i++){
				// For the 0th index quarter start date is the leave closure start date
				if($i == 0){					
					$quarterStartDate = $leaveClosureStartDate;
					$leavePeriodMonth = date('m',strtotime($quarterStartDate));
					$leavePeriodYear = date('Y',strtotime($quarterStartDate));

					$leavePeriodDates = $payslipDb->getSalaryDateRange($leavePeriodMonth,$leavePeriodYear,strtotime($quarterStartDate));
					//The month and year of the last salary date is the actual month and year of the leave closure start date
					$leavePeriodMonth = date('m',strtotime($leavePeriodDates['Last_SalaryDate']));
					$leavePeriodYear = date('Y',strtotime($leavePeriodDates['Last_SalaryDate']));
				}else{
					if((int)$leavePeriodMonth===12){
						$leavePeriodMonth = 1;
						$leavePeriodYear++;
					} else {
						$leavePeriodMonth++;
					}
					$leavePeriodDates = $payslipDb->getSalaryDay($leavePeriodMonth,$leavePeriodYear);
				}
				$leavePeriodStartDate = $leavePeriodDates['Salary_Date'];
				$leavePeriodEndDate = $leavePeriodDates['Last_SalaryDate'];
			
				array_push($quarterArray,$leavePeriodStartDate);
				array_push($quarterArray,$leavePeriodEndDate);
			}

			//To get each quarter start and end date in a separate arrays
			$quarterArray = array_chunk($quarterArray,2);
			if($period > 1){
				$newQuarterArray = array();
				$leavePeriodAllRanges = array_chunk($quarterArray,$period);
				for ($i = 0; $i < count($leavePeriodAllRanges); $i++) {
					$leavePeriodAllRange = $leavePeriodAllRanges[$i];
					$leavePeriodAllRangeCount = count($leavePeriodAllRange);

					$leavePeriodStartDate = $leavePeriodAllRange[0][0];
					$leavePeriodEndDate = $leavePeriodAllRange[$leavePeriodAllRangeCount-1][1];

					array_push($newQuarterArray,$leavePeriodStartDate);
					array_push($newQuarterArray,$leavePeriodEndDate);
				}
				if(!empty($newQuarterArray)){
					$quarterArray = array_chunk($newQuarterArray,2);
				}
			}
		}else{
			//If the leave closure is based on the employee date of join
			$monthCount = ($period == 3) ? ("+3 month"): (($period == 6) ? ("+6 month") : ("+1 month"));

			/* iteration to get the quarter start and end date for a leave period */
			for ($i=0; $i<$iMax; $i++){
				// For the 0th index quarter start date is the leave closure start date
				if($i == 0){					
					$quarterStartDate = $leaveClosureStartDate;
				}else{
					$quarterStartDate = date('Y-m-d', strtotime("+1 day",strtotime($quarterEndDate)));
				}
				
				//push the quarter start date in an array
				array_push($quarterArray,$quarterStartDate);
				
				$quarterEndDate = date('Y-m-d', strtotime($monthCount,strtotime($quarterStartDate)));
				$quarterEndDate = date('Y-m-d', strtotime('-1 day',strtotime($quarterEndDate))); 
				
				//push the quarter end date
				array_push($quarterArray,$quarterEndDate);
			}	
			//To get each quarter start and end date in a separate arrays
			$quarterArray = array_chunk($quarterArray,2);
		}
		return $quarterArray;
	}

	/** Get the Employee Ids for the Leave Type-Custom Group  */
	public function getLeaveTypeCustomGroupEmployees($customGroupId){
		$customGroupEmpIds = array();
		if(!empty($customGroupId)){
			$customGroupEmployeesType = array('Default','AdditionalInclusion');

			$qryCustomGroupId = $this->_db->select()->from($this->_ehrTables->customEmployeeGroupEmployees,array('Employee_Id'))
																				->where("Type IN (?)", $customGroupEmployeesType);
			if(is_array($customGroupId))
			{
				$qryCustomGroupId->where('Group_Id IN (?)', $customGroupId);
			}	
			else
			{
				$qryCustomGroupId->where('Group_Id = ?', $customGroupId);
			}
			$customGroupEmpIds = $this->_db->fetchCol($qryCustomGroupId);
			return $customGroupEmpIds;
		}else{
			return $customGroupEmpIds;
		}
	}

	public function getCustomGroupEmployeeDetails($leaveTypeId,$employeeId=NULL)
	{
		$customGroupEmpIds 		  = array();
		$formId 				  = '32';
		$customGroupEmployeesType = array('Default','AdditionalInclusion');
		$qryCustomGroupId 		  = $this->_db->select()->from(array('CEGE'=>$this->_ehrTables->customEmployeeGroupEmployees),array('CEGE.Employee_Id'))
										->joinInner(array('CGAF'=>$this->_ehrTables->customGroupAssociateForm), 'CEGE.Group_Id=CGAF.Custom_Group_Id', array(''))
										->where('CGAF.Parent_Id = ?', $leaveTypeId)
										->where('CGAF.Form_Id = ?',$formId)
										->where('CEGE.Type IN (?)', $customGroupEmployeesType);	

		if(!empty($employeeId))
		{
			$qryCustomGroupId->where('CEGE.Employee_Id IN (?)',$employeeId);
		}								
		$customGroupEmpIds = $this->_db->fetchCol($qryCustomGroupId);
		return $customGroupEmpIds;
	}

	public function getGradeEmployeeDetails($leaveTypeId,$employeeId=NULL)
	{
		$gradeEmployeeId 	= array();
		$qryGradeEmployeeId = $this->_db->select()->from(array('LTG'=>$this->_ehrTables->leavetypegrade),array('EJ.Employee_Id'))
		 										->joinInner(array('DN'=>$this->_ehrTables->designation), 'LTG.Grade_Id=DN.Grade_Id', array(''))
												 ->joinInner(array('EJ'=>$this->_ehrTables->empJob), 'EJ.Designation_Id=DN.Designation_Id', array(''))
												->where('LTG.LeaveType_Id = ?',$leaveTypeId);
		if(!empty($employeeId))
		{
			$qryGradeEmployeeId->where('EJ.Employee_Id IN (?)',$employeeId);
		}										
		$gradeEmployeeId 	= $this->_db->fetchCol($qryGradeEmployeeId);
		return $gradeEmployeeId;
	}

	public function getOrganizationEmployeeDetails($employeeId=NULL)
	{
		$qryOrganizationEmployeeId = $this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),array('J.Employee_Id'))
												->joinInner(array('P'=>$this->_ehrTables->empPersonal),'J.Employee_Id = P.Employee_Id AND P.Form_Status = 1',array(''));
		if(!empty($employeeId))
		{
			$qryOrganizationEmployeeId->where('J.Employee_Id IN (?)',$employeeId);
		}
		$organizationEmployeeId 	= $this->_db->fetchCol($qryOrganizationEmployeeId);	
		return $organizationEmployeeId;									 
 	}

	/* Validate the custom group leave type to update the eligible days for custom group employees */
	public function validateUpdateCustomGroupLTEligibleDays($source,$leaveTypeData=NULL,$newEmployeeIds=NULL,$triggerFormName=NULL,$updateProbationEmployeeLeaveBalance=0){
		$eligDaysDaysResult = $isCustomGroupLeaveExists = $customGroupEligibleDaysUpdateResult = 0;
		$customGroupLeaveTypeDetails = $customGroupEmpIds = $leaveTypeCustomGroupEmployees = array();
		$allCustomGroupEmpIds = $allCustomGroupLTEmpIds = array();

		/* If the leave details are send */
		if(!IS_NULL($leaveTypeData) && !empty($leaveTypeData) && count($leaveTypeData) > 0){
			$customGroupLeaveTypeDetails = array($leaveTypeData);
		}else{
			/* Get the custom group leave type details */
			$customGroupLeaveTypeDetails = $this->getCustomGroupLeaveDetails();
		}

		/** If the leave balance has to be updated for the probation employees then get the after probation leave types */
		if((int)$updateProbationEmployeeLeaveBalance === 1 && count($customGroupLeaveTypeDetails) > 0){
			$keyValue = 'After Probation';
			$customGroupLeaveTypeDetails = array_filter($customGroupLeaveTypeDetails, function($value) use ($keyValue){
				return $value["Prorate_Leave_Balance_From"] == $keyValue;
			});
		}

		/* If custom group normal leave type exists */
		if(!empty($customGroupLeaveTypeDetails) && count($customGroupLeaveTypeDetails) > 0 )
		{
			$isCustomGroupLeaveExists = 1;
			$customGroupEligibleDaysUpdatedCount = 0;
			foreach($customGroupLeaveTypeDetails as $row)
			{
				/* If the coverage is custom group */
				$customGroupEmpIds = $this->getLeaveTypeCustomGroupEmployees($row['Custom_Group_Id']);
				/* If the employee id exists in the leavetype-custom group */
				if(!empty($customGroupEmpIds) && count($customGroupEmpIds) > 0){
					if(!IS_NULL($triggerFormName)){
						$allCustomGroupEmpIds [] = $customGroupEmpIds; // push all the employee ids
					}

					/* If the leave balance update is triggered from leave type add/edit */
					if($source == 'refreshCustomGroup' && !IS_NULL($newEmployeeIds) && is_array($newEmployeeIds)){
						/* If the leave balance update is triggered after the custom group employees is refreshed from employee import or
						employee migration or employee form when the form status is changed from 0 to 1 */
						
						/* Get the common employee ids from custom group employees list and the new employees list */
						$leaveTypeCustomGroupEmployees = array_intersect($newEmployeeIds,$customGroupEmpIds);
						/* If employee id exists in the custom group */
						if(!empty($leaveTypeCustomGroupEmployees) && count($leaveTypeCustomGroupEmployees)>0){
							if(!IS_NULL($triggerFormName)){
								$allCustomGroupLTEmpIds [] = $leaveTypeCustomGroupEmployees;
							}
							/* Update the eligible days for custom group employees for custom group leave type */
							$customGroupEligibleDaysUpdateResult = $this->calcUpdateEligibleDaysForCustomGroupEmp($leaveTypeCustomGroupEmployees,$row['LeaveType_Id'],
																		$row);
						}else{
							// if employee id not exists in the custom group, consider the response as success
							$customGroupEligibleDaysUpdateResult = 1;
						}

					}else{
						$customGroupEligibleDaysUpdateResult = 0;
					}

					/* If the eligible days, update the success count */
					if(!empty($customGroupEligibleDaysUpdateResult))
						$customGroupEligibleDaysUpdatedCount += 1;
				}else{
					/* If custom group employees not exists then consider response as success */
					$customGroupEligibleDaysUpdatedCount += 1;
				}
			}
			$eligDaysDaysResult = ($customGroupEligibleDaysUpdatedCount == count($customGroupLeaveTypeDetails)) ? 1 : 0;
		}

		if($triggerFormName == 'CustomEmployeeGroup'){
			return array( 'customGroupLeaveTypeDetails' => $customGroupLeaveTypeDetails, 'customGroupEmpIds' => $allCustomGroupEmpIds,
			'leaveTypeCustomGroupEmployees' => $allCustomGroupLTEmpIds, 'eligDaysDaysResult' => $eligDaysDaysResult);
		}else{
			/* If the custom group leave type exists return eligibility days response */
			if(!empty($isCustomGroupLeaveExists)){
				return $eligDaysDaysResult;
			}else{
				/* If the custom group leave type not exists return success response */
				return 1;
			}
		}	
	}

	/** Calculate and update the eligible days for the Custom Group-Leave Type  */
	public function calcUpdateEligibleDaysForCustomGroupEmp($customGroupEmpIds,$leaveTypeId,$leaveTypeData)
	{
		$leaveTypeNotUsedEmployeeId = $this->getLeaveTypeNotUsedEmployeeIds($customGroupEmpIds,$leaveTypeId);
		if(!empty($leaveTypeNotUsedEmployeeId))
		{
			$activeEmployeeDetails 			= $this->getActiveEmployeesDetails($leaveTypeData,$leaveTypeNotUsedEmployeeId);
			$employeeEligibleLeave 			= $this->getEmployeeEligibleLeave($activeEmployeeDetails);
			$updated 						= $this->insertEmployeeEligibleLeave($employeeEligibleLeave,NULL,'refresh-custom-group');
		}
		else
		{
			$updated = 0;
		}
		return $updated;
	}

	public function getLeaveTypeNotUsedEmployeeIds($employeeIds,$leaveTypeId) 
    {
		$validEmployeeId 	  = array();
        $eligibleLeaveDetails = $this->getEmployeeEligibleLeaveDetails($employeeIds,$leaveTypeId);
	    if(!empty($eligibleLeaveDetails))
        {
			$approvalStatus        = array('Applied','Returned');
			//if any leave records presented and that should be in applied,returned status we should not override the balance 
			//because user can edit the record becuase of new rule/custom group change leave might have been removed

			$leaveTakenDetails    = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->empLeaves, array('*'))
														->where('LeaveType_Id = ?',$leaveTypeId)
														->where('Approval_Status IN (?)', $approvalStatus)
														->where('Employee_Id IN (?)',$employeeIds));
														
			foreach($eligibleLeaveDetails as $row)
			{
				//when the leaves taken should be greater than zero and las then those leave should not be override.
				//if it override means then leave taken should be zero and new balance will be provided.
				if((empty($row['Leaves_Taken']) || $row['Leaves_Taken'] == 0) && (empty($row['Last_CO_Balance']) || $row['Last_CO_Balance'] == 0))
				{
				   if(!empty($leaveTakenDetails))
				   {
						foreach($leaveTakenDetails as $leaveTaken)
						{
							if($leaveTaken['Employee_Id']==$row['Employee_Id'] && $row['Leave_Closure_Start_Date']<= $leaveTaken['Start_Date'] 
							&& $row['Leave_Closure_End_Date'] >= $leaveTaken['Start_Date'])
							{
								 //employee already has leave applied in that duration so those employee ids are invalid we should not push those ids
							}
							else
							{
								array_push($validEmployeeId,$row['Employee_Id']);
							}
						}
				   }
				   else
				   {
						array_push($validEmployeeId,$row['Employee_Id']); 
				   }
              	}
			}
			return array_unique($validEmployeeId);
        }
        else
        {
            return $employeeIds;
        }
  	}


	public function getEmployeeLeaveDetails($employeeIds,$leaveTypeId,$row)
	{
		$leaveTakenDetails = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->empLeaves, array('*'))
																			->where('LeaveType_Id = ?',$leaveTypeId)
																			->where('Start_Date >= ?',$row['finstart'])
																			->where('Start_Date <= ?',$row['finend'])
																			->where('Employee_Id = ?',$employeeIds));
		return $leaveTakenDetails;
	}

	/* Form the where condition to get the custom group employees and its eligible days */
	public function customGroupWhere($leaveTypeAliasName,$leaveTypeId,$employeeId,$empLevelLeaves=null){
		$customGroupWhere = '';

		/* Custom group employees query */
		$customGroupEmployeesType = array('Default','AdditionalInclusion');
		$customGroupEmpIdQry = $this->_db->select()->from(array('CEGE'=>$this->_ehrTables->customEmployeeGroupEmployees),array('CEGE.Employee_Id'))
									->where("CEGE.Group_Id = $leaveTypeAliasName.Custom_Group_Id")
									->where("CEGE.Type IN (?)", $customGroupEmployeesType);

		/* Custom Group where condition in employee leave history */
		if(empty($leaveTypeId)){
			/* Check Custom Group leave type exist */
			$customGroupLeaveType = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->leavetype,array('LeaveType_Id'))
										->where('Coverage = ?','CUSTOMGROUP'));
										
			/* If the custom group leave type exist */
			if(!empty($customGroupLeaveType)){
				if(!empty($empLevelLeaves))
				{
					$customGroupWhere = $this->_db->quoteInto("$leaveTypeAliasName.Coverage = 'ORG' OR ($leaveTypeAliasName.Coverage = 'GRA' AND $leaveTypeAliasName.LeaveType_Id IN (?))",$empLevelLeaves).
										' OR '.$this->_db->quoteInto("($leaveTypeAliasName.Coverage = 'CUSTOMGROUP' AND $leaveTypeAliasName.LeaveType_Id IN (?)", $customGroupLeaveType).
										' AND '.$this->_db->quoteInto("$employeeId IN (?))",$customGroupEmpIdQry);
				}		
				else
				{
					$customGroupWhere = $this->_db->quoteInto("$leaveTypeAliasName.Coverage =?",'ORG').
										' OR '.$this->_db->quoteInto("($leaveTypeAliasName.Coverage = 'CUSTOMGROUP' AND $leaveTypeAliasName.LeaveType_Id IN (?)", $customGroupLeaveType).
										' AND '.$this->_db->quoteInto("$employeeId IN (?))",$customGroupEmpIdQry);
				}
			}else{
				/** Respective where condition will be formed in the respective function */
			}
		}else{
			/* Custom Group where condition used in leave balance validation for the particular leave type */
			$customGroupWhere = $this->_db->quoteInto('((L.Coverage = \'ORG\' OR L.Coverage = \'GRA\') AND L.LeaveType_Id = ?)',$leaveTypeId).
								' OR '.$this->_db->quoteInto("($leaveTypeAliasName.Coverage = 'CUSTOMGROUP' AND $leaveTypeAliasName.LeaveType_Id = ?", $leaveTypeId).
								' AND '.$this->_db->quoteInto("$employeeId IN (?))",$customGroupEmpIdQry);
		}

		return $customGroupWhere;
	}

	/* Get the custom group leave type details */
	public function getCustomGroupLeaveDetails($leaveTypeId = null,$customGroupId=null){
		$leaveEnforcementConfiguration = array(1,2);
		$customGroupNormalLeaveTypeQry = $this->_db->select()->from(array('leave'=>$this->_ehrTables->leavetype),array('leave.LeaveType_Id',
												'leave.Applicable_During_Probation','leave.Total_Days','leave.Enable_Proration','leave.Prorate_Leave_Balance_From',
												'leave.Prorate_After','leave.Gender','leave.Custom_Group_Id',
												'leave.Leave_Closure_Based_On','leave.Leave_Closure_Month','leave.Leave_Closure_Year'))
											->where('leave.Leave_Status = ?','Active')
											->where('leave.Leave_Enforcement_Configuration IN (?)',$leaveEnforcementConfiguration)
											->where('leave.Coverage = ?','CUSTOMGROUP');
		if(!IS_NULL($leaveTypeId)){
			$customGroupNormalLeaveTypeQry->where('LeaveType_Id = ?', $leaveTypeId);
		}

		if(!IS_NULL($customGroupId)){
			$customGroupNormalLeaveTypeQry->where('Custom_Group_Id IN (?)', $customGroupId);
		}

		return $this->_db->fetchAll($customGroupNormalLeaveTypeQry);
	}

	/* Update new joinee leave type balance and update the leave type balance when the DOJ is changed */
	public function updateNewJoineeLeaveTypeBalance($employeeDetails,$isNewEmp=0){
		$eligibleForLeaveBalance = array();
		if(isset($employeeDetails['Date_Of_Join']) && isset($employeeDetails['Probation_Date']) && isset($employeeDetails['Employee_Id'])
		&& isset($employeeDetails['Form_Status']) && isset($employeeDetails['Is_Update'])){
			$employeeId = $employeeDetails['Employee_Id'];
			$empDOJ = $employeeDetails['Date_Of_Join'];
			$probationDate = $employeeDetails['Probation_Date'];
			$empFormStatus = $employeeDetails['Form_Status'];
			$isUpdate = $employeeDetails['Is_Update'];			
			if (!empty($empDOJ))
			{
				$dateOfJoin = date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($empDOJ))); //current DOJ
				$strCurDOJ  = strtotime($dateOfJoin);
				
				$preDOJ     = $this->_dbJob->getDateOfJoin ($employeeId); //previous DOJ
				$strPreDOJ  = strtotime($preDOJ);				
				
				$probationEndDate = '';

				if(!empty($probationDate)){
					//probation date + 1 day
					$probationEndDate = date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($probationDate.'+1 days')));
				}
				
				$oldProbationDate = $this->_dbJob->getProbationDate ($employeeId);
				$oldProbationDate  = strtotime($oldProbationDate);
				$newProbationDate = strtotime($probationDate);

				/* When the employee is added from clone/migrate/import/employee form then no need to compare DOJ with previous DOJ. */
				if (((empty($isNewEmp) && $strPreDOJ != $strCurDOJ) || !empty($isNewEmp)) && $empFormStatus == 1 || ($oldProbationDate != $newProbationDate && $empFormStatus == 1))
				{
					$eligibleForLeaveBalance = array('Employee_Id'=>$employeeId,
													'Date_Of_Join'=>$dateOfJoin,
													'Probation_End_Date'=>$probationEndDate,
													'Is_Update'=>$isUpdate);
					
					return $eligibleForLeaveBalance;								
					
				}
			}

			return $eligibleForLeaveBalance;
		}else{
			return $eligibleForLeaveBalance;
		}
	}

	/* Validate the leaves exists or leave balance imported or deferred leaves exists for the custom group employees
	for all the custom group leave type and return the details */
	public function validateCustomGroupEmpLeaves($customGroupId){
		$customGroupLeaveTypeDetails = $this->getCustomGroupLeaveDetails(NULL,$customGroupId);

		$validationResponse = $customGroupLeaveTypeIds = $leaveTypeDet = $customGroupNewEmpIds = $empCustomGroupLeaveDetails = array();
		$empIdInavailedLeaves = $empIdInLeaves = $customGroupLeaveAppliedEmpIds = $empIdsNotInLeavesArr = $empIdInCOLeaves = array();
		$isSuccess = false;

		/* Get the custom group employees */
		$customGroupNewEmpIds = $this->getLeaveTypeCustomGroupEmployees($customGroupId);

		/* If custom group normal leave type exists */
		if(!empty($customGroupLeaveTypeDetails) && count($customGroupLeaveTypeDetails) > 0 )
		{
			foreach($customGroupLeaveTypeDetails as $row)
			{
				$customGroupLeaveTypeId = $row['LeaveType_Id'];
				array_push($customGroupLeaveTypeIds,$customGroupLeaveTypeId);				
				/* Push the leave type details in an array to update the employee custom group-leave type eligible days to 0 
				in case the employee is removed during the custom group edit */
				$leaveTypeDet[] = array('LeaveType_Id' => $customGroupLeaveTypeId,
										'CO_Year' => date('Y'));
			}
			
			if(!empty($customGroupLeaveTypeIds) && !empty($customGroupNewEmpIds) && count($customGroupLeaveTypeIds) > 0 && 
			count($customGroupNewEmpIds) > 0 && !empty($leaveTypeDet) && count($leaveTypeDet)>0){
				$CustomGroupLeaveEmpDetails = array('Employee_Ids' => $customGroupNewEmpIds,
											'LeaveType_Ids'=> $customGroupLeaveTypeIds);				

				/* Call the function to check custom group-leave type is used by the custom group employees */
				$empCustomGroupLeaveDetails = $this->getLeaveTypeUsed(0,$CustomGroupLeaveEmpDetails);

				if(!empty($empCustomGroupLeaveDetails) && count($empCustomGroupLeaveDetails) > 0){
					$isSuccess = true;
					/* If leaves exist in leaves table */
					if(!empty($empCustomGroupLeaveDetails['leavesTakenDetails']) && count($empCustomGroupLeaveDetails['leavesTakenDetails']) > 0){
						$empIdInLeaves = array_column($empCustomGroupLeaveDetails['leavesTakenDetails'], 'Employee_Id');
					}

					/* If availed leave balance exists in availed leaves table */
					if(!empty($empCustomGroupLeaveDetails['availedLeavesDetails']) && count($empCustomGroupLeaveDetails['availedLeavesDetails']) > 0){
						$empIdInavailedLeaves = array_column($empCustomGroupLeaveDetails['availedLeavesDetails'], 'Employee_Id');
					}

					/* If deferred leaves exist in deferred table */
					if(!empty($empCustomGroupLeaveDetails['deferredLeavesDetails']) && count($empCustomGroupLeaveDetails['deferredLeavesDetails']) > 0){
						$empIdInCOLeaves = array_unique($empCustomGroupLeaveDetails['deferredLeavesDetails']);
					}

					$customGroupLeaveAppliedEmpIds = array_merge($empIdInavailedLeaves,$empIdInLeaves,$empIdInCOLeaves);
					
					/* If leaves exists for the custom group employees */
					if(!empty($customGroupLeaveAppliedEmpIds)){
						$customGroupLeaveAppliedEmpIds = array_unique($customGroupLeaveAppliedEmpIds); // remove duplicate ids

						/** Get the values and remove the index of this array as the index will not be in the same order.
						 *  The values are pushed to this array by fetching the values from the keys of the multiple arrays. Also while removing
						 * the duplicate ids, the index may get varied if some of the repeated employee ids are removed. This array will 
						 * be considered as JSON automatically as the index differs. So only the values are fetched from this array before using it */
						$customGroupLeaveAppliedEmpIds = array_values($customGroupLeaveAppliedEmpIds);

						foreach($customGroupNewEmpIds as $customGroupEmpId){
							/* If the custom group employee not exists in the availed leaves/leaves/carryover table */
							if(!in_array($customGroupEmpId,$customGroupLeaveAppliedEmpIds)){
								array_push($empIdsNotInLeavesArr, $customGroupEmpId);
							}
						}
					}else{
						/* If leaves does not exists return the custom group employee ids in employee id not in leaves array */
						$empIdsNotInLeavesArr = $customGroupNewEmpIds;
					}
				}
			}else{
				/* If custom group leave type does not exists return the custom group employee ids in employee id not in leaves array */
				$empIdsNotInLeavesArr = $customGroupNewEmpIds;
			}
		}else{
			/* If custom group leave type does not exists return the custom group employee ids in employee id not in leaves array */
			$empIdsNotInLeavesArr = $customGroupNewEmpIds;
		}

		$validationResponse['success'] = $isSuccess;
		$validationResponse['EmpIds_Not_In_Leaves'] = $empIdsNotInLeavesArr;
		$validationResponse['Leave_Types'] = $leaveTypeDet;
		$validationResponse['Custom_Group_Emp_Ids'] = $customGroupNewEmpIds;
		$validationResponse['Custom_Group_LT_Details'] = $customGroupLeaveTypeDetails;
		$validationResponse['Custom_Group_Emp_Leave_Details'] = $empCustomGroupLeaveDetails;
		$validationResponse['Custom_Group_Applied_Emp_Ids'] = $customGroupLeaveAppliedEmpIds;
		
		return $validationResponse;
	}

	/** Get the employee encashment remaining days for a leave type */
	public function getEmpLeaveTypeEncashmentRemainingDays($historyRes){
		/** To return the details during payslip generation and the apply encashment form-leave type change event */
		foreach ($historyRes as $key=>$row)
		{
			$row['Encashed_Days'] = ($row['Encashed_Days']>0) ? $row['Encashed_Days'] : 0;
			$row['Leaves_Taken'] = ($row['Leaves_Taken']>0) ? $row['Leaves_Taken'] : 0;
			$row['No_Of_Days'] = ($row['No_Of_Days'] > 0) ? $row['No_Of_Days'] : 0;
			$remainingEncashmentDays = 0;

			$currentYearEmployeeLeaveEligiblity = $row['Eligible_Days'];
			$totalLeavesTaken = $row['Leaves_Taken'];
			if($totalLeavesTaken > $currentYearEmployeeLeaveEligiblity)
			{
               $remainingEncashmentDays = 0;
			   $totalEncashmentLimit 	= 0;
			}
			else
			{
				$currentYearBalance 	 = $currentYearEmployeeLeaveEligiblity-$totalLeavesTaken;
				$totalEncashmentLimit 	 = Min($currentYearBalance,$row['Encashment_Limit']);
				$remainingEncashmentDays = $totalEncashmentLimit - $row['Encashed_Days'];
			}
			$historyRes[$key]['Emp_Encashment_Total_Eligible_Days']  	= $totalEncashmentLimit;
			$historyRes[$key]['Emp_Encashment_Remaining_Eligible_Days'] = $remainingEncashmentDays;
		}

		return $historyRes;
	}

	/** Get the encashment leave type details */
	public function getEncashmentLeaveTypeDetails($processName,$listActiveEmployee,$leaveTypeId){
		/** If the carry over closure process is initiated */
		if($processName == 'carryover'){
			$ltOnCond   = "L.Carry_Over='Yes' AND L.Carry_Over_Accumulation_Limit > '0'";
		}else{
			$ltOnCond   = "LE_Year=CO_Year AND L.Encashment='Yes' AND L.Auto_Encashment='1'";
		}
		$currentDate = date('Y-m-d');

		$eligLvCond = 'el.LeaveType_Id = L.LeaveType_Id AND el.Employee_Id = P.Employee_Id';

		$lvBal      = '((IFNULL(el.No_Of_Days, 0) + (CASE WHEN el.Eligible_Days is NULL THEN L.Total_Days ELSE el.Eligible_Days END)) - ((IFNULL(SUM(en.Encashed_Days), 0)) + IFNULL(SUM(el.Leaves_Taken), 0)))';

		$eligDays   = 'CASE WHEN el.Eligible_Days is NULL THEN L.Total_Days ELSE el.Eligible_Days END';

		$enOnCond   = 'en.LeaveType_Id = L.LeaveType_Id AND en.EN_Year=el.CO_Year AND en.Employee_Id = P.Employee_Id';

		$qryLeaveEncashmentDetails = $this->_db->select()
										->from(array('P'=>$this->_ehrTables->empPersonal),
												array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS J.Employee_Id as count'),'P.Employee_Id','L.LeaveType_Id','L.Leave_Name','L.Total_Days','L.Encashment_Limit',
												'EmployeeName' => new Zend_Db_Expr('Concat(P.Emp_First_Name," ", P.Emp_Last_Name)'),
												'Leave_Balance'=>new Zend_Db_Expr($lvBal), 'L.Coverage','L.Leave_Enforcement_Configuration','el.LE_Year','el.CO_Year',
												'Eligible_Days'=>new Zend_Db_Expr($eligDays),'L.CarryOver_Limit','el.No_Of_Days','L.Enable_Proration','SUM(el.Leaves_Taken) as Leaves_Taken','el.Last_CO_Balance'))

										->joinInner(array('J'=>$this->_ehrTables->empJob),'P.Employee_Id = J.Employee_Id',array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN J.User_Defined_EmpId IS NULL THEN P.Employee_Id ELSE J.User_Defined_EmpId END'),'J.Date_Of_Join'))

										->joinLeft(array('R'=>$this->_ehrTables->resignation),"R.Employee_Id = J.Employee_Id AND R.Approval_Status = 'Approved'", 'R.Resignation_Date')

										->joinInner(array('L'=>$this->_ehrTables->leavetype),$ltOnCond,array('L.Carry_Over_Accumulation_Limit'))
										
										->joinLeft(array('el'=>$this->_ehrTables->empEligbleLeave),$eligLvCond,array())
										
										->joinLeft(array('en'=>$this->_ehrTables->encashedLeave),$enOnCond,array('SUM(en.Encashed_Days) as Encashed_Days'))
										
										->where('L.Leave_Status = \'Active\'')
										->where('P.Form_Status = 1')
										->where('el.Leave_Closure_End_Date < ?',$currentDate)										
										->group('L.LeaveType_Id')
										->group('P.Employee_Id');
	
		if(!empty($leaveTypeId))
		{
			$qryLeaveEncashmentDetails->where('L.LeaveType_Id IN (?)',$leaveTypeId);
		}								

		if($listActiveEmployee==='Yes')
		{
			$qryLeaveEncashmentDetails->where('el.Leave_Closure_End_Date < R.Resignation_Date OR R.Resignation_Date IS NULL');
		}

		return $qryLeaveEncashmentDetails;
	}

	/** Function to update the employee experience leave when the leave closure is run or when
	 * the employee form is updated or added with the new DOJ and form status 1
	 */
	public function updateEmpAllExpLeave($source,$expSourceDetails){
		$expLeaveTypeBalUpdateRes = $totalExpRecRes = 0;

		/** Fetch the experience based leave - leave type id */
		$experienceLeaveTypeQry = $this->_db->select()->from(array('leave'=>$this->_ehrTables->leavetype),array('leave.LeaveType_Id'))
								->where('leave.Leave_Status = ?','Active')
								->where('leave.Leave_Enforcement_Configuration = ?',5);

		$experienceLeaveType = $this->_db->fetchCol($experienceLeaveTypeQry);

		/** If the experience based leave exist */
		if(count($experienceLeaveType)>0)
		{
			foreach($experienceLeaveType as $row)
			{
				$updateExperienceLeaveCount = 0;

				/** Get the experience details */
				$getExperienceDetails = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->empExperienceLeave, array('Experience_Id','LeaveType_Id'))
										->where('LeaveType_Id = ?', $row));

				foreach($getExperienceDetails as $experienceDetails)
				{
					$expLvEnforcemntDet = array (
						'Experience_Id' => $experienceDetails['Experience_Id'],
						'LeaveType_Id' => $experienceDetails['LeaveType_Id']
					);

					/** Call the function to update the experience based leave in the table */
					$updateExperienceLeave = $this->_dbExperienceLeave->getEmployeeExperienceRange($expLvEnforcemntDet,$source,$expSourceDetails);

					$updateExperienceLeaveCount += $updateExperienceLeave;
				}

				
				$expLvCount = ($updateExperienceLeaveCount === count($getExperienceDetails)) ? 1 : 0;				
				$totalExpRecRes += $expLvCount;
			}

			/** Validate all the experience based leave type -  eligible days are updated. If updated return 1, otherwise return 0 */
			$expLeaveTypeBalUpdateRes = ($totalExpRecRes === count($experienceLeaveType)) ? 1 : 0;
			
		}else{
			$expLeaveTypeBalUpdateRes = 1;
		}

		return $expLeaveTypeBalUpdateRes;
	}

	/*when the payslip is not generated for particular month we need to return that month*/
	public function checkAllPayslipGenerated($action)
	{
		$payoutSalaryMonth 	= array();
		$payslipExist 		= $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->monthlyPayslip, array('Payslip_Id')));
		$orgCode 			= $this->_ehrTables->getOrgCode();
		if($orgCode =='noncalendartest1')
		{
			$payslipExist = 0;
		}
		
		if(!empty($payslipExist))
		{
			$dbFinancialYr    			= new Default_Model_DbTable_FinancialYear();
			$dbPayslip 					= new Payroll_Model_DbTable_Payslip();
			$assessmentYear 			= $this->_orgDetails['Assessment_Year'];
			$payslipMonths 				= $dbFinancialYr->getFiscalMonthYear(null,$assessmentYear);														
			$financialStartMonthExplode = explode(',',$payslipMonths[0]);
			$financialEndMonthExplode 	= explode(',',$payslipMonths[11]);
			$financialStartMonth 		= date('Y-m',strtotime($financialStartMonthExplode[1].'-'.$financialStartMonthExplode[0]));
			$financialEndMonth 			= date('Y-m',strtotime($financialEndMonthExplode[1].'-'.$financialEndMonthExplode[0]));

			$payslipMonth 			= $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->monthlyPayslip,array(new Zend_Db_Expr('MIN(STR_TO_DATE(Salary_Month,"%m,%Y")) as SalaryMonth')))
																	->where('Salary_Month IN (?)',$payslipMonths));
			
			if(!empty($payslipMonth))
			{
				$payslipMonthExplode 	= explode('-',$payslipMonth);
				$minimumPayslipMonth 	= date('Y-m',strtotime($payslipMonthExplode[0].'-'.$payslipMonthExplode[1]));

				if(strtotime($minimumPayslipMonth) > strtotime($financialStartMonth)) 
				{
					$minimumPayslipMonth = $minimumPayslipMonth;
				}
				else 
				{
					$minimumPayslipMonth = $financialStartMonth;
				}
				
				if($action=='financialClosure')
				{
					$payrollStartMonth = $minimumPayslipMonth;
					$payrollEndMonth   = $financialEndMonth;
				}
				else 
				{
					$payrollStartMonth 		= $minimumPayslipMonth;
					$leaveClosureDetails    = $this->getEmployeeEligibleLeaveDetails(NULL,NULL,'Selected Month');
					if(!empty($leaveClosureDetails))
					{
						$maxLeaveClosureEndDate = $leaveClosureDetails[0]['finend'];
						$payrollEndMonth   		= date('Y-m',strtotime($maxLeaveClosureEndDate));
					}
					else
					{
						$payrollEndMonth   = $financialEndMonth;
					}
				}

				$locationPair     = $this->_dbLocation->getLocationPair();
				$departmentPair   = $this->_dbDept->getDeptPairs();
				$employeeTypePair = $this->_dbEmpType->getEmpTypePairs();
				
				if(!empty($locationPair)&&!empty($departmentPair)&&!empty($employeeTypePair))
				{
					$location     	= array_keys($locationPair);
					$department   	= array_keys($departmentPair);
					$employeeType 	= array_keys($employeeTypePair);
					$maxMonthSalary = $dbPayslip->getmaxMonthlyPayslipSalary('Monthly');
					
					$searchArray  	= array('Employee_Type' => $employeeType,
										'Location'        => $location,
										'Department'      => $department,
										'salaryRangeFrom' => 0,
										'salaryRangeTo'   => $maxMonthSalary,
										'readyToGenerate' => 'checked',
										'payslipGenerated'=> 'checked',
										'pendingApproval' => 'checked');
					
					$searchAll  			= '';
					$approvalsPendingEmpIds = array();
					
					while(strtotime($payrollStartMonth) <= strtotime($payrollEndMonth))
					{
						$payoutSalaryMonth 	= date('n,Y',strtotime($payrollStartMonth));
						$month 		 		= explode(',', $payoutSalaryMonth);
						$paycyleDate 		= $dbPayslip->getSalaryDay($month[0], $month[1]);

						//get payslip generated employees list
						$payslipGeneratedEmployeesList 	= $dbPayslip->getPayslipGenereatedEmployees($payoutSalaryMonth, $searchArray, $searchAll,'Yes');
		
						$payslipGeneratedEmployeesList = array_column($payslipGeneratedEmployeesList, 'Employee_Id');
						
						//get payslip not generated employees list
						$payslipPendingEmployeesList 	= $dbPayslip->getPendingPayslipGenerationEmployees($payoutSalaryMonth, $searchArray, $searchAll, $paycyleDate, $approvalsPendingEmpIds, $payslipGeneratedEmployeesList,'Yes');
						if(!empty($payslipPendingEmployeesList))
						{
							//payslip generated from month format should be like April 2020
							$payslipGeneratedFromMonth 	= date('F Y',strtotime($payrollStartMonth));
							
							//payslip generated to month format should be like March 2021
							$payslipGeneratedToMonth 	= date('F Y',strtotime($payrollEndMonth));
				
							return array('From'=>$payslipGeneratedFromMonth,'To'=>$payslipGeneratedToMonth);
						}
			
						$payrollStartMonth = date('Y-m',strtotime($payrollStartMonth. " + 1 month"));
					}
				}
				else 
				{
					//payslip generated from month format should be like April 2020
					$payslipGeneratedFromMonth 	= date('F Y',strtotime($financialStartMonth));
					
					//payslip generated to month format should be like March 2021
					$payslipGeneratedToMonth 	= date('F Y',strtotime($financialEndMonth));
					return array('From'=>$payslipGeneratedFromMonth,'To'=>$payslipGeneratedToMonth);
				}	
			}
			else 
			{
				//when there is no payslip exist in current financial year we could allow the employee to do the leave closure
				return $payoutSalaryMonth;
			}
		}
		else 
		{
			return $payoutSalaryMonth;
		}
	}

	//get the active employee details employee id,date of join,employee confirmation date and fixed date
	public function getActiveEmployeesDetails($leaveTypeDetails,$employeeId=NULL,$action=NULL,$newYearLeaveClosureDates=NULL)
	{
		$activeEmployeeDetails = array();
		$fixedDays = $leaveTypeDetails['Prorate_After'];
		$qryActiveEmployeeDetails = $this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),array('J.Employee_Id','J.Date_Of_Join',
															new Zend_Db_Expr("DATE_ADD(J.Probation_Date, INTERVAL 1 DAY) as Employee_Confirmation_Date"),
															new Zend_Db_Expr("DATE_ADD(J.Date_Of_Join, INTERVAL '$fixedDays' DAY) as Fixed_Date")))
															->joinInner(array('P'=>$this->_ehrTables->empPersonal),'J.Employee_Id = P.Employee_Id AND P.Form_Status = 1',array(''))
															->joinLeft(array('R'=>$this->_ehrTables->resignation), 'J.Employee_Id=R.Employee_Id AND R.Approval_Status="Approved"', array('R.Resignation_Date'));
        
		if($leaveTypeDetails['Coverage']=='CUSTOMGROUP')
		{
			$employeeIds = $this->getCustomGroupEmployeeDetails($leaveTypeDetails['LeaveType_Id'],$employeeId);
		}
		else if($leaveTypeDetails['Coverage']=='GRA')
		{
			$employeeIds  = $this->getGradeEmployeeDetails($leaveTypeDetails['LeaveType_Id'],$employeeId);
		}
		else
		{
			$employeeIds  = $this->getOrganizationEmployeeDetails($employeeId);
		}

		if(!empty($employeeIds))
		{
			$qryActiveEmployeeDetails->where('J.Employee_Id IN (?)', $employeeIds);

			if(!empty($leaveTypeDetails['Gender']) && $leaveTypeDetails['Gender'] != 'ALL')
			{
				$qryActiveEmployeeDetails->where('P.Gender = ?',$leaveTypeDetails['Gender']);
			}
			$qryActiveEmployeeDetails->where('J.Date_Of_Join IS NOT NULL');
			$activeEmployeeDetails = $this->_db->fetchAll($qryActiveEmployeeDetails);

			$dependent = $this->_db->select()->from(array('ED'=>$this->_ehrTables->empDependent),array(new Zend_Db_Expr('count(Dependent_Id) as Child_Count'),'ED.Employee_Id'))
																						->where('ED.Employee_Id IN (?)', $employeeIds)
																						->group('ED.Employee_Id')
																						->where('ED.Relationship IN (?)',array('Son ','Daughter'));
			$dependentEmployeeDetails = $this->_db->fetchAll($dependent);
			foreach($activeEmployeeDetails as $key => $row)
			{
				if($leaveTypeDetails['Leave_Closure_Based_On']=='Employee Date Of Join') {
					$leaveClosureDates = $this->formLeaveClosureDateBasedOnDateOfJoin($row['Date_Of_Join']);
				}
				else if (in_array($leaveTypeDetails['Leave_Closure_Based_On'], ['Limited Replenishment on Approval', 'Unlimited Replenishment on Approval'])) 
				{
					$leaveClosureDates = $this->replenishmentLeaveClosureDate($row['Date_Of_Join']);
				}
				else if (in_array($leaveTypeDetails['Leave_Closure_Based_On'], ['Employee Service'])) 
				{
					$employmentDate['Date_Of_Join']= $row['Date_Of_Join'];
					$employmentDate['finend']	   = date('Y-m-d');
					$employmentYear 			   = $this->_dbServiceLeave->getEmploymentYear($employmentDate);
					$leaveClosureDates 			   = $this->replenishmentLeaveClosureDate($row['Date_Of_Join'],$employmentYear);
				}
				else{
					if(is_null($newYearLeaveClosureDates)){
						$leaveClosureDates = array(
							'finstart' => $leaveTypeDetails['Leave_Closure_Start_Date'],
							'finend' => $leaveTypeDetails['Leave_Closure_End_Date'],
							'coyear' => date('Y',strtotime($leaveTypeDetails['Leave_Closure_Start_Date']))
						);
					}else{
						$leaveClosureDates = $newYearLeaveClosureDates;
					}
				}
				//leave closure start date should be always greater than or equal to employee date of join.when we do that we will not consider future year joined employees
				if(strtotime($row['Date_Of_Join']) <= strtotime($leaveClosureDates['finend']))
				{
					//resignation date should be empty or
					//leave closure start date should be always less than or equal to employee resignation date.when we do that we will not consider previous year resigned employee
					if((empty($row['Resignation_Date'])) || (!empty($row['Resignation_Date']) && strtotime($row['Resignation_Date']) >= strtotime($leaveClosureDates['finstart'])))
					{
						$activeEmployeeDetails[$key]['finstart'] 	 	= $leaveClosureDates['finstart'];
						$activeEmployeeDetails[$key]['finend'] 		 	= $leaveClosureDates['finend'];
						$activeEmployeeDetails[$key]['coyear'] 		 	= $leaveClosureDates['coyear'];
						$activeEmployeeDetails[$key]['LeaveType_Id'] 	= $leaveTypeDetails['LeaveType_Id'];
						$activeEmployeeDetails[$key]['Total_Days'] 	 	= $leaveTypeDetails['Total_Days'];
						$row['finend'] 				 				 	= $leaveClosureDates['finend'];
						$employmentYear 							 	= $this->_dbServiceLeave->getEmploymentYear($row);
						$activeEmployeeDetails[$key]['Employment_Year']	= $employmentYear;
						$activeEmployeeDetails[$key]['Child_Count'] 	= 0;

						foreach($dependentEmployeeDetails as $dependentEmploye)
						{
							if($dependentEmploye['Employee_Id']==$row['Employee_Id'])
							{
								$activeEmployeeDetails[$key]['Child_Count'] = $dependentEmploye['Child_Count'];
							}
						}
						
						if($leaveTypeDetails['Enable_Proration'] == 1)
						{
							if($leaveTypeDetails['Prorate_Leave_Balance_From'] == 'After Probation')
							{
								$activeEmployeeDetails[$key]['Proration_Date'] = $row['Employee_Confirmation_Date'];
							}
							elseif($leaveTypeDetails['Prorate_Leave_Balance_From'] == 'Date Of Join')
							{
								$activeEmployeeDetails[$key]['Proration_Date'] = $row['Date_Of_Join'];
							}
							else
							{
								$activeEmployeeDetails[$key]['Proration_Date'] = $row['Fixed_Date'];
							}

							if($leaveTypeDetails['Proration_Threshold_Date']=='Before specific day of the month')
							{
								$specificDayProrationDate = $this->getSpecificDayProrationDate($leaveTypeDetails,$activeEmployeeDetails[$key]);
								if(!empty($specificDayProrationDate))
								{
									$activeEmployeeDetails[$key]['Proration_Date'] = $specificDayProrationDate;
								}			
							}
						}
						else
						{
							$activeEmployeeDetails[$key]['Proration_Date'] = NULL;
						}
					}
					else
					{
						unset($activeEmployeeDetails[$key]);
					}
				}
				else
				{
					unset($activeEmployeeDetails[$key]);
				}
			}
		}
        return $activeEmployeeDetails;
	}

	public function getSpecificDayProrationDate($leaveTypeDetails,$activeEmployeeDetails)
	{
		$dbPayslip 	   			  = new Payroll_Model_DbTable_Payslip();
		$specificDayProrationDate = NULL;
		if($activeEmployeeDetails['finstart'] <= $activeEmployeeDetails['Proration_Date'] && $activeEmployeeDetails['Proration_Date'] <= $activeEmployeeDetails['finend'])
		{
			$salaryDateDetails  = $dbPayslip->getSalaryDateRange(date('m',strtotime($activeEmployeeDetails['Proration_Date'])),date('Y',strtotime($activeEmployeeDetails['Proration_Date'])),strtotime($activeEmployeeDetails['Proration_Date']));
			$concatSpecificDate = date('Y',strtotime($salaryDateDetails['Last_SalaryDate'])).date('m',strtotime($salaryDateDetails['Last_SalaryDate'])).$leaveTypeDetails['Day_Of_The_Month'];
			$specificDate       = date('Y-m-d',strtotime($concatSpecificDate));
			if($specificDate <= $activeEmployeeDetails['Proration_Date'])
			{
				$specificDayProrationDate = date('Y-m-d', strtotime('+1 day', strtotime($salaryDateDetails['Last_SalaryDate'])));
			}
		}
		return $specificDayProrationDate;
	}

	//this function will get the employee eligible leaves for group of employees
	public function getEmployeeEligibleLeave($activeEmployeeDetails)
	{
		$employeeEligibleLeave = array();
		if(!empty($activeEmployeeDetails))
		{
			foreach($activeEmployeeDetails as $activeEmployee)
			{
				if(!empty($activeEmployee['Proration_Date']))
				{
					$eligibleDays = $this->calculateEligibleDays($activeEmployee);
				}
				else
				{
					$eligibleDays = $activeEmployee['Total_Days'];
				}
				$employeeEligibleLeave[] = array('LeaveType_Id'	    		=> $activeEmployee['LeaveType_Id'],
												'Employee_Id'   			=> $activeEmployee['Employee_Id'],
												'Eligible_Days' 			=> $eligibleDays,
												'CO_Year'       			=> $activeEmployee['coyear'],
												'LE_Year'       			=> $activeEmployee['coyear'],
												'Leave_Closure_Start_Date' 	=> $activeEmployee['finstart'],
												'Leave_Closure_End_Date'   	=> $activeEmployee['finend']);
			}
		}

		return $employeeEligibleLeave;
	}

	//this function will calculate the eligible days for an employee for a leavetype
	public function calculateEligibleDays($leaveTypeDetails)
	{
		if(!empty($leaveTypeDetails))
		{
			$leaveClosureStartDate = $leaveTypeDetails['finstart'];
			$leaveClosureEndDate   = $leaveTypeDetails['finend'];

			if($leaveClosureStartDate <= $leaveTypeDetails['Proration_Date'] && $leaveTypeDetails['Proration_Date'] <= $leaveClosureEndDate)
			{
				$startDate		= date_create($leaveTypeDetails['Proration_Date']);
				$endDate		= date_create($leaveClosureEndDate);
				$numberOfDays	= date_diff($startDate,$endDate);
				$numberOfDays  	= $numberOfDays->format("%a")+1;
				
				$leaveTypeMinimumDuration = isset($leaveTypeDetails['leaveTypeMinimumDuration']) ? $leaveTypeDetails['leaveTypeMinimumDuration'] : '';
				$semiValue = $this->getSemiValue($leaveTypeDetails['LeaveType_Id'],$leaveTypeMinimumDuration);
				
				$eligibleDays 	= ($leaveTypeDetails['Total_Days'] / 365) * $numberOfDays;
				$eligibleDays 	= $this->_dbCommonFun->getRoundOffValue('Leave Eligiblity',$eligibleDays,'',$semiValue);
			}
			//when the pro ration date falls in previous year we need to return total days as eligible days
			elseif($leaveClosureStartDate > $leaveTypeDetails['Proration_Date'])
			{
				$eligibleDays = $leaveTypeDetails['Total_Days'];
			}
			//when the pro ration date falls in future year we need to return 0 as eligible days
			elseif($leaveTypeDetails['Proration_Date'] > $leaveClosureEndDate)
			{
				$eligibleDays = 0;
			}
			else
			{
				$eligibleDays = 0;
			}
		}
		else
		{
			$eligibleDays = 0;
		}

		return $eligibleDays;
	}

	//Get leave type duration details
	public function getLeaveTypeDurationDetails($leaveTypeIds=NULL,$fetchPair=0){
		if($fetchPair == 1){
			$leaveTypeDurationQuery = $this->_db->select()->from($this->_ehrTables->leaveTypesDuration, array('Duration_Value','Leave_Duration'))
										->where('LeaveType_Id IN (?)',$leaveTypeIds);
			return $this->_db->fetchPairs($leaveTypeDurationQuery);
		}else{
			$leaveTypeDurationQuery = $this->_db->select()->from(array('L'=>$this->_ehrTables->leaveTypesDuration),array('*'));

			if(!IS_NULL($leaveTypeIds)){
				$leaveTypeDurationQuery = $leaveTypeDurationQuery->where('L.LeaveType_Id IN (?)',$leaveTypeIds);
			}
			$leaveDurationDetails = $this->_db->fetchAll($leaveTypeDurationQuery);
			$leaveDurationGroupResult = $this->_dbCommonFun->organizeDataByEmployeeIdAndDate($leaveDurationDetails, 'LeaveType_Id');
			return $leaveDurationGroupResult;
		}
	}

	//Get leave type minimum duration for all the leave type ids 
	public function getLeaveTypeMinimumDuration($leaveTypeIds){
		$minimumLeaveDurations = array(); // To store the minimum durations for each leave type ID

		if(!is_array($leaveTypeIds)){
			$leaveTypeIds=array($leaveTypeIds);
		}
		$leaveDurationGroupResult = $this->getLeaveTypeDurationDetails($leaveTypeIds);

		// Loop through each leave type ID
		foreach ($leaveTypeIds as $key => $value){
			$durationLeaveTypeId = $value;
			$leaveDurationDetails = $this->_dbCommonFun->ensureArray($leaveDurationGroupResult, $durationLeaveTypeId);
			// Get the minimum duration for the current leave type ID
			if (!empty($leaveDurationDetails)) {
				// Initialize minimum duration and its priority
				$minimumLeaveDuration = $leaveDurationDetails[0]['Leave_Duration'];
				$minimumDurationValue = $leaveDurationDetails[0]['Duration_Value'];

				// Use a loop to find the minimum duration
				for ($i = 1; $i < count($leaveDurationDetails); $i++) {
					$currentLeaveDuration = $leaveDurationDetails[$i]['Leave_Duration'];
					$currentDurationValue = $leaveDurationDetails[$i]['Duration_Value'];

					// Check if the current duration has a lower priority than the stored one
					if ($currentDurationValue < $minimumDurationValue) {
						$minimumLeaveDuration = $currentLeaveDuration;
						$minimumDurationValue = $currentDurationValue;
					}
				}
			} else {
				$minimumLeaveDuration = 'Half Day'; // Default value if no leave durations are found
			}

			// Store the result for the current leave type ID
			$minimumLeaveDurations[$durationLeaveTypeId] = array($minimumLeaveDuration);
		}
		return $minimumLeaveDurations;
	}

	//this function will delete the existing eligible leaves. based on leavetypeId,coYear and employeeId. 
	// insert the eligible leave for that leavetypeId,coYear and employeeId
	public function insertEmployeeEligibleLeave($employeeEligibleLeaves,$leaveTypeUpdation=NULL,$action)
	{
		$auditEmployeeEligibleLeave = array();
		$inserted = 0;
		if(!empty($employeeEligibleLeaves[0]['LeaveType_Id']) && !empty($employeeEligibleLeaves[0]['Employee_Id']) && !empty($employeeEligibleLeaves[0]['CO_Year']))
		{
			$leaveTypeId = array_column($employeeEligibleLeaves,'LeaveType_Id');
			$employeeId  = array_column($employeeEligibleLeaves,'Employee_Id');
			if($leaveTypeUpdation=='Yes')
			{
				// $deleteEligibleLeaves = $this->_db->quoteInto('LeaveType_Id IN (?)', $leaveTypeId);
				$auditEmployeeEligibleLeaveDetails = $this->getEmployeeEligibleLeaveDetails(NULL,$leaveTypeId);
				$eligibleLeaveId  = array_column($auditEmployeeEligibleLeaveDetails,'Eligible_Leave_Id');
			}
			else
			{
				// $deleteEligibleLeaves = $this->_db->quoteInto('LeaveType_Id IN (?)', $leaveTypeId);
				// $deleteEligibleLeaves .= $this->_db->quoteInto('AND Employee_Id IN (?)', $employeeId);	
				$auditEmployeeEligibleLeaveDetails = $this->getEmployeeEligibleLeaveDetails($employeeId,$leaveTypeId);
				$eligibleLeaveId  = array_column($auditEmployeeEligibleLeaveDetails,'Eligible_Leave_Id');
			}

			if(!empty($auditEmployeeEligibleLeaveDetails))
			{
				foreach($auditEmployeeEligibleLeaveDetails as $row)
				{
					$auditEmployeeEligibleLeave[] = array('LeaveType_Id'   			=> $row['LeaveType_Id'],
														'Employee_Id'   			=> $row['Employee_Id'],
														'Eligible_Days' 			=> $row['Eligible_Days'],
														'Leaves_Taken' 			    => $row['Leaves_Taken'],
														'Leave_Balance' 			=> $row['Leave_Balance'],
														'No_Of_Days' 		    	=> $row['No_Of_Days'],
														'Last_CO_Balance' 			=> $row['Last_CO_Balance'],
														'Accumulation_Lapsed_Days'  => $row['Accumulation_Lapsed_Days'],
														'Replenishment_Limit'  		=> $row['Replenishment_Limit'],
														'CO_Year'      				=> $row['CO_Year'],
														'LE_Year'       			=> $row['LE_Year'],
														'Leave_Closure_Start_Date'  => $row['Leave_Closure_Start_Date'],
														'Leave_Closure_End_Date'    => $row['Leave_Closure_End_Date'],
														'Audit_Reason'       		=> $action,
														'Added_On'                  => date('Y-m-d H:i:s'),
														'Added_By'                  => $this->_logEmpId);
				}

				if(!empty($auditEmployeeEligibleLeave))
				{
					$inserted = $this->_ehrTables->insertMultiple($this->_ehrTables->auditEmpEligbleLeave, $auditEmployeeEligibleLeave);
				}
			}

			if(!empty($eligibleLeaveId))
			{
				$deleteEligibleLeaves = $this->_db->quoteInto('Eligible_Leave_Id IN (?)', $eligibleLeaveId);
				$deleted=$this->_db->delete($this->_ehrTables->empEligbleLeave,$deleteEligibleLeaves);
			}
			
			$inserted = $this->_ehrTables->insertMultiple($this->_ehrTables->empEligbleLeave, $employeeEligibleLeaves);
		}
		return $inserted;
	}

	//Calculate from date for leave applicable and the accumulate from configuration
	public function calculateFromDate($leaveTypeDetails,$employeeId){
		$applicableFromDate = '';
		if($leaveTypeDetails['Accumulate_Eligible_Days']==1)
		{
			$applicableFrom 					= $leaveTypeDetails['Accumulate_From'];
			$leaveTypeDetails['Prorate_After'] 	= $leaveTypeDetails['Accumulate_After'];
		}
		else
		{
			$applicableFrom 					= $leaveTypeDetails['Prorate_Leave_Balance_From'];
			$leaveTypeDetails['Prorate_After'] 	= $leaveTypeDetails['Prorate_After'];
		}
		
		$employeeDetails 	= $this->getActiveEmployeesDetails($leaveTypeDetails,[$employeeId]);
		if(!empty($employeeDetails))
		{
			if($applicableFrom === 'After Probation'){
				$applicableFromDate = $employeeDetails[0]['Employee_Confirmation_Date'];
			}else if($applicableFrom === 'Custom Configuration'){
				$applicableFromDate = $employeeDetails[0]['Fixed_Date'];
			}else{
				$applicableFromDate = $employeeDetails[0]['Date_Of_Join'];
			}
		}
		
		return $applicableFromDate;
	}


	public function calculateLeaveApplicableFromDate($leaveTypeDetails,$employeeId){
		$applicableFrom = $leaveTypeDetails['Applicable_During_Probation'];
		$leaveTypeDetails['Prorate_After'] 	= $leaveTypeDetails['Leave_Activation_Days'];
		$employeeDetails 	= $this->getActiveEmployeesDetails($leaveTypeDetails,[$employeeId]);
		if(!empty($employeeDetails))
		{
			if($applicableFrom === 'After Probation'){
				$applicableFromDate = $employeeDetails[0]['Employee_Confirmation_Date'];
			}else if($applicableFrom === 'Custom Configuration'){
				$applicableFromDate = $employeeDetails[0]['Fixed_Date'];
			}else{
				$applicableFromDate = $employeeDetails[0]['Date_Of_Join'];
			}
		}
		
		return $applicableFromDate;
	}

	//Validate the employee is eligible to apply the leave type
	public function validateEmployeeLeave($validationDetails,$documentUpload=NULL){
		$dbTimesheetHrs = new Employees_Model_DbTable_TimesheetHours();

		$leaveTypeId = $validationDetails['leaveTypeId'];
		$leaveId = $validationDetails['leaveId'];
		$leaveFrom = $validationDetails['leaveFrom'];
		$leaveTo = $validationDetails['leaveTo'];
		$employeeId = $validationDetails['employeeId'];
		$duration = $validationDetails['duration'];
		$leavePeriod = $validationDetails['leavePeriod'];

		$result = $this->leaveValidate ($leaveTypeId, $leaveId, $leaveFrom, $leaveTo, $employeeId, $duration, $leavePeriod,$documentUpload);
				
		$gradeId = $this->getGradeId($employeeId);
		
		$result['regularHours'] = $dbTimesheetHrs->getRegularHrs($gradeId);
		
		if (empty($result['regularHours']))
		{
			$result['regularHours'] = 0;
		}
		$result['leaveFreeze'] = $this->checkLeaveFreeze ($leaveTypeId, $leaveFrom, $leaveTo);
		return $result;
	}

	public function __destruct()
    {
        
    }

	//fuction to initiate workflowengine
	public function initiateWorkflowEngine($eventId,$instanceData,$sessionId)
	{
		$orgCode = $this->_ehrTables->getOrgCode();
		$instanceData->orgCode=$orgCode;
		$curl = curl_init();
		$method = "POST";
		$apiBaseUrl = Zend_Registry::get('workflowEngineInitiateBaseUrl');
		if(!empty($apiBaseUrl))
		{
			$url = $apiBaseUrl;
		}	
		else
		{
			curl_close($curl);
			return "";
		}
	
		/* Create an object */
		$workflowGroupVar = new \stdClass();
		$workflowGroupVar->event_id =$eventId;
		$workflowGroupVar->instance_data = $instanceData;
		
		/* Convert the object to JSON */
		$workflowGroupVarJSON = json_encode($workflowGroupVar);

		curl_setopt($curl, CURLOPT_POST, 1);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $workflowGroupVarJSON);
		curl_setopt($curl, CURLOPT_URL, $url);
		
		//Set API headers
		$isDomain = Zend_Registry::get('Domain');
		$isDomainArray = explode(".",$isDomain);
		$dbPrefix=$isDomainArray[0].'_';
		$apiHeaders = array(
			'content-type:application/json',
			'org_code:'.$orgCode,
			'employee_id:'.$sessionId,
			'db_prefix:'.$dbPrefix
		);
		curl_setopt($curl, CURLOPT_HTTPHEADER, $apiHeaders);

		curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

		// EXECUTE:
		$result = curl_exec($curl);
		$result=json_decode($result);
		curl_close($curl);
		if($result->message=="Workflow instantiated successfully")
		{
			return $result->workflowProcessInstanceId;
		}
		return "";
	}

	public function getUnpaidLeaveOverrideDetails($leaveData)
	{
		$leaveStartDate = $leaveData['Start_Date'];
		$leaveEndDate   = $leaveData['End_Date'];
		$dbPayslip 		= new Payroll_Model_DbTable_Payslip();
		$unpaidLeaveOverrideDetails = array(); 
		while($leaveStartDate <= $leaveEndDate)
		{
			$salaryDateDetails = $dbPayslip->getSalaryDateRange(date('m',strtotime($leaveStartDate)),date('Y',strtotime($leaveStartDate)),strtotime($leaveStartDate));

            //if leaveStartDate is greater than or equal to salaryDate then we need to consider the leaveStartDate as salaryDate
			if($salaryDateDetails['Salary_Date'] <= $leaveData['Start_Date'])
			{
				$salaryDate = $leaveStartDate;
			}
			else
			{
				$salaryDate = $salaryDateDetails['Salary_Date'];
			}

			//if leaveEndDate is lesser than or equal to lastSalaryDate then we need to consider the leaveEndDate as lastSalaryDate
			if($salaryDateDetails['Last_SalaryDate'] >= $leaveEndDate)
			{
				$lastSalaryDate = $leaveEndDate;
			}
			else
			{
				$lastSalaryDate = $salaryDateDetails['Last_SalaryDate'];
			}
			
			$totalLeaveDays = $dbPayslip->getLeaveFrequency($leaveData['Employee_Id'],$salaryDate,$lastSalaryDate,$leaveData['LeaveType_Id'],$leaveData['Leave_Id']);
            
			if($totalLeaveDays > 0)
			{
				$payslipMonth = date('n,Y', strtotime($lastSalaryDate));

				$unpaidLeaveOverride    = array('Unpaid_Leave_Override'	    =>'Leave',
												'Unpaid_Leave_Override_Id'	=>$leaveData['Leave_Id'],
												'Payslip_Month'           	=>$payslipMonth,
												'Total_Leave_Days'			=>$totalLeaveDays);

				array_push($unpaidLeaveOverrideDetails, $unpaidLeaveOverride);
			}
			$leaveStartDate = date('Y-m-d', strtotime('+1 day', strtotime($salaryDateDetails['Last_SalaryDate'])));
		}

		return $unpaidLeaveOverrideDetails;
	}

	public function insertUnpaidLeaveOverrideDetails($unpaidLeaveOverrideDetails)
	{
		$updated = 1;
		if(!empty($unpaidLeaveOverrideDetails))
		{
			$unpaidLeaveOverride 	= array_column($unpaidLeaveOverrideDetails, 'Unpaid_Leave_Override');
			$unpaidLeaveOverrideId 	= array_column($unpaidLeaveOverrideDetails, 'Unpaid_Leave_Override_Id');

			$deleted = $this->deleteUnpaidLeaveOverrideDetails($unpaidLeaveOverride[0],$unpaidLeaveOverrideId[0]);
			if($deleted)
			{
				$updated = $this->_ehrTables->insertMultiple($this->_ehrTables->unpaidLeaveOverride, $unpaidLeaveOverrideDetails);
			}
		}
		return $updated;
	}

	public function deleteUnpaidLeaveOverrideDetails($unpaidLeaveOverride,$unpaidLeaveOverrideId)
	{
		$where = $this->_db->quoteInto('Unpaid_Leave_Override = ? AND ', $unpaidLeaveOverride).
					$this->_db->quoteInto('Unpaid_Leave_Override_Id = ?',$unpaidLeaveOverrideId);
					
		$deleted = $this->_db->delete($this->_ehrTables->unpaidLeaveOverride, $where);
		if(empty($deleted))
		{
			$deleted = 1;
		}
		
		return $deleted;
	}

	public function getLeaveUnitCalculation($leaveTypeDetails,$documentUpload,$totalDays)
	{
		if($leaveTypeDetails['Enable_Leave_Exception']=='Yes' && $leaveTypeDetails['Leave_Unit']=='0.5')
		{
			if(!empty($leaveTypeDetails['Document_Upload']))
			{
				if(!empty($documentUpload))
				{
					$totalDays = $totalDays/$leaveTypeDetails['Leave_Unit'];
				}
			}
			else
			{
				$totalDays = $totalDays/$leaveTypeDetails['Leave_Unit'];
			}
		}
		return $totalDays;
	}

	public function getMinimumLimitForHalfPaidLeaveDeduction($leaveTypeDetails,$documentUpload)
	{
		if($leaveTypeDetails['Enable_Leave_Exception']=='Yes' && $leaveTypeDetails['Half_Paid_Leave_Deduction']=='Yes' && empty($documentUpload))
		{
			return $leaveTypeDetails['Minimum_Limit']; 
		}
		else
		{
			return $leaveTypeDetails['Minimum_Total_Days']; 
		}
	}

	//Get the leave closure start date and end date for the selected month
	public function getSelectedMonthLeaveClosureDates($leaveClosureMonth,$leaveClosureYear,$action)
    {
		$leaveClosureDate=array();
		if($action === 'leave-closure')	
		{
			//Find the next leave year leave closure dates
			$leaveYear= $leaveClosureYear + 1;
		}
		else
		{
			$leaveYear = $leaveClosureYear;
		}

		$dbPayslip  = new Payroll_Model_DbTable_Payslip();

		//Form the leave closure end date
		$leaveClosureDetails 		  = $dbPayslip->getSalaryDay($leaveClosureMonth,$leaveYear);
		$leaveClosureDate['finend']   = $leaveClosureDetails['Last_SalaryDate'];
		//Form the leave closure start date
		$previousYear                 = $leaveYear-1;
		$leaveClosureDetails 		  = $dbPayslip->getSalaryDay($leaveClosureMonth,$previousYear);
		$leaveClosureStartDate		  = date('Y-m-d', strtotime("+1 day", strtotime($leaveClosureDetails['Last_SalaryDate'])));
		$leaveClosureDate['finstart'] = $leaveClosureStartDate;
		$leaveClosureDate['coyear']   = date('Y',strtotime($leaveClosureDate['finstart']));
		$leaveClosureDate['leaveclosureendyear']   = $leaveYear;
        return $leaveClosureDate;
    }


	public function formLeaveClosureDateBasedOnDateOfJoin($dateOfJoin)
	{
		$leaveClosureDate=array();
		$currentDate  = date('Y-m-d');
		$currentYear  = date('Y',strtotime($currentDate));
		$employeeDateOfJoinMonth 	 = date('m',strtotime($dateOfJoin));
		$employeeDateOfJoinDay = date('d',strtotime($dateOfJoin));
		$employeeDateOfJoinYear = date('Y',strtotime($dateOfJoin));

		/** If the current date is lesser than the employee date of join(19 dec 2023 < 1 Feb 2024|| 19 Dec 2023) then 
		 * leave closure dates should be 1 feb 2024 - 31 Jan 2025 */ 
		if(strtotime($currentDate) <= strtotime($dateOfJoin)){
			$leaveClosureDate['finstart'] = date('Y-m-d',strtotime($employeeDateOfJoinYear.'-'.$employeeDateOfJoinMonth.'-'.$employeeDateOfJoinDay));
			$endDate = date('Y-m-d', strtotime('+1 year', strtotime($leaveClosureDate['finstart'])));
			$leaveClosureDate['finend']   = date('Y-m-d', strtotime("-1 day", strtotime($endDate)));
		}else{
			/** If the current date is greater than or equal to the employee date of join(19 dec 2023 > 18 dec 2022 || 19 Jan 2024 > 18 dec 2022) then 
		 	* leave closure dates should be 18 dec 2023 - 17 dec 2024 */
			$leaveClosureStartDate = date('Y-m-d',strtotime($currentYear.'-'.$employeeDateOfJoinMonth.'-'.$employeeDateOfJoinDay));

			//leave closure start date is 18 dec 2023 if current date is 19 dec 2023 or it is 18 dec 2024 if current date is 19 jan 2024
			if(strtotime($leaveClosureStartDate) > strtotime($currentDate)){
				$previousYear = $currentYear - 1;
				$leaveClosureStartDate = date('Y-m-d',strtotime($previousYear.'-'.$employeeDateOfJoinMonth.'-'.$employeeDateOfJoinDay));
			}
			$leaveClosureDate['finstart'] = $leaveClosureStartDate;
			$endDate = date('Y-m-d', strtotime('+1 year', strtotime($leaveClosureStartDate)));
			$leaveClosureDate['finend']   = date('Y-m-d', strtotime("-1 day", strtotime($endDate)));			
		}
		$leaveClosureDate['coyear']   = date('Y',strtotime($leaveClosureDate['finstart']));
		return $leaveClosureDate;
	}

	public function getLeaveTypeDetails($updateProbationEmployeeLeaveBalance,$leaveTypeId=NULL)
    {
        $qryLeaveType=$this->_db->select()->from(array('L'=>$this->_ehrTables->leavetype),array('*'))
													->where('L.Leave_Status = ?','Active');

		//If the leave has to be updated for the probation employee
		if($updateProbationEmployeeLeaveBalance === 1){
			$qryLeaveType->where('leave.Prorate_Leave_Balance_From = ?','After Probation');
		}
		
		if(!empty($leaveTypeId))
		{
			$qryLeaveType->where('L.LeaveType_Id IN (?)',$leaveTypeId);
		}

		$leaveTypeDetails = $this->_db->fetchAll($qryLeaveType);

		return $leaveTypeDetails;
    }

	public function getLeaveClosureDetails($employeeId,$leaveTypeId)
	{
		$eligibleDaysDetails = array();
		$leaveClosureDetails = $this->getEmployeeEligibleLeaveDetails($employeeId,$leaveTypeId);
		if(!empty($leaveClosureDetails))
		{
			$eligibleDaysDetails = $leaveClosureDetails[0];
			if(!empty($eligibleDaysDetails))
			{
				$eligibleDaysDetails['Leave_Balance'] 		 	 = $this->getEmpLeaveTypeBalance ($employeeId, $leaveTypeId);
				$eligibleDaysDetails['finstart'] 				 = $eligibleDaysDetails['Leave_Closure_Start_Date'];
				$eligibleDaysDetails['finend']   				 = $eligibleDaysDetails['Leave_Closure_End_Date'];
				$eligibleDaysDetails['coyear']   				 = $eligibleDaysDetails['CO_Year'];
				$eligibleDaysDetails['leaveEncashmentStartDate'] = $eligibleDaysDetails['Leave_Closure_Start_Date'];
				$eligibleDaysDetails['leaveEncashmentEndDate']   = $eligibleDaysDetails['Leave_Closure_End_Date'];
			}
		}
		return $eligibleDaysDetails;
	}

	public function getEmployeeEligibleLeaveDetails($employeeId=NULL,$leaveTypeId=NULL,$leaveClosureBasedOn=NULL)
	{
		$qryEligibleDays = $this->_db->select()->from(array('EL'=>$this->_ehrTables->empEligbleLeave),array('*'))
																		->order('EL.Leave_Closure_End_Date ASC');

		if(!empty($employeeId))																
		{
			$qryEligibleDays->where('EL.Employee_Id IN (?)',$employeeId);
		}

		if(!empty($leaveTypeId))																
		{
			$qryEligibleDays->where('EL.LeaveType_Id IN (?)',$leaveTypeId);
		}
		
		if(!empty($leaveClosureBasedOn))
		{
			$qryEligibleDays->joinInner(array('LT'=>$this->_ehrTables->leavetype),'EL.LeaveType_Id = LT.LeaveType_Id',array('EL.Leave_Closure_Start_Date as finstart','EL.Leave_Closure_End_Date as finend'))
																		->where('LT.Leave_Closure_Based_On = ?',$leaveClosureBasedOn)
																		->where('LT.Leave_Status = ?','Active');
		}

		$leaveClosureDetails = $this->_db->fetchAll($qryEligibleDays);
	
		return $leaveClosureDetails;
	}

	public function getAuditEmployeeEligibleLeaveDetails($employeeId=NULL,$leaveTypeId=NULL,$leaveClosureBasedOn=NULL)
	{
		$qryEligibleDays = $this->_db->select()->from(array('EL'=>$this->_ehrTables->auditEmpEligbleLeave),array('*'))
																		->order('EL.Audit_Eligible_Leave_Id DESC');

		if(!empty($employeeId))																
		{
			$qryEligibleDays->where('EL.Employee_Id IN (?)',$employeeId);
		}

		if(!empty($leaveTypeId))																
		{
			$qryEligibleDays->where('EL.LeaveType_Id IN (?)',$leaveTypeId);
		}
		
		if(!empty($leaveClosureBasedOn))
		{
			$qryEligibleDays->joinInner(array('LT'=>$this->_ehrTables->leavetype),'EL.LeaveType_Id = LT.LeaveType_Id',array('EL.Leave_Closure_Start_Date as finstart','EL.Leave_Closure_End_Date as finend'))
																		->where('LT.Leave_Closure_Based_On = ?',$leaveClosureBasedOn)
																		->where('LT.Leave_Status = ?','Active')
																		->where('EL.Audit_Reason = ?','leave-closure');
		}

		$leaveClosureDetails = $this->_db->fetchAll($qryEligibleDays);
	
		return $leaveClosureDetails;
	}

	//check whether the partial leave closure exist or not.
	public function checkPartialLeaveClosureExist($employeeId=NULL)
    {
    	$qryPartialLeaveClosure = $this->_db->select()->from(array('EL'=>$this->_ehrTables->empEligbleLeave),array('EL.Employee_Id'))
																									->group('EL.Employee_Id')
																									->where('EL.CO_Year !=EL.LE_Year');
		if(!empty($employeeId))
		{
			$qryPartialLeaveClosure->where('EL.Employee_Id =?',$employeeId);
		}
		$partialLeaveClosureEmployee = $this->_db->fetchCol($qryPartialLeaveClosure);
		return $partialLeaveClosureEmployee;
    }

	public function getLeaveClosureExist($startDate)
	{
		$leaveClosureDetails = $this->getEmployeeEligibleLeaveDetails(NULL,NULL,'Selected Month');
		foreach($leaveClosureDetails as $leaveClosure)
		{
			/* If the payslip start date is greater than leave closure end date we need to ask the user to do the leave closure*/
			if (!empty($leaveClosure['finend']) && strtotime($leaveClosure['finend']) < strtotime($startDate))
			{
				return 1;
			}
		}
		return 0;
	}

	public function getLeaveClosureAndLeaveEncashmentDetails()
	{
		$autoEncashedLeaves = $nonAutoEncashedLeaves = $carryOverLeaves = array();
		$leaveClosureExist  = $leaveEncashmentExist  = 0;
		$currentDate = date('Y-m-d');
		$qryLeaveClosureDetails = $this->_db->select()->from(array('EL'=>$this->_ehrTables->empEligbleLeave),array('EL.*','LT.Leave_Name','LT.Encashment','LT.Auto_Encashment','LT.Carry_Over'))
															->joinInner(array('EJ'=>$this->_ehrTables->empJob),'EL.Employee_Id = EJ.Employee_Id',array(''))
															->joinInner(array('EP'=>$this->_ehrTables->empPersonal),'EJ.Employee_Id = EP.Employee_Id AND EP.Form_Status = 1',array())
															->joinInner(array('LT'=>$this->_ehrTables->leavetype),'EL.LeaveType_Id = LT.LeaveType_Id',array(''))
															->where('LT.Leave_Status = ?','Active')
															->where('EL.Leave_Closure_End_Date < ?',$currentDate)
															->where('LT.Leave_Closure_Based_On = ?','Selected Month')
															->group('EL.Eligible_Leave_Id');

		$leaveClosureDetails = $this->_db->fetchAll($qryLeaveClosureDetails);	

		if(!empty($leaveClosureDetails))
		{
			foreach($leaveClosureDetails as $leaveClosure)
			{
				if($leaveClosure['Encashment']=='Yes' && $leaveClosure['Auto_Encashment']==1)
				{
					//we need to group the values based on leaveTypeId
					$leaveTypeIds = array_column($autoEncashedLeaves,'LeaveType_Id');
					if(!(in_array($leaveClosure['LeaveType_Id'],$leaveTypeIds)))
					{
						array_push($autoEncashedLeaves,$leaveClosure);
					}
				}
				
				if($leaveClosure['Encashment']=='Yes' && $leaveClosure['Auto_Encashment']==0)
				{
					//we need to group the values based on leaveTypeId
					$leaveTypeIds = array_column($nonAutoEncashedLeaves,'LeaveType_Id');
					if(!(in_array($leaveClosure['LeaveType_Id'],$leaveTypeIds)))
					{
						array_push($nonAutoEncashedLeaves,$leaveClosure);
					}
				}

				if($leaveClosure['Carry_Over']=='Yes')
				{
					//we need to group the values based on leaveTypeId
					$leaveTypeIds = array_column($carryOverLeaves,'LeaveType_Id');
					if(!(in_array($leaveClosure['LeaveType_Id'],$leaveTypeIds)))
					{
						array_push($carryOverLeaves,$leaveClosure);
					}
				}
			}

			foreach($leaveClosureDetails as $leaveClosure)
			{
				if($leaveClosure['LE_Year']<date('Y') && $leaveClosure['LE_Year']==$leaveClosure['CO_Year'])
				{
					$leaveEncashmentExist = 1;
					break;
				}
			}

			foreach($leaveClosureDetails as $leaveClosure)
			{
				if($leaveClosure['CO_Year']<date('Y'))
				{
					$leaveClosureExist = 1;
					break;
				}
			}
		}
		return array('Leave_Closure_Exist'=>$leaveClosureExist,'Leave_Encashment_Exist'=>$leaveEncashmentExist,'Leave_Closure_Details'=>$leaveClosureDetails,
					 'Auto_Encashed_Leaves'=>$autoEncashedLeaves,'Non_Auto_Encashed_Leaves'=>$nonAutoEncashedLeaves,'Carry_Over_Leaves'=>$carryOverLeaves);
	}

	public function getLeaveClosureYear()
	{
		$payslipExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->monthlyPayslip, array('Payslip_Id')));
		if(!empty($payslipExist))
		{
			$leaveClosureYear = array('Current Year'=>'Current Year');	
		}
		else
		{
			$leaveClosureYear = array('Current Year'=>'Current Year',
									'Previous Year'=>'Previous Year');
		}
		return $leaveClosureYear;
	}

	//fuction to call custom group refresh api
	public function callLeaveBalanceApi()
	{
		$orgCode = $this->_ehrTables->getOrgCode();
		// $orgCode='testingdomain';
		$curl = curl_init();
		$method = "POST";
		$apiBaseUrl = Zend_Registry::get('hrappBeRoBaseUrl');
		if(!empty($apiBaseUrl))
		{
			$url = $apiBaseUrl;
		}	
		else
		{
			curl_close($curl);
			return "Failed";
		}

		$requestBody = '{
			"query":"query getTeamLeaveHistory { getTeamLeaveHistory { errorCode message leaveHistory}}"
		}';

		curl_setopt($curl, CURLOPT_POST, 1);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $requestBody);
		curl_setopt($curl, CURLOPT_URL, $url);
		
		//Set API headers
		$apiHeaders = $this->_dbCommonFun->getApiHeaders($orgCode);
		curl_setopt($curl, CURLOPT_HTTPHEADER, $apiHeaders);

		curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

		// EXECUTE:
		$leaveHistoryResponse = curl_exec($curl);
		curl_close($curl);
		if(!$leaveHistoryResponse)
		{
			return '';
		}
		else
		{
			$leaveHistoryResponse = json_decode($leaveHistoryResponse,true);
			if(isset($leaveHistoryResponse['message'])){
				return '';
			}else if(isset($leaveHistoryResponse['data']) && $leaveHistoryResponse['data']){
				$error = $leaveHistoryResponse['data']['getTeamLeaveHistory']['errorCode'];
				$leaveHistoryDetails = $leaveHistoryResponse['data']['getTeamLeaveHistory']['leaveHistory'];
				
				if($error){
					return '';
				}else{
					$leaveHistory = json_decode($leaveHistoryDetails,true);
					$leaveBalance = $leaveBalanceDetails = array();
					foreach($leaveHistory as $key => $value) 
					{
						$leaveBalance['Employee_Id']  					  = $value['userDefinedEmployeeId'];
						$leaveBalance['Name'] 							  = $value['employeeName'];
						$leaveBalance['Department'] 					  = $value['departmentName'];
						$leaveBalance['Date_Of_Join'] 					  = date($this->_orgDF['php'], strtotime($value['dateOfJoin']));
						$leaveBalance['Leave_Name'] 		  			  = $value['leaveName'];
						$leaveBalance['Total_Eligibility(Per_Annum)']  	  = $value['currentYearEligibleDays'] + $value['coDays'];
						$leaveBalance['Leaves_Taken'] 	  				  = $value['leavesTaken'];
						$leaveBalance['Leave_Balance(Per_Annum)']         = $value['leaveBalance'];
						$leaveBalance['Current_Year_Eligibility'] 		  = $value['currentYearEligibleDays'];
						$leaveBalance['Carry_Over_Days']          		  = $value['coDays'];
						$leaveBalance['Carry_Over_Balance']            	  = $value['coBalance'];
						$leaveBalance['Leave_Eligiblity_Based_On_Period'] = $value['eligibleDays'];

						if($value['accuralRestriction'] && strtolower($value['accuralRestriction']) === 'yes' 
						&& $value['Accrual']=='Based on the custom configuration' && in_array($value['Period'],array(1,3,6))){
							//From this endpoint, we are calculating the actual balance using co days. So we do not need to add co balance
							$leaveBalance['Total_Leave_Balance_Based_On_Period'] = $value['remainingDaysTillCurrentLeavePeriod'];
						}else{
							$leaveBalanceBasedOnPeriod 						  = $value['eligibleDays']-$value['leavesTaken'];
							$leaveBalance['Total_Leave_Balance_Based_On_Period'] = $leaveBalanceBasedOnPeriod+$value['coBalance'];
						}
						
						if($leaveBalanceBasedOnPeriod < 0)
						{
							$leaveBalanceBasedOnPeriod = 0;
						}
						$leaveBalance['Leave_Balance_Based_On_Period']    = $leaveBalanceBasedOnPeriod;
						$leaveBalance['Leave_Closure_Start_Date']      	  = date($this->_orgDF['php'], strtotime($value['leaveClosureStartDate']));
						$leaveBalance['Leave_Closure_End_Date']        	  = date($this->_orgDF['php'], strtotime($value['leaveClosureEndDate']));

						array_push($leaveBalanceDetails,$leaveBalance);
					}
					
					return $leaveBalanceDetails;
				}
			}else{
				return '';
			}
		}
	}

	public function getPendingApprovalLeaveDays($employeeId,$leaveTypeId)
	{
		return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empLeaves, new Zend_Db_Expr('SUM(Total_Days)'))
										->where('Approval_Status IN (?)', array('Applied', 'Returned'))
										->where('LeaveType_Id = ?',$leaveTypeId)
										->where('Employee_Id = ?',$employeeId));
	}

	//Function to calculate the number of months till current month when the leave period is monthly, quarterly and halfyearly
	public function getPreviousCurrentLeavePeriodDetails($leavePeriodStartEndDates,$leaveClosureStartDate,$leaveClosureEndDate,$currentDate,$dateOfJoin,$leavePeriod,$eligibleDaysDetails){
		$totalNumberOfMonths = 0;
		$previousMonthsTotalEligibileDays = 0;
		$previousLeavePeriodEndDate = '';
		$currentLeavePeriodStartDate = '';
		$currentLeavePeriodEndDate = '';
		$futurePeriodStartDate ='';
		$futurePeriodEndDate ='';

		$currentDateTimeStamp=strtotime($currentDate);
		$leaveClosureEndDateTimestamp=strtotime($leaveClosureEndDate);
		$leaveClosureStartDateTimestamp=strtotime($leaveClosureStartDate);
		$dateOfJoinTimeStamp=strtotime($dateOfJoin);
		$currentDateToCompare=$currentDate;

		if( $currentDateTimeStamp >  $leaveClosureEndDateTimestamp){
			$currentDateToCompare = $leaveClosureEndDate;
			$currentDateTimeStamp = $leaveClosureEndDateTimestamp;
		} else if( $currentDateTimeStamp <  $leaveClosureStartDateTimestamp){
			$currentDateToCompare = $leaveClosureStartDate;
			$currentDateTimeStamp = $leaveClosureStartDateTimestamp;
		}

		$employeeStartDate='';
		/* If accumulateFromDate lies in between leave closure start and end date */
		if($dateOfJoinTimeStamp >= $leaveClosureStartDateTimestamp && 
		$dateOfJoinTimeStamp <= $leaveClosureEndDateTimestamp){
			$employeeStartDate = $dateOfJoin;
		}
		else if($dateOfJoinTimeStamp < $leaveClosureStartDateTimestamp){
			/* If date of join is less than the leave closure start date */
			$employeeStartDate = $leaveClosureStartDate;
		}

		if(!empty($employeeStartDate)){
			$employeeStartDateTimeStamp=strtotime($employeeStartDate);
			for($i=0;$i<=count($leavePeriodStartEndDates);$i++){
				$leavePeriodStartDateTimeStamp = strtotime($leavePeriodStartEndDates[$i][0]);
				$leavePeriodEndDateTimeStamp = strtotime($leavePeriodStartEndDates[$i][1]);

				if($totalNumberOfMonths > 0 || (($employeeStartDateTimeStamp >= $leavePeriodStartDateTimeStamp) &&
				($employeeStartDateTimeStamp <= $leavePeriodEndDateTimeStamp))){
					if(($currentDateTimeStamp >= $leavePeriodStartDateTimeStamp) && ($currentDateTimeStamp <= $leavePeriodEndDateTimeStamp)){
						$currentLeavePeriodStartDate=$leavePeriodStartEndDates[$i][0];
						$currentLeavePeriodEndDate=$leavePeriodStartEndDates[$i][1];
						if($leavePeriodStartDateTimeStamp == $leaveClosureStartDateTimestamp){
							$previousLeavePeriodEndDate = '';
						}else{
							$previousLeavePeriodEndDate = date('Y-m-d', strtotime('-1 day', $leavePeriodStartDateTimeStamp));
						}
						break;
					}else{
						if($leavePeriod == 1){
							$currentPeriodMonthNumber = date("n", $leavePeriodEndDateTimeStamp);
							$previousMonthsTotalEligibileDays += $eligibleDaysDetails[$currentPeriodMonthNumber];
						}	
						$totalNumberOfMonths += 1;
					}
				}
			}
		}

		// when the currentLeavePeriodEndDate is less than leaveClosureEndDate we need to find the future start date end date.
		if(!empty($currentLeavePeriodEndDate))
		{
			$currentLeavePeriodEndDateTimeStamp = strtotime($currentLeavePeriodEndDate);
			if(($currentLeavePeriodEndDateTimeStamp < $leaveClosureEndDateTimestamp))
			{
				$futurePeriodStartDate = date('Y-m-d', strtotime('+1 day', $currentLeavePeriodEndDateTimeStamp));
				$futurePeriodEndDate   = $leaveClosureEndDate;
			}
		}

		return array(
			'numberOfPreviousLeavePeriod' =>$totalNumberOfMonths,
			'previousMonthsTotalEligibileDays' => $previousMonthsTotalEligibileDays,
			'previousLeavePeriodEndDate'=>$previousLeavePeriodEndDate,
			'currentLeavePeriodStartDate' =>$currentLeavePeriodStartDate,
			'currentLeavePeriodEndDate' =>$currentLeavePeriodEndDate,
			'futurePeriodStartDate' =>$futurePeriodStartDate,
			'futurePeriodEndDate' =>$futurePeriodEndDate,
			'employeeStartDate' => $employeeStartDate,
			'currentDateToCompare' => $currentDateToCompare
		);
	}

	//Calculate employee total eligible days when the accumulate eligible days is enabled
	public function getAccumulationEnabledLeaveTypeEligibleDays($leaveTypeDetails,$leaveHistory,$employeeLeaveDetails){
		$currentLeavePeriodTotalEligibleDays = $oneLeavePeriodApplicableDays = 0;

		$accumulateFromDate = $employeeLeaveDetails['accumulateFromDate'];
		$currentDate = $employeeLeaveDetails['currentDate'];
		$leavePeriodStartEndDates = $employeeLeaveDetails['leavePeriodStartEndDates'];
		$leaveClosureStartDate = $employeeLeaveDetails['leaveClosureStartDate'];
		$leaveClosureEndDate = $employeeLeaveDetails['leaveClosureEndDate'];
		$empTotCoDays = $employeeLeaveDetails['empTotCoDays'];
		$leaveTakenWithAppliedDays = $employeeLeaveDetails['leaveTakenWithAppliedDays'];
		$dateOfJoin = $employeeLeaveDetails['dateOfJoin'];
		$monthlyEligibleDaysDetails = $employeeLeaveDetails['monthlyEligibleDaysDetails'];

		//Call the function to find the previous and current leave period details
		$previousCurrentLeavePeriodDetails = $this->getPreviousCurrentLeavePeriodDetails($leavePeriodStartEndDates,$leaveClosureStartDate,$leaveClosureEndDate,$currentDate,$accumulateFromDate,$leaveTypeDetails['Period'],$monthlyEligibleDaysDetails);
		if($previousCurrentLeavePeriodDetails['numberOfPreviousLeavePeriod'] > 0){
			//Calculate previous leave period total eligible days if leave period is monthly
			if($leaveTypeDetails['Period'] == 1){
				$previousLeavePeriodTotalEligibleDays = $previousCurrentLeavePeriodDetails['previousMonthsTotalEligibileDays'];
			}else{
				$previousLeavePeriodTotalEligibleDays = $previousCurrentLeavePeriodDetails['numberOfPreviousLeavePeriod'] * $leaveTypeDetails['Eligible_Days_Based_On_Period'];
			}
			//Previous leave period total eligible days
			$previousLeavePeriodTotalEligibleDays = min($leaveHistory['Eligible_Days'],$previousLeavePeriodTotalEligibleDays);
		}else{
			$previousLeavePeriodTotalEligibleDays = 0;
		}

		//Calculate previous leave period total eligible days if leave period is monthly
		if($leaveTypeDetails['Period'] == 1){
			$currentPeriodMonthNumber = $month = date("n", strtotime($previousCurrentLeavePeriodDetails['currentLeavePeriodEndDate']));
			$currentPeriodApplicableEligibileDays = $monthlyEligibleDaysDetails[$currentPeriodMonthNumber];
		}else{
			$currentPeriodApplicableEligibileDays = $leaveTypeDetails['Eligible_Days_Based_On_Period'];
		}
		//Compare yearly leave balance and total eligible days till current leave period
		$currentLeavePeriodTotalEligibleDays = min($leaveHistory['Eligible_Days'],$currentPeriodApplicableEligibileDays);

		//Sum of previous and current leave period eligibility
		$eligibleDaysTillCurrentPeriod = $previousLeavePeriodTotalEligibleDays+$currentLeavePeriodTotalEligibleDays;
		$eligibleDaysTillCurrentPeriod = min($eligibleDaysTillCurrentPeriod,$leaveHistory['Eligible_Days']);

		//Current Eligibility = Total Eligible days Till Current Month + Total CO days - Leave taken(Including Approved and Applied leave days)
		$actualEligibleDaysTillCurrentPeriod = ($eligibleDaysTillCurrentPeriod+ $empTotCoDays) - $leaveTakenWithAppliedDays;
		
		return $actualEligibleDaysTillCurrentPeriod;
	}

	//Function to get the eligible days for a leave period from the organization level config table
	public function getEligibleDaysForLeavePeriod($leaveTypeId){
		return ($this->_db->fetchPairs($this->_db->select()->from(array($this->_ehrTables->orgLeavePeriodEligibleDays), array('Month_Id','Eligible_Days'))
					->where('LeaveType_Id = ?', $leaveTypeId)));
	}

	//fuction to validate the restricted adjacent leaves
	public function validateRestrictedAdjacentLeaves($args)
	{
		$message = '';
		$resultDetails= array();
		$orgCode = $this->_ehrTables->getOrgCode();
		$curl = curl_init();
		$method = "POST";
		$apiBaseUrl = Zend_Registry::get('employeeSelfServiceRoBaseURL');
		if(!empty($apiBaseUrl))
		{
			$url = $apiBaseUrl;
		}	
		else
		{
			curl_close($curl);
			return array('success' => false, 'message' => 'Something went wrong in validating the restricted adjacent leaves. Please contact system admin.');
		}

		/* Create an object */
		$apiInputs = new \stdClass();
		$apiInputs->leaveId = (int)$args['leaveId'];
		$apiInputs->employeeId = (int)$args['employeeId'];
		$apiInputs->leaveTypeId = (int)$args['leaveType'];
		$apiInputs->leaveFrom = $args['leaveFrom'];
		$apiInputs->leaveTo = $args['leaveTo'];
		
		/* Convert the object to JSON */
		$apiInputsJSON = json_encode($apiInputs);
		$requestBody = '{
			"variables" : '.$apiInputsJSON.',
			"query":"query validateAdjacentLeave($leaveId:Int!,$employeeId: Int!,$leaveTypeId:Int!,$leaveFrom: Date!,$leaveTo: Date!) { validateAdjacentLeave(leaveId: $leaveId, employeeId:$employeeId,leaveTypeId: $leaveTypeId, leaveFrom:$leaveFrom,  leaveTo:$leaveTo) {errorCode message validationResult }}"
		}';

		curl_setopt($curl, CURLOPT_POST, 1);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $requestBody);
		curl_setopt($curl, CURLOPT_URL, $url);
		
		//Set API headers
		$apiHeaders = $this->_dbCommonFun->getApiHeaders($orgCode);
		curl_setopt($curl, CURLOPT_HTTPHEADER, $apiHeaders);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

		// EXECUTE:
		$result = curl_exec($curl);
		curl_close($curl);
		if(!$result)
		{
			return array('success' => false, 'message' => 'There seem to be some technical difficulties in validating the restricted adjacent leaves. Please contact system admin.');
		}
		//Handle API response
		$responseData = json_decode($result,true);
		if(isset($responseData['message']) && (isset($responseData['message']['message']) && $responseData['message']['message'] == 'API gateway timeout.')){
			$message = 'Unable to validate the restricted adjacent leaves. Please try again.';
		}else {
			if (isset($responseData['data']) && isset($responseData['data']['validateAdjacentLeave'])
			&& !empty($responseData['data']['validateAdjacentLeave'])){
				$resultDetails = json_decode($responseData['data']['validateAdjacentLeave']['validationResult'],true);
				$message = '';
			} else if(isset($responseData['errors']) && count($responseData['errors']) > 0) {
				if(isset($responseData['errors'][0]['message'])){
					$errorResponse = $responseData['errors'][0];
					$errorCode = $responseData['errors'][0]['extensions']['code'];
					switch($errorCode){
						case 'ELR0132'://Sorry, an error occurred while validating the adjacent leaves are restricted or not. Please contact the platform administrator
							$message =  $responseData['errors'][0]['message'];
							break;
						default:
							$message =  'Sorry, an error occurred while validating the adjacent leaves are restricted or not. Please contact the platform administrator';
							break;
					}
				}else{
					$message = 'Something went wrong while validating the restricted adjacent leaves. Please contact platform administrator.';
				}
			} else {
				$message = 'Oops! Something went wrong in validating the restricted adjacent leaves. Please contact the platform administrator.';
			}
		}
		return array('success' => ($message) ? false : true, 'message' => $message,'validationResult'=>$resultDetails);
	}

	public function getSemiValue($leaveTypeIds,$minimumLeaveDuration=''){
		if(empty($minimumLeaveDuration)){
			$leaveTypeMinimumDurationDetails = $this->getLeaveTypeMinimumDuration($leaveTypeIds);
			$minimumLeaveDuration = (!empty($leaveTypeMinimumDurationDetails) && isset($leaveTypeMinimumDurationDetails[$leaveTypeIds])) ? $leaveTypeMinimumDurationDetails[$leaveTypeIds][0] : '';
		}
		$semiValue;
		switch($minimumLeaveDuration){
			case 'Quarter Day':
				$semiValue = 4;
				break;
			case 'Full Day':
				$semiValue = 1;
				break;
			case 'Half Day':
			default:
				$semiValue = 2;
				break;
		}
		return $semiValue;
	}

	//fuction to validate the pre approval request
	public function updateMonthlyLeaveAccrual($month,$year)
	{
		$message 	= '';
		$orgCode 	= $this->_ehrTables->getOrgCode();
		$curl 	 	= curl_init();
		$method 	= "POST";
		$apiBaseUrl = Zend_Registry::get('employeeSelfServiceWoBaseURL');
		if(!empty($apiBaseUrl))
		{
			$url = $apiBaseUrl;
		}	
		else
		{
			curl_close($curl);
			return array('success' => false, 'message' => 'Something went wrong while doing monthly leave accrual. Please contact system admin.');
		}
		
		/* Create an object */
		$apiInputs 			= new \stdClass();
		$apiInputs->month 	= (int)$month;
		$apiInputs->year 	= (int)$year;
		
		/* Convert the object to JSON */
		$apiInputsJSON = json_encode($apiInputs);
		$requestBody = '{
			"variables" : '.$apiInputsJSON.',
			"query":"mutation addLeaveAccrual($month:Int!, $year:Int!) { addLeaveAccrual(month:$month, year: $year) { errorCode message} }"
		}';

		curl_setopt($curl, CURLOPT_POST, 1);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $requestBody);
		curl_setopt($curl, CURLOPT_URL, $url);
		
		//Set API headers
		$apiHeaders = $this->_dbCommonFun->getApiHeaders($orgCode);
		curl_setopt($curl, CURLOPT_HTTPHEADER, $apiHeaders);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

		// EXECUTE:
		$result = curl_exec($curl);
		curl_close($curl);
		if(!$result)
		{
			return array('success' => false, 'msg' => 'There seem to be some technical difficulties in validating the monthly leave accrual. Please contact system admin.','type'=>'warning','responseData'=>$result);
		}
		//Handle API response
		$responseData = json_decode($result,true);
		if(isset($responseData["data"]["addLeaveAccrual"]["message"]) && (isset($responseData["data"]["addLeaveAccrual"]["message"]) && $responseData["data"]["addLeaveAccrual"]["message"] == 'API gateway timeout.'))
		{
			$message = 'Unable to validate the monthly leave accrual request. Please try again.';
			return array('success' => false, 'msg' => $message,'type'=>'warning','responseData'=>$responseData);
		}
		else if(isset($responseData["data"]["addLeaveAccrual"]["message"]) && (strtolower($responseData["data"]["addLeaveAccrual"]["message"]) == 'monthly leave accrual process completed successfully'))
		{
			$message = $responseData["data"]["addLeaveAccrual"]["message"];
			return array('success' => true, 'msg' => $message,'type'=>'success','responseData'=>$responseData);
		}
		else if(isset($responseData["data"]["addLeaveAccrual"]["message"]))
		{
			$message = $responseData["data"]["addLeaveAccrual"]["message"];
			return array('success' => false, 'msg' => $message,'type'=>'warning','responseData'=>$responseData);
		}
		else
		{
			$message = 'Unable to validate the monthly leave accrual request. Please try again.';
			return array('success' => false, 'msg' => $message,'type'=>'warning','responseData'=>$responseData,'apiBaseUrl'=>$apiBaseUrl,'requestBody'=>$requestBody,'apiHeaders'=>$apiHeaders);
		}
	}


/**
 * Retrieves the most recent period end date from the leave accruals table and
 * returns an array with the keys 'text' and 'value'. The value of 'text' is a
 * string representation of the month and year and the value of 'value' is a
 * string in the format 'n,Y' where n is the month and Y is the year.
 *
 * If the most recent period end date is empty, it will return the current month
 * and year.
 *
 * @return array An array containing the month and year details.
 */
	public function getEmpLeaveAccrualDetails()
	{
		$periodEndDate = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empLeaveAccruals, new Zend_Db_Expr('MAX(Period_End_Date)')));
		$dbPayslip 				= new Payroll_Model_DbTable_Payslip();
		if(empty($periodEndDate))
		{
			$currentDate 			= date('Y-m-d');
			$startDate 				= date('Y-m-d', strtotime('-5 month', strtotime($currentDate)));
		}
		else
		{
			$startDate = date('Y-m-d', strtotime('+1 day', strtotime($periodEndDate)));
		}
		$salaryDetails     		= $dbPayslip->getSalaryDateRange(date('m',strtotime($startDate)),date('Y',strtotime($startDate)),strtotime($startDate));
		$currentMonthValue 		= date('n,Y',strtotime($salaryDetails['Last_SalaryDate']));
		$currentMonth 			= date('F,Y',strtotime($salaryDetails['Last_SalaryDate']));
		return array('text'=>$currentMonth,'value'=>$currentMonthValue);
	}

/**
 * Retrieves a list of active leave type IDs that have entitlement types
 * of either 'Attendance' or 'Leave'.
 *
 * @return array An array of leave type IDs matching the specified criteria.
 */

	public function getEntitlementLeaveExist()
    {
		$entitlementType=array('Attendance','Leave');
		return $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->leavetype, array('LeaveType_Id'))
									->where('Leave_Status =?', 'Active')
									->where('Entitlement_Type IN (?)',$entitlementType)
									->order('Leave_Name ASC'));
    }

	
	public function getLeaveDetails($leaveIds){
		return $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->empLeaves, array('Employee_Id','Leave_Id','Total_Days','LeaveType_Id','Approval_Status'))
								->where('Leave_Id IN (?)', $leaveIds));
	}

/**
 * Calculates and returns the leave closure dates based on the given start date
 * and an optional number of years to add. The returned array contains the 
 * financial start date, financial end date, and the company year.
 *
 * @param string $leaveClosureStartDate The start date for the leave closure in 'Y-m-d' format.
 * @param int $leaveClosureEndYear Optional. The number of years to extend the leave closure. Default is 50 years.
 * @return array An associative array with 'finstart', 'finend', and 'coyear' keys.
 */

	public function replenishmentLeaveClosureDate($leaveClosureStartDate,$leaveClosureEndYear=50)
	{
		$leaveClosureDate = [];
		if (!empty($leaveClosureStartDate)) {
			$leaveClosureDate['finstart'] = $leaveClosureStartDate;
			$endDate = date('Y-m-d', strtotime("+$leaveClosureEndYear year", strtotime($leaveClosureDate['finstart'])));
			$leaveClosureDate['finend'] = date('Y-m-d', strtotime("-1 day", strtotime($endDate)));
			$leaveClosureDate['coyear'] = date('Y', strtotime($leaveClosureDate['finstart']));
		}
		return $leaveClosureDate;
	}


/**
 * Runs the replenishment closure for a given leave Id, if the leave is eligible for replenishment
 * during leave approval.
 * 
 * @param int $eligibleLeaveId The leave Id for which the replenishment closure will be run.
 * @return int 1 if the replenishment closure was run, 0 otherwise.
 */
	public function replenishmentDuringLeaveApproval($eligibleLeaveId)
	{
		$runReplinishmentClosure = 0;
		$leaveDetails = $this->getReplenishmentLeaveDetails($eligibleLeaveId);
		if(!empty($leaveDetails)) 
		{
			if($leaveDetails['Leave_Closure_Based_On']=='Limited Replenishment on Approval' && !empty($leaveDetails['leaveTypeReplenishmentLimit']))
			{
				if($leaveDetails['employeeReplenishmentLimit'] < $leaveDetails['leaveTypeReplenishmentLimit'])
				{
					$employeeReplenishmentLimit = $leaveDetails['employeeReplenishmentLimit']+1;
					$runReplinishmentClosure = $this->runReplinishmentClosure($leaveDetails,$employeeReplenishmentLimit);
				}
			}
			else if ($leaveDetails['Leave_Closure_Based_On']=='Unlimited Replenishment on Approval') 
			{
				$employeeReplenishmentLimit = 0;
				$runReplinishmentClosure = $this->runReplinishmentClosure($leaveDetails,$employeeReplenishmentLimit);
			}
		}
		return $runReplinishmentClosure;
	}


/**
 * Runs the replenishment closure for a given leave Id, if the leave is eligible for replenishment
 * during leave approval.
 * 
 * @param array $leaveDetails The leave details array containing the leave Id, employee Id, leave type Id, 
 *      total days, leave closure based on, leave enforcement configuration, start date, and end date.
 * @param int $employeeReplenishmentLimit The employee replenishment limit.
 * @return int 1 if the replenishment closure was run, 0 otherwise.
 */
	public function runReplinishmentClosure($leaveDetails,$employeeReplenishmentLimit)
	{
		$insertEmployeeEligibleLeave = 0; 
		$leaveBalance = $this->getEmpLeaveTypeBalance($leaveDetails['Employee_Id'], $leaveDetails['LeaveType_Id']); 
		if (empty($leaveBalance) || (float)$leaveBalance === 0.0)
		{
			if($leaveDetails['Leave_Enforcement_Configuration']==1)
			{
				$totalDays=  $leaveDetails['Total_Days'];
			}
			else if($leaveDetails['Leave_Enforcement_Configuration']==4)
			{
				$totalDays = $this->calculateMaternityLeaveSlabTotalDays($leaveDetails);                
			}
			else
			{
				$totalDays = 0;
			}

			$leaveClosureStartDate   	  = date('Y-m-d', strtotime("+1 day", strtotime($leaveDetails['End_Date'])));
			$leaveClosureDates 			  = $this->replenishmentLeaveClosureDate($leaveClosureStartDate);
			if(!empty($leaveClosureDates))
			{
				$employeeEligibleLeave[]  = array('LeaveType_Id'   			=> $leaveDetails['LeaveType_Id'],
												'Employee_Id'   			=> $leaveDetails['Employee_Id'],
												'Eligible_Days' 			=> $totalDays,
												'Replenishment_Limit'		=> $employeeReplenishmentLimit,
												'CO_Year'      				=> $leaveClosureDates['coyear'],
												'LE_Year'       			=> $leaveClosureDates['coyear'],
												'Leave_Closure_Start_Date'  => $leaveClosureDates['finstart'],
												'Leave_Closure_End_Date'    => $leaveClosureDates['finend']);

				$insertEmployeeEligibleLeave 	= $this->insertEmployeeEligibleLeave($employeeEligibleLeave,'','leave-closure');
			}
			return $insertEmployeeEligibleLeave;
		}
	}


/**
 * Calculates the total maternity leave days for a given employee and leave type.
 *
 * Gets the current number of children for the employee and then gets the total maternity leave days from the maternity leave slabs table.
 *
 * @param array $leaveDetails An array containing the employee id and leave type id.
 * @return int The total maternity leave days.
 */
	public function calculateMaternityLeaveSlabTotalDays($leaveDetails)
	{
		$dbMaternityleave = new Employees_Model_DbTable_MaternitySlabs();  
		$qryChildCount = $this->_db->select()->from(array('ED'=>$this->_ehrTables->empDependent),array(new Zend_Db_Expr('count(Dependent_Id) as Child_Count')))
																						->where('ED.Employee_Id= ?', $leaveDetails['Employee_Id'])
																						->where('ED.Relationship IN (?)',array('Son ','Daughter'));
		$childCount = $this->_db->fetchOne($qryChildCount);
		$childRange	   = $childCount+1;
		$eligibleDays  = $dbMaternityleave->maternityLeaveSlab($leaveDetails['Employee_Id'],$leaveDetails['LeaveType_Id'],$childRange);
		return $eligibleDays;
	}
	/**
	 * Gets the leave details for a given eligible leave id.
	 *
	 * @param int $eligibleLeaveId
	 *
	 * @return array
	 */

	public function getReplenishmentLeaveDetails($eligibleLeaveId)
	{
		$approvalStaus = array('Cancelled','Rejected');

		return $this->_db->fetchRow($this->_db->select()->from(array('L'=>$this->_ehrTables->empLeaves),array('L.Employee_Id','L.End_Date'))
									->joinInner(array('LT'=>$this->_ehrTables->leavetype), 'L.LeaveType_Id=LT.LeaveType_Id',array('LT.LeaveType_Id','LT.Leave_Enforcement_Configuration','LT.Leave_Closure_Based_On','LT.Total_Days','LT.Replenishment_Limit as leaveTypeReplenishmentLimit'))
									->joinInner(array('EL'=>$this->_ehrTables->empEligbleLeave), 'L.LeaveType_Id=EL.LeaveType_Id AND L.Employee_Id=EL.Employee_Id',array('EL.Replenishment_Limit as employeeReplenishmentLimit'))
									->where('EL.Eligible_Leave_Id = ?', $eligibleLeaveId)
									->where('L.Approval_Status NOT IN (?)',$approvalStaus)
									->order('L.End_Date DESC'));
	}

	
/**
 * Checks if the leave closure has been completed for a given leave type id.
 *
 * Gets the last record from the audit eligible leaves table for the given leave type id
 * and audit reason 'leave-closure'. If the record exists, it means the leave closure
 * has been completed.
 *
 * @param int $leaveTypeId The leave type id.
 *
 * @return array An array containing the audit eligible leave details.
 */
/******  f954dee8-71de-4db0-945e-7bae83cb8b4d  *******/
	public function isLeaveClosureCompleted($leaveTypeId)
	{
		$qryLeaveClosureCompleted = $this->_db->select()->from(array('EL'=>$this->_ehrTables->auditEmpEligbleLeave),array('*'))
																		->order('EL.Audit_Eligible_Leave_Id DESC')
																		->where('EL.Audit_Reason = ?','leave-closure')
																		->where('EL.LeaveType_Id= ?',$leaveTypeId);
		$leaveClosureCompleted = $this->_db->fetchAll($qryLeaveClosureCompleted);
		return $leaveClosureCompleted;
	}

	public function getLeaveTypeCodeFromSlab($leaveTypeId,$codeBasedOnAction){
		$columnName = ($codeBasedOnAction === 'Approve') ? 'Approve_Leave_Type_Code' : 'Cancel_Leave_Type_Code';

		return $this->_db->fetchCol($this->_db->select()
			->from($this->_ehrTables->leaveTypesPaymentConfigurationSlab, array($columnName))
			->where('LeaveType_Id = ?', $leaveTypeId));
	}


	/**
	 * Updates the early checkout ignore status for an employee attendance summary.
	 * 
	 * @param array $inputArray Array containing:
	 *   - leaveId: ID of the early checkout record
	 *   - summaryDate: Date of the attendance summary
	 *   - employeeId: ID of the employee
	 *   - approvalStatus: Status of the approval
	 *   - sessionId: ID of the current session
	 * 
	 * @return bool Returns true if update successful, false if attendance summary not found
	 */
	public function updateEarlyCheckoutIgnoreStatus($inputArray) {
		extract($inputArray, EXTR_SKIP);
		// Get the attendance summary ID for the early checkout leave
		$attendanceSummaryId = $this->_db->fetchOne(
			$this->_db->select()
				->from(array('EAS' => $this->_ehrTables->employeeAttendanceSummary), 'EAS.Attendance_Summary_Id')
				->where('EAS.Early_Checkout_Leave_Id = ?', $leaveId)
		);
	
		if ($attendanceSummaryId) {
			// Update the early checkout ignore status
			$updateDetails= array(
				'Early_Checkout_Ignore_Status' => 'Yes',
				'Updated_On' => date('Y-m-d H:i:s'),
				'Updated_By' => $sessionId
			);
			
			$where = array('Attendance_Summary_Id = ?' => $attendanceSummaryId);
			$updated =$this->_db->update($this->_ehrTables->employeeAttendanceSummary, $updateDetails, $where);	

			//update the system log
			//Log Message: Early checkout is ignored for the attendance date 2025-04-01 and for the employee 670 as the early checkout leave is Rejected - 678
			$trackSysLog =  $this->_dbCommonFun->updateResult (array(
				'updated'        => $updated,
				'action'         => '',
				'trackingMsg'    => 'Early checkout is ignored for the attendance date '.$summaryDate.' and for the employee '.$employeeId.' as the early checkout leave is '.$approvalStatus.' - ',
				'trackingColumn' => $attendanceSummaryId,
				'formName'       => 'Early Checkout',
				'sessionId'      => $sessionId));
	
			$response = array('earlyCheckout' => 1,'updated' => $updated);		
			return $response;
		}
		
		$response = array('earlyCheckout' => 0,'updated' => 0);	
		return $response;
	}
}