[production]
phpSettings.display_startup_errors = 0
phpSettings.display_errors = 0
includePaths.library = APPLICATION_PATH "/../library"
bootstrap.path = APPLICATION_PATH "/Bootstrap.php"
bootstrap.class = "Bootstrap"
appnamespace = "Application"
resources.frontController.params.displayExceptions = 0

resources.modules[]=""
resources.frontController.params.prefixDefaultModule = "1"
resources.frontController.defaultModule = "default"
resources.view.doctype = "XHTML1_TRANSITIONAL"

resources.db.adapter = "PDO_MYSQL"
resources.db.params.charset = "utf8"
resources.db.params.port = 3306

resources.db.params.persistent = true
resources.db.params.compress = true

resources.mail.transport.type     = smtp
resources.mail.transport.auth     = "login"
resources.mail.transport.ssl      = "ssl"
resources.mail.transport.port     = 465
resources.mail.transport.register = true

resources.session.cache_limiter = must-revalidate
resources.view.charset = "UTF-8"
resources.view.helperPath.View_Helper = APPLICATION_PATH "/views/helpers"
resources.layout.layoutPath = APPLICATION_PATH "/layouts/scripts/"

mobileapps.production = 1
resources.frontController.moduleDirectory = APPLICATION_PATH "/modules"

mobileapps.domain = hrapp.co.in
mobileapps.productlogo=0

mobileapps.region = ap-south-1
mobileapps.version = 2006-03-01
mobileapps.bucketName = caprice-dev-stage
mobileapps.imageBucket = s3.hrapp-dev-public-asset
mobileapps.logoBucket = s3.hrapp-dev-public-images
mobileapps.smregion = ap-south-1
mobileapps.signedURLValidity = '1200 seconds'
mobileapps.reimbursementSignedURLValidity = '604800 seconds'

mobileapps.secretname = hrapp-stage
mobileapps.ocrapiurlprefix = https://6e8og9bg90.execute-api.ap-south-1.amazonaws.com/dev/
mobileapps.iciciApiBaseUrl = https://5nr7ovdyq8.execute-api.ap-south-1.amazonaws.com/dev/
mobileapps.iciciCIBBaseURL = https://cibnext.icicibank.com/corp/AuthenticationController
mobileapps.ccAvenueWorkingKey = ""
mobileapps.ccAvenueAccessCode = ""
mobileapps.ccAvenueURL = ""
mobileapps.facebookURL = https://www.facebook.com/HRAPPONCLOUD/
mobileapps.twitterURL = https://twitter.com/hrapponcloud
mobileapps.linkedinURL = https://www.linkedin.com/showcase/********
mobileapps.googleURL = "https://www.google.com/maps/dir//hrapp/data=!4m6!4m5!1m1!4e2!1m2!1m1!1s0x3ba8562e7d3f6c1b:0xb8f93ff5aa0725c8?sa=X&ved=2ahUKEwiw59nourbiAhVZT30KHVnQBjsQ9RcwFXoECAkQDg"
mobileapps.websiteURL = https://hrapp.in/
mobileapps.atsBaseURL= https://api.hrapp.co.in/ats/graphql
mobileapps.clientipUrl= "https://api.ipify.org?format=json"
mobileapps.integrationBaseURL= https://api.hrapp.co.in/integration/rographql
mobileapps.trstscoreBaseURL= https://api.hrapp.co.in/trstscore/wographql
mobileapps.workflowEngineInitiateBaseUrl=https://api.hrapp.co.in/workflowEngine/workflow/initiate
mobileapps.coreHrRoBaseUrl=https://api.hrapp.co.in/coreHr/rographql
mobileapps.hrappBeRoBaseUrl=https://api.hrapp.co.in/hrappBe/roGraphql
mobileapps.employeeSelfServiceRoBaseURL=https://api.hrapp.co.in/employeeSelfService/rographql
mobileapps.employeeSelfServiceWoBaseURL=https://api.hrapp.co.in/employeeSelfService/wographql
mobileapps.coreHrWoBaseUrl=https://api.hrapp.co.in/coreHr/wographql
mobileapps.payrollAdminWoBaseUrl= https://api.hrapp.co.in/payrollAdmin/woGraphql
mobileapps.integrationWoExternalBaseURL= https://api.hrapp.co.in/integration/externalauth
mobileapps.batchProcessingExternalBaseURL=https://api.hrapp.co.in/batchProcessing/external

mobileapps.entomoAccessTokenUrl=epms/noAuth/token
mobileapps.entomoLoginUrl=epms/oatlogin
mobileapps.entomoRefreshTokenUrl=epms/refresh

mobileapps.firebaseApiKey = AIzaSyB-QCDxis2HG3hHIreLPiidSlN_eCyi3m8
mobileapps.firebaseAuthDomain = hrappsample.firebaseapp.com
mobileapps.firebaseDatabaseURL = https://hrappsample.firebaseio.com
mobileapps.firebaseProjectId = hrappsample
mobileapps.firebaseStorageBucket = hrappsample.appspot.com
mobileapps.firebaseMessagingSenderId = 514098503657
mobileapps.firebaseAppId = 1:514098503657:web:8f1e1d5922aa690c
mobileapps.refreshTokenAPIUrl = https://securetoken.googleapis.com/v1/

mobileapps.truleadFirebaseApiKey = AIzaSyDqZn6gno9dipDFBl4RSRBgCVX0-rb3BaU
mobileapps.truleadFirebaseAuthDomain = hrapptrulead.firebaseapp.com
mobileapps.truleadFirebaseDatabaseURL = https://hrapptrulead-default-rtdb.firebaseio.com
mobileapps.truleadFirebaseProjectId = hrapptrulead
mobileapps.truleadFirebaseStorageBucket = hrapptrulead.appspot.com
mobileapps.truleadFirebaseMessagingSenderId = 805458700336
mobileapps.truleadFirebaseAppId = 1:805458700336:web:f9d557a877255c0c01a5f5

mobileapps.appSecretKey = a60be24833cbd6f0e424e133b0f55c96
mobileapps.appVersion[] = 120201912
mobileapps.appVersion[] = 121202001
mobileapps.appVersion[] = 122202002
mobileapps.appVersion[] = 123202002
mobileapps.appVersion[] = 124202003
mobileapps.appVersion[] = 125202003
mobileapps.appVersion[] = 132200000
mobileapps.appVersion[] = 132300000
mobileapps.appVersion[] = 133000000
mobileapps.appVersion[] = 134000000
mobileapps.appVersion[] = 135000000
mobileapps.appVersion[] = 136000000
mobileapps.appVersion[] = 137000000

mobileapps.productlogo = 0
mobileapps.redirectionurl = https://www.hrapp.co.in/appmanager/mobilemanager

resources.db.params.dbname = hrapp_managerdb
resources.db.params.username = ""
resources.db.params.encrypt = 0
resources.db.params.password = ""
resources.db.profiler.enabled = false
resources.mail.transport.host     = ""
resources.mail.defaultfrom.email = "<EMAIL>"