<?php
//=========================================================================================
//=========================================================================================
/* Program        : Billing.php									   						*/
/* Property of Caprice Technologies Pvt Ltd,
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,
* Coimbatore, Tamilnadu, India.															*/
/* All Rights Reserved.            														*/
/* Use of this material without the express consent of Caprice Technologies
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law.*/
/*                                                                                    	*/
/* Description    : MQL Query to get invoice billing details							*/
/*                                                                                   	*/
/*                                                                                    	*/
/*Revisions      :                                                                    	*/
/*Version    Date           Author                  Description                       	*/
/*0.1        30-May-2013    Narmadha               Initial Version         	            */
/*                                                                                    	*/
/*                                                                                    	*/
/*                                                                                    	*/
//=========================================================================================
//=========================================================================================

class Default_Model_DbTable_Billing extends Zend_Db_Table_Abstract
{
    protected $_db        = null;
    protected $_salesDb   = null;
    protected $_ehrTables = null;
    protected $_orgDF     = null;
	
    public function init()
    {
        $this->_ehrTables    = new Application_Model_DbTable_Ehr();
        $this->_accessRights = new Default_Model_DbTable_AccessRights();
        $this->_db           = Zend_Registry::get('subHrapp');
        $this->_salesDb      = Zend_Registry::get('Hrapp');
        $this->_orgDF        = $this->_ehrTables->orgDateformat();
    }
    
	public function encryptDecrypt($string, $decrypt)
    {
    	if($decrypt)
    	{
    		$string = urldecode($string);
    		$decrypted = base64_decode($string);
    		return $decrypted;
    	}else{
    		$encrypted = base64_encode($string);
    		return urlencode($encrypted);
    	}
    }
    
	/**
	 *	listBilling() used list billing tansaction details in grid
	*/
	//public function listBilling ($sortField, $sortOrder, $page, $rows, $searchAll, $sessionId)
    public function listBilling($page, $rows, $sortField, $sortOrder, $searchAll, $searchDetails, $sessionId)
    {
    	$empDetails = $this->_db->fetchRow($this->_db->select()
									->from(array('E'=>$this->_ehrTables->empPersonal),
											   array(new Zend_Db_Expr('CONCAT(E.Emp_First_Name,\' \',E.Emp_Middle_Name,\' \',E.Emp_Last_Name) as Employee_Name')))
									
									->joinLeft(array('EC'=>$this->_ehrTables->empContacts), 'EC.Employee_Id=E.Employee_Id',
										   array('EC.cApartment_Name', 'EC.cStreet_Name', 'EC.Mobile_No', 'EC.cCity', 'EC.cState',
												 'EC.cPincode'))
									
									->joinLeft(array('C'=>$this->_ehrTables->country), 'C.Country_Code=EC.cCountry',
											   array('C.Country_Name'))
									
									->joinLeft(array('J'=>$this->_ehrTables->empJob), 'J.Employee_Id=E.Employee_Id', array('J.Emp_Email'))
									
									->where('EC.Employee_Id = ?', $sessionId));
		
    	$streetName   = $this->encryptDecrypt($empDetails['cStreet_Name'],0);
    	$city         = $this->encryptDecrypt($empDetails['cCity'],0);
    	$state        = $this->encryptDecrypt($empDetails['cState'],0);
    	$country      = $this->encryptDecrypt($empDetails['Country_Name'],0);
    	$pincode      = $this->encryptDecrypt($empDetails['cPincode'],0);
    	$mobileNo     = $this->encryptDecrypt($empDetails['Mobile_No'],0);
    	$employeeName = $this->encryptDecrypt($empDetails['Employee_Name'],0);
    	$employeeMail = $this->encryptDecrypt($empDetails['Emp_Email'],0);
    	
    	$date      = new DateTime();
    	$timeStamp = $date->getTimestamp();
		$timeStamp = $this->encryptDecrypt($timeStamp, 0);
    	$orgCode   = $this->_ehrTables->getOrgCode();
		$orgCode1  = $this->encryptDecrypt($orgCode,0);
    	$newStatus = array('Paid','Initiated','Unpaid');
		
		switch ($sortField)
		{
			case 1: $sortField = 'Generated_Date'; break;
			case 2: $sortField = 'No_Active_Employees'; break;
			case 3: $sortField = 'No_Inactive_Employees'; break;
			case 4: $sortField = 'Total_Amount'; break;
			case 5: $sortField = 'Status'; break;
		}
		
    	$qryBilling = $this->_salesDb->select()
							->from($this->_ehrTables->billingTransaction,
								   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS Invoice_No as Count'), 'Invoice_No', 'Generated_On',
										 'Amt_Active_Employees', 'Amt_Inactive_Employees', 'Total_Amount',
										 'No_Active_Employees', 'No_Inactive_Employees','Outstanding_Balance', 'Discount_Amount',
										 'Status'=>new Zend_Db_Expr('(CASE WHEN `Transaction_Status` = \'Initiated\' THEN \'Unpaid\' ELSE `Transaction_Status` END)'),
										 'Time_Stamp'=>new Zend_Db_Expr("'$timeStamp'"),'Org_Code'=>new Zend_Db_Expr("'$orgCode1'"),
										 'StreetName'=>new Zend_Db_Expr("'$streetName'"),'City'=>new Zend_Db_Expr("'$city'"),
										 'State'=>new Zend_Db_Expr("'$streetName'"),'Country'=>new Zend_Db_Expr("'$country'"),
										 'Pincode'=>new Zend_Db_Expr("'$pincode'"),'Mobile_No'=>new Zend_Db_Expr("'$mobileNo'"),
										 'Employee_Name'=>new Zend_Db_Expr("'$employeeName'"),
										 'Employee_Email'=>new Zend_Db_Expr("'$employeeMail'"),
										 'Generated_Date'=> new Zend_Db_Expr('Date_format(Generated_On, "'.$this->_orgDF['sql'].'")'),
										 'DT_RowClass' => new Zend_Db_Expr('"billing"'),
										 'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', Invoice_No)"),
                                         'Audit'=> new Zend_Db_Expr("(".$this->_db->select()->distinct()
																								 ->from(array('BA'=>$this->_ehrTables->billingAudit),'Billing_Id')
                                                                                                 ->where('BA.Billing_Id = Invoice_No').")")))
							
							->where('Org_Code = ?', $orgCode)
							->where('Transaction_Status IN (?)', $newStatus)
							->order("$sortField $sortOrder")
							->limit($rows, $page);
		
		if (!empty($searchAll) && $searchAll != null)
		{
			$conditions = $this->_db->quoteInto('Transaction_Status = ?', $searchAll);
			
			if ($searchAll == 'Initiated')
			{
				$conditions .= $this->_db->quoteInto('or Transaction_Status = ?', $searchAll);
			}
			
			if (preg_match($this->_orgDF['regex'],$searchAll))
			{
				$transMonth = date('Y-m-d', strtotime($this->_ehrTables->changeDateformat($searchAll)));
				$conditions .= $this->_db->quoteInto('or Generated_On = ?', $searchAll);
			}
			
			if ($searchAll >= 0 && preg_match('/^[0-9*\.]/', $searchAll))
			{
				$conditions .= $this->_db->quoteInto('or No_Active_Employees = ?', $searchAll);
				$conditions .= $this->_db->quoteInto('or No_Inactive_Employees = ?', $searchAll);
				$conditions .= $this->_db->quoteInto('or Total_Amount = ?', $searchAll);
			}
			
			$qryBilling->where($conditions);
		}
		
        
        if($searchDetails['Billing_Start_Date']!= '' )
        {
            $qryBilling->where($this->_db->quoteInto('Generated_On >= ?', ($searchDetails['Billing_Start_Date'].' 00:00:00')));
        }
        
        if($searchDetails['Billing_End_Date'] != '')
        {
            $qryBilling->where($this->_db->quoteInto('Generated_On <= ?', ($searchDetails['Billing_End_Date'].' 23:59:59')));
        }
        
        if ( $searchDetails['Active_Employee_Amount_Start'] !='' && $searchDetails['Active_Employee_Amount_Start'] >=0 &&
			preg_match('/^[0-9*\.]/', $searchDetails['Active_Employee_Amount_Start']))
		{
			$qryBilling->having($this->_db->quoteInto('Amt_Active_Employees >= ?', $searchDetails['Active_Employee_Amount_Start']));
		}
        
        if ( $searchDetails['Active_Employee_Amount_End'] !='' && $searchDetails['Active_Employee_Amount_End'] >=0 &&
			preg_match('/^[0-9*\.]/', $searchDetails['Active_Employee_Amount_End']))
		{
			$qryBilling->having($this->_db->quoteInto('Amt_Active_Employees <= ?', $searchDetails['Active_Employee_Amount_End']));
		}
        
        if ( $searchDetails['InActive_Employee_Amount_Start'] !='' && $searchDetails['InActive_Employee_Amount_Start'] >=0 &&
			preg_match('/^[0-9*\.]/', $searchDetails['InActive_Employee_Amount_Start']))
		{
			$qryBilling->having($this->_db->quoteInto('Amt_Inactive_Employees >= ?', $searchDetails['InActive_Employee_Amount_Start']));
		}
        
         if ( $searchDetails['InActive_Employee_Amount_End'] !='' && $searchDetails['InActive_Employee_Amount_End'] >=0 &&
			preg_match('/^[0-9*\.]/', $searchDetails['InActive_Employee_Amount_End']))
		{
			$qryBilling->having($this->_db->quoteInto('Amt_Inactive_Employees <= ?', $searchDetails['InActive_Employee_Amount_End']));
		}
        
        
        if ( $searchDetails['Discount_Amount_Start'] !='' && $searchDetails['Discount_Amount_Start'] >=0 &&
			preg_match('/^[0-9*\.]/', $searchDetails['Discount_Amount_Start']))
		{
			$qryBilling->having($this->_db->quoteInto('Discount_Amount >= ?', $searchDetails['Discount_Amount_Start']));
		}
        
        if ( $searchDetails['Discount_Amount_End'] !='' && $searchDetails['Discount_Amount_End'] >=0 &&
			preg_match('/^[0-9*\.]/', $searchDetails['Discount_Amount_End']))
		{
			$qryBilling->having($this->_db->quoteInto('Discount_Amount <= ?', $searchDetails['Discount_Amount_End']));
		}
        
        if ( $searchDetails['Total_Amount_Start'] !='' && $searchDetails['Total_Amount_Start'] >=0 &&
			preg_match('/^[0-9*\.]/', $searchDetails['Total_Amount_Start']))
		{
			$qryBilling->having($this->_db->quoteInto('Total_Amount >= ?', $searchDetails['Total_Amount_Start']));
		}
        
        if ( $searchDetails['Total_Amount_End'] !='' && $searchDetails['Total_Amount_End'] >=0 &&
			preg_match('/^[0-9*\.]/', $searchDetails['Total_Amount_End']))
		{
			$qryBilling->having($this->_db->quoteInto('Total_Amount <= ?', $searchDetails['Total_Amount_End']));
		}
        
        if ( $searchDetails['Billing_Status'] != '')
		{
            if( $searchDetails['Billing_Status'] == "Unpaid")
            {
                $qryBilling->where('Transaction_Status IN (?)', array("Initiated","Unpaid"));
            }
            else
            {
                $qryBilling->where('Transaction_Status = ?', $searchDetails['Billing_Status']);
            }
        }
        
        
		/**
		 * SQL queries
		 * Get data to display
		*/
		$billing = $this->_salesDb->fetchAll($qryBilling);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_salesDb->fetchOne('select FOUND_ROWS()');
		
		/* Total data set length */
		$iTotal = $this->_salesDb->fetchOne($this->_salesDb->select()
											->from($this->_ehrTables->billingTransaction, new Zend_Db_Expr('COUNT(Invoice_No)'))
											->where('Org_Code = ?', $orgCode)
											->where('Transaction_Status IN (?)', $newStatus));
		
		/**
		 * Output array with Json encode
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $billing);
    }
	
	/**
	 *	listPaymentHistory() used to list payment details of invoice number
	*/
	public function listPaymentHistory($invoiceNo, $sortField, $sortOrder, $page, $rows, $searchAll)
    {
		switch ($sortField)
		{
			case 1: $sortField = 'order_id'; break;
			case 2: $sortField = 'tracking_id'; break;
			case 3: $sortField = 'bank_ref_no'; break;
			case 4: $sortField = 'order_status'; break;
			case 5: $sortField = 'failure_message'; break;
			case 6: $sortField = 'payment_mode'; break;
			case 7: $sortField = 'card_name'; break;
			case 8: $sortField = 'amount'; break;
		}
		
    	$qryBilling = $this->_salesDb->select()
										->from($this->_ehrTables->paymentDetails,
											   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS Payment_Id as Count'), 
													 'order_id', 'tracking_id', 'bank_ref_no', 'order_status', 'failure_message', 
													 'payment_mode', 'card_name', 'amount', 
													 'DT_RowClass' => new Zend_Db_Expr('"billingHistory"'),
													 'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', Payment_Id)")))
										
										->where('order_id = ?', $invoiceNo)
										->order("$sortField $sortOrder")
										->limit($rows, $page);
    	
    	/**
		 * SQL queries
		 * Get data to display
		*/
		$billing = $this->_salesDb->fetchAll($qryBilling);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_salesDb->fetchOne('select FOUND_ROWS()');
		
		/* Total data set length */
		$iTotal = $this->_salesDb->fetchOne($this->_salesDb->select()->from($this->_ehrTables->paymentDetails,
																			new Zend_Db_Expr('COUNT(Payment_Id)')));
		
		/**
		 * Output array with Json encode
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $billing);
    }
    
	/**
	 *	billingReportCount() used to get count of the billing transaction details
	*/
	public function billingReportCount($discount, $transMonth, $activeAmt, $inactiveAmt, $totalAmt, $status, $transOpA, $transOpB,
									   $transOpC, $transOpD, $transOpE, $orgCode, $billDate)
    {
    	$qryBilling = $this->_salesDb->select()
									->from($this->_ehrTables->billingTransaction, new Zend_Db_Expr('COUNT(Generated_On)'))
									
									->where('Transaction_Status IN (?)', array('Paid','Initiated','Unpaid'))
									->where('Org_Code = ?', $orgCode);
		
        if (!empty($billDate) && preg_match('/^(\d{4}\-\d{2}\-\d{2})/',$billDate))
        {
            $billDate = date('Y-m-d', strtotime($billDate));
            $qryBilling->where('Generated_On = ?', $billDate);
        }
        if (!empty($transMonth) && !empty($transOpA) && preg_match($this->_orgDF['regex'],$transMonth))
        {
            $transMonth = date('Y-m-d', strtotime($this->_ehrTables->changeDateformat($transMonth)));
            $qryBilling->where('Generated_On '. $transOpA .' ?', $transMonth);
        }
        if (!empty($status))
        {
            $qryBilling->where('Transaction_Status LIKE ?', $status);
        }
        if ( !empty($activeAmt) && preg_match('/^[0-9*\.]/', $activeAmt) && !empty($transOpB)) {
            $qryBilling->where('Amt_Active_Employees' . $transOpB . '?', $activeAmt);
        }
        if ( !empty($inactiveAmt) && preg_match('/^[0-9*\.]/', $inactiveAmt) && !empty($transOpC)) {
            $qryBilling->where('Amt_Inactive_Employees' . $transOpC . '?', $inactiveAmt);
        }
        if ( !empty($totalAmt) && preg_match('/^[0-9*\.]/', $totalAmt) && !empty($transOpD)) {
            $qryBilling->where('Total_Amount' . $transOpD . '?', $totalAmt);
        }
        if ( !empty($discount) && preg_match('/^[0-9*\.]/', $discount) && !empty($transOpE)) {
            $qryBilling->where('Discount_Amount' . $transOpE . '?', $discount);
        }
		
        return $this->_salesDb->fetchOne($qryBilling);
    }
	
	
	
	
	

    public function billingAccess($employeeId, $orgCode)
    {
        $emplole=$this->_accessRights->employeeAccessRights($employeeId,'Roles');
    	if($emplole['Admin']=='admin')
    	{
    		return true;
    	}	
    	else
    	{
    		return false;
    	}	
    	
    }
    public function ratePlan($orgCode)
    {
        $qryRate = $this->_salesDb->select()->from(array('R'=>$this->_ehrTables->orgChoiceRate), array('Discount', 'Payment_Penalty',
    			'Status'=>'Plan_Status'))
        ->joinLeft(array('B'=>$this->_ehrTables->billingRate), 'R.Billing_Id=B.Billing_Id', array( 'Active_Rate'=>'Rate_Active_Employees',
    			'Inactive_Rate'=> 'Rate_Inactive_Employees'))
        ->where('R.Org_Code = ?', $orgCode);
        $getRatePlan = $this->_salesDb->fetchRow($qryRate);
        return $getRatePlan;
    }
    
    
    public function viewTransactionDetails($invoiceNo,$sessionId,$view=null)
    {
    	$getTransaction = $this->_salesDb->select()->from(array('T'=>$this->_ehrTables->billingTransaction), array('Org_Code',
    			'No_Active_Employees', 'No_Inactive_Employees', 'Discount_Amount', 'Generated_By','Outstanding_Balance',
    			'T.Updated_By', 'Updated_On'=>new Zend_Db_Expr("date_format(T.Updated_On,'%d/%m/%Y at %T')"),
    			'Generated_On'=> new Zend_Db_Expr("date_format(Generated_On,'%d/%m/%Y at %T')"),
    			'Amt_Active_Employees',	'Amt_Inactive_Employees', 'Total_Amount', 'Status'=>'Transaction_Status','Tax_Class','Tax_Class_Percentage',
    			'Tax_SubClass1','Tax_SubClass1_Percentage','Tax_SubClass2','Tax_SubClass2_Percentage','Tax_SubClass3','Tax_SubClass3_Percentage',
    			'Tax_SubClass4','Tax_SubClass4_Percentage','Billing_Date'=> new Zend_Db_Expr('Date_format(T.Billing_Date, "%d/%m/%Y")')))
    			 
    			->joinInner(array('P'=>$this->_ehrTables->orgChoiceRate), 'P.Org_Code=T.Org_Code', array('Payment_Penalty',
    					'Due_Date'=>new Zend_Db_Expr("DATE_FORMAT(DATE_ADD(P.Billing_Date, Interval P.Payment_Buffer_days Day), '%d/%m/%Y')")))
    						
    					->joinLeft(array('B'=>$this->_ehrTables->billingRate), 'B.Billing_Id=P.Billing_Id',
    							array('Rate_Active_Employees','Rate_Inactive_Employees','Frequency'))
    								
    							->joinLeft(array('R'=>$this->_ehrTables->registerUser), 'R.Org_Code = T.Org_Code', array('Name'=>new Zend_Db_Expr("Concat(R.First_Name,' ',R.Last_Name)"),
    									'Org_Name', 'Email_Id'))
    									->joinInner(array('A'=>$this->_ehrTables->appLogin), 'T.Generated_By=A.User_Id', array('AddedBy_Name'=>new Zend_Db_Expr("CONCAT(A.First_Name, ' ', A.Last_Name)")))
    									->joinLeft(array('U'=>$this->_ehrTables->appLogin), 'T.Updated_By=U.User_Id', array('UpdatedBy_Name'=>new Zend_Db_Expr("CONCAT(U.First_Name, ' ',U.Last_Name)")))
    
    									->where('T.Invoice_No = ?', $invoiceNo);
    	
    	$transactionDetails = $this->_salesDb->fetchRow($getTransaction);
    	
    	$addressDetails = $this->_db->fetchRow($this->_db->select()->from(array('EJ'=>$this->_ehrTables->empJob), array(''))
    			->joinLeft(array('L'=>$this->_ehrTables->location), 'L.Location_Id=EJ.Location_Id',
    					array('Location_Name','Street1','Street2','Phone','Pincode'))
				//->joinLeft(array('S'=>$this->_ehrTables->state),'S.State_Id=L.State_Id', array('S.State_Name as State'))
				->joinLeft(array('C'=>$this->_ehrTables->city),'C.City_Id=L.City_Id', array('C.City_Name as City'))
				//->joinLeft(array('co'=>$this->_ehrTables->country),'co.Country_Code=L.Country_Code', array('co.Country_Name as Country'))
    			->where('Employee_Id = ?', '1'));
    	 
    	$result = array('Transaction'=>$transactionDetails,'Address'=>$addressDetails,'View'=>$view);
    	return $result;
    	 
    }
    
    public function getOrgName($orgCode)
    {
        $orgName = $this->_salesDb->fetchOne($this->_salesDb->select()->from($this->_ehrTables->companyInfo, 'Org_Name'));
        $regOrgName = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->orgDetails, array('Org_Name', 'Report_LogoPath'))->where('Org_Code = ?', $orgCode));
        return array('RegOrg'=>$regOrgName, 'Org_Name'=>$orgName);

    }
	
	public function billingReportExport($formData, $orgCode)
    {
    	$newStatus = array('Paid','Initiated','Unpaid');
        $qryBilling = $this->_salesDb->select()->from(array('T'=>$this->_ehrTables->billingTransaction), array('Amt_Active_Employees',	'Amt_Inactive_Employees', 'Total_Amount',
			'Status'=>new Zend_Db_Expr('(CASE WHEN `Transaction_Status` = \'Initiated\' THEN \'Unpaid\' ELSE `Transaction_Status` END)'),
				'No_Active_Employees', 'No_Inactive_Employees',	'Outstanding_Balance', 'Discount_Amount',
				'Generated_Date'=> new Zend_Db_Expr('Date_format(Generated_On, "'.$this->_orgDF['sql'].'")')))
				 ->where('T.Transaction_Status IN (?)', $newStatus)
        ->where('T.Org_Code = ?', $orgCode);
		
//		$billStartDate=$formData[0];
//		$billEndDate=$formData[1];
//		$ActiveEmployeeAmountStart=$formData[2];
//		$ActiveEmployeeAmountEnd=$formData[3];
//		$InActiveEmployeeAmountStart=$formData[4];
//		$InActiveEmployeeAmountEnd=$formData[5];
//		$DiscountAmountStart=$formData[6];
//		$DiscountAmountEnd=$formData[7];
//		$InActiveEmployeeAmountEnd=$formData[8];
//		$InActiveEmployeeAmountEnd=$formData[9];
//		$billingStatus=$formData[10];
//		
//		
//		if($billStartDate!= '' )
//        {
//            $qryBilling->where($this->_db->quoteInto('Generated_On >= ?', ($billStartDate.' 00:00:00')));
//        }
//        
//        if($billEndDate != '')
//        {
//            $qryBilling->where($this->_db->quoteInto('Generated_On <= ?', ($billEndDate.' 23:59:59')));
//        }
//        
//        if ( $ActiveEmployeeAmountStart !='' && $ActiveEmployeeAmountStart >=0 &&
//			preg_match('/^[0-9*\.]/', $ActiveEmployeeAmountStart))
//		{
//			$qryBilling->having($this->_db->quoteInto('Amt_Active_Employees >= ?', $ActiveEmployeeAmountStart));
//		}
//        
//        if ( $ActiveEmployeeAmountEnd !='' && $ActiveEmployeeAmountEnd >=0 &&
//			preg_match('/^[0-9*\.]/', $ActiveEmployeeAmountEnd))
//		{
//			$qryBilling->having($this->_db->quoteInto('Amt_Active_Employees <= ?', $ActiveEmployeeAmountEnd));
//		}
//        
//        if ( $InActiveEmployeeAmountStart !='' && $InActiveEmployeeAmountStart >=0 &&
//			preg_match('/^[0-9*\.]/', $InActiveEmployeeAmountStart))
//		{
//			$qryBilling->having($this->_db->quoteInto('Amt_Inactive_Employees >= ?', $InActiveEmployeeAmountStart));
//		}
//        
//         if ( $InActiveEmployeeAmountEnd !='' && $InActiveEmployeeAmountEnd >=0 &&
//			preg_match('/^[0-9*\.]/', $InActiveEmployeeAmountEnd))
//		{
//			$qryBilling->having($this->_db->quoteInto('Amt_Inactive_Employees <= ?', $InActiveEmployeeAmountEnd));
//		}
//        
//        
//        if ( $DiscountAmountStart !='' && $DiscountAmountStart >=0 &&
//			preg_match('/^[0-9*\.]/', $DiscountAmountStart))
//		{
//			$qryBilling->having($this->_db->quoteInto('Discount_Amount >= ?', $DiscountAmountStart));
//		}
//        
//        if ( $DiscountAmountEnd !='' && $DiscountAmountEnd >=0 &&
//			preg_match('/^[0-9*\.]/', $DiscountAmountEnd))
//		{
//			$qryBilling->having($this->_db->quoteInto('Discount_Amount <= ?', $DiscountAmountEnd));
//		}
//        
//        if ( $InActiveEmployeeAmountStart !='' && $InActiveEmployeeAmountStart >=0 &&
//			preg_match('/^[0-9*\.]/', $InActiveEmployeeAmountStart))
//		{
//			$qryBilling->having($this->_db->quoteInto('Total_Amount >= ?', $InActiveEmployeeAmountStart));
//		}
//        
//		
//        if ( $InActiveEmployeeAmountEnd !='' && $InActiveEmployeeAmountEnd >=0 &&
//			preg_match('/^[0-9*\.]/', $InActiveEmployeeAmountEnd))
//		{
//			$qryBilling->having($this->_db->quoteInto('Total_Amount <= ?', $InActiveEmployeeAmountEnd));
//		}
//        
//        if ( $billingStatus != '')
//		{
//            if( $billingStatus == "Unpaid")
//            {
//                $qryBilling->where('Transaction_Status IN (?)', array("Initiated","Unpaid"));
//            }
//            else
//            {
//                $qryBilling->where('Transaction_Status = ?', $billingStatus);
//            }
//        }

        //if(!empty($billDate) && preg_match('/^(\d{4}\-\d{2}\-\d{2})/',$billDate))
        //{
        //    $billDate = date('Y-m-d', strtotime($billDate));
        //    $qryBilling->where('Generated_On = ?', $billDate);
        //}
        //if(!empty($transMonth) && !empty($transOpA) && preg_match($this->_orgDF['regex'],$transMonth))
        //{
        //    $transMonth = date('Y-m-d', strtotime($this->_ehrTables->changeDateformat($transMonth)));
        //    $qryBilling->where('Generated_On '. $transOpA .' ?', $transMonth);
        //}
        //if(!empty($status))
        //{
        //    $qryBilling->where('Transaction_Status LIKE ?', $status);
        //}
        //if ( !empty($activeAmt) && preg_match('/^[0-9*\.]/', $activeAmt) && !empty($transOpB)) {
        //    $qryBilling->where('Amt_Active_Employees' . $transOpB . '?', $activeAmt);
        //}
        //if ( !empty($inactiveAmt) && preg_match('/^[0-9*\.]/', $inactiveAmt) && !empty($transOpC)) {
        //    $qryBilling->where('Amt_Inactive_Employees' . $transOpC . '?', $inactiveAmt);
        //}
        //if ( !empty($totalAmt) && preg_match('/^[0-9*\.]/', $totalAmt) && !empty($transOpD)) {
        //    $qryBilling->where('Total_Amount' . $transOpD . '?', $totalAmt);
        //}
        //if ( !empty($discount) && preg_match('/^[0-9*\.]/', $discount) && !empty($transOpE)) {
        //    $qryBilling->where('Discount_Amount' . $transOpE . '?', $discount);
        //}
        $rowBilling = $this->_salesDb->fetchAll($qryBilling);
        return $rowBilling;
    }
	
    public function billingReportExportcrt($discount, $transMonth, $activeAmt, $inactiveAmt, $totalAmt, $status,
    $transOpA, $transOpB, $transOpC, $transOpD, $transOpE, $orgCode, $billDate)
    {

    	$newStatus = array('Paid','Initiated','Unpaid');
        $qryBilling = $this->_salesDb->select()->from(array('T'=>$this->_ehrTables->billingTransaction), array('Amt_Active_Employees',	'Amt_Inactive_Employees', 'Total_Amount',
			'Status'=>new Zend_Db_Expr('(CASE WHEN `Transaction_Status` = \'Initiated\' THEN \'Unpaid\' ELSE `Transaction_Status` END)'),
				'No_Active_Employees', 'No_Inactive_Employees',	'Outstanding_Balance', 'Discount_Amount',
				'Generated_Date'=> new Zend_Db_Expr('Date_format(Generated_On, "'.$this->_orgDF['sql'].'")')))
				 ->where('T.Transaction_Status IN (?)', $newStatus)
        ->where('T.Org_Code = ?', $orgCode);

        if(!empty($billDate) && preg_match('/^(\d{4}\-\d{2}\-\d{2})/',$billDate))
        {
            $billDate = date('Y-m-d', strtotime($billDate));
            $qryBilling->where('Generated_On = ?', $billDate);
        }
        if(!empty($transMonth) && !empty($transOpA) && preg_match($this->_orgDF['regex'],$transMonth))
        {
            $transMonth = date('Y-m-d', strtotime($this->_ehrTables->changeDateformat($transMonth)));
            $qryBilling->where('Generated_On '. $transOpA .' ?', $transMonth);
        }
        if(!empty($status))
        {
            $qryBilling->where('Transaction_Status LIKE ?', $status);
        }
        if ( !empty($activeAmt) && preg_match('/^[0-9*\.]/', $activeAmt) && !empty($transOpB)) {
            $qryBilling->where('Amt_Active_Employees' . $transOpB . '?', $activeAmt);
        }
        if ( !empty($inactiveAmt) && preg_match('/^[0-9*\.]/', $inactiveAmt) && !empty($transOpC)) {
            $qryBilling->where('Amt_Inactive_Employees' . $transOpC . '?', $inactiveAmt);
        }
        if ( !empty($totalAmt) && preg_match('/^[0-9*\.]/', $totalAmt) && !empty($transOpD)) {
            $qryBilling->where('Total_Amount' . $transOpD . '?', $totalAmt);
        }
        if ( !empty($discount) && preg_match('/^[0-9*\.]/', $discount) && !empty($transOpE)) {
            $qryBilling->where('Discount_Amount' . $transOpE . '?', $discount);
        }
        $rowBilling = $this->_salesDb->fetchAll($qryBilling);
        return $rowBilling;
    }
	
	public function mailBillingExport($invoiceNo){
		$newStatus = array('Paid','Initiated','Unpaid');
        $qryBilling = $this->_salesDb->select()->from(array('T'=>$this->_ehrTables->billingTransaction), array('Amt_Active_Employees',	'Amt_Inactive_Employees', 'Total_Amount',
			'Status'=>new Zend_Db_Expr('(CASE WHEN `Transaction_Status` = \'Initiated\' THEN \'Unpaid\' ELSE `Transaction_Status` END)'),
				'No_Active_Employees', 'No_Inactive_Employees',	'Outstanding_Balance', 'Discount_Amount',
				'Generated_Date'=> new Zend_Db_Expr('Date_format(Generated_On, "'.$this->_orgDF['sql'].'")')))
				 ->where('T.Transaction_Status IN (?)', $newStatus)
        ->where('T.Invoice_No = ?', $invoiceNo);
		
		return $this->_salesDb->fetchAll($qryBilling);
	}

	public function __destruct()
    {
        
    }
    
}