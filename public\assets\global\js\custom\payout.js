$(function () {
    $.fn.dataTable.ext.errMode = 'none';

    resetFormValues();

    //Initialize variables
    var setOtpTimer,paySourceType, sPayoutSalaryMonth,accountBalanceCurrency,payslipIds,accountNumber;
    var payslipData = ignoredPayslipData = payslipIds = [];
    var monthlyPayslipCount = hourlyPayslipCount = transactionGroupId = accountBalance = totalSalary = isUserInitiated = 0;
    var isPayoutWizardFirstPage = 0;
    var fieldValidation = [null, undefined, ''];
    var isKeyPressEvent = false;
    var previousSearchValue = '';
    var isChangePreferenceList1 = isChangePreferenceList2 = false;
    var changePreferenceTransactionType,preferenceMatchedCategory,transactionCategory,minSalaryPayslip,maxSalaryPayslip,initialPreferences;
    $("#oneCategoryMatchedModal,#allCategoryMatchedModal,#fewCategoryMatchedModal,#bothCategoryNotMatched,#applyFilter,#confirmTransaction,div#transactionNotes").hide(); 
    
    /* We need to show the currency symbol as INR for now. In future, we will provide a option to user
    to choose currency during connnecting banking registration. Then we will show the chosen currency
    symbol in the payout and payout history */
    var totalSalaryCurrency = 'INR';
    // get domainName and orgCode
    var domainName = fnIsDomain(),
        orgCode = fngetOrgCode(),
        domainName = domainName.split('.')[0];

    //hide the payslip monthly and hourly card initially
    $('.payslip-card-hidden').hide();

    /* Payout employees list - hide all the chip close for the filter fields */
    $('#payoutLocationFilterChipClose').hide();
    $('#payoutDepartmentFilterChipClose').hide();
    $('#payoutTypeFilterChipClose').hide();

    // Payout wizard
    var modalpayoutformWizard = $("#payoutform").stepFormWizard({
        height: 'auto',
        theme: "simple", // type of wizard
        startStep : 0,
        onSlideChanged: function(from,to) {
            $('#payoutform .sf-btn-next').removeClass('nextHidden');
            $('#payoutform .sf-btn-prev').addClass('prevHidden');

            resetPayoutTabFieldsInSlideChange();

            switch(to)
            {
                case 0:
                    if (!($('#monthPickerPage').hasClass('alreadyloaded')))
                    {
                        $('#monthPickerPage').addClass('alreadyloaded');                        
                    }
                    break;
                case 1:
                    $('#payoutform .sf-btn-prev').removeClass('prevHidden');
                    $('#payoutform .sf-btn-next').addClass('nextHidden');
                    resetPayoutFormButtons();

                    if (!($('#payoutPaymentModePage').hasClass('alreadyloaded')))
                    {
                        $('#payoutPaymentModePage').addClass('alreadyloaded');
                    }
                    break;
                case 2:
                    $('#payoutform .sf-btn-prev').removeClass('prevHidden');
                    resetPayoutFormButtons();

                    /* Update the account balance */
                    $('#payoutform .sf-controls').prop('style','text-align:center;');                    
                    $('#payoutform .sf-controls .availableBalanceText').html("");
                    $('#payoutform .sf-controls').append('<span class="availableBalanceText lgPayoutAvailableBalance">Available Balance: <span class="accountBalance payout-history-view-amount lgPayoutAvailableBalance">'+accountBalanceCurrency+accountBalance+'</span></span>');

                    /* Available balance in medium screen */
                    $('#payoutAvailableBalance').append('<span class="availableBalanceText zone-float">Available Balance: <span class="accountBalance payout-history-view-amount">'+accountBalanceCurrency+accountBalance+'</span></span>');
                    
                    resetPayoutFormAvailBal();

                    // Change the next button text to Proceed and append total salary and currency symbol
                    setTotalSalaryInWizardButton(totalSalaryCurrency,totalSalary);

                    if (!($('#payoutEmployeesPage').hasClass('alreadyloaded')))
                    {
                        $('#payoutEmployeesPage').addClass('alreadyloaded');
                    }
                    break;                        
                case 3:
                    $('#payoutform .sf-btn-next').addClass('nextHidden');
                    $('#payoutform .sf-btn-prev').addClass('prevHidden');
                    resetPayoutFormButtons();

                    if (!($('#checkStatusPage').hasClass('alreadyloaded')))
                    {
                        $('#checkStatusPage').addClass('alreadyloaded');
                    }

                    break;    
            }
        },
        onNext: function(i) {
            switch (i)
            {
                case 0:
                    /* Month picker page */

                    /* validate whether all the fields are given or not in first tab */
                    if(isUserInitiated == 0){
                        if(validatePayoutMonthPickerTab() == 0){
                            return false;
                        }
                    }
                    break;
                case 1:
                    /* Debit account number page */
                    if(isUserInitiated == 0){
                        if(validatePayoutMonthPickerTab() == 0){
                            return false;
                        }else{
                            /* validate whether the second tab is valid or not*/
                            if(validatePayoutPaymentModeTab() == 0){
                            return false;
                            }
                        }
                    }
                    break;
                case 2:
                    /* Payout List Page */
                    setTimeout(function() {
                        /* Need to restrict the key press event in the list payout page as
                        proceed button is triggered when the enter button is pressed in the search input */
                        if(!isKeyPressEvent){
                            /* validate whether the first tab is valid or not */
                            if(isUserInitiated == 0){
                                if(validatePayoutMonthPickerTab() == 0){
                                    return false;
                                }else{
                                    /* validate whether the second tab is valid or not*/
                                    if(validatePayoutPaymentModeTab() == 0){
                                    return false;
                                    }else{
                                        /* validate whether the second tab is valid or not*/
                                        validatePayoutListTab();
                                        return false;
                                    }
                                }
                            }
                        }else{
                            isKeyPressEvent = false;
                            return false;
                        }
                        
                    },1500);

                    /*  If the first transaction is initiated, we should not retrun false */
                    if(isUserInitiated !== 1){
                        return false;
                    }
                    break;
                case 3:                    
                    break;

            }
        },
        onPrev: function(i) {
            switch(i)
            {
                /* There is no back option in the month picker and the check status page. So need to reset the buttons for
                the case 0 and 3 */
                case 1:
                    /* While moving to Month picker page from debit account number selection page,
                    reset the next and prev buttons */
                    $('#payoutform .sf-btn-prev').addClass('prevHidden');
                    $('#payoutform .sf-btn-next').removeClass('nextHidden');
                    resetPayoutFormButtons();
                    break;
                case 2:
                /* When clicking back button from screen 3 to screen 2*/
                    resetFilterFields();
                break;
            }
        },
        onFinish: function() {
            /* redirect to view payout history page to check the tx status */
            
            $("body").append("<form class='viewPayoutHistory' style='display:none;'></form>");
            $('.viewPayoutHistory').prop('method','POST');
            $('.viewPayoutHistory').prop('action',pageUrl() + 'payroll/payout-history/view-payout-history');
            
            $('.viewPayoutHistory').append('<input type="hidden" name = "Transaction_Group_Id" value= "'+transactionGroupId+'" />');
            
            $('.viewPayoutHistory').submit();
            $('.viewPayoutHistory').remove(); 
            return false;
        }
    });

    /* Refresh the wizard and set the next, prev, finish button css */
    setTimeout(function() {
        $(window).trigger('resize');

        /* refresh the wizard */
        modalpayoutformWizard.refresh();

        /* update the css for buttons */
        $('#payoutform .sf-btn-next').addClass('payout-visible-button');
        $('#payoutform .sf-btn-prev').addClass('payout-hidden-button');
        $('#payoutform .sf-btn-finish').addClass('payout-checkstatus-button')
                                       .addClass('payout-visible-button')
                                       .addClass('bg-primary');
        
        /* Hide the prev button and show the next button in the first page */
        $('#payoutform .sf-btn-next').removeClass('nextHidden');
        $('#payoutform .sf-btn-prev').addClass('prevHidden');
    
        $('#payoutform .sf-btn-prev').text('Back');
        $('#payoutform .sf-btn-next').text('Next');
        $('#payoutform .sf-btn-finish').val('Check Status');
        
        /* hide the wizard title(tab) here */
        $('div .sf-nav-wrap').addClass('hidden');

        /* Reset the tab values */
        resetPayoutTabFieldsInSlideChange();

        /* Reset the button size based on the screen size*/
        resetPayoutFormButtons();
    }, 500);

    /* trigger the wizard */
    $('#modalpayoutform-box .sf-nav-step-0').trigger('click');

    /* reset the fields based on the screen size */
    $(document).ready(function() {
        resetPayoutFormFields();       
        resetPayslipView();

        /* If it is on key press event, return true. It will be checked in list payout page
        and proceed button will not be triggered for the key press event. */
        isKeyPressEvent = false;
        $(window).on('keydown', function(event) {
            if(event.keyCode == 13) {
                isKeyPressEvent = true;
            }
        });
    });

    /* Sidebar toggle event */
    $('[data-toggle]').on('click', function(event) {
        /* Trigger the window resize function to update the width of the payout page.
        Fix for the payout wizard width while minimizing the side bar.  */
        $(window).trigger('resize');
    });

    /* reset the fields based on the screen size */
    $(window).on('resize', function() {
        resetPayoutFormFields();
        resetPayslipView();

        /* Show the total salary based on the screen size */
        setTotalSalaryBasedOnScreen(accountBalanceCurrency,totalSalary);
    });

    /* reset the fields based on the screen size */
    function resetPayoutFormFields(){
        $('.sm-payoutBankName,.sm-orLabel').addClass('hidden');
        $('.lg-payoutBankName,.lg-orLabel').removeClass('hidden');
        if($(window).width() < 961 ){
            $('.sm-payoutBankName,.sm-orLabel').removeClass('hidden');
            $('.lg-payoutBankName,.lg-orLabel').addClass('hidden');
        }

        resetPayoutFormAvailBal();
        resetPayoutFormButtons();
    }

    /* Function to add and remove class required for the next and back button while redirecting to the previous
    and the next page in wizard */
    function resetPayoutFormButtons(){
        $('#payoutform .sf-btn-next').addClass('sf-right'); //add the float css property to the next button
        $('#payoutform .sf-btn-prev').addClass('sf-left'); //add the float css property to the prev button

        /* If next button has to be hidden */
        if($('#payoutform .sf-btn-next').hasClass('nextHidden')){
            $('#payoutform .sf-btn-next').removeClass('payout-visible-button'); //remove the button visibility css            
            $('#payoutform .sf-btn-next').addClass('payout-hidden-button'); //add the button hidden css
        }else{
            $('#payoutform .sf-btn-next').removeClass('payout-hidden-button'); //remove the button hidden css
            $('#payoutform .sf-btn-next').addClass('payout-visible-button');//add the button visiblity css
        }

        /* If back button has to be hidden */
        if($('#payoutform .sf-btn-prev').hasClass('prevHidden')){
            $('#payoutform .sf-btn-prev').removeClass('payout-visible-button'); //remove the button visibility css
            $('#payoutform .sf-btn-prev').addClass('payout-hidden-button'); //add the button hidden css
        }else{
            $('#payoutform .sf-btn-prev').removeClass('payout-hidden-button'); //remove the button hidden css
            $('#payoutform .sf-btn-prev').addClass('payout-visible-button'); //add the button visiblity css
        }
    }

    /* Hide and show the available balance medium screen text based on the active screen */
    function resetPayoutFormAvailBal(){
        $('#payoutform .sf-controls #payoutAvailableBalance').hide();
        $('#payoutform .sf-controls .lgPayoutAvailableBalance').hide();
        if($(window).width() > 1189 ){
            if($('#payoutform .sf-viewport .sf-step-2').hasClass('sf-step-active')){
                $('#payoutform .sf-controls .lgPayoutAvailableBalance').show();
            }
        }else{
            if($('#payoutform .sf-viewport .sf-step-2').hasClass('sf-step-active')){
                $('#payoutform .sf-controls #payoutAvailableBalance').show();
            }
        }
    }

    /* Click event for month picker */
    $('#payoutSalaryMonth').on('click', function () {
        let dateString = $(".datepicker-switch").text();
        // Regular expression to match the month and year
        var regex = /([a-zA-Z]+) (\d{4})/;

        // Use the exec method to extract matches
        var matches = regex.exec(dateString);

        // Check if there are matches
        if (matches && matches.length >= 3) {
            // Extracted month and year
            var month = matches[1];
            var year = matches[2];
            /* convert the payout month based on which server side accepts */
            var getYear = parseInt(year);
            var getMonth = moment(month, 'MMMM').format('M');
            sPayoutSalaryMonth = getMonth+','+getYear;
        }
    });

    // Redirect to salary payslip if they click generate button in the month picker page popup
    $('#redirectToPayslip').on('click', function () {
        window.location.href=pageUrl () +'payroll/salary-payslip';        
    });

    //Change event of the direct debit
    $('#payoutAccountId').on('change', function () {
        accountNumber = '';

        //If add your bank is choosen, redirect to the eft configuration page to add account details
        if($(this).select2('val') != ''){
            if($(this).select2('val') == 'Add your bank'){
                //Reset the value in drop down
                $(this).select2('val','');

                //hide the payslip card
                $('.payslip-card-hidden').hide();

                window.location.href=pageUrl () +'organization/eft-configuration';
            }else{
                /* As there is a chance to deregister and delete, check whether the 
                account number is locked or not. Will be implement in future*/
                var payoutAccountLockStatus = lockPayoutAccountNumber();
                if(payoutAccountLockStatus == 0){
                    accountNumber = $(this).find('option:selected').text();

                    /* show the payslip card */
                    $('.payslip-card-hidden').show();
                }
            }
        }else{
            //hide the payslip card
            $('.payslip-card-hidden').hide();
        }
    });

    //change event for paysource if monthly or hourly card is choosen
    $('.paySourceCard').on('click', function() {
        // Uncheck the select all checkbox
        $(".selectPayoutCheckbox").prop('checked', false);

        paySourceType = $(this).attr('paySourceType');
        if($.inArray(paySourceType,fieldValidation) == -1){
            /* validate whether the first and second tab is valid or not*/
            if(validatePayoutMonthPickerTab() !== 0 && validatePayoutPaymentModeTab() !== 0){
                retrieveBankAccountBalance();
            }
        }else{
            // show the error popup
            $('#payoutErrorContent').text('Something went wrong. Please contact system admin');
            $('#modalPayoutError').modal('toggle');
        }
    });

    /* When checkbox is checked or unchecked */
    $(document).on('click','.payoutCheckbox',function(){
        var checkBoxId = $(this).prop('id');
        var splitCheckBox = checkBoxId.split('-');
        // Uncheck the deselect all
        $(".deselectPayoutCheckbox").prop('checked', false);
        if($(this).is(':checked'))
        {   
            $("#"+checkBoxId).prop('checked', 'checked');
            $.each(ignoredPayslipData, function(key, row){
                /* If payslip id is checked, remove it in this array */
                if(row == splitCheckBox[1]){
                    ignoredPayslipData.splice(key,1);
                    return;
                }
            });

            checkUncheckSelectAll();
            sumTotalSalary();
        }
        else
        {
            $("#"+checkBoxId).prop('checked', false);

            /* If payslip id is unchecked, push it in this array */
            if($.inArray(splitCheckBox[1],ignoredPayslipData) == -1){
                ignoredPayslipData.push(splitCheckBox[1]);
            }
            checkUncheckSelectAll();
            sumTotalSalary();
        }
    });
    
    /* When 'select all' checkbox is checked or unchecked */
    $(document).on('click','.selectPayoutCheckbox',function(){
        if($(this).is(':checked'))
        {
            // Uncheck the deselect all
            $(".deselectPayoutCheckbox").prop('checked', false);

            updateIgnorePayslipId(0);
        }
        else
        {
            //check the deselect all
            $(".deselectPayoutCheckbox").prop('checked', 'checked');

            updateIgnorePayslipId(1);
        }
    });

    /* When 'Deselect all' checkbox is checked or unchecked */
    $(document).on('click','.deselectPayoutCheckbox',function(){
        if($(this).is(':checked'))
        {
            //Uncheck the select all
            $(".selectPayoutCheckbox").prop('checked', false);

            /* Remove all the index and add */
            ignoredPayslipData = [];

            updateIgnorePayslipId(1);
        }
        else
        {
            //check the select checkbox
            $(".selectPayoutCheckbox").prop('checked', 'checked');

            updateIgnorePayslipId(0);
        }
    });


    /* Apply filter in the payout employees list page */
    $('#filterPayoutLocation,#filterPayoutDepartment,#filterPayoutEmployeeType,#filterPayoutLocationXS,#filterPayoutDepartmentXS,#filterPayoutEmployeeTypeXS').on('change', function() {
        var filterId = $(this).prop('id');
        if(filterId == "filterPayoutLocation" || filterId == "filterPayoutLocationXS"){
            $('#s2id_filterPayoutLocation, #s2id_filterPayoutLocationXS').select2('val', $(this).val())
        } else if(filterId == "filterPayoutDepartment" || filterId == "filterPayoutDepartmentXS"){
            $('#s2id_filterPayoutDepartment, #s2id_filterPayoutDepartmentXS').select2('val', $(this).val())
        }else{
            $('#s2id_filterPayoutEmployeeType, #s2id_filterPayoutEmployeeTypeXS').select2('val', $(this).val());
        }

        if($("#s2id_filterPayoutLocation").select2('val') !== ""){
            $('#payoutLocationFilterChipName').html($("#s2id_filterPayoutLocation").text());
            $('#payoutLocationFilterChipClose').show();
        } else{
            $('#payoutLocationFilterChipName').html("All");
            $('#payoutLocationFilterChipClose').hide();
        }

        if($("#s2id_filterPayoutDepartment").select2('val') !== ""){
            $('#payoutDepartmentFilterChipName').html($("#s2id_filterPayoutDepartment").text());
            $('#payoutDepartmentFilterChipClose').show();
        } else{
            $('#payoutDepartmentFilterChipName').html("All");
            $('#payoutDepartmentFilterChipClose').hide();
        }

        if($("#s2id_filterPayoutEmployeeType").select2('val') !== ""){
            $('#payoutEmployeeTypeFilterChipName').html($("#s2id_filterPayoutEmployeeType").text());
            $('#payoutTypeFilterChipClose').show();
        } else{
            $('#payoutEmployeeTypeFilterChipName').html("All");
            $('#typeFilterChipClose').hide();
        }
        listPayoutEmployees();
    });

    /* Reset the filter */
    $('.resetPayoutFilter').on('click', function(){
        resetFilterFields();       
        listPayoutEmployees();
    });

    /* Change preference for this transaction */
    $('#changePreference').on('click', function(){
        $("#oneCategoryMatchedModal,#allCategoryMatchedModal,#fewCategoryMatchedModal,#bothCategoryNotMatched,#applyFilter,#confirmTransaction,div#transactionNotes").hide(); 
        $('#modalLowerAccountBalancePopup').modal('hide');
        isChangePreferenceList1 = isChangePreferenceList2 = false;
        if($(window).width()<=1024){
            $('#payoutErrorContent').text('Changing the preference is not supported in your current device, please launch the application in a larger screen to perform this action');
            $('#modalPayoutError').modal('toggle');
        }
        else{
        staticPreference("default",0);
        }
    });
    
    /* Close change preference for this transaction */
    $('#closeChangePreference').on('click', function(){
        isChangePreferenceList1 = isChangePreferenceList2 = false;
        $("#oneCategoryMatchedModal,#allCategoryMatchedModal,#fewCategoryMatchedModal,#bothCategoryNotMatched").hide(); 
        $('#modalChangePreference').modal('hide');
    });

    /** Reset the location fileter chip when close button is clicked */
    $('#payoutLocationFilterChipClose').on('click', function() {
        $('#payoutLocationFilterChipName').html("All");
        $('#payoutLocationFilterChipClose').hide();
        $('#s2id_filterPayoutLocation, #s2id_filterPayoutLocationXS').select2('val','');
        listPayoutEmployees();
    });
    
    /** Reset the department fileter chip when close button is clicked */
    $('#payoutDepartmentFilterChipClose').on('click', function() {
        $('#payoutDepartmentFilterChipName').html("All");
        $('#payoutDepartmentFilterChipClose').hide();
        $('#s2id_filterPayoutDepartment, #s2id_filterPayoutDepartmentXS').select2('val','');
        listPayoutEmployees();
    });
    
    /** Reset the Employee Type fileter chip when close button is clicked */
    $('#payoutTypeFilterChipClose').on('click', function() {
        $('#payoutEmployeeTypeFilterChipName').html("All");
        $('#payoutTypeFilterChipClose').hide();
        $('#s2id_filterPayoutEmployeeType, #s2id_filterPayoutEmployeeTypeXS').select2('val','');
        listPayoutEmployees();
    });

    /** When user select Proceed to payment */
    $('#confirmTransaction').on('click', function() {
        $('#showPreferencePopup').modal('hide');
        $("#oneCategoryMatchedModal,#allCategoryMatchedModal,#fewCategoryMatchedModal,#bothCategoryNotMatched").hide(); 
        requestOtp(2);
    });

    
    var timer = null;
    /** Get transaction details based on the value entered in the serach input. */
    /** the browser only recognises a change when the field blurs, so we are using the input event of the search field */
    $('#searchPayoutTransactionDetails,#searchPayoutTransaction').on('input', function () {
        $('#searchPayoutTransactionDetails,#searchPayoutTransaction').val($(this).val());
        if (timer) {
            clearTimeout(timer);
        }
        /**The search filter will be applied only after the user stops entering the value in the search field */
        timer = setTimeout(function() {
            listPayoutEmployees(0,1);
        }, 1000);
    });

    function clearTimeout(){
        timer = null;
    }

    /* When the continue button is clicked in the lower account balance popup */
    $('#confirmSendOtp').on('click', function(){
        isUserInitiated = 0;

        /* Popup is not closed as 'payout-tx-popup-opacity' is set in modal. So remove the class while click cancel */
        if($(this).parent().parent().parent().parent() .hasClass('payout-tx-popup-opacity')){
            $(this).parent().parent().parent().parent() .removeClass('payout-tx-popup-opacity')
        }
        $('#modalLowerAccountBalancePopup').modal('hide');

        validateEmployeeBankAccount(null);
    });
    
     /* When user selects to make payment of one category type*/
    $('#applyFilter').on('click', function(){
        //If the matched category is upto 2 lakh set the max as 2 lakh
        if(preferenceMatchedCategory=='upto 2 lakh'){
            $('#maxSalary').val(200000);
            $('#payslipSalaryRangeSliderStep').data("ionRangeSlider").update({
                to: 200000,
                });

            listPayoutEmployees();
        }
        //If the matched category is greater than 2 lakh set the min as 2 lakhs
        else{
           $('#minSalary').val(200000);
           $('#payslipSalaryRangeSliderStep').data("ionRangeSlider").update({
                from: 200000,
            });
            listPayoutEmployees();
        }
        $('#showPreferencePopup').modal('hide');
        $("#fewCategoryMatchedModal").hide();
    });

    /* When the resend button is clicked in the otp screen, send the otp */
    $('#otpResendTimer').on('click', function(){
        isUserInitiated = 0;
        
        if($('#otpResendTimer').text() == "Resend"){
            $('#payoutOtp').val('');
            clearInterval(setOtpTimer);
            requestOtp(1);
        }
    });

    /* Close the lower account balance popup if user click cancel */
    $('#cancelTransactionConfirmation').on('click', function() {
        isUserInitiated = 0;

        /* Popup is not closed as 'payout-tx-popup-opacity' is set in modal. So remove the class while click cancel */
        if($(this).parent().parent().parent().parent() .hasClass('payout-tx-popup-opacity')){
            $(this).parent().parent().parent().parent() .removeClass('payout-tx-popup-opacity')
        }
        $('#modalLowerAccountBalancePopup').modal('hide');
    });

    /* When the cancel button is clicked in the otp screen, clear the timer berore hide the screen */
    $('#cancelOtpScreen').on('click', function(){
        isUserInitiated = 0;

        resetPayoutPayslipData();

        hideOtpModal();
    });

    /* When continue button is clicked in the otp screend*/
    $('#confirmInitiateTx').on('click', function(){
        if($('#formModalOtpPopup').valid()){
            /* reset the button text */
            $('#otpResendTimer').text('Resend');

            clearInterval(setOtpTimer);
            initiateFirstPayoutTx();
        }
    });

    /* View monthly and hourly payslip */
    $(document).on('click','.viewPayslipFromPayout .spViewPayslipFromPayout',function(){
        if(paySourceType == "Monthly Payslip"){
            //Remove the earnings and deduction tab content
            $("#earningsTab").find("tr:gt(0)").remove();
            $("#deductTab").find("tr:gt(0)").remove();
            // Also clear the combined table if it exists
            $("#earningsDeductionsTab").empty();
            view_pdf_payslip_template($(this).attr('vpPayslipId'), $(this).attr('vpEmployeeName'), $(this).attr('vpTemplateId'), 'view',null,'payout','Monthly',domainName, orgCode)
        }else{
            //Remove the earnings and deduction tab content
            $("#earningsHourlyTab").find("tr:gt(0)").remove();
            $("#deductHourlyTab").find("tr:gt(0)").remove();
            if (!$('#payslipTemplateHourlyCount').val()) {
                viewHourlyPayslip($(this).attr('vpPayslipId'),$(this).attr('vpEmployeeName'),'payout');
            }
            else {
                view_pdf_payslip_template($(this).attr('vpPayslipId'), $(this).attr('vpEmployeeName'), $(this).attr('vpTemplateId'), 'view',null,'payout','Hourly',domainName, orgCode)
            }
        }
    });

    /* Function used to check and uncheck the payslip employee card and the deselectall checkbox */
    function updateIgnorePayslipId(isInsert){
        if(isInsert == 1){
            //Uncheck the all the payslip ids
            $(".payoutCheckbox").prop('checked', false);

            /* If deselect all is checked or select all is unchecked, push all the payslip id in this array */
            if(payslipData != ""){ 
                // If payslip data exists
                if(payslipData.length > 0){
                    for (var payslipIndex=0; payslipIndex<payslipData.length; payslipIndex++){
                        ignoredPayslipData.push(payslipData[payslipIndex]['Payslip_Id']);
                    }

                    sumTotalSalary();
                }
            }
        }else{
            //Uncheck the all the payslip ids
            $(".payoutCheckbox").prop('checked', 'checked');            

            /* Empty the ignored payslip id array */
            ignoredPayslipData = [];

            sumTotalSalary();
        }
    }


    /* Function to check monthly and hourly payslip exists for the salary month.
    And to get the payslip count for the payout month */
    function checkPayslipExistsForPayoutMonth(sPayoutSalaryMonth,isPayoutWizardFirstPage){
        // setMask('#wholepage');
        var isPayslipValid;
        $.ajax ({
            type     : 'POST',
            dataType : 'json',
            async    : false,
            url      : pageUrl () +'payroll/payout/check-monthly-hourly-payslip-exists/payoutSalaryMonth/'+sPayoutSalaryMonth,
            success  : function (result)
            {
                if(result !== '' && result !== null){
                    // If payout salary month is valid
                    if(result['success']){
                        monthlyPayslipCount = result['monthlyPayslipCount'];
                        hourlyPayslipCount = result['hourlyPayslipCount'];

                        var monthlyPayslipText = result['monthlyPayslipCount'] <= 1 ? result['monthlyPayslipCount']+' Employee' : result['monthlyPayslipCount']+' Employees';
                        var hourlyPayslipText = result['hourlyPayslipCount'] <= 1 ? result['hourlyPayslipCount']+' Employee' : result['hourlyPayslipCount']+' Employees';
                        
                        $('#monthlyPayslipCount').text(monthlyPayslipText);
                        $('#hourlyPayslipCount').text(hourlyPayslipText);

                        if(isPayoutWizardFirstPage == 1 && result['aggregatorDetailsExist'] == 0){
                            isPayslipValid = -6;
                        }else{
                            // If monthly and hourly payslip not generated for the payout salary month
                            if((isPayoutWizardFirstPage == 1 && result['monthlyPayslipExists'] == 0 && 
                            result['isHourlyPayslipExists'] == 0) || (isPayoutWizardFirstPage !== 1 &&
                            ((paySourceType == 'Monthly Payslip' && result['monthlyPayslipExists'] == 0) ||
                            (paySourceType == 'Hourly Payslip' && result['isHourlyPayslipExists'] == 0)))){
                                isPayslipValid = 0;
                            }
                            /* If monthly and hourly payslip exists or any one of them exists, but all the payslip for the employees are settled or if the payment is initiated */
                            else if((isPayoutWizardFirstPage == 1 && ((result['monthlyPayslipExists'] > 0 &&
                            result['isAllMonthlyPayslipPaid'] == result['monthlyPayslipExists']) ||
                            result['monthlyPayslipExists'] == 0) && (result['isHourlyPayslipExists'] == 0 ||
                            (result['isHourlyPayslipExists'] > 0 &&
                            result['isAllHourlyPayslipPaid'] == result['isHourlyPayslipExists']))) ||
                            (isPayoutWizardFirstPage !== 1 && (paySourceType == 'Monthly Payslip' &&
                            result['monthlyPayslipExists'] > 0 && result['isAllMonthlyPayslipPaid'] == result['monthlyPayslipExists']
                            ) || (paySourceType == 'Hourly Payslip' && result['isHourlyPayslipExists'] > 0 && 
                            result['isAllHourlyPayslipPaid'] == result['isHourlyPayslipExists']))){
                                isPayslipValid = -4;
                            }
                            /* If monthly and hourly payslip exists, but bank account not exists */
                            else if((isPayoutWizardFirstPage == 1 && (result['monthlyPayslipExists'] > 0 && result['monthlyPayslipCount'] == 0) && 
                            (result['isHourlyPayslipExists'] > 0 && result['hourlyPayslipCount'] == 0))||
                            (isPayoutWizardFirstPage !== 1 && (paySourceType == 'Monthly Payslip' && 
                            result['monthlyPayslipExists'] > 0 && result['monthlyPayslipCount'] == 0) || 
                            (paySourceType == 'Hourly Payslip' &&
                            result['isHourlyPayslipExists'] > 0 && result['hourlyPayslipCount'] == 0))){
                                isPayslipValid = -2;
                            }
                            else{
                                isPayslipValid = 1;
                            }
                        }
                    }else{
                        isPayslipValid = -1;
                    }
                    // removeMask();
                }else{
                    //Show something went wrong popup
                    isPayslipValid = -5;                    
                }
            },
            error: function (payslipExistsErrorResult) {
                if(payslipExistsErrorResult.status == 200){
                    //Show the session expired popup
                    isPayslipValid = -3;
                }else{
                    /* To handle internal server error */
                    isPayslipValid = -5; 
                }
            }
        });
       
        return isPayslipValid;
    }

    /* Function to validate month picker tab*/
    function validatePayoutMonthPickerTab(){
        $('#payoutmonthPickerPage').removeClass('tabvalid');

        /* Payslip related validations has to be checked for both monthly and hourly payslip in the first page only.
        In the remaining page, it has to be checked based on the paysource type */
        if($('#payoutform .sf-viewport .sf-step-0').hasClass('sf-step-active')){
            paySourceType = '';
            isPayoutWizardFirstPage = 1;
        }else{
            isPayoutWizardFirstPage = 0;
        }

        if($.inArray(sPayoutSalaryMonth,fieldValidation) == -1){
            //validate whether the payslip exists for the selected month or not when the next button is clicked
            var isMonthValid = checkPayslipExistsForPayoutMonth(sPayoutSalaryMonth,isPayoutWizardFirstPage);
            if(isMonthValid == 1){
                $('#payoutmonthPickerPage').addClass('tabvalid');
                return 1;
            }else if(isMonthValid == -1){
                // show the invalid salary month choosen alert
                $('#payoutErrorContent').text('Please select payout month');
                $('#modalPayoutError').modal('toggle');
                return 0;
            }else if(isMonthValid == -2){
                // If bank account is not associated to any one of the employees, show the alert
                $('#payoutErrorContent').text('Please associate the bank account details to the employee');
                $('#modalPayoutError').modal('toggle');
                return 0;
            }else if(isMonthValid == -3){
                sessionExpired();
                return 0;
            }else if(isMonthValid == -4){
                // If all the payslip for employees are settled or if the payment is initiated
                $('#payoutErrorContent').text('There is no outstanding payslips to be settled for employees');
                $('#modalPayoutError').modal('toggle');
                return 0;
            }else if(isMonthValid == -5){
                /* If empty response is returned or if internal server error occurs */
                $('#payoutErrorContent').text('Something went wrong. Please contact system admin');
                $('#modalPayoutError').modal('toggle');
                return 0;
            }else if(isMonthValid == -6){
                /* If aggregator details not exist */
                $('#payoutErrorContent').text('The aggregator ID is missing. Please contact your system administrator for configuration.');
                $('#modalPayoutError').modal('toggle');
                return 0;
            }else{
                // show the payslip not generated alert
                $('#modalPayslipWarning').modal('toggle');
                return 0;
            }
        }
        else{
            $('#payoutErrorContent').text('Please select payout month');
            $('#modalPayoutError').modal('toggle');
            return 0;
        }
    }

    /* Function to validate Debit account number tab */
    function validatePayoutPaymentModeTab(){
        $('#payoutPaymentModeTabValid').removeClass('tabvalid');
        if(($.inArray($('#payoutAccountId').val(),fieldValidation ) == -1 &&  $('#payoutAccountId') != 'Add your bank')){
            $('#payoutPaymentModeTabValid').addClass('tabvalid');
        }
        else{
            $('#payoutErrorContent').text('Please select debit account number');
            $('#modalPayoutError').modal('toggle');
            return 0;
        }
    }

    /* When the proceed button is clicked, show the next screen based on the
    available balance and the total salary */
    function validatePayoutListTab(){
        if(!!totalSalary && totalSalary > 0){
            /* If the available bal is less than total salary, then show the lower
            account balance popup */
            if(accountBalance < totalSalary){
                /* show the popup after set the box shadow and backgroud for the popup */
                if(!$('#modalLowerAccountBalancePopup').hasClass('payout-tx-popup-opacity')){
                    $('#modalLowerAccountBalancePopup').addClass('payout-tx-popup-opacity');
                }

                /* When the process button is clicked in the payout employees list page */
                $('#modalLowerAccountBalancePopup').modal('toggle');
            }else{
                if(isUserInitiated == 0){
                    validateEmployeeBankAccount(null);
                }
            }
        }else{
            /* Show the error popup*/
            $('#payoutErrorContent').text('Payment could not be processed as the total salary is less than or equal to zero');
            $('#modalPayoutError').modal('toggle');
        }
    }

    //Initial loading set the min and max value in salary slider
    function payslipSalarySlider(from,to){
        $('#minSalary').val(from);
        $('#maxSalary').val(to);
        updatePayslipRangeSlider(from,to)
    }

    // Call the action to get the payout employees for the selected month and paysource type
    function listPayoutEmployees(isFirstCall,isSearch=0){
        setMask('#wholepage');

        /* - During the first call empty all the filter and search fields
        - Reset the payout payslip data*/
        if(isFirstCall == 1){            
            var filterPayoutEmployeeType = filterPayoutLocation = filterPayoutDepartment = searchPayoutTransactionDetails = '';
            payslipData = ignoredPayslipData = payslipIds = [];
            totalSalary = 0;
        }else{
            var filterPayoutEmployeeType        = $('#s2id_filterPayoutEmployeeType').select2('val');
            var filterPayoutLocation            = $('#s2id_filterPayoutLocation').select2('val');
            var filterPayoutDepartment          = $('#s2id_filterPayoutDepartment').select2('val');
            var searchPayoutTransactionDetails  = $('#searchPayoutTransactionDetails').val() ? $('#searchPayoutTransactionDetails').val() : $('#searchPayoutTransaction').val() ;
            var payslipSalaryRangeFrom          =  JSON.parse($('#minSalary').val());
            var payslipSalaryRangeTo            =  JSON.parse($('#maxSalary').val());
        }

        if(previousSearchValue !== searchPayoutTransactionDetails || isSearch !== 1){
            previousSearchValue = searchPayoutTransactionDetails;
            $.ajax ({
                type     : 'POST',
                async    : true,
                dataType : 'json',
                url      : pageUrl() + 'payroll/payout/list-payout-employees',
                data     : {
                    Salary_Month : sPayoutSalaryMonth,
                    Paysource_Type: paySourceType,
                    Employee_Type: filterPayoutEmployeeType,
                    Location: filterPayoutLocation,
                    Department: filterPayoutDepartment,
                    searchTransactionDetails: searchPayoutTransactionDetails,
                    Payslip_Salary_Range_From : payslipSalaryRangeFrom?payslipSalaryRangeFrom:null,
                    Payslip_Salary_Range_To : payslipSalaryRangeTo?payslipSalaryRangeTo:null   
                },
                success  : function (listResult)
                {
                    $('.payout-filter-btn').removeClass('open');
           
                    if(listResult !== null && listResult !==''){
                      
                        var viewCard = listResult.viewCard;
                        payslipData = listResult.payoutPayslipData;

                        // Uncheck the select all and deselect all
                        $(".selectPayoutCheckbox,.deselectPayoutCheckbox").prop('checked', false);
                        if(isFirstCall == 1){
                            minSalaryPayslip=listResult.payslipMonthMinimumSalary;
                            maxSalaryPayslip=listResult.payslipMonthMaximumSalary;
                            payslipSalarySlider(listResult.payslipMonthMinimumSalary,listResult.payslipMonthMaximumSalary)
                            // If the paysource type is clicked, trigger the event to open the next tab                            
                            $('.sf-nav-step-2').trigger('click');
                        }

                        $("#payoutEmployeesViewcard").html('');
                        $("#payoutEmployeesViewcard").append(viewCard);

                        //If record exists, then check and uncheck the cards based on the user selection
                        if(payslipData.length > 0){
                            checkUncheckCards();
                        }

                        // set the account balance
                        $('#payoutform .sf-controls').attr("style","text-align:center;color: #484646;font-size: 1.1em;cursor: default;");
                        $('#payoutform .sf-controls .accountBalance').attr("style","color: #000;font-weight: 800;");
                        $('#payoutform .sf-controls .accountBalance').text(accountBalanceCurrency+' '+accountBalance);

                        sumTotalSalary();
                        removeMask();
                    }else{
                        removeMask();
                        $('#payoutErrorContent').text('Something went wrong. Please contact system admin');
                        $('#modalPayoutError').modal('toggle');
                    }

                    createPagination();
                },
                error  : function (listPayoutErrorResult){
                    removeMask();
                    if(listPayoutErrorResult.status == 200){                        
                        sessionExpired ();
                    }else{
                        /* To handle internal server error */
                        $('#payoutErrorContent').text('Something went wrong. Please contact system admin');
                        $('#modalPayoutError').modal('toggle');
                    }
                }
            });
        }else{
            removeMask();
        }
    }

    // Sum the total salary to be credited
    function sumTotalSalary(){
        totalSalary = 0;
        // If payslip data exists
        if(!!payslipData && payslipData != "" && payslipData.length > 0){
            for (var payslipIndex=0; payslipIndex<payslipData.length; payslipIndex++){
                /* If payslip id is unchecked, then it should not be considered to sum the total salary.
                If payslip id not exists in ignored payslip array, then it should be sum to get 
                the total salary amount */
                if($.inArray(payslipData[payslipIndex]['Payslip_Id'], ignoredPayslipData) == -1){
                    totalSalary = parseFloat(totalSalary)+parseFloat(payslipData[payslipIndex]['Total_Salary']);
                }
            }
            setTotalSalaryInWizardButton(totalSalaryCurrency,totalSalary);
        }else{
            setTotalSalaryInWizardButton(totalSalaryCurrency,0);
        }
    }

    /* Set the Total salary in Proceed button in wizard */
    function setTotalSalaryInWizardButton(accountBalanceCurrency,totalSalary){
        // Change the next button text to Proceed and append total salary and currency symbol
        $('#payoutform .sf-btn-next').text('');

        /* Show the total salary based on the screen size */
        setTotalSalaryBasedOnScreen(accountBalanceCurrency,totalSalary);
    }

    /* Function to get the selected payslip id in an array */
    function getSelectedPayslipId(){
        payslipIds = [];
        // If payslip data exists
        if(!!payslipData && payslipData != "" && payslipData.length > 0){
            for (var payslipIndex=0; payslipIndex<payslipData.length; payslipIndex++){
                /* If payslip id is unchecked, then it should not be considered for the payout */
                if($.inArray(payslipData[payslipIndex]['Payslip_Id'], ignoredPayslipData) == -1){
                    payslipIds.push(payslipData[payslipIndex]['Payslip_Id']);
                }
            }
        }
        return payslipIds;
    }

    // on click function for to get salary ranges
    $('#payslipRangeSelectButton').on('click',function(){
        $('#payslipRangeSelectButton').addClass('hidden');
        listPayoutEmployees();
    })
    
    $('#closePreferencePopupModel,#closeCategoryPopup').on('click',function(){
        $("#oneCategoryMatchedModal,#allCategoryMatchedModal,#fewCategoryMatchedModal,#bothCategoryNotMatched").hide(); 
    })

   
    /* call the action to send the otp to the user id */
    function requestOtp(isResend){
        setMask('#wholepage');
        var reqPayslipIds = getSelectedPayslipId();
        if(!!reqPayslipIds && reqPayslipIds != "" && reqPayslipIds.length > 0){
            $.ajax ({
                type     : 'POST',
                async    : true,
                dataType : 'json',
                url      : pageUrl() + 'payroll/payout/request-otp',
                data     : {
                    Transaction_Group_Id : transactionGroupId,
                    Organization_Account_Id:  $('#payoutAccountId').select2('val'),
                    Paysource_Id: reqPayslipIds[0], //get the first payslip id and send
                    Paysource: paySourceType,
                    Preference:changePreferenceTransactionType,
                    isPreferenceChanged:isChangePreferenceList1 || isChangePreferenceList2
                },
                success  : function (otpResult)
                {    
                    /* Hide the lower account balance incase if it is opened */
                    if($('#modalLowerAccountBalancePopup').hasClass('in')){
                        $('#modalLowerAccountBalancePopup').modal('hide');
                    }
                    isChangePreferenceList1 = isChangePreferenceList2 = false;
                    if(otpResult !== null && otpResult !==''){
                        if(!otpResult.success)
                        {
                            /* In case of failure, we will update the status for the transaction group id in the respective tables. 
                            So we have to reset it 0 to use the new transaction group for the next otp request. */
                            transactionGroupId = 0;

                            /* Show the error popup*/
                            $('#payoutErrorContent').text(otpResult.msg);
                            $('#modalPayoutError').modal('toggle');
                        }
                        else
                        {
                            /* Transaction group id to initate the first payment */
                            transactionGroupId = otpResult.transactionGroupId;

                            /* Show the otp screen for the first time only*/
                            if(isResend != 1){
                                showOtpPopup();
                            }else{
                                /* reset the timer */
                                setTimerForOtp();
                            }
                        }
                        removeMask();
                    }else{
                        $('#payoutErrorContent').text('Something went wrong. Please contact system admin.');
                        $('#modalPayoutError').modal('toggle');
                    }
                    removeMask();
                },
                error : function(requestOtpErrorResult){
                    /* Hide the lower account balance incase if it is opened */
                    if($('#modalLowerAccountBalancePopup').hasClass('in')){
                        $('#modalLowerAccountBalancePopup').modal('hide');
                    }

                    removeMask();

                    if(requestOtpErrorResult.status == 200){                        
                        sessionExpired ();
                    }else{
                        /* To handle internal server error */
                        $('#payoutErrorContent').text('Something went wrong. Please contact system admin.');
                        $('#modalPayoutError').modal('toggle');
                    }
                }
            });
        }
        else{
            /* Hide the lower account balance incase if it is opened */
            if($('#modalLowerAccountBalancePopup').hasClass('in')){
                $('#modalLowerAccountBalancePopup').modal('hide');
            }

            removeMask();

             /* Show the error popup*/
             $('#payoutErrorContent').text('No Payout employees found. Please try again');
             $('#modalPayoutError').modal('toggle');
        }
    }
    
    /* Show the otp popup after the otp is send */
    function showOtpPopup(){
        isUserInitiated = 0;

        /* Clear the otp while open */
        $('#payoutOtp').val('');

        /* Reset the otp input */
        $('#payoutOtp').removeClass('form-error');

        $('#payoutOtp-error').html('');

        /* Set the timer */
        setTimerForOtp();
        
        /* Show the otp screen */
        $('#modalOtpPopup').modal('toggle');
    }

    /* set the timer - max time for OTP is 3 minute */
    function setTimerForOtp(){
        timerText = '3:00';

        $('#otpResendTimer').text(timerText+" Minutes");

        setOtpTimer = setInterval(function() {
            var timer = timerText.split(':');

            /* parse string to integer */
            var minutes = parseInt(timer[0], 10);
            var seconds = parseInt(timer[1], 10);

            --seconds;

            minutes = (seconds < 0) ? --minutes : minutes;
            
            
            seconds = (seconds < 0) ? 59 : (seconds < 10) ? '0' + seconds : seconds;
            
            timerText = minutes + ':' + seconds;
            minOrSeconds = (minutes < 1) ? ' Seconds' : ' Minutes';

            if (minutes == 0 && seconds==0){
                /* reset the button text */
                $('#otpResendTimer').text('Resend');
                /* clear the timer */
                clearInterval(setOtpTimer);
                return;
            } else{
                /* set the timer in the button text */
                $('#otpResendTimer').text(timerText + minOrSeconds);
            }
        }, 1000);
    }


    /* call the action to initiate the first tx */
    function retrieveBankAccountBalance(){
        setMask('#wholepage');
        $.ajax ({
            type     : 'POST',
            async    : true,
            dataType : 'json',
            url      : pageUrl() + 'payroll/payout/retrieve-bank-account-balance',
            data     : {
                Organization_Account_Id: $('#payoutAccountId').select2('val')
            },
            success  : function (accBalanceResult)
            {
                if(accBalanceResult !== null && accBalanceResult !==''){
                    accountBalance = accBalanceResult.accountBalance;
                    accountBalanceCurrency = accBalanceResult.currency;
                    if(accBalanceResult.isAccountBalanceRetrieved && accBalanceResult.accountRegistered)
                    {
                        removeMask();
                        listPayoutEmployees(1);
                    }
                    else
                    {
                        removeMask();
                        /* show the error popup */
                        $('#payoutErrorContent').text(accBalanceResult.msg);
                        $('#modalPayoutError').modal('toggle');
                    }
                }else{
                    removeMask();
                    $('#payoutErrorContent').text('Something went wrong. Please contact system admin');
                    $('#modalPayoutError').modal('toggle');
                }
            },
            error  : function (accBalanceErrorResult){
                removeMask();
                if(accBalanceErrorResult.status == 200){
                    sessionExpired ();
                }else{
                    /* To handle internal server error */
                    $('#payoutErrorContent').text('Something went wrong. Please contact system admin');
                    $('#modalPayoutError').modal('toggle');
                }
            }
        });
    }

    /* call the action to initiate the first tx */
    function initiateFirstPayoutTx(){
        setMask('#wholepage');
        var reqPayslipIds = getSelectedPayslipId();
        if(!!reqPayslipIds && reqPayslipIds != "" && reqPayslipIds.length > 0){
            $.ajax ({
                type     : 'POST',
                async    : true,
                dataType : 'json',
                url      : pageUrl() + 'payroll/payout/initiate-icici-payment',
                data     : {
                    Transaction_Group_Id : transactionGroupId,
                    Organization_Account_Id: $('#payoutAccountId').select2('val'),
                    Paysource: paySourceType,
                    Paysource_Id: payslipIds, //get the first payslip id and send
                    OTP: $('#payoutOtp').val(),
                    Preference:changePreferenceTransactionType?changePreferenceTransactionType:null
                },
                success  : function (initiateResult)
                {
                    if(initiateResult !== null && initiateResult !==''){                        
                        if(initiateResult.isTxInitiated)
                        {
                            // hide the otp modal
                            hideOtpModal();

                            /* Update this flag to '1' on success to not validate all the tabs
                            while click the checkstatus button */
                            isUserInitiated = 1;

                            $('#checkStatusText').text("Your online salary process request to ICICI bank with account number "+accountNumber+ " under processing")

                            
                            // If the first tx is initiated, trigger the event to open the next tab                            
                            $('.sf-nav-step-3').trigger('click');
                        }
                        else
                        {
                            isUserInitiated = transactionGroupId = 0;
                            hideOtpModal();
                            /* Show the error popup*/
                            $('#payoutErrorContent').text(initiateResult.msg);
                            $('#modalPayoutError').modal('toggle');
                        }
                    }else{
                        isUserInitiated = transactionGroupId = 0;
                        hideOtpModal();
                        
                        $('#payoutErrorContent').text('There seems to be some technical difficulties, so do not reinitiate the transaction. Please wait for some time or contact system admin.');
                        $('#modalPayoutError').modal('toggle');
                    }
                    removeMask();
                },
                error  : function (initiateErrorResult){
                    isUserInitiated = transactionGroupId = 0;
                    hideOtpModal();
                    removeMask();
                    if(initiateErrorResult.status == 200){
                        sessionExpired ();
                    }else{
                        /* To handle internal server error */
                        $('#payoutErrorContent').text('There seems to be some technical difficulties, so do not reinitiate the transaction. Please wait for some time or contact system admin.');
                        $('#modalPayoutError').modal('toggle');
                    }
                }
            });
        }
        else{
            /* Show the error popup*/
            $('#payoutErrorContent').text('No Payout employees found. Please try again');
            $('#modalPayoutError').modal('toggle');
        }
    }

    /* hide the otp modal */
    function hideOtpModal(){
        isUserInitiated = 0;

        /* reset the button text */
        $('#otpResendTimer').text('Resend');

        clearInterval(setOtpTimer);
        $('#modalOtpPopup').modal('hide');
    }

    /* Function to lock the organization account if not locked.*/
    function lockPayoutAccountNumber(){
        // setMask('#wholepage');
        var isOrgAccountLocked = 1;
        $.ajax ({
            type     : 'POST',
            async    : false,
            dataType : 'json',
            url      : pageUrl() + 'payroll/payout/lock-payout-account-number/Organization_Account_Id/'+$('#payoutAccountId').select2('val'),
            data     : {},
            success  : function (payoutAccountLockResponse)
            {
                if(payoutAccountLockResponse !== null && payoutAccountLockResponse !==''){                        
                    /* If organization account is valid */
                    if(payoutAccountLockResponse.isOrgAccountValid){
                        /* If organization account is locked by other users */
                        if(payoutAccountLockResponse.orgAccountLockFlag > 0){
                            /* Show the error popup*/
                            $('#s2id_payoutAccountId').select2('val','');
                            $('#payoutErrorContent').text(payoutAccountLockResponse.msg);
                            $('#modalPayoutError').modal('toggle');
                        }else{
                            isOrgAccountLocked = 0;
                        }
                    }else{
                        /* Show the error popup*/
                        $('#s2id_payoutAccountId').select2('val','');
                        $('#payoutErrorContent').text(payoutAccountLockResponse.msg);
                        $('#modalPayoutError').modal('toggle');
                    }
                }else{
                    $('#payoutErrorContent').text('Something went wrong. Please contact system admin');
                    $('#modalPayoutError').modal('toggle');
                }
                // removeMask();
            },
            error  : function (payoutAccountLockErrorResponse){
                if(payoutAccountLockErrorResponse.status == 200){
                    sessionExpired ();
                }else{
                    /* To handle internal server error */
                    $('#payoutErrorContent').text('Something went wrong. Please contact system admin');
                    $('#modalPayoutError').modal('toggle');
                }
            }
        });

        return isOrgAccountLocked;
    }

    /*
    Function to check and uncheck the select all and deselect all checkbox
    based on the employee card check and uncheck
    */
    function checkUncheckSelectAll(){       
        //If all the records are unchecked, then uncheck the select all and check the deselect all
        if(payslipData.length == ignoredPayslipData.length){
            // Uncheck the select all
            $(".selectPayoutCheckbox").prop('checked', false);

            // Uncheck the deselect all
            $(".deselectPayoutCheckbox").prop('checked', 'checked');
        }else if(ignoredPayslipData.length == 0){
            // If all the records are checked,
            // Check the select all
            $(".selectPayoutCheckbox").prop('checked', 'checked');

            // Uncheck the deselect all
            $(".deselectPayoutCheckbox").prop('checked', false);
        }else{
            // If partial records are checked,
            // Uncheck the select all
            $(".selectPayoutCheckbox").prop('checked', false);

            // Uncheck the deselect all
            $(".deselectPayoutCheckbox").prop('checked', false);
        }
    }

    /* Check and uncheck cards based on the ignored employee payslip during list */
    function checkUncheckCards(){
        for (var payslipIndex=0; payslipIndex<payslipData.length; payslipIndex++){
            /* If payslip id is already unchecked, then it should be unchecked while reset */
            if($.inArray(payslipData[payslipIndex]['Payslip_Id'], ignoredPayslipData) == -1){   
                if(!$('#payout-'+payslipData[payslipIndex]['Payslip_Id']).is(':checked'))
                {
                    $('#payout-'+payslipData[payslipIndex]['Payslip_Id']).prop('disabled',true);
                }
            }else{
                if($('#payout-'+payslipData[payslipIndex]['Payslip_Id']).is(':checked'))
                {
                    $('#payout-'+payslipData[payslipIndex]['Payslip_Id']).prop('checked',false);
                }
            }

            /* Check and Uncheck the Select all and deselect all checkbox based on the card 
            checkbox selection */
            if(payslipIndex == payslipData.length-1){
                checkUncheckSelectAll();
                sumTotalSalary();
            }
        }
    }

    /* Reset the values while opening the form */
    function resetFormValues(){
        $('#payoutAccountId').val('');

        //hide the payslip card
        $('.payslip-card-hidden').hide();

        $('#payoutOtp').val('');
    }

    /* Reset payout form fields on slide change */
    function resetPayoutTabFieldsInSlideChange(){
        // add the primary buttons for wizard
        $('#payoutform .sf-btn-next').addClass('bg-primary');
        $('#payoutform .sf-btn-prev').addClass('bg-primary');
    
    
        // Hide the available Balance text in all the pages except payout employees list
        $('#payoutform .sf-controls .availableBalanceText').prop('style', 'display:none;');

        // Change the next button text to 'Next' as it is changed to proceed in payout employees list page
        $('#payoutform .sf-btn-next').text('Next');
        
        // Empty the content
        $('#payoutAvailableBalance').html("");
        $('#payoutTotalSalary').html("");
    }

    /* Reset payout filter fields */
    function resetFilterFields(){
        $('#payoutLocationFilterChipName').html("All");
        $('#s2id_filterPayoutLocation,#s2id_filterPayoutDepartment,#s2id_filterPayoutEmployeeType,#s2id_filterPayoutLocationXS,#s2id_filterPayoutDepartmentXS,#s2id_filterPayoutEmployeeTypeXS').select2('val','');
        $('#payoutLocationFilterChipName,#payoutDepartmentFilterChipName,#payoutEmployeeTypeFilterChipName').html("All");
        $('#payoutLocationFilterChipClose,#payoutDepartmentFilterChipClose,#payoutTypeFilterChipClose').hide();
        $('#minSalary').val(minSalaryPayslip);
        $('#maxSalary').val(maxSalaryPayslip);
        //Need to update the salary slider to default value
        $('#payslipSalaryRangeSliderStep').data("ionRangeSlider").update({
            from: minSalaryPayslip,
            to: maxSalaryPayslip
        });
    }

    /*
    - Delete the transaction group id from the mapping table.
    - Update the payment status as NULL for the payslip id associated to the 
    transaction group id
    */
    function resetPayoutPayslipData(){
        setMask('#wholepage');
        $.ajax ({
            type     : 'POST',
            async    : true,
            dataType : 'json',
            url      : pageUrl() + 'payroll/payout/reset-payout-payslip-data',
            data     : {
                Transaction_Group_Id : transactionGroupId
            },
            success  : function (resetResponse)
            {
                /* Status will be updated for the old transaction group id in the respective table. So reset the group id to  0 */
                transactionGroupId = 0;

                if(resetResponse !== '' && resetResponse !== null){
                    if(!resetResponse['success']){
                        $('#payoutErrorContent').text(resetResponse['msg']);
                        $('#modalPayoutError').modal('toggle');
                    }
                }else{
                    $('#payoutErrorContent').text('Something went wrong. Please contact system admin');
                    $('#modalPayoutError').modal('toggle');
                }
                removeMask();
            },
            error  : function (resetErrorResponse){
                /* Status will be updated for the old transaction group id in the respective table. So reset the group id to  0 */
                transactionGroupId = 0;

                removeMask();
                if(resetErrorResponse.status == 200){
                    sessionExpired ();
                }else{
                    /* To handle internal server error */
                    $('#payoutErrorContent').text('Something went wrong. Please contact system admin');
                    $('#modalPayoutError').modal('toggle');
                }
            }
        });
    }

    /* Reset the payslip view details style based on the screen size */
    function resetPayslipView(){
        if($(window).width() > 991 )
        {            
            $('#hideDiv,#form16HideDiv,#hideDivHrly').prop({'style':'display:none'});
            $('#divTable,#divTableHrly').prop({'style':'border:1px solid'});
            $('#earndiv,#deductdiv,#earnDivHrly,#deductDivHrly').prop({'style':''});
            $('#earningsTab').prop({'style':'border-right:1px solid;width:100%'});
            $('#earningsHourlyTab').prop({'style':'border-right:1px solid'});            
            $('#formHeader,#formHourlyHeader').prop({'style':'font-weight:bold;text-align:right'});
            $('#formAddress,#formAddress1,#formHourlyAddress,#formHourlyAddress1').prop({'style':'text-align:right'});
        }
        else
        {            
            $('#hideDiv,#form16HideDiv,#hideDivHrly').prop({'style':'height:20px'});
            $('#divTable,#divTableHrly').prop({'style':''});
            $('#earndiv,#deductdiv,#earnDivHrly,#deductDivHrly').prop({'style':'border:1px solid'});
            $('#earningsTab').prop({'style':''});
            $('#earningsHourlyTab').prop({'style':''});            
            $('#formHeader,#formHourlyHeader').prop({'style':'font-weight:bold;text-align:left'});
            $('#formAddress,#formAddress1,#formHourlyAddress,#formHourlyAddress1').prop({'style':'text-align:left'});
            $('#incent').prop({'style':''});
        }
    }
    
    /* Show the total salary based on the screen size */
    function setTotalSalaryBasedOnScreen(accountBalanceCurrency,totalSalary){
        $('#payoutTotalSalary').hide();

        /* Total salary in small screen */
        $('#payoutTotalSalary').html('');
        $('#payoutTotalSalary').append('<span class="zone-float">Total Salary:<span class="payout-history-view-amount">'+accountBalanceCurrency+totalSalary+'</span></span>');

        if($(window).width() > 1189 ){
            if($('#payoutform .sf-viewport .sf-step-2').hasClass('sf-step-active')){
                $('#payoutform .sf-btn-next').text('');
                $('#payoutform .sf-btn-next').append('<span>'+accountBalanceCurrency+totalSalary+'</span><span> Total Salary </span><span style="margin-left:30px">Proceed</span>');
            }
        }else{
            if($('#payoutform .sf-viewport .sf-step-2').hasClass('sf-step-active')){
                $('#payoutform .sf-btn-next').text('');

                $('#payoutform .sf-btn-next').append('<span style="">Proceed</span>');
                $('#payoutTotalSalary').show();
            }
        }
    }
   
    //to make preference draggable
    $("#preference-list1").sortable({ cancel: ".ui-state-disabled"});

    $( "#preference-list2" ).sortable({
        cancel: ".ui-state-disabled"
     });

    /*  - Validate the employees salary account is active or not before initiating the payment.
        - Validate whether the employee bank account is other than ICICI based on which category popup will be displayed
    */
    function validateEmployeeBankAccount(transactionCategoryArray){  
        setMask('#wholepage');
        var reqPayslipIds = getSelectedPayslipId();
        //If preference changed compare the value of updated and old array. If the position changed return true
        if(transactionCategoryArray){    
            for(var i=1;i<=Object.keys(transactionCategoryArray).length;i++)
            {
                var checkPreferenceArray = array_diff(initialPreferences[i]["Transaction_Types"], transactionCategoryArray[i]["Transaction_Types"] ); 
                if(transactionCategoryArray[i].Category_Id==1){
                   checkPreferenceArray ? isChangePreferenceList1 = true: isChangePreferenceList1 = false;
                }
                if(transactionCategoryArray[i].Category_Id==2){
                  checkPreferenceArray ? isChangePreferenceList2 = true: isChangePreferenceList2 = false;
                }
            }
        }
        if(!!reqPayslipIds && reqPayslipIds != "" && reqPayslipIds.length > 0){
            $.ajax ({
                type     : 'POST',
                async    : true,
                dataType : 'json',
                url      : pageUrl() + 'payroll/payout/check-payout-employees-txn-type',
                data     : {
                    Paysource: paySourceType,
                    Paysource_Id: payslipIds,
                    Organization_Account_Id:  $('#payoutAccountId').select2('val'),
                    ChangedPreference:transactionCategoryArray
                },
                success  : function (validateResponse)
                {
                    if(validateResponse !== '' && validateResponse !== null){
                    if(validateResponse['success']){
                    if(validateResponse['isAllEmployeesBankAccountICICI'] == 0)
                    {
                        changePreferenceTransactionType=validateResponse.categoryTransactionType;
                        preferenceMatchedCategory=validateResponse.matchedCategory;
                        var resultdata=validateResponse.categoryTransactionType;
                        //Display the popup model when one category is selected and preferences matched
                        if(validateResponse.popup=='oneCategoryMatchedModal'){
                            var categoryView = ""; 
                            for (var i = 0; i <= resultdata.length-1; i++) { 
                                categoryView += "<div class='modalWidth'><p class='text-center oneCategoryContent'>Your payment type </p>";
                                categoryView += "<p class='text-center oneCategoryContent'>is selected as</p>"
                                categoryView += "<p class='text-center oneCategoryContent'>" + resultdata[i]['transaction_type'] +"</p>";
                                categoryView += "<p class='text-center oneCategoryContent'>from your preference</p><p class='margin;'> Do you wish to proceed?</p></div>"
                            }
                            $("div#oneCategoryMatchedModal").empty(); 
                            $("div#oneCategoryMatchedModal").append(categoryView); 
                            $('#confirmTransaction,#oneCategoryMatchedModal').show(); 
                            $("#applyFilter").hide();
                        }
                        //Display the popup when preferences is matched for all the category
                        else if(validateResponse.popup=='allCategoryMatchedModal'){
                                var categoryView = "";
                                categoryView += "<div class='modalWidth'><p class='text-center oneCategoryContent'>Your payment type</p>";
                                categoryView += "<p class='text-center'>is selected as </p></div>";
                                // Map the transaction preference along with category 
                                categoryView += "<div class='row'>";
                                for (var i = 0; i <= resultdata.length-1; i++) { 
                                    categoryView += "<p><div class='col-md-7 col-sm-7 col-xs-7 allcategory-prefertype'>"+ resultdata[i]['Category'] + "<br/></div><div class='col-md-5 col-sm-5 col-xs-5 allcategory-prefertype' ><b>" + resultdata[i]['transaction_type'] +  "<br/></b></div></p>";
                                }
                                categoryView += "</div>";
                                categoryView += "<div class='allMatchedContent'><p class='text-center oneCategoryContent'>from your preference</p>";
                                categoryView += "<p class='text-center'>Do you wish to proceed? </p></div>";
                                $("div#allCategoryMatchedModal").empty(); 
                                $("div#allCategoryMatchedModal").append(categoryView); 
                                $('#confirmTransaction,#allCategoryMatchedModal').show(); 
                                $("#applyFilter").hide();
                        }
                        //Display this popup when only one preference matched
                        else if(validateResponse.popup=='fewCategoryMatchedModal'){
                            var categoryView = ""; 
                                categoryView += "<p class=text-center>Few transactions are exceeding / not eligible for the payment preference set by the System admin.You can contact the system admin to change the preference or proceed with the other transactions using the filter option to restrict the payments " + validateResponse.matchedCategory+" </p><br/>";
                                for (var i = 0; i <= resultdata.length-1; i++) { 
                                    categoryView += "<div class='row oneCategoryContent'><div class='col-md-7 col-sm-7 col-xs-7 allcategory-prefertype'>"+ resultdata[i]['Category'] + "<br/></div><div class='col-md-5 col-sm-5 col-xs-5 allcategory-prefertype'><b>" + resultdata[i]['transaction_type'] +  "<br/></b></div></div>";
                                }
                                $("div#fewCategoryMatchedModal").empty(); 
                                $("div#fewCategoryMatchedModal").append(categoryView); 
                                $('#confirmTransaction').hide(); 
                                $("#applyFilter, #fewCategoryMatchedModal").show();
                        }
                        //Notes will be shown only for NEFT transaction type
                        if(validateResponse.popup!='bothCategoryNotMatched'){
                            if(validateResponse.notes.length>0){
                                var notesView = ""; 
                                notesView += "<p class='listNotes note-content'>Note:</p>";
                                // Execute the transaction preference view based on transaction types count 
                                for (var i = 0; i < validateResponse.notes.length; i++) {
                                    notesView += "<p class='transactionPreferenceNotesList note-preference-list-content'>" + 
                                    (i+1) + ". " + validateResponse.notes[i] + "</p>";
                                }
                                $("div#transactionNotes").empty(); 
                                $("div#transactionNotes").append(notesView); 
                                $("div#transactionNotes").show();
                                $('#showPreferencePopup').modal('toggle');
                            }
                            else{
                                $("div#transactionNotes").hide();
                                $('#showPreferencePopup').modal('toggle');
                            }
                        }
                        //If preference does not matched for both category show this popup                       
                        else{
                            $('#WarningContent').text('Transactions are not eligible for the payment preference set by the System admin.Please contact the system admin/payroll admin to change the preference');
                            $('#bothCategoryNotMatched').modal('toggle');
                            //Check whether login user has access rights for EFT configuration. If exist display "Change" option
                            if(validateResponse.eftRights==="1")
                            {
                                $("#changeEFTPreferences").show();
                                $("#closeNotMatchedModel").hide();
                            }
                            else
                            {
                                $("#changeEFTPreferences").hide();
                                $("#closeNotMatchedModel").show();
                            }
                        }
                        removeMask();
                    }
                    else{
                        requestOtp(0);  
                    }
                }else{
                    /* If some other error occurs or if the salary account is not associated to the
                    selected employees */                            
                    $('#payoutErrorContent').text($validateResponse['msg']);
                    $('#modalPayoutError').modal('toggle');
                }
                    }else{
                        $('#payoutErrorContent').text('Something went wrong. Please contact system admin');
                        $('#modalPayoutError').modal('toggle');
                    }
                },
                error  : function (validateErrorResponse){
                    removeMask();
                    if(validateErrorResponse.status == 200){
                        sessionExpired ();
                    }else{
                        /* To handle internal server error */
                        $('#payoutErrorContent').text('Something went wrong. Please contact system admin');
                        $('#modalPayoutError').modal('toggle');
                    }
                }
            }); 
        }else{
            removeMask();
            /* Show the error popup*/
            $('#payoutErrorContent').text('No Payout employees found. Please try again');
            $('#modalPayoutError').modal('toggle');   
        }
    }

    /* To display the transaction type notes in change preference screen*/
    function showTransactionTypesNotes(note){
        var count=1;
        var defaultNotesView = "";
        defaultNotesView += "<div id='transactionNotesView' class='preferencePayout'> <h6  class='transactionNotes edit-perference-modal-payout'> Notes: </h6></div>";
        //display the notes for RTGS type
        defaultNotesView += "<p class='transaction-type line-height-sm'>"+ count +". NEFT is now available for 24x7.</p>";
        for(i=0;i<=note.length-1;i++){
            count++;
            defaultNotesView += 
            "<p class='transaction-type line-height-sm'>" + (count) + ". The transaction timings for " + note[i]['Transaction_Type']+ " is "+ note[i]['Start_Time'].split(':')[0]+"."+note[i]['Start_Time'].split(':')[1]+"AM to "+note[i]['End_Time'].split(':')[0]+"."+note[i]['End_Time'].split(':')[1]+"PM(IST), Monday to Saturday (Except 2nd and 4th Saturday).</p>";
        }
        defaultNotesView += "<p class='transaction-type line-height-sm'>"+(count+1)+". In case your employee's bank account is ICICI, then the system will override the transaction type to internal (i.e ICICI to ICICI).</p>";
        $("div#transactionTypesNotes").empty();
        $("div#transactionTypesNotes").append(defaultNotesView);
    }


    // function to get the static preferences
    function staticPreference(option, catIdx){
        setMask('#wholepage');
        $.ajax ({
            type     : 'POST',
            url      : pageUrl()+'organization/eft-configuration/get-default-transaction-preferences/',
            async    : true,
            dataType :"json",
            data     : {
                Organization_Account_Id:  $('#payoutAccountId').select2('val')
            },
            success  : function(result)
            {
                var orgAccountTransactionPreferences=result.orgTransactionPreferences;

                if(orgAccountTransactionPreferences){
                    if(option == "default"){
                        transactionCategory = orgAccountTransactionPreferences;
                        //Assign this preference to variable inorder to validate the preference changed or not
                        initialPreferences = JSON.parse(JSON.stringify(orgAccountTransactionPreferences));
                        $('#modalChangePreference').modal('show');
                    }else{
                        transactionCategory[catIdx]["Transaction_Types"] = [];
                        orgAccountTransactionPreferences[catIdx]["Transaction_Types"].forEach(val => {
                        transactionCategory[catIdx]["Transaction_Types"].push(val);
                        });
                    }
                    bindPreferenceTypes();
                    showTransactionTypesNotes(result.notes);
                    removeMask();   
                }
            },
            error : function(errorResult){
                removeMask();
                if(errorResult.status == 200){
                     sessionExpired();
                }else{
                 /* To handle internal server error */
                  jAlert({ msg : "Something went wrong. Please contact system admin", type : "warning" });
                }
            }
        });
    }


    /*Function to display each preference card under that category*/
    function preferenceCard(id,catId,transactionTypes,className){
        var card='<div class="text-center white-text  m-2 preferenceCard'+className+'" categoryId="'+catId+'" preferenceName="'+transactionTypes+'">'+
        ' <div class="card " id="transactionPreferenceCategory'+catId+'">'+
        '        <div class="card-body skill-card-body-margin"> '+
        '           <div class="col-xs-12 m-t-0 card-body-content test">'+
        '              <p class="transactionPreferenceList preferencePayout">'+
        '                   <i class="fa fa-align-justify edit-preference-justify justify-margin skill-card-content-margin edit-perference-modal-payout" aria-hidden="true"></i>'+
        '                 <span class="edit-preference-name edit-preferencename-modal-payout">'+
        '                         <label>'+  transactionTypes+'</label>'+
        '                 </span>'+
        '              </p>'+                    
        '           </div>'+
        '         </div>'+
        '     </div>'+
        '</div>';
        return card;
    }
    
    //Bind the transaction preference based on category
    function bindPreferenceTypes(){       
        $.each(transactionCategory, function(key, value) {
            var preferenceView = "";
            preferenceView += "<div class='text-center-sm'><span class='paymentCategoryHeader ui-state-disabled'>Payment Preference " +
            transactionCategory[key]["Category_Name"] +
            "</span>";
            preferenceView +="<button type='button' class='btn btn-sm payout-reset-btn resetFilter reset-preference-btn reset-preference-btn-modal-payout' id='resetOrganizationPreference' onclick='resetSelectedPreference("+key+")'>Reset<div class='ripple-wrapper'></div></button></div>";
            var transactionTypes = transactionCategory[key]["Transaction_Types"];
            //Bind the preference of category1 and 2 separately
            if(transactionCategory[key]['Category_Id']==1){
                $('#preference-list1').html('');
                $('#preference-list-header1').html('');
                $('#preference-list-header1').append(preferenceView);
            }
            else{
                $('#preference-list2').html('');
                $('#preference-list-header2').html('');
                $('#preference-list-header2').append(preferenceView);
            }           
            preferenceView="";
            //Define the classname
            var className = 'EachPreference' + transactionCategory[key]['Category_Id'];
            // Execute the transaction preference view based on transaction types count
            for (var i = 0; i < transactionTypes.length; i++) {
                preferenceView +=preferenceCard(i,transactionCategory[key]['Category_Id'],transactionTypes[i],className);
            }
            //Append the transaction preference based on categoryId
            if(transactionCategory[key]['Category_Id']==1){
                $('#preference-list1').append(preferenceView);
            }
            else{
                $('#preference-list2').append(preferenceView);
            }
        });
        $("#transactionNotesView").show();
    }

    //To reset the particular category preferences  
    $.resetSelectedPreferenceList = function(catId){
        /*If user wants to reset the preference of category2, we need to update the 
        changes in category1 in that same array*/
        var isPreference = resetPreferenceList(catId);
        if(isPreference){
            staticPreference("reset", catId);
        }          
    };

    /* Function to create the pagination during page load, search, and filter in payout employees list page*/
    function createPagination(){
        var paginationId = '#payoutEmployeesViewcard'; // Id of payout employees list card
        var dataclass = '.payout-child'; // class of the each payout payslip cards

        $('.custom-pagination').html('');

        /* Call the function to create/update the pagination 
        Params - Length of the payout records, Max record to be filled in a page, id of the employees list card, class of the each payout payslip cards */
        var paginationSize = 5; //set the max no.of pages to be displayed.
        $.pagination($('#payoutEmployeesViewcard .payout-child').length, 8, paginationId, dataclass,paginationSize); 
    }
    
    //On second modal close, first modal scroll will not work. To avoid that,using this
    $('#modalPayslipWarning,#modalLowerAccountBalancePopup,#modalOtpPopup,#modalPayoutError,#modalFormHourlyWagesPayslip,#modalFormMonthlySalary').on('hidden.bs.modal', function () {
        if($('#modalPayslipWarning,#modalLowerAccountBalancePopup,#modalOtpPopup,#modalPayoutError,#modalFormHourlyWagesPayslip,#modalFormMonthlySalary').is(':visible'))
            $('body').addClass('modal-open');
    });

    //When user changes the preference again validate the changed preference based on which display the popup
    $('#savePreference').on('click', function(){
        $('#modalChangePreference').modal('hide');
        var isPreference = resetPreferenceList(0);
        if(isPreference){
            validateEmployeeBankAccount(transactionCategory);
        }      
    });

    //On second modal close, first modal scroll will not work. To avoid that,using this
    $('#modalChangePreference').on('hidden.bs.modal', function () {
        if($('#sessionExipredModel,#oneCategoryMatchedModal,allCategoryMatchedModal,fewCategoryMatchedModal').is(':visible'))
        {
            $('body').addClass('modal-open');
        }
        });

    //Function to check the difference of 2 array
    function array_diff(array1, array2) {
        if(array1.length==array2.length)
        {
            //Compare the elements array along with position
            for(var i=0;i<=array1.length;i++){
                if(array1[i]!=array2[i]){
                    return 1;
                }
            }
            return 0;
        }
        else{
            return 1;
        }
    }

    //Function to update the payslip range slip
    function updatePayslipRangeSlider(From,To){
        // initialise ion range sliders and set the values to the payslip range slider
        $("#payslipSalaryRangeSliderStep").ionRangeSlider({
            skin: "round",
            type: "double",
            min: From,
            max: To,
            from: From,
            to: To,
            step:1,
            onChange: function (data) {
                $('#minSalary').val(data.from);
                $('#maxSalary').val(data.to);
                $('#payslipRangeSelectButton').removeClass('hidden');
    
            }
        });
    }
    var salarySliderPayslip = $("#payslipSalaryRangeSliderStep").data("ionRangeSlider");

    //Function to reset the particular preference list based on category Id
    function resetPreferenceList(catId){
        if(catId!=1)
        {
            $('#preference-list1 .preferenceCardEachPreference1').each(function(i){
                var categoryName= $(this).attr('preferenceName');
                var carIdx = $(this).attr('categoryId');         
                if(i==0) {
                transactionCategory[carIdx]["Transaction_Types"] = [];
                }
                transactionCategory[carIdx]["Transaction_Types"].push(categoryName);
            });
        }
        if(catId!=2)
        {
             //categoryId, transactionkey
            $('#preference-list2 .preferenceCardEachPreference2').each(function(i){
                var categoryName= $(this).attr('preferenceName');
                var carIdx = $(this).attr('categoryId');          
                if(i==0) {
                transactionCategory[carIdx]["Transaction_Types"] = [];
                }
                 transactionCategory[carIdx]["Transaction_Types"].push(categoryName);
            });
        }
        return true;
    }
});
    //function to reset preferences
    function resetSelectedPreference(catId){
        $.resetSelectedPreferenceList(catId);
    }


