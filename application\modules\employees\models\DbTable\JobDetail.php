<?php
//=========================================================================================
//=========================================================================================
/* Program : JobDetail.php											   			         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,	                                     *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MQL Query to retrive employee job details							     *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Narmadha            	  Initial Version        	         *
 *  0.2		   25-Jul-2014	  Mahesh				  Added 1.employeeApproverId()       *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Sandhosh                Changes in file for mobile app     *
 *                                                    1.Extra fields are added in        *
 *                                                    field list of list query.          *
 *                                                                                       *
 *  1.4        06-Mar-2016    Nivethitha              Changes in file for Bootstrap      *
 *                                                                                       */
//=========================================================================================
//=========================================================================================
class Employees_Model_DbTable_JobDetail extends Zend_Db_Table_Abstract
{

    protected $_db = null;

    protected $_ehrTables = null;
    
    protected $_orgDF = null;
    
    public function init()
    {
        $this->_ehrTables = new Application_Model_DbTable_Ehr();
        $this->_db = Zend_Registry::get('subHrapp');
        $this->_orgDF = $this->_ehrTables->orgDateformat();
    }
    
	// to get all the employee id who is the reportee for the manager.
    public function managersReportee($employeeId)
    {
        return $this->_db->fetchCol($this->_db->select()
									->from(array('J'=>$this->_ehrTables->empJob), array('Employee_Id'))
									
									->joinInner(array('P'=>$this->_ehrTables->empPersonal),'J.Employee_Id=P.Employee_Id',array())
									
									->where('J.Manager_Id = ?', $employeeId)
									->where('P.Form_Status = 1')
									->where('J.Emp_Status Like ?', 'Active'));
    }
	
	// to get all the employee name who is the reportee for the manager.
    public function managersReporteesName($employeeId)
    {
        return $this->_db->fetchAll($this->_db->select()
									->from(array('J'=>$this->_ehrTables->empJob),
										   array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name)"),
												 'D.Designation_Name'))
									
									->joinInner(array('P'=>$this->_ehrTables->empPersonal),'J.Employee_Id=P.Employee_Id',array())
									
									->joinInner(array('D'=>$this->_ehrTables->designation),'D.Designation_Id=J.Designation_Id',
												array(''))
									
									->where('J.Manager_Id = ?', $employeeId)
									->where('P.Form_Status = 1')
									->where('J.Emp_Status Like ?', 'Active')
									->order('P.Emp_First_Name ASC'));
    }
	
	/**
	 * Get approver details by employeeId
	 */
    public function approverEmail($empId)
    {
        $qryManagerId = $this->_db->select()->from(array('J'=>$this->_ehrTables->empJob), array('Manager_Id'))
        ->joinInner(array('P'=>$this->_ehrTables->empPersonal),'J.Manager_Id=P.Employee_Id',array())
        ->where('J.Employee_Id = ?', $empId)->where('P.Form_Status =1')->where('J.Emp_Status Like ?', 'Active');
        $rowManagerId = $this->_db->fetchOne($qryManagerId);
        $qryApprover = $this->_db->select()->from($this->_ehrTables->empPersonal, array('Emp_First_Name', 'Emp_Last_Name', 'Employee_Id'))
        ->where('Form_Status = 1');
        if($rowManagerId!=NULL){
            $qryApprover->where('Employee_Id = ?', $rowManagerId);
        }
        else{
            $qryApprover->where('Employee_Id = ?', $empId);
        }
         
        $rowApprover = $this->_db->fetchRow($qryApprover);
        return $rowApprover;
         
    }
	
	/**
	 * Get approver name by employeeId
	 */
    public function approverName($empId)
    {
        $managerId = $this->_db->fetchOne($this->_db->select()->from(array('J'=>$this->_ehrTables->empJob), array('Manager_Id'))
										->joinInner(array('P'=>$this->_ehrTables->empPersonal),'J.Manager_Id=P.Employee_Id',array())
										
										->where('J.Employee_Id = ?', $empId)
										->where('P.Form_Status =1')
										->where('J.Emp_Status Like ?', 'Active'));
        
        if($managerId!=NULL)
        {
            return $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->empPersonal,
													  array('Employee_Name'=>new Zend_Db_Expr("CONCAT(Emp_First_Name,' ',Emp_Last_Name)"), 'Employee_Id'))
											
											->where('Form_Status = 1')
											->where('Employee_Id = ?', $managerId));
        }
    }
	
    //getting the employeeId, employeeName, approverId
    public function employeeApproverId($empArray)
    {
    	$empQry=$this->_db->select()
        ->from(array('emp'=>$this->_ehrTables->empPersonal),
        array('emp.Employee_Id',new Zend_Db_Expr("CONCAT(Emp_First_Name, ' ', Emp_Last_Name) as Employee_Name"),'J.Manager_Id'))
        ->joinInner(array('J'=>$this->_ehrTables->empJob), 'emp.Employee_Id=J.Employee_Id', array());
        $empQry->where('emp.Employee_Id IN (?)',$empArray)
        ->where('emp.Form_Status =?',1);

        $rowEmp = $this->_db->fetchAll($empQry);

        return $rowEmp;
    	
    	
    }
    
	// to check whether the logged employee is a manager
    public function isManager($empId)
    {
        $qryIsManager = $this->_db->select()->from($this->_ehrTables->empPersonal, 'Is_Manager')
        ->where('Form_Status = 1')->where('Employee_Id = ?', $empId);
        $rowIsManager = $this->_db->fetchOne($qryIsManager);
        return $rowIsManager;
    }
    
	// to get the grade and location for the chosen employee
    public function getGradeLocation($employeeId)
    {
        $qryGetGL = $this->_db->select()->from(array('J'=>$this->_ehrTables->empJob), 'J.Location_Id')
							->joinInner(array('P'=>$this->_ehrTables->empPersonal),'J.Employee_Id=P.Employee_Id',array())
							->joinInner(array('D'=>$this->_ehrTables->designation), 'D.Designation_Id=J.Designation_Id', 'D.Grade_Id')
							->joinInner(array('G'=>$this->_ehrTables->empGrade), 'G.Grade_Id = D.Grade_Id', 'OvertimeFixedAmount')
							->joinLeft(array('S'=>$this->_ehrTables->salary), 'J.Employee_Id = S.Employee_Id',
									   array('S.Is_OvertimeEmployee','S.Overtime_Wage'))
							->where('J.Employee_Id = ?', $employeeId)
							->where('P.Form_Status = 1');
        
		$rowGetGL = $this->_db->fetchRow($qryGetGL);
        
		if ($rowGetGL['Is_OvertimeEmployee'] == 'EMP')
		{
        	$rowGetGL['OvertimeFixedAmount'] = $rowGetGL['Overtime_Wage']; 
        }
        
		return $rowGetGL;
    }
    
	/**
	 * Get employee location details by employeeId
	 */
	public function getEmpLocation($empId)
    {
    	if(!empty($empId))
    	{
    		return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob, 'Location_Id')
    														->where('Employee_Id = ?',$empId));
    	}
    }
    
    // to displays employee job details in the tooltip
    public function empJobInfo($empId)
    {
        $qryInfo = $this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal),array())
        ->joinInner(array('U'=>$this->_ehrTables->empLogin),'U.Employee_Id=P.Employee_Id', array('User_Name'))
        ->joinLeft(array('J'=>$this->_ehrTables->empJob),'J.Employee_Id=P.Employee_Id', 'Emp_Email')
        ->joinLeft(array('T'=>$this->_ehrTables->empType), 'J.EmpType_Id=T.EmpType_Id', array('Employee_Type'))
        ->joinInner(array('D'=>$this->_ehrTables->dept), 'J.Department_Id=D.Department_Id', 'Department_Name')
        ->joinInner(array('Ds'=>$this->_ehrTables->designation), 'J.Designation_Id=Ds.Designation_Id', 'Designation_Name')
        ->joinLeft(array('L'=>$this->_ehrTables->location), 'L.Location_Id=J.Location_Id', 'Location_Name')
        ->joinLeft(array('C'=>$this->_ehrTables->empContacts), 'C.Employee_Id=J.Employee_Id', array('Mobile_No', 'Land_Line_No')	)
        ->where('J.Employee_Id = ?', $empId)->where('P.Form_Status = 1');
        $rowInfo = $this->_db->fetchRow($qryInfo);

        return $rowInfo;
    }
	
	/**
	 * Get employee mailid by employeeId
	 */
    public function getEmployeeEmailId($empId)
    {
        $qryEmail = $this->_db->select()->from(array('J'=>$this->_ehrTables->empJob), 'Emp_Email')
        ->joinLeft(array('P'=>$this->_ehrTables->empPersonal),'J.Employee_Id=P.Employee_Id',array('Employee_Name'=>new Zend_Db_Expr("CONCAT(Emp_First_Name,' ',Emp_Last_Name)")))
        ->where('J.Employee_Id = ?', $empId);
        $rowEmail = $this->_db->fetchRow($qryEmail);
        return $rowEmail;
    }
	
	/**
	 * Get designation details from experience table
	 */
    public function getExperienceDetails()
    {
        $getExperience = $this->_db->select()->from($this->_ehrTables->empExperience,array('Designation'))->group('Designation');
        $result = $this->_db->fetchCol($getExperience);
        return $result;
    }
	
	/**
	 * Get employee training details
	 */
    public function getEmpTraining()
    {
        $empTraining = $this->_db->select()->from($this->_ehrTables->empTraining,array('Training_Name'))->group('Training_Name');
        $result = $this->_db->fetchCol($empTraining);
        return $result;
    }
	
    /**
     *
     * get pf policynumber for employee ...
     * @param int $empId - employee
     * @return policynumber
     */
    public function getPfPolicyNo($empId) {
        return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob, 'Pf_PolicyNo')->where('Employee_Id = ?', $empId));
    }
	
    
    /**
     * get employee's date of join
     * @param unknown_type $empId
     *
     */
	public function getDateOfJoin($empId,$isActInActEmpDOJFetch=NULL,$checkFormStatus=1)
	{
		$qryEmpId = $this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),'Date_Of_Join')
								->joinInner(array('P'=>$this->_ehrTables->empPersonal),'J.Employee_Id = P.Employee_Id',array())
                                ->where('J.Employee_Id = ?', $empId);
                                
        /** If the form status has to be checked */
        if((int)$checkFormStatus === 1){
            $qryEmpId->where('P.Form_Status =1');
        }
        /**If the employee DOJ has to be fetched only when the employee status is active */
        if(IS_NULL($isActInActEmpDOJFetch)){
            $qryEmpId->where('J.Emp_Status Like ?', 'Active');
        }

		return $this->_db->fetchOne($qryEmpId);
	}
    
    public function getProbationDate($empId)
    {
  		$qryEmpId = $this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),'Probation_Date')
								->joinInner(array('P'=>$this->_ehrTables->empPersonal),'J.Employee_Id = P.Employee_Id',array())
                                ->where('J.Employee_Id = ?', $empId)
                                ->where('P.Form_Status =1')
                                ->where('J.Emp_Status Like ?', 'Active');
        
		return $this->_db->fetchOne($qryEmpId);
	}
    
	/**
	 * Get mid join employee details between dates
	 */
	public function getJoinDetailsBetweenDates($dates, $employeeId = NULL)
	{
		$fiscalWhere = $this->_db->quoteInto('Date_Of_Join BETWEEN ?', date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($dates['finstart'])))).
					   $this->_db->quoteInto(' AND ?', date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($dates['finend']))));
		$getJoinDetailsBetweenDatesQry = $this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),array('J.Employee_Id','Date_Of_Join'))
															->joinInner(array('P'=>$this->_ehrTables->empPersonal),'J.Employee_Id = P.Employee_Id AND P.Form_Status = 1',array())
															->joinLeft(array('R'=>$this->_ehrTables->resignation), 'J.Employee_Id = R.Employee_Id' ,array(
                                                                'Resignation_Date'=>new Zend_Db_Expr('(CASE WHEN `R`.`Approval_Status` = "Approved" THEN `R`.`Resignation_Date`
                                                                WHEN ( `J`.`Emp_Status` = "InActive" AND `J`.`Emp_InActive_Date` IS NOT NULL ) THEN `J`.`Emp_InActive_Date` ELSE NULL END)')
                                                            ))
															->where($fiscalWhere);
															
        if (!is_null($employeeId))
        {
            /*opening balance calculation in salary payslip this will be used*/
            $getJoinDetailsBetweenDatesQry->where('P.Employee_Id =?',$employeeId)
                        ->where('J.Emp_Status IN (?)',array('Active','InActive'));
        }
        else 
        {
            $getJoinDetailsBetweenDatesQry->where('J.Emp_Status Like ?', 'Active');
        }

        $getJoinDetailsBetweenDatesQry->group('J.Employee_Id');

        return $this->_db->fetchAll($getJoinDetailsBetweenDatesQry);
    }
    /**
	 * when the probation date fall is current leave closure year or future leave closure year we need to get those employee details
	 */
	public function getProbationDetailsBetweenDates($dates)
	{
		$getProbationDetailsBetweenDatesQry = $this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),array('J.Employee_Id','Date_Of_Join'))
															->joinInner(array('P'=>$this->_ehrTables->empPersonal),'J.Employee_Id = P.Employee_Id AND P.Form_Status = 1',array())
                                                            ->joinLeft(array('R'=>$this->_ehrTables->resignation), 'J.Employee_Id = R.Employee_Id' ,array(
                                                                'Resignation_Date'=>new Zend_Db_Expr('(CASE WHEN `R`.`Approval_Status` = "Approved" THEN `R`.`Resignation_Date`
                                                                WHEN ( `J`.`Emp_Status` = "InActive" AND `J`.`Emp_InActive_Date` IS NOT NULL ) THEN `J`.`Emp_InActive_Date` ELSE NULL END)')
                                                            ))
                                                            ->where('J.Emp_Status Like ?', 'Active')
                                                            ->where('J.Probation_Date >= ?',$dates['finstart'])
                                                            ->group('J.Employee_Id');
        return $this->_db->fetchAll($getProbationDetailsBetweenDatesQry);
	}
   		
	public function getActiveEmployeesDetail($empLeaveCoverage)
	{
	    $getActiveEmployeesDetail = $this->_db->select()->distinct()->from(array('J'=>$this->_ehrTables->empJob),array('J.Employee_Id'))
															->joinInner(array('P'=>$this->_ehrTables->empPersonal),'J.Employee_Id = P.Employee_Id AND P.Form_Status = 1',array())
                                                            ->joinLeft(array('R'=>$this->_ehrTables->resignation), "J.Employee_Id = R.Employee_Id AND R.Approval_Status = 'Approved'" ,'Resignation_Date')
                                                            ->where('J.Emp_Status Like ?','Active');
        if($empLeaveCoverage['Gender'] !== 'ALL')
        {
            $getActiveEmployeesDetail->where('P.Gender = ?',$empLeaveCoverage['Gender']);
        } 
        if($empLeaveCoverage['Coverage'] ==='GRA')
        {
            $getActiveEmployeesDetail->joinInner(array('D'=>$this->_ehrTables->designation),'D.Designation_Id = J.Designation_Id',array(''))
                                ->joinInner(array('LG'=>$this->_ehrTables->leavetypegrade),'D.Grade_Id = LG.Grade_Id',array(''))
                                ->where('LG.LeaveType_Id = ?',$empLeaveCoverage['LeaveType_Id']);
        }
        if($empLeaveCoverage['Coverage'] ==='CUSTOMGROUP')
        {
            $customGroupId = $empLeaveCoverage['Custom_Group_Id'];
            $leaveTypeId = $empLeaveCoverage['LeaveType_Id'];
            
            $getActiveEmployeesDetail->joinInner(array('CEGE'=>$this->_ehrTables->customEmployeeGroupEmployees),"CEGE.Group_Id = $customGroupId AND J.Employee_Id = CEGE.Employee_Id",array())
                                    ->joinInner(array('el'=>$this->_ehrTables->empEligbleLeave),"el.Employee_Id = P.Employee_Id AND el.LeaveType_Id= $leaveTypeId",array())
                                    ->where("CEGE.Type IN (?)", array('Default','AdditionalInclusion'));
        }
	    return $this->_db->fetchCol($getActiveEmployeesDetail);
	}
	
	/**
	 * Get join employee details between dates
	 */
	public function getJoinEmpBetweenDates($dates)
	{
		$fiscalWhere = $this->_db->quoteInto('Date_Of_Join BETWEEN ?', date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($dates['finstart'])))).
					   $this->_db->quoteInto(' AND ?', date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($dates['finend']))));
		$resignationWhere = $this->_db->quoteInto('R.Approval_Status = ? OR R.Resignation_Date IS NULL', 'Approved');
		$getJoinEmpBetweenDatesQry = $this->_db->select()
		->from(array('J'=>$this->_ehrTables->empJob),'J.Employee_Id')
		->joinInner(array('P'=>$this->_ehrTables->empPersonal),'J.Employee_Id = P.Employee_Id AND P.Form_Status = 1',array())
		->joinLeft(array('R'=>$this->_ehrTables->resignation), 'J.Employee_Id = R.Employee_Id' ,array())
		->where($fiscalWhere)
		->where($resignationWhere);
		return $this->_db->fetchCol($getJoinEmpBetweenDatesQry);
    }
    
    /* Get all the active employees till the current leave closure year and the 
    current leave closure year inactive employees */
    public function getEmpDetailsTillCurrentLeaveClosure($leaveClosureDates=NULL,$employeeIds=NULL,$leaveTypeDetails=NULL,$listActiveEmployee=NULL)
	{   
		$empDetailsQry = $this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),array('J.Employee_Id','Date_Of_Join'))
                                            ->joinInner(array('P'=>$this->_ehrTables->empPersonal),'J.Employee_Id = P.Employee_Id AND P.Form_Status = 1',array())
                                            ->joinLeft(array('R'=>$this->_ehrTables->resignation), 'J.Employee_Id = R.Employee_Id',array(
                                                'Resignation_Date'=>new Zend_Db_Expr('(CASE WHEN `R`.`Approval_Status` = "Approved" THEN `R`.`Resignation_Date`
                                                                    WHEN ( `J`.`Emp_Status` = "InActive" AND `J`.`Emp_InActive_Date` IS NOT NULL ) THEN `J`.`Emp_InActive_Date` ELSE NULL END)'),
                                            ));                                            
        
        /** If the leave closure date is sent */
        if(!is_null($leaveClosureDates) && !empty($leaveClosureDates) && isset($leaveClosureDates['finend'])){
            $empDetailsQry->where('J.Date_Of_Join <= ?',$leaveClosureDates['finend']);

            if($listActiveEmployee=='Yes')
            {
                $leaveStartDate = $leaveClosureDates['finend'];
            }
            else 
            {
               $leaveStartDate = $leaveClosureDates['finstart'];
            }
            $resignationWhere = $this->_db->quoteInto('J.Emp_Status LIKE ?','Active').
                        ' OR '.$this->_db->quoteInto('J.Emp_InActive_Date >= ?', $leaveStartDate).
                        ' OR '.($this->_db->quoteInto("(R.Approval_Status = 'Approved' AND R.Resignation_Date >= ?)", $leaveStartDate));

            $empDetailsQry->where($resignationWhere);
        }

        $leaveTypeGender = isset($leaveTypeDetails['Gender']) ? $leaveTypeDetails['Gender'] : 'ALL';

        if($leaveTypeGender != 'ALL')
            $empDetailsQry->where('P.Gender = ?',$leaveTypeGender);
        
        /** If the employee ids are sent we do not need to validate leave type coverage and get the respective employee ids.
         * We can validate the given employees fall in the current leave closure or not
         */
        if (!is_null($employeeIds) && !empty($employeeIds))
            $empDetailsQry->where('J.Employee_Id IN (?)',$employeeIds);
        else if (!is_null($leaveTypeDetails) && !empty($leaveTypeDetails))
        {
            /** If the employee ids are not sent then we need to validate the leave type coverage and get the respective emp ids.
             *  For organization coverage we do not need to get the employee ids. For the grade and the custom group coverage we need to 
             * get the employee ids. Based on that we can validate the given employees fall in the current leave closure or not
            */

            $leaveTypeCoverage = isset($leaveTypeDetails['Coverage']) ? $leaveTypeDetails['Coverage'] : '';
            
            /** If the leave type coverage is Grade */
            if($leaveTypeCoverage === 'GRA'){
                $empDetailsQry->joinInner(array('D'=>$this->_ehrTables->designation),'D.Designation_Id = J.Designation_Id',array(''))
                ->joinInner(array('LG'=>$this->_ehrTables->leavetypegrade),'D.Grade_Id = LG.Grade_Id',array(''))
                ->where('LG.LeaveType_Id = ?',$leaveTypeCoverage);
            }else if($leaveTypeCoverage === 'CUSTOMGROUP' && isset($leaveTypeDetails['Custom_Group_Id']) 
            && !empty($leaveTypeDetails['Custom_Group_Id'])){
                 /** If the leave type coverage is Grade get the custom group employees */
                $dbLeave = new  Employees_Model_DbTable_Leave();
                $customGroupEmpIds = getLeaveTypeCustomGroupEmployees($leaveTypeDetails['Custom_Group_Id']);

                if (!is_null($customGroupEmpIds) && !empty($customGroupEmpIds)){
                    $empDetailsQry->where('J.Employee_Id IN (?)',$customGroupEmpIds);
                }
            }
        }

        $empDetailsQry->group('J.Employee_Id');

		return $this->_db->fetchAll($empDetailsQry);
    }
    
    public function __destruct()
    {
        
    }
}

