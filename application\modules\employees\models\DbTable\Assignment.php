<?php
//=========================================================================================
//=========================================================================================
/* Program : Assignment.php											   			         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd, 									 *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MQL Query to retrive, add, update assignments details.				     *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Shobana		          Initial Version        	         *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Ni<PERSON>hitha              Changes in file for mobile app     *
 *                                                    1.Extra fields are added in        *
 *                                                    field list of list query.          */
//=========================================================================================
//=========================================================================================
class Employees_Model_DbTable_Assignment extends Zend_Db_Table_Abstract
{
    protected $_orgDF       = null;
    protected $_db          = null;
    protected $_ehrTables   = null;
	protected $_dbCommonFun = null;
	protected $_orgDetails    = null;

    public function init()
    {
		$this->_dbCommonFun = new Application_Model_DbTable_CommonFunction();
        $this->_ehrTables   = new Application_Model_DbTable_Ehr();
		$this->_db          = Zend_Registry::get('subHrapp');
        $this->_orgDF       = $this->_ehrTables->orgDateformat();
		if (Zend_Registry::isRegistered('orgDetails'))
			$this->_orgDetails = Zend_Registry::get('orgDetails');
	}

    //to list and search tasks
	public function listAssignment($page, $rows, $sortField, $sortOrder, $searchAll=null, $searchArr, $role, $sessionId)
    {
		$taskName            = $searchArr['taskName'];
		$assignorName        = $searchArr['assignorName'];
		$assigneeName        = $searchArr['assigneeName'];
		$projectId           = $searchArr['projectId'];
		$status              = $searchArr['status'];
		$assignmentBeginDate = $searchArr['assignmentBeginDate'];
		$assignmentEndDate   = $searchArr['assignmentEndDate'];
		$serviceProviderId = $searchArr['serviceProviderId'];
		
        switch($sortField)
        {
			case 1: $sortField = 'assign.Task_Name'; break;
			case 2: $sortField = 'proj.Project_Name'; break;
			case 3: $sortField = 'AM.Activity_Name'; break;
			//case 3: $sortField = 'assign.Assignor'; break;
			//case 4: $sortField = 'assign.Creator'; break;
			case 4: $sortField = 'assign.Start_Date'; break;
			case 5: $sortField = 'assign.Due_Date'; break;
			case 6: $sortField = 'assign.Approval_Status'; break;
			default:
				$sortField = 'assign.Task_Id'; $sortOrder = 'desc'; break;
		}
        
		$adminconditions = $this->_db->quoteInto('assign.Assignor = ? or ', $sessionId) .
						   $this->_db->quoteInto('assign.Employee_Id = ? or ', $sessionId).
					       $this->_db->quoteInto('assign.Creator = ? or ', $sessionId) .
					       $this->_db->quoteInto('job.Manager_Id = ? ', $sessionId);
		
        $qryTask = $this->_db->select()->distinct()->from(array('assign'=>$this->_ehrTables->assignment),
									   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS assign.Task_Id as count'),
											 new Zend_Db_Expr("DATE_FORMAT(assign.Start_Date,'".$this->_orgDF['sql']."') as Start_Date"),
											 new Zend_Db_Expr("DATE_FORMAT(assign.Due_Date,'".$this->_orgDF['sql']."') as Due_Date"),
											 new Zend_Db_Expr("DATE_FORMAT(assign.Added_Date,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
											 new Zend_Db_Expr("DATE_FORMAT(assign.Modified_Date,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On"),
											 'assign.Priority','assign.Approval_Status','assign.Task_Name','assign.Task_Id', 'assign.Description',
											 'assign.Activity_Id','assign.Assignor', 'assign.Project_Id','Admin'=>new Zend_Db_Expr("'".$role."'"),
											 'assign.Start_Date as Assign_Start_Date', 'assign.Due_Date as Assign_Due_Date'))
								
								->joinInner(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=assign.Employee_Id',
											array(new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as EmployeeName"),
												  'emp.Employee_Id'))
								
								->joinInner(array('job'=>$this->_ehrTables->empJob),'assign.Employee_Id=job.Employee_Id',array(''))
								
								->joinInner(array('emp1'=>$this->_ehrTables->empPersonal),'emp1.Employee_Id=assign.Assignor',
											array(new Zend_Db_Expr("CONCAT(emp1.Emp_First_Name, ' ', emp1.Emp_Last_Name) as Assignor"),
												  'emp1.Employee_Id as Assignor_Id'))
								
								->joinInner(array('emp2'=>$this->_ehrTables->empPersonal),'emp2.Employee_Id=assign.Creator',
											array(new Zend_Db_Expr("CONCAT(emp2.Emp_First_Name, ' ', emp2.Emp_Last_Name) as Creator"),
												  'emp2.Employee_Id as Creator_Id'))
								
								->joinInner(array('proj'=>$this->_ehrTables->project),'proj.Project_Id=assign.Project_Id',
											array('proj.Project_Name'))
								
								->joinInner(array('act'=>$this->_ehrTables->timesheetActivity),'act.Project_Activity_Id=assign.Activity_Id',
														array(''))

								->joinInner(array('AM'=>$this->_ehrTables->activitiesMaster),'act.Activity_Id = AM.Activity_Id', array('AM.Activity_Name'))
								
								->joinLeft(array('emp4'=>$this->_ehrTables->empPersonal),'emp4.Employee_Id=assign.Added_By',
										   array('Added_By_Name'=>new Zend_Db_Expr("CONCAT(emp4.Emp_First_Name, ' ', emp4.Emp_Last_Name)")))
								
								->joinLeft(array('EB'=>$this->_ehrTables->empPersonal),'EB.Employee_Id=assign.Updated_By',
										   array('Updated_By_Name'=>new Zend_Db_Expr("CONCAT(EB.Emp_First_Name, ' ', EB.Emp_Last_Name)")))
								
								->joinLeft(array('his'=>$this->_ehrTables->auditAssignment),'his.Task_Id=assign.Task_Id',
										   array('his.Task_Id as History'))
								
								->order("$sortField $sortOrder")
								->limit($rows, $page);
		
		if ($role != "admin")
		{
			$qryTask->where($adminconditions);
		}
		
		/**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			$conditions  = $this->_db->quoteInto('assign.Task_Name  Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or proj.Project_Name  Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or AM.Activity_Name Like ?', "%$searchAll%");
			//$conditions .= $this->_db->quoteInto('or emp1.Emp_First_Name Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto(new Zend_Db_Expr('or Concat(emp1.Emp_First_Name," ",emp1.Emp_Last_Name) Like ?'),"%$searchAll%");
            $conditions .= $this->_db->quoteInto('or assign.Approval_Status Like ?', "%$searchAll%");
			
			$qryTask->where($conditions);		
		}
		
		if ( ! empty($taskName) && preg_match('/^[a-zA-Z0-9 ]/', $taskName) )
        {
            $qryTask->where("assign.Task_Name Like ?", "$taskName%");
        }
		
		if ( ! empty($projectId) )
        {
            $qryTask->where('assign.Project_Id = ?', $projectId);
        }
		
		if ( ! empty($assignorName) && preg_match('/^[a-zA-Z ]/', $assignorName) )
        {
//            $qryTask->where("emp1.Emp_First_Name Like ?", "$assignorName%");
//			$qryTask->where("emp1.Emp_Last_Name Like ?", "$assignorName%");
            $qryTask->where(new Zend_Db_Expr('Concat(emp1.Emp_First_Name," ",emp1.Emp_Last_Name) Like ?'),"%$assignorName%");
        }
		
		if ( ! empty($assigneeName) && preg_match('/^[a-zA-Z ]/', $assigneeName) )
        {
//            $qryTask->where("emp.Emp_First_Name Like ?", "$assigneeName%");
//			$qryTask->where("emp.Emp_Last_Name Like ?", "$assigneeName%");
            $qryTask->where(new Zend_Db_Expr('Concat(emp.Emp_First_Name," ",emp.Emp_Last_Name) Like ?'),"%$assigneeName%");
            
        }
		
		/* Filter for assigment start Date */
		if ($assignmentBeginDate != '')
		{
		    $qryTask->where($this->_db->quoteInto('assign.Start_Date >= ?', $assignmentBeginDate));
		}
		
		/* Filter for assigment end Date */
		if ($assignmentEndDate != '')
		{	
			$qryTask->where($this->_db->quoteInto('assign.Due_Date <= ?', $assignmentEndDate));
		}
		
		if ( ! empty($status) && preg_match('/^[a-zA-Z]/', $status) )
        {
            $qryTask->where('assign.Approval_Status Like ?', "$status%");
		}
		
		if(!empty($serviceProviderId)&& $this->_orgDetails['Field_Force']==1)
        {
            $qryTask->where('job.Service_Provider_Id = ?',$serviceProviderId);
        }

		$qryTask = $this->_dbCommonFun->getDivisionDetails($qryTask,'job.Department_Id');
		
		if($role=="admin")
		{
			$qryTask = $this->_dbCommonFun->formServiceProviderQuery($qryTask,'job.Service_Provider_Id',$sessionId);
		}

		/**
		 * SQL queries
		 * Get data to display
		*/
		$assignment = $this->_db->fetchAll($qryTask);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		/* Total data set length */
		$qryAssignmentTotal = $this->_db->select()->from(array('assign'=>$this->_ehrTables->assignment),
														 new Zend_Db_Expr('COUNT(Task_Id)'))
														 
													->joinInner(array('job'=>$this->_ehrTables->empJob),
																'assign.Employee_Id=job.Employee_Id',array(''));
		
		if ($role != "admin")
		{
			$qryAssignmentTotal->where($adminconditions);
		}
		
		$iTotal = $this->_db->fetchOne($qryAssignmentTotal);
		
		/**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $assignment);
	}
	
	/**
	 *	Update transfer details in transfer table and update comment too if exist
    */
	public function updateAssignment($taskArray, $fetchOldTaskValue, $sessionId,$customFormName)
    {
		$qryTask = $this->_db->select()->from($this->_ehrTables->assignment,new Zend_Db_Expr('Count(Project_Id) as taskCnt'))
										->where('Task_Name = ?',$taskArray['Task_Name'])
										->where('Project_Id = ?' , $taskArray['Project_Id'])
										->where('Activity_Id = ?' , $taskArray['Activity_Id'])
										->where('Employee_Id IN (?)' , $taskArray['Employee_Id'])
										->where('Approval_Status IN (?)', array("Assigned", "InProgress", "InReview", "ReOpened"));
										
		if( !empty($taskArray['Task_Id']))
        {
            $qryTask->where('Task_Id != ?',$taskArray['Task_Id']);
        }
		
		$isExist = $this->_db->fetchOne($qryTask);
		
		if($isExist == 0)
        {
			/**
			*	If Task id exist then process update action
		   */
		    if (!empty($taskArray['Task_Id']))
		    {
				$action = 'Edit';
				
				$taskArray['Employee_Id']   = $fetchOldTaskValue['Employee_Id'];
				$taskArray['Modified_Date'] = date('Y-m-d H:i:s');
				$taskArray['Updated_By']    = $sessionId;
				
				$updated = $this->_db->update($this->_ehrTables->assignment, $taskArray, array('Task_Id = '. $taskArray['Task_Id']));
				
				if ($updated && $fetchOldTaskValue != '')
				{
					if ($taskArray['Task_Name'] != $fetchOldTaskValue['Task_Name'] || $taskArray['Employee_Id'] != $fetchOldTaskValue['Employee_Id']
						|| $taskArray['Start_Date'] != $fetchOldTaskValue['Start_Date'] || $taskArray['Due_Date'] != $fetchOldTaskValue['Due_Date']
						|| $taskArray['ProjectName'] != $fetchOldTaskValue['Project_Id'] || $taskArray['Activity'] != $fetchOldTaskValue['Activity_Id']
						|| $taskArray['Status'] != $fetchOldTaskValue['Status']|| $taskArray['Priority'] != $fetchOldTaskValue['Priority'])
					{
						$assHistory = array( 'Task_Name'           => $fetchOldTaskValue['Task_Name'],
											 'Task_Id'             => $taskArray['Task_Id'],
											 'TaskName_ChangedTo'  => $taskArray['Task_Name'],
											 'Assignor'            => $fetchOldTaskValue['Assignor'],
											 'Assignor_ChangedTo'  => $taskArray['Assignor'],
											 'Employee_Id'         => $fetchOldTaskValue['Employee_Id'],
											 'EmployeeId_ChangedTo'=> $taskArray['Employee_Id'],
											 'Start_Date'          => $fetchOldTaskValue['Assign_StartDate'],
											 'StartDate_ChangedTo' => $taskArray['Start_Date'],
											 'Due_Date'            => $fetchOldTaskValue['Assign_DueDate'],
											 'DueDate_ChangedTo'   => $taskArray['Due_Date'],
											 'Project_Id'          => $fetchOldTaskValue['Project_Id'],
											 'ProjectId_ChangedTo' => $taskArray['Project_Id'],
											 'Activity_Id'         => $fetchOldTaskValue['Activity_Id'],
											 'ActivityId_ChangedTo'=> $taskArray['Activity_Id'],
											 'Approval_Status'     => $fetchOldTaskValue['Status'],
											 'Status_ChangedTo'    => $taskArray['Approval_Status'],
											 'Priority'            => $fetchOldTaskValue['Priority'],
											 'Priority_ChangedTo'  => $taskArray['Priority'],
											 'Modified_Date'       => $taskArray['Modified_Date'],
											 'Updated_By'          => $sessionId);
						 
						$this->_db->insert($this->_ehrTables->auditAssignment,$assHistory);
					}
				}
		    }
		    else
		    {
			   /**
				*	If Task id is empty then process add action
			   */
			    $action = 'Add';
			   
			    for($i=0; $i<count($taskArray['Employee_Id']); $i++)
			    {
					$insertionArray[] = $taskArray;
					
					$insertionArray[$i]['Employee_Id']     = $taskArray['Employee_Id'][$i];
					$insertionArray[$i]['Approval_Status'] = "Assigned";
					$insertionArray[$i]['Added_Date']      = date('Y-m-d H:i:s');
					$insertionArray[$i]['Added_By']        = $sessionId;
				}
			   
			   $updated = $this->_ehrTables->insertMultiple($this->_ehrTables->assignment,$insertionArray);
			}
		   
		   /**
			*	this function will handle
			*		update system log function
			*		clear submit lock fucntion
			*		return success/failure array
		   */
		   return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
														   'action'         => $action,
														   'trackingColumn' => $taskArray['Task_Name'],
														   'formName'       => $customFormName,
														   'sessionId'      => $sessionId,
														   'tableName'      => $this->_ehrTables->assignment));
		}
        else
        {
			return array('success' => false, 'msg'=>$customFormName.' Name already exist', 'type'=>'info');
        }
	}
	
	//to delete task
    public function deleteAssignment($taskId, $sessionId,$customFormName)
    {
		$deleted = 0;
		
		$assignmentRow = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->assignment, array('Lock_Flag', 'Task_Name'))
											  ->where('Task_Id = ?', $taskId));
		
		if ($assignmentRow['Lock_Flag'] == 0) 
		{
			$deleted = $this->_db->delete($this->_ehrTables->assignment,'Task_Id='.$taskId);
			
			if ($deleted)
			{
				$this->_db->delete($this->_ehrTables->auditAssignment, 'Task_Id='.$taskId);
			}
		}
		
		/**
		*	delete activity for common function
		*		1)check lock is exist or not.
		*			If lock is exist then show error message like employee is open record for update.
		*		2)If No lockflag then process delete activity
		*		3)Update delete activity in system log
		*		4)return success/failure message
	   */
	   return $this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
													   'lockFlag'       => $assignmentRow['Lock_Flag'],
													   'formName'       => $customFormName,
													   'trackingColumn' => $assignmentRow['Task_Name'],
													   'sessionId'      => $sessionId));
	}
	
	/**
	 * Get assignment history details
	 */
	public function assignmentHistory($taskId)
    {
		$qryAssignmentHistory = $this->_db->select()
									->from(array('ah'=>$this->_ehrTables->auditAssignment),
										   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS ah.Audit_Id as count'),'ah.Audit_Id',
												 'ah.Task_Name', 'ah.Approval_Status', 'ah.Priority',
												 new Zend_Db_Expr("DATE_FORMAT(ah.Start_Date,'".$this->_orgDF['sql']."') as Start_Date"),
												 new Zend_Db_Expr("DATE_FORMAT(ah.Due_Date,'".$this->_orgDF['sql']."') as Due_Date"),
												 new Zend_Db_Expr("DATE_FORMAT(ah.Modified_Date,'".$this->_orgDF['sql']." %T') as Modified_Date")))
									
									->joinInner(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=ah.Employee_Id',
												array(new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as EmployeeName")))
									
									->joinInner(array('emp2'=>$this->_ehrTables->empPersonal),'emp2.Employee_Id=ah.Updated_By',
												array(new Zend_Db_Expr("CONCAT(emp2.Emp_First_Name, ' ', emp2.Emp_Last_Name) as Modified_By")))
									
									->joinInner(array('emp1'=>$this->_ehrTables->empPersonal),'emp1.Employee_Id=ah.Assignor',
												array(new Zend_Db_Expr("CONCAT(emp1.Emp_First_Name, ' ', emp1.Emp_Last_Name) as AssignorName")))
									
									->joinInner(array('proj'=>$this->_ehrTables->project),'proj.Project_Id=ah.Project_Id',
												array('proj.Project_Name'))
									
									->joinInner(array('act'=>$this->_ehrTables->timesheetActivity),'act.Project_Activity_Id=ah.Activity_Id',
														array(''))

									->joinInner(array('AM'=>$this->_ehrTables->activitiesMaster),'act.Activity_Id = AM.Activity_Id', array('AM.Activity_Name'))
									
									->where('ah.Task_Id =?',$taskId);
		/**
		 * SQL queries
		 * Get data to display
		*/
		$assignmentHistory = $this->_db->fetchAll($qryAssignmentHistory);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		/* Total data set length */
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->auditAssignment, new Zend_Db_Expr('COUNT(Audit_Id)'))
									   ->where('Task_Id =?',$taskId));
				
		/**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $assignmentHistory);
	}
    
	//to fetch task record based on taskid
    public function viewTask($taskId)
    {
        return $this->_db->fetchRow($this->_db->select()->from(array('ass'=>$this->_ehrTables->assignment),
															   array(new Zend_Db_Expr("DATE_FORMAT(ass.Start_Date,'".$this->_orgDF['sql']."') as Start_Date"),
																	 new Zend_Db_Expr("DATE_FORMAT(ass.Due_Date,'".$this->_orgDF['sql']."') as Due_Date"),
																	 'ass.Task_Id','ass.Task_Name','ass.Assignor','ass.Creator',
																	 'ass.Employee_Id','ass.Project_Id','ass.Activity_Id','ass.Priority',
																	 'Status'=>'ass.Approval_Status','ass.Description','ass.Updated_By',
																	 'ass.Start_Date as Assign_StartDate', 'ass.Due_Date as Assign_DueDate', 
																	 new Zend_Db_Expr("DATE_FORMAT(ass.Added_Date,'".$this->_orgDF['sql']." at %T') as Added_Date"),
																	 new Zend_Db_Expr("DATE_FORMAT(ass.Modified_Date,'".$this->_orgDF['sql']." at %T') as Modified_Date")))
									
									->joinInner(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=ass.Assignor',
												array(new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as AssignorName")))
									
									->joinInner(array('emp1'=>$this->_ehrTables->empPersonal),'emp1.Employee_Id=ass.Employee_Id',
												array(new Zend_Db_Expr("CONCAT(emp1.Emp_First_Name, ' ', emp1.Emp_Last_Name) as EmployeeName")))
									
									->joinInner(array('emp2'=>$this->_ehrTables->empPersonal),'emp2.Employee_Id=ass.Creator',
												array(new Zend_Db_Expr("CONCAT(emp2.Emp_First_Name, ' ', emp2.Emp_Last_Name) as CreatorName")))
									
									->joinLeft(array('emp3'=>$this->_ehrTables->empPersonal),'emp3.Employee_Id=ass.Updated_By',
												array(new Zend_Db_Expr("CONCAT(emp3.Emp_First_Name, ' ', emp3.Emp_Last_Name) as UpdatedEmp")))
									
									->joinLeft(array('job'=>$this->_ehrTables->empJob),'job.Employee_Id=emp1.Employee_Id',
											   array('job.Manager_Id as Manager_Id'))
									
									->joinInner(array('pro'=>$this->_ehrTables->project),'pro.Project_Id=ass.Project_Id',
												array('pro.Project_Name'))

									->joinInner(array('act'=>$this->_ehrTables->timesheetActivity),'act.Project_Activity_Id=ass.Activity_Id',array(''))

									->joinInner(array('AM'=>$this->_ehrTables->activitiesMaster),'act.Activity_Id = AM.Activity_Id', array('AM.Activity_Name'))
									
									->where('ass.Task_Id =?', $taskId));
    }

	/**
	 *Get assignment details assigned today
	 */
	public function assignedToday($empId)
    {
        $this->_db->setFetchMode(Zend_Db::FETCH_ASSOC);
		
		$qryDashboardTask =  $this->_db->fetchAll($this->_db->select()
									->from($this->_ehrTables->assignment,
										   array('Task_Name', 'Approval_Status',
												 new Zend_Db_Expr("DATE_FORMAT(Due_Date,'".$this->_orgDF['sql']."') as Due_Date")))
									
									->where('Due_Date >= ?', new Zend_Db_Expr('CURDATE()'))
									->where('Employee_Id = ?', $empId)
									->where('Approval_Status IN (?)', array("Assigned", "InProgress", "InReview", "ReOpened")));
									
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');

		$iTotal= $this->_db->fetchAll($this->_db->select()
		->from($this->_ehrTables->assignment,
			   array('Task_Name', 'Approval_Status',
					 new Zend_Db_Expr("DATE_FORMAT(Due_Date,'".$this->_orgDF['sql']."') as Due_Date")))
		
		->where('Due_Date >= ?', new Zend_Db_Expr('CURDATE()'))
		->where('Employee_Id = ?', $empId)
		->where('Approval_Status IN (?)', array("Assigned", "InProgress", "InReview", "ReOpened")));;
						
		if(!empty($empId))
		{
			return array("iTotalRecords" => count($iTotal), "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $qryDashboardTask);
		}
		else
		{
			return 	null;
		}
	}

	public function __destruct()
    {
        
    }

}