<?php
	$customFormNameA = $this->customFormNameA;
	$customFormNameB = $this->customFormNameB;
	$customFormNameC = $this->customFormNameC;
	$customFormNameD = $this->customFormNameD;

	$finalFormName = ((!empty($customFormNameA) && !empty($customFormNameA['New_Form_Name'])) ? $customFormNameA['New_Form_Name'] : $this->formNameA);
	
	$this->headTitle($finalFormName);		
	
	// Employee
	$empUser         = $this->empUser;
	$rolesUser       = $this->rolesUser;
	$empSelfAccess   = $this->empSelfAccess;
	$empSelfUser     = $this->empSelfAccess;
	$departments     = $this->department;
	$designation     = $this->designation;
	$deptParent      = $this->deptParent;
	$empType         = $this->empType;
	$defaultImage    = $this->defaultImage;
	$language        = $this->language;
	$countries       = $this->countries;
	$empLocation     = $this->empLocation;
	$listManager     = $this->listManager;
	$empWorkSchedule = $this->empWorkSchedule;
	$empBankAccType  = $this->empBankAccType;
	$maritalStatus   = $this->maritalStatus;
	$empESICReason   = $this->empESICReason;
	$coursesList     = $this->coursesList;
    $empProfession   = $this->empProfession;
    $forms        	 =  $this->forms;
    $salutationPair  = $this->salutationPair;
	$bloodGroupPair  = $this->bloodGroupPair;
	$serviceProvider = $this->serviceProvider;

    $modules      =  $this->modules;
    $subForms     =  $this->subForms ;
    $getFormId    =  $this->getFormId ;
    $formIdString = implode(", ",array_values($getFormId)); 
    $formIdCount  = count($getFormId);
    $employees    = $this->employeeName;
    $roleDesignation = $this->roleDesignation;
    $designationAccess = $this->designationAccess;
	
	// employee directory
	$empDirectoryAccess = $this->empDirectoryAccess;
	// redeem rewards
	$rewardsAccess = $this->rewardsAccess;
	$isRewardEnabled = $this->isRewardEnabled;

	// Grade
	$empGradeAccess  = $this->gradeAccess;
	$grades          = $this->grades;
	$parentGrade     = $this->parentGrade;
	$parentGradeId   = $parentGrade['Grade_Id'];
	$parentGradeName = $parentGrade['Grade'];
	$deptHierarchy 	 = $this->deptHierarchy;
	$datasetupForms  = $this->datasetupForms;
	$workPlaceList 	 = $this->workPlaceList;
	
	if (Zend_Registry::isRegistered('orgDetails'))
			 $orgDetails = Zend_Registry::get('orgDetails');

	if(empty($empUser['Admin']))
	{
		$disableField = 'disabled';
		if($orgDetails['Employee_Edit']==0)
		{
			// when employee edit is not enabled in organization settings we need to hide the add/edit/delete buttons for following forms
			// Experience, Asset, Education Info, Training, Award, Insurance
			$hideButton = "true";
		}
		else
		{
			// when employee edit is enabled in organization settings we need to show the add/edit/delete buttons for following forms
			// Experience, Asset, Education Info, Training, Award, Insurance
			$hideButton = "false";
		}
	}
	else
	{
		//When the logged in employee is admin we should not hide or disable any fields or forms
		$disableField = '';
		$hideButton = "false";
	}
	// Employee Type
	$empTypeAccess = $this->empTypeAccess;
	$viewEmployeeType = $this->viewEmployeeType;
	
	$department = array();
	
	foreach ($employees as $key => $row) {
		if (!in_array($row['Department_Name'], $department)) {
			array_push($department, $row['Department_Name']);
		}
	}
	$customField = $this->customField;    
	
    $pincode = $caste = $ethnicRace = $aadhaarNo = $pan = $pfNumber= $enableFieldArr = array();
    $pincode['Enable'] = $caste['Enable'] = $ethnicRace['Enable'] = $aadhaarNo['Enable'] = $pan['Enable'] = $pfNumber['Enable'] = 1;
	$enableFieldArr['Pincode'] = $enableFieldArr['Caste'] = $enableFieldArr['EthnicRace'] = $enableFieldArr['AadhaarNo'] = $enableFieldArr['PAN'] = $enableFieldArr['PFNumber'] = 1;
    $pincode['Required'] = 1;
	$caste['Required'] = $ethnicRace['Required'] = $aadhaarNo['Required'] = $pan['Required'] = $pfNumber['Required'] = 0;
    $pincode['Field_Name'] = 'Pincode';
    $caste['Field_Name'] = 'Caste';
    $ethnicRace['Field_Name'] = 'Ethnic Race';
    $aadhaarNo['Field_Name'] = 'Aadhaar Number';
	$pan['Field_Name'] = 'PAN No.';
	$pfNumber['Field_Name'] = 'PF Number';
		
	foreach($customField as $custom)
    {
        if($custom['Field_Name'] == 'Pincode' )
        {
            $pincode['Enable'] = $enableFieldArr['Pincode'] = $custom['Enable'];
            $pincode['Required'] = $custom['Required'];
            $pincode['Field_Name'] = ($custom['New_Field_Name'] != '' ?  $custom['New_Field_Name'] : $custom['Field_Name']);
        }
        
        if($custom['Field_Name'] == 'Caste' )
        {
            $caste['Enable'] = $enableFieldArr['Caste'] = $custom['Enable'];
            $caste['Required'] = $custom['Required'];
            $caste['Field_Name'] = ($custom['New_Field_Name'] != '' ?  $custom['New_Field_Name'] : $custom['Field_Name']);
        }
        
        if($custom['Field_Name'] == 'Ethnic Race' )
        {
            $ethnicRace['Enable'] = $enableFieldArr['EthnicRace'] = $custom['Enable'];
            $ethnicRace['Required'] = $custom['Required'];
            $ethnicRace['Field_Name'] = ($custom['New_Field_Name'] != '' ?  $custom['New_Field_Name'] : $custom['Field_Name']);
        }
        
        if($custom['Field_Name'] == 'Aadhaar Number' )
        {
            $aadhaarNo['Enable'] = $enableFieldArr['AadhaarNo'] = $custom['Enable'];
            $aadhaarNo['Required'] =  $custom['Required'];
            $aadhaarNo['Field_Name'] = ($custom['New_Field_Name'] != '' ?  $custom['New_Field_Name'] : $custom['Field_Name']);
        }
		
		if($custom['Field_Name'] == 'PAN No.' )
        {
            $pan['Enable'] = $enableFieldArr['PAN'] = $custom['Enable'];
            $pan['Required'] = $custom['Required'];
            $pan['Field_Name'] = ($custom['New_Field_Name'] != '' ?  $custom['New_Field_Name'] : $custom['Field_Name']);
        }
		
		if($custom['Field_Name'] == 'PF Number' )
        {
            $pfNumber['Enable'] = $enableFieldArr['PFNumber'] =  $custom['Enable'];
			$pfNumber['Required'] = $custom['Required'];
            $pfNumber['Field_Name'] = ($custom['New_Field_Name'] != '' ?  $custom['New_Field_Name'] : $custom['Field_Name']);
        }
    }
?>
<!-- manager combo we are loading the values dynamically so we need to save the value in hidden field and use it employee.js  -->
<input type="hidden" name="DisableManagerId" id="disableManagerId" value=<?php echo $disableField; ?> />
<input type="hidden" name="empDirectoryAccess" id="empDirectoryAccess" value="<?php echo (int)$empDirectoryAccess['View']; ?> "/>
<input type="hidden" name="rewardsAccess" id="rewardsAccess" value="<?php echo (int)$rewardsAccess['View']; ?> "/>
<input type="hidden" name="designationAccess" id="designationAccess" value="<?php echo $designationAccess['View']; ?> "/>
<input type="hidden" name="employeeAccess" id="employeeAccess" value="<?php echo $empUser['View']; ?> "/>
<input type="hidden" name="empGradeAccess" id="empGradeAccess" value="<?php echo $empGradeAccess['View']; ?> "/>
<input type="hidden" name="empTypeAccess" id="empTypeAccess" value="<?php echo $empTypeAccess['View']; ?> "/>
 <div class="modal fade" id="modalRoleEmployee" aria-hidden="false">
	<div class="modal-dialog modal-full" id="fullModal">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="false">
					<i class="mdi-hardware-keyboard-backspace" id="backEmployeeRole"></i>
				</button>
				<h4 class="modal-title"></h4>
			</div>
			<div>
				
			<form role="form" class="form-horizontal form-validation" id="editFormEmployeeAccessRights" method="POST" action="">
				<input type="hidden" name="formEmployeeId" id="formRoleEmployeeId">
				<input type="hidden" name="formDesignationId" id="formRoleDesignationId">
					
				<input type="hidden" name="FormId" id="formId" value="<?=$formIdString?>">
				<input type="hidden" name="FormId" id="formIdCount" value="<?=$formIdCount?>">
				<div class="checkbox checkbox-secondary text-center">
						<label>	
							Check to select all<input type="checkbox" class="md-checkbox all" id="all">
						</label>  		
				</div>
 
        <div>
		<div class="panel">
			<div class="panel-header">
			  <h3><strong></strong></h3>
			</div>
			<div> 
				<p></p>	
				<div class="nav-tabs3">
					<ul class="nav nav-tabs">
					<?php			
					if(!empty($this->modules) && is_array($this->modules) && count($this->modules)>0)
					{
					    foreach ($this->modules as $module)
					    {
                            $moduleName = trim($module['Module_Name']);
                            if ($moduleName != "Home" && $moduleName != "Contact" && $moduleName != "Training")
                            {
                                if($moduleName=='Organization')
                                {?>
                                    <li class="modules"><a href="#collapse<?=$moduleName?>" data-toggle="tab"><i class="icon hr-organization hrapp-icon-size"></i><?php echo $moduleName; ?></a></li>	
                                <?php }
                                else if($moduleName=='Recruitment')
                                { ?>
                                      <li class="modules"><a href="#collapse<?=$moduleName?>" data-toggle="tab"><i class="icon hr-recuritment hrapp-icon-size"></i><?php echo $moduleName; ?></a></li>
                                <?php	}
								else if($moduleName=='Onboarding')
                                { ?>
                                      <li class="modules"><a href="#collapse<?=$moduleName?>" data-toggle="tab"><i class="icon hr-onboarding hrapp-icon-size"></i><?php echo $moduleName; ?></a></li>
                                <?php	}
                                else if($moduleName=='Employees')
                                { ?>
                                      <li class='active modules'><a href="#collapse<?=$moduleName?>" data-toggle="tab"><i class="icon hr-employee hrapp-icon-size"></i><?php echo $moduleName; ?></a></li>
                                <?php	}
								else if($moduleName=='Core HR')
								{ ?>
									  <li class="modules"><a href="#collapse<?=str_replace(' ','',$moduleName)?>" data-toggle="tab"><i class="icon hr-core-hr hrapp-icon-size"></i><?php echo $moduleName; ?></a></li>
								<?php	}
								else if($moduleName=='Data Loss Prevention')
								{ ?>
									 <li class="modules"><a href="#collapse<?=str_replace(' ','',$moduleName)?>" data-toggle="tab"><i class="icon hr-data-loss-prevention hrapp-icon-size"></i><?php echo $moduleName; ?></a></li>
								<?php	}
								else if($moduleName=='Performance Management')
								{ ?>
									  <li class="modules"><a href="#collapse<?=str_replace(' ','',$moduleName)?>" data-toggle="tab"><i class="icon hr-performance-management hrapp-icon-size"></i><?php echo $moduleName; ?></a></li>
								<?php	}
                                else if($moduleName=='Payroll')
                                { ?>
                                      <li class="modules"><a href="#collapse<?=$moduleName?>" data-toggle="tab"><i class="icon hr-payroll hrapp-icon-size"></i><?php echo $moduleName; ?></a></li>
                                <?php	}
								 else if($moduleName=='Benefits')
								 { ?>
									   <li class="modules"><a href="#collapse<?=$moduleName?>" data-toggle="tab"><i class="icon hr-benefits hrapp-icon-size"></i><?php echo $moduleName; ?></a></li>
								 <?php	}
                                else if($moduleName=='Compliance Management')
                                { ?>
                                      <li class="modules"><a href="#collapse<?=str_replace(' ','',$moduleName)?>" data-toggle="tab"><i class="icon hr-compliance-management hrapp-icon-size"></i><?php echo $moduleName; ?></a></li>
                                <?php	}
                                else if($moduleName=='Integration')
                                { ?>
                                      <li class="modules"><a href="#collapse<?=str_replace(' ','',$moduleName)?>" data-toggle="tab"><i class="icon hr-forms-manager hrapp-icon-size"></i><?php echo $moduleName; ?></a></li>
                                <?php	}
                                else if($moduleName=='Reports')
                                { ?>
                                      <li class="modules"><a href="#collapse<?=$moduleName?>" data-toggle="tab"><i class="icon hr-report hrapp-icon-size"></i><?php echo $moduleName; ?></a></li>
                                <?php	}
								else if($moduleName=='Help')
                                { ?>
                                      <li class="modules"><a href="#collapse<?=$moduleName?>" data-toggle="tab"><i class="icon hr-help hrapp-icon-size"></i><?php echo $moduleName; ?></a></li>
                                <?php	}
								else if($moduleName=='Admin Roles')
                                { ?>
                                      <li class="modules"><a href="#collapse<?=str_replace(' ','',$moduleName)?>" data-toggle="tab"><i class="icon hr-admin-roles hrapp-icon-size"></i><?php echo $moduleName; ?></a></li>
								<?php	}
								else if($moduleName=='Settings')
                                { ?>
                                      <li class="modules"><a href="#collapse<?=str_replace(' ','',$moduleName)?>" data-toggle="tab"><i class="icon hr-settings hrapp-icon-size"></i><?php echo $moduleName; ?></a></li>
								<?php	}
								 else if($moduleName=='Roster Management')
								 { ?>
									   <li class="modules"><a href="#collapse<?=str_replace(' ','',$moduleName)?>" data-toggle="tab"><i class="icon hr-roster-management hrapp-icon-size"></i><?php echo $moduleName; ?></a></li>
								 <?php	}
								 else if($moduleName =='Workflow')
								 {?>
									<li class="modules"><a href="#collapse<?=str_replace(' ','',$moduleName)?>" data-toggle="tab"><i class="icon hr-workflow hrapp-icon-size"></i><?php echo $moduleName; ?></a></li>
							  		<?php

								 }
								// show the Productivity Monitoring module in roles form
								 else if($moduleName === 'Productivity Monitoring')
								 {?>
									<li class="modules"><a href="#collapse<?=str_replace(' ','',$moduleName)?>" data-toggle="tab"><i class="icon hr-productivity-monitoring hrapp-icon-size"></i><?php echo $moduleName; ?></a></li>
							  		<?php

								 }
								 // show the Asset Management module in roles form
								 else if($moduleName === 'Asset Management')
								 {?>
									<li class="modules"><a href="#collapse<?=str_replace(' ','',$moduleName)?>" data-toggle="tab"><i class="icon hr-asset-management hrapp-icon-size"></i><?php echo $moduleName; ?></a></li>
							  		<?php

								 }
								 else if($moduleName === 'Billing')
								 {?>
									<li class="modules"><a href="#collapse<?=str_replace(' ','',$moduleName)?>" data-toggle="tab"><i class="icon hr-billing hrapp-icon-size"></i><?php echo $moduleName; ?></a></li>
							  		<?php

								 }
								 else if($moduleName=='Employee Self Service')
								 { ?>
									   <li class="modules"><a href="#collapse<?=str_replace(' ','',$moduleName)?>" data-toggle="tab"><i class="icon hr-employee-self-service hrapp-icon-size"></i><?php echo $moduleName; ?></a></li>
								 <?php	}
								 else if($moduleName=='My Team')
								 { ?>
									   <li class="modules"><a href="#collapse<?=str_replace(' ','',$moduleName)?>" data-toggle="tab"><i class="icon hr-my-team hrapp-icon-size"></i><?php echo $moduleName; ?></a></li>
								 <?php	}
								 else if($moduleName=='Tax and Statutory Compliance')
								 { ?>
									   <li class="modules"><a href="#collapse<?=str_replace(' ','',$moduleName)?>" data-toggle="tab"><i class="icon hr-payroll hrapp-icon-size"></i><?php echo $moduleName; ?></a></li>
								 <?php	}
                            }
					    }
					}?>      
					</ul>
					<div class="tab-content">
							<?php	
							if(!empty($this->modules) && is_array($this->modules) && count($this->modules)>0)
							{
							    foreach ($this->modules as $module)
							    {
								$moduleName = trim($module['Module_Name']);
								
								if($moduleName == 'Compliance Management')
                                    $moduleName = 'ComplianceManagement';

								if($moduleName == 'Admin Roles')
                                    $moduleName = 'AdminRoles';	
									
								if($moduleName == 'Roster Management')
									$moduleName = 'RosterManagement';

								if($moduleName == 'Performance Management')
									$moduleName = 'PerformanceManagement';

								if($moduleName === 'Data Loss Prevention')
									$moduleName = 'DataLossPrevention';
									
								// change the module name for setting as unique Id in checkbox fields
								if($moduleName === 'Productivity Monitoring')
									$moduleName = 'ProductivityMonitoring';
								if($moduleName === 'Asset Management')
									$moduleName = 'AssetManagement';
								if($moduleName === 'Core HR')
									$moduleName = 'CoreHR';
								if($moduleName === 'Employee Self Service')
									$moduleName = 'EmployeeSelfService';
								if($moduleName === 'My Team')
									$moduleName = 'MyTeam';
								if($moduleName === 'Tax and Statutory Compliance')
									$moduleName = 'TaxandStatutoryCompliance';
								
											
								if ($moduleName != "Home" && $moduleName != "Contact" && $moduleName != "Training")
								{
									if($moduleName=='Employees'){ ?>								
										<div class="tab-pane active" id="collapse<?=$moduleName?>">
									<?php }	else { ?>
										<div class="tab-pane" id="collapse<?=str_replace(' ','',$moduleName)?>">                                        
									<?php }	?>
									
										<p>
										<header class="col-md-12 col-xs-12 paddingCls">
											<label class="col-md-4 col-xs-4 paddingCls checkbox checkbox-secondary">
											</label>
											<div class="col-md-8 col-xs-8 paddingCls">
												
											<?php if ( $moduleName != 'AdminRoles')
											{ ?>
 										        <label class="control-label col-md-2 col-xs-2 paddingCls text-center"><i class="visible-xs visible-sm mdi-action-visibility"></i><span class="hidden-xs hidden-sm">View</span>
										        </label>
												<!--  should not show Delete, Add, Edit roles heading for PerformanceManagement,DataLossPrevention & Help modules-->
												<?php  if ($moduleName != "Help" && $moduleName != "PerformanceManagement" && $moduleName != "DataLossPrevention")
												{ ?>
												    <?php if($moduleName != "AssetManagement") { ?>
														<label class="control-label  col-md-2 col-xs-2 paddingCls text-center"><i class="visible-xs visible-sm mdi-content-add"></i><span class="hidden-xs hidden-sm">Add</span>
														</label>
													<?php  } ?>
													<!--  should not show Delete,Edit roles heading for Integration module-->
													<?php if($moduleName != "Integration")
													{ ?>
														<label class="control-label  col-md-2 col-xs-2 paddingCls text-center"><i class="visible-xs visible-sm mdi-editor-mode-edit"></i><span class="hidden-xs hidden-sm">Update</span>
														</label>
														<!--  should not show Delete roles heading for Benefits modules-->
														<?php if($moduleName!=="Benefits")
														{ ?>
															<label class="control-label  col-md-2 col-xs-2 paddingCls text-center"><i class="visible-xs visible-sm mdi-action-delete" style="padding-right:15px;"></i><span class="hidden-xs hidden-sm">Delete</span>
															</label>

														<?php
														}
													}
												}
											}
											else
											{ ?> 
												<label class="control-label  col-md-2 col-xs-2 paddingCls text-center"><i class="visible-xs visible-sm mdi-editor-mode-edit"></i><span class="hidden-xs hidden-sm">Update</span>
												</label>
											<?php
											}
											
											if ($moduleName == "Employees" || $moduleName == "Payroll" || $moduleName == "TaxandStatutoryCompliance" ||
											$moduleName == "Reports" ||  $moduleName == 'AdminRoles' || $moduleName == 'Organization')
											{ ?>
												<label class="control-label col-md-2 col-xs-2 paddingCls text-center"><i class="visible-xs visible-sm glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm">Opt Choice</span>
												</label>
											  	       
												<?php if($moduleName == "Employees")
												{ ?>
													<label class="control-label col-md-2 col-xs-2 paddingCls text-center"><i class="visible-xs visible-sm fa fa-key"></i><span class="hidden-xs hidden-sm">HR Grp</span>
													</label>
												
												<?php
												}
												
												if($moduleName == "Payroll" || $moduleName == "TaxandStatutoryCompliance" )
												{ ?>
													<label class="control-label col-md-2 col-xs-2 paddingCls text-center"><i class="visible-xs visible-sm fa fa-key"></i><span class="hidden-xs hidden-sm">Payroll Grp</span>
													</label>
										
												<?php
												}
											} else
											{ ?>
											    <label class="control-label col-md-2 col-xs-2 paddingCls text-center"></label>
										     
												<?php
											} ?>
											</div>     
									    </header>
										
										<div class="col-md-12 col-xs-12 paddingCls" style="overflow-y:scroll;max-height:600px;"> 	
										
										<div class="col-md-12 col-xs-12 paddingCls">
									
											<label class="col-md-4 col-xs-4 paddingCls checkbox checkbox-secondary text-center" hidden style="margin-top:-5px;">
											</label>
									
											<div class="col-md-8 col-xs-8 paddingCls">
											<?php if( $moduleName != 'AdminRoles' )
											{ ?>
												<div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
													<label>
														<input type="checkbox" class="md-checkbox checkAll viewAllByTab" id="<?=$moduleName?>-ViewAll">
													</label>
										       </div>												
										       
												<?php  if ($moduleName != "Help" && $moduleName != "PerformanceManagement" && $moduleName != "DataLossPrevention")
												{ ?>
													<?php if($moduleName != "AssetManagement") { ?>
														<div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
															<label>
															<input type="checkbox" class="md-checkbox checkAll addAllByTab" id="<?=$moduleName?>-AddAll">
															</label>
														</div>
													<?php  } ?>
													
													<?php  if($moduleName!="Help" && $moduleName!="Integration" && $moduleName != "PerformanceManagement")
													{ ?>
														<div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
															<label>
																<input type="checkbox"  class="md-checkbox checkAll updateAllByTab" id="<?=$moduleName?>-UpdateAll">
															</label>
														</div>
														<!-- check the module is Productivity monitoring and do not show the delete All option -->
														<?php  if($moduleName!=="Benefits")
														{ ?>
														
															<div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
																<label>
																	<input type="checkbox" class="md-checkbox checkAll deleteAllByTab" id="<?=$moduleName?>-DeleteAll">
																</label>
															</div>

														<?php
														}
														else { ?>
															<label class="control-label  col-md-2 col-xs-2 paddingCls text-center"></label>
													
														<?php  }
														}
													else { ?>
														<label class="control-label  col-md-2 col-xs-2 paddingCls text-center"></label>
														<label class="control-label  col-md-2 col-xs-2 paddingCls text-center"></label>
											       
													<?php  }
												}
											}
											
											if ($moduleName == "Employees" || $moduleName == "Payroll" ||
											$moduleName == "Reports" || $moduleName == "Organization"|| $moduleName == "TaxandStatutoryCompliance")
											{ ?>
												<div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
													<label>
														<input type="checkbox" class="md-checkbox checkAll opAllByTab" id="<?=$moduleName?>-OptionalChoiceAll">
													</label>
												</div>
											  	       
												<?php if($moduleName == "Employees")
												{ ?>													      
													<div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
														<label>
															<input type="checkbox" class="md-checkbox checkAll hrAllByTab" id="<?=$moduleName?>-HrGroupAll">
														</label>
													</div>	
												   
													<?php
												}
												
												if($moduleName == "Payroll" || $moduleName == "TaxandStatutoryCompliance")
												{ ?>
												    <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
														<label>
															<input type="checkbox" class="md-checkbox checkAll payrollAllByTab" id="<?=$moduleName?>-PayrollGroupAll">
														</label>
													</div>		
												   <?php
												}
										    }
										    else
									        { ?>
										        <label class="control-label col-md-2 col-xs-2 paddingCls text-center"></label>
											   
											<?php
											} ?>
											
											</div>
									    </div>
										
										<?php
											$formcnt = 0;
											if(!empty($this->forms) && is_array($this->forms) && count($this->forms)>0)
											{
											    foreach ($this->forms as $form)
											    {
													if($form['Form_Name'] != 'Contact Us' && $form['Form_Name'] != 'Support')
                                                    {
                                                        /** custom form name updation for form name labels in roles**/
                                                        if(array_key_exists('Enable',$form) && !empty($form['New_Form_Name']))
                                                        {                                                        
                                                            $formName = trim($form['Form_Name']);
                                                            $customFormNameLabel = trim($form['New_Form_Name']);
                                                        }
                                                        else
                                                        {
                                                            $formName = $customFormNameLabel = trim($form['Form_Name']);
                                                        }													
                                                        
                                                        $formcnt++;
                                          
                                                        if ($form['Module_Id'] == $module['Module_Id'])
                                                        {
                                                            $formDetailId=$form['Form_Id'];
                                                            
                                                            if($form['Form_Name'] == 'Admin') {
                                                            ?>
                                                            <div class="col-md-12 col-xs-12 paddingCls">
                                                                <label class="col-md-4 col-xs-4 paddingCls checkbox checkbox-secondary"><?=$customFormNameLabel?>
                                                                    <i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
                                                                       data-placement="top" data-content="Have access to all modules in the system except roles and ESOP"
                                                                       data-original-title="Admin">																	
                                                                    </i>
                                                                </label>
                                                            <div class="col-md-8 col-xs-8 paddingCls">
                                                           
                                                            <?php } elseif($form['Form_Name'] == 'Super Admin') { ?>
                                                           
                                                            <div class="col-md-12 col-xs-12 paddingCls">
                                                                <label class="col-md-4 col-xs-4 paddingCls checkbox checkbox-secondary"><?=$customFormNameLabel?>
                                                                    <i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
                                                                       data-placement="top" data-content="Have access to all modules(except ESOP) in the system along with ability to assign roles for others"
                                                                       data-original-title="Super Admin">																	
                                                                    </i>
                                                                </label>
                                                            <div class="col-md-8 col-xs-8 paddingCls">
                                                                
                                                            <?php } elseif($form['Form_Name'] == 'Employee Admin') { ?>
                                                            <div class="col-md-12 col-xs-12 paddingCls">
                                                                <label class="col-md-4 col-xs-4 paddingCls checkbox checkbox-secondary"><?=$customFormNameLabel?>
                                                                    <i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
                                                                       data-placement="top" data-content="Can view other employee details even he/she is not their manager"
                                                                       data-original-title="Employee Admin">																	
                                                                    </i>
                                                                </label>
                                                            <div class="col-md-8 col-xs-8 paddingCls">
                                                           
                                                            <?php } elseif($form['Form_Name'] == 'Payroll Admin') { ?>
                                                            <div class="col-md-12 col-xs-12 paddingCls">
                                                                <label class="col-md-4 col-xs-4 paddingCls checkbox checkbox-secondary"><?=$customFormNameLabel?>
                                                                    <i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
                                                                       data-placement="top" data-content="Can view other employees payroll details even he/she is not their manager"
                                                                       data-original-title="Payroll Admin">																	
                                                                    </i>
                                                                </label>
                                                            <div class="col-md-8 col-xs-8 paddingCls">
                                                            
                                                            <?php }elseif($form['Form_Name'] == 'Roster Admin') { ?>
																<div class="col-md-12 col-xs-12 paddingCls">
																	<label class="col-md-4 col-xs-4 paddingCls checkbox checkbox-secondary"><?=$customFormNameLabel?>
																		<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
																		   data-placement="top" data-content="Can view other employees shift details even he/she is not their manager"
																		   data-original-title="Roster Admin">																	
																		</i>
																	</label>
																<div class="col-md-8 col-xs-8 paddingCls">
															<?php }
															elseif($form['Form_Name'] == 'Benefits Admin') { ?>
                                                            <div class="col-md-12 col-xs-12 paddingCls">
                                                                <label class="col-md-4 col-xs-4 paddingCls checkbox checkbox-secondary"><?=$customFormNameLabel?>
                                                                    <i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
                                                                       data-placement="top" data-content="Can allocate equity to employees"
                                                                       data-original-title="Benefits Admin">																	
                                                                    </i>
                                                                </label>
                                                            <div class="col-md-8 col-xs-8 paddingCls">
                                                            
                                                            <?php }
															elseif($form['Form_Name'] == 'Service Provider Admin') {
																if($orgDetails['Field_Force']==1) { ?>
                                                            <div class="col-md-12 col-xs-12 paddingCls">
                                                                <label class="col-md-4 col-xs-4 paddingCls checkbox checkbox-secondary"><?=$customFormNameLabel?>
                                                                    <i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
                                                                       data-placement="top" data-content="Can view service provider level employee details in the employee and payroll module"
                                                                       data-original-title="Service Provider Admin">																	
                                                                    </i>
                                                                </label>
                                                            <div class="col-md-8 col-xs-8 paddingCls">
                                                            
                                                            <?php } else { ?>
                                                            
                                                            <div class="col-md-12 col-xs-12 paddingCls"> 
                                                                  <label class="col-md-4 col-xs-4 paddingCls checkbox checkbox-secondary"></label>
                                                            <div class="col-md-8 col-xs-8 paddingCls">
                                                          
                                                            <?php }

															} elseif ($form['Form_Name'] == 'Productivity Monitoring Admin'){ ?>
																<div class="col-md-12 col-xs-12 paddingCls">
																	<label class="col-md-4 col-xs-4 paddingCls checkbox checkbox-secondary"><?=$customFormNameLabel?>
																		<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
																		   data-placement="top" data-content="Can view other employees productivity monitoring details"
																		   data-original-title="Productivity Monitoring Admin">																	
																		</i>
																	</label>
																<div class="col-md-8 col-xs-8 paddingCls">
																<?php 
															} else { ?>
                                                            
                                                            <div class="col-md-12 col-xs-12 paddingCls"> 
                                                                  <label class="col-md-4 col-xs-4 paddingCls checkbox checkbox-secondary"><?=$customFormNameLabel?></label>
                                                            <div class="col-md-8 col-xs-8 paddingCls">
                                                          
                                                            <?php }
                                                          
                                                            if($formName != "Tax Rules"  && $formName != "EFT Configuration" && $formName != "Support" && $formName != "Organization Settings" 
															&& $formName != "Performance Evaluation" && $formName != "Data Import" && $formName != "Compliance Forms" && $formName !== "Employee Data Management"
                                                            && $formName != "Super Admin" && $formName != "Admin" && $formName != "Employee Admin" && $formName != "Payroll Admin" 
															&& $formName != "Benefits Admin" && $formName != "Service Provider Admin" && $formName !== "Activity Tracker" && $formName != "Roster Admin"
															&& $formName != "Productivity Monitoring Admin")
                                                            { ?>
                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                    <label>
                                                                        <input type="checkbox" class="md-checkbox view<?=$moduleName?> checkAll view <?=$moduleName?>-ViewAll <?=str_replace(" ","",$formName)?>"
                                                                        id="view-<?=$formDetailId?>" name="view<?=$formDetailId?>">
                                                                    </label>
                                                                </div>
                                                                <?php
                                                            }
                                                            else
                                                            {
                                                                if($moduleName != "AdminRoles")
                                                                { ?>
                                                                    <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                    </label>
                                                                <?php
                                                                }
                                                            }
                                                            
                                                            if ($formName != "Inbox" && $formName != "Salary Payslip" && $formName != "Final Settlement"
                                                            && $formName != "Organization Settings" && $formName != "Performance Evaluation" && $moduleName != "Help" && $moduleName != "PerformanceManagement"
                                                            && $formName != "Tax Rules" && $formName != "EFT Configuration" && $formName != "Perquisite Tracker" && $formName != "Flexi Benefit Declaration"
                                                            && $formName != "HR Reports" && $formName != "Employees Reports" && $formName != "Payroll Reports" && $formName !== 'Core HR' && $formName !== 'Integration'
															&& $formName != "Recruitment Reports" && $formName != "Timeline" && $formName !== 'Data Loss Prevention' && $formName !== 'File Transfers' 
															&& $formName != "Shortlisted Candidates" && $formName != "Data Import" && $formName != "Form Downloads" && $formName !== "Employee Data Management"
                                                            && $formName != "Compliance Forms" && $formName != "Super Admin" && $formName != "Roster Admin" && $formName != "Productivity Monitoring Admin" &&
															$formName != "Admin" && $formName != "Employee Admin" && $formName != "Payroll Admin" && $formName != "Benefits Admin" && $formName != "Service Provider Admin" && $formName != "Calendar View" && $formName != "Trulead"
															&& $formName != "Payout" && $formName != "Payout History" && $formName != "Organization Chart" && $formName != "Approval Management" && $formName != "Dashboard" && $formName != "Interview Calendar"
															&& $formName !='Tax' && $formName !== 'Productivity Monitoring' && $formName !== "Activity Tracker" && $formName !== "Reports"  && $formName != 'Workforce Analytics' && $formName !='Organization' && $formName !== "Activity Dashboard" && $formName !== "Assets" && $formName !== 'Performance Management')
                                                            { ?>
                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                    <label>
                                                                        <input type="checkbox" class="md-checkbox add<?=$moduleName?> checkAll add <?=$moduleName?>-AddAll <?=str_replace(" ","",$formName)?>"
                                                                        id="add-<?=$formDetailId?>" name="add<?=$formDetailId?>">
                                                                    </label>
                                                                </div>			
                                                            <?php
                                                            }
                                                            else
                                                            {
                                                                if ($moduleName != "AdminRoles" && $moduleName !== "AssetManagement")
                                                                {?>
                                                                      <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                      </div>
                                                                <?php
                                                                } 
                                                            } 
                                                      
                                                            if ($formName != "Inbox" && $formName != "Salary Payslip" && $formName != "Support"
                                                            && $formName != "Organization Settings" && $formName != "Performance Evaluation" && $formName != "Help Topics"
                                                            && $formName != "Tax Rules" && $formName != "Shortlisted Candidates" && $formName != "File Transfers"
                                                            && $formName != "HR Reports" && $formName != "Employees Reports" && $formName != "Payroll Reports"
															&& $formName != "Recruitment Reports" && $formName != "Timeline" 
                                                            && $formName != "EFT Configuration" && $formName != "Data Import" && $formName != "Form16" && $formName != "Document Templates" 
                                                            && $formName != "Form Downloads" && $formName != "Compliance Forms" && $formName != "Form12BA"
                                                            && $formName != "Perquisite Tracker" && $formName != "Calendar View" && $formName != "Super Admin" && $formName !== "Employee Data Management"
                                                            && $formName != "Admin" && $formName != "Employee Admin" && $formName != "Service Provider Admin" && $formName != "Roster Admin" && $formName != "Productivity Monitoring Admin"
															&& $formName != "Payroll Admin" && $formName != "Benefits Admin" && $formName != "API Dashboard" && $formName != "OCR" && $formName != "GVP" && $formName != "Trulead" 
															&& $formName != "Payout" && $formName != "Payout History" && $formName != "Organization Chart" && $formName != "Approval Management" && $formName != "Dashboard" && $formName != "Interview Calendar"
															&& $formName !== "Activity Tracker" && $formName !== "Reports" && $formName !== "Activity Dashboard"  && $formName != 'Workforce Analytics')
                                                            { ?>
                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                    <label>	
                                                                      <input type="checkbox" class="md-checkbox update<?=$moduleName?> checkAll update <?=$moduleName?>-UpdateAll <?=str_replace(" ","",$formName)?>"
                                                                      id="update-<?=$formDetailId?>" name="update<?=$formDetailId?>">
                                                                    </label>
                                                                </div>		
                                                            <?php
                                                            }
                                                            /** While 'check to select all' is checked, we should not check, all the admin modules roles **/
                                                            else if (($moduleName == 'AdminRoles')
                                                            && ( $formName == "Admin" || $formName == "Employee Admin" || $formName == "Payroll Admin" || $formName == "Benefits Admin" || $formName == "Roster Admin" || $formName == "Productivity Monitoring Admin" || ($orgDetails['Field_Force']==1 && $formName == "Service Provider Admin")))
                                                            { ?>
                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                    <label>	
                                                                      <input type="checkbox" class="md-checkbox update<?=$moduleName?> <?=$moduleName?>-UpdateAll <?=str_replace(" ","",$formName)?>"
                                                                      id="update-<?=$formDetailId?>" name="update<?=$formDetailId?>">
                                                                    </label>
                                                                </div>
                                                            <?php
                                                            }
                                                            else
                                                            {
                                                             ?>
																<div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
																</div>
                                                            <?php													      
                                                            } 
                                                           
                                                            if ( $formName != "Reimbursement" && $formName != "Final Settlement" &&
                                                            $formName != "Organization Settings" && $formName != "Performance Evaluation" && $formName != "Help Topics"
                                                            && $formName != "Tax Rules" && $formName != "Individuals" &&
                                                            $formName != "Shift Allowance" && $formName != "Support" && $formName != "Shortlisted Candidates"
                                                            && $formName != "HR Reports" && $formName != "Employees Reports" && $formName != "Payroll Reports"
															&& $formName != "Recruitment Reports" && $formName != "Timeline" && $formName != "Core HR" && $formName != "Leave" && $formName !== 'Integration' && $formName !== 'Pre Approvals' && $formName !== 'Special Wages' && $formName !== 'Comp Off'
															&& $formName != "EFT Configuration" && $formName !== 'Data Loss Prevention' && $formName != "File Transfers" && $formName !== 'Recruitment'
                                                            && $formName != "Data Import" && $formName != "Form16"
                                                            && $formName != "Flexi Benefit Declaration" && $formName != "Form Downloads"
                                                            && $formName != "Compliance Forms" && $formName != "Document Templates" && $formName !== "Employee Data Management"
                                                            && $formName != "Form12BA" && $formName != "Perquisite Tracker"   && $formName != "Calendar View" && $formName != "Roster Admin" && $formName != "Productivity Monitoring Admin"
															&& $formName != "Super Admin" && $formName != "Admin" && $formName != "Employee Admin" && $formName != "Payroll Admin" && $formName != "Benefits Admin" 
															&& $formName != "Service Provider Admin" && $formName != "API Dashboard" && $formName != "OCR" && $formName != "GVP" && $formName !='ESOP' && $formName != "Trulead"
															&& $formName != "Payout" && $formName != "Payout History" && $formName != "Organization Chart" && $formName != "Approval Management" && $formName != "Dashboard" && $formName != "Interview Calendar" 
															&& $formName !='Tax' && $formName !== 'Productivity Monitoring' && $formName !== "Activity Tracker" && $formName != 'Workforce Analytics' && $formName !== "Reports" && $formName !='Organization' && $formName !== "Activity Dashboard" && $formName !== 'Performance Management')
                                                            {?>
                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                    <label>
                                                                    <input type="checkbox" class="md-checkbox checkAll delete<?=$moduleName?> <?=$moduleName?>-DeleteAll <?=str_replace(" ","",$formName)?>"
                                                                    id="delete-<?=$formDetailId?>" name="delete<?=$formDetailId?>">
                                                                    </label>
                                                                </div>
                                                            <?php
                                                            }
                                                            else
                                                            {
                                                                if ($moduleName != "AdminRoles")
                                                                { ?>
                                                                      <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                      </div>
                                                                <?php
                                                                }
                                                            }
                                                          
                                                            if ($moduleName == "Employees" || $moduleName == "Payroll" ||
                                                            $moduleName == "Reports" || $moduleName == "Recruitment" || $moduleName == "TaxandStatutoryCompliance")
                                                            {
                                                                if (($formName == "Employee Travel") || $formName == "Inbox" || $formName == "Leaves" || $formName == "Attendance" ||
                                                                $moduleName == "Reports" || 
                                                                ($moduleName == "Payroll" && $formName != "Final Settlement" &&
                                                                $formName != "Commission" && $formName != "Deductions"  && $formName != "Payslip Template" && $formName != "Salary Template" &&
                                                                $formName != "Loan Types" && $formName != "Shift Types" && $formName != "Bonus Types" && $formName != "Allowances" ||
                                                                $formName == "Short Time Off" && $formName == "Adhoc Allowance" ) || 
																($moduleName == "TaxandStatutoryCompliance" && $formName != "Allowance Types"&& $formName != "TDS History" && $formName != "Labour Welfare Fund"
																&& $formName != "Insurance"  && $formName != "Tax Declarations" && $formName != "Fixed Health Insurance"&& $formName != "Perquisite Tracker"  ))
                                                                { ?>
																	
																	
                                                                    <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                        <label>
                                                                            <input type="checkbox" class="md-checkbox optionalChoice<?=$moduleName?> checkAll optional <?=$moduleName?>-OptionalChoiceAll <?=str_replace(" ","",$formName)?>"
                                                                            id="optionalChoice-<?=$formDetailId?>" name="optionalChoice<?=$formDetailId?>">
                                                                        </label>
                                                                    </div>
                                                                 
                                                                    <?php
                                                                }
                                                                else
                                                                { ?>
                                                                    <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                    </div>
                                                                    <?php
                                                                }
                                                              
                                                                if($moduleName == "Employees")
                                                                {
                                                                    if($formName == "Timesheets" || $formName == "Attendance" || $formName == "Leaves" ||
                                                                        $formName == "Transfer" || $formName == "Resignation" || $formName == "Employee Travel" ||
                                                                        $formName == "Awards" || $formName == "Complaints")
                                                                    { ?>
                                                                  
                                                                        <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                            <label>
                                                                                <input type="checkbox" class="md-checkbox HrGroup<?=$moduleName?> checkAll HrGroup <?=$moduleName?>-HrGroupAll <?=str_replace(" ","",$formName)?>"
                                                                                id="hrGroup-<?=$formDetailId?>" name="hrGroup<?=$formDetailId?>">
                                                                            </label>
                                                                        </div> 
                                                                        <?php
                                                                    }
                                                                    else
                                                                    { ?>
                                                                        <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                        </label>
                                                                        <?php
                                                                    } 
                                                                }
                                                              
                                                                if($moduleName == "Payroll" || $moduleName == "TaxandStatutoryCompliance")
                                                                {													       
                                                                    if($formName == "Bonus" || $formName == "Commission" || $formName == "Deductions" || $formName == "Reimbursement" || $formName == "Advance Salary" || $formName == "Loan" ||
                                                                    $formName == "Shift Allowance" || $formName == "Tax Declarations" || $formName == "Salary Payslip" ||
                                                                    $formName == "Flexi Benefit Declaration" || $formName == "Perquisite Tracker")
                                                                    { ?>
                                                                        <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                            <label>
                                                                                <input type="checkbox" class="md-checkbox PayrollGroup<?=$moduleName?> checkAll PayrollGroup <?=$moduleName?>-PayrollGroupAll <?=str_replace(" ","",$formName)?>"
                                                                                id="payrollGroup-<?=$formDetailId?>" name="payrollGroup<?=$formDetailId?>">
                                                                            </label>
                                                                        </div>
                                                                        <?php
                                                                    }
                                                                    else
                                                                    { ?>
                                                                        <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                        </label>
                                                                        <?php
                                                                    }														  
                                                                }
                                                            }
                                                            /** While 'check to select all' is checked, we should not check, all the admin modules roles **/
                                                            else if($moduleName == "AdminRoles")
                                                            {
                                                                if( $formName == "Super Admin" ) { ?>
                                                                    <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                        <label>
                                                                            <input type="checkbox" class="md-checkbox optionalChoice<?=$moduleName?> <?=$moduleName?>-OptionalChoiceAll <?=str_replace(" ","",$formName)?>"
                                                                            id="optionalChoice-<?=$formDetailId?>" name="optionalChoice<?=$formDetailId?>">
                                                                        </label>
                                                                    </div>															
                                                                <?php
                                                                }
                                                            }
                                                            else
                                                            { ?>
                                                                <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                </label>
                                                          
                                                                <?php
                                                            } ?>
                                                           
                                                            </div> 
                                                            
															<?php
															
                                                            if (in_array($formName, array("Leaves", "Performance Evaluation", "Awards", "Loan" ,"Tax Rules", "Shift Allowance","Commission","Salary Payslip",
                                                               "Insurance", "Reimbursement", 'Attendance', "Organization Settings", "Organization Policies","Announcements","Salary", "Employee Data Management",
                                                                "Data Import", "Allowances", "Tax Declarations","Compliance Forms","Employees Document Upload", "Shift Scheduling",'Core HR', "Integration",
                                                                "Bonus", "Timesheets", "Deductions", "Payslip Template","Salary Template","Employees", "Miscellaneous", "Provident Fund","EFT Configuration",
                                                                "NPS","Short Time Off","Compensatory Off","TDS History","Fixed Health Insurance","Labour Welfare Fund","Individuals","Payout History",'Activity Tracker')))
                                                            {
                                                                if(!empty($this->subForms) && is_array($this->subForms) && count($this->subForms)>0)
                                                                {													
                                                                    foreach($this->subForms as $subformrec)
                                                                    {
                                                                        /** custom sub form name updation for sub form name labels in roles**/
                                                                        if(array_key_exists('Enable',$subformrec) && !empty($subformrec['New_Form_Name']))
                                                                        {
                                                                            $customSubFormName = trim($subformrec['New_Form_Name']);
                                                                        }
                                                                        else
                                                                        {
                                                                            $customSubFormName = trim($subformrec['Form_Name']);
                                                                        }
																		$hiddenForms = array("Work from home (pre-approval)", "Work during week off (pre-approval)", "Work during holiday (pre-approval)", "On Duty Settings", "Salary Details", "Fixed Insurance", "Variable Insurance");
                                                                        if ($form['Form_Id'] == $subformrec['Sub_Form'] && !in_array($subformrec['Form_Name'], $hiddenForms))
                                                                        { ?>
                                                                            <div class="col-md-12 col-xs-12 paddingCls">  		  
                                                                  
                                                                            <div class="col-md-4 col-xs-4 paddingCls checkbox checkbox-secondary">
                                                                               +<?=$customSubFormName?>
                                                                            </div>
                                                                  
                                                                            <div class="col-md-8 col-xs-8 paddingCls"> 
                                                                  
                                                                            <?php 
                                                                            if ($subformrec['Form_Name'] == "Employee")
                                                                            { ?>
                                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                    <label>
                                                                                        <input type="checkbox" class="md-checkbox view<?=$moduleName?> checkAll view <?=$moduleName?>-ViewAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                        id="view-<?=$subformrec['Form_Id']?>" name="view<?=$subformrec['Form_Id']?>">
                                                                                    </label>
                                                                                </div>
                                                                      
                                                                                <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
                                                                                
                                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                    <label>																		
                                                                                        <input type="checkbox" class="md-checkbox update<?=$moduleName?> checkAll update <?=$moduleName?>-UpdateAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                        id="update-<?=$subformrec['Form_Id']?>" name="update<?=$subformrec['Form_Id']?>">
                                                                                    </label>
                                                                                </div>
                                                                      
                                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                    <label>
                                                                                        <input type="checkbox" class="md-checkbox delete<?=$moduleName?> checkAll delete <?=$moduleName?>-DeleteAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                        id="delete-<?=$subformrec['Form_Id']?>" name="delete<?=$subformrec['Form_Id']?>">
                                                                                    </label>
                                                                                </div>
                                                                                
                                                                                <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
                                                                                <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
                                                                    
                                                                                <?php
                                                                            }
                                                                            
                                                                            if ($subformrec['Form_Name'] == "Mailbox" || $subformrec['Form_Name'] == "Data Setup Dashboard" || $subformrec['Form_Name'] == "View Transactions")
                                                                            { ?>
                                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                    <label>
                                                                                        <input type="checkbox" class="md-checkbox view<?=$moduleName?> checkAll view <?=$moduleName?>-ViewAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                        id="view-<?=$subformrec['Form_Id']?>" name="view<?=$subformrec['Form_Id']?>">
                                                                                    </label>
                                                                                </div>
                                                                                <?php
                                                                            }
                                                                            
                                                                            if ($subformrec['Form_Name'] == "Roles" || $subformrec['Form_Name'] == "Leave Encashment" || $subformrec['Form_Name'] == "Attendance Box" || $subformrec['Form_Name'] == "Dashboard Attendance" || $subformrec['Form_Name'] === "Attendance Finalization")
                                                                            {
                                                                                if($subformrec['Form_Name'] == "Roles")
                                                                                { ?>															  
                                                                                    <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
                                                                                    <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
                                                                       
                                                                                    <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                        <label>
                                                                                            <input type="checkbox" class="md-checkbox update<?=$moduleName?> checkAll update <?=$moduleName?>-UpdateAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                            id="update-<?=$subformrec['Form_Id']?>" name="update<?=$subformrec['Form_Id']?>">
                                                                                        </label>
                                                                                    </div>
                                                                                    
                                                                                    <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label> 
                                                                                    
                                                                                    <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                        <label>
                                                                                            <input type="checkbox" class="md-checkbox optionalChoice<?=$moduleName?> checkAll optional <?=$moduleName?>-OptionalChoiceAll <?=str_replace(" ","",$formName)?>"
                                                                                            id="optionalChoice-<?=$subformrec['Form_Id']?>" name="optionalChoice<?=$subformrec['Form_Id']?>">
                                                                                        </label>
                                                                                    </div>
                                                                                    <?php
																				}
																				
																				if($subformrec['Form_Name'] == "Dashboard Attendance")
                                                                                { ?>															  
                                                                                    <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
                                                                                    <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
                                                                                    <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
                                                                                    <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label> 
                                                                                    
                                                                                    <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                        <label>
                                                                                            <input type="checkbox" class="md-checkbox optionalChoice<?=$moduleName?> checkAll optional <?=$moduleName?>-OptionalChoiceAll <?=str_replace(" ","",$formName)?>"
                                                                                            id="optionalChoice-<?=$subformrec['Form_Id']?>" name="optionalChoice<?=$subformrec['Form_Id']?>">
                                                                                        </label>
                                                                                    </div>
                                                                                    <?php
																				}

                                                                                
                                                                               if($subformrec['Form_Name'] == "Leave Encashment")
                                                                               { ?>
                                                                                   
																					<div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                    	<label>
                                                                                        	<input type="checkbox" class="md-checkbox view<?=$moduleName?> checkAll view <?=$moduleName?>-ViewAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                        	id="view-<?=$subformrec['Form_Id']?>" name="view<?=$subformrec['Form_Id']?>">
                                                                                    	</label>
                                                                                    </div>
                                                                                    <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                        <label>
                                                                                            <input type="checkbox" class="md-checkbox add<?=$moduleName?> checkAll add <?=$moduleName?>-AddAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                            id="add-<?=$subformrec['Form_Id']?>" name="add<?=$subformrec['Form_Id']?>">
                                                                                        </label>
																					</div> 
																					
																					
                                                                                    <?php
                                                                                }
																				
																				if($subformrec['Form_Name'] == "Attendance Box")
                                                                                { ?>
                                                                                    <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
                                                                                    <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
                                                                       
                                                                                    <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                        <label>
                                                                                            <input type="checkbox" class="md-checkbox update<?=$moduleName?> checkAll update <?=$moduleName?>-UpdateAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                            id="update-<?=$subformrec['Form_Id']?>" name="update<?=$subformrec['Form_Id']?>">
                                                                                        </label>
                                                                                    </div>
																					
                                                                                    <?php
																				}
																				
																				
																				if($subformrec['Form_Name'] == "Attendance Finalization")
                                                                                { ?>
                                                                                    
                                                                                    <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                    	<label>
                                                                                        	<input type="checkbox" class="md-checkbox view<?=$moduleName?> checkAll view <?=$moduleName?>-ViewAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                        	id="view-<?=$subformrec['Form_Id']?>" name="view<?=$subformrec['Form_Id']?>">
                                                                                    	</label>
                                                                                    </div>
																					<label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
																					<label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
																					<label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
																					<div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
																						<label>
																							<input type="checkbox" class="md-checkbox optionalChoice<?=$moduleName?> checkAll optional <?=$moduleName?>-OptionalChoiceAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
																							id="optionalChoice-<?=$subformrec['Form_Id']?>" name="optionalChoice<?=$subformrec['Form_Id']?>">
																						</label>
                                                                                	</div>

																					
                                                                                    <?php
																				}
                                                                            }
                                                                  
                                                                            if (in_array($subformrec['Form_Name'], array("Mail Client Configuration","Insurance Payment Tracker",'PT Payment Tracker',
                                                                            'PF Payment Tracker','Provident Fund Rules','TDS Payment Tracker','Tax Configuration','Loan Settings',
                                                                            'Gratuity Settings','Gratuity','Form16 Import', 'Short Time Off (Permission)', 'Short Time Off (On Duty)',
                                                                            'NPS Payment Tracker','Permission Settings','LWF Payment Tracker','Attendance Configuration','Roster')))
                                                                            { ?>
                                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                    <label>
                                                                                        <input type="checkbox" class="md-checkbox view<?=$moduleName?> checkAll view <?=$moduleName?>-ViewAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                        id="view-<?=$subformrec['Form_Id']?>" name="view<?=$subformrec['Form_Id']?>">
                                                                                    </label>
                                                                                </div>
                                                                      
                                                                                <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
                                                                      
                                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                    <label>
                                                                                        <input type="checkbox" class="md-checkbox update<?=$moduleName?> checkAll update <?=$moduleName?>-UpdateAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                        id="update-<?=$subformrec['Form_Id']?>" name="update<?=$subformrec['Form_Id']?>">
                                                                                    </label>
                                                                                </div>
                                                                  
                                                                                <?php
                                                                            }
                                                                            
                                                                            if (in_array($subformrec['Form_Name'], array('Tax Declaration Import','HRA Declaration Import','Candidates')))
                                                                            { ?>
                                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                    <label>
                                                                                        <input type="checkbox" class="md-checkbox view<?=$moduleName?> checkAll view <?=$moduleName?>-ViewAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                        id="view-<?=$subformrec['Form_Id']?>" name="view<?=$subformrec['Form_Id']?>">
                                                                                    </label>
                                                                                </div>
                                                                            
                                                                                <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
                                                                                
                                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                    <label>
                                                                                        <input type="checkbox" class="md-checkbox update<?=$moduleName?> checkAll update <?=$moduleName?>-UpdateAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                        id="update-<?=$subformrec['Form_Id']?>" name="update<?=$subformrec['Form_Id']?>">
                                                                                    </label>
                                                                                </div>
                                                                            
                                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                   <label>
                                                                                       <input type="checkbox" class="md-checkbox delete<?=$moduleName?> checkAll delete <?=$moduleName?>-DeleteAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                       id="delete-<?=$subformrec['Form_Id']?>" name="delete<?=$subformrec['Form_Id']?>">
                                                                                   </label>
                                                                                </div>
                                                                                <?php
                                                                            }
                                                                            
                                                                            if($subformrec['Form_Name'] == 'Attendance Import')
                                                                            { ?>
                                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                    <label>
                                                                                        <input type="checkbox" class="md-checkbox view<?=$moduleName?> checkAll view <?=$moduleName?>-ViewAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                        id="view-<?=$subformrec['Form_Id']?>" name="view<?=$subformrec['Form_Id']?>">
                                                                                    </label>
                                                                                </div>
                                                                      
                                                                                <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
                                                                                
                                                                                <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
                                                                                
                                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                    <label>
                                                                                        <input type="checkbox" class="md-checkbox delete<?=$moduleName?> checkAll delete <?=$moduleName?>-DeleteAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                        id="delete-<?=$subformrec['Form_Id']?>" name="delete<?=$subformrec['Form_Id']?>">
                                                                                    </label>
                                                                                </div>
                                                                                
                                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                    <label>
                                                                                        <input type="checkbox" class="md-checkbox optionalChoice<?=$moduleName?> checkAll optional <?=$moduleName?>-OptionalChoiceAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                        id="optionalChoice-<?=$subformrec['Form_Id']?>" name="optionalChoice<?=$subformrec['Form_Id']?>">
                                                                                    </label>
                                                                                </div>
                                                                  
                                                                                <?php
                                                                            } 
																			
																			
                                                                            if( $subformrec['Form_Name'] == 'Deferred Loan' || $subformrec['Form_Name'] =='Alert Settings')
                                                                            { ?>
                                                                                                  
                                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                    <label>
                                                                                        <input type="checkbox" class="md-checkbox view<?=$moduleName?> checkAll view <?=$moduleName?>-ViewAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                        id="view-<?=$subformrec['Form_Id']?>" name="view<?=$subformrec['Form_Id']?>">
                                                                                    </label>
                                                                                </div>
                                                                                
                                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                    <label>
                                                                                        <input type="checkbox" class="md-checkbox add<?=$moduleName?> checkAll add <?=$moduleName?>-AddAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                        id="add-<?=$subformrec['Form_Id']?>" name="add<?=$subformrec['Form_Id']?>">
                                                                                    </label>
                                                                                </div>
                                                                                
                                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                    <label>
                                                                                        <input type="checkbox" class="md-checkbox update<?=$moduleName?> checkAll update <?=$moduleName?>-UpdateAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                        id="update-<?=$subformrec['Form_Id']?>" name="update<?=$subformrec['Form_Id']?>">
                                                                                    </label>
                                                                                </div>
                                                                                
                                                                                <?php
                                                                            }
                                                              
																			if($subformrec['Form_Name'] == 'Compensatory Off Balance' || $subformrec['Form_Name'] == 'Tax Entities' 
																			|| $subformrec['Form_Name'] == 'Tax Slabs'  || $subformrec['Form_Name'] == 'Tax Sections' 
																			|| $subformrec['Form_Name'] == 'Section Investments' || $subformrec['Form_Name'] == 'Tax Exemptions' || $subformrec['Form_Name'] == 'Tax Rebates')
                                                                            { ?>														      
                                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                    <label>
                                                                                        <input type="checkbox" class="md-checkbox view<?=$moduleName?> checkAll view <?=$moduleName?>-ViewAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                        id="view-<?=$subformrec['Form_Id']?>" name="view<?=$subformrec['Form_Id']?>">
                                                                                    </label>
                                                                                </div>
                                                                            
                                                                                <?php
                                                                            }
                                                                    
                                                                            if($moduleName == 'TaxandStatutoryCompliance' && $formName == 'Compliance Forms')
                                                                            { ?>																	
                                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                    <label>
                                                                                        <input type="checkbox" class="md-checkbox view<?=$moduleName?> checkAll view <?=$moduleName?>-ViewAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                        id="view-<?=$subformrec['Form_Id']?>" name="view<?=$subformrec['Form_Id']?>">
                                                                                    </label>
                                                                                </div>
                                                                       
                                                                                <?php if(in_array($subformrec['Form_Name'],
                                                                                      array('Document Template Engine','Generate Letter')))
                                                                                { ?> 
                                                                                    <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                        <label>
                                                                                            <input type="checkbox" class="md-checkbox add<?=$moduleName?> checkAll add <?=$moduleName?>-ViewAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                            id="add-<?=$subformrec['Form_Id']?>" name="add<?=$subformrec['Form_Id']?>">
                                                                                        </label>
                                                                                    </div>
                                                                        
                                                                                    <?php
                                                                                } else
                                                                                { ?>                                                                        
                                                                                    <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
                                                                        
                                                                                    <?php
                                                                                }
                                                                                
                                                                                if(!in_array($subformrec['Form_Name'],
                                                                                array('Form16','Form12BA','Form24Q')))
                                                                                { ?>                                                                     
                                                                                    <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                        <label>
                                                                                            <input type="checkbox" class="md-checkbox update<?=$moduleName?> checkAll update <?=$moduleName?>-ViewAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                            id="update-<?=$subformrec['Form_Id']?>" name="update<?=$subformrec['Form_Id']?>">
                                                                                        </label>
                                                                                    </div>
                                                                                    <?php
                                                                                } else
                                                                                { ?>                                                                        
                                                                                    <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
                                                                        
                                                                                    <?php
                                                                                }
                                                                                
                                                                                if(!in_array($subformrec['Form_Name'],
                                                                                array('Form16','Form12BA','Form24Q'))){ ?> 
                                                                                    <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                        <label>
                                                                                            <input type="checkbox" class="md-checkbox delete<?=$moduleName?> checkAll delete <?=$moduleName?>-ViewAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                            id="delete-<?=$subformrec['Form_Id']?>" name="delete<?=$subformrec['Form_Id']?>">
                                                                                        </label>
                                                                                    </div>
                                                                        
                                                                                    <?php
                                                                                } else
                                                                                { ?>                                                                        
                                                                                    <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
                                                                        
                                                                                    <?php
                                                                                }
                                                                                
                                                                                if($subformrec['Form_Name'] == 'Form16')
                                                                                { ?>
                                                                                    <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                        <label>
                                                                                            <input type="checkbox" class="md-checkbox optionalChoice<?=$moduleName?> checkAll optional <?=$moduleName?>-OptionalChoiceAll <?=str_replace(" ","",$formName)?> <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                            id="optionalChoice-<?=$subformrec['Form_Id']?>" name="optionalChoice<?=$subformrec['Form_Id']?>">
                                                                                        </label>
                                                                                    </div>	
                                                                                    
                                                                                    <?php
                                                                                } else
                                                                                { ?>
                                                                                    <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
                                                                        
                                                                                    <?php
                                                                                } ?>
                                                                                
                                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                    <label>
                                                                                        <input type="checkbox" class="md-checkbox PayrollGroup<?=$moduleName?> checkAll PayrollGroup <?=$moduleName?>-PayrollGroupAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                        id="payrollGroup-<?=$subformrec['Form_Id']?>" name="payrollGroup<?=$subformrec['Form_Id']?>">
                                                                                    </label>
                                                                                </div>	
                                                                        
                                                                                <?php
																			}

                                                                            if(in_array($subformrec['Form_Name'], array('Holiday Assign','Holidays','Holiday Types','Policy Types','Organization Details','Contact Details',
                                                                           'Organization Account','Grades','Designations','Employee Type','Timesheet Hours','Employee Directory','Redeem Rewards','Employee Details', 'Attendance Regularization',
                                                                            'Timesheet Activities','Leave Types','Leave Freeze','Leave Enforcement Configuration','Employee Bank Account','Award Types','Skill Definition','Skill Level Association',
                                                                            'Performance Assessment','Professional Tax','Bonus Types','Commission Percentage','Commission Types', "Allowance Import", "Adhoc Allowance Import","Work Schedule", 'User Accounts', 'Full & Final Settlement',
                                                                            'Expense Types','Loan Types','Shift Types','Insurance Types','Premium Contribution', 'Attendance Settings', 'Device Management','Workschedule Weekoff', "Allowance Types", 'Projects', 'Designations/Positions', 'Custom Employee Groups', 'Business Unit / Cost Center',
                                                                            'Tax Section Allowance Mapping','Leave Import', 'Deduction Import', 'Leave Balance Import', 'Salary Import','Dependent Import','HRA Declarations', 'Proof Of Investment', 'Leave', 'Geo-Fencing & Selfie Attendance', 'Pre Approvals','Special Wages','Comp Off', 'Recruitment',
                                                                            'Employee Import','Adhoc Allowance','Fixed Health Insurance Types','Multi Company Setup','Document Sub Type' ,'Income Under Section24', 'My Activity','My Team Activity','Goals And Achievement','LOP Recovery')))
                                                                            { ?>
                                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                    <label>
                                                                                        <input type="checkbox" class="md-checkbox view<?=$moduleName?> checkAll view <?=$moduleName?>-ViewAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                        id="view-<?=$subformrec['Form_Id']?>" name="view<?=$subformrec['Form_Id']?>">
                                                                                    </label>
                                                                                </div>
																				<?php
																				if($subformrec['Form_Name'] != 'Employee Directory' && $subformrec['Form_Name'] != 'Redeem Rewards' && $subformrec['Form_Name'] !== 'My Activity' && $subformrec['Form_Name'] !== 'My Team Activity')
																				{ ?>
																					<?php
																					if($subformrec['Form_Name'] == 'Leave')
																					{ ?>
																						<label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
																					<?php } else { ?>
                                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                    <label>
                                                                                        <input type="checkbox" class="md-checkbox add<?=$moduleName?> checkAll add <?=$moduleName?>-AddAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                        id="add-<?=$subformrec['Form_Id']?>" name="add<?=$subformrec['Form_Id']?>">
                                                                                    </label>
                                                                                </div>
																				<?php } ?>
																				<?php
																				if($subformrec['Form_Name'] == 'Multi Company Setup' || $subformrec['Form_Name'] === 'My Activity' || $subformrec['Form_Name'] === 'My Team Activity')
																				{ ?>
																					<label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
																				<?php } else { ?>
																					<div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
																					<label>
																						<input type="checkbox" class="md-checkbox update<?=$moduleName?> checkAll update <?=$moduleName?>-UpdateAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
																						id="update-<?=$subformrec['Form_Id']?>" name="update<?=$subformrec['Form_Id']?>">
																					</label>
																				</div>
																				<?php } ?>
																				<?php
																				if($subformrec['Form_Name'] === 'My Activity' || $subformrec['Form_Name'] === 'My Team Activity' || $subformrec['Form_Name'] == 'Leave' ||
																					$subformrec['Form_Name'] === 'Employee Details' || $subformrec['Form_Name'] === 'Attendance Regularization' || $subformrec['Form_Name'] == 'Pre Approvals'|| $subformrec['Form_Name'] == 'Special Wages'|| $subformrec['Form_Name'] == 'Comp Off' || $subformrec['Form_Name'] == 'Recruitment' || $subformrec['Form_Name'] == 'Business Unit / Cost Center' || $subformrec['Form_Name'] == 'LOP Recovery')
																				{ ?>
																					<label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
																				<?php } else { ?>
																					<div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
																					<label>
																						<input type="checkbox" class="md-checkbox delete<?=$moduleName?> checkAll delete <?=$moduleName?>-DeleteAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
																						id="delete-<?=$subformrec['Form_Id']?>" name="delete<?=$subformrec['Form_Id']?>">
																					</label>
																				</div>
																				<?php } ?>
																				
                                                                      
																				
																				<?php 
																				if($subformrec['Form_Name'] == "Full & Final Settlement" || $subformrec['Form_Name'] === 'Income Under Section24' || $subformrec['Form_Name'] === 'Proof Of Investment')
																				{ ?>
                                                                                <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                    <label>
                                                                                        <input type="checkbox" class="md-checkbox optionalChoice<?=$moduleName?> checkAll optional <?=$moduleName?>-OptionalChoiceAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                        id="optionalChoice-<?=$subformrec['Form_Id']?>" name="optionalChoice<?=$subformrec['Form_Id']?>">
                                                                                    </label>
																				</div>
																				<?php } ?>
																				<?php } ?>
																				<?php
                                                                                
																				/** While 'check to select all' is checked, we should not check, all the admin modules roles **/
																				if($moduleName == "Organization")
																				{ 
																					if( $subformrec['Form_Name'] == "Organization Account" ) { ?>
																						<div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
																							<label>
																								<input type="checkbox" class="md-checkbox optionalChoice<?=$moduleName?> checkAll optional <?=$moduleName?>-OptionalChoiceAll <?=str_replace(" ","",$formName)?>"
																								id="optionalChoice-<?=$subformrec['Form_Id']?>" name="optionalChoice<?=$subformrec['Form_Id']?>">
																							</label>
																						</div>															
																					<?php
																					}
																				} ?>


                                                                                <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
                                                                      
                                                                                <?php if($subformrec['Form_Name'] == 'HRA Declarations')
                                                                                { ?>
                                                                                    <div class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center">
                                                                                        <label>
                                                                                            <input type="checkbox" class="md-checkbox PayrollGroup<?=$moduleName?> checkAll PayrollGroup <?=$moduleName?>-PayrollGroupAll <?=str_replace(" ","",$subformrec['Form_Name'])?>"
                                                                                            id="payrollGroup-<?=$subformrec['Form_Id']?>" name="payrollGroup<?=$subformrec['Form_Id']?>">
                                                                                        </label>
                                                                                    </div>	
                                                                                    
                                                                                    <?php
                                                                                } else
                                                                                { ?>
                                                                                    <label class="col-md-2 col-xs-2 paddingCls checkbox checkbox-secondary text-center"></label>
                                                                                    
                                                                                    <?php
                                                                                }
                                                                            }
                                                             
                                                                  
                                                                            if($moduleName == 'Employees')
                                                                            { ?>
                                                                       
                                                                                <?php
                                                                            }
                                                                  
                                                                            if($moduleName == 'Payroll' || $moduleName == "TaxandStatutoryCompliance")
                                                                            { ?>
                                                                      
                                                                                <?php
                                                                            }
                                                                            
                                                                            if($moduleName == 'Payroll' || $moduleName == "TaxandStatutoryCompliance")
                                                                            {
                                                                              
                                                                            } ?>
                                                                            </div>
                                                                            </div>
                                                                        
                                                                            <?php
                                                                        }
                                                                    }
                                                                }
                                                            } ?>
                                                            </div>	
                                                            
                                                            <?php
                                                        }
                                                    }
                                                }
											} ?>
										</div>		    
										</p>
										</div>
									<?php
								}
							    }
							} ?>	
					</div>
				</div>
			</div>
		</div>
	</div>

		<div class="col-md-12 col-xs-12 paddingCls panel-group panel-accordion" id="accordion1">
					<div class="panel panel-default">
					  <div class="panel-heading">
					    <h4>
					      <a class="collapsed" data-toggle="collapse" data-parent="#accordion1" href="#copyRoles">
					      Copy Roles
					      </a>
					    </h4>
					  </div>
					  <div id="copyRoles" class="panel-collapse collapse">
					    <div class="panel-body">
					<div class="row">
						
						
						<div class="form-group col-md-12">
							<label class="col-md-3 control-label m-b-10">Designation</label>
							<div class="col-md-4 m-b-10">
								 <select class="form-control" data-search="true" id="copyRolesDesignation" >
									   <option value="">All</option>
									   <?php
									   foreach ($roleDesignation as $key => $row)
									   {
										   echo '<option value="'.$key.'">'.$row.'</option>';
									   }
									   ?>
								 </select>
							</div>
							<div class="col-md-5">
								<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="CopyFromDesignation">
									<i class="mdi-content-send"></i> Copy From Designation
								</button>
							</div>
						</div>
						<div class="form-group col-md-12">
							<label class="col-md-3 control-label m-b-10">Employee Name</label>
							<div class="col-md-4 m-b-10">
								 <select class="form-control" data-search="true" id="copyRolesEmployee" >
									   <option value="">All</option>
									   <?php
									foreach ($departments as $key => $row) {
										echo '<optgroup label="'. $row .'">';
										
										foreach ($employees as $empKey => $empRow) {
											if ($row == $empRow['Department_Name']) {
												echo '<option value="'. $empRow['value'] .'">'. $empRow['text'] .'</option>';
											}
										}
										
										echo '</optgroup>';
									}
									?>
								   </select>
							</div>
							<div class="col-md-5">
								<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="CopyFromEmployee">
									<i class="mdi-content-send"></i> Copy From Employee
								</button>
							</div>
						</div>
					</div>			
					     	
					      
					    </div>
					  </div>
					</div>
				</div>		
				<div class="col-md-12 col-xs-12 text-center">
					<button type="reset" class="cancel" id="resetRoleEmployee" style="display: none;" >Reset</button>
					<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="editFormEmployeeRole" style="bottom: 5px;">
						<i class="mdi-content-send"></i> Submit
					</button>
				</div>
			</form>
			</div>
			<div class="modal-footer text-center">
			</div>			
		</div>
	</div>
</div>

<?php
$sty='';
$statusFlag=0;
/* Mask will be set if the dependency form datasetup is Incomplete */
for($i=0;$i<count($datasetupForms);$i++)
{
	if($datasetupForms[$i]['Table_Name'] == "employees" && $datasetupForms[$i]['Form_Status'] == "Open")
	{
		// To check whether all the previous datasetup prerequisites are completed 
		// If completed then the mask will be removed
		for($l=0;$l<$i;$l++)
		{
			if($datasetupForms[$l]['Form_Status'] == 'Open' && $datasetupForms[$l]['Is_Required'] == 1)
			{
				$statusFlag=1;
			}
		}
		if($statusFlag==1)
		{
			$sty ="pointer-events: none; opacity: 0.5;";
		}
		else{
			$sty = '';
		}
	} 
}
 if ((int)$empDirectoryAccess['View'] === 1 || (int)$rewardsAccess['View'] === 1) { ?>
<div class="col-md-12 portlets paddingCls tab-spacing-cls">
	<div class="col-md-12 paddingCls bg-f9f9f9 tab-wrapper">
		<div class="pointer-cls border-bottom-secondary tab-border-cls bg-f9f9f9 tab-body" id="empTab">
			<div class="tab-active-text tab-text-font text-secondary custom-tab-content"  id="employeeProfileTab">Employee</div>
		</div>
		<?php if ((int)$empDirectoryAccess['View'] === 1 ) { ?>
			<div class="pointer-cls border-bottom-secondary bg-f9f9f9 tab-body" id="employeeDirectoryTab">
				<div class="tab-text-font custom-tab-content" id="empDirTab">Employee Directory</div>
			</div>
		<?php } ?>
		<?php if ((int)$rewardsAccess['View'] === 1 && $isRewardEnabled === "Yes") { ?>
			<div class="pointer-cls border-bottom-secondary bg-f9f9f9 tab-body" id="rewardsAndRecognitionTab">
				<div class="tab-text-font custom-tab-content" id="rewardTab">Redeem Rewards</div>
			</div>
		<?php } ?>
	</div>
</div>
<?php }  ?>
<input type="hidden" name="LogUser" id="LogInUserAdmin" value="<?php echo $empUser['Admin']; ?>"/>
<input type="hidden" name="LogUserMan" id="LogInUserManager" value="<?php echo $empUser['Is_Manager']; ?>"/>
<div id="modalEmpRedirection" class="modal fade">
	<div class="modal-dialog">
		<div class="modal-content" style="min-height: 250px;    display: flex;    flex-direction: column;">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
					<i class="icons-office-52" style="color: black"></i>
				</button>
			</div>
			<div class="modal-body">
				<h4 class="modal-title" style="color:red">
					Add/Update Employee Details
				</h4>
				<div style="margin-top: 10px">
				'My Team' -> '
					<a id="myTeamLink" style="color:blue;text-decoration: underline;"  href=<?php echo $this->baseUrl('v3/my-team/team-summary'); ?>>
						Team Summary
					</a>'
				</div>
				<br>
				<?php if (!empty($empUser['Admin'])) { ?>
					<h4 class="modal-title" style="color:red">
						Add/Update Designations
					</h4>
					<div style="margin-top: 10px">
					'Core HR' -> 'Employee Data Management' -> '
						<a id="myTeamLink" style="color:blue;text-decoration: underline;"   href=<?php echo $this->baseUrl('in/core-hr/designations'); ?>>
							Designations
						</a>'
					</div>
				<?php }  ?>
			</div>
		</div>
	</div>
</div>
<!--my-profile card-->
<?php if (!empty($empUser['Admin'])) { ?>
	<div class="col-md-12 portlets my-profile-main-card add-panel-padding" id="myProfileAndTeamPanel">
		<div class="card my-profile-card-radius">
			<div class="row">
				<div class="col-xs-12 my-profile-img-column">
					<img width="50" height="50" alt="my-team" src="<?php echo $this->newImg;?>"/>
					<div class="my-profile-heading">
						Explore your enhanced employee profile and team summary!
					</div>
				</div>
				<div class="col-xs-12 my-profile-sub-text">
					We're excited to share our platform's new enhancements for a better experience! 
					Now you can easily update your 
					<a id="myProfileLink" class="text-secondary" href=<?php echo $this->baseUrl('v3/employee-self-service/my-profile'); ?>>
					profile
					</a> by clicking on your profile image, and get a snapshot of your team's dynamics under 'My Team' -> '
					<a id="myTeamLink" class="text-secondary" href=<?php echo $this->baseUrl('v3/my-team/team-summary'); ?>>
						Team Summary
					</a>', and manage job positions/designations in 'CoreHR' -> 'Employee Data Management' -> '
					<a id="myTeamLink" class="text-secondary" href=<?php echo $this->baseUrl('in/core-hr/designations'); ?>>
						Designations
					</a>'. These improvements make your profile management and team insights a better experience.
				</div>
			</div>
		</div>
	</div>
<?php }  else if ($empUser['Is_Manager'] == 1) { ?>
	<div class="col-md-12 portlets my-profile-main-card add-panel-padding" id="myProfileAndTeamPanel">
		<div class="card my-profile-card-radius">
			<div class="row">
				<div class="col-xs-12 my-profile-img-column">
					<img width="50" height="50" alt="my-team" src="<?php echo $this->newImg;?>"/>
					<div class="my-profile-heading">
						Explore your enhanced employee profile and team summary!
					</div>
				</div>
				<div class="col-xs-12 my-profile-sub-text">
					We're excited to share our platform's new enhancements for a better experience! 
					Now you can easily update your 
					<a id="myProfileLink" class="text-secondary" href=<?php echo $this->baseUrl('v3/employee-self-service/my-profile'); ?>>
					profile
					</a> by clicking on your profile image, and get a snapshot of your team's dynamics under 'My Team' -> '
					<a id="myTeamLink" class="text-secondary" href=<?php echo $this->baseUrl('v3/my-team/team-summary'); ?>>
						Team Summary
					</a>'. These improvements make your profile management and team insights a better experience.
				</div>
			</div>
		</div>
	</div>
<?php } else { ?>
	<div class="col-md-12 portlets my-profile-main-card add-panel-padding" id="myProfilePanel">
		<div class="card my-profile-card-radius">
			<div class="row">
				<div class="col-xs-12 my-profile-img-column">
					<img width="50" height="50" alt="my-team" src="<?php echo $this->newImg;?>"/>
					<div class="my-profile-heading">
						Get ready for an enhanced profile experience!
					</div>
				</div>
				<div class="col-xs-12 my-profile-sub-text">
					Your employee profile experience just got an upgrade! In <span class="text-secondary" id="redirectionTimer"></span> seconds, you'll be automatically taken to the new interface. 
					For immediate access, simply click on your 
					<a id="myProfileLink" class="text-secondary" href=<?php echo $this->baseUrl('v3/employee-self-service/my-profile'); ?>>
					employee profile
					</a> (Select using your user profile in the top right corner).
				</div>
			</div>
		</div>
	</div>
<?php }?>

<?php 
	if (($empUser['View'])&& ((!empty($customFormNameA) && array_key_exists("Enable",$customFormNameA) && $customFormNameA['Enable'] == 1) || empty($customFormNameA))) {
?>
<!-- Employee Grid Panel -->
<div class="col-md-12 portlets add-panel-padding old-emp-panel hidden">
	<div class="panel" id="gridPanelEmployee" style="<?php echo $sty;?>">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong id="lblFormNameA"><?php echo $finalFormName; ?></strong></h3>
		</div>
		<div class="panel-content">
			<!--Employee Grid Toolbar Icons-->
			<div class="m-b-10">
				<?php if ($empUser['Add'] && !empty($empUser['Admin'])) { ?>
				<!-- Add Employee Button -->
				<button type="button" class="btn btn-white btn-embossed toolbar-icons" title="Add" data-toggle="modal" data-target="#modalFormEmployee" id="addEmployee">
					<i class="mdi-content-add"></i><span class="hidden-xs hidden-sm"> Add</span>
				</button>
				<?php } ?>
				<!-- View Employee Button -->
				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="viewEmployee" title="View">
					<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
				</button>
				
				<?php if ($empUser['Update']) { ?>
				<!-- Edit Employee Button -->
				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="Edit" id="editEmployee">
					<i class="mdi-editor-mode-edit"></i><span class="hidden-xs hidden-sm"> Edit</span>
				</button>    
				<?php } if($rolesUser['Op_Choice']==1) { ?>
				<!-- Delete Employee Button -->
				<button type="button" class="btn btn-white btn-embossed toolbar-icons disabled btn-off" title="Role"  id="roleEmployee">
					<i class="fa fa-key"></i><span class="hidden-xs hidden-sm"> Role</span>
				</button>

				<button type="button" class="btn btn-white btn-embossed toolbar-icons disabled btn-off" title="Clone Roles"  id="cloneRolesEmployee">
					<i class="hr-clone-roles"></i><span class="hidden-xs hidden-sm"> Clone Roles</span>
				</button>
				
				<?php } if(($empUser['Admin'] == 'admin')){?>
				<button type="button" class="btn btn-white btn-embossed toolbar-icons disabled btn-off" title="Clone"  id="cloneEmployee">
					<i class="hr-employee-clone"></i><span class="hidden-xs hidden-sm"> Clone</span>
				</button>
				<button type="button" class="btn btn-white btn-embossed toolbar-icons disabled btn-off " title="Department" id="departmentLevelAccess">
					<i class="hr-department"></i><span class="hidden-xs hidden-sm"> Department Level Access</span>
				</button>
				<?php } if ($empUser['Delete']==1 /*|| ($empSelfUser['Delete'] && $this->formStatus == 0)*/) { ?>
				<!-- Delete Employee Button -->
				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="Delete" id="deleteEmployee">
					<i class="mdi-action-delete"></i><span class="hidden-xs hidden-sm"> Delete</span>
				</button>  
				<?php }?>
				
				<!-- Filter Employee Button -->
				<a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterEmployee">
					<i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm"> Filter</span>
				</a>
				<button type="button" class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="ViewEmployeedetails" title="Expand All" >
						<i class="hr-expand"></i><span class="hidden-xs hidden-sm">Expand All</span>
				</button>
			</div>
			
			<!-- Employee Grid Table -->
			<table class="table dataTable table-striped table-dynamic table-hover" id="tableEmployee">
				<thead>
					<tr>
						<th></th>
						<th id="employeeEmployeeId">Employee Id</th>
						<th id="employeeEmployeeName">Name</th>
						<th id="employeeEmployeeDesignation">Designation</th>
						<th id="employeeEmployeeDepartment">Department</th>
                        <th id="employeeEmployeeLocation">Location</th>
						<th id="employeeEmployeeStatus">Status</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
		</div>
	</div>
</div>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="employee-context-menu" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextEmployee"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($empUser['Update'] && (!empty($empUser['Admin']) || $empUser['Is_Manager'] == 0)) { ?>
		<li><a tabindex="-1" id="editContextEmployee"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<?php } if($rolesUser['Op_Choice']==1) { ?>
		<li><a tabindex="-1" id="roleContextEmployee"><i class="fa fa-key" style="font-size: 19px;margin-left: 4px;"></i> Role</a></li>
		<li><a tabindex="-1" id="CloneRolesContextEmployee"><i class="hr-clone-roles"></i> Clone Roles</a></li>
		<?php } if ($empUser['Admin'] == 'admin') { ?>
		<li><a tabindex="-1" id="cloneContextEmployee"><i class="hr-employee-clone" style="font-size: 19px;margin-left: 4px;"></i> Clone</a></li>
		<li><a tabindex="-1" id="departmentContextEmployee"><i class="hr-department"></i> Department Level Access</a></li>
		<?php } if ($empUser['Delete']==1 /*|| ($empSelfUser['Delete']*/ && $this->formStatus == 0) { ?>
		<li><a tabindex="-1" id="deleteContextEmployee"><i class="mdi-action-delete"></i> Delete</a></li>
		<?php } ?>
	</ul>
</div>

<!-- Employee Filter form -->
<div class="builder" id="filterPanelEmployee">
	<div id="closeFilterEmployee"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel" style="width: 100%;" id="filterResetEmployee">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="filterApplyEmployee">Apply</button>
			<?php if ($empUser['Is_Manager'] == 0 && empty($empUser['Admin'])) { ?>
			<div class="form-group">
				<label>Employee Name</label>
				<input type="text" class="form-control" id="filterEmployeeName" readonly="readonly" value="<?php echo $empUser['Employee_Name']; ?>" >
			</div>
			
			<?php } else { ?>
			
			<div class="form-group">
				<label>Employee Name</label>
				<input type="text" class="form-control" id="filterEmployeeName" placeholder="Employee Name">
			</div>
			
			<?php } ?>
			
			
			<div class="form-group">
				<label>Manager Name</label>
				<input type="text" class="form-control" id="filterManagerName" placeholder="Manager Name">
			</div>
			
			<div class="form-group">
				<label>Designation</label>
				<select class="form-control" data-search="true" id="filterDesigination" >
					<option value="">All</option>
					<?php
					foreach ($designation as $key => $row)
					{
						echo '<option value="'.$key.'">'.$row.'</option>';
					}
					?>
				</select>
			</div>
			
			<div class="form-group">
				<label>Department</label>
				<select class="form-control" data-search="true" id="filterDepartment" >
					<option value="">All</option>
					<?php
					foreach ($deptHierarchy as $key => $row)
					{
						echo '<option value="'. $row['Department_Id'] .'">'. $row['Department_Name'] .'</option>';
						
						foreach($row['Child'] as $val =>$name)
						{
							
							if($name['Parent_Type_Id'] == $row['Department_Id'])
							{
								echo '<option value="'. $name['Department_Id'] .'">&nbsp;&nbsp;'. $name['Department_Name'] .'</option>';
								
								foreach($row['Child'] as $v =>$k)
								{
									if($k['Parent_Type_Id'] == $name['Department_Id'])
										echo '<option value="'. $k['Department_Id'] .'">&nbsp;&nbsp;&nbsp;&nbsp;'. $k['Department_Name'] .'</option>';
								}
								
							}
						}
					}
					?>
				</select>
			</div>
            
            <div class="form-group">
				<label>Location</label>
				<select class="form-control" data-search="true" id="filterLocation" >
					<option value="">All</option>
					<?php
					foreach ($empLocation as $key => $row)
					{
						echo '<option value="'.$key.'">'.$row.'</option>';
					}
					?>
				</select>
			</div>
			
			<div class="form-group">
				<label>Employee Type</label>
				<select class="form-control" data-search="true" id="filterEmployeeType" >
					<option value="">All</option>
					<?php
					foreach ($empType as $key => $row)
					{
						echo '<option value="'.$key.'">'.$row.'</option>';
					}
					?>
				</select>
			</div>
			
			<?php if ($orgDetails['Field_Force'] == 1) { ?>
			<div class="form-group">
				<label>Service Provider</label>
				<select class="form-control" data-search="true" id="filterServiceProvider">
					<option value="">All</option>
					<?php
					foreach ($serviceProvider as $key => $row)
					{
						echo '<option value="'. $key .'">'. $row .'</option>';
					}
					?>
				</select>												
			</div>
			<?php } ?>

			<div class="form-group">
				<label>Employee Status</label>
				<select class="form-control" data-search="true" id="filterEmployeeStatus" >
					<option value="">All</option>
					<option value="Active">Active</option>
					<option value="InActive">InActive</option>
				</select>
			</div>
            
            <div class="form-group">
				<label>Employee Category</label>
				<select class="form-control" data-search="true" id="filterEmployeeCategory" >
					<option value="">All</option>
					<option value="Manager">Manager</option>
					<option value="NonManager">Non-Manager</option>
				</select>
			</div>
			
		</div>
	</div>
</div>

<!--View, Edit Form Modal-->
<div class="modal fade" id="modalFormEmployee" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace" id="backEmpEmployees"></i>
				</button>
				
				<?php if ($empUser['Update'] && (!empty($empUser['Admin']) || $empUser['Is_Manager'] == 0)) { ?>
				<button type="button" class="close form-icons" aria-hidden="true" id="editInViewEmployee">
					<i class="mdi-editor-mode-edit"></i>
				</button>
				<?php } ?>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<!--View Employee Import Form-->
				<form id="viewFormEmployee" data-style="arrow">
					<input type="hidden" name="fieldForce" id="fieldForce" value="<?php echo $orgDetails['Field_Force']; ?>" />
                    <fieldset id="viewPersonalInfo">
						<legend>Personal Info</legend>
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="control-label">Personal Details</label>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Profile Picture</label>
								<div class="col-md-7 col-sm-7">
									<img id='employeePhoto' style="width: 132px; height: 170px;">
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Employee Id</label>
								<div class="col-md-7 col-sm-7"><p id="vUserDefEmpId">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group fieldViewIsExternalEmployeeId">
								<label class="col-md-5 col-sm-5 control-label">Biometric Integration Id</label>
								<div class="col-md-7 col-sm-7"><p id="vExternalEmployeeId">-</p></div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">First Name</label>
								<div class="col-md-7 col-sm-7"><p id="vFirstName">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Middle Name</label>
								<div class="col-md-7 col-sm-7"><p id="vMiddleName">-</p></div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Last Name</label>
								<div class="col-md-7 col-sm-7"><p id="vLastName">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Known as</label>
								<div class="col-md-7 col-sm-7"><p id="vNickName">-</p></div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">DOB</label>
								<div class="col-md-7 col-sm-7"><p id="vDOB">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Place Of Birth</label>
								<div class="col-md-7 col-sm-7"><p id="vPlaceOfBirth">-</p></div>
							</div>
						</div>

						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Gender</label>
								<div class="col-md-7 col-sm-7"><p id="vGender">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Nationality</label>
								<div class="col-md-7 col-sm-7"><p id="vNationality">-</p></div>
							</div>
						</div>

						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Languages Known</label>
								<div class="col-md-7 col-sm-7"><p id="vLanguagesKnown">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Blood Group</label>
								<div class="col-md-7 col-sm-7"><p id="vBloodGroup">-</p></div>
							</div>
						</div>

						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Marital Status</label>
								<div class="col-md-7 col-sm-7"><p id="vMaritalStatus">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Manager</label>
								<div class="col-md-7 col-sm-7"><p id="vIsManager">-</p></div>
							</div>
						</div>
						
						
						<div class="row panelOtherCountry">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Military Service</label>
								<div class="col-md-7 col-sm-7"><p id="vMilitaryService">-</p></div>
							</div>
							<?php if($ethnicRace['Enable'] == 1) {?>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label"><?php echo $ethnicRace['Field_Name'];?></label>
								<div class="col-md-7 col-sm-7"><p id="vEthnicRace">-</p></div>
							</div>
							<?php } ?>
						</div>
						
						<div class="row panelOtherCountry">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Religion</label>
								<div class="col-md-7 col-sm-7"><p id="vReligion">-</p></div>
							</div>
							<?php if($caste['Enable'] == 1) {?>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label"><?php echo $caste['Field_Name'];?></label>
								<div class="col-md-7 col-sm-7"><p id="vCaste">-</p></div>
							</div>
							<?php } ?>
						</div>

						<div class="row panelOtherCountry">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Hobby</label>
								<div class="col-md-7 col-sm-7"><p id="vHobby">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Do you have a disability?</label>
								<div class="col-md-7 col-sm-7"><p id="vPhysicallyChallenged">-</p></div>
							</div>
						</div>
						
						<div class="row panelOtherCountry">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Smoker</label>
								<div class="col-md-7 col-sm-7"><p id="vSmoker">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Smoker As Of</label>
								<div class="col-md-7 col-sm-7"><p id="vSmokerAsOf">-</p></div>
							</div>
						</div>
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Personal Email</label>
								<div class="col-md-7 col-sm-7"><p id="vPersonalEmail">-</p></div>
							</div>
						</div>
						
						<div class="row panelViewEmployeeDependent">
							<div class="col-md-12 col-sm-12 form-group">
								<div class="row">
									<div class="col-md-6 col-sm-6 form-group">
										<label class="control-label">Dependent Details</label>
									</div>
								</div>
								<table class="table dataTable table-striped table-dynamic table-hover" id="tableViewEmployeeDependent">
									<thead>
										<tr>
                                            <th></th>
											<th id="employeeDependentName">Name</th>
											<th id="employeeDependentRelationship">Relationship</th>
											<th id="employeeDependentBirthDate">Birth Date</th>
										</tr>
										</thead>
									<tbody>
									
									</tbody>
								</table>
							</div>
						</div>
						
						<div class="row fieldViewDrivingLicense">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="control-label">Driving License Details</label>
							</div>
						</div>
						
						<div class="row fieldViewDrivingLicense">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Driving License No</label>
								<div class="col-md-7 col-sm-7"><p id="vDrivingLicenseNo">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Issue Date</label>
								<div class="col-md-7 col-sm-7"><p id="vIssueDate">-</p></div>
							</div>
						</div>
						
						<div class="row fieldViewDrivingLicense">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Expiry Date</label>
								<div class="col-md-7 col-sm-7"><p id="vExpiryDate">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Issuing Authority</label>
								<div class="col-md-7 col-sm-7"><p id="vIssuingAuthority">-</p></div>
							</div>
						</div>
						
						<div class="row fieldViewDrivingLicense">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Issuing Country</label>
								<div class="col-md-7 col-sm-7"><p id="vIssuingCountry">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Issuing State</label>
								<div class="col-md-7 col-sm-7"><p id="vIssuingState">-</p></div>
							</div>
						</div>
						
						<div class="row fieldViewDrivingLicense">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Vehicle Type</label>
								<div class="col-md-7 col-sm-7"><p id="vVehicleType">-</p></div>
							</div>
						</div>
						
						<div class="row fieldViewPassport">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="control-label">Passport Details</label>
							</div>
						</div>
						
						<div class="row fieldViewPassport">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Passport No</label>
								<div class="col-md-7 col-sm-7"><p id="vPassportNo">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Issue Date</label>
								<div class="col-md-7 col-sm-7"><p id="vPassportIssueDate">-</p></div>
							</div>
						</div>
						
						<div class="row fieldViewPassport">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Expiry Date</label>
								<div class="col-md-7 col-sm-7"><p id="vPassportExpiryDate">-</p></div>
							</div>
						</div>
						
						<!-- Start Allow User Sign in -->
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Allow User Sign In</label>
								<div class="col-md-7 col-sm-7"><p id="formViewAllowUserSignin">-</p></div>
							</div>
						
							<div class="col-md-6 col-sm-6 form-group fieldViewWorkEmailAddress">
								<label class="col-md-5 col-sm-5 control-label">Work Email Address</label>
								<div class="col-md-7 col-sm-7"><p id="vWorkEmailAddress">-</p></div>
							</div>
						</div>
							
						<!-- End Allow User Sign in -->
						
						<!-- Enable Sign In With Mobile Number -->
						<div class="row fieldViewSignInMobileNumber">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Enable Sign In With Mobile Number</label>
								<div class="col-md-7 col-sm-7"><p id="formViewEnableSignInWithMobileNo">-</p></div>
							</div>
						
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Mobile Number</label>
								<div class="col-md-7 col-sm-7"><p id="vSignInMobileNumber">-</p></div>
							</div>
						</div>
					</fieldset>
					
					<fieldset id="viewJobInfo">
						<legend>Job Info</legend>
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="control-label">Job Details</label>
							</div>
						</div>
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Department</label>
								<div class="col-md-7 col-sm-7"><p id="vDepartmentName">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Designation</label>
								<div class="col-md-7 col-sm-7"><p id="vDesignationName">-</p></div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Employee Type</label>
								<div class="col-md-7 col-sm-7"><p id="vEmployeeType">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Employee Email</label>
								<div class="col-md-7 col-sm-7"><p id="vEmployeeMail">-</p></div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Location</label>
								<div class="col-md-7 col-sm-7"><p id="vLocation">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Job Code</label>
								<div class="col-md-7 col-sm-7"><p id="vJobCode">-</p></div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Date Of Join</label>
								<div class="col-md-7 col-sm-7"><p id="vDateOfJoin">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Manager Name</label>
								<div class="col-md-7 col-sm-7"><p id="vManagerName">-</p></div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Employee Status</label>
								<div class="col-md-7 col-sm-7"><p id="vEmployeeStatus">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Work Schedule</label>
								<div class="col-md-7 col-sm-7"><p id="vWorkSchedule">-</p></div>
							</div>
						</div>
						
						<div class="row fieldViewInactive">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Employee InActive Date</label>
								<div class="col-md-7 col-sm-7"><p id="vEmployeeInActiveDate">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Resignation Reason</label>
								<div class="col-md-7 col-sm-7"><p id="vESICReason">-</p></div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Attendance Enforced Payment</label>
								<div class="col-md-7 col-sm-7"><p id="vAttendanceEnforcement">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Commission Based Employee</label>
								<div class="col-md-7 col-sm-7"><p id="vCommissionBasedEmployee">-</p></div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">TDS Exemption</label>
								<div class="col-md-7 col-sm-7"><p id="vTDSEXemption">-</p></div>
							</div>	
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Probation Date</label>
								<div class="col-md-7 col-sm-7"><p id="vProbationDate">-</p></div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Confirmed</label>
								<div class="col-md-7 col-sm-7"><p id="vConfirmed">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group fieldViewIsConfirmed">
								<label class="col-md-5 col-sm-5 control-label">Confirmation Date</label>
								<div class="col-md-7 col-sm-7"><p id="vConfirmationDate">-</p></div>
							</div>
						</div>
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Admin For</label>
								<div class="col-md-7 col-sm-7"><p id="vDepartmentMap">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Previous Experience</label>
								<div class="col-md-7 col-sm-7"><p id="vPreviousEmployeeExperience">-</p></div>
							</div>
						</div>
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Employee Profession</label>
								<div class="col-md-7 col-sm-7"><p id="vEmployeeProfession">-</p></div>
							</div>
							<?php if ($orgDetails['Field_Force'] == 1) { ?>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Service Provider</label>
								<div class="col-md-7 col-sm-7"><p id="vServiceProvider">-</p></div>
							</div>
							<?php } ?>
						</div>
						
						<div class="row panelViewEmployeeExperience">
							<div class="col-md-12 col-sm-12 form-group">
								<div class="row">
									<div class="col-md-6 col-sm-6 form-group">
										<label class="control-label">Experience Details</label>
									</div>
								</div>
								<table class="table dataTable table-striped table-dynamic table-hover" id="tableViewEmployeeExperience">
									<thead>
										<tr>
                                            <th></th>
											<th id="employeeExperienceCompanyName">Company Name</th>
											<th id="employeeExperienceLocation">Location</th>
											<th id="employeeExperienceDesignation">Designation</th>
											<th id="employeeExperienceStartDate">Start Date</th>
											<th id="employeeExperienceEndDate">End Date</th>
											<th id="employeeExperienceDuration">Duration (months)</th>
										</tr>
									</thead>
									<tbody>
									
									</tbody>
								</table>
							</div>
						</div>
						
						<div class="row panelViewEmployeeAssets">
							<div class="col-md-12 col-sm-12 form-group">
								<div class="row">
									<div class="col-md-6 col-sm-6 form-group">
										<label class="control-label">Asset Details</label>
									</div>
								</div>
								<table class="table dataTable table-striped table-dynamic table-hover" id="tableViewEmployeeAssets">
									<thead>
										<tr>
                                            <th></th>
											<th id="employeeAssetViewAssetName">Asset Name</th>
											<th id="employeeAssetViewSerialNo">Serial No</th>
											<th id="employeeAssetViewReceiveDate">Receive Date</th>
											<th id="employeeAssetViewReturnDate">Return Date</th>
										</tr>
									</thead>
									<tbody>
									
									</tbody>
								</table>
							</div>
						</div>
						
						
					</fieldset>
					
					<fieldset id="viewContactInfo">
						<legend>Contact Info</legend>
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="control-label">Permanent Address</label>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Apartment Name</label>
								<div class="col-md-7 col-sm-7"><p id="vPerApartmentName">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Street</label>
								<div class="col-md-7 col-sm-7"><p id="vPerStreet">-</p></div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">City</label>
								<div class="col-md-7 col-sm-7"><p id="vPerCity">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">State</label>
								<div class="col-md-7 col-sm-7"><p id="vPerState">-</p></div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Country</label>
								<div class="col-md-7 col-sm-7"><p id="vPerCountry">-</p></div>
							</div>
							<?php if($pincode['Enable'] == 1) { ?>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label"><?php echo $pincode['Field_Name'];?></label>
								<div class="col-md-7 col-sm-7"><p id="vPerPincode">-</p></div>
							</div>
							<?php } ?>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="control-label">Current Address</label>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Apartment Name</label>
								<div class="col-md-7 col-sm-7"><p id="vCurApartmentName">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Street</label>
								<div class="col-md-7 col-sm-7"><p id="vCurStreet">-</p></div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">City</label>
								<div class="col-md-7 col-sm-7"><p id="vCurCity">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">State</label>
								<div class="col-md-7 col-sm-7"><p id="vCurState">-</p></div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Country</label>
								<div class="col-md-7 col-sm-7"><p id="vCurCountry">-</p></div>
							</div>
							<?php if($pincode['Enable'] == 1) { ?>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label"><?php echo $pincode['Field_Name'];?></label>
								<div class="col-md-7 col-sm-7"><p id="vCurPincode">-</p></div>
							</div>
							<?php } ?>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="control-label">Office Address</label>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Apartment Name</label>
								<div class="col-md-7 col-sm-7"><p id="vOffApartmentName">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Street</label>
								<div class="col-md-7 col-sm-7"><p id="vOffStreet">-</p></div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">City</label>
								<div class="col-md-7 col-sm-7"><p id="vOffCity">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">State</label>
								<div class="col-md-7 col-sm-7"><p id="vOffState">-</p></div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Country</label>
								<div class="col-md-7 col-sm-7"><p id="vOffCountry">-</p></div>
							</div>
							<?php if($pincode['Enable'] == 1) {?>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label"><?php echo $pincode['Field_Name'];?></label>
								<div class="col-md-7 col-sm-7"><p id="vOffPincode">-</p></div>
							</div>
							<?php } ?>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="control-label">Contact Information</label>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">LandLine Number</label>
								<div class="col-md-7 col-sm-7"><p id="vLandLineNumber">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Mobile No</label>
								<div class="col-md-7 col-sm-7"><p id="vMobileNo">-</p></div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Emergency Contact No</label>
								<div class="col-md-7 col-sm-7"><p id="vFaxNumber">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Work Number</label>
								<div class="col-md-7 col-sm-7"><p id="vWorkNumber">-</p></div>
							</div>
						</div>
					</fieldset>
					
					<fieldset id="viewCareerInfo">
						<legend>Career Info</legend>
						<div class="row panelViewEmployeeEducation">
							<div class="col-md-12 col-sm-12 form-group">
								<div class="row">
									<div class="col-md-6 col-sm-6 form-group">
										<label class="control-label">Education Details</label>
									</div>
								</div>
                                
                                <table class="table dataTable table-striped table-dynamic table-hover" id="tableViewEmployeeEducation">
									<thead>
										<tr>
                                            <th></th>
											<th id="employeeEducationSchoolDiplomaDeg">School/Diploma/Deg</th>
											<th id="employeeEducationSpecialisation">Specialisation</th>
											<th id="employeeEducationInstituteName">Institute Name</th>
											<th id="employeeEducationUniversity">University</th>
											<th id="employeeEducationYearofPassing">Year of Passing</th>
											<th id="employeeEducationPercentage">Percentage</th>
											<th id="employeeEducationGrade">Grade</th>
										</tr>
									</thead>
									<tbody>
									
									</tbody>
								</table>
							</div>
						</div>
                    	
						<div class="row panelViewEmployeeCertificate">
							<div class="col-md-12 col-sm-12 form-group">
								<div class="row">
									<div class="col-md-6 col-sm-6 form-group">
										<label class="control-label">Certification Details</label>
									</div>
								</div>
								<table class="table dataTable table-striped table-dynamic table-hover" id="tableViewEmployeeCertificate">
									<thead>
										<tr>
                                            <th></th>
											<th id="employeeCertificateName">Certification Name</th>
											<th id="employeeCertificateReceivedDate">Received Date</th>
											<th id="employeeCertificateReceivedFrom">Received From</th>
										</tr>
									</thead>
									<tbody>
									
									</tbody>
								</table>
							</div>
						</div>
						
						<div class="row panelViewEmployeeTraining">
							<div class="col-md-12 col-sm-12 form-group">
								<div class="row">
									<div class="col-md-6 col-sm-6 form-group">
										<label class="control-label">Training Details</label>
									</div>
								</div>
								<table class="table dataTable table-striped table-dynamic table-hover" id="tableViewEmployeeTraining">
									<thead>
										<tr>
                                            <th></th>
											<th id="employeeTrainingName">Training Name</th>
											<th id="employeeTrainingStartDate">Start Date</th>
											<th id="employeeTrainingEndDate">End Date</th>
											<th id="employeeTrainingDuration">Duration (months)</th>
											<th id="employeeTrainingTrainer">Trainer</th>
											<th id="employeeTrainingCenter">Center</th>
										</tr>
									</thead>
									<tbody>
									
									</tbody>
								</table>
							</div>
						</div>
						
						<div class="row panelViewEmployeeAward">
							<div class="col-md-12 col-sm-12 form-group">
								<div class="row">
									<div class="col-md-6 col-sm-6 form-group">
										<label class="control-label">Award Details</label>
									</div>
								</div>
								<table class="table dataTable table-striped table-dynamic table-hover" id="tableViewEmployeeAward">
									<thead>
										<tr>
                                            <th></th>
											<th id="employeeAwardName">Award Name</th>
											<th id="employeeAwardReceivedOn">Received On</th>
											<th id="employeeAwardReceivedFrom">Received From</th>
											<th id="employeeAwardReceivedFor">Received For</th>
										</tr>
									</thead>
									<tbody>
									
									</tbody>
								</table>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="control-label">Skill Set</label>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Primary Skill</label>
								<div class="col-md-7 col-sm-7"><p id="vPrimarySkill">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Secondary Skill</label>
								<div class="col-md-7 col-sm-7"><p id="vSecondarySkill">-</p></div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Known Skills</label>
								<div class="col-md-7 col-sm-7"><p id="vKnownSkills">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Hands On</label>
								<div class="col-md-7 col-sm-7"><p id="vHandsOn">-</p></div>
							</div>
						</div>
					</fieldset>
					
					<fieldset id="viewBankInfo">
						<legend>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Other Details&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</legend>
						<!-- <div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="control-label">Bank Account Info</label>
							</div>
						</div>
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Account Number</label>
								<div class="col-md-7 col-sm-7"><p id="vAccountNumber">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Bank Name</label>
								<div class="col-md-7 col-sm-7"><p id="vBankName">-</p></div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Branch Name</label>
								<div class="col-md-7 col-sm-7"><p id="vBranchName">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Branch Street</label>
								<div class="col-md-7 col-sm-7"><p id="vBranchStreet">-</p></div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Branch City</label>
								<div class="col-md-7 col-sm-7"><p id="vBranchCity">-</p></div>
							</div>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Branch State</label>
								<div class="col-md-7 col-sm-7"><p id="vBranchState">-</p></div>
							</div>
						</div>
						
						<div class="row">
						<?php if($pincode['Enable'] == 1) {?>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label"><?php //echo $pincode['Field_Name'];?></label>
								<div class="col-md-7 col-sm-7"><p id="vBranchPincode">-</p></div>
							</div>
							<?php } ?>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">Account Type</label>
								<div class="col-md-7 col-sm-7"><p id="vAccountType">-</p></div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">IFSC Code</label>
								<div class="col-md-7 col-sm-7"><p id="vIFSCCode">-</p></div>
							</div>
						</div> -->
						
						<div class="row panelViewEmployeeInsurance">
							<div class="col-md-12 col-sm-12 form-group">
								<div class="row">
									<div class="col-md-6 col-sm-6 form-group">
										<label class="control-label">Insurance Details</label>
									</div>
								</div>
								<table class="table dataTable table-striped table-dynamic table-hover" id="tableViewEmployeeInsurance">
									<thead>
										<tr>
											<th id="employeeInsuranceName">Insurance Name</th>
											<th id="employeeInsurancePolicyNo">Policy No</th>
										</tr>
									</thead>
									<tbody>
									
									</tbody>
								</table>
							</div>
						</div>
						
						<div class="row panelOtherCountry">
							<?php if($aadhaarNo['Enable'] == 1) { ?>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="control-label">Identity Details</label>
							</div>
							<?php } if($pan['Enable'] == 1) { ?>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="control-label">Taxable Details</label>
							</div>
							<?php } ?>
						</div>
						
						<div class="row panelOtherCountry">
							<?php if($aadhaarNo['Enable'] == 1) { ?>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label"><?php echo $aadhaarNo['Field_Name'];?></label>
								<div class="col-md-7 col-sm-7"><p id="vAadhaarNo">-</p></div>
							</div>
							<?php } if($pan['Enable'] == 1) { ?>
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label"><?php echo $pan['Field_Name'];?></label>
								<div class="col-md-7 col-sm-7"><p id="vPAN">-</p></div>
							</div>
							<?php } ?>
						</div>
						
						<div class="row panelOtherCountry">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="control-label">Provident Fund Details</label>
							</div>
						</div>
						
						<div class="row panelOtherCountry">
							<div class="col-md-6 col-sm-6 form-group">
								<label class="col-md-5 col-sm-5 control-label">UAN Number</label>
								<div class="col-md-7 col-sm-7"><p id="vUAN">-</p></div>
							</div>
							<?php if($pfNumber['Enable'] == 1) { ?>
							<div class="col-md-6 col-sm-6 form-group panelViewEmployeeProvidentFund">
								<label class="col-md-5 col-sm-5 control-label"><?php echo $pfNumber['Field_Name'];?></label>
								<div class="col-md-7 col-sm-7"><p id="vProvidentFundNumber">-</p></div>
							</div>
							<?php } ?>
						</div>
					</fieldset>
				</form>
				
				<?php if ($empUser['Update']) { ?>
				<!--Add/Edit Employee Form-->
				<form role="form" id="editFormEmployee" class="form-horizontal form-validation" data-style="arrow" action="">
					<input type="hidden" name="Emp_Id" id="Emp_Id" /> 
					<input type="hidden" name="Employee_Id" id="Employee_Id" /> 
					<input type="hidden" name="EmployeeRoleAccess" id="EmployeeRoleAccess" />
					<input type="hidden" name="Emp_Image" id="Emp_Image" />
					<input type="hidden" name="Emp_Form_Status" id="Emp_Form_Status" value="0" />
					<input type="hidden" name="superAdminAcc" id="superAdminAcc" value="<?php echo $rolesUser['Op_Choice']; ?>" />					
					<input type="hidden" name="empProfileValid" id="empProfileValid" value="0" />
					<?php
					/** pass an hidden array to jquery to check whether the elements exists or not. Based on that, field
						validation will be done **/
						foreach($enableFieldArr as $key=>$value)
						{
						  echo '<input type="hidden" name="Per_Enable_Fields[]" id="is'.$key.'Enable" value="'. $value. '">';
						}
					?>					
					<fieldset id="editPersonalInfo">
						<legend>Personal Info</legend>
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<div class="fileinput fileinput-new" data-provides="fileinput">
									<!--<label class="col-lg-5 col-md-5 col-sm-5 control-label">Upload Photo</label>-->
									<p class="col-lg-5 col-md-5 col-sm-5"><strong>Upload Photo<br></strong>										
										<span class="short_explanation">*</span> Image Size : Height: 170px , Width: 132px<br>
										<span class="short_explanation">*</span> Allowed Image Formats : (.jpeg, .jpg, .png, .gif)
									</p>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<div id="parentDiv" style="border:1px solid #ddd;border-radius: 1px; padding: 4px;height: 180px;width: 142px;">
											<div class="fileinput-new removeDefaultImage">
												<?php echo '<img style="height: 170px;width: 132px;" class="img-responsive" alt="Profile Photo" data-src="" src="'.$defaultImage.'"/>'; ?>
											</div>
											
											<div id="childDiv" style="height: 175px;width: 137px;">
												<div class="fileinput-preview profilesize addDefaultImage"></div>
												
												<div class="btn-file changeImageBasedHidden" id="changeImageDiv" style=" margin-top: -41px; margin-left: 15px; background: rgba(65, 67, 70, 0.5); color: #fff; font-size: 10px; font-weight: bold; left: 0; line-height: 9px; position: absolute; padding: 7px 0; text-align: center; width: 142px;">
													<span >Change</span>											
													<input type="file" name="changeuploadEmployeeFile" id="FileChangeImagewidth">
												</div>
												
												<div class="changeImageBasedHidden" id="RemoveImageDiv">
													<a href="#" class="btn-delete" id="deleteImage" style=" margin-top: -19px; margin-left: 16px; background: rgba(65, 67, 70, 0.5); color: #fff; font-size: 9px; font-weight: bold; left: 0; line-height: 10px; position: absolute; padding: 7px 0; text-align: center; width: 141px;"> Remove </a>													
												</div>
											
												<div class="selectImageBasedHidden" id="selectImageDiv">																									
													<div class="btn-file currentselectImageWidth" style="margin-top: -19px; margin-left: 15px; background: rgba(65, 67, 70, 0.5); color: #fff; font-size: 10px; font-weight: bold; left: 0; line-height: 9px; position: absolute; padding: 7px 0; text-align: center; width: 142px;">
													<span>Select image...</span>
													<input type="file" name="selectuploadEmployeeFile" id="FileSelectImageWidth">												
													</div>												
												</div>
											</div>
										</div>
									</div>
										
									</div>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Employee Id <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control alphaNumSpCDotHySlash vRequired" id="userDefinedEmployeeId" placeholder="Employee Id" minlength="1"	maxlength="50" <?php echo $disableField; ?>>
									</div>
								</div>
							</div>

							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Biometric Integration Id</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control alphaNum" name="formJobEditExternalEmployeeId" id="formJobEditExternalEmployeeId" placeholder="Biometric Integration Id" minlength="1" maxlength="15" <?php echo $disableField; ?>>
										<div class="biometricIntegrationIdMessage"><b style="color: red">Please enter the biometric integration id for biometric data processing.</b></div>
									</div>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Salutation <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<select class="form-control vRequired" data-search="true" id="formEditSalutation" name="formEditSalutation">
											<option value="">--Select--</option>													
											<?php
												foreach ($salutationPair as $key => $row)
												{
													echo '<option value="'. $key .'">'. $row .'</option>';
												}
											?>
										</select>
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">First Name <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control employeeName vEmpName" id="formEditFirstName" name="formEditFirstName" placeholder="First Name" <?php echo $disableField; ?>>
									</div>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Middle Name</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control employeeName vLastName" id="formEditMiddleName" name="formEditMiddleName" placeholder="Middle Name" <?php echo $disableField; ?>>
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Last Name <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vRequired employeeName vLastName" id="formEditLastName" name="formEditLastName" placeholder="Last Name" <?php echo $disableField; ?>>
									</div>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label"> Known as</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control onlyLetterSp" minlength="1" maxlength="50" id="formEditNickName" name="formEditNickName" placeholder="Known as" >
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Gender <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">										
										<select class="form-control vRequired" data-search="true" id="formEditGender" name="formEditGender" <?php echo $disableField; ?>>
											<option value="Male">Male</option>
											<option value="Female">Female</option>
										</select>
									</div>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">DOB <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="date-picker form-control vRequired vDOB datePickerRead" name="formEditDOB" id="formEditDOB" placeholder="Select a date..." <?php echo $disableField; ?>>										
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Place of Birth</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control onlyLetterSp" minlength="1" maxlength="50" id="formEditPlaceOfBirth" name="formEditPlaceOfBirth" placeholder="Birth Place" <?php echo $disableField; ?>>
									</div>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Marital Status <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<select class="form-control vRequired" data-search="true" id="formEditMaritalStatus" name="formEditMaritalStatus">
											<option value="">--Select--</option>
											<?php
												foreach ($maritalStatus as $key => $row)
												{
													echo '<option value="'. $key .'">'. $row .'</option>';
												}
											?>
										</select>
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Blood Group <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<select class="form-control vRequired" data-search="true" id="formEditBloodGroup" name="formEditBloodGroup">
											<option value="">--Select--</option>
											<?php
												foreach ($bloodGroupPair as $key => $row)
												{
													echo '<option value="'. $key .'">'. $row .'</option>';
												}
											?>
										</select>
									</div>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Languages Known </label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<select multiple class="form-control" id="formEditLanguagesKnown" name="formEditLanguagesKnown">											
											<?php
												foreach ($language as $key => $row)
												{
													echo '<option value="'. $key .'">'. $row .'</option>';
												}
											?>
										</select>
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Nationality <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vEmpName onlyLetterSp" id="formEditNationality" name="formEditNationality" placeholder="Nationality" <?php echo $disableField; ?>>
									</div>
								</div>
							</div>
						</div>
						
						<div class="row panelOtherCountry">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Military Service</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<div class="form-group togglebutton" style="padding-left: 15px; padding-top: 10px;">
											<label>
											  <input type="checkbox" style="margin-top: 15px;" class="col-sm-9 md-checkbox" name="formEditMilitaryService" id="formEditMilitaryService" <?php echo $disableField; ?>>
											</label>
										</div>
									</div>
								</div>
							</div>
							<?php if($ethnicRace['Enable'] == 1) {
								$ethnicRaceRequire = $ethnicRace['Required'] == 1 ? 'form-control vLastName onlyLetterSp vRequired' : 'form-control vLastName onlyLetterSp';
								$ethnicRaceLbl = $ethnicRace['Required'] == 1 ? '<span class="short_explanation">*</span>' : '';
									
							?>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label"><?php echo $ethnicRace['Field_Name'].$ethnicRaceLbl;?></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="<?php echo $ethnicRaceRequire;?>" id="formEditEthnicRace" name="formEditEthnicRace" placeholder="<?php echo $ethnicRace['Field_Name'];?>" <?php echo $disableField; ?>>
									</div>
								</div>
							</div>
							<?php }  else { ?>
								<input type="hidden"  id="formEditEthnicRace" name="formEditEthnicRace" >
							<?php } ?>
						</div>						
						
						<div class="row panelOtherCountry">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Hobbies</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vHobbies alphaSpNlComma" id="formEditHobbies" name="formEditHobbies" placeholder="Hobbies" >
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Religion</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vLastName onlyLetterSp" id="formEditReligion" name="formEditReligion" placeholder="Religion" <?php echo $disableField; ?>>
									</div>
								</div>
							</div>
						</div>						
						
						<div class="row panelOtherCountry">
							<?php if($caste['Enable'] == 1) {
								$isCasteRequired = $caste['Required'] == 1 ? 'form-control vCaste onlyLetterSp vRequired' : 'form-control vCaste onlyLetterSp';
								$casteLbl = $caste['Required'] == 1 ? '<span class="short_explanation">*</span>' : '';									
							?>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label"><?php echo $caste['Field_Name'].$casteLbl;?></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="<?php echo $isCasteRequired;?>" id="formEditCaste" name="formEditCaste" placeholder="<?php echo $caste['Field_Name'];?>" <?php echo $disableField; ?>>
									</div>
								</div>
							</div>
							<?php } else { ?>
								<input type="hidden" id="formEditCaste" name="formEditCaste">
							<?php } ?>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Do you have a disability?</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<div class="form-group togglebutton" style="padding-left: 21px; padding-top: 10px;">
											<label>
											  <input type="checkbox" style="margin-top: 15px;" class="col-sm-9 md-checkbox" name="formEditPhysicallyChallenged" id="formEditPhysicallyChallenged" <?php echo $disableField; ?>>
											</label>
										</div>
									</div>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Manager</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<div class="form-group togglebutton" style="padding-left: 15px; padding-top: 10px;">
											<label>
											  <input type="checkbox" style="margin-top: 15px;" class="col-sm-9 md-checkbox" name="formEditIsManager" id="formEditIsManager" <?php echo $disableField; ?>>
											</label>
										</div>
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Personal Email</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vEmail" id="formEditPersonalEmail" name="formEditPersonalEmail" placeholder="Personal Email">
									</div>
								</div>
							</div>
						</div>

						<div class="row panelOtherCountry">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Smoker</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<div class="form-group togglebutton" style="padding-left: 21px; padding-top: 10px;">
											<label>
											  <input type="checkbox" style="margin-top: 15px;" class="col-sm-9 md-checkbox" name="formEditSmoker" id="formEditSmoker" <?php echo $disableField; ?>>
											</label>
										</div>
									</div>
								</div>
							</div>
							<div class="col-md-6 col-sm-6 panelSmokerAsOf">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Smoker As Of <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="date-picker form-control vSmokerAsOf datePickerRead" name="formEditSmokerAsOf" id="formEditSmokerAsOf" placeholder="Select a date..." <?php echo $disableField; ?>>										
									</div>
								</div>
							</div>
						</div>
						<!--Employee Dependent Grid-->
						<div class="col-md-12">
                            <h3>Dependent Details</h3>
                        </div>
                        
                        <div class="m-b-10" style="width: 98%">
                            <button type="button" class="btn btn-white btn-embossed toolbar-icons"  id="addDependent" title="Add"  data-target="#modalFormDependent" ><!--data-toggle="modal"-->
                                <i class="mdi-content-add"></i><span> Add</span>
                            </button>
                            
                            <button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="editDependent" title="Edit">
                                <i class="mdi-editor-mode-edit"></i><span> Edit</span>
                            </button>
                            
                            <button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="deleteDependent" title="Delete" >
                                <i class="mdi-action-delete"></i><span> Delete</span>
                            </button>
                        </div>
                        <div class="row" id="panelEditEmployeeDependent">
							<div class="col-md-12 col-sm-12 form-group">	
								<table class="table dataTable table-striped table-dynamic table-hover" id="tableEditEmployeeDependent">
									<thead>
										<tr>
											<th></th>
											<th id="employeeDependentName">Name</th>
											<th id="employeeDependentRelationship">Relationship</th>
											<th id="employeeDependentBirthDate">Birth Date</th>	
										</tr>
									</thead>
									<tbody>
									
									</tbody>
								</table>
							</div>
						</div>
						
                            <div class="col-md-12">
                                <div class="form-group togglebutton">
									<label class="col-md-5 col-sm-5 control-label" style="font-size: medium;padding-left: 0px;">Driving License Details</label>
									<label style="margin-top: 10px;">
										<input type="checkbox" class="col-md-7 md-checkbox" name="formEditIsLicense" id="formEditIsLicense">
									</label>
								</div>
							</div>
						
						<div class="row panelEditFormLicense">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label" id="lblDrivingLicenseNo">Driving License No <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control alphaNumSpHy vDrivingLicenseNo" id="formEditDrivingLicenseNo" name="formEditDrivingLicenseNo" placeholder="Driving License No">
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label" id="lblIssueDate">Issue Date <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="date-picker form-control vLicenseIssueDate datePickerRead" name="formEditIssueDate" id="formEditIssueDate" placeholder="Select a date...">										
									</div>
								</div>
							</div>
						</div>
						
						<div class="row panelEditFormLicense">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label" id="lblExpiryDate">Expiry Date <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="date-picker form-control vLicenseExpiryDate datePickerRead" name="formEditExpiryDate" id="formEditExpiryDate" placeholder="Select a date...">										
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label" id="lblIssuingAuthority">Issuing Authority <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control onlyLetterSp vLastName" id="formEditIssuingAuthority" name="formEditIssuingAuthority" placeholder="Issuing Authority">
									</div>
								</div>
							</div>
						</div>
						
						<div class="row panelEditFormLicense">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label" id="lblIssuingCountry">Issuing Country <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<select class="form-control" data-search="true" id="formEditIssuingCountry" name="formEditIssuingCountry">
											<option value="">--Select--</option>
											<?php
											foreach ($countries as $key => $row)
											{
												echo '<option value="'. $key .'">'. $row .'</option>';
											}
											?>
										</select>
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label" id="lblIssuingState">Issuing State <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control onlyLetterSp vIssuingName" id="formEditIssuingState" name="formEditIssuingState" placeholder="Issuing State">
									</div>
								</div>
							</div>
						</div>
						
						<div class="row panelEditFormLicense">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label" id="lblVehicleType">Vehicle Type <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control onlyLetterSpDotAmpBracket vIssuingName" id="formEditVehicleType" name="formEditVehicleType" placeholder="Vehicle Type">
									</div>
								</div>
							</div>
						</div>
						
                            <div class="col-md-12">
								<div class="form-group togglebutton">
									<label class="col-md-5 control-label" style="font-size: medium; padding-left: 0px;">Passport Details</label>
									<label style="margin-top: 10px;">
										<input type="checkbox" class="col-md-7 md-checkbox" name="formEditIsPassport" id="formEditIsPassport">
									</label>
								</div>
							</div>
						
						<div class="row panelEditFormPassport">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label" id="lblPassportNo">Passport No <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control onlyLetterNumber vIssuingName" id="formEditPassportNo" name="formEditPassportNo" placeholder="Passport No">
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label" id="lblPassportIssueDate">Issue Date <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="date-picker form-control vPassportIssueDate datePickerRead" name="formEditPassportIssueDate" id="formEditPassportIssueDate" placeholder="Select a date...">										
									</div>
								</div>
							</div>
						</div>
						
						<div class="row panelEditFormPassport">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label" id="lblPassportExpiryDate">Expiry Date <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="date-picker form-control vPassportExpireDate datePickerRead" name="formEditPassportExpiryDate" id="formEditPassportExpiryDate" placeholder="Select a date...">										
									</div>
								</div>
							</div>
						</div>

						<!-- Start Allow User Sign in -->

                            <div class="col-md-12">
								<div class="form-group togglebutton">
									<label class="col-md-5 control-label" style="font-size: medium; padding-left: 0px;">Allow User Sign In</label>
									<label style="margin-top: 10px;">
										<input type="checkbox" class="col-md-7 md-checkbox" name="formEditAllowUserSignIn" id="formEditAllowUserSignIn" <?php echo $disableField; ?>>
									</label>
								</div>
							</div>
						
						<div class="panelEditFormAllowUserSignInBtn hidden" >
							<button type="button" class="btn round-btn btn-secondary" id="formEditEmailButton" <?php echo $disableField; ?>>Email Address</button>
							<button 
								type="button" 
								title="OTP based signin is a premium feature and will be charged separately. 
Also please understand user can either use email address or mobile number to signin but not both." 
								class="btn btn-secondary-default round-btn" 
								id="formEditMobileButton" <?php echo $disableField; ?>
							>Mobile Number
							</button>						
						</div>
						<div class="row panelEditFormAllowUserSignin">
							<div class="col-lg-7 col-md-7 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label" id="lblAllowUserSignin">Work Email Address <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vEmail vCheckEmployeeMail" id="formEditWorkEmailAddress" name="formEditWorkEmailAddress" placeholder="Work Email Address" <?php echo $disableField; ?>>
									</div>
								</div>
							</div>
							<div class="col-lg-5 col-md-5 col-sm-12">
								<div class="form-group togglebutton">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label" id="lblAllowUserSignin">Send Email Invitation </label>
									<label style="margin-top: 10px;">
										<input type="checkbox" class="col-md-7 md-checkbox" name="formEditSendInvitation" id="formEditSendInvitation" <?php echo $disableField; ?>>
									</label>
								</div>
							</div>
						</div>

						<div class="row panelEditFormSignInMobileNumber hidden">
							<div class="col-lg-7 col-md-7 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label" id="lblAllowUserSignin">Mobile Number<span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input class="mobile-input-cls form-control formMobileNo vPhoneNum" autocomplete="off" type="tel" id="formEditSignInMobileNumber" name="formEditSignInMobileNumber" <?php echo $disableField; ?>>
									</div>
								</div>
							</div>
						</div>
                    </fieldset>
                    <fieldset  id="editJobInfo">
						<legend>Job Info</legend>
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Employee Type <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<select class="form-control vRequired" data-search="true" id="formJobEditEmployeeType" name="formJobEditEmployeeType" <?php echo $disableField; ?>>
											<option value="">--Select--</option>
											<?php
											foreach ($empType as $key => $row)
											{
												echo '<option value="'.$key.'">'. $row .'</option>';
											}
											?>
										</select>
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Designation <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<select class="form-control vRequired" data-search="true" id="formJobEditDesignationName" name="formJobEditDesignationName" <?php echo $disableField; ?>>
											<option value="">--Select--</option>
											<?php
											foreach ($designation as $key => $row)
											{
												echo '<option value="'. $key .'">'. $row .'</option>';
											}
											?>
										</select>
									</div>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Department <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<select class="form-control vRequired" data-search="true" id="formJobEditDepartmentName" name="formJobEditDepartmentName" <?php echo $disableField; ?>>
											<option value="">--Select--</option>
											<?php
											foreach ($deptHierarchy as $key => $row)
											{
												echo '<option value="'. $row['Department_Id'] .'">'. $row['Department_Name'] .'</option>';
												
												foreach($row['Child'] as $val =>$name)
												{
													
													if($name['Parent_Type_Id'] == $row['Department_Id'])
													{
														echo '<option value="'. $name['Department_Id'] .'">&nbsp;&nbsp;'. $name['Department_Name'] .'</option>';
														
														foreach($row['Child'] as $v =>$k)
														{
															if($k['Parent_Type_Id'] == $name['Department_Id'])
																echo '<option value="'. $k['Department_Id'] .'">&nbsp;&nbsp;&nbsp;&nbsp;'. $k['Department_Name'] .'</option>';
														}
														
													}
												}
											}
											?>
									</select>
									</div>
								</div>
							</div>
                            <div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Employee Mail</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vEmail vCheckEmployeeMail" id="formJobEditEmployeeMail" name="formJobEditEmployeeMail" placeholder="Employee Email" >
									</div>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Location <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<select class="form-control vRequired" data-search="true" id="formJobEditLocation" name="formJobEditLocation" <?php echo $disableField; ?>>
											<option value="">--Select--</option>
											<?php
											foreach ($empLocation as $key => $row)
											{
												echo '<option value="'. $key .'">'. $row .'</option>';
											}
											?>
										</select>
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Job Code</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vIssuingName alphaNumSpComma" name="formJobEditJobCode" id="formJobEditJobCode" placeholder="Job Code" <?php echo $disableField; ?>>
									</div>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Date Of Join <span class="short_explanation">*</span>
									<i class="icon-info employeeDOJInfoIcon" rel="popover" data-container="body" data-toggle="popover" data-placement="top" data-content="Updating Date Of Join is restricted as attendance/salary/leave/leave balance import record already exists for this employee">
									</i>
									</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="date-picker form-control vRequired vDateOfJoin datePickerRead" name="formJobEditDateOfJoin" id="formJobEditDateOfJoin" placeholder="Select a date..." <?php echo $disableField; ?>>										
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label" id="manager">Manager <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<select class="form-control" data-search="true" id="formJobEditManager" name="formJobEditManager" >
										</select>
									</div>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Commission Based Employee</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<div class="form-group togglebutton" style="padding-left: 15px; padding-top: 10px;">
											<label>
												<input type="checkbox" style="margin-top: 15px;" class="col-sm-9 md-checkbox" name="formJobEditCommissionBasedEmployee" id="formJobEditCommissionBasedEmployee" <?php echo $disableField; ?>>
											</label>
										</div>
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Work Schedule <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<select class="form-control vRequired" data-search="true" id="formJobEditWorkSchedule" name="formJobEditWorkSchedule" <?php echo $disableField; ?>>
											<option value="">--Select--</option>
											<?php
											foreach ($empWorkSchedule as $key => $row)
											{
												echo '<option value="'. $key .'">'. $row .'</option>';
											}
											?>
										</select>
									</div>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Employee Status
									<i class="icon-info" rel="popover" data-container="body" data-toggle="popover" data-placement="top"
									data-content="Employee(s) can be made inactive only through applying/initiating the resignation in the exit management form">
									</i></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<select class="form-control vRequired" data-search="true" id="formJobEditEmployeeStatus" name="formJobEditEmployeeStatus" <?php echo $disableField; ?>>
											<option value="Active" selected="selected">Active</option>
										</select>
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Attendance Enforced Payment
									<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
									data-placement="top" data-content="Enabling  this flag generates the  payslip  for this employee only if this employee added attendance,leave or the compoff details for all the working days">
									</i>
									</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<div class="form-group togglebutton" style="padding-left: 15px; padding-top: 10px;">
											<label>
												<input type="checkbox" style="margin-top: 15px;" class="col-sm-9 md-checkbox" name="formJobEditAttendanceEnforcement" id="formJobEditAttendanceEnforcement" <?php echo $disableField; ?>>
											</label>
										</div>
									</div>
								</div>
							</div>
						</div>
						
						<div class="row fieldEditInactive">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Employee Inactive Date <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="date-picker form-control vInactiveDate datePickerRead" disabled="true" name="formJobEditInactiveDate" id="formJobEditInactiveDate" placeholder="Select a date...">										
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Resignation Reason<span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<select class="form-control" disabled="true" data-search="true" id="formJobEditESICReason" name="formJobEditESICReason">
											<option value="">--Select--</option>
											<?php
											foreach ($empESICReason as $key => $row)
											{
												echo '<option value="'. $row['value'] .'">'. $row['text'] .'</option>';
											}
											?>
										</select>
									</div>
								</div>
							</div>
						</div>
						
						<div class="row">
						    <div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Employee Profession <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<select class="form-control vRequired" data-search="true" id="formJobEditEmployeeProfession" name="formJobEditEmployeeProfession" <?php echo $disableField; ?>>
											<!--<option value="">--Select--</option>-->
                                            <?php
											foreach ($empProfession as $key => $row)
											{
												echo '<option value="'.$key .'">'. $row .'</option>';
											}
											?>
                        				</select>
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12 height70 errorRemoveCls">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">TDS Exemption</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<div class="form-group togglebutton" style="padding-left: 15px; padding-top: 10px;">
											<label>
												<input type="checkbox" style="margin-top: 15px;" class="col-sm-9 md-checkbox" name="formJobEditTDSExemption" id="formJobEditTDSExemption" <?php echo $disableField; ?>>
											</label>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12 height70 errorRemoveCls">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Probation Date </label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="date-picker form-control vCheckProbationDate datePickerRead" name="formJobEditProbationDate" id="formJobEditProbationDate" placeholder="Select a date..." <?php echo $disableField; ?>>
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12 height70 errorRemoveCls">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Confirmed</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<div class="form-group togglebutton" style="padding-left: 15px; padding-top: 10px;">
											<label>
												<input type="checkbox" style="margin-top: 15px;" class="col-sm-9 md-checkbox" name="formJobEditConfirmed" id="formJobEditConfirmed" <?php echo $disableField; ?>>
											</label>
										</div>
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12 height70 errorRemoveCls fieldEditIsConfirmed">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Confirmation Date <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="date-picker form-control vConfirmationDate datePickerRead" name="formJobEditConfirmationDate" id="formJobEditConfirmationDate" placeholder="Select a date..." <?php echo $disableField; ?>>										
									</div>
								</div>
							</div>

							<div class="col-lg-6 col-md-6 col-sm-12 height70 errorRemoveCls">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Previous Experience</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<div class="col-lg-4 col-md-4 col-sm-4">
											<input type="number" class="form-control" min="1" max="99"  id="formPreviousEmployeeExperienceYears" name="Employee_Experience_Years" placeholder="Years" <?php echo $disableField; ?>>
										</div>
										<label class="col-lg-2 col-md-2 col-sm-2 control-label" style='padding:0px;'>Yrs</label>
										<div class="col-lg-4 col-md-4 col-sm-4">
											<input type="number" class="form-control" min="1" max="11"  id="formPreviousEmployeeExperienceMonths" name="Employee_Experience_Months" placeholder="Months" <?php echo $disableField; ?>>
										</div>
										<label class="col-lg-2 col-md-2 col-sm-2 control-label" style='padding:0px;'>Mths</label>
									</div>
								</div>
							</div>	

							<?php if ($orgDetails['Field_Force'] == 1) { ?>
										<div class="col-lg-6 col-md-6 col-sm-12 height70 errorRemoveCls">
											<div class="form-group">
												<label class="col-lg-5 col-md-5 col-sm-5 control-label">Service Provider <span class="short_explanation">*</span></label>
												<div class="col-lg-7 col-md-7 col-sm-7">
													<select class="form-control vRequired" data-search="true" id="formServiceProvider" name="formServiceProvider" <?php echo $disableField; ?>>
														<option value="">--Select--</option>
														<?php
														foreach ($serviceProvider as $key => $row)
														{
															echo '<option value="'. $key .'">'. $row .'</option>';
														}
														?>
													</select>												
												</div>
											</div>
										</div>
							<?php } ?>
						</div>
						
						<!--Employee Experience Details-->
						<div class="row" id="panelEditEmployeeExperience">
							<div class="col-md-12">
								<h3>Experience Details</h3>
							</div>
							<input type="hidden" name="Experience_Id" id="experienceId" />
							<div class="col-md-12 col-sm-12 form-group">
								 
								<div class="m-b-10" style="width: 94%">
									<?php if($hideButton=='false') { ?>
									<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="addExperience" title="Add" data-toggle="modal" data-target="#modalFormExperience" >
										<i class="mdi-content-add"></i><span> Add</span>
									</button>
									<?php }
									if(!empty($empUser['Admin'])){ ?>
									<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="editExperience" title="Edit">
										<i class="mdi-editor-mode-edit"></i><span> Edit</span>
									</button>
									
									<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="deleteExperience" title="Delete" >
										<i class="mdi-action-delete"></i><span> Delete</span>
									</button>
									<?php } ?>
								</div>
								 
								<table class="table dataTable table-striped table-dynamic table-hover" id="tableEditEmployeeExperience">
									<thead>
										<tr>
											<th></th>
											<th id="employeeExperienceCompanyName">Company Name</th>
											<th id="employeeExperienceLocation">Location</th>
											<th id="employeeExperienceDesignation">Designation</th>
											<th id="employeeExperienceFrom">From</th>
											<th id="employeeExperienceTo">To</th>
											<th id="employeeExperienceDuration">Duration (months)</th>
										</tr>
									</thead>
									<tbody>
									
									</tbody>
								</table>
							</div>
						</div>
						
						<!--Employee Asset Details-->
						<div class="row" id="panelEditEmployeeAsset">
							<div class="col-md-12">
								<h3>Asset Details</h3>
							</div>
							
							<div class="col-md-12 col-sm-12 form-group">
								<div class="m-b-10" style="width: 94%">
									<?php if($hideButton=='false') { ?>
									<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="addAsset" title="Add" data-toggle="modal" data-target="#modalFormAsset" >
										<i class="mdi-content-add"></i><span> Add</span>
									</button>
									<?php }
									if(!empty($empUser['Admin'])){ ?>
									<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="editAsset" title="Edit">
										<i class="mdi-editor-mode-edit"></i><span> Edit</span>
									</button>
									
									<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="deleteAsset" title="Delete" >
										<i class="mdi-action-delete"></i><span> Delete</span>
									</button>
									<?php } ?>
								</div>
								
							

								<table class="table dataTable table-striped table-dynamic table-hover" id="tableEditEmployeeAsset">
									<thead>
										<tr>
											<th></th>
											<th id="employeeAssetAsset">Asset</th>
											<th id="employeeAssetSerialNo">Serial No</th>
											<th id="employeeAssetReceiveDate">Receive Date</th>
											<th id="employeeAssetReturnDate">Return Date</th>
										</tr>
									</thead>
									<tbody>
									
									</tbody>
								</table>
							</div>
						</div>
                    </fieldset>
                    <fieldset  id="editContactInfo">
						<legend>Contact Info</legend>
						<div class="row">
							<h3 style="margin-left: 15px;">Permanent Address</h3>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Apartment Name</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control alphaNumSpCDotHySlash" minlength="1" maxlength="255" id="formContactEditPerApartmentName" name="formContactEditPerApartmentName" placeholder="Apartment Name" <?php echo $disableField; ?>>
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Street <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vRequired alphaNumSpCDotHySlash" minlength="1" maxlength="255" id="formContactEditPerStreet" name="formContactEditPerStreet" placeholder="Street Name" <?php echo $disableField; ?>>
									</div>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">City <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vEmpName onlyLetSpBrHyComma" id="formContactEditPerCity" name="formContactEditPerCity" placeholder="City Name" <?php echo $disableField; ?>>
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">State <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vEmpName onlyLetSpBrComma" id="formContactEditPerState" name="formContactEditPerState" placeholder="State Name" <?php echo $disableField; ?>>
									</div>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Country <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<select class="form-control vRequired" data-search="true" id="formContactEditPerCountry" name="formContactEditPerCountry" <?php echo $disableField; ?>>
											<option value="">--Select--</option>
											<?php
											foreach ($countries as $key => $row)
											{
												echo '<option value="'. $key .'">'. $row .'</option>';
											}
											?>
										</select>
									</div>
								</div>
							</div>
							<?php if($pincode['Enable'] == 1) {
								$require = $pincode['Required'] == 1 ? 'form-control vRequired alphaNumSpHy' : 'form-control alphaNumSpHy';
								$requireLbl = $pincode['Required'] == 1 ? '<span class="short_explanation">*</span>' : '';
									
							?>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label"><?php echo $pincode['Field_Name'].$requireLbl;?></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="<?php echo $require;?>" id="formContactEditPerZip" name="formContactEditPerZip" placeholder="<?php echo $pincode['Field_Name'];?>" <?php echo $disableField; ?>>
									</div>
								</div>
							</div>
							<?php } else { ?>
								<input type="hidden"  id="formContactEditPerZip" name="formContactEditPerZip" >
							<?php } ?>
						</div>
						
						<div class="row">
							<h3 style="margin-left: 15px;">Current Address</h3>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Same as Permanent Address</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<div class="form-group togglebutton" style="padding-left: 15px; padding-top: 10px;">
											<label>
												<input type="checkbox" style="margin-top: 15px;" class="col-sm-9 md-checkbox" name="formContactEditCurSameAddress" id="formContactEditCurSameAddress" >
											</label>
										</div>
									</div>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Apartment Name</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control alphaNumSpCDotHySlash" minlength="1" maxlength="255" id="formContactEditCurApartmentName" name="formContactEditCurApartmentName" placeholder="Apartment Name" >
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Street <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vRequired alphaNumSpCDotHySlash" minlength="1" maxlength="255" id="formContactEditCurStreet" name="formContactEditCurStreet" placeholder="Street Name" >
									</div>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">City <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vEmpName onlyLetSpBrHyComma" id="formContactEditCurCity" name="formContactEditCurCity" placeholder="City Name" >
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">State <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vEmpName onlyLetSpBrComma" id="formContactEditCurState" name="formContactEditCurState" placeholder="State Name" >
									</div>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Country <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<select class="form-control vRequired" data-search="true" id="formContactEditCurCountry" name="formContactEditCurCountry">
											<option value="">--Select--</option>
											<?php
											foreach ($countries as $key => $row)
											{
												echo '<option value="'. $key .'">'. $row .'</option>';
											}
											?>
										</select>
									</div>
								</div>
							</div>
							<?php if($pincode['Enable'] == 1) {
								$require = $pincode['Required'] == 1 ? 'form-control vRequired alphaNumSpHy' : 'form-control alphaNumSpHy';
								$requireLbl = $pincode['Required'] == 1 ? '<span class="short_explanation">*</span>' : '';
									
							?>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label"><?php echo $pincode['Field_Name'].$requireLbl;?></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="<?php echo $require;?>" id="formContactEditCurZip" name="formContactEditCurZip" placeholder="<?php echo $pincode['Field_Name'];?>" >
									</div>
								</div>
							</div>
							<?php } else { ?>
								<input type="hidden" id="formContactEditCurZip" name="formContactEditCurZip">
							<?php } ?>
						</div>
						
						<div class="row">
							<h3 style="margin-left: 15px;">Office Address</h3>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Same as Location Address</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<div class="form-group togglebutton" style="padding-left: 15px; padding-top: 10px;">
											<label>
												<input type="checkbox" style="margin-top: 15px;" class="col-sm-9 md-checkbox" name="formContactEditOffSameAddress" id="formContactEditOffSameAddress" <?php echo $disableField; ?>>
											</label>
										</div>
									</div>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Building Name <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vRequired alphaNumSpCDotHySlash" id="formContactEditOffApartmentName" name="formContactEditOffApartmentName" placeholder="Apartment Name" minlength="1" maxlength="255" <?php echo $disableField; ?>>
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Street <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vRequired alphaNumSpCDotHySlash" id="formContactEditOffStreet" name="formContactEditOffStreet" placeholder="Street Name"  minlength="1" maxlength="255" <?php echo $disableField; ?>>
									</div>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">City <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vEmpName onlyLetSpBrHyComma" id="formContactEditOffCity" name="formContactEditOffCity" placeholder="City Name" <?php echo $disableField; ?>>										
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">State <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vEmpName onlyLetSpBrComma" id="formContactEditOffState" name="formContactEditOffState" placeholder="State Name" <?php echo $disableField; ?>>										
									</div>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Country <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<select class="form-control vRequired" data-search="true" id="formContactEditOffCountry" name="formContactEditOffCountry" <?php echo $disableField; ?>>
											<option value="">--Select--</option>
											<?php
											foreach ($countries as $key => $row)
											{
												echo '<option value="'. $key .'">'. $row .'</option>';
											}
											?>
										</select>
									</div>
								</div>
							</div>
							<?php if($pincode['Enable'] == 1) {
								$require = $pincode['Required'] == 1 ? 'form-control vRequired alphaNumSpHy' : 'form-control alphaNumSpHy';
								$requireLbl = $pincode['Required'] == 1 ? '<span class="short_explanation">*</span>' : '';
									
							?>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label"><?php echo $pincode['Field_Name'].$requireLbl;?></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="<?php echo $require;?>" id="formContactEditOffZip" name="formContactEditOffZip" placeholder="<?php echo $pincode['Field_Name'];?>" <?php echo $disableField; ?>>
									</div>
								</div>
							</div>
							<?php } else { ?>
							<input type="hidden" id="formContactEditOffZip" name="formContactEditOffZip">
							<?php } ?>
						</div>
						
						<div class="row">
							<h3 style="margin-left: 15px;">Contact Information</h3>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Land Line No</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control  vPhoneNum" id="formContactEditLandlineNo" name="formContactEditLandlineNo">
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Mobile No <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
									<input type="text" class="form-control vRequired vPhoneNum" id="formContactEditMobileNo" name="formContactEditMobileNo">
									</div>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Work Number</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vPhoneNum" id="formContactEditWorkNo" name="formContactEditWorkNo" maxlength="30">
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Emergency Contact No</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vPhoneNum" id="formContactEditFaxNo" name="formContactEditFaxNo">
									</div>
								</div>
							</div>
						</div>
                    </fieldset>
                    <fieldset  id="editCareerInfo">
						<legend>Career Info</legend>
						
						<!--Employee Education Details-->
						<!--<div class="row" id="panelEditEmployeeEducation">
							<div class="col-md-12">
								<h3>Education Details</h3>
							</div>-->
                            
                        <div class="row" id="panelEditEmployeeEducation">
                            <div class="col-md-12">
								<div class="form-group togglebutton">
									<label class="col-md-5 control-label" style="font-size: medium; padding-left: 15px;">Education Details</label>
									<label style="margin-top: 10px;">
										<input type="checkbox" class="col-md-7 md-checkbox" name="formEditIsEducation" id="formEditIsEducation">
									</label>
								</div>
							</div>
						</div>
                            
							
                        <div class="row panelEditFormEducation">                                
							<div class="col-md-12 col-sm-12 form-group">
								<div class="m-b-10" style="width: 94%">
									<?php if($hideButton=='false') { ?>
									<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="addEducation" title="Add" data-toggle="modal" data-target="#modalFormEducation" >
										<i class="mdi-content-add"></i><span> Add</span>
									</button>
									<?php }
									if(!empty($empUser['Admin'])){ ?>
									<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="editEducation" title="Edit">
										<i class="mdi-editor-mode-edit"></i><span> Edit</span>
									</button>
									
									<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="deleteEducation" title="Delete" >
										<i class="mdi-action-delete"></i><span> Delete</span>
									</button>
									<?php } ?>
								</div>
							
								<table class="table dataTable table-striped table-dynamic table-hover" id="tableEditEmployeeEducation">
									<thead>
										<tr>
											<th></th>
											<th id="empEducationSchoolDiplomaDeg">School/Diploma/Deg</th>
											<th id="empEducationSpecialisation">Specialisation</th>
											<th id="empEducationInstituteName">Institute Name</th>
											<th id="empEducationUniversity">University</th>
											<th id="empEducationYearOfPassing">Year Of Passing</th>
											<th id="empEducationPercentage">Percentage</th>
											<th id="empEducationGrade">Grade</th>
										</tr>
									</thead>
									<tbody>
									
									</tbody>
								</table>
							</div>
						</div>
												
						<!--Employee Certificate Details-->
						<div class="row" id="panelEditEmployeeCertificate">
							<div class="col-md-12">
								<h3>Certification Details</h3>
							</div>
							
							<div class="col-md-12 col-sm-12 form-group">
								<div class="m-b-10" style="width: 94%">
									<?php if($hideButton=='false') { ?>
									<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="addCertificate" title="Add" data-toggle="modal" data-target="#modalFormCertificate" >
										<i class="mdi-content-add"></i><span> Add</span>
									</button>
									<?php }
									if(!empty($empUser['Admin'])){ ?>
									<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="editCertificate" title="Edit">
										<i class="mdi-editor-mode-edit"></i><span> Edit</span>
									</button>
									
									<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="deleteCertificate" title="Delete" >
										<i class="mdi-action-delete"></i><span> Delete</span>
									</button>
									<?php } ?>
								</div>
							
								<table class="table dataTable table-striped table-dynamic table-hover" id="tableEditEmployeeCertificate">
									<thead>
										<tr>
											<th></th>
											<th id="empCertificateName">Certificate Name</th>
											<th id="empCertificateReceivedOn">Received On</th>
											<th id="empCertificateReceivedFrom">Received From</th>
										</tr>
									</thead>
									<tbody>
									
									</tbody>
								</table>
							</div>
						</div>
						
						<!--Employee Training Details-->
						<div class="row" id="panelEditEmployeeTraining">
							<div class="col-md-12">
								<h3>Training Details</h3>
							</div>
							
							<div class="col-md-12 col-sm-12 form-group">
								<div class="m-b-10" style="width: 94%">
									<?php if($hideButton=='false') { ?>
									<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="addTraining" title="Add" data-toggle="modal" data-target="#modalFormTraining" >
										<i class="mdi-content-add"></i><span> Add</span>
									</button>
									<?php }
									if(!empty($empUser['Admin'])){ ?>
									<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="editTraining" title="Edit">
										<i class="mdi-editor-mode-edit"></i><span> Edit</span>
									</button>
									
									<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="deleteTraining" title="Delete" >
										<i class="mdi-action-delete"></i><span> Delete</span>
									</button>
									<?php } ?>
								</div>
							
								<table class="table dataTable table-striped table-dynamic table-hover" id="tableEditEmployeeTraining">
									<thead>
										<tr>
											<th></th>
											<th id="empTrainingName">Training Name</th>
											<th id="empTrainingStartDate">Start Date</th>
											<th id="empTrainingEndDate">End Date</th>
											<th id="empTrainingDuration">Duration (Months)</th>
											<th id="empTrainingTrainer">Trainer</th>
											<th id="empTrainingCentre">Centre</th>
										</tr>
									</thead>
									<tbody>
									
									</tbody>
								</table>
							</div>
						</div>
						
						<!--Employee Award Details-->
						<div class="row" id="panelEditEmployeeAward">
							<div class="col-md-12">
								<h3>Award Details</h3>
							</div>
							
							<div class="col-md-12 col-sm-12 form-group">
								<div class="m-b-10" style="width: 94%">
									<?php if($hideButton=='false') { ?>
									<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="addAward" title="Add" data-toggle="modal" data-target="#modalFormAward" >
										<i class="mdi-content-add"></i><span> Add</span>
									</button>
									<?php }
									if(!empty($empUser['Admin'])){ ?>
									<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="editAward" title="Edit">
										<i class="mdi-editor-mode-edit"></i><span> Edit</span>
									</button>
									
									<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="deleteAward" title="Delete" >
										<i class="mdi-action-delete"></i><span> Delete</span>
									</button>
									<?php } ?>
								</div>
								
								<table class="table dataTable table-striped table-dynamic table-hover" id="tableEditEmployeeAward">
									<thead>
										<tr>
											<th></th>
											<th id="empAwardName">Award Name</th>
											<th id="empAwardReceivedOn">Received On</th>
											<th id="empAwardReceivedFrom">Received From</th>
											<th id="empAwardReceivedFor">Received For</th>
										</tr>
									</thead>
									<tbody>
									
									</tbody>
								</table>
							</div>
						</div>
						
						<div class="row">
							<h3 style="margin-left: 15px;">Skill Details</h3>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Primary Skill</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vSkill" id="formCareerEditPrimarySkill" name="formCareerEditPrimarySkill" placeholder="Primary Skills">
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Secondary Skill</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vSkill" id="formCareerEditSecondarySkill" name="formCareerEditSecondarySkill" placeholder="Secondary Skills" >
									</div>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Known Skills</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vSkill" id="formCareerEditKnownSkills" name="formCareerEditKnownSkills" placeholder="Known Skills" >
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Hands On</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vSkill" id="formCareerEditHandsOn" name="formCareerEditHandsOn" placeholder="Hands On Skills" >
									</div>
								</div>
							</div>
						</div>
                    </fieldset>
					<fieldset  id="editBankInfo">
						<legend>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Other Details&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</legend>
						<!-- <div class="row">
							<div class="col-md-12">
								<div class="form-group togglebutton">
									<label class="col-md-5 control-label" style="font-size: medium; padding-left: 0px;">Bank Account Details </label>
									<label style="margin-top: 10px;">
										<input type="checkbox" class="col-md-7 md-checkbox" name="formBankAccEditIsBankAccount" id="formBankAccEditIsBankAccount">
									</label>
								</div>
							</div>
						</div> -->
<!-- 						
						<div class="row panelEditFormBankAccount">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Account Number <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vAccountNumber onlyLetterNumber" id="formBankAccEditAccountNumber" name="formBankAccEditAccountNumber" placeholder="Account Number" >
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Bank Name <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vIssuingName onlyLetterSp" id="formBankAccEditBankName" name="formBankAccEditBankName" placeholder="Bank Name" >
									</div>
								</div>
							</div>
						</div>
						
						<div class="row panelEditFormBankAccount">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Branch Name <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control alphaNumSpDot" minlength="1" maxlength="50" id="formBankAccEditBranchName" name="formBankAccEditBranchName" placeholder="Branch Name" >
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Street <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control alphaNumSpCDotHy" id="formBankAccEditStreet" name="formBankAccEditStreet" placeholder="Street Name" minlength="1" maxlength="100">
									</div>
								</div>
							</div>
						</div>
						
						<div class="row panelEditFormBankAccount">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">City <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control onlyLetterSp" minlength="1" maxlength="50" id="formBankAccEditCity" name="formBankAccEditCity" placeholder="City Name" >
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">State <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control onlyLetterSp" minlength="1" maxlength="50" id="formBankAccEditState" name="formBankAccEditState" placeholder="State Name" >
									</div>
								</div>
							</div>
						</div>
						
						<div class="row panelEditFormBankAccount">
							<?php //if($pincode['Enable'] == 1) {
								//$require = $pincode['Required'] == 1 ? 'form-control alphaNumSpHy vRequired' : 'form-control alphaNumSpHy';
								//$requireLbl = $pincode['Required'] == 1 ? '<span class="short_explanation">*</span>' : '';
									
							?>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label"><?php echo $pincode['Field_Name'].$requireLbl;?></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="<?php //echo $require;?>" id="formBankAccEditPincode" name="formBankAccEditPincode" placeholder="<?php echo $pincode['Field_Name'];?>" >
									</div>
								</div>
							</div>
							<?php //} else { ?>
							<input type="hidden" id="formBankAccEditPincode" name="formBankAccEditPincode">
							<?php //} ?>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">Account Type <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<select class="form-control vRequired" data-search="true" id="formBankAccEditAccountType" name="formBankAccEditAccountType" >
											<option value="">--Select--</option>
											<?php
											// foreach ($empBankAccType as $key => $row)
											// {
											// 	echo '<option value="'. $key .'">'. $row .'</option>';
											// }
											?>
										</select>
									</div>
								</div>
							</div>
						</div>
						
						<div class="row panelEditFormBankAccount">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">IFSC Code <span class="short_explanation">*</span></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control vIssuingName onlyLetterNumber" id="formBankAccEditIFSCCode" name="formBankAccEditIFSCCode" placeholder="IFSC Code" >
									</div>
								</div>
							</div>
						</div>
						 -->
						<!--Employee Insurance Details-->
						<div class="row" id="panelEditEmployeeInsurance">
							<div class="col-md-12">
								<h3>Insurance Details</h3>
							</div>
							
							<div class="col-md-12 col-sm-12 form-group">
							
							<?php if(!empty($empUser['Admin'])){ ?>
								<div class="m-b-10">
									<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="addInsurance" title="Add" data-toggle="modal" data-target="#modalFormInsurance" >
										<i class="mdi-content-add"></i><span> Add</span>
									</button>
									
									<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="editInsurance" title="Edit">
										<i class="mdi-editor-mode-edit"></i><span> Edit</span>
									</button>
									
									<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="deleteInsurance" title="Delete" >
										<i class="mdi-action-delete"></i><span> Delete</span>
									</button>
								</div>

							<?php } ?>

								<table class="table dataTable table-striped table-dynamic table-hover" id="tableEditEmployeeInsurance">
									<thead>
										<tr>
											<th id="empEmployeeInsurance">Insurance</th>
											<th id="empEmployeePolicyNo">Policy No</th>
										</tr>
									</thead>
									<tbody>
									
									</tbody>
								</table>
							</div>
						</div>
						
						<input type="hidden" name="panelPANVisible" id="panelPANVisible" />
						<input type="hidden" name="panelProvidentFundVisible" id="panelProvidentFundVisible" />
						
						<div class="row panelOtherCountry">
							<?php if($aadhaarNo['Enable'] == 1) { ?>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<h3 style="margin-left: 15px;">Identity Details</h3>
								</div>
							</div>
							<?php } if($pan['Enable'] == 1) { ?>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<h3>Taxable Details</h3>
								</div>
							</div>
							<?php } ?>
						</div>
						<div class="row panelOtherCountry">
							<?php
							if($aadhaarNo['Enable'] == 1)
							{
							    $require = ($aadhaarNo['Required'] == 1 ? "form-control aadharNo vRequired": "form-control aadharNo");
							    $requireLbl = ($aadhaarNo['Required'] == 1 ? '<span class="short_explanation">*</span>': '');
                            ?>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label"><?php echo $aadhaarNo['Field_Name'].$requireLbl;?></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="<?php echo $require;?>" maxlength="25" id="formEditAadhaarNumber" name="formEditAadhaarNumber" placeholder="<?php echo $aadhaarNo['Field_Name'];?>" <?php echo $disableField; ?>>
									</div>
								</div>
							</div>
							<?php }  else { ?>
							<input type="hidden" id="formEditAadhaarNumber" name="formEditAadhaarNumber">
							<?php
                            }
                            
                            if($pan['Enable'] == 1)
							{
								$require = ($pan['Required'] == 1 ? "form-control aadharNo vRequired": "form-control aadharNo");
							    $requireLbl = ($pan['Required'] == 1 ? '<span class="short_explanation">*</span>': '');
							?>
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label"><?php echo $pan['Field_Name'].$requireLbl;?></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="<?php echo $require;?>" maxlength="25" id="formBankEditPAN" name="formBankEditPAN" placeholder="<?php echo $pan['Field_Name'];?>" <?php echo $disableField; ?>>
									</div>
								</div>
							</div>
							<?php } else { ?>
								<input type="hidden" id="formBankEditPAN" name="formBankEditPAN">
							<?php } ?>
						</div>
						
						<div class="row panelOtherCountry">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<h3 style="margin-left: 15px;">Provident Fund Details</h3>
								</div>
							</div>
						</div>
						<div class="row panelOtherCountry">
							<div class="col-lg-6 col-md-6 col-sm-12">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label">UAN</label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="form-control aadharNo" id="formEditUANNumber" maxlength="25" name="formEditUANNumber" placeholder="Universal Account Number"  <?php echo $disableField; ?>>
									</div>
								</div>
							</div>
							<?php if($pfNumber['Enable'] == 1) {
								$require = ($pfNumber['Required'] == 1 ? "form-control alphaNumHypSlash vRequired": "form-control alphaNumHypSlash");
							    $requireLbl = ($pfNumber['Required'] == 1 ? '<span class="short_explanation">*</span>': '');	
							?>							
							<div class="col-lg-6 col-md-6 col-sm-12 panelProvidentFund">
								<div class="form-group">
									<label class="col-lg-5 col-md-5 col-sm-5 control-label"><?php echo $pfNumber['Field_Name'].$requireLbl;?></label>
									<div class="col-lg-7 col-md-7 col-sm-7">
										<input type="text" class="<?php echo $require;?>" minlength="3" maxlength="30" id="formBankEditProvidentFund" name="formBankEditProvidentFund" placeholder="<?php echo $pfNumber['Field_Name'];?>" <?php echo $disableField; ?>>
									</div>
								</div>
							</div>
							<?php }  else { ?>
								<input type="hidden" id="formBankEditProvidentFund" name="formBankEditProvidentFund">
							<?php } ?>
						</div>
						
						<div class="row">
							<noscript>
								<input class="nocsript-finish-btn sf-right nocsript-sf-btn" type="submit" value="finish"/>
							</noscript>
						</div>
					</fieldset>
					
					<button type="reset" class="cancel" id="resetEmployee" style="display: none;" >Reset</button>
                </form>
				<?php } ?>
				
			</div>
			<div class="modal-footer text-center" id="employeeDraftButtonPanel">
				
				<?php if ($empUser['Update'] /*|| $empSelfUser['Update']*/) { ?>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="editFormDraftEmployee" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Draft
				</button>
				<?php } ?>
				
			</div>
		</div>
	</div>
</div>

<?php if ($empUser['Update'] /*|| $empSelfUser['Update']*/) { ?>

<!--
	Employee Dependent
-->

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="context-menu11" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="editContextDependent"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<li><a tabindex="-1" id="deleteContextDependent"><i class="mdi-action-delete"></i> Delete</a></li>
	</ul>
</div>

<!-- Form Employee Dependent -->
<div class="modal fade" id="modalFormDependent" aria-hidden="true">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;">
					<i class="mdi-hardware-keyboard-backspace" id="backEmployeeDependent"></i>
				</button>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="editFormDependent" method="POST" action="">
					<input type="hidden" name="Dependent_Id" id="Dependent_Id" />
					
					<div class="form-group">
						<label class="col-md-4 control-label">First Name <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="form-control onlyLetterSp vTaxRules" id="formEditDependentFirstName" name="formEditDependentFirstName" placeholder="Dependent First Name">
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">Last Name</label>
						<div class="col-md-8">
							<input type="text" class="form-control onlyLetterSp" minlength="1" maxlength="30" id="formEditDependentLastName" name="formEditDependentLastName" placeholder="Dependent Last Name">
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">Relationship <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<select class="form-control vRequired" data-search="true" name="formEditDependentRelationship" id="formEditDependentRelationship">
								<option value="">-- Select --</option>
							</select>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">Birth Date <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="date-picker form-control vDependentDOB vRequired datePickerRead" name="formEditDependentDOB" id="formEditDependentDOB" placeholder="Select a date...">							
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">Age <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="form-control vRequired" id="formEditDependentAge" name="formEditDependentAge" placeholder="Age" readonly="readonly">
						</div>
					</div>
					
					<button type="reset" class="cancel" id="resetDependent" style="display: none;" >Reset</button>
				</form>
			</div>
			<div class="modal-footer text-center">
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="editFormResetDependent" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="editFormSubmitDependent" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
			</div>
		</div>
	</div>
</div>

<!-- modal Dependent close confirmation -->
<div class="modal fade" id="modalDirtyDependent" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditDependantConfEmployee"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditDependantConfEmployee">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editDirtyCloseDependent">Yes</button>
			</div>
		</div>
	</div>
</div>

<!-- Modal Employee dependent delete confirmation -->
<div class="modal fade" id="modalDeleteDependent" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteDependantConfEmployee"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteDependantConfEmployee">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="modalDeleteConfirmDependent">Yes</button>
			</div>
		</div>
	</div>
</div>

<!--
	Employee Experience
-->

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="context-menu22" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="editContextExperience"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<li><a tabindex="-1" id="deleteContextExperience"><i class="mdi-action-delete"></i> Delete</a></li>
	</ul>
</div>

<!-- Form Employee Experience -->
<div class="modal fade" id="modalFormExperience" aria-hidden="true">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;">
					<i class="mdi-hardware-keyboard-backspace" id="backEmployeeExperience"></i>
				</button>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="editFormExperience" method="POST" action="">
					<input type="hidden" name="Experience_Id" id="Experience_Id" />
					
					<div class="form-group">
						<label class="col-md-4 control-label">Company Name <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="form-control alSpDotAndHypen vName" id="formEditExperienceCompanyName" name="formEditExperienceCompanyName" placeholder="Company Name">
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">Location <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="form-control vName onlyLetterSp" id="formEditExperienceLocation" name="formEditExperienceLocation" placeholder="Location Name">
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">Designation <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="form-control onlyAlnumHypAndSp vName" id="formEditExperienceDesignation" name="formEditExperienceDesignation" placeholder="Designation">
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">From <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="date-picker form-control vExperienceStartDate datePickerRead" name="formEditExperienceFrom" id="formEditExperienceFrom" placeholder="Select a date...">							
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">To <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="date-picker form-control vExperienceEndDate datePickerRead" name="formEditExperienceTo" id="formEditExperienceTo" placeholder="Select a date...">							
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">Duration (months)</label>
						<div class="col-md-8">
							<input type="text" class="form-control" id="formEditExperienceDuration" name="formEditExperienceDuration" readonly="readonly" placeholder="Duration">
						</div>
					</div>
					
					<div class="form-group">
						<div class="fileinput fileinput-new" data-provides="fileinput">
							<p class="col-md-4"><strong>Documents<br></strong>
							<span class="short_explanation"></span> Allowed File types :  (.png,.jpg,.jpeg,.doc,.txt,.pdf,.docx,.csv,.xls,.xlsx,.tif,.tiff,.bmp)<br>
							<span class="short_explanation"></span> Max File Size : 3MB
							</p>
							
							<div class="col-md-8">
								<div class="file" id="buttonEmployeesDocumentFile">												
									<button type="submit" class="btn btn-secondary" id="uploadEmployeesDocument" style="bottom: 5px;">
										<i class="fa fa-paperclip attachfiles"></i> Add Documents
									</button>
									<input type="file" class="custom-file " name="uploadEmployeesDocumentFiles" id="uploadEmployeesDocumentFiles" multiple=multiple>													
								</div>
								
								<div id="EmployeesDocumentAttachmentsList" style="font-size:15px;margin-top: 10px"> </div>                                               
							</div>                                   
						</div>
					</div>
					
					<button type="reset" class="cancel" id="resetExperience" style="display: none;" ></button>
				</form>
			</div>
			<div class="modal-footer text-center">
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="editFormResetExperience" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="editFormSubmitExperience" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
			</div>
		</div>
	</div>
</div>

<!-- modal Experience close confirmation -->
<div class="modal fade" id="modalDirtyExperience" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditExperienceConfEmployee"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditExperienceConfEmployee">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editDirtyCloseExperience">Yes</button>
			</div>
		</div>
	</div>
</div>

<!-- Modal Employee dependent delete confirmation -->
<div class="modal fade" id="modalDeleteExperience" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteExperienceConfEmployee"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteExperienceConfEmployee">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="modalDeleteConfirmExperience">Yes</button>
			</div>
		</div>
	</div>
</div>

<!--
	Employee Asset
-->

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="context-menu23" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="editContextAsset"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<li><a tabindex="-1" id="deleteContextAsset"><i class="mdi-action-delete"></i> Delete</a></li>
	</ul>
</div>

<!-- Form Employee Asset -->
<div class="modal fade" id="modalFormAsset" aria-hidden="true">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;">
					<i class="mdi-hardware-keyboard-backspace" id="backEmployeeAsset"></i>
				</button>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="editFormAsset" method="POST" action="">
					<input type="hidden" name="Asset_Id" id="Asset_Id" />
					
					<div class="form-group">
						<label class="col-md-4 control-label">Asset <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="form-control vRequired alNumSpDotHypen vTaxRules" id="formEditAssetEmployeeAsset" name="formEditAssetEmployeeAsset" placeholder="Asset">
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">Serial No <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="form-control alphaNum vRequired" minlength="2" maxlength="25" id="formEditAssetSerialNo" name="formEditAssetSerialNo" placeholder="Serial No">
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">Receive Date <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="date-picker form-control vRequired vAssetReceiveDate datePickerRead" name="formEditAssetReceiveDate" id="formEditAssetReceiveDate" placeholder="Select a date...">							
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">Return Date</label>
						<div class="col-md-8">
							<input type="text" class="date-picker form-control vAssetReturnDate orgDateFormat datePickerRead" name="formEditAssetReturnDate" id="formEditAssetReturnDate" placeholder="Select a date..." <?php echo $disableField; ?>>							
						</div>
					</div>
					
					<button type="reset" class="cancel" id="resetAsset" style="display: none;" ></button>
				</form>
			</div>
			<div class="modal-footer text-center">
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="editFormResetAsset" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="editFormSubmitAsset" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
			</div>
		</div>
	</div>
</div>

<!-- modal Asset close confirmation -->
<div class="modal fade" id="modalDirtyAsset" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditAssetConfEmployee"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditAssetConfEmployee">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editDirtyCloseAsset">Yes</button>
			</div>
		</div>
	</div>
</div>

<!-- Modal Employee dependent delete confirmation -->
<div class="modal fade" id="modalDeleteAsset" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteAssetConfEmployee"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteAssetConfEmployee">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="modalDeleteConfirmAsset">Yes</button>
			</div>
		</div>
	</div>
</div>


<!--
	Employee Education
-->

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="context-menu41" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="editContextEducation"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<li><a tabindex="-1" id="deleteContextEducation"><i class="mdi-action-delete"></i> Delete</a></li>
	</ul>
</div>

<!-- Form Employee Education -->
<div class="modal fade" id="modalFormEducation" aria-hidden="true">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;">
					<i class="mdi-hardware-keyboard-backspace" id="backEmployeeEducation"></i>
				</button>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="editFormEducation" method="POST" action="">
					<input type="hidden" name="Education_Id" id="Education_Id" />
					
					<div class="form-group">
						<label class="col-md-4 control-label">School/Diploma/Deg <span class="short_explanation">*</span></label>
						<div class="col-md-8">							
							<select class="form-control vRequired" data-search="true" id="formEditEducationEmployeeEducation" name="formEditEducationEmployeeEducation" >
								<option value="">-- Select --</option>
								<?php
								foreach ($coursesList as $key => $row)
								{
									echo '<option value="'.$key.'">'.$row.'</option>';
								}
								?>
							</select>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">Specialisation </label>
						<div class="col-md-8">
							<input type="text" class="form-control vSpecialisation" minlength="2" maxlength="30" id="formEditEducationEmployeeSpecialisation" name="formEditEducationEmployeeSpecialisation" placeholder="Specialisation">
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">Institute Name </label>
						<div class="col-md-8">
							<input type="text" class="form-control onlyLetterSpDot" id="formEditEducationEmployeeInstituteName" name="formEditEducationEmployeeInstituteName" minlength="3" maxlength="50"  placeholder="Institute Name">
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">University </label>
						<div class="col-md-8">
							<input type="text" class="form-control onlyLetterSpDot" name="formEditEducationUniversity" minlength="2" maxlength="50"  id="formEditEducationUniversity" placeholder="University">
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">Year Of Passing <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="number" class="form-control vRequired vYearPassing" min="1900" maxlength="4" max="<?php echo date("Y");  ?>"  id="formEditEducationYearOfPassing" name="formEditEducationYearOfPassing" placeholder="Year Of Passing">
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">Percentage (%)</label>
						<div class="col-md-8">
							<input type="number" class="form-control" id="formEditEducationPercentage" max="100" name="formEditEducationPercentage" placeholder="Percentage">
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">Grade </label>
						<div class="col-md-8">
							<input type="text" class="form-control alphaNum" name="formEditEducationGrade" maxlength="10" id="formEditEducationGrade" placeholder="Grade">
						</div>
					</div>
					
					<button type="reset" class="cancel" id="resetEducation" style="display: none;" ></button>
				</form>
			</div>
			<div class="modal-footer text-center">
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="editFormResetEducation" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="editFormSubmitEducation" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
			</div>
		</div>
	</div>
</div>

<!-- modal Education close confirmation -->
<div class="modal fade" id="modalDirtyEducation" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditEducationConfEmployee"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditEducationConfEmployee">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editDirtyCloseEducation">Yes</button>
			</div>
		</div>
	</div>
</div>

<!-- Modal Employee certificate delete confirmation -->
<div class="modal fade" id="modalDeleteEducation" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteEducationConfEmployee"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteEducationConfEmployee">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="modalDeleteConfirmEducation">Yes</button>
			</div>
		</div>
	</div>
</div>

<!--
	Employee Certificate
-->

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="context-menu42" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="editContextCertificate"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<li><a tabindex="-1" id="deleteContextCertificate"><i class="mdi-action-delete"></i> Delete</a></li>
	</ul>
</div>

<!-- Form Employee Certificate -->
<div class="modal fade" id="modalFormCertificate" aria-hidden="true">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;">
					<i class="mdi-hardware-keyboard-backspace" id="backEmployeeCertificate"></i>
				</button>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="editFormCertificate" method="POST" action="">
					<input type="hidden" name="Certification_Id" id="Certification_Id" />
					
					<div class="form-group">
						<label class="col-md-4 control-label">Certificate Name <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="form-control vCertification vRequired" minlength="1" maxlength="30" id="formEditCertificateEmployeeCertificate" name="formEditCertificateEmployeeCertificate" placeholder="Certificate Name">
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">Received On <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="date-picker form-control vRequired vReceivedOn datePickerRead" name="formEditCertificateReceivedOn" id="formEditCertificateReceivedOn" placeholder="Select a date...">							
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">Received From <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="form-control onlyLetterSpDot vRequired" minlength="1" maxlength="30" name="formEditCertificateReceivedFrom" id="formEditCertificateReceivedFrom" placeholder="Received From">
						</div>
					</div>
					
					<button type="reset" class="cancel" id="resetCertificate" style="display: none;" ></button>
				</form>
			</div>
			<div class="modal-footer text-center">
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="editFormResetCertificate" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="editFormSubmitCertificate" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
			</div>
		</div>
	</div>
</div>

<!-- modal Certificate close confirmation -->
<div class="modal fade" id="modalDirtyCertificate" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditCertificateConfEmployee"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditCertificateConfEmployee">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editDirtyCloseCertificate">Yes</button>
			</div>
		</div>
	</div>
</div>

<!-- Modal Employee certificate delete confirmation -->
<div class="modal fade" id="modalDeleteCertificate" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteCertificateConfEmployee"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteCertificateConfEmployee">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="modalDeleteConfirmCertificate">Yes</button>
			</div>
		</div>
	</div>
</div>

<!--
	Employee Training
-->

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="context-menu43" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="editContextTraining"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<li><a tabindex="-1" id="deleteContextTraining"><i class="mdi-action-delete"></i> Delete</a></li>
	</ul>
</div>

<!-- Form Employee Training -->
<div class="modal fade" id="modalFormTraining" aria-hidden="true">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;">
					<i class="mdi-hardware-keyboard-backspace" id="backEmployeeTraining"></i>
				</button>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="editFormTraining" method="POST" action="">
					<input type="hidden" name="Training_Id" id="Training_Id" />
					
					<div class="form-group">
						<label class="col-md-4 control-label">Training Name <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="form-control vTaxRules alphaNumSpHy" id="formEditTrainingEmployeeTraining" name="formEditTrainingEmployeeTraining" placeholder="Training Name">
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">Start Date <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="date-picker form-control vTrainingStartDate datePickerRead vRequired" name="formEditTrainingStartDate" id="formEditTrainingStartDate" placeholder="Select a date...">							
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">End Date <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="date-picker form-control vTrainingEndDate datePickerRead vRequired" name="formEditTrainingEndDate" id="formEditTrainingEndDate" placeholder="Select a date...">
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">Duration(months) <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="form-control" readonly="readonly" name="formEditTrainingDuration" id="formEditTrainingDuration" placeholder="Duration">
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">Trainer <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="form-control vTaxRules alphaNumSpHy" name="formEditTrainingTrainer" id="formEditTrainingTrainer" placeholder="Trainer">
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">Centre <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="form-control vTaxRules alphaNumSpHy vRequired" name="formEditTrainingCentre" id="formEditTrainingCentre" placeholder="Centre">
						</div>
					</div>
					
					<button type="reset" class="cancel" id="resetTraining" style="display: none;" ></button>
				</form>
			</div>
			<div class="modal-footer text-center">
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="editFormResetTraining" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="editFormSubmitTraining" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
			</div>
		</div>
	</div>
</div>

<!-- modal Training close confirmation -->
<div class="modal fade" id="modalDirtyTraining" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditTrainingDetailsConfEmployee"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditTrainingDetailsConfEmployee">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editDirtyCloseTraining">Yes</button>
			</div>
		</div>
	</div>
</div>

<!-- Modal Employee training delete confirmation -->
<div class="modal fade" id="modalDeleteTraining" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteTrainingDetailsConfEmployee"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteTrainingDetailsConfEmployee">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="modalDeleteConfirmTraining">Yes</button>
			</div>
		</div>
	</div>
</div>

<!--
	Employee Award
-->

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="context-menu44" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="editContextAward"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<li><a tabindex="-1" id="deleteContextAward"><i class="mdi-action-delete"></i> Delete</a></li>
	</ul>
</div>

<!-- Form Employee Award -->
<div class="modal fade" id="modalFormAward" aria-hidden="true">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;">
					<i class="mdi-hardware-keyboard-backspace" id="backEmployeeAwards"></i>
				</button>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="editFormAward" method="POST" action="">
					<input type="hidden" name="Award_Id" id="Award_Id" />
					
					<div class="form-group">
						<label class="col-md-4 control-label">Award Name <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="form-control vTaxRules alphaNumSpHy" id="formEditAwardEmployeeAward" name="formEditAwardEmployeeAward" placeholder="Award Name">
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">Received On <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="date-picker form-control vRequired vAwardReceivedOn datePickerRead" name="formEditAwardReceivedOn" id="formEditAwardReceivedOn" placeholder="Select a date...">							
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">Received From <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="form-control onlyLetterSpDot vRequired" minlength="3" maxlength="30" name="formEditAwardReceivedFrom" id="formEditAwardReceivedFrom" placeholder="Received From">
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-4 control-label">Received For <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="form-control onlyLetterSpDot vRequired" minlength="3" maxlength="30" name="formEditAwardReceivedFor" id="formEditAwardReceivedFor" placeholder="Received For">
						</div>
					</div>
					
					<button type="reset" class="cancel" id="resetAward" style="display: none;" ></button>
				</form>
			</div>
			<div class="modal-footer text-center">
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="editFormResetAward" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="editFormSubmitAward" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
			</div>
		</div>
	</div>
</div>

<!-- modal Award close confirmation -->
<div class="modal fade" id="modalDirtyAward" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditAwardDetailsConfEmployee"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditAwardDetailsConfEmployee">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editDirtyCloseAward">Yes</button>
			</div>
		</div>
	</div>
</div>

<!-- Modal Employee dependent delete confirmation -->
<div class="modal fade" id="modalDeleteAward" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteAwardDetailsConfEmployee"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteAwardDetailsConfEmployee">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="modalDeleteConfirmAward">Yes</button>
			</div>
		</div>
	</div>
</div>


<?php if ($empUser['Admin'] == 'admin') { ?>
	<div class="modal fade" id="formCloneRolesEmployee" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace" id="backEmployeeCloneRole"></i>
				</button>
				<h4 class="modal-title">Clone Roles</h4>
			</div>
			<div class="modal-body">
				
				<!--Clone Roles Form-->
				<form role="form" class="form-horizontal form-validation" id="CloneRolesformEmployee" method="POST" action="">
					<input type="hidden" name="form_CloneRolesEmployee" id="form_CloneRolesEmployee" />
					<div class="row">
						<div class="form-group">
							<label class="col-md-4 control-label">Employee <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select multiple="multiple" class="form-control vRequired selectAlll" data-search="true" id="formEmployeeCloneRoles" name="Employee" >
									<option value="selectAll">--Select all--</option>
									<option value="clearAll">--Clear all--</option>
								</select>
							</div>
						</div>
					
					</div>
				</form>
				
			</div>
			<div class="modal-footer text-center" id="formActionCloneRolesEmployee">
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetCloneRolesEmployee" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitCloneRolesEmployee" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
				
			</div>
		</div>
	</div>
</div>
<div class="modal fade" id="modalDirtyCloneRolesEmployee" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeCloneRoleConfEmployee"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noCloneRoleConfEmployee">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseCloneRolesFormEmployee">Yes</button>
			</div>
		</div>
	</div>
</div>
<?php } ?>

<?php if ($empUser['Admin'] == 'admin') { ?>
	<div class="modal fade" id="modalFormDepartment" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace" id="backEmployeeDepartment"></i>
				</button>
				<h4 class="modal-title">Department Level Access</h4>
			</div>
			<div class="modal-body">
				
				<!--Clone Roles Form-->
				<form role="form" class="form-horizontal form-validation" id="departmentEmployee" method="POST" action="">
					<input type="hidden" name="modalFormDepartment" id="form_DepartmentEmployee" />
					<div class="row">
						<div class="form-group">
							<label class="col-md-4 control-label">Admin For<span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select multiple="multiple" class="form-control vRequired selectAlll" data-search="true" id="formDepartmentlevel" name="Department" >
									<option value="selectAll">--Select all--</option>
									<option value="clearAll">--Clear all--</option>
									<?php
					               foreach ($deptParent as $key => $row)
					               {
						           echo '<option value="'.$key.'">'.$row.'</option>';
					               }
					               ?>
								</select>
							</div>
						</div>
					
					</div>
				</form>
				
			</div>
			<div class="modal-footer text-center" id="formActionDepartmentLevelAccess">
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetDepartmentLevelAccess" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitDepartmentLevelAccess" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
				
			</div>
		</div>
	</div>
</div>
<div class="modal fade" id="ismodalDirtyDepartmentLevelAccess" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeptLevelAccessConfEmployee"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeptLevelAccessConfEmployee">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editDepartmentLevelFormEmployee">Yes</button>
			</div>
		</div>
	</div>
</div>
<?php } ?>



<!--
	Employee Clone
-->
<?php if ($empUser['Update']) { ?>

<div class="modal fade" id="modalFormClone" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace" id="backCloneEmployee"></i>
				</button>
				<h4 class="modal-title">Clone Employee</h4>
			</div>
			<div class="modal-body">
				
				<!--Clone Employee Form-->
				<form role="form" class="form-horizontal form-validation" id="CloneEmployeeform" method="POST" action="">
					<input type="hidden" name="form_Clone" id="formCloneEmployee" />
					<div class="row">
						
						<div class="form-group">
							<label class="col-md-4 control-label">Employee Name</label>
							<div class="col-md-8">
								<p id="EmployeeName">-</p>
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Date of Birth</label>
							<div class="col-md-8">
								<p id="DateofBirth">-</p>
							</div>
						</div>
					<!--User Defined Employee Id-->
						<div class="form-group">
							<label class="col-md-4 control-label">Employee Id<span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<input type="text" class="form-control alphaNumSpCDotHySlash vRequired" id="cloneUserDefinedEmployeeId" placeholder="Employee Id" minlength="1"	maxlength="50">
							</div>
						</div>
						<div class="form-group">
							<label class="col-md-4 control-label">Biometric Integration Id</label>
							<div class="col-md-8">
								<input type="text" class="form-control alphaNum" name="cloneBiometricIntegrationId" id="cloneBiometricIntegrationId" placeholder="Biometric Integration Id" minlength="1" maxlength="15">
								<div class="biometricIntegrationIdMessage"><b style="color: red">Please enter the biometric integration id for biometric data processing.</b></div>
							</div>
						</div>
					<!--Add Date of joining-->
						<div class="form-group">
							<label class="col-md-4 control-label">Date Of Join<span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<input type="text" class="date-picker form-control vRequired vDateOfJoin datePickerRead" name="DateOfJoin" id="date_of_joining" placeholder="Select a date...">										
							</div>
						</div>

						<?php if ($orgDetails['Field_Force'] == 1) { ?>
						<div class="form-group">
							<label class="col-lg-4 col-md-4 col-sm-4 control-label">Service Provider <span class="short_explanation">*</span></label>
							<div class="col-lg-8 col-md-8 col-sm-8">
								<select class="form-control vRequired" data-search="true" id="cloneServiceProvider" name="cloneServiceProvider">
									<option value="">--Select--</option>
									<?php
									foreach ($serviceProvider as $key => $row)
									{
										echo '<option value="'. $key .'">'. $row .'</option>';
									}
									?>
								</select>												
							</div>
						</div>
						<?php } ?>
						<div class="form-group">
							<label class="col-lg-4 col-md-4 col-sm-4 control-label">Department <span class="short_explanation">*</span></label>
							<div class="col-lg-8 col-md-8 col-sm-8">
								<select class="form-control vRequired" data-search="true" id="cloneDepartment" name="cloneDepartment" >
									<option value="">--Select--</option>
									<?php
									foreach ($deptHierarchy as $key => $row)
									{
										echo '<option value="'. $row['Department_Id'] .'">'. $row['Department_Name'] .'</option>';
										
										foreach($row['Child'] as $val =>$name)
										{
											
											if($name['Parent_Type_Id'] == $row['Department_Id'])
											{
												echo '<option value="'. $name['Department_Id'] .'">&nbsp;&nbsp;'. $name['Department_Name'] .'</option>';
												
												foreach($row['Child'] as $v =>$k)
												{
													if($k['Parent_Type_Id'] == $name['Department_Id'])
														echo '<option value="'. $k['Department_Id'] .'">&nbsp;&nbsp;&nbsp;&nbsp;'. $k['Department_Name'] .'</option>';
												}
												
											}
										}
									}
									?>
								</select>
							</div>
						</div>

						<div class="form-group">
							<label class="col-lg-4 col-md-4 col-sm-4 control-label">Designation <span class="short_explanation">*</span></label>
							<div class="col-lg-8 col-md-8 col-sm-8">
								<select class="form-control vRequired" data-search="true" id="cloneDesignation" name="cloneDesignation">
									<option value="">--Select--</option>
									<?php
									foreach ($designation as $key => $row)
									{
										echo '<option value="'. $key .'">'. $row .'</option>';
									}
									?>
								</select>										
							</div>
						</div>

						<div class="form-group">
							<label class="col-lg-4 col-md-4 col-sm-4 control-label">Employee Type <span class="short_explanation">*</span></label>
							<div class="col-lg-8 col-md-8 col-sm-8">
								<select class="form-control vRequired" data-search="true" id="cloneEmployeeType" name="cloneEmployeeType" >
									<option value="">--Select--</option>
									<?php
									foreach ($empType as $key => $row)
									{
										echo '<option value="'.$key.'">'. $row .'</option>';
									}
									?>
								</select>						
							</div>
						</div>

						<div class="form-group">
							<label class="col-lg-4 col-md-4 col-sm-4 control-label">Location <span class="short_explanation">*</span></label>
							<div class="col-lg-8 col-md-8 col-sm-8">
								<select class="form-control vRequired" data-search="true" id="cloneLocation" name="cloneLocation" >
									<option value="">--Select--</option>
									<?php
									foreach ($empLocation as $key => $row)
									{
										echo '<option value="'. $key .'">'. $row .'</option>';
									}
									?>
								</select>											
							</div>
						</div>

						<div class="form-group">
							<label class="col-lg-4 col-md-4 col-sm-4 control-label">Work Schedule <span class="short_explanation">*</span></label>
							<div class="col-lg-8 col-md-8 col-sm-8">
								<select class="form-control vRequired" data-search="true" id="cloneWorkSchedule" name="cloneWorkSchedule" >
									<option value="">--Select--</option>
									<?php
									foreach ($empWorkSchedule as $key => $row)
									{
										echo '<option value="'. $key .'">'. $row .'</option>';
									}
									?>
								</select>										
							</div>
						</div>
					</div>
				</form>
				
			</div>
			<div class="modal-footer text-center" id="formActionCloneEmployee">
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetCloneEmployee" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitCloneEmployee" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
				
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="modalDirtyCloneEmployee" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeCloneEmployeeConfDesignation"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noCloneEmployeeConfDesignation">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseCloneEmployeeForm">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } ?>

<!--
	Employee Insurance
-->

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="context-menu51" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="editContextInsurance"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<li><a tabindex="-1" id="deleteContextInsurance"><i class="mdi-action-delete"></i> Delete</a></li>
	</ul>
</div>

<!-- Form Employee Insurance -->
<div class="modal fade" id="modalFormInsurance" aria-hidden="true">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;">
					<i class="mdi-hardware-keyboard-backspace" id="backEmployeeInsurance"></i>
				</button>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="editFormInsurance" method="POST" action="">
					<input type="hidden" name="Policy_Id" id="Policy_Id" />
					
					<div class="form-group" id="listInsuranceType">
						<label class="col-md-4 control-label">Insurance Type<span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<select class="notranslate form-control vRequired" data-search="true" id="formEditInsuranceEmployeeInsuranceType" name="formEditInsuranceEmployeeInsuranceType" >
								<option value="1">Insurance</option>
								<option value="2">Fixed Health Insurance</option>
							</select>
						</div>
					</div>
					<div class="form-group" id="displayDivInsuranceEmployeeInsuranceType">
							<div class="col-md-4"><label class="control-label">Insurance Type</label></div>
							<div class="col-md-8"><p id="displayInsuranceEmployeeInsuranceType"></p></div>
					</div>
					<div class="form-group"id="listInsurance">
						<label class="col-md-4 control-label">Insurance <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<select class="form-control vRequired" data-search="true" id="formEditInsuranceEmployeeInsurance" name="formEditInsuranceEmployeeInsurance" >
								<option value="">-- Select --</option>
							</select>
						</div>
					</div>
					<div class="form-group" id="displayDivInsuranceEmployeeInsurance">
							<div class="col-md-4"><label class="control-label">Insurance</label></div>
							<div class="col-md-8"><p id="displayInsuranceEmployeeInsurance"></p></div>
							<input type="hidden" name="Policy_Id" id="InsuranceId" />
					</div>
					<div class="form-group">
						<label class="col-md-4 control-label">Policy No <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<input type="text" class="form-control vRequired vPolicyNo" id="formEditInsurancePolicyNo" name="formEditInsurancePolicyNo" placeholder="Policy No">
						</div>
					</div>
					
					<button type="reset" class="cancel" id="resetInsurance" style="display: none;" ></button>
				</form>
			</div>
			<div class="modal-footer text-center">
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="editFormResetInsurance" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="editFormSubmitInsurance" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
			</div>
		</div>
	</div>
</div>

<!-- modal Insurance close confirmation -->
<div class="modal fade" id="modalDirtyInsurance" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditInsuranceDetailsConfEmployee"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditInsuranceDetailsConfEmployee">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editDirtyCloseInsurance">Yes</button>
			</div>
		</div>
	</div>
</div>

<!-- Modal Employee dependent delete confirmation -->
<div class="modal fade" id="modalDeleteInsurance" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteInsuranceDetailsConfEmployee"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteInsuranceDetailsConfEmployee">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="modalDeleteConfirmInsurance">Yes</button>
			</div>
		</div>
	</div>
</div>
<?php } ?>

<!-- Change Password Modal -->
<div class="modal fade" id="modalFormChangePassword" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace" id="backEmployeeChangePassword"></i>
				</button>
				
				<h4 class="modal-title">Change Password</h4>
			</div>
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="empEditFormChangePassword" method="POST" action="">
					<input type="hidden" name="formEditEmployeeId" id="formEditEmployeeId" />
					
					<div class="form-group">
						<label class="col-md-3 control-label">User Name</label>
						<div class="col-md-9">
							<input type="text" class="form-control" id="formEditUserName" name="formEditUserName" readonly="readonly" placeholder="User Name">
						</div>
					</div>
				<?php if($empUser['Admin']=='admin')
				{?>
				<input type="hidden" id="formEditCurrentPassword" value="">
				<?php }
				else
				{ ?>
					<div class="form-group" id="currentPassword">
							<label class="col-md-3 control-label">Current Password <span class="short_explanation">*</span></label>
							<div class="col-md-9">
								<input type="password" class="form-control vRequired vPassword" id="formEditCurrentPassword" name="formEditCurrentPassword" placeholder="Current Password">
							</div>
					</div>
				<?php } ?>
					<div class="form-group">
						<label class="col-md-3 control-label">New Password <span class="short_explanation">*</span></label>
						<div class="col-md-9">
							<input type="password" class="form-control vRequired vPassword" id="formEditNewPassword" name="formEditNewPassword" placeholder="New Password">
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-md-3 control-label">Confirm Password <span class="short_explanation">*</span></label>
						<div class="col-md-9">
							<input type="password" class="form-control vRequired" id="formEditConfirmPassword" name="formEditConfirmPassword" placeholder="Confirm Password" disabled="true">
						</div>
					</div>
				</form>
			</div>
			<div class="modal-footer text-center">
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="editFormResetChangePassowrd" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="editFormSubmitChangePassowrd" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
			</div>
		</div>
	</div>
</div>

<!-- Dirty modal for employee change password modal -->
<div class="modal fade" id="modalDirtyChangePassword" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditConfirmPasswordConfEmployee"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfirmPasswordConfEmployee">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editFormCloseConfirmChangePassword">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php if ($empUser['Update'] /*|| $empSelfUser['Update']*/) { ?>
<div class="modal fade" id="modalDirtyEmployee" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditConfirmEmployeeConfEmployee"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfirmEmployeeConfEmployee">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editFormCloseConfirmEmployee">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } if ($empUser['Delete']==1 /*|| ($empSelfUser['Delete'] && $this->formStatus == 0)*/) { ?>

<!-- Delete COnfirmation Modal -->
<div class="modal fade" id="modalDeleteEmployee" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteConfEmployee"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfEmployee">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConformEmployee">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } }
$sty='';
$statusFlag=0;
/* Mask will be set if the dependency form datasetup is Incomplete */
for($i=0;$i<count($datasetupForms);$i++)
{
	if($datasetupForms[$i]['Table_Name'] == "designation" && $datasetupForms[$i]['Form_Status'] == "Open")
	{
		// To check whether all the previous datasetup prerequisites are completed 
		// If completed then the mask will be removed
		for($l=0;$l<$i;$l++)
		{
			if($datasetupForms[$l]['Form_Status'] == 'Open' && $datasetupForms[$l]['Is_Required'] == 1)
			{
				$statusFlag=1;
			}
		}
		if($statusFlag==1)
		{
			$sty = "pointer-events: none; opacity: 0.5;";
		}
		else{
			$sty = '';
		}
	}
}

if ($designationAccess['View'] && ((!empty($customFormNameB) && array_key_exists("Enable",$customFormNameB) && $customFormNameB['Enable'] == 1) || empty($customFormNameB)))  {

?>
<div id="checkPanelEmployeeDesignation"></div>
<!-- Employee Designation Grid Panel -->
<div class="col-md-12 portlets add-panel-padding old-emp-panel hidden">
	<div class="panel" id="gridPanelEmployeeDesignation" style="<?php echo $sty;?>">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong id="lblFormNameB"><?php echo ((!empty($customFormNameB) && !empty($customFormNameB['New_Form_Name'])) ? $customFormNameB['New_Form_Name'] : $this->formNameB)?></strong></h3>
		</div>
		<div class="panel-content">
			<!--Employee Designation Grid Toolbar Icons-->
		
				<div class="m-b-10">
					
					<?php if ($designationAccess['Add']) { ?>
					<!--Add Employee Designation Button-->
					<button type="button" class="btn btn-white btn-embossed toolbar-icons" title="Add" data-toggle="modal" data-target="#modalFormEmployeeDesignation" id="addEmployeeDesignation">
						<i class="mdi-content-add"></i><span class="hidden-xs hidden-sm"> Add</span>
					</button>
					<?php } ?>
					
					<!--View Employee Designation Button-->
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="viewEmployeeDesignation" title="View">
						<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
					</button>
					<?php
					//if(!empty($empUser['Admin']) && ($empUser['Admin']=='admin' || $empSelfUser['Admin']=='admin'))
					if($rolesUser['Op_Choice']==1)
					{ ?>
					
					<button type="button" class="btn btn-white btn-embossed toolbar-icons" title="Role"  id="roleDesignation">
					<i class="fa fa-key"></i><span class="hidden-xs hidden-sm"> Role</span>
					</button>
					<?php
					} ?>
					<?php if ($designationAccess['Update']) { ?>
					<!--Edit Employee Designation Button-->
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="Edit" id="editEmployeeDesignation">
						<i class="mdi-editor-mode-edit"></i><span class="hidden-xs hidden-sm"> Edit</span>
					</button>
					<?php } if ($designationAccess['Delete']) { ?>
					<!--Delete Employee Designation Button-->
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="Delete" id="deleteEmployeeDesignation">
						<i class="mdi-action-delete"></i><span class="hidden-xs hidden-sm"> Delete</span>
					</button>
					<?php }
					 if($rolesUser['Op_Choice']==1){?>
					<button type="button" class="btn btn-white btn-embossed toolbar-icons disabled btn-off" title="Clone Roles"  id="cloneRoles">
						<i class="hr-clone-roles"></i><span class="hidden-xs hidden-sm"> Clone Roles</span>
					</button>
					<?php } ?>
					<!--Filter Employee Designation Button-->
					<a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterEmployeeDesignation">
						<i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm"> Filter</span>
					</a>
				</div>

			
			<!-- Employee Designation Grid Table -->
			<table class="table dataTable table-striped table-dynamic table-hover" id="tableEmployeeDesignation">
				<thead>
					<tr>
						<th></th>
						<th id="designationDesignation">Designation</th>
						<th id="designationGrade">Grade</th>
						<th id="designationProbationDays">Probation Days</th>
						<th id="designationEmployeeConfirmation">Employee Confirmation</th>
						<th id="designationAttendanceEnforcemetPayment">Attendance Enforced Payment</th>
						<th id="designationAttendanceEnforcemetGeoLocation">Attendance Enforced GeoLocation</th> 
						<th id="designationNoticePeriodPayByEmployer">Notice Period Pay By Employer</th>
						<th id="designationNoticePeriodPayByEmployee">Notice Period Pay By Employee</th> 
						<th id="designationStatus">Status</th> 

					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
		</div>
	</div>
</div>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="context-menu1" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextEmployeeDesignation"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($designationAccess['Update']) { ?>
		<li><a tabindex="-1" id="editContextEmployeeDesignation"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<?php } if($rolesUser['Op_Choice']==1){ ?>
		<li><a tabindex="-1" id="roleContextEmployeeDesignation"><i class="fa fa-key" style="font-size: 19px;margin-left: 4px;"></i> Role</a></li>
		<li><a tabindex="-1" id="CloneRolesContextEmployeeDesignation"><i class="hr-clone-roles"></i> Clone Roles</a></li>
		<?php } if ($designationAccess['Delete']) { ?>
		<li><a tabindex="-1" id="deleteContextEmployeeDesignation"><i class="mdi-action-delete"></i> Delete</a></li>
		<?php } ?>
	</ul>
</div>

<!-- Employee Designation Filter Panel -->
<div class="builder" id="filterPanelEmployeeDesignation">
	<div id="closeFilterEmployeeDesignation"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel" style="width: 100%;" id="filterResetEmployeeDesignation">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="filterApplyEmployeeDesignation">Apply</button>
			
			<div class="form-group">
				<label>Designation</label>
				<input type="text" class="form-control" id="filterDesignationName" placeholder="Designation">
			</div>
			
			<div class="form-group">
				<label>Grade</label>
				<select class="form-control" data-search="true" id="filterGrade" >
					<option value="">All</option>
					<?php
					foreach ($grades as $key => $row)
					{
						echo '<option value="'.$key.'">'.$row.'</option>';
					}
					?>
				</select>
			</div>
			
			<div class="form-group">
				<label>Probation Days</label>
				<div class="input-group">
					<input type="number" class="form-control" name="ProbationDaysStart" id="filterProbationDaysStart" min="0" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="form-control" name="ProbationDaysEnd" id="filterProbationDaysEnd" min="0" placeholder="End"/>
				</div>
			</div>
			<div class="form-group">
				<label>Employee Confirmation</label>
				<select class="form-control" data-search="true" id="filterEmployeeConfirmation" >
					<option value="">All</option>
					<option value="Automatic">Automatic</option>
					<option value="Manual">Manual</option>
				</select>
			</div>
			<div class="form-group">
				<label>Attendance Enforced Payment</label>
				<select class="form-control" data-search="true" id="filterAttendanceEnforce" >
					<option value="">All</option>
					<option value="1">Yes</option>
					<option value="2">No</option>
				</select>
			</div>
			<div class="form-group">
				<label>Attendance Enforced GeoLocation</label>
				<select class="form-control" data-search="true" id="filterGeoEnforce" >
					<option value="">All</option>
					<option value="1">Yes</option>
					<option value="2">No</option>
				</select>
			</div>
			<div class="form-group">
				<label>Notice Period Pay By Employer</label>
				<select class="form-control" data-search="true" id="filterNoticePeriodPayByEmployer" >
					<option value="">All</option>
					<option value="Yes">Yes</option>
					<option value="No">No</option>
				</select>
			</div>
			<div class="form-group">
				<label>Notice Period Pay By Employee</label>
				<select class="form-control" data-search="true" id="filterNoticePeriodPayByEmployee" >
					<option value="">All</option>
					<option value="Yes">Yes</option>
					<option value="No">No</option>
				</select>
			</div>			
			<div class="form-group">
				<label>Status</label>
				<select class="form-control" data-search="true" id="filterDesignationStatus" >
					<option value="">All</option>
					<option value="Active">Active</option>
					<option value="InActive">InActive</option>
				</select>
			</div>
			
		</div>
	</div>
</div>

<!-- Modal for Add Form, View Form, Edit Form -->
<div class="modal fade" id="modalFormEmployeeDesignation" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true"><i class="mdi-hardware-keyboard-backspace" id="backDesignation"></i></button>
				
				<?php if ($designationAccess['Update']) { ?>
				<button type="button" class="close form-icons" aria-hidden="true" id="editInViewEmployeeDesignation">
					<i class="mdi-editor-mode-edit"></i>
				</button>
				<?php } ?>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<!--View Employee Designation Form-->
				<form role="form" id="viewFormEmployeeDesignation" >
					<div class="row">
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Designation</label></div>
							<div class="col-md-7"><p id="viewEmpDesignationName"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Grade</label></div>
							<div class="col-md-7"><p id="viewGrade"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Probation Days</label></div>
							<div class="col-md-7"><p id="viewProbationDays"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Employee Confirmation</label></div>
							<div class="col-md-7"><p id="viewEmployeeConfirmation"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Attendance Enforce Payment</label></div>
							<div class="col-md-7"><p id="viewAttendanceEnforce"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Attendance Enforce GeoLocation</label></div>
							<div class="col-md-7"><p id="viewAttendanceGeoLocation"></p></div>
						</div> 
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Notice Period Days Within Probation</label></div>
							<div class="col-md-7"><p id="viewNoticePeriodDaysWithinProbation"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Notice Period Days After Probation</label></div>
							<div class="col-md-7"><p id="viewNoticePeriodDaysAfterProbation"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Notice Period Pay By Employer</label></div>
							<div class="col-md-7"><p id="viewNoticePeriodPayByEmployer"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Notice Period Pay By Employee</label></div>
							<div class="col-md-7"><p id="viewNoticePeriodPayByEmployee"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Status</label></div>
							<div class="col-md-7"><p id="viewDesignationStatus"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Description</label></div>
							<div class="col-md-7"><p id="viewEmployeeDesignationDescription"></p></div>
						</div>
					</div>
					
					<div class="row additionalInfoPanel">
						<hr class="view-hr"/>
						
						<div class="form-group" style="font-size: large;margin-left: 13px;">
							<label class="control-label text-center">Additional Information</label>
						</div>
						
						<div class="form-group additionalInformation">
							<div class="col-md-5"><label class="control-label">Added On</label></div>
							<div class="col-md-7"><p id="addedOnEmployeeDesignation"></p></div>
						</div>
						
						<div class="form-group additionalInformation">
							<div class="col-md-5"><label class="control-label">Added By</label></div>
							<div class="col-md-7"><p id="addedByEmployeeDesignation"></p></div>
						</div>
						
						<div class="form-group additionalInformation updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated On</label></div>
							<div class="col-md-7"><p id="updatedOnEmployeeDesignation"></p></div>
						</div>
						
						<div class="form-group additionalInformation updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated By</label></div>
							<div class="col-md-7"><p id="updatedByEmployeeDesignation"></p></div>
						</div>
					</div>
				</form>
				
				<?php if ($designationAccess['Add'] == 1 || $designationAccess['Update'] == 1) { ?>
				
				<!--Add/Edit Employee designation Form-->
				<form role="form" class="form-horizontal form-validation" id="editFormEmployeeDesignation" method="POST" action="">
					<input type="hidden" name="Designation_Id" id="formDesignationId" />
					
					<div class="row">
						
						<!--Start Employee Designation Field Set-->
						<div class="form-group">
							<label class="col-md-3 control-label">Designation <span class="short_explanation">*</span></label>
							<div class="col-md-9">
								<input type="text" class="form-control vDesignation" id="formDesignationName" name="Designation_Name" placeholder="Designation">
							</div>
                        </div>
						<!--End Employee Designation Field Set-->
						
						<!--Start Grade Field Set-->
						<div class="form-group">
							<label class="col-md-3 control-label">Grade <span class="short_explanation">*</span></label>
							<div class="col-md-9">
								<select class="form-control vRequired" data-search="true" id="formGrade" name="Grade_Id" >
									<option value="">-- Select --</option>
									<?php
									foreach ($grades as $key => $row)
									{
										echo '<option value="'.$key.'">'.$row.'</option>';
									}
									?>
								</select>
							</div>
						</div>
						<!--End Grade Field Set-->
						
						<!--Start Probation Days Field Set-->
						<div class="form-group">
							<label class="col-md-3 control-label">Probation Days <span class="short_explanation">*</span></label>
							<div class="col-md-9">																
								<input type="number" class="form-control vRequired" id="formProbationDays" name="formProbationDays" placeholder="Probation Days" min=0 max="2000">
							</div>
						</div>
						<!--End Probation Days Field Set-->
						
						<!--Start Employee Confirmation Field Set-->
						<div class="form-group">
							<label class="col-md-3 control-label">Employee Confirmation <span class="short_explanation">*</span></label>
							<div class="col-md-9">
								<select class="form-control vRequired" data-search="true" id="formEmployeeConfirmation" name="Employee_Confirmation" >
									<option value="">-- Select --</option>
									<option value="Automatic">Automatic</option>
									<option value="Manual">Manual</option>
								</select>
							</div>
						</div>
						<!--End Employee Confirmation Field Set-->
						
						<div class="form-group">
							<label class="col-md-3 control-label">Attendance Enforce Payment
							<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
                            data-placement="top" data-content="Enabling  this flag generates the  payslip  for the employees only if they have  added attendance,leave or the compoff details for all the working days">
                                                                      
							</i>
							</label>
							<div class="col-md-9 togglebutton togglebutton-material-blue" style="margin-top: 15px;">
								<label>
										<input type="checkbox" class="md-checkbox" id="formAttendanceEnforce">
								</label>
							</div>
						</div>
					
						<!-- Enforce GeoLocation -->
						<div class="form-group">
							<label class="col-md-3 control-label">Attendance Enforce GeoLocation
							<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
                            data-placement="top" data-content="Enable this flag if employees under this designation add the attendance only when the GPS is turned on.">
							</i>
							</label>
							<div class="col-md-9 togglebutton togglebutton-material-blue" style="margin-top: 15px;">
								<label>
										<input type="checkbox" class="md-checkbox" id="formAttendanceGeoLocation">
								</label>
							</div>
						</div>

						<!-- Geo location Enforce alert -->
						<div style="margin: 15px; display: none;" id="geoLocationEnforceAlert">
							<div id="title-preview" class="preview dis-none active" style="display: block;">
								<div class="alert media fade in alert-danger">
									<div style="font-size: 8px; margin-bottom: 10px;">
										<i class="icons-office-52 pull-right" id="hideGeoLocationEnforceAlert"></i>
									</div>
									<p>Ensure employees are using the mobile app to check-in or checkout, 
									as the geo-coordinates captured in the web/mobile browsers might not be accurate 
									due to the WiFi network, proxy servers or VPN. You can categories the source of attendance data 
									in the attendance report to validate the accuracy. It is advised that you request employees to 
									log their attendance with a mobile app to capture accurate geo-coordinates. </p>
								</div>
							</div>
						  </div>
						<!--Start Notice Period Pay By Employer-->
						<div class="form-group">
							<label class="col-md-3 control-label">Notice Period Pay By Employer
							</label>
							<div class="col-md-9 togglebutton togglebutton-material-blue" style="margin-top: 15px;">
								<label>
										<input type="checkbox" class="md-checkbox" id="formNoticePeriodPayByEmployer">
								</label>
							</div>
						</div>
						<!--End Notice Period Pay By Employer-->
						<!--Start Notice Period Pay By Employee-->
						<div class="form-group">
							<label class="col-md-3 control-label">Notice Period Pay By Employee
							</label>
							<div class="col-md-9 togglebutton togglebutton-material-blue" style="margin-top: 15px;">
								<label>
										<input type="checkbox" class="md-checkbox" id="formNoticePeriodPayByEmployee">
								</label>
							</div>
						</div>
						<!--End Notice Period Pay By Employee-->  
						<!--Start Notice Period Days Within Probation-->
						<div class="form-group">
							<label class="col-md-3 control-label">Notice Period Days Within Probation <span class="short_explanation">*</span></label>
							<div class="col-md-9">																
								<input type="number" class="form-control vRequired" id="formNoticePeriodDaysWithinProbation" name="formNoticePeriodDaysWithinProbation" placeholder="Notice Period Days Within Probation" min=0 max="183">
							</div>
						</div>
						<!--End Notice Period Days Within Probation -->
						<!--Start Notice Period Days After Probation-->
						<div class="form-group">
							<label class="col-md-3 control-label">Notice Period Days After Probation <span class="short_explanation">*</span></label>
							<div class="col-md-9">																
								<input type="number" class="form-control vRequired" id="formNoticePeriodDaysAfterProbation" name="formNoticePeriodDaysAfterProbation" placeholder="Notice Period Days After Probation" min=0 max="183">
							</div>
						</div>
						<!--End Notice Period Days After Probation-->
						<!--Start Employee Designation Status Field Set-->
						<div class="form-group">
							<label class="col-md-3 control-label">Status <span class="short_explanation">*</span></label>
							<div class="col-md-9">
								<select class="form-control vRequired" data-search="true" id="formDesignationStatus" name="formDesignationStatus">
								</select>
							</div>
                        </div>
						<!--End Employee Designation Status Field Set-->
						<!--Start Employee Designation Description Field Set-->
						<div class="form-group">
							<label class="col-md-3 control-label">Description </label>
							<div class="col-md-9">
								<textarea name="description" id="formEmployeeDesignationDescription" rows="5" class="form-control vComments" placeholder="Write your Description..."></textarea>
							</div>
                        </div>
						<!--End Employee Designation Description Field Set-->
						
					</div>
					
					<button type="reset" class="cancel" id="formEmpDesignationReset" style="display: none;" ></button>
				</form>
				
				<?php } ?>
				
			</div>
			<div class="modal-footer text-center" id="formActionEmployeeDesignation">
				
				<?php if ($designationAccess['Add'] == 1 || $designationAccess['Update'] == 1) { ?>
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetEmployeeDesignation" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitEmployeeDesignation" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
				<?php } ?>
				
			</div>
		</div>
	</div>
</div>

<!-- Form Dirty Confirmation Modal -->
<?php if ($designationAccess['Add'] || $designationAccess['Update']) { ?>
<div class="modal fade" id="modalDirtyEmployeeDesignation" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditConfDesignation"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfDesignation">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseConformEmployeeDesignation">Yes</button>
			</div>
		</div>
	</div>
</div>

<!-- Update confirmation modal shown in designation form when grade is changed and salary exists for that designation employee -->
<div class="modal fade" id="modalSalRecalConfirmEmployeeDesignation" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditSalRecalConfEmpDesignation"></i></button>
				<h4 class="modal-title"><strong>Update</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Salary has to be recalculated for all employees with this designation due to this change. Are you sure you want to do this?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditSalRecalConfEmpDesignation">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseSalRecalEmployeeDesignation">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } if ($designationAccess['Delete']) {  ?>
<!-- Delete COnfirmation Modal -->
<div class="modal fade" id="modalDeleteEmployeeDesignation" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteConfDesignation"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfDesignation">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConformEmployeeDesignation">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } }
if ($designationAccess['Admin'] == 'admin') { ?>
	<div class="modal fade" id="formCloneRoles" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace" id="backDesignationCloneRole"></i>
				</button>
				<h4 class="modal-title">Clone Roles</h4>
			</div>
			<div class="modal-body">
				
				<!--Clone Roles Form-->
				<form role="form" class="form-horizontal form-validation" id="CloneRolesform" method="POST" action="">
					<input type="hidden" name="form_CloneRoles" id="form_CloneRoles" />
					<div class="row">
						<div class="form-group">
							<label class="col-md-4 control-label">Designation <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select multiple="multiple" class="form-control vRequired selectAlll" data-search="true" id="formDesignationCloneRoles" name="Desgination" >
									<option value="selectAll">--Select all--</option>
									<option value="clearAll">--Clear all--</option>
								</select>
							</div>
						</div>
					
					</div>
				</form>
				
			</div>
			<div class="modal-footer text-center" id="formActionCloneRoles">
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetCloneRoles" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitCloneRoles" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
				
			</div>
		</div>
	</div>
</div>
<div class="modal fade" id="modalDirtyCloneRoles" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeCloneRoleConfDesignation"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noCloneRoleConfDesignation">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseCloneRolesForm">Yes</button>
			</div>
		</div>
	</div>
</div>
<?php }
 if ($empUser['View'] !=1 /*&& $empSelfAccess['View'] !=1*/ && $empTypeAccess['View'] != 1 && $designationAccess['View']!=1 && $empGradeAccess['View'] !=1) { ?>

<div class="col-md-12 portlets add-panel-padding">
	<div class="txt_center">Sorry, Access Denied...</div>
</div>

<?php } ?>
