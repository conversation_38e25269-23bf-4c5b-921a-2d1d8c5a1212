<?php
//=========================================================================================
//=========================================================================================
/* Program : MemosController.php													     *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : Memos are used within organizations to instruct employees about         *
 * policies, disseminate information and delegate responsibilities. Memos are useful     *
 * for manager to inform and motivate employees.										 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        17-Aug-2013    Narmadha		          Initial Version        	         *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Dhanabal                Changed in file for mobile app     *
 *                                                                                       *
 *  1.5        09-Feb-2016    Prasanth               Changed in file for Bootstrap        *
 *							  				                		                     */
//=========================================================================================
//=========================================================================================
include APPLICATION_PATH."/validations/Validations.php";
class Employees_MemosController extends Zend_Controller_Action
{
    protected $_dbMemos          = null;
    protected $_dbPersonal       = null;
    protected $_dbAccessRights   = null;
    protected $_memoAccessRights = null;
    protected $_logEmpId         = null;
    protected $_dbCommonFun      = null;
	protected $_memoUsers        = null;
	protected $_validation       = null;
	protected $_ehrTables          = null;
    protected $_formName         = 'Memos';
	protected $_hrappMobile = null;
    
    public function init()
    {
		$this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
		if ($this->_hrappMobile->checkAuth())
        {
			$this->_dbCommonFun    = new Application_Model_DbTable_CommonFunction();
            $this->_dbMemos        = new Employees_Model_DbTable_Memos();
            $this->_dbPersonal     = new Employees_Model_DbTable_Personal();
            $this->_dbAccessRights = new Default_Model_DbTable_AccessRights();
            $this->_validation 	   = new Validations();
            $this->_ehrTables 	   = new Application_Model_DbTable_Ehr();
			
			$userSession             = $this->_dbCommonFun->getUserDetails ();
            $this->_logEmpId         = $userSession['logUserId'];
			$this->_memoAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formName);
			$this->_memoUsers        = $this->_memoAccessRights['Employee'];
            //$this->_dbAccessRights->refreshUserSessionTimestamp($this->_logEmpId);
        }
        else
        {
            if (Zend_Session::namespaceIsset('lastRequest'))
                Zend_Session:: namespaceUnset('lastRequest');
            
            $sessionUrl = new Zend_Session_Namespace('lastRequest');
            $sessionUrl->lastRequestUri = 'employees/memos';
            $this->_redirect('auth');
        }
    }
	
    public function indexAction()
    {
		$checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

		if ($checkSessionAuth)
		{
			$this->_helper->layout()->disableLayout()->setLayout('admin_layout');
		
			$this->view->formNameA        = $this->_formName;
			$this->view->customFormNameA = $this->_ehrTables->getCustomForms($this->_formName);
			
			$this->view->employeeDetails = $this->_dbCommonFun->listEmployeesDetails ('Memos', '', $this->_logEmpId);
			
			$this->view->memoUser =  array('Is_Manager'    => $this->_memoUsers['Is_Manager'],
										   'View'          => $this->_memoUsers['View'],
										   'Update'        => $this->_memoUsers['Update'],
										   'Delete'        => $this->_memoUsers['Delete'],
										   'Add'           => $this->_memoUsers['Add'],
										   'Admin'         => $this->_memoAccessRights['Admin'],
										   'Employee_Name' => $this->_dbPersonal->employeeId($this->_logEmpId));
			
			$this->view->dateformat =  $this->_ehrTables->orgDateformat();
		} else {
			$this->_redirect('auth');
		}
    }

    /**
     * Get memos deails to show in a grid
     */
    public function listMemosAction()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-memos', 'json')->initContext();
			
            if ($this->_memoUsers['View'] == 1)
            {
                $sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
				
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$empFirstName = $this->_getParam('Emp_First_Name', null);
				$empFirstName = filter_var($empFirstName, FILTER_SANITIZE_STRIPPED);
				
				$memoBeginDate = $this->_getParam('MemoBeginDate', null);
				$memoBeginDate = filter_var($memoBeginDate, FILTER_SANITIZE_STRIPPED);
				
				$memoEndDate = $this->_getParam('MemoEndDate', null);
				$memoEndDate = filter_var($memoEndDate, FILTER_SANITIZE_STRIPPED);
				
                $memoUserArr = array('Is_Manager' => $this->_memoUsers['Is_Manager'],
									 'Admin'      => $this->_memoAccessRights['Admin'],
									 'LogId'      => $this->_logEmpId);
                
				$searchArr = array('EmpFirstName'  => $empFirstName,
								   'MemoBeginDate' => $memoBeginDate,
								   'MemoEndDate'   => $memoEndDate);
				
				$this->view->result = $this->_dbMemos->listMemos ($page, $rows, $sortField, $sortOrder, $searchAll, $searchArr, $memoUserArr);
            }
        }
        else
        {
            $this->_helper->redirector ('index', 'memos', 'employees');
        }
    }

	
	/**
	 *	add or update Memo details
	*/
	public function updateMemosAction()
	{
		$this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('update-memos', 'json')->initContext();
			
			$memoId = $this->_getParam('memoId', null);
			$memoId = filter_var($memoId, FILTER_SANITIZE_NUMBER_INT);
			
			if (($this->_memoUsers['Is_Manager'] == 1 || !empty($this->_memoAccessRights['Admin'])) && 
				($this->_memoUsers['Add'] == 1 || ($this->_memoUsers['Update'] == 1 && !empty($memoId))))
			{
				if ( $this->getRequest()->isPost() )
				{
					$formData = $this->getRequest()->getPost();
					
					$employeeId = $this->_validation->intValidation($formData['employeeId']);
					
					$memoDate = $this->_validation->dateValidation($formData['memoDate']);
					
					$description['value'] = $this->_validation->commonFilters($formData['description']);
					$description['valid'] = $this->_validation->lengthValidation($description, 5, 500, true);
					
					if ($employeeId['valid'] && !empty($employeeId['value']) && $memoDate['valid'] && !empty($memoDate['value']) &&
						$description['valid'] && !empty($description['value']))
					{
						$memoEmpId  = $employeeId['value'];
						$memoDate   = $memoDate['value'];
						$memoDesc   = $description['value'];
						$ckAddedBy  = $this->_dbPersonal->ckAddedById($memoEmpId);
						$ehrTables  = new Application_Model_DbTable_Ehr();
						$dateOfJoin = date('Y-m-d',strtotime($ehrTables->changeDateformat($this->_dbPersonal->getDateOfJoin($memoEmpId))));
						
						if ($memoEmpId != $this->_logEmpId && ($ckAddedBy == $this->_logEmpId || !empty($this->_memoAccessRights['Admin'])) &&
							(($memoId == 0 && strtotime($memoDate) >= strtotime(date('Y-m-d'))) ||
							($memoId > 0 && strtotime($memoDate) >= strtotime($dateOfJoin))))
						{
							$memoData = array('MemoTo_Id'   => $memoEmpId,
											  'Memo_Date'   => $memoDate,
											  'Description' => htmlentities($memoDesc));
							
                            $customFormName = $this->_ehrTables->getCustomForms($this->_formName);
                            $customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formName);
                            
							$this->view->result = $this->_dbMemos->updateMemo ($memoData, $memoId, $this->_logEmpId,$customFormNamee);
							
							
						}
						else
						{
							$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
						}
					}
					else
					{
						$this->view->result = array('success'=> false, 'msg' => 'Invalid data', 'type' => 'info');
					}
				}
			}
			else
			{
				$this->view->result = array('success'=> false, 'msg' => 'Invalid data', 'type' => 'warning');
			}
		}
		else
		{
			$this->_helper->redirector ('index', 'memos', 'employees');
		}
	}
	
    /**
     * Delete memos
     */
    public function deleteMemosAction()
    {
        $this->_helper->layout->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('delete-memos', 'json')->initContext();
			
			if ($this->_memoUsers['Delete'] == 1 &&
				($this->_memoUsers['Is_Manager'] == 1 || !empty($this->_memoAccessRights['Admin'])))
            {
				$memoId = $this->_getParam('memoId', null);
				$memoId = filter_var($memoId, FILTER_SANITIZE_NUMBER_INT);
				
				if (!empty($memoId) && $memoId > 0)
				{
                    $customFormName = $this->_ehrTables->getCustomForms($this->_formName);
					$customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formName);
                    
					$this->view->result = $this->_dbMemos->deleteMemo ($memoId, $this->_logEmpId,$customFormNamee);
				}
                else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
				}
            }
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Access denied', 'type' => 'warning');
			}
        }
        else
        {
            $this->_helper->redirector ('index', 'memos', 'employees');
        }
    }

	public function __destruct()
    {
        
    }

}