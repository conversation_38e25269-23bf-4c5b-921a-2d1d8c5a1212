<?php
	$isAdmin          = $this->isAdmin;
	$attendanceAccess = $this->attendanceAccess;
	$importAccess     = $this->importAccess;
	$attendanceSettingsAccess     = $this->attendanceSettingsAccess;
	$deviceManagementAccess		  = $this->deviceManagementAccess;
	$attendanceBoxAccess = $this->attendanceBoxAccess;
	$attendanceFinalizationAccess = $this->attendanceFinalizationAccess; //attendance Finalization acess rights

	$logEmployeeName       = $this->logEmployeeName;
	$logEmployeeId         = $this->logEmployeeId;
	$importFormatTblHeader = $this->importFormatTblHeader;
	$employeeName          = $this->employeeName;
	$schemaTitle           = $this->schemaTitle;
	
	$employees     = $this->employeeDetails;
	
	$workPlaceList = $this->workPlaceList;
	$importDataSource = $this->importDataSource;
	$formSourceList = $this->formSourceList;
	$attendanceImportErrorCode = $this->attendanceImportErrorCode;
	$empLocation     = $this->empLocation;
	$deptHierarchy 	 = $this->deptHierarchy;   
	$managerNames 	 = $this->managerNames;

	$mapAPIKey       = $this->mapAPIKey;
 
	$workschedule  = $this->workschedule;
	$leaveType  = $this->leaveType;
	$unpaidLeaveType  = $this->unpaidLeaveType;
	$getDeviceName = $this->getDeviceName;

	$orgSettings = $this->viewOrganizationDetails;
	$serviceProvider = $this->serviceProvider;
	$attendanceSettings = $this->attendanceSettings;
	$customGroup = $this->customGroup;
	$department = array();
	
	foreach ($employees as $key => $row) {
		if (!in_array($row['Department_Name'], $department)) {
			array_push($department, $row['Department_Name']);
		}
	}
	
	$customFormNameA = $this->customFormNameA;
	$customFormNameB = $this->customFormNameB;
	$customFormNameC = $this->customFormNameC;
	$customFormNameD = $this->customFormNameD;
	$customFormNameE = $this->customFormNameE;
	$customFormNameG = $this->customFormNameG;
	
	$finalFormName = ((!empty($customFormNameA) && !empty($customFormNameA['New_Form_Name'])) ? $customFormNameA['New_Form_Name'] : $this->formNameA);
	
	$this->headTitle($finalFormName);		
	
	$dateformat = $this->dateformat;
    $datetime = $this->datetime;
	
	
	if(!empty($dateformat))
	{
		$dformat = $dateformat['bs'];
		//$dformat1 = $dateformat['jq'];	
	}
	else
	{
		$dformat = 'dd/mm/yyyy';
		//$dformat1 = 'dd/mm/yy';	
	}

	$month 				= $this->month;
	$attendanceYear 	= $this->attendanceYear;
	$salaryDateDetails 	= $this->salaryDateDetails;
	$currentMonthYear 	= date('Y', strtotime($salaryDateDetails['Last_SalaryDate']));
	$currentMonth 		= date('n', strtotime($salaryDateDetails['Last_SalaryDate']));


	$prefillMonthYear = $currentMonth.','.$currentMonthYear;
	//  check if the attendance finalization view access is available and show the Attendance Finalization Tab 
	// $attendanceFinalizationAccess['View']=0;
	// $attendanceFinalizationAccessView = 1;
	// $attendanceAccess['View']=0;
	// $attendanceAccessView = 0;
	
	$attendanceAccessView = 0;
	    ?>
		<div class="col-lg-12 col-md-12 col-sm-12 portlets paddingCls tab-spacing-cls">
		<div class="col-lg-12 col-md-12 col-sm-12 paddingCls bg-f9f9f9 tab-wrapper">
			<?php if($attendanceAccess['View']||$importAccess['View']||$attendanceSettingsAccess['View']||$deviceManagementAccess['View']) {?>
			<div class="col-lg-1 col-md-1 col-sm-1 pointer-cls border-bottom-secondary tab-border-cls bg-f9f9f9 tab-body" id="attTab">
				<div class="tab-active-text tab-text-font text-secondary custom-tab-content"  id="employeeAttendanceTab"><?php echo $finalFormName ?></div>
			</div>
			<?php }
			else 
			{ ?>
				<div class="col-lg-1 col-md-1 col-sm-1" id="attTab">	
				</div>
			<?php } ?>

			<?php if($attendanceFinalizationAccess['View']) {?>
			<div class="col-lg-2 col-md-2 col-sm-2 border-bottom-secondary pointer-cls bg-f9f9f9 tab-body" id="attFinTab">
				<div class="tab-text-font custom-tab-content" id="employeeAttendanceFinalizationTab"><?php echo $this->formNameG?></div>
			</div>
	        <?php }
			else 
			{ ?>
				<div class="col-lg-2 col-md-2 col-sm-2" id="attFinTab">	
				</div>
			<?php } ?>

        

			<div class="col-lg-9 col-md-9 col-sm-9 custom-tab-actions border-bottom-secondary pointer-cls bg-f9f9f9 tab-body" style="padding-top:25px;">
				<select class="form-control payslipMonthListSelect" id="attendanceYear" data-search="true" name="Attendance Year" style="padding:5px;">
					<?php
						foreach ($attendanceYear as $key => $row)
						{
							if($row==$currentMonthYear)
							{
								echo '<option value="'.$key.'" selected="selected">'.$row.'</option>';
							}
							else 
							{
								echo '<option value="'.$key.'">'.$row.'</option>';
							}
						}
					?>
				</select>
				<select class="form-control payslipMonthListSelect" id="attendanceMonth" data-search="true" name="Attendance Month" style="padding:5px;">
					<?php
						foreach ($month as $key => $row)
						{
							if($key==$currentMonth)
							{
								echo '<option value="'.$key.'" selected="selected">'.$row.'</option>';
							}
							else 
							{
								echo '<option value="'.$key.'">'.$row.'</option>';
							}
						}
					?>
				</select>
			</div>
			<input type="hidden" name="currentMonth" id="currentMonth" value="<?php echo $prefillMonthYear; ?> "/>
		</div>
	</div> 
	
	<!-- access rights of all the subforms in attendance -->
	<input type="hidden" name="attendanceFinalizationAccess" id="attendanceFinalizationAccess" value="<?php echo $attendanceFinalizationAccess['View']; ?> "/>
	<input type="hidden" name="attendanceAccess" id="attendanceAccess" value="<?php echo $attendanceAccess['View']; ?> "/>
	<input type="hidden" name="importAccess" id="importAccess" value="<?php echo $importAccess['View']; ?> "/>
	<input type="hidden" name="attendanceSettingsAccess" id="attendanceSettingsAccess" value="<?php echo $attendanceSettingsAccess['View']; ?> "/>
	<input type="hidden" name="deviceManagementAccess" id="deviceManagementAccess" value="<?php echo $deviceManagementAccess['View']; ?> "/>
	<input type="hidden" name="googleMapAPIKey" id="googleMapAPIKey" value="<?php echo $mapAPIKey; ?> "/>
	<input type="hidden" name="fieldForce" id="fieldForce" value="<?php echo $orgSettings['Field_Force']; ?>" />
	<?php
	if($attendanceAccessView==0)
	{?>

			<div class="col-md-12 portlets add-panel-padding" id="attendancePanel">
				<div class="card my-profile-card-radius">
					<div class="row">
						<div class="col-xs-12 my-profile-img-column">
							<img width="50" height="50" alt="my-team" src="<?php echo $this->newImg;?>"/>
							<div class="my-profile-heading">
								Explore your enhanced attendance!
							</div>
						</div>
						<div class="col-xs-12 my-profile-sub-text">
							We're excited to share our platform's new enhancements for a better experience! 
							Now you can easily review your attendance under 'Employee Self Service' -> '
							<a id="myProfileLink" class="text-secondary" href=<?php echo $this->baseUrl('v3/employee-self-service/attendance'); ?>>
								Attendance
							</a>', and get a snapshot of your team's dynamics under 'My Team' -> '
							<a id="myTeamLink" class="text-secondary" href=<?php echo $this->baseUrl('v3/my-team/attendance'); ?>>
								Attendance
							</a>'.
						</div>
					</div>
				</div>
			</div>
	<?php
	}
	?>
	

	<?php 
	
	if ($attendanceAccess['View'] && ((!empty($customFormNameA) && array_key_exists("Enable",$customFormNameA) && $customFormNameA['Enable'] == 1) || empty($customFormNameA))) {
?>
<!--Attendance Grid Panel-->
<div class="col-md-12 portlets add-panel-padding">
	<div class="panel" id="gridPanelAttendance">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong id="lblFormNameA"><?php echo $finalFormName;?></strong></h3>
		</div>
		<div class="panel-content">
			<!--Attendance Grid Toolbar Icons-->
			
				<div class="m-b-10">
					
					<?php if ($attendanceAccess['Is_Manager'] || $attendanceAccess['Admin']) {?>
					<button type="button" class="btn btn-white btn-embossed toolbar-icons" title="Check All" id="attendanceCheckAll">
						<i class="hr-check-all"></i><span class="hidden-xs hidden-sm">Check All</span>
					</button>
					
					<?php } if ($attendanceAccess['Add']) { ?>
					<button type="button" class="btn btn-white btn-embossed toolbar-icons" title="Add" data-toggle="modal" id="addAttendance">
						<i class="mdi-content-add"></i><span class="hidden-xs hidden-sm"> Add</span>
					</button>
					<?php } if ($attendanceBoxAccess['Update'] &&($attendanceAccess['Is_Manager'] || $attendanceAccess['Admin'])) { ?>
					<a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="addAttendanceBoxInAtt">
						<i class="hr-attendance-box"></i><span class="hidden-xs hidden-sm"> Attendance Box</span>
					</a>
					<?php } ?>
					
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="viewAttendance" title="View">
						<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
					</button>
					
					<?php if ($attendanceAccess['Update'] && $orgSettings['Multiple_Source_Attendance_Same_Time']=='No') { ?>
					
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="Edit" id="editAttendance">
						<i class="mdi-editor-mode-edit"></i><span class="hidden-xs hidden-sm"> Edit</span>
					</button>
					
					<?php } if ($attendanceAccess['Delete']) { ?>
					
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="Delete" id="deleteAttendance">
						<i class="mdi-action-delete"></i><span class="hidden-xs hidden-sm"> Delete</span>
					</button>
					
					<?php } if ($attendanceAccess['Is_Manager'] || $attendanceAccess['Admin'] ) {?>
					
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="Status Update" id="statusAttendance">
						<i class="mdi-action-info-outline"></i><span class="hidden-xs hidden-sm"> Status Update</span>
					</button>
					
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="Status Approval" data-toggle="modal" data-target="#modalMultiStatusAttendance" id="multistatusAttendance">
						<i class="hr-status-approval"></i><span class="hidden-xs hidden-sm"> Status Approval</span>
					</button>
					<?php } if ($attendanceAccess['Admin'] ) {?>
					<div class="btn-group" style="margin-top: 0px;" id="btnBreakHours">
						<button type="button" class="btn btn-whitep dropdown-toggle" data-toggle="dropdown" aria-expanded="true">
							<i class="hr-include-break-hours"></i>
							<span class="hidden-xs hidden-sm">Break Hours</span>
							<span class="caret"></span>
							<div class="ripple-wrapper"></div>
						</button>
						<span class="dropdown-arrow dropdown-arrow-inverse"></span>
						<ul class="dropdown-menu dropdown-inverse" role="menu">
							<li>
								<a id="includeDetails" style="cursor: pointer;" data-toggle="modal"
								   data-target="#modalIncluded">Included<!--<i class="hr-include-break-hours"></i><span class="hidden-xs hidden-sm">Included</span>--></a>
								
							</li>
							<li>
								<a id="excludedDetails" style="cursor: pointer;" data-toggle="modal"
								   data-target="#modalExcluded"">Excluded<!--<i class="hr-exclude-break-hours"></i><span class="hidden-xs hidden-sm">Excluded</span>--></a>
							</li>
						</ul>
					</div>
					
					<?php } if ($attendanceAccess['Op_Choice']) {?>
					
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="Bulk copy" id="copyAttendance">
						<i class="mdi-action-info-outline"></i><span class="hidden-xs hidden-sm"> Bulk Copy</span>
					</button>
					
					<?php }?>

					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="Comment" id="commentAttendance">
						<i class="mdi-communication-comment"></i><span class="hidden-xs hidden-sm"> Comment</span>
					</button>

					<?php if ($attendanceAccess['Is_Manager'] == 1 || $attendanceAccess['Admin'] == 'admin') { ?>
				
					<button type="button" class="btn btn-white btn-embossed visible-lg-* toolbar-icons" id="exportAttendance" title="Export">
						<i class="mdi-communication-import-export"></i><span class="hidden-xs hidden-sm"> Export</span>
					</button>
                    
                    <?php }?>
					
					<a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterAttendance">
						<i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm"> Filter</span>
					</a>
					<button type="button" class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="ViewAttendanceSummary" title="Expand All" >
						<i class="hr-expand"></i><span class="hidden-xs hidden-sm">Expand All</span>
				</button>
			</div>		
			<div class="attLegend">
				<div style="display: flex; padding: 5px">
					<div class="color-legend attRegularization"></div>
					<span>Regularized attendance</span>
				</div>
				<div style="display: flex; padding: 5px">
					<div class="color-legend addedBy"></div>
					<span>Attendance added by others</span>
				</div>
			</div>
			<!-- Attendance Grid Table -->
			<table class="table dataTable table-striped table-dynamic table-hover" id="tableAttendance">
				<thead>
					<tr>
						<th></th>
						<th id="attendanceEmployeeId">Employee Id</th>
						<th id="attendanceEmployeeName">Employee Name</th>
						<th id="attendanceDate">Attendance Date</th>
						<th id="attendancePunchInDate">Punch In</th>
						<th id="attendancePunchOutDate">Punch Out</th>
						<th id="attendanceTotalHours">Total Hours</th>
						<th id="attendanceAttendanceStatus">Status</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
		</div>
	</div>
</div>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="attendance-context-menu" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextAttendance"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($attendanceAccess['Update'] ) { ?>
		<li><a id="editContextAttendance"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<?php } if ($attendanceAccess['Delete']) { ?>
		<li><a tabindex="-1" id="deleteContextAttendance"><i class="mdi-action-delete"></i> Delete</a></li>
		<?php } if ($attendanceAccess['Is_Manager']) {?>
		<li><a tabindex="-1" id="statusUpdateContextAttendance"><i class="mdi-action-info-outline"></i> Status Update</a></li>
		<?php } ?>
		<li><a tabindex="-1" id="commentContextAttendance"><i class="mdi-communication-comment"></i> Comment</a></li>
		
		<li><a tabindex="-1" id="excludeBreakhoursContextAttendance"><i class="mdi-action-visibility"></i> Break Hours</a></li>
	</ul>
</div>

<!-- warning user about accuracy of geo co-ordinate -->
<div class="modal fade" id="accuracyWarningModalAttendance" aria-hidden="true">
		<div class="modal-dialog geo-coordinates-modal">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true" style="color: var(--primary-color)">
						<i class="icons-office-52" style="font-size: 14px"></i>
					</button>
				</div>
				<div class="modal-body" style="text-align:center">
					<div style="justify-content: center; display: flex;">
						<img style="width: 300px" alt="warning-img-geo-accuracy" 
						onerror="this.onerror=null; this.src='<?php echo $this->baseUrl('images/geo-accuracy-warning-img.png'); ?>'" class="lazyload" 
						data-src="<?php echo $this->baseUrl('images/geo-accuracy-warning-img.webp'); ?>" />
					</div>
					<div class="geo-coordinates-alert-content" id="geoCoordinatesAlertContentAttendance">
					</div>
					<button type="submit" class="btn btn-secondary btn-embossed" id="proceedAttendanceBtn">
						Proceed Now
					</button>
					<br>
				</div>
			</div>
		</div>
	</div>

<!-- Modal for Add Form, View Form, Edit Form -->
<div class="modal fade" id="modalFormAttendance" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace" id="backAttendance"></i>
				</button>
				
				<?php if ($attendanceAccess['Update']) { ?>
				<button type="button" class="close form-icons" aria-hidden="true" id="editInViewAttendance">
					<i class="mdi-editor-mode-edit"></i>
				</button>
				<?php } ?>
				
				<h4 class="modal-title" id="modalFormTitleAttendance"></h4>
			</div>
			<div class="modal-body">
				<!--View Attendance Form-->
				<form role="form" id="viewFormAttendance" >
					<div class="row">
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Employee Name</label></div>
							<div class="col-md-7"><p id="viewEmployeeName"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Forwarded To</label></div>
							<div class="col-md-7"><p id="viewForwardTo"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Check In Date</label></div>
							<div class="col-md-7"><p id="viewPunchInDate"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Check In Time</label></div>
							<div class="col-md-7"><p id="viewPunchInTime"></p></div>
						</div>
						<div class="form-group" id='vActualCheckIn'>
							<div class="col-md-5"><label class="control-label">Actual Check In Time</label></div>
							<div class="col-md-7"><p id="viewActualPunchInTime"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Check In Work Place</label></div>
							<div class="col-md-7"><p id="viewCheckInWorkPlace"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Check Out Date</label></div>
							<div class="col-md-7"><p id="viewPunchOutDate"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Check Out Time</label></div>
							<div class="col-md-7"><p id="viewPunchOutTime"></p></div>
						</div>
						<div class="form-group" id='vActualCheckOut'>
							<div class="col-md-5"><label class="control-label">Actual Check Out Time</label></div>
							<div class="col-md-7"><p id="viewActualPunchOutTime"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Check Out Work Place</label></div>
							<div class="col-md-7"><p id="viewCheckOutWorkPlace"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Total Hours</label></div>
							<div class="col-md-7"><p id="viewTotalHrs"></p></div>
						</div>
						<div class="form-group" id="vActualTotalHrs">
							<div class="col-md-5"><label class="control-label">Actual Total Hours</label></div>
							<div class="col-md-7"><p id="viewActualTotalHrs"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Auto Short Time Off</label></div>
							<div class="col-md-7"><p id="viewAutoShortTimeOff"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Late Attendance</label></div>
							<div class="col-md-7"><p id="viewLateAttendance"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Late Attendance Hours</label></div>
							<div class="col-md-7"><p id="viewLateAttendanceHours" style="color:red;"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Status</label></div>
							<div class="col-md-7"><p id="viewStatus"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Exclude Break Hours</label></div>
							<div class="col-md-7"><p id="viewExcludeBreakHours"></p></div>
						</div>
						
						<!--geolocation-->
						<div id="geolocation">
							
						</div>
						<div id="geolocationOut">
							
						</div>
						<!-- Data Source Details -->
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Check In DataSource</label></div>
							<div class="col-md-7"><p id="viewCheckInDataSource"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Check Out Data Source</label></div>
							<div class="col-md-7"><p id="viewCheckOutDataSource"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Check In Form Source</label></div>
							<div class="col-md-7"><p id="viewCheckInFormSource"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Check Out Form Source</label></div>
							<div class="col-md-7"><p id="viewCheckOutFormSource"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Approved By</label></div>
							<div class="col-md-7"><p id="approvedByAttendance"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Approved On</label></div>
							<div class="col-md-7"><p id="approvedOnAttendance"></p></div>
						</div>

					</div>	
					
					<div class="row">
						<hr class="view-hr"/>
						
						<div class="form-group" style="font-size: large;margin-left: 13px;">
							<label class="control-label text-center">Additional Information</label>
						</div>
						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Added On</label></div>
							<div class="col-md-7"><p id="addedOnAttendance"></p></div>
						</div>
						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Added By</label></div>
							<div class="col-md-7"><p id="addedByAttendance"></p></div>
						</div>
						
						<div class="form-group updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated On</label></div>
							<div class="col-md-7"><p id="updatedOnAttendance"></p></div>
						</div>
						
						<div class="form-group updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated By</label></div>
							<div class="col-md-7"><p id="updatedByAttendance"></p></div>
						</div>
						
					</div>
				</form>
				<input type="hidden" name="logAttEmpIsManager" id="logAttEmpIsManager" value="<?php echo $attendanceAccess['Is_Manager']; ?>" />
				<?php if ($attendanceAccess['Add'] == 1 || $attendanceAccess['Update'] == 1) { ?>
				
				<!--Add/Edit Attendance Form-->
				<form role="form" class="form-horizontal form-validation" id="editFormAttendance" method="POST" action="">
					<input type="hidden" name="Attendance_Id" id="formAttendanceId" />
					<input type="hidden" name="logEmpId" id="logEmpId" value="<?php echo $logEmployeeId; ?>" />
					<input type="hidden" name="logEmpIsAdmin" id="logEmpIsAdmin" value="<?php echo $isAdmin; ?>" />
					<input type="hidden" name="oldInDate" id="oldInDate" />
					<input type="hidden" name="isLateAttendanceRecord" id="isLateAttendanceRecord" />
					<input type="hidden" name="addattend" id="addatt_Id" />

					<!--<input type="hidden" name="timezoneTime" id="timezoneDatetime" value="<php echo date('m/d/Y h:i:s a');?>" />-->
                    
					<div class="row">
						
						<!--Start employee Name Field Set-->
						<div class="form-group">
							<label class="col-md-4 control-label">Employee Name <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control vRequired" name="Employee_Name" id="formEmployeeId" data-search="true" data-placeholder="Employee Name">
									<?php
									if ($attendanceAccess['Is_Manager'] == 1 || $attendanceAccess['Admin'] == 'admin')
									{
										foreach ($department as $key => $row) {
											echo '<optgroup label="'. $row .'">';
											
											foreach ($employees as $empKey => $empRow) {
												if ($row == $empRow['Department_Name']) {
													echo '<option value="'. $empRow['value'] .'">'. $empRow['text'] .'</option>';
												}
											}
											
											echo '</optgroup>';
										}
									}
									else{
										echo '<option value="'. $logEmployeeId .'">'. $logEmployeeName .'</option>';
									}
									?>
								  </select>
							</div>
                        </div>
						<!--End employee Name Field Set-->
						
						<!--Start Forwarded To Field Set-->
						<div class="form-group">
							<label class="col-md-4 control-label">Forward To <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control" data-search="true" id="formForwardTo" name="ForwardTo" disabled="true">
									
								</select>
							</div>
                        </div>
						<!--End Forwarded To Field Set-->
						
						<!--Start Punch In Field Set-->
						<div class="form-group">
							<label class="col-md-4 control-label">Check In <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<input type="text" name="AttendancePunchIn" id="attendancePunchIn" class="datetimepicker form-control datePickerRead" placeholder="Choose a date...">
                                <!--<input type="text" name="AttendancePunchIn" id="attendancePunchIn" class="datepicker form-control vRequired" placeholder="Choose a date...">                                -->
							</div>
                        </div>
						<!--End Punch In Field Set-->

						<!--Start checkin work place Field Set-->
							<div class="form-group" id="checkInWorkPlaceDiv">
								<label class="col-md-4 control-label">Check In Work Place<span class="short_explanation">*</span></label>
								<div class="col-md-8">
									<select class="form-control vRequired" data-search="true" id="checkInWorkPlace" name="Checkin Work Place" data-placeholder="Check In Work Place">
										<option value="">-- Select --</option>
										<?php
											for($i=0;$i<count($workPlaceList);$i++)
											{
												echo '<option value="'. $workPlaceList[$i]['Work_Place_Id'] .'">'. $workPlaceList[$i]['Work_Place'].'</option>';
											}
										?>
									</select>
								</div>
							</div>
						<!--End checkin work place Field Set-->

						<!--Start Punch Out Field Set-->
						<div class="form-group">
							<label class="col-md-4 control-label">Check Out </label>
							<div class="col-md-8">
								<input type="text" name="AttendancePunchOut" id="attendancePunchOut" class="datetimepicker form-control" placeholder="Choose a date...">
							</div>
                        </div>
						<!--End Punch Out Field Set-->
						
						<!--Start check Out work place Field Set-->
							<div class="form-group" id="checkOutWorkPlaceDiv">
								<label class="col-md-4 control-label">Check Out Work Place</label>
								<div class="col-md-8">
									<select class="form-control" data-search="true" id="checkOutWorkPlace" name="Checkout Work Place" data-placeholder="Check Out Work Place">
										<option value="">-- Select --</option>
										<?php
											for($i=0;$i<count($workPlaceList);$i++)
											{
												echo '<option value="'. $workPlaceList[$i]['Work_Place_Id'] .'">'. $workPlaceList[$i]['Work_Place'].'</option>';
											}
										?>
									</select>
								</div>
							</div>
						<!--End checkin work place Field Set-->

						<!--Start Exclude Break Hours Field Set-->
						<div class="form-group">
							<label class="col-md-4 control-label">Exclude Break Hours</label>
							<div class="col-md-8">
								<div class="form-group togglebutton">
									<label>
									  <input type="checkbox" style="margin-top: 15px; margin-left: 15px;" class="col-sm-9 md-checkbox" name="ExcludeBreakHours" id="excludeBreakHours" >
									</label>
								  </div>
							</div>
                        </div>
						<!--End Exclude Break Hours Field Set-->
						
						<!--Start Status Field Set-->
						<div class="form-group">
							<label class="col-md-4 control-label">Status</label>
							<div class="col-md-8">
								<label class="control-label" id="formStatus" name="Status">Draft</label>
							</div>
                        </div>
						<!--End Status Field Set-->
						
						<!--Start Comment Field Set-->
						<div class="form-group">
							<label class="col-md-4 control-label">Add Comment</label>
							<div class="col-md-8">
								<textarea name="comment" id="formAttendanceComment" rows="5" class="form-control vAlphaNumSpCDotHySlashNewLine" placeholder="Write your Comment..." ></textarea>
							</div>
                        </div>
						<!--End Comment Field Set-->
						
					</div>
					
					<button type="reset" class="cancel" id="formReset" style="display: none;" ></button>
				</form>
				
				<?php } ?>
				
			</div>
			<div class="modal-footer text-center" id="formActionAttendance">
				
				<?php if ($attendanceAccess['Add'] == 1 || $attendanceAccess['Update'] == 1) { ?>
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetAttendance" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitAttendance" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
				<?php } ?>
				
			</div>
		</div>
	</div>
</div>

<?php  if ($attendanceAccess['Op_Choice']) {?>

<div class="modal fade" id="modalFormBulkCopy" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace" id="backBulkAttendance"></i>
				</button>
				<h4 class="modal-title">Bulk Attedance Update</h4>
			</div>
			<div class="modal-body">
				
				<!--Add/Edit bulk copy Form-->
				<form role="form" class="form-horizontal form-validation" id="editFormBulkCopy" method="POST" action="">
					<input type="hidden" name="Copy_Attendence_Id" id="formCopyAttendenceId" />
					<div class="row">
						
						<!--Start employee Name Field Set-->
						<div class="form-group">
							<label class="col-md-4 control-label">Add Attendance For <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select multiple="multiple" class="form-control vRequired selectAlll" data-search="true" id="copyEmpList" name="copyEmpList">
									<option value="selectAll">--Select all--</option>
									<option value="clearAll">--Clear all--</option>
									
								</select>
							</div>
                        </div>
						<!--End employee Name Field Set-->
						
						<!--Start Punch In Date To Field Set-->
						<div class="form-group">
							<label class="col-md-4 control-label">Punch In Date </label>
							<div class="col-md-8">
								<label class="col-md-4 control-label" id="cpPunchInDate"> </label>
							</div>
                        </div>
						<!--End Punch In Date To Field Set-->
						
						<!--Start Punch In Time Field Set-->
						<div class="form-group">
							<label class="col-md-4 control-label">Punch In Time </label>
							<div class="col-md-8">
								<label class="col-md-4 control-label" id="cpPunchInTime"> </label>
							</div>
                        </div>
						<!--End Punch In Time Field Set-->
						
						<!--Start Punch Out Date Field Set-->
						<div class="form-group">
							<label class="col-md-4 control-label">Punch Out Date </label>
							<div class="col-md-8">
								<label class="col-md-4 control-label" id="cpPunchOutDate"> </label>
							</div>
                        </div>
						<!--End Punch Out Date Field Set-->
						
						<!--Start Punch Out Time Field Set-->
						<div class="form-group">
							<label class="col-md-4 control-label">Punch Out Time</label>
							<div class="col-md-8">
								<label class="col-md-4 control-label" id="cpPunchOutTime"> </label>
							</div>
                        </div>
						<!--End Punch Out Time Field Set-->
						
						<!--Start Comment Field Set-->
						<div class="form-group">
							<label class="col-md-4 control-label">Add Comment</label>
							<div class="col-md-8">
								<textarea name="comment" id="cpComment" rows="5" class="form-control vAlphaNumSpCDotHySlashNewLine" placeholder="Write your Comment..." ></textarea>
							</div>
                        </div>
						<!--End Comment Field Set-->
						
					</div>
				</form>
				
			</div>
			<div class="modal-footer text-center" id="formActionBulkCopy">
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetBulkCopy" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitBulkCopy" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
				
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="modalDirtyCopyAttendance" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeBulkConfAttendance"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noBulkConfAttendance">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseConformCopyAttendance">Yes</button>
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="modalViewLocationInMap" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;color: black;" aria-hidden="true"><i class="mdi-hardware-keyboard-backspace" id="backAttendanceSchema"></i></button>
				<h4 class="modal-title"><strong>View</strong> Location</h4>
			</div>
			<div class="modal-body" id="modalViewLocationInMapBody"></div>
		</div>
	</div>
</div>

<?php } ?>

<!--Filter Form-->
<div class="builder" id="filterPanelAttendance">
	<div id="closeFilterAttendance"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel" style="width: 100%;" id="filterResetAttendance">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="filterApplyAttendance">Apply</button>
			
			<?php if ($attendanceAccess['Is_Manager'] == 0 && empty($attendanceAccess['Admin'])) { ?>
			<div class="form-group">
				<label>Employee Name</label>
				<input type="text" class="form-control" id="filterEmployeeFirstName" readonly="readonly" value="<?php echo $attendanceAccess['Employee_Name']; ?>" >
			</div>
			
			<?php } else { ?>
			
			<div class="form-group">
				<label>Employee Name</label>
				<input type="text" class="form-control" id="filterEmployeeFirstName" placeholder="Employee Name">
			</div>
			
			<?php } ?>

			<!-- <div class="form-group">
				<label>Attendance Date</label>
				<div class="row">
					<div class="col-md-5" style="padding:10px!important">
						<input type="text" class="date-picker form-control datePickerRead" name="attendanceDateBegin" id="filterAttendanceDateBegin" data-orientation="top" placeholder="Begin" >
					</div>
					<span class="col-md-1" style="padding-top:25px!important">to</span>
					<div class="col-md-5" style="padding:10px!important">
						<input type="text" class="date-picker form-control datePickerRead" name="attendanceDateEnd" id="filterAttendanceDateEnd" data-orientation="top" placeholder="End" />
					</div>
				</div>
			</div> -->

			<div class="form-group">
				<label>Attendance Date</label>
				<div class="input-daterange b-datepicker input-group" data-date-format="<?php echo $dformat; ?>" id="datepicker">
					<input type="text" class="input-md form-control" name="attendanceDateBegin" id="filterAttendanceDateBegin" data-orientation="top" placeholder="Begin" />
					<span class="input-group-addon">to</span>
					<input type="text" class="input-md form-control" name="attendanceDateEnd" id="filterAttendanceDateEnd" data-orientation="top" placeholder="End" />
				</div>
			</div>

			<div class="form-group">
				<label>Location</label>
				<select class="form-control" data-search="true" id="filterAttendanceLocation" >
					<option value="">All</option>
					<?php
					foreach ($empLocation as $key => $row)
					{
						echo '<option value="'.$key.'">'.$row.'</option>';
					}
					?>
				</select>
			</div>

			<div class="form-group">
				<label>Department</label>
				<select class="form-control" data-search="true" id="filterAttendanceDepartment" >
					<option value="">All</option>
					<?php
					foreach ($deptHierarchy as $key => $row)
					{
						echo '<option value="'. $row['Department_Id'] .'">'. $row['Department_Name'] .'</option>';
						
						foreach($row['Child'] as $val =>$name)
						{
							
							if($name['Parent_Type_Id'] == $row['Department_Id'])
							{
								echo '<option value="'. $name['Department_Id'] .'">&nbsp;&nbsp;'. $name['Department_Name'] .'</option>';
								
								foreach($row['Child'] as $v =>$k)
								{
									if($k['Parent_Type_Id'] == $name['Department_Id'])
										echo '<option value="'. $k['Department_Id'] .'">&nbsp;&nbsp;&nbsp;&nbsp;'. $k['Department_Name'] .'</option>';
								}
								
							}
						}
					}
					?>
				</select>
			</div>

            <div class="form-group">
				<label>Manager Name</label>
				<select class="form-control" data-search="true" id="filterAttendanceManagerName">
					<option value="">All</option>
					<?php
					foreach ($managerNames as $row)
					{
						echo '<option value="'.$row['Employee_Id'].'">'.$row['Employee_Name'].'</option>';
					}
					?>
				</select>
			</div>

			<div class="form-group">
				<label>Auto Short Time Off</label>
				<select class="form-control" data-search="true" id="filterAutoShortTimeOff" >
					<option value="">All</option>
					<option value="Yes">Yes</option>
					<option value="No" >No</option>
				</select>
			</div>

			<div class="form-group">
				<label>Late Attendance</label>
				<select class="form-control" data-search="true" id="filterLateAttendance" >
					<option value="">All</option>
					<option value="Outside grace time(Slab)">Outside grace time(Slab)</option>
					<option value="Within grace time(Slab)" >Within grace time(Slab)</option>
					<option value="Outside grace time">Outside grace time</option>
					<option value="Within grace time" >Within grace time</option>
					<option value="No" >No</option>
				</select>
			</div>

			<div class="form-group">
				<label>Check In Work Place</label>
				<select class="form-control" data-search="true" id="filterCheckinWorkPlace" >
					<option value="">All</option>
					<?php
						for($i=0;$i<count($workPlaceList);$i++)
						{
							echo '<option value="'. $workPlaceList[$i]['Work_Place_Id'] .'">'. $workPlaceList[$i]['Work_Place'].'</option>';
						}
					?>
				</select>
			</div>

			<div class="form-group">
				<label>Check Out Work Place</label>
				<select class="form-control" data-search="true" id="filterCheckOutWorkPlace" >
					<option value="">All</option>
					<?php
						for($i=0;$i<count($workPlaceList);$i++)
						{
							echo '<option value="'. $workPlaceList[$i]['Work_Place_Id'] .'">'. $workPlaceList[$i]['Work_Place'].'</option>';
						}
					?>
				</select>
			</div>
			<div class="form-group">
				<label>Check In Form Source</label>
				<select class="form-control" data-search="true" id="filterCheckinFormSource" >
					<option value="">All</option>
					<?php
						for($i=0;$i<count($formSourceList);$i++)
						{
							echo '<option value="'. $formSourceList[$i]['Form_Id'] .'">'. $formSourceList[$i]['Form_Name'].'</option>';
						}
					?>
				</select>
			</div>

			<div class="form-group">
				<label>Check Out Form Source</label>
				<select class="form-control" data-search="true" id="filterCheckOutFormSource" >
					<option value="">All</option>
					<?php
						for($i=0;$i<count($formSourceList);$i++)
						{
							echo '<option value="'. $formSourceList[$i]['Form_Id'] .'">'. $formSourceList[$i]['Form_Name'].'</option>';
						}
					?>
				</select>
			</div>
			<div class="form-group">
				<label>Break Hours</label>
				<select class="form-control" data-search="true" id="filterBreakHours" >
					<option value="">All</option>
					<option value="0">Included</option>
					<option value="1">Excluded</option>
				</select>
			</div>			

			<?php if ($orgSettings['Field_Force'] == 1) { ?>
			<div class="form-group">
				<label>Service Provider</label>
				<select class="form-control" data-search="true" id="filterServiceProvider">
					<option value="">All</option>
					<?php
					foreach ($serviceProvider as $key => $row)
					{
						echo '<option value="'. $key .'">'. $row .'</option>';
					}
					?>
				</select>												
			</div>
										
			<?php } ?>
			
			<div class="form-group">
				<label>Status</label>
				<select class="form-control" data-search="true" id="filterStatus" >
					<option value="">All</option>
					<option value="Draft">Draft</option>
					<option value="Applied" >Applied</option>
					<option value="Approved" >Approved</option>
					<option value="Returned" >Returned</option>
					<option value="Rejected" >Rejected</option>
				</select>
			</div>
			
		</div>
	</div>
</div>

<div class="modal fade" id="modalCommentAttendance" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeCommentAttendance"></i></button>
				<h4 class="modal-title"><strong>Comment</strong></h4>
			</div>
			
			<div class="modal-body">
				<!-- comment Grid Table -->
				<table class="table dataTable table-striped table-dynamic table-hover tableCommentAttendance">
					<thead>
						<tr>
							<th></th>
							<th id="attendanceCommentEmployeeName">Employee Name</th>
							<th id="attendanceCommentComment">Comment</th>
							<th id="attendanceCommentStatus">Status</th>
							<th id="attendanceCommentAddedOn">Added On</th>
						</tr>
					</thead>
					<tbody>
					
					</tbody>
				</table>
			</div>
			
		</div>
	</div>
</div>

<!-- Form Dirty Confirmation Modal -->
<?php if ($attendanceAccess['Add'] || $attendanceAccess['Update']) { ?>
<div class="modal fade" id="modalDirtyAttendance" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditConfAttendance"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfAttendance">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseConformAttendance">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } if ($attendanceAccess['Delete']) { ?>
<!-- Delete COnfirmation Modal -->
<div class="modal fade" id="modalDeleteAttendance" aria-hidden="true" >
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteConfirmationAttendance"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body" id="deleteAttendanceMessage"></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfirmationAttendance">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConformAttendance">Yes</button>
			</div>
		</div>
	</div>
</div>
<?php }  ?>
<div class="modal fade" id="modalIncluded" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
						<i class="icons-office-52" id="closeIncludeBreakHoursConfAttendance"></i>
					</button>
					<h4 class="modal-title">
						<strong>BreakHours</strong> Confirmation</h4>
				</div>

				<div class="modal-body">Are you sure want to include breakhours ?
					<br>
				</div>

				<div class="modal-footer">
					<button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noIncludeBreakHoursConfAttendance">No</button>
					<button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="includeBreakHours">Yes</button>
				</div>
			</div>
		</div>
	</div>

	<div class="modal fade" id="modalExcluded" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
						<i class="icons-office-52" id="closeExcludeBreakHoursConfAttendance"></i>
					</button>
					<h4 class="modal-title">
						<strong>BreakHours</strong> Confirmation</h4>
				</div>

				<div class="modal-body">Are you sure want to exclude breakhours?
					<br>
				</div>

				<div class="modal-footer">
					<button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEXcludeBreakHoursConfAttendance">No</button>
					<button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="excludedBreakHours">Yes</button>
				</div>
			</div>
		</div>
	</div>

<?php if ($attendanceAccess['Is_Manager'] || $attendanceAccess['Admin']) {?>

<div class="modal fade" id="modalStatusAttendance" aria-hidden="true" style="z-index: 10;">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace" id="backStatusAttendance"></i>
				</button>
				<h4 class="modal-title"> Update Status</h4>
			</div>
			<div class="modal-body">
				
				<!--status Attendance Form-->
				<form role="form" class="form-horizontal form-validation" id="statusFormAttendance" method="POST" action="">
					<input type="hidden" name="statusAttendance_Id" id="formStatusAttendanceId" />
					<div class="row">
						<div class="form-group">
							<label class="col-md-3 control-label">Status <span class="short_explanation">*</span></label>
							<div class="col-md-9">
								<select class="form-control vRequired" data-search="true" name="Status" id="statusApproval">
									<option value="Approved">Approved</option>
									<option value="Returned">Returned</option>
									<option value="Rejected">Rejected</option>
								</select>
							</div>
						</div>
						<div class="form-group">
							<label class="col-md-3 control-label" id="labelStatusComment">Comment </label>
							<div class="col-md-9">
								<textarea name="comment" id="formStatusComment" rows="5" class="form-control vAlphaNumSpCDotHySlashNewLine"
										  placeholder="Write your Comment..." ></textarea>
							</div>
						</div>
						
					</div>
				</form>
				
			</div>
			<div class="modal-footer text-center">
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetStatusAttendance" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formStatusAttendance" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="modalDirtyStatusAttendance" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditStatusConfAttendance"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditStatusConfAttendance">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseConformStatusAttendance">Yes</button>
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="modalMultiStatusAttendance" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditMultiStatusConfAttendance"></i></button>
				<h4 class="modal-title"><strong>Approval</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure you want to approve the records?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditMultiStatusConfAttendance">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" data-style="expand-left"  id="multiStatusApprovalAttendance">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } }
if ($importAccess['View'] && ((!empty($customFormNameB) && array_key_exists("Enable",$customFormNameB) && $customFormNameB['Enable'] == 1) || empty($customFormNameB))) {

	?>

<div class="col-md-12 portlets add-panel-padding">
	<div class="panel" id="gridPanelAttendanceImportData">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong id="lblFormNameB"><?php echo ((!empty($customFormNameB) && !empty($customFormNameB['New_Form_Name'])) ? $customFormNameB['New_Form_Name'] : $this->formNameB);?></strong></h3>
		</div>
		<div class="panel-content">
			<!--Attendance Import data Grid Toolbar Icons-->
			<div class="m-b-10">
					
				<?php  if ($importAccess['Op_Choice']) { ?>
				
				<?php  if ($attendanceAccess['Is_Manager']) { ?>
				<button type="button" class="btn btn-white btn-embossed toolbar-icons" title="Check All" id="attImportDataCheckAll">
					<i class="hr-check-all"></i><span class="hidden-xs hidden-sm">Check All</span>
				</button>
				<?php } ?>

				<button type="button" class="btn btn-white btn-embossed visible-lg-* toolbar-icons" id="importAttendanceImportData" title="Import" >
					<i class="mdi-file-cloud-upload"></i><span class="hidden-xs hidden-sm"> Import Data</span>
				</button>
				
				<button type="button" class="btn btn-white btn-embossed visible-lg-* toolbar-icons" id="processAttendanceImportData" title="Process">
					<i class="mdi-image-tune"></i><span class="hidden-xs hidden-sm"> Process</span>
				</button>
				
				<button type="button" class="btn btn-white btn-embossed visible-lg-* toolbar-icons" id="SyncIClockDataImportData" title="Sync iClock Data" >
					<i class="hr-sync"></i><span class="hidden-xs hidden-sm"> Sync </span>
				</button>
				
				<?php  if ($attendanceAccess['Is_Manager']) {?>
				<button type="button" class="btn btn-white btn-embossed visible-lg-* toolbar-icons btnAttendanceStatus" id="cInAttendanceImportData" title="C/In" >
					<i class="hr-sign-in-1"></i><span class="hidden-xs hidden-sm"> C/In</span>
				</button>
				
				<button type="button" class="btn btn-white btn-embossed visible-lg-* toolbar-icons btnAttendanceStatus" id="cOutAttendanceImportData" title="C/Out" >
					<i class="hr-sign-out"></i><span class="hidden-xs hidden-sm"> C/Out</span>
				</button>
				
				<button type="button" class="btn btn-white btn-embossed visible-lg-* toolbar-icons btnBreakHours" id="includeBrkHrsImportData"
						title="Include Break Hours">
					<i class="hr-include-break-hours"></i><span class="hidden-xs hidden-sm"> Include Break Hours</span>
				</button>
				
				<button type="button" class="btn btn-white btn-embossed visible-lg-* toolbar-icons btnBreakHours" id="excludeBrkHrImportData"
						title="Exclude Break Hours" >
					<i class="hr-exclude-break-hours"></i><span class="hidden-xs hidden-sm"> Exclude Break Hours</span>
				</button>

				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off " id="editAttendanceImportData" title="Edit">
					<i class="mdi-editor-mode-edit"></i><span class="hidden-xs hidden-sm"> Edit</span>
				</button>
				
				<?php } } ?>
				
				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="viewAttendanceImportData" title="View">
					<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
				</button>
				
				<?php  if ($importAccess['Delete']) { ?>
				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="Delete"
						id="deleteAttendanceImportData"><i class="mdi-action-delete"></i><span class="hidden-xs hidden-sm"> Delete</span>
				</button>
				<?php } ?>
				
				<?php if ($importAccess['Is_Manager'] == 1 || $importAccess['Admin'] == 'admin') { ?>
				
				<button type="button" class="btn btn-white btn-embossed visible-lg-* toolbar-icons" id="exportAttendanceImport" title="Export">
					<i class="mdi-communication-import-export"></i><span class="hidden-xs hidden-sm"> Export</span>
				</button>
				
				<?php }?>

				<a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterImportData">
					<i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm"> Filter</span>
				</a>
				
			</div>
			
			<!-- Attendance Import data Grid Table -->
			<table class="table dataTable table-striped table-dynamic table-hover" id="tableAttendanceImportData">
				<thead>
					<tr>
						<th></th>
						<th id="attendanceImportEmployeeName">Employee Name</th>
						<th id="attendanceImportExternalEmployeeId">Biometric Integration Id</th>
						<th id="attendanceImportDateTime">Date/Time</th>
						<th id="attendanceImportStatus">Status</th>
						<th id="attendanceImportBreakHours">Break Hours</th>
						<th id="attendanceImportRollupStatus">Rollup Status</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
		</div>
	</div>
</div>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="attendanceImportData-context-menu" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextAttImportData"><i class="mdi-action-visibility"></i> View</a></li>
		<?php  if ($attendanceAccess['Is_Manager'] && $importAccess['Op_Choice']) {?>
		<li><a tabindex="-1" id="editContextAttImportData"><i class="mdi-editor-mode-edit"></i> Edit</a></li>	
		<li><a tabindex="-1" class="btnAttendanceStatus" id="cinContextAttImportData"><i class="hr-sign-in-1"></i>  C/In</a></li>
		<li><a tabindex="-1" class="btnAttendanceStatus" id="coutContextAttImportData"><i class="hr-sign-out"></i> C/Out</a></li>
		<li><a tabindex="-1" class="btnBreakHours" id="includeContextAttImportData"><i class="hr-include-break-hours"></i> Include Break Hours</a></li>
		<li><a tabindex="-1" class="btnBreakHours" id="excludeContextAttImportData"><i class="hr-exclude-break-hours"></i> Exclude Break Hours</a></li>
		<?php }  if ($importAccess['Delete']) { ?>
		<li><a tabindex="-1" id="deleteContextAttImportData"><i class="mdi-action-delete"></i> Delete</a></li>
		<?php } ?>
	</ul>
</div>

<?php 

/* when the attendance process type is 1 first in & last out method in that time below function will run*/
if($orgSettings['Attendance_Process_Type_Id'] == 1)
{ ?>

<input type="hidden" name="Attendance_Process_Method" id="formAttendanceProcessMethod" value=<?php echo $orgSettings['Attendance_Process_Type_Id'];?>>


<div class="modal fade" id="modalProcessConformation" aria-hidden="true" >
	<div class="modal-dialog">
		<div class="modal-content">
			<div id="checkboxErrorNotify"></div>
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeProcessConfirmationAttendance"></i></button>
				<h4 class="modal-title" ><strong>Process</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="formModalAttendanceProcess" method="POST" action="">
					<div class="form-group">
						<label class="col-md-4 control-label">Date range <span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<div class="col-md-5">
								<input type="text" name="processFromDate" id="processFromDate" class="date-picker form-control datePickerRead"  placeholder="From Date" >
							</div>
							<span class="col-md-1">to</span>
							<div class="col-md-5">
								<input type="text" name="processToDate" id="processToDate" class="date-picker form-control datePickerRead" placeholder="To Date" />
							</div>
						</div>
					</div>

					<div class="form-group">
						<input type="checkbox" class="md-checkbox" name="formProcessAttCheck" id="formProcessAttCheck" >
						Have you imported complete attendance data for the above date range?<b style="color: red"> To process the attendance data successfully a pair of attendance records are mandatory.</b><br>
					</div>

				</form>
			</div>
			
			<div class="modal-footer text-center">
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formProcessAttImportData" style="margin-top: 5px;">
					<i class="mdi-content-send"></i> Process
				</button>
			</div>
		</div>
	</div>
</div>


<?php } 
else
{?>

<input type="hidden" name="Attendance_Process_Method" id="formAttendanceProcessMethod" value=<?php echo $orgSettings['Attendance_Process_Type_Id']; ?>>

	<div class="modal fade" id="modalProcessConformation" aria-hidden="true" >
		<div class="modal-dialog">
			<div class="modal-content">
				<div id="checkboxErrorNotify"></div>
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeProcessConfirmationAttendance"></i></button>
					<h4 class="modal-title" ><strong>Process</strong> Confirmation</h4>
				</div>
				
				<div class="modal-body">
					<form role="form" class="form-horizontal form-validation" id="formModalAttendanceProcess" method="POST" action="">
						<div class="form-group">
							<input type="checkbox" class="md-checkbox" name="formProcessAttCheck" id="formProcessAttCheck" >
							Have you imported complete attendance data for all day?<b style="color: red"> If so with incomplete pair will be created as below selected status.</b><br>
						</div>
						<div class="form-group">
							<label class="col-md-4 control-label">Please select <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control vRequired" id="formProcessAttStatus" name="formProcessAttStatus" >
									<option value="">-- Select --</option>
									<option value="Draft">Draft</option>
								</select>
							</div>
						</div>
						<div class="form-group">
							<label class="col-md-4 control-label">Date range <span class="short_explanation">*</span></label>
							<div class="col-md-8" >
								<div class="col-md-5">
									<input type="text" name="processFromDate" id="processFromDate" class="date-picker form-control datePickerRead" placeholder="From Date" >
								</div>
								<span class="col-md-1">to</span>
								<div class="col-md-5">
									<input type="text" name="processToDate" id="processToDate" class="date-picker form-control datePickerRead" placeholder="To Date" />
								</div>
							</div>
						</div>
					</form>
				</div>
				
				<div class="modal-footer text-center">
					<button type="submit" class="btn btn-primary btn-embossed ladda-button" data-style="expand-left" id="formProcessAttImportData" style="margin-top: 5px;">
						<i class="mdi-content-send"></i> Process
					</button>
				</div>
			</div>
		</div>
	</div>

<?php }
?>




<?php if ($importAccess['Op_Choice']) { ?>
<div class="modal fade" id="modalFormSyncIClock" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true"><i class="mdi-hardware-keyboard-backspace" id="backAttendanceSync"></i></button>
				
				<h4 class="modal-title">Sync</h4>
			</div>
			
			<div class="modal-body">
				<!--Add Sync iclock data Form-->
				<form role="form" class="form-horizontal form-validation" id="editFormSyncIClock" method="POST" action="">
				<input type="hidden" name="Request_Id" id="formASRequestId" />

					<div class="row">
						
						<!--Date range for attendance import-->
						<div class="form-group">
							<label class="col-md-4 control-label">Date range <span class="short_explanation">*</span></label>
							<div class="row col-md-8" >
								<div class="col-md-5">
									<input type="text" name="from_Date" id="from_Date"  class="date-picker form-control vRequired datePickerRead" placeholder="From Date" >
								</div>
								<span class="col-md-1">to</span>
								<div class="col-md-5">
									<input type="text" name="to_Date" id="to_Date" class="date-picker form-control vRequired datePickerRead" placeholder="To Date" />
								</div>
							</div>
						</div>			

					<!--Device name for attendance import-->
					<div class="form-group">
							<label class="col-md-4 control-label">Device Name <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control vRequired" name="Device_Name" id="deviceName" data-search="true" data-placeholder="Device_Name">
									<option value ="">--Select--</option>
									<?php
										foreach ($getDeviceName as $deviceKey => $deviceRow) 
										{
											echo '<option value="'. $deviceKey .'">'. $deviceRow .'</option>';
										}
									?>
								  </select>
							</div>
                        </div>
					</div>
					
					<button type="reset" class="cancel" id="formSyncIclockReset" style="display: none;" ></button>
				</form>
				
			</div>
			<div class="modal-footer text-center" id="formActionSyncIclock">
				
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetSyncIclock" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitSyncIclock" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Extract
				</button>
				
			</div>
		</div>
	</div>
</div>

<!-- Attendance data Import Model -->
<div class="modal fade" id="modalImportAttendanceData" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="form-icons close pull-left" style="margin-right: 10px;" data-dismiss="modal" aria-hidden="true"><i class="mdi-hardware-keyboard-backspace" id="backAttendanceImportData"></i></button>
				<h4 class="modal-title"><strong>Upload</strong> <?php echo (!empty($customFormNameB) ? $customFormNameB['New_Form_Name'] : $this->formNameB);?></h4>
			</div>
			<div class="modal-body">
				<form id="formAttendanceDataImport" class="form-validation" enctype="multipart/form-data" role="form">
					<div class="form-group">
						<div class="form-group">
							<label class="col-md-4 control-label">Title<span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control vRequired" data-search="true" id="attSchemaTitle" name="attSchemaTitle" >
									<option value="">-- Select --</option>
									<?php
									foreach ($schemaTitle as $key => $row)
									{
										echo '<option value="'.$key.'">'.$row.'</option>';
									}
									?>
								</select>
							</div>
                        </div>
						
						<p><strong>Upload File<span class="short_explanation">*</span> (*.xls, *.xlsx)</strong></p>
						<div class="file">
							<div class="option-group">
								<span class="file-button btn-secondary">Choose File</span>
								<input type="file" class="custom-file vUploader vFileFormat" name="uploadAttDataFile" id="uploadDataFile"
									   onchange="document.getElementById('dataUploader').value = this.value.split('\\').pop();">
								<input type="text" class="form-control" id="dataUploader" placeholder="Upload attendance data" readonly="">
							</div>
						</div>
					</div>
					<div class="form-group text-center">
						<button type="button" class="btn btn-secondary btn-embossed toolbar-icons ladda-button" data-style="expand-left"
								id="uploadAttendanceDataImport" title="Upload">
							<i class="mdi-file-cloud-upload"></i> Upload
						</button>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>

<?php } ?>

<div class="modal fade" id="modalFormImportData" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true"><i class="mdi-hardware-keyboard-backspace" id="backViewAttendanceImportData"></i></button>
				<?php  if ($attendanceAccess['Is_Manager'] && $importAccess['Op_Choice']) {?>
				<button type="button" class="close form-icons" aria-hidden="true" id="editInViewImportData">
					<i class="mdi-editor-mode-edit"></i>
				</button>
				<?php } ?>
				<h4 class="modal-title" id="modalFormTitleImportData"></h4>
			</div>
			<div class="modal-body">
				<!--View Import Data Form-->
				<form role="form" id="viewFormImportData" >
					<div class="row">
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Employee Name</label></div>
							<div class="col-md-7"><p id="viewImportEmployeeName"></p></div>
						</div>
						<!-- <div class="form-group">
							<div class="col-md-5"><label class="control-label">Department</label></div>
							<div class="col-md-7"><p id="viewImportDepartment"></p></div>
						</div> -->
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Biometric Integration Id</label></div>
							<div class="col-md-7"><p id="viewImportExternalEmployee"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Date/Time</label></div>
							<div class="col-md-7"><p id="viewImportDateTime"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Form Source</label></div>
							<div class="col-md-7"><p id="viewImportFormSource"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Data Source</label></div>
							<div class="col-md-7"><p id="viewImportDataSource"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Geo Coordinates</label></div>
							<div class="col-md-7"><p id="viewGeoCoordinates"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Address</label></div>
							<div class="col-md-7"><p id="viewImportAddress"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Status</label></div>
							<div class="col-md-7">
								<p id="viewImportStatus"></p>
								<select class="form-control vRequired" data-search="true" id="editImportStatus" name="importDataStatus">
								</select>
							</div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Break Hours</label></div>
							<div class="col-md-7"><p id="viewImportBreakHours"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Error Message</label></div>
							<div class="col-md-7"><p id="viewErrorMessage"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Rollup Status</label></div>
							<div class="col-md-7">
								<p id="viewImportRollupStatus"></p>
								<select class="form-control vRequired" data-search="true" id="editImportRollup" name="importDataRollup" >
									
								</select>
							</div>
						</div>
					</div>

					<div class="row">
						<hr class="view-hr"/>
						
						<div class="form-group" style="font-size: large;margin-left: 13px;">
							<label class="control-label text-center">Additional Information</label>
						</div>
						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Added On</label></div>
							<div class="col-md-7"><p id="attendanceImportAddedOn"></p></div>
						</div>
						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Added By</label></div>
							<div class="col-md-7"><p id="attendanceImportAddedBy"></p></div>
						</div>
						
						<div class="form-group updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated On</label></div>
							<div class="col-md-7"><p id="attendanceImportUpdatedOn"></p></div>
						</div>
						
						<div class="form-group updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated By</label></div>
							<div class="col-md-7"><p id="attendanceImportUpdatedBy"></p></div>
						</div>
					</div>

				</form>
				
			</div>
			<div class="modal-footer text-center" id="formActionImportData">
				
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitImportData" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
				
			</div>
		</div>
	</div>
</div>


<?php if ($importAccess['Delete']) { ?>
<!-- Delete COnfirmation Modal -->
<div class="modal fade" id="modalDeleteAttendanceImportData" aria-hidden="true" >
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteConfAttendanceImport"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfAttendanceImport">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConformAttendanceImport">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } ?>

<!--Filter Form for attendance import data-->
<div class="builder" id="filterPanelImportData">
	<div id="closeFilterImportData"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel" style="width: 100%;" id="filterResetImportData">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="filterApplyImportData">Apply</button>
			
			<div class="form-group">
				<label>Employee Name</label>
				<input type="text" class="form-control" id="filterImportDataEmployeeName" >
			</div>
			<div class="form-group">
				<label>Biometric Integration Id</label>
				<input type="text" class="form-control" id="filterImportDataExtempId" >
			</div>
			<div class="form-group">
				<label>Attendance Status</label>
				<select class="form-control" data-search="true" id="filterAttendanceStatus" >
					<option value="">All</option>
					<option value="C/In" >C/In</option>
					<option value="C/Out" >C/Out</option>
				</select>
			</div>
			
			<!-- <div class="form-group">
				<label>Date</label>
				<div class="row">
					<div class="col-md-5" style="padding:10px!important">
						<input type="text" class="date-picker form-control datePickerRead" name="start" id="filterAttendanceImportDateBegin" data-orientation="top" placeholder="Begin" >
					</div>
					<span class="col-md-1" style="padding-top:25px!important">to</span>
					<div class="col-md-5" style="padding:10px!important">
						<input type="text" class="date-picker form-control datePickerRead" name="end" id="filterAttendanceImportDateEnd" data-orientation="top" placeholder="End" />
					</div>
				</div>
			</div> -->

			<div class="form-group">
				<label>Date</label>
				<div class="input-daterange b-datepicker input-group" data-date-format="<?php echo $dformat; ?>" id="datepicker">
					<input type="text" class="input-md form-control" name="start" id="filterAttendanceImportDateBegin" data-orientation="top" placeholder="Begin" />
					<span class="input-group-addon">to</span>
					<input type="text" class="input-md form-control" name="end" id="filterAttendanceImportDateEnd" data-orientation="top" placeholder="End" />
				</div>
			</div>

			<div class="form-group">
				<label>Location</label>
				<select class="form-control" data-search="true" id="filterLocation" >
					<option value="">All</option>
					<?php
					foreach ($empLocation as $key => $row)
					{
						echo '<option value="'.$key.'">'.$row.'</option>';
					}
					?>
				</select>
			</div>

			<?php if ($orgSettings['Field_Force'] == 1) { ?>
			<div class="form-group">
				<label>Service Provider</label>
				<select class="form-control" data-search="true" id="filterAIServiceProvider">
					<option value="">All</option>
					<?php
					foreach ($serviceProvider as $key => $row)
					{
						echo '<option value="'. $key .'">'. $row .'</option>';
					}
					?>
				</select>												
			</div>
										
			<?php } ?>

			<div class="form-group">
				<label>Data Source</label>
				<select class="form-control" data-search="true" id="filterDataSource" >
					<option value="">All</option>
					<?php
					for($i=0;$i<count($importDataSource);$i++)
					{
						echo '<option value="'. $importDataSource[$i]['Data_Source'] .'">'. $importDataSource[$i]['Data_Source'].'</option>';
					}
					?>
				</select>
			</div>

			<div class="form-group">
				<label>Form Source</label>
				<select class="form-control" data-search="true" id="filterFormSource" >
					<option value="">All</option>
					<?php
						for($i=0;$i<count($formSourceList);$i++)
						{
							echo '<option value="'. $formSourceList[$i]['Form_Id'] .'">'. $formSourceList[$i]['Form_Name'].'</option>';
						}
				 	?>
				</select>
			</div>
			
			<div class="form-group">
				<label>Rollup Status</label>
				<select class="form-control" data-search="true" id="filterRollupStatus" >
					<option value="">All</option>
					<option value="0" >Unprocessed</option>
					<option value="1" >Processed</option>
					<option value="-1" >Invalid Record</option>
					<option value="2" >Manual Process</option>
					<option value="3">Already Exist</option>
				</select>
			</div>

			<div class="form-group">
				<label>Error Message</label>
				<select class="form-control" data-search="true" id="filterErrorMessage" >
					<option value="">All</option>
					<?php
					foreach ($attendanceImportErrorCode as $errorCode => $errorMessage) 
					{
						echo '<option value="'. $errorCode .'">'. $errorMessage .'</option>';
					}?>
				</select>
			</div>
			
		</div>
	</div>
</div>

<!--Confirmation msg for attendance import data include/exclude break hours-->
<div class="modal fade" id="modalBrkHrsImportData" aria-hidden="true" >
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeChangeConformBrkHrsImportData"></i></button>
				<h4 class="modal-title"><strong>Change</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to change break hours?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noChangeConformBrkHrsImportData">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="changeConformBrkHrsImportData">Yes</button>
			</div>
		</div>
	</div>
</div>

<!--Confirmation msg for attendance import data Cin/Cout attendance status-->
<div class="modal fade" id="modalAttStatusImportData" aria-hidden="true" >
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeChangeConformAttStatusImportData"></i></button>
				<h4 class="modal-title"><strong>Change</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to change attendance status?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noChangeConformAttStatusImportData">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="changeConformAttStatusImportData">Yes</button>
			</div>
		</div>
	</div>
</div>

<div class="col-md-12 portlets add-panel-padding">
	<div class="panel" id="gridPanelAttendanceSchema">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i><strong>Import </strong>Format</h3>
		</div>
		<div class="panel-content">
			<!--Attendance Schema Grid Toolbar Icons-->
			<div class="m-b-10">
				<?php  if ($importAccess['Op_Choice']) { ?>
				<button type="button" class="btn btn-white btn-embossed visible-lg-* toolbar-icons" id="importAttendanceSchema"
						title="Import" >
					<i class="mdi-file-cloud-upload"></i><span class="hidden-xs hidden-sm"> Import Format</span>
				</button>
				<?php } ?>
				
				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="viewAttendanceSchema" title="View">
					<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
				</button>
				
				<?php  if ($importAccess['Delete']) { ?>
				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="Delete"
						data-toggle="modal" data-target="#modalDeleteAttendanceSchema" id="deleteAttendanceSchema">
					<i class="mdi-action-delete"></i><span class="hidden-xs hidden-sm"> Delete</span>
				</button>
				<?php } ?>
				
			</div>
			
			
			<!-- Attendance Schema Grid Table -->
			<table class="table dataTable table-striped table-dynamic table-hover" id="tableAttendanceSchema">
				<thead>
					<tr>
						<th></th>
						<th id="attendanceSchemaName">Schema Name</th>
						<th id="attendanceSchemaEmployeeName">Employee Name</th>
						<th id="attendanceSchemaExternalEmployeeId" >Biometric Integration Id</th>
						<th id="attendanceSchemaStatus">Status</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
		</div>
	</div>
</div>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="attendanceImportFormat-context-menu" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextAttendanceSchema"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($importAccess['Delete']) { ?>
		<li><a tabindex="-1" id="deleteContextAttendanceSchema"><i class="mdi-action-delete"></i> Delete</a></li>
		<?php } ?>
	</ul>
</div>

<!-- Attendance format Import Model -->
<div class="modal fade" id="modalImportAttendanceFormat" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="form-icons close pull-left" style="margin-right: 10px;" data-dismiss="modal" aria-hidden="true"><i class="mdi-hardware-keyboard-backspace" id="backImportFormat"></i></button>
				<h4 class="modal-title"><strong>Upload</strong> Attendance Format File</h4>
			</div>
			<div class="modal-body">
				<form id="formAttendanceFormatImport" class="form-validation" enctype="multipart/form-data" role="form">
					<div class="form-group">
						<div class="form-group">
							<label class="col-md-4 control-label">Title<span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<input type="text" name="formatTitle" class="form-control" id="formAttFormatTitle" required>
							</div>
                        </div>
						
						<p><strong>Upload File<span class="short_explanation">*</span> (*.xls, *.xlsx)</strong></p>
						<div class="file">
							<div class="option-group">
								<span class="file-button btn-secondary">Choose File</span>
								<input type="file" class="custom-file vUploader vFileFormat" name="uploadAttFormatFile" id="uploadFormatFile"
									   onchange="document.getElementById('formatUploader').value = this.value.split('\\').pop();">
								<input type="text" class="form-control" id="formatUploader" placeholder="Upload attendance format" readonly="">
							</div>
						</div>
					</div>
					<div class="form-group text-center">
						<button type="button" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="uploadAttendanceFormatImport">
							<i class="mdi-file-cloud-upload"></i> Upload
						</button>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="modalFormAttendanceSchema" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true"><i class="mdi-hardware-keyboard-backspace" id="backAttendanceSchema"></i></button>
				
				<h4 class="modal-title"> View Attendance schema</h4>
			</div>
			<div class="modal-body">
				<!--View Attendance Schema Form-->
				<form role="form" id="viewFormAttendanceSchema" >
					<div class="row">
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Schema Name</label></div>
							<div class="col-md-7"><p id="viewSchemaName"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Department</label></div>
							<div class="col-md-7"><p id="viewDepartment"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Employee Name</label></div>
							<div class="col-md-7"><p id="viewSchemaEmployeeName"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Employee Id</label></div>
							<div class="col-md-7"><p id="viewEmployeeId"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Added On</label></div>
							<div class="col-md-7"><p id="viewSchemaAddedOn"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Attendance Status</label></div>
							<div class="col-md-7"><p id="viewAttendanceStatus"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Location Id</label></div>
							<div class="col-md-7"><p id="viewLocationId"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Id Number</label></div>
							<div class="col-md-7"><p id="viewIdNumber"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">WorkSchedule Id</label></div>
							<div class="col-md-7"><p id="viewWorkScheduleId"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Verify Code</label></div>
							<div class="col-md-7"><p id="viewVerifyCode"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Card No</label></div>
							<div class="col-md-7"><p id="viewCardNo"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Date Format</label></div>
							<div class="col-md-7"><p id="viewDateFormat"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-7"><label class="control-label">Attendance Status Pairs</label></div>
						</div>
						
					</div>
					
					<table class="table dataTable table-striped table-dynamic table-hover" id="tableStatusPair">
					<thead>
						<tr>
							<th id="attendanceStatusPairA">Status Pair - A</th>
							<th id="attendanceStatusPairB">Status Pair - B</th>
						</tr>
					</thead>
					<tbody>
					
					</tbody>
				</table>
				
				</form>
			</div>
			
		</div>
	</div>
</div>

<div class="modal fade" id="modalFormImportFormat" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true"><i class="mdi-hardware-keyboard-backspace" id="backEditImportFormat"></i></button>
				
				<h4 class="modal-title"> Upload File Field Mapping</h4>
			</div>
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="editFormImportFormat" method="POST" action="">
					<div class="row">
						<input type="hidden" name="formTblHeader" id="formTblHeader" value="<?php echo count($importFormatTblHeader);  ?>">
						<input type="hidden" name="schemaName" id="formTblHeaderSchema">
						<input type="hidden" name="uploadedFileExtension" id="formTblFileExtension">
						<input type="hidden" name="csvDataArray" id="formTblHeaderCSVArray">
						<input type="hidden" name="formTblHeaderCellFormatArray" id="formTblHeaderCellFormatArray">
						<?php
						foreach ($importFormatTblHeader as $key => $row)
						{
						?>
						<div class="form-group">
							<label class="col-md-4 control-label"><?php echo str_replace("_"," ",$row); 
							if(in_array($row, array('Employee_Id', 'Added_On', 'Attendance_Status')))
							{
							?>
								<span class="short_explanation">*</span>	
							
							<?php } ?>
							</label>
							<div class="col-md-8">
								<select class="form-control" data-search="true" name="<?php echo $row; ?>" id="field_<?php echo $key;?>" >
									<option value="">-- Select --</option>
								</select>
							</div>
                        </div>	
						<?php }?>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Date Format Seperator<span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control" data-search="false" name="dateSep" id="dateSep">
									<option value=":">:</option>
									<option value="/">/</option>
									<option value="-">-</option>
									<option value=".">.</option>
									<option value=",">,</option>
								</select>
							</div>
                        </div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Date Format<span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<?php
									$dateFormatOption = '<option value="d">Day</option>
														 <option value="m">Month</option>
														 <option value="Y">Year</option>';
									
									for ($i=0; $i<3; $i++)
									{ ?>
									<div class="col-md-4">
										<select class="form-control" data-search="false" name="date_<?php echo $i; ?>" id="date_<?php echo $i; ?>" >
											<option value=''>-- Select--</option>
											<?php echo $dateFormatOption; ?>
										</select>
									</div>
								<?php
									}
								?>
							</div>
                        </div>
						
					</div>
					
					<button type="reset" class="cancel" id="formImportFormatReset" style="display: none;" ></button>
				</form>
			</div>
			<div class="modal-footer text-center">
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetImportFormat" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitImportFormat" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
				
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="modalFormImportCheckIn" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true"><i class="mdi-hardware-keyboard-backspace" id="backCheckinImportFormat"></i></button>
				
				<h4 class="modal-title">File Field Mapping</h4>
			</div>
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="editFormImportCheckIn" method="POST" action="">
					<div class="row">
						<input type="hidden" name="checkInSchemaId" id="formCheckInSchemaId">
						
						<div class="form-group col-md-12">
							<label class="col-md-6 control-label">Check-In Status</label>
							<label class="col-md-6 control-label">Check-Out Status</label>
                        </div>
						
						<div class="form-group col-md-12">
							<div class="col-md-5">
								<select class="form-control vRequired" data-search="true" name="checkIn" id="formImportCheckIn">
									<option value="">-- Select --</option>
								</select>
							</div>
							<div class="col-md-5">
								<select class="form-control vRequired" data-search="true" name="checkOut" id="formImportCheckOut">
									<option value="">-- Select --</option>
								</select>
							</div>
							<div class="col-md-2">
								<button type="reset" class="cancel" id="formStatusPairReset" style="display: none;" ></button>
								<button type="submit" class="btn btn-secondary btn-embossed" id="formSubmitMappingSubGrid" style="bottom: 5px;">
									<i class="mdi-content-send"></i> Submit
								</button>
							</div>
                        </div>
					</div>
					
				</form>
				
				<!-- attendance field Mapping punchIn/PunchOut Grid Table -->
				<table class="table dataTable table-striped table-dynamic table-hover" id="tableCheckInMapping">
					<thead>
						<tr>							
							<th></th>
							<th id="attendanceCheckin">Check In</th>
							<th id="attendanceCheckOut">Check Out</th>
						</tr>
					</thead>
					<tbody>
					
					</tbody>
				</table>
				
			</div>
		</div>
	</div>
</div>

<?php  if ($importAccess['Delete']) { ?>
<!-- Delete COnfirmation Modal -->
<div class="modal fade" id="modalDeleteAttendanceSchema" aria-hidden="true" >
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteConformAttendanceSchema"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConformAttendanceSchema">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConformAttendanceSchema">Yes</button>
			</div>
		</div>
	</div>
</div>

<!-- Delete COnfirmation Modal -->
<div class="modal fade" id="modalDeleteStatusPair" aria-hidden="true" >
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52"  id="closeDeleteConformStatusPair"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConformStatusPair">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConformStatusPair">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } }

if ($attendanceSettingsAccess['View'] && ((!empty($customFormNameC) && array_key_exists("Enable",$customFormNameC) && $customFormNameC['Enable'] == 1) || empty($customFormNameC))) {
 ?>

<!--Attendance Settings Grid Panel-->
<div class="col-md-12 portlets add-panel-padding">
	<div class="panel" id="gridPanelAttendanceSetting">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong id="lblFormNameC"><?php echo ((!empty($customFormNameC) && !empty($customFormNameC['New_Form_Name'])) ? $customFormNameC['New_Form_Name'] : $this->formNameC)?></strong></h3>
		</div>
		<div class="panel-content">
			<!--Attendance Setting Grid Toolbar Icons-->
				<div class="m-b-10">
					
					<?php if ($attendanceSettingsAccess['Add']) { ?>
					<button type="button" class="btn btn-white btn-embossed toolbar-icons" title="Add" data-toggle="modal" data-target="#modalFormAttendanceSettings" id="addAttendanceSettings">
						<i class="mdi-content-add"></i><span class="hidden-xs hidden-sm"> Add</span>
					</button>
					<?php } ?>
					
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="viewAttendanceSettings" title="View">
						<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
					</button>
					
					<?php if ($attendanceSettingsAccess['Update']) { ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="editAttendanceSettings" title="Edit">
						<i class="mdi-editor-mode-edit"></i><span class="hidden-xs hidden-sm"> Edit</span>
					</button>
					<?php } if ($attendanceSettingsAccess['Delete']) { ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="Delete" id="deleteAttendanceSettings">
						<i class="mdi-action-delete"></i><span class="hidden-xs hidden-sm"> Delete</span>
					</button>
					<?php } ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="BreakHours" id="breakHoursAttendanceSettings">
						<i class="fa fa-table" info-outline"></i><span class="hidden-xs hidden-sm"> BreakHours</span>
					</button>
					
					<a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterAttendanceSettings">
						<i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm"> Filter</span>
					</a>
				
				</div>
			
			<!-- Attendance Settings Grid Table -->
			<table class="table dataTable table-striped table-dynamic table-hover" id="tableAttendanceSettings">
				<thead>
					<tr>
						<th></th>
						<th id="attendanceSettingsConfigurationType">Configuration Type</th>
						<th id="attendanceSettingsWorkSchedule">Work Schedule</th>
						<th id="attendanceSettingsCustomGroup">Custom Group</th>
						<th id="attendanceSettingsAlternativeLeave">Alternative Leave Type</th>
						<th id="attendanceSettingsStatusUpdate">Status</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>

		</div>
	</div>
</div>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="attendance-settings-context-menu" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextAttendanceSettings"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($attendanceSettingsAccess['Update']) { ?>
		<li><a tabindex="-1" id="editContextAttendanceSettings"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<?php } if ($attendanceSettingsAccess['Delete']) { ?>
		<li><a tabindex="-1" id="deleteContextAttendanceSettings"><i class="mdi-action-delete"></i> Delete</a></li>
		<?php } ?>
	</ul>
</div>

<!--Filter Form-->
<div class="builder" id="filterPanelAttendanceSettings">
	<div id="closeFilterAttendanceSettings"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel" style="width: 100%;" id="filterResetAttendanceSettings">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="filterApplyAttendanceSettings">Apply</button>
			
			<div class="form-group">
				<label>Work Schedule</label>
				<select class="form-control" data-search="true" id="filterASWorkSchedule" >
					<option value="">All</option>
					<?php
					foreach ($workschedule as $key => $row)
					{
						echo '<option value="'.$key.'">'.$row.'</option>';
					}
					?>
				</select>
			</div>
			
			<div class="form-group">
				<label>Delayed Entry From</label>
				<div class="input-group">
					<input type="number" class="input-md form-control" name="filterASDelayedEntryStart" id="filterASDelayedEntryStart" data-orientation="top" placeholder="Start"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="input-md form-control" name="filterASDelayedEntryEnd"  id="filterASDelayedEntryEnd" data-orientation="top" placeholder="End"/>
				</div>
			</div>

			<div class="form-group">
				<label>Delayed Entry Upto</label>
				<div class="input-group">
					<input type="number" class="input-md form-control" name="filterASDelayedEntryUptoStart" id="filterASDelayedEntryUptoStart" data-orientation="top" placeholder="Start"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="input-md form-control" name="filterASDelayedEntryUptoEnd"  id="filterASDelayedEntryUptoEnd" data-orientation="top" placeholder="End"/>
				</div>
			</div>
			
			<div class="form-group">
				<label>Delayed Entry Max Limit</label>
				<div class="input-group">
					<input type="number" class="input-md form-control" name="filterASDelayedEntryMaxLimitStart" id="filterASDelayedEntryMaxLimitStart" data-orientation="top" placeholder="Start"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="input-md form-control" name="filterASDelayedEntryMaxLimitEnd"  id="filterASDelayedEntryMaxLimitEnd" data-orientation="top" placeholder="End"/>
				</div>
			</div>
			
			<div class="form-group">
				<select class="form-control" data-search="true" id="filterASMaxLimitPeriod" >
					<option value="Per Month">Per Month</option>
					<option value="Per Year">Per Year</option>
				</select>
			</div>
			
			<div class="form-group">
				<label>Total Days</label>
				<div class="input-group">
					<input type="number" class="input-md form-control" name="filterASTotalDaysStart" id="filterASTotalDaysStart" data-orientation="top" placeholder="Start"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="input-md form-control" name="filterASTotalDaysEnd"  id="filterASTotalDaysEnd" data-orientation="top" placeholder="End"/>
				</div>
			</div>
			
			<div class="form-group">
				<label>Status</label>
				<select class="form-control" data-search="true" id="filterASStatus" >
					<option value="">All</option>
					<option value="Active">Active</option>
					<option value="InActive">InActive</option>
				</select>
			</div>
			
		</div>
	</div>
</div>

<!-- Modal for Add Form, View Form, Edit Form -->
<div class="modal fade" id="modalFormAttendanceSettings" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;"
						aria-hidden="true"><i class="mdi-hardware-keyboard-backspace" id="backAttendanceSettings"></i></button>
				
				<?php if ($attendanceSettingsAccess['Update']) { ?>
				<button type="button" class="close form-icons" aria-hidden="true" id="editInViewAttendanceSettings">
					<i class="mdi-editor-mode-edit"></i>
				</button>
				<?php } ?>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<!--View Attendance Settings Form-->
				<form role="form" id="viewFormAttendanceSettings" >
					<div class="row">
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Configuration Type</label></div>
							<div class="col-md-7"><p id="viewConfigurationType"></p></div>
						</div>
						<div class="form-group viewShortageType">
							<div class="col-md-5"><label class="control-label">Custom Group</label></div>
							<div class="col-md-7"><p id="viewCustomGroup"></p></div>
						</div>
						<div class="form-group viewShortageType">
							<div class="col-md-5"><label class="control-label">Shift Margin</label></div>
							<div class="col-md-7"><p id="viewShiftMargin"></p></div>
						</div>
						<div class="form-group viewShortageType">
							<div class="col-md-5"><label class="control-label">Quarter Day Applied for Shortage Hours From</label></div>
							<div class="col-md-3"><p id="viewQuarterDayHoursFrom"></p></div>
							<label class="col-md-1 control-label"> To </label>
							<div class="col-md-3"><p id="viewQuarterDayHoursTo"></p></div>
						</div>
						<div class="form-group viewShortageType">
							<div class="col-md-5"><label class="control-label">Half Day Applied for Shortage Hours From</label></div>
							<div class="col-md-3"><p id="viewHalfDayHoursFrom"></p></div>
							<label class="col-md-1 control-label"> To </label>
							<div class="col-md-3"><p id="viewHalfDayHoursTo"></p></div>
						</div>
						<div class="form-group viewShortageType">
							<div class="col-md-5"><label class="control-label">Full Day Applied for Shortage Hours From</label></div>
							<div class="col-md-3"><p id="viewFullDayHoursFrom"></p></div>
							<label class="col-md-1 control-label"> To </label>
							<div class="col-md-3"><p id="viewFullDayHoursTo"></p></div>
						</div>
						<div class="form-group viewLateAttendanceType">
							<div class="col-md-5"><label class="control-label">Work Schedule</label></div>
							<div class="col-md-7"><p id="viewASWorkSchedule"></p></div>
						</div>
						<div class="form-group viewLateAttendanceType">
							<div class="col-md-5"><label class="control-label">Delayed Entry From (In minutes)</label></div>
							<div class="col-md-7"><p id="viewASDelayedEntry"></p></div>
						</div>
						<div class="form-group viewLateAttendanceType">
							<div class="col-md-5"><label class="control-label">Delayed Entry Upto (In minutes)</label></div>
							<div class="col-md-7"><p id="viewASDelayedEntryUpto"></p></div>
						</div>
						<div class="form-group viewLateAttendanceType">
							<div class="col-md-5"><label class="control-label">Delayed Entry Maximum Limit</label></div>
							<div class="col-md-7"><p id="viewASDelayedEntryMaxLimit"></p></div>
						</div>
						<div class="form-group viewLateAttendanceType">
							<div class="col-md-5"><label class="control-label">Auto Short Time Off</label></div>
							<div class="col-md-7"><p id="viewASAutoShortTimeOff"></p></div>
						</div>
						<div class="form-group viewLateAttendanceType">
							<div class="col-md-5"><label class="control-label">Total Days</label></div>
							<div class="col-md-7"><p id="viewASTotalDays"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Leave Type</label></div>							
							<div class="col-md-7"><div id="viewLeaveTypes" style="margin: 0px 0px 10px -15px;"></div></div>
						</div>

						<div class="form-group viewLateAttendanceType">
							<div class="col-md-5"><label class="control-label">Initiate leave for late attendance after every</label></div>
							<div class="col-md-7"><p id="viewLateAttendanceLeaveFrequency"></p></div>
						</div>

						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Alternative Leave Type</label></div>
							<div class="col-md-7"><p id="viewASAlternativeLeaveType"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Status</label></div>
							<div class="col-md-7"><p id="viewASStatus"></p></div>
						</div>
					</div>
					
					<div class="row">
						<hr class="view-hr"/>
						
						<div class="form-group" style="font-size: large;margin-left: 13px;">
							<label class="control-label text-center">Additional Information</label>
						</div>
						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Added On</label></div>
							<div class="col-md-7"><p id="addedOnAttendanceSettings"></p></div>
						</div>
						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Added By</label></div>
							<div class="col-md-7"><p id="addedByAttendanceSettings"></p></div>
						</div>
						
						<div class="form-group updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated On</label></div>
							<div class="col-md-7"><p id="updatedOnAttendanceSettings"></p></div>
						</div>
						
						<div class="form-group updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated By</label></div>
							<div class="col-md-7"><p id="updatedByAttendanceSettings"></p></div>
						</div>
					</div>
				</form>
				
				<?php if ($attendanceSettingsAccess['Add'] == 1 || $attendanceSettingsAccess['Update'] == 1) { ?>
				
				<!--Add/Edit Attendance Settings Form-->
				<form role="form" class="form-horizontal form-validation" id="editFormAttendanceSettings" method="POST" action="">
					<input type="hidden" name="AS_Attendance_Settings_Id" id="formASAttendanceSettingsId" />
					<div class="row">
						<div class="form-group">
							<label class="col-md-5 control-label">Configuration Type <span class="short_explanation">*</span></label>
							<div class="col-md-7">
								<select class="form-control vRequired" id="formConfigurationType" name="formConfigurationType">									
									<option value="Late Attendance">Late Attendance</option>
									<option value="Attendance Shortage">Attendance Shortage</option>
								</select>
							</div>
                        </div>

						<div class="form-group shortageType">
							<label class="col-md-5 control-label">Custom Group<span class="short_explanation">*</span>
							</label>
							<div class="col-md-7">
								<select class="form-control shortageField" data-search="true" id="formCustomGroup" name="Custom_Group" >
									<option value="">-- Select --</option>
									<?php
									foreach ($customGroup as $key => $row)
									{
										echo '<option value="'.$key.'">'.$row.'</option>';
									}
									?>
								</select>
							</div>
                        </div>

						<div class="form-group shortageType">
							<div class="col-md-5">
								<label class="col-md-5 control-label" style=" width: 100%;margin-left: -15px;">Consider Shift Margin For Shortage Calculation <span class="short_explanation">*</span></label>
							</div>
							<div class="col-md-7">
								<select class="form-control shortageField" id="formShiftMargin" name="formShiftMargin">									
									<option value="Yes">Yes</option>
									<option value="No">No</option>
								</select>
							</div>
                        </div>

						<div class="form-group shortageType">
							<label class="col-md-5 control-label">Quarter Day Leave Applied For The Shortage Hours From </label>
							<div class="col-md-3">
								<input type="text" name="timepicker" id="formQuarterDayHoursFrom" placeholder="From"
								class="form-control FormatTime">
							</div>
							<label class="col-md-1 control-label"> To </label>
							<div class="col-md-3">
								<input type="text" name="timepicker" id="formQuarterDayHoursTo" placeholder="To"
								class="form-control FormatTime">
							</div>
						</div>

						<div class="form-group shortageType">
							<label class="col-md-5 control-label">Half Day Leave Applied For The Shortage Hours From <span class="short_explanation">*</span></label>
							<div class="col-md-3">
								<input type="text" name="timepicker" id="formHalfDayHoursFrom" placeholder="From"
								class="form-control shortageField FormatTime">
							</div>
							<label class="col-md-1 control-label"> To </label>
							<div class="col-md-3">
								<input type="text" name="timepicker" id="formHalfDayHoursTo" placeholder="To"
								class="form-control shortageField FormatTime">
							</div>
						</div>
						
						<div class="form-group shortageType">
							<label class="col-md-5 control-label">Full Day Leave Applied For The Shortage Hours From <span class="short_explanation">*</span></label>
							<div class="col-md-3">
								<input type="text" name="timepicker" id="formFullDayHoursFrom" placeholder="From"
								class="form-control shortageField FormatTime">
							</div>
							<label class="col-md-1 control-label"> To </label>
							<div class="col-md-3">
								<input type="text" name="timepicker" id="formFullDayHoursTo" placeholder="To"
								class="form-control shortageField FormatTime">
							</div>
						</div>

						

						

						<!-- <div class="form-group shortageType">
							<label class="col-md-5 control-label">Override Regular Hours Per Day <span class="short_explanation">*</span></label>
							<div class="col-md-7">
								 <input type="text" name="timepicker" id="formFullDayHours" placeholder="Override Regular Hours Per Day"
								 class="form-control shortageField overtimeDayHours timeFrom FormatTime">
							</div>
                        </div>

						<div class="form-group shortageType">
							<label class="col-md-5 control-label">Override Regular Hours Per Half A Day <span class="short_explanation">*</span></label>
							<div class="col-md-7">
								<input type="text" name="timepicker" id="formHalfDayHours" placeholder="Override Regular Hours Per Half A Day"
								 class="form-control shortageField overtimeHalfDayHours timeFrom FormatTime">
							</div>
						</div> -->

						<div class="form-group lateAttendanceType">
							<label class="col-md-5 control-label">Work Schedule <span class="short_explanation">*</span></label>
							<div class="col-md-7">
								<select class="form-control lateAttendanceField" data-search="true" id="formASWorkScheduleId" name="AS_WorkSchedule_Id" >
									<option value="">-- Select --</option>
									<?php
									foreach ($workschedule as $key => $row)
									{
										echo '<option value="'.$key.'">'.$row.'</option>';
									}
									?>
								</select>
							</div>
                        </div>
						
						<div class="form-group lateAttendanceType">
							<label class="col-md-5 control-label">Delayed Entry From <span class="short_explanation">*</span></label>
							<div class="col-md-3">
								<input type="number" class="form-control lateAttendanceField" min="1" max="240"  id="formASDelayedEntry" name="AS_Delayed_Entry" placeholder="Delayed Entry From">
							</div>
							<label class="col-sm-4 control-label">Minutes after the regular schedule</label>
                        </div>

						<div class="form-group lateAttendanceType">
							<label class="col-md-5 control-label">Delayed Entry Upto <span class="short_explanation">*</span></label>
							<div class="col-md-3">
								<input type="number" class="form-control lateAttendanceField" min="1" max="28"  id="formDelayedEntryUpto" name="Delayed_Entry_Upto" placeholder="Delayed Entry Upto">
							</div>
							<label class="col-sm-4 control-label">Minutes after the regular schedule</label>
                        </div>
						
						<div class="form-group lateAttendanceType">
							<label class="col-md-5 control-label">Delayed Entry Max Limit <span class="short_explanation">*</span></label>
							<div class="col-md-3">
								<input type="number" class="form-control lateAttendanceField" min="0"  id="formASDelayedEntryMaxLimit" name="AS_Delayed_Entry_Max_Limit" placeholder="Delayed Entry Max Limit">
							</div>
							<div class="col-md-4">
								<select class="form-control lateAttendanceField" data-search="true" id="formASDelayedEntryPeriod" name="AS_Delayed_Entry_Period" >
									<option value="Per Month">Per Month</option>
									<option value="Per Year">Per Year</option>
								</select>
							</div>
						</div>

						<div class="form-group lateAttendanceType">
							<label class="col-md-5 control-label">Initiate leave for late attendance after every<span class="short_explanation">*</span></label>
							<div class="col-md-3">
								<input type="number" class="form-control lateAttendanceField" min="0"  id="formLateAttendanceLeaveFrequency" name="Late_Attendance_Leave_Frequency" placeholder="">
							</div>
							<label class="col-sm-4 control-label">days of delayed entry.(This policy is applied after the initial grace period)</label>
						</div>

						<div class="form-group lateAttendanceType">
							<div class="col-md-5">
								<label class="col-md-5 control-label" style=" width: 100%;margin-left: -15px;">Auto Short Time Off<span class="short_explanation">*</span></label>
							</div>
							<div class="col-md-7">
								<select class="form-control autoShortTimeOff" id="formAutoShortTimeOff" name="formAutoShortTimeOff">									
									<option value="Yes">Yes</option>
									<option value="No">No</option>
								</select>
							</div>
                        </div>

						<div class="form-group lateAttendanceType">
							<label class="col-md-5 control-label">Total Days <span class="short_explanation">*</span></label>
							<div class="col-md-7">
								<select class="form-control lateAttendanceField" data-search="true" id="formASTotalDays" name="AS_Total_Days" >
									<option value="0.25">Quarter day</option>
									<option value="0.50">Half day</option>
									<option value="1.00">Full day</option>
								</select>
							</div>
						</div>

						<div class="form-group" id= "paidLeave">
							<label class="col-md-5 control-label">Leave Type <span class="short_explanation">*</span>
								<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
																		data-placement="top" data-content="Click plus(+) button to select leave types. Priority of leave types is based on the order of leave types selected ">
								</i>
							</label>	
							<div class="col-md-7" style="display: flex"> 
								<form role="form" autocomplete="off">
									<div style="width: 80%;">
										<select class="form-control" data-search="true"id="formASLeaveTypeId" name="AS_LeaveType_Id" placeholder="Leave Type" >
											<option value=''>--Select--</option>
											<?php
												foreach ($leaveType as $key => $row)
												{
													echo '<option value="'.$key.'">'.$row.'</option>';
												}
											?>
										</select>
									</div>
									<div style="margin: 5px 0px 0px 15px;">
										<i class="icon hr-add" id="addLeaveType" style="font-size: 20px; color: #337ab7;"></i>
									</div>
								</form>
							</div>
						</div>
						
						<div class="form-group"><div class="col-md-12" id="leaveTypeArea"></div></div>
						
						<div class="form-group">
							<label class="col-md-5 control-label">Alternative Leave Type <span class="short_explanation">*</span></label>
							<div class="col-md-7">
								<select class="form-control vRequired" data-search="true" id="formASAlternativeLeaveTypeId" name="AS_Alternative_LeaveType_Id" >
									<option value="">-- Select --</option>
									<?php
									foreach ($unpaidLeaveType as $key => $row)
									{
										echo '<option value="'.$key.'">'.$row.'</option>';
									}
									?>
								</select>
							</div>
                        </div>
						
						<!-- <div class="form-group lateAttendanceType">
							<label class="col-md-4 control-label">Enable Email Notification</label>
							<div class="col-md-8">
								<div class="form-group togglebutton">
									<label>
									  <input type="checkbox" style="margin-top: 15px; margin-left: 15px;" class="col-sm-9 md-checkbox" name="EnableEmailNotification" id="EnableEmailNotification" >
									</label>
								  </div>
							</div>
                        </div> -->
						
						<!-- <div class="form-group lateAttendanceType" id="notifyPersonPanel">
							<label class="col-md-4 control-label">Email Notification Person <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select multiple="multiple" class="form-control selectAlll" data-search="true" id="formASNotificationPerson" name="formASNotificationPerson">
									<option value="selectAll">--Select all--</option>
									<option value="clearAll">--Clear all--</option>
								</select>
							</div>
                        </div> -->
						
						<div class="form-group">
							<label class="col-md-5 control-label">Status</label>
							<div class="col-md-7">
								<select class="form-control" id="formAttendanceSettingsStatus" name="AS_Status" >
									
								</select>
							</div>
                        </div>
						
					</div>
					
					<button type="reset" class="cancel" id="formASReset" style="display: none;" ></button>
				</form>
				
				<?php } ?>
				
			</div>
			<div class="modal-footer text-center" id="formActionAttendanceSettings">
				
				<?php if ($attendanceSettingsAccess['Add'] == 1 || $attendanceSettingsAccess['Update'] == 1) { ?>
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetAttendanceSettings" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitAttendanceSettings" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
				<?php } ?>
				
			</div>
		</div>
	</div>
</div>


<!-- Form Dirty Confirmation Modal -->
<?php if ($attendanceSettingsAccess['Add'] || $attendanceSettingsAccess['Update']) { ?>
<div class="modal fade" id="modalDirtyAttendanceSettings" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditConfAttendanceSettings"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfAttendanceSettings">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseConformAttendanceSettings">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } if ($attendanceSettingsAccess['Delete']) { ?>
<!-- Delete COnfirmation Modal -->
<div class="modal fade" id="modalDeleteAttendanceSettings" aria-hidden="true" >
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteConfAttendanceSettings"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfAttendanceSettings">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConformAttendanceSettings">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } } if ($attendanceAccess['View'] != 1 && $importAccess['View'] != 1 && $attendanceSettingsAccess['View'] != 1) { ?>

<div class="col-md-12 portlets">
	<div class="txt_center">Sorry, Access Denied...</div>
</div>

<?php
}
if ($deviceManagementAccess['View'] && ((!empty($customFormNameE) && array_key_exists("Enable",$customFormNameE) && $customFormNameE['Enable'] == 1) || empty($customFormNameE))) {
 ?>
<!--Device management Grid Panel-->
<div class="col-md-12 portlets add-panel-padding">
	<div class="panel" id="gridPanelDeviceManagement">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong id="lblFormNameE"><?php echo ((!empty($customFormNameE) && !empty($customFormNameE['New_Form_Name'])) ? $customFormNameE['New_Form_Name'] : $this->formNameE)?></strong></h3>
		</div>
		<div class="panel-content">
			<!--Device Management Grid Toolbar Icons-->
				<div class="m-b-10">
					
					<?php if ($deviceManagementAccess['Add']) { ?>
					<button type="button" class="btn btn-white btn-embossed toolbar-icons" title="Add" data-toggle="modal" data-target="#modalFormDeviceManagement" id="addDeviceManagement">
						<i class="mdi-content-add"></i><span class="hidden-xs hidden-sm"> Add</span>
					</button>
					<?php } ?>
					
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="viewDeviceManagement" title="View">
						<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
					</button>
					
					<?php if ($deviceManagementAccess['Update']) { ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="editDeviceManagement" title="Edit">
						<i class="mdi-editor-mode-edit"></i><span class="hidden-xs hidden-sm"> Edit</span>
					</button>
					<?php } if ($deviceManagementAccess['Delete']) { ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="Delete" id="deleteDeviceManagement">
						<i class="mdi-action-delete"></i><span class="hidden-xs hidden-sm"> Delete</span>
					</button>
					<?php } ?>
					
					<a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterDeviceManagement">
						<i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm"> Filter</span>
					</a>
				
				</div>
			
			<!-- Device Management Grid Table -->
			<table class="table dataTable table-striped table-dynamic table-hover" id="tableDeviceManagement">
				<thead>
					<tr>
						<th></th>
						<th id="deviceMgmtDeviceName">Device Name</th>
						<th id="deviceMgmtMachineType">Machine Type</th>
						<th id="deviceMgmtConnectionType">Connection Type</th>
						<th id="deviceMgmtIPAddress">IP Address</th>
						<th id="deviceMgmtPort">Port</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>

		</div>
	</div>
</div>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="device-management-context-menu" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextDeviceManagement"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($deviceManagementAccess['Update']) { ?>
		<li><a tabindex="-1" id="editContextDeviceManagement"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<?php } if ($deviceManagementAccess['Delete']) { ?>
		<li><a tabindex="-1" id="deleteContextDeviceManagement"><i class="mdi-action-delete"></i> Delete</a></li>
		<?php } ?>
	</ul>
</div>

<!--Filter Form-->
<div class="builder" id="filterPanelDeviceManagement">
	<div id="closeFilterDeviceManagement"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel" style="width: 100%;" id="filterResetDeviceManagement">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="filterApplyDeviceManagement">Apply</button>
			
			<div class="form-group">
				<label>Device Name</label>
				<div class="input-group">
					<input type="text" class="input-md form-control" name="filterASDeviceName" id="filterASDeviceName" data-orientation="top" placeholder="Device Name"/>
				</div>
			</div>
			
			<div class="form-group">
				<label>Machine Type</label>
				<select class="form-control" data-search="true" id="filterASMachineType" >
					<option value="">--Select--</option>
					<option value = "IN">IN</option>
					<option value = "OUT">OUT</option>
					<option value = "IN and OUT">IN and OUT</option>
				</select>
			</div>
			
			<div class="form-group">
				<label>Connection Type</label>
				<select class="form-control" data-search="true" id="filterASConnectionType" >
					<option value="">--Select--</option>
					<option value = "udp">UDP</option>
					<option value = "tcp">TCP</option>
				</select>
			</div>
			
			<div class="form-group">
				<label>IP Address</label>
				<div class="input-group">
					<input type="text" class="input-md form-control" name="filterASIpAddress" id="filterASIpAddress" data-orientation="top" placeholder="IP Address"/>
				</div>
			</div>
			
			<div class="form-group">
				<label>Port</label>
				<div class="input-group">
					<input type="number" class="input-md form-control" name="filterASPort" id="filterASPort" data-orientation="top" placeholder="Port"/>
				</div>
			</div>
			
		</div>
	</div>
</div>

<!-- Device management from-->
<div class="modal fade" id="modalFormDeviceManagement" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;"
						aria-hidden="true"><i class="mdi-hardware-keyboard-backspace" id="backDeviceMangement"></i></button>
				
				<?php if ($deviceManagementAccess['Update']) { ?>
				<button type="button" class="close form-icons" aria-hidden="true" id="editInViewDeviceManagement">
					<i class="mdi-editor-mode-edit"></i>
				</button>
				<?php } ?>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<!--View Device management Form-->
				<form role="form"  id="viewFormDeviceManagement">
					<div class="row">
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Device Name</label></div>
							<div class="col-md-7"><p id="viewASDeviceName"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Serial Number</label></div>
							<div class="col-md-7"><p id="viewASSerialNumber"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">IP Address</label></div>
							<div class="col-md-7"><p id="viewASIpAddress"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Port</label></div>
							<div class="col-md-7"><p id="viewASPort"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Inport</label></div>
							<div class="col-md-7"><p id="viewASInport"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Timeout</label></div>
							<div class="col-md-7"><p id="viewASTimeout"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Attendance Parser</label></div>
							<div class="col-md-7"><p id="viewASAttendanceParser"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Connection Type</label></div>
							<div class="col-md-7"><p id="viewASConnectionType"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Machine Type</label></div>
							<div class="col-md-7"><p id="viewASMachineType"></p></div>
						</div>
					</div>
					
					<div class="row">
						<hr class="view-hr"/>
						
						<div class="form-group" style="font-size: large;margin-left: 13px;">
							<label class="control-label text-center">Additional Information</label>
						</div>
						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Added On</label></div>
							<div class="col-md-7"><p id="addedOnDeviceManagement"></p></div>
						</div>
						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Added By</label></div>
							<div class="col-md-7"><p id="addedByDeviceManagement"></p></div>
						</div>
						
						<div class="form-group updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated On</label></div>
							<div class="col-md-7"><p id="updatedOnDeviceManagement"></p></div>
						</div>
						
						<div class="form-group updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated By</label></div>
							<div class="col-md-7"><p id="updatedByDeviceManagement"></p></div>
						</div>
					</div>
				</form>
				
				<!--Add/Edit Device management Form-->
				<?php if ($deviceManagementAccess['Add'] == 1 || $deviceManagementAccess['Update'] == 1) { ?>
				<form role="form" class="form-horizontal form-validation"  id="editFormDeviceManagement" method="POST" action="">
					<input type="hidden" name="AS_Device_Management_Id" id="formASDeviceManagementId" />
					<div class="row">
						
						<div class="form-group">
							<label class="col-md-4 control-label">Device Name <span class="short_explanation">*</span>
							<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
                                                                       data-placement="top" data-content="Name of the Biometric Device">
                                                                      
							</i>
							</label>
							<div class="col-md-8">
								<input type="text" class="form-control vRequired vName onlyLetterSpDotAmpBracket" id="formASDeviceName" name="Device_Name" placeholder="Device Name">
							</div>
                        </div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Serial Number
							<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
                                                                       data-placement="top" data-content="An identification number for the biometric device">
                                                                      
							</i>
							</label>
							<div class="col-md-8">
								<input type="text" class="form-control onlyLetterSpDotAmpBracket" id="formASSerialNumber" name="Serial_Number" maxlength="50" placeholder="Serial Number">
							</div>
                        </div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">IP Address <span class="short_explanation">*</span>
							<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
                                                                       data-placement="top" data-content="IP address of your biometric device which is needed to log into the device">
                                                                      
							</i>
							</label>
						
							<div class="col-md-8">
							<!-- vIpAddress -->
							<input type="text" class="form-control vRequired" id="formASIpAddress" name="IP_Address" placeholder="IP Address">
							</div>
                        </div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Port <span class="short_explanation">*</span>
							<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
                                                                       data-placement="top" data-content="A port number is a way to identify a specific process to which an Internet or other network message is to be forwarded when it arrives at a server.">
                                                                      
							</i>
							</label>
							<div class="col-md-8">
								<input type="number" class="form-control vRequired" max="999999" min="1000"  id="formASPort" name="AS_Port" placeholder="Port">
							</div>
                        </div>

						<div class="form-group">
							<label class="col-md-4 control-label">Inport
							</label>
							<div class="col-md-8">
							<input type="number" class="form-control" max="999999" min="1000"  id="formASInport" name="AS_Inport" placeholder="Inport">	
							</div>
                        </div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Timeout <span class="short_explanation">*</span>
							<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
                                                                       data-placement="top" data-content=" A preset time period in biometric device during which a given request must be completed or the request is canceled">
                                                                      
							</i>
							</label>
							<div class="col-md-8">
								<input type="number" class="form-control vRequired" max="999999999" min="0"  id="formASTimeout" name="AS_Timeout" placeholder="Timeout">	
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Attendance Parser 
							<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
                                                                       data-placement="top" data-content="Interpreter component that breaks data into smaller elements for easy translation into another language.">
                                                                      
							</i>
							</label>
							<div class="col-md-8">
								<select class="form-control" data-search="true" id="formASAttendanceParser" name="AS_Attendance_Parser" >
									<option value="">-- Select --</option>
									<option value ="v6.60">v6.60</option>
									<option value="legacy">Legacy</option>
								</select>
							</div>
                        </div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Connection Type
							<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
                                                                       data-placement="top" data-content="Protocol used for the purpose of sharing resources">
                                                                      
							</i>
							</label>
							<div class="col-md-8">
								<select class="form-control" data-search="true" id="formASConnectionType" name="AS_Connection_Type">
										<option value="">-- Select --</option>
										<option value ="udp">UDP</option>
										<option value="tcp">TCP</option>
								</select>
							</div>
                        </div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Machine Type <span class="short_explanation">*</span>
							<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
                                                                       data-placement="top" data-content="Denotes the type of machine (Check-in/Check-out/Both Check-in and Check-out)">
							</i>
							</label>
							<div class="col-md-8">
								<select class="form-control vRequired" data-search="true" id="formASMachineType" name="formASMachineType">
									<option value="">-- Select --</option>
									<option value ="IN">IN</option>
									<option value="OUT">OUT</option>
									<option value ="IN and OUT">IN and OUT</option>
								</select>
							</div>
                        </div>
						
					</div>
					
					<button type="reset" class="cancel" id="formASResetDeviceManagement" style="display: none;" ></button>
				</form>	
				<?php } ?>
				</div>		
				<div class="modal-footer text-center" id="formActionDeviceManagement">
				
				<?php if ($deviceManagementAccess['Add'] == 1 || $deviceManagementAccess['Update'] == 1) { ?>
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetDeviceManagement" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitDeviceManagement" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
				<?php } ?>
				
			</div>	
			</div>
		</div>
	</div>
</div>

<!-- Form Dirty Confirmation Modal -->
<?php if ($deviceManagementAccess['Add'] || $deviceManagementAccess['Update']) { ?>
<div class="modal fade" id="modalDirtyDeviceManagement" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditConfAttendanceDevice"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfAttendanceDevice">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseConformDeviceManagement">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } if ($deviceManagementAccess['Delete']) { ?>
<!-- Delete COnfirmation Modal -->
<div class="modal fade" id="modalDeleteDeviceManagement" aria-hidden="true" >
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteConfDeviceMangement"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfDeviceMangement">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConformDeviceManagement">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php  } if ($deviceManagementAccess['View'] != 1 && $attendanceAccess['View'] != 1 && $importAccess['View'] != 1 && $attendanceSettingsAccess['View'] != 1) { ?>

<div class="col-md-12 portlets">
	<div class="txt_center">Sorry, Access Denied...</div>
</div>

<?php
}}
?>
