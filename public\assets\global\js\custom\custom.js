//////Global Variable for session tracking
var latitude = '';
var longitude = '';
var watchGeoPositionId = '';
var isGeoLocationEnforced = false;
var errorMsgJSON={
                    "705": "There seems to be some technical issues. Please try after some time.",
                    "706": "There seems to be some technical issues. Please try after some time.",
                    "719": "Access denied",
                    "766": "There seems to be some technical issues. Please try after some time.",
                    "767": "Something went wrong. Please contact system administrator.",
                    "770": "Something went wrong. Please contact system administrator.",
                    "771": "Something went wrong. Please contact system administrator.",
                    "778": "Something went wrong. Please contact system administrator.",
                    "779": "could not add candidates to the completed rounds",
                    "780": "Could not able to cancel the round",
                    "900": "There seems to be some technical issues. Please try after some time.",
                    "797": "Could not cancel the Completed or In-progress round"
                }
var regimeComparisionDetails = {};
var selectedTaxRegime='';
let partnerid = $.cookie('partnerid');
    partnerid = partnerid ? (partnerid.toLowerCase()) : "-";
 let dCode = $.cookie("d_code"),
    bCode = $.cookie("b_code");
let organizationCode = "";
// ip-address api
var ipAddressAPI = "https://api.ipify.org?format=json";
// form the API custom domain URL
let customDomainBaseUrl = '';
// check whether prod/staging or local
if(fnIsProduction()) {
    let url =  window.location.origin; // get the current location url ("https://infotech.hrapp.co.in")
    let urlNoProtocol = url.replace(/^https?:\/\//i, ""); //ignore the https protocol ("infotech.hrapp.co.in")
    urlNoProtocol = urlNoProtocol.split(".");  //split the url with dot (["infotech", "hrapp", "co", "in"]) - can ignore 0th index , it is orgcode
    urlNoProtocol = urlNoProtocol[3] ?  // check whether 3rd index is available (may be for staging)
                    urlNoProtocol[1]+'.'+urlNoProtocol[2]+'.'+urlNoProtocol[3] :
                    urlNoProtocol[1]+'.'+urlNoProtocol[2];

    customDomainBaseUrl = "https://api."+urlNoProtocol + "/"; // custom domain base url ("https://api.hrapp.co.in/")
}
else {
    customDomainBaseUrl = "https://api.hrapp.co.in/"; //if working from local - use staging API
}

// call get orgcode function to prefetch the organization code to use it in all the api/ajax calls request headers
fngetOrgCode();
getThemeColors();
//Alert when the DataSetup is not completed
function dataSetup(id)
{
    //if(id != undefined && $('#dataSetup').val() == 0)
    if(id != undefined && id != 'dataimport' && $('#dataSetup').val() == 0)
    {
        jAlert ({ msg : 'You have not completed your data setup, please visit data setup dashboard to complete the pre-requisites', type : 'info' });

        //Set Mask for the whole page.
        setMask('.custom-page','.dataSetupDashboardMask');

        //Data Setup Dashboard text will be blinking when Mask is set for whole page.
        $('#dataSetupDashboard').prop('style','-webkit-animation: blink .75s linear infinite;color: red;font-weight: bold;');
    }
    else if($('#dataSetup').val() == 0 && $('#dataSetupImport').val() != 2)
    {
        if($('#dataSetupImport').val() != 0)
        {
            $('.gridPanelAllowanceImport').addClass('logMask');
            $('#gridPanelDeductionImport,#gridPanelLeavesImport,#gridPanelLeaveImport,#gridPanelDependentImport,'+
              '#gridPanelAdhocAllowances,#gridPanelForm16Import,#gridPanelTaxDeclarationImport,#gridPanelTdsHistoryImport').addClass('logMask');
        }
        else
        {
           jAlert ({ msg : 'You have not completed your data setup, please visit data setup dashboard to complete the pre-requisites', type : 'info' });

            //Set Mask for the whole page.
            setMask('.custom-page','.dataSetupDashboardMask');
        }
    }
}

//for setting mask
function setMask(element,id)
{
    //removeMask();
    if(id!=''&& id!==undefined)
    {
        /** Set the mask for the page when the datasetup dashboard is enabled */
        if(element === '.custom-page' && id === '.dataSetupDashboardMask')
        {
            $(element).append('<div class="dataSetupDashboardMask">'+
                                '<ul>'+
                                    '<li class="pageLoader2">'+
                                        '<div class="loader" id="datasetupdashboard">'+

                                        '</div>'+
                                    '</li>'+
                                    '<div style="clear: both"></div>'+
                                '</ul>'+
                            '</div>');
        }else{
            $(element).append('<div class="logMask"><div></div></div>');
        }
    }
    else if(element == '.custom-page')
    {
        $(element).append('<div class="pageLoading">'+
                            '<ul class="pageLoader12">'+
                                '<li class="pageLoader2">'+
                                    '<div class="loader" id="datasetup">'+

                                    '</div>'+
                                '</li>'+
                                '<div style="clear: both"></div>'+
                            '</ul>'+
                        '</div>');
    }
    else
    {
        $(element).append(`<div class="custom-loading-cls" id="custom-loading">
            <div class="loader">
                <div class="loader--dot"></div>
                <div class="loader--dot"></div>
                <div class="loader--dot"></div>
                <div class="loader--dot"></div>
                <div class="loader--dot"></div>
                <div class="loader--dot"></div>
            </div>
        </div>`);

        //$(element).append('<div class="pageLoading"><div></div></div>');
    }
    //$(element).addClass('pageLoading');
}

function removeMask(id)
{
    if(id==='hrappLogin' || id === 'maillogin')
    {
        $(".logMask").remove();
    } else if(id==".custom-page"){
        $(".pageLoading").remove();
    } else{
        $('#custom-loading').prop('style', 'display:none');
        $('#custom-loading').remove();
    }
}

function setMaskDatasetup(element)
{
    /* Mask will be set if the dependency form datasetup is Incomplete */
    $.ajax({
        type     : 'POST',
        async    : false,
        dataType : "json",
        url      : pageUrl()+ 'datasetup-dashboard/index/set-mask-prerequisites',

        success: function(result) {
            var prevId = 0;
            for(i=0;i < result.length; i++ )
            {
                if(result[i]['Table_Name'] == element && result[i]['Form_Status'] == "Open")
                   {
                    statusFlag = 0;
                    for(l=0;l<i;l++)
                    {
                      if(result[l]['Form_Status'] == 'Open' && result[l]['Is_Required'] == 1)
                      {
                        statusFlag = 1;
                      }
                    }
                    if(statusFlag==1)
                    {
                        dataSetup('datasetup');
                    }
                }
            }
        }
    });
}

/**
 *$.fn.dataTable.version  - to check datatable version   (Currently- 1.10.0)
 **/
function _timer(callback)
{
    var time = 0;     //  The default time of the timer
    var mode = 1;     //    Mode: count up or count down
    var status = 0;    //    Status: timer is running or stoped
    var timer_id;    //    This is used by setInterval function

    // this will start the timer ex. start the timer with 1 second interval timer.start(1000)
    this.start = function(interval)
    {
        interval = (typeof(interval) !== 'undefined') ? interval : 1000;

        if(status === 0)
        {
            status = 1;
            timer_id = setInterval(function()
            {
                switch(mode)
                {
                    default:
                    if(time)
                    {
                        time--;
                        generateTime();
                        if(typeof(callback) === 'function') callback(time);
                    }
                    break;

                    case 1:
                    if(time < 86400)
                    {
                        time++;
                        generateTime();
                        if(typeof(callback) === 'function') callback(time);
                    }
                    break;
                }
            }, interval);
        }
    }

    //  Same as the name, this will stop or pause the timer ex. timer.stop()
    this.stop =  function()
    {
        if(status == 1)
        {
            status = 0;
            clearInterval(timer_id);
        }
    }

    // Reset the timer to zero or reset it to your own custom time ex. reset to zero second timer.reset(0)
    this.reset =  function(sec)
    {
        sec = (typeof(sec) !== 'undefined') ? sec : 0;
        time = sec;
        generateTime(time);
    }

    // Change the mode of the timer, count-up (1) or countdown (0)
    this.mode = function(tmode)
    {
        mode = tmode;
    }

    // This methode return the current value of the timer
    this.time = function(ctime)
    {
        time = ctime;
    }

    // This methode return the current value of the timer
    this.getTime = function()
    {
        return time;
    }

    // This methode return the current mode of the timer count-up (1) or countdown (0)
    this.getMode = function()
    {
        return mode;
    }

    // This methode return the status of the timer running (1) or stoped (1)
    this.getStatus
    {
        return status;
    }

    // This methode will render the time variable to hour:minute:second format
    function generateTime()
    {
        var second = time % 60;
        var minute = Math.floor(time / 60) % 60;
        var hour = Math.floor(time / 3600) % 60;

        second = (second < 10) ? '0'+second : second;
        minute = (minute < 10) ? '0'+minute : minute;
        hour = (hour < 10) ? '0'+hour : hour;

        $('div.timer span.second').html(second);
        $('div.timer span.minute').html(minute);
        $('div.timer span.hour').html(hour);
    }
}

// example use
var timer;

//****************** YOUR CUSTOMIZED JAVASCRIPT **********************//





function fnIsDomain () {
	return localStorage.getItem('domain');
}

function portNumber(){
    var port = location.port || (location.protocol === 'https:' ? '443' : '80');
    return port;
}

function tzDate() {
    return localStorage.getItem('TZDate');
}

function tzTime() {
    var tTime = localStorage.getItem('TZTime').split(" ");
    return tTime[0]+":"+tTime[2]+":"+tTime[4]+" "+tTime[5];

}

function setMaxDateBasedOnResignation(currentDate, ResignationDate, dateId){
    if(ResignationDate && currentDate){
        if((new Date(ResignationDate) < new Date(new Date(tzDate())))){
            $('#'+dateId).datepicker('option', 'maxDate',new Date(ResignationDate));
        } else {
            $('#'+dateId).datepicker('option', 'maxDate',new Date(tzDate()));
        }
    } else if(currentDate){
        $('#'+dateId).datepicker('option', 'maxDate',new Date(tzDate()));
    } else if(ResignationDate){
        $('#'+dateId).datepicker('option', 'maxDate',new Date(ResignationDate));
    } else{
        $('#'+dateId).datepicker('option', 'maxDate',null);
    }
}

function fnIsProduction ()
{
    if(localStorage.getItem('production') !== null) {
        var isProduction = Number(localStorage.getItem('production'));
    } else {
        var isProduction = $.cookie('isProduction');
    }

    return parseInt(isProduction,10);
}

/**
 *  pageUrl used to get the application url
*/
function pageUrl()
{
	if (fnIsProduction ())
	{
		var pathUrl = window.location.protocol + "//" + window.location.hostname+"/";
	}
	else
	{
        // var pathUrl = "http://localhost:"+portNumber()+"/hrapponline/";

        var pathUrl = "http://127.0.0.1:"+portNumber()+"/hrapponline/";

    }
	localStorage.setItem('pageUrl', pathUrl);
	return pathUrl;
}

// get the custom domain url
function getApiCustomDomains(endPointName) {
    let apiCustomDomain = '';

    switch(endPointName) {
        case 'ats' :
        case 'hrappBe':
            apiCustomDomain = customDomainBaseUrl+endPointName+"/graphql" // (ex: https://api.hrapp.co.in/ats/graphql,https://api.hrapp.co.in/hrappBe/graphql)
            break;
        case 'coreHrRo':
            apiCustomDomain = customDomainBaseUrl+"coreHr/rographql" // (https://api.hrapp.co.in/coreHr/rographql)
            break;
        case 'coreHrWo':
            apiCustomDomain = customDomainBaseUrl+"coreHr/wographql" // (https://api.hrapp.co.in/coreHr/wographql)
            break;
        case 'integrationRo' :
            apiCustomDomain = customDomainBaseUrl+"integration/rographql" // (https://api.hrapp.co.in/integration/rographql)
            break;
        case 'orgChart' :
            apiCustomDomain = customDomainBaseUrl+endPointName+"/orgchart" // (https://api.hrapp.co.in/orgChart/orgchart)
            break;
        case 'exitManagement' :
            apiCustomDomain = customDomainBaseUrl+endPointName+"/exitManagement" // (https://api.hrapp.co.in/exitManagement/exitManagement)
            break;
        case 'dynamicFormBuilder' :
            apiCustomDomain = customDomainBaseUrl+endPointName+"/graphqldynamicformbuilder" // (https://api.hrapp.co.in/dynamicFormBuilder/graphqldynamicformbuilder)
            break;
        case 'payrollAdminRo' :
            apiCustomDomain = customDomainBaseUrl+"payrollAdmin/roGraphql" // (https://api.hrapp.co.in/payrollAdmin/roGraphql)
            break;
        case 'payrollAdminWo' :
            apiCustomDomain = customDomainBaseUrl+"payrollAdmin/woGraphql" // (https://api.hrapp.co.in/payrollAdmin/woGraphql)
            break;
        case 'billingRead' :
            apiCustomDomain = customDomainBaseUrl+"billing/rographql" // (https://api.hrapp.co.in/billing/rographql)
            break;
        case 'atssignin':
            apiCustomDomain = customDomainBaseUrl+"ats/signinGraphql" // (https://api.hrapp.co.in/ats/signinGraphql)
            break;
        case 'atsExternal':
            apiCustomDomain = customDomainBaseUrl+"ats/external" // (https://api.hrapp.co.in/ats/signinGraphql)
            break;
        case 'payrollAdminRo' :
            apiCustomDomain = customDomainBaseUrl+"payrollAdmin/roGraphql" // (https://api.hrapp.co.in/payrollAdmin/roGraphql)
            break;
        case 'payrollAdminWo' :
            apiCustomDomain = customDomainBaseUrl+"payrollAdmin/woGraphql" // (https://api.hrapp.co.in/payrollAdmin/woGraphql)
            break;
        case 'workflow' :
        default:
            apiCustomDomain = customDomainBaseUrl+"workflowEngine" // (https://api.hrapp.co.in/workflowEngine)
            break;
    }

    return apiCustomDomain;
}

// check to display leave closure warning popup
if ($('#leaveClosureWarningValue').val() == 1 && localStorage.getItem('showLeaveClosurePopup') == 'show') {
    $('#leaveClosureWarningModal').addClass('leave-closure-warning-modal');
    $('#leaveClosureWarningModal').modal('toggle');
    localStorage.setItem('showLeaveClosurePopup', 'hide');
}

// open tax regime comparison and choose popup
$('#compareTaxRegime').on('click', function(){
    $('#totalIncome').html(regimeComparisionDetails.Currency_Symbol + ' '+regimeComparisionDetails.Total_Income);
    $('#oldRegimeTax').html('Total Tax : '+ regimeComparisionDetails.Currency_Symbol + ' '+ regimeComparisionDetails.Old_Regime_Tax);
    $('#newRegimeTax').html('Total Tax : '+ regimeComparisionDetails.Currency_Symbol + ' '+ regimeComparisionDetails.New_Regime_Tax);
    //remove selected class for all at initial
    $('#oldRegime').removeClass('tax-block-box-selected');
    $('#stopNotify').removeClass('tax-block-box-selected');
    $('#newRegime').removeClass('tax-block-box-selected');

    //show recommand label for low tax amount
    if(regimeComparisionDetails.New_Regime_Tax < regimeComparisionDetails.Old_Regime_Tax){
        $("#newTaxRegime").prop("checked", true);
        $('#newRegime').addClass('tax-block-box-selected');
        selectedTaxRegime = 'NEW_REGIME';
        $('#recommandNewRegime').show();
        $('#recommandOldRegime').hide();
    }
    else{
        $("#oldTaxRegime").prop("checked", true);
        $('#oldRegime').addClass('tax-block-box-selected');
        selectedTaxRegime = 'OLD_REGIME';
        $('#recommandOldRegime').show();
        $('#recommandNewRegime').hide();
    }
    $('#selectTaxRegimeModal').modal('toggle');

})

// on selecting tax regime change style for the selected one
// $('.newTaxRegimeFor').on('change', function() {
$(document).on('change', '.newTaxRegimeFor', function() {
    if(this.value === 'OLD_REGIME'){
        selectedTaxRegime = 'OLD_REGIME';
        $('#newRegime').removeClass('tax-block-box-selected');
        $('#stopNotify').removeClass('tax-block-box-selected');
        $('#oldRegime').addClass('tax-block-box-selected');
    }
    else if(this.value === 'NEW_REGIME'){
        selectedTaxRegime = 'NEW_REGIME';
        $('#oldRegime').removeClass('tax-block-box-selected');
        $('#stopNotify').removeClass('tax-block-box-selected');
        $('#newRegime').addClass('tax-block-box-selected');
    }
    else{
        selectedTaxRegime = 'DO_NOT_REMIND';
        $('#newRegime').removeClass('tax-block-box-selected');
        $('#oldRegime').removeClass('tax-block-box-selected');
        $('#stopNotify').addClass('tax-block-box-selected');

    }
})

//function to submit the selected tax regime
$('#submitSelectedTaxRegime').on('click', function(){
    //need to handle success and failure response
     setMask('#wholepage');
     $.ajax({
        type: 'POST',
        dataType: 'json',
        async: false,
        url: pageUrl() + 'payroll/tax-rules/update-employee-tax-regime',
        data : { REGIME_TYPE : selectedTaxRegime },
        success: function (result) {
            $('#selectTaxRegimeModal').modal('hide');
            removeMask();
            if (isJson(result) && result.msg) {
                if(result.success){
                    //on success tax update hide the notify bar.
                    $('#tax-regime-notify-bar').hide();
                    $('#additional-top-margin').hide();
                    jAlert({ msg : result.msg, type : result.type });
                }
                else{
                    jAlert({ msg : result.msg, type : result.type });
                }
            }else{
                jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
            }
        },
        error: function(updateTaxRegimeError){
            $('#selectTaxRegimeModal').modal('hide');
            removeMask();
            if (updateTaxRegimeError.status === 200) {
                sessionExpired();
                }
            else {
                    /* To handle internal server error */
                    jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                }
        }
    });

})


$('#truleadRedirection').on('click', function(){
    let partnerRedirectionURL = "https://exlygenze.truleadai.com/trulead-redirection/?rtkn=";
    encryptRefreshToken(partnerRedirectionURL);
})

function encryptRefreshToken(partnerRedirectionURL){
    setMask("#wholepage");
    var getPartnerRedirectionURLQuery = `query encryptRefreshToken($refreshToken:String!) { encryptRefreshToken(refreshToken:$refreshToken) { errorCode message refreshToken}}`;
    $.ajax({
        method: 'POST',
        url: getApiCustomDomains('ats'),
        async: true,
        headers: getGraphqlAPIHeaders(),
        data: JSON.stringify({
            query: getPartnerRedirectionURLQuery,
            variables:{
                refreshToken:$.cookie('refreshToken') ? $.cookie('refreshToken') : null,
            }
        }),
        success: function(result) {
            if(result.data){
                const {errorCode, refreshToken} = result.data.encryptRefreshToken;
                if (!errorCode && refreshToken) {
                    window.location.href=partnerRedirectionURL+refreshToken;
                } else {
                    $(window).scrollTop(0);
                    jAlert({
                        msg: 'Something went wrong while redirecting. Please contact system administrator',
                        type: 'warning'
                    });
                }
            } else {
                $(window).scrollTop(0);
                jAlert({
                    msg: 'Something went wrong while redirecting. Please contact system administrator',
                    type: 'warning'
                });
            }
            removeMask();
        },
        error: function() {
            $(window).scrollTop(0);
            jAlert({
                msg: 'Something went wrong while redirecting. Please contact system administrator',
                type: 'warning'
            });
           removeMask();
        }
    });
}

function showSignOffAlert(){
    if($('#signOffAlertType').val() == "present-orange-signoff-alert") {
        $('#signoffDueModal').modal('toggle');
    }
}

// design of the default payslip template preview. called when no payslip template is set as default
async function prefillPayslipPreviewModalContent(headingTxt) {
    let imgPath;
    if(await supportsWebp()) {
        imgPath = pageUrl()+'images/default-payslip.webp'
    }
    else{
        imgPath =  pageUrl()+'images/default-payslip.png'
    }

    $('#modalBodyPayslipTemplatePreview').html(`
        <i class="mdi-alert-error payslip-preview-warning-icon"></i>
        <div id="PayslipTemplatePreviewModalHeader" class="payslip-preview-modal-header">${headingTxt}</div>
        <div class="payslip-preview-modal-body">
            <div class="payslip-preview-modal-body-content">
                <div class="payslip-preview-number-count">1</div>
                If you want create and customize your payslip template, please click here
            </div>
            <button id="customTemplateButton" type="button" class="btn btn-secondary btn-embossed payslip-preview-custom-button">
                <a rel="noopener noreferrer" href="${pageUrl()}payroll/payslip-template" target="_blank" class="payslip-preview-button-link" style="color: white !important;">
                    Custom Template
                </a>
            </button>
            <div class="payslip-preview-modal-body-content">
                <div class="payslip-preview-number-count">2</div>
                We have default payslip template, preview in below. If you continue with default please click here
            </div>
            <button id="defaultTemplateButton" type="button" class="btn btn-secondary btn-embossed payslip-preview-custom-button">Default Template</button>
            <div class="payslip-preview-image-body">
                <div class="payslip-preview-image-body-content">
                    Default Payslip Template
                </div>
                <img class="img-responsive" alt="default payslip" src="${imgPath}">
            </div>
        </div>
    `);
}

let defaultPayslipType = '';
let defaultServiceProviderId = 0;
let defaultServiceProviderName = '';
// return the default payslip templates list
function fnGetDefaultPayslipTemplateList(headingTxt, payslipType, serviceProviderId, serviceProviderName,callback) {
    defaultPayslipType         = payslipType;
    defaultServiceProviderId   = serviceProviderId;
    defaultServiceProviderName = serviceProviderName;

    setMask('#wholepage');
    $.ajax({
        type: 'POST',
        dataType: 'json',
        async: true,
        url: pageUrl() + 'payroll/payslip-template/list-default-payslip-template/payslipType/'+payslipType+'/serviceProviderId/'+serviceProviderId,
        success: function (listDefaultPayslipTemplateResponse) {
            if(listDefaultPayslipTemplateResponse.data){
                callback('payslipTemplateExist'); // return the response to the parent function
            } else {
                prefillPayslipPreviewModalContent(headingTxt);
                callback('payslipTemplateNotExist'); // if not payslip templates are available for all the payslip types // return error when the data is empty
            }
            removeMask();
        },
        error: function (listDefaultPayslipTemplateError) {
            if (listDefaultPayslipTemplateError.status === 200) {
                sessionExpired();
              }
            else {
                callback('error'); // return error except session expiretion error
            }
            removeMask();
        }
    })
}

$("#camuUrlWarningModalCloseBtn").on("click", function() {
    $('#camuUrlWarningModal').modal('toggle');
});

$(".announcementElement").on("click", function() {
    var aTitleValue = $(this).find('.announcementSubElement').attr('aTitle');
    var aContent = $(this).find('.announcementSubElement').html();
    $("#announcementPopupTitle").text(aTitleValue);
    $("#announcementContent").html(aContent);
    $("#announcementContentPopup").modal('toggle');
});

// check landed form view access and call help screen endpoint to get help screen details
if($('#landedFormAccess').val() === "Yes") {
    showSignOffAlert();
    let isAdminForLoadedForm = $('#isAdminForLoadedForm').val();
    let isManager = $('#isManager').val();
    // when the partnerid is camu and camuUrl is empty, then we should present the warning alert
    if(!$('#camuUrl').val() && partnerid.toLowerCase() == 'camu' && parseInt(isAdminForLoadedForm, 10)) {
        $('#camuUrlWarningModal').modal('toggle');
    }
    var helpURLQuery = `query listHelpUrlAndText(
        $moduleId:Int!,
        $applicableForEmployee:String!,
        $applicableForManager:String!,
        $applicableForAdmin:String!
    ){
        listHelpUrlAndText(
            moduleId:$moduleId,
            applicableForEmployee:$applicableForEmployee,
            applicableForManager:$applicableForManager,
            applicableForAdmin:$applicableForAdmin
        ) {
            errorCode
            message
            helpDetails {
                Title
                Type
                Url
                Embed_Url
                Text
            }
        }
    }`;
    let helpScreenVariables = {
        moduleId : parseInt($('#moduleId').val(), 10),
        applicableForEmployee: "Yes",
        applicableForManager: parseInt(isManager, 10) ? "Yes" : "No",
        applicableForAdmin: parseInt(isAdminForLoadedForm, 10)  ? "Yes" : "No"
    }
    $.ajax({
        method: 'POST',
        url: getApiCustomDomains('hrappBe'),
        headers: getGraphqlAPIHeaders(),
        data: JSON.stringify({
            query: helpURLQuery,
            variables : helpScreenVariables
        }),
        success: function(result) {
            const {errorCode, helpDetails} = result.data.listHelpUrlAndText;
            //if data listed successfully
            if (!errorCode && helpDetails && helpDetails.length > 0) {
                $('#helpOption').removeClass('hidden');
                $("#helpScreenContent").html('');
                for(var helpDetail of helpDetails) {
                    // as of now we are presenting videos alone, so checked the type as URL
                    if(helpDetail.Type === "Url") {
                        var imgURL = pageUrl() + 'images/play-video.png';
                        // append each video as separate card
                        $("#helpScreenContent").append(`
                            <div
                                helpScreenURL="${helpDetail.Embed_Url}"
                                helpScreenTitle="${helpDetail.Title}"
                                class="help-screen-panel-content"
                            >
                                <img
                                    width="25px"
                                    height="20px"
                                    alt="help"
                                    src="${imgURL}"
                                />
                                <span style="padding-left: 8px;">${helpDetail.Title}</span>
                            </div>
                        `);
                    }
                }
            } else {
                // when we don't have any videos for the landed module, then we can hide help option
                $('#helpOption').addClass('hidden');
            }
        },
        error: function() {
            // if we get error from this endpoint, then we can hide help option
            $('#helpOption').addClass('hidden');
        }
    });
    // load the chatbot script only for admins in all the forms
    if(parseInt(isAdminForLoadedForm, 10)) {
        var headScript = document.createElement("script");
        headScript.type = "text/javascript";
        headScript.async = true;
        headScript.defer = true;
        headScript.id = "hs-script-loader";
        headScript.src = "//js.hs-scripts.com/8169364.js";
        document.head.appendChild(headScript);
    }
}

// while clicking help option, we need to present available videos which is retrieved from BE in help panel
$(document).on('click', '#helpOption', function () {
    $('#helpOption').addClass('hidden'); // hide help option while help panel
    $('#helpScreenPanel').addClass('visible');
});


// while closing the help panel, again we need to present help option
$(document).on('click', '#helpScreenCloseBtn', function () {
    $('#helpOption').removeClass('hidden');
    $('#helpScreenPanel').removeClass('visible');
});

// when any of video card in help panel was clicked, we have to present relevant help video in help video model
$("body").on('click', '.help-screen-panel-content', function() {
    let helpScreenUrl = $(this).attr("helpScreenURL"),  // help video url is bind with this element helpScreenURL attribute
    helpScreenTitle = $(this).attr("helpScreenTitle"); // help video title is bind with this element helpScreenTitle attribute
    $('#helpScreenCloseBtn').trigger('click');
    $('#helpScreenModal .modal-title').html(helpScreenTitle);
    $('#helpScreenModalContent').html(
        `<iframe
            width="90%"
            height="500"
            allowFullScreen="true"
            src="${helpScreenUrl}"
        ></iframe>`
    );
    $('#helpScreenModal').modal('toggle');
});

// remove help screen video contents when the help screen modal was closed
$('#helpScreenModal').on('hide.bs.modal', function (e) {
    $('#helpScreenModal .modal-title').html("");
    $('#helpScreenModalContent').html("");
});

// triggered when clicking the default template button in payslip template preview popup
$(document).on('click', '#defaultTemplateButton', function () {
    let ipAddress = '';
    setMask('#wholepage');
    // get client ip address using this common function
    getClientIp(function (ipAdd) {
        ipAddress = ipAdd;

        let defaultPayslipQuery = `mutation addDefaultTemplate($payslipType:String!,$serviceProviderId:Int,$serviceProviderName:String){ addDefaultTemplate (payslipType:$payslipType serviceProviderId:$serviceProviderId serviceProviderName:$serviceProviderName){ errorCode  message monthlyTemplateId hourlyTemplateId validationError}}`;

        let variables = { payslipType: defaultPayslipType, serviceProviderId: defaultServiceProviderId, serviceProviderName: defaultServiceProviderName};

        $.ajax({
            method: 'POST',
            url: 'https://api.'+localStorage.getItem('domain')+'/payrun/graphql',
            contentType: 'application/json',
            headers: getGraphqlAPIHeaders(ipAddress),
            data: JSON.stringify({
                query: defaultPayslipQuery,
                variables: variables
            }),

            success: function(defaultTemplateAddResponse) {
                if (defaultTemplateAddResponse.data) {
                    $('#modalPayslipTemplatePreview').modal('toggle'); // close preview popup
                    $('#modalShowSuccessMsg').modal('toggle'); // open success popup
                } else {
                    $(window).scrollTop(0);
                    jAlert({
                        msg: 'Something went wrong. Please contact system administrator',
                        type: 'warning'
                    });
                }
                removeMask();
            },
            error: function (defaultTemplateAddError) {
                $(window).scrollTop(0);
                if (defaultTemplateAddError && defaultTemplateAddError[0]) {
                    // error returned from backend
                    var error = JSON.parse(defaultTemplateAddError[0].message);
                    var errorCode = error.errorCode;
                    switch(errorCode){
                        case "PSP0001":
                                jAlert({
                                    msg: 'Unable to add default payslip template',
                                    type: 'warning'
                                });
                            break;
                        case "PSP0001":
                                jAlert({
                                    msg: 'Sorry, you dont have access to add payslip template. Please contact system administrator.',
                                    type: 'warning'
                                });
                            break;
                        case "IVE0183":
                            jAlert({
                                msg: 'Please provide a valid service provider name.',
                                type: 'warning'
                            });
                            break;
                        case "IVE0000":
                        default:
                            jAlert({
                                msg: 'Something went wrong. Please contact system administrator',
                                type: 'warning'
                            });
                            break;
                    }
                } else {
                    jAlert({
                        msg: 'Something went wrong. Please contact system administrator',
                        type: 'warning'
                    });
                }
                removeMask();
            }
        });
    });

})
/**
 *  GraphQl URL
*/
function fnGetOnboardGraphqlUrl()
{
    var domainName = fnIsDomain();

    /** At present we are using same url for both production and stagging in future it will be changed */
    var graphqlUrl;
	if (fnIsProduction ())
	{
        if(domainName == 'upshotindia.com')
        {
            graphqlUrl = "https://jg60r5115i.execute-api.ap-south-1.amazonaws.com/upshotindia/graphql";
        }
        else
        {
            graphqlUrl = "https://onboardapi."+domainName+"/graphql";
        }
	}
	else
	{
        graphqlUrl = "https://onboardapi."+domainName+"/graphql";
    }
	return graphqlUrl;
}

/**
 *  Open URL in new tab
*/
function fnOpenInNewTab(url, focus) {
    var win = window.open(url, focus);
    win.focus();
}
    //Show Comment Details In
    function viewCommentGrid(uniqueId,formName,idA)
    {
        if (uniqueId > 0)
        {
            $('#modalComment'+idA).modal('toggle');

            var commentTable = $('.tableComment'+idA).dataTable({
                "bPaginate"      : false,
                "bLengthChange"  : false,
                "bFilter"        : false,
                "bDestroy"       : true,
                "bAutoWidth"     : false,
                "bServerSide"    : true,
                "bDeferRender"   : true,
                "sServerMethod"  : "POST",
                "sAjaxSource"    : pageUrl()+'default/index/list-comments/formName/'+formName+'/parentId/'+uniqueId,
                "sAjaxDataProp"  : "aaData",
                "aoColumnDefs"   : [{ 'bSortable': false, 'aTargets': ['_all'] },
                                    { "sClass" : "visible-sm visible-xs hidden-md hidden-lg", "aTargets" : [0] },
                                    { "sClass" : "hidden-sm hidden-xs visible-md visible-lg", "aTargets" : [3] },
                                    { "sClass" : "hidden-sm hidden-xs visible-md visible-lg", "aTargets" : [4] }],
                "aaSorting"      : [],
                "aoColumns"      : [{
                    "mData": function (row, type, set)
                    {
                        return '<i class="fa fa-plus-square-o hidden-lg hidden-md"></i>';
                    }
                },
                {
                    "mData" : function (row, type, set) {
                        return '<div class="'+ row['DT_RowId'] +'" >'+ row['Employee_Name'] +'</div>';
                    }
                },
                {
                    "mData" : function (row, type, set) {
                        return '<div  class="'+ row['DT_RowId'] +'" style="word-break: break-all;" >'+ row['Emp_Comment'] +'</div>';
                    }
                },
                {
                    "mData" : function (row, type, set) {
                        return '<div class="'+ row['DT_RowId'] +'" >'+ row['Approval_Status'] +'</div>';
                    }
                },
                {
                    "mData" : function (row, type, set) {
                        return '<div class="'+ row['DT_RowId'] +'text-center" >'+ row['Added_On'] +'</div>';
                    }
                }]
            });

	    return commentTable;
	}

    }


 function formCollapse(operation,idA,idE)
 {
	if (operation=='add')
	{
		$('#collapseIcon'+idA).removeClass('collapsed');
		$('#collapse'+idA).removeClass('collapse');
		$('#collapse'+idA).addClass('collapse in');
		//$('#collapseIcon'+idA).addClass('a:after');

		$('#collapse'+idE).removeClass('collapse in');
		$('#collapse'+idE).addClass('collapse');
		$('#collapseIcon'+idE).addClass('collapsed');
	}
	else if (operation=='edit' || operation=='submit')
	{
		$('#collapseIcon'+idE).removeClass('collapsed');
		$('#collapse'+idE).removeClass('collapse');
		$('#collapse'+idE).addClass('collapse in');

		$('#collapse'+idA).removeClass('collapse in');
		$('#collapse'+idA).addClass('collapse');
		$('#collapseIcon'+idA).addClass('collapsed');
	}
	else
	{
		$('#collapse'+idE).removeClass('collapse in');
		$('#collapse'+idE).addClass('collapse');
		$('#collapseIcon'+idE).addClass('collapsed');

		$('#collapse'+idA).removeClass('collapse in');
		$('#collapse'+idA).addClass('collapse');
		$('#collapseIcon'+idA).addClass('collapsed');
	}

}

//Show Additional Info Record In View Page
function AdditionalInfo(record) {
        var addedByPanel ='<hr class="view-hr"/>'+
                                   '<div class="form-group" style="font-size: large;margin-left: 13px;"><label class="control-label">Additional Information</label></div>'+
                                   '<div class="form-group">'+
                                           '<div class="col-md-5"><label class="control-label">Added On</label></div>'+
                                           '<div class="col-md-7"><p>'+record.Added_On+'</p></div>'+
                                   '</div>'+

                                   '<div class="form-group">'+
                                           '<div class="col-md-5"><label class="control-label">Added By</label></div>'+
                                           '<div class="col-md-7"><p>'+record.Added_By+'</p></div>'+
                                   '</div>';
        var updatedByPanel='';
            if (record.Updated_On !=null && record.Updated_By !=null)
            {
             updatedByPanel ='<div class="form-group">'+
                                                '<div class="col-md-5"><label class="control-label">Updated On</label></div>'+
                                                '<div class="col-md-7"><p>'+record.Updated_On+'</p></div>'+
                                        '</div>'+

                                        '<div class="form-group">'+
                                                '<div class="col-md-5"><label class="control-label">Updated By</label></div>'+
                                                '<div class="col-md-7"><p>'+record.Updated_By+'</p></div>'+
                                        '</div>';
            }
          var additionalInfo = addedByPanel + updatedByPanel ;
          $('div#viewAdditionalinformation').empty();
          $('div#viewAdditionalinformation').append(additionalInfo);
}

function fnUserLogin (userName, password, remember, l) {
    setMask('#loginmask','hrappLogin');

    $.ajax({
        type     : 'POST',
        dataType : 'json',
        //async    : false,
        url      : pageUrl() + "auth/index/mobile-login",
        data     : { username : userName, password : password ,remember_me : remember },
        success  : function(result)
        {
            if(result !== null)
            {
                if (result.success)
                {

                    if(result.appIdentity == 'Mobile')
                    {
                        localStorage.setItem('AppIdentityUser',btoa(userName));
                                top.location.href = pageUrl() ;
                        localStorage.setItem('AppIdentityPwd',btoa(password));
                                top.location.href = pageUrl() ;
                    }

                    switch (result.redirect)
                    {
                        case 'changePassword':
                            localStorage.setItem('changeLoginPwdDisplay', 1);
                            top.location.href = pageUrl() ;

                            //
                            //setTimeout(function(){


                                //$('#menuChangePassword').trigger('click');
                            //}, 15000);

                            break;

                        default :
                            localStorage.setItem('changeLoginPwdDisplay', 0);
                            top.location.href = pageUrl() + result.redirect;
                    }
                    //removeMask();
                }
                else
                {
                    //removeMask();
                    removeMask('hrappLogin');

                    $('#error-panel').html(result.msg).show();

                    l.stop();
                }
            }
            else{
                //removeMask();
                removeMask('hrappLogin');
            }

        }
    });
}

// to get php action request headers
function getPhpActionHeaders() {
    let authorizationHeader = $.cookie('accessToken') ? $.cookie('accessToken') : null;
    let refreshTokenHeader = $.cookie('refreshToken') ? $.cookie('refreshToken') : null;
    return {
        org_code: organizationCode,
        Authorization: authorizationHeader,
        partnerid: partnerid,
        refresh_token: refreshTokenHeader,
        user_ip: $.cookie('userIpAddress'),
        additional_headers: JSON.stringify(
            {
                org_code: organizationCode,
                Authorization: authorizationHeader,
                partnerid: partnerid,
                refresh_token: refreshTokenHeader,
                user_ip: $.cookie('userIpAddress'),
                d_code: dCode,
                b_code: bCode,
            }
        )
    }
}

function getGraphqlAPIHeaders(ipAddress = "") {
    let authorizationHeader = $.cookie('accessToken') ? $.cookie('accessToken') : null;
    let refreshTokenHeader = $.cookie('refreshToken') ? $.cookie('refreshToken') : null;
    let empUidHeader = $.cookie('empUid') ? $.cookie('empUid') : null;

    return {
        'Content-Type': 'application/json',
        org_code: organizationCode,
        Authorization: authorizationHeader,
        partnerid: partnerid,
        refresh_token: refreshTokenHeader,
        user_ip: ipAddress ? ipAddress : $.cookie('userIpAddress'),
        additional_headers: JSON.stringify(
            {
                org_code: organizationCode,
                Authorization: authorizationHeader,
                partnerid: partnerid,
                refresh_token: refreshTokenHeader,
                user_ip: ipAddress ? ipAddress : $.cookie('userIpAddress'),
                d_code: dCode,
                b_code: bCode,
                emp_uid: empUidHeader
            }
        )
    }
}

function getGraphqlNoAuthAPIHeaders() {
    return {
        'Content-Type': 'application/json',
        org_code: organizationCode,
        additional_headers: JSON.stringify(
            {
                org_code: organizationCode,
                d_code: dCode,
                b_code: bCode,
            }
        )
    }
}

// to set php action request headers in ajax settings
function setPhpActionHeaders() {
    $.ajaxSettings.headers["Authorization"] = $.cookie('accessToken') ? $.cookie('accessToken') : null;
    $.ajaxSettings.headers["partnerid"] = partnerid;
    $.ajaxSettings.headers["refresh_token"] = $.cookie('refreshToken') ? $.cookie('refreshToken') : null;
    $.ajaxSettings.headers["user_ip"] = $.cookie('userIpAddress');
    $.ajaxSettings.headers["org_code"] = organizationCode;
    $.ajaxSettings.headers["additional_headers"] = JSON.stringify(
        {
            org_code: organizationCode,
            Authorization: $.cookie('accessToken') ? $.cookie('accessToken') : null,
            partnerid: partnerid,
            refresh_token: $.cookie('refreshToken') ? $.cookie('refreshToken') : null,
            user_ip: $.cookie('userIpAddress'),
            d_code: dCode,
            b_code: bCode,
        }
    );
}

// to delete php action request headers in ajax settings
function deletePhpActionHeaders() {
    delete $.ajaxSettings.headers["Authorization"];
    delete $.ajaxSettings.headers["authorization"];// need to remove this, after changed it to 'Authorization' in all the used places
    delete $.ajaxSettings.headers["partnerid"];
    delete $.ajaxSettings.headers["additional_headers"];
    delete $.ajaxSettings.headers["org_code"];
    delete $.ajaxSettings.headers["refresh_token"];
    delete $.ajaxSettings.headers["user_ip"];
}

function fngetOrgCode(callback){
    var orgCode = '';
    //If the organization code is fetched already
    if(organizationCode){
        if(typeof callback != 'undefined'){
            callback(organizationCode);
        }
        return organizationCode;
    }else{
        // for staging and production usage
        if(fnIsProduction()) {
            let url = window.location.href;
            let urlNoProtocol = url.replace(/^https?:\/\//i, "");
            let urlHeaders = urlNoProtocol.split(".");
            orgCode = urlHeaders[0];
        } else {
            orgCode = "capricetest"; // local dev usage
        }
        organizationCode = orgCode;
        if(typeof callback != 'undefined'){
            callback(orgCode);
        }
        return orgCode;
    }
}

function getThemeColors() {
    if (!localStorage.getItem("brand_color")) {
        var themQuery = `query customColorPicker {
            customColorPicker {
                errorCode
                message
                colorResult {
                    Primary_Color
                    Secondary_Color
                    Hover_Color
                    Table_Header_Color
                    Table_Header_Text_Color
                    Career_Page_Caption
                    Org_Name
                    Date_Format
                    Field_Force
                    __typename
                }
                __typename
            }
        }`;
        $.ajax({
            method: 'POST',
            url: getApiCustomDomains('atsExternal'),
            headers: getGraphqlNoAuthAPIHeaders(),
            data: JSON.stringify({
                query: themQuery,
            }),
            success: function(result) {
                const { colorResult, errorCode  } = result.data.customColorPicker;
                //if data listed successfully
                if (!errorCode && colorResult && colorResult.length > 0) {
                    const { Primary_Color, Secondary_Color, Hover_Color} = colorResult[0];
                    document.documentElement.style.setProperty('--primary-color', Primary_Color);
                    document.documentElement.style.setProperty('--secondary-color', Secondary_Color);
                    document.documentElement.style.setProperty('--hover-color', Hover_Color);
                    localStorage.setItem("brand_color", JSON.stringify(colorResult[0]));
                } else {
                    SetDefaultColors();
                }
            },
            error: function() {
                SetDefaultColors();
            }
        });
    } else {
        const { Primary_Color, Secondary_Color, Hover_Color} = JSON.parse(localStorage.getItem("brand_color"));
        document.documentElement.style.setProperty('--primary-color', Primary_Color);
        document.documentElement.style.setProperty('--secondary-color', Secondary_Color);
        document.documentElement.style.setProperty('--hover-color', Hover_Color);
    }
}

function SetDefaultColors() {
    document.documentElement.style.setProperty('--primary-color', '#260029');
    document.documentElement.style.setProperty('--secondary-color', '#ec407a');
    document.documentElement.style.setProperty('--hover-color', '#FAD4E1');
}

// content for view monthly payslip function
function viewPayslipTemplateContent(templateResult, employeeName,payslipType,payslipId,calledFrom, type, viewFormId) {
    var result,employeeName,payslipType;
    if (templateResult && payslipId) {
        if(payslipType == "Monthly"){
            $.ajax({
                type: 'POST',
                dataType: 'json',
                async: false,
                url: pageUrl() + 'payroll/salary-payslip/view-salary-payslip/payslipId/' + payslipId+'/calledFrom/'+calledFrom,
                success: function (monthlyresult) {
                    if (isJson(monthlyresult)) {
                        if (monthlyresult.success) {
                            fnUpdatePayslipDetails(monthlyresult,employeeName,payslipType,type,viewFormId);
                        } else {
                            removeMask();
                            jAlert({ msg: "An error occurred while retrieving the payslip. Please contact the system administrator.", type: "warning" });
                        }
                    }else{
                        sessionExpired ();
                    }
                },
                error: function(error){
                    if (error.status == 200) {
                        sessionExpired();
                      }
                    else {
                          /* To handle internal server error */
                          jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                      }
                    removeMask();
                }
            });
        }
        else{
            $.ajax({
                type        : 'POST',
                dataType    : 'json',
                async       : false,
                url         : pageUrl () +'payroll/salary-payslip/view-wage-payslip/payslipId/'+ payslipId+'/calledFrom/'+calledFrom,
                success: function (hourlyresult) {
                    if (isJson(hourlyresult)) {
                        if (hourlyresult.success) {
                            fnUpdatePayslipDetails(hourlyresult,employeeName,payslipType,type,viewFormId);
                        }
                    }else{
                        sessionExpired ();
                    }
                },
                error: function(error){
                    if (error.status == 200) {
                        sessionExpired();
                      }
                    else {
                          /* To handle internal server error */
                          jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                      }
                    removeMask();
                }
            });
        }
    }
    else {
        fnUpdatePayslipDetails(templateResult,employeeName,payslipType,type,viewFormId);
    }
}
// function to update payslip details
function fnUpdatePayslipDetails(result,employeeName,payslipType,type,viewFormId){
    $('#formActionPayslipSubGrid').hide();
    var currency = payslipType == "Monthly" ? result.Currency : result.Payslip['Currency'] ;

    //If show payslip address flag is enabled, then only show the organization address in payslip.
    if ($('#displayPayslipAddress').val() == 1) {
        $("#payslipStreet1").text(result.Payslip['Street1']+',');
        if (result.Payslip['Street2']) {
            $("#payslipStreet2").text(result.Payslip['Street2'] + ',');
        }
        else
        {
            $("#payslipStreet2").text('');
        }
        $("#payslipCity").text(result.Payslip['City']+',');
        $("#payslipState").text(result.Payslip['State']+',');
        $("#payslipCountry").text(result.Payslip['Country']+',');
        $("#payslipPinCode").text(result.Payslip['Pincode']);
    }
    else {
        $('#payslipTemplateAddress').html('');
        $("#payslipStreet1").text('');
        $("#payslipStreet2").text('');
        $("#payslipCity").text('');
        $("#payslipState").text('');
        $("#payslipCountry").text('');
        $("#payslipPinCode").text('');
    }
    $("#formHeader").text(result.Org_Name['Org_Name']);
    $("#viewBusinessUnit").text(fnCheckNull(result.Payslip['Business_Unit']));
    if ($('#showReportCreator').val() == 1) {
        $('#viewFormMonthlyPayslip').append(`<div style="text-align:right; font-size:10px; color:#000; margin-top:10px">Report generated by ${result.Payslip['First_Name']} ${result.Payslip['Last_Name']} on ${result.Payslip['Generated_On']} </div>`);
    }

    // function to update personal details
    fnUpdatePersonalDetailsInPayslip(result,employeeName,payslipType);

    $("#earnAmt").html(``);
    $("#deductAmt").html(``);
    $("#earnAmt").text("Amount" + ((currency) ? "(" + currency + ")" : ''));
    $("#deductAmt").text("Amount" + ((currency) ? "(" + currency + ")" : ''));

    // function to update earnings, deduction, organization contribution, and form16 tables
    fngetTableContent($('#earningsTab'), $('#deductTab'), $('#exemptionsPanel'), $('#form16SummaryTab'), $('#sec10ExemptionsPanel'), $('#contributionTab1'), $('#contributionTab2'), result, false,payslipType);

    // Handle the new combined earnings and deductions table if it exists
    if ($('#earningsDeductionsTab').length > 0) {
        fnPopulateCombinedEarningsDeductionsTable(result, payslipType);
    }

    //$('#modalFormMonthlySalary').modal('toggle');
    $('#formActionPayslip').hide();
    $('#formGeneratePayslip, #formGeneratePayslipSubGrid').hide();
    $('#viewFormMonthlySalaryUser').show();
    $('#viewFormMonthlyPayslip,#formReport').show();
    formPayslipHtmlToPdf(type,viewFormId);
}

function formPayslipHtmlToPdf(type,viewFormId) {
    var element= document.querySelector('#' + viewFormId);

    // options which are given to html2pdf
    var opt = {
        margin: 0.3,
        filename: 'Payslip.pdf',
        image: { type: 'jpeg', quality: 1 },
        html2canvas: { dpi: 300, letterRendering: false, scale: 2 },
        jsPDF: { unit: 'in', format: [12,20], orientation: 'portrait'}
    };
    /**html2pdf configuration to download and get output */
    // type is download allow file to download
    if (type == 'download') {
        // Check if we're in a mobile native app
        if (localStorage.getItem('isNativeMobileApp') == 1) {
            // Adding Alert for Testing
            jAlert({ msg: "File is processing. Please wait.", type: "info" });
            // For mobile native app, upload to S3 with 10-minute expiry and open the presigned URL
            html2pdf().set(opt).from(element).outputPdf("blob").then(function (pdf) {
                // Get organization code and domain name for S3 path
                var orgCode = fngetOrgCode();
                var domainName = fnIsDomain();
                domainName = domainName ? domainName.split('.')[0] : localStorage.getItem('domain').split('.')[0];

                // Create a unique filename with timestamp
                var timestamp = new Date().getTime();
                var fileName = 'Payslip' + timestamp + '.pdf';

                // Create S3 path
                var s3Path = domainName + '/' + orgCode + '/temp/Payslip/' + fileName;

                // Get presigned PUT URL
                var putUrl = fngetSignedPutUrl(s3Path, 'bucketName');

                if ($.inArray(putUrl, ['', null, 'sessionexpired', 'internalerror']) === -1) {
                    // Upload PDF to S3
                    deletePhpActionHeaders();
                    $.ajax({
                        url: putUrl,
                        type: 'PUT',
                        contentType: 'application/pdf',
                        processData: false,
                        data: pdf,
                    }).done(function(err, res) {
                        if (res == "success" || !err) {
                            // Get presigned URL for viewing
                            var viewUrl = fngetSignedUrl(s3Path, 'bucketName');
                            if ($.inArray(viewUrl, ['', null, 'sessionexpired', 'internalerror']) === -1) {
                                // Open the PDF in an external viewer
                                window.open(viewUrl, '_self');
                            } else {
                                jAlert({ msg: "Unable to generate viewable PDF link", type: "warning" });
                            }
                        } else {
                            jAlert({ msg: "Unable to upload PDF to server", type: "warning" });
                        }
                        removeMask();
                    }).fail(function() {
                        jAlert({ msg: "Failed to upload PDF to server", type: "warning" });
                        removeMask();
                    });
                    setPhpActionHeaders();
                } else {
                    jAlert({ msg: "Unable to generate upload link for PDF", type: "warning" });
                    removeMask();
                }
            });
        } else {
            // For regular web browser, use the normal blob download
            html2pdf().set(opt).from(element).save();
            removeMask();
        }
    }
    // convert pdf base64 data to canvas image by passing html2pdf output
    else {
        html2pdf().set(opt).from(element).outputPdf().then(function (pdf) {

            $('#the-canvas').removeClass('hidden');

            var print_content = 'data:application/pdf;base64,' + btoa(pdf);
            var pdfData = pdf

            // Loaded via <script> tag, create shortcut to access PDF.js exports.
            var pdfjsLib = window['pdfjs-dist/build/pdf'];

            // The workerSrc property shall be specified.
            pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

            // Using DocumentInitParameters object to load binary data.
            var loadingTask = pdfjsLib.getDocument({ data: pdfData });
            loadingTask.promise.then(function (pdf) {

                // Fetch the first page
                var pageNumber = 1;
                pdf.getPage(pageNumber).then(function (page) {

                    var scale = 1.5;
                    var viewport = page.getViewport({ scale: scale });

                    // Prepare canvas using PDF page dimensions
                    var canvas = document.getElementById('the-canvas');
                    var context = canvas.getContext('2d');
                    context.webkitImageSmoothingEnabled = false;
                    context.mozImageSmoothingEnabled = false;
                    context.imageSmoothingEnabled = false;

                    canvas.height = viewport.height;
                    canvas.width = viewport.width;


                    // Render PDF page into canvas context
                    var renderContext = {
                        canvasContext: context,
                        viewport: viewport
                    };
                    var renderTask = page.render(renderContext);
                    renderTask.promise.then(function () {
                        if (type == 'print') {
                            $('.sidebar, .topbar, .header, .hrsubmenu_img, .footer, #gridPanelMonthlySalary, #estimatePayrollPanel, #gridPanelHourlyWagesPayslip, #filterPanelMonthlySalary, #filterPanelHourlyWagesPayslip, #PrintScreen').addClass('hidden');
                            $(".panel-content").get(0).style.setProperty('height', '1100px');
                            $(".printable_portion").get(0).style.setProperty('height', '1000px');
                            $(".printable_portion").append(`<embed width='100%' height='100%' src=${print_content}>`);
                            $('#printPanel').removeClass('hidden').addClass('panel');
                            $('.main-content').addClass('DTTT_Print');
                            $('#printHeader').removeClass('hidden');

                            $('#exitPrint').on('click', function() {
                                $('.sidebar, .topbar, .header, .hrsubmenu_img, .footer, #gridPanelMonthlySalary, #estimatePayrollPanel, #gridPanelHourlyWagesPayslip, #filterPanelMonthlySalary, #filterPanelHourlyWagesPayslip,#PrintScreen').removeClass('hidden');
                                $('#printPanel').removeClass('panel').addClass('hidden');
                                $('#printHeader').addClass('hidden');
                                $('.main-content').removeClass('DTTT_Print');
                            });
                        }
                        if (type == 'view') {
                            $('#modalViewSalaryPayslipTemplate, #modalViewPayslipTemplate, #modalViewPayoutPayslipTemplate').modal('show');
                        }
                        removeMask();
                    });
                });
            }, function (reason) {
                removeMask();
            });
        });
    }
}

/* get table content */
function fngetTableContent(earnVar, deductVar, exempVar, form16Var, sec10Var, contribution1Var, contribution2Var, result, templateVariable,payslipType) {
        function fngetOperation(val, var_type) {
            if (!templateVariable) {
                var_type.append(val)
            }
            else {
                if (var_type == earnVar) {
                    earnVar += val;
                }
                else if (var_type == deductVar) {
                    deductVar += val;
                }
                else if (var_type == exempVar) {
                    exempVar += val;
                }
                else if (var_type == form16Var) {
                    form16Var += val;
                }
                else {
                    sec10Var += val;
                }
            }
        }
        var winSize;
        var maxVal = Math.max((result.Deduction).length, (result.Incentive).length);
        var totalIncentive = 0;
        var totalDeduction = 0;
        var height;
        var isMultiComponentExist = false;
        var netPayAmt, currency, isPfEmployee, isIncuranceEmployee, isEtfEmployee;
        $(window).on('resize', function() {
            winSize = $(window).width();
        });
        if (winSize > 991) {
            height = '40px';
        }
        else {
            height = '0px';
        }
        if (!templateVariable) {
            earnVar.html('');
            deductVar.html('');
        }
        for (var i = 0; i < maxVal; i++) {
            if (result.Incentive[i]) {
                if (result.Incentive[i]['Incentive_Name'] != undefined) {
                    totalIncentive = parseFloat(totalIncentive) + parseFloat(result.Incentive[i]['Incentive_Amount']);

                    if (result.Incentive[i]['Description'] && result.Incentive[i]['Description'] !== "Multi_Components" && (result.Incentive[i]['Incentive_Name'] != 'Provident Fund' && result.Incentive[i]['Incentive_Name'] != 'Insurance' && result.Incentive[i]['Incentive_Name'] != 'Advance Salary')) {
                        fngetOperation('<tr class="child"><td class="col-md-10 text-left" id="incent" style="height:40px;word-break: break-word;">' +/*result.Incentive[i]['Incentive_Name']+' - '+*/result.Incentive[i]['Description'] + '</td><td class="text-right" id="incentAmt" style="height:40px">' + result.Incentive[i]['Incentive_Amount'] + '</td></tr>', earnVar);
                    }
                    else {
                        if (result.Incentive[i]['Incentive_Name'] == 'Provident Fund') {
                            fngetOperation('<tr id="incent" class="child"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;"> Allowance - Other</td><td class="text-right" style="height:40px">' + result.Incentive[i]['Incentive_Amount'] + '</td></tr>', earnVar);
                        }
                        else if (result.Incentive[i]['Incentive_Name'] == 'Insurance') {
                            fngetOperation('<tr id="incent" class="child"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">Allowance - Insurance</td><td class="text-right" style="height:40px">' + result.Incentive[i]['Incentive_Amount'] + '</td></tr>', earnVar);
                        }
                        else {
                            if (result.Incentive[i]['Description'] === "Multi_Components") {
                                let earnHtmlContent = earnVar.html();
                                let earningHeading = result.Incentive[i]['Incentive_Name'] == "Basic Pay" ? "Pay Scale" : result.Incentive[i]['Incentive_Name'];

                                if(!earnHtmlContent.includes("Basic Pay")) {
                                    isMultiComponentExist = true;
                                    fngetOperation('<tr><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">Basic Pay</td><td class="text-right" style="height:40px"></td></tr>', earnVar);
                                }
                                fngetOperation('<tr id="incent" class="child"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;padding-left: 20px;">' + earningHeading + '</td><td class="text-right" style="height:40px">' + result.Incentive[i]['Incentive_Amount'] + '</td></tr>', earnVar);
                            } else {
                                fngetOperation('<tr id="incent" class="child"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">' + result.Incentive[i]['Incentive_Name'] + '</td><td class="text-right" style="height:40px">' + result.Incentive[i]['Incentive_Amount'] + '</td></tr>', earnVar);
                            }
                        }
                    }
                }
                else {
                    if (winSize > 991)
                        fngetOperation('<tr class="child"><td style="height:40px"></td><td style="height:' + height + '"></td></tr>', earnVar);
                    else
                        $('#earndiv').prop({ 'style': 'display:none' });
                }
            }
            else {
                if (winSize < 991)
                    $('#earndiv').prop({ 'style': 'display:none' });
                else
                    fngetOperation('<tr class="child"><td style="height:40px"></td><td style="height:' + height + '"></td></tr>', earnVar);

            }
        }

        if(isMultiComponentExist) {
            maxVal += 1;
        }
        for (var i = 0; i < maxVal; i++) {
            if (result.Deduction[i]) {
                if (result.Deduction[i]['Deduction_Name'] != undefined) {
                    totalDeduction = parseFloat(totalDeduction) + parseFloat(result.Deduction[i]['Deduction_Amount']);

                    if (result.Deduction[i]['Description']) {
                        fngetOperation('<tr class="child"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">'/*+result.Deduction[i]['Deduction_Name']+' - '*/ + result.Deduction[i]['Description'] + '</td><td class="text-right" style="height:40px">' + result.Deduction[i]['Deduction_Amount'] + '</td></tr>', deductVar);
                    }
                    else {
                        fngetOperation('<tr class="child"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">' + result.Deduction[i]['Deduction_Name'] + '</td><td class="text-right" style="height:40px">' + result.Deduction[i]['Deduction_Amount'] + '</td></tr>', deductVar);
                    }
                }
                else {
                    fngetOperation('<tr class="child"><td class="col-md-10 text-left"style="height:40px;word-break: break-word;"></td><td style="height:40px"></td></tr>', deductVar);
                }
            }
            else {
                fngetOperation('<tr class="child"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;"></td><td style="height:40px"></td></tr>', deductVar);
            }
        }

        if(payslipType == "Monthly"){
            netPayAmt   =  result.Payslip['Incentive_Amount'] ;
            currency    =  result.Currency ;
            isPfEmployee = result.salaryDetails.Is_PfEmployee;
            isIncuranceEmployee = result.salaryDetails.Is_InsuranceEmployee;
            isEtfEmployee = result.salaryDetails.Is_ETFEmployee;
            eligibleForContributionPensionFund  = result.salaryDetails.Eligible_For_Contribution_Pension_Scheme;
        }else{
            netPayAmt   =  result.Payslip['Total_Salary'];
            currency    =  result.Payslip['Currency'];
            isPfEmployee = result.salaryDetails.Is_Pf;
            isIncuranceEmployee = result.salaryDetails.Is_Insurance;
            isEtfEmployee = 0;
            eligibleForContributionPensionFund = 'No';
        }
        $("#totEarn, #netPay, #intermediatePayment, .viewInsuranceDetails, .viewFHInsuranceDetails").remove();


        if ($("#earningsTabFooter").length && $("#earningsTabFooter tr").length > 0) {
            $('#totalEarningsAmount').text(result.totalEarnings);
            $('#totalDeductionsAmount').text(result.totalDeductions);
            $('#totalOutstandingAmount').text(result.Payslip['Outstanding_Amt']);
            $('#totalNetPay').text(netPayAmt);

            if (typeof result.IntermediatePaymentAmt === 'string') {
                // Apply the replace function
                intermediatePaymentAmt = parseFloat(result.IntermediatePaymentAmt.replace(/[^0-9.]/g, ''));
            }
            else
            {
                intermediatePaymentAmt = result.IntermediatePaymentAmt;
            }

            if (intermediatePaymentAmt > 0)
            {
                $('.totalIntermediatePaymentPanel').show();
                $('#totalIntermediatePayment').text(intermediatePaymentAmt);
            }
            else
            {
                $('.totalIntermediatePaymentPanel').hide();
            }
            fngetOperation($("#earningsTabFooter tr"),earnVar);
            fngetOperation($("#deductionsTabFooter tr"),deductVar);

        } else {
            // total earnings
            fngetOperation('<tr class="child" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">Total Earnings</td><td class="text-right" style="height:40px">' + result.totalEarnings + '</td></tr>', earnVar);

            // total deductions
            fngetOperation('<tr class="child" id="totEarn" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">Total Deductions</td><td class="text-right" style="height:40px">' + (result.totalDeductions) + '</td></tr>',deductVar);

            if (typeof result.IntermediatePaymentAmt === 'string') {
                // Apply the replace function
                intermediatePaymentAmt = parseFloat(result.IntermediatePaymentAmt.replace(/[^0-9.]/g, ''));
            }
            else
            {
                intermediatePaymentAmt = result.IntermediatePaymentAmt;
            }

            // intermediate payment is available, then add empty cell in deduction
            if (intermediatePaymentAmt > 0){
                fngetOperation('<tr class="child" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">Total Intermediate Payment</td><td class="text-right" style="height:40px">' + (result.IntermediatePaymentAmt) + '</td></tr>', earnVar);
                fngetOperation('<tr class="child" id="netPay" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;"></td><td class="text-right" style="height:40px"></td></tr>',deductVar);
            }

            // outstanding amount
            fngetOperation('<tr class="child" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">Outstanding Amount</td><td class="text-right" style="height:40px">' + result.Payslip['Outstanding_Amt'] + '</td></tr>', earnVar);

            // net pay
            fngetOperation('<tr class="child" id="netPay" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">Netpay</td><td class="text-right" style="height:40px">' + netPayAmt + '</td></tr>',deductVar);
        }



        if(netPayAmt){
            $('#netpayInWord').html(currency ? `<b>: ${currency}</b>`+'  '+ number_to_words(netPayAmt) + ' Only' : ': '+number_to_words(netPayAmt) + ' Only');
            $('#netpayInWord').prop('style', 'display:inline-block;');
        }

        if(result.totalEarningsAndOrgShareAmount)
        {
            $('#totalEarningsAndOrgShareAmountInWord').html(currency ? `<b>: ${currency}</b>`+'  '+ number_to_words(result.totalEarningsAndOrgShareAmount) + ' Only' : ': '+number_to_words(result.totalEarningsAndOrgShareAmount) + ' Only');
            $('#totalEarningsAndOrgShareAmountInWord').prop('style', 'display:inline-block;');
        }

        if (typeof result.orgLwfAmount === 'string')
        {
            // Apply the replace function
            orgLwfAmount = parseFloat(result.orgLwfAmount.replace(/[^0-9.]/g, ''));
        }
        else
        {
            orgLwfAmount = result.orgLwfAmount;
        }

        if (isPfEmployee == 1 || isIncuranceEmployee == 1 || isEtfEmployee == 1 || orgLwfAmount > 0 ||  templateVariable ||  eligibleForContributionPensionFund == 'Yes') {
            var contributionDetails = fngetContributionDetails(result.orgPfAmt,result.orgPfAmtHeader,result.adminCharge,result.edliCharge,result.etfAmt,result.orgLwfAmount,
                result.orgLWFAmtHeader,result.InsuranceDetails,result.FixedHealthInsurance,result.orgEtfAmtHeader,result.orgCPSAmtHeader,result.Payslip['CPS_Employer_Share_Amount']);
            $('#contributionTab1,#contributionTab2').html('');
            for(i=0; i< contributionDetails.length; i++){
                if(i%2 == 0){
                    if(templateVariable){
                        contribution1Var +=  contributionDetails[i];
                    }
                    else{
                        contribution1Var.append(contributionDetails[i]);
                    }
                }else{
                    if(templateVariable){
                        contribution2Var +=  contributionDetails[i];
                    }
                    else{
                        contribution2Var.append(contributionDetails[i]);
                    }
                }
            }
            if(contributionDetails.length%2 != 0){
                if(templateVariable){
                    contribution2Var +=  '<tr class="child hidden-xs hidden-sm" style="border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;"></td><td class="text-right" style="height:40px"></td></tr>';
                }
                else{
                    contribution2Var.append('<tr class="child hidden-xs hidden-sm" style="border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;"></td><td class="text-right" style="height:40px"></td></tr>');
                }
            }
        }
        else{
            $('#emptyContributionDiv').prop('style', 'height: 0px;');
            $('#empContribution').prop('style', 'display:none');
        }

    /** Form16 Summary **/
    if (result.Form16Exists && result.Form16Exists == 1 && payslipType == "Monthly") {
        if (!templateVariable) {
            form16Var.html('');
            sec10Var.html('');
            exempVar.html('');
        }
        var currencySymbol = ((currency) ? "(" + currency + ")" : '');
        $("#exemptionsAmt").html(``);
        $("#sectionsAmt").html(``);
        $("#form16SummaryAmt").html(``);
        $("#exemptionsAmt").text("Amount" + currencySymbol);
        $("#sectionsAmt").text("Amount" + currencySymbol);
        $("#form16SummaryAmt").text("Amount" + currencySymbol);

        /** Perks/Other Income/Exemptions/Rebates **/
        var form16TaxDetails = result.Form16['form16TaxDetails'];
        if (form16TaxDetails.length > 0) {
            for (var i = 0; i < form16TaxDetails.length; i++) {
                if ((form16TaxDetails[i]['ExemptionName'] !== '')) {
                    fngetOperation('<tr class="child" id="E' + form16TaxDetails[i]['ExemptionName'] + '"><td class="col-md-10 text-left" style="height:40px">' + form16TaxDetails[i]['ExemptionName'] + '</td><td class="text-right" style="height:40px">' + form16TaxDetails[i]['ExemptionAmount'] + '</td></tr>', exempVar);
                }
                else {
                    fngetOperation('<tr class="child"><td class="col-md-10 text-left"style="height:40px"></td><td style="height:40px"></td></tr>', exempVar);
                }
            }
        }
        else {
            fngetOperation('<tr class="child"><td class="col-md-10 text-left"style="height:40px"></td><td style="height:40px"></td></tr>', exempVar);
        }

        /** Sec 10 Exemptions **/

        var sec10Exemption = result.Form16['Sec10Exemption'];

        if (sec10Exemption.length > 0) {
            for (var i = 0; i < sec10Exemption.length; i++) {
                if ((sec10Exemption[i]['Investment_Category'] != undefined && sec10Exemption[i]['Investment_Category'] != '')) {
                    fngetOperation('<tr class="child" id="S' + sec10Exemption[i]['Investment_Category'] + '"><td class="col-md-10 text-left" style="height:40px">' + sec10Exemption[i]['Investment_Category'] + '</td><td class="text-right" style="height:40px">' + sec10Exemption[i]['ExemptAmt'] + '</td></tr>', sec10Var);
                }
                else {
                    fngetOperation('<tr class="child"><td class="col-md-10 text-left"style="height:40px"></td><td style="height:40px"></td></tr>', sec10Var);
                }
            }
        }
        else {
            fngetOperation('<tr class="child"><td class="col-md-10 text-left"style="height:40px"></td><td style="height:40px"></td></tr>', sec10Var);
        }

        /** Form16 Summary **/

        var form16Summary = result.Form16['form16Summary'];
        if (form16Summary.length > 0) {
            for (var fs = 0; fs < form16Summary.length; fs++) {
                if (form16Summary[fs]['SummaryName'] != '') {
                    fngetOperation('<tr class="child" id="' + form16Summary[fs]['SummaryName'] + '"><td class="col-md-10 text-left" style="height:40px">' + form16Summary[fs]['SummaryName'] + '</td><td class="text-right" style="height:40px">' + form16Summary[fs]['SummaryAmount'] + '</td></tr>', form16Var);
                }
                else {
                    fngetOperation('<tr class="child"><td class="col-md-10 text-left" style="height:40px"></td><td class="text-right" style="height:40px"></td></tr>', form16Var);
                }
            }
            if (result.Form16['taxReliefDetails'] && result.Form16['taxReliefDetails'].Tax_Relief_Exist === "Yes") {
                let taxReliefAmt = result.Form16['taxReliefDetails'].Monthly_Tax_Relief_Amount;
                let taxReliefLabel = result.Form16['taxReliefDetails'].Tax_Relief_Lable_Name;
                $('#taxReliefInWord').html(currency ? `<b>: ${currency}</b>`+'  '+ (taxReliefAmt)  : ': '+(taxReliefAmt));
                $('#taxReliefInWord').prop('style', 'display:inline-block;');
                $('#viewMonthlyTaxReliefLabel').text(taxReliefLabel);
            } else {
                $("#taxReliefDiv").prop('style', 'display:none;');
            }
        }
        else {
            $("#taxReliefDiv").prop('style', 'display:none;');
            fngetOperation('<tr class="child"><td class="col-md-10 text-left" style="height:40px"></td><td class="text-right" style="height:40px"></td></tr>', form16Var);
        }
    }
    else {
        $('#empForm16Table').prop('style', 'display:none');
        $('#emptyForm16Div').removeAttr('style');
        $('#form16SummaryTab tbody').html('');
    }
    return [earnVar, deductVar, exempVar, form16Var, sec10Var, contribution1Var, contribution2Var];

}
// function to get contribution details
function fngetContributionDetails(orgPfAmt,orgPfAmtHeader,adminCharge,edliCharge,etfAmt,orgLwfAmount,orgLWFAmtHeader,InsuranceDetails,FixedHealthInsurance,orgEtfAmtHeader,orgCPSAmtHeader,cpsEmployerShareAmount){
    var contributionDetails=[];

    if (typeof orgPfAmt === 'string')
    {
        floatOrgPfAmt     = parseFloat(orgPfAmt.replace(/[^0-9.]/g, ''));
    }
    else
    {
        floatOrgPfAmt     = orgPfAmt;
    }

    if (typeof adminCharge === 'string')
    {
        floatAdminCharge  = parseFloat(adminCharge.replace(/[^0-9.]/g, ''));
    }
    else
    {
        floatAdminCharge     = adminCharge;
    }

    if (typeof edliCharge === 'string')
    {
        floatEdliCharge   = parseFloat(edliCharge.replace(/[^0-9.]/g, ''));
    }
    else
    {
        floatEdliCharge     = edliCharge;
    }

    if (typeof etfAmt === 'string')
    {
        floatEtfAmt  = parseFloat(etfAmt.replace(/[^0-9.]/g, ''));
    }
    else
    {
        floatEtfAmt     = etfAmt;
    }

    if (typeof orgLwfAmount === 'string')
    {
        floatOrgLwfAmount = parseFloat(orgLwfAmount.replace(/[^0-9.]/g, ''));
    }
    else
    {
        floatOrgLwfAmount = orgLwfAmount;
    }

    if(typeof cpsEmployerShareAmount === 'string')
    {
        floatCPSEmployerShareAmount     = parseFloat(cpsEmployerShareAmount.replace(/[^0-9.]/g, ''));
    }
    else
    {
        floatCPSEmployerShareAmount     = cpsEmployerShareAmount;
    }

    if (floatOrgPfAmt > 0) {
        contributionDetails.push('<tr class="child" style="border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">' + orgPfAmtHeader + '</td><td class="text-right" style="height:40px">' + orgPfAmt + '</td></tr>');
        // check adminCharge is avilable
        if (floatAdminCharge > 0){
            contributionDetails.push('<tr class="child" style="border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">' + 'Admin Charge' + '</td><td class="text-right" style="height:40px">' + adminCharge + '</td></tr>');
        }
        // check edliCharge is avilable
        if (floatEdliCharge > 0){
            contributionDetails.push('<tr class="child" style="border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">' + 'EDLI Charge' + '</td><td class="text-right" style="height:40px">' + edliCharge + '</td></tr>');
        }
    }
    // check etfAmt is avilable
    if (floatEtfAmt > 0){
        contributionDetails.push('<tr class="child" style="border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">'+orgEtfAmtHeader+'</td><td class="text-right" style="height:40px">' + etfAmt + '</td></tr>');
    }

    if (floatCPSEmployerShareAmount > 0){
        contributionDetails.push('<tr class="child" style="border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">'+orgCPSAmtHeader+'</td><td class="text-right" style="height:40px">' + cpsEmployerShareAmount + '</td></tr>');
    }

    // check orgLwfAmount is avilable
    if (floatOrgLwfAmount > 0){
        contributionDetails.push('<tr class="child" style="border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">' + orgLWFAmtHeader + '</td><td class="text-right" style="height:40px">' + orgLwfAmount + '</td></tr>');
    }
    // check InsuranceDetails is avilable
    if (InsuranceDetails.length > 0) {
        for (i = 0; i < InsuranceDetails.length; i++) {
            contributionDetails.push('<tr class="child" style="border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">' + InsuranceDetails[i].InsuranceName + '</td><td class="text-right" style="height:40px">' + InsuranceDetails[i].Amount + '</td></tr>');
        }
    }
    // check FixedHealthInsurance is avilable
    if(FixedHealthInsurance.length > 0){
        for (i = 0; i < FixedHealthInsurance.length; i++) {
            contributionDetails.push('<tr class="child viewFHInsuranceDetails" style="border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">' + FixedHealthInsurance[i].Insurance_Name + '</td><td class="text-right" style="height:40px">' + FixedHealthInsurance[i].Insurance_Amount + '</td></tr>');
        }
    }
    return contributionDetails;
}
// function to get leave details
function fngetLeaveDetails(paidLev, unPaidLev){
    var pLeave = paidLev;
    var uPLeave = unPaidLev;
    var leav, upleav,leavePaid,leaveUnpaid;
    var lvarr = '';
    var ulvarr = '';
    $('#viewMonthlyPaidLeaveDetails').html('');
    $('#viewMonthlyUnpaidLeaveDetails').html('');

        if (pLeave.length > 0) {
        leavePaid = '(';
        for (var x = 0; x < pLeave.length; x++) {
            leav = pLeave[x][0].split(" ");
            lName = '';
            if (leav.length > 1) {
                for (var pl in leav) {
                    lName += leav[pl].substring(0, 1);
                }
            }
            else {
                lName += pLeave[x][0].substring(0, 2);
            }

            lName = lName.toUpperCase();
            leavePaid += lName + " - " + pLeave[x][1];
            lvarr += lName + " - " + pLeave[x][0];

            if (x != pLeave.length - 1) {
                lvarr += ", ";
                leavePaid += ", ";
            }
            $('#viewMonthlyPaidLeaveDetails').append('<tr class="child"><td class="col-md-10 text-left" id="pltype" style="height:40px;word-break: break-word;">' + pLeave[x][0] +'</td><td class="text-right" id="plbalance" style="height:40px">' + pLeave[x][1] + '</td></tr>');
        }
        leavePaid += ')';
    }
    else {
        leavePaid = '';
        $('#viewMonthlyPaidLeaveDetails').append('<tr class="child"><td class="col-md-10 text-left" id="pltype" style="height:40px;word-break: break-word;">-</td><td class="text-center" id="plbalance" style="height:40px">-</td></tr>');
    }

    if (uPLeave.length > 0) {
        leaveUnpaid = '(';
        for (var y = 0; y < uPLeave.length; y++) {
            upleav = uPLeave[y][0].split(" ");
            ulName = '';
            if (upleav.length > 1) {
                for (var ul in upleav) {
                    ulName += upleav[ul].substring(0, 1);
                }
            }
            else {
                ulName += uPLeave[y][0].substring(0, 2);
            }

            ulName = ulName.toUpperCase();
            leaveUnpaid += ulName + " - " + uPLeave[y][1];
            ulvarr += (y === 0 && lvarr != '') ? (", " + ulName + " - " + uPLeave[y][0]) : (ulName + " - " + uPLeave[y][0]);

            if (y != uPLeave.length - 1) {
                leaveUnpaid += ", ";
                ulvarr += ", ";
            }
            $('#viewMonthlyUnpaidLeaveDetails').append('<tr class="child"><td class="col-md-10 text-left" id="upltype" style="height:40px;word-break: break-word;">' + uPLeave[y][0] +'</td><td class="text-right" id="uplbalance" style="height:40px">' + uPLeave[y][1] + '</td></tr>');
        }
        leaveUnpaid += ')';
    }
    else {
        leaveUnpaid = '';
        $('#viewMonthlyUnpaidLeaveDetails').append('<tr class="child"><td class="col-md-10 text-left" id="upltype" style="height:40px;word-break: break-word;">-</td><td class="text-center" id="uplbalance" style="height:40px">-</td></tr>');
    }
    var leaves = lvarr.concat(ulvarr);

    if (leaves != '') {
        $('#notes, #notesHourly').html('<b>Note :</b> ' + leaves);
        $('#notes, #notesHourly').prop('style', 'display:""');
    }
    else
        $('#notes, #notesHourly').prop('style', 'display:none');

    return [leavePaid,leaveUnpaid];
}

// Function to populate the combined earnings and deductions table
function fnPopulateCombinedEarningsDeductionsTable(result, payslipType) {
    var maxVal = Math.max((result.Deduction).length, (result.Incentive).length);
    var earningsRows = [];
    var deductionsRows = [];
    var currency = payslipType == "Monthly" ? result.Currency : result.Payslip['Currency'];
    var netPayAmt = payslipType == "Monthly" ? result.Payslip['Incentive_Amount'] : result.Payslip['Total_Salary'];
    var intermediatePaymentAmt = 0;

    // Process earnings (incentives)
    for (var i = 0; i < result.Incentive.length; i++) {
        if (result.Incentive[i] && result.Incentive[i]['Incentive_Name'] != undefined) {
            var earningName = '';
            var earningAmount = result.Incentive[i]['Incentive_Amount'];

            if (result.Incentive[i]['Description'] && result.Incentive[i]['Description'] !== "Multi_Components" &&
                (result.Incentive[i]['Incentive_Name'] != 'Provident Fund' &&
                 result.Incentive[i]['Incentive_Name'] != 'Insurance' &&
                 result.Incentive[i]['Incentive_Name'] != 'Advance Salary')) {
                earningName = result.Incentive[i]['Description'];
            } else {
                if (result.Incentive[i]['Incentive_Name'] == 'Provident Fund') {
                    earningName = 'Allowance - Other';
                } else if (result.Incentive[i]['Incentive_Name'] == 'Insurance') {
                    earningName = 'Allowance - Insurance';
                } else {
                    if (result.Incentive[i]['Description'] === "Multi_Components") {
                        earningName = result.Incentive[i]['Incentive_Name'] == "Basic Pay" ? "Pay Scale" : result.Incentive[i]['Incentive_Name'];
                        // Add indentation for multi-component items to match original design
                        earningName = '&nbsp;&nbsp;&nbsp;&nbsp;' + earningName;
                    } else {
                        earningName = result.Incentive[i]['Incentive_Name'];
                    }
                }
            }

            earningsRows.push({
                name: earningName,
                amount: earningAmount
            });
        }
    }

    // Process deductions
    for (var i = 0; i < result.Deduction.length; i++) {
        if (result.Deduction[i] && result.Deduction[i]['Deduction_Name'] != undefined) {
            var deductionName = result.Deduction[i]['Description'] ?
                               result.Deduction[i]['Description'] :
                               result.Deduction[i]['Deduction_Name'];
            var deductionAmount = result.Deduction[i]['Deduction_Amount'];

            deductionsRows.push({
                name: deductionName,
                amount: deductionAmount
            });
        }
    }

    // Clear existing content
    $('#earningsDeductionsTab').empty();

    // Populate the combined table
    var maxRows = Math.max(earningsRows.length, deductionsRows.length);
    for (var i = 0; i < maxRows; i++) {
        var earningName = earningsRows[i] ? earningsRows[i].name : '';
        var earningAmount = earningsRows[i] ? earningsRows[i].amount : '';
        var deductionName = deductionsRows[i] ? deductionsRows[i].name : '';
        var deductionAmount = deductionsRows[i] ? deductionsRows[i].amount : '';

        var rowHtml = '<tr class="child">' +
            '<td class="text-left" style="min-height:40px;word-break: break-word;color: #000;vertical-align: top;padding: 8px;">' + earningName + '</td>' +
            '<td class="text-right" style="min-height:40px;border-right: 2px solid #807c7c;color: #000;vertical-align: top;padding: 8px;">' + earningAmount + '</td>' +
            '<td class="text-left" style="min-height:40px;word-break: break-word;color: #000;vertical-align: top;padding: 8px;">' + deductionName + '</td>' +
            '<td class="text-right" style="min-height:40px;color: #000;vertical-align: top;padding: 8px;">' + deductionAmount + '</td>' +
            '</tr>';

        $('#earningsDeductionsTab').append(rowHtml);
    }

    // Update footer totals
    $('#totalEarningsAmount').text(result.totalEarnings);
    $('#totalDeductionsAmount').text(result.totalDeductions);
    $('#totalOutstandingAmount').text(result.Payslip['Outstanding_Amt']);
    $('#totalNetPay').text(netPayAmt);

    // Handle intermediate payment
    if (typeof result.IntermediatePaymentAmt === 'string') {
        intermediatePaymentAmt = parseFloat(result.IntermediatePaymentAmt.replace(/[^0-9.]/g, ''));
    } else {
        intermediatePaymentAmt = result.IntermediatePaymentAmt;
    }

    if (intermediatePaymentAmt > 0) {
        $('.totalIntermediatePaymentPanel').show();
        $('#totalIntermediatePayment').text(intermediatePaymentAmt);
    } else {
        $('.totalIntermediatePaymentPanel').hide();
    }

    // Update net pay in words
    if (netPayAmt) {
        $('#netpayInWord').html(currency ? `<b>: ${currency}</b>` + '  ' + number_to_words(netPayAmt) + ' Only' : ': ' + number_to_words(netPayAmt) + ' Only');
        $('#netpayInWord').prop('style', 'display:inline-block;');
    }
}

// function to update personal details
function fnUpdatePersonalDetailsInPayslip(result,employeeName,payslipType){
    // function to get leave details
    var levDetails = fngetLeaveDetails(result.PaidLeave[2],result.UnpaidLeave[2]);
    var worked_count = payslipType == "Monthly" ? result.WorkedDays : result.actualHours['daysWorked'] ;
    var paidleave = ((result.PaidLeave[1] != null) ? result.PaidLeave[1] : 0);
    var pfPolicy = ((result.Payslip['Pf_PolicyNo'] != null) ? result.Payslip['Pf_PolicyNo'] : '-');
    var unPaidleave = ((result.UnpaidLeave[1] != null) ? result.UnpaidLeave[1] : 0);
    var onDuty = ((result.OnDuty[1] != null) ? result.OnDuty[1] : 0);

    if(result.previousPaycyleDate && result.previousPaycyleDate['Consider_Cutoff_Days_For_Attendance_And_Timeoff'] == 'yes' && result.previousPaycyleDate['Cutoff_Day'] > 0){
        var prevUnPaidleave = (( result.PrevMonthUnpaidLeave.length > 0 && result.PrevMonthUnpaidLeave[1] != null) ? result.PrevMonthUnpaidLeave[1] : 0);
        $("#viewMonthlyPrevMonthUnpaidLeave").text(prevUnPaidleave);
        let previousMonthDateRange = "("+fnFormatDate(result.previousPaycyleDate['Salary_Date'],'Leaves')+" To "+fnFormatDate(result.previousPaycyleDate['Last_SalaryDate'],'Leaves')+")";
        let currentMonthDateRange = "("+fnFormatDate(result.currentPaycyleDate['Salary_Date'],'Leaves')+" To "+fnFormatDate(result.currentPaycyleDate['Last_SalaryDate'],"Leaves")+")";
        $('#viewMonthlyPrevMonthUnpaidLeaveLabel').text('Unpaid Leave '+previousMonthDateRange);
        $('#viewMonthlyUnpaidLeaveLabel').text('Unpaid Leave '+currentMonthDateRange);
    } else {
        $("#viewMonthlyPrevMonthUnpaidLeaveElement").hide();
    }

    $("#payMonth").text("Payslip for the Month of " + result.Payslip_Month);
    $("#viewMonthlyEmployeeId").text(result.Payslip['User_Defined_EmpId']);
    $("#viewMonthlyEmployeeName").text(employeeName);
    $("#viewMonthlyDesignation").text(result.Payslip['Designation_Name']);
    $("#viewMonthlyDateOfJoin").text(result.Payslip['Date_Of_Join']);
    $("#viewMonthlyDaysWorked").text(worked_count);
    $("#viewMonthlyPaidDays").text(result.PaidDays);
    $("#viewMonthlyDateOfRelieving").text(fnCheckNull(result.Payslip['Emp_Relieving_Date']));
    $("#viewMonthlyPaidLeave").text(paidleave + levDetails[0]);
    $("#viewMonthlyUnpaidLeave").text(unPaidleave + levDetails[1]);
    $("#viewMonthlyOnDuty").text(onDuty);
    $("#viewMonthlyDepartment").text(result.Payslip['Department_Name']);
    $("#viewMonthlyPFAccountNumber").text(pfPolicy);
    $("#viewMonthlyESI").text((result.Payslip['Policy_No']) ? (result.Payslip['Policy_No']) : "-");
    $("#viewMonthlyPANNo").text(fnCheckNull(result.Emp_Pan));
    $("#viewMonthlUAN").text(fnCheckNull(result.Payslip['UAN']));
    $("#viewMonthlyBankName").text(fnCheckNull(result.Payslip['Bank_Name']));
    $("#viewMonthlyAadhaarNo").text(fnCheckNull(result.Payslip['Aadhaar_Card_Number']));
    $("#viewMonthlyBankAccountNo").text(fnCheckNull(result.Payslip['Bank_Account_Number']));
    if(payslipType == "Hourly"){
        $("#viewHoursWorked").text(result.WorkedHours);
        $("#viewDayWages").text(result.actualHours['dayWage']);
        $("#viewOTHours").text(fnCheckNull(result.OverTimeHours));
        $("#viewHoliday").text(fnCheckNull(result.Holiday));
    }
}
/* view print and pdf for payslip templates */
function view_pdf_payslip_template(Payslip_Id, Employee_Name, Template_Id,type,templateResult,formName,payslipType,domainName, orgCode) {
    setMask('#wholepage');
    // get file content by passing template location
    $.ajax({
        type: 'POST',
        dataType: 'json',
        async: true,
        url: pageUrl() + 'forms-manager/form-downloads/get-file-content',
        data: {
            key: domainName + "/" + orgCode + "/" + "Payslip Template/" + payslipType + "/" + Template_Id,
            formName: formName,
            bucketName: 'bucketName'
        },
        success: function (result) {
            var file = result;
            // remove bootstrap style, because it override other styles.
            var div = document.createElement('div');
            div.innerHTML = file;
            var elements = div.getElementsByTagName('link');
            while (elements[0])
                elements[0].parentNode.removeChild(elements[0])
            var file_content = div.innerHTML;
            if(!templateResult){
                // assign retrieved content to view form
                $('#viewFormMonthlyPayslip').html(file_content);
                $('#viewFormMonthlyPayslip').append(`<div style="text-align:center; font-size:10px; color:#000; margin-top:10px">'."This is system generated report. No signature is required".'</div>`)
                if(payslipType == "Monthly"){
                    $('#modalViewSalaryPayslipTemplate .modal-title,#modalViewPayoutPayslipTemplate .modal-title').html("<strong>View<strong> Monthly Salary Payslip");
                }else{
                    $('#modalViewSalaryPayslipTemplate .modal-title,#modalViewPayoutPayslipTemplate .modal-title').html("<strong>View<strong> Hourly Wages Payslip");
                }
                // call this function to display values
                viewPayslipTemplateContent(true, Employee_Name,payslipType,Payslip_Id,formName, type, 'viewFormMonthlyPayslip');
            }
            else{
                // assign retrieved content to view form
                $('#viewFormPayslipTemplate').html(file_content);
                if(payslipType == "Monthly"){
                    $('#modalViewPayslipTemplate .modal-title').html("<strong>View<strong> Monthly Salary Payslip");
                }else{
                    $('#modalViewPayslipTemplate .modal-title').html("<strong>View<strong> Hourly Wages Payslip");
                }
                // call this function to display values
                viewPayslipTemplateContent(templateResult, 'Xxx Yyy',payslipType,null,formName, type, 'viewFormPayslipTemplate');
            }
        },
        error: function (error) {
            if (error.status == 200) {
                sessionExpired();
            } else {
                jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
            }
            removeMask();
        }
    });
}
//  function to convert number to words
function number_to_words(amount){
   if(amount != 0){
    var words = new Array();
    words[0] = '';
    words[1] = 'One';
    words[2] = 'Two';
    words[3] = 'Three';
    words[4] = 'Four';
    words[5] = 'Five';
    words[6] = 'Six';
    words[7] = 'Seven';
    words[8] = 'Eight';
    words[9] = 'Nine';
    words[10] = 'Ten';
    words[11] = 'Eleven';
    words[12] = 'Twelve';
    words[13] = 'Thirteen';
    words[14] = 'Fourteen';
    words[15] = 'Fifteen';
    words[16] = 'Sixteen';
    words[17] = 'Seventeen';
    words[18] = 'Eighteen';
    words[19] = 'Nineteen';
    words[20] = 'Twenty';
    words[30] = 'Thirty';
    words[40] = 'Forty';
    words[50] = 'Fifty';
    words[60] = 'Sixty';
    words[70] = 'Seventy';
    words[80] = 'Eighty';
    words[90] = 'Ninety';
    amount = amount.toString();
    var atemp = amount.split(".");
    var number = atemp[0].split(",").join("");
    var n_length = number.length;
    var words_string = "";
    if (n_length <= 9) {
        var n_array = new Array(0, 0, 0, 0, 0, 0, 0, 0, 0);
        var received_n_array = new Array();
        for (var i = 0; i < n_length; i++) {
            received_n_array[i] = number.substr(i, 1);
        }
        for (var i = 9 - n_length, j = 0; i < 9; i++, j++) {
            n_array[i] = received_n_array[j];
        }
        for (var i = 0, j = 1; i < 9; i++, j++) {
            if (i == 0 || i == 2 || i == 4 || i == 7) {
                if (n_array[i] == 1) {
                    n_array[j] = 10 + parseInt(n_array[j]);
                    n_array[i] = 0;
                }
            }
        }

        value = "";
        for (var i = 0; i < 9; i++) {
            if (i == 0 || i == 2 || i == 4 || i == 7) {
                value = n_array[i] * 10;
            } else {
                value = n_array[i];
            }
            if (value != 0) {
                words_string += words[value] + " ";
            }
            if ((i == 1 && value != 0) || (i == 0 && value != 0 && n_array[i + 1] == 0)) {
                words_string += "Crore ";
            }
            if ((i == 3 && value != 0) || (i == 2 && value != 0 && n_array[i + 1] == 0)) {
                words_string += "Lakh ";
            }
            if ((i == 5 && value != 0) || (i == 4 && value != 0 && n_array[i + 1] == 0)) {
                words_string += "Thousand ";
            }
            if (i == 6 && value != 0 && (n_array[i + 1] != 0 && n_array[i + 2] != 0)) {
                words_string += "Hundred and ";
            } else if (i == 6 && value != 0) {
                words_string += "Hundred ";
            }
        }
        words_string = words_string.split("  ").join(" ");
    }
    return words_string;
   }else{
       return 'Zero';
   }
}
/* view hourly payslip function */
function viewHourlyPayslip(payslipId,employeeName,calledFrom){
    setMask('#wholepage');
    $.ajax ({
        type     : 'POST',
        dataType : 'json',
        async    : true,
        url      : pageUrl () +'payroll/salary-payslip/view-wage-payslip/payslipId/'+ payslipId+'/calledFrom/'+calledFrom,
        success  : function (result)
        {
            if (isJson (result))
            {
                if (result.success)
                {
                    var paidleave = ((result.PaidLeave[1] != null) ? result.PaidLeave[1] : 0);
                    var pfPolicy = ((result.Payslip['Pf_PolicyNo']!=null) ? result.Payslip['Pf_PolicyNo'] : '-');
                    var unPaidleave = ((result.UnpaidLeave[1] != null) ? result.UnpaidLeave[1] : 0);
                    var emptyDedHrly = 0;
                    // function to get leave details
                    var levDetails = fngetLeaveDetails(result.PaidLeave[2],result.UnpaidLeave[2]);

                    $('#modalFormHourlyWagesPayslip .modal-title').html("<strong>View<strong> Hourly Wages Payslip");

                    $("#viewHourlyEmployeeId").text(result.Payslip['User_Defined_EmpId']);
                    $("#viewHourlyUAN").text(fnCheckNull(result.Payslip['UAN']));
                    $("#viewHourlyBankName").text(fnCheckNull(result.Payslip['Bank_Name']));
                    $("#viewHourlyBankAccountNo").text(fnCheckNull(result.Payslip['Bank_Account_Number']));
                    $("#viewHourlyPanNo").text(fnCheckNull(result.Emp_Pan));

                    $("#earnHourlyAmt").text("Amount"+((result.Payslip['Currency'])?"("+result.Payslip['Currency']+")":''));
                    $("#deductHourlyAmt").text("Amount"+((result.Payslip['Currency'])?"("+result.Payslip['Currency']+")":''));


                    $('#imagesHourly').html("");

                    if(result.Image !== null && result.Image !== ''){

                        var sUrl = fngetSignedUrl(result.Image,'logoBucket');
                        if ($.inArray(sUrl, ['', null,'sessionexpired','internalerror']) === -1) {
                            $('#imagesHourly').append('<img id="orgLogoInHourlyPayslip" style="width: 100px;height:100%;" class="img-responsive" alt="Report Logo" data-src="" src="'+sUrl+'">');
                        }
                        else
                        {
                            $('#images').html("");
                        }
                    }
                    else{
                        $('#imagesHourly').html("");
                    }
                    //If show report creator flag is enabled, then only show the who generate the report.
                    if($('#showReportCreator').val() == 1)
                    {
                        $("#formHourlyReport").text("Report generated by"+result.Payslip['First_Name']+" "+result.Payslip['Last_Name']+" on "+result.Payslip['Generated_On']);
                    }
                    //If display payslip address flag is enabled, then only show the organization address in payslip.
                    if($('#displayPayslipAddress').val() == 1)
                    {
                        $("#formHourlyHeader").text(result.Org_Name['Org_Name']);
                        $("#formHourlyAddress").text(result.Payslip['Street1']+','+result.Payslip['Street2']);
                        $("#formHourlyAddress1").text(result.Payslip['City']+','+result.Payslip['State']+','+result.Payslip['Country']+'-'+result.Payslip['Pincode']);
                    }
                    $("#payHourlyMonth").text("Payslip for the Month of "+result.Payslip_Month);
                    $("#viewHourlyEmployeeName").text(employeeName);
                    $("#viewHourlyDesignation").text(result.Payslip['Designation_Name']);
                    $("#viewHourlyPaidLeave").text(paidleave+levDetails[0]);
                    $("#viewHourlyDepartment").text(result.Payslip['Department_Name']);
                    $("#viewHourlyDateOfJoin").text(result.Payslip['Date_Of_Join']);
                    $("#viewHourlyPFAccountNumber").text(pfPolicy);
                    $("#viewHourlyESI").text(fnCheckNull(result.Payslip['Policy_No']));
                    $("#viewHourlyUnpaidLeave").text(unPaidleave+levDetails[1]);
                    $("#viewHourlyDaysWorked").text(result.actualHours['daysWorked']);
                    $("#viewHourlyRegularHours").text(result.WorkedHours);
                    $("#viewHourlyDayWages").text(result.actualHours['dayWage']);
                    $("#viewHourlyOtHours").text(fnCheckNull(result.OverTimeHours));
                    $("#viewHourlyHolidays").text(fnCheckNull(result.Holiday));

                   var maxVal = Math.max((result.Deduction).length,(result.Incentive).length);
                    var totalIncentive = 0;
                    var totalDeduction= 0;
                    var lastArry = [];
                    var deductArray = [];

                    for(var i=0;i<maxVal;i++)
                    {
                        if(result.Incentive[i])
                        {
                            if (result.Incentive[i]['Incentive_Name']!=undefined)
                            {
                                totalIncentive = parseFloat(totalIncentive) + parseFloat(result.Incentive[i]['Incentive_Amount']);
                                if (result.Incentive[i]['Description'])
                                {
                                    $('#earningsHourlyTab tbody').append('<tr id="incentHrly" class="child"><td class="col-md-10 text-left" style="height:40px">'+result.Incentive[i]['Incentive_Name']+' - '+result.Incentive[i]['Description']+'</td><td class="text-right" style="height:40px">'+result.Incentive[i]['Incentive_Amount']+'</td></tr>');
                                }
                                else
                                {
                                    $('#earningsHourlyTab tbody').append('<tr id="incentHrly" class="child"><td class="col-md-10 text-left" style="height:40px">'+result.Incentive[i]['Incentive_Name']+'</td><td class="text-right" style="height:40px">'+result.Incentive[i]['Incentive_Amount']+'</td></tr>');
                                }
                            }
                            else
                            {
                                $('#earningsHourlyTab tbody').append('<tr class="child"><td style="height:40px"></td><td style="height:40px"></td></tr>');
                            }
                        }
                        else
                        {
                            $('#earningsHourlyTab tbody').append('<tr class="child"><td style="height:40px"></td><td style="height:40px"></td></tr>');
                        }
                    }

                    $('#earningsHourlyTab tbody').append('<tr class="child" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px">Total Earnings</td><td class="text-right" style="height:40px">'+totalIncentive.toFixed(2)+'</td></tr>');
                    //$('#earningsHourlyTab tbody').append('<tr class="child" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px">Total Intermediate Payment</td><td class="text-right" style="height:40px">'+totalIncentive.toFixed(2)+'</td></tr>');
                    $('#earningsHourlyTab tbody').append('<tr class="child" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px">Outstanding Amount</td><td class="text-right" style="height:40px">'+result.Payslip['Outstanding_Amt']+'</td></tr>');
                    // netpay in words
                    $('#netpayInWordHourly').html(result.Payslip['Currency'] ? `<b>: ${result.Payslip['Currency']}</b>`+'  '+ number_to_words(result.Payslip['Total_Salary']) + ' Only' : ': '+number_to_words(result.Payslip['Total_Salary']) + ' Only');
                    $('#netpayInWordHourly').prop('style', 'display:inline-block;');

                    for(var i=0;i<maxVal;i++)
                    {
                        if (result.Deduction[i])
                        {
                            if(result.Deduction[i]['Deduction_Name']!=undefined)
                            {
                                totalDeduction = parseFloat(totalDeduction)+parseFloat(result.Deduction[i]['Deduction_Amount']);

                                if(result.Deduction[i]['Description'])
                                {
                                   $('#deductHourlyTab tbody').append('<tr class="child"><td class="col-md-10 text-left" style="height:40px">'+result.Deduction[i]['Deduction_Name']+' - '+result.Deduction[i]['Description']+'</td><td class="text-right" style="height:40px">'+result.Deduction[i]['Deduction_Amount']+'</td></tr>');
                                }
                                else
                                {
                                    $('#deductHourlyTab tbody').append('<tr class="child"><td class="col-md-10 text-left" style="height:40px">'+result.Deduction[i]['Deduction_Name']+'</td><td class="text-right" style="height:40px">'+result.Deduction[i]['Deduction_Amount']+'</td></tr>');
                                }
                            }
                            else
                            {
                                emptyDedHrly++;
                                $('#deductHourlyTab tbody').append('<tr class="child"><td class="col-md-10 text-left"style="height:40px"></td><td style="height:40px"></td></tr>');
                            }
                        }
                        else
                        {
                            emptyDedHrly++;
                            $('#deductHourlyTab tbody').append('<tr class="child"><td class="col-md-10 text-left"style="height:40px"></td><td style="height:40px"></td></tr>');
                        }
                    }

                    $("#totEarnHrly").remove();
                    $("#netPayHrly").remove();
                    if(emptyDedHrly > 0 && (emptyDedHrly == (result.Incentive).length))
                    {
                        if($(window).width() < 991)
                        {
                            $('#deductDivHrly').prop({'class':'hidden-xs hidden-sm hidden-md'});
                            $('#earningsHourlyTab tbody').append('<tr class="child" id="totEarnHrly" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px">Total Deductions</td><td class="text-right" style="height:40px">'+totalDeduction.toFixed(2)+'</td></tr>');
                            $('#earningsHourlyTab tbody').append('<tr class="child" id="netPayHrly" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px">Netpay</td><td class="text-right" style="height:40px">'+result.Payslip['Total_Salary']+'</td></tr>');
                            //$('#earningsHourlyTab tbody').append('<tr class="child" id="netPayHrly" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px">Netpay</td><td class="text-right" style="height:40px">'+result.Payslip['Total_Salary']+'</td></tr>');
                        }
                        else
                        {
                            $('#deductHourlyTab tbody').append('<tr class="child" id="totEarnHrly" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px">Total Deductions</td><td class="text-right" style="height:40px">'+totalDeduction.toFixed(2)+'</td></tr>');
                            $('#deductHourlyTab tbody').append('<tr class="child" id="netPayHrly" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px">Netpay</td><td class="text-right" style="height:40px">'+result.Payslip['Total_Salary']+'</td></tr>');
                            //$('#deductHourlyTab tbody').append('<tr class="child" id="netPayHrly" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px">Netpay</td><td class="text-right" style="height:40px">'+result.Payslip['Total_Salary']+'</td></tr>');
                        }

                        $(window).on('resize', function() {
                            //winSize = $(window).width();
                            $("#totEarnHrly").remove();
                            $("#netPayHrly").remove();
                            if($(window).width() < 991 && emptyDedHrly > 0)
                            {
                                $('#deductDivHrly').prop({'class':'col-md-6 paddingCls hidden-xs hidden-sm hidden-md'});
                                $('#earningsHourlyTab tbody').append('<tr class="child" id="totEarnHrly" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px">Total Deductions</td><td class="text-right" style="height:40px">'+totalDeduction.toFixed(2)+'</td></tr>');
                                $('#earningsHourlyTab tbody').append('<tr class="child" id="netPayHrly" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px">Netpay</td><td class="text-right" style="height:40px">'+result.Payslip['Total_Salary']+'</td></tr>');
                                //$('#earningsHourlyTab tbody').append('<tr class="child" id="netPayHrly" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px">Netpay</td><td class="text-right" style="height:40px">'+result.Payslip['Total_Salary']+'</td></tr>');
                            }
                            else
                            {
                                $('#deductDivHrly').prop({'class':'col-md-6 paddingCls'});
                                $('#deductHourlyTab tbody').append('<tr class="child" id="totEarnHrly" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px">Total Deductions</td><td class="text-right" style="height:40px">'+totalDeduction.toFixed(2)+'</td></tr>');
                                $('#deductHourlyTab tbody').append('<tr class="child" id="netPayHrly" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px">Netpay</td><td class="text-right" style="height:40px">'+result.Payslip['Total_Salary']+'</td></tr>');
                                //$('#deductHourlyTab tbody').append('<tr class="child" id="netPayHrly" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px">Netpay</td><td class="text-right" style="height:40px">'+result.Payslip['Total_Salary']+'</td></tr>');
                            }
                        });
                    }
                    else
                    {
                        $('#deductDivHrly').prop({'class':'col-md-6 paddingCls'});
                        $('#deductHourlyTab tbody').append('<tr class="child" id="totEarnHrly" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px">Total Deductions</td><td class="text-right" style="height:40px">'+totalDeduction.toFixed(2)+'</td></tr>');
                        $('#deductHourlyTab tbody').append('<tr class="child" id="netPayHrly" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px">Netpay</td><td class="text-right" style="height:40px">'+result.Payslip['Total_Salary']+'</td></tr>');
                        //$('#deductHourlyTab tbody').append('<tr class="child" id="netPayHrly" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px">Netpay</td><td class="text-right" style="height:40px">'+result.Payslip['Total_Salary']+'</td></tr>');

                         $(window).on('resize', function() {
                            $("#totEarnHrly").remove();
                            $("#netPayHrly").remove();
                            $('#deductDivHrly').prop({'class':'col-md-6 paddingCls'});
                            $('#deductHourlyTab tbody').append('<tr class="child" id="totEarnHrly" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px">Total Deductions</td><td class="text-right" style="height:40px">'+totalDeduction.toFixed(2)+'</td></tr>');
                            $('#deductHourlyTab tbody').append('<tr class="child" id="netPayHrly" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px">Netpay</td><td class="text-right" style="height:40px">'+result.Payslip['Total_Salary']+'</td></tr>');
                            //$('#deductHourlyTab tbody').append('<tr class="child" id="netPayHrly" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px">Netpay</td><td class="text-right" style="height:40px">'+result.Payslip['Total_Salary']+'</td></tr>');
                         });
                    }

                    if (result.salaryDetails.Is_Pf == 1 || result.salaryDetails.Is_Insurance == 1 ) {
                        $('#empContributionHourly').prop('style', 'display:block;border: 1px solid');
                        var  contributionDetails= fngetContributionDetails(result.orgPfAmt,result.orgPfAmtHeader,result.adminCharge,result.edliCharge,result.etfAmt,result.orgLwfAmount,
                            result.orgLWFAmtHeader,result.InsuranceDetails,result.FixedHealthInsurance,result.orgEtfAmtHeader,result.orgCPSAmtHeader,0);
                        $('#contributionTab1Hourly tbody,#contributionTab2Hourly tbody').html('');
                        if(contributionDetails.length > 0){
                        $(window).on('resize', function() {
                            $('#contributionTab1Hourly tbody,#contributionTab2Hourly tbody').html('');
                            if ($(window).width() < 991) {
                                $('#emptyContributionDivHourly').prop('style', 'height: 20px;');
                                for(i=0; i< contributionDetails.length; i++){
                                    $('#contributionTab1Hourly tbody').append(contributionDetails[i]);
                                }
                            }
                            else {
                                $('#emptyContributionDivHourly').prop('style', 'height: 40px;border-left: 1px solid;border-right: 1px solid;');
                                for(i=0; i< contributionDetails.length; i++){
                                    if(i%2 == 0){
                                    $('#contributionTab1Hourly tbody').append(contributionDetails[i]);
                                    }else{
                                    $('#contributionTab2Hourly tbody').append(contributionDetails[i])
                                    }
                                }
                                if(contributionDetails.length%2 != 0){
                                    $('#contributionTab2Hourly tbody').append('<tr class="child hidden-xs hidden-sm" style="border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;"></td><td class="text-right" style="height:40px"></td></tr>');
                                }
                            }
                        });
                        if ($(window).width() < 991) {
                            $('#emptyContributionDivHourly').prop('style', 'height: 20px;');
                            for(i=0; i< contributionDetails.length; i++){
                                $('#contributionTab1Hourly tbody').append(contributionDetails[i]);
                             }
                        }
                        else {
                            $('#emptyContributionDivHourly').prop('style', 'height: 40px;border-left: 1px solid;border-right: 1px solid;');
                            for(i=0; i< contributionDetails.length; i++){
                                if(i%2 == 0){
                                $('#contributionTab1Hourly tbody').append(contributionDetails[i]);
                                }else{
                                $('#contributionTab2Hourly tbody').append(contributionDetails[i])
                                }
                            }
                            if(contributionDetails.length%2 != 0){
                                $('#contributionTab2Hourly tbody').append('<tr class="child hidden-xs hidden-sm" style="border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;"></td><td class="text-right" style="height:40px"></td></tr>');
                            }
                        }
                        }else{
                            $('#emptyContributionDivHourly').prop('style', 'height: 0px;');
                            $('#empContributionHourly').prop('style', 'display:none');
                        }
                    }
                    else{
                        $('#emptyContributionDivHourly').prop('style', 'height: 0px;');
                        $('#empContributionHourly').prop('style', 'display:none');
                    }

                    $('#modalFormHourlyWagesPayslip').modal('toggle');
                    $('#formGenerateHourlyPayslip, #formGenerateHourlyPayslipSubGrid').hide();
                    $('#formActionHourlyWagesPayslip').hide();
                    $('#viewFormHourlyPayslipUser').show();
                    $('#viewFormHourlyPayslip').show();
                }
            }
            else
            {
                sessionExpired ();
            }
            removeMask();
        },
        error: function(error){
            if (error.status == 200) {
                sessionExpired();
              }
            else {
                  /* To handle internal server error */
                  jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
              }
            removeMask();
        }
    });
}

$('#formForgetPasswordSubmit').on('click', function(){
    if ($("#editForgetPassword").valid())
    {
        $.ajax ({
            type     : 'POST',
            async    : false,
            dataType : "json",
            url      : pageUrl ()+'auth/index/mobile-reset/user/'+$('#formCPUserName').text()+'/reseturl/'+$('#paramurl').val(),
            data     : {
                newPassword        : $('#formCPNewPassword').val(),
                conformNewPassword : $('#formCPConfirmPassword').val()
            },
            success  : function(result)
            {
                if (result.success)
                {
                    jAlert({ panel:$('#editForgetPassword'), msg : result.msg, type : result.type });

                    top.location.href = pageUrl() + result.redirect;
                }
                else
                {
                    jAlert({ panel:$('#editForgetPassword'), msg : result.msg, type : result.type });
                }
            }
        });
    }
});

$('.convertDecimalPos1').on('change', function () {
        $(this).val( parseFloat ($(this).val()).toFixed(2) );
});

$('.parseInteger').on('change', function () {
    $(this).val( parseInt ($(this).val()));
});

/** Grid Headers */
function gridHeader(cells)
{
   headerArray=[];
   $.each(cells, function(i,v) {
       //Do not include employee id column in small and medium screen
       if(v.innerText != 'Employee Id')
       {
           i= i-1;
           headerArray['Header'+i] = v.innerText;
       }
   });
}

/** Reload Combo **/
function fnReloadCombo(field,list,type,purpose)
{
    if(list !== '' && list !== null){
        field.find('option').remove();

        if(purpose !== undefined && purpose == 'form')
        {
            if(type !== undefined && type == 'multiselect'){
                field.append( "<option value='selectAll'>--Select all--</option>");
                field.append( "<option value='clearAll'>--Clear all--</option>");
            }
            else{
                field.append( "<option value=''>-- Select --</option>");
            }
        }
        else if(purpose !== undefined && purpose == 'filter')
        {
              field.append( "<option value=''>All</option>");
        }
        $.each(list, function(key, row){
            field.append('<option value='+key+'>'+row+'</option>');
        });
    }
    else{
        field.find('option').remove();
        //field.append( "<option value=''>-- Select --</option>");
        if(purpose !== undefined && purpose == 'form')
        {
            if(type !== undefined && type == 'multiselect'){
                field.append( "<option value='selectAll'>--Select all--</option>");
                field.append( "<option value='clearAll'>--Clear all--</option>");
            }
            else{
                field.append( "<option value=''>-- Select --</option>");
            }
        }
        else if(purpose !== undefined && purpose == 'filter')
        {
              field.append( "<option value=''>All</option>");
        }
    }
}

    //On top bar menu change password button click event
    $('#menuChangePassword').on('click', function(){
        $('#modalMenuFormChangePassword').modal('toggle');
        $('#formResetChangePassword').trigger('click');
    });

    // Verifiying whether new pwd & confirm pwd are same
    $('#formCPNewPassword, #formCPConfirmPassword').on('keyup keypress blur change', function(){
        var newPassword = $('#formCPNewPassword').val();
        var confirmPassword = $('#formCPConfirmPassword').val();
        var newPasswordValid = 0;
        var confirmNewPasswordValid = 0;

        //$('#formCPNewPassword, #formCPConfirmPassword').removeClass("pwdCharError");
        //$('#formCPConfirmPassword').removeClass("newPasswordError");

        if (newPassword != '' && newPassword.length >= 6 )
        {
            if (!(/^[\w\#\.\/\-\!\@\$\%\*\&\_\+\=\?]+$/.test(newPassword)))
            {
                $('#formCPNewPassword').addClass("pwdCharError");

                $.validator.addMethod('vPwdCharError', function () { return false; }, 'Invalid Symbol');

                $.validator.addClassRules("pwdCharError", { vPwdCharError : true });
            }
            else
            {
                newPasswordValid = 1;

                $('#formCPNewPassword').removeClass("pwdCharError");
            }
        }

        if (confirmPassword != '' && confirmPassword.length >= 6)
        {
            if (!(/^[\w\#\.\/\-\!\@\$\%\*\&\_\+\=\?]+$/.test(confirmPassword)))
            {
                $('#formCPConfirmPassword').addClass("pwdCharError");

                $.validator.addMethod('vPwdCharError', function () { return false; }, 'Invalid Symbol');

                $.validator.addClassRules("pwdCharError", { vPwdCharError : true });
            }
            else
            {
                confirmNewPasswordValid = 1;

                $('#formCPConfirmPassword').removeClass("pwdCharError");
            }
        }

        if (newPasswordValid == 1 && confirmNewPasswordValid == 1)
        {
            if (newPassword == confirmPassword)
            {
                $('#formCPConfirmPassword').removeClass("newPasswordError");
            }
            else
            {
                $('#formCPConfirmPassword').addClass("newPasswordError");

                $.validator.addMethod('vNewPasswordError', function () { return false; }, "Password doesn't match.");

                $.validator.addClassRules("newPasswordError", { vNewPasswordError : true });
            }
        }
    });

    //On reset change password form
    $('#formResetChangePassword').on('click', function(){
        $('#formCPOldPassword, #formCPNewPassword, #formCPConfirmPassword').val('');
        $('#formCPNewPassword, #formCPConfirmPassword').removeClass("pwdCharError");
        $('#formCPConfirmPassword').removeClass("newPasswordError");
        $('#editFormChangePassword').validate().resetForm();
    });


    //On submit change password form
    $('#formSubmitChangePassword').on('click', function(){
        if ($("#editFormChangePassword").valid())
        {
            $.ajax ({
                type     : 'POST',
                async    : false,
                dataType : "json",
                url      : pageUrl ()+'employees/employees/change-employee-password/uId/'+$('#loginEmpId').val() +'/pId/'+$('#loginEmpId').val(),
                data     : {
                    userName           : $('#formCPUserName').html(),
                    oldPassword        : $('#formCPOldPassword').val(),
                    newPassword        : $('#formCPNewPassword').val(),
                    conformNewPassword : $('#formCPConfirmPassword').val()
                },
                success  : function(result)
                {
                    if (result.success)
                    {
                        jAlert({ msg : result.msg, type : result.type });

                        $('#modalMenuFormChangePassword').modal('toggle');
                    }
                    else
                    {
                        jAlert({ panel:$('#editFormChangePassword'), msg : result.msg, type : result.type });
                    }
                }
            });
        }
    });

    /** Show self approval instructions when user clicks read more in the self approval notification  */
    $('#formSubmitSelfApprovalInstructions').on('click', function(){
        var l = Ladda.create(this);
        l.start();
        $('#modalSelfApprovalInstructions').modal('toggle');
    });

    /** show the registration status in self approval notification */
    $('#registrationStatus').on('click', function(){

        setMask('#wholepage');

        $.ajax ({
            type     : 'POST',
            async    : true,
            dataType : "json",
            url      : pageUrl ()+'default/index/retrieve-bank-account-status',
            data     : { },
            success  : function(result)
            {
                removeMask();
                if(result.isRegistrationRetrievedStatus == 1)
                {
                    jAlert({msg : result.msg, type :'info' });
                    $('#modalSelfApprovalInstructions').modal('toggle');
                }
                else
                {
                    jAlert({ panel: $('#formSelfApprovalInstructions'), msg : result.msg, type :'warning' });
                }
            }
         });
    })

    ////Avoid negative value in filter
    //$('.bulider,.inner input[type=number]').on('change', function(){
    //    if(!isNaN($(this).val()))
    //    {
    //        $(this).val(Math.abs($(this).val()));
    //    }
    //});

/**
 *  Validation for email
*/
function validateEmail(email) {
    var re = /^[\w-\.]+@([\w-]+\.)+[\w-]{2,24}$/;

    return re.test(email);
}

/**
 *  fnResetPassword () used to reset user password
*/
function fnResetPassword (mailId, l) {
    $.ajax({
        type     : 'POST',
        dataType : 'json',
        async    : false,
        url      : pageUrl() + "auth/index/lost",
        data     : { forgotPwd : mailId },
        success  : function(result)
        {
            if (result.success)
            {
                jAlert({ panel : $('.form-password'),  msg : result.msg, type : result.type });

                $('#user-mail').val('');

                l.stop();
            }
            else
            {
                //jAlert({ msg : result.msg, type : result.type });
                $('#errorResetPanel').html(result.msg).show();

                l.stop();
            }
        }
    });
}

function pad(number, length) {
	var str = "" + number;
	while (str.length < length)
    {
		str = '0'+str;
	}
	return str;
}

function startTime() {
    var timeZone = $('#att-timeZone').text();
    $('.att-time').html(formatAMPM((timeZone.replace("UTC", " ")).replace(":", ".")));
    var t = setTimeout(startTime, 1000);
}

function formatAMPM(offset) {
    var splOffset = offset.split(".");

    switch(splOffset[1])
    {
        case '30' : crtOffset = '50'; break;
        case '45' : crtOffset = '75'; break;
        default: crtOffset = splOffset[1];
    }

    var utcDate = new Date( new Date().getTime() + (splOffset[0] + '.'+crtOffset) * 3600 * 1000);
    var hours = utcDate.getUTCHours();
	var minutes = utcDate.getUTCMinutes();
	var seconds = utcDate.getUTCSeconds();
    var ampm = hours >= 12 ? 'pm' : 'am';

	hours = hours % 12;
	hours = hours ? hours : 12; // the hour '0' should be '12'

	hours = hours < 10 ? '0'+hours : hours;
	minutes = minutes < 10 ? '0'+minutes : minutes;
	seconds = seconds < 10 ? '0'+seconds : seconds;

	var strTime = hours + ' : ' + minutes + ' : ' + seconds + ' ' + ampm;

	return strTime;
}

function jAlert (alertData) {

    if (alertData['panel'])
    {
        modalName=alertData['panel'].parent().parent().parent().parent().prop('id');
        fnModalScrollTop(modalName);
        panel = alertData['panel'];

        //made noty_container_type_alert animated bounceInLeft
        //made noty_container_type_alert animated bounceInLeft
	}
    else
    {
        panel =  $('.msgContentPanel');
    }
      $('.noty_container_type_alert').remove();
//    panel = (alertData['panel']) ? alertData['panel'] : $('.msgContentPanel');

    if (typeof (alertData['nonCaseConversionMsg']) != "undefined" && alertData['nonCaseConversionMsg'] !== null) {
        msg = alertData['nonCaseConversionMsg'];
    }
    else
    {
        string = alertData['msg'];
        msg = string.charAt(0).toUpperCase() + string.substr(1).toLowerCase();
    }


    panel.noty({
		text        : '<div class="alert alert-'+alertData['type']+'"><p><strong>'+msg+'.</p></div>',
		layout      : 'top', //or left, right, bottom-right...
		theme       : 'made',
		maxVisible  : 10,
		animation   : {
			open  : 'animated bounceInLeft',
			close : 'animated bounceOutLeft'
		},
		timeout: 10000
	});
}
// this function used for showing more information in alert.
function jAlertLong (alertData) {

    if (alertData['panel'])
    {
        modalName=alertData['panel'].parent().parent().parent().parent().prop('id');
        fnModalScrollTop(modalName);
        panel = alertData['panel'];
    }
    else
    {
        panel =  $('.msgContentPanel');
    }
      $('.noty_container_type_alert').remove();

    string = alertData['msg'];
   if (string && string.length >= 1)
      {
          var firstChar = string.charAt(0);
          var remainingStr = string.slice(1);
          string = firstChar.toUpperCase() + remainingStr;
          msg=string;
      }

    panel.noty({
		text        : '<div class="alert alert-'+alertData['type']+'"><p><strong>'+msg+'</p></div>',
		layout      : 'top', //or left, right, bottom-right...
		theme       : 'made',
		maxVisible  : 10,
		animation   : {
			open  : 'animated bounceInLeft',
			close : 'animated bounceOutLeft'
		},
		timeout: 20000
	});
}
function fnShowReminder(linkName)
{
    sameField = $('#'+linkName);
	if (sameField.hasClass(linkName))
	{
	    $('.hiddenGrid').prop('style', 'overflow: hidden; outline: none; display: none;margin:0px;');
	    $('#reminder').removeAttr('style','margin-bottom:0px;');
	    sameField.addClass('toggle'+linkName).removeClass(linkName);
	    $('#reminder').prop('style','margin-bottom:0px;');
        if (linkName === "attendanceEnforce" || linkName === "assetManagement" || linkName === "shiftScheduling" || linkName === "probationEmployee"
        || linkName === "fullAndFinalSettlementEmployee" || linkName === "lopRecoveryEmployee")
        {
            $('#'+linkName+'Grid').prop('style', 'overflow: hidden; outline: none; display: block;margin:0px; max-height:20000px;');
        }
        else
        {
             $('#'+linkName+'Grid').prop('style', 'overflow: hidden; outline: none; display: block;margin:0px;');
        }

	}
	else if (sameField.hasClass('toggle'+linkName))
	{
	        sameField.addClass(linkName).removeClass('toggle'+linkName);

		$('#'+linkName+'Grid').prop('style', 'overflow: hidden; outline: none; display: none;margin:0px;');
	}

}

function fnUpdateAttendance(id){
    var checkIcon = $('.att-in-out');
    var checkType = checkIcon.children('i');
    var checkinDataSource=findDevice();
    var checkoutDataSource=findDevice();
    $('#workPlaceModel').modal('hide');

    $.ajax({
        type     : 'POST',
        dataType : 'json',
        //async    : false,
        url      : pageUrl() + "employees/attendance/update-attendance/istheme/DashBoard",
        data     : {
        latitude  : latitude,
        longitude : longitude,
        workPlaceId : id,
        formSource : 'Dashboard Attendance',
        Checkin_Data_Source:checkinDataSource,
        Checkout_Data_Source:checkoutDataSource
                    },
        success  : function(result)
        {
            if (result != null && result != '')
            {
                if (result.success)
                {
                    if (checkType.hasClass('fa-sign-in'))
                    {
                        checkIcon.prop('title', 'Check Out');
                        checkType.addClass('fa-sign-out').removeClass('fa-sign-in');
                        $('.att-in-out-text').html('Check Out');


                        $('#totalEmpPresenceToday').html(result.totalPresence);

                        jAlert({ msg : result.msg, type : result.type });
                        timer.reset();
                        timer.start();

                        removeMask('.custom-page');

                        listAttendance (result.attList);
                    }
                    else if (checkType.hasClass('fa-sign-out'))
                    {
                        checkIcon.prop('title', 'Check In');
                        checkType.addClass('fa-sign-in').removeClass('fa-sign-out');
                        $('.att-in-out-text').html('Check In');


                        $('#totalEmpPresenceToday').html(result.totalPresence);

                        jAlert({ msg : result.msg, type : result.type });

                        timer.stop();

                        removeMask('.custom-page');
                        listAttendance (result.attList);
                    }
                }
                else
                {
                    jAlert({ msg : result.msg, type : result.type });

                    removeMask('.custom-page');
                }
            }
            else
            {
                jAlert({ msg : 'Your Session is timeout. Please login again.', type : 'warning' });
            }
        },
        error  : function (errorResult){
            removeMask('.custom-page');
            if(errorResult.status == 200){
                sessionExpired();
            }else{
                /* To handle internal server error */
                jAlert({ msg : 'Something went wrong. Please contact system admin.', type : 'warning' });
            }
        }
    });
}

function fnShowCheckinCheckoutList (field) {
    sameField = $('#show_hide_attcheckinoutlist');
	if (sameField.hasClass('attViewList'))
	{
		sameField.addClass('ZPalistsel').removeClass('attViewList');

		$('.attHiddenPanel').prop('style', 'display:block;');

		$('.ZPciicosel').prop('title', 'Hide attendance entries');

		$('#attentence_checkinout_time').prop('style', 'overflow: hidden; outline: none; display: block;');

		$('#attentence_weekview').prop('style', 'display: block;');
    }
	else if (sameField.hasClass('ZPalistsel'))
	{
		sameField.addClass('attViewList').removeClass('ZPalistsel');

		$('.attHiddenPanel').prop('style', 'display:none;');

		$('.ZPciicosel').prop('title', 'View attendance entries');

		$('#attentence_checkinout_time').prop('style', 'overflow: hidden; outline: none; display: none;');

		$('#attentence_weekview').prop('style', 'display: none;');
	}
}

function checkAttendance () {
    $.ajax({
		type     : 'POST',
        dataType : 'json',
        async    : false,
		url      : pageUrl() + "employees/attendance/get-employee-details/isAction/Check_Attendance",
		success  : function(result)
		{
            var checkIcon = $('.att-in-out');
            var checkType = checkIcon.children('i');

			if (result != null && result != '' && result != false)
			{
                if (result.PunchOut_Date == null)
                {
                    /**
                     *  When Page refresh If Punch In is Present and No Punch Out
                     *      1)  Hide Check In Button and Show Check Out Button
                     *      2)  Calculate Time Difference between Punch In Time and Current Time in Seconds
                     *      3)  Set the difference Seconds in Timer
                    */
                    checkIcon.prop('title', 'Check Out');
                    checkType.addClass('fa-sign-out').removeClass('fa-sign-in');
                    $('.att-in-out-text').html('Check Out');

                    timer.time(result.TimeSec);
                    timer.start();
                }
                else
                {
                    checkIcon.prop('title', 'Check In');
                    checkType.addClass('fa-sign-in').removeClass('fa-sign-out');
                    $('.att-in-out-text').html('Check In');

                    timer.stop();
                }
            }
			else
			{
				//No Punch In Record for login employee in this date

                checkIcon.prop('title', 'Check In');
                checkType.addClass('fa-sign-in').removeClass('fa-sign-out');
                $('.att-in-out-text').html('Check In');

                timer.stop();
			}
		}
	});
}

function listAttendance (attArr) {
    $('.att-list').empty();

    todayAttencance = '<li class="hed">'+
                            '<div>Check In</div>'+
                            '<div>Check Out</div>'+
                        '</li>';

    if (attArr.length > 0)
    {
        for (var x in attArr)
        {
            todayAttencance += '<li class="'+((x % 2 == 0) ? 'row-even' : 'row-odd')+'">'+
                '<div>' + attArr[x]['AttendancePunchInDate']+'</div>'+
                '<div>' + ((attArr[x]['AttendancePunchOutDate'] == null) ? '-' : attArr[x]['AttendancePunchOutDate'])+'</div>'+
                                '</li>';
        }
    }

    $('.att-list').html(todayAttencance);
}

function menuSearch (searchText) {
    var menuName = (searchText.val()).toLowerCase();

    if (menuName.length)
    {
        menuFilterArr = $('a[class*=menu-'+(menuName.replace(" ", "-"))+']');

        var isExist = [];

        $(menuFilterArr).each(function( index, value ) {
            var classArr = $(value).prop('class').split(/\s+/);

            $.each(classArr, function( subIndex, subValue ) {
                var menuArr = subValue.split('menu-');

                if (menuArr.length == 2)
                {
                    $('.'+subValue).css('display', 'block');

                    isExist.push(subValue);
                }
            });
        });

        var totalList = $('a[class*=menu-]');

        $.each(isExist, function( index, value ) {
            totalList = totalList.not('.'+value);
        });

        $(totalList).css('display', 'none');

        $('.module-level').each(function( index, value ) {
            if ($(this).children('.search-menu').is('[style="display: block;"]'))
            {
                $(this).css('display', 'block');
            }
            else
            {
                $(this).css('display', 'none');
            }
        });

        if (menuFilterArr.length)
        {
            $('#searchMenuTemp').html("");
        }
        else
        {
            $('#searchMenuTemp').html("No record found");
        }
    }
    else
    {
        $(".module-level, .search-menu").css('display', 'block');
    }
}

/* Get the rows which are currently selected */
function fnGetSelected( oTableLocal ) {
    return oTableLocal.$('tr.row_selected');
}

/**
 *  fnRefreshTable function used to refresh the grid table
*/
function fnRefreshTable (table) {
    //table.api().ajax.reload();
    //or
  table.fnDraw();
}

/**
 *  Check if the value is null or empty set '-'
*/
function fnCheckNull (value) {
    return (value == null || value === '') ? '-' : value;
}

/**
 *  fnCheckSelectVal() used to check value is empty or null
*/
function fnCheckSelectVal (value) {
    return (value == null || value <= 0) ? '' : value;
}

function hrappDepartmentClassification(formDivisionId,tableName,secondTableName)
{
    if($('body').data('page') == 'employees' || $('body').data('page') == 'attendance' || $('body').data('page') == 'leaves' ||
    $('body').data('page') == 'salary' || $('body').data('page') == 'salary-payslip' || $('body').data('page') == 'assignments'||
    $('body').data('page') == 'awards' || $('body').data('page') == 'compensatory-off' ||
    $('body').data('page') == 'employee-bank-account' || $('body').data('page') == 'employee-travel' ||
    $('body').data('page') == 'employees-document-upload' || $('body').data('page') == 'memos' || $('body').data('page') == 'performance-evaluation' ||
    $('body').data('page') == 'resignation' || $('body').data('page') == 'short-time-off' || $('body').data('page') == 'skillset-assessment' ||
    $('body').data('page') == 'timesheets' || $('body').data('page') == 'transfer' || $('body').data('page') == 'warnings' ||
    $('body').data('page') == 'allowances' || $('body').data('page') == 'advance-salary' || $('body').data('page') == 'bonus' ||
    $('body').data('page') == 'commission' || $('body').data('page') == 'deductions' || $('body').data('page') == 'etf' ||
    $('body').data('page') == 'final-settlement' || $('body').data('page') == 'fixed-health-insurance' || $('body').data('page') == 'flexi-benefit-declaration' ||
    $('body').data('page') == 'gratuity' || $('body').data('page') == 'gratuity-nomination' || $('body').data('page') == 'insurance' ||
    $('body').data('page') == 'loan' || $('body').data('page') == 'perquisite-tracker' || $('body').data('page') == 'provident-fund' ||
    $('body').data('page') == 'reimbursement' ||$('body').data('page') == 'shift-allowance' ||$('body').data('page') == 'tax-declarations' ||$('body').data('page') == 'tds-history'||
    $('body').data('page') == 'compliance-forms'|| $('body').data('page') == 'document-generator'|| $('body').data('page') == 'complaints'||
    $('body').data('page')=='hr-reports'|| $('body').data('page') == 'timeline' )
    {
            if(formDivisionId >= 0 && tableName!='')
            {
                $.ajax ({
                       type     : 'POST',
                       async    : false,
                       dataType : 'json',
                       url      : pageUrl() + 'employees/employees/set-hrapp-global-value/',
                       data     : {
                           Hrapp_Global_Value : formDivisionId
                       },
                       success  : function ()
                       {
                           localStorage.setItem('HrappDepartmentClassificationId',formDivisionId);
                           fnRefreshTable (tableName);
                           if(secondTableName!='')
                           {
                            fnRefreshTable (secondTableName);
                           }
                       }
                   });
            }

        }
}

function checkMaxEmployeeId(fieldId)
{
     formDivisionId = localStorage.getItem('HrappDepartmentClassificationId');
     $.ajax ({
                type     : 'POST',
                async    : false,
                dataType : 'json',
                url      : pageUrl() + 'employees/employees/get-max-employee-id/',
                data     : {
                    Hrapp_Global_Value : formDivisionId
                },
                success  : function (result)
                {
                    if(result > 0)
                    {
                       $(fieldId).val(result);
                    }
                }
            });

}

function checkUserDefinedEmployeeIdExist(userDefinedEmployeeId,employeeId,userDefineEmployeeFieldId)
{
    if($(userDefineEmployeeFieldId).val()){
        $.ajax ({
            type     : 'POST',
            async    : false,
            dataType : 'json',
            url      : pageUrl() + 'employees/employees/employeeid-exist/',
            data     : {
                employeeId            : employeeId,
                userDefinedEmployeeId : userDefinedEmployeeId,
            },
            success  : function (result)
            {
                if(result != null && result != ''){
                    if(!result.success)
                    {
                        $(userDefineEmployeeFieldId).addClass('vCheckEmployeeId');
                        $.validator.addMethod('vCheckEmployeeId', function (value) {
                            return false;
                        }, result.msg);
                        $(userDefineEmployeeFieldId).valid();
                    }
                    else
                    {
                        $(userDefineEmployeeFieldId).removeClass('vCheckEmployeeId');
                        $(userDefineEmployeeFieldId).valid();
                    }
                }
            }
        });
    }
}

function checkBiometricIntegrationIdExist(biometricIntegrationId,employeeId,biometricIntegrationFieldId)
{
    if($(biometricIntegrationFieldId).val()){
        $.ajax ({
            type     : 'POST',
            async    : false,
            dataType : 'json',
            url      : pageUrl() + 'employees/employees/biometric-integration-id-exist/',
            data     : {
                employeeId            : employeeId,
                biometricIntegrationId : biometricIntegrationId
            },
            success  : function (result)
            {
                if(result != null && result != ''){
                    if(!result.success)
                    {
                        $(biometricIntegrationFieldId).addClass('vckExternalEmpId');
                        $.validator.addMethod('vckExternalEmpId', function (value) {
                            return false;
                        }, result.msg);
                        $(biometricIntegrationFieldId).valid();
                    }
                    else
                    {
                        $(biometricIntegrationFieldId).removeClass('vckExternalEmpId');
                        $(biometricIntegrationFieldId).valid();
                    }
                }
            }
        });
    }
}


/* Function to get the authentication methods subscribed by the organization */
function fnGetAuthenticationMethods(emailButton, mobileButton, action){
    $.ajax ({
        type     : 'POST',
        dataType : 'json',
        async    : false,
        url      : pageUrl () +'auth/index/get-authentication-methods',
        success  : function (authenticationMethods)
        {
            let isEmailEnabled, isMobileEnabled = false;
            if(authenticationMethods.length > 0) {
                authenticationMethods.forEach(element => {
                    /* Authentication_Method_Id, 1, 3, 4 refers to the Email and password, Google, Microsoft authentication methods*/
                    if (parseInt(element.Authentication_Method_Id,10) === 1 || parseInt(element.Authentication_Method_Id,10) === 3 || parseInt(element.Authentication_Method_Id,10) === 4) {
                        isEmailEnabled = true;
                    } /* Authentication_Method_Id 2 refers to the Mobile authentication method*/
                    if (parseInt(element.Authentication_Method_Id,10) === 2) {
                        isMobileEnabled = true;
                    }
                });
            }
            if(!isEmailEnabled) {
                $(emailButton).addClass('btn-disabled');
                $(emailButton).removeClass('btn-secondary');
                $(emailButton).removeClass('btn-secondary-default');
                if(isMobileEnabled){
                    $(mobileButton).removeClass('btn-disabled');
                    $(mobileButton).removeClass('btn-secondary-default');
                    $(mobileButton).addClass('btn-secondary');
                    if(action === 'Edit')
                    $(mobileButton).trigger('click');
                }
            } else {
                $(emailButton).removeClass('btn-disabled');
                $(emailButton).removeClass('btn-secondary-default');
                $(emailButton).addClass('btn-secondary');
                if(action === 'Edit')
                $(emailButton).trigger('click');

                if(isMobileEnabled){
                    $(mobileButton).removeClass('btn-disabled');
                    $(mobileButton).addClass('btn-secondary-default');
                    $(mobileButton).removeClass('btn-secondary');
                }
            }

            if(!isMobileEnabled) {
                $(mobileButton).removeClass('btn-secondary');
                $(mobileButton).removeClass('btn-secondary-default');
                $(mobileButton).addClass('btn-disabled');
            }
        }, error :  function(getAuthMethodsError){
            if(getAuthMethodsError.status === 200){
                sessionExpired();
            } else {
                jAlert({ msg : "Something went wrong. Please contact system admin", type : "warning" });
            }
        }
    });
}

/* Enable or Disable authentication methods based on the authentication methods subscribed by the organization */
function enableOrDisableAuthenticationMethods(emailButton, mobileButton){
    /* If email method is enabled then add btn-secondary class and remove other class */
    if(!$(emailButton).hasClass('btn-disabled')){
        $(emailButton).addClass('btn-secondary');
        $(emailButton).removeClass('btn-disabled');
        $(emailButton).removeClass('btn-secondary-default');
        $(emailButton).trigger('click');
    }/* If mobile method is enabled then add btn-secondary class and remove other class */
    else if(!$(mobileButton).hasClass('btn-disabled')){
        $(mobileButton).addClass('btn-secondary');
        $(mobileButton).removeClass('btn-disabled');
        $(mobileButton).removeClass('btn-secondary-default');
        $(mobileButton).trigger('click');
    }
}

//Department
function fnGetDepartmentName(formName,fieldId,formDivisionId)
{
        $.ajax ({
            type     : 'POST',
            dataType : 'json',
            async    : false,
            url      : pageUrl () +'organization/departments/list-department-types',
            data     : {
                _fId   : formName,
                departmentId : formDivisionId,
            },
            success  : function (result)
            {
               $('#'+fieldId).find('option').remove();
               if(formName == 'Salary Payslip')
               {
                  $('#'+fieldId).append("<option value='selectAll'>--Select All--</option>");
                  $('#'+fieldId).append("<option value='clearAll'>--Clear all--</option>");
               }
               else
               {
                 $('#'+fieldId).append("<option value='select'>--Select--</option>");
               }
               $.each(result, function(k,v)
               {
                if(v != undefined)
                {
                    $('#'+fieldId).append("<option value='" + v['Department_Id'] + "'>" +v['Department_Name'] + "</option>");

                    $.each(v['Child'],function(a,b){

                        if(b['Parent_Type_Id'] == v['Department_Id'])
                        {
                            $('#'+fieldId).append("<option value='" + b['Department_Id'] + "'> &nbsp;&nbsp;" +b['Department_Name'] + "</option>");

                            var child = v['Child'].filter(function (person) { return person.Parent_Type_Id == b['Department_Id'] });

                            $.each(child,function(c,d)
                            {
                                $('#'+fieldId).append("<option value='" + d['Department_Id'] + "'> &nbsp;&nbsp;&nbsp;&nbsp;" +d['Department_Name'] + "</option>");
                            })
                        }
                        });
                }
            });
            }
        });
}


function fnGetEmployeeName(formName,fieldId,isMultiSelect=0)
{
    $.ajax ({
        type     : 'POST',
        dataType : 'json',
        async    : false,
        url      : pageUrl () +'default/employee-info/list-employee-details',
        data     : {
            _fId   : formName,
        },
        success  : function (result)
        {
            var employeeField;

            if (result && isJson (result))
            {
                if (result.length > 0)
                {
                    employeeField = $(fieldId);

                    /** For single select dropdown prefill the select option */
                    if(parseInt(isMultiSelect)===0){
                        employeeArray = '<option value="">--Select--</option>';
                    }else{
                        employeeArray = '<option value="selectAll">--Select all--</option>'+
						                '<option value="clearAll">--Clear all--</option>';
                    }

                    employeeField.find('option').remove();

                    var empDepartment = [];

                    for (var row in result) {
                       if($.inArray(result[row]['Department_Name'],empDepartment) == -1)
                        {
                            empDepartment.push(result[row]['Department_Name']);
                        }
                    }

                    for(var y in empDepartment)
                    {
                        employeeArray += '<optgroup label="'+empDepartment[y]+'">';

                        for(var x in result)
                        {
                            if(result[x]['Department_Name'] == empDepartment[y])
                            {
                                employeeArray += '<option value="' + result[x]['value'] + '" id="' + result[x]['User_Defined_EmpId'] + '">' + result[x]['text'] + '</option>'
                            }
                        }
                        employeeArray += '</optgroup>';
                    }

                    employeeField.append(employeeArray);

                    employeeField.select2('val', '');
                }
                else
                {
                    employeeField = $(fieldId);
                    employeeField.find('option').remove();
                    employeeField.select2('val', '');

                    if(formName=='Attendance' || formName=='Leaves')
                    {
                     jAlert ({ msg : 'No timesheet  hours alloted for your grade', type : 'info' });
                    }
                }
            }
            else
            {
                employeeField = $(fieldId);
                employeeField.find('option').remove();
                employeeField.select2('val', '');
            }
        },
        error :  function(getEmployeeNameErrorResult){
            var employeeField = $(fieldId);
            employeeField.find('option').remove();
            employeeField.select2('val', '');

            if(getEmployeeNameErrorResult === 200){
                sessionExpired();
            }
        }
    });
}

function setHrappDepartmentClassifcationCombo()
{
     if($('body').data('page') == 'login')
    {
        formDivisionId = 0;
        localStorage.setItem('HrappDepartmentClassificationId',formDivisionId);
    }
}
var setHrappDepartmentClassifcationCombo = setHrappDepartmentClassifcationCombo();




function fnOrgDate () {
    var dateformat = '';
    if($('body').data('page') != 'salary-payslip' && $('body').data('page') != 'login')
    {
        $.ajax({
            type     : 'POST',
            dataType : 'json',
            async    : false,
            url      : pageUrl() + 'employee-info/org-date-format',
            success  : function(result)
            {
                //dateformat = JSON.parse(result);
                dateformat = result;
            }
        });
    }
    return dateformat;
}


var dateFormat = fnOrgDate();

function fnHrappSystemDateFormat (fieldVal) {
    if (fieldVal)
	{
		var jsDateFormat;
		var dateValue = fieldVal.split('/');

		switch(dateFormat[0])
		{
            case 'mm/dd/yy' : jsDateFormat = new Date(dateValue[2], (dateValue[0]-1), dateValue[1]); break;
            case 'yy/mm/dd' : jsDateFormat = new Date(dateValue[0], (dateValue[1]-1), dateValue[2]); break;
            case 'yy/dd/mm' : jsDateFormat = new Date(dateValue[0], (dateValue[2]-1), dateValue[1]); break;
            case 'dd/mm/yy' :
            default         :


            jsDateFormat = new Date(dateValue[2], (dateValue[1]-1), dateValue[0]);
		}

		return jsDateFormat;
	}
}

/**
*
*/
function fnDateFormatChange (date) {
	var result = new Date(new Date(tzDate()).setHours(0,0,0,0));

	dateArr = date.split("/");

	if (dateArr.length != 3)
		dateArr = date.split("-");

	if (dateArr.length == 3)
	{
		var accessFormatArr = accessrights.Date_Format.php.split("/");

		for (var i in dateArr)
		{
			switch(accessFormatArr[i])
			{
				case "d":
					result = new Date(result.setDate(dateArr[i]));
					break;

				case "m":
					result = new Date(result.setMonth(dateArr[i]-1));
					break;

				case "Y":
					result = new Date(result.setFullYear(dateArr[i]));
					break;
			}
		}
	}

	return result;
}

/**
 *  fnDateFormatter used to format the date
*/
function fnDateFormatter (date) {
    if (date != '' && date != null)
    {
        date = new Date(date);
        y = date.getFullYear();
        m = date.getMonth() + 1;
        d = date.getDate();

        return (m < 10 ? ('0'+m) : m) + '/' + (d < 10 ? ('0'+d) : d) + '/' + y;
    }
    else
    {
        return '';
    }

}

/**
 *  fnDateFormatter used to format the date
*/
function fnServerDateFormatter (date) {
    if (date != '' && date != null)
    {
        date = new Date(date);
        y = date.getFullYear();
        m = date.getMonth() + 1;
        d = date.getDate();

        return y + '-' + (m < 10 ? ('0'+m) : m) + '-' + (d < 10 ? ('0'+d) : d);
    }
    else
    {
        return '';
    }
}

/**
 *  fnDateFormatter used to format the date
*/
function fnServerMonthFormatter (date) {

    if (date != '' && date != null)
    {
        date = '01 ,'+date;
        date = new Date(date);
        y = date.getFullYear();
        m = date.getMonth() + 1;
        return y + '-' + (m < 10 ? ('0'+m) : m);
    }
    else
    {
        return '';
    }
}


function fnServerDateTimeFormatter (dateTime) {
    if (dateTime != '' && dateTime != null)
    {
        date = new Date(dateTime);
        y = date.getFullYear();
        m = date.getMonth() + 1;
        d = date.getDate();

        h = date.getHours();
        min = date.getMinutes();

        return y + '-' + (m < 10 ? ('0'+m) : m) + '-' + (d < 10 ? ('0'+d) : d) + ' '+ (h < 10 ? ('0'+h) : h)+':'+ (min < 10 ? ('0'+min) : min)+':00';
    }
    else
    {
        return '';
    }
}

/**
 *  fnFormatDate used to change date format for grid loading
*/
function fnFormatDate (dateValue,formName) {
    if(formName != undefined && (formName == 'Leaves' || formName == 'Individuals' || formName == 'Candidates' || formName == 'Resignation' || formName=="Attendance Finalization")){
        if (dateValue != '' && dateValue != null){
             dateValue = dateValue.split('-');

             switch(dateFormat[0])
             {
                 case 'mm/dd/yy' : jsDateFormat = dateValue[1] + '/' + dateValue[2] + '/' + dateValue[0]; break;
                 case 'yy/mm/dd' : jsDateFormat = dateValue[0] + '/' + dateValue[1] + '/' + dateValue[2]; break;
                 case 'yy/dd/mm' : jsDateFormat = dateValue[0] + '/' + dateValue[2] + '/' + dateValue[1]; break;
                 case 'dd/mm/yy' :
                 default         :


                 jsDateFormat = dateValue[2] + '/' + dateValue[1] + '/' + dateValue[0];
             }
             return jsDateFormat;
         }
         else
         {
             return '';
         }
    } else if (formName == 'Job Post'){
        // check if dateValue is empty or not
        if (dateValue != '' && dateValue != null) {
            // Split date by using '/'. Because from backend itself it return in YYYY/MM/DD format
            dateValue = dateValue.split('/');
            switch (dateFormat[0]) {
                case 'mm/dd/yy': jsDateFormat = dateValue[1] + '/' + dateValue[2] + '/' + dateValue[0]; break;
                case 'yy/mm/dd': jsDateFormat = dateValue[0] + '/' + dateValue[1] + '/' + dateValue[2]; break;
                case 'yy/dd/mm': jsDateFormat = dateValue[0] + '/' + dateValue[2] + '/' + dateValue[1]; break;
                case 'dd/mm/yy':
                default:
                    jsDateFormat = dateValue[2] + '/' + dateValue[1] + '/' + dateValue[0];
            }
            return jsDateFormat;
        }
        else {
            return '';
        }
    }
    else{
        if (dateValue != '' && dateValue != null){
             dateValue = dateValue.split('/');

             switch(dateFormat[0])
             {
                 case 'mm/dd/yy' : jsDateFormat = dateValue[2] + '-' + dateValue[0] + '-' + dateValue[1]; break;
                 case 'yy/mm/dd' : jsDateFormat = dateValue[0] + '-' + dateValue[1] + '-' + dateValue[2]; break;
                 case 'yy/dd/mm' : jsDateFormat = dateValue[0] + '-' + dateValue[2] + '-' + dateValue[1]; break;
                 case 'dd/mm/yy' :
                 default         :


                 jsDateFormat = dateValue[2] + '-' + dateValue[1] + '-' + dateValue[0];
             }
             return jsDateFormat;
         }
         else
         {
             return '';
         }
    }
}

// change the date value (in org date format) to YYYY/MM/DD format - used in resignation for setting in the date picker
function setDateinPicker(date) {

    var date = date.split('/');
    var jsDateFormat = '';
    switch (dateFormat[0]) {
        case 'mm/dd/yy': jsDateFormat = date[2] + '/' + date[0] + '/' + date[1]; break;
        case 'yy/mm/dd': jsDateFormat = date[0] + '/' + date[1] + '/' + date[2]; break;
        case 'yy/dd/mm': jsDateFormat = date[0] + '/' + date[2] + '/' + date[1]; break;
        case 'dd/mm/yy':
        default:
            jsDateFormat = date[2] + '/' + date[1] + '/' + date[0];
    }
    return jsDateFormat;
}


/**
 *	getDifferBwDates function used to get difference between 2 dates.
*/
function fnDifferBwDates (startDate, endDate) {
	var date1 = new Date(startDate);
	var date2 = new Date(endDate);

	var timeDiff = Math.abs(date2.getTime() - date1.getTime());

	return Math.ceil(timeDiff / (1000 * 3600 * 24));
}

/**check the different between 2 dates */
function fnDiffDates (startDate, endDate) {
var date1 = new Date(startDate);
var date2 = new Date(endDate);

 var diff = date2 - date1;
 return diff;

}

/**
 *  Convert Time to seconds
*/
function fnTimeToSeconds (time) {
    time = time.split(/:/);
    return time[0] * 3600 + time[1] * 60;
}

// convert decimal to time in hh:mm:ss format
function convertDecimalToTime(value, isSeconnds = false) {
    var decimalTimeString = value;
    var decimalTime = parseFloat(decimalTimeString);
    decimalTime = decimalTime * 60 * 60;
    var hours = Math.floor((decimalTime / (60 * 60)));
    decimalTime = decimalTime - (hours * 60 * 60);
    var minutes = Math.floor((decimalTime / 60));
    decimalTime = decimalTime - (minutes * 60);
    var seconds = Math.round(decimalTime);
    if(hours < 10)
    {
        hours = "0" + hours;
    }
    if(minutes < 10)
    {
        minutes = "0" + minutes;
    }
    if(seconds < 10)
    {
        seconds = "0" + seconds;
    }
    if(isSeconnds) {
        return hours + ":" + minutes + ":" + seconds;
    } else {
        return hours + ":" + minutes;
    }
}

/**
 *	isJson function to check a string is valid JSON.
*/
function isJson (str) {
    if ($.inArray(str, ['', null]) === -1)
    {
        return true;
    }
    else
    {
        return false;
    }

//    try
//	{
//        JSON.parse(str);
//    }
//	catch (e)
//	{
//
//    }
}

/**
 *  Session Expired fucntion redirect to authenticate page
*/
function sessionExpired () {
    $('#sessionExpired').trigger('click');
}

/**
 *  fnGridButtons function used to set disable /enable when user select/unselect grid record
*/
function fnGridButtons (buttons, action) {
    if (action)
    {
        buttons.addClass('btn-white btn-on').removeClass('disabled btn-secondary-default btn-off');
    }
    else
    {
        buttons.addClass('disabled btn-secondary-default btn-off').removeClass('btn-white btn-on');
    }
}

/**
 *	setLock function used to set lock for that form
*/
function setLock (lockData) {
    setMask('wholepage');
    $.ajax({
        type     : 'POST',
        dataType : 'json',
        async    : false,
        url      : pageUrl()+'default/index/check-lock-form',
        data     : {
            formName : lockData['formName'],
            uniqueId : lockData['uniqueId']
        },
        success  : function(result)
        {
            if (isJson(result))
            {
                if (result.success)
                {
                    removeMask();
                    lockData['callback'](result);
                }
                else
                {
                    if (result.msg == 'Expired')
                    {
                        removeMask();
                        sessionExpired();
                    }
                    else
                    {
                        if ($.inArray(lockData['panel'], ['', undefined, null]) === -1)
                        {
                            removeMask();
                            jAlert({ panel: lockData['panel'], msg : result.msg, type : result.type });
                        }
                        else
                        {
                            //jAlert({ msg : result.msg, type : result.type });
                            if(result.msg == 'Unable to setLock'){
                                removeMask();
                                $('.logout').trigger('click');
                            }
                            else{
                                removeMask();
                                jAlert({ msg : result.msg, type : result.type });
                            }
                        }
                    }
                }
            }
            else
            {
                removeMask();
                jAlert({ msg : "Something went wrong. Please contact system admin", type : "warning" });
            }
        },
        error  : function (error){
            removeMask();
            if(error.status == 200){
                sessionExpired();
            }else{
                /* To handle internal server error */
                jAlert({ msg : "Something went wrong. Please contact system admin", type : "warning" });
            }
        }
    });
}

/**
 *	checkLock function used to check user have lock for that form
*/
function checkLock (lockData) {
    $.ajax({
        type     : 'POST',
        dataType : 'json',
        async    : false,
        url      : pageUrl()+'default/index/check-user-lock-form',
        data     : {
            formName : lockData['formName']
        },
        success  : function(result)
        {
            if (isJson(result))
            {
                if (result.success)
                {
                    lockData['callback'](result);
                }
                else
                {
                    if (result.msg == 'Expired')
                    {
                        sessionExpired();
                    }
                    else
                    {
                        jAlert({ msg : result.msg, type : result.type });
                    }
                }
            }
            else
            {
                /* To handle internal server error */
                jAlert({ msg : "Something went wrong. Please contact system admin", type : "warning" });
            }
        }, error  : function (error){
            if(error.status == 200){
                sessionExpired();
            }else{
                /* To handle internal server error */
                jAlert({ msg : "Something went wrong. Please contact system admin", type : "warning" });
            }
        }
    });
}

/**
 *	clearLock function used to
*/
function clearLock (lockData) {
    $.ajax({
        type     : 'POST',
        dataType : 'json',
        async    : false,
        url      : pageUrl() + 'default/index/clear-session',
        data     : {
            formName : lockData['formName'],
            uniqueId : lockData['uniqueId']
        },
        success  : function(result)
        {
            if (result.success)
            {
                lockData['callback'](result);
            }
            else
            {

            }
        }, error  : function (error){
            if(error.status == 200){
                sessionExpired();
            }else{
                /* To handle internal server error */
                jAlert({ msg : "Something went wrong. Please contact system admin", type : "warning" });
            }
        }
    });
}

// Set Value to select field dynamically
function fnSetSelectFieldValues (selectArray) {
    var field  = selectArray['field'];
        resultAry = selectArray['result'];

//   field.prop('disabled', false).find('option').remove();
field.prop('disabled', false).find('option').remove();



    field.prop('required', true);
    if((resultAry.Manager).length == 1)
    {
        result = resultAry.Manager;
    }
    else if((resultAry.Manager).length > 1)
    {
        //$("#formForwardTo").select2('val',resultAry.ActualManager[0].Manager_Id);
        result = resultAry.Manager;
    }
    else
    {
        if(resultAry.IsAdmin[0] != undefined && resultAry.IsAdmin[0] != null){
            $("#formForwardTo").select2('val',resultAry.IsAdmin[0].Manager_Id);
            result = resultAry.IsAdmin;
        }
        else{
            result = '';
        }
    }

    if(result != '')
    {
        if (result.length == 1)
        {
            field.append('<option value="' + result[0]['Manager_Id'] + '" selected="selected">' + result[0]['Employee_Name'] + '</option>');

            field.select2( 'val', result[0]['Manager_Id']);

            field.prop('readonly', true);
        }
        else
        {
            field.append( "<option value=''>-- Select --</option>");

            for (var x in result)
            {
                field.append("<option value='" + result[x]['Manager_Id'] + "'>" + result[x]['Employee_Name'] + "</option>");
            }

            field.select2( 'val', '');
            if(resultAry.ActualManager[0] != undefined)
                field.select2('val',resultAry.ActualManager[0].Manager_Id);

            field.prop('readOnly', false);
        }
    }
    else{
        field.append( "<option value=''>-- Select --</option>");

        field.select2( 'val', '');

        field.prop('readOnly', false);
    }
}
/* load the combo box values based on the result*/
function fnLoadSelectFieldValues(selectArray) {
    var field = selectArray['field'];
    result = selectArray['result'];
    field.find('option').remove();
    field.append("<option value=''>-- Select --</option>");
    for (var x in result) {
        field.append("<option value='" + result[x]['Manager_Id'] + "'>" + result[x]['Employee_Name'] + "</option>");
    }
    field.select2('val', '');
}

/**
 *  Close Filter form If filter open state
*/
function fnFilterClose (formName) {
    if ($('#filterPanel'+ formName).hasClass('open'))
        $('#filterPanel'+ formName).removeClass('open');

    $('#filterPanel'+ formName).hide();
}

/**
 *  Scroll to top of modal
*/
function fnModalScrollTop (modalId) {
    // Check if modalId is not an empty string or null
    if (modalId) {
        $('#' + modalId).animate({ scrollTop: 0 }, 'slow');
    }
}

/**
 *  Prefill additional information values in view form
*/
function fnPreFillAdditionalPanel (formName, record) {
    $('#addedOn'+ formName).text( record.Added_On );
    $('#addedBy'+ formName).text( record.Added_By_Name );
    if (record.Updated_By_Name != null && record.Updated_On != null)
    {
        $('#modalForm'+ formName +' .updatedPanel').show();

        $('#updatedOn'+ formName).text(record.Updated_On);
        $('#updatedBy'+ formName).text(record.Updated_By_Name);
    }
    else
    {
        $('#modalForm'+ formName +' .updatedPanel').hide();
    }
}

function createHiddenTable(oTable, nTr, dataArray) {
    var aData = oTable.fnGetData(nTr);
    var sOut = '<table class="sub-table" cellpadding="5" cellspacing="0" border="0" style="padding-left:50px;">';
    //for(var x in dataArray)
    //{
    //    var dataHeader = dataArray[x].replace("_", " ");
    //
    //    sOut += '<tr><td>'+dataHeader+'</td><td>:</td><td>' + aData[dataArray[x]] + '</td></tr>';
    //}
    var dataCount = dataArray.length;

    for(var x=0 ; x<dataCount; x++)
    {
        var dataHeader = dataArray[x].replace("_", " ");
        sOut += '<tr><td>'+dataHeader+'</td><td>:</td><td>' + aData[dataArray[x]] + '</td>';
        x++;
        if(dataCount >= x)
        {
            var dataHeader = dataArray[x].replace("_", " ");
            sOut += '<td style="padding-right:50px;">'+dataHeader+'</td><td>:</td><td>' + aData[dataArray[x]] + '</td></tr>';
        }
        else
        {
            sOut +='</tr>';
        }

    }

    sOut += '</table>';

    return sOut;
}

/**
 *  Reapply select field UI due to data load getting more time
*/
function fnReapplyInputSelectionStyle() {
    $('select').each(function(){
      function format(state) {
            var state_id = state.id;
            if (!state_id)  return state.text; // optgroup
            var res = state_id.split("-");
            if (res[0] == 'image') {
                if (res[2]) return "<img class='flag' src='assets/images/flags/" + res[1].toLowerCase() + "-" + res[2].toLowerCase() +".png' style='width:27px;padding-right:10px;margin-top: -3px;'/>" + state.text;
                else return "<img class='flag' src='assets/images/flags/" + res[1].toLowerCase() + ".png' style='width:27px;padding-right:10px;margin-top: -3px;'/>" + state.text;
            }
            else {
                return state.text;
            }
        }
        $(this).select2({

            formatResult: format,
            formatSelection: format,
            placeholder: $(this).data('placeholder') ?  $(this).data('placeholder') : '',
            allowClear: $(this).data('allowclear') ? $(this).data('allowclear') : true,
            minimumInputLength: $(this).data('minimumInputLength') ? $(this).data('minimumInputLength') : -1,
            minimumResultsForSearch: $(this).data('search') ? 1 : -1,
            dropdownCssClass: $(this).data('style') ? 'form-white' : ''
        });
    });
}

function fngetSignedUrl(key, bucketName) {
    var url = '';
    $.ajax({
        type     : 'POST',
        dataType : 'json',
        async    : false,
        url      : pageUrl () +'forms-manager/form-downloads/get-aws-signed-url',
        data     : {
                 key   : key,
                 bucket : bucketName
            },
        success  : function(result)
        {
            url = result;
        },
        error: function (error) {
            if (error.status == 200) {
                sessionExpired();
                return 'sessionexpired';
            } else {
                return 'internalerror';
            }
        }
    });
    return url;
}

function fngetSignedPutUrl(key,bucketName){
    var url = '';
    $.ajax({
        type     : 'POST',
        dataType : 'json',
        async    : false,
        url      : pageUrl () +'forms-manager/form-downloads/get-aws-signed-put-url',
        data     : {
                 key   : key,
                 bucket : bucketName
            },
        success  : function(result)
        {
            url = result;
        },
        error: function (error) {
            if (error.status == 200) {
                sessionExpired();
                return 'sessionexpired';
            } else {
                return 'internalerror';
            }
        }
    });
    return url;
}

function fngetFileContent(key, formName, bucketName) {
    var response = '';
    $.ajax({
        type: 'POST',
        dataType: 'json',
        async: false,
        url: pageUrl() + 'forms-manager/form-downloads/get-file-content',
        data: {
            key,
            formName,
            bucketName
        },
        success: function (result) {
            response = result;
        },
        error: function (error) {
            if (error.status == 200) {
                sessionExpired();
                return 'sessionexpired';
            } else {
                return 'internalerror';
            }
        }
    });
    return response;
}
function fndeleteS3Document(key,bucketName){
    var response = '';
    $.ajax({
        type     : 'POST',
        dataType : 'json',
        async    : false,
        url      : pageUrl () +'forms-manager/form-downloads/delete-s3-document',
        data     : {
                key   : key,
                bucket : bucketName
            },
        success  : function(result)
        {
            response = result;
        },
        error: function (error) {
            if (error.status == 200) {
                sessionExpired();
                return 'sessionexpired';
            } else {
                return 'internalerror';
            }
        }
    });
    return response;
}

/**set the lock flag for the particular record when update/delete functions */
function setLockAPI(orgCode, formName, uniqueId, Employee_Id, errorPage, atsBaseURL, fn) {
    var setlockmutation = `
                mutation MyMutation($formName: String!,$uniqueId:Int!,$Employee_Id:Int!) {
                    setLock(formName: $formName,uniqueId:$uniqueId,Employee_Id:$Employee_Id) {
                        errorCode message
                        }
                    }
            `;
    var variables = { formName: formName, uniqueId: uniqueId, Employee_Id: Employee_Id };
    if(errorPage){
        var panel=$(errorPage)
    }
    else{
        var panel="";
    }
    $.ajax({
        method: 'POST',
        url: atsBaseURL,
        headers: getGraphqlAPIHeaders(),
        data: JSON.stringify({
            query: setlockmutation,
            variables: variables
        }),

        success: function(result) {
            if (result.data !== null) {
                return fn(0, true);
            } else {

                let errorCode = "", message = "";
                if(result && result.errors && result.errors.length > 0) {
                    errorCode = JSON.parse(result.errors[0].message).errorCode;
                    message = JSON.parse(result.errors[0].message).message;
                }
                removeMask();
                $(window).scrollTop(0);

                switch (errorCode) {
                    case 712:
                    case 714:
                    case 715:
                    case 716:
                        jAlert({
                            panel: panel,
                            msg: message,
                            type: 'warning'
                        });
                        break;
                    case 713:
                        sessionExpired();
                        break;
                    case 705:
                    case 706:
                        jAlert({
                            panel: panel,
                            msg: 'There seems to be some technical issues. Please try after some time.',
                            type: 'warning'
                        });
                        break;
                    case 717:
                    default:
                        jAlert({
                            panel: panel,
                            msg: 'Something went wrong. Please contact system administrator.',
                            type: 'warning'
                        });
                        break;
                }
            }
        },
        error: function(result) {
            $(window).scrollTop(0);
            removeMask();
            jAlert({
                panel: panel,
                msg: 'There seems to be some technical issues. Please try after sometime.',
                type: 'info'
            });
        }
    });
}

/**clear the lock flag that is set for the particular record during update/delete */
function clearLockAPI(orgCode, formName, uniqueId, Employee_Id, errorPage, atsBaseURL, fn) {
    var clearLockMutation = `
        mutation MyMutation($formName: String!,$uniqueId:Int!,$Employee_Id:Int!) {
            clearLock(formName: $formName,uniqueId:$uniqueId,Employee_Id:$Employee_Id) {
                errorCode message
                }
            }
    `;
    var variables = { formName: formName, uniqueId: uniqueId, Employee_Id: Employee_Id };
    if(errorPage){
        var panel=$(errorPage)
    }
    else{
        var panel="";
    }

    $.ajax({
        method: 'POST',
        url: atsBaseURL,
        contentType: 'application/json',
        headers: getGraphqlAPIHeaders(),
        data: JSON.stringify({
            query: clearLockMutation,
            variables: variables
        }),

        success: function(result) {
            if (result.data !== null) {
                removeMask();

                return fn(0, true);
            } else {
                $(window).scrollTop(0);
                removeMask();
                jAlert({
                    panel: panel,
                    msg: 'Something went wrong. Please contact system administrator',
                    type: 'warning'
                });
            }
        },
        error: function(result) {
            $(window).scrollTop(0);
            removeMask();
            jAlert({
                panel: panel,
                msg: 'Something went wrong. Please contact system administrator',
                type: 'warning'
            });
        }
    });
}

/** check the access rights for the form*/
function checkAccessRights(orgCode,atsBaseURL,formName,employeeId,errorPage,fn)
{
    setMask('#wholepage');
    // variables to get access rights
    var variables = {
        "formName" : formName,
        "employeeId" : employeeId
    }

    //mutation to get the employee access rights
    var mutation = `mutation
    (
        $formName: String,
        $employeeId : Int!,

    )
    { getAccessRights
        (
            formName:$formName,
            employeeId:$employeeId,

        ) { errorCode message rights {Role_View Role_Add Role_Update Role_Delete Role_Optional_Choice Role_Hr_Group Role_Payroll_Group Is_Manager Employee_Role}}}`;


     //get the access rights from the backend
     $.ajax({
        method: 'POST',
        url: atsBaseURL,
        headers: getGraphqlAPIHeaders(),
        data: JSON.stringify({
            query: mutation,
            variables : variables
        }),

        success: function(result) {
            //if data listed successfully
            if (result.data) {
                //Access rights
                var accessRights = result.data.getAccessRights.rights;
                removeMask();
                return fn(0, accessRights); // 0 means no error, accessRights contains role params
            }
            else
            {
                removeMask();
                //error occured while listing the modules
                var error = JSON.parse(result.errors[0].message);
                var errorCode = error.errorCode;
                $(window).scrollTop(0);
                switch (errorCode) {
                    //technical errors
                    case 705:
                    case 706:
                        jAlert({
                            panel: $(errorPage),
                            msg: 'There seems to be some technical issues. Please try after some time.',
                            type: 'warning'
                        });
                        break;
                        //access denied
                    case 752:
                        $(errorPage).css('display','none');
                        $('#access-denied').css('display','block');
                    break;
                    //functional errors
                    case 751:
                    default:
                        jAlert({
                            panel: $(errorPage),
                            msg: 'Something went wrong. Please contact system administrator.',
                            type: 'warning'
                        });
                        break;
                }
                return fn(errorCode);
            }
        },
        error: function(result) {
            $(window).scrollTop(0);
            removeMask();
            jAlert({
                panel: $(errorPage),
                msg: 'Something went wrong. Please contact system administrator',
                type: 'warning'
            });
            return fn("catch-error");
        }
    });


}
/**
 * function to get custom form name
 */
function getCustomFormDetail(url, formName, callback) {
    var variables = {
        formName: formName
    };
    var mutation = `mutation
        GetCustomForm($formName: String!) {
            getCustomForm(formName: $formName) {
                error { code message }
                result { customFormId customFormName isEnabled  }
            }
    }`;

    $.ajax({
        url: url,
        method: "POST",
        headers: getGraphqlAPIHeaders(),
        data: JSON.stringify({
            variables: variables,
            query: mutation,
            partnerid: partnerid,
        }),
        success: function (response) {
            if (!response.data.getCustomForm.error) {
                callback(response.data.getCustomForm.result)
            } else {
                callback()
            }
        },
        error: function (err) {
            callback(null);
        }
    });
}

/** function to get the client ip address */
function getClientIp(callback)
{
    deletePhpActionHeaders();
    //jquery function to get the client ip address
    $.getJSON(ipAddressAPI,function(e) {
        if(e.ip)
            document.cookie = "userIpAddress="+e.ip+"; path=/;secure";
        else
            document.cookie = "userIpAddress=IP Blocked by user; path=/;secure";
        callback(e.ip);
    }).fail(function(error) {
        document.cookie = "userIpAddress=IP Blocked by user; path=/;secure";
        // while getting error from ipify endpoint we can return emptry response to callback function
        callback('');
    });
    setPhpActionHeaders();
}

// Check uploaded logo file format
function fncheckFileFormat(value) {
    if ($.inArray(value, ['png', 'jpg', 'jpeg']) == -1) {
        return false;
    } else {
        return true;
    }
}


/**
 *  JQuery Ready  functions
*/
$(function() {
    // change the background of the active module when inactive modules are hovered
    $('.nav-sidebar').on('mouseenter',function(e){
        $('.hrapp-sidebar-active').css('background','transparent');
        e.stopImmediatePropagation();
    });

    $('.nav-sidebar').on('mouseleave',function(e) {
        $('.hrapp-sidebar-active').css('background','#ffffff29');
        e.stopImmediatePropagation();

    });

    // whenever we hover over a menu item that has a submenu
    $('#menu-wrapper ul').children('li').on('mouseover', function() {
        var $menuItem = $(this),
            $submenuWrapper = $('> ul', $menuItem);

        // grab the menu item's position relative to its positioned parent
        var menuItemPos = $menuItem.position();
        // place the submenu in the correct position relevant to the menu item
        $submenuWrapper.css({
        top: menuItemPos.top,
        left: menuItemPos.left + Math.round($menuItem.outerWidth())
        });

        // get the scollTop (distance scrolled from top)
        var scrollTop = $(window).scrollTop();
        // get the top offset of the dropdown (distance from top of the page)
        var topOffset = $($submenuWrapper).offset() ? $($submenuWrapper).offset().top : 0;
        // calculate the dropdown offset relative to window position
        var relativeOffset = topOffset-scrollTop;
        // get the window height
        var windowHeight = $(window).height();

        var sideHeight = $($submenuWrapper).height();

        var totalHeight = topOffset + sideHeight
        // if the relative offset is greater than half the window height,
        // reverse the dropdown.
        if(totalHeight > windowHeight && sideHeight < 315){
            $submenuWrapper.css({
                top : (menuItemPos.top - sideHeight) + ($menuItem.height() + 15),
            })

        }

    });

    var ipAddressFetchedTime;
    function getUserIpAddress(){
        deletePhpActionHeaders();
        var ipAddressRestriction = localStorage.getItem('ipAddressRestriction');
          /* Get the current network ip address */
          ipAddressFetchedTime = new Date();
          localStorage.setItem('ipAddressFetchedTime', ipAddressFetchedTime);
          $.getJSON(ipAddressAPI, function(data) {
            if(data.ip) {
                /* set the IP address in the Cookie to log the IP address in the system log */
                document.cookie = "userIpAddress="+data.ip+"; path=/;secure";
                if(ipAddressRestriction == 1) {
                    var verifiedIPAddress = localStorage.getItem('verifiedIPAddress');

                    /* if the IP address restriction is enabled and current network was changed then get the whitelisted ip address list */
                    if(verifiedIPAddress == data.ip) {
                        removeMask();
                        fnRefreshToken();
                    } else {
                        setMask('body');
                        $.ajax({
                            type     : 'POST',
                            dataType : 'json',
                            async    : false,
                            url      : pageUrl() + 'auth/index/list-whitelisted-ipaddress',
                            success  : function(result)
                            {
                                /* If the IP address restriction is disabled then remove the values from local storage.
                                so that we will not validate the ip address for future request.  */
                                if (result.IP_Address_Restriction == 0) {
                                    localStorage.removeItem('ipAddressRestriction');
                                    localStorage.removeItem('verifiedIPAddress');
                                    fnRefreshToken();
                                } else if (result.IP_Address_Restriction == 1 && result.Whitelisted_IP_Address.includes(data.ip)){
                                    /* If the current ip is whitelisted then refresh the token */
                                    localStorage.setItem('verifiedIPAddress',data.ip);
                                    fnRefreshToken();
                                } else {
                                    /* If the current ip is not whitelisted present the error page to the user. */
                                    $('body').addClass('modal-open');
                                    $('#ipAddressWhitelistingWarningModal').show();
                                }
                                removeMask();
                            }, error  : function (error){
                                removeMask();
                                if(error.status == 200){
                                    sessionExpired();
                                }else{
                                    /* To handle internal server error */
                                    jAlert({ msg : "Something went wrong. Please contact system admin", type : "warning" });
                                }
                            }
                        });
                    }
                } else {
                    fnRefreshToken();
                }
            } else {
                document.cookie = "userIpAddress=IP Blocked by user; path=/;secure";
                /* If user restricted the app to fetch the ip address then present the error page */
                if(ipAddressRestriction == 1) {
                    $('body').addClass('modal-open');
                    $('#ipAddressWhitelistingWarningModal').show();
                }
            }
        });
        // }).error(function(error) {
        //     /* will remove it after testing */
        //     /* If the IP address API is not available, API URL is wrong or internet connection is not available,
        //     then the error.readyState will be 0 or 4  */
        //     if(error.readyState === 0 || error.readyState === 4) {
        //         if(ipAddressRestriction == 1) {
        //             jAlert({ msg : "Unable to get the IP address. Please contact system Administrator.", type : "warning" });
        //         }
        //     } else {
        //         document.cookie = "userIpAddress=IP Blocked by user; path=/;secure";
        //         /* If user restricted the app to fetch the ip address then present the error page */
        //         if(ipAddressRestriction == 1) {
        //             $('body').addClass('modal-open');
        //             $('#ipAddressWhitelistingWarningModal').show();
        //         }
        //     }
        // });
        setPhpActionHeaders();
    }

    function getIpAddress(){
        var currentTime = new Date();
        var storedIpAddressFetchedTime = new Date(localStorage.getItem('ipAddressFetchedTime'));
        const timeDifference = Math.abs(currentTime.getTime() - storedIpAddressFetchedTime.getTime());
        const timeDifferenceInMinutes = timeDifference / (1000 * 60);
        if (timeDifferenceInMinutes > 10) {
            getUserIpAddress();
        }
    }
    /* Before each ajax call we should confirm that the user is using the application from whitelisted ip or not */
    /* Also, The Firebase id token will get expired in 1hr. So before sending the ajax request generate new id token
     if the token has generated 30mins before*/


    $.ajaxSetup({
        beforeSend: function(xhr, settings) {


            /* we are passing orgcode in all ajax calls request header. So header set action is restricted for
            the get-org-code endpoint because we can't send orgcode as header to avoid recursive api calls.*/
            if(!settings.url.includes("get-org-code")){
                /* Do not get the ip address if the ajax url is ipAddressAPI or it contains list-whitelisted-ipaddress.
                We have already checked in the auth page, so do not check.*/
                if(settings.url !== ipAddressAPI && !settings.url.includes("list-whitelisted-ipaddress") && !window.location.href.includes('/auth')){
                    // Common ajax headers are not accepted in "ipify" API call. So it is removed before making a call.
                    deletePhpActionHeaders();
                    getIpAddress();
                    setPhpActionHeaders();
                } else {
                    fnRefreshToken();
                }
            }
        },
        headers: getPhpActionHeaders()
    });

    //   // Call the function initially
      getUserIpAddress();
    //   // Call the function every 10 minutes (600,000 milliseconds)
      var interval = setInterval(getUserIpAddress, 600000);

    $('#refreshIpAddress').on('click',function() {
        $('body').removeClass('modal-open');
        location.reload();
    });

    /* The Firebase id token will get expired in 1hr.
    This Function is used to generate new id token if the token has been generated 30mins before the current time. */
    function fnRefreshToken(){
        try{
            if(partnerid === 'entomo'){
                return 'success';
            }else{
                var user = firebase.auth().currentUser;
                if(user){
                    var accessToken = user.ra;
                    /** Decode access token to get token expiry time */
                    var splitAccessToken = accessToken.split('.')[1];
                    var tokenURI = splitAccessToken.replace(/-/g, '+').replace(/_/g, '/');
                    var decodedToken = JSON.parse(decodeURIComponent(atob(tokenURI).split('').map(function(c) {
                        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                    }).join('')));

                    /* Get token expiry time from decoded token */
                    var tokenExpiryTime = decodedToken.exp;
                    /* Difference between expiry time of the token and current time */
                    var difference = parseInt(tokenExpiryTime,10) - (new Date() / 1000);
                    var differenceInMinutes = Math.floor(difference/60);

                    /* Generate new token if the token is going to expiry in one minute or already expired */
                    if(differenceInMinutes <= 1){
                        user.getIdToken(true).then(function(result){
                            /** The access token will gets expired in 1hr.
                            * If the token exists in the cookie then the new token will take some time to set
                            * so we can set the expiry time for the accesstoken as 59mins*/
                            var accessTokenExpiryTime = new Date();
                            accessTokenExpiryTime.setTime(accessTokenExpiryTime.getTime() + 3540 * 1000); //59mins = 3540 secs
                            $.removeCookie('accessToken',{path:'/'});
                            document.cookie = "accessToken="+user.ra+"; expires=" + accessTokenExpiryTime.toUTCString() + "; path=/;secure";
                        });
                    }
                }
            }
        } catch(error){
        }
    }

    $('input[type=text]').prop('autocomplete', 'off');
    if ($('body').data('page') != undefined)
    {
        if ($('body').data('page') =='employees'||$('body').data('page') =='timesheets'||$('body').data('page') =='leaves'||$('body').data('page') =='holidays'||$('body').data('page') =='allowances'||$('body').data('page') =='bonus')
        {
            var jump=function(e)
            {
                if (e)
                {
                    e.preventDefault();
                    var target = $(this).attr("href");
                }
                else
                {
                    var target = location.hash;
                }

                if(target != "#" && target !='' && target != null)
                {
                    $('html,body').animate(
                    {
                        scrollTop: $(target).offset().top
                    },function()
                    {
                        location.hash = target;
                    });
                }

               $('.table').parent().removeClass('force-table-responsive');
            }

             $('html, body').hide();

            //  $(window).on("load", function()
            //  {
            //     $('.table').parent().removeClass('force-table-responsive');
            //     //   $('a[href^=#]').on("click", jump);
            //          $('a[href^="#"]').on("click", jump);


            //        if (location.hash)
            //         {
            //           setTimeout(function()
            //           {
            //             $('html, body').scrollTop(0).show();
            //             jump();
            //           }, 0);
            //         }
            //         else
            //         {
            //           $('html, body').show();
            //         }
            // });

            if (document.readyState === 'complete') {
                // If the document has already loaded, execute the code directly
                handleLoad();
            } else {
                // If the document is still loading, attach the 'load' event handler
                $(window).on('load', handleLoad);
            }
        }
    }

    function handleLoad() {
        $('.table').parent().removeClass('force-table-responsive');
        $('a[href^="#"]').on("click", jump);

        if (location.hash) {
            setTimeout(function() {
                $('html, body').scrollTop(0).show();
                jump();
            }, 0);
        } else {
            $('html, body').show();
        }
    }



    //$.fn.dataTable.ext.errMode = 'none';

    localStorage.setItem('geoLocationAttendance', 0);

    var $input = $('#backbuttonrefresh');

    $input.val() == 'yes' ? location.reload(true) : $input.val('yes');

    $(document).not('#login-block').on("click mousedown mouseup focus blur keydown change", function()
    {
        $(document).trigger('ajaxStart');
    });




    $(window).on('beforeunload',function(){
        $.ajax({
                type     : 'POST',
                dataType : 'json',
                url      : pageUrl() + 'default/index/clear-user-session',
                success  : function()
                {}
            });
    });


    if ($('body').data('page') != undefined)
    {
        if ($('body').data('page') != 'login')
        {
            $('.logout').on('click',function()
            {
                setMask('body');
                $.ajax({
                    type     : 'POST',
                    dataType : 'json',
                    url      : pageUrl() + "auth/index/clear-session-throw-lock",
                    success  : function(result)
                    {
                        let entomoLogoutQuery = `query logoutFromEntomo($xAuthToken: String!){ logoutFromEntomo(xAuthToken: $xAuthToken){ errorCode message } }`;

                        if($.cookie('partnerid') == 'entomo'){
                            //Call an endpoint to logout user from entomoe
                            $.ajax({
                            method: 'POST',
                            url: getApiCustomDomains('atssignin'),
                            headers: getGraphqlAPIHeaders(),
                            data: JSON.stringify({
                                query: entomoLogoutQuery,
                                variables:{
                                    xAuthToken:$.cookie('accessToken') ? $.cookie('accessToken') : null,
                                }
                            }),
                            success  : function() { },
                            error  : function (){ }
                            });
                        }else{
                            firebase.auth().signOut();
                            $.removeCookie('partnerid',{path:'/'});
                        }
                        localStorage.setItem('showLeaveClosurePopup','show');

                        /* Remove the ip address restrcition values */
                        localStorage.removeItem('ipAddressRestriction');
                        localStorage.removeItem('verifiedIPAddress');
                        localStorage.removeItem('ipAddressAPI');

                        $.removeCookie('empUid',{path:'/'});
                        $.removeCookie('accessToken',{path:'/'});
                        $.removeCookie('refreshToken',{path:'/'});
                        $.removeCookie('additional_headers',{path:'/'});
                        $.removeCookie('userIpAddress',{path:'/'});
                        deletePhpActionHeaders();

                        /* remove paycycle start date, paycycle end date and payslip employee ids set for the attendance finalization  */
                        localStorage.removeItem('paycycleStartDate');
                        localStorage.removeItem('paycycleEndDate');
                        localStorage.removeItem('payslipEmployeeIds');

                        document.cookie = "showAuth=0; path=/;secure";
                        window.location.href = pageUrl()+"auth/index/logout";

                        if (fnIsProduction ())
                        {
                            if($.cookie("Appmailboxidentity") != null && $.cookie("Appmailboxidentity") != undefined)
                                $.removeCookie('Appmailboxidentity',{path:'/'});
                        }

                        /* clear the watchposition if exists */
                        if(watchGeoPositionId)
                            navigator.geolocation.clearWatch(watchGeoPositionId);

                    }, error  : function (errorResult){
                        removeMask('.custom-page');

                        /* clear the watchposition if exists */
                        if(watchGeoPositionId)
                            navigator.geolocation.clearWatch(watchGeoPositionId);

                        if($.cookie('partnerid') !== 'entomo'){
                            firebase.auth().signOut();
                            $.removeCookie('partnerid',{path:'/'});
                        }
                        deletePhpActionHeaders();
                        $.removeCookie('empUid',{path:'/'});
                        $.removeCookie('accessToken',{path:'/'});
                        $.removeCookie('refreshToken',{path:'/'});
                        $.removeCookie('additional_headers',{path:'/'});

                        document.cookie = "showAuth=0; path=/;secure";
                        window.location.href = pageUrl()+"auth/index/logout";

                        if (fnIsProduction ())
                        {
                            if($.cookie("Appmailboxidentity") != null && $.cookie("Appmailboxidentity") != undefined)
                                $.removeCookie('Appmailboxidentity',{path:'/'});
                        }
                    }
                });

            });



            $('.profile').prop('href', pageUrl() + 'v3/employee-self-service/my-profile');

            $('.morphsearch-input').on('keyup', function () {
                menuSearch ($(this));
            });

            $('.morphsearch-submit').on('click', function () {
                menuSearch ($(this));
            });

            $('.toggle_fullscreen').on('click', function () {
                var fullScreen = $(this).children('i');

                $('.page-content').css("background-color","");

                if (fullScreen.hasClass('icon-size-fullscreen'))
                {
                    fullScreen.addClass('icon-size-actual').removeClass('icon-size-fullscreen');
                }
                else if (fullScreen.hasClass('icon-size-actual'))
                {
                    fullScreen.addClass('icon-size-fullscreen').removeClass('icon-size-actual');
                }
            });

            $('.morphsearch').prop('style', 'display:none;');

            $('.searchform, .morphsearch-close').on('click', function () {
                $('.morphsearch').prop('style', 'display:block;');

                $('.morphsearch-content').removeAttr('style');
                $('.morphsearch-content').prop('style', 'height:auto;');
            });

            $('.morphsearch').on('hide', function () {
                $('.morphsearch').prop('style', 'display:block;');
            });

            $('.mdi-action-open-in-browser').addClass('icons-office-58').removeClass('mdi-action-open-in-browser');

            // set the submenu hover as 1
            createSubmenuHover()

        }

        switch ($('body').data('page'))
        {
            case 'login':
                $('#userName, #userPassword').on('focus', function (){
                    $('#error-panel').hide();
                });
                break;

            case 'index':
                //Call the starttime function only from the dashboard
                startTime();

                $('.hrsubmenu_img').prop('style', 'display:none;');

                $('.registered-employees').prop('href', pageUrl() + "employees/employees");

                $('.presence-today').prop('href', pageUrl() + "employees/attendance/attendance-box");

                timer = new _timer(function(time) {
                            if (time == 0)
                            {
                                timer.stop();
                            }
                        });
                timer.reset(0);
                timer.mode(1);

                if (localStorage.changeLoginPwdDisplay == 1)
                {
                    localStorage.changeLoginPwdDisplay = 0;
                    $('#menuChangePassword').trigger('click');
                }

                checkAttendance ();

                /**
                 *  1 hour   = 60 Minutes
                 *  1 Minute = 60 Seconds
                 *  1 Second = 1000 Milli Seconds
                 *
                 *  5 Minute = 5 * 60 * 1,000 = 3,00,000 milli seconds
                */

                setTimeout(function(){
                    checkAttendance ();
                }, 300000);

                /**
                 *  Set Current Day and Complete Date in Attendance Box
                */
                var d = new Date(tzDate());
                var weekday = new Array(7);
                weekday[0]=  "Sunday";
                weekday[1] = "Monday";
                weekday[2] = "Tuesday";
                weekday[3] = "Wednesday";
                weekday[4] = "Thursday";
                weekday[5] = "Friday";
                weekday[6] = "Saturday";

                var monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October",
                                  "November", "December"];

                var datePanelText = weekday[d.getDay()] + ' - ' + d.getDate() + ' ' + monthNames[d.getMonth()] + ' ' + d.getFullYear();


                var offset = new Date(tzDate()).getTimezoneOffset();

                offset = ((offset<0? '+':'-')+ // Note the reversed sign!
                          pad(parseInt(Math.abs(offset/60)), 2)+ ':'+
                          pad(Math.abs(offset%60), 2));

                //$('.time-zone').empty();
                //$('.time-zone').html('IST (GMT'+offset+')');

                $('#locationRefreshButton').on('click', function(){
                    $('#geoLocationModel').modal('hide');
                });

                function checkworkPlaceEnabled() {
                    setMask('.custom-page');
                    $.ajax({
                        type     : 'POST',
                        dataType : 'json',
                        url      : pageUrl() + "employees/attendance/is-enable-work-place",
                        success  : function(result)
                        {
                            if(result == 1 ){
                                removeMask('.custom-page');
                                $('#workPlaceModel').modal('show');
                            } else {
                                fnUpdateAttendance(null);
                            }
                        }, error  : function (errorResult){
                            removeMask('.custom-page');
                            if(errorResult.status == 200){
                                sessionExpired();
                            }else{
                                /* To handle internal server error */
                                jAlert({ msg : 'Something went wrong. Please contact system admin.', type : 'warning' });
                            }
                        }
                    });
                }


                $('.att-in-out, .att-in-out-text').on('click', function () {
                    /* get the geo coordinates only if the geo location is enforced for the employee */
                    if(isGeoLocationEnforced){
                        /* the geo coordinates is enforeced then the user should use the mobile application,
                        otherwise we need to get confirmation from the user that the location will not be accurate and the user wants to proceed check-in/checkout */
                        if(findDevice() == 'Mobile Application') {
                            /* if the geo coordinates is enforced then get the co ordinates */
                            getLocation(function(res){
                                checkworkPlaceEnabled(res);
                            });
                        } else {
                            // show confirmation modal
                            var deviceType = findDevice() == 'Web' ? 'web'  : 'mobile';

                            $('#geoCoordinatesAlertContent').html('The geo coordinate might not be accurate in '+
                                deviceType+' browsers as the data might be misrepresented due to the Wifi network, '+
                                'proxy servers or VPN. If you still want to proceed please progress to next step. '+
                                'It is advised that you use mobile app for capturing accurate geo coordinates.');

                            $('#geoCoordinateAccuracyWarningModal').modal('toggle');
                        }
                    } else {
                        /* if the geo coordinates does not enforced then do not get the co ordinates */
                        latitude = '';
                        longitude = '';
                        var result = {
                            latitude: '',
                            longitude: ''
                        }
                        checkworkPlaceEnabled(result);
                    }
                });

                /* if the user proceeds to get the geo cordinates get them */
                $('#proceedGeoAccuracyBtn').on('click', function () {
                    $('#geoCoordinateAccuracyWarningModal').modal('toggle');
                    getLocation(function(res){
                        checkworkPlaceEnabled(res);
                    });
                });

                $('.view-att-entries').on('click', function () {
                    window.location.href = pageUrl() + 'employees/attendance';
                });

                $('.list-reporting-tailes-box').on('change', function () {
                    if ($('.list-reporting-tailes-box:checked').val() == 'on')
                    {
                        $('.list-reporting-tailes').addClass('active');
                        $('.list-reporting-box').removeClass('active');

                        $('.widget-infobox-reporting-tails').css('display', 'block');

                        $('.widget-infobox-reporting-box').css('display', 'none');
                    }
                    else
                    {
                        $('.list-reporting-box').addClass('active');
                        $('.list-reporting-tailes').removeClass('active');

                        $('.widget-infobox-reporting-tails').css('display', 'none');

                        $('.widget-infobox-reporting-box').css('display', 'block');
                    }
                });

                //Employee Statistics Chart
                if(fnIsProduction())
                {
                    if(((window.location.href).split('/'))[3] != 'mailbox' && ((window.location.href).split('/'))[3] != 'datasetup-dashboard')
                        getstatisticFunc();
                }
                else
                {
                    if(((window.location.href).split('/'))[4] != 'mailbox' && ((window.location.href).split('/'))[4] != 'datasetup-dashboard')
                        getstatisticFunc();
                }

                $.fn.dataTable.ext.errMode = 'none';

                var tableLeaveSummaryPanel = $('#tableDashboardLeaveSummaryPanel').dataTable({
                    "bDestroy"      : true,
                    "bPaginate"     : false,
                    "bLengthChange" : false,
                    "bFilter"       : false,
                    "bAutoWidth"    : false,
                    "paging"        : false,
                    "bInfo"         : false,
                    "aoColumnDefs"  : [{ 'bSortable': false, 'aTargets': ['_all'] },
                                       { "sClass" : "text-center", "aTargets" : [1, 2, 3] }],
                    "aaSorting"     : [],
                    "sServerMethod" : "POST",
                    "sAjaxSource"   : pageUrl () + 'employees/leaves/list-leave-history/',
                    "sAjaxDataProp" : "aaData",
                    "language"      : {"emptyTable": "No leave record found" },
                    "aoColumns"     : [{
                        "mData" : function (row, type, set)
                        {
                            return '<div style="word-break: break-all;">'+ row['Leave_Name'] +'</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            if(row['Elig_Days'] != null || row['Elig_Days'] != '')
                            {
                                if (row['No_Of_Days'] != null && row['No_Of_Days'] != '' && row['No_Of_Days'] > 0 ){
                                    eliglibleDays = parseFloat(row['Elig_Days']) + parseFloat(row['No_Of_Days']);
                                }
                                else{
                                    eliglibleDays = row['Elig_Days'];
                                }

                                entitled = parseFloat(Math.round(eliglibleDays * 100) / 100).toFixed(1);
                            }
                            else
                            {
                                entitled = "-";
                            }

                            return '<div class="text-center">'+ entitled +'</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            if(row['Leaves_Taken'] != null || row['Leaves_Taken'] != '')
                            {
                                leaveTaken = parseFloat(Math.round(row['Leaves_Taken'] * 100) / 100).toFixed(1);
                            }
                            else
                            {
                                leaveTaken = "-";
                            }

                            return '<div class="text-center">'+ leaveTaken +'</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            if(row['Leave_Balance'] != null || row['Leave_Balance'] != '')
                            {
                                leaveBalance = parseFloat(Math.round(row['Leave_Balance'] * 100) / 100).toFixed(1);
                            }
                            else
                            {
                                leaveBalance = "-";
                            }

                            return '<div class="text-center">'+ leaveBalance +'</div>';
                        }
                    }]
                });


                 $('#tableDashboardLeaveSummaryPanel').parent().removeClass('force-table-responsive');

                 var tableUpcomingHolidaysPanel = $('#tableDashboardUpcomingHolidaysPanel').dataTable({
                    "bDestroy"      : true,
                    "bPaginate"     : false,
                    "bLengthChange" : false,
                    "bFilter"       : false,
                    "bAutoWidth"    : false,
                    "paging"        : false,
                    "bInfo"         : false,
                    "aoColumnDefs"  : [{ 'bSortable': false, 'aTargets': ['_all'] }],
                    "aaSorting"     : [],
                    "sServerMethod" : "POST",
                    "sAjaxSource"   : pageUrl () + 'organization/holidays/list-dashboard-holidays/',
                    "sAjaxDataProp" : "aaData",
                    "language"      : {"emptyTable": "No Holiday record found" },
                    "aoColumns"     : [{
                        "mData" : function (row, type, set)
                        {
                            return '<div >'+ row['Holiday_Name'] +'</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set)
                        {
                            return '<div >'+ row['HolidayDate'] +'</div>';
                        }
                    }]
                });
                $('#tableDashboardUpcomingHolidaysPanel').parent().removeClass('force-table-responsive');

                var tableTasksPanel = $('#tableDashboardTasksPanel').dataTable({
                    "bDestroy"      : true,
                    "bPaginate"     : false,
                    "bLengthChange" : false,
                    "bFilter"       : false,
                    "bAutoWidth"    : false,
                    "paging"        : false,
                    "bInfo"         : false,
                    "aoColumnDefs"  : [{ 'bSortable': false, 'aTargets': ['_all'] }],
                    "aaSorting"     : [],
                    "sServerMethod" : "POST",
                    "sAjaxSource"   : pageUrl () + 'employees/assignments/list-dashboard-assignment',
                    "sAjaxDataProp" : "aaData",
                    "language"      : {"emptyTable": "No Task record found" },
                    "aoColumns"     : [{
                        "mData" : function (row, type, set)
                        {
                            return '<div >'+ row['Task_Name'] +'</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set)
                        {
                            return '<div >'+ row['Due_Date'] +'</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set)
                        {
                            return '<div >'+ row['Approval_Status'] +'</div>';
                        }
                    }]
                });
                $('#tableDashboardTasksPanel').parent().removeClass('force-table-responsive');

                 $(document).on('click', '#sessionExpiredConfirm', function () {
                    top.location.href = pageUrl() + "auth";
                });

                break;

            default:
                //$('.hrsubmenu_img').prop('style', 'display:block;');
                $('.hrsubmenu_img').prop('style', 'display:inline-table;');

                /*  Add event listener for select and unselect details  */
                $(document).on('click', '#sessionExpiredConfirm', function () {
                    top.location.href = pageUrl() + "auth";
                });


                /**
                 *  make number field values are decimal in field change
                 */
                $('.decimalField').on('change', function () {
                    $(this).val( parseFloat($(this).val()).toFixed(2) );
                });

                /**
                 *  Check validation for change seelct field
                */
                $('select.form-control, input[type=text], input[type=number], textarea').on('change blur', function () {
                    if ($(this).parents('form.form-validation').length)
                    {
                        $(this).valid();
                    }
                });



                /**
                 *  Validations by class name for description
                */
                // validation regx,
                $.validator.addMethod("regx", function(value, element, regexp) {
                    return new RegExp(regexp).test(value);
                }, "Please enter a valid input.");

                // validation for onlyLetter and Space,
                $.validator.addMethod("onlyLetterSp", function (value) {
                    if (value != '')
                        return /^[a-zA-Z\ ]+$/.test(value);


                    return true;
                }, "Only alphabets and spaces are allowed.");

                /**
                *  Validation for username
               */
                function validateUserName(username) {
                    var email = /^[\w-\.]+@([\w-]+\.)+[\w-]{2,24}$/;
                    var alphanum = /^[\w]*$/;

                    if (email.test(username) == true) {
                        return true;
                    }
                    else if (alphanum.test(username) == true) {
                        return true;
                    }
                    else {
                        return false;
                    }
                }


                $.validator.addMethod('validUserName', function (value) {
                    if (value != '')
                        return validateUserName(value);

                    return true;
                }, 'User name should be email address or alphanumeric characters.');

                // validation for allowance name reserved keywords,
                $.validator.addMethod("allowanceName", function (value) {
                    // Retrieve the serialized array from the hidden input
                    var serializedArray = $('#reserveKeyWordAllowance').val();

                    // Deserialize the array
                    var reserveArr = JSON.parse(serializedArray);
                    var allowanceWords=[];
                    for(var i=0; i<reserveArr.length; i++)
                    {
                        allowanceWords.push(reserveArr[i].toLowerCase());
                    }
                    valLow = value.toLowerCase();

                    if ($.inArray(valLow,allowanceWords) != -1)
                        return false;
                    else
                        return true;
                    //return true;
                }, "Please note that the input you've provided is either a reserved keyword or a title already in use within adhoc allowances, deductions, fixed health insurance, or insurance forms. Kindly choose a unique value.");

                  // validation for adhoc allowance name reserved keywords,
                  $.validator.addMethod("adhocAllowanceName", function (value) {
                    // Retrieve the serialized array from the hidden input
                    var serializedArray = $('#reserveKeyWordAdhocAllowance').val();

                    // Deserialize the array
                    var reserveArr = JSON.parse(serializedArray);
                    var words=[];
                    for(var i=0; i<reserveArr.length; i++)
                    {
                        words.push(reserveArr[i].toLowerCase());
                    }
                    valLow= value.toLowerCase();

                    if ($.inArray(valLow,words) != -1)
                        return false;
                    else
                        return true;
                    //return true;
                }, "Your entry appears to be either a reserved keyword or a title currently in use in the allowance, deduction, fixed health insurance, or insurance forms. Please provide a different, unique value.");



                $.validator.addMethod("deductionTitle", function (value) {
                // Retrieve the serialized array from the hidden input
                var serializedArray = $('#reserveKeyWords').val();

                // Deserialize the array
                var reserveArr = JSON.parse(serializedArray);

                  var words=[];
                  for(var i=0; i<reserveArr.length; i++)
                  {
                      words.push(reserveArr[i].toLowerCase());
                  }
                  valLow= value.toLowerCase();

                  if ($.inArray(valLow,words) != -1)
                      return false;
                  else
                      return true;
                  //return true;
                }, "The input provided is identified as a reserved keyword or it is already being used as a title within adhoc allowances, allowances, fixed health insurance, or insurance forms. We request you to enter a unique value.");



                // validation for Only numbers, comma and spaces,
                $.validator.addMethod("numSpComma", function (value) {
                    if (value != '')
                        return /^[0-9\ \,]+$/.test(value);

                    return true;
                }, "Only numbers, comma and spaces are allowed");


                $.validator.addMethod("onlyLetterSpSlash", function (value) {
                    if (value != '')
                        return /^[\a-zA-Z\/\ ]+$/.test(value);

                    return true;
                }, "Only alphabets, slash(/) and spaces are allowed.");

                // validation for alphanumeric,+, comma, /, dot, #, &, ` hypen and spaces,
                $.validator.addMethod("alphaNumSpCDotHySlash", function (value) {
                    if (value != '')
                        return /^[a-zA-Z0-9\|\\_\\.\,\#\+\&\/\-\(\)\:\`\'\ ]*$/.test(value);

                    return true;
                }, "Only alphanumeric, spaces and symbols(+ , / . # & : () ' - ` | _) are allowed.");

                // Validation for alphanumeric, +, comma, /, dot, #, &, `, ?, hyphen, spaces, and new line character
                $.validator.addMethod("commonInputAlphaValidation", function (value) {
                    if (value !== '')
                        return /^[\p{L}\p{M}\s.,#\+&\/\-():'ñd'!@#$%^&*_±=|\\;?"]+$/u.test(value);
                    return true;
                }, "Only alphanumeric, spaces, and symbols (+, /, ., #, &, :, (), ', -, _, `, ?) are allowed.");

                // Validation for alphanumeric, +, comma, /, dot, #, &, `, ?, hyphen, spaces, and new line character
                $.validator.addMethod("commonInputAlphaNumericValidation", function (value) {
                    if (value !== '')
                        return /^[\p{L}\p{M}\p{Nd}\s.,#\+&\/\-():'ñd'!@#$%^&*_±=|\\;?"]+$/u.test(value);
                    return true;
                }, "Only alphanumeric, spaces, and symbols (+, /, ., #, &, :, (), ', -, _, `, ?) are allowed.");


                // validation for alphanumeric,+, comma, /, dot, #, &, ` ? hypen,spaces and new line character,
                $.validator.addMethod("alphaNumSpCDotHySlashNewLine", function (value) {
                    if (value != '')
                        return /^[\w\.\,\#\+\&\/\-\(\)\:\'\`\?\n\"; ]*$/.test(value);
                    return true;
                }, "Only alphanumeric, spaces and symbols(+ , / . # & : () ' - _ ` ? ;) are allowed.");

                 // validation for alphanumeric, comma, /, dot, hypen and spaces,
                $.validator.addMethod("aadharNo", function (value) {
                    if (value != '')
                        return /^[\w\.\,\/\-\(\)\:\ ]*$/.test(value);

                    return true;
                }, "Only alphanumeric, spaces and symbols(, / . : () - ) are allowed.");

                // validation for alphaNumSpCDotHy
                $.validator.addMethod("alphaNumSpCDotHy", function (value) {
                    if (value != '')
                        return /^[\w\.\,\-\ ]*$/.test(value);

                    return true;
                }, "Only alphanumeric, spaces and symbols(, . - ) are allowed.");

                // validation for Only alphanumeric, hypens and spaces,
                $.validator.addMethod("alphaNumSpHy", function (value) {
                    if (value != '')
                        return /^[\w\-\ ]*$/.test(value);

                    return true;
                }, "Only alphanumeric, spaces and symbol(-)  are allowed");

                // validation for Only alphaSpNlComma, hypens and spaces,
                $.validator.addMethod("alphaSpNlComma", function (value) {
                    if (value != '')
                        return /^[a-zA-Z\ \,\n\r]+$/.test(value);

                    return true;
                }, "Only alphabets, spaces and symbol(,)  are allowed.");

                // validation for Phone Number,
                $.validator.addMethod("vPhoneNum", function (value) {
                    if (value != '')
                        //     /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/
                        //return /([0-9]{10})|(\([0-9]{3}\)\s+[0-9]{3}\-[0-9]{4})/.test(value)

                        return /^([\+][0-9]{1,3}[ \.\-])?([\(]{1}[0-9]{2,6}[\)])?([0-9 \+\.\-\/]{5,15})((x|ext|extension)[ ]?[0-9]{1,4})?$/.test(value);

                    return true;
                }, "Provide a valid phone number.");

                // validation for alpha or alphanumeric,
                $.validator.addMethod("alphaORalphanum", function (value) {
                    if (value != '')
                        return /^\d*[a-zA-Z][a-zA-Z\d ]*$/.test(value);

                    return true;
                }, "Only alphabets or alphanumeric are allowed.");

                // validation for alphaNumSp,
                $.validator.addMethod("alphaNumSp", function (value) {
                    if (value != '')
                        return /^[\w\ ]*$/.test(value);

                    return true;
                }, "Only alphanumeric and spaces are allowed.");

                // validation for alphaNum,
                $.validator.addMethod("alphaNum", function (value) {
                    if (value != '')
                        return /^[\w]*$/.test(value);

                    return true;
                }, "Only alphanumeric characters are allowed.");

                // validation for alphaNumSpComma,
                $.validator.addMethod("alphaNumSpComma", function (value) {
                    if (value != '')
                        return /^[\w\,\ ]*$/.test(value);

                    return true;
                }, "Only alphanumeric, spaces and symbol(,)  are allowed.");

                // validation for onlyLetterNumber,
                $.validator.addMethod("onlyLetterNumber", function (value) {
                    if (value != '')
                        return /^[0-9a-zA-Z ]+$/.test(value);

                    return true;
                }, "Only alphanumeric characters are allowed.");

                // validation for alphaNumSpDot,
                $.validator.addMethod("alphaNumSpDot", function (value) {
                    if (value != '')
                        return /^[\w\.\ ]*$/.test(value);

                    return true;
                }, "Only alphanumeric, dot and spaces are allowed.");

                // validation for text area field to allow alphabets, dots and space with multiple lines
                $.validator.addMethod('textAreaValidation', function (value) {
                    if (value != '')
                        return /^[\w.\s]+$/.test(value);

                    return true;
                }, "Only alphanumeric, dot and spaces are allowed.");

                // validation for alphaNumHypSlash
                $.validator.addMethod("alphaNumHypSlash", function (value) {
                    if (value != '')
                        return /^[\w\-\/\ ]*$/.test(value);

                    return true;
                }, "Only alphanumeric and symbols(/ -)  are allowed.");

                // validation for alphaNumHypSlashHash
                $.validator.addMethod("alphaNumHypSlashHash", function (value) {
                    if (value != '')
                        return /^[\w\-\/\#\ ]*$/.test(value);

                    return true;
                }, "Only alphanumeric and symbols(# - /)  are allowed.");

                $.validator.addMethod('vProjectName', function (value) {
                    if (value != '')
                        return /^[\w\#\.\&\,\-\ ]*$/.test(value);

                    return true;
                }, 'Only alphanumeric, spaces and symbols(# & - , .)  are allowed.');

                $.validator.addMethod('vAccept', $.validator.methods.accept, 'Please upload a valid file.');

                $.validator.addMethod('numberRequired', function (value) {
                    return value > 0.00;
                }, 'This field is required');

                // validation for onlyNumbers
                $.validator.addMethod("onlyNumbers", function (value) {
                    if (value != '')
                        return /^\d+$/.test(value);
                    return true;
                }, "Only whole numbers are allowed.");

                // validation for onlynumber,
                $.validator.addMethod("onlyNum", function (value) {
                    if (value != '')
                        return /^[0-9]+$/.test(value);

                    return true;
                }, "Only numerics are allowed.");

                // validation for alphaNumHypBrack
                $.validator.addMethod("alphaNumHypBrack", function (value) {
                    if (value != '')
                        return /^[\w\-\(\) ]*$/.test(value);

                    return true;
                }, "Only alphanumeric, spaces and symbols( - () ) are allowed.");

                //Validation for email
                $.validator.addMethod('vEmail', function (value) {
                    if (value != '')
                        return validateEmail(value);

                    return true;
                }, 'Please provide a valid email address.');

                $.validator.addMethod('vStreet', function (value) {
                    if (value != '')
                        return /^[\w\&\.\-\,\(\)\/\ ]*$/.test(value);

                    return true;

                }, 'Only alphanumeric, spaces and symbols(, / . & () - ) are allowed.');


                // validation for only alphanumeric,hypen,& and space
                $.validator.addMethod("onlyAlnumHypAndSp", function (value) {
                    if (value != '')
                        return /^[\w\-\&\ ]*$/.test(value);

                    return true;
                }, "Only alphanumeric, spaces and symbols(- &)  are allowed");

                /** city validation **/
                $.validator.addMethod("onlyLetSpBrHyComma", function (value) {
                    if (value != '')
                        return /^[\w\-\(\)\,\ ]*$/.test(value);

                    return true;
                }, "Only alphanumeric, spaces and symbols(- , () )  are allowed");

                /** state validation **/
                $.validator.addMethod("onlyLetSpBrComma", function (value) {
                    if (value != '')
                        return /^[\w\(\)\,\ ]*$/.test(value);

                    return true;
                }, "Only alphanumeric, spaces and symbols(, () )  are allowed.");

                // validation for onlyLetter, Space, dot
                $.validator.addMethod("onlyLetterSpDot", function (value) {
                    if (value != '')
                        return /^[a-zA-Z\.\ ]+$/.test(value);

                    return true;
                }, "Only alphabets, spaces and symbol(.)  are allowed.");


                $.validator.addMethod("employeeName", function (value) {
                    if (value != '') {
                        return /^[a-zA-Z.\s_-]+$/.test(value);
                    }
                    return true;
                }, "Only alphabets, spaces, periods, underscores, and hyphens are allowed.");

                // validation for only alphanumeric, comma, space, dot, ampersand, brackets
                $.validator.addMethod("onlyLetterSpDotAmpBracket", function (value) {
                    if (value != '')
                        return /^[\w\.\&\,\(\)\ ]+$/.test(value);
                    return true;
                }, "Only alphanumeric, spaces and symbols(& , () . )  are allowed.");

                // validation for only letter, space, numbers, /, +, ., #, hypen
                $.validator.addMethod('vCertification', function (value) {
                    if (value != '')
                        return /^[\w\.\+\#\-\/\ ]*$/.test(value);

                    return true;

                }, 'Only alphanumeric, spaces and symbols(- . + / # )  are allowed.');

                // validation for only letter, space, numbers, /, +, ., #, &, hypen
                $.validator.addMethod('documentTitle', function (value) {
                    if (value != '')
                        return /^[\w\.\&\+\#\-\/\ ]*$/.test(value);

                    return true;

                }, 'Only alphanumeric, spaces and symbols(- . + / # & )  are allowed.');


                // validation for only letter, space, numbers, ., hypen
                $.validator.addMethod('alNumSpDotHypen', function (value) {
                    if (value != '')
                        return /^[\w\.\-\ ]*$/.test(value);

                    return true;

                }, 'Only alphanumeric, spaces and symbols(- . )  are allowed.');

                // validation for only letter, space, numbers, ., hypen
                $.validator.addMethod('alSpDotAndHypen', function (value) {
                    if (value != '')
                        return /^[a-zA-Z\.\-\&\ ]+$/.test(value);

                    return true;

                }, 'Only alphabets, spaces and symbols(- . & )  are allowed.');

                $.validator.addMethod('vUserDefId', function (value) {
                    if (value != '')
                        return /^[\w\&\.\-\,\(\)\/]*$/.test(value);

                    return true;

                }, 'Only alphanumeric and symbols(- , . & / () )  are allowed.');

                $.validator.addMethod('vIpAddress', function (value) {
                    if (value != '')
                        return /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(value);

                    return true;

                }, 'Please provide a valid IP Address.');
                $.validator.addMethod('alphahNumHyDotBrackSpc',function (value) {
                    if (value != '')
                        return /^[0-9a-zA-Z\-\ \.\(\) ]*$/.test(value);
                },"Only alphanumeric, spaces and symbols(- . () )  are allowed.");

                $.validator.addMethod('vTitle', function (value) {
                    if (value != '')
                        return /^[\w\.\,\/\-\(\)\'\ ]*$/.test(value);

                }, "Only alphanumeric, spaces and symbols(- , . ' / () )  are allowed.");

                $.validator.addMethod(
                    'vWebsite',
                    function(value) {
                        if (value != '')
                            return /^((https?|ftp|smtp):\/\/)?(www.)?[a-z0-9]+(\.[a-z]{2,}){1,3}(#?\/?[a-zA-Z0-9#]+)*\/?(\?[a-zA-Z0-9-_]+=[a-zA-Z0-9-%]+&?)?$/.test(value);
                        return true;
                    },
                    'Please provide a valid Website.'
                );
                $.validator.addMethod(
                    'vClosingDate',
                    function(value) {
                        var date1 = new Date($('#closingDate').datepicker('getDate'));
                        var today = new Date();
                        today.setHours(0,0,0,0);
                        if(date1-today<0)
                        {
                            return false;
                        }
                        else
                        {
                            return true;
                        }
                    },
                    'Closing date should be equal to or greater than current date and posting date.'
                );

                // Validate alphabets, numbers, and additional special characters
                $.validator.addMethod("allCommonInputAlphaNumericValidation", function (value) {
                    if (value !== '')
                        return /^[^<>]*$/u.test(value);
                    return true;
                }, "Only alphanumeric characters, spaces and special characters are allowed.");


                $.validator.addClassRules({
                    //Allow Only Letters and spaces
                    onlyLetterSp : {
                        onlyLetterSp : true
                    },
                    numSpComma : {
                        numSpComma : true
                    },
                    alphaNumSpCDotHy : {
                        alphaNumSpCDotHy : true
                    },
                    alphaNumSpCDotHySlash : {
                        alphaNumSpCDotHySlash : true
                    },
                    alphaNumSpHy : {
                        alphaNumSpHy : true
                    },
                    alphaNumHypSlash : {
                        alphaNumHypSlash : true
                    },
                    alphaORalphanum : {
                        alphaORalphanum : true
                    },
                    alphaSpNlComma : {
                        alphaSpNlComma : true
                    },
                    alphaNumSpComma : {
                        alphaNumSpComma : true
                    },
                    alphaNumSp : {
                        alphaNumSp : true
                    },
                    alphaNum : {
                        alphaNum : true
                    },
                    onlyLetterNumber : {
                        onlyLetterNumber : true
                    },
                    alphaNumSpDot : {
                        alphaNumSpDot : true
                    },
                    vPhoneNum : {
                        vPhoneNum: true,
                        minlength: 5,
                        maxlength: 15
                    },
                    onlyNumbers : {
                        onlyNumbers: true
                    },

                    onlyNum : {
                        onlyNum: true
                    },

                    vRequired : {
                        required: true
                    },
                    vNumberRequired : {
                        required: true,
                        number: true,
                        numberRequired : true
                    },
                    vNumber :{
                        number: true,
                        numberRequired : true
                    },
                    vEmpFieldLength : {
                        minlength: 3,
                        maxlength: 50
                    },
                    vDocSubType : {
                        maxlength: 100
                    },
                    //Set Validation for Policy Field
                    vPolicy: {
                        required: true,
                        minlength: 2,
                        maxlength: 100,
                        alphaNumSpCDotHySlash: true
                    },
                    vDesignation: {
                        required: true,
                        minlength: 2,
                        maxlength: 75,
                        alphaNumSpCDotHySlash: true
                    },
                    //Set Validation for Name Field
                    vName : {
                        required: true,
                        minlength: 3,
                        maxlength: 50
                    },
                    vReason: {
                        required: true,
                        minlength: 3,
                        maxlength: 200,
                        alphaNumSpCDotHySlash: true,
                    },
                    vOrgName : {
                        required: true,
                        minlength: 3,
                        maxlength: 100,
                    },
                    vRoundName : {
                        minlength: 3,
                        maxlength: 50
                    },
                    vEmpName : {
                        required: true,
                        minlength: 1,
                        maxlength: 50
                    },
                    vZip : {
                        minlength: 2,
                        maxlength: 15
                    },
                    //Set Validation for Description
                    vRemarks : {
                        minlength: 5,
                        maxlength: 500,
                        alphaNumSpCDotHySlashNewLine: true
                    },
                    //Set Validation for Description
                    vAlphaNumSpCDotHySlashNewLine: {
                        minlength: 5,
                        maxlength: 3000,
                        alphaNumSpCDotHySlashNewLine: true
                    },
                    //Set Validation for Description
                    vDescription : {
                        minlength: 5,
                        maxlength: 500,
                        alphaNumSpDot: true
                    },
                    //Set Validation for Description
                    vComments: {
                        minlength: 5,
                        maxlength: 600,
                        alphaNumSpCDotHySlashNewLine: true
                    },
                    //Set Validation for announcementText
                    vAnnouncementText: {
                        minlength: 5,
                        maxlength: 1000,
                    },
                    //Set Validation for Policy No
                    vPolicyNo : {
                        minlength: 1,
                        maxlength: 30,
                        alphaNumSpDot: true
                    },
                    vProject : {
                        //vProjectName : true,
                        required: true,
                        minlength: 3,
                        maxlength: 50
                    },
                    vUploader : {
                        required: true,
                        //vAccept: "file/xls, file/xlsx"
                    },
                    vEmail : {
                        vEmail : true
                    },
                    vTaxRules : {
                        required: true,
                        minlength: 3,
                        maxlength: 30
                    },
                    vJobCode : {
                        minlength: 1,
                        maxlength: 30
                    },
                    alphaNumHypBrack : {
                        alphaNumHypBrack : true
                    },
                    vStreet : {
                        vStreet : true
                    },
                    onlyLetSpBrHyComma : {
                        onlyLetSpBrHyComma : true
                    },
                    onlyLetSpBrComma : {
                        onlyLetSpBrComma : true
                    },
                    onlyLetterSpDot : {
                        onlyLetterSpDot : true
                    },
                    onlyLetterSpDotAmp : {
                        onlyLetterSpDotAmp : true
                    },
                    vCertification : {
                        vCertification : true
                    },
                    alNumSpDotHypen : {
                        alNumSpDotHypen : true
                    },
                    alSpDotAndHypen : {
                        alSpDotAndHypen : true
                    },
                    vUserDefId : {
                        vUserDefId : true
                    },
                    vIpAddress : {
                        vIpAddress : true
                    },
                    alphahNumHyDotBrackSpc : {
                        alphahNumHyDotBrackSpc : true
                    },
                    vWebsite: {
                        vWebsite: true
                    },
                    vNotes : {
                        minlength: 5,
                        maxlength: 500
                    },
                    vTitle  : {
                        vTitle:true
                    },
                    vClosingDate : {
                        vClosingDate:true
                    },
                    vIssuingName : {
                        minlength: 1,
                        maxlength: 30
                    },
                    vNameLength : {
                        minlength: 1,
                        maxlength: 50
                    },
                    vTextArea : {
                        minlength: 5,
                        maxlength: 600,
                        textAreaValidation: true
                    }
                });
                break;
        }

    }
    $('.leaves-panel .panel-reload').on('click', function () {
        tableLeaveSummaryPanel.fnReloadAjax( pageUrl () + "employees/leaves/list-leave-history" );
    });

    $('.task-panel .panel-reload').on('click', function () {
        tableTasksPanel.fnReloadAjax( pageUrl () + "employees/assignments/list-dashboard-assignment" );
    });


    $('.holidays-panel .panel-reload').on('click', function () {
        tableUpcomingHolidaysPanel.fnReloadAjax( pageUrl () + "organization/holidays/list-dashboard-holidays" );
    });
    //To add filter Icon globaly
    var filterImg = pageUrl() + 'images/filter.png';
    $('.filterImg').append('<img class="img-responsive recruitment-filter-img" alt="Filter" src="'+filterImg+'">');
});


/*fucntion to check whether the browser support webp format or not*/
async function supportsWebp() {
  if (!self.createImageBitmap) return false;

  const webpData = 'data:image/webp;base64,UklGRh4AAABXRUJQVlA4TBEAAAAvAAAAAAfQ//73v/+BiOh/AAA=';
  const blob = await fetch(webpData).then(r => r.blob());
  return createImageBitmap(blob).then(() => true, () => false);
}



function fnDeviceColumnDetails(headerArray,valueArray)
{

     sOut = '';
   	sOut += '<div class="row hidden-md hidden-lg">'+
		    '<div class="col-xs-4 col-sm-5 col-md-5">'+ headerArray['Header3'] +'</div>'+
		    '<div class="col-xs-1 col-sm-1 col-md-1"> : </div>'+
		    '<div class="col-xs-6 col-sm-6 col-md-6">'+ fnCheckNull (valueArray['Value_One']) +'</div>'+
		'</div>'
		if (headerArray['Header4']!=undefined)
		{
	sOut +=	    '<div class="row hidden-md hidden-lg">'+
		    '<div class="col-xs-4 col-sm-5 col-md-5">'+ headerArray['Header4'] +'</div>'+
		    '<div class="col-xs-1 col-sm-1 col-md-1"> : </div>'+
		    '<div class="col-xs-6 col-sm-6 col-md-6">'+ fnCheckNull (valueArray['Value_Two']) +'</div>'+
		    '</div>';

		}
		if (headerArray['Header5']!=undefined)
		{
            sOut +=	    '<div class="row hidden-lg">'+
		    '<div class="col-xs-4 col-sm-5 col-md-5">'+ headerArray['Header5'] +'</div>'+
		    '<div class="col-xs-1 col-sm-1 col-md-1"> : </div>'+
		    '<div class="col-xs-6 col-sm-6 col-md-6">'+ fnCheckNull (valueArray['Value_Three']) +'</div>'+
		    '</div>';
		}
		if (headerArray['Header6']!=undefined)
		{
            sOut +=	    '<div class="row hidden-lg">'+
		    '<div class="col-xs-4 col-sm-5 col-md-5">'+ headerArray['Header6'] +'</div>'+
		    '<div class="col-xs-1 col-sm-1 col-md-1"> : </div>'+
		    '<div class="col-xs-6 col-sm-6 col-md-6">'+ fnCheckNull (valueArray['Value_Four']) +'</div>'+
		    '</div>';
		}

		if (headerArray['Header7']!=undefined)
		{
	sOut +=	    '<div class="row">'+
		    '<div class="col-xs-4 col-sm-5 col-md-5">'+ headerArray['Header7'] +'</div>'+
		    '<div class="col-xs-1 col-sm-1 col-md-1"> : </div>'+
		    '<div class="col-xs-6 col-sm-6 col-md-6">'+ fnCheckNull (valueArray['Value_Five']) +'</div>'+
		    '</div>';
		}
		if (headerArray['Header8']!=undefined)
		{
	sOut +=	    '<div class="row">'+
		    '<div class="col-xs-4 col-sm-5 col-md-5">'+ headerArray['Header8'] +'</div>'+
		    '<div class="col-xs-1 col-sm-1 col-md-1"> : </div>'+
		    '<div class="col-xs-6 col-sm-6 col-md-6">'+ fnCheckNull (valueArray['Value_Six']) +'</div>'+
		    '</div>';
		}
		if (headerArray['Header9']!=undefined)
		{
	sOut +=	    '<div class="row">'+
		    '<div class="col-xs-4 col-sm-5 col-md-5">'+ headerArray['Header9'] +'</div>'+
		    '<div class="col-xs-1 col-sm-1 col-md-1"> : </div>'+
		    '<div class="col-xs-6 col-sm-6 col-md-6">'+ fnCheckNull (valueArray['Value_Seven']) +'</div>'+
		    '</div>';
		}
		if (headerArray['Header10']!=undefined)
		{
	sOut +=	    '<div class="row">'+
		    '<div class="col-xs-4 col-sm-5 col-md-5">'+ headerArray['Header10'] +'</div>'+
		    '<div class="col-xs-1 col-sm-1 col-md-1"> : </div>'+
		    '<div class="col-xs-6 col-sm-6 col-md-6">'+ fnCheckNull (valueArray['Value_Eight']) +'</div>'+
		    '</div>';
		}
		if (headerArray['Header11']!=undefined)
		{
	sOut +=	    '<div class="row">'+
		    '<div class="col-xs-4 col-sm-5 col-md-5">'+ headerArray['Header11'] +'</div>'+
		    '<div class="col-xs-1 col-sm-1 col-md-1"> : </div>'+
		    '<div class="col-xs-6 col-sm-6 col-md-6">'+ fnCheckNull (valueArray['Value_Nine']) +'</div>'+
		    '</div>';
		}
        if (headerArray['Header12'] != undefined) {
            sOut += '<div class="row">' +
                '<div class="col-xs-4 col-sm-5 col-md-5">' + headerArray['Header12'] + '</div>' +
                '<div class="col-xs-1 col-sm-1 col-md-1"> : </div>' +
                '<div class="col-xs-6 col-sm-6 col-md-6">' + fnCheckNull(valueArray['Value_Ten']) + '</div>' +
                '</div>';
        }
    return sOut;

}

/* Formating function for row details */
        function fnShowHiddenPaymentTrackerDetails ( nTr ) {
            var aData = tablePaymentTrackerSubGridView.fnGetData( nTr );

                sOut = '<div class="row hidden-md hidden-lg">'+
                            '<div class="col-xs-4">Amount Paid</div>'+
                            '<div class="col-xs-1"> : </div>'+
                            '<div class="col-xs-6">'+ fnCheckNull (aData['Amount_Paid']) +'</div>'+
                        '</div>';

            if (aData['Payment_Type'] != "Cash" && aData['Payment_Type'] != "Debit Card" && aData['Payment_Type'] != "Credit Card")
            {
                sOut +=  '<div class="row">'+
                            '<div class="col-xs-4">Document No</div>'+
                            '<div class="col-xs-1"> : </div>'+
                            '<div class="col-xs-6">'+ fnCheckNull (aData['Document_No']) +'</div>'+
                        '</div>'+
                        '<div class="row">'+
                            '<div class="col-xs-4">Bank Name</div>'+
                            '<div class="col-xs-1"> : </div>'+
                            '<div class="col-xs-6">'+ fnCheckNull (aData['Bank_Name']) +'</div>'+
                        '</div>'+
                        '<div class="row">'+
                            '<div class="col-xs-4">Branch Name</div>'+
                            '<div class="col-xs-1"> : </div>'+
                            '<div class="col-xs-6">'+ fnCheckNull (aData['Branch_Name']) +'</div>'+
                        '</div>';
            }

            sOut += '<div class="row hidden-md hidden-lg">'+
                        '<div class="col-xs-4">Description</div>'+
                        '<div class="col-xs-1"> : </div>'+
                        '<div class="col-xs-6">'+ fnCheckNull (aData['Description']) +'</div>'+
                    '</div>'+
                    '<div class="row">'+
                        '<div class="col-xs-4">Added On</div>'+
                        '<div class="col-xs-1"> : </div>'+
                        '<div class="col-xs-6">'+ fnCheckNull (aData['Added_On']) +'</div>'+
                    '</div>'+
                    '<div class="row">'+
                        '<div class="col-xs-4">Added By</div>'+
                        '<div class="col-xs-1"> : </div>'+
                        '<div class="col-xs-6">'+ fnCheckNull (aData['Added_By_Name']) +'</div>'+
                    '</div>';

            if (aData['Updated_By_Name'] != null && aData['Updated_On'] != null)
            {
                sOut += '<div class="row">'+
                            '<div class="col-xs-4">Updated On</div>'+
                            '<div class="col-xs-1"> : </div>'+
                            '<div class="col-xs-6">'+ fnCheckNull (aData['Updated_On']) +'</div>'+
                        '</div>'+
                        '<div class="row">'+
                            '<div class="col-xs-4">Updated By</div>'+
                            '<div class="col-xs-1"> : </div>'+
                            '<div class="col-xs-6">'+ fnCheckNull (aData['Updated_By_Name']) +'</div>'+
                        '</div>';
            }

            return sOut;
        }

    /* selectAll Event for multiselect combo */
    $('.selectAlll').on('change', function () {
        var selectedValues = $(this).select2('val');

        if (selectedValues !=null) {
            var values =[];
            var checkAll = ['selectAll'];
            var clearAll = ['clearAll'];

            /** check whether the selectAll value exists in selectedvalues array **/
            var is_select = checkAll.every(function(element, index) {
                return element == selectedValues[index];

            });

            /** check whether the clearAll value exists in selectedvalues array **/
            var is_clear = clearAll.every(function(element, index) {
                return element == selectedValues[index];

            });

            /** if the selectAll exists in selectedValues array, proceeds to select all values **/
            if (is_select) {
                /** push the dropdown option value in array **/
                $('.selectAlll option').each(function() {
                    values.push( $(this).prop('value') );
                });

                /** remove the --Select all-- value from values array**/
                var result = values.filter(function(elem){
                   return elem != 'selectAll' && elem != 'clearAll';
                });

                $(this).select2('val',result);
            }
            else if (is_clear) {
                /** if the clearAll exists in selectedValues array, proceeds to clear all values **/

                $(this).select2('val','');
            }
        }
    });

    //File uploader validation for xls/xlsx format
    $('.vFileFormat').on('change', function(){
        var val = $(this).val().toLowerCase();
        var regex = new RegExp("(.*?)\.(xls|xlsx)$");

        if(!(regex.test(val)))
        {
            $(this).addClass("fileFormatError");

            $.validator.addMethod('vFileFormatError', function () { return false; }, "File type should be in xls or xlsx format.");

            $.validator.addClassRules("fileFormatError", { vFileFormatError : true });
        }
        else
        {
            $(this).removeClass("fileFormatError");
        }

        $(this).valid();
    });

    $(document).ready(function() {

        if($('body').data('page') == 'index'){
            setMask('.custom-page');
            /* get the geo coordinates only if the geo location is enforced for the employee */
            $.ajax({
                type     : 'POST',
                dataType : 'json',
                url      : pageUrl() + "employees/attendance/check-geo-enforce",
                success  : function(result)
                {
                    isGeoLocationEnforced = result['isEnable'];
                    if (result['isEnable'] && navigator.geolocation) {
                        /** navigator.geolocation.watchPosition Returns the current position of the user and
                         *  continues to return updated position as the user moves (like the GPS in a car).
                         * When the user comes to dashboard we will continue tracking the position until the user performs the check-in/checkout */
                        watchGeoPositionId = navigator.geolocation.watchPosition(function (position) {

                        });
                    }
                    removeMask('.custom-page');
                },
                error  : function (errorResult){
                    removeMask('.custom-page');
                    if(errorResult.status == 200){
                        sessionExpired();
                    }else{
                        /* To handle internal server error */
                        jAlert({ msg : 'Something went wrong. Please contact system admin.', type : 'warning' });
                    }
                }
            });
            //function to retrievetax amount
            retrieveTaxRegimeAmount();
        }
        /* During page load, update the ICICI banner related content in the eft-configuration page */
        if($('body').data('page') == "eft-configuration") {
            (async () => {
            if(await supportsWebp()) {
                $('.banner-c1').prop('style','background-image:url("'+pageUrl()+'images/digital-banking-solution.webp")')
                resizeICICIBannerStartNowText();
            }
            else{

                $('.banner-c1').prop('style','background-image:url("'+pageUrl()+'images/digital-banking-solution.png")')
                resizeICICIBannerStartNowText();
            }
            })();
        }
        /** When the user refresh the page we need to show the multi comapny landing page, so reset the show auth page flag.  */
        document.cookie = "showAuth=0; path=/;secure";
        if($("#Enable_Workflow").val() === "Yes") {
            let apiCallCount = 0, serviceProviderEmployees = ["-"], groupId= [];
            if($("#isServiceProviderAdmin").val() === "1") {
                getServiceProviderEmployees().then((response) => {
                    serviceProviderEmployees = response;
                    apiCallCount++;
                    checkApiCallCOunt(apiCallCount, serviceProviderEmployees, groupId);
                }).catch(() => {
                    apiCallCount++;
                    checkApiCallCOunt(apiCallCount, serviceProviderEmployees, groupId);
                })
                getEmployeeGroupIds().then((response) => {
                    groupId = response;
                    apiCallCount++;
                    checkApiCallCOunt(apiCallCount, serviceProviderEmployees, groupId);
                }).catch(() => {
                    apiCallCount++;
                    checkApiCallCOunt(apiCallCount, serviceProviderEmployees, groupId);
                })
            } else {
                apiCallCount = 1;
                getServiceProviderEmployees().then((response) => {
                    serviceProviderEmployees = response;
                    apiCallCount++;
                    checkApiCallCOunt(apiCallCount, serviceProviderEmployees, groupId);
                }).catch(() => {
                    apiCallCount++;
                    checkApiCallCOunt(apiCallCount, serviceProviderEmployees, groupId);
                })
            }
        }
    });

    // Bind a scroll event to the window
    $(".modal").on("scroll", function() {
      $('.ui-datepicker').hide();
    });

    $(".date-picker, .datetimepicker").on('click', function() {
      $('.ui-datepicker').show();
    })

    $(window).on('load', function() {
        $.ajax({
            type: 'POST',
            dataType: 'json',
            url: pageUrl() + 'default/index/clear-user-session',
            success: function() {
                // Handle any actions on success here if needed
            }
        });
    });

    function checkApiCallCOunt(count, serviceProviderEmployees, groupId) {count
        if(count === 2) {
            getTaskCount(serviceProviderEmployees, groupId);
        }
    }

    function getTaskCount(serviceProviderEmployees, groupId, formId = 31) {
        var employeeId = localStorage.getItem('LoginEmpId');
        let authorizationHeader = $.cookie('accessToken') ? $.cookie('accessToken') : null;
        let refreshTokenHeader = $.cookie('refreshToken') ? $.cookie('refreshToken') : null;

        let myTaskFilter = [
            {"key":"assignee","value":[employeeId],"operator":"in"}
        ];
        if($("#isServiceProviderAdmin").val() === "1") {
            myTaskFilter.push({
                "key": "custom_group_id",
                "value": groupId.length > 0 ? groupId : ['-'],
                "operator": "in",
                "Employee_Id" : serviceProviderEmployees
            });
        } else {
            myTaskFilter.push({
                "key": "custom_group_id",
                "value": groupId.length > 0 ? groupId : ['-'],
                "operator": "in"
            });
        }
        axios.post(getApiCustomDomains() + "/workflow/userTaskCount", {
            "filter": myTaskFilter,
            "form_id": formId,
        },{
            headers: {
                org_code: organizationCode,
                employee_id: employeeId,
                db_prefix: localStorage.getItem('domain'),
                authorization:authorizationHeader,
                refresh_token:refreshTokenHeader
            }
        }).then(response => {
            if(response.data) {
                let taskCountRes = JSON.parse(response.data.message);
                let groupCount = taskCountRes.groups
                  ? taskCountRes.groups.taskcount
                  : 0;
                let assigneeCount = taskCountRes.assignee
                  ? taskCountRes.assignee.taskcount
                  : 0;
                let totalCount = assigneeCount + groupCount;
                if(totalCount > 0) {
                    let notificationMsg = totalCount + " Leave request(s) waiting for your approval";
                    $("#leaveNotificationCount").text(notificationMsg);
                    let totalNotificationCount = parseInt($("#notificationsCnt").val()) + 1;
                    $("#notificationCount, #notificationSubCount").text(totalNotificationCount);
                } else {
                    $("#leaveNotification").addClass("hidden");
                }
            }
        })
        .catch(function () {
            $("#leaveNotification").addClass("hidden");
        });
    };

    function getEmployeeGroupIds() {
        var atsBaseUrl = getApiCustomDomains('ats');
        var employeeId = localStorage.getItem('LoginEmpId');
        var getGroupIdQuery = `
        query ($employeeId:Int!)
        { getEmployeeGroupIds (employeeId:$employeeId) { errorCode message GroupIdArray}}
        `;
        var variables = { employeeId: parseInt(employeeId) };
        return new Promise((resolve, reject) => {
            $.ajax({
                method: 'POST',
                url: atsBaseUrl,
                headers: getGraphqlAPIHeaders(),
                data: JSON.stringify({
                    query: getGroupIdQuery,
                    variables: variables
                }),
                success: function (result) {
                    let groupId = result.data.getEmployeeGroupIds.GroupIdArray
                    resolve(groupId);
                },
                error: function (error) {
                    reject(error);
                }
            });
        });
    }

    function getServiceProviderEmployees() {
        var atsBaseUrl = getApiCustomDomains('ats');
        var employeeId = localStorage.getItem('LoginEmpId');
        var getServiceProviderEmployeesQuery = `
            query ($employeeId:Int!)
            { getServiceProviderEmployees (employeeId:$employeeId) { errorCode message employeeId}}
        `;
        var variables = { employeeId: parseInt(employeeId) };
        return new Promise((resolve, reject) => {
            $.ajax({
                method: 'POST',
                url: atsBaseUrl,
                headers: getGraphqlAPIHeaders(),
                data: JSON.stringify({
                    query: getServiceProviderEmployeesQuery,
                    variables: variables
                }),
                success: function (result) {
                    if(result.data){
                        let serviceProviderEmployees = result.data.getServiceProviderEmployees.employeeId.length > 0
                         ? result.data.getServiceProviderEmployees.employeeId : ['-'];
                         resolve(serviceProviderEmployees);
                    } else {
                        reject("error");
                    }
                },
                error: function (error) {
                    reject(error);
                }
            });
        });
    }

    $(window).on('resize', function() {
        /* During window resize, change the ICICI banner related content in the
        eft-configuration page based on the screen size */
        if($('body').data('page') == 'eft-configuration'){
            resizeICICIBannerStartNowText();
        }
    });

    /* Form submit and reset event.
    Function is used to remove the 'form-errors' for the dropdown fields */
    $("button[type='submit'],button[type='reset']").on('click', function(e) {
        try{
            /* Get the closest <form> element from the submit/reset button */
            var getClosestForm = $(this).closest("form");

            /* 'Closest' method will return the current element(submit/reset) if the current element lies inside the <form> element.
            This is for the form which does not have subform. In this case, 'getClosestForm' length will be zero.
            So get the form element by using the modal-body class */
            if(getClosestForm.length == 0){
                var parentDiv =  $(this).parent().parent();
                var modalbodyElement = $(parentDiv).children(".modal-body");

                /* Get the <form> element from the 'modal-body' element */
                getClosestForm = $(modalbodyElement).find('form');

                getFormRelElements(getClosestForm);

            }else{
                /* For the sub forms, submit and reset buttons are declared outside the forms.
                So for the subforms,<form> element will be returned */
                getFormRelElements(getClosestForm);
            }
        }
        catch(error){
        }
    });

    if ($('body').data('page')!='billingpayment' && $('body').data('page')!= 'login' && $('body').data('page') != 'salary-payslip'
        && $('body').data('page')!= 'reset')
    {
        $(".orgDateFormat").datepicker({dateFormat:dateFormat[0]});

        // $(".datePickerRead").focusin(function(){
        $(document).on('focusin', '.datePickerRead', function() {
            $('.datePickerRead').prop('readonly', true);
        });

        // $(".datePickerRead").focusout(function(){
        $(document).on('focusout', '.datePickerRead', function() {
            $('.datePickerRead').prop('readOnly', false);
        });

        if($(window).width() <= 768){
            $('select').prop('data-search',false);
        }
    }

    // Position the datepicker based on the input field
    $('.date-picker, .datetimepicker').on('click', function() {
        // Get the position of the input field
        var inputPosition = $(this).offset();
        // Set the top and left properties for the datepicker
        $('.ui-datepicker').css({
          'top': inputPosition.top + $(this).outerHeight(),
          'left': inputPosition.left
        });
    });

    if ($('body').data('page')!='billingpayment' && $('body').data('page')!= 'login' && $('body').data('page')!= 'reset')
    {
        //$('#formShiftStartDate,#formShiftEndDate').datepicker("setDate",new Date(tzDate()));
        $('.holidayDatePicker').datepicker({dateFormat:dateFormat[0],minDate:new Date(tzDate())});

        $('#formLeaveFreezeFrom').datepicker({dateFormat:dateFormat[0]});
        $('#formLeaveFreezeTo, #deductionFormEffectiveDate, #formAdhocConsiderationDate').datepicker({dateFormat:dateFormat[0]/*,minDate:new Date*/});

        var dateConversion = new Date().getTime() - 24 * 60 * 60 * 1000;
        var yesterday = new Date(dateConversion);
        $('#processFromDate,#processToDate').datepicker({

            dateFormat: dateFormat[0], maxDate: yesterday
        });

        $('#formPaymentDate, #formAssignmentEndDate,#formAssignmentStartDate,#formClosingDate').datepicker({
        dateFormat:dateFormat[0],minDate:new Date(tzDate())/*new Date*/});

        $('#Award_Date, #Memo_Date, #formComplaintDate, #Warning_Date').datepicker({
        dateFormat:dateFormat[0],
        minDate:new Date(tzDate())});

        $('#closingDate,#postingDate,#expectedJoinDate,#formResignationNoticeDate, #formResignationDate,#Assessment_Start_Month,#Assessment_End_Month,#formTransferDate,#formStartDate,#formEndDate,#formEditPaymentDate, #formInsPaymentTrackerPaymentDate, #formPfPaymentTrackerPaymentDate, #formPaymentTrackerPaymentDate, #formEtfPaymentTrackerPaymentDate, #formTdsPaymentTrackerPaymentDate,#formJobEditProbationDate,#Compensatory_From,#FromDate,#ToDate,#formLwfPaymentTrackerPaymentDate,#eSTOStartDate,#eSTOEndDate').datepicker({
            dateFormat:dateFormat[0]
        });

        // $('#filterAttendanceDateBegin,#filterAttendanceDateEnd,#filterAttendanceImportDateBegin,#filterAttendanceImportDateEnd').datepicker({
        //     dateFormat: dateFormat[0]
        // });

        $('.datepickerInitiator').datepicker({
            dateFormat:dateFormat[0]
        });

       $('.closeMonthPicker').on('change', function(){
          $('.datepicker').prop('style','display:none;');
        });


        $('#formInvoiceDate,#formInvoiceOCRDate,#formShiftStartDate, #formShiftEndDate,#formAppliedDate, #formCommissionConsiderationDate, #formLoanConsiderationDate, #formReimbursementConsiderationDate, #formShiftConsiderationDate').datepicker({dateFormat:dateFormat[0],maxDate:new Date(tzDate())});


        //datepicker
        $('#formMSRecalcEffectiveDate').datepicker({ dateFormat: dateFormat[0]});

        $('#formTravelStartDate,#formTravelEndDate').datepicker({dateFormat: dateFormat[0],minDate:new Date(tzDate())});

        $('#formWeekEndingDate,#cloneWeekEndingDate,#exportWeekEndingDate').datepicker({dateFormat: dateFormat[0],beforeShowDay:
		function(dt)
		{
		return [dt.getDay() == 6, ""];
		}
        });
        $('#formActivityFrom').datepicker({
            dateFormat: dateFormat[0],
            beforeShowDay: function(dt) {
                return [dt.getDay() == 0, ""];
            }
        });
        $('#formActivityTo').datepicker({ dateFormat: dateFormat[0],
            beforeShowDay: function(dt) {
                return [dt.getDay() == 6, ""];
            }
        });

        var dateToday = new Date(tzDate());
        var yearrange = (dateToday.getFullYear() - 100) + ":" + (dateToday.getFullYear() + 100);

         /** employee form - personal info **/
        $('#CandidateDOB,#formEditDOB, #Date_Of_Birth').datepicker({ dateFormat: dateFormat[0],changeYear: true,
            yearRange: yearrange, maxDate: new Date((new Date(tzDate()).getFullYear()-15),11,31)});

        $("#formEditDependentDOB, #Dependent_DOB").datepicker({  dateFormat: dateFormat[0],
        changeYear: true,yearRange: yearrange, maxDate: new Date(tzDate())});

        //$("#formEditSmokerAsOf").datepicker({ dateFormat: format[0],changeYear: true,
        //yearRange: "1900:2999",maxDate: new Date});

        $("#formEditIssueDate,#formEditPassportIssueDate").datepicker({  dateFormat: dateFormat[0],
        changeYear: true,yearRange: yearrange, maxDate: new Date(tzDate())});

        $("#formEditExpiryDate,#formEditPassportExpiryDate").datepicker({  dateFormat: dateFormat[0],
        changeYear: true,yearRange: yearrange, minDate: new Date(tzDate())});

        /** Employees - Job Info **/
        $("#formJobEditDateOfJoin,#formJobEditConfirmationDate,#formJobEditInactiveDate,#Date_Of_Join, #Confirmation_Date").datepicker({  dateFormat: dateFormat[0],
            changeYear: true,yearRange: yearrange
        });

        /** Set the previous month-last date as the max date in the date picker */
        var empImportInactiveMaxDate = "-" + dateToday.getDate() + "D"; // -1D
        $("#formEIInactiveDate").datepicker({
            dateFormat: dateFormat[0],
            changeYear: true,
            yearRange: yearrange,
            maxDate: empImportInactiveMaxDate
        });

        $("#formEditExperienceFrom, #formEditExperienceTo").datepicker({ dateFormat: dateFormat[0],changeYear: true,
        yearRange: yearrange, maxDate: new Date(tzDate())});

        $( "#formEditAssetReceiveDate,#date_of_joining" ).datepicker({ dateFormat: dateFormat[0], maxDate: new Date(tzDate())});

        $( "#formEditCertificateReceivedOn,#formEditTrainingStartDate,#formEditTrainingEndDate,#formEditAwardReceivedOn,#formEditSmokerAsOf" ).datepicker({ dateFormat: dateFormat[0],
            changeYear: true,maxDate: new Date(tzDate()),yearRange: yearrange});

        $('#formHWEffectiveFrom,#formMSEffectiveFrom, #Effective_Date').datepicker({ dateFormat: dateFormat[0],changeYear: true});

        $("#deductionFormEndDate").datepicker({
            dateFormat:dateFormat[0],
            minDate: '+1m',
            beforeShow: function() {
            //get date startDate is set to
            var startDate = $("#deductionFormEffectiveDate").datepicker('getDate');
            //if a date was selected else do nothing
            if (startDate != null) {
            startDate.setMonth(startDate.getMonth()+1);
            $(this).datepicker('option', 'minDate',startDate);
            }
            }
        });

        $("#formbonusDate").datepicker({
        dateFormat:dateFormat[0],
        defaultDate: new Date(tzDate()),
        //$( "#formbonusDate" ).datepicker({ defaultDate: +15 });
        });
    }


    /***************************/

        // Inline popups
        $('.popupspan').magnificPopup({
          delegate: 'a',
          removalDelay: 500, //delay removal by X to allow out-animation
          callbacks: {
            beforeOpen: function() {
               this.st.mainClass = this.st.el.prop('data-effect');
            }
          },
          midClick: true // allow opening popup on middle mouse click. Always set it to true if you don't provide alternative source.
        });

        // Image popups
        $('.image-popups').magnificPopup({
          delegate: 'a',
          type: 'image',
          removalDelay: 500, //delay removal by X to allow out-animation
          callbacks: {
            beforeOpen: function() {
              // just a hack that adds mfp-anim class to markup
               this.st.image.markup = this.st.image.markup.replace('mfp-figure', 'mfp-figure mfp-with-anim');
               this.st.mainClass = this.st.el.prop('data-effect');
            }
          },
          closeOnContentClick: true,
          midClick: true // allow opening popup on middle mouse click. Always set it to true if you don't provide alternative source.
        });

        // Hinge effect popup
        $('a.hinge').magnificPopup({
          mainClass: 'mfp-with-fade',
          removalDelay: 1000, //delay removal by X to allow out-animation
          callbacks: {
            beforeClose: function() {
                this.content.addClass('hinge');
            },
            close: function() {
                this.content.removeClass('hinge');
            }
          },
          midClick: true
        });

    var fileType={};
    fileType["resume"]="documents";
    fileType["mou"]="documents";
    fileType["logo"]="publicImages";
    fileType["formimage"]="publicImages";
    fileType["profilepicture"]="publicImages";

    //To prepare a path based on File type for s3
    function prepareS3Path(type,fileName,domainName,orgCode,formName,isRequireNewUrl){
        var fileType={};
        fileType["resume"]="documents";
        fileType["mou"]="documents";
        fileType["logo"]="publicImages";
        fileType["formimage"]="publicImages";
        fileType["profilepicture"]="publicImages";

        if(isRequireNewUrl){
        d = new Date();
        dformat = [d.getFullYear(), d.getMonth() + 1, d.getDate()].join('-') + '.' + [d.getHours(), d.getMinutes(), d.getSeconds()].join(':');
        s3Path= domainName + '/' + orgCode + '/' + fileType[type.toLowerCase()] + '/' + formName+'/' + dformat +'?'+ fileName;
        }else{
            s3Path= domainName + '/' + orgCode + '/' + fileType[type.toLowerCase()] + '/' + formName+'/' + fileName;
        }
        return s3Path;
    }

    // To create a space in S3 for given path
function fngetAtsPresignedPutUrl(orgCode="",s3path,atsBaseUrl,callback){
        var dataRequest={
            "variables" : {
            "fileName" : s3path,
            "action" : "upload"
            },
            "query":"mutation ($fileName: String!,$action: String!) { getPresignedUrl(fileName:$fileName,action:$action) { errorCode message presignedUrl}}"
            }

            $.ajax({
                method: 'POST',
                url: atsBaseUrl,
                contentType: 'application/json',
                headers: getGraphqlAPIHeaders(),
                data: JSON.stringify(dataRequest),

                success: function(result) {
                    if (result.data !== null) {
                        callback(result.data.getPresignedUrl.presignedUrl);
                    } else {
                        $(window).scrollTop(0);
                        jAlert({
                            panel: $(errorPage),
                            msg: 'Something went wrong. Please contact system administrator',
                            type: 'warning'
                        });
                    }
                },
                error: function(result) {
                    $(window).scrollTop(0);
                    removeMask();
                    jAlert({
                        panel: $(errorPage),
                        msg: 'Something went wrong. Please contact system administrator',
                        type: 'warning'
                    });
                }
            });
    }
// To delete image from S3 to given path
function deleteS3Files(fileName,orgCode,atsBaseUrl,callback){
        var dataRequest={
            "variables" : {
                "fileName" : fileName
            },
            "query":"mutation ($fileName: String!) { deleteS3Files(fileName:$fileName) { errorCode message}}"
        }

        $.ajax({
            method: 'POST',
            url: atsBaseUrl,
            contentType: 'application/json',
            headers: getGraphqlAPIHeaders(),
            data: JSON.stringify(dataRequest),

            success: function(result) {
                if (result.data !== null) {
                    callback(result.data);
                } else {
                    $(window).scrollTop(0);
                    jAlert({
                        panel: $(errorPage),
                        msg: 'Something went wrong. Please contact system administrator',
                        type: 'warning'
                    });
                }
            },
            error: function(result) {
                $(window).scrollTop(0);
                jAlert({
                    panel: $(errorPage),
                    msg: 'Something went wrong. Please contact system administrator',
                    type: 'warning'
                });
            }
        });
    }
    /** report logo in view page **/
    function fngetReportLogo(logoId,logoName,type)
    {
        let orgCode    = fngetOrgCode();

        var sUrl = fngetSignedUrl("hrapp_upload/"+orgCode+"_tmp/logos/"+logoName,'logoBucket');
        if($.inArray(sUrl, ['', null,'sessionexpired','internalerror']) === -1){

            if(type == 'edit' && type != undefined)
            {
                $("#"+logoId+" img").prop('src', sUrl);
            }
            else
            {
                logoId.html("");
                logoId.append('<img id="viewImage" style="width:150px;height:100px"class="img-responsive" alt="'+pageUrl()+'images/image-upload.png'+'" data-src="" src="'+sUrl+'">');
            }
        }
        else
        {
            if(type == 'edit' && type != undefined)
            {
                $("#"+logoId+" img").prop('src', pageUrl()+'images/image-upload.png');
            }
            else
            {
                logoId.html("-");
            }
        }
    }

   /** product logo in view page **/
   function fngetProductLogo(logoId,logoName,type)
   {

       var sUrl = fngetSignedUrl("hrapp_upload/"+logoName,'logoBucket');
       if($.inArray(sUrl, ['', null,'sessionexpired','internalerror']) === -1){

           if(type == 'edit' && type != undefined)
           {
               $("#"+logoId+" img").prop('src', sUrl);
           }
           else
           {
               logoId.html("");
               logoId.append('<img class="svg orgProductLogoSvg" id="viewOrgProductLogo" style="width: 150px; height:100px;"class="img-responsive" alt="'+pageUrl()+'images/image-upload.png'+'" data-src="" src="'+sUrl+'">');
           }
       }
       else
       {
           if(type == 'edit' && type != undefined)
           {
               $("#"+logoId+" img").prop('src', pageUrl()+'images/image-upload.png');
           }
           else
           {
               logoId.html("-");
           }
       }
   }

   function getLocation(callback, action='dashboard') {
    let orgCode = fngetOrgCode();
    /* Check if Geolocation is supported */
    if (navigator.geolocation) {
        /** navigator.geolocation.watchPosition Returns the current position of the user and
         *  continues to return updated position as the user moves (like the GPS in a car). */
        var geoPositionId = navigator.geolocation.watchPosition(function (data) {
            /* For testing only we should remove this after testing */

            latitude = data.coords.latitude;
            longitude = data.coords.longitude;

            var result = {
                latitude: latitude,
                longitude: longitude
            }
            /* if the cordinates are retrieved then clear the watch. */
            navigator.geolocation.clearWatch(geoPositionId);
            navigator.geolocation.clearWatch(watchGeoPositionId);

            callback(result);
        },
        function error(error){

            if(action == 'dashboard') {

                if(orgCode == "capricecloud" || orgCode == "demo" || orgCode == "infotech")
                {
                    switch(error.code) {
                        case error.PERMISSION_DENIED:
                            alert("User denied the request for Geolocation.");
                            break;
                        case error.POSITION_UNAVAILABLE:
                            alert("Location information is unavailable.");
                        break;
                        case error.TIMEOUT:
                            alert("The request to get user location timed out.");
                            break;
                        case error.UNKNOWN_ERROR:
                            alert("An unknown error occurred.");
                            break;
                        default:
                            alert(error.code);
                            break;
                    }
                }
                $('#geoLocationModel').modal('show');
            } else {
                callback({latitude: '',longitude: ''});
            }
        },
        {
            maximumAge:1000, timeout:5000, enableHighAccuracy: true
        });
        } else {
            /* Geolocation is not supported by this browser. */
            if(action == 'dashboard') {
                let orgCode = fngetOrgCode();

                if(orgCode == "capricecloud" || orgCode == "demo" || orgCode == "infotech")
                {
                    alert('Geolocation is not supported by this browser');
                }
                $('#geoLocationModel').modal('show');
            } else {
                callback({latitude: '',longitude: ''});
            }
        }
    }

//Is mobile device or not
   function findDevice()
    {
        var parser = new UAParser();
        var result = parser.getResult();
        var osName=result.os.name;
        var deviceType=result.device.type;

       if(deviceType=='mobile' || osName=='Android' || osName=='Windows Phone')
         {
            if($.cookie("isNativeMobileApp") == 1){
                deviceType='Mobile Application';
            } else {
                deviceType='Mobile Browser';
            }
         }
         else
         {
           deviceType='Web';
         }
       return deviceType;
   }
/* Get the rows which are currently selected in custom report. separate function because we are hiding the grid when opening the view form so the row selection is removed and we are not able to get the selected row */
function fnGetSelectedRw( oTableLocal, recId ) {
    return oTableLocal.$('tr#row_'+recId);
}

/* - Change the banner start now button title based on the screen size
    - Hide and show the register option based on the screen size */
function resizeICICIBannerStartNowText(){
    if($(window).width() < 768 || ($(window).width() >= 1015 && $(window).width() <= 1059)){
        $('.icici-banner-start-title').text('START');
        if($(window).width() < 768 ){
            $('.icici-banner-register').hide();
        }
    }else{
        $('.icici-banner-register').show();
        $('.icici-banner-start-title').text('START NOW');
    }
}


/* Function to get the form id and method attributes to remove the form-error class for all the combo fields
on form submit and reset */
function getFormRelElements(getClosestForm){
    $.each(getClosestForm, function(formIndex,formTagElement) {
        /* Get the form id */
        var modalFormId = $(formTagElement).attr("id")

        /* Get the form attribute */
        var modalFormMethodAttr = $('#'+modalFormId).prop('method')

        if(!!modalFormMethodAttr){
            /* If it is edit form*/
            if(modalFormMethodAttr.toUpperCase() === 'POST'){
                removeFormClassErrorForForms('#'+modalFormId);
            }
        }
    });
}
/* Function to remove the 'form-error' class for the dropdown fields in the form */
function removeFormClassErrorForForms(modalFormId){
    /* Get the select elements in the forms */
    var selectTagElements = $(modalFormId).find('select');

    /* If selectTagElements is not empty, then following part will be iterated for all the
    select elements in the forms */
    $.each(selectTagElements, function(selectIndex,selectId) {
        /* Get the id for the select element */
        var selectTagId = $(selectId).attr("id")

        /* If the select tag has form-error */
        if($('#'+selectTagId).hasClass('form-error') || $('#s2id_'+selectTagId).hasClass('form-error')){
            $('#s2id_'+selectTagId).removeClass('form-error')
        }
    });
}

function viewS3Files(filePath,orgCode,atsBaseUrl,keys,callback){
    var dataRequest={
        "variables" : {
        "fileName" : filePath,
        "action" : "view",
        "isEmployee":1,
        },
        "query":"mutation ($fileName: String!,$action: String!,$isEmployee:Int) { getPresignedUrl(fileName:$fileName,action:$action,isEmployee:$isEmployee) { errorCode message presignedUrl}}"
        }

        $.ajax({
            method: 'POST',
            url: atsBaseUrl,
            contentType: 'application/json',
            headers: getGraphqlAPIHeaders(),
            data: JSON.stringify(dataRequest),

            success: function(result) {
                if (result.data !== null) {
                    callback(result.data,keys);
                } else {
                    $(window).scrollTop(0);
                    jAlert({
                        panel: $(errorPage),
                        msg: 'Something went wrong. Please contact system administrator',
                        type: 'warning'
                    });
                }
            },
            error: function(result) {
                $(window).scrollTop(0);
                jAlert({
                    panel: $(errorPage),
                    msg: 'Something went wrong. Please contact system administrator',
                    type: 'warning'
                });
            }
        });
}

function getPresignedURLForView(filePath,fileType,atsBaseUrl,callback){
    setMask('#wholepage');
    var dataRequest={
        "variables" : {
        "fileName" : filePath,
        "action" : "view",
        "type":fileType,
        },
        "query":"mutation ($fileName: String!,$action: String!,$type: String) { getPresignedUrl(fileName:$fileName,action:$action,type:$type) { errorCode message presignedUrl}}"
        }

        $.ajax({
            method: 'POST',
            url: atsBaseUrl,
            contentType: 'application/json',
            headers: getGraphqlAPIHeaders(),
            data: JSON.stringify(dataRequest),

            success: function(result) {
                removeMask()

                if (result.data !== null) {
                    callback(result.data.getPresignedUrl.presignedUrl);
                } else {
                    $(window).scrollTop(0);
                    jAlert({
                        panel: $(errorPage),
                        msg: 'Error while getting the pre signed url, please try after sometime.',
                        type: 'warning'
                    });
                }
            },
            error: function(result) {
                $(window).scrollTop(0);
                removeMask()
                jAlert({
                    panel: $(errorPage),
                    msg: 'Error while getting the pre signed url, please try after sometime.',
                    type: 'warning'
                });
            }
        });
}


// To Get the Request from the parameters and send responce
// To Avoid get multiple axaj service in single page
function getRequstSendResponse(methodType,errorPanelId,atsBaseUrl,dataRequest,orgCode,ipAddress,callback){
    $.ajax({
    method: methodType,
    url: atsBaseUrl,
    headers: getGraphqlAPIHeaders(ipAddress),
    data: JSON.stringify(dataRequest),
        success: function(result) {
            if(result.data){
                callback(result);
            }else{
                let errorCode = "";
                if(result && result.errors && result.errors.length > 0) {
                    errorCode = JSON.parse(result.errors[0].message).errorCode;
                }
                if(errorMsgJSON[errorCode]){
                    removeMask();
                    $(window).scrollTop(0);
                    jAlert({
                        panel: errorPanelId?$(errorPanelId):'',
                        msg: errorMsgJSON[errorCode],
                        type: 'warning'
                    });
                }else{
                    removeMask();
                    $(window).scrollTop(0);
                    jAlert({
                        msg: 'Something went wrong. Please contact system administrator.',
                        type: 'warning'
                    });
                }
            }
        },
        //error while listing modules
        error: function(result) {
            removeMask();
            $(window).scrollTop(0);
            jAlert({
                panel: $('#wholepage'),
                msg: 'Something went wrong. Please contact system administrator.',
                type: 'warning'
            });
        }

    });
}

function prepareSelectOptions(lists,valueId,valueName,attr1,attr2,attr3){
    var options='';
    if(typeof lists !='undefined'){
        for(list of lists){
            options+='<option value="'+list[valueId]+'"';
                if(typeof attr1!='undefined')
                    options+=' '+attr1+'="'+list[attr1]+'"';
                if(typeof attr2!='undefined')
                    options+=' '+attr2+'="'+list[attr2]+'"';
                if(typeof attr3!='undefined')
                    options+=' '+attr3+'="'+list[attr3]+'"';
            options+='>'+list[valueName]+'</option>'
        }
    }
    return options;
}

function getAccessRightsJson(orgCode,atsBaseUrl,formName,employeeId,errorPanelId,accessRightsParams,callback){

    // get the access rights for the form
    checkAccessRights(orgCode,atsBaseUrl,formName,employeeId,errorPanelId,function(error, accessRights){
        // check if access rights are retrieved successfully
        if(accessRights)
        {
            //assign the access rights
            accessRightsParams['viewAccessRights'] = accessRights.Role_View;
            accessRightsParams['addAccessRights'] = accessRights.Role_Add;
            accessRightsParams['updateAccessRights'] = accessRights.Role_Update;
            accessRightsParams['deleteAccessRights'] = accessRights.Role_Delete;
        }
        callback(accessRightsParams);
    });
}
/* send the Signin link to email and notify the user*/
function sendSigninLinkToEmail(email, sendInvitation, callback) {
    if(sendInvitation){
        var actionCodeSettings = {
            'url': pageUrl() + 'auth?signinEmailLink=1&value='+btoa(email),
            'handleCodeInApp': true // This must be true.
        };

        var message = null;
        /** send signin link*/
        firebase.auth().sendSignInLinkToEmail(email, actionCodeSettings).then(function() {
            callback(null,'Signup link sent successfully');
        }).catch(function(error) {
            // Handle Errors here.
            switch(error.code){
                case 'auth/unauthorized-continue-uri' :
                    message = 'We are working on to activate your account. For more information, contact our support team';
                    break;
                default:
                    message = 'Could not send email link';
                    break;
            }
            callback(message,null);
        });
    } else {
        callback(null,null);
    }
}

// function to retrieve new and old regime based tax amount
function retrieveTaxRegimeAmount() {

    $.ajax({
        type: 'POST',
        dataType: 'json',
        async: false,
        url: pageUrl() + 'payroll/tax-rules/compare-tax-regime',
        success: function (result) {
            if (isJson(result) && result.success) {
                var regimeDetails = result.regimeComparisionDetails;
                regimeComparisionDetails = regimeDetails;
                //check whether want to show compare tax regime alert or not
                if(regimeDetails.Trigger_Regime_Change)
                {
                    $('#tax-regime-notify-bar').show();
                    $('#additional-top-margin').show();
                }
                else{
                        $('#tax-regime-notify-bar').hide();
                        $('#additional-top-margin').hide();
                }
            }else{
                 //on error we hide the notify alert bar
                $('#tax-regime-notify-bar').hide();
                $('#additional-top-margin').hide();
            }
        },
        error: function(retrieveTaxRegimeError){
            //on error we hide the notify alert bar
            $('#tax-regime-notify-bar').hide();
            $('#additional-top-margin').hide();
            if (retrieveTaxRegimeError.status === 200) {
                sessionExpired();
            }
        }
    });
}

// function to trim multiple line or new line
function replaceSentanceWithoutExtraSapceAndNewline(sentance) {
    if (sentance) {
      let modifiedSentance = sentance.replace(/(\r\n|\n|\r)/gm, "");
      modifiedSentance.replace(/\s\s+/g, " ");
      return modifiedSentance;
    }
    return "";
}

// export xlsx file
function exportExcelFile(fileHeader, exportData, sheetFileName,organizationName=null,reportTitle=null,createdBy=null,createdOn=null,footerData=null,headerReference=null,headerFormula=null,result=null) {
    let fileExportData = exportData;
    // Create workbook and worksheet
    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet(sheetFileName);
    worksheet.columns = fileHeader;
    // Calculate the number of columns to merge
    const numColumnsToMerge = fileHeader.length;
    const cellValues = [];
    if (organizationName != null) {
        cellValues.push(organizationName);
    }

    if (reportTitle != null) {
        cellValues.push(reportTitle);
    }

    if (createdBy != null && createdBy !='') {
        cellValues.push(createdBy);
    }

    if (createdOn != null && createdOn !='') {
        cellValues.push(createdOn);
    }

    const startRowIndex = 1;
    const cellValueLength = cellValues.length;
    for (let i = 0; i < cellValueLength; i++) {
        if (cellValues[i]) {
            // Merge the cells in the start row, start column, end row, end column
            worksheet.mergeCells(i+1, 1, i+1, numColumnsToMerge);
            const cell = worksheet.getCell(`A${startRowIndex + i}`);
            cell.value = cellValues[i];
            cell.font = { bold: true };
        }
    }

    if(reportTitle!=null)
    {
        if (sheetFileName.toLowerCase() === 'wps')
        {
            firstHeaderDetails = result.firstHeaderDetails;
            if(firstHeaderDetails!=null)
            {
                firstHeaderRow = worksheet.addRow(firstHeaderDetails);
                firstHeaderRow.eachCell((cell) => { // Iterate over cells in header row
                    cell.fill = {
                        type: "pattern",
                        pattern: "solid",
                        fgColor: { argb: "92CDDC" },
                    };
                    cell.alignment = { vertical: "middle", horizontal: "center" };
                    cell.font = {
                        name: "Calibri",
                        size: 11,
                        bold: true,
                    };
                    cell.border = {
                        top: { style: "thin" },
                        left: { style: "thin" },
                        bottom: { style: "thin" },
                        right: { style: "thin" },
                    };
                });
            }

            firstHeaderData = result.firstHeaderData;
            if(firstHeaderData!=null)
            {
                worksheet.addRow(firstHeaderData);
            }
        }

        const headerRow = worksheet.addRow(fileHeader.map((header) => header.header)); // Add header row

        headerRow.eachCell((cell) => { // Iterate over cells in header row
            cell.fill = {
                type: "pattern",
                pattern: "solid",
                fgColor: { argb: "92CDDC" },
            };
            cell.alignment = { vertical: "middle", horizontal: "center" };
            cell.font = {
                name: "Calibri",
                size: 11,
                bold: true,
            };
            cell.border = {
                top: { style: "thin" },
                left: { style: "thin" },
                bottom: { style: "thin" },
                right: { style: "thin" },
            };
        });


        if (sheetFileName.toLowerCase() === 'form 24q annexure ii')
        {
            if(headerReference!=null)
            {
                worksheet.addRow(headerReference.map((header) => header.header));
            }

            if(headerFormula!=null)
            {
                worksheet.addRow(headerFormula.map((header) => header.header));
            }
        }
    }

    // assign data in each row
    if (fileExportData && fileExportData.length > 0) {
        worksheet.addRows(fileExportData);
    }
    // header styles (background and border)
    if (fileExportData && fileExportData.length > 0) {
        for (let rowIndex = 1; rowIndex <= fileExportData.length; rowIndex++) {
        const row = worksheet.getRow(rowIndex);
            row.eachCell((cell) => {
                if (rowIndex === cellValueLength+1) {
                    if(reportTitle==null)
                    {
                        cell.fill = {
                            type: "pattern",
                            pattern: "solid",
                            fgColor: { argb: "92CDDC" },
                        };
                        cell.alignment = { vertical: "middle", horizontal: "center" };
                        cell.font = {
                            name: "Calibri",
                            size: 11,
                            bold: true,
                        };
                        cell.border = {
                            top: { style: "thin" },
                            left: { style: "thin" },
                            bottom: { style: "thin" },
                            right: { style: "thin" },
                        };
                    }
                } else {
                    cell.alignment = { wrapText: true };
                    const lengths = row.values.map((v) => v.toString().length);
                    const maxLength = Math.max(
                        ...lengths.filter((v) => typeof v === "number")
                    );
                    if (maxLength > 40) {
                        row.height = maxLength / 2;
                    }
                }
            });
        }
    }

    // freeze column and row
    worksheet.views = [
        {
            state: "frozen",
            xSplit: 2,
            ySplit: cellValueLength+1,
        },
    ];

    // auto width of column based on value length
    worksheet.columns.forEach((column) => {
        const lengths = column.values
            .filter((v) => !cellValues.includes(v)) // filter out values that are in cellValues to ignore the values to calculating widths
            .map((v) => (v !== null ? v.toString().length : 0)); // map remaining values to their lengths, handle null
        const maxLength = Math.max(
        ...lengths.filter((v) => typeof v === "number")
        );
        column.width = maxLength > 50 ? 54 : maxLength + 4;
    });

    // generate excel file with given file name
    workbook.xlsx.writeBuffer().then((data) => {
        convertToBlobAndDownload(data, sheetFileName);
    });
};

function getPayrollPrerequisiteData()
{
    var urlParams  = new URLSearchParams(window.location.search);
    var data = urlParams.get('data');
    return data;
}
// convert the passed data as blob and download the files
function convertToBlobAndDownload(data, filename) {
    const url = window.URL.createObjectURL(
        new Blob([data], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        })
    );
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
};


/* load the combo box values based on the result*/
function fnTravelDetails(selectArray) {
    var field = selectArray['field'];
    result = selectArray['result'];
    field.find('option').remove();
    field.append("<option value=''>-- Select --</option>");
    for (var x in result) {
        field.append("<option value='" + result[x]['Travel_Id'] + "' id='" + result[x]['Budget'] + "'>" + result[x]['Purpose'] + "</option>");
    }
    field.select2('val', '');
}

function exportExcelFrontEnd(loanId,linkValue,modName,filterArray,checkedColumns='',filterGroupBy='')
{
    if (linkValue == 'Tds' || linkValue === 'Payment Register' || linkValue == 'Insurance Statement'
    || linkValue == 'Ssnit Tier 1' || linkValue == 'Ssnit Tier 2' || linkValue === 'Provident Fund Detailed Report'
    || linkValue == 'Reimbursement Allowances' || linkValue == 'Hourly Wage Payslip' || linkValue == 'Monthly Master Report'
    || linkValue == 'Hourly Master Report' || linkValue == "Esi Monthly" || linkValue == "Esi Hourly"
    || linkValue == 'Esic Monthly' || linkValue == 'Esic Hourly' || linkValue == 'Attendance Summary Hourly'
    || linkValue == 'Attendance Summary Monthly' || linkValue == 'Employee Utilization' || linkValue == 'Additional Wage Summary'
    || linkValue == 'Uan Based Ecr' || linkValue == 'Uan Based Ecr Hourly'|| linkValue.toLowerCase() === 'uan based ecr(arrear)'
    || linkValue == 'Eft Monthly' || linkValue == 'Eft Hourly' || linkValue == 'Loan Amortization'
    || linkValue == 'Attendance Shortage' || linkValue == 'Employee Wise Expenses' || linkValue == 'Reimbursement'
    || linkValue === 'Pay Bill' || linkValue === 'Monthly Payslip Comprehensive' || linkValue==='Monthly Salary'
    || linkValue === 'Employee Status' || linkValue === 'Lop Recovery' || linkValue === 'Employee Step Increment'
    || linkValue.toLowerCase() == 'pt annual return(form 5a)')
    {
        document.location.href = pageUrl () + 'reports/hr-reports/export-csv/disGrid/s/loanId/'+loanId+'/linkValue/'+linkValue+'/_modName/'+modName+'/filterArray/'+filterArray+'/checkedColumns/'+checkedColumns+'/filterGroupBy/'+filterGroupBy;
    }
    else
    {
        setMask('#wholepage');
        $.ajax({
            type: 'POST',
            async: true,
            dataType: "json",
            url: pageUrl () + 'reports/hr-reports/export-csv/disGrid/s/loanId/'+loanId+'/linkValue/'+linkValue+'/_modName/'+modName+'/filterArray/'+filterArray+'/checkedColumns/'+checkedColumns+'/filterGroupBy/'+filterGroupBy,
            success: function (result) {
                isExportingAttendanceData = false;
                if(result.fileExportData && result.fileExportData.length > 0) {
                    let fileHeader = result.fileHeader;
                    let fileExportData = result.fileExportData;
                    let createdBy = result.createdBy;
                    let createdOn = result.createdOn;
                    let organizationName = result.organizationName;
                    let footerData = result.footerData;

                    let headerReference = result.headerReference;
                    let headerFormula = result.headerFormula;
                    let reportTitle = result.reportTitle;


                    exportExcelFile(fileHeader,fileExportData,linkValue,organizationName,reportTitle,createdBy,createdOn,footerData,headerReference,headerFormula,result);
                } else {
                    jAlert({ msg: 'There are no employees to export attendance data', type: 'warning' });
                }
                removeMask();
            },
            error: function (err) {
                isExportingAttendanceData = false;
                if (err.status == 200) {
                    sessionExpired();
                } else {
                    jAlert({ msg: 'Something went wrong. Please contact system admin', type: 'warning' });
                }
                removeMask();
            }
        });
    }
}