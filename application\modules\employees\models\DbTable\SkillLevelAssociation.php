<?php
//=========================================================================================
//=========================================================================================
/* Program : SkillLevelAssociation.php											         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MYSQL query for skill level association     						     *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        16-Oct-2013    Mahesh                  Initial Version                    *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Dhanabal                Changes in file for mobile app     *
 *                                                    1.Extra fields are added in        *
 *                                                    field list of list query.          */
//=========================================================================================
//=========================================================================================
class Employees_Model_DbTable_SkillLevelAssociation extends Zend_Db_Table_Abstract
{

    protected $_db = null;

    protected $_dbPersonal = null;

    protected $_ehrTables = null;

    protected $_orgDF = null;
    
    protected $_commonFunction = null;
    
    protected $_eftConfiguration = null;
    
    public function init()
    {
        $this->_ehrTables = new Application_Model_DbTable_Ehr();
        $this->_db = Zend_Registry::get('subHrapp');
        $this->_dbPersonal = new Employees_Model_DbTable_Personal();
        $this->_commonFunction = new Application_Model_DbTable_CommonFunction();
        $this->_orgDF = $this->_ehrTables->orgDateformat();
        $this->_dbFinancialYr = new Default_Model_DbTable_FinancialYear();
       
        $this->_eftConfiguration =new Organization_Model_DbTable_EftConfiguration();
    }
  
    /* Search Complaints */
    public function searchSkillLevelAssociation($page,$rows,$sortField,$sortOrder,$searchAll=null)
    {
        switch ($sortField)
        {
            default:
                case 1: $sortField = 'SD.Title'; break;
        }
        
        $qrySkillDefinition = $this->_db->select()
                                        ->from(array('SA'=>$this->_ehrTables->skillLevelAssoc),
                                            array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS SA.SkillDefinition_Id as count'),
                                                'SA.SkillDefinition_Id','SA.SkillDefinition_Id As Skill_Association_Id','SA.Description',
                                                new Zend_Db_Expr("DATE_FORMAT(SA.Added_On,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
                                                new Zend_Db_Expr("DATE_FORMAT(SA.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On"),
                                                                'DT_RowClass' => new Zend_Db_Expr('"skillLevelAssoc"'),
                                                                  'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', SA.SkillDefinition_Id)")))
        
                                        ->joinInner(array('SD'=>$this->_ehrTables->skillDefinition), 'SD.SkillDefinition_Id = SA.SkillDefinition_Id',
                                                       array('SD.Title'))
                                                  
                                        ->joinInner(array('EP'=>$this->_ehrTables->empPersonal),'SA.Added_By=EP.Employee_Id',
                                                    array(new Zend_Db_Expr("CONCAT(EP.Emp_First_Name, ' ', EP.Emp_Last_Name) as Added_By")))
                                                    
                                        ->joinLeft(array('EP1'=>$this->_ehrTables->empPersonal),'SA.Updated_By=EP1.Employee_Id',
                                                    array(new Zend_Db_Expr("CONCAT(EP1.Emp_First_Name, ' ', EP1.Emp_Last_Name) as Updated_By")))
                                                     
                                        ->order("$sortField $sortOrder")
                                        ->limit($rows, $page);
        
    	if (!empty($searchAll) && $searchAll != null)
		{
			$qrySkillDefinition->where('SD.Title Like ?', "%$searchAll%");
		}
                
        $skillDefinitionDetails = $this->_db->fetchAll($qrySkillDefinition);
                
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
                
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->skillLevelAssoc, new Zend_Db_Expr('COUNT(SkillDefinition_Id)')));
                
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $skillDefinitionDetails);
    }
    
      /* Update Complaints */
    public function updateSkillLevelAssociation($skillAssociationDetails,$sessionId,$skillAssociationId,$skillDefinitionDetails,$formName)
    {
        $qrySkillDefinition = $this->_db->select()->from($this->_ehrTables->skillLevelAssoc, new Zend_Db_Expr('count(SkillDefinition_Id)'))
                                        ->where("SkillDefinition_Id = ?",$skillAssociationDetails['SkillDefinition_Id']);


         if (!empty($skillAssociationId))
                $qrySkillDefinition->where('SkillDefinition_Id != ?', $skillAssociationDetails['SkillDefinition_Id']);

        $skillDefinitionExists = $this->_db->fetchOne($qrySkillDefinition);

        $totalWeightage = $this->getDesignationWeightage($skillDefinitionDetails);
        if($totalWeightage)
        {
            if(empty($skillDefinitionExists))
            {
                if(!empty($skillAssociationId))
                {
                    $action = 'Edit';
                    $skillAssociationDetails['Updated_On'] = date('Y-m-d H:i:s');
                    $skillAssociationDetails['Updated_By'] = $sessionId;
                    $skillAssociationDetails['Lock_Flag']  = 0;
                    $updated = $this->_db->update($this->_ehrTables->skillLevelAssoc, $skillAssociationDetails, array('SkillDefinition_Id = '.$skillAssociationDetails['SkillDefinition_Id']));
                }
                else
                {
                        $action = 'Add';
                        $skillAssociationDetails['Added_On'] = date('Y-m-d H:i:s');
                        $skillAssociationDetails['Added_By'] = $sessionId;
                        $updated =  $this->_db->insert($this->_ehrTables->skillLevelAssoc, $skillAssociationDetails);
                        $skillDefinitionId = $skillAssociationDetails['SkillDefinition_Id'];
                }
                
                return $this->updateSkillLevelDesignation($skillDefinitionDetails,$sessionId,$formName);
            }
            else
            {
                return $this->updateSkillLevelDesignation($skillDefinitionDetails,$sessionId,$formName);
            } 
        }
        else 
        {
            return array('success'=>false, 'msg'=>'Total Weightage should be less than or equal hundred for these designation', 'type'=>'info');  
        }    
    }
    
    public function getDesignationPairs($skillId,$lineItemId)
    {
      if(empty($lineItemId))
      {
          $getDesignationPairs = $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->designation,array('Designation_Id', 'Designation_Name'))
                                                    ->where('Designation_Id NOT IN ?', $this->_db->select()->from($this->_ehrTables->skillLevels,array('Designation_Id'))->where('SkillDefinition_Id = ?', $skillId))
                                                    ->where('Designation_Status =?','Active')
                                                    ->order('Designation_Name ASC'));
      
      }
      else
      {
       
        $getDesignationPairs = $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->designation,array('Designation_Id', 'Designation_Name'))
                                                    ->where('Designation_Id IN ?', $this->_db->select()->from($this->_ehrTables->skillLevels,array('Designation_Id'))->where('LineItem_Id = ?', $lineItemId))
                                                    ->where('Designation_Status =?','Active')
                                                    ->order('Designation_Name ASC'));
      }
      return $getDesignationPairs;
    }
    
    public function getExpectedSkillLevel($employeeId,$skillId)
    {
        $getDesignationPairs =$this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->skillLevels,array('Expected_Skill_Level'))
                                                    ->where('SkillDefinition_Id = ?', $skillId)
                                                    ->where('Designation_Id = ?',$this->_db->select()->from($this->_ehrTables->empJob,array('Designation_Id'))->where('Employee_Id = ?', $employeeId)));
    
       return $getDesignationPairs;
    }
    
    
    public function deleteSkillLevelAssociation($skillDefinitionId, $logEmpId,$customFormName)
    {
         $ckSkillLevel = $this->_db->fetchOne($this->_db->select()->from(array('pLevel'=>$this->_ehrTables->performanceLevel), new Zend_Db_Expr('COUNT(Performance_Id)'))
                       ->where('pLevel.SkillDefinition_Id = ?', $skillDefinitionId));
       
        if (!empty($skillDefinitionId) && $ckSkillLevel == 0)
        {
      	    $skillDefinitionLock = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->skillLevelAssoc, array('Lock_Flag', 'SkillDefinition_Id'))
                                         ->where('SkillDefinition_Id = ?', $skillDefinitionId));
			
           if ($skillDefinitionLock['Lock_Flag'] == 0)
           {
                     $deleted  = $this->_db->delete($this->_ehrTables->skillLevelAssoc, 'SkillDefinition_Id='.(int)$skillDefinitionId);
                     $deleted1 = $this->_db->delete($this->_ehrTables->skillLevels, 'SkillDefinition_Id='.(int)$skillDefinitionId);
         
                     return $this->_commonFunction->deleteRecord (array('deleted' => $deleted,
                                                                        'tableName'      => $this->_ehrTables->skillLevelAssoc,
                                                                        'lockFlag'       => $skillDefinitionLock['Lock_Flag'],
                                                                        'formName'       => $customFormName,
                                                                        'trackingColumn' => $skillDefinitionLock['SkillDefinition_Id'],
                                                                        'sessionId'      => $logEmpId));
           }
        }
        else
        {
            return array('success'=>false, 'msg'=>'Unable to delete '.$customFormName.'. Please, contact system admin', 'type'=>'info');
        }
    } 
    
    
     public function searchSkillLevelDesignation($skillDefinitionId)
    {
        $qrySkillDefinition = $this->_db->select()->from(array('SL'=>$this->_ehrTables->skillLevels),
                                                    array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS SL.LineItem_Id as count'),
                                                                  'SL.LineItem_Id','SL.SkillDefinition_Id','SL.Designation_Id','SL.Expected_Skill_Level','SL.Weightage',
                                                                  'DT_RowClass' => new Zend_Db_Expr('"skillLevels"'),
                                                                  'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', SL.LineItem_Id)")))
        
                                                    ->joinInner(array('D'=>$this->_ehrTables->designation), 'D.Designation_Id = SL.Designation_Id',
                                                               array('D.Designation_Name'))
                                                  
                                                    ->where('SL.SkillDefinition_Id = ?', $skillDefinitionId);
 
        	
        $skillDefinitionDetails = $this->_db->fetchAll($qrySkillDefinition);
                
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
                
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->skillLevels, new Zend_Db_Expr('COUNT(LineItem_Id)'))->where('SkillDefinition_Id = ?', $skillDefinitionId));
                
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $skillDefinitionDetails);
    }
    
    
    /* Update Skill Level Designation */
    public function updateSkillLevelDesignation($skillDesignationDetails,$sessionId,$formName)
    {
        $totalWeightage = $this->getDesignationWeightage($skillDesignationDetails);
        if($totalWeightage)
        {
            if(!empty($skillDesignationDetails['LineItem_Id']))
            {
                    $action = 'Edit';
                    $skillDesignationDetails['Designation_Id']=$skillDesignationDetails['Designation_Id'][0];
                    $updated = $this->_db->update($this->_ehrTables->skillLevels, $skillDesignationDetails, array('LineItem_Id = '.$skillDesignationDetails['LineItem_Id']));
                    $lineItemId = $skillDesignationDetails['LineItem_Id'];
                    $updated =1;
            }
            else
            {
                $bulkDesignationDetails = array();
                $updateSkillDesignationDetails = array();
                for($i=0;$i<count($skillDesignationDetails['Designation_Id']);$i++)
                {
                    $bulkDesignationDetails[$i]['LineItem_Id'] = $skillDesignationDetails['LineItem_Id'];
                    $bulkDesignationDetails[$i]['Designation_Id'] = $skillDesignationDetails['Designation_Id'][$i];
                    $bulkDesignationDetails[$i]['Weightage'] = $skillDesignationDetails['Weightage'];
                    $bulkDesignationDetails[$i]['Expected_Skill_Level'] = $skillDesignationDetails['Expected_Skill_Level'];
                    $bulkDesignationDetails[$i]['SkillDefinition_Id'] = $skillDesignationDetails['SkillDefinition_Id'];
                    array_push($updateSkillDesignationDetails,$bulkDesignationDetails[$i]);
                }
                $action = 'Add';
                if(!empty($updateSkillDesignationDetails))
                    $updated =  $this->_ehrTables->insertMultiple($this->_ehrTables->skillLevels, $updateSkillDesignationDetails);
                else 
                    $updated =0;

                $lineItemId = $this->_db->lastInsertId();
            }
            return $this->_commonFunction->updateResult (array('updated'    => $updated,
                                                                'action'         => $action,
                                                                'trackingColumn' => $lineItemId,
                                                                'formName'       => $formName,
                                                                'sessionId'      => $sessionId,
                                                                'tableName'      => $this->_ehrTables->skillLevels));
        }
        else 
        {
            return array('success'=>false, 'msg'=>'Total Weightage should be less than or equal hundred for these designation', 'type'=>'info');      
        }                                                        
    }

    public function getDesignationWeightage($skillDesignationDetails)
    {
        for($i=0;$i<count($skillDesignationDetails['Designation_Id']);$i++)
        {
            $totalWeightage = 0; 
            $weightageQry = $this->_db->select()->from(array('SL'=>$this->_ehrTables->skillLevels),array(new Zend_Db_Expr('sum(Weightage) as Weightage')))
                                                                            ->where('SL.Designation_Id = ?', $skillDesignationDetails['Designation_Id'][$i]);

            if(!empty($skillDesignationDetails['LineItem_Id']))
                $weightageQry->where('LineItem_Id != ?', $skillDesignationDetails['LineItem_Id']);

            $weightage = $this->_db->fetchOne($weightageQry);                                                            
            
            if(empty($weightage))
            {
                $weightage = 0;
            }
            $totalWeightage = $weightage+$skillDesignationDetails['Weightage'];

            if($totalWeightage > 100)
                return false;
        }
        
        return true;
    }

    public function getPerformanceManagementSettings()
    {
        $performanceManagementSettings =  $this->_db->fetchRow($this->_db->select()->from(array('RS'=>$this->_ehrTables->performanceManagementSettings),array('Performance_Management_Mode','Maximum_Rating')));

        $performanceManagementSettings['Minimum_Rating'] = 1;

        return $performanceManagementSettings;
    }

    public function deleteSkillLevelDesignation($lineItemId,$logEmpId,$formName)
    {
        if (!empty($lineItemId))
        {
            $deleted = $this->_db->delete($this->_ehrTables->skillLevels, 'LineItem_Id='.(int)$lineItemId);
            return $this->_commonFunction->deleteRecord (array( 'deleted'        => $deleted,
                                                                'tableName'      => $this->_ehrTables->skillLevels,
                                                                'lockFlag'       => 0,
                                                                'formName'       => $formName,
                                                                'trackingColumn' => $lineItemId,
                                                                'sessionId'      => $logEmpId));
		  
        }
        else
        {
            return array('success'=>false, 'msg'=>'Unable to delete '.$formName.'. Please, contact system admin', 'type'=>'info');
        }
    }
    
    public function getAssessmentMonth($employeeId, $appliedMonth)
    {
 	     $deductionMonth = array();
	     //$maxDeductionEffectiveDate = $appliedMonth;
		
        $maxDeductionEffectiveDate = date('Y-m', strtotime($appliedMonth));
					
        
		//$deductionMonth['Min']=date('Y-m', strtotime('+1 month', strtotime($maxDeductionEffectiveDate)));
        $deductionMonth['Min'] = date('Y-m',strtotime($maxDeductionEffectiveDate));
        $currentFiscalYear = $this->_dbFinancialYr->financialStartYear(date('n'),date('Y'));
        $currentFiscalYear = $currentFiscalYear+1;
        $financialMonthRange = $this->_dbFinancialYr->financialYr();
        $deductionEndMonth = $currentFiscalYear.'-'.end($financialMonthRange);
        $deductionMonth['Max'] = date('Y-m',strtotime($deductionEndMonth));
		
        return $deductionMonth;
	
    }

    public function __destruct()
    {
        
    }	


}

