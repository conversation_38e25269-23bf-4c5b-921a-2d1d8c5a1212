<?php
//=========================================================================================
//=========================================================================================
/* Program : Prerequisite.php															         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2017 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MYSQL Query to retrive, add and update monthly salary and houry wages   *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                 Description                       	 *
 *  0.1        10-Oct-2017    Jayanthi               Initial Version         	         */
//=========================================================================================
//=========================================================================================
class Payroll_Model_DbTable_Prerequisite extends Zend_Db_Table_Abstract
{
	protected $_db          = null;
	protected $_ehrTables   = null;
	protected $_dbPayslip   = null;
	protected $_dbPersonal  = null;
	protected $_dbOrgDetail = null;
	protected $_orgDF       = null;
	protected $_dbCommonFun = null;

	public function init()
	{
		$this->_ehrTables   = new Application_Model_DbTable_Ehr();
		$this->_dbPersonal  = new Employees_Model_DbTable_Personal();
		$this->_dbPayslip  = new Payroll_Model_DbTable_Payslip();
		$this->_dbOrgDetail = new Organization_Model_DbTable_OrgSettings();
		$this->_dbPayslip   = new Payroll_Model_DbTable_Payslip();
		$this->_dbCommonFun = new Application_Model_DbTable_CommonFunction();
		$this->_db          = Zend_Registry::get('subHrapp');
		$this->_orgDF       = $this->_ehrTables->orgDateformat();
		$this->_dbFinancialYr = new Default_Model_DbTable_FinancialYear();
		$this->_dbSalary = new Payroll_Model_DbTable_Salary();
		
		if (Zend_Registry::isRegistered('orgDetails'))
             $this->_orgDetails = Zend_Registry::get('orgDetails');
	}

	/** if dept id is parent then get its child also else pass dept Id**/
	public function getDepartmentDetails($empdept)
	{
		$deptChildArr = array();
		foreach($empdept as $empdep)
		{
			$IsDeptParentId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->dept,array('Parent_Type_Id'))
			->where('Department_Id = ?',$empdep));

			if(empty($IsDeptParentId))
			{
				/** get all the child department ids for the  parent department **/
				$getDepChildIds = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->dept,
																   array('Department_Id'))
															->where('Parent_Type_Id = ?',$empdep));
				
				/** If child department exists **/
				if(!empty($getDepChildIds))
				{
					/** include parent type id **/
					array_push($getDepChildIds,$empdep);
					foreach($getDepChildIds as $getDepChildIdsCheck)
					{
						if(!in_array(  $getDepChildIdsCheck,  $deptChildArr)){

							array_push($deptChildArr,$getDepChildIdsCheck);
							}
					}
				}
				else	/** If child department doesnot exists **/
				{
					/** Parent department Id it self assigned **/	
					
					if(!in_array(  $empdep,  $deptChildArr)){	
					array_push($deptChildArr,$empdep);
					}
				}
			}
			else
			{
			    $samp=	$empdep;
				if(!in_array(  $samp,  $deptChildArr)){
				array_push($deptChildArr,$samp);
				}
			}
			
		}

		return $deptChildArr;
	}

	public function getPayslipGenerateEmployeeIds($payslipMonth,$employeeIds)
	{
		$qryMonthlyEmp = $this->_db->select()->from($this->_ehrTables->monthlyPayslip, 'Employee_Id')
											->where('Salary_Month = ?', $payslipMonth)
											->where('Employee_Id IN (?)',$employeeIds);
		$employeeIds = $this->_db->fetchCol($qryMonthlyEmp);
		return $employeeIds;
	}
	
	
	/**
	 * get pending approvals count
	 */
	public function getApprovalsCount($payslipMonth,$empLocation,$empdept,$empType, $empIds, $currentAssessmentYear,$payPeriod)
	{
		$payslipMonthInactiveEmployees = array();
		$month = explode(',', $payslipMonth);
		$paycyleDate =  $this->_dbPayslip->getSalaryDay($month[0], $month[1],'',$payPeriod);
		
		#If the Consider cutoff day for attendance and timeoff is enabled, then attendance and timeoff should be considered based on the cutoff day
		$paycyleDateWithCutoff =  $this->_dbPayslip->getSalaryDay($month[0], $month[1], 31,$payPeriod);
		
		$currentdate = date('Y-m-1');
		$payslipdate = date($month[1].'-'.$month[0].'-1');
		$dbSalary = new Payroll_Model_DbTable_Salary();

		if(empty($empIds)){
			/** To get employees based on the choosed fields in salary payslip form **/ 
			$arrayEmployee = $dbSalary->monthlySalaryEmployee(array(), $empLocation, $empdept, $empType, $paycyleDate['Salary_Date'], $paycyleDate['Last_SalaryDate']);
		}else{
			$arrayEmployee = $empIds;
		}

		$approvalsCount = array();
		$approvalsCountEmpId = array();

		$arrayEmployeeCount = count($arrayEmployee);
		if($arrayEmployeeCount > 0)
		{
			$payslipGeneratedEmployeeIds = $this->getPayslipGenerateEmployeeIds($payslipMonth,$arrayEmployee);
			if(!empty($payslipGeneratedEmployeeIds))
			{
				$arrayEmployee = array_diff($arrayEmployee, $payslipGeneratedEmployeeIds);
			}
			$arrayEmployeeCount = count($arrayEmployee);
		}


		$payslipNotGeneratedEmployeeCount = 0; 
		$payslipEmployeeDetails = array();
		if($arrayEmployeeCount > 0)
		{
			$payslipEmployeeDetails 		  		= $this->getPayslipEmployeeDetails($payslipMonth,$arrayEmployee,$payPeriod);
			$arrayEmployee 					  		= $payslipEmployeeDetails['salaryEmployeeIds'];
			$futureMonthPayslipGeneratedEmployeeIds = $this->_dbPayslip->getFutureMonthPayslipGeneratedEmployeeIds($payslipMonth);
			if(!empty($futureMonthPayslipGeneratedEmployeeIds))
			{
				$arrayEmployee = array_diff($arrayEmployee,$futureMonthPayslipGeneratedEmployeeIds);
			}
			$payslipNotGeneratedEmployeeCount = count($arrayEmployee);
		}

		if($payslipNotGeneratedEmployeeCount > 0)
		{
			$dbFinancialYear = new Default_Model_DbTable_FinancialYear();
			$financialstartEndDates = $dbFinancialYear->fiscalStartEndDate('PHP');
			/** To fetch employees count in pending approvals based on the choosed fields employees in salary payslip form **/ 
			$approvalsDetails[0] = $this->getAdvanceSalaryCount($paycyleDate, $arrayEmployee);
			$approvalsDetails[1] = $this->getBonusCount($paycyleDate, $arrayEmployee);
			$approvalsDetails[2] = $this->getCommissionCount($paycyleDate, $arrayEmployee);
			$approvalsDetails[3] = $this->getDeductionCount($paycyleDate, $arrayEmployee);
			$approvalsDetails[4] = $this->getLoanCount($paycyleDate, $arrayEmployee);
			$approvalsDetails[5] = $this->getReimbursementCount($paycyleDate, $arrayEmployee);
			$approvalsDetails[6] = $this->getResignationCount($paycyleDate, $arrayEmployee);
			$approvalsDetails[7] = $this->getShiftCount($paycyleDateWithCutoff, $arrayEmployee);
			$approvalsDetails[8] = $this->getTaxDeclarationCount($payslipEmployeeDetails);
			$approvalsDetails[9] = $this->getAttendanceCount($paycyleDateWithCutoff, $arrayEmployee);
			$approvalsDetails[10]= $this->getLeaveCount($payslipMonth, $arrayEmployee, $paycyleDateWithCutoff);
			$approvalsDetails[11]= $this->getSalaryRecalculationCount( $arrayEmployee);
			$approvalsDetails[12]= $this->getFinancialClosure($payslipMonth);
			$approvalsDetails[14] =  $this->getAssetManagement($payslipEmployeeDetails);
			$approvalsDetails[15]= $this->getHraDeclarationCount($payslipEmployeeDetails);
			$approvalsDetails[16]= $this->getCompOffCount($paycyleDateWithCutoff, $arrayEmployee);
			$approvalsDetails[17]= $this->getdeferredLoanCount($paycyleDate, $arrayEmployee);
			$approvalsDetails[18] = $this->getShiftNotScheduledDays($payslipEmployeeDetails,$paycyleDateWithCutoff);
			
			$approvalsDetails[19]= array();

			$dbLeave     = new Employees_Model_DbTable_Leave();
			$approvalsDetails[20] = $dbLeave->getLeaveClosureExist($paycyleDate['Salary_Date']);
	
			/** Get the income under section24 pending approval records count */
			$approvalsDetails[21] = $this->getHousePropertyCount($payslipEmployeeDetails);
			$approvalsDetails[22] = $this->getAdditionalWageClaimCount($paycyleDate, $arrayEmployee);
			$approvalsDetails[23] = $this->getProbationEmployeeDetails($paycyleDate,$arrayEmployee,$payslipEmployeeDetails);
			$approvalsDetails[24] = $this->getFFEmployeeDetails($payslipMonth,$payslipEmployeeDetails);
			$approvalsDetails[25] = $this->getLOPRecoveryEmployeeDetails($payslipMonth,$arrayEmployee);
			$approvalsDetails[26] = $this->getTaxReliefCount($payslipEmployeeDetails);
			$approvalsDetails[27] = $this->getShortTimeOffCount($paycyleDate,$arrayEmployee);
			$approvalsDetails[28] = $this->getEarlyCheckoutCount($paycyleDateWithCutoff,$arrayEmployee);

			$approvalsCount[0] = $approvalsDetails[0]['Count'];
			$approvalsCount[1] = $approvalsDetails[1]['Count'];
			$approvalsCount[2] = $approvalsDetails[2]['Count'];
			$approvalsCount[3] = $approvalsDetails[3]['Count'];
			$approvalsCount[4] = $approvalsDetails[4]['Count'];
			$approvalsCount[5] = $approvalsDetails[5]['Count'];
			$approvalsCount[6] = $approvalsDetails[6]['Count'];
			$approvalsCount[7] = $approvalsDetails[7]['Count'];
			$approvalsCount[8] = $approvalsDetails[8]['Count'];
			$approvalsCount[9] = $approvalsDetails[9]['Count'];
			$approvalsCount[10]= $approvalsDetails[10]['Count'];
			$approvalsCount[11]= $approvalsDetails[11]['Count'];
			$approvalsCount[12]= $approvalsDetails[12]['Count'];
			$approvalsCount[13]= $this->getAttendanceEnforcePayment($payslipEmployeeDetails,$paycyleDateWithCutoff);
			$approvalsCount[14]= $approvalsDetails[14]['Count']; 
			$approvalsCount[15]= $approvalsDetails[15]['Count'];
			$approvalsCount[16]= $approvalsDetails[16]['Count'];
			$approvalsCount[17]= $approvalsDetails[17]['Count'];
			$approvalsCount[18]= $approvalsDetails[18]['Count'];
			$approvalsCount[19] = $arrayEmployee;
			
			/** Check leave closure or leave encashment has to be run or not */
			$approvalsCount[20] = 0;
			if(!empty($approvalsDetails[20])){	
					$approvalsCount[20] = 1;
			}
			
			$approvalsCount[21]= $approvalsDetails[21]['Count'];
			$approvalsCount[22]= $approvalsDetails[22]['Count'];
			$approvalsCount[23]= $approvalsDetails[23];
			$approvalsCount[24]= $approvalsDetails[24]['Count'];
			$approvalsCount[25] = $approvalsDetails[25]['Count'];
			$approvalsCount[26] = $approvalsDetails[26]['Count'];
			$approvalsCount[27] = $approvalsDetails[27]['Count'];
			$approvalsCount[28] = $approvalsDetails[28]['Count'];

			// to fetch pending approval employee ids
			if($approvalsCount[0] > 0){
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[0]['EmployeeIds']);
			}
			if($approvalsCount[1] > 0){
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[1]['EmployeeIds']) ;
			}
			if($approvalsCount[2] > 0){
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[2]['EmployeeIds']) ;
			}
			if($approvalsCount[3] > 0){
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[3]['EmployeeIds']) ;
			}
			if($approvalsCount[4] > 0){
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[4]['EmployeeIds']) ;
			}
			if($approvalsCount[5] > 0){
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[5]['EmployeeIds']) ;
			}
			if($approvalsCount[6] > 0){
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[6]['EmployeeIds']) ;
			}
			if($approvalsCount[7] > 0){
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[7]['EmployeeIds']) ;
			}
			if($approvalsCount[8] > 0){
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[8]['EmployeeIds']) ;
			}
			if($approvalsCount[9] > 0){
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[9]['EmployeeIds']) ;
			}
			if($approvalsCount[10] > 0){
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[10]['EmployeeIds']) ;
			}
			if($approvalsCount[11] > 0){
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[11]['EmployeeIds']) ;
			}
			if($approvalsCount[12] > 0){
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[12]['EmployeeIds']);
			}
			
			if($approvalsCount[14] > 0){
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[14]['EmployeeIds']) ;
			}

			if($approvalsCount[15] > 0){
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[15]['EmployeeIds']) ;
			}
			if($approvalsCount[16] > 0){
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[16]['EmployeeIds']) ;
			}
			if($approvalsCount[17] > 0){
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[17]['EmployeeIds']) ;
			}
			if($approvalsCount[18] > 0){
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[18]['EmployeeIds']) ;
			}
			if($approvalsCount[20] > 0){
				/** Push all the payslip employees in the approval count if the leave closure is not run */
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $arrayEmployee);
			}
			if($approvalsCount[21] > 0){
				/** Push all the income under section pending approval ids */
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[21]['EmployeeIds']) ;
			}
			if($approvalsCount[22] > 0){
				/** Push all the overtime claim pending approval ids */
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[22]['EmployeeIds']) ;
			}
			if($approvalsDetails[24] > 0){
				/** Push all the final settlement pending approval ids */
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[24]['EmployeeIds']) ;
			}
			if($approvalsDetails[25] > 0){
				/** Push all the final settlement pending approval ids */
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[25]['EmployeeIds']) ;
			}
			if($approvalsCount[26] > 0){
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[26]['EmployeeIds']) ;
			}
			if($approvalsCount[27] > 0){
				//Push all the short-time-off employee ids
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[27]['EmployeeIds']);
			}
			if($approvalsCount[28] > 0){
				//Push all the early checkout employee ids
				$approvalsCountEmpId = array_merge($approvalsCountEmpId, $approvalsDetails[28]['EmployeeIds']);
			}
		}
		else
		{
			$approvalsCount[0] =$approvalsCount[1] =$approvalsCount[2] =$approvalsCount[3] =$approvalsCount[4] =0;
			$approvalsCount[5] =$approvalsCount[6] =$approvalsCount[7] =$approvalsCount[8] =$approvalsCount[9] =0;
			$approvalsCount[10] =$approvalsCount[11] =$approvalsCount[12] =$approvalsCount[13] =$approvalsCount[14] =$approvalsCount[15] = $approvalsCount[16]=0;
			$approvalsCount[17] = $approvalsCount[18] = $approvalsCount[19] = $approvalsCount[20] = $approvalsCount[21] = $approvalsCount[22] = $approvalsCount[23] = 0;
			$approvalsCount[24] = $approvalsCount[26] = $approvalsCount[27] = $approvalsCount[28] = 0;
		}
		
		if($approvalsCount[13] != 0 )
		{
			if(count($approvalsCount[13])>0){
				/* Fetch all the employee id from the approvalsCount[13] array */
				$attEnforcedEmpId = array_column($approvalsCount[13], 'Employee_Id');

				$approvalsCountEmpId = array_merge($approvalsCountEmpId, array_unique($attEnforcedEmpId));
			}
		}
		
		
		if(!empty($approvalsCount[23])){
			$probationEmployeeDetails = $approvalsDetails[23];
			$probationEmployeeCount = count($probationEmployeeDetails);
			$probationEmployeeId = array();
			for($i=0;$i<$probationEmployeeCount;$i++)
			{
				array_push($probationEmployeeId, $probationEmployeeDetails[$i]['Employee_Id']);
			}
			$approvalsCountEmpId = array_merge($approvalsCountEmpId, array_unique($probationEmployeeId));
		}

		if($approvalsCount[8] != 0 ){
			$approvalsCount[8] = $approvalsDetails[8]['taxDeclarationDetails'];
		}

		if($approvalsCount[14] != 0 )
		{
			$approvalsCount[14] = $approvalsDetails[14]['employeeAssetDetails'];
		}

		if($approvalsCount[15] != 0 ){
			$approvalsCount[15] = $approvalsDetails[15]['hraDeclarationDetails'];
		}

		if($approvalsCount[18] != 0 )
		{
			$approvalsCount[18] = $approvalsDetails[18]['unScheduledShiftDetails'];
		}

		if($approvalsCount[21] != 0 ){
			$approvalsCount[21] = $approvalsDetails[21]['incomeUnderSection24Details'];
		}

		if($approvalsCount[24] != 0 ){
			$approvalsCount[24] = $approvalsDetails[24]['ffEmployeeDetails'];
		}

		if($approvalsCount[26] != 0 ){
			$approvalsCount[26] = $approvalsDetails[26]['taxReliefDeclarationDetails'];
		}
		return array("approvalsCount"=>$approvalsCount,"approvalEmpIds" => $approvalsCountEmpId);		
	}
	
	/**
	 * get pending approvals of advance salary
	 */
	public function getAdvanceSalaryCount($paycyleDate, $arrayEmployee)
	{
		$qryAdvanceSalaryEmpId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->advanceSalary, 'Employee_Id')
		->where('Approval_Status LIKE ?', 'Applied')
		->where(new Zend_Db_Expr('DATE(Submission_Date)') ." >= ?", $paycyleDate['Prev_CutoffDate'])
		->where(new Zend_Db_Expr('DATE(Submission_Date)') ." <= ?", $paycyleDate['Cutoff_Date'])
		->where('Employee_Id IN (?)',$arrayEmployee));
		
		return array("Count" => count($qryAdvanceSalaryEmpId), "EmployeeIds" =>$qryAdvanceSalaryEmpId);

	}

	/**
	 * get pending approvals of additional wage claim
	 */
	public function getAdditionalWageClaimCount($paycyleDate, $arrayEmployee)
	{
		$overtimeSettings = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->overtimeSettings, array('Comp_Off_Applicable_For_Overtime','Shift_Allowance_Applicable_For_Overtime')));

		/*When the compoff  is applicable or shift allowance applicable for overtime then only we need to check the additional wage claim exist or not*/
		if($overtimeSettings['Comp_Off_Applicable_For_Overtime']==1 || $overtimeSettings['Shift_Allowance_Applicable_For_Overtime']==1 )
		{
			$overtimeClaimEmployeeId = $this->_db->fetchCol($this->_db->select()->from(array('OC'=>$this->_ehrTables->overtimeClaims),'Employee_Id')
											->where('OC.Employee_Id IN (?)',$arrayEmployee)
											->where("OC.Approval_Status = 'Applied' or OC.Approval_Status = 'Returned' or OC.Approval_Status = 'Approved'")
											->where(new Zend_Db_Expr('DATE(OC.Start_Date_Time)') ." >= ?", $paycyleDate['Prev_CutoffDate'])
											->where(new Zend_Db_Expr('DATE(OC.Start_Date_Time)') ." <= ?", $paycyleDate['Cutoff_Date']));
		}
		else 
		{
			$overtimeClaimEmployeeId = array();
		}									
	
		$overtimeClaimCount = count($overtimeClaimEmployeeId);

		return array("Count" => $overtimeClaimCount, "EmployeeIds" =>$overtimeClaimEmployeeId);
	}
	
	/**
	 * get probation employee for given payroll month
	 */
    public function getProbationEmployeeDetails($paycyleDate,$arrayEmployee,$payslipEmployeeDetails)
    {
		if(!empty($payslipEmployeeDetails['resignedEmployeeIds']))
		{
			$validEmployeeIds=array_diff($arrayEmployee,$payslipEmployeeDetails['resignedEmployeeIds']);
		}
		else
		{
			$validEmployeeIds=$arrayEmployee;
		}

		$validEmployeeIds = array_filter($validEmployeeIds);

		if(!empty($validEmployeeIds))
		{
			$probationEmployeeDetails = $this->_db->fetchAll($this->_db->select()->from(array('EP'=>$this->_ehrTables->empPersonal), array('EP.Employee_Id','Employee_Name'=>new Zend_Db_Expr("CONCAT(EP.Emp_First_Name,' ',EP.Emp_Last_Name)"),
			new Zend_Db_Expr("DATE_FORMAT(EJ.Probation_Date,'".$this->_orgDF['sql']."') as Probation_Date")))
			->joinInner(array('EJ'=>$this->_ehrTables->empJob), 'EP.Employee_Id=EJ.Employee_Id', array(''))
			->where('EJ.Probation_Date >= ?', $paycyleDate['Salary_Date'])
			->where('EJ.Probation_Date <= ?', $paycyleDate['Last_SalaryDate'])
			->where('EJ.Confirmed = ?',0)
			->where('EJ.Emp_Status=?','Active')
			->where('EJ.Employee_Id IN (?)',$validEmployeeIds));
		}
		else
		{
           $probationEmployeeDetails = array();
		}  
		return $probationEmployeeDetails;
	}
	/**
	 * get pending approvals of bonus
	 */
	public function getBonusCount($paycyleDate, $arrayEmployee)
	{
		$qryBonusEmpId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empBonus, 'Employee_Id')
					->where("Approval_Status = 'Pending Approval' or Approval_Status = 'In Process'")
					->where(new Zend_Db_Expr('DATE(Bonus_Date)') ." >= ?", $paycyleDate['Prev_CutoffDate'])
					->where(new Zend_Db_Expr('DATE(Bonus_Date)') ." <= ?", $paycyleDate['Cutoff_Date'])
					->where('Employee_Id IN (?)',$arrayEmployee));

		return array("Count" => count($qryBonusEmpId), "EmployeeIds" =>$qryBonusEmpId);

	}
	
	/**
	 * get pending approvals of commission
	 */
	public function getCommissionCount($paycyleDate, $arrayEmployee)
	{
		$qryCommissionsEmpId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->commission, 'Employee_Id')
			->where('Commission_Status LIKE ?', 'Applied')
			->where(new Zend_Db_Expr('DATE(Submission_Date)') ." >= ?", $paycyleDate['Prev_CutoffDate'])
			->where(new Zend_Db_Expr('DATE(Submission_Date)') ." <= ?", $paycyleDate['Cutoff_Date'])
			->where('Employee_Id IN (?)',$arrayEmployee));

		return array("Count" => count($qryCommissionsEmpId), "EmployeeIds" =>$qryCommissionsEmpId);

	}
	
	/**
	 * get pending approvals of Deduction
	 */
	public function getDeductionCount($paycyleDate, $arrayEmployee)
	{
		$qryDeductionEmpId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->deductions, 'Employee_Id')
						->where("Approval_Status = 'Applied'")
						->where(new Zend_Db_Expr('DATE(Effective_Date)') ." >= ?", $paycyleDate['Prev_CutoffDate'])
						->where(new Zend_Db_Expr('DATE(Effective_Date)') ." <= ?", $paycyleDate['Cutoff_Date'])
						->where('Employee_Id IN (?)',$arrayEmployee));
	
		return array("Count" => count($qryDeductionEmpId), "EmployeeIds" =>$qryDeductionEmpId);
	}
	
	/**
	 * get pending approvals of loan
	 */
	public function getLoanCount($paycyleDate,$arrayEmployee)
	{
		$qryLoanEmpId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empLoan, 'Employee_Id')
		->where("Approval_Status IN (?)", array('Pending Approval','In Process'))
		->where(new Zend_Db_Expr('DATE(Submission_Date)') ." >= ?", $paycyleDate['Prev_CutoffDate'])
		->where(new Zend_Db_Expr('DATE(Submission_Date)') ." <= ?", $paycyleDate['Cutoff_Date'])
		->where('Employee_Id IN (?)',$arrayEmployee));

		return array("Count" => count($qryLoanEmpId), "EmployeeIds" =>$qryLoanEmpId);
	}


	/**
	 * get pending approvals of deferred loan
	 */
	public function getdeferredLoanCount($paycyleDate,$arrayEmployee)
	{
		$qryDeferredLoanEmpId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->deferredLoan, 'Employee_Id')
		->where('Defer_Date >= ?', $paycyleDate['Salary_Date'])
		->where('Defer_Date <= ?', $paycyleDate['Last_SalaryDate'])
		->where("Defer_Status LIKE 'Applied'")
		->where('Employee_Id IN (?)',$arrayEmployee));

		return array("Count" => count($qryDeferredLoanEmpId), "EmployeeIds" =>$qryDeferredLoanEmpId);

	}
	
	/**
	 * get pending approvals of reimbursement
	 */
	public function getReimbursementCount($paycyleDate,$arrayEmployee)
	{
		/* If the reimbursement is settled to the employees inside payroll then reimbursement should be approved
		in the salary month itself */
		if($this->_orgDetails['Display_Reimbursement_Payslip'] == 1){
			$qryReimbursementEmpId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->reimbursement, 'Employee_Id')
			->where("Approval_Status IN (?)", array('Pending Approval','In Process'))
			->where(new Zend_Db_Expr('DATE(Submission_Date)') ." >= ?", $paycyleDate['Prev_CutoffDate'])
			->where(new Zend_Db_Expr('DATE(Submission_Date)') ." <= ?", $paycyleDate['Cutoff_Date'])
			->group('Request_Id')
			->where('Employee_Id IN (?)',$arrayEmployee));
	
			return array("Count" => count($qryReimbursementEmpId), "EmployeeIds" =>$qryReimbursementEmpId);

		}else{
			return array("Count" => 0, "EmployeeIds" =>array());
		}
	}
	
	/**
	 * get pending approvals of resignation
	 */
	public function getResignationCount($paycyleDate,$arrayEmployee)
	{
		$qryResignationEmpId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->resignation, 'Employee_Id')
		->where('Resignation_Date >= ?', $paycyleDate['Salary_Date'])
		->where('Resignation_Date <= ?', $paycyleDate['Last_SalaryDate'])
		->where("Approval_Status LIKE 'Applied'")
		->where('Employee_Id IN (?)',$arrayEmployee));
		return array("Count" => count($qryResignationEmpId), "EmployeeIds" =>$qryResignationEmpId);
	}
	
	/**
	 * get pending approvals of shift
	 */
	public function getShiftCount($paycyleDate,$arrayEmployee)
	{
		$qryShiftEmpId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empShift, 'Employee_Id')
		->where("Approval_Status IN (?)", array('Pending Approval','In Process'))
		->where(new Zend_Db_Expr('DATE(Submission_Date)') ." >= ?", $paycyleDate['Prev_CutoffDate'])
		->where(new Zend_Db_Expr('DATE(Submission_Date)') ." <= ?", $paycyleDate['Cutoff_Date'])
		->group('Request_Id')
		->where('Employee_Id IN (?)',$arrayEmployee));

		return array("Count" => count($qryShiftEmpId), "EmployeeIds" =>$qryShiftEmpId);

	}
	
	/**
	 * get pending approvals of Tax Declaration
	 */
	public function getTaxDeclarationCount($payslipEmployeeDetails)
	{
		$taxDeclarationEmployeeIds = array();
		if(!empty($payslipEmployeeDetails['resignedFinanicalClosureEmployeeIds']))
		{
			$taxDeclarationEmployeeIds = $this->getTaxDeclarationEmployeeIds($payslipEmployeeDetails['resignedFinanicalClosureEmployeeIds']);
		}

		$taxDeclarationDetails = $this->getEmployeePersonalInfo($payslipEmployeeDetails,$taxDeclarationEmployeeIds);
		return array("Count" => count($taxDeclarationEmployeeIds), "EmployeeIds" =>$taxDeclarationEmployeeIds,'taxDeclarationDetails'=>$taxDeclarationDetails);	
	}

	
	
		/**
	 * get pending approvals of Tax Declaration
	 */
	public function getHraDeclarationCount($payslipEmployeeDetails)
	{
		$hraDeclarationEmployeeIds = array();
		if(!empty($payslipEmployeeDetails['resignedFinanicalClosureEmployeeIds']))
		{
			$hraDeclarationEmployeeIds = $this->getHraDeclarationEmployeeIds($payslipEmployeeDetails['resignedFinanicalClosureEmployeeIds']);
		}
		
		$hraDeclarationDetails = $this->getEmployeePersonalInfo($payslipEmployeeDetails,$hraDeclarationEmployeeIds);
		return array("Count" => count($hraDeclarationEmployeeIds), "EmployeeIds" =>$hraDeclarationEmployeeIds,'hraDeclarationDetails'=>$hraDeclarationDetails);
	}

	public function getHousePropertyCount($payslipEmployeeDetails)
	{
		$incomeUnderSection24EmployeeIds = array();
		if(!empty($payslipEmployeeDetails['resignedFinanicalClosureEmployeeIds']))
		{
			$incomeUnderSection24EmployeeIds = $this->getIncomeUnderSection24EmployeeIds($payslipEmployeeDetails['resignedFinanicalClosureEmployeeIds']);
		}
		$incomeUnderSection24Details = $this->getEmployeePersonalInfo($payslipEmployeeDetails,$incomeUnderSection24EmployeeIds);
		return array("Count" => count($incomeUnderSection24EmployeeIds), "EmployeeIds" =>$incomeUnderSection24EmployeeIds,'incomeUnderSection24Details'=>$incomeUnderSection24Details);
	}

	public function getTaxDeclarationEmployeeIds($employeeId)
	{
		$status = array('Declared','Applied','Returned');
		$taxDeclarationEmployeeIds = $this->_db->fetchCol($this->_db->select()->from(array('TD'=>$this->_ehrTables->taxDeclaration), array('TD.Employee_Id'))
												->where('TD.Approval_Status IN (?)', $status)
												->where('TD.Employee_Id  IN (?)', $employeeId)
												->where('TD.Assessment_Year = ?', $this->_orgDetails['Assessment_Year'])
												->Group('TD.Declaration_Id'));
		return $taxDeclarationEmployeeIds;	 									
	}

	public function getHraDeclarationEmployeeIds($employeeId)
	{
		$status = array('Applied','Returned'); 
		$hraDeclarationEmployeeIds = $this->_db->fetchCol($this->_db->select()->from(array('HD'=>$this->_ehrTables->hraDeclaration), array('HD.Employee_Id'))
												->joinInner(array('LL'=>$this->_ehrTables->landLord), 'HD.Declaration_Id = LL.Declaration_Id', array())
												->joinInner(array('S'=>$this->_ehrTables->salary), 'HD.Employee_Id = S.Employee_Id', array())
												->where('HD.Employee_Id  IN (?)',$employeeId)
												->where('HD.Assessment_Year = ?', $this->_orgDetails['Assessment_Year'])
												->where('LL.Approval_Status IN (?)', $status)
												->Group('HD.Declaration_Id'));
		return $hraDeclarationEmployeeIds;	 									
	}

	public function getIncomeUnderSection24EmployeeIds($employeeIds)
	{
		$incomeUnderSection24EmployeeIds = array();
		$housePropertyStatus = array('Applied', 'Declared', 'Reopened', 'Returned');
		
		$qrySOPIncomeEmployeeId = $this->_db->select()->distinct()->from(array('SO'=>$this->_ehrTables->selfOccupiedPropertyIncome), array('SO.Employee_Id'))
														->where('SO.Employee_Id IN (?)',$employeeIds)
														->where('SO.Assessment_Year = ?', $this->_orgDetails['Assessment_Year'])
														->where('SO.Approval_Status IN (?)', $housePropertyStatus);
		$selfOccupiedIncomeEmployeeIds = $this->_db->fetchCol($qrySOPIncomeEmployeeId);												

		$qryRPIncomeEmployeeId = $this->_db->select()->distinct()->from(array('RP'=>$this->_ehrTables->rentedPropertyIncome), array('RP.Employee_Id'))
														->where('RP.Employee_Id IN (?)',$employeeIds)
														->where('RP.Assessment_Year = ?', $this->_orgDetails['Assessment_Year'])
														->where('RP.Approval_Status IN (?)', $housePropertyStatus);
		$rentedPropertyEmployeeIds = $this->_db->fetchCol($qryRPIncomeEmployeeId);

		if(!empty($selfOccupiedIncomeEmployeeIds) && !empty($selfOccupiedIncomeEmployeeIds))
		{
			$incomeUnderSection24EmployeeIds = array_unique(array_merge($selfOccupiedIncomeEmployeeIds, $rentedPropertyEmployeeIds));
		}
		else if(!empty($selfOccupiedIncomeEmployeeIds))
		{
			$incomeUnderSection24EmployeeIds = $selfOccupiedIncomeEmployeeIds;
		}
		else if(!empty($rentedPropertyEmployeeIds))
		{
			$incomeUnderSection24EmployeeIds = $rentedPropertyEmployeeIds;
		}
		return $incomeUnderSection24EmployeeIds;
	}

	/**
	 * get pending approvals of Tax Declaration
	 */
	public function getTaxReliefCount($payslipEmployeeDetails)
	{
		$taxReliefDeclarationEmployeeIds = array();
		if(!empty($payslipEmployeeDetails['resignedFinanicalClosureEmployeeIds']))
		{
			$status = array('Approved','Rejected');
			$taxReliefDeclarationEmployeeIds = $this->_db->fetchCol($this->_db->select()->from(array('TRD'=>$this->_ehrTables->taxReliefDeclaration), array('TRD.Employee_Id'))
									->where('TRD.Approval_Status NOT IN (?)', $status)
									->where('TRD.Employee_Id  IN (?)', $payslipEmployeeDetails['resignedFinanicalClosureEmployeeIds'])
									->where('TRD.Assessment_Year = ?', $this->_orgDetails['Assessment_Year']));
		}

		$taxReliefDeclarationDetails = $this->getEmployeePersonalInfo($payslipEmployeeDetails,$taxReliefDeclarationEmployeeIds);
		return array("Count" => count($taxReliefDeclarationEmployeeIds), "EmployeeIds" =>$taxReliefDeclarationEmployeeIds,'taxReliefDeclarationDetails'=>$taxReliefDeclarationDetails);	
	}

	public function getEmployeePersonalInfo($payslipEmployeeDetails,$employeeIds)
	{
		$employeeInfo    = array();
		if(!empty($payslipEmployeeDetails['employeeDetails']))
		{
			$employeeDetails = $payslipEmployeeDetails['employeeDetails'];
			if(!empty($employeeIds))
			{
				foreach($employeeIds as $employeeId)
				{
					foreach($employeeDetails as $employeeData)
					{
						if($employeeData['Employee_Id'] ==$employeeId)
						{
							array_push($employeeInfo,array('Employee_Id'=>$employeeData['User_Defined_EmpId'],'Employee_Name'=>$employeeData['Employee_Name']));
						}
					}
				}
			}
		}
		return $employeeInfo;
	}

	/**
	 * get pending approvals of attendance
	 */
	public function getAttendanceCount($paycyleDate,$arrayEmployee)
	{
		$qryAttendanceEmpId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->attendance, 'Employee_Id')
		->where('PunchIn_Date >= ?', $paycyleDate['Salary_Date'])
		->where('PunchIn_Date <= ?', $paycyleDate['Last_SalaryDate'])
		->where("Approval_Status LIKE 'Applied'")
		->where('Employee_Id IN (?)',$arrayEmployee));

		return array("Count" => count($qryAttendanceEmpId), "EmployeeIds" =>$qryAttendanceEmpId);

	}

	/**
	 * get pending approvals of attendance
	 */
	public function getCompOffCount($paycyleDate,$arrayEmployee)
	{
		$qryCompensatoryOffEmpId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->compOff, 'Employee_Id')
		->where('Compensatory_Date >= ?', $paycyleDate['Salary_Date'])
		->where('Compensatory_Date <= ?', $paycyleDate['Last_SalaryDate'])
		->where("Approval_Status LIKE 'Applied'")
		->where('Employee_Id IN (?)',$arrayEmployee));

		return array("Count" => count($qryCompensatoryOffEmpId), "EmployeeIds" =>$qryCompensatoryOffEmpId);
	}
	
	/**
	 * get pending approvals of leave
	 */
	public function getLeaveCount($salaryMonth, $arrayEmployee, $paycyleDate )
	{		
		$invalidLvStatus = array('Applied', 'Returned', 'Cancel Applied');

		/** Get all the employee ids whose leaves are in the above invalidLvStatus so that the respective count will be presented
		 * in the prerequisite
		 */
		$leaveStatusPendingEmpIds = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empLeaves, 
										array('Employee_Id'))
									->where('Approval_Status IN (?)', $invalidLvStatus)
									->where('End_Date >= ?', $paycyleDate['Salary_Date'])
									->where('Start_Date <= ?', $paycyleDate['Last_SalaryDate'])
									->where('Employee_Id IN (?)',$arrayEmployee));

		if(!empty($leaveStatusPendingEmpIds) && count($leaveStatusPendingEmpIds) > 0){
			$leaveStatusPendingEmpIdsCount = count($leaveStatusPendingEmpIds);
		}else{
			$leaveStatusPendingEmpIdsCount = 0;
			$leaveStatusPendingEmpIds = array();
		}
		return array("Count" => $leaveStatusPendingEmpIdsCount, "EmployeeIds" =>$leaveStatusPendingEmpIds);
	}

	/** Get pending approvals of short time off */
	public function getShortTimeOffCount($paycyleDate,$arrayEmployee)
	{
		$invalidShortTimeOffStatus = array('Applied', 'Returned', 'Cancel Applied');

		$shortTimeOffEmpId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->shortTimeOff, 'Employee_Id')
		->where('Short_Time_Off_Date >= ?', $paycyleDate['Salary_Date'])
		->where('Short_Time_Off_Date <= ?', $paycyleDate['Last_SalaryDate'])
		->where('Approval_Status IN (?)',$invalidShortTimeOffStatus)
		->where('Employee_Id IN (?)',$arrayEmployee));

		return array("Count" => count($shortTimeOffEmpId), "EmployeeIds" =>$shortTimeOffEmpId);
	}

	/** Get early checkout records which are not processed for the salary month */
	public function getEarlyCheckoutCount($paycyleDate,$arrayEmployee)
	{
		$invalidEarlyCheckoutValues = array('2', '4');

		$earlyCheckoutEmpIds = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->employeeAttendanceSummary, 'Employee_Id')
		->where('Attendance_Date >= ?', $paycyleDate['Salary_Date'])
		->where('Attendance_Date <= ?', $paycyleDate['Last_SalaryDate'])
		->where('Early_Checkout IN (?)',$invalidEarlyCheckoutValues)
		->where('(Early_Checkout_Short_Time_Off_Id IS NULL or Early_Checkout_Short_Time_Off_Id = ?)', 0)
		->where('(Early_Checkout_Leave_Id IS NULL or Early_Checkout_Leave_Id = ?)', 0)
		->where('(Early_Checkout_Ignore_Status IS NULL or Early_Checkout_Ignore_Status = ?)','No')
		->where('Employee_Id IN (?)',$arrayEmployee));
		
		return array("Count" => count($earlyCheckoutEmpIds), "EmployeeIds" => $earlyCheckoutEmpIds);
	}
	
	/**
	 * check employees for salary recalculation
	 */
	public function getSalaryRecalculationCount( $arrayEmployee )
	{
		$checkSalaryRecalcEmployeeEmpId = $this->_db->fetchCol($this->_db->select()->from(array('S'=>$this->_ehrTables->salary), 'S.Employee_Id')
											->joinInner(array('EJ'=>$this->_ehrTables->empJob), 'EJ.Employee_Id = S.Employee_Id', array())
											->where('EJ.Emp_Status Like ?', 'Active')
											->where('S.Salary_Recalc >= ?',1)
											->where('S.Employee_Id IN (?)',$arrayEmployee));
		return array("Count" => count($checkSalaryRecalcEmployeeEmpId), "EmployeeIds" =>$checkSalaryRecalcEmployeeEmpId);
	}
	
	/**
	 * check for financial closure
	 */
	public function getFinancialClosure($salaryMonth)
	{
		$month = explode(',', $salaryMonth);
		
		//getting all the months(financial start month to financial end month)
		$financialMonthRange = $this->_dbFinancialYr->financialYr();
		
		$orgCode = $this->_ehrTables->getOrgCode();
		$orgdetails = $this->_dbOrgDetail->viewOrgDetail($orgCode);

		if($orgdetails['Financial_Closure_Tracking'] != 0 && $month[0] == current($financialMonthRange))
		{
			if($orgdetails['Financial_Closure_Tracking'] == 1)
			{	
				return array("Count" => 1, "EmployeeIds" => array());
			}
			else
			{
				return array("Count" => 0, "EmployeeIds" => array());
			}
		}
	}
	
	/**
	 * check for attendance enforce payment
	 */
	public function getAttendanceEnforcePayment($payslipEmployeeDetails,$paycyleDate)
	{
		$attendanceLackEmp = array();
		$salaryEmpId = $payslipEmployeeDetails['salaryEmployeeIds'];
		
		if(count($salaryEmpId)>0)
		{
			$attendanceEnforcedEmployeeDetails = $this->_dbPayslip->getAttendanceEnforcePaymentEnabledEmployeeId($salaryEmpId,$paycyleDate['Salary_Date'],$paycyleDate['Last_SalaryDate']);
			if(count($attendanceEnforcedEmployeeDetails)>0)
			{
				$attendanceLackEmp = $this->_dbPayslip->getAbsentEmployeeDetails($attendanceEnforcedEmployeeDetails,$paycyleDate['Salary_Date'],$paycyleDate['Last_SalaryDate'],'salary-payslip','');
			}
		}
		
		if(count($attendanceLackEmp)>0)
		{
			return $attendanceLackEmp;
		}
		else
		{
			return 0;
		}
	}

	
	public function getAssetManagement($payslipEmployeeDetails)
	{
		$employeeAssetDetails = array();
		$paycyleDate 		  = $payslipEmployeeDetails['paycyleDate'];
		$resignedEmployeeIds  = $payslipEmployeeDetails['resignedEmployeeIds'];
		if(!empty($resignedEmployeeIds))
		{
			$employeeDetails = $payslipEmployeeDetails['resignedEmployeeDetails'];
			$assetDetails    = $this->getAssetDetails($resignedEmployeeIds);
			foreach($employeeDetails as $employeeData)
			{
				foreach($assetDetails as $assetData)
				{
					if($employeeData['Employee_Id']==$assetData['Employee_Id'] && (strtotime($assetData['Return_Date']) > strtotime($employeeData['Last_Salary_Date']) || empty($assetData['Return_Date']) || $assetData['Return_Date']=='0000-00-00'))
					{
						array_push($employeeAssetDetails,array('User_Defined_EmpId'=>$employeeData['User_Defined_EmpId'],'Employee_Id'=>$employeeData['Employee_Id'],'Employee_Name'=>$employeeData['Employee_Name']));
					}
				}
			}
		}
		
		$assetEmployeeId = array_column($employeeAssetDetails,'Employee_Id');
		return array("Count" => count($assetEmployeeId), "EmployeeIds" =>$assetEmployeeId,'employeeAssetDetails'=>$employeeAssetDetails);
	}

	public function getAssetDetails($employeeId)
	{
		$qryAssets = $this->_db->select()->distinct()->from($this->_ehrTables->empAssets, array('Employee_Id','Return_Date'))
								->where('Employee_Id in (?)', $employeeId);
		$rowAssets = $this->_db->fetchAll($qryAssets);

		return $rowAssets;
	}
	
	public function getShiftNotScheduledDays($payslipEmployeeDetails,$paycyleDate)
	{
		$unScheduledShiftDetails	    = array();
		$shiftRoasterEmployeeDetails	= array();
		$salaryEmpId 					= $payslipEmployeeDetails['salaryEmployeeIds'];
		
		if(count($salaryEmpId)>0)
		{
			$employeeDetails = $payslipEmployeeDetails['employeeDetails'];
			foreach($employeeDetails as $employeeData)
			{
				if($employeeData['Work_Schedule']=='Shift Roster')
				{
					array_push($shiftRoasterEmployeeDetails,$employeeData); 
				}
			}
		}
		
		if(!empty($shiftRoasterEmployeeDetails))
		{
			$shiftRoasterEmployeeIds 	= array_column($shiftRoasterEmployeeDetails,'Employee_Id');
			$shiftDetails 				= $this->getShiftDetails($shiftRoasterEmployeeIds,$paycyleDate['Salary_Date'],$paycyleDate['Last_SalaryDate']);
			$unScheduledShiftDetails    = $this->findShiftNonScheduledDays($shiftDetails, $shiftRoasterEmployeeDetails,$paycyleDate);
		}
		
		$unScheduledShiftEmployeeId = array_unique(array_column($unScheduledShiftDetails,'Employee_Id'));
		
		return array("Count" => count($unScheduledShiftEmployeeId), "EmployeeIds" =>$unScheduledShiftEmployeeId,'unScheduledShiftDetails'=>$unScheduledShiftDetails);
	}

	public function findShiftNonScheduledDays($shiftDetails, $shiftRoasterEmployeeDetails,$paycyleDate) {
		// Step 1: Create a new array to hold the non-scheduled dates for each employee
		$nonScheduledDatesByEmployee = array();
		$considerCutoffDaysForAttendanceAndTimeoff  =  strtolower($this->_orgDetails['Consider_Cutoff_Days_For_Attendance_And_Timeoff']); 
		// Step 2: Loop through the shiftRoasterEmployeeDetails array to compare with shiftDetails
		foreach ($shiftRoasterEmployeeDetails as $shiftRoasterEmployee) {
			$employeeId = $shiftRoasterEmployee['Employee_Id'];
			$userDefinedEmpId = $shiftRoasterEmployee['User_Defined_EmpId'];
			$employeeName = $shiftRoasterEmployee['Employee_Name'];

			if($considerCutoffDaysForAttendanceAndTimeoff == "yes")
			{
				//when the employee is mid joined we need to consider the bigger date because date of join should be greater than salaryDate
				if($paycyleDate['Salary_Date'] > $shiftRoasterEmployee['Salary_Date'])
				{
					$salaryDate = $paycyleDate['Salary_Date'];
				}
				else
				{
					$salaryDate = $shiftRoasterEmployee['Salary_Date'];
				}

				//when the employee is mid resigned we need to consider the smaller date resignation date should be lesser than lastSalaryDate
				if($paycyleDate['Last_SalaryDate'] < $shiftRoasterEmployee['Last_Salary_Date'])
				{
					$lastSalaryDate = $paycyleDate['Last_SalaryDate'];
				}
				else
				{
					$lastSalaryDate = $shiftRoasterEmployee['Last_Salary_Date'];
				}
			}
			else
			{
				//resigned and midjoined handle in both calendar and non calendar
				$salaryDate = $shiftRoasterEmployee['Salary_Date'];
				$lastSalaryDate = $shiftRoasterEmployee['Last_Salary_Date'];
			}
			

			
			$employeeSalaryRangeDates = $this->generateDateRange($salaryDate,$lastSalaryDate);
			// Check if the employee exists in the shiftDetails array
			if (isset($shiftDetails[$employeeId])) {
				// Find the shiftDetails for the specific employee
				$employeeShiftScheduledDates = $shiftDetails[$employeeId];
			} else {
				$employeeShiftScheduledDates = array();
			}
			// Find the non-scheduled dates by comparing with the shiftDetails
			$nonScheduledDates = array_diff($employeeSalaryRangeDates, $employeeShiftScheduledDates);

			// Loop through the non-scheduled dates and save data for each date
			 foreach ($nonScheduledDates as $nonScheduledDate) {
				$nonScheduledDate = date($this->_orgDF['php'], strtotime($nonScheduledDate));
				$nonScheduledDatesByEmployee[] = array(
					'User_Defined_EmpId' => $userDefinedEmpId,
					'Employee_Id' => $employeeId,
					'Employee_Name' => $employeeName,
					'Non_Scheduled_Date' => $nonScheduledDate,
				);
        	 }
		}
		return $nonScheduledDatesByEmployee;
	}
	

	public function generateDateRange($startDate, $endDate) 
	{
		$dateRange = array();
		$current = strtotime($startDate);
		$last = strtotime($endDate);
		$step = '+1 day'; // incrementor in the while loop

		while ($current <= $last) {
			$dateRange[] = date('Y-m-d', $current);
			$current = strtotime($step, $current); // incrementor in the while loop
		}

		return $dateRange;
	}

	public function getShiftDetails($employeeId,$salaryDate,$lastSalaryDate)
	{
		$shiftDatesByEmployee = array();
		$shiftDetails = $this->_db->fetchAll($this->_db->select()->from(array('SEM'=>$this->_ehrTables->shiftEmpMapping), array('Shift_Start_Date','Employee_Id')) 
																		->where('SEM.Employee_Id IN (?)', $employeeId)
																		->where('SEM.Shift_Start_Date >= ?', $salaryDate) 
																		->where('SEM.Shift_Start_Date <= ?', $lastSalaryDate)
																		->order(array('SEM.Employee_Id ASC', 'SEM.Shift_Start_Date ASC'))); 
		foreach ($shiftDetails as $shift) {
			$employeeId = $shift['Employee_Id'];
			
			// If the Employee_Id does not exist in the array, initialize an empty array
			if (!isset($shiftDatesByEmployee[$employeeId])) {
				$shiftDatesByEmployee[$employeeId] = array();
			}
			
			// Add the Shift_Start_Date to the array for the corresponding Employee_Id
			$shiftDatesByEmployee[$employeeId][] = $shift['Shift_Start_Date'];
		}																
		return $shiftDatesByEmployee;   
	}

	public function getFFEmployeeDetails($payslipMonth,$payslipEmployeeDetails){
		$settlementEmpIds = $settlementNotInitiatedEmpIds = $settlementDraftEmpIds = $settlementPendingEmpIds = array();
		$ffEmployeeDetails = array();
		$payslipMonthInactiveEmployees = $payslipEmployeeDetails['resignedEmployeeIds'];
		if(!empty($payslipMonthInactiveEmployees) && count($payslipMonthInactiveEmployees)>0){
			//Get the employee details with settlement status
			$settlementEmpDetails = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->employeeFullAndFinalSettlement, array('Employee_Id','Settlement_Status'))
										->where('Salary_Month = ?', $payslipMonth)
										->where('Employee_Id IN (?)', $payslipMonthInactiveEmployees));
			if(!empty($settlementEmpDetails) && count($settlementEmpDetails) > 0){
				$settlementEmpIds = array_column($settlementEmpDetails, 'Employee_Id');

				$draftStatus = 'Pending Approval';
				$settlementDraftEmpDetails = array_filter($settlementEmpDetails, function($value) use ($draftStatus){
					return $value["Settlement_Status"] == $draftStatus;
				});
				if(!empty($settlementDraftEmpDetails) && count($settlementDraftEmpDetails) > 0){
					$settlementDraftEmpIds = array_column($settlementDraftEmpDetails, 'Employee_Id');
				}
			}

			$settlementNotInitiatedEmpIds	= array_diff($payslipMonthInactiveEmployees,$settlementEmpIds);
			$settlementPendingEmpIds 	 	= array_merge($settlementNotInitiatedEmpIds,$settlementDraftEmpIds);
			$ffEmployeeDetails 				= $this->getEmployeePersonalInfo($payslipEmployeeDetails,$settlementPendingEmpIds);
		}
		return array("Count" => count($settlementPendingEmpIds), "EmployeeIds" =>$settlementPendingEmpIds,'ffEmployeeDetails'=>$ffEmployeeDetails);
	}

	public function getPayslipEmployeeDetails($salaryMonth,$arrayEmployee,$payPeriod=NULL)
	{
		$resignedFinanicalClosureEmployeeIds = array();
		$resignedEmployeeIds = array();
		$resignedEmployeeDetails = array();
		$employeeDetails = array();
		$month = explode(',', $salaryMonth);
		if($payPeriod)
		{
			$paycyleDate = $this->_dbPayslip->getSalaryDay($month[0], $month[1],'',$payPeriod);
		}
		else
		{
			$paycyleDate = $this->_dbPayslip->getSalaryDay($month[0], $month[1]);
		}
		
		//getting all the months(financial start month to financial end month)
		$financialMonthRange = $this->_dbFinancialYr->financialYr();

		if(count($arrayEmployee)>0)
		{
			$employeeDetails = $this->_dbPayslip->getSalaryPayslipEmployeeDetails($arrayEmployee,$paycyleDate['Salary_Date'],$paycyleDate['Last_SalaryDate']);
			$salaryStartDateTimestamp = strtotime($paycyleDate['Salary_Date']);
			$salaryEndDateTimestamp = strtotime($paycyleDate['Last_SalaryDate']);
			foreach($employeeDetails as $employeeData)
			{
				$resignationDate = $employeeData['Resignation_Date'] ?? '';
				if(!empty($resignationDate)){
					$employeeResignationDateTimestamp = strtotime($resignationDate);
					$resignationStatus = strtolower($employeeData['Approval_Status'] ?? '');
					// Check if employee resigned within the current payroll cycle period
					if($employeeResignationDateTimestamp >= $salaryStartDateTimestamp && $employeeResignationDateTimestamp <= $salaryEndDateTimestamp
					&& $resignationStatus  === 'approved')
					{
						// Employee resigned during this payroll cycle - include in resigned processing
						array_push($resignedEmployeeDetails,$employeeData);
					}
					// Financial closure includes employees resigned within cycle OR at financial year end
					if(($employeeResignationDateTimestamp >= $salaryStartDateTimestamp && $employeeResignationDateTimestamp <= $salaryEndDateTimestamp && $resignationStatus  === 'approved') || $month[0] == end($financialMonthRange))
					{
						array_push($resignedFinanicalClosureEmployeeIds,$employeeData['Employee_Id']);
					}
				}
			}
			
			if(!empty($employeeDetails))
			{
				$arrayEmployee = array_column($employeeDetails, 'Employee_Id');
			}
			else
			{
				$arrayEmployee = array();
			}
			
		}

		if(!empty($resignedEmployeeDetails))
		{
			$resignedEmployeeIds = array_column($resignedEmployeeDetails, 'Employee_Id');
		}
		else
		{
			$resignedEmployeeIds = array();
		}
		
		
		return array('salaryEmployeeIds'=>$arrayEmployee,'resignedEmployeeIds'=>$resignedEmployeeIds,'resignedEmployeeDetails'=>$resignedEmployeeDetails,'resignedFinanicalClosureEmployeeIds'=>$resignedFinanicalClosureEmployeeIds,'paycyleDate'=>$paycyleDate,'employeeDetails'=>$employeeDetails);
	}

	//Function to get employee lop recovery records for the payslip month
	public function getLOPRecoveryEmployeeDetails($payslipMonth,$salaryEmpId){
		$lopRecoveryEmpIds = array();

		$recoveryQuery = $this->_db->select()->from(array('ELR' => $this->_ehrTables->employeeLopRecovery), array(''))
							->joinInner(array('LRA' => $this->_ehrTables->employeeLopRecoveryAmountDetails), 'ELR.LOP_Recovery_Id = LRA.LOP_Recovery_Id', array('EL.Employee_Id'))
                            ->joinInner(array('EL' => $this->_ehrTables->empLeaves), 'LRA.Leave_Id = EL.Leave_Id', array(''))
                            ->where('EL.Employee_Id IN (?)', $salaryEmpId)
                            ->where('ELR.Salary_Month = ?', $payslipMonth)
							->where('ELR.Approval_Status = ?', 'Applied')
							->group('EL.Employee_Id');

        $lopRecoveryEmployeeDetails = $this->_db->fetchAll($recoveryQuery);
		if(!empty($lopRecoveryEmployeeDetails) && count($lopRecoveryEmployeeDetails) > 0){
			$lopRecoveryEmpIds = array_column($lopRecoveryEmployeeDetails, 'Employee_Id');
		}
		return array("Count" => count($lopRecoveryEmpIds), "EmployeeIds" =>$lopRecoveryEmpIds);
	}
	
	public function __destruct()
    {
        
    }	
	
}