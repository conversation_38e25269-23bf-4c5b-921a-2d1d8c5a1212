<?php
//=========================================================================================
//=========================================================================================
/* Program : EmplopyeeGroupsController.php 								           			 *
 * Property of Caprice Technologies Pvt Ltd,											 *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,										 *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies				 *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : The Roles Template form is to create a groups of permissions. In which  *
 * each group will be called as a template. These templates will be having the permissions*
 * associated with it, which in turn will be assigned to employee/employee groups.       *							    	                                                 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *	Version    Date                 Author                  Description                  *
 *	0.1        16-August-2019       Ahalya                Initial Version         	     *
 *                                                                                    	 *
 *  																	                 *
 *                                                                                   	 */
//=========================================================================================
//=========================================================================================
class Employees_RolesTemplateController extends Zend_Controller_Action
{
    /**initialise the variables to be used */

    protected $_dbAccessRights = null;

    protected $_logEmpId = null;

    protected $_dbCommonFunction = null;

    protected $_hrappMobile = null;

    public function init()
    {
        $this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
        if ($this->_hrappMobile->checkAuth())
        {
            $this->_dbAccessRights              = new Default_Model_DbTable_AccessRights();

            $this->_dbCommonFunction            = new Application_Model_DbTable_CommonFunction();
            /**get the current user session */
            $userSession                        = $this->_dbCommonFunction->getUserDetails (); 
            /**get the employee id who is currently logged in */          
            $this->_logEmpId                    = $userSession['logUserId'];
			
            
			/**refresh the time stamp */
            //$this->_dbAccessRights->refreshUserSessionTimestamp($this->_logEmpId);

        }
        else
        {
            if (Zend_Session::namespaceIsset('lastRequest'))
                Zend_Session:: namespaceUnset('lastRequest');
            
            $session = new Zend_Session_Namespace('lastRequest');
            $session->lastRequestUri = 'employees/roles-template'; 
            $this->_redirect('auth');
        }
    }

    public function indexAction()
    {
        $checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

        if ($checkSessionAuth)
        {
            $this->_helper->layout()->disableLayout()->setLayout('admin_layout');
            /** to set the view files of this particular controller in .html extension*/
            $this->getHelper('viewRenderer')->setViewSuffix('html');
        } else {
			$this->_redirect('auth');
		}
    }

    public function __destruct()
    {
        
    }

}

