<?php
//=========================================================================================
//=========================================================================================
/* Program : AssignmentsController.php											         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : A project will have multiple activities. Admin or a lead person         *
 * will add activities and tasks to a project. These tasks are assigned to an employee.  *
 * Assignments component handles the tasks assigned by the supervisors to his/her        *
 * sub-ordinates. This is another very important task in HR that not only keeps record   *
 * of each job assigned by the employees, but also follows up, so that the task may      *
 * be completed on time. 																 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions      :                                                                    	 *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Shobana			      Initial Version        	         *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Nivethitha         Changed in file for mobile app          *
 *                                                                                       *
 *  1.5        10-Feb-2016    Deepak             Changed in file for Bootstrap           *
 *  						      	  					      				             */
//=========================================================================================
//=========================================================================================

include APPLICATION_PATH."/validations/Validations.php";

class Employees_AssignmentsController extends Zend_Controller_Action
{
	protected $_validation         = null;
    protected $_dbCommonFunction   = null;
    protected $_dbAssignment       = null;
    protected $_dbAccessRights     = null;
    protected $_assignAccessRights = null;
	protected $_assignAccess       = null;
    protected $_logEmpId           = null;
	protected $_ehrTables          = null;
	protected $_formName           = 'Assignments';
	protected $_dbAlerts           = null;
	protected $_hrappMobile		   = null;
	protected $_dbEmployee     	   = null;
	protected $_orgDetails         = null;
        
    public function init()
    {
		$this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
		if ($this->_hrappMobile->checkAuth())
        {
			$this->_validation 	       = new Validations();
			$this->_dbCommonFunction   = new Application_Model_DbTable_CommonFunction();
			$this->_dbAssignment       = new Employees_Model_DbTable_Assignment();
            $this->_dbAccessRights     = new Default_Model_DbTable_AccessRights();
			$this->_ehrTables 	   = new Application_Model_DbTable_Ehr();
			$this->_dbAlerts       = new Default_Model_DbTable_Alerts();
			$this->_dbEmployee     = new Employees_Model_DbTable_Employee();

			$userSession               = $this->_dbCommonFunction->getUserDetails ();
			$this->_logEmpId           = $userSession['logUserId'];
            $this->_assignAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formName);
			$this->_assignAccess       = $this->_assignAccessRights['Employee'];
			//$this->_dbAccessRights->refreshUserSessionTimestamp($this->_logEmpId);
			$this->_assignmentAccessRights      = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, 'Assignments');
			
			if (Zend_Registry::isRegistered('orgDetails'))
			$this->_orgDetails = Zend_Registry::get('orgDetails');
        }
        else
        {
            if (Zend_Session::namespaceIsset('lastRequest'))
                Zend_Session:: namespaceUnset('lastRequest');
            
            $session = new Zend_Session_Namespace('lastRequest');
            $session->lastRequestUri = 'employees/assignments';
            $this->_redirect('auth');
        }
    }

    public function indexAction()
    {
        $checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

        if ($checkSessionAuth)
		{
			$this->_helper->layout()->disableLayout()->setLayout('admin_layout');
		
			$dbPersonal = new Employees_Model_DbTable_Personal();
			$dbProject  = new Organization_Model_DbTable_Project();
			
			$this->view->formNameA = $this->_formName;
			$this->view->customFormNameA = $this->_ehrTables->getCustomForms($this->_formName);
	
			$result = $this->_dbAlerts->showHomePanelData($this->_logEmpId);
			
			$logEmployeeDetails = $dbPersonal->employeeName($this->_logEmpId);
			$this->view->logEmployeeId   = $this->_logEmpId;
			$this->view->logEmployeeName = $logEmployeeDetails['Employee_Name'];
			
			$this->view->projects = $dbProject->getprojectList();
			$this->view->assignee = $dbPersonal->employeeDetail('', '', 'Emp_First_Name', 'ASC', '', '','', '', $this->_logEmpId,
																	  '', '', '', '', 'Assignments', '', 1, 1, '');
			
			$this->view->assignmentAccess =  array( 'Is_Manager' => $this->_assignAccess['Is_Manager'],
													'View'       => $this->_assignAccess['View'],
													'Add'        => $this->_assignAccess['Add'],
													'Update'     => $this->_assignAccess['Update'],
													'Delete'     => $this->_assignAccess['Delete'],
													'Admin'      => $this->_assignAccessRights['Admin']);
			
			$this->view->dateformat = $this->_ehrTables->orgDateformat();

			$this->view->orgDetails      = $this->_orgDetails;
			$this->view->serviceProvider = $this->_dbEmployee->getEmployeeServiceProviderList($this->_logEmpId);
		} else {
			$this->_redirect('auth');
		}
	}

	public function listDashboardAssignmentAction()
	{
		$result = $this->_dbAlerts->showHomePanelData($this->_logEmpId);

		$this->_helper->layout()->disableLayout();
		
        if(isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('list-assignment', 'json')->initContext();

			if($this->_assignmentAccessRights['Employee']['View'] == 1)
			{
				$this->view->result = $result['assignedTask'];
			}
			else
			{
				$this->view->result = array("iTotalRecords" => 0 , "iTotalDisplayRecords" => 0, "aaData" => '');
			}
			
		}
	}

    /**
     * Get assignment details to show in a grid
     */
    public function listAssignmentAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if(isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-assignment', 'json')->initContext();
            
			if ( $this->_assignAccess['View'] == 1)
            {
				$sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
                
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$taskName = $this->_getParam('sSearch_0', null);
				$taskName = filter_var($taskName, FILTER_SANITIZE_STRIPPED);
				
				$assignorName = $this->_getParam('sSearch_1', null);
				$assignorName = filter_var($assignorName, FILTER_SANITIZE_STRIPPED);
				
				$assigneeName = $this->_getParam('sSearch_2', null);
				$assigneeName = filter_var($assigneeName, FILTER_SANITIZE_STRIPPED);
				
				$projectId = $this->_getParam('sSearch_3', null);
				$projectId = filter_var($projectId, FILTER_SANITIZE_STRIPPED);
				
				$assignmentBeginDate = $this->_getParam('sSearch_4', null);
				$assignmentBeginDate = filter_var($assignmentBeginDate, FILTER_SANITIZE_STRIPPED);
				
				$assignmentEndDate = $this->_getParam('sSearch_5', null);
				$assignmentEndDate = filter_var($assignmentEndDate, FILTER_SANITIZE_STRIPPED);
				
				$status = $this->_getParam('sSearch_6', null);
				$status = filter_var($status, FILTER_SANITIZE_STRIPPED);

				$serviceProviderId = $this->_getParam('sSearch_7', null);
				$serviceProviderId = filter_var($serviceProviderId, FILTER_SANITIZE_NUMBER_INT);
				
				if ($this->_assignAccessRights['Admin'] == 'admin')
                {
                    $role = 'admin';
                }
                else if ($this->_assignAccess['Is_Manager'] == 1)
                {
                    $role = 'manager';
                }
                else
                {
                    $role = "";
                }
				
				$searchArr = array( 'taskName'  	      => $taskName,
								    'assignorName'  	  => $assignorName,
									'assigneeName'  	  => $assigneeName,
									'projectId'    	      => $projectId,
									'status'              => $status,
									'assignmentBeginDate' => $assignmentBeginDate,
									'assignmentEndDate'   => $assignmentEndDate,
									'serviceProviderId' => $serviceProviderId);
				
				//to display all the assignment records in the data grid
				$this->view->result = $this->_dbAssignment->listAssignment($page, $rows, $sortField, $sortOrder, $searchAll, $searchArr,
																		   $role, $this->_logEmpId);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'assignments', 'employees');
        }
    }
	
	/**
     * Update assignment
     */
    public function updateAssignmentAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if(isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('update-assignment', 'json')->initContext();
			
			$taskId = $this->_getParam('taskId',null);
			$taskId = filter_var($taskId, FILTER_SANITIZE_NUMBER_INT);
			
			if ((empty($taskId) && $this->_assignAccess['Add'] == 1) || (!empty($taskId) && $this->_assignAccess['Update'] == 1))
			{
				if($this->getRequest()->isPost())
				{
					$formData = $this->getRequest()->getPost();
					
					$taskName = $this->_validation->alphaNumSpCDotHySlashValidation($formData['taskName']);
					$taskName['valid'] = $this->_validation->lengthValidation($taskName, 3, 50, true);
					
					$description['value'] = $this->_validation->commonFilters($formData['description']);
					$description['valid'] = $this->_validation->lengthValidation($description, 5, 600, false);
					
					$startDateValidation = 1;
					$fetchOldTaskValue   = '';
					
					if(empty($taskId)) // Check start date greater than or equal to curent date on adding new assignment
					{
						$startDateValidation = (date("Y-m-d") <= $formData['fromDate'])? 1 : 0;
					}
					else // fetching value before update to insert in history if any change has made
					{
						$fetchOldTaskValue = $this->_dbAssignment->viewTask($taskId);
					}
					
					if (($startDateValidation == 1) && ($formData['fromDate'] <= $formData['dueDate']) &&
						!empty($taskName['value']) && $taskName['valid'] && !empty($formData['fromDate']) &&
						!empty($formData['dueDate']) && !empty($formData['projectId']) && !empty($formData['creatorId']) &&
						!empty($formData['assignorId']) && !empty($formData['assigneeId']) && !empty($formData['activityId']) &&
						!empty($formData['priority']) && !empty($formData['status']) && $description['valid'])
					{
						$taskArr = array('Task_Id'         => $taskId,
										 'Task_Name'       => $taskName['value'],
										 'Start_Date'      => $formData['fromDate'],
										 'Due_Date'        => $formData['dueDate'],
										 'Assignor'        => $formData['assignorId'],
										 'Creator'         => $formData['creatorId'],
										 'Employee_Id'     => $formData['assigneeId'],
										 'Project_Id'      => $formData['projectId'],
										 'Activity_Id'     => $formData['activityId'],
										 'Priority'        => $formData['priority'],
										 'Approval_Status' => $formData['status'],
										 'Description'     => htmlentities($description['value']));
						
                            $customFormName = $this->_ehrTables->getCustomForms($this->_formName);
							$customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formName);
                        
						//to update the task
						$this->view->result = $this->_dbAssignment->updateAssignment($taskArr, $fetchOldTaskValue, $this->_logEmpId,$customFormNamee);
					}
					else
					{
						$this->view->result = array('success'=>false, 'msg'=>'Invalid Data', 'type'=>'info');
					}                                                 
				}
			}
			else
			{
				$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'danger');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'assignments', 'employees');
        }
    }

	/**
     * Delete assignment
     */
    public function deleteAssignmentAction()
    {
        $this->_helper->layout()->disableLayout();
     
	    if(isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('delete-assignment', 'html')->initContext();
			
			if( $this->_assignAccess['Delete'] == 1 )
			{
				$taskId = $this->_getParam('taskId', null);
				$taskId = filter_var($taskId, FILTER_SANITIZE_NUMBER_INT);
				
				if (!empty($taskId) && $taskId > 0)
				{
                    $customFormName = $this->_ehrTables->getCustomForms($this->_formName);
					$customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formName);
                    
					$this->view->result = $this->_dbAssignment->deleteAssignment($taskId, $this->_logEmpId,$customFormNamee);
				}
				else
				{
					$this->view->result = array('success'=>false, 'msg'=>'Invalid data', 'type'=>'info');
				}
			}
			else
			{
				$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'info');	
			}
        }
        else
        {
            $this->_helper->redirector('index', 'assignments', 'employees');
        }
    }
	
	public function updateEmployeeImportDownloadSheet($selectFieldOptions)
	{
		$selectFieldValue = '';
		
		$i=1;
		
		foreach($selectFieldOptions as $key=>$value)
		{
			if($i == 1)
				$selectFieldValue = $value;
			else
				$selectFieldValue .= ",".$value;
			
			$i++;
		}
		
		return $selectFieldValue;
	}
	
	/**
     * Get audit assignment details to show in a grid
     */
    public function showAuditAssignmentAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if(isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('show-audit-assignment', 'json')->initContext();
        
			if ( $this->_assignAccess['View'] == 1)
            {
				$taskId = $this->_getParam('taskId', null);
				$taskId = filter_var($taskId, FILTER_SANITIZE_NUMBER_INT);
			
				if(!empty($taskId))
				{
					$this->view->result = $this->_dbAssignment->assignmentHistory($taskId);
				}
			}
        }
        else
        {
            $this->_helper->redirector('index', 'assignments', 'employees');
        }
    }
	
    /**
     * Get activities based on projectid
     */
   	public function getActivityAction()
    {
        $this->_helper->layout->disableLayout();
    
	    if(isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('get-activity', 'json')->initContext();
			
			$projectId=$this->_getParam('projectId', null);
			$projectId = filter_var($projectId, FILTER_SANITIZE_NUMBER_INT);
			
			if(!empty($projectId))
			{
				$dbActivity = new Employees_Model_DbTable_TimesheetActivity();
				//to list the activities based on project
				$this->view->result = $dbActivity->getActivityPairs($projectId);
			}
        }
        else
        {
            $this->_helper->redirector('index', 'assignments', 'employees');
        }
    }

	public function __destruct()
    {
        
    }
	 
}