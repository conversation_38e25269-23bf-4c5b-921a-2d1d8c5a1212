
/********************  Dash Board Widget CSS ****************/

/********************  Dash Board Widget CSS ****************/
@media screen and (max-width: 610px) {
.username {
min-width: 30px;
}
}

@media (max-width: 360px) {
  .form-horizontal .form-group {
      margin-right: 0px !important;
      margin-left: 0px !important;
  }
}

@media screen and (min-width: 1200px){
        #fullModal {
          width: 80%;
}}

@media screen and (min-width: 1200px){
  #fullModalPerformanceAssessment {
    width: 92%;
    margin-left:7%;
}}

@media screen and (max-width: 610px) { .topbar .header-right .header-menu #notifications-header .dropdown-menu {
    left: -160px !important;
}
}

.fc .fc-view-harness {
  height: 300px !important;
}

.fc .fc-daygrid-day-number {
  color: #878585;
}

.fc-daygrid-day-events {
  cursor: pointer;
}

.fc .fc-col-header-cell-cushion {
  color: black;
}

.fc .fc-toolbar-title {
  font-size: 1em !important;
}

.fc-col-header {
  width: 100% !important;
}

.fc-scrollgrid-sync-table {
  width: 100% !important;
}

.fc .fc-daygrid-body {
  width: 100% !important;
}

.fc-today-button {
  display: none !important;
}

.leave-schedules-content {
  border: 1px solid;
  border-radius: 5px;
  background: #f1f1f1;
  padding: 5px;
}

.dropdown {
  position: relative !important;
}

.orange-bg {
  background-color: orange;
  color: white;
  height: 28px;
  padding: 5px;
  display: inline-block;
}

.ip-button {
  height: 31px;
  width: 192px;
  font-size: 12px;
}

.location-translation-info > .popover > .popover-content {
  margin: 10px !important;
  background-color: rgb(247, 247, 247) !important;
}

.location-translation-info > .popover > .popover-title {
  background-color: transparent !important;
  border-bottom: none !important;
}

.pageLoading {
    display:    block;
    position:   fixed;
    z-index:    10000000;
    top:        0;
    left:       0;
    height:     100%;
    width:      100%;
    opacity: 0.5;
    background: rgba(0, 0, 0, 0.3) 
                /*50% 50% */
                no-repeat;
}

.pageLoading div{
    /*content: url('../../../images/ajax-loader.gif');*/
    /*content: url('../../../images/default.svg'); */
    margin-top: 40%;
    margin-left: 47%;
    z-index:    100000000;
}

.dataSetupDashboardMask {
  display:    block;
  position:   fixed;
  z-index:    4;
  top:        0;
  left:       0;
  height:     100%;
  width:      100%;
  opacity: 0.5;
  background: rgba(0, 0, 0, 0.3)
              no-repeat;
}

.dataSetupDashboardMask div{
  margin-top: 40%;
  margin-left: 47%;
  z-index: 4;
}


/* When the body has the loading class, we turn
   the scrollbar off with overflow:hidden */
body.loading {
    overflow: hidden;   
}

/* Anytime the body has the loading class, our
   modal element will be visible */
body.loading .pageLoading {
    display: block;
}

/*///////////////////////////////////////////////////////////////////////////*/
.paddingCls
{
    padding-right:0px !important;
    padding-left:0px !important;
}
.profilesize>img{height: 170px; width: 132px;}
.logosize>img{height: 100px; width: 150px;}
.reportLogoSize{height: auto; width: 150px; max-height: 150px;}

.parentDivBorder {
  border:1px solid #ddd;
  border-radius: 4px;
  height: 100px;
  width: 150px;
}

.db-bg-green {
    background-color: #00a65a !important;
}

.db-bg-red {
    background-color: #dd4b39 !important;
}

.db-bg-aqua {
    background-color: #00c0ef !important;
}

.db-bg-orange {
    background-color: #ff851b !important;
}

.db-bg-grass {
    background-color: #a1c436 !important;
}

.db-bg-corp-orange {
    background-color: #08AF7A !important;
}

.db-bg-corp-grass {
    background-color: #0097A7 !important;
}

/*Big Info widget*/
.small-box:hover {
    text-decoration: none;
    color: #f9f9f9;
}

.small-box {
    color: #f9f9f9;
    border-radius: 2px;
    position: relative;
    display: block;
    margin-bottom: 20px;
    box-shadow: 1px 1px 15px #808080d1;
    -webkit-box-shadow : 1px 1px 15px #808080d1;
}


.small-box>.inner {
    padding: 10px;
}

.small-box h3, .small-box p {
    z-index: 5px;
}

.small-box h3 {
    font-size: 38px !important;
    font-weight: bold !important;
    margin: 0 0 10px 0;
    white-space: nowrap;
    padding: 0;
}

sup {
    top: -.5em;
}

sub, sup {
    position: relative;
    font-size: 75%;
    line-height: 0;
    vertical-align: baseline;
}

.small-box p {
    font-size: 15px;
    margin: 0 0 10px;
}

/*
.small-box .icon {
    position: absolute;
    top: auto;
    bottom: 8px;
    right: 5px;
    z-index: 0;
    font-size: 70px;
    color: rgba(0, 0, 0, 0.15);
}*/

.small-box .icon {
    -webkit-transition: all .3s linear;
    -o-transition: all .3s linear;
    transition: all .3s linear;
    position: absolute;
    top: 5px;
    right: 10px;
    z-index: 0;
    font-size: 90px;
    color: rgba(0,0,0,0.15);
}

.small-box:hover .icon {
    animation-name: tansformAnimation;
    animation-duration: .5s;
    animation-iteration-count: 1;
    animation-timing-function: ease;
    animation-fill-mode: forwards;
    -webkit-animation-name: tansformAnimation;
    -webkit-animation-duration: .5s;
    -webkit-animation-iteration-count: 1;
    -webkit-animation-timing-function: ease;
    -webkit-animation-fill-mode: forwards;
    -moz-animation-name: tansformAnimation;
    -moz-animation-duration: .5s;
    -moz-animation-iteration-count: 1;
    -moz-animation-timing-function: ease;
    -moz-animation-fill-mode: forwards;
}

/*.small-box>.small-box-footer {*/
.small-box-footer {
    position: relative;
    text-align: center;
    padding: 3px 0;
    color: #fff;
    color: rgba(255,255,255,0.8);
    display: block;
    z-index: 0;
    background: rgba(0,0,0,0.1);
    text-decoration: none;
    border-radius: 15px; 
}

/*Medium size infor panel with progress bar*/
.db-info-box {
    display: block;
    min-height: 90px;
    background: #fff;
    width: 100%;
    box-shadow: 0 1px 1px rgba(0,0,0,0.1);
    border-radius: 2px;
    margin-bottom: 15px;
}

.db-info-box-icon {
    border-top-left-radius: 2px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 2px;
    display: block;
    float: left;
    height: 90px;
    width: 90px;
    text-align: center;
    font-size: 45px;
    line-height: 90px;
    background: rgba(0,0,0,0.2);
}

.db-info-box-content {
    padding: 5px 10px;
    margin-left: 90px;
}

.db-info-box-text {
    text-transform: uppercase;
}

.db-progress-description, .db-info-box-text {
    display: block;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.db-info-box-number {
    display: block;
    font-weight: bold;
    font-size: 18px;
}

.db-info-box .progress, .db-info-box .progress .progress-bar {
    border-radius: 0;
}

.db-info-box .progress {
    background: rgba(0,0,0,0.2);
    margin: 5px -10px 5px -10px;
    height: 2px;
}

.progress, .progress>.progress-bar, .progress .progress-bar, .progress>.progress-bar .progress-bar {
    border-radius: 1px;
}

.progress, .progress>.progress-bar {
    -webkit-box-shadow: none;
    box-shadow: none;
}

.progress {
    height: 12px;
    margin-bottom:10px;
    border-radius: 8px !important;
    overflow: hidden;
    background-color: #f5f5f5;
    -webkit-box-shadow: inset 0 1px 2px rgba(0,0,0,.1);
    box-shadow: inset 0 1px 2px rgba(0,0,0,.1);
}

.progress-description {
    margin: 0;
}

.progress-description, .db-info-box-text {
    display: block;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.db-info-box .progress .progress-bar {
    background: #fff;
}

.progress-bar {
    float: left;
    width: 0;
    height: 100%;
    font-size: 12px;
    line-height: 20px;
    color: #fff;
    text-align: center;
    background-color: #337ab7;
    -webkit-box-shadow: inset 0 -1px 0 rgba(0,0,0,.15);
    box-shadow: inset 0 -1px 0 rgba(0,0,0,.15);
    -webkit-transition: width .6s ease;
    -o-transition: width .6s ease;
    transition: width .6s ease;
}

.payment-reminder-header {
  margin-bottom: 20px;
  margin-top: 20px;
}

.payment-reminder-content {
  font-size: 1.5em;
  font-weight: bold;
}

@media screen and (max-width: 768px) {
  .hide-image-in-small-screens {
    display: none !important;
  }
}
@media screen and (max-width: 1000px) and (min-width: 769px) {
  .hide-image-in-small-screens {
    display: flex !important;
    justify-content: center;
  }
}
/***********************************************************/

table thead th.no_filter input[type=text] {
    display: none;
}

table.dataTable.select tbody tr,
table.dataTable thead th:first-child {
  cursor: pointer;
}

.dropdown-menu .divider {
    background-color: #e5e5e5 !important;
}

.dropdown-menu > li > a.toggle-vis {
    padding: 0px 20px;
}

.billing-dropdown-menu li a i {
    font-size: 15px;
}

.dropdown-menu.billing-dropdown-menu > li > a {
    padding: 5px 10px !important;
}

#context-menu ul.dropdown-menu, .context-menu ul.dropdown-menu {
    background: white;
    border-radius: 2px;
}

#context-menu .dropdown-menu li, .context-menu .dropdown-menu li {
    margin: 5px 0px;
    cursor: pointer;
}

#context-menu .dropdown-menu li:hover, .context-menu .dropdown-menu li:hover {
    background: #e0e0e0;
}

/************ View field value *************/
.view-hr{
    width: 100%;
    color: black;
    height: 0px;
    background-color:black;
}

.view-label{
    margin-left: 5px;
}

/*************************/

table tr.row_selected td, table tr.sub_grid_row_selected td {
    background-color: #dce1e4 !important;
}

table > tbody > tr.row_selected:hover > td, table > tbody > tr.sub_grid_row_selected:hover > td {
    background-color: #bdbdbd !important;
}

.table tbody tr {
    cursor: pointer;
}

/*.sidebar .logopanel a {*/
/*    background: url(../../../images/white_text_onlya.png) no-repeat;*/
/*    position:absolute;*/
/*    width:126px; */
/*    height:28px; */
/*    left:50%; */
/*    top:50%;*/
/*    margin-left:-63px; */
/*    margin-top:-14px; */
/*}*/

/*.sidebar .logopanel a {
background: url(../../../images/Lekhajokha_CAPS.png) no-repeat;
position: absolute;
display: block;
width: 100%;
height: 50%;
top: 30%;
margin-left: 11%;
}*/

.sidebar .logopanel div{
/*background: url(../../../images/Lekhajokha_CAPS.png) no-repeat;*/
/*position: absolute;*/
/*display: block;*/
width: 100%;
height: 100%;
color: white;
font-weight: bold;
text-align: center;
font-size: 190%;
/*top: 10%;*/
overflow: hidden;
text-overflow: ellipsis;
white-space: nowrap;

}




/*.sidebar .logopanel a {
    background: url(../../../images/white_text_with_logo.png) no-repeat;
    display: block;
    height: 40px;
}*/

.modal-content .modal-body {
    padding: 10px 24px 16px 24px;
}

@media screen and (max-width: 430px) {
    .topbar .header-right .header-menu #notifications-header {
         display: block; 
    }
}

.main-content.adjustPadding .page-content {
    padding: 3.7em 2em 0em;
}

.leave-closure-warning-modal{
  display: flex;
  align-items: center;
}

.leave-closure-warning-modal-content{
  display: flex;
  align-items: center;
}

.leave-closure-warning-modal-heading{
  color: ; 
  font-weight: 500; 
  font-size: 2em;
  margin-bottom: 15px;
  text-align: left;
}

.leave-closure-warning-modal-img{
  width: 100%;
  height: 100%;
  margin: 15px;
}

.leave-closure-warning-modal-img-body{
  display: flex;
  justify-content: center;
}

.column-cus-info-bar{
  background-color: #D4E1FB;
  padding: 0.4em;
}

.column-cus-info-icon{
  color: #004CE4;
}

@media (max-width: 768px) {
    /**
     * Original Code : .form-inline { margin-left: 15px; }
     * So we reduce margin-left to 0px for better ui in mobile
    */
    .dataTables_wrapper.form-inline {
         margin-left: 0px; 
    }
    .leave-closure-warning-modal-content{
      display: block !important;
    }
    .leave-closure-warning-modal-heading{
      text-align: center !important;
    }
}

@media (min-width: 768px),(max-width: 992px) {
    .adjustMargin{
        margin-top: -2.5em !important;
        margin-bottom: 2em;
    }
}

@media (min-width: 993px) {
    .adjustMargin{
        margin-top: -3em !important;
        margin-bottom: 2em;
    }
}

.main-content .page-content .panel:hover .control-btn a:nth-child(2),
.main-content .page-content .panel:hover .control-btn a:nth-child(5) {
    display: none !important;
}

.main-content .page-content .panel:hover .control-btn > a.hidden.panel-reload,
.main-content .page-content .panel:hover .control-btn > a.hidden.panel-popout {
    color: #000000 !important;
}

.toolbar-icons i, .print-toolbar-icons i {
    font-size: 16px;
}

.toolbar-icons, .print-toolbar-icons {
    padding: 8px 15px;
}

.form-icons i {
    font: normal normal normal 24px/1 'Material-Design-Icons';
}

.icon-label-required {
    font-size: 1em;
    color: red;
}

.form-icons {
    padding: 6px 10px;
}

.sub-table {
    color: #000000;
}

.sub-table:hover {
    color: #0097a7;
}

.sub-table tr td {
    padding: 5px 10px;   
}

.short_explanation {
    font-size: 1em;
    color: red;
}

.btn-on {
    display: inline;
}

.btn-off {
    display: none;
}

.modal-body p {
    word-wrap: break-word;
}

.groupFrom, .groupTo {
    width: 40% !important;
    float: left;
}

.groupDivider {
    width: 10% !important;
    float: left;
}

.addedBy {
    background-color:#D0F889 !important;
}
.attRegularization {
  background-color:#FFE3E3 !important;
  opacity: 1;
}
.attLegend {
  display: flex;
  justify-content: flex-end;
}
@media screen and (max-width: 500px) {
  .attLegend {
    display: block !important;
  }
}
.color-legend {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}
.departmentLevel{
    background-color:#D0F889 !important;
}

/*.modal#modal-slideright .modal-body {
    max-height: 420px;
    overflow-y: auto;
}

.modal-open .modal#modal-slideright {
    overflow-y: hidden;
}*/

/*.modal-body {
    max-height: calc(100vh - 212px);
    overflow-y: auto;
}*/





/*************************/
/**
 *  Search page CSS
*/
.prepend-icon input {
    padding-left: 65px !important;
}

.capitalize {
    text-transform: capitalize;
}

.morphsearch-content {
    margin-top: 2em;
    padding: 0 3%;
}

.morphsearch.open .morphsearch-form {
    height: 80px;
    width: 90%;
}

.morphsearch.open .morphsearch-input {
    font-size: 4em;
}

.dummy-column {
    float: left;
    width: 100%;
    padding: 0px;
}

@media screen and (max-width: 1000px) {
    .morphsearch .dummy-column {
        float: left;
    }
}

.morphsearch .dummy-media-object {
    background : none;
    margin: .3em .3em;
    width: 130px;
    float: left;
    clear: right;
}

.morphsearch .dummy-media-object h3 {
    color: white;
    text-align: -webkit-center;
    text-align: center;
    width: 100%;
    height: 30px;
    font-size: .8em;
    margin: 5px auto;
}

.morphsearch .dummy-column:nth-child(2), .morphsearch .dummy-column:nth-child(5) {
    margin: 0;
}

/**
 *  Attendance Box
*/
.fa-1-half-x {
    font-size: 1.5em;
}

.att-box {
    background-color: #FFF;
    float: left;
    width: 100%;
    border-radius: 15px;
    box-shadow: 1px 1px 15px #808080d1;
    -webkit-box-shadow : 1px 1px 15px #808080d1;
}

.att-box p {
    font-size: 14px;
    color: #666;
    text-align: center;
    /*padding: 15px 0 0px 0px;*/
    padding: 21px 0 0px 0px;
}
.pointer {cursor: pointer;}

.work-place-img {
  border: 1px solid;
  display: inline-block;
  margin: 0;
  position: relative;
  box-shadow: 0 0 9px rgba(155, 155, 155, 0.55);
}

.work-place-name {
  position: bottom;
  bottom: 1em;
  left: 0;
  text-align: center;
  width: 100%;
  font-weight: bold;
}

.att-time {
    font-size: 24px;
    color: var(--primary-color);
    text-align: center;
    /*margin: 10px 0 3px 0;*/
    padding: 10px 0 3px 0px;
    word-spacing: 10px;
}

.cursorPoint {
    cursor: pointer;
}

.att-time-zone {
    font-size: 12px;
    color: #999;
    float: right;
    margin: 0 50px 15px 0;
    /*margin:6px 50px 15px 0;*/
}

.att-time-zone span {
    float: left;
    margin-top: 5px;
}

.zone-float {
    float: left;
}

.zone-globel {
    margin-top: 5px;
    margin-right: 5px;
}

.att-in-out {
    margin-left: 10px;
    margin-top: 3px;
    float: left;
    padding-left: 5px;
}

.att-in-out-text {
    margin-left: 5px;
    margin-top: 8px;
    float: left;
    padding-left: 0px;
    cursor: pointer;
}

.att-but {
    background-color: #FAFAFA;
    border-top: 1px solid #eeeeee;
    border-bottom: 1px solid #d9d9d9;
    height: 42px;
    float: left;
    width: 100%;
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 15px;
}

.attClock {
    font-size: 24px;
    color: #666;
    float: right;
    margin-right: 15px;
    line-height: 40px;
    letter-spacing: 1px;
}

.attTimeViewPanel {
    position: relative;
    background-color: #FFF;
    border-left: 1px solid #eeeeee;
    float: right;
    padding: 10px;
    border-bottom-right-radius : 15px;
}

.attViewList, .ZPalistsel, .floatPanelButton {
    width: 20px;
    height: 20px;
    overflow: hidden;
    background-repeat: no-repeat;
    cursor: pointer;
}

.attTimeViewPanel .attHiddenPanel {
    position: absolute;
    bottom: -6px;
    left: 0px;
    height: 6px;
    width: 40px;
    background-color: #FFF;
}

/* Hide / Show Panel */
.attBoxList,.hiddenGrid {
    background-color: #FFF;
    float: left;
    width: 100%;
    margin: 5px 0 0 0;
    overflow: auto;
    max-height: 200px;
}

.attBoxList ul,.hiddenGrid ul {
    margin: 0px;
    padding: 0px;
    list-style: none;
}

.attBoxList li.hed, .attBoxList li,.hiddenGrid li.hed, .hiddenGrid li {
    list-style: none;
    width: 100%;
    float: left;
}

.attBoxList li.hed,.hiddenGrid li.hed {
    line-height: 36px;
    border-bottom: 1px solid #d9d9d9;
}

.attBoxList li,.hiddenGrid li {
    line-height: 28px;
}

.attBoxList li.hed div,.hiddenGrid li.hed div {
    color: #666;
}

.row-odd {
    background-color: #f9f9f9;
}

.attBoxList li div {
    width: 50%;
    text-align: center;
    float: left;
    /*color: #999;*/
}

/*.attBoxList li div,.hiddenGrid li div  */   
.hiddenGrid li div.gridText {
    width: 50%;
    text-align: left;
    padding-left: 8px; 
    float: left;
    /*color: #999;*/
}

.hiddenGrid li div.gridNum {
    width: 50%;
    text-align: Center;
    /*padding-left: 8px; */
    float: left;
    /*color: #999;*/
}

.attViewEntryPanel {
    background-color: #FAFAFA;
    border-top: 1px solid #eeeeee;
    border-bottom: 1px solid #d9d9d9;
    height: 40px;
    float: left;
    width: 100%;
}

.viewAllButton {
    float: left;
    padding-left: 5px;
}

.viewAllButton span {
    float: left;
    color: #999;
    line-height: 20px;
    margin: 10px 0 0 10px;
    cursor: pointer;
}

.floatPanel {
    padding: 10px;
    float: right;
}

/***********************************************************/
/**
 *  Styles for sub menu list
*/

.hrsubmenu_img {
    /*background-color: white;
    margin-top: 10px;
    margin-left: 0px;
    margin-right: 0px;*/
    
    /*border-bottom: 1px solid white;
    margin-top: -20px;
    margin-left: -30px;
    margin-right: -30px;*/
    margin-top: -2%;
    /*margin: -2% 10% 0% 20%;*/
    background-color: white;
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
    text-align: center;
    /*width: 660px;*/
}

.hide-top-menus {
  display: none !important;
}

.hrsubmenu_img ul li {
    float: left;
    list-style: none;
    clear: right;
    padding: 10px 10px 5px;
    margin: 0px 10px;
}

/**
 *  Hrapp modules, form icons
*/
.hrapp-icon-size {
    font-size: 16px;
}

.hrapp-search-icons {
    font-size: 40px;
    color: white;
    padding-left: 30px;
}

.hrapp-datasetup-icon {
  color: #666666;
  font-size: 18px;
  padding: 10px 15px;  
}

.active-sub-menu, .hrsubmenu_img ul li:hover {
    border-bottom: 3px solid var(--primary-color); /**default - the active submodule's hyperlink color below the top bar */
    padding-bottom: 2px !important;
}

.hrsubmenu_img ul li.active-sub-menu a i.hrapp-icon-size, .hrsubmenu_img ul li:hover, .hrsubmenu_img ul li:hover a i.hrapp-icon-size {
    color: var(--primary-color) !important; /**default - the active submodule's icon color below the top bar */
    padding-bottom: 2px;
}

.hrsubmenu_img ul li a i.hrapp-icon-size {
    color: #5b5b5b;
    font-size: 25px;
    padding-top: 0px;
    margin-top: 0px;
}

.hrapp_orgimg:hover,.hrapp_jobimg:hover,.hrapp_empimg:hover,.hrapp_payrollimg:hover ,.hrapp_reportimg:hover,.hrapp_helpimg:hover {
   -webkit-animation: pulse 1s;
            animation: pulse 1s;       
}

/**
 *  Organization Module
*/
.hrapp_orgimg {
	background-image:url("../../../images/org_hrappimg.png");
	background-repeat:no-repeat;
	width:50px;
	height:50px;
	margin:auto;
}

.locations_orgimg {
	background-position: -599px -3px ;
	margin: auto;
}

.locations_orggrayimg {
	background-position: -648px -3px ;
	margin: auto;
}

.departments_orgimg {
	background-position: -899px -3px ;
	margin: auto;
}

.departments_orggrayimg {
	background-position: -950px 0;
	margin: auto;
}

.projects_orgimg {
	background-position: -199px 0;
	margin: auto;
}

.projects_orggrayimg {
	background-position: -249px -3px ;
	margin: auto;
}

.organizationprofile_orgimg {
	background-position: -401px 0;
	margin: auto;
}

.organizationprofile_orggrayimg {
	background-position: -451px 0;
	margin: auto;
}

.organizationpolicies_orgimg {
	background-position: -500px 0;
	margin: auto;
}

.organizationpolicies_orggrayimg {
	background-position: -550px 0;
	margin: auto;
}

.workschedule_orgimg {
	background-position: 0 -1px ;
	margin: auto;
}

.workschedule_orggrayimg {
	background-position: -49px 0;
	margin: auto;
}

.holidays_orgimg {
	background-position: -701px -3px ;
	margin: auto;
}

.holidays_orggrayimg {
	background-position: -750px -3px ;
	margin: auto;
}

.systemlog_orgimg {
	background-position: -99px 0;
	margin: auto;
}

.systemlog_orggrayimg {
	background-position: -149px 0;
	margin: auto;
}

.organizationsettings_orgimg {
	background-position: -300px -3px ;
	margin: auto;
}

.organizationsettings_orggrayimg {
	background-position: -352px 0;
	margin: auto;
}

.employeeimport_orgimg {
	background-position: -799px 0;
	margin: auto;
	
}
.employeeimport_orggrayimg {
	background-position: -849px -2px ;
	margin: auto;
}

/**
 *  Recuritement Module
*/

.hrapp_jobimg {
	background-image:url("../../../images/recruitment_hrappimg.png");
	background-repeat:no-repeat;
	width:50px;
	height:50px;
}

.jobpostrequisition_jobimg {
	background-position: -199px -3px ;
	margin: auto;
}

.jobpostrequisition_jobgrayimg {
	background-position: -249px -3px ;
	margin: auto;
}
.jobposts_jobimg {
	background-position: -99px -3px ;
	margin: auto;
}

.jobposts_jobgrayimg {
	background-position: -150px -3px ;
	margin: auto;
}

.jobcandidates_jobimg {
	background-position: -300px -3px ;
	margin: auto;
}

.jobcandidates_jobgrayimg {
	background-position: -350px -3px ;
	margin: auto;
}

.shortlistedcandidates_jobimg {
	background-position: 0 0;
	margin: auto;
}

.shortlistedcandidates_jobgrayimg {
	background-position: -50px 0;
	margin: auto;
}

/**
 *  Employee Module
*/

.hrapp_empimg {
	background-image:url("../../../images/emp_hrappimg.png");
	background-repeat:no-repeat;
	width:50px;
	height:50px;
}

.employees_empimg {
	background-position: -1299px 0;
	margin: auto;
}

.employees_empgrayimg {
	background-position: -1350px 0;
	margin: auto;
}

.timesheets_empimg {
	background-position: -199px -2px ;
    margin: auto;
}

.timesheets_empgrayimg {
	background-position: -249px -3px ;
	margin: auto;
}

.attendance_empimg {
	background-position: -999px 0;
	margin: auto;
}

.attendance_empgrayimg {
	background-position: -1048px 0;
	margin: auto;
}

.assignments_empimg {
	background-position: -901px -3px ;
	margin: auto;
}

.assignments_empgrayimg {
	background-position: -949px -1px ;
	margin: auto;
}

.leaves_empimg {
	background-position: -700px -3px ;
	margin: auto;
}

.leaves_empgrayimg {
	background-position: -750px -3px ;
	margin: auto;
}

.transfer_empimg {
	background-position: -99px 0;
	margin: auto;
}

.transfer_empgrayimg {
	background-position: -149px -2px ;
	margin: auto;
}

.resignation_empimg {
	background-position: -398px -3px ;
	margin: auto;
}

.resignation_empgrayimg {
	background-position: -449px -3px ;
	margin: auto;
}

.employeetravel_empimg {
	background-position: -1100px 0;
	margin: auto;
}

.employeetravel_empgrayimg {
	background-position: -1150px -1px ;
	margin: auto;
}

.inbox_empimg {
	background-position: -802px -2px ;
	margin: auto;
}

.inbox_empgrayimg {
	background-position: -852px -3px ;
	margin: auto;
}

.warnings_empimg {
	background-position: 0 -2px ;
	margin: auto;
}

.warnings_empgrayimg {
	background-position: -50px 0;
	margin: auto;
}

.memos_empimg {
	background-position: -599px -3px ;
	margin: auto;
}

.memos_empgrayimg {
	background-position: -649px -3px ;
	margin: auto;
}

.awards_empimg {
	background-position: -1400px -1px ;
	margin: auto;
}

.awards_empgrayimg {
	background-position: -1449px 0;
	margin: auto;
}

.complaints_empimg {
	background-position: -1199px -1px ;
	margin: auto;
}

.complaints_empgrayimg {
	background-position: -1249px -2px ;
	margin: auto;
}

.performanceevaluation_empimg {
	background-position: -499px -3px ;
	margin: auto;
}

.performanceevaluation_empgrayimg {
	background-position: -548px -3px ;
	margin: auto;
}

.skillsetassessment_empimg {
	background-position: -300px -1px ;
	margin: auto;
}

.skillsetassessment_empgrayimg {
	background-position: -349px -3px ;
	margin: auto;
}

.miscellaneous_empimg {
	background-position: -512px 2px;
    left: 12%;
    position: relative;
}

.miscellaneous_empgrayimg {
	background-position: -567px 3px;
	position: relative;
	left: 12%;
}

/**
 *  Payroll Module
*/
.hrapp_payrollimg {
	background-image:url("../../../images/payroll_hrappimg.png");
	background-repeat:no-repeat;
	width:50px;
	height:50px;
	margin: auto;
}

.salary_payrollimg {
	background-position: -698px 0;
	margin: auto;
}

.salary_payrollgrayimg {
	background-position: -751px 0;
	margin: auto;
}

.salarypayslip_payrollimg {
	background-position: -599px 0;
	margin: auto;
}

.salarypayslip_payrollgrayimg {
	background-position: -648px 0;
	margin: auto;
}

.taxrules_payrollimg {
	background-position: -1200px 0;
	margin: auto;
}

.taxrules_payrollgrayimg {
	background-position: -1250px 0;
	margin: auto;
}

.allowances_payrollimg {
	background-position: -401px -3px ;
	margin: auto;
}

.allowances_payrollgrayimg {
	background-position: -449px 0;
	margin: auto;
}

.bonus_payrollimg {
	background-position: -201px -2px ;
	margin: auto;
}

.bonus_payrollgrayimg {
	background-position: -250px 0;
	margin: auto;
}

.commission_payrollimg {
	background-position: -100px 0;
	margin: auto;
}

.commission_payrollgrayimg {
	background-position: -151px 0;
	margin: auto;
}

.deductions_payrollimg {
	background-position: 0 0;
	margin: auto;
}

.deductions_payrollgrayimg {
	background-position: -49px 0;
	margin: auto;
}

.reimbursement_payrollimg {
	background-position: -802px 0;
	margin: auto;
}

.reimbursement_payrollgrayimg {
	background-position: -850px -1px ;
	margin: auto;
}

.providentfund_payrollimg {
	background-position: -899px 0;
	margin: auto;
}

.providentfund_payrollgrayimg {
	background-position: -950px 0;
	margin: auto;
}

.advancesalary_payrollimg {
	background-position: -500px 0;
	margin: auto;
}

.advancesalary_payrollgrayimg {
	background-position: -549px 0;
	margin: auto;
}

.loan_payrollimg {
	background-position: -999px 0;
	margin: auto;
}

.loan_payrollgrayimg {
	background-position: -1049px 0;
	margin: auto;	
}

.shift_payrollimg {
	background-position: -1401px 0;
	margin: auto;
}

.shift_payrollgrayimg {
	background-position: -1450px 0;
	margin: auto;
}

.insurance_payrollimg {
	background-position: -1100px 0;
	margin: auto;
}

.insurance_payrollgrayimg {
	background-position: -1152px 0;
	margin: auto;
}

.taxdeclarations_payrollimg {
	background-position: -1299px 0;
	margin: auto;
}

.taxdeclarations_payrollgrayimg {
	background-position: -1350px 0;
	margin: auto;
}

.balancesheettransaction_payrollimg {
	background-position: -300px 0;
	margin: auto;
}

.balancesheettransaction_payrollgrayimg {
	background-position: -352px 0;
	margin: auto;
}

/**
 *  Report Module
*/
.hrapp_reportimg {
	background-image:url("../../../images/report_hrappimg.png");
	background-repeat:no-repeat;
	width:50px;
	height:50px;
	margin: auto;
}

.attendanceshortagereports_reportimg {
	background-position: -500px 0;
	margin: auto;
}

.attendanceshortagereports_reportgrayimg {
	background-position: -551px 0;
	margin: auto;
}

.hrreports_reportimg {
	background-position: -701px 0;
	margin: auto;
}

.hrreports_reportgrayimg {
	background-position: -750px 0;
	margin: auto;
}

.employeesreports_reportimg {
	background-position: 0 -1px ;
	margin: auto;
}

.employeesreports_reportgrayimg {
	background-position: -50px 0;
	margin: auto;
}

.payrollreports_reportimg {
	background-position: -1200px -3px ;
	margin: auto;
}

.payrollreports_reportgrayimg {
	background-position: -1251px 0;
	margin: auto;
}

.recruitmentreports_reportimg {
	background-position: -2200px 0;
	margin: auto;
}

.recruitmentreports_reportgrayimg {
	background-position: -2250px -1px ;
	margin: auto;
}

.height70{
  height: 70px;
}

/**
 *  Help Module
*/
.hrapp_helpimg {
	background-image:url("../../../images/help_hrappimg.png");
	background-repeat:no-repeat;
	width:50px;
	height:50px;
}

.helptopics_helpimg {
	background-position: 0 0;
	margin: auto;
}

.helptopics_helpgrayimg {
	background-position: -49px -1px ;
	margin: auto;
}
.contactus_helpimg {
	background-position: -98px -3px ;
	margin: auto;
}

.contactus_helpgrayimg {
	background-position: -150px -3px ;
	margin: auto;
}

/***********************************************************/

/**
 *  Custom Filter Builder
*/

.filter {
  -webkit-transition: -webkit-transform 600ms cubic-beziercubic-bezier(0.25, 0.1, 0.25, 1);
  background: #ffffff;
  border-left: 1px solid rgba(224, 226, 234, 0.53);
  bottom: 0;
  box-shadow: 0 0 9px rgba(155, 155, 155, 0.55);
  position: fixed;
  right: -250px;
  top: 0;
  transition: transform 600ms cubic-bezier(0.25, 0.1, 0.25, 1);
  width: 250px;
  z-index: 1000;
  overflow-y: auto !important;
}
.filter .inner .filter-container {
  padding: 20px;
}
.filter .inner .filter-container button {
  width: 100%;
}
.filter h4 {
  font-family: 'Lato', 'Open Sans';
  font-size: 14px;
  font-weight: 900;
  margin-bottom: 10px;
  margin-top: 10px;
  padding-top: 10px;
  text-transform: uppercase;
}
.filter a {
  color: #A1A1A1;
  display: block;
  position: relative;
  width: 100%;
}
.filter a:hover {
  color: #818181;
  text-decoration: none !important;
}
.filter a:active {
  color: #818181;
  text-decoration: none !important;
}
.filter a:focus {
  color: #818181;
  text-decoration: none !important;
}
.filter a.active {
  color: #2E2E2E;
}
.filter .filter-close {
  padding: 7px;
  position: absolute;
  right: 36px;
  top: 20px;
  width: auto;
  z-index: 10;
}
/*.filter .filter-toggle {
  cursor: pointer;
  font-size: 16px;
  height: 50px;
  left: -48px;
  line-height: 50px;
  padding-top: 2px;
  position: absolute;
  text-align: center;
  top: 15%;
  width: 54px;
  z-index: 1010;
}
.filter .filter-toggle i {
  padding-right: 5px;
}
.filter:before {
  background-color: #ffffff;
  border-bottom-left-radius: 4px;
  border-left: 1px solid rgba(224, 226, 234, 0.53);
  border-top-left-radius: 4px;
  box-shadow: -3px 2px 2px rgba(189, 189, 189, 0.28);
  content: '';
  height: 50px;
  left: -47px;
  position: absolute;
  top: 15%;
  width: 48px;
}*/
.filter.open {
  -ms-transform: translate(-100%, 0);
  -webkit-transform: translate3d(-100%, 0, 0);
  transform: translate3d(-100%, 0, 0);
}
/**********************/
/**
 *  Grid Floating Add button
*/
button.gridAdd {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 9999;
    padding: 8px 15px;
    -moz-border-radius: 50% !important;
    -webkit-border-radius: 50% !important;
    border-radius: 50% !important;
    border-radius: 20px;
}

/***********************************/


/*.panel-accordion .panel-body a {*/
/*  background-color: transparent;*/
/*  color: #121212;*/
/*  display: inline-block;*/
/*  padding: 15px;*/
/*  width: 100%;*/
/*}*/

td.details-control {
    background: url('../../../images/add.png') no-repeat center center;
    cursor: pointer;
}
tr.shown td.details-control {
    background: url('../../../images/cancel.png') no-repeat center center;
}
.multiselect {
    width:20em;
    height:15em;
    border:solid 1px #c0c0c0;
    overflow:auto;
}
 
.multiselect label {
    display:block;
}
 
.multiselect-on {
    color:#ffffff;
    background-color:#000099;
}
.DTTT_Print {
margin-left: 0 !important;
margin-top: -20 !important;
}
#printReportsTable {
    width: 95%;
    margin: 10px;
    letter-spacing: 0px
}

.paymentTracker_editing_row {
    background-color: #D0F889 !important; /* Add !important to make sure override datables base styles */
}
 
@media (max-width: someresolution){
 .span8{
  width:99.3214534%;
 }
}

#print-modal-controls{border:1px solid #ccc;border-radius:8px;
-webkit-border-radius:8px;-moz-border-radius:8px;top:15px;left:50%;margin:0 0 0 -81px;position:fixed;padding:5px 0;background:rgba(250,250,250,0.75)
}
#print-modal-controls a{color:#FFF;display:block;float:left;height:32px;text-decoration:none;text-indent:-999em;width:80px
}
#print-modal-controls a:hover{opacity:.75}#print-modal-controls a.print{background:url(images/icon-print.png) no-repeat 50% 50%
}
#print-modal-controls a.close{background:url(images/icon-close.png) no-repeat 50% 50%
}


/*send mail button */
.write-answer #sendMail {
  float: right;
  margin-right: 0;
  margin-top: 20px;
}

/*mailbox*/
.attachmentname {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(29, 106, 154, 0.72);
  color: #fff;
  visibility: hidden;
  opacity: 0;
}

/*sender text overflow issue in mobile view*/

@media (max-width: 800px), (max-width: 1000px),(max-width: 1200px),(max-width: 1400px),(max-width: 1600px) {
  .page-app.mailbox .message-item .sender {
    width: 28% !important;
    margin-left: 10%;
  }
  .page-app.mailbox .message-item .subject {
    width: 43% !important;
  }
  
}

@media (max-width: 700px) {
  .geo-coordinates-modal {
    max-width: 650px;
    line-height: 2;
  }
}

@media (min-width: 701px) {
  .geo-coordinates-modal {
    width: 700px;
    line-height: 2;
  }
}

@media (max-width: 480px) {
  .page-app.mailbox .message-item .sender {
    width: 50% !important;
  }
  .page-app.mailbox .message-item .subject {
    display: none;
  }
}
/*@media (max-width: 1291px) {*/
/*  .page-app .email-details{*/
/*    display: none !important;*/
/*  }*/
/*  */
/*}*/

@media (max-width: 800px),(max-width: 1000px),(max-width: 1200px),(max-width: 1400px) {
  .page-content.mailbox:not(.mailbox-send) .email-details {
    display: none;
  }
  .page-content.mailbox:not(.mailbox-send) .email-details .email-subject .go-back-list i {
    display: block;
  }
  .page-content.mailbox-send .emails-list {
    display: none;
  }
  /*.page-app.mailbox .message-item .sender {*/
  /*  width: 150px;*/
  /*}*/
  /*.page-app.mailbox .message-item .subject {*/
  /*  width: auto;*/
  /*}*/
}
/*reducing height of mail list*/
.page-app.mailbox .message-item {
    height: 42px;
    line-height: 25px;
    /*padding: 5px 20px -25px 8px;*/
}

.tab-content{
    border-top:2px solid #bebebe !important;
    font: 13px/1.4285714 Arial, sans-serif;
}

.emails-list .nav-tabs > li i:hover {
color: #2c8ca1;
}

.emails-list .nav-tabs a::before{
    background: #667277;
}

.page-app.mailbox .messages-list:after {
border-left: none;

}

.mailbox .note-table{
    display: none;
}
.email-details .email-content {
    padding:5px;
}
#mailcc:hover,#mailbcc:hover{
    text-decoration: underline;
}

.email-details .write-answer
{
   padding: 0px; 
}

.write-answer .note-editable {
    
    min-height: 150px !important;
    height : auto !important;
}
/*.main-content .page-content {
    margin-top:20px;
}*/
[class^="flaticon-"]:before, [class*=" flaticon-"]:before, [class^="flaticon-"]:after, [class*=" flaticon-"]:after
{
    font-size:50px !important;
    color: #0097a7 !important;
}


.gmail_signature * {
    background: inherit !important;
}

.emails-list section .tab-content {
    padding: 10px; 
}

.mailbox-send {
    margin-top: 0px !important;
}

.email-details .email-subject {
    border-color: #bebebe !important;
}

.email-details .write-answer .answr-textarea {
  background: #ffffff !important;
  border: 1px solid #dfdfdf !important;
  font-family: 'Merriweather', serif !important;
  font-size: 16px !important;
  height: auto;
  margin-top: 38px !important;
  padding: 20px !important;
  width: 100% !important;
}


/*for folded corner document view  */
/*.thumbnaildiv {
  position: relative;
  width: 30%;
  padding: 1em 1.5em;
  margin: 2em auto;
  color: #fff;
  background: #97C02F;
  overflow: hidden;
}

.thumbnaildiv:before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  border-width: 0 26px 26px 0;
  border-style: solid;
  border-color: #dce1e4 #dce1e4 #bebebe #bebebe;
  background: #658E15;
  -webkit-box-shadow: 0 1px 1px rgba(0,0,0,0.3), -1px 1px 1px rgba(0,0,0,0.2);
  -moz-box-shadow: 0 1px 1px rgba(0,0,0,0.3), -1px 1px 1px rgba(0,0,0,0.2);
  box-shadow: 0 1px 1px rgba(0,0,0,0.3), -1px 1px 1px rgba(0,0,0,0.2);
  
  display: block; width: 0;
}

.thumbnaildiv.rounded {
  -moz-border-radius: 5px 0 5px 5px;
   -webkit-border-radius: 5px 0 5px 5px;
  border-radius: 5px 0 5px 5px;
}

.thumbnaildiv.rounded:before {
  border-width: 8px;
  border-color: #fff #fff transparent transparent;
  -moz-border-radius: 0 0 0 5px;
  -webkit-border-radius: 0 0 0 5px;
  border-radius: 0 0 0 5px;
}    */


/*employee form tabs*/

.sf-nav-step{
    background: #585858 !important;
	color:#ADB3B8 !important;
}
.sf-arrow .sf-nav li.sf-nav-step:after
{
	border-left:26px solid #585858 !important;
}
.sf-active{
    background: #319DB5 !important;
    color: #FFF !important;
}
.sf-arrow .sf-nav li.sf-nav-step.sf-active:after{
	content: "";
	position: absolute;
	right: -24px;
	top: 0;
	width: 0;
	height: 0;
	border-top: 26px solid transparent;
	border-bottom: 26px solid transparent;
	border-left:26px solid #4981FD !important;
}

.lockBg:hover {    
    background-color: #181a1d !important;
}

@media (max-width: 768px) {
    .billingPrintMargin{
        width:90% !important;
    }
}


.logMask {
    display:    block;
    position:   fixed;
    z-index:    10000000;
    top:        0;
    left:       0;
    height:     100%;
    width:      100%;
    opacity: 0.5;
    background: rgba(0, 0, 0, 0.3) 
                /*50% 50% */
                no-repeat;
}

.orgProductLogoSvg path{
	fill:#727272 !important;
}

@media (max-width: 767px) {
    .form16VHead{        
        margin-left: 4% !important;
        width: 93% !important;
        page-break-inside: avoid;
    }
   .form16VTable{        
    font-size: 11px !important;    
    text-align: left;
    width: 97% !important;
   }
   .form16VNotes{
      font-size: 11px !important;	
   }
}

/** Footer popup issue fix **/
.popupspan,.supportspan{
font-size: 98% !important;
opacity: 0.8;
}

@media (max-width: 767px) {
    .alertSettingsFStyle{        
        margin-top: 2% !important;       
    }
   .alertSettingsTStyle{            
        text-align: right !important;   
   }
}

.pageLoader1 {
    background: #fff;
    width: 720px;
    margin: auto;
    margin-top: 60px;
    list-style: none;
    padding: 0;
    -moz-box-shadow: 0px 0px 4px 0px rgba(0,0,0,0.37);
    box-shadow:      0px 0px 4px 0px rgba(0,0,0,0.37);
}
.pageLoader2 {
    width: 180px;
    height: 180px;
    line-height: 176px;
    text-align: center;
    float: left;
    background-repeat: no-repeat;
    background-position: center;
     margin-top: 20%;
    margin-left: 47%;
}
.pageLoader3 {
    vertical-align: middle;
}




/* Adding page load mask -- multi approval in attendance and leave */
/*  http://jsfiddle.net/z8w4vuau/50/   */
#wrapper{
    position:relative;
    background:rgba(0, 0, 0, 0.3) /*50% 50% */ no-repeat;
    height:100%;
}

.profile-main-loader{
    left: 50% !important;
    margin-left:0px;
    position: fixed !important;
    top: 50% !important;
    margin-top: 0px;
    width: 45px;
    z-index: 9000 !important;
}

.profile-main-loader .loader {
  position: relative;
  margin: 0px auto;
  width: 45px;
  height:45px;
}
.profile-main-loader .loader:before {
  content: '';
  display: block;
  padding-top: 100%;
}

.circular-loader {
  -webkit-animation: rotate 2s linear infinite;
          animation: rotate 2s linear infinite;
  height: 100%;
  -webkit-transform-origin: center center;
      -ms-transform-origin: center center;
          transform-origin: center center;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  margin: auto;
}

.loader-path {
  stroke-dasharray: 89,200;
  stroke-dashoffset: -10;
  -webkit-animation: dash 1.5s ease-in-out infinite, color 6s ease-in-out infinite;
          animation: dash 1.5s ease-in-out infinite, color 6s ease-in-out infinite;
  stroke-linecap: round;
}

@-webkit-keyframes rotate {
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}

@keyframes rotate {
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
@-webkit-keyframes dash {
  0% {
    stroke-dasharray: 1,200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 100,200;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 89,200;
    stroke-dashoffset: -124;
  }
}
@keyframes dash {
  0% {
    stroke-dasharray: 1;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 89,200;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 89,200;
    stroke-dashoffset: -124;
  }
}
@-webkit-keyframes color {
  0% {
    stroke: #2B2E33;
  }
  40% {
    stroke: #2B2E33;
  }
  66% {
    stroke: #2B2E33;
  }
  80%, 90% {
    stroke: #2B2E33;
  }
}
@keyframes color {
  0% {
    stroke: #2B2E33;
  }
  40% {
    stroke: #2B2E33;
  }
  66% {
    stroke: #2B2E33;
  }
  80%, 90% {
    stroke: #2B2E33;
  }
}


.butn {
  -webkit-font-smoothing: subpixel-antialiased;
  -webkit-transition: border 0.25s linear, color 0.25s linear, background-color 0.25s linear;
  border: none;
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 10px;
  margin-right: 10px;
  padding: 7px 24px 8px;
  transition: border 0.25s linear, color 0.25s linear, background-color 0.25s linear;
  vertical-align: middle;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.butn i {
  font-size: 10px;
}
.butn:hover {
  color: #ffx;
  outline: 0;
  outline: none;
}

#timeline::before {
  background: -moz-linear-gradient(top, #319db5 0, #dddddd 55%);
  background: -ms-linear-gradient(top, #319db5 0, #dddddd 55%);
  background: -o-linear-gradient(top, #319db5 0, #dddddd 55%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #00a2d9), color-stop(55%, #dddddd));
  background: -webkit-linear-gradient(top, #319db5 0, #dddddd 55%);
  background: #319db5;
  background: linear-gradient(to bottom, #319db5 0, #dddddd 55%);
  content: '';
  height: 500%;
  /*left: 18px;*/
  position: absolute;
  top: 0;
  width: 4px;
}

.time {
    background-color: #d3d3d3 !important;
    -webkit-print-color-adjust: exact; 
}

/** For tooltip z-index is changed **/
.modal-open .modal {
  z-index: 1000 !important;
}
.hr-cancel-circle {
    font-size: 30px;
    top: 13px;
    right: 11px;
    position: absolute;
    color: #d04437;
}

.hr-check-circle {
    font-size: 30px;
    top: 13px;
    right: 11px;
    position: absolute;
    color: #14892c;
}

.widget-infobox .left1 {
  /*float: left;*/
  padding-left: 5%;
  padding-right: 5%;
}

.widget-infobox .left1 i {
  color: #ffffff;
  font-size: 23px;
  text-align: center;
}

.widget-infobox .left1 i::before {
  display: inline-block;
  height: 35px;
  margin-top: 0;
  padding: 5px;
  width: 35px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
}

#tableCustomReports tbody td {
  vertical-align: top;
}

.reports_grid tbody td {
  vertical-align: top;
}


/* End of loading animation for attendance and leave multi approval. */

/*makeadmin's scrollbar fix*/
/*.main-content .page-content .panel.maximized { 
      overflow: scroll;
}*/

.icon-barr {
    width: 100%; /* Full-width */
    background-color: #555; /* Dark-grey background */
    overflow: auto; /* Overflow due to float */
}

.icon-barr a {
    float: left; /* Float links side by side */
    text-align: center; /* Center-align text */
    width: 33.33%; /* Equal width (5 icons with 20% width each = 100%) */
    padding: 2px 0; /* Some top and bottom padding */
    transition: all 0.3s ease; /* Add transition for hover effects */
    color: white; /* White text color */
    font-size: 36px; /* Increased font size */
}

.icon-barr a:hover {
    background-color: #888; /* Add a hover color */
}

.icon-barr .active {
    background-color: #0097a7; /* Add an active/current color */
}


/* subscription css */
.pricing-table {
    box-shadow : none !important ;
    width : 100%;
    margin-left : none !important;
    padding-top : 0% !important;
  }
  
  
  .pricing-table .plan .description div {
      padding-left: 10%;
      padding-right: 10%;
      padding-top: 2% !important;
  }
  
  /* @media(max-width : 1300px) {
    .pricing-table.plan-separated.num-plan-3 .plan {
      margin-right: 3.5%;
      width: 28% !important ;
      margin-top : 3% !important 
  } */
  
  .pricing-table.plan-separated.num-plan-3 .plan {
    margin-left : 3.5%;
    width : 95% !important ;
    margin-top : 10% !important;
    border-radius: 25px !important;
  
  }

  .pricing-table .plan:last-child .plan-header {
    border-top-left-radius: 25px;
    border-top-right-radius: 25px;
  }
  
  .custom-class {
    text-align : center ;
  }
  
  .btn { white-space:normal !important; word-wrap:break-word !important; }
  
  /* .btn:not(.btn-sm):not(.btn-hg):not(.btn-xs):not(.btn-lg):not(.bootstrap-touchspin-up):not(.bootstrap-touchspin-down) {
    width : 100% !important
  } */
  }
  
  
  .btny:not(.btn-sm):not(.btn-hg):not(.btn-xs):not(.btn-lg):not(.bootstrap-touchspin-up):not(.bootstrap-touchspin-down)
  {
    height : 10% !important 
  }
  
  .pricing-table .plan .plan-header .title {
    font-family : "Lato" !important ;
    font-weight : 600 !important ;
    font-size : 15px !important
  }
  
  .pricing-table .plan .description .plan-item {
    font-family : "Lato" !important ;
    font-weight : 300 !important ;
    font-size : 13px !important
  }
  
  .statustag{
    margin-top:-10px !important;
  }
  
  .statustag .active{
    background-color:#4ca64c !important;
  }
  
  .statustag .locked{
    background-color: #ff6666 !important;
  }
  
  .pricing-table .plan .statustag  {
    font-size : 10px !important
  }
  
  .pricing-table .plan .plan-header .active, .pricing-table .plan .plan-header .locked
  {
    padding : 3px 12px !important;
  }
  
  .activebutton
  {
    background-color:#bdc3c7 !important;
  }

  .self-approval-info-text {
    font-weight: 700;
    color : black;
  }



.payout-cancel-btn-color{
    color: #ea4b5a !important;
}

.payout-search-field {
    margin: 38px 20px -10px 20px;
    padding: 0px 10px;
    box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.12), 0 1px 6px 0 rgba(0, 0, 0, 0.12);
    transition: box-shadow 0.28s cubic-bezier(0.4, 0, 0.2, 1);
}

.payout-warning-content{
    font-weight: 500 !important;
    font-size: 24px;
    padding: 20px !important;
    text-align: center;
    color: #000;
}
.warning-text{
  font-size: 20px !important;
  text-align: center;
  padding: 20px !important;
}
.payout-warning-footer-div{
    text-align: center;
    padding: 30px 0px 50px !important;
}

    .payout-title{
        color: #888383;
        font-size: 1.3em;
        font-weight: 500;
        margin-bottom : 10px;
    }

    .payout-history-title {
        margin-top: 30px;
    }

    .payout-filter-options {
      margin-top: 30px;
      padding: 0px !important;
    }

    .payout-content{
        font-size: 1.5em;
        line-height: 50px;
        color : #747272;
    }

    .payout-card-border
    {       
        border-radius: 5px !important;
    }

    .payout-card-image-div{
        height: auto !important;
    }

    .payout-month-card-cls{
        float: right;
    }
    .payout-card-width-cls{
        width: 200px;
        cursor: pointer;
    }

    .payout-history-panel {
        background-color: white;
        width: 100%;
        padding: 50px;
    }

    .payout-line {
        margin: 10px 20px 30px 20px;
        height: 2px;
        background-color: #eee;
    } 

  .payout-history-card-font {
    white-space : nowrap;
    font-size: 12px;
  }

  .payout-history-card-white{
      color : #ffffff;
      font-weight: 500;
  }

  .payout-history-alert-img {
    width: 20em;
    margin-left: auto;
    margin-right: auto;
    padding: 50px;
  }

  .payout-history-warning-img {
    width: 10em;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 50px;
  }

  .payout-history-search-img {
    width: 20em;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 30px;
  }
  
  .modal-full{
    margin-top: 3% !important;
  }
  .payout-history-logo-and-view-button {
     display: flex;
     flex-direction: row-reverse;
     justify-content: space-between;
     margin: 22px;
     align-items: center;
  }
  
  .view-payout-history-transactions {
      display: flex;
      flex-direction: row-reverse;
      margin-top: 1.5em;

  }
  @media screen and (max-width: 600px){ 
    justify-content: center;
  }
  .connected-banking-logo {
    width: 130px;
    margin-left: auto;
    margin-bottom: 10px;
    height: 40px;
  }
  .connected-banking-logo-payout-history {
    width: 130px;
    height: 40px;
  }

  .connected-banking-logo-payout-history-view {
    width: 130px;
    height: 40px;
    margin : 0px 30px 10px auto;
  }

  .connected-banking-logo-payout {
    width: 130px;
    height: 40px;
    margin : 20px 20px 10px auto;
  }

    .chip {
    border-color: #f0edee;
    border-width: 1px;
    border-style: solid;
    border-radius: 6px;
    min-width: 50px;
    max-width: fit-content;
    color: var(--primary-color);
    position: relative;
    display: flex;
    align-items: center;
    height: 25px;
    font-size: 10px;
    font-weight: 100;
    line-height: 32px;
    padding: 0px 5px;
    box-shadow: aqua;
    box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.12), 0 1px 6px 0 rgba(0, 0, 0, 0.12);
    transition: box-shadow 0.28s cubic-bezier(0.4, 0, 0.2, 1);
}

  .chip-name {
    max-width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left: auto;
    margin-right: auto;
  }
  
  .chip-button-close {
    color: gray;
    cursor: pointer;
    display: inline-block;
    border-radius: 50%;
    right: -11px;
    height: 35px;
    width: 15px;
    font-size: 17px;
    font-weight: bold;
    text-align: center;
  } 

.payout-history-view-card-success {
    background: #4caf50;
    width: 10px;
    height: 100%;
    position: absolute;
    border-radius: 25px 0px 0px 25px;
  }
    
  .payout-history-view-card-failure {
    background: #ff0000;
    width: 10px;
    height: 100%;
    position: absolute;
    border-radius: 25px 0px 0px 25px;
  }

  .payslip-generation-pending-card {
    background: #4981FD;
    width: 10px;
    height: 100%;
    position: absolute;
    border-radius: 25px 0px 0px 25px;
  }

  .payslip-generation-pending-approval-card {
    background: #FF8A22;
    width: 10px;
    height: 100%;
    position: absolute;
    border-radius: 25px 0px 0px 25px;
  }
  .payslip-daterange-pointer-cls{
    pointer-events: none;
  }

  .generated-payslip-text{
    font-size:10px;
    color:green
  }

  .pending-approval-text{
    font-size:10px;
    color:#FF8A22
  }

  .generatedColorCode{
    width: 20px;
    background: green;
    border: 1px;
  }
  .pendingApprovalColorCode{
    width: 20px;
    background: #FF8A22;
    border: 1px;
  }

  .readyGenColorCode{
    width: 20px;
    background: #4981FD;
    border: 1px;
  }

  .ready-to-generate-text{
    font-size:10px;
    color:#4981FD;
  }

  .payslip-monthly-card-margin-cls{
    margin: auto;
  }

  .payslip-multi-chip-name{
    max-width: 100%;
    margin-left: auto;
    margin-right: auto;
  }

  .payslip-visible-button{
    display:block !important;
    color: #FFF !important;
    cursor: pointer;
    font-family: RobotoDraft,Roboto,Helvetica Neue,Helvetica,Arial,sans-serif !important;
    letter-spacing: 1px;
    font-size: 14px;
    border-radius: 25px !important;
    border : none !important;
  }

  .clock-size{
    margin-top: 3%;
    font-size: 50px !important;
    color: red;
  }

  .geo-coordinates-alert-content {
    justify-content: center; display: flex; text-align: center; padding: 30px;
    color: black;
  }

  @media screen and (max-width: 768px){
    .payslip-visible-button{
        display:block !important;
        margin-bottom:10px;
        margin-left: 20px;
        margin-right: 20px;
        text-align: center;
        font-family: RobotoDraft,Roboto,Helvetica Neue,Helvetica,Arial,sans-serif !important;
        letter-spacing: 1px;
        font-size: 14px;
    }
  }

  .payslipCardTransition:hover{
    box-shadow: 0 5px 15px rgba(0, 0, 0, .3) !important;       
  }
  
  .payslipPayrollHeading{
    text-align: right;
  }

  .payslip-generation-multi-chip{
    display: -webkit-flex; /* Safari */
    -webkit-flex-wrap: wrap; /* Safari 6.1+ */
    display: flex;
    flex-wrap: wrap;
  }

  .payslipCardSelect{
    -ms-transform: scale(1.2); /* IE 9 */
    -webkit-transform: scale(1.2); /* Safari 3-8 */
    transform: scale(1.2); 
  }

  .payslip-salary-date-input{
    width: 205px;
    padding: 9px;
    border: 2px solid #b7afaf;
    border-radius: 7px;
  }
  .payslipStatusList{
    margin: auto;
    top: 10px;
    float: none;
  }

  .payslipStatusCls{
    margin-top:17px;
  }
  
  .payslipStatusTitle{
    margin-top: 8px;
    text-align: center;
  }
  
  .payslipWizardDisableButton{
    pointer-events: none;
    background: #c8d4d4 !important;
    border: 1px !important;
  }

  .payslipMonthListSelect{
    width: 205px;
  }
  
  .payslipGenerationNote{
    text-align:right;
    color:red
  }
  
  .payout-history-view-card-inprogress {
    background: #FFC107;
    width: 10px;
    height: 100%;
    position: absolute;
    border-radius: 25px 0px 0px 25px;
  }

  .payout-history-view-panel {
    padding : 0px;
    border-radius: 10px !important;
    margin-bottom: 20px;
  }

  .payout-history-warning-msg {
    margin-bottom: 40px;
  }

  .self-approval-heading {
    margin-top: 20px;
    margin-bottom: 20px;
    font-size: 15px;
    font-weight: 500;
  }

  .payout-history-view-card-row {
    padding: 5px 5px 5px 15px;
    font-size: 10px;
  }

  .payout-history-card-content{
      line-height: 1.5;
  }

  .payout-history-img {
    position: absolute;
    top: 15%;
    left: 30%;
    height: 70%;
    width: 70%;
  }

  .payout-history-img-parent {
    position: absolute;
    height: 100%;
  }

  .payout-history-view-left-panel{
    margin-left: -15px;
  }

  .payout-history-long-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

    .payout-history-view-amount {
      font-weight: 900;
    }

    .payout-history-view-status {
        margin-top: 20px;
    }

    .payout-history-view-status-text {
        padding: 10px;
    }

    .payout-card-checkbox {
        position: absolute;
        right: 10px;
        top: 3px;
    }

    .payout-text-right-cls{
      text-align: right;
    }

    .p-payout{
      margin-top:5px;
    }

    .view-payslip-payout{
      color: #4981FD;
      cursor: pointer;
      float: right;
    }

    .p-card-title{
      text-align:center;
      font-weight: 700;
      color: #000;
      display: block;
      padding: 5px;
      font-size: 13px;
    }

    .p-card-content{
      padding: 0px 0px 10px !important;
      text-align: center;
      font-size: 13px;
    }
  
    .payout-card-div-padding{
      padding: 40px 20px;
    }

    .payout-error-img{
        padding:10px;        
    }

    .payout-common-error-img{
        width: 110px;
        height: 100px;  
    }
    .warning-img{
        width:110px;
        height:100px;
        padding:10px;
    }
    .payout-warning-btn{
        font-weight: 500;
        box-shadow: none;
        background: #fff;
        border-color: #f3f0f0 !important;
    }
    .payout-tx-popup-opacity{
        opacity: 1.5 !important;
        background: rgba(78, 43, 43, 0.3) !important;
    }
    .payout-tx-popup-shadow{
        border-radius: 5px;
        box-shadow: 0 27px 24px 0 rgba(202, 91, 91, 0.2), 0 40px 77px 0 rgba(224, 197, 197, 0.2);
        border-color: #e08383;
    }

  .payout-otp-text{
      font-size: 14px;
      text-align: center;
      color: #696868;
      font-weight: 200;
      margin-top:10px;
  }
  
  .select-payout-checkbox{
      font-size: 12px;
      margin-top: 17px;
  }
  .deselect-payout-checkbox{
      font-size: 12px;
      margin-top: 17.5px !important;
  }

  .payout-btn {
    border-radius: 5px !important;
    color : #9ea9b0 !important;
    border: solid 0px !important;
  }

  .payout-search-option {
      padding-right : 50px;
  }

  .payout-filter-btn {
      width : 100%;
  }

  .payout-history-status-integator-pending {
    background: rgb(255, 193, 7);
    width: 50px;
    height: 10px;
    position: absolute;
    margin-top: 6px;
    margin-bottom: auto;
  }

  .payout-history-status-integator-success {
    background: #4caf50;
    width: 50px;
    height: 10px;
    position: absolute;
    margin-top: 6px;
    margin-bottom: auto;
  }

  .payout-history-status-integator-failed {
    background: #ff0000;
    width: 50px;
    height: 10px;
    position: absolute;
    margin-top: 6px;
    margin-bottom: auto;
  }

  .payout-history-status-label {
    margin-left: 70px;
    font-size: 10px;
  }

  .payout-status-legend {
      margin-top: 15px;
      padding: 0px !important;
  }

  .payout-text-center-cls {
    text-align: center;
  }

  .payout-inline-block{
    display: inline-block;
  }

  .arrow-up {
    margin-top: -30px;
    margin-bottom: 20px;
    width: 0;
    height: 0px;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid rgba(255, 255, 255, 1.84);
    margin-left: 0px;
  }

  .arrow-up-border {
    margin-top: -30px;
    margin-bottom: 20px;
    width: 0;
    height: 0px;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid rgba(170,185,188, 1.84);
    z-index: 1;
  }

    .payout-filter-btn-group {
      margin: 20px 4px 0px 4px;
      text-align: center;
    }

    .payout-filter-dropdown {
        min-width: 100%;
        padding: 20px !important;
        margin-top: 15px;
        padding-bottom: 50px;
    }

    .payout-filter-panel-header {
        height: 50px;
    }

    .payout-filter-reset-btn {
        color: #4981fd !important;
        border-radius: 5px !important;
        margin-left: 20px !important;
        background-color: transparent !important;
        border: 1px solid #4981fd!important;
    }

    .payout-filter-title {
        padding-top: 5px;
    }

    .payout-profile-img {
        width: 60px;
        margin-left: auto;
        margin-right: auto;
        height: 60px;
        margin-top: 20px;
        margin-bottom: auto;
    }

    .payout-history-profile-img {
        width: 60px;
        margin-left: auto;
        margin-right: auto;
        height: 60px;
        margin-top: 20px;
        margin-bottom: auto;
    }

    .payout-check-box{
      margin:0;
      text-align: right;
    }

    .payout-salary-div{
      padding-right: 0;
    }

    #payoutOtp-error{
      text-align: center;
      margin-top: 0px !important;
    }

    .payout-history-footer {
        margin-bottom: 10px;
    }

    .pageLoadingMask {
        display: block;
        position: fixed;
        z-index: 10000000;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        opacity: 0.5;
        background: #393c3e /*50% 50% */ no-repeat;
    }
    
    .warning-err-img{
      width:100px;
    }
    
    .payout-modal-footer{
      padding-bottom: 20px !important;
    }

    .otp-popup-header{
      color: #000;
      font-weight: 500;
      font-size: 2em;
    }

    .otp-popup{
      margin-left: 0px !important;
    }

    .header-profile-img{
      height: 35px;
      width: 35px !important;
    }
    .otp-resend-button{
      border-radius: 5px;
    }

    .otp-input-main-div{
      margin-bottom:0px !important;
    }

    .payout-otp-success-btn-color{
      color: #2a2525 !important;
    }

    .otp-payout-modal-footer{
      padding-bottom: 30px !important;
    }

    .otp-input-div{
      margin-top:10px;
    }

    .payout-error-popup-btn{
      border-radius: 5px;
      width: 100px !important;
    }

    .tax-regime-notify-bar{
          background: #e43383;
          min-height: 40px;
          margin-left: -29px;
          margin-top: -23px;
          position: fixed;
          width: 100%;
          z-index: 1;
    }

    .compare-and-choose-button{
      margin: 0px;
      color: #e43383 !important;
      border-radius: 20px;
      background: white !important;
      word-break: break-word;
    }
    .tax-recommand-label{
      background-color: #03a9f4;
      width: fit-content;
      padding: 0.2em 0.4em;
      color: white;
      border-radius: 5px;
      font-weight: 500;
      text-align: center;
      margin-left: 0.3em;
    }
    .tax-alert-content{
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      color: white;
      padding: 0.4em;
    }

    .select-tax-ragime-header{
      background-color: #ffe9f7 !important;
      color: black;
      font-size: 1.5em;
      text-transform: inherit;
      font-weight: 500;
      display: flex;
      align-items: center;
    }
    .select-tax-ragime-body-content{
       display: flex;
       flex-direction: column;
       justify-content:center;
       align-items: center;
       padding: 25px;
    }
    .tax-regime-heading {
      font-size: 1.3em;
      font-weight: bold;
      color: #200023;
      margin-bottom: 0px;
    }
    .regime-heading-with-tag{
      display: flex;
      flex-wrap: wrap;
      align-items: baseline;
      margin-bottom: 0.5em;
    }
    .tax-regime-sub-content {
      font-size: 1.2em;
      color: #200023;
      font-weight: 500;
    }
    .tax-block-box{
        width: 100%;
        display: flex;
        flex-direction: row;
        margin-bottom: 25px;
        align-items: center;
        padding: 1em;
        border-radius: 0.7em;
        box-shadow: 1px 2px 22px -6px #b4b4b4;
        cursor: pointer;
    }
    .tax-block-box-selected{
        border: 2px solid var(--primary-color);
    }
    .income-label-box{
      width: 100%;
      padding-bottom: 25px;
      padding-top: 5px;
    }
    .tax-action-button{
      display: flex;
      justify-content:center;
      padding-bottom: 1em;
    }
    .additional-top-margin{
      margin:3em;
    }
     @media screen and (max-width : 450px)
    {
      .select-tax-ragime-body-content{
        padding: 0px;
      }
    } 
     @media screen and (max-width : 970px)
    {  
      .tax-regime-notify-bar{
        margin-left:-5px;
      }
    }    
    @media screen and (max-width : 600px){
      .additional-top-margin{
        margin: 4em;
      }
    }
     @media screen and (max-width : 360px){
      .additional-top-margin{
        margin: 5em;
      }
      .modal-content .modal-body{
        padding:10px 12px 16px 12px;
      }
      .modal-dialog{
        margin:0px
      }
      .tax-block-box {
        padding: 0.5em;
      }
    }
    .icici-banner-parent-row{
      margin-left: -20px !important;
      margin-right: -20px !important;
      margin-bottom: 30px;
    }

    .icici-banner-child-div{
      padding:0px;
    }

    .banner-c1{		
      height:90px;
      padding-left:0px !important;
      padding-right: 0px !important;
      transform: translate3d(0px, 0px, 0px);
      background-repeat: no-repeat;
      background-position: 50% 50%;
      background-size: cover;
      border-top-left-radius: 15px !important;
      border-bottom-left-radius: 15px !important;

    }

    .icici-banner-mask{
      height: 90px;
      width: 100%;
      position: absolute;
      opacity: 0.8;
      background-color: black;
      border-top-left-radius: 15px !important;
      border-bottom-left-radius: 15px !important;
    }

    .icici-banner-logo{
      height:auto;
      width:176px;
    }

    .icici-banner-c1-a1 {
      left: 10px;
      position: absolute;     
      margin : 40px 20px 10px 0px;
      animation: iciciLogoA1 7s cubic-bezier(0.175, 0.885, 0.32, 1.275) 0s infinite normal;
    }

    @keyframes iciciLogoA1 {
      from {bottom: -130px; opacity:0;}
      11%{
        opacity: 0;
      }

      12%{
        opacity: 1;
      }       
      to {bottom: -80px;}
    }

    .icici-banner-font{
      color: #fff;
      font-style: italic;
    }

    .banner-c1-t2-parent{
      padding-right: 10px;
    }

    .banner-c1-t2-row{
      margin-left: 0px !important;
      margin-right: 0px !important;
    }

    .banner-c1-t2-text1{
      margin-top: 10px !important;
    }

    .banner-c1-t2-text2{
      margin-bottom: 5px;
      color: rgb(255, 255, 255); font-weight: 800; font-family: "Open Sans";
      float:right;
    }

    .banner-c1-t2-text3{
      margin-bottom: 5px;
      font-size: 0.83em; 
      text-align: right;
      line-height: 1.4;
      letter-spacing: 0px;
      float: right;
    }

    .banner-c2{
      padding-left:15px !important;
      padding-top:10px;
      height:90px;
      border-top-right-radius: 15px;
      border-bottom-right-radius: 15px;
    }

    .icici-banner-start{
      border: 0px solid rgb(0, 0, 0);      
      background-color: #E4711E;
      background-image: linear-gradient(to bottom, #E4711E, #ba4037);
      opacity: 1; 
      height: 43px;
      line-height: 43px;
      margin-left: 0px;
      margin-top: 0px;
      margin-bottom: 5px !important;
      text-align:center;
    }


    .icici-banner-start-text{      
      padding-left: 7px;
      font-style: normal; 
      font-weight: bold; 
      font-size: 14px; 
      letter-spacing: 8px;
      color: #fff;
      font-family: RobotoDraft,Roboto,Helvetica Neue,Helvetica,Arial,sans-serif;
    }

    .banner-c1-t2-text2-font{
      font-size:1.85em;
    }

    .icici-banner-register{
      font-size: 0.83em;
      text-align: center;
    }

    .banner-current-account{
      color: #fff;
      text-decoration: underline;
    }

    .bannerFadeIn {
      -webkit-animation: bannerFadeIn 7s linear 7s infinite;
      animation: bannerFadeIn 7s linear 7s infinite;
    }

    .custom-loading-cls {
      background: #dad8d8;
      color: #666666;
      position: fixed;
      height: 100%;
      width: 100%;
      z-index: 5000;
      top: 0;
      left: 0;
      float: left;
      text-align: center;
      opacity: .80;
    }

    
    .custom_spinner {
        margin: 0 auto;
        text-align: center;
        margin-top: 50vh;
    }
    
    #btn--yp {
      box-sizing: content-box;
      position: fixed;
      z-index: 9;
      bottom: 1em;
      right: 1em;
      border: solid 1em transparent;
      width: 4.625em;
      height: 3.25em;
      background: url(https://s3-us-west-2.amazonaws.com/s.cdpn.io/2017/icon-yp.svg) 50%/cover content-box;
      font: 16px/ 1.25 trebuchet ms, sans-serif;
      text-indent: 200vw;
      text-shadow: none;
      filter: grayscale(1) drop-shadow(0 0 1px #e8e0e0);
      transition: .5s;
      white-space: nowrap;
    }
    #btn--yp:before {
      box-sizing: inherit;
      position: absolute;
      left: 0;
      bottom: 100%;
      margin: 1em -.5em;
      padding: .5em;
      width: 100%;
      border-radius: 5px;
      background: #e8e0e0;
      color: #000;
      text-align: center;
      text-decoration: none;
      text-indent: 0vw;
      white-space: normal;
      animation: float 1s ease-in-out infinite alternate;
      content: attr(data-txt);
    }
    #btn--yp:hover, #btn--yp:focus {
      outline: none;
      filter: grayscale(0) drop-shadow(0 0 1px crimson);
    }

    #payslipTemplateWarning{
      font-size : 75px !important; 
      color:orange !important
    }

    #payslipTemplateModalDescription{
      font-size:1.3em;
      padding-left:3em; 
      padding-bottom:2em;
      padding-right:3em;
      text-align:center;
    }

    #payslipTemplateCloseButton{
      text-align:center;    
      padding-bottom: 3em;
    }

    @keyframes float {
      to {
        transform: translateY(0.75em);
      }
    }
    
    .custom-loading-cls {
      background: white;
      position: fixed;
      height: 100%;
      width: 100%;
      z-index: 5000;
      top: 0;
      left: 0;
      float: left;
      text-align: center;
      background-repeat: no-repeat;
      background-size: cover;
      /* background-image: url('./app-loader-bg.png'), linear-gradient(white,white); */
      background-blend-mode: darken;
      opacity: 0.85;
    }
    
    @media screen and (max-width: 960px){
      .custom-loading-cls {
        background-image: none !important;
      }
    }
    
    .loader {
      height: 20px;
      width: 450px;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      margin: auto;
    }
    .loader--dot {
      animation-name: loader;
      animation-timing-function: ease-in-out;
      animation-duration: 3s;
      animation-iteration-count: infinite;
      height: 20px;
      width: 20px;
      border-radius: 100%;
      background-color: black;
      position: absolute;
      border: 2px solid white;
    }
    .loader--dot:first-child {
      background-color: #84db39;
      animation-delay: 0.5s;
    }
    .loader--dot:nth-child(2) {
      background-color: #804bbc;
      animation-delay: 0.4s;
    }
    .loader--dot:nth-child(3) {
      background-color: #fa3c5a;
      animation-delay: 0.3s;
    }
    .loader--dot:nth-child(4) {
      background-color: #e58315;
      animation-delay: 0.2s;
    }
    .loader--dot:nth-child(5) {
      background-color: #3bade3;
      animation-delay: 0.1s;
    }
    .loader--dot:nth-child(6) {
      background-color: #f6e61a;
      animation-delay: 0s;
    }
    .custom-loading-cls {
      background: white;
      position: fixed;
      height: 100%;
      width: 100%;
      z-index: 5000;
      top: 0;
      left: 0;
      float: left;
      text-align: center;
      opacity: 0.8;
    }
    
    @keyframes loader {
      15% {
        transform: translateX(0);
      }
      45% {
        transform: translateX(230px);
      }
      65% {
        transform: translateX(230px);
      }
      95% {
        transform: translateX(0);
      }
    }
    
    .dot {
      background: var(--secondary-color);
    }
    .dot, .dot:after {
      display: inline-block;
      width: 2em;
      height: 2em;
      border-radius: 50%;
      animation: a 1.5s calc(((var(--i) + var(--o, 0))/var(--n) - 1)*1.5s) infinite;
    }
    .dot:after {
      --o: 1;
      background: #400059;
      content: "";
    }
    
    @keyframes a {
      0%, 50% {
        transform: scale(0);
      }
    }
    
    @keyframes rotate {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }
    
    @-webkit-keyframes bannerFadeIn {
      from {
        opacity: 0;
      }
    
      to {
        opacity: 1;
      }
    }
    
    @keyframes bannerFadeIn {
      from {
        opacity: 0;
      }
    
      to {
        opacity: 1;
      }
    }

    .banner-start-a3 {
      -webkit-animation: startRotation 7s linear 7s infinite;
      animation: startRotation 7s linear 7s infinite;
    }

    @keyframes startRotation {
      from {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
        opacity: 0;
      }
    
      40% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
      }
    
      60% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
        opacity: 1;
      }
    
      80% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
      }
    
      to {
        -webkit-transform: perspective(400px);
        transform: perspective(400px);
      }
    }

    @-webkit-keyframes startRotation {
      from {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
        opacity: 0;
      }
    
      40% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
      }
    
      60% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
        opacity: 1;
      }
    
      80% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
      }
    
      to {
        -webkit-transform: perspective(400px);
        transform: perspective(400px);
      }
    }

    .banner-start-a3 a:hover{
      color: #fff !important;
    }

    .icici-banner-start a:link{
      text-decoration: none;
    }

    .banner-start-a3 a:visited{
      color: #fff;
    }

    .payout-filter-img{
      display: inline-block;
      width: 1em;
      padding-bottom: 2px;
      margin-right: 10px;
    }

    .payout-history-refresh-status{
      float: right;
      /* background-color: #319DB5; */
      /* color: #fff; */
      line-height: 25px;
      border-radius: 3px;
      padding: 5px;
      cursor: pointer;
      box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
      background-color: #fff;
    }

    .payout-history-search-row{
      margin-left: 4px !important;
      margin-right: 0px !important;
    }

    .payout-history-filter-row{
      padding-left: 4px !important;
      padding-right: 0px !important;
    }

    .payout-history-filter-row-parent{
      padding-left: 0px !important;
    }

    .payout-history-filter-chip{
      padding-left: 4px !important;
    }

    .payout-history-panel-layout{
      padding-top: 50px;
      padding-bottom: 50px;
    }

    .dashboard-self-approval-notification-height{
      height: 140px !important;
    }

    .dashboard-payment-notification-height{
      height: 137px !important;
    }

    /* Dashboard notification alerts z-index */
    #noty_topRight_layout_container {
      z-index: 200 !important;
    }

    .payout-history-month{
      text-align: center;
      font-weight: bold;
    }

    .payout-history-card {
      line-height: 30px;
      white-space: nowrap;
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      background: linear-gradient(135deg, var(--primary-color), #74007D) !important;
      border-radius: 4px !important;
      transition: box-shadow .5s ease, transform .2s ease; 
      will-change: transform;
      box-shadow: 0 2px 5px rgba(0, 0, 0, .2) !important;
      transform:
          translateY(var(--ty, 0))
          rotateX(var(--rx, 0))
          rotateY(var(--ry, 0))
          translateZ(var(--tz, -12px));
    }
      
    .parent-payout-history:hover::before {
        box-shadow: 0 5px 15px rgba(0, 0, 0, .3) !important;       
    }
  
      
    .payout-history-card::after {
        position: relative;
        display: inline-block;
        content: attr(data-title);
        transition: transform .2s ease; 
        font-weight: bold;
        letter-spacing: .01em;
        will-change: transform;
        transform:
          translateY(var(--ty, 0))
          rotateX(var(--rx, 0))
          rotateY(var(--ry, 0));      
    }
    
    .parent-payout-history {
      align-items: center;
      transform-style: preserve-3d;
      transform: perspective(800px);
    }

    .payout-tx-history-footer{
      color: #fff;
    }

    .payslip-template-grapesjs-close {
      font-size: 35px;
      padding-right: 3px;
      display: none;
    }
  
    .payslip-template-grapes-js-module {
      min-height:90px;
      overflow:hidden;
      padding-top:10px
    }

    .payslip-template-default-tag{
      border-radius: 7px;
      background: #ffc042;
      padding: 5px;
      font-size: 10px;
    }

    .payslip_netpay_inwords{
      border: 1px solid;
      border-top: none;
    }
    
    .payslip_netpay{
      padding-left: 5px;
    }

    .netpay_body{
      display: inline-block;
      padding: 10px;
    }
    
    .contribution_hidden{
      height: 40px;
      display: none
    }

    .org_contribution{
      text-align:left;
      height:40px;
      background:lightgray;
      padding-top: 10px; 
      padding-left: 15px
    }

    .org_contribution_tab{
      width:100%
    }

    .org_contribution_table{
      width:100%;
      border-left:1px solid
    }
    
    .urlBox{
      border: 1px solid;
      height: 60px;
      border-radius: 7px;
    }
    .urlAlignment{
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      text-align: center;
      padding: 15px;
    }
    
    .pinAlignment{
      text-align: center;
        padding: 19px;
    }
    
    .circleStyle{
      height: 46px;
        width: 46px;
        border-radius: 50%;
        margin-top: -9px;
        display: inline-block;   
    }

    .viewInvoiceDetails{
      max-width: 100px;
    }

    .descriptionData{
      overflow-wrap: break-word;
    }

    @media (max-width: 991px) {
      .col-negative-margin-left{
        margin-left: none;
      }
    }

    @media (min-width: 992px) {
      .col-negative-margin-left{
        margin-left: -20px !important;
      }
    }

    @media(max-width: 1601px){
      .payslipStatusTitle{
        text-align: left;
      }
    }

    @media (max-width: 991px) {
      .payslip-template-grapes-js-module {
        padding: 20px;
      }
      .payslip-template-grapesjs-close {
        padding: 2% !important;
      }
      .payslipMonthList{
        width: 205px;
        float: none;
        margin: auto;
      }
      .payslipSalaryDate{
        float: none;
        margin: auto;
        width: 210px;
        margin-top: 30px;
      }
      .payslipPayrollHeading{
        text-align: center;
      }
    }
    
    @media screen and (min-width: 350px){
      .payout-history-card{
        margin: 20px 5px;
      }
    }
    @media screen and (max-width: 350px){
      /* Fix for notification alert responsiveness in dashboard */
      #noty_topRight_layout_container, .notyselfApprovalAlertContainer,.notyPaymentAlertContainer, .notyOutageNotificationContainer {
          width : 100% !important;
          right: 0px !important;
      }
    }

    @media screen and (max-width: 768px){
      /* To make the sidebar clickable when the notification alert is shown in dashboard */
      #noty_topRight_layout_container {
          top: 30px !important;          
      }
    }
    
    @media screen and (min-width: 351px){
      /* Fix for notification alert responsiveness in dashboard */
      #noty_topRight_layout_container, .notyselfApprovalAlertContainer,.notyPaymentAlertContainer, .notyOutageNotificationContainer {
        width : 350px !important;
      }
    }



    @media screen and (max-width: 400px){
      .payout-select-box-div{
        width:265px; 
        padding: 10px;
      }
    }

    @media screen and (min-width: 401px){
      .payout-select-box-div{
        width:300px; 
        padding: 10px;
      }
    }

    @media screen and (max-width: 767px){
      .payout-history-refresh{
        margin-top:10px !important;
      }
    }

    @media screen and (min-width: 991px) and (max-width: 1199px){
      .payout-history-refresh{
        margin-top:10px !important;
      }
    }

    @media screen and (max-width: 384px){
      .banner-c2{
        padding-left:6px !important;
      }
      
      .banner-c1-t2-text2-font{
        font-size:0.63em;
      }
    }

    @media screen and (max-width: 500px){
      .banner-c1-t2-text1{
        margin-top: 20px !important;
      }
    }

    @media screen and (min-width: 385px) and (max-width: 500px){
      .banner-c1-t2-text2-font{
        font-size:1em;
      }
      
      .banner-c1-t2-text3{
        font-size: 0.5em;
      }

      .banner-c2{
        padding-left:12.5px !important;
      }
    }

    @media screen and (min-width: 501px) and (max-width: 768px){
      .banner-c1-t2-text2-font{
        font-size:1.3em;
      }
    }

    @media screen and (max-width: 768px){
      .icici-banner-logo{
        height:auto;
        width:100px;
      }

      .banner-c2{
        padding-top:30px !important;
      }

      .icici-banner-start{
        height: 25px;
        line-height: 25px;
      }

      .icici-banner-start-text{
        font-size: 10px;
        letter-spacing: 5px;
      }
    }

    @media screen and (min-width: 769px) and (max-width: 990px){
      .icici-banner-logo{
        height:auto;
        width:120px;
      }
    }

    @media screen and (min-width: 991px) and (max-width: 1513px){
      .icici-banner-logo{
        height:auto;
        /* width:100%; */
        width:140px;
      }
    }

    @media screen and (min-width: 769px) and (max-width: 1553px){
      .icici-banner-start{
        height: 33px;
        line-height: 33px;
      }
    }

    @media screen and (max-width: 768px){
      .payout-warning-modal-width{
        width: auto;   
      }

      .otp-input{
        width: 260px;
        text-align: center;
        height: 50px;
        border-radius: 5px;
        border: 2px solid;
        font-size: 2em;
        border-color: #f3f0f0 !important;
      }

      .otp-popup-button{
        width: 124px !important;
        border-radius: 5px;
        height: 40px !important;
      }
    }

    @media screen and (min-width: 768px){
      .payout-warning-modal-width{
        width: 400px !important;   
      }

      .otp-input{
        width: 360px;
        text-align: center;
        height: 50px;
        border-radius: 5px;
        border: 2px solid;
        font-size: 2em;
        border-color: #f3f0f0 !important;
      }

      .otp-popup-button{
        width: 177px !important;
        border-radius: 5px;
        height: 40px !important;
      }
    }

    @media screen and (max-width: 600px){
      .list-payout{
        padding-left: 0px !important;
        padding-right: 1px !important;
      }
    }
    @media screen and (min-width: 1200px) and (max-width:1500px){
        .payout-history-profile-img {
            width: 45px;
            margin-left: auto;
            margin-right: auto;
            height: 45px;
            margin-top: 20px;
            margin-bottom: auto;
        }
    }

    @media screen and (max-width:320px){
        .payout-profile-img {
            width: 35px;
            margin-left: auto;
            margin-right: auto;
            height: 35px;
            margin-top: 20px;
            margin-bottom: auto;
        }
    }

    @media screen and (min-width: 321px) and (max-width:420px){
        .payout-profile-img {
            width: 40px;
            margin-left: auto;
            margin-right: auto;
            height: 40px;
            margin-top: 20px;
            margin-bottom: auto;
        }
    }

    @media screen and (min-width: 420px) and (max-width:1500px){
        .payout-profile-img {
            width: 45px;
            margin-left: auto;
            margin-right: auto;
            height: 45px;
            margin-top: 20px;
            margin-bottom: auto;
        }
    }

    @media screen and (min-width: 1024px) {
        .payout-history-card-img {
            margin-top : 1.5em;
        }
    }

  @media screen and (max-width: 991px) {
    .payout-month-card-cls{
        float: unset;
    }
    .payout-sm-card-center-cls{
        text-align: center;
    }
    .payout-sm-card-inline-block{
        display: inline-block;
    }
  }

  @media screen and (min-width: 1270px) {
    .cardsclass {
      height : 100px;
    }
  }
  
  @media screen and (min-width: 1200px) and (max-width: 1270px) {
    .cardsclass {
      height : 120px;
    }
  }
    
  @media screen and (min-width: 1035px) and (max-width: 1200px) {
    .cardsclass {
      height : 170px;
    }
  }
  
  @media screen and (min-width: 1025px) and (max-width: 1035px) {
    .cardsclass {
      height : 122px;
    }
  }
  
  @media screen and (max-width: 1025px) {
    .cardsclass {
      height : 126px;
    }
  }
  
@media screen and (max-width: 768px) {

    .payout-history-panel {
        background-color: white;
        width: 100%;
        padding: 50px 0px;
    }
  
}

@media screen and (min-width: 768px) and (max-width: 1280px) {

    .payout-history-panel {
        background-color: white;
        width: 100%;
        padding: 50px 50px;
    }
    .connected-banking-logo-payout-history {
        width: 130px;
        height: 40px;
      }
}

@media screen and (min-width: 1280px) {

    .payout-history-panel {
        background-color: white;
        width: 100%;
        padding: 50px 100px;
    }
}

@media screen and (min-width: 420px) and (max-width: 1024px) {

    .payout-history-card-panel {
        margin : 0% 5%;
    }
}

@media screen and (min-width: 1280px) {

    .payout-history-card-panel {
        margin : 0% 0%;
    }
}

@media screen and (max-width: 420px) {
  
    .payout-history-card-panel {
        margin : 0%;
    }
    .connected-banking-logo-payout {
        width: 130px;
        height: 40px;
        margin : 30px auto 0px auto;
    }
    .connected-banking-logo-payout-history {
        width: 130px;
        height: 40px;
        margin-bottom:15px;
    }
    .connected-banking-logo-payout-history-view {
        width: 130px;
        height: 40px;
        margin : 0px auto 30px auto;
      } 
      .payout-history-logo-and-view-button {
        display: flex;
        flex-direction: column;
        margin: 22px;
        align-items: center;
      }
}
  
  @media screen and (min-width: 1200px) and (max-width: 1280px) {
  
  .pricing-table .plan .description div {
    padding-left : 7% !important
  }
  
  @media screen and (min-width: 1025px) and (max-width: 1080px) {

    .pricing-table .plan .description div {
        padding-left : 7% !important;
    }
  }