<?php
//=========================================================================================
//=========================================================================================
/* Program : ComplaintsController.php											         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : Complaints can be raised against an employee(A) by another employee(B)  *
 * if their behavior is displeasing. Registered complaints will be forwarded to          *
 * employee(A)'s manager. He can review the complaint and take action against an         *
 * employee(A).                                        									 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        16-Sep-2013    Mahesh Kumar            Initial Version                    *
 *  0.2        06-Jul-2014    Mahesh kumar            Modified mail coding and used      *                                                                              	
 *                                                    the common mail function           *
 *                                                                                       *
 *  1.0        02-Feb-2015    Nivethitha             Changed in file for mobile app      *
 *                                                                                       *
 *  1.5        02-Feb-2016    Prasanth             Changed in file for Bootstrap         *
 *							  								                             */
//=========================================================================================
//=========================================================================================

include APPLICATION_PATH."/validations/Validations.php";
class Employees_ComplaintsController extends Zend_Controller_Action
{

    protected $_validation        = null;
    protected $_dbComplaint	      = null;
    protected $_dbAccessRights    = null;
    protected $_employeeAccess    = null;
    protected $_logEmpId          = null;
    protected $_dbCommonFunction  = null;
    protected $_ehrTables	      = null;
    protected $_dbPersonal	      = null;
    protected $_formName          = 'Complaints';
    protected $_hrappMobile = null;
    
    public function init()
    {
        $this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
        if ($this->_hrappMobile->checkAuth())
        {
            $this->_validation 	      = new Validations();
            $this->_dbCommonFunction  = new Application_Model_DbTable_CommonFunction();
            $this->_dbComplaint       = new Employees_Model_DbTable_Complaints();
            $this->_dbAccessRights    = new Default_Model_DbTable_AccessRights();
            $this->_dbPersonal        = new Employees_Model_DbTable_Personal();
            $userSession              = $this->_dbCommonFunction->getUserDetails ();
            $this->_logEmpId          = $userSession['logUserId'];
            $this->_employeeAccess    = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formName);
            $this->_ehrTables         = new Application_Model_DbTable_Ehr();
            
            //$this->_dbAccessRights->refreshUserSessionTimestamp($this->_logEmpId);
        }
        else
       	{
       	    if (Zend_Session::namespaceIsset('lastRequest'))
       	        Zend_Session:: namespaceUnset('lastRequest');
       	    
       	    $sessionUrl = new Zend_Session_Namespace('lastRequest');
       	    $sessionUrl->lastRequestUri = 'employees/complaints';
       	    $this->_redirect('auth');
        }
    }

    public function indexAction()
    {
        $checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

        if ($checkSessionAuth)
        {
            $this->_helper->layout()->disableLayout()->setLayout('admin_layout');
                
            $this->view->formNameA = $this->_formName;
			$this->view->customFormNameA = $this->_ehrTables->getCustomForms($this->_formName);
			
            $this->view->logEmployeeId = $this->_logEmpId;
        
            $this->view->employeeName = $this->_dbCommonFunction->listEmployeesDetails('Complaints', '', $this->_logEmpId);
        
            $this->view->userAccess = array('Is_Manager'=>$this->_employeeAccess['Employee']['Is_Manager'],
                            'View'=>$this->_employeeAccess['Employee']['View'],
                            'Update'=>$this->_employeeAccess['Employee']['Update'],
                            'Add'=>$this->_employeeAccess['Employee']['Add'],
                            'Delete'=>$this->_employeeAccess['Employee']['Delete'],
                            'Admin'=>$this->_employeeAccess['Admin'],
                            'Employee_Name' => $this->_dbPersonal->employeeId($this->_logEmpId));
			
			$this->view->dateformat = $this->_ehrTables->orgDateformat();
        } else {
			$this->_redirect('auth');
		}
    }
    
    public function listComplaintAction()
    {
	$this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            if ($this->_employeeAccess['Employee']['View'] == 1)
            {
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
                $ajaxContext->addActionContext('list-complaint', 'json')->initContext();
                
                $complaintFromName        = $this->_getParam('Complaint_From_Name', null);
                $complaintFromName        = filter_var($complaintFromName, FILTER_SANITIZE_STRIPPED);
		
                $complaintAgainstName     = $this->_getParam('Complaint_Against_Name', null);
                $complaintAgainstName     = filter_var($complaintAgainstName, FILTER_SANITIZE_STRIPPED);
		
                $title         		       = $this->_getParam('Title', null);
                $title         		       = filter_var($title, FILTER_SANITIZE_STRIPPED);
		
                $complaintDateBegin          = $this->_getParam('Complaint_Date_Begin', null);
                $complaintDateBegin          = filter_var($complaintDateBegin, FILTER_SANITIZE_STRIPPED);
            
                $complaintDateEnd            = $this->_getParam('Complaint_Date_End', null);
                $complaintDateEnd            = filter_var($complaintDateEnd, FILTER_SANITIZE_STRIPPED);
		
                $approvalStatus           = $this->_getParam('Approval_Status', null);
                $approvalStatus           = filter_var($approvalStatus, FILTER_SANITIZE_STRIPPED);
                
                $searchArray  = array('Complaint_From_Name'		=>$complaintFromName,
                              'Complaint_Against_Name'		=>$complaintAgainstName,
                              'Title'				=>$title,
                              'Complaint_Date_Begin'		=>$complaintDateBegin,
                              'Complaint_Date_End'		=>$complaintDateEnd,
                              'Approval_Status'			=>$approvalStatus);
                
                $userDetailsArray = array('Is_Manager' => $this->_employeeAccess['Employee']['Is_Manager'],
                              'Admin'      => $this->_employeeAccess['Admin'],
                              'Session_Id' => $this->_logEmpId,
                              'Form_Name'=>$this->_formName);
		
                
                $sortField  = $this->_getParam('iSortCol_0', null);
                $sortField  = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
                
                $sortOrder  = $this->_getParam('sSortDir_0', null);
                $sortOrder  = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
                
                $page       = $this->_getParam('iDisplayStart', null);
                $page       = isset($page) ? intval($page) : 0;
                $page       = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
                
                $rows       = $this->_getParam('iDisplayLength', null);
                $rows       = isset($rows) ? intval($rows) : 10;
                $rows       = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);

                $searchAll  = $this->_getParam('sSearch', null);
                $searchAll  = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
                
                $this->view->result = $this->_dbComplaint->searchComplaints($page,$rows,$sortField,$sortOrder,$searchAll,$searchArray,$userDetailsArray);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'complaints', 'employees');
        }
    
    }
     
    public function updateComplaintAction()
    {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('update-complaint', 'json')->initContext();
			
            $complaintId = $this->_getParam('Complaint_Id');
    	    $complaintId = $this->_validation->intValidation($complaintId);
            
            if ((!empty($complaintId['value']) && $this->_employeeAccess['Employee']['Update'] == 1 ) ||
				(empty($complaintId['value']) && $this->_employeeAccess['Employee']['Add'] == 1 ))
            {
	        if ($this->getRequest()->isPost())
                {
                    $formData = $this->getRequest()->getPost();
            
                    $complaintFromId    = $this->_validation->intValidation($formData['Complaint_From']);
                    $complaintAgainstId = $this->_validation->intValidation($formData['Complaint_Against']);
                    $approverId        	= $this->_validation->intValidation($formData['Forward_To']);
                    $title    		    = $this->_validation->alphaValidation($formData['Title']);
                    $complaintDate      = $this->_validation->dateValidation($formData['Complaint_Date']);
                    $approvalStatus    	= $this->_validation->alphaValidation($formData['Approval_Status']);
            		$comments['value'] 	= $this->_validation->commonFilters($formData['Comments']);
        	        $comments['valid'] 	= $this->_validation->lengthValidation($comments, 5, 600, false);
					
					//get employee resignation date
					$employeeResignationDate = $this->_dbCommonFunction->getEmployeeResignationDate($complaintAgainstId['value']);
                        
                    if ($complaintFromId['valid'] && $complaintAgainstId['valid'] && $approverId['valid'] && $title['valid']
                		    && !empty($complaintDate['value']) && $approvalStatus['valid'] && $complaintDate['value'] <= date("Y-m-d")
							&& ($complaintDate['value'] <= $employeeResignationDate || empty($employeeResignationDate)))
                    {
                        $complaintDetails  = array('Complaint_Id'		 => $complaintId['value'],
                                                   'ComplaintFrom_Id'	 => $complaintFromId['value'],
                                                   'ComplaintAgainst_Id' => $complaintAgainstId['value'],
                                                   'Approver_Id'      	 => $approverId['value'],
                                                   'Title'		         => $title['value'],
                                                   'Complaint_Date'		 => date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($complaintDate['value']))),
                                                   'Approval_Status'	 => $approvalStatus['value']);
                                
                        $customFormName = $this->_ehrTables->getCustomForms($this->_formName);
                        $customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formName);
                                
                        $updated = $this->_dbComplaint->updateComplaints($complaintDetails,$this->_logEmpId,$comments['value'],$customFormNamee);
                        
                        if ($updated['success'])
                        {
                            if ($complaintId['value'] == 0)
                            {
                                $this->view->result = $this->_dbCommonFunction->communicateMail (array('employeeId' => $complaintFromId['value'],
														'ModuleName' => 'Employees',
														'formName'   => $this->_formName,
														'customFormName'   => $customFormNamee,
														'successMsg' => $customFormNamee,
														'formUrl'    => '/employees/complaints',
														'inboxTitle' => $customFormNamee.' Notification',
														'action'     => 'added'));
                            }
                            else
                            {
                                $this->view->result = array('success' => true, 'msg' => $customFormNamee.' updated successfully', 'type' => 'success');
                            }
                        }
                        else
                        {
                            $this->view->result = $updated;
                        }
                    }
                    else
                    {
                        $this->view->result = array('success' => false, 'msg'=>'Invalid data', 'type'=>'info');
                    }
                }			
            }
            else
            {
				$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'info');
            }
        }
        else
        {
            $this->_helper->redirector('index', 'complaints', 'employees');
        }
    }
    
    public function updateStatusApprovalAction()
    {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
    	    $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('update-status-approval', 'json')->initContext();
			
            $complaintId = $this->_getParam('Complaint_Id');
    	    $complaintId = $this->_validation->intValidation($complaintId);
            
            if (!empty($complaintId['value']))
            {
                $dbComment    = new Payroll_Model_DbTable_PayrollComment();
                $accessStatus = $dbComment->payrollStatus($complaintId['value'],'Complaints');
        
                if($this->_employeeAccess['Employee']['Is_Manager'] == 1 && $accessStatus['Status'] == 'Submitted'
                                && $accessStatus['Approver_Id'] == $this->_logEmpId)
                {
                    if ($this->getRequest()->isPost())
                    {
                        $formData = $this->getRequest()->getPost();
        
                        $approvalStatus    = $this->_validation->alphaValidation($formData['Approval_Status']);
                        $comments['value'] = $this->_validation->commonFilters($formData['Comments']);
                        $comments['valid'] = $this->_validation->lengthValidation($comments, 5, 600, false);
                        
                        $customFormName = $this->_ehrTables->getCustomForms($this->_formName);
                        $customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formName);
                        
                       if(((($approvalStatus['value'] == 'Rejected' || $approvalStatus['value'] == 'Returned') && !empty($comments))
                            ||($approvalStatus['value'] == 'Actioned')) && !empty($approvalStatus['value']))
                       {
                           $complaintEmp=$this->_dbComplaint->complaintEmployee($complaintId['value']);
                    
                           $commentArray = array('Approval_Status' => $approvalStatus['value'],
                                  'Emp_Comment'     => $comments['value'],
                                  'Parent_Id'       => $complaintId['value'] );
                                        
                            $statusReport = $this->_dbComplaint->statusReport($commentArray, $this->_logEmpId, $this->_formName,$customFormNamee);
                       
                           if ( $statusReport)
                           {
                               $statusMsg = 0;
                               $result    = '';
                               
                               $logEmpName = $this->_dbPersonal->employeeName($this->_logEmpId);
                               if($this->_logEmpId != $accessStatus['Added_By'])
                               {
                                       if($approvalStatus['value'] == 'Actioned')
                                       {
                                           if($accessStatus['Added_By'] ==  $complaintEmp['ComplaintFrom_Id'])
                                               $msgDescA = "<p>".$logEmpName['Employee_Name']. " took action on ". $complaintEmp['Complaint_Against_Name']." as per your request.</p>";
                                           else
                                               $msgDescA = "<p>".$logEmpName['Employee_Name']. " took action on ". $complaintEmp['Complaint_Against_Name']." as per ". $complaintEmp['AddedBy_Name']." request which is raised by you.</p>";
                                       }
                                       else
                                       {
                                           if($accessStatus['Added_By'] ==  $complaintEmp['ComplaintFrom_Id'])
                                               $msgDescA = "<p>Your ".$customFormNamee." request has been ".strtolower($approvalStatus['value'])."</p>";
                                           else
                                               $msgDescA = "<p>Your ".$customFormNamee." request against the employee ". $complaintEmp['Complaint_Against_Name'] ." has been ".strtolower($approvalStatus['value'])."</p>";
                                       }
                                       $result = $this->_dbCommonFunction->communicateMail (array('employeeId'  => $accessStatus['Added_By'],
                                                                                              'ModuleName'  => 'Employees',
																							'formName'   => $this->_formName,
																							  'customFormName'   => $customFormNamee,
                                                                                              'successMsg'  => $customFormNamee,
                                                                                              'formUrl'     => '/employees/complaints',
                                                                                              'inboxTitle'  => $customFormNamee.' Notification',
                                                                                              'mailContent' => $msgDescA,
                                                                                              'action'      => 'updated'));
                               }
           
                               if($this->_logEmpId !=  $complaintEmp['ComplaintFrom_Id'] &&  $complaintEmp['ComplaintFrom_Id'] != $accessStatus['Added_By'] && $approvalStatus['value'] == 'Actioned')
                               {                                            	
                                   if($approvalStatus['value'] == 'Actioned')
                                   {
                                       $msgDescA = "<p>".$logEmpName['Employee_Name']. " took action on ". $complaintEmp['Complaint_Against_Name']." as per your request which is raised by ". $complaintEmp['AddedBy_Name'].".</p>";
                                   }
                                   else 
                                   {
                                       $msgDescA = "<p>Your Complaint request has been ".strtolower($approvalStatus['value'])." which is raised by ". $complaintEmp['AddedBy_Name'].".</p>";
                                   }
           
                                    $result = $this->_dbCommonFunction->communicateMail (array('employeeId'  => $accessStatus['Added_By'],
                                                                                              'ModuleName'  => 'Employees',
																							  'formName'   => $this->_formName,
																							  'customFormName'   => $customFormNamee,
                                                                                              'successMsg'  => $customFormNamee,
                                                                                              'formUrl'     => '/employees/complaints',
                                                                                              'inboxTitle'  => $customFormNamee.' Notification',
                                                                                              'mailContent' => $msgDescA,
                                                                                              'action'      => 'updated'));
                               }
                                                   
                               if (empty($result))
                               {
                                   $this->view->result = array('success' => true, 'msg'=>"Status updated successfully", 'type'=>'success');
                               }
                               else
                               {
                                   $this->view->result = $result;
                               }
                           }
                           else
                           {
                               $this->view->result = array('success' => false, 'msg'=>"Unable to update status.Please, contact admin", 'type'=>'info');
                           }
                       }
                       else
                       {
                           $this->view->result = array('success' => false, 'msg'=>"Invalid data", 'type'=>'info');
                       }
                    }
                }
                else
                {
                    $this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'info');
                }
            }	    
        }
        else
        {
            $this->_helper->redirector('index', 'complaints', 'employees');
        }
    }

    public function __destruct()
    {
        
    }
}


