<?php
//=========================================================================================
//=========================================================================================
/* Program : TimesheetsController.php											         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : Timesheet is for track how many regular and overtime hours an           *
 * employee have been worked in a week. This information may be used for payroll, client *
 * billing, and increasingly for project costing, estimation, tracking and management.   *
 * One of the major uses of timesheets in a project management environment is comparing  *
 * planned costs versus actual costs, as well as measuring employee performance, and     *
 * identifying problematic tasks. This knowledge can drive corporate strategy as users   *
 * stop performing or reassign unprofitable work. The details maintained in Timesheet    *
 * are project, activity, type, date, hours, weekend date, approver etc.				 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Devirani,Shobana        Initial Version        	         *
 *  0.2        06-Jul-2014    Mahesh                  Modified mail coding and used      *                                                                              	
 *                                                    the common mail function           *                                                                                    	
 *  0.3		   18-Sep-2014	  Mahesh				  Modified Function                  *
 *					                                  1.showTimesheetHoursAction       	 *
 *                                                    2.addTimesheetHoursAction        	 *
 *													  3.updateTimesheetHoursAction		 *
 *                                                                                       *
 *  1.0        02-Feb-2015    Saranya                 Changed in file for mobile app     *
 *                                                                                       *
 *  1.5        16-Feb-2016    Suresh               Changed in file for Bootstrap         *
 *							  				                		                     */
//=========================================================================================
//=========================================================================================
include APPLICATION_PATH."/validations/Validations.php";
class Employees_TimesheetsController extends Zend_Controller_Action
{
    protected $_dbTimesheet = null;
    protected $_dbTimesheetHrs = null;
    protected $_dbActivity = null;
    protected $_dbPersonal = null;
    protected $_basePath = null;
    protected $_dbAccessRights = null;
    protected $_timesheetAccessRights = null;
    protected $_hrsAccessRights = null;
    protected $_activityAccessRights = null;
    protected $_logEmpId = null;
    protected $_dbManager = null;
    protected $_dbJobDetail = null;
    protected $_dbProject = null;
    protected $_dbInbox = null;
    protected $_dbGrade = null;
    protected $_dbWorkSchedule = null;
    protected $_ehrTables = null;
    protected $_dbComment = null;
    protected $_orgDateFormat = null;
    protected $_orgName = null;
    protected $_dbOrgSettings = null;
    protected $_isMobile = null;
    protected $_hrappMobile = null;
    protected $_checkSession = null;
    protected $_inboxName = 'Inbox';
    protected $_formNameA = 'Timesheets';
    protected $_formNameC = 'Timesheet Activities';
    protected $_formNameB = 'Timesheet Hours';
    protected $_formIdA = 23; // team timesheets
    protected $_formIdB = 262; // self timesheets
    protected $_dbCommonFunction = null;
    protected $_dbEmployee     = null;
    protected $_orgDetails     = null;
    protected $_timesheetSettings = null;
    
    public function init()
    {
        $this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
		if ($this->_hrappMobile->checkAuth())
        {
		    $this->_dbGrade = new Employees_Model_DbTable_Grade();
            $this->_basePath          = new Zend_View_Helper_BaseUrl();
	        $this->_validation 	        = new Validations();
		    $this->_dbCommonFunction    = new Application_Model_DbTable_CommonFunction();
            $this->_dbTimesheet 	= new Employees_Model_DbTable_Timesheet();
            $this->_dbTimesheetHours 	= new Employees_Model_DbTable_TimesheetHours();
            $this->_dbTimesheetActivity = new Employees_Model_DbTable_TimesheetActivity();
            $this->_dbPersonal = new Employees_Model_DbTable_Personal();
			$this->_dbProject = new Organization_Model_DbTable_Project();
            $this->_ehrTables = new Application_Model_DbTable_Ehr();
            $this->_dbAccessRights      = new Default_Model_DbTable_AccessRights();
            $this->_dbEmployee     = new Employees_Model_DbTable_Employee();
            $userSession                = $this->_dbCommonFunction->getUserDetails ();
            $this->_logEmpId            = $userSession['logUserId'];
            $this->_employeeAccessA     = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameA);
            $this->_employeeAccessB     = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameB);
            $this->_employeeAccessC     = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameC);
            $this->_formIdAccessA       = $this->_dbAccessRights->employeeAccessRightsBasedOnFormId($this->_logEmpId, $this->_formIdA);
            $this->_formIdAccessB       = $this->_dbAccessRights->employeeAccessRightsBasedOnFormId($this->_logEmpId, $this->_formIdB);
            $this->_timesheetSettings   = $this->_dbTimesheet->getTimesheetSettings();
			
            if (Zend_Registry::isRegistered('orgDetails'))
				$this->_orgDetails = Zend_Registry::get('orgDetails');
        }
        else
        {
            if (Zend_Session::namespaceIsset('lastRequest'))
                Zend_Session::namespaceUnset('lastRequest');
       
	        $session = new Zend_Session_Namespace('lastRequest');
            $session->lastRequestUri = 'employees/timesheets';
            $this->_redirect('auth');
        }
    }

    public function indexAction()
    {
		$checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

		if ($checkSessionAuth)
        {
        	//$this->view->isMobile = $this->_hrappMobile->findMobile($this->_helper->layout);
	        $this->_helper->layout()->disableLayout()->setLayout('admin_layout');
			
            $this->view->formNameA = $this->_formNameA;
            $this->view->formNameB = $this->_formNameB;
            $this->view->formNameC = $this->_formNameC;
			
			$this->view->customFormNameA = $this->_ehrTables->getCustomForms($this->_formNameA);
			$this->view->customFormNameB = $this->_ehrTables->getCustomForms($this->_formNameB);
			$this->view->customFormNameC = $this->_ehrTables->getCustomForms($this->_formNameC);
		
		    //$this->view->employeeName = $this->_dbCommonFunction->listEmployeesDetails('Timesheets', '', $this->_logEmpId);
			$this->view->employeeName = $this->_dbPersonal->employeeDetail('', '', 'Emp_First_Name', 'ASC', '', '','', '',$this->_logEmpId,
																'', '', '', '', 'Timesheets', '', 1, 1, '');
			
            $this->view->userAccessA = array('Is_Manager'=>$this->_employeeAccessA['Employee']['Is_Manager'],
                                               'View'=>$this->_employeeAccessA['Employee']['View'],
                                               'Add'=>$this->_employeeAccessA['Employee']['Add'],
                                               'Update'=>$this->_employeeAccessA['Employee']['Update'],
                                               'Delete'=>$this->_employeeAccessA['Employee']['Delete'],
                                               'Admin'=>$this->_employeeAccessA['Admin'],
                                               'Session_Id'    => $this->_logEmpId,
                                               'Employee_Name' => $this->_dbPersonal->employeeId($this->_logEmpId));
            $this->view->userAccessB = array('Is_Manager'=>$this->_employeeAccessB['Employee']['Is_Manager'],
                                                  'View'=>$this->_employeeAccessB['Employee']['View'],
                                                  'Add'=>$this->_employeeAccessB['Employee']['Add'],
                                                  'Update'=>$this->_employeeAccessB['Employee']['Update'],
                                                  'Delete'=>$this->_employeeAccessB['Employee']['Delete'],
                                                  'Admin'=>$this->_employeeAccessB['Admin']);
            $this->view->userAccessC = array('Is_Manager'=>$this->_employeeAccessC['Employee']['Is_Manager'],
                                              'View'=>$this->_employeeAccessC['Employee']['View'],
                                              'Add'=>$this->_employeeAccessC['Employee']['Add'],
                                              'Update'=>$this->_employeeAccessC['Employee']['Update'],
                                              'Delete'=>$this->_employeeAccessC['Employee']['Delete'],
                                              'Admin'=>$this->_employeeAccessC['Admin']);
            $this->view->formIdAccessA = array('Is_Manager'=>$this->_formIdAccessA['Employee']['Is_Manager'],
                                                'View'=>$this->_formIdAccessA['Employee']['View'],
                                                'Delete'=>$this->_formIdAccessA['Employee']['Delete'],
                                                'Admin'=>$this->_formIdAccessA['Admin']);
            $this->view->formIdAccessB = array('View'=>$this->_formIdAccessB['Employee']['View']);
			$this->view->newImg    = $this->_basePath->baseUrl('images/new.png');
	    $this->view->gradeName 	        = $this->_dbGrade->getGrade();
	    $this->view->projectName	    = $this->_dbProject->getprojectName();
	    $this->view->activityName 	    = $this->_dbTimesheet->getActivity();
		$this->view->dateformat         = $this->_ehrTables->orgDateformat();   
        $this->view->timesheetSettings  = $this->_timesheetSettings;
	    $this->view->orgDetails         = $this->_orgDetails;
        $this->view->serviceProvider    = $this->_dbEmployee->getEmployeeServiceProviderList($this->_logEmpId);
        } else {
			$this->_redirect('auth');
		}
    }
    
    public function listTimesheetDetailsAction()
    {
	$this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            if ($this->_employeeAccessA['Employee']['View'] == 1)
            {
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
                $ajaxContext->addActionContext('list-timesheet-details', 'json')->initContext();
                
                $employeeName  = $this->_getParam('Employee_Name', null);
                $employeeName  = filter_var($employeeName, FILTER_SANITIZE_STRIPPED);
		
		        $weekendDateBegin  = $this->_getParam('Week_Ending_Date_Begin', null);
                $weekendDateBegin  = filter_var($weekendDateBegin, FILTER_SANITIZE_STRIPPED);
		
		        $weekendDateEnd  = $this->_getParam('Week_Ending_Date_End', null);
                $weekendDateEnd  = filter_var($weekendDateEnd, FILTER_SANITIZE_STRIPPED);
		
		
		        $approvalStatus  = $this->_getParam('Approval_Status', null);
                $approvalStatus  = filter_var($approvalStatus, FILTER_SANITIZE_STRIPPED);

                $serviceProviderId = $this->_getParam('ServiceProviderId', null);
				$serviceProviderId = filter_var($serviceProviderId, FILTER_SANITIZE_NUMBER_INT);
		
		
		        $searchArray  = array('Employee_Name'=>$employeeName,
					 'Week_Ending_Date_Begin'=>$weekendDateBegin,
					 'Week_Ending_Date_End'=>$weekendDateEnd,
					 'Approval_Status'=>$approvalStatus,
                     'serviceProviderId' => $serviceProviderId);
               
		        $sortField  = $this->_getParam('iSortCol_0', null);
                $sortField  = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
                
                $sortOrder  = $this->_getParam('sSortDir_0', null);
                $sortOrder  = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
                
                $page       = $this->_getParam('iDisplayStart', null);
                $page       = isset($page) ? intval($page) : 0;
                $page       = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
                
                $rows       = $this->_getParam('iDisplayLength', null);
                $rows       = isset($rows) ? intval($rows) : 10;
                $rows       = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);

                $searchAll  = $this->_getParam('sSearch', null);
                $searchAll  = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
		
		
		    
                $userDetailsArray = array('Is_Manager' => $this->_employeeAccessA['Employee']['Is_Manager'],
					      'Op_Choice'=>$this->_employeeAccessA['Employee']['Optional_Choice'],
					      'Admin'      => $this->_employeeAccessA['Admin'],
					      'Session_Id' => $this->_logEmpId,
					      'Form_Name'=>$this->_formNameA);
		
		$this->view->result = $this->_dbTimesheet->searchTimesheetDetails($page,$rows,$sortField,$sortOrder,$searchAll,$searchArray,$userDetailsArray);    
            }
        }
        else
        {
            $this->_helper->redirector('index', 'timesheets', 'employees');
        }
    }
    public function updateTimesheetDetailsAction()
    {
        $this->_helper->layout->disableLayout();
		if (isset($_SERVER['HTTP_REFERER']))
		{
		    $ajaxContext = $this->_helper->getHelper('AjaxContext');
		    $ajaxContext->addActionContext('update-timesheet-details', 'json')->initContext();
				
		    $requestId = $this->_getParam('Request_Id');
		    $requestId = $this->_validation->intValidation($requestId);
		    
		    if ((!empty($requestId['value']) && $this->_employeeAccessA['Employee']['Update'] == 1 ) ||
					(empty($requestId['value']) && $this->_employeeAccessA['Employee']['Add'] == 1 ))
		    {
                if ($this->getRequest()->isPost())
                {
                    $formData = $this->getRequest()->getPost();
                    $action    = $this->_validation->alphaValidation($formData['Action']);
                    $employeeId        = $this->_validation->intValidation($formData['Employee_Id']);
                    $approverId        = $this->_validation->intValidation($formData['Approver_Id']);
                    $weekEndDate         = $this->_validation->dateValidation($formData['Week_Ending_Date']);
                    $approvalStatus    = $this->_validation->alphaValidation($formData['Approval_Status']);
                    $comments          = $this->_validation->alphaNumSpCDotHySlashNewLineValidation($formData['Comments']);
                    $comments['valid'] = $this->_validation->lengthValidation($comments, 5, 600, false);
                    $day1            = $this->_validation->hoursValidation($formData['Day1']);
                    $day2            = $this->_validation->hoursValidation($formData['Day2']);
                    $day3            = $this->_validation->hoursValidation($formData['Day3']);
                    $day4            = $this->_validation->hoursValidation($formData['Day4']);
                    $day5            = $this->_validation->hoursValidation($formData['Day5']);
                    $day6            = $this->_validation->hoursValidation($formData['Day6']);
                    $day7            = $this->_validation->hoursValidation($formData['Day7']);
                    $timesheetType   = $this->_validation->alphaValidation($formData['Timesheet_Type']);
                    $projectId   	 = $this->_validation->intValidation($formData['Project_Id']);
                    $activityId      = $this->_validation->intValidation($formData['Activity_Id']);

                    $description          = $this->_validation->alphaNumSpCDotHySlashNewLineValidation($formData['Description']);
                    $description['valid'] = $this->_validation->lengthValidation($description, 5,3000, false);
			        
                    if ($employeeId['valid'] && $approverId['valid'] && $approvalStatus['valid'] &&
                        $timesheetType['valid'] && $projectId['valid'] && $activityId['valid'] && $description['valid']
                            && !empty($employeeId['value']) && !empty($approverId['value']) && !empty($approvalStatus['value'])
                            && !empty($timesheetType['value']) && !empty($projectId['value']) && !empty($activityId['value'])
                            && $day1['valid'] && $day2['valid'] && $day3['valid'] && $day4['valid']
                            && $day5['valid'] && $day6['valid'] && $day7['valid']
                            && (!empty($day1['value']) || !empty($day2['value']) || !empty($day3['value']) || !empty($day4['value']) || 
                            !empty($day5['value']) || !empty($day6['value']) || !empty($day7['value'])))
                    {

                        $weekEndDateNumber = date('N', strtotime($weekEndDate['value']));
                        //weekend date should be always saturday if any other day is choosen we need to return the error message.
                        if($weekEndDateNumber==6)
                        {
                            $timesheetDetails  =  array('Request_Id'     	=> $requestId['value'],
                                                        'Employee_Id'       => $employeeId['value'],
                                                        'Approver_Id'       => $approverId['value'],
                                                        'Week_Ending_Date'  => date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($weekEndDate['value']))),
                                                        'Approval_Status'   => $approvalStatus['value'],
                                                        'Timesheet_Type'    => $timesheetType['value'],
                                                        'Project_Id'        => $projectId['value'],
                                                        'Project_Activity_Id'=> $activityId['value'],
                                                        'Day1'			    => $day1['value'],
                                                        'Day2'			    => $day2['value'],
                                                        'Day3'			    => $day3['value'],
                                                        'Day4'			    => $day4['value'],
                                                        'Day5'			    => $day5['value'],
                                                        'Day6'			    => $day6['value'],
                                                        'Day7'			    => $day7['value'],
                                                        'Description'	    => $description['value'],
                                                        'Added_By'          => $this->_logEmpId,
                                                        'Added_Date'        => date('Y-m-d H:i:s'));
                    
                            if($timesheetDetails['Request_Id'] == 0 || empty($timesheetDetails['Request_Id']))
                            {
                                        $lastRequestId = $this->_dbTimesheet->maxRequestId();
                                        $currentRequestId = $lastRequestId+1;
                                        $timesheetDetails['Request_Id']=$currentRequestId;
                            }
                            
                            $customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
                            $formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
                            
                            $this->view->result = $this->_dbTimesheet->updateTimesheetDetails($timesheetDetails,$action['value'],$comments['value'], $formName);
                        }
                        else 
                        {
                            $this->view->result = array('success' => false, 'msg'=>'Please select a valid weekend date', 'type'=>'warning');
                        }
                    }
                    else
                    {
                        $this->view->result = array('success' => false, 'msg'=>'Invalid data', 'type'=>'warning');
                    }
                }
					
		    }
            else
		    {
					$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'info');
		    }
		}
		else
		{
		    $this->_helper->redirector('index', 'timesheets', 'employees');
		}
    }
   
    
   
    public function listTimesheetTrackingAction()
    {
	    $this->_helper->layout->disableLayout();
	    if (isset($_SERVER['HTTP_REFERER']))
	    {
		if ($this->_employeeAccessA['Employee']['View'] == 1)
		{
		    $ajaxContext = $this->_helper->getHelper('AjaxContext');
		    $ajaxContext->addActionContext('list-timesheet-tracking', 'json')->initContext();
		    
		    $requestId 	 = $this->_getParam('Request_Id', null);
		    $requestId     = filter_var($requestId, FILTER_SANITIZE_NUMBER_INT);
			
			$employeeId 	 = $this->_getParam('employeeId', null);
		    $employeeId     = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
			
			$weekendDate 	 = $this->_getParam('weekendDate', null);
		    $weekendDate     = filter_var($weekendDate, FILTER_SANITIZE_NUMBER_INT);

            $approvalStatus 	 = $this->_getParam('approvalStatus', null);
		    $approvalStatus     = filter_var($approvalStatus, FILTER_SANITIZE_STRIPPED);
		  
            $this->view->result = $this->_dbTimesheet->searchTimesheetTracking($requestId,$employeeId,$weekendDate,$approvalStatus,$this->_employeeAccessA['Employee']);
		}
	    }
	    else
	    {
		$this->_helper->redirector('index', 'timesheets', 'employees');
	    }
    }
    
    public function updateTimesheetTrackingAction()
    {
        $this->_helper->layout->disableLayout();
		if (isset($_SERVER['HTTP_REFERER']))
		{
		    $ajaxContext = $this->_helper->getHelper('AjaxContext');
		    $ajaxContext->addActionContext('update-timesheet-tracking', 'json')->initContext();
		    
		    if ($this->_employeeAccessA['Employee']['Update'] == 1  || $this->_employeeAccessA['Employee']['Add'] == 1 )
		    {
                if ($this->getRequest()->isPost())
                {
                    $formData = $this->getRequest()->getPost();
                    $requestId        = $this->_validation->intValidation($formData['Request_Id']);
                    $approvalStatus   = $this->_validation->alphaValidation($formData['Approval_Status']);
                    $employeeId       = $this->_validation->intValidation($formData['Employee_Id']);
                    $approverId       = $this->_validation->intValidation($formData['Approver_Id']);
                    $weekEndDate      = $this->_validation->dateValidation($formData['Week_Ending_Date']);
                    
                    if ($requestId['valid'])
                    {
                        $currentDate = date('Y-m-d');

                        
                        //We should not allow the employee to submit the record before the week end date.
                        if(($this->_timesheetSettings['Timesheet_Submission_Before_Weekend_Date']=='Yes') || ($this->_timesheetSettings['Timesheet_Submission_Before_Weekend_Date']=='No'
                        && ($approvalStatus['value']=='Applied' && $currentDate >= $weekEndDate['value'])) || $approvalStatus['value']=='Draft')
                        {
                            $customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
                            $formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
                            
                            $updated = $this->_dbTimesheet->updateTimesheetStatus($requestId['value'],$approvalStatus['value'], $formName);
                        
                            if($approvalStatus['value']=='Draft')
                            {
                                $this->view->result = $updated;
                            }
                            elseif($approvalStatus['value']=='Applied')
                            {
                                if ($updated['success'])
                                {
                                    $customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
                                    $formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
                                    
                                    $this->view->result = $this->_dbCommonFunction->communicateMail (array('employeeId' => $approverId['value'],
                                                                    'ModuleName' => 'Employees',
                                                                    'formName'   => $this->_formNameA,
                                                                    'customFormName'   => $formName,
                                                                    'successMsg' => $formName.' Details',
                                                                    'formUrl'    => '/employees/timesheets',
                                                                    'inboxTitle' => $formName.' Notification',
                                                                    'action'     => 'added'));
                                }
                                else
                                {
                                    $this->view->result = $updated;
                                }
                            }
                        }
                        else 
                        {
                            $this->view->result = array('success' => false, 'msg' =>'Timesheet record should not be allowed to submit before the weekend date', 'type' => 'warning');
                        } 
                    }
                    else
                    {
                        $this->view->result = array('success' => false, 'msg'=>'Invalid data', 'type'=>'warning');
                    }
                }
		    }
		    else
		    {
					$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'info');
		    }
		}
		else
		{
		    $this->_helper->redirector('index', 'timesheets', 'employees');
		}
    }
     
     public function deleteTimesheetTrackingAction()
    {
	$this->_helper->layout->disableLayout();
	if (isset($_SERVER['HTTP_REFERER']))
	{
	    $ajaxContext = $this->_helper->getHelper('AjaxContext');
	    $ajaxContext->addActionContext('delete-timesheet-tracking', 'json')->initContext();
	    
	    $projectId = $this->_getParam('Project_Id', null);
	    $projectId = filter_var($projectId, FILTER_SANITIZE_NUMBER_INT);
        
        $activityId = $this->_getParam('Activity_Id', null);
	    $activityId = filter_var($activityId, FILTER_SANITIZE_NUMBER_INT);
        
        $timesheetType = $this->_getParam('Timesheet_Type', null);
	    $timesheetType = filter_var($timesheetType, FILTER_SANITIZE_STRIPPED);
        
        $requestId = $this->_getParam('Request_Id', null);
	    $requestId = filter_var($requestId, FILTER_SANITIZE_NUMBER_INT);
        
        $employeeId = $this->_getParam('Employee_Id', null);
	    $employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
        
        $weekEndingDate = $this->_getParam('Week_Ending_Date', null);
	   
        
        $weekEndDate         = $this->_validation->dateValidation($weekEndingDate);

        if($this->_employeeAccessA['Employee']['Delete'] == 1){
            if (!empty($projectId) && $projectId > 0 && !empty($activityId) && $activityId > 0 &&
                !empty($requestId) && $requestId > 0 && !empty($employeeId) && $employeeId > 0 &&
                !empty($weekEndingDate) && !empty($timesheetType) )
            {
                $deleteTimesheet = array('Project_Id'=>$projectId,
                                        'Activity_Id'=>$activityId,
                                        'Timesheet_Type'=>$timesheetType,
                                        'Request_Id'=>$requestId,
                                        'Employee_Id'=>$employeeId,
                                        'Week_Ending_Date'=>date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($weekEndDate['value']))));
            
                $customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
                $formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
            
                $this->view->result = $this->_dbTimesheet->deleteTimesheetDetails($deleteTimesheet, $this->_logEmpId, $formName, $this->_formNameA);
            }
            else
            {
                $this->view->result = array('success'=>false, 'msg'=>'Invalid data','type'=>'warning');
            }
        }else{
            $this->view->result = array('success'=>false, 'msg'=>'Access denied','type'=>'warning');
        }
	}
	else
	{
	    $this->_helper->redirector('index', 'timesheets', 'employees');
	}
    }
    
    public function activitySubgridAction()
    {
	$this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            if ($this->_employeeAccessC['Employee']['View'] == 1)
            {
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
                $ajaxContext->addActionContext('list-timesheet-activity', 'json')->initContext();
                
                $projectId  = $this->_getParam('Project_Id', null);
                $projectId  = filter_var($projectId, FILTER_SANITIZE_NUMBER_INT);
               
		//if($projectId>0)
		//{
		     $this->view->result = $this->_dbTimesheetActivity->searchTimesheetActivity($projectId);
		//}
	    }
        }
        else
        {
            $this->_helper->redirector('index', 'timesheets', 'employees');
        }
	
    }
    
    
    public function listTimesheetActivityAction()
    {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            if ($this->_employeeAccessC['Employee']['View'] == 1)
            {
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
                $ajaxContext->addActionContext('list-timesheet-activity', 'json')->initContext();
                
                $projectId  = $this->_getParam('Project_Id', null);
                $projectId  = filter_var($projectId, FILTER_SANITIZE_NUMBER_INT);
               
		$sortField  = $this->_getParam('iSortCol_0', null);
                $sortField  = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
                
                $sortOrder  = $this->_getParam('sSortDir_0', null);
                $sortOrder  = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
                
                $page       = $this->_getParam('iDisplayStart', null);
                $page       = isset($page) ? intval($page) : 0;
                $page       = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
                
                $rows       = $this->_getParam('iDisplayLength', null);
                $rows       = isset($rows) ? intval($rows) : 10;
                $rows       = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);

                $searchAll  = $this->_getParam('sSearch', null);
                $searchAll  = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
                  
		$this->view->result = $this->_dbTimesheetActivity->searchTimesheetActivityProjects($page,$rows,$sortField,$sortOrder,$this->_logEmpId,$searchAll);
		
	    }
        }
        else
        {
            $this->_helper->redirector('index', 'timesheets', 'employees');
        }
    }
    
     public function updateTimesheetActivityAction()
    {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
	    $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('update-timesheet-activity', 'json')->initContext();
	    
            $activityId = $this->_getParam('Activity_Id');
    	    $activityId = $this->_validation->intValidation($activityId);
            
            if ((!empty($activityId['value']) && $this->_employeeAccessC['Employee']['Update'] == 1 ) ||
		    (empty($activityId['value']) && $this->_employeeAccessC['Employee']['Add'] == 1 ))
            {
                if ($this->getRequest()->isPost())
                {
                    $formData = $this->getRequest()->getPost();
                                            
                    $projectId	        = $this->_validation->intValidation($formData['Project_Id']);
                    $activityName	        = $this->_validation->alphaNumSpCDotHySlashValidation($formData['Activity_Name']);
                    $activityName['valid']  = $this->_validation->lengthValidation($activityName,3,100,true);                    
                    
                    $activityDescription          = $this->_validation->alphaNumSpCDotHySlashNewLineValidation($formData['Activity_Description']);
                    $activityDescription['valid'] = $this->_validation->lengthValidation($activityDescription, 5, 600, false);

                    $isBillable = $this->_validation->alphaValidation($formData['Is_Billable']);
                    
                    $activityFrom = $this->_validation->dateValidation($formData['Activity_From']);
                    $activityTo = $this->_validation->dateValidation($formData['Activity_To']);
                                            
                    $datediff = (strtotime($activityTo['value']) - strtotime($activityFrom['value']));
                    $totalDateDifference = floor($datediff / (60 * 60 * 24));

                    // totalDateDifference should be either 0 or 6 
                    // 0 means both dates are empty, 6 means both dates(Activity_From and Activity_To) are selected with one week difference
                    // if totalDateDifference is other than 0 or 6 then any one date selected and other date not selected
                    if ($projectId['valid'] && $activityName['valid'] && $activityDescription['valid'] &&
                    !empty($projectId['value']) && !empty($activityName['value']) 
                    && $isBillable['valid'] && !empty($isBillable['value']) &&
                    ((empty($activityFrom['value']) && empty($activityTo['value'])) || 
                    ($totalDateDifference > 0 && date('w', strtotime($activityFrom['value'])) == 0 &&  date('w', strtotime($activityTo['value'])) == 6)))
                    {
                        $timesheetActivity = array('Activity_Id'  => $activityId['value'],
                                                    'Project_Id'  => $projectId['value'],
                                                    'Activity_Name' => $activityName['value'],
                                                    'Activity_From' => empty($activityFrom['value']) ? null : $activityFrom['value'],
                                                    'Activity_To' => empty($activityTo['value']) ? null : $activityTo['value'],
                                                    'Description'   => $activityDescription['value'],
                                                    'Is_Billable'   => $isBillable['value']);
                        
                        $customFormName = $this->_ehrTables->getCustomForms($this->_formNameC);
                        $formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameC);						
                                            
                        $this->view->result = $this->_dbTimesheetActivity->updateTimesheetActivity($timesheetActivity,$this->_logEmpId, $formName);
                    }
                    else
                    {
                        $this->view->result = array('success' => false, 'msg'=>'Invalid data', 'type'=>'warning');
                    }
                }
            }
            else
            {
				$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'info');
            }
        }
        else
        {
                    $this->_helper->redirector('index','timesheets','employees');
        }
    }
   

    public function deleteTimesheetActivityAction()
    {
         $this->_helper->layout->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-timesheet-activity', 'json')->initContext();
            
            $projectId = $this->_getParam('Project_Id');
            $projectId = filter_var($projectId, FILTER_SANITIZE_NUMBER_INT);
            	
            $activityId = $this->_getParam('Activity_Id', null);
            $activityId = filter_var($activityId, FILTER_SANITIZE_NUMBER_INT);
            
            if (!empty($projectId) && $projectId > 0 && $this->_employeeAccessC['Employee']['Delete'] == 1)
            {
                $customFormName = $this->_ehrTables->getCustomForms($this->_formNameC);
                $formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameC);

				if (!empty($activityId) && $activityId > 0)
				{
					$this->view->result = $this->_dbTimesheetActivity->deleteActivity($projectId,$activityId,$this->_logEmpId, $formName);
				}
				else
				{
					$this->view->result = $this->_dbTimesheetActivity->deleteActivityByProject($projectId,$this->_logEmpId,$formName);
				}
            }
            else
            {
               $this->view->result = array('success'=>false, 'msg'=>'Invalid data', 'type'=>'warning');
            }
        }
        else
        {
            $this->_helper->redirector('index', 'timesheets', 'employees');
        }
    }
    

 public function listTimesheetHoursAction()
    {
       $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            if ($this->_employeeAccessB['Employee']['View'] == 1)
            {
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
                $ajaxContext->addActionContext('list-timesheet-hours', 'json')->initContext();
                
                $gradeId            = $this->_getParam('Grade_Id', null);
                $regularHours       = $this->_getParam('Regular_Hours', null);
                $overTimeHours      = $this->_getParam('Overtime_Hours', null);
                $breakHours         = $this->_getParam('Break_Hours', null);
                
                $gradeId           = filter_var($gradeId, FILTER_SANITIZE_NUMBER_INT);
                $regularHours      = filter_var($regularHours, FILTER_SANITIZE_STRIPPED);
                $overTimeHours     = filter_var($overTimeHours, FILTER_SANITIZE_STRIPPED);
                $breakHours        = filter_var($breakHours, FILTER_SANITIZE_STRIPPED);
               
                $sortField  = $this->_getParam('iSortCol_0', null);
                $sortField  = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
                
                $sortOrder  = $this->_getParam('sSortDir_0', null);
                $sortOrder  = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
                
                $page       = $this->_getParam('iDisplayStart', null);
                $page       = isset($page) ? intval($page) : 0;
                $page       = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
                
                $rows       = $this->_getParam('iDisplayLength', null);
                $rows       = isset($rows) ? intval($rows) : 10;
                $rows       = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);

                $searchAll  = $this->_getParam('sSearch', null);
                $searchAll  = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
                
		$this->view->result = $this->_dbTimesheetHours->searchTimesheetHours($page,$rows,$sortField,$sortOrder,$this->_logEmpId,$searchAll,$gradeId,$regularHours,$overTimeHours,$breakHours);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'timesheets', 'employees');
        }
    }
   
    //version 0.3 => added break hours field
    public function updateTimesheetHoursAction()
    {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('update-timesheet-hours', 'json')->initContext();
	    
            $timesheetId = $this->_getParam('Timesheet_Id');
    	    $timesheetId = $this->_validation->intValidation($timesheetId);
           
	  
             if ((!empty($timesheetId['value']) && $this->_employeeAccessB['Employee']['Update'] == 1 ) ||
                (empty($timesheetId['value']) && $this->_employeeAccessB['Employee']['Add'] == 1 ))
                {
					if ($this->getRequest()->isPost())
					{
						$formData = $this->getRequest()->getPost();
                                                
                        $gradeId	     = $this->_validation->intValidation($formData['Grade_Id']);

                        $regularHours  = $this->_validation->hoursValidation($formData['Regular_Hours']);
                        $regularHours['valid'] = $this->_validation->minMaxValueValidation($regularHours['value'],0.5,'');
                        
                        $overtimeHours  = $this->_validation->hoursValidation($formData['Overtime_Hours']);
                        $overtimeHours['valid']  = $this->_validation->minMaxValueValidation($overtimeHours['value'],0,'');
                        
                        $breakHours  = $this->_validation->hoursValidation($formData['Break_Hours']);
                        $breakHours['valid']  = $this->_validation->minMaxValueValidation($breakHours['value'],0,'');
                        
		                                  
                        if ($gradeId['valid'] && $regularHours['valid'] && $overtimeHours['valid'] && $breakHours['valid']
                            && (!empty($regularHours['value']) || !empty($overtimeHours['value']) || !empty($breakHours['value'])) )
                        {
                            $timesheetHours = array('Timesheet_Id'  => $timesheetId['value'],
                                                    'Grade_Id'         => $gradeId['value'],
                                                    'Regular_Hours'    => $regularHours['value'],
                                                    'Overtime_Hours'   => $overtimeHours['value'],
                                                    'Break_Hours'      => $breakHours['value']);
                            
							$customFormName = $this->_ehrTables->getCustomForms($this->_formNameB);
							$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameB);
							
                            $this->view->result = $this->_dbTimesheetHours->updateTimesheetHours($timesheetHours,$this->_logEmpId, $formName);
						}
						else
						{
						  $this->view->result = array('success' => false, 'msg'=>'Invalid data', 'type'=>'warning');
						}
					}
				
            }
            else
            {
				$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'info');
            }
        }
        else
        {
                    $this->_helper->redirector('index','timesheets','employees');
        }
    }

    /**
     * Delete timesheet hours
     */
    public function deleteTimesheetHoursAction()
    {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-timesheet-hours', 'json')->initContext();
            
            $timesheetId = $this->_getParam('Timesheet_Id', null);
            $timesheetId = filter_var($timesheetId, FILTER_SANITIZE_NUMBER_INT);
            
            if (!empty($timesheetId) && $timesheetId > 0 && $this->_employeeAccessB['Employee']['Delete'] == 1)
            {
				$customFormName = $this->_ehrTables->getCustomForms($this->_formNameB);
				$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameB);
							
                $this->view->result = $this->_dbTimesheetHours->deleteTimesheetHours($timesheetId, $this->_logEmpId, $formName);
            }
	    else
	    {
		$this->view->result = array('success'=>false, 'msg'=>'Invalid data', 'type'=>'warning');
	    }
        }
        else
        {
            $this->_helper->redirector('index', 'timesheets', 'employees');
        }
    }
    
     public function updateStatusApprovalAction()
    {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
	    $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('update-status-approval', 'json')->initContext();
			
            $requestId = $this->_getParam('Request_Id');
    	    $requestId = $this->_validation->intValidation($requestId);
            
            if (!empty($requestId['value']))
            {
	        $dbComment    = new Payroll_Model_DbTable_PayrollComment();
                $accessStatus = $dbComment->payrollStatus($requestId['value'], $this->_formNameA);
        
        // when the employee is manager and approver id is login employee id or employee is admin we are allowing the user to update the timesheet status        
		if((($this->_employeeAccessA['Employee']['Is_Manager'] == 1 && $accessStatus['Approver_Id'] == $this->_logEmpId) || ($this->_employeeAccessA['Admin']=='admin')) 
            && $accessStatus['Status'] == 'Applied')
		{
		     if ($this->getRequest()->isPost())
		    {
                         $formData = $this->getRequest()->getPost();

			 $approvalStatus    = $this->_validation->alphaValidation($formData['Approval_Status']);
			 $comments          = $this->_validation->alphaNumSpCDotHySlashValidation($formData['Comments']);
			 $comments['valid'] = $this->_validation->lengthValidation($comments, 5, 600, false);
			
			if(((($approvalStatus['value'] == 'Rejected' || $approvalStatus['value'] == 'Returned') && !empty($comments))
				 ||($approvalStatus['value'] == 'Approved')) && !empty($approvalStatus['value']))
			 {
			    $timesheetEmp=$this->_dbTimesheet->timesheetEmployee($requestId['value']);
		 
			     $commentArray = array('Approval_Status' => $approvalStatus['value'],
						   'Emp_Comment'     => $comments['value'],
						   'Parent_Id'       => $requestId['value'] );
							     
			     $statusReport = $this->_dbTimesheet->statusReport($commentArray, $this->_logEmpId, $this->_formNameA);
			     if ( $statusReport)
			    {
				    $statusMsg = 0;
				    $result    = '';
				    
				    if($this->_logEmpId != $accessStatus['Added_By'])
				    {
						$customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
						$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
						
					    if($accessStatus['Added_By'] == $timesheetEmp['Employee_Id'])
						    $msgDescA = "<p>Your ".$formName." request has been ".strtolower($approvalStatus['value'])."</p>";
					    else
						    $msgDescA = "<p>Your ".$formName." request for the employee ". $timesheetEmp['Employee_Name'] ." has been ".strtolower($approvalStatus['value'])."</p>";
					 	
					    $result = $this->_dbCommonFunction->communicateMail (array('employeeId'     => $accessStatus['Added_By'],
																			       'ModuleName'     => 'Employees',
																			       'formName'       => $this->_formNameA,
																				   'customFormName' => $formName,
																				   'successMsg'     => $formName.' Details',
																				   'formUrl'        => '/employees/timesheets',
																				   'inboxTitle'     => $formName.' Notification',
																			       'mailContent'    => $msgDescA,
																			       'action'         => 'updated'));
					}
				    
				    if($this->_logEmpId != $timesheetEmp['Employee_Id'] &&
				       $timesheetEmp['Employee_Id'] != $accessStatus['Added_By'] && $approvalStatus['value'] == 'Approved')
				    {
						$customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
						$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
						
					    $msgDescX = "<p>Your ".$formName." request has been ".strtolower($approvalStatus['value'])."</p>";
					    
					    $result = $this->_dbCommonFunction->communicateMail (array('employeeId'     => $accessStatus['Added_By'],
																			       'ModuleName'     => 'Employees',
																				   'formName'       => $this->_formNameA,
																				   'customFormName' => $formName,
																				   'successMsg'     => $formName.' Details',
																				   'formUrl'        => '/employees/timesheets',
																				   'inboxTitle'     => $formName.' Notification',
																			       'mailContent'    => $msgDescX,
																			       'action'         => 'updated'));
				    }
				    
				    if (empty($result))
				    {
					    $this->view->result = array('success' => true, 'msg'=>"Status updated successfully", 'type'=>'success');
				    }
				    else
				    {
					    $this->view->result = $result;
				    }
			    }
			    else
			    {
				//$this->view->result = array('success' => false, 'msg'=>"Invalid data", 'type'=>'info');
				    $this->view->result = array('success' => false, 'msg'=>"Unable to update status.Please, contact admin", 'type'=>'info');
			    }
			}
			else
			{
				$this->view->result = array('success' => false, 'msg'=>"Invalid data", 'type'=>'warning');
			}
		    }
		}
		else
		{
			$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'info');
		}
            }
	    
        }
        else
        {
            $this->_helper->redirector('index', 'timesheets', 'employees');
        }
    }   

     public function tsActivityAction()
    {
        $this->_helper->layout->disableLayout();
        if(isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('ts-activity', 'html')->initContext();

            $projectId = $this->_getParam('projectid', null);
            $projectId = filter_var($projectId, FILTER_SANITIZE_NUMBER_INT);

            $formData = $this->getRequest()->getPost();

            $weekEndDate = $formData['weekEndDate'];

            if(!empty($projectId) && !empty($weekEndDate))
            {

                $this->view->result = $this->_dbTimesheet->selectActivity($projectId, null, $weekEndDate);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'timesheets', 'employees');
        }
    }
   
	public function listAuditTimeSheetAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('list-audit-time-sheet', 'json')->initContext();
			
			$requestId = $this->_getParam('timeSheetId', null);
			$requestId = filter_var($requestId, FILTER_SANITIZE_NUMBER_INT);
			
			if(!empty($requestId) && $this->_employeeAccessA['Employee']['View']==1)
			{				 
				$this->view->result = $this->_dbTimesheet->listTimesheetAudit($requestId);
				
            }			
        }
        else
        {
            $this->_helper->redirector('index', 'timesheets', 'employees');
        }
    }
	
	/**
     * Get timesheet hours
     */
    public function getTimesheetHoursAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$employeeId = $this->_getParam('employeeId', null);
            $employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
		    if (!empty($employeeId))
            {
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
				$ajaxContext->addActionContext('get-timesheet-hours', 'json')->initContext();
                
                $gradeId = $this->_dbTimesheet->getEmpTsGrade($employeeId);
				
				$this->view->result = $this->_dbTimesheet->getTimesheetHrsLimit($gradeId);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'timesheets', 'employees');
        }
    }

    public function cloneTimesheetDetailsAction()
    {
        $this->_helper->layout->disableLayout();
		if (isset($_SERVER['HTTP_REFERER']))
		{
		    $ajaxContext = $this->_helper->getHelper('AjaxContext');
		    $ajaxContext->addActionContext('clone-timesheet-details', 'json')->initContext();
				
		    $requestId = $this->_getParam('Request_Id');
		    $requestId = $this->_validation->intValidation($requestId);
		    
		    if ((!empty($requestId['value']) && $this->_employeeAccessA['Employee']['Update'] == 1 ) ||
					(empty($requestId['value']) && $this->_employeeAccessA['Employee']['Add'] == 1 ))
		    {
                if ($this->getRequest()->isPost())
                {
                    $formData           = $this->getRequest()->getPost();
                    $weekEndDate        = $this->_validation->dateValidation($formData['Week_Ending_Date']);
                    $weekEndDateNumber  = date('N', strtotime($weekEndDate['value']));
                    //weekend date should be always saturday if any other day is choosen we need to return the error message.
                    if($weekEndDateNumber==6)
                    {
                        $timesheetDetails   = $this->_dbTimesheet->getTimeSheetDetails($requestId['value']);
                        $lastRequestId      = $this->_dbTimesheet->maxRequestId();
                        $currentRequestId   = $lastRequestId+1;

                        $timesheetDetailsCount = count($timesheetDetails);
                        $timesheetInsertCount = 0;
                        foreach($timesheetDetails as $timesheet)
                        {
                            $timesheetDetails =  array('Request_Id'     	=> $currentRequestId,
                                                'Employee_Id'       => $timesheet['Employee_Id'],
                                                'Approver_Id'       => $timesheet['Approver_Id'],
                                                'Week_Ending_Date'  => date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($weekEndDate['value']))),
                                                'Approval_Status'   => 'Draft',
                                                'Timesheet_Type'    => $timesheet['Timesheet_Type'],
                                                'Project_Id'        => $timesheet['Project_Id'],
                                                'Project_Activity_Id'=> $timesheet['Project_Activity_Id'],
                                                'Day1'			    => $timesheet['Day1'],
                                                'Day2'			    => $timesheet['Day2'],
                                                'Day3'			    => $timesheet['Day3'],
                                                'Day4'			    => $timesheet['Day4'],
                                                'Day5'			    => $timesheet['Day5'],
                                                'Day6'			    => $timesheet['Day6'],
                                                'Day7'			    => $timesheet['Day7'],
                                                'Description'	    => $timesheet['Description'],
                                                'Added_By'          => $this->_logEmpId,
                                                'Added_Date'        => date('Y-m-d H:i:s'));
                            $customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
                            $formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
                            $result = $this->_dbTimesheet->updateTimesheetDetails($timesheetDetails,'Add','', $formName);
                            if(!empty($result['success']))
                            {
                                $timesheetInsertCount++;
                            }
                        }
                        
                        if($timesheetDetailsCount == $timesheetInsertCount)
                        {
                            $timesheetDetails   = $this->_dbTimesheet->getTimeSheetDetails($currentRequestId);
                            $this->view->result = array('success' => true, 'msg'=>'Timesheet details cloned successfully', 'type'=>'success','timesheetDetails'=>$timesheetDetails);
                        }
                        else
                        {
                            $this->view->result = array('success'=>false, 'msg'=>'<div>Timesheet data are not cloned due to one of the reason.
                                                            <li>	1) Total activity hours for this weekend should not exceed the regular/overtime hours limit.</li>
                                                            <li>	2) Activity already exist for this timesheet type and weekend date.</li>
                                                            <li>	3) Timesheets already exist for this weekend date.</li></div>', 'type'=>'info');
                        }
                    }
                    else 
                    {
                        $this->view->result = array('success' => false, 'msg'=>'Please select a valid weekend date', 'type'=>'warning');
                    }
                }
		    }
            else
		    {
					$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'info');
		    }
		}
		else
		{
		    $this->_helper->redirector('index', 'timesheets', 'employees');
		}
    }
    
    public function __destruct()
    {
        
    }  
    
}