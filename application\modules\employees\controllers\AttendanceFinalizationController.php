<?php
//=========================================================================================
//=========================================================================================
/* Program : AttendanceFinalizationController.php 								         *
 * Property of Caprice Technologies Pvt Ltd,											 *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,										 *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies				 *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : The attendance Finalization is where the Attendance records that are in *
 *  	         Applied/Draft status will be listed. And if the Employees have no       *
 *               leave/attendance for a day, It will be listed.                          *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *	Version    Date                 Author                  Description                  *
 *	0.1        26-Dec-2019          Ahalya                Initial Version         	     *
 *                                                                                    	 *
 *  																	                 *
 *                                                                                   	 */
//=========================================================================================
//=========================================================================================
include APPLICATION_PATH."/validations/Validations.php";

class Employees_AttendanceFinalizationController extends Zend_Controller_Action
{
    /**declare the variables to be used */
    protected $_validation      = null;
    protected $_hrappMobile     = null;
    protected $_dbAccessRights  = null;
    protected $_dbCommonFun     = null;
    protected $_logEmpId        = null;
    protected $_formName        = 'Attendance Finalization';
    protected $_formNameA       = 'Attendance';
    protected $_ehrTables       = null;
    protected $_dbAttendance    = null;
    protected $_dbAttendanceFinalization    = null;
    protected $_attendanceIncompleteFormId = 367;
    protected $_noAttendanceFormId = 368;
    protected $_attendanceShortageFormId = 369;
    protected $_earlyCheckOutFormId = 324;
    
    public function init()
    {
        $this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
        if($this->_hrappMobile->checkAuth()) {
            $this->_dbAccessRights    = new Default_Model_DbTable_AccessRights();
            $this->_validation 	      = new Validations();
            $this->_dbCommonFunction  = new Application_Model_DbTable_CommonFunction();
            $this->_ehrTables         = new Application_Model_DbTable_Ehr();
            $this->_dbAttendance      = new Employees_Model_DbTable_Attendance();
            $this->_dbAttendanceFinalization = new Employees_Model_DbTable_AttendanceFinalization();

            $userSession              = $this->_dbCommonFunction->getUserDetails ();
			$this->_logEmpId          = $userSession['logUserId'];
            $this->_attendanceFinalizationAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formName);

            $this->_attendanceIncompleteAccessRights      = $this->_dbAccessRights->employeeAccessRightsBasedOnFormId($this->_logEmpId,$this->_attendanceIncompleteFormId);
            $this->_noAttendanceAccessRights      = $this->_dbAccessRights->employeeAccessRightsBasedOnFormId($this->_logEmpId,$this->_noAttendanceFormId);
            $this->_attendanceShortageAccessRights      = $this->_dbAccessRights->employeeAccessRightsBasedOnFormId($this->_logEmpId,$this->_attendanceShortageFormId);
            $this->_earlyCheckOutAccessRights      = $this->_dbAccessRights->employeeAccessRightsBasedOnFormId($this->_logEmpId,$this->_earlyCheckOutFormId);
        }
        else
        {
            if (Zend_Session::namespaceIsset('lastRequest'))
                Zend_Session:: namespaceUnset('lastRequest');
            
            $session = new Zend_Session_Namespace('lastRequest');
            $session->lastRequestUri = 'v3/my-team/attendance/attendance-finalization'; 
            $this->_redirect('auth');
        }

    }

    public function indexAction()
    {
        $checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

        if ($checkSessionAuth)
        {
            $this->_helper->layout()->disableLayout()->setLayout('admin_layout');
            $this->getHelper('viewRenderer')->setViewSuffix('html');
        } else {
			$this->_redirect('auth');
		}
    }

    /* Function to retrieve the attendance or no attendance records. */
    public function listAttendanceFinalizationAction()
    {
		$this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('list-attendance-finalization', 'json')->initContext();

            if($this->getRequest()->isPost())
            {
                $formData = $this->getRequest()->getPost();
                if(empty($formData )){
                    $requestBody =$this->getRequest()->getRawBody();
                    if ($requestBody)
                    {
                        $formData = Zend_Json::decode($requestBody);
                    }
                    else{								
                        $this->view->result = array('success'=>false, 'msg'=>'Sorry! Something went wrong. Please contact system admin', 'type'=>'warning','requestBody'=>$requestBody);				
                    }						
                }
                $regularizationRequestLimitDetails = array();

                /* there are two methods of finalization, attendance and no attendance */
                $finalizationMethod = filter_var($formData['finalizationMethod'], FILTER_SANITIZE_STRIPPED);
                $finalizationMethod = $this->_validation->alphaValidation($finalizationMethod); 

                $actualSubTab = isset($formData['actualSubTab']) ? $formData['actualSubTab'] : '';
                $attendanceIncompleteAccess = ($this->_attendanceIncompleteAccessRights['Employee']['View']==1 && $actualSubTab== 'attTab');
                $noAttendanceAccess = ($this->_noAttendanceAccessRights['Employee']['View']==1 && $actualSubTab== 'noAttTab');
                $attendanceShortageAccess = ($this->_attendanceShortageAccessRights['Employee']['View']==1 && $actualSubTab== 'shortTab');

                if($this->_attendanceFinalizationAccessRights['Employee']['View']==1 || ($attendanceIncompleteAccess || $noAttendanceAccess || $attendanceShortageAccess) || ($finalizationMethod['value'] === 'dashboardAttendance' || $finalizationMethod['value'] === 'dashboardNoAttendance'))
                {
                    /* Common filter fields for both attendance and no attendance finalization */
                    if(isset($formData['employeeType'])){
                        $employeeType   = $formData['employeeType'];
                    }
                    else{
                        $employeeType   = '';
                    }

                    if(isset($formData['location'])){
                        $location       = $formData['location'];
                    }
                    else{
                        $location       = '';
                    }

                    if(isset($formData['department'])){
                        $department     = $formData['department'];
                    }
                    else{
                        $department     = '';
                    }
                    
                    if(isset($formData['employeeId'])){
                        $payslipEmpIds  = $formData['employeeId'];
                    }
                    else{
                        $payslipEmpIds  = '';
                    }
                    
                    $startDate  = filter_var($formData['startDate'], FILTER_SANITIZE_STRIPPED);
                    $startDate  = $this->_validation->dateValidation($startDate); 

                    $endDate    = filter_var($formData['endDate'], FILTER_SANITIZE_STRIPPED);
                    $endDate    = $this->_validation->dateValidation($endDate); 

                    //send the subtab value to differentiate to get the leave details
                    

                    $filterArray = array('employeeType' => $employeeType,
								        'location'      => $location,
                                        'department'    => $department,
                                        'startDate'     => $startDate['value'],
                                        'endDate'       => $endDate['value'],
                                        'payslipEmployeeIds' => $payslipEmpIds);
                   
                    if(!empty($finalizationMethod['value']) && $finalizationMethod['valid'] && 
                        (empty($startDate['value']) || (!empty($startDate['value']) && $startDate['valid'])) &&
                        (empty($endDate['value']) || (!empty($endDate['value']) && $endDate['valid'])) &&
                        (empty($payslipEmpIds) || (!empty($payslipEmpIds) && is_array($payslipEmpIds)))
                        ){
                        
                        if($finalizationMethod['value'] === 'attendance' || $finalizationMethod['value'] === 'noAttendance')
                        {
                            if(!empty($payslipEmpIds) && is_array($payslipEmpIds) && $finalizationMethod['value'] === 'noAttendance')
                            {
                                $filterMinDate = $displayMinDate = $filterArray['startDate'];
                                $filterMaxDate = $displayMaxDate = $filterArray['endDate'];
                            }
                            else if(!empty($filterArray['startDate']) && !empty($filterArray['endDate']))
                            {
                                $filterMinDate = $displayMinDate = $filterArray['startDate'];
                                $filterMaxDate = $displayMaxDate = $filterArray['endDate'];
                            }
                            else
                            {
                                /* Call the function to get the next payslip-start and end date or the financial year-start and end date */
                                $filterMinMaxDates = $this->_dbCommonFunction->getLastPayslipMonth(null, null, null, 190);
                            
                                $filterMinDate = $displayMinDate = $filterMinMaxDates['salaryDate'];
                                
                                /* Get filter max date */
                                $currentDate = date('Y-m-d');

                                /* If current date is lesser than salary start date or current date is greater than salary end date then
                                last salary date should be set as max date */
                                if((strtotime($currentDate) < strtotime($filterMinMaxDates['salaryDate'])) ||
                                (strtotime($currentDate) > strtotime($filterMinMaxDates['lastSalaryDate']))){
                                    $filterMaxDate = $filterMinMaxDates['lastSalaryDate'];
                                }
                                else{
                                    $filterMaxDate = $currentDate;
                                }
                                $displayMaxDate = $filterMaxDate;
                            }
                        }
                        else
                        {
                            /** if the finalization method is dashboardAttendance or dashboardNoAttendance */

                            $currentDate = date('Y-m-d');
                            $currentDateMinusFourty = date('Y-m-d',strtotime('-39 days',strtotime($currentDate)));

                            $minMaxDates = $this->_dbCommonFunction->getLastPayslipMonth($this->_logEmpId, '', $finalizationMethod['value'], 190);
                            if(isset($minMaxDates['payslipGenerated']) && $minMaxDates['payslipGenerated'] === 1){
                                $dojOrPayMonthStartDate =  $minMaxDates['salaryDate'];
                            }else{
                                //Get the date of join
                                $dojOrPayMonthStartDate = $this->_dbCommonFunction->getDateOfJoin($this->_logEmpId);
                            }

                            //Compare date of join or (last payslip generated month+ 1month - salary date) and the (current date - 40 days)
                            if(strtotime($currentDateMinusFourty) > strtotime($dojOrPayMonthStartDate)){
                                /** Consider the payslip generated for june,2022 month and the current date is 20 Aug 2022 then the (current date-40 days)
                                 * is 10 July 2022. In this case,the dojOrPayMonthStartDate will be 1 July 2022. So we need to present regularization from 10 July 2022.*/
                                /** Or if the employee joins after before 10 July 2022 and payslip is not generated
                                 * then we will present the regularization list from 10 July 2022.*/
                                $startDate = $currentDateMinusFourty;
                            }else{
                                $startDate = $dojOrPayMonthStartDate;
                            }

                            $filterMinDate = $displayMinDate = $startDate;
                            $filterMaxDate = $displayMaxDate = $currentDate;

                            $regularizationValidationResult = $this->_dbAttendanceFinalization->getNewMinDateByRegularizationCutOff($filterMinDate,$currentDate);
                            if(!empty($regularizationValidationResult['regularizationNextMonthStartDate'])){
                                $filterMinDate = $regularizationValidationResult['regularizationNextMonthStartDate'];
                               
                                if($regularizationValidationResult['attendanceRegularizationAfter'] === 'current date'){
                                    /** Consider the current date is 27 Jun 2024. If the cut off day is 1 then new filter min date will be 26 Jun 2024. 
                                     * 1. If the payslip is generated for Jun 2024 then input filter min date will be 1 jul 2024. As the payslip is
                                     * generated the employee cannot regularize attendance for june month. So the below condition is checked to change 
                                     * the min date to 1 jul 2024.
                                     * 2. If the payslip is not generated for jun 2024 or if the employee joined before 26 Jun 2024 then newFilterMinDate
                                     * value will remain the same
                                     * 3. If the employee joined on 2 Jul 2024 then new filter min date value will be changed to 2 Jul 2024  
                                     * In the first and third scenarios, the minimum date will be greater than the current date(max date). So the max date
                                     * value should be changed. */
                                    if(strtotime($filterMinDate) > strtotime($filterMaxDate)){
                                        $filterMaxDate = $filterMinDate;
                                    }
                                }
                            }
                            $filterArray['payslipEmployeeIds'] = array($this->_logEmpId);
                        }

                        /* Update min and max date in filter array */
                        if(empty($filterArray['startDate']))
                            $filterArray['startDate'] = $filterMinDate;

                        if(empty($filterArray['endDate']))
                            $filterArray['endDate'] = $filterMaxDate;

                        $hrReports = new Reports_Model_DbTable_HrReports();
                        $strictModeEmployees = $hrReports->getStrictModeEmployees();

                        //Validate the attendance regularization request limit exceed or not
                        if($finalizationMethod['value'] === 'dashboardAttendance' || $finalizationMethod['value'] === 'dashboardNoAttendance'){
                            $regularizationRequestLimitDetails = $this->_dbAttendanceFinalization->formRegularizationRequestLimitResponse($this->_logEmpId,$filterArray['startDate'],$filterArray['endDate']);
                        }

						if($finalizationMethod['value'] == 'attendance'  || $finalizationMethod['value'] === 'dashboardAttendance') { 
                            /** The attendance finalization method filter has status field 
                             * We should retrieve only the attendance record which are in Draft and Apllied status. 
                             * The default value of the status field in the filter should be array('Draft','Applied')  */
                            if(!empty($formData['status'])) {
                                $filterArray['status'] = $formData['status'];
                            } else {
                                $filterArray['status'] = array('Draft','Applied');
                            }

                            if($attendanceIncompleteAccess && $actualSubTab== 'attTab') 
                            {
                                $isManager = $this->_attendanceIncompleteAccessRights['Employee']['Is_Manager'];
                                $isAdmin   = $this->_attendanceIncompleteAccessRights['Admin'];
                            }
                            else if($noAttendanceAccess && $actualSubTab== 'noAttTab') 
                            {
                                $isManager = $this->_noAttendanceAccessRights['Employee']['Is_Manager'];
                                $isAdmin   = $this->_noAttendanceAccessRights['Admin'];
                            }
                            else if($attendanceShortageAccess && $actualSubTab== 'shortageTab') 
                            {
                                $isManager = $this->_attendanceShortageAccessRights['Employee']['Is_Manager'];
                                $isAdmin   = $this->_attendanceShortageAccessRights['Admin'];
                            }
                            else if($this->_attendanceFinalizationAccessRights['Employee']['View']==1)
                            {
                                $isManager = $this->_attendanceFinalizationAccessRights['Employee']['Is_Manager'];
                                $isAdmin   = $this->_attendanceFinalizationAccessRights['Admin'];   
                            }
                            else
                            {
                                $isManager = 0;
                                $isAdmin   = 0;
                            }

                            $attendanceFinalizationAccess = array('Is_Manager' => $isManager,
                                                                  'Admin'       => $isAdmin);

                            $attendanceData = $this->_dbAttendanceFinalization->listAttendanceFinalization($filterArray, $finalizationMethod['value'],$this->_logEmpId,$attendanceFinalizationAccess);
                            $this->view->result = array('success' => true, 'msg'=>"", 'type'=>'success', 'attendanceData'=>$attendanceData, 'noAttendanceData'=>array(), 'strictModeEmployees'=> $strictModeEmployees, 'filterMinDate'=> $displayMinDate, 'filterMaxDate'=> $displayMaxDate,'regularizationRequestLimitDetails'=>$regularizationRequestLimitDetails);
							
						} else if($finalizationMethod['value'] === 'noAttendance' || $finalizationMethod['value'] === 'dashboardNoAttendance'){

                            $currentDate = date('Y-m-d');
                            if($finalizationMethod['value'] === 'noAttendance' && $displayMaxDate==$currentDate)
                            {
                                $displayMaxDate = date('Y-m-d',strtotime('-1 days',strtotime($displayMaxDate)));
                                $filterArray['endDate'] = $displayMaxDate;
                            }

                            $noAttendanceData = $this->_dbAttendanceFinalization->listNoAttendance($filterArray,$this->_logEmpId, $finalizationMethod['value'],$actualSubTab);

                            
                            $this->view->result = array('success' => true, 'msg'=>"", 'type'=>'success', 'attendanceData'=>array(), 'noAttendanceData'=>$noAttendanceData, 'strictModeEmployees'=> $strictModeEmployees, 'filterMinDate'=> $displayMinDate, 'filterMaxDate'=> $displayMaxDate,'regularizationRequestLimitDetails'=>$regularizationRequestLimitDetails);
						} else {
                            /* Finalization method should be attendance or noAttendance */
							$this->view->result = array('success' => false, 'msg'=>"Sorry! Something went wrong. Please contact system admin", 'type'=>'warning', 'attendanceData'=>array(),'noAttendanceData'=>array(), 'strictModeEmployees'=> array(), 'filterMinDate'=> '', 'filterMaxDate'=> '','regularizationRequestLimitDetails'=>$regularizationRequestLimitDetails);
						}
					} else {
						$this->view->result = array('success' => false, 'msg'=>"Sorry! Something went wrong. Please contact system admin", 'type'=>'warning', 'attendanceData'=>array(),'noAttendanceData'=>array(), 'strictModeEmployees'=> array(), 'filterMinDate'=> '', 'filterMaxDate'=> '','regularizationRequestLimitDetails'=>$regularizationRequestLimitDetails);
                    }
                } else {
				$this->view->result = array('success' => false, 'msg'=>"Sorry! Access Denied", 'type'=>'warning', 'attendanceData'=>array(),'noAttendanceData'=>array(), 'strictModeEmployees'=> array(), 'filterMinDate'=> '', 'filterMaxDate'=> '','regularizationRequestLimitDetails'=>$regularizationRequestLimitDetails);
			    }
            } else {
                $this->view->result = array('success' => false, 'msg'=>"Sorry! Something went wrong. Please contact system admin", 'type'=>'warning', 'attendanceData'=>array(),'noAttendanceData'=>array(), 'strictModeEmployees'=> array(), 'filterMinDate'=> '', 'filterMaxDate'=> '','regularizationRequestLimitDetails'=>$regularizationRequestLimitDetails);
            }
		}
    }

    /* Function to validate the attendance finalization records before auto closure */
    public function validateAttendanceFinalizationAction()
    {
        $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('validate-attendance-finalization', 'json')->initContext();
			
			if(($this->_attendanceFinalizationAccessRights['Employee']['Optional_Choice']==1 || $this->_noAttendanceAccessRights['Employee']['Add']==1) && !empty($this->_noAttendanceAccessRights['Admin']))
			{
                if ($this->getRequest()->isPost())
                {
                    $formData = $this->getRequest()->getPost();
                    if(empty($formData )){
                        $requestBody =$this->getRequest()->getRawBody();
                        if ($requestBody)
                        {
                            $formData = Zend_Json::decode($requestBody);
                        }
                        else{								
                            $this->view->result = array('success'=>false, 'msg'=>'Sorry! Something went wrong. Please contact system admin', 'type'=>'warning','requestBody'=>$requestBody);				
                        }						
                    }

                    $attendanceId = filter_var($formData['attendanceId'], FILTER_SANITIZE_NUMBER_INT, FILTER_REQUIRE_ARRAY);
                    
                    if(!empty($attendanceId)){
                        $validateAttendanceData = $this->_dbAttendanceFinalization->validateAttendanceFinalization($attendanceId);
                        if(!empty($validateAttendanceData)){
                            $validatedResult = array(
                                'totalRecords'=>count($attendanceId),
                                'validAttendanceIds'=>$validateAttendanceData['validAttendanceIds'],
                                'invalidRecords'=>$validateAttendanceData['invalidRecords'],
                                'invalidReason'=>$validateAttendanceData['invalidReason']
                                );

                            $this->view->result = array('success' => true, 'type'=>'success', 'msg'=>"", 'validatedResult' => $validatedResult);
                        } else {
                            $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! could not Initiate the Auto Closure. Please try after some time", 'validatedResult'=>null);
                        }
                    } else {
                        $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Please select minimum one record to initiate the auto closure", 'validatedResult'=>null);
                    }
                } else {
                    $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! Something went wrong. Please contact system admin", 'validatedResult'=>null);
                }
            } else {
                $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! Access Denied", 'validatedResult'=>null);
            }
        } else {
            $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! Something went wrong. Please contact system admin", 'validatedResult'=>null);
        }
    }

    /* Function to get the unpaid leave types(normal leave types) */
    public function getUnpaidLeavesAction()
    {
        $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('get-unpaid-leaves', 'json')->initContext();
            
            if(($this->_attendanceFinalizationAccessRights['Employee']['View']==1 || $this->_noAttendanceAccessRights['Employee']['Add']==1) && !empty($this->_noAttendanceAccessRights['Admin']))
            {
				$this->view->result = array('success' => true, 'msg' => "", 'type' => 'success', 'leaveTypes' => $this->_dbAttendanceFinalization->getUnpaidLeaves());
            }
            else 
            {
				$this->view->result = array('success' => false, 'msg' => "Sorry! Access Denied", 'type' => 'warning', 'leaveTypes' => array());
			}
        }
    }

    public function deleteAttendanceFinalizationAction()
    {
        $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-attendance-finalization', 'json')->initContext();
            
            if(($this->_attendanceFinalizationAccessRights['Employee']['Optional_Choice']==1 || $this->_attendanceIncompleteAccessRights['Employee']['Delete']==1) && !empty($this->_attendanceIncompleteAccessRights['Admin']))
            {
                if ($this->getRequest()->isPost())
                {
                    $formData = $this->getRequest()->getPost();
                    if(empty($formData )){
                        $requestBody =$this->getRequest()->getRawBody();
                        if ($requestBody)
                        {
                            $formData = Zend_Json::decode($requestBody);
                        }
                        else{								
                            $this->view->result = array('success'=>false, 'msg'=>'Sorry! Something went wrong. Please contact system admin', 'type'=>'warning','requestBody'=>$requestBody);				
                        }						
                    }
					
					$attendanceId = filter_var($formData['attendanceId'], FILTER_SANITIZE_NUMBER_INT, FILTER_REQUIRE_ARRAY);
					
					if(count($attendanceId)>0)
					{
                        $customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
						$customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
						$deletedCount = 0;

                        $attendanceSummarySelectedDetails = [];
					    foreach($attendanceId as $val)
                        {
                            $deleted = $this->_dbAttendance->deleteAttendance($val, $this->_logEmpId, $this->_formNameA, $customFormNamee);
                            if($deleted['success']){
                                $deletedCount++;
                                $attendanceSummarySelectedDetails[] = $deleted['attendanceDetails'];
                            }
                        }
                        $this->_dbCommonFunction->triggerAttendanceSummaryStepFunction($attendanceSummarySelectedDetails,'attendance');

                        if(count($attendanceId) == $deletedCount)
                            $this->view->result = array('success' => true, 'msg'=>$customFormNamee." record deleted successfully", 'type'=>'success');
                        elseif($deletedCount > 1)
                            $this->view->result = array('success' => true, 'msg'=>$customFormNamee." record deleted partially", 'type'=>'success');
                        else
                            $this->view->result = array('success' => true, 'msg'=>"Unable to delete ".$customFormNamee." record", 'type'=>'success');
                    } else {
						$this->view->result = array('success' => false, 'msg'=>"Please select minimum one record to initiate the deletion", 'type'=>'info');	
					}
                } else {
                    $this->view->result = array('success' => false, 'msg'=>"Sorry! Something went wrong. Please contact system admin", 'type'=>'warning');
                }
            }
            else 
            {
				$this->view->result = array('success' => false, 'msg' => "Sorry! Access Denied", 'type' => 'warning');
			}
        }
    }

    /* Validate auto LOP records */
    public function validateAutoLopAction()
    {
        $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('validate-auto-lop', 'json')->initContext();
			
			if(($this->_attendanceFinalizationAccessRights['Employee']['Optional_Choice']==1 || $this->_noAttendanceAccessRights['Employee']['Add']==1) && !empty($this->_noAttendanceAccessRights['Admin']))
			{
                if ($this->getRequest()->isPost())
                {
                    $formData = $this->getRequest()->getPost();
                    if(empty($formData )){
                        $requestBody =$this->getRequest()->getRawBody();
                        if ($requestBody)
                        {
                            $formData = Zend_Json::decode($requestBody);
                        }
                        else{								
                            $this->view->result = array('success'=>false, 'msg'=>'Sorry! Something went wrong. Please contact system admin', 'type'=>'warning','requestBody'=>$requestBody);				
                        }						
                    }

                    $autoLopDetails  = $formData['Auto_LOP_Details'];

                    $leaveTypeId  = filter_var($formData['LeaveType_Id'], FILTER_SANITIZE_NUMBER_INT);
                    $leaveTypeId  = $this->_validation->intValidation($leaveTypeId);

                    $startDate  = filter_var($formData['startDate'], FILTER_SANITIZE_STRIPPED);
                    $startDate  = $this->_validation->dateValidation($startDate); 

                    $endDate    = filter_var($formData['endDate'], FILTER_SANITIZE_STRIPPED);
                    $endDate    = $this->_validation->dateValidation($endDate);

                    /* Validate inputs */
                    if(is_array($autoLopDetails) && count($autoLopDetails)>0 && ($leaveTypeId['valid'] && !empty($leaveTypeId['value'])) &&
                    (!empty($startDate['value']) && $startDate['valid']) && (!empty($endDate['value']) && $endDate['valid'])){
                        /* Validate the lop records */
                        $autoLopValidatedResult = $this->_dbAttendanceFinalization->validateAutoLop($autoLopDetails,$leaveTypeId['value'],$startDate['value'],$endDate['value']);
                        if(!empty($autoLopValidatedResult)){
                            if(empty($autoLopValidatedResult['Custom_Msg'])){
                                $this->view->result = array('success' => true, 'type'=>'success', 'msg'=>"", 'validatedResult' => $autoLopValidatedResult);
                            }else{
                                $this->view->result = array('success' => false, 'type'=>'success', 'msg'=>$autoLopValidatedResult['Custom_Msg'], 'validatedResult' => null);
                            }
                        } else {
                            $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! could not validate Auto LOP. Please try after some time.", 'validatedResult'=>null);
                        }
                    } else {
                        $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! Something went wrong. Please contact system admin.", 'validatedResult'=>null);
                    }
                } else {
                    $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! Something went wrong. Please contact system admin.", 'validatedResult'=>null);
                }
            } else {
                $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! Access Denied.", 'validatedResult'=>null);
            }
        } else {
            $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! Something went wrong. Please contact system admin.", 'validatedResult'=>null);
        }
    }


    public function initiateAutoAttendanceAction()
    {
        $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('initiate-auto-attendance', 'json')->initContext();
			
			if(($this->_attendanceFinalizationAccessRights['Employee']['Optional_Choice']==1 || $this->_noAttendanceAccessRights['Employee']['Add']==1) && !empty($this->_noAttendanceAccessRights['Admin']))
			{
                if ($this->getRequest()->isPost())
                {
                    $formData = $this->getRequest()->getPost();
                    if(empty($formData )){
                        $requestBody =$this->getRequest()->getRawBody();
                        if ($requestBody)
                        {
                            $formData = Zend_Json::decode($requestBody);
                        }
                        else{								
                            $this->view->result = array('success'=>false, 'msg'=>'Sorry! Something went wrong. Please contact system admin', 'type'=>'warning','requestBody'=>$requestBody);				
                        }						
                    }
                    $autoAttendanceDetails  = $formData['Auto_Attendance_Details'];
                    /* Validate inputs */
                    if(is_array($autoAttendanceDetails) && count($autoAttendanceDetails)>0){
                        /* Validate the lop records */
                        $autoAttendanceResult = $this->_dbAttendanceFinalization->initiateAutoAttendance($autoAttendanceDetails,$this->_logEmpId);
                        if(!empty($autoAttendanceResult['insertAttendanceDetails'])){
                            if(strtolower($autoAttendanceResult['attendanceInsertion'])=='complete')
                            {
                                $this->view->result = array('success' => true, 'type'=>'success', 'msg'=>"Attendance record added successfully");
                            }
                            else
                            {
                                $this->view->result = array('success' => true, 'type'=>'success', 'msg'=>"Attendance record added paritally");
                            }
                            
                        } else {
                            $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! unable to insert attendance record. Please try after some time.");
                        }
                    } else {
                        $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! Something went wrong. Please contact system admin.");
                    }
                } else {
                    $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! Something went wrong. Please contact system admin.");
                }
            } else {
                $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! Access Denied.");
            }
        } else {
            $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! Something went wrong. Please contact system admin.");
        }
    }

    public function initiateAutoLopAction()
    {
        $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('initiate-auto-lop', 'json')->initContext();
			
			if(($this->_attendanceFinalizationAccessRights['Employee']['Optional_Choice']==1 || $this->_noAttendanceAccessRights['Employee']['Add']==1) && !empty($this->_noAttendanceAccessRights['Admin']))
			{
                if ($this->getRequest()->isPost())
                {
                    $formData = $this->getRequest()->getPost();
                    if(empty($formData )){
                        $requestBody =$this->getRequest()->getRawBody();
                        if ($requestBody)
                        {
                            $formData = Zend_Json::decode($requestBody);
                        }
                        else{								
                            $this->view->result = array('success'=>false, 'msg'=>'Sorry! Something went wrong. Please contact system admin', 'type'=>'warning','requestBody'=>$requestBody);				
                        }						
                    }

                    $autoLopDetails  = $formData['Auto_LOP_Details'];

                    $leaveTypeId  = filter_var($formData['LeaveType_Id'], FILTER_SANITIZE_NUMBER_INT);
                    $leaveTypeId  = $this->_validation->intValidation($leaveTypeId);

                    $startDate  = filter_var($formData['startDate'], FILTER_SANITIZE_STRIPPED);
                    $startDate  = $this->_validation->dateValidation($startDate); 

                    $endDate    = filter_var($formData['endDate'], FILTER_SANITIZE_STRIPPED);
                    $endDate    = $this->_validation->dateValidation($endDate);

                    /* Validate inputs */
                    if(is_array($autoLopDetails) && count($autoLopDetails)>0 && ($leaveTypeId['valid'] && !empty($leaveTypeId['value'])) &&
                    (!empty($startDate['value']) && $startDate['valid']) && (!empty($endDate['value']) && $endDate['valid'])){
                        /* Get the custom form name for the attendance finalization form */
                        $customFormName = $this->_ehrTables->getCustomForms($this->_formName);
                        $customFormName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formName);

                        /* Validate and get the employee leave details and apply auto lop */
                        $this->view->result = $this->_dbAttendanceFinalization->getAutoLopEmpLeaveDetails($autoLopDetails,$leaveTypeId['value'],
                                                                $startDate['value'],$endDate['value'],$this->_logEmpId,$this->_formName,$customFormName);
                        
                    } else {
                        $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! Something went wrong. Please contact system admin.");
                    }
                } else {
                    $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! Something went wrong. Please contact system admin.");
                }
            } else {
                $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! Access Denied.");
            }
        } else {
            $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! Something went wrong. Please contact system admin.");
        }
    }

    public function initiateAttendanceShortageLeaveAction()
    {
        $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('initiate-attendance-shortage-leave', 'json')->initContext();
			
			if(($this->_attendanceFinalizationAccessRights['Employee']['Optional_Choice']==1 || $this->_attendanceShortageAccessRights['Employee']['Add']==1) && !empty($this->_attendanceShortageAccessRights['Admin']))
			{
                if ($this->getRequest()->isPost())
                {
                    $formData = $this->getRequest()->getPost();
                    if(empty($formData )){
                        $requestBody =$this->getRequest()->getRawBody();
                        if ($requestBody)
                        {
                            $formData = Zend_Json::decode($requestBody);
                        }
                        else{								
                            $this->view->result = array('success'=>false, 'msg'=>'Sorry! Something went wrong. Please contact system admin', 'type'=>'warning','requestBody'=>$requestBody);				
                        }						
                    }
                    $noAttendanceDetails  = $formData['noAttendanceDetails'];
                    if(is_array($noAttendanceDetails) && count($noAttendanceDetails)>0)
                    {
                        $this->view->result = $this->_dbAttendanceFinalization->InitiateLeaveAsPerAttendanceShortage($noAttendanceDetails,$this->_logEmpId);
                    }
                    else
                    {
                        $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Invalid Data");
                    }
                } else {
                    $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! Something went wrong. Please contact system admin.");
                }
            } else {
                $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! Access Denied.");
            }
        } else {
            $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! Something went wrong. Please contact system admin.");
        }
    }

    public function initiateAutoClosureAndApprovalAction(){
        $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('initiate-auto-closure-and-approval', 'json')->initContext();
			
			if(($this->_attendanceFinalizationAccessRights['Employee']['Optional_Choice']==1 || $this->_attendanceIncompleteAccessRights['Employee']['Add']==1) && !empty($this->_attendanceIncompleteAccessRights['Admin']))
			{
                if ($this->getRequest()->isPost())
                {
                    $formData = $this->getRequest()->getPost();
                    if(empty($formData )){
                        $requestBody =$this->getRequest()->getRawBody();
                        if ($requestBody)
                        {
                            $formData = Zend_Json::decode($requestBody);
                        }
                        else{								
                            $this->view->result = array('success'=>false, 'msg'=>'Sorry! Something went wrong. Please contact system admin', 'type'=>'warning','requestBody'=>$requestBody);				
                        }						
                    }
            
                    $attendanceId = filter_var($formData['attendanceId'], FILTER_SANITIZE_NUMBER_INT, FILTER_REQUIRE_ARRAY);
                    
                    if(!empty($attendanceId)){
                        $customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
                        $customFormName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);

                        $resultData = $this->_dbAttendanceFinalization->initiateAutoClosureAndApproval($attendanceId, $this->_logEmpId, 'Attendance', $customFormName);
                        $this->view->result = $resultData;
                    } else {
                        $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Please select minimum one record to initiate the auto closure", 'resultData'=>null);
                    }
                } else {
                    $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! Something went wrong. Please contact system admin", 'resultData'=>null);
                }
            } else {
                $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! Access Denied", 'resultData'=>null);
            }
        } else {
            $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! Something went wrong. Please contact system admin", 'resultData'=>null);
        }
    }

    public function initiateIgnoreAttendanceShortageAction(){
        $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('initiate-ignore-attendance-shortage', 'json')->initContext();
			
			if(($this->_attendanceFinalizationAccessRights['Employee']['Optional_Choice']==1 || $this->_attendanceShortageAccessRights['Employee']['Add']==1) && !empty($this->_attendanceShortageAccessRights['Admin']))
			{
                if ($this->getRequest()->isPost())
                {
                    $formData = $this->getRequest()->getPost();
                    if(empty($formData )){
                        $requestBody =$this->getRequest()->getRawBody();
                        if ($requestBody)
                        {
                            $formData = Zend_Json::decode($requestBody);
                        }
                        else{								
                            $this->view->result = array('success'=>false, 'msg'=>'Sorry! Something went wrong. Please contact system admin', 'type'=>'warning','requestBody'=>$requestBody);				
                        }						
                    }

                    $attendanceShortageDetails  = $formData['ignoreAttShortageEmp'];
                    if(is_array($attendanceShortageDetails) && count($attendanceShortageDetails)>0)
                    {
                        $this->view->result = $this->_dbAttendanceFinalization->initiateIgnoreAttendanceShortage($attendanceShortageDetails,$this->_logEmpId);
                    }
                    else
                    {
                        $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Invalid Data");
                    }
                } else {
                    $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! Something went wrong. Please contact system admin");
                }
            } else {
                $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! Access Denied");
            }
        } else {
            $this->view->result = array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! Something went wrong. Please contact system admin");
        }
    }

    /* Function to get strict mode employees */
    public function getStrictModeEmployeesAction()
    {
        $this->_helper->layout()->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('get-strict-mode-employees', 'json')->initContext();
            if($this->_attendanceShortageAccessRights['Employee']['View']==1 || $this->_earlyCheckOutAccessRights['Employee']['View']==1)
            {
                $hrReports = new Reports_Model_DbTable_HrReports();
                $strictModeEmployees = $hrReports->getStrictModeEmployees();
                $this->view->result = array('success' => true, 'msg' => "", 'type' => 'success', 'strictModeEmployees' => $strictModeEmployees);
            }
            else
            {
                $this->view->result = array('success' => false, 'msg' => "Sorry! Access Denied", 'type' => 'warning', 'strictModeEmployees' => array());
            }
        } else {
            $this->view->result = array('success' => false, 'msg' => "Sorry! Something went wrong. Please contact system admin", 'type' => 'warning', 'strictModeEmployees' => array());
        }
    }

    public function __destruct()
    {
        
    }
}



