<?php
//=========================================================================================
//=========================================================================================
/* Program : Personal.php														         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MYSQL query for employee personal details							     *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Narmadha                Initial Version         	         *
 *                                                                                       *
 *  0.2        25-Jul-2014    Mahesh				  Modified Function                	 *
 *												      1.employeeDetail					 *
 *												                                         *
 *  1.0        02-Feb-2015    Sandhosh, Prasanth   Changes in file for mobile app        *
 *                                                    1.Extra fields are added in        *
 *                                                    field list of list query.          *
 *                                                    2.employeeName                     *
 *                                                                                       *
 *  1.4        05-Mar-2016    Nivethitha              Changes in file for Bootstrap      *
 *                                                                                       */
//=========================================================================================
//=========================================================================================
class Employees_Model_DbTable_Personal extends Zend_Db_Table_Abstract
{

    protected $_dbManager = null;

    protected $_dbJobDetail = null;

    protected $_db = null;

    protected $_ehrTables = null;
    
    protected $_orgDF = null;
	
	protected $_hrappMobile = null;
	
	protected $_isMobile = null;
    
    protected $_dbCommonFun   = null;

    public function init()
    {
        $this->_ehrTables = new Application_Model_DbTable_Ehr();
        $this->_db = Zend_Registry::get('subHrapp');
        $this->_dbManager = new Default_Model_DbTable_Manager();
        $this->_dbJobDetail = new Employees_Model_DbTable_JobDetail();
        $this->_orgDF = $this->_ehrTables->orgDateformat();
		$this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
		$this->_isMobile = $this->_hrappMobile->checkDevice();
        $this->_dbAccessRights = new Default_Model_DbTable_AccessRights();
    }
    
	// to get employee name by employee id.
    public function employeeId($empId)
    {
		return $this->_db->fetchOne($this->_db->select()
								   ->from($this->_ehrTables->empPersonal,
										  array('Employee_Name'=>new Zend_Db_Expr("CONCAT(Emp_First_Name,' ',Emp_Last_Name)")))
								   ->where('Form_Status = 1')
								   ->where('Employee_Id = ?', $empId));

    }
    
	// to get currency symbol an employee
    public function currencySymbol($employeeId)
    {
        $qryLocation = $this->_db->select()->from($this->_ehrTables->empJob, 'Location_Id')->where('Employee_Id = ?', $employeeId);
        
		$qrySymb = $this->_db->select()->from($this->_ehrTables->location, 'Currency_Symbol')
        ->where('Location_Id = ?', $qryLocation);
        
		$rowSymb = $this->_db->fetchOne($qrySymb);
        
		return $rowSymb;
    }
    
	//version 0.2=>included conditions for Hr group, payroll group, bulk attendace update
    // to get all the employee details for selecting the employee name from popup
    public function employeeDetail($page, $rows, $sort, $order, $first, $last, $dept, $mail, $empId, $designation, $workSchedule, $adminAccess,
								   $isManager, $formName, $punchInDate, $isMobile, $isSettings = null,$complaintFromId=null,$allowanceEmp=null)
    {
        switch($sort)
        {
            case 'Department_Name':
                $sort = "D.$sort";
                break;
            case 'Emp_Email':
                $sort = "J.$sort";
                break;
            case 'Designation_Name':
                $sort = "Dn.$sort";
                break;
			case 'Employee_Name':
				$sort = "Employee_Name";
				break;
			case 'Title':
                $sort = "WS.$sort";
                break;
            default:
                $sort = "P.$sort";
            break;
        }
  
        $qryManager = $this->_db->select()
							->from(array('J'=>$this->_ehrTables->empJob),
								   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS J.Employee_Id as count'),
										 'J.Emp_Email','J.Employee_Id','J.Manager_Id', 'J.Employee_Id as value',
										 new Zend_Db_Expr("DATE_FORMAT(J.Date_Of_Join,'".$this->_orgDF['sql']."') as Date_Of_Join")))
							
							->joinInner(array('C'=>$this->_ehrTables->empContacts), 'C.Employee_Id=J.Employee_Id', array('C.Mobile_No'))
							
							->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id=J.Employee_Id',
										array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name)"),
											  'text'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name,'-',CASE WHEN J.User_Defined_EmpId IS NULL THEN P.Employee_Id ELSE J.User_Defined_EmpId END)")))
							
							->joinLeft(array('M'=>$this->_ehrTables->empPersonal), 'M.Employee_Id=J.Manager_Id',
									   array('Manager_Name'=>new Zend_Db_Expr("CONCAT(M.Emp_First_Name,' ',M.Emp_Last_Name)")))
							
							->joinInner(array('D'=>$this->_ehrTables->dept), 'D.Department_Id=J.Department_Id', array('D.Department_Name'))
							
							->joinInner(array('Dn'=>$this->_ehrTables->designation), 'Dn.Designation_Id = J. Designation_Id',
										array('Dn.Designation_Name', 'Dn.Grade_Id'))
							
							->joinInner(array('L'=>$this->_ehrTables->location), 'J.Location_Id = L.Location_Id', array('L.Currency_Symbol'))
							
							->joinLeft(array('S'=>$this->_ehrTables->salary),'S.Employee_Id = P.Employee_Id', array('Basic_Pay'))
							
							->where('P.Form_Status = 1')
							->order("$sort $order");
    
        if($formName == 'Adhoc Allowance' || $formName == 'Allowances' || $formName== 'NPS' || $formName== 'Provident Fund' || $formName=='Final Settlement')
		{
			$qryManager->where('J.Emp_Status IN (?)',array('Active','InActive'));
        }
        else 
        {
            $qryManager->where('J.Emp_Status Like ?', 'Active');
        }                    
        
		if($formName == 'Contact Details')
        {
            $qryManager->where('C.Mobile_No IS NOT NULL')
			           ->where('J.Emp_Email IS NOT NULL');
        }
		
		if($formName == 'Timesheets')
        {
            $qryManager->joinInner(array('TH'=>$this->_ehrTables->timesheetHrs), 'TH.Grade_Id = Dn.Grade_Id', array());
        }
		
        if($formName == 'Warnings' || $formName == 'Memos' || $formName == 'Skillset Assessment' ||  $formName == 'Performance Assessment' ||
		   $formName == 'Awards' || $formName == 'Complaints')
        {
            $qryManager->where('J.Employee_Id != ?', $empId);
        }
	
        if(!empty($complaintFromId) && $formName == 'Complaints')
        {
            $qryManager->where('J.Employee_Id != ?', $complaintFromId);
        }   
	
        if ($formName == 'HrGroup' || $formName == 'PayrollGroup')
        {
        	$fieldName = ($formName == 'HrGroup') ? 'Role_Hr_Group' : 'Role_Payroll_Group';

            $rolesAccessQry =  $this->_db->select()->from(array('R'=>$this->_ehrTables->roleAccessControl), 'EJ.Employee_Id')
        	->joinInner(array('EJ'=>$this->_ehrTables->empJob), 'EJ.Roles_Id=R.Roles_Id', array())
        	->where('EJ.Emp_Status Like ?', 'Active')        	
        	->where('R.'.$fieldName.' = ?',1);        	
        	$rolesData = $this->_db->fetchCol($rolesAccessQry);
			
        	$rolesEmpQry = $this->_db->select()->from($this->_ehrTables->empAccessRights, array('Employee_Id'))
        	->group('Employee_Id')
        	->where(''.$fieldName.' =?',1);
        	$rolesEmp = $this->_db->fetchCol($rolesEmpQry);
        	
        	$qryRoles = $this->_db->select()->from(array('R'=>$this->_ehrTables->roles), 'EJ.Employee_Id')
        	->joinInner(array('EJ'=>$this->_ehrTables->empJob), 'EJ.Designation_Id=R.Designation_Id', array())
        	->where('EJ.Emp_Status Like ?', 'Active')        	
        	->where('R.'.$fieldName.' = ?',1)        	
        	->where('EJ.Employee_Id NOT IN(?)',$this->_db->select()->from($this->_ehrTables->empAccessRights, array('Employee_Id')));
        	$rolesDesignation = $this->_db->fetchCol($qryRoles);
        	
        	$rolesEmp = array_unique(array_merge($rolesData,$rolesEmp,$rolesDesignation));
        	
        	$qryManager->where('J.Emp_Email != \'NULL\' AND J.Emp_Email != ""');
			
			if(!empty($rolesEmp))
				$qryManager->where('J.Employee_Id IN (?)', $rolesEmp);        	
        }
        if($formName == 'Bulk Attendance Update')
        {
        	$qryManager->joinLeft(array('WS'=>$this->_ehrTables->workSchedule), 'WS.WorkSchedule_Id = J.Work_Schedule', array('WS.Title'));
			
        	//getting employees who dont have punchindate equal to $punchInDate
        	$qryManager->where("J.Employee_Id NOT IN(?)",$this->_db->select()->from($this->_ehrTables->attendance, 'Employee_Id')
        	->where('PunchIn_Date = ?',$punchInDate)
        	->where('Approval_Status IN(?)',array('Applied','Returned','Approved','Draft')));
        	
        	$orWhereQry = $this->_db->quoteInto('Start_Date <= ? AND ',$punchInDate)
        	.$this->_db->quoteInto('End_Date >= ?', $punchInDate);
        	
        	//getting employees who dont have applied leave on $punchIndate
        	$qryManager->where("J.Employee_Id NOT IN(?)",$this->_db->select()->from($this->_ehrTables->empLeaves, 'Employee_Id')
        			->where($orWhereQry)
        			->where('Approval_Status IN(?)',array('Applied','Returned','Approved')));
        	
        	//not the login employee                  
        	$qryManager->where('J.Employee_Id != ?', $empId);
        	//employee date of join should be lesser than $punchIndate
        	$qryManager->where('J.Date_Of_Join <= ?', $punchInDate)
					->group('J.Employee_Id');
        }
        
        $qryManager->joinLeft(array('T'=>$this->_ehrTables->timesheetHrs), 'T.Grade_Id = Dn.Grade_Id', array('Hrs'=>'T.Regular_Hours'));
        
        if(empty($adminAccess) && $isManager == 1 && $formName != 'Project' && $formName != 'Employee Roles')
        {
            $qryManager->where('J.Manager_Id=? or J.Employee_Id = ?', $empId);
            
        }
        
        if($formName == 'Deductions' || $formName == 'Reimbursement' || $formName == 'Bonus' || $formName == 'Loan' || $formName == 'Shift Allowance' || $formName == 'Adhoc Allowance')
        {
            $getSalaryWageEmpId = $this->_db->fetchCol($this->_db->select()
                            ->union(array($this->_db->select()->from($this->_ehrTables->salary, array('Employee_Id')),
                            $this->_db->select()->from($this->_ehrTables->hourlyWages, array('Employee_Id')))));
           $qryManager->where('P.Employee_Id IN (?)', !empty($getSalaryWageEmpId)?$getSalaryWageEmpId:array(0));
        }
        
        if (in_array($formName, array('Assignments', 'Deductions', 'Bonus', 'Reimbursement', 'Loan', 'Shift Allowance')))
        {
            $managerId = $this->ckAddedById($empId);
			
            if(empty($adminAccess) && $isManager == 1)
            {
                $qryManager->where('J.Manager_Id=? or J.Employee_Id = ?', $empId);
            }
            //elseif(!empty($managerId))
            //{
            //    $qryManager->where('J.Manager_Id = ?', $managerId);
            //}	
        }
		
        if (in_array($formName, array('Fixed Insurance', 'Variable Insurance', 'Provident Fund', 'Allowances', 'Loan', 'Deductions', 'Bonus', 'NPS')))
        {
            $qryManager->joinInner(array('ET'=>$this->_ehrTables->empType), 'ET.EmpType_Id=J.EmpType_Id', array())
            ->where('ET.Benefits_Applicable = 1');
        }
        
        if($formName == 'Fixed Insurance' || $formName == 'Variable Insurance')
        {
            $qryManager->joinInner(array('IG'=>$this->_ehrTables->insuranceGrade),'IG.Grade_Id=Dn.Grade_Id',array())
            ->joinInner(array('I'=>$this->_ehrTables->insurancetypeGrade),'I.Insurance_Grade_Id=IG.Insurance_Grade_Id',array())
            ->group('J.Employee_Id');
            
			if($formName == 'Fixed Insurance')
            {
                $qryManager->where('I.Org_ShareAmount IS NOT NULL');
            }
            else
            {
                $qryManager->where('I.Org_ShareAmount IS NULL');
            }
        }
        
        if($formName == 'Deferred Loan')
        {
            $qryManager->joinInner(array('EL'=>$this->_ehrTables->empLoan), 'EL.Employee_Id=J.Employee_Id', array())
            ->joinInner(array('ET'=>$this->_ehrTables->empType), 'ET.EmpType_Id=J.EmpType_Id', array())
            ->where('ET.Benefits_Applicable = 1')->where('EL.Approval_Status = "Paid"')->group('EL.Employee_Id');
        }	
       
        $employeeAccess = $this->_dbAccessRights->employeeAccessRights($empId, $formName);
        $isManager = $employeeAccess['Employee']['Is_Manager'];
        
        if (empty($employeeAccess['Admin']) && $isManager == 1 )
        {
            $qryManager->where('J.Manager_Id = ? or J.Employee_Id = ?', (int)$empId);
        }
        else if(empty($employeeAccess['Admin']) && $isManager == 0)
        {
            $qryManager->where('J.Employee_Id = ?', (int)$empId);
        }
        
        
        if($formName == 'Allowances' && !empty($allowanceEmp))
        {
            $qryManager->where('J.Employee_Id NOT IN (?)', $allowanceEmp);
        }
        
		if($formName == 'Employee Roles')
        {
            $rolesEmpQry = $this->_db->select()->from($this->_ehrTables->empAccessRights, array('Employee_Id'))->group('Employee_Id');
            $rolesEmp = $this->_db->fetchCol($rolesEmpQry);
            $qryManager->where('J.Employee_Id IN (?)', $rolesEmp);
        }
        
        if( ! empty($first) && preg_match('/^[a-zA-Z]/', $first) ) {
            $qryManager->where('P.Emp_First_Name LIKE ?',"$first%");
        }
        if( ! empty($last) && preg_match('/^[a-zA-Z]/', $last) ) {
            $qryManager->where('P.Emp_Last_Name LIKE ?',"$last%");
        }
        if ( ! empty($designation) && preg_match('/^[a-zA-Z]/', $designation) ) {
            $qryManager->where('Dn.Designation_Name LIKE ?', "$designation%");
        }
        if( ! empty($dept) && preg_match('/^[a-zA-Z]/', $dept) ) {
            $qryManager->where('D.Department_Name LIKE ?',"$dept%");
        }
        if( ! empty($mail) ) {
            $qryManager->where('J.Emp_Email LIKE ?',"$mail%");
        }
		if( ! empty($workSchedule) && preg_match('/^[a-zA-Z]/', $workSchedule) && ($formName == 'Bulk Attendance Update')) {
            $qryManager->where('WS.Title LIKE ?',"$workSchedule%");
        }
     
        $dbCommonFun = new Application_Model_DbTable_CommonFunction(); 
        if(in_array($formName, array('Adhoc Allowance','Allowances','Payroll Reports')))
		{
			$qryManager = $dbCommonFun->formServiceProviderQuery($qryManager,'J.Service_Provider_Id',$empId);
		}
        $employeeList = $this->_db->fetchAll($qryManager);
      	
        if(in_array($formName, array('Adhoc Allowance','Allowances','NPS','Provident Fund','Final Settlement')))
        {
     	  $employeeList = $dbCommonFun->listInactiveEmployeeTillFinalSettlement($employeeList);
		}
        if (!empty($isSettings))
		{
			return $employeeList;
		}
		else
		{
         	$rowManager = $employeeList;
			$countManager = $this->_db->fetchOne('select FOUND_ROWS()');
			$arrManager = array("total"=>$countManager, "rows"=>$rowManager);
			return Zend_Json::encode($arrManager);
		}
    }
    
    
   
    
	// 	to get employee details for whom the salary is not assigned so that one can select the employee name from popup
	public function addSalaryEmployee($page, $rows, $sort, $order, $first, $last, $dept, $mail, $empId, $designation, $adminAccess, $isManager, $formName, $isFilterEmployee = null)
    {
        switch($sort)
        {
            case 'Department_Name':
                $sort = "D.$sort";
                break;
            
			case 'Emp_Email':
                $sort = "J.$sort";
                break;
            
			case 'Designation_Name':
                $sort = "Dn.$sort";
                break;
            
			default:
                $sort = "P.$sort";
				break;
        }
        
        $qryEmployee = $this->_db->select()
							->from(array('J'=>$this->_ehrTables->empJob),
								   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS J.Employee_Id as count'), 'J.Emp_Email',
										 'J.Employee_Id', 'J.Date_Of_Join as Mob_Date_Of_Join',
										 new Zend_Db_Expr("DATE_FORMAT(J.Date_Of_Join,'".$this->_orgDF['sql']."') as Date_Of_Join")))
							
							->joinLeft(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id=J.Employee_Id',
									   array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name)")))
							
							->joinInner(array('D'=>$this->_ehrTables->dept), 'D.Department_Id=J.Department_Id',
										array('D.Department_Name'))
							
							->joinInner(array('Dn'=>$this->_ehrTables->designation), 'Dn.Designation_Id = J. Designation_Id',
										array('Dn.Designation_Name'))
							
							->joinInner(array('L'=>$this->_ehrTables->location), 'J.Location_Id = L.Location_Id', 'L.Currency_Symbol')
							
							->where('P.Form_Status = 1')
							->where('J.Emp_Status Like ?', 'Active')
							->order("$sort $order");
		
		if ($isFilterEmployee != 'Yes')
		{
			$qryEmployee->limit($rows, ($page - 1) * $rows);
		}
		
		if ($formName != 'Bonus' || formName != 'Commission')
        {
            $qryEmployee->where('J.Employee_Id NOT IN (?)', $this->_db->select()->from($this->_ehrTables->salary, 'Employee_Id'))
            ->where('J.Employee_Id NOT IN (?)', $this->_db->select()->from($this->_ehrTables->hourlyWages, 'Employee_Id'));
        }
        
		if(empty($adminAccess) && $isManager == 1)
        {
            $qryEmployee->where('J.Manager_Id=? or J.Employee_Id = ?', $empId);
        }
        if( ! empty($first) && preg_match('/^[a-zA-Z]/', $first) ) {
            $qryEmployee->where('P.Emp_First_Name LIKE ?',"$first%");
        }
        if( ! empty($last) && preg_match('/^[a-zA-Z]/', $last) ) {
            $qryEmployee->where('P.Emp_Last_Name LIKE ?',"$last%");
        }
        if ( ! empty($designation) && preg_match('/^[a-zA-Z]/', $designation) ) {
            $qryEmployee->where('Dn.Designation_Name LIKE ?', "$designation%");
        }
        if( ! empty($dept) && preg_match('/^[a-zA-Z]/', $dept) ) {
            $qryEmployee->where('D.Department_Name LIKE ?',"$dept%");
        }
        if( ! empty($mail) ) {
            $qryEmployee->where('J.Emp_Email LIKE ?',"$mail%");
        }
        $rowEmployee = $this->_db->fetchAll($qryEmployee);
        $countEmployee = $this->_db->fetchOne('select FOUND_ROWS()');
        $arrEmployee = array("total"=>$countEmployee, "rows"=>$rowEmployee);

        return Zend_Json::encode($arrEmployee);
    }
    
	//to get only the monthly salaried employee details so that one can select the employee name from popup
    public function salaryEmployee($page, $rows, $sort, $order, $first, $last, $dept, $mail, $empId, $designation, $adminAccess, $isManager, $formName, $isMobile, $isSettings = null)
    {
        switch($sort)
        {
            case 'Department_Name':
                $sort = "D.$sort";
                break;
            case 'Emp_Email':
                $sort = "J.$sort";
                break;
            case 'Designation_Name':
                $sort = "Dn.$sort";
                break;
            default:
                $sort = "P.$sort";
            break;
        }
        
        $assYr = '0';
        if($formName == 'Tax Declarations')// for mobile tax declaration add form 
        {
        	$dbFinanYr = new Default_Model_DbTable_FinancialYear();
        	$assYr = $dbFinanYr->getAssessmentYr();
		}
        $qryManager = $this->_db->select()
        ->from(array('S'=>$this->_ehrTables->salary), array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS S.Employee_Id as count'), 'Basic_Pay', 'Employee_Id'))
        ->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id=S.Employee_Id', 
        		array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name)"),
        			  'Assessment_Year'=>new Zend_Db_Expr($assYr)))
        ->joinInner(array('J'=>$this->_ehrTables->empJob), 'S.Employee_Id=J.Employee_Id', array('J.Emp_Email', 'J.Manager_Id'))
        ->joinLeft(array('M'=>$this->_ehrTables->empPersonal), 'M.Employee_Id=J.Manager_Id', array('Manager_Name'=>new Zend_Db_Expr("CONCAT(M.Emp_First_Name,' ',M.Emp_Last_Name)")))
        ->joinInner(array('D'=>$this->_ehrTables->dept), 'D.Department_Id=J.Department_Id', array('D.Department_Name'))
        ->joinInner(array('Dn'=>$this->_ehrTables->designation), 'Dn.Designation_Id = J. Designation_Id', array('Dn.Designation_Name'))
        ->joinInner(array('L'=>$this->_ehrTables->location), 'J.Location_Id = L.Location_Id', 'L.Currency_Symbol')
        ->where('P.Form_Status = 1')->where('J.Emp_Status Like ?', 'Active')->order("$sort $order");
		
        if(!$isMobile)
        	$qryManager->limit($rows, ($page - 1) * $rows);
        	
        if($formName == 'Advance Salary')
        {
            $qryManager->joinInner(array('ET'=>$this->_ehrTables->empType), 'ET.EmpType_Id=J.EmpType_Id', array())
            ->where('ET.Benefits_Applicable = 1');
        }
       
        if($formName == 'Tax Declarations')
        {
            $managerId = $this->ckAddedById($empId);
            if(empty($adminAccess) && $isManager == 1)
            {
                $qryManager->where('J.Manager_Id=? or J.Employee_Id = ?', $empId);
            }
            elseif(!empty($managerId))
            {
                $qryManager->where('J.Manager_Id = ?', $managerId);
            }
        }
        if(empty($adminAccess) && $isManager == 1 && $formName != 'Tax Declarations')
        {
            	
            if($formName == 'Commission')
            {
                $countManager = $this->_dbManager->countManager($empId, $isManager);
                if($formName == 'Bonus')
                {
                    $qryManager->where('D.BonusType_Id IS NOT NULL and D.BonusType_Id != 0');
                }
                if(empty($countManager))
                $qryManager->where('J.Manager_Id = ? or J.Employee_Id = ?', (int)$empId);
                else
                $qryManager->where('J.Manager_Id = ?', (int)$empId);
            }
            else
            $qryManager->where('J.Manager_Id = ?', (int)$empId);
        }
        if($formName == 'Commission')
        {
            //$qryManager->where('J.Commission_Percent != 0')->where('J.Commission_Employee = 1');
        	$qryManager->where('J.Commission_Employee = 1');
        }
        	
        if( ! empty($first) && preg_match('/^[a-zA-Z]/', $first) ) {
            $qryManager->where('P.Emp_First_Name LIKE ?',"$first%");
        }
        if( ! empty($last) && preg_match('/^[a-zA-Z]/', $last) ) {
            $qryManager->where('P.Emp_Last_Name LIKE ?',"$last%");
        }
        if ( ! empty($designation) && preg_match('/^[a-zA-Z]/', $designation) ) {
            $qryManager->where('Dn.Designation_Name LIKE ?', "$designation%");
        }
        if( ! empty($dept) && preg_match('/^[a-zA-Z]/', $dept) ) {
            $qryManager->where('D.Department_Name LIKE ?',"$dept%");
        }
        if( ! empty($mail) ) {
            $qryManager->where('J.Emp_Email LIKE ?',"$mail%");
        }

		if (!empty($isSettings))
		{
			return $this->_db->fetchAll($qryManager);
		}
		else
		{
			$rowManager = $this->_db->fetchAll($qryManager);
			$countManager = $this->_db->fetchOne('select FOUND_ROWS()');
			$arrManager = array("total"=>$countManager, "rows"=>$rowManager);
	
			return Zend_Json::encode($arrManager);
		}
    }
    
	// to get employee id by their first name and last name
    public function empWorkId($firstName, $lastName)
    {
        return $this->_db->fetchCol($this->_db->select()
									->from(array('P'=>$this->_ehrTables->empPersonal), array('P.Employee_Id'))
									
									->joinInner(array('J'=>$this->_ehrTables->empJob), 'P.Employee_Id=J.Employee_Id', array())
									
									->where('P.Emp_First_Name LIKE ?', $firstName)
									->where('P.Emp_Last_Name LIKE ?', "$lastName%")
									->where('P.Form_Status = 1')
									->where('J.Emp_Status Like ?', 'Active'));
    }
    
	// to get employee id by their first name and last name
    public function empSalaryId($firstName, $lastName)
    {
        $qryEmpId = $this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal), array('P.Employee_Id'))
        ->joinInner(array('S'=>$this->_ehrTables->salary), 'P.Employee_Id=S.Employee_Id', array())
        ->joinInner(array('J'=>$this->_ehrTables->empJob), 'P.Employee_Id=J.Employee_Id', array())
        ->where('J.Emp_Status Like ?', 'Active')
        ->where('P.Emp_First_Name LIKE ?', $firstName)->where('P.Emp_Last_Name LIKE ?', "$lastName%")
        ->where('P.Form_Status = 1');
        $rowEmpId = $this->_db->fetchCol($qryEmpId);
        return $rowEmpId;
    }
    
	// to check if the emp is eligible for benefits
	public function checkValidEmp($firstName, $lastName) 
    {
        $qryEmpId = $this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal), array('P.Employee_Id'))
     //   ->joinInner(array('S'=>$this->_ehrTables->salary), 'P.Employee_Id=S.Employee_Id', array())
        ->joinInner(array('J'=>$this->_ehrTables->empJob), 'P.Employee_Id=J.Employee_Id', array())
        ->joinInner(array('ET'=>$this->_ehrTables->empType), 'ET.EmpType_Id=J.EmpType_Id', array())
        ->where('ET.Benefits_Applicable = 1')
        ->where('J.Emp_Status Like ?', 'Active')
        ->where('P.Emp_First_Name LIKE ?', $firstName)->where('P.Emp_Last_Name LIKE ?', "$lastName%")
        ->where('P.Form_Status = 1');
        $rowEmpId = $this->_db->fetchCol($qryEmpId);
        return $rowEmpId;
    }
    
	// to check if the emp is eligible for pf and insurance benefits
	public function checkPfInsuranceEmp($employeeId) 
    {
        $qryEmpId = $this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal), array('ET.Benefits_Applicable'))
        ->joinInner(array('J'=>$this->_ehrTables->empJob), 'P.Employee_Id=J.Employee_Id', array())
        ->joinInner(array('ET'=>$this->_ehrTables->empType), 'ET.EmpType_Id=J.EmpType_Id', array())
        ->where('J.Emp_Status Like ?', 'Active')
        ->where('P.Employee_Id = ?', $employeeId)
        ->where('P.Form_Status = 1');
        $rowEmpId = $this->_db->fetchone($qryEmpId);
        return $rowEmpId;
    }
    
	// to get employee id by their first name and last name
    public function empManagerId($firstName, $lastName)
    {
        $qryEmpId = $this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal), array('P.Employee_Id'))
        ->joinInner(array('J'=>$this->_ehrTables->empJob), 'P.Employee_Id=J.Employee_Id', array())
        ->where('J.Emp_Status Like ?', 'Active')
        ->where('P.Emp_First_Name LIKE ?', $firstName)->where('P.Emp_Last_Name LIKE ?', "$lastName%")
        ->where('P.Is_Manager = 1')->where('P.Form_Status = 1');
        $rowEmpId = $this->_db->fetchCol($qryEmpId);
        return $rowEmpId;
    }

	/**
	 * Check whether logged in employee have payroll access rights or not
	 */
    public function checkPayrollEmployee($empId,$formId)
    {
        $formName = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->forms, array('Form_Name'))
                                    ->where('Form_Id = ?', $formId));

        $employeeAccessRights = $this->_dbAccessRights->employeeAccessRights ($empId, $formName);
        
        $formAccess = $employeeAccessRights['Employee']['Optional_Choice'];
        if($formAccess > 0)
        {
            return $empId;
        }
        else
        {
            return 0;
        }
    }
    
	// to get employee first and last name by employee id
    public function empFirstName($empId)
    {
        $qryEmpName = $this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal), array('Emp_First_Name', 'Emp_Last_Name'))
        ->joinInner(array('J'=>$this->_ehrTables->empJob), 'P.Employee_Id=J.Employee_Id', array())
        ->where('J.Emp_Status Like ?', 'Active')
        ->where('P.Form_Status = 1')->where('P.Employee_Id = ?', $empId);
        $rowEmpName = $this->_db->fetchRow($qryEmpName);
        return $rowEmpName;
    }
    
	//to get employee first and last name by employee id
    public function employeeName($empId,$employeeStatus=NULL)
    {
        $this->_db->setFetchMode(Zend_Db::FETCH_ASSOC);
        $qryEmployeeName = $this->_db->select()
						->from(array('P'=>$this->_ehrTables->empPersonal),
							   array('Employee_Name'=>new Zend_Db_Expr("CONCAT(Emp_First_Name,' ',Emp_Last_Name)"),
									 'P.Emp_First_Name', 'P.Emp_Last_Name', 'P.Gender'))
						
						->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.Employee_Id=P.Employee_Id',
									array('J.Location_Id', 'J.Emp_Email',
										  'Join_Month'=>new Zend_Db_Expr("DATE_FORMAT(J.Date_Of_Join, '%M, %Y')")))
						
						->joinInner(array('U'=>$this->_ehrTables->empLogin),'U.Employee_Id=P.Employee_Id', array('U.User_Name'))
						
						->joinLeft(array('C'=>$this->_ehrTables->empContacts), 'C.Employee_Id=P.Employee_Id', array('C.Mobile_No'))
						
						->joinInner(array('D'=>$this->_ehrTables->designation),'D.Designation_Id=J.Designation_Id',
									array('D.Grade_Id', 'D.Designation_Name'))
						
						->joinLeft(array('H'=>$this->_ehrTables->timesheetHrs), 'H.Grade_Id=D.Grade_Id',
									array('H.Regular_Hours', 'H.OverTime_Hours'))
						
						->joinLeft(array('L'=>$this->_ehrTables->location), 'L.Location_Id=J.Location_Id', array('L.Location_Name'))
						->where('P.Form_Status = 1')
						->where('P.Employee_Id = ?', $empId);

        
        if(empty($employeeStatus))
        {
            $qryEmployeeName->where('J.Emp_Status Like ?', 'Active');
        }
        
        $employeeName = $this->_db->fetchRow($qryEmployeeName);

        return $employeeName;
    }
    
	//to get salary employee first and last name by employee id
    public function salaryEmpName($empId)
    {
        $this->_db->setFetchMode(Zend_Db::FETCH_ASSOC);
        $qryEmpName = $this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal), array('Employee_Name'=>new Zend_Db_Expr("CONCAT(Emp_First_Name,' ',Emp_Last_Name)")))
        ->joinInner(array('J'=>$this->_ehrTables->empJob), 'P.Employee_Id=J.Employee_Id', array())
        ->joinInner(array('S'=>$this->_ehrTables->salary), 'P.Employee_Id=S.Employee_Id', array())
        ->where('J.Emp_Status Like ?', 'Active')
        ->where('P.Form_Status = 1')->where('P.Employee_Id = ?', $empId);
        $rowEmpName = $this->_db->fetchOne($qryEmpName);
        return $rowEmpName;
    }
    
	//to get salary employee first and last name by employee id
    public function benefitsEmpName($empId)
    {
        $this->_db->setFetchMode(Zend_Db::FETCH_ASSOC);
        $qryEmpName = $this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal), array('Employee_Name'=>new Zend_Db_Expr("CONCAT(Emp_First_Name,' ',Emp_Last_Name)")))
        ->joinInner(array('J'=>$this->_ehrTables->empJob), 'P.Employee_Id=J.Employee_Id', array())
      //  ->joinInner(array('S'=>$this->_ehrTables->salary), 'P.Employee_Id=S.Employee_Id', array())
        ->joinInner(array('ET'=>$this->_ehrTables->empType), 'ET.EmpType_Id=J.EmpType_Id', array())
        ->where('ET.Benefits_Applicable = 1')
        ->where('J.Emp_Status Like ?', 'Active')
        ->where('P.Form_Status = 1')->where('P.Employee_Id = ?', $empId);
        $rowEmpName = $this->_db->fetchOne($qryEmpName);
        return $rowEmpName;
    }
    
	/**
	 * Check whether given employeeId have benifts applicable or not
	 */
	public function pfInsuranceEmpName($empId)
    {
        $this->_db->setFetchMode(Zend_Db::FETCH_ASSOC);
        $qryEmpName = $this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal), array('Employee_Name'=>new Zend_Db_Expr("CONCAT(Emp_First_Name,' ',Emp_Last_Name)")))
        ->joinInner(array('J'=>$this->_ehrTables->empJob), 'P.Employee_Id=J.Employee_Id', array())
        ->joinInner(array('ET'=>$this->_ehrTables->empType), 'ET.EmpType_Id=J.EmpType_Id', array())
        ->where('ET.Benefits_Applicable = 1')
        ->where('J.Emp_Status Like ?', 'Active')
        ->where('P.Form_Status = 1')->where('P.Employee_Id = ?', $empId);
        $rowEmpName = $this->_db->fetchOne($qryEmpName);
        return $rowEmpName;
    }
	
	/**
	 * Check whether given employeeId have benifts applicable or not
	 */
    public function isBenefitsApplicable($empId,$salaryStartDate=NULL)
    {		
        $qryEmpEligible = $this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal), array(new Zend_Db_Expr('COUNT(P.Employee_Id)')))
        ->joinInner(array('J'=>$this->_ehrTables->empJob), 'P.Employee_Id=J.Employee_Id', array())
        ->joinInner(array('ET'=>$this->_ehrTables->empType), 'ET.EmpType_Id=J.EmpType_Id', array())
        ->where('ET.Benefits_Applicable = 1')        
        ->where('P.Form_Status = 1')->where('P.Employee_Id = ?', $empId);
		
		/** get employee active status based on salary start date **/
		$dbCommonFun = new Application_Model_DbTable_CommonFunction();
		$activeWhere = $dbCommonFun->getActiveEmployees($salaryStartDate);		
		
		$qryEmpEligible->where($activeWhere);				
		
        $rowEmpEligible = $this->_db->fetchOne($qryEmpEligible);
        return $rowEmpEligible;
    }
	
	/**
	 * Check whether given employeeId have benifts applicable or not
	 */
    public function isBenefitsApplicableDraftStatus($empId)
    {
        $qryEmpEligible = $this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal), array(new Zend_Db_Expr('COUNT(P.Employee_Id)')))
        ->joinInner(array('J'=>$this->_ehrTables->empJob), 'P.Employee_Id=J.Employee_Id', array())
        ->joinInner(array('ET'=>$this->_ehrTables->empType), 'ET.EmpType_Id=J.EmpType_Id', array())
        ->where('ET.Benefits_Applicable = 1')
        /* ->where('J.Emp_Status Like ?', 'Active') */
        ->where('P.Employee_Id = ?', $empId);
        $rowEmpEligible = $this->_db->fetchOne($qryEmpEligible);
        return $rowEmpEligible;
    }

    // to get the employee name and their email id
    public function emailAddress($fromEmpId,$toEmpId)
    {
        $qryfromemail=$this->_db->select()
                                ->from(array('p1'=>$this->_ehrTables->empPersonal),
                                       array('Employee_Name'=>new Zend_Db_Expr("CONCAT(p1.Emp_First_Name,' ',p1.Emp_Last_Name)")))
                                ->joinInner(array('j1'=>$this->_ehrTables->empJob), 'p1.Employee_Id=j1.Employee_Id', array('j1.Emp_Email'))
                                ->where('p1.Form_Status = 1')->where('j1.Emp_Status Like ?', 'Active')->where('p1.Employee_Id = ?',$fromEmpId)
                                ->where('j1.Emp_Email!="" or j1.Emp_Email IS NOT NULL');
        
        $qrytoemail=$this->_db->select()
                              ->from(array('p1'=>$this->_ehrTables->empPersonal),
                                     array('Employee_Name'=>new Zend_Db_Expr("CONCAT(p1.Emp_First_Name,' ',p1.Emp_Last_Name)")))
                              ->joinInner(array('j1'=>$this->_ehrTables->empJob), 'p1.Employee_Id=j1.Employee_Id',
                                          array('j1.Emp_Email'))
                              ->where('p1.Form_Status = 1')->where('j1.Emp_Status Like ?', 'Active')
                              ->where('p1.Employee_Id = ?',$toEmpId)
                              ->where('j1.Emp_Email!="" or j1.Emp_Email IS NOT NULL');
                              
        $rowemail = $this->_db->fetchAll($this->_db->select()->union(array($qryfromemail, $qrytoemail)));		
        return $rowemail;
    }
    
	// to get all the payroll employees
    public function payrollEmpDetail($page, $rows, $sort, $order, $first, $last, $dept, $mail, $designation, $formId, $isMobile,$isSettings = null)
    {

        $optionalChoiceEnabledEmployeeId = $this->_db->fetchCol($this->_db->select()->distinct()->from(array('R'=>$this->_ehrTables->roleAccessControl), array('EJ.Employee_Id'))
                                                ->joinInner(array('EJ'=>$this->_ehrTables->empJob), 'R.Roles_Id=EJ.Roles_Id', array(''))
                                                ->where('R.Role_Optional_Choice = 1')
                                                ->where('R.Form_Id = ?', $formId));
        switch($sort)
        {
            case 'Department_Name':
                $sort = "D.$sort";
                break;
            case 'Emp_Email':
                $sort = "J.$sort";
                break;
            case 'Designation_Name':
                $sort = "Dn.$sort";
                break;
            default:
                $sort = "P.$sort";
            break;
        }

        if(empty($optionalChoiceEnabledEmployeeId))
        {
            $optionalChoiceEnabledEmployeeId='';
        }
        
        $qryPayroll = $this->_db->select()
        ->from(array('J'=>$this->_ehrTables->empJob), array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS J.Employee_Id as count'), 'J.Emp_Email', 'J.Employee_Id'))
        ->joinLeft(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id=J.Employee_Id', array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name)")))
        ->joinInner(array('D'=>$this->_ehrTables->dept), 'D.Department_Id=J.Department_Id', array('D.Department_Name'))
        ->joinInner(array('Dn'=>$this->_ehrTables->designation), 'Dn.Designation_Id = J. Designation_Id', array('Dn.Designation_Name'))
        ->where('P.Form_Status = 1')->where('J.Emp_Status Like ?', 'Active')->where('J.Employee_Id IN (?)', $optionalChoiceEnabledEmployeeId)
        ->order("$sort $order");
        
        //avoid non-numeric value warnings
        $rows= intval($rows);
        $page= intval($page);
        
        if(!$isMobile)
        	$qryPayroll->limit($rows, ($page - 1) * $rows);
        
        if( ! empty($first) && preg_match('/^[a-zA-Z]/', $first) ) {
            $qryPayroll->where('P.Emp_First_Name LIKE ?',"$first%");
        }
        if( ! empty($last) && preg_match('/^[a-zA-Z]/', $last) ) {
            $qryPayroll->where('P.Emp_Last_Name LIKE ?',"$last%");
        }
        if ( ! empty($designation) && preg_match('/^[a-zA-Z]/', $designation) ) {
            $qryPayroll->where('Dn.Designation_Name LIKE ?', "$designation%");
        }
        if( ! empty($dept) && preg_match('/^[a-zA-Z]/', $dept) ) {
            $qryPayroll->where('D.Department_Name LIKE ?',"$dept%");
        }
        if( ! empty($mail) ) {
            $qryPayroll->where('J.Emp_Email LIKE ?',"$mail%");
        }
		
		if (!empty($isSettings))
		{
			return $this->_db->fetchAll($qryPayroll);
		}
		else
		{
			$rowPayroll = $this->_db->fetchAll($qryPayroll);
			$countPayroll = $this->_db->fetchOne('select FOUND_ROWS()');
			$arrPayroll = array("total"=>$countPayroll, "rows"=>$rowPayroll);
				
			return Zend_Json::encode($arrPayroll);
		}
    }
	
	/** To get Approver Name for the Bonus Employee **/
    public function bonusApproverEmpDetail($formId)
    {
        $dbCommonFunction   = new Application_Model_DbTable_CommonFunction();
        $inputs = "{\"formId\":\"$formId\"}";
        $response = $dbCommonFunction->executeCommonLibraryFunction(null, 'func', 'bonusApproverEmpDetail', $inputs);
        return $response;
    }
	
	/**
	 * Get managerId by employeeId
	 */
    public function ckAddedById($empId)
    {
        $getAddedBy = $this->_db->fetchOne($this->_db->select()
										   ->from(array('J'=>$this->_ehrTables->empJob), 'Manager_Id')
										   ->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'J.Manager_Id=P.Employee_Id', array())
										   ->where('J.Employee_Id = ?', $empId)
										   ->where('P.Form_Status = 1'));
        return $getAddedBy;
    }
    
    /**
	 * Get mailId by employeeId
	 */
    public function getEmpmail($employeeId)
    {
        $sql=$this->_db->select()
        ->from(array('emp'=>$this->_ehrTables->empJob),array('emp.Emp_Email'))
        ->where('emp.Employee_Id = ?', $employeeId);
        $name=$this->_db->fetchOne($sql);
        return $name;
        	
    }
    // get mobile number of each employee
    public function getMobileNo($employeeId){
        $sql=$this->_db->select()
        ->from(array('emp'=>$this->_ehrTables->empContacts),array('emp.Mobile_No'))
        ->where('emp.Employee_Id = ?', $employeeId);
        $mobileNo=$this->_db->fetchOne($sql);
        return $mobileNo; 
    }
    /**
	 * Get dateOfJoin by employeeId
	 */
    public function getDateOfJoin($employeeId)
    {
        $sql=$this->_db->select()
        ->from(array('emp'=>$this->_ehrTables->empJob),array(new Zend_Db_Expr("DATE_FORMAT(emp.Date_Of_Join,'".$this->_orgDF['sql']."') as Date_Of_Join")))
        ->where('emp.Employee_Id = ?', $employeeId);
        $result=$this->_db->fetchOne($sql);
        return $result;
         
    }
	
	/**
	 * Get nationality details
	 */
    public function getNationality()
    {
        $nationalityQry = $this->_db->select()->from($this->_ehrTables->empPersonal,array('Nationality'))->group('Nationality');
        $result = $this->_db->fetchCol($nationalityQry);
        return $result;
    }
	
	/**
	 * Get educational details
	 */
    public function getEducationalDetails()
    {

        $getEduDetails = $this->_db->select()->from($this->_ehrTables->empEducation,array('Education_Type'))->group('Education_Type');
        $result = $this->_db->fetchCol($getEduDetails);
        return $result;
    }
	
	/**
	 * Get Language details
	 */
    public function getLanguage()
    {
        $getLang=$this->_db->select()->from($this->_ehrTables->empLang,array('Lang_Known'))->group('Lang_Known');
        $result=$this->_db->fetchCol($getLang);
        return $result;
    }
    
	/**
     *
     * This function is to find employee passport details for an employee ...
     *
     */
    public function getEmpPassport($employeeId)
    {
        $employeeId = (int) $employeeId;
        $getEmpPassport = $this->_db->select()->from($this->_ehrTables->empPassport,array('*'))->where('Employee_Id = ?', $employeeId);
        $passportResult = $this->_db->fetchRow($getEmpPassport);
        return $passportResult;
    }
    
	/**
     *
     * This function is to find employee
     * contact details
     */
    public function getEmpContacts($empId)
    {
	$employeeIdCount = $this->_db->fetchOne($this->_db->select()
			    ->from(array('contact'=>$this->_ehrTables->empContacts),new Zend_Db_Expr('count(Employee_Id)'))
	      ->where('contact.Use_Location_Address = ?', "1")
	      ->where('contact.Employee_Id = ?', $empId));
	
	if($employeeIdCount>0)
	{
	  
	  $empContactQry = $this->_db->select()
			    ->from(array('EC'=>$this->_ehrTables->empContacts),array('EC.Employee_Id','EC.pApartment_Name','EC.pStreet_Name','EC.pCity','EC.pState','EC.pCountry',
				    'EC.pPincode','EC.cApartment_Name','EC.cStreet_Name','EC.cCity','EC.cState','EC.cCountry','EC.cPincode','EC.Use_Location_Address',
				    'EC.Land_Line_No','EC.Mobile_No','EC.Fax_No','EC.Work_No'))
			    
			     ->joinLeft(array('EJ'=>$this->_ehrTables->empJob),'EC.Employee_Id=EJ.Employee_Id',array(''))
			     ->joinLeft(array('L'=>$this->_ehrTables->location),'EJ.Location_Id=L.Location_Id',array('L.Street1 as oApartment_Name','L.Street2 as oStreet_Name','L.Pincode as oPincode','L.Country_Code as oCountry'))
			    
			    ->joinLeft(array('S'=>$this->_ehrTables->state),'S.State_Id=L.State_Id', array('S.State_Name as oState'))
								
			    ->joinLeft(array('C'=>$this->_ehrTables->city),'C.City_Id=L.City_Id', array('C.City_Name as oCity'))
			    
			    ->joinLeft(array('PC'=>$this->_ehrTables->country),'EC.pCountry=PC.Country_Code',array('Country_Name as pCountryName'))
			    ->joinLeft(array('CC'=>$this->_ehrTables->country),'EC.cCountry=CC.Country_Code',array('Country_Name as cCountryName'))
			    ->joinLeft(array('LC'=>$this->_ehrTables->country),'L.Country_Code=LC.Country_Code',array('Country_Name as oCountryName'))
			    ->where('EC.Employee_Id = ?', $empId);
	
	    
	}
	else
	{
	      $empContactQry = $this->_db->select()
		->from(array('contact'=>$this->_ehrTables->empContacts),array('*'))
		->joinLeft(array('pCountry'=>$this->_ehrTables->country),'contact.pCountry=pCountry.Country_Code',array('Country_Name as pCountryName'))
		->joinLeft(array('cCountry'=>$this->_ehrTables->country),'contact.cCountry=cCountry.Country_Code',array('Country_Name as cCountryName'))
		->joinLeft(array('oCountry'=>$this->_ehrTables->country),'contact.oCountry=oCountry.Country_Code',array('Country_Name as oCountryName'))
		//->joinLeft(array('S'=>$this->_ehrTables->state),'S.State_Id=contact.oState', array('S.State_Name as oState','S.State_Id as oStateId'))								
		//->joinLeft(array('C'=>$this->_ehrTables->city),'C.City_Id=contact.oCity', array('C.City_Name as oCity','C.City_Id as oCityId'))
		->where('contact.Employee_Id = ?', $empId);
	
	}
	
	
        
//	$empContactQry = $this->_db->select()
//        ->from(array('contact'=>$this->_ehrTables->empContacts),array('*'))
//        ->joinLeft(array('pCountry'=>$this->_ehrTables->country),'contact.pCountry=pCountry.Country_Code',array('Country_Name as pCountryName'))
//        ->joinLeft(array('cCountry'=>$this->_ehrTables->country),'contact.cCountry=cCountry.Country_Code',array('Country_Name as cCountryName'))
//        ->joinLeft(array('oCountry'=>$this->_ehrTables->country),'contact.oCountry=oCountry.Country_Code',array('Country_Name as oCountryName'))
//        ->where('contact.Employee_Id = ?', $empId);

        $empContactDet = $this->_db->fetchRow($empContactQry);
        return $empContactDet;

    }

    /**
     *
     * This function is to fetch employee
     * job details
     */

    public function getEmpJobInformation($empId)
    {		
        return $this->_db->fetchRow($this->_db->select()
									->from(array('personal'=>$this->_ehrTables->empPersonal), array('Emp_First_Name', 'Emp_Last_Name'))
									
									->joinLeft(array('job'=>$this->_ehrTables->empJob), 'personal.Employee_Id=job.Employee_Id', array('*',
                                    'Previous_Employee_Experience_Years' => new Zend_Db_Expr('FLOOR(CASE WHEN job.Previous_Employee_Experience IS NULL THEN 0 ELSE job.Previous_Employee_Experience/12 END)'),
									'Previous_Employee_Experience_Months' => new Zend_Db_Expr('CASE WHEN job.Previous_Employee_Experience IS NULL THEN 0 ELSE job.Previous_Employee_Experience%12 END')))
									
									->joinLeft(array('w'=>$this->_ehrTables->workSchedule), 'w.WorkSchedule_Id=job.Work_Schedule',
											   array('Title'))
                                    
                                    ->joinLeft(array('SP' => $this->_ehrTables->serviceProvider),'job.Service_Provider_Id = SP.Service_Provider_Id',array('job.Service_Provider_Id','SP.Service_Provider_Name'))           
									
									->joinLeft(array('manPersonal'=>$this->_ehrTables->empPersonal), 'job.Manager_Id=manPersonal.Employee_Id',
											   array('Emp_First_Name as Manager_First_Name','Emp_Last_Name as Manager_Last_Name'))
									
									->joinLeft(array('empDesig'=>$this->_ehrTables->designation), 'job.Designation_Id=empDesig.Designation_Id',
											   array('Designation_Name'))
									
									->joinLeft(array('empDept'=>$this->_ehrTables->dept), 'job.Department_Id=empDept.Department_Id',
											   array('Department_Name'))
									
									->joinLeft(array('esicReason'=>$this->_ehrTables->esicReason), 'job.Reason_Id=esicReason.Reason_Id',
											   array('ESIC_Reason'))
									
									->joinLeft(array('empType'=>$this->_ehrTables->empType), 'job.EmpType_Id=empType.EmpType_Id',
											   array('Employee_Type as Employee_Type'))
									
									->joinLeft(array('location'=>$this->_ehrTables->location), 'job.Location_Id=location.Location_Id',
											   array('Location_Name'))
									
									->joinLeft(array('EP'=>$this->_ehrTables->empProfession), 'EP.Profession_Id = job.Emp_Profession',
                                            array('Profession_Name'))
                                    ->joinLeft(array('DLA' =>$this->_ehrTables->departmentLevel),'personal.Employee_Id = DLA.Employee_Id',array(''))
							
                                    ->joinLeft(array('D1'=>$this->_ehrTables->dept),'DLA.Department_Id=D1.Department_Id',
                                                       array('Mapped_Department'=>new Zend_Db_Expr('GROUP_CONCAT(DISTINCT D1.Department_Name order by D1.Department_Name Asc)')))
									->where('job.Employee_Id = ?', $empId));
	}
    
	/**
     *
     * This function is to find employee Designation of an employee.
     */
    public function getEmpDesignation($employeeId)
    {
        $srDesQry = $this->_db->select()->distinct()
        ->from(array('job'=>$this->_ehrTables->empJob),array('Designation_Id'))
        ->joinLeft(array('designation'=>$this->_ehrTables->designation),'job.Designation_Id=designation.Designation_Id',array('Designation_Name','Designation_Id'))
        ->where('job.Employee_Id = ?', $employeeId);
        $srDesResult = $this->_db->fetchRow($srDesQry);
        return $srDesResult;
    }
    
	/**
     *
     * This function is to find employee experience details for an employee ...
     *
     */
    public function getEmpExperience($employeeId)
    {
 $qryResult = $this->_db->select()->from($this->_ehrTables->empExperience,
										   array('*',
												 new Zend_Db_Expr("DATE_FORMAT(Start_Date_Join,'".$this->_orgDF['sql']."') as StartDateJoin"),
												 new Zend_Db_Expr("DATE_FORMAT(End_Date,'".$this->_orgDF['sql']."') as EndDate")))
									->where('Employee_Id = ?', (int) $employeeId);
				$subGridDataQryResult = $this->_db->fetchAll($qryResult);
				
				$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empExperience,
																  new Zend_Db_Expr('COUNT(Experience_Id)')));
				
				if(!empty($iTotal))
				{
					for($l=0;$l<count($subGridDataQryResult);$l++)
					{
						/** get uploaded declaration uploaded files **/				
						$employeesDocumentFiles =  $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->empExperienceDocuments,array('File_Name','File_Size'))
							->where('Experience_Id = ?', $subGridDataQryResult[$l]['Experience_Id']));
							
						$subGridDataQryResult[$l]['Employees_Document_File_Path'] = $employeesDocumentFiles;
																		  
					}
				}
				return $subGridDataQryResult;
    }

    /**
     *
     * This function is to find employee educational details for an employee ...
     *
     */
    public function getEmpEducation($employeeId)
    {
        $employeeId = (int) $employeeId;

        $educationSelect = $this->_db->select()->from($this->_ehrTables->empEducation, array('*'))
									->joinLeft(array('c'=>$this->_ehrTables->courseDetails),'Education_Type=c.Course_Id ',
												array('c.Course_Name'))
									->where('Employee_Id = ?',$employeeId);
        $educationRow = $this->_db->fetchAll($educationSelect);
        
        $illiterateDet = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empPersonal, array('Is_Illiterate'))
									->where('Employee_Id = ?',$employeeId));
        
        if(!$educationRow)
        {
            return array('Illiterate' => $illiterateDet);
        }
        
        return array('Illiterate'=> $illiterateDet, 'EducationDet'=>$educationRow);
    }
    
	/**
     *
     * This function is to find employee certification details for an employee ...
     *
     */
    public function getEmpCertificate ($employeeId)
    {
        return $this->_db->fetchAll($this->_db->select()
									->from($this->_ehrTables->empCertifications,
										   array('*', 'ReceivedDate' => new Zend_Db_Expr("date_format(Received_Date, '".$this->_orgDF['sql']."')")))
									->where('Employee_Id = ?',(int) $employeeId));
    }
    
	/**
     * This function is to find employee award details for an employee ...
     */
    public function getEmpAward ($employeeId)
    {
        return $this->_db->fetchAll($this->_db->select()
									->from($this->_ehrTables->empAward,
										   array('*', 'ReceivedOn' => new Zend_Db_Expr("date_format(Received_On, '".$this->_orgDF['sql']."')")))
									->where('Employee_Id = ?',(int) $employeeId));
    }
    
	/**
     *
     * This function is to find employee training details for an employee ...
     *
     */
    public function getEmpTraining($employeeId)
    {
        return $this->_db->fetchAll($this->_db->select()
									->from($this->_ehrTables->empTraining,
										   array('*',
												 'TrainingStartDate' => new Zend_Db_Expr("date_format(Training_Start_Date, '".$this->_orgDF['sql']."')"),
												 'TrainingEndDate' => new Zend_Db_Expr("date_format(Training_End_Date, '".$this->_orgDF['sql']."')")))
									->where('Employee_Id = ?', (int) $employeeId));
    }
	
	/**
	 * Get employee skillset by employeeId
	 */
    public function getEmpSkillset($employeeId)
    {
        $employeeId = (int) $employeeId;
        $empSkills = $this->_db->select()->from($this->_ehrTables->empSkillset, array('*'))->where('Employee_Id = ?',$employeeId);
        $skillsRow= $this->_db->fetchRow($empSkills);
        if(!$skillsRow)
        {
            return false;
        }
        return $skillsRow;
    }

    /**
     *
     * This is for fetch employee bank details.
     */
    public function getEmpBankDetails($employeeId)
    {
        $employeeId = (int) $employeeId;

//        $empBank = $this->_db->select()->from(array('EB'=>$this->_ehrTables->empBank), array('*'))
//						->joinLeft(array('AT'=>$this->_ehrTables->accountType),'EB.Account_Type_Id=AT.Account_Type_Id',
//								   array('AT.Account_Type'))											
//					->where('Employee_Id = ?',$employeeId);

        $empBank = $this->_db->select()->from(array('EB'=>$this->_ehrTables->empBank),
											  array('*'))
						->joinLeft(array('AT'=>$this->_ehrTables->accountType),'EB.Account_Type_Id=AT.Account_Type_Id',
								   array('AT.Account_Type'))											
					->where('EB.Employee_Id = ?',$employeeId);
        
		//$bankRow= $this->_db->fetchRow($empBank);
        $bankRow= $this->_db->fetchAll($empBank);
		if(!$bankRow)
        {
            return false;
        }
        
		return $bankRow;
    }

	/**
	 * Get count of employee mailId
	 */
    public function countEmpEmailId($emailId, $empId)
    {
        if (!empty($emailId))
        {
            
        
            $qryEmailId = $this->_db->select()->from($this->_ehrTables->empJob, new Zend_Db_Expr('COUNT(Emp_Email)'))
											->where('Emp_Email = ?', $emailId)
											->where('Emp_Email IS NOT NULL')
											->where('Emp_Status = ?', 'Active')
											->where('Emp_Email != ?', '');
            
			if (!empty($empId))
            {
                $qryEmailId->where('Employee_Id != ?', $empId);
            }
			
            return $this->_db->fetchOne($qryEmailId);
        }
        else
        {
            return 0;
        }
    }
    
    //  Get count of employee mobile number
    public function countEmpMobileNumber($mobileNumber, $empId)
    {
        if (!empty($mobileNumber))
        {
            $qry = $this->_db->select()->from(array('EP'=>$this->_ehrTables->empPersonal), new Zend_Db_Expr('COUNT(Sign_In_Mobile_Number)'))
                                            
                                            ->joinLeft(array('EJ'=>$this->_ehrTables->empJob),'EP.Employee_Id = EJ.Employee_Id',
                                            array('EJ.Emp_Status'))

                                            ->where('EP.Sign_In_Mobile_Number = ?', $mobileNumber)

											->where('EJ.Emp_Status = ?', 'Active');
            
			if (!empty($empId))
            {
                $qry->where('EP.Employee_Id != ?', $empId);
            }
            return $this->_db->fetchOne($qry);
        }
        else
        {
            return 0;
        }
    }

    /**
     *
     * This is for fetch employee bank details.
     */
    public function checkBankDetails($bankAccNo,$bankName, $empId='')
    {
        if(!empty($bankAccNo) && !empty($bankName))
        {
            $empBank = $this->_db->select()->from($this->_ehrTables->empBank, array(new Zend_Db_Expr('Count(Employee_Id)')))
            ->where('Bank_Account_Number = ?',$bankAccNo)
            ->where('Bank_Name like ?',"$bankName");
            if(!empty($empId))
            {
                $empBank->where('Employee_Id != ?', $empId);
            }
            $bankRow= $this->_db->fetchOne($empBank);
            return $bankRow;
        }
        else
        return 0;
    }

    /**
     *
     * This is for fetch employee login details.
     */
    public function getEmpLogin($employeeId)
    {
        $employeeId = (int) $employeeId;
        $loginQry = $this->_db->select()->from($this->_ehrTables->empLogin, array('Username'=>'User_Name', 'Created_Date', 'Employee_Id'))->where('Employee_Id = ?',$employeeId);
        $getLogin = $this->_db->fetchRow($loginQry);
        if(!$getLogin)
        {
            return false;
        }
        return $getLogin;
    }
	
    /**
     *
     * This function is fetch employee driving license details
     */
    public function getEmpDrivingLicense($empId)
    {
        $empLicenseQry = $this->_db->select()
        ->from(array('license'=>$this->_ehrTables->empDrivingLicense),array('*'))
        ->joinLeft(array('country'=>$this->_ehrTables->country),'license.Issuing_Country=country.Country_Code',array('Country_Name'))
        ->where('license.Employee_Id = ?', $empId);
        $empLicenseDet = $this->_db->fetchRow($empLicenseQry);
        return $empLicenseDet;

    }
	
    /**
     *
     * This function is to find employee language details for an employee ...
     *
     */
    public function getEmpLanguage($employeeId)
    {
        $employeeId= (int) $employeeId;
        $getEmpLang = $this->_db->select()->from($this->_ehrTables->empLang,array('Lang_Known'))->where('Employee_Id = ?', $employeeId);
        $langResult = $this->_db->fetchCol($getEmpLang);
        return $langResult;

    }

    /**
     *
     * This function is to find employee language details for an employee ...
     *
     */
    public function getEmpLangDet($employeeId)
    {
        $employeeId= (int) $employeeId;
        $getEmpLang = $this->_db->select()
        ->from(array('empLang'=>$this->_ehrTables->empLang),array(''))
        ->joinLeft(array('lang'=>$this->_ehrTables->languages),'lang.Lang_Id=empLang.Lang_Known',array('lang.Language_Name'))
        ->where('empLang.Employee_Id = ?', $employeeId);
        $langResult = $this->_db->fetchCol($getEmpLang);
        return $langResult;

    }

    /**
     *
     * This function is to find dependent details for an employee ...
     *
     */
    public function getEmpDependent ($employeeId)
    {
        return $this->_db->fetchAll($this->_db->select()
									->from($this->_ehrTables->empDependent,
										   array('*',
												 new Zend_Db_Expr("CONCAT(Dependent_First_Name,' ',Dependent_Last_Name) as Dependent_Name"),
												 new Zend_Db_Expr("DATE_FORMAT(Dependent_DOB,'".$this->_orgDF['sql']."') as Date_Of_Birth")))
									->where('Employee_Id = ?', (int) $employeeId));
    }

    /**
     *
     * This function is to find hobby details for an employee ...
     *
     */
    public function getHobby($employeeId)
    {
        $employeeId = (int) $employeeId;
        $hobbyQry = $this->_db->select()->from($this->_ehrTables->empHobbies, 'Emp_Hobby')->where('Employee_Id='.$employeeId);
        $hobby = $this->_db->fetchCol($hobbyQry);
        return $hobby;
    }

    /**
     *
     * This function is to fetch dependent relationship
     * based on marital status
     *
     */
    public function getMaritalStatusRelationship ($maritalStatusId, $employeeId=null)
    {
        $maritalStatusRelationship =  $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->maritalRelation,
															   array('Dependent_Relationship')));
		
		/** We should restrict to add only on father and mother. Rest can be more than one **/	
		if($employeeId!=null){
			$relationshipArr = array(0=>'Father',1=>'Mother');
			$dependentRelationShip = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empDependent, array('Relationship'))
									->where('Employee_Id = ?', $employeeId)
									->where('Relationship IN (?)', $relationshipArr));
			
			if(!empty($dependentRelationShip)){
				foreach($maritalStatusRelationship as $key=>$row){					
					if(in_array($row['Dependent_Relationship'],$dependentRelationShip)){						
						unset($maritalStatusRelationship[$key]);
					}	
				}
			}
			
			return $maritalStatusRelationship;
		}
		else{
			return $maritalStatusRelationship;
		}
    }

    /**
     *
     * This function is to find
     * marital status details from a table.
     */
    public function getMaritalStatusRelation($maritalStatusId)
    {
        $marQry = $this->_db->select()
        ->from(array('mar'=>$this->_ehrTables->maritalRelation),array('Marital_Status_Id','Dependent_Relationship'));
        $marStatusRelation = $this->_db->fetchPairs($marQry);
        return $marStatusRelation;
    }
    
	/**
     *
     * This function is to find
     * marital status details from a table.
     */
    public function getMaritalStatus()
    {
        return $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->maritalStatus, array('Marital_Status_Id', 'Marital_Status')));
    }
    
	//to list Marital Status in employee import inline edit
    public function getMaritals()
    {
    	return  $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->maritalStatus,array('value'=>'Marital_Status',
																									   'text' => 'Marital_Status')));
    }
    
	//to list laguages known in employee import inline edit    
    public function getLanguagesKnown()
    {    
    	return $this->_db->fetchAll($this->_db ->select()->from(array('LK'=>$this->_ehrTables->languages),
								array('value'=> 'LK.Language_Name','text'=>'LK.Language_Name'))
    	->order('LK.Language_Name ASC'));
    }
	
	/**
     *
     * This function is to find
     * language details from a table.
     */
    public function getLanguages()
    {
        $langQry = $this->_db->select()
        ->from(array('lang'=>$this->_ehrTables->languages),array('lang.Lang_Id','lang.Language_Name'))->order('Language_Name ASC');
        $language = $this->_db->fetchPairs($langQry);
        return $language;
    }
    
	/**
     *
     * This function is to find managerIds by managername ...
     *
     */
    public function getManagerByName($firstName,$lastName)
    {
        $this->_db->setFetchMode(Zend_Db::FETCH_BOTH);
        $srEmpQry = $this->_db->select()
        ->from(array('personal'=>$this->_ehrTables->empPersonal),array('Employee_Id'))
        ->where('personal.Emp_First_Name Like ?', "$firstName%")
        ->where('personal.Emp_Last_Name Like ?', "$lastName%")
        ->where('personal.Is_Manager = ?', 1);
        $srEmpresult = $this->_db->fetchAll($srEmpQry);
        return $srEmpresult;
    }
    
	/**
     *
     * This function is check whether the given username
     * exists or not in a database.
     */
    public function getEmpUsername($userName,$empId = NULL)
    {
        $loginDetQry = $this->_db->select()
        ->from($this->_ehrTables->empLogin, array(new Zend_Db_Expr('COUNT(User_Name)')))
        ->where('User_Name = ?', $userName);
        if(!is_null($empId))
        {
        	$loginDetQry->where('Employee_Id != ?',$empId);
        }
        return $this->_db->fetchOne($loginDetQry);
    }
	

    /**
     *
     * This function is to fetch employee
     * work schedule details ...
     */
    public function workSchedulePair()
    {
        return $this->_db->fetchPairs($this->_db->select()
									  ->from($this->_ehrTables->workSchedule, array('WorkSchedule_Id', 'Title'))
									  ->group('WorkSchedule_Id')
                                      ->where('WorkSchedule_Status = ?','Active')
									  ->order('Title ASC'));
    }
    
	//to list workSchedule in employee import inline edit
	public function getWorkSchedules()
    {
        return $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->workSchedule,array('value'=> 'Title','text'=>'Title'))
        												->group('WorkSchedule_Id')
                                                        ->where('WorkSchedule_Status = ?','Active')
                                                        ->order('Title ASC'));
    }
    
	/**
     * employeeId =5:fetch row[0]['Manager_id]=7 row[0]['managerName]=nithya nagaraj
     * if admin then manager_id will null so we can't make join inner and produce some error
     * so that make sure that manager_id is empty if it is null then employeeId as manager_id
     * else get employee_id's manager_id
     */
    public function loadManagerNameWithIdFetchRow($employeeId)
    {
        $fetchManId = $this->_db->fetchOne($this->_db->select()->from(array('JOB'=>$this->_ehrTables->empJob),array('Manager_Id'))->where('JOB.Employee_Id =?',$employeeId));
        $loadManagerNameWithIdFetchAllSql = (empty($fetchManId)) ? $this->_db->select()->from(array('PER'=>$this->_ehrTables->empPersonal),array('Manager_Id'=>'Employee_Id','managerName' => new Zend_Db_Expr("CONCAT(PER.Emp_First_Name, ' ',PER.Emp_Last_Name)")))->where('PER.Employee_Id =?',$employeeId) :
        $this->_db->select()->from(array('PER'=>$this->_ehrTables->empPersonal),array('Manager_Id'=>'Employee_Id','managerName' => new Zend_Db_Expr("CONCAT(PER.Emp_First_Name, ' ',PER.Emp_Last_Name)")))->where('PER.Employee_Id IN (?)',$fetchManId);
        return $this->_db->fetchRow($loadManagerNameWithIdFetchAllSql);
    }
    
	/**
	 * Get Reportee names by ids
	 */
	public function reporteeName($ids)
    {
		$reportee = array();
        if (!empty($ids) && is_array($ids))
		{
            foreach ($ids as $id)
			{
                array_push($reportee, $this->employeeName($id));
            }
        }
		
        return  $reportee;
    }
	
    public function __destruct()
    {
        
    }	
	
}

