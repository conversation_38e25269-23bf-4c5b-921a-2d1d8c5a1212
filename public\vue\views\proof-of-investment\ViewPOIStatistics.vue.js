var customStatisticsCard = Vue.component('view-poi-statistics', {
    template: `
        <section>
            <section class="component-padding" style="margin-top:45px" v-if="!showListData && !loadSkeleton" >
            <div>
                <v-card class="card-radius poi-statistics-view-card top-data-card">
                    <v-card-title class="poi-statistics-card-title">  
                        <div class= "poi-statistics-card-heading title font-weight-medium" style="padding-top: 15px">
                            Self Occupied
                        </div>
                    </v-card-title>
                    <v-card-text class="poi-statistics-card-content">
                        <v-row class="poi-statistics-status-row">
                            <v-col xl="12" lg="12" md="12" sm="12" cols="12" class="poi-statistics-card-data"  v-if="!loadStatisticsData">
                                <custom-statistics-card v-for="item in selfOccupiedPropertyStatistics" :key="item"
                                    :unique-id = item
                                    :view-data1 ="item.empCount" 
                                    :view-data-txt1="item.status" 
                                    :avatar-color="item.iconColor" 
                                    :is-icon="true" 
                                    :icon-name="item.iconName"
                                    :show-button = "true"
                                    :is-clickable-card = "true"
                                    :icon-outline-color ="item.iconColor"
                                    @click-card="retrieveEmpDeclaration($event)">
                                    <template slot="actionButton" v-if="item.status === 'Declared' || item.status === 'Returned' || item.status === 'Reopened'">
                                        <v-btn small rounded @click.stop="fnNotify(item)" :disabled="item.empCount === 0 && rolesResponse.Role_Optional_Choice">
                                                Notify
                                                <v-icon class="statistics-btn-icon ml-1">
                                                    send
                                            </v-icon> 
                                        </v-btn>
                                    </template>
                                </custom-statistics-card>
                            </v-col>
                            <v-col xl="12" lg="12" md="12" sm="12" cols="12" class="poi-statistics-card-data" v-else>
                                <v-skeleton-loader style="margin:2% !important" v-for="i in 5" :key="i"
                                    ref="skeleton"
                                    type="image"
                                    class="mx-auto"
                                ></v-skeleton-loader>
                            </v-col>
                        </v-row>
                    </v-card-text>
                </v-card>
                <v-card class="card-radius poi-statistics-view-card top-data-card mt-10">
                    <v-card-title class="poi-statistics-card-title">  
                        <div class= "poi-statistics-card-heading title font-weight-medium" style="padding-top: 15px">
                            Rented
                        </div>
                    </v-card-title>
                    <v-card-text class="poi-statistics-card-content">
                        <v-row class="poi-statistics-status-row">
                            <v-col xl="12" lg="12" md="12" sm="12" cols="12" class="poi-statistics-card-data"  v-if="!loadStatisticsData">
                                <custom-statistics-card v-for="item in rentedPropertyStatistics" :key="item"
                                    :unique-id = item
                                    :view-data1 ="item.empCount" 
                                    :view-data-txt1="item.status" 
                                    :avatar-color="item.iconColor" 
                                    :is-icon="true" 
                                    :icon-name="item.iconName"
                                    :show-button = "true"
                                    :is-clickable-card = "true"
                                    :icon-outline-color ="item.iconColor"
                                    @click-card="retrieveEmpDeclaration($event)">
                                    <template slot="actionButton" v-if="item.status === 'Declared' || item.status === 'Returned' || item.status === 'Reopened'">
                                        <v-btn small rounded @click.stop="fnNotify(item)" :disabled="item.empCount === 0 && rolesResponse.Role_Optional_Choice">
                                                Notify
                                                <v-icon class="statistics-btn-icon ml-1">
                                                    send
                                            </v-icon> 
                                        </v-btn>
                                    </template>
                                </custom-statistics-card>
                            </v-col>
                            <v-col xl="12" lg="12" md="12" sm="12" cols="12" class="poi-statistics-card-data" v-else>
                                <v-skeleton-loader style="margin:2% !important" v-for="i in 5" :key="i"
                                    ref="skeleton"
                                    type="image"
                                    class="mx-auto"
                                ></v-skeleton-loader>
                            </v-col>
                        </v-row>
                    </v-card-text>
                </v-card>
                <v-card class="card-radius poi-statistics-view-card top-data-card mt-10" :style="isTaxReliefHasAccess ? '' : 'display: none'">
                    <v-card-title class="poi-statistics-card-title">  
                        <div class= "poi-statistics-card-heading title font-weight-medium" style="padding-top: 15px">
                            Tax Relief
                        </div>
                    </v-card-title>
                    <v-card-text class="poi-statistics-card-content">
                        <v-row style="margin-top: -2%;">
                            <v-col cols="12" class="poi-statistics-card-data"  v-if="!loadTaxReliefStatistics" style="align-items: center;"
                                :style="windowWidth <= 1700 ? '' : 'flex-wrap: nowrap;'"
                            >
                                <custom-statistics-card 
                                    v-for="item in taxReliefStatistics"
                                    :key="item"
                                    :unique-id = item
                                    :view-data1 ="item.empCount" 
                                    :view-data-txt1="item.status" 
                                    :avatar-color="item.iconColor" 
                                    :is-icon="true" 
                                    :icon-name="item.iconName"
                                    :show-button = "true"
                                    :is-clickable-card = "true"
                                    :icon-outline-color ="item.iconColor"
                                    @click-card="retrieveEmpDeclaration($event, 'tax-relief')"
                                ></custom-statistics-card>
                                <div class="d-flex flex-column">
                                    <v-btn color="primary" small @click="addTaxRelief()" style="max-width: 200px">
                                            View All
                                    </v-btn>
                                    <v-btn color="primary" class="mt-2" small @click="addTaxRelief('bulk')" style="max-width: 200px">
                                            Bulk Import
                                    </v-btn>
                                </div>
                            </v-col>
                            <v-col cols="12" class="poi-statistics-card-data" v-if="loadTaxReliefStatistics">
                                <v-skeleton-loader style="margin:2% !important" v-for="i in 6" :key="i"
                                    ref="skeleton"
                                    type="image"
                                    class="mx-auto"
                                ></v-skeleton-loader>
                            </v-col>
                        </v-row>
                    </v-card-text>
                </v-card>
            </div>
            </section>
            <section>
                <list-income-under-section v-if="showListData && !loadSkeleton" 
                    user-type="admin" 
                    :list-response="listData" 
                    :render-count="listRenderCount" 
                    :currency-symbol="currencySymbol"
                    :income-source-types = "incomeSourceTypes"
                    :tax-declaration-base-url = "taxDeclarationBaseUrl"
                    :ats-base-url = "atsBaseUrl"
                    :api-headers = "apiHeaders"
                    :org-code = "orgCode"
                    :employee-id = "employeeId"
                    :emp-taxable-income = "empTaxableIncome"
                    :emp-annual-tax = "empAnnualTax"
                    :current-assessment-year = "currentAssessmentYear"
                    :selected-assessment-year="selectedAssessmentYear"
                    :roles-response = "rolesResponse"
                    :form-source = "formSource"
                    :chosen-status = "chosenStatus"
                    :chosen-emp-ids= "chosenEmployeeIds"
                    :chosen-type="chosenType"
                    :success-type="successType"
                    :success-data = "successData"
                    @handle-error="handleError($event)"
                    @open-close-view-modal="openCloseViewModal($event)"
                    @handle-document-error="handleDocumentError($event)"
                    @handle-warning-msg="handleWarningMessages($event)"
                    @handle-approval-success="handleSuccessMessages($event)"								
                    @back-to-statistics = "backToStatistics()"							
                    @change-employee="fnChangeEmployee($event)">								
                </list-income-under-section>
            </section>
            <custom-email-template-modal :open-modal="openNotifyModal" 
                @closemodal="openNotifyModal = false" 
                @send-email-template="sendEmailNotify($event)"
                :base-path="basePath"
                :default-template="defaultTemplateContent"
                :sub-title="chosenStatus"
                :chip-content="loggedEmployeeDetail">
            </custom-email-template-modal>
          
            <section v-if="loadSkeleton" style="margin-top: 70px;padding: 30px;">
                <v-skeleton-loader
                    ref="skeleton1"
                    type="list-item-avatar-three-line"
                    class="mx-auto"
                ></v-skeleton-loader>
                <br>
                <br>
                <br>
                <v-skeleton-loader
                    ref="skeleton2"
                    type="table-thead"
                    class="mx-auto"
                ></v-skeleton-loader>
                <br>
                <div v-for="i in 3" :key="i">
                    <v-skeleton-loader
                        ref="skeleton2"
                        type="list-item-avatar"
                        class="mx-auto"
                    ></v-skeleton-loader>
                    <br>
                </div>
            </section>
            <employee-list-modal 
                        v-if="showEmpModal"
						ref="empListModal" 
						:show-modal="showEmpModal"
						:department-list="departmentList" 
						:designation-list="designationList"
						:location-list="locationList" 
						:emp-type-list="empTypeList" 
						:work-schedule-list="workScheduleList"
						:employees-list="employeesList"
						:modal-title="empModalTitle"
						:table-headers="empListHeader"
						:single-select="true"
						table-item-key="employee_id"
						no-match-image-name="income_initial_img"
						submit-button-text="Proceed"
                        :show-filter-search="true"
                        @apply-filter="fnApplyingFilterInEmpListPopup($event)"
                        @close-employee-modal="fnCancelEmpListModal(true)"
                        @proceed-on-modal="fnProceedInEmpListModal($event)"
            >
            </employee-list-modal>
            <!-- custom loading -->
            <custom-loading-screen v-if="loadingInPOI"></custom-loading-screen>
        </section>
        `,


props: {
    apiHeaders : {
        type : Object,
        required : true
    },

    orgCode : {
        type : String,
        required : true
    },

    employeeId : {
        type : Number,
        required : true
    },

    taxDeclarationBaseUrl : {
        type: String,
        required: true
    },

    atsBaseUrl : {
        type : String,
        required : true
    },

    selectedAssessmentYear : {
        type : Number,
        required : true
    },

    currentAssessmentYear : {
        type : Number,
        required : true
    },

    incomeSourceTypes : {
        type : Array,
        required : true
    },

    rolesResponse : {
        type : Object,
        required : true
    },

    formSource : {
        type : String,
        required : true
    },

    poiStatisticsRenderCount : {
        type : Number,
        default : 1
    },

    basePath : {
        type : String,
        required : true
    },

    baseUrl : {
        type : String,
        required : true
    },

    taxDeclarationGraphql : {
        type : String,
        required : true
    },

    taxStatutoryGraphql: {
        type : String,
        required : true
    },

    atsGraphql : {
        type : String,
        required : true
    },
    successData : {
        type : Object,
        default : {}
    },
    successType : {
        type : String,
        default : ''
    },

    retrieveStatistics : {
        type: Boolean ,
        default : false
    },

    windowWidth: {
        type : [String, Number],
        default : ''
    },
},

data: function () {
    
    return {
        // notification variables
        openNotifyModal: false,
        //Declared,Returned,Reopened
        rentedEmailTemplate:[{
            subject:"Gentle Reminder: Submit your rented property tax declaration proofs.",
            content:"It's about time to submit your proof. Please submit the proof of investment for the tax declaration to adjust your TDS calculation in the upcoming payroll. Failing which your statement of finances will not be considered in this financial year, and there may be a higher tax outgo for your salary."
        },
        {
            subject:"Gentle Reminder: Amendment required on your rented property tax declaration",
            content:"It's about time to act on your returned tax declaration Please review the returned tax declaration and amend it as required for review and approval. Delay in returning the tax declaration will have higher tax outgo for your salary in the upcoming payroll."
        },
        {
            subject:"Gentle Reminder: Amendment required on your rented property tax declaration",
            content:"It's about time to add additional proofs for your reopened tax declaration. Please review the reopened tax declaration and add additional investments as required and submit for review. Delay in providing further tax investment proof will have a higher tax outgo for your salary in the upcoming payroll."
        }],
        selfOccupiedTemplate:[{
            subject:"Gentle Reminder: Submit your self-occupied property tax declaration proofs.",
            content:"It's about time to submit your proof. Please submit the proof of investment for the tax declaration to adjust your TDS calculation in the upcoming payroll. Failing which your statement of finances will not be considered in this financial year, and there may be a higher tax outgo for your salary."
        },
        {
            subject:"Gentle Reminder: Amendment required on your self-occupied property tax declaration",
            content:"It's about time to act on your returned tax declaration Please review the returned tax declaration and amend it as required for review and approval. Delay in returning the tax declaration will have higher tax outgo for your salary in the upcoming payroll."
        },
        {
            subject:"Gentle Reminder: Amendment required on your self-occupied property tax declaration",
            content:"It's about time to add additional proofs for your reopened tax declaration. Please review the reopened tax declaration and add additional investments as required and submit for review. Delay in providing further tax investment proof will have a higher tax outgo for your salary in the upcoming payroll."
        }],
        loggedEmployeeDetail: {},
        // statistics variables
        showListData : false,
        loadSkeleton : false,
        empTaxableIncome : '',
        empAnnualTax : '',
        listRenderCount : 0,

        loadStatisticsData : false,
        loadTaxReliefStatistics: false,
        chosenStatus : '',
        chosenType : '',
        chosenEmployeeIds : [],

        statusData : [ {
            status : 'Declared',
            countKey : 'declaredCount',
            empIdKey : 'declaredEmployeeIds',
            iconColor : '#ba68c8',
            iconName : 'fas fa-hand-paper',
        },
        {
            status : 'Applied',
            countKey : 'appliedCount',
            empIdKey : 'appliedEmployeeIds',
            iconColor : '#1976d2',
            iconName : 'fas fa-hand-pointer',
        },
        {
            status : 'Returned',
            countKey : 'returnedCount',
            empIdKey : 'returnedEmployeeIds',
            iconColor : '#80DEEA',
            iconName : 'reply',
        },
        {
            status : 'Approved',
            countKey : 'approvedCount',
            empIdKey : 'approvedEmployeeIds',
            iconColor : '#00c853',
            iconName : 'check',
        },
        {
            status : 'Reopened',
            countKey : 'reopenedCount',
            empIdKey : 'reopenedEmployeeIds',
            iconColor : '#FFAB00',
            iconName : 'fa-refresh',
        },
        {
            status : 'Rejected',
            countKey : 'rejectedCount',
            empIdKey : 'rejectedEmployeeIds',
            iconColor : 'red',
            iconName : 'close',
        },
        ],

        isTaxReliefHasAccess: false,
        selfOccupiedPropertyStatistics : {},
        rentedPropertyStatistics : {},
        taxReliefStatistics : {},
        // employees popup variables
        departmentList : [],
        designationList : [],
        locationList  : [],
        empTypeList : [],
        workScheduleList : [],
        showEmpModal: false,
        empModalTitle: "",
        employeesList: [],
        empListHeader:[
            {
                text: 'Employee Id',
                value: 'user_defined_empId'
            },
            {
                text: 'Employee Name',
                align: 'left',
                value: 'employee_name'
            },
            {
                text: 'Designation',
                value: 'designation_name'
            },
            {
                text: 'Department',
                value: 'department_name'
            }
        ],
        selectedDesignation: [],
        selectedDepartment: [],
        selectedEmpType: [],
        selectedLocation: [],
        selectedWorkSchedule: [],
        selectedEmpId: '',
        loadingInPOI: false,
        currencySymbol : ''
    };
},

watch :{
    retrieveStatistics(val) {
       if(val) {
           // fetch the POI statistics data
            this.loadStatisticsData = true;
            this.loadTaxReliefStatistics = true;

            setTimeout(()=>{
                this.retrievePOIStatistics();
                this.retrieveTaxReliefStatistics();
            },1000)	
       }
    },
    // assessment year filter
    selectedAssessmentYear(val) {
        // fetch the POI statistics data
        this.loadStatisticsData = true;
        this.loadTaxReliefStatistics = true;

        setTimeout(()=>{
            this.retrievePOIStatistics();
            this.retrieveTaxReliefStatistics();
        },1000)	
    }
},

mounted: function () {
    if (this.poiStatisticsRenderCount == 1) {
        let styleElem = document.createElement('style');
        styleElem.textContent = `
            .component-padding{
                padding: 50px;
            }
            .poi-statistics-view-card {
                width : 100%;
                padding: 0 1% 0 1%;
            }
            .poi-statistics-card-heading {
                color : var(--v-primary-base);
            }
            .poi-statistics-card-data {
                font-size : 1em;
                display: flex;
                flex-direction: row;
                justify-content: center;
                align-items: stretch;
                flex-wrap: wrap;
            }
            .top-data-card {
				border-radius: 1em !important;
				box-shadow: 0px 2px 10px 0px #d3d1d18a;
				background-image: url(../vue/assets/images/bank-statement/green-left-background.png),url(../vue/assets/images/bank-statement/green-right-background.png);
                background-position: left bottom, right bottom;
                background-size: 359px 171px;
            }
            .poi-statistics-status-row {
                margin-top : -3%
            }
            .poi-statistics-card-content {
                padding-bottom :0px !important
            }
            .poi-statistics-img {
                margin-top :1% !important
            }

            .v-skeleton-loader__image {
                width : 200px !important;
                height : 115px !important
            }
            @media screen and (max-width:600px)  {
                .component-padding{
                    padding: 35px;
                }
                .poi-statistics-card-title {
                    display : flex;
                    justify-content : center;
                }
            }
            `;
            document.head.appendChild(styleElem);
           
    }

    // fetch the POI statistics data
    this.loadStatisticsData = true;
    this.loadTaxReliefStatistics = true;

    setTimeout(()=>{
        this.retrievePOIStatistics();
        this.fetchTaxReliefFormAccess();
    },1000)	
},
computed:{
    defaultTemplateContent(){
        if(this.chosenType == "rented-property"){
            if(this.chosenStatus == "Declared"){
                return this.rentedEmailTemplate[0]
            }
            else if(this.chosenStatus == "Returned"){
                return this.rentedEmailTemplate[1]

            }
            else{
                return this.rentedEmailTemplate[2]
            }
        }
        else{
            if(this.chosenStatus == "Declared"){
                return this.selfOccupiedTemplate[0]
            }
            else if(this.chosenStatus == "Returned"){
                return this.selfOccupiedTemplate[1]

            }
            else{
                return this.selfOccupiedTemplate[2]
            }
        }
    }
},
methods: {  
   //function to send email notification to the users
    sendEmailNotify(mailContents){
        try{
            var self = this;
            self.loadingInPOI = true;
            // query to send email template
			var sendEmailTemplate = self.taxDeclarationGraphql.query(`poiNotifyEmployees ($approvalStatus:String!,$sourceType:String!,$employeeIdArray:[Int]!, $assessmentYear:Int!,$emailSubjectContent:String!,$emailBodyContent:String!) { poiNotifyEmployees(approvalStatus:$approvalStatus,sourceType:$sourceType,employeeIdArray:$employeeIdArray,assessmentYear:$assessmentYear,emailSubjectContent:$emailSubjectContent,emailBodyContent:$emailBodyContent) { errorCode message validationError}}`);
            
            sendEmailTemplate({
                "approvalStatus": self.chosenStatus,
                "sourceType": self.chosenType,
                "employeeIdArray": self.chosenEmployeeIds,
                "assessmentYear": self.selectedAssessmentYear,
                "emailSubjectContent": mailContents[0],
                "emailBodyContent": mailContents[1]
            }).then(response => {
                self.$emit('handle-success', ["Notification sent successfully"]);
                self.loadingInPOI = false;
                self.openNotifyModal = false;
                self.openCloseViewModal(false);
            })
            .catch(notifyErr => {
                self.handleValidationError(notifyErr);
                self.loadingInPOI = false;
            });
        }
        catch(notifyErr){
            self.handleValidationError(notifyErr);
            self.loadingInPOI = false;
        }
    },
    // handle notification backend input validation 
    handleValidationError(error) {
        let self = this;            
        if(error && error.length > 0 && error[0] && error[0].message) {
            // error returned from backend
            var validationError = JSON.parse(error[0].message);
            var errorCode = validationError.errorCode;

            // check if the error code is validation error
            if(errorCode === 'IVE0000') {

                var validationError = error.validationError;
                var validationErrorCode = Object.keys(validationError);

                for (var i in validationErrorCode) {
                    switch (validationErrorCode[i]) {
                        case 'IVE0038':
                            // emailSubjectContent validation
                            self.handleWarningMessages('Invalid email subject');
                            break;
                        case 'IVE0039' :
                            // emailBodyContent validation
                            self.handleWarningMessages('Invalid email content');
                            break;
                        default:
                             // emailBodyContent validation
                             self.handleWarningMessages('Invalid input request');
                        break;
                    }
                }
            } else if (errorCode === '_DB0105') { // same error code is used for all access rights. But messages should be different.
                self.handleWarningMessages('Sorry, you dont have rights to send notifications. Please contact system administrator.');
            }
            else {
                self.openNotifyModal = false;
                self.openCloseViewModal(false);
                self.handleError(error);
            }
        }

    },
    // get the employee income and tax details
    fnRetrieveEmpTaxationData() {
        let self = this;
        try {
            self.loadSkeleton = true;
            //get the org date format
            axios.post(self.baseUrl + `payroll/salary-payslip/get-employee-tax-details`, {
                employeeId : self.selectedEmpId,
                assessmentYear : self.selectedAssessmentYear
            }).then(response => {
                // check if the data is retrieved
                if(response.data && response.data.success) {
                    self.empTaxableIncome =  response.data.empTaxDetails.Tax_Calculation_Details.Annual_Taxable_Income;
                    self.empAnnualTax = response.data.empTaxDetails.Tax_Calculation_Details.Annual_Tax;
                    self.currencySymbol = response.data.empTaxDetails.Tax_Calculation_Details.Currency_Symbol;
                    self.listPropertyDetails();
                }
                else {
                    self.loadSkeleton = false;
                    self.showListData = false;
                    self.handleError(null);
                }
            })
            .catch(function (fetchEmpTaxationError) {
                self.loadSkeleton = false;
                self.showListData = false;
                self.handleError(fetchEmpTaxationError);
            });
        }
        catch(fetchEmpTaxationError) {
            self.loadSkeleton = false;
            self.showListData = false;
            self.handleError(fetchEmpTaxationError);
        }

    },  
    listPropertyDetails() {
        // list Property details
        var self = this;
        try {
            self.loadSkeleton = true;
            var declarationList = self.taxDeclarationGraphql.query(`CommentQuery($employeeId:Int!,$assessmentYear:Int!,$status:[String]) { listHouseProperties (employeeId:$employeeId,assessmentYear:$assessmentYear,status:$status) { errorCode message houseProperties}}`);
            declarationList({
                "assessmentYear" : self.selectedAssessmentYear,
                "employeeId" : self.selectedEmpId,
                "status": self.chosenStatus
            }).then(function (response) {
                if (response.listHouseProperties.houseProperties) {
                    self.listRenderCount++;
                    self.listData = JSON.parse(response.listHouseProperties.houseProperties);
                    self.showListData = true;
                    self.loadSkeleton = false;
                }else{
                    self.loadSkeleton = false;
                    self.showListData = false;
                    self.handleError(null);
                }
            })
            .catch(function (declarationListFetchError) {
                self.loadSkeleton = false;
                self.showListData = false;
                self.handleError(declarationListFetchError);
            });
        }
        catch(declarationListFetchError) {
            self.loadSkeleton = false;
            self.showListData = false;
            self.handleError(declarationListFetchError);
        }
    },

    // handle error messages from BE
    handleError(error , isList=0) {
        if(isList) {
            this.$emit('handle-poi-error', error)
        }
        else {
            if(error && error.length > 0 && error[0] && error[0].message) {
                // handle error from POI Approval actions
                var approvalError = JSON.parse(error[0].message);
                var errorCode = approvalError.errorCode;
                // check if the error codes are from POi approval actions 
                if(errorCode === 'PR0017' || errorCode === 'PR0013' || errorCode === 'PR0116') {
                    this.listPropertyDetails();
                    this.$emit('handle-error', error);
                }
                else {
                    this.$emit('handle-error', error);
                }
            } else {
                this.$emit('handle-error', error);
            }
        }
    },

    // handle error from document viewer
    handleDocumentError(fetchPresignedUrlError) {
        this.$emit('handle-document-error',fetchPresignedUrlError);
    },

    // open/close the modal -> to change the top bar class
    openCloseViewModal(value) {
        this.$emit('open-close-view-modal',value);
    },

    // open/close the modal -> to change the top bar class
    handleWarningMessages(value) {
        this.$emit('handle-warning-msg',value);
    },
        
    // open/close the modal -> to change the top bar class
    handleSuccessMessages(approvalSuccess) {
        this.$emit('handle-success', approvalSuccess);
        this.listPropertyDetails();
    },

    // show POI statistics page
    backToStatistics() {
        this.showListData = false;
        this.retrievePOIStatistics();
        this.retrieveTaxReliefStatistics();
        this.$emit('show-hide-statistics',true);
    },

    // fetch the POI statistics data
    retrievePOIStatistics() {
        var self = this;
        try {
            var declarationList = self.taxDeclarationGraphql.query(`poiStatistics ($assessmentYear:Int!) { poiStatistics (assessmentYear:$assessmentYear) 
                { errorCode message poiStatistics logInEmpDetails}}`);
            declarationList({
                "assessmentYear" : self.selectedAssessmentYear,
            }).then(function (response) {
                // success response from POI statistics
                if(response.poiStatistics) {
                    var poiResponse = response.poiStatistics.poiStatistics;
                    poiResponse = JSON.parse(poiResponse);

                    // self occupied statistics data
                    var selfOccupiedData = poiResponse.selfOccupiedPropertyDetails;
                    // rented statistics data
                    var rentedPropertyData = poiResponse.rentedPropertyDetails;

                    var data1 = [];
                    var data2 = [];

                    var statusData = self.statusData;
                    self.loggedEmployeeDetail = JSON.parse(response.poiStatistics.logInEmpDetails);
                    // form the data for selfOccupied/Rented -> to show the statistics card 
                    for(var i in statusData) {
                        data1.push( {
                            type : 'self-occupied-property',
                            status : statusData[i].status,
                            empCount : selfOccupiedData[statusData[i].countKey] ? selfOccupiedData[statusData[i].countKey] : 0,
                            chosenEmployeeIds : selfOccupiedData[statusData[i].empIdKey] ? selfOccupiedData[statusData[i].empIdKey] : [],
                            iconColor : statusData[i].iconColor,
                            iconName : statusData[i].iconName,
                        });
                    
                        data2.push( {
                            type : 'rented-property',
                            status : statusData[i].status,
                            empCount : rentedPropertyData[statusData[i].countKey] ? rentedPropertyData[statusData[i].countKey] : 0,
                            chosenEmployeeIds : rentedPropertyData[statusData[i].empIdKey] ? rentedPropertyData[statusData[i].empIdKey] : [],
                            iconColor : statusData[i].iconColor,
                            iconName : statusData[i].iconName,
                        });
                    }
                    self.selfOccupiedPropertyStatistics = data1;
                    self.rentedPropertyStatistics = data2;
                    self.loadStatisticsData = false;
                    self.fetchDropDownData();
                }
                else {
                    self.loadStatisticsData = false;
                    self.handleError(null,1);
                }
            })
            .catch(function (poiStatisticsError) {
                self.loadStatisticsData = false;
                self.handleError(declarationListFetchError,1);
            });
        }
        catch(poiStatisticsError) {
            self.loadStatisticsData = false;
            self.handleError(declarationListFetchError,1);
        }
    },

    fetchTaxReliefFormAccess() {
        var self = this;
        var accessRights = self.atsGraphql(`mutation(
        $formName: String,
        $employeeId: Int!) {
            getAccessRights
                (
                    formName: $formName,
                    employeeId:$employeeId
                ) 
                {
                    errorCode message rights {
                        Role_View Role_Add Role_Update Role_Delete Role_Optional_Choice Role_Hr_Group Role_Payroll_Group Is_Manager
                    }
                }
            }
        `);
        accessRights({
            formName: 'Tax Relief Declaration',
            employeeId: self.employeeId
        })
        .then(function (response) {
            if (response) {
                var response = response.getAccessRights.rights;
                if(response.Role_View) {
                    self.isTaxReliefHasAccess = true;
                    self.retrieveTaxReliefStatistics();
                } else {
                    self.isTaxReliefHasAccess = false;
                }
            } else {
                self.isTaxReliefHasAccess = false;
            }
        })
        .catch(function () {
            self.isTaxReliefHasAccess = false;
        })
    },

    retrieveTaxReliefStatistics() {
        var self = this;
        if (self.isTaxReliefHasAccess) {
            try {
                var taxReliefDeclarationList = self.taxStatutoryGraphql.query(`retrieveTaxReliefStatusCount($assessmentYear: String!) {
                    retrieveTaxReliefStatusCount(assessmentYear: $assessmentYear) {
                    errorCode
                    message
                    declared
                    applied
                    approved
                    rejected
                    reopened
                    returned
                    }
                }`);
                taxReliefDeclarationList({
                    "assessmentYear" : self.selectedAssessmentYear.toString(),
                }).then(function (response) {
                    // success response from POI statistics
                    if(response.retrieveTaxReliefStatusCount) {
                        var taxReliefResponse = response.retrieveTaxReliefStatusCount;
                        const {
                            declared,
                            applied,
                            approved,
                            reopened,
                            returned,
                            rejected,
                        } = taxReliefResponse;
                        var taxReliefData = [];
                        var statusCountValue = {"Declared": declared, "Applied": applied, "Returned":returned , "Approved": approved, "Reopened": reopened, "Rejected": rejected};
                        var statusData = JSON.parse(JSON.stringify(self.statusData));
                        for(var i in statusData) {
                            taxReliefData.push( {
                                type : 'tax-relief',
                                status : statusData[i].status,
                                empCount : statusCountValue[statusData[i].status],
                                chosenEmployeeIds : [],
                                iconColor : statusData[i].iconColor,
                                iconName : statusData[i].iconName,
                            });
                        }
                        self.taxReliefStatistics = taxReliefData;
                        self.loadTaxReliefStatistics = false;
                    }
                    else {
                        self.loadTaxReliefStatistics = false;
                    }
                })
                .catch(function (taxReliedError) {
                    self.loadTaxReliefStatistics = false;
                    self.handleError(taxReliedError,1);
                });
            }
            catch {
                self.loadTaxReliefStatistics = false;
            }
        } else {
            self.loadTaxReliefStatistics = false;        
        }
    },

    // function to show the emp list popup
    retrieveEmpDeclaration(chosenData, type = "") {
        this.chosenStatus = chosenData.status;
        this.chosenEmployeeIds = chosenData.chosenEmployeeIds;
        this.chosenType = chosenData.type;
        // only show the popup while the count of records is greater than zero
        if (chosenData.empCount > 0) {
            if (type === "tax-relief") {
                window.location.href = this.baseUrl + `v3/tax-and-statutory-compliance/tax-relief?assessMentYear=${this.selectedAssessmentYear}&status=${this.chosenStatus}`;
            } else {
                this.$emit('show-hide-statistics', false);
                // reset selected filter
                this.selectedDesignation = []; this.selectedDepartment = []; this.selectedEmpType = []; this.selectedLocation = []; this.selectedWorkSchedule = [];
                this.fnRetrieveEmployees('fromStatisticsCard');
            }
        }
    },

    addTaxRelief(type) {
        if (type === "bulk") {
            window.location.href = this.baseUrl + `v3/tax-and-statutory-compliance/tax-relief?assessMentYear=${this.selectedAssessmentYear}&status=bulk`;
        } else {
            window.location.href = this.baseUrl + `v3/tax-and-statutory-compliance/tax-relief?assessMentYear=${this.selectedAssessmentYear}`;
        }
    },

    // open Mail notification popup
    fnNotify(chosenData) {
        this.chosenStatus = chosenData.status;
        this.chosenEmployeeIds = chosenData.chosenEmployeeIds;
        this.chosenType = chosenData.type;
        if(this.chosenEmployeeIds.length > 0){
            this.openNotifyModal = true;
            this.openCloseViewModal(true);
        }

    },

    handleWarningMessages(value) {
        this.$emit('handle-warning-msg',value);
    },
        
    // open/close the modal -> to change the top bar class
    handleSuccessMessages(approvalSuccess) {
        this.$emit('handle-success', approvalSuccess);
        this.listPropertyDetails();
    },

    // to fetch dropdown details to show employees list popup
    fetchDropDownData() {
        var self = this;
        // dropdown query
        var fetchDropdownDetails = self.atsGraphql.query(`samplequery { getDropDownBoxDetails { errorCode message designations { Designation_Id Designation_Name } departments { Department_Id Department_Name} workSchedules { WorkSchedule_Id Title} locations { Location_Id Location_Name} employeeType { EmpType_Id Employee_Type}}}`);
        fetchDropdownDetails().then(dropDownData => {
            self.departmentList = dropDownData.getDropDownBoxDetails.departments;
            self.designationList = dropDownData.getDropDownBoxDetails.designations;
            self.locationList = dropDownData.getDropDownBoxDetails.locations;
            self.empTypeList = dropDownData.getDropDownBoxDetails.employeeType;
            self.workScheduleList = dropDownData.getDropDownBoxDetails.workSchedules;
        }).catch(dropdownDataErr => {
            self.departmentList = [], self.designationList = [], self.locationList = [], self.empTypeList = [], self.workScheduleList = [];
        })
    },
    
    // retrieve employees based on scenarios and list it in popup
    fnRetrieveEmployees(from){
        var self = this;
        if(from !== 'fromFilter')
            self.loadingInPOI = true;
        try {
            // dropdown query
            var fetchEmployees = self.taxDeclarationGraphql.query(`getEmployeesDetails ($approvalStatus:String,$assessmentYear:Int,$sourceType:String!,$employeeIdArray:[Int],$designation:[Int],$department:[Int],$employeeType:[Int],$workSchedule:[Int],$location:[Int]) { getEmployeesDetails (approvalStatus:$approvalStatus,assessmentYear:$assessmentYear,sourceType:$sourceType,employeeIdArray:$employeeIdArray,designation: $designation,department:$department,employeeType:$employeeType,workSchedule:$workSchedule,location:$location) { errorCode message employeeDetails}}`);
            fetchEmployees({
                "approvalStatus": self.chosenStatus,
                "assessmentYear" :self.selectedAssessmentYear,
                "sourceType": self.chosenType,
                "employeeIdArray": self.chosenEmployeeIds,
                "designation" : self.selectedDesignation,
                "department" : self.selectedDepartment,
                "employeeType" : self.selectedEmpType,
                "workSchedule" : self.selectedWorkSchedule,
                "location" : self.selectedLocation
            }).then(empListRes => {
                self.empModalTitle = 'Income Under Section24 ' + self.chosenStatus + ' Employees';
                if(empListRes.getEmployeesDetails.employeeDetails)
                    self.employeesList = JSON.parse(empListRes.getEmployeesDetails.employeeDetails);
                // stop loading child component using refs
                if(self.$refs.empListModal)
                    self.$refs.empListModal.isLoading = false;
                self.showEmpModal = true;
                self.loadingInPOI = false;
                self.openCloseViewModal(true);
            }).catch(empListErr => {
                self.fnCancelEmpListModal(true);
                self.handleError(empListErr);
                self.loadingInPOI = false;
            })
        } catch (empListErr) {
            self.fnCancelEmpListModal(true);
            self.handleError(empListErr);
            self.loadingInPOI = false;
        }

    },
    fnChangeEmployee(chooseEmpParam) {
        this.chosenStatus = chooseEmpParam[0];
        this.chosenEmployeeIds = chooseEmpParam[1];
        this.chosenType = chooseEmpParam[2];
        this.fnRetrieveEmployees('fromView') 
    },
    // function called when filter is applied in employees list popup
    fnApplyingFilterInEmpListPopup(filterParam) {
        this.selectedDepartment = filterParam[0],
        this.selectedDesignation = filterParam[1],
        this.selectedEmpType = filterParam[2],
        this.selectedLocation = filterParam[3],
        this.selectedWorkSchedule = filterParam[4];
        this.fnRetrieveEmployees('fromFilter');
    },
    // click proceed button in employees selection modal
    fnProceedInEmpListModal(selectedEmp) {
        this.selectedEmpId = selectedEmp[0].employee_id;
        this.fnCancelEmpListModal(false);
        this.listPropertyDetails();
    },
    // click on close btn in employees list popup
    fnCancelEmpListModal(showFilter) {
        this.showEmpModal = false;
        this.openCloseViewModal(false);
        let styleElements = document.createElement('style');
        styleElements.textContent = `
            .v-data-table table {
                padding: 0 2px !important;
            }
            .v-data-table{
                background: #f1f1f1 !important;
            }
            .v-data-table table{
                background: #f1f1f1;
            }
            .v-data-table tbody tr{
                box-shadow: none !important;
            }
            .v-data-table table {
                background: rgb(241, 241, 241) !important;
            }
            .v-data-table tbody .v-data-table__mobile-table-row td:nth-child(2) {
                justify-content: space-between;;
            }
            .v-data-table tbody .v-data-table__mobile-table-row td:nth-child(3) {
                justify-content: space-between;;
            }
            .v-data-table tbody td:nth-child(2) .v-data-table__mobile-row__header {
                    display: flex;
            }
            .v-data-table tbody td:nth-child(3) .v-data-table__mobile-row__header {
                    display: flex
            }
            @media (max-width: 600px) {
                .v-data-table thead tr:last-child th {
                    display: flex !important;
                }
            }
        `;
        document.head.appendChild(styleElements);
        // show assessment year when the modal is closed and the page is not listPage
        if(!this.showListData && this.formSource === 'POI')
            this.$emit('show-filter', showFilter);
    }
},


});
