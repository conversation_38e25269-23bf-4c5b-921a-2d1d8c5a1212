<?php
class Default_Form_Payment extends Zend_Form
{
	public function init()
	{
		$formDecoration = array('FormElements', array(array('data'=>'HtmlTag'), array('tag'=>'table', 'class'=>'table_msg1')), 'Form');

		$liDecorationA = array('ViewHelper', array('Description', array('escape' => false, 'tag' => false), array('placement' => Zend_Form_Decorator_Abstract::APPEND, 'tag' => 'em')), array(array('data'=>'HtmlTag'), array('tag' => 'td','style'=>'padding-left:25%;')),
				array(array('row'=>'HtmlTag'), array('tag'=>'tr','openOnly'=>true)));
		$liDecorationB = array('ViewHelper',array('Description', array('escape' => false, 'tag' => false), array('placement' => Zend_Form_Decorator_Abstract::APPEND, 'tag' => 'em')), array(array('data'=>'HtmlTag'), array('tag' => 'td')),
				array(array('row'=>'HtmlTag'), array('tag'=>'tr','closeOnly'=>true)));
			
		$this->setAttribs(array('id'=>'paymentForm', 'class'=>'hrapp_forms hrapp_validation','style'=>'display:none; background-color:aliceblue;height:1500px;'));

		$labelInvoiceNo = new Zend_Form_Element_Text('labelInvoiceNo');
		$labelInvoiceNo->setOrder(2)->setValue('Invoice No  :')->setDecorators($liDecorationA);
		$invoiceNo = new Zend_Form_Element_Text('Invoice_No');
		$invoiceNo->setOrder(3)->setDecorators($liDecorationB);
		
		$labelAmount = new Zend_Form_Element_Text('labelAmount');
		$labelAmount->setOrder(4)->setValue('Amount :')->setDecorators($liDecorationA);
		$totalAmount = new Zend_Form_Element_Text('Total_Amount');
		$totalAmount->setOrder(5)->setDecorators($liDecorationB);
		
		$labelUrl = new Zend_Form_Element_Text('labelUrl');
		$labelUrl->setOrder(6)->setValue('Organization Url :')->setDecorators($liDecorationA);
		$orgazinationUrl = new Zend_Form_Element_Text('Organization_Url');
		$orgazinationUrl ->setOrder(7)->setDecorators($liDecorationB);
		
		$labelcustomerName = new Zend_Form_Element_Text('labelCustomerName');
		$labelcustomerName->setOrder(8)->setValue('Customer Name :')->setDecorators($liDecorationA);
		$customerName = new Zend_Form_Element_Text('Customer_Name');
		$customerName ->setOrder(9)->setDecorators($liDecorationB);
		
		$labelBillingDate = new Zend_Form_Element_Text('billingDate');
		$labelBillingDate->setOrder(10)->setValue('Billing Date :')->setDecorators($liDecorationA);
		$billingDate = new Zend_Form_Element_Text('Billing_Date');
		$billingDate ->setOrder(11)->setDecorators($liDecorationB);
		
		$merchantId =  new Zend_Form_Element_Hidden('merchant_id');
		$merchantId->setValue('54678');
		
		$orderId =  new Zend_Form_Element_Hidden('order_id');
		$orderId->addFilters(array('StringTrim', 'StripTags', 'Int'))
		->addValidator('Int');
		
		$amount =  new Zend_Form_Element_Hidden('amount');
		
		$currency =  new Zend_Form_Element_Hidden('currency');
		$currency->setValue('INR')
		->setAttrib('id','currency');
		
		$redirectUrl =  new Zend_Form_Element_Hidden('redirect_url');
		$redirectUrl->setValue('');
		
		$cancelUrl =  new Zend_Form_Element_Hidden('cancel_url');
		$cancelUrl->setValue('');
		
		$language =  new Zend_Form_Element_Hidden('language');
		$language->setValue('EN');
		
		$billingName      =  new Zend_Form_Element_Hidden('billing_name');
		
		$billingAddress   =  new Zend_Form_Element_Hidden('billing_address');
		
		$billingCity 	  =  new Zend_Form_Element_Hidden('billing_city');
		
		$billingState 	  =  new Zend_Form_Element_Hidden('billing_state');
		
		$billingZip 	  =  new Zend_Form_Element_Hidden('billing_zip');
		
		$billingCountry   =  new Zend_Form_Element_Hidden('billing_country');
		
		$billingTelephone = new Zend_Form_Element_Hidden('billing_tel');
		
		$billingEmail 	  = new Zend_Form_Element_Hidden('billing_email');
		
		
		$rateSubmit = new Zend_Form_Element_Submit('Pay');
	
		$paymentHeading = new Zend_Form_Element_Text('paymentHeading');
		$paymentHeading ->setOrder(0)
		->setValue('Payment Details')
		->setDecorators(array('ViewHelper', array('Description', array('escape' => false, 'tag' => false), array('placement' => Zend_Form_Decorator_Abstract::APPEND, 'tag' => 'em')), array(array('data'=>'HtmlTag'), array('tag' => 'td', 'colspan'=>'2','style'=>'text-align:center;font-weight:bold;')), array(array('row'=>'HtmlTag'), array('tag'=>'tr'))));
			

		$rateSubmit->setOrder(12)->setLabel('Pay')->setAttrib('style','width:120px;')
		->setDecorators(array('ViewHelper', array('Description', array('escape' => false, 'tag' => false), array('placement' => Zend_Form_Decorator_Abstract::APPEND, 'tag' => 'em')), array(array('data'=>'HtmlTag'), array('tag' => 'td', 'colspan'=>2,'style'=>'text-align:center;width:40%;')),
				array(array('row'=>'HtmlTag'), array('tag'=>'tr'))));
		$this->addElements(array($paymentHeading,$labelInvoiceNo,$invoiceNo,$labelAmount,$totalAmount,$labelUrl,$orgazinationUrl,$labelcustomerName,$customerName,$labelBillingDate,$billingDate,
				$merchantId,$orderId,$currency,$amount,$redirectUrl,$cancelUrl,$language,$billingName,$billingAddress,$billingCity,$billingState,$billingZip,$billingCountry,$billingTelephone,$billingEmail,$rateSubmit))->setDecorators($formDecoration);

	}

}

?>