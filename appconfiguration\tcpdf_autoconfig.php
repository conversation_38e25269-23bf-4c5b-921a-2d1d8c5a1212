<?php
//============================================================+
// File name   : custom_config.php
// Begin       : 2017-10-04
// Last Update : 2017-10-04
//
// Description : Configuration file for TCPDF.
// Author      : Jayant<PERSON> R
// -------------------------------------------------------------------
// The file custom_config.php is customized from tcpdf_config.php.
// It is customized according to the requirements needed for HRAPP.
// Main customization done is, the header logo is fetched from s3 bucket.
// (ie) The external link for the logo is used rather than the logo image
// from the project. 
//============================================================+


    // Automatic calculation for the following K_PATH_MAIN constant
	$k_path_main = str_replace( '\\', '/', realpath(substr(dirname(__FILE__), 0, 0-strlen('config'))));
	if (substr($k_path_main, -1) != '/') {
		$k_path_main .= '/';
	}

	/**
	 * Installation path (/var/www/tcpdf/).
	 * By default it is automatically calculated but you can also set it as a fixed string to improve performances.
	 */
	define ('K_PATH_MAIN', $k_path_main);
    
    define('K_PATH_IMAGES', '');
                        
    define('K_CELL_HEIGHT_RATIO', 1.25);
    
    define('PDF_PAGE_ORIENTATION', 'P');
    
    define('PDF_UNIT', 'mm');
    
    define('PDF_PAGE_FORMAT', 'A4');
    
    define('PDF_CREATOR', '');
    
    define('PDF_FONT_NAME_MAIN', 'helvetica');
    
    define('PDF_FONT_SIZE_MAIN', 13);
    
    define('PDF_FONT_NAME_DATA', 'helvetica');
    
    define('PDF_FONT_SIZE_DATA', 8);
    
    define('PDF_FONT_MONOSPACED', 'courier');
    
    define('PDF_MARGIN_LEFT', 12);
    
    define('PDF_MARGIN_TOP', 27);
    
    define('PDF_MARGIN_RIGHT', 12);
    
    define('PDF_MARGIN_HEADER', 5);
    
    define('PDF_MARGIN_FOOTER', 10);
    
    define('PDF_MARGIN_BOTTOM', 25);
    
    define('PDF_IMAGE_SCALE_RATIO', 1.25);
    
    define('K_PATH_CACHE', K_PATH_MAIN.'cache/');
    
    define('K_BLANK_IMAGE', K_PATH_IMAGES.'_blank.png');
						
 
 ?>
