<?php
//===========================================================================================
//===========================================================================================
/* Program : Attendance.php											   			           *
 * Property of Caprice Technologies Pvt Ltd,                                               *
 * Copyright (c) 2013 - 2015 Caprice Technologies Pvt Ltd,                                 *
 * Coimbatore, Tamilnadu, India.														   *
 * All Rights Reserved.            														   *
 * Use of this material without the express consent of Caprice Technologies                *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law.   *
 *                                                                                    	   *
 * Description : MQL Query to retrive, add, update attendance details.                     *
 *                                                                                   	   *
 *                                                                                    	   *
 * Revisions :                                                                    	       *
 *    Version         Date           Author                  Description                   *
 *      1.0        08-April-2015    Prasanth               Initial Version        	       *
 *                                                                                         */
//===========================================================================================
//===========================================================================================

class Employees_Model_DbTable_Rfidattendance extends Zend_Db_Table_Abstract
{

    protected $_db = null;
    protected $_ehrTables = null;
    protected $_dbAttendance = null;
    
    public function init ()
    {
        $this->_ehrTables = new Application_Model_DbTable_Ehr();
        $this->_db = Zend_Registry::get('subHrapp');
        $this->_dbAttendance = new Employees_Model_DbTable_Attendance();
		$this->_dbCommonFunction  = new Application_Model_DbTable_CommonFunction();
    }
    
	/**
	 *	getSchemaId function used to get Bio Attendance Schema Id.
	*/
	public function getSchemaId ()
	{
		return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendanceHeader, array('Schema_Id'))
									->where('Schema_Name = ?', 'Bio Attendance'));
	}
	
    /**
     *  updateAttendance function used to update attendance.
    */
    public function updateAttendance ($orgId, $machineId, $updateData, $punchingType=null)
    {
		$date = date('Y-m-d');
		$time = date('H:i:s');
		$addcount = $updateCount = 0;
		$added = array();
		$updated = array();
		$error = $success = 0;
		
		//for bio attendance update using api
		if($punchingType!=null && $punchingType == 'api'){
			$succesArray = array();
			array_push($succesArray,0);
		} else if($punchingType!=null && $punchingType == 'device'){
			$succesArray = array('FailedRecords'=>array());
		}
		
		if (!empty($orgId) && !empty($machineId))
		{
			$schemaId = $this->getSchemaId();
			
			foreach($updateData as $key => $row)
			{				
				$exist = '';
				
				$exist = $this->_db->fetchRow($this->_db->select()->from(array('J'=>$this->_ehrTables->empJob), array('J.Employee_Id','J.External_EmpId','J.Manager_Id'))
												->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id=J.Employee_Id',
															array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name, ' ', P.Emp_Last_Name)")))
												->joinInner(array('ET'=>$this->_ehrTables->empType),'ET.EmpType_Id=J.EmpType_Id',array('Exclude_Break_Hours'))
												->where('J.External_EmpId = ? ', $row['ExternalEmpId']));
												
				if (!empty($exist))
				{
					$empId     = $exist['External_EmpId'];
					$empName   = $exist['Employee_Name'];
					$managerId = $exist['Manager_Id'];
					$breakHours = $exist['Exclude_Break_Hours'];
					$addedBy    = $exist['Employee_Id'];
					if($punchingType==null){
						$dateArr   = str_split($row['DOT'], 2);
						$punchDate = $dateArr[2].$dateArr[3].'-'.$dateArr[1].'-'.$dateArr[0];
						$punchPreviousDate = $dateArr[2].$dateArr[3].'-'.$dateArr[1].'-'.($dateArr[0] - 1);
						$punchTime = $dateArr[4].':'.$dateArr[5].':00';
						
						$punchDateTime = $punchDate. ' ' .$punchTime;
						$punchTypeArr = str_split(($dateArr[6]), 1);
					} else {
						$punchDateTime = $row['DOT'];
						$punchType 	   = $row['Status'];
						$punchTypeArr  = str_split(($punchType), 1);
					}

					$importData = array('Schema_Id'           => $schemaId,
										'Employee_Id'         => $empId,
										'Employee_Name'       => $empName,
										'Exclude_Break_Hours' => $breakHours,
										'Data_Source'		  =>'Biometric',
										'Added_On'            => $punchDateTime);
					
					
					switch ($punchTypeArr[1]){
						case 1 :

							$importData['Attendance_Status'] = 'C/In';
							$existAttImp = $this->_dbCommonFunction->attendanceImportAlreadyExist($importData);
							
							if (empty($existAttImp))
							{
								$importData['Added_On_Date_Time'] = date('Y-m-d H:i:s');
								$importData['Added_By']           = $addedBy;
								$inserted = $this->_db->insert($this->_ehrTables->attendanceImport, $importData);
								
								if ($inserted)
								{
									++$success;
									//for bio attendance update using api
								}
								else
								{
									++$error;
									if($punchingType!=null && $punchingType == 'api'){
										array_push($succesArray,$row['id']);
									} else if($punchingType!=null && $punchingType == 'device'){
										$responseArray = array('EmployeeId'=> $row['ExternalEmpId'],
															   'PunchTime'=> $row['DOT'],
															   'Status'=> $row['Status']);
										array_push($succesArray['FailedRecords'],$responseArray);
									}
									$errorData = array('Error_String' => $row['Request_String'],
													   'Comments'     => 'Unable to insert attendance data',
													   'Added_On'     => date('Y-m-d H:i:s'));
									
									$this->_db->insert($this->_ehrTables->biometricErrorLog, $errorData);
								}
							}
							else
							{
								++$error;
								$errorData = array('Error_String' => $row['Request_String'],
												   'Comments'     => $empName.' have already check in',
												   'Added_On'     => date('Y-m-d H:i:s'));
								
								$this->_db->insert($this->_ehrTables->biometricErrorLog, $errorData);
							}
							break;
						
						case 2 :
							$importData['Attendance_Status'] = 'C/Out';
							$existAttImp = $this->_dbCommonFunction->attendanceImportAlreadyExist($importData);
							
							if (empty($existAttImp))
							{
								$importData['Added_On_Date_Time'] = date('Y-m-d H:i:s');	
								$importData['Added_By']           = $addedBy;
								$updated = $this->_db->insert($this->_ehrTables->attendanceImport, $importData);
								
								if ($updated)
								{
									++$success;
								}
								else
								{
									++$error;
									if($punchingType!=null && $punchingType == 'api'){
										array_push($succesArray,$row['id']);
									} else if($punchingType!=null && $punchingType == 'device'){
										$responseArray = array('EmployeeId'=> $row['ExternalEmpId'],
															   'PunchTime'=> $row['DOT'],
															   'Status'=> $row['Status']);
										array_push($succesArray['FailedRecords'],$responseArray);
									}
									$errorData = array('Error_String' => $row['Request_String'],
													   'Comments'     => 'Unable to insert attendance data',
													   'Added_On'     => date('Y-m-d H:i:s'));
									
									$this->_db->insert($this->_ehrTables->biometricErrorLog, $errorData);
								}
							}
							else
							{
								++$error;
								$errorData = array('Error_String' => $row['Request_String'],
												   'Comments'     => $empName.' have already check out',
												   'Added_On'     => date('Y-m-d H:i:s'));
								
								$this->_db->insert($this->_ehrTables->biometricErrorLog, $errorData);
							}
							break;
						
						default :
							$importData['Attendance_Status'] = 'Err';
							
							$existAttImp = $this->_dbCommonFunction->attendanceImportAlreadyExist($importData);
							
							if (empty($existAttImp))
							{
								$importData['Added_On_Date_Time'] = date('Y-m-d H:i:s');
								$importData['Added_By']           = $addedBy;
								$updated = $this->_db->insert($this->_ehrTables->attendanceImport, $importData);
								if ($updated)
								{
									++$success;
								}
								else
								{
									++$error;
									//for bio attendance update using api
									if($punchingType!=null && $punchingType == 'api'){
										array_push($succesArray,$row['id']);
									} else if($punchingType!=null && $punchingType == 'device'){
										$responseArray = array('EmployeeId'=> $row['ExternalEmpId'],
																'PunchTime'=> $row['DOT'],
																'Status'=> $row['Status']);
										array_push($succesArray['FailedRecords'],$responseArray);
									}
									$errorData = array('Error_String' => $row['Request_String'],
													'Comments'     => 'Unable to update',
													'Added_On'     => date('Y-m-d H:i:s'));
									
									$this->_db->insert($this->_ehrTables->biometricErrorLog, $errorData);
								}
							}
							break;
					}
				}
				else
				{
					++$error;
					if($punchingType!=null && $punchingType == 'api'){
						array_push($succesArray,$row['id']);
					} else if($punchingType!=null && $punchingType == 'device'){
						$responseArray = array('EmployeeId'=> $row['ExternalEmpId'],
												'PunchTime'=> $row['DOT'],
												'Status'=> $row['Status']);
						array_push($succesArray['FailedRecords'],$responseArray);
					}
					$errorData = array('Error_String' => $row['Request_String'],
									   'Comments'     => 'No match for this biometric id',
									   'Added_On'     => date('Y-m-d H:i:s'));
					
					$this->_db->insert($this->_ehrTables->biometricErrorLog, $errorData);
				}
			}
		}
		else
		{
			++$error;
			if($punchingType == 'api'){
				foreach($updateData as $key => $row)
				{
					array_push($succesArray,$row['id']);
				}
			} else if($punchingType == 'device'){
				foreach($updateData as $key => $row)
				{
					$responseArray = array('EmployeeId'=> $row['ExternalEmpId'],
										'PunchTime'=> $row['DOT'],
										'Status'=> $row['Status']);
					array_push($succesArray['FailedRecords'],$responseArray);
				}
			}
			$errorData = array('Error_String' => $_SERVER['REDIRECT_URL'].'?'.$_SERVER['QUERY_STRING'],
							   'Comments'     => 'Invalid data',
							   'Added_On'     => date('Y-m-d H:i:s'));
			
			$this->_db->insert($this->_ehrTables->biometricErrorLog, $errorData);
		}
		
		$cnt = $error + $success;
		 
		if ($cnt > 1)
		{
			//for bio attendance update using api
			if($punchingType==null){
			
				if ($success > 0 && $error == 0)
				{
					return $success.' record(s) successfully Updated';
				}
				else if ($success == 0 && $error > 0)
				{
					return $error.' record(s) Unable to Update';
				}
				else if ($success > 0 && $error > 0)
				{
					return $success.' record(s) successfully Updated and '. $error .' record(s) Unable to Update';
				}
			} else{
				///for bio attendance update using api
				return $succesArray;
			}
		}
		else
		{
			//for bio attendance update using api
			if($punchingType==null){
				if ($success)
					return 'Successfully Updated';
				else
					return 'Unable to Update';
			} else{
				//for bio attendance update using api
				return $succesArray;
			}
			
		}
    }
	public function getEmployeeId($employeeId)
    {
		return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
									->where('External_EmpId = ?', $employeeId));
	}
    
    public function updateCamAttendance($camAttendance)
    {
        $schemaId = $this->getSchemaId();
    	$employeeId = $this->getEmployeeId($camAttendance['Employee_Id']);  
        
        if($camAttendance['Attendance_Status']=='in' || $camAttendance['Attendance_Status']=='ot-in')
        {
            $camAttendance['Attendance_Status'] = 'C/In';
        }
        else
        {
            $camAttendance['Attendance_Status'] = 'C/Out';
        }
        
        $employeeName = $this->getEmployeeName($employeeId);

		$existAttImp = $this->_dbCommonFunction->attendanceImportAlreadyExist($camAttendance);
							
        if(empty($existAttImp))
        {
            $insertData = array('Schema_Id'          => $schemaId,
                                'Employee_Id'       => $camAttendance['Employee_Id'],
                                'Employee_Name'     => $employeeName,
								'Attendance_Status' => $camAttendance['Attendance_Status'],
                                'Added_On'          => $camAttendance['Added_On']);

			 $insertData['Added_On_Date_Time'] = date('Y-m-d H:i:s');
			 $insertData['Added_By']           = $employeeId;
		     $inserted = $this->_db->insert($this->_ehrTables->attendanceImport, $insertData);   
        }
        else
        {
              $errorData = array('Error_String' => $camAttendance['Request_String'],
                                'Comments'     => $employeeName.' have already check'.$camAttendance['Attendance_Status'],
                                'Added_On'     => date('Y-m-d H:i:s'));
              
              $updated  = $this->_db->insert($this->_ehrTables->biometricErrorLog, $errorData);
        }
        
        if(!empty($inserted))
        {
            return 'ok';
        }
        else
        {
            return 'failure';   
        }
    }
    
    public function getEmployeeName($employeeId)
    {
        $employeeName = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empPersonal,
                array('Employee_Name'=>new Zend_Db_Expr("CONCAT(Emp_First_Name,' ',Emp_Last_Name)")))
                                ->where('Employee_Id = ?', $employeeId));
        return $employeeName;
    }

	public function __destruct()
    {
        
    }	

}