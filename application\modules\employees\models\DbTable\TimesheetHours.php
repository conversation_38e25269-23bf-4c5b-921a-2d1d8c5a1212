<?php
//=========================================================================================
//=========================================================================================
/* Program : TimesheetHours.php										   			         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MQL Query to retrive, add, update timesheet hours					     *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Shobana            	  Initial Version        	         *
 *  0.2        16-May-2014    Mahesh                  Modified Function					 *
 *  												  1.deleteTimesheetHours             *
 *  0.3		   18-Sep-2014    Mahesh				  Added break hours in search,add	 *
 *													  update and view function           *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Saranya                 Changes in file for mobile app     *
 *                                                    1.Extra fields are added in        *
 *                                                    field list of list query.          */
//=========================================================================================
//=========================================================================================
class Employees_Model_DbTable_TimesheetHours extends Zend_Db_Table_Abstract
{
    protected $_dbPersonal = null;
    protected $_dbGrade = null;
    protected $_orgDF = null;
    protected $_db = null;
    protected $_ehrTables = null;
    protected $_formNameA = 'Organization Account';
    protected $_formNameB = 'Timesheet Hours';
    protected $_formNameC = 'Account Schema';
    protected $_commonFunction = null;
    public function init()
    {
        $this->_ehrTables   = new Application_Model_DbTable_Ehr();
        $this->_db          = Zend_Registry::get('subHrapp');
        $this->_dbPersonal  = new Employees_Model_DbTable_Personal();
        $this->_dbGrade     = new Employees_Model_DbTable_Grade();
        $this->_orgDF       = $this->_ehrTables->orgDateformat();
	$this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
	$this->_commonFunction = new Application_Model_DbTable_CommonFunction();
    }
	
	/**
	 * Get regular hours by gradeId
	 */
    public function getRegularHrs($gradeId)
    {
        $hrsQry=$this->_db->select()->from(array('hrs'=>$this->_ehrTables->timesheetHrs),array('hrs.Regular_Hours'))
        ->where('hrs.Grade_Id = ?', $gradeId);
        $regHrs=$this->_db->fetchOne($hrsQry);
        return $regHrs;
    }
	
//    //to list and search timesheethours
//    public function searchTimesheetHours($grade, $regularCondition, $regular, $overTimeCondition, $overTime, $breakCondition,
//										 $breakHrs, $totalCondition, $total, $page, $rows, $sortField, $sortOrder)
//    {
//        switch ($sortField)
//		{
//            case 'Grade_Id':
//                $sortField = "hours.Grade_Id";
//                break;
//			
//            default:
//                $sortField = $sortField;
//				break;
//        }
//		
//        $hrsQry = $this->_db->select()
//        ->from(array('hours'=>$this->_ehrTables->timesheetHrs),
//        array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS hours.Grade_Id as count'),'hours.Timesheet_Id','hours.Grade_Id',
//    					'hours.Regular_Hours','hours.OverTime_Hours','hours.Break_Hours','hours.Total_Hours', 'hours.Description',
//						'hours.Added_Date as Added_On', 'hours.Modified_Date as Updated_On'))
//
//        ->joinInner(array('grade'=>$this->_ehrTables->empGrade),'grade.Grade_Id=hours.Grade_Id',array('grade.Grade'))
//		
//		->joinLeft(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=hours.Updated_By',
//			array(new Zend_Db_Expr("CONCAT(Emp_First_Name, ' ', Emp_Last_Name) as Updated_By_Name")))
//		
//        ->order("$sortField $sortOrder")
//        ->limit($rows,($page-1)*$rows);
//
//        if (! empty($regular) && preg_match('/^[0-9]/', $regular) && !empty($regularCondition))
//        {
//            $hrsQry->where('hours.Regular_Hours'.$regularCondition.' ?',$regular);
//        }
//
//        if (! empty($overTime) && preg_match('/^[0-9]/', $overTime) && !empty($overTimeCondition))
//        {
//            $hrsQry->where('hours.OverTime_Hours'.$overTimeCondition.'?',$overTime);
//        }
//        
//        if ($breakHrs!='' && preg_match('/^[0-9]/', $breakHrs) && !empty($breakCondition))
//        {
//        	$hrsQry->where('hours.Break_Hours'.$breakCondition.'?',$breakHrs);
//        }
//
//        if (! empty($total) && preg_match('/^[0-9]/', $total) && !empty($totalCondition))
//        {
//            $hrsQry->where('hours.Total_Hours'.$totalCondition.'?',$total);
//        }
//
//        if (! empty($grade) && preg_match('/^[0-9]/', $grade))
//        {
//            $hrsQry->where('hours.Grade_Id = ?',$grade);
//        }
//		
//        $hrsRes   = $this->_db->fetchAll($hrsQry);
//		
//		$countHrs = $this->_db->fetchOne('select FOUND_ROWS()');
//		
//		$timesheethrs=array("total"=>$countHrs,"rows"=>$hrsRes);
//		
//        return Zend_Json::encode($timesheethrs);
//    }

    public function searchTimesheetHours($page,$rows,$sortField,$sortOrder,$logEmpId,$searchAll=null,$gradeId,$regularHours,$overTimeHours,$breakHours)
    {
        switch ($sortField)
        {
			case 1: $sortField = 'EG.Grade'; break;
			case 2: $sortField = 'TH.Regular_Hours'; break;
			case 3: $sortField = 'TH.OverTime_Hours'; break;
			case 4: $sortField = 'TH.Break_Hours'; break;
			default:
				$sortField = 'TH.Grade_Id'; $sortOrder = 'asc'; break;
        }
		
       $qryTimesheetHours = $this->_db->select()->from(array('TH'=>$this->_ehrTables->timesheetHrs),
				        array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS TH.Timesheet_Id as count'),'TH.Timesheet_Id','TH.Grade_Id',
				    			       'TH.Regular_Hours','TH.OverTime_Hours','TH.Break_Hours','TH.Description',
							        new Zend_Db_Expr("DATE_FORMAT(TH.Added_On,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
									new Zend_Db_Expr("DATE_FORMAT(TH.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On"),
							       'DT_RowClass' => new Zend_Db_Expr('"thours"'),
                                                               'DT_RowId' => new Zend_Db_Expr("CONCAT('row_',TH.Timesheet_Id)")))
				
				        ->joinInner(array('EG'=>$this->_ehrTables->empGrade),'EG.Grade_Id=TH.Grade_Id',array('EG.Grade'))
						
					->joinLeft(array('EP'=>$this->_ehrTables->empPersonal),'EP.Employee_Id=TH.Updated_By',
							array(new Zend_Db_Expr("CONCAT(EP.Emp_First_Name, ' ',EP.Emp_Last_Name) as Updated_By")))
					
					->joinLeft(array('EP1'=>$this->_ehrTables->empPersonal),'EP1.Employee_Id=TH.Added_By',
							array(new Zend_Db_Expr("CONCAT(EP1.Emp_First_Name, ' ',EP1.Emp_Last_Name) as Added_By")))
						
					->order("$sortField $sortOrder")
					->limit($rows, $page);

		if (!empty($searchAll) && $searchAll != null)
		{
			//$qryTimesheetHours->where('EG.Grade Like ?', "%$searchAll%")
			//		->orwhere('TH.Regular_Hours Like ?', "%$searchAll%")
			//		->orwhere('TH.Overtime_Hours Like ?', "%$searchAll%")
			//		->orwhere('TH.Break_Hours Like ?', "%$searchAll%");
					
			$conditions  = $this->_db->quoteInto('EG.Grade Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or TH.Regular_Hours Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or TH.Overtime_Hours Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or TH.Break_Hours Like ?', "%$searchAll%");
			
			$qryTimesheetHours->where($conditions);
		}
	
                if (!empty($gradeId))
		{
			$qryTimesheetHours->where($this->_db->quoteInto('TH.Grade_Id = ?',$gradeId));
		}
                if (!empty($regularHours))
		{
			$qryTimesheetHours->where($this->_db->quoteInto('TH.Regular_Hours = ?',$regularHours));
		}
                
                if (!empty($overTimeHours))
		{
			$qryTimesheetHours->where($this->_db->quoteInto('TH.Overtime_Hours = ?',$overTimeHours));
		}
		
		 if (!empty($breakHours))
		{
			$qryTimesheetHours->where($this->_db->quoteInto('TH.Break_Hours = ?',$breakHours));
		}
		
		$timesheetHoursDetails = $this->_db->fetchAll($qryTimesheetHours);
                
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
                
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->timesheetHrs, new Zend_Db_Expr('COUNT(Timesheet_Id)')));
                
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $timesheetHoursDetails);
    }

	
    //fetch timesheethours based on the hoursid
    public function viewTimesheetHours($gradeId)
    {
        $hrsQry = $this->_db->select()
    				->from(array('hours'=>$this->_ehrTables->timesheetHrs),
        array('hours.Regular_Hours','hours.OverTime_Hours', 'hours.Break_Hours', 'hours.Total_Hours', 'hours.Timesheet_Id',
		    					'hours.Description','hours.Grade_Id','hours.Updated_By',
        new Zend_Db_Expr("DATE_FORMAT(hours.Added_Date,'".$this->_orgDF['sql']." at %T') as Added_Date"),
        new Zend_Db_Expr("DATE_FORMAT(hours.Modified_Date,'".$this->_orgDF['sql']." at %T') as Modified_Date")))
        ->joinInner(array('grade'=>$this->_ehrTables->empGrade),'grade.Grade_Id=hours.Grade_Id',array('grade.Grade'))
        ->joinInner(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=hours.Updated_By',
        array(new Zend_Db_Expr("CONCAT(Emp_First_Name, ' ', Emp_Last_Name) as EmployeeName")))
        ->where('hours.Grade_Id = ?',$gradeId);
        $hrs=$this->_db->fetchRow($hrsQry);

        if ($hrs == null) {
             
            return null;
             
        } else {
             
            return $hrs;
        }
    }
    
    //version : 0.2 => if any employee is under timesheet grade, then we are not allow to delete 
    //                 because for adding attendance and leave,and for payslip calculation timesheet hours needed
    
    public function deleteTimesheetHours($timesheetId, $logEmpId, $formName)
    {
	 $gradeId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->timesheetHrs,'Grade_Id')
		     ->where('Timesheet_Id = ?', $timesheetId));
	  $getEmployeeCount = $this->_db->fetchOne($this->_db->select()
													 ->from(array('J'=>$this->_ehrTables->empJob),
															array(new Zend_Db_Expr('Count(P.Employee_Id)')))
													 
													 ->joinInner(array('P'=>$this->_ehrTables->empPersonal),
																 'P.Employee_Id=J.Employee_Id', array(''))
													 
													 ->joinInner(array('D'=>$this->_ehrTables->designation),
																 'D.Designation_Id = J.Designation_Id', array(''))
													 
													 ->joinInner(array('G'=>$this->_ehrTables->empGrade),'G.Grade_Id = D.Grade_Id',
																 array(''))
													 
													 ->where('P.Form_Status = 1')
													 ->where('J.Emp_Status Like ?', 'Active')
													 ->where('G.Grade_Id = ?', $gradeId));
	 if (!empty($timesheetId) && $getEmployeeCount ==0)
        {
		    $timesheetHoursLock = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->timesheetHrs, array('Lock_Flag', 'Timesheet_Id','Grade_Id'))
		     ->where('Timesheet_Id = ?', $timesheetId));
		    
		   
			
		    if ($timesheetHoursLock['Lock_Flag'] == 0)
		    {
			    $deleted = $this->_db->delete($this->_ehrTables->timesheetHrs, 'Timesheet_Id='.(int)$timesheetId);
		    }
		    return $this->_commonFunction->deleteRecord (array('deleted'        => $deleted,
								    'tableName'      => $this->_ehrTables->timesheetHrs,
								    'lockFlag'       => $timesheetHoursLock['Lock_Flag'],
								    'formName'       => $formName,
								    'trackingColumn' => $timesheetHoursLock['Timesheet_Id'],
								    'sessionId'      => $logEmpId));
	}
        else
        {
            return array('success'=>false, 'msg'=>'Unable to delete '.$formName.'. Please, contact system admin', 'type'=>'info');
        }
    }
	
	/**
	 * Get timesheet hours details by gradeId
	 */
    public function timesheetHoursExists($gradeId)
    {
        $hrsQry=$this->_db->select()->from(array('hrs'=>$this->_ehrTables->timesheetHrs),array(new Zend_Db_Expr('Count(Timesheet_Id)')))
        ->where('hrs.Grade_Id = ?', $gradeId);
        $exists = $this->_db->fetchOne($hrsQry);

        return $exists;
    }
	
    public function updateTimesheetHours($timesheetHours,$sessionId, $formName)
    {
        $qryAccountCode = $this->_db->select()->from($this->_ehrTables->timesheetHrs, new Zend_Db_Expr('count(Timesheet_Id)'))
                                                                                 ->where('Grade_Id = ?', $timesheetHours['Grade_Id']);

         if (!empty($timesheetHours['Timesheet_Id']))
                $qryAccountCode->where('Timesheet_Id != ?', $timesheetHours['Timesheet_Id']);

        $timesheetHoursExists = $this->_db->fetchOne($qryAccountCode);
        if(empty($timesheetHoursExists))
        {
                if(!empty($timesheetHours['Timesheet_Id']))
                {
                        $action = 'Edit';
                        $timesheetHours['Updated_On'] = date('Y-m-d H:i:s');
                        $timesheetHours['Updated_By'] = $sessionId;
                        $timesheetHours['Lock_Flag']  = 0;
                        $updated = $this->_db->update($this->_ehrTables->timesheetHrs, $timesheetHours, array('Timesheet_Id = '.$timesheetHours['Timesheet_Id']));
                        
                        
                }
                else
                {
                        $action = 'Add';
                        $timesheetHours['Added_On'] = date('Y-m-d H:i:s');
                        $timesheetHours['Added_By'] = $sessionId;
                        $updated =  $this->_db->insert($this->_ehrTables->timesheetHrs, $timesheetHours);
                }
                
                /** Update the DataSetup status **/
                if($updated)
                {
                    $dataSetupStatus = $this->_db->fetchOne($this->_db->select()
                                                        ->from($this->_ehrTables->datasetupDashboard,
                                                                        array('Status'))
                                                        ->where('Form_Id = ?',24));
                    if($dataSetupStatus == 'Open')
                    {
                        $updated = $this->_db->update($this->_ehrTables->datasetupDashboard, array('Status'=> 'Completed'), array('Form_Id = '. '24'));
                    }
                }
                
                
              return $this->_commonFunction->updateResult (array('updated'    => $updated,
								'action'         => $action,
								'trackingColumn' => $timesheetHours['Timesheet_Id'],
								'formName'       => $formName,
								'sessionId'      => $sessionId,
								'tableName'      => $this->_ehrTables->timesheetHrs));
        }
        else
        {
            return array('success' => false, 'msg'=>'Grade Already Exist', 'type'=>'info');
        } 
    }

    ////to update timesheet hours
    //public function updateTimesheetHours($gradeId, $formdata, $sessionEmp)
    //{
    //
    //    $updateHrs = array('Regular_Hours'=>$formdata['Regular_Hours'],
    //			'OverTime_Hours'=>$formdata['OverTime_Hours'],
    //    		'Break_Hours'=>$formdata['Break_Hours'],
    //			'Total_Hours'=>$formdata['Total_Hours'],
    //			'Description'=>htmlentities($formdata['Description']),
    //			'Modified_Date'=>date('Y-m-d H:i:s'),
    //			'Grade_Id'=>$gradeId,
    //			'Updated_By'=>$sessionEmp,
    //			'Lock_Flag'=>0);
    //
    //    $updated = $this->_db->update($this->_ehrTables->timesheetHrs,$updateHrs,'Grade_Id='.(int)$gradeId);
    //
    //    if ($updated) {
    //        $gradeName = $this->_dbGrade->getGradeById($gradeId);
    //        $this->_ehrTables->trackEmpSystemAction('Edit Timesheet Hours - '.$gradeName, $sessionEmp);
    //        return $updateHrs;
    //
    //    } else {
    //
    //        return null;
    //
    //    }
    //}

	/**
	 * Check whether timesheet hours exists or not for a logged in employee
	 */
    public function empTimesheetHoursExists($logEmpId)
    {
        $hrsQry=$this->_db->select()->from(array('job'=>$this->_ehrTables->empJob),array(''))//array(new Zend_Db_Expr('Count(Timesheet_Id)')))
        ->joinInner(array('des'=>$this->_ehrTables->designation),'des.Designation_Id=job.Designation_Id',array(''))
        ->joinInner(array('hrs'=>$this->_ehrTables->timesheetHrs),'hrs.Grade_Id=des.Grade_Id',array(new Zend_Db_Expr('Count(Timesheet_Id)')))
        ->where('job.Employee_Id = ?', $logEmpId);

        $exists = $this->_db->fetchOne($hrsQry);
         
        return $exists;
    }

    public function __destruct()
    {
        
    }	
}

