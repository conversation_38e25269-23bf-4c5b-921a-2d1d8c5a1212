/*=========================================================================================*/
/*=========================================================================================*/
/* Program        : roles-template.js													   */
/* Property of Caprice Technologies Pvt Ltd,                                              
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,
 * Coimbatore, Tamilnadu, India. 							                               */
/* All Rights Reserved.            														   */
/* Use of this material without the express consent of Caprice Technologies
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law.   */
/*                                                                                    	   */
/* Description    :jquery for employee - roles template  							 	   */
/*                                                                                   	   */
/*                                                                                    	   */
/*Revisions      :                                                                    	   */
/*Version    Date           Author                  Description                       	   */
/*0.1        19-Aug-2019    Ahalya      		    Initial Version         			   */
/*        			                                                                       */
/*                                                                                    	   */
/*                                                                                    	   */
/*                                                                                    	   */
/*=========================================================================================*/
/*=========================================================================================*/
$(function() {
    var orgCode                     = fngetOrgCode(),
    employeeId                      = parseInt(localStorage.getItem('LoginEmpId'),10);
    var ipAddress                   = "";
    // Get clientIp address
    getClientIp(function(ipAdd) {
        ipAddress = ipAdd;
    });

    //ATS API 
    var atsBaseUrl                  = getApiCustomDomains('ats');


    //employee access rights for each action 
    var accessRightsParams = {
        addAccessRights         :0,
        updateAccessRights      :0,
        deleteAccessRights      :0,
        viewAccessRights        :0
    }

    
    var addRoleTemplates, //add action flag
        editRoleTemplates, //edit action flag
        idViewEdit                    = 0; // editing from view page flag 

    // set the form dirty to false at initial
    var isDirtyFormRolesTemplate      = false;
    var chosenRoleTemplate            = ''; //chosen role template id
    var chosenRoletemplateKey         = ''; //chosen role template key
    var rolesTemplates                = []; //list of roles

    var rolesTemplatePermissions = [];  //roles template permissions
    var rolesOptionsCount = 3; // For now Only Approval, Claim Override and Notifications permissions are there. So it is set to 3
    var roleApprovalCount = 0;
    var roleClaimOverrideCount = 0;
    var roleNotificationsCount = 0;
    var forms, modules, mainFormRoles, subForms, subFormRoles  = [];
    var copyRolesTemplateId = '';
    var moduleCount      = '';
    var lastInsertedId = ''; //last inserted record's Id 


    // initially hide the list and access denied page
    $('#listRolesTemplate').css('display','none');
    $('#access-denied').css('display','none');


    setMask('wholepage');

    // get the access rights for the form
    checkAccessRights(orgCode,atsBaseUrl,'Roles Template',employeeId,'#listRolesTemplate',function(error, accessRights){
        // check if access rights are retrieved successfully
        if(accessRights)
        {
            // assign the access rights
            accessRightsParams['viewAccessRights'] = accessRights.Role_View;
            accessRightsParams['addAccessRights'] = accessRights.Role_Add;
            accessRightsParams['updateAccessRights'] = accessRights.Role_Update;
            accessRightsParams['deleteAccessRights'] = accessRights.Role_Delete;

            // check view access rights exists
            if(accessRightsParams['viewAccessRights'])
            {
                // show the list page and hide the access denied page
                $('#listRolesTemplate').css('display','block');
                $('#access-denied').css('display','none');
                //get the roles template list to show in list page 
                listRolesTemplates(0);

            }
            else 
            {
                // hide the list and show the Access denied message if there is no access rights
                $('#listRolesTemplate').css('display','none');
                $('#access-denied').css('display','block');
            }
        }

    });


    //when add button is clicked 
   $('#addRolesTemplates').on('click', function() {
    //check add access rights 
     if(parseInt(accessRightsParams['addAccessRights'])) {
         addRoleTemplates = 1; //set the add flag
         retrieveCustomEmpGroups(0);
     }
     else 
     {
        // access denied
         $(window).scrollTop(0);
         jAlert({
             panel: $('#listRolesTemplate'),
             msg: 'Sorry, Access denied',
             type: 'warning'
         });
     }
 });

    // set form dirty when inputs are given
    $('#rolesTemplateName,#description,#empGroup').on('change',function(e,initialCheck = false) {

        // Check if the change function is triggered while form reset or during inputs 
        initialCheck ? isDirtyFormRolesTemplate = false : isDirtyFormRolesTemplate = true;

    });

    // remove error set in roles template name
    $('#rolesTemplateName').on('input',function() {
        addRemoveValidationError('remove','rolesTemplateName','');

    });

    // close the add/edit roles template model
    $('#closeRolesTemplateModal').on('click', function() {
        //check if the form is dirty 
        if (isDirtyFormRolesTemplate) {
             // open the close confirmation modal and make the scroll
            $('#modalCloseRolesTemplate').modal({
                backdrop: 'static',
                keyboard: false
            });

        }
        else 
        {
            // check if action is add
            if(addRoleTemplates)
            {
                //close the add modal 
                cancelForm();
            }
            else 
            {
                // close edit form
                closeEditForm();
            }
        }
    });

    //confirm close of the create roles template modal 
    $('#confirmClose').on('click',function() {

        
        if(editRoleTemplates){

            // close the edit form
            closeEditForm();
        }
        else {
            
            if($('#modalRolesTemplatePermissions').is(':visible')) {
                clearLockAPI(orgCode, 'Roles Template Access Rights', chosenRoleTemplate, employeeId, '#listRolesTemplate', atsBaseUrl, function(error, data) {
                    if (data) {
                        $('#modalCloseRolesTemplate').modal('hide');
    
                        $('#modalRolesTemplatePermissions').modal('toggle'); 
                        $('#modalRolesTemplatePermissions').on('hidden.bs.modal', function (e) {
                            $('body').removeClass('modal-open');
                        });
                
                    }
                });
            }
            else {
                $('#modalCloseRolesTemplate').modal('hide');
                $('#modalCreateRolesTemplate').modal('hide');
                $('#modalCreateRolesTemplate').on('hidden.bs.modal', function (e) {
                    $('body').removeClass('modal-open');
                });
    
            }
        }
    });



    //cancel close confirmation 
    $('#cancelCloseForm,#cancelClose').on('click',function() {
        //close the confirmation popup and enable scrolling in the first modal 
        $('#modalCloseRolesTemplate').modal('hide');
        openPreviousModal(); //open if any previously opened modal (to enable scroll)
    });

    //create roles templates 
    $('#createRolesTemplate').on('click',function() {
        //check if the action is add/edit 
        var l = Ladda.create(this);
        //check whether form is dirty 
        if(isDirtyFormRolesTemplate)
        {
            $('#s2id_empGroup').removeClass('form-error');

            //validate the inputs in the form 
            if ($('#formRolesTemplate').valid()) { 

                // selected Emp group in add/edit
                var selectedEmpGroup = $('#empGroup').select2('val').map(function(v) {
                    return parseInt(v, 10);
                    });


                //add/edit role templates inputs 
                var variables = {
                    templateName           : $('#rolesTemplateName').val(),
                    description             : $('#description').val(),
                    customGroupId           : selectedEmpGroup
                }
                // check if action is add
                if(addRoleTemplates)
                {
                    //add the variable if the action is add 
                    variables['addedBy'] = employeeId;
                    // query for add
                    var mutation = `mutation ($templateName: String!,$description : String,$customGroupId:[Int!],$addedBy:Int!) 
                                    { addRolesTemplate(templateName:$templateName,description:$description,customGroupId:$customGroupId, addedBy:$addedBy) 
                                        { message errorCode validationError data}
                                    }`;
                }
                else 
                {
                    //add these variables if the action is edit 
                    variables['updatedBy'] = employeeId;
                    variables['templateId'] = chosenRoleTemplate;

                    // query for edit 
                    var mutation = `mutation ($templateId:Int!,$templateName: String!,$description : String,$customGroupId:[Int!],$updatedBy:Int!) 
                                    { editRolesTemplate(templateId:$templateId,templateName:$templateName,description:$description,customGroupId:$customGroupId,updatedBy:$updatedBy) 
                                        { message errorCode validationError}
                                    }`;

                }
                setMask('#wholepage');
                // add/edit roles template
                $.ajax({
                    method: 'POST',
                    url: atsBaseUrl,
                    headers: getGraphqlAPIHeaders(ipAddress),
                    data: JSON.stringify({
                        query: mutation,
                        variables: variables
                    }),

                    success: function(result) {
                        if(result.data)
                        {
                            //check if the action is add
                            if(addRoleTemplates)
                            {
                                //close the add modal 
                                removeMask();
                                // last inserted Id  
                                lastInsertedId = result.data.addRolesTemplate.data;
                                // trigger update permissions to open the roles modal 
                                $('#updatePermissions').trigger('click');
                            }
                            else {
                                //clear the lock flag if the action is edit 
                                clearLockAPI(orgCode, 'Roles Template', chosenRoleTemplate, employeeId, '#listRolesTemplate', atsBaseUrl, function(error, data) {
                                    if (data) {
                                        //close the edit modal and show the list 
                                        removeMask();
                                        $('#modalCreateRolesTemplate').modal('toggle');
                                        $('#modalCreateRolesTemplate').on('hidden.bs.modal', function (e) {
                                            $('body').removeClass('modal-open');
                                        });
                                        //list the roles template 
                                        listRolesTemplates(0);
                                    }
                                });
                            }
                        }
                        else {
                            // if add/edit roles template failed 
                            removeMask();
                            var error = JSON.parse(result.errors[0].message);
                            var errorCode = error.errorCode;

                            $(window).scrollTop(0);
                            switch (errorCode) {
                                case 900: //validation errors 
                                    displayValidationErrors(error.validationError);
                                    break;
                                    //technical issues 
                                case 705:
                                case 706:
                                    cancelForm();
                                    jAlert({
                                        panel: $('#listRolesTemplate'),
                                        msg: 'There seems to be some technical issues. Please try after some time.',
                                        type: 'warning'
                                    });
                                    break;
                                case 719://access denied 
                                    cancelForm();
                                    jAlert({
                                        panel: $('#listRolesTemplate'),
                                        msg: 'Sorry, Access denied',
                                        type: 'warning'
                                    });
                                    break;
                                case 755:
                                case 756: //functionality errors 
                                default:
                                    cancelForm();
                                    jAlert({
                                        panel: $('#listRolesTemplate'),
                                        msg: 'Something went wrong. Please contact system administrator.',
                                        type: 'warning'
                                    });
                                    break;
                            }
                        }
                    },
                    error: function(result) {
                        // error while add/edit roles template records
                        cancelForm();
                        removeMask();
                        jAlert({
                            panel: $('#listRolesTemplate'),
                            msg: 'Something went wrong. Please contact system administrator.',
                            type: 'warning'
                        });
                    }
                })
            }
            else 
            {
                l.stop();
            }
        }
        else 
        {
            //form has no changes
            l.stop();
            $(window).scrollTop(0);
            jAlert({ panel: $('#createRoles'), msg: 'Form has no changes', type: 'info' });

        }
    });

    var timer = null;

    //search roles template  
    $('#search,#searchrolestemp').on('input', function() {
        $('#search,#searchrolestemp').val($(this).val());
        if (timer) {
            clearTimeout(timer);
        }
        //The search filter will be applied only after the user stops entering the value in the search field
        timer = setTimeout(function() {

            listRolesTemplates(1); //list roles template based on search

        }, 1000);
    });

    //delete roles template record 
    $(document).on('click', '#deleterolerecord', function(e) {
        //check the delete rights  
        if(parseInt(accessRightsParams['deleteAccessRights'])){
            // chosen role template records
            chosenRoleTemplate = parseInt($(this).attr('templateId'),10);
            // open the delete confirmation modal
            $('#modalDeleteRoleTemplate').modal('toggle');
        }
        else 
        {
            // access denied for delete
            $(window).scrollTop(0);
            jAlert({
                panel: $('#listRolesTemplate'),
                msg: 'Sorry, Access denied',
                type: 'warning'
            });
        }
        e.stopPropagation();
    });

    //confirm deleting the roles template record
    $('#deleteConfirmRequisition').on('click',function() {
        //delete the roles template in backend 
        deleteRolesTemplate();
    });

    //edit roles template from the view page 
    $('#idViewEdit').on('click',function() {
        //check update rights 
        if(accessRightsParams['updateAccessRights'])
        {
            idViewEdit = 1; //set flag to 1 if the edit is from view page 
            chosenRoleTemplate = parseInt($(this).attr('templateId'),10); //chosen role template record
            chosenRoleTemplateKey = parseInt($(this).attr('templateKey'),10); //chosen index of the role template
            $('#editrolerecord').trigger('click'); //trigger the edit function 
        } 
        else{
            // edit access denied
            jAlert({
                panel: $('.custom-modal-content'),
                msg: 'Sorry, Access denied',
                type: 'warning'
            });
        } 
    });

    // edit the role template record
     $(document).on('click', '#editrolerecord', function(e) {
        if(parseInt(accessRightsParams['updateAccessRights'])) {
            editRoleTemplates       = 1; //set the edit flag
            chosenRoleTemplate      = idViewEdit ?  chosenRoleTemplate : parseInt($(this).attr('templateId'),10); //chosen role template record
            chosenRoleTemplateKey   = idViewEdit ? chosenRoleTemplateKey  : parseInt($(this).attr('templateKey'),10); //chosen index of the role template

            retrieveCustomEmpGroups(1);
        }
        else 
        {
            // access denied
            $(window).scrollTop(0);
            jAlert({
                panel: $('#listRolesTemplate'),
                msg: 'Sorry, Access denied',
                type: 'warning'
            });
        }
        e.stopPropagation(); //to stop the click function of main div 

     });

    //view  roles template details 
   $(document).on('click', '.rolestemplatedetails ', function(e) {
        chosenRoleTemplate  = parseInt($(this).attr('templateId'),10); //chosen role template record 
        chosenRoleTemplateKey  = parseInt($(this).attr('templateKey'),10); //chosen index of the role template

        var rolesTemplateName   = rolesTemplates[chosenRoleTemplateKey].Template_Name; // roles template name
        var description         = rolesTemplates[chosenRoleTemplateKey].Description; // roles template decription

        var chosenGroups      = rolesTemplates[chosenRoleTemplateKey].Group_Name; //associated custom employee groups

        // concat the associated custom employee groups
        var chosenEmpGroups = '' ;
        for(var i in chosenGroups) {
            i ==  0 ? (chosenEmpGroups = chosenGroups[i]) : (chosenEmpGroups = chosenEmpGroups+', '+ chosenGroups[i]);
        }

        // make the content empty
        $('#viewRolesTemplateName,#viewDescription,#viewRolesTemplateImage,#viewEmpGroup').html('');
        // append the name, description, associated custom employee groups and image
        $('#viewRolesTemplateName').append(rolesTemplateName);
        $('#viewEmpGroup').append(chosenEmpGroups ? chosenEmpGroups : '-');
        $('#viewDescription').append(description ? description : '-');

        
        rolesTemplateName          = rolesTemplateName.match(/\b(\w)/g);
        rolesTemplateName          = rolesTemplateName.join('').toUpperCase();
        $('#viewRolesTemplateImage').append('<div class="viewImage"> '+rolesTemplateName+' </div>');

        // set the role template id and index in the edit button
        $('#idViewEdit').attr('templateId',chosenRoleTemplate)
                        .attr('templateKey',chosenRoleTemplateKey);
        
        //show the details of the roles template 
        $('#modalViewRolesTemplate').modal({
            backdrop: 'static',
            keyboard: false
        });

        $('#modalViewRolesTemplate').on('hidden.bs.modal', function (e) {
            $('body').addClass('modal-open');
        });

    });

    //close the view roles template modal 
    $('#closeRolesTemplateViewModal,#cancelView').on('click', function() {
        //close the view modal and enable page scolling 
        $('#modalViewRolesTemplate').modal('toggle');
        $('#modalViewRolesTemplate').on('hidden.bs.modal', function (e) {
            $('body').removeClass('modal-open');
        });
    });

    // when view permissions is clicked
    $(document).on('click', '#updatePermissions', function(e) {
        isDirtyFormRolesTemplate = false;
        chosenRoleTemplate = addRoleTemplates ? lastInsertedId :  parseInt($(this).attr('templateId'),10); // chosen template id;
        retrieveAccessRights(0); // retrieve access rights
        e.stopPropagation();
    });

    // when the employees are choosen/removed in checkbox 
    $(document).on('click', 'input[type=checkbox]', function(event) {
            isDirtyFormRolesTemplate = true;
            var targetId = event.target.id; // get the target id 
            var checked = $(this).prop("checked"); // check if the permissions are checked/unchecked 
            switch (targetId) {
                // check if the Select All checkbox is checked
                case 'all':
                    // enable/disable all permissions if the check all is given 
                    enableDisableAllPermissions(checked);
                    break;
            
                default:
                    // enable/disable individual permissions
                    enableDisablePermissions(event.target.id,checked);
                    break;
            }
    });

    // copying roles from other roles template
    $('#copyFromRolesTemplate').on('click', function(e) {
        copyRolesTemplateId = parseInt($('#s2id_copyRolesTemplates').select2('val'),10);
        if(copyRolesTemplateId)
        {
            retrieveAccessRights(1);

        }
        else {
            jAlert({ panel: $('#rolesPermissions'), msg: 'Please choose an role template to copy', type: 'info' });
        }
        e.stopPropagation();
    });

    // submit roles
    $('#submitRoles').on('click', function() {
        if(isDirtyFormRolesTemplate) 
        {
            var anyBoxesChecked = false; 
            $('#editRolesTemplateAccessRights input[type="checkbox"]').each(function() {
                if ($(this).is(":checked")) {
                    anyBoxesChecked = true;
                }
            });

            // check if checkboxes are checked/not
            if(anyBoxesChecked)
            {
                // variables to update roles template permissions
                var variables = {
                    templateId : chosenRoleTemplate,
                    employeeId : employeeId,
                }

                var rolesPermissions = []; //roles array

                var roleApproval = 0;
                var roleClaimOverride = 0;
                var roleNotifications = 0;
                // concat forms and subForms
                let formAndSubForms = forms.concat(subForms);
                // loop through the formAndSubForms and assign access rights that are enabled
                for (var l in formAndSubForms)
                {
                    var formId = formAndSubForms[l].Form_Id;
                    
                    roleApproval        =    $('#Approval-'+formId).is(":checked") ?  1 : 0;
                    roleClaimOverride   =    $('#ClaimOverride-'+formId).is(":checked") ?  1 : 0;
                    roleNotifications   =    $('#Notifications-'+formId).is(":checked") ?  1 : 0;

                    rolesPermissions.push({
                        roleAdd : 0,
                        roleUpdate : 0,
                        roleDelete : 0,
                        roleView : 0,
                        roleOptionalChoice : 0,
                        roleHrGroup : 0,
                        rolePayrollGroup : 0,
                        roleApproval :roleApproval ,
                        roleClaimOverride : roleClaimOverride,
                        roleNotifications : roleNotifications,
                        formId : formId
                    });
        
                    roleApproval = 0;
                    roleClaimOverride = 0;
                    roleNotifications = 0;
            
                }
        
                // assign the roles permissions to the variables
                variables['roleArray'] = rolesPermissions;

                // mutation to update roles template permissions
                var mutation = `mutation 
                                ($roleArray:[formsRole!],$templateId:Int!,$employeeId: Int!) 
                                    { updateRolesTemplatePermission 
                                        (roleArray : $roleArray,templateId:$templateId,employeeId:$employeeId) 
                                            { errorCode message validationError}
                                    }`;


                setMask('#wholepage');
                $.ajax({
                    type: 'POST',
                    async: false,
                    url: atsBaseUrl,
                    contentType: 'application/json',
                    headers: getGraphqlAPIHeaders(ipAddress),
                    data: JSON.stringify({
                        query: mutation,
                        variables: variables
                    }),
                    success: function(result) {
                        if (result.data) {
                            $('#modalRolesTemplatePermissions').modal('toggle');
                            removeMask();
                            clearLockAPI(orgCode, 'Roles Template Access Rights', chosenRoleTemplate, employeeId, '#listRolesTemplate', atsBaseUrl, function(error, data) {
                                if (data) {
                                // check if the action is add 
                                if(addRoleTemplates) {
                                    listRolesTemplates(0);
                                }
                                else  {
                                    jAlert({
                                        panel: $('#listRolesTemplate'),
                                        msg: 'Permissions updated successfully.',
                                        type: 'info'
                                    });
                                }
                            }
                            });
                        }
                        else {
                            clearLockAPI(orgCode, 'Roles Template Access Rights', chosenRoleTemplate, employeeId, '#listRolesTemplate', atsBaseUrl, function(error, data) {
                                if (data) {
                                    $('#modalCloseRolesTemplate').modal('hide');
        
                                    $('#modalRolesTemplatePermissions').modal('toggle'); 
                                    $('#modalRolesTemplatePermissions').on('hidden.bs.modal', function (e) {
                                        $('body').removeClass('modal-open');
                                    });
                            
                                }
                            });
                            removeMask();
                            //handle errors while retrieving 
                            var error = JSON.parse(result.errors[0].message);
                            var errorCode = error.errorCode;
                            $(window).scrollTop(0);
                            switch (errorCode) {
                                //technical errors 
                                case 705:
                                case 706:
                                    jAlert({
                                        panel: $('#listRolesTemplate'),
                                        msg: 'There seems to be some technical issues. Please try after some time.',
                                        type: 'warning'
                                    });
                                    break;
                                case 719: //access denied 
                                    jAlert({
                                        panel: $('#listRolesTemplate'),
                                        msg: 'Sorry, Access denied',
                                        type: 'warning'
                                    });
                                    break;
                                    // functionality errors 
                                case 900:
                                case 745:
                                default:
                                    jAlert({
                                        panel: $('#listRolesTemplate'),
                                        msg: 'Something went wrong. Please contact system administrator.',
                                        type: 'warning'
                                    });
                                    break;
                            }
                        }
                    },
                    error:function(result)
                    {
                        removeMask();
                        clearLockAPI(orgCode, 'Roles Template Access Rights', chosenRoleTemplate, employeeId, '#listRolesTemplate', atsBaseUrl, function(error, data) {
                            if (data) {
                                $('#modalCloseRolesTemplate').modal('hide');
    
                                $('#modalRolesTemplatePermissions').modal('toggle'); 
                                $('#modalRolesTemplatePermissions').on('hidden.bs.modal', function (e) {
                                    $('body').removeClass('modal-open');
                                });
                        
                            }
                        });
                        jAlert({
                            panel: $('#listRolesTemplate'),
                            msg: 'Something went wrong. Please contact system administrator.',
                            type: 'warning'
                        });
                    }
            
                });
            }
            else 
            {
                jAlert({ panel: $('#rolesPermissions'), msg: 'Please choose atleast one role', type: 'info' });

            }
        }
        else 
        {
            jAlert({ panel: $('#rolesPermissions'), msg: 'Form has no changes', type: 'info' });
        }
    });

    /**when back button is clicked in roles template modal */
    $('#backRolesTemplate').on('click',function(){
        // check if the form is dirty
        if(isDirtyFormRolesTemplate)
        {
            $('#modalCloseRolesTemplate').modal({
                backdrop: 'static',
                keyboard: false
            });
        }
        else 
        {
            clearLockAPI(orgCode, 'Roles Template Access Rights', chosenRoleTemplate, employeeId, '#listRolesTemplate', atsBaseUrl, function(error, data) {
                if (data) {
                    $('#modalRolesTemplatePermissions').modal('toggle'); 
                    $('#modalRolesTemplatePermissions').on('hidden.bs.modal', function (e) {
                        $('body').removeClass('modal-open');
                    });
            
                }
            });
        }
    });


  function retrieveCustomEmpGroups(isEdit) {
   
    var mutation = `query ($isDropDownCall :Int! ,$employeeId : Int!) 
        { listCustomGroups (isDropDownCall:$isDropDownCall ,employeeId:$employeeId) { errorCode message listCustomGroups }
        }`;
    


    var variables = {
        isDropDownCall : 1,
        employeeId   : employeeId
    }
        setMask('#wholepage');
        // add/edit roles template
        $.ajax({
            method: 'POST',
            url: atsBaseUrl,
            headers: getGraphqlAPIHeaders(ipAddress),
            data: JSON.stringify({
            query: mutation,
            variables: variables
        }),

        success: function(result) {
            if(result.data)
            {
                removeMask();
                var response = result.data.listCustomGroups
                var customGroups = JSON.parse(response.listCustomGroups);

                $('#empGroup').html('');

                for(var i in customGroups) {
                    $('#empGroup').append(
                        "<option value='" +
                            customGroups[i].Group_Id +
                            "'>" +
                            customGroups[i].Group_Name +                                               
                            '</option>'
                    );
                }

                if(isEdit) {
                    //append the modal header 
                    $('.workflow-modal-header').html('')
                    .append('Edit Roles Template');

                    var errorPage           = idViewEdit ? '.custom-modal-content' : '#listRolesTemplate'; //set the error class to show the failure response 
                    var rolesTemplateName   = rolesTemplates[chosenRoleTemplateKey].Template_Name; // template name
                    var description         = rolesTemplates[chosenRoleTemplateKey].Description; // template description
                    var chosenEmpGroup      = rolesTemplates[chosenRoleTemplateKey].Group_Id; // template description

                    setMask('#wholepage');
                    setLockAPI(orgCode, 'Roles Template', chosenRoleTemplate, employeeId, errorPage, atsBaseUrl, function(error, data) {
                    if (data) {
                    //prefi;; the template name and description values in the input field
                    $('#rolesTemplateName').val(rolesTemplateName)
                    .trigger('change',true)
                    $('#description').val(description)
                    .trigger('change',true)

                    $('#s2id_empGroup').select2('val',chosenEmpGroup);
                    $('#empGroup').trigger('change',true)
                                           .removeClass('form-error')
                    // reset the form errors 
                    $('#formRolesTemplate')
                    .validate()
                    .resetForm();
                    // check if the action is edit from view page
                    if(idViewEdit)
                    {
                    // close the view modal
                    $('#modalViewRolesTemplate').modal('toggle');
                    idViewEdit = 0;
                    }
                    //show the edit roles template modal 
                    //open the model and disbale the closing of model when clicked outside 
                    $('#modalCreateRolesTemplate').modal({
                    backdrop: 'static',
                    keyboard: false
                    });
                    //to enable scroll in the first modal when the second modal which is opened is closed 
                    $('#modalCreateRolesTemplate').on('hidden.bs.modal', function (e) {
                    $('body').addClass('modal-open');
                    });
                    removeMask();
                    }
                    });
                }
                else {
                    //append the modal header 
                    $('.workflow-modal-header').html('')
                    .append('Create Roles Template');

                    // make the input values empty 
                    $('#rolesTemplateName,#description').val('').trigger('change',true);

                    // make the emp group input empty and remove validation errors
                    $('#s2id_empGroup').select2('val','').removeClass('form-error');
                    $('#empGroup').trigger('change',true);
                    $('#empGroup option:selected').removeAttr('selected');
                    $('#empGroup').addClass('empty');

                    // reset the form
                    $('#formRolesTemplate')
                    .validate()
                    .resetForm();

                    // open the add modal
                    $('#modalCreateRolesTemplate').modal({
                    backdrop: 'static',
                    keyboard: false
                    });
                    // open the if any previously openend modal
                    openPreviousModal();
                }
                
            }
            else {
            // if add/edit roles template failed 
            removeMask();
            var error = JSON.parse(result.errors[0].message);
            var errorCode = error.errorCode;

            $(window).scrollTop(0);
            switch (errorCode) {
                
                //technical issues 
                case '_DB0000':
                    jAlert({
                        panel: $('#listRolesTemplate'),
                        msg: 'There seems to be some technical issues. Please try after some time.',
                        type: 'warning'
                    });
                    break;
                case '_DB0100'://access denied 
                    jAlert({
                        panel: $('#listRolesTemplate'),
                        msg: 'Sorry, Access denied',
                        type: 'warning'
                    });
                    break;
                case 'ERE0015':
                case 'ERE0015': //functionality errors 
                default:
                    jAlert({
                        panel: $('#listRolesTemplate'),
                        msg: 'Something went wrong. Please contact system administrator.',
                        type: 'warning'
                    });
                    break;
                }
                addRoleTemplates = 0;
                editRoleTemplates = 0;
    
            }
        },
        error: function(result) {
            // error while add/edit roles template records
            
            removeMask();
            jAlert({
                panel: $('#listRolesTemplate'),
                msg: 'Something went wrong. Please contact system administrator.',
                type: 'warning'
            });

            addRoleTemplates = 0;
            editRoleTemplates = 0;
        }
    })
    }


    /**function to retrieve role template permissions */
    function retrieveAccessRights(copyRoles=0)
    {
        
        // variables to retrieve the employee groups  
        var variables = {
            templateId   : copyRoles ? copyRolesTemplateId : chosenRoleTemplate,
            employeeId   : employeeId
        } 

        // mutation to retrieve roles template permissions
        var mutation = `mutation CommentQuery
                        ($templateId:Int!,$employeeId:Int!) 
                            { retrieveRolesTemplatePermission (templateId: $templateId,employeeId : $employeeId)  
                                { errorCode message 
                                    modules { Module_Id Module_Name }  
                                    mainForms { Form_Id Form_Name Sub_Form Module_Id Module_Name } 
                                    subForms { Form_Id Form_Name Sub_Form Module_Id Module_Name } 
                                    mainFormRoles { Template_Id Form_Id Role_Approval Role_Claim_Override Role_Notification } 
                                    subFormRoles { Template_Id Form_Id Role_Approval Role_Claim_Override Role_Notification } 
                                }
                            }`;

        setMask('#wholepage');
        $.ajax({
            type: 'POST',
            async: false,
            url: atsBaseUrl,
            contentType: 'application/json',
            headers: getGraphqlAPIHeaders(ipAddress),
            data: JSON.stringify({
                query: mutation,
                variables: variables
            }),
            success: function(result) {
                if (result.data) {
                    rolesTemplatePermissions = result.data.retrieveRolesTemplatePermission; // roles tempalte permissions
                    moduleCount = rolesTemplatePermissions.modules.length; //no. of considered modules
                    removeMask(); 

                    // load the copy roles dropdown
                    if(!copyRoles)
                    {
                        // clear the dropdown before appending
                        $('#copyRolesTemplates').html('');

                        for(var i in rolesTemplates)
                        {
                            // check if not the currently opened roles template
                            if (chosenRoleTemplate !== rolesTemplates[i].Template_Id){
                                $('#copyRolesTemplates').append(
                                    "<option value='" +
                                        rolesTemplates[i].Template_Id +
                                        "'>" +
                                        rolesTemplates[i].Template_Name +                                               
                                        '</option>'
                                );
                            }
                        }
                        if(parseInt(i,10) === rolesTemplates.length)
                        {
                            $('#copyRolesTemplates').addClass('empty');

                        }
                    }


                    prefillRolesTemplatePermissions(copyRoles); //prefill the available roles template permissions
                    
                } else {
                    removeMask();
                    //handle errors while retrieving 
                    var error = JSON.parse(result.errors[0].message);
                    var errorCode = error.errorCode;
                    $(window).scrollTop(0);
                    switch (errorCode) {
                        //technical errors 
                        case 705:
                        case 706:
                            jAlert({
                                panel: $('#listRolesTemplate'),
                                msg: 'There seems to be some technical issues. Please try after some time.',
                                type: 'warning'
                            });
                            break;
                        case 719: //access denied 
                            jAlert({
                                panel: $('#listRolesTemplate'),
                                msg: 'Sorry, Access denied',
                                type: 'warning'
                            });
                            break;
                            // functionality errors 
                        case 746:
                        default:
                            jAlert({
                                panel: $('#listRolesTemplate'),
                                msg: 'Something went wrong. Please contact system administrator.',
                                type: 'warning'
                            });
                            break;
                    }
                }
            },
            error:function(result)
            {
                removeMask();
                jAlert({
                    panel: $('#listRolesTemplate'),
                    msg: 'Something went wrong. Please contact system administrator.',
                    type: 'warning'
                });
            }
        });
    }

    /**enable/disable ApproveAll, ApprovalOverridde and Approval Notification permissions */
    function enableDisablePermissions(targetId,checked)
    {
        if($('#'+targetId).hasClass('All')){

            //get the module Id and the permission name - Approval/ApprovalOverride/Notifications to check/uncheck the particular permissions  
            var id = targetId.split('-');
            var moduleId = parseInt(id[1],10);
            var permissionsName   = id[0];

            permissionsName = permissionsName.substring(0,permissionsName.length-3); //permission name 
                    // concat forms and subForms
                    let formAndSubForms = forms.concat(subForms);
                    //loop through the formAndSubForms 
                    for (var j in formAndSubForms)
                    {
                        //check if the moduleid is equal to the currently opened moduleid 
                        if(formAndSubForms[j].Module_Id === moduleId) {
                            var formId = formAndSubForms[j].Form_Id;
                                //check the formAndSubForms under the Approval/Claim Override and Notifications 
                                $('#'+permissionsName+'-'+formId).prop('checked',checked);
                        }
                    }

                    // Conditions to check/uncheck the dependency permissions
                    switch (permissionsName) {
                        case 'Approval': // when Approval All is clicked --- Approval Dependency -> Notifications and Claim Override 
                            // if the value is checked
                            if(checked)
                            {
                                // check if the Notification value is unchecked.
                                if(!$("#NotificationsAll-"+moduleId).is(":checked"))
                                {
                                    $('#NotificationsAll-'+moduleId).trigger('click');
                                }
                            }
                            // if Approval All is unchecked
                            else 
                            {
                                // if claim override is checked, uncheck it
                                if($("#ClaimOverrideAll-"+moduleId).is(":checked"))
                                {
                                    $('#ClaimOverrideAll-'+moduleId).trigger('click');
                                }
                            }
                            break;
                        case 'ClaimOverride': // when Claim Override All is clicked ---  Claim Override dependency -> Approval 
                            // check if the Claim Override is checked
                            if(checked)
                            {
                                // If Approval Overrode is checked or not - If not check it.
                                if(!$("#ApprovalAll-"+moduleId).is(":checked"))
                                {
                                    $('#ApprovalAll-'+moduleId).trigger('click');
                                }
                            }
                            
                            break;
                    
                        default: // when Notifications All is clicked - Dependency -> Approval and Claim Override
                            // if Notifications All is unchecked , Uncheck both Claim Override and Approval All
                            if(!checked)
                            {
                                if($("#ApprovalAll-"+moduleId).is(":checked"))
                                {
                                    $('#ApprovalAll-'+moduleId).trigger('click');
                                }
                                if($("#ClaimOverrideAll-"+moduleId).is(":checked"))
                                {
                                    $('#ClaimOverrideAll-'+moduleId).trigger('click');
                                }
                            }
                            break;
                    }
                    checkUncheckAllCheckbox();

        }
        else 
        {
            // get the target id 
            var id = targetId.split('-');
            var permissionsName   = id[0]; //permission name
            var formId  = parseInt(id[1],10); //form id
            // check/uncheck based on the dependencies
            switch (permissionsName) {
                case 'Approval': // when Approval All is clicked --- Approval Dependency -> Notifications and Claim Override 
                // if the value is checked
                    if(checked)
                    {
                        
                        if(!$("#Notifications-"+formId).is(":checked"))
                        {
                            $('#Notifications-'+formId).trigger('click');
                        }
                    }
                    else 
                    {
                        if($("#ClaimOverride-"+formId).is(":checked"))
                        {
                            $('#ClaimOverride-'+formId).trigger('click');
                        }
                    }
                    
                    break;
                case 'ClaimOverride':// when Claim Override All is clicked ---  Claim Override dependency -> Approval 
                // check if the Claim Override is checked
                    if(checked)
                    {
                        if(!$("#Approval-"+formId).is(":checked"))
                        {
                            $('#Approval-'+formId).trigger('click');
                        }
                    }
                    else 
                    {
                        if($("#Approval-"+formId).is(":checked"))
                        {
                            $('#Approval-'+formId).trigger('click');
                        }
                    }
                    break;

                default:// when Notifications All is clicked - Dependency -> Approval and Claim Override
                // if Notifications All is unchecked , Uncheck both Claim Override and Approval All
                    if(!checked)
                    {
                        if($("#Approval-"+formId).is(":checked"))
                        {
                            $('#Approval-'+formId).trigger('click');
                        }
                        if($("#ClaimOverride-"+formId).is(":checked"))
                        {
                            $('#ClaimOverride-'+formId).trigger('click');

                        }
                    }
                    break;
            }
            var formCount = 0;
            var formCheckedCount = 0;
        

            var currentModuleId = $("#moduleTabs li.active").prop('id'); //get the active module id 
            currentModuleId = currentModuleId.split('active'); //split and get the moduleid form that
            currentModuleId = parseInt(currentModuleId[0],10);
            // concat forms and subForms
            let formAndSubForms = forms.concat(subForms);
            for ( var i in formAndSubForms)
            {
                if(formAndSubForms[i].Module_Id === currentModuleId) {
                    var formId = formAndSubForms[i].Form_Id;
                    formCount++;
                    if($('#'+permissionsName+'-'+formId).is(":checked")) 
                    {
                        formCheckedCount++;
                    }
                    
                }
            }


            if(formCheckedCount===formCount)
            {
                if(!$('#'+permissionsName+'All-'+currentModuleId).is(":checked")){
                    $('#'+permissionsName+'All-'+currentModuleId).trigger('click');
                }
            }
            else 
            {
                if(!checked) {
                    switch (permissionsName) {
                        case 'Approval':
                            $('#'+permissionsName+'All-'+currentModuleId).prop('checked',false);

                            break;
                        case 'ClaimOverride':
                            $('#'+permissionsName+'All-'+currentModuleId).prop('checked',false);
                            $('#ApprovalAll-'+currentModuleId).prop('checked',false);

                        default:
                            $('#'+permissionsName+'All-'+currentModuleId).prop('checked',false);
                            break;
                    }
                }
            }

            checkUncheckAllCheckbox();

        }
    }

    /**prefill the roles templat permissions */
    function prefillRolesTemplatePermissions(copyRoles=0)
    {
        modules = rolesTemplatePermissions.modules; // available modules
        forms   = rolesTemplatePermissions.mainForms; // available forms
        subForms   = rolesTemplatePermissions.subForms; // available subForms
        mainFormRoles = rolesTemplatePermissions.mainFormRoles; //roles for mainForms
        subFormRoles = rolesTemplatePermissions.subFormRoles; //roles for subForms
        // check if action is copy roles
        if(copyRoles)
        {
            if(mainFormRoles.length> 0 || subFormRoles.length > 0)
            {
                isDirtyFormRolesTemplate = true;
                prefillRoles();
            }
            else {
                jAlert({ panel: $('#rolesPermissions'), msg: 'No permissions available for the chosen role template', type: 'info' });
            }
        }
        else 
        {
            $('#s2id_copyRolesTemplates').select2('val', '');
            $('#copyRolesTemplates').trigger('change');
            $('#copyRolesTemplates').addClass('empty');

            // check if the action is add roles template
            if(addRoleTemplates) {
                // prefill the roles form and open it
                    prefillRoles();
                    openRolesForm();
            }
            else {
                // check if the roles are already updated to set the lock
                if(mainFormRoles.length > 0 || subFormRoles.length > 0)
                {
                    // set lock to open the permissions tab
                    setMask('#wholepage');
                    setLockAPI(orgCode, 'Roles Template Access Rights', chosenRoleTemplate, employeeId, '#listRolesTemplate', atsBaseUrl, function(error, data) {
                        if (data) {
                            // prefill the roles form and open it
                            removeMask();
                            prefillRoles();
                            openRolesForm();
                        }
                    });
                }
                else 
                {
                    // prefill the roles form and open it
                    prefillRoles();
                    openRolesForm();
                }
            }

        }
    }


    // open the roles template form
    function openRolesForm() {
        $('#modalRolesTemplatePermissions').modal({
            backdrop: 'static',
            keyboard: false
        });
        //check whether any other modal is opened and add open class to first modal 
        $('body').on('hidden.bs.modal', function () {
            if($('.modal.in').length > 0)
            {
                $('body').addClass('modal-open');
            }
        });

        if(addRoleTemplates) {
            // close the create modal
            $('#modalCreateRolesTemplate').modal('toggle');
            $('#modalCreateRolesTemplate').on('hidden.bs.modal', function (e) {
                $('body').removeClass('modal-open');
            });
            isDirtyFormRolesTemplate = true; //set the dirty to true as update permissions is automatically opened from add form

        }
    }


    // prefill the permissions
    function prefillRoles()
    {
        $('#moduleTabs,#tabContents').html(''); //make the html contents empty, before appending 

        //assign the modules in the tabs 
        for(var i in modules) {
            var moduleId = modules[i].Module_Id;
            var moduleName = modules[i].Module_Name;

            //append the tabs based on the module 
            $('#moduleTabs').append('<li id="'+moduleId+'active" class="modules employee-group-modules"><a href="#collapse'+moduleName+'" data-toggle="tab"><i class="icon hr-employee hrapp-icon-size"></i>'
            + modules[i].Module_Name +
            '</a></li>');

            //check if the module id=3(recruitment) to initially have the Recruitment tab as active
            if(moduleId === 3) {
                $('#'+moduleId+'active').addClass('active');
            }
            else 
            {
                $('#'+moduleId+'active').removeClass('active');
            }

        }
        for(var i in modules)
        {
            var moduleId = modules[i].Module_Id;
            var moduleName = modules[i].Module_Name;
            var count = moduleId;
                //append the tab contents for each module 
                $('#tabContents').append('<div class="tab-pane" id="collapse'
                    +moduleName+'"><p><header class="col-md-12 col-xs-12 paddingCls" id="moduleheaders'
                    +count+'"></header><div class="col-md-12 col-xs-12 paddingCls" style="overflow-y:scroll;max-height:600px;overflow-x: hidden;"><div class="col-md-12 col-xs-12 paddingCls"></div><div id="checkboxDiv'
                    +count+'"></div></div>');

                //append the headers 
                $('#moduleheaders'+count).append('<label class="col-md-6 col-xs-6 paddingCls checkbox checkbox-secondary"></label><div class="col-md-6 col-xs-6 paddingCls"><label class="control-label col-md-3 col-xs-2 paddingCls text-center hidden"><i class="visible-xs visible-sm mdi-action-visibility"></i><span class="hidden-xs hidden-sm">Approval</span></label><label class="control-label  col-md-3 col-xs-2 paddingCls text-center hidden"><i class="visible-xs visible-sm mdi-content-add"></i><span class="hidden-xs hidden-sm">Claim Override</span></label><label class="control-label  col-md-3 col-xs-2 paddingCls text-center"><i class="visible-xs visible-sm mdi-editor-mode-edit"></i><span class="hidden-xs hidden-sm">Notifications</span>');

                //append the checkAll checkbox
                $('#checkboxDiv'+count).append('<label class="col-md-6 col-xs-6 paddingCls checkbox checkbox-secondary text-center" hidden style="margin-top:-5px;"></label><div class="col-md-6 col-xs-6 paddingCls" id="checkAllIndividualApprovals'
                +count+'"></div>');

                // append the checkbox for every form 
                $('#checkAllIndividualApprovals'+count).append('<div class="col-md-3 col-xs-2 paddingCls checkbox checkbox-secondary text-center hidden"><label><input type="checkbox" source="permissions" class="md-checkbox All" id="ApprovalAll-'
                    +moduleId+'"><span class="checkbox-material"><span class="check"></span></span></label></div><div class="col-md-3 col-xs-2 paddingCls checkbox checkbox-secondary text-center hidden"><label><input type="checkbox" source="permissions" class="md-checkbox All" id="ClaimOverrideAll-'
                    +moduleId+'"><span class="checkbox-material"><span class="check"></span></span></label></div><div class="col-md-3 col-xs-2 paddingCls checkbox checkbox-secondary text-center"><label><input type="checkbox" source="permissions" class="md-checkbox All" id="NotificationsAll-'
                    +moduleId+'"><span class="checkbox-material"><span class="check"></span></span></label></div>');

                // make the recruitment as active class
                if(moduleId ===3) {
                    $('#collapse'+moduleName).addClass('active');
                }
                else 
                {
                    $('#collapse'+moduleName).removeClass('active');
                }

                //show the form names in ther permissions tab 
                for(var j in forms)
                {
                    //sho the check boxes for individual permissions 
                    if(modules[i].Module_Id === forms[j].Module_Id) {
                            var count = moduleId;
                            var formId = forms[j].Form_Id;
                            var fromName = forms[j].Form_Name;
                            $('#checkboxDiv'+count).append('<label class="col-md-6 col-xs-6 paddingCls checkbox checkbox-secondary '
                            +moduleName+' ">'
                            +fromName+'</label><div class="col-md-6 col-xs-6 paddingCls" id="checkRights'+j+'"></div>');

                            $('#checkRights'+j).append('<div class="col-md-3 col-xs-2 paddingCls checkbox checkbox-secondary text-center hidden"><label><input type="checkbox" source="permissions" class="md-checkbox" id="Approval-'
                            +formId+'"><span class="checkbox-material"><span class="check"></span></span></label></div><div class="col-md-3 col-xs-2 paddingCls checkbox checkbox-secondary text-center hidden"><label><input type="checkbox" source="permissions" class="md-checkbox" id="ClaimOverride-'
                            +formId+'"><span class="checkbox-material"><span class="check"></span></span></label></div><div class="col-md-3 col-xs-2 paddingCls checkbox checkbox-secondary text-center"><label><input type="checkbox" source="permissions" class="md-checkbox" id="Notifications-'
                            +formId+'"><span class="checkbox-material"><span class="check"></span></span></label></div>');
                            for(var s in subForms) {
                                //sho the check boxes for individual permissions 
                                    if(forms[j].Form_Id === subForms[s].Sub_Form) {
                                        var count = moduleId;
                                        var formId = subForms[s].Form_Id;
                                        var fromName = subForms[s].Form_Name;
                                        $('#checkboxDiv'+count).append('<div class="row"><label class="col-md-6 col-xs-6 paddingCls checkbox checkbox-secondary '
                                        +moduleName+' " style="padding-left: 25px !important;font-weight: 400;">'
                                        +'+'+fromName+'</label><div class="col-md-6 col-xs-6 paddingCls" id="checkRights'+formId+'"></div>');

                                        $('#checkRights'+formId).append('<div class="col-md-3 col-xs-2 paddingCls checkbox checkbox-secondary text-center hidden"><label><input type="checkbox" source="permissions" class="md-checkbox" id="Approval-'
                                        +formId+'"><span class="checkbox-material"><span class="check"></span></span></label></div><div class="col-md-3 col-xs-2 paddingCls checkbox checkbox-secondary text-center hidden"><label><input type="checkbox" source="permissions" class="md-checkbox" id="ClaimOverride-'
                                        +formId+'"><span class="checkbox-material"><span class="check"></span></span></label></div><div class="col-md-3 col-xs-2 paddingCls checkbox checkbox-secondary text-center"><label><input type="checkbox" source="permissions" class="md-checkbox" id="Notifications-'
                                        +formId+'"><span class="checkbox-material"><span class="check"></span></span></label></div></div>');
                                }
                            }
                    }
                    
                }
        }
        //check whether roles exists for the particular employee group 
        if(mainFormRoles.length>0 || subFormRoles.length > 0)
        {
            var moduleName = '';
            // loop through the available modules 
            for(var i in modules) {
                    var moduleId = modules[i].Module_Id; //module id 
                    moduleName = modules[i].Module_Name; //module name 
                    formRolesCount = 0; //reset the form roles count for every module
                    //loop through the available groups 

                    // concat forms and subForms
                    let formAndSubForms = forms.concat(subForms);
                    for(var k in formAndSubForms)
                    {
                        //check if the module id of formAndSubForms and the module id is equal 
                        if(formAndSubForms[k].Module_Id === moduleId) {
                            var formId = formAndSubForms[k].Form_Id; 
                            let mainFormAndSubFormRoles = mainFormRoles.concat(subFormRoles);
                            //loop through the available roles for the particular group 
                            for(var j in mainFormAndSubFormRoles){
                                //get the count of the formAndSubForms in the particular module 
                                 formRolesCount = $('.'+moduleName).length;
                                 //form id of the access rights 
                                 var mainFormRolesFormId = mainFormAndSubFormRoles[j].Form_Id;
                                    //check if form id is equal to the formid of the access rights 
                                     if(formId === mainFormRolesFormId)
                                     {
                                        //permissions for particular form 
                                        var roleApproval = mainFormAndSubFormRoles[j].Role_Approval;
                                        var roleClaimOverride = mainFormAndSubFormRoles[j].Role_Claim_Override;
                                        var roleNotifications = mainFormAndSubFormRoles[j].Role_Notification;

                                        //enable the respective permissions if available 
                                        roleApproval === 1 ? (roleApprovalCount++, $('#Approval-'+formId).prop('checked',true)) : $('#Approval-'+formId).prop('checked',false);
                                        roleClaimOverride === 1 ? (roleClaimOverrideCount++, $('#ClaimOverride-'+formId).prop('checked',true)) : $('#ClaimOverride-'+formId).prop('checked',false);
                                        roleNotifications === 1 ? (roleNotificationsCount++,$('#Notifications-'+formId).prop('checked',true)) : $('#Notifications-'+formId).prop('checked',false);
                                     }
                            }
                        }
                    }


                    //check whether the particular permissions are available for all forms and enable the 'ALL' checkboxes of individual permissions 
                    formRolesCount === roleApprovalCount ? $('#ApprovalAll-'+moduleId).prop('checked',true) : $('#ApprovalAll-'+moduleId).prop('checked',false);
                    formRolesCount === roleClaimOverrideCount ?  $('#ClaimOverrideAll-'+moduleId).prop('checked',true) : $('#ClaimOverrideAll-'+moduleId).prop('checked',false);                
                    formRolesCount === roleNotificationsCount ? $('#NotificationsAll-'+moduleId).prop('checked',true) : $('#NotificationsAll-'+moduleId).prop('checked',false);
                    //reset the counts 
                    roleApprovalCount = 0;
                    roleClaimOverrideCount = 0;
                    roleNotificationsCount = 0;  
            }
            // check/uncheck the 'Check All' checkbox
            checkUncheckAllCheckbox();
        }
        else 
        {
            /**disable all the permissions */
            enableDisableAllPermissions(false);  
        }
    }

    /** check/uncheck the 'Check All' checkbox */
    function checkUncheckAllCheckbox()
    {
        //get the count of the permissions which all checked 
        var checkedAllClassCount = $('.All').filter(':checked').length;
        //check the count of the checked class count is equal to the  available roles
        if(moduleCount*rolesOptionsCount === checkedAllClassCount) {
            //if all permissions are available in all modules, then check the select all checkbox 
            $('#all').prop('checked',true);
        }
        else {
            //if not all permissions are available, uncheck the select all checkbox 
            $('#all').prop('checked',false);
        }
    }

    /**to enable or disable all the permissions */
    function enableDisableAllPermissions(checked)
    {
            //to enable/disable all the checkboxes if there is no mainForm roles from the backend or if the check all option is unchecked 
            for(var i in modules)
            {
                var moduleId = modules[i].Module_Id;
                
                    //check/uncheck the approvalAll, ClaimOverrideAll and NotificationsAll checkboxes 
                    $('#ApprovalAll-'+moduleId).prop('checked',checked);
                    $('#ClaimOverrideAll-'+moduleId).prop('checked',checked);
                    $('#NotificationsAll-'+moduleId).prop('checked',checked);

                    // concat forms and subForms
                    let formAndSubForms = forms.concat(subForms);
                    for(var j in formAndSubForms)
                    {
                        //check whether the formAndSubForms belong to the recruitment module 
                        if(modules[i].Module_Id === formAndSubForms[j].Module_Id) {
                                var formId = formAndSubForms[j].Form_Id;
                                //uncheck the Approval, ClaimOverride and Notification access of the individual formAndSubForms 
                                $('#Approval-'+formId).prop('checked',checked);
                                $('#ClaimOverride-'+formId).prop('checked',checked);
                                $('#Notifications-'+formId).prop('checked',checked);
                            
                        }
                    }
                
            }
            $('#all').prop('checked',checked);
    }


    // clear lock and close the modals
    function closeEditForm()
    {
        setMask('#wholepage');
        // clear lock for the record
        clearLockAPI(orgCode, 'Roles Template', chosenRoleTemplate, employeeId, '#listRolesTemplate', atsBaseUrl, function(error, data) {
            if (data) {
                // close the add/edit form
                removeMask();
                $('#modalCloseRolesTemplate').modal('hide');
                cancelForm();
            }
        });
    }

    /** delete the roles template record*/ 
    function deleteRolesTemplate()
    {
        //check if the role template is chosen 
        if (chosenRoleTemplate > 0 && !isNaN(chosenRoleTemplate)) {
            setMask('#wholepage');

            //set lock before deleting the record 
            setLockAPI(orgCode, 'Roles Template', chosenRoleTemplate, employeeId, '#listRolesTemplate', atsBaseUrl, function(error, data) {
                if (data) {
                        var mutation = `
                            mutation ($templateId:Int!,$employeeId:Int!) 
                                { deleteRolesTemplate(templateId:$templateId,employeeId:$employeeId) 
                                    { message errorCode validationError}
                                }
                            `;
                        //delete roles template record 
                        $.ajax({
                            type: 'POST',
                            async: false,
                            url: atsBaseUrl,
                            headers:getGraphqlAPIHeaders(ipAddress),
                            data: JSON.stringify({
                                query: mutation,
                                variables: {
                                    templateId: chosenRoleTemplate,
                                    employeeId: employeeId
                                }
                            }),
                            success: function(result) {
                                if (result.data) {
                                    //clear lock and refresh the list after success 
                                    clearLockAPI(orgCode, 'Roles Template', chosenRoleTemplate, employeeId, '#listRolesTemplate', atsBaseUrl, function(error, data) {
                                        if (data) {
                                            removeMask();
                                            listRolesTemplates(0);
                                            $(window).scrollTop(0);
                                            jAlert({
                                                panel: $('#listRolesTemplate'),
                                                msg: 'Roles Template deleted successfully',
                                                type: 'info'
                                            });
                                        }
                                    });
                                } else {
                                    //clear lock and show the corresponding error message if failure 
                                    clearLockAPI(orgCode, 'Roles Template', chosenRoleTemplate, employeeId, '#listRolesTemplate', atsBaseUrl, function(error, data) {
                                        if (data) {
                                            removeMask();
                                            //handle errors while delete 
                                            var error = JSON.parse(result.errors[0].message);
                                            var errorCode = error.errorCode;
                                            $(window).scrollTop(0);
                                            switch (errorCode) {
                                                //technical errors from backedn 
                                                case 705:
                                                case 706:
                                                    jAlert({
                                                        panel: $('#listRolesTemplate'),
                                                        msg: 'There seems to be some technical issues. Please try after some time',
                                                        type: 'warning'
                                                    });
                                                    break;
                                                case 719: //access denied 
                                                    jAlert({
                                                        panel: $('#listRolesTemplate'),
                                                        msg: 'Sorry, Access denied',
                                                        type: 'warning'
                                                    });
                                                    break;
                                                    //functional errors 
                                                case 900:
                                                case 757:
                                                default:
                                                    jAlert({
                                                        panel: $('#listRolesTemplate'),
                                                        msg: 'Something went wrong. Please contact system administrator',
                                                        type: 'warning'
                                                    });
                                                    break;
                                            }
                                        }
                                    });
                                }
                            },
                            error:function(result)
                            {
                                removeMask();
                                // error while deleting the record
                                $(window).scrollTop(0);
                                jAlert({
                                    panel: $('#listRolesTemplate'),
                                    msg: 'Something went wrong. Please contact system administrator.',
                                    type: 'warning'
                                });
                            }
                            
                        });
                }
            });

        }
        else 
        {
            
            // if no role template is chosen
            $(window).scrollTop(0);
           jAlert({ panel: $('#listRolesTemplate'), msg: 'Kindly select a Role Template.', type: 'info' });
        }
    }

    /**set validation errors in fields if thrown from backend */ 
   function displayValidationErrors(validationErros) {
        //JSON of validation errors and codes from backend 
        var validationErrorIds = {
            'ERR-689' : 'rolesTemplateName',
            'ERR-678' : 'description',
            'ERR-683' : 'rolesTemplateName'
        }
        // check the validation error code and set the error accordingly
        for (var key in validationErros) {
            
                // add/remove validation errors to the fields
                addRemoveValidationError('add',validationErrorIds[key],validationErros[key]);
        }
    }


    /**add or remove the validation errors  */
    function addRemoveValidationError(action,fieldId,message) {
        //set the validation errors to the fields when thrown from backend 
        if(action === 'add')
        {
            $('#'+fieldId).addClass('validation');
                    $.validator.addMethod(
                        fieldId,
                        function() {
                            return false;
                        },
                        message
                    );
                    var id = fieldId.replace(/"/g, '');
                    $.validator.addClassRules('validation', { [id]: true });

                    $('#' + fieldId).valid();
        }
        else 
        {
            //remove the validation errors that is set 
            $('#'+fieldId).removeClass('validation');
                    $.validator.addMethod(
                        fieldId,
                        function() {
                            return true;
                        },
                        ''
                    );
                    var id = fieldId.replace(/"/g, '');
                    $.validator.addClassRules('validation', { [id]: false });

                    $('#' + fieldId).valid();
        }
    }
    

    /**to enable scrolling in first modal when second modal is closed  */
    function openPreviousModal() {
        //check whether any other modal is opened and add open class to first modal 
        $('body').on('hidden.bs.modal', function () {
            if($('.modal.in').length > 0)
            {
                $('body').addClass('modal-open');
            }
        });
    }

   /**close the add/update modal,reset the variables and show the list */ 
   function cancelForm() {
        $(window).scrollTop(0);
        // reset the flags
        addRoleTemplates = 0;
        editRoleTemplates = 0;
        // set the form dirty to false
        isDirtyFormRolesTemplate = false;
        $('#listRolesTemplate').show(); //show the list page
        // close the add/edit modal
        $('#modalCreateRolesTemplate').modal('toggle'); 
        $('#modalCreateRolesTemplate').on('hidden.bs.modal', function (e) {
            $('body').removeClass('modal-open');
        });
    }

    // list Roles Template details
    function listRolesTemplates(isSearch=0)
    {
        setMask('#wholepage');

        //variables to list roles template records 
        var variables = {
            searchString : $('#search').val() ? $('#search').val() : $('#searchrolestemp').val() ? $('#searchrolestemp').val() : '',
            isDropDownCall : 0,
            employeeId: employeeId
        }
        // query/mutation to list role templates
        var mutation =`query ($searchString:String,$isDropDownCall : Int!,$employeeId:Int!) 
                    { listRolesTemplate(searchString:$searchString,isDropDownCall:$isDropDownCall,employeeId:$employeeId) 
                        { message errorCode rolesTemplates 
                            { Template_Id Template_Name Description AssociatedGroupCount Group_Id Group_Name}
                        }
                    }`;
        // list roles templates
        $.ajax({
            method: 'POST',
            url: atsBaseUrl,
            headers: getGraphqlAPIHeaders(ipAddress),
            data: JSON.stringify({
                query: mutation,
                variables: variables
            }),
            success: function(result) {
                if(result.data)
                {
                    // list of roles templates 
                    rolesTemplates = result.data.listRolesTemplate.rolesTemplates;
                    //update the list page 
                    $('#listRolesTemplatesId').html('');
                    $('#listImage').html('');
                    $('#listImage1').html('');
                    //classes for pagination 
                    var paginationId = '#listRolesTemplatesId';
                    var dataclass = '.rolestemplatedetails';
                    isDirtyFormRolesTemplate = false;
                    removeMask();
                    if(rolesTemplates.length > 0) {
                        //set the roles template details in the card 
                        for (var key = 0; key < rolesTemplates.length; key++) {
                            //template name 
                            var templateName = rolesTemplates[key].Template_Name;
                            var templateNameShort = '';

                            //get the initials of template name 
                            templateName = templateName.match(/\b(\w)/g);
                            //convert the initials of template name in caps 
                            templateName = templateName.join('').toUpperCase();
                            templateName = templateName.length > 4 ? templateName.substring(0,4) : templateName;
                            templateName.length >= 2 ? templateNameShort = templateName.substring(0,2) : templateNameShort = templateName;
                            // template description 
                            var description = rolesTemplates[key].Description;
                            // check if the length of the description is greater than 100 and append '...' 
                            description = description ? description.length <= 100 ? description : description.substring(0,100)+'...' : '-';

                            $('#listRolesTemplatesId').append(
                                '<div templateId= "' +
                                    rolesTemplates[key].Template_Id +
                                    '" templateKey= "' +
                                    key +
                                    '" style="cursor:pointer" class="col-lg-6 col-md-6 col-sm-6 rolestemplatedetails workflows"> <div class="panel widget-member2 recruitment-cards"> <div class = "row"><span class="list-icons" templateId= "' +
                                    rolesTemplates[key].Template_Id +
                                    '" templateKey= "' +
                                    key +
                                    '" id = "deleterolerecord"><i class="fa fa-times edit-icons" title="Delete" aria-hidden="true"></i></span><span class="list-icons" templateId= "' +
                                    rolesTemplates[key].Template_Id +
                                    '" templateKey= "' +
                                    key +
                                    '" id = "editrolerecord"><i class="fa fa-pencil edit-icons" title="Edit" aria-hidden="true"></i></span></div> <div class="row cardsclass"><div class="col-lg-2 col-xs-3 card-display-name" id="listImage"> <div class="listImage"> '
                                    +templateName+' </div> </div> <div class="col-lg-2 col-xs-3 card-display-name-short" id="listImage1"> <div class="listImage"> '
                                    +templateNameShort+' </div></div> <div class="col-lg-10 col-xs-9">  <div class="clearfix"> <h3 class="m-t-0 member-name workflow-card-heading roles-template-decription"><strong class="card-heading">' +
                                    rolesTemplates[key].Template_Name +
                                    '</strong> </h3> </div> <div class="row"> <div class="col-sm-12 m-t-10 card-body-content roles-template-decription"> ' +
                                    description +                                       
                                    ' </div></div> </div> </div><div class= "row" style="margin-top:10px; border-top : 1px solid lightsteelblue;">  <div class="list-options"> <a templateId= "' +
                                    rolesTemplates[key].Template_Id + '" id="updatePermissions">View Permissions</a>  </div> </div> </div> </div>'
                            );
                        }
                        //show the search and pagination if the record is greater than 0 
                        $('#searchfilter,#paginationsm,#searchfiltersm,.client-verticalstyle').show();

                        $('.custom-pagination').html('');
                        var paginationSize = 7;
                        //function to apply pagination 
                        $.pagination($('#listRolesTemplatesId .rolestemplatedetails').length, 6, paginationId, dataclass,paginationSize); // total No.of Records and max limit per page
                        //show the success message based on the action 
                         if(addRoleTemplates)
                         {
                            $(window).scrollTop(0);
                            jAlert({
                                panel: $('#listRolesTemplate'),
                                msg: 'Roles Template created and permissions associated successfully',
                                type: 'info'
                            });
                            addRoleTemplates = 0;
                         }
                        else if(editRoleTemplates)
                        {
                            $(window).scrollTop(0);
                            jAlert({
                                panel: $('#listRolesTemplate'),
                                msg: 'Roles Template updated successfully',
                                type: 'info'
                            });
                            editRoleTemplates = 0;
                        }
                        
                    }
                    else 
                    {
                        var paginationSize = 7;

                        //set the pagination default to 1 page if there are no employee records 
                        $.pagination(1, 6, paginationId, dataclass,paginationSize);
                        $('#listRolesTemplatesId').html('');

                            //show the no records found message and image 
                            var img = pageUrl() + 'images/records-not-found.png';
                            $('#listRolesTemplatesId').append(
                                '<img class="recruitment-search-not-found" src="' + img + '" alt="searchnotfound">'
                                ); 
                            // check if the list action is not search
                        if (!isSearch) {
                            // hide the search and pagination
                            $('#searchfilter,#paginationsm,#searchfiltersm,.client-verticalstyle').hide();
                            // show no records message
                            $('#listRolesTemplatesId').append('<div class="recruitment-records-not-found">No roles template created yet.</div>');
                        } else {
                            // show the no records found for search
                            $('#listRolesTemplatesId').append('<div class="recruitment-records-not-found">No roles template match your search.</div>');
                        }
                    }
                }
                else 
                {
                        removeMask();
                        var error = JSON.parse(result.errors[0].message);
                        var errorCode = error.errorCode;
                        $(window).scrollTop(0);
                        switch (errorCode) {
                            //functional errors 
                            case 705:
                            case 706:
                                jAlert({
                                    panel: $('#listRolesTemplate'),
                                    msg: 'There seems to be some technical issues. Please try after some time.',
                                    type: 'warning'
                                });
                                break;
                                //access denied 
                            case 719:
                                jAlert({
                                    panel: $('#listRolesTemplate'),
                                    msg: 'Sorry, Access denied',
                                    type: 'warning'
                                });
                                break;
                                //functional errors 
                            case 758:
                            default:
                                jAlert({
                                    panel: $('#listRolesTemplate'),
                                    msg: 'Something went wrong. Please contact system administrator.',
                                    type: 'warning'
                                });
                                break;
                        }
                }
            }, 
            error: function(result) {
                // error while listing roles template
                removeMask();
                $(window).scrollTop(0);
                jAlert({
                    panel: $('#listRolesTemplate'),
                    msg: 'Something went wrong. Please contact system administrator.',
                    type: 'warning'
                });
            }
        });



    }



       

});