<?php
	$formName 		= $this->formName;
	$formTitle		= $formName.' Report';
	$this->headTitle($formTitle);
	$reportUser 	= $this->reportUser;
	$orgForm 		= $this->orgForm;
	$empForm 		= $this->empForm;
	$gridKeyArr 	= $this->gridKeyArr;
	$columnArr		= $this->columnArr;
	$filterData 	= $this->filterData;
	$labelArray 	= $this->labelArray;
	$tableArray 	= $this->tableArray;
	$employeeName 	= $this->employeeName;
	$reportData 	= $this->rowDriRep;
	$headerTitle    = $this->headerTitle;
	$headerLogo     = $this->headerLogo;
	$headerString   = $this->headerString;
    $address        = $this->address;
    $panTan         = $this->panTan;
	$dateformat 	= $this->dateformat;
	$lastPayslipMonth = $this->lastPayslipMonth;
	$orgDetails 	= $this->orgDetails ;
	$loginEmpId 	= $this->loginEmpId;
	$loginEmpName 	= $this->logEmpName;
	$reportDataCount= count($reportData);
	$gridKeyCount 	= count($gridKeyArr);
	$serviceProviderId = $this->serviceProviderId;
	$managerNames =$this->managerNames;

	if(!empty($dateformat))
	{
		$dformat = $dateformat['bs'];	
	}
	else
	{
		$dformat = 'dd/mm/yyyy';
	}
	$bankId = $this->bankId;
	$linkVal = explode('linkValue/',$_SERVER['REQUEST_URI']);
?>
<input type="hidden" name="serviceProviderId" id="serviceProviderId" value="<?php echo  $serviceProviderId; ?>" />	
<input type="hidden" name="fieldForce" id="fieldForce" value="<?php echo  $orgDetails['Field_Force']; ?>" />

<?php if($linkVal[1] == 'timesheet' || $linkVal[1] == 'timesheet-comprehensive')
{
$timesheetSettings 	= $this->timesheetSettings ;
?>
<input type="hidden" name="timesheetPeriod" id="timesheetPeriod" value="<?php echo  $timesheetSettings['Timesheet_Report_Download_Period_In_Days']; ?>" />

<?php
} 
else
{ ?>
<input type="hidden" name="timesheetPeriod" id="timesheetPeriod" value="" />
<?php 
} ?>

<?php 	if(($formName == "Eft Monthly" || $formName == "Eft Hourly")  && empty($bankId))
	  	{ ?>
			<div class="container" style="padding: 20px;">
				<div class="alert alert-info alert-dismissible">
					<a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
					<strong>Info!</strong> Please add the organization account(Organization->Eft Configuration).organization account should be in Active status.
				</div>
			</div>
	   <?php 
	   }  
  else { ?>

	
<!--By default set print panel as hidden and show when user clicks on print-->
	<div class="row">
		
		<div class="hidden col-md-12" id="printPanel">
			<div class="col-md-12" style="margin-top: 20px">
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" aria-hidden="true" id="PrintScreen" >
					<i class=""></i> Print
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" aria-hidden="true" id="exitPrint" >
					<i class=""></i> Back
				</button>
			</div>
			
			<div class="preview_header" name="printable">
				<table width ="98%" style="border-collapse:collapse;border-bottom:1px solid #000; margin-bottom: 20px;" cellpadding="0">
					<tbody>
                        <tr><?php if($headerLogo != ''){echo '<td style="width:30%;"> <img class="reportLogoSize" style="padding-right:10px;" src="'.$headerLogo.'"> </td>';} ?>
                            <td style="width: 70%;text-align: right;">
                                <span style="font-weight:bold;font-size:20px;"><?php echo $headerTitle; ?></span>
                                <span><?php echo $address.$panTan; ?></span>
                                <!--<span><?php echo $headerString; ?></span>-->
                            </td>
                        </tr>
					</tbody>
				</table>
			</div>
			<div class="printable_portion" name="printable" style=""></div>
            <div style="text-align: right;padding-right: 35px;"><span><?php echo $headerString; ?></span></div><br>
		</div>
	</div>


<div class="col-md-12 portlets">
	<div class="panel" id="gridPanelReports">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <?php echo $this->formName; ?></h3>
		</div>
		<div class="panel-content">
			<div class="m-b-10">
				<!-- Message for the eft monthly report -->
				<?php if(!empty($reportDataCount) && !empty($gridKeyCount) && 
				$linkVal[1] == 'eft-monthly' || $linkVal[1] == 'eft-hourly') { ?>
					<div style="margin-bottom: 1em;color: grey;margin-left: 0.1em;">Note : Kindly apply respective filters to get the report</div>
				<?php } ?>
				<!-- Filter Button in Grid Toolbar -->
				<?php if(!empty($reportDataCount) && !empty($gridKeyCount)) { ?>
					<a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterHrReports">
						<i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm"> Filter</span>
					</a>
				<?php } ?>
				<!-- Export Print -->
				<?php
				if($reportUser['Optional_Choice'] == 1 && !empty($reportDataCount) && !empty($gridKeyCount))
				{?>
					<!-- Export as EXCEL -->
					<button type="button" class="btn btn-white btn-embossed visible-lg-* toolbar-icons" id="exportExcel" title="Export as EXCEL" >
						<i class="fa fa-table"></i><span class="hidden-xs hidden-sm"> EXPORT EXCEL</span>
					</button>

					<?php
					$saveReportStructureForms = array('Monthly Salary Payslip','Bank Salary Statement','Fixed Health Insurance','Timesheet Comprehensive','Esi Monthly','Lwf', 'Professional Tax Monthly');
					if(in_array($formName,$saveReportStructureForms)) 
					{
					?>
					<button type="button" class="btn btn-white btn-embossed visible-lg-* toolbar-icons" id="saveReportStructure" title="Save Report Structure">
								<i class="fa fa-save"></i><span class="hidden-xs hidden-sm"> Save Report Structure</span>
					</button>
					<?php } ?>

					<?php
					if($formName === "Eft Monthly" || $formName === "Eft Hourly" || $formName === "Esic Hourly" || $formName === "Esic Monthly") {
					?>
					<button type="button" class="btn btn-white btn-embossed visible-lg-* toolbar-icons" id="exportAsText" title="Export as Text file" >
								<i class="fa fa-file-text-o"></i><span class="hidden-xs hidden-sm"> Text</span>
					</button>
					<?php }
					elseif($formName === "Uan Based Ecr" || $formName === "Uan Based Ecr Hourly" || strtolower($formName)== 'uan based ecr(arrear)' ) { ?>
						<a class="btn btn-white btn-embossed visible-lg-* toolbar-icons" id="exportAsText" href="" download="ECR.txt">
						<i class="fa fa-file-text-o"></i><span class="hidden-xs hidden-sm"> Text</span>
					</a>

					<?php }
				
				
				}?>
			</div>
	
			<div id="divGrid">
			
			<!--  Grid Panel -->
			<?php
			if(!empty($reportDataCount) && !empty($gridKeyCount))
				{
					?>
						<table class="table dataTable table-striped table-dynamic table-hover" id="tableReports">
							<thead>
									<?php
									if(count($gridKeyArr)>2)
									{
										echo '<th> </th>';
									}
									foreach ($gridKeyArr as $key => $row) {
										if($key < 6)
										{
											$gridHeaders = str_replace('_', ' ', $row);
											echo '<th>'. $gridHeaders .'</th>';
										}
									}
									?>
							</thead>
							<tbody>
							
							</tbody>
						</table>
						<?php
					//}
				}
				else
				{
					?>
					<p>No records found</p>
					<?php
				}
				?>
			</div>	
		</div>
	</div>
</div>

<!--/********** Bar Chart *************/-->
<div class="col-md-12 portlets">
	<div class="panel" id="barChartPanel">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> BAR Chart</h3>
		</div>
		<div class="panel-content">
			<div class="col-md-12">
				<div class="m-b-10"></div>
			</div>
			<div>
				<div id="barChartReports"></div>
			</div>
		</div>
	</div>
</div>

<!--/********** Pie Chart *************/-->
<div class="col-md-12 portlets">
	<div class="panel" id="pieChartPanel">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i>PIE Chart</h3>
		</div>
		<div class="panel-content">
			<div class="col-md-12">
				<div class="m-b-10"></div>
			</div>
			<div>
				<div id="pieChartReports"></div>
			</div>
		</div>
	</div>
</div>	

<!--Filter Form-->
<?php if(!empty($reportDataCount) && !empty($gridKeyCount)) { ?>
	<div class="builder" id="filterPanelReports">
	<div id="closeFilterReports"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
<?php } else { ?>
	<div class="filter" id="filterPanelReports">
<?php }?>
	
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel" style="width: 100%;" id="filterResetReports">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="filterApplyReports">Apply</button>
				<?php
				
				for($i=0;$i<count($labelArray);$i++)
				{
					if(($labelArray[$i] == 'Service Provider' && $orgDetails['Field_Force']==1 && $linkVal[1] != 'employee-step-increment') || ($labelArray[$i] != 'Service Provider'))
					{
					$idvalue = $i;
					$filterId = 'id'.$idvalue;
					?>
					<div class="form-group lblEftTransferModeBasedHidden<?php echo $filterId;?>">
					<label><?php echo $labelArray[$i]; ?></label>
					<?php
					if($tableArray[$i] == 'DPicker')
					{
						if($linkVal[1] == 'timesheet' || $linkVal[1] == 'timesheet-comprehensive')
						{
							$currentDate = date('Y-m-d');
							//if the current date is saturday we can use that day else we need to find the upcoming saturday and prefill it in the weekend filter
							if(date('N', strtotime($currentDate))==6)
							{
								$sdate =  $ldate = $currentDate;
							}
							else 
							{
								$sdate =  $ldate = date("Y-m-d", strtotime("next saturday"));
							}
						}
						else 
						{
							$sdate = date('Y-m-01');
							$ldate = date('Y-m-d');	
						}

						if($linkVal[1] == 'absentees' || $linkVal[1] == 'attendance-and-absence-overview')
						{
							$sdate = date('Y-m-d');
							$ldate = date('Y-m-d');	
						}
							

						$sdate = DateTime::createFromFormat('Y-m-d', $sdate);$sdate = $sdate->format("'".$dateformat['php']."'");
						$ldate = DateTime::createFromFormat('Y-m-d', $ldate);$ldate = $ldate->format("'".$dateformat['php']."'");
						$sdate = str_replace("'", "", $sdate);$ldate = str_replace("'", "", $ldate);
						?>
						<input type="hidden" name="startDate" id="startDate" value="<?php echo  $sdate; ?>" />
						<?php
						if($linkVal[1] == 'new-joinees' || $linkVal[1] == 'probation') { ?>
							<div class="input-daterange b-datepicker input-group" data-date-format="<?php echo $dformat; ?>" id="datepicker">
							<input type="text" class="input-md form-control" name="start" id="filterDateBegin<?php echo $filterId;?>" value= "<?php echo $sdate; ?>" data-orientation="top" placeholder="Start"/>
							<span class="input-group-addon">to</span>
							<input type="text" class="input-md form-control" name="end" id="filterDateEnd<?php echo $filterId;?>" value= "<?php echo $ldate; ?>" data-orientation="top" placeholder="End"/>
							</div>
						<?php } 
						elseif(($linkVal[1] == 'attendance' && $filterId=='id3') || ($linkVal[1] == 'attendance-summary-hourly' && $filterId=='id1') || ($linkVal[1] == 'attendance-summary-monthly' && $filterId=='id1') || ($linkVal[1] == 'attendance-muster-info' && $filterId=='id1')
						|| ($linkVal[1] == 'attendance-register' && $filterId=='id1') || ($linkVal[1] == 'employee-utilization' && $filterId=='id1') || ($linkVal[1] == 'additional-wage-summary' && $filterId=='id1') || ($linkVal[1] == 'absentees' && $filterId=='id1')
						|| ($linkVal[1] == 'attendance-and-absence-overview' && $filterId=='id1')) { ?>
							<div class="input-daterange b-datepicker input-group" data-date-format="<?php echo $dformat; ?>" id="datepicker">
							<input type="text" class="input-md form-control" name="start" id="filterDateBegin<?php echo $filterId;?>" value= "<?php echo $sdate; ?>" data-orientation="top" placeholder="Start"/>
							<span class="input-group-addon">to</span>
							<input type="text" class="input-md form-control" name="end" id="filterDateEnd<?php echo $filterId;?>" value= "<?php echo $ldate; ?>" data-orientation="top" placeholder="End"/>
							</div>
						<?php } 

						elseif( $linkVal[1] == 'reimbursement-bank-statement' && $filterId=='id1' ) { ?>
							<div class="input-daterange b-datepicker input-group" data-date-format="<?php echo $dformat; ?>" id="datepicker">
							<input type="text" class="input-md form-control" name="start" id="filterDateBegin<?php echo $filterId;?>" value= "<?php echo $sdate; ?>" data-orientation="top" placeholder="Start"/>
							<span class="input-group-addon">to</span>
							<input type="text" class="input-md form-control" name="end" id="filterDateEnd<?php echo $filterId;?>" value= "<?php echo $ldate; ?>" data-orientation="top" placeholder="End"/>
							</div>
						<?php } 

						elseif( $linkVal[1] == 'employee-wise-expenses' && $filterId=='id1' ) { ?>
							<div class="input-daterange b-datepicker input-group" data-date-format="<?php echo $dformat; ?>" id="datepicker">
							<input type="text" class="input-md form-control" name="start" id="filterDateBegin<?php echo $filterId;?>" value= "<?php echo $sdate; ?>" data-orientation="top" placeholder="Start"/>
							<span class="input-group-addon">to</span>
							<input type="text" class="input-md form-control" name="end" id="filterDateEnd<?php echo $filterId;?>" value= "<?php echo $ldate; ?>" data-orientation="top" placeholder="End"/>
							</div>
						<?php }
							elseif( $linkVal[1] == 'attendance-shortage' && $filterId=='id1' ) { ?>
								<div class="input-daterange b-datepicker input-group" data-date-format="<?php echo $dformat; ?>" id="datepicker">
								<input type="text" class="input-md form-control" name="start" id="filterDateBegin<?php echo $filterId;?>" value= "<?php echo $sdate; ?>" data-orientation="top" placeholder="Start"/>
								<span class="input-group-addon">to</span>
								<input type="text" class="input-md form-control" name="end" id="filterDateEnd<?php echo $filterId;?>" value= "<?php echo $ldate; ?>" data-orientation="top" placeholder="End"/>
								</div>
							<?php } 

							elseif( $linkVal[1] == 'shift-report' ) { ?> 
								<div class="input-daterange b-datepicker input-group" data-date-format="<?php echo $dformat; ?>" id="datepicker"> 
								<input type="text" class="input-md form-control" name="start" id="filterDateBegin<?php echo $filterId;?>" value= "<?php echo $sdate; ?>" data-orientation="top" placeholder="Start"/> 
								<span class="input-group-addon">to</span> 
						
								<input type="text" class="input-md form-control" name="end" id="filterDateEnd<?php echo $filterId;?>" value= "<?php echo $ldate; ?>" data-orientation="top" placeholder="End"/> 
								</div> 
							<?php }  

							elseif( $linkVal[1] == 'shift-unassigned' ||  $linkVal[1] == 'timesheet' || $linkVal[1] == 'timesheet-comprehensive') { ?> 
								<div class="input-daterange b-datepicker input-group" data-date-format="<?php echo $dformat; ?>" id="datepicker"> 
								<input type="text" class="input-md form-control" name="start" id="filterDateBegin<?php echo $filterId;?>" value= "<?php echo $sdate; ?>" data-orientation="top" placeholder="Start"/> 
								<span class="input-group-addon">to</span> 

								<input type="text" class="input-md form-control" name="end" id="filterDateEnd<?php echo $filterId;?>" value= "<?php echo $ldate; ?>" data-orientation="top" placeholder="End"/> 
								</div> 
							<?php }
						else { ?>
							<div class="input-daterange b-datepicker input-group" data-date-format="<?php echo $dformat; ?>" id="datepicker">
							<input type="text" class="input-md form-control" name="start" id="filterDateBegin<?php echo $filterId;?>" data-orientation="top" placeholder="Start"/>
							<span class="input-group-addon">to</span>
							<input type="text" class="input-md form-control" name="end" id="filterDateEnd<?php echo $filterId;?>" data-orientation="top" placeholder="End"/>
						</div>						
					<?php } }
					elseif($tableArray[$i] == 'MPicker')
					{
						$currentMonth = '';$currentMonthStrtotime='';
						if($linkVal[1] == 'tds'){
							/** Get the last payslip 'month' and 'year' and set in the month picker for these reports */
							$currentMonth=date('Y-m-d', strtotime($lastPayslipMonth));//Format: '2020-02-26'
							$currentMonthStrtotime=strtotime($currentMonth);

							$tdsSnapShotStartDateStrtotime = strtotime('2020-04-01');
							/** The salary payslip - tax related snapshot details are updated from the month April 2020. So validate the last generated payslip month
							 * is greater than or equal to the month "April 2020" If yes, present the last generated payslip month in the filter form and get the tax 
							 * details for the last generated payslip month. Otherwise set present the month "April,2020" in the filter form and 
							 * get the tax details for the month April 2020
							 */
							if($currentMonthStrtotime >= $tdsSnapShotStartDateStrtotime){
								$currentMonth = date('F Y',$currentMonthStrtotime);
							}else{
								$currentMonth = date('F Y',$tdsSnapShotStartDateStrtotime);
							}
							// For the tds report set the minimum month as april,2020 as the form16 snapshot details are saved from this month
						?>
						<input type="hidden" name="lastPaySlipMonth" id="lastPaySlipMonth" value="<?php echo  $currentMonth; ?>" />
						<input type="text" class="b-datepicker vmonthMax vMonthClosure form-control vRequired closeMonthPicker" data-date-format="MM,yyyy" data-date-start-date="April,2020"
						data-view="1" data-date-min-view-mode=1 data-date-autoclose="true" id="filter<?php echo $filterId;?>" value="<?php echo $currentMonth;?>" data-orientation="top">
						<?php
						}else { 
							if($linkVal[1] == 'ecr-hourly' || $linkVal[1] == 'esic-hourly' || $linkVal[1] == "uan-based-ecr-hourly" || $linkVal[1] == 'esi-hourly' || $linkVal[1] == 'eft-hourly'
							|| $linkVal[1] == 'hourly-wage-payslip' || $linkVal[1] == 'lop-recovery')
							{
										/** Get the last payslip 'month' and 'year' and set in the month picker for these reports */
										$currentMonth=date('Y-m-d', strtotime($lastPayslipMonth));
										$currentMonthStrtotime=strtotime($currentMonth);
										$currentMonth = date('F Y',$currentMonthStrtotime);
							}
							elseif($linkVal[1] == 'ecr-monthly' || $linkVal[1] == 'esic-monthly' ||   $linkVal[1] == 'uan-based-ecr'|| $linkVal[1] == 'uan-based-ecr(arrear)' || $linkVal[1] === 'salary-register' 
							|| $linkVal[1] === 'payment-register'|| $linkVal[1] === 'monthly-payslip-comprehensive' || $linkVal[1]=='work-sheet' || $linkVal[1] == 'esi-monthly' ||  $linkVal[1] === 'bimonthly-salary-register' ||
								$linkVal[1] === 'monthly-salary-payslip' || $linkVal[1] === 'pay-bill' || $linkVal[1] === 'reimbursement-allowances'  
								|| $linkVal[1] === 'insurance-statement' || $linkVal[1] === 'ssnit-tier-1' || $linkVal[1] === 'ssnit-tier-2' || $linkVal[1] === 'provident-fund-detailed-report' || $linkVal[1] === 'wps'
								|| $linkVal[1] === 'bank-salary-statement' || $linkVal[1] === 'fixed-health-insurance')
							{
										/** Get the last payslip 'month' and 'year' and set in the month picker for these reports */
										$currentMonth=date('Y-m-d', strtotime($lastPayslipMonth));
										$currentMonthStrtotime=strtotime($currentMonth);
										$currentMonth = date('F Y',$currentMonthStrtotime);
							}
						?> 
						<input type="hidden" name="lastPaySlipMonth" id="lastPaySlipMonth" value="<?php echo  $currentMonth; ?>" />
						<input type="text" class="b-datepicker vmonthMax vMonthClosure form-control vRequired closeMonthPicker" data-date-format="MM,yyyy"
						data-view="1" data-date-min-view-mode=1 data-date-autoclose="true" id="filter<?php echo $filterId;?>" value="<?php echo $currentMonth;?>" data-orientation="top">
					<?php } 
					}
					elseif($labelArray[$i] == 'Employee Name' || $labelArray[$i] == 'Manager Name' || $labelArray[$i] == 'Name')
					{
						if(in_array($linkVal[1], array('attendance','leaves','short-time-off','compensatory-off','compensatory-off-balance')) && $labelArray[$i] == 'Manager Name'){ ?>
								<select class="form-control vRequired" name="Manager_Name"  id="filter<?php echo $filterId;?>"   data-search="true" data-placeholder="Manager Name">
									<option value="">All</option>
									<?php
										foreach ($managerNames as $row)
										{
											echo '<option value="'.$row['Employee_Id'].'">'.$row['Employee_Name'].'</option>';
										}
									?>
								</select>
						<?php }
						elseif($linkVal[1] == 'employee-wise-expenses'|| $linkVal[1] == 'additional-wage-summary' || $linkVal[1] == 'employee-step-increment')
						{?>
						
								<input type="hidden" name="loginEmpId" id="loginEmpId" value="<?php echo  $loginEmpId; ?>" />
								<select class="form-control vRequired" name="Employee_Name"  id="filter<?php echo $filterId;?>"   data-search="true" data-placeholder="Employee Name">
								<option value="">--Select--</option>
								<?php
								foreach ($employeeName as $empKey => $empRow) {
									if($empRow['value'] == $loginEmpId)
									{
										echo '<option value="'. $empRow['value'] .'" selected="selected">'. $empRow['text'] .'</option>';
									}
									else
									{
									echo '<option value="'. $empRow['value'] .'">'. $empRow['text'] .'</option>';
									}
								}
								?>
								</select>
								
						<?php }
					   else
					   {?>
						
						<div class="form-group">
							<input type="text" class="form-control" id="filter<?php echo $filterId;?>" placeholder="Employee Name" >
						</div>
				
						
					<?php }}
					elseif($tableArray[$i] == 'Amount') {
						?>
							<!--<input type="number" class="input-md form-control" name="filter<?php echo $filterId;?>" min="0" id="filter<?php echo $filterId;?>" placeholder="Maxmimum"/>-->
                            <div class="input-group">
                            <input type="number" class="input-md form-control" name="filterAmountStart<?php echo $filterId;?>" id="filterAmountStart<?php echo $filterId;?>" placeholder="Start"/>
							<span class="input-group-addon">to</span>
							<input type="number" class="input-md form-control" name="filterAmountEnd<?php echo $filterId;?>"  id="filterAmountEnd<?php echo $filterId;?>" data-orientation="top" placeholder="End"/>
                            </div>
						<?php							
					}
					elseif(($tableArray[$i] == 'Bulk_Upload_File_For' || $tableArray[$i] == 'Transaction_Type' || $tableArray[$i] == 'Debit_Account_Number') && ($linkVal[1] == 'eft-monthly' || $linkVal[1] == 'eft-hourly')) {
							?>
							<div class="eftTransferModeBasedHidden<?php echo $filterId;?>">
								<select class="form-control" data-search="true" id="filter<?php echo $filterId;?>" >
									<?php
									if($tableArray[$i] == 'Bulk_Upload_File_For'){
										foreach ($filterData[$i] as $key => $row)
										{
											echo '<option value="'.$key.'">'.$row.'</option>';
										}
									}
									elseif($tableArray[$i] == 'Transaction_Type'){
										foreach($filterData[$i] as $key=> $row)
										{
											echo '<option value="'.$key.'">'.$row.'</option>';
										}
									}else if($tableArray[$i] == 'Debit_Account_Number'){
										foreach ($filterData[$i] as $key => $row)
										{
											echo '<option value="'.$key.'">'.$row.'</option>';
										}
									}
									?>
								</select>
							</div>
							<?php
					}
					else
					{
						?>						
						<select class="form-control <?php echo str_replace(' ', '', $labelArray[$i]);?>" data-search="true" id="filter<?php echo $filterId;?>" >
						<?php
						//attendance summary monthly,summary hourly,attendance muster info and employee utilization we should not provide all option for service provider filter
						if(!(in_array($linkVal[1], array('attendance-summary-hourly','attendance-summary-monthly','employee-utilization')) 
						&& $labelArray[$i]=='Service Provider'))
						{ ?>
							<option value="">All</option>
						<?php 
						}
						if(is_array($filterData[$i]) && !empty($filterData[$i])&& isset($filterData[$i][0]) && array_key_exists('Child',$filterData[$i][0]))
						{
							foreach ($filterData[$i] as $key => $row)
							{
								if(isset($row['Department_Id']) && isset($row['Department_Name']))
								{

									echo '<option value="'. $row['Department_Id'] .'">'. $row['Department_Name'] .'</option>';
						
									if(isset($row['Child']) && !empty($row['Child']))
									{
										foreach($row['Child'] as $val =>$name)
										{
											
											if($name['Parent_Type_Id'] == $row['Department_Id'])
											{
												echo '<option value="'. $name['Department_Id'] .'">&nbsp;&nbsp;'. $name['Department_Name'] .'</option>';
												
												foreach($row['Child'] as $v =>$k)
												{
													if($k['Parent_Type_Id'] == $name['Department_Id'])
														echo '<option value="'. $k['Department_Id'] .'">&nbsp;&nbsp;&nbsp;&nbsp;'. $k['Department_Name'] .'</option>';
												}
												
											}
										}
									}
									
								}
								
							}                                    
						}
						else
						{
							if($linkVal[1] === 'form16-summary(monthly)' || $linkVal[1] === 'pt-annual-return(form-5a)'){
								$selectValue = (count($filterData[$i]) > 0) ? end($filterData[$i]) : '';
							}else
							{
								if($labelArray[$i] == 'Service Provider' && $orgDetails['Field_Force']==1 && 
								(in_array($linkVal[1], array('attendance-summary-hourly','attendance-summary-monthly','employee-utilization','absentees'))))
								{
									$selectValue = $serviceProviderId;
								}
								else 
								{
									$selectValue = '';
								}
							}

							foreach ($filterData[$i] as $key => $row)
							{
								if($selectValue!= '' && $key == $selectValue){
									echo '<option value="'.$key.'"  selected="selected">'.$row.'</option>';
								}else{
									echo '<option value="'.$key.'">'.$row.'</option>';
								}
							}

							if($linkVal[1] === 'form16-summary(monthly)' || $linkVal[1] == 'pt-annual-return(form-5a)'){
						?>
						<input type="hidden" name="form16SummaryMonthlyMaxAssessmentYear" id="form16SummaryMonthlyMaxAssessmentYear" value="<?php echo  $selectValue; ?>" />
						
						<?php }
					}?>
						</select>						
						<?php
					}
					?>
					</div>
					<?php
				}
				}
				?>
		</div>
	</div>
</div>

<!--/************ before exporting into pdf select required columns ****************/-->
<div class="modal fade" id="modalExportColumnsList" aria-hidden="true">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<h4 class="modal-title" id="Export">Export</h4>
			</div>
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="formExportColumnsList" method="POST" action="">
					<div class="form-group">
						<label class="col-md-4 control-label">Select Columns <span class="short_explanation"></span></label>
						<div class="col-md-8">
							<select multiple="multiple" class="form-control selectAlll" data-search="true" id="selectExportColumnsList" >
								<option value="selectAll">--Select all--</option>
								<option value="clearAll">--Clear all--</option>
								<?php
								foreach ($columnArr as $key => $row)
								{
									if($row == 'Month')
									$row = 'Payslip_Month';
									if($row == 'Account_Number')
									$row = 'Bank_Account_Number';
									$column = str_replace('_', ' ', $row);
									echo '<option value="'.$row.'">'.$column.'</option>';
								}
								?>
							</select>
						</div>
					</div>
				</form>
			</div>
			<div class="modal-footer text-center">
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="submitExportColumnsList" style="bottom: 5px;">
						<i class="mdi-content-send"></i> Submit
				</button>
			</div>
		</div>
	</div>
</div>

<!--/************ before exporting into Print select required columns ****************/-->
<div class="modal fade" id="modalPrintColumnsList" aria-hidden="true">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<h4 class="modal-title" id="ExportPrintTitle">Export</h4>
			</div>
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="formPrintColumnsList" method="POST" action="">
					<div class="form-group">
						<label class="col-md-4 control-label"> Select Columns<span class="short_explanation"></span></label>
						<div class="col-md-8">
							<select multiple="multiple" class="form-control selectAlll" data-search="true" id="selectPrintColumnsList" >
								<option value="selectAll">--Select all--</option>
								<option value="clearAll">--Clear all--</option>
								<?php
								foreach ($columnArr as $key => $row)
								{
									if($row == 'Month')
									$row = 'Payslip_Month';
									if($row == 'Account_Number')
									$row = 'Bank_Account_Number';
									$column = str_replace('_', ' ', $row);
									echo '<option value="'.$row.'">'.$column.'</option>';
								}
								?>
							</select>
						</div>
					</div>
					<div class="form-group">
						<label class="col-md-4 control-label">Print View</label>
						<div class="col-md-8">
							<div class="radio radio-secondary">
								<label>
									<input type="radio" name="printView" value="landscape" checked="" class="md-radio printView"><span class="circle"></span><span class="check"></span>
									Landscape
								</label>
								<label>
									<input type="radio" name="printView" value="portrait" class="md-radio printView"><span class="circle"></span><span class="check"></span>
									Portrait
								</label>
							</div>
							
						</div>
					</div>
				</form>
			</div>
			<div class="modal-footer text-center">
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="submitPrintColumnsList" style="bottom: 5px;">
						<i class="mdi-content-send"></i> Submit
				</button>
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="modalPdfWarning" aria-hidden="true">
	<div class="modal-dialog payout-warning-modal-width">
		<div class="modal-content payout-warning-modal-width">	
			<div class="payout-text-center-cls">
				<i class="fa fa-clock-o clock-size" aria-hidden="true"></i>
			</div>
			<div class="modal-body warning-text">Hyperlink for invoices and other documents are time sensitive and hence it expires in 7 days. Any reference to the invoices after 7 days requires a document refresh.</div>
			
			<div class="modal-footer payout-warning-footer-div">
			  <button type="button" class="btn btn btn-white btn-embossed otp-popup-button payout-cancel-btn-color payout-warning-btn" data-dismiss="modal" id="cancelPdfWarning" >Cancel</button>
			  <button type="button" class="btn btn btn-white btn-embossed otp-popup-button payout-success-btn-color payout-warning-btn text-secondary" data-dismiss="modal" id="generatePdf">Generate</button>
			</div>
		</div>
	</div>
</div>
<div class="modal fade" id="modalExcelWarning" aria-hidden="true">
	<div class="modal-dialog payout-warning-modal-width">
		<div class="modal-content payout-warning-modal-width">	
			<div class="payout-text-center-cls">
				<i class="fa fa-clock-o clock-size" aria-hidden="true"></i>
			</div>
			<div class="modal-body warning-text">Hyperlink for invoices and other documents are time sensitive and hence it expires in 7 days. Any reference to the invoices after 7 days requires a document refresh.</div>
			
			<div class="modal-footer payout-warning-footer-div">
			  <button type="button" class="btn btn btn-white btn-embossed otp-popup-button payout-cancel-btn-color payout-warning-btn" data-dismiss="modal" id="cancelExcelWarning" >Cancel</button>
			  <button type="button" class="btn btn btn-white btn-embossed otp-popup-button payout-success-btn-color payout-warning-btn text-secondary" data-dismiss="modal" id="generateExcel">Generate</button>
			</div>
		</div>
	</div>
</div>
<!--/******************** Choose items to Export********************/-->
<div class="modal fade" id="modalExportPdf" aria-hidden="true">
	<div class="modal-dialog modal-sm">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<h4 class="modal-title" id="Export">Export</h4>
			</div>
			
			<div class="modal-body">
				<form role="form" id="formExportReport">
					<div class="row">
						<div class="form-group">
							<label class="col-sm-6 col-xs-6 control-label">All</label>
							<div class="form-group togglebutton" style="padding-left: 15px; padding-top: 10px;">
								<label>
								  <input type="checkbox" style="margin-top: 15px;" class="col-sm-6 col-xs-6 md-checkbox" name="formExportAll" id="formExportAll" >
								</label>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-6 col-xs-6 control-label">Pie</label>
							<div class="form-group togglebutton" style="padding-left: 15px; padding-top: 10px;">
								<label>
								  <input type="checkbox" style="margin-top: 15px;" class="col-sm-6 col-xs-6 md-checkbox" name="formExportPie" id="formExportPie" >
								</label>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-6 col-xs-6 control-label">Bar</label>
							<div class="form-group togglebutton" style="padding-left: 15px; padding-top: 10px;">
								<label>
								  <input type="checkbox" style="margin-top: 15px;" class="col-sm-6 col-xs-6 md-checkbox" name="formExportBar" id="formExportBar" >
								</label>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-6 col-xs-6 control-label">Grid</label>
							<div class="form-group togglebutton" style="padding-left: 15px; padding-top: 10px;">
								<label>
								  <input type="checkbox" style="margin-top: 15px;" class="col-sm-6 col-xs-6 md-checkbox" name="formExportGrid" id="formExportGrid" >
								</label>
							</div>
						</div>
					</div>
				</form>	
			</div>
			
			<div class="modal-footer text-center">
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="exportReport" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Export
				</button>
			</div>
			
		</div>
	</div>
</div>

<!--/************ before exporting into Excel select required columns ****************/-->
<div class="modal fade" id="modalExcelColumnsList" aria-hidden="true">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" id="closemodalExcelColumnsList" data-dismiss="modal" style="margin-right: 10px;">
                    <i class="mdi-hardware-keyboard-backspace"></i>
                </button>
				<h4 class="modal-title" id="ExportExcelTitle">Export Excel</h4>
			</div>
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="formExcelColumnsList" method="POST" action="">
					<div class="form-group selectExcelColumnsListPanel">
						<label class="col-md-4 control-label"> Select Columns<span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<select multiple="multiple" class="form-control vRequired selectAlll" data-search="true" id="selectExcelColumnsList" >
								
							</select>
						</div>
					</div>
					<div class="form-group groupBy">
						<label class="col-md-4 control-label" id="groupByLabel">Group By</label>
							<div class="col-md-8">
								<select class="form-control" data-search="true" id="filterGroupBy">
									<option value="Business_Unit">Business Unit</option>
									<option value="Location" >Location</option>
									<option value="Department" >Department</option>
									<option value="Designation">Designation</option>
									<option value="Employee_Type">Employee Type</option>
								</select>
							</div>
					</div>
				</form>
			</div>
			<div class="modal-footer text-center">
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="submitExcelColumnsList" style="bottom: 5px;">
						<i class="mdi-content-send"></i> Submit
				</button>
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="modalReportStructureColumnsList" aria-hidden="true">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" id="closemodalReportStructureColumnsList" data-dismiss="modal" style="margin-right: 10px;">
                    <i class="mdi-hardware-keyboard-backspace"></i>
                </button>
				<h4 class="modal-title" id="ExportReportStructureTitle">Save Report Structure</h4>
			</div>
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="formReportStructureColumnsList" method="POST" action="">
					<div class="form-group selectReportStructureColumnsListPanel">
						<label class="col-md-4 control-label"> Select Columns<span class="short_explanation">*</span></label>
						<div class="col-md-8">
							<select multiple="multiple" class="form-control vRequired selectAlll" data-search="true" id="selectReportStructureColumnsList" >
								
							</select>
						</div>
					</div>
					<!-- <div class="form-group groupBy">
						<label class="col-md-4 control-label">Group By<span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control" data-search="true" id="filterReportStructureGroupBy">
									<option value="Business_Unit">Business Unit</option>
									<option value="Location" >Location</option>
									<option value="Department" >Department</option>
									<option value="Designation">Designation</option>
									<option value="Employee_Type">Employee Type</option>
								</select>
							</div>
					</div> -->
				</form>
			</div>
			<div class="modal-footer text-center">
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="submitReportStructureColumnsList" style="bottom: 5px;">
						<i class="mdi-content-send"></i> Save
				</button>
			</div>
		</div>
	</div>
</div>



<div class="modal fade" id="modalExportPrint" aria-hidden="true">
	<div class="modal-dialog modal-sm">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<h4 class="modal-title" id="PrintReport">Export</h4>
			</div>
			
			<div class="modal-body">
				<form role="form" id="formPrintReport">
					<div class="row">
					<div class="form-group">
						<label class="col-sm-6 col-xs-6 control-label">All</label>
							<div class="form-group togglebutton" style="padding-left: 15px; padding-top: 10px;">
								<label>
								  <input type="checkbox" style="margin-top: 15px;" class="col-sm-6 col-xs-6 md-checkbox" name="formPrintAll" id="formPrintAll" >
								</label>
							</div>
					</div>
					<div class="form-group">
						<label class="col-sm-6 col-xs-6 control-label">Pie</label>
						<div class="form-group togglebutton" style="padding-left: 15px; padding-top: 10px;">
								<label>
								  <input type="checkbox" style="margin-top: 15px;" class="col-sm-6 col-xs-6 md-checkbox" name="formPrintPieChart" id="formPrintPieChart" >
								</label>
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-6 col-xs-6 control-label">Bar</label>
						<div class="form-group togglebutton" style="padding-left: 15px; padding-top: 10px;">
								<label>
								  <input type="checkbox" style="margin-top: 15px;" class="col-sm-6 col-xs-6 md-checkbox" name="formPrintBarChart" id="formPrintBarChart" >
								</label>
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-6 col-xs-6 control-label">Grid</label>
						<div class="form-group togglebutton" style="padding-left: 15px; padding-top: 10px;">
								<label>
								  <input type="checkbox" style="margin-top: 15px;" class="col-sm-6 col-xs-6 md-checkbox" name="formPrintGrid" id="formPrintGrid" >
								</label>
						</div>
					</div>
					</div>
				</form>	
			</div>
			
			<div class="modal-footer text-center">
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" aria-hidden="true" id="exportAsPrint" style="bottom: 5px;">
				<!--<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" data-dismiss="modal" id="exportAsPrint" style="bottom: 5px;">-->
					<i class="mdi-content-send"></i> Print
				</button>
			</div>
			
		</div>
	</div>
</div>
<?php } ?>
