<?php
	$customFormNameA = $this->customFormNameA;
	$customFormNameB = $this->customFormNameB;
	$customFormNameC = $this->customFormNameC;
	$customFormNameD = $this->customFormNameD;
	$customFormNameE = $this->customFormNameE;
	$leaveClosureYear= $this->leaveClosureYear;
	
	$finalFormName = ((!empty($customFormNameA) && !empty($customFormNameA['New_Form_Name'])) ? $customFormNameA['New_Form_Name'] : $this->formNameA);
	
	$this->headTitle($finalFormName);	  
    	
	$leaveTypeUser = $this->leaveTypeUser;
	$leaveUser     = $this->leaveUser;
	$monthType     = $this->monthTypes;
	$leaveEncashmentAccessRights = $this->leaveEncashment;
	
	$leaveFreezeUser     = $this->leaveFreezeUser;
	$leaveEnforcementUser     = $this->leaveEnforcementUser;
	$approvalManagementUser = $this->approvalManagementUser;
	
	$leaveTypes    = $this->leaveTypes;
	$empLocation     = $this->empLocation;
	$workSchedule         = $this->workSchedule;
	
	$formNameA     = $this->formNameA;
	$formNameB     = $this->formNameB;
	$formNameC     = $this->formNameC;

	$employees     = $this->employeeDetails;
	$empESICReason = $this->empESICReason;
	$employeeName  = $leaveUser['Employee_Name'];
	
	$serviceProvider = $this->serviceProvider;

	$orgDetails		 = $this->orgDetails;
	$deptHierarchy 	 = $this->deptHierarchy;
	$leaveSettings	 = $this->leaveSettings;

	$currentYear = date('Y');
	$previousYear = $currentYear-1;
	$futureYear = $currentYear+1;

	$currentYrMonth = '01'.'/'.intval(date('m')).'/'.$currentYear;	

	$leaveClosureYearList = array(
		$previousYear => $previousYear,
	 	$currentYear => $currentYear,
    	$futureYear => $futureYear
	);

	if ($leaveUser['Op_Choice'] == 1)
	{
		$leaveClosureDetails = $this->getLeaveClosureDetails;

		$carryOverLeaves = $this->carryOverLeaves;
		//Leave Encashment
		$autoEncashedLeave        	 = $this->autoEncashedLeave;
		$countNonAutoEncashedLeave   = count($this->nonAutoEncashedLeave);
	}	
	
	$department = array();
	
	foreach ($employees as $key => $row) {
		if (!in_array($row['Department_Name'], $department)) {
			array_push($department, $row['Department_Name']);
		}
	}
	
	$dateformat = $this->dateformat;
	
	if(!empty($dateformat))
	{
		$dformat = $dateformat['bs'];
		//$dformat = 'yyyy/dd/mm';
	}
	else
	{
		$dformat = 'dd/mm/yyyy';
	}
	
	$gradesPair = $this->gradesPair;
	$managerNames =$this->managerNames;
	$listMonth    = $this->listMonth;
	$designation     = $this->designation;

	$leaveAccrualDetails   = $this->leaveAccrualDetails;
	$entitlementLeaveExist = $this->entitlementLeaveExist;
	
	?>
	<?php
	$isDomain = Zend_Registry::get('Domain');
	if($isDomain == 'hrapp.co.in'){
		//Show the leave form in staging alone
		$leaveAccessView=1;
	}else{
		//manually hiding now am adding this condition
		$leaveAccessView=0;
	}
	if($leaveAccessView==0)
	{
	?>
	<div class="col-md-12 portlets add-panel-padding" id="leavePanel">
		<div class="card my-profile-card-radius">
			<div class="row">
				<div class="col-xs-12 my-profile-img-column">
					<img width="50" height="50" alt="my-team" src="<?php echo $this->newImg;?>"/>
					<div class="my-profile-heading">
						Explore your enhanced Leaves!
					</div>
				</div>
				<div class="col-xs-12 my-profile-sub-text">
					We're excited to share our platform's new enhancements for a better experience! 
					Now you can easily review your leaves under 'Employee Self Service' -> '
					<a id="myProfileLink" class="text-secondary" href=<?php echo $this->baseUrl('v3/employee-self-service/leave-request'); ?>>
						Leaves
					</a>'.
				</div>
			</div>
		</div>
	</div>
	<?php
	}
	?>
	
<?php
   //manually hiding now am adding this condition
	if ($leaveUser['View'] && ((!empty($customFormNameA) && array_key_exists("Enable",$customFormNameA) && $customFormNameA['Enable'] == 1) || empty($customFormNameA))) {
	

/** to print the employeejoinfrom & employeejointo month automatically in quarter wise leave form **/
		$getEmployeeJoinFromTo = $this->getEmployeeJoinFromTo; 

?>
<?php
 if ($leaveSettings['Enable_Workflow'] == "Yes" && (int)$approvalManagementUser['View'] === 1 && $leaveAccessView==1) { ?>
<div class="col-md-12 portlets paddingCls tab-spacing-cls">
	<div class="col-md-12 paddingCls bg-f9f9f9 tab-wrapper">
		<div class="pointer-cls border-bottom-secondary tab-border-cls bg-f9f9f9 tab-body" id="leaveTab">
			<div class="tab-active-text tab-text-font text-secondary custom-tab-content"  id="leaveFormTab">Leave</div>
		</div>
		<div class="pointer-cls border-bottom-secondary bg-f9f9f9 tab-body" id="approvalTab">
			<div class="tab-text-font custom-tab-content" id="approvalFormTab">
				<a id="formTabLink" class="tab-a-tag-color" href=<?php echo $this->baseUrl('v3/approvals/approval-management?form_id=31'); ?>>Approvals</a>
			</div>
		</div>
	</div>
</div>
<?php } ?>


<!-- Leave Grid Panel -->
<div class="col-md-12 portlets add-panel-padding">
	<div class="panel" id="gridPanelLeave">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong id="lblFormNameA"><?php echo $finalFormName;?></strong></h3>
		</div>
		<div class="panel-content">
			<!--<div class="m-b-20">
				<div class="btn-group">
					<button id="table-edit_new" class="btn btn-sm btn-dark"><i class="fa fa-plus"></i> Add New Line</button>
				</div>
			</div>-->
			<div class="m-b-10">
				
				<?php if (($leaveUser['Is_Manager'] == 1  || $leaveUser['Admin'] ) && ($leaveAccessView==1)) { ?>
				<button type="button" class="btn btn-white btn-embossed toolbar-icons" title="Check All" id="leaveCheckAll">
					<i class="hr-check-all"></i><span class="hidden-xs hidden-sm">Check All</span>
				</button>
				
				<?php } if ($leaveUser['Add'] == 1 && ($leaveAccessView==1)) { ?>
				
				<!-- Add Button in Grid Toolbar -->
				<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="addLeave" title="Add" >
					<i class="mdi-content-add"></i><span class="hidden-xs hidden-sm"> Add</span>
				</button>
				
				<?php } if ($leaveUser['Op_Choice'] == 1) { 
				if(!empty($leaveClosureDetails['Leave_Closure_Exist'])) {?>
				<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="LeaveClosure" title="Leave Closure"  >
					<i class="hr-lock-alt"></i><span class="hidden-xs hidden-sm"> Leave Closure</span>
				</button>				
				<?php } ?>
				
				<?php
				
				if(!empty($leaveClosureDetails['Leave_Encashment_Exist']))
				{ ?>				
				<button type="button" class="btn btn-white btn-embossed toolbar-icons ladda-button" data-style="expand-left" id="LeaveEncashment" title="Leave Encashment" >
					<i class="mdi-action-autorenew"></i><span class="hidden-xs hidden-sm"> Leave Encashment</span>
				</button>
				<?php } }
				
				if($leaveAccessView==1)
				{?>
				<!-- View Button in Grid Toolbar -->
				<button type="button" class="btn btn-secondary-default btn-embossed btn-off disabled toolbar-icons" id="viewLeave" title="View Leave" >
					<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
				</button>
				
				<!-- History Button in Grid Toolbar -->
				<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="historyLeave" title="View History" >
					<i class="mdi-action-history"></i><span class="hidden-xs hidden-sm"> History</span>
				</button>
				
				<!-- Comments Button in Grid Toolbar -->
				<button type="button" class="btn btn-secondary-default btn-embossed btn-off disabled toolbar-icons" id="commentsLeave" title="View Comments" >
					<i class="mdi-communication-comment"></i><span class="hidden-xs hidden-sm"> Comments</span>
				</button>
				<?php } ?>
				<?php if (($leaveUser['Update'] == 1 || $leaveUser['LEdit'] > 0) && ($leaveAccessView==1)) { ?>
				
				<!-- Edit Button in Grid Toolbar -->
				<button type="button" class="btn btn-secondary-default btn-embossed btn-off disabled toolbar-icons" id="editLeave" title="Edit" >
					<i class="mdi-editor-mode-edit"></i><span class="hidden-xs hidden-sm"> Edit</span>
				</button>
				
				<?php } if ($leaveSettings['Enable_Workflow'] == 'No' && ($leaveUser['Is_Manager'] == 1 || $leaveUser['Admin']) && ($leaveAccessView==1)) { ?>
				
				<!-- Multi Status Approvel Button in Grid Toolbar -->
				<button type="button" class="btn btn-secondary-default btn-embossed btn-off disabled toolbar-icons" id="multiStatusApprovalLeave" title="Multiple Status Approval" >
					<i class="hr-status-approval"></i><span class="hidden-xs hidden-sm"> Status Approval</span>
				</button>
				
				<!-- Status Update Button in Grid Toolbar -->
				<button type="button" class="btn btn-secondary-default btn-embossed btn-off disabled toolbar-icons" id="statusApprovalLeave" title="Status Approval" >
					<i class="mdi-action-info-outline"></i><span class="hidden-xs hidden-sm"> Status Update</span>
				</button>
				
				<?php } if (($leaveUser['Op_Choice'] == 1 || $leaveUser['Add'] == 1) && ($leaveAccessView==1)) { ?>
				
				<!-- Delete Button in Grid Toolbar -->
				<button type="button" class="btn btn-secondary-default btn-embossed btn-off disabled toolbar-icons" id="cancelLeave" title="Cancel Leave">
					<i class="mdi-content-block"></i><span class="hidden-xs hidden-sm"> Cancel Leave</span>
				</button>
				
				<?php } if (($leaveUser['Delete'] == 1) && ($leaveAccessView==1)) { ?>
				
				<!-- Delete Button in Grid Toolbar -->
				<button type="button" class="btn btn-secondary-default btn-embossed btn-off disabled toolbar-icons" id="deleteLeave" data-toggle="modal" title="Delete">
					<i class="mdi-action-delete"></i><span class="hidden-xs hidden-sm"> Delete</span>
				</button>
				
				<?php } ?>
				
				<?php if (($leaveUser['Is_Manager'] == 1 || $leaveUser['Admin'] == 'admin') && ($leaveAccessView==1)) { ?>
			
				<button type="button" class="btn btn-white btn-embossed visible-lg-* toolbar-icons" id="exportLeave" title="Export">
					<i class="mdi-communication-import-export"></i><span class="hidden-xs hidden-sm"> Export Leave Register</span>
				</button>
				
				<?php }?>

				<?php if ($leaveUser['Admin'] && (count($entitlementLeaveExist) > 0)) { ?>
					<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="monthlyLeaveAccrual" title="Monthly Leave Accrual">
						<i class="hr-lock-alt"></i><span class="hidden-xs hidden-sm">Monthly Leave Accrual</span>
					</button>				
				<?php }
				
				if($leaveAccessView==1)
				{?>
				<button type="button" class="btn btn-white btn-embossed visible-lg-* toolbar-icons" id="exportLeaveHistory" title="Export">
					<i class="mdi-communication-import-export"></i><span class="hidden-xs hidden-sm"> Export Leave Balance</span>
				</button>
				<!-- Filter Button in Grid Toolbar -->
				<a class="filter-toggle btn btn-white btn-embossed toolbar-icons" id="filterLeave">
					<i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm"> Filter</span>
				</a>
				
				<button type="button" class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="ViewLeaveSummary" title="Expand All" >
						<i class="hr-expand"></i><span class="hidden-xs hidden-sm">Expand All</span>
				</button>
				<?php } ?>
			</div>
			
			<input type="hidden" name="Session_Id" id="Session_Id" value="<?php echo $leaveUser['Session_Id']; ?>" />
			<input type="hidden" name="Is_Manager" id="Is_Manager" value="<?php echo $leaveUser['Is_Manager']; ?>" />
			<input type="hidden" name="Enable_Workflow" id="Enable_Workflow" value="<?php echo $leaveSettings['Enable_Workflow']; ?>" />
			<input type="hidden" name="Enforce_Comment_For_Leave" id="Enforce_Comment_For_Leave" value="<?php echo $leaveSettings['Enforce_Comment_For_Leave']; ?>" />
			<input type="hidden" name="Enable_CAMU_Scheduler" id="Enable_CAMU_Scheduler" value="<?php echo $leaveSettings['Enable_CAMU_Scheduler']; ?>" />
			
			<!-- Leave Grid -->
		<?php
		if($leaveAccessView==1)
		{
		?>
			<table class="table table-hover table-dynamic table-striped" id="tableLeave">
				<thead>
					<tr>
						<th></th>
						<th id="leaveEmployeeId">Employee Id</th>
						<th id="leaveEmployeeName">Employee Name</th>
						<th id="leaveLeaveName">Leave Name</th>
						<th id="leaveLeaveStartDate">Start Date</th>
						<th id="leaveLeaveEndDate">End Date</th>
						<th id="leaveLeaveTotalDays">Total Days</th>
						<th id="leaveLeaveStatus">Status</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
		<?php } ?>
			
		</div>
	</div>
</div>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="contextMenuLeave" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextLeave"><i class="mdi-action-visibility"></i> View</a></li>
        <?php if ($leaveUser['Update'] == 1) { ?>
		<li><a tabindex="-1" id="editContextLeave"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<?php } if ($leaveUser['Delete'] == 1) { ?>
		<li><a tabindex="-1" id="deleteContextLeave"><i class="mdi-action-delete"></i> Delete</a></li>
        <?php } if ($leaveSettings['Enable_Workflow'] == 'No' && ($leaveUser['Is_Manager'] == 1 || $leaveUser['Admin'])) { ?>
		<li><a tabindex="-1" id="statusUpdateContextLeave"><i class="mdi-action-info-outline"></i> Status Update</a></li>	
        <?php } ?>
        <li><a tabindex="-1" id="historyContextLeave"><i class="mdi-action-history"></i> History</a></li>
        <li><a tabindex="-1" id="commentContextLeave"><i class="mdi-communication-comment"></i> Comments</a></li>
		<?php if ($leaveUser['Op_Choice'] == 1 || $leaveUser['Add'] == 1) { ?>
		<li><a tabindex="-1" id="cancelContextLeave"><i class="mdi-content-block"></i> Cancel Leave</a></li>
		<?php } ?>
	</ul>
</div>

<?php
	if($leaveAccessView==1)
	{
	?>
	
<!--Filter Form-->
<div class="builder" id="filterPanelLeave">
	<!--<a class="filter-toggle"><i class="glyphicon glyphicon-filter"></i></a>-->
	<div id="closeFilterLeave"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel" style="width: 100%;" id="filterResetLeave">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="filterApplyLeave">Apply</button>
			
			
			<?php if ($leaveUser['Is_Manager'] == 0 && empty($leaveUser['Admin'])) { ?>
			<div class="form-group">
				<label>Employee Name</label>
				<input type="text" class="form-control" id="filterEmployeeName" readonly="readonly" value="<?php echo $leaveUser['Employee_Name']; ?>" >
			</div>
			
			<?php } else { ?>
			
			<div class="form-group">
				<label>Employee Name</label>
				<input type="text" class="form-control" id="filterEmployeeName" placeholder="Employee Name">
			</div>
			
			<?php } ?>
			
			<div class="form-group">
				<label>Leave Type</label>
				<select class="form-control" data-search="true" id="filterLeaves" >
					<option value="">All</option>
					<?php
					foreach ( $leaveTypes as $key => $row )
					{
						echo '<option value="'. $key .'">'. $row .'</option>';
					}
					?>
				</select>
			</div>
			
			<div class="form-group">
				<label>Leave Date</label>
				<div class="input-daterange b-datepicker input-group" data-date-format="<?php echo $dformat; ?>" id="datepicker">
					<input type="text" class="input-md form-control" name="start" id="filterLeaveDateBegin" data-orientation="top" placeholder="Begin" />
					<span class="input-group-addon">to</span>
					<input type="text" class="input-md form-control" name="end" id="filterLeaveDateEnd" data-orientation="top" placeholder="End" />
				</div>
			</div>

			<div class="form-group">
				<label>Location</label>
				<select class="form-control" data-search="true" id="filterLocation" >
					<option value="">All</option>
					<?php
					foreach ($empLocation as $key => $row)
					{
						echo '<option value="'.$key.'">'.$row.'</option>';
					}
					?>
				</select>
			</div>

			<div class="form-group">
				<label>Department</label>
				<select class="form-control" data-search="true" id="filterDepartment" >
					<option value="">All</option>
					<?php
					foreach ($deptHierarchy as $key => $row)
					{
						echo '<option value="'. $row['Department_Id'] .'">'. $row['Department_Name'] .'</option>';
						
						foreach($row['Child'] as $val =>$name)
						{
							
							if($name['Parent_Type_Id'] == $row['Department_Id'])
							{
								echo '<option value="'. $name['Department_Id'] .'">&nbsp;&nbsp;'. $name['Department_Name'] .'</option>';
								
								foreach($row['Child'] as $v =>$k)
								{
									if($k['Parent_Type_Id'] == $name['Department_Id'])
										echo '<option value="'. $k['Department_Id'] .'">&nbsp;&nbsp;&nbsp;&nbsp;'. $k['Department_Name'] .'</option>';
								}
								
							}
						}
					}
					?>
				</select>
			</div>

			<div class="form-group">
				<label>Designation</label>
				<select class="form-control" data-search="true" id="filterDesignation" >
					<option value="">All</option>
					<?php
					foreach ($designation as $key => $row)
					{
						echo '<option value="'.$key.'">'.$row.'</option>';
					}
					?>
				</select>
			</div>

			<div class="form-group">
				<label>Work Schedule</label>
				<select class="form-control" data-search="true" id="filterWorkSchedule">
					<option value="">All</option>
					<?php
					foreach ($workSchedule as $key => $row)
					{
						echo '<option value="'.$key.'">'.$row.'</option>';
					}
					?>
				</select>
			</div>
			
			<div class="form-group">
				<label>Manager Name</label>
				<select class="form-control" data-search="true" id="filterManagerName">
					<option value="">All</option>
					<?php
					foreach ($managerNames as $row)
					{
						echo '<option value="'.$row['Employee_Id'].'">'.$row['Employee_Name'].'</option>';
					}
					?>
				</select>
			</div>			
			
			<div class="form-group">
				<label>Late Arrival</label>
				<select class="form-control" data-search="false" id="filterLateAttendance" >
					<option value="">All</option>
					<option value="1">Yes</option>
					<option value="2">No</option>
				</select>
			</div>

			<div class="form-group">
				<label>Early Checkout</label>
				<select class="form-control" data-search="false" id="filterEarlyCheckoutLeave" >
					<option value="">All</option>
					<option value="1">Yes</option>
					<option value="2">No</option>
				</select>
			</div>

			<?php if ($orgDetails['Field_Force'] == 1) { ?>
			<div class="form-group">
				<label>Service Provider</label>
				<select class="form-control" data-search="true" id="filterServiceProvider">
					<option value="">All</option>
					<?php
					foreach ($serviceProvider as $key => $row)
					{
						echo '<option value="'. $key .'">'. $row .'</option>';
					}
					?>
				</select>												
			</div>
										
			<?php } ?>

			<div class="form-group">
				<label>Status</label>
				<select class="form-control" data-search="true" id="filterStatus" >
					<option value="">All</option>
					<option value="Applied">Applied</option>
					<option value="Approved">Approved</option>
					<option value="Rejected">Rejected</option>
					<option value="Returned">Returned</option>
					<option value="Cancelled">Cancelled</option>
					<option value="Cancel Applied">Cancel Applied</option>
				</select>
			</div>
		</div>
	</div>
</div>

<?php
}
if ($leaveUser['Op_Choice'] == 1)
{
	if (count($carryOverLeaves) > 0)
	{
?>

<!--Leave Closure modal-->
<div class="modal fade" id="modalLeaveClosure" aria-hidden="true">
	<div class="modal-dialog modal-full" id="fullModal">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" aria-hidden="true" style="margin-right: 10px;">
					<i class="mdi-hardware-keyboard-backspace" id="backLeaveClosure"></i>
				</button>
				<!--<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52"></i></button>-->
				<h4 class="modal-title"><strong>Leave Closure</strong></h4>
			</div>
			
			<div class="modal-body">
				<form role="form" id="formLeaveCarryOver" >					
					<input type="hidden" name="carryOverLeave" id="CO_Leave" value="<?php echo count($carryOverLeaves); ?>" />
					
					<!--Leave Type Grids-->
					<div class="row panel-group panel-accordion" id="viewAccordion">
                        
						<?php foreach ($carryOverLeaves as $key => $row) { ?>
						<div class="panel panel-default">
							<div class="panel-heading" style="display: flex;">
								<h4>
									<a data-toggle="collapse" data-parent="#viewAccordion" href="#viewPanel<?php echo $row['LeaveType_Id']; ?>">
										Leave Type - <?php echo $row['Leave_Name']; ?>
									</a>
								</h4>
								<?php if ($leaveUser['Is_Manager'] == 1 || $leaveUser['Admin'] == 'admin') { ?>
									<button type="button" class="btn btn-white btn-embossed visible-lg-* toolbar-icons exportLeaveClosure hidden" style="display: flex;margin-top:10px"
										id="exportLeaveClosure-<?php echo $row['LeaveType_Id']; ?>" name="<?php echo $row['Leave_Name']; ?>" title="Export">
										<i class="mdi-communication-import-export"></i><span class="hidden-xs hidden-sm"> Export</span>
									</button>
								<?php }?>
							</div>
							<div id="viewPanel<?php echo $row['LeaveType_Id']; ?>" class="panel-collapse collapse in">
								<div class="panel-body">
									<div class="row">
										<table class="table dataTable table-striped table-dynamic table-hover tableCarryOver" id="table-<?php echo $row['LeaveType_Id']; ?>">
											<thead>
												<tr>
													<th></th>
													<th id="leaveTypeLeaveEmployeeId">Employee Id</th>
													<th id="leaveTypeLeaveEmployeeName">Employee Name</th>
													<th id="leaveTypeLeaveTotalDays">Current Year Leave Balance</th>
													<th id="leaveTypeLeaveCarryForwardLimit">Carry Forward Limit</th>
													<th id="leaveTypeLeaveLeavesForward">Leaves Forward</th>
													<th id="leaveTypePreviousYearBalance">Previous Year CO Balance</th>
													<th id="leaveTypeTotalCarryOverBalance">Total Carry Over Balance</th>
												</tr>
											</thead>
											<tbody>
											
											</tbody>
										</table>
									</div>
								</div>
							</div>
                        </div>
                        <?php } ?>
                    </div>
				</form>
			</div>
			<div class="modal-footer text-center">
				
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="carryForwardFormSubmitLeave" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Carry Forward
				</button>
				
			</div>
		</div>
	</div>
</div>

<?php } if (count($this->autoEncashedLeave) > 0) { ?>

<!-- Leave Encashment modal -->
<div class="modal fade" id="modalLeaveEncashment" aria-hidden="true">
	<div class="modal-dialog modal-full" id="fullModal">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" aria-hidden="true" style="margin-right: 10px;">
					<i class="mdi-hardware-keyboard-backspace" id="backLeaveLeaveEncashment"></i>
				</button>
				<!--<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52"></i></button>-->
				<h4 class="modal-title"><strong>Leave Encashment</strong></h4>
			</div>
			
			<div class="modal-body">
				<form role="form" id="formLeaveEncashment" >
					<input type="hidden" name="EncashLeave" id="Encash_Leave" value="<?php echo count($this->autoEncashedLeave); ?>" />
					
					<!--Leave Type Grids-->
					<div class="row panel-group panel-accordion" id="viewLeaveEncashmentAccordion">
                        
						<?php foreach ($autoEncashedLeave as $key => $row) { ?>
						<div class="panel panel-default">
							<div class="panel-heading" style="display: flex;">
								<h4>
									<a data-toggle="collapse" data-parent="#viewLeaveEncashmentAccordion" href="#viewLEPanel<?php echo $row['LeaveType_Id']; ?>">
										Leave Type - <?php echo $row['Leave_Name']; ?>
									</a>
								</h4>
								<?php if ($leaveUser['Is_Manager'] == 1 || $leaveUser['Admin'] == 'admin') { ?>
									<button type="button" class="btn btn-white btn-embossed visible-lg-* toolbar-icons exportLeaveEncashment hidden" style="display: flex;margin-top:10px"
										id="exportLeaveEncashment-<?php echo $row['LeaveType_Id']; ?>"  name="<?php echo $row['Leave_Name']; ?>" title="Export">
										<i class="mdi-communication-import-export"></i><span class="hidden-xs hidden-sm"> Export</span>
									</button>
								<?php }?>
							</div>
							<div id="viewLEPanel<?php echo $row['LeaveType_Id']; ?>" class="panel-collapse collapse in">
								<div class="panel-body">
									<div class="row">
										<table class="table dataTable table-striped table-dynamic table-hover tableLeaveEncashment" id="tableLeaveEncashment-<?php echo $row['LeaveType_Id']; ?>">
											<thead>
												<tr>
													<th></th>
													<th id="leaveViewEncashmentEmployeeId">Employee Id</th>
													<th id="leaveViewEncashmentEmployeeName">Employee Name</th>
													<th id="leaveViewEncashmentLeaveBalance">Current Year Leave Balance</th>
													<th id="leaveViewEncashmentLimit">Encashment Limit</th>
													<th id="leaveViewEncashmentDays">Encashment Days</th>
												</tr>
											</thead>
											<tbody>
											
											</tbody>
										</table>
									</div>
								</div>
							</div>
                        </div>
                        <?php } ?>
                    </div>
				</form>
			</div>
			<div class="modal-footer text-center">
				
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="leaveEncashSubmit" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Leave Encashment
				</button>
				
			</div>
		</div>
	</div>
</div>

<?php } if ($leaveUser['Encashment'] == 1) { ?>

<!-- Leave Encashment modal -->
<div class="modal fade" id="modalFormApplyLeaveEncashment" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" aria-hidden="true" style="margin-right: 10px;">
					<i class="mdi-hardware-keyboard-backspace" id="backLeaveApplyLeaveEncashment"></i>
				</button>
				<!--<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52"></i></button>-->
				<h4 class="modal-title"><strong>Apply Leave Encashment</strong></h4>
			</div>
			
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="formApplyLeaveEncashment" method="POST" action="">
				
					<div class="row">
						<div class="form-group">
							<label class="col-md-4 control-label">Employee Name <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<?php if ($leaveUser['LE_IsManager'] == 1 || $leaveUser['LE_IsAdmin'] == 'admin') { ?>
								<select class="form-control vRequired" data-search="true" id="LE_Employee_Name" name="LE_Employee_Name" data-placeholder="Employee Name">
									<option value="">-- Select --</option>
									<?php
									foreach ($department as $key => $row)
									{
										echo '<optgroup label="'. $row .'">';
										
										foreach ($employees as $empKey => $empRow)
										{
											if ($row == $empRow['Department_Name'])
											{
												echo '<option value="'. $empRow['value'] .'">'. $empRow['text'] .'</option>';
											}
										}
										
										echo '</optgroup>';
									}
									?>
								</select>
								<?php } else { ?>
								<select class="form-control vRequired" data-search="true" id="LE_Employee_Name" name="LE_Employee_Name" data-placeholder="Employee Name">
									<?php echo '<option value="'. $leaveUser['Session_Id'] .'">'. $employeeName .'</option>'; ?>
								</select>
								<?php } ?>
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Leave Name <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control vRequired" data-search="true" id="LE_Leave_Name" name="LE_Leave_Name" data-placeholder="Leave Name">
									<option value="">-- Select --</option>
								</select>
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Available Days</label>
							<div class="col-md-8">
								<input type="number" class="form-control" name="Available_Days" id="Available_Days" placeholder="Available Days" readonly="readonly">
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Encashment Days <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<input type="number" class="form-control vRequired" name="Encashment_Days" id="Encashment_Days" min=0.5 placeholder="Encashment Days" >
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="modal-footer text-center">
				
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetApplyLeaveEncashment" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitApplyLeaveEncashment" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
				
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="modalDirtyApplyLeaveEncashment" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeApplyLeaveEncashmentConf"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noApplyLeaveEncashmentConf">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="formDirtyCloseApplyLeaveEncashment">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } } ?>

<!--View Comments modal-->
<div class="modal fade" id="modalCommentLeave" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeCommentLeave"></i></button>
				<h4 class="modal-title"><strong>Comment</strong></h4>
			</div>
			
			<div class="modal-body">
				<!-- comment Grid Table -->
				<table class="table dataTable table-striped table-dynamic table-hover tableCommentLeave">
					<thead>
						<tr>
							<th></th>
							<th id="leaveCommentEmployeeName">Employee Name</th>
							<th id="leaveCommentComment">Comment</th>
							<th id="leaveCommentStatus">Status</th>
							<th id="leaveCommentAddedOn">Added On</th>
						</tr>
					</thead>
					<tbody>
					
					</tbody>
				</table>
			</div>
			
		</div>
	</div>
</div>

<!--View Leave history modal-->
<div class="modal fade" id="modalHistoryLeave" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" aria-hidden="true" style="margin-right: 10px;">
					<i class="mdi-hardware-keyboard-backspace" id="backLeaveHistory"></i>
				</button>
				<!--<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52"></i></button>-->
				<h4 class="modal-title"><strong>View History</strong></h4>
			</div>
			
			<div class="modal-body">
				<!-- History Grid Table -->
				<table class="table dataTable table-striped table-dynamic table-hover" id="tableHistoryLeave">
					<thead>
						<tr>
							<th></th>
							<th id="leaveHistoryLeaveName">Leave Name</th>
							<th id="leaveHistoryTotalDays">Total Days</th>
							<th id="leaveHistoryCarryOverDays">Carry Over Days</th>
							<th id="leaveHistoryCarryOverBalance">Carry Over Balance</th>
							<th id="leaveHistoryLeaveTaken">Leave Taken</th>
							<th id="leaveHistoryLeaveBalance">Leave Balance</th>
						</tr>
					</thead>
					<tbody>
					
					</tbody>
				</table>
			</div>
			
		</div>
	</div>
</div>

<!--View, Add, Edit Form Modal-->
<div class="modal fade" id="modalFormLeave" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" aria-hidden="true" style="margin-right: 10px;">
					<i class="mdi-hardware-keyboard-backspace" id="backLeave"></i>
				</button>
				
				<?php if ($leaveUser['Update'] == 1) { ?>
				<button type="button" class="close form-icons" aria-hidden="true" id="editInViewLeave">
					<i class="mdi-editor-mode-edit"></i>
				</button>
				<?php } ?>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<!--View Leave Form-->
				<form role="form" id="viewFormLeave" >
					<input type="hidden" name="fieldForce" id="fieldForce" value="<?php echo $orgDetails['Field_Force']; ?>" />
					<div class="row">
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Employee Name</label></div>
							<div class="col-md-7"><p id="vEmployeeName"></p></div>
						</div>
						<?php if ($leaveSettings['Enable_Workflow'] == "No") { ?>
							<div class="form-group">
								<div class="col-md-5"><label class="control-label">Manager Name</label></div>
								<div class="col-md-7"><p id="vForwardedTo"></p></div>
							</div>
						<?php } ?>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Leave Type</label></div>
							<div class="col-md-7"><p id="vLeaveType"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Reason</label></div>
							<div class="col-md-7"><p id="vReason"></p></div>
						</div>
						<div class="form-group" >
							<div class="col-md-5"><label class="control-label">Duration</label></div>
							<div class="col-md-7"><p id="vDuration"></p></div>
						</div>
						<div class="form-group" id ="viewLeavePeriod">
							<div class="col-md-5"><label class="control-label">Leave Period</label></div>
							<div class="col-md-7"><p id="vLeavePeriod"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Leave From</label></div>
							<div class="col-md-7"><p id="vLeaveFrom"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Leave To</label></div>
							<div class="col-md-7"><p id="vLeaveTo"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Total Day(s)</label></div>
							<div class="col-md-7"><p id="vTotalDays"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Hours</label></div>
							<div class="col-md-7"><p id="vHours"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Contact Details</label></div>
							<div class="col-md-7"><p id="vContactDetails"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Alternate Person</label></div>
							<div class="col-md-7"><p id="vAlternatePerson"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Late Arrival</label></div>
							<div class="col-md-7"><p id="vLateAttendance"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Late Arrival Hours</label></div>
							<div class="col-md-7"><p id="vLateAttendanceHours" style="color:red;"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Early Checkout</label></div>
							<div class="col-md-7"><p id="vEarlyCheckout"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Early Checkout Hours</label></div>
							<div class="col-md-7"><p id="vEarlyCheckoutHours" style="color:red;"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Status</label></div>
							<div class="col-md-7"><p id="vStatus"></p></div>
						</div>
						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Approved By</label></div>
							<div class="col-md-7"><p id="approvedByLeave"></p></div>
						</div>
 						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Approved On</label></div>
							<div class="col-md-7"><p id="approvedOnLeave"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Documents </label></div>
							<div class="col-md-7"><p id="vDocuments"></p></div>
						</div>
						
					</div>
					<!--Additional Information Block-->
					<div class="row additionalInfoPanel">
						<hr class="view-hr"/>
						
						<div class="form-group" style="font-size: large;margin-left: 13px;">
							<label class="control-label text-center">Additional Information</label>
						</div>
						
						<div class="form-group additionalInformation">
							<div class="col-md-5"><label class="control-label">Added On</label></div>
							<div class="col-md-7"><p id="addedOnLeave"></p></div>
						</div>
						
						<div class="form-group additionalInformation">
							<div class="col-md-5"><label class="control-label">Added By</label></div>
							<div class="col-md-7"><p id="addedByLeave"></p></div>
						</div>
						
						<div class="form-group additionalInformation updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated On</label></div>
							<div class="col-md-7"><p id="updatedOnLeave"></p></div>
						</div>
						
						<div class="form-group additionalInformation updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated By</label></div>
							<div class="col-md-7"><p id="updatedByLeave"></p></div>
						</div>
					</div>
				</form>
				
				<?php if ($leaveUser['Add'] == 1 || $leaveUser['Update'] == 1 || $leaveUser['LEdit'] > 0) { ?>
				
				<!--Add/Edit Leave Form-->
				<form role="form" class="form-horizontal form-validation" id="editFormLeave" method="POST" action="">
					<input type="hidden" name="Leave_Id" id="Leave_Id" />
					<input type="hidden" name="Timesheet_Hours" id="Timesheet_Hours" />
					<input type="hidden" name="Activation_Hours" id="Activation_Hours" />
					<input type="hidden" name="Activation_Days" id="Activation_Days" />
					<input type="hidden" name="enableLeaveException" id="enableLeaveException" />					
					<div class="row">
						
						<div class="form-group">
							<label class="col-md-4 control-label">Employee Name <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control vRequired" data-search="true" id="Employee_Name" name="Employee_Name" data-placeholder="Employee Name">
									<?php
									if ($leaveUser['Is_Manager'] == 1 || $leaveUser['Admin'] == 'admin')
									{
										echo '<option value="">--Select--</option>';
										
										foreach ($department as $key => $row)
										{
											echo '<optgroup label="'. $row .'">';
											
											foreach ($employees as $empKey => $empRow)
											{
												if ($row == $empRow['Department_Name'])
												{
													echo '<option value="'. $empRow['value'] .'">'. $empRow['text'] .'</option>';
												}
											}
											
											echo '</optgroup>';
										}
									}
									else
									{
										echo '<option value="'. $leaveUser['Session_Id'] .'">'. $employeeName .'</option>';
									}
									?>
								</select>
							</div>
						</div>
						
						<?php if ($leaveSettings['Enable_Workflow'] == "No") { ?>
						<div class="form-group">
							<label class="col-md-4 control-label">Manager Name <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control vRequired" data-search="true" id="Forward_To" name="Forward_To" data-placeholder="Forward To">
									<option value="">-- Select --</option>
								</select>
							</div>
                        </div>
						<?php } ?>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Leave Type <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control vRequired" data-search="true" id="formLeaveType" name="Leave_Type" data-placeholder="Leave Type">
								</select>
							</div>
						</div>

						<div class="form-group lvFormLeaveBalanceRow">
							<div class="col-md-12" style="width: 98%;">
								<table class="table dataTable table-striped table-dynamic table-hover" id="leaveBalanceTable">
									<thead>
										<tr>
											<th style="text-transform: capitalize;">
												Leave Taken
												<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
												data-placement="top" data-content="Leave taken during the leave year"> </i>
											</th>
											<th style="text-transform: capitalize;">
												Leave Balance(Based On Leave Period)
												<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
												data-placement="top" data-content="Current Leave Eligibility - Leave Taken"> </i>
											</th>
											<th style="text-transform: capitalize;">
												Leave Balance(Per Annum)
												<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
												data-placement="top" data-content="Total Leave Eligibility - Leave Taken"> </i>
											</th>
											<th style="text-transform: capitalize;">
												Current Leave Eligibility
												<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
												data-placement="top" data-content="Current leave eligibility is based on leave period configuration and carry over balance"> </i>
											</th>
											<th style="text-transform: capitalize;">
												Total Leave Eligibility
												<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
												data-placement="top" data-content="Total leave eligibility based on leave year and carry over balance"> </i>
											</th>
										</tr>
									</thead>
								</table>
							</div>
						</div>

						<div class="form-group" style="display: none;" id="leaveExceptionRules">
							<div class="col-md-12">
								<label class="control-label">Leave Exception Rules : </label>
								<p><b>Only when the evidence for medical grounds is submitted and authorised by the reviewing manager, medical leave will be considered, in all other cases half paid leave will be considered for the applied leave days and an equivalent days are deducted as unpaid leave.</b></p>
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Reason Type <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control vRequired" data-search="true" id="Reason_Type" name="Reason_Type" data-placeholder="Reason Type">
									<option value="">-- Select --</option>
									<?php
									foreach ( $empESICReason as $key => $row )
									{
										echo '<option value="'. $key .'">'. $row .'</option>';
									}
									?>
								</select>
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Reason <span class="short_explanation">*</span></label>
							<div class="col-md-8">
							<input type="text" class="form-control vRequired vAlphaNumSpCDotHySlashNewLine" minlength="2" maxlength="600" id="Reason" name="Reason" placeholder="Reason">
						</div>
						</div>

						<div class="form-group" id="leavesDocumentUpload">
							<div class="fileinput fileinput-new" data-provides="fileinput">
							<p class="col-md-4" id="leaveLabelId"><strong>Documents<br></strong>
							<span class="short_explanation">*</span> Allowed File types :  (.png,.jpg,.jpeg,.pdf,.tif,.tiff,.bmp)<br>
							<span class="short_explanation">*</span> Max File Size : 3MB
							</p>
								
								<div class="col-md-8">
									<div class="file" id="buttonEmployeesDocumentUploadFile">												
										<button type="submit" class="btn btn-secondary" id="uploadEmployeesDocument" style="bottom: 5px;">
											<i class="fa fa-paperclip attachfiles"></i> Add Documents
										</button>
										<input type="file" class="custom-file " name="uploadEmployeesDocumentUploadFiles" id="uploadEmployeesDocumentUploadFiles" multiple=multiple>													
									</div>
									
									<div id="EmployeesDocumentUploadAttachmentsList" style="font-size:15px;margin-top: 10px"> </div>                                               
							</div>                                  
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Duration <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control vRequired" data-search="true" id="Duration" name="Duration" data-placeholder="Leave Type">
								</select>
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Leave Period <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control vRequired"  id="Leave_Period" name="Leave_Period" data-placeholder="Leave Period">
									<!-- <option value="">-- Select --</option>
									<option value="First Half">First Half</option>
									<option value="Second Half">Second Half</option> -->
								</select>
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Leave Date <span class="short_explanation">*</span></label>
							<div class="row col-md-8">
								<div class="col-md-5">
								<!--<input type="text" name="timepicker" class="date-picker form-control" placeholder="Select a date...">-->
									<input type="text" class="date-picker form-control vRequired Leave_From LeaveMinFrom datePickerRead" name="Leave_From" id="Leave_From" data-orientation="top" placeholder="Leave From Date" >
								</div>
								<span class="col-md-1">to</span>
								<div class="col-md-5">
									<input type="text" class="date-picker form-control vRequired Leave_To LeaveMinTo datePickerRead" name="Leave_To" id="Leave_To" data-orientation="top" placeholder="Leave To Date" />
								</div>
							</div>
						</div>

						<div class="form-group" style="display: flex; justify-content: center;">
							<div id="calendar" class="hidden" style="margin-top: -20px !important;width: 83%"></div>
						</div>

						<!-- During the leavetype change, present the employee applicable leave date range in this info bar -->
						<div class="form-group showLvApplicableDateRangeInfo">
							<div class="col-md-4"></div>
							<div class="col-md-8">
							<div class="column-cus-info-bar">
								<i class="fa fa-info-circle column-cus-info-icon" aria-hidden="true"></i>
								<span id="lvApplicableDateRangeMsg"></span>
							</div>
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Total Days</label>
							<div class="col-md-8">
								<input type="number" class="form-control" name="Total_Days" id="Total_Days" placeholder="Total No of Days" readonly="readonly">
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Hours</label>
							<div class="col-md-8">
								<input type="number" class="form-control" name="Hours" id="Hours" placeholder="Hours" readonly="readonly">
							</div>
						</div>

						<div class="form-group">
							<label class="col-md-4 control-label">Contact No <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<input type="text" class="form-control vRequired vPhoneNum" name="Contact_No" id="Contact_No" placeholder="Contact Number" readonly="readonly">
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Alternate Person
							<?php
									if ($leaveSettings['Enforce_Alternate_Person_For_Leave'] == "Yes") {
										echo '<span class="short_explanation">*</span>';
									}
							?>
							</label>
							<div class="col-md-8">
								<?php
									if ($leaveSettings['Enforce_Alternate_Person_For_Leave'] == "Yes") {
										echo '<select multiple="multiple" class="form-control selectAlll vRequired" data-search="true" id="Alternate_Person" name="Alternate_Person"></select>';
									} else {
										echo '<select multiple="multiple" class="form-control selectAlll" data-search="true" id="Alternate_Person" name="Alternate_Person"></select>';
									}
								?>
							</div>
                        </div>

						
								
						
						<div class="form-group">
							<label class="col-md-4 control-label">Status</label>
							<div class="col-md-8">
								<label class="control-label" id="Leave_Status" name="Leave_Status">-</label>
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Comment
								<?php
									if ($leaveSettings['Enforce_Comment_For_Leave'] == "Yes") {
										echo '<span class="short_explanation">*</span>';
									}
								?>
							</label>
							<div class="col-md-8">
								<?php
									if ($leaveSettings['Enforce_Comment_For_Leave'] == "Yes") {
										echo '<textarea name="Comment" id="Comment" rows="5" class="form-control vAlphaNumSpCDotHySlashNewLine vRequired" placeholder="Write your Comments..." ></textarea>';
									} else {
										echo '<textarea name="Comment" id="Comment" rows="5" class="form-control vAlphaNumSpCDotHySlashNewLine" placeholder="Write your Comments..." ></textarea>';
									}
								?>
							</div>
                        </div>
						
					</div>
					
					<button type="reset" class="cancel" id="resetLeave" style="display: none;" >Reset</button>
				</form>
				
				<?php } ?>
				
			</div>
			<div class="modal-footer text-center">
				
				<?php if ($leaveUser['Add'] == 1 || $leaveUser['Update'] == 1 || $leaveUser['LEdit'] > 0) { ?>
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="editFormResetLeave" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="editFormSubmitLeave" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
				<?php } ?>
				
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="modalLeaveSchedules" aria-hidden="true" style="z-index: 10;">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace" id="backModalLeaveSchedules"></i>
				</button>
				<h4 class="modal-title"> Schedules</h4>
			</div>
			<div class="modal-body">
				<div id="leaveScheduleEvents"></div>
			</div>
		</div>
	</div>
</div>

<!--Status Approval, Leave approval modal-->
<div class="modal fade" id="modalStatusUpdateLeave" aria-hidden="true" style="z-index: 10;">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace" id="backStatusUpdateLeave"></i>
				</button>
				
				<h4 class="modal-title"> Update Status</h4>
			</div>
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="statusUpdateFormLeave" method="POST" action="">
					<input type="hidden" name="Leave_Id" id="formStatusLeaveId" />
					
					<div class="row">
						<div class="form-group">
							<label class="col-md-3 control-label">Status <span class="short_explanation">*</span></label>
							<div class="col-md-9">
								<select class="form-control vRequired" name="Status" id="statusApproval">
									<option value="Approved">Approved</option>
									<option value="Returned">Returned</option>
									<option value="Rejected">Rejected</option>
								</select>
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-3 control-label" id="labelStatusComment">Comment </label>
							<div class="col-md-9">
								<textarea name="comment" id="formStatusComment" rows="5" class="form-control vAlphaNumSpCDotHySlashNewLine" placeholder="Write your Comment..." ></textarea>
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="modal-footer text-center" id="formActionLeave">
				
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="statusUpdateFormResetLeave" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="statusUpdateFormSubmitLeave" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
				
			</div>
		</div>
	</div>
</div>


<?php if($leaveUser['Admin'] && (count($entitlementLeaveExist) > 0)) { ?>
<div class="modal fade" id="modalMonthlyLeaveAccrual" aria-hidden="true" style="z-index: 10;">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace" id="backMonthlyLeaveAccrual"></i>
				</button>
				
				<h4 class="modal-title">Monthly Leave Accrual</h4>
			</div>
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="statusMonthlyLeaveAccrual" method="POST" action="">
					<div class="row">
						<div class="form-group">
							<label class="col-md-3 control-label">Leave Accrual Month <span class="short_explanation">*</span></label>
							<div class="col-md-9">
								<select class="form-control vRequired" name="leaveAccrualMonth" id="leaveAccrualMonth">
									<?php			
										if (isset($orgDetails['Org_Code']) && strtolower(trim($orgDetails['Org_Code'])) == 'noncalendartest1') {
											echo '<option value="12,2024">December,2024</option>'; 
											echo '<option value="1,2025">January,2025</option>'; 
										}
										else
										{
											echo '<option value="'.$leaveAccrualDetails['value'] .'">'.$leaveAccrualDetails['text'] .'</option>'; 
										}
									?>
								</select>
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="modal-footer text-center" id="formMonthlyLeaveAccrual">
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="submitMonthlyLeaveAccrual" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
				
			</div>
		</div>
	</div>
</div>

<?php } ?>

<?php if ($leaveUser['Is_Manager'] == 1 || $leaveUser['Admin']) { ?>
<!--Confirmation Approval modal for multiple status update-->
<div class="modal fade" id="modalDirtyMultiStatusUpdateLeave" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeSubmitMultiStatusApprovalLeave"></i></button>
				<h4 class="modal-title"><strong>Approval</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure you want to approve these records ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noSubmitMultiStatusApprovalLeave">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="formSubmitMultiStatusApproval">Yes</button>
			</div>
		</div>
	</div>
</div>

<!--isDirty modal for status approval-->
<div class="modal fade" id="modalDirtyStatusUpdateLeave" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeStatusConfirmationLeaves"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noStatusConfirmationLeaves">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="formDirtyCloseStatusUpdateLeave">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } ?>

<!-- Form Dirty Confirmation Modal -->
<?php if ($leaveUser['Add'] == 1 || $leaveUser['Update'] == 1 || $leaveUser['LEdit'] > 0) { ?>
<div class="modal fade" id="modalDirtyLeave" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditConfirmationLeaves"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfirmationLeaves">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editDirtyCloseLeave">Yes</button>
			</div>
		</div>
	</div>
</div>

<!-- Delete Confirmation Modal -->
<?php } if ($leaveUser['Delete'] == 1) { ?>
<div class="modal fade" id="modalDeleteLeave" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" style="color: black;" id="closeDeleteConfirmationLeaves"></i></button>
				<h4 id="deleteConfirmationTitle" class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">
				<div id="leaveDeleteConfirmationHeading">
					Are you sure to delete?
				</div>
				<br>
				<div id="leaveDeleteWarningModalContent" style="text-align: center">
					Leave Records which are <b>Applied, Cancelled, Rejected,</b> and <b>Returned</b> can be deleted.
				</div>
			</div>
			
			<div class="modal-footer" id="leaveDeleteConfirmationFooter" style="padding: 20px">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfirmationLeaves">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="modalDeleteConfirmLeave">Yes</button>
			</div>
		</div>
	</div>
</div>
<?php } 

if ($leaveUser['Add'] == 1 || $leaveUser['Update'] == 1) { ?>
	<div class="modal fade" id="modalLapsedDays" aria-hidden="true" >
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeLapsedDaysConfirmation"></i></button>
					<h4 class="modal-title"><strong>Lapsed Days</strong> Confirmation</h4>
				</div>
				
				<div class="modal-body" id="lapsedDaysMessage"></div>
				
				<div class="modal-footer">
				  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="confirmNoLapsedDays">No</button>
				  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="confirmYesLapsedDays">Yes</button>
				</div>
			</div>
		</div>
	</div>
	<?php } 

}
//Leave Enacashment Form to do
if ($leaveEncashmentAccessRights['View'] && ((!empty($customFormNameC) && array_key_exists("Enable",$customFormNameC) && $customFormNameC['Enable'] == 1) || empty($customFormNameC))) {
	?>
	
	<!-- LeaveEncashment Grid Panel -->
	<div class="col-md-12 portlets add-panel-padding">
		<div class="panel" id="gridPanelLeaveEnacashment">
			<div class="panel-header md-panel-controls">
				<h3><i class="icon-list"></i> <strong id="lblFormNameC"><?php echo ((!empty($customFormNameC) && !empty($customFormNameC['New_Form_Name'])) ? $customFormNameC['New_Form_Name'] : $this->formNameC);?></strong></h3>
			</div>
			<div class="panel-content">
					<div class="m-b-10">   
						<?php /** If the employee is having access then present the apply leave encashment button from the leave closure month till the leave closure run */
						if ($leaveUser['Op_Choice'] == 1 &&  !empty($nonAutoEncashedLeave)) { ?>
						<!-- Apply Leave Encashment Button in Grid Toolbar -->
							<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="applyLeaveEncashment" title="Apply Leave Encashment" >
								<i class="hr-payroll-reimbursement"></i><span class="hidden-xs hidden-sm"> Apply Leave Encashment</span>
							</button>
						<?php }?>
				
						<!-- View Button in Grid Toolbar -->
						<button type="button" class="btn btn-secondary-default btn-embossed btn-off disabled toolbar-icons" id="viewLeaveEncashment" title="View" >
							<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
						</button>
						<!-- Filter Button in Grid Toolbar -->
						<a class="filter-toggle btn btn-white btn-embossed toolbar-icons" id="filterEncashmentLeave">
							<i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm"> Filter</span>
						</a>
						
					</div>
				
				<!-- LeaveEncashment Grid -->
				<table class="table table-hover table-dynamic table-striped" id="tableLeaveEncashment">
					<thead>
						<tr>
							<th></th>
							<th id="leaveEncashmentEmployeeId">Employee Id</th>
							<th id="leaveEncashmentEmployeeName">Employee Name</th>
							<th id="leaveEncashmentLeaveName">Leave Name</th>
							<th id="leaveEncashmentEncashedDays">Encashed Days</th>
							<th id="leaveEncashmentEncashedDate">Encashed Date</th>
							<th id="leaveEncashmentStatus">Status</th>
						</tr>
					</thead>
					<tbody>
					
					</tbody>
				</table>
				
			</div>
		</div>
	</div>
	
	<!-- Your custom menu with dropdown-menu as default styling -->
	<div id="contextMenuLeaveEncashment" class="context-menu">
		<ul class="dropdown-menu" role="menu">
			<li><a tabindex="-1" id="viewContextLeaveEncashment"><i class="mdi-action-visibility"></i> View</a></li>
		</ul>
	</div>

	<!-- Filter Leave encashment -->
<div class="builder" id="filterPanelLeaveEncashment">
	<div id="closeFilterLeaveEncashment"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel" style="width: 100%;" id="filterResetLeaveEncashment">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="filterApplyLeaveEncashment">Apply</button>
			
			<div class="form-group">
				<label>Employee Name</label>
				<input type="text" class="form-control" id="filter_LE_EmployeeName">
			</div>
			<div class="form-group">
				<label>Leave Name</label>
				<select class="form-control" data-search="true" id="filter_LE_Leaves" >
					<option value="">All</option>
					<?php
					foreach ( $leaveTypes as $key => $row )
					{
						echo '<option value="'. $key .'">'. $row .'</option>';
					}
					?>
				</select>
			</div>
			<div class="form-group">
				<label>Encash Date</label>
				<div class="input-daterange b-datepicker input-group" data-date-format="<?php echo $dformat; ?>" id="datepicker">
					<input type="text" class="input-md form-control" name="start" id="filter_LE_DateBegin" data-orientation="top" placeholder="Begin" />
					<span class="input-group-addon">to</span>
					<input type="text" class="input-md form-control" name="end" id="filter_LE_DateEnd" data-orientation="top" placeholder="End" />
				</div>
			</div>
			
			<div class="form-group">
				<label>Status</label>
				<select class="form-control" data-search="true" id="filter_LE_Status" >
					<option value="">All</option>
					<option value="Applied">Applied</option>
					<option value="Paid">Paid</option>
				</select>
			</div>
		</div>
	</div>
</div>	
	

	<!-- Modal for View Form -->
<div class="modal fade" id="modalFormLeaveEncashment" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" aria-hidden="true" style="margin-right: 10px;">
					<i class="mdi-hardware-keyboard-backspace" id="backViewLeaveEncashment"></i>
				</button>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<!--View Leave Encashment Form-->
				<form role="form" id="viewFormLeaveEncashment" >
					<div class="row">
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Employee Name</label></div>
							<div class="col-md-7"><p id="vEncashEmployeeName"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Leave Name</label></div>
							<div class="col-md-7"><p id="vEncashLeaveName"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Encashed Days</label></div>
							<div class="col-md-7"><p id="vEncashedDays"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Encashed Year</label></div>
							<div class="col-md-7"><p id="vEncashedYear"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Encashed Date</label></div>
							<div class="col-md-7"><p id="vEncashedDate"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Encashed Status</label></div>
							<div class="col-md-7"><p id="vEncashedStatus"></p></div>
						</div>
					</div>
				</form>	
			</div>
		</div>
	</div>
</div>
<?php }

if ($leaveTypeUser['View'] && ((!empty($customFormNameB) && array_key_exists("Enable",$customFormNameB) && $customFormNameB['Enable'] == 1) || empty($customFormNameB))) {
?>

													<!-- Leave Type Form -->

<!-- LeaveType Grid Panel -->
<div class="col-md-12 portlets add-panel-padding">
	<div class="panel" id="gridPanelLeaveType">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong id="lblFormNameB"><?php echo ((!empty($customFormNameB) && !empty($customFormNameB['New_Form_Name'])) ? $customFormNameB['New_Form_Name'] : $this->formNameB);?></strong></h3>
		</div>
		<div class="panel-content">
				<div class="m-b-10">
					
					<!-- Add Button in Grid Toolbar -->
					<?php if ($leaveTypeUser['Add'] == 1) { ?>
					
					<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="addLeaveType" title="Add" data-toggle="modal" data-target="#modalFormLeaveType" >
						<i class="mdi-content-add"></i><span class="hidden-xs hidden-sm"> Add</span>
					</button>
					
					<?php } ?>
					
					<!-- View Button in Grid Toolbar -->
					<button type="button" class="btn btn-secondary-default btn-embossed btn-off disabled toolbar-icons" id="viewLeaveType" title="View" >
						<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
					</button>
					
					<!-- Edit Button in Grid Toolbar -->
					<?php if ($leaveTypeUser['Update'] == 1) { ?>
					
					<button type="button" class="btn btn-secondary-default btn-embossed btn-off disabled toolbar-icons" id="editLeaveType" title="Edit" >
						<i class="mdi-editor-mode-edit"></i><span class="hidden-xs hidden-sm"> Edit</span>
					</button>
					
					<?php } if ($leaveTypeUser['Delete'] == 1) { ?>
					
					<!-- Delete Button in Grid Toolbar -->
					<button type="button" class="btn btn-secondary-default btn-embossed btn-off disabled toolbar-icons" id="deleteLeaveType" data-toggle="modal" data-target="#modalDeleteLeaveType" title="Delete">
						<i class="mdi-action-delete"></i><span class="hidden-xs hidden-sm"> Delete</span>
					</button>
					
					<?php } ?>

					<?php if ($leaveTypeUser['Is_Manager'] == 1 || $leaveTypeUser['Admin'] == 'admin') { ?>
			
					<button type="button" class="btn btn-white btn-embossed visible-lg-* toolbar-icons" id="exportLeaveType" title="Export">
						<i class="mdi-communication-import-export"></i><span class="hidden-xs hidden-sm"> Export</span>
					</button>
					
					<?php }?>
					
					<!-- Filter Button in Grid Toolbar -->
					<a class="filter-toggle btn btn-white btn-embossed toolbar-icons" id="filterLeaveType">
						<i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm"> Filter</span>
					</a>
					
				</div>
			
			<!-- LeaveType Grid -->
			<table class="table table-hover table-dynamic table-striped" id="tableLeaveType">
				<thead>
					<tr>
						<th></th>
						<th id="leaveTypeLeaveName">Leave Name</th>
						<th id="leaveTypeLeaveType">Leave Type</th>
						<th id="leaveTypesTotalDays">Leave Eligibility In Days</th>
						<th id="leaveTypeEncashment">Encashment</th>
						<th id="leaveTypeCarryOver">Carry Over</th>
						<th id="leaveTypeLeaveStatus">Leave Status</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
			
		</div>
	</div>
</div>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="contextMenuLeaveType" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextLeaveType"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($leaveTypeUser['Update'] == 1) { ?>
		<li><a tabindex="-1" id="editContextLeaveType"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<?php } if ($leaveTypeUser['Delete'] == 1) { ?>
		<li><a tabindex="-1" id="deleteContextLeaveType"><i class="mdi-action-delete"></i> Delete</a></li>
		<?php } ?>
	</ul>
</div>

<!--Filter Form-->
<div class="builder" id="filterPanelLeaveType">
	<!--<a class="filter-toggle"><i class="glyphicon glyphicon-filter"></i></a>-->
	<div id="closeFilterLeaveType"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel" style="width: 100%;" id="filterResetLeaveType">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="filterApplyLeaveType">Apply</button>
			
			<div class="form-group">
				<label>Leave Name</label>
				<input type="text" class="form-control" id="filterLeaveName" placeholder="Leave Name">
			</div>
			
			<div class="form-group">
				<label>Leave Type</label>
				<select class="form-control" data-search="true" id="filterLeave_Type" >
					<option value="">All</option>
					<option value="Paid Leave">Paid Leave</option>
					<option value="Unpaid Leave">Unpaid Leave</option>
					<option value="On Duty">On Duty</option>
				</select>
			</div>
			
			<div class="form-group">
				<label>Carry Over</label>
				<select class="form-control" data-search="true" id="filterCarryOver" >
					<option value="">All</option>
					<option value="Yes">Yes</option>
					<option value="No">No</option>
				</select>
			</div>
			
			<div class="form-group">
				<label>Leave Encashment</label>
				<select class="form-control" data-search="true" id="filterLeaveEncashment" >
					<option value="">All</option>
					<option value="Yes">Yes</option>
					<option value="No">No</option>
				</select>
			</div>
			
			<div class="form-group">
				<label>Status</label>
				<select class="form-control" data-search="true" id="filterLTStatus" >
					<option value="">All</option>
					<option value="Active">Active</option>
					<option value="InActive">InActive</option>
				</select>
			</div>
		</div>
	</div>
</div>

<!--View, Add, Edit Form Modal-->
<div class="modal fade" id="modalFormLeaveType" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" aria-hidden="true" style="margin-right: 10px;">
					<i class="mdi-hardware-keyboard-backspace" id="backLeaveType"></i>
				</button>
				
				<?php if ($leaveTypeUser['Update'] == 1) { ?>
				<button type="button" class="close form-icons" aria-hidden="true" id="editInViewLeaveType">
					<i class="mdi-editor-mode-edit"></i>
				</button>
				<?php } ?>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<!--View LeaveType Form-->
				<form role="form" id="viewFormLeaveType" >
					<div class="row">
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Leave Name</label></div>
							<div class="col-md-7"><p id="vLeaveName"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Leave Type</label></div>
							<div class="col-md-7"><p id="vFormLeaveType"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Leave Enforcement Configuration</label></div>
							<div class="col-md-7"><p id="vLeaveEnforcementConfiguration"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Leave Closure Based On</label></div>
							<div class="col-md-7"><p id="viewLeaveClosureBasedOn"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Replenishment Limit</label></div>
							<div class="col-md-7"><p id="viewReplenishmentLimit"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Leave Closure Month</label></div>
							<div class="col-md-7"><p id="viewLeaveClosureMonth"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Leave Closure Start Date</label></div>
							<div class="col-md-7"><p id="viewLeaveClosureStartDate"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Leave Closure End Date</label></div>
							<div class="col-md-7"><p id="viewLeaveClosureEndDate"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Coverage</label></div>
							<div class="col-md-7"><p id="vLeaveCoverage"></p></div>
						</div>
						<div class="form-group viewLeaveCoverageBasedHidden">
							<div class="col-md-5"><label class="control-label">Grade</label></div>
							<div class="col-md-7"><p id="vLeaveGrade"></p></div>
						</div>
						<div class="form-group viewLeaveCustomGroupCoverageBasedHidden">
							<div class="col-md-5"><label class="control-label">Custom Group</label></div>
							<div class="col-md-7"><p id="vLeaveTypeCustomGroup"></p></div>
						</div>
						<div class="form-group viewLeaveCustomGroupCoverageBasedHidden">
							<div class="col-md-5"><label class="control-label">Custom Group Employees</label></div>
							<div class="col-md-7"><p><span id="vLeaveTypeCustomGroupEmployees"></span><i class="icon-info vLeaveTypeCustomGroupEmpToolTip" rel="popover" data-container="body"
								 	data-toggle="popover"	data-placement="top" data-content="">
								</i></p></div>
						</div>                        
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Leave Eligibility In Days</label></div>
							<div class="col-md-7"><p id="vFormTotalDays"></p></div>
						</div>
				        <div class="form-group">
							<div class="col-md-5"><label class="control-label">Leave Applicable</label></div>
							<div class="col-md-7"><p id="vApplicableOnProbation"></p></div>
						</div>
						<div class="form-group" id="viewPanelLeaveActivationAfter">
							<div class="col-md-5"><label class="control-label">Leave Activation After</label></div>
							<div class="col-md-7"><p id="vLeaveActivationAfter"></p></div>
						</div>
                        <div class="form-group">
							<div class="col-md-5"><label class="control-label">Applicable during Notice Period</label></div>
							<div class="col-md-7"><p id="vApplicableOnNoticePeriod"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Enable Proration</label></div>
							<div class="col-md-7"><p id="vEnableProration"></p></div>
						</div>
						
						<div class="form-group" id="viewPanelProrateLeaveBalanceFrom">
							<div class="col-md-5"><label class="control-label">Prorate Leave Balance From</label></div>
							<div class="col-md-7"><p id="viewProrateLeaveBalanceFrom"></p></div>
						</div>		
						<div class="form-group" id="viewPanelProrateAfter">
							<div class="col-md-5"><label class="control-label">Prorate After</label></div>
							<div class="col-md-7"><p id="viewProrateAfter"></p></div>
						</div>

						<div class="form-group" id="viewPanelProbationDateRoundUp">
							<div class="col-md-5"><label class="control-label">Proration Threshold Date</label></div>
							<div class="col-md-7"><p id="viewProbationDateRoundUp"></p></div>
						</div>		
						<div class="form-group" id="viewPanelSpecificDay">
							<div class="col-md-5"><label class="control-label">Day of the month</label></div>
							<div class="col-md-7"><p id="viewSpecificDay"></p></div>
						</div>
						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Carry Over</label></div>
							<div class="col-md-7"><p id="vCarryOver"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Carry Over Limit</label></div>
							<div class="col-md-7"><p id="vCarryOverLimit"></p></div>
						</div>
                        <div class="form-group">
							<div class="col-md-5"><label class="control-label">Accumulation Limit</label></div>
							<div class="col-md-7"><p id="vCarryOverAccumulationLimit"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Leave Encashment</label></div>
							<div class="col-md-7"><p id="vLeaveEncashment"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Leave Encashment Limit</label></div>
							<div class="col-md-7"><p id="vLeaveEncashmentLimit"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Minimum Limit</label></div>
							<div class="col-md-7"><p id="vFormMinTotalDays"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Maximum Limit</label></div>
							<div class="col-md-7"><p id="vMaximumLimit"></p></div>
						</div>
                        <div class="form-group">
							<div class="col-md-5"><label class="control-label">Auto Encashment</label></div>
							<div class="col-md-7"><p id="vLeaveAutoEncashment"></p></div>
						</div>

						<div class="form-group" id="viewPanelLeaveEncashmentForFF">
							<div class="col-md-5"><label class="control-label">Leave Encashment For F&F</label></div>
							<div class="col-md-7"><p id="vLeaveEncashmentFF"></p></div>
						</div>

						<div class="form-group" id="viewPanelLeaveDeductionForFF">
							<div class="col-md-5"><label class="control-label">Leave Deduction For F&F</label></div>
							<div class="col-md-7"><p id="vLeaveDeductionFF"></p></div>
						</div>

						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Frequency</label></div>
							<div class="col-md-7"><p id="vFrequency"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Display in Payslip</label></div>
							<div class="col-md-7"><p id="vDisplayInPayslip"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Document Submission</label></div>
							<div class="col-md-7"><p id="vDocumentUpload"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Threshold limit for Document Submission</label></div>
							<div class="col-md-7"><p id="vMaxDaysDocumentUpload"></p></div>
						</div>
                        <div class="form-group">
							<div class="col-md-5"><label class="control-label">Leave Period</label></div>
							<div class="col-md-7"><p id="vleavePeriod"></p></div>
						</div>
						<div class="form-group" id="viewPanelAccrual">
							<div class="col-md-5"><label class="control-label">Accrual</label></div>
							<div class="col-md-7"><p id="vAccrual"></p></div>
						</div>
                        <div id="viewMonthsList" style="padding: 10px;">
							<div class="form-group" >
								<div class="col-md-2 col-sm-4 col-xs-6" style="padding: 15px" >
									<div style="padding-bottom: 5px">Jan</div>
									<b><p id="viewMonth_1"></p></b>
								</div>
								<div class="col-md-2 col-sm-4 col-xs-6" style="padding: 15px" >
									<div style="padding-bottom: 5px">Feb</div>
									<b><p id="viewMonth_2"></p></b>
								</div>
								<div class="col-md-2 col-sm-4 col-xs-6" style="padding: 15px" >
									<div style="padding-bottom: 5px">Mar</div>
									<b><p id="viewMonth_3"></p></b>
								</div>
								<div class="col-md-2 col-sm-4 col-xs-6" style="padding: 15px" >
									<div style="padding-bottom: 5px">Apr</div>
									<b><p id="viewMonth_4"></p></b>
								</div>
								<div class="col-md-2 col-sm-4 col-xs-6" style="padding: 15px" >
									<div style="padding-bottom: 5px">May</div>
									<b><p id="viewMonth_5"></p></b>
								</div>
								<div class="col-md-2 col-sm-4 col-xs-6" style="padding: 15px" >
									<div style="padding-bottom: 5px">Jun</div>
									<b><p id="viewMonth_6"></p></b>
								</div>
							</div>
							<div class="form-group">
								<div class="col-md-2 col-sm-4 col-xs-6" style="padding: 15px" >
									<div style="padding-bottom: 5px">Jul</div>
									<b><p id="viewMonth_7"></p></b>
								</div>
								<div class="col-md-2 col-sm-4 col-xs-6" style="padding: 15px" >
									<div style="padding-bottom: 5px">Aug</div>
									<b><p id="viewMonth_8"></p></b>
								</div>
								<div class="col-md-2 col-sm-4 col-xs-6" style="padding: 15px" >
									<div style="padding-bottom: 5px">Sep</div>
									<b><p id="viewMonth_9"></p></b>
								</div>
								<div class="col-md-2 col-sm-4 col-xs-6" style="padding: 15px" >
									<div style="padding-bottom: 5px">Oct</div>
									<b><p id="viewMonth_10"></p></b>
								</div>
								<div class="col-md-2 col-sm-4 col-xs-6" style="padding: 15px" >
									<div style="padding-bottom: 5px">Nov</div>
									<b><p id="viewMonth_11"></p></b>
								</div>
								<div class="col-md-2 col-sm-4 col-xs-6" style="padding: 15px" >
									<div style="padding-bottom: 5px">Dec</div>
									<b><p id="viewMonth_12"></p></b>							
								</div>
							</div>
						</div>
                        <div class="form-group" id="vieweligibleOnPeriod">
							<div class="col-md-5"><label class="control-label">Eligible Days Based On Period</label></div>
							<div class="col-md-7"><p id="vEligibleDaysBasedOnPeriod"></p></div>
						</div>
						<div class="form-group" id="viewPanelAccrualRestrictionPeriod">
							<div class="col-md-5"><label class="control-label">Accrual Restriction For a Period</label></div>
							<div class="col-md-7"><p id="viewAccrualRestrictionPeriod"></p></div>
						</div>
						<div class="form-group" id="viewPanelAccrualUntil">
							<div class="col-md-5"><label class="control-label">Accrual Until</label></div>
							<div class="col-md-7"><p id="viewAccrualUntil"></p></div>
						</div>

						<div class="form-group" id="viewPanelAccumulateEligibleDays">
							<div class="col-md-5"><label class="control-label">Accumulate Eligible Days</label></div>
							<div class="col-md-7"><p id="vAccumulateEligibleDays"></p></div>
						</div>
						<div class="form-group" id="viewPanelAccumulateFrom">
							<div class="col-md-5"><label class="control-label">Accumulate From</label></div>
							<div class="col-md-7"><p id="viewAccumulateFrom"></p></div>
						</div>		
						<div class="form-group" id="viewPanelAccumulateAfter">
							<div class="col-md-5"><label class="control-label">Accumulate After</label></div>
							<div class="col-md-7"><p id="viewAccumulateAfter"></p></div>
						</div>
						<?php
						if ($leaveSettings['Enable_Workflow'] == "Yes")
						{
							echo'
								<div class="form-group">
									<div class="col-md-5"><label class="control-label">Approval Workflow</label></div>
									<div class="col-md-7"><p id="vapprovalWorkflow"></p></div>
								</div>
							';
						}
						?>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Gender</label></div>
							<div class="col-md-7"><p id="vGender"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Leave Approval Cutoff</label></div>
							<div class="col-md-7"><p id="vLeaveApprovalCutoff"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Leave Calculation Days</label></div>
							<div class="col-md-7"><p id="vLeaveCalculationDays"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Advance Notification</label></div>
							<div class="col-md-7"><p id="vLeaveAdvanceNotification"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Show Balance</label></div>
							<div class="col-md-7"><p id="vShowStatisticsInDashboard"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Restrict Employee To Apply</label></div>
							<div class="col-md-7"><p id="vRestrictEmployeeToApply"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Enable Leave Exception</label></div>
							<div class="col-md-7"><p id="vEnableLeaveException"></p></div>
						</div>
						<div class="form-group viewPanelEnableLeaveException">
							<div class="col-md-5"><label class="control-label">Half Paid Leave Minimum Limit</label></div>
							<div class="col-md-7"><p id="vMinimumLimit"></p></div>
						</div>
						<div class="form-group viewPanelEnableLeaveException">
							<div class="col-md-5"><label class="control-label">Half Paid Leave Deduction</label></div>
							<div class="col-md-7"><p id="vHalfPaidLeaveDeduction"></p></div>
						</div>
						<div class="form-group viewPanelEnableLeaveException">
							<div class="col-md-5"><label class="control-label">Leave Unit</label></div>
							<div class="col-md-7"><p id="vLeaveUnit"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Parent Leave</label></div>
							<div class="col-md-7"><p id="vParentLeave"></p></div>
						</div>
						<div class="form-group" id="vParentLeavePanel">
							<div class="col-md-5"><label class="control-label">Parent Leave Name</label></div>
							<div class="col-md-7"><p id="vParentLeaveName"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Enable Personal Choice</label></div>
							<div class="col-md-7"><p id="vEnablePersonalChoice"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Leave Status</label></div>
							<div class="col-md-7"><p id="vLeaveStatus"></p></div>
						</div>
					</div>
					<!--Additional Information Block-->
					<div class="row additionalInfoPanel">
						<hr class="view-hr"/>
						
						<div class="form-group" style="font-size: large;margin-left: 13px;">
							<label class="control-label text-center">Additional Information</label>
						</div>
						
						<div class="form-group additionalInformation">
							<div class="col-md-5"><label class="control-label">Added On</label></div>
							<div class="col-md-7"><p id="addedOnLeaveType"></p></div>
						</div>
						
						<div class="form-group additionalInformation">
							<div class="col-md-5"><label class="control-label">Added By</label></div>
							<div class="col-md-7"><p id="addedByLeaveType"></p></div>
						</div>
						
						<div class="form-group additionalInformation updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated On</label></div>
							<div class="col-md-7"><p id="updatedOnLeaveType"></p></div>
						</div>
						
						<div class="form-group additionalInformation updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated By</label></div>
							<div class="col-md-7"><p id="updatedByLeaveType"></p></div>
						</div>
					</div>
				</form>
				
				<?php if (($leaveTypeUser['Add'] == 1 || $leaveTypeUser['Update'] == 1)) { ?>
				
				<!--Add/Edit LeaveType Form-->
				<form role="form" class="form-horizontal form-validation" id="editFormLeaveType" method="POST" action="">
					<input type="hidden" name="LeaveType_Id" id="LeaveType_Id" />
					<input type="hidden" name="Used_Leave_Type" id="Used_Leave_Type" />
					<input type="hidden" name="Login_Emp_Id" id="Login_Emp_Id" value="<?php echo $leaveTypeUser['Login_Emp_Id']; ?>" />
					
					<div class="row">
						
						<div class="form-group">
							<label class="col-md-4 control-label">Leave Name <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<input type="text" name="Leave_Name" class="form-control vName alphaNumSpCDotHySlash" placeholder="Leave Name" id="Leave_Name" >
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Leave Type <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control vRequired" id="Leave_Type" data-search="true" name="Leave_Type" data-placeholder="Leave Type">
									<option value="">-- Select --</option>
									<option value="Paid Leave">Paid Leave</option>
									<option value="Unpaid Leave">Unpaid Leave</option>
									<option value="On Duty">On Duty</option>
								</select>
							</div>
                        </div>

						<div class="form-group">
							<label class="col-md-4 control-label">Leave Enforcement Configuration </label>
							<div class="col-md-8">
								<select class="form-control" id="leaveEnforcementConfiguration" data-search="true" name="leaveEnforcementConfiguration">
									<option value="1">Normal Leave</option>
									<option value="2">Service Based Leave I</option>
									<option value="3">Quarter Wise Leave</option>
									<option value="4">Maternity Leave Slabs</option>
									<option value="5">Service Based Leave II</option>
									<option value="6">Roster Leave</option>
								</select>
							</div>
                        </div>

						<div class="form-group formPanelLeaveClosureBasedOn">
							<label class="col-md-4 control-label">Leave Closure Based On<span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control vRequired" data-search="true" id="formLeaveClosureBasedOn" name="Leave_Closure">
										<option value="Employee Date Of Join">Employee Date Of Join</option>
										<option value="Selected Month">Selected Month</option>
										<option value="Limited Replenishment on Approval">Limited Replenishment on Approval</option>
										<option value="Unlimited Replenishment on Approval">Unlimited Replenishment on Approval</option>
										<option value="Employee Service">Employee Service</option>
								</select>
							</div>
						</div>

						<div class="form-group" id="replenishmentLimitPanel" style="display: none;">
							<label class="col-md-4 control-label">Replenishment Limit<span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<input type="number" name="Replenishment_Limit" class="form-control vRequired" placeholder="Replenishment Limit" id="replenishmentLimit"  min="0" max="127" step="1" >
							</div>
						</div>

						<div class="form-group formPanelLeaveClosureMonth">
							<label class="col-md-4 control-label">Leave Closure Month<span class="short_explanation">*</span>
							<i class="icon-info" rel="popover" data-container="body" data-toggle="popover" data-placement="top" data-content="Leave closure month is a month in which, the leaves for that year will be closed and based on this, the leave can be carried over or encashed. "></i>
							</label>
							<div class="col-md-4">
								<select class="form-control vRequired" data-search="true" id="formLeaveClosureMonth" name="Closure_Month">									
									<?php
										foreach ($listMonth as $key => $row) {
											echo '<option value="'.$key.'">'.$row.'</option>';
										}
									?>
								</select>
							</div>
							<div class="col-md-4">
								<select class="form-control vRequired" data-search="true" id="formLeaveClosureEndYear" name="Closure_End_Year">
									<?php
										foreach ($leaveClosureYearList as $key => $row) {
											echo '<option value="'.$key.'">'.$row.'</option>';
										}
									?>
								</select>
							</div>
						</div>

						<div class="form-group leaveClosureDates">
							<label class="col-md-4 control-label">Leave Closure Dates</label>
							<div class="col-md-8">
								<p id="viewLeaveClosureDates"></p>
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Coverage <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control" data-search="true" id="Leave_Coverage" name="Leave_Coverage">									
									<option value="ORG">Organization</option>
									<!-- <option value="GRA">Grade</option> -->
									<option value="CUSTOMGROUP">Custom Group</option>
								</select>								
							</div>
						</div>
						
						<div class="form-group leaveCoverageBasedHidden">
							<label class="col-md-4 control-label" id="labelLeaveGrade">Grade</label>
							<div class="col-md-8">
								<select multiple="multiple" class="form-control vRequired selectAlll" data-search="true" id="Leave_Grade" name="Leave_Grade">
									<option value="selectAll">--Select all--</option>
									<option value="clearAll">--Clear all--</option>
									<?php
										// foreach ($gradesPair as $key => $row)
										// {
										// echo '<option value="'.$key.'">'.$row.'</option>';
										// }
									?>
								</select>
							</div>
						</div>

						<div class="form-group leaveCustomGroupCoverageBasedHidden">
							<label class="col-md-4 control-label">Custom Group <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control" data-search="true" id="Leave_Type_Custom_Group" name="Leave_Type_Custom_Group">
								</select>
							</div>
						</div>
						
						<div class="form-group" id="eligibleTotalDays">
						<!-- <div class="form-group"> -->
							<label class="col-md-4 control-label">Leave Eligibility In Days <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<input type="number" name="LT_Total_Days" class="form-control vRequired" placeholder="Total days" id="LT_Total_Days"  min="0.25" max="365" step="0.25" >
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Applicable during Notice Period <span class="short_explanation">*</span></label>
							<div class="col-md-8 form-group togglebutton togglebutton-material-blue" style="margin-left: 10px;">
								<label>
									<input type="checkbox" class="col-sm-9 md-checkbox" id="ApplicableDuringNoticePeriod" name="ApplicableDuringNoticePeriod">
								</label>
							</div>
                        </div>                        
                        
                        <div class="form-group">
							<label class="col-md-4 control-label">Leave Applicable<span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control vRequired" data-search="true" id="ApplicableDuringProbation" name="ApplicableDuringProbation">																			
									<option value="Date Of Join">Date Of Join</option>
									<option value="During Probation">During Probation</option>
									<option value="After Probation">After Probation</option>
									<option value="Custom Configuration">Custom Configuration</option>
								</select>
							</div>
                        </div>

						<div class="form-group" id="panelLeaveActivationDays">
							<label class="col-md-4 control-label">Leave Activation After <span class="short_explanation">*</span></label>
							<div class="col-md-6">
								<input type="number" name="Leave_Activation_Days" class="form-control onlyNum vRequired" placeholder="Leave Activation After" id="Leave_Activation_Days" min="0" max="5000">
							</div>
							<label class="col-sm-2 control-label">day(s)</label>
						</div>

						<div class="form-group">
							<label class="col-md-4 control-label">Enable Proration 
								<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
									data-placement="top" data-content="Disabling the proration will not prorate the number of leave days for this leave type during employee onboarding, and if it is a paid leave, there will not be any calculation to deduct the additional leaves taken during full and final settlement.">
								</i>
							</label>
							<div class="col-md-8 form-group togglebutton togglebutton-material-blue" style="margin-left: 10px;">
								<label>
									<input type="checkbox" class="col-sm-9 md-checkbox" id="EnableProration" name="EnableProration">
								</label>
							</div>
                        </div> 

						<div class="form-group" id="panelProrateLeaveBalanceFrom">
							<label class="col-md-4 control-label">Prorate Leave Balance From<span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control" id="prorateLeaveBalanceFrom" data-search="true" name="Prorate Leave Balance From" data-placeholder="Prorate Leave Balance From">
									<option value="Date Of Join">Date Of Join</option>
									<option value="After Probation">After Probation</option>
									<option value="Custom Configuration">Custom Configuration</option>
								</select>
							</div>
                        </div>

						<div class="form-group" id="panelProrateAfter">
							<label class="col-md-4 control-label">Prorate After<span class="short_explanation">*</span></label>
							<div class="col-md-6">
								<input type="number" name="Prorate_After" class="form-control onlyNum vRequired" placeholder="Prorate After" id="prorateAfter" min="1" max="5000">
							</div>
							<label class="col-sm-2 control-label">day(s)</label>
						</div>

						<div class="form-group" id="panelProrationDateRoundUp">
							<label class="col-md-4 control-label">Proration Threshold Date<span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control" id="prorationDateRoundUp" data-search="true" name="Proration Date Round Up" data-placeholder="Proration Date Round Up">
									<option value="Any day of the month">Any day of the month</option>	
									<option value="Before specific day of the month">Before specific day of the month</option>
								</select>
							</div>
                        </div>

						<div class="form-group" id="panelSpecificDay">
							<label class="col-md-4 control-label">Day of the month<span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<input type="number" name="Day_Of_The_Month" class="form-control onlyNum vRequired" placeholder="Day of the month" id="specificDay" min="1" max="28">
							</div>
						</div>

						<div class="form-group">
							<label class="col-md-4 control-label">Carry Over <span class="short_explanation">*</span></label>
							<div class="col-md-8 form-group togglebutton togglebutton-material-blue" style="margin-left: 10px;">
								<label>
									<input type="checkbox" class="col-sm-9 md-checkbox" id="Carry_Over" name="Carry_Over">
								</label>
							</div>
                        </div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Carry Over Limit</label>
							<div class="col-md-8">
								<input type="number" name="Carry_Over_Limit" class="form-control" placeholder="Carry Over Limit" id="Carry_Over_Limit" min="0" max="365" step="0.25">
							</div>
						</div>
                        
                        <div class="form-group">
							<label class="col-md-4 control-label">Accumulation Limit</label>
							<div class="col-md-8">
								<input type="number" name="Carry_Over_Accumulation_Limit" class="form-control" placeholder="Accumulation Limit" id="Carry_Over_Accumulation_Limit" min="0" max="1500" step="0.25">
							</div>
						</div>

						<div class="form-group">
							<label class="col-md-4 control-label">Minimum Limit <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<input type="number" name="LT_Min_Total_Days" class="form-control vRequired" placeholder="Minimum Limit" id="LT_Min_Total_Days" min="0.25" max="365" step="0.25">
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Maximum Limit <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<input type="number" name="Maximum_Limit" class="form-control vRequired" placeholder="Maximum Limit" id="Maximum_Limit" min="0.25" step="0.25">
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Leave Encashment <span class="short_explanation">*</span></label>
							<div class="col-md-8 form-group togglebutton togglebutton-material-blue" style="margin-left: 10px;">
								<label>
									<input type="checkbox" class="col-sm-9 md-checkbox" id="Leave_Encashment" name="Leave_Encashment">
								</label>
							</div>
                        </div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Leave Encashment Limit</label>
							<div class="col-md-8">
								<input type="number" name="Leave_Encashment_Limit" class="form-control" placeholder="Leave Encashment Limit" id="Leave_Encashment_Limit" min="0" max="365" step="0.25">
							</div>
						</div>
						
                        <div class="form-group" style="display: none;">
							<label class="col-md-4 control-label">Auto Encashment</label>
							<div class="col-md-8 form-group togglebutton togglebutton-material-blue" style="margin-left: 10px;">
								<label>
									<input type="checkbox" class="col-sm-9 md-checkbox" id="Auto_Encashment" name="Auto_Encashment">
								</label>
							</div>
                        </div>
                        
						<div class="form-group" id="panelLeaveEncashmentForFF">
							<label class="col-md-4 control-label">Leave Encashment For F&F <span class="short_explanation">*</span>
							<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
							data-placement="top" data-content="Enabling leave encashment for 'Full and Final Settlement' will activate the leave encashment for the remaining leave days from the employee's eligibility.">
							</i></label>
							<div class="col-md-8 form-group togglebutton togglebutton-material-blue" style="margin-left: 10px;">
								<label>
									<input type="checkbox" class="col-sm-9 md-checkbox" id="leaveEncashmentForFF" name="Leave_Encashment_For_FF">
								</label>
							</div>
                        </div>

						<div class="form-group" id="panelLeaveDeductionForFF">
							<label class="col-md-4 control-label">Leave Deduction For F&F <span class="short_explanation">*</span>
							<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
							data-placement="top" data-content="Enabling leave deduction for 'Full and Final Settlement' will activate the deduction for additional leaves taken during the employee's tenure.">
							</i>
							</label>
							<div class="col-md-8 form-group togglebutton togglebutton-material-blue" style="margin-left: 10px;">
								<label>
									<input type="checkbox" class="col-sm-9 md-checkbox" id="leaveDeductionForFF" name="Leave_Deduction_For_FF">
								</label>
							</div>
                        </div>

						<div class="form-group">
							<label class="col-md-4 control-label">Frequency <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<input type="number" name="Frequency" class="form-control vRequired" placeholder="Frequency" id="Frequency" min="0" max="365">
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Display in Payslip</label>
							<div class="col-md-8 form-group togglebutton togglebutton-material-blue" style="margin-left: 10px;">
								<label>
									<input type="checkbox" class="col-sm-9 md-checkbox" id="Display_In_Payslip" name="Display_In_Payslip">
								</label>
							</div>
												</div>
												
									<div class="form-group">
							<label class="col-md-4 control-label">Document Submission <span class="short_explanation">* </span> 
							<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
                                                                       data-placement="top" data-content=" Based on this button we will enable the document upload in leaves form">
								</i></label>
							<div class="col-md-8 form-group togglebutton togglebutton-material-blue" style="margin-left: 10px;">
								<label>
									<input type="checkbox" class="col-sm-9 md-checkbox" id="Document_Upload" name="Document_Upload">
								</label>
							</div>
									</div>
												
									<div class="form-group" id="maxDays_DocumentUpload">
							<label class="col-md-4 control-label">Threshold limit for Document Submission (in days) <span class="short_explanation" id ="isMandatoryField">* </span> <i class="icon-info" rel="popover" 
										data-container="body" data-toggle="popover" data-placement="top" data-content=" Threshold limit for which the document upload is mandatory in leaves form"></i>
							</label>
							<div class="col-md-8">
								<input type="number" name="maxDaysForDocumentUpload" class="form-control vRequired" placeholder="max days for document upload" id="maxDaysForDocumentUpload">
									</div>
								</div>
						
                        <div class="form-group">
							<label class="col-md-4 control-label">Leave Period </label>
							<div class="col-md-8">
								<select class="form-control" data-search="true" id="leavePeriod" name="Period">
									<option value="1">Monthly</option>
									<option value="3">Quarterly</option>
									<option value="6">Half Yearly</option>
                                    <option value="12">Annually</option>
								</select>
							</div>
                        </div>

						<div class="form-group leaveAccrualPanel">
							<label class="col-md-4 control-label">Accrual </label>
							<div class="col-md-8">
								<select class="form-control" data-search="true" id="leaveAccrual" name="leaveAccrual">
									<option value="From the beginning of the leave period">From the beginning of the leave period</option>
									<option value="After the end of the leave period">After the end of the leave period</option>
									<option value="Based on the custom configuration">Based on the custom configuration</option>
								</select>
							</div>
                    	</div>
                        
                        <div class="form-group" id="eligibleOnPeriod">
							<label class="col-md-4 control-label">Eligible Days Based On Period <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<input type="number" name="EligibleDaysBasedOnPeriod" class="form-control" placeholder="Eligible Days Based On Period" id="EligibleDaysBasedOnPeriod" min="0.25" max="365" step="0.25" data-rule-checkMaxLimit="true" data-msg-checkMaxLimit="Eligible days should not be greater than total days" >
							</div>
						</div>
                        
                        <div id="monthsList" style="padding: 10px;">
							<div class="form-group" >
								<div class="col-md-2 col-sm-4 col-xs-6" style="padding: 15px" >
									<div>Jan</div>
									<input type="number" name="Month_1" class="form-control" id="Month_1" min="0" max="30" step="0.25">
								</div>
								<div class="col-md-2 col-sm-4 col-xs-6" style="padding: 15px" >
									<div>Feb</div>
									<input type="number" name="Month_2" class="form-control" id="Month_2" min="0" max="30" step="0.25">
								</div>
									<div class="col-md-2 col-sm-4 col-xs-6" style="padding: 15px" >
									<div>Mar</div>
									<input type="number" name="Month_3" class="form-control" id="Month_3" min="0" max="30" step="0.25">
								</div>
								<div class="col-md-2 col-sm-4 col-xs-6" style="padding: 15px" >
									<div>Apr</div>
									<input type="number" name="Month_4" class="form-control" id="Month_4" min="0" max="30" step="0.25">
								</div>
								<div class="col-md-2 col-sm-4 col-xs-6" style="padding: 15px" >
									<div>May</div>
									<input type="number" name="Month_5" class="form-control" id="Month_5" min="0" max="30" step="0.25">
								</div>
								<div class="col-md-2 col-sm-4 col-xs-6" style="padding: 15px" >
									<div>Jun</div>
									<input type="number" name="Month_6" class="form-control" id="Month_6" min="0" max="30" step="0.25">
								</div>
							</div>
							<div class="form-group">
								<div class="col-md-2 col-sm-4 col-xs-6" style="padding: 15px" >
									<div>Jul</div>
									<input type="number" name="Month_7" class="form-control" id="Month_7" min="0" max="30" step="0.25">
								</div>
								<div class="col-md-2 col-sm-4 col-xs-6" style="padding: 15px" >
									<div>Aug</div>
									<input type="number" name="Month_8" class="form-control" id="Month_8" min="0" max="30" step="0.25">
								</div>
								<div class="col-md-2 col-sm-4 col-xs-6" style="padding: 15px" >
									<div>Sep</div>
									<input type="number" name="Month_9" class="form-control" id="Month_9" min="0" max="30" step="0.25">
								</div>
								<div class="col-md-2 col-sm-4 col-xs-6" style="padding: 15px" >
									<div>Oct</div>
									<input type="number" name="Month_10" class="form-control" id="Month_10" min="0" max="30" step="0.25">
								</div>
								<div class="col-md-2 col-sm-4 col-xs-6" style="padding: 15px" >
									<div>Nov</div>
									<input type="number" name="Month_11" class="form-control" id="Month_11" min="0" max="30" step="0.25">
								</div>
								<div class="col-md-2 col-sm-4 col-xs-6" style="padding: 15px" >
									<div>Dec</div>
									<input type="number" name="Month_12" class="form-control" id="Month_12" min="0" max="30" step="0.25">								
								</div>
							</div>
						</div>
						
						<div class="form-group" id="panelAccrualRestriction">
							<label class="col-md-4 control-label">Accrual Restriction For a Period</label>
							<div class="col-md-8 form-group togglebutton togglebutton-material-blue" style="margin-left: 10px;">
								<label>
									<input type="checkbox" class="col-sm-9 md-checkbox" id="accrualRestrictionForPeriod" name="accrualRestrictionForPeriod">
								</label>
							</div>
                        </div>
						
						<div class="form-group" id="panelAccrualUntil">
							<label class="col-md-4 control-label">Accrual Until<span class="short_explanation">*</span></label>
							<div class="col-md-4">
								<input type="number" name="accrualUntil" class="form-control onlyNum vRequired" placeholder="Accrual Until" id="accrualUntil" min="1" max="5000">
							</div>
							<label class="col-sm-4 control-label">Day(s) from the leave applicability</label>
						</div>

						<div class="form-group accumulateEligibleDaysRow">
							<label class="col-md-4 control-label">Accumulate Eligible Days
							<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
								data-placement="top" data-content="By enabling this flag, the leaves can be accumulated to the upcoming leave periods if the employees did not take leaves in the current leave period.">
							</i></label>
							<div class="col-md-8 form-group togglebutton togglebutton-material-blue" style="margin-left: 10px;">
								<label>
									<input type="checkbox" class="col-sm-9 md-checkbox" id="accumulateEligibleDays" name="AccumulateEligibleDays">
								</label>
							</div>
						</div>

						<div class="form-group" id="panelAccumulateFrom">
							<label class="col-md-4 control-label">Accumulate From<span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control" id="accumulateFrom" data-search="true" name="Accumulate_From" data-placeholder="Accumulate From">
									<option value="Date Of Join">Date Of Join</option>
									<option value="After Probation">After Probation</option>
									<option value="Custom Configuration">Custom Configuration</option>
								</select>
							</div>
                        </div>

						<div class="form-group" id="panelAccumulateAfter">
							<label class="col-md-4 control-label">Accumulate After<span class="short_explanation">*</span></label>
							<div class="col-md-6">
								<input type="number" name="Accumulate_After" class="form-control vRequired" placeholder="Accumulate After" id="accumulateAfter" min="1" max="5000">
							</div>
							<label class="col-sm-2 control-label">day(s)</label>
						</div>

						<?php
							if ($leaveSettings['Enable_Workflow'] == "Yes")
							{
								echo'
									<div class="form-group">
										<label class="col-md-4 control-label">Approval Workflow <span class="short_explanation">*</span></label>
										<div class="col-md-8">
											<select class="form-control vRequired"  id="approvalWorkflow" name="approvalWorkflow" data-placeholder="Approval Workflow"></select>
										</div>
									</div>
								';
							}
						?>
                        
						<div class="form-group">
							<label class="col-md-4 control-label">Gender </label>
							<div class="col-md-8">
								<select class="form-control" data-search="true" id="Gender" name="Gender">
									<option value="ALL">All</option>
									<option value="Male">Male</option>
									<option value="Female">Female</option>
								</select>
							</div>
                        </div> 
						
						<div class="form-group">
							<label class="col-md-4 control-label">Leave Approval Cutoff</label>
							<div class="col-md-6">
								<input type="number" name="Leave_Approval_Cutoff" class="form-control" placeholder="Leave Approval Cutoff" id="Leave_Approval_Cutoff" min="1" max="720">
							</div>
							<label class="col-sm-2 control-label">hour(s)</label>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Advance Notification</label>
							<div class="col-md-6">
								<input type="number" name="Leave_Advance_Notification" class="form-control" placeholder="Leave AdvanceNotification" id="Leave_Advance_Notification" min="1" max="30"  step="1">
							</div>
							<label class="col-sm-2 control-label">day(s)</label>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Leave Calculation Days</label>
							<div class="col-md-8">
								<select class="form-control vRequired" data-search="true" id="Leave_Calculation_Days" name="Leave_Calculation_Days">									
									<option value="0">Business Working Days</option>
									<option value="1">All Days Of Month</option>
								</select>								
							</div>
						</div>

						<div class="form-group">
							<label class="col-md-4 control-label">Show Balance
							<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
								data-placement="top" data-content="Enabling this flag will show the leave balance in the dashboard, employees leave history and while creating a leave record">
							</i></label>
							<div class="col-md-8 form-group togglebutton togglebutton-material-blue" style="margin-left: 10px;">
								<label>
									<input type="checkbox" class="col-sm-9 md-checkbox" id="Show_Statistics_In_Dashboard" name="Show_Statistics_In_Dashboard">
								</label>
							</div>
                        </div>
						<div class="form-group">
							<label class="col-md-4 control-label">Restrict Employee To Apply
							<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
								data-placement="top" data-content="Enabling this flag will restrict the employee to create a leave record with this leave type">
							</i></label>
							<div class="col-md-8 form-group togglebutton togglebutton-material-blue" style="margin-left: 10px;">
								<label>
									<input type="checkbox" class="col-sm-9 md-checkbox" id="Restrict_Employee_To_Apply" name="Restrict_Employee_To_Apply">
								</label>
							</div>
                        </div>

						<div class="form-group">
							<label class="col-md-4 control-label">Enable Leave Exception
								<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
									data-placement="top" data-content="Enabling leave exception will allow employees configure telugna medical/half paid leave policy">
								</i>
							</label>
							<div class="col-md-8 form-group togglebutton togglebutton-material-blue" style="margin-left: 10px;">
								<label>
									<input type="checkbox" class="col-sm-9 md-checkbox" id="formEnableLeaveException" name="enableLeaveException">
								</label>
							</div>
                        </div>

						<div class="form-group formPanelEnableLeaveException">
							<label class="col-md-4 control-label">Half Paid Leave Minimum Limit </label>
							<div class="col-md-8">
								<input type="number" name="Minimum_Limit" class="form-control" placeholder="Minimum Limit" id="formMinimumLimit" min="0.5" max="365">
							</div>
						</div>

						<div class="form-group formPanelEnableLeaveException">
							<label class="col-md-4 control-label">Half Paid Leave Deduction
								<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
									data-placement="top" data-content="Enabling half paid leave deduction will deduct half salary instead of full salary">
								</i>
							</label>
							<div class="col-md-8 form-group togglebutton togglebutton-material-blue" style="margin-left: 10px;">
								<label>
									<input type="checkbox" class="col-sm-9 md-checkbox" id="formHalfPaidLeaveDeduction" name="enableHalfPaidLeaveDeduction">
								</label>
							</div>
                        </div>

						<div class="form-group formPanelEnableLeaveException">
							<label class="col-md-4 control-label">Leave Unit</label>
							<div class="col-md-8">
								<select class="form-control" id="formLeaveUnit" data-search="true" name="Leave_Unit" data-placeholder="Leave Unit">
									<option value="">-- Select --</option>
									<option value="0.5">0.5</option>
									<option value="1">1</option>
								</select>
							</div>
                        </div>

						<div class="form-group">
							<label class="col-md-4 control-label">Parent Leave
								<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
									data-placement="top" data-content="Enabling parental leave will allow the employees to utilize their parental leave balance before accessing the child leave balance.">
								</i>
							</label>
							<div class="col-md-8 form-group togglebutton togglebutton-material-blue" style="margin-left: 10px;">
								<label>
									<input type="checkbox" class="col-sm-9 md-checkbox" id="enableParentLeave" name="enableParentLeave">
								</label>
							</div>
                        </div>

						<div class="form-group"  id="formParentLeaveNamePanel">
							<label class="col-md-4 control-label" id="">Parent Leave Name</label>
							<div class="col-md-8">
								<select class="form-control vRequired" data-search="true" id="formParentLeaveName" name="Leave_Name">									
									<?php
										foreach ($leaveTypes as $key => $row)
										{
											echo '<option value="'. $key .'">'. $row .'</option>';
										}
									?>
								</select>
							</div>
						</div>
						

						<div class="form-group">
							<label class="col-md-4 control-label">Enable Personal Choice 
								<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
									data-placement="top" data-content="Enabling personal choice will allow employees to apply for leave for the holidays that are tagged with personal choice.">
								</i>
							</label>
							<div class="col-md-8 form-group togglebutton togglebutton-material-blue" style="margin-left: 10px;">
								<label>
									<input type="checkbox" class="col-sm-9 md-checkbox" id="enablePersonalChoice" name="enablePersonalChoice">
								</label>
							</div>
                        </div>

						<div class="form-group">
							<label class="col-md-4 control-label">Leave Status</label>
							<div class="col-md-8">
								<select class="form-control vRequired" data-search="true" id="Leave_Type_Status" name="Leave_Type_Status">									
									<option value="Active">Active</option>
									<option value="Inactive">Inactive</option>
								</select>								
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Description</label>
							<div class="col-md-8">
								<textarea name="Description" id="Description" rows="5" class="form-control vDescription" placeholder="Write your Description..." ></textarea>
							</div>
                        </div>
						
					</div>
					
					<button type="reset" class="cancel" id="resetLeaveType" style="display: none;" >Reset</button>
				</form>
				
				<?php } ?>
				
			</div>
			<div class="modal-footer text-center">
				
				<?php if (($leaveTypeUser['Add'] == 1 || $leaveTypeUser['Update'] == 1) && ($leaveTypeUser['Is_Manager'] == 1 || !empty($leaveTypeUser['Admin']))) { ?>
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="editFormResetLeaveType" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="editFormSubmitLeaveType" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
				<?php } ?>
				
			</div>
		</div>
	</div>
</div>

<!-- Form Dirty Confirmation Modal -->
<?php if (($leaveTypeUser['Add'] == 1 || $leaveTypeUser['Update'] == 1)) { ?>
<div class="modal fade" id="modalDirtyLeaveType" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditConfLeaveType"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfLeaveType">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editDirtyCloseLeaveType">Yes</button>
			</div>
		</div>
	</div>
</div>

<!-- Delete Confirmation Modal -->
<?php } if ($leaveTypeUser['Delete'] == 1) { ?>
<div class="modal fade" id="modalDeleteLeaveType" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteConfLeaveType"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure to delete?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfLeavetype">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="modalDeleteConfirmLeaveType">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } }
if ($leaveFreezeUser['View'] && ((!empty($customFormNameD) && array_key_exists("Enable",$customFormNameD) && $customFormNameD['Enable'] == 1) || empty($customFormNameD))) {
?>
<div class="col-md-12 portlets add-panel-padding">
	<div class="panel panelLeaveFreeze">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong id="lblFormNameD"><?php echo ((!empty($customFormNameD) && !empty($customFormNameD['New_Form_Name'])) ? $customFormNameD['New_Form_Name'] : $this->formNameD)?></strong></h3>
		</div>
		<div class="panel-content">
			
				<div class="m-b-10">
					<?php if($leaveFreezeUser['Add'] == 1){?>
					<button type="button" class="btn btn-white btn-embossed toolbar-icons" title="Add Leave Freeze"
					data-toggle="modal"  data-target="#modalLeaveFreeze" id="addLeaveFreeze"><i class="mdi-content-add"></i><span class="hidden-xs hidden-sm"> Add</span></button>
					<?php } ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons btn-off disabled" title="View Leave Freeze"
							id="viewLeaveFreeze"><i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span></button>
					<?php if($leaveFreezeUser['Update'] == 1){?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons btn-off disabled" title="Edit Leave Freeze"
							id="editLeaveFreeze"><i class="mdi-editor-mode-edit"></i><span class="hidden-xs hidden-sm"> Edit</span></button>
					<?php }
					if ($leaveFreezeUser['Delete']== 1) { ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="Delete Leave Freeze"
					data-toggle="modal" data-target="#modalDeleteLeaveFreeze" id="deleteLeaveFreeze"><i class="mdi-action-delete"></i><span class="hidden-xs hidden-sm"> Delete</span>
					</button>
					<?php } ?>
					
					<a class="filter-toggle btn btn-white btn-embossed toolbar-icons" id="filterLeaveFreeze">
					<i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm"> Filter</span>
					</a>
				</div>
			
			
			<table class="table dataTable table-striped table-dynamic table-hover" id="tableLeaveFreeze">
				<thead>
					<tr>
						<th></th>
						<th id="leaveFreezeLeaveType">Leave Type</th>
						<th id="leaveFreezeFreezeFrom">Freeze From</th>
						<th id="leaveFreezeFreezeTo">Freeze To</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
		</div>
	</div>	
</div>


<!-- Your custom menu with dropdown-menu as default styling -->
<div id="contextMenuLeaveFreeze" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextLeaveFreeze"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($leaveFreezeUser['Update']) { ?>
		<li><a tabindex="-1" id="editContextLeaveFreeze"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<?php }
		if ($leaveFreezeUser['Delete']) { ?>
		<li><a tabindex="-1" id="deleteContextLeaveFreeze"><i class="mdi-action-delete"></i> Delete</a></li>
		<?php } ?>
	</ul>
</div>

<div class="builder" id="filterPanelLeaveFreeze">
	<div id="closeFilterLeaveFreeze"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel filterReset" id="cancelLeaveFreeze" style="width: 100%;">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" id="applyLeaveFreeze" style="width: 100%;">Apply</button>
			
			<div class="form-group">
				<label>Leave Type</label>
				<select class="form-control" data-search="true" id="filterLeaveTypeFreeze" >
					<option value="">All</option>
					<?php
					foreach ($leaveTypes as $key => $row)
					{
						echo '<option value="'.$key.'">'.$row.'</option>';
					}
					?>
				</select>
			</div>
			<div class="form-group">
				<label>Leave Freeze Date</label>
				<div class="input-daterange b-datepicker input-group" data-date-format="<?php echo $dformat; ?>" id="datepicker">
					<input type="text" class="input-md form-control" name="start" id="filterLeaveFreezeFrom" data-orientation="top" placeholder="Begin" />
					<span class="input-group-addon">to</span>
					<input type="text" class="input-md form-control" name="end" id="filterLeaveFreezeTo" data-orientation="top" placeholder="End" />
				</div>
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="modalLeaveFreeze" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;"><i class="mdi-hardware-keyboard-backspace" id="backLeaveFreeze"></i></button>
				<?php
				if($leaveFreezeUser['Update']==1)
				{
				?>
					<button type="button" class="close form-icons" aria-hidden="true" id="editLeaveFreezeIcon">
						<i class="mdi-editor-mode-edit"></i>
					</button>
				<?php
				}
				?>
				<h4 class="modal-title" id="viewLeaveFreezeTitle"></h4>
			</div>
			<div class="modal-body">
				<form role="form" id="viewLeaveFreezeForm" >
					<div class="row">
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Leave Type</label></div>
							<div class="col-md-7"><p id="viewLeaveFreezeType"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Freeze From</label></div>
							<div class="col-md-7"><p id="viewLeaveFreezeFrom"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Freeze To</label></div>
							<div class="col-md-7"><p id="viewLeaveFreezeTo"></p></div>
						</div>
					       <div id="viewAdditionalinformation">
						</div>
					</div>
				</form>
				<?php if ($leaveFreezeUser['Add'] == 1 || $leaveFreezeUser['Update'] == 1) { ?>
				
				
				<form role="form" class="form-horizontal form-validation" id="editLeaveFreezeForm" method="POST" action="">
					<input type="hidden" name="Leave_Freeze_Type_Id" id="formLeaveFreezeId" />
					<div class="form-group">
							<label class="col-md-4 control-label">Leave Type <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control vRequired" data-search="true" id="formLeaveFreezeType" name="Leave_Freeze_Type" data-placeholder="Leave Type">
									<option value="">-- Select --</option>
									<?php
									foreach ( $leaveTypes as $key => $row )
									{
										echo '<option value="'. $key .'">'. $row .'</option>';
									}
									?>
								</select>
							</div>
					</div>
					<div class="form-group">
							<label class="col-md-4 control-label">Leave Freeze Date<span class="short_explanation">*</span></label>
							<div class="row col-md-8">
								<div class="col-md-5">
									<input type="text" class="date-picker form-control vRequired datePickerRead" name="Leave_Freeze_From" id="formLeaveFreezeFrom" data-orientation="top" placeholder="Leave Freeze From" />
								</div>
								<span class="col-md-1">to</span>
								<div class="col-md-5">
									<input type="text" class="date-picker form-control vRequired datePickerRead" name="Leave_Freeze_To" id="formLeaveFreezeTo" data-orientation="top" placeholder="Leave Freeze To" />
								</div>
							</div>
					</div>
						
					<button type="submit" id="formSubmit" style="display: none;">Submit</button>
					<button type="reset" class="cancel" id="formResetLeaveFreeze" style="display: none;" >Reset</button>
				</form>
				
				
						
				<?php } ?>
			</div>
			<div class="modal-footer text-center" id="formLeaveFreezeButton">
				<?php if ($leaveFreezeUser['Add'] == 1 || $leaveFreezeUser['Update'] == 1) { ?>
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="resetLeaveFreeze" style="bottom: 5px;"><i class="mdi-action-restore"></i> Reset</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="submitLeaveFreeze" style="bottom: 5px;"><i class="mdi-content-send"></i> Submit</button>
				<?php } ?>
			</div>
			
		</div>
	</div>
</div>




<!-- Form Dirty Confirmation Modal -->
<div class="modal fade" id="dirtyLeaveFreeze" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditConfLeaveFreeze"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfLeaveFreeze">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="closeLeaveFreeze">Yes</button>
			</div>
		</div>
	</div>
</div>



<?php if ($leaveFreezeUser['Delete']==1) { ?>
<!-- Delete COnfirmation Modal -->
<div class="modal fade" id="modalDeleteLeaveFreeze" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteConfLeaveFreeze"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure to delete?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfLeaveFreeze">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConfirmLeaveFreeze">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php }}
if ($leaveEnforcementUser['View'] && ((!empty($customFormNameE) && array_key_exists("Enable",$customFormNameE) && $customFormNameE['Enable'] == 1) || empty($customFormNameE))) {
?>

<div class="col-md-12 portlets add-panel-padding">
	<div class="panel panelLeaveEnforcement">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong id="lblFormNameE"><?php echo ((!empty($customFormNameE) && !empty($customFormNameE['New_Form_Name'])) ? $customFormNameE['New_Form_Name'] : $this->formNameE)?></strong></h3>
		</div>
		<div class="panel-content">
			
				<div class="m-b-10">
					<?php if($leaveEnforcementUser['Add'] == 1){?>
					<button type="button" class="btn btn-white btn-embossed toolbar-icons" title="Add Leave Enforcement"
					data-toggle="modal"  data-target="#modalLeaveEnforcement" id="addLeaveEnforcement"><i class="mdi-content-add"></i><span class="hidden-xs hidden-sm"> Add</span></button>
					<?php } ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons btn-off disabled" title="View Leave Enforcement"
							id="viewLeaveEnforcement"><i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span></button>
					<?php if($leaveEnforcementUser['Update'] == 1){?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons btn-off disabled" title="Edit Leave Enforcement"
							id="editLeaveEnforcement"><i class="mdi-editor-mode-edit"></i><span class="hidden-xs hidden-sm"> Edit</span></button>
					<?php }?>
					
				</div>
			
			
			<table class="table dataTable table-striped table-dynamic table-hover" id="tableLeaveEnforcement">
				<thead>
					<tr>
						<th id="LeaveEnforcementConfLeaveType">Leave Type</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
		</div>
	</div>	
</div>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="contextMenuLeaveEnforcement" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextLeaveEnforcement"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($leaveEnforcementUser['Update']) { ?>
		<li><a tabindex="-1" id="editContextLeaveEnforcement"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<?php }?>
	</ul>
</div>


<div class="modal fade" id="modalLeaveEnforcement" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;"><i class="mdi-hardware-keyboard-backspace" id="backLeaveEnforcementConfiguration"></i></button>
				<?php
				if($leaveEnforcementUser['Update']==1)
				{
				?>
					<button type="button" class="close form-icons" aria-hidden="true" id="editLeaveEnforcementIcon">
						<i class="mdi-editor-mode-edit"></i>
					</button>
				<?php
				}
				?>
				<h4 class="modal-title" id="viewLeaveEnforcementTitle"></h4>
			</div>
			<div class="modal-body">
				<form role="form" id="viewLeaveEnforcementForm" >
					<div class="row">
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Leave Type</label></div>
							<div class="col-md-7"><p id="viewLeaveEnforcementType"></p></div>
						</div>
						<div id="gridViewEmployeeService">
							<table class="table dataTable table-striped table-dynamic table-hover" id="tableViewEmployeeService">
							<thead>
								<tr>
									<th id="leaveEmployeeServiceViewFrom">Service From</th>
									<th id="leaveEmployeeServiceViewTo">Service To</th>
									<th id="leaveEmployeeServiceViewMaximumLeaveDays">Maximum Leave Days</th>
								</tr>
							</thead>
							<tbody>
							</tbody>
						   </table>  
						</div>
							
						<div id="gridViewLeaveQuarter">
							<table class="table dataTable table-striped table-dynamic table-hover" id="tableViewLeaveQuarter">
							 <thead>
								 <tr>
								     <th></th>
									 <th id="leaveQuarterViewEmploymentYearFrom">Employment Year From</th>
									 <th id="leaveQuarterViewEmploymentYearTo">Employment Year To</th>
									 <th id="leaveQuarterViewPeriod">Period</th>
								 </tr>
							 </thead>
							 <tbody>
							 </tbody>
							</table>  
						</div>

							<!-- <div id="gridViewLeaveJoinQuarter">
							<table class="table dataTable table-striped table-dynamic table-hover" id="tableViewLeaveJoinQuarter">
							 <thead>
								 <tr>
								     <th></th>
									 <th>Join From</th>
									 <th>Join To</th>
									 <th>Total Days</th>
								 </tr>
							 </thead>
							 <tbody>
							 </tbody>
							</table>  
						</div> -->
					    
						<div id="gridViewMaternityLeave">
							<table class="table dataTable table-striped table-dynamic table-hover" id="tableViewMaternityLeave">
							 <thead>
								 <tr>
									 <th id="leaveMaternityViewMinimumChild">Minimum Child</th>
									 <th id="leaveMaternityViewMaximumChild">Maximum Child</th>
									 <th id="leaveMaternityViewMaximumLeaveDays">Maximum Leave Days</th>
								 </tr>
							 </thead>
							 <tbody>
							 </tbody>
							</table>  
						</div>
						<div id="gridViewEmployeeExperience">
							<table class="table dataTable table-striped table-dynamic table-hover" id="tableViewEmployeeExperience">
							<thead>
								<tr>
									<th id="leaveEmployeeExperienceServiceFrom">Service From(Days)</th>
									<th id="leaveEmployeeExperienceServiceTo">Service To(Days)</th>
									<th id="leaveEmpExpServiceMaximumLeaveDays">Maximum Leave Days</th>
								</tr>
							</thead>
							<tbody>
							</tbody>
						   </table>  
						</div>
						
					</div>
				</form>
				<?php if ($leaveEnforcementUser['Add'] == 1 || $leaveEnforcementUser['Update'] == 1) { ?>
			    <form role="form" class="form-horizontal form-validation" id="editLeaveEnforcementForm" method="POST" action="">
					 <div class="panel-group panel-accordion" id="accordion">
							<div class="panel panel-default">
								<div class="panel-heading">
								       <h4>
									 <a class="collapsed" data-toggle="collapse" data-parent="#accordion" href="#collapseLeaveEnforcement" id='collapseIconLeaveEnforcement'>
									 Leave Enforcement Configuration
									 </a>
								       </h4>
							       </div>
				<div id="collapseLeaveEnforcement" class="panel-collapse collapse">
					<div class="panel-body">
					    <input type="hidden" name="Leave_Enforcement_Id" id="formLeaveEnforcementId" />
						<input type="hidden" name="Service_Id" id="formServiceId" />
						<input type="hidden" name="Leave_Quarter_Id" id="formLeaveQuarterId" />
						<input type="hidden" name="Maternity_Slab_Id" id="formMaternitySlabId" />
						<input type="hidden" name="Experience_Id" id="formExperienceId" />
						<input type="hidden" name="Leave_Enforcement_Configuration" id="formLeaveEnforcementConfiguration" />
						<input type="hidden" name="totaldaysloop" id="formTotaldaysloop" value="4" />

						
									<div class="form-group">
										<label class="col-md-3 control-label">Leave Type <span class="short_explanation">*</span></label>
											<div class="col-md-9">
												<select class="form-control vRequired" data-search="true" id="formLeaveEnforcementType" name="Leave_Freeze_Type" data-placeholder="Leave Type">
													
												</select>
											</div>
									</div>			
								
									<div id="formEmployeeService">
										<div class="form-group">
										   <label class="col-md-3 control-label">Employee Service <span class="short_explanation">*</span></label>
											<div class="col-md-4">
												<input type="number" class="form-control vRequired"  id="formServiceFrom" name="formServiceFrom" placeholder="Service From" min="0" max="100">
											</div>
											<span class="col-md-1">to</span>
											<div class="col-md-4">
												<input type="number" class="form-control"  id="formServiceTo" name="formServiceTo" placeholder="Service To" min="1" max="100">
											</div>
									   </div>
									
										<div class="form-group">
												<label class="col-md-3 control-label">Maximum Leave Days<span class="short_explanation">*</span></label>
												<div class="col-md-9">
													<input type="number" class="form-control vRequired" name="Total_Days" id="formServiceDays" placeholder="Total No of Days" min="0" max="365">
												</div>
										</div>
									</div>	


										<div id="formEmployeeExperience">
										<div class="form-group">
										   <label class="col-md-3 control-label">Employee Service(Days)<span class="short_explanation">*</span></label>
											<div class="col-md-4">
												<input type="number" class="form-control vRequired"  id="formExperienceFrom" name="formExperienceFrom" placeholder="Days From" min="0" max="10000">
											</div>
											<span class="col-md-1">to</span>
											<div class="col-md-4">
												<input type="number" class="form-control"  id="formExperienceTo" name="formExperienceTo" placeholder="Days To" min="1" max="10000">
											</div>
									   </div>
													
										<div class="form-group">
												<label class="col-md-3 control-label">Maximum Leave Days<span class="short_explanation">*</span></label>
												<div class="col-md-9">
													<input type="number" class="form-control vRequired" name="Total_Days" step="0.5" id="formExperienceDays" placeholder="Total No of Days" min="0" max="365">
												</div>
										</div>	
									</div>	
						
									<div id="formLeaveQuarter">
									<div class="form-group">
												<label class="col-md-3 control-label">Period<span class="short_explanation"></span></label>
												<div class="col-md-9">
													<input type="text" class="form-control" name="formPeriod" id="formPeriod" readonly="readonly">
												</div>
										</div>
										
										<div class="form-group">
										   <label class="col-md-3 control-label">Employment Year<span class="short_explanation">*</span></label>
											<div class="col-md-4">
												<input type="number" class="form-control vRequired"  id="formZeroLeaveFrom" name="ZeroLeaveFrom" placeholder="Year From" min="0" max="100">
											</div>
											<span class="col-md-1">to</span>
											<div class="col-md-4">
												<input type="number" class="form-control"  id="formZeroLeaveTo" name="ZeroLeaveFrom" placeholder="Year To" min="1" max="100">
											</div>
									   </div>

<!--/** to print the employeejoinfrom & employeejointo month automatically in quarter wise leave form **/-->
									   <div class="form-group">
											<label class="col-md-4 control-label">Employee Join From<span class="short_explanation"></span></label>
											<label class="col-md-4 control-label">Employee Join To<span class="short_explanation"></span></label>
											<label class="col-md-4 control-label">Maximum Leave Days<span class="short_explanation"></span></label>
										</div>
										
										<?php 	for($i=0;$i<4;$i++){?>

										<div class="form-group">
												<label class="col-md-4 control-label"><?php echo $getEmployeeJoinFromTo[$i][1]; ?><span class="short_explanation"></span></label>
											
												<input type="hidden" name="formJoinFrom" id="formJoinFrom<?php echo $i; ?>" value="<?php echo $getEmployeeJoinFromTo[$i][3]; ?>" />
												<label class="col-md-4 control-label"><?php echo $getEmployeeJoinFromTo[$i][2]; ?><span class="short_explanation"></span></label> 
												<input type="hidden" name="formJoinTo" id="formJoinTo<?php echo $i; ?>" value="<?php echo $getEmployeeJoinFromTo[$i][4]; ?>" />

												<label class="col-md-4 control-label"> <input type="number" class="form-control" name="Total_Days" id="formQuarterDays<?php echo $i; ?>" placeholder="Total No of Days"></label>
										</div>

										
										<?php }?>

									</div>
									
									<div id="formMaternityLeave">
										<div class="form-group">
										   <label class="col-md-3 control-label">Child <span class="short_explanation">*</span></label>
											<div class="col-md-4">
												<input type="number" class="form-control vRequired"  id="formChildFrom" name="formChildFrom" placeholder="Minimum Child" min="0" max="100">
											</div>
											<span class="col-md-1">to</span>
											<div class="col-md-4">
												<input type="number" class="form-control"  id="formChildTo" name="formChildTo" placeholder="Maximum Child" min="1" max="100">
											</div>
									   </div>
										<div class="form-group">
												<label class="col-md-3 control-label">Maximum Leave Days<span class="short_explanation">*</span></label>
												<div class="col-md-9">
													<input type="number" class="form-control vRequired" name="Total_Days" id="formMaternityDays" placeholder="Total No of Days" min="0" max="365">
												</div>
										</div>
									</div>	
									
								    <div class="text-center" id="formActionLeaveEnforcement">
										    <?php if ($leaveEnforcementUser['Add'] == 1 || $leaveEnforcementUser['Update'] == 1) { ?>
											    <button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="resetLeaveEnforcement" style="bottom: 5px;"><i class="mdi-action-restore"></i> Reset</button>
											    <button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="submitLeaveEnforcement" style="bottom: 5px;"><i class="mdi-content-send"></i> Add</button>
										    <?php } ?>
								    </div>
					</div>
					
					</div>
				
					</div>
							
			</div>
							<!--<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="addLeaveEnforcement" title="Add">
								<i class="mdi-content-add"></i><span> Add</span>
							</button>-->
							<div id="gridEmployeeService">
							<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="addEmployeeService" title="Add">
								<i class="mdi-content-add"></i><span> Add</span>
							</button>
							
								<table class="table dataTable table-striped table-dynamic table-hover" id="tableEmployeeService">
										<thead>
											<tr>
												<th class="text-right"></th>
												<th id="leaveEmpServiceFrom">Service From</th>
												<th id="leaveEmpServiceTo">Service To</th>
												<th id="leaveEmpServiceMaximumLeaveDays">Maximum Leave Days</th>
											</tr>
										</thead>
										<tbody>
										</tbody>
								</table>
							</div>
							<div id="gridLeaveQuarter">
								<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="addLeaveQuarter" title="Add">
								<i class="mdi-content-add"></i><span> Add</span>
								</button>
								<table class="table dataTable table-striped table-dynamic table-hover" id="tableLeaveQuarter">
										<thead>
											<tr>
												<th></th>
												<th class="text-right"></th>
												<th id="leaveQuarterEmploymentYearFrom">Employment Year From</th>
												<th id="leaveQuarterEmploymentYearTo">Employment Year To</th>
												<th id="leaveQuarterPeriod">Period</th>
											</tr>
										</thead>
										<tbody>
										</tbody>
								</table>
							</div>
							<div id="gridMaternityLeave">
								<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="addMaternityLeave" title="Add">
								<i class="mdi-content-add"></i><span> Add</span>
								</button>
								<table class="table dataTable table-striped table-dynamic table-hover" id="tableMaternityLeave">
										<thead>
											<tr>
												<th class="text-right"></th>
												<th id="leaveMaternityMinimumChild">Minimum Child</th>
												<th id="leaveMaternityMaximumChild">Maximum Child</th>
												<th id="leaveMaternityMaximumLeaveDays">Maximum Leave Days</th>
											</tr>
										</thead>
										<tbody>
										</tbody>
								</table>
							</div>
							<div id="gridEmployeeExperience">
							<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="addEmployeeExperience" title="Add">
								<i class="mdi-content-add"></i><span> Add</span>
							</button>
							
								<table class="table dataTable table-striped table-dynamic table-hover" id="tableEmployeeExperience">
										<thead>
											<tr>
												<th class="text-right"></th>
												<th id="leaveEmpExperienceServiceFrom">Service From(Days)</th>
												<th id="leaveEmpExperienceServiceTo">Service To(Days)</th>
												<th id="leaveEmpExperienceMaximumLeaveDays">Maximum Leave Days</th>
											</tr>
										</thead>
										<tbody>
										</tbody>
								</table>
							</div>
			    </form>	
						
				<?php } ?>
		</div>
	</div>
</div>
</div>



<!-- Form Dirty Confirmation Modal -->
<div class="modal fade" id="dirtyLeaveEnforcement" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditConfLeaveEnforcement"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfLeaveEnforcement">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="closeLeaveEnforcement">Yes</button>
			</div>
		</div>
	</div>
</div>



<?php if ($leaveEnforcementUser['Delete']==1) { ?>
<!-- Delete COnfirmation Modal -->
<div class="modal fade" id="modalDeleteLeaveEnforcement" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteConfLeaveEnforcement"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure to delete?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfLeaveEnforcement">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConfirmLeaveEnforcement">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php }}

if ($leaveTypeUser['View'] != 1 && $leaveUser['View'] !=1 && $leaveFreezeUser['View']!=1 && $leaveEnforcementUser['View']!=1) { ?>

<div class="col-md-12 portlets add-panel-padding">
	<div class="txt_center">Sorry, Access Denied...</div>
</div>

<?php } ?>

