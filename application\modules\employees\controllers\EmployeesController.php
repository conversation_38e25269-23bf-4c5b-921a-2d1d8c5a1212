<?php
//=========================================================================================
//=========================================================================================
/* Program : EmployeesController.php											         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : The Employee Information Module maintains all relevant employee         *
 * related information, including different types of personal information, detailed      *
 * qualifications and work experience, job related information etc. Information captured *
 * in this module is utilized by all other modules, thus eliminating data redundancy. By *
 * default Employee have access rights based on his designation , user can update the    *
 * access rights, this specific access rights are added in Employee Access Right table.  *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Devirani,Shobana        Initial Version        	         *
 *  0.2        18-Apr-2014    Mahesh                  changed action                  	 *
 *                                         	          1.updateGradeAction ()              * 
 *                                                                                    	 *
 *  0.3		   18 Sep-2014    Mahesh				  Modified Function					 *
 *                                                    1.createUserName                   *
 *													  2.createUserNameRecursive			 *
 *  1.0        02-Feb-2015    Sandhosh                Changes in file for mobile app     *
 *                                                                                       *
 *  1.4        05-Mar-2016    Nivethitha              Changes in file for Bootstrap      *
 *                                                                                       *
 *  1.5        12-Aug-2016     Shobana                 Added PAN and TAN                 * 
 *							                                                    		 */
//=========================================================================================
//=========================================================================================

include APPLICATION_PATH.'/validations/Validations.php';

class Employees_EmployeesController extends Zend_Controller_Action
{
	protected $_validation         = null;
	protected $_dbCommonFunction     = null;
    protected $_dbPersonal = null;
    protected $_basePath = null;
    protected $_dbJobDetail = null;
    protected $_dbLocation = null;
    protected $_dbAccessRights = null;
    protected $_dbDept = null;
    protected $_dbEmpType = null;
    protected $_dbModules = null;
    protected $_dbDesignation = null;
    protected $_empAccessRights = null;
    protected $_empSelfAccessRights = null;
    protected $_logEmpId = null;
    protected $_designationAccessRights = null;
    protected $_gradeAccessRights = null;
	protected $_emptypeAccessRights = null;
    protected $_rolesAccessRights = null;
    protected $_dbGrade = null;
    protected $_ehrTables = null;
	protected $_dbEmployee = null;
    protected $_orgDateFormat = null;
    protected $_dbInsurance = null;
	protected $_employeeAccess = null;
	//protected $_employeeSelfAccess = null;
	protected $_designationEmpAccess = null;
	protected $_gradeEmployeeAccess = null;
	protected $_empTypeEmployeeAccess = null;
	protected $_empDirectoryAccessRights = null;
    protected $_formNameC = 'Grades';
    protected $_formNameA = 'Employees';
    protected $_formNameB = 'Designations';
	protected $_formNameD = 'Employee Type';
	protected $_formNameE = 'Employee Directory';
	protected $_formNameG = 'Redeem Rewards';
	protected $_hrappMobile = null;
	protected $_orgDetails  = null;
	protected $_dbDataImport = null;
	protected $_dbAttendance = null;
	
    public function init()
    {
		$this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
		if ($this->_hrappMobile->checkAuth())
        {
			$this->_validation 	      = new Validations();
			$this->_dbCommonFunction  = new Application_Model_DbTable_CommonFunction();
            $this->_basePath          = new Zend_View_Helper_BaseUrl();
            $this->_dbPersonal        = new Employees_Model_DbTable_Personal();
            $this->_dbJobDetail       = new Employees_Model_DbTable_JobDetail();
            $this->_dbDept            = new Organization_Model_DbTable_Department();
            $this->_dbLocation        = new Organization_Model_DbTable_Location();
            $this->_dbEmpType         = new Employees_Model_DbTable_EmployeeType();
            $this->_dbAccessRights    = new Default_Model_DbTable_AccessRights();
            $this->_dbDesignation     = new Employees_Model_DbTable_Designation();
            $this->_dbGrade           = new Employees_Model_DbTable_Grade();
            $this->_dbEmployee        = new Employees_Model_DbTable_Employee();
            $this->_dbModules         = new Application_Model_DbTable_Modules();
            $this->_ehrTables         = new Application_Model_DbTable_Ehr();
			$this->_dbInsurance       = new Payroll_Model_DbTable_Insurance();
			$this->_dbDataSetup       = new DatasetupDashboard_Model_DbTable_DataSetupDashboard();
			$this->_dbFixedHealthIns   = new Payroll_Model_DbTable_FixedHealthInsurance();
			$this->_orgDetails        = Zend_Registry::get('orgDetails');
			$userSession                       = $this->_dbCommonFunction->getUserDetails ();
			$this->_logEmpId                   = $userSession['logUserId'];		
			$this->_empAccessRights            = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameA);
            $this->_empSelfAccessRights        = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, 'Employee');
            $this->_designationAccessRights    = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameB);
            $this->_gradeAccessRights          = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameC);
			$this->_emptypeAccessRights        = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameD);
			$this->_empDirectoryAccessRights    = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameE);
			$this->_rewardsAccessRights      = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameG);
            $this->_rolesAccessRights          = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, 'Admin');
			$this->_roleSuperAdminAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, 'Super Admin');
            $this->_orgDateFormat              = $this->_ehrTables->orgDateformat();
			$this->_employeeAccess             = $this->_empAccessRights['Employee'];
			$this->_empDirectoryAccess         = $this->_empDirectoryAccessRights['Employee'];
			$this->_rewardsAccess	           = $this->_rewardsAccessRights['Employee'];
			$this->_designationEmpAccess       = $this->_designationAccessRights['Employee'];
			$this->_gradeEmployeeAccess        = $this->_gradeAccessRights['Employee'];
			$this->_empTypeEmployeeAccess      = $this->_emptypeAccessRights['Employee'];
            $this->_dbDataImport				= new Organization_Model_DbTable_DataImport();
			$this->_dbAttendance   				= new Employees_Model_DbTable_Attendance();
        }
        else
        {
            if (Zend_Session::namespaceIsset('lastRequest'))
                Zend_Session:: namespaceUnset('lastRequest');
            
            $session = new Zend_Session_Namespace('lastRequest');
            $session->lastRequestUri = 'employees/employees';
            $this->_redirect('auth');
		}
    }
	
    public function indexAction ()
    {
		$checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

		if ($checkSessionAuth)
		{
			$this->_helper->layout()->disableLayout()->setLayout('admin_layout');
		
			$dbManager = new Default_Model_DbTable_Manager();
			
			$this->view->formNameA = $this->_formNameA;
			$this->view->formNameB = $this->_formNameB;
			$this->view->formNameC = $this->_formNameC;
			$this->view->formNameD = $this->_formNameD;
			
			$this->view->customFormNameA = $this->_ehrTables->getCustomForms($this->_formNameA);
			$this->view->customFormNameB = $this->_ehrTables->getCustomForms($this->_formNameB);
			$this->view->customFormNameC = $this->_ehrTables->getCustomForms($this->_formNameC);
			$this->view->customFormNameD = $this->_ehrTables->getCustomForms($this->_formNameD);
			
			$this->view->defaultImage    = $this->_basePath->baseUrl('images/defaultPhot.jpg');
			$this->view->newImg    = $this->_basePath->baseUrl('images/new.png');
			$this->view->language        = $this->_dbPersonal->getLanguages();
			$this->view->countries       = $this->_dbEmployee->getCountries();
			$this->view->formStatus      = $this->_dbEmployee->getFormStatus($this->_logEmpId);
			$this->view->department      = $this->_dbDept->getDeptPairs();
			$this->view->designation     = $this->_dbDesignation->getDesignationPairs();
			$this->view->empType         = $this->_dbEmpType->getEmpTypePairs();
			$this->view->empLocation     = $this->_dbLocation->getLocationPair();
			$this->view->grades          = $this->_dbGrade->getGrade();
			$this->view->parentGrade     = json_decode( json_encode($this->_dbGrade->parentGrade()), true);
			$this->view->listManager     = json_decode( json_encode($dbManager->managerName('', '', 'Emp_First_Name', 'ASC', '', '', '', '', '', 'Employees', '')), true);
			$this->view->empWorkSchedule = $this->_dbPersonal->workSchedulePair();
			$this->view->empBankAccType  = $this->_dbEmployee->getBankAccountType();
			$this->view->maritalStatus   = $this->_dbPersonal->getMaritalStatus();
			$this->view->empESICReason   = $this->_dbEmployee->getESICReason();
			$this->view->coursesList     = $this->_dbEmployee->getCoursesList();
			$this->view->empProfession   = $this->_dbEmployee->getEmpProfession();
			
			$this->view->deptHierarchy   = $this->_dbDept->getDepartmentTypes();
			$this->view->deptParent      = $this->_dbDept->getParentDepartmentNull();
			
			$this->view->employeeName=$this->_dbCommonFunction->listEmployeesDetails('Employee Roles','','','');
			$this->view->roleDesignation = $this->_dbDesignation->getDesignationPairs('RolesForm');
	
			$this->view->forms = $this->_dbModules->mainForms();
			$this->view->restrictEmpAccessForManager = $this->_orgDetails['Restrict_Emp_Access_For_Manager'];
			$this->view->modules = $this->_dbModules->getModules('All');
			$this->view->subForms = $this->_dbModules->subForms();
			$this->view->getFormId=$this->_dbEmployee->getFormId();

			$this->view->salutationPair = $this->_dbCommonFunction->getSalutation();
			$this->view->serviceProvider = $this->_dbEmployee->getEmployeeServiceProviderList($this->_logEmpId);
			$this->view->workPlaceList 	 = $this->_dbAttendance->listWorkPlace();
			// employees access rights
			$this->view->empUser =  array('Is_Manager' => $this->_employeeAccess['Is_Manager'],
										  'View'       => $this->_employeeAccess['View'],
										  'Add'        => $this->_employeeAccess['Add'],
										  'Update'     => $this->_employeeAccess['Update'],
										  'Delete'     => $this->_employeeAccess['Delete'],
										  'Admin'      => $this->_empAccessRights['Admin'],
										  'Employee_Name' => $this->_dbPersonal->employeeId($this->_logEmpId));
			
			//designation access rights
			$this->view->designationAccess = array('Is_Manager' => $this->_designationEmpAccess['Is_Manager'],
												   'View'       => $this->_designationEmpAccess['View'],
												   'Add'        => $this->_designationEmpAccess['Add'],
												   'Update'     => $this->_designationEmpAccess['Update'],
												   'Delete'     => $this->_designationEmpAccess['Delete'],
												   'Admin'      => $this->_designationAccessRights['Admin']);
				
			//grade access rights
			$this->view->gradeAccess =  array('Is_Manager' => $this->_gradeEmployeeAccess['Is_Manager'],
											  'View'       => $this->_gradeEmployeeAccess['View'],
											  'Add'        => $this->_gradeEmployeeAccess['Add'],
											  'Update'     => $this->_gradeEmployeeAccess['Update'],
											  'Delete'     => $this->_gradeEmployeeAccess['Delete'],
											  'Admin'      => $this->_gradeAccessRights['Admin']);
			//Employee Type Access
			$this->view->empTypeAccess =  array('Is_Manager' => $this->_empTypeEmployeeAccess['Is_Manager'],
												'View'       => $this->_empTypeEmployeeAccess['View'],
												'Add'        => $this->_empTypeEmployeeAccess['Add'],
												'Update'     => $this->_empTypeEmployeeAccess['Update'],
												'Delete'     => $this->_empTypeEmployeeAccess['Delete'],
												'Admin'      => $this->_emptypeAccessRights['Admin']);
												
			// Employee Directory Access
			$this->view->empDirectoryAccess = array('View'=> $this->_empDirectoryAccess['View']);
			// Redeem Rewards Access
			$this->view->rewardsAccess = array('View' => $this->_rewardsAccess['View']);
			// reward flag is enabled in rewards settings from this function
			$this->view->isRewardEnabled = $this->_dbEmployee->getRedeemRewardsEnableFlag();
			
			$this->view->rolesUser = array('Update'     => $this->_rolesAccessRights['Employee']['Update'],
										   'Op_Choice'  => $this->_roleSuperAdminAccessRights['Employee']['Optional_Choice'],
										   'Admin'=>$this->_rolesAccessRights['Admin']);
			
			$this->view->customField = $this->_ehrTables->getCustomFields();
			
			/** To Unmask panel in datasetup dashboard. In this function, if the Data Setup
			 * prerequisites is enabled in the organization settings then data setup forms will be returned.
			 * otherwise empty array will be returned. */
			$this->view->datasetupForms = $this->_dbDataSetup->validateRetrieveDataSetupForms();
			
			$this->view->bloodGroupPair = $this->_dbCommonFunction->getBloodGroup();
		} else {
			$this->_redirect('auth');
		}
    }
	
	/**
     * Get employees details to show in a grid
     */
    public function listEmployeesAction ()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-employees', 'json')->initContext();
			
            if ($this->_employeeAccess['View'] == 1 /*|| $this->_employeeSelfAccess['View'] == 1*/)
            {
				$sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
                
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$employeeName = $this->_getParam('sSearch_0', null);
				$employeeName = filter_var($employeeName, FILTER_SANITIZE_STRIPPED);
				
				$managerName = $this->_getParam('sSearch_1', null);
				$managerName = filter_var($managerName, FILTER_SANITIZE_STRIPPED);
                
				$designation = $this->_getParam('sSearch_2',null);
                $designation = filter_var($designation, FILTER_SANITIZE_NUMBER_INT);
                
				$department = $this->_getParam('sSearch_3',null);
                $department = filter_var($department, FILTER_SANITIZE_NUMBER_INT);
                
                $location = $this->_getParam('sSearch_4',null);
                $location = filter_var($location, FILTER_SANITIZE_NUMBER_INT);
                
				$empType = $this->_getParam('sSearch_5',null);
                $empType = filter_var($empType, FILTER_SANITIZE_NUMBER_INT);
                
				$status = $this->_getParam('sSearch_6',null);
                $status = filter_var($status, FILTER_SANITIZE_STRIPPED);
				
                $category = $this->_getParam('sSearch_7',null);
                $category = filter_var($category, FILTER_SANITIZE_STRIPPED);

				$serviceProviderId = $this->_getParam('sSearch_8',null);
                $serviceProviderId = filter_var($serviceProviderId, FILTER_SANITIZE_NUMBER_INT);
                                
				$empUser = array('Admin'  => $this->_empAccessRights['Admin'],
								 'SuperAdmin'  => $this->_roleSuperAdminAccessRights['Employee']['Optional_Choice'],
								 'Update' => $this->_employeeAccess['Update'],
                                 'LogId'  => $this->_logEmpId,
								 'Delete' => $this->_employeeAccess['Delete'],
								 'Is_Manager' => $this->_employeeAccess['Is_Manager'],
                                 'View'   => $this->_employeeAccess['View']);
                
				$searchArr = array('employeeName' => $employeeName,
								   'managerName'  => $managerName,
								   'designation'  => $designation,
								   'department'   => $department,
                                   'location'     => $location,
								   'empType'      => $empType, 
								   'status'       => $status,
								   'category'     => $category,
								   'serviceProviderId'=>$serviceProviderId);
				
				$this->view->result = $this->_dbEmployee->listEmployeeDetails ($page, $rows, $sortField, $sortOrder, $searchAll, $searchArr,$empUser/*, $selfUser*/);
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
     * Get employee details to view
     */
    public function viewEmployeesAction ()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('view-employees', 'json')->initContext();
			
			if ($this->_employeeAccess['View'] == 1 /*|| $this->_employeeSelfAccess['View'] == 1*/)
			{
				$employeeId = $this->_getParam('employeeId', null);
				$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
				
                $tab = $this->_getParam('tab', null);
				$tab = filter_var($tab, FILTER_SANITIZE_STRIPPED);
                
                $formAction = $this->_getParam('formAction', null);
				$formAction = filter_var($formAction, FILTER_SANITIZE_STRIPPED);
				
				if ($employeeId > 0)
				{
                    $orgDateFormat = $this->_orgDateFormat['php'];
                    $employeeArr = array();
                    $employeeArr['Form_Status'] = $this->_dbEmployee->getFormStatus($employeeId);
                    
                    switch($tab)
                    {
                        case 'PersonalInfo':
                            /**
                             *	Employee Profile picture
                            */
                            $imagePath      = $this->_ehrTables->uploadPath.'images/';
                            $profilePath    = $this->_dbEmployee->getEmpPhotoPath($employeeId);
                            $profilePicture = $this->_basePath->baseUrl('images/defaultPhot.jpg');
							$defaultImage   = 1;
                            //$profilePicture = $imagePath.'defaultPhot.jpg';
                            
							if (!empty($profilePath))
                            {
                                $profilePicture = $imagePath.$profilePath;//.$employeeId.'.jpg';
                                $defaultImage   = 0;
                            }
                            
                            $personalInfo  = $this->_dbEmployee->getEmpDetails($employeeId);
                            
                            if (!empty($personalInfo['Smokerasof']) && $personalInfo['Smokerasof'] !='0000-00-00')
                                $personalInfo['SmokerAsOf'] = date ($orgDateFormat, strtotime($personalInfo['Smokerasof']));
                            else
                                $personalInfo['SmokerAsOf'] = '';
                            
                            /**
                             *	Employee Date of Birth
                            */
                            if (!empty($personalInfo['DOB']) && $personalInfo['DOB'] !='0000-00-00')
                                $personalInfo['DateOfBirth'] = date($orgDateFormat, strtotime($personalInfo['DOB']));
                            else
                                $personalInfo['DateOfBirth'] = '';
                            
                            /**
                             *	Employee Hobbies
                            */
                            $employeeHobbies  = $this->_dbPersonal->getHobby($employeeId);
                            $strEmpHobby      = nl2br(implode (',', $employeeHobbies));
                            $personalInfo['Hobbies'] = $strEmpHobby ;
                            
                            /**
                             *	Employee Laguages
                            */
                            $employeeLang   = $this->_dbPersonal->getEmpLangDet($employeeId);
                            $employeeLangId = $this->_dbPersonal->getEmpLanguage($employeeId);
                            
                            $personalInfo['languages']   = nl2br (implode (',', $employeeLang));
                            //$personalInfo['languageIds'] = nl2br (implode (',', $employeeLangId));
                            $personalInfo['languageIds'] = $employeeLangId;
                            
                            /**
                             *	Employee Driving License Details
                            */
                            $employeeLicenseArr = $this->_dbPersonal->getEmpDrivingLicense($employeeId);
                            
                            if (!empty($employeeLicenseArr))
                            {
                                $personalInfo['isDrivingLicense']   = true;
                                
                                if (!empty($employeeLicenseArr['License_Issue_Date']) && $employeeLicenseArr['License_Issue_Date'] != '0000-00-00')
                                    $employeeLicenseArr['LicenseIssueDate'] = date($orgDateFormat, strtotime($employeeLicenseArr['License_Issue_Date']));
                                else
                                    $employeeLicenseArr['LicenseIssueDate'] = '';
                                
                                if (!empty($employeeLicenseArr['License_Expiry_Date']) && $employeeLicenseArr['License_Expiry_Date'] != '0000-00-00')
                                    $employeeLicenseArr['LicenseExpiryDate'] = date($orgDateFormat, strtotime($employeeLicenseArr['License_Expiry_Date']));
                                else
                                    $employeeLicenseArr['LicenseExpiryDate'] = '';
                                
                                $personalInfo['employeeLicense'] = $employeeLicenseArr;
                            }
                            else
                            {
                                $personalInfo['isDrivingLicense'] = false;
                            }
                            
                            /**
                             *	Employee Passport Details
                            */
                            $empPassport = $this->_dbPersonal->getEmpPassport($employeeId);
                            
                            if (!empty($empPassport))
                            {
                                $personalInfo['isPassport'] = true;
                                
                                if (!empty($empPassport['Issue_Date']) && $empPassport['Issue_Date'] != '0000-00-00')
                                    $empPassport['IssueDate'] = date($orgDateFormat, strtotime($empPassport['Issue_Date']));
                                else
                                    $empPassport['IssueDate'] = '';
                                
                                if (!empty($empPassport['Expiry_Date']) && $empPassport['Expiry_Date'] != '0000-00-00')
                                    $empPassport['ExpiryDate'] = date($orgDateFormat, strtotime($empPassport['Expiry_Date']));
                                else
                                    $empPassport['ExpiryDate'] = '';
                                
                                $personalInfo['employeePassport'] = $empPassport;
                            }
                            else
                            {
                                $personalInfo['isPassport'] = false;
                            }
                            
                            /**
                             *	Login details
                            */
                            $empLoginArr = $this->_dbPersonal->getEmpLogin($employeeId);
                            
                            if (!empty($empLoginArr))
                            {
                                $personalInfo['isLogin'] = true;
                                
                                $personalInfo['employeeLogin'] = $empLoginArr;
                            }
                            else
                            {
                                $personalInfo['isLogin'] = false;
                            }
                            
                            $roles = $this->_dbAccessRights->employeeAccessRights($employeeId, $this->_formNameA);
                            
                            $personalInfo['roleAccess'] = (!empty($roles['Admin']) || ($this->_logEmpId == $employeeId));
                            
                            
                            $personalInfo['dependentArr'] = $this->_dbPersonal->getEmpDependent ($employeeId);
                            
                            $employeeArr['employeePicture'] = $profilePicture;
							$employeeArr['defaultImage']	= $defaultImage;
							$employeeArr['personalInfo']    = $personalInfo;
							$employeeArr['empJobEmail'] = $this->_dbPersonal->getEmpmail($employeeId);
							$employeeArr['empMobileNo'] = $this->_dbPersonal->getMobileNo($employeeId);
                            break;
                        
                        case 'JobInfo':
                            /**
                             *	Employee Job Details
                            */
                            $employeeJob = $this->_dbPersonal->getEmpJobInformation($employeeId);
                            
							
                            if (!empty($employeeJob['Date_Of_Join']) && $employeeJob['Date_Of_Join'] != '0000-00-00')
							{
								if($formAction != 'Edit')
								{
									$employeeJob['Date_Of_Join'] = date($orgDateFormat, strtotime($employeeJob['Date_Of_Join']));
								}
								else{
									//$employeeJob['OrgFmt_Date_Of_Join'] = date($orgDateFormat, strtotime($employeeJob['Date_Of_Join']));
									$employeeJob['OrgFmt_Date_Of_Join'] = date($orgDateFormat, strtotime($employeeJob['Date_Of_Join']));
									//$employeeJob['OrgFmt_Date_Of_Join'] = str_replace('/','-',$employeeJob['OrgFmt_Date_Of_Join']);
								}
							}
                            else
							{
                                $employeeJob['Date_Of_Join'] = $employeeJob['OrgFmt_Date_Of_Join'] = '';
							}
                            
                            if (!empty($employeeJob['Manager_First_Name']))
                                $employeeJob['Manager_Name'] = $employeeJob['Manager_First_Name']." ".$employeeJob['Manager_Last_Name'];
                            else
                                $employeeJob['Manager_Name'] = '';
                            
                            if (!empty($employeeJob['Emp_Status']))
                            {
                                if ($employeeJob['Emp_Status'] == "InActive")
                                {
                                    $employeeJob['InActive_Date'] = date ($orgDateFormat, strtotime($employeeJob['Emp_InActive_Date']));
                                }
                            }
                            
                            if (!empty($employeeJob['Confirmed']))
                            {
                                if ($employeeJob['Confirmed'])
                                {						
                                    if (!empty($employeeJob['Confirmation_Date']) && $employeeJob['Confirmation_Date'] != '0000-00-00')
									{
										if($formAction != 'Edit')
										{
											$employeeJob['Confirmation_Date'] = date($orgDateFormat, strtotime($employeeJob['Confirmation_Date']));
										}
									}
                                    else
									{
                                        $employeeJob['Confirmation_Date'] = '';
									}
                                }
                                else
                                {
                                    $employeeJob['Confirmation_Date'] = '';
                                }
                            }
                            else
                            {
                                $employeeJob['Confirmation_Date'] = '';
                            }
							
							if (!empty($employeeJob['Probation_Date']) && $employeeJob['Probation_Date'] != '0000-00-00')
							{
								if($formAction != 'Edit')
								{
									$employeeJob['Probation_Date'] = date($orgDateFormat, strtotime($employeeJob['Probation_Date']));
								}
							}
                            else
							{
                                $employeeJob['Probation_Date'] = '';
							}							
							
                            $employeeJob['experienceArr'] = $this->_dbPersonal->getEmpExperience ($employeeId);
                            $employeeJob['assestArr']     = $this->_dbEmployee->getMuliptleAsset ($employeeId);
                            $employeeJob['orgCode']       = strtoupper(substr($this->_ehrTables->getOrgCode (),0,2));
						    $employeeArr['jobInfo']       = $employeeJob;
                            break;
                        
                        case 'ContactInfo':
                            /**
                             *	Employee Contact Details
                            */
                            $empContact = $this->_dbPersonal->getEmpContacts($employeeId);
                            $employeeArr['contactInfo'] = $empContact;
							
                            break;
                        
                        case 'CareerInfo':
                            /**
                             *	Employee Career Details
                            */
                            $empCareer = array();
                            
                            $empCareer['educationArr']   = $this->_dbPersonal->getEmpEducation ($employeeId);
                            $empCareer['certificateArr'] = $this->_dbPersonal->getEmpCertificate ($employeeId);
                            $empCareer['trainingArr']    = $this->_dbPersonal->getEmpTraining ($employeeId);
                            $empCareer['awardArr']       = $this->_dbPersonal->getEmpAward ($employeeId);
                            $empCareer['skillsetArr']    = $this->_dbPersonal->getEmpSkillset($employeeId);
                            $employeeArr['careerInfo']   = $empCareer;
							
                            break;
                        
                        case 'BankInfo':
                            /**
                             *	Employee bank details
                            */
                            $employeeBank['bankDetails'] = $this->_dbPersonal->getEmpBankDetails($employeeId);
                            $employeeBank['insuranceArr'] = $this->_dbInsurance->getInsPoliNo($employeeId);
                            $employeeArr['bankInfo']      = $employeeBank;
                            $employeeArr['Pf_PolicyNo']   = $this->_dbJobDetail->getPfPolicyNo($employeeId);
							$employeeArr['additional_details']  =  $this->_dbEmployee->getEmpAdditionalPersonalDetais($employeeId);
							
                            break;
                    }
					
					$this->view->result = array('success' => true, 'Employee_Details' => $employeeArr);
				}
				//else
				//{
				//	$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
				//}
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Sorry, Access denied', 'type' => 'danger');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
	 *	get location address
	*/
	public function getLocationAction ()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('get-location', 'json')->initContext();
            
			$locationId = $this->_getParam('locationId', null);
            $locationId = filter_var($locationId, FILTER_SANITIZE_NUMBER_INT);
			
            if (!empty($locationId))
            {
                $this->view->result = $this->_dbLocation->getLocationDetails ($locationId);
            }
			else
			{
				$this->view->result = array('success' => false);
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
     * checking selected empolyeetype is benefit applicable or not based on this we'l
     * hide insurance & pf details
     */
    public function benefitTypeAction ()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('benefit-type', 'json')->initContext();
			
            $empType = $this->_getParam('_empType', null);
            $empType = filter_var($empType, FILTER_SANITIZE_NUMBER_INT);
			
			$employeeId = $this->_getParam('employeeId', null);
            $employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
			
            $viewEmployeeType = $this->_dbEmpType->viewEmployeeType ($empType,'No',$employeeId);
            
			$this->view->result = $viewEmployeeType;
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
     * find form status of an employeeId ...
     */
    public function biometricIntegrationIdExistAction()
    {
        $this->_helper->layout()->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('biometric-integration-id-exist', 'html')->initContext();
			
			if ($this->getRequest()->isPost())
			{
				$formData							= $this->getRequest()->getPost();
				$biometricIntegrationId   			= $this->_validation->onlyLetterNumberValidation($formData['biometricIntegrationId']);
				$biometricIntegrationId['valid'] 	= $this->_validation->lengthValidation($biometricIntegrationId,1,15,true);
				$employeeId   						= $this->_validation->intValidation($formData['employeeId']);
				if (!empty($biometricIntegrationId['value']) && $biometricIntegrationId['valid'])
				{
					$biometricIntegrationIdExist = $this->_dbCommonFunction->checkBiometricIntegrationIdExist($biometricIntegrationId['value'],$employeeId['value']);
					if(!empty($biometricIntegrationIdExist))
					{
						$this->view->result = array('success' => false, 'msg' => 'Biometric Integration Id already exist', 'type' => 'info');
					}
					else
					{
						$this->view->result = array('success' => true);
					}
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid Data', 'type' => 'warning');
				}
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Invalid Data', 'type' => 'warning');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
    * Check whether the entered date is less than DOB or not
    */
    public function isLessDOB ($dob, $checkDate)
    {
        $dobYear   = date('Y',strtotime('+14 years', strtotime($this->_ehrTables->changeDateformat($dob))));
        $checkYear = date('Y',strtotime($this->_ehrTables->changeDateformat($checkDate)));
		
        return ($checkYear < $dobYear);
    }

    /**
     * Check whether entered DOB valid or not
     */
    public function validDOB ($dob)
    {
        $dobYear     = date('Y', strtotime($this->_ehrTables->changeDateformat($dob)));
        $currentYear = date('Y') - 14;
		
		return ((int)$dobYear > (int)$currentYear);
    }
	
	/**
	 *	Check values if empty return null
	*/
	public function fnCheckValue ($value)
	{
		return !empty($value) ? $value : new Zend_Db_Expr('NULL');
	}
	
	/**
     * create username dynamically using algorithm
     */
    /*public function createUserName ($firstName, $lastName, $empId = NULL)
    {
        if (!empty($firstName) && !empty($lastName))
        {
            if (strlen($firstName) >= 7) //first name >= 7
            {
                // get first letter of the lastname and first 7 letter of first name
                $username = substr($lastName, 0, 1).substr($firstName, 0, 7);
            }
            else
            {
                $fnameLen = strlen($firstName);
                $remain = 8 - $fnameLen - 1; // username total val (8) - firtname length - 1
                // get first letter of username and firstname and last name from first position  to $remain position
                $username = substr($lastName, 0, 1).$firstName.substr($lastName, 0, $remain);
            }
			
            if (!is_null($empId))
            {
            	$loginDetails = $this->_dbPersonal->getEmpUsername($username, $empId); // check if username exist
            }
            else
            {
            	$loginDetails = $this->_dbPersonal->getEmpUsername($username); // check if username exist
            }
			
			if ($loginDetails > 0) //if username exists
            {
				$newUserName = substr($username, 0, 7); //get first 7 letter of username
                $newUserName = $newUserName.'1'; // add 1 as 8th letter
                $digitCount = 0;
                
				//checking weather the username already exist
                if (!is_null($empId))
                {
                	$loginDetails = $this->_dbPersonal->getEmpUsername ($username, $empId); 
                }
                else
                {
                	$loginDetails = $this->_dbPersonal->getEmpUsername ($username);
                }
                
                //$loginDetails = $this->_dbPersonal->getEmpUsername($newUserName);// check if username exist
                if ($loginDetails > 0)//if username exists
                {
                    return $this->createUserNameRecursive ($newUserName, $digitCount, $empId); // create new username
                }
                else
                {
                    return $newUserName;
                }
            }
            else
            {
                return $username;
            }
        }
    }*/
	
	/**
     * This ajax action is for create user name dynamically
     * and also checks username exists or not ...
     */
    /*public function checkUserNameExistsAction ()
    {
        $this->_helper->layout->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('check-user-name-exists', 'json')->initContext();
			
            $firstName = $this->_getParam('firstName', null);
            $firstName = filter_var($firstName, FILTER_SANITIZE_STRIPPED);
            
            $lastName = $this->_getParam('lastName', null);
            $lastName = filter_var($lastName, FILTER_SANITIZE_STRIPPED);
            
            $userName = $this->_getParam('userName', null);
            $userName = filter_var($userName, FILTER_SANITIZE_STRIPPED);
            
            $employeeId = $this->_getParam('employeeId', null);
            $employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
            
            if ((is_null($userName) || empty($userName)) && (is_null($employeeId) || empty($employeeId)) )
            {	
               $this->view->result = $this->createUserName ($firstName, $lastName);
            }
            elseif (!empty($employeeId) &&  (empty($userName) || is_null($userName)) )
            {
               $this->view->result = $this->createUserName ($firstName, $lastName, $employeeId);
            }
            else
            {
				$this->view->result = $this->_dbPersonal->getEmpUsername ($userName, $employeeId);
            }
		}
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }*/
	
	/**
     * creating and binding avalible grade insurance type  based on designation
     */
    public function dynamicInsuranceAction ()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('dynamic-insurance', 'json')->initContext();
			
            $designationId = $this->_getParam('designationId', null);
            $designationId = filter_var($designationId, FILTER_SANITIZE_NUMBER_INT);
			
			$employeeId = $this->_getParam('employeeId', null);
			$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
			
            $grade = $this->_dbEmployee->getGradeByDesgination($designationId);
            $insType = $this->_dbInsurance->getInsTypesByDesignation ($designationId);
			$fixedHealthInsurance = $this->_dbFixedHealthIns->getAvailableFixedHealthInsurance($employeeId);
			
			//$this->view->result = $this->_dbInsurance->getInsTypesByDesignation ($designationId);
            $this->view->result = array('insurance'=>$insType, 'Grade'=>$grade, 'fixedHealthInsurance'=>$fixedHealthInsurance);
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
     * Update employees
     */
    public function updateEmployeesAction ()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('update-employees', 'json')->initContext();
			
			$employeeId = $this->_getParam('employeeId', null);
			$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
			$employeeId = $this->_validation->intValidation($employeeId);
			
			if (($this->_employeeAccess['Update'] == 1 /*|| $this->_employeeSelfAccess['Update'] == 1*/) &&
				!empty($employeeId['value']) && $employeeId['value'] > 0 && $employeeId['valid'])
			{
				if ( $this->getRequest()->isPost() )
				{
					$formData = $this->getRequest()->getPost();                    
					
					/** Get the customizised field details**/
					$customField = $this->_ehrTables->getCustomFields();
					
					$pincodeCustom = $casteCustom = $ethnicRaceCustom = $aadhaarNoCustom = $panCustom = array();
					$pincodeCustom['Enable'] = $casteCustom['Enable'] = $ethnicRaceCustom['Enable'] = $aadhaarNoCustom['Enable'] = $panCustom['Enable'] = 1;
					$pincodeCustom['Required'] = 1;
					$casteCustom['Required'] = $ethnicRaceCustom['Required'] = $aadhaarNoCustom['Required'] =  $panCustom['Required']= 0;
					
					
					foreach($customField as $custom)
					{
						if($custom['Field_Name'] == 'Pincode' )
						{
							$pincodeCustom['Enable'] = $custom['Enable'];
							$pincodeCustom['Required'] =  $custom['Required'];							
						}
						
						if($custom['Field_Name'] == 'Caste' )
						{
							$casteCustom['Enable'] = $custom['Enable'];
							$casteCustom['Required'] =  $custom['Required'];							
						}
						
						if($custom['Field_Name'] == 'Ethnic Race' )
						{
							$ethnicRaceCustom['Enable'] = $custom['Enable'];
							$ethnicRaceCustom['Required'] =  $custom['Required'];							
						}
						
						if($custom['Field_Name'] == 'Aadhaar Number' )
						{
							$aadhaarNoCustom['Enable'] = $custom['Enable'];
							$aadhaarNoCustom['Required'] =  $custom['Required'];							
						}
						
						if($custom['Field_Name'] == 'PAN No.' )
						{
							$panCustom['Enable'] = $custom['Enable'];
							$panCustom['Required'] = $custom['Required'];							
						}
					}
					
					$btnClick = $formData['btnClick'];
					
					$userIpAddress = $formData['ipAddress'];

					$personalDataArr = $formData['personalData'];

					/**
					 *	Server side validation for Personal Information details
					*/
					$salutation = $this->_validation->alphaValidation($personalDataArr['Salutation']);
					
					$employeeFirstName    = $this->_validation->employeeNameValidation($personalDataArr['EmployeeFirstName']);
					$employeeMiddleName   = $this->_validation->employeeNameValidation($personalDataArr['EmployeeMiddleName']);
					$employeeLastName     = $this->_validation->employeeNameValidation($personalDataArr['EmployeeLastName']);
					$employeeNickName     = $this->_validation->onlyLetterSpaceValidation($personalDataArr['EmployeeNickName']);
					$hobbies              = $this->_validation->alphaSpNlCommaValidation($personalDataArr['Hobbies']);
					//$ethnicRace           = $this->_validation->onlyLetterSpaceValidation($personalDataArr['EthnicRace']);
					$placeOfBirth         = $this->_validation->onlyLetterSpaceValidation($personalDataArr['PlaceOfBirth']);
					$gender               = $this->_validation->alphaValidation($personalDataArr['Gender']);
					$dateOfBirth          = $this->_validation->dateValidation($personalDataArr['DateOfBirth']);
					$maritalStatus        = $this->_validation->intValidation($personalDataArr['MaritalStatus']);
					$bloodGroup           = $this->_validation->commonFilters($personalDataArr['BloodGroup']);
					$languagesKnown       = isset($personalDataArr['LanguagesKnown'])?$personalDataArr['LanguagesKnown']:array();
					$militaryService      = $this->_validation->checkboxValidation($personalDataArr['MilitaryService']);
					$isManager            = $this->_validation->checkboxValidation($personalDataArr['IsManager']);
					$physicallyChallenged = $this->_validation->checkboxValidation($personalDataArr['PhysicallyChallenged']);
					$personalEmail        = $this->_validation->emailValidation($personalDataArr['PersonalEmail']);
					$smoker               = $this->_validation->checkboxValidation($personalDataArr['Smoker']);
					$smokerAsOf           = $this->_validation->dateValidation($personalDataArr['SmokerAsOf']);
					$nationality          = $this->_validation->onlyLetterSpaceValidation($personalDataArr['Nationality']);
					$religion             = $this->_validation->onlyLetterSpaceValidation($personalDataArr['Religion']);
					//$caste                = $this->_validation->onlyLetterSpaceValidation($personalDataArr['Caste']);
					$isLicense            = $this->_validation->checkboxValidation($personalDataArr['isLicense']);
					$isPassport           = $this->_validation->checkboxValidation($personalDataArr['isPassport']);
					$autoPwdGenerate      = $personalDataArr['pwdGenerate'];
					
					$employeeFirstName['valid']  = $this->_validation->lengthValidation($employeeFirstName, 1, 50, true);
					$employeeMiddleName['valid'] = $this->_validation->lengthValidation($employeeMiddleName, 1, 50, false);
					$employeeLastName['valid']   = $this->_validation->lengthValidation($employeeLastName, 1, 50, true);
					$employeeNickName['valid']   = $this->_validation->lengthValidation($employeeNickName, 1, 50, false);
					$hobbies['valid']            = $this->_validation->lengthValidation($hobbies, 1, 100, false);
					//$ethnicRace['valid']         = $this->_validation->lengthValidation($ethnicRace, 3, 50, false);
					$placeOfBirth['valid']       = $this->_validation->lengthValidation($placeOfBirth, 1, 50, false);
					$nationality['valid']        = $this->_validation->lengthValidation($nationality, 1, 50, true);
					$religion['valid']           = $this->_validation->lengthValidation($religion, 1, 50, false);
					//$caste['valid']              = $this->_validation->lengthValidation($caste, 2, 50, false);
					
					/* Get the values of Allow user signin, send invitation and work email */
					$allowUserSignin    = $this->_validation->checkboxValidation($personalDataArr['allowUserSignin']);
					$sendInvitation    	= $this->_validation->checkboxValidation($personalDataArr['sendInvitation']);
					$enableSignInWithMobile = $personalDataArr['enableSignInWithMobile'];
					$signInMobileNumber     = $personalDataArr['signInMobileNumber'];
					$signInMobileNumberCountryCode = $personalDataArr['mobileNoCountryCode'];
					$workEmail 			= $this->_validation->emailValidation($personalDataArr['workEmail']);
					
					if($salutation['valid'] && $gender['valid'] && $maritalStatus['valid']){
						/**Validate the salutation based on the employee gender and the marital status */
						$salutation['value'] = $this->_dbCommonFunction->getEmpSalutationBasedGenderMaritalStatus($salutation['value'],$gender['value'],$maritalStatus['value']);
					}

					if($ethnicRaceCustom['Enable']){
                        $ethnicRace           = $this->_validation->onlyLetterSpaceValidation($personalDataArr['EthnicRace']);
						$ethnicRace['valid']  = $this->_validation->lengthValidation($ethnicRace, 1, 50, false);
					}
                    else
                    {
                        $ethnicRace['value'] = '';
                        $ethnicRace['valid'] = 1;
                    }
                    
                    if($casteCustom['Enable']){
                        $caste                = $this->_validation->onlyLetterSpaceValidation($personalDataArr['Caste']);
						$caste['valid']              = $this->_validation->lengthValidation($caste, 1, 50, false);
                    }
                    else
                    {
                        $caste['value'] = '';
                        $caste['valid'] = 1;
                    }
					
					$result = array('success' => true);
					
					/**
					 *	conditional validation for Personal Information details
					*/
					if (!empty($salutation['value']) && $salutation['valid'] && !empty($employeeFirstName['value']) && $employeeFirstName['valid'] &&
						$employeeMiddleName['valid'] && !empty($employeeLastName['value']) && $employeeLastName['valid'] && $employeeNickName['valid'] &&
						$hobbies['valid'] && ((empty($ethnicRace['value']) && !($ethnicRaceCustom['Required']))  || ($ethnicRace['valid'])) 
						&& !empty($gender['value']) && $gender['valid'] && !empty($dateOfBirth['value']) &&
						$dateOfBirth['valid'] && $placeOfBirth['valid'] && !empty($maritalStatus['value']) && $maritalStatus['valid'] &&
						!empty($bloodGroup) && $militaryService['valid'] && $isManager['valid'] &&
						$physicallyChallenged['valid'] && $smoker['valid'] &&
						(empty($personalEmail['value']) || (!empty($personalEmail['value']) && $personalEmail['valid'])) &&
						($smoker['value'] == 0 || ($smoker['value'] == 1 && !empty($smokerAsOf['value']) && $smokerAsOf['valid'])) &&
						!empty($nationality['value']) && $nationality['valid'] && $religion['valid'] &&
						((empty($caste['value']) && !($casteCustom['Required']))  || ($caste['valid'])) /*&&
						!empty($userName['value']) && $userName['valid']*/ ) 
					{
						/**
						 *	Check date of birth is valid
						*/
						$isValidDOB = $this->validDOB ($dateOfBirth['value']);
						
						if ($isValidDOB)
						{
							$result = array('success' => false, 'msg' => 'Invalid date of birth', 'type' => 'info');
						}
						
						/**
						 *	Check employee personal mail id is exists or not
						*/
						if ($result['success'] && !empty($personalEmail['value']) && $personalEmail['valid'])
						{
							$isEmailExist = $this->_dbPersonal->countEmpEmailId ($personalEmail['value'], $employeeId['value']);
							
							if ($isEmailExist > 0)
							{
								$result = array('success' => false, 'msg' => 'Employee Personal email id already exist', 'type' => 'info');
							}
						}
						
						/**
						 *	Check License details
						*/
						if ($result['success'] && $isLicense['value'])
						{
							$licenseNo               = $this->_validation->alphaNumSpHyValidation($personalDataArr['LicenseNo']);
							$licenseIssueDate        = $this->_validation->dateValidation($personalDataArr['LicenseIssueDate']);
							$licenseExpiryDate       = $this->_validation->dateValidation($personalDataArr['LicenseExpiryDate']);
							$licenseIssuingCountry   = $this->_validation->alphaValidation($personalDataArr['LicenseIssuingCountry']);
							$licenseIssuingAuthority = $this->_validation->onlyLetterSpaceValidation($personalDataArr['LicenseIssuingAuthority']);
							$licenseIssuingState     = $this->_validation->onlyLetterSpaceValidation($personalDataArr['LicenseIssuingState']);
							$vehicleType             = $this->_validation->alphaSpCommaDotAmpBracketValidation($personalDataArr['VehicleType']);
							
							$licenseNo['valid']               = $this->_validation->maxLengthValidation($licenseNo, 30, true);
							$licenseIssuingAuthority['valid'] = $this->_validation->lengthValidation($licenseIssuingAuthority, 1, 50, true);
							$licenseIssuingState['valid']     = $this->_validation->lengthValidation($licenseIssuingState, 1, 30, true);
							$vehicleType['valid']             = $this->_validation->lengthValidation($vehicleType, 1, 30, true);
							
							if (!empty($licenseNo['value']) && $licenseNo['valid'] && !empty($licenseIssueDate['value']) &&
								$licenseIssueDate['valid'] && !empty($licenseExpiryDate['value']) && $licenseExpiryDate['valid'] &&
								!empty($licenseIssuingCountry['value']) && $licenseIssuingCountry['valid'] && !empty($licenseIssuingAuthority['value']) &&
								$licenseIssuingAuthority['valid'] && !empty($licenseIssuingState['value']) && $licenseIssuingState['valid'] &&
								!empty($vehicleType['value']) && $vehicleType['valid'])
							{
								$isValidLicenseIssueDate  = $this->isLessDOB ($dateOfBirth['value'], $licenseIssueDate['value']);
								$isValidLicenseExpireDate = $this->isLessDOB ($dateOfBirth['value'], $licenseExpiryDate['value']);
								
								if ($isValidLicenseIssueDate || (date('Y-m-d') < $licenseIssueDate['value']))
								{
									$result = array('success' => false, 'msg' => 'Invalid license issue date', 'type' => 'info');
								}
								else if ($isValidLicenseExpireDate)
								{
									$result = array('success' => false, 'msg' => 'Invalid license expire date', 'type' => 'info');
								}
								else if ($licenseIssueDate['value'] >= $licenseExpiryDate['value'])
								{
									$result = array('success' => false, 'msg' => 'License issue date should be less than expire date', 'type' => 'info');
								}
								else if (date('Y-m-d') > $licenseExpiryDate['value'])
								{
									$result = array('success' => false, 'msg' => 'License already expired', 'type' => 'info');
								}
								else
								{
									$licenseData = array('Employee_Id'         => $employeeId['value'],
														 'Driving_License_No'  => $licenseNo['value'],
														 'License_Issue_Date'  => $licenseIssueDate['value'],
														 'License_Expiry_Date' => $licenseExpiryDate['value'],
														 'Issuing_Authority'   => $licenseIssuingAuthority['value'],
														 'Issuing_Country'     => $licenseIssuingCountry['value'],
														 'Issuing_State'       => $licenseIssuingState['value'],
														 'Vehicle_Type'        => $vehicleType['value']);
								}
							}
							else
							{
								$result = array('success' => false, 'msg' => 'Invalid license details', 'type' => 'info');
							}
						}
						
						/**
						 *	Check Passport details
						*/
						if ($result['success'] && $isPassport['value'])
						{
							$passportNo          = $this->_validation->onlyLetterNumberValidation($personalDataArr['PassportNo']);
							$passportNo['valid'] = $this->_validation->lengthValidation($passportNo, 1, 30, true);
							
							$passportIssueDate  = $this->_validation->dateValidation($personalDataArr['PassportIssueDate']);
							$passportExpiryDate = $this->_validation->dateValidation($personalDataArr['PassportExpiryDate']);
							
							if (!empty($passportNo['value']) &&$passportNo['valid'] && !empty($passportIssueDate['value']) &&
								$passportIssueDate['valid'] && !empty($passportExpiryDate['value']) && $passportExpiryDate['valid'])
							{
								if (($dateOfBirth['value'] >= $passportIssueDate['value']) || (date('Y-m-d') < $passportIssueDate['value']))
								{
									$result = array('success' => false, 'msg' => 'Invalid passport issue date', 'type' => 'info');
								}
								else if ($dateOfBirth['value'] >= $passportExpiryDate['value'])
								{
									$result = array('success' => false, 'msg' => 'Invalid passport expire date', 'type' => 'info');
								}
								else if ($passportIssueDate['value'] >= $passportExpiryDate['value'])
								{
									$result = array('success' => false, 'msg' => 'Passport issue date should be less than expire date', 'type' => 'info');
								}
								else if (date('Y-m-d') > $passportExpiryDate['value'])
								{
									$result = array('success' => false, 'msg' => 'Passport already expired', 'type' => 'info');
								}
								else
								{
									$passportData = array('Employee_Id' => $employeeId['value'],
														  'Passport_No' => $passportNo['value'],
														  'Issue_Date'  => $passportIssueDate['value'],
														  'Expiry_Date' => $passportExpiryDate['value']);
								}
							}
							else
							{
								$result = array('success' => false, 'msg' => 'Invalid passport details', 'type' => 'info');
							}
						}
					}
					else
					{
						$result = array('success' => false, 'msg' => 'Invalid data on personal info', 'type' => 'info');
					}

					if($result['success']){
						/* work email is mandatory when the allow user signin in true. */
						if($allowUserSignin['value'] == 0) {
							$result = array('success' => true, 'msg' => '', 'type' => 'info');
						}else if($allowUserSignin['value'] == 1 && $allowUserSignin['valid']){
							if($enableSignInWithMobile == 0){
								if($sendInvitation['valid'] && $workEmail['value'] && $workEmail['valid']){
									$result = array('success' => true, 'msg' => '', 'type' => 'info');
								}else{
									$result = array('success' => false, 'msg' => 'Invalid allow user sign in data', 'type' => 'info');
								}
							}else{
								if($signInMobileNumber){
									$result = array('success' => true, 'msg' => '', 'type' => 'info');
								}else{
									$result = array('success' => false, 'msg' => 'Invalid allow user sign in data', 'type' => 'info');
								}
							}
						}else{
							$result = array('success' => false, 'msg' => 'Invalid allow user sign in data', 'type' => 'info');
						}
					} else {
						$result = array('success' => false, 'msg' => 'Invalid data on personal info', 'type' => 'info');
					}
					
					
					/**
					 *	Server side validation for Job Information
					*/
					if ($result['success'])
					{
						$jobDataArr         = $formData['jobData'];
						
						$employeeType       = $this->_validation->intValidation($jobDataArr['EmployeeType']);
						$employeeEmail      = $this->_validation->emailValidation($jobDataArr['EmployeeEmail']);
						$departmentName     = $this->_validation->intValidation($jobDataArr['DepartmentName']);
						$designationName    = $this->_validation->intValidation($jobDataArr['DesignationName']);
						$location           = $this->_validation->intValidation($jobDataArr['Location']);
						$dateOfJoin         = $this->_validation->dateValidation($jobDataArr['DateOfJoin']);
						$managerId          = $this->_validation->intValidation($jobDataArr['ManagerId']);
						$commissionEmployee = $this->_validation->checkboxValidation($jobDataArr['CommissionEmployee']);
						$workSchedule       = $this->_validation->intValidation($jobDataArr['WorkSchedule']);
						$employeeStatus     = $this->_validation->alphaValidation($jobDataArr['EmployeeStatus']);
						$inactiveDate       = $this->_validation->dateValidation($jobDataArr['InactiveDate']);
						$isConfirmed        = $this->_validation->checkboxValidation($jobDataArr['Confirmed']);						
						$confirmationDate   = $this->_validation->dateValidation($jobDataArr['ConfirmationDate']);
						//$isDirector         = $this->_validation->checkboxValidation($jobDataArr['IsDirector']);
                        $isDirector         = $this->_validation->intValidation($jobDataArr['IsDirector']);
						$TDSEXemption       = $this->_validation->checkboxValidation($jobDataArr['TDSExemption']);
						$attendanceEnforcedPayment = $this->_validation->checkboxValidation($jobDataArr['AttendanceEnforcementPayment']);
						$ECISReason         = $this->_validation->intValidation($jobDataArr['ECISReason']);
						$jobCode            = $this->_validation->alphaNumSpCommaValidation($jobDataArr['JobCode']);
						$externalEmployeeId = $this->_validation->onlyLetterNumberValidation($jobDataArr['ExternalEmployeeId']);

						$userDefEmployeeId = $this->_validation->alphaNumSpCDotHySlashValidation($jobDataArr['UserDefinedEmpId']);
						$userDefEmployeeId['valid'] = $this->_validation->lengthValidation($userDefEmployeeId, 1,50, true);
						$probationDate       = $this->_validation->dateValidation($jobDataArr['ProbationDate']); 
						$jobCode['valid']            = $this->_validation->lengthValidation($jobCode, 3, 30, false);
						$externalEmployeeId['valid'] = $this->_validation->lengthValidation($externalEmployeeId, 1, 15,true);
						
						$previousEmployeeExperienceYears  = $this->_validation->intValidation($jobDataArr['Previous_Employee_Experience_Years']);
						$previousEmployeeExperienceMonths = $this->_validation->intValidation($jobDataArr['Previous_Employee_Experience_Months']);
						$previousEmployeeExperienceYears['valid']  = $this->_validation->lengthValidation($previousEmployeeExperienceYears, 1,99, false);
						$previousEmployeeExperienceMonths['valid'] = $this->_validation->lengthValidation($previousEmployeeExperienceMonths, 1,11, false);

						$serviceProviderId = $this->_validation->intValidation($jobDataArr['Service_Provider_Id']);
						
						if (!empty($employeeType['value']) && $employeeType['valid'] && !empty($departmentName['value']) &&
						    $previousEmployeeExperienceYears['valid'] && $previousEmployeeExperienceMonths['valid'] &&  
							$departmentName['valid'] && !empty($designationName['value']) && $designationName['valid'] &&
							!empty($location['value']) && $location['valid'] && !empty($dateOfJoin['value']) && $dateOfJoin['valid'] &&
							(($this->_orgDetails['Field_Force']==1 && !empty($serviceProviderId['value']) && $serviceProviderId['valid']) || 
							($this->_orgDetails['Field_Force']==0 && empty($serviceProviderId['value']) && $serviceProviderId['valid'])) &&
							(empty($employeeEmail['value']) || (!empty($employeeEmail['value']) && $employeeEmail['valid'])) &&
							/*!empty($managerId['value']) &&*/ $managerId['valid'] && !empty($workSchedule['value']) && $workSchedule['valid'] &&
							!empty($employeeStatus['value']) && $employeeStatus['valid'] && ($employeeStatus['value'] == 'Active' ||
							($employeeStatus['value'] == 'InActive' && !empty($inactiveDate['value']) && $inactiveDate['valid'] &&
							 !empty($ECISReason['value']) && $ECISReason['valid'])) && $isDirector['valid'] && !empty($isDirector['value'])&&  $TDSEXemption['valid'] && $attendanceEnforcedPayment['valid'] &&
							(($userDefEmployeeId['valid'] && !empty($userDefEmployeeId['value'])) || empty($userDefEmployeeId['value'])) && (empty($probationDate['value']) || (!empty($probationDate['value']) && $probationDate['valid'])) &&
							(empty($externalEmployeeId['value']) || (!empty($externalEmployeeId['value']) && $externalEmployeeId['valid'])) &&
							(!$isConfirmed['value'] || ($isConfirmed['value'] && !empty($confirmationDate['value']) && $confirmationDate['valid'])))
						{
							/**
							 *	Check Employee job mail id is already exists
							*/
							if (!empty($employeeEmail) && $employeeEmail['valid'])
							{
								$isEmailExist = $this->_dbPersonal->countEmpEmailId ($employeeEmail['value'], $employeeId['value']);
								
								if ($isEmailExist > 0)
								{
									$result = array('success' => false, 'msg' => 'Employee email id already exists', 'type' => 'info');
								}
							}
							
							/**
							 *	Check date of join is already exist
							*/
							if ($result['success'])
							{
								$isValidDateOfJoin = $this->isLessDOB ($dateOfBirth['value'], $dateOfJoin['value']);
								
								if ($isValidDateOfJoin)
								{
									$result = array('success' => false, 'msg' => 'Invalid joining date', 'type' => 'info');
								}
							}
							
							/**
							 *	Check Biometric Integration Id is already exists or not
							*/
							if ($result['success'] && !empty($externalEmployeeId['value']) && $externalEmployeeId['valid'])
							{
								$biometricIntegrationIdExist = $this->_dbCommonFunction->checkBiometricIntegrationIdExist ($externalEmployeeId['value'], $employeeId['value']);
								
								if(!empty($biometricIntegrationIdExist))
								{
									$result = array('success' => false, 'msg' => 'Biometric Integration Id already exist', 'type' => 'warning');
								}
							}
							
							/**
							 *	Check confirmation date
							*/
							if ($result['success'] && $isConfirmed['value'] && !empty($confirmationDate['value']) && $confirmationDate['valid'])
							{
								$isValidDateOfJoin = $this->isLessDOB ($dateOfBirth['value'], $confirmationDate['value']);
								
								if ($isValidDateOfJoin)
								{
									$result = array('success' => false, 'msg' => 'Invalid joining date', 'type' => 'info');
								}
								else
								{
									/**
									 *	show error if confirmation date less than joining date
									*/
									if (strtotime ($dateOfJoin['value']) > strtotime ($confirmationDate['value']))
									{
										$result = array('success' => false, 'msg' => 'Confirmation date must be greater than Date of Join', 'type' => 'info');
									}
								}
							}

							/** Get the employee old DOJ */
							$employeeOldDoj = $this->_dbJobDetail->getDateOfJoin($employeeId['value'],'Yes',0);

							/** If the employee DOJ exist and if the old and new doj not equal then validate employee attendance, salary, leave exist */
							if(!empty($employeeOldDoj) && strtotime ($employeeOldDoj) != strtotime ($dateOfJoin['value'])){
								$employeeDojUsedResult = $this->_dbEmployee->validateEmployeeDojUsed($employeeId['value'], $dateOfJoin['value']);

								/** If employee attendance/leaves/salary already exist for this employee then employee DOJ cannot be changed */
								if(!empty($employeeDojUsedResult['Is_Emp_Doj_Used'])){
									$result = array('success' => false, 'msg' => 'Date Of Join cannot be changed as the employee has either attendance or leave or leave balance import or salary record', 'type' => 'warning');	
								}
							}
						}
						else
						{
							$result = array('success' => false, 'msg' => 'Invalid data on job info', 'type' => 'info');
						}
					}
					
					/**
					 *	Server side validation for Contact Information
					*/
					if ($result['success'])
					{
						$contactDataArr  = $formData['contactData'];
						
						$perApartmentName = $this->_validation->alphaNumSpCDotHySlashValidation($contactDataArr['PerApartmentName']);
						$perStreet        = $this->_validation->alphaNumSpCDotHySlashValidation($contactDataArr['PerStreet']);
						$perCity          = $this->_validation->cityValidation($contactDataArr['PerCity']);
						$perState         = $this->_validation->stateValidation($contactDataArr['PerState']);
						$perCountry       = $this->_validation->alphaValidation($contactDataArr['PerCountry']);
						//$perZip           = $this->_validation->alphaNumSpHyValidation($contactDataArr['PerZip']);
						$curApartmentName = $this->_validation->alphaNumSpCDotHySlashValidation($contactDataArr['CurApartmentName']);
						$curStreet        = $this->_validation->alphaNumSpCDotHySlashValidation($contactDataArr['CurStreet']);
						$curCity          = $this->_validation->cityValidation($contactDataArr['CurCity']);
						$curState         = $this->_validation->stateValidation($contactDataArr['CurState']);
						$curCountry       = $this->_validation->alphaValidation($contactDataArr['CurCountry']);
						//$curZip           = $this->_validation->alphaNumSpHyValidation($contactDataArr['CurZip']);
						
						$useLocationAddress  = $this->_validation->checkboxValidation($contactDataArr['useLocationAddress']);
						
						if($useLocationAddress['value'] == 1){
							$offApartmentName['value'] = $contactDataArr['OffApartmentName'];
							$offStreet['value'] = $contactDataArr['OffStreet'];
							$offCity['value'] = $contactDataArr['OffCity'];
							$offState['value'] = $contactDataArr['OffState'];
							$offCountry['value'] = $contactDataArr['OffCountry'];
							
							$offApartmentName['valid'] = $offStreet['valid'] = $offCity['valid'] = $offState['valid'] = $offCountry['valid'] = 1;
						}
						else{
							$offApartmentName = $this->_validation->alphaNumSpCDotHySlashValidation($contactDataArr['OffApartmentName']);
							$offStreet        = $this->_validation->alphaNumSpCDotHySlashValidation($contactDataArr['OffStreet']);
							$offCity          = $this->_validation->cityValidation($contactDataArr['OffCity']);
							$offState         = $this->_validation->stateValidation($contactDataArr['OffState']);
							$offCountry       = $this->_validation->alphaValidation($contactDataArr['OffCountry']);
							$offApartmentName['valid'] = $this->_validation->lengthValidation($offApartmentName, 1,255, true);
							$offStreet['valid']        = $this->_validation->lengthValidation($offStreet, 1,255, true);
							$offCity['valid']          = $this->_validation->lengthValidation($offCity, 1, 50, true);
							$offState['valid']         = $this->_validation->lengthValidation($offState, 1, 50, true);
						}
						$landlineNo       = $this->_validation->phoneValidation($contactDataArr['LandlineNo']);
						$mobileNo         = $this->_validation->phoneValidation($contactDataArr['MobileNo']);
						if($mobileNo['valid'])
							$mobileNo['valid']  	= $this->_validation->maxLengthValidation($mobileNo, 15, true);
						$workNo           = $this->_validation->phoneValidation($contactDataArr['WorkNo']);
						$faxNo            = $this->_validation->phoneValidation($contactDataArr['FaxNo']);
						
						$perApartmentName['valid'] = $this->_validation->lengthValidation($perApartmentName, 1,255, false);
						$perStreet['valid']        = $this->_validation->lengthValidation($perStreet, 1,255, true);
						$perCity['valid']          = $this->_validation->lengthValidation($perCity, 1, 50, true);
						$perState['valid']         = $this->_validation->lengthValidation($perState, 1, 50, true);
						$curApartmentName['valid'] = $this->_validation->lengthValidation($curApartmentName, 1, 255, false);
						$curStreet['valid']        = $this->_validation->lengthValidation($curStreet, 1, 255, true);
						$curCity['valid']          = $this->_validation->lengthValidation($curCity, 1, 50, true);
						$curState['valid']         = $this->_validation->lengthValidation($curState, 1, 50, true);
						$landlineNo['valid']       = $this->_validation->maxLengthValidation($landlineNo, 15, false);
						$workNo['valid']           = $this->_validation->maxLengthValidation($workNo, 30, false);
						$faxNo['valid']            = $this->_validation->maxLengthValidation($faxNo, 15, false);
						
						if($pincodeCustom['Enable']){
							$perZip           = $this->_validation->alphaNumSpHyValidation($contactDataArr['PerZip']);
							$perZip['valid']           = $this->_validation->lengthValidation($perZip, 1, 15, true);
							
							$curZip           = $this->_validation->alphaNumSpHyValidation($contactDataArr['CurZip']);	
							$curZip['valid']           = $this->_validation->lengthValidation($curZip, 1, 15, true);
							
							if($useLocationAddress['value'] == 1){
								$offZip['value'] = $contactDataArr['OffZip'];
								$offZip['valid'] = 1;
							}
							else{
								$offZip           = $this->_validation->alphaNumSpHyValidation($contactDataArr['OffZip']);
								$offZip['valid']           = $this->_validation->lengthValidation($offZip, 1, 15, true);
							}
						}
						else
						{
							$perZip['value'] = '';
							$perZip['valid'] = 1;
						
							$curZip['value'] = '';
							$curZip['valid'] = 1;
						
							$offZip['value'] = '';
							$offZip['valid'] = 1;
						}
						
						
						if (($useLocationAddress['value'] == 0 || $useLocationAddress['value'] == 1) &&(empty($perApartmentName['value']) || (!empty($perApartmentName['value']) && $perApartmentName['valid'])) &&
							!empty($perStreet['value']) && $perStreet['valid'] && !empty($perCity['value']) && $perCity['valid'] &&
							!empty($perState['value']) && $perState['valid'] && !empty($perCountry['value']) && $perCountry['valid'] &&
							((empty($perZip['value']) && !($pincodeCustom['Required']))  || ($perZip['valid'] && !empty($perZip['value']))) &&
							(empty($curApartmentName['value']) || (!empty($curApartmentName['value']) && $curApartmentName['valid'])) &&
							!empty($curStreet['value']) && $curStreet['valid'] && !empty($curCity['value']) && $curCity['valid'] &&
							!empty($curState['value']) && $curState['valid'] && !empty($curCountry['value']) && $curCountry['valid'] &&
							((empty($curZip['value']) && !($pincodeCustom['Required']))  || ($curZip['valid'] && !empty($curZip['value']))) &&
							(empty($offApartmentName['value']) || (!empty($offApartmentName['value']) && $offApartmentName['valid'])) &&
							!empty($offStreet['value']) && $offStreet['valid'] && !empty($offCity['value']) && $offCity['valid'] &&
							!empty($offState['value']) && $offState['valid'] && !empty($offCountry['value']) && $offCountry['valid'] &&
							((empty($offZip['value']) && !($pincodeCustom['Required'])) || ($offZip['valid'] && !empty($offZip['value']))) && !empty($mobileNo['value']) && $mobileNo['valid'] &&
							(empty($landlineNo['value']) || (!empty($landlineNo['value']) && $landlineNo['valid'])) &&
							(empty($mobileNo['value']) || (!empty($mobileNo['value']) && $mobileNo['valid'])) &&
							(empty($workNo['value']) || (!empty($workNo['value']) && $workNo['valid'])) &&
							(empty($faxNo['value']) || (!empty($faxNo['value']) && $faxNo['valid'])))
						{
							
						}
						else
						{
							$result = array('success' => false, 'msg' => 'Invalid data on contact info', 'type' => 'info');
						}
					}
					
					/**
					 *	Server side validation for Career Information
					*/
					if ($result['success'])
					{
						$careerDataArr  = $formData['careerData'];
						
                        $illiterate     = $this->_validation->checkboxValidation($careerDataArr['Illiterate']);                   
                        
						$primarySkill   = $this->_validation->skillsValidation($careerDataArr['PrimarySkill']);
						$secondarySkill = $this->_validation->skillsValidation($careerDataArr['SecondarySkill']);
						$knownSkills    = $this->_validation->skillsValidation($careerDataArr['KnownSkills']);
						$handsOn        = $this->_validation->skillsValidation($careerDataArr['HandsOn']);
						
						$primarySkill['valid']   = $this->_validation->lengthValidation($primarySkill, 1, 50, false);
						$secondarySkill['valid'] = $this->_validation->lengthValidation($secondarySkill, 1, 50, false);
						$knownSkills['valid']    = $this->_validation->lengthValidation($knownSkills, 1, 50, false);
						$handsOn['valid']        = $this->_validation->lengthValidation($handsOn, 1, 50, false);
						
						if ((empty($primarySkill['value']) || (!empty($primarySkill['value']) && $primarySkill['valid'])) &&
							(empty($secondarySkill['value']) || (!empty($secondarySkill['value']) && $secondarySkill['valid'])) &&
							(empty($knownSkills['value']) || (!empty($knownSkills['value']) && $knownSkills['valid'])) &&
							(empty($handsOn['value']) || (!empty($handsOn['value']) && $handsOn['valid'])))
						{
							
						}
						else
						{
							$result = array('success' => false, 'msg' => 'Invalid data on career info', 'type' => 'info');
						}
					}
					
					/**
					 *	Server side validation for Bank account Information
					*/
					if ($result['success'])
					{
						$bankDataArr    = $formData['bankData'];
								
						// $isBankDetails  = $this->_validation->checkboxValidation($bankDataArr['isBankDetails']);
						// $accountNumber  = $this->_validation->onlyLetterNumberValidation($bankDataArr['AccountNumber']);
						// $bankName       = $this->_validation->onlyLetterSpaceValidation($bankDataArr['BankName']);
						// $branchName     = $this->_validation->alphaNumSpDotValidation($bankDataArr['BranchName']);
						// $branchStreet   = $this->_validation->alphaNumSpCDotHyValidation($bankDataArr['BranchStreet']);
						// $branchCity     = $this->_validation->onlyLetterSpaceValidation($bankDataArr['BranchCity']);
						// $branchState    = $this->_validation->onlyLetterSpaceValidation($bankDataArr['BranchState']);
						// //$branchPincode  = $this->_validation->alphaNumSpHyValidation($bankDataArr['BranchPincode']);
						// $accountType    = $this->_validation->intValidation($bankDataArr['AccountType']);
						// $branchIFSCCode = $this->_validation->onlyLetterNumberValidation($bankDataArr['BranchIFSCCode']);


						$providentFund  = $this->_validation->alphaNumHypSlashValidation($bankDataArr['ProvidentFund']);
						//$professionTax  = $this->_validation->alphaNumHypSlashValidation($bankDataArr['ProfessionalTax']);
                        //$pan            = $this->_validation->panValidation($bankDataArr['PAN']);
						//$aadhaarNo      = $this->_validation->aadhaarValidation($bankDataArr['AadhaarNo']);
						$uanNumber      = $this->_validation->aadhaarValidation($bankDataArr['UAN']);
						
						// $accountNumber['valid']  = $this->_validation->lengthValidation($accountNumber, 8, 30, false);
						// $bankName['valid']       = $this->_validation->lengthValidation($bankName, 1, 30, false);
						// $branchName['valid']     = $this->_validation->lengthValidation($branchName, 1, 50, false);
						// $branchStreet['valid']   = $this->_validation->lengthValidation($branchStreet, 1, 100, false);
						// $branchCity['valid']     = $this->_validation->lengthValidation($branchCity, 1, 50, false);
						// $branchState['valid']    = $this->_validation->lengthValidation($branchState, 1, 50, false);
						// //$branchPincode['valid']  = $this->_validation->lengthValidation($branchPincode, 2, 14, false);
						// $branchIFSCCode['valid'] = $this->_validation->lengthValidation($branchIFSCCode, 1, 30, false);

						$providentFund['valid']  = $this->_validation->lengthValidation($providentFund, 1, 30, false);
						//$professionTax['valid']  = $this->_validation->lengthValidation($professionTax, 3, 30, false);
                        //$pan['valid']            = $this->_validation->lengthValidation($pan, 10, 10, false);
						//$aadhaarNo['valid']      = $this->_validation->lengthValidation($aadhaarNo, 12, 12, false);
						$uanNumber['valid']      = $this->_validation->lengthValidation($uanNumber, 0, 25, false);
						
						// if($pincodeCustom['Enable']){
						// 	$branchPincode  = $this->_validation->alphaNumSpHyValidation($bankDataArr['BranchPincode']);
						// 	$branchPincode['valid']  = $this->_validation->lengthValidation($branchPincode, 1, 15, false);
						// }
						// else
						// {
						// 	$branchPincode['value'] = '';
						// 	$branchPincode['valid'] = 1;
						// }
						
						if($panCustom['Enable']){
							$pan            = $this->_validation->aadhaarValidation($bankDataArr['PAN']);
                            $pan['valid']   = $this->_validation->lengthValidation($pan, 0, 25, false);
						}
						else
						{
							$pan['value'] = '';
							$pan['valid'] = 1;
						}
						
						if($aadhaarNoCustom['Enable']){
							$aadhaarNo      = $this->_validation->aadhaarValidation($bankDataArr['AadhaarNo']);
							$aadhaarNo['valid']      = $this->_validation->lengthValidation($aadhaarNo, 0, 25, false);
						}
						else
						{
							$aadhaarNo['value'] = '';
							$aadhaarNo['valid'] = 1;
						}
						
						if (
							// $isBankDetails['value'] == 0 || ($isBankDetails['value'] == 1 &&
							// !empty($accountNumber['value']) && $accountNumber['valid'] &&
                            // !empty($bankName['value']) && $bankName['valid'] &&
							// !empty($branchName['value']) && $branchName['valid'] &&
                            // !empty($branchStreet['value']) && $branchStreet['valid'] &&
							// !empty($branchCity['value']) && $branchCity['valid'] &&
                            // !empty($branchState['value']) && $branchState['valid'] &&
							// ((empty($branchPincode['value']) && !($pincodeCustom['Required']))  ||
                            //  ($branchPincode['valid'] && !empty($branchPincode['value']))) &&
							// //!empty($branchPincode['value']) && $branchPincode['valid'] &&
                            // !empty($accountType['value']) && $accountType['valid'] &&
							// !empty($branchIFSCCode['value']) && $branchIFSCCode['valid']) &&
							
							(empty($providentFund['value']) || (!empty($providentFund['value']) && $providentFund['valid'])) &&
							//(empty($professionTax['value']) || (!empty($professionTax['value']) && $professionTax['valid'])) &&
                            //(empty($pan['value']) || (!empty($pan['value']) && $pan['valid'])) &&
							((empty($pan['value']) && !($panCustom['Required']))  || ($pan['valid'] && !empty($pan['value']))) &&
							((empty($aadhaarNo['value']) && !($aadhaarNoCustom['Required']))  || ($aadhaarNo['valid'] && !empty($aadhaarNo['value']))) &&
							//(empty($aadhaarNo['value']) || (!empty($aadhaarNo['value']) && $aadhaarNo['valid'])) &&
							(empty($uanNumber['value']) || (!empty($uanNumber['value']) && $uanNumber['valid'])))
						{
							//To check whether entered bank account number and bank name already exists or not.
							//$checkBankInfo = $this->_dbPersonal->checkBankDetails ($accountNumber['value'], $bankName['value'], $employeeId['value']);
							
							// if ($checkBankInfo > 0)
							// {
							// 	$result = array('success' => false, 'msg' => 'Bank Account details already exist', 'type' => 'info');
							// }
						}
						else
						{
							$result = array('success' => false, 'msg' => 'Invalid data on bank account info', 'type' => 'info');
						}
					}
					
					/**
					 *	If no server side validation error than we process update process
					*/
					if ($result['success'])
					{
						$formDataArr = array();
						
						/**
						 *	Get form status from table
						*/
						$formStatus = $oldFormStatus =  $this->_dbEmployee->getFormStatus ($employeeId['value']);
						
						//if (empty($formStatus))
						//{
						//	$formStatus = 0;
						//}
                        
                        $formStatus = 1;
						
						/**
						 *	Employee personal information
						*/
						$formDataArr['personalDataArr'] = array('Salutation'            => $salutation['value'],
																//'Photo_Path'            => $employeePhotoPath,
																'Emp_First_Name'        => $employeeFirstName['value'],
																'Emp_Middle_Name'       => $this->fnCheckValue ($employeeMiddleName['value']),
																'Emp_Last_Name'         => $employeeLastName['value'],
																'Emp_Pref_First_Name'   => $this->fnCheckValue ($employeeNickName['value']),
																'Gender'                => $gender['value'],
																'DOB'                   => $dateOfBirth['value'],
																'Place_Of_Birth'        => $this->fnCheckValue ($placeOfBirth['value']),
																'Marital_Status'        => $maritalStatus['value'],
																'Military_Service'      => $militaryService['value'],
																'Ethnic_Race'           => $this->fnCheckValue ($ethnicRace['value']),
																'Nationality'           => $nationality['value'],
																'Religion'              => $this->fnCheckValue ($religion['value']),
																'Caste'                 => $this->fnCheckValue ($caste['value']),
																'Blood_Group'           => $bloodGroup,
																'Smoker'                => $smoker['value'],
																'Smokerasof'            => $this->fnCheckValue ($smokerAsOf['value']),
																'Is_Manager'            => $isManager['value'],
																'Physically_Challenged' => $physicallyChallenged['value'],
																'Form_Status'           => $formStatus,
																'Personal_Email'        => $this->fnCheckValue ($personalEmail['value']),
																'Is_Illiterate'         => $illiterate['value'],
																'Allow_User_Signin'		=> $allowUserSignin['value'],
																'Work_Email' 			=> $workEmail['value'],
																'Enable_Sign_In_With_Mobile_No' => $enableSignInWithMobile,
																'Sign_In_Mobile_Number' => $signInMobileNumber,
																'Sign_In_Mobile_No_Country_Code'=> $signInMobileNumberCountryCode,
																//'Photo_Path'            => $path,
                                                                //'PAN'                   => $pan['value'],
																// 'UserName_OverWrite'    => $isOverWriteUserName['value'],
																'Lock_Flag'             => 0);
						
						//Employee Language
						$formDataArr['languageDataArr'] = $languagesKnown;
						
						//employee hobbies
						if (!empty($hobbies['value']))
						{
							$formDataArr['hobbyDataArr'] = array('Employee_Id' => $employeeId['value'],
																 'Emp_Hobby'   => htmlentities ($hobbies['value']));
						}
						
						//Employee License details
						if ($isLicense['value'])
						{
							$formDataArr['licenseDataArr'] = $licenseData;
						}
						
						//Employee Passport details
						if ($isPassport['value'])
						{
							$formDataArr['passportDataArr'] = $passportData;
						}
						
						if($userDefEmployeeId['value']!='-'){
							//$userdefval = strtoupper($userDefEmployeeId['value']);
							$userdefval = $userDefEmployeeId['value'];
							
						}
						else{
							$userdefval = new Zend_Db_Expr('NULL');
						}
						
						$previousEmployeeExperience = $this->_dbEmployee->getPreviousExperienceInMonths($previousEmployeeExperienceYears['value'],$previousEmployeeExperienceMonths['value']);
						/**
						 *	Job Details
						*/
						$formDataArr['jobInfoDataArr'] = array('Employee_Id'         => $employeeId['value'],
															   'EmpType_Id'          => $employeeType['value'],
															   'Department_Id'       => $departmentName['value'],
															   'Designation_Id'      => $designationName['value'],
															   'Date_Of_Join'        => $dateOfJoin['value'],
															   'Job_Code'            => $this->fnCheckValue ($jobCode['value']),
															   'Emp_Email'           => $this->fnCheckValue ($employeeEmail['value']),
															   'Manager_Id'          => $managerId['value'],
															   'Location_Id'         => $location['value'],
															   'Confirmed'           => $isConfirmed['value'],
															   'Emp_Profession'      => $isDirector['value'],
															   'TDS_Exemption'       => $TDSEXemption['value'],
															   'Attendance_Enforced_Payment' => $attendanceEnforcedPayment['value'],
															   'Emp_Status'          => $employeeStatus['value'],
                                                               'Confirmation_Date'   => $this->fnCheckValue ($confirmationDate['value']),
															   'Probation_Date'      => $probationDate['value'],
															   'Commission_Employee' => $commissionEmployee['value'],
															   'Work_Schedule'       => $workSchedule['value'],
															   'External_EmpId'      => $this->fnCheckValue ($externalEmployeeId['value']),
															   'User_Defined_EmpId'  => $userdefval,
															   'Service_Provider_Id' => $this->fnCheckValue($serviceProviderId['value']),
															   'Previous_Employee_Experience' => $previousEmployeeExperience);

						if($employeeStatus['value'] === 'Active'){
							$formDataArr['jobInfoDataArr']['Emp_InActive_Date']= new Zend_Db_Expr('NULL');
							$formDataArr['jobInfoDataArr']['Reason_Id']= 0;
						}else{
							$employeeInactiveDetails = $this->_dbEmployee->getJobEmpInactiveDetail($employeeId['value']);

							$formDataArr['jobInfoDataArr']['Emp_InActive_Date'] = $employeeInactiveDetails['Emp_InActive_Date'];
							$formDataArr['jobInfoDataArr']['Reason_Id'] = $employeeInactiveDetails['Reason_Id'];
						}
						
						if($useLocationAddress['value'] == 1)
						{
							$formDataArr['contactInfoDataArr'] = array('Employee_Id'      => $employeeId['value'],
																   'pApartment_Name'  => /*$this->fnCheckValue (*/$perApartmentName['value']/*)*/,
																   'pStreet_Name'     => $perStreet['value'],
																   'pCity'            => $perCity['value'],
																   'pState'           => $perState['value'],
																   'pCountry'         => $perCountry['value'],
																   'pPincode'         => $perZip['value'],
																   'cApartment_Name'  => /*$this->fnCheckValue (*/$curApartmentName['value']/*)*/,
																   'cStreet_Name'     => $curStreet['value'],
																   'cCity'            => $curCity['value'],
																   'cState'           => $curState['value'],
																   'cCountry'         => $curCountry['value'],
																   'cPincode'         => $curZip['value'],
																   'Use_Location_Address' => $useLocationAddress['value'],
																   'oApartment_Name'  => new Zend_Db_Expr('NULL'),
																   'oStreet_Name'     => new Zend_Db_Expr('NULL'),
																   'oCity'            => new Zend_Db_Expr('NULL'),
																   'oState'           => new Zend_Db_Expr('NULL'),
																   'oCountry'         => new Zend_Db_Expr('NULL'),
																   'oPincode'         => new Zend_Db_Expr('NULL'),
																   'Land_Line_No'     => $this->fnCheckValue ($landlineNo['value']),
																   'Mobile_No'        => $mobileNo['value'],
																   'Fax_No'           => /*$this->fnCheckValue (*/$faxNo['value']/*)*/,
																   'Work_No'          => $this->fnCheckValue ($workNo['value']));
							
						}
						else
						{
							$formDataArr['contactInfoDataArr'] = array('Employee_Id'      => $employeeId['value'],
																   'pApartment_Name'  => /*$this->fnCheckValue (*/$perApartmentName['value']/*)*/,
																   'pStreet_Name'     => $perStreet['value'],
																   'pCity'            => $perCity['value'],
																   'pState'           => $perState['value'],
																   'pCountry'         => $perCountry['value'],
																   'pPincode'         => $perZip['value'],
																   'cApartment_Name'  => /*$this->fnCheckValue (*/$curApartmentName['value']/*)*/,
																   'cStreet_Name'     => $curStreet['value'],
																   'cCity'            => $curCity['value'],
																   'cState'           => $curState['value'],
																   'cCountry'         => $curCountry['value'],
																   'cPincode'         => $curZip['value'],
																   'Use_Location_Address' =>$useLocationAddress['value'],
																   'oApartment_Name'  => $offApartmentName['value'],
																   'oStreet_Name'     => $offStreet['value'],
																   'oCity'            => $offCity['value'],
																   'oState'           => $offState['value'],
																   'oCountry'         => $offCountry['value'],
																   'oPincode'         => $offZip['value'],
																   'Land_Line_No'     => $this->fnCheckValue ($landlineNo['value']),
																   'Mobile_No'        => $mobileNo['value'],
																   'Fax_No'           => /*$this->fnCheckValue (*/$faxNo['value']/*)*/,
																   'Work_No'          => $this->fnCheckValue ($workNo['value']));
							
						}
			
            			/**
						 *	Skill Details
						*/
						if (!empty($primarySkill['value']) || !empty($secondarySkill['value']) || !empty($knownSkills['value']) ||
							!empty($handsOn['value']))
						{
							$formDataArr['careerInfoDataArr'] = array('Employee_Id'     => $employeeId['value'],
																	  'Primary_Skill'   => $primarySkill['value'],
																	  'Secondary_Skill' => $secondarySkill['value'],
																	  'Known_Skills'    => $knownSkills['value'],
																	  'Hands_On'        => $handsOn['value']);
						}
						
						/**
						 *	Bank Account Details
						*/
						// if ($isBankDetails['value'] == 1)
						// {
						// 	$formDataArr['bankInfoDataArr'] = array('Employee_Id'         => $employeeId['value'],
						// 											'Bank_Account_Number' => $accountNumber['value'],
						// 											'Bank_Name'           => $bankName['value'],
						// 											'Branch_Name'         => $branchName['value'],
						// 											'Street'              => $branchStreet['value'],
						// 											'City'                => $branchCity['value'],
						// 											'State'               => $branchState['value'],
						// 											'Zip'                 => $branchPincode['value'],
						// 											'Account_Type_Id'     => $accountType['value'],
						// 											'IFSC_Code'           => $branchIFSCCode['value']);
            			// }
            			
                        /**
                        *	PAN details
                        */
                        if (!empty($pan['value']) && $pan['valid'])
                        {   
                            $formDataArr['personalDataArr']['PAN'] = $pan['value'];
                        } else
						{
							$formDataArr['personalDataArr']['PAN'] = new Zend_Db_Expr('NULL');
						}
						
                        /**
                        *	Aadhaar Card Number details
                        */
                        if (!empty($aadhaarNo['value']) && $aadhaarNo['valid'])
                        {   
                            $formDataArr['personalDataArr']['Aadhaar_Card_Number'] = $aadhaarNo['value'];
                        }
						else
						{
							$formDataArr['personalDataArr']['Aadhaar_Card_Number'] = new Zend_Db_Expr('NULL');
						}
						
                        /**
                        *	UAN details
                        */
                        if (!empty($uanNumber['value']) && $uanNumber['valid'])
                        {   
                            $formDataArr['personalDataArr']['UAN'] = $uanNumber['value'];
                        } else
						{
							$formDataArr['personalDataArr']['UAN'] = new Zend_Db_Expr('NULL');
						}
                       
						/**
						 *	provident Fund details
						*/
						if (!empty($providentFund['value']) && $providentFund['valid'])
						{
							$formDataArr['jobInfoDataArr']['Pf_PolicyNo'] = $providentFund['value'];
						}
                        
						$customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
						$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
						
						$result = $this->_dbEmployee->updateEmployeesInfo ($formDataArr, $employeeId['value'], $this->_logEmpId, 'save', $formName,$btnClick);
						/* If the old form status is 0, refresh the custom group employee list */
						if($result['success'] && empty($oldFormStatus)){							
							$refreshCustomGroup = $this->_dbEmployee->callCustomGroupRefreshApi(array($employeeId['value']),$this->_logEmpId);
						}
					}
					
					$this->view->result = $result;
				}
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Sorry, Access denied', 'type' => 'danger');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
     * Delete employees
     */
    public function deleteEmployeesAction ()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-employees', 'html')->initContext();
	
				$employeeId = $this->_getParam('employeeId', null);
				$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
				$employeeId = $this->_validation->intValidation($employeeId);
			
            if (/*($this->_employeeSelfAccess['Delete'] == 1 && $this->_logEmpId == $employeeId['value']) ||*/
				$this->_employeeAccess['Delete'] == 1)
            {
			    if (!empty($employeeId['value']) && $employeeId['valid'])
                {
					$customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
					$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
				
                    $this->view->result = $this->_dbEmployee->deleteEmployee ($employeeId['value'], $this->_logEmpId, $formName);
                }
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
				}    
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Sorry, Access denied', 'type' => 'danger');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
	 *	list employees dependent details
	*/
	public function listEmployeeDependentsAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-employee-dependents', 'json')->initContext();
			
            $sortField = $this->_getParam('iSortCol_0', null);
			$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
			
			$sortOrder = $this->_getParam('sSortDir_0', null);
			$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
			
			$page = $this->_getParam('iDisplayStart', null);
			$page = isset($page) ? intval($page) : 0;
			$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
			
			$rows = $this->_getParam('iDisplayLength', null);
			$rows = isset($rows) ? intval($rows) : 10;
			$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
			
			$searchAll = $this->_getParam('sSearch', null);
			$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
			
			$employeeId = $this->_getParam('employeeId', null);
			$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
			
			$this->view->result = $this->_dbEmployee->listEmployeeDependent ($page, $rows, $sortField, $sortOrder, $searchAll, $employeeId);
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
   
	/**
     * Get marital relationship details based on  maritalStatusId
     */
    public function listMaritalRelationshipAction ()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('list-marital-relationship', 'json')->initContext();
				
            $maritalStatusId = $this->_getParam('maritalStatusId', null);
            $maritalStatusId = filter_var($maritalStatusId, FILTER_SANITIZE_NUMBER_INT);
			
			$employeeId = $this->_getParam('employeeId', null);
			$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
			
			// if (!empty($maritalStatusId) && !empty($employeeId))
			if (!empty($maritalStatusId))

            {
				$this->view->result = $this->_dbPersonal->getMaritalStatusRelationship ($maritalStatusId, $employeeId);
            }
			else
			{
				$this->view->result = array();
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
	 *	Add, edit employee dependent details
	*/
	public function updateEmployeeDependentAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('update-employee-dependent', 'json')->initContext();
			
            if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				
				$employeeId   = $this->_validation->intValidation($formData['employeeId']);
				$dependentId  = $this->_validation->intValidation($formData['dependentId']);
				$firstName    = $this->_validation->onlyLetterSpaceValidation($formData['firstName']);
				$lastName     = $this->_validation->onlyLetterSpaceValidation($formData['lastName']);
				$relationship = $this->_validation->alphaValidation($formData['relationship']);
				$dateOfBirth  = $this->_validation->dateValidation($formData['dateOfBirth']);
			//	$age          = $this->_validation->alphaNumSpValidation($formData['age']);
				
				$firstName['valid'] = $this->_validation->lengthValidation($firstName, 3, 30, true);
				$lastName['valid']  = $this->_validation->lengthValidation($lastName, 1, 30, true);
				
				if (!empty($employeeId['value']) && $employeeId['valid'] && !empty($firstName['value']) && $firstName['valid'] &&
					(empty($lastName['value']) || (!empty($lastName['value']) && $lastName['valid'])) && !empty($relationship['value']) && 
					$relationship['valid'] && !empty($dateOfBirth['value']) && $dateOfBirth['valid'] /*&& !empty($age['value']) && $age['valid']*/)
				{
					$dependentData = array('Employee_Id'          => $employeeId['value'],
										   'Dependent_First_Name' => $firstName['value'],
										   'Dependent_Last_Name'  => $lastName['value'],
										   'Relationship'         => $relationship['value'],
										   'Dependent_DOB'        => $dateOfBirth['value']);
										   //'Age'                  => $age['value']);
					
					$this->view->result = $this->_dbEmployee->updateDependent ($dependentData, $dependentId['value'], $this->_logEmpId);
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid Data', 'type' => 'warning');
				}
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
	 *	Delete Employee dependent details
	*/
	public function deleteEmployeeDependentAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-employee-dependent', 'json')->initContext();
			
            $employeeId = $this->_getParam('employeeId', null);
			$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
			
			$dependentId = $this->_getParam('dependentId', null);
			$dependentId = filter_var($dependentId, FILTER_SANITIZE_NUMBER_INT);
			
			$maritalStatusId = $this->_getParam('maritalStatusId', null);
			$maritalStatusId = filter_var($maritalStatusId, FILTER_SANITIZE_NUMBER_INT);
			
			if (!empty($dependentId) && $dependentId > 0)
			{
				$this->view->result = $this->_dbEmployee->deleteEmployeeDependent ($dependentId,$employeeId,$this->_logEmpId);
			}
			else if (!empty($employeeId) && $employeeId > 0 && !empty($maritalStatusId))
			{
				$this->view->result = $this->_dbEmployee->deleteEmployeeDependentByMaritalStatus ($employeeId, $maritalStatusId,$this->_logEmpId);
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
    
	/**
	 *	List employee experience details
	*/
	public function listEmployeeExperienceAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-employee-experience', 'json')->initContext();
			
            $sortField = $this->_getParam('iSortCol_0', null);
			$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
			
			$sortOrder = $this->_getParam('sSortDir_0', null);
			$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
			
			$page = $this->_getParam('iDisplayStart', null);
			$page = isset($page) ? intval($page) : 0;
			$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
			
			$rows = $this->_getParam('iDisplayLength', null);
			$rows = isset($rows) ? intval($rows) : 10;
			$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
			
			$searchAll = $this->_getParam('sSearch', null);
			$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
			
			$employeeId = $this->_getParam('employeeId', null);
			$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
			
			$isJobCandidateView = $this->_getParam('isJobCandidateView', 0);
			$isJobCandidateView = filter_var($isJobCandidateView, FILTER_SANITIZE_NUMBER_INT);
			
			$this->view->result = $this->_dbEmployee->listEmployeeExperiences ($page, $rows, $sortField, $sortOrder, $searchAll,
																			   $employeeId, $isJobCandidateView);
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
	 *	Add, Edit Employee experience details
	*/
	public function updateEmployeeExperienceAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('update-employee-experience', 'json')->initContext();
			
            if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				
				$employeeId      = $this->_validation->intValidation($formData['employeeId']);
				$experienceId    = $this->_validation->intValidation($formData['experienceId']);
				$companyName     = $this->_validation->alSpDotAndHypenValidation($formData['companyName']);
				$locationName    = $this->_validation->onlyLetterSpaceValidation($formData['locationName']);
				$designationName = $this->_validation->designationValid($formData['designationName']);
				$startDate       = $this->_validation->dateValidation($formData['startDate']);
				$endDate         = $this->_validation->dateValidation($formData['endDate']);
				$dateOfBirth     = $this->_validation->dateValidation($formData['dateOfBirth']);
				$duration        = $this->_validation->amountValidation($formData['duration']);
				
				$companyName['valid']     = $this->_validation->lengthValidation($companyName, 3, 50, true);
				$locationName['valid']    = $this->_validation->lengthValidation($locationName, 3, 50, true);
				$designationName['valid'] = $this->_validation->lengthValidation($designationName, 3, 50, true);
				
				if (!empty($employeeId['value']) && $employeeId['valid'] && !empty($companyName['value']) && $companyName['valid'] &&
					!empty($locationName['value']) && $locationName['valid'] && !empty($designationName['value']) && $designationName['valid'] &&
					!empty($startDate['value']) && $startDate['valid'] && !empty($endDate['value']) && $endDate['valid'] &&
					!empty($dateOfBirth['value']) && $dateOfBirth['valid'] && (strtotime ($startDate['value']) <= strtotime (date('Y-m-d'))))
				{
					if (strtotime ($startDate['value']) > strtotime ($endDate['value']))
					{
						$this->view->result = array('success' => false, 'msg' => 'Start Date should be ', 'type' => 'warning');
					}
					else
					{
						$isValidStartDate = $this->isLessDOB ($dateOfBirth['value'], $startDate['value']);
						
						if (!$isValidStartDate)
						{
							$fromDate = date_create ($startDate['value']);
							$toDate   = date_create ($endDate['value']);
							$interval = date_diff ($fromDate, $toDate);
							$years    = $interval->y;
							$months   = $interval->m;
							
							$experienceData = array('Employee_Id'           => $employeeId['value'],
													'Prev_Company_Name'     => $companyName['value'],
													'Prev_Company_Location' => $locationName['value'],
													'Designation'           => $designationName['value'],
													'Start_Date_Join'       => $startDate['value'],
													'End_Date'              => $endDate['value'],
													'Duration'              => $duration['value'],
													'Years'                 => $years,
													'Months'                => $months);
							
							$this->view->result = $this->_dbEmployee->updateExperience ($experienceData, $experienceId['value'], $this->_logEmpId);
						}
						else
						{
							$this->view->result = array('success' => false, 'msg' => 'Start date should be greater than date of birth', 'type' => 'warning');
						}
					}
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid Data', 'type' => 'warning');
				}
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	public function updateExperienceFilesAction()
    {
		$this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('update-experience-files', 'json')->initContext();
			
            $experienceId = $this->_getParam('experienceId', null);
			$experienceId = filter_var($experienceId, FILTER_SANITIZE_NUMBER_INT);
                         
			if ( $this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				
					$customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
					$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
					
							$employeesDocumentDetails = array();
	
					$employeesDocumentFilesArr = array();				
					
					if(!empty($formData['employeesDocumentFiles']))
					{
						$uploadPathExists = 1;
						
						foreach($formData['employeesDocumentFiles'] as $key=>$row)
						{
							$employeesDocumentFilesArr[$key]['File_Name'] = $row['Name'];
							$employeesDocumentFilesArr[$key]['File_Size'] = $row['Size'];
						}							
					}
					if(!empty($uploadPathExists))
					{                    		
						$this->view->result = $this->_dbEmployee->updateExperienceFiles($employeesDocumentDetails,$employeesDocumentFilesArr,$experienceId, $this->_logEmpId, $formName);
					}
					else
					{
						$this->view->result = array('success' => false, 'msg' => 'Please Upload the Document(s)', 'type' => 'info');
					}
			}
        }
		else
		{
			$this->_helper->redirector('index', 'employees', 'employees');
		}
    }
	
	public function removeUploadedFilesAction()
    {
		$this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('remove-uploaded-files', 'json')->initContext();
			
            $experienceId = $this->_getParam('experienceId');
    	    $experienceId = $this->_validation->intValidation($experienceId);
			
			$employeesDocumentFileName = $this->_getParam('_employeesDocumentFileName');
			
			if(!empty($experienceId['value']) && !empty($employeesDocumentFileName))
			{
				$this->view->result = $this->_dbEmployee->deleteEmployeesDocumentFiles($experienceId['value'],$employeesDocumentFileName);
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
			}	
        }
		else{
			$this->_helper->redirector('index', 'employees', 'employees');
		}
    }
	
//	delete employee exp
	public function deleteEmployeeExperienceDetailsAction()
	{
		$this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-employee-experience', 'json')->initContext();
			
            $experienceId = $this->_getParam('experienceId', null);
			$experienceId = filter_var($experienceId, FILTER_SANITIZE_NUMBER_INT);
			
			if (!empty($experienceId) && $experienceId > 0)
			{
				$this->view->result = $this->_dbEmployee->deleteEmployeeExperienceDetails ($experienceId);
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
	}
	
	/**
	 *	Delete Employee experience details
	*/
	public function deleteEmployeeExperienceAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-employee-experience', 'json')->initContext();
			
            $experienceId = $this->_getParam('experienceId', null);
			$experienceId = filter_var($experienceId, FILTER_SANITIZE_NUMBER_INT);
			
			$docExists = $this->_getParam('docExists', null);
			
			$deleteDocs = $this->_getParam('deleteDocs', null);
			
			if (!empty($experienceId) && $experienceId > 0)
			{
				$this->view->result = $this->_dbEmployee->deleteEmployeeExperience ($experienceId, $docExists, $deleteDocs);
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
	 *	list employees all assets details
	*/
	public function listEmployeeAssetAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-employee-asset', 'json')->initContext();
			
            $sortField = $this->_getParam('iSortCol_0', null);
			$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
			
			$sortOrder = $this->_getParam('sSortDir_0', null);
			$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
			
			$page = $this->_getParam('iDisplayStart', null);
			$page = isset($page) ? intval($page) : 0;
			$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
			
			$rows = $this->_getParam('iDisplayLength', null);
			$rows = isset($rows) ? intval($rows) : 10;
			$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
			
			$searchAll = $this->_getParam('sSearch', null);
			$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
			
			$employeeId = $this->_getParam('employeeId', null);
			$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
			
			$this->view->result = $this->_dbEmployee->listEmployeeAssets ($page, $rows, $sortField, $sortOrder, $searchAll, $employeeId);
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
	 *	Add, Update employee assets details
	*/
	public function updateEmployeeAssetAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('update-employee-asset', 'json')->initContext();
			
            if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				
				$employeeId  = $this->_validation->intValidation($formData['employeeId']);
				$assetId     = $this->_validation->intValidation($formData['assetId']);
				$assetName   = $this->_validation->alNumSpDotHypenValidation($formData['assetName']);
				$serialNo    = $this->_validation->alNumWithoutSpaceValidation($formData['serialNo']);
				$receiveDate = $this->_validation->dateValidation($formData['receiveDate']);
				$returnDate  = $this->_validation->dateValidation($formData['returnDate']);
				
				$assetName['valid'] = $this->_validation->lengthValidation($assetName, 3, 30, true);
				$serialNo['valid']  = $this->_validation->lengthValidation($serialNo, 2, 25, true);
				
				if (!empty($employeeId['value']) && $employeeId['valid'] && !empty($assetName['value']) && $assetName['valid'] &&
					!empty($serialNo['value']) && $serialNo['valid'] && !empty($receiveDate['value']) && $receiveDate['valid'] &&
					/*!empty($returnDate['value']) && $returnDate['valid'] &&*/ (strtotime ($receiveDate['value']) <= strtotime (date('Y-m-d'))) 
					/*(strtotime ($returnDate['value']) <= strtotime (date('Y-m-d')))*/)
				{
					if ($returnDate['value'] && (strtotime ($receiveDate['value']) > strtotime ($returnDate['value'])))
					{
						$this->view->result = array('success' => false, 'msg' => 'Receive Date should be less than or equal to return date', 'type' => 'info');
					}
					else
					{
						$assetData = array('Employee_Id'  => $employeeId['value'],
										   'Asset_Name'   => $assetName['value'],
										   'Serial_No'    => $serialNo['value'],
										   'Receive_Date' => $receiveDate['value'],
										   'Return_Date'  => $returnDate['value']);
						
						$this->view->result = $this->_dbEmployee->updateAsset ($assetData, $assetId['value'], $this->_logEmpId);
					}
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid Data', 'type' => 'info');
				}
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
	 *	Delete Employee assets details
	*/
	public function deleteEmployeeAssetAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-employee-asset', 'json')->initContext();
			
            $assetId = $this->_getParam('assetId', null);
			$assetId = filter_var($assetId, FILTER_SANITIZE_NUMBER_INT);
			
			if (!empty($assetId) && $assetId > 0)
			{
				$this->view->result = $this->_dbEmployee->deleteEmployeeAsset ($assetId);
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
	 *	List employee education details
	*/
	public function listEmployeeEducationAction()
    {
	    $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-employee-education', 'json')->initContext();
			
            $sortField = $this->_getParam('iSortCol_0', null);
			$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
			
			$sortOrder = $this->_getParam('sSortDir_0', null);
			$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
			
			$page = $this->_getParam('iDisplayStart', null);
			$page = isset($page) ? intval($page) : 0;
			$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
			
			$rows = $this->_getParam('iDisplayLength', null);
			$rows = isset($rows) ? intval($rows) : 10;
			$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
			
			$searchAll = $this->_getParam('sSearch', null);
			$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
			
			$employeeId = $this->_getParam('employeeId', null);
			$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
			
			$isJobCandidateView = $this->_getParam('isJobCandidateView', 0);
			$isJobCandidateView = filter_var($isJobCandidateView, FILTER_SANITIZE_NUMBER_INT);
			
			$this->view->result = $this->_dbEmployee->listEmployeeEducation ($page, $rows, $sortField, $sortOrder, $searchAll,
																			 $employeeId, $isJobCandidateView);
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
	 *	add, edit employee education details
	*/
	public function updateEmployeeEducationAction()
    {
	    $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('update-employee-education', 'json')->initContext();
			
            if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				
				$employeeId     = $this->_validation->intValidation($formData['employeeId']);
				$educationId    = $this->_validation->intValidation($formData['educationId']);
				$specialisation = $this->_validation->alHypDotBracketSpValidation($formData['specialisation']);
				$educationType  = $this->_validation->intValidation($formData['educationType']);
				$instituteName  = $this->_validation->alSpDotValidation($formData['instituteName']);
				$university     = $this->_validation->alSpDotValidation($formData['university']);
				$grade          = $this->_validation->alNumWithoutSpaceValidation($formData['grade']);
				
				$specialisation['valid'] = $this->_validation->lengthValidation($specialisation, 2, 30, false);
				$educationType['valid']  = $this->_validation->lengthValidation($educationType, 1, 30, true);
				$instituteName['valid']  = $this->_validation->lengthValidation($instituteName, 3, 50, false);
				$university['valid']     = $this->_validation->lengthValidation($university, 2, 50, false);
				if($grade['valid'])
					$grade['valid']          = $this->_validation->lengthValidation($grade, 0, 10, false);
				
				if (!empty($employeeId['value']) && $employeeId['valid'] && !empty($educationType['value']) && $educationType['valid'] &&
					$specialisation['valid'] && $instituteName['valid'] && $university['valid'] &&
					$formData['yearOfPassing'] <= date('Y') &&
					(empty($formData['percentage']) || ($formData['percentage'] >= 1 && $formData['percentage'] <= 100)) &&
					((!empty($grade['value']) && $grade['valid']) || empty($grade['value'])))
				{
					$educationData = array('Employee_Id'     => $employeeId['value'],
										   'Education_Type'  => $educationType['value'],
										   'Specialisation'  => $specialisation['value'],
										   'Institute_Name'  => $formData['instituteName'],
										   'University'      => $formData['university'],
										   'Year_Of_Passing' => $formData['yearOfPassing'],
										   'Percentage'      => $formData['percentage'],
										   'Grade'           => $grade['value']);
					
					$this->view->result = $this->_dbEmployee->updateEducation ($educationData, $educationId['value'], $this->_logEmpId);
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid Data', 'type' => 'info');
				}
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
	 *	delete employee education
	*/
	public function deleteEmployeeEducationAction()
    {
	    $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-employee-education', 'json')->initContext();
			
            $educationId = $this->_getParam('educationId', null);
			$educationId = filter_var($educationId, FILTER_SANITIZE_NUMBER_INT);
			
			if (!empty($educationId) && $educationId > 0)
			{
				$this->view->result = $this->_dbEmployee->deleteEmployeeEducation ($educationId);
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
	 *	List employee certification details
	*/
	public function listEmployeeCertificationAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-employee-certification', 'json')->initContext();
			
            $sortField = $this->_getParam('iSortCol_0', null);
			$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
			
			$sortOrder = $this->_getParam('sSortDir_0', null);
			$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
			
			$page = $this->_getParam('iDisplayStart', null);
			$page = isset($page) ? intval($page) : 0;
			$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
			
			$rows = $this->_getParam('iDisplayLength', null);
			$rows = isset($rows) ? intval($rows) : 10;
			$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
			
			$searchAll = $this->_getParam('sSearch', null);
			$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
			
			$employeeId = $this->_getParam('employeeId', null);
			$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
			
			$this->view->result = $this->_dbEmployee->listEmployeeCertification ($page, $rows, $sortField, $sortOrder, $searchAll, $employeeId);
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
	 *	add, edit employee certification details
	*/
	public function updateEmployeeCertificationAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('update-employee-certification', 'json')->initContext();
			
            if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				
				$employeeId      = $this->_validation->intValidation($formData['employeeId']);
				$certificationId = $this->_validation->intValidation($formData['certificationId']);
				$certificateName = $this->_validation->certificationValidation($formData['certificateName']);
				$receivedOn      = $this->_validation->dateValidation($formData['receivedOn']);
				$receivedFrom    = $this->_validation->alSpDotValidation($formData['receivedFrom']);
				
				$certificateName['valid'] = $this->_validation->lengthValidation($certificateName, 1, 30, true);
				$receivedFrom['valid']  = $this->_validation->lengthValidation($receivedFrom, 1, 30, true);
				
				if (!empty($employeeId['value']) && $employeeId['valid'] && !empty($certificateName['value']) && $certificateName['valid'] &&
					!empty($receivedOn['value']) && $receivedOn['valid'] && !empty($receivedFrom['value']) && $receivedFrom['valid'] &&
					(strtotime ($receivedOn['value']) <= strtotime (date('Y-m-d'))))
				{
					$certificationData = array('Employee_Id'               => $employeeId['value'],
											   'Certification_Name'        => $certificateName['value'],
											   'Received_Date'             => $receivedOn['value'],
											   'Certificate_Received_From' => $receivedFrom['value']);
					
					$this->view->result = $this->_dbEmployee->updateCertification ($certificationData, $certificationId['value'], $this->_logEmpId);
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid Data', 'type' => 'info');
				}
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
    
	/**
	 *	delete employee certification
	*/
	public function deleteEmployeeCertificationAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-employee-award', 'json')->initContext();
			
            $certificationId = $this->_getParam('certificationId', null);
			$certificationId = filter_var($certificationId, FILTER_SANITIZE_NUMBER_INT);
			
			if (!empty($certificationId) && $certificationId > 0)
			{
				$this->view->result = $this->_dbEmployee->deleteEmployeeCertification ($certificationId);
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
	 *	List employee training details
	*/
	public function listEmployeeTrainingAction()
    {
		$this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('list-employee-training', 'json')->initContext();
			
			$sortField = $this->_getParam('iSortCol_0', null);
			$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
			
			$sortOrder = $this->_getParam('sSortDir_0', null);
			$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
			
			$page = $this->_getParam('iDisplayStart', null);
			$page = isset($page) ? intval($page) : 0;
			$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
			
			$rows = $this->_getParam('iDisplayLength', null);
			$rows = isset($rows) ? intval($rows) : 10;
			$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
			
			$searchAll = $this->_getParam('sSearch', null);
			$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
			
			$employeeId = $this->_getParam('employeeId', null);
			$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
			
			$this->view->result = $this->_dbEmployee->listEmployeeTraining($page, $rows, $sortField, $sortOrder, $searchAll, $employeeId);
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
        
    }
	
	/**
	 *	add, edit employee training details
	*/
    public function updateEmployeeTrainingAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('update-employee-training', 'json')->initContext();
			
            if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				
				$employeeId   = $this->_validation->intValidation($formData['employeeId']);
				$trainingId   = $this->_validation->intValidation($formData['trainingId']);
				$trainingName = $this->_validation->alphaNumSpHyValidation($formData['trainingName']);
				$startDate    = $this->_validation->dateValidation($formData['startDate']);
				$endDate      = $this->_validation->dateValidation($formData['endDate']);
				$duration 	  = $this->_validation->amountValidation($formData['duration']);
				$trainer 	  = $this->_validation->alphaNumSpHyValidation($formData['trainer']);
				$centre  	  = $this->_validation->alphaNumSpHyValidation($formData['centre']);
				
				$trainingName['valid'] = $this->_validation->lengthValidation($trainingName, 3, 30, true);
				$trainer['valid']  = $this->_validation->lengthValidation($trainer, 3, 30, true);
				$centre['valid']  = $this->_validation->lengthValidation($centre, 3, 30, true);
				
				if (!empty($employeeId['value']) && $employeeId['valid'] && !empty($trainingName['value']) && $trainingName['valid'] &&
					!empty($trainingName['value']) && $trainingName['valid'] &&  $startDate['valid']
					 && $endDate['valid'] &&  $duration['valid'] &&
					!empty($trainer['value']) && $trainer['valid'] &&!empty($centre['value']) && $centre['valid'] &&
					(strtotime ($startDate['value']) <= strtotime ($endDate['value']) ))
				{
					$trainingData = array('Employee_Id'         => $employeeId['value'],
										  'Training_Id'         => $trainingId['value'],
										  'Training_Name'       => $trainingName['value'],
										  'Training_Start_Date' => $startDate['value'],
										  'Training_End_Date'   => $endDate['value'],
										  'Training_Duration'   => $duration['value'],
										  'Trainer'             => $trainer['value'],
										  'Center'              => $centre['value']);
				
					$this->view->result = $this->_dbEmployee->updateTraining ($trainingData, $trainingId['value'], $this->_logEmpId);
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid Data', 'type' => 'info');
				}
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }

    /**
	 *	delete employee training
	*/
	public function deleteEmployeeTrainingAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('delete-employee-training', 'json')->initContext();
					
			$trainingId = $this->_getParam('trainingId', null);
			$trainingId = filter_var($trainingId, FILTER_SANITIZE_NUMBER_INT);
			
			if (!empty($trainingId) && $trainingId > 0)
			{
				$this->view->result = $this->_dbEmployee->deleteEmployeeTraining ($trainingId);
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
     
	/**
	 *	List employee awards details
	*/
	public function listEmployeeAwardAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-employee-award', 'json')->initContext();
			
            $sortField = $this->_getParam('iSortCol_0', null);
			$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
			
			$sortOrder = $this->_getParam('sSortDir_0', null);
			$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
			
			$page = $this->_getParam('iDisplayStart', null);
			$page = isset($page) ? intval($page) : 0;
			$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
			
			$rows = $this->_getParam('iDisplayLength', null);
			$rows = isset($rows) ? intval($rows) : 10;
			$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
			
			$searchAll = $this->_getParam('sSearch', null);
			$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
			
			$employeeId = $this->_getParam('employeeId', null);
			$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
			
			$this->view->result = $this->_dbEmployee->listEmployeeAwards ($page, $rows, $sortField, $sortOrder, $searchAll, $employeeId);
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
	 *	add, edit employee awards details
	*/
	public function updateEmployeeAwardAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('update-employee-award', 'json')->initContext();
			
            if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				
				$employeeId   = $this->_validation->intValidation($formData['employeeId']);
				$awardId      = $this->_validation->intValidation($formData['awardId']);
				$awardName    = $this->_validation->alphaNumSpHyValidation($formData['awardName']);
				$receivedOn   = $this->_validation->dateValidation($formData['receivedOn']);
				$receivedFrom = $this->_validation->alSpDotValidation($formData['receivedFrom']);
				$receivedFor  = $this->_validation->alSpDotValidation($formData['receivedFor']);
				
				$awardName['valid'] = $this->_validation->lengthValidation($awardName, 3, 30, true);
				$receivedFrom['valid']  = $this->_validation->lengthValidation($receivedFrom, 3, 30, true);
				$receivedFor['valid']  = $this->_validation->lengthValidation($receivedFor, 3, 30, true);
				
				if (!empty($employeeId['value']) && $employeeId['valid'] && !empty($awardName['value']) && $awardName['valid'] &&
					!empty($receivedOn['value']) && $receivedOn['valid'] && !empty($receivedFrom['value']) && $receivedFrom['valid'] &&
					!empty($receivedFor['value']) && $receivedFor['valid'] && (strtotime ($receivedOn['value']) <= strtotime (date('Y-m-d'))))
				{
					$awardData = array('Employee_Id'   => $employeeId['value'],
									   'Award_Name'    => $awardName['value'],
									   'Received_On'   => $receivedOn['value'],
									   'Received_From' => $receivedFrom['value'],
									   'Received_For'  => $receivedFor['value']);
					
					$this->view->result = $this->_dbEmployee->updateAward ($awardData, $awardId['value'], $this->_logEmpId);
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid Data', 'type' => 'info');
				}
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
	 *	delete employee awards details
	*/
	public function deleteEmployeeAwardAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-employee-award', 'json')->initContext();
			
            $awardId = $this->_getParam('awardId', null);
			$awardId = filter_var($awardId, FILTER_SANITIZE_NUMBER_INT);
			
			if (!empty($awardId) && $awardId > 0)
			{
				$this->view->result = $this->_dbEmployee->deleteEmployeeAward ($awardId);
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
	 *	List employee insurance detials
	*/
	public function listEmployeeInsuranceAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-employee-insurance', 'json')->initContext();
			
            $sortField = $this->_getParam('iSortCol_0', null);
			$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
			
			$sortOrder = $this->_getParam('sSortDir_0', null);
			$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
			
			$page = $this->_getParam('iDisplayStart', null);
			$page = isset($page) ? intval($page) : 0;
			$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
			
			$rows = $this->_getParam('iDisplayLength', null);
			$rows = isset($rows) ? intval($rows) : 10;
			$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
			
			$searchAll = $this->_getParam('sSearch', null);
			$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
			
			$employeeId = $this->_getParam('employeeId', null);
			$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
			
			$this->view->result = $this->_dbEmployee->listEmployeeInsurances ($page, $rows, $sortField, $sortOrder, $searchAll, $employeeId);
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
	 *	add, update employee insuance details
	*/
	public function updateEmployeeInsuranceAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('update-employee-insurance', 'json')->initContext();
			
            if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				
				$employeeId      = $this->_validation->intValidation($formData['employeeId']);
				$policyId        = $this->_validation->intValidation($formData['policyId']);
				$insuranceTypeId = $this->_validation->intValidation($formData['insuranceTypeId']);

				$policyNo['value'] = $this->_validation->commonFilters($formData['policyNo']);				
				$policyNo['valid']  = $this->_validation->lengthValidation($policyNo, 1, 30, true);
				
				if (!empty($employeeId['value']) && $employeeId['valid'] && !empty($insuranceTypeId['value']) && $insuranceTypeId['valid'] &&
					!empty($policyNo['value']) && $policyNo['valid'])
				{
					$policyData = array('Employee_Id'      => $employeeId['value'],
										'InsuranceType_Id' => $insuranceTypeId['value'],
										'Policy_No'        => htmlentities($policyNo['value']),
										'Insurance_Type'   => $formData['insuranceType'] == 1 ? 'insurance' :'fixedHealthInsurance' );
					
					$this->view->result = $this->_dbEmployee->updateInsurancePolicy ($policyData, $policyId['value'], $this->_logEmpId);
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid Data', 'type' => 'info');
				}
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
	 *	Delete employee insurance details
	*/
	public function deleteEmployeeInsuranceAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-employee-insurance', 'json')->initContext();
			
            $policyId = $this->_getParam('policyId', null);
			$policyId = filter_var($policyId, FILTER_SANITIZE_NUMBER_INT);
			
			$employeeId = $this->_getParam('employeeId', null);
			$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
			
			$designationId = $this->_getParam('designationId', null);
			$designationId = filter_var($designationId, FILTER_SANITIZE_NUMBER_INT);
			
			if (!empty($policyId) && $policyId > 0)
			{
				$this->view->result = $this->_dbEmployee->deleteEmployeeInsurance ($policyId);
			}
			else if (!empty($employeeId) && $employeeId > 0 && !empty($designationId) && $designationId > 0)
			{
				$insuranceTypeData = array_keys($this->_dbInsurance->getInsTypesByDesignation ($designationId));
				
				$this->view->result = $this->_dbEmployee->deleteEmployeeInsuranceByDesingaiton ($employeeId, $insuranceTypeData);
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
     * Get designation details to show in a grid
     */
    public function listDesignationAction ()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-designation', 'json')->initContext();
			
            if ($this->_designationEmpAccess['View'] == 1 )
            {
				$sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
                
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$designationName = $this->_getParam('sSearch_0', null);
				$designationName = filter_var($designationName, FILTER_SANITIZE_STRIPPED);
				
				$gradeType = $this->_getParam('sSearch_1', null);
				$gradeType = filter_var($gradeType, FILTER_SANITIZE_STRIPPED);
				
				$probationDaysStart = $this->_getParam('sSearch_2', null);
				$probationDaysStart = filter_var($probationDaysStart, FILTER_SANITIZE_NUMBER_INT);
				
				$probationDaysEnd = $this->_getParam('sSearch_3', null);
				$probationDaysEnd = filter_var($probationDaysEnd, FILTER_SANITIZE_NUMBER_INT);

				$employeeConfirmation = $this->_getParam('sSearch_4', null);
				$employeeConfirmation = filter_var($employeeConfirmation, FILTER_SANITIZE_STRIPPED);

				$attendanceEnforced = $this->_getParam('sSearch_5', null);
				$attendanceEnforced = filter_var($attendanceEnforced, FILTER_SANITIZE_NUMBER_INT);
				
				$attendanceEnforcedGeoLocation = $this->_getParam('sSearch_6', null);
				$attendanceEnforcedGeoLocation = filter_var($attendanceEnforcedGeoLocation, FILTER_SANITIZE_NUMBER_INT);

				$status = $this->_getParam('sSearch_7', null);
				$status = filter_var($status, FILTER_SANITIZE_STRIPPED);

				$noticePeriodPayByEmployer = $this->_getParam('sSearch_8', null);
				$noticePeriodPayByEmployer = filter_var($noticePeriodPayByEmployer, FILTER_SANITIZE_STRIPPED);

				$noticePeriodPayByEmployee = $this->_getParam('sSearch_9', null);
				$noticePeriodPayByEmployee = filter_var($noticePeriodPayByEmployee, FILTER_SANITIZE_STRIPPED);

				$searchArr = array( 'designationName'    	=> $designationName,
								    'gradeType'         	=> $gradeType,
									'probationDaysStart' 	=> $probationDaysStart,
									'probationDaysEnd'   	=> $probationDaysEnd,
									'employeeConfirmation'	=> $employeeConfirmation,
									'attendanceEnforce'	    => $attendanceEnforced,
									'attendanceEnforcedGeoLocation' => $attendanceEnforcedGeoLocation,
									'noticePeriodPayByEmployer' => $noticePeriodPayByEmployer,
									'noticePeriodPayByEmployee' => $noticePeriodPayByEmployee,
								    'status' => $status);
				$this->view->result = $this->_dbDesignation->listDesignation ($page, $rows, $sortField, $sortOrder, $searchAll,
															$searchArr, $this->_roleSuperAdminAccessRights['Employee']['Optional_Choice']);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
     * Update designation
     */
    public function updateDesignationAction ()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('update-designation', 'json')->initContext();
			
            $designationId = $this->_getParam('designationId', null);
            $designationId  =  filter_var($designationId, FILTER_SANITIZE_NUMBER_INT);
			
			if ((empty($designationId) && $this->_designationEmpAccess['Add'] == 1) ||
				(!empty($designationId) && $this->_designationEmpAccess['Update'] == 1))
			{
				if ($this->getRequest()->isPost())
				{
					$formData = $this->getRequest()->getPost();
					
					$designationName = $this->_validation->alphaNumSpCDotHySlashValidation($formData['designationName']);
					$designationName['valid'] = $this->_validation->lengthValidation($designationName,2,75, true);
					
					$probationDays = $this->_validation->intValidation($formData['probationDays']);
					$probationDays = $this->_validation->minMaxValueValidation($formData['probationDays'],0,2000);						
					
					$employeeConfirmation = $this->_validation->alphaNumSpCDotHySlashValidation($formData['employeeConfirmation']);
					
					$attendanceEnforcedPayment = $this->_getParam("attendanceEnforcedPayment", null);					
					$attendanceEnforcedPayment = $this->_validation->checkboxValidation($attendanceEnforcedPayment);
				
					$description = $this->_validation->alphaNumSpCDotHySlashNewLineValidation($formData['description']);
					$description['valid'] = $this->_validation->lengthValidation($description, 5, 600, false);

					$attendanceEnforcedGeoLocation = $this->_getParam("attendanceEnforcedGeoLocation", null);					
					$attendanceEnforcedGeoLocation = $this->_validation->checkboxValidation($attendanceEnforcedGeoLocation);
					
					$noticePeriodDaysWithinProbation = $this->_validation->intValidation($formData['noticePeriodDaysWithinProbation']);
					if($noticePeriodDaysWithinProbation['valid'])
					{
						$noticePeriodDaysWithinProbation = $this->_validation->minMaxValueValidation($formData['noticePeriodDaysWithinProbation'],0,183);	
					}

					$noticePeriodDaysAfterProbation = $this->_validation->intValidation($formData['noticePeriodDaysAfterProbation']);
					if($noticePeriodDaysAfterProbation['valid'])
					{
						$noticePeriodDaysAfterProbation = $this->_validation->minMaxValueValidation($formData['noticePeriodDaysAfterProbation'],0,183);	
					}

					$noticePeriodPayByEmployer=$formData['noticePeriodPayByEmployer'];
					$noticePeriodPayByEmployee=$formData['noticePeriodPayByEmployee'];
					$status = $this->_validation->alphaValidation($formData['status']);

                    $oldStatus = $this->_getParam('oldStatus', '');
					$oldStatus = (isset($oldStatus) &&!empty($oldStatus)) ? $oldStatus : '';

					$noticePeriodPayStaticValues = array('Yes','No');
					if (!empty($designationName['value']) && $designationName['valid'] && !empty($employeeConfirmation['value']) && $employeeConfirmation['valid'] && !empty($formData['gradeId']) &&
						 $description['valid']  && ((!empty($probationDays['value']) && $probationDays['valid']) || empty($probationDays['value'])) 
						 && $noticePeriodDaysWithinProbation['valid'] && $noticePeriodDaysAfterProbation['valid']
						 && ($status['valid'] && !empty($status['value'])) && in_array($noticePeriodPayByEmployer,$noticePeriodPayStaticValues)
						 && in_array($noticePeriodPayByEmployee,$noticePeriodPayStaticValues))
					{
						$designationArray = array('Designation_Id'   		=> $designationId,
												  'Designation_Name' 		=> $designationName['value'],
												  'Grade_Id'         		=> $formData['gradeId'],
												  'Probation_Days'   		=> $probationDays['value'],
												  'Employee_Confirmation' 	=> $employeeConfirmation['value'],
												  'Attendance_Enforced_Payment'=> $attendanceEnforcedPayment['value'],
												  'Attendance_Enforced_GeoLocation'=> $attendanceEnforcedGeoLocation['value'],
												  'Notice_Period_Pay_By_Employer' => $noticePeriodPayByEmployer,
												  'Notice_Period_Pay_By_Employee' => $noticePeriodPayByEmployee,
												  'Notice_Period_Days_Within_Probation' => $noticePeriodDaysWithinProbation['value'],
												  'Notice_Period_Days_After_Probation'  => $noticePeriodDaysAfterProbation['value'],
												  'Designation_Status' => $status['value'],
												  'Description'      		=> $description['value']);

						$customFormName = $this->_ehrTables->getCustomForms($this->_formNameB);
						
						$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameB);

						$this->view->result = $this->_dbDesignation->updateDesignation($designationArray, $this->_logEmpId, $formName,$oldStatus);
					}
					else
					{
						$this->view->result = array('success' => false, 'msg' => 'Invalid Data', 'type' => 'warning');
					}
				}
			}
			else
			{
				$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type' => 'info');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }

	/**
     * Delete designation
     */
    public function deleteDesignationAction ()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-designation', 'json')->initContext();
				
            if ($this->_designationEmpAccess['Delete'] == 1)
            {
				$designationId = $this->_getParam('designationId', null);
				$designationId = filter_var($designationId, FILTER_SANITIZE_NUMBER_INT);
				
				if (!empty($designationId) && $designationId > 0)
				{
					$customFormName = $this->_ehrTables->getCustomForms($this->_formNameB);
					$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameB);
						
					$this->view->result = $this->_dbDesignation->deleteDesignation ($designationId, $this->_logEmpId, $formName);
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
				}    
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Sorry, Access denied', 'type' => 'danger');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
     * to display all the grade records in the data grid
     */
    public function listGradeAction ()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-grade', 'json')->initContext();
            
			if ($this->_gradeEmployeeAccess['View'] == 1)
            {
				$sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
                
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$grade = $this->_getParam('sSearch_0', null);
				$grade = filter_var($grade, FILTER_SANITIZE_STRIPPED);
				
				$minExpBeginRange = $this->_getParam('sSearch_1', null);
				$minExpBeginRange = filter_var($minExpBeginRange, FILTER_SANITIZE_STRIPPED);
				
				$minExpEndRange = $this->_getParam('sSearch_2', null);
				$minExpEndRange = filter_var($minExpEndRange, FILTER_SANITIZE_STRIPPED);
				
				$maxExpBeginRange = $this->_getParam('sSearch_3', null);
				$maxExpBeginRange = filter_var($maxExpBeginRange, FILTER_SANITIZE_STRIPPED);
				
				$maxExpEndRange = $this->_getParam('sSearch_4', null);
				$maxExpEndRange = filter_var($maxExpEndRange, FILTER_SANITIZE_STRIPPED);
				
				$minSalBeginRange = $this->_getParam('sSearch_5', null);
				$minSalBeginRange = filter_var($minSalBeginRange, FILTER_SANITIZE_STRIPPED);
				
				$minSalEndRange = $this->_getParam('sSearch_6', null);
				$minSalEndRange = filter_var($minSalEndRange, FILTER_SANITIZE_STRIPPED);
				
				$maxSalBeginRange = $this->_getParam('sSearch_7', null);
				$maxSalBeginRange = filter_var($maxSalBeginRange, FILTER_SANITIZE_STRIPPED);
				
				$maxSalEndRange = $this->_getParam('sSearch_8', null);
				$maxSalEndRange = filter_var($maxSalEndRange, FILTER_SANITIZE_STRIPPED);
				
				$parentGrade = $this->_getParam('sSearch_9', null);
				$parentGrade = filter_var($parentGrade, FILTER_SANITIZE_STRIPPED);
				
				$searchArr = array( 'grade'            => $grade,
								    'minExpBeginRange' => $minExpBeginRange,
									'minExpEndRange'   => $minExpEndRange,
									'maxExpBeginRange' => $maxExpBeginRange,
									'maxExpEndRange'   => $maxExpEndRange,
									'minSalBeginRange' => $minSalBeginRange,
									'minSalEndRange'   => $minSalEndRange,
									'maxSalBeginRange' => $maxSalBeginRange,
									'maxSalEndRange'   => $maxSalEndRange,
									'parentGrade'      => $parentGrade);
				
				$this->view->result = $this->_dbGrade->listEmployeeGrade ($page, $rows, $sortField, $sortOrder, $searchAll, $searchArr,
																		  $this->_logEmpId);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
     * to update the employee grade based on the access rights
     */
	public function updateGradeAction ()
    {
	    $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('update-grade', 'json')->initContext();
			
            $gradeId = $this->_getParam('gradeId', null);
            $gradeId = filter_var($gradeId, FILTER_SANITIZE_NUMBER_INT);
            
			if ((empty($gradeId) && $this->_gradeEmployeeAccess['Add'] == 1) ||
				(!empty($gradeId) && $this->_gradeEmployeeAccess['Update'] == 1))
			{
				if ($this->getRequest()->isPost())
				{
					$formData = $this->getRequest()->getPost();
					
					$gradeName = $this->_validation->alphaNumSpCDotHySlashValidation($formData['grade']);
                    $gradeName['valid'] = $this->_validation->lengthValidation($gradeName, 1, 50, true);
                    
                    $minExp = $this->_validation->percentageValidation($formData['minExperience']);
                    
                    $maxExp = $this->_validation->percentageValidation($formData['maxExperience']);
                    
                    $minHrlyWages = $this->_validation->amountValidation($formData['minHourlyWages']);
					$minHrlyWages['valid'] = $this->_validation->minMaxValueValidation($minHrlyWages,1,9999999);
                    
                    $maxHrlyWages = $this->_validation->amountValidation($formData['maxHourlyWages']);
                    $maxHrlyWages['valid'] = $this->_validation->minMaxValueValidation($maxHrlyWages,1,9999999);
					
                    $minOTHrlyWages = $this->_validation->amountValidation($formData['minOvertimeWages']);
                    $minOTHrlyWages['valid'] = $this->_validation->minMaxValueValidation($minOTHrlyWages,1,9999999);
					
                    $maxOTHrlyWages = $this->_validation->amountValidation($formData['maxOvertimeWages']);
                    $maxOTHrlyWages['valid'] = $this->_validation->minMaxValueValidation($maxOTHrlyWages,1,9999999);
					
                    $minAnnualSalary = $this->_validation->amountValidation($formData['minAnnualSalary']);
                    $minAnnualSalary['valid'] = $this->_validation->minMaxValueValidation($minAnnualSalary,1,9999999999999);
					
                    $maxAnnualSalary = $this->_validation->amountValidation($formData['maxAnnualSalary']);
					$maxAnnualSalary['valid'] = $this->_validation->minMaxValueValidation($maxAnnualSalary,1,9999999999999);
					
					$overtimeEligiblity = $this->_validation->intValidation($formData['overtimeEligiblity']);
					$overtimeAllocation = $this->_validation->alphaNumSpHyValidation($formData['overtimeAllocation']);
					$wageFactor = $this->_validation->percentageValidation($formData['wageFactor']);
					
                    $overtimeFixedAmt = $this->_validation->amountValidation($formData['overtimeFixedAmount']);
                    $overtimeFixedAmt['valid']= $this->_validation->minMaxValueValidation($overtimeFixedAmt,0,99999999);
					
					$description = $this->_validation->alphaNumSpCDotHySlashNewLineValidation($formData['description']);
					$description['valid'] = $this->_validation->lengthValidation($description, 5, 600, false);
					
					if (($formData['minExperience'] <= $formData['maxExperience']) && !empty($formData['grade']) && $gradeName['valid'] && ($formData['minHourlyWages'] <= $formData['maxHourlyWages']) &&
						($formData['minOvertimeWages'] <= $formData['maxOvertimeWages']) && ($formData['minAnnualSalary'] <= $formData['maxAnnualSalary']) &&
                        (empty($formData['minExperience']) || ($minExp['valid'] && $formData['minExperience'] >= 0 &&
						$formData['minExperience'] <= 99)) && (empty($formData['maxExperience']) || ($maxExp['valid'] && $formData['maxExperience'] >= 0
						&& $formData['maxExperience'] <= 99)) && $minHrlyWages['valid'] && $maxHrlyWages['valid'] && $minOTHrlyWages['valid'] &&
                         $maxOTHrlyWages['valid'] && $minAnnualSalary['valid'] && $maxAnnualSalary['valid'] && $overtimeFixedAmt['valid'] && $description['valid'])
                    {
						if(!empty($overtimeFixedAmt['value']))
						{
							$overtimeFixedAmt['value'] = $overtimeFixedAmt['value'];
						}
						else 
						{
							$overtimeFixedAmt['value'] = new Zend_Db_Expr('NULL');
						}
                        $empGradeArray = array('Grade_Id'            => $gradeId,
											   'Grade'               => $gradeName['value'],
											   'Min_Experience'      => $minExp['value'],
											   'Max_Experience'      => $maxExp['value'],
											   'Eligible_Overtime'   => $overtimeEligiblity['value'],
											   'Overtime_Allocation' => $overtimeAllocation['value'],
											   'Overtime_Wage_Index' => $wageFactor['value'],
											   'OvertimeFixedAmount' => $overtimeFixedAmt['value'],
											   'Min_HourlyWages'     => $minHrlyWages['value'],
											   'Max_HourlyWages'     => $maxHrlyWages['value'],
											   'Min_OvertimeWages'   => $minOTHrlyWages['value'],
											   'Max_OvertimeWages'   => $maxOTHrlyWages['value'],
											   'Min_AnnualSalary'    => $minAnnualSalary['value'],
											   'Max_AnnualSalary'    => $maxAnnualSalary['value'],
											   'Description'         => $description['value']);
                        
						$customFormName = $this->_ehrTables->getCustomForms($this->_formNameC);
						$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameC);
						
						$this->view->result = $this->_dbGrade->updateGrade ($empGradeArray, $formData['parentGradeId'], $this->_logEmpId, $formName);
					}
					else
					{
						$this->view->result = array('success' => false, 'msg' => 'Invalid Data', 'type' => 'warning');
					}
				}
			}
            else
            {
				$this->view->result = array('success' => false, 'msg' => 'Sorry, Access Denied', 'type' => 'info');
            }
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
     * to delete the employee Grade based on the access rights
     */
	public function deleteGradeAction ()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-grade', 'json')->initContext();
			
			if ($this->_gradeEmployeeAccess['Delete'] == 1)
            {
				$gradeId = $this->_getParam('gradeId', null);
				$gradeId = filter_var($gradeId, FILTER_SANITIZE_NUMBER_INT);
				
				if (!empty($gradeId) && $gradeId > 0)
				{
					$customFormName = $this->_ehrTables->getCustomForms($this->_formNameC);
					$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameC);
					
					
					$this->view->result = $this->_dbGrade->deleteGrade ($gradeId, $this->_logEmpId, $formName);
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
				}    
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Sorry, Access denied', 'type' => 'danger');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
     *loads the all the employee types to the grid
     */
    public function listEmployeeTypeAction ()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-employee-type', 'json')->initContext();
			
            if ($this->_empTypeEmployeeAccess['View'] == 1)
            {
				$sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);				
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
                
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);

				$empType = $this->_getParam('sSearch_0', null);
				$empType = filter_var($empType, FILTER_SANITIZE_STRIPPED);

				$benefitsApplicable = $this->_getParam('sSearch_1', null);
				$benefitsApplicable = filter_var($benefitsApplicable,  FILTER_SANITIZE_NUMBER_INT);

				$holidayEligiblity = $this->_getParam('sSearch_2', null);
				$holidayEligiblity = filter_var($holidayEligiblity, FILTER_SANITIZE_NUMBER_INT);

				$salaryCalcDays = $this->_getParam('sSearch_3', null);
				$salaryCalcDays = filter_var($salaryCalcDays,  FILTER_SANITIZE_NUMBER_INT);

				$excludeBreakHours = $this->_getParam('sSearch_4', null);
				$excludeBreakHours = filter_var($excludeBreakHours,  FILTER_SANITIZE_NUMBER_INT);

				$attendanceProcessStatus = $this->_getParam('sSearch_5', null);
				$attendanceProcessStatus = filter_var($attendanceProcessStatus,  FILTER_SANITIZE_STRIPPED);

				$enableWorkPlace = $this->_getParam('sSearch_6', null);
				$enableWorkPlace = filter_var($enableWorkPlace,  FILTER_SANITIZE_NUMBER_INT);

				$status = $this->_getParam('sSearch_7', null);
				$status = filter_var($status, FILTER_SANITIZE_STRIPPED);

				$searchArr = array( 'empType'   => $empType,
									 'benefitsApplicable' => $benefitsApplicable,
									 'holidayEligiblity'=>$holidayEligiblity,
									 'salaryCalcDays'=>$salaryCalcDays,
									 'excludeBreakHours' => $excludeBreakHours,
									 'enableWorkPlace' => $enableWorkPlace,
									 'attendanceProcessStatus'=>$attendanceProcessStatus,
									 'status' => $status
     	    					 );
								  
				
				$this->view->result = $this->_dbEmpType->listEmployeeType ($page, $rows, $sortField, $sortOrder, $searchArr,$searchAll, $this->_logEmpId);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
     * to update the employee type based on the access rights
     */
	public function updateEmployeeTypeAction ()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('update-employee-type', 'html')->initContext();
			
            $employeeTypeId = $this->_getParam('employeeTypeId', null);
            $employeeTypeId = filter_var($employeeTypeId, FILTER_SANITIZE_NUMBER_INT);
            
            if ((empty($employeeTypeId) && $this->_empTypeEmployeeAccess['Add'] == 1) ||
				(!empty($employeeTypeId) && $this->_empTypeEmployeeAccess['Update'] == 1))
			{
				if ($this->getRequest()->isPost())
				{
					$formData = $this->getRequest()->getPost();
				
					$employeeType = $this->_validation->alphaNumSpCDotHySlashValidation($formData['employeeType']);
					$employeeType['valid'] = $this->_validation->lengthValidation($employeeType, 3, 50, true);
				
					$benefitsApplicable = $this->_validation->intValidation($formData['benefitsApplicable']);
					
					$holidayEligiblity = $this->_validation->intValidation($formData['holidayEligiblity']);
					
					$displayTotalHoursInMinutes = $this->_validation->intValidation($formData['displayTotalHoursInMinutes']);
					
					$excludeBreakHours = $this->_validation->intValidation($formData['excludeBreakHours']);

					$enableWorkPlace = $this->_validation->intValidation($formData['enableWorkPlace']);

					$workSchedule = $this->_validation->alphaNumSpHyValidation($formData['workSchedule']);

					$dashboardAttendanceAutoApproval = $this->_validation->onlyLetterSpaceValidation($formData['dashboardAttendanceAutoApproval']);

					$salaryCalculationDays = $this->_validation->intValidation($formData['salaryCalculationDays']);

					$fixedDaysOfMonth = $this->_validation->intValidation($formData['fixedDaysOfMonth']);

					$compOffCalculationDays = $this->_validation->intValidation($formData['compOffCalculationDays']);

					$compOffFixedDays = $this->_validation->intValidation($formData['compOffFixedDays']);

					$attendanceProcessStatus = $this->_validation->alphaNumSpHyValidation($formData['attendanceProcessStatus']);

					$workPlaceIds = $formData['workPlaceId'];
					
					$employeeTypeStatus = $this->_validation->alphaValidation($formData['employeeTypeStatus']);

					$employeeTypeOldStatus = $this->_getParam('employeeTypeOldStatus', '');
					$employeeTypeOldStatus = (isset($employeeTypeOldStatus) &&!empty($employeeTypeOldStatus)) ? $employeeTypeOldStatus : '';

					$description = $this->_validation->alphaNumSpCDotHySlashNewLineValidation($formData['description']);
					$description['valid'] = $this->_validation->lengthValidation($description, 5, 500, false);

					if (!empty($employeeType['value']) && $employeeType['valid'] && $dashboardAttendanceAutoApproval['valid'] &&
						$benefitsApplicable['valid'] && $holidayEligiblity['valid'] && $displayTotalHoursInMinutes['valid'] &&
						$excludeBreakHours['valid'] && $enableWorkPlace['valid'] && !empty($workSchedule['value']) && $workSchedule['valid'] &&
						$salaryCalculationDays['valid'] && $fixedDaysOfMonth['valid'] && !empty($attendanceProcessStatus['value']) && $attendanceProcessStatus['valid'] &&  $description['valid']
						&&(($dashboardAttendanceAutoApproval['value']=='Automatically For Selected Work Place'&&!empty($workPlaceIds)) || (in_array($dashboardAttendanceAutoApproval['value'], array('Automatically','Manually'))&&empty($workPlaceIds)))
						&&(($workSchedule['value']=='Shift Roster' && ($salaryCalculationDays['value']==1 || $salaryCalculationDays['value']==3 ) ) || ($workSchedule['value']=='Employee Level' && in_array($salaryCalculationDays['value'], array(0,1,2,3))))
						&&(($compOffCalculationDays['value']==3 && ($compOffFixedDays['value'] >=25 && $compOffFixedDays['value'] <= 31)) || (in_array($compOffCalculationDays['value'], array(0,1,2)) && empty($compOffFixedDays['value'])))
						&& $employeeTypeStatus['valid'] && !empty($employeeTypeStatus['value']))
					{
						
						$employeeTypeArr = array('EmpType_Id'          => $employeeTypeId,
												 'Employee_Type'       => $employeeType['value'],
												 'Benefits_Applicable' => $benefitsApplicable['value'],
												 'Holiday_Eligiblity'  => $holidayEligiblity['value'],
												 'Salary_Calc_Days'    => $salaryCalculationDays['value'],
												 'Fixed_Days'	       => $fixedDaysOfMonth['value'],
												 'Comp_Off_Days'       => $compOffCalculationDays['value'],
												 'Comp_Off_Fixed_Days' => $compOffFixedDays['value'],
												 'Display_Total_hours_In_Minutes'	 => $displayTotalHoursInMinutes['value'],
												 'Exclude_Break_Hours' => $excludeBreakHours['value'],	
												 'Enable_Work_Place' => $enableWorkPlace['value'],	
												 'Approve_Dashboard_Attendance' => $dashboardAttendanceAutoApproval['value'],
												 'Work_Schedule'	   => $workSchedule['value'],
												 'Attendance_Process_Status'=> $attendanceProcessStatus['value'],
												 'EmployeeType_Status'	=> $employeeTypeStatus['value'],
												 'Description'         => $description['value']);
						
						$customFormName = $this->_ehrTables->getCustomForms($this->_formNameD);
						$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameD);
						
						$this->view->result = $this->_dbEmpType->updateEmployeeType ($employeeTypeArr, $this->_logEmpId, $formName,$workPlaceIds, $employeeTypeOldStatus);
					}
					else
					{
						$this->view->result = array('success' => false, 'msg' => 'Invalid Data', 'type' => 'warning');
					}
				}
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Access denied', 'type' => 'danger');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
     * to delete the employee type based on the access rights
     */
	public function deleteEmployeeTypeAction ()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-employee-type', 'html')->initContext();
			
            if ($this->_empTypeEmployeeAccess['Delete'] == 1 )
            {
				$employeeTypeId = $this->_getParam('employeeTypeId', null);
				$employeeTypeId = filter_var($employeeTypeId, FILTER_SANITIZE_NUMBER_INT);
				
				if (!empty($employeeTypeId) && $employeeTypeId > 0)
				{
					$customFormName = $this->_ehrTables->getCustomForms($this->_formNameD);
					$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameD);
						
					$this->view->result = $this->_dbEmpType->deleteEmployeeType ($employeeTypeId, $this->_logEmpId, $formName);
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
				}    
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Sorry, Access denied', 'type' => 'danger');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }    
   
    /**
     * Get designation details to view
     */
    public function viewDesignationAction ()
    {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $this->_helper->layout->setLayout('tab_layout');

            $designationId = $this->_getParam('_dId', null);
            $designationId = filter_var($designationId, FILTER_SANITIZE_NUMBER_INT);

            if (!empty($designationId) && $this->_checkSession)
            {
                if ($this->_designationEmpAccess['View'] == 1)
                {
                    //to fetch the record of the designationId obtained from the grid
                    $designation = $this->_dbDesignation->viewDesignation($designationId);
					
                    $this->view->designation = $designation;
                }
                else
                {
                    $this->view->access = 'Sorry, Access Denied...';
                }
            }
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }

    /**
     * Add designation
     */
    public function addDesignationAction ()
    {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $this->_helper->layout->setLayout('tab_layout');
            if ($this->_checkSession)
            {
                if ($this->_designationEmpAccess['Add'] == 1 )
                {
                    $designationForm = new Employees_Form_Designation();
                    $this->view->designationForm = $designationForm;

                    //to list the grades in dropdown
                    $grades = $this->_dbGrade->getGrade();
                    $designationForm->getElement('Grade')->addMultiOptions(array('' => '--Select--'));
                    if (!empty($grades))
                    {
                        $designationForm->getElement('Grade')->addMultiOptions($grades);
                    }

                    if ($this->getRequest()->isPost())
                    {
                    	$formData = $this->getRequest()->getPost();
                    	if ($this->_isMobile)//mobile
                    	{
                    		$this->view->isMobile = true;
                    		$formData = $this->_hrappMobile->updateDetailsForm($formData,$designationForm,$this->_helper->layout);
                    	}
                    	if ($designationForm->isValid($formData))
                    	{
                    
                            $formdata = $designationForm->getValues();

                            //to check whether the designation name already exists or not
                            $exists = $this->_dbDesignation->designationExists($formdata['Designation_Name']);

                            if (count($exists) == 0)
                            {
                                // to insert the posted form values into table
                                $inserted = $this->_dbDesignation->addDesignation($formdata, $this->_logEmpId);

                                if ($inserted)
                                {
                                    //to display the inserted designation details in view
                                    $employeeName = $this->_dbPersonal->employeeName($this->_logEmpId);
                                    $inserted['EmployeeName'] = $employeeName['Employee_Name'];
                                    $inserted['Added_Date'] = date($this->_orgDateFormat['php'].' \a\t H:i:s');
                                    $this->view->designation = $inserted;
                                    $this->view->grade = $grades[$formdata['Grade']];
                                    $this->view->message = array('a' => 'Designation added successfully!','b' => 'success','c' => '','d' => '300','e' => 'designation_grid');
                                }
                                else
                                {
                                    $this->view->message = array('a' => 'Error in Insertion','b' => 'fatal','c' => 'errboxid','d' => '200');
                                }
                            }
                            else
                            {
                                //to display the message if the designation already exists in table
                                $this->view->message = array('a' => 'Designation already exists','b' => 'info','c' => 'infoboxid','d' => '250');
                            }
                        }
                        else
                        {
                            $this->view->message = array('a' => 'Invalid Data','b' => 'warning','c' => 'warningboxid','d' => '150');
                        }

                    }
                }
                else
                {
                    $this->view->access = 'Sorry, Access Denied...';
                }
            }
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }

    

    /**
     * to view grade details
     *
     */
    public function viewGradeAction ()
    {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $this->_helper->layout->setLayout('tab_layout');

            $gradeId = $this->_getParam('gradeid', null);
            $gradeId = filter_var($gradeId, FILTER_SANITIZE_NUMBER_INT);

            if (!empty($gradeId) && $this->_checkSession)
            {
                if ($this->_gradeEmployeeAccess['View'] == 1 )
                {
                    //to fetch the record of the gradeid obtained from the grid
                    $grade = $this->_dbGrade->viewGrade($gradeId);

                    $this->view->grade = $grade;
                }
                else
                {
                    $this->view->access = 'Sorry, Access Denied...';
                }
            }
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }

    }

    

    /**
     * Update designation roles
     */
    public function designationRolesAction ()
    {
        $this->_helper->layout->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
            $this->_helper->layout->setLayout('tab_layout');
			
            $designationId = $this->_getParam('_dId', null);
            $designationId  =  filter_var($designationId, FILTER_SANITIZE_NUMBER_INT);
            
			if (!empty($designationId) && $this->_checkSession)
            {
                if ($this->_rolesAccessRights['Employee']['Update'] == 1 )
                {
                    $forms = $this->_dbModules->mainForms();
                    $modules = $this->_dbModules->getModules();
                    $subForms = $this->_dbModules->subForms();
					
                    //to fetch the record of the designationid obtained from the grid
                    $designationRow = $this->_dbDesignation->viewDesignation($designationId);
					
                    if (empty($designationRow))
                    {
                        $rolesForm = new Employees_Form_AccessRights();
                        $rolesForm->setForms($modules,$forms,$subForms,'', '');
                        $rolesForm = $rolesForm->start('');
                        $this->view->message = array('a' => 'Record doesn\'t exists!','b' => 'info','c' => 'infoboxid','d' => '220');
                        $this->view->accessRightsForm = $rolesForm;
                    }
                    else
                    {
                        $recordLimit = $this->_dbAccessRights->checkOpenedRecordLimit($this->_logEmpId);
						
                        if (!$recordLimit[0])
                        {
                            $this->view->access = 'You don\'t have rights to edit more than $recordLimit[1] records.';
                        }
                        else
                        {
                            $checkFormLock = $this->_dbAccessRights->checkLockFlag($designationId, $this->_ehrTables->roles, 'Designation_Id');
                            
							if ($checkFormLock == $this->_logEmpId || $checkFormLock == 0)
                            {
                                $setDesLock = $this->_dbAccessRights->setLockFlag($this->_logEmpId, $designationId, $this->_ehrTables->roles, 'Designation_Id');
                                
								if ($setDesLock)
                                {
                                    $formRoles = $this->_dbAccessRights->formRoles($designationId);
									$subformRoles = $this->_dbAccessRights->subFormRoles($designationId);
                                    
									$rolesForm = new Employees_Form_AccessRights();
                                    $rolesForm->setForms($modules,$forms,$subForms,$formRoles, $subformRoles);
                                    $rolesForm = $rolesForm->start($designationRow['Designation_Name']);
									
                                    $this->view->accessRightsForm = $rolesForm;
									
                                    $this->view->roleExists = $this->_dbAccessRights->roleExists($designationId);
									
                                    if ($this->getRequest()->isPost())
                                    {
                                        if ($rolesForm->isValid($this->getRequest()->getPost()))
                                        {
                                            $formData = $rolesForm->getValues();
											
                                            $updateData = $this->_dbAccessRights->updateEmpAccessRights($designationRow['Designation_Name'], $formData,$designationId,$this->_logEmpId,$modules);
                                            
											if ($updateData > 0)
                                            {
                                                //to display the updated role details
                                                $rolesForm->populate($formData);
                                                $employeeName = $this->_dbPersonal->employeeName($this->_logEmpId);
                                                $updated['EmployeeName'] = $employeeName['Employee_Name'];
                                                $updated['Modified_Date'] = date($this->_orgDateFormat['php'].' \a\t H:i:s');
                                                	
                                                $this->view->message = array('a' => 'Roles updated successfully!','b' => 'success','c' => '','d' => '300');
                                            }
                                            else
                                            {
                                                //to display the error message if any error occured during updation
                                                $this->view->message = array('a' => 'Error in Updation','b' => 'fatal','c' => 'errboxid','d' => '200');
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    $this->_redirect('auth/index/logout');
                                }
                            }
                            else
                            {
                                $editEmpName = $this->_dbPersonal->employeeName($checkFormLock);
                                $this->view->access = $editEmpName['Employee_Name'] . ' is updating this record. Please Wait...';
                            }
                        }
                    }
                }
                else
                {
                    $this->view->access = 'Sorry, Access Denied...';
                }
            }
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }

    /**
     * Update employee roles
     */
    public function employeeRolesAction ()
    {
	$this->_helper->layout()->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
	{
		$ajaxContext = $this->_helper->getHelper('AjaxContext');
		$ajaxContext->addActionContext('employee-roles', 'json')->initContext();
		
		$employeeId = $this->_getParam('employeeId', null);
		$employeeId  =  filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
		
		$designationId = $this->_getParam('designationId', null);
		$designationId  =  filter_var($designationId, FILTER_SANITIZE_NUMBER_INT);
		
		if (!empty($employeeId) && ($this->_roleSuperAdminAccessRights['Employee']['Optional_Choice'] == 1 ||
			$this->_rolesAccessRights['Employee']['Update'] == 1))
		{
			if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				$employeeRoleData = $this->_getParam('employeeRoleData', null);
				
                $formData = $this->getRequest()->getPost();
                
				$empRoles = $this->_dbEmployee->getEmpAccessRights($employeeId);
				
				if($empRoles>0)
				{
					$this->view->result = $this->_dbEmployee->updateEmpAccessRights($employeeRoleData,'Edit',$this->_logEmpId);				
				}
				else
				{
				  $this->view->result = $this->_dbEmployee->updateEmpAccessRights($employeeRoleData,'Add',$this->_logEmpId);					
				}
			}
		}
		else if (!empty($designationId) && ($this->_roleSuperAdminAccessRights['Employee']['Optional_Choice'] == 1
			|| $this->_rolesAccessRights['Employee']['Update'] == 1 ))
		{
			if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				$designationRoleData = $this->_getParam('designationRoleData', null);
				
				$designationRoles = $this->_dbAccessRights->roleExists($designationId);
				
				if($designationRoles>0)
				{
                	$this->view->result = $this->_dbEmployee->updateDesignationAccessRights($designationRoleData,'Edit',$this->_logEmpId);				
				}
				else
				{
				  $this->view->result = $this->_dbEmployee->updateDesignationAccessRights($designationRoleData,'Add',$this->_logEmpId);					
				}
			}
		}
		else
		{
			$this->view->result = array('success'=> false, 'msg' => 'Sorry, Access Denied !', 'ImageExists'=>0, 'type' => 'info');
		}
	}
    }

    /**
     * Check lock in eployee roles table
     */
    public function empCheckLockAction ()
    {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('emp-check-lock', 'json')->initContext();
            $employeeId = $this->_getParam('empId', null);
            $employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
            if (!empty($employeeId))
            {
                $checkFormLock = $this->_dbAccessRights->checkLockFlag($employeeId, $this->_ehrTables->empAccessRights, 'Employee_Id');
                if ($checkFormLock != $this->_logEmpId && $checkFormLock != 0) // if lockflag is set to 0 or his logId, employee can access the form
                {
                    $editEmpName = $this->_dbPersonal->employeeName($checkFormLock);
                    $this->view->access = array('m','<div style="font-size:14px">'.$editEmpName['Employee_Name'] . ' is updating this record. Please Wait...</div>');
                }
                elseif ($checkFormLock == $this->_logEmpId)
                {
                    $this->view->access = array('m','<div style="font-size:14px;width:250px;">This Employee record has been opened by your session in some other browser or system.<br/>
							If you still have problem in opening the form, contact System Admin</div>');
                }
            }
            else
            {
                $this->view->access = array('n','unselect'.$employeeId);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }

    /**
     * Server side validation for dynamic educational rows
     */
    public function checkDynamicEducation($formData)
    {
        $count = count($formData['Education_Type']);
        $dynamicError = 0;
        $educationDetails = array();
        $filterAlpha = new Zend_Filter_Alpha(array('allowwhitespace' => true));
        $validAlpha = new Zend_Validate_Alpha(array('allowwhitespace' => true));
        $filterAlnum = new Zend_Filter_Alnum(array('allowwhitespace' => true));
        $validAlnum = new Zend_Validate_Alnum(array('allowwhitespace' => true));
        $filterDigits = new Zend_Filter_Digits();
        $validDigits = new Zend_Validate_Digits();

        if ($count > 0)
        {
            for($dyCntA = 0;$dyCntA < $count;$dyCntA++)
            {
                $educationType = filter_var($formData['Education_Type'][$dyCntA], FILTER_SANITIZE_STRIPPED);
                if (!preg_match('/^[\w\.\ ]*$/',$educationType) || empty($educationType))//if (empty($educationType))
                {
                    $dyCntA = $count;
                    $educationDetails = array();
                    $dynamicError = $dynamicError + 1;
                    return $educationDetails;
                }
                else
                {
                    $educationDetails[$dyCntA]['educationType'] = $educationType;
                }
                if ($dynamicError == 0 && !empty($formData['Specialisation'][$dyCntA]))
                {
                    $specialisation = filter_var($formData['Specialisation'][$dyCntA], FILTER_SANITIZE_STRIPPED);
                    $specialisation = $filterAlpha->filter($specialisation);
                    $specialisationError = $validAlpha->isValid($specialisation);
                    if (empty($specialisation) || !preg_match('/^[a-zA-Z\ \']+$/',$specialisation))
                    {
                        $dyCntA = $count;
                        $dynamicError = $dynamicError + 1;
                        $educationDetails = array();
                        return $educationDetails;
                    }
                    else
                    {
                        $educationDetails[$dyCntA]['specialisation'] = $specialisation;
                    }
                }
                if ($dynamicError == 0 && !empty($formData['Institute_Name'][$dyCntA]))
                {
                    $dyCntAnstituteName = filter_var($formData['Institute_Name'][$dyCntA], FILTER_SANITIZE_STRIPPED);
                    $dyCntAnstituteName = $filterAlpha->filter($dyCntAnstituteName);
                    $dyCntAnstituteNameError = $validAlpha->isValid($dyCntAnstituteName);
                    if (empty($dyCntAnstituteName) || !preg_match('/^[a-zA-Z\ \']+$/',$dyCntAnstituteName))
                    {
                        $dyCntA = $count;
                        $dynamicError = $dynamicError + 1;
                        $educationDetails = array();
                        return $educationDetails;
                    }
                    else
                    {
                        $educationDetails[$dyCntA]['instituteName'] = $dyCntAnstituteName;
                    }
                }
                if ($dynamicError == 0 && !empty($formData['University'][$dyCntA]))
                {
                    $university = filter_var($formData['University'][$dyCntA], FILTER_SANITIZE_STRIPPED);
                    $university = $filterAlpha->filter($university);
                    $universityError = $validAlpha->isValid($university);
                    if (empty($university) || !preg_match('/^[a-zA-Z\ \']+$/',$dyCntAnstituteName))
                    {
                        $dyCntA = $count;
                        $dynamicError = $dynamicError + 1;
                        $educationDetails = array();
                        return $educationDetails;
                    }
                    else
                    {
                        $educationDetails[$dyCntA]['university'] = $university;
                    }
                }
                if ($dynamicError == 0 && !empty($formData['Year_Of_Passing'][$dyCntA]))
                {
                    $yearOfPassing = filter_var($formData['Year_Of_Passing'][$dyCntA], FILTER_SANITIZE_NUMBER_INT);
                    $curYear = date('Y');
                    $validatorChain = new Zend_Validate();
                    $validatorChain->addValidator(new Zend_Validate_StringLength(array('min' => 4,
							'max' => 4)))
                    ->addValidator(new Zend_Validate_Digits())
                    ->addValidator(new Zend_Validate_Between(array('min' => 1950, 'max' => $curYear)));
                    $yearOfPassingError = $validatorChain->isValid($yearOfPassing);

                    if (empty($yearOfPassing) || $yearOfPassingError == false)
                    {
                        $dyCntA = $count;
                        $dynamicError = $dynamicError + 1;
                        $educationDetails = array();
                        return $educationDetails;
                    }
                    else
                    {
                        $educationDetails[$dyCntA]['yearOfPassing'] = $yearOfPassing;
                    }
                }
                if ($dynamicError == 0 && !empty($formData['Percentage'][$dyCntA]))
                {
                    if (!preg_match('/^[0-9\.]+$/',$formData['Percentage'][$dyCntA]))
                    {
                        $dyCntA = $count;
                        $dynamicError = $dynamicError + 1;
                        $educationDetails = array();
                        return $educationDetails;
                    }
                    else
                    {
                        $educationDetails[$dyCntA]['percentage'] = $formData['Percentage'][$dyCntA];
                    }
                }
                if ($dynamicError == 0 && !empty($formData['Grade'][$dyCntA]))
                {
                    $grade = filter_var($formData['Grade'][$dyCntA], FILTER_SANITIZE_STRIPPED);
                    $grade = $filterAlpha->filter($grade);
                    $gradeNameError = $validAlpha->isValid($grade);
                    if (empty($grade) || !preg_match('/^[a-zA-Z\ \']+$/',$grade))
                    {
                        $dyCntA = $count;
                        $dynamicError = $dynamicError + 1;
                        $educationDetails = array();
                        return $educationDetails;
                    }
                    else
                    {
                        $educationDetails[$dyCntA]['grade'] = $grade;
                    }
                }

            }
        }
        return $educationDetails;
    }

    /**
     * Server side validation for dynamic certification rows
     */
    public function checkDynamicCertification($formData)
    {
        $count = count($formData['Certification_Name']);
        $dynamicError = 0;
        $certificationDetails = array();
        $filterAlpha = new Zend_Filter_Alpha(array('allowwhitespace' => true));
        $validAlpha = new Zend_Validate_Alpha(array('allowwhitespace' => true));
        $filterAlnum = new Zend_Filter_Alnum(array('allowwhitespace' => true));
        $validAlnum = new Zend_Validate_Alnum(array('allowwhitespace' => true));
        $filterDigits = new Zend_Filter_Digits();
        $validDigits = new Zend_Validate_Digits();

        if ($count > 0)
        {
            for($dyCntA = 0;$dyCntA < $count;$dyCntA++)
            {
                $certificationName = filter_var($formData['Certification_Name'][$dyCntA], FILTER_SANITIZE_STRIPPED);
                if (!preg_match('/^[\w\#\+\.\,\/\-\ ]*$/',$certificationName) || empty($certificationName))
                {
                    $dyCntA = $count;
                    $dynamicError = $dynamicError + 1;
                    $certificationDetails = array();
                    return $certificationDetails;
                }
                else
                {
                    $certificationDetails[$dyCntA]['certificationName'] = $certificationName;
                }
                if ($dynamicError == 0 && !empty($formData['Received_Date'][$dyCntA]))
                {
                    $receivedDate = filter_var($formData['Received_Date'][$dyCntA], FILTER_SANITIZE_STRIPPED);
                    if (empty($receivedDate) || !preg_match($this->_orgDateFormat['regex'],$receivedDate))
                    {
                        $dyCntA = $count;
                        $dynamicError = $dynamicError + 1;
                        $certificationDetails = array();
                        return $certificationDetails;
                    }
                    else
                    {
                        $certificationDetails[$dyCntA]['receivedDate'] = $receivedDate;
                    }
                }
                if ($dynamicError == 0 && !empty($formData['Certificate_Received_From'][$dyCntA]))
                {
                    $certificateReceivedFrom = filter_var($formData['Certificate_Received_From'][$dyCntA], FILTER_SANITIZE_STRIPPED);
                    $certificateReceivedFrom = $filterAlpha->filter($certificateReceivedFrom);
                    $certificateReceivedFromError = $validAlpha->isValid($certificateReceivedFrom);
                    if (empty($certificateReceivedFrom) || !preg_match('/^[a-zA-Z\ \']+$/',$certificateReceivedFrom))
                    {
                        $dyCntA = $count;
                        $dynamicError = $dynamicError + 1;
                        $certificationDetails = array();
                        return $certificationDetails;
                    }
                    else
                    {
                        $certificationDetails[$dyCntA]['certificateReceivedFrom'] = $certificateReceivedFrom;
                    }
                }

            }

        }
        return $certificationDetails;
    }

    /**
     * Server side validation for dynamic training rows
     */
    public function checkDynamicTraining($formData, $dob, $saveType)
    {
        $count = count($formData['Training_Name']);
        $dynamicError = 0;
        $invalid = 0;
        $trainingDetails = array();
        $filterAlpha = new Zend_Filter_Alpha(array('allowwhitespace' => true));
        $validAlpha = new Zend_Validate_Alpha(array('allowwhitespace' => true));
        $filterAlnum = new Zend_Filter_Alnum(array('allowwhitespace' => true));
        $validAlnum = new Zend_Validate_Alnum(array('allowwhitespace' => true));
        $filterDigits = new Zend_Filter_Digits();
        $validDigits = new Zend_Validate_Digits();

        if ($count > 0)
        {
            for($dyCntA = 0;$dyCntA < $count;$dyCntA++)
            {
                $trainingName = filter_var($formData['Training_Name'][$dyCntA], FILTER_SANITIZE_STRIPPED);
                if (!preg_match('/^[\w\.\- ]*$/',$trainingName) || empty($trainingName))
                {
                    $dyCntA = $count;
                    $trainingDetails = array();
                    $dynamicError = $dynamicError + 1;
                    return $trainingDetails;
                }
                else
                {
                    $trainingDetails[$dyCntA]['trainingName'] = $trainingName;
                }
                if ($dynamicError == 0 && !empty($formData['Training_Start_Date'][$dyCntA]))
                {
                    $trainingStartDate = filter_var($formData['Training_Start_Date'][$dyCntA], FILTER_SANITIZE_STRIPPED);
                    if (empty($trainingStartDate) || !preg_match($this->_orgDateFormat['regex'],$trainingStartDate))
                    {
                        $dyCntA = $count;
                        $dynamicError = $dynamicError + 1;
                        $trainingDetails = array();
                        return $trainingDetails;
                    }
                    else if (!empty($dob) && !empty($trainingStartDate) && $this->isLessDOB($dob,$trainingStartDate) && $saveType == 'Save')
                    {
                        $trainingDetails = 'Beyond DOB';
                        return $trainingDetails;
                    }	
                    else
                    {
                        $trainingDetails[$dyCntA]['trainingStartDate'] = $trainingStartDate;
						$dyCntAnvalid = 0;
						
                        if ($dynamicError == 0 && !empty($formData['Training_End_Date'][$dyCntA]))
                        {
                            $trainingEndDate = filter_var($formData['Training_End_Date'][$dyCntA], FILTER_SANITIZE_STRIPPED);
                            if (empty($trainingEndDate) || !preg_match($this->_orgDateFormat['regex'],$trainingEndDate))
                            {
                                $dyCntA = $count;
                                $dynamicError = $dynamicError + 1;
                                $trainingDetails = array();
                                return $trainingDetails;
                            }
                            else if (!empty($dob) && !empty($trainingEndDate) && $this->isLessDOB($dob,$trainingEndDate) && $saveType == 'Save')
                            {
                                $trainingDetails = 'Beyond DOB';
                                return $trainingDetails;
                            }
                            else
                            {
                                $trainingDetails[$dyCntA]['trainingEndDate'] = $trainingEndDate;

                                for($dyCntB = 0;$dyCntB < $count;$dyCntB++)
                                {
                                    if ($dyCntB != $dyCntA)
                                    {
                                        if ((strtotime($this->_ehrTables->changeDateformat($trainingDetails[$dyCntA]['trainingStartDate'])) <= strtotime($this->_ehrTables->changeDateformat($formData['Training_Start_Date'][$dyCntB])) &&
                                        strtotime($this->_ehrTables->changeDateformat($trainingDetails[$dyCntA]['trainingStartDate'])) <= strtotime($this->_ehrTables->changeDateformat($formData['Training_End_Date'][$dyCntB])) &&
                                        strtotime($this->_ehrTables->changeDateformat($trainingDetails[$dyCntA]['trainingEndDate'])) <= strtotime($this->_ehrTables->changeDateformat($formData['Training_Start_Date'][$dyCntB])) &&
                                        strtotime($this->_ehrTables->changeDateformat($trainingDetails[$dyCntA]['trainingEndDate'])) <= strtotime($this->_ehrTables->changeDateformat($formData['Training_End_Date'][$dyCntB]))) ||
                                        (strtotime($this->_ehrTables->changeDateformat($trainingDetails[$dyCntA]['trainingStartDate'])) >= strtotime($this->_ehrTables->changeDateformat($formData['Training_Start_Date'][$dyCntB])) &&
                                        strtotime($this->_ehrTables->changeDateformat($trainingDetails[$dyCntA]['trainingStartDate'])) >= strtotime($this->_ehrTables->changeDateformat($formData['Training_End_Date'][$dyCntB])) &&
                                        strtotime($this->_ehrTables->changeDateformat($trainingDetails[$dyCntA]['trainingEndDate'])) >= strtotime($this->_ehrTables->changeDateformat($formData['Training_Start_Date'][$dyCntB])) &&
                                        strtotime($this->_ehrTables->changeDateformat($trainingDetails[$dyCntA]['trainingEndDate'])) >= strtotime($this->_ehrTables->changeDateformat($formData['Training_End_Date'][$dyCntB]))))
                                        {
                                            //doNothing;
                                        }
                                        else
                                        {
                                            $dyCntAnvalid = $dyCntAnvalid + 1;
                                            $dyCntB = $count;
                                            $dynamicError = $dynamicError + 1;
                                            $trainingDetails = array();
                                            return $trainingDetails;
                                        }
                                    }
                                }

                            }
                        }

                    }
                }
	
                if ($dynamicError == 0 && !empty($formData['Training_Duration'][$dyCntA]) && $saveType == 'Save')
                {
                    $trainingDuration = filter_var($formData['Training_Duration'][$dyCntA], FILTER_SANITIZE_STRIPPED);
                    if (empty($trainingDuration) || !preg_match('/^[0-9\.]+$/',$trainingDuration))
                    {
                        $dyCntA = $count;
                        $dynamicError = $dynamicError + 1;
                        $trainingDetails = array();
                        return $trainingDetails;
                    }
                    else
                    {
                        $trainingDetails[$dyCntA]['trainingDuration'] = $trainingDuration;
                    }
                }
				else
				{
					if ($this->_isMobile)
					{
						if ($dynamicError == 0 && empty($formData['Training_Duration'][$dyCntA]))
						{
							$trainingDetails[$dyCntA]['trainingDuration'] = 0;
						}
						else if ($dynamicError == 0 && !empty($formData['Training_Duration'][$dyCntA]))
						{
							$trainingDetails[$dyCntA]['trainingDuration'] = $formData['Training_Duration'][$dyCntA];
						}	
					}
					else
					{
						if ($dynamicError == 0 && empty($formData['Duration'][$dyCntA]))
						{
							$trainingDetails[$dyCntA]['trainingDuration'] = 0;
						}
						else if ($dynamicError == 0 && !empty($formData['Duration'][$dyCntA]))
						{
							$trainingDetails[$dyCntA]['trainingDuration'] = $formData['Duration'][$dyCntA];
						}	
					}
				}
				
				if ($dynamicError == 0 && !empty($formData['Trainer'][$dyCntA]))
                {
                    $trainer = filter_var($formData['Trainer'][$dyCntA], FILTER_SANITIZE_STRIPPED);
                    $trainer = $filterAlpha->filter($trainer);
                    $trainerError = $validAlpha->isValid($trainer);
                    if (empty($trainer) || !preg_match('/^[a-zA-Z\ \']+$/',$trainer))
                    {
                        $dyCntA = $count;
                        $dynamicError = $dynamicError + 1;
                        $trainingDetails = array();
                        return $trainingDetails;
                    }
                    else
                    {
                        $trainingDetails[$dyCntA]['trainer'] = $trainer;
                    }
                }
                if ($dynamicError == 0 && !empty($formData['Center'][$dyCntA]))
                {
                    $center = filter_var($formData['Center'][$dyCntA], FILTER_SANITIZE_STRIPPED);
                    $center = $filterAlpha->filter($center);
                    $centerError = $validAlpha->isValid($center);
                    if (empty($center) || !preg_match('/^[a-zA-Z\ \']+$/',$center))
                    {
                        $dyCntA = $count;
                        $dynamicError = $dynamicError + 1;
                        $trainingDetails = array();
                        return $trainingDetails;
                    }
                    else
                    {
                        $trainingDetails[$dyCntA]['center'] = $center;
                    }
                }
            }
        }
        return $trainingDetails;
    }

    /**
     * Server side validation for dynamic awards rows
     */
    public function checkDynamicAward($formData)
    {
        $count = count($formData['Award_Name']);
        $dynamicError = 0;
        $awardDetails = array();
        $filterAlpha = new Zend_Filter_Alpha(array('allowwhitespace' => true));
        $validAlpha = new Zend_Validate_Alpha(array('allowwhitespace' => true));
        $filterAlnum = new Zend_Filter_Alnum(array('allowwhitespace' => true));
        $validAlnum = new Zend_Validate_Alnum(array('allowwhitespace' => true));
        $filterDigits = new Zend_Filter_Digits();
        $validDigits = new Zend_Validate_Digits();

        if ($count > 0)
        {
            for($dyCntA = 0;$dyCntA < $count;$dyCntA++)
            {
                $awardName = filter_var($formData['Award_Name'][$dyCntA], FILTER_SANITIZE_STRIPPED);
                if (!preg_match('/^[\w\.\- ]*$/',$awardName) || empty($awardName))
                {
                    $dyCntA = $count;
                    $awardDetails = array();
                    $dynamicError = $dynamicError + 1;
                    return $awardDetails;
                }
                else
                {
                    $awardDetails[$dyCntA]['awardName'] = $awardName;
                }
                if ($dynamicError == 0 && !empty($formData['Received_On'][$dyCntA]))
                {
                    $receivedOn = filter_var($formData['Received_On'][$dyCntA], FILTER_SANITIZE_STRIPPED);
                    if (empty($receivedOn) || !preg_match($this->_orgDateFormat['regex'],$receivedOn))
                    {
                        $dyCntA = $count;
                        $dynamicError = $dynamicError + 1;
                        $awardDetails = array();
                        return $awardDetails;
                    }
                    else
                    {
                        $awardDetails[$dyCntA]['receivedOn'] = $receivedOn;
                    }
                }
                if ($dynamicError == 0 && !empty($formData['Received_From'][$dyCntA]))
                {
                    $receivedFrom = filter_var($formData['Received_From'][$dyCntA], FILTER_SANITIZE_STRIPPED);
                    $receivedFrom = $filterAlpha->filter($receivedFrom);
                    $receivedFromError = $validAlpha->isValid($receivedFrom);
                    if (empty($receivedFrom) || !preg_match('/^[a-zA-Z\ \']+$/',$receivedFrom))
                    {
                        $dyCntA = $count;
                        $dynamicError = $dynamicError + 1;
                        $awardDetails = array();
                        return $awardDetails;
                    }
                    else
                    {
                        $awardDetails[$dyCntA]['receivedFrom'] = $receivedFrom;
                    }
                }
                if ($dynamicError == 0 && !empty($formData['Received_For'][$dyCntA]))
                {
                    $receivedFor = filter_var($formData['Received_For'][$dyCntA], FILTER_SANITIZE_STRIPPED);
                    $receivedFor = $filterAlpha->filter($receivedFor);
                    $receivedForError = $validAlpha->isValid($receivedFor);
                    if (empty($receivedFor) || !preg_match('/^[a-zA-Z\ \']+$/',$receivedFor))
                    {
                        $dyCntA = $count;
                        $dynamicError = $dynamicError + 1;
                        $awardDetails = array();
                        return $awardDetails;
                    }
                    else
                    {
                        $awardDetails[$dyCntA]['receivedFor'] = $receivedFor;
                    }
                }

            }
        }
        return $awardDetails;
    }

    /**
     * Server side validation for dynamic dependent rows
     */
    public function checkDynamicDependent($formData)
    {
        $count = count($formData);
        $dynamicError = 0;
        $dependentDetails = array();
        $filterAlpha = new Zend_Filter_Alpha(array('allowwhitespace' => true));
        $validAlpha = new Zend_Validate_Alpha(array('allowwhitespace' => true));
        $filterAlnum = new Zend_Filter_Alnum(array('allowwhitespace' => true));
        $validAlnum = new Zend_Validate_Alnum(array('allowwhitespace' => true));
        $filterDigits = new Zend_Filter_Digits();
        $validDigits = new Zend_Validate_Digits();

        if ($count > 0)
        {
            for($dyCntA = 0;$dyCntA < $count;$dyCntA++)
            {
                $dependentFirstName = filter_var($formData[$dyCntA]['dependentFirstName'], FILTER_SANITIZE_STRIPPED);
                $dependentFirstName = $filterAlpha->filter($dependentFirstName);
                $dependentFirstNameError = $validAlpha->isValid($dependentFirstName);
    
	            if (empty($dependentFirstName) || !preg_match('/^[a-zA-Z\ \']+$/',$dependentFirstName))
                {
                    $dyCntA = $count;
                    $dependentDetails = array();
                    $dynamicError = $dynamicError + 1;
                    return $dependentDetails;
                }
                else
                {
                    $dependentDetails[$dyCntA]['dependentFirstName'] = $dependentFirstName;
                }
    
	            if ($dynamicError == 0 && !empty($formData[$dyCntA]['dependentLastName']))
                {
                    $dependentLastName = filter_var($formData[$dyCntA]['dependentLastName'], FILTER_SANITIZE_STRIPPED);
                    $dependentLastName = $filterAlpha->filter($dependentLastName);
                    $dependentLastNameError = $validAlpha->isValid($dependentLastName);
    
	                if (empty($dependentLastName) || !preg_match('/^[a-zA-Z\ \']+$/',$dependentLastName))
                    {
                        $dyCntA = $count;
                        $dependentDetails = array();
                        $dynamicError = $dynamicError + 1;
                        return $dependentDetails;
                    }
                    else
                    {
                        $dependentDetails[$dyCntA]['dependentLastName'] = $dependentLastName;
                    }
                }
    
	            if ($dynamicError == 0 && !empty($formData[$dyCntA]['dependentDOB'] ))
                {
                    $dependentDOB = filter_var($formData[$dyCntA]['dependentDOB'], FILTER_SANITIZE_STRIPPED);
    
	                if (empty($dependentDOB) || !preg_match($this->_orgDateFormat['regex'],$dependentDOB))
                    {
                        $dyCntA = $count;
                        $dynamicError = $dynamicError + 1;
                        $dependentDetails = array();
                        return $dependentDetails;
                    }
                    else
                    {
                        $dependentDetails[$dyCntA]['dependentDOB'] = $dependentDOB;
                    }
                }
    
	            if ($dynamicError == 0 && !empty($formData[$dyCntA]['dependentGender']))
                {
                    $dependentGender = filter_var($formData[$dyCntA]['dependentGender'], FILTER_SANITIZE_STRIPPED);
                    $dependentGender = $filterAlpha->filter($dependentGender);
                    $dependentGenderError = $validAlpha->isValid($dependentGender);
    
	                if (empty($dependentGender) || !preg_match('/^[a-zA-Z\ \']+$/',$dependentGender))
                    {
                        $dyCntA = $count;
                        $dynamicError = $dynamicError + 1;
                        $dependentDetails = array();
                        return $dependentDetails;
                    }
                    else
                    {
                        $dependentDetails[$dyCntA]['dependentGender'] = $dependentGender;
                    }
                }
    
	            if ($dynamicError == 0 && !empty($formData[$dyCntA]['dependentRelationship']))
                {
                    $dependentRelationship = filter_var($formData[$dyCntA]['dependentRelationship'], FILTER_SANITIZE_STRIPPED);
                    $dependentRelationship = $filterAlpha->filter($dependentRelationship);
                    $dependentRelationshipError = $validAlpha->isValid($dependentRelationship);
    
	                if (empty($dependentRelationship) || !preg_match('/^[a-zA-Z\ \']+$/',$dependentRelationship))
                    {
                        $dyCntA = $count;
                        $dynamicError = $dynamicError + 1;
                        $dependentDetails = array();
                        return $dependentDetails;
                    }
                    else
                    {
                        $dependentDetails[$dyCntA]['dependentRelationship'] = $dependentRelationship;
                    }
                }
    
	            if ($dynamicError == 0 && !empty($formData[$dyCntA]['dependentAge']))
                {
                    $dependentAge = filter_var($formData[$dyCntA]['dependentAge'], FILTER_SANITIZE_STRIPPED);
                    $dependentAge = $filterAlnum->filter($dependentAge);
                    $dependentAgeError = $validAlnum->isValid($dependentAge);
    
	                if (empty($dependentAge) || !preg_match('/^[0-9a-zA-Z\ \']+$/',$dependentAge))
                    {
                        $dyCntA = $count;
                        $dynamicError = $dynamicError + 1;
                        $dependentDetails = array();
                        return $dependentDetails;
                    }
                    else
                    {
                        $dependentDetails[$dyCntA]['dependentAge'] = $dependentAge;
                    }
	            }
            }
        }
        return $dependentDetails;
    }

    /**
     * Server side validation for dynamic exerience rows
     */
    public function checkDynamicExperience($formData, $dob, $saveType)
    {
        $count = count($formData['Prev_Company_Name']);
        $dynamicError = 0;
        $invalid = 0;
        $experienceDetails = array();
        $filterAlpha = new Zend_Filter_Alpha(array('allowwhitespace' => true));
        $validAlpha = new Zend_Validate_Alpha(array('allowwhitespace' => true));
        $filterAlnum = new Zend_Filter_Alnum(array('allowwhitespace' => true));
        $validAlnum = new Zend_Validate_Alnum(array('allowwhitespace' => true));
        $filterDigits = new Zend_Filter_Digits();
        $validDigits = new Zend_Validate_Digits();

        if ($count > 0)
        {
            for($dyCntA = 0;$dyCntA < $count;$dyCntA++)
            {
                $prevCompanyName = filter_var($formData['Prev_Company_Name'][$dyCntA], FILTER_SANITIZE_STRIPPED);
                $prevCompanyName = $filterAlnum->filter($prevCompanyName);
                
				if (empty($prevCompanyName) || !preg_match('/^[0-9a-zA-Z\ \']+$/',$prevCompanyName))
                {
			        $dyCntA = $count;
                    $experienceDetails = array();
                    $dynamicError = $dynamicError + 1;
                    return $experienceDetails;
                }
                else
                {
                    $experienceDetails[$dyCntA]['prevCompanyName'] = $prevCompanyName;
                }

				
                if ($dynamicError == 0 && !empty($formData['Prev_Company_Location'][$dyCntA]))
                {
                    $prevCompanyLocation = filter_var($formData['Prev_Company_Location'][$dyCntA], FILTER_SANITIZE_STRIPPED);
                    $prevCompanyLocation = $filterAlpha->filter($prevCompanyLocation);
                    $prevCompanyLocationError = $validAlpha->isValid($prevCompanyLocation);
                    
					if (empty($prevCompanyLocation) || !preg_match('/^[a-zA-Z\ \']+$/',$prevCompanyLocation))
                    {
			            $dyCntA = $count;
                        $experienceDetails = array();
                        $dynamicError = $dynamicError + 1;
                        return $experienceDetails;
                    }
                    else
                    {
                        $experienceDetails[$dyCntA]['prevCompanyLocation'] = $prevCompanyLocation;
                    }
                }

                if ($dynamicError == 0 && !empty($formData['Designation'][$dyCntA]))
                {
                    $designation = filter_var($formData['Designation'][$dyCntA], FILTER_SANITIZE_STRIPPED);
                    $designation = $filterAlpha->filter($designation);
                    $designationError = $validAlpha->isValid($designation);
                    
					if (empty($designation) || !preg_match('/^[a-zA-Z\ \']+$/',$prevCompanyName))
                    {
			            $dyCntA = $count;
                        $experienceDetails = array();
                        $dynamicError = $dynamicError + 1;
                        return $experienceDetails;
                    }
                    else
                    {
                        $experienceDetails[$dyCntA]['designation'] = $designation;
                    }
                }

                if ($dynamicError == 0 && !empty($formData['Start_Date_Join'][$dyCntA]))
                {
                    $startDateJoin = filter_var($formData['Start_Date_Join'][$dyCntA], FILTER_SANITIZE_STRIPPED);
            
			        if (empty($startDateJoin) || !preg_match($this->_orgDateFormat['regex'],$startDateJoin))
                    {
			            $dyCntA = $count;
                        $dynamicError = $dynamicError + 1;
                        $experienceDetails = array();
                        return $experienceDetails;
                    }
                    else if (!empty($dob) && !empty($startDateJoin) && $this->isLessDOB($dob,$startDateJoin) && $saveType == 'Save')
                    {
                        $experienceDetails = 'Beyond DOB';
                        return $experienceDetails;
                    }
                    else
                    {
                        $experienceDetails[$dyCntA]['startDateJoin'] = $startDateJoin;
                        $dyCntAnvalid = 0;
                        
						if (!empty($formData['End_Date'][$dyCntA]))
                        {
                            $endDate = filter_var($formData['End_Date'][$dyCntA], FILTER_SANITIZE_STRIPPED);
                        
						    if (empty($endDate) || !preg_match($this->_orgDateFormat['regex'],$endDate))
                            {
								$dyCntA = $count;
                                $dynamicError = $dynamicError + 1;
                                $experienceDetails = array();
                                return $experienceDetails;
                            }
                            else if (!empty($dob) && !empty($endDate) && $this->isLessDOB($dob,$endDate) && $saveType == 'Save')
                            {
								$experienceDetails = 'Beyond DOB';
                                return $experienceDetails;
                            }
                            else
                            {
                                $experienceDetails[$dyCntA]['endDate'] = $endDate;
                                for($dyCntB = 0;$dyCntB < $count;$dyCntB++)
                                {
                                    if ($dyCntB != $dyCntA)
                                    {
                                        if ((strtotime($this->_ehrTables->changeDateformat($experienceDetails[$dyCntA]['startDateJoin'])) <= strtotime($this->_ehrTables->changeDateformat($formData['Start_Date_Join'][$dyCntB])) &&
                                        strtotime($this->_ehrTables->changeDateformat($experienceDetails[$dyCntA]['startDateJoin'])) <= strtotime($this->_ehrTables->changeDateformat($formData['End_Date'][$dyCntB])) &&
                                        strtotime($this->_ehrTables->changeDateformat($experienceDetails[$dyCntA]['endDate'])) <= strtotime($this->_ehrTables->changeDateformat($formData['Start_Date_Join'][$dyCntB])) &&
                                        strtotime($this->_ehrTables->changeDateformat($experienceDetails[$dyCntA]['endDate'])) <= strtotime($this->_ehrTables->changeDateformat($formData['End_Date'][$dyCntB]))) ||
                                        (strtotime($this->_ehrTables->changeDateformat($experienceDetails[$dyCntA]['startDateJoin'])) >= strtotime($this->_ehrTables->changeDateformat($formData['Start_Date_Join'][$dyCntB])) &&
                                        strtotime($this->_ehrTables->changeDateformat($experienceDetails[$dyCntA]['startDateJoin'])) >= strtotime($this->_ehrTables->changeDateformat($formData['End_Date'][$dyCntB])) &&
                                        strtotime($this->_ehrTables->changeDateformat($experienceDetails[$dyCntA]['endDate'])) >= strtotime($this->_ehrTables->changeDateformat($formData['Start_Date_Join'][$dyCntB])) &&
                                        strtotime($this->_ehrTables->changeDateformat($experienceDetails[$dyCntA]['endDate'])) >= strtotime($this->_ehrTables->changeDateformat($formData['End_Date'][$dyCntB]))))
                                        {
                                            //doNothing;
                                        }
                                        else
                                        {
								            $dyCntAnvalid = $dyCntAnvalid + 1;
                                            $dyCntB = $count;
                                            $dynamicError = $dynamicError + 1;
                                            $experienceDetails = array();
                                            return $experienceDetails;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
				
                if ($dynamicError == 0 && !empty($formData['Duration'][$dyCntA]) && $saveType == 'Save')
                {
                    $duration = filter_var($formData['Duration'][$dyCntA], FILTER_SANITIZE_STRIPPED);
                 
				    if (!preg_match('/^[0-9\.]+$/',$duration) || empty($duration))
                    {
                        $dyCntA = $count;
                        $dynamicError = $dynamicError + 1;
                        $experienceDetails = array();
                        return $experienceDetails;
                    }
                    else
                    {
                        $experienceDetails[$dyCntA]['duration'] = $duration;
                    }
                }
                else if ($dynamicError == 0 && empty($formData['Duration'][$dyCntA]))
                {
                    $experienceDetails[$dyCntA]['duration'] = 0;
                }
                else if ($dynamicError == 0 && !empty($formData['Duration'][$dyCntA]))
                {
                    $experienceDetails[$dyCntA]['duration'] = $formData['Duration'][$dyCntA];
                }
            }
        }
        return $experienceDetails;
    }

	/**
     * Get employeeIds by selected manager name
     */
    public function getManagerByName($selManagerName)
    {
        $empName = explode(' ', $selManagerName);
        $empIds = $this-> _dbPersonal->getManagerByName($empName[0],$empName[1]);
        return $empIds;
    }

    /**
     * This action is to add employee experience dynamic fields
     * while click '+' image. ...
     *
     *
     *
     *
     */
    public function newexpfieldAction ()
    {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('newexpfield', 'html')->initContext();

            $expId = $this->_getParam('exp_id', null);
            $expId = filter_var($expId, FILTER_SANITIZE_NUMBER_INT);

            $prevCompanyName = $this->_getParam('Prev_Company_Name', null);
            $prevCompanyName = filter_var($prevCompanyName, FILTER_SANITIZE_STRIPPED);

            $prevCompanyLocation = $this->_getParam('Prev_Company_Location', null);
            $prevCompanyLocation = filter_var($prevCompanyLocation, FILTER_SANITIZE_STRIPPED);

            $Designation = $this->_getParam('Designation', null);
            $Designation = filter_var($Designation, FILTER_SANITIZE_STRIPPED);

            $startDateJoin = $this->_getParam('Start_Date_Join', null);
            $startDateJoin = filter_var($startDateJoin, FILTER_SANITIZE_STRIPPED);

            $endDateVal = $this->_getParam('End_Date', null);
            $endDateVal = filter_var($endDateVal, FILTER_SANITIZE_STRIPPED);

            $durationVal = $this->_getParam('Duration', null);
            $durationVal = filter_var($durationVal, FILTER_SANITIZE_STRIPPED);

            $sameRow = array(
					'ViewHelper',
					'Description',
            	
            array(array('data' => 'HtmlTag'),
            array('tag' => 'td')));

            $companyName = new Zend_Form_Element_Text('Prev_Company_Name');
            $companyName->setRequired(true)->setValue($prevCompanyName);
            $companyName->setIsArray(true)->setAttrib('id','company_name'.$expId)->setAttrib('style','width:120px;');
            $companyName->setDecorators(array(
					'ViewHelper',
					'Description',
            	
            array(array('data' => 'HtmlTag'), array('tag' => 'td')),
            array(array('row' => 'HtmlTag'),array('tag' => 'tr', 'openOnly' => true,'id' => 'dynamicExpRow'.$expId ,'class' => 'expRow'.$expId))))
            ->setAttrib('class','Prev_Company_Name expElem1');
            $this->view->companyName = $companyName;

            $companyLocation = new Zend_Form_Element_Text('Prev_Company_Location');
            $companyLocation->setRequired(true)->setValue($prevCompanyLocation)->setAttrib('style','width:120px;');
            $companyLocation->setIsArray(true)->setAttrib('id','company_location'.$expId);
            $companyLocation->setDecorators($sameRow)->setAttrib('class','Prev_Company_Location expElem2');
            $this->view->companyLocation = $companyLocation;

            $desig = new Zend_Form_Element_Text('Designation');
            $desig->setRequired(true)->setValue($Designation);
            $desig->setIsArray(true)->setAttrib('id','desig'.$expId)->setAttrib('class','Designation expElem3')->setAttrib('style','width:120px;');
            $desig->setDecorators($sameRow);
            $this->view->desig = $desig;

            $dateJoin = new Zend_Form_Element_Text('Start_Date_Join');
            $dateJoin->setRequired(true)->setAttrib('class','startDateJoin expElem4')->setValue($startDateJoin);
            $dateJoin->setIsArray(true)->setAttrib('id','startDateJoin'.$expId)->setAttrib('style','width:120px');
            $dateJoin->setDecorators($sameRow);
            $this->view->dateJoin = $dateJoin;

            $enddate = new Zend_Form_Element_Text('End_Date');
            $enddate->setRequired(true)->setAttrib('class','End_Date expElem5')->setValue($endDateVal);
            $enddate->setIsArray(true)->setAttrib('id','End_Date'.$expId)->setAttrib('style','width:120px');
            $enddate->setDecorators($sameRow);
            $this->view->enddate = $enddate;

            $duration = new Zend_Form_Element_Text('Duration');
            $duration->setRequired(true)->setValue($durationVal)->setAttrib('class','Duration expElem6');
            $duration->setIsArray(true)->setAttrib('id','Duration'.$expId)->setAttrib('style','width:50px;')
            ->setDescription(' months <img id="delExperience'.$expId.'" class="removeExperience" src="'.$this->_basePath->baseUrl('images/remove.png').'">' )
            ->setAttrib('readonly','readonly');
            $duration->setDecorators(array('ViewHelper',array('Description', array('escape' => false, 'tag' => false),
            array('placement' => Zend_Form_Decorator_Abstract::APPEND, 'tag' => 'em')),
            array(array('data' => 'HtmlTag'), array('tag' => 'td','align' => 'left','id' => 'durCol'.$expId))));
            $this->view->duration = $duration;
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }

    /**
     * This action is to add employee education dynamic fields
     * while click '+' image. ...
     */
    public function neweducationfieldAction ()
    {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('neweducationfield', 'html')->initContext();

            $id = $this->_getParam('id', null);
            $id = filter_var($id, FILTER_SANITIZE_NUMBER_INT);

            $educationType = $this->_getParam('Education_Type', null);
            $educationType = filter_var($educationType, FILTER_SANITIZE_STRIPPED);

            $specialisation = $this->_getParam('Specialisation', null);
            $specialisation = filter_var($specialisation, FILTER_SANITIZE_STRIPPED);

            $instituteName = $this->_getParam('Institute_Name', null);
            $instituteName = filter_var($instituteName, FILTER_SANITIZE_STRIPPED);

            $university = $this->_getParam('University', null);
            $university = filter_var($university, FILTER_SANITIZE_STRIPPED);

            $yearOfPassing = $this->_getParam('Year_Of_Passing', null);
            $yearOfPassing = filter_var($yearOfPassing, FILTER_SANITIZE_NUMBER_INT);

            $percentage = $this->_getParam('Percentage', null);
            $percentage = filter_var($percentage, FILTER_SANITIZE_NUMBER_INT);

            $distinction = $this->_getParam('Distinction', null);
            $distinction = filter_var($distinction, FILTER_SANITIZE_STRIPPED);

            $sameRow = array(
					'ViewHelper',
					'Description',
            	
            array(array('data' => 'HtmlTag'),
            array('tag' => 'td','align' => 'left')));

            $eduType = new Zend_Form_Element_Text('Education_Type');
            $eduType->setRequired(true)->setValue($educationType);
            $eduType->setIsArray(true)
            ->setAttrib('id','Education_Type'.$id)
            ->setAttrib('class','Education_Type')
            ->setAttrib('style','width:120px;');
            $eduType->setDecorators(array(
					'ViewHelper',
					'Description',
            	
            array(array('data' => 'HtmlTag'), array('tag' => 'td')),
            array(array('row' => 'HtmlTag'),array('tag' => 'tr', 'openOnly' => true,'id' => 'dynamicEduRow'.$id ))));
            $this->view->eduType = $eduType;

            $special = new Zend_Form_Element_Text('Specialisation');
            $special->setRequired(true)->setValue($specialisation);
            $special->setIsArray(true)->setAttrib('id','Specialisation'.$id)->setAttrib('class','Specialisation')->setAttrib('style','width:120px;');
            $special->setDecorators($sameRow);
            $this->view->special = $special;

            $insName = new Zend_Form_Element_Text('Institute_Name');
            $insName->setRequired(true)->setValue($instituteName);
            $insName->setIsArray(true)->setAttrib('id','Institute_Name'.$id)->setAttrib('class','Institute_Name')->setAttrib('style','width:110px;');
            $insName->setDecorators($sameRow);
            $this->view->insName = $insName;

            $univer = new Zend_Form_Element_Text('University');
            $univer->setIsArray(true)->setValue($university)->setAttrib('id','University'.$id)->setAttrib('class','University')->setAttrib('style','width:100px;');
            $univer->setDecorators($sameRow);
            $this->view->univer = $univer;

            $yrOfPassing = new Zend_Form_Element_Text('Year_Of_Passing');
            $yrOfPassing->setRequired(true)->setValue($yearOfPassing);
            $yrOfPassing->setIsArray(true)->setAttrib('id','yop'.$id)->setAttrib('class','Year_Of_Passing')->setAttrib('style','width:100px;');
            $yrOfPassing->setDecorators($sameRow);
            $this->view->yrOfPassing = $yrOfPassing;

            $percent = new Zend_Form_Element_Text('Percentage');
            $percent->setRequired(true)->setValue($percentage);
            $percent->setIsArray(true)->setAttrib('id','Percentage'.$id)->setAttrib('class','Percentage')
            ->setAttrib('style','width:100px;');
            $percent->setDecorators($sameRow);
            $this->view->percent = $percent;

            $grade = new Zend_Form_Element_Text('Grade');
            $grade->setRequired(true)->setAttrib('id','Distinction'.$id)->setAttrib('class','Distinction')->setValue($distinction);
            $grade->setIsArray(true)->setAttrib('style','width:50px;')
            ->setDescription('<a href="Javascript:void(0);"></a>&nbsp;<a href="Javascript:void(0);"><img id="delEducation'.$id.'"
					class="removeEducation" src="'.$this->_basePath->baseUrl('images/remove.png').'"></a>' );
            $grade->setDecorators(array('ViewHelper',array('Description', array('escape' => false, 'tag' => false),
            array('placement' => Zend_Form_Decorator_Abstract::APPEND, 'tag' => 'em')),
            array(array('data' => 'HtmlTag'), array('tag' => 'td','align' => 'left'))));
            $this->view->grade = $grade;
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }

    /**
     * This action is to add employee certificate dynamic fields
     * while click '+' image. ...
     * 
     * 
     *
     *
     *
     *
     */
    public function newcertificatefieldAction ()
    {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('newcertificatefield', 'html')->initContext();

            $sameRow = array(
					'ViewHelper',
					'Description',
            	
            array(array('data' => 'HtmlTag'),
            array('tag' => 'td','align' => 'left')));

            $certificateId = $this->_getParam('certificate_id', null);
            $certificateId = filter_var($certificateId, FILTER_SANITIZE_NUMBER_INT);

            $certificationName = $this->_getParam('Certification_Name', null);
            $certificationName = filter_var($certificationName, FILTER_SANITIZE_STRIPPED);

            $receivedDate = $this->_getParam('Received_Date', null);
            $receivedDate = filter_var($receivedDate, FILTER_SANITIZE_STRIPPED);

            $certificateReceivedFrom = $this->_getParam('Certificate_Received_From', null);
            $certificateReceivedFrom = filter_var($certificateReceivedFrom, FILTER_SANITIZE_STRIPPED);

            $certName = new Zend_Form_Element_Text('Certification_Name');
            $certName->setValue($certificationName);
            $certName->setIsArray(true)->setAttrib('id','Certification_Name'.$certificateId)->setAttrib('style','width:150px;')
            ->setAttrib('class','Certification_Name');
            $certName->setDecorators(array(
					'ViewHelper',
					'Description',
            	
            array(array('data' => 'HtmlTag'), array('tag' => 'td')),
            array(array('row' => 'HtmlTag'),array('tag' => 'tr', 'openOnly' => true,'id' => 'dynamicCertRow'.$certificateId ))))
            ->setAttrib('class','Certification_Name');
            $this->view->certName = $certName;

            $recdDate = new Zend_Form_Element_Text('Received_Date');
            $recdDate->setRequired(true)->setAttrib('id','Received_Date'.$certificateId)->setAttrib('class','Received_Date');
            $recdDate->setIsArray(true)
            ->setValue($receivedDate)->setAttrib('style','width:80px');
            $recdDate->setDecorators($sameRow);
            $this->view->recdDate = $recdDate;

            $recdFrom = new Zend_Form_Element_Text('Certificate_Received_From');
            $recdFrom->setRequired(true)->setAttrib('id','Certificate_Received_From'.$certificateId)->setAttrib('style','width:150px;')
            ->setAttrib('class','Certificate_Received_From');
            $recdFrom->setIsArray(true)->setValue($certificateReceivedFrom)->setAttrib('class','Certificate_Received_From')
            ->setDescription('<a href="Javascript:void(0);"><img id="delCertificate'.$certificateId.'" class="removeCertificate"
					src="'.$this->_basePath->baseUrl('images/remove.png').'"></a>' );
            $recdFrom->setDecorators(array('ViewHelper',array('Description', array('escape' => false, 'tag' => false),
            array('placement' => Zend_Form_Decorator_Abstract::APPEND, 'tag' => 'em')),
            array(array('data' => 'HtmlTag'), array('tag' => 'td','align' => 'left'))));
            $this->view->recdFrom = $recdFrom;
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }

    /**
     * This action is to add employee training dynamic fields
     * while click '+' image. ...
     */
    public function newtrainingfieldAction ()
    {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('newtrainingfield', 'html')->initContext();

            $sameRow = array(
					'ViewHelper',
					'Description',
            	
            array(array('data' => 'HtmlTag'),
            array('tag' => 'td','align' => 'left')));

            $trainingId = $this->_getParam('training_id', null);
            $trainingId = filter_var($trainingId, FILTER_SANITIZE_NUMBER_INT);

            $trainingName = $this->_getParam('Training_Name', null);
            $trainingName = filter_var($trainingName, FILTER_SANITIZE_STRIPPED);

            $trainingStartDate = $this->_getParam('Training_Start_Date', null);
            $trainingStartDate = filter_var($trainingStartDate, FILTER_SANITIZE_STRIPPED);

            $trainingEndDate = $this->_getParam('Training_End_Date', null);
            $trainingEndDate = filter_var($trainingEndDate, FILTER_SANITIZE_STRIPPED);

            $trainingDuration = $this->_getParam('Training_Duration', null);
            $trainingDuration = filter_var($trainingDuration, FILTER_SANITIZE_STRIPPED);

            $trainer = $this->_getParam('Trainer', null);
            $trainer = filter_var($trainer, FILTER_SANITIZE_STRIPPED);

            $center = $this->_getParam('Center', null);
            $center = filter_var($center, FILTER_SANITIZE_STRIPPED);

            $trainName = new Zend_Form_Element_Text('Training_Name');
            $trainName->setValue($trainingName);
            $trainName->setIsArray(true)->setAttrib('id','Training_Name'.$trainingId)->setAttrib('style','width:120px;');
            $trainName->setDecorators(array(
					'ViewHelper',
					'Description',
            	
            array(array('data' => 'HtmlTag'), array('tag' => 'td')),
            array(array('row' => 'HtmlTag'),array('tag' => 'tr', 'openOnly' => true,'id' => 'dynamicTrainRow'.$trainingId ))))
            ->setAttrib('class','Training_Name');
            $this->view->trainName = $trainName;

            $trainStartDate = new Zend_Form_Element_Text('Training_Start_Date');
            $trainStartDate->setAttrib('class','Training_Start_Date')->setValue($trainingStartDate)->setAttrib('style','width:80px');
            $trainStartDate->setIsArray(true)->setAttrib('id','Training_Start_Date'.$trainingId);
            $trainStartDate->setDecorators($sameRow);
            $this->view->trainStartDate = $trainStartDate;

            $trainEndDate = new Zend_Form_Element_Text('Training_End_Date');
            $trainEndDate->setAttrib('id','Training_End_Date'.$trainingId)->setAttrib('class','Training_End_Date');
            $trainEndDate->setIsArray(true)->setValue($trainingEndDate)->setAttrib('style','width:80px');
            $trainEndDate->setDecorators($sameRow);
            $this->view->trainEndDate = $trainEndDate;

            $trainDuration = new Zend_Form_Element_Text('Training_Duration');
            $trainDuration->setAttrib('id','Training_Duration'.$trainingId)->setValue($trainingDuration)->setDescription(' months ');
            $trainDuration->setIsArray(true)->setAttrib('style','width:50px;')->setAttrib('class','Training_Duration')->setAttrib('readonly','readonly');
            $trainDuration->setDecorators(array('ViewHelper',array('Description', array('escape' => false, 'tag' => false),
            array('placement' => Zend_Form_Decorator_Abstract::APPEND, 'tag' => 'em')),
            array(array('data' => 'HtmlTag'), array('tag' => 'td','align' => 'left','id' => 'trainDurCol'.$trainingId))));
            $this->view->trainDuration = $trainDuration;

            $trainerName = new Zend_Form_Element_Text('Trainer');
            $trainerName->setAttrib('id','Trainer'.$trainingId)->setValue($trainer);
            $trainerName->setIsArray(true)->setAttrib('style','width:100px;')->setAttrib('class','Trainer');
            $trainerName->setDecorators($sameRow);
            $this->view->trainerName = $trainerName;

            $centerName = new Zend_Form_Element_Text('Center');
            $centerName->setAttrib('id','Center'.$trainingId)->setValue($center)->setAttrib('class','Center');
            $centerName->setIsArray(true)->setAttrib('style','width:100px;')
            ->setDescription('<a href="Javascript:void(0);"><img id="delTraining'.$trainingId.'" class="removeTraining" src="'.$this->_basePath->baseUrl('images/remove.png').'"></a>' );
            $centerName->setDecorators(array('ViewHelper',array('Description', array('escape' => false, 'tag' => false),
            array('placement' => Zend_Form_Decorator_Abstract::APPEND, 'tag' => 'em')),
            array(array('data' => 'HtmlTag'), array('tag' => 'td','align' => 'left'))));
            $this->view->centerName = $centerName;
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }

    /**
     * This action is to add employee award dynamic fields
     * while click '+' image. ...
     *
     */
    public function newawardAction ()
    {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('newaward', 'html')->initContext();

            $sameRow = array(
					'ViewHelper',
					'Description',
            	
            array(array('data' => 'HtmlTag'),
            array('tag' => 'td','align' => 'left')));

            $awardId = $this->_getParam('award_id', null);
            $awardId = filter_var($awardId, FILTER_SANITIZE_NUMBER_INT);

            $awardName = $this->_getParam('Award_Name', null);
            $awardName = filter_var($awardName, FILTER_SANITIZE_STRIPPED);

            $receivedOn = $this->_getParam('Received_On', null);
            $receivedOn = filter_var($receivedOn, FILTER_SANITIZE_STRIPPED);

            $receivedFrom = $this->_getParam('Received_From', null);
            $receivedFrom = filter_var($receivedFrom, FILTER_SANITIZE_STRIPPED);

            $receivedFor = $this->_getParam('Received_For', null);
            $receivedFor = filter_var($receivedFor, FILTER_SANITIZE_STRIPPED);

            $awName = new Zend_Form_Element_Text('Award_Name');
            $awName->setAttrib('id','Award_Name'.$awardId)->setAttrib('style','width:150px');
            $awName->setIsArray(true)->setValue($awardName)->setAttrib('class','Award_Name');
            $awName->setDecorators(array(
					'ViewHelper',
					'Description',
            	
            array(array('data' => 'HtmlTag'), array('tag' => 'td')),
            array(array('row' => 'HtmlTag'),array('tag' => 'tr', 'openOnly' => true,'id' => 'dynamicAwardRow'.$awardId ))));
            $this->view->awName = $awName;

            $recdOn = new Zend_Form_Element_Text('Received_On');
            $recdOn->setAttrib('class','Received_On')->setAttrib('id','Received_On'.$awardId)->setAttrib('style','width:100px');
            $recdOn->setIsArray(true)->setValue($receivedOn);
            $recdOn->setDecorators($sameRow);
            $this->view->recdOn = $recdOn;

            $recdFrom = new Zend_Form_Element_Text('Received_From');
            $recdFrom->setAttrib('id','Received_From'.$awardId)->setValue($receivedFrom)->setAttrib('style','width:150px');
            $recdFrom->setIsArray(true)->setAttrib('class','Received_From');
            $recdFrom->setDecorators($sameRow);
            $this->view->recdFrom = $recdFrom;

            $recdFor = new Zend_Form_Element_Text('Received_For');
            $recdFor->setAttrib('id','Received_For'.$awardId)->setValue($receivedFor)->setAttrib('style','width:120px');
            $recdFor->setIsArray(true)->setAttrib('class','Received_For')
            ->setDescription('<a href="Javascript:void(0);"><img id="delAward'.$awardId.'" class="removeAward"
					src="'.$this->_basePath->baseUrl('images/remove.png').'"></a>' );
            $recdFor->setDecorators(array('ViewHelper',array('Description', array('escape' => false, 'tag' => false),
            array('placement' => Zend_Form_Decorator_Abstract::APPEND, 'tag' => 'em')),
            array(array('data' => 'HtmlTag'), array('tag' => 'td','align' => 'left'))));
            $this->view->recdFor = $recdFor;
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }

    /*public function createUserNameRecursive($userName, $digitCount, $empId = NULL)
    {
        // check if username has a number by each letter
        for($i =0;$i < 8;$i++)
        {
            $rest = substr($userName, $i, 1); //
            if (ctype_digit($rest)) // if letter is a number
            {
                if ($digitCount == 0)
                {
                    $noInUname = $rest;
                    $digitCount = $digitCount+1;
                }
                else
                {
                    $noInUname = $noInUname.$rest;
                    $digitCount = $digitCount+1;
                }
            }
        }
        if ($digitCount > 0) // if integer exists in username
        {
            $noAppended = $noInUname + 1; //
            $noCount = strlen($noAppended);
            $newUserName = substr($userName, 0, (8-$noCount)).$noAppended;
        }
        else
        {
            $newUserName = substr($userName, 0, 7).'1';
        }

        //checking weather the username already exist
        if (!is_null($empId))
        {
        	$loginDetails = $this->_dbPersonal->getEmpUsername($newUserName,$empId);
        }
        else
        {	 	
        	$loginDetails = $this->_dbPersonal->getEmpUsername($newUserName);
        }	
        $digitCount = 0;
        if ($loginDetails > 0) // if created username name exists, create another username
        {
            $newUname = $this->createUserNameRecursive($newUserName,$digitCount);
        }
        else
        {
            $newUname = $newUserName;
        }
        return $newUname;
    }*/
	
    /**
     * Check whether the entered employee details are already exists or not
     */
    public function checkSameAction ()
    {
        $this->_helper->layout->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('check-same', 'json')->initContext();

            $empId = $this->_getParam('empId', null);
            $empId = filter_var($empId, FILTER_SANITIZE_NUMBER_INT);
            $empFirstName = $this->_getParam('empFirstName', null);
            $empFirstName = filter_var($empFirstName, FILTER_SANITIZE_STRIPPED);
            $empLastName = $this->_getParam('empLastName', null);
            $empLastName = filter_var($empLastName, FILTER_SANITIZE_STRIPPED);
            $nationality = $this->_getParam('nationality', null);
            $nationality = filter_var($nationality, FILTER_SANITIZE_STRIPPED);
            $dob = $this->_getParam('dob', null);
            $dob = filter_var($dob, FILTER_SANITIZE_STRIPPED);
            $bloodGroup = $this->_getParam('bloodGroup', null);
            $bloodGroup = filter_var($bloodGroup, FILTER_SANITIZE_STRIPPED);
            $maritalStatus = $this->_getParam('maritalStatus', null);
            $maritalStatus = filter_var($maritalStatus, FILTER_SANITIZE_STRIPPED);

            $employeeId = $this->_dbEmployee->checkEmployeeExists($empId,$empFirstName,$empLastName,$nationality,$dob,$bloodGroup,$maritalStatus);
            $this->view->result = $employeeId;
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }

    /**
     *  Check whether the entered dependent details are already exists or not
     */
    public function checkDepDuplicate($dependentData)
    {
        $depDuplicate = 'false';
        $dependentDataCount = count($dependentData);
        for($i = 0;$i < $dependentDataCount;$i++)
        {
            for($j = 0;$j < $dependentDataCount;$j++)
            {
                if ($i != $j)
                {
                    if ($dependentData[$i]['dependentFirstName'] == $dependentData[$j]['dependentFirstName'] && $dependentData[$i]['dependentLastName'] == $dependentData[$j]['dependentLastName'] &&
                    $dependentData[$i]['dependentDOB'] == $dependentData[$j]['dependentDOB'] && $dependentData[$i]['dependentGender'] == $dependentData[$j]['dependentGender'] &&
                    $dependentData[$i]['dependentRelationship'] == $dependentData[$j]['dependentRelationship'] )
                    {
                        $i = count($dependentData);
                        $j = count($dependentData);
                        $depDuplicate = 'true';
                    }
                }
            }

        }
        if ($depDuplicate == 'true')
        {
            return true;
        }
        else
        {
            return false;
        }

    }

    /**
     * server side validation for dynamic asset...
     * @param array $assetData
     * @return boolean
     */
    public function checkDynamicAsset($addEmpFormData)
    {
        $trimFilter = new Zend_Filter_StringTrim();
        $tagsFilter = new Zend_Filter_StripTags();
        $alphaValidate = new Zend_Validate_Alpha(array('allowWhiteSpace' => true));
        $regexValidate = new Zend_Validate_Regex(array('pattern' => '/^[\w\-\ ]*$/'));
        $strLenValidate = new Zend_Validate_StringLength(array(3,20));
         
        $rowWithDynFields = array();
        $dupexist = array();
        if (!empty($addEmpFormData['JobForm']['Asset_Name0']))
        {
            for($m = 0;$m <= $addEmpFormData['JobForm']['asset_id']; $m++ )
            {
                $addEmpFormData['JobForm']['Asset_Name'.$m] = $trimFilter->filter($addEmpFormData['JobForm']['Asset_Name'.$m]);
                $addEmpFormData['JobForm']['Asset_Name'.$m] = $tagsFilter->filter($addEmpFormData['JobForm']['Asset_Name'.$m]);
                $addEmpFormData['JobForm']['Serial_No'.$m] = $trimFilter->filter($addEmpFormData['JobForm']['Serial_No'.$m]);
                $addEmpFormData['JobForm']['Serial_No'.$m] = $tagsFilter->filter($addEmpFormData['JobForm']['Serial_No'.$m]);
                $addEmpFormData['JobForm']['Receive_Date'.$m] = $trimFilter->filter($addEmpFormData['JobForm']['Receive_Date'.$m]);
                $addEmpFormData['JobForm']['Receive_Date'.$m] = $tagsFilter->filter($addEmpFormData['JobForm']['Receive_Date'.$m]);
                $addEmpFormData['JobForm']['Return_Date'.$m] = $trimFilter->filter($addEmpFormData['JobForm']['Return_Date'.$m]);
                $addEmpFormData['JobForm']['Return_Date'.$m] = $tagsFilter->filter($addEmpFormData['JobForm']['Return_Date'.$m]);
				$assetDtValid = true;
				$currDate = strtotime($this->_ehrTables->changeDateformat(date($this->_orgDateFormat['php'])));
				$assetRecDt = !empty($addEmpFormData['JobForm']['Receive_Date'.$m]) ? strtotime($this->_ehrTables->changeDateformat($addEmpFormData['JobForm']['Receive_Date'.$m])) : $addEmpFormData['JobForm']['Receive_Date'.$m];
				$assetRetDt = !empty($addEmpFormData['JobForm']['Return_Date'.$m]) ? strtotime($this->_ehrTables->changeDateformat($addEmpFormData['JobForm']['Return_Date'.$m])): $addEmpFormData['JobForm']['Return_Date'.$m];
				
				if (!empty($assetRecDt))
				{
					if ($assetRecDt <= $currDate)
					{
						if (!empty($assetRetDt))
						{
							if ($assetRetDt >= $assetRecDt)
							{
								$assetDtValid = true;
							}
							else $assetDtValid = false;
						}
						else $assetDtValid = true;
					}
					else	$assetDtValid = false;
				}
				else	$assetDtValid = true;
					
                if ($regexValidate->isValid($addEmpFormData['JobForm']['Asset_Name'.$m]) &&  $strLenValidate->isValid($addEmpFormData['JobForm']['Asset_Name'.$m])
                && $regexValidate->isValid($addEmpFormData['JobForm']['Serial_No'.$m]) &&  $strLenValidate->isValid($addEmpFormData['JobForm']['Serial_No'.$m]) &&
                		$assetDtValid)
                {
                    if (!empty($addEmpFormData['JobForm']['Asset_Name'.$m]) && !empty($addEmpFormData['JobForm']['Serial_No'.$m])) {
                        $rowWithDynFields[$m] = array($addEmpFormData['JobForm']['Asset_Name'.$m],$addEmpFormData['JobForm']['Serial_No'.$m],
                        							  $addEmpFormData['JobForm']['Receive_Date'.$m], $addEmpFormData['JobForm']['Return_Date'.$m]);
                    }
                }
                else
                {
                    return false;
                }
            }
            $currentRow = current($rowWithDynFields);
            foreach ($rowWithDynFields as $key => $row)
            {
                if ($key > 0 )
                {
                    count(array_diff_assoc($currentRow,$row)) < 1 ? array_push($dupexist, 1) : null;
                }
            }
            return (count($rowWithDynFields) < 1) ||  count($dupexist) > 0 ? false : true;
        }
        else {
            return true;
        }
         
    }

    /**
     * server side validation for dynamic insurnce...
     * @param array $assetData
     * @return boolean
     * 
     *
     *
     *
     */
    public function checkDynamicInsurance($addEmpFormData)
    {
        $desig = $this->_dbInsurance->getInsTypesByDesignation($addEmpFormData['JobForm']['Designation_Id']);
        $trimFilter = new Zend_Filter_StringTrim();
        $tagsFilter = new Zend_Filter_StripTags();
        $regexValidate = new Zend_Validate_Regex(array('pattern' => '/^[\w\-\ ]*$/'));
        $strLenValidate = new Zend_Validate_StringLength(array(3,20));
        $rowWithDynFields = array();
        $dupexist = array();
        
		if (!empty($addEmpFormData['payrollForm']['Insurance_Type0']))
		{
            for($m = 0;$m <= $addEmpFormData['payrollForm']['insurance_id']; $m++ )
            {
                $addEmpFormData['payrollForm']['Insurance_Type'.$m] = $trimFilter->filter($addEmpFormData['payrollForm']['Insurance_Type'.$m]);
                $addEmpFormData['payrollForm']['Insurance_Type'.$m] = $tagsFilter->filter($addEmpFormData['payrollForm']['Insurance_Type'.$m]);
                $addEmpFormData['payrollForm']['Policy_No'.$m] = $trimFilter->filter($addEmpFormData['payrollForm']['Policy_No'.$m]);
                $addEmpFormData['payrollForm']['Policy_No'.$m] = $tagsFilter->filter($addEmpFormData['payrollForm']['Policy_No'.$m]);
                 
                if (array_key_exists($addEmpFormData['payrollForm']['Insurance_Type'.$m], $desig) &&  $strLenValidate->isValid($addEmpFormData['payrollForm']['Insurance_Type'.$m])
                && $regexValidate->isValid($addEmpFormData['payrollForm']['Policy_No'.$m]) &&  $strLenValidate->isValid($addEmpFormData['payrollForm']['Policy_No'.$m]))
                {
                    if (!empty($addEmpFormData['payrollForm']['Insurance_Type'.$m]) && !empty($addEmpFormData['payrollForm']['Policy_No'.$m])) {
                        $rowWithDynFields[$m] = array($addEmpFormData['payrollForm']['Insurance_Type'.$m],$addEmpFormData['payrollForm']['Policy_No'.$m]);
                    }
                }
                else
                {
                    return false;
                }
            }
            $currentRow = current($rowWithDynFields);
            foreach ($rowWithDynFields as $key => $row)
            {
                if ($key > 0 )
                {
                    count(array_diff_assoc($currentRow,$row)) < 1 ? array_push($dupexist, 1) : null;
                }
            }
            return (count($rowWithDynFields) < 1) ||  count($dupexist) > 0 ? false : true;
        }
        else
		{
            return true;
        }
    }

    /**
     * To delete employee photo from server
     */
    public function removeImageAction ()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('remove-image', 'json')->initContext();
			
            if ($this->_gradeEmployeeAccess['Update'] == 1 || $this->_employeeAccess['Add'] == 1)
            {
            	$employeeId = $this->_getParam('employeeId', null);
            	$employeeId = filter_var($employeeId, FILTER_SANITIZE_STRIPPED);
            	if (!empty($employeeId))
            	{
					$this->view->result = $this->_dbEmployee->deletePhotoPath('', $employeeId);
            	}
				else{
					$this->view->result = array('success'=> false, 'msg' => 'Unable to delete Image !', 'ImageExists'=>1, 'type' => 'info');
				}
            }
			else{
				$this->view->result = array('success'=> false, 'msg' => 'Sorry, Access Denied..', 'ImageExists'=>1, 'type' => 'info');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
         
    }
	
	/**
	 *	getDesignationRolesAction () used to get designation roles.
	*/
	public function getDesignationRolesAction ()
    {
        $this->_helper->layout->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('get-designation-roles', 'html')->initContext();
			
			$designationId = $this->_getParam('Designation_Id', null);
                        $designationId = filter_var($designationId, FILTER_SANITIZE_NUMBER_INT);
			
			$employeeId = $this->_getParam('Employee_Id', null);
                        $employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
			
			$forms = $this->_dbModules->mainForms();
			$modules = $this->_dbModules->getModules('All');
			$subForms = $this->_dbModules->subForms();
				
			if (!empty($designationId) && $designationId > 0)
			{
				$formRoles = $this->_dbAccessRights->formRoles($designationId);
				$subformRoles = $this->_dbAccessRights->subFormRoles($designationId);
				$this->view->result = Zend_Json::encode(array('success' => true, 'forms' => $forms, 'modules' => $modules, 'subForms' => $subForms, 'formRoles' => $formRoles, 'subFormRoles' => $subformRoles ));
			}
			else if((!empty($employeeId) && $employeeId > 0))
			{
			        $formRoles = $this->_dbEmployee->formEmpRoles($employeeId);
                                $subformRoles = $this->_dbEmployee->subFormEmpRoles($employeeId);
				$this->view->result = Zend_Json::encode(array('success' => true, 'forms' => $forms, 'modules' => $modules, 'subForms' => $subForms, 'formRoles' => $formRoles, 'subFormRoles' => $subformRoles ));
                        }
			else
			{
				$this->view->result = Zend_Json::encode(array('success' => false, 'msg' => 'Invalid Data', 'type' => 'fatal', 'width' => '250'));
			}
		}
		else
		{
			$this->_helper->redirector('index', 'employees', 'employees');
		}
    }
	
	/* This action is not used */
    public function addEmployeeDetailsAction()
    {
		 $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('add-employee-details', 'json')->initContext();
			
			$employeeId = $this->_getParam('employeeId', null);
			$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
			$employeeId = $this->_validation->intValidation($employeeId);
			
			if ($this->_employeeAccess['Add'] == 1)
			{
				if ( $this->getRequest()->isPost() )
				{
					$formData = $this->getRequest()->getPost();
					
					$personalDataArr = $formData['personalData'];
					
					/**
					 *	Server side validation for Personal Information details
					*/
					$salutation = $this->_validation->alphaValidation($personalDataArr['Salutation']);
					
					$employeeFirstName    = $this->_validation->alSpDotValidation($personalDataArr['EmployeeFirstName']);
					$employeeMiddleName   = $this->_validation->alSpDotValidation($personalDataArr['EmployeeMiddleName']);
					$employeeLastName     = $this->_validation->alSpDotValidation($personalDataArr['EmployeeLastName']);
					$employeeNickName     = $this->_validation->onlyLetterSpaceValidation($personalDataArr['EmployeeNickName']);
					$hobbies              = $this->_validation->alphaSpNlCommaValidation($personalDataArr['Hobbies']);
					$ethnicRace           = $this->_validation->onlyLetterSpaceValidation($personalDataArr['EthnicRace']);
					$placeOfBirth         = $this->_validation->onlyLetterSpaceValidation($personalDataArr['PlaceOfBirth']);
					$gender               = $this->_validation->alphaValidation($personalDataArr['Gender']);
					$dateOfBirth          = $this->_validation->dateValidation($personalDataArr['DateOfBirth']);
					$maritalStatus        = $this->_validation->intValidation($personalDataArr['MaritalStatus']);
					$bloodGroup           = $this->_validation->commonFilters($personalDataArr['BloodGroup']);
					$languagesKnown       = $personalDataArr['LanguagesKnown'];
					$militaryService      = $this->_validation->checkboxValidation($personalDataArr['MilitaryService']);
					$isManager            = $this->_validation->checkboxValidation($personalDataArr['IsManager']);
					$physicallyChallenged = $this->_validation->checkboxValidation($personalDataArr['PhysicallyChallenged']);
					$personalEmail        = $this->_validation->emailValidation($personalDataArr['PersonalEmail']);
					$smoker               = $this->_validation->checkboxValidation($personalDataArr['Smoker']);
					$smokerAsOf           = $this->_validation->dateValidation($personalDataArr['SmokerAsOf']);
					$nationality          = $this->_validation->onlyLetterSpaceValidation($personalDataArr['Nationality']);
					$religion             = $this->_validation->onlyLetterSpaceValidation($personalDataArr['Religion']);
					$caste                = $this->_validation->onlyLetterSpaceValidation($personalDataArr['Caste']);
					$isLicense            = $this->_validation->checkboxValidation($personalDataArr['isLicense']);
					$isPassport           = $this->_validation->checkboxValidation($personalDataArr['isPassport']);
					// $isOverWriteUserName  = $this->_validation->checkboxValidation($personalDataArr['isOverWriteUserName']);
					// $userName             = $this->_validation->alNumWithoutSpaceValidation($personalDataArr['userName']);
					
					$employeeFirstName['valid']  = $this->_validation->lengthValidation($employeeFirstName, 3, 50, true);
					$employeeMiddleName['valid'] = $this->_validation->lengthValidation($employeeMiddleName, 1, 50, false);
					$employeeLastName['valid']   = $this->_validation->lengthValidation($employeeLastName, 1, 50, true);
					$employeeNickName['valid']   = $this->_validation->lengthValidation($employeeNickName, 3, 50, false);
					$hobbies['valid']            = $this->_validation->lengthValidation($hobbies, 3, 100, false);
					$ethnicRace['valid']         = $this->_validation->lengthValidation($ethnicRace, 3, 50, false);
					$placeOfBirth['valid']       = $this->_validation->lengthValidation($placeOfBirth, 3, 50, false);
					$nationality['valid']        = $this->_validation->lengthValidation($nationality, 3, 50, true);
					$religion['valid']           = $this->_validation->lengthValidation($religion, 3, 50, false);
					$caste['valid']              = $this->_validation->lengthValidation($caste, 2, 50, false);
					// $userName['valid']           = $this->_validation->lengthValidation($userName, 1, 15, true);
					
					$result = array('success' => true);
					
					/**
					 *	conditional validation for Personal Information details
					*/
					if (!empty($salutation['value']) && $salutation['valid'] && !empty($employeeFirstName['value']) && $employeeFirstName['valid'] &&
						$employeeMiddleName['valid'] && !empty($employeeLastName['value']) && $employeeLastName['valid'] && $employeeNickName['valid'] &&
						$hobbies['valid'] && $ethnicRace['valid'] && !empty($gender['value']) && $gender['valid'] && !empty($dateOfBirth['value']) &&
						$dateOfBirth['valid'] && $placeOfBirth['valid'] && !empty($maritalStatus['value']) && $maritalStatus['valid'] &&
						!empty($bloodGroup)  && $militaryService['valid'] && $isManager['valid'] &&
						$physicallyChallenged['valid'] && $smoker['valid'] &&
						(empty($personalEmail['value']) || (!empty($personalEmail['value']) && $personalEmail['valid'])) &&
						($smoker['value'] == 0 || ($smoker['value'] == 1 && !empty($smokerAsOf['value']) && $smokerAsOf['valid'])) &&
						!empty($nationality['value']) && $nationality['valid'] && $religion['valid'] && $caste['valid'] /*&&
						!empty($userName['value']) && $userName['valid']*/) 
					{
						/**
						 *	Check date of birth is valid
						*/
						$isValidDOB = $this->validDOB ($dateOfBirth['value']);
						
						if ($isValidDOB)
						{
							$result = array('success' => false, 'msg' => 'Invalid date of birth', 'type' => 'info');
						}
						
						/**
						 *	Check employee personal mail id is exists or not
						*/
						if ($result['success'] && !empty($personalEmail['value']) && $personalEmail['valid'])
						{
							$isEmailExist = $this->_dbPersonal->countEmpEmailId ($personalEmail['value'], $employeeId['value']);
							
							if ($isEmailExist > 0)
							{
								$result = array('success' => false, 'msg' => 'Employee Personal email id already exist', 'type' => 'info');
							}
						}
						
						/**
						 *	Check License details
						*/
						if ($result['success'] && $isLicense['value'])
						{
							$licenseNo               = $this->_validation->alphaNumSpHyValidation($personalDataArr['LicenseNo']);
							$licenseIssueDate        = $this->_validation->dateValidation($personalDataArr['LicenseIssueDate']);
							$licenseExpiryDate       = $this->_validation->dateValidation($personalDataArr['LicenseExpiryDate']);
							$licenseIssuingCountry   = $this->_validation->alphaValidation($personalDataArr['LicenseIssuingCountry']);
							$licenseIssuingAuthority = $this->_validation->onlyLetterSpaceValidation($personalDataArr['LicenseIssuingAuthority']);
							$licenseIssuingState     = $this->_validation->onlyLetterSpaceValidation($personalDataArr['LicenseIssuingState']);
							$vehicleType             = $this->_validation->alphaSpCommaDotAmpBracketValidation($personalDataArr['VehicleType']);
							
							$licenseNo['valid']               = $this->_validation->maxLengthValidation($licenseNo, 30, true);
							$licenseIssuingAuthority['valid'] = $this->_validation->lengthValidation($licenseIssuingAuthority, 3, 50, true);
							$licenseIssuingState['valid']     = $this->_validation->lengthValidation($licenseIssuingState, 3, 30, true);
							$vehicleType['valid']             = $this->_validation->lengthValidation($vehicleType, 3, 30, true);
							
							if (!empty($licenseNo['value']) && $licenseNo['valid'] && !empty($licenseIssueDate['value']) &&
								$licenseIssueDate['valid'] && !empty($licenseExpiryDate['value']) && $licenseExpiryDate['valid'] &&
								!empty($licenseIssuingCountry['value']) && $licenseIssuingCountry['valid'] && !empty($licenseIssuingAuthority['value']) &&
								$licenseIssuingAuthority['valid'] && !empty($licenseIssuingState['value']) && $licenseIssuingState['valid'] &&
								!empty($vehicleType['value']) && $vehicleType['valid'])
							{
								$isValidLicenseIssueDate  = $this->isLessDOB ($dateOfBirth['value'], $licenseIssueDate['value']);
								$isValidLicenseExpireDate = $this->isLessDOB ($dateOfBirth['value'], $licenseExpiryDate['value']);
								
								if ($isValidLicenseIssueDate || (date('Y-m-d') < $licenseIssueDate['value']))
								{
									$result = array('success' => false, 'msg' => 'Invalid license issue date', 'type' => 'info');
								}
								else if ($isValidLicenseExpireDate)
								{
									$result = array('success' => false, 'msg' => 'Invalid license expire date', 'type' => 'info');
								}
								else if ($licenseIssueDate['value'] >= $licenseExpiryDate['value'])
								{
									$result = array('success' => false, 'msg' => 'License issue date should be less than expire date', 'type' => 'info');
								}
								else if (date('Y-m-d') > $licenseExpiryDate['value'])
								{
									$result = array('success' => false, 'msg' => 'License already expired', 'type' => 'info');
								}
								else
								{
									$licenseData = array('Employee_Id'         => $employeeId['value'],
														 'Driving_License_No'  => $licenseNo['value'],
														 'License_Issue_Date'  => $licenseIssueDate['value'],
														 'License_Expiry_Date' => $licenseExpiryDate['value'],
														 'Issuing_Authority'   => $licenseIssuingAuthority['value'],
														 'Issuing_Country'     => $licenseIssuingCountry['value'],
														 'Issuing_State'       => $licenseIssuingState['value'],
														 'Vehicle_Type'        => $vehicleType['value']);
								}
							}
							else
							{
								$result = array('success' => false, 'msg' => 'Invalid license details', 'type' => 'info');
							}
						}
						
						/**
						 *	Check Passport details
						*/
						if ($result['success'] && $isPassport['value'])
						{
							$passportNo          = $this->_validation->onlyLetterNumberValidation($personalDataArr['PassportNo']);
							$passportNo['valid'] = $this->_validation->lengthValidation($passportNo, 1, 30, true);
							
							$passportIssueDate  = $this->_validation->dateValidation($personalDataArr['PassportIssueDate']);
							$passportExpiryDate = $this->_validation->dateValidation($personalDataArr['PassportExpiryDate']);
							
							if (!empty($passportNo['value']) &&$passportNo['valid'] && !empty($passportIssueDate['value']) &&
								$passportIssueDate['valid'] && !empty($passportExpiryDate['value']) && $passportExpiryDate['valid'])
							{
								if (($dateOfBirth['value'] >= $passportIssueDate['value']) || (date('Y-m-d') < $passportIssueDate['value']))
								{
									$result = array('success' => false, 'msg' => 'Invalid passport issue date', 'type' => 'info');
								}
								else if ($dateOfBirth['value'] >= $passportExpiryDate['value'])
								{
									$result = array('success' => false, 'msg' => 'Invalid passport expire date', 'type' => 'info');
								}
								else if ($passportIssueDate['value'] >= $passportExpiryDate['value'])
								{
									$result = array('success' => false, 'msg' => 'Passport issue date should be less than expire date', 'type' => 'info');
								}
								else if (date('Y-m-d') > $passportExpiryDate['value'])
								{
									$result = array('success' => false, 'msg' => 'Passport already expired', 'type' => 'info');
								}
								else
								{
									$passportData = array('Employee_Id' => $employeeId['value'],
														  'Passport_No' => $passportNo['value'],
														  'Issue_Date'  => $passportIssueDate['value'],
														  'Expiry_Date' => $passportExpiryDate['value']);
								}
							}
							else
							{
								$result = array('success' => false, 'msg' => 'Invalid passport details', 'type' => 'info');
							}
						}
						
					/**
					 *	If no server side validation error than we process update process
					*/
					if ($result['success'])
					{
						$formDataArr = array();
						
						/**
						 *	Get form status from table
						*/
						$formStatus = $this->_dbEmployee->getFormStatus ($employeeId['value']);
						
						if (empty($formStatus))
						{
							$formStatus = 0;
						}
						
						/**
						 *	Employee personal information
						*/
						$formDataArr['personalDataArr'] = array('Salutation'            => $salutation['value'],
																'Emp_First_Name'        => $employeeFirstName['value'],
																'Emp_Middle_Name'       => $this->fnCheckValue ($employeeMiddleName['value']),
																'Emp_Last_Name'         => $employeeLastName['value'],
																'Emp_Pref_First_Name'   => $this->fnCheckValue ($employeeNickName['value']),
																'Gender'                => $gender['value'],
																'DOB'                   => $dateOfBirth['value'],
																'Place_Of_Birth'        => $this->fnCheckValue ($placeOfBirth['value']),
																'Marital_Status'        => $maritalStatus['value'],
																'Military_Service'      => $militaryService['value'],
																'Ethnic_Race'           => $this->fnCheckValue ($ethnicRace['value']),
																'Nationality'           => $nationality['value'],
																'Religion'              => $this->fnCheckValue ($religion['value']),
																'Caste'                 => $this->fnCheckValue ($caste['value']),
																'Blood_Group'           => $bloodGroup,
																'Smoker'                => $smoker['value'],
																'Smokerasof'            => $this->fnCheckValue ($smokerAsOf['value']),
																'Is_Manager'            => $isManager['value'],
																'Physically_Challenged' => $physicallyChallenged['value'],
																'Form_Status'           => $formStatus,
																'Personal_Email'        => $this->fnCheckValue ($personalEmail['value']),
																//'Photo_Path'            => $path,
																// 'UserName_OverWrite'    => $isOverWriteUserName['value'],
																'Lock_Flag'             => 0);
						
						//Employee Language
						$formDataArr['languageDataArr'] = $languagesKnown;
						
						//employee hobbies
						if (!empty($hobbies['value']))
						{
							$formDataArr['hobbyDataArr'] = array('Employee_Id' => $employeeId['value'],
																 'Emp_Hobby'   => htmlentities ($hobbies['value']));
						}
						
						//Employee License details
						if ($isLicense['value'])
						{
							$formDataArr['licenseDataArr'] = $licenseData;
						}
						
						//Employee Passport details
						if ($isPassport['value'])
						{
							$formDataArr['passportDataArr'] = $passportData;
						}
						
						// if (!empty($loginData))
						// {
						// 	$formDataArr['loginDataArr'] = $loginData;
						// }
							$loginData = array(
												'Created_Date'   => date('Y-m-d H:i:s')
											);
							
							$formDataArr['loginDataArr'] = $loginData;
					        $result = $this->_dbEmployee->addEmployees($formDataArr, $this->_logEmpId);	
					}
					else
					{
						$result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
					}
							
					}
					else
					{
						
						$result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
					}
					$this->view->result = $result;
				}
				
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Sorry, Access denied', 'type' => 'danger');
			}
		}				
     }
	
	/** upload Employee Profile Photo **/
	public function uploadEmployeeProfilePhotoAction()
    {
        $this->_helper->layout->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
		    $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('upload-employee-profile-photo', 'json')->initContext();
			
			$employeeId = $this->_getParam('employeeId', null);
			$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
			$employeeId = $this->_validation->intValidation($employeeId);
			
			$extension = $this->_getParam('extension', null);
			$extension = filter_var($extension, FILTER_SANITIZE_STRIPPED);
			$extension = $this->_validation->alphaValidation($extension);
			
			if ((($this->_employeeAccess['Update'] == 1 ) || $this->_employeeAccess['Add'] == 1 ) &&
				 $extension['valid'] && $employeeId['valid'])
			{
							
				//File name should be unique.image path will be in the format employeeid.fileextension
				$result = $this->_dbEmployee->updateEmpPhoto($employeeId['value'].'.'.$extension['value'], $employeeId['value']);
				if($result)	{	
					$this->view->result = array('success' => true, 'employeePhotoPath'=>$employeeId['value'].'.'.$extension['value'] );
				} else{
					$this->view->result = array('success' => true, 'employeePhotoPath'=>$employeeId['value'].'.'.$extension['value'] );
				}
				
			}
			else{
				$this->view->result = array('success' => false, 'msg' => 'Sorry, Access denied', 'type' => 'danger');
			}
		}
		else{
			$this->_helper->redirector('index', 'employees', 'employees');
		}
    }
	
	//Update Employee as draft	
	public function updateEmployeeAsDraftAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if(isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('update-employee-as-draft', 'json')->initContext();
			
			if ($this->_employeeAccess['Update'] == 1 /*|| $this->_employeeSelfAccess['Update'] == 1*/ || $this->_employeeAccess['Add'] == 1)
			{
				if($this->getRequest()->isPost())
				{
					$formData = $this->getRequest()->getPost();
					
					$customField = $this->_ehrTables->getCustomFields();
					
					$pincodeCustom = $casteCustom = $ethnicRaceCustom = $aadhaarNoCustom = $panCustom = array();
					$pincodeCustom['Enable'] = $casteCustom['Enable'] = $ethnicRaceCustom['Enable'] = $aadhaarNoCustom['Enable'] = $panCustom['Enable']=  1;
					$pincodeCustom['Required'] = 1;
					$casteCustom['Required'] = $ethnicRaceCustom['Required'] = $aadhaarNoCustom['Required'] = $panCustom['Required'] = 0;
					
					
					foreach($customField as $custom)
					{
						if($custom['Field_Name'] == 'Pincode' )
						{
							$pincodeCustom['Enable'] = $custom['Enable'];
							$pincodeCustom['Required'] =  $custom['Required'];							
						}
						
						if($custom['Field_Name'] == 'Caste' )
						{
							$casteCustom['Enable'] = $custom['Enable'];
							$casteCustom['Required'] =  $custom['Required'];							
						}
						
						if($custom['Field_Name'] == 'Ethnic Race' )
						{
							$ethnicRaceCustom['Enable'] = $custom['Enable'];
							$ethnicRaceCustom['Required'] =  $custom['Required'];							
						}
						
						if($custom['Field_Name'] == 'Aadhaar Number' )
						{
							$aadhaarNoCustom['Enable'] = $custom['Enable'];
							$aadhaarNoCustom['Required'] =  $custom['Required'];							
						}
						
						if($custom['Field_Name'] == 'PAN No.' )
						{
							$panCustom['Enable'] =  $custom['Enable'];
							$panCustom['Required'] = $custom['Required'];
							$panCustom['Field_Name'] = ($custom['New_Field_Name'] != '' ?  $custom['New_Field_Name'] : $custom['Field_Name']);
						}
					}
					
					$bankDataArr    = $formData['bankData'];
					$jobDataArr         = $formData['jobData'];
					$personalDataArr = $formData['personalData'];
					//$employeePhotoPath    = $formData['photoPath'];
					
                    $btnClick = $formData['btnClick'];
					
					if($ethnicRaceCustom['Enable']){
                       $personalDataArr['EthnicRace'] = $personalDataArr['EthnicRace'];
					}
                    else
                    {
						$personalDataArr['EthnicRace'] = '';
                    }
                    
                    if($casteCustom['Enable']){
                        $personalDataArr['Caste'] = $personalDataArr['Caste'];
                    }
                    else
                    {
                        $personalDataArr['Caste'] = '';
                    }
					
					if($formData['employeeId'] > 0)
					{
						$employeeId = $formData['employeeId'];
					}
					else
					{
						$employeeId = $this->_dbEmployee->getMaxEmployeeId() + 1;
					}					
					
					
					$depDuplicate = "false";
					$duplBankDet = "false";
					 
					//To check whether entered bank account number and bank name already exists or not.
					// $bankAccNo = $bankDataArr['AccountNumber'];
					// $bankName = $bankDataArr['BankName'];
					
					// if(!empty($bankAccNo) && !empty($bankName))
					// {
					// 	if($formData['employeeId'] > 0){
					// 		$bankDet = $this->_dbPersonal->checkBankDetails($bankAccNo,$bankName,$formData['employeeId']);
					// 	}
					// 	else{
					// 		$bankDet = $this->_dbPersonal->checkBankDetails($bankAccNo,$bankName);	
					// 	}
						
					// 	if($bankDet>0)
					// 	{
					// 		$duplBankDet = "true";
					// 	}
					// }
					
					if($formData['employeeId'] > 0){
						$biometricIntegrationIdExist = $this->_dbCommonFunction->checkBiometricIntegrationIdExist($jobDataArr['ExternalEmployeeId'], $formData['employeeId']);
						$ckEmpEmailId = $this->_dbPersonal->countEmpEmailId($jobDataArr['EmployeeEmail'], $formData['employeeId']);
						$ckPersonalEmailId = $this->_dbPersonal->countEmpEmailId($personalDataArr['PersonalEmail'], $formData['employeeId']);
					}
					else{
						$biometricIntegrationIdExist = $this->_dbCommonFunction->checkBiometricIntegrationIdExist($jobDataArr['ExternalEmployeeId'], 0);
						$ckEmpEmailId = $this->_dbPersonal->countEmpEmailId($jobDataArr['EmployeeEmail'], 0);
						$ckPersonalEmailId = $this->_dbPersonal->countEmpEmailId($personalDataArr['PersonalEmail'], 0);
					}
					
					//To check whether employee firstname,lastname entered or not.
					if($duplBankDet == "true" || !empty($biometricIntegrationIdExist) || $ckEmpEmailId>0 || $ckPersonalEmailId>0)
					{
						$this->view->result = array('success' => false, 'msg' => 'Invalid employee details', 'type' => 'warning');	
					}
					else if(empty($personalDataArr['EmployeeFirstName']) || empty($personalDataArr['EmployeeLastName']))
					{
						$this->view->result = array('success' => false, 'msg' => 'Enter employee firstname and lastname', 'type' => 'warning');	
					}
					else
					{
						$empId = $formData['employeeId'];
						
						if(!empty($personalDataArr['EmployeeFirstName']) && !empty($personalDataArr['EmployeeLastName'])
						&& !empty($personalDataArr['Nationality']) && !empty($personalDataArr['DateOfBirth']) &&
						!empty($personalDataArr['BloodGroup']) && !empty($personalDataArr['MaritalStatus']))
						{
							$existEmployeeId = $this->_dbEmployee->checkEmployeeExists($empId,$personalDataArr['EmployeeFirstName'],
																						$personalDataArr['EmployeeLastName'],
																						$personalDataArr['Nationality'],
																						$personalDataArr['DateOfBirth'],
																						$personalDataArr['BloodGroup'],
																						$personalDataArr['MaritalStatus']);
						}
						
						if(!empty($existEmployeeId) && $existEmployeeId > 0)
						{
							$this->view->result = array('success' => false, 'msg' => 'Duplicate employee details', 'type' => 'warning');
						}
						else
						{
							//To check whether all required driving license details entered or not.
							if((!empty($personalDataArr['LicenseNo']) || !empty($personalDataArr['LicenseExpiryDate'])
							|| !empty($personalDataArr['LicenseIssuingCountry']) || !empty($personalDataArr['LicenseIssuingState'])
							|| !empty($personalDataArr['VehicleType'])) && $personalDataArr['isLicense']!="false")
							{
								
								$drivingLicenseNo = $personalDataArr['LicenseNo'];
								$licenseExpiryDate = $personalDataArr['LicenseExpiryDate'];
								$issuingCountry = $personalDataArr['LicenseIssuingCountry'];
								$issuingState = $personalDataArr['LicenseIssuingState'];
								$vehicleType = $personalDataArr['VehicleType'];
									
								$licenseArray = array();
								$licenseArray[0] = $drivingLicenseNo;
								$licenseArray[1] = $licenseExpiryDate;
								$licenseArray[2] = $issuingCountry;
								$licenseArray[3] = $issuingState;
								$licenseArray[4] = $vehicleType;
									
								//To filter empty values and compare.
								$filterArray = array_filter($licenseArray);
								$filterCount = count($filterArray);
								$licenseCount = count($licenseArray);
							}
							
							if (!empty($filterArray) && $filterCount < $licenseCount)
							{
								$this->view->result = array('success' => false, 'msg' => 'Enter all required driving license details', 'type' => 'warning');
							}
							else
							{
								if((empty($personalDataArr['PassportNo']) && !empty($personalDataArr['PassportExpiryDate']))
								|| (!empty($personalDataArr['PassportNo']) && empty($personalDataArr['PassportExpiryDate'])))
								{
									$this->view->result = array('success' => false, 'msg' => 'Enter all required passport details', 'type' => 'warning');
								}
								else
								{
									$dobValid = 0;
									
									if(!empty($personalDataArr['DateOfBirth']))
									{
										$dobYear     = date('Y', strtotime($personalDataArr['DateOfBirth']));
										$currentYear = date('Y') - 14;
										
										$dobValid = ((int)$dobYear > (int)$currentYear);
									}

									if(!empty($personalDataArr['DateOfBirth']) && $dobValid == 1)
									{
										$this->view->result = array('success' => false, 'msg' => 'Employee should be at least 14 years completed before starting an employment', 'type' => 'warning');
									}
									else
									{
											$beyondDOB = false;
											
										if(!empty($personalDataArr['DateOfBirth']) && !empty($jobDataArr['DateOfJoin']))
										{
											$beyondDOB = $this->isLessDOB($personalDataArr['DateOfBirth'], $jobDataArr['DateOfJoin']);
										}
										
										if($beyondDOB == true)
										{
											$this->view->result = array('success' => false, 'msg' => 'Date Of Birth should be less than the Date of Joining', 'type' => 'warning');
										}
										else
										{
											$allowUserSignin = $this->_validation->checkboxValidation($personalDataArr['allowUserSignin']);
											$enableSignInWithMobile = $this->_validation->checkboxValidation($personalDataArr['enableSignInWithMobile']);
											$workEmail = $this->_validation->emailValidation($personalDataArr['workEmail']);

												/* work email is mandatory when the allow user signin in true. */
												$empLoginUserName = '';

												if($allowUserSignin['value'] == 1 ){
													if($allowUserSignin['valid']){
														if($enableSignInWithMobile['value'] == 0){
															if( !$workEmail['value'] || !$workEmail['valid']){
																$this->view->result = array('success' => false, 'msg' => 'Invalid allow user sign in data', 'type' => 'info');
															} else {
																$empLoginUserName = $workEmail['value'];
															}
														}else{
															if(!$personalDataArr['signInMobileNumber']){
																$this->view->result = array('success' => false, 'msg' => 'Invalid allow user sign in data', 'type' => 'info');
															} else {
																$empLoginUserName = $personalDataArr['mobileNoCountryCode'].$personalDataArr['signInMobileNumber'];
															}
														}
													} else {
														$this->view->result = array('success' => false, 'msg' => 'Invalid allow user sign in data', 'type' => 'info');
													}
												}

												$empLoginData  = array( "Employee_Id" => $employeeId,
																		"User_Name" => $empLoginUserName ? $empLoginUserName : '',
																		"Created_Date" => date('Y-m-d H:i:s'));
												
												$formDataArr = array();
												
												/**
												 *	Get form status from table
												*/
												$formStatus = $this->_dbEmployee->getFormStatus ($employeeId);
												
												if (empty($formStatus))
												{
													$formStatus = 0;
												}
												
												/**
												 *	Employee personal information
												*/
												$formDataArr['personalDataArr'] = array('Employee_Id'			=> $employeeId,
																						'Salutation'            => $personalDataArr['Salutation'],
																						//'Photo_Path'            => $employeePhotoPath,
																						'Emp_First_Name'        => $personalDataArr['EmployeeFirstName'],
																						'Emp_Middle_Name'       => $this->fnCheckValue ($personalDataArr['EmployeeMiddleName']),
																						'Emp_Last_Name'         => $personalDataArr['EmployeeLastName'],
																						'Emp_Pref_First_Name'   => $this->fnCheckValue ($personalDataArr['EmployeeNickName']),
																						'Gender'                => $this->fnCheckValue ($personalDataArr['Gender']),
																						'DOB'                   => $this->fnCheckValue ($personalDataArr['DateOfBirth']),
																						'Place_Of_Birth'        => $this->fnCheckValue ($personalDataArr['PlaceOfBirth']),
																						'Marital_Status'        => $this->fnCheckValue ($personalDataArr['MaritalStatus']),
																						'Military_Service'      => $this->fnCheckValue ($personalDataArr['MilitaryService']),
																						'Ethnic_Race'           => $this->fnCheckValue ($personalDataArr['EthnicRace']),
																						'Nationality'           => $this->fnCheckValue ($personalDataArr['Nationality']),
																						'Religion'              => $this->fnCheckValue ($personalDataArr['Religion']),
																						'Caste'                 => $this->fnCheckValue ($personalDataArr['Caste']),
																						'Blood_Group'           => $this->fnCheckValue ($personalDataArr['BloodGroup']),
																						'Smoker'                => $personalDataArr['Smoker'],
																						'Smokerasof'            => $this->fnCheckValue ($personalDataArr['SmokerAsOf']),
																						'Is_Manager'            => $personalDataArr['IsManager'],
																						'Physically_Challenged' => $personalDataArr['PhysicallyChallenged'],
																						'Form_Status'           => $formStatus,
																						'Is_Illiterate'         => $personalDataArr['Illiterate'],
																						'Personal_Email'        => $this->fnCheckValue ($personalDataArr['PersonalEmail']),
																						'Allow_User_Signin'     => $allowUserSignin['value'],
																						'Work_Email'			=> $workEmail['value'],
																						'Enable_Sign_In_With_Mobile_No' => $enableSignInWithMobile['value'],
																						'Sign_In_Mobile_No_Country_Code'=> $personalDataArr['mobileNoCountryCode'],
																						'Sign_In_Mobile_Number' => $this->fnCheckValue ($personalDataArr['signInMobileNumber']));
												
												$formInfoArr['personalInfoArr'] = array(
																						'Salutation'            => $personalDataArr['Salutation'],
																						//'Photo_Path'            => $employeePhotoPath,
																						'Emp_First_Name'        => $personalDataArr['EmployeeFirstName'],
																						'Emp_Middle_Name'       => $this->fnCheckValue ($personalDataArr['EmployeeMiddleName']),
																						'Emp_Last_Name'         => $personalDataArr['EmployeeLastName'],
																						'Emp_Pref_First_Name'   => $this->fnCheckValue ($personalDataArr['EmployeeNickName']),
																						'Gender'                => $this->fnCheckValue ($personalDataArr['Gender']),
																						'DOB'                   => $this->fnCheckValue ($personalDataArr['DateOfBirth']),
																						'Place_Of_Birth'        => $this->fnCheckValue ($personalDataArr['PlaceOfBirth']),
																						'Marital_Status'        => $this->fnCheckValue ($personalDataArr['MaritalStatus']),
																						'Military_Service'      => $this->fnCheckValue ($personalDataArr['MilitaryService']),
																						'Ethnic_Race'           => $this->fnCheckValue ($personalDataArr['EthnicRace']),
																						'Nationality'           => $this->fnCheckValue ($personalDataArr['Nationality']),
																						'Religion'              => $this->fnCheckValue ($personalDataArr['Religion']),
																						'Caste'                 => $this->fnCheckValue ($personalDataArr['Caste']),
																						'Blood_Group'           => $this->fnCheckValue ($personalDataArr['BloodGroup']),
																						'Smoker'                => $personalDataArr['Smoker'],
																						'Smokerasof'            => $this->fnCheckValue ($personalDataArr['SmokerAsOf']),
																						'Is_Manager'            => $personalDataArr['IsManager'],
																						'Physically_Challenged' => $personalDataArr['PhysicallyChallenged'],
																						'Form_Status'           => $formStatus,
																						'Is_Illiterate'         => $personalDataArr['Illiterate'],
																						'Personal_Email'        => $this->fnCheckValue ($personalDataArr['PersonalEmail']),
																						'Allow_User_Signin'     => $allowUserSignin['value'],
																						'Work_Email'			=> $workEmail['value'],
																						'Enable_Sign_In_With_Mobile_No' => $enableSignInWithMobile['value'],
																						'Sign_In_Mobile_No_Country_Code'=> $personalDataArr['mobileNoCountryCode'],
																						'Sign_In_Mobile_Number' => $this->fnCheckValue ($personalDataArr['signInMobileNumber']));
												
												//Employee Language
												if (!empty($personalDataArr['LanguagesKnown']))
													$formDataArr['languageDataArr'] = $this->fnCheckValue ($personalDataArr['LanguagesKnown']);
												
												//employee hobbies
												if (!empty($personalDataArr['Hobbies']))
												{
													$formDataArr['hobbyDataArr'] = array('Employee_Id' => $employeeId,
																							'Emp_Hobby'   => htmlentities ($personalDataArr['Hobbies']));
												}
												
												//Employee License details
												if ($personalDataArr['isLicense'])
												{
													$formDataArr['licenseDataArr'] = array('Employee_Id'         => $employeeId,
																							'Driving_License_No'  => $personalDataArr['LicenseNo'],
																							'License_Issue_Date'  => $personalDataArr['LicenseIssueDate'],
																							'License_Expiry_Date' => $personalDataArr['LicenseExpiryDate'],
																							'Issuing_Country'     => $personalDataArr['LicenseIssuingCountry'],
																							'Issuing_Authority'   => $personalDataArr['LicenseIssuingAuthority'],
																							'Issuing_State'       => $personalDataArr['LicenseIssuingState'],
																							'Vehicle_Type'        => $personalDataArr['VehicleType']);
												}
												
												//Employee Passport details
												if ($personalDataArr['isPassport'])
												{
													$formDataArr['passportDataArr'] =  array('Employee_Id' => $employeeId,
																								'Passport_No' => $personalDataArr['PassportNo'],
																								'Issue_Date'  => $personalDataArr['PassportIssueDate'],
																								'Expiry_Date' => $personalDataArr['PassportExpiryDate']);
												}
												
												if (!empty($empLoginData))
												{
													$formDataArr['loginDataArr'] = $empLoginData;
												}

												$previousEmployeeExperience = $this->_dbEmployee->getPreviousExperienceInMonths($jobDataArr['Previous_Employee_Experience_Years'],$jobDataArr['Previous_Employee_Experience_Months']);

												//when we save the employee record as draft in that time service provider id comes as empty. 
												//we need to update the logged in employee id service provider id by default.
												if(empty($jobDataArr['Service_Provider_Id']))
												{
													$serviceProviderId = $this->_dbCommonFunction->getEmployeeServiceProviderId($this->_logEmpId,1);
												}
												else
												{
													$serviceProviderId = $jobDataArr['Service_Provider_Id'];
												}
												
												/**
												 *	Job Details
												*/
												/** For Biometric Integration Id, space has to be removed from both sides and Prefixed zero has to be removed
												 * to check that Biometric Integration Id already exists or not **/													
												$formDataArr['jobInfoDataArr'] = array('Employee_Id'         => $employeeId,
																						'EmpType_Id'          => $this->fnCheckValue ($jobDataArr['EmployeeType']),
																						'Department_Id'       => $this->fnCheckValue ($jobDataArr['DepartmentName']),
																						'Designation_Id'      => $this->fnCheckValue ($jobDataArr['DesignationName']),
																						'Date_Of_Join'        => $this->fnCheckValue ($jobDataArr['DateOfJoin']),
																						'Job_Code'            => $this->fnCheckValue ($jobDataArr['JobCode']),
																						'Emp_Email'           => $this->fnCheckValue ($jobDataArr['EmployeeEmail']),
																						'Manager_Id'          => $this->fnCheckValue ($jobDataArr['ManagerId']),
																						'Location_Id'         => $this->fnCheckValue ($jobDataArr['Location']),
																						'Confirmed'           => $jobDataArr['Confirmed'],
																						'Emp_Profession'      => $jobDataArr['IsDirector'],
																						'Emp_Status'          => $jobDataArr['EmployeeStatus'],
																						'TDS_Exemption'       => $jobDataArr['TDSExemption'],
																						'Attendance_Enforced_Payment' => $jobDataArr['AttendanceEnforcementPayment'],
																						'Confirmation_Date'   => $this->fnCheckValue ($jobDataArr['ConfirmationDate']),
																						'Probation_Date'      => $this->fnCheckValue ($jobDataArr['ProbationDate']),
																						'Commission_Employee' => $jobDataArr['CommissionEmployee'],
																						'Work_Schedule'       => $this->fnCheckValue ($jobDataArr['WorkSchedule']),
																						'User_Defined_EmpId'  => $this->fnCheckValue ($jobDataArr['UserDefinedEmpId']),
																						'Previous_Employee_Experience' => $previousEmployeeExperience,
																						'Service_Provider_Id' =>$this->fnCheckValue ($serviceProviderId),
																						'External_EmpId'      => $this->fnCheckValue($jobDataArr['ExternalEmployeeId']),
																						'Global_Resource_Id'  => $employeeId);
												
												if($jobDataArr['EmployeeStatus'] === 'Active'){
													$formDataArr['jobInfoDataArr']['Emp_InActive_Date']= new Zend_Db_Expr('NULL');
													$formDataArr['jobInfoDataArr']['Reason_Id']= 0;
												}else{
													$employeeInactiveDetails = $this->_dbEmployee->getJobEmpInactiveDetail($employeeId);
													$formDataArr['jobInfoDataArr']['Emp_InActive_Date'] = $employeeInactiveDetails['Emp_InActive_Date'];
													$formDataArr['jobInfoDataArr']['Reason_Id'] = $employeeInactiveDetails['Reason_Id'];
												}

												$contactDataArr  = $formData['contactData'];
												$careerDataArr   = $formData['careerData'];
												
												if($pincodeCustom['Enable']){
													$contactDataArr['PerZip'] = $contactDataArr['PerZip'];
													$contactDataArr['CurZip'] = $contactDataArr['CurZip'];
													$contactDataArr['OffZip'] = $contactDataArr['OffZip'];
												}
												else
												{
													$contactDataArr['PerZip'] = $contactDataArr['CurZip'] = $contactDataArr['OffZip'] = '';
												}
					
												/**
												 *	Contact Details
												*/
												$formDataArr['contactInfoDataArr'] = array('Employee_Id'      => $employeeId,
																							'pApartment_Name'  => $contactDataArr['PerApartmentName'],
																							'pStreet_Name'     => $contactDataArr['PerStreet'],
																							'pCity'            => $contactDataArr['PerCity'],
																							'pState'           => $contactDataArr['PerState'],
																							'pCountry'         => $contactDataArr['PerCountry'],
																							'pPincode'         => $contactDataArr['PerZip'],
																							'cApartment_Name'  => $contactDataArr['CurApartmentName'],
																							'cStreet_Name'     => $contactDataArr['CurStreet'],
																							'cCity'            => $contactDataArr['CurCity'],
																							'cState'           => $contactDataArr['CurState'],
																							'cCountry'         => $contactDataArr['CurCountry'],
																							'cPincode'         => $contactDataArr['CurZip'],
																							'oApartment_Name'  => $contactDataArr['OffApartmentName'],
																							'oStreet_Name'     => $contactDataArr['OffStreet'],
																							'oCity'            => $contactDataArr['OffCity'],
																							'oState'           => $contactDataArr['OffState'],
																							'oCountry'         => $contactDataArr['OffCountry'],
																							'oPincode'         => $contactDataArr['OffZip'],
																							'Land_Line_No'     => $contactDataArr['LandlineNo'],
																							'Mobile_No'        => $contactDataArr['MobileNo'],
																							'Fax_No'           => $contactDataArr['FaxNo'],
																							'Work_No'          => $contactDataArr['WorkNo']);
												
												/**
												 *	Career details
												*/
												
												/**
												 *	Skill Details
												*/
												if (!empty($careerDataArr['PrimarySkill']) || !empty($careerDataArr['SecondarySkill']) || !empty($careerDataArr['KnownSkills']) ||
													!empty($careerDataArr['HandsOn']))
												{
													$formDataArr['careerInfoDataArr'] = array('Employee_Id'     => $employeeId,
																								'Primary_Skill'   => $careerDataArr['PrimarySkill'],
																								'Secondary_Skill' => $careerDataArr['SecondarySkill'],
																								'Known_Skills'    => $careerDataArr['KnownSkills'],
																								'Hands_On'        => $careerDataArr['HandsOn']);
												}
												
												
												// if($pincodeCustom['Enable']){
												// 		$bankDataArr['BranchPincode'] = $bankDataArr['BranchPincode'];
												// }
												// else
												// {
												// 	$bankDataArr['BranchPincode'] = '';
												// }
												
												/**
												 *	Bank Account Details
												*/
												// if ($bankDataArr['isBankDetails'] == 'true')
												// {														
												// 	$formDataArr['bankInfoDataArr'] = array('Employee_Id'         => $employeeId,
												// 											'Bank_Account_Number' => $bankDataArr['AccountNumber'],
												// 											'Bank_Name'           => $bankDataArr['BankName'],
												// 											'Branch_Name'         => $bankDataArr['BranchName'],
												// 											'Street'              => $bankDataArr['BranchStreet'],
												// 											'City'                => $bankDataArr['BranchCity'],
												// 											'State'               => $bankDataArr['BranchState'],
												// 											'Zip'                 => $bankDataArr['BranchPincode'],
												// 											'Account_Type_Id'     => $bankDataArr['AccountType'],
												// 											'IFSC_Code'           => $bankDataArr['BranchIFSCCode']);
												// }
												
												
												//if (!empty($bankDataArr['PAN']))
												//{
												//	$formDataArr['bankInfoDataArr']['PAN'] = $bankDataArr['PAN'];
												//}
												
												/**
												 *	PAN 
												*/
												if($panCustom['Enable']){
													if (!empty($bankDataArr['PAN']))
													{
														$formDataArr['personalDataArr']['PAN'] = $bankDataArr['PAN'];
													}
												}
												else
												{
													$formDataArr['personalDataArr']['PAN'] = '';
												}
												
												/**
												 *	Aadhaar No
												*/
												if ($aadhaarNoCustom['Enable'])
												{   
													$formDataArr['personalDataArr']['Aadhaar_Card_Number'] = $bankDataArr['AadhaarNo'];
												}
												else
												{
													$formDataArr['personalDataArr']['Aadhaar_Card_Number'] = '';
												}
												
												/**
												*	UAN 
												*/
												if (!empty($bankDataArr['UAN']))
												{   
													$formDataArr['personalDataArr']['UAN'] = $bankDataArr['UAN'];
												}
												
												/**
												 *	provident Fund details
												*/
												if (!empty($bankDataArr['ProvidentFund']))
												{
													$formDataArr['jobInfoDataArr']['Pf_PolicyNo'] = $bankDataArr['ProvidentFund'];
												}
												$empId= $formData['empId'];
												if($formData['employeeId'] > 0)
												{
													$mode = 'save';
												}
												else
												{
													$mode = 'Draft';	
												}
												$customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
												$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
												
												$userDefinedEmployeeIdExist = $this->_dbCommonFunction->checkUserDefinedEmployeeIdExist ($formDataArr['jobInfoDataArr']['User_Defined_EmpId'],$employeeId);
												if(!empty($userDefinedEmployeeIdExist))
												{
													$this->view->result = array('success' => false, 'msg' => 'Employee Id already exist', 'type' => 'info');
												}
												else
												{
													/** If Biometric Integration Id exists **/
													if (!empty($formDataArr['jobInfoDataArr']['External_EmpId']))
													{														
														/** Check that Biometric Integration Id already exists in employee form **/
														$biometricIntegrationIdExist = $this->_dbCommonFunction->checkBiometricIntegrationIdExist($formDataArr['jobInfoDataArr']['External_EmpId'], $employeeId);
														if (!empty($biometricIntegrationIdExist))
														{
															$this->view->result = array('success' => false, 'msg' => 'Biometric Integration Id already exist', 'type' => 'warning');
														}
														else{															
															$this->view->result = $this->_dbEmployee->updateEmployees ($formDataArr, $employeeId, $this->_logEmpId, $mode, $formName,$btnClick,$formInfoArr, $empId);
														}
													}
													else
													{														
														$this->view->result = $this->_dbEmployee->updateEmployees ($formDataArr, $employeeId, $this->_logEmpId, $mode, $formName,$btnClick,$formInfoArr,$empId);
													}
												}
										}
									}
								}
							}
						}
					}
				}
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Sorry, Access denied', 'type' => 'danger');
			}
		}
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }

	public function employeeidExistAction()
	{
		 $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('employeeid-exist', 'html')->initContext();
			
			if ($this->getRequest()->isPost())
			{
				$formData						= $this->getRequest()->getPost();
				$userDefinedEmployeeId   		= $this->_validation->alphaNumSpCDotHySlashValidation($formData['userDefinedEmployeeId']);
				$userDefinedEmployeeId['valid'] = $this->_validation->lengthValidation($userDefinedEmployeeId,1,50,true);
				$employeeId   					= $this->_validation->intValidation($formData['employeeId']);
				if (!empty($userDefinedEmployeeId['value']) && $userDefinedEmployeeId['valid'])
				{
					$userDefinedEmployeeIdExist = $this->_dbCommonFunction->checkUserDefinedEmployeeIdExist ($userDefinedEmployeeId['value'],$employeeId['value']);
					if(!empty($userDefinedEmployeeIdExist))
					{
						$this->view->result = array('success' => false, 'msg' => 'Employee Id already exist', 'type' => 'info');
					}
					else
					{
						$this->view->result = array('success' => true);
					}
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid Data', 'type' => 'warning');
				}
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Invalid Data', 'type' => 'warning');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
	}
	
    public function mailExistAction()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('mail-exist', 'html')->initContext();
			
            $employeeId = $this->_getParam('EmployeeId', null);
            $employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
            
            $emailId = $this->_getParam('EmailId', null);
            $emailId = filter_var($emailId, FILTER_SANITIZE_STRIPPED);
			
            if (!empty($emailId))
            {
                $result = $this->_dbPersonal->countEmpEmailId ($emailId, $employeeId);
                
                if ($result > 0)
                    $this->view->result = array('success' => false, 'msg' => 'Employee email id already exists', 'type' => 'info');
                else
                    $this->view->result = array('success' => true);
                
            }
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
	}
	
	public function mobileNumberExistAction()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('mobile-number-exist', 'html')->initContext();
			
            $employeeId = $this->_getParam('EmployeeId', null);
            $employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
            
            $mobileNumber = $this->_getParam('MobileNumber', null);
            $mobileNumber = filter_var($mobileNumber, FILTER_SANITIZE_STRIPPED);

			if (!empty($mobileNumber))
            {
                $result = $this->_dbPersonal->countEmpMobileNumber ($mobileNumber, $employeeId);

				if ($result > 0)
                    $this->view->result = array('success' => false, 'msg' => 'Employee mobile number already exists', 'type' => 'info');
                else
                    $this->view->result = array('success' => true);
                
            }
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
	}
	
	//To get the parent grade
	public function getParentGradeAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if(isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('get-parent-grade', 'json')->initContext();
			
			$gradeId = $this->_getParam('Grade_Id', null);
            $gradeId = filter_var($gradeId, FILTER_SANITIZE_NUMBER_INT);
            
			$this->view->result = $this->_dbGrade->listParentGrade ($gradeId);
			
        }
		else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	public function getProbationDateAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if(isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('get-probation-date', 'json')->initContext();
			
			$designationId = $this->_getParam('designationId', null);
            $designationId = filter_var($designationId, FILTER_SANITIZE_NUMBER_INT);
			
			$dateOfJoin = $this->_getParam('dateOfJoin', null);
			$dateOfJoin  = $this->_validation->dateValidation($dateOfJoin);
			
			if($dateOfJoin['valid'])
			{
				$this->view->result = $this->_dbDesignation->getProbationDate($designationId,$dateOfJoin['value']);	
			}
        }
		else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
	 *	clone employee 
	*/
	public function cloneEmployeeAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('clone-employee', 'json')->initContext();
			
            if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				$employeeId   = $this->_validation->alphaNumSpCDotHySlashValidation($formData['cloneUserDefinedEmployeeId']);
				$employeeId['valid'] = $this->_validation->lengthValidation($employeeId,1,50,true);

				$cloneBiometricIntegrationId   		  = $this->_validation->onlyLetterNumberValidation($formData['cloneBiometricIntegrationId']);
				$cloneBiometricIntegrationId['valid'] = $this->_validation->lengthValidation($cloneBiometricIntegrationId,1,15,false);
				
				$dateOfJoining  = $this->_validation->dateValidation($formData['doj']);
				$designationId= $this->_validation->intValidation($formData['Designation_Id']);
				$departmentId= $this->_validation->intValidation($formData['Department_Id']);
				$employeeType= $this->_validation->intValidation($formData['Employee_Type_Id']);
				$locationId= $this->_validation->intValidation($formData['Location_Id']);
				$workScheduleId= $this->_validation->intValidation($formData['WorkSchedule_Id']);

				$serviceProviderId= $this->_validation->intValidation($formData['Service_Provider_Id']);
		
				$old_emp_Id  = $this->_validation->intValidation($formData['old_emp_id']);

				$userIpAddress = $formData['ipAddress'];
				
				if (!empty($employeeId['value']) && $employeeId['valid'] && ((!empty($cloneBiometricIntegrationId['value']) 
				    && $cloneBiometricIntegrationId['valid']) || empty($cloneBiometricIntegrationId['value'])) &&
					!empty($dateOfJoining['value']) && $dateOfJoining['valid']&& !empty($old_emp_Id['value']) && $old_emp_Id['valid'] &&
					(($this->_orgDetails['Field_Force']==1 && !empty($serviceProviderId['value']) && $serviceProviderId['valid']) || 
					($this->_orgDetails['Field_Force']==0 && empty($serviceProviderId['value']) && $serviceProviderId['valid']))
					&& (!empty($designationId['value']) && $designationId['valid'])
					&& (!empty($employeeType['value']) && $employeeType['valid'])
					&& (!empty($departmentId['value']) && $departmentId['valid'])
					&& (!empty($locationId['value']) && $locationId['valid'])
					&& (!empty($workScheduleId['value']) && $workScheduleId['valid']))
				{
					$cloneData = array('User_Defined_EmpId'          => $employeeId['value'],
										'DOJ'              => $dateOfJoining['value'],
										'Old_Employee_Id'  => $old_emp_Id['value'],
										'Designation_Id'   =>$designationId['value'],
										'Department_Id'    =>$departmentId['value'],
										'EmpType_Id'       =>$employeeType['value'],
										'Location_Id'      =>$locationId['value'],
										'Work_Schedule'    =>$workScheduleId['value'],
										'External_EmpId'   =>$cloneBiometricIntegrationId['value'],
										'Service_Provider_Id'=>$this->fnCheckValue($serviceProviderId['value']));
					
					$this->view->result = $this->_dbEmployee->cloneEmployee ($cloneData, $old_emp_Id['value'], $userIpAddress, $this->_logEmpId);
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid Data', 'type' => 'warning');
				}
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
	 *	Clone Roles Designation Level
	*/
	public function cloneRolesAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('clone-roles', 'json')->initContext();
			
            if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				$currentDesigId   = $this->_validation->intValidation($formData['currentDesigId']);
				$designation  = $formData['designation'];
				
				if (!empty($currentDesigId['value']) && $currentDesigId['valid'])
				{
					
					$this->view->result = $this->_dbEmployee->cloneRoles ($designation, $currentDesigId['value'], $this->_logEmpId);
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid Data', 'type' => 'warning');
				}
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	/**
	 *	Get designation names for multi select combo in clone roles designation level
	*/
	public function getDesignationPairsAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('get-designation-pairs', 'json')->initContext();
			
            if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				
				$designationId = $this->_getParam('designationId');
				$designationId = $this->_validation->intValidation($designationId);
				
				if (!empty($designationId['value']) && $designationId['valid'])
				{
					
					$this->view->result = $this->_dbDesignation->getDesignationPairs(NULL, $designationId['value']);
				}
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	
	/**
	 *	Get employee names for multi select combo in clone roles employee level
	*/
	public function getEmployeeNamesAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('get-employee-names', 'json')->initContext();
			
            if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				
				$employeeId = $this->_getParam('employeeId');
				$employeeId = $this->_validation->intValidation($employeeId);
				
				if (!empty($employeeId['value']) && $employeeId['valid'])
				{
					
					$this->view->result = $this->_dbEmployee->getEmployeeNames($employeeId['value']);
				}
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	
	
	/**
	 *	Clone Roles Employee Level
	*/
	public function cloneRolesEmployeeAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('clone-roles-employee', 'json')->initContext();
			
            if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				$currentEmpId   = $this->_validation->intValidation($formData['currentEmpId']);
				$employeeIds  = $formData['employeeIds'];
				
				
				if (!empty($currentEmpId['value']) && $currentEmpId['valid'])
				{
					
					$this->view->result = $this->_dbEmployee->cloneRolesEmployee ($employeeIds, $currentEmpId['value'], $this->_logEmpId);
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid Data', 'type' => 'warning');
				}
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	public function setHrappGlobalValueAction()
    {
        $this->_helper->layout()->disableLayout();
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('set-hrapp-global-value', 'json')->initContext();
			
			$hrappGlobalValue = $this->_getParam('Hrapp_Global_Value');
			$hrappGlobalValue = $this->_validation->intValidation($hrappGlobalValue);
			 
            setcookie('HrappDepartmentClassificationId',$hrappGlobalValue['value'],time() +(8640000 * 30),'/','', true);
        
		}
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }
	public function getMaxEmployeeIdAction()
    {
        // action body
		  $this->_helper->layout()->disableLayout();
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('get-max-employee-id', 'json')->initContext();
			
			$hrappGlobalValue = $this->_getParam('Hrapp_Global_Value');
			$hrappGlobalValue = $this->_validation->intValidation($hrappGlobalValue);
			
			if($_COOKIE['HrappDepartmentClassificationId'] > 0 && $hrappGlobalValue['value'] > 0)
            {
				if($hrappGlobalValue['value']== $_COOKIE['HrappDepartmentClassificationId'])
				{
					$this->view->result = $this->_dbEmployee->getDivisionMaxEmployeeId ($hrappGlobalValue['value']);
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid Department Classification Details', 'type' => 'warning');
				}
			}
			else
			{
			    $maxEmployeeId = $this->_dbEmployee->getMaxEmployeeId ();
				$this->view->result = $maxEmployeeId+1;
			}        
		}
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
	}
	

	public function updateDepartmentLevelAccessAction()
    {
		$this->_helper->layout()->disableLayout();
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('update-department-level-access', 'json')->initContext();
			if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				$currentEmpId   = $this->_validation->intValidation($formData['currentEmployeeId']);
				$parentDepartmentId  = $formData['parentId'];

  			   if (!empty($currentEmpId['value']) && $currentEmpId['valid'])
				{
					
					$this->view->result = $this->_dbEmployee->departmentLevelAccess($parentDepartmentId, $currentEmpId['value'], $this->_logEmpId);
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid Data', 'type' => 'warning');
				}
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
		
	}

    public function validateEmployeeDojUsedAction()
    {
        $this->_helper->layout()->disableLayout();
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('validate-employee-doj-used', 'json')->initContext();
			if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				$employeeId   = $this->_validation->intValidation($formData['employeeId']);
				$employeeDoj   = $this->_validation->dateValidation($formData['employeeDoj']);

  			   	if (!empty($employeeId['value']) && $employeeId['valid'] && !empty($employeeDoj['value']) && $employeeDoj['valid']){					
					$employeeDojUsedResult = $this->_dbEmployee->validateEmployeeDojUsed($employeeId['value'], $employeeDoj['value']);

					$this->view->result = array('success' => true, 'employeeDojUsedDetails'=> $employeeDojUsedResult, 'msg' => 'Employee DOJ used details retrieved successfully', 'type' => 'success');
				}
				else{
					$this->view->result = array('success' => false, 'employeeDojUsedDetails'=> array(), 'msg' => 'Invalid Data', 'type' => 'warning');
				}
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees', 'employees');
        }
    }

	public function __destruct()
    {
        
    }
}