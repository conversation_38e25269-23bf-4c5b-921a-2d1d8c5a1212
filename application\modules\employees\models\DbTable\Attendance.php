<?php
//===========================================================================================
//===========================================================================================
/* Program : Attendance.php											   			           *
 * Property of Caprice Technologies Pvt Ltd,                                               *
* Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                         *
* Coimbatore, Tamilnadu, India.														       *
* All Rights Reserved.            														   *
* Use of this material without the express consent of Caprice Technologies                 *
* or assignees is unlawful and subject to prosecution to the fullest extent of the law.    * 
*                                                                                    	   *
* Description : MQL Query to retrive, add, update attendance details and also to           *
* update status reports. Mysql Query to retrive imported attendance and attendance import  *
* format, process imported attendance and to update attendance status and rollup status    *
*                                                                                   	   *
*                                                                                    	   *
* Revisions :                                                                    	       *
*  Version    Date           Author                  Description                           *
*  0.1        30-May-2013    Shobana            	  Initial Version        	           *
*  0.2        16-Sep-2013    Narmadha                Attendance Import                     *
*  0.3        23-Jan-2014    Mahesh                  Added function                        *
*                                                    1.getleaveApplied                     *
*  0.4		   14-Apr -2014   Sandhosh                Added funtions:					   *
*										     	      1.getAttendanceInLeaveRange,         *
*													  2.attendanceDateFormat			   *
*																						   *
*                                                    Modified Functions:                   *
*                                        		      1.tableHeader                        *
*                                        		      2.getAttendanceImportStatus          *
*																						   *
*  0.5		   25-Jul-2014	  Mahesh			      Added copyAttendance()			   *
*																						   *
*  0.6		   18-Sep-2014    Mahesh				  Added Function 					   *
*													  1.updateImportedData 			       *
*													  Modified Functions				   *
* 													  1.readAttendanceImport			   *
* 													  									   *
*  1.0        02-Feb-2015    Dhanabal                Changes in file for mobile app        *
*                                                    1.Extra fields are added in           *
*                                                    field list of list query.             *
*                                                                                          *
*  1.5        20-Feb-2016    Deepak             Changed in file for Bootstrap              *
*                                                                                          */
//===========================================================================================
//===========================================================================================
class Employees_Model_DbTable_Attendance extends Zend_Db_Table_Abstract
{
	protected $_orgDF = null;
	protected $_db = null;
	protected $_ehrTables = null;
	protected $_dbTimesheetHrs = null;
	protected $_dbComment = null;
	protected $_dbPersonal = null;
	protected $_dbCommonFun = null;
	protected $_dbPayslip   = null;
	
	public function init()
	{
		$this->_db = Zend_Registry::get('subHrapp');
		
		$this->_dbCommonFun = new Application_Model_DbTable_CommonFunction();
		$this->_ehrTables = new Application_Model_DbTable_Ehr();
		$this->_dbTimesheetHrs = new Employees_Model_DbTable_TimesheetHours();
		$this->_dbComment = new Payroll_Model_DbTable_PayrollComment();
		$this->_dbPersonal = new Employees_Model_DbTable_Personal();
		$this->_orgDF = $this->_ehrTables->orgDateformat();
		$this->_dbPayslip = new Payroll_Model_DbTable_Payslip();
		$this->_dbWorkSchedule = new Organization_Model_DbTable_WorkSchedule();
		if (Zend_Registry::isRegistered('orgDetails'))
             $this->_orgDetails = Zend_Registry::get('orgDetails');
        
	}
	/* check employee has punchin record for that given time range*/
	public function checkEmployeeAttendance ($employeeId)
	{
		$currentWorkScheduleDetails = array();
	
		if (!empty($employeeId))
		{
			$currentWorkScheduleDetails = $this->getCurrentWorkScheduleDetails($employeeId,date('Y-m-d H:i:s'));

			if(!empty($currentWorkScheduleDetails))
			{
				$employeeDetails     = $this->getEmployeeAttendanceDetails($employeeId);
				$getDraftAttCount    = array();		
				
				if($this->_orgDetails['Multiple_Source_Attendance_Same_Time']=='Yes' && !empty($employeeDetails) && !empty($employeeDetails['External_EmpId']))
				{
					$getDraftAttCountQry = $this->_db->select()->from(array('AI'=>$this->_ehrTables->attendanceImport),array('Attendance_Status','Added_On'))
												->where('Employee_Id = ?', $employeeDetails['External_EmpId'])
												->where('AI.Added_On >= ?', $currentWorkScheduleDetails['Consideration_From'])
												->where('AI.Added_On <= ?', $currentWorkScheduleDetails['Consideration_To'])
												->where('Rollup_Flag = ?',0);
												
					$getDraftAttCount = $this->_db->fetchAll($getDraftAttCountQry);

					if(!empty($getDraftAttCount))
					{
						$addedOn = array_unique(array_column($getDraftAttCount,'Added_On'));
						// Convert date-time strings to timestamps
						$timestamps = array_map(function ($dateTime) {
							return strtotime($dateTime);
						}, $addedOn);
						// Find the maximum timestamp
						$maxTimestamp = max($timestamps);
						 
						// Find the index of the maximum timestamp in the original array
						$maxIndex = array_search($maxTimestamp, $timestamps);
						// Get the corresponding Attendance_Status
						$maxAttendanceStatus = $getDraftAttCount[$maxIndex]['Attendance_Status'];
						
						if($maxAttendanceStatus=='C/In')
						{
							// Convert the maximum timestamp back to a date-time string
							$getDraftAttCount['Attendance_PunchIn_Date'] = date('Y-m-d H:i:s', $maxTimestamp);
						
							$getDraftAttCount['Attendance_Id'] = 0;
							$currentDateTime = date('Y-m-d H:i:s');
							if(strtotime($currentDateTime)>strtotime($getDraftAttCount['Attendance_PunchIn_Date']))
								$getDraftAttCount['TimeSec'] = strtotime($currentDateTime) - strtotime($getDraftAttCount['Attendance_PunchIn_Date']);
							else 
								$getDraftAttCount['TimeSec'] = 0;
							return $getDraftAttCount;	
						}
						else
						{
							return array();
						}
					}
					else
					{
						return array();
					}
				}
				else
				{
					$qryAttendanceMaxDate = $this->_db->select()->from(array('A'=>$this->_ehrTables->attendance),array('Attendance_Max_Date'=>new Zend_Db_Expr('MAX(Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T"))')))
						->where('Employee_Id = ?', $employeeId)
						->where('Approval_Status = ?',"Draft")
						->where(new Zend_Db_Expr('Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T")').' >= ?', $currentWorkScheduleDetails['Consideration_From'])
						->where(new Zend_Db_Expr('Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T")').' <= ?', $currentWorkScheduleDetails['Consideration_To']);

					$attendanceMaxDate = $this->_db->fetchOne($qryAttendanceMaxDate);
					
					
					if(!empty($attendanceMaxDate))
					{
						$getDraftAttCountQry = $this->_db->select()->from(array('att'=>$this->_ehrTables->attendance),array('Attendance_Id','PunchOut_Date',
																	'Attendance_PunchIn_Date'=>new Zend_Db_Expr('(Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T"))')))
																	->where('Employee_Id = ?', $employeeId)
																	->where('Approval_Status = ?',"Draft")
																	->where(new Zend_Db_Expr('Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T")').' = ?', $attendanceMaxDate);

						$getDraftAttCount = $this->_db->fetchRow($getDraftAttCountQry);
						
						if(!empty($getDraftAttCount))
						{
							$currentDateTime = date('Y-m-d H:i:s');
							if(strtotime($currentDateTime)>strtotime($getDraftAttCount['Attendance_PunchIn_Date']))
								$getDraftAttCount['TimeSec'] = strtotime($currentDateTime) - strtotime($getDraftAttCount['Attendance_PunchIn_Date']);
							else 
								$getDraftAttCount['TimeSec'] = 0;
						}
						return $getDraftAttCount;
					}
					else 
					{
						return $getDraftAttCount;
					}
				}			
			}
			else 
			{
				return $currentWorkScheduleDetails;
			}
		}
		else 
		{
			return $currentWorkScheduleDetails;
		}
	}
	
	/* get the checkin and checkout record for current date*/
	public function getCurrentDateAttendance ($employeeId)
	{
		$getAttendanceDetails = array();
		if (!empty($employeeId))
		{
			$currentWorkScheduleDetails = $this->getCurrentWorkScheduleDetails($employeeId,date('Y-m-d H:i:s'));

			if(!empty($currentWorkScheduleDetails))
			{
				$getAttendanceDetails = $this->_db->select()->from(array('att'=>$this->_ehrTables->attendance),array('AttendancePunchInDate'=>new Zend_Db_Expr('(Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "'.$this->_orgDF['sql'].' %T"))'),
										'AttendancePunchOutDate'=>new Zend_Db_Expr('(Date_Format(Concat(PunchOut_Date," ",PunchOut_Time), "'.$this->_orgDF['sql'].' %T"))')
				 				   ))
												->where('Employee_Id = ?', $employeeId)
												->order('Concat(PunchIn_Date," ",PunchIn_Time) DESC')
												->where(new Zend_Db_Expr('Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T")').' >= ?', $currentWorkScheduleDetails['Consideration_From'])
												->where(new Zend_Db_Expr('Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T")').' <= ?', $currentWorkScheduleDetails['Consideration_To']);
				$getAttendanceDetails = $this->_db->fetchAll($getAttendanceDetails);
				return $getAttendanceDetails;
			}
			else 
			{
				return $getAttendanceDetails;
			}
		}
		else
		{
			return $getAttendanceDetails;
		}
	}

	/*based on the current time we need to check the shift/workschedule is fall in current day or previous day*/
	public function getCurrentWorkScheduleDetails($employeeId,$currentDateTime)
	{
		$emptyWorkScheduleDetails = array();
		if (!empty($employeeId))
		{
			$currentDate = date('Y-m-d',strtotime($currentDateTime));
			$yesterday   = date('Y-m-d', strtotime("-1 day",strtotime($currentDate)));
			$tomorrow   = date('Y-m-d', strtotime("+1 day",strtotime($currentDate)));
		
			$currentWorkScheduleDetails = $this->getGraceTimeDetails($employeeId,$currentDate);
			$yesterdayWorkScheduleDetails = $this->getGraceTimeDetails($employeeId,$yesterday);
			$tomorrowWorkScheduleDetails = $this->getGraceTimeDetails($employeeId,$tomorrow);
			
			if(!empty($currentWorkScheduleDetails))
			{
				if($currentDateTime >= $currentWorkScheduleDetails['Consideration_From'] && $currentDateTime < $currentWorkScheduleDetails['Consideration_To'])
				{
					return $currentWorkScheduleDetails;
				}
			}

			if(!empty($yesterdayWorkScheduleDetails))
			{
				if($currentDateTime >= $yesterdayWorkScheduleDetails['Consideration_From'] && $currentDateTime < $yesterdayWorkScheduleDetails['Consideration_To'])
				{
					return $yesterdayWorkScheduleDetails;
				}
			} 

			if(!empty($tomorrowWorkScheduleDetails))
			{
				if($currentDateTime >= $tomorrowWorkScheduleDetails['Consideration_From'] && $currentDateTime < $tomorrowWorkScheduleDetails['Consideration_To'])
				{
					return $tomorrowWorkScheduleDetails;
				}

			}

			return $emptyWorkScheduleDetails;
		}
		else 
		{
           return $emptyWorkScheduleDetails;
		}	
			
	}
	
	public function getEmployeeDetails ($employeeId)
	{
		$qryAtt = $this->_db->select()->from($this->_ehrTables->attendance,
											 array('Attendance_Id', 'Employee_Id', 'PunchIn_Date', 'PunchOut_Date'))
							->where('Employee_Id = ?', $employeeId)
							->where('PunchIn_Date = "'.Date('Y-m-d').'"')
							->order('Attendance_Id DESC')
							->limit(1);
		
		$qryEmpDet = $this->_db->select()
							->from(array('emp'=>$this->_ehrTables->empPersonal),
								   array(new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as Employee_Name")))
							
							->joinLeft(array('job'=>$this->_ehrTables->empJob),'emp.Employee_Id=job.Employee_Id',
									   array('job.Emp_Status', 'job.Employee_Id'))
							
							->joinLeft(array('des'=>$this->_ehrTables->designation),'des.Designation_Id=job.Designation_Id',
									   ('des.Designation_Name'))
							
							->joinLeft(array('dep'=>$this->_ehrTables->dept),'job.Department_Id=dep.Department_Id',
									   array('dep.Department_Name'))
							
							->joinLeft(array('att'=>$qryAtt), 'att.Employee_Id=emp.Employee_Id',
									   array('att.Attendance_Id', 'att.PunchIn_Date', 'att.PunchOut_Date'))
							
							->where('emp.Employee_Id = ?', $employeeId)
							->group('emp.Employee_Id');
		
		$empDetails = $this->_db->fetchRow($qryEmpDet);

		$employeeDetails     = $this->getEmployeeAttendanceDetails($employeeId);
		if($this->_orgDetails['Multiple_Source_Attendance_Same_Time']=='Yes' && !empty($employeeDetails) && !empty($employeeDetails['External_EmpId']))
		{
			$checkAtt = $this->checkEmployeeAttendance($employeeId);
			/*we need to return the attendanceId,PunchoutDate and Timer details as well*/
			if(!empty($checkAtt))
			{
				if($checkAtt['TimeSec'] > 0)
				{
					$empDetails['Attendance_Id'] = 1;
				}
				else
				{
					$empDetails['Attendance_Id'] = $checkAtt['Attendance_Id'];
				}
               $empDetails['PunchIn_Date']  = $checkAtt['Attendance_PunchIn_Date'];
			   $empDetails['PunchOut_Date'] = NULL;			   
			} 
		}

		return $empDetails;
	}
	
	public function checkPresentEmployees ($salaryDate,$lastSalaryDate,$attendanceBoxAccess)
	{
		$hrReports 			= new Reports_Model_DbTable_HrReports();
		$qryMonthlyEmp 		= $this->_db->select()->from(array('ET'=>$this->_ehrTables->empType), array('EP.Employee_Id','EJ.Work_Schedule as WorkSchedule_Id','ET.Work_Schedule','EJ.Location_Id','Salary_Date'=>new Zend_Db_Expr('(CASE WHEN (`EJ`.`Date_Of_Join` >= "'.$salaryDate.'" AND `EJ`.`Date_Of_Join` <= "'.$lastSalaryDate.'") THEN EJ.Date_Of_Join ELSE  "'.$salaryDate.'" END)'),'Last_Salary_Date'=>new Zend_Db_Expr('(CASE WHEN (`R`.`Resignation_Date` >= "'.$salaryDate.'" AND `R`.`Resignation_Date` <= "'.$lastSalaryDate.'") THEN R.Resignation_Date ELSE  "'.$lastSalaryDate.'" END)')))
                                                    ->joinInner(array('EJ'=>$this->_ehrTables->empJob), 'ET.EmpType_Id=EJ.EmpType_Id', array('Employee_Name'=>new Zend_Db_Expr("CONCAT(EP.Emp_First_Name,' ', EP.Emp_Last_Name)"),
													'User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN EP.Employee_Id ELSE EJ.User_Defined_EmpId END'),'Employee_Email'=>'EJ.Emp_Email'))
                                                    ->joinInner(array('EP'=>$this->_ehrTables->empPersonal), 'EP.Employee_Id = EJ.Employee_Id', array('ET.Work_Schedule','EP.Photo_Path'))
													->joinInner(array('DN'=>$this->_ehrTables->designation), 'DN.Designation_Id=EJ.Designation_Id', array('DN.Designation_Name'))
													->joinInner(array('D'=>$this->_ehrTables->dept), 'D.Department_Id=EJ.Department_Id', array('D.Department_Name'))
													->joinInner(array('L'=>$this->_ehrTables->location), 'L.Location_Id=EJ.Location_Id', array('L.Location_Name'))
                                                    ->joinLeft(array('R'=>$this->_ehrTables->resignation), 'EJ.Employee_Id=R.Employee_Id AND R.Approval_Status="Approved" AND `R`.`Resignation_Date` >= "'.$salaryDate.'"', array(''))
                                                    ->where("EJ.Emp_Status='Active' OR (EJ.Emp_Status='InActive' AND EJ.Emp_InActive_Date >= '$salaryDate')")
													->where("EJ.Emp_Email!='<EMAIL>' OR EJ.Emp_Email IS NULL")
                                                    ->where('EP.Form_Status = 1')
												    ->where('EJ.Date_Of_Join <= ?', $lastSalaryDate)
													->order("EJ.User_Defined_EmpId ASC");
		
		if(empty($attendanceBoxAccess['Admin']))
		{
			$getEmployeeId = $this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
										->where('Manager_Id = ?', $attendanceBoxAccess['LogId']);
	    	if($attendanceBoxAccess['Is_Manager'] == 1 && !empty($getEmployeeId))
        	{
        		$qryMonthlyEmp->where('EP.Employee_Id = :EmpId or EP.Employee_Id IN (?)', $getEmployeeId)
							->bind(array('EmpId'=>$attendanceBoxAccess['LogId']));
        	}
        	else 
        	{
        		$qryMonthlyEmp->where('EP.Employee_Id = ?', $attendanceBoxAccess['LogId']);
        	}
		}
		$qryMonthlyEmp 			  	 = $this->_dbCommonFun->formServiceProviderQuery($qryMonthlyEmp,'EJ.Service_Provider_Id',$attendanceBoxAccess['LogId']);
		$attendanceSummaryDetails 	 = $this->_db->fetchAll($qryMonthlyEmp);											
		$attendanceMusterEmployeeId  = array_column($attendanceSummaryDetails,'Employee_Id'); 
		$sourceType 				 = 'dashboardNoAttendance';
		$attendanceDetails           = $hrReports->getAttendanceDetails($attendanceMusterEmployeeId,$salaryDate,$lastSalaryDate,$sourceType);
        $attendanceImportDetails     = $hrReports->getAttendanceImportDetails($attendanceMusterEmployeeId,$salaryDate,$lastSalaryDate);
        $leaveDetails                = $hrReports->getLeaveDetails($attendanceMusterEmployeeId,$salaryDate,$lastSalaryDate,$sourceType);
        $compensatoryOffDetails      = $hrReports->getCompensatoryOffDetails($attendanceMusterEmployeeId,$salaryDate,$lastSalaryDate,$sourceType);
		$shortTimeOffDetails         = $hrReports->getShortTimeOffDetails($attendanceMusterEmployeeId,$salaryDate,$lastSalaryDate,$sourceType);

		$rosterManagementSettings           = $this->_dbPayslip->getRosterManagementSettings();
        $allWorkScheduleDetails             = $this->getAllWorkScheduleDetails($salaryDate,$lastSalaryDate);
        $allEmployeeShiftDetails            = $this->getAllEmployeeShiftDetails($attendanceSummaryDetails,$salaryDate,$lastSalaryDate);
        $allHolidayDetails                  = $this->_dbPayslip->getAllHolidayDates($salaryDate,$lastSalaryDate);

		$attendanceByDateAndEmployeeId      = $hrReports->organizeDataByEmployeeIdAndDate($attendanceDetails,'Employee_Id','Attendance_Date');
        $compensatoryOffByDateAndEmployeeId = $hrReports->organizeDataByEmployeeIdAndDate($compensatoryOffDetails,'Employee_Id','Compensatory_Date');
        $leaveByEmployeeId                  = $hrReports->organizeDataByEmployeeIdAndDate($leaveDetails,'Employee_Id');
		$attendanceImportByEmployeeId       = $hrReports->organizeDataByEmployeeIdAndDate($attendanceImportDetails,'Employee_Id');
		$shortTimeOffByDateAndEmployeeId    = $hrReports->organizeDataByEmployeeIdAndDate($shortTimeOffDetails,'Employee_Id','Short_Time_Off_Date');



		$presentEmployeeDetails 	= array();
		$absentEmployeeDetails 		= array();
		$leaveEmployeeDetails 		= array();
		$compoffEmployeeDetails 	= array();
		$timeOffEmployeeDetails 	= array();
		$shortTimeOffEmployeeDetails = array();
		$shiftNotScheduledDetails 	= array();

		unset($attendanceDetails);
		unset($attendanceImportDetails);
		unset($leaveDetails);
		unset($compensatoryOffDetails);
		unset($shortTimeOffDetails);
		$currentDate = date('Y-m-d');
		
		foreach($attendanceSummaryDetails as $employeeDetails)
		{
		    $step 					   		 = '+1 day'; /**incrementor in while loop**/
			$current 				   		 = strtotime($salaryDate);
        	$last 					   		 = strtotime($lastSalaryDate);
			$employeeId                		 = $employeeDetails['Employee_Id']; 
			$workScheduleId  				 = $employeeDetails['WorkSchedule_Id'];
            if($employeeDetails['Work_Schedule']=='Shift Roster')
            {
                $employeeShiftDetails = $allEmployeeShiftDetails[$employeeId]?? null;
            }
            else
            {
                $employeeWorkScheduleDetails = $allWorkScheduleDetails[$workScheduleId]?? null;
            }
			$employeeHolidayDetails 	= $allHolidayDetails[$employeeId]?? null;
			$leaveDetails           	= $hrReports->ensureArray($leaveByEmployeeId, $employeeId);
			$attendanceImportDetails 	= $hrReports->ensureArray($attendanceImportByEmployeeId, $employeeId);
	        while( $current <= $last )
            {
				$dayType 				   = '';
                $startDate 				   = date('Y-m-d',$current);
				$employeeDetails['Start_Date'] = $startDate;
				$workScheduleDetails       = array();
				$busineesWorkingDays       = 0;
				if( $employeeDetails['Salary_Date'] && (strtotime($employeeDetails['Salary_Date']) > strtotime($startDate)))
				{
					$dayType = 'Employee Yet To Join';
				}
				elseif($employeeDetails['Last_Salary_Date'] && (strtotime($employeeDetails['Last_Salary_Date']) < strtotime($startDate)))
				{
					$dayType = 'Employee Resigned';
				}
				else
				{
					if($employeeDetails['Work_Schedule']=='Shift Roster')
					{
						if($rosterManagementSettings['Overlap_Shift_Schedule']==1)
						{
							$workScheduleDetails = $this->getGraceTimeDetails($employeeId,$startDate);
							$busineesWorkingDays = $this->_dbPayslip->getBusinessWorkingDays($startDate, $startDate, $employeeId,NULL,1,'leaves');
						}
						else
						{
							$workSchedule = $this->_dbCommonFun->getWorkScheduleDetailsByDateForShiftRosterEmployees($employeeId,$startDate,$employeeShiftDetails,$allWorkScheduleDetails,$rosterManagementSettings);
							if(!empty($workSchedule))
							{
								$checkWorkingDay     = $this->_dbCommonFun->checkWorkingDay($employeeId,$startDate,$employeeHolidayDetails,$workSchedule);
								$workScheduleDetails = $checkWorkingDay['Work_Schedule_Details'];
								$busineesWorkingDays = $checkWorkingDay['Business_Working_Days'];
							}
						}
					}
					else
					{
						$workSchedule = $employeeWorkScheduleDetails[$startDate] ?? null;
						if ($workSchedule !== null)
						{
							$checkWorkingDay     = $this->_dbCommonFun->checkWorkingDay($employeeId,$startDate,$employeeHolidayDetails,$workSchedule);
							$workScheduleDetails = $checkWorkingDay['Work_Schedule_Details'];
							$busineesWorkingDays = $checkWorkingDay['Business_Working_Days'];
						}
					}

					$employeeIdAndDate      = $employeeId . '|' . $startDate;
					$attendanceDetails      = $hrReports->ensureArray($attendanceByDateAndEmployeeId, $employeeIdAndDate);
					$compensatoryOffDetails = $hrReports->ensureArray($compensatoryOffByDateAndEmployeeId, $employeeIdAndDate);
					$shortTimeOffDetails 	= $hrReports->ensureArray($shortTimeOffByDateAndEmployeeId, $employeeIdAndDate);

					if(!empty($workScheduleDetails))
					{
						if(!empty($attendanceDetails))
						{
							foreach($attendanceDetails as $attendance)
							{
								if($attendance['Attendance_Date'] == $startDate)
								{
									$dayType = 'Present';
									$employeeDetails['PunchIn_Time']  = date($this->_orgDF['php'], strtotime($attendance['PunchIn_Date'])).' '.$attendance['PunchIn_Time'];

									if(!empty($attendance['PunchOut_Date']))
									{
										$employeeDetails['PunchOut_Time'] = date($this->_orgDF['php'], strtotime($attendance['PunchOut_Date'])).' '.$attendance['PunchOut_Time'];
									}
									else
									{
										$employeeDetails['PunchOut_Time'] = NULL;
									}
									
									array_push($presentEmployeeDetails,$employeeDetails);
									break;
								}
							}
						}
					
						
						if(empty($dayType))
						{
							if(!empty($attendanceImportDetails))
							{
								foreach($attendanceImportDetails as $attendanceImport)
								{
									$attendancePunchInDate = $attendanceImport['Added_On'];
									if(empty($firstHalfDetails) && $attendancePunchInDate >= $workScheduleDetails['Consideration_From'] && $attendancePunchInDate <= $workScheduleDetails['Consideration_To'])
									{
										$dayType = 'Present';
										// we have first in and last out and every valid check in and checkout so we are not considering the status always we will consider records as PunchIn
										$employeeDetails['PunchIn_Time'] =  date($this->_orgDF['php'].' H:i:s', strtotime($attendancePunchInDate));
										array_push($presentEmployeeDetails,$employeeDetails);
										break;
									}
								}
							}
						}

						if($busineesWorkingDays > 0)
						{
							if(empty($dayType))
							{
								if(!empty($leaveDetails))
								{
									foreach($leaveDetails as $leave)
									{
										if($startDate >= $leave['Start_Date'] && $startDate <= $leave['End_Date'])
										{
											if(!empty($leave['Leave_Calculation_Days']) || ($busineesWorkingDays > 0 && empty($leave['Leave_Calculation_Days'])))
											{
												$dayType = 'OnLeave';
												array_push($timeOffEmployeeDetails,$employeeDetails);
												array_push($leaveEmployeeDetails,$employeeDetails);
												break;
											}
										}
									}
								}
							}
							
							if(empty($dayType))
							{
								if(!empty($compensatoryOffDetails))
								{
									foreach($compensatoryOffDetails as $compensatoryOff)
									{
										if($compensatoryOff['Compensatory_Date'] == $startDate)
										{
											$dayType = 'CompOff';
											array_push($timeOffEmployeeDetails,$employeeDetails);
											array_push($compoffEmployeeDetails,$employeeDetails);
											break;
										}
									}
								}
							}

							if(!empty($shortTimeOffDetails))
							{
								foreach($shortTimeOffDetails as $shortTimeOff)
								{
									if($shortTimeOff['Short_Time_Off_Date'] == $startDate)
									{
										$dayType = 'shortTimeOff';
										$employeeDetails['Start_Date_Time']  = date($this->_orgDF['php'].' H:i:s', strtotime($shortTimeOff['Start_Date_Time']));
										$employeeDetails['End_Date_Time']    = date($this->_orgDF['php'].' H:i:s', strtotime($shortTimeOff['End_Date_Time']));
										array_push($shortTimeOffEmployeeDetails,$employeeDetails);
										break;
									}
								}
							}
						}
						else
						{
							$dayType = 'Holiday';
						}
					}
					else
					{
						array_push($shiftNotScheduledDetails,$employeeDetails);
						$dayType = 'ShiftNotScheduled';
					}
				}

				if(empty($dayType))
				{
					array_push($absentEmployeeDetails,$employeeDetails);
				}

				$current = strtotime($step, $current); /**incrementor in while loop**/
			}
		}
		
		return array('Presents'=>$presentEmployeeDetails, 'Absentees'=>$absentEmployeeDetails,'TimeOff'=>$timeOffEmployeeDetails,
					'Leave'=>$leaveEmployeeDetails,'CompOff'=>$compoffEmployeeDetails,'ShiftNotScheduled'=>$shiftNotScheduledDetails,
					'ShortTimeOff'=>$shortTimeOffEmployeeDetails);
	}

	// to get the exclude break hours value form the employee type for prefilling the exclude break hours toggle button while adding attendance  
	public function getEmpBreakDetails ($employeeId, $attendanceId=null)
    {
         $breakHoursAndWorkPlace = $this->_db->fetchRow($this->_db->select()->from(array('EJ'=>$this->_ehrTables->empJob), array(''))
        ->joinInner(array('E'=>$this->_ehrTables->empType), 'E.EmpType_Id = EJ.EmpType_Id', array('Exclude_Break_Hours as breakHours','Enable_Work_Place as workPlace'))
        ->where('EJ.Employee_Id =?',$employeeId));
        
        return $breakHoursAndWorkPlace;
    }	
	public function getEmpAttendanceDetails ($employeeId, $attendanceId=null)
	{
		$qryEmpDetails = $this->_db->select()
								->from(array('emp'=>$this->_ehrTables->empPersonal),
									   array(new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as Employee_Name"),
											 'emp.Is_Manager', 'emp.Employee_Id'))
								
								->joinInner(array('job'=>$this->_ehrTables->empJob),'emp.Employee_Id=job.Employee_Id',
										   array('job.Manager_Id'))
								
								->joinLeft(array('app'=>$this->_ehrTables->empPersonal), 'app.Employee_Id=job.Manager_Id',
									   array(new Zend_Db_Expr("CONCAT(app.Emp_First_Name, ' ', app.Emp_Last_Name) as Approver_Name")))
								
								->joinLeft(array('des'=>$this->_ehrTables->designation),'des.Designation_Id=job.Designation_Id',
										   array('des.Grade_Id'));
		
		if ($attendanceId != null && $attendanceId > 0)
		{
			$qryEmpDetails->joinInner(array('att'=>$this->_ehrTables->attendance),
									  'att.Employee_Id=emp.Employee_Id AND att.PunchOut_Date=NULL',
									  array('att.PunchIn_Date', 'att.PunchIn_Time'))
						
						//->where('att.PunchIn_Date = "'.Date('Y-m-d').'"')
						->where('att.Attendance_Id = ?', $attendanceId);
		}
		
		$qryEmpDetails->where('emp.Employee_Id = ?', $employeeId);
		
		$empDet = $this->_db->fetchRow($qryEmpDetails);
		
		if ($empDet['Manager_Id'] == null && $empDet['Is_Manager'] == 1)
		{
			$empDet['Manager_Id'] = $empDet['Employee_Id'];
			$empDet['Approver_Name'] = $empDet['Employee_Name'];
		}
		
		return $empDet;
	}
	
	/**
	 *Get attendance count for approval to a logged in employee
	 */
	public function editOption($logEmpId)
	{
		$qryEmpCount = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendance, new Zend_Db_Expr('count(Attendance_Id)'))
				->where('Approver_Id = ?', $logEmpId));
		return $qryEmpCount;
	}
	
	//to list and search attendance
	public function listAttendance($page, $rows, $sortField, $sortOrder, $searchAll=null, $searchArr, $attendanceAccess, $formName)
	{
		$employeeId        = $searchArr['employeeId'];
		$totalHours        = $searchArr['totalHours'];
		$attendanceBeginDate  = $searchArr['attendanceBeginDate'];
		$attendanceEndDate    = $searchArr['attendanceEndDate'];
		$breakHours        = $searchArr['breakHours'];
		$status            = $searchArr['status'];
		$checkInWorkPlaceId  = $searchArr['checkInWorkPlaceId'];
		$checkOutWorkPlaceId = $searchArr['checkOutWorkPlaceId'];
		$checkInFormSourceId  = $searchArr['checkInFormSourceId'];
		$checkOutFormSourceId = $searchArr['checkOutFormSourceId'];
		$serviceProviderId	  = $searchArr['serviceProviderId'];
		$locationId = $searchArr['Location_Id'];
		$departmentId = $searchArr['Department_Id'];
		$managerId = $searchArr['Manager_Id'];
		$lateAttendance = $searchArr['lateAttendance'];
		$autoShortTimeOff = $searchArr['autoShortTimeOff'];

        switch($sortField)
        {
			case 1: $sortField = 'EJ.User_Defined_EmpId'; break;
			case 2: $sortField = 'emp.Emp_First_Name'; break;
			case 3: $sortField = 'att.Attendance_Date'; break;
			case 4: $sortField = 'Attendance_PunchIn_Date'; break;
			case 5: $sortField = 'Attendance_PunchOut_Date'; break;
			case 6: $sortField = 'att.Total_Hours'; break;
			case 7: $sortField = 'att.Approval_Status'; break;
		}
        
		$formId = 0;
		
		if (!empty($formName))
		{
			$formId = $this->_dbComment->getFormId($formName);
			
			$qryComment = $this->_db->select()->distinct()->from(array('Cm'=>$this->_ehrTables->comment), 'Parent_Id')
											->where('Cm.Parent_Id = att.Attendance_Id AND Cm.Form_Id='.$formId);
		}
		
		$qryAttendance = $this->_db->select()->from(array('att'=>$this->_ehrTables->attendance),
													array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS att.Attendance_Id as count'),
														  'att.Attendance_Id','att.Total_Hours','att.Employee_Id','att.Approver_Id', 'att.Late_Attendance','att.Checkin_Longitude','att.Actual_Punch_In_Time','att.Checkin_Work_Place_Id','att.Checkout_Work_Place_Id','att.Actual_PunchOut_Time','att.Actual_Total_Hours','att.Checkin_Latitude','att.Checkout_Longitude','att.Checkout_Latitude',
														  'att.Late_Attendance_Hours','att.Late_Attendance_Hours_From_Grace','att.Always_Grace','Display_Late_Attendance'=>new zend_Db_Expr('(case when att.Late_Attendance =4 then "Outside grace time(Slab)" when att.Late_Attendance =3 then "Within grace time(Slab)" when att.Late_Attendance =2 then "Outside grace time" when att.Late_Attendance =1 then "Within grace time" else "No" end)'),
														  'Attendance_PunchIn_Date'=>new Zend_Db_Expr('(Date_Format(Concat(att.PunchIn_Date," ",att.PunchIn_Time), "%Y-%m-%d %T"))'),
														  'Attendance_PunchOut_Date'=>new Zend_Db_Expr('(Date_Format(Concat(att.PunchOut_Date," ",att.PunchOut_Time), "%Y-%m-%d %T"))'),
														  new Zend_Db_Expr("DATE_FORMAT(att.PunchIn_Date,'".$this->_orgDF['sql']."') as PunchIn_Date"), 'att.PunchIn_Date as Mob_PunchIn_Date',
														  new Zend_Db_Expr("DATE_FORMAT(att.Attendance_Date,'".$this->_orgDF['sql']."') as AttendanceDate"),'att.Attendance_Date',
														  new Zend_Db_Expr("DATE_FORMAT(att.PunchOut_Date,'".$this->_orgDF['sql']."') as PunchOut_Date"), 'att.PunchOut_Date as Mob_PunchOut_Date',
														  new Zend_Db_Expr("DATE_FORMAT(att.PunchIn_Time,'%H:%i') as PunchIn_Time"),'att.PunchIn_Time as Display_PunchIn_Time','att.PunchOut_Time as Display_PunchOut_Time',
														'Total_Hours1'=>new Zend_Db_Expr("CASE WHEN ET.Display_Total_Hours_In_Minutes='1' Then ( CONCAT(FLOOR(att.Total_Hours),':', LPAD(ROUND((att.Total_Hours - FLOOR(att.Total_Hours)) * 60) % 60,2,0)) ) else att.Total_Hours END  "),
														'Actual_Total_Hours1'=>new Zend_Db_Expr("CASE WHEN ET.Display_Total_Hours_In_Minutes='1' Then ( CONCAT(FLOOR(att.Actual_Total_Hours),':', LPAD(ROUND((att.Actual_Total_Hours - FLOOR(att.Actual_Total_Hours)) * 60) % 60,2,0)) ) else att.Actual_Total_Hours END  "),
														  new Zend_Db_Expr("DATE_FORMAT(att.PunchOut_Time,'%H:%i') as PunchOut_Time"),'att.Auto_Short_Time_Off',
														  new Zend_Db_Expr("DATE_FORMAT(att.Added_Date,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
														  new Zend_Db_Expr("DATE_FORMAT(att.Modified_Date,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On"),
														  new Zend_Db_Expr("DATE_FORMAT(att.Approved_On,'".$this->_orgDF['sql']." %H:%i:%s') as Approved_On"),
														  'att.Approval_Status','att.Added_By', 'att.Checkin_Data_Source','att.Checkout_Data_Source','att.Approved_By',
														  'Admin'=>new Zend_Db_Expr("'".$attendanceAccess['Admin']."'"),
														  'Log_Id'=>new Zend_Db_Expr("'".$attendanceAccess['LogId']."'"),
														  'Update'=>new Zend_Db_Expr($attendanceAccess['Update']),
														  'Comment'=> new Zend_Db_Expr('('.$qryComment.')'),
														  'DT_RowClass' => new Zend_Db_Expr('"attendance"'), 'att.Added_By',
														  'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', att.Attendance_Id)"),
														  'BreakHrs'=>new zend_Db_Expr('case when att.Exclude_Break_Hours = 1 then "Yes" else "No" end')))
														 
							->joinLeft(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=att.Employee_Id',
									   array(new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as Employee_Name"),'emp.Is_Manager'))
							
						   ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'emp.Employee_Id=EJ.Employee_Id',
                                            array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN emp.Employee_Id ELSE EJ.User_Defined_EmpId END')))

							->joinLeft(array('ET'=>$this->_ehrTables->empType), 'EJ.EmpType_Id=ET.EmpType_Id', array('Display_Total_Hours_In_Minutes'))				
							
							->joinInner(array('emp1'=>$this->_ehrTables->empPersonal),'emp1.Employee_Id=att.Added_By',
									   array(new Zend_Db_Expr("CONCAT(emp1.Emp_First_Name, ' ', emp1.Emp_Last_Name) as Added_By_Name")))
							
							->joinLeft(array('emp2'=>$this->_ehrTables->empPersonal),'emp2.Employee_Id=att.Updated_By',
									   array(new Zend_Db_Expr("CONCAT(emp2.Emp_First_Name, ' ', emp2.Emp_Last_Name) as Updated_By_Name")))
							
							->joinLeft(array('ap'=>$this->_ehrTables->empPersonal),'ap.Employee_Id=att.Approver_Id',
									   array(new Zend_Db_Expr("CONCAT(ap.Emp_First_Name, ' ', ap.Emp_Last_Name) as Approver_Name")))
							
							->joinLeft(array('des'=>$this->_ehrTables->designation),'des.Designation_Id=EJ.Designation_Id',
									   ('des.Grade_Id'))
							
							->joinLeft(array('emp3'=>$this->_ehrTables->empPersonal),'emp3.Employee_Id=att.Approved_By',
									   array(new Zend_Db_Expr("CONCAT(emp3.Emp_First_Name, ' ', emp3.Emp_Last_Name) as Approved_By_Name")))

							->joinLeft(array('ws'=>$this->_ehrTables->workSchedule),'EJ.Work_Schedule=ws.WorkSchedule_Id',
											   array('ws.WorkSchedule_Id','ws.Grace_Time_Flag','ws.Check_Out_Time_Buffer'))
							
							->joinLeft(array('AWP1'=>$this->_ehrTables->attendanceWorkPlace), 'att.Checkin_Work_Place_Id = AWP1.Work_Place_Id', 
											   array('Work_Place as Checkin_Work_Place'))

							->joinLeft(array('AWP2'=>$this->_ehrTables->attendanceWorkPlace), 'att.Checkout_Work_Place_Id = AWP2.Work_Place_Id', 
											   array('Work_Place as Checkout_Work_Place'))

							->joinLeft(array('FS1'=>$this->_ehrTables->forms), 'att.Checkin_Form_Source = FS1.Form_Id',
												array('Form_Name as Checkin_Form_Source'))

							->joinLeft(array('FS2'=>$this->_ehrTables->forms), 'att.Checkout_Form_Source = FS2.Form_Id',
												array('Form_Name as Checkout_Form_Source'))

							->order("$sortField $sortOrder")
							->limit($rows, $page);
									
		/**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			$conditions = $this->_db->quoteInto(new Zend_Db_Expr('Concat(emp.Emp_First_Name," ",emp.Emp_Last_Name) Like ?'),"%$searchAll%");
			$conditions .= $this->_db->quoteInto('or att.PunchIn_Date Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or att.PunchIn_Time Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or att.PunchOut_Date Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or att.PunchOut_Time Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or att.Total_Hours Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or att.Approval_Status Like ?', "%$searchAll%");
		    $conditions .= $this->_db->quoteInto('or EJ.User_Defined_EmpId Like ?', "%$searchAll%");
		    $conditions .= $this->_db->quoteInto('or AWP1.Work_Place Like ?', "%$searchAll%");
		    $conditions .= $this->_db->quoteInto('or AWP2.Work_Place Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or att.Auto_Short_Time_Off Like ?', "%$searchAll%");
			
			$qryAttendance->where($conditions);
		}

		/* Filter for Employee Name */
		if ( ! empty($employeeId) /*&& preg_match('/^[a-zA-Z]+$/', $employeeId)*/ )
		{
            $qryAttendance->where($this->_db->quoteInto(new Zend_Db_Expr('Concat(emp.Emp_First_Name," ",emp.Emp_Last_Name) Like ?'),"%$employeeId%"));
		}
		
		/* Filter for attendance start Date */
		if ($attendanceBeginDate != '')
		{
		    $qryAttendance->where($this->_db->quoteInto('att.Attendance_Date >= ?', $attendanceBeginDate));
		}
		
		/* Filter for attendance end Date */
		if ($attendanceEndDate != '')
		{	
			$qryAttendance->where($this->_db->quoteInto('att.Attendance_Date <= ?', $attendanceEndDate));
		}
		
		if ($totalHours >= 0 && preg_match('/^[0-9*\.]/', $totalHours))
		{
			$qryAttendance->where('att.Total_Hours >= ?', $totalHours);
		}
		
		if (!empty($status) && preg_match('/^[a-zA-Z]/', $status))
		{
			if($status == 'AlertsApplied')
			{
				$qryAttendance->where('att.Approval_Status = ?', 'Applied')
							  ->where('att.Approver_Id = ?', $employeeId);
			}
			else
			{
				$qryAttendance->where('att.Approval_Status = ?', $status);
			}
		}
		
		if ($breakHours >=0 && preg_match('/\d/', $breakHours))
		{
			$qryAttendance->where('att.Exclude_Break_Hours = ?', $breakHours);
		}

		if ($checkInWorkPlaceId >0)
		{
			$qryAttendance->where('att.Checkin_Work_Place_Id = ?', $checkInWorkPlaceId);
		}

		if ($checkOutWorkPlaceId >0)
		{
			$qryAttendance->where('att.Checkout_Work_Place_Id = ?', $checkOutWorkPlaceId);
		}

		if ($checkInFormSourceId >0)
		{
			$qryAttendance->where('att.Checkin_Form_Source = ?', $checkInFormSourceId);
		}

		if ($checkOutFormSourceId >0)
		{
			$qryAttendance->where('att.Checkout_Form_Source = ?', $checkOutFormSourceId);
		}

		if(!empty($locationId))
		{
			$qryAttendance->where('EJ.Location_Id = ?',$locationId);
		}

		if(!empty($departmentId))
		{
			$qryAttendance->where('EJ.Department_Id = ?',$departmentId);
		}

		if (!empty($managerId))
		{
			$qryAttendance->where('att.Approver_Id = ?',$managerId);
		}

		if(!empty($serviceProviderId)&& $this->_orgDetails['Field_Force']==1)
        {
            $qryAttendance->where('EJ.Service_Provider_Id = ?',$serviceProviderId);
        }

		if (!empty($autoShortTimeOff))
		{
			$qryAttendance->where('att.Auto_Short_Time_Off = ?',$autoShortTimeOff);
		}

		if(!empty($lateAttendance))
        {
			if($lateAttendance == 'Outside grace time(Slab)'){
            	$qryAttendance->where('att.Late_Attendance = ?',4);
			}else if($lateAttendance == 'Within grace time(Slab)'){
            	$qryAttendance->where('att.Late_Attendance = ?',3);
			}else if($lateAttendance == 'Outside grace time'){
            	$qryAttendance->where('att.Late_Attendance = ?',2);
			}else if($lateAttendance == 'Within grace time'){
            	$qryAttendance->where('att.Late_Attendance = ?',1);
			}else if($lateAttendance == 'No'){
            	$qryAttendance->where('att.Late_Attendance = ?',0);
			}
        }

		if(empty($attendanceAccess['Admin']))
		{
			$getEmployeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
												  ->where('Manager_Id = ?', $attendanceAccess['LogId']));
			
			if ( $attendanceAccess['Is_Manager'] == 1 && !empty($getEmployeeId))
			{
				if($this->_orgDetails['Immediate_Reportees_View_Only']==0)
				{
					$getEmployeeId = $this->_dbCommonFun->getMultiLevelManagerIds($attendanceAccess['LogId'],1);
					array_push($getEmployeeId,$attendanceAccess['LogId']);
					$qryAttendance->where('att.Employee_Id IN (?)', $getEmployeeId);
				}
				else
				{
					$qryAttendance->where('att.Employee_Id = :EmpId or att.Approver_Id = :EmpId or att.Employee_Id IN (?)', $getEmployeeId)
							  ->bind(array('EmpId'=>$attendanceAccess['LogId']));
				}
			}
			else
			{
				$qryAttendance->where('att.Employee_Id = ?', $attendanceAccess['LogId']);
			}
		}
		
		/**
		 * SQL queries
		 * Get data to display
		*/
        $qryAttendance = $this->_dbCommonFun->getDivisionDetails($qryAttendance,'EJ.Department_Id');
		
		if(!empty($attendanceAccess['Admin']))
		{
			$qryAttendance = $this->_dbCommonFun->formServiceProviderQuery($qryAttendance,'EJ.Service_Provider_Id',$attendanceAccess['LogId']);
		}
				
		$attendance = $this->_db->fetchAll($qryAttendance);
	
		foreach ($attendance as &$row) {
			if (!empty($row['Late_Attendance_Hours_From_Grace'])) {
				$row['Late_Attendance_Time'] = "The employee checked in late by " . $row['Late_Attendance_Hours_From_Grace'] . "(HH:MM:SS) excluding the grace period of " . $row['Always_Grace'] . "(HH:MM:SS). Including the grace period, the employee was delayed by " . $row['Late_Attendance_Hours'] . "(HH:MM:SS)";
			} else {
				$row['Late_Attendance_Time'] = '';
			}
		}
		unset($row);
		//$qryFormat = $this->_db->fetchOne($qryDate);
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		/* Total data set length */
		$qryAttendanceCnt = $this->_db->select()->from($this->_ehrTables->attendance, new Zend_Db_Expr('COUNT(Attendance_Id)'));
		
		if(empty($attendanceAccess['Admin']))
		{
			$getEmployeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
												  ->where('Manager_Id = ?', $attendanceAccess['LogId']));
			
			if ( $attendanceAccess['Is_Manager'] == 1 && !empty($getEmployeeId))
			{
				if($this->_orgDetails['Immediate_Reportees_View_Only']==0)
				{
					$getEmployeeId = $this->_dbCommonFun->getMultiLevelManagerIds($attendanceAccess['LogId'], 1);
					array_push($getEmployeeId,$attendanceAccess['LogId']);
					$qryAttendanceCnt->where('Employee_Id IN (?)', $getEmployeeId);
				}
				else
				{
					$qryAttendanceCnt->where('Employee_Id = :EmpId or Approver_Id = :EmpId or Employee_Id IN (?)', $getEmployeeId)
							  ->bind(array('EmpId'=>$attendanceAccess['LogId']));
				}
			}
			else
			{
				$qryAttendanceCnt->where('Employee_Id = ?', $attendanceAccess['LogId']);
			}
		}
		
		$iTotal = $this->_db->fetchOne($qryAttendanceCnt);
		$result = array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $attendance);
		// Free up memory after the function call
		unset($qryAttendance, $attendance, $key, $row, $iTotal, $qryAttendanceCnt);
		return $result;
	}

	public function checkGraceTimeExist($employeeId,$PunchInDate='null')
	{
		//  check if shift is enabled for the employee id and get the associated shift for the punch in date 
		// else get the default workschedule from  emp_Job to check whether grace time exist
		$checkShiftEnabled=$this->checkShiftEnabled($employeeId);

		if($checkShiftEnabled== 'Shift Roster')
		{
			$orWhereQry = $this->_db->quoteInto('Shift_Start_Date <= ? AND ', $PunchInDate)
			.$this->_db->quoteInto('Shift_End_Date >= ?', $PunchInDate);

			$qryGraceTimeExist =$this->_db->select()->from(array('WS'=>$this->_ehrTables->workSchedule),array('WS.Grace_Time_Flag'))
				->joinLeft(array('EST'=>$this->_ehrTables->empShiftType),'EST.WorkSchedule_Id=WS.WorkSchedule_Id',array(''))
				->joinLeft(array('ESM'=>$this->_ehrTables->shiftEmpMapping),'ESM.Shift_Type_Id=EST.Shift_Type_Id',array(''))
				->where('ESM.Employee_Id=?',$employeeId)
				->where($orWhereQry); 
			$graceTimeExist = $this->_db->fetchOne($qryGraceTimeExist);
		}
		else
		{
		$qryGraceTimeExist =$this->_db->select()->from(array('WS'=>$this->_ehrTables->workSchedule),array('WS.Grace_Time_Flag'))
							->joinInner(array('job'=>$this->_ehrTables->empJob),'job.Work_Schedule=WS.WorkSchedule_Id',array(''))
							->where('job.Employee_Id=?',$employeeId);
						
		$graceTimeExist = $this->_db->fetchOne($qryGraceTimeExist);
		}
        return $graceTimeExist;
	}

	//Get Work schedule check in grace time and Check out time buffer details.
	public function getGraceTimeDetails($employeeId,$punchInDate)
	{
		// check if shift is enabled for the employee id and get the grace time details for the shift assocciated 
		// else get the grace time details with the default work schedule tagged 	
		$checkShiftEnabled=$this->checkShiftEnabled($employeeId);

		if($checkShiftEnabled ==='Shift Roster')
		{
			$graceTimeDetails = $this->getShiftDetails($employeeId,$punchInDate);
		}
		else
		{
			$qryGraceTimeDetails =$this->_db->select()->from(array('WS'=>$this->_ehrTables->workSchedule),
										array('WS.Check_In_Grace_Time', 'WS.Grace_Time_Flag','WS.Early_Check_In_Override','WS.Check_Out_Time_Buffer','WS.WorkSchedule_Id',
											'WS.Twodays_Flag','WS.Check_In_Consideration_Time','WS.Check_Out_Consideration_Time','WS.Regular_Work_Start_Time','WS.Regular_Work_End_Time',
											'WS.OverTime_Cooling_Period','WS.Allow_Attendance_Outside_Regular_WorkHours','WS.Title',
											'Regular_From' =>new Zend_Db_Expr('Date_Format(Concat('.'"'.$punchInDate.'"'.'," ",WS.Regular_Work_Start_Time), "%Y-%m-%d %T")'),
											'Regular_To'=>new Zend_Db_Expr('Date_Format(Concat('.'"'.$punchInDate.'"'.'," ",WS.Regular_Work_End_Time), "%Y-%m-%d %T")')))
								->joinInner(array('job'=>$this->_ehrTables->empJob),'job.Work_Schedule=WS.WorkSchedule_Id',
											array(''))
								->where('job.Employee_Id=?',$employeeId);
			
			$graceTimeDetails = $this->_db->fetchRow($qryGraceTimeDetails);					
	
		}

		if(!empty($graceTimeDetails))
		{
			if($graceTimeDetails['Twodays_Flag']==1)
			{
				$graceTimeDetails['Regular_To'] = date("Y-m-d H:i:s", strtotime('+1 days',strtotime($graceTimeDetails['Regular_To'])));
			}
			

			if($graceTimeDetails['Check_In_Consideration_Time'] > 0) 
			{
				$graceTimeDetails['Consideration_From'] = date('Y-m-d H:i:s',strtotime('-'.$graceTimeDetails["Check_In_Consideration_Time"].'minutes',strtotime($graceTimeDetails['Regular_From'])));
			}
			else 
			{
				$graceTimeDetails['Consideration_From'] = $graceTimeDetails['Regular_From'];
			}

			if($graceTimeDetails['Check_Out_Consideration_Time'] > 0) 
			{
				$graceTimeDetails['Consideration_To'] = date('Y-m-d H:i:s',strtotime('+'.$graceTimeDetails["Check_Out_Consideration_Time"].'minutes',strtotime($graceTimeDetails['Regular_To'])));
			}
			else
			{
				$graceTimeDetails['Consideration_To'] =   $graceTimeDetails['Regular_To'];
			}

			$overlapShiftSchedule = $this->_db->fetchOne($this->_db->select()->from(array('RMS'=>$this->_ehrTables->rosterManagementSettings), array('RMS.Overlap_Shift_Schedule')));
		
		
			//when the overlapping shift schedule is enabled and its a shift roster we should allow the consideration to time get overlapped.but regular timings should not get overlapped.
			if((int)$overlapShiftSchedule===1&&$checkShiftEnabled==='Shift Roster')
			{
				$nextDate = date("Y-m-d", strtotime('+1 days',strtotime($punchInDate)));
				$nextShiftGraceTimeDetails = $this->getShiftDetails($employeeId,$nextDate);
				//when the shift is not scheduled for next day we should not allow the user to add the attendance today.   
				if(!empty($nextShiftGraceTimeDetails))
				{
					if($nextShiftGraceTimeDetails['Check_In_Consideration_Time'] > 0) 
					{
						$nextShiftGraceTimeDetails['Consideration_From'] = date('Y-m-d H:i:s',strtotime('-'.$nextShiftGraceTimeDetails["Check_In_Consideration_Time"].'minutes',strtotime($nextShiftGraceTimeDetails['Regular_From'])));
					}
					else 
					{
						$nextShiftGraceTimeDetails['Consideration_From'] = $nextShiftGraceTimeDetails['Regular_From'];
					}
			
					//current date shift should end(consideration to) next shift consideration from time -1 minute.
					$nextShiftConsiderationTime= date("Y-m-d H:i:s", strtotime("-1 minutes", strtotime($nextShiftGraceTimeDetails['Consideration_From'])));

					//we introduce the overlapping shift schedule flag only to overlap overtime detail.but we should not allow the regular time getting overlapped.
					if($nextShiftConsiderationTime > $graceTimeDetails['Regular_To'])
					{
						/* Check-in minimum consideration date time */
						$checkInConsiderationDateTime = strtotime($graceTimeDetails['Consideration_From']);

						/* Check-out maximum consideration date time */
						$checkOutConsiderationDateTime = strtotime($nextShiftConsiderationTime);
						
						/* If check out consideration time is greater than check in consideration time */
						if($checkOutConsiderationDateTime > $checkInConsiderationDateTime){
							/* Find the difference between check out consideration datetime and the check-in consideration datetime in seconds */
							$differenceInDateTime = $checkOutConsiderationDateTime - $checkInConsiderationDateTime;
						
							/* One day = 86400 seconds. If the difference is less than or equal to 86400 then check-in datetime and the 
							checkout datetime falls on the same day */
							if($differenceInDateTime <= 86400){
								$graceTimeDetails['Consideration_To'] = $nextShiftConsiderationTime;
							}
						}
					}
					else 
					{
						return '';
					}
				}
				else 
				{
					return $nextShiftGraceTimeDetails;
				}
			}
			$lunchBreakDetails = $this->getLunchBreakHours($graceTimeDetails);
			$graceTimeDetails['Lunch_Break_From'] 	= $lunchBreakDetails['Lunch_Break_From'];
			$graceTimeDetails['Lunch_Break_To'] 	= $lunchBreakDetails['Lunch_Break_To'];

			return $graceTimeDetails;  
		}
		
	}

	public function getShiftDetails($employeeId,$punchInDate)
	{
		$orWhereQry = $this->_db->quoteInto('Shift_Start_Date <= ? AND ', $punchInDate)
		.$this->_db->quoteInto('Shift_End_Date >= ?', $punchInDate);
		$qryGraceTimeDetails =$this->_db->select()->from(array('WS'=>$this->_ehrTables->workSchedule),
								array('WS.Check_In_Grace_Time', 'WS.Grace_Time_Flag','WS.Early_Check_In_Override','WS.Check_Out_Time_Buffer','WS.WorkSchedule_Id',
										'WS.Twodays_Flag','WS.Check_In_Consideration_Time','WS.Check_Out_Consideration_Time','WS.Title','WS.Regular_Work_Start_Time','WS.Regular_Work_End_Time',
										'WS.OverTime_Cooling_Period','WS.Allow_Attendance_Outside_Regular_WorkHours',
										'Regular_From' =>new Zend_Db_Expr('Date_Format(Concat('.'"'.$punchInDate.'"'.'," ",WS.Regular_Work_Start_Time), "%Y-%m-%d %T")'),
										'Regular_To'=>new Zend_Db_Expr('Date_Format(Concat('.'"'.$punchInDate.'"'.'," ",WS.Regular_Work_End_Time), "%Y-%m-%d %T")')))
						->joinLeft(array('EST'=>$this->_ehrTables->empShiftType),'EST.WorkSchedule_Id=WS.WorkSchedule_Id',
									array(''))
						->joinLeft(array('ESM'=>$this->_ehrTables->shiftEmpMapping),'ESM.Shift_Type_Id=EST.Shift_Type_Id',
									array(''))
						->where('ESM.Employee_Id=?',$employeeId)
						->where($orWhereQry);
						
		$graceTimeDetails = $this->_db->fetchRow($qryGraceTimeDetails);

		return $graceTimeDetails;
	}

	public function getLunchBreakHours($graceTimeDetails=null,$fromDateTime=null,$toDateTime=null)
	{
		if(!is_null($graceTimeDetails)){
			$actualRegularFrom = $graceTimeDetails['Regular_From'];
			$actualRegularTo = $graceTimeDetails['Regular_To'];
		}else{
			$actualRegularFrom = $fromDateTime;
			$actualRegularTo = $toDateTime;
		}

		$regularTo 	= new DateTime($actualRegularTo);
		$regularFrom= new DateTime($actualRegularFrom);

		$interval 	= $regularTo->diff($regularFrom);
		$hours 	  	= $interval->format('%H');
		$minutes  = $interval->format('%i');

		if($hours>0)
		{
			$hoursToMinutes = $hours*60;
		}
		else
		{
			$hoursToMinutes = 0;
		}
		$midTime = ceil(($hoursToMinutes + $minutes) / 2);
		$lunchBreakFrom = date('Y-m-d H:i:s',strtotime('+'.$midTime.'minutes',strtotime($actualRegularFrom)));

		$lunchBreakEndMinutes = 1;
		$lunchBreakTo = date('Y-m-d H:i:s',strtotime('+'.$lunchBreakEndMinutes.'minutes',strtotime($lunchBreakFrom)));

		$lunchBreakDetails = array('Lunch_Break_From'=>$lunchBreakFrom,'Lunch_Break_To'=>$lunchBreakTo);

		return $lunchBreakDetails;
	}

	public function calculateLateAttendanceHours($attendancePunchInDateTime,$workScheduleStartTime)
	{
		$workScheduleStartTime 		= new DateTime($workScheduleStartTime); // First date and time
		$attendancePunchInDateTime	= new DateTime($attendancePunchInDateTime); // Second date and time
		$interval  					= $workScheduleStartTime->diff($attendancePunchInDateTime); // Get the difference between the two dates
		$time_diff 					= $interval->format('%H:%I:%S'); // Get the time difference in hours, minutes, and seconds format
		return $time_diff;
	}
	
	public function getTotalHours($attendanceArray)
	{ 
		if (!empty($attendanceArray['PunchOut_Time']) && $attendanceArray['PunchOut_Time']!= null)
 		{
			if ($attendanceArray['PunchOut_Date'] > $attendanceArray['PunchIn_Date'])
			{
				$diff = (strtotime($attendanceArray['PunchOut_Date']) + strtotime($attendanceArray['PunchOut_Time'])) -
						(strtotime($attendanceArray['PunchIn_Date']) + strtotime($attendanceArray['PunchIn_Time']));
			}
			else if (strtotime($attendanceArray['PunchOut_Date']) == strtotime($attendanceArray['PunchIn_Date']))
			{
				$diff = strtotime($attendanceArray['PunchOut_Time']) - strtotime($attendanceArray['PunchIn_Time']);
			}
			
			$totalHours = $diff/60/60;
		}
		else
		{
			$totalHours = 0;
		}

		return $totalHours;

	}

	public function checkGeoEnforce($logEmpId)
	{
		$qry =$this->_db->select()->from(array('EJ'=>$this->_ehrTables->empJob),
									array('EJ.Designation_Id'))
							->joinInner(array('des'=>$this->_ehrTables->designation),'des.Designation_Id = EJ.Designation_Id',
										array('des.Attendance_Enforced_GeoLocation'))
							->where('EJ.Employee_Id=?',$logEmpId);
		$details = $this->_db->fetchRow($qry);

		if($details['Attendance_Enforced_GeoLocation']){
			return array('isEnable' => true, 'msg'=>'User has enforced geo location', 'type'=>'info');
		} else {
			return array('isEnable' => false, 'msg'=>'User has not enforced geo location', 'type'=>'info');
		}
	}

	public function listWorkPlace()
	{
		$qryWorkPlace = $this->_db->select()->from($this->_ehrTables->attendanceWorkPlace,array('Work_Place_Id','Work_Place','Image_Url'))
									->where('Work_Place_Status=?','Active')
									->order('Work_Place ASC');

		return $this->_db->fetchAll($qryWorkPlace);
	}

	// 29 - form id for Attendance, 154 - Attendance Box
	// 174 - Dashboard Attendance, 190 - Attendance Finalization
	// 203 - Attendance Regularization
	public function listAttendanceFormSource()
	{
		return $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->forms, array('Form_Id', 'Form_Name'))
									->where('Form_Id IN (?)', array(29,154,174,203))
									->order('Form_Name ASC')
						);
	}

	public function isEnableWorkPlace($employeeId=null)
	{
		$checkWorkPlaceEnabled = $this->_db->fetchOne($this->_db->select()
									->from(array('ET'=>$this->_ehrTables->empType), array('ET.Enable_Work_Place'))
									
									->joinLeft(array('J'=>$this->_ehrTables->empJob), 'ET.EmpType_Id=J.EmpType_Id', array())
								
									  ->where('J.Employee_Id = ?', $employeeId));
		
		return $checkWorkPlaceEnabled;
	}
	public function getOfficeWorkPlaceId()
	{
		$officeWorkPlaceId = $this->_db->fetchOne($this->_db->select()
									->from(array('WP'=>$this->_ehrTables->attendanceWorkPlace), array('WP.Work_Place_Id'))
									
									->where('WP.Work_Place = ?', 'Office'));

		return $officeWorkPlaceId;
	}

	/*This function will return the attendance status whether its should be applied or approved*/
	public function getDashboardAttendanceApprovalStatus($employeeId,$checkInworkPlaceId,$checkOutWorkPlaceId)
	{
		$dashboardAttendanceApprovalDetails = $this->_db->fetchRow($this->_db->select()->from(array('ET'=>$this->_ehrTables->empType), array('ET.Approve_Dashboard_Attendance',new Zend_Db_Expr('Group_CONCAT(AAWS.Work_Place_Id ORDER BY AAWS.Work_Place_Id ASC) as Work_Place_Id')))
									->joinLeft(array('J'=>$this->_ehrTables->empJob), 'ET.EmpType_Id=J.EmpType_Id', array())
									->joinLeft(array('AAWS'=>$this->_ehrTables->autoApprovalWorkPlace), 'ET.EmpType_Id=AAWS.Employee_Type_Id', array())
									->where('J.Employee_Id = ?', $employeeId));
		
		if($dashboardAttendanceApprovalDetails['Approve_Dashboard_Attendance']=='Automatically For Selected Work Place')
		{
			$workPlaceIds = explode(',', $dashboardAttendanceApprovalDetails['Work_Place_Id']);
			if (in_array($checkInworkPlaceId, $workPlaceIds) || in_array($checkOutWorkPlaceId, $workPlaceIds))
			{
				return 'Approved';
			}
			else
			{
				return 'Applied';
			}
		}
		else if($dashboardAttendanceApprovalDetails['Approve_Dashboard_Attendance']=='Automatically')
		{
            return 'Approved';
		}
		else
		{
			return 'Applied';
		}
	}
	public function updateAttendance($attendanceArray, $comment, $sessionId, $formName, $customFormName,$onDutyAttendanceDetails,$attendanceDetails,$isTheme)
	{
		$hrReports = new Reports_Model_DbTable_HrReports();
		/*check whether full day compensatory off exist and full day leave exist for that employee.we should not consider the late attendance leave*/
		$attendancePunchInDate = date('Y-m-d H:i:s',strtotime($attendanceArray['PunchIn_Date'] .' '. $attendanceArray['PunchIn_Time']));
		$graceTimeDetails	   = $this->getCurrentWorkScheduleDetails($attendanceArray['Employee_Id'],$attendancePunchInDate);
		$workScheduleDetails   = $graceTimeDetails;
		$attendanceDayIsWorkingDay = 0;

		if(!empty($onDutyAttendanceDetails))
		{
			if(!empty($onDutyAttendanceDetails['Attendance_Duration']))
			{
				$attendanceDetails = $this->getOnDutyAttendanceDetails($onDutyAttendanceDetails['Attendance_Duration'],$onDutyAttendanceDetails['Attendance_Period'],$workScheduleDetails,$attendanceArray['Checkin_Work_Place_Id']);
				if(!empty($attendanceDetails))
				{
					$attendanceArray['PunchIn_Date']  			= date('Y-m-d',strtotime($attendanceDetails['attendancePunchInDateTime']));
					$attendanceArray['PunchIn_Time']  			= date('H:i:s',strtotime($attendanceDetails['attendancePunchInDateTime']));
					$attendanceArray['PunchOut_Date'] 			= date('Y-m-d',strtotime($attendanceDetails['attendancePunchOutDateTime']));
					$attendanceArray['PunchOut_Time'] 			= date('H:i:s',strtotime($attendanceDetails['attendancePunchOutDateTime']));
					$attendanceArray['Approval_Status']        	= 'Applied';
					$attendanceArray['Checkout_Work_Place_Id'] 	= $attendanceArray['Checkin_Work_Place_Id'];
					$attendanceArray['Checkout_Form_Source'] 	= $attendanceArray['Checkin_Form_Source'];
					$attendanceArray['Checkout_Latitude']    	= $attendanceArray['Checkin_Latitude'];
					$attendanceArray['Checkout_Longitude']   	= $attendanceArray['Checkin_Longitude'];
					$attendanceArray['Checkout_Address']     	= $attendanceArray['Checkin_Address'];
					$attendanceArray['Checkout_Data_Source'] 	= $attendanceArray['Checkin_Data_Source'];
				}
			}
		}
		
		// check if shift is enabled and shift exist for the punch in date or if shift is disabled for the employee id.
		$checkShiftEnabled=$this->checkShiftEnabled($attendanceArray['Employee_Id']);
		$ShiftExist=$this->CheckEmpShiftExist($attendanceArray['Employee_Id'],$attendanceArray['PunchIn_Date']); 
		if($checkShiftEnabled=='Shift Roster' && $ShiftExist!=null || $checkShiftEnabled=='Employee Level')
		{
		$attendanceArray['Total_Hours'] = $this->getTotalHours($attendanceArray);
			
		if ($attendanceArray['Approval_Status'] == 'Approved')
		{
			$attendanceArray['PunchOut_Time'] = date('H:i:s',strtotime($attendanceArray['PunchOut_Time']));
		}
		else
		{
			if (empty($attendanceArray['PunchOut_Time']))
			{
				$attendanceArray['PunchOut_Time'] = "";
				$attendanceArray['Actual_PunchOut_Time'] = "";
				$attendanceArray['Approval_Status'] = "Draft";
			}
			else if (!empty($attendanceArray['PunchOut_Time']))
			{
				//change punch out time based on work schedule check out time buffer
		       if($workScheduleDetails['Check_Out_Time_Buffer'] > 0)
				{
					$getActualPunchOut=$this->_dbWorkSchedule->getActualPunchOutDetails($attendanceArray,$workScheduleDetails);
				
					$attendanceArray['Actual_PunchOut_Time'] = $getActualPunchOut['Actual_PunchOut_Time'];
					$attendanceArray['PunchOut_Time'] = $getActualPunchOut['PunchOut_Time'];
				}
				else
				{
					$attendanceArray['Actual_PunchOut_Time'] = date('H:i:s',strtotime($attendanceArray['PunchOut_Time']));
					$attendanceArray['PunchOut_Time'] = date('H:i:s',strtotime($attendanceArray['PunchOut_Time']));
				}
			
				$attendanceArray['Approval_Status'] = "Applied";
			}
		}
		
		$graceTimeExist=$this->checkGraceTimeExist($attendanceArray['Employee_Id'],$attendanceArray['PunchIn_Date']);

		if($graceTimeExist==1)
		{
			if(!empty($attendanceArray['Attendance_Id']))
			{
				$attCheckInData= $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->attendance,array('PunchIn_Time','PunchOut_Time','Actual_Punch_In_Time','Approval_Status','Actual_Total_Hours'))
																				->where('Attendance_Id = ?', $attendanceArray['Attendance_Id']));

				
				
				$currentPunchInTime=date('H:i:s',strtotime($attendanceArray['PunchIn_Time']));
				$currentPunchOutTime=date('H:i:s',strtotime($attendanceArray['PunchOut_Time'])); 
		
				if($attCheckInData['Approval_Status']=='Draft')
				{
					if($attCheckInData['PunchIn_Time'] == $currentPunchInTime)
					{
							$attendanceArray['PunchIn_Time']         = $attendanceArray['PunchIn_Time'];
							$attendanceArray['Actual_Punch_In_Time'] = $attCheckInData['Actual_Punch_In_Time'];
							$attendanceArray['Actual_Total_Hours']   = $this->_dbWorkSchedule->getActualTotalHours($attendanceArray);
					}
					else
					{
						$getActualPunchInDetails=$this->_dbWorkSchedule->getActualPunchInDetails($attendanceArray,$workScheduleDetails);
						$attendanceArray['Actual_Punch_In_Time']=$getActualPunchInDetails['Actual_Punch_In_Time'];
						$attendanceArray['PunchIn_Time']=$getActualPunchInDetails['PunchIn_Time'];
						$attendanceArray['Actual_Total_Hours']=$getActualPunchInDetails['Actual_Total_Hours'];
					}

				}
				elseif($attCheckInData['Approval_Status']=='Applied'||$attCheckInData['Approval_Status']=='Returned')
				{
					if($attCheckInData['PunchIn_Time'] == $currentPunchInTime && $attCheckInData['PunchOut_Time'] == $currentPunchOutTime)
					{
							$attendanceArray['PunchIn_Time']         = $attendanceArray['PunchIn_Time'];
							$attendanceArray['Actual_Punch_In_Time'] = $attCheckInData['Actual_Punch_In_Time'];
							$attendanceArray['Actual_Total_Hours']   = $attCheckInData['Actual_Total_Hours'];
					}
					else
					{
						$getActualPunchInDetails=$this->_dbWorkSchedule->getActualPunchInDetails($attendanceArray,$workScheduleDetails);
						$attendanceArray['Actual_Punch_In_Time']=$getActualPunchInDetails['Actual_Punch_In_Time'];
						$attendanceArray['PunchIn_Time']=$getActualPunchInDetails['PunchIn_Time'];
						$attendanceArray['Actual_Total_Hours']=$getActualPunchInDetails['Actual_Total_Hours'];
					}
            	}
			}
			else
			{
				$getActualPunchInDetails=$this->_dbWorkSchedule->getActualPunchInDetails($attendanceArray,$workScheduleDetails);
				$attendanceArray['Actual_Punch_In_Time']=$getActualPunchInDetails['Actual_Punch_In_Time'];
				$attendanceArray['PunchIn_Time']=$getActualPunchInDetails['PunchIn_Time'];
				$attendanceArray['Actual_Total_Hours']=$getActualPunchInDetails['Actual_Total_Hours'];
			}
		}
		else
		{
			$attendanceArray['Actual_Punch_In_Time']=$attendanceArray['PunchIn_Time'];
			$attendanceArray['PunchIn_Time']=$attendanceArray['PunchIn_Time'];
			$attendanceArray['Actual_Total_Hours']= $this->_dbWorkSchedule->getActualTotalHours($attendanceArray);
			
		}
		$attendanceArray['Total_Hours'] = $this->getTotalHours($attendanceArray);

		$isExists = $this->getAttendanceExist($attendanceArray);
		
		if( $isExists == 0)
		{
			if(!empty($graceTimeDetails))
			{
				$attendanceActualDate 	= date('Y-m-d',strtotime($graceTimeDetails['Regular_From']));
				$fullDayCompOffExist 	= $hrReports->getCompensatoryOff($attendanceArray['Employee_Id'],$attendanceActualDate,1,'','dashboardNoAttendance');
				$fullDayLeaveExist   	= $hrReports->getLeaveDuration($attendanceArray['Employee_Id'],$attendanceActualDate,1,'','dashboardNoAttendance',1);
				$attendanceGeneralConfig = $this->_dbCommonFun->getAttendanceGeneralConfiguration();

				$attendanceArray['Attendance_Date'] = $attendanceActualDate;
				//If the check-in or check-out form source is attendance regularization
				if($attendanceArray['Checkin_Form_Source'] == 203 || $attendanceArray['Checkout_Form_Source'] == 203){
					$validateAttendanceConfigResult = $this->validateAttendanceGeneralConfig($attendanceArray['Employee_Id'],$attendanceActualDate,$attendanceGeneralConfig);
					if(!empty($validateAttendanceConfigResult)){
						if($validateAttendanceConfigResult['employeeRegularizeAttendance'] == 0){
							$attendanceConfigErrorMessage = 'Attendance cannot be regularized for this date as the current date exceeds regularization cutoff date, '.$validateAttendanceConfigResult['regularizationEmployeeCutOffNextDate'];
						}else if($validateAttendanceConfigResult['regularizationRequestLimitExceed'] == 1){
							$attendanceConfigErrorMessage = 'Attendance cannot be regularized as you have exceeded the regularization request limit of '.$validateAttendanceConfigResult['employeeRegularizationRequestLimit'].' days per month';
						}else{
							$attendanceConfigErrorMessage = '';
						}
					}else{
						$attendanceConfigErrorMessage = 'There seem to be some technical difficulties in validating the pre-approval request. Please contact system admin.';
					}
				}else{
					$attendanceConfigErrorMessage = '';
				}
				if(empty($attendanceConfigErrorMessage)){
					$wfhPreApprovalErrorMessage = '';
					
					if($attendanceArray['PunchIn_Date']){
						$validationPunchInDateTime = date('Y-m-d H:i:s',strtotime((($attendanceArray['PunchIn_Date'].' '.$attendanceArray['PunchIn_Time']))));
					}else{
						$validationPunchInDateTime = '';
					}

					if($attendanceArray['PunchOut_Date']){
						$validationPunchOutDateTime = date('Y-m-d H:i:s',strtotime((($attendanceArray['PunchOut_Date'].' '.$attendanceArray['PunchOut_Time']))));
					}else{
						$validationPunchOutDateTime = '';
					}

					$wfhPreApprovalResult = $this->validateEmpPreApprovalRequest($attendanceArray['Employee_Id'],$validationPunchInDateTime,$validationPunchOutDateTime,$attendanceArray['Checkin_Work_Place_Id'],$attendanceArray['Checkout_Work_Place_Id']);
					if($wfhPreApprovalResult){
						if($wfhPreApprovalResult['success'] == false){
							$wfhPreApprovalErrorMessage = $wfhPreApprovalResult['message'];
						}
					}else{
						$wfhPreApprovalErrorMessage = 'There seem to be some technical difficulties in validating the pre-approval request. Please contact system admin.';
					}
					
					if(empty($wfhPreApprovalErrorMessage))
					{
						if(empty($fullDayCompOffExist))
						{
							if($checkShiftEnabled=='Shift Roster')
							{
								$businessWorkingDays = $this->_dbPayslip->getBusinessWorkingDays($attendanceActualDate, $attendanceActualDate, $attendanceArray['Employee_Id'],NULL,1,'leaves');
							}
							else 
							{
								$busineesWorkingDays = $this->_dbPayslip->getBusinessWorkingDays($attendanceActualDate, $attendanceActualDate, $attendanceArray['Employee_Id'],'',1);
							}

							if(empty($fullDayLeaveExist) ||  (!empty($fullDayLeaveExist) && $fullDayLeaveExist['Leave_Calculation_Days']==0 && $busineesWorkingDays==0))
							{
								$payslipExist =	$this->payslipExistForAttendanceDate($attendanceArray['Employee_Id'],$attendancePunchInDate);
								if(!empty($payslipExist))
								{
									return array('success' => false, 'msg'=>'Payslip exist for this attendance duration', 'type'=>'info');	
								}
								else 
								{
									$employeeDetails     = $this->getEmployeeAttendanceDetails($attendanceArray['Employee_Id']);
									//"When the 'Multiple_Source_Attendance_Same_Time' configuration is configured as 'Yes' and a biometric integration ID is present, attendance records will be forwarded to the import. In cases where the customer wishes to capture latitude and longitude, it is essential for the biometric integration ID to remain unoccupied."
									if($this->_orgDetails['Multiple_Source_Attendance_Same_Time']=='Yes' && !empty($employeeDetails) && !empty($employeeDetails['External_EmpId']))
									{
										$attendanceImportDetails = $this->insertAttendanceImportRecord($attendanceArray,$employeeDetails,$customFormName,$sessionId,$attendanceDetails,$isTheme);
										return $attendanceImportDetails;
									}
									else
									{
										if($attendanceArray['Attendance_Id'] == null || $attendanceArray['Attendance_Id'] == 0)
										{
											if(!empty($attendanceArray['Employee_Id']))
											{
												$shortTimeOffConfiguration 	             = $this->listShortLeaveRequestSettings(array($attendanceArray['Employee_Id']),$attendanceArray['Attendance_Date'],$attendanceArray['Attendance_Date']);
												$shortTimeOffConfiguration['Admin_Id']   = $attendanceArray['Approver_Id'];
												$shortTimeOffConfiguration['Session_Id'] = $sessionId;
											}
											else
											{
												$shortTimeOffConfiguration = array();
											}

											$lateAttendanceDetails = $this->checkLateAttendance($attendanceArray['Employee_Id'], $attendanceArray['PunchIn_Date'],
																										$attendanceArray['PunchIn_Time'],$shortTimeOffConfiguration);

											$attendanceArray['Late_Attendance'] 	  = $lateAttendanceDetails['Late_Attendance'];
											$attendanceArray['Late_Attendance_Hours'] = $lateAttendanceDetails['Late_Attendance_Hours'];
											$attendanceArray['Late_Attendance_Hours_From_Grace'] 	= $lateAttendanceDetails['Late_Attendance_Hours_From_Grace'];
											$attendanceArray['Always_Grace'] 						= $lateAttendanceDetails['Always_Grace'];
											$attendanceArray['Auto_Short_Time_Off'] 				= $lateAttendanceDetails['Auto_Short_Time_Off'];

											$attendanceDayIsWorkingDay = $lateAttendanceDetails['Business_Working_Day'];
										}
										
										
										$punchInTime = date('H:i:s',strtotime($attendanceArray['PunchIn_Time']));
										
										$updated = 0;

										if (!empty($attendanceArray['Attendance_Id']))
										{
											$action = 'Edit';
											
											$attendanceId = $attendanceArray['Attendance_Id'];

											$attendanceArray['Modified_Date'] = date('Y-m-d H:i:s');
											$attendanceArray['Updated_By'] = $sessionId;
											if($attendanceArray['Total_Hours'] >= 0)
											{
												$updated = $this->_db->update($this->_ehrTables->attendance, $attendanceArray, array('Attendance_Id = '. $attendanceArray['Attendance_Id']));	
											}
											else{
												return array('success' => false, 'msg'=>'Check in date & time should be lesser than check out date & time, please check the grace time configuration in work schedule or contact system admin', 'type'=>'info');
											}
										}
										else
										{
											$action = 'Add';
											
											$attendanceArray['Added_Date'] = date('Y-m-d H:i:s');
											$attendanceArray['Added_By'] = $sessionId;
											if($attendanceArray['Total_Hours'] >= 0)
											{
												$updated = $this->_db->insert($this->_ehrTables->attendance, $attendanceArray);
											}
											else{
												return array('success' => false, 'msg'=>'Check in date & time should be lesser than check out date & time, please check the grace time configuration in work schedule or contact system admin', 'type'=>'info');
											}
											if($updated)
											{
												$attendanceId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendance, array(new Zend_Db_Expr('Max(Attendance_Id)'))));	
											}
										}
										
										if($updated)
										{
											if (!empty($comment))
											{
												$addComment = array('Form_Id'         => $this->_dbComment->getFormId($formName),
																	'Emp_Comment'     => htmlentities(strip_tags(trim($comment))),
																	'Parent_Id'       => $attendanceId,
																	'Approval_Status' => $attendanceArray['Approval_Status'],
																	'Employee_Id'     => $sessionId,
																	'Added_On'        => date('Y-m-d H:i:s'));
												
												$this->_db->insert($this->_ehrTables->comment,$addComment);
											}
										}
										
										/**
										*	this function will handle
										*		update system log function
										*		clear submit lock fucntion
										*		return success/failure array
										*/
										$updatedResult = $this->_dbCommonFun->updateResult (array('updated'        => $updated,
																								'action'         => $action,
																								'trackingColumn' => $attendanceId,
																								'formName'       => $customFormName,
																								'sessionId'      => $sessionId,
																								'tableName'      => $this->_ehrTables->attendance));
										
										if($updatedResult['success'])
										{
											$updatedResult['status'] = $attendanceArray['Approval_Status'];
										
											$qryEmpAttendance = $this->_db->select()->from($this->_ehrTables->attendance, array('Employee_Id'))
																					->where('PunchIn_Date = ?', date('Y-m-d'))
																					->group('Employee_Id');
											
											$qryImportEmp = $this->_db->select()
																->from($this->_ehrTables->attendanceImport, array('Employee_Id'))
																
																->where('Added_On >= ?', date('Y-m-d 00:00:00'))
																->where('Added_On <= ?', date('Y-m-d 23:59:59'))
																->group('Employee_Id');
											
											$dbAlerts = new Default_Model_DbTable_Alerts();
											$qryPresence = $dbAlerts->getEmployeeQuery ();
											
											$qryPresence
												->where('job.Emp_Status=\'Active\'')
												->where('emp.Employee_Id IN ?', $qryEmpAttendance)
												->orwhere('job.External_EmpId IN ?', $qryImportEmp);
												
											$updatedResult['totalPresence'] = $this->_db->fetchOne($qryPresence);
											
											if($attendanceArray['Late_Attendance'] == 2 && ($attendanceArray['Attendance_Id'] == null || $attendanceArray['Attendance_Id'] == 0))
											{
												$this->applyLateAttendanceLeave($attendanceArray, $sessionId, $attendanceActualDate, $attendanceDayIsWorkingDay);
											}
											$compOffBalanceArray = array( 'Approval_Status' => $attendanceArray['Approval_Status'],
																		'Parent_Id'       => $attendanceId);
											$this->updateCompOffBalance($compOffBalanceArray);
										}
										
										if(($attendanceArray['Late_Attendance'] == 2 || $attendanceArray['Late_Attendance'] == 4) && ($attendanceArray['Attendance_Id'] == null || $attendanceArray['Attendance_Id'] == 0))
										{
											$this->applyLateAttendanceLeave($attendanceArray, $sessionId, $attendanceActualDate, $attendanceDayIsWorkingDay);
										}

										$lateAttendanceEmailNotificationValues = array(1,2,3,4);
										if(!empty($attendanceGeneralConfig) && count($attendanceGeneralConfig) > 0){
											//Send an email when the attendance is added for current date and if employee arrive late or late attendance leave applied because of late arrival
											if(($attendanceGeneralConfig['Late_Attendance_Email_Notification']=='Yes') && (strtotime(date('Y-m-d')) == strtotime($attendanceArray['Attendance_Date'])) && (in_array($attendanceArray['Late_Attendance'],$lateAttendanceEmailNotificationValues)) 
											&& ($attendanceArray['Attendance_Id'] == null || $attendanceArray['Attendance_Id'] == 0)){
												$dbPersonal    = new Employees_Model_DbTable_Personal();
												$employeeEmailAddress = $dbPersonal->getEmpmail($attendanceArray['Employee_Id']);
												if(!empty($employeeEmailAddress)){
													$lateAttendanceGracePeriodValues = array(1,3);
													$basePath = new Zend_View_Helper_BaseUrl();

													if(in_array($attendanceArray['Late_Attendance'],$lateAttendanceGracePeriodValues)){
														$emailSubject = 'Late Arrival Today - Grace Period Applied';
														$emailContent = 'This is to bring to your attention that you arrived late today, but your arrival falls within the grace period.';
														$urlString = $_SERVER['HTTP_HOST'].$basePath->baseUrl('/v3/employee-self-service/attendance');
													}else{
														$emailSubject = 'Late Attendance Leave Applied Due to Your Delayed Arrival Today';
														$emailContent = 'This is to inform you that your late arrival today has resulted in the application of late attendance leave.';
														$urlString = $_SERVER['HTTP_HOST'].$basePath->baseUrl('/employees/leaves');
													}

													$allEmailInputs = array(
														'emailSubject'=> $emailSubject,
														'ToAddresses'=>$employeeEmailAddress,
														'CcAddresses'=>null,
														'supportEmail'=> $this->_orgDetails['HR_Admin_Email_Address'],
														'buttonText'=> 'Click here to check it out',
														'subtitle'=>$emailContent,
														'redirectionUrl'=>$urlString
													);
				
													$emailResponseMesssage = $this->_dbCommonFun->sendBulkTemplateEmail($allEmailInputs);
												}
											}
										}

										$compOffBalanceArray = array( 'Approval_Status' => $attendanceArray['Approval_Status'],
																	'Parent_Id'       => $attendanceId);
										$this->updateCompOffBalance($compOffBalanceArray);
										$attendanceSummaryDetails = [];
										$attendanceSummaryDetails[] = $attendanceArray;
										$this->_dbCommonFun->triggerAttendanceSummaryStepFunction($attendanceSummaryDetails,'attendance');
									}
								}
								
								return $updatedResult;
							}else{
								return array('success' => false, 'msg'=>'You have applied leave on this date', 'type'=>'info');	
							}
						}
						else
						{
							return array('success' => false, 'msg'=>'You have applied compensatory off on this date', 'type'=>'info');
						}
						
					}else{
						return array('success' => false, 'msg'=> $wfhPreApprovalErrorMessage, 'type'=>'info');
					}
				}else{
					return array('success' => false, 'msg'=> $attendanceConfigErrorMessage, 'type'=>'info');
				}
			}
			else
			{
				return array('success' => false, 'msg'=>'Shift has not been allocated for the punchin date', 'type'=>'info');
			}
		}
		else
		{
			$dbHrReports    = new Reports_Model_DbTable_HrReports();
			$attendanceActualDate 	= date('Y-m-d',strtotime($graceTimeDetails['Regular_From']));
			$attendanceDetails = $dbHrReports->getAttendanceDetails($attendanceArray['Employee_Id'],$attendanceActualDate,$attendanceActualDate,'attendanceMusterInfo');
			if(!empty($attendanceDetails))
			{
				$attendanceData = '';
				foreach($attendanceDetails as $attendance)
				{
					if(empty($attendance['PunchOut_Date']))
					{
						$attendance['PunchOut_Date'] = '-';
					}
					else
					{
						$attendance['PunchOut_Date'] = date($this->_orgDF['php'], strtotime($attendance['PunchOut_Date'])).' '.$attendance['PunchOut_Time'];
					}

					$attendanceData .= 'Check In :'.date($this->_orgDF['php'], strtotime($attendance['PunchIn_Date'])).' '.$attendance['PunchIn_Time'].' '.'Check Out :'.$attendance['PunchOut_Date'];
				
				}
			}
			return array('success' => false, 'msg'=>'You have already applied attendance for following duration '.$attendanceData, 'type'=>'info');
		}
	  }
		else
		{
			return array('success' => false, 'msg'=>'Shift Has not been allocated for the PunchIn date', 'type'=>'info');
		}
	}
	
		
	public function checkShiftEnabled($employeeId=null)
	{ 
	$checkShiftEnabled = $this->_db->fetchOne($this->_db->select()
												->from(array('SEM'=>$this->_ehrTables->empType), array('SEM.Work_Schedule'))
												
												->joinLeft(array('J'=>$this->_ehrTables->empJob), 'SEM.EmpType_Id=J.EmpType_Id', array())
											
												  ->where('J.Employee_Id = ?', $employeeId));
											
		return $checkShiftEnabled;
	}


	public function CheckEmpShiftExist($empId, $PunchInDate) 
    {
		$orWhereQry = $this->_db->quoteInto('Shift_Start_Date <= ? AND ', $PunchInDate)
						.$this->_db->quoteInto('Shift_End_Date >= ?', $PunchInDate);
    $qryEmpShiftExist = $this->_db->select() 
                           ->from(array('sem'=>$this->_ehrTables->shiftEmpMapping), array('')) 
                           ->joinInner(array('ST'=>$this->_ehrTables->empShiftType), 'sem.Shift_Type_Id=ST.Shift_Type_Id',
				array('WorkSchedule_Id'))
                           ->where('Employee_Id = ?', $empId) 
                           ->where($orWhereQry); 
						   $empShiftExist = $this->_db->fetchOne($qryEmpShiftExist); 
            return  $empShiftExist;  
    } 

	
	//To get CheckIn checkOut data source

	public function getDataSource($attendanceId,$checkIn,$checkOut,$punchInDate,$punchInTime,$punchOutDate,$punchOutTime,$isTheme)
	{
		$checkDataSourceArr=array();
		if ($isTheme == 'Bootstrap' || $isTheme == 'DashBoard' || $isTheme =='dashboardMissedCheckout')
		{
			if($attendanceId>0)
			{
					$checkInQry=$this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->attendance, array('PunchIn_Date','PunchIn_Time','Checkin_Data_Source'))
					->where('Attendance_Id = ?',$attendanceId));
					
						$checkDataSourceArr['Checkin_Data_Source']=$checkInQry['Checkin_Data_Source'];
						$checkDataSourceArr['Checkout_Data_Source']=$checkOut;
			}
			else
			{
						$checkDataSourceArr['Checkin_Data_Source']=$checkIn;
						$checkDataSourceArr['Checkout_Data_Source']='';
			}
		}
		else
		{
			if($attendanceId > 0)
			{
				if(!empty($punchInDate))
				{
					//check whether punchin date and time is same exisiting punchin date and time in that record.if yes we need to maintatin the CheckIn_Data_Source
					$checkinDataSourceQry=$this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendance, array('Checkin_Data_Source'))
																->where('Attendance_Id = ?',$attendanceId)
																->where('PunchIn_Date  = ?',$punchInDate)
																->where('PunchIn_Time  = ?',$punchInTime));
					if($checkinDataSourceQry!='')	
					{
						$checkDataSourceArr['Checkin_Data_Source']=$checkinDataSourceQry;
					}
					else
					{
						$checkDataSourceArr['Checkin_Data_Source']=$checkIn;
					}
				}
				else
				{
				$checkDataSourceArr['Checkin_Data_Source'] = '';
				}
				
				if(!empty($punchOutDate))
				{
					//check whether punchout date and time is same exisiting punchout time in that record.if yes we need to maintatin the CheckOut_Data_Source
					$checkoutDataSourceQry=$this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendance, array('Checkout_Data_Source'))
										->where('Attendance_Id = ?',$attendanceId)
										->where('PunchOut_Date  = ?',$punchOutDate)
										->where('PunchOut_Time  = ?',$punchOutTime));
					if($checkoutDataSourceQry!='')	
					{
						$checkDataSourceArr['Checkout_Data_Source']=$checkoutDataSourceQry;
					}
					else
					{
						$checkDataSourceArr['Checkout_Data_Source']=$checkOut;
					}
				}
				else
				{
					$checkDataSourceArr['Checkout_Data_Source'] = '';
				}
			}
			else
			{
				if(!empty($punchInDate))
				{
					$checkDataSourceArr['Checkin_Data_Source']=$checkIn;
				}
				else
				{
					$checkDataSourceArr['Checkin_Data_Source'] = '';
					
				}
				
				if(!empty($punchOutDate))
				{
				$checkDataSourceArr['Checkout_Data_Source']=$checkOut;	
				}
				else
				{
					$checkDataSourceArr['Checkout_Data_Source'] = '';
				}
			}
		}
		
        return $checkDataSourceArr;
	}

		//To get CheckIn checkOut data source

		public function getGeolocation($attendanceId,$latitude,$longitude,$punchInDate,$punchInTime,$punchOutDate,$punchOutTime,$isTheme)
		{
			$address = NULL;

			if($this->_orgDetails['Location_Translation'] == 1)
			{
				if(!empty($latitude) && !empty($longitude))
				{
					$curl = curl_init();

					$url = 'https://maps.googleapis.com/maps/api/geocode/json?latlng='.$latitude.','.$longitude.'&key='.Zend_Registry::get('googleAddressAPIKey');
	
					curl_setopt($curl, CURLOPT_URL, $url);
					curl_setopt($curl, CURLOPT_HTTPHEADER, array(
					   'Content-Type: application/json',
					));
					curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
					curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
					// EXECUTE:
					$result = curl_exec($curl);
	
					$response = json_decode($result, true);
	
					if($response['status'] == 'OK')
					{
						$address = $response['results'][0]['formatted_address'];
					}
					elseif($response['status'] == 'ZERO_RESULTS')
					{
						$address = 'Address not found';
					}
					else
					{
						$address = 'Not available';
					}
				}
				else
				{
					$address = NULL;
				}
			}
			else
			{
				$address = NULL;
			}

			$checkGeolocation=array();
			if ($isTheme == 'Bootstrap' || $isTheme == 'DashBoard' || $isTheme =='dashboardMissedCheckout')
			{
				if($attendanceId>0)
				{
					$checkInQry=$this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->attendance, array('PunchIn_Date','PunchIn_Time','Checkin_Latitude','Checkin_Longitude','Checkin_Address'))
					->where('Attendance_Id = ?',$attendanceId));
					
						$checkGeolocation['Checkin_Latitude']=$checkInQry['Checkin_Latitude'];
						$checkGeolocation['Checkin_Longitude']=$checkInQry['Checkin_Longitude'];
						$checkGeolocation['Checkin_Address']=$checkInQry['Checkin_Address'];
						$checkGeolocation['Checkout_Latitude']=$latitude;
						$checkGeolocation['Checkout_Longitude']=$longitude;
						$checkGeolocation['Checkout_Address']= $address;
				}
				else
				{
					$checkGeolocation['Checkin_Latitude']=$latitude;
					$checkGeolocation['Checkin_Longitude']=$longitude;
					$checkGeolocation['Checkin_Address']=$address;
					$checkGeolocation['Checkout_Latitude']='';
					$checkGeolocation['Checkout_Longitude']='';
					$checkGeolocation['Checkout_Address']='';
				}
			}
			else
			{
				if($attendanceId > 0)
				{
					if(!empty($punchInDate))
					{
						//check whether punchin date and time is same exisiting punchin date and time in that record.if yes we need to maintatin the CheckIn_Latitude abd checkin_longitude
						$checkinDataSourceQry=$this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->attendance, array('Checkin_Latitude','Checkin_Longitude','Checkin_Address'))
																	->where('Attendance_Id = ?',$attendanceId)
																	->where('PunchIn_Date  = ?',$punchInDate)
																	->where('PunchIn_Time  = ?',$punchInTime));
						if(count($checkinDataSourceQry)>0)
						{
							$checkGeolocation['Checkin_Latitude']=$checkinDataSourceQry[0]['Checkin_Latitude'];
							$checkGeolocation['Checkin_Longitude']=$checkinDataSourceQry[0]['Checkin_Longitude'];
							$checkGeolocation['Checkin_Address']=$checkinDataSourceQry[0]['Checkin_Address'];
						}
						else
						{
							$checkGeolocation['Checkin_Latitude']=$latitude;
							$checkGeolocation['Checkin_Longitude']=$longitude;
							$checkGeolocation['Checkin_Address']=$address;
						}
					}
					else
					{
						$checkGeolocation['Checkin_Latitude'] = '';
						$checkGeolocation['Checkin_Longitude'] = '';
						$checkGeolocation['Checkin_Address'] = '';
					}
					
					if(!empty($punchOutDate))
					{
						//check whether punchout date and time is same exisiting punchout time in that record.if yes we need to maintatin the Checkout_latitude and Checkout_Longitude
						$checkoutDataSourceQry=$this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->attendance, array('Checkout_Latitude','Checkout_Longitude','Checkout_Address'))
											->where('Attendance_Id = ?',$attendanceId)
											->where('PunchOut_Date  = ?',$punchOutDate)
											->where('PunchOut_Time  = ?',$punchOutTime));
						
						if(count($checkoutDataSourceQry)>0)
						{
							$checkGeolocation['Checkout_Latitude']=$checkoutDataSourceQry[0]['Checkout_Latitude'];
							$checkGeolocation['Checkout_Longitude']=$checkoutDataSourceQry[0]['Checkout_Longitude'];
							$checkGeolocation['Checkout_Address']=$checkoutDataSourceQry[0]['Checkout_Address'];
						}
						else
						{
							$checkGeolocation['Checkout_Latitude']=$latitude;
							$checkGeolocation['Checkout_Longitude']=$longitude;
							$checkGeolocation['Checkout_Address']=$address;
						}
					}
					else
					{
						$checkGeolocation['Checkout_Latitude']="";
						$checkGeolocation['Checkout_Longitude']="";
						$checkGeolocation['Checkout_Address']="";
					}
				}
				else
				{
					if(!empty($punchInDate))
					{
						$checkGeolocation['Checkin_Latitude']=$latitude;
						$checkGeolocation['Checkin_Longitude']=$longitude;
						$checkGeolocation['Checkin_Address']=$address;
					}
					else
					{
						$checkGeolocation['Checkin_Latitude']="";
						$checkGeolocation['Checkin_Longitude']="";
						$checkGeolocation['Checkin_Address']="";
					}
					
					if(!empty($punchOutDate))
					{
						$checkGeolocation['Checkout_Latitude']=$latitude;
						$checkGeolocation['Checkout_Longitude']=$longitude;	
						$checkGeolocation['Checkout_Address']=$address;	
					}
					else
					{
						$checkGeolocation['Checkout_Latitude']="";
						$checkGeolocation['Checkout_Longitude']="";
						$checkGeolocation['Checkout_Address']="";
					}
				}
			}
			
			return $checkGeolocation;
		}	


	 /**
	 * Update status and comment
	 */
	public function statusReport($commentArray, $formName,$customFormName,$sessionId)
	{
		$attendanceDetails = $this->_db->fetchRow($this->_db->select()->from(array('A'=>$this->_ehrTables->attendance),
								array('A.Employee_Id','Attendance_PunchIn_Date'=>new Zend_Db_Expr('(Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T"))'),
								'A.Attendance_Date','A.PunchOut_Date','A.PunchOut_Time','A.Approval_Status'))
								->where('Attendance_Id =?', $commentArray['Parent_Id']));


		$payslipExist =	$this->payslipExistForAttendanceDate($attendanceDetails['Employee_Id'],$attendanceDetails['Attendance_PunchIn_Date']);
		if(!empty($payslipExist))
		{
			return array('success'=>false,'attendanceDetails'=>$attendanceDetails,'approvalStatus'=>$commentArray['Approval_Status']);
		}
		else 
		{
			
			
				$commentArray['Approved_By']  = $sessionId;
					$commentArray['Approved_On'] = date('Y-m-d H:i:s');
				
				$updated = $this->_db->update($this->_ehrTables->attendance,  array('Approval_Status' => $commentArray['Approval_Status'],
																					'Approved_By'=>$commentArray['Approved_By'],
																					'Approved_On'=>$commentArray['Approved_On']),
																					'Attendance_Id = ' . $commentArray['Parent_Id']);
			
			
				unset($commentArray['Approved_By']);
				unset($commentArray['Approved_On']);
			
			
				
				if(!empty($commentArray['Emp_Comment']) && $updated)
				{
					$commentArray['Form_Id']  = $this->_dbComment->getFormId($formName);
					$commentArray['Added_On'] = date('Y-m-d H:i:s');
					
					$this->_db->insert($this->_ehrTables->comment,$commentArray);
				}
				
				$this->_dbCommonFun->updateResult (array('updated'        => $updated,
														'action'         => 'Edit',
														'trackingColumn' => $commentArray['Parent_Id'].','.$commentArray['Approval_Status'],
														'formName'       => $customFormName.' Status',
														'sessionId'      => $commentArray['Employee_Id'],
														'tableName'      => $this->_ehrTables->attendance));
				
				if ( $updated)
				{
					$this->updateCompOffBalance($commentArray);
					return array('success'=>true,'attendanceDetails'=>$attendanceDetails,'approvalStatus'=>$commentArray['Approval_Status']);
				}
				else
				{
					return array('success'=>false,'attendanceDetails'=>$attendanceDetails,'approvalStatus'=>$commentArray['Approval_Status']);
				}
			
		}
	}

	/*check whether the attendance exist for given duration*/
	public function getAttendanceExist($attendanceArray)
	{
		$tempAttendanceCnt = 0;
		$punchInSec = date('Y-m-d H:i:s',strtotime($attendanceArray['PunchIn_Date'] .' '. $attendanceArray['PunchIn_Time']));

		if(!empty($attendanceArray['PunchOut_Date']) && !empty($attendanceArray['PunchOut_Time']))
			$punchOutSec = date('Y-m-d H:i:s',strtotime($attendanceArray['PunchOut_Date'] .' '. $attendanceArray['PunchOut_Time']));
		else
			$punchOutSec = '';
		
		$statusConditions = $this->_db->quoteInto('Approval_Status = ? or ', "Approved") .
							$this->_db->quoteInto('Approval_Status = ? ', "Applied");
		 
		
		if(!empty($punchOutSec))
		{
			$punchConditions = $this->_db->quoteInto(new Zend_Db_Expr(' Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T") BETWEEN ?'),$punchInSec)
								.$this->_db->quoteInto(new Zend_Db_Expr('AND ?'), $punchOutSec)
								.' OR '. $this->_db->quoteInto(new Zend_Db_Expr(' Date_Format(Concat(PunchOut_Date," ",PunchOut_Time), "%Y-%m-%d %T") BETWEEN  ?'),$punchInSec)
								.$this->_db->quoteInto(new Zend_Db_Expr('AND ?'), $punchOutSec);
				
			$qryAttendanceCnt = $this->_db->select()->from($this->_ehrTables->attendance, array('Count(Attendance_Id)'))
										->where('Employee_Id = ?', $attendanceArray['Employee_Id'])
										->where($statusConditions)
										->where($punchConditions);
										
			if($attendanceArray['Attendance_Id'] > 0)
			{
				$qryAttendanceCnt->where('Attendance_Id != ?',$attendanceArray['Attendance_Id']);
			}
				
			$tempAttendanceCnt = $this->_db->fetchOne($qryAttendanceCnt);
		}
		 
		if ( empty($punchOutSec) || $tempAttendanceCnt == 0)
		{
			
			$punchConditions = $this->_db->quoteInto(new Zend_Db_Expr(' ? BETWEEN Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T") AND Date_Format(Concat(PunchOut_Date," ",PunchOut_Time), "%Y-%m-%d %T")'), $punchInSec);
			
			if(!empty($punchOutSec))
			{
				$punchConditions .= ' OR '. $this->_db->quoteInto(new Zend_Db_Expr(' ? BETWEEN Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T") AND Date_Format(Concat(PunchOut_Date," ",PunchOut_Time), "%Y-%m-%d %T")'), $punchOutSec);
			}
			
			$qryAttendanceCnt = $this->_db->select()->from($this->_ehrTables->attendance, array('Count(Attendance_Id)'))
										->where('Employee_Id = ?', $attendanceArray['Employee_Id'])
										->where($statusConditions)
										->where($punchConditions);
										
			if($attendanceArray['Attendance_Id'] > 0)
			{
				$qryAttendanceCnt->where('Attendance_Id != ?',$attendanceArray['Attendance_Id']);
			}
		
			$isExists = $this->_db->fetchOne($qryAttendanceCnt);
		}
		else
		{
			$isExists = $tempAttendanceCnt;
		}
		
		return $isExists;
	}
	
	//adding attendance details for the employees using bulk attendance update
	public function bulkAttendanceUpdate($parentId, $formData, $comment, $formName, $sessionEmp,$status, $customFormName)
	{
		$hrReports = new Reports_Model_DbTable_HrReports();
		$insertAttendance = 0;
		$updatedAttendance = 0;
		$addComment = array();
		$formId = $this->_dbComment->getFormId($formName);

		for($i = 0;$i<count($formData);$i++)
		{ 
			if(empty($formData[$i]['PunchOut_Date']))
			{
				$formData[$i]['PunchOut_Date'] = NULL;
				$formData[$i]['PunchOut_Time'] = NULL;
			}
			$checkInConsiderationTime = $this->getValidPunchIn($formData[$i]['Employee_Id'], $formData[$i]['PunchIn_Date'], date("H:i:s", strtotime($formData[$i]['PunchIn_Time'])));
			$checkOutConsiderationTime = $this->getValidPunchOut($formData[$i]['Employee_Id'], $formData[$i]['PunchOut_Date'], date("H:i:s", strtotime($formData[$i]['PunchOut_Time'])), $formData[$i]['PunchIn_Date'],date("H:i:s", strtotime($formData[$i]['PunchIn_Time'])));

			if($checkInConsiderationTime == 0 && ($checkOutConsiderationTime==0 || empty($formData[$i]['PunchOut_Time'])))
			{
				/*attendance should not exist for this same duration*/
				$isExists = $this->getAttendanceExist($formData[$i]);
				$attendancePunchInDate = date('Y-m-d H:i:s',strtotime($formData[$i]['PunchIn_Date'] .' '. $formData[$i]['PunchIn_Time']));
				$checkOutBufferTime = $this->getCurrentWorkScheduleDetails($formData[$i]['Employee_Id'],$attendancePunchInDate);
				$startDate = date('Y-m-d',strtotime($checkOutBufferTime['Regular_From']));
				/*check whether full day compensatory off exist and full day leave exist for that employee.we should not consider the late attendance leave*/
				$fullDayCompOffExist = $hrReports->getCompensatoryOff($formData[$i]['Employee_Id'],$startDate,1,'','dashboardNoAttendance');
				$fullDayLeaveExist   = $hrReports->getLeaveDuration($formData[$i]['Employee_Id'],$startDate,1,'','dashboardNoAttendance',1);
				$formData[$i]['Attendance_Date'] = $startDate;
				if( $isExists == 0 && empty($fullDayLeaveExist) && empty($fullDayCompOffExist))
				{
					if(!empty($formData[$i]['PunchOut_Date'])){
						if($checkOutBufferTime['Check_Out_Time_Buffer'] > 0 )
						{
							/*This function help us to add the buffer time to punchout time.*/
							$getActualCheckOut=$this->_dbWorkSchedule->getActualPunchOutDetails($formData[$i],$checkOutBufferTime);
							$formData[$i]['Actual_PunchOut_Time'] 	= $getActualCheckOut['Actual_PunchOut_Time'];
							$formData[$i]['PunchOut_Time']			= $getActualCheckOut['PunchOut_Time'];
						}
						else
						{
							$formData[$i]['Actual_PunchOut_Time'] = date('H:i:s',strtotime($formData[$i]['PunchOut_Time']));
							$formData[$i]['PunchOut_Time'] = date('H:i:s',strtotime($formData[$i]['PunchOut_Time']));
						}
					}	
					
					if($checkOutBufferTime['Grace_Time_Flag']==1)
					{
						$getActualPunchInDetails=$this->_dbWorkSchedule->getActualPunchInDetails($formData[$i],$checkOutBufferTime);
						$formData[$i]['Actual_Punch_In_Time']=$getActualPunchInDetails['Actual_Punch_In_Time'];
						$formData[$i]['PunchIn_Time']=$getActualPunchInDetails['PunchIn_Time'];
						$formData[$i]['Actual_Total_Hours']=$getActualPunchInDetails['Actual_Total_Hours'];
						$formData[$i]['Total_Hours']=$this->getTotalHours($formData[$i]);
					}
				
					if($formData[$i]['Total_Hours'] >= 0)
					{
						$inserted = $this->_db->insert($this->_ehrTables->attendance,$formData[$i]);
						$updatedAttendance++;
						$lastId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendance,new Zend_Db_Expr('MAX(Attendance_Id)')));
						if ($inserted)
						{
							if (!empty($comment))
							{
								$addComment[] = array( 'Form_Id'      => $formId,
													'Emp_Comment'     => $comment,
													'Parent_Id'       => $lastId,
													'Approval_Status' => $status,
													'Employee_Id'     => $sessionEmp,
													'Added_On'        => date('Y-m-d H:i:s'));
							}
							$insertAttendance++;
						}
					}
				}	
				
			}
		}
		
		if ($insertAttendance > 0)
		{
			if(count($addComment) > 0)
			{
				$this->_ehrTables->insertMultiple($this->_ehrTables->comment,$addComment);
			}
		}
		if($updatedAttendance ==0)
		{
			return array('success' => false, 'msg'=>'Unable to add attendance due to the presence of leave or comp off or attendance or unscheduled shift/work schedule', 'type'=>'info');
		}
		else if(count($formData)> $updatedAttendance)
		{
			return array('success' => true, 'msg'=>'Attendance was not added for some of the employees due to leave or comp off or attendance or unscheduled shift/work schedule', 'type'=>'info');
		}
		else
		{
			return $this->_dbCommonFun->updateResult (array('updated'        => $insertAttendance,
															'action'         => 'Add',
															'trackingColumn' => $parentId,
															'formName'       => $customFormName,
															'sessionId'      => $sessionEmp,
															'tableName'      => $this->_ehrTables->attendance));
		}
		 
	}
	
	//to delete attendance
	public function deleteAttendance($attendanceId, $sessionId, $formName, $customFormName)
	{
		$deleted 				= 0;
		$compOffExist 			= 0;
		$compOffEncashmentExist = 0;
		$payslipExist 			= 0;
		$overtimeClaimExist 	= 0;
		$currentWorkScheduleDetails = array();
		$attendanceRow = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->attendance, array('*',
																				'Attendance_PunchIn_Date'=>new Zend_Db_Expr('(Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T"))')))
											  								  ->where('Attendance_Id = ?', $attendanceId));

		/*When the attendance record is approved when we try to delete that approved record in that time only we need to check
		When the employee has comp off,comp off encashment and payslip for that particular day*/																		
		if($attendanceRow['Approval_Status'] == 'Approved')
		{
			$payslipExist =	$this->payslipExistForAttendanceDate($attendanceRow['Employee_Id'],$attendanceRow['Attendance_PunchIn_Date']);
			$attendanceRow['Parent_Id'] = $attendanceId;
			$configurationDetails 		= $this->getCompOffConfigurationDetails($attendanceRow);
			$compOffBalanceDetails  	= $configurationDetails['Comp_Off_Balance'];
			$specialWageDetails 		= $configurationDetails['Special_Wage'];
			$attendanceDetails 			= $configurationDetails['Attendance_Details'];	
			$currentWorkScheduleDetails = $configurationDetails['Current_Work_Schedule_Details'];	

			/*When the employee change the special wage configuration settings/workschedule configuration in that compOffBalanceDetails returned as empty in that time 
			we need to use below code to get the compoff balance details */
			if(empty($compOffBalanceDetails))
			{
				$startDate  			= $configurationDetails['Start_Date'];
				$compOffBalanceDetails 	= $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->compOffBalance, array('Comp_Off_Balance_Id','Attendance_Id','Employee_Id','Worked_Date','Expiry_Date','Total_Days','Remaining_Days','Comp_Off_Attendance_Balance','Comp_Off_Overtime_Claim_Balance','Source'))
																							->where('Employee_Id = ?', $attendanceRow['Employee_Id'])
																							->where('Worked_Date = ?', $startDate));
			}

			if(!empty($compOffBalanceDetails))
			{
				$compOffStatus = array('Rejected','Cancelled');
				$compOffExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->compOff, new Zend_Db_Expr('COUNT(Comp_Off_Balance_Id)'))
																	->where('Approval_Status NOT IN (?)',$compOffStatus)
																	->where('Comp_Off_Balance_Id = ?', $compOffBalanceDetails['Comp_Off_Balance_Id']));

				$compOffEncashmentExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->compOffEncashment,new Zend_Db_Expr('COUNT(Comp_Off_Encashment_Id)'))
																	->where('Comp_Off_Balance_Id = ?', $compOffBalanceDetails['Comp_Off_Balance_Id']));													
			}
		}
		else 
		{
			//when the attendance is not approved we need to call the function to get current workschedule details 
			$currentWorkScheduleDetails = $this->getCurrentWorkScheduleDetails($attendanceRow['Employee_Id'],$attendanceRow['Attendance_PunchIn_Date']);	
		}

		
		if(!empty($currentWorkScheduleDetails))
		{
			$overtimeClaimExist = $this->_db->fetchOne($this->_db->select()->from(array('OC'=>$this->_ehrTables->overtimeClaims),new Zend_Db_Expr('COUNT(Overtime_Claim_Id)'))
									->where('OC.Employee_Id = ?', $attendanceRow['Employee_Id'])
									->where('OC.Approval_Status != ?','Rejected')
									->where('OC.Start_Date_Time >= ?', $currentWorkScheduleDetails['Consideration_From'])
									->where('OC.Start_Date_Time <= ?', $currentWorkScheduleDetails['Consideration_To'])
									->where('OC.End_Date_Time >= ?', $currentWorkScheduleDetails['Consideration_From'])
									->where('OC.End_Date_Time <= ?', $currentWorkScheduleDetails['Consideration_To']));
		}

		/*When the employee has comp off,comp off encashment,payslip and overtime claim for that particular day 
		we should not allow the user to delete the attendance record*/
		if(empty($compOffExist) && empty($compOffEncashmentExist) && empty($payslipExist) && empty($overtimeClaimExist))
		{
			if ($attendanceRow['Lock_Flag'] == 0 || $attendanceRow['Approval_Status'] == 'Draft') 
			{
				$attendanceRow['Lock_Flag'] = 0;
				
				$updatedStatus = $this->updateAttendanceImportData($attendanceId,'',$sessionId);
				unset($attendanceRow['Attendance_PunchIn_Date']);
				if(isset($attendanceRow['Parent_Id']))
				{
					unset($attendanceRow['Parent_Id']);
				}
				$attendanceRow['Deleted_On'] = date('Y-m-d H:i:s');
				$attendanceRow['Deleted_By'] = $sessionId;
				$inserted = $this->_db->insert($this->_ehrTables->archiveAttendance, $attendanceRow);

				$deleted = $this->_db->delete($this->_ehrTables->attendance,'Attendance_Id='.$attendanceId);

				if ($deleted)
				{
					$this->_dbComment->deleteComment($attendanceId, $formName);
				}
				if($deleted)
				{
					$ignoreShortageWhere['Employee_Id = ?'] = $attendanceRow['Employee_Id'];
					$ignoreShortageWhere['Attendance_Date = ?'] = $attendanceRow['Attendance_Date'];

					//Delete attendance ignore shortage records					
					$this->_db->delete($this->_ehrTables->ignoreEmpAttshortagelist,$ignoreShortageWhere);

					if($attendanceRow['Late_Attendance']==2 || $attendanceRow['Late_Attendance']==4)
					{
						//Delete late attendance
						$delLeaveDetailsQuery = $this->_db->select()
											->from($this->_ehrTables->empLeaves, array('Leave_Id','Total_Days','LeaveType_Id','Approval_Status'))
											->where('Start_Date = ?', $attendanceRow['Attendance_Date'])
											->where('Employee_Id = ?', $attendanceRow['Employee_Id'])
											->where('Reason = ?', 'Late Attendance')
											->where('Late_Attendance != ?',0);

						$leaveData = $this->_db->fetchRow($delLeaveDetailsQuery);	
						$dbLeave = new Employees_Model_DbTable_Leave();
						/** If the approval status is approved or cancel applied then we should revert the leave balance */
						if(!empty($leaveData)){
							if($leaveData['Approval_Status'] === 'Approved' || $leaveData['Approval_Status'] === 'Cancel Applied'){
								$cancelLeaveBalance = $dbLeave->cancelLeaveBalance($leaveData['Total_Days'], $attendanceRow['Employee_Id'], $leaveData['LeaveType_Id']);
								if(!empty($cancelLeaveBalance)){
									$deleteLeave = $dbLeave->deleteLeaveRecord($leaveData['Leave_Id'], 'Leaves');
								}
							}
							else
							{
								$deleteLeave = $dbLeave->deleteLeaveRecord($leaveData['Leave_Id'], 'Leaves');
							}
						}
					}
					
					if(!empty($compOffBalanceDetails)&&!empty($specialWageDetails)&&$specialWageDetails['Comp_Off']=='Yes'&&!empty($attendanceDetails) && $attendanceRow['Approval_Status'] == 'Approved')
					{
						$compOffDetails = $this->calculateCompOffBalance($attendanceDetails['Employee_Id'],$compOffBalanceDetails,$attendanceDetails,$specialWageDetails,$currentWorkScheduleDetails);
						
						$compOffExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->compOff, new Zend_Db_Expr('COUNT(Comp_Off_Balance_Id)'))
																				->where('Comp_Off_Balance_Id = ?', $compOffBalanceDetails['Comp_Off_Balance_Id']));
						/* when there is comp off record exist in any status we should not delete the comp off balance record we need to update the balance*/													
						if(!empty($compOffDetails['Total_Days']) || !empty($compOffExist))
						{
							$updated = $this->_db->update($this->_ehrTables->compOffBalance, $compOffDetails,array('Comp_Off_Balance_Id = '. $compOffBalanceDetails['Comp_Off_Balance_Id']));
						}
						else 
						{
							//delete the comp off balance record
							$this->_db->delete($this->_ehrTables->compOffBalance,'Comp_Off_Balance_Id='.$compOffBalanceDetails['Comp_Off_Balance_Id']);
						}
					}

					if($attendanceRow['Auto_Short_Time_Off']=='Yes')
					{
						$lateAttendanceShortTimeOffWhere['Employee_Id = ?'] = $attendanceRow['Employee_Id'];
						$lateAttendanceShortTimeOffWhere['Short_Time_Off_Date = ?'] = $attendanceRow['Attendance_Date'];
						$lateAttendanceShortTimeOffWhere['Reason = ?'] ='Late Attendance';
						$this->_db->delete($this->_ehrTables->shortTimeOff,$lateAttendanceShortTimeOffWhere);
					}
				}
			}
		}
		
		/**
		*	delete activity for common function
		*		1)check lock is exist or not.
		*			If lock is exist then show error message like employee is open record for update.
		*		2)If No lockflag then process delete activity
		*		3)Update delete activity in system log
		*		4)return success/failure message
	   */
		$this->_dbCommonFun->deleteRecord (array('deleted'       => $deleted,
												'lockFlag'       => $attendanceRow['Lock_Flag'],
												'formName'       => $customFormName,
												'trackingColumn' => $attendanceId,
												'sessionId'      => $sessionId));
		if($deleted){
			return array('success'=>true,'attendanceDetails'=>$attendanceRow);
		} else {
			return array('success'=>false,'attendanceDetails'=>$attendanceRow);
		}
	}
	/*Check payslip exist for particular attendance date for that employee*/
	public function payslipExistForAttendanceDate($employeeId,$punchInDate)
	{
		$payslipExist = 1;
		$graceTimeDetails=$this->getCurrentWorkScheduleDetails($employeeId,$punchInDate);
		if(!empty($graceTimeDetails))
		{
            $attendanceActualDate = date('Y-m-d',strtotime($graceTimeDetails['Regular_From']));

			$salaryDateDetails = $this->_dbPayslip->getSalaryDateRange(date('m',strtotime($attendanceActualDate)),date('Y',strtotime($attendanceActualDate)),strtotime($attendanceActualDate), 29);
		
			$salaryMonth = date('n,Y', strtotime($salaryDateDetails['Last_SalaryDate'])); 
		
			$payslipExist = $this->_dbCommonFun->payslipExist($employeeId,$salaryMonth);
		}
		return $payslipExist;										
	}

	//update the attendance import status to unprocessed while deleting the attendance record
	
	public function updateAttendanceImportData($attendanceId,$attendanceImportExist,$sessionId)
	{
		$punchInAttendanceId  ='';
		$punchOutAttendanceId ='';
		$updated = 0;

		$qryAttedanceDetails = $this->_db->select()->from(array('EA'=>$this->_ehrTables->attendance), array('Approval_Status',new Zend_Db_Expr('Date_Format(Concat(PunchIn_Date," ",Actual_Punch_In_Time), "%Y-%m-%d %T") as Added_On_In')
									  ,new Zend_Db_Expr('Date_Format(Concat(PunchOut_Date," ",Actual_PunchOut_Time), "%Y-%m-%d %T") as Added_On_Out')))
								->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.Employee_Id=EA.Employee_Id',array('External_EmpId as EmpId'))	  
								->where('Attendance_Id = ?', $attendanceId);
		
		$getAttendanceDetails = $this->_db->fetchRow($qryAttedanceDetails);

		if(!empty($getAttendanceDetails['Added_On_In']) && !empty($getAttendanceDetails['EmpId']))
		{
			$punchInAttendanceId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendanceImport, array('Attendance_Id'))
																	->where('Added_On = ?', $getAttendanceDetails['Added_On_In'])
																	->where('Employee_Id = ?',$getAttendanceDetails['EmpId']));
			
			if(!empty($punchInAttendanceId) && !empty($attendanceImportExist))
			{
				$updated = 1;
			}														
			if(!empty($punchInAttendanceId) && empty($attendanceImportExist))
			{
				$updated = $this->_db->update($this->_ehrTables->attendanceImport, array('Rollup_Flag'=>0,'Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$sessionId), array('Attendance_Id = ?'=>$punchInAttendanceId));
			}														
		}
		
	
		if(!empty($getAttendanceDetails['Added_On_Out']) && !empty($getAttendanceDetails['EmpId']))
		{
			$punchOutAttendanceId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendanceImport, array('Attendance_Id'))
																->where('Added_On = ?', $getAttendanceDetails['Added_On_Out'])
																->where('Employee_Id = ?',$getAttendanceDetails['EmpId']));
			
			
			if(!empty($punchOutAttendanceId) && !empty($attendanceImportExist))
			{
				$updated = 1;
			}																	
			else if(!empty($punchOutAttendanceId) && empty($attendanceImportExist))
			{
				$updated = $this->_db->update($this->_ehrTables->attendanceImport, array('Rollup_Flag'=>0,'Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$sessionId), array('Attendance_Id = ?'=>$punchOutAttendanceId));
			}													
		}
/* when the attendance process type is 1 first in & last out method in that time below function will run*/
		if($this->_orgDetails['Attendance_Process_Type_Id']==1)
		{
			if(!empty($getAttendanceDetails['Added_On_In']) && !empty($getAttendanceDetails['Added_On_Out']) && !empty($getAttendanceDetails['EmpId']))
			{
			
				$punchOutAttendanceId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->attendanceImport, array('Attendance_Id'))
																	->where('Added_On >= ?', $getAttendanceDetails['Added_On_In'])
																	->where('Added_On <= ?', $getAttendanceDetails['Added_On_Out'])
																	->where('Employee_Id = ?',$getAttendanceDetails['EmpId']));
				
				
				if(!empty($punchOutAttendanceId) && !empty($attendanceImportExist))
				{
					$updated = 1;
				}																	
				else if(!empty($punchOutAttendanceId) && empty($attendanceImportExist))
				{
					$whereAttendanceImport['Attendance_Id IN (?)'] = $punchOutAttendanceId;
                    $updated =  $this->_db->update($this->_ehrTables->attendanceImport, array('Rollup_Flag'=>0,'Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$sessionId), $whereAttendanceImport);
				}
			}														
		}

		return $updated;
	}

	//to fetch attendance based on the attendance id (Copy bulb attendance)
	public function viewAttendance($attendanceId)
	{
		if ($attendanceId != "")
		{
			return $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->attendance,
																   array(new Zend_Db_Expr("DATE_FORMAT(PunchIn_Date,'".$this->_orgDF['sql']."') as PunchIn_Date"),
																		 new Zend_Db_Expr("DATE_FORMAT(PunchOut_Date,'".$this->_orgDF['sql']."') as PunchOut_Date"),
																		 new Zend_Db_Expr("DATE_FORMAT(PunchIn_Time,'%H:%i:%s') as PunchIn_Time"),
																		 new Zend_Db_Expr("DATE_FORMAT(PunchOut_Time,'%H:%i:%s') as PunchOut_Time"),
																		 'PunchIn_Time as PunchIn_Time_Seconds','Auto_Short_Time_Off',
																		 'Total_Hours','Status'=>'Approval_Status','Attendance_Id','Total_Hours','Exclude_Break_Hours', 'Late_Attendance','Checkin_Work_Place_Id','Attendance_Date',
																		 'Employee_Id', 'Approver_Id', 'PunchIn_Date as DPunchIn_Date', 'PunchOut_Date as DPunchOut_Date', 'Checkin_Form_Source',
																		 'PunchIn_Time as DPunchIn_Time','PunchOut_Time as DPunchOut_Time'))
										
										->where('Attendance_Id = ?',$attendanceId));
		}
	}
	
	/**
	 * Get attendance details based on attendanceId (Status Approval)
	 */
	public function attendanceEmployee($attendanceId)
	{
		$qryEmpId = $this->_db->select()->from($this->_ehrTables->attendance, array('Employee_Id'))
								->where('Attendance_Id = ?', $attendanceId);
		
		return $this->_db->fetchRow($this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal),
												 array('P.Employee_Id','Employee_Name'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name, ' ', P.Emp_Last_Name)")))
									
									->joinInner(array('J'=>$this->_ehrTables->empJob), 'P.Employee_Id=J.Employee_Id', array())
									->where('J.Emp_Status Like ?', 'Active')
									->orwhere('J.Emp_Status Like ?', 'InActive')
									->where('P.Employee_Id = ?', $qryEmpId)
									->where('P.Form_Status = 1')); 
	}
	
	/**
	 * Get attendance import details to show in a grid
	 */
	public function listAttendanceImport($page, $rows, $sortField, $sortOrder, $searchAll=null, $searchArr, $importAccess)
	{
		$employeeName     = $searchArr['employeeName'];
		$employeeStartId  = $searchArr['employeeStartId'];
		$employeeEndId    = $searchArr['employeeEndId'];
		$beginDate        = $searchArr['beginDate'];
		$endDate          = $searchArr['endDate'];
		$attendanceStatus = $searchArr['attendanceStatus'];
		$rollupStatus     = $searchArr['rollupStatus'];
		$finalExtEmpId    = $searchArr['finalExtEmpId'];
		$errorMessage     = $searchArr['errorMessage'];
		$locationId       = $searchArr['locationId'];
		$serviceProviderId= $searchArr['serviceProviderId'];
		$dataSource 	= $searchArr['dataSource'];

        switch($sortField)
        {
			case 1: $sortField = 'P.Emp_First_Name'; break;
			case 2: $sortField = 'J.External_EmpId'; break;
			case 3: $sortField = 'AI.Added_On'; break;			
			case 4: $sortField = 'AI.Attendance_Status'; break;
			case 5: $sortField = 'Exclude_Break_Hours'; break;
			case 6: $sortField = 'Rollup_Status'; break;
		}
		
		$qryAttendanceImport = $this->_db->select()->from(array('AI'=>$this->_ehrTables->attendanceImport),
														  array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS AI.Attendance_Id as Count'),
																'AI.Schema_Id', 'AI.Rollup_Flag', 'AI.Data_Source', 'AI.Attendance_Status', 'AI.Employee_Id',
																'AI.Attendance_Id', 'AI.Card_No',  'AI.Verify_Code','AI.Added_On',
																'AI.Import_Latitude','AI.Import_Longitude','AI.Import_Address',
																new Zend_Db_Expr("DATE_FORMAT(AI.Added_On,'".$this->_orgDF['sql']." %T') as Date_Time"),
																'AI.Employee_Id', 'AI.Department','AI.Location_Id','AI.Data_Source',
																new Zend_Db_Expr("DATE_FORMAT(AI.Added_On_Date_Time,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On_Date_Time"),
																new Zend_Db_Expr("DATE_FORMAT(AI.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On"),
																'AI.WorkSchedule_Id', 'AI.Id_Number', 'AI.Description', 'AI.Rollup_Flag as Rollup',
																'BreakHrs'=>new zend_Db_Expr('case when Exclude_Break_Hours = 1 then "Excluded" else "Included" end'),
																'Rollup_Status'=>new Zend_Db_Expr('CASE WHEN Rollup_Flag = 0 THEN "Unprocessed"
																								  WHEN Rollup_Flag = 2 THEN "Manual Process"
																								  WHEN Rollup_Flag = 3 THEN "Already Exist"
																								  WHEN Rollup_Flag = 1 THEN "Processed"
																								  WHEN Rollup_Flag = -1 THEN "Invalid Record" END')))
																->joinLeft(array('EC'=>$this->_ehrTables->errorCodes), 'EC.Error_Code=AI.Error_Code',array('EC.Error_Message'))
																->joinLeft(array('J'=>$this->_ehrTables->empJob),'AI.Employee_Id=J.External_EmpId',
																			array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN J.User_Defined_EmpId IS NULL THEN AI.Employee_Id ELSE J.User_Defined_EmpId END')))					
																->joinLeft(array('P'=>$this->_ehrTables->empPersonal),'J.Employee_Id = P.Employee_Id',array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name, ' ', P.Emp_Last_Name)")))
																->joinLeft(array('FS1'=>$this->_ehrTables->forms), 'AI.Form_Source = FS1.Form_Id',array('Form_Name as Attendance_Form_Source'))

																->joinLeft(array('AWP'=>$this->_ehrTables->attendanceWorkPlace), 'AI.Work_Place = AWP.Import_Work_Place',array('AWP.Work_Place'))

																->joinLeft(array('EP1'=>$this->_ehrTables->empPersonal), 'AI.Added_By = EP1.Employee_Id',array('Added_By'=>new Zend_Db_Expr("CONCAT(EP1.Emp_First_Name, ' ', EP1.Emp_Last_Name)")))

																->joinLeft(array('EP2'=>$this->_ehrTables->empPersonal), 'AI.Updated_By = EP2.Employee_Id',array('Updated_By'=>new Zend_Db_Expr("CONCAT(EP2.Emp_First_Name, ' ', EP2.Emp_Last_Name)")))

																->order("$sortField $sortOrder")
																->limit($rows, $page);
		
		/**
		 *	Search All columns using single input
		*/
		
		if (!empty($searchAll) && $searchAll != null && $searchAll != '')
		{
			$conditions = $this->_db->quoteInto(new Zend_Db_Expr('Concat(P.Emp_First_Name," ",P.Emp_Last_Name) Like ?'),"%$searchAll%");
			
			$conditions .= $this->_db->quoteInto('or J.External_EmpId Like ?', "%$searchAll%");
			
			
			$conditions .= $this->_db->quoteInto('or AI.Added_On Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or AI.Attendance_Status Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or AI.Data_Source Like ?', "%$searchAll%");
			
			$breakHrsExist = 0;
			$rollupStatusExist = 0;
			
			if(substr_count("excluded",strtolower($searchAll)) > 0)
			{
				$breakHrsExist = 1;
				$conditions .= $this->_db->quoteInto(' or Exclude_Break_Hours = ?', 1);
			}
			
			if(substr_count("included",strtolower($searchAll)) > 0)
			{
				if($breakHrsExist == 1)
					$conditions .= $this->_db->quoteInto(' and Exclude_Break_Hours = ?', 0);
				else
					$conditions .= $this->_db->quoteInto(' or Exclude_Break_Hours = ?', 0);
			}
			
			if(substr_count("unprocessed",strtolower($searchAll)) > 0)
			{
				$conditions .= $this->_db->quoteInto(' or Rollup_Flag = ?', 0);
			}
			
			if(substr_count("manual process",strtolower($searchAll)) > 0)
			{
				$conditions .= $this->_db->quoteInto(' or Rollup_Flag = ?', 2);
			}
			
			if(substr_count("already exist",strtolower($searchAll)) > 0)
			{
				$conditions .= $this->_db->quoteInto(' or Rollup_Flag = ?', 3);
			}
			
			if(substr_count("processed",strtolower($searchAll)) > 0)
			{
				$conditions .= $this->_db->quoteInto(' or Rollup_Flag = ?', 1);
			}
			
			if(substr_count("invalid record",strtolower($searchAll)) > 0)
			{
				$conditions .= $this->_db->quoteInto(' or Rollup_Flag = ?', -1);
			}


			if(substr_count("there is no attendance pair to process",strtolower($searchAll)) > 0)
			{
				$conditions .= $this->_db->quoteInto(' or AI.Error_Code = ?', 'EAI001');
			}
			
			if(substr_count("work schedule is not configured for this day",strtolower($searchAll)) > 0)
			{
				$conditions .= $this->_db->quoteInto(' or AI.Error_Code = ?', 'EAI002');
			}
			
			if(substr_count("there is no shift schedule for this employee",strtolower($searchAll)) > 0)
			{
				$conditions .= $this->_db->quoteInto(' or AI.Error_Code = ?', 'EAI003');
			}
			
			if(substr_count("this attendance record doesn't fall within the configured boundaries",strtolower($searchAll)) > 0)
			{
				$conditions .= $this->_db->quoteInto(' or AI.Error_Code = ?', 'EAI004');
			}
			if(substr_count("this attendance record or it's pair doesn't fall within the regular schedule",strtolower($searchAll)) > 0)
			{
				$conditions .= $this->_db->quoteInto(' or AI.Error_Code = ?', 'EAI005');
			}
	
			$qryAttendanceImport->where($conditions);

		}
									
		if(empty($importAccess['Admin']))
		{
			$getEmployeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('External_EmpId'))
																			->where('Manager_Id = ?', $importAccess['LogId'])
																			->where('External_EmpId IS NOT NULL'));
			$logId    = $this->getExternalEmployeeId($importAccess['LogId']);
			if(empty($logId))
			{
				//when the logged in employee doesnt have the external employee id we should not list any records.
				$logId=-1;
			}

			if($importAccess['Is_Manager'] == 1 && !empty($getEmployeeId))
			{
				$qryAttendanceImport->where('AI.Employee_Id = :EmpId or AI.Employee_Id IN (?)', $getEmployeeId)
					->bind(array('EmpId'=>$logId));
			}
			else
			{
					$qryAttendanceImport->where('AI.Employee_Id = ?', $logId);
			}
		}
		
		if(!empty($employeeName) && preg_match('/^[a-zA-Z ]/', $employeeName))
	    {  
			$qryAttendanceImport->where($this->_db->quoteInto(new Zend_Db_Expr('Concat(P.Emp_First_Name," ",P.Emp_Last_Name) Like ?'),"%$employeeName%"));
		}
		
		if (! empty($finalExtEmpId))
		{
			$qryAttendanceImport->where('J.External_EmpId = ?', $finalExtEmpId);
		}
		
		if(!empty($beginDate) )
		{
			$qryAttendanceImport->where($this->_db->quoteInto('Date_Format(AI.Added_On, "%Y-%m-%d") >= ?', $beginDate));
		}
		
		if(!empty($endDate) )
		{
			$qryAttendanceImport->where($this->_db->quoteInto('Date_Format(AI.Added_On, "%Y-%m-%d") <= ?', $endDate));
		}
		
		if(!empty($attendanceStatus))
		{
			$qryAttendanceImport->where('AI.Attendance_Status = ?', str_replace("-","/", $attendanceStatus));
		}
		
		if($rollupStatus!='')
		{
			$qryAttendanceImport->where('Rollup_Flag = ?', $rollupStatus);
		}

		if($errorMessage!='')
		{
			$qryAttendanceImport->where('AI.Error_Code = ?', $errorMessage);
		}
		
		if(!empty($serviceProviderId)&& $this->_orgDetails['Field_Force']==1)
        {
            $qryAttendanceImport->where('J.Service_Provider_Id = ?',$serviceProviderId);
        }

		if(!empty($formSource))
        {
            $qryAttendanceImport->where('AI.Form_Source = ?',$formSource);
        }

		if(!empty($dataSource))
        {
            $qryAttendanceImport->where('AI.Data_Source = ?',$dataSource);
        }

		if(!empty($locationId))
        {
            $qryAttendanceImport->where('J.Location_Id = ?',$locationId);
        }

		$qryAttendanceImport = $this->_dbCommonFun->getDivisionDetails($qryAttendanceImport,'J.Department_Id');

		if(!empty($importAccess['Admin']))
		{
			$qryAttendanceImport = $this->_dbCommonFun->formServiceProviderQuery($qryAttendanceImport,'J.Service_Provider_Id',$importAccess['LogId']);
		}
		/**
		 * SQL queries
		 * Get data to display
		*/

		 //$qryAttendanceImport->where("J.Emp_Status = ?","Active");
		 $attendanceImport = $this->_db->fetchAll($qryAttendanceImport);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		/* Total data set length */
		$qryAttendanceImportCnt = $this->_db->select()->from($this->_ehrTables->attendanceImport, new Zend_Db_Expr('COUNT(Attendance_Id)'));

		if(empty($importAccess['Admin']))
		{
			$getEmployeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('External_EmpId'))
																			->where('Manager_Id = ?', $importAccess['LogId']));
	
			$logId    = $this->getExternalEmployeeId($importAccess['LogId']);
			if(empty($logId))
			{
				//when the logged in employee doesnt have the external employee id we should not list any records.
				$logId=-1;
			}
			if($importAccess['Is_Manager'] == 1 && !empty($getEmployeeId))
			{
				$qryAttendanceImportCnt->where('Employee_Id = :EmpId or Employee_Id IN (?)', $getEmployeeId)
					->bind(array('EmpId'=>$logId));
			}
			else
			{
				$qryAttendanceImportCnt->where('Employee_Id = ?', $logId);
			}
		}
		
		$iTotal = $this->_db->fetchOne($qryAttendanceImportCnt);
		
		foreach($attendanceImport as $key=>$row)
		{
			if($this->_orgDetails['Attendance_Process_Type_Id']==1)
			{
				$attendanceImport[$key]['Attendance_Status'] = 'First In & Last Out';
			}
		}
		/**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $attendanceImport);
	}

	public function getExternalEmployeeId($employeeId)
	{
		return  $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob,
																	 array('External_EmpId'))
										  ->where('Employee_Id = ?', $employeeId));
		
		
	}

    /* check attendance import data exist for the given date and employee id*/
	public function getAttendanceImportExist($employeeId,$workScheduleDetails,$rollUpFlag='')
    {
        $attendanceImportEmployeeId = $this->getExternalEmployeeId($employeeId);

  		/*External Employee Id is non mandatory field so we need to check whether attendance import employee id empty or not*/
		if(!empty($attendanceImportEmployeeId))
		{
			$attendanceImportExistQuery = $this->_db->select()->from(array('AI'=>$this->_ehrTables->attendanceImport),
											new Zend_Db_Expr('COUNT(Attendance_Id)'))
											->where('Employee_Id = ?', $attendanceImportEmployeeId)
											->where('AI.Added_On >= ?', $workScheduleDetails['Consideration_From'])
											->where('AI.Added_On <= ?', $workScheduleDetails['Consideration_To']);
			if(!empty($rollUpFlag)){
				$attendanceImportExistQuery->where('Rollup_Flag IN (?)', $rollUpFlag);
			}
			$attendanceImportExist = $this->_db->fetchOne($attendanceImportExistQuery);
		}
		else 
		{
			$attendanceImportExist = 0;
		}
	
       	return $attendanceImportExist;  
    }
	
	/* V0.4 all attendance status pair for that schema*/
	public function getAttendanceImportStatus($schemaId)
	{
		return $this->_db->fetchAll($this->_db->select()->union(array($this->_db->select()->from($this->_ehrTables->attendanceStatus,
																								 array('Status_Pair'=>'Status_PairA'))
																	  ->where('Schema_Id = ?', $schemaId),
																	  $this->_db->select()->from($this->_ehrTables->attendanceStatus,
																								 array('Status_Pair'=>'Status_PairB'))
																	  ->where('Schema_Id = ?', $schemaId))));
	}
	
	/**
	 * Update attendance import status
	 */
	public function updateAttendanceImportStatus($attendanceId, $status, $schemaId, $rollupFlag, $sessionId)
	{
		$cntStatus = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendanceStatus,
																	 new Zend_Db_Expr('COUNT(Schema_Id)'))
										  ->where('Status_PairA LIKE ? or Status_PairB LIKE ?', $status)
										  ->where('Schema_Id = ?', $schemaId));
		
		$this->_db->update($this->_ehrTables->attendanceImport, array('Lock_Flag'=>0,'Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$sessionId), array('Attendance_Id = ?'=>$attendanceId));
		 
		if($cntStatus > 0)
		{
			// update only if status != existing status or
			// rollup flag != exiting rollup flag and equals 2[mual process] because update is allowed only if user chooses manual process
			$updated = $this->_db->update($this->_ehrTables->attendanceImport, array('Attendance_Status' => $status,
																					 'RollUp_Flag'       => $rollupFlag,
																					 'Updated_On'=>date('Y-m-d H:i:s'),
																					 'Updated_By'=>$sessionId,
																					 'Lock_Flag'         => 0),
										  array('Attendance_Id = ?'=>$attendanceId));
			
			/**
			*	this function will handle
			*		update system log function
			*		clear submit lock fucntion
			*		return success/failure array
		   */
		   return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
														   'action'         => 'Edit',
														   'trackingColumn' => '',
														   'formName'       => 'Import Data',
														   'sessionId'      => $sessionId,
														   'tableName'      => $this->_ehrTables->attendanceImport));
		}
		else
		{
			return array('success' => false, 'msg'=>'Attendance Status pair not exist', 'type'=>'info');
		}
	}

	/**
	 * Get attendance rollup flag by attendanceId
	 */
	public function getAttendanceRollUpFlag($attendanceId)
	{
		$getAttImportRow = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->attendanceImport,
																		   array('Added_On',
																				 'Rollup_Flag', 'Employee_Id',
																				 'Rollup_Status'=>new Zend_Db_Expr('CASE WHEN Rollup_Flag = 0 THEN "Unprocessed"
																												   WHEN Rollup_Flag = 2 THEN "Manual Process"
																												   WHEN Rollup_Flag = 3 THEN "Already Exist"
																												   WHEN Rollup_Flag = -1 THEN "Invalid Record" END')))
												->where('Attendance_Id = ?', $attendanceId));
		if(!empty($getAttImportRow))
		{
			$getCount = $this->_db->fetchOne($this->_db->select()->from(array('A'=>$this->_ehrTables->attendance), array('Count(Attendance_Id)'))
											 ->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.Employee_Id=A.Employee_Id', array())
											 ->where('External_EmpId = ?', $getAttImportRow['Employee_Id'])
											 ->where(new Zend_Db_Expr('Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T")').' <= ?', $getAttImportRow['Added_On'])
											 ->where(new Zend_Db_Expr('Date_Format(Concat(PunchOut_Date," ",PunchOut_Time), "%Y-%m-%d %T")').' >= ?', $getAttImportRow['Added_On']));
			
			if($getCount > 0 && $getAttImportRow['Rollup_Flag']!=2)
			{
				// if manual attendance entry exist then existing and manual process Rollup status are shown in combobox
				return array(array('Rollup_Flag'=>$getAttImportRow['Rollup_Flag'], 'Rollup_Status'=>$getAttImportRow['Rollup_Status']));
			}
			else
			{
				// if not only existing rollup status is shown
				return array(array('Rollup_Flag'=>$getAttImportRow['Rollup_Flag'], 'Rollup_Status'=>$getAttImportRow['Rollup_Status']),
						array('Rollup_Flag'=>2, 'Rollup_Status'=>'Manual Process'));
			}
		}
	}

	/*get the error code and display it in attendance import filter*/
	public function getAttendanceImportErrorCode()
	{
		$errorCode = array('EAI001','EAI002','EAI003','EAI004','EAI005');
        
		$qryAttendanceImportErrorCode = $this->_db->select()
							->from($this->_ehrTables->errorCodes,array('Error_Code','Error_Message'))
													->where('Error_Code IN (?)', $errorCode);

		$attendanceImportErrorCode=$this->_db->fetchPairs($qryAttendanceImportErrorCode);

		return $attendanceImportErrorCode;
	}

	// process attendance import data to attendance table
	public function processAttendanceImportData($processedInput,$logEmpId)
	{
		$validUpdatedarr	= array();
		$returnVal 			= 0;
		$hrReports 			= new Reports_Model_DbTable_HrReports();
		$attendanceDateFrom = $processedInput['Process_From'];
		$attendanceDateTo   = $processedInput['Process_To'];
		$formName        	= 'Attendance';
		$customFormName  	= $this->_ehrTables->getCustomForms($formName);
		$customFormName  	= ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $formName);
		$attendanceDayIsWorkingDay = 0;

		$dbLeave 			= new Employees_Model_DbTable_Leave();
		$leaveClosureExist  = $dbLeave->getLeaveClosureExist($attendanceDateTo);
		$lateAttendanceSettings     = $this->getLateAttendanceSettings();
		$allAttendanceDetails = [];
		if(!empty($leaveClosureExist) && !empty($lateAttendanceSettings))
		{
			return array('success' =>false, 'msg'=>'Before processing attendance data for the specified date range, please ensure that the leave closure or leave encashment is completed', 'type'=>'warning');
		}

		// Universal employee filtering based on work schedules (if provided)
		$employeeIds = null;
		if(isset($processedInput['workScheduleIds'])&&!empty($processedInput['workScheduleIds']) && is_array($processedInput['workScheduleIds']))
		{
			// Get employee IDs based on work schedules
			$employeeIds = $this->getEmployeeIdsByWorkSchedules(
				$processedInput['workScheduleIds'],
				$attendanceDateFrom,
				$attendanceDateTo
			);

			// If no employees found for the given work schedules, return appropriate message
			if(empty($employeeIds))
			{
				return array('success' => false, 'msg' => 'No eligible employees found for the specified work schedules in the given date range', 'type' => 'warning');
			}
		}


		/* when the attendance process type is 1 first in & last out method in that time below function will run*/
		if($this->_orgDetails['Attendance_Process_Type_Id']==1)
		{
			$attendanceImportedData 					= $this->getAttendanceImportDetails($attendanceDateFrom,$attendanceDateTo,$logEmpId,$employeeIds);
			$attendanceImportDetailsDateBased 			= $attendanceImportedData['dateBasedIndex'];
			$attendanceImportDetailsEmployeeIdAndDate 	= $attendanceImportedData['employeeIdAndDateBasedIndex'];
			$attendanceImportDetailsEmployeeIdDateTime 	= $attendanceImportedData['employeeIdDateTimeBasedIndex'];
			$organizeAttendanceImportDetails 			= $attendanceImportedData['organizeAttendanceImportDetails'];

			$adminIds             = $this->_dbCommonFun->listAdmins();
			$superAdminApproverId = $adminIds[0];

			$eligibleEmployeeId = array_unique(array_column($organizeAttendanceImportDetails,'Actual_Employee_Id'));
			if(!empty($eligibleEmployeeId))
			{
				$shortTimeOffConfiguration 	             = $this->listShortLeaveRequestSettings($eligibleEmployeeId,$attendanceDateFrom,$attendanceDateTo);
				$shortTimeOffConfiguration['Admin_Id']   = $superAdminApproverId;
				$shortTimeOffConfiguration['Session_Id'] = $logEmpId;
			}
			else
			{
				$shortTimeOffConfiguration = array();
			}
			
	        /*for that given date range atleast one or more unprocessed,invalid,already exist,manual process record is there below condition is true*/
			if(!empty($attendanceImportDetailsDateBased))
			{
				$current = strtotime($attendanceDateFrom);
				$last = strtotime($attendanceDateTo);
				
				$step = '+1 day'; /**incrementor in while loop**/
				while( $current <= $last )
				{
					$startDate = date('Y-m-d',$current);
					
					if(isset($attendanceImportDetailsDateBased[$startDate]))
					{
						$getEmployeeId = array_unique(array_column($attendanceImportDetailsDateBased[$startDate],'Employee_Id'));
					}
					else
					{
						$getEmployeeId = array();
					}
					
					/*for this start date if there is any employee id with unprocessed,invalid,already exist,manual process record is there below condition is true*/
					if(!empty($getEmployeeId))
					{
						foreach($getEmployeeId as $employeeId)
						{
							$workScheduleDetails = $this->getGraceTimeDetails($employeeId,$startDate);
							
							$attendanceImportEmployeeId = $this->getExternalEmployeeId($employeeId);										
							
							$approverId = $this->_db->fetchOne($this->_db->select()->from(array('EJ'=>$this->_ehrTables->empJob), array('EJ.Manager_Id'))
											->where('EJ.Employee_Id = ?', $employeeId));

							if(empty($approverId))
							{
								$approverId = $superAdminApproverId;
							}

							/*check whether workschedule/shift configuration done that for particular day*/
			                if(!empty($workScheduleDetails))
							{
				                /** if there is any attendance import record in processed status for this consideration range we need to unprocess the records **/
								$getAttendanceImportId = $this->getAttendanceImportId($attendanceImportEmployeeId,$workScheduleDetails,1);
					
								if(!empty($getAttendanceImportId))
								{ 
									$whereAttendanceImport['Attendance_Id IN (?)'] = $getAttendanceImportId;
									$this->_db->update($this->_ehrTables->attendanceImport, array('Rollup_Flag'=>0,'Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$logEmpId), $whereAttendanceImport);
								}

								/** find the first in and last out attendance records for given consideration range **/

								$qryGetAttendance = $this->_db->select()->from(array('AI'=>$this->_ehrTables->attendanceImport),
											array('Attendance_Min_Date'=>new Zend_Db_Expr('MIN(Added_On)'),'Attendance_Id',
												'Attendance_Max_Date'=>new Zend_Db_Expr('MAX(Added_On)')))
											->where('Employee_Id = ?', $attendanceImportEmployeeId)
											->where('AI.Added_On >= ?', $workScheduleDetails['Consideration_From'])
											->where('AI.Added_On <= ?', $workScheduleDetails['Consideration_To']);		

									
								$getAttendance = $this->_db->fetchRow($qryGetAttendance);	
								
								if(!empty($getAttendance))
								{
									$getAttendanceImportId = $this->getAttendanceImportId($attendanceImportEmployeeId,$workScheduleDetails,NULL);
									$updateErrorCode = $this->updateAttendanceImportErrorCode($employeeId,$workScheduleDetails,$getAttendanceImportId,$logEmpId);
							
									if(!empty($updateErrorCode))
									{
										$returnVal += $updateErrorCode;
									}
									/* first in and last out attendance date and time are same need to update that record as invalid with proper error code*/
									elseif($getAttendance['Attendance_Min_Date'] == $getAttendance['Attendance_Max_Date'])
									{
										$whereAttendanceImport['Attendance_Id IN (?)'] = $getAttendance['Attendance_Id'];
										$this->_db->update($this->_ehrTables->attendanceImport, array('Rollup_Flag'=>-1,'Error_Code'=>'EAI001','Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$logEmpId), $whereAttendanceImport);
										$returnVal += 1 ;
									}
									else 
									{
										$attendanceActualDate     = date('Y-m-d',strtotime($workScheduleDetails['Regular_From']));
										
										$checkShiftExist =  $this->checkShiftEnabled($employeeId); 
										if($checkShiftExist=='Shift Roster')
										{
											$businessWorkingDays = $this->_dbPayslip->getBusinessWorkingDays($attendanceActualDate, $attendanceActualDate, $employeeId,NULL,1,'leaves');
										}
										else 
										{
											$businessWorkingDays = $this->_dbPayslip->getBusinessWorkingDays($attendanceActualDate, $attendanceActualDate, $employeeId,'',1);
										}

										if($businessWorkingDays > 0)
										{
											/* get the attendance status based employee type configuration*/
											$approvalStatus=$this->getProcessedBiometricAttendanceRecordStatus($employeeId);
										}
										else
										{
											$approvalStatus = "Applied";
										}
										$workPlace = $this->getCheckInCheckOutWorkPlace($employeeId,$attendanceImportEmployeeId,$getAttendance['Attendance_Min_Date'],$getAttendance['Attendance_Max_Date']);

										$checkInAttendanceImportkey     = $employeeId.'|'.$getAttendance['Attendance_Min_Date'];
										if(isset($attendanceImportDetailsEmployeeIdDateTime[$checkInAttendanceImportkey]))
										{
											$checkInAttendanceImportDetails = $attendanceImportDetailsEmployeeIdDateTime[$checkInAttendanceImportkey];
										}
										else
										{
											$checkInAttendanceImportDetails['Data_Source'] = 'Biometric';
											$checkInAttendanceImportDetails['Form_Source'] = 'Biometric';
										}
										
										$checkOutAttendanceImportkey = $employeeId.'|'.$getAttendance['Attendance_Max_Date'];
										if(isset($attendanceImportDetailsEmployeeIdDateTime[$checkOutAttendanceImportkey]))
										{
											$checkOutAttendanceImportDetails = $attendanceImportDetailsEmployeeIdDateTime[$checkOutAttendanceImportkey];
										}
										else
										{
											$checkOutAttendanceImportDetails['Data_Source'] = 'Biometric';
											$checkOutAttendanceImportDetails['Form_Source'] = 'Biometric';
										}
								
										$attendanceArray = array('Attendance_Id'       	=> '',
																'Employee_Id'         	=> $employeeId,
																'Approver_Id'         	=> $approverId,
																'Approval_Status'     	=> $approvalStatus,
																'Attendance_Date'     	=> $attendanceActualDate,
																'PunchIn_Date'        	=> date('Y-m-d',strtotime($getAttendance['Attendance_Min_Date'])),
																'Checkin_Work_Place_Id' => $workPlace['Checkin_Work_Place_Id'],
																'PunchOut_Date'       	=> date('Y-m-d',strtotime($getAttendance['Attendance_Max_Date'])),
																'PunchIn_Time'        	=>date('H:i:s',strtotime($getAttendance['Attendance_Min_Date'])) ,
																'PunchOut_Time'       	=>date('H:i:s',strtotime($getAttendance['Attendance_Max_Date'])) ,
																'Checkout_Work_Place_Id'=> $workPlace['Checkout_Work_Place_Id'],
																'Exclude_Break_Hours' 	=> 1,
																'Late_Attendance'     	=> 0,
																'Checkin_Data_Source'	=> isset($checkInAttendanceImportDetails['Data_Source']) ? $checkInAttendanceImportDetails['Data_Source'] : '',
																'Checkout_Data_Source'  => isset($checkOutAttendanceImportDetails['Data_Source']) ? $checkOutAttendanceImportDetails['Data_Source'] : '',
																'Checkin_Form_Source'	=> isset($checkInAttendanceImportDetails['Form_Source']) ? $checkInAttendanceImportDetails['Form_Source'] : '',
																'Checkout_Form_Source'  => isset($checkOutAttendanceImportDetails['Form_Source']) ? $checkOutAttendanceImportDetails['Form_Source'] : '',

																'Checkin_Latitude'      => isset($checkInAttendanceImportDetails['Import_Latitude']) ? $checkInAttendanceImportDetails['Import_Latitude'] : '',
																'Checkin_Longitude'     => isset($checkInAttendanceImportDetails['Import_Longitude']) ? $checkInAttendanceImportDetails['Import_Longitude'] : '',
																'Checkin_Address'       => isset($checkInAttendanceImportDetails['Import_Address']) ? $checkInAttendanceImportDetails['Import_Address'] : '',
																'Checkout_Latitude'     => isset($checkOutAttendanceImportDetails['Import_Latitude']) ? $checkOutAttendanceImportDetails['Import_Latitude'] : '',
																'Checkout_Longitude'    => isset($checkOutAttendanceImportDetails['Import_Longitude']) ? $checkOutAttendanceImportDetails['Import_Longitude'] : '',
																'Checkout_Address'      => isset($checkOutAttendanceImportDetails['Import_Address']) ? $checkOutAttendanceImportDetails['Import_Address'] : '',

																'Actual_Punch_In_Time'=>'',
																'Actual_Total_Hours' => 0,
																'Actual_PunchOut_Time' => '',
																'Total_Hours'=>0,
																'Added_Date'=>date('Y-m-d H:i:s'),
																'Added_By'=>$logEmpId);
									
										$validAttendanceData = $this->getValidPunchOut($attendanceArray['Employee_Id'], $attendanceArray['PunchOut_Date'], $attendanceArray['PunchOut_Time'] , $attendanceArray['PunchIn_Date'], $attendanceArray['PunchIn_Time']);											
										/* if the attendance record fall in regular schedule value below functionality will run else error code will be updated */
										if($validAttendanceData==0)
										{
											/* based on workschedule configuration check out time buffer calculation will happen */
											if($workScheduleDetails['Check_Out_Time_Buffer'] > 0)
											{
												$getActualPunchOut=$this->_dbWorkSchedule->getActualPunchOutDetails($attendanceArray,$workScheduleDetails);
												$attendanceArray['Actual_PunchOut_Time'] = $getActualPunchOut['Actual_PunchOut_Time'];
												$attendanceArray['PunchOut_Time'] = $getActualPunchOut['PunchOut_Time'];
											}
											else
											{
												$attendanceArray['Actual_PunchOut_Time'] = date('H:i:s',strtotime($attendanceArray['PunchOut_Time']));
												$attendanceArray['PunchOut_Time'] = date('H:i:s',strtotime($attendanceArray['PunchOut_Time']));
											}
											/* based on workschedule configuration grace time calculation will happen */
											if($workScheduleDetails['Grace_Time_Flag']==1)
											{
												$getActualPunchInDetails=$this->_dbWorkSchedule->getActualPunchInDetails($attendanceArray,$workScheduleDetails);
												$attendanceArray['Actual_Punch_In_Time']=$getActualPunchInDetails['Actual_Punch_In_Time'];
												$attendanceArray['PunchIn_Time']=$getActualPunchInDetails['PunchIn_Time'];
												$attendanceArray['Actual_Total_Hours']=$getActualPunchInDetails['Actual_Total_Hours'];
											}
											else 
											{
												$attendanceArray['Actual_Punch_In_Time'] = date('H:i:s',strtotime($getAttendance['Attendance_Min_Date']));
												$attendanceArray['PunchIn_Time'] = date('H:i:s',strtotime($getAttendance['Attendance_Min_Date']));
												$attendanceArray['Actual_Total_Hours']= $this->_dbWorkSchedule->getActualTotalHours($attendanceArray);
											}

											$attendanceArray['Total_Hours'] = $this->getTotalHours($attendanceArray);
											
											if($attendanceArray['Total_Hours'] >= 0 || empty($attendanceArray['Total_Hours']))
											{
												/** To check whether attendance is added for this consideration range need to delete that attendance record **/
												$qryAttendanceId = $this->_db->select()->from($this->_ehrTables->attendance,array('Attendance_Id'))
																							->where('Employee_Id = ?', $employeeId)
																							->where(new Zend_Db_Expr('Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T")').' >= ?', $workScheduleDetails['Consideration_From'])
																							->where(new Zend_Db_Expr('Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T")').' <= ?', $workScheduleDetails['Consideration_To']);
												$getAttendanceId = $this->_db->fetchCol($qryAttendanceId);
												if(!empty($getAttendanceId))
												{
													$attendanceSummarySelectedDetails = [];
													foreach($getAttendanceId as $attendanceId)
													{
														$deleteAttendance = $this->deleteAttendance($attendanceId,$logEmpId,$formName,$customFormName);
														if($deleteAttendance['success']){
															$attendanceSummarySelectedDetails[] = $deleteAttendance['attendanceDetails'];
														}
													}
													$this->_dbCommonFun->triggerAttendanceSummaryStepFunction($attendanceSummarySelectedDetails,'attendance');
												}

												$lateAttendanceDetails = $this->checkLateAttendance($attendanceArray['Employee_Id'], $attendanceArray['PunchIn_Date'],$attendanceArray['PunchIn_Time'],$shortTimeOffConfiguration);
												$attendanceArray['Late_Attendance'] 	  = $lateAttendanceDetails['Late_Attendance'];
												$attendanceArray['Late_Attendance_Hours'] = $lateAttendanceDetails['Late_Attendance_Hours'];
												$attendanceArray['Late_Attendance_Hours_From_Grace'] 	  = $lateAttendanceDetails['Late_Attendance_Hours_From_Grace'];
												$attendanceArray['Always_Grace'] = $lateAttendanceDetails['Always_Grace'];
												$attendanceArray['Auto_Short_Time_Off'] = $lateAttendanceDetails['Auto_Short_Time_Off'];

												$attendanceDayIsWorkingDay = $lateAttendanceDetails['Business_Working_Day'];


												$attendanceExist = $this->getAttendanceExist($attendanceArray);
												if($attendanceExist == 0)
												{
													$inserted = $this->_db->insert($this->_ehrTables->attendance, $attendanceArray);
												}
												else
												{
													$inserted =0;
													if(!empty($getAttendanceImportId))
													{
														$whereAttendanceImport['Attendance_Id IN (?)'] = $getAttendanceImportId;
														$this->_db->update($this->_ehrTables->attendanceImport, array('Rollup_Flag'=>3,'Error_Code'=>'','Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$logEmpId), $whereAttendanceImport);
														$returnVal += 1 ;
													}
												}
											}

											/* while adding the attendance record  need to update the comp off balance as well*/
											if($inserted)
											{
												$allAttendanceDetails[] = $attendanceArray;
												$attendanceId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendance, array(new Zend_Db_Expr('Max(Attendance_Id)'))));	
												$compOffBalanceArray = array(  	'Approval_Status' => $attendanceArray['Approval_Status'],
																				'Parent_Id'        => $attendanceId);
												$this->updateCompOffBalance($compOffBalanceArray);
											}
											
											/* based on the consideration range roll up flag updated as processed*/
											/*when the attendance import record updated as processed we need to update the error code as well*/
											if(!empty($getAttendanceImportId) && !empty($inserted))
											{ 
												$whereAttendanceImport['Attendance_Id IN (?)'] = $getAttendanceImportId;
												$this->_db->update($this->_ehrTables->attendanceImport, array('Rollup_Flag'=>1,'Error_Code'=>'','Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$logEmpId), $whereAttendanceImport);
												$returnVal += 1 ;
											}
										
											/*when the late attendance is deducted leave will be applied automatically*/
										
											if($attendanceArray['Late_Attendance'] == 2 || $attendanceArray['Late_Attendance'] == 4)
											{
												$this->applyLateAttendanceLeave($attendanceArray, $logEmpId,$attendanceActualDate, $attendanceDayIsWorkingDay);
											}

										}
										else 
										{
											$qryGetAttendance = $this->_db->select()->from(array('AI'=>$this->_ehrTables->attendanceImport),
																							array('Attendance_Id'))
																							->where('Employee_Id = ?', $attendanceImportEmployeeId)
																							->where('AI.Added_On >= ?', $workScheduleDetails['Consideration_From'])
																							->where('AI.Added_On <= ?', $workScheduleDetails['Consideration_To']);		
																							
											$getAttendanceId = $this->_db->fetchCol($qryGetAttendance);	
							
											if(!empty($getAttendanceId))
											{
													$whereAttendanceImport['Attendance_Id IN (?)'] = $getAttendanceId;
													$this->_db->update($this->_ehrTables->attendanceImport, array('Rollup_Flag'=>-1,'Error_Code'=>'EAI005','Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$logEmpId), $whereAttendanceImport);
													$returnVal += 1 ;
											}
										}						
									}
								}
							}
							else 
							{
								$qryAttendanceImportId = $this->_db->select()->from(array('AI'=>$this->_ehrTables->attendanceImport),array('Attendance_Id','Added_On'))
																									->where('Employee_Id = ?', $attendanceImportEmployeeId)
																									->where('Date(AI.Added_On) = ?', $startDate);
								
								$getAttendanceImportId = $this->_db->fetchAll($qryAttendanceImportId);
								
								$checkShiftEnabled=$this->checkShiftEnabled($employeeId);
								
								foreach ($getAttendanceImportId as $key => $attendanceImportDetails) 
								{
									$graceTimeDetails=$this->getCurrentWorkScheduleDetails($employeeId,$attendanceImportDetails['Added_On']);
									
									if(empty($graceTimeDetails))
									{
										$whereAttendanceImport['Attendance_Id IN (?)'] = $attendanceImportDetails['Attendance_Id'];
										if($checkShiftEnabled=='Shift Roster')
										{
											$this->_db->update($this->_ehrTables->attendanceImport, array('Rollup_Flag'=>-1,'Error_Code'=>'EAI003','Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$logEmpId), $whereAttendanceImport);
											$returnVal += 1 ;
										}
										else 
										{
											$this->_db->update($this->_ehrTables->attendanceImport, array('Rollup_Flag'=>-1,'Error_Code'=>'EAI002','Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$logEmpId), $whereAttendanceImport);
											$returnVal += 1 ;
										}
										
									}
									
								}
							} 
						}
					}
		
					$current = strtotime($step, $current); /**incrementor in while loop**/
				}
				$this->_dbCommonFun->triggerAttendanceSummaryStepFunction($allAttendanceDetails,'attendance');
			}
			else 
			{
				/* when there is no attendance record to process we need to present this message*/
				$returnVal = -1;
			}


			if($returnVal > 0)
			{
				return array('success' => true, 'msg'=>'Attendance records are processed partially.For records that were not processed please review the errors in the attendance import grid', 'type'=>'info');
			}
			else if($returnVal == 0)
			{
				return array('success' => false, 'msg'=>'Attendance processed successfully', 'type'=>'info');
			}
			else if($returnVal == -1)
			{
				return array('success' => false, 'msg'=>'Attendance already processed', 'type'=>'info');
			}
			else 
			{
                $this->view->result = array('success' => false, 'msg'=>"Sorry! Something went wrong. Please contact system admin", 'type'=>'warning');
			}
		}
		else
		{
			$getExternalEmpId   = $this->getValidCheckInCheckOutEmployeeId($attendanceDateFrom,$attendanceDateTo,$logEmpId,$employeeIds);
			$getAttImportStatus = $this->attendanceStatusPairs();
			if(!empty($getExternalEmpId) && !empty($getAttImportStatus))
			{
				$empAttendanceData  = $empAttendanceUpdateData = array();
				$importattData = array();
				$validAttendanceId = array();
				$invalidAttendanceId = array();
				$existAttendanceId = array();
				if(!empty($getExternalEmpId))
				{
					foreach($getExternalEmpId as $validEmpId)
					{
						$getvalidAttendanceData = $this->_db->fetchAll($this->_db->select()
																	->from(array('I'=>$this->_ehrTables->attendanceImport),
																			array('Attendance_Id', 'Attendance_Status',
																					'Exclude_Break_Hours','Data_Source', 'I.Added_On',
																					'Location_Id', 'Verify_Code','Schema_Id',
																					'Punch_Date'=>new Zend_Db_Expr('Date(I.Added_On)'),
																					'Punch_Time'=>new Zend_Db_Expr('Time_Format(I.Added_On, "%H:%i:%s")')))
																	
																	->joinInner(array('J'=>$this->_ehrTables->empJob),
																				'J.External_EmpId = I.Employee_Id',
																				array('J.Employee_Id'))
																	
																	->joinInner(array('D'=>$this->_ehrTables->designation),
																				'J.Designation_Id = D.Designation_Id', array())
																	
																	->joinInner(array('G'=>$this->_ehrTables->empGrade),
																				'G.Grade_Id = D.Grade_Id',
																				array('Grade','Grade_Id'))
																	
																	->where('I.Employee_Id = (?)', $validEmpId)
																	->where('Rollup_Flag != 1')
																	->where('Date(I.Added_On) >= ?', $attendanceDateFrom)
																	->where('Date(I.Added_On) <= ?', $attendanceDateTo)
																	->order('I.Added_On ASC'));

						$eligibleEmployeeId = array_unique(array_column($getvalidAttendanceData,'Employee_Id'));
						if(!empty($eligibleEmployeeId))
						{
							$shortTimeOffConfiguration 	             = $this->listShortLeaveRequestSettings($eligibleEmployeeId,$attendanceDateFrom,$attendanceDateTo);
							$adminIds             					 = $this->_dbCommonFun->listAdmins();
							$superAdminApproverId 					 = $adminIds[0];
							$shortTimeOffConfiguration['Admin_Id']   = $superAdminApproverId;
							$shortTimeOffConfiguration['Session_Id'] = $logEmpId;
						}
						else
						{
							$shortTimeOffConfiguration = array();
						}																	
						$importattData = $this->readAttendanceImport($getvalidAttendanceData, $logEmpId);
						if(count($importattData['add'])>0)
						{
							foreach($importattData['add'] as $empAttData)
							{
								if(!in_array($empAttData, $empAttendanceData))
									$empAttendanceData[] = $empAttData;
							}
						}
						if(count($importattData['update'])>0)	
						{
							foreach($importattData['update'] as $empAttData)
							{
								if(!in_array($empAttData, $empAttendanceUpdateData))
									$empAttendanceUpdateData[] = $empAttData;
							}
						}
						if(count($importattData['updateValid'])>0)
						{
							foreach($importattData['updateValid'] as $validAttId)
							{
								$validAttendanceId[] = $validAttId;
							}
						}
						if(count($importattData['updateInvalid'])>0)
						{
							foreach($importattData['updateInvalid'] as $invalidAttId)
								$invalidAttendanceId[] = $invalidAttId;
						}
						if(count($importattData['exist'])>0)
						{
							foreach($importattData['exist'] as $existAttId)
								$existAttendanceId[] = $existAttId;
						}
					}
				}
				$invalidAttendanceId = array_unique(array_diff($invalidAttendanceId, array_intersect($invalidAttendanceId, $validAttendanceId)));
				$validAttendanceId = array_unique($validAttendanceId);
				$existAttendanceId = array_unique($existAttendanceId);
				$returnVal = 0;
				if(!empty($empAttendanceData))
				{

					$inserted=0;
					// check if shift roster is enabled.if enabled, insert only the record for which shift is assigned else insert all the valid records 
					foreach($empAttendanceData as $val)
					{ 
						if(!empty($val['Employee_Id']))
						{
						$checkShiftEnabled=$this->checkShiftEnabled($val['Employee_Id']);

						if(!empty($val['PunchIn_Date'] ))
						{
							$punchDate = $val['PunchIn_Date'];
						}
						else
						{
							$punchDate = $val['PunchOut_Date'];
						}
						$CheckEmpShiftExist=$this->CheckEmpShiftExist($val['Employee_Id'],$punchDate);
						
						if($checkShiftEnabled=='Shift Roster' && !empty($CheckEmpShiftExist) || $checkShiftEnabled == 'Employee Level')
						{ 
							$attendanceImportEmployeeId = $this->getExternalEmployeeId($val['Employee_Id']);
						
							if(!empty($val['PunchIn_Date']) && !empty($val['PunchIn_Time']))
							{
								$attendancePunchInDate  = date('Y-m-d H:i:s',strtotime($val['PunchIn_Date'] .' '. $val['PunchIn_Time']));
							}
							else 
							{
								$attendancePunchInDate  = '';
							}

							if(!empty($val['PunchOut_Date']) && !empty($val['PunchOut_Time']))
							{
								$attendancePunchOutDate = date('Y-m-d H:i:s',strtotime($val['PunchOut_Date'] .' '. $val['PunchOut_Time']));
							}
							else 
							{
								$attendancePunchOutDate  = '';
							}
							
							$workPlace = $this->getCheckInCheckOutWorkPlace($val['Employee_Id'],$attendanceImportEmployeeId,$attendancePunchInDate,$attendancePunchOutDate);

							if (!empty($val['PunchOut_Time']))
							{
								//change punch out time based on work schedule check out time buffer
								$checkOutTimeBufferDetails=$this->getGraceTimeDetails($val['Employee_Id'],$punchDate);
								if($checkOutTimeBufferDetails['Check_Out_Time_Buffer']>0)
								{
									$getActualPunchOut=$this->_dbWorkSchedule->getActualPunchOutDetails($val,$checkOutTimeBufferDetails);
									$val['Actual_PunchOut_Time'] = $getActualPunchOut['Actual_PunchOut_Time'];
									$val['PunchOut_Time'] = $getActualPunchOut['PunchOut_Time'];
								}
								else
								{
									$val['Actual_PunchOut_Time'] = date('H:i:s',strtotime($val['PunchOut_Time']));
									$val['PunchOut_Time'] = date('H:i:s',strtotime($val['PunchOut_Time']));
								}
								$val['Checkout_Work_Place_Id'] = $workPlace['Checkout_Work_Place_Id'];
							}
							
							//Check in grace time details
							$graceTimeExist=$this->checkGraceTimeExist($val['Employee_Id'],$val['PunchIn_Date']);
							if($graceTimeExist==1)
							{
								if(!empty($val['PunchIn_Date']))
								{
									$graceTimeDetails=$this->getGraceTimeDetails($val['Employee_Id'],$val['PunchIn_Date']);
									$getActualPunchInDetails=$this->_dbWorkSchedule->getActualPunchInDetails($val,$graceTimeDetails);
									$val['Actual_Punch_In_Time']=$getActualPunchInDetails['Actual_Punch_In_Time'];
									$val['PunchIn_Time']=$getActualPunchInDetails['PunchIn_Time'];
									$val['Actual_Total_Hours']=$getActualPunchInDetails['Actual_Total_Hours'];
									$val['Total_Hours']=$this->getTotalHours($val);
									$val['Checkin_Work_Place_Id'] = $workPlace['Checkin_Work_Place_Id'];
								}
							}
							else
							{
								if(!empty($val['PunchIn_Date']))
								{ 
									$val['Actual_Punch_In_Time']=$val['PunchIn_Time'];
									$val['Actual_Total_Hours']=$this->_dbWorkSchedule->getActualTotalHours($val);
									$val['Total_Hours']=$this->getTotalHours($val);
									$val['Checkin_Work_Place_Id'] = $workPlace['Checkin_Work_Place_Id'];
								}
							}

							if($val['Approval_Status']=='Approved')
							{
								$checkShiftExist = $this->checkShiftEnabled($val['Employee_Id']); 
								if($checkShiftExist=='Shift Roster')
								{
									$businessWorkingDays = $this->_dbPayslip->getBusinessWorkingDays($val['Attendance_Date'], $val['Attendance_Date'], $val['Employee_Id'],NULL,1,'leaves');
								}
								else 
								{
									$businessWorkingDays = $this->_dbPayslip->getBusinessWorkingDays($val['Attendance_Date'], $val['Attendance_Date'], $val['Employee_Id'],'',1);
								}
								if($businessWorkingDays > 0)
								{
									$val['Approval_Status']	=  $this->getProcessedBiometricAttendanceRecordStatus($val['Employee_Id']);
								}
								else
								{
									$val['Approval_Status'] = "Applied";
								}
								
							} 

							if($val['Total_Hours'] >= 0){
								$val['Checkin_Data_Source']='Biometric';
								if($val['Approval_Status']!='Draft')
								{
									$val['Checkout_Data_Source']='Biometric';
								}
								/** To check whether attendance is added for this consideration range need to delete that attendance record **/
								$qryAttendanceId = $this->_db->select()->from($this->_ehrTables->attendance,array('Attendance_Id'))
								->where('Employee_Id = ?', $val['Employee_Id'])
								->where('PunchIn_Date = ?', $val['PunchIn_Date'])
								->where('PunchIn_Time = ?', $val['PunchIn_Time']);
								
								$getAttendanceId = $this->_db->fetchCol($qryAttendanceId);

								if(!empty($getAttendanceId))
								{
									foreach($getAttendanceId as $attendanceId)
									{
										$this->deleteAttendance($attendanceId,$logEmpId,$formName,$customFormName);
									}
								}
								
								if($val['PunchIn_Date'] != null || $val['PunchIn_Time'] != null)
								{
									$lateAttendanceDetails = $this->checkLateAttendance($val['Employee_Id']
																,$val['PunchIn_Date'],$val['PunchIn_Time'],$shortTimeOffConfiguration);

									$val['Late_Attendance'] 	  = $lateAttendanceDetails['Late_Attendance'];
									$val['Late_Attendance_Hours'] = $lateAttendanceDetails['Late_Attendance_Hours'];
									$val['Late_Attendance_Hours_From_Grace'] 	  = $lateAttendanceDetails['Late_Attendance_Hours_From_Grace'];
									$val['Always_Grace'] = $lateAttendanceDetails['Always_Grace'];
									$val['Auto_Short_Time_Off'] = $lateAttendanceDetails['Auto_Short_Time_Off'];
									$attendanceDayIsWorkingDay = $lateAttendanceDetails['Business_Working_Day'];
								}
								else
								{
									$val['Late_Attendance'] = 0;
									$val['Late_Attendance_Hours'] = new Zend_Db_Expr('NULL');
									$val['Late_Attendance_Hours_From_Grace'] = new Zend_Db_Expr('NULL');
									$val['Always_Grace'] = new Zend_Db_Expr('NULL');
									$val['Auto_Short_Time_Off'] = 'No';

								}
								
								$attendanceExist = $this->ckAttendanceExist($val['Employee_Id'], strtotime($attendancePunchInDate),strtotime($attendancePunchOutDate));
							
								if($attendanceExist == 0)
								{
									$inserted = $this->_db->insert($this->_ehrTables->attendance, $val);
								}
								else
								{
									$inserted = 0;
								}
							}
							
							if($inserted)
							{
								$allAttendanceDetails[] = $val;
								if($val['Late_Attendance'] == 2 || $val['Late_Attendance'] == 4)
								{
									$empWorkScheduleDetails=$this->getGraceTimeDetails($val['Employee_Id'],$val['PunchIn_Date']);
									$leaveDate = date('Y-m-d',strtotime($empWorkScheduleDetails['Regular_From']));
									$this->applyLateAttendanceLeave($val, $logEmpId,$leaveDate, $attendanceDayIsWorkingDay);
								}
								$attendanceId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendance, array(new Zend_Db_Expr('Max(Attendance_Id)'))));	
								$compOffBalanceArray = array(  	'Approval_Status' => $val['Approval_Status'],
																'Parent_Id'        => $attendanceId);
								$this->updateCompOffBalance($compOffBalanceArray);
							} 
						}
						else
						{
							$returnVal='-2';
						}
						}
					}
				
					if($inserted)
					{    
						if(!empty($validAttendanceId))
						{ 		$validUpdatedarr=array();
							foreach($validAttendanceId as $validAttId)
							{ 
									$getvalidAttData = $this->_db->select() ->from(array('I'=>$this->_ehrTables->attendanceImport),
																						array('Attendance_Id','Attendance_Status',
																								'Punch_Date'=>new Zend_Db_Expr('Date(I.Added_On)')))
																				->joinInner(array('J'=>$this->_ehrTables->empJob), 'I.Employee_Id=J.External_EmpId',array('Employee_Id'))				
																				->where('I.Attendance_Id = (?)', $validAttId)
																				->where('Rollup_Flag != 1');
																				

													$getvalidAttendanceData=$this->_db->fetchRow($getvalidAttData );								
									if(!empty($getvalidAttendanceData['Employee_Id']))
									{
									$checkShiftEnabled=$this->checkShiftEnabled($getvalidAttendanceData['Employee_Id']);
									$CheckEmpShiftExist=$this->CheckEmpShiftExist($getvalidAttendanceData['Employee_Id'],$getvalidAttendanceData['Punch_Date']);
									// get the employee id for which shift is not assigned in an array
									if($checkShiftEnabled =='Shift Roster' && empty($CheckEmpShiftExist))
									{
										array_push($validUpdatedarr,$getvalidAttendanceData['Attendance_Id']);
									}
									}								  
								}
								// get the employee id for which shift is assigned 
							$validAttendance=array_diff($validAttendanceId,$validUpdatedarr);
						if(!empty($validAttendance))
						{
							$validUpdated = $this->_db->update($this->_ehrTables->attendanceImport, array('Rollup_Flag'=> 1,'Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$logEmpId),
															array('Attendance_Id IN (?)'=>$validAttendance));
						
							if($validUpdated)
							{
								$returnVal += 1;
							}
						}
						}
						$returnVal += 1;
						
					}
					$this->_dbCommonFun->triggerAttendanceSummaryStepFunction($allAttendanceDetails,'attendance');
				}
				
				if(!empty($empAttendanceUpdateData))
				{
					foreach($empAttendanceUpdateData as $key => $empAttendanceDetails)
					{
						$lateAttendanceDetails = $this->checkLateAttendance($empAttendanceDetails['Employee_Id'], $empAttendanceDetails['PunchIn_Date'],
																								$empAttendanceDetails['PunchIn_Time'],$shortTimeOffConfiguration);

						$empAttendanceUpdateData[$key]['Late_Attendance'] 	    = $lateAttendanceDetails['Late_Attendance'];
						$empAttendanceUpdateData[$key]['Late_Attendance_Hours'] = $lateAttendanceDetails['Late_Attendance_Hours'];
						$empAttendanceUpdateData[$key]['Late_Attendance_Hours_From_Grace'] = $lateAttendanceDetails['Late_Attendance_Hours_From_Grace'];
						$empAttendanceUpdateData[$key]['Always_Grace'] = $lateAttendanceDetails['Always_Grace'];
						$empAttendanceUpdateData[$key]['Auto_Short_Time_Off'] = $lateAttendanceDetails['Auto_Short_Time_Off'];
						$attendanceDayIsWorkingDay = $lateAttendanceDetails['Business_Working_Day'];
					}
					
					foreach($empAttendanceUpdateData as $attendanceArray)
					{
						$attCheckInData= $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->attendance,array('PunchIn_Time','PunchOut_Time','Actual_Punch_In_Time','Approval_Status','Actual_Total_Hours','Actual_PunchOut_Time'))
																	->where('Attendance_Id = ?', $attendanceArray['Attendance_Id']));
						$currentPunchInTime=date('H:i:s',strtotime($attendanceArray['PunchIn_Time']));
						
							//Check out details
						if(empty($attendanceArray['PunchOut_Time']))
						{
							$attendanceArray['PunchOut_Time'] = "";
							$attendanceArray['Actual_PunchOut_Time'] = "";
							$attendanceArray['Approval_Status'] = "Draft";
						}
						else
						{
							if (!empty($attendanceArray['PunchOut_Time']))
							{
								$attendanceImportEmployeeId = $this->getExternalEmployeeId($attendanceArray['Employee_Id']);
							
								$attendancePunchInDate  = '';
								if(!empty($attendanceArray['PunchOut_Date']) && !empty($attendanceArray['PunchOut_Time']))
								{
									$attendancePunchOutDate = date('Y-m-d H:i:s',strtotime($attendanceArray['PunchOut_Date'] .' '. $attendanceArray['PunchOut_Time']));
								}
								else 
								{
									$attendancePunchOutDate  = '';
								}
								
								$workPlace = $this->getCheckInCheckOutWorkPlace($attendanceArray['Employee_Id'],$attendanceImportEmployeeId,$attendancePunchInDate,$attendancePunchOutDate);
								//change punch out time based on work schedule check out time buffer
								$getCheckOutTime=$this->getGraceTimeDetails($attendanceArray['Employee_Id'],$attendanceArray['PunchIn_Date']);
								if($getCheckOutTime['Check_Out_Time_Buffer'] > 0)
								{
									$getActualPunchOut=$this->_dbWorkSchedule->getActualPunchOutDetails($attendanceArray,$getCheckOutTime);
									$attendanceArray['Actual_PunchOut_Time'] = $getActualPunchOut['Actual_PunchOut_Time'];
									$attendanceArray['PunchOut_Time'] = $getActualPunchOut['PunchOut_Time'];
								}
								else
								{
									$attendanceArray['Actual_PunchOut_Time'] = date('H:i:s',strtotime($attendanceArray['PunchOut_Time']));
									$attendanceArray['PunchOut_Time'] = date('H:i:s',strtotime($attendanceArray['PunchOut_Time']));
								}
								$attendanceArray['Checkout_Work_Place_Id'] = $workPlace['Checkout_Work_Place_Id'];
								$checkShiftExist =  $this->checkShiftEnabled($attendanceArray['Employee_Id']); 
								if($checkShiftExist=='Shift Roster')
								{
									$businessWorkingDays = $this->_dbPayslip->getBusinessWorkingDays($attendanceArray['Attendance_Date'], $attendanceArray['Attendance_Date'], $attendanceArray['Employee_Id'],NULL,1,'leaves');
								}
								else 
								{
									$businessWorkingDays = $this->_dbPayslip->getBusinessWorkingDays($attendanceArray['Attendance_Date'], $attendanceArray['Attendance_Date'], $attendanceArray['Employee_Id'],'',1);
								}

								if($businessWorkingDays > 0)
								{
									$attendanceArray['Approval_Status']=$this->getProcessedBiometricAttendanceRecordStatus($attendanceArray['Employee_Id']);
								}
								else
								{
									$attendanceArray['Approval_Status'] = "Applied";
								}
								
							}
						}
					
							if($attCheckInData['Approval_Status']=='Draft')
							{
								if($attCheckInData['PunchIn_Time'] == $currentPunchInTime)
								{
									$attendanceArray['PunchIn_Time']         = $attendanceArray['PunchIn_Time'];
									$attendanceArray['Actual_Punch_In_Time'] = $attCheckInData['Actual_Punch_In_Time'];
									$attendanceArray['Actual_Total_Hours']   = $this->_dbWorkSchedule->getActualTotalHours($attendanceArray);
								}
								else
								{
									if(!empty($attendanceArray['PunchIn_Date']))
									{
										$graceTimeDetails=$this->getGraceTimeDetails($attendanceArray['Employee_Id'],$attendanceArray['PunchIn_Date']);
										$getActualPunchInDetails=$this->_dbWorkSchedule->getActualPunchInDetails($attendanceArray,$graceTimeDetails);
										$attendanceArray['Actual_Punch_In_Time']=$getActualPunchInDetails['Actual_Punch_In_Time'];
										$attendanceArray['PunchIn_Time']=$getActualPunchInDetails['PunchIn_Time'];
										$attendanceArray['Actual_Total_Hours']=$getActualPunchInDetails['Actual_Total_Hours'];
									}	
								}
							}
						if($attendanceArray['Total_Hours'] >= 0){
							$updated = $this->_db->update($this->_ehrTables->attendance,$attendanceArray, 'Attendance_Id = '. $attendanceArray['Attendance_Id']);	
						}
					}
					
					if($updated)
					{
						if(!empty($validAttendanceId))
						{    
							$validUpdated = $this->_db->update($this->_ehrTables->attendanceImport, array('Rollup_Flag'=> 1,'Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$logEmpId),
															array('Attendance_Id IN (?)'=>$validAttendanceId));
							if($validUpdated)
							{
								$returnVal += 1;
							}
						}
						$returnVal += 1;
					}
				}
				
				if(!empty($existAttendanceId))
				{
					$existUpdated = $this->_db->update($this->_ehrTables->attendanceImport, array('Rollup_Flag'=>3,'Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$logEmpId),
													array('Attendance_Id IN (?)'=>$existAttendanceId));
					if($existUpdated)
					{
						$returnVal += 1;
					}
				}
				
				if(!empty($invalidAttendanceId))
				{
					$invalidUpdated = $this->_db->update($this->_ehrTables->attendanceImport, array('Rollup_Flag'=>-1,'Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$logEmpId),
														array('Attendance_Id IN (?)'=>$invalidAttendanceId));
					
					if($invalidUpdated)
					{
						$returnVal += 1;
					}
				}
			}
			else
			{
				$returnVal = -1;
			}
	
			if($returnVal == -2)
			{
				return array('success' => true, 'msg'=>'Attendance cant be processed since shift has not been allocated', 'type'=>'info');
			}
			else if(!empty($validUpdatedarr))
			{
				return array('success' => true, 'msg'=>'Attendance processed successfully for employees who is allocated with shift', 'type'=>'info');
			}
			else if($returnVal > 0 && empty($validUpdatedarr))
			{
				return array('success' => true, 'msg'=>'Attendance processed successfully', 'type'=>'info');
			}
			else if($returnVal == 0)
			{
				return array('success' => false, 'msg'=>'Attendance already processed', 'type'=>'info');
			}
			else if($returnVal == -1)
			{
				return array('success' => false, 'msg'=>'No biometric integration id or attendance status pairs found', 'type'=>'warning');
			}
			else 
			{
                $this->view->result = array('success' => false, 'msg'=>"Sorry! Something went wrong. Please contact system admin", 'type'=>'warning');
			}
		}
	}

	// get the employee id based on attendancefrom and attendance to range
	public function getAttendanceImportDetails($attendanceDateFrom,$attendanceDateTo,$logEmpId,$employeeIds = null)
	{
		$attendanceProcessDetails = array();
		$qryAttendanceProcessData = $this->_db->select()->from(array('AI'=>$this->_ehrTables->attendanceImport),array('AI.*'))
												->joinInner(array('EJ'=>$this->_ehrTables->empJob), 'AI.Employee_Id=EJ.External_EmpId',array('EJ.Employee_Id as Actual_Employee_Id','EJ.External_EmpId'))
												->where('Rollup_Flag != 1')
												->where('Date(AI.Added_On) >= ?', $attendanceDateFrom)
												->where('Date(AI.Added_On) <= ?', $attendanceDateTo);

		$qryAttendanceProcessData->joinInner(array('EP'=>$this->_ehrTables->empPersonal), 'EJ.Employee_Id=EP.Employee_Id', array(''))
								->joinInner(array('D'=>$this->_ehrTables->designation),'EJ.Designation_Id = D.Designation_Id', array())
								->joinInner(array('H'=>$this->_ehrTables->timesheetHrs),'H.Grade_Id = D.Grade_Id', array())
								->where('EP.Form_Status = 1');

		// Add employee ID filtering for batch processing
		if(!empty($employeeIds) && is_array($employeeIds))
		{
			$qryAttendanceProcessData->where('EJ.Employee_Id IN (?)', $employeeIds);
		}

		$qryAttendanceProcessData = $this->_dbCommonFun->formServiceProviderQuery($qryAttendanceProcessData,'EJ.Service_Provider_Id',$logEmpId);

		$getAttendanceProcessData = $this->_db->fetchAll($qryAttendanceProcessData);

		// Separate arrays for date-based and date-time-based indexing
		$employeeIdAndDateBasedIndex  = array();
		$dateBasedIndex 			  = array();
		$employeeIdDateTimeBasedIndex = array();

		foreach ($getAttendanceProcessData as $attendanceProcess) {
			$addedOnDateTime = $attendanceProcess['Added_On'];
			$addedOnDate     = date('Y-m-d', strtotime($addedOnDateTime));
			$employeeId      = $attendanceProcess['Actual_Employee_Id'];

			$attendanceProcess['Employee_Id'] = $employeeId;
			// Index by date (without time)
			$employeeIdAndDate = $employeeId . '|' . $addedOnDate;
		
			if (!isset($employeeIdAndDateBasedIndex[$employeeIdAndDate])) {
				$employeeIdAndDateBasedIndex[$employeeIdAndDate] = array();
			}
			$employeeIdAndDateBasedIndex[$employeeIdAndDate][] = $attendanceProcess;
			
			if (!isset($dateBasedIndex[$addedOnDate])) {
				$dateBasedIndex[$addedOnDate] = array();
			}
			$dateBasedIndex[$addedOnDate][] = $attendanceProcess;

			// Create a unique key by concatenating Employee_Id and Added_On_DateTime
			$employeeIdAndDateTime = $employeeId . '|' . $addedOnDateTime;
			// Index by date-time with Employee_Id
			if (!isset($employeeIdDateTimeBasedIndex[$employeeIdAndDateTime])) {
				$employeeIdDateTimeBasedIndex[$employeeIdAndDateTime] = $attendanceProcess;
			} else {
				// If the key already exists, create an array to store multiple records
				if (!is_array($employeeIdDateTimeBasedIndex[$employeeIdAndDateTime])) {
					$employeeIdDateTimeBasedIndex[$employeeIdAndDateTime] = array($employeeIdDateTimeBasedIndex[$employeeIdAndDateTime]);
				}
				$employeeIdDateTimeBasedIndex[$employeeIdAndDateTime][] = $attendanceProcess;
			}
		}

		return array('employeeIdAndDateBasedIndex'=>$employeeIdAndDateBasedIndex,'dateBasedIndex'=>$dateBasedIndex,'employeeIdDateTimeBasedIndex'=>$employeeIdDateTimeBasedIndex,'organizeAttendanceImportDetails'=>$getAttendanceProcessData);
	}

	//get valid check in checkout EmployeeId/External Employee Id
	public function getValidCheckInCheckOutEmployeeId($attendanceDateFrom,$attendanceDateTo,$logEmpId,$employeeIds = null)
	{

		$qryAttendanceProcessData = $this->_db->select()->from(array('AI'=>$this->_ehrTables->attendanceImport),array(''))
												->joinInner(array('EJ'=>$this->_ehrTables->empJob), 'AI.Employee_Id=EJ.External_EmpId',array('EJ.External_EmpId'))
												->joinInner(array('EP'=>$this->_ehrTables->empPersonal), 'EJ.Employee_Id=EP.Employee_Id', array(''))
												->joinInner(array('D'=>$this->_ehrTables->designation),'EJ.Designation_Id = D.Designation_Id', array())
												->joinInner(array('H'=>$this->_ehrTables->timesheetHrs),'H.Grade_Id = D.Grade_Id', array())
												->where('EP.Form_Status = 1')
												->where('Rollup_Flag != 1')
												->where('Date(AI.Added_On) >= ?', $attendanceDateFrom)
												->where('Date(AI.Added_On) <= ?', $attendanceDateTo)
												->group('AI.Employee_Id');

		// Add employee ID filtering
		if(!empty($employeeIds) && is_array($employeeIds))
		{
			$qryAttendanceProcessData->where('EJ.Employee_Id IN (?)', $employeeIds);
		}

		$qryAttendanceProcessData = $this->_dbCommonFun->formServiceProviderQuery($qryAttendanceProcessData,'EJ.Service_Provider_Id',$logEmpId);

		$getAttendanceProcessData = $this->_db->fetchCol($qryAttendanceProcessData);

		return $getAttendanceProcessData;
	}

	/*This function help us to update the attendance import error codes*/
	public function updateAttendanceImportErrorCode($employeeId,$workScheduleDetails,$getAttendanceImportId,$sessionId)
	{
		$updateAttendanceImportErrorCode = 1;
		$hrReports 			  = new Reports_Model_DbTable_HrReports();
		$startDate 			  = date('Y-m-d', strtotime($workScheduleDetails['Regular_From']));
		$payslipExist 		  = $this->payslipExistForAttendanceDate($employeeId,$workScheduleDetails['Regular_From']); 
		$fullDayCompOffExist  = $hrReports->getCompensatoryOff($employeeId,$startDate,1,'','dashboardNoAttendance');
		$fullDayLeaveExist    = $hrReports->getLeaveDuration($employeeId,$startDate,1,'','dashboardNoAttendance',1);
		$employeeDetails      = $this->getEmployeeDateOfJoinResignation($employeeId);
		$firstHalfLeaveExist  = $hrReports->getLeaveDuration($employeeId,$startDate,0.5,'First Half','dashboardNoAttendance',1);
		$secondHalfLeaveExist = $hrReports->getLeaveDuration($employeeId,$startDate,0.5,'Second Half','dashboardNoAttendance',1);

		$quarterLeaveDurationConfigured = $this->_dbCommonFun->isQuarterDurationConfigured();
		if(!empty($quarterLeaveDurationConfigured)){
			$firstQuarterLeaveExist  = $hrReports->getLeaveDuration($employeeId,$startDate,0.25,'First Quarter','dashboardNoAttendance',1);
			$secondQuarterLeaveExist = $hrReports->getLeaveDuration($employeeId,$startDate,0.25,'Second Quarter','dashboardNoAttendance',1);
			$thirdQuarterLeaveExist  = $hrReports->getLeaveDuration($employeeId,$startDate,0.25,'Third Quarter','dashboardNoAttendance',1);
			$fourthQuarterLeaveExist = $hrReports->getLeaveDuration($employeeId,$startDate,0.25,'Fourth Quarter','dashboardNoAttendance',1);
		}else{
			$firstQuarterLeaveExist = $secondQuarterLeaveExist = $thirdQuarterLeaveExist = $fourthQuarterLeaveExist = 0;
		}

		
		$firstHalfLeaveDays = $this->getLeaveDays($firstHalfLeaveExist);
		$secondHalfLeaveDays = $this->getLeaveDays($secondHalfLeaveExist);
		$firstQuarterLeaveDays = $this->getLeaveDays($firstQuarterLeaveExist);
		$secondQuarterLeaveDays = $this->getLeaveDays($secondQuarterLeaveExist);
		$thirdQuarterLeaveDays = $this->getLeaveDays($thirdQuarterLeaveExist);
		$fourthQuarterLeaveDays = $this->getLeaveDays($fourthQuarterLeaveExist);

		// Sum all the leave days
		$totalLeaveDays = $firstHalfLeaveDays + $secondHalfLeaveDays + $firstQuarterLeaveDays + $secondQuarterLeaveDays + $thirdQuarterLeaveDays + $fourthQuarterLeaveDays;

		$checkShiftExist =  $this->checkShiftEnabled($employeeId); 
		if($checkShiftExist=='Shift Roster')
		{
			$businessWorkingDays = $this->_dbPayslip->getBusinessWorkingDays($startDate, $startDate, $employeeId,NULL,1,'leaves');
		}
		else 
		{
			$businessWorkingDays = $this->_dbPayslip->getBusinessWorkingDays($startDate, $startDate, $employeeId,'',1);
		}

		//if attendance is added after payslip generation we need to display this error message.
		if(!empty($payslipExist) && !empty($getAttendanceImportId)) 
		{ 
			$whereAttendanceImport['Attendance_Id IN (?)'] = $getAttendanceImportId; 
			$this->_db->update($this->_ehrTables->attendanceImport, array('Rollup_Flag'=>-1,'Error_Code'=>'EAI008','Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$sessionId), $whereAttendanceImport); 
		} 
		//if attendance is added before date of join we need to display this error message.
		elseif(!empty($employeeDetails['Date_Of_Join']) && $startDate < $employeeDetails['Date_Of_Join'] && !empty($getAttendanceImportId))
		{
			$whereAttendanceImport['Attendance_Id IN (?)'] = $getAttendanceImportId;
			$this->_db->update($this->_ehrTables->attendanceImport, array('Rollup_Flag'=>-1,'Error_Code'=>'EAI006','Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$sessionId), $whereAttendanceImport);
		}
		//if attendance is added after resignation date we need to display this error message.
		elseif(!empty($employeeDetails['Resignation_Date']) && $startDate > $employeeDetails['Resignation_Date'] && !empty($getAttendanceImportId))
		{
			$whereAttendanceImport['Attendance_Id IN (?)'] = $getAttendanceImportId;
			$this->_db->update($this->_ehrTables->attendanceImport, array('Rollup_Flag'=>-1,'Error_Code'=>'EAI007','Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$sessionId), $whereAttendanceImport);
		}
		elseif(!empty($fullDayLeaveExist) && (!empty($fullDayLeaveExist['Leave_Calculation_Days']) || ($businessWorkingDays > 0 && empty($fullDayLeaveExist['Leave_Calculation_Days']))) && !empty($getAttendanceImportId))   
		{
			if($this->_orgDetails['Leave_Auto_Rejection']=='Yes' && $fullDayLeaveExist['Total_Days']==1)
			{
				$autoCancelLeaveDetails = $this->autoRejectLeaveDetails($fullDayLeaveExist);
			}
			else
			{
				//when the fullday leave exist in that time we need to dispaly the error message
				$whereAttendanceImport['Attendance_Id IN (?)'] = $getAttendanceImportId; 
				$this->_db->update($this->_ehrTables->attendanceImport, array('Rollup_Flag'=>-1,'Error_Code'=>'EAI009','Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$sessionId), $whereAttendanceImport); 
			}
		}
		elseif(!empty($fullDayCompOffExist) && !empty($getAttendanceImportId)) 
		{ 
			//when the fullday compensatory off exist in that time we need to dispaly the error message
			$whereAttendanceImport['Attendance_Id IN (?)'] = $getAttendanceImportId; 
			$this->_db->update($this->_ehrTables->attendanceImport, array('Rollup_Flag'=>-1,'Error_Code'=>'EAI010','Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$sessionId), $whereAttendanceImport); 
		}
		elseif($totalLeaveDays == 1 && !empty($getAttendanceImportId)) 
		{
			if($this->_orgDetails['Leave_Auto_Rejection']=='Yes'){
				if(isset($firstHalfLeaveExist['Total_Days']) && $firstHalfLeaveExist['Total_Days']==0.5) {
					$autoCancelLeaveDetails = $this->autoRejectLeaveDetails($firstHalfLeaveExist);
				}

				if(isset($secondHalfLeaveExist['Total_Days']) && $secondHalfLeaveExist['Total_Days']==0.5) {
					$autoCancelLeaveDetails = $this->autoRejectLeaveDetails($secondHalfLeaveExist);
				}

				if(isset($firstQuarterLeaveExist['Total_Days']) && $firstQuarterLeaveExist['Total_Days']==0.25) {
					$autoCancelLeaveDetails = $this->autoRejectLeaveDetails($firstQuarterLeaveExist);
				}

				if(isset($secondQuarterLeaveExist['Total_Days']) && $secondQuarterLeaveExist['Total_Days']==0.25) {
					$autoCancelLeaveDetails = $this->autoRejectLeaveDetails($secondQuarterLeaveExist);
				} 
				
				if(isset($thirdQuarterLeaveExist['Total_Days']) && $thirdQuarterLeaveExist['Total_Days']==0.25) {
					$autoCancelLeaveDetails = $this->autoRejectLeaveDetails($thirdQuarterLeaveExist);
				}

				if(isset($fourthQuarterLeaveExist['Total_Days']) && $fourthQuarterLeaveExist['Total_Days']==0.25){
					$autoCancelLeaveDetails = $this->autoRejectLeaveDetails($fourthQuarterLeaveExist);
				}
			} else {
				$whereAttendanceImport['Attendance_Id IN (?)'] = $getAttendanceImportId; 
				$this->_db->update($this->_ehrTables->attendanceImport, array('Rollup_Flag'=>-1,'Error_Code'=>'EAI009','Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$sessionId), $whereAttendanceImport); 
			}
		}
		else 
		{
			$updateAttendanceImportErrorCode = 0;
		}

		return $updateAttendanceImportErrorCode;
	}

	// Helper function to safely get the Total_Days as float
	public function getLeaveDays($leaveData) {
		return isset($leaveData['Total_Days']) ? floatVal($leaveData['Total_Days']) : floatVal(0);
	}

	public function autoRejectLeaveDetails($leaveData)
	{
		$dbLeave = new Employees_Model_DbTable_Leave();
		/** If the approval status is approved or cancel applied then we should revert the leave balance */
		if(!empty($leaveData)){

			if($leaveData['Approval_Status'] === 'Approved' || $leaveData['Approval_Status'] === 'Cancel Applied')
			{
				$dbLeave->cancelLeaveBalance($leaveData['Total_Days'], $leaveData['Employee_Id'], $leaveData['LeaveType_Id']);
			}
			$whereEmployeeLeaves['Leave_Id IN (?)'] = $leaveData['Leave_Id']; 
			$this->_db->update($this->_ehrTables->empLeaves, array('Approval_Status'=>'Rejected'), $whereEmployeeLeaves); 
		}
	}
	/*get the employee date of join and resignation date based on employee id*/
	public function getEmployeeDateOfJoinResignation($employeeId)
	{
		$empJobDetails = $this->_db->fetchRow($this->_db->select()->from(array('J'=>$this->_ehrTables->empJob), array('Emp_InActive_Date','Date_Of_Join','Manager_Id'))
													->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id = J.Employee_Id', array())
													->where('J.Employee_Id = ?', $employeeId)
													->where('P.Form_Status = 1'));

		$resignationDate = $this->_db->fetchOne($this->_db->select()->from(array('R'=>$this->_ehrTables->resignation), array('Resignation_Date'))				
																				->where('R.Employee_Id = ?', $employeeId)
																				->where('R.Approval_Status LIKE ?', 'Approved'));

		if(!empty($empJobDetails['Emp_InActive_Date']))
		{
			$resignationDate = $empJobDetails['Emp_InActive_Date'];
		}
		elseif(!empty($resignationDate))
		{
			$resignationDate = $resignationDate;   
		}
		else 
		{
			$resignationDate = '';
		}

		return array('Date_Of_Join'=>$empJobDetails['Date_Of_Join'],'Resignation_Date'=>$resignationDate,'Manager_Id'=>$empJobDetails['Manager_Id']);
	}

	/*get the attendance import id based on employee id and workschedule details and rollup flags.*/
	public function getAttendanceImportId($employeeId,$workScheduleDetails,$rollupFlag)
	{
		$qryAttendanceImportId = $this->_db->select()->from(array('AI'=>$this->_ehrTables->attendanceImport),array('Attendance_Id'))
												->where('Employee_Id = ?', $employeeId)
												->where('AI.Added_On >= ?', $workScheduleDetails['Consideration_From'])
												->where('AI.Added_On <= ?', $workScheduleDetails['Consideration_To']);

		if($rollupFlag==1)
		{
			$qryAttendanceImportId->where('Rollup_Flag = ?',$rollupFlag);
		}

		$getAttendanceImportId = $this->_db->fetchCol($qryAttendanceImportId);

		return $getAttendanceImportId;

	}
	// get processed biometric attendance record status
	public function getProcessedBiometricAttendanceRecordStatus($employeeId)
	{
		$qryAttendanceProcessStatus =$this->_db->select()->from(array('EJ'=>$this->_ehrTables->empJob), array(''))
										->joinInner(array('E'=>$this->_ehrTables->empType), 'E.EmpType_Id = EJ.EmpType_Id', array('E.Attendance_Process_Status'))
									 	->where('Employee_Id = ?',$employeeId);

		$attendanceProcessStatus=$this->_db->fetchOne($qryAttendanceProcessStatus);
		
		if($attendanceProcessStatus=='Auto Approval')
		{
			$approvalStatus = "Approved";
		}
		elseif($attendanceProcessStatus=='Manual Approval')
		{
			$approvalStatus = "Applied";
		}
		else
		{
			$approvalStatus = "Applied";
		}
		return $approvalStatus;
	}
	
	// listing the device name in combo box
	public function getDeviceName()
	{
		$qryDeviceName = $this->_db->select()
							->from($this->_ehrTables->deviceManagement,array('Device_Id','Device_Name'));
		$deviceName=$this->_db->fetchPairs($qryDeviceName);
		return $deviceName;
	}

	public function syncIclockData($deviceId,$fromDate,$toDate)
	{
		$syncDataArray =array();
		$ehrTables = new Application_Model_DbTable_Ehr();

		// retrieve the device details from the device management
		$getDeviceDetails = $this->_db->fetchRow($this->_db->select()
														 ->from(array($this->_ehrTables->deviceManagement))
														 ->where('Device_Id = (?)', $deviceId));
		array_push($syncDataArray,$getDeviceDetails);

		// get the organization code 
		$getOrgCode = $ehrTables->getOrgCode();
		$isDomain = Zend_Registry::get('Domain');
		
		// get the domain name
		$isDomainArray = explode(".",$isDomain);
		$dataBaseName = $isDomainArray[0].'_'.$getOrgCode;
		array_push($syncDataArray,$dataBaseName);
		
		$getEmployeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('External_EmpId'))
								->where('Emp_Status = ?', 'Active')
								->where('External_EmpId IS NOT NULL')
								->orwhere('Emp_Status = ?','InActive' AND 'Emp_InActive_Date >=',$fromDate AND 'Emp_InActive_Date <=',$toDate));
		
		array_push($syncDataArray,$getEmployeeId,$fromDate,$toDate);
		return $syncDataArray;
	}
	
	// updating biometric sync history when sync button is clicked
	public function updateBiometricSyncHistory($attendanceSyncArray)
	{
		$action = 'Add';
		
		$updated = $this->_db->insert($this->_ehrTables->attendanceSyncHistory, $attendanceSyncArray);
		
		if($updated)
			$requestId = $this->_db->lastInsertId();

			/*
			 *	this function will handle
			 *		return success/failure array
			*/
			return array('success' => true, 'requestId'=>$requestId, 'type'=>'success');

	}

	//for updating the syncIclockData
		// public function syncIclockData($syncdata)
	// {
	// 	$devicelogEpush = 'devicelogs'.date("_n_Y");
	// //	$devicelogEpush = 'devicelogs_10_2015';
	// 	$empEpush = 'employees';
	// 	$device = 'devices';
	// 	try
	//     {
	// 		try
	// 		{
	// 			$params = array('host'     => $syncdata['ipAddress'],
	// 							'username' => $syncdata['userName'],
	// 							'password' => $syncdata['password'],
	// 							'encrypt'  => 0,
	// 							'dbname'   => $syncdata['databaseName']);
	// 			try
	// 			{
	// 				$orgDb = Zend_Db::factory('PDO_MYSQL', $params);
	// 				$orgDb->getConnection();
	// 			}
	// 			catch (Zend_Db_Adapter_Exception $e)
	// 			{
	// 				return array('success' => false, 'msg'=>'Please Check the iclock details', 'type'=>'warning');
	// 				// perhaps a failed login credential, or perhaps the RDBMS is not running
	// 			}
	// 			catch (Zend_Exception $e)
	// 			{
	// 				return array('success' => false, 'msg'=>'Please Check the iclock details', 'type'=>'warning');
	// 			}
	// 			//get the device details based on device name
	// 			if(!empty($syncdata['deviceName']))
	// 			{
	// 				$getDeviceCount = $orgDb->fetchOne($orgDb->select()
	// 												   ->from(array('D'=>$device),array('Count(DeviceId)'))
	// 												   ->where(new Zend_Db_Expr("CONCAT(D.DeviceFName,D.DeviceSName)")." = ?",$syncdata['deviceName']));
						
	// 				if($getDeviceCount==0)
	// 				{
	// 					return array('success' => false, 'msg'=>'Device Name not found', 'type'=>'warning');
	// 				}
	// 			}
	// 			//get all the data without considering the device name
	// 			$getSyncIclockData = $orgDb->select()->from(array('EP'=>$empEpush),
	// 														array('EP.EmployeeCode as Employee_Id','EmployeeName as Employee_Name'))
				
	// 									->joinInner(array('DP'=>$devicelogEpush),'EP.EmployeeCode=DP.UserId',
	// 												array('LogDate as Added_On','Attendance_Status' =>new Zend_Db_Expr('CASE WHEN direction = "out" THEN "C/Out" WHEN direction = "in" THEN "C/In" end'),
	// 													  new Zend_Db_Expr('1 as Schema_Id')))
										
	// 									->where('DP.hrapp_syncstatus NOT IN (?)', 1);
				
	// 			//get the data's based on device name	
	// 			if(!empty($syncdata['deviceName']))
	// 			{
	// 				$getSyncIclockData->joinInner(array('D'=>$device),'D.DeviceId=DP.DeviceId',array())
	// 								->where(new Zend_Db_Expr("CONCAT(D.DeviceFName,D.DeviceSName)")." = ?",$syncdata['deviceName']);		
	// 			}
						
	// 			$syncIclockData=$orgDb->fetchAll($getSyncIclockData);
				
	// 			if(count($syncIclockData) > 0)
	// 			{
	// 				//insert the details in attendance import table
	// 				$this->_ehrTables->insertMultiple($this->_ehrTables->attendanceImport,$syncIclockData);
					
	// 				$getDeviceLogId = $orgDb->select()->from(array('DP'=>$devicelogEpush),array('DP.DeviceLogId'))
	// 											->joinInner(array('EP'=>$empEpush),'EP.EmployeeCode=DP.UserId',array());
						
	// 				if(!empty($syncdata['deviceName']))
	// 				{
	// 					$getDeviceLogId->joinInner(array('D'=>$device),'D.DeviceId=DP.DeviceId',array())
	// 									->where(new Zend_Db_Expr("CONCAT(D.DeviceFName,D.DeviceSName)")." = ?",$syncdata['deviceName']);
	// 				}
					
	// 				$deviceLogId = $orgDb->fetchCol($getDeviceLogId);
	// 				//updating the already synced data as 1.
	// 				$validUpdated = $orgDb->update($devicelogEpush, array('hrapp_syncstatus'=> 1), array('DeviceLogId IN (?)'=>$deviceLogId));
					
	// 				return array('success' => true, 'msg'=>'Attendance data extracted successfully', 'type'=>'success');
	// 			}		
	// 			else
	// 			{
	// 				return array('success' => false, 'msg'=>'Already iclock datas are extracted', 'type'=>'warning');
	// 			}
	// 		}
	// 		catch(PDOException $pdoEx)
	// 		{
	// 			return array('success' => false, 'msg'=>'Please Check the iclock details', 'type'=>'warning');
	// 		}
	//     }
	//     catch(PDOException $pdoEx)
    // 	{
	// 		return array('success' => false, 'msg'=>'Please Check the iclock details', 'type'=>'warning');
    // 	}
	// }

	
	//for updating the break hours(include/exclude) in attendance import table
	public function updateImportedData($attendanceImportIds, $breakHourProcess, $sessionId)
	{
		$val = ($breakHourProcess == "exclude") ? 1 : 0;
		
		$whereCondition = $this->_db->quoteInto('Attendance_Id IN (?)', $attendanceImportIds);
		
		$attendanceImportData = array();
		$attendanceImportData['Exclude_Break_Hours'] = $val;
		$attendanceImportData['Updated_On'] = date('Y-m-d H:i:s');
		$attendanceImportData['Updated_By'] = $sessionId;
		$updated = $this->_db->update($this->_ehrTables->attendanceImport,$attendanceImportData, $whereCondition);
		
		/**
		 *	this function will handle
		 *		update system log function
		 *		clear submit lock fucntion
		 *		return success/failure array
		*/
		return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
														'action'         => 'Edit',
														'trackingColumn' => '',
														'formName'       => 'Attendance Import Data',
														'sessionId'      => $sessionId,
														'tableName'      => $this->_ehrTables->attendanceImport));
	}
	
	//for updating the attendance Status(Cin/Cout) in attendance import table
	public function updateImportStatus($attendanceImportIds, $status, $sessionId)
	{
		$whereCondition = $this->_db->quoteInto('Attendance_Id IN (?)', $attendanceImportIds);
		
		$updated = $this->_db->update($this->_ehrTables->attendanceImport,array('Attendance_Status'=>$status,'Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$sessionId), $whereCondition);
		
		/**
		 *	this function will handle
		 *		update system log function
		 *		clear submit lock fucntion
		 *		return success/failure array
		*/
		return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
														'action'         => 'Edit',
														'trackingColumn' => '',
														'formName'       => 'Attendance Import Data',
														'sessionId'      => $sessionId,
														'tableName'      => $this->_ehrTables->attendanceImport));
	}
	
	/**
	 * Get attendance schema details to show in a grid
	 */
	public function listAttendanceSchema($page, $rows, $sortField, $sortOrder, $searchAll)
	{
		switch($sortField)
        {
			case 1: $sortField = 'Schema_Name'; break;
			case 2: $sortField = 'Employee_Name'; break;
			case 3: $sortField = 'Employee_Id'; break;
			case 4: $sortField = 'Attendance_Status'; break;
		}
		
		$qryAttendanceSchema = $this->_db->select()->from($this->_ehrTables->attendanceHeader,
														  array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS Schema_Id as Count'),
																'Attendance_Status','Schema_Name', 'Employee_Id', 'Employee_Name',
																'Department', 'Location_Id', 'WorkSchedule_Id', 'Id_Number', 'Card_No',
																'Verify_Code', 'Added_On', 'Date_Format', 'Schema_Id'))
												
												->order("$sortField $sortOrder")
												->limit($rows, $page);
						
		if(!empty($searchAll) && preg_match('/^[a-zA-Z ]/', $searchAll))
		{			
			$conditions  = $this->_db->quoteInto('Schema_Name  Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or Employee_Name  Like ?', "%$searchAll%");			
			$conditions .= $this->_db->quoteInto('or Employee_Id Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or Attendance_Status Like ?', "%$searchAll%");
			
			$qryAttendanceSchema->where($conditions);
		}
		
		/**
		 * SQL queries
		 * Get data to display
		*/
		$attendanceSchema = $this->_db->fetchAll($qryAttendanceSchema);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendanceHeader, new Zend_Db_Expr('COUNT(Schema_Id)')));
		
		/**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $attendanceSchema);
	}
	
	// get field name from attendance_header_format table except 'Schema_Id', 'Schema_Name' field
	/* V0.4 user specified date format*/
	public function tableHeader()
	{
		return $this->_db->fetchCol($this->_db->select()->distinct()->from('information_schema.columns', array('COLUMN_NAME'))
									->where('table_name = ?', $this->_ehrTables->attendanceHeader)
									->where('COLUMN_NAME NOT IN (?)', array('Schema_Id', 'Schema_Name', 'Date_Format')));
	}
	
	/**
	 * Delete schema by schemaId
	 */
	public function deleteSchema($schemaId, $logEmpId)
	{
		/**
		 *	Check this schema id is already exists in attendance import table
		 *		and return the no of records.
		*/
		$exInAttImport = $this->_db->fetchOne($this->_db->select()
											  ->from($this->_ehrTables->attendanceImport, new Zend_Db_Expr('Count(Schema_Id)'))
											  ->where('Schema_Id = ?', $schemaId));
		
		/**
		 *	Check this schema id is already exists in attendance header table
		 *		and return the no of records.
		*/
		$exInAttHeader = $this->_db->fetchOne($this->_db->select()
											  ->from($this->_ehrTables->attendanceHeader, new Zend_Db_Expr('Count(Schema_Id)'))
											  ->where('Schema_Name = ?', 'Bio Attendance')
											  ->where('Schema_Id = ?', $schemaId));
		
		if ( $exInAttImport == 0 && $exInAttHeader == 0 )
		{
			/* Delete activity */
			$deleted =1;
			
			$deleted = $this->_db->delete($this->_ehrTables->attendanceHeader, array('Schema_Id = ?'=>$schemaId));
			
			if($deleted)
				$this->_db->delete($this->_ehrTables->attendanceStatus, array('Schema_Id = ?'=>$schemaId));		
			
			/**
			*	delete activity for common function
			*		1)check lock is exist or not.
			*			If lock is exist then show error message like employee is open record for update.
			*		2)If No lockflag then process delete activity
			*		3)Update delete activity in system log
			*		4)return success/failure message
		   */
		   return $this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
								'lockFlag'       => 0,
								'formName'       => 'Attendance Schema',
								'trackingColumn' => '',
								'sessionId'      => $logEmpId,
								'comboPair'      => $this->schemaPairs()));
		}
		else
		{
			return array('success'=>false, 'msg'=>'Unable to delete Attendance Schema. Please, contact system admin', 'type'=>'info');
		}
	}
	
	/**
	 * Get schema details
	 */
	public function schemaPairs()
	{
		return $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->attendanceHeader, array('Schema_Id', 'Schema_Name')));
	}
	
	/**
	 * Get schemaId after insert attendance header data
	 */
	public function addAttendanceFileHeader($headerData, $sessionId)
	{
		$updated = $this->_db->insert($this->_ehrTables->attendanceHeader, $headerData);
		$schemaId = $this->_db->lastInsertId();
		/**
		 *	this function will handle
		 *		update system log function
		 *		clear submit lock fucntion
		 *		return success/failure array
		*/
		
		
		$result = $this->_dbCommonFun->updateResult (array('updated'        => $updated,
														   'action'         => 'Add',
														   'trackingColumn' => '',
														   'formName'       => 'Attendance Header',
														   'sessionId'      => $sessionId,
														   'tableName'      => $this->_ehrTables->attendanceHeader,
														   'comboPair'      => $this->schemaPairs()));
		
		if($result['success'])
		{
			$result['schemaId'] = $schemaId;
		}
		
		return $result;
	}
	
	/**
	 * Get status pairs details to show in a grid
	 */
	public function listStatusPair($page, $rows, $sortField, $sortOrder, $schemaId)
	{
		switch($sortField)
        {
			case 0: $sortField = 'Status_PairA'; break;
			case 1: $sortField = 'Status_PairB'; break;
		}
		
		$attendanceStatusPair = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->attendanceStatus,
													   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS Schema_Id as Count'),
															 'Status_PairA', 'Status_PairB'))
											
											->order("$sortField $sortOrder")
											->limit($rows, $page)
											->where('Schema_Id = ?', $schemaId));
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendanceStatus, new Zend_Db_Expr('COUNT(Schema_Id)'))
									   ->where('Schema_Id = ?', $schemaId));
		
		/**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $attendanceStatusPair);
	}
	
	// Status pair add/Update on import attendance format
	public function updateStatusPair($statusArray, $oldValue)
	{
		$qryStatusPair = $this->_db->select()->from($this->_ehrTables->attendanceStatus, new Zend_Db_Expr('Count(Schema_Id)'))
											->where('Schema_Id = ?', $statusArray['Schema_Id'])
											->where('Status_PairA = ? or Status_PairB = ?', $statusArray['Status_PairA'])
											->where('Status_PairA = ? or Status_PairB = ?', $statusArray['Status_PairB']);
		
		if($oldValue['statusPairA'] != '' && $oldValue['statusPairB'] != '')
		{
			$qryStatusPair->where('Schema_Id = ?',   $statusArray['Schema_Id'])
						->where('Status_PairA != ?',  $oldValue['statusPairA'])
						->where('Status_PairB != ?',  $oldValue['statusPairB']);
		}
		
		$isExist = $this->_db->fetchOne($qryStatusPair);
		
		if ($isExist == 0)
		{
			if($oldValue['statusPairA'] != '' && $oldValue['statusPairB'] != '')
			{
				$action = "updated";
				
				$where = array('Schema_Id= ?'     => $statusArray['Schema_Id'],
							   'Status_PairA = ?' => $oldValue['statusPairA'],
							   'Status_PairB = ?' => $oldValue['statusPairB']);
				
				$updated = $this->_db->update($this->_ehrTables->attendanceStatus,$statusArray,$where);	
			}
			else
			{
				$action = "added";
				
				$updated = $this->_db->insert($this->_ehrTables->attendanceStatus, $statusArray);	
			}
			
			if($updated)
			{
				return array('success' => true, 'msg'=>'Status Pair ' .$action.' succesfully', 'type'=>'success');	
			}
			else
			{
				return array('success' => false, 'msg'=>'Unable to '.$action.' Status Pair', 'type'=>'info');	
			}
		}
		else
		{
			return array('success' => false, 'msg'=>'Status Pair already exist', 'type'=>'info');
		}
	}
	
	// Status pair delete on import attendance format
	public function deleteStatusPair($statusArray)
	{
		$where = array('Schema_Id= ?'     => $statusArray['schemaId'],
					   'Status_PairA = ?' => $statusArray['checkIn'],
					   'Status_PairB = ?' => $statusArray['checkOut']);
		
		$deleted = $this->_db->delete($this->_ehrTables->attendanceStatus,$where);
		
		if($deleted)
		{
			return array('success'=>true, 'msg'=>'Attendance status pair deleted succesfully', 'type'=>'success');
		}
		else
		{
			return array('success'=>false, 'msg'=>'Unable to delete Attendance status pair', 'type'=>'info');
		}
	}
	
	/**
	 * Get attendance status by schemaId
	 */
	public function getAttendanceFileStatus($schemaId)
	{
		return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendanceHeader, 'Attendance_Status')
				->where('Schema_Id = ?', $schemaId));
	}
	
	/**
	 * Get attendance header data by schemaId
	 */
	public function getAttendanceHeaderData($schemaId)
	{
		return $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->attendanceHeader,
															   array('Department','Employee_Name','Employee_Id', 'Added_On',
																	 'Attendance_Status', 'Location_Id', 'Id_Number', 'WorkSchedule_Id',
																	 'Verify_Code', 'Card_No','Work_Place'))
									->where('Schema_Id = ?', $schemaId));
	}
	
	/**
	 * Get attendance count
	 */
	public function ckAttendanceStatus($attendanceId, $logEmpId, $status)
	{
		return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendance, new Zend_Db_Expr('count(Employee_Id)'))
									->where('Approval_Status = ?', $status)
									->where('Attendance_Id = ?', $attendanceId)
									->where('Approver_Id = ?', $logEmpId));
	}

	/**
	 * Get attendance status by attedanceId
	 */
	public function getStatus($attendanceId)
	{
		return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendance,array('Status'=>'Approval_Status'))
									->where('Attendance_Id = ?', $attendanceId));
	}

	/**
	 * Get attendance details for current date for a given employeeId
	 * whose status is "Returned" / "Draft"
	 */
	public function ckAttendanceId($attendanceId, $empId, $date)
	{
		$conditions = $this->_db->quoteInto('att.Approval_Status = ? or ', "Draft") .
		$this->_db->quoteInto('att.Approval_Status = ? ', "Returned");

		$attQry = $this->_db->select()
		->from(array('att'=>$this->_ehrTables->attendance), 'att.Employee_Id')
		->where('att.Attendance_Id = ?',$attendanceId)
		->where('att.PunchIn_Date = ?',date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($date))));

		$attRow=$this->_db->fetchOne($attQry);

		return $attRow;
	}

	/**
	 * Get attendance details by date , time and attendanceId
	 */
	public function getAttendanceByDate($empId,$punchInDate,$punchInTime,$punchOutTime,$attId)
	//public function getAttendanceByDate($empId,$punchInDate,$punchInTime,$punchOutTime)
	{
		if ($empId != "" && $punchInDate != "")
		{
			$punchInDate=date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($punchInDate)));
			 
			$dateConditions = $this->_db->quoteInto('att.PunchIn_Date = ? or ', $punchInDate) .
			$this->_db->quoteInto('att.PunchOut_Date = ? ', $punchInDate);
			 
			$statusConditions = $this->_db->quoteInto('att.Approval_Status = ? or ', "Approved") .
			$this->_db->quoteInto('att.Approval_Status = ? ', "Applied");
			 
			$this->_db->setFetchMode(Zend_Db::FETCH_ASSOC);
			 
			$attQry=$this->_db->select()
			->from(array('att'=>$this->_ehrTables->attendance),array(new Zend_Db_Expr("DATE_FORMAT(att.PunchIn_Date,'".$this->_orgDF['sql']."') as PunchIn_Date"),
					new Zend_Db_Expr("DATE_FORMAT(att.PunchOut_Date,'".$this->_orgDF['sql']."') as PunchOut_Date"),
					new Zend_Db_Expr("DATE_FORMAT(att.PunchIn_Time,'%H:%i') as PunchIn_Time"),
					new Zend_Db_Expr("DATE_FORMAT(att.PunchOut_Time,'%H:%i') as PunchOut_Time"),
					'att.Total_Hours','att.Employee_Id','att.Approver_Id','att.Attendance_Id',
					'Status'=>'att.Approval_Status','att.Added_By'))
					->joinInner(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=att.Employee_Id ',
							array(new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as EmployeeName")))
							->joinInner(array('emp1'=>$this->_ehrTables->empPersonal),'emp1.Employee_Id=att.Approver_Id',
									array(new Zend_Db_Expr("CONCAT(emp1.Emp_First_Name, ' ', emp1.Emp_Last_Name) as ApproverName")))
									->joinInner(array('job'=>$this->_ehrTables->empJob),'emp.Employee_Id=job.Employee_Id',array(''))
									->joinInner(array('des'=>$this->_ehrTables->designation),'des.Designation_Id=job.Designation_Id',('des.Grade_Id'))
									->where('att.Employee_Id = ?',$empId)
									->where($statusConditions)
									->where($dateConditions)
									->order('att.PunchIn_Time DESC');
			 
			if (!empty($attId))
			{
				$attQry->where ('att.Attendance_Id != ?',$attId);
			}
			if (!empty($punchInTime))
			{
				$tconditions = $this->_db->quoteInto('att.PunchIn_Time <= ? or ', $punchInTime) .
				$this->_db->quoteInto('att.PunchIn_Time >= ? ', $punchInTime);
				$attQry->where($tconditions);
			}
			 
			$attRow=$this->_db->fetchAll($attQry);

			return $attRow;
		}
	}

	//to fetch the attendance record for an employee based on date
	public function getEmpAttendanceByDate($empId,$punchInDate,$attId = null)
	{
		if (!empty($empId) && !empty($punchInDate))
		{
			$punchInDate = date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($punchInDate)));
			 
			$this->_db->setFetchMode(Zend_Db::FETCH_ASSOC);
			
			$attQry = $this->_db->select()->from(array('att'=>$this->_ehrTables->attendance),
												 array(new Zend_Db_Expr("DATE_FORMAT(att.PunchIn_Date,'".$this->_orgDF['sql']."') as PunchIn_Date"),
													   new Zend_Db_Expr("DATE_FORMAT(att.PunchOut_Date,'".$this->_orgDF['sql']."') as PunchOut_Date"),
													   new Zend_Db_Expr("DATE_FORMAT(att.PunchIn_Time,'%H:%i') as PunchIn_Time"),
													   new Zend_Db_Expr("DATE_FORMAT(att.PunchOut_Time,'%H:%i') as PunchOut_Time"),
													   'att.PunchOut_Date as mob_PunchOut_Date', 'att.PunchIn_Date as mob_PunchIn_Date',
													   'att.Total_Hours','att.Exclude_Break_Hours','att.Employee_Id','att.Approver_Id', 'att.Late_Attendance',
													   'att.Attendance_Id','Status'=>'att.Approval_Status','att.Added_By','att.Lock_Flag'))
								
								->joinInner(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=att.Employee_Id ',
											array(new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as EmployeeName")))
								
								->joinInner(array('emp1'=>$this->_ehrTables->empPersonal),'emp1.Employee_Id=att.Approver_Id',
											array(new Zend_Db_Expr("CONCAT(emp1.Emp_First_Name, ' ', emp1.Emp_Last_Name) as ApproverName")))
								
								->joinInner(array('job'=>$this->_ehrTables->empJob),'emp.Employee_Id=job.Employee_Id',array(''))
								
								->joinInner(array('des'=>$this->_ehrTables->designation),'des.Designation_Id=job.Designation_Id',
											('des.Grade_Id'))
								
								->where('att.Employee_Id = ?',$empId)
								->where('att.Approval_Status = ?',"Draft")
								->where('att.PunchIn_Date = ?',$punchInDate);
			if($attId != null)
			{
				$attQry->where('att.Attendance_Id != ?',$punchInDate);
			}
			
			return $this->_db->fetchRow($attQry);
		}
	}

	/* get Attendance or compensatory off exist for given leave from and to date*/
	public function getAttendanceInLeaveRange($empId, $leavefromDate, $leavetoDate, $duration, $leavePeriod)
	{
		if (!empty($empId) && !empty($leavefromDate) && !empty($leavetoDate))
		{
			$leavefromDate 			= date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($leavefromDate)));
			$leavetoDate 			= date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($leavetoDate)));
			$dbHrReports    		= new Reports_Model_DbTable_HrReports();
			$attendanceDetails  	= $dbHrReports->getAttendanceDetails($empId,$leavefromDate,$leavetoDate,'dashboardNoAttendance');
			$compensatoryOffDetails = $dbHrReports->getCompensatoryOffDetails($empId,$leavefromDate,$leavetoDate,'dashboardNoAttendance');

			if(in_array($leavePeriod,array('First Quarter','Second Quarter')))
			{
				$compOffPeriodToCheck = 'First Half';
			}
			else
			{
				$compOffPeriodToCheck = 'Second Half';
			}

			//when the leave duration is full day if we have attendance or compensatory off exist we need to pass the error message
			if($duration==1 && (!empty($attendanceDetails) || !empty($compensatoryOffDetails)))
			{
				return 1;
			}
			//when the leave duration is half day if we have compensatory off exist for full day we need to pass the error message and 
			// leave and compoff in same period(first half,first half) we need to pass the error message
			else if($duration == 0.5)
			{
				if(!empty($compensatoryOffDetails))
				{
					foreach($compensatoryOffDetails as 	$compensatoryOff)
					{
						if($compensatoryOff['Period'] == $leavePeriod ||$compensatoryOff['Duration'] == 1)
						{
							return 1;
						}
						else
						{
							return 0;
						}
					}
				}
			}
			////when the leave duration is quarter day if we have compensatory off exist for full day we need to pass the error message and 
			// leave and compoff in same period(first half,first half) we need to pass the error message
			else
			{
				if(!empty($compensatoryOffDetails))
				{
					foreach($compensatoryOffDetails as 	$compensatoryOff)
					{
						if(($compensatoryOff['Period'] == $compOffPeriodToCheck || $compensatoryOff['Duration'] == 1))
						{
							return 1;
						}
						else
						{
							return 0;
						}
					}
				}
			}
		}
		return 0;
	}
	 
	/**
	 * Get attendance header details by fileHeaders and schemaId
	 */
	public function attendanceFileHeader($fileHeaders,$schemaId)
	{
		$qryAttHeader = $this->_db->select()->from($this->_ehrTables->attendanceHeader, array(new Zend_Db_Expr('COUNT(Schema_Id)')))
		//->where('Employee_Name IN (?)', $fileHeaders)
		->where('Employee_Id IN (?)', $fileHeaders)->where('Added_On IN (?)', $fileHeaders)
		->where('Attendance_Status IN (?)', $fileHeaders)->where('Schema_Id = ?', $schemaId);
		$cntAttHeader = $this->_db->fetchOne($qryAttHeader);
		return $cntAttHeader;
	}

	/**
	 * Add status pair
	 */
	public function addStatuspair($statuspairData)
	{
		$statuspair = $this->_ehrTables->insertMultiple($this->_ehrTables->attendanceStatus, $statuspairData);
		if($statuspair)
		{
			return true;
		}

	}

	/**
	 * Get attendance status pairs by schemaId
	 */
	public function getAttendanceStatusPairs($schemaId)
	{
		return $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->attendanceStatus,
																 array('Status_PairB','Status_PairA'))
									  ->where('Schema_Id = ?', $schemaId));
	}

	/**
	 * Get all attendance status pairs
	 */
	public function attendanceStatusPairs()
	{
		$getAttImportStatus = $this->_db->fetchCol($this->_db->select()
				->union(array($this->_db->select()->from($this->_ehrTables->attendanceStatus, array('Status_PairA')),
						$this->_db->select()->from($this->_ehrTables->attendanceStatus, array('Status_PairB')))));
		return $getAttImportStatus;
	}

	/**
	 * Get attendance import data by schemaId
	 */
	public function getAttendanceImportData($schemaId)
	{
		$headerData = array();
		$headerData['Header'] = $this->getAttendanceHeaderData($schemaId);
		$headerData['Status'] = $this->getAttendanceStatusPairs($schemaId);
		return $headerData;
	}

// get employee exclude break hours value from employee type table and to be passed while importing attendance record file.
	public function employeeBreakImport($importData)
	{ 
		$empbreakHours =$this->_db->fetchOne($this->_db->select()->from(array('EJ'=>$this->_ehrTables->empJob), array(''))
        ->joinInner(array('E'=>$this->_ehrTables->empType), 'E.EmpType_Id = EJ.EmpType_Id', array('Exclude_Break_Hours'))
		->where('EJ.External_EmpId = ? ', $importData['Employee_Id']));
       
        return $empbreakHours;
	}

	/**
	 * Add attendance import
	 */
	public function addAttendanceImport($importData,$sessionId,$attendanceDateFormat)
	{
		if (!empty($importData))
		{
			$inserted = 0;
			$keys = array_column($importData, 'Added_On');
			array_multisort($keys, SORT_ASC, $importData);
			$isDateFormatCorrect = 'No';
			foreach($importData as $attendanceImportDetails)
			{
				$attendanceImportedDate = date('Y-m-d', strtotime($attendanceImportDetails['Added_On']));
				$referenceDate          = '1970-01-01';
				if ($attendanceImportedDate==$referenceDate) 
				{
					return array('success' => false, 'msg' => 'The format of the attendance schema, represented by '.$attendanceDateFormat.',does not align with the date format in the imported data', 'type' => 'warning');
				}
				else
				{
					$isDateFormatCorrect = 'Yes';					
				}
			}

			if($isDateFormatCorrect=='Yes')
			{
				foreach($importData as $attendanceImportDetails)
				{
					$attendanceImportExist = $this->_dbCommonFun->attendanceImportAlreadyExist($attendanceImportDetails);
					if(empty($attendanceImportExist))
					{
						$attendanceImportDetails['Added_On_Date_Time'] = date('Y-m-d H:i:s');
						$attendanceImportDetails['Added_By']           = $sessionId;
						$inserted = $this->_db->insert($this->_ehrTables->attendanceImport, $attendanceImportDetails);
					}
				}
			}

			if ($inserted)
				return array('success' => true, 'msg' => 'Data has been imported successfully', 'type' => 'success');
			else
			    return array('success' => false, 'msg' => 'Unable to import the data.attendance records are already imported.please delete those record and import again', 'type' => 'warning'); 	
		}
	}

	/*When the punchin times falls outside the regular consideration configuration we are returning as "1" else returning as "0".
		valid scenario returned as "0".
		invalid scenario returned as "1" 
	*/
	public function getValidPunchIn($empId,$punchInDate, $punchInTime)
	{

		$punchInDateTime = date("Y-m-d H:i:s",strtotime($punchInDate.' '.$punchInTime));
	   
		//Based on the given punchin date and time we could identify whether the workschedule details is falling in 1.given date,2.given date(-1 day),3.given date (+1 day)
		$empGraceTimeDetails = $this->getCurrentWorkScheduleDetails($empId,$punchInDateTime);
		if(!empty($empGraceTimeDetails))
		{
			$validPunchIn = $this->getPunchInDetails($empGraceTimeDetails,$punchInDateTime);
		}
		else 
		{
			$validPunchIn = 1;
		}
	
		return $validPunchIn;
	}

	/*When the punchin and punchout times falls outside the regular consideration configuration we are returning as "1" else returning as "0".
	  valid scenario returned as "0".
	  invalid scenario returned as "1" 
	*/
	public function getValidPunchOut($empId, $punchOutDate, $punchOutTime , $punchInDate = null, $punchInTime= null)
	{
		if(!empty($punchInDate)&&!empty($punchInTime))
		{
			$punchInDateTime = date("Y-m-d H:i:s",strtotime($punchInDate.' '.$punchInTime));
		
			//Based on the given punchin date and time we could identify whether the workschedule details is falling in 1.given date,2.given date(-1 day),3.given date (+1 day)
			$empGraceTimeDetails = $this->getCurrentWorkScheduleDetails($empId,$punchInDateTime);
			if(!empty($empGraceTimeDetails))
			{
				$punchOutDateTime = date("Y-m-d H:i:s",strtotime($punchOutDate.' '.$punchOutTime));
		
				$validPunchOut = $this->getPunchOutDetails($empGraceTimeDetails,$punchOutDateTime,$punchInDateTime);	
			}
			else 
			{
				$validPunchOut = 1;
			}
		}
		else
		{
			$validPunchOut=1;
		}
 		return $validPunchOut;
	}

	//this function retrive given punchin date time is valid punchin or not
	public function getPunchInDetails($empGraceTimeDetails,$punchInDateTime)
	{
		$invalidAttCIn = 0;
		// check in should be present inside regular consideration from and consideration to range
		if($empGraceTimeDetails['Consideration_From'] <= $punchInDateTime && $empGraceTimeDetails['Consideration_To'] > $punchInDateTime)
		{
			//punchInDateTime less than regular end time its a valid record.then its a valid record.
			if($punchInDateTime < $empGraceTimeDetails['Regular_To'])
			{
				$invalidAttCIn = 0;
			}
			// when the allow attendance outside regular work hours flag is 1
			// punchin date time should be greater than or equal to regular work end time.then its a valid record.
			elseif($punchInDateTime >= $empGraceTimeDetails['Regular_To'] && $empGraceTimeDetails['Allow_Attendance_Outside_Regular_WorkHours']==1)
			{
				$invalidAttCIn = 0;
			}
			// when the allow attendance outside regular work hours flag is zero
			// punchin date time should be greater than or equal to regular work end time.then its not valid record.
			// then we shouldn't allow employee to add a punchin record
			elseif($punchInDateTime >= $empGraceTimeDetails['Regular_To'] && $empGraceTimeDetails['Allow_Attendance_Outside_Regular_WorkHours']==0)
			{
				$invalidAttCIn = 1;
			}
		}
		else
		{
				$invalidAttCIn = 1;
		}
        return $invalidAttCIn;
	}
	
	//this function retrive given punchout date time is valid punchout or not
	public function getPunchOutDetails($empGraceTimeDetails,$punchOutDateTime,$punchInDateTime)
	{
		$invalidAttCOut = 0;	
	
		if($empGraceTimeDetails['Consideration_From'] <= $punchInDateTime && $empGraceTimeDetails['Consideration_To'] > $punchInDateTime)
		{
			if($empGraceTimeDetails['Consideration_From'] <= $punchOutDateTime && $empGraceTimeDetails['Consideration_To'] >= $punchOutDateTime)
			{
				//punchOutDateTime greater than regular from time then its a valid record.
				if($punchOutDateTime > $empGraceTimeDetails['Regular_From'])
				{
					$invalidAttCOut = 0;
				}
				// when the allow attendance outside regular work hours flag is 1
				// punchOutDateTime should be less than or equal to regular from time then its a valid record.
				elseif($punchOutDateTime <= $empGraceTimeDetails['Regular_From'] && $empGraceTimeDetails['Allow_Attendance_Outside_Regular_WorkHours']==1)
				{
					$invalidAttCOut = 0;
				}
				// when the allow attendance outside regular work hours flag is zero
				// punchOutDateTime should be less than or equal to regular work end time then its not valid record.
				elseif($punchOutDateTime <= $empGraceTimeDetails['Regular_From'] && $empGraceTimeDetails['Allow_Attendance_Outside_Regular_WorkHours']==0)
				{
					$invalidAttCOut = 1;
				}
			}
			else
			{
				$invalidAttCOut = 1;
			}
		}
		else
		{
			$invalidAttCOut = 1;
		}
		return $invalidAttCOut;
	}

	//version 0.6=> added Exclude Break Hours in attendance import process
	public function readAttendanceImport($arrAttendanceId, $logEmpId)
	{
		$empAttendanceArr = array();
		$invalidAttendanceId = array();
		$validAttendanceId = array();
		$existAttendanceId = array();
		$updateAttendanceArr = array();
		
		if(!empty($arrAttendanceId))
		{
			$cntValidAttId = count($arrAttendanceId);
			if($cntValidAttId > 0)
			{
				for ($cnt = 0; $cnt<$cntValidAttId; $cnt++)
				{
					$employeeDetails = $this->getEmployeeDateOfJoinResignation($arrAttendanceId[$cnt]['Employee_Id']);
					if(!empty($employeeDetails['Manager_Id']))
					{
						$approverId = $employeeDetails['Manager_Id'];
					}
					else 
					{
						$approverId = $arrAttendanceId[$cnt]['Employee_Id'];
					}

					$graceTimeDetails=$this->getCurrentWorkScheduleDetails($arrAttendanceId[$cnt]['Employee_Id'],$arrAttendanceId[$cnt]['Added_On']);

					if(!empty($graceTimeDetails))
					{
						$attendanceActualDate = date('Y-m-d',strtotime($graceTimeDetails['Regular_From']));
						$updateErrorCode = $this->updateAttendanceImportErrorCode($arrAttendanceId[$cnt]['Employee_Id'],$graceTimeDetails,$arrAttendanceId[$cnt]['Attendance_Id'],$logEmpId);
						if(!empty($updateErrorCode))
						{
							$invalidAttendanceId[] = $arrAttendanceId[$cnt]['Attendance_Id'];
						}
						else 
						{
							//we are not handling error code for every valid check in & check out method.so when invalid record is processed again we need to update the error code as empty
							if(!empty($arrAttendanceId[$cnt]['Attendance_Id']))
							{
								$whereAttendanceImport['Attendance_Id IN (?)'] = $arrAttendanceId[$cnt]['Attendance_Id'];
								$this->_db->update($this->_ehrTables->attendanceImport, array('Error_Code'=>'','Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$logEmpId), $whereAttendanceImport);
							}
							$qryStatusPairB = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->attendanceStatus, 'Status_PairB')
								->where('Status_PairA LIKE ?', $arrAttendanceId[$cnt]['Attendance_Status'])->where('Schema_Id = ?', $arrAttendanceId[$cnt]['Schema_Id']));
						
							$mispair = 0;
							
							if(isset($arrAttendanceId[$cnt]['Attendance_Status']) && isset($arrAttendanceId[$cnt+1]['Attendance_Status']))
							{
								if(in_array($arrAttendanceId[$cnt+1]['Attendance_Status'], $qryStatusPairB))
								{
									if($arrAttendanceId[$cnt]['Location_Id'] == $arrAttendanceId[$cnt+1]['Location_Id'] &&
											$arrAttendanceId[$cnt]['Verify_Code'] == $arrAttendanceId[$cnt+1]['Verify_Code'] &&
											$arrAttendanceId[$cnt]['Exclude_Break_Hours'] == $arrAttendanceId[$cnt+1]['Exclude_Break_Hours'])
									{
										if (!empty($arrAttendanceId[$cnt+1]['Added_On']))
										{
											$attHours=$this->_dbTimesheetHrs->getRegularHrs($arrAttendanceId[$cnt]['Grade_Id']);
											
											if (strtotime($arrAttendanceId[$cnt+1]['Added_On']) > strtotime($arrAttendanceId[$cnt]['Added_On']))
											{
												$hoursDiff=strtotime($arrAttendanceId[$cnt+1]['Added_On']) - strtotime($arrAttendanceId[$cnt]['Added_On']);
											}
											else if (strtotime($arrAttendanceId[$cnt+1]['Punch_Date']) == strtotime($arrAttendanceId[$cnt]['Punch_Date']))
											{
												$hoursDiff=strtotime($arrAttendanceId[$cnt+1]['Punch_Time']) - strtotime($arrAttendanceId[$cnt+1]['Punch_Time']);
											}
											
											$workedHours=$hoursDiff/60/60;
										}
										else
										{
											$workedHours=0;
											$otHours=0;
										}

										// ck attendance already exist if not add
										$attendanceExist = $this->ckAttendanceExist($arrAttendanceId[$cnt]['Employee_Id'], strtotime($arrAttendanceId[$cnt]['Added_On']),
												strtotime($arrAttendanceId[$cnt+1]['Added_On']));
										
										if($attendanceExist == 0)
										{
											$attendanceData = array( 'Employee_Id'=>$arrAttendanceId[$cnt]['Employee_Id'],
																	'Location_Id'=>$arrAttendanceId[$cnt]['Location_Id'],
																	'Attendance_Date'=>$attendanceActualDate,
																	'Exclude_Break_Hours'=>$arrAttendanceId[$cnt]['Exclude_Break_Hours'],
																	'Approver_Id'=>$approverId,
																	'Added_By'=>$logEmpId,
																	'Added_Date'=>date('Y-m-d H:i:s'));
											
											if($workedHours<24)
											{
												$attendanceData['PunchIn_Date'] = $arrAttendanceId[$cnt]['Punch_Date'];
												$attendanceData['PunchOut_Date'] = $arrAttendanceId[$cnt+1]['Punch_Date'];
												$attendanceData['PunchIn_Time'] = $arrAttendanceId[$cnt]['Punch_Time'];
												$attendanceData['PunchOut_Time'] = $arrAttendanceId[$cnt+1]['Punch_Time'];
												$attendanceData['Checkin_Data_Source'] = $arrAttendanceId[$cnt]['Data_Source'];
												$attendanceData['Checkout_Data_Source'] = $arrAttendanceId[$cnt+1]['Data_Source'];
												$attendanceData['Total_Hours'] = round($workedHours,2);
												$attendanceData['Approval_Status'] = 'Approved';
												
												//Grace 
												$attendanceData['Actual_Punch_In_Time'] ='';
												$attendanceData['Actual_PunchOut_Time'] ='';
												$attendanceData['Actual_Total_Hours']=''; 
												
												$invalidAttCIn = 0;
												$invalidAttCOut = 0;   
												// Based on work schedule configuration to check punch in valid or not 
												$invalidAttCIn = $this->getValidPunchIn($attendanceData['Employee_Id'],$attendanceData['PunchIn_Date'],$attendanceData['PunchIn_Time'] );

													if($invalidAttCIn == 1)
													{
														$attendanceData['PunchIn_Time'] = null;
														$attendanceData['PunchIn_Date'] = null;
														$attendanceData['Total_Hours'] = round(0,2);
														$attendanceData['Approval_Status'] = 'Draft';

													}
											// Based on work schedule configuration(Grace time,considration check-in time and check-out time) to check punch in valid or not 
											$invalidAttCOut = $this->getValidPunchOut($attendanceData['Employee_Id'],$attendanceData['PunchOut_Date'], $attendanceData['PunchOut_Time'],$attendanceData['PunchIn_Date'],$attendanceData['PunchIn_Time'] );
													
													if($invalidAttCOut == 1)	
													{
														$attendanceData['PunchOut_Time'] = null;
														$attendanceData['PunchOut_Date'] = null;
														$attendanceData['Total_Hours'] = round(0,2);
														$attendanceData['Approval_Status'] = 'Draft';
													}

												//Check-in and check-out are invalid 
												if($invalidAttCOut == 1 && $invalidAttCIn == 1)
												{
													unset($attendanceData);
												}
												else
												{
													$empAttendanceArr[] = $attendanceData;
												}
											}
											else
											{
												$updateAttendanceDetails = array();
												
												//Check attendance record exist without punchout date/time
												$punchInDateTime = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendance, array(new Zend_Db_Expr('Concat(PunchIn_Date," ",PunchIn_Time) as PunchInDateTime')))
																						->where('Employee_Id = ?', $arrAttendanceId[$cnt+1]['Employee_Id'])
																						->where('Attendance_Date = ?', $attendanceActualDate)
																						->where(new Zend_Db_Expr('Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T")').' <= ?',$arrAttendanceId[$cnt+1]['Added_On'])
																						->where('Approval_Status = ?', 'Draft'));

												if(!empty($punchInDateTime))
												{
													$punchInAttExist = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->attendance, array(new Zend_Db_Expr('Concat(PunchIn_Date," ",PunchIn_Time) as PunchInDateTime'),'Attendance_Id','Checkin_Data_Source'))
													->where('Employee_Id = ?', $arrAttendanceId[$cnt]['Employee_Id'])
													->where(new Zend_Db_Expr('Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T"))').'= ?',$punchInDateTime)
													->where('Approval_Status = ?', 'Draft'));										
												}
												else
												{
													$punchInAttExist = array();
												}
												
												
												//If exist, update
												if(!empty($punchInAttExist))
												{
													if($punchInAttExist[0]['PunchInDateTime'] != '' || $punchInAttExist[0]['Attendance_Id'] > 0)
													{
														if (strtotime($arrAttendanceId[$cnt+1]['Punch_Date'] . ' ' . $arrAttendanceId[$cnt+1]['Punch_Time']) > strtotime($punchInAttExist[0]['PunchInDateTime']))
														{
															$hoursDiff=strtotime($arrAttendanceId[$cnt+1]['Punch_Date'] . ' ' . $arrAttendanceId[$cnt+1]['Punch_Time']) - strtotime($punchInAttExist[0]['PunchInDateTime']);
														}
														else if (strtotime($arrAttendanceId[$cnt+1]['Punch_Date']) == strtotime(date("Y-m-d", strtotime($punchInAttExist[0]['PunchInDateTime']))))
														{
															$hoursDiff=strtotime($arrAttendanceId[$cnt+1]['Punch_Time']) - strtotime($arrAttendanceId[$cnt+1]['Punch_Time']);
														}
														
														$workedHours=$hoursDiff/60/60;
														
														$updateAttendanceDetails = array('Employee_Id'     	=> $arrAttendanceId[$cnt+1]['Employee_Id'],
																						'Attendance_Id'   	=> $punchInAttExist[0]['Attendance_Id'],
																						'Attendance_Date'   => $attendanceActualDate,
																						'PunchIn_Date'   	=> date("Y-m-d", strtotime($punchInAttExist[0]['PunchInDateTime'])),
																						'PunchIn_Time'   	=> date("H:i:s", strtotime($punchInAttExist[0]['PunchInDateTime'])),
																						'PunchOut_Date'   	=> $arrAttendanceId[$cnt+1]['Punch_Date'],
																						'PunchOut_Time'   	=> $arrAttendanceId[$cnt+1]['Punch_Time'],
																						'Checkin_Data_Source'=>$punchInAttExist[0]['Checkin_Data_Source'],
																						'Checkout_Data_Source'=>$arrAttendanceId[$cnt+1]['Data_Source'],
																						'Total_Hours'     	=> round($workedHours,2),
																						'Approval_Status' 	=> 'Approved',
																						'Actual_Punch_In_Time'=>date("H:i:s", strtotime($punchInAttExist[0]['Actual_Punch_In_Time'])),
																						'Actual_PunchOut_Time'=>date("H:i:s", strtotime($punchInAttExist[0]['Actual_PunchOut_Time']))
																						);
														
														$invalidAttCOut = 0;
														// Based on work schedule configuration(Grace time,considration check-in time and check-out time) to check punchout valid or not 
														$invalidAttCOut = $this->getValidPunchOut($updateAttendanceDetails['Employee_Id'],$updateAttendanceDetails['PunchOut_Date'], $updateAttendanceDetails['PunchOut_Time'], $updateAttendanceDetails['PunchIn_Date'], $updateAttendanceDetails['PunchIn_Time'] );
														if( $invalidAttCOut == 1)
														{
															unset($updateAttendanceDetails);
														}								
													}
												}
												else //Add new attendance record
												{
													$empPunchoutDetails	= $attendanceData;
													
													$empPunchoutDetails['PunchOut_Date'] = $arrAttendanceId[$cnt+1]['Punch_Date'];
													$empPunchoutDetails['PunchOut_Time'] = $arrAttendanceId[$cnt+1]['Punch_Time'];
													$empPunchoutDetails['Checkout_Data_Source'] = $arrAttendanceId[$cnt+1]['Data_Source'];
													
													
													$empPunchoutDetails['PunchIn_Date'] = $empPunchoutDetails['PunchIn_Time'] = $empPunchoutDetails['Checkin_Data_Source'] = null;
													$empPunchoutDetails['Total_Hours'] =round(0,2);
													$empPunchoutDetails['Approval_Status'] = 'Draft';
													
													$invalidAttCOut = 0;
													$invalidAttCOut = $this->getValidPunchOut($empPunchoutDetails['Employee_Id'], $empPunchoutDetails['PunchOut_Date'], $empPunchoutDetails['PunchOut_Time'], $empPunchoutDetails['PunchIn_Time'] );
													if($invalidAttCOut == 1)
													{
														unset($empPunchoutDetails);
													}
												
												}
												
												//Punch In record details - no 24hrs record
												$attendanceData['PunchIn_Date'] = $arrAttendanceId[$cnt]['Punch_Date'];
												$attendanceData['PunchIn_Time'] = $arrAttendanceId[$cnt]['Punch_Time'];
												$attendanceData['Checkin_Data_Source'] = $arrAttendanceId[$cnt]['Data_Source'];
												
												$attendanceData['PunchOut_Date'] = $attendanceData['PunchOut_Time'] = $attendanceData['Checkout_Data_Source'] = null;
												$attendanceData['Total_Hours'] =round(0,2);
												$attendanceData['Approval_Status'] = 'Draft';

												$invalidAttCIn = 0;
												// Based on work schedule configuration(Grace time,considration check-in time and check-out time) to check punch in valid or not 
												$invalidAttCIn = $this->getValidPunchIn($attendanceData['Employee_Id'],$attendanceData['PunchIn_Date'],$attendanceData['PunchIn_Time'] );
												if($invalidAttCIn == 1)
												{
													unset($attendanceData);
												}	
												else
												{
													$empAttendanceArr[] = $attendanceData;
												}
												
												if(!empty($empPunchoutDetails))
													$empAttendanceArr[] = $empPunchoutDetails;
													
												if(!empty($updateAttendanceDetails))
													$updateAttendanceArr[] = $updateAttendanceDetails;
											}

											//invalid records 
											if($invalidAttCIn == 1)
											{
												$invalidAttendanceId[] = $arrAttendanceId[$cnt]['Attendance_Id'];
											}
											else
											{
												$validAttendanceId[] = $arrAttendanceId[$cnt]['Attendance_Id'];

											}
											if($invalidAttCOut == 1)
											{
												$invalidAttendanceId[] = $arrAttendanceId[$cnt+1]['Attendance_Id'];
											}
											else
											{
												$validAttendanceId[] = $arrAttendanceId[$cnt+1]['Attendance_Id'];
											}
										}
										else
										{
											$existAttendanceId[] = $arrAttendanceId[$cnt]['Attendance_Id'];
											$existAttendanceId[] = $arrAttendanceId[$cnt+1]['Attendance_Id'];
										}
									}
									else
									{
										$invalidAttendanceId[] = $arrAttendanceId[$cnt]['Attendance_Id'];
										$invalidAttendanceId[] = $arrAttendanceId[$cnt+1]['Attendance_Id'];
									}
									$cnt += 1;
								}
								else
								{
									$mispair = 1;
								}
							}
							else
							{
								$mispair = 1;
							}
							
							if($mispair == 1)
							{
								$qryStatusPair = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->attendanceStatus,
																							array('Status_PairA','Status_PairB'))
																->where('Schema_Id = ?', $arrAttendanceId[$cnt]['Schema_Id']));
								
								$attendanceStatusExist = 0;
								$addAttendanceDetails = $updateAttendanceDetails = array();
								
								foreach($qryStatusPair as $val)
								{
									if($val['Status_PairA'] == $arrAttendanceId[$cnt]['Attendance_Status'] || $val['Status_PairB'] == $arrAttendanceId[$cnt]['Attendance_Status'])
									{
										$isAttendanceRecordToAdd = 0;
										
										if($val['Status_PairB'] == $arrAttendanceId[$cnt]['Attendance_Status'])
										{
											$punchInDateTime = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendance, array(new Zend_Db_Expr('MAX(Concat(PunchIn_Date," ",PunchIn_Time)) as PunchInDateTime')))
																->where('Employee_Id = ?', $arrAttendanceId[$cnt]['Employee_Id'])
																->where('Attendance_Date = ?', $attendanceActualDate)
																->where(new Zend_Db_Expr('Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T")').' <= ?', $arrAttendanceId[$cnt]['Added_On'])
																->where('Approval_Status = ?', 'Draft'));

											if($punchInDateTime)
											{
												$punchInAttExist = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->attendance, array(new Zend_Db_Expr('Concat(PunchIn_Date," ",PunchIn_Time) as PunchInDateTime'),'Attendance_Id','Checkin_Data_Source'))
												->where('Employee_Id = ?', $arrAttendanceId[$cnt]['Employee_Id'])
												->where(new Zend_Db_Expr('Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T")').'= ?',$punchInDateTime)
												->where('Approval_Status = ?', 'Draft'));					
											}
											else
											{
												$punchInAttExist = array();
											}
												
											if($punchInAttExist)
											{
												if($punchInAttExist[0]['PunchInDateTime'] != '' || $punchInAttExist[0]['Attendance_Id'] > 0)
												{
													if (strtotime($arrAttendanceId[$cnt]['Punch_Date'] . ' ' . $arrAttendanceId[$cnt]['Punch_Time']) > strtotime($punchInAttExist[0]['PunchInDateTime']))
													{
														$hoursDiff=strtotime($arrAttendanceId[$cnt]['Punch_Date'] . ' ' . $arrAttendanceId[$cnt]['Punch_Time']) - strtotime($punchInAttExist[0]['PunchInDateTime']);
													}
													else if (strtotime($arrAttendanceId[$cnt]['Punch_Date']) == strtotime(date("Y-m-d", strtotime($punchInAttExist[0]['PunchInDateTime']))))
													{
														$hoursDiff=strtotime($arrAttendanceId[$cnt]['Punch_Time']) - strtotime($arrAttendanceId[$cnt]['Punch_Time']);
													}
													
													$workedHours=$hoursDiff/60/60;
													
													$punchinn = date("Y-m-d", strtotime($punchInAttExist[0]['PunchInDateTime'])).' '.date("H:i:s", strtotime($punchInAttExist[0]['PunchInDateTime']));
													$attendanceExist = $this->ckAttendanceExist($arrAttendanceId[$cnt]['Employee_Id'], strtotime($punchinn), strtotime($arrAttendanceId[$cnt]['Added_On']));
													
													if($attendanceExist == 0)
													{
														$updateAttendanceDetails = array('Employee_Id' 	=> $arrAttendanceId[$cnt]['Employee_Id'],
																					'Attendance_Id'   	=> $punchInAttExist[0]['Attendance_Id'],
																					'Attendance_Date'   => $attendanceActualDate,
																					'PunchIn_Date'   	=> date("Y-m-d", strtotime($punchInAttExist[0]['PunchInDateTime'])),
																					'PunchIn_Time'   	=> date("H:i:s", strtotime($punchInAttExist[0]['PunchInDateTime'])),
																					'Checkin_Data_Source'	=>$punchInAttExist[0]['Checkin_Data_Source'],
																					'Checkout_Data_Source' =>$arrAttendanceId[$cnt]['Data_Source'],
																					'PunchOut_Date'   	=> $arrAttendanceId[$cnt]['Punch_Date'],
																					'PunchOut_Time'   	=> $arrAttendanceId[$cnt]['Punch_Time'],
																					'Total_Hours'     	=> round($workedHours,2),
																					'Approval_Status' 	=> 'Approved');
														
														$invalidAttCOut = 0;
														// Based on work schedule configuration(Grace time,considration check-in time and check-out time) to check punchout valid or not 
														$invalidAttCOut = $this->getValidPunchOut($updateAttendanceDetails['Employee_Id'],$updateAttendanceDetails['PunchOut_Date'], $updateAttendanceDetails['PunchOut_Time'], $updateAttendanceDetails['PunchIn_Date'], $updateAttendanceDetails['PunchIn_Time'] );
														if($invalidAttCOut == 1)
														{
															unset($updateAttendanceDetails);
															$invalidAttendanceId[] = $arrAttendanceId[$cnt]['Attendance_Id'];
														}	

													} else {
														$existAttendanceId[] = $arrAttendanceId[$cnt]['Attendance_Id'];
													}
												}
											}
											else
											{
												$isAttendanceRecordToAdd = 1;
											}
										}
										else
										{
											$isAttendanceRecordToAdd = 1;
										}
										
										if($isAttendanceRecordToAdd == 1)
										{
											$addAttendanceDetails = array('Employee_Id'         => $arrAttendanceId[$cnt]['Employee_Id'],
																		'Location_Id'         => $arrAttendanceId[$cnt]['Location_Id'],
																		'Exclude_Break_Hours' => $arrAttendanceId[$cnt]['Exclude_Break_Hours'],
																		'Attendance_Date'     => $attendanceActualDate,
																		'Approval_Status'     => 'Draft',
																		'Approver_Id'         => $approverId,
																		'Added_By'            => $logEmpId,
																		'Added_Date'          => date('Y-m-d H:i:s'),
																		'Total_Hours'         => round(0,2));
											
											if($val['Status_PairA'] == $arrAttendanceId[$cnt]['Attendance_Status'])
											{
												$addAttendanceDetails['PunchIn_Date'] = $arrAttendanceId[$cnt]['Punch_Date'];
												$addAttendanceDetails['PunchIn_Time'] = $arrAttendanceId[$cnt]['Punch_Time'];
												$addAttendanceDetails['Checkin_Data_Source'] = $arrAttendanceId[$cnt]['Data_Source'];
												$addAttendanceDetails['PunchOut_Time'] = $addAttendanceDetails['PunchOut_Date'] = $addAttendanceDetails['Checkout_Data_Source'] = null;
												
												$invalidAttCIn = 0;
												// Based on work schedule configuration(Grace time,considration check-in time and check-out time) to check punchin valid or not 
												$invalidAttCIn = $this->getValidPunchIn($addAttendanceDetails['Employee_Id'],$addAttendanceDetails['PunchIn_Date'],$addAttendanceDetails['PunchIn_Time'] );
												if($invalidAttCIn == 1)
												{
													unset($addAttendanceDetails);
													$invalidAttendanceId[] = $arrAttendanceId[$cnt]['Attendance_Id'];	
												}
											}
											else
											{
												$addAttendanceDetails['PunchOut_Time'] = $arrAttendanceId[$cnt]['Punch_Time'];
												$addAttendanceDetails['PunchOut_Date'] = $arrAttendanceId[$cnt]['Punch_Date'];
												$addAttendanceDetails['Checkout_Data_Source'] = $arrAttendanceId[$cnt]['Data_Source'];
												$addAttendanceDetails['PunchIn_Time'] = $addAttendanceDetails['PunchIn_Date'] = $addAttendanceDetails['Checkin_Data_Source'] = null;
												
												$invalidAttCOut = 0;
												// Based on work schedule configuration(Grace time,considration check-in time and check-out time) to check punchout valid or not 
												$invalidAttCOut = $this->getValidPunchOut($addAttendanceDetails['Employee_Id'],$addAttendanceDetails['PunchOut_Date'], $addAttendanceDetails['PunchOut_Time'], $addAttendanceDetails['PunchIn_Time'] );
												if($invalidAttCOut == 1)
												{
													unset($addAttendanceDetails);
													$invalidAttendanceId[] = $arrAttendanceId[$cnt]['Attendance_Id'];
												}

											}
											
											$attendanceStatusExist = 1;
											break;
										}
										
									}
								}
								
								if($attendanceStatusExist && !empty($addAttendanceDetails))
								{
									if($addAttendanceDetails['PunchIn_Date'] != null)
									{
										$attendanceExist = $this->ckAttendanceExist($arrAttendanceId[$cnt]['Employee_Id'], NULL, strtotime($arrAttendanceId[$cnt]['Added_On']));
									}
									else
									{
										$attendanceExist = $this->ckAttendanceExist($arrAttendanceId[$cnt]['Employee_Id'], strtotime($arrAttendanceId[$cnt]['Added_On']), NULL);
									}
									
									if($attendanceExist == 0)
									{
										$empAttendanceArr[] = $addAttendanceDetails;
										$validAttendanceId[] = $arrAttendanceId[$cnt]['Attendance_Id'];
									}
									else
									{
										$existAttendanceId[] = $arrAttendanceId[$cnt]['Attendance_Id'];
									}
								}
								elseif(!empty($updateAttendanceDetails))
								{
									$updateAttendanceArr[] = $updateAttendanceDetails;
									$validAttendanceId[] = $arrAttendanceId[$cnt]['Attendance_Id'];
								}
								else
								{
									if(empty($existAttendanceId))
									$invalidAttendanceId[] = $arrAttendanceId[$cnt]['Attendance_Id'];
								}
								
							}

						}
					}
					else 
					{
						/*Get the attendance import record based on employee id and added on */
						$qryAttendanceImportId = $this->_db->select()->from(array('AI'=>$this->_ehrTables->attendanceImport),array('Attendance_Id','Added_On'))
																									->where('Employee_Id = ?', $arrAttendanceId[$cnt]['Employee_Id'])
																									->where('AI.Added_On = ?', $arrAttendanceId[$cnt]['Added_On']);
								
						$getAttendanceImportId = $this->_db->fetchAll($qryAttendanceImportId);
						
						/*This function help us to find the employee is associated with shift roaster or workschedule*/
						$checkShiftEnabled=$this->checkShiftEnabled($arrAttendanceId[$cnt]['Employee_Id']);
						
						foreach ($getAttendanceImportId as $key => $attendanceImportDetails) 
						{
							$graceTimeDetails=$this->getCurrentWorkScheduleDetails($arrAttendanceId[$cnt]['Employee_Id'],$attendanceImportDetails['Added_On']);
							
							/*when the grace time details returned as empty based on the checkShiftEnabled update the error code in attendance import table*/
							if(empty($graceTimeDetails))
							{
								$whereAttendanceImport['Attendance_Id IN (?)'] = $attendanceImportDetails['Attendance_Id'];
								if($checkShiftEnabled=='Shift Roster')
								{
									$this->_db->update($this->_ehrTables->attendanceImport, array('Rollup_Flag'=>-1,'Error_Code'=>'EAI003','Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$logEmpId), $whereAttendanceImport);
									$invalidAttendanceId[] = $arrAttendanceId[$cnt]['Attendance_Id'];
								}
								else 
								{
									$this->_db->update($this->_ehrTables->attendanceImport, array('Rollup_Flag'=>-1,'Error_Code'=>'EAI002','Updated_On'=>date('Y-m-d H:i:s'),'Updated_By'=>$logEmpId), $whereAttendanceImport);
									$invalidAttendanceId[] = $arrAttendanceId[$cnt]['Attendance_Id'];
								}
								
							}
							
						}
					}
   				}
			}
		}
		
		return array('add'=>$empAttendanceArr, 'updateValid'=>$validAttendanceId, 'updateInvalid'=>$invalidAttendanceId, 'exist'=> $existAttendanceId,
					 'update' => $updateAttendanceArr);
	}

	/**
	 * Check whether attendance exists or not for a given employeeId , punchIn , punchOut.
	 */
	public function ckAttendanceExist($employeeId, $punchIn, $punchOut)
	{
		//New punchin or puchout or both should not lies between old punchin and punchout
		//old punchin or punchout or both should not lies between new punchin and punchout
		
			if(!empty($punchIn))
			$punchIn= date('Y-m-d H:i:s',$punchIn);
			if(!empty($punchOut))
			$punchOut= date('Y-m-d H:i:s',$punchOut);
			
			if($punchIn !== '' && $punchIn !== null && !empty($punchIn) && (empty($punchOut) || $punchOut == '' || $punchOut == null)){
				$qryCondition = $this->_db->quoteInto(new Zend_Db_Expr(' ? BETWEEN Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T") AND Date_Format(Concat(PunchOut_Date," ",PunchOut_Time), "%Y-%m-%d %T")'), $punchIn);
			} elseif($punchOut !== '' && $punchOut !== null && !empty($punchOut) && (empty($punchIn) || $punchIn == '' || $punchIn == null)) {
				$qryCondition = $this->_db->quoteInto(new Zend_Db_Expr(' ? BETWEEN Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T") AND Date_Format(Concat(PunchOut_Date," ",PunchOut_Time), "%Y-%m-%d %T")'), $punchOut);
			} else{
				$qryCondition = $this->_db->quoteInto(new Zend_Db_Expr(' ? BETWEEN Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T") AND Date_Format(Concat(PunchOut_Date," ",PunchOut_Time), "%Y-%m-%d %T")'), $punchIn)
									.' or '. $this->_db->quoteInto(new Zend_Db_Expr(' ? BETWEEN Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T") AND Date_Format(Concat(PunchOut_Date," ",PunchOut_Time), "%Y-%m-%d %T")'), $punchOut)
									.' or '.$this->_db->quoteInto(new Zend_Db_Expr(' Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T") BETWEEN ?'),$punchIn)
									.$this->_db->quoteInto(new Zend_Db_Expr('AND ?'), $punchOut)
									.' or '.$this->_db->quoteInto(new Zend_Db_Expr(' Date_Format(Concat(PunchOut_Time," ",PunchOut_Time), "%Y-%m-%d %T") BETWEEN ?'),$punchIn)
									.$this->_db->quoteInto(new Zend_Db_Expr('AND ?'), $punchOut);
			}
			
			$qryAttendanceCnt = $this->_db->select()->from(array('A'=>$this->_ehrTables->attendance), array('Count(Attendance_Id)'))
			->where('Employee_Id = ?', $employeeId)->where('Approval_Status != "Draft" and Approval_Status != "Rejected"')
			->where($qryCondition);
			
			
			$exists = $this->_db->fetchOne($qryAttendanceCnt);
		
		return $exists;
	}
	
	//Delete attendance import data
	public function deleteAttendanceimportData($attendanceId, $logEmpId)
	{
		$deleted = 0;
		
		$attendanceimportDataRow = $this->_db->fetchRow($this->_db->select()
										   ->from($this->_ehrTables->attendanceImport, array('*'))
										   ->where('Attendance_Id = ?', $attendanceId));
		
		if ($attendanceimportDataRow['Lock_Flag'] == 0)
		{
			$attendanceimportDataRow['Deleted_On'] = date('Y-m-d H:i:s');
			$attendanceimportDataRow['Deleted_By'] = $logEmpId;
			$inserted = $this->_db->insert($this->_ehrTables->archiveAttendanceImport, $attendanceimportDataRow);
			$deleted = $this->_db->delete($this->_ehrTables->attendanceImport,'Attendance_Id='.$attendanceId);
		}
		
		return $this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
														'lockFlag'       => $attendanceimportDataRow['Lock_Flag'],
														'formName'       => 'Attendance Import Data',
														'trackingColumn' => $attendanceId,
														'sessionId'      => $logEmpId));
    }

	/**
	 * Check whether schema exists or not for a given shema title
	 */
	public function schemaExist($schemaTitle)
	{
		return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendanceHeader, new Zend_Db_Expr('COUNT(Schema_Id)'))
				->where('Schema_Name LIKE ?', $schemaTitle));
	}

	/**
	 * Check whether schema exists or not for a given shema header
	 */
	public function attendanceSchemaExist($schemaHeader)
	{
		if(!empty($schemaHeader) && count($schemaHeader)>0)
		{
			$qryAttHeader = $this->_db->select()->from($this->_ehrTables->attendanceHeader, array(new Zend_Db_Expr('COUNT(Schema_Id)')))
			->where('Department = ?', $schemaHeader['Department'])->where('Employee_Name = ?', $schemaHeader['Employee_Name'])
			->where('Employee_Id = ?', $schemaHeader['Employee_Id'])->where('Added_On = ?', $schemaHeader['Added_On'])
			->where('Attendance_Status = ?', $schemaHeader['Attendance_Status'])->where('Location_Id = ?', $schemaHeader['Location_Id'])
			->where('Id_Number = ?', $schemaHeader['Id_Number'])->where('Verify_Code = ?', $schemaHeader['Verify_Code'])
			->where('Card_No = ?', $schemaHeader['Card_No'])->where('WorkSchedule_Id = ?', $schemaHeader['WorkSchedule_Id']);
			$cntAttHeader = $this->_db->fetchOne($qryAttHeader);
			return $cntAttHeader;
			 
		}
	}

	//getting whether leave applied on punchin date
	public function getLeaveApplied($employeeId, $punchInDate)
	{
		$orWhereQry = $this->_db->quoteInto('Start_Date <= ? AND ',date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($punchInDate))))
		.$this->_db->quoteInto('End_Date >= ?', date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($punchInDate))));
		 
		$qryLeaveApplied = $this->_db->select()->from(array('L'=>$this->_ehrTables->empLeaves), array('Count(Leave_Id)'))
		->where('L.Approval_Status != ?', 'Rejected')
		->where('L.Approval_Status != ?', 'Cancelled')
		->where('L.Duration = ?', 1)
		->where('L.Employee_Id = ?', $employeeId)
		->Where($orWhereQry);

		return $this->_db->fetchOne($qryLeaveApplied);
	}

	
	/* V0.4 user specified date format in attendance report*/
	public function attendanceDateFormat($schemaId)
	{
		return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendanceHeader, 'Date_Format')
				->where('Schema_Id = ?', $schemaId));
	}
	
	
	/********************************************* Attendance Settings ********************************************************/

	/**
	 * to list and search attendance settings
	 */
	public function listAttendanceSettings($page, $rows, $sortField, $sortOrder, $searchAll=null, $searchArr)
    {
		$workScheduleId            = $searchArr['workScheduleId'];
		$delayedEntryStart         = $searchArr['delayedEntryStart'];
		$delayedEntryEnd           = $searchArr['delayedEntryEnd'];
		$delayedEntryUptoStart     = $searchArr['delayedEntryUptoStart'];
		$delayedEntryUptoEnd       = $searchArr['delayedEntryUptoEnd'];
		$leaveType                 = $searchArr['leaveType'];
		$delayedEntryMaxLimitStart = $searchArr['delayedEntryMaxLimitStart'];
		$delayedEntryMaxLimitEnd   = $searchArr['delayedEntryMaxLimitEnd'];
		$maxLimitPeriod            = $searchArr['maxLimitPeriod'];
		$totalDaysStart            = $searchArr['totalDaysStart'];
		$totalDaysEnd              = $searchArr['totalDaysEnd'];
		$status                    = $searchArr['status'];
		
		/**
		 *	Sorting columns based on display column order in grid
		*/
		switch ($sortField)
		{
			case 1: $sortField = 'AS.Configuration_Type'; break;
			case 2: $sortField = 'AS.Title'; break;
			case 3: $sortField = 'AS.Group_Name'; break;
			case 5: $sortField = 'AS.Alternate_LeaveType_Name'; break;
			case 6: $sortField = 'AS.Attendance_Settings_Status'; break;
			default: $sortField = 'WS.Configuration_Type'; break;
		}
		

		/**
		 *	Query to fetch data from various tables
		*/
        $qryAttendanceSettings = $this->_db->select()
								->from(array('AS'=>$this->_ehrTables->attendanceSettings),
									   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS AS.Attendance_Settings_Id as count'),
											 'AS.Attendance_Settings_Id', 'AS.Delayed_Entry_From', 'AS.Delayed_Entry_Upto', 'AS.Delayed_Entry_Maximum_Limit',
											 'AS.Delayed_Entry_Maximum_Limit_Period', 'AS.Total_Days', 'AS.Attendance_Settings_Status', 'AS.Shift_Margin',
											 'AS.Enable_Notification','AS.Configuration_Type','AS.Shortage_From_For_Full_Day_Leave','AS.Shortage_To_For_Full_Day_Leave',
											 'AS.Shortage_From_For_Half_Day_Leave','AS.Shortage_To_For_Half_Day_Leave','AS.Shortage_From_For_Quarter_Day_Leave','AS.Shortage_To_For_Quarter_Day_Leave',
											 'AS.Custom_Group_Id','AS.Late_Attendance_Leave_Frequency','AS.Auto_Short_Time_Off',
											 'totalDays' => new Zend_Db_Expr("CASE 
																WHEN AS.Total_Days = 1.00 THEN 'Full Day'
																WHEN AS.Total_Days = 0.50 THEN 'Half Day'
																WHEN AS.Total_Days = 0.25 THEN 'Quarter Day'
																ELSE 'Invalid'
															END
														"),
											 new Zend_Db_Expr("DATE_FORMAT(AS.Added_On,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
											 new Zend_Db_Expr("DATE_FORMAT(AS.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On")))
								
								->joinLeft(array('WS'=>$this->_ehrTables->workSchedule),'AS.WorkSchedule_Id=WS.WorkSchedule_Id',
											array('WS.Title', 'WS.WorkSchedule_Id'))

								->joinLeft(array('CEG'=>$this->_ehrTables->customEmployeeGroup),'AS.Custom_Group_Id=CEG.Group_Id',
											array('CEG.Group_Name'))
								
								->joinLeft(array('LT1'=>$this->_ehrTables->leavetype),'AS.Alternate_LeaveType_Id=LT1.LeaveType_Id',
											array('LT1.Leave_Name as Alternate_LeaveType_Name', 'LT1.LeaveType_Id as Alternate_LeaveType_Id'))
								
								->joinInner(array('empPersonal1'=>$this->_ehrTables->empPersonal),'AS.Added_By=empPersonal1.Employee_Id',
											array(new Zend_Db_Expr("CONCAT(empPersonal1.Emp_First_Name, ' ', empPersonal1.Emp_Last_Name) as Added_By_Name")))
								
								->joinLeft(array('empPersonal2'=>$this->_ehrTables->empPersonal),'AS.Updated_By=empPersonal2.Employee_Id',
											array(new Zend_Db_Expr("CONCAT(empPersonal2.Emp_First_Name, ' ', empPersonal2.Emp_Last_Name) as Updated_By_Name")))
								
								->order("$sortField $sortOrder")
								->limit($rows, $page);
									
		if($sortField == 'AS.Delayed_Entry_Maximum_Limit')
		{
			$qryAttendanceSettings->order(new Zend_Db_Expr("FIELD(AS.Delayed_Entry_Maximum_Limit_Period, 'Per Month','Per Year') ASC") );
		}
		
		/**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			$conditions  = $this->_db->quoteInto('WS.Title  Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or AS.Delayed_Entry_From Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or AS.Delayed_Entry_Upto Like ?', "%$searchAll%");
			// $conditions .= $this->_db->quoteInto('or LT.LeaveType_Name Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or AS.Delayed_Entry_Maximum_Limit Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or AS.Delayed_Entry_Maximum_Limit_Period Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or AS.Total_Days Like ?', "%$searchAll%");
			
			$qryAttendanceSettings->where($conditions);		
		}
		
		if ($workScheduleId != '' && $workScheduleId != null && preg_match('/^[0-9]/', $workScheduleId))
		{
			$qryAttendanceSettings->where($this->_db->quoteInto('AS.WorkSchedule_Id = ?', $workScheduleId));
		}
		
		if (($delayedEntryStart >=0) && preg_match('/^[0-9*\.]/', $delayedEntryStart) && !empty($delayedEntryStart))
		{
		    $qryAttendanceSettings->where($this->_db->quoteInto('AS.Delayed_Entry_From >= ?', $delayedEntryStart));
		}
		
		if (($delayedEntryEnd >=0) && preg_match('/^[0-9*\.]/', $delayedEntryEnd) && !empty($delayedEntryEnd))
		{
		    $qryAttendanceSettings->where($this->_db->quoteInto('AS.Delayed_Entry_From <= ?', $delayedEntryEnd));
		}

		if (($delayedEntryUptoStart >=0) && preg_match('/^[0-9*\.]/', $delayedEntryUptoStart) && !empty($delayedEntryUptoStart))
		{
		    $qryAttendanceSettings->where($this->_db->quoteInto('AS.Delayed_Entry_Upto >= ?', $delayedEntryUptoStart));
		}
		
		if (($delayedEntryUptoEnd >=0) && preg_match('/^[0-9*\.]/', $delayedEntryUptoEnd) && !empty($delayedEntryUptoEnd))
		{
		    $qryAttendanceSettings->where($this->_db->quoteInto('AS.Delayed_Entry_Upto <= ?', $delayedEntryUptoEnd));
		}
		
		if (($delayedEntryMaxLimitStart >=0) && preg_match('/^[0-9*\.]/', $delayedEntryMaxLimitStart) && !empty($delayedEntryMaxLimitStart))
		{
		    $qryAttendanceSettings->where($this->_db->quoteInto('AS.Delayed_Entry_Maximum_Limit >= ?', $delayedEntryMaxLimitStart));
			$qryAttendanceSettings->where($this->_db->quoteInto('AS.Delayed_Entry_Maximum_Limit_Period Like ?', "%$maxLimitPeriod%"));
		}
		
		if (($delayedEntryMaxLimitEnd >=0) && preg_match('/^[0-9*\.]/', $delayedEntryMaxLimitEnd) && !empty($delayedEntryMaxLimitEnd))
		{
		    $qryAttendanceSettings->where($this->_db->quoteInto('AS.Delayed_Entry_Maximum_Limit <= ?', $delayedEntryMaxLimitEnd));
			$qryAttendanceSettings->where($this->_db->quoteInto('AS.Delayed_Entry_Maximum_Limit_Period Like ?', "%$maxLimitPeriod%"));
		}
		
		if ($leaveType != '' && $leaveType != null && preg_match('/^[0-9]/', $leaveType))
		{
			$qryAttendanceSettings->where($this->_db->quoteInto('AS.LeaveType_Id = ?', $leaveType));
		}
		
		if (($totalDaysStart >=0) && preg_match('/^[0-9*\.]/', $totalDaysStart) && !empty($totalDaysStart))
		{
		    $qryAttendanceSettings->where($this->_db->quoteInto('AS.Total_Days >= ?', $totalDaysStart));
		}
		
		if (($totalDaysEnd >=0) && preg_match('/^[0-9*\.]/', $totalDaysEnd) && !empty($totalDaysEnd))
		{
		    $qryAttendanceSettings->where($this->_db->quoteInto('AS.Total_Days <= ?', $totalDaysEnd));
		}
		
		if (! empty($status))
		{
			$qryAttendanceSettings->where('AS.Attendance_Settings_Status LIKE ?',$status);
		}
		
		$queryAttendanceSettings = $this->_db->fetchAll($qryAttendanceSettings);

		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');

		//multiselect
		foreach ($queryAttendanceSettings as $key => $row)
		{
			 //multiselect for combo box
            $queryAttendanceSettings[$key]['LeaveType_Id'] = implode(",", $this->_db->fetchCol($this->_db->select()
																				->from(array('lt'=>$this->_ehrTables->lateAttendanceLeaveTypes),
																					   array(''))
																				
																				->joinInner(array('le'=>$this->_ehrTables->leavetype),
																							'le.LeaveType_Id = lt.LeaveType_Id',
																							array('LeaveType_Id'))
																				
																				->where('lt.Attendance_Settings_Id = ?', $row['Attendance_Settings_Id'])));
			
			// //view form -leave type Name
            $queryAttendanceSettings[$key]['Leave_Name'] = implode(",", $this->_db->fetchCol($this->_db->select()
																					 ->from(array('lt'=>$this->_ehrTables->lateAttendanceLeaveTypes),
																							array(''))
																					 
																					 ->joinInner(array('le'=>$this->_ehrTables->leavetype),
																								 'le.LeaveType_Id=lt.LeaveType_Id',
																								 array('Leave_Name'))
																					 
																					 ->where('lt.Attendance_Settings_Id = ?', $row['Attendance_Settings_Id'])));		
			
		}
		
		/* Total data set length */
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendanceSettings,
																  new Zend_Db_Expr('COUNT(Attendance_Settings_Id)')));
		
		// $arrayAttendanceSettings = array();
		// foreach($queryAttendanceSettings as $key =>$value)
		// {
		// 	if($value['Attendance_Settings_Id'] != 0)
		// 		$queryAttendanceSettings[$key] = $value;
		// }
		
		/**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $queryAttendanceSettings);
	}
	
	/**
	 *	Update attendance settings details in attendance settings table and update system log too
    */
	public function updateAttendanceSettings($attendanceSettingArray, $leaveTypesArray, $notificationArray, $sessionId, $formName)
	{
		$qryAttendanceSettings = $this->_db->select()->from(array('AS'=>$this->_ehrTables->attendanceSettings),
															new Zend_Db_Expr('Count(Attendance_Settings_Id) as totalAttendanceId'))
		
									->joinInner(array('WS'=>$this->_ehrTables->workSchedule),'AS.WorkSchedule_Id=WS.WorkSchedule_Id',
												array('WS.Title'))
								
									->where('AS.WorkSchedule_Id = ?', $attendanceSettingArray['WorkSchedule_Id'])
									->where('AS.Attendance_Settings_Status = "Active"');
		
		if (!empty($attendanceSettingArray['Attendance_Settings_Id']))
		{
			$qryAttendanceSettings->where('Attendance_Settings_Id != ?', $attendanceSettingArray['Attendance_Settings_Id']);
		}
		
        $isExist = $this->_db->fetchRow($qryAttendanceSettings);
		
		if ($isExist['totalAttendanceId'] == 0)
		{
			if (!empty($attendanceSettingArray['Attendance_Settings_Id']))
			{
				$action = 'Edit';
				
				$attendanceSettingArray['Updated_On'] = date('Y-m-d H:i:s');
				$attendanceSettingArray['Updated_By'] = $sessionId;
				
				$updated = $this->_db->update($this->_ehrTables->attendanceSettings, $attendanceSettingArray,
											  array('Attendance_Settings_Id = '. $attendanceSettingArray['Attendance_Settings_Id']));
				
				$attendanceSettingsId = $attendanceSettingArray['Attendance_Settings_Id'];
				$positionCount=0;
				// updating multi select leave types to the table
				if(count($leaveTypesArray) > 0)
				{
					foreach($leaveTypesArray as $leaveTypeId)
					{
						if($leaveTypeId != 0)
						{
							$positionCount++;
							$leaveTypes[] = array('Attendance_Settings_Id'=>$attendanceSettingsId,'LeaveType_Id'=>$leaveTypeId,'Position'=>$positionCount);
							$leaveDel=$this->_db->delete($this->_ehrTables->lateAttendanceLeaveTypes, 'Attendance_Settings_Id='.(int)$attendanceSettingsId,'LeaveType_Id='.(int)$leaveTypeId, 'Position='.(int)$positionCount);
						}
					}
						$leaveUpd=$this->_ehrTables->insertMultiple($this->_ehrTables->lateAttendanceLeaveTypes, $leaveTypes);
				}
			}
			else
			{
				$action = 'Add';
				
				$attendanceSettingArray['Added_On'] = date('Y-m-d H:i:s');
				$attendanceSettingArray['Added_By'] = $sessionId;
				
				$updated = $this->_db->insert($this->_ehrTables->attendanceSettings, $attendanceSettingArray);
				
				if($updated)
					$attendanceSettingsId = $this->_db->lastInsertId();
				
				$positionCount=0;
				//adding multi select leave types 
				if(count($leaveTypesArray) > 0)
				{
					foreach($leaveTypesArray as $leaveTypeId)
					{
						if($leaveTypeId != 0)
						{
							$positionCount++;
							$leaveTypes[] = array('Attendance_Settings_Id'=>$attendanceSettingsId,'LeaveType_Id'=>$leaveTypeId,'Position'=>$positionCount);
						}
					}
					$leaveAdd=$this->_ehrTables->insertMultiple($this->_ehrTables->lateAttendanceLeaveTypes, $leaveTypes);
				}
			}
			
			if($updated)
			{
				if (!empty($attendanceSettingsId) && $attendanceSettingsId != 0)
				{
					$this->_db->delete($this->_ehrTables->lateAttendanceNotificationPerson,'Attendance_Settings_Id='.$attendanceSettingsId);
					
					if(!empty($notificationArray['Employee_Id']) && $attendanceSettingArray['Enable_Notification'] == 1)
					{
						foreach($notificationArray['Employee_Id'] as $employeeId)
						{
							$notifyArray[] = array('Employee_Id' => $employeeId,
												   'Attendance_Settings_Id'   => $attendanceSettingsId);
						}
						 
						$this->_ehrTables->insertMultiple($this->_ehrTables->lateAttendanceNotificationPerson, $notifyArray);
					}
				}
			}
			
			

			/*
			 *	this function will handle
			 *		update system log function
			 *		clear submit lock fucntion
			 *		return success/failure array
			*/
			return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
															'action'         => $action,
															'trackingColumn' => $isExist['Title'],
															'formName'       => $formName,
															'sessionId'      => $sessionId,
															'tableName'      => $this->_ehrTables->attendanceSettings));
		}
		else
		{
			return array('success' => false, 'msg'=>$formName.' already exist in active status', 'type'=>'info');
		}
	}
	
	//Delete attendance settings
	public function deleteAttendanceSettings($attendanceSettingsId, $logEmpId, $formName)
	{
		$deleted = 0;
		
		$attendanceSettingsRow = $this->_db->fetchRow($this->_db->select()
										   ->from($this->_ehrTables->attendanceSettings, array('Lock_Flag'))
										   ->where('Attendance_Settings_Id = ?', $attendanceSettingsId));
		
		if ($attendanceSettingsRow['Lock_Flag'] == 0)
		{
			$deleted = $this->_db->delete($this->_ehrTables->attendanceSettings,'Attendance_Settings_Id='.$attendanceSettingsId);
			$deleteLeaveTypes =	$this->_db->delete($this->_ehrTables->lateAttendanceLeaveTypes, 'Attendance_Settings_Id='.(int)$attendanceSettingsId);

			if($deleted && $deleteLeaveTypes)
				$this->_db->delete($this->_ehrTables->lateAttendanceNotificationPerson,'Attendance_Settings_Id='.$attendanceSettingsId);
		}
		
		/**
		 *	delete activity for common function
		 *		1)check lock is exist or not.
		 *			If lock is exist then show error message like employee is open record for update.
		 *		2)If No lockflag then process delete activity
		 *		3)Update delete activity in system log
		 *		4)return success/failure message
		*/
		return $this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
														'lockFlag'       => $attendanceSettingsRow['Lock_Flag'],
														'formName'       => $formName,
														'trackingColumn' => $attendanceSettingsId,
														'sessionId'      => $logEmpId));
    }
	
	public function checkLateAttendance($employeeId, $attendancePunchInDate, $attendancePunchInTime,$shortTimeOffConfiguration=NULL)
	{
		$lateAttendance 	   	   	  = 0;
		$lateAttendanceHours   	   	  = new Zend_Db_Expr('NULL');
		$lateAttendanceHoursFromGrace = new Zend_Db_Expr('NULL');
		$alwaysGraceTime			  = new Zend_Db_Expr('NULL');
		$lateAttendanceDetails 	   	  = array();
		$regularFromTime 			  = '';
		// $attendancePunchInDateTime    = date("Y-m-d H:i:s",strtotime($attendancePunchInDate.' '.$attendancePunchInTime));
		$attendancePunchInDateTime    = date("Y-m-d H:i:00", strtotime($attendancePunchInDate . ' ' . $attendancePunchInTime));
		$empGraceTimeDetails          = $this->getCurrentWorkScheduleDetails($employeeId,$attendancePunchInDateTime);
		$attendanceSettingsDetails    = array();
		$autoShortTimeOff 			  = 'No';
		if(!empty($empGraceTimeDetails))
		{
			$checkShiftExist =  $this->checkShiftEnabled($employeeId); 
		
			$regularFromTime = $empGraceTimeDetails['Regular_From'];
			$startDate = date('Y-m-d', strtotime($empGraceTimeDetails['Regular_From']));

			if($checkShiftExist=='Shift Roster')
			{
				$businessWorkingDays = $this->_dbPayslip->getBusinessWorkingDays($startDate, $startDate, $employeeId,NULL,1,'leaves');
				$workScheduleId = $this->_db->fetchOne($this->_db->select()->from(array('J'=>$this->_ehrTables->empJob), array('J.Work_Schedule'))
																									->where('Employee_Id = ?', $employeeId));
			}
			else 
			{
				$businessWorkingDays = $this->_dbPayslip->getBusinessWorkingDays($startDate, $startDate, $employeeId,'',1);
				$workScheduleId = $empGraceTimeDetails['WorkSchedule_Id'];
			}

			if($businessWorkingDays > 0)
			{
				$dbHrReports    = new Reports_Model_DbTable_HrReports();
				// conditions for checking => if short time off exists for the late duration 
				// short time off start date time should lie before or equal to the work schedule start time 
				// short time off end date time should lie after or equal to the attendance punch in time
				$empGraceTimeDetails['Regular_To'] = $attendancePunchInDateTime;
				$isShortTimeOffApplied 			   = $dbHrReports->getShortTimeOff($employeeId,$empGraceTimeDetails,'lateAttendance');									
				$isLeaveApplied                    = $dbHrReports->getLeaveDuration($employeeId,$startDate,0.5,'First Half','dashboardNoAttendance',1);
				$isCompOffApplied                  = $dbHrReports->getCompensatoryOff($employeeId,$startDate,0.5,'First Half','dashboardNoAttendance');

				//Check whether leave,comp off is applied on first half of that punchin date and check if any short time off exists
				if($isLeaveApplied == 0 && $isShortTimeOffApplied ==0 && $isCompOffApplied ==0)
				{
					$attendanceSettingsDetails = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->attendanceSettings,
																						array('Delayed_Entry_From', 'Delayed_Entry_Upto','Auto_Short_Time_Off','Delayed_Entry_Maximum_Limit','Delayed_Entry_Maximum_Limit_Period','Late_Attendance_Leave_Frequency'))
											->where('WorkSchedule_Id = ?', $workScheduleId)
											->where('Configuration_Type = ?','Late Attendance')
											->where('Attendance_Settings_Status = ?', 'Active'));
					if(!empty($attendanceSettingsDetails))
					{
						$isAttendanceExist = $dbHrReports->getAttendanceCount($employeeId,$empGraceTimeDetails, 'lateAttendance'); 

						//If attendance does not exist for the day 
						if($isAttendanceExist == 0){ 
							$delayedEntryFromTime = date('Y-m-d H:i:s',strtotime('+'.$attendanceSettingsDetails["Delayed_Entry_From"].'minutes',strtotime($empGraceTimeDetails['Regular_From']))); 
							$delayedEntryToTime = date('Y-m-d H:i:s',strtotime('+'.$attendanceSettingsDetails["Delayed_Entry_Upto"].'minutes',strtotime($empGraceTimeDetails['Regular_From']))); 

							if(strtotime($attendancePunchInDateTime) > strtotime($delayedEntryToTime)){
								//If the attendance punch in time is greater than maximum delayed entry time then late attendance leave has to be applied
								$lateAttendance = 2;
								$lateAttendanceHours = $this->calculateLateAttendanceHours($attendancePunchInDateTime,$empGraceTimeDetails['Regular_From']);
								$lateAttendanceHoursFromGrace = $this->calculateLateAttendanceHours($attendancePunchInDateTime,$delayedEntryFromTime);
								$alwaysGraceTime = $this->calculateLateAttendanceHours($delayedEntryFromTime,$empGraceTimeDetails['Regular_From']);

							}else if((strtotime($attendancePunchInDateTime) > strtotime($delayedEntryFromTime))
							&& (strtotime($attendancePunchInDateTime) <= strtotime($delayedEntryToTime))){
								//If the attendance punch in time falls in between the delayed entry time or inside the grace time
								if($attendanceSettingsDetails['Delayed_Entry_Maximum_Limit_Period'] == 'Per Month')
								{
									$punchInMonth = date("m", strtotime($startDate));// get month from the punch-in date
									$punchInYear  = date("Y", strtotime($startDate));// get year from the punch-in date
									/* Get the paycycle start and end date for the punch in month and year */
									$paycyledate = $this->_dbPayslip->getSalaryDateRange($punchInMonth,$punchInYear,strtotime($startDate));
									$lateAttendanceFrom   = $paycyledate['Salary_Date'];
									$lateAttendanceTo     = $paycyledate['Last_SalaryDate'];
								}
								elseif($attendanceSettingsDetails['Delayed_Entry_Maximum_Limit_Period'] == 'Per Year')
								{
									$yearRange = $this->_dbCommonFun->getDateRangeBasedOnDuration($startDate,$attendanceSettingsDetails['Delayed_Entry_Maximum_Limit_Period']);
									$lateAttendanceFrom   = $yearRange['From_Date'];
									$lateAttendanceTo     = $yearRange['To_Date'];
								}

								$lateAttendance = 1;
								$empTotalLateAttendance = $this->getLateAttendanceCount($employeeId,$lateAttendanceFrom,$lateAttendanceTo,array($lateAttendance));
								
								if($empTotalLateAttendance >= $attendanceSettingsDetails['Delayed_Entry_Maximum_Limit'])
								{
									$lateAttendance 			  = $this->getLateAttendanceLeaveFrequency($employeeId,$lateAttendanceFrom,$lateAttendanceTo,$attendanceSettingsDetails);
									$lateAttendanceHours 		  = $this->calculateLateAttendanceHours($attendancePunchInDateTime,$empGraceTimeDetails['Regular_From']);
									$lateAttendanceHoursFromGrace = $this->calculateLateAttendanceHours($attendancePunchInDateTime,$delayedEntryFromTime);
									$alwaysGraceTime              = $this->calculateLateAttendanceHours($delayedEntryFromTime,$empGraceTimeDetails['Regular_From']);
								}
							}else{
								$lateAttendance = 0;
								$lateAttendanceHoursFromGrace = new Zend_Db_Expr('NULL');
								$alwaysGraceTime = new Zend_Db_Expr('NULL');
							}
						}
					}		
				}
			}
		}

		if(!empty($shortTimeOffConfiguration)&&!empty($shortTimeOffConfiguration['settingsDetails'])&& 
		($lateAttendance==2 || $lateAttendance==4)  && !empty($regularFromTime) && !empty($attendanceSettingsDetails) && 
		$attendanceSettingsDetails['Auto_Short_Time_Off']=='Yes')
		{
			$autoShortTimeOff 	= $this->addLateAttendanceShortTimeOff($employeeId,$shortTimeOffConfiguration,$regularFromTime,$attendancePunchInDateTime);
			if($autoShortTimeOff=='Yes')
			{
				$lateAttendance 			  = 0;
				$lateAttendanceHours   	   	  = new Zend_Db_Expr('NULL');
				$lateAttendanceHoursFromGrace = new Zend_Db_Expr('NULL');
				$alwaysGraceTime			  = new Zend_Db_Expr('NULL');
			}
		}
		$lateAttendanceDetails['Late_Attendance'] = $lateAttendance;   
		$lateAttendanceDetails['Late_Attendance_Hours'] = $lateAttendanceHours; 
		$lateAttendanceDetails['Late_Attendance_Hours_From_Grace'] = $lateAttendanceHoursFromGrace; 
		$lateAttendanceDetails['Always_Grace'] = $alwaysGraceTime;
		$lateAttendanceDetails['Auto_Short_Time_Off'] = $autoShortTimeOff;
		$lateAttendanceDetails['Business_Working_Day'] = $businessWorkingDays;
		return $lateAttendanceDetails;
	}

	public function getLateAttendanceLeaveFrequency($employeeId,$startDate,$endDate,$attendanceSettingsDetails)
	{
		if($attendanceSettingsDetails['Late_Attendance_Leave_Frequency']==1)
		{
			$lateAttendance = 2;
		}
		else
		{
			$lateAttendanceDetails = array(3,4);
			$empTotalLateAttendance = $this->getLateAttendanceCount($employeeId,$startDate,$endDate,$lateAttendanceDetails);
			$empTotalLateAttendance = $empTotalLateAttendance+1;
			$lateAttendanceLeaveFrequency = $attendanceSettingsDetails['Late_Attendance_Leave_Frequency'];
			if ($empTotalLateAttendance % $lateAttendanceLeaveFrequency === 0) 
			{
				$lateAttendance = 4;
			} 
			else 
			{
				$lateAttendance = 3;
			}
		}
		return $lateAttendance;
	} 

	public function getLateAttendanceCount($employeeId,$startDate,$endDate,$lateAttendance)
	{
		
		/* Get the total late attendance record based on the delayed entry max limit period */
		$qryEmpTotalLateAttendance = $this->_db->select()->from($this->_ehrTables->attendance,new Zend_Db_Expr('count(Attendance_Id)'))
																								->where('Late_Attendance IN (?)',$lateAttendance) 
																								->where('Employee_Id = ?', $employeeId)
																								->where('Attendance_Date >= ?',$startDate)
																								->where('Attendance_Date <= ?', $endDate);
		$empTotalLateAttendance = $this->_db->fetchOne($qryEmpTotalLateAttendance);

		return $empTotalLateAttendance;
	}
	
	public function applyLateAttendanceLeave($attendanceArray='null', $sessionId='null', $leaveDate, $attendanceDayIsWorkingDay)
	{
		$employeeDetails =  $this->getEmployeeJobDetails($attendanceArray['Employee_Id']);
		if(!empty($employeeDetails))
		{
			if(!empty($employeeDetails['Manager_Id']))
			{
				$approverId = $employeeDetails['Manager_Id'];
			}
			else 
			{
				$approverId = $attendanceArray['Employee_Id'];
			}
			$contactNo = $employeeDetails['Mobile_No'];
			$regularHours = $employeeDetails['Regular_Hours'];
		
			$attendanceSettingsDetails = $this->_db->fetchAll($this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),
											array('J.Employee_Id', 'J.Location_Id'))

											->joinInner(array('WS'=>$this->_ehrTables->workSchedule),
											'J.Work_Schedule = WS.WorkSchedule_Id', array('WS.WorkSchedule_Id'))

											->joinInner(array('AST'=>$this->_ehrTables->attendanceSettings),
											'J.Work_Schedule = AST.WorkSchedule_Id',
											array('AST.Attendance_Settings_Id', 'AST.Total_Days', 'AST.Alternate_LeaveType_Id','AST.Late_Attendance_Leave_Frequency','AST.Leave_Assignment_Method',
											'AST.From_Hours_For_Full_Day_Leave','AST.To_Hours_For_Full_Day_Leave','AST.From_Hours_For_Half_Day_Leave',
											'AST.To_Hours_For_Half_Day_Leave','AST.From_Hours_For_Quarter_Day_Leave','AST.To_Hours_For_Quarter_Day_Leave'))

											->joinInner(array('LA'=>$this->_ehrTables->lateAttendanceLeaveTypes),
											'AST.Attendance_Settings_Id = LA.Attendance_Settings_Id',
											array('LA.LeaveType_Id','LA.Position'))

											->where('J.Employee_Id = ?', $attendanceArray['Employee_Id'])
											->order('LA.Position ASC'));
			
			$dbLeave = new Employees_Model_DbTable_Leave();
			$attendanceArr = 0;

			$lateAttendanceHours = $attendanceArray['Late_Attendance_Hours'];
			$leaveAssignmentMethod = $attendanceSettingsDetails[0]['Leave_Assignment_Method'];

			if(strtolower($leaveAssignmentMethod) === 'fixed leave duration'){
				$duration = $totalDays = $attendanceSettingsDetails[0]['Total_Days'];
			}else{
				$lateAttendanceHoursInDecimal = $this->_dbCommonFun->hoursToDecimal($lateAttendanceHours);
				
				$lateAttendanceFullDayHoursFrom = $attendanceSettingsDetails[0]['From_Hours_For_Full_Day_Leave'];
				$lateAttendanceFullDayHoursTo = $attendanceSettingsDetails[0]['To_Hours_For_Full_Day_Leave'];
				$lateAttendanceHalfDayHoursFrom = $attendanceSettingsDetails[0]['From_Hours_For_Half_Day_Leave'];
				$lateAttendanceHalfDayHoursTo = $attendanceSettingsDetails[0]['To_Hours_For_Half_Day_Leave'];
				$lateAttendanceQuarterDayHoursFrom = $attendanceSettingsDetails[0]['From_Hours_For_Quarter_Day_Leave'];
				$lateAttendanceQuarterDayHoursTo = $attendanceSettingsDetails[0]['To_Hours_For_Quarter_Day_Leave'];

				if($lateAttendanceHoursInDecimal >= $lateAttendanceFullDayHoursFrom && $lateAttendanceHoursInDecimal <= $lateAttendanceFullDayHoursTo ){
					if($attendanceDayIsWorkingDay == 1){
						$duration = $totalDays = 1;
					}else{
						$duration = $totalDays = 0.5;
					}
				}else if($lateAttendanceHoursInDecimal >= $lateAttendanceHalfDayHoursFrom && $lateAttendanceHoursInDecimal <= $lateAttendanceHalfDayHoursTo){
					$duration = $totalDays = 0.5;
				}else if($lateAttendanceHoursInDecimal >= $lateAttendanceQuarterDayHoursFrom && $lateAttendanceHoursInDecimal <= $lateAttendanceQuarterDayHoursTo){
					$duration = $totalDays = 0.25;
				}else{
					$duration = $totalDays = 0;
				}
			}
			
			if($duration > 0){
				if($duration == 1)
				{
					$leavePeriod = new Zend_Db_Expr('NULL');
					$hours = $regularHours;
				}
				else if($duration == 0.5)
				{
					$leavePeriod = "First Half";
					$hours = ($duration * $regularHours);
				}else if($duration == 0.25)
				{
					$leavePeriod = "First Quarter";
					$hours = ($duration * $regularHours);
				} 

				// for the paid leave type balance deduction
				foreach($attendanceSettingsDetails as $attendanceSettings)
				{
					//Validate the employee is eligible to apply the leave type
					$validationDetails = array(
						'leaveTypeId' => $attendanceSettings['LeaveType_Id'],
						'leaveId' => 0,
						'leaveFrom' => $leaveDate,
						'leaveTo' => $leaveDate,
						'employeeId' => $attendanceArray['Employee_Id'],
						'duration' => $duration,
						'leavePeriod' => $leavePeriod
					);

					$result = $dbLeave->validateEmployeeLeave($validationDetails);
					$isLeaveDateValid = 1;
					if(!empty($result)){
						if(!is_null($result['leaveFreeze']) && $result['leaveFreeze']>0){
							$isLeaveDateValid = 0;
						}

						if (!is_null($result['frequency']) && !empty($result['frequency']) && !is_null($result['maxlimit'])
						&& !empty($leaveDate))
						{
							$errorCodestartMsg = $result['frequency']['startMsg'] ?? null;
							if (!is_null($errorCodestartMsg) && !empty($errorCodestartMsg))
							{
								$isLeaveDateValid = 0;
							}
						}

						if (!is_null($result['maxlimit']) && !empty($leaveDate))
						{
							$errorCodeendMsg =  $result['maxlimit']['endMsg'] ?? null;

							if (!is_null($errorCodeendMsg) && !empty($errorCodeendMsg))
							{
								$isLeaveDateValid = 0;
							}
						}

						if(!empty($result['maxlimit']['Leave_Period_Lapsed_Days']))
						{
							$isLeaveDateValid = 0;
						}
					}
					if($isLeaveDateValid == 1){
						$attendanceArr++;
						$leaveTypeId = $attendanceSettings['LeaveType_Id'];
						break;
					}
				}

				// if paid leave balance is not available for an employee, we will consider unpaid leave type
				if($attendanceArr == 0)
				{
					$leaveTypeId = $attendanceSettingsDetails[0]['Alternate_LeaveType_Id'];
				}
				
				$leaveDetailsArray = array('Reason'           => "Late Attendance",
										'Duration'         => $duration,
										'Start_Date'       => $leaveDate,
										'End_Date'         => $leaveDate,
										'Total_Days'       => $totalDays,
										'Hours'            => $hours,
										'Contact_Details'  => $contactNo,
										'Employee_Id'      => $attendanceArray['Employee_Id'],
										'Approver_Id'      => $approverId,
										'Leave_Period'	   => $leavePeriod,
										'LeaveType_Id'     => $leaveTypeId,
										'Alternate_Person' => array(),
										'Reason_Id'        => 0,
										'Approval_Status'  => "Applied",
										'Added_By'         => $sessionId,
										'Added_On'         => date('Y-m-d H:i:s'),
										'Late_Attendance'  => 1,
										'Late_Attendance_Hours'  =>$attendanceArray['Late_Attendance_Hours'],
										'Late_Attendance_Hours_From_Grace'  =>$attendanceArray['Late_Attendance_Hours_From_Grace'],
										'Always_Grace'  =>$attendanceArray['Always_Grace']);
										
				$lateAttendanceLeaveExist = $this->getLateAttendanceLeaveExist($leaveDetailsArray['Employee_Id'],$leaveDetailsArray['Start_Date']);							   

				// if(empty($lateAttendanceLeaveExist))
				// {
				// 	$this->_db->insert($this->_ehrTables->empLeaves,$leaveDetailsArray);
				// }
				$dbLeave = new Employees_Model_DbTable_Leave();
				if(empty($lateAttendanceLeaveExist))
				{
					$dbLeave->updateLeave ($leaveDetailsArray,array(), $sessionId, "", 0,
					"", array(), "","","",'No');
				}
			}
		}						   
	}

	/* check whetether the late attendance leave is added for that employee or not*/
	public function getLateAttendanceLeaveExist($employeeId,$startDate)
	{
		/** To apply leave for more than 2 days the below qry is used to apply leave for a period using between condition **/
		$getLeaveExistCondition = $this->_db->quoteInto(new Zend_Db_Expr(' ? BETWEEN Date_Format(L.Start_Date, "%Y-%m-%d") AND Date_Format(L.End_Date, "%Y-%m-%d")'), $startDate);
									
		/** To check whether leave is added or not **/
	
		$getLeaveDetailsQry = $this->_db->select()->from(array('L'=>$this->_ehrTables->empLeaves),array('L.Leave_Id'))
                                                            ->joinInner(array('LT'=>$this->_ehrTables->leavetype),'L.LeaveType_Id = LT.LeaveType_Id',array())
    														->where('L.Employee_Id = ?', $employeeId)
															->where('L.Late_Attendance = 1')
															->where($getLeaveExistCondition);

		$getLeaveDetails = $this->_db->fetchOne($getLeaveDetailsQry);    
		
		return $getLeaveDetails;
				
	}

    /*get the comp off configuration details based on the attendance id*/
	public function getCompOffConfigurationDetails($commentArray)
	{
		$attendanceDetails = $compOffBalanceDetails = $specialWageDetails = $currentWorkScheduleDetails = array();
		$startDate = '';
		if($commentArray['Approval_Status'] == 'Approved')
		{
			$attendanceDetails = $this->_db->fetchRow($this->_db->select()->from(array('A'=>$this->_ehrTables->attendance),
													array('A.Attendance_Id','A.PunchIn_Date','A.Total_Hours','A.Employee_Id',
														'Attendance_PunchIn_Date'=>new Zend_Db_Expr('(Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T"))')))
													->joinInner(array('EJ'=>$this->_ehrTables->empJob),'EJ.Employee_Id = A.Employee_Id',array('EJ.Location_Id'))
													->joinInner(array('DN'=>$this->_ehrTables->designation), 'DN.Designation_Id=EJ.Designation_Id', array(''))
													->joinInner(array('T'=>$this->_ehrTables->timesheetHrs), 'DN.Grade_Id=T.Grade_Id', array('T.Regular_Hours','T.OverTime_Hours','T.Break_Hours'))
													->where('Attendance_Id =?', $commentArray['Parent_Id']));
			if(!empty($attendanceDetails))
			{
			   $employeeId = $attendanceDetails['Employee_Id'];
			   
			   $currentWorkScheduleDetails = $this->getCurrentWorkScheduleDetails($employeeId,$attendanceDetails['Attendance_PunchIn_Date']);
			 
			   if(!empty($currentWorkScheduleDetails)) 
			   {
					$startDate = date('Y-m-d',strtotime($currentWorkScheduleDetails['Regular_From']));
					$specialWageDetails = $this->_dbPayslip->getSpecialWageConfiguration($employeeId,$startDate,'CompoffUpdate');
					if(!empty($specialWageDetails))
					{
						if($specialWageDetails['Comp_Off']=='Yes')
						{
							$compOffBalanceDetails = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->compOffBalance, array('Comp_Off_Balance_Id','Attendance_Id','Employee_Id','Worked_Date','Expiry_Date','Total_Days','Remaining_Days','Comp_Off_Attendance_Balance','Comp_Off_Overtime_Claim_Balance','Source'))
																							->where('Employee_Id = ?', $employeeId)
																							->where('Worked_Date = ?', $startDate));
						}
					}
			   }
			}
		}	   		
		return array('Attendance_Details'=>$attendanceDetails,'Start_Date'=>$startDate,'Comp_Off_Balance'=>$compOffBalanceDetails,'Special_Wage'=>$specialWageDetails,'Current_Work_Schedule_Details'=>$currentWorkScheduleDetails);
	}

	/* when the employee is eligible for compensatory off , compensatory off balance will be updated*/
	public function updateCompOffBalance($commentArray)
	{
		$dbCompensatoryOff 		= new Employees_Model_DbTable_CompensatoryOff();
		$configurationDetails 	= $this->getCompOffConfigurationDetails($commentArray);
		$compOffBalanceDetails  = $configurationDetails['Comp_Off_Balance'];
		$specialWageDetails 	= $configurationDetails['Special_Wage'];
		$startDate   			= $configurationDetails['Start_Date'];
		$attendanceDetails 		= $configurationDetails['Attendance_Details'];	
		$currentWorkScheduleDetails = $configurationDetails['Current_Work_Schedule_Details'];	
		if(!empty($startDate)&&!empty($specialWageDetails) && $specialWageDetails['Comp_Off']=='Yes'&&!empty($attendanceDetails))
		{
			$specialWageDetails['Regular_Hours']  = $attendanceDetails['Regular_Hours'];
			$compOffBalanceRules 	= $dbCompensatoryOff->getCompOffBalanceRules($specialWageDetails);
			$employeeId 		= $attendanceDetails['Employee_Id'];
			$compOffDetails = $this->calculateCompOffBalance($employeeId,$compOffBalanceDetails,$attendanceDetails,$specialWageDetails,$currentWorkScheduleDetails);
			if(!empty($compOffBalanceDetails))
			{
				/*when the comp off record is cancelled and rejected we are not allowing the user to update the comp off record.there should not be 
				any balance is deducted for cancelled and rejected status record so we dont have any issues while updating the comp off balance*/
				$compOffStatus = array('Cancelled','Rejected'); 
				$compOffExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->compOff,new Zend_Db_Expr('COUNT(Compensatory_Off_Id)'))
																->where('Approval_Status NOT IN (?)',$compOffStatus) 
																->where('Comp_Off_Balance_Id = ?', $compOffBalanceDetails['Comp_Off_Balance_Id']));

				$compOffEncashmentExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->compOffEncashment,new Zend_Db_Expr('COUNT(Comp_Off_Encashment_Id)'))
																->where('Comp_Off_Balance_Id = ?', $compOffBalanceDetails['Comp_Off_Balance_Id']));												

				/*When the employee applies the comp off encashment we should not update the comp off balance in that time.
				when the comp off records are in cancelled and rejected we should able to update the comp off balance. */      
				if(empty($compOffExist) && empty($compOffEncashmentExist))
				{
					$compOffDetails['Comp_Off_Balance_Rules'] = $compOffBalanceRules;
					$updated = $this->_db->update($this->_ehrTables->compOffBalance, $compOffDetails,array('Comp_Off_Balance_Id = '. $compOffBalanceDetails['Comp_Off_Balance_Id']));
				}		
			}
			else 
			{
				if($compOffDetails['Total_Days'] > 0)
				{
					if($specialWageDetails['Comp_Off_Expiry_Type']=='Fixed Days')
					{
						$expiryDate = date("Y-m-d",strtotime("+".$specialWageDetails['Comp_Off_Expiry_Days']." days", strtotime($startDate)));
					}
					else if($specialWageDetails['Comp_Off_Expiry_Type']=='Same Payroll Month')
					{
						$salaryDateDetails = $this->_dbPayslip->getSalaryDateRange(date('m',strtotime($startDate)),date('Y',strtotime($startDate)),strtotime($startDate));
						$expiryDate        = $salaryDateDetails['Last_SalaryDate']; 
					}
					else if($specialWageDetails['Comp_Off_Expiry_Type']=='Calendar Year')
					{
						$expiryDate = $this->calculateExpiryDate($startDate,12);
					}
					else if($specialWageDetails['Comp_Off_Expiry_Type']=='Fiscal Year')
					{
						$fiscalEndMonth = (int)($this->_orgDetails['Fiscal_StartMonth']) - 1;
						if ($fiscalEndMonth == 0) {
							$fiscalEndMonth = 12;
						}
						$expiryDate = $this->calculateExpiryDate($startDate,$fiscalEndMonth);
					}

					$compOffBalance = array('Employee_Id'=>$employeeId,
											'Worked_Date'=>$startDate,
											'Expiry_Date'=>$expiryDate,
											'Total_Hours'=>$compOffDetails['Total_Hours'],
											'Total_Days'=>$compOffDetails['Total_Days'],
											'Remaining_Days'=>$compOffDetails['Remaining_Days'],
											'Comp_Off_Attendance_Balance'=>$compOffDetails['Comp_Off_Attendance_Balance'],
											'Comp_Off_Overtime_Claim_Balance'=>0,
											'Source'=>$compOffDetails['Source'],
											'Comp_Off_Balance_Rules'=>$compOffBalanceRules);
											
					$inserted = $this->_db->insert($this->_ehrTables->compOffBalance,$compOffBalance);						
				}
			}
		}
		else 
		{
			return false;
		}	
	}

	public function calculateExpiryDate($startDate,$payslipMonth)
	{
		$payslipYear 	= date('Y', strtotime($startDate));
		$paycyleDate	= $this->_dbPayslip->getSalaryDay($payslipMonth,$payslipYear);
		if($startDate <= $paycyleDate['Last_SalaryDate'])
		{
			$expiryDate = $paycyleDate['Last_SalaryDate']; 
		}
		else
		{
			$payslipYear 	= $payslipYear + 1;
			$paycyleDate 	= $this->_dbPayslip->getSalaryDay($payslipMonth,$payslipYear);
			$expiryDate 	= $paycyleDate['Last_SalaryDate']; 
		}
		return $expiryDate;
	}

	/*Calculate the total hours,total days,remaining days,comp off attendance balance and source for that particular day*/
	public function calculateCompOffBalance($employeeId,$compOffBalanceDetails,$attendanceDetails,$specialWageDetails,$currentWorkScheduleDetails)
	{
		$specialWageWorkingHours 	= $this->getSpecialWageWorkingHours($employeeId,$attendanceDetails,$specialWageDetails,$currentWorkScheduleDetails);
		$totalWorkedHours          	= $specialWageWorkingHours['Total_Worked_Hours'];
		$expectedWorkingHours 		= $specialWageWorkingHours['Minimum_Hours_For_Full_Day_Comp_Off'];
		$halfDayExpectedWorkingHours = $specialWageWorkingHours['Minimum_Hours_For_Half_Day_Comp_Off'];

		if($totalWorkedHours >= $expectedWorkingHours)
		{
			$compOffAttendanceBalance = 1;
		}
		else if($totalWorkedHours >= $halfDayExpectedWorkingHours && $specialWageDetails['Allow_Half_Day_Comp_Off_Credit']=='Yes')
		{
			$compOffAttendanceBalance = 0.5;	
		}
		else 
		{
			$compOffAttendanceBalance = 0;
		}

		if($specialWageDetails['Work_Day_Type']=='Extra Work Hours(Weekday)')
		{
			$compOffAttendanceBalance = 0;
		}


		$compOffApplicability = $specialWageDetails['Comp_Off_Applicability_For_Overtime_Hours'];
		$totalOTHours         = $specialWageWorkingHours['Total_OT_Hours'];

		if($specialWageDetails['Comp_Off_Balance_Approval'] === 'Automatic'){
			if($compOffApplicability=='Both Full Day & Half Day') 
			{
				if($totalOTHours >= $specialWageDetails['Minimum_OT_Hours_For_Full_Day_Comp_Off'])
				{
					$compOffAttendanceBalance += 1;	
				}
				elseif($totalOTHours >= $specialWageDetails['Minimum_OT_Hours_For_Half_Day_Comp_Off'])
				{
					$compOffAttendanceBalance += 0.5;
				}
			}

			if (in_array($compOffApplicability, array('Full Day')) && $totalOTHours >= $specialWageDetails['Minimum_OT_Hours_For_Full_Day_Comp_Off']) {
				$compOffAttendanceBalance += 1;
			}

			if (in_array($compOffApplicability, array('Half Day')) && $totalOTHours >= $specialWageDetails['Minimum_OT_Hours_For_Half_Day_Comp_Off']) {
				$compOffAttendanceBalance += 0.5;
			}
		}
		/*When the  comp off balance exist we need to calculate total hours,total days,remaining days,comp off attendance balance and source like below*/
		if(!empty($compOffBalanceDetails))
		{
			if($compOffBalanceDetails['Comp_Off_Attendance_Balance'] > $compOffAttendanceBalance)
			{
				/*When the existing comp off attendance balance is greater than current comp off attendance balance we need to find the difference.
				that difference should be subtracted from total days and remaining days*/
				$updatedBalance = $compOffBalanceDetails['Comp_Off_Attendance_Balance']-$compOffAttendanceBalance;
				$compOffBalanceDetails['Comp_Off_Attendance_Balance']= $compOffAttendanceBalance;
				$compOffBalanceDetails['Total_Days'] 				-= $updatedBalance;
				$compOffBalanceDetails['Remaining_Days']            -= $updatedBalance;  
			}
			else 
			{
				/*When the existing comp off attendance balance is lesser than current comp off attendance balance we need to find the difference.
				that difference should be added to total days and remaining days*/
				$updatedBalance = $compOffAttendanceBalance-$compOffBalanceDetails['Comp_Off_Attendance_Balance'];
				$compOffBalanceDetails['Comp_Off_Attendance_Balance']= $compOffAttendanceBalance;
				$compOffBalanceDetails['Total_Days'] 				+= $updatedBalance;
				$compOffBalanceDetails['Remaining_Days']            += $updatedBalance;
			}

			
			if($compOffBalanceDetails['Comp_Off_Attendance_Balance'] > 0 && $compOffBalanceDetails['Comp_Off_Overtime_Claim_Balance'] > 0)
			{
				/*When the comp off attendance balance and comp off overtime claim balance is greater than zero so comp off balance is added through
				both attendance and overtime claim so we need to update the source as attendance and overtime claim*/
				$compOffBalanceDetails['Source'] = 'Attendance And Overtime Claim';
			}
			elseif($compOffBalanceDetails['Comp_Off_Attendance_Balance'] > 0 && ($compOffBalanceDetails['Comp_Off_Overtime_Claim_Balance']==0 || empty($compOffBalanceDetails['Comp_Off_Overtime_Claim_Balance'])))
			{
				/*When the comp off attendance balance is greater than zero and overtime claim balance is zero.so comp off balance is added through
				attendance alone so we need to update the source as attendance*/
				$compOffBalanceDetails['Source'] = 'Attendance';
			}
			elseif($compOffBalanceDetails['Comp_Off_Overtime_Claim_Balance'] > 0 && ($compOffBalanceDetails['Comp_Off_Attendance_Balance']==0 || empty($compOffBalanceDetails['Comp_Off_Attendance_Balance']))) 
			{
				/*When the comp off overtime claim is greater than zero and comp off attendance balance is zero.so comp off balance is added through
				overtime claim alone so we need to update the source as  overtime claim*/
				$compOffBalanceDetails['Source'] = 'Overtime Claim';
			}
		}
		else 
		{
			$compOffBalanceDetails['Comp_Off_Attendance_Balance']= $compOffAttendanceBalance;
			$compOffBalanceDetails['Total_Days'] 				 = $compOffAttendanceBalance;
			$compOffBalanceDetails['Remaining_Days']             = $compOffAttendanceBalance;
			$compOffBalanceDetails['Source']                     = 'Attendance';
		}

		$compOffBalanceDetails['Total_Hours'] = $totalWorkedHours;

		return $compOffBalanceDetails;
	}

	/*Get the employee expected working hours and worked hours through this function */
	public function getSpecialWageWorkingHours($employeeId,$attendanceDetails,$specialWageDetails,$currentWorkScheduleDetails)
	{
		$totalWorkedHours = $this->getTotalWorkedHours($employeeId,$currentWorkScheduleDetails);
	
		if($specialWageDetails['Comp_Off_Threshold']=='Work Schedule Hours')
		{
			$expectedWorkingHours = $attendanceDetails['Regular_Hours'];
			$expectedHalfDayWorkingHours = $expectedWorkingHours/2;
		}
		else 
		{
			$expectedWorkingHours = $specialWageDetails['Fixed_Regular_Hours'];
			$expectedHalfDayWorkingHours = $specialWageDetails['Minimum_Hours_For_Half_Day_Comp_Off'];
		}

		if($totalWorkedHours > $attendanceDetails['Regular_Hours'])
		{
			$totalOTHours = $totalWorkedHours-$attendanceDetails['Regular_Hours'];
		}
		else
		{
			$totalOTHours = 0;
		}

        return array('Minimum_Hours_For_Full_Day_Comp_Off'=>$expectedWorkingHours,'Minimum_Hours_For_Half_Day_Comp_Off'=>$expectedHalfDayWorkingHours,'Total_Worked_Hours'=>$totalWorkedHours,'Total_OT_Hours'=>$totalOTHours);
	}

	/*Get the employee total attendance hours for that particular day we should not consider break hours and overtime hours compoff calculation*/
	public function getTotalWorkedHours($employeeId,$currentWorkScheduleDetails)
	{
		$totalWorkedHours = $this->_db->fetchOne($this->_db->select()->from(array('A'=>$this->_ehrTables->attendance),array('WorkedHrs' => new Zend_Db_Expr('SUM(A.Total_Hours)')))
                  	->where('A.Employee_Id = ?', $employeeId)
                    ->where('A.Approval_Status LIKE ?', 'Approved')
                    ->where(new Zend_Db_Expr('Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T")').' >= ?', $currentWorkScheduleDetails['Consideration_From'])
					->where(new Zend_Db_Expr('Date_Format(Concat(PunchOut_Date," ",PunchOut_Time), "%Y-%m-%d %T")').' <= ?', $currentWorkScheduleDetails['Consideration_To']));

		return $totalWorkedHours;			
	}

	public function setBreakHours($attendanceIdArr,$statusId)
	{
		$whereBonus['Attendance_Id IN (?)'] = $attendanceIdArr;
		$updated=$this->_db->update($this->_ehrTables->attendance, $statusId, $whereBonus);
		$updated=1;
		if($updated)
		{
			if($statusId['Exclude_Break_Hours']==0)
			{
				return array('success'=>true, 'msg'=>'Break hours Included successfully', 'type'=>'success');
			}
			else
			{
				return array('success'=>true, 'msg'=>'Break hours Excluded successfully', 'type'=>'success');
			}
			
		}
		else
		{
				return array('success'=>false, 'msg'=>'Unable to Update', 'type'=>'Faliure');
		}
		
	}

	/********************************************* Device Management ********************************************************/
	
	/**
	 * to list and search device details
	 */


	public function listDeviceDetails($page, $rows, $sortField, $sortOrder, $searchAll=null, $searchArr)
    {
		$deviceName            	 = $searchArr['deviceName'];
		$machineType             = $searchArr['machineType'];
		$connectionType          = $searchArr['connectionType'];
		$ipAddress           	 = $searchArr['ipAddress'];
		$port 					 = $searchArr['port'];
		
		/**
		 *	Sorting columns based on display column order in grid
		*/
		switch ($sortField)
		{
			case 2: $sortField = 'DM.Machine_Type'; break;
			case 3: $sortField = 'DM.Connection_Type'; break;
			case 4: $sortField = 'DM.IP_Address'; break;
			case 5: $sortField = 'DM.Port'; break;
			default: $sortField = 'DM.Device_Name'; break;
		}
		
		/**
		 *	Query to fetch data from various tables
		*/
        $qryDeviceDetails = $this->_db->select()
								->from(array('DM'=>$this->_ehrTables->deviceManagement),
									   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS DM.Device_Id as count'),
											 'DM.Device_Id','DM.Device_Name', 'DM.Serial_Number', 'DM.IP_Address',
											 'DM.Port', 'DM.Inport', 'DM.Timeout','DM.Attendance_Parser',
											 'DM.Connection_Type','DM.Machine_Type',
											 new Zend_Db_Expr("DATE_FORMAT(DM.Added_On,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
											 new Zend_Db_Expr("DATE_FORMAT(DM.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On")))							 

											 ->joinLeft(array('AB'=>$this->_ehrTables->empPersonal),'AB.Employee_Id=DM.Added_By',
											 array('Added_By_Name'=>new Zend_Db_Expr("CONCAT(AB.Emp_First_Name, ' ', AB.Emp_Last_Name)")))
								  
								  			->joinLeft(array('EB'=>$this->_ehrTables->empPersonal),'EB.Employee_Id=DM.Updated_By',
											 array('Updated_By_Name'=>new Zend_Db_Expr("CONCAT(EB.Emp_First_Name, ' ', EB.Emp_Last_Name)")))  

											->order("$sortField $sortOrder")
											->group('DM.Device_Id')
											->limit($rows, $page);												


		/**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			$conditions  = $this->_db->quoteInto('DM.Device_Name  Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or DM.Machine_Type Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or DM.Connection_Type Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or DM.IP_Address Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or DM.Port Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or DM.Inport Like ?', "%$searchAll%");
			
			$qryDeviceDetails->where($conditions);		
		}
		
		if ($deviceName != '' && $deviceName != null && preg_match('/^[a-zA-Z 0-9*\.]/', $deviceName))
		{
			$qryDeviceDetails->where($this->_db->quoteInto('DM.Device_Name = ?', $deviceName));
		}
		
		if ($machineType!='' && $machineType!= null && preg_match('/^[a-zA-Z ]/', $machineType) && !empty($machineType))
		{
		    $qryDeviceDetails->where($this->_db->quoteInto('DM.Machine_Type = ?', $machineType));
		}
		
		if ($connectionType!='' && $connectionType!=null && preg_match('/^[a-zA-Z ]/', $connectionType) && !empty($connectionType))
		{
			$qryDeviceDetails->where($this->_db->quoteInto('DM.Connection_Type = ?', $connectionType));
		}
		
		if ($ipAddress != '' && $ipAddress != null && preg_match('/^[0-9*\.]/', $ipAddress))
		{
			$qryDeviceDetails->where($this->_db->quoteInto('DM.IP_Address = ?', $ipAddress));
		}
		
		if ($port!='' && $port!=null  && preg_match('/^[0-9]/', $port) && !empty($port))
		{
		    $qryDeviceDetails->where($this->_db->quoteInto('DM.Port = ?', $port));
		}
		
		/**
		 * SQL queries
		 * Get data to display
		*/
		$deviceDetails = $this->_db->fetchAll($qryDeviceDetails);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		/* Total data set length */
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->deviceManagement,
																  new Zend_Db_Expr('COUNT(Device_Id)')));
		
		$arrayDeviceManagement = array();
		foreach($deviceDetails as $key =>$value)
		{
			if($value['Device_Id'] != 0)
				$arrayDeviceManagement[$key] = $value;
		}
		
		/**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $arrayDeviceManagement);
	}
	
	/**
	 *	Update attendance settings details in attendance settings table and update system log too
    */
	public function updateDeviceDetails($deviceDetailsArray, $sessionId, $formName)
	{
		$qryDeviceDetails = $this->_db->select()->from(array('DM'=>$this->_ehrTables->deviceManagement),
															new Zend_Db_Expr('Count(Device_Id) as totalDeviceId'))
								
									->where('DM.Device_Name = ?', $deviceDetailsArray['Device_Name']);
		
		if (!empty($deviceDetailsArray['Device_Id']))
		{
			$qryDeviceDetails->where('Device_Id != ?', $deviceDetailsArray['Device_Id']);
		}
		
		$isExist = $this->_db->fetchRow($qryDeviceDetails);
		
		if ($isExist['totalDeviceId'] == 0)
		{
			if (!empty($deviceDetailsArray['Device_Id']))
			{
				$action = 'Edit';
				
				$deviceDetailsArray['Updated_On'] = date('Y-m-d H:i:s');
				$deviceDetailsArray['Updated_By'] = $sessionId;
				
				$updated = $this->_db->update($this->_ehrTables->deviceManagement, $deviceDetailsArray,
											  array('Device_Id = '. $deviceDetailsArray['Device_Id']));
				
				$deviceManagementId = $deviceDetailsArray['Device_Id'];
			}
			else
			{
				$action = 'Add';
				
				$deviceDetailsArray['Added_On'] = date('Y-m-d H:i:s');
				$deviceDetailsArray['Added_By'] = $sessionId;
				
				$updated = $this->_db->insert($this->_ehrTables->deviceManagement, $deviceDetailsArray);
				
				if($updated)
					$deviceId = $this->_db->lastInsertId();
			}
			/*
			 *	this function will handle
			 *		update system log function
			 *		clear submit lock fucntion
			 *		return success/failure array
			*/
			return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
															'action'         => $action,
															'trackingColumn' => $deviceDetailsArray['Device_Name'],
															'formName'       => $formName,
															'sessionId'      => $sessionId,
															'tableName'      => $this->_ehrTables->deviceManagement));
		}
		else
		{
			return array('success' => false, 'msg'=>'Device name already exists.', 'type'=>'info');
		}
	}
	
	//Delete attendance settings
	public function deleteDeviceDetails($deviceId, $logEmpId, $formName)
	{
		$deleted = 0;
		
		$deviceDetailsRow = $this->_db->fetchRow($this->_db->select()
										   ->from($this->_ehrTables->deviceManagement, array('Lock_Flag'))
										   ->where('Device_Id = ?', $deviceId));
		
		if ($deviceDetailsRow['Lock_Flag'] == 0)
		{
			$deleted = $this->_db->delete($this->_ehrTables->deviceManagement,'Device_Id='.$deviceId);
			
		}
		
		/**
		 *	delete activity for common function
		 *		1)check lock is exist or not.
		 *			If lock is exist then show error message like employee is open record for update.
		 *		2)If No lockflag then process delete activity
		 *		3)Update delete activity in system log
		 *		4)return success/failure message
		*/
		return $this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
														'lockFlag'       => $deviceDetailsRow['Lock_Flag'],
														'formName'       => $formName,
														'trackingColumn' => $deviceId,
														'sessionId'      => $logEmpId));
    }

	// get employee checkin and checkout work place 
	public function getCheckInCheckOutWorkPlace($employeeId,$attendanceImportEmployeeId,$punchInDate,$punchOutDate)
	{
		$checkInWorkPlaceId  = null;
		$checkOutWorkPlaceId = null;
		$isEnableWorkPlace 	 = $this->isEnableWorkPlace($employeeId);

		if($isEnableWorkPlace == 1)
		{
			if(!empty($punchInDate))
			{
				$firstInAttendanceId = $this->getEmployeeAttendanceImportId($attendanceImportEmployeeId,$punchInDate);
				$checkInWorkPlaceId  = $this->getWorkPlaceId($firstInAttendanceId);
				if(empty($checkInWorkPlaceId)&&$this->_orgDetails['Field_Force']==0)
				{
					$checkInWorkPlaceId = $this->getOfficeWorkPlaceId();
				}
			}

			if(!empty($punchOutDate))
			{
				$lastOutAttendanceId = $this->getEmployeeAttendanceImportId($attendanceImportEmployeeId,$punchOutDate);
				$checkOutWorkPlaceId = $this->getWorkPlaceId($lastOutAttendanceId);
				if(empty($checkOutWorkPlaceId) && $this->_orgDetails['Field_Force']==0)
				{
					$checkOutWorkPlaceId = $this->getOfficeWorkPlaceId();
				}
			}
		}

		return array('Checkin_Work_Place_Id'=>$checkInWorkPlaceId,'Checkout_Work_Place_Id'=>$checkOutWorkPlaceId);
	}

	// get the work place id based on attendance import id
	public function getWorkPlaceId($attendanceId)
	{
		$workPlaceId = $this->_db->fetchOne($this->_db->select()->from(array('AI'=>$this->_ehrTables->attendanceImport), array('AWP.Work_Place_Id'))
												->joinInner(array('AWP'=>$this->_ehrTables->attendanceWorkPlace), 'AWP.Import_Work_Place=AI.Work_Place', array())
												->where('Attendance_Id = ?', $attendanceId));

		return $workPlaceId;
	}

    // get the attendance id based on attendance import employee id and addedon
	public function getEmployeeAttendanceImportId($attendanceImportEmployeeId,$addedOn)
	{
		$attendanceImportId = $this->_db->fetchOne($this->_db->select()->from(array('AI'=>$this->_ehrTables->attendanceImport),array('Attendance_Id'))
											->where('Employee_Id = ?', $attendanceImportEmployeeId)
											->where('AI.Added_On = ?', $addedOn));

		return $attendanceImportId;									
	}

	// get the attendance settings configuration based on configuration type
	public function getAttendanceSettings($configurationType,$customGroupId=NULL)
	{
		$qryAttendanceSettingsDetails = $this->_db->select()->from($this->_ehrTables->attendanceSettings,array('*'))
															->where('Configuration_Type = ?',$configurationType)
															->where('Attendance_Settings_Status = ?', 'Active');

		if(!empty($customGroupId))
		{
			$qryAttendanceSettingsDetails->where('Custom_Group_Id = ?',$customGroupId);
		}													

		$attendanceSettingsDetails = $this->_db->fetchAll($qryAttendanceSettingsDetails);
		return $attendanceSettingsDetails;																																										
	}

	// get the employee job details based on employee id
	public function getEmployeeJobDetails($employeeId)
	{
		$employeeDetails = $this->_db->fetchRow($this->_db->select()->from(array('J'=>$this->_ehrTables->empJob), array('J.Employee_Id','J.Manager_Id'))
												->joinInner(array('Dn'=>$this->_ehrTables->designation), 'Dn.Designation_Id=J.Designation_Id', array())
												->joinInner(array('T'=>$this->_ehrTables->timesheetHrs), 'T.Grade_Id = Dn.Grade_Id',array('T.Regular_Hours'))
												->joinInner(array('C'=>$this->_ehrTables->empContacts), 'C.Employee_Id=J.Employee_Id', array('C.Mobile_No'))			
												->where('J.Employee_Id = ?', $employeeId));

		return $employeeDetails;										
	}

	/* check whether the attendance shortage leave is added for that employee or not*/
	public function getAttendanceShortageLeaveExist($employeeId,$startDate,$endDate,$action)
	{
		if($action === 'deleteAttendanceShortage') 
		{ 
			$status = array('Applied','Returned','Rejected'); 
		}
		else
		{
			$status = array('Applied','Approved','Cancel Applied');
		}
		/** To check whether leave is added or not **/
		$getLeaveDetailsQry = $this->_db->select()->from(array('L'=>$this->_ehrTables->empLeaves),array('L.Leave_Id'))
                                                            ->joinInner(array('LT'=>$this->_ehrTables->leavetype),'L.LeaveType_Id = LT.LeaveType_Id',array())
    														->where('L.Employee_Id IN (?)', $employeeId)
															->where('L.Approval_Status IN (?)', $status)
															->where('L.Attendance_Shortage = 1')
															->where('L.Start_Date >= ?', $startDate)
															->where('L.Start_Date <= ?', $endDate);
		$getLeaveDetails = $this->_db->fetchCol($getLeaveDetailsQry);    
		return $getLeaveDetails;
	}

	// delete the attendance shortage based on employee id and start date and end date.
	public function deleteAttendanceShortage($employeeId,$startDate,$endDate)
	{
		$deleted = 0;
		$leaveId = $this->getAttendanceShortageLeaveExist($employeeId,$startDate,$endDate,'deleteAttendanceShortage');
		if(!empty($leaveId))
		{
			$whereLeaveId['Leave_Id IN (?)'] = $leaveId;
			$deleted = $this->_db->delete($this->_ehrTables->empLeaves,$whereLeaveId);
		}
		return $deleted;
	}

	//get the attendance hours from the duration of regular from and regular to.
	public function getAttendanceTotalHours($employeeId,$currentWorkScheduleDetails,$employeeLateAttendanceDetails='')
	{
		$delayedEntryDetails = '';
		if(!empty($employeeLateAttendanceDetails)){
			$delayedEntryDetails = array(
				'Delayed_Entry_From_Time'=> date('Y-m-d H:i:s',strtotime('+'.$employeeLateAttendanceDetails["Delayed_Entry_From"].'minutes',strtotime($currentWorkScheduleDetails['Regular_From']))),
				'Delayed_Entry_Upto_Time'=> date('Y-m-d H:i:s',strtotime('+'.$employeeLateAttendanceDetails["Delayed_Entry_Upto"].'minutes',strtotime($currentWorkScheduleDetails['Regular_From'])))
			);
		}
		$totalHours = 0;
		//Get the start time and end time based on the regular hours
		$qryAttendanceDetails = $this->_db->select()->from(array($this->_ehrTables->attendance),array('Late_Attendance','Actual_Start_Time'=>new Zend_Db_Expr("CASE WHEN PunchIn_Date AND (Date_Format(Concat(PunchIn_Date,' ',PunchIn_Time), '%Y-%m-%d %T') > '".$currentWorkScheduleDetails['Regular_From']."')THEN 
										(Date_Format(Concat(PunchIn_Date,' ',PunchIn_Time), '%Y-%m-%d %T')) ELSE '".$currentWorkScheduleDetails['Regular_From']."' END"),
										'Actual_End_Time'=>new Zend_Db_Expr("CASE WHEN PunchOut_Date AND (Date_Format(Concat(PunchOut_Date,' ',PunchOut_Time), '%Y-%m-%d %T') > '".$currentWorkScheduleDetails['Regular_To']."')THEN 
										'".$currentWorkScheduleDetails['Regular_To']."' ELSE (Date_Format(Concat(PunchOut_Date,' ',PunchOut_Time), '%Y-%m-%d %T')) END")))
										->where(new Zend_Db_Expr('Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T")').' >= ?', $currentWorkScheduleDetails['Consideration_From'])
										->where(new Zend_Db_Expr('Date_Format(Concat(PunchOut_Date," ",PunchOut_Time), "%Y-%m-%d %T")').' <= ?', $currentWorkScheduleDetails['Consideration_To'])
										->where('Employee_Id = ?',$employeeId);
		$attendanceDetails = $this->_db->fetchAll($qryAttendanceDetails);
		//Find total hours
		if(!empty($attendanceDetails)){
			foreach($attendanceDetails as $attendanceHours)
			{
				$attendanceFrom = $attendanceHours['Actual_Start_Time'];
				$attendanceTo = $attendanceHours['Actual_End_Time'];
								
				//If the late attendance grace time is not calculated
				if(!empty($delayedEntryDetails) && $attendanceHours['Late_Attendance'] == 1){
					//As the grace time is provided for delayed entry it has to be added while calculating the total hours
					if((strtotime($attendanceHours['Actual_Start_Time']) >= strtotime($delayedEntryDetails['Delayed_Entry_From_Time'])
					&& strtotime($attendanceHours['Actual_Start_Time']) < strtotime($delayedEntryDetails['Delayed_Entry_Upto_Time']))){
						$attendanceFrom = $delayedEntryDetails['Delayed_Entry_From_Time'];
					}
				}

				$punchInDetails = explode(' ',$attendanceFrom);
				$punchOutDetails = explode(' ',$attendanceTo);

				$attendanceArray = array('PunchIn_Date'=>$punchInDetails[0],'PunchIn_Time'=>$punchInDetails[1],
				'PunchOut_Date'=>$punchOutDetails[0],'PunchOut_Time'=>$punchOutDetails[1]);
				
				$totalHours += $this->getTotalHours($attendanceArray);
			}
		}
		if($totalHours >= 0)
		{
			return $totalHours;
		}
		else
		{
           return 0; 
		}
	}

	//Function to get the late attendance settings
	public function getLateAttendanceSettings(){
		$lateAttendanceSettingsDetails = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->attendanceSettings,
											array('WorkSchedule_Id','Delayed_Entry_From', 'Delayed_Entry_Upto', 'Delayed_Entry_Maximum_Limit','Delayed_Entry_Maximum_Limit_Period'))
										->where('Attendance_Settings_Status = ?', 'Active')
										->where('Configuration_Type = ?','Late Attendance'));
		return $lateAttendanceSettingsDetails;
	}

	public function getEmployeeId($userDefinedEmpId)
    {
        $qryEmployeeId = $this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
                                    ->where('User_Defined_EmpId = ?', $userDefinedEmpId);
        $employeeId = $this->_db->fetchOne($qryEmployeeId);
        return $employeeId;
    }

	//fuction to validate the pre approval request
	public function validateEmpPreApprovalRequest($employeeId,$punchInDateTime,$punchOutDateTime,$checkInWorkPlaceId,$checkOutWorkPlaceId)
	{
		$message = '';
		$preApprovalRequestType='';
		$preApprovalRequestStatus = '';
		
		// Check if pre-approval settings exist before making API call
		$preApprovalSettings = $this->_db->fetchAll($this->_db->select()
			->from(array('A' => $this->_ehrTables->preApprovalSettings), 
				   array('A.Pre_Approval_Type', 'A.Coverage', 'A.Custom_Group_Id', 'A.Type_Of_Day', 'A.Status'))
			->where('A.Status = ?', 'Active'));
		
		// If no active pre-approval settings exist, skip API call
		if (empty($preApprovalSettings)) {
			return array('success' => true, 'message' => '', 'preApprovalRequestType' => '', 'preApprovalRequestStatus' => '');
		}
		
		$orgCode = $this->_ehrTables->getOrgCode();
		$curl = curl_init();
		$method = "POST";
		$apiBaseUrl = Zend_Registry::get('coreHrRoBaseUrl');
		if(!empty($apiBaseUrl))
		{
			$url = $apiBaseUrl;
		}	
		else
		{
			curl_close($curl);
			return array('success' => false, 'message' => 'Something went wrong in validating the pre-approval request. Please contact system admin.');
		}

		$checkInWorkPlaceId = $checkInWorkPlaceId ?? 0;
		$checkOutWorkPlaceId = $checkOutWorkPlaceId ?? 0;
		
		/* Create an object */
		$apiInputs = new \stdClass();
		$apiInputs->employeeId = (int)$employeeId;
		$apiInputs->source = "attendance";
		$apiInputs->punchInDateTime = $punchInDateTime;
		$apiInputs->punchOutDateTime = $punchOutDateTime;
		$apiInputs->attendanceDate = '';
		$apiInputs->checkInWorkPlaceId = (int)$checkInWorkPlaceId;
		$apiInputs->checkOutWorkPlaceId = (int)$checkOutWorkPlaceId;
		$apiInputs->checkOutWorkPlaceId = (int)$checkOutWorkPlaceId;
		$apiInputs->checkInCheckoutWorkPlace = '';
		$apiInputs->punchType = '';
		
		/* Convert the object to JSON */
		$apiInputsJSON = json_encode($apiInputs);
		$requestBody = '{
			"variables" : '.$apiInputsJSON.',
			"query":"query validateWfhPreApproval($employeeId:Int!, $source:String!, $attendanceDate:Date, $attendanceDateTime: String, $punchInDateTime: String, $punchOutDateTime: String, $punchType: String!, $checkInCheckoutWorkPlace:String!, $checkInWorkPlaceId: Int, $checkOutWorkPlaceId: Int) { validateWfhPreApproval(employeeId:$employeeId, source: $source, attendanceDate: $attendanceDate, attendanceDateTime: $attendanceDateTime, punchInDateTime: $punchInDateTime, punchOutDateTime: $punchOutDateTime, punchType: $punchType, checkInCheckoutWorkPlace:$checkInCheckoutWorkPlace, checkInWorkPlaceId: $checkInWorkPlaceId, checkOutWorkPlaceId: $checkOutWorkPlaceId) { errorCode message preApprovalRequestType preApprovalRequestStatus } }"
		}';

		curl_setopt($curl, CURLOPT_POST, 1);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $requestBody);
		curl_setopt($curl, CURLOPT_URL, $url);
		
		//Set API headers
		$apiHeaders = $this->_dbCommonFun->getApiHeaders($orgCode);
		curl_setopt($curl, CURLOPT_HTTPHEADER, $apiHeaders);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

		// EXECUTE:
		$result = curl_exec($curl);
		curl_close($curl);
		if(!$result)
		{
			return array('success' => false, 'message' => 'There seem to be some technical difficulties in validating the pre-approval request. Please contact system admin.');
		}
		//Handle API response
		$responseData = json_decode($result,true);
		if(isset($responseData['message']) && (isset($responseData['message']['message']) && $responseData['message']['message'] == 'API gateway timeout.')){
			$message = 'Unable to validate the work from home pre-approval request. Please try again.';
		}else {
			if (isset($responseData['data']) && isset($responseData['data']['validateWfhPreApproval'])
			&& !empty($responseData['data']['validateWfhPreApproval'])){
				$resultDetails = $responseData['data']['validateWfhPreApproval'];
				$preApprovalRequestType = isset($resultDetails['preApprovalRequestType']) ? $resultDetails['preApprovalRequestType']: '';
				$preApprovalRequestStatus = isset($resultDetails['preApprovalRequestStatus']) ? $resultDetails['preApprovalRequestStatus']: '';
				$message = '';
			} else if(isset($responseData['errors']) && count($responseData['errors']) > 0) {
				if(isset($responseData['errors'][0]['message'])){
					$errorResponse = $responseData['errors'][0];
					$errorCode = $responseData['errors'][0]['extensions']['code'];
					switch($errorCode){
						case 'CHR0057':
							if (
								isset($responseData['errors'][0]['extensions']['failedPreApprovals']) &&
								is_array($responseData['errors'][0]['extensions']['failedPreApprovals'])
							) {
								$failedPreApprovals = $responseData['errors'][0]['extensions']['failedPreApprovals'];
							}else {
								$failedPreApprovals = array();
							}


							if (is_array($failedPreApprovals) && count($failedPreApprovals) > 0) {
								// Join pre-approvals into comma-separated string
								$preApprovalTypes = implode(', ', $failedPreApprovals);
								$message = "Attendance cannot be submitted as the following pre-approval request(s) are either missing or pending approval: $preApprovalTypes. Please ensure all required pre-approvals are submitted and approved before proceeding.";
							} else {
								$message =  "We couldn't validate the pre-approval request at this time. Please reach out to your system administrator.";
							}
							break;
						case 'CHR0056':// The request date-time does not fall in between the Aug 01 2023 09:00:00 and the Aug 01 2023 18:00:00
						case 'CHR0066':// The request date-time does not fall in between the Aug 01 2023 09:00:00 and the Aug 01 2023 18:00:00
						case 'CHR0067':// The week-off or holiday request for the specified date has not been applied or granted
							$message =  $responseData['errors'][0]['message'];
							break;
						default:
							// "CWS0008": Work schedule does not exist.
							// "_EC0007" : Invalid input field(s).
							// "CHR0063": Error while getting the employee timezone current date-time.
							// "CHR0058": Error while validating the work from home pre-approval request.
							// "CHR0059": Error while getting the employee timezone current date-time.
							// "CHR0101": Error while processing the request to validate the work from home pre-approval request.
							$message =  'There seem to be some technical difficulties in validating the pre-approval request. Please contact system admin.';
							break;
					}
				}else{
					$message = 'Unable to validate the work from home pre-approval request. Please try again.';
				}
			} else {
				$message = 'There seem to be some technical difficulties in validating the pre-approval request. Please contact system admin.';
			}
		}
		return array('success' => ($message) ? false : true, 'message' => $message,'preApprovalRequestType'=>$preApprovalRequestType,'preApprovalRequestStatus' => $preApprovalRequestStatus);
	}


	public function validateManagerAttendanceApproval($lastSalaryDate){
		$updateStatus = 0;
		$displayCutOffDate = '';
		$message = '';
		$cutOffDate = '';
		$attendanceGeneralConfig = $this->_dbCommonFun->getAttendanceGeneralConfiguration();
		if(!empty($attendanceGeneralConfig) && count($attendanceGeneralConfig) > 0){
			$attendanceApprovalCutOffDaysForManager = $attendanceGeneralConfig['Attendance_Approval_Cut_Off_Days_For_Manager'];
			if($attendanceGeneralConfig['Attendance_Approval_Cut_Off_Days_For_Manager'] >= 1){
				$cutOffDate = date('Y-m-d', strtotime("+$attendanceApprovalCutOffDaysForManager days", strtotime($lastSalaryDate)));
				$currentDate = date('Y-m-d');
				//If current date is 3 Jul 2023 and the cut off date is Jul 2023 then jun month all the attendance record status can be updated by manager.
				if($currentDate<=$cutOffDate){
					$updateStatus = 1;
					$message = 'Current date is lesser than or equal to the attendance approval cut-off date. So status can be updated.';
				}else{
					$displayCutOffDate = date('d M Y',strtotime($cutOffDate));
					$message = 'Current date is greater than the attendance approval cut-off date. So status cannot be updated.';
					$updateStatus = 0;
				}
			}else{
				$updateStatus = 1;
				$message = 'Attendance approval cut-off for manager is less than 1. So status can be updated.';
			}
		}else{
			$updateStatus = 1;
			$message = 'Attendance general configuration does not exist. So status can be updated.';
		}
		return array('updateStatus' => $updateStatus, 'message' => $message, 'actualCutOffDate'=>$cutOffDate, 'cutOffDate' => $displayCutOffDate);
	}

	public function validateAttendanceGeneralConfig($employeeId,$attendanceDate,$attendanceGeneralConfig){
		$updateStatus = 0;
		$displayCutOffDate = '';
		$employeeRegularizeAttendance = 0;
		$regularizationEmployeeCutOffNextDate = '';
		$regularizationRequestLimitExceed = 0;
		$employeeRegularizationRequestLimit = 0;
		$dbAttendanceFinalization = new Employees_Model_DbTable_AttendanceFinalization();

		if(!empty($attendanceGeneralConfig) && count($attendanceGeneralConfig) > 0){
			$currentDate = date('Y-m-d');
			$salaryDateDetails = $this->_dbPayslip->getSalaryDateRange(date('m',strtotime($attendanceDate)),date('Y',strtotime($attendanceDate)),strtotime($attendanceDate), 29);

			$employeeRegularizationRequestLimit = $attendanceGeneralConfig['Attendance_Regularization_Request_Limit_For_Employee'];
			$employeeRegularizationCutOffDays = $attendanceGeneralConfig['Attendance_Regularization_Cut_Off_Days_For_Employee'];
			if( $employeeRegularizationCutOffDays >= 1 || $employeeRegularizationRequestLimit >= 1 ){
				if($employeeRegularizationCutOffDays >= 1){

					$regularizationEmpResult = $dbAttendanceFinalization->getNewMinDateByRegularizationCutOff($attendanceDate,$currentDate,$attendanceGeneralConfig);
					$attendanceRegularizationAfter = strtolower($regularizationEmpResult['attendanceRegularizationAfter']);
					
					if($attendanceRegularizationAfter === 'Last day of payroll'){
						//Attendance date: 14 May 2023, Cut Off Date: 31 May 2023, Cut Off Date + 1 day: 1 Jun 2023. Attendance cannot be applied
						//Attendance date: 14 Jun 2023, Cut Off Date: 30 Jun 2023, Cut Off Date + 1 day: 1 Jul 2023. Attendance can be applied
						if(empty($regularizationEmpResult['regularizationNextMonthStartDate'])){
							$employeeRegularizeAttendance = 1;
						}else{
							$regularizationEmployeeCutOffNextDate = $regularizationEmpResult['displayCutOffDate'];
							$employeeRegularizeAttendance = 0;
						}
					}else{
						$currentDateRegularizationCutOffDate = $regularizationEmpResult['currentDateRegularizationCutOffDate'];
						$currentDateRegularizationCutOffDateTs = strtotime($currentDateRegularizationCutOffDate);
						if(strtotime($attendanceDate) >= $currentDateRegularizationCutOffDateTs){
							$employeeRegularizeAttendance = 1;
						}else{
							$regularizationEmployeeCutOffNextDate = date('d M Y',$currentDateRegularizationCutOffDateTs);
							$employeeRegularizeAttendance = 0;
						}
					}
					if($employeeRegularizationRequestLimit >= 1){
						$regularizationRequestLimitExceed=$dbAttendanceFinalization->validateRegularizationRequestLimit($employeeId,$salaryDateDetails,$employeeRegularizationRequestLimit);
					}
				}
			}else{
				$employeeRegularizeAttendance = 1;
				$regularizationRequestLimitExceed = 0;
			}
		}else{
			$employeeRegularizeAttendance = 1;
			$regularizationRequestLimitExceed = 0;
		}
		return array('employeeRegularizeAttendance' => $employeeRegularizeAttendance, 'regularizationRequestLimitExceed' => $regularizationRequestLimitExceed, 'regularizationEmployeeCutOffNextDate' => $regularizationEmployeeCutOffNextDate, 'employeeRegularizationRequestLimit' => $employeeRegularizationRequestLimit );
	}

	public function getOnDutyAttendanceDetails($attendanceDuration,$attendancePeriod,$workScheduleDetails,$checkInWorkPlaceId)
	{
		$attendanceWorkPlace = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->attendanceWorkPlace,array('*'))
																				->where('Work_Place_Status = ?', 'Active')
																				->where('Auto_Attendance = ?', 'Yes')
																				->where('Work_Place_Id = ?', $checkInWorkPlaceId));
		if(!empty($attendanceWorkPlace))								
		{
			if($attendanceDuration==1)
			{
                  $attendancePunchInDateTime = $workScheduleDetails['Regular_From'];
				  $attendancePunchOutDateTime = $workScheduleDetails['Regular_To'];
			}
			else
			{
				if($attendancePeriod=='First Half')
				{
					$attendancePunchInDateTime = $workScheduleDetails['Regular_From'];
					$attendancePunchOutDateTime = $workScheduleDetails['Lunch_Break_From'];
				}
				else
				{
					$attendancePunchInDateTime = $workScheduleDetails['Lunch_Break_To'];
					$attendancePunchOutDateTime = $workScheduleDetails['Regular_To'];
				}
			}
		}
		
		if(!empty($attendancePunchInDateTime) && !empty($attendancePunchInDateTime))
		{
			return array('attendancePunchInDateTime'=>$attendancePunchInDateTime,'attendancePunchOutDateTime'=>$attendancePunchOutDateTime);
		}
		else
		{
			return 0;
		}
	}
	
	public function getAllWorkScheduleDetails($shiftStartDate,$shiftEndDate)
	{
		$qryWorkScheduleDetails =$this->_db->select()->from(array('WS'=>$this->_ehrTables->workSchedule),array('WS.Check_In_Grace_Time', 'WS.Grace_Time_Flag','WS.Early_Check_In_Override',
											'WS.Check_Out_Time_Buffer','WS.WorkSchedule_Id','WS.Twodays_Flag','WS.Check_In_Consideration_Time','WS.Check_Out_Consideration_Time',
											'WS.Regular_Work_Start_Time','WS.Regular_Work_End_Time','WS.OverTime_Cooling_Period','WS.Allow_Attendance_Outside_Regular_WorkHours','WS.Title',
											'WS.Regular_Hours_Per_Day','WS.Overtime_Hours_Per_Day','WS.OverTime_Cooling_Period','WS.Overtime_Threshold_Per_Day'));
											
		$workScheduleDetails = $this->_db->fetchAll($qryWorkScheduleDetails);	
		$workScheduleWeekOffDates  = $this->calculateWorkScheduleWeekOffDates($shiftStartDate,$shiftEndDate);
		$shiftDetails = array();
		foreach($workScheduleDetails as $workSchedule)
		{
			$step 	 = '+1 day'; /**incrementor in while loop**/
			$current = strtotime($shiftStartDate);
			$last 	 = strtotime($shiftEndDate);
			while( $current <= $last )
            {
				$startDate 				   		= date('Y-m-d',$current);
				$workSchedule['Regular_From'] 	= date("Y-m-d H:i:s",strtotime($startDate.' '.$workSchedule['Regular_Work_Start_Time']));
				$workSchedule['Regular_To'] 	= date("Y-m-d H:i:s",strtotime($startDate.' '.$workSchedule['Regular_Work_End_Time']));
				if($workSchedule['Twodays_Flag']==1)
				{
					$workSchedule['Regular_To'] = date("Y-m-d H:i:s", strtotime('+1 days',strtotime($workSchedule['Regular_To'])));
				}

				if($workSchedule['Check_In_Consideration_Time'] > 0) 
				{
					$workSchedule['Consideration_From'] = date('Y-m-d H:i:s',strtotime('-'.$workSchedule["Check_In_Consideration_Time"].'minutes',strtotime($workSchedule['Regular_From'])));
				}
				else 
				{
					$workSchedule['Consideration_From'] = $workSchedule['Regular_From'];
				}

				if($workSchedule['Check_Out_Consideration_Time'] > 0) 
				{
					$workSchedule['Consideration_To'] = date('Y-m-d H:i:s',strtotime('+'.$workSchedule["Check_Out_Consideration_Time"].'minutes',strtotime($workSchedule['Regular_To'])));
				}
				else
				{
					$workSchedule['Consideration_To'] =   $workSchedule['Regular_To'];
				}
				$workSchedule['Work_Schedule_Date']   = date("Y-m-d",strtotime($workSchedule['Regular_From']));
				$workScheduleId = $workSchedule['WorkSchedule_Id'];
				$workScheduleWeekOff = $workScheduleWeekOffDates[$startDate][$workScheduleId] ?? null;
				if ($workScheduleWeekOff !== null)
				{
					$workSchedule['Week_Off_Exist']='Yes';
					$workSchedule['Week_Off_Duration']=$workScheduleWeekOff['Duration'];
				}
				else
				{
					$workSchedule['Week_Off_Exist']      = 'No';
					$workSchedule['Week_Off_Duration']   = 0;
				}
			
				$lunchBreakDetails = $this->getLunchBreakHours($workSchedule);
				$workSchedule['Lunch_Break_From'] 	= $lunchBreakDetails['Lunch_Break_From'];
				$workSchedule['Lunch_Break_To'] 	= $lunchBreakDetails['Lunch_Break_To'];
				
				array_push($shiftDetails,$workSchedule);
				$current = strtotime($step, $current);
			}
		}

		$shiftDetailsByDateAndEmployee = array();
		foreach ($shiftDetails as $shiftDetail) {
			$workScheduleDate = $shiftDetail['Work_Schedule_Date'];
			$workScheduleId   = $shiftDetail['WorkSchedule_Id'];
			$shiftDetailsByDateAndEmployee[$workScheduleId][$workScheduleDate] = $shiftDetail;
		}

		unset($workScheduleDetails,$workScheduleWeekOffDates,$lunchBreakDetails,$shiftDetails);
		return $shiftDetailsByDateAndEmployee;
	}

	public function calculateWorkScheduleWeekOffDates($startDate,$endDate)
    {
        $workScheduleWeekOff = $this->_db->fetchAll($this->_db->select()->from(array('WO'=>$this->_ehrTables->workScheduleWeekOff), array('WO.WorkSchedule_Id', 'WO.Week_Number','WO.Duration','WO.Day_Id','WD.Day_Name','WO.Exclude_Last_Week')) 
                                                              ->joinInner(array('WD'=>$this->_ehrTables->weekdays), 'WD.Day_Id=WO.Day_Id',array(''))); 
        $weekendInfo = array();
        foreach ($workScheduleWeekOff as $data) {
            $workScheduleId = $data['WorkSchedule_Id'];
            $weekNumber = $data['Week_Number'];
            $duration = $data['Duration'];
            $dayId = $data['Day_Id'];
            $dayName = $data['Day_Name'];
            $excludeLastWeek = $data['Exclude_Last_Week']; // Assuming you have this field in your data
            
            $firstDate = strtotime($startDate);
            $lastDate = strtotime($endDate);
            $step = '+1 day';

            while ($firstDate <= $lastDate) {
                // Get the week number of the current date
                $currentWeekNumber = $this->_dbPayslip->weekOfMonth($firstDate);
                
                // Get the day of the week (1 - Monday, 7 - Sunday)
                $currentDayNo = date('N', $firstDate);
            
                // Check if current week is 4th or 5th and excludeLastWeek is 'Yes'
                if (in_array($currentWeekNumber, array(4, 5)) && $excludeLastWeek == 'Yes') {
                    // Calculate the date of next week
                    $nextWeekDate = date('Y-m-d', strtotime("+7 day", $firstDate));
                    
                    // Get the week number of the next week
                    $nextWeekNo = $this->_dbPayslip->weekOfMonth(strtotime($nextWeekDate));
            
                    // Check if next week is not the 5th week of the month
                    if ($nextWeekNo == 5) {
                        // Check if the current day matches the specified dayId and weekNumber
                        if ($currentWeekNumber == $weekNumber && $currentDayNo == $dayId) {
							$weekendDate = date('Y-m-d', $firstDate);
                            // Create an array with weekend date information and add it to $weekendInfo
                            $weekendDates = array(
                                'WorkSchedule_Id' => $workScheduleId,
                                'Week_Number' => $weekNumber,
                                'Duration' => $duration,
                                'Day_Id' => $dayId,
                                'Day_Name' => $dayName,
                                'Weekend_Date' => $weekendDate
                            );
							$weekendInfo[] = $weekendDates;
                        }
                    }
                } 
                else 
                {
                    // Check if the current day matches the specified dayId and weekNumber
                    if ($currentWeekNumber == $weekNumber && $currentDayNo == $dayId) {
                        // Create an array with weekend date information and add it to $weekendInfo
						$weekendDate = date('Y-m-d', $firstDate);
                        $weekendDates = array(
                            'WorkSchedule_Id' => $workScheduleId,
                            'Week_Number' => $weekNumber,
                            'Duration' => $duration,
                            'Day_Id' => $dayId,
                            'Day_Name' => $dayName,
                            'Weekend_Date' => $weekendDate
                        );
						$weekendInfo[] = $weekendDates;
                    }
                }
                
                // Move to the next date
                $firstDate = strtotime($step, $firstDate);
            }
        }

		$weekendInfoByWorkSchedule = array();
		foreach ($weekendInfo as $weekend) {
			$weekendDate = $weekend['Weekend_Date'];
			$workScheduleId = $weekend['WorkSchedule_Id'];
			$weekendInfoByWorkSchedule[$weekendDate][$workScheduleId] = $weekend;
		}
		unset($workScheduleWeekOff,$weekendInfo);
        return $weekendInfoByWorkSchedule;
    }


	public function getAllEmployeeShiftDetails($employeeDetails,$salaryDate,$lastSalaryDate)
    {
		$shiftRoasterEmployeeIds = array();
		$shiftDetailsByDateAndEmployee = array();
		foreach($employeeDetails as $employeeData)
		{
			if($employeeData['Work_Schedule']=='Shift Roster')
			{
				array_push($shiftRoasterEmployeeIds,$employeeData['Employee_Id']); 
			}
		}
		
		if(!empty($shiftRoasterEmployeeIds))
		{
			$allShiftDetails = $this->_db->fetchAll($this->_db->select()->from(array('SEM'=>$this->_ehrTables->shiftEmpMapping), array('SEM.Shift_Start_Date','SEM.Employee_Id','EST.WorkSchedule_Id','SEM.Week_Off')) 
														->joinInner(array('EST'=>$this->_ehrTables->empShiftType),'EST.Shift_Type_Id=SEM.Shift_Type_Id',array(''))
														->where('SEM.Employee_Id IN (?)', $shiftRoasterEmployeeIds)
														->where('SEM.Shift_Start_Date >= ?', $salaryDate) 
														->where('SEM.Shift_Start_Date <= ?', $lastSalaryDate)
														->order(array('SEM.Employee_Id ASC', 'SEM.Shift_Start_Date ASC'))); 

			foreach ($allShiftDetails as $shiftDetail) {
				$shiftStartDate = $shiftDetail['Shift_Start_Date'];
				$employeeId = $shiftDetail['Employee_Id'];
				$shiftDetailsByDateAndEmployee[$employeeId][$shiftStartDate] = $shiftDetail;
			}
			unset($allShiftDetails);											
		}
		return $shiftDetailsByDateAndEmployee;
    }

	public function getOverLapShiftSchedulingDetails($allShiftDetails,$allWorkScheduleDetails,$startDate,$graceTimeDetails) 
	{ 
		$nextDate = date("Y-m-d", strtotime('+1 days',strtotime($startDate))); 
		$shiftDetails = $allShiftDetails[$nextDate] ?? null; 
		if ($shiftDetails !== null) 
		{ 
			$workScheduleId = $shiftDetails['WorkSchedule_Id']; 
			$nextShiftGraceTimeDetails = $allWorkScheduleDetails[$workScheduleId][$nextDate]?? null; 
			//when the shift is not scheduled for next day we should not allow the user to add the attendance today.    
			if ($nextShiftGraceTimeDetails !== null) 
			{ 
				if($nextShiftGraceTimeDetails['Check_In_Consideration_Time'] > 0)  
				{ 
					$nextShiftGraceTimeDetails['Consideration_From'] = date('Y-m-d H:i:s',strtotime('-'.$nextShiftGraceTimeDetails["Check_In_Consideration_Time"].'minutes',strtotime($nextShiftGraceTimeDetails['Regular_From']))); 
				} 
				else  
				{ 
					$nextShiftGraceTimeDetails['Consideration_From'] = $nextShiftGraceTimeDetails['Regular_From']; 
				} 
		 
				//current date shift should end(consideration to) next shift consideration from time -1 minute. 
				$nextShiftConsiderationTime= date("Y-m-d H:i:s", strtotime("-1 minutes", strtotime($nextShiftGraceTimeDetails['Consideration_From']))); 
 
				//we introduce the overlapping shift schedule flag only to overlap overtime detail.but we should not allow the regular time getting overlapped. 
				if($nextShiftConsiderationTime > $graceTimeDetails['Regular_To']) 
				{ 
					/* Check-in minimum consideration date time */ 
					$checkInConsiderationDateTime = strtotime($graceTimeDetails['Consideration_From']); 
 
					/* Check-out maximum consideration date time */ 
					$checkOutConsiderationDateTime = strtotime($nextShiftConsiderationTime); 
					 
					/* If check out consideration time is greater than check in consideration time */ 
					if($checkOutConsiderationDateTime > $checkInConsiderationDateTime){ 
						/* Find the difference between check out consideration datetime and the check-in consideration datetime in seconds */ 
						$differenceInDateTime = $checkOutConsiderationDateTime - $checkInConsiderationDateTime; 
					 
						/* One day = 86400 seconds. If the difference is less than or equal to 86400 then check-in datetime and the  
						checkout datetime falls on the same day */ 
						if($differenceInDateTime <= 86400){ 
							$graceTimeDetails['Consideration_To'] = $nextShiftConsiderationTime; 
						} 
					} 
				} 
				else  
				{ 
					return ''; 
				} 
			} 
			else  
			{ 
				return $nextShiftGraceTimeDetails; 
			} 
            return $graceTimeDetails; 
		} 
	}

	public function getEmployeeAttendanceDetails($employeeId)
	{
		$employeeDetails = $this->_db->fetchRow($this->_db->select()->from(array('EJ'=>$this->_ehrTables->empJob), array('EJ.External_EmpId'))
												->joinInner(array('EP'=>$this->_ehrTables->empPersonal), 'EP.Employee_Id=EJ.Employee_Id',
															array('Employee_Name'=>new Zend_Db_Expr("CONCAT(EP.Emp_First_Name, ' ', EP.Emp_Last_Name)")))
												->joinInner(array('ET'=>$this->_ehrTables->empType),'ET.EmpType_Id=EJ.EmpType_Id',array('Exclude_Break_Hours'))
												->where('EJ.Employee_Id = ? ', $employeeId));
		return $employeeDetails;										
	}

	public function insertAttendanceImportRecord($formData,$employeeDetails,$customFormName,$sessionId,$attendanceDetails,$isTheme)
	{
		$attendanceImportDetails = array();
		$schemaId                = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendanceHeader, array('Schema_Id'))
																			->where('Schema_Name = ?', 'Bio Attendance'));
		if(!empty($schemaId))																	
		{
			$punchInAddedOnDateTime = date('Y-m-d H:i:s', strtotime($formData['PunchIn_Date'].' '.$formData['PunchIn_Time']));
			$importData = array('Schema_Id'           => $schemaId,
								'Employee_Id'         => $employeeDetails['External_EmpId'],
								'Employee_Name'       => $employeeDetails['Employee_Name'],
								'Exclude_Break_Hours' => $employeeDetails['Exclude_Break_Hours'],
								'Data_Source'		  => isset($formData['Checkin_Data_Source']) ? $formData['Checkin_Data_Source'] : '',
								'Form_Source'		  => isset($formData['Checkin_Form_Source']) ? $formData['Checkin_Form_Source'] : '',
								'Attendance_Status'   => 'C/In',
								'Import_Latitude'     => isset($formData['Checkin_Latitude']) ? $formData['Checkin_Latitude'] : '',
								'Import_Longitude'    => isset($formData['Checkin_Longitude']) ? $formData['Checkin_Longitude'] : '',
								'Import_Address'      => isset($formData['Checkin_Address']) ? $formData['Checkin_Address'] : '',
								'Added_On'            => $punchInAddedOnDateTime,
								'Added_On_Date_Time'  => date('Y-m-d H:i:s'),
								'Added_By'            => $sessionId);

			$attendanceImportExist    = $this->_dbCommonFun->attendanceImportAlreadyExist($importData);
			$importData['Work_Place'] = $this->getWorkPlaceDetails($formData['Checkin_Work_Place_Id']);
			
			if(empty($attendanceImportExist))
			{	
				if(!empty($attendanceDetails))
				{
					$importData['Attendance_Status'] = 'C/Out';
				}

				if($isTheme=='dashboardMissedCheckout')
				{
					$importData['Attendance_Status'] = 'C/Out';
				}
				array_push($attendanceImportDetails,$importData);
			}

			if(!empty($formData['PunchOut_Date']) && !empty($formData['PunchOut_Time']))
			{
				$importData['Attendance_Status'] = 'C/Out';
				$importData['Data_Source']       = isset($formData['Checkout_Data_Source']) ? $formData['Checkout_Data_Source'] : '';
				$importData['Form_Source'] 		 = isset($formData['Checkout_Form_Source']) ? $formData['Checkout_Form_Source'] : '';
				$importData['Work_Place'] 		 = $this->getWorkPlaceDetails($formData['Checkout_Work_Place_Id']);
				$importData['Import_Latitude']   = isset($formData['Checkout_Latitude']) ? $formData['Checkout_Latitude'] : '';
				$importData['Import_Longitude']  = isset($formData['Checkout_Longitude']) ? $formData['Checkout_Longitude'] : '';
				$importData['Import_Address']    = isset($formData['Checkout_Address']) ? $formData['Checkout_Address'] : '';
				$importData['Added_On'] 		 = date('Y-m-d H:i:s', strtotime($formData['PunchOut_Date'].' '.$formData['PunchOut_Time']));
				$importData['Added_On_Date_Time']= date('Y-m-d H:i:s');
				$importData['Added_By']          = $sessionId;

				$attendanceImportExist = $this->_dbCommonFun->attendanceImportAlreadyExist($importData);
				if(empty($attendanceImportExist))
				{					
					array_push($attendanceImportDetails,$importData);
				}
			}
		}
		else
		{
			return array('success' => false, 'msg'=>'The attendance import format has not been configured within the application. Please reach out to your system administrator for assistance', 'type'=>'warning');
		}

		if(!empty($attendanceImportDetails))
		{
			$insertAttendanceImportDetails = $this->_ehrTables->insertMultiple($this->_ehrTables->attendanceImport,$attendanceImportDetails);			
			$trackingColumn = $formData['Employee_Id'].' '.$attendanceImportDetails[0]['Added_On'];
			$updatedResult = $this->_dbCommonFun->updateResult (array('updated'        => $insertAttendanceImportDetails,
														'action'         => 'Add',
														'trackingColumn' => $trackingColumn,
														'formName'       => $customFormName,
														'sessionId'      => $sessionId,
														'tableName'      => $this->_ehrTables->attendance));

		    $updatedResult['status'] = $formData['Approval_Status'];

			return $updatedResult;
		}
		else
		{
			return array('success' => false, 'msg'=>'Attendance records already exist for this same time period', 'type'=>'warning');
		}
	}

	public function getWorkPlaceDetails($attendanceWorkPlace)
	{
		if (!empty($attendanceWorkPlace)) 
		{
			$importWorkPlace = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendanceWorkPlace, array('Import_Work_Place'))
																							   ->where('Work_Place_Id = ?', $attendanceWorkPlace));
		}
		else 
		{
			$importWorkPlace = null; // Set to null if $attendanceWorkPlace is empty
		}
		return $importWorkPlace;
	}

	public function getAttendanceImportDataSource()
	{
		return $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->attendanceImport, array('Data_Source','Data_Source'))
																							->where('Data_Source IS NOT NULL')
																							->group('Data_Source'));
	}


	/**
	 * list short leave request settings
	 * 
	 * @param int $employeeId
	 * @param string $attendanceProcessFrom
	 * @param string $attendanceProcessTo
	 * @return array
	 */
	public function listShortLeaveRequestSettings($employeeId,$attendanceProcessFrom,$attendanceProcessTo)
	{
		$organizeEmployeeDetails = array();
		$shortTimeOffPermissionFormId = 129;
		$shortTimeOffCoverage = $this->_dbCommonFun->getCoverageForFormId($shortTimeOffPermissionFormId);
		$shortTimeOffDetailsQuery = $this->_db->select()->from(array('SL'=>$this->_ehrTables->shortLeaveRequestSettings),
								array('SL.Maximum_Limit','SL.Min_Short_Time_Per_Request','SL.Max_Short_Time_Per_Request',
								'SL.Short_Leave_settings_Id','SL.Frequency','SL.Gender', 'SL.Leave_Activation_Days',
								'SL.Period','SL.Minimum_Duration','SL.Maximum_Duration','SL.Limit_By','SL.Short_Time_Off_Activation_After'))
								->where('SL.Limit_By = ?', "Request");
		
		$result = $this->_db->fetchRow($shortTimeOffDetailsQuery);	

		if(strtolower($shortTimeOffCoverage) === 'custom group')
		{
			$customGroupEmployeesType = array('Default','AdditionalInclusion');
			$customGroupParentIdQry = $this->_db->select()->from(array('CEGE'=>$this->_ehrTables->customEmployeeGroupEmployees),array('CEGE.Employee_Id'))
											->joinInner(array('CEG'=>$this->_ehrTables->customEmployeeGroup), 'CEGE.Group_Id=CEG.Group_Id', array(''))
											->joinInner(array('CEGMF'=>$this->_ehrTables->customEmployeeGroupModuleForms), 'CEG.Group_Id=CEGMF.Group_Id', array(''))
											->joinInner(array('CGAF'=>$this->_ehrTables->customGroupAssociateForm), 'CEGMF.Group_Id=CGAF.Custom_Group_Id', array(''))
											->where('CGAF.Form_Id = ?',$shortTimeOffPermissionFormId)
											->where('CEGMF.Form_Id = ?',$shortTimeOffPermissionFormId)
											->where('CEGE.Employee_Id IN (?)',$employeeId)
											->where("CEGE.Type IN (?)", $customGroupEmployeesType);

			$customGroupEmployeeId = $this->_db->fetchCol($customGroupParentIdQry);                    
		}
		else
		{
			$customGroupEmployeeId = array();
		}

		$attendanceRange =array();

		if(!empty($result))
		{
			$shortTimeOffPeriod 		= $result['Period'];
			if ($shortTimeOffPeriod === "Per Month") {
				//get the salary start date and end date based on short time off start date
				$salaryDateDetails  				= $this->_dbPayslip->getSalaryDateRange(date('m',strtotime($attendanceProcessFrom)), date('Y',strtotime($attendanceProcessFrom)),strtotime($attendanceProcessFrom));
				$attendanceProcessFromStartDate  	= $salaryDateDetails['Salary_Date'];
				$attendanceProcessFromEndDate  		= $salaryDateDetails['Last_SalaryDate'];

				$salaryDateDetails  				= $this->_dbPayslip->getSalaryDateRange(date('m',strtotime($attendanceProcessTo)), date('Y',strtotime($attendanceProcessTo)),strtotime($attendanceProcessTo));
				$attendanceProcessToStartDate  	    = $salaryDateDetails['Salary_Date'];
				$attendanceProcessToEndDate  		= $salaryDateDetails['Last_SalaryDate'];
			} 
			else 
			{
				$yearRange 							= $this->_dbCommonFun->getDateRangeBasedOnDuration($attendanceProcessFrom,$shortTimeOffPeriod);
				$attendanceProcessFromStartDate 	= $yearRange["From_Date"];
				$attendanceProcessFromEndDate 		= $yearRange["To_Date"];

				$yearRange 						= $this->_dbCommonFun->getDateRangeBasedOnDuration($attendanceProcessTo,$shortTimeOffPeriod);
				$attendanceProcessToStartDate 	= $yearRange["From_Date"];
				$attendanceProcessToEndDate 	= $yearRange["To_Date"];
			}

			$attendanceRange = array('attendanceProcessFromStartDate'=>$attendanceProcessFromStartDate,
									'attendanceProcessFromEndDate'=>$attendanceProcessFromEndDate,
									'attendanceProcessToStartDate'=>$attendanceProcessToStartDate,
									'attendanceProcessToEndDate'=>$attendanceProcessToEndDate);
			////shorttimeoff record we are getting before the loop when the record add while processing not considered.						
			// $shortTimeOffDetails  	   = $this->getShortTimeOffEmpDetails($employeeId,$attendanceProcessFromStartDate,$attendanceProcessToEndDate);
			$employeeDetails    = $this->_db->fetchAll($this->_db->select()->from(array('EJ'=>$this->_ehrTables->empJob),array('EJ.Employee_Id','EJ.Date_Of_Join','EJ.Manager_Id'))
															->joinInner(array('CD'=>$this->_ehrTables->empContacts), 'CD.Employee_Id=EJ.Employee_Id', array('CD.Mobile_No')));									

			$organizeEmployeeDetails = $this->_dbCommonFun->organizeDataByEmployeeIdAndDate($employeeDetails,'Employee_Id');
			unset($employeeDetails);
		}

		$settingsResponse = array(
			'coverage' => $shortTimeOffCoverage,
			'settingsDetails' => $result,
			'customGroupEmployeeId' => $customGroupEmployeeId,
			'attendanceRange' => $attendanceRange,
			'employeeDetails'=> $organizeEmployeeDetails,
			// 'shortTimeOffDetails' => $shortTimeOffDetails,
		);
		return $settingsResponse;
    }

/**
 * Get short time off details for a given employee id and date range.
 *
 * @param int|array $employeeId
 * @param string $startDate
 * @param string $endDate
 * @return array
 */
	public function getShortTimeOffEmpDetails($employeeId,$startDate,$endDate)
	{
		$qryShortTimeDetails = $this->_db->select()->from(array('ST'=>$this->_ehrTables->shortTimeOff),array('ST.Employee_Id','ST.Short_Time_Off_Id','ST.Total_Hours','ST.Short_Time_Off_Date'))
										->where('ST.Employee_Id IN (?)', $employeeId)
										->where('ST.Request_For = ?', "Permission")
										->where('ST.Approval_Status IN (?)', array('Applied', 'Approved', 'Cancel Applied','Returned'))
										->where('ST.Short_Time_Off_Date >= ?', $startDate)
										->where('ST.Short_Time_Off_Date <= ?', $endDate);
        $shortTimeOffDetails = $this->_db->fetchAll($qryShortTimeDetails);

		$organizeShortTimeOffEmployeeDetails = $this->_dbCommonFun->organizeDataByEmployeeIdAndDate($shortTimeOffDetails,'Employee_Id');
		unset($shortTimeOffDetails);
		return $organizeShortTimeOffEmployeeDetails;
	}


/**
 * Return the short time off period based on attendance range and attendance date.
 * 
 * @param array $attendanceRange
 * @param string $attendanceDate
 * @return array
 */
	public function getShortTimeOffPeriod($attendanceRange,$attendanceDate)
	{
		$attendanceProcessFromStartDate = $attendanceRange['attendanceProcessFromStartDate'];
		$attendanceProcessFromEndDate   = $attendanceRange['attendanceProcessFromEndDate'];
		$attendanceProcessToStartDate   = $attendanceRange['attendanceProcessToStartDate'];
		$attendanceProcessToEndDate     = $attendanceRange['attendanceProcessToEndDate'];
			
		if($attendanceProcessFromStartDate <= $attendanceDate && $attendanceProcessFromEndDate >= $attendanceDate)
		{
			$startDate = $attendanceProcessFromStartDate;
			$endDate   = $attendanceProcessFromEndDate;
			return array('startDate'=>$startDate,'endDate'=>$endDate);
		}
		else if($attendanceProcessToStartDate <= $attendanceDate && $attendanceProcessToEndDate >= $attendanceDate)
		{
			$startDate = $attendanceProcessToStartDate;
			$endDate   = $attendanceProcessToEndDate;
			return array('startDate'=>$startDate,'endDate'=>$endDate);
		}
		return '';
	}

/**
 * Adds a short time off entry for late attendance based on the employee's configuration and attendance details.
 *
 * @param int $employeeId The ID of the employee.
 * @param array $shortTimeOffConfiguration Configuration details for short time off, including settings and coverage.
 * @param string $regularFromTime The regular start time for the employee's shift.
 * @param string $attendancePunchInDateTime The actual punch-in time for attendance.
 * @return string Returns 'Yes' if short time off was applied successfully, otherwise 'No'.
 */
	public function addLateAttendanceShortTimeOff($employeeId,$shortTimeOffConfiguration,$regularFromTime,$attendancePunchInDateTime)
	{
		$attendanceDate 				= date('Y-m-d', strtotime($regularFromTime));
		$addShortTimeOff 				= 'No';
		$shortTimeOffSettings 			= $shortTimeOffConfiguration['settingsDetails'];
		$shortTimeOffCoverage 			= $shortTimeOffConfiguration['coverage'];
		$employeeDetailsByEmployeeId    = $shortTimeOffConfiguration['employeeDetails'];
		$employeeDetails  				= $this->_dbCommonFun->ensureArray($employeeDetailsByEmployeeId, $employeeId);
		//shorttimeoff record we are getting before the loop when the record add while processing not considered.
		// $shortTimeOffByEmployeeId    	= $shortTimeOffConfiguration['shortTimeOffDetails'];
		// $shortTimeOffDetails  			= $this->_dbCommonFun->ensureArray($shortTimeOffByEmployeeId, $employeeId);

		$validEmployee=false;	
		if(strtolower($shortTimeOffCoverage)=='organization')
		{
            $validEmployee=true;
		}
		else
		{
			if(!empty($shortTimeOffConfiguration['customGroupEmployeeId']))
			{
				if(in_array($employeeId, $shortTimeOffConfiguration['customGroupEmployeeId']))
				{
					$validEmployee=true;	
				}
			}
		}

		$startTime = new DateTime($regularFromTime);
		$endTime   = new DateTime($attendancePunchInDateTime);
		$interval  = $startTime->diff($endTime);

		// Extract the difference in hours and minutes
		$currentShortTimeOffInMinutes = ($interval->h * 60) + $interval->i;

		if(!empty($shortTimeOffSettings) && !empty($validEmployee))
		{
			$maxShortTimePerRequest 	 = $shortTimeOffSettings['Max_Short_Time_Per_Request'];
			$minShortTimePerRequest 	 = $shortTimeOffSettings['Min_Short_Time_Per_Request'];

			$shortTimeOffPeriod  = $this->getShortTimeOffPeriod($shortTimeOffConfiguration['attendanceRange'],$attendanceDate);
			$shortTimeOffCount   = 1; 
			//we will enable this when we process the attendance record for single day
			// $shortTimeOffDetails = $shortTimeOffConfiguration['shortTimeOffDetails'];
			$shortTimeOffByEmployeeId		= $this->getShortTimeOffEmpDetails(array($employeeId),$shortTimeOffPeriod['startDate'],$shortTimeOffPeriod['endDate']);
			$shortTimeOffDetails  			= $this->_dbCommonFun->ensureArray($shortTimeOffByEmployeeId, $employeeId);
			if(!empty($shortTimeOffDetails))
			{
				foreach($shortTimeOffDetails as $shortTimeOff)
				{
					if($shortTimeOff['Short_Time_Off_Date'] >= $shortTimeOffPeriod['startDate'] && $shortTimeOff['Short_Time_Off_Date'] <= $shortTimeOffPeriod['endDate'])
					{
						$shortTimeOffCount++;
					}
				}
			}

			$activationDate='';
			$isShortTimeActivated=0;	
			$shortTimeOffActivationAfterDays = $shortTimeOffSettings['Short_Time_Off_Activation_After'];
			if($shortTimeOffActivationAfterDays > 0 ){
				if(!empty($employeeDetails))
				{
					$employeeDateOfJoin = $employeeDetails[0]['Date_Of_Join'];
					$shortTimeOffDate	= $attendanceDate;
					$activationDate 	= date('Y-m-d', strtotime("+".$shortTimeOffActivationAfterDays." day",strtotime($employeeDateOfJoin)));
					if(strtotime($shortTimeOffDate) >= strtotime($activationDate)){
						$isShortTimeActivated=1;
					}else{
						$isShortTimeActivated=0;
					}
				}
			}else{
				$isShortTimeActivated=1;
			}
			
			if($isShortTimeActivated == 1){
				if(($shortTimeOffSettings['Limit_By'] == 'Request'  && ($currentShortTimeOffInMinutes >= $minShortTimePerRequest && $currentShortTimeOffInMinutes <= $maxShortTimePerRequest)))
				{
					if($shortTimeOffSettings['Limit_By'] == 'Request' && (($shortTimeOffCount) <= $shortTimeOffSettings['Maximum_Limit'])) 
					{
						if(!empty($employeeDetails[0]['Manager_Id']))
						{
                           $shortTimeOffForwardTo =  $employeeDetails[0]['Manager_Id'];
						}
						else if(!empty($shortTimeOffConfiguration['Admin_Id']) && $shortTimeOffConfiguration['Admin_Id']!=NULL)
						{
							$shortTimeOffForwardTo = $shortTimeOffConfiguration['Admin_Id'];
						}

						if(empty($shortTimeOffForwardTo))
						{
							$shortTimeOffForwardTo = $employeeId;
						}

						$newTimestamp = strtotime($regularFromTime) + ($maxShortTimePerRequest * 60); // Convert minutes to seconds
						// Convert the new timestamp back to a formatted DateTime string
						$regularEndTime = date('Y-m-d H:i:s', $newTimestamp);

						// Convert to hours, minutes, and seconds
						$hours = floor($maxShortTimePerRequest / 60);
						$minutes = $maxShortTimePerRequest % 60;
						$seconds = 0; // No seconds are specified in the input

						// Format as HH:MM:SS
						$totalHours = sprintf("%02d:%02d:%02d", $hours, $minutes, $seconds);

						$shortTimeOffArr = array('Short_Time_Off_Id'=> 0,
												'Employee_Id'       => $employeeId,
												'Approver_Id'       => $shortTimeOffForwardTo,
												'Request_For'       => 'Permission',
												'Short_Time_Off_Date'=> $attendanceDate,
												'Start_Date_Time'   => $regularFromTime,
												'End_Date_Time'     => $regularEndTime,
												'Total_Hours'       => $totalHours,
												'Reason'            => 'Late Attendance',
												'Alternate_Person'  => $employeeId,
												'Contact_Details'   => $employeeDetails[0]['Mobile_No'],
												'Approval_Status'   => "Approved",
											    'Added_On' 			=> date('Y-m-d H:i:s'),
												'Added_By' 			=> $shortTimeOffConfiguration['Session_Id']);

						$inserted = $this->_db->insert($this->_ehrTables->shortTimeOff, $shortTimeOffArr);
						if(!empty($inserted))
						{
							$addShortTimeOff ='Yes';
						}
					}
				}
			}
		}

		return $addShortTimeOff;

	}

    /**
     * Checks if an auto short time off or late attendance is associated with a given attendance record.
     *
     * This function verifies whether the attendance record identified by the 'Attendance_Id' in the provided
     * array has a late attendance or auto short time off associated with it. If the punch-in date and time
     * from the attendance array differ from the existing record and either condition exists, an error message
     * is returned indicating the restriction.
     *
     * @param array $attendanceArray Array containing details of the attendance record, including 'Attendance_Id'
     *                               and punch-in date and time.
     * @return array|string Returns an associative array with 'success', 'msg', and 'type' keys if there is an
     *                      association that prevents changing the punch-in time. Otherwise, returns an empty string.
     */
	public function getAutoShortTimeOffLateAttendanceExist($attendanceArray)
	{
		$errorMessage = '';
		if(!empty($attendanceArray['Attendance_Id']))
		{
			$attendanceRow 		   = $this->viewAttendance($attendanceArray['Attendance_Id']);
			$attendancePunchInDate = date('Y-m-d H:i',strtotime($attendanceRow['DPunchIn_Date'] .' '. $attendanceRow['PunchIn_Time']));
			$currentPunchInDate    = date('Y-m-d H:i',strtotime($attendanceArray['PunchIn_Date'] .' '. $attendanceArray['PunchIn_Time']));

			if(strtotime($attendancePunchInDate)!= strtotime($currentPunchInDate))
			{
				if($attendanceRow['Late_Attendance'] > 0)
				{
					$errorMessage = array('success' => false, 'msg'=>'Late attendance is associated with this record, so the punch-in time cannot be changed', 'type'=>'info');
				}
				else if($attendanceRow['Auto_Short_Time_Off']=='Yes')
				{
					$errorMessage = array('success' => false, 'msg'=>'Auto short time off is associated with this record, so the punch-in time cannot be changed', 'type'=>'info');
				}
			}
		}
		return $errorMessage;
	}

	/**
	 * Get employee IDs based on work schedule types
	 * @param array $workScheduleIds Array of work schedule IDs to filter by
	 * @param string $processFromDate Start date for shift roster filtering
	 * @param string $processToDate End date for shift roster filtering
	 * @return array Array of employee IDs
	 */
	public function getEmployeeIdsByWorkSchedules($workScheduleIds, $processFromDate, $processToDate)
	{
		$employeeIds = array();

		if(empty($workScheduleIds) || !is_array($workScheduleIds))
		{
			return $employeeIds;
		}

		// Get non-shift roster employees (Employee Level work schedule)
		// These employees have work_schedule = 'Employee Level' in employee_type table
		// and their work schedule is defined in emp_job.work_schedule_id
		$qryNonShiftEmployees = $this->_db->select()
			->from(array('EJ' => $this->_ehrTables->empJob), array('EJ.Employee_Id'))
			->joinInner(array('ET' => $this->_ehrTables->empType), 'EJ.EmpType_Id = ET.EmpType_Id', array())
			->joinInner(array('EP' => $this->_ehrTables->empPersonal), 'EJ.Employee_Id = EP.Employee_Id', array())
			->where('ET.Work_Schedule = ?', 'Employee Level')
			->where('EJ.Work_Schedule IN (?)', $workScheduleIds)
			->where('EP.Form_Status = ?', 1);

		$nonShiftEmployees = $this->_db->fetchCol($qryNonShiftEmployees);
		if(!empty($nonShiftEmployees))
		{
			$employeeIds = array_merge($employeeIds, $nonShiftEmployees);
		}

		// Get shift roster employees (Shift Roster work schedule)
		// These employees have work_schedule = 'Shift Roster' in employee_type table
		// and their work schedule is defined through shift_emp_mapping and emp_shift_type tables
		$qryShiftEmployees = $this->_db->select()
			->from(array('SEM' => $this->_ehrTables->shiftEmpMapping), array('SEM.Employee_Id'))
			->joinInner(array('EST' => $this->_ehrTables->empShiftType), 'SEM.Shift_Type_Id = EST.Shift_Type_Id', array())
			->joinInner(array('EJ' => $this->_ehrTables->empJob), 'SEM.Employee_Id = EJ.Employee_Id', array())
			->joinInner(array('ET' => $this->_ehrTables->empType), 'EJ.EmpType_Id = ET.EmpType_Id', array())
			->joinInner(array('EP' => $this->_ehrTables->empPersonal), 'EJ.Employee_Id = EP.Employee_Id', array())
			->where('ET.Work_Schedule = ?', 'Shift Roster')
			->where('EST.WorkSchedule_Id IN (?)', $workScheduleIds)
			->where('EP.Form_Status = ?', 1)
			->where('SEM.Shift_Start_Date >= ?', $processFromDate)
			->where('SEM.Shift_End_Date <= ?', $processToDate)
			->group('SEM.Employee_Id');

		$shiftEmployees = $this->_db->fetchCol($qryShiftEmployees);
		if(!empty($shiftEmployees))
		{
			$employeeIds = array_merge($employeeIds, $shiftEmployees);
		}

		// Remove duplicates and return unique employee IDs
		return array_unique($employeeIds);
	}

	public function __destruct()
    {

    }
}