<?php
    $ehrTables    = new Application_Model_DbTable_Ehr();
    /* Get the instance details to present or hide the landing page */
    $instancesDetails = $this->instancesDetails;

    $isDomain       = Zend_Registry::get('Domain');
    $isDomainArray  = explode(".",$isDomain);
    $orgName        = $ehrTables->organizationName();
    $domainDetails  = $ehrTables->domainDetails();
    $orgCode        = $ehrTables->getOrgCode();
    
    $this->headTitle()->setSeparator(' - ');
    $this->headTitle($orgName . ' | '.strtoupper($isDomainArray[0]));

    $termsOfUse     = $domainDetails['Terms_Link'];
    $privacyPolicy  = $domainDetails['Privacy_Policy_Link'];

    /* If the multi instance is mapped, then present the landing page */ 
    $socialMediaURls = $ehrTables->getSocialMediaURls();
?>
    
        <!DOCTYPE html>
        <html lang="en">
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <meta content="" name="description" />
                <meta content="" name="author" />
                
                <!--Favicon-->
                <link rel="apple-touch-icon" sizes="180x180" href="<?php echo $this->baseUrl('/apple-touch-icon.png?v=9B9bjrPr00'); ?>">
                <link rel="icon" type="image/png" sizes="32x32" href="<?php echo $this->baseUrl('/favicon-32x32.png?v=9B9bjrPr00'); ?>">
                <link rel="icon" type="image/png" sizes="16x16" href="<?php echo $this->baseUrl('/favicon-16x16.png?v=9B9bjrPr00'); ?>">
                <link rel="manifest" href="<?php echo $this->baseUrl('/site.webmanifest?v=9B9bjrPr00'); ?>">
                <link rel="mask-icon" href="<?php echo $this->baseUrl('/safari-pinned-tab.svg?v=9B9bjrPr00'); ?>" color="#5bbad5">
                <link rel="shortcut icon" href="<?php echo $this->baseUrl('/favicon.ico?v=9B9bjrPr00'); ?>">
                <meta name="apple-mobile-web-app-title" content="<?php echo strtoupper($isDomainArray[0]); ?>">
                <meta name="application-name" content="<?php echo strtoupper($isDomainArray[0]); ?>">
                <meta name="msapplication-TileColor" content="#da532c">
                <meta name="theme-color" content="#ffffff">
                
                <meta name="msapplication-TileImage" content="<?php echo $this->baseUrl('/mstile-70x70.png'); ?>">
                <meta name="msapplication-TileImage" content="<?php echo $this->baseUrl('/mstile-144x144.png'); ?>">
                <meta name="msapplication-TileImage" content="<?php echo $this->baseUrl('/mstile-150x150.png'); ?>">
                <meta name="msapplication-TileImage" content="<?php echo $this->baseUrl('/mstile-310x150.png'); ?>">
                <meta name="msapplication-TileImage" content="<?php echo $this->baseUrl('/mstile-310x310.png'); ?>">
                <meta name="msapplication-TileImage" content="<?php echo $this->baseUrl('/browserconfig.xml'); ?>">
                <!--Favicon END-->
                
                <!--Title Start-->
                <?php echo $this->headTitle(); ?>
                <!--Title End-->
                
                <!-- BASE CSS -->
                <link href="assets/global/css/multi-instance-landing-page/bootstrap.min.css" rel="stylesheet">
                <link href="assets/global/css/multi-instance-landing-page/style.css" rel="stylesheet">
                <link href="assets/global/css/multi-instance-landing-page/menu.css" rel="stylesheet">
                <link href="assets/global/css/multi-instance-landing-page/vendors.min.css" rel="stylesheet">
                <link href="assets/global/css/multi-instance-landing-page/icon_fonts/css/all_icons.min.css" rel="stylesheet">
                <link href="assets/global/css/multi-instance-landing-page/skins/square/grey.css" rel="stylesheet">

                <script src="assets/global/js/multi-instance-landing-page/modernizr.js" defer></script>
                <!-- Modernizr -->
            </head>
            <!-- head -->

            <body>

                <div id="preloader">
                    <div data-loader="circle-side"></div>
                </div><!-- /Preload -->
                    <div id="wizard_container">
                        <form name="multiInstanceLandingPage" id="wrapped" method="POST">
                            <input id="website" name="website" type="text" value="">
                            <div id="middle-wizard">
                                <div class="step" data-state="landingPage">

                                    <header>
                                        <div class="container-fluid">
                                            <div class="row">
                                                <div class="col-3">
                                                    <div id="logo_home">
                                                        <h1><a href="<?php echo $socialMediaURls['websiteURL'];?>" rel="noopener noreferrer" target="_blank" aria-label="websiteURL"></a></h1>
                                                    </div>
                                                </div>
                                                <div class="col-9">
                                                    <div id="social">
                                                        <ul>
                                                            <?php  echo '<li><a href="'.$socialMediaURls['facebookURL'].'" rel="noopener noreferrer" target="_blank" aria-label="facebookURL"><i class="icon-facebook"></i></a></li>'.
                                                            '<li><a href="'.$socialMediaURls['twitterURL'].'" rel="noopener noreferrer" target="_blank" aria-label="twitterURL"><i class="icon-twitter"></i></a></li>'.
                                                            '<li><a href="'.$socialMediaURls['googleURL'].'" rel="noopener noreferrer" target="_blank" aria-label="googleURL"><i class="icon-google"></i></a></li>'.
                                                            '<li><a href="'.$socialMediaURls['linkedinURL'].'" rel="noopener noreferrer" target="_blank" aria-label="linkedinURL"><i class="icon-linkedin"></i></a></li>'; ?>
                                                        </ul>
                                                    </div>
                                                    <!-- /social -->
                                                </div>
                                            </div>
                                        </div>
                                    </header>
                                    <!-- /Header -->

                                    <main>
                                        <div class="container wizard-container">
                                            <div class="wizard_title">
                                                <h3>Choose your organization</h3>
                                                <?php echo '<p>'.$orgName.'</p>'; ?>
                                            </div>
                                            <div class="row justify-content-center" >
                                                <?php foreach($instancesDetails as $key => $row) { ?>
                                                    <div class="col-lg-4">
                                                        <div class="item organization-card multi-instance-cards" orgCode="<?php echo $row['Org_Code']; ?>" parentOrgCode="<?php echo $orgCode; ?>" orgURL="<?php echo $row['Org_Code'].".".$isDomain; ?>">
                                                            <?php echo '<input id="answer_'.$key.'" type="radio" name="branch_group" value="Seo-Optimization" class="required">'.
                                                            '<label for="answer_'.$key.'" class="multi-instance-card">' ?>
                                                                <span>
                                                                    <figure><img src="<?php echo !empty($row['Org_Logo']) ? $row['Org_Logo'] : 'images/landing-page-default-logo.webp'; ?>" onerror="this.onerror=null; this.src='<?php echo !empty($row['Org_Logo']) ? $row['Org_Logo'] : 'images/landing-page-default-logo.png'; ?>'" alt="logo"></figure>
                                                                </span>
                                                                    <strong class="multi-instance-text"><?php echo $row['Org_Name']; ?></strong>
                                                                    <div class="multi-instance-text"><?php echo $row['Org_Description']; ?></div>
                                                            </label>
                                                        </div>
                                                    </div>
                                                <?php } ?>
                                            </div>
                                            <!-- /row-->

                                    <footer>
                                    <?php if($domainDetails['Copy_Right']==1) { ?>
                                        <div class="container clearfix">
                                            <ul>
                                                <li><a href="<?php echo $domainDetails['Support_Link']; ?>" class="animated_link" rel="noopener noreferrer" target="_blank">Support</a></li>
                                                <li><a href="<?php echo $termsOfUse; ?>" class="animated_link" rel="noopener noreferrer" target="_blank">Terms of use</a></li>
                                                <li><a href="<?php echo $privacyPolicy; ?>" class="animated_link" rel="noopener noreferrer"target="_blank">Privacy Policy</a></li>
                                            </ul>
                                            <p>&copy; 2013-<?php echo date("Y"); ?><a href="http://www.capricetech.com" rel="noopener noreferrer" target="_blank"> Caprice Cloud Solutions Pvt Ltd</a>.<span> All rights reserved.</span></p>
                                        </div>
                                    <?php } else { ?> 
                                        <ul style="text-align:center;float:none;">
                                        <li><a href="<?php echo $domainDetails['Support_Link']; ?>" class="animated_link" rel="noopener noreferrer" target="_blank">Support</a></li>
                                        <li><a href="<?php echo $termsOfUse; ?>" class="animated_link" rel="noopener noreferrer"target="_blank">Terms of use</a></li>
                                        <li><a href="<?php echo $privacyPolicy; ?>" class="animated_link" rel="noopener noreferrer" target="_blank">Privacy Policy</a></li>
                                        </ul>
                                    <?php } ?>
                                        
                                    </footer>
                                    <!-- /footer -->

                                    </div>
                                        <!-- /Container -->
                                    </main>
                                    <!-- /main -->

                                </div>
                                <!-- /step-->
                            </div>
                            <!-- /middle-wizard -->
                        </form>
                    </div>
                    <!-- /Wizard container -->

                <!-- COMMON SCRIPTS -->
                <script src="assets/global/js/multi-instance-landing-page/jquery-2.2.4.min.js"></script>
                <script src="assets/global/js/multi-instance-landing-page/common_scripts.min.js"></script>
                <script src="assets/global/js/multi-instance-landing-page/menu.js"></script>
                <script src="assets/global/js/multi-instance-landing-page/main.js?v=2"></script>
                <script src="assets/global/js/multi-instance-landing-page/wizard_func_single_branch.js"></script>
            </body>
        </html>
   

