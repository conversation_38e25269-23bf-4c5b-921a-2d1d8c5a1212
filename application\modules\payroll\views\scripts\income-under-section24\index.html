<!DOCTYPE html>
<html>
	<head>
		<!-- common scripts -->
		<script src="https://cdn.jsdelivr.net/npm/vue@2/dist/vue.min.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/vuetify@2.2.3/dist/vuetify.min.js"></script>
		<script src="https://cdn.jsdelivr.net/gh/f/graphql.js@master/graphql.min.js"></script>
		<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.5.3/jspdf.debug.js"></script>
		<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.9.1/html2pdf.bundle.min.js"></script>
		<script src="https://mozilla.github.io/pdf.js/build/pdf.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/vue-cookies@1.6.1/vue-cookies.min.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/v-tooltip@2.0.2"></script>
		<script src="https://cdn.jsdelivr.net/npm/vee-validate@2.x/dist/vee-validate.js"></script> 
		<script src="https://cdn.jsdelivr.net/npm/filepond@4.30.6/dist/filepond.min.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/vue-filepond@6.0.3/dist/vue-filepond.min.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/filepond-plugin-file-validate-size@2.2.8/dist/filepond-plugin-file-validate-size.min.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/filepond-plugin-file-validate-type@1.2.9/dist/filepond-plugin-file-validate-type.min.js"></script>
		<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.4.1/jspdf.min.js"></script>
		<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/2.3.4/jspdf.plugin.autotable.min.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/html2canvas@1.0.0-rc.5/dist/html2canvas.min.js"></script>
		
		<!-- styles -->
		<link href="https://cdn.jsdelivr.net/npm/vuetify@2.2.11/dist/vuetify.min.css" rel="stylesheet" />
		<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/filepond@4.30.6/dist/filepond.min.css">
		<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@mdi/font@latest/css/materialdesignicons.min.css">
		<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.12.1/css/all.min.css" rel="stylesheet" />
		<link href="https://fonts.googleapis.com/icon?family=Material+Icons&display=block" rel="stylesheet" />
	
		<script>
			// Register the plugin with FilePond
			FilePond.registerPlugin(FilePondPluginFileValidateSize);
			FilePond.registerPlugin(FilePondPluginFileValidateType);

		</script>
	</head> 
	<body>
		<!--  html content -->
		<div id="incomeUnderSection24" class="col-md-12 portlets p-10">
			<template>
				<v-app class="bg_grey_lighten1">
					<!-- desktop topbar design -->
					<v-card class="bg_grey_lighten1 hidden-sm-and-down" :class="topBarClass" flat height="4em">
						<v-toolbar color="grey lighten-5">
							<span class="padding-2em"></span>
							<v-tabs v-model="currentTab" background-color="#f9f9f9">
									<v-tab v-if="isTaxDeclarationHasAccess" href="#tab-1" class="active_tab_bg" @click="fnRedirect('tax-declarations')">
										<div style="text-transform: capitalize !important;" class="font-weight-bold">Tax Declarations</div>
									</v-tab>
									<v-tab href="#tab-2" class="active_tab_bg">
										<div style="text-transform: capitalize !important;" class="font-weight-bold">Income Under Section24</div>
									</v-tab>
									<v-tab v-if="isPOIHasAccess" href="#tab-3" class="active_tab_bg" @click="fnRedirect('proof-of-investment')">
										<div style="text-transform: capitalize !important;" class="font-weight-bold">Proof Of Investment</div>
									</v-tab>
									<v-row style="margin-right: 100px;" class="topbar-right-action">
										<v-col cols="12" class="d-flex justify-end align-center" style="margin-top: -10px;">
											<div class="secondaryColor p-r-10 font-weight-bold">Assessment Year:</div>
											<div class="m-r-10">
												<v-autocomplete
													@change="listDeclarations()"
													v-model="assessmentYear"
													style="max-width: 70px;margin-top: 12px;"
													item-text="Year"
													item-value="Year"
													:items="assessmentYearList"
												></v-autocomplete>
											</div>
											<v-menu fixed bottom offset-y offset-x v-model="filterOpen" :close-on-content-click="false" 
												class="filter-menu-position" max-height="400" min-width="300">
													<template v-slot:activator="{ on }">
														<v-avatar size="35" color="primary" v-on="on" class="cursor_pointer_class">
																<i class="fa fa-filter filter-icon-property"></i>
														</v-avatar>
													</template>
													<v-card class="p-10" >
														<div class="d-flex justify-end">
															<v-icon color="primary" @click="filterOpen=!filterOpen" class="p-r-10 p-t-10">
																close</v-icon>
														</div>
														<v-row>
															<v-col cols="12">
																<v-autocomplete background-color="white" deletable-chips multiple
																	v-model="selectedStatus" :items="statusList" dense chips
																	filled label="Status">
																</v-autocomplete>
															</v-col>
														</v-row>
														<v-btn color="primary white--text" rounded @click="fnApplyResetFilter('apply')">Apply</v-btn>
														<v-btn color="primary" rounded outlined @click="fnApplyResetFilter('reset')">Reset</v-btn>
												</v-card>
											</v-menu>
										</v-col>
									</v-row>
							</v-tabs>
						</v-toolbar>
					</v-card>
					<!-- mobile topbar design -->
					<v-card class="bg_grey_lighten1 hidden-md-and-up" :class="topBarClass" flat height="4em">
						<v-toolbar color="grey lighten-5">
							<v-tabs v-model="currentTab" centered show-arrows>
								<v-tab v-if="isTaxDeclarationHasAccess" href="#tab-1" class="active_tab_bg" @click="fnRedirect('tax-declarations')">
									<div style="text-transform: capitalize !important;" class="font-weight-bold">Tax Declarations</div>
								</v-tab>
								<v-tab href="#tab-2" class="active_tab_bg">
									<div style="text-transform: capitalize !important;" class="font-weight-bold">Income Under Section24</div>
								</v-tab>
								<v-tab v-if="isPOIHasAccess" href="#tab-3" class="active_tab_bg" @click="fnRedirect('proof-of-investment')">
									<div style="text-transform: capitalize !important;" class="font-weight-bold">Proof Of Investment</div>
								</v-tab>
							</v-tabs>
						</v-toolbar>
					</v-card>
					<!-- mobile footer design with filter-->
					<v-bottom-navigation fixed class="hidden-md-and-up" color="teal" elevation="15">
						<v-row class="d-flex justify-center">
							<v-col cols="8" class="d-flex align-center justify-end" style="margin-top: -20px">
								<div class="secondaryColor p-r-10 font-weight-bold" style="padding: 10px;">Assessment Year:</div>
								<div class="m-r-10">
									<v-autocomplete
										@change="listDeclarations()"
										v-model="assessmentYear"
										style="max-width: 100px;margin-top: 10px;"
										item-text="Year"
										item-value="Year"
										:items="assessmentYearList"
									></v-autocomplete>
								</div>
							</v-col>
							<v-col cols="4" class="text-xs-center d-flex">
								<v-menu fixed top offset-y offset-x v-model="filterOpenInMobile" :close-on-content-click="false" 
									class="filter-menu-position" :min-width="windowWidth > 600 ? '50%' : '100%'">
										<template v-slot:activator="{ on }">
											<v-avatar size="35" color="primary" v-on="on" class="cursor_pointer_class">
													<i class="fa fa-filter filter-icon-property"></i>
											</v-avatar>
										</template>
										<v-card class="p-10">
											<div class="d-flex justify-end">
												<v-icon color="primary" @click="filterOpenInMobile=!filterOpenInMobile" class="p-r-10 p-t-10">
													close</v-icon>
											</div>
											<v-row class="d-flex justify-center">
												<v-col cols="12" class="d-flex justify-center">
													<v-autocomplete background-color="white" deletable-chips multiple
														v-model="selectedStatus" :items="statusList" dense chips
														filled label="Status" style="max-width: 300px;">
													</v-autocomplete>
												</v-col>
												<v-btn color="primary white--text" rounded @click="fnApplyResetFilter('apply')">Apply</v-btn>
												<v-btn color="primary" rounded outlined @click="fnApplyResetFilter('reset')">Reset</v-btn>	
											</v-row>
									</v-card>
								</v-menu>
							</v-col>
						</v-row>
					</v-bottom-navigation>
					<section v-if="!isAccessDenied && !incomeUnderSection24FetchError">
						<list-income-under-section 
								ref="incomeUnder24"
								:user-type="author" 
								:list-response="listData" 
								:render-count="listRenderCount" 
								:self-occupied-initial-count="initialSelfOccupiedCount"
								v-if="showViewPage && !loadSkeleton" 
								:currency-symbol="currencySymbol"
								:income-source-types = "incomeSourceTypes"
								:tax-declaration-base-url = "taxDeclarationBaseUrl"
								:ats-base-url = "atsBaseURL"
								:hrapp-base-url="baseUrl"
								:api-headers="apiHeaders"
								:org-code = "orgCode"
								:employee-id = "employeeId"
								:emp-taxable-income = "empTaxableIncome"
								:emp-annual-tax = "empAnnualTax"
								:view-tax-calculation = "viewTaxCalculation"
								:tax-sheet-data = "taxSheetData"
								:current-assessment-year = "currentAssessmentYear"
								:it-declaration-settings-status="itDeclarationSettingsStatus"
								:selected-assessment-year="assessmentYear"
								:roles-response = "rolesResponse"
								:is-add-success = "isAddSuccess"
								:is-edit-success = "isEditSuccess"
								:success-type   = "successType"
								:success-data = "successData"
								:form-source = "formSource"
								:compare-tax-regime-details = "compareTaxRegimeDetails"
								:show-tax-regime-change = "showTaxRegimeChange"
								:enable-view-tds-button = "enableViewTDSButton"
								:tax-regime-change-restriction-msg="taxRegimeChangeRestrictionMsg"
								:enable-tax-regime-change-button="enableTaxRegimeChangeButton"
								:payroll-country="countryCode"
								@change-employee="fnChangeEmployee($event)"
 								@handle-error="handleError($event)"
								@handle-document-error="handleDocumentError($event)"
								@handle-add-edit-error="handleAddEditError($event)" 
								@open-close-view-modal="openCloseViewModal($event)"
								@handle-add-edit-success = "handleAddEditSuccess($event)"
								@handle-warning-msg="handleWarningMessages($event)"
								@handle-tax-sheet-error="handleTaxSheetError()"
								@handle-tax-regime-update-response="handleTaxRegimeUpdateResponse($event)"
								@handle-tax-compare="handleCompareRegime($event)"
								>								
							</list-income-under-section>
							<employee-list-modal 
							v-if="showEmpModal"
							ref="empListModal" 
							:show-modal="showEmpModal"
							:department-list="departmentList" 
							:designation-list="designationList"
							:location-list="locationList" 
							:emp-type-list="empTypeList" 
							:work-schedule-list="workScheduleList"
							:employees-list="employeesList"
							modal-title="Select Employee"
							:table-headers="empListHeader"
							:single-select="true"
							table-item-key="employee_id"
							no-match-image-name="income_initial_img"
							submit-button-text="Proceed"
							:show-filter-search="true"
							@apply-filter="fnApplyingFilterInEmpListPopup($event)"
							@close-employee-modal="fnCancelEmpListModal()"
							@proceed-on-modal="fnProceedInEmpListModal($event)"
						>
						</employee-list-modal>
						<section v-else-if="loadSkeleton" style="margin-top: 70px;padding: 30px;">
							<v-skeleton-loader
								ref="skeleton1"
								type="list-item-avatar-three-line"
								class="mx-auto"
							></v-skeleton-loader>
							<br>
							<br>
							<br>
							<v-skeleton-loader
								ref="skeleton2"
								type="table-thead"
								class="mx-auto"
							></v-skeleton-loader>
							<br>
							<div v-for="i in 3" :key="i">
								<v-skeleton-loader
									ref="skeleton2"
									type="list-item-avatar"
									class="mx-auto"
								></v-skeleton-loader>
								<br>
							</div>
						</section>
					</section>
					<!--Access Denied-->
					<access-denied-screen v-if="isAccessDenied"></access-denied-screen> 
					<!--Fetching Error-->
					<div v-if="incomeUnderSection24FetchError">
							<fetching-error-screen button-text="Retry" :content="errorContent" 
							:main-title="errorTitle" image-name="initial-fetch-error-image" 
							@button-click="retryFetching"></fetching-error-screen>
					</div>
					<!--custom snack bars for showing success and error messages -->
					<custom-snack-bar v-if="snackbar && snackBarMsg"
						:show-snack-bar="snackbar" 
						:snack-bar-msg="snackBarMsg" 
						show-emoji="false" 
						emoji=""
						:snack-bar-type="snackBarType"
						:snack-bar-position-top = "true"
						@close-snack-bar="snackbar = false">
					</custom-snack-bar>		
					<!-- custom loading -->
					<custom-loading-screen v-if="loadingScreen"></custom-loading-screen>
				</v-app>
			</template>
		</div>


		<!-- script content -->
		<script>
			let primaryColor, secondaryColor;
			if (!localStorage.getItem("brand_color")) {
				const { Primary_Color, Secondary_Color} = JSON.parse(localStorage.getItem("brand_color"));
				primaryColor = Primary_Color;
				secondaryColor = Secondary_Color;
			} else {
				primaryColor = '#260029';
				secondaryColor = '#ec407a';
			}
			// vue instance declarations
			var app = new Vue({
				el: '#incomeUnderSection24',
				vuetify: new Vuetify(
					{
						theme: {
							options: {
								customProperties: true,
							},
							themes: {
								light: {
									primary: primaryColor,
									secondary: secondaryColor,
									grey: '#9E9E9E',
									green : '#41ae57',
									blue : '#63759f'
								}
							}
						}
					}
				),
				components: {
                    FilePond: vueFilePond.default()
                },
				data() {
					return {
						taxDeclarationBaseUrl:  'https://api.'+localStorage.getItem('domain')+'/employee-taxation/graphql',
						atsBaseURL : 'https://api.'+localStorage.getItem('domain')+'/ats/graphql',
						taxAndStatutoryRO:  'https://api.'+localStorage.getItem('domain')+'/taxAndStatutory/rographql',
						partnerid: $cookies.get("partnerid"),
						dCode: $cookies.get("d_code"),
						bCode: $cookies.get("b_code"),
						employeeId: parseInt(localStorage.getItem('LoginEmpId'), 10),
						loadingScreen: false,
						showViewPage: false,
						loadSkeleton: false,
						// top bar variables
						topBarClass : 'income-under-top-bar',
						currentTab: 'tab-2',
						formSource : 'IncomeUS24',
						author: 'employee',
						windowWidth: 0,

						// static data
						staticData:"",
						assessmentYearList: [],
						assessmentYear: 0,
						currentAssessmentYear: 0,
						currencySymbol: "",
						incomeSourceTypes : [],
						itDeclarationSettingsStatus: "",

						// access rights variables
						roleView : 0,
						roleAdd  : 0,
						roleEdit : 0,
						roleDelete : 0,
						isAccessDenied : false,
						rolesResponse : {},
						isPOIHasAccess: false,
						isTaxDeclarationHasAccess: false,

						//snackbar props
						snackbar :false,
						snackBarType : '',
						snackBarMsg :'',

						//fetching error screen props
						errorTitle:'',
						errorContent:'',
						incomeUnderSection24FetchError:false,
						fetchErrorAccessRights: false,
						
						// list params
						userIp: "",
						listData: "",
						listRenderCount: 0,
						initialSelfOccupiedCount: 0,
						// filter data
						filterOpenInMobile: false,
						filterOpen: false,
						statusList: ['Applied', 'Approved', 'Declared', 'Reopened', 'Returned'],
						selectedStatus: [],

						documentError : false,

						// emp taxation data
						empTaxableIncome : '',
						empAnnualTax     : '',
						viewTaxCalculation : 0,
						taxSheetData : {},
						enableViewTDSButton: true,


						// add/edit actions
						isAddSuccess : false,
						isEditSuccess : false,
						successType   : '',
						successData : {},

						// employees popup variables
						chosenStatus : '',
						chosenType : '',
						chosenEmployeeIds : [],
						departmentList : [],
						designationList : [],
						locationList  : [],
						empTypeList : [],
						workScheduleList : [],
						showEmpModal: false,
						employeesList: [],
						empListHeader:[
							{
								text: 'Employee Id',
								value: 'user_defined_empId'
							},
							{
								text: 'Employee Name',
								align: 'left',
								value: 'employee_name'
							},
							{
								text: 'Designation',
								value: 'designation_name'
							},
							{
								text: 'Department',
								value: 'department_name'
							}
						],
						selectedDesignation: [],
						selectedDepartment: [],
						selectedEmpType: [],
						selectedLocation: [],
						selectedWorkSchedule: [],

						//Tax regime comparison details
						compareTaxRegimeDetails: {},
						showTaxRegimeChange: true,
						enableTaxRegimeChangeButton: false,
						taxRegimeChangeRestrictionMsg: '',
						countryCode: '',
					
					}
				},
				computed: {
					isLocalEnv() {
						let currentUrl = window.location.href;
						if (
							parseInt(localStorage.getItem("isProduction"), 10) === 0 ||
							currentUrl.includes("hrapponline")
						) {
							return true;
						} else {
							return false;
						}
					},
					//to get orgCode dynamically from the current url
					orgCode() {
						if (this.isLocalEnv) {
							return "happy"; // local db connection
						} else {
							let oCode1 = localStorage.getItem("orgCode");
							if (oCode1) {
								return oCode1;
							} else {
								let url = window.location.href;
								let urlNoProtocol = url.replace(/^https?:\/\//i, "");
								let oCode = urlNoProtocol.split(".");
								oCode = oCode[0];
								return oCode;
							}
						}
					},
					basePath() {
						if (localStorage.getItem('production') == 0) {
							return '/hrapponline/'
						} else {
							return '/'
						}
					},
					baseUrl(){
						var pathParts = location.pathname.split('/');
						if (localStorage.getItem('production') == 0) {
							var url = location.origin + '/' + pathParts[1].trim('/') + '/'; // http://localhost/hrapponline/
						} else {
							var url = location.origin + '/'; // http://subdomain.hrapp.co
						}
						return url;
					},
					apiHeaders() {
						let authorizationHeader = $cookies.get("accessToken") ? $cookies.get("accessToken") : null;
						let refreshTokenHeader = $cookies.get("refreshToken") ? $cookies.get("refreshToken") : null;
						return {
							'Content-Type': 'application/json',
							org_code: this.orgCode,
							Authorization: authorizationHeader,
							refresh_token: refreshTokenHeader,
							user_ip: this.userIp,
							partnerid: this.partnerid ? this.partnerid : "-",
							additional_headers: JSON.stringify(
								{
									org_code: this.orgCode,
									Authorization: authorizationHeader,
									refresh_token: refreshTokenHeader,
									user_ip: this.ipAddress,
									partnerid: this.partnerid ? this.partnerid : "-",
									d_code: this.dCode,
									b_code: this.bCode,
								}
							)
						}
					},
					graphQl(){
						return graphql(this.taxDeclarationBaseUrl, {
							method: 'POST',
							headers: this.apiHeaders,
							asJSON: true
						});
					},

					atsGraphQl(){
						return graphql(this.atsBaseURL, {
							method: 'POST',
							headers: this.apiHeaders,
							asJSON: true
						});
					},

					taxGraphql(){
						return graphql(this.taxAndStatutoryRO, {
							method: 'POST',
							headers: this.apiHeaders,
							asJSON: true
						});
					},
				},
				created() {
            		let ipAddressRestriction = localStorage.getItem('ipAddressRestriction');
				 	// function to get ip
					try{
						axios.get('https://api.ipify.org?format=json').then(response => { 
							this.userIp = response.data.ip;
						}).catch(error => {
							/* If the IP address API is not available, API URL is wrong or internet connection is not available,
							then the error.readyState will be 0 or 4  */
							if((error.readyState === 0 || error.readyState === 4) && ipAddressRestriction == 1) {
								this.handleWarningMessages("Unable to get the IP address. Please contact system Administrator.");
							} else {
								this.userIp = "IP Blocked by user";
							}
						})
					}catch{
						this.userIp = "";
					}
					window.$cookies.set("userIpAddress", this.ipAddress);
				},	
				// common error handling function
				errorCaptured() {
					this.snackbar = true;
					this.snackBarMsg = "Something went wrong while loading income under section24 from. Please try after some time.";
					this.snackBarType = "warning";
					return false;
				},
				mounted() {
					setTimeout(()=>{
						this.loadingScreen = true;
						this.fetchPOIFormAccess();
						this.fetchTaxDeclarationsFormAccess();
                    	this.fnCheckAccessRights();
						this.fetchPayrollCountry();
					},2000);

					this.$nextTick(function () {
						window.addEventListener('resize', this.getWindowWidth);
						//Init
						this.getWindowWidth();
					});
					
				},
				beforeDestroy() {
					window.removeEventListener('resize', this.getWindowWidth);
				},
				methods: {
					getWindowWidth(event) {
						this.windowWidth = document.documentElement.clientWidth;
						// hide and show of filter based on window size
						if (this.filterOpen && this.windowWidth <= 960) {
							this.filterOpen = false;
						}
						if (this.filterOpenInMobile && this.windowWidth > 960) {
							this.filterOpenInMobile = false;
						}
					},
					// function to redirect
					fnRedirect(path){
						this.loadingScreen = true;
						window.location.href = this.baseUrl + "payroll/" + path;
					},
					// check POI form access rights
					fetchPOIFormAccess() {
						var self = this;
						var accessRights = self.atsGraphQl(`mutation(
						$formName: String,
						$employeeId: Int!) {
							getAccessRights
								(
									formName: $formName,
									employeeId:$employeeId
								) 
								{
									errorCode message rights {
										Role_View Role_Add Role_Update Role_Delete Role_Optional_Choice Role_Hr_Group Role_Payroll_Group Is_Manager
									}
								}
							}
						`);
						accessRights({
							formName: 'Proof Of Investment',
							employeeId: self.employeeId
						})
						.then(function (response) {
							if (response) {
								var response = response.getAccessRights.rights;
								if(response.Role_View) {
									self.isPOIHasAccess = true;
								}
							}
						})
					},
					// check tax-declarations form access rights
					fetchTaxDeclarationsFormAccess() {
						var self = this;
						var accessRights = self.atsGraphQl(`mutation(
						$formName: String,
						$employeeId: Int!) {
							getAccessRights
								(
									formName: $formName,
									employeeId:$employeeId
								) 
								{
									errorCode message rights {
										Role_View Role_Add Role_Update Role_Delete Role_Optional_Choice Role_Hr_Group Role_Payroll_Group Is_Manager
									}
								}
							}
						`);
						accessRights({
							formName: 'Tax Declarations',
							employeeId: self.employeeId
						})
						.then(function (response) {
							if (response) {
								var response = response.getAccessRights.rights;
								if(response.Role_View) {
									self.isTaxDeclarationHasAccess = true;
								}
							}
						})
					},
					fetchPayrollCountry() {
						try {
							let self = this;
							self.loadingScreen = true;
							var payrollCountry = self.taxGraphql(`query 
							listPayrollGeneralSettings {
    							listPayrollGeneralSettings {
									errorCode
									message
									listPayrollGeneralSettingsData {
										Settings_Id
										Slab_Wise_PF
										Slab_Wise_NPS
										Country_Name
										Country_Code
									}
    							}
  							}`);
							payrollCountry()
							.then(function (response) {
								if(
								response && 
								response.listPayrollGeneralSettings && 
								response.listPayrollGeneralSettings.listPayrollGeneralSettingsData && 
								response.listPayrollGeneralSettings.listPayrollGeneralSettingsData.length
								) {
									let {Country_Code} = response.listPayrollGeneralSettings.listPayrollGeneralSettingsData[0];
									self.countryCode = Country_Code? Country_Code.toLowerCase() : 'in';
								}
							}).catch(function (error) {
								self.handleError(error, 1);
							})
						} catch (error) {
							self.handleError(error, 1);
						}
					},
					

					// check access rights
					fnCheckAccessRights() {
						try {
							var self = this;
							self.loadingScreen = true;
							self.fetchErrorAccessRights = false;
							var accessRights = self.atsGraphQl(`mutation(
							$formName: String,
							$employeeId: Int!) {
								getAccessRights
									(
										formName: $formName,
										employeeId:$employeeId
									) 
									{
										errorCode message rights {
											Role_View Role_Add Role_Update Role_Delete Role_Optional_Choice Role_Hr_Group Role_Payroll_Group Is_Manager
										}
									}
								}
							`);
							accessRights({
								formName: 'Income Under Section24',
								employeeId: self.employeeId
							})
							.then(function (response) {
								if (response) {
									// rights retrieved successfully
									var response = response.getAccessRights.rights;

									self.rolesResponse = response;

									self.roleView = response.Role_View;
									self.roleAdd = response.Role_Add;
									self.roleEdit = response.Role_Update;
									self.roleDelete = response.Role_Delete;

									self.loadingScreen = false;
									// check if the user has view access for Whitelisted IP
									if(response.Role_View) {
										self.isAccessDenied = false;
										self.incomeUnderSection24FetchError = false;
										// function to retrieve static data
										self.fnRetrieveStaticData();
									}
									else {
										self.isAccessDenied = true; //set access denied
									}
								}
							})
							.catch(function (accessRightsFetchError) {
								self.fetchErrorAccessRights = true;
								self.handleError(accessRightsFetchError,1);
							});
						}
						catch(accessRightsFetchError) {
							self.fetchErrorAccessRights = true;
							self.handleError(accessRightsFetchError,1);
						}
					},

					// handle BE error codes, functional, technical errors
					handleError(err,isListPage = 0,action='') {
						var self = this;
						self.loadingScreen = false;
						self.loadSkeleton = false;
						// handle BE error codes
						if (err && err.length > 0 && err[0] && err[0].message) {
							// error returned from backend
							var error = JSON.parse(err[0].message);
							var errorCode = error.errorCode;
							var errorMessage = error.message;

							switch (errorCode) {
								// technical errors
								case 705:
                    			case 706:
								case "_DB0000":
									if(isListPage){
										self.incomeUnderSection24FetchError = true;
										self.errorTitle = "Oops";
										self.errorContent = "It’s us ! There seems to be some technical difficulties while fetching the declarations. Please try after some time."									
									}
									else{
										self.snackbar = true;
										self.snackBarType = "warning";
										self.snackBarMsg = "Oops ! Sorry, This issue seems to be from our side. Would you mind trying it after some time or talk to your administrator ?"
									}
									break;
								case "PR0104" :
								case "PR0004" :
								
										self.snackbar = true;
										self.snackBarType = "warning";
										self.snackBarMsg = "Oops ! Sorry, This issue seems to be from our side. Would you mind trying it after some time or talk to your administrator ?"
									break;
								case "PR0111":
										self.snackbar = true;
										self.snackBarType = "warning";
										self.snackBarMsg = "Assessment year is closed and hence no amendments are allowed."
									break;
								// access denied
								case 752 :
									self.isAccessDenied = true;
									break;
								case 'DB0100' : 
									self.snackbar = true;
									self.snackBarType = "warning";
									self.snackBarMsg = "Sorry, you don't have access to view the declaration. Please contact your administrator."
									break;
								case 'DB0101' :
									self.snackbar = true;
									self.snackBarType = "warning";
									self.snackBarMsg = "Sorry, you don't have access to add the declaration. Please contact your administrator."
									break;
								case 'DB0102' :
									self.snackbar = true;
									self.snackBarType = "warning";
									self.snackBarMsg = "Sorry, you don't have access to update the declaration. Please contact your administrator."
									break;
								case 'DB0103' :
									self.snackbar = true;
									self.snackBarType = "warning";
									self.snackBarMsg = "Sorry, you don't have access to delete the declaration. Please contact your administrator."
									break;


								// set lock errors
								case 712:
								case 714:
								case 715:
								case 716:
									self.snackbar = true;
									self.snackBarType = "warning";
									self.snackBarMsg = errorMessage;
									break;

								// status rejection error or if invalid records are sent for deletion
								case 'PR0010' :
									self.snackbar = true;
									self.snackBarType = "warning";
									self.snackBarMsg = action === 'edit' ? "Cannot edit declarations in Applied/Approved status." : "Some of the records could not be deleted as they are opened in some other user sessions.";
									break;

								// partial deleted error
								case 'PR0011' :
									self.snackbar = true;
									self.snackBarType = "warning";
									self.snackBarMsg = "Some of the records could not be deleted as they are opened in some other user sessions.";
									break;
								
								// functional error
								case 760 : 
									self.snackbar = true;
									self.snackBarType = "warning";
									self.snackBarMsg = "Hmm., Something went wrong from our side. Would you mind trying it after some time or talk to your administrator?"
									break;
								case 'PF0112': 
									self.snackbar = true;
									self.snackBarType = "warning";
									self.snackBarMsg = "Error while processing the request to retrieve payroll general setting details."
									break;
								case 'PF0008': 
									self.snackbar = true;
									self.snackBarType = "warning";
									self.snackBarMsg = "An error occurred while attempting to retrieve the payroll general setting details. Please contact the platform administrator for assistance."
									break;
								
								case '_DB0001' :
								case '_DB0002' :
								case '_DB0104' :
								case '_UH0001' :
								case 751 :
								case "PR0103":
								case "PR0003":
								case "PR0101":
								case "PR0001":
								case "PR0108" :
								case "PR0008" :
								case "PR0109" :
								case "PR0009" :
								case "_EC0001": 
								case "PR0107" : 
								case "PR0007" :
								case "SGE0110": // Error while getting the field force details.
								case "SGE0111": // Error while getting the service provider employeeIds.
								default :
									if(isListPage) {
										self.incomeUnderSection24FetchError = true;
										self.errorTitle = "Oops";
										self.errorContent = "Something went wrong while fetching the declarations. Please try after some time."									
									}
									else {
										self.snackbar = true;
										self.snackBarType = "warning";
										self.snackBarMsg =  action === 'add' ? "Something went wrong while adding the declaration. Please try after some time." 
														                    : action === 'edit' ? "Something went wrong while updating the declaration. Please try after some time." 
																			: "Something went wrong. Please contact system administrator."
									}
									break;
							}
						}
						else {
							// if error in list page, show the fetch error screen
							if(isListPage) {
								self.incomeUnderSection24FetchError = true;
								self.errorTitle = 'Oops',
								self.errorContent = 'Something went wrong while fetching the declarations. Please try after some time.'
							}
							else if(this.documentError){
								self.snackbar = true;
								self.snackBarType = "warning";
								self.snackBarMsg = "Oops ! Sorry, This issue seems to be from our side. Would you mind trying it after some time or talk to your administrator?"
							}
							else {
								self.snackbar = true;
								self.snackBarType = "warning";
								self.snackBarMsg = "Something went wrong. Please contact system administrator."

							}
						}
					},

					// retry fetching access rights/list page from error page
					retryFetching() {
						// check if the fetch error while retrieving access rights/list
						if(this.fetchErrorAccessRights) 
						{
							this.fnCheckAccessRights();
						}
						else {
							this.fnRetrieveStaticData();
						}
					},
					// function to retrieve static data
					fnRetrieveStaticData(){
						try {
							var self = this;
							self.loadingScreen = true;
							var staticDataList = self.graphQl.query(`getHousePropertyStaticData { getHousePropertyStaticData { errorCode message housePropertyStaticData}}`);
							staticDataList().then(function (response) {
								if(response.getHousePropertyStaticData.housePropertyStaticData){
										self.staticData = JSON.parse(response.getHousePropertyStaticData.housePropertyStaticData);
										self.itDeclarationSettingsStatus = self.staticData.incomeTaxDeclarationSubmissionStatus;
										self.currentAssessmentYear = self.staticData.currentAssessmentYear;
										self.assessmentYear = self.staticData.currentAssessmentYear;
										self.assessmentYearList = self.staticData.minAndMaxAssessmentYear;
										self.incomeSourceTypes = self.staticData.incomeSourceTypes ? self.staticData.incomeSourceTypes : [];
										self.loadingScreen = false;
										// retrieve the taxable income
										self.fnRetrieveEmpTaxationData();
										self.listDeclarations();
										self.fetchDropDownData();
										self.retrieveCompareTaxRegimeDetails();
								}else{
									self.incomeUnderSection24FetchError = true;
									self.handleError(null, 1);
								}
							})
							.catch(function (staticDataFetchError) {
								self.incomeUnderSection24FetchError = true;
								self.handleError(staticDataFetchError,1);
							});
						}
						catch(staticDataFetchError) {
							self.incomeUnderSection24FetchError = true;
							self.handleError(staticDataFetchError, 1);
						}
					},

					handleCompareRegime(regimeTerm) {
						let self = this;
						self.retrieveCompareTaxRegimeDetails();
						self.fnRetrieveEmpTaxationData(regimeTerm);
					},

					// get the employee income and tax details
					fnRetrieveEmpTaxationData(regimeTerm = null) {
						let self = this;
						try {
							//get the org date format
							axios.post(self.baseUrl + `payroll/salary-payslip/get-employee-tax-details`, {
								employeeId : self.employeeId,
								assessmentYear : self.assessmentYear,
								taxRegime: regimeTerm,
							}).then(response => {
								// check if the data is retrieved
								if(response.data && response.data.success) {
									self.viewTaxCalculation = response.data.empTaxDetails.Tax_Calculation_View;
									self.empTaxableIncome =  response.data.empTaxDetails.Tax_Calculation_Details.Annual_Taxable_Income;
									self.empAnnualTax = response.data.empTaxDetails.Tax_Calculation_Details.Annual_Tax;
									self.taxSheetData = response.data.empTaxDetails.Tax_Calculation_Details;
									self.currencySymbol = response.data.empTaxDetails.Tax_Calculation_Details.Currency_Symbol;
									if (response.data.empTaxDetails.Enable_View_Tds_Button) {
										self.enableViewTDSButton  = true;
									} else {
										self.enableViewTDSButton = false;
									}
									if(regimeTerm && regimeTerm.length && self.$refs && self.$refs.incomeUnder24){
										self.$refs.incomeUnder24.downloadPdf()
									}
								}
								else {
									self.empTaxableIncome = '' ;
									self.empAnnualTax = '';
								}
							})
							.catch(function (fetchEmpTaxationError) {
								self.empTaxableIncome = '' ;
								self.empAnnualTax = '';
							});
						}
						catch(fetchEmpTaxationError) {
							self.empTaxableIncome = '' ;
							self.empAnnualTax = '';
						}
						
					},
					// list declaration
					listDeclarations(){
						try {
							var self = this;
							self.loadSkeleton = true;
							var declarationList = self.graphQl.query(` CommentQuery($employeeId:Int!,$assessmentYear:Int!,$status:[String]) { listHouseProperties (employeeId:$employeeId,assessmentYear:$assessmentYear,status:$status) { errorCode message houseProperties}}`);
							declarationList({
								"assessmentYear" : self.assessmentYear,
								"employeeId": self.employeeId,
								"status": self.selectedStatus
							})
							.then(function (response) {
								if (response.listHouseProperties.houseProperties) {
									self.showViewPage = true;
									self.listRenderCount++;
									self.listData = JSON.parse(response.listHouseProperties.houseProperties);
									// get initial length of self occupied records, because after applying filter, count may change. It will affect max limit validation in self-occupied list.
									if(self.listData.selfoccupiedrecords && self.listData.selfoccupiedrecords.length && self.selectedStatus.length == 0){
										let selfOccupiedRecsWithoutRejected = self.listData.selfoccupiedrecords.filter(el => el.approval_status !== 'Rejected');
										self.initialSelfOccupiedCount = selfOccupiedRecsWithoutRejected ? selfOccupiedRecsWithoutRejected.length : 0;
									}
									self.loadSkeleton = false;
								}else{
									self.incomeUnderSection24FetchError = true;
									self.handleError(null, 1);
								}
							})
							.catch(function (listFetchError) {
								self.incomeUnderSection24FetchError = true;
								self.handleError(listFetchError,1);
							});
						}
						catch(listFetchError) {
							self.incomeUnderSection24FetchError = true;
							self.handleError(listFetchError, 1);
						}
					},
					// function apply and reset filters
					fnApplyResetFilter(type){
						if(type == 'reset'){
							// reset selected status value
							this.selectedStatus = [];
						}
						this.filterOpen = false;
						this.filterOpenInMobile = false;
						this.listDeclarations();
					},

					// handle error during doc view
					handleDocumentError(fetchPresignedUrlError) {
						this.documentError = true;
						this.topBarClass = 'income-under-top-bar';
						this.handleError(fetchPresignedUrlError,0)
					},

					// handle error whle downloading tax sheet
					handleTaxSheetError() {

						this.snackbar = true;
						this.snackBarType = "warning";
						this.snackBarMsg = "Something went wrong while downloading the tax sheet. Please try again"

					},

					openCloseViewModal(value) {
						if(value) {
							this.topBarClass = 'income-under-top-bar-index';

						}
						else {
							this.topBarClass = 'income-under-top-bar';
						}
					},
					// handle success from Add/Edit
					handleAddEditSuccess(addEditSuccess) {

						var action = addEditSuccess[1];
						this.successType = addEditSuccess[0];

						if(action == 'add') {
							this.snackBarMsg = "Hurray.!, You successfully added your declaration to save your tax."
							this.isAddSuccess = true;
							this.isEditSuccess = false;
							this.successData = {};
							this.listDeclarations();
						}
						else if(action == 'edit'){
							// edit success
							this.snackBarMsg = "Declaration updated successfully."
							this.isAddSuccess = false;
							this.isEditSuccess = true;
							this.successData = addEditSuccess[2];
							this.listDeclarations();
						}
						else {
							this.snackBarMsg = "Declaration(s) deleted successfully."
							this.isAddSuccess = false;
							this.isEditSuccess = false;
							this.successData = {
								'isDelete' : 1
							};
							this.listDeclarations();
						}

						this.snackbar = true;
						this.snackBarType = "success";

					},
					// handle error from Add/Edit function
					handleAddEditError(errorEvent) {
						var action = errorEvent[1];
            			var addEditError = errorEvent[0];
						this.handleError(addEditError,0,action)
					},

					handleWarningMessages(message){
						this.snackBarMsg = message;
						this.snackBarType = "warning";
						this.snackbar = true;
					},
					fnChangeEmployee(chooseEmpParam) {
						this.chosenStatus = chooseEmpParam[0];
						this.chosenEmployeeIds = chooseEmpParam[1];
						this.chosenType = chooseEmpParam[2];
						// reset selected filter
						this.selectedDesignation = []; this.selectedDepartment = []; this.selectedEmpType = []; this.selectedLocation = []; this.selectedWorkSchedule = [];
						this.fnRetrieveEmployees('fromView') 
					},
					fetchDropDownData() {
						var self = this;
						// dropdown query
						var fetchDropdownDetails = self.atsGraphQl.query(`samplequery { getDropDownBoxDetails { errorCode message designations { Designation_Id Designation_Name } departments { Department_Id Department_Name} workSchedules { WorkSchedule_Id Title} locations { Location_Id Location_Name} employeeType { EmpType_Id Employee_Type}}}`);
						fetchDropdownDetails().then(dropDownData => {
							self.departmentList = dropDownData.getDropDownBoxDetails.departments;
							self.designationList = dropDownData.getDropDownBoxDetails.designations;
							self.locationList = dropDownData.getDropDownBoxDetails.locations;
							self.empTypeList = dropDownData.getDropDownBoxDetails.employeeType;
							self.workScheduleList = dropDownData.getDropDownBoxDetails.workSchedules;
						}).catch(dropdownDataErr => {
							self.departmentList = [], self.designationList = [], self.locationList = [], self.empTypeList = [], self.workScheduleList = [];
						})
					},
					fnRetrieveEmployees(from){
						var self = this;
						if(from !== 'fromFilter')
							self.loadingScreen = true;
						// dropdown query
						var fetchEmployees = self.graphQl.query(`getEmployeesDetails ($approvalStatus:String,$assessmentYear:Int,$sourceType:String!,$employeeIdArray:[Int],$designation:[Int],$department:[Int],$employeeType:[Int],$workSchedule:[Int],$location:[Int]) { getEmployeesDetails (approvalStatus:$approvalStatus,assessmentYear:$assessmentYear,sourceType:$sourceType,employeeIdArray:$employeeIdArray,designation: $designation,department:$department,employeeType:$employeeType,workSchedule:$workSchedule,location:$location) { errorCode message employeeDetails}}`);
						fetchEmployees({
							"approvalStatus": self.chosenStatus,
							"assessmentYear" :self.assessmentYear,
							"sourceType": self.chosenType,
							"employeeIdArray": self.chosenEmployeeIds,
							"designation" : self.selectedDesignation,
							"department" : self.selectedDepartment,
							"employeeType" : self.selectedEmpType,
							"workSchedule" : self.selectedWorkSchedule,
							"location" : self.selectedLocation
						}).then(empListRes => {
							if(empListRes.getEmployeesDetails.employeeDetails)
								self.employeesList = JSON.parse(empListRes.getEmployeesDetails.employeeDetails);
							if(self.$refs.empListModal)
								self.$refs.empListModal.isLoading = false;
							if(self.employeesList.length > 0)
								self.showEmpModal = true;
							self.loadingScreen = false;
							self.openCloseViewModal(true);
						}).catch(empListErr => {
							self.handleError(empListErr);
							self.fnCancelEmpListModal();
							self.loadingScreen = false;
						})
					},
					// function called when filter is applied in employees list popup
					fnApplyingFilterInEmpListPopup(filterParam) {
						this.selectedDepartment = filterParam[0],
						this.selectedDesignation = filterParam[1],
						this.selectedEmpType = filterParam[2],
						this.selectedLocation = filterParam[3],
						this.selectedWorkSchedule = filterParam[4];
						this.fnRetrieveEmployees('fromFilter');
					},
					// click proceed button in employees selection modal
					fnProceedInEmpListModal(selectedEmp) {
						this.employeeId = selectedEmp[0].employee_id;
						this.showEmpModal = false;
						this.selectedStatus = [];
						this.initialSelfOccupiedCount = 0;
						this.fnCancelEmpListModal();
						this.fnRetrieveEmpTaxationData();
						this.listDeclarations(); 
						this.retrieveCompareTaxRegimeDetails();
					},
					// click on close btn in employees list popup
					fnCancelEmpListModal() {
						this.showEmpModal = false;
						this.openCloseViewModal(false);
						let styleElements = document.createElement('style');
						styleElements.textContent = `
							.v-data-table table {
                    			padding: 0 2px !important;
							}
							.v-data-table{
								background: #f1f1f1 !important;
							}
							.v-data-table table{
								background: #f1f1f1;
							}
							.v-data-table tbody tr{
                                box-shadow: none !important;
                       		}
							.v-data-table table {
								background: rgb(241, 241, 241) !important;
							}
							.v-data-table tbody .v-data-table__mobile-table-row td:nth-child(2) {
								justify-content: space-between;;
							}
							.v-data-table tbody td:nth-child(2) .v-data-table__mobile-row__header {
									display: flex;
							}
							@media (max-width: 600px) {
								.v-data-table thead tr:last-child th {
									display: flex !important;
								}
							}
						`;
						document.head.appendChild(styleElements);
					},
					// get the employee tax regime details
					retrieveCompareTaxRegimeDetails() {
						let self = this;
						try {
							axios.post(self.baseUrl + `payroll/tax-rules/compare-tax-regime`, {
								requestResource : 'DECLARATIONSFORM',
								employeeId: self.employeeId
							}).then(response => {
								let regimeResonseData = response.data;
								if (regimeResonseData && regimeResonseData.success) {
									let regimeDetails = regimeResonseData.regimeComparisionDetails;
									self.compareTaxRegimeDetails = regimeDetails;
									/** Check whether Tax Regime Change button can be presented or not */
									if (regimeDetails.Trigger_Regime_Change) {
										self.enableTaxRegimeChangeButton = true;
									} else {
										self.enableTaxRegimeChangeButton = false;
									}

									/** Get the error message that has to be shown to user when the system does not allow the user to change tax regime */
									if (regimeDetails.Regime_Change_Restriction_Message) {
										self.taxRegimeChangeRestrictionMsg = regimeDetails.Regime_Change_Restriction_Message;
									} else {
										self.taxRegimeChangeRestrictionMsg = '';
									}
								} else {
									self.compareTaxRegimeDetails = {};
									self.enableTaxRegimeChangeButton = false;
									self.taxRegimeChangeRestrictionMsg='';
								}
							})
							.catch(function (retrieveCompareTaxRegimeCatchError) {
								self.compareTaxRegimeDetails = {};
								self.enableTaxRegimeChangeButton = false;
								self.taxRegimeChangeRestrictionMsg='';
							});
						}
						catch(retrieveCompareTaxRegimeMainCatchError) {
							self.compareTaxRegimeDetails = {};
							self.enableTaxRegimeChangeButton = false;
							self.taxRegimeChangeRestrictionMsg='';
						}
					},
					//handle tax regime update success/failure response
					handleTaxRegimeUpdateResponse(responseEvent){
						this.snackBarMsg = responseEvent[1];
						if(responseEvent[0] == 'success') {
							this.snackbar = true;
							this.snackBarType = "success";
							this.fnRetrieveEmpTaxationData();//call the action to retrieve employee tax sheet details
						}
						else{
							this.snackbar = true;
							this.snackBarType = "warning";
						}
					}
				}
			})
		</script>
	</body>
</html>

<!--  own styles -->
<style scoped lang="css">
	.income-under-top-bar-index {
		height: 5em;
		position: fixed !important;
		width: 100% !important;
		top : 50px;
	}
	.income-under-top-bar {
		height: 5em;
		position: fixed !important;
		width: 100% !important;
		z-index: 2000;
		top : 50px;
	}
	.active_tab_bg {
		background:#f9f9f9 !important;
	}
	.v-data-table thead tr:last-child th{
		font-size: 15px;
	}
	.v-data-table tbody tr{
		cursor: pointer;
	}
	.topbar-right-action{
		margin-right: 100px;
	}
	.v-application--is-ltr .v-chip .v-chip__close.v-icon.v-icon--right{
		margin-right: 0px !important;
	}			
	@media screen and (max-width:1024px){
		.topbar-right-action{
			margin-right: 0px !important;
		}
	}	
</style>
