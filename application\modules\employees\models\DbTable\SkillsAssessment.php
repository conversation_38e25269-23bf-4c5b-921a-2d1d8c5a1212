<?php
//=========================================================================================
//=========================================================================================
/* Program : SkillsAssessment.php												         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MYSQL query for skillset assessment        							 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        17-Oct-2013    Mahesh                  Initial Version         	         *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Saranya                 Changes in file for mobile app     *
 *                                                    1.Extra fields are added in        *
 *                                                    field list of list query.          */
//=========================================================================================
//=========================================================================================

class Employees_Model_DbTable_SkillsAssessment extends Zend_Db_Table_Abstract
{
    protected $_dbComment   = null;
    protected $_orgDF       = null;
    protected $_db          = null;
    protected $_ehrTables   = null;
    protected $_dbCommonFun = null;

    public function init()
    {
        $this->_ehrTables   = new Application_Model_DbTable_Ehr();
        $this->_db          = Zend_Registry::get('subHrapp');
        $this->_orgDF       = $this->_ehrTables->orgDateformat();
        $this->_dbComment   = new Payroll_Model_DbTable_PayrollComment();
        $this->_dbCommonFun = new Application_Model_DbTable_CommonFunction();
    }
	
	/**
     * Get performance start month
     */
    public function getPerformanceMonth()
    {
        return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->orgDetails,'Performance_StartMonth'));
    }
	
	/**
     * Get skillset details to show in a grid
     */
    public function listSkillsetAssessment ($page, $rows, $sortField, $sortOrder, $searchAll, $searchArr, $skillMatrixUser)
    {
    	$employeeName              = $searchArr['Employee_Name'];
		$assessmentStartMonth      = $searchArr['AssessmentStartMonth'];		
		$assessmentEndMonth        = $searchArr['AssessmentEndMonth'];		
		$skillLevelStart           = $searchArr['SkillLevel_Start'];
		$skillLevelEnd             = $searchArr['SkillLevel_End'];
		$skillDefinition           = $searchArr['Skill_Definition'];
		$skillLevelBegin           = $searchArr['SkillLevelBegin'];
		$skillLevelEnd             = $searchArr['SkillLevelEnd'];
		
        /**
		 *	Sorting columns based on display column order in grid
		*/
		switch ($sortField)
		{
            case 1: $sortField = 'EJ.User_Defined_EmpId'; break;
			case 2: $sortField = 'emp.Emp_First_Name'; break;
			case 3: $sortField = 'skill.Assessment_From'; break;
			case 4: $sortField = 'skill.Assessment_To'; break;
			case 5: $sortField = new Zend_Db_Expr('TRIM(TRAILING "." FROM TRIM(TRAILING "0" from ROUND(AVG(skillMat.Skill_Level),2)))'); break;
			default:
				$sortField = 'skill.Added_On'; $sortOrder = 'desc'; break;
		}
        
        $qrySkillMatrix = $this->_db->select()
                                ->from(array('skill'=>$this->_ehrTables->skillset),
                                       array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS skill.Skillset_Id as count'), 'skill.Skillset_Id',
											 'skill.Employee_Id', 'skill.Assessment_From', 'skill.Assessment_To',
                                             new Zend_Db_Expr("DATE_FORMAT(skill.Added_On,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
											 new Zend_Db_Expr("DATE_FORMAT(skill.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On"),
                                             
                                             new Zend_Db_Expr("DATE_FORMAT(skill.Assessment_From,'".$this->_orgDF['sql']."') as AssessmentFrom"),
								             new Zend_Db_Expr("DATE_FORMAT(skill.Assessment_To,'".$this->_orgDF['sql']."') as AssessmentTo"),
								  			 'DT_RowClass' => new Zend_Db_Expr('"skillset-assessment"'),
											 'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', skill.Skillset_Id)")))
								
                                ->joinLeft(array('skillMat'=>$this->_ehrTables->skillMatrix),'skill.Skillset_Id=skillMat.Skillset_Id',
										   array('skillMat.Line_Id', 'Average_Level_Rating' => new Zend_Db_Expr('TRIM(TRAILING "." FROM TRIM(TRAILING "0" from ROUND(AVG(skillMat.Skill_Level),2)))')))
                                
                                ->joinLeft(array('skillDef'=>$this->_ehrTables->skillDefinition),
										   'skillDef.SkillDefinition_Id=skillMat.SkillDefinition_Id', array('skillDef.Title'))
                                
                                ->joinInner(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=skill.Employee_Id',
                                            array(new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as Employee_Name"),
                                                  'skill.Employee_Id','emp.DOB as Date_Of_Join'))
                                
                                  ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'emp.Employee_Id=EJ.Employee_Id',
                                            array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN emp.Employee_Id ELSE EJ.User_Defined_EmpId END')))
                                
                                ->joinInner(array('emp1'=>$this->_ehrTables->empPersonal),'emp1.Employee_Id=skill.Added_By',
                                            array(new Zend_Db_Expr("CONCAT(emp1.Emp_First_Name, ' ', emp1.Emp_Last_Name) as Added_By_Name")))
                                
                                ->joinLeft(array('emp3'=>$this->_ehrTables->empPersonal),'emp3.Employee_Id=skill.Updated_By',
                                           array(new Zend_Db_Expr("CONCAT(emp3.Emp_First_Name, ' ', emp3.Emp_Last_Name) as Updated_By_Name")))
								
								->order("$sortField $sortOrder")
								->group('skill.Skillset_Id')
								->limit($rows, $page);
		
        if (empty($skillMatrixUser['Admin']))
        {
            $employeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
												  ->where('Manager_Id = ?', $skillMatrixUser['LogId']));
			
            if ( $skillMatrixUser['Is_Manager'] == 1 && !empty($employeeId)) {
                $qrySkillMatrix
					->where('skill.Employee_Id = :EmpId  or skill.Employee_Id IN (?)', $employeeId)
					->bind(array('EmpId' => $skillMatrixUser['LogId']));
            }
            else
			{
                $qrySkillMatrix->where('skill.Employee_Id = ?', $skillMatrixUser['LogId']);
            }
        }
		
        /**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			 $conditions  = $this->_db->quoteInto(new Zend_Db_Expr('Concat(emp.Emp_First_Name," ",emp.Emp_Last_Name) Like ?'),"%$searchAll%");
             
             $conditions .= $this->_db->quoteInto('or skillMat.Skill_Level = ?', "$searchAll");
            
             $conditions .= $this->_db->quoteInto('or skill.Assessment_From = ?', "$searchAll");
             
             $conditions .= $this->_db->quoteInto('or skill.Assessment_To = ?', "$searchAll");
            
			 $conditions .= $this->_db->quoteInto('or skillMat.SkillDefinition_Id = ?', "$searchAll");
             
             $conditions .= $this->_db->quoteInto('or EJ.User_Defined_EmpId Like ?', "%$searchAll%");
			
			 $qrySkillMatrix->where($conditions);
			
		}
		
		/* Filter for Employee Name */
		if ($employeeName != '' && preg_match('/^[a-zA-Z]/', $employeeName)) {
			$qrySkillMatrix->where($this->_db->quoteInto(new Zend_Db_Expr('Concat(emp.Emp_First_Name," ",emp.Emp_Last_Name) Like ?'),"%$employeeName%"));
				//->where($this->_db->quoteInto('emp.Emp_First_Name Like ? or ', "%$employeeName%").
				//		$this->_db->quoteInto('emp.Emp_Last_Name Like ?', "%$employeeName%"));
		}
		
		if (!empty($assessmentStartMonth))
		{
            $qrySkillMatrix->where($this->_db->quoteInto('skill.Assessment_From >= ?', $assessmentStartMonth));
		}
		
		if(!empty($assessmentEndMonth))
		{
			$qrySkillMatrix->where($this->_db->quoteInto('skill.Assessment_To <= ?', $assessmentEndMonth));
		}
		
		/* Filter for Skill Level Begin */
		if ($skillLevelStart != '') {
			$qrySkillMatrix->where("skillMat.Skill_Level >= ?", $skillLevelStart);
				//->bind(array('skillMatStart'=>$this->_ehrTables->skillMatrix));
		}
		
		/* Filter for Skill Level End */
		if ($skillLevelEnd != '') {
			$qrySkillMatrix->where("skillMat.Skill_Level <= ?", $skillLevelEnd);
				//->bind(array('skillMat'=>$this->_ehrTables->skillMatrix));
		}
		
		/* Filter for Skill Definition */
		if ($skillDefinition != '') {
			$qrySkillMatrix->where($this->_db->quoteInto('skillMat.SkillDefinition_Id = ?', "$skillDefinition"));
		}
		
		/* Filter for Average Skill Level Begin */
		if ($skillLevelBegin != '') {
			$qrySkillMatrix->having(new Zend_Db_Expr('TRIM(TRAILING "." FROM TRIM(TRAILING "0" from ROUND(AVG(Skill_Level),2)))') .' >= ?', $skillLevelBegin);
		}
		
		/* Filter for Average Skill Level End */
		if ($skillLevelEnd != '') {
			$qrySkillMatrix->having(new Zend_Db_Expr('TRIM(TRAILING "." FROM TRIM(TRAILING "0" from ROUND(AVG(Skill_Level),2)))') .' <= ?', $skillLevelEnd);
		}
		
		$qrySkillMatrix = $this->_dbCommonFun->getDivisionDetails($qrySkillMatrix,'EJ.Department_Id');
		
		/**
		 * SQL queries
		 * Get data to display
		*/
		$skillsetAssessment = $this->_db->fetchAll($qrySkillMatrix);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		/* Total data set length */
		$qrySkillMatrixCnt = $this->_db->select()->from($this->_ehrTables->skillset, new Zend_Db_Expr('COUNT(Skillset_Id)'))
												->group('Skillset_Id');
		
		if (empty($skillMatrixUser['Admin']))
        {
            $employeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
												  ->where('Manager_Id = ?', $skillMatrixUser['LogId']));
			
            if ( $skillMatrixUser['Is_Manager'] == 1 && !empty($employeeId))
			{
                $qrySkillMatrixCnt
					->where('Employee_Id = :EmpId  or Employee_Id IN (?)', $employeeId)
					->bind(array('EmpId' => $skillMatrixUser['LogId']));
            }
            else
			{
                $qrySkillMatrixCnt->where('Employee_Id = ?', $skillMatrixUser['LogId']);
            }
            $iTotal = $this->_db->fetchOne($qrySkillMatrixCnt);
        }
        else
        {
            $iTotal = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->skillset, new Zend_Db_Expr('Skillset_Id'))
                                                            ->group('Skillset_Id'));
			$iTotal = count($iTotal);
        }
		
        //$iTotal = count($qrySkillMatrixCnt);
		//$iTotal = $this->_db->fetchOne($qrySkillMatrixCnt);
		
		/**
		 * Output array with Json encode
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $skillsetAssessment);
    }
	
	/**
     * Get skillId by employeeId , assessFrom
     */
    public function getSkillId ($employeeId,$assessFrom)
    {
        return $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->skillset, array('Skillset_Id'))
									->where('Employee_Id = ?', $employeeId)
									->where('Assessment_From = ?', $assessFrom));
    }
	
	/**
     * Check skill matrix exist for skillset assessment id
     */
    public function checkSkillMatrixExists ($skillId)
    {
        return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->skillMatrix, new Zend_Db_Expr('Count(Line_Id)'))
									->where('Skillset_Id= ?', $skillId));
    }
	
	/**
     * Add/Edit skillset assessment and skill matric details
     */
    public function updateSkillsetAssessment ($skillsetData, $skillsetId, $formName, $sessionId, $comments,$customFormName)
    {
		$qryCheckExist = $this->_db->select()
										->from($this->_ehrTables->skillset, new Zend_Db_Expr('count(Skillset_Id)'))
										
										->where('Employee_Id = ?', $skillsetData['Employee_Id'])
										->where('Assessment_From = ?', $skillsetData['Assessment_From']);
		
        if (!empty($skillsetId))
        {
            $qryCheckExist->where('Skillset_Id != ?', $skillsetId);
        }
        
        $checkExist = $this->_db->fetchOne($qryCheckExist);
		
		if ($checkExist == 0)
		{
			if ($skillsetId == 0)
			{
				$action = 'Add';
				
				$skillsetData['Added_On']  = date('Y-m-d H:i:s'); /*new Zend_Db_Expr('NOW()');*/
				$skillsetData['Added_By']  = $sessionId;
				$skillsetData['lock_Flag'] = $sessionId;
				
				$updated = $this->_db->insert ($this->_ehrTables->skillset, $skillsetData);
				
				if ($updated)
					$skillsetId = $this->_db->lastInsertId();
			}
			else
			{
				$action = 'Edit';
				
				$skillsetData['Updated_On'] = date('Y-m-d H:i:s'); /*new Zend_Db_Expr('NOW()');*/
				$skillsetData['Updated_By'] = $sessionId;
				
				$updated = $this->_db->update ($this->_ehrTables->skillset, $skillsetData, 'Skillset_Id = '.$skillsetId);
			}
			
			if ($updated && !empty($comments))
			{
				$skillsetId = $this->_db->lastInsertId();
				
				$this->_db->insert($this->_ehrTables->comment, array('Form_Id'     => $this->_dbComment->getFormId($formName),
																	 'Emp_Comment' => htmlentities($comments),
																	 'Parent_Id'   => $skillsetId,
																	 'Employee_Id' => $sessionId,
																	 'Added_On'    => date('Y-m-d H:i:s') /*new Zend_Db_Expr('NOW()')*/));
			}
			
			/**
			 *	this function will handle
			 *		update system log function
			 *		clear submit lock fucntion
			 *		return success/failure array
		    */
			$result = $this->_dbCommonFun->updateResult (array('updated'        => $updated,
															   'action'         => $action,
															   'trackingColumn' => $skillsetId,
															   'formName'       => $customFormName,
															   'sessionId'      => $sessionId,
															   'tableName'      => $this->_ehrTables->skillset));
			
			if ($result['success'])
			{
				$result['Skillset_Id'] = $skillsetId;
			}
			
			return $result;
		}
		else
		{
			return array('success' => false, 'msg' => $customFormName.' already exists for this employee', 'type' => 'info');
		}
    }
	
	/**
     * Delete skillset
     */
    public function deleteSkillset ($skillsetId, $sessionId, $formName,$customFormName)
    {
		$deleted = 0;
		
		/**
		 *	Get skillset assessment 
		*/
        $skillsetLock = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->skillset, array('Lock_Flag'))
											 ->where('Skillset_Id = ?', $skillsetId));
		
		/**
		 *	If there is no lock then continue delete process
		*/
		if ($skillsetLock == 0)
		{
			$deleted = $this->_db->delete ($this->_ehrTables->skillset, array('Skillset_Id = ?'=>(int)$skillsetId));
			
			if ($deleted)
			{
				$isExist = $this->_db->fetchOne ($this->_db->select()
												 ->from($this->_ehrTables->skillMatrix, new Zend_Db_Expr('count(Skillset_Id)'))
												 ->where('Skillset_Id = ?', $skillsetId));
				
				if ($isExist > 0)
				{
					$deleted = $this->_db->delete ($this->_ehrTables->skillMatrix, array('Skillset_Id = ?' => (int)$skillsetId));
				}
				
				if ($deleted)
				{
					$this->_dbComment->deleteComment ($skillsetId, $formName);
				}
			}
		}
		
		/**
		 *	delete activity for common function
		 *		1)check lock is exist or not.
		 *			If lock is exist then show error message like employee is open record for update.
		 *		2)If No lockflag then process delete activity
		 *		3)Update delete activity in system log
		 *		4)return success/failure message
		*/
		return $this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
														'tableName'      => $this->_ehrTables->skillset,
														'lockFlag'       => $skillsetLock,
														'formName'       => $customFormName,
														'trackingColumn' => $skillsetId,
														'sessionId'      => $sessionId));
    }
	
	/**
     * Get skill details to show in subgrid
     */
    public function listSkillMatrix ($page, $rows, $sortField, $sortOrder, $searchAll, $requestId)
    {
		/**
		 *	Sorting columns based on display column order in grid
		*/
		switch ($sortField)
		{
			case 1: $sortField = 'SM.Skill_Level'; break;
				default: $sortField = 'SD.Title'; break;
		}
		
		/**
		 *	Query to fetch all skill matrix details for skill set assessment id
		*/
        $qrySkills = $this->_db->select()
								->from(array('SM'=>$this->_ehrTables->skillMatrix),
									   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS SM.Line_Id as Count'), 'SM.Line_Id', 'SM.Skill_Level',
											 'SM.Skillset_Id'))
								
								->joinInner(array('SD'=>$this->_ehrTables->skillDefinition), 'SD.SkillDefinition_Id=SM.SkillDefinition_Id',
											array('SD.SkillDefinition_Id', 'SD.Title'))
								
								->where("SM.Skillset_Id = ?", $requestId)
								->order("$sortField $sortOrder")
								->limit($rows, $page);
        
		/**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			$conditions = $this->_db->quoteInto('SD.Title Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or SM.Skill_Level Like ?', "%$searchAll%");
			
			$qrySkills->where($conditions);
		}
		
        /**
		 * SQL queries
		 * Get data to display
		*/
		$skillMatrix = $this->_db->fetchAll($qrySkills);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		/* Total data set length */
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->skillMatrix, new Zend_Db_Expr('COUNT(Line_Id)'))
									   ->where("Skillset_Id= ?", $requestId));
		
		/**
		 * Output array with Json encode
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $skillMatrix);
    }
	
	/**
	 *	Add / Edit skill matrix details
	*/
	public function updateSkillMatrix ($skillMatrixData, $skillMatrixId, $sessionId)
	{
		$qryCheckExists = $this->_db->select()
										->from($this->_ehrTables->skillMatrix, new Zend_Db_Expr('COUNT(Line_Id)'))
										->where("Skillset_Id = ?", $skillMatrixData['Skillset_Id'])
										->where("SkillDefinition_Id = ?", $skillMatrixData['SkillDefinition_Id']);
		
		if ($skillMatrixId > 0)
		{
			$qryCheckExists->where('Line_Id != ?', $skillMatrixId);
		}
		
		$isExists = $this->_db->fetchOne($qryCheckExists);
		
		if ($isExists == 0)
		{
			if ($skillMatrixId > 0)
			{
				$action = 'Edit';
				
				$updated = $this->_db->update($this->_ehrTables->skillMatrix, $skillMatrixData, array('Line_Id = '. $skillMatrixId));
			}
			else
			{
				$action = 'Add';
				
				$updated = $this->_db->insert($this->_ehrTables->skillMatrix, $skillMatrixData);
			}
			
			if ($updated)
			{
				$skillsetData = array('Updated_On' =>date('Y-m-d H:i:s'), /*new Zend_Db_Expr('NOW()'),*/
									  'Updated_By' => $sessionId);
				
				$this->_db->update ($this->_ehrTables->skillset, $skillsetData, 'Skillset_Id = '.$skillMatrixData['Skillset_Id']);
				
				return array('success' => true, 'msg' => 'Skill Level '. ($action == 'Add' ? 'added' : 'updated') .' successfully', 'type' => 'success');
			}
			else
			{
				return array('success' => false, 'msg' => 'Unable to '. ($action == 'Add' ? 'add' : 'update') .' skill level', 'type' => 'warning');
			}
		}
		else
		{
			return array('success' => false, 'msg' => 'Skill Level already exists', 'type' => 'info');
		}
	}
	
	/**
	 *	Delete Skill Level record
	*/
	public function deleteSkillMatrix ($skillMatrixId, $sessionId)
	{
		$skillsetId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->skillMatrix, 'Skillset_Id')
										   ->where("Line_Id = ?", $skillMatrixId));
		
		$deleted = $this->_db->delete ($this->_ehrTables->skillMatrix, 'Line_Id='.$skillMatrixId);
		
		if ($deleted)
		{
			if ($skillsetId > 0)
			{
				$skillsetData = array('Updated_On' => date('Y-m-d H:i:s'), /*new Zend_Db_Expr('NOW()'),*/
									  'Updated_By' => $sessionId);
				
				$this->_db->update ($this->_ehrTables->skillset, $skillsetData, 'Skillset_Id = '.$skillsetId);
			}
			
			return array('success' => true, 'msg' => 'Skill Level deleted successfully', 'type' => 'success');
		}
		else
		{
			return array('success' => false, 'msg' => 'Unable to delete skill level', 'type' => 'warning');
		}
	}

	public function __destruct()
    {
        
    }	
	
	
}