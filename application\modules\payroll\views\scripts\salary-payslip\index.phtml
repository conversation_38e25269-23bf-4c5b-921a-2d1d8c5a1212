<?php
    $this->headTitle($this->formNameA);
	
	$salaryPayslip   = $this->payslipUser;
	$fullFinalSettlementAccess = $this->fullFinalSettlementUser;
	$biMonthlyAccess = $this->biMonthlyUser;
	$salaryReviewAccess = $this->salaryReviewUser;
	$employeeName    = $this->employeeName;
	$departmentPair  = $this->department;
	$locationPair	 = $this->location;
	$businessUnitPair = $this->businessUnit;
	$employeeTypePair = $this->employeeType;
	$payslipMonthPair = $this->payslipMonthPair;
	$payslipMonthYear = $this->monthYear;
    $assessMnth       = $this->assessMnth;
    $assessmentYr     = $this->assessmentYr;
	$monthlySalaryImg = $this->monthlySalaryImg;
    $hourlySalaryImg = $this->hourlySalaryImg;
    $deptHierarchy = $this->deptHierarchy;
    $maxMonthSalary = $this->maxMonthSalary;
	$customField = $this->customField;
	$showReportCreator= $this->ShowReportCreator;
	$displayPayslipAddress = $this->displayPayslipAddress;
	$financialClosureTracking = $this->financialClosureTracking;
	$pfNumber = $pan = array();
    $pfNumber['Enable'] = $pan['Enable'] = 1;	
    $pfNumber['Field_Name'] = 'PF Number';
    $pan['Field_Name'] = 'PAN No.';
    $payslipReviewProcess = $this->payslipReviewProcess;
	$payrollPeriod = $this->payrollPeriod;

	foreach($customField as $custom)
    {
        if($custom['Field_Name'] == 'PF Number' )
        {
            $pfNumber['Enable'] = $custom['Enable'];            
            $pfNumber['Field_Name'] = ($custom['New_Field_Name'] != '' ?  $custom['New_Field_Name'] : $custom['Field_Name']);
        }
        
        if($custom['Field_Name'] == 'PAN No.' )
        {
            $pan['Enable']  = $custom['Enable'];
            $pan['Field_Name'] = ($custom['New_Field_Name'] != '' ?  $custom['New_Field_Name'] : $custom['Field_Name']);
        }
	}

	$fieldForce				= $this->fieldForce;
	$serviceProviderAdmin	= $this->serviceProviderAdmin;
	$serviceProvider	 	= $this->serviceProvider;
	$serviceProviderId      = $this->serviceProviderId;
	$accountNumberList      = $this->accountNumberList;
	$paymentStatusPair 		= array('Yet To Review','Rejected','Yet To Finalize','Employee Owes','Nil Payment','Salary Hold','Unpaid','Paid'); 
    ?>
	<input type="hidden" name="fieldForce" id="fieldForce" value="<?php echo $fieldForce ?>" />
	<input type="hidden" name="payrollPeriod" id="payrollPeriod" value="<?php echo strtolower($payrollPeriod); ?>" />
	<input type="hidden" name="serviceProviderId" id="defaultServiceProviderId" value="<?php echo $serviceProviderId ?>" />
	<input type="hidden" name="updatedPaymentStatus" id="updatedPaymentStatus"/>
	<input type="hidden" name="payslipType" id="payslipType"/>

	<?php
	if ($salaryPayslip['View'] == 1) {
?>
<?php if ((int)$fullFinalSettlementAccess['View'] === 1) { ?>
	<div class="col-md-12 portlets paddingCls tab-spacing-cls">
		<div class="col-md-12 paddingCls bg-f9f9f9 tab-wrapper">
			<div class="pointer-cls border-bottom-secondary tab-border-cls bg-f9f9f9 tab-body" id="salaryPayslipTab">
				<div class="tab-active-text tab-text-font text-secondary custom-tab-content"  id="salaryPayslipFormTab">Salary Payslip</div>
			</div>
			<div class="pointer-cls border-bottom-secondary bg-f9f9f9 tab-body" id="fullFinalSettlementTab">
				<div class="tab-text-font custom-tab-content" id="fullFinalSettlementFormTab">
					<a id="formTabLink" class="tab-a-tag-color" href=<?php echo $this->baseUrl('v3/payroll/full-and-final-settlement'); ?>>
						Full & Final Settlement
					</a>
				</div>
			</div>
		</div>
	</div>
<?php } ?>
<!--By default set print panel as hidden and show when user clicks on print-->
<!-- <div class="row">
	<div class="col-lg-12 portlets">
		<div  class="hidden" id="printPanel">
			<div class="panel-header hidden" id="printHeader">
				<h3 style="text-align: center; font-weight: 800"> Attendance Shortage Report</h3>
			</div>
			<div class="panel-content pagination2 table-responsive">
				<div class="col-md-12 clearfix">
					<button type="submit" class="btn btn-secondary btn-embossed ladda-button" aria-hidden="true" id="PrintScreen" >
						<i class=""></i> Print
					</button>
					<button type="submit" class="btn btn-secondary btn-embossed ladda-button" aria-hidden="true" id="exitPrint" >
						<i class=""></i> Back
					</button>
				</div>
				<div class="preview_header" name="printable"></div>
				<div class="printable_portion" name="printable"></div>
			</div>
		</div>
	</div>
</div> -->

<!-- Payslip Prerequisite modal confirmation-->
<div class="modal fade" id="dirtyPayslipPreRequisite" aria-hidden="true" style="z-index:4000 !important;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closePayslipPreRequisite"></i></button>
                <h4 class="modal-title"><strong>Approvals</strong> Confirmation</h4>
            </div>
            
            <div class="modal-body">Some of the approvals are pending for generating payslip.Do you want to view them before generating payslip?<br></div>
            
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="showgeneratePayslip">No</button>
              <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="showPreRequisite">Yes</button>
            </div>
        </div>
    </div>
</div>
<!-- Payslip generation error popup -->
<div class="modal fade" id="modalPayslipGenerationError" aria-hidden="true">
	<div class="modal-dialog payout-warning-modal-width">
		<div class="modal-content payout-warning-modal-width">	
            <div class="payout-text-center-cls">
                <?php echo '<img class="img-responsive payout-error-img warning-err-img payout-inline-block" alt="Warning" src="'.$this->warningImg.'">'; ?>
            </div>		
			<div class="modal-body payout-warning-content" id="payslipErrorContent">
			</div>			
			<div class="modal-footer payout-warning-footer-div">
            <button type="button" class="btn btn btn-white btn-embossed payout-error-popup-btn payout-success-btn-color payout-warning-btn text-secondary" data-dismiss="modal" id="closePayslipErrModal">Ok</button></div>
		</div>
	</div>
</div>

<!-- warning popup when payslip templates are not added -->
<div class="modal fade" id="modalSalaryPayslipPrereq" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
					<i class="icons-office-52"></i>
				</button>
				<h4 class="modal-title" id="salaryPayslipPrereqTitle"></h4>
			</div>
			<!-- modal body -->
			<div class="modal-body">
			<div id="warningMessage"> </div>				
				<br>
			</div>
			<!-- modal footer -->
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal">Ok</button>
			</div>
		</div>
	</div>
</div>

<!-- payslip template preview modal -->
<div class="modal fade" id="modalPayslipTemplatePreview" aria-hidden="true">
	<div class="modal-dialog modal-lg" >
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" style="color: var(--primary-color)"></i></button>
			</div>
			<div class="modal-body" style="text-align: center;" id="modalBodyPayslipTemplatePreview"></div>
		</div>
	</div>
</div>

<!-- success popup after default payslip template is added-->
<div class="modal fade" id="modalShowSuccessMsg" aria-hidden="true">
	<div class="modal-dialog" >
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" style="color: var(--primary-color)"></i></button>
			</div>
			<div class="modal-body payslip-template-success-modal-body" id="modalBodySuccessMsg">
				<i class="mdi-action-check-circle payslip-template-success-popup-icon"></i>
				<div id="PayslipTemplatePreviewModalHeader" class="payslip-preview-modal-header">Payslip template successfully added</div>
				<div class="payslip-template-success-modal-sub-content">by clicking continue to initiate payslip</div>
				<button id="continueBtnInSuccessMdl" type="button" class="btn btn-secondary btn-embossed payslip-preview-custom-button">Continue</button>
			</div>
		</div>
	</div>
</div>

<!-- warning popup when pdf button is clicked in the mobile application-->
<div class="modal fade" id="modalPayslipPDFMobileApp" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
					<i class="icons-office-52"></i>
				</button>
				<h4 class="modal-title"><strong>PDF not Available</strong> </h4>
			</div>
			<!-- modal body -->
			<div class="modal-body">
				The PDF download feature is currently not available in the Mobile application, Please use the Web/Mobile browser to download the PDF file.  
				<br>
			</div>
			<!-- modal footer -->
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal">Ok</button>
			</div>
		</div>
	</div>
</div>


<div class="sub-tab-card col-md-12 paddingCls bg-ffffff tab-wrapper">
	<div class="pointer-cls border-bottom-secondary tab-border-cls bg-ffffff sub-tab-body" id="monthlyTab">
		<div class="tab-active-text tab-text-font text-secondary custom-sub-tab-content"  id="monthlyFormTab">Monthly/Hourly</div>
	</div>
	<?php if ((int)$biMonthlyAccess['View'] == 1) { ?>
		<div class="pointer-cls border-bottom-secondary bg-ffffff sub-tab-body" id="biMonthlyTab">
			<div class="tab-text-font custom-sub-tab-content" id="bimonthlyFormTab">
				<a id="subFormTabLink" class="tab-a-tag-color" href=<?php echo $this->baseUrl('v3/payroll/salary-payslip/bi-monthly'); ?>>
					Bi-Monthly
				</a>
			</div>
		</div>
	<?php } ?>
</div>

<!--Estimated payroll summary card-->
<?php if ($salaryPayslip['Admin'] === 'admin' && empty($serviceProviderAdmin['Employee']['Update'])) { ?>
	<div class="col-md-12 portlets estimated-main-card hidden add-panel-padding" id="estimatePayrollPanel">
		<div class="card estimated-card-radius">
			<input type="hidden" name="admin-rights-check-value" id="adminRightsCheck" value="<?php echo $salaryPayslip['Admin']; ?>" />
			<div class="row estimated-row">
				<div class="col-xlg-1 col-lg-1 col-md-2 col-sm-2 col-xs-12 estimated-img-column estimated-xs-center">
					<img width="100px" height="100px" alt="estimated-payroll" src="<?php echo $this->estimatedImg;?>" 
					onerror="this.onerror=null; this.src='<?php echo $this->estimatedAlternateImg;?>'"/>
				</div>
				<div class="col-xlg-1 col-lg-1 col-md-3 col-sm-3 col-xs-12 estimated-heading estimated-xs-center">
					Estimated Payroll Summary
				</div>
				<div class="col-xlg-1 col-lg-1 estimated-amount-column"></div>
				<div class="estimated-md-content col-md-7 col-sm-7 col-xs-12">
					<div class="estimated-amount" id="estimatedAmountMobile"></div>
					<div class="estimated-amount-heading">Organization total payroll cost</div>
					<button type="button" class="btn btn-sm btn-secondary btn-embossed estimated-recalculate-btn" id="recalculationSmall">
						Recalculate Now
					</button>
					<div class="estimated-updated-on">Last Recalculated On: <span id="estimatedPayrollUpdatedOnMobile"></span></div>
					<div class="estimated-updated-on">Last Updated By: <span id="estimatedPayrollUpdatedByMobile"></span></div>
				</div>
				<div class="col-xlg-7 col-lg-7 col-md-12 col-sm-12 col-xs-12 padding-xs-cls">
					<div class="col-xlg-12 col-lg-12 col-md-12 col-sm-12 col-xs-6 estimated-monthly-column hidden" id="monthlyEstimatedColumn">
						<div class="card estimated-monthly-hourly-card" id="monthlyEstimatedGrid">
							<div class="row estimated-monthly-hourly-row">
								<div class="col-md-1 estimated-xs-img-avatar">
									<div class="estimated-img-avatar">
										<img width="40%" height="56%" alt="monthly-money-bag" class="estimated-img-avatar-img" src="<?php echo $this->monthlyMoneyBag;?>" 
										onerror="this.onerror=null; this.src='<?php echo $this->monthlyBagAlternateImg;?>'"/>
									</div>
								</div>
								<div class="col-md-2">
									<div class="estimated-monthly-hourly-heading estimated-xs-center">Monthly Salary</div>
									<div class="estimated-monthly-hourly-date estimated-xs-center" id="monthlyPayslipMonth"></div>
								</div>
								<div class="col-md-2">
									<div class="estimated-monthly-hourly-heading estimated-xs-center" id="monthlyEarnings"></div>
									<div class="estimated-monthly-hourly-content estimated-xs-center">Earnings</div>
								</div>
								<div class="estimated-monthly-hourly-plus">+</div>
								<div class="col-md-2" style="padding-top: 8px;">
									<div class="estimated-monthly-hourly-heading estimated-xs-center" id="monthlyContribution"></div>
									<div class="estimated-monthly-hourly-content estimated-xs-center">Organization Contribution</div>
								</div>
								<div class="estimated-monthly-hourly-minus">-</div>
								<div class="col-md-2">
									<div class="estimated-monthly-hourly-heading estimated-xs-center" id="monthlyDeduction"></div>
									<div class="estimated-monthly-hourly-content estimated-xs-center">Deduction</div>
								</div>
								<div class="estimated-monthly-hourly-equals">=</div>
								<div class="col-md-2">
									<div class="estimated-monthly-hourly-heading estimated-xs-center" id="monthlyPayrollCost"></div>
									<div class="estimated-monthly-hourly-content estimated-xs-center">Payroll Cost</div>
								</div>
							</div>
						</div>
					</div>
					<div class="col-xlg-12 col-lg-12 col-md-12 col-sm-12 col-xs-6 estimated-hourly-column hidden" id="hourlyEstimatedColumn">
						<div class="card estimated-monthly-hourly-card" id="hourlyEstimatedGrid">
							<div class="row estimated-monthly-hourly-row">
								<div class="col-md-1 estimated-xs-img-avatar">
									<div class="estimated-img-avatar">
										<img width="40%" height="56%" alt="monthly-money-bag" class="estimated-img-avatar-img" src="<?php echo $this->hourlyMoneyBag;?>" 
										onerror="this.onerror=null; this.src='<?php echo $this->hourlyBagAlternateImg;?>'"/>
									</div>
								</div>
								<div class="col-md-2">
									<div class="estimated-monthly-hourly-heading estimated-xs-center">Hourly Salary</div>
									<div class="estimated-monthly-hourly-date estimated-xs-center" id="hourlyPayslipMonth"></div>
								</div>
								<div class="col-md-2">
									<div class="estimated-monthly-hourly-heading estimated-xs-center" id="hourlyEarnings"></div>
									<div class="estimated-monthly-hourly-content estimated-xs-center">Earnings</div>
								</div>
								<div class="estimated-monthly-hourly-plus">+</div>
								<div class="col-md-2" style="padding-top: 8px;">
									<div class="estimated-monthly-hourly-heading estimated-xs-center" id="hourlyContribution"></div>
									<div class="estimated-monthly-hourly-content estimated-xs-center">Organization Contribution</div>
								</div>
								<div class="estimated-monthly-hourly-minus">-</div>
								<div class="col-md-2">
									<div class="estimated-monthly-hourly-heading estimated-xs-center" id="hourlyDeduction"></div>
									<div class="estimated-monthly-hourly-content estimated-xs-center">Deduction</div>
								</div>
								<div class="estimated-monthly-hourly-equals">=</div>
								<div class="col-md-2">
									<div class="estimated-monthly-hourly-heading estimated-xs-center" id="hourlyPayrollCost"></div>
									<div class="estimated-monthly-hourly-content estimated-xs-center">Payroll Cost</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="col-md-2 col-xs-12 estimated-amount-column estimated-xs-center">
					<div id="recalculationContent" class="hidden">
						<div class="estimated-amount estimated-xs-center" id="estimatedAmount"></div>
						<div class="estimated-amount-heading estimated-xs-center">Organization total payroll cost</div>
						<button type="button" class="btn btn-sm btn-secondary btn-embossed estimated-recalculate-btn" id="recalculationLarge">
							Recalculate Now
						</button>
						<div class="estimated-updated-on estimated-xs-center">Last Recalculated On: <span id="estimatedPayrollUpdatedOn"></span></div>
						<div class="estimated-updated-on estimated-xs-center">Last Recalculated By: <span id="estimatedPayrollUpdatedBy"></span></div>
					</div>
					<div id="calculationContent" class="hidden">
						<button type="button" class="btn btn-sm btn-secondary btn-embossed estimated-recalculate-btn" id="calculateEstimation">
							Calculate Now
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
<?php }?>

<!-- recalculation confirmation -->
<div class="modal fade" id="modalRecalculationConfirmation" aria-hidden="true">
	<div class="modal-dialog modal-lg" >
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
					<i class="icons-office-52" id="closeRecalculationConfirmation" style="color: var(--primary-color);"></i>
				</button>
			</div>
			
			<div class="modal-body" style="text-align: center">
				<div class="col-md-6" style="text-align: center;">
					<i class="fa fa-info-circle recalculate-popup-alert-icon"></i>
					<div class="estimated-monthly-hourly-heading" style="padding: 2em 2em 2em;text-align: center;">Payroll estimation process takes few moments for crunching the numbers</div>
					<div style="text-align: center;padding: 0px 2em;">* By clicking the proceed button you are confirming to run the payroll estimator</div>
					<div style="text-align: center;padding: 2em 2em 2em;">
						<button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="cancelRecalculationConfirmation">Cancel</button>
						<button type="button" class="btn btn-secondary btn-embossed" id="proceedRecalculation">Proceed</button>
					</div>
				</div>
				<div class="col-md-6" style="background: #fef5f8; text-align: center; border-radius: 10px;">
					<i class="fa fa-question-circle recalculate-popup-alert-icon" style="color: var(--primary-color);"></i>
					<div class="estimated-monthly-hourly-heading">Payroll estimation is based on the current state of various components</div>
					<div style="display: flex; padding: 10px">
						<div class="number-legend">1</div>
						All the payroll prerequisites should be in complete or approved status to consider them for payroll estimation.
					</div>
					<div style="display: flex; padding: 10px">
						<div class="number-legend">2</div>
						Employees not having previous month payslip might not be having an accurate forecast for the current month payroll.
					</div>
					<div style="display: flex; padding: 10px">
						<div class="number-legend">3</div>
						Any update to salary component should be refreshed via salary recalculation method for accurate payroll estimation.
					</div>
				</div>
			</div>
			
			<div class="modal-footer" style="padding-bottom: 2em"></div>
		</div>
	</div>
</div>
<!-- MonthlySalary Grid Panel -->
<div class="col-md-12 portlets add-panel-padding">
	<div class="panel" id="gridPanelMonthlySalary">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong>Monthly Salary Payslip</strong></h3>
		</div>
		<div class="panel-content">			
			<div class="m-b-10">
					
					<!--Select all records-->
					
					<button type="button" class="btn btn-white btn-embossed toolbar-icons" title="Check All" id="monthlyCheckAll">
						<i class="hr-check-all"></i><span class="hidden-xs hidden-sm">Check All</span>
					</button>
					
					
					<!-- View Button in Grid Toolbar -->
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="viewMonthlySalary" title="View">
						<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
					</button>
					<input type="hidden" name="showReportCreator" id="showReportCreator" value="<?php echo $showReportCreator; ?>" />
					<!-- displayPayslipAddress -->
					<input type="hidden" name="displayPayslipAddress" id="displayPayslipAddress" value="<?php echo $displayPayslipAddress; ?>" />
					<!-- template count -->
					<input type="hidden" name="logEmpId" id="logEmpId" value="<?php echo $this->logEmpId; ?>" />
					<input type="hidden" name="formId" id="formId" value="<?php echo $this->formId; ?>" />
					<input type="hidden" name="financialClosureTracking" id="financialClosureTracking" value="<?php echo $financialClosureTracking; ?>" />
					<?php if ($salaryPayslip['Op_Choice']) { ?>
					<button type="button" class="btn btn-white btn-embossed visible-lg-* toolbar-icons" id="generateMonthlyPayslip" title="GeneratePayslip">
						<i class="fa fa-cog"></i><span class="hidden-xs hidden-sm"> Initiate Payslip</span>
					</button>
					
					<!-- Delete Button in Grid Toolbar -->
					<?php } if ($salaryPayslip['Delete']) { ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="deleteMonthlyPayslip" title="Delete">
						<i class="mdi-action-delete"></i><span class="hidden-xs hidden-sm"> Delete</span>
					</button>
					
					<?php } if ($salaryReviewAccess['Op_Choice'] && $payslipReviewProcess == 'Yes') { ?>
					<button type="button" class="btn btn-secondary-default btn-embossed btn-off disabled toolbar-icons" id="updatePayslipReviewStatus" title="Review Salary Payslip" >
						<i class="mdi-action-info-outline"></i><span class="hidden-xs hidden-sm"> Review Payslip </span>
					</button>
					<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="reviewAndReconcileRedirect" title="Review And Reconcile" >
						<i class="mdi-action-info-outline"></i><span class="hidden-xs hidden-sm"> Review and Reconcile </span>
					</button>
					<?php } ?>
					
					<!-- Export PDF -->
					<button type="button" class="btn btn-white btn-embossed toolbar-icons disabled btn-off" id="exportPdf" title="Export as Pdf" >
					<i class="fa fa-file-pdf-o"></i><span class="hidden-xs hidden-sm"> PDF</span>
					</button>
				
					<?php if ($salaryPayslip['Op_Choice']) { ?>
						<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="exportExcel" title="Export as Excel" >
							<i class="fa fa-table"></i><span class="hidden-xs hidden-sm"> Export Excel</span>
						</button>
					<?php } ?>
					
					<?php if ($salaryPayslip['Op_Choice'] && empty($serviceProviderAdmin['Employee']['Update'])) { ?>
					

					<div class="btn-group" style="margin-top: 0px;" id="buttonClosureMonthly">
						<button type="button" class="btn btn-whitep dropdown-toggle" data-toggle="dropdown" aria-expanded="true">
							<i class="hr-lock-alt"></i>
							<span class="hidden-xs hidden-sm">Closure</span>
							<span class="caret"></span>
							<div class="ripple-wrapper"></div>
						</button>
						<span class="dropdown-arrow dropdown-arrow-inverse"></span>
						<ul class="dropdown-menu dropdown-inverse" role="menu" style="width:100%">
							<li>
								<a id="quarterClosureMonthlyPayslip" style="cursor: pointer;">Quarter Closure</a>
							</li>	
							<li>
								<a id="revertClosureMonthlyPayslip" style="cursor: pointer;">Revert Closure</a>
							</li>
						</ul>	
					</div>	

					<?php } 
					
					if ($salaryPayslip['Op_Choice']) { ?>
					<button type="button" class="btn btn-white btn-embossed visible-lg-* toolbar-icons" id="tdsOverride" title="Tds Override">
						<i class="fa fa-cog"></i><span class="hidden-xs hidden-sm">TDS Override</span>
					</button>
					<?php }
					
					if ($salaryPayslip['Op_Choice']) { ?>
						<div class="btn-group" style="margin-top: 0px;" id="buttonPaymentStatusMonthly">
							<button type="button" class="btn btn-whitep dropdown-toggle" data-toggle="dropdown" aria-expanded="true">
								<i class="mdi-action-info-outline"></i>
								<span class="hidden-xs hidden-sm">Update Payment Status</span>
								<span class="caret"></span>
								<div class="ripple-wrapper"></div>
							</button>
							<span class="dropdown-arrow dropdown-arrow-inverse"></span>
							<ul class="dropdown-menu dropdown-inverse" role="menu" style="width:100%">
								<li>
									<a id="approveAndReleaseMonthly" style="cursor: pointer;">Approve & Release</a>
								</li>	
								<li>
									<a id="salaryHoldMonthly" style="cursor: pointer;">Withhold Salary</a>
								</li>
								<li>
									<a id="revertSalaryWithholdMonthly" style="cursor: pointer;">Revert Salary Withhold</a>
								</li>
							<?php if (empty($accountNumberList)) { ?>
								<li>
									<a id="paidMonthly" style="cursor: pointer;">Paid</a>
								</li>
								<li>
									<a id="revertPaidMonthly" style="cursor: pointer;">Revert Payment</a>
								</li>
							<?php } ?>
								
							</ul>
						</div>
					<?php }

					if ($fieldForce!=1) { ?>
					<button type="button" class="btn btn-white btn-embossed toolbar-icons disabled btn-off" id="formCommunicateEmployee" title="Notify In Email" >
					<i class="mdi-content-send"></i><span class="hidden-xs hidden-sm">Notify in Email</span>
					</button>
					<?php } ?>

					<!-- Filter Button in Grid Toolbar -->
					<a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterMonthlySalaryPayslip">
						<i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm">Filter</span>
					</a>
			</div>
			
			<!-- MonthlySalary Grid -->			
			<table class="table dataTable table-striped table-dynamic table-hover" id="tableMonthlySalary">
				<thead>
					<tr>
						<th></th>
						<th id="monthlyPayslipEmpId">Employee Id</th>
						<th id="monthlyPayslipEmpName">Employee Name</th>
						<th id="monthlyPayslipSalaryMonth">Salary Month</th>
						<th id="monthlyPayslipTotalSalary">Total Salary</th>
						<th id="monthlyPayslipPaymentStatus">Payment Status</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>		
		</div>
	</div>
</div>

<!-- MonthlySalary Grid Panel -->
<?php if ($salaryPayslip['Op_Choice'] == 1) { ?>
<div class="col-md-12 portlets hidden add-panel-padding" id="generatePayslipGrid">
	<div class="panel">
		<div class="panel-header">
			<h3><i class="icon-list"></i> <strong>Payslip Generation</strong></h3>
		</div>
		<div class="panel-content">	
			<!-- generate payslip wizard design -->
			<form id="payslipGenerationForm" data-style="simple" >
			<!-- design of first step in wizard -->
			<fieldset class="withScroll show-scroll">
				<div class="row">
					<div class="col-xlg-12 col-lg-12 col-md-12 col-sm-12 col-xs-12">
						<p class="m-t-10 m-b-20 f-16 payout-text-center-cls"><strong class="payout-title">Choose Frequency</strong></p>
					</div>
				</div>
				<div class="row">
					<!-- Monthly Salary Card -->
					<div class="col-xlg-12 col-lg-12 col-md-12 col-sm-12 col-xs-12 payout-sm-card-center-cls payout-text-center-cls payslip-card-hidden payout-card-div-padding">
						<div class="card small payslipCardTransition payout-card-border payout-card-width-cls payslip-monthly-card-margin-cls" paySourceType="Monthly Payslip" id="monthlyPayslipCard">
							<div class="card-image payout-card-image-div payout-text-center-cls">
								<?php echo '<img class="img-responsive" alt="Month" src="'.$monthlySalaryImg.'">'; ?>
							</div>
							<span class="p-card-title"><?php echo $payrollPeriod; ?></span>
						</div>
					</div>
					<!-- payslip month selecttion title -->
					<div class="col-xlg-6 col-lg-6 col-md-6 col-sm-12 col-xs-12">
						<p class="payslipPayrollHeading"><strong class="payout-title">Payroll Month</strong></p>
					</div>
					<!-- payslip month selecttion-->        
					<div class="col-xlg-6 col-lg-6 col-md-6 col-sm-12 col-xs-12 payslipMonthList">
						<select class="form-control payslipMonthListSelect" id="payslipMonthYear" data-search="true" name="Payslip Month Year">
							<?php
								foreach ($payslipMonthYear as $key => $row)
								{
									echo '<option value="'.$key.','.explode(',', $row)[1].'">'.$row.'</option>';
								}
							?>
						</select>
					</div>
				</div>
				<?php if (strtolower($payrollPeriod)=== 'bimonthly'){ ?>
				<div class="row m-t-20 m-b-10 ">
					<!-- service provider title -->
					<div class="col-xlg-6 col-lg-6 col-md-6 col-sm-12 col-xs-12 hidden" id="payPeriodTitle">
						<p class="payslipPayrollHeading"><strong class="payout-title">Pay Period</strong></p>
					</div>
					<!-- service provider combo -->        
					<div class="col-xlg-6 col-lg-6 col-md-6 col-sm-12 col-xs-12 payslipSalaryDate">
						<select class="form-control payslipMonthListSelect" id="payPeriod" data-search="true" name="Pay Period">
							<option value="First Half">First Half</option>
							<option value="Second Half">Second Half</option>
						</select>
					</div>
				</div>
				<?php } ?>
				<div class="row m-t-20 m-b-10 ">
					<!-- payslip duration title -->
					<div class="col-xlg-6 col-lg-6 col-md-6 col-sm-12 col-xs-12 hidden" id="salRangeTitle">
						<p class="payslipPayrollHeading"><strong class="payout-title">Salary Duration</strong></p>
					</div>
					<!-- payslip duration date range picker-->        
					<div class="col-xlg-6 col-lg-6 col-md-6 col-sm-12 col-xs-12 payslipSalaryDate">
						<div class="payslip-salary-date-input" id="payslipSalaryRange" ></div>
					</div>
				</div>
				<?php if ($fieldForce== 1) { ?>
				<div class="row m-t-20 m-b-10 ">
					<!-- service provider title -->
					<div class="col-xlg-6 col-lg-6 col-md-6 col-sm-12 col-xs-12 hidden" id="serviceProviderTitle">
						<p class="payslipPayrollHeading"><strong class="payout-title">Service Provider</strong></p>
					</div>
					<!-- service provider combo -->        
					<div class="col-xlg-6 col-lg-6 col-md-6 col-sm-12 col-xs-12 payslipSalaryDate">
						<select class="form-control payslipMonthListSelect" id="monthlyServiceProvider" data-search="true" name="Service Provider">
							<?php
								foreach ($serviceProvider as $key => $row)
								{
									if($key == $serviceProviderId)
									{
										echo '<option value="'.$key.'"  selected="selected">'.$row.'</option>';
									}
									else 
									{
										echo '<option value="'.$key.'">'.$row.'</option>';
									}
									
								}
							?>
						</select>
					</div>
				</div>
				<?php } ?> 
             					
				
			</fieldset>
			<!-- design of second step in wizard -->
			<fieldset class="withScroll show-scroll">
				<div class="col-xlg-12 col-lg-12 col-md-12 col-sm-12 col-xs-12">
					<p class="m-t-10 m-b-20 f-16 payout-text-center-cls"><strong class="payout-title">Choose your option</strong></p>
				</div>
				<!-- filter option display in chip format -->
				<div class="row">
					<div class="panel-content payout-history-card-font">
						<!-- filter by location -->
						<div class="col-xlg-3 col-lg-3 col-md-3 col-sm-6 col-xs-12 payout-history-title">
							<p><strong>FILTER BY LOCATION</strong></p>
							<div class="payslip-generation-multi-chip" id="locationChip">
								<div class="chip" >
									<span class="payslip-multi-chip-name" id="payslipGenerationLocationFilterChipName">All</span>
									<span class="chip-button-close chip-button-close-location hidden" id="payslipGenerationLocationFilterChipClose" role="button">x</span>
								</div>
							</div>
						</div>
						<!-- filter by department -->
						<div class="col-xlg-3 col-lg-3 col-md-3 col-sm-6 col-xs-12 payout-history-title">
							<p><strong>FILTER BY DEPARTMENT</strong></p>
							<div class="payslip-generation-multi-chip" id="deptChip">
								<div class="chip">
									<span class="payslip-multi-chip-name" id="payslipGenerationDepartmentFilterChipName">All</span>
									<span class="chip-button-close chip-button-close-department hidden" id="payslipGenerationDepartmentFilterChipClose" role="button">x</span>
								</div>
							</div>
						</div>
						<!-- filter by salary range slider -->
						<div class="col-xlg-3 col-lg-3 col-md-3 col-sm-6 col-xs-12 payout-history-title">
							<p><strong>FILTER BY SALARY RANGE</strong></p>
							<div class="col-xs-12 col-xlg-9 col-lg-9 col-md-8 col-sm-12 paddingCls">
								<input type="text" class="js-range-slider" id="salaryRangeSlider" name="my_range" value="" />
								<input type="hidden" name="maxMonthSalary" id="maxMonthSalary" value="<?php echo $maxMonthSalary; ?>" />
							</div>
							<div class="col-xs-12 col-xlg-3 col-lg-3 col-md-4 col-sm-12"> 
								<button type="button" id="salaryrangeSelectBtn" class="btn btn-sm btn-secondary hidden">Ok</button>
							</div>
						</div>
						<!-- filter by employee type -->
						<div class="col-xlg-3 col-lg-3 col-md-3 col-sm-6 col-xs-12 payout-history-title">
							<p><strong>FILTER BY EMPLOYEE TYPE</strong></p>
							<div class="payslip-generation-multi-chip" id="empTypeChip" > 
								<div class="chip">
									<span class="payslip-multi-chip-name" id="payslipGenerationEmployeeTypeFilterChipName">All</span>
									<span class="chip-button-close chip-button-close-employee-type hidden" id="payslipGenerationEmployeeTypeFilterChipClose" role="button">x</span>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!--XS screen filter and search button-->
				<div class="row payout-filter-btn-group hidden-sm hidden-md hidden-lg hidden-xlg">
					<div class="col-xs-12 payout-history-xs-filter-row-parent">
						<div class="payout-btn payout-filter-btn btn-group">
							<button type="button" class="btn payout-btn payout-filter-btn-hgt payout-pagination-filter-border-right col-xs-4" data-toggle="dropdown" aria-expanded="true">
								<?php echo '<img class="img-responsive payout-filter-img" alt="Filter" src="'.$this->filterImg.'">'; ?>
								<div class="ripple-wrapper"></div>
							</button>                            
							<!-- search to find employees -->
							<div class="col-xs-8 append-icon payout-pagination-search-border-right">
								<input type="text" class="form-control payout-search-box" id="searchPayslipTransactionDetails" placeholder="Search" >
								<i class="fa fa-search payout-search-icon"></i>
							</div>

							<ul class="dropdown-menu dropdown-inverse payout-filter-dropdown" role="menu">                            
								<div class="arrow-up-border">
								</div>
								<div class="arrow-up">
								</div>
												
								<div>
									<span><?php echo '<img class="img-responsive payout-filter-img" alt="Filter" src="'.$this->filterImg.'">'; ?>Filter</span>
									<button type="button" class="btn btn-sm payout-filter-reset-btn resetPayoutFilter">Reset<div class="ripple-wrapper"></div></button>
								</div>

								<div class="row">
								<!-- select location to filter -->
									<div class="col-xlg-4 col-lg-4 col-md-4 col-sm-12 col-xs-12 payout-filter-options">
										<div class="col-sm-12">Location</div>
										<div class="col-sm-12">
											<select multiple="multiple" class="form-control selectAlll" data-search="true" id="filterPayslipGenerationLocationXS" >
												<option value="selectAll">--Select all--</option>
												<option value="clearAll">--Clear all--</option>
												<?php
												foreach ( $locationPair as $key => $row )
												{
													echo '<option value="'. $key .'">'. $row .'</option>';
												}
												?>
											</select>
										</div>
									</div>
									<!-- select department to filter -->
									<div class="col-xlg-4 col-lg-4 col-md-4 col-sm-12 col-xs-12 payout-filter-options">
										<div class="col-sm-12">Department</div>
										<div class="col-sm-12">
											<select multiple="multiple" class="form-control selectAlll" data-search="true" id="filterPayslipGenerationDepartmentXS" >
												<option value="selectAll">--Select all--</option>
												<option value="clearAll">--Clear all--</option>
												<?php
												foreach ( $departmentPair as $key => $row )
												{
													echo '<option value="'. $key .'">'. $row .'</option>';
												}
												?>
											</select>
										</div>
									</div>
									<!-- select employee type to filter -->
									<div class="col-xlg-4 col-lg-4 col-md-4 col-sm-12 col-xs-12 payout-filter-options">
										<div class="col-sm-12">Employee Type</div>
										<div class="col-sm-12">
											<select multiple="multiple" class="form-control selectAlll" data-search="true" id="filterPayslipGenerationEmployeeTypeXS" >
												<option value="selectAll">--Select all--</option>
												<option value="clearAll">--Clear all--</option>
												<?php
												foreach ( $employeeTypePair as $key => $row )
												{
													echo '<option value="'. $key .'">'. $row .'</option>';
												}
												?>
											</select>
										</div>
									</div>
								</div>
							</ul>
						</div>
					</div>
				</div>
				<!-- Filter , search , select, deselct-->
				<div class="row payout-filter-btn-group">
					<!-- filter , search, pagination -->
					<div class="col-sm-12 col-md-12 col-lg-10 col-xlg-5 payout-filter-row-parent">
						<div class="btn-group payout-btn payout-filter-btn">                        
							<button type="button" class="btn payout-btn payout-filter-btn-hgt hidden-xs col-sm-3" data-toggle="dropdown" aria-expanded="true">
								<?php echo '<img class="img-responsive payout-filter-img" alt="Filter" src="'.$this->filterImg.'">'; ?>
								<span>Filter</span>
								<div class="ripple-wrapper"></div>
							</button>
							
							<div class="hidden-xs col-sm-3 append-icon payout-pagination-search-border-right">
								<input type="text" class="form-control payout-search-box" id="searchPayslipGenerationTransaction" placeholder="Search" >
								<i class="fa fa-search  payout-search-icon"></i>
							</div>

							<!-- Pagination -->
							<div class="col-xlg-6 col-lg-6 col-md-6 col-sm-6 col-sm-9 payout-history-filter-row payout-text-center-cls">
								<div id="paginationStep2" class="custom-pagination">
								</div>
							</div>
					
							<ul class="dropdown-menu dropdown-inverse payout-filter-dropdown" role="menu">
								<div class="arrow-up-border">
								</div>
								<div class="arrow-up">
								</div>
												
								<div>
									<span><?php echo '<img class="img-responsive payout-filter-img" alt="Filter" src="'.$this->filterImg.'">'; ?>Filter</span>
									<button type="button" class="btn btn-sm payout-filter-reset-btn resetPayoutFilter">Reset<div class="ripple-wrapper"></div></button>
								</div>

								<div class="row">
								<!-- select location to filter -->
									<div class="col-xlg-4 col-lg-4 col-md-4 col-sm-12 col-xs-12 payout-filter-options">
										<div class="col-sm-12">Location</div>
										<div class="col-sm-12">
											<select multiple="multiple" class="form-control selectAlll" data-search="true" id="filterPayslipGenerationLocation" >
												<option value="selectAll">--Select all--</option>
												<option value="clearAll">--Clear all--</option>
												<?php
												foreach ( $locationPair as $key => $row )
												{
													echo '<option value="'. $key .'">'. $row .'</option>';
												}
												?>
											</select>
										</div>
									</div>
									<!-- select department to filter -->
									<div class="col-xlg-4 col-lg-4 col-md-4 col-sm-12 col-xs-12 payout-filter-options">
										<div class="col-sm-12">Department</div>
										<div class="col-sm-12">
											<select multiple="multiple" class="form-control selectAlll" data-search="true" id="filterPayslipGenerationDepartment" >
												<option value="selectAll">--Select all--</option>
												<option value="clearAll">--Clear all--</option>
												<?php
												foreach ( $departmentPair as $key => $row )
												{
													echo '<option value="'. $key .'">'. $row .'</option>';
												}
												?>
											</select>
										</div>
									</div>
									<!-- select employee type to filter -->
									<div class="col-xlg-4 col-lg-4 col-md-4 col-sm-12 col-xs-12 payout-filter-options">
										<div class="col-sm-12">Employee Type</div>
										<div class="col-sm-12">
											<select multiple="multiple" class="form-control selectAlll" data-search="true" id="filterPayslipGenerationEmployeeType" >
												<option value="selectAll">--Select all--</option>
												<option value="clearAll">--Clear all--</option>
												<?php
												foreach ( $employeeTypePair as $key => $row )
												{
													echo '<option value="'. $key .'">'. $row .'</option>';
												}
												?>
											</select>
										</div>
									</div>
								</div>
							</ul>
						</div>
					</div>
					<!-- payslip status selection -->
					<div class="col-sm-12 col-md-12 col-lg-10 col-xlg-4 payslipStatusCls">
						<!-- payslip status title -->
						<div class="col-lg-4 col-xlg-4 col-sm-4 col-md-4 col-xs-12 payslipStatusTitle">
							<span>Payslip Status</span>
						</div>
						<!-- payslip status option -->
						<div class="col-lg-6 col-xlg-6 col-sm-6 col-md-6 col-xs-12 payout-text-center-cls">
							<select class="form-control selectAlll" id="filterPayslipStatus" >
								<option value="All">All</option>
								<option value="Payslip Generated">Payslip Generated</option>
								<option value="Pending Approval">Pending Approval</option>
								<option value="Ready To Generate">Ready To Generate</option>
							</select>
						</div>
					</div>
					<!-- select all and deselect all checkbox -->
					<div class="col-sm-12 col-md-12 col-lg-10 col-xlg-3 payout-filter-component">
						<div class="col-lg-5 col-xlg-6 col-sm-6 col-md-6 col-xs-12 checkbox checkbox-secondary select-payout-checkbox" id="payslipSelectCheckbox">
							<label>
								<input type="checkbox" id="payslipGenerationSelectAll" class="md-checkbox selectPayoutCheckbox" required="" aria-required="true">
							</label>
							<span>Select all</span>
						</div>
						<div class="col-lg-5 col-xlg-6 col-sm-6 col-md-6 col-xs-12 checkbox checkbox-secondary deselect-payout-checkbox" id="payslipDeSelectCheckbox">
							<label>
								<input type="checkbox" id="payslipGenerationDeSelectAll" class="md-checkbox deselectPayoutCheckbox" required="" aria-required="true">
								<span>Deselect all</span>
							</label>
						</div>
					</div>
				</div>
				<!-- line between cards and upper portion -->
				<div class="payout-line">
				</div>
				<!--Div to append the payslip employees ard-->
				<div id="payslipEmployeesViewCard" >
				</div>
				<div class="col-xs-12"></div>
				<div class="col-xs-12 payout-history-footer">
					<div id="payslipStatusList" class="col-xs-12 col-sm-8 col-md-8 col-lg-6 col-xlg-6 payslipStatusList payout-text-center-cls">
						<!-- payslip already generated -->
						<div class="col-lg-4 col-xlg-4 col-sm-4 col-md-4 col-sm-4 col-xs-12 payout-text-center-cls" id="generatedSelect">
								<label>
									<input disabled class="generatedColorCode" required="" aria-required="true">
									<span  class="generated-payslip-text">Payslip Generated</span>
								</label>
						</div>
						<!-- payslip already generated -->
						<div class="col-lg-4 col-xlg-4 col-sm-4 col-md-4 col-sm-4 col-xs-12 payout-text-center-cls" id="pendingApprovalSelect">
							<label>
								<input disabled  class="pendingApprovalColorCode" required="" aria-required="true">
								<span  class="pending-approval-text">Pending Approval</span>
							</label>
						</div>
						<!-- ready to generate payslip -->
						<div class="col-lg-4 col-xlg-4 col-sm-4 col-md-4 col-sm-4 col-xs-12 payout-text-center-cls" id="readyToGenSelect">
							<label>
								<input disabled  class="readyGenColorCode" required="" aria-required="true">
								<span class="ready-to-generate-text">Ready To Generate</span>
							</label>
						</div>
					</div>
				</div>
			</fieldset>
			<fieldset class="withScroll show-scroll">
				<div class="col-xlg-12 col-lg-12 col-md-12 col-sm-12 col-xs-12">
					<p class="m-t-10 m-b-20 f-16 payout-text-center-cls"><strong class="payout-title">Payslip Generated Employees List</strong></p>
				</div>
				<!-- filter option display in chip format -->
				<div class="row">
					<div class="panel-content payout-history-card-font">
						<!-- filter by location -->
						<div class="col-xlg-3 col-lg-3 col-md-3 col-sm-6 col-xs-12 payout-history-title">
							<p><strong>FILTER BY LOCATION</strong></p>
							<div class="payslip-generation-multi-chip" id="locationChipLastStep">
								<div class="chip" >
									<span class="payslip-multi-chip-name" id="payslipGenerationLocationFilterChipNameLastStep">All</span>
									<span class="chip-button-close chip-button-close-location hidden" id="payslipGenerationLocationFilterChipCloseLastStep" role="button">x</span>
								</div>
							</div>
						</div>
						<!-- filter by department -->
						<div class="col-xlg-3 col-lg-3 col-md-3 col-sm-6 col-xs-12 payout-history-title">
							<p><strong>FILTER BY DEPARTMENT</strong></p>
							<div class="payslip-generation-multi-chip" id="deptChipLastStep">
								<div class="chip">
									<span class="payslip-multi-chip-name" id="payslipGenerationDepartmentFilterChipNameLastStep">All</span>
									<span class="chip-button-close chip-button-close-department hidden" id="payslipGenerationDepartmentFilterChipCloseLastStep" role="button">x</span>
								</div>
							</div>
						</div>
						<!-- filter by salary range slider -->
						<div class="col-xlg-3 col-lg-3 col-md-3 col-sm-6 col-xs-12 payout-history-title">
							<p><strong>FILTER BY SALARY RANGE</strong></p>
							<div class="col-xs-12 col-xlg-9 col-lg-9 col-md-8 col-sm-12 paddingCls">
							<input type="text" class="js-range-slider" id="salaryRangeSliderLastStep" name="my_range" value="" />
							</div>
							<div class="col-xs-12 col-xlg-3 col-lg-3 col-md-4 col-sm-12"> 
								<button type="button" id="salaryrangeSelectBtnLastStep" class="btn btn-sm btn-secondary hidden">Ok</button>
							</div>
						</div>
						<!-- filter by employee type -->
						<div class="col-xlg-3 col-lg-3 col-md-3 col-sm-6 col-xs-12 payout-history-title">
							<p><strong>FILTER BY EMPLOYEE TYPE</strong></p>
							<div class="payslip-generation-multi-chip" id="empTypeChipLastStep" > 
								<div class="chip">
									<span class="payslip-multi-chip-name" id="payslipGenerationEmployeeTypeFilterChipNameLastStep">All</span>
									<span class="chip-button-close chip-button-close-employee-type hidden" id="payslipGenerationEmployeeTypeFilterChipCloseLastStep" role="button">x</span>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!--XS screen filter and search button-->
				<div class="row payout-filter-btn-group hidden-sm hidden-md hidden-lg hidden-xlg">
					<div class="col-xs-12 payout-history-xs-filter-row-parent">
						<div class="payout-btn payout-filter-btn btn-group">
							<button type="button" class="btn payout-btn payout-filter-btn-hgt payout-pagination-filter-border-right col-xs-4" data-toggle="dropdown" aria-expanded="true">
								<?php echo '<img class="img-responsive payout-filter-img" alt="Filter" src="'.$this->filterImg.'">'; ?>
								<div class="ripple-wrapper"></div>
							</button>                            
							<!-- search to find employees list -->
							<div class="col-xs-8 append-icon payout-pagination-search-border-right">
								<input type="text" class="form-control payout-search-box" id="searchPayslipTransactionDetailsLastStep" placeholder="Search" >
								<i class="fa fa-search payout-search-icon"></i>
							</div>

							<ul class="dropdown-menu dropdown-inverse payout-filter-dropdown" role="menu">                            
								<div class="arrow-up-border">
								</div>
								<div class="arrow-up">
								</div>
												
								<div>
									<span><?php echo '<img class="img-responsive payout-filter-img" alt="Filter" src="'.$this->filterImg.'">'; ?>Filter</span>
									<button type="button" class="btn btn-sm payout-filter-reset-btn resetPayoutFilterLast">Reset<div class="ripple-wrapper"></div></button>
								</div>

								<div class="row">
								<!-- select location to filter -->
									<div class="col-xlg-4 col-lg-4 col-md-4 col-sm-12 col-xs-12 payout-filter-options">
										<div class="col-sm-12">Location</div>
										<div class="col-sm-12">
											<select multiple="multiple" class="form-control selectAlll" data-search="true" id="filterPayslipGenerationLocationXSLastStep" >
												<option value="selectAll">--Select all--</option>
												<option value="clearAll">--Clear all--</option>
												<?php
												foreach ( $locationPair as $key => $row )
												{
													echo '<option value="'. $key .'">'. $row .'</option>';
												}
												?>
											</select>
										</div>
									</div>
									<!-- select department to filter -->
									<div class="col-xlg-4 col-lg-4 col-md-4 col-sm-12 col-xs-12 payout-filter-options">
										<div class="col-sm-12">Department</div>
										<div class="col-sm-12">
											<select multiple="multiple" class="form-control selectAlll" data-search="true" id="filterPayslipGenerationDepartmentXSLastStep" >
												<option value="selectAll">--Select all--</option>
												<option value="clearAll">--Clear all--</option>
												<?php
												foreach ( $departmentPair as $key => $row )
												{
													echo '<option value="'. $key .'">'. $row .'</option>';
												}
												?>
											</select>
										</div>
									</div>
									<!-- select employee type to filter -->
									<div class="col-xlg-4 col-lg-4 col-md-4 col-sm-12 col-xs-12 payout-filter-options">
										<div class="col-sm-12">Employee Type</div>
										<div class="col-sm-12">
											<select multiple="multiple" class="form-control selectAlll" data-search="true" id="filterPayslipGenerationEmployeeTypeXSLastStep" >
												<option value="selectAll">--Select all--</option>
												<option value="clearAll">--Clear all--</option>
												<?php
												foreach ( $employeeTypePair as $key => $row )
												{
													echo '<option value="'. $key .'">'. $row .'</option>';
												}
												?>
											</select>
										</div>
									</div>
								</div>
							</ul>
						</div>
					</div>
				</div>
				<!-- Filter , search , select, deselct-->
				<div class="row payout-filter-btn-group">
					<!-- filter , search, pagination -->
					<div class="col-sm-12 col-md-12 col-lg-10 col-xlg-5 payout-filter-row-parent">
						<div class="btn-group payout-btn payout-filter-btn">                        
							<button type="button" class="btn payout-btn payout-filter-btn-hgt hidden-xs col-sm-3" data-toggle="dropdown" aria-expanded="true">
								<?php echo '<img class="img-responsive payout-filter-img" alt="Filter" src="'.$this->filterImg.'">'; ?>
								<span>Filter</span>
								<div class="ripple-wrapper"></div>
							</button>
							<!-- search to find employees list -->
							<div class="hidden-xs col-sm-3 append-icon payout-pagination-search-border-right">
								<input type="text" class="form-control payout-search-box" id="searchPayslipGenerationTransactionLastStep" placeholder="Search" >
								<i class="fa fa-search  payout-search-icon"></i>
							</div>

							<!-- Pagination -->
							<div class="col-xlg-6 col-lg-6 col-md-6 col-sm-6 col-sm-9 payout-history-filter-row payout-text-center-cls">
								<div id="paginationLastStep">
								</div>
							</div>
					
							<ul class="dropdown-menu dropdown-inverse payout-filter-dropdown" role="menu">
								<div class="arrow-up-border">
								</div>
								<div class="arrow-up">
								</div>
												
								<div>
									<span><?php echo '<img class="img-responsive payout-filter-img" alt="Filter" src="'.$this->filterImg.'">'; ?>Filter</span>
									<button type="button" class="btn btn-sm payout-filter-reset-btn resetPayoutFilterLast">Reset<div class="ripple-wrapper"></div></button>
								</div>

								<div class="row">
								<!-- select location to filter -->
									<div class="col-xlg-4 col-lg-4 col-md-4 col-sm-12 col-xs-12 payout-filter-options">
										<div class="col-sm-12">Location</div>
										<div class="col-sm-12">
											<select multiple="multiple" class="form-control selectAlll" data-search="true" id="filterPayslipGenerationLocationLastStep" >
												<option value="selectAll">--Select all--</option>
												<option value="clearAll">--Clear all--</option>
												<?php
												foreach ( $locationPair as $key => $row )
												{
													echo '<option value="'. $key .'">'. $row .'</option>';
												}
												?>
											</select>
										</div>
									</div>
									<!-- select department to filter -->
									<div class="col-xlg-4 col-lg-4 col-md-4 col-sm-12 col-xs-12 payout-filter-options">
										<div class="col-sm-12">Department</div>
										<div class="col-sm-12">
											<select multiple="multiple" class="form-control selectAlll" data-search="true" id="filterPayslipGenerationDepartmentLastStep" >
												<option value="selectAll">--Select all--</option>
												<option value="clearAll">--Clear all--</option>
												<?php
												foreach ( $departmentPair as $key => $row )
												{
													echo '<option value="'. $key .'">'. $row .'</option>';
												}
												?>
											</select>
										</div>
									</div>
									<!-- select employee type to filter -->
									<div class="col-xlg-4 col-lg-4 col-md-4 col-sm-12 col-xs-12 payout-filter-options">
										<div class="col-sm-12">Employee Type</div>
										<div class="col-sm-12">
											<select multiple="multiple" class="form-control selectAlll" data-search="true" id="filterPayslipGenerationEmployeeTypeLastStep" >
												<option value="selectAll">--Select all--</option>
												<option value="clearAll">--Clear all--</option>
												<?php
												foreach ( $employeeTypePair as $key => $row )
												{
													echo '<option value="'. $key .'">'. $row .'</option>';
												}
												?>
											</select>
										</div>
									</div>
								</div>
							</ul>
						</div>
					</div>
				</div>
				<!-- line between cards and upper portion -->
				<div class="payout-line">
				</div>
				<!--Div to append the payslip generated employees card-->
				<div id="payslipGeneratedEmployeesViewCard" >
				</div>
				<div class="col-xs-12"></div>
				<div class="col-xs-12 payout-history-footer">
					<div id="payslipStatusListLast" class="col-xs-12 col-sm-8 col-md-8 col-lg-6 col-xlg-6 payslipStatusList payout-text-center-cls">
						<!-- payslip generated color code-->
						<div class="col-lg-4 col-xlg-4 col-sm-4 col-md-4 col-sm-4 col-xs-12 payout-text-center-cls payslipStatusList" id="generatedSelectLastStep">
								<label>
									<input disabled class="generatedColorCode" required="" aria-required="true">
									<span  class="generated-payslip-text">Payslip Generated</span>
								</label>
						</div>
					</div>
				</div>
			</fieldset>
			</form>
			<div class="txn-type-footer"> 
                <p class="payslipGenerationNote"> Note: There is mix of payslip status. Please select appropriate employee records to either approve or generate payslip.</p>
            </div>
		</div>
	</div>
</div>
<?php } if ($salaryReviewAccess['Op_Choice'] == 1 && $payslipReviewProcess == 'Yes') {?>
<div class="modal fade" id="modalPayslipReviewStatus" aria-hidden="true" style="z-index: 10;">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" id="modalPayslipReviewFormClose" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace"></i>
				</button>
				<h4 class="modal-title"> Update Review Status</h4>
			</div>
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="payslipReviewStatusForm" method="POST" action="">
					<input type="hidden" name="Payslip_Id" id="formReviewPayslipId" />
					<div class="row">
						<div class="form-group">
							<label class="col-md-4 control-label">Status <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control vRequired" name="Status" id="payslipReviewStatus">
									<option value="Review Completed">Review Completed</option>
									<option value="Rejected">Rejected</option>
								</select>
							</div>
						</div>
					</div>
				</form>
				
			</div>
			<div class="modal-footer text-center">
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button"  data-style="expand-left" id="formResetReviewStatus" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button"  data-style="expand-left" data-style="expand-left" id="formSubmitPayslipReview" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
			</div>
		</div>
	</div>
</div>
<!-- Form Dirty Confirmation Modal For Review Status Approval-->
<div class="modal fade" id="modalDirtyPayslipReviewStatus" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditStatusConfReimbursement"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="notClosePayslipReviewForm">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseConfirmPayslipReview">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } ?>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="monthly-payslip-context-menu" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextMonthlySalary"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($salaryPayslip['View']) { ?>
		<li><a tabindex="-1" id="pdfContextMonthlySalary"><i class="fa fa-file-pdf-o"></i> PDF</a></li>
		<?php } if ($salaryPayslip['Delete']) { ?>
		<li><a tabindex="-1" id="deleteContextMonthlyPayslip"><i class="mdi-action-delete"></i> Delete</a></li>		
		<?php } if ($salaryReviewAccess['Op_Choice'] && $payslipReviewProcess == 'Yes') { ?>
		<li><a tabindex="-1" id="updateContextPayslipReviewStatus"><i class="mdi-action-delete"></i> Delete</a></li>		
		<?php } ?>		
	</ul>
</div>


<!--Filter Form-->
<div class="builder" id="filterPanelMonthlySalaryPayslip">
	<div id="closeFilterMonthlySalaryPayslip"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel" style="width: 100%;" id="cancelMonthlySalary">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="applyMonthlySalaryPayslip">Apply</button>
			
			<!--Filter for Employee Name-->
			<?php if ($salaryPayslip['View'] == 1 && $salaryPayslip['Is_Manager'] == 0 && empty($salaryPayslip['Admin'])) { ?>
			
			<div class="form-group">
				<label>Employee Name</label>
				<input type="text" class="form-control" id="filterEmployeeName" readonly="readonly" value="<?php echo $salaryPayslip['Employee_Name']; ?>" >
			</div>
			
			<?php } else { ?>
			
			<div class="form-group">
				<label>Employee Name</label>
				<input type="text" class="form-control" id="filterEmployeeName" placeholder="Employee Name" >
			</div>
			
			<?php } ?>
			
			<!-- Filter for Salary Month -->
			<div class="form-group">
				<label>Salary Month</label>
				 <input type="text" class="b-datepicker form-control closeMonthPicker" data-date-format="MM,yyyy" data-view="1" data-date-min-view-mode=1 data-date-autoclose="true" id="filterSalaryMonth" data-orientation="top">
			</div>			
			
			<!--Filter For Total Amount -->
			<div class="form-group">
				<label>Total Salary</label>
				<div class="input-group">
					<input type="number" class="form-control" name="totalSalaryStart" id="filterTotalSalaryStart" min="0" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="form-control" name="totalSalaryEnd" id="filterTotalSalaryEnd" min="0" placeholder="End"/>
				</div>
			</div>
            
            <div class="form-group">
				<label>Employee Type</label>
				<select class="form-control" data-search="true" id="filterMonEmployeeType" >
					<option value="">All</option>
					<?php
					foreach ( $employeeTypePair as $key => $row )
					{
						echo '<option value="'. $key .'">'. $row .'</option>';
					}
					?>
				</select>
			</div>
            
            <div class="form-group">
				<label>Location</label>
				<select class="form-control" data-search="true" id="filterMonLocations" >
					<option value="">All</option>
					<?php
					foreach ( $locationPair as $key => $row )
					{
						echo '<option value="'. $key .'">'. $row .'</option>';
					}
					?>
				</select>
			</div>
            
            <div class="form-group">
				<label>Department</label>
				<select class="form-control" data-search="true" id="filterMonDepartment" >
					<option value="">All</option>
					<?php
					foreach ($deptHierarchy as $key => $row)
					{
						echo '<option value="'. $row['Department_Id'] .'">'. $row['Department_Name'] .'</option>';
						
						foreach($row['Child'] as $val =>$name)
						{
							
							if($name['Parent_Type_Id'] == $row['Department_Id'])
							{
								echo '<option value="'. $name['Department_Id'] .'">&nbsp;&nbsp;'. $name['Department_Name'] .'</option>';
								
								foreach($row['Child'] as $v =>$k)
								{
									if($k['Parent_Type_Id'] == $name['Department_Id'])
										echo '<option value="'. $k['Department_Id'] .'">&nbsp;&nbsp;&nbsp;&nbsp;'. $k['Department_Name'] .'</option>';
								}
								
							}
						}
					}
					?>
                    
				</select>
			</div>

			<div class="form-group">
				<label>Payment Status</label>
				<select class="form-control" data-search="true" id="filterMonPaymentStatus" >
					<option value="">All</option>
					<?php
					foreach ( $paymentStatusPair as $paymentStatus)
					{
						echo '<option value="'. $paymentStatus .'">'. $paymentStatus .'</option>';
					}
					?>
				</select>
			</div>

			<?php if ($fieldForce== 1) { ?>
				<div class="form-group">
					<label>Service Provider</label>
					<select class="form-control" data-search="true" id="filterMonthlyServiceProvider">
						<?php
						foreach ($serviceProvider as $key => $row)
						{
							if($key == $serviceProviderId)
							{
								echo '<option value="'.$key.'"  selected="selected">'.$row.'</option>';
							}
							else
							{
								echo '<option value="'. $key .'">'. $row .'</option>';
							}
						}
						?>
					</select>												
				</div>
			<?php } ?>
		</div>
	</div>
</div>

<div class="modal fade" id="modalBulkPDFMonthlyPayslip" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeBulkPDFMonthlyPayslip"></i></button>
				<!--<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>-->
			</div>
			
			<div class="modal-body">This process will be completed in 2 - 20 mins</div>
			
			<div class="modal-footer" id="bulkPDFFooter">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="bulkPDFMonthlyPayslip">OK</button>
			  <!--<button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConfirmMonthlyPayslip">Yes</button>-->
			</div>
		</div>
	</div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="modalDeleteMonthlyPayslip" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteConfMonthlyPayslip"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer" id="deleteFooter">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfMonthlyPayslip">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConfirmMonthlyPayslip">Yes</button>
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="modalDeletePdfExport" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteConfPdfExport"></i></button>
				<h4 class="modal-title"><strong>Pdf Export</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">You have already initiated a Monthly Payslip Export process. Do you want to restart the process ?<br></div>
			
			<div class="modal-footer" id="deleteFooter">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfPdfExport">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConfirmPdfExport">Yes</button>
			  
			</div>
		</div>
	</div>
</div>

<!-- Generate Monthly Salary Payslip Form-->
<div class="modal fade" id="modalFormMonthlySalary" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" id="viewPageCloseMonthlySalary" style="margin-right: 10px;" data-dismiss="modal">
					<i class="mdi-hardware-keyboard-backspace"></i>
				</button>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<div id="payslipGridMsgPanel"></div>
				<!--<div style="text-align: right;height: 40px;font-size:10px" id="formReport"></div>	-->
                <!--View Monthly Salary Payslip Form-->
				<form role="form" id="viewFormMonthlyPayslip" style="overflow: hidden;"> <!--style="/*height: 1500px*/border: 2px solid rgba(0,0,0,0.4);"-->
					<div>
						<div style="text-align:center;font-weight:bold;margin-bottom:30px;height: 20px;font-size: large;" id="payMonth"></div>
                        <!--<div style="text-align: right;height: 40px;font-size:10px" id="formReport"></div>-->
						
                        <!--<table style="width:100%">-->
                        <!--    <tr>-->
                        <!--        <td>-->
                                    <!--<div style="height: 100%" class = "" id="images"></div>-->
                                <!--</td>-->
                                <!--<td>-->
                                    <!--<div class="col-md-12">-->
                                    <!--    <div class = "col-md-6" id="images"></div>-->
                                    <!--    <div class="col-md-6" style="height: 100%">-->
                                    <!--        <div style="text-align:right;font-weight:bold"  id="formHeader"></div>-->
                                    <!--        <div style="text-align:right"  id="formAddress"></div>							-->
                                    <!--        <div style="text-align:right" id="formAddress1"></div>-->
                                    <!--   </div>-->
                                    <!--</div>-->
                        <!--        </td>-->
                        <!--    </tr>-->
                        <!--</table>-->
                        
                        <div class="col-md-12 col-xs-12 col-sm-0 form-group" style="padding: 0px;"> 
							<div class = "col-md-5 col-sm-6" id="images"></div>
                            <div class="col-md-7 col-sm-6" style="height: 100%;padding-right:0%">
                                <div style="text-align:right;font-weight:bold"  id="formHeader"></div>
                                <div style="text-align:right"  id="formAddress"></div>							
                                <div style="text-align:right" id="formAddress1"></div>
                            </div>
						</div>
					</div>
					
					
					<!--<div style="text-align:center;font-weight:bold;margin-top:20px" id="payHourlyMonth"></div>-->
                    
                    
					<div class="row" id="empDetails" style="margin-top:30px">
						<div class="col-md-6 form-group"> 
							<label class="col-md-5 col-sm-5 control-label"> Employee Id :</label>
							<div class="col-md-7 col-sm-7"><p id="viewMonthlyEmployeeId"></p></div>
						</div>
						<div class="col-md-6 form-group"> 
							<label class="col-md-5 col-sm-5 control-label"> Employee Name :</label>
							<div class="col-md-7 col-sm-7"><p id="viewMonthlyEmployeeName"></p></div>
						</div>
						<!-- To do -->
						<!-- <div class="col-md-6 form-group"> 
							<label class="col-md-5 col-sm-5 control-label"> Father's Name :</label>
							<div class="col-md-7 col-sm-7"><p id="viewMonthlyFatherName"></p></div>
						</div> -->
						<div class="col-md-6 form-group"> 
							<label class="col-md-5 col-sm-5 control-label"> Department :</label>
							<div class="col-md-7 col-sm-7"><p id="viewMonthlyDepartment"></p></div>
						</div>
						<div class="col-md-6 form-group"> 
							<label class="col-md-5 col-sm-5 control-label"> Designation :</label>
							<div class="col-md-7 col-sm-7"><p id="viewMonthlyDesignation"></p></div>
						</div>
						<div class="col-md-6 form-group"> 
							<label class="col-md-5 col-sm-5 control-label"> Date Of Join :</label>
							<div class="col-md-7 col-sm-7"><p id="viewMonthlyDateOfJoin"></p></div>
						</div>
						<div class="col-md-6 form-group">
							<label class="col-md-5 col-sm-5 control-label"> ESI/Insurance No. :</label>
							<div class="col-md-7 col-sm-7"><p id="viewMonthlyESI"></p></div>
						</div>
						<?php if($pfNumber['Enable'] == 1) { ?>
						<div class="col-md-6 form-group"> 
							<label class="col-md-5 col-sm-5 control-label"><?php echo $pfNumber['Field_Name'];?> :</label>
							<div class="col-md-7 col-sm-7"><p id="viewMonthlyPFAccountNumber"></p></div>
						</div>
						<?php } ?>
						<!-- <div class="col-md-6 form-group">
							<label class="col-md-5 col-sm-5 control-label"> Division :</label>
							<div class="col-md-7 col-sm-7"><p id="viewMonthlyDivision"></p></div>
						</div> -->
						<div class="col-md-6 form-group">
							<label class="col-md-5 col-sm-5 control-label">PF UAN No. :</label>
							<div class="col-md-7 col-sm-7"><p id="viewMonthlUAN"></p></div>
						</div>
						<?php if($pan['Enable'] == 1) { ?>
						<div class="col-md-6 form-group"> 
							<label class="col-md-5 col-sm-5 control-label"><?php echo $pan['Field_Name'];?> :</label>
							<div class="col-md-7 col-sm-7"><p id="viewMonthlyPANNo"></p></div>
						</div>
						<?php } ?>
					
						
						<!-- 						
						<div class="col-md-6 form-group"> 
							<label class="col-md-5 col-sm-5 control-label"> Salary Day :</label>
							<div class="col-md-7 col-sm-7"><p id="viewMonthlySalaryDay"></p></div>
						</div> -->
						<div class="col-md-6 form-group"> 
							<label class="col-md-5 col-sm-5 control-label"> Bank Name :</label>
							<div class="col-md-7 col-sm-7"><p id="viewMonthlyBankName"></p></div>
						</div>
						<div class="col-md-6 form-group"> 
							<label class="col-md-5 col-sm-5 control-label"> Bank Account No. :</label>
							<div class="col-md-7 col-sm-7"><p id="viewMonthlyBankAccountNo"></p></div>
						</div>
						
						<!-- <div class="col-md-6 form-group"> 
							<label class="col-md-5 col-sm-5 control-label"> HoliDay Days :</label>
							<div class="col-md-7 col-sm-7"><p id="viewMonthlyHoliyDay"></p></div>
						</div> -->
						
						<div class="col-md-6 form-group"> 
							<label class="col-md-5 col-sm-5 control-label"> Paid Leave :</label>
							<div class="col-md-7 col-sm-7"><p id="viewMonthlyPaidLeave"></p></div>
						</div>
						
						
						
												
						<div class="col-md-6 form-group"> 
							<label class="col-md-5 col-sm-5 control-label">  Unpaid Leave :</label>
							<div class="col-md-7 col-sm-7"><p id="viewMonthlyUnpaidLeave"></p></div>
						</div>
						<div class="col-md-6 form-group">
							<label class="col-md-5 col-sm-5 control-label"> Days Worked :</label>
							<div class="col-md-7 col-sm-7"><p id="viewMonthlyDaysWorked"></p></div>
						</div>
						<!-- <div class="col-md-6 form-group"> 
							<label class="col-md-5 col-sm-5 control-label">  WeekOff :</label>
							<div class="col-md-7 col-sm-7"><p id="viewWeekOff"></p></div>
						</div> -->
					</div>	
						
					<div class="col-md-12 paddingCls" id="divTable" style="border: 1px solid">
                        <div class="col-md-6 paddingCls" id="earndiv">
                            <table class="table-striped" id="earningsTab">
                                <tbody>
                                    <th class="col-md-10 col-xs-12 col-sm-12 text-left" id="earnView" style="height: 40px;border-bottom: 1px solid;background-color: lightgray;">Earnings</th>
                                    <th class="text-right" id="earnAmt" style="height: 40px;border-bottom: 1px solid;background-color: lightgray;">Amount</th>
                                </tbody>
                            </table>
                        </div>
                        <div id="hideDiv" style="height: 40px;display: none"></div>
                        <div class="col-md-6 paddingCls" id="deductdiv">												
                            <table class="table-striped" id="deductTab" style="width: 100%;">  
                                <tbody>
                                    <th class="col-md-10 col-xs-12 col-sm-12 text-left" id="deductView" style="height: 40px;border-bottom: 1px solid;background-color: #D3D3D3;">Deductions</th>
                                    <th class="text-right" id="deductAmt" style="height: 40px;border-bottom: 1px solid;background-color: lightgray;">Amount</th>
                                </tbody>
                            </table>
                        </div>
                    </div>
					<div class="payslip_netpay_inwords">
						<div class="netpay_body">
							<b class="payslip_netpay">Netpay In Words </b>
							<div id="netpayInWord"></div>
						</div>
					</div>
					<div id="contributionHideDiv" class="contribution_hidden"></div>
					<div class="col-md-12 paddingCls" id="emptyContributionDiv" style="display: none"></div>
					<div class="col-md-12 paddingCls" id="empContribution">
						<div class="org_contribution"><b>Organization Contribution</b></div>
						<div class="col-md-6 paddingCls" id="contributionDiv1">
							<table class="table-striped org_contribution_tab" id="contributionTab1">
								<tbody></tbody>
							</table>
						</div>
						<div class="col-md-6 paddingCls" id="contributionDiv2">
							<table class="table-striped hidden-sm hidden-xs org_contribution_table" id="contributionTab2">
								<tbody></tbody>
							</table>
						</div>
					</div>
					<div id="form16HideDiv" style="height: 40px;display: none"></div>
					<div class="col-md-12 paddingCls" id="emptyForm16Div" style="display: none"></div>
					<div class="col-md-12 paddingCls" id="empForm16Table">
						<div class="col-md-6 paddingCls" id="exemptionsdiv">
                            <table class="table-striped" id="exemptionsPanel">
                                <tbody>                                    
                                </tbody>
                            </table>
							<table class="table-striped" id="sec10ExemptionsPanel">
									<tbody>										
									</tbody>
							</table>
                        </div>
						<div class="col-md-6 paddingCls" id="form16Summarydiv">
							<table class="table-striped" id="form16SummaryTab">
									<tbody>										
									</tbody>
							</table>
						</div>
					</div>

					<div class="col-md-12 col-xs-12 col-sm-12" style="margin: 0% 0% 2% 0%" id="notes"></div>
                    <!--<div style="text-align: right;height: 40px;font-size:10px" id="formReport"></div>	-->
                </form>
                <div style="text-align: right;font-size:10px" id="formReport"></div>	
				<!--style="font-weight:bold;height: 40px;"--> 
			</div>
        </div>        
    </div>
</div>
<!-- view payslip template modal -->
<div class="modal fade" id="modalViewSalaryPayslipTemplate" aria-hidden="true">
	<div class="modal-dialog modal-lg" style="height: 1800px;">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" >
					<i class="mdi-hardware-keyboard-backspace" id="exitPayslipPreview"></i>
				</button>
				<h4 class="modal-title"><strong>View</strong> Monthly Payslip</h4>
			</div>
			<div class="modal-body">
				<!--View PayslipTemplate Form-->
				<canvas class="hidden" id="the-canvas" style="width:100%;height:100%"></canvas>
			</div>
		</div>
	</div>
</div>
<div class="custom-loading-cls" id="custom-loading-payslip" style="display:none">
	<div class="a custom_spinner" style="--n: 5;">
		<div class="dot" style="--i: 0;"></div>
		<div class="dot" style="--i: 1;"></div>
		<div class="dot" style="--i: 2;"></div>
		<div class="dot" style="--i: 3;"></div>
		<div class="dot" style="--i: 4;"></div>
	</div>
</div>
<!-- Generate Form24Q-->
<div class="modal fade" id="modalFormQuarterClosure" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" id="viewPageCloseQuarterClosure" style="margin-right: 10px;" data-dismiss="modal">
					<i class="mdi-hardware-keyboard-backspace"></i>
				</button>
				
				<h4 class="modal-title">Quarter Closure</h4>
			</div>
			
			<div class="modal-body">
				<!--Add/Edit MonthlySalary Form-->
				<form role="form" class="form-horizontal form-validation" id="formGenerateFormQuarterClosure" method="POST" action="">
					<div class="row">                        
                        <div class="form-group">
							<label class="col-md-4 control-label">Assessment Year <span class="short_explanation">*</span></label>
								<div class="col-md-8">
									<select class="form-control vRequired" data-search="true" name="Assessment Year" id="assessmentYr">
								          <option value="<?php echo $assessmentYr; ?>"><?php echo $assessmentYr; ?></option>
									</select>
								</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Quarter <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control vRequired" data-search="true" id="formQuarterMnth" name="QuarterMonth" >
									<option value="">--Select--</option>
									<?php
									foreach ($assessMnth as $key => $row)
									{
										echo '<option value="'.$row.'">'.$row.'</option>';
									}
									?>
								</select>
							</div>
						</div>
						
					</div>
					
					<button type="reset" class="cancel" id="Form24QReset" style="display: none;" ></button>
				</form>
			</div>
			<div class="modal-footer text-center" id="formActionFormQuarterClosure">				
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formCancelQuarterClosure" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitQuarterClosure" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
                <button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitRevertClosure" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
			</div>
			
        </div>        
    </div>
</div>

<div class="modal fade" id="modalFormTdsOverride" aria-hidden="true">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" id="viewPageCloseTdsOverride" style="margin-right: 10px;" data-dismiss="modal">
					<i class="mdi-hardware-keyboard-backspace"></i>
				</button>
				
				<h4 class="modal-title">TDS Override</h4>
			</div>
			
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="formTdsOverride" method="POST" action="">
					<div class="row">                        
                        <div class="form-group">
							<label class="col-md-4 control-label">Actual Tax<span class="short_explanation">*</span></label>
								<div class="col-md-8">
									<p id="viewActualTax"></p>
								</div>
						</div>
						<div class="form-group">
							<label class="col-md-4 control-label">Override Tax<span class="short_explanation">*</span></label>
								<div class="col-md-8">
									<input type="number" class="form-control parseInteger" step="1" min="0" max="9999999999999" id="overrideTax" name="overrideTax" placeholder="Override Tax">
								</div>
						</div>
					</div>
				</form>
			</div>
			<div class="modal-footer text-center" id="formActionFormTdsOverride">				
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitTdsOverride" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
         	</div>
	    </div>        
    </div>
</div>

<div class="modal fade" id="dirtyMonthlySalary" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditConfMonthlyPayslip"></i></button>
                <h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
            </div>
            
            <div class="modal-body">Are you sure want to close this form?<br></div>
            
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfMonthlyPayslip">No</button>
              <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="closeMonthly">Yes</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="dirtyQuarterClosure" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeQuaterClosureConfMonthlyPayslip"></i></button>
                <h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
            </div>
            
            <div class="modal-body">Are you sure want to close this form?<br></div>
            
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noQuaterClosureConfMonthlyPayslip">No</button>
              <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="closeQuarterClosure">Yes</button>
            </div>
        </div>
    </div>
</div>


<!-- hourly wages payslip Grid Panel -->
<div class="col-md-12 portlets add-panel-padding">
	<div class="panel" id="gridPanelHourlyWagesPayslip">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong>Hourly Wages Payslip</strong></h3>
		</div>
		<div class="panel-content">			
			<div class="m-b-10">
					
					<!--Select all the records-->
					<?php if ($salaryPayslip['Op_Choice'] == 1 || $salaryPayslip['Delete'] == 1) { ?>
					<button type="button" class="btn btn-white btn-embossed toolbar-icons" title="Check All" id="hourlyCheckAll">
						<i class="hr-check-all"></i><span class="hidden-xs hidden-sm">Check All</span>
					</button>
					<?php } ?>
					
                    <!-- View Button in Grid Toolbar -->
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="viewHourlyWagesPayslip" title="View">
						<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
					</button>
                    
					<!-- Update Button in Grid Toolbar -->
					<?php if ($salaryPayslip['Op_Choice']) { ?>
					<button type="button" class="btn btn-white btn-embossed visible-lg-* toolbar-icons" id="initiateHourlyWagesPayslip" title="GenerateHourly">
						<i class="fa fa-cog"></i><span class="hidden-xs hidden-sm"> Initiate Payslip</span>
					</button>
					
					<!-- Export Print -->
					<!-- <button type="button" class="btn btn-white btn-embossed toolbar-icons disabled btn-off" id="exportHourlyPrint" title="Print" >
					<i class="fa fa-print"></i><span class="hidden-xs hidden-sm"> Print</span>
					</button> -->
					
					<!-- Export PDF -->
					<button type="button" class="btn btn-white btn-embossed toolbar-icons disabled btn-off" id="exportHourlyPdf" title="Export as Pdf" >
					<i class="fa fa-file-pdf-o"></i><span class="hidden-xs hidden-sm"> PDF</span>
					</button>
					
                    <!-- Delete Button in Grid Toolbar -->
					<?php } if ($salaryPayslip['Delete']) { ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="deleteHourlyWagesPayslip" title="Delete">
						<i class="mdi-action-delete"></i><span class="hidden-xs hidden-sm"> Delete</span>
					</button>
					
					<?php }  ?>
                    
                    <!-- Filter Button in Grid Toolbar -->
                    <a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterHourlyWagesPayslip">
                        <i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm">Filter</span>
                    </a>

					<?php
					if ($salaryPayslip['Op_Choice']) { ?>
						<div class="btn-group" style="margin-top: 0px;" id="buttonPaymentStatusHourly">
							<button type="button" class="btn btn-whitep dropdown-toggle" data-toggle="dropdown" aria-expanded="true">
								<i class="mdi-action-info-outline"></i>
								<span class="hidden-xs hidden-sm">Update Payment Status</span>
								<span class="caret"></span>
								<div class="ripple-wrapper"></div>
							</button>
							<span class="dropdown-arrow dropdown-arrow-inverse"></span>
							<ul class="dropdown-menu dropdown-inverse" role="menu" style="width:100%">
								<li>
									<a id="approveAndReleaseHourly" style="cursor: pointer;">Approve & Release</a>
								</li>
								<li>
									<a id="salaryHoldHourly" style="cursor: pointer;">Withhold Salary</a>
								</li>
								<li>
									<a id="revertSalaryWithholdHourly" style="cursor: pointer;">Revert Salary Withhold</a>
								</li>
							<?php if (empty($accountNumberList)) { ?>
								<li>
									<a id="paidHourly" style="cursor: pointer;">Paid</a>
								</li>
								<li>
									<a id="revertPaidHourly" style="cursor: pointer;">Revert Payment</a>
								</li>
							<?php } ?>
								
							</ul>
						</div>
					<?php } ?>

					<button type="button" class="btn btn-white btn-embossed toolbar-icons disabled btn-off" id="formHourlycommunicateEmployee" title="Notify in Email" >
					<i class="mdi-content-send"></i><span class="hidden-xs hidden-sm">Notify in Email</span>
					</button>
			</div>
			
            <!-- Expense Type Grid -->			
			<table class="table dataTable table-striped table-dynamic table-hover" id="tableHourlyWagesPayslip">
				<thead>
					<tr>
						<th></th>
						<th id="hourlyPayslipEmpId">Employee Id</th>
						<th id="hourlyPayslipEmpName">Employee Name</th>
						<th id="hourlyPayslipSalaryMonth">Salary Month</th>
						<th id="hourlyPayslipTotalSalary">Total Salary</th>	
						<th id="hourlyPayslipPaymentStatus">Payment Status</th>	
					</tr>
				</thead>
				<tbody>
					
				</tbody>
			</table>
		</div>
	</div>
</div>

<!-- Your custom menu with dropdown-menu as default styling -->
<!--<div id="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextHourlyWagesPayslip"><i class="mdi-action-visibility"></i> View</a></li>
		<!--<php if ($salaryPayslip['Update']) { -->
		<!--<li><a tabindex="-1" id="editContextHourlyWagesPayslip"><i class="mdi-editor-mode-edit"></i> Edit</a></li>-->
		
		<!--<li><a tabindex="-1" id="deleteContextHourlyWagesPayslip"><i class="mdi-action-delete"></i> Delete</a></li>		-->
		<!--<php } >		-->
	<!--</ul>-->
<!--</div>-->

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="hourly-payslip-context-menu" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextHourlyWagesPayslip"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($salaryPayslip['Op_Choice']) { ?>
		<li><a tabindex="-1" id="pdfContextHourlyWagesPayslip"><i class="fa fa-file-pdf-o"></i> PDF</a></li>
		<?php } if ($salaryPayslip['Delete']) { ?>
		<li><a tabindex="-1" id="deleteContextHourlyWagesPayslip"><i class="mdi-action-delete"></i> Delete</a></li>		
		<?php } ?>		
	</ul>
</div>


<!--Filter Form-->
<div class="builder" id="filterPanelHourlyWagesPayslip">
	<div id="closeFilterHourlyWagesPayslip"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel filterReset" style="width: 100%;" id="cancelHourlyWagesPayslip">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="applyHourlyWagesPayslip">Apply</button>
            
			<!--Filter for Employee Name-->
			<?php if ($salaryPayslip['View'] == 1 && $salaryPayslip['Is_Manager'] == 0 && empty($salaryPayslip['Admin'])) { ?>
			
			<div class="form-group">
				<label>Employee Name</label>
				<!--<input type="text" class="form-control" id="filterhourlyEmployeeName" readonly="readonly" value="<php echo $salaryPayslip['Employee_Name']['Emp_First_Name'] .' '. $salaryPayslip['Employee_Name']['Emp_Last_Name']; ?>" >-->
				<input type="text" class="form-control" id="filterhourlyEmployeeName" readonly="readonly" value="<?php echo $salaryPayslip['Employee_Name']; ?>" >
			</div>
			
			<?php } else { ?>
			
			<div class="form-group">
				<label>Employee Name</label>
				<input type="text" class="form-control" id="filterhourlyEmployeeName" placeholder="Employee Name" >
			</div>
			
			<?php } ?>
			
			<!-- Filter for Salary Month -->
			<div class="form-group">
				<label>Salary Month</label>
				<input type="text" class="b-datepicker form-control closeMonthPicker" data-date-format="MM,yyyy" data-view="1" data-date-min-view-mode=1 data-date-autoclose="true" id="filterhourlySalaryMonth" data-orientation="top">
				<!--<input type="month" class="form-control" id="filterhourlySalaryMonth">-->
			</div>			
			
			<!--Filter For Total Amount -->
			<div class="form-group">
				<label>Total Salary</label>
				<div class="input-group">
					<input type="number" class="form-control" name="totalSalaryStart" id="filterhourlyTotalSalaryStart" min="0" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="form-control" name="totalSalaryEnd" id="filterhourlyTotalSalaryEnd" min="0" placeholder="End"/>
				</div>
			</div>

			<div class="form-group">
				<label>Payment Status</label>
				<select class="form-control" data-search="true" id="filterHouPaymentStatus" >
					<option value="">All</option>
					<?php
					foreach ( $paymentStatusPair as $paymentStatus)
					{
						echo '<option value="'. $paymentStatus .'">'. $paymentStatus .'</option>';
					}
					?>
				</select>
			</div>

			<?php if ($fieldForce== 1) { ?>
				<div class="form-group">
					<label>Service Provider</label>
					<select class="form-control" data-search="true" id="filterHourlyServiceProvider">
						<?php
						foreach ($serviceProvider as $key => $row)
						{
							if($key == $serviceProviderId)
							{
								echo '<option value="'.$key.'"  selected="selected">'.$row.'</option>';
							}
							else 
							{
								echo '<option value="'. $key .'">'. $row .'</option>';
							}
						}
						?>
					</select>												
				</div>
			<?php } ?>
		</div>
	</div>
</div>


<!--View,Add,Edit Hourly Wages Payslip Form-->
<div class="modal fade" id="modalFormHourlyWages" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" id="backHoulryPayslip" style="margin-right: 10px;" data-dismiss="modal">
					<i class="mdi-hardware-keyboard-backspace"></i>
				</button>
				
				<h4 class="modal-title"></h4>
			</div>
			
			<div class="modal-body">
				<div id="hourlyPayslipGridMsgPanel"></div>	
                <?php if ($salaryPayslip['Op_Choice'] == 1) { ?>
				
				<!--Add/Edit HourlyWages Form-->
				<form role="form" class="form-horizontal form-validation" id="formGenerateHourlyPayslip" method="POST" action="">
					<input type="hidden" name="loginEmpHWPayslipDeleteRights" id="loginEmpHWPayslipDeleteRights" value="<?php echo ($salaryPayslip['Op_Choice'] == 1 || $salaryPayslip['Delete'] == 1); ?>" />
					<div class="row">                        
                        <div class="form-group">
							<label class="col-md-4 control-label">Payslip Type <span class="short_explanation">*</span></label>
								<div class="col-md-8">
									<select class="notranslate form-control vRequired" disabled data-search="true" name="Payslip Type" id="hourlyPayslipType">
										<option value="HourlyWages">Hourly Wages</option>
									</select>
								</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">For the Month<span class="short_explanation">*</span></label>
							<div class="col-md-8">																
                     			<input type="text" class="b-datepicker form-control vRequired closeMonthPicker"
                                       data-date-format="MM,yyyy" data-view="1" data-date-min-view-mode=1 data-date-autoclose="true" id="salaryMonthHourly" data-orientation="top" autocomplete="off">
							</div>							
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Location <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select multiple="multiple" class="form-control vRequired selectAlll" data-search="true" id="formHourlyLocation" name="hourlyLocation" >
									<option value="selectAll">--Select all--</option>
									<option value="clearAll">--Clear all--</option>
									<?php
									foreach ($locationPair as $key => $row)
									{
										echo '<option value="'.$key.'">'.$row.'</option>';
									}
									?>
								</select>
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Department <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select multiple="multiple" class="form-control vRequired selectAlll" data-search="true" id="formHourlyDepartment" name="hourlyDepartment" >
									<?php
                                    foreach ($deptHierarchy as $key => $row)
                                    {
                                        echo '<option value="'. $row['Department_Id'] .'">'. $row['Department_Name'] .'</option>';
                                        
                                        foreach($row['Child'] as $val =>$name)
                                        {
                                            
                                            if($name['Parent_Type_Id'] == $row['Department_Id'])
                                            {
                                                echo '<option value="'. $name['Department_Id'] .'">&nbsp;&nbsp;'. $name['Department_Name'] .'</option>';
                                                
                                                foreach($row['Child'] as $v =>$k)
                                                {
                                                    if($k['Parent_Type_Id'] == $name['Department_Id'])
                                                        echo '<option value="'. $k['Department_Id'] .'">&nbsp;&nbsp;&nbsp;&nbsp;'. $k['Department_Name'] .'</option>';
                                                }
                                                
                                            }
                                        }
                                    }
                                    ?>
								</select>
							</div>
						</div>

						<?php if ($fieldForce== 1) { ?>
						<div class="form-group">
							<label class="col-md-4 control-label">Service Provider</label>
							<div class="col-md-8">
								<select class="form-control" data-search="true" id="formHourlyServiceProvider">
									<?php
									foreach ($serviceProvider as $key => $row)
									{
										if($key == $serviceProviderId)
										{
											echo '<option value="'.$key.'"  selected="selected">'.$row.'</option>';
										}
										else 
										{
											echo '<option value="'.$key.'">'.$row.'</option>';	
										}
									}
									?>
								</select>
							</div>
						</div>
						<?php } ?>

					</div>
					
					<button type="reset" class="cancel" id="formHourlyPayslipReset" style="display: none;" ></button>
				</form>
				
				<?php } ?>
			</div>
			<div class="modal-footer text-center" id="formActionHourlyPayslip">				
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formCancelHourlyPayslip" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formVerifyHourlyPayslip" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Verify Payslip
				</button>
			</div>
			
			<div class="modal-body" style="padding: 0px;">
			
			<form role="form" class="form-horizontal form-validation" id="formGenerateHourlyPayslipSubGrid" method="POST" action="">
				<label id="monthHourlyTitle" style="margin: 0% 2% 0% 2%;"> Verify Hourly Wages Payslip for the month of Mar,2016</label>
			<div id="payslipSubGrid" style="margin: 0% 2% 0% 2%;">
				<table class="table dataTable table-striped table-dynamic table-hover" id="tableHourlyPayslipSubGrid">
					<thead>
						<tr>
							<th></th>
							<th id="hourlyPayslipSubEmployeeName">Employee Name</th>
							<th id="hourlyPayslipTotalSalary">Total Salary</th>
						</tr>
					</thead>
					<tbody>
					
					</tbody>
				</table>
			</div>
			</form>
			
			</div>
			
			<div class="modal-footer text-center" id="formActionHourlyPayslipSubGrid">				
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formsubmitHourlyPayslip" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Generate Payslip
				</button>
			</div>
			
			
        </div>        
    </div>
</div>



<!--View,Add,Edit Hourly Wages Payslip Form-->
<div class="modal fade" id="modalFormHourlyWagesPayslip" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" style="margin-right: 10px;" data-dismiss="modal" id="viewHrlyPageClose">
					<i class="mdi-hardware-keyboard-backspace"></i>
				</button>
				
				
				
				<h4 class="modal-title"></h4>
			</div>
			
			<div class="modal-body">
				<form role="form" id="viewFormHourlyPayslip"  style="overflow: hidden;">
					<div>
                        <div style="text-align:center;font-weight:bold;margin-bottom:30px;font-size: large;" id="payHourlyMonth"></div>
                        <!--<div style="text-align: right;height: 40px;font-size:10px" id="formHourlyReport"></div>-->
                        <!--<table style="width:100%">
                            <tr>
                                <td>
                                    <div style="height: 100%" class = "" id="imagesHourly"></div>
                                </td>
                                <td>
                                    <div style="height: 100%">
                                        <div style="text-align:right;font-weight:bold"  id="formHourlyHeader"></div>
                                        <div style="text-align:right"  id="formHourlyAddress"></div>							
                                        <div style="text-align:right" id="formHourlyAddress1"></div>
                                    </div>
                                </td>
                            </tr>
                        </table>-->
                        
                        <div class="col-md-12 col-sm-0 form-group" style="padding: 0px;"> 
							<div class = "col-md-5 col-sm-6" id="imagesHourly"></div>
                            <div class="col-md-7 col-sm-6" style="height: 100%;padding-right:0%">
                                <div style="text-align:right;font-weight:bold"  id="formHourlyHeader"></div>
                                <div style="text-align:right"  id="formHourlyAddress"></div>							
                                <div style="text-align:right" id="formHourlyAddress1"></div>
                            </div>
						</div>
                    </div>
					
					<div class="row" style="margin-top:30px">
					<div class="col-md-6 col-sm-6 form-group"> 
							<label class="col-md-5 col-sm-5 control-label"> Employee Id :</label>
							<div class="col-md-7 col-sm-7"><p id="viewHourlyEmployeeId"></p></div>
						</div>
						<div class="col-md-6 col-sm-6 form-group"> 
							<label class="col-md-5 col-sm-5 control-label"> Employee Name :</label>
							<div class="col-md-7 col-sm-7"><p id="viewHourlyEmployeeName"></p></div>
						</div>
						<div class="col-md-6 col-sm-6 form-group"> 
							<label class="col-md-5 col-sm-5 control-label"> Department :</label>
							<div class="col-md-7 col-sm-7"><p id="viewHourlyDepartment"></p></div>
						</div>

						<div class="col-md-6 col-sm-6 form-group"> 
							<label class="col-md-5 col-sm-5 control-label"> Designation :</label>
							<div class="col-md-7 col-sm-7"><p id="viewHourlyDesignation"></p></div>
						</div>
						
						<div class="col-md-6 col-sm-6 form-group"> 
							<label class="col-md-5 col-sm-5 control-label"> Date Of Join :</label>
							<div class="col-md-7 col-sm-7"><p id="viewHourlyDateOfJoin"></p></div>
						</div>
						
						<?php if($pfNumber['Enable'] == 1) { ?>
						<div class="col-md-6 col-sm-6 form-group"> 
							<label class="col-md-5 col-sm-5 control-label"><?php echo $pfNumber['Field_Name'];?> :</label>
							<div class="col-md-7 col-sm-7"><p id="viewHourlyPFAccountNumber"></p></div>
						</div>
						<?php } ?>
						
						<div class="col-md-6 col-sm-6 form-group">
							<label class="col-md-5 col-sm-5 control-label">PF UAN No. :</label>
							<div class="col-md-7 col-sm-7"><p id="viewHourlyUAN"></p></div>
						</div>
						<div class="col-md-6 col-sm-6 form-group">
							<label class="col-md-5 col-sm-5 control-label"> ESI/Insurance No. :</label>
							<div class="col-md-7 col-sm-7"><p id="viewHourlyESI"></p></div>
						</div>
						<div class="col-md-6 col-sm-6 form-group">
							<label class="col-md-5 col-sm-5 control-label"> PAN Number :</label>
							<div class="col-md-7 col-sm-7"><p id="viewHourlyPanNo"></p></div>
						</div>
						<div class="col-md-6 col-sm-6 form-group">
							<label class="col-md-5 col-sm-5 control-label"> Bank Name :</label>
							<div class="col-md-7 col-sm-7"><p id="viewHourlyBankName"></p></div>
						</div>
						
						<div class="col-md-6 col-sm-6 form-group">
							<label class="col-md-5 col-sm-5 control-label"> Bank Account No. :</label>
							<div class="col-md-7 col-sm-7"><p id="viewHourlyBankAccountNo"></p></div>
						</div>
						
						<div class="col-md-6 col-sm-6 form-group"> 
							<label class="col-md-5 col-sm-5 control-label"> Paid Leave :</label>
							<div class="col-md-7 col-sm-7"><p id="viewHourlyPaidLeave"></p></div>
						</div>
						<div class="col-md-6 col-sm-6 form-group"> 
							<label class="col-md-5 col-sm-5 control-label">  Unpaid Leave :</label>
							<div class="col-md-7 col-sm-7"><p id="viewHourlyUnpaidLeave"></p></div>
						</div>
						
						<div class="col-md-6 col-sm-6 form-group">
							<label class="col-md-5 col-sm-5 control-label"> Days Worked :</label>
							<div class="col-md-7 col-sm-7"><p id="viewHourlyDaysWorked"></p></div>
						</div>

						<div class="col-md-6 col-sm-6 form-group">
							<label class="col-md-5 col-sm-5 control-label"> Hours Worked :</label>
							<div class="col-md-7 col-sm-7"><p id="viewHourlyRegularHours"></p></div>
						</div>

						<div class="col-md-6 col-sm-6 form-group">
							<label class="col-md-5 col-sm-5 control-label"> Day Wages :</label>
							<div class="col-md-7 col-sm-7"><p id="viewHourlyDayWages"></p></div>
						</div>
						<div class="col-md-6 col-sm-6 form-group">
							<label class="col-md-5 col-sm-5 control-label"> OT Hours :</label>
							<div class="col-md-7 col-sm-7"><p id="viewHourlyOtHours"></p></div>
						</div>
						<div class="col-md-6 col-sm-6 form-group">
                            <label class="col-md-5 col-sm-5 control-label"> Holidays :</label>
                            <div class="col-md-7 col-sm-7"><p id="viewHourlyHolidays"></p></div>
                        </div>
					
						
					</div>	
					
                    <div class="col-md-12 paddingCls" id="divTableHrly" style="border: 1px solid">
                        <div class="col-md-6 paddingCls" id="earnDivHrly">
                            <table class="table-striped" id="earningsHourlyTab">
                                <tbody>
                                    <th class="col-md-10 col-xs-10 col-sm-10 text-left" id="earnHourlyView" style="height: 40px;border-bottom: 1px solid;background-color: lightgray;">Earnings</th>
                                    <th class="text-right col-md-2" id="earnHourlyAmt" style="height: 40px;border-bottom: 1px solid; padding-right: 0px;background-color: lightgray;">Amount </th>
                                </tbody>
                            </table>
                        </div>
                        <div id="hideDivHrly" style="height: 40px;display: none"></div>
                        <div class="col-md-6 paddingCls" id="deductDivHrly">
                            <table class="table-striped" id="deductHourlyTab" >  
                                <tbody>
                                    <th class="col-md-10 col-xs-10 col-sm-10 text-left" id="deductHourlyView" style="height: 40px;border-bottom: 1px solid;background-color: lightgray;">Deductions</th>
                                    <th class="text-right col-md-2" id="deductHourlyAmt" style="height: 40px;border-bottom: 1px solid; padding-right: 0px;background-color: lightgray;">Amount </th>
                                </tbody>
                            </table>
                        </div>
					</div>
					<div class="payslip_netpay_inwords">
						<div class="netpay_body">
							<b class="payslip_netpay">Netpay In Words </b>
							<div id="netpayInWordHourly"></div>
						</div>
					</div>
					<div id="contributionHideDivHourly" class="contribution_hidden"></div>
					<div class="col-md-12 paddingCls" id="emptyContributionDivHourly" style="display: none"></div>
					<div class="col-md-12 paddingCls" id="empContributionHourly">
						<div class="org_contribution"><b>Organization Contribution</b></div>
						<div class="col-md-6 paddingCls" id="contributionDiv1Hourly">
							<table class="table-striped org_contribution_tab" id="contributionTab1Hourly">
								<tbody></tbody>
							</table>
						</div>
						<div class="col-md-6 paddingCls" id="contributionDiv2Hourly">
							<table class="table-striped hidden-sm hidden-xs org_contribution_table" id="contributionTab2Hourly">
								<tbody></tbody>
							</table>
						</div>
					</div>
					<div class="col-md-12 col-xs-12 col-sm-12" style="margin: 0% 0% 2% 0%" id="notesHourly"></div>                    
                </form>
				<div style="text-align: right;height: 40px;font-size:10px" id="formHourlyReport"></div>
			</div>
            
            
            <div class="modal-footer text-center" id="formActionHourlyWagesPayslip">				
                <?php if ($salaryPayslip['Op_Choice'] == 1 || $salaryPayslip['Op_Choice'] == 1) { ?>
                <button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetHourlyWagesPayslip" style="bottom: 5px;">
                    <i class="mdi-action-restore"></i> Reset
                </button>
                <button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitHourlyWagesPayslip" style="bottom: 5px;">
                    <i class="mdi-content-send"></i> Submit
                </button>
                <?php } ?>
            </div>
        </div>        
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="modalDeleteHourlyWagesPayslip" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteConfHourlyPayslip"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfHourlyPayslip">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConfirmHourlyWagesPayslip">Yes</button>
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="modalFormExportCsv" aria-hidden="true">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" id="viewPageCloseExportCsv" style="margin-right: 10px;" data-dismiss="modal">
					<i class="mdi-hardware-keyboard-backspace"></i>
				</button>
				
				<h4 class="modal-title">Export Payslip Summary</h4>
			</div>
			
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="formExportCsv" method="POST" action="">
					<div class="row">                       
						<div class="form-group">
							<label class="col-md-4 control-label">Report Title<span class="short_explanation">*</span></label>
								<div class="col-md-8">
									<select class="form-control" data-search="true" id="filterReportTitle">
										<option value="Consolidated Pay Report">Consolidated Pay Report</option>
										<option value="Consolidated Salary Statement" >Consolidated Salary Statement</option>
									</select>
								</div>
						</div>
						<div class="form-group">
							<label class="col-md-4 control-label">Payslip Month<span class="short_explanation">*</span></label>
								<div class="col-md-8">
									<select class="form-control" data-search="true" id="filterPayslipMonth">
										<?php
										foreach ($payslipMonthPair as $key => $row)
										{
											echo '<option value="'. $key .'">'. $row .'</option>';
										}
										?>
									</select>
								</div>
						</div>
                        <div class="form-group">
							<label class="col-md-4 control-label">Business Unit</label>
								<div class="col-md-8">
									<select multiple="multiple" class="form-control selectAlll" data-search="true" id="filterBusinessUnit" >
										<option value="selectAll">--Select all--</option>
										<option value="clearAll">--Clear all--</option>
										<?php
										foreach ($businessUnitPair as $key => $row)
										{
											echo '<option value="'. $key .'">'. $row .'</option>';
										}
										?>
									</select>
								</div>
						</div>
						<div class="form-group">
							<label class="col-md-4 control-label">Employee Type</label>
								<div class="col-md-8">
									<select multiple="multiple" class="form-control selectAlll" data-search="true" id="filterEmployeeType" >
										<option value="selectAll">--Select all--</option>
										<option value="clearAll">--Clear all--</option>
										<?php
										foreach ( $employeeTypePair as $key => $row )
										{
											echo '<option value="'. $key .'">'. $row .'</option>';
										}
										?>
									</select>
								</div>
						</div>
						<div class="form-group groupBy">
							<label class="col-md-4 control-label">Group By<span class="short_explanation">*</span></label>
								<div class="col-md-8">
									<select class="form-control" data-search="true" id="filterGroupBy">
										<option value="Business_Unit_Id">Business Unit</option>
										<option value="Location_Id" >Location</option>
									    <option value="Department_Id" >Department</option>
										<option value="Designation_Id">Designation</option>
										<option value="EmpType_Id">Employee Type</option>
									</select>
								</div>
						</div>
					</div>
				</form>
			</div>
			<div class="modal-footer text-center" id="formActionFormExportCsv">				
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitExportCsv" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
         	</div>
	    </div>        
    </div>
</div>


<?php if ($salaryPayslip['Admin'] === 'admin') { ?>
	<div class="modal fade" id="modalPaymentStatus" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
						<i class="icons-office-52" id="closeExportAllConfSalary"></i>
					</button>
					<h4 class="modal-title">
						<strong>Payment Status</strong> Confirmation</h4>
				</div>

				<div class="modal-body" id="paymentStatusMsg">
					<br>
				</div>

				<div class="modal-footer">
					<button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="paymentStatusNo">No</button>
					<button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="paymentStatusYes">Yes</button>
				</div>
			</div>
		</div>
	</div>

<?php } ?>


<?php } else { ?>

<div class="col-md-12 portlets add-panel-padding">
	<div class="txt_center">Sorry, Access Denied...</div>
</div>

<?php } ?>
