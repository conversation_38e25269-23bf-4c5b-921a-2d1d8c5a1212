<?php
	$viewTransactionDetails = $this->transactionInfo;
	$doimanDetails = $this->doimanDetails;

	
	$viewAddress = $viewTransactionDetails['Address'];
	$viewTransaction = $viewTransactionDetails['Transaction'];
	
	$subTotal =$viewTransaction['Amt_Active_Employees']+$viewTransaction['Amt_Inactive_Employees'];
	$discount =$viewTransaction['Discount_Amount'];
	$amount = $subTotal-$discount;
	$taxClassCalculation=($viewTransaction['Tax_Class_Percentage']*$amount)/100;
	
	$netAmount1 =$subTotal+$taxClassCalculation;
	$netAmount =$subTotal+$taxClassCalculation;
	$tscDetails1 =0;
	
	if(!empty($viewTransaction))
	{
?>

	<div class="row">
		<div class="col-lg-12 portlets">
			<div class="hidden" id="printPanel" style="padding: 20px 0px 10px 10px">				
					<div class="col-md-12">
						<button type="submit" class="btn btn-secondary btn-embossed ladda-button" aria-hidden="true" id="PrintScreen" >
							<i class=""></i> Print
						</button>
						<button type="submit" class="btn btn-secondary btn-embossed ladda-button" aria-hidden="true" id="exitPrint" >
							<i class=""></i> Back
						</button>
					</div>
					<div class="preview_header" name="printable">
						
					</div>
					<div class="printable_portion" name="printable"></div>
				<!--</div>-->
			</div>
		</div>
	</div>

	<!--<div class="row invoice-page">-->
	<div class="row invoice-page" >	
        <div class="col-md-12 p-t-0">
			<div class="row">
			  <div class="col-md-12">
				<div class="pull-left">
				  <h4 class="w-500 c-gray f-14"><strong>FROM</strong></h4>                    
                    <address>
                      <p class="width-300 m-t-10"><strong><?php echo $doimanDetails['Organization_Name'];?></strong></p>
                      <p class="width-300"><?php echo $doimanDetails['Street1'].' '.$doimanDetails['Street2'];?></p>
                      <p class="width-300"><?php echo $doimanDetails['City'].'-'.$doimanDetails['Pincode'];?></p>
                      <p class="width-300"> Mobile No:<?php echo $doimanDetails['Contact_No'];?></p>
					  <p class="width-300">Website:<?php echo $doimanDetails['Talk_Link'];?></p>
					</address>
                  </div>
                  <div class="pull-right">
                    <h4 class="w-500 c-gray f-14"><strong>TO</strong></h4>                    
                    <address>
                      <p class="width-300 m-t-10"><strong><?php echo $viewTransaction['Org_Name'];?></strong></p>
                      <p class="width-300"><?php echo $viewAddress['Street1'];?>,<?php echo $viewAddress['Street2'];?></p>
                      <p class="width-300"><?php echo $viewAddress['City'];?>-<?php echo $viewAddress['Pincode'];?></p>
                      <p class="width-300"> Mobile No:<?php echo $viewAddress['Phone'];?></p>
					  <p class="width-300">Email:<?php echo $viewTransaction['Email_Id'];?></p>
                    </address>
                  </div>
			    </div>
			</div>
		    <div class="row">
			    <div class="col-md-12">
			        <div class="row">
						<div class="col-md-12 m-t-20 m-b-20">
						  <p><strong style="padding-left: 15px;">Invoice Date:</strong> <span><?php echo $viewTransaction['Billing_Date'];?></span></p>
						  <p><strong style="padding-left: 15px;">Due Date:</strong> <span><?php echo $viewTransaction['Due_Date'];?></span></p>
						</div>
                    </div>
                  <table class="table">
                    <thead>
                      <tr>
                        <th style="width:200px" class="unseen text-center">Service Description</th>
                        <th class="text-center">Total No Of Employee</th>
                        <th style="width:200px" class="text-right">Amount Per Employee</th>
                        <th style="width:100px" class="text-right">Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr class="item-row">
                        <td class="delete-wpr">
                          <p class="qty text-center">Active</p>
                        </td>
                        <td>
                            <p class="text-center"><?php echo $viewTransaction['No_Active_Employees'];?></p>                          
                        </td>
                        <td>
                          <p class="text-right"><?php echo $viewTransaction['Rate_Active_Employees'];?></p>
                        </td>
                        <td class="text-right"><?php echo $viewTransaction['Amt_Active_Employees'];?></td>
                      </tr>
                      <tr class="item-row">
                        <td class="delete-wpr">
                          <p class="qty text-center">Inactive</p>
                        </td>
                        <td>
							<p class="text-center"><?php echo $viewTransaction['No_Inactive_Employees'];?></p>
                        </td>
                        <td class="text-right"><?php echo $viewTransaction['Rate_Inactive_Employees'];?></td>
                        <td class="text-right"><?php echo $viewTransaction['Amt_Inactive_Employees'];?></td>
                      </tr>                      
                      <tr>
                        <td colspan="2" rowspan="6"></td>
                        <td class="text-right"><b>Subtotal:</b></td>
                        <td class="text-right" id="subtotal"><?php echo number_format($subTotal,2,'.','');?></td>
                      </tr>
						<?php if($discount>0) { ?>
						<tr>
						  <td class="text-right no-border"><strong>Discount:</strong></td>
						  <td class="text-right"><?php echo $discount;?></td>
						</tr>
						<?php } ?>
                      <tr>
                        <td class="text-right no-border"><b><?php echo $viewTransaction['Tax_Class'];?>(<?php echo $viewTransaction['Tax_Class_Percentage'];?>%):</b></td>
                        <td class="text-right"><?php echo number_format($taxClassCalculation,2,'.','');?></td>
                      </tr>
					  
					  <?php 
						$tscDetails1=0;$tscDetails2=0;$tscDetails3=0;$tscDetails4=0;
						if(!empty($viewTransaction['Tax_SubClass1']))
						{
							$tscDetails1 = ($viewTransaction['Tax_SubClass1_Percentage']*$taxClassCalculation)/100;?>
							<tr>
								<td class="text-right no-border"><b><?php echo $viewTransaction['Tax_SubClass1'];?>(<?php echo $viewTransaction['Tax_SubClass1_Percentage'];?>%):</b></td>
								<td class="text-right"><?php echo number_format($tscDetails1,2,'.','');?></td>
							</tr><?php							
						}
						if(!empty($viewTransaction['Tax_SubClass2']))
						{
							$tscDetails2 = ($viewTransaction['Tax_SubClass2_Percentage']*$taxClassCalculation)/100;?>
							<tr>
								<td class="text-right no-border"><b><?php echo $viewTransaction['Tax_SubClass2'];?>(<?php echo $viewTransaction['Tax_SubClass2_Percentage'];?>%):</b></td>
								<td class="text-right"><?php echo number_format($tscDetails2,2,'.','');?></td>
							</tr><?php
							//number_format($discount,2,'.','')
						}
						if(!empty($viewTransaction['Tax_SubClass3']))
						{
							$tscDetails3 = ($viewTransaction['Tax_SubClass3_Percentage']*$taxClassCalculation)/100;?>
							<tr>
								<td class="text-right no-border"><b><?php echo $viewTransaction['Tax_SubClass3'];?>(<?php echo $viewTransaction['Tax_SubClass3_Percentage'];?>%):</b></td>
								<td class="text-right"><?php echo number_format($tscDetails3,2,'.','');?></td>
							</tr><?php
						}
                        
						if(!empty($viewTransaction['Tax_SubClass4']))
						{
							$tscDetails4 = ($viewTransaction['Tax_SubClass4_Percentage']*$taxClassCalculation)/100;?>
                            
							<tr>
								<td class="text-right no-border"><b><?php echo $viewTransaction['Tax_SubClass4'];?>(<?php echo $viewTransaction['Tax_SubClass4_Percentage'];?>%):</b></td>
								<td class="text-right"><?php echo number_format($tscDetails4,2,'.','');?></td>
							</tr><?php							
						}
						$tscDetails = $tscDetails1+$tscDetails2+$tscDetails3+$tscDetails4;
						$netAmount  = $netAmount1+$tscDetails;
					  ?>
					  
                      <tr>
                        <td class="text-right no-border"><b>NetAmount:</b></td>
                        <td class="text-right" id="total"><?php echo round($netAmount).'.00';?></td>
                      </tr>
					  
                    </tbody>
                  </table>
                  <div class="well bg-white">
                  Thank you for your business. Please make sure all cheques payable to <strong><?php echo $doimanDetails['Organization_Name'];?></strong> Account No. <?php echo $doimanDetails['Account_No'];?>
                  </div>
                </div>
              </div>
            </div>
          </div>		
<?php 
	}	
?>
