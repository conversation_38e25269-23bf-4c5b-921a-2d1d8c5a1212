<?php
	$customFormNameA    = $this->customFormNameA;
	$customFormNameB    = $this->customFormNameB;
	$customFormNameC    = $this->customFormNameC;
	
	$headerFormName    = $this->headerFormName;
	$this->headTitle(((!empty($headerFormName) && !empty($headerFormName['New_Form_Name'])) ? $headerFormName['New_Form_Name'] : 'Compliance Forms'));
	
	$form12baUser     = $this->form12User;
	$employeeName     = $this->employeeName;
	$departmentPair   = $this->department;
	$locationPair	  = $this->location;
	$employeeTypePair = $this->employeeType;
    $assessmentYr     = $this->assessmentYr;
    $financialClosure = $this->financialClosure;
	$assessmentYr24Q  = $this->assessmentYr24Q;
    
	$form16User         = $this->form16User;
	$financialStartYear = $this->financialStartYear;
	$checkAllAccess     =($form16User['Admin'] == 'admin' || $form16User['Payroll_Group'] == 1) ? 1 : 0;
	
	$form24QUser   = $this->form24QUser;
	$assessMnth = $this->assessMnth;
	
	$finalFormNameA = ((!empty($customFormNameA) && !empty($customFormNameA['New_Form_Name'])) ? $customFormNameA['New_Form_Name'] : $this->formNameA);
	$finalFormNameB = ((!empty($customFormNameB) && !empty($customFormNameB['New_Form_Name'])) ? $customFormNameB['New_Form_Name'] : $this->formNameB);
	$finalFormNameC = ((!empty($customFormNameC) && !empty($customFormNameC['New_Form_Name'])) ? $customFormNameC['New_Form_Name'] : $this->formNameC);
?>


<div class="col-md-12 portlets paddingCls tab-spacing-cls">
	<div class="col-md-12 paddingCls bg-f9f9f9 tab-wrapper">
		<div class="pointer-cls border-bottom-secondary tab-border-cls bg-f9f9f9 tab-body" id="complianceFormsTab">
			<div class="tab-active-text tab-text-font text-secondary custom-tab-content"  id="complianceFormsFormTab">Compliance Forms</div>
		</div>
		<?php if ((int)$form24QUser['View'] === 1) { ?>
			
			<div class="pointer-cls border-bottom-secondary bg-f9f9f9 tab-body" id="form24QTab">
				<div class="tab-text-font custom-tab-content" id="form24QFormTab">
					<a id="formTabLink" class="tab-a-tag-color" href=<?php echo $this->baseUrl('v3/tax-and-statutory-compliance/compliance-forms/form-24q'); ?>>
						Form 24Q
					</a>
				</div>
			</div>
		<?php } ?>
	</div>
</div>

<!--By default set print panel as hidden and show when user clicks on print-->
<div class="row">
	<div class="col-lg-12 portlets">
		<div class="hidden" id="printPanel" style="padding: 20px 0px 10px 10px">
			<div class="panel-content pagination2 table-responsive">
				<div class="col-md-12" style="margin-top: 20px">
					<button type="submit" class="btn btn-secondary btn-embossed ladda-button" aria-hidden="true" id="PrintScreen" >
						<i class=""></i> Print
					</button>
					<button type="submit" class="btn btn-secondary btn-embossed ladda-button" aria-hidden="true" id="exitPrint" >
						<i class=""></i> Back
					</button>
				</div>
				<div class="preview_header" name="printable"></div>
				<div class="printable_portion" name="printable"></div>
			</div>
		</div>
	</div>
</div>

<?php 	
	if ($form12baUser['View'] == 1 && ((!empty($customFormNameA) && array_key_exists("Enable",$customFormNameA) && $customFormNameA['Enable'] == 1) || empty($customFormNameA))) {
?>

<!--By default set print panel as hidden and show when user clicks on print-->
<!--<div class="row">
	<div class="col-lg-12 portlets">
		<div class="hidden" id="printPanel">
			<div class="panel-content pagination2 table-responsive">
				<div class="col-md-12 clearfix">
					<button type="submit" class="btn btn-secondary btn-embossed ladda-button" aria-hidden="true" id="PrintScreen" >
						<i class=""></i> Print
					</button>
					<button type="submit" class="btn btn-secondary btn-embossed ladda-button" aria-hidden="true" id="exitPrint" >
						<i class=""></i> Back
					</button>
				</div>
				<div class="preview_header" name="printable"></div>
				<div class="printable_portion" name="printable"></div>
			</div>
		</div>
	</div>
</div>
-->

<div class="col-md-12 portlets add-panel-padding">
	<div class="panel" id="gridPanelForm12BA">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong id="lblFormNameA"><?php echo $finalFormNameA;?></strong></h3>
			<input type="hidden" name="financialClosure" id="financialClosure" value="<?php echo $financialClosure; ?>" />
		</div>
		<div class="panel-content">			
			<div class="m-b-10">                    
                    <!--Select all the records-->					
					<?php if ($form12baUser['Payroll_Group'] == 1 || $form12baUser['Admin'] == 'admin') { ?>
					<button type="button" class="btn btn-white btn-embossed toolbar-icons" title="Check All" id="form12baCheckAll">
						<i class="hr-check-all"></i><span class="hidden-xs hidden-sm">Check All</span>
					</button>
					<?php } ?>
                    
					<!-- View Button in Grid Toolbar -->
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="viewForm12BA" title="View">
						<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
					</button>
					
					<?php if ($form12baUser['Payroll_Group'] == 1 || $form12baUser['Admin'] == 'admin') { ?>
					<button type="button" class="btn btn-white btn-embossed visible-lg-* toolbar-icons" id="generateForm12BA" title="GeneratePayslip">
						<i class="fa fa-cog"></i><span class="hidden-xs hidden-sm"> Generate Form12BA</span>
					</button>
                    <?php } ?>
					
					<!-- Export Print -->
					<button type="button" class="btn btn-white btn-embossed toolbar-icons disabled btn-off" id="exportPrint" title="Print" >
					<i class="fa fa-print"></i><span class="hidden-xs hidden-sm"> Print</span>
					</button>
					
					<!-- Export PDF -->
					<!-- <button type="button" class="btn btn-white btn-embossed toolbar-icons disabled btn-off" id="exportPdf" title="Export as Pdf" >
					<i class="fa fa-file-pdf-o"></i><span class="hidden-xs hidden-sm"> PDF</span>
					</button> -->
					            
                    <!-- Delete Button in Grid Toolbar -->
					<?php if ($form12baUser['Payroll_Group'] || $form12baUser['Admin'] == 'admin' ) { ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="deleteForm12BA" title="Delete">
						<i class="mdi-action-delete"></i><span class="hidden-xs hidden-sm"> Delete</span>
					</button>
					
					<?php } ?>
                    
                    <!-- Filter Button in Grid Toolbar -->
                    <a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterForm12BA">
                        <i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm">Filter</span>
                    </a>					
			</div>
            
            <!-- Form 12BA Grid -->			
			<table class="table dataTable table-striped table-dynamic table-hover" id="tableForm12BA">
				<thead>
					<tr>
						<th id="form12BAEmployeeName">Employee Name</th>
						<th id="form12BAFinancialYear">Financial Year</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>            
        </div>
    </div>
</div>

<!--Filter Form-->
<div class="builder" id="filterPanelForm12BA">
	<div id="closeFilterForm12BA"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel filterReset" style="width: 100%;" id="cancelForm12BA">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="applyForm12BA">Apply</button>
            
			<!--Filter for Employee Name-->
			<?php if ($form12baUser['View'] == 1 && $form12baUser['Is_Manager'] == 0 && empty($form12baUser['Admin']) && empty($form12baUser['Payroll_Group'])) { ?>
			
			<div class="form-group">
				<label>Employee Name</label>
				<!--<input type="text" class="form-control" id="filterhourlyEmployeeName" readonly="readonly" value="<php echo $salaryPayslip['Employee_Name']['Emp_First_Name'] .' '. $salaryPayslip['Employee_Name']['Emp_Last_Name']; ?>" >-->
				<input type="text" class="form-control" id="filterForm12baEmployeeName" readonly="readonly" value="<?php echo $form12baUser['Employee_Name']; ?>" >
			</div>
			
			<?php } else { ?>
			
			<div class="form-group">
				<label>Employee Name</label>
				<input type="text" class="form-control" id="filterForm12baEmployeeName" placeholder="Employee Name" >
			</div>
			
			<?php } ?>
			
			<div class="form-group">
				<label>Assessment Year</label>
				<div class="input-group">
					<input type="number" class="input-md form-control" min="1" name="filterForm12baAssessmentYrBegin" id="filterForm12baAssessmentYrBegin" data-orientation="top" placeholder="Start"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="input-md form-control" min="1" name="filterForm12baAssessmentYrEnd" id="filterForm12baAssessmentYrEnd" data-orientation="top" placeholder="End"/>
				</div>
			</div>
            
		</div>
	</div>
</div>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="form12ba-context-menu" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextForm12BA"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($form12baUser['View']) { ?>
		<li><a tabindex="-1" id="printContextForm12BA"><i class="fa fa-print"></i> Print</a></li>
		<!-- <li><a tabindex="-1" id="pdfContextForm12BA"><i class="fa fa-file-pdf-o"></i> PDF</a></li> -->
		<?php } if ($form12baUser['Payroll_Group'] || $form12baUser['Admin'] == 'admin') { ?>
		<li><a tabindex="-1" id="deleteContextForm12BA"><i class="mdi-action-delete"></i> Delete</a></li>		
		<?php } ?>		
	</ul>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="modalDeleteForm12BA" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteConf12BA"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfForm12BA">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConfirmForm12BA">Yes</button>
			</div>
		</div>
	</div>
</div>

<!-- Generate Monthly Salary Payslip Form-->
<div class="modal fade" id="modalForm12ba" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" id="viewPageClose" style="margin-right: 10px;" data-dismiss="modal">
					<i class="mdi-hardware-keyboard-backspace" id="backForm12BA"></i>
				</button>
				
				<h4 class="modal-title">Generate Form12BA</h4>
			</div>
			
			<div class="modal-body">
				<!--<div id="payslipGridMsgPanel"></div>-->
                <form role="form" id="viewForm12BA" style="overflow: hidden;">
					<div id="form12BAView">
                        
                    </div>
                </form>
                
                
				<?php if ($form12baUser['Payroll_Group'] == 1 || $form12baUser['Admin'] == 'admin') { ?>
				
				<!--Add/Edit MonthlySalary Form-->
				<form role="form" class="form-horizontal form-validation" id="formGenerateForm12BA" method="POST" action="">
					<div class="row">                        
                        <div class="form-group">
							<label class="col-md-4 control-label">Financial Year <span class="short_explanation">*</span></label>
								<div class="col-md-8">
									<select class="form-control vRequired" data-search="true" name="Financial Year" id="financialYr">
									<option value="<?php echo $financialStartYear; ?>"><?php echo $financialStartYear; ?></option>
									</select>
								</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Employee Type <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select multiple="multiple" class="form-control vRequired selectAlll" data-search="true" id="formEmployeeType" name="Employee Type" >
									<option value="selectAll">--Select all--</option>
									<option value="clearAll">--Clear all--</option>
									<?php
									foreach ($employeeTypePair as $key => $row)
									{
										echo '<option value="'.$key.'">'.$row.'</option>';
									}
									?>
								</select>
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Location <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select multiple="multiple" class="form-control vRequired selectAlll" data-search="true" id="formLocation" name="Location" >
									<option value="selectAll">--Select all--</option>
									<option value="clearAll">--Clear all--</option>
									<?php
									foreach ($locationPair as $key => $row)
									{
										echo '<option value="'.$key.'">'.$row.'</option>';
									}
									?>
								</select>
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Department <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select multiple="multiple" class="form-control vRequired selectAlll" data-search="true" id="formDepartment" name="Department" >
									<option value="selectAll">--Select all--</option>
									<option value="clearAll">--Clear all--</option>
									<?php
									foreach ($departmentPair as $key => $row)
									{
										echo '<option value="'.$key.'">'.$row.'</option>';
									}
									?>
								</select>
							</div>
						</div>
					</div>
					
					<button type="reset" class="cancel" id="form12baReset" style="display: none;" ></button>
				</form>
				
				<?php } ?>
			</div>
			<div class="modal-footer text-center" id="formActionForm12ba">				
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formCancelForm12ba" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitForm12ba" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
			</div>
			
        </div>        
    </div>
</div>

<div class="modal fade" id="modalDirtyForm12ba" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditConf12BA"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfForm12BA">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseConfirmForm12ba">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php
	} if ($form16User['View'] == 1 && ((!empty($customFormNameB) && array_key_exists("Enable",$customFormNameB) && $customFormNameB['Enable'] == 1) || empty($customFormNameB))) {
?>
<!--<div class="row">
	<div class="hidden col-md-12" id="viewTaxFormPanel" style="padding: 20px 0px 10px 10px">			
		<div class="col-md-12" style="margin-top: 20px">
			<button type="submit" class="btn btn-secondary btn-embossed ladda-button" aria-hidden="true" id="PrintForm16Screen" >
				<i class=""></i> Print
			</button>
			<button type="submit" class="btn btn-secondary btn-embossed ladda-button" aria-hidden="true" id="exitTaxFormView" >
				<i class=""></i> Back
			</button>
		</div>
		<div class="preview_header" name="printable"></div>
		<div class="printable_portion" name="printable"></div>
	</div>
</div>-->

<!-- Form 16 Grid Panel -->
<div class="col-md-12 portlets add-panel-padding">
	<div class="panel" id="gridPanelForm16">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong id="lblFormNameB"><?php echo $finalFormNameB;?></strong></h3>
		</div>
		<div class="panel-content">			
			<div class="m-b-10">
				<!-- Check all Button in Grid Toolbar -->
				<?php if ($form16User['Admin'] == 'admin' || $form16User['Payroll_Group'] == 1) { ?>
					<button type="button" class="btn btn-white btn-embossed toolbar-icons" title="Check All" id="form16CheckAll">
						<i class="hr-check-all"></i><span class="hidden-xs hidden-sm">Check All</span>
					</button>
				<?php } ?>
				
				<!-- View Button in Grid Toolbar -->
				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons btn-off disabled" title="View" id="viewForm16Button">
					<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
				</button>
				
				<!-- Generate Button in Grid Toolbar -->
				<?php if ($form16User['Admin'] == 'admin' || $form16User['Payroll_Group'] == 1) { ?>
					<button type="button" class="btn btn-white btn-embossed visible-lg-* toolbar-icons" id="generateForm16Button" title="Generate "<?php echo $finalFormNameB;?> >
						<i class="fa fa-cog"></i><span class="hidden-xs hidden-sm"> Generate <?php echo $finalFormNameB;?></span>
					</button>
				<?php } ?>
				
				<?php if ($form16User['Op_Choice'] == 1){ ?>
					<!-- Export Print -->
					<button type="button" class="btn btn-white btn-embossed toolbar-icons disabled btn-off" id="exportForm16Print" title="Print" >
					<i class="fa fa-print"></i><span class="hidden-xs hidden-sm"> Print</span>
					</button>
					
					<!-- Export PDF -->
					<!-- <button type="button" class="btn btn-white btn-embossed toolbar-icons disabled btn-off" id="exportForm16Pdf" title="Export as Pdf" >
					<i class="fa fa-file-pdf-o"></i><span class="hidden-xs hidden-sm"> PDF</span>
					</button> -->
				<?php } ?>
				
				<!-- Delete Button in Grid Toolbar -->
				<?php if ($form16User['Admin'] == 'admin' || $form16User['Payroll_Group'] == 1) { ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="Delete" id="deleteForm16">
						<i class="mdi-action-delete"></i><span class="hidden-xs hidden-sm"> Delete</span>
					</button>
				<?php } ?>
				
				<!-- Filter Button in Grid Toolbar -->
				<a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterForm16">
					<i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm">Filter</span>
				</a>					
			</div>
			
            <!-- Final Settlement Grid -->			
			<table class="table dataTable table-striped table-dynamic table-hover" id="tableForm16">
				<thead>
					<tr>						
						<th id="form16EmployeeName">Employee Name</th>
						<th id="form16AssessmentYear">Assessment Year</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
		</div>
	</div>
</div>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="form16-context-menu" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextForm16"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($form16User['Op_Choice'] == 1){ ?>
		<li><a tabindex="-1" id="printContextForm16"><i class="fa fa-print"></i> Print</a></li>
		<!-- <li><a tabindex="-1" id="pdfContextForm16"><i class="fa fa-file-pdf-o"></i> PDF</a></li> -->
		<?php } if ($form16User['Admin'] == 'admin' || $form16User['Payroll_Group'] == 1) { ?>
		<li><a tabindex="-1" id="deleteContextForm16"><i class="mdi-action-delete"></i> Delete</a></li>
		<?php } ?>
	</ul>
</div>

<div class="builder" id="filterPanelForm16">
	<div id="closeFilterForm16"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel filterReset" style="width: 100%;" id="cancelForm16">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="applyForm16">Apply</button>            
			
			<!--Filter for Employee Name-->
			<?php if ($form16User['Is_Manager'] == 0 && empty($form16User['Admin']) && empty($form16User['Payroll_Group'])) { ?>
			
			<div class="form-group">
				<label>Employee Name</label>				
				<input type="text" class="form-control" id="filterEmployeeName" readonly="readonly" value="<?php echo $form16User['Employee_Name']; ?>" >
			</div>
			
			<?php } else { ?>
			
			<div class="form-group">
				<label>Employee Name</label>				
				<input type="text" class="form-control" id="filterEmployeeName" placeholder="Employee Name" >
			</div>
			
			<?php } ?>
			
			<!--Filter For Assessment Year -->
			<div class="form-group">
				<label>Assessment Year</label>
				<div class="input-group">
					<input type="number" class="form-control" name="assessmentYearStart" id="filterAssessmentYearStart" min="0" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="form-control" name="assessmentYearEnd" id="filterAssessmentYearEnd" min="0" placeholder="End"/>
				</div>
			</div>
			
		</div>
	</div>
</div>

<div class="modal fade" id="modalForm16" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" id="closeViewForm16" style="margin-right: 10px;" data-dismiss="modal">
					<i class="mdi-hardware-keyboard-backspace" id="backForm16"></i>
				</button>
				
				<h4 class="modal-title"></h4>
			</div>
			
			<div class="modal-body">
				<form role="form" id="viewForm16" style="overflow: hidden;">					
                </form>
				
                <?php if ($form16User['Admin'] == 'admin' || $form16User['Payroll_Group'] == 1) { ?>
				
				<!-- Form 16 Generation Form-->
				<form role="form" class="form-horizontal form-validation" id="formGenerateForm16" method="POST" action="">
					<input type="hidden" name="formId" id="formId" value="" />
					<input type="hidden" name="Is_AdminMgr" id="Is_AdminMgr" value="<?php echo $checkAllAccess; ?>" />
					
					<div class="row">                                               
						<div class="form-group">
							<label class="col-md-4 control-label">Assessment Year<span class="short_explanation">*</span></label>
							<div class="col-md-8">																
								<select class="form-control vRequired" data-search="true" name="formAssessmentYear" id="formAssessmentYear">
									<?php
										echo '<option value="'. $financialStartYear .'">'. $financialStartYear .'</option>';
									?>										
								</select>
							</div>							
						</div>
						
						<div class="form-group" id="form16EmployeeType">
							<label class="col-md-4 control-label">Employee Type <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select multiple="multiple" class="form-control vRequired selectAlll" data-search="true" id="form16EmployeeType" name="Employee Type" >
									<option value="selectAll">--Select all--</option>
									<option value="clearAll">--Clear all--</option>
									<?php
									foreach ($employeeTypePair as $key => $row)
									{
										echo '<option value="'.$key.'">'.$row.'</option>';
									}
									?>
								</select>
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Location <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select multiple="multiple" class="form-control vRequired selectAlll" data-search="true" id="form16Location" name="Location" >
									<option value="selectAll">--Select all--</option>
									<option value="clearAll">--Clear all--</option>
									<?php
									foreach ($locationPair as $key => $row)
									{
										echo '<option value="'.$key.'">'.$row.'</option>';
									}
									?>
								</select>
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Department <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select multiple="multiple" class="form-control vRequired selectAlll" data-search="true" id="form16Department" name="Department" >
									<option value="selectAll">--Select all--</option>
									<option value="clearAll">--Clear all--</option>
									<?php
									foreach ($departmentPair as $key => $row)
									{
										echo '<option value="'.$key.'">'.$row.'</option>';
									}
									?>
								</select>
							</div>
						</div>
					</div>
				</form>
				
				<?php } ?>
			</div>
			<div class="modal-footer text-center" id="formActionForm16">				
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="form16Reset" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="form16Generate" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Generate <?php echo $finalFormNameB;?>
				</button>
			</div>
        </div>        
    </div>
</div>

<?php if ($form16User['Admin'] == 'admin' || $form16User['Payroll_Group'] == 1) { ?>
<!-- Delete COnfirmation Modal -->
<div class="modal fade" id="modalDeleteForm16" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteConfForm16"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfForm16">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConfirmForm16">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php }  ?>

<div class="modal fade" id="dirtyForm16" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditConfForm16"></i></button>
                <h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
            </div>
            
            <div class="modal-body">Are you sure want to close this form?<br></div>
            
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfForm16">No</button>
              <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="closeForm16">Yes</button>
            </div>
        </div>
    </div>
</div>

<?php } if ((($form24QUser['Payroll_Group'] == 1 || $form24QUser['Admin'] == 'admin')) && ((!empty($customFormNameC) && array_key_exists("Enable",$customFormNameC) && $customFormNameC['Enable'] == 1) || empty($customFormNameC))) { ?>

<!--<div class="row">
	<div class="col-lg-12 portlets">
		<div class="hidden" id="printForm24QPanel">
			<div class="panel-content pagination2 table-responsive">
				<div class="col-md-12 clearfix">
					<button type="submit" class="btn btn-secondary btn-embossed ladda-button" aria-hidden="true" id="PrintForm24QScreen" >
						<i class=""></i> Print
					</button>
					<button type="submit" class="btn btn-secondary btn-embossed ladda-button" aria-hidden="true" id="exitForm24QPrint" >
						<i class=""></i> Back
					</button>
				</div>
				<div class="preview_header" name="printable"></div>
				<div class="printable_portion" name="printable"></div>
			</div>
		</div>
	</div>
</div>
-->
<div class="col-md-12 portlets add-panel-padding">
	<div class="panel" id="gridPanelForm24Q">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong id="lblFormNameC"><?php echo $finalFormNameC;?></strong></h3>
		</div>
		<div class="panel-content">			
			<div class="m-b-10">
                    
					<!-- View Button in Grid Toolbar -->
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="viewForm24QButton" title="View">
						<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
					</button>
					
					<?php if ($form24QUser['Payroll_Group'] == 1 || $form24QUser['Admin'] == 'admin') { ?>
					<button type="button" class="btn btn-white btn-embossed visible-lg-* toolbar-icons" id="generateForm24Q" title="GeneratePayslip">
						<i class="fa fa-cog"></i><span class="hidden-xs hidden-sm"> Generate Form24Q</span>
					</button>
                    <?php } ?>
					
					<!-- Export Print -->
					<button type="button" class="btn btn-white btn-embossed toolbar-icons disabled btn-off" id="exportForm24QPrint" title="Print" >
					<i class="fa fa-print"></i><span class="hidden-xs hidden-sm"> Print</span>
					</button>
					
					<!-- Export PDF -->
					<!-- <button type="button" class="btn btn-white btn-embossed toolbar-icons disabled btn-off" id="exportForm24QPdf" title="Export as Pdf" >
					<i class="fa fa-file-pdf-o"></i><span class="hidden-xs hidden-sm"> PDF</span>
					</button> -->
					
                    <!-- Delete Button in Grid Toolbar -->
					<?php if ($form24QUser['Payroll_Group'] || $form24QUser['Admin'] == 'admin') { ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="deleteForm24Q" title="Delete">
						<i class="mdi-action-delete"></i><span class="hidden-xs hidden-sm"> Delete</span>
					</button>
					
					<?php } ?>
                    
                    <!-- Filter Button in Grid Toolbar -->
                    <a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterForm24Q">
                        <i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm">Filter</span>
                    </a>					
			</div>
            
        	<table class="table dataTable table-striped table-dynamic table-hover" id="tableForm24Q">
				<thead>
					<tr>
						<th id="form24qAssessmentYear">Assessment Year</th>
                        <th id="form24qQuarter">Quarter</th>						
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
            
        </div>
    </div>
</div>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="form24Q-context-menu" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextForm24Q"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($form24QUser['View']) { ?>
		<li><a tabindex="-1" id="printContextForm24Q"><i class="fa fa-print"></i> Print</a></li>
		<!-- <li><a tabindex="-1" id="pdfContextForm24Q"><i class="fa fa-file-pdf-o"></i> PDF</a></li> -->
		<?php } if ($form24QUser['Payroll_Group'] == 1 || $form24QUser['Admin'] == 'admin') { ?>
		<li><a tabindex="-1" id="deleteContextForm24Q"><i class="mdi-action-delete"></i> Delete</a></li>		
		<?php } ?>		
	</ul>
</div>

<!--Filter Form-->
<div class="builder" id="filterPanelForm24Q">
	<div id="closeFilterForm24Q"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel filterReset" style="width: 100%;" id="cancelForm24Q">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="applyForm24Q">Apply</button>
            
			<!--Filter for Assessment Year-->
			<div class="form-group">
				<label>Assessment Year</label>
				<div class="input-group">
					<input type="number" class="input-md form-control" min="1" name="filterForm24QAssessmentYrBegin" id="filterForm24QAssessmentYrBegin" data-orientation="top" placeholder="Start"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="input-md form-control" min="1" name="filterForm24QAssessmentYrEnd" id="filterForm24QAssessmentYrEnd" data-orientation="top" placeholder="End"/>
				</div>
			</div>
            
            <div>
                <label>Quarter</label>
                <select class="form-control" data-search="true" id="filterQuarter" >
					<option value="">All</option>
					<?php
					foreach ($assessMnth as $key => $row)
					{
						echo '<option value="'.$row.'">'.$row.'</option>';
					}
					?>
				</select>
            </div>
            
		</div>
	</div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="modalDeleteForm24Q" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteConfForm24q"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfForm24q">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConfirmForm24Q">Yes</button>
			</div>
		</div>
	</div>
</div>

<!-- Generate Form24Q-->
<div class="modal fade" id="modalForm24Q" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" id="viewPageClose" style="margin-right: 10px;" data-dismiss="modal">
					<i class="mdi-hardware-keyboard-backspace" id="backForm24q"></i>
				</button>
				
				<h4 class="modal-title">Generate Form24Q</h4>
			</div>
			
			<div class="modal-body">
				<!--<div id="payslipGridMsgPanel"></div>-->
                <form role="form" id="viewForm24Q" style="overflow: hidden;">
					<div id="form24QView">
                        
                    </div>
                </form>
                
                
				<?php if ($form24QUser['Payroll_Group'] == 1 || $form24QUser['Admin'] == 'admin') { ?>
				
				<!--Add/Edit MonthlySalary Form-->
				<form role="form" class="form-horizontal form-validation" id="formGenerateForm24Q" method="POST" action="">
					<div class="row">                        
                        <div class="form-group">
							<label class="col-md-4 control-label">Assessment Year <span class="short_explanation">*</span></label>
								<div class="col-md-8">
									<select class="form-control vRequired" data-search="true" name="Assessment Year" id="assessmentYr">
									<option value="<?php echo $assessmentYr24Q; ?>"><?php echo $assessmentYr24Q; ?></option>
									</select>
								</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Quarter <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control vRequired" data-search="true" id="formQuarterMnth" name="Location" >
									<option value="select">--Select--</option>
									<?php
									foreach ($assessMnth as $key => $row)
									{
										echo '<option value="'.$row.'">'.$row.'</option>';
									}
									?>
								</select>
							</div>
						</div>
						
					</div>
					
					<button type="reset" class="cancel" id="Form24QReset" style="display: none;" ></button>
				</form>
				
				<?php } ?>
			</div>
			<div class="modal-footer text-center" id="formActionForm24Q">				
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formCancelForm24Q" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitForm24Q" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
			</div>
			
        </div>        
    </div>
</div>

<!--Quarter Closure modal-->
<div class="modal fade" id="modalForm24QPrereqPrompt" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeQuarterClosure"></i></button>
				<h4 class="modal-title form24GenerationTitle"></h4>
			</div>
			
			<div class="modal-body">
			<div id="form24QWarningMessage"> </div>
				<br>
			</div>
			<!-- modal footer -->
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal">Ok</button>
			</div>			
		</div>
	</div>
</div>

<?php } ?>