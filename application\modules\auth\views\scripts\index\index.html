<!DOCTYPE html>
<html lang="en">
<title></title>

<meta name="viewport" content="width=device-width, initial-scale=1.0"> 

<!-- Start Favicon-->
<link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png?v=9B9bjrPr00">
<link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png?v=9B9bjrPr00">
<link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png?v=9B9bjrPr00">
<link rel="manifest" href="site.webmanifest?v=9B9bjrPr00">
<link rel="mask-icon" href="safari-pinned-tab.svg?v=9B9bjrPr00" color="#5bbad5">
<link rel="shortcut icon" href="favicon.ico?v=9B9bjrPr00">
<meta name="apple-mobile-web-app-title" content="">
<meta name="application-name" content="">
<meta name="msapplication-TileColor" content="#da532c">
<meta name="theme-color" content="#ffffff">

<meta name="msapplication-TileImage" content="mstile-70x70.png">
<meta name="msapplication-TileImage" content="mstile-144x144.png">
<meta name="msapplication-TileImage" content="mstile-150x150.png">
<meta name="msapplication-TileImage" content="mstile-310x150.png">
<meta name="msapplication-TileImage" content="mstile-310x310.png">
<meta name="msapplication-TileImage" content="browserconfig.xml">
<!--End Favicon-->

<!-- Common css files - MANDATORY-->
<link href="assets/global/css/ui.css" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
<link href="assets/md-layout1/material-design/css/material.css" rel="stylesheet">

<!-- CSS used for icons -->
<link href="assets/global/css/icons/font-awesome/font-awesome.css" rel="stylesheet">
<link href="assets/global/css/icons/line-icons/line-icons.css" rel="stylesheet">
<link href="assets/global/css/iconstyles.css?v=27" rel="stylesheet">

<!-- Custom css files -->
<link href="assets/md-layout1/css/layoutForLogin.css?v=1" rel="stylesheet">
<link rel="stylesheet" href="assets/global/css/auth/index.css?v=9">

<!-- Mobile number input -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/16.0.4/css/intlTelInput.css">

<!-- <script src="assets/global/plugins/jquery/jquery-1.11.1.min.js"></script> -->
<script src="https://code.jquery.com/jquery-3.7.1.js" integrity="sha256-eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-migrate/3.5.2/jquery-migrate.js"></script>
<script src="assets/global/js/plugins.js?v=1"></script>
<!-- <script src="assets/global/plugins/jquery-validation/jquery.validate.min.js"></script> -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.20.0/jquery.validate.min.js" integrity="sha512-WMEKGZ7L5LWgaPeJtw9MBM4i5w5OSBlSjTjCtSnvFJGSVD26gE5+Td12qN5pvWXhuWaWcVwF++F7aqu9cvqP0A==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>	
<script src="assets/global/plugins/jquery-cookies/jquery.cookies.js?v=1"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/16.0.4/js/utils.js"></script>'
<script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/16.0.4/js/intlTelInput-jquery.min.js"></script>

<script src="assets/global/js/custom/index.js?v=43"></script>
<script src="https://cdn.jsdelivr.net/npm/lazysizes@5.2.0/lazysizes.min.js" async></script>

<!-- Add Firebase products that you want to use -->
<script src="https://www.gstatic.com/firebasejs/6.3.1/firebase-app.js"></script>
<script src="https://www.gstatic.com/firebasejs/6.3.1/firebase-auth.js"></script>

<div id="loginForm" class="loginForm">
        <div class="float-lg-start col-lg-12 float-sm-start col-sm-12">

                <div class="float-lg-start col-lg-6 loginImgDiv">
                        <h2 style="white-space: nowrap;color: var(--primary-color);">Refreshing business software for all your teams
                        </h2>
                        <h4 style="white-space: nowrap;color: #8D708F;">Ready to go, easy to use, affordable cloud
                                solutions</h4>
                        <img class="loginImg" src="assets/global/css/auth/login_img.webp" alt="login_image" onerror="this.onerror=null; this.src='assets/global/css/auth/login_img.png'" height="auto" width="530">
                </div>
                <div class="float-lg-start col-lg-6 float-sm-start col-sm-12">
                        <div class="hr-app-login-Types">
                                <div class="hr-app-logo">
                                        <img class="logo-img org-logo" id="org-logo" src="" alt="org_logo" style="display: none;"
                                                height="auto" width="220">
                                </div>
                                <div class="m-t-40 email-div card-tag">
                                        <div class="card-signin-enabled card-signin-center" id="card-signin-with-email" style="
                                        color: var(--primary-color); border: solid 2px var(--primary-color); border-radius: 15px; padding-top: 23px;
                                        padding-bottom: 23px; padding-left: 25px;">
                                                <div class="email-header">
                                                        <div class="container">
                                                                <span class="float-lg-start col-lg-1 float-sm-start col-sm-1 icon-padding">
                                                                        <img class="mail-logo" id="signinWithEmailIcon"
                                                                                src="assets/global/css/auth/email-logo.webp"
                                                                                onerror="this.onerror=null; this.src='assets/global/css/auth/email-logo.png'"
                                                                                alt="email_image" height="29" width="40"> </span>
                                                                <span class="float-lg-start col-lg-10 float-sm-start col-sm-10 card-signin-text" id="signinWithEmailBtnText">Sign In With Email</span>
                                                        </div>
                                                </div>
                                        </div>
                                        <div class="card-login" style="display: none;">
                                                <div class="form-group login-card hideLoginCardDiv">
                                                        <i class="icons-office-52 pull-right hideLoginCard"
                                                                id="hideLoginCard"></i>
                                                </div>
                                                <div class="container">
                                                        <form role="form" id="emailPaswordForm" class="form-horizontal form-validation" data-style="arrow" action="">
                                                                <div class="form-group login-card user-welcome" style="text-align:center">
                                                                        <img data-src="assets/global/css/auth/user-icon.webp"
                                                                        alt="user_icon" onerror="this.onerror=null; this.src='assets/global/css/auth/user-icon.png'" class="lazyload" height="50" width="50">
                                                                        <div style="text-align:center; color:var(--primary-color)" class="m-t-10 m-b-10">welcome</div>
                                                                </div>

                                                                <div class="form-group login-card user-success" style="text-align:center; display:none" >
                                                                        <div class="user-success-input"></div>
                                                                </div>
                                                                <form role="form" class="form-horizontal form-validation" data-style="arrow" action="">
                                                                <div class="form-group login-card formSigninEmailDiv">
                                                                        <input type="text" id="formSigninEmailId"
                                                                                class="form-control emailId vRequired vEmail"
                                                                                placeholder="Email" autocomplete="off">
                                                                </div>
                                                                <div class="form-group login-card" id="email-verification-div">
                                                                        <button class="email-submit-button"
                                                                                id="email-verification-button" >Next</button>
                                                                        
                                                                </div>
                                                                </form>
                                                                <form id="paswordForm" role="form" class="form-horizontal form-validation" data-style="arrow" action="" autocomplete="off">
                                                                <div class="form-group login-card formSigninPasswordDiv" id="formSigninPasswordDiv" style="display:none">
                                                                        <div class="append-icon">
                                                                                <input type="password"
                                                                                        id="formSigninPassword"
                                                                                        class="form-control password floating-label vRequired"
                                                                                        placeholder="Password"
                                                                                        autocomplete="off"
                                                                                        >
                                                                                <i class="toggle-password fa fa-eye-slash" id="togglePassword"></i>
                                                                        </div>
        
                                                                </div>
                                                                <div class="form-group login-card" id="rememberPasswordDiv" style="display:none">
                                                                        <input type="checkbox" id="rememberPassword" >
                                                                        <label for="rememberPassword" style="width: 100%">Remember
                                                                                Password</label>
                                                                </div>
                                                                <div class="form-group login-card" id="email-password-submit-div" style="display:none">
                                                                        <button class="email-submit-button "
                                                                                id="email-password-submit-button" >Sign In</button>
                                                                </div>
                                                                </form>
                                                               
                                                        </form>
                                                        
                                                        <div class="form-group login-card  forgotPasswordDiv">
                                                                <a id="forgotPassword" class="forgotPassword">Forgot
                                                                        password?</a>
                                                        </div>


                                                </div>
                                        </div>

                                        <div class="card-mail-alert" style="display: none;">
                                                <div class="container">
                                                        <div class="form-group login-card hideLoginCardDiv">
                                                                <i class="icons-office-52 pull-right hideLoginCard"
                                                                        id="hideLoginCard"></i>
                                                        </div>

                                                        <div class="form-group login-card  forgotPasswordDiv" style="margin-bottom: 20px;">
                                                                <h5 style="color: var(--primary-color);margin-bottom: 0px !important;">
                                                                   You are yet to verify your email address. We have just sent a new verification link to your registered email 
                                                                   address for your verification. Please check your spam folder if you do not see it in your inbox.
                                                                </h5>
                                                                <img class="lazyload" data-src="assets/global/css/auth/shake-mail.gif"
                                                                        alt="shake_email" height="246" width="261"> 
                                                                
                                                        </div>

                                                        <div class="form-group login-card  forgotPasswordDiv">
                                                                <a id="sendSignInLink" class="sendSignInLink">Resend</a>
                                                        </div>

                                                </div>
                                        </div>

                                        <div class="card-verification-mail-alert" style="display: none;">
                                                <div class="container">
                                                        <div class="form-group login-card hideLoginCardDiv">
                                                                <i class="icons-office-52 pull-right hideLoginCard"
                                                                        id="hideLoginCard"></i>
                                                        </div>

                                                        <div class="form-group login-card  forgotPasswordDiv">
                                                                <img src="assets/global/css/auth/mail-alert.webp" onerror="this.onerror=null; this.src='assets/global/css/auth/mail-alert.png'"
                                                                        alt="mail-alert" height="110" width="217">
                                                        </div>
                                                        <div class="form-group login-card  mailAlertContent">
                                                                Sorry!, Your email address must be verified before you sign-in
                                                        </div>

                                                        <div class="form-group login-card  mailAlertSpamContent">
                                                                We have just sent a new verification link to your registered email 
                                                                address for your verification. Please check your spam folder if you do not see it in your inbox.
                                                        </div>

                                                        <button class="resend-btn" id="sendVerificationLink" style="width: 50% !important;">Resend</button>
                                                </div>
                                        </div>


                                        <div class="card-reset" style="display: none;">
                                                <div class="container">
                                                        <div class="form-group login-card ">
                                                                <i class="icons-office-52 pull-right hideLoginCard"
                                                                        id="hideLoginCard"></i>
                                                        </div>

                                                        <div class="form-group login-card  forgotPasswordDiv">
                                                                <h3
                                                                        style="white-space: nowrap;color:var(--primary-color);margin-bottom: 24px;">
                                                                        Reset your password</h3>
                                                                <h5 style="color: gray;">Enter your email id and get
                                                                        verification email</h5>
                                                        </div>
                                                        <form role="form" id="forgotEmailPaswordForm" class="form-horizontal form-validation" data-style="arrow" action="" autocomplete="off">
                                                                <div class="form-group login-card ">
                                                                        <input type="text" id="formResetPasswordEmailId"
                                                                                class="form-control emailId floating-label vEmail vRequired"
                                                                               
                                                                                placeholder="Email" autocomplete="off">
                                                                        
                                                                </div>
                                                        </form>

                                                        <div class="form-group login-card m-b-40">
                                                                <button class="email-reset-button"
                                                                        id="email-reset-button">Send</button>
                                                        </div>

                                                        <div class="form-group login-card  forgotPasswordDiv">
                                                                <a id="signInEmailLink" class="signInEmailLink">Sign
                                                                        In</a>
                                                        </div>

                                                </div>
                                        </div>


                                        <div class="card-new-password" style="display: none;min-height: 300px;">
                                                <div class="container">
                                                        <div class="form-group login-card ">
                                                                <i class="icons-office-52 pull-right hideLoginCard"
                                                                        id="hideLoginCard"></i>
                                                        </div>
                                                        <form role="form"  name="emailPasswordUpdateForm" id="emailPasswordUpdateForm" class="form-horizontal form-validation card-new-password-form" data-style="arrow" action="">
                                                                <div class="form-group login-card ">
                                                                        <div class="append-icon">
                                                                                <input type="text"
                                                                                        id="formUpdatePasswordEmailId"
                                                                                        class="form-control newemailId vRequired vEmail"
                                                                                        placeholder="Email"
                                                                                        required=""
                                                                                        aria-required="true" 
                                                                                        autocomplete="off">
                                                                                <i class="success-icon warning-icons fa fa-check-circle"
                                                                                        style="display: none;color:green !important;"></i>
                                                                                <i class=" wrong-icon warning-icons icons-office-52-circle"
                                                                                        style="display: none;"></i>
                                                                        </div>
                                                                </div>
                                                                <div id="newEmailPasswordDiv" class="form-group login-card hidden">
                                                                        <div class="append-icon">
                                                                                <input type="password"
                                                                                        id="newEmailPassword"
                                                                                        class="form-control newEmailPassword password vPassword vRequired"
                                                                                        placeholder="Password"
                                                                                        required=""
                                                                                        aria-required="true" autocomplete="off">
                                                                                <i
                                                                                        class="toggle-password  fa fa-eye-slash"></i>
                                                                        </div>
                                                                </div>
                                                                <div id="confirmEmailPasswordDiv" class="form-group login-card hidden">
                                                                        <div class="append-icon">
                                                                                <input type="password"
                                                                                        id="confirmEmailPassword"
                                                                                        class="form-control confirmEmailPassword password vPassword vRequired"
                                                                                        placeholder="Confirm Password"
                                                                                        required=""
                                                                                        aria-required="true" autocomplete="off">
                                                                                <i
                                                                                        class="toggle-password  fa fa-eye-slash"></i>
                                                                        </div>
                                                                </div>

                                                                <div class="form-group login-card  forgotPasswordDiv hidden">
                                                                        <a id="forgotPassword" class="forgotPassword">Forgot
                                                                                password?</a>
                                                                </div>
                                                        </form>

                                                        <div id="rememberUpdatePasswordDiv" class="form-group login-card m-b-40 hidden">
                                                                <input type="checkbox" id="rememberUpdatePassword">
                                                                <label for="rememberUpdatePassword" style="width: 100%">Remember Password</label>
                                                        </div>
                                                        <div class="form-group login-card ">
                                                                <button class="email-confirm-button hidden"
                                                                        id="formUpdatePasswordSubmit">Update</button>
                                                                <button class="email-confirm-button "
                                                                        id="formUpdatePasswordNext">Next</button>
                                                                <button class="email-confirm-button hidden"
                                                                        id="formVerifiedEmailSignin">Sign In</button>
                                                        </div>
                                                </div>
                                        </div>


                                </div>

                                <div class="m-t-40 card-sent-link-succuss" style="display: none;">
                                        <div class="container">
                                                <div class="form-group login-card ">
                                                        <i class="icons-office-52 pull-right hideLoginCard"
                                                                id="hideLoginCard"></i>
                                                </div>

                                                <div class="form-group login-card  m-b-40" style="text-align:center">
                                                        <img class="success-gif lazyload"
                                                                data-src="assets/global/css/auth/succuss-logo.gif"
                                                                alt="success" height="120" width="120">
                                                </div>

                                                <div class="form-group login-card  mailAlertContent" id="mailSuccessContent">
                                                        
                                                </div>

                                                <div class="form-group login-card  mailAlertSpamContent">
                                                        If the email does not arrive, check your spam folder or try after sometime.
                                                </div>
                                        </div>
                                </div>

                                <div class="m-t-40 card-mail-failure" style="display: none;">
                                        <div class="container">
                                                <div class="form-group login-card hideLoginCardDiv">
                                                        <i class="icons-office-52 pull-right hideLoginCard"
                                                                id="hideLoginCard"></i>
                                                </div>

                                                <div class="form-group login-card forgotPasswordDiv">
                                                        <img src="assets/global/css/auth/warning.webp"
                                                                alt="image" height="100" width="auto">
                                                </div>
                                                <div class="form-group login-card mailAlertContent" id="mailFailureContent">
                                                        
                                                </div>
                                                <div class="form-group login-card forgotPasswordDiv" style="display: none;" id="resendSigninLink">
                                                        <a id="resend-btn" class="sendSignInLink">Resend</a>
                                                </div>
                                        </div>
                                </div>


                                <div class="m-t-40 mobile-div card-tag">
                                        <div class="card-signin-enabled card-signin-center card-signin-with-mobile" style="
                                        color: var(--primary-color); border: solid 2px var(--primary-color); border-radius: 15px; padding-top: 23px;
                                        padding-bottom: 23px; padding-left: 25px;">
                                                <div class="mobile-header">
                                                        <div class="container">
                                                                <span class=" float-lg-start col-lg-1 float-sm-start col-sm-1 icon-padding">
                                                                        <img class="m-r-20 mobile-logo" id="signinWithMobileIcon"
                                                                                src="assets/global/css/auth/mobile-icon.webp" onerror="this.onerror=null;this.src='assets/global/css/auth/mobile-icon.png'"
                                                                                alt="mobile-icon" height="40" width="25"></span>
                                                                <span class=" float-lg-start col-lg-10 float-sm-start col-sm-10 card-signin-text" id="signinWithMobileBtnText">Sign In With Mobile Number</span>
                                                        </div>
                                                </div>
                                        </div>
                                        <div class="card-mobile-login" style="display: none;">
                                                <div class="form-group login-card hideLoginCardDiv">
                                                        <i class="icons-office-52 pull-right hideMblLoginCard"
                                                                id="hideMblLoginCard"></i>
                                                </div>
                                                <div class="container">
                                                        
                                                        <form role="form" id="mobilePaswordForm" class="form-horizontal form-validation" data-style="arrow" action="" autocomplete="off">
                                                                <div class="form-group login-card ">
                                                                                <input type="tel" id="mobileNo"
                                                                                class="form-control formMobileNo vRequired  onlyNum allownumericwithoutdecimal"
                                                                                placeholder="Mobile Number" autocomplete="off"
                                                                                maxlength="15" >
                                                                </div>
                                                                
                                                                <div class="form-group login-card  m-t-40">
                                                                        <div class="append-icon">
                                                                                <input type="password"
                                                                                        id="formSigninOtp"
                                                                                        class="form-control formSigninOtp password floating-label vRequired vOTP allownumericwithoutdecimal"
                                                                                        placeholder="OTP"
                                                                                        autocomplete="off" disabled>
                                                                                <i class="resendOtp">Resend</i>
                                                                                <i class="toggle-password taggle-resend-eye fa fa-eye-slash"></i>
                                                                        </div>

                                                                </div>
                                                                <div class="form-group login-card ">
                                                                        <a id="getOtpLink" class="getOtpLink" tabindex="0">Generate OTP</a>                                                                            
                                                                        <div class="otp-timmer" id="otp-timmer" style="display:none">
                                                                                <img class="m-r-10 timer-logo lazyload"
                                                                                data-src="assets/global/css/auth/timmer.webp" onerror="this.onerror=null; this.src='assets/global/css/auth/timmer.png'"
                                                                                alt="timmer" height="36" width="33">
                                                                                <span></span>
                                                                        </div>
                                                                </div>
                                                        </form>
                                                        
                                                <div class="form-group login-card">
                                                        <button class="email-submit-button "
                                                                id="mobile-password-submit-button" disabled style="cursor:not-allowed">Sign In</button>
                                                </div>

                                                </div>
                                        </div>

                                </div>


                                
                                <div class="m-t-40 card-tag">
                                        <div class="card-signin-enabled card-signin-center google-div" style="
                                        color: var(--primary-color); border: solid 2px var(--primary-color); border-radius: 15px; padding-top: 23px;
                                        padding-bottom: 23px; padding-left: 25px;">
                                                <div class="container" id="btnGooglePopupSignin">
                                                        <span class=" float-lg-start col-lg-1 float-sm-start col-sm-1 icon-padding"><img class="mail-logo" id="signinWithGoogleIcon"
                                                                src="assets/global/css/auth/google-logo.webp" alt="google_logo" onerror="this.onerror=null;this.src='assets/global/css/auth/google-logo.png'" height="30" width="30"> </span>
                                                        <span class=" float-lg-start col-lg-10 float-sm-start col-sm-10 card-signin-text" id="signInWithGoogle">Sign In With Google</span>
                                                </div>
                                        </div>
                                </div>

                                <div class="m-t-40 card-tag">
                                        <div class="card-signin-enabled card-signin-center window-div" style="
                                        color: var(--primary-color); border: solid 2px var(--primary-color); border-radius: 15px; padding-top: 23px;
                                        padding-bottom: 23px; padding-left: 25px;">
                                                <div class="container" id="btnMicrosoftPopupSignin">
                                                        <span class=" float-lg-start col-lg-1 float-sm-start col-sm-1 icon-padding">
                                                                <img class="m-r-20 mail-logo" id="signinWithMicrosoftIcon"
                                                                        src="assets/global/css/auth/microsoft-logo.webp" alt="microsoft" onerror="this.onerror=null;this.src='assets/global/css/auth/microsoft-logo.png'"
                                                                        height="30" width="30"> </span>
                                                        <span class=" float-lg-start col-lg-10 float-sm-start col-sm-10 card-signin-text" id="signInWithMicrosoft">Sign In With Microsoft</span>
                                                </div>
                                        </div>
                                </div>

                        </div>
                </div>


        </div>


</div>

<div id="network-error" class="network-error" style="display: none;">
        <div class="float-lg-start col-lg-12 float-sm-start col-sm-12">
                <div class="hr-app-error-page-logo">
                        <img class="logo-img org-logo-error lazyload" id="org-logo-error" data-src="" alt="org_logo" height="45"
                                width="150">
                </div>

                <div class="networkErrorImgDiv">
                        <img class="networkErrorImg lazyload" id="network-error-img" data-src="assets/global/css/auth/networkError.webp" onerror="this.onerror=null; this.src='assets/global/css/auth/networkError.png'" alt="network_error" height="350">
                        <img class="networkErrorImg lazyload" id="ip-address-restriction-img" data-src="assets/global/css/auth/ip-address-restriction.webp" onerror="this.onerror=null; this.src='assets/global/css/auth/ip-address-restriction.png'" alt="network_error" height="350">
                        <img class="networkErrorImg lazyload" id="ip-address-not-found-img" data-src="assets/global/css/auth/ip-address-not-found.webp" onerror="this.onerror=null; this.src='assets/global/css/auth/ip-address-not-found.png'" alt="network_error" height="350">
                </div>
               
                <div style="max-width: 700px; margin: auto;">
                        <h3 class="network-error-msg" id="error-msg1" style="text-align: center;"></h3>
                </div>
                <h4 class="" id="error-msg2" style="text-align: center; color: gray;"></h4>

                <div class="refresh-div">
                        <button class="refresh-btn" id="refresh-btn">Refresh</button>
                </div>
        </div>
</div>

<input type="submit" hidden class="mdl-button mdl-js-button mdl-button--raised"
id="sign-in-button" value="Sign-in" />


<div class="footer">
        <p><a id="support_link"  rel="noopener noreferrer" target="_blank">Support</a> | <a id="terms_link"  rel="noopener noreferrer" target="_blank">Terms of use </a> | <a id="policy_link"  rel="noopener noreferrer" target="_blank">Privacy Policy</a> </p>
        <p id="capriceLink" style="display:none;">© 2013-<span id="currentYear"></span> <a href="http://www.capricetech.com" rel="noopener noreferrer" target="_blank">Caprice Cloud Solutions Pvt Ltd.</a> <span>All rights reserved.</span></p>
</div>
</html>