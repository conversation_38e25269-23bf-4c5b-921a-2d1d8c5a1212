<?php
//=========================================================================================
//=========================================================================================
/* Program : Grade.php											   				         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MQL Query to retrive, add, update grade.							     *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Shobana	              Initial Version        	         *                  
 *  0.3        14-May-2014    Mahesh                  Added Function                     *                                                 
 *                                                    1.getSalaryChange                  *
 *                                                    Modified Function                  *
 *                                                    Changed Functions                  * 
 *                                                    1.updateGrade()                    * 
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Nivethitha              Changes in file for mobile app     *
 *                                                    1.Extra fields are added in        *
 *                                                    field list of list query.          *
 *                                                                                       *
 *  1.4        25-Feb-2016    Deepak              Changes in file for Bootstrap          *
 *                                                                                       */
//=========================================================================================
//=========================================================================================
class Employees_Model_DbTable_Grade extends Zend_Db_Table_Abstract
{
	protected $_dbPersonal  = null;
	protected $_orgDF       = null;
	protected $_db          = null;
	protected $_ehrTables   = null;
	protected $_dbCommonFun = null;

    public function init()
    {
        $this->_ehrTables   = new Application_Model_DbTable_Ehr();
        $this->_db          = Zend_Registry::get('subHrapp');
        $this->_dbPersonal  = new Employees_Model_DbTable_Personal();
        $this->_orgDF       = $this->_ehrTables->orgDateformat();
		$this->_dbCommonFun = new Application_Model_DbTable_CommonFunction();
    }
    
	/**
	 *	List employees grade details
	*/
	public function listEmployeeGrade ($page, $rows, $sortField, $sortOrder, $searchAll=null, $searchArr, $logEmpId)
    {
		$grade            = $searchArr['grade'];
		$minExpBeginRange = $searchArr['minExpBeginRange'];
		$minExpEndRange   = $searchArr['minExpEndRange'];
		$maxExpBeginRange = $searchArr['maxExpBeginRange'];
		$maxExpEndRange   = $searchArr['maxExpEndRange'];
		$minSalBeginRange = $searchArr['minSalBeginRange'];
		$minSalEndRange   = $searchArr['minSalEndRange'];
		$maxSalBeginRange = $searchArr['maxSalBeginRange'];
		$maxSalEndRange   = $searchArr['maxSalEndRange'];
		$parentGrade      = $searchArr['parentGrade'];
		
		switch($sortField)
        {
			case 1: $sortField = 'grade.Grade'; break;
			case 2: $sortField = 'grade1.Grade'; break;
			case 3: $sortField = 'grade.Min_AnnualSalary'; break;
			case 4: $sortField = 'grade.Max_AnnualSalary'; break;
			case 5: $sortField = 'grade.Min_HourlyWages'; break;
			case 6: $sortField = 'grade.Max_HourlyWages'; break;
		}
		
		$qryGrade = $this->_db->select()
							->from(array('grade'=>$this->_ehrTables->empGrade),
								   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS grade.Grade_Id as count'),'grade.Grade_Id',
										 'grade.Grade','grade.Min_Experience','Log_Id'=>new Zend_Db_Expr($logEmpId),
										 'grade.Max_Experience','grade.Min_HourlyWages','grade.Max_HourlyWages',
										 'grade.Min_OvertimeWages','grade.Max_OvertimeWages','grade.Lock_Flag', 'grade.Description',
										 'grade.Min_AnnualSalary','grade.Max_AnnualSalary','grade.OvertimeFixedAmount',
										 'grade.Eligible_Overtime','grade.Overtime_Allocation','grade.Overtime_Wage_Index',
										 'Added_On'=>new Zend_Db_Expr("DATE_FORMAT(grade.Added_On,'".$this->_orgDF['sql']." %H:%i:%s')"),
										 'Updated_On'=>new Zend_Db_Expr("DATE_FORMAT(grade.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s')")))
							
							->joinLeft(array('grade1'=>$this->_ehrTables->empGrade),'grade1.Grade_Id=grade.ParentGrade_Id',
									   array('grade1.Grade as ParentGrade','grade.ParentGrade_Id'))
							
							->joinInner(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=grade.Added_By',
										array(new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as Added_By_Name")))
							
							->joinLeft(array('emp1'=>$this->_ehrTables->empPersonal),'emp1.Employee_Id=grade.Updated_By',
										array(new Zend_Db_Expr("CONCAT(emp1.Emp_First_Name, ' ', emp1.Emp_Last_Name) as Updated_By_Name")))
							
							->order("$sortField $sortOrder")
							->limit($rows, $page);
		
		/**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			$conditions = $this->_db->quoteInto('grade.Grade Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or grade1.Grade Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or grade.Min_HourlyWages Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or grade.Max_HourlyWages Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or grade.Min_AnnualSalary Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or grade.Max_AnnualSalary Like ?', "%$searchAll%");
			
			$qryGrade->where($conditions);
		}
					
        if ( ! empty($grade) && preg_match('/^[0-9a-zA-Z ]+$/', $grade) )
        {
            $qryGrade->where('grade.Grade Like ?', "$grade%");
        }
		
		if (($minExpBeginRange >=0) && preg_match('/^[0-9*\.]/', $minExpBeginRange) && !empty($minExpBeginRange))
		{
		    $qryGrade->where($this->_db->quoteInto('grade.Min_Experience >= ?', $minExpBeginRange));
		}
		
		if (($minExpEndRange >=0) && preg_match('/^[0-9*\.]/', $minExpEndRange) && !empty($minExpEndRange))
		{
		    $qryGrade->where($this->_db->quoteInto('grade.Min_Experience <= ?', $minExpEndRange));
		}
		
		if (($maxExpBeginRange >=0) && preg_match('/^[0-9*\.]/', $maxExpBeginRange) && !empty($maxExpBeginRange))
		{
		    $qryGrade->where($this->_db->quoteInto('grade.Max_Experience >= ?', $maxExpBeginRange));
		}
		
		if (($maxExpEndRange >=0) && preg_match('/^[0-9*\.]/', $maxExpEndRange) && !empty($maxExpEndRange))
		{
		    $qryGrade->where($this->_db->quoteInto('grade.Max_Experience <= ?', $maxExpEndRange));
		}
		
		if (($minSalBeginRange >=0) && preg_match('/^[0-9*\.]/', $minSalBeginRange) && !empty($minSalBeginRange))
		{
		    $qryGrade->where($this->_db->quoteInto('grade.Min_AnnualSalary >= ?', $minSalBeginRange));
		}
		
		if (($minSalEndRange >=0) && preg_match('/^[0-9*\.]/', $minSalEndRange) && !empty($minSalEndRange))
		{
		    $qryGrade->where($this->_db->quoteInto('grade.Min_AnnualSalary <= ?', $minSalEndRange));
		}
		
		if (($maxSalBeginRange >=0) && preg_match('/^[0-9*\.]/', $maxSalBeginRange) && !empty($maxSalBeginRange))
		{
		    $qryGrade->where($this->_db->quoteInto('grade.Max_AnnualSalary >= ?', $maxSalBeginRange));
		}
		
		if (($maxSalEndRange >=0) && preg_match('/^[0-9*\.]/', $maxSalEndRange) && !empty($maxSalEndRange))
		{
		    $qryGrade->where($this->_db->quoteInto('grade.Max_AnnualSalary <= ?', $maxSalEndRange));
		}
		
		if (! empty($parentGrade) && preg_match('/^[0-9a-zA-Z ]+$/', $parentGrade))
        {
            $qryGrade->where('grade.ParentGrade_Id = ?',$parentGrade);
        }
		
		/**
		 * SQL queries
		 * Get data to display
		*/
		$grade = $this->_db->fetchAll($qryGrade);
		
        
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		$leafGrade = $this->parentGrade();
        
        for ($i = 0; $i<count($grade); $i++)
        {
            $grade[$i]['LastChild'] = isset($leafGrade['Grade_Id'])?$leafGrade['Grade_Id']:'';
            $grade[$i]['LastChild_Name'] = isset($leafGrade['Grade'])?$leafGrade['Grade']:'';
        }
		
		/* Total data set length */
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empGrade, new Zend_Db_Expr('COUNT(Grade_Id)')));
		
		/**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $grade);
	}
	
	/**
	 *	Update grade details in grade table and update system log too
    */
	public function updateGrade($gradeArray, $parentGrade, $sessionId, $formName)
	{
	   $nameExistsQry=$this->_db->select()->from($this->_ehrTables->empGrade, new Zend_Db_Expr('COUNT(Grade_Id)'))
		->where('Grade = ?',$gradeArray['Grade']);
        if( !empty($gradeArray['Grade_Id']))
        {
            $nameExistsQry->where('Grade_Id != ?',$gradeArray['Grade_Id']);
        }
        
        $nameExists=$this->_db->fetchOne($nameExistsQry);
		
		/**
		 *	Check grade is Already exist or not
		*/
		$qryGrade = $this->_db->select()->from($this->_ehrTables->empGrade, new Zend_Db_Expr('Count(Grade_Id)'))
									->where('Grade = ?',$gradeArray['Grade'])
									->where('Min_Experience = ?',$gradeArray['Min_Experience'])
									->where('Max_Experience = ?',$gradeArray['Max_Experience'])
									->where('Min_HourlyWages = ?',$gradeArray['Min_HourlyWages'])
									->where('Max_HourlyWages = ?',$gradeArray['Max_HourlyWages'])
									->where('Min_OvertimeWages = ?',$gradeArray['Min_OvertimeWages'])
									->where('Max_OvertimeWages = ?',$gradeArray['Max_OvertimeWages'])
									->where('Min_AnnualSalary = ?',$gradeArray['Min_AnnualSalary'])
									->where('Max_AnnualSalary = ?',$gradeArray['Max_AnnualSalary']);
		
		if (!empty($gradeArray['Grade_Id']))
		{
			$qryGrade->where('Grade_Id != ?', $gradeArray['Grade_Id']);
		}
		
        $isExist = $this->_db->fetchOne($qryGrade);
		
		if ( $nameExists == 0 && $isExist == 0)
		{
			
			// If grade id exist then process update action
			
			if (!empty($gradeArray['Grade_Id']))
			{
				$action = 'Edit';
				
				$gradeArray['Updated_On'] = date('Y-m-d H:i:s');
				$gradeArray['Updated_By'] = $sessionId;
				
				if( !empty($parentGrade) )
				{
					$gradeArray['ParentGrade_Id'] = $parentGrade;
				}
				else
				{
					$gradeArray['ParentGrade_Id'] =  new Zend_Db_Expr('NULL');	
				}
				
				$updated = $this->_db->update($this->_ehrTables->empGrade, $gradeArray, array('Grade_Id = '. $gradeArray['Grade_Id']));
			}
			else
			{
				/**
				 *	If grade id is empty then process add action
				*/
				$action = 'Add';
				
				$gradeArray['Added_On'] = date('Y-m-d H:i:s');
				$gradeArray['Added_By'] = $sessionId;
				
				if( !empty($parentGrade) )
				{
					$gradeArray['ParentGrade_Id'] = $parentGrade;
				}
				else
				{
					$gradeArray['ParentGrade_Id'] = new Zend_Db_Expr('NULL');
				}
				
				$updated = $this->_db->insert($this->_ehrTables->empGrade, $gradeArray);
			}
			
            /** Update the DataSetup status **/
            if($updated)
            {
                $updated = $this->_dbCommonFun->updateDataSetupDashboard('Completed','20');                
            }
            
            
			/**
			 *	this function will handle
			 *		update system log function
			 *		clear submit lock fucntion
			 *		return success/failure array
			*/
			$result = $this->_dbCommonFun->updateResult (array('updated'        => $updated,
															   'action'         => $action,
															   'trackingColumn' => $gradeArray['Grade'],
															   'formName'       => $formName,
															   'sessionId'      => $sessionId,
															   'tableName'      => $this->_ehrTables->empGrade));
			
			if ($result['success'] && !empty($gradeArray['Grade_Id']))
			{
				$getSalaryChange = $this->getSalaryChange (array('Grade_Id'          => $gradeArray['Grade_Id'],

																 'Min_AnnualSalary'  => $gradeArray['Min_AnnualSalary'],
																 'Max_AnnualSalary'  => $gradeArray['Max_AnnualSalary'],
																 'Min_HourlyWages'   => $gradeArray['Min_HourlyWages'],
																 'Max_HourlyWages'   => $gradeArray['Max_HourlyWages'],
																 'Min_OvertimeWages' => $gradeArray['Min_OvertimeWages'],
																 'Max_OvertimeWages' => $gradeArray['Max_OvertimeWages']));
				
				if (count($getSalaryChange[0]) > 0 || count($getSalaryChange[1]) > 0)
				{
					$result = array('success' => true, 'msg'=>$formName.' updated successfully. Please update salary details for '.$gradeArray['Grade'].' grade employees', 'type'=>'success');
				}
			}
			
			$result['comboPair'] = $this->getGrade();
			
			return $result;
		}
		else
		{
			return array('success' => false, 'msg'=>$formName.' already exist', 'type'=>'info');
		}
	}
	
	public function checkExist ($gradeId, $table)
	{
		return $this->_db->fetchOne($this->_db->select()->from($table, new Zend_Db_Expr('Count(Grade_Id)'))
											  ->where('Grade_Id = ?', $gradeId));
	}
	
	/**
	 *	Delete Grade
	*/
    public function deleteGrade ($gradeId, $logEmpId, $formName)
    {
		$exInAllowance  = $this->checkExist ($gradeId, $this->_ehrTables->allowances);
		$exInDesigation = $this->checkExist ($gradeId, $this->_ehrTables->designation);
		$exInHrs        = $this->checkExist ($gradeId, $this->_ehrTables->timesheetHrs);
		$exInLoan       = $this->checkExist ($gradeId, $this->_ehrTables->loanType);
		$exMonthSal     = $this->checkExist ($gradeId, $this->_ehrTables->salary);
		$exAuditMonth   = $this->checkExist ($gradeId, $this->_ehrTables->salaryHistory);
		$exAuditHrly    = $this->checkExist ($gradeId, $this->_ehrTables->auditWages);
		$exInHrlyWage   = $this->checkExist ($gradeId, $this->_ehrTables->hourlyWages);
		$exEmpInsurance = $this->checkExist ($gradeId, $this->_ehrTables->insuranceGrade);
		
		
		$parentGrade=$this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empGrade, new Zend_Db_Expr('Count(Grade_Id)'))
	                                	->where('ParentGrade_Id = ?', $gradeId));
	 	
 		
		if ($exInAllowance == 0 && $exInDesigation == 0 && $exInHrs == 0 && $exInLoan == 0 && $exMonthSal == 0 && $exAuditMonth == 0 &&
			$exAuditHrly == 0 && $exInHrlyWage == 0 && $exEmpInsurance == 0 && $parentGrade==0)
		
		{
			$deleted = 0;
			
            $gradeRow = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->empGrade, array('Lock_Flag', 'Grade'))
											 ->where('Grade_Id = ?', $gradeId));
			 
            if ($gradeRow['Lock_Flag'] == 0)
            {
                $deleted = $this->_db->delete($this->_ehrTables->empGrade,'Grade_Id='.$gradeId);
			}
			
			return $this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
															'lockFlag'       => $gradeRow['Lock_Flag'],
															'formName'       => $formName,
															'trackingColumn' => $gradeRow['Grade'],
															'sessionId'      => $logEmpId));
        }
        else
        {
            return array('success'=>false, 'msg'=>'Unable to delete '.$formName.'. Please, contact system admin', 'type'=>'info');
        }
	}
    
	//to list grades in dropdown
    public function getGrade()
    {
        //$this->_db->setFetchMode(Zend_Db::FETCH_OBJ);
		
        return $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->empGrade,array('Grade_Id','Grade'))
        ->order('Grade ASC'));
	}


	public function getEmployeeAssoiciatedGrade()
	{
		$employeeAssoicatedGradeDetails = array();
		$qryEmployeeAssoicatedGrade 	= $this->_db->select()->from(array('EG'=>$this->_ehrTables->empGrade),array('EG.Grade_Id','EG.Grade'))
		 										->joinInner(array('DN'=>$this->_ehrTables->designation), 'EG.Grade_Id=DN.Grade_Id', array(''))
												->joinInner(array('EJ'=>$this->_ehrTables->empJob), 'EJ.Designation_Id=DN.Designation_Id', array(''))
												->order('EG.Grade ASC');
		$employeeAssoicatedGradeDetails = $this->_db->fetchPairs($qryEmployeeAssoicatedGrade);
		return $employeeAssoicatedGradeDetails;
	}
	
	//to list grades in dropdown
    public function parentGrade()
    {
        $parentgrades = $this->_db->fetchCol($this->_db->select()->from(array('grade'=>$this->_ehrTables->empGrade),
																		array('grade.ParentGrade_Id')));
       
	    if(!empty($parentgrades))
        {
            return $this->_db->fetchRow($this->_db->select()->from(array('grade'=>$this->_ehrTables->empGrade),
																   array('grade.Grade_Id','grade.Grade'))
										->where('grade.Grade_Id NOT IN (?)', $parentgrades));
        }
    }
	//to list grades in dropdown
    public function listParentGrade($gradeId)
    {
        //$this->_db->setFetchMode(Zend_Db::FETCH_OBJ);
		
        return $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->empGrade,array('Grade_Id','Grade'))
									  ->where('Grade_Id != ?',$gradeId)
                                      ->order('Grade ASC'));
	   
	
	}
	
	
	
    
	//to fetch the grade record based on its id
    public function viewGrade($gradeId)
    {
		$this->_db->fetchRow($this->_db->select()->from(array('grade'=>$this->_ehrTables->empGrade),
														array('grade.Grade_Id','grade.Grade','grade.ParentGrade_Id','grade.Description',
															  'grade.Min_Experience','grade.Max_Experience','grade.Min_HourlyWages',
															  'grade.Max_HourlyWages','grade.Min_OvertimeWages','grade.Max_OvertimeWages',
															  'grade.Min_AnnualSalary','grade.Max_AnnualSalary','grade.Updated_By','grade.OvertimeFixedAmount',
															  new Zend_Db_Expr("DATE_FORMAT(grade.Added_On,'".$this->_orgDF['sql']." at %T') as Added_Date"),
															  new Zend_Db_Expr("DATE_FORMAT(grade.Updated_On,'".$this->_orgDF['sql']." at %T') as Modified_Date")))
							 
							 ->joinLeft(array('grade1'=>$this->_ehrTables->empGrade),'grade1.Grade_Id=grade.ParentGrade_Id',
										array('grade1.Grade as ParentGrade'))
							 
							 ->joinInner(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=grade.Updated_By',
										 array(new Zend_Db_Expr("CONCAT(Emp_First_Name, ' ', Emp_Last_Name) as Employee_Name")))
							 
							 ->where('grade.Grade_Id = ?', $gradeId));
	}
    
	
	//to check whether atleast a single grade exists or not
    public function checkGrade()
    {
        return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empGrade, new Zend_Db_Expr('Count(Grade_Id)')));
    }
    
	/**
	 * Get grade name by gradeId
	 */
	public function getGradeById($gradeId)
    {
        return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empGrade,array('Grade'))
									->where('Grade_Id = ?', $gradeId));
    }
	
    //getting the child grades
    //i,e if A grade is passed as parameter, then all grades lower than A grades can be fetched
    public function getParentgrade($gradeId)
    {
        $getGrade = array($gradeId);
        if(!empty($gradeId))
        {
            while ( $gradeId != NULL && !empty($gradeId))
            {
                $qryGrade = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empGrade, 'Grade_Id')
                                 									 ->where('ParentGrade_Id = ?', $gradeId));
                if(in_array($qryGrade,$getGrade) || empty($qryGrade) || $gradeId == $qryGrade)
                {
                    break;
                }
                elseif(!empty($qryGrade))
                {
                    array_push($getGrade, $qryGrade);
                    $gradeId = $qryGrade;
                }
            }
        }
        return $getGrade;
    }
	
	/**
	 * Get salary range details for given grade , minAnnualSalary, maxAnnualSalary, minHourlyWage,
       maxHourlyWage, minOverTimeWage, maxOverTimeWage, desiId
     */
    public function getSalaryChange ($gradeArray)
    {
		$gradeId         = $gradeArray['Grade_Id'];
		
		$minAnnualSalary = $gradeArray['Min_AnnualSalary'];
		$maxAnnualSalary = $gradeArray['Max_AnnualSalary'];
		$minHourlyWage   = $gradeArray['Min_HourlyWages'];
		$maxHourlyWage   = $gradeArray['Max_HourlyWages'];
		$minOverTimeWage = $gradeArray['Min_OvertimeWages'];
		$maxOverTimeWage = $gradeArray['Max_OvertimeWages'];
		$desiId          = '';
		
		if (array_key_exists("Designation_Id",$gradeArray))
			$desiId = $gradeArray['Designation_Id'];
		
		$whereCond = $this->_db->quoteInto('(Regular_Hourly_Wages > ? or ', $maxHourlyWage) .
					$this->_db->quoteInto('Regular_Hourly_Wages < ? or ', $minHourlyWage) .
					$this->_db->quoteInto('Overtime_Hourly_Wages > ? or ', $maxOverTimeWage) .
					$this->_db->quoteInto('Overtime_Hourly_Wages < ? )', $minOverTimeWage);
    	
    	$whereCondition = $this->_db->quoteInto('Annual_Gross_Salary > ? or ', $maxAnnualSalary) .
							$this->_db->quoteInto('Annual_Gross_Salary < ? ', $minAnnualSalary);
    	
    	$getSalaryChangeQry = $this->_db->select()->from(array('S'=>$this->_ehrTables->salary), 'S.Employee_Id')
									
									->joinInner(array('EJ'=>$this->_ehrTables->empJob), 'EJ.Employee_Id = S.Employee_Id', array())
									
									->joinInner(array('D'=>$this->_ehrTables->designation),'EJ.Designation_Id = D.Designation_Id', array())
									
									->joinInner(array('G'=>$this->_ehrTables->empGrade),'G.Grade_Id = D.Grade_Id', array())
									
									->where('G.Grade_Id = ?', $gradeId)
									->where('EJ.Emp_Status = ?', "Active")
									->where($whereCondition);
		
    	$getWageChangeQry = $this->_db->select()
											->from(array('W'=>$this->_ehrTables->hourlyWages), 'W.Employee_Id')
											
											->joinInner(array('EJ'=>$this->_ehrTables->empJob), 'EJ.Employee_Id = W.Employee_Id', array())
											
											->joinInner(array('D'=>$this->_ehrTables->designation),'EJ.Designation_Id = D.Designation_Id',
														array())
											
											->joinInner(array('G'=>$this->_ehrTables->empGrade),'G.Grade_Id = D.Grade_Id', array())
											
											->where('G.Grade_Id = ?', $gradeId)
											->where('EJ.Emp_Status = ?', "Active")
											->where($whereCond);
		
    	if (!is_null($desiId))
		{
    		$getSalaryChangeQry->where('EJ.Designation_Id = ?',$desiId);
    		$getWageChangeQry->where('EJ.Designation_Id = ?',$desiId);
    	}
    	
		$getSalaryChange = $this->_db->fetchCol($getSalaryChangeQry);
    	$getWageChange = $this->_db->fetchCol($getWageChangeQry);
    	
		return array($getSalaryChange,$getWageChange);
	}
	
	public function __destruct()
    {
        
    }	
    
}

