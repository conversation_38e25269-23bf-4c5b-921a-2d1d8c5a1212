<?php 
    $datasetupForms = $this->datasetupForms;
if($datasetupForms) {
for($k=1;$k>=0;$k--)
{
?>
<div class="col-md-12 col-xs-12 col-sm-12">
<h3><strong><?php echo ($k==1?'Mandatory':'Optional');?>  Prerequisites</strong></h3>
<div class="row m-t-12">
<?php
    $disable = '';
    $colorArr = array('bg-blue','bg-red','bg-purple','bg-orange','bg-pink','bg-yellow','bg-blue',
                      'bg-red','bg-purple','bg-orange','bg-pink','bg-yellow','bg-red','bg-purple',
                      'bg-blue','bg-orange');
    
    for($i=0;$i<count($datasetupForms);$i++)
    {
        /** Mandatory/Optional Prerequisite.
        If $k=1, Mandatory
            $k=0, Optional**/
        if($datasetupForms[$i]['Is_Required'] == $k)
        {
            if($datasetupForms[$i]['Form_Status'] == 'Completed' && $datasetupForms[$i]['Table_Content'] > 0)     
            {
                $disable = "panel";
                $icon = "Complete";
                $style = "color: green;";
                $sty = '';
                /**grid panel to redirect to the corresponding form */
                  if($datasetupForms[$i]['Form_Name']=='Employee Type')   
                 {
                   $getid = '#gridPanelEmployeeType';
                   $url = $this->baseUrl(strtolower($datasetupForms[$i]['Module_Name']).'/'.strtolower(str_replace(' ','-', $datasetupForms[$i]['Url_Form'])).'/'. $getid);   
                  }
                 elseif($datasetupForms[$i]['Form_Name']=='Leave Types')
                 {
                   $getid = '#gridPanelLeaveType';
                   $url = $this->baseUrl(strtolower($datasetupForms[$i]['Module_Name']).'/'.strtolower(str_replace(' ','-', $datasetupForms[$i]['Url_Form'])).'/'. $getid);
                }
                elseif($datasetupForms[$i]['Form_Name']=='Holiday Types')
                 {
                    $getid = '#gridPanelHoliday';
                    $url = $this->baseUrl(strtolower($datasetupForms[$i]['Module_Name']).'/'.strtolower(str_replace(' ','-', $datasetupForms[$i]['Url_Form'])).'/'. $getid);
                }
                elseif($datasetupForms[$i]['Form_Name']=='Allowance Types')
                  { 
                    $getid = '#gridPanelAllowanceTypes';
                    $url = $this->baseUrl(strtolower($datasetupForms[$i]['Module_Name']).'/'.strtolower(str_replace(' ','-', $datasetupForms[$i]['Url_Form'])).'/'. $getid);
                 }
                elseif($datasetupForms[$i]['Form_Name']=='Bonus Types')
                 {
                   $getid = '#gridPanelBonusType';
                   $url = $this->baseUrl(strtolower($datasetupForms[$i]['Module_Name']).'/'.strtolower(str_replace(' ','-', $datasetupForms[$i]['Url_Form'])).'/'. $getid);
                }
                elseif($datasetupForms[$i]['Form_Name']=='Locations')
                {          
                  $url = $this->baseUrl("v3/core-hr/locations");
                }
                elseif($datasetupForms[$i]['Form_Name']=='Grades')
                {          
                  $url = $this->baseUrl("v3/core-hr/grades");
                }
                elseif($datasetupForms[$i]['Form_Name']=='Timesheet Hours')
                {          
                  $url = $this->baseUrl("employees/timesheets");
                }
                elseif($datasetupForms[$i]['Form_Name']=='Department Hierarchy')
                {          
                  $url = $this->baseUrl("v3/core-hr/department-hierarchy");
                }
                elseif($datasetupForms[$i]['Form_Name']=='Employee Type')
                {          
                  $url = $this->baseUrl("v3/core-hr/employee-type");
                }
                else
                {          
                  $url = $this->baseUrl(strtolower($datasetupForms[$i]['Module_Name']).'/'.strtolower(str_replace(' ','-', $datasetupForms[$i]['Url_Form'])));
                }
            }
             
                
            
            else
            {
                $icon = "Incomplete";
                $style = "color: red;";
                /** The Form with Open status will be enabled
                when 1) Previous Form Status is Completed,
                     2) For Org Settings form as this is the 1st form,
                     3) When the Form doesn't have any Previous Form.**/
                if($datasetupForms[$i]['Prev_Form_Status'] == 'Completed' || $datasetupForms[$i]['Form_Name'] == 'Organization Settings'
                   || $datasetupForms[$i]['Prev_Form_Id'] == 0)
                {   
                    $disable = "panel";
                    $url = $this->baseUrl(strtolower($datasetupForms[$i]['Module_Name']).'/'.strtolower(str_replace(' ','-',$datasetupForms[$i]['Url_Form'])));
                    if($datasetupForms[$i]['Form_Name']=='Employee Type')
                    {
                      $getid = '#gridPanelEmployeeType';
                      $url = $this->baseUrl(strtolower($datasetupForms[$i]['Module_Name']).'/'.strtolower(str_replace(' ','-', $datasetupForms[$i]['Url_Form'])).'/'. $getid);
                    }
                    elseif($datasetupForms[$i]['Form_Name']=='Leave Types')
                    {
                      $getid = '#gridPanelLeaveType';
                      $url = $this->baseUrl(strtolower($datasetupForms[$i]['Module_Name']).'/'.strtolower(str_replace(' ','-', $datasetupForms[$i]['Url_Form'])).'/'. $getid);
                    }
                   elseif($datasetupForms[$i]['Form_Name']=='Holiday Types')
                    {
                       $getid = '#gridPanelHoliday';
                       $url = $this->baseUrl(strtolower($datasetupForms[$i]['Module_Name']).'/'.strtolower(str_replace(' ','-', $datasetupForms[$i]['Url_Form'])).'/'. $getid);
                    }
                   elseif($datasetupForms[$i]['Form_Name']=='Allowance Types')
                     { 
                       $getid = '#gridPanelAllowanceTypes';
                       $url = $this->baseUrl(strtolower($datasetupForms[$i]['Module_Name']).'/'.strtolower(str_replace(' ','-', $datasetupForms[$i]['Url_Form'])).'/'. $getid);
                     }
                   elseif($datasetupForms[$i]['Form_Name']=='Bonus Types')
                    {
                      $getid = '#gridPanelBonusType';
                      $url = $this->baseUrl(strtolower($datasetupForms[$i]['Module_Name']).'/'.strtolower(str_replace(' ','-', $datasetupForms[$i]['Url_Form'])).'/'. $getid);
                    }
                    elseif($datasetupForms[$i]['Form_Name']=='Locations')
                    {          
                      $url = $this->baseUrl("v3/core-hr/locations");
                    }
                    elseif($datasetupForms[$i]['Form_Name']=='Grades')
                    {          
                      $url = $this->baseUrl("v3/core-hr/grades");
                    }
                    elseif($datasetupForms[$i]['Form_Name']=='Timesheet Hours')
                    {          
                      $url = $this->baseUrl("employees/timesheets");
                    }
                    elseif($datasetupForms[$i]['Form_Name']=='Department Hierarchy')
                    {          
                      $url = $this->baseUrl("v3/core-hr/department-hierarchy");
                    }
                    elseif($datasetupForms[$i]['Form_Name']=='Employee Type')
                    {          
                      $url = $this->baseUrl("v3/core-hr/employee-type");
                    }
                    else
                  {
                    $disable = "panel";
                    $url = "javascript:void(0)";
                  }
                  $statusFlag=0;
                    // To check whether all the previous datasetup prerequisites are completed 
                    // If completed then the mask will be removed
                    for($l=0;$l<$i;$l++)
                    {
                      if($datasetupForms[$l]['Form_Status'] == 'Open' && $datasetupForms[$l]['Is_Required'] == 1)
                      {
                        $statusFlag=1;
                      }
                    }
                    if($statusFlag==0)
                    {
                      $sty='';
                    }
                    else{
                      $sty = "pointer-events: none; opacity: 0.5;";
                    }
               }
               else{
                // To set mask if previous datasetup prerequisites are Incomplete
                $sty = "pointer-events: none; opacity: 0.5;";
              }
            }
            // If the datasetup prerequisites is Optional, 
            // then mask will not be set
            if($k==0)
            {
              $sty='';
            }
            if(($datasetupForms[$i]['Form_Name']=='Work Schedule') || ($datasetupForms[$i]['Form_Name']=='Holidays')) { 
                $url = $this->baseUrl('in/'.strtolower(str_replace(' ','-', $datasetupForms[$i]['Module_Name'])).'/'.strtolower(str_replace(' ','-', $datasetupForms[$i]['Url_Form'])).'/');
            }
            elseif(($datasetupForms[$i]['Form_Name']=='Designations/Positions')) { 
                $url = $this->baseUrl('in/core-hr/designations');
            }
            elseif(($datasetupForms[$i]['Form_Name']=='Employees')) { 
                $url = $this->baseUrl('v3/my-team/team-summary');
            }
            elseif($datasetupForms[$i]['Form_Name']=='Holiday Types'){
                $url = $this->baseUrl('in/'.strtolower(str_replace(' ','-', $datasetupForms[$i]['Module_Name'])).'/'.'holidays'.'/');
            }
            elseif(($datasetupForms[$i]['Form_Name']=='Locations')) { 
              $url = $this->baseUrl('v3/core-hr/locations');
            }
            elseif(($datasetupForms[$i]['Form_Name']=='Grades')) { 
              $url = $this->baseUrl('v3/core-hr/grades');
            }
            elseif($datasetupForms[$i]['Form_Name']=='Timesheet Hours')
            {          
              $url = $this->baseUrl("employees/timesheets");
            }
            elseif($datasetupForms[$i]['Form_Name']=='Department Hierarchy')
            {          
              $url = $this->baseUrl("v3/core-hr/department-hierarchy");
            }
            elseif($datasetupForms[$i]['Form_Name']=='Employee Type')
            {          
              $url = $this->baseUrl("v3/core-hr/employee-type");
            }
            else
               $url = $this->baseUrl(strtolower($datasetupForms[$i]['Module_Name']).'/'.strtolower(str_replace(' ','-', $datasetupForms[$i]['Url_Form'])).'/');
              
        
?>             
               
                      
	<div class="col-xs-12 col-sm-6 col-md-4 col-lg-4">
		<div class="<?php echo $disable;?>" style="height:150px;<?php echo $sty;?>" id="<?php echo strtolower(str_replace(' ','-',$datasetupForms[$i]['Form_Name'])); ?>"> 
			<div class="widget-infobox" style="float: none;">
                <div class="left1" style="padding-left:5%;padding-right: 5px;">
               <a href="<?php echo $url?>"> 
                  <h3>
                        <div style="height: 40px;text-align: center;">
                            <div class="left">
                                <?php if($datasetupForms[$i]['Form_Name'] == 'Allowance Types')
                                { ?>
                                    <i class="<?php echo $colorArr[$i]; ?> hr-payroll-loan"></i>
                                <?php }
                                elseif($datasetupForms[$i]['Form_Name'] == 'Bonus Types') {
                                   ?>
                                    <i class="<?php echo $colorArr[$i]; ?> hr-payroll-bonus"></i>
                                <?php 
                                }
                                elseif($datasetupForms[$i]['Form_Name'] == 'Work Schedule') {
                                  ?>
                                   <i class="<?php echo $colorArr[$i]; ?> hr-core-hr-work-schedule"></i>
                               <?php 
                               }
                               elseif($datasetupForms[$i]['Form_Name'] == 'Holidays' || $datasetupForms[$i]['Form_Name'] == 'Holiday Types') {
                                ?>
                                 <i class="<?php echo $colorArr[$i]; ?> hr-organization-holiday-types"></i>
                              <?php 
                              }
                                else { ?>
                                    
                                  <i class="<?php echo $colorArr[$i]; ?> hr-<?php echo strtolower($datasetupForms[$i]['Module_Name']).'-'.strtolower(str_replace(' ','-',$datasetupForms[$i]['Form_Name']));  ?>"></i>
                                  <?php } ?>
                            </div>
                            <div class="right" style="padding-top: 3%;">
                                 <strong><?php echo $datasetupForms[$i]['Form_Name']; ?></strong>
                            </div>
                        </div>
                    </h3>                        
                        <div> <?php echo $datasetupForms[$i]['Description']; ?></div>
                    </a>
            
            	<div class="panel-content widget-table col-md-12">
				
                    <?php
                    if(!empty($datasetupForms[$i]['Youtube_Link']))
                    {
                    ?>
                        <div> Youtube link <a href="<?php echo $datasetupForms[$i]['Youtube_Link']; ?>"></a></div>
                    <?php }
                    if(!empty($datasetupForms[$i]['Document_Link']))
                    {
                    ?>
                         <div> Document link <a href="<?php echo $datasetupForms[$i]['Document_Link']; ?>"></a></div>
                    <?php } ?>
                </div>
                
            <a href="<?php echo $url; ?>">
			<div class="panel-content widget-table" style="padding-bottom:10% !important;">
                <div style="position: absolute;font-weight: bold;bottom: 12px;right: 11px;<?php echo $style;?>">
                <br>
                <?php echo $icon; ?>
                </br>
                </div>
			</div>
                </a>                
                </div>
			</div>
		</div>
    </div>


<?php } } } ?>

</div>
</div>

<?php
} else { ?>

<div class="col-md-12 portlets">
	<div class="txt_center">Sorry, Access Denied...</div>
</div>

<?php } ?>
