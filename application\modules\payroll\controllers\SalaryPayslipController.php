<?php
//=========================================================================================
//=========================================================================================
/* Program : SalaryPayslipController.php									   		     *
 * Property of Caprice Technologies Pvt Ltd,											 *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,										 *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies				 *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : Payslip is a a piece of paper showing the full amount of an employee’s  *
 * pay, and the money deducted as tax, pension and National insurance contributions.	 *
 * Salary payslips component manages monthly salary of all employees in the organization.*
 * Payslips can be generated on any specific date of the month. This component is also   *
 * responsible for keeping the history of past salaries of all employees. Payslip for    *
 * each month for each employee will always be stored here, which can be accessed        *
 * anytime either by the employee or the employer. 										 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Narmadha     	          Initial Version            		 *
 *  0.2        01-Apr-2014    Mahesh                  Modified Functions                 *
 *                                                    1.viewSalaryPayslip & its phtml    *
 *                                                    2.viewWagePayslip & its phtml      *
 *                                                    (for view as well as print)        *
 *                            Mahesh,Sandhosh         1.exportMonthlyPayslipAction       *
 *													  2.payslipHTML						 *
 * 													  3.exportHourlyPayslipAction        *
 *          																			 *
 *  0.3        14-May-2014    Mahesh                  Modified Function                  *
 *                                                    1.deleteSalaryPayslipAction        *
 *            										  2.deleteWagePayslipAction          *
 *																						 *
 *  0.4  	   06-Jul-2014    Mahesh				  Modified Action					 *
 *													  1.payslipMailNotificationAction 	 *
 *					                      												 *
 *  0.5		   18-Sep-2014    Mahesh				  Modified Function					 *
 *													  1.payslipHTML			 			 *
 *													  2.payslipMailNotificationAction    *
 *																						 *
 *  1.0        02-Feb-2015    Dhanabal                Changed in file for mobiles app    *
 *  1.5		   22-Apr-2016    Nivethitha			  Changed in file for bootstrap      *
 *  1.6        05-Feb-2020    Shanthi				Remove payslipMailNotificationAction */
//=========================================================================================
//=========================================================================================
class Payroll_SalaryPayslipController extends Zend_Controller_Action
{
	protected $_loginEmpUser = null;
	protected $_dbInbox = null;
	protected $_dbPayslip = null;
	protected $_dbAccessRights = null;
	protected $_payslipAccessRights = null;
	protected $_dbPersonal = null;
	protected $_loginEmpUserId = null;
	protected $_logEmpId = null;
	protected $_ehrTables = null;
	protected $_dbEmpType = null;
	protected $_dbLocation = null;
	protected $_dbDept = null;
	protected $_dbOrgSettings = null;
	protected $_inboxName = 'Inbox';
	protected $_formNameA = 'Salary Payslip';
	protected $_formNameD = 'Bimonthly Payslip';
	protected $_formNameB = 'Full & Final Settlement';
	protected $_formNameC = 'Salary Review';
	protected $_showReportCreator   = null;
	protected $_orgDetails = null;
	protected $_dbPayslipTemplate = null;
	protected $_basePath = null;
	protected $_hrappMobile = null;
	protected $_dbEmployee  = null;
	protected $_locale = null;
	
	public function init()
	{
		$this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
		// if ($this->_hrappMobile->checkAuth())
        // {
			$this->_dbCommonFun = new Application_Model_DbTable_CommonFunction();
            $userSession        = $this->_dbCommonFun->getUserDetails ();
			$this->_logEmpId    = $userSession?$userSession['logUserId']:1;
			$this->_basePath          = new Zend_View_Helper_BaseUrl();
			$this->_dbInbox    = new Employees_Model_DbTable_Inbox();
			$this->_dbLeave        = new Employees_Model_DbTable_Leave();
			$this->_dbPayslip  = new Payroll_Model_DbTable_Payslip();
			$this->_dbPrerequisite  = new Payroll_Model_DbTable_Prerequisite();
			$this->_dbPersonal = new Employees_Model_DbTable_Personal();
			$this->_dbDept     = new Organization_Model_DbTable_Department();
			$this->_dbLocation = new Organization_Model_DbTable_Location();
			$this->_dbEmpType  = new Employees_Model_DbTable_EmployeeType();
			$this->_ehrTables  = new Application_Model_DbTable_Ehr();
			$this->_dbAccessRights = new Default_Model_DbTable_AccessRights();			
			$this->_dbOrgSettings  = new Organization_Model_DbTable_OrgSettings();
			$this->_dbFinacialYr   = new Default_Model_DbTable_FinancialYear();
			$this->_dbPayslipTemplate = new Payroll_Model_DbTable_PayslipTemplate();
			$this->_payslipAccessRights   = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameA);
			$this->_fullFinalSettlementAccessRights   = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameB);
			$this->_salaryReviewAccessRights   = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameC);
			$this->_biMonthlyAccessRights   = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameD);
			$this->_payslipEmployeeAccess = $this->_payslipAccessRights['Employee'];
			$this->_fullFinalSettlementAccess = $this->_fullFinalSettlementAccessRights['Employee'];
			$this->_biMonthlyAccess = $this->_biMonthlyAccessRights['Employee'];

			$this->_dbEmployee      = new Employees_Model_DbTable_Employee();
			$this->_ehrTables 		= new Application_Model_DbTable_Ehr();
			$this->_orgCode 		= $this->_ehrTables->getOrgCode();
			$this->_dbPayout        = new Payroll_Model_DbTable_Payout();
			$this->_locale          = $this->_dbCommonFun->getEmployeeLocale($this->_logEmpId);

			 if (Zend_Registry::isRegistered('orgDetails'))
             $this->_orgDetails = Zend_Registry::get('orgDetails');
		// }
		// else
		// {
			// if (Zend_Session::namespaceIsset('lastRequest'))
			// 	Zend_Session:: namespaceUnset('lastRequest');
			
			// $session = new Zend_Session_Namespace('lastRequest');
			// $session->lastRequestUri = 'payroll/salary-payslip';
			// $this->_redirect('auth');
		// }
	}
	
	/**
	 *
	 * This action shows the total monthly salary and total wages earned by an employee for a month.
	 * Payslip details are shown based on the access rights.
	 */
	public function indexAction()
	{
		$checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

		if ($checkSessionAuth)
		{
			$this->_helper->layout()->disableLayout()->setLayout('admin_layout');
		
			$this->view->formNameA = $this->_formNameA;
			
			$dbPersonal = new Employees_Model_DbTable_Personal();
			$payrollComment = new Payroll_Model_DbTable_PayrollComment();
			
			$this->view->logEmpId	  = $this->_logEmpId;
			$this->view->formId 	  = $payrollComment->getFormId($this->_formNameA);
			$this->view->location     = $this->_dbLocation->getLocationPair();
			$this->view->department   = $this->_dbDept->getDeptPairs();
			$this->view->employeeType = $this->_dbEmpType->getEmpTypePairs();
			$this->view->assessMnth   = $this->_ehrTables->assessMonths();
			$this->view->businessUnit = $this->_dbDept->getbusinessUnitPair();


			$financialClosure = $this->_ehrTables->getFinancialClosure();
			$assessmentYr = $this->_dbFinacialYr->getAssessmentYr();
			$orgCode = $this->_ehrTables->getOrgCode();
			$dbOrgDetail = new Organization_Model_DbTable_OrgSettings();
			$showReporCreator = $dbOrgDetail->viewOrgDetail($orgCode);
			$this->view->ShowReportCreator = $showReporCreator['Show_Report_Creator'];
			$this->view->displayPayslipAddress = $this->_orgDetails['Display_Payslip_Address'];
			$this->view->financialClosureTracking = $this->_orgDetails['Financial_Closure_Tracking'];
			$this->view->fieldForce               = $this->_orgDetails['Field_Force'];
			$this->view->deptHierarchy   = $this->_dbDept->getDepartmentTypes();
		  
			$this->view->payslipMonthPair = $this->_dbCommonFun->getPayslipMonth();

			$this->view->assessmentYr = $this->_dbFinacialYr->getAssessmentYr();
			
			$this->view->customField = $this->_ehrTables->getCustomFields();
			$this->view->monthYear = $this->_dbCommonFun->getPayslipMonthYear();
			$this->view->monthlySalaryImg  = $this->_basePath->baseUrl('images/month.png');
			$this->view->hourlySalaryImg   = $this->_basePath->baseUrl('images/hourly.png');
			$this->view->warningImg        = $this->_basePath->baseUrl('images/warning.png');
			$this->view->estimatedAlternateImg     = $this->_basePath->baseUrl('images/estimated-payroll.png');
			$this->view->estimatedImg   = $this->_basePath->baseUrl('images/estimated-payroll.webp');
			$this->view->monthlyBagAlternateImg   = $this->_basePath->baseUrl('images/monthly-money-bag.png');
			$this->view->monthlyMoneyBag    = $this->_basePath->baseUrl('images/monthly-money-bag.webp');
			$this->view->hourlyBagAlternateImg   = $this->_basePath->baseUrl('images/hourly-money-bag.png');
			$this->view->hourlyMoneyBag    = $this->_basePath->baseUrl('images/hourly-money-bag.webp');
			$this->view->maxMonthSalary    = $this->_dbPayslip->getmaxMonthlyPayslipSalary('Monthly');
			$this->view->filterImg   = $this->_basePath->baseUrl('images/filter.png');
			$this->view->payslipUser =  array('Is_Manager'=>$this->_payslipAccessRights['Employee']['Is_Manager'],
											  'View'=>$this->_payslipAccessRights['Employee']['View'],
											  'Delete'=>$this->_payslipAccessRights['Employee']['Delete'],
											  'Op_Choice'=>$this->_payslipAccessRights['Employee']['Optional_Choice'],
											  'Admin'=>$this->_payslipAccessRights['Admin'],
											  'Employee_Name' => $this->_dbPersonal->employeeId($this->_logEmpId));
			$this->view->fullFinalSettlementUser = array('View' => $this->_fullFinalSettlementAccess['View']);
			$this->view->biMonthlyUser = array('View' => $this->_biMonthlyAccess['View']);
			$this->view->serviceProviderAdmin = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, 'Service Provider Admin');											  
			$this->view->serviceProvider      = $this->_dbEmployee->getEmployeeServiceProviderList($this->_logEmpId);
			$this->view->serviceProviderId    = $this->_dbCommonFun->getEmployeeServiceProviderId($this->_logEmpId,1);
			$this->view->accountNumberList    = $this->_dbPayout->getCbAccountNumbers();
			$this->view->salaryReviewUser = array('Op_Choice' => $this->_salaryReviewAccessRights['Employee']['Optional_Choice']);
			$this->view->payslipReviewProcess = $this->_orgDetails['Payslip_Review_Process'];
			$this->view->payrollPeriod 		  = $this->_orgDetails['Payroll_Period'];

		} else {
			$this->_redirect('auth');
		}
	}
	
	/**
	 * This action get the hourly wages payslip details from the dataTable and
	 * will show them in a grid
	 */
	public function hourlyWagesPayslipAction()
	{
		$this->_helper->layout()->disableLayout();
		$checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();
		if ($checkSessionAuth)
		{
			if (isset($_SERVER['HTTP_REFERER']))
			{
				$ajaxContext = $this->_helper->getHelper('AjaxContext');
				$ajaxContext->addActionContext('hourly-wages-payslip', 'json')->initContext();
				
				if ($this->_payslipEmployeeAccess['View'] == 1)
				{
					$sortField = $this->_getParam('iSortCol_0', null);
					$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
					
					$sortOrder = $this->_getParam('sSortDir_0', null);
					$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
					
					$page = $this->_getParam('iDisplayStart', null);
					$page = isset($page) ? intval($page) : 0;
					$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
					
					$rows = $this->_getParam('iDisplayLength', null);
					$rows = isset($rows) ? intval($rows) : 10;
					$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);

					$searchAll = $this->_getParam('sSearch', null);
					$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
					
					$employeeName = $this->_getParam('employeeName',null);
					$employeeName = filter_var($employeeName, FILTER_SANITIZE_STRIPPED);
								
					$salaryMonth = $this->_getParam('salaryMonth', null);
					$salaryMonth = filter_var($salaryMonth, FILTER_SANITIZE_STRIPPED);
					
					$totAmountStart = $this->_getParam('totalSalaryStart', null);
					$totAmountStart = filter_var($totAmountStart, FILTER_SANITIZE_STRIPPED);
					
					$totAmountEnd = $this->_getParam('totalSalaryEnd', null);
					$totAmountEnd = filter_var($totAmountEnd, FILTER_SANITIZE_STRIPPED);

					$paymentStatus = $this->_getParam('paymentStatus',null);
					$paymentStatus = filter_var($paymentStatus, FILTER_SANITIZE_STRIPPED);
					
					$serviceProviderId = $this->_getParam('serviceProviderId', null);
					$serviceProviderId = filter_var($serviceProviderId, FILTER_SANITIZE_NUMBER_INT);
					
					$searchArray  = array('Employee_Name'      	=> $employeeName,
										'Salary_Month'	   	  	=> $salaryMonth,
										'Total_Salary_Start' 	=> $totAmountStart,
										'Total_Salary_End'   	=> $totAmountEnd,
										'Payment_Status'   		=> $paymentStatus,
										'Service_Provider_Id'	=> $serviceProviderId);
					
					$userDetailsArray = array('Is_Manager' => $this->_payslipEmployeeAccess['Is_Manager'],
											'Op_Choice'  => $this->_payslipEmployeeAccess['Optional_Choice'],
											'Delete'     => $this->_payslipEmployeeAccess['Delete'],
											'Admin'      => $this->_payslipAccessRights['Admin'],
											'LogId'      => $this->_logEmpId,
											'Form_Name'  => $this->_formNameA);
					$this->view->result = $this->_dbPayslip->showHourlyPayslip($page, $rows, $sortField, $sortOrder, $searchAll,$searchArray,$userDetailsArray);
				}
			}
			else
			{
				$this->_helper->redirector('index', 'salary-payslip', 'payroll');
			}
		}
		else 
		{
			$this->_redirect('auth');
		}
	}
	
	/**
	 * This action get the monthly salary payslip details from the dataTable and
	 * will show them in a grid
	 */
	public function monthlySalaryPayslipAction()
	{
		$this->_helper->layout()->disableLayout();
		$checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();
		if ($checkSessionAuth)
		{
			if (isset($_SERVER['HTTP_REFERER']))
			{
				$ajaxContext = $this->_helper->getHelper('AjaxContext');
				$ajaxContext->addActionContext('monthly-salary-payslip', 'json')->initContext();
				
				if ($this->_payslipEmployeeAccess['View'] == 1)
				{
					$sortField = $this->_getParam('iSortCol_0', null);
					$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
					
					$sortOrder = $this->_getParam('sSortDir_0', null);
					$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
					
					$page = $this->_getParam('iDisplayStart', null);
					$page = isset($page) ? intval($page) : 0;
					$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
					
					$rows = $this->_getParam('iDisplayLength', null);
					$rows = isset($rows) ? intval($rows) : 10;
					$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);

					$searchAll = $this->_getParam('sSearch', null);
					$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
					
					$employeeName = $this->_getParam('employeeName',null);
					$employeeName = filter_var($employeeName, FILTER_SANITIZE_STRIPPED);
								
					$salaryMonth = $this->_getParam('salaryMonth', null);
					$salaryMonth = filter_var($salaryMonth, FILTER_SANITIZE_STRIPPED);
					
					$totAmountStart = $this->_getParam('totalSalaryStart', null);
					$totAmountStart = filter_var($totAmountStart, FILTER_SANITIZE_STRIPPED);
					
					$totAmountEnd = $this->_getParam('totalSalaryEnd', null);
					$totAmountEnd = filter_var($totAmountEnd, FILTER_SANITIZE_STRIPPED);
					
					$employeeType = $this->_getParam('employeeType', null);
					$employeeType = filter_var($employeeType, FILTER_SANITIZE_STRIPPED);
					
					$location = $this->_getParam('location', null);
					$location = filter_var($location, FILTER_SANITIZE_STRIPPED);
					
					$department = $this->_getParam('department', null);
					$department = filter_var($department, FILTER_SANITIZE_STRIPPED);

					$paymentStatus = $this->_getParam('paymentStatus',null);
					$paymentStatus = filter_var($paymentStatus, FILTER_SANITIZE_STRIPPED);

					$serviceProviderId = $this->_getParam('serviceProviderId', null);
					$serviceProviderId = filter_var($serviceProviderId, FILTER_SANITIZE_NUMBER_INT);
					
					$searchArray  = array('Employee_Name'      	=> $employeeName,
										'Salary_Month'	   		=> $salaryMonth,
										'Total_Salary_Start' 	=> $totAmountStart,
										'Total_Salary_End'   	=> $totAmountEnd,
										'Employee_Type'     	=> $employeeType,
										'Location'           	=> $location,
										'Department'         	=> $department,
										'Payment_Status'		=> $paymentStatus,
									    'Service_Provider_Id'	=> $serviceProviderId);
					
					$userDetailsArray = array('Is_Manager' => $this->_payslipEmployeeAccess['Is_Manager'],
											'Op_Choice'  => $this->_payslipEmployeeAccess['Optional_Choice'],
											'Delete'     => $this->_payslipEmployeeAccess['Delete'],
											'Admin'      => $this->_payslipAccessRights['Admin'],
											'LogId'      => $this->_logEmpId,
											'Form_Name'  => $this->_formNameA);
					
					$this->view->result = $this->_dbPayslip->showSalaryPayslip($page, $rows, $sortField, $sortOrder, $searchAll,
																								$searchArray,$userDetailsArray);
				}
			}
			else
			{
				$this->_helper->redirector('index', 'salary-payslip', 'payroll');
			}
		}
		else 
		{
			$this->_redirect('auth');
		}
	}
	
	/**
	 * This action is to verify and generate payslip for monthly salaried employee and hourly wages employee
	 */
	public function generatePayslipAction()
	{
		$layout = $this->_helper->layout();
		$layout->disableLayout('layout');
		
		if(isset($_SERVER['HTTP_REFERER']))
		{
			$layout->setLayout('tab_layout');

			if($this->_checkSession)
			{
				if($this->_payslipAccessRights['Employee']['Optional_Choice']==1)
				{
					// to check whether the table is locked
					$getThrowSession = $this->_dbPayslip->getPayslipEmpId(array($this->_ehrTables->monthlyPayslip,$this->_ehrTables->wagePayslip));
					
					if(!empty($getThrowSession))
					{
						foreach ($getThrowSession as $throwSession)
						{
							$getEmpId[] = $throwSession['Session_Id'];
							$getTable[] = $throwSession['Table_Name'];
							$getThrowTable[] = array($throwSession['Session_Id'], $throwSession['Table_Name']);
						}
					}
					
					if (count($getThrowSession) == 2 && !(in_array($this->_logEmpId, $getEmpId))) // will show message if two user access who has not locked the form
					{
						$editEmpNameA = $this->_dbPersonal->employeeName($getEmpId[0]);
						$editEmpNameB = $this->_dbPersonal->employeeName($getEmpId[1]);
						$this->view->access = $editEmpNameA['Employee_Name'] . ' and '. $editEmpNameB['Employee_Name'].' are accessing this form. Please Wait...';
					}
					else
					{
						// 	lock form and table if user chooses monthly salary option
						if(empty($getThrowSession) || (count($getThrowSession) == 1 && (!in_array($this->_logEmpId, $getEmpId) && in_array($this->_ehrTables->wagePayslip, $getTable)))
								|| ((count($getThrowSession) == 1 || count($getThrowSession) == 2) && in_array(array($this->_logEmpId,$this->_ehrTables->monthlyPayslip), $getThrowTable)))
						{
							$setPayslipLock = $this->_dbAccessRights->setLockFlag($this->_logEmpId, '', $this->_ehrTables->monthlyPayslip, '');
						}
						// lock form and table if user chooses hourly wages option
						else if(count($getThrowSession) == 1 && !in_array($this->_logEmpId, $getEmpId) && in_array($this->_ehrTables->monthlyPayslip, $getTable)
								|| ((count($getThrowSession) == 1 || count($getThrowSession) == 2) && in_array(array($this->_logEmpId,$this->_ehrTables->wagePayslip), $getThrowTable)))
						{
							$setPayslipLock = $this->_dbAccessRights->setLockFlag($this->_logEmpId, '', $this->_ehrTables->wagePayslip, '');
						}
							
						$location = $this->_dbLocation->getLocationPair();
						$department = $this->_dbDept->getDeptPairs();
						$employeeType = $this->_dbEmpType->getEmpTypePairs();
						
						$payslipForm = new Payroll_Form_GeneratePayslip();
						
						$currdate = date('Y-m');
						$currentDate = explode('-',$currdate);
						$getSalaryDay = $this->_dbPayslip->getSalaryDay($currentDate[1],$currentDate[0]);
						$salaryDay = $getSalaryDay['Salary_Date'];
						
						if (strtotime(date('Y-m-d')) >= strtotime($salaryDay))
						{
							$disableMonth = $currdate;
							$payslipForm->Payslip_Month->setValue( date('M,Y',strtotime(date("Y-m", strtotime($currdate)) . " - 1 month")));
						}
						else
						{
							$disableMonth = date('Y-m',strtotime(date("Y-m", strtotime($currdate)) . " - 1 month"));
							$payslipForm->Payslip_Month->setValue( date('M,Y',strtotime(date("Y-m", strtotime($currdate)) . " - 2 month")));
						}
						
						$payslipForm->Salary_Day->setValue($disableMonth);
						
						$SalaryTypebased = $this->_getParam('salaryType', null);
						
						if($SalaryTypebased == 'monthly')
						{
							$payslipForm->Payslip_Type->addMultiOptions(array('a'=>'Monthly Salary', 'b'=>'Hourly Wages'));	
						}
						else
						{
							$payslipForm->Payslip_Type->addMultiOptions(array('b'=>'Hourly Wages', 'a'=>'Monthly Salary'));
						}						
						
						// if session exists for single or both tables and if logged in employee is the session employee then he can access only monthly salary
						if(count($getThrowSession) == 2 && in_array(array($this->_logEmpId,$this->_ehrTables->monthlyPayslip), $getThrowTable)
								||(count($getThrowSession) ==1 && in_array($this->_ehrTables->wagePayslip, $getTable)))
						{							
							$payslipForm->Payslip_Type->addMultiOptions(array('a'=>'Monthly Salary', 'b'=>'Hourly Wages'));
						}
						// or hourly wages
						else if((count($getThrowSession) == 2 && in_array(array($this->_logEmpId,$this->_ehrTables->wagePayslip), $getThrowTable))
								|| (count($getThrowSession) == 1 && in_array($this->_ehrTables->monthlyPayslip, $getTable)))
						{							
							$payslipForm->Payslip_Type->addMultiOptions(array('b'=>'Hourly Wages', 'a'=>'Monthly Salary'));
						}
						// otherwise both
						else
						{
							$payslipForm->Payslip_Type->addMultiOptions(array('a'=>'Monthly Salary', 'b'=>'Hourly Wages'));
						}
						if(!empty($employeeType))
							$payslipForm->Emp_Type->addMultiOptions($employeeType);
						if(!empty($location))
							$payslipForm->Emp_Location->addMultiOptions($location);
						if(!empty($department))
							$payslipForm->Emp_Dept->addMultiOptions($department);
						$this->view->payslip = $payslipForm;
					}
				}
				else
				{
					$this->view->access = 'Sorry, Access Denied...';
				}
			}
			else
			{
				$this->_redirect('auth');
			}
		}
		else
		{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}
	}
	
	/**
	 * view the monthly salary payslip details of an employee and also for print preview.
	 */
	public function viewSalaryPayslipAction()
	{
		$this->_helper->layout()->disableLayout();
        // if (isset($_SERVER['HTTP_REFERER']))
        // {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('view-salary-payslip', 'json')->initContext();
			
			$payslipId = $this->_getParam('payslipId', null);
			$payslipId = filter_var($payslipId, FILTER_SANITIZE_NUMBER_INT);

			$calledFrom = $this->_getParam('calledFrom', null);
			$calledFrom = filter_var($calledFrom, FILTER_SANITIZE_STRIPPED);
			
			if(!empty($payslipId))
			{
				if($this->_payslipEmployeeAccess['View']==1 || $calledFrom === 'payout')
				{
					try {
					
						$orgCode = $this->_ehrTables->getOrgCode();

						$payslip = $this->_dbPayslip->viewMonthlySalary($payslipId);

						$payslipDetails = $this->_dbPayslip->getMonthlyPayslipDetails($payslipId, $orgCode, $payslip);

						// Free memory immediately after use
						unset($payslip);

						$payslipNumberConversion = $this->_dbPayslip->salaryPayslipAmountConversion($payslipDetails, $this->_locale);

						// Free memory before sending response
						unset($payslipDetails);

						$this->view->result = $payslipNumberConversion;

					} catch (Exception $e) {
						error_log("Memory error in viewSalaryPayslipAction: " . $e->getMessage());
						$this->view->result = array(
							'success' => false,
							'msg' => 'Unable to load payslip due to memory constraints. Please try again.',
							'type' => 'error',
							'error' => $e->getMessage()
						);
					}
				}
			}
		// }
		// else
		// {
			// $this->_helper->redirector('index', 'salary-payslip', 'payroll');
		// }
	}

	/**
	 * view the monthly salary payslip details of an employee and also for print preview.
	 */
	public function viewBwdSalaryPayslipAction()
	{
		$this->_helper->layout()->disableLayout();
        // if (isset($_SERVER['HTTP_REFERER']))
        // {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('view-bwd-salary-payslip', 'json')->initContext();
			
			$payslipId = $this->_getParam('payslipId', null);
			$payslipId = filter_var($payslipId, FILTER_SANITIZE_NUMBER_INT);

			$calledFrom = $this->_getParam('calledFrom', null);
			$calledFrom = filter_var($calledFrom, FILTER_SANITIZE_STRIPPED);
			
			if(!empty($payslipId))
			{
				if($this->_biMonthlyAccess['View']==1 || $calledFrom === 'payout')
				{
					$orgCode = $this->_ehrTables->getOrgCode();
					$payrollPeriod = strtolower($this->_orgDetails['Payroll_Period']);
					$payslip = $this->_dbPayslip->viewMonthlySalary($payslipId,false,NULL,$payrollPeriod);
										
					$payslipDetails = $this->_dbPayslip->getMonthlyPayslipDetails($payslipId,$orgCode,$payslip);
					$payslipNumberConversion = $this->_dbPayslip->salaryPayslipAmountConversion($payslipDetails,$this->_locale);
					$this->view->result = $payslipNumberConversion;
				}
			}
		// }
		// else
		// {
			// $this->_helper->redirector('index', 'salary-payslip', 'payroll');
		// }
	}

	
	
	/**
	 * view the hourly wages payslip details of an employee and also for print preview.
	 */
	public function viewWagePayslipAction()
	{
        $this->_helper->layout()->disableLayout();
		
		// if(isset($_SERVER['HTTP_REFERER']) || $_COOKIE['isNativeMobileApp'] == 1)
		if(1)
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('view-wage-payslip', 'json')->initContext();
                
            $payslipId = $this->_getParam('payslipId', null);
            $payslipId = filter_var($payslipId, FILTER_SANITIZE_NUMBER_INT);
                
			$calledFrom = $this->_getParam('calledFrom', null);
			$calledFrom = filter_var($calledFrom, FILTER_SANITIZE_STRIPPED);

            if(!empty($payslipId))
            {
                if($this->_payslipEmployeeAccess['View']==1 || $calledFrom === 'payout')
				{
                    $orgCode = $this->_ehrTables->getOrgCode();
                    
                    $payslip = $this->_dbPayslip->viewHoulryWages($payslipId,$orgCode);
                    
                    //adding Total Hourly wage and Total overtime wage as Wage Incentives
                    if($payslip['Payslip']['Total_OvertimeWages'] > 0)
                    {
                        array_unshift($payslip['Incentive'],array('Incentive_Name'=>'Total Overtime Wages','Incentive_Amount'=>$payslip['Payslip']['Total_OvertimeWages']));
                    }
                    array_unshift($payslip['Incentive'],array('Incentive_Name'=>'Total Hourly Wages','Incentive_Amount'=>$payslip['Payslip']['Total_HourlyWages']));				
					
                    $this->view->result = $payslip;
                    $this->view->currency = $this->_dbPersonal->currencySymbol($payslip['Payslip']['Employee_Id']);
                }
            }
		}
		else
		{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}
	}
	
	//version:0.3 =>changed for deleting multiple rows  at a time
	/**
	* delete monthly salary payslip details of an employee based on the maximum month.
	*/
	public function deleteSalaryPayslipAction()
	{
		$this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$payslipId= $this->_getParam('payslipId', null);			
			
			if(!empty($payslipId) && $payslipId > 0 && $this->_payslipEmployeeAccess['Delete']==1)
			{
				$ajaxContext = $this->_helper->getHelper('AjaxContext');
				$ajaxContext->addActionContext('delete-salary-payslip', 'json')->initContext();

				$fiscalMonthArray = array();

				if(count($payslipId) > 0){
					if (Zend_Registry::isRegistered('orgDetails')){
						$dbFinancialYr = new Default_Model_DbTable_FinancialYear();
						/** Get the current financial year fiscal months in an array */
						$fiscalMonthArray = $dbFinancialYr->getFiscalMonthYear(null,$this->_orgDetails['Assessment_Year']);

						$payslipDetails = $this->_dbPayslip->getPayslipDeleteDetails($payslipId);
						$employeeRecoveryRequestDetails = $this->_dbPayslip->getDeductionMonthLOPRecovery($payslipDetails);
					}
				}
				for($z=0;$z<count($payslipId);$z++)
				{
					$componentDetails = array(
						'lopRecoveryRequest' => $employeeRecoveryRequestDetails,
					);
					$payslip = $this->_dbPayslip->salaryDelete($payslipId[$z], $this->_logEmpId,$fiscalMonthArray,$componentDetails);
				}

				$this->view->result = $payslip;
			}
		}
		else
		{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}
	}


	public function deleteBwdSalaryPayslipAction()
	{
		$this->_helper->layout()->disableLayout();
    	if (isset($_SERVER['HTTP_REFERER']))
        {
			if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				$ajaxContext = $this->_helper->getHelper('AjaxContext');
				$ajaxContext->addActionContext('delete-bwd-salary-payslip', 'json')->initContext();
				$payslipId = array_map('intval', $formData['payslipId']);
				// $payslipId= $this->_getParam('payslipId', null);
				if(!empty($payslipId) && $payslipId > 0 && $this->_biMonthlyAccess['Delete']==1)
				{
					$fiscalMonthArray = array();
					$monthlyPayslipDelete = array();
					$monthlyPayslipDelete['success'] = true;
					if(count($payslipId) > 0){
						if (Zend_Registry::isRegistered('orgDetails')){
							$dbFinancialYr = new Default_Model_DbTable_FinancialYear();
							/** Get the current financial year fiscal months in an array */
							$fiscalMonthArray = $dbFinancialYr->getFiscalMonthYear(null,$this->_orgDetails['Assessment_Year']);
							
							//based on bwdPayslipId get the equivalent monthlyPayslipId for that employee
							$bwdPayslipDetails = $this->_dbPayslip->getBwdPayslipDeleteDetails($payslipId);
							if(!empty($bwdPayslipDetails))
							{
								$bwdemployeeRecoveryRequestDetails = $this->_dbPayslip->getDeductionMonthLOPRecovery($bwdPayslipDetails);
								$bwdPayslipDetailsByPayslipId = $this->_dbCommonFun->organizeDataByEmployeeIdAndDate($bwdPayslipDetails,'bwdPayslipId');
								
								//getLopRecoveryDetails for that monthly salary employee
								$monthlyPayslipIds = array_column($bwdPayslipDetails, 'monthlyPayslipId');
								if(!empty($monthlyPayslipIds)){
									$payslipDetails 				= $this->_dbPayslip->getPayslipDeleteDetails($monthlyPayslipIds);
									if(!empty($payslipDetails))
									{
										$employeeRecoveryRequestDetails = $this->_dbPayslip->getDeductionMonthLOPRecovery($payslipDetails);
									}
								}
							}
							
						}
					}

					for($z=0;$z<count($payslipId);$z++)
					{
						if(!empty($bwdPayslipDetailsByPayslipId))
						{
							$monthlyPayslipDetails  = $this->_dbCommonFun->ensureArray($bwdPayslipDetailsByPayslipId,$payslipId[$z]);
							if(!empty($monthlyPayslipDetails))
							{
								if(!empty($monthlyPayslipDetails[0]['monthlyPayslipId']))
								{
									$monthlyComponentDetails = array('lopRecoveryRequest' => $employeeRecoveryRequestDetails);
									$monthlyPayslipDelete = $this->_dbPayslip->salaryDelete($monthlyPayslipDetails[0]['monthlyPayslipId'], $this->_logEmpId,$fiscalMonthArray,$monthlyComponentDetails);
								}
							}
						}
					
						//monthly payslip is exist needs to be deleted before the bwd payslip is deleted
						if(!empty($monthlyPayslipDelete) && $monthlyPayslipDelete['success'])
						{
							$componentDetails = array('lopRecoveryRequest' => $bwdemployeeRecoveryRequestDetails);
							$payslip = $this->_dbPayslip->bwdSalaryDelete($payslipId[$z], $this->_logEmpId,$fiscalMonthArray,$componentDetails);
						}	
					}
					$this->view->result = $payslip;
				}
				else
				{
					$this->view->result = array('success' =>false, 'msg'=>"payslip id should not be empty", 'type'=>'info');
				}
			}
		}
	}
	
	//version:0.3 =>changed for deleting multiple rows  at a time
	/**
	* delete hourly wages payslip details of an employee based on the maximum month.
	*/
	public function deleteWagePayslipAction()
	{
		$this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {   
			$payslipId= $this->_getParam('payslipId', null);			
			
			if(!empty($payslipId) && $payslipId > 0 && $this->_payslipEmployeeAccess['Delete']==1)
			{
				$ajaxContext = $this->_helper->getHelper('AjaxContext');
				$ajaxContext->addActionContext('delete-wage-payslip', 'json')->initContext();
				
				$fiscalMonthArray = array();

				if(count($payslipId) > 0){
					if (Zend_Registry::isRegistered('orgDetails')){
						$dbFinancialYr = new Default_Model_DbTable_FinancialYear();
						/** Get the current financial year fiscal months in an array */
						$fiscalMonthArray = $dbFinancialYr->getFiscalMonthYear(null,$this->_orgDetails['Assessment_Year']);
					}
				}

				for($z=0;$z<count($payslipId);$z++)
				{
					$payslip = $this->_dbPayslip->wagesDelete($payslipId[$z], $this->_logEmpId, $fiscalMonthArray);
				}
				
				$this->view->result = $payslip;				
			}
		}
		else
		{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}		
	}
	
	/**
	 * verify and generates the payslip for monthly salaries and hourly wages employee
	 * based on the payslip month, location, employee type and department.
	 */
	public function verifyPayslipAction()
	{
		$layout = $this->_helper->layout();
		$layout->disableLayout('layout');
		if(isset($_SERVER['HTTP_REFERER']))
		{
			if($this->_checkSession)
			{
					
				$layout->setLayout('tab_layout');
				$empType = $this->_getParam('type', null);
				$empType = filter_var($empType, FILTER_SANITIZE_STRIPPED);
				$empLocation = $this->_getParam('location', null);
				$empdept = $this->_getParam('dept', null);
				$payslipMonth = $this->_getParam('month', null);
				$empLocation = filter_var($empLocation, FILTER_SANITIZE_STRIPPED);
				$empdept = filter_var($empdept, FILTER_SANITIZE_STRIPPED);
				$payslipMonth = filter_var($payslipMonth, FILTER_SANITIZE_STRIPPED);
					
				if($this->_payslipAccessRights['Employee']['Optional_Choice']==1)
				{
					$payslipForm = new Payroll_Form_GeneratePayslip();
					$payslipMon = explode(',', $payslipMonth);
					$payslipYr = date('Y', strtotime($payslipMon[0].$payslipMon[1]));
					$payslipMon = date('n', strtotime($payslipMon[0].$payslipMon[1]));
					$getSalaryDay = $this->_dbPayslip->getSalaryDay($payslipMon, $payslipYr);
					$salaryDay = $getSalaryDay['Last_SalaryDate'];
					//current date should greater than last salary date
					//if(strtotime(date('Y-m-d')) >= strtotime($salaryDay))
					if(strtotime($salaryDay))
					{
						// to check whether table is locked
						$getThrowSession = $this->_dbPayslip->getPayslipEmpId(array($this->_ehrTables->monthlyPayslip,$this->_ehrTables->wagePayslip));
						if(!empty($getThrowSession))
						{
							foreach ($getThrowSession as $throwSession)
							{
								$getEmpId[] = $throwSession['Session_Id'];
								$getTable[] = $throwSession['Table_Name'];
								$getThrowTable[] = array($throwSession['Session_Id'], $throwSession['Table_Name']);
							}
						}
						// will show message if two user access who has not locked the form
						if(count($getThrowSession) == 2 && !(in_array($this->_logEmpId, $getEmpId)))
						{
							$editEmpNameA = $this->_dbPersonal->employeeName($getEmpId[0]);
							$editEmpNameB = $this->_dbPersonal->employeeName($getEmpId[1]);
							$this->view->access = $editEmpNameA['Employee_Name'] . ' and '. $editEmpNameB['Employee_Name'].' are accessing this form. Please Wait...';
						}
						else
						{
							$this->view->verifyPayslip = $empType;
							$this->view->payslipMonth = $payslipMonth;
							$this->view->dept = $empdept;
							$this->view->location = $empLocation;
							$salaryMonth = explode(',', $payslipMonth);
							$salaryMonth = date('n,Y', strtotime($salaryMonth[0].$salaryMonth[1]));
							$location = explode(',', $empLocation);
							$dept = explode(',', $empdept);
							if(!empty($empType))
							{
								$employeetype = explode(',', $empType);
							}
						}
					}
					else
					{
						$this->view->access = 'Please enter a valid month...';
					}
				}
				else
				{
					$this->view->access = 'Sorry, Access Denied...';
				}
			}
			else
			{
				$this->_redirect('auth');
			}
		}
		else
		{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}
	}
	
	/**
	 * This action get the payslip detail from the dataTable by the payslip month, location, employee type and department
	 * and will show them in a grid.
	 */
	public function verifySalaryWagePayslipAction()
	{
		$this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('verify-salary-wage-payslip', 'json')->initContext();
			
            if ($this->_payslipEmployeeAccess['View'] == 1)
            {
				$formData = $this->getRequest()->getPost();
				$sortField = $this->_getParam('iSortCol_0', null);
                $sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
                
                $sortOrder = $this->_getParam('sSortDir_0', null);
                $sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
                
                $page = $this->_getParam('iDisplayStart', null);
                $page = isset($page) ? intval($page) : 0;
                $page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
                
                $rows = $this->_getParam('iDisplayLength', null);
                $rows = isset($rows) ? intval($rows) : 10;
                $rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
				
				$payslipType = $this->_getParam('payslipType',null);
				$payslipType = filter_var($payslipType, FILTER_SANITIZE_STRIPPED);
							
				$salaryMonth = $this->_getParam('salaryMonth', null);
				$salaryMonth = filter_var($salaryMonth, FILTER_SANITIZE_STRIPPED);

				$serviceProviderId = $this->_getParam('serviceProviderId', null);
				$serviceProviderId = filter_var($serviceProviderId, FILTER_SANITIZE_NUMBER_INT);
				
				$location   = $formData['location'];
				$department = $formData['department'];

				/** if dept id is parent then get its child also else pass dept Id**/
				$department=  $this->_dbPrerequisite->getDepartmentDetails($department);
				
				$payMonth = explode('-', urldecode($salaryMonth));
				$payslipMonth = round($payMonth[1]).','.$payMonth[0];
				
				$month = array('1'=>'Jan', '2'=>'Feb', '3'=>'Mar', '4'=>'Apr', '5'=>'May', '6'=>'Jun', '7'=>'Jul',
								'8'=>'Aug', '9'=>'Sep', '10'=>'Oct', '11'=>'Nov', '12'=>'Dec');
				
				$searchArray  = array('Payslip_Type'  => $payslipType,
										'Salary_Month'  => $payslipMonth,										  
										'Location'      => $location,
										'Department'    => $department,
										'serviceProviderId' => $serviceProviderId,
										'PayMonth'	  => $month[round($payMonth[1])].','.$payMonth[0]);

				$this->view->result = $this->_dbPayslip->verifyHourlyPayslip($page, $rows, $sortField, $sortOrder,$searchArray);
			}
		}
		else
		{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}		
	}
	
	/**
	 * This action show all the payment details for each employee.
	 */
	public function payslipDetailedViewAction()
	{
		$this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('payslip-detailed-view', 'json')->initContext();
			
            if ($this->_payslipEmployeeAccess['View'] == 1)
            {
				$payslipId = $this->_getParam('payslipId', null);
				$param = $this->_getParam('param', null);
	
				if (!empty($payslipId) && $param == 'HourlyWages')
				{	
					$this->view->result = $this->_dbPayslip->hourlyWageDetail($payslipId);
				}
			}
		}
		else
		{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}
	}
	
	/**
	 * After verification the payslip can be generated based on month, location, department and employee type
	 */
	public function salaryWagePayslipAction()
	{					
		$this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('salary-wage-payslip', 'json')->initContext();
			
            if ($this->_payslipEmployeeAccess['Optional_Choice'] == 1)
            {
					$empType     = $this->_getParam('type', null);
					$empLocation = $this->_getParam('location', null);
					$empdept     = $this->_getParam('dept', null);
					$payslipMonth = $this->_getParam('month', null);
					$empIdList   = $this->_getParam('employeeId', null);
					$serviceProviderId   = $this->_getParam('serviceProviderId', null);
					$getEmpId = array();
					$getTable = array();
					$getThrowTable = array();
					if(!empty($empLocation) && !empty($empdept) && !empty($payslipMonth))
					{
						$salaryMonth = $payslipMonth;	
						$payslipMonth   = explode('-', $payslipMonth);
						$payslipMonth   = round($payslipMonth[1]).','.$payslipMonth[0];
						$empLocation = explode(',', $empLocation);
						$empdept = explode(',', $empdept);

     					/** if dept id is parent then get its child also else pass dept Id**/
						$empdept=  $this->_dbPrerequisite->getDepartmentDetails($empdept);
							
						if(!empty($empType))
						{
							$empType = explode(',', $empType);
						}

						if(strtolower($this->_orgDetails['Payroll_Period'])=='bimonthly')
						{
							$payPeriod = $this->_getParam('payPeriod', null);
							$payPeriod = filter_var($payPeriod, FILTER_SANITIZE_STRIPPED);
						}
						else
						{
							$payPeriod = '';
						}
						// to check whether table is locked
						$getThrowSession = $this->_dbPayslip->getPayslipEmpId(array($this->_ehrTables->monthlyPayslip,$this->_ehrTables->wagePayslip));
						if(!empty($getThrowSession))
						{
							foreach ($getThrowSession as $throwSession)
							{
								$getEmpId[] = $throwSession['Session_Id'];
								$getTable[] = $throwSession['Table_Name'];
								$getThrowTable[] = array($throwSession['Session_Id'], $throwSession['Table_Name']);
							}
						}
						if(count($getThrowSession) == 2 && !(in_array($this->_logEmpId, $getEmpId))) // will show message if two user access who has not locked the form
						{
							$editEmpNameA = $this->_dbPersonal->employeeName($getEmpId[0]);
							$editEmpNameB = $this->_dbPersonal->employeeName($getEmpId[1]);
							$this->view->access = $editEmpNameA['Employee_Name'] . ' and '. $editEmpNameB['Employee_Name'].' are accessing this form. Please Wait...';
						}
						else
						{
							if(!empty($empdept)&&!empty($empLocation)&&!empty($empType)&&!empty($payslipMonth))
							{
								if(!empty($payPeriod))
								{
									$monthlyPayslip = $this->_dbPayslip->generateBwdMonthly($payslipMonth, $this->_logEmpId, $empLocation, $empdept, $empType, $salaryMonth, $empIdList,'No',$payPeriod);
								}
								else
								{
									$monthlyPayslip = $this->_dbPayslip->generateMonthly($payslipMonth, $this->_logEmpId, $empLocation, $empdept, $empType, $salaryMonth, $empIdList,'No');
								}
                                // $monthlyPayslip[1] = get all the employee id whose status for overtime, attendance, bonus, leave is not approved  so as not to generate payslip for those employees
								// $monthlyPayslip[2] = get all the employee id whose status for overtime, attendance, bonus, leave is getting approved
								// $monthlyPayslip[3] = get all the employee id whose benefit is changed from previous month to current month
								// $monthlyPayslip[4] = get all the employee id whose attendance getting lacked.
								// $monthlyPayslip[5] = get all the employee id whose previous month payslip is not there
								// $monthlyPayslip[6] = check the assets are returned during employee last payslip month
								// $monthlyPayslip[7] = check the tax declaration status are approved financial closure or employee resignation
								
								if(is_array($monthlyPayslip) && count($monthlyPayslip)>0)
								{
									$payslipTemplateId = $this->_dbPayslipTemplate->listTemplateId($serviceProviderId,"Monthly");

									//hrappdev-1003- when attendance enforcement flag has to be checked in this condition
									if($monthlyPayslip[0]=='success' && (count($monthlyPayslip[1])==0 && count($monthlyPayslip[3])==0 && count($monthlyPayslip[4])==0 && count($monthlyPayslip[5])==0 && count($monthlyPayslip[6]) == 0 && count($monthlyPayslip[7]) == 0 && count($monthlyPayslip[8]) == 0 && count($monthlyPayslip[9]) == 0 && count($monthlyPayslip[10]) == 0 && 
									count($monthlyPayslip[11]) == 0 && count($monthlyPayslip[13]) == 0) && count($monthlyPayslip[12]) > 0)
									{
										$this->view->result = array('success' => true, 'msg' => 'Payslip Generated Successfully!', 'payslipGeneratedId' => $monthlyPayslip[12], 'payslipTemplateId' => $payslipTemplateId, 'type' => 'success');
									}
									/** If payslip generated but there are pending approval */
									elseif($monthlyPayslip[0]=='success' && (count($monthlyPayslip[1])>0 || count($monthlyPayslip[3])>0 || count($monthlyPayslip[4])>0 || count($monthlyPayslip[5])>0 || count($monthlyPayslip[6])>0 || count($monthlyPayslip[7])>0 || count($monthlyPayslip[8])>0 || count($monthlyPayslip[9])>0 || count($monthlyPayslip[10]) > 0 || count($monthlyPayslip[11]) > 0 || count($monthlyPayslip[12] == 0)
									|| count($monthlyPayslip[13]) > 0))
									{
										$payslipmsg = '';
										if(count($monthlyPayslip[1])>0)
										{
											$empnameA = $this->_dbPayslip->employeename($monthlyPayslip[1]);
											$payslipmsg .= 'Please update all the status that are requested by the employee(s) and the actions awaiting with the employee(s) to generate payslip';
										}
										if(count($monthlyPayslip[3])>0)
										{
											$empnameB = $this->_dbPayslip->employeename($monthlyPayslip[3]);
											$payslipmsg .= 'Please update salary for ';
											$empnameBCount = count($empnameB);
											for($payB=0;$payB<$empnameBCount;$payB++){
												$payslipmsg .= $empnameB[$payB];
												if($payB != (count($empnameB)-1)){
													$payslipmsg .= ", ";
												}
													
											}
											$payslipmsg .= ' to generate payslip.';
										}
										if(count($monthlyPayslip[4])>0)
										{
											$empnameC = $this->_dbPayslip->employeename($monthlyPayslip[4]);
											$payslipmsg .= 'Please update attendance for ';
											$empnameCCount = count($empnameC);
											for($payC=0;$payC<$empnameCCount;$payC++){
												$payslipmsg .= $empnameC[$payC];
												if($payC != (count($empnameC)-1)){
													$payslipmsg .= ", ";
												}
													
											}
											$payslipmsg .= ' to generate payslip.';
										}
										if(count($monthlyPayslip[5])>0)
										{
											$empnameC = $this->_dbPayslip->employeename($monthlyPayslip[5]);
											$payslipmsg .= 'The payslip for the current month cannot be generated for the following employees: ';
											$empnameCCount = count($empnameC);
											for($payC=0;$payC<$empnameCCount;$payC++){
												$payslipmsg .= $empnameC[$payC];
												if($payC != (count($empnameC)-1)){
													$payslipmsg .= ", ";
												}
													
											}
											$payslipmsg .= ' This is because either the payslip for the future month has already been generated or the payslip for the previous month has not been generated for them.';
										}
										if(count($monthlyPayslip[6])>0)
										{
											$empnameC = $this->_dbPayslip->employeename($monthlyPayslip[6]);
											$payslipmsg .= 'Please ask the following employees ';
											$empnameCCount = count($empnameC);
											for($payC=0;$payC<$empnameCCount;$payC++){
												$payslipmsg .= $empnameC[$payC];
												if($payC != (count($empnameC)-1)){
													$payslipmsg .= ", ";
												}
													
											}
											$payslipmsg .= '  to return the assets for generating their payslip';
										}
										if(count($monthlyPayslip[7])>0)
										{
											$empnameC = $this->_dbPayslip->employeename($monthlyPayslip[7]);
											$payslipmsg .= 'Please update the tax declaration status that are requested by ';
											$empnameCCount = count($empnameC);
											for($payC=0;$payC<$empnameCCount;$payC++){
												$payslipmsg .= $empnameC[$payC];
												if($payC != (count($empnameC)-1)){
													$payslipmsg .= ", ";
												}
													
											}
											$payslipmsg .= '  to generate the payslip';
										}
										if(count($monthlyPayslip[8])>0)
										{
											$empnameC = $this->_dbPayslip->employeename($monthlyPayslip[8]);
											$payslipmsg .= 'Please update the hra declaration status that are requested by ';
											$empnameCCount = count($empnameC);
											for($payC=0;$payC<$empnameCCount;$payC++){
												$payslipmsg .= $empnameC[$payC];
												if($payC != (count($empnameC)-1)){
													$payslipmsg .= ", ";
												}
													
											}
											$payslipmsg .= '  to generate the payslip';
										}
										if(count($monthlyPayslip[9])>0)
										{
											$empnameC = $this->_dbPayslip->employeename($monthlyPayslip[9]);
											$payslipmsg .= 'Please add/update the shift scheduling for ';
											$empnameCCount = count($empnameC);
											for($payC=0;$payC<$empnameCCount;$payC++){
												$payslipmsg .= $empnameC[$payC];
												if($payC != (count($empnameC)-1)){
													$payslipmsg .= ", ";
												}
													
											}
											$payslipmsg .= '  to generate the payslip';
										}
										if(count($monthlyPayslip[10])>0)
										{
											$empnameC = $this->_dbPayslip->employeename($monthlyPayslip[10]);
											$payslipmsg .= 'Please recalculate salary details for ';
											$empnameCCount = count($empnameC);
											for($payC=0;$payC<$empnameCCount;$payC++){
												$payslipmsg .= $empnameC[$payC];
												if($payC != (count($empnameC)-1)){
													$payslipmsg .= ", ";
												}
													
											}
											$payslipmsg .= '  to generate the payslip';
										}
										if(count($monthlyPayslip[11])>0)
										{
											$empnameC = $this->_dbPayslip->employeename($monthlyPayslip[11]);
											$payslipmsg .= 'Please update the Gratuity Status for ';
											$empnameCCount = count($empnameC);
											for($payC=0;$payC<$empnameCCount;$payC++){
												$payslipmsg .= $empnameC[$payC];
												if($payC != (count($empnameC)-1)){
													$payslipmsg .= ", ";
												}
													
											}
											$payslipmsg .= '  to generate the payslip';
										}
										/** If the house property declaration is not approved or if the house property declaration
										 * is 'Returned'/'Reopened' during the financial end month or if the employee resigned in 
										 * the payslip month
										 */
										if(count($monthlyPayslip[13])>0)
										{
											$empnameC = $this->_dbPayslip->employeename($monthlyPayslip[13]);
											$payslipmsg .= 'Please update the income under section24 declaration status that are requested by ';
											$empnameCCount = count($empnameC);
											for($payC=0;$payC<$empnameCCount;$payC++){
												$payslipmsg .= $empnameC[$payC];
												if($payC != (count($empnameC)-1)){
													$payslipmsg .= ", ";
												}
													
											}
											$payslipmsg .= '  to generate the payslip';
										}

										$this->view->result = array('success' => false, 'msg' => $payslipmsg, 'payslipGeneratedId' => $monthlyPayslip[12],'payslipTemplateId' => $payslipTemplateId, 'type' => 'danger');
									}
									 // $monthlyPayslip[1] = get all the employee id whose status for overtime, attendance, bonus, leave is not approved  so as not to generate payslip for those employees
									// $monthlyPayslip[2] = get all the employee id whose benefit is changed from previous month to current month
									// $monthlyPayslip[3] = get all the employee id whose attendance getting lacked.
									// $monthlyPayslip[4] = get all the employee id whose previous month payslip is not there
									// $monthlyPayslip[5] = check the assets are returned during employee last payslip month
								    // $monthlyPayslip[6] = check the tax declaration status are approved financial closure or employee resignation
									/** If payslip not generated and there are pending approval */
									elseif ($monthlyPayslip[0]=='f' && (count($monthlyPayslip[1])>0 || count($monthlyPayslip[3])>0 || count($monthlyPayslip[4])>0 || count($monthlyPayslip[5])>0 || count($monthlyPayslip[6])>0 || count($monthlyPayslip[7])>0 || count($monthlyPayslip[8])>0 || count($monthlyPayslip[9])>0 || count($monthlyPayslip[10])>0 || count($monthlyPayslip[11])>0 || count($monthlyPayslip[12]) > 0 
									|| count($monthlyPayslip[13]) > 0))
									{
										$payslipmsg = '';
										if(count($monthlyPayslip[1])>0)
										{
											$empnameA = $this->_dbPayslip->employeename($monthlyPayslip[1]);
											$payslipmsg .= 'Please update all the status that are requested by the employee(s) and the actions awaiting with the employee(s) to generate payslip';
										}
										if(count($monthlyPayslip[3])>0)
										{
											$empnameB = $this->_dbPayslip->employeename($monthlyPayslip[3]);
											$payslipmsg .= ' Please update salary details for ';
											$empnameBCount = count($empnameB);
											for($payB=0;$payB<$empnameBCount;$payB++){
												$payslipmsg .= $empnameB[$payB];
												if($payB != (count($empnameB)-1)){
													$payslipmsg .= ", ";
												}
													
											}
											$payslipmsg .= ' to generate payslip.';
										}
										if(count($monthlyPayslip[4])>0)
										{
											$empnameC = $this->_dbPayslip->employeename($monthlyPayslip[4]);
											$payslipmsg .= 'Please update attendance for ';
											$empnameCCount = count($empnameC);
											for($payC=0;$payC<$empnameCCount;$payC++){
												$payslipmsg .= $empnameC[$payC];
												if($payC != (count($empnameC)-1)){
													$payslipmsg .= ", ";
												}
													
											}
											$payslipmsg .= ' to generate payslip.';
										}
										if(count($monthlyPayslip[5])>0)
										{
											$empnameC = $this->_dbPayslip->employeename($monthlyPayslip[5]);
											$payslipmsg .= 'The payslip for the current month cannot be generated for the following employees: ';
											$empnameCCount = count($empnameC);
											for($payC=0;$payC<$empnameCCount;$payC++){
												$payslipmsg .= $empnameC[$payC];
												if($payC != (count($empnameC)-1)){
													$payslipmsg .= ", ";
												}
													
											}
											$payslipmsg .= ' This is because either the payslip for the future month has already been generated or the payslip for the previous month has not been generated for them.';
										}
										if(count($monthlyPayslip[6])>0)
										{
											$empnameC = $this->_dbPayslip->employeename($monthlyPayslip[6]);
											$payslipmsg .= 'Please ask the following employees ';
											$empnameCCount = count($empnameC);
											for($payC=0;$payC<$empnameCCount;$payC++){
												$payslipmsg .= $empnameC[$payC];
												if($payC != (count($empnameC)-1)){
													$payslipmsg .= ", ";
												}
													
											}
											$payslipmsg .= ' to return the assets for generating their payslip';
										}
										if(count($monthlyPayslip[7])>0)
										{
											$empnameC = $this->_dbPayslip->employeename($monthlyPayslip[7]);
											$payslipmsg .= 'Please update the tax declaration status that are requested by ';
											$empnameCCount = count($empnameC);
											for($payC=0;$payC<$empnameCCount;$payC++){
												$payslipmsg .= $empnameC[$payC];
												if($payC != (count($empnameC)-1)){
													$payslipmsg .= ", ";
												}
													
											}
											$payslipmsg .= '  to generate the payslip';
										}
										if(count($monthlyPayslip[8])>0)
										{
											$empnameC = $this->_dbPayslip->employeename($monthlyPayslip[8]);
											$payslipmsg .= 'Please update the hra declaration status that are requested by ';
											$empnameCCount = count($empnameC);
											for($payC=0;$payC<$empnameCCount;$payC++){
												$payslipmsg .= $empnameC[$payC];
												if($payC != (count($empnameC)-1)){
													$payslipmsg .= ", ";
												}
													
											}
											$payslipmsg .= '  to generate the payslip';
										}
										

										if(count($monthlyPayslip[9])>0)
										{
											$empnameC = $this->_dbPayslip->employeename($monthlyPayslip[9]);
											$payslipmsg .= 'Please add/update the shift scheduling for ';
											$empnameCCount = count($empnameC);
											for($payC=0;$payC<$empnameCCount;$payC++){
												$payslipmsg .= $empnameC[$payC];
												if($payC != (count($empnameC)-1)){
													$payslipmsg .= ", ";
												}
													
											}
											$payslipmsg .= '  to generate the payslip';
										}

										if(count($monthlyPayslip[10])>0)
										{
											$empnameC = $this->_dbPayslip->employeename($monthlyPayslip[10]);
											$payslipmsg .= 'Please recalculate salary details for ';
											$empnameCCount = count($empnameC);
											for($payC=0;$payC<$empnameCCount;$payC++){
												$payslipmsg .= $empnameC[$payC];
												if($payC != (count($empnameC)-1)){
													$payslipmsg .= ", ";
												}
													
											}
											$payslipmsg .= '  to generate the payslip';
										}

										if(count($monthlyPayslip[11])>0)
										{
											$empnameC = $this->_dbPayslip->employeename($monthlyPayslip[11]);
											$payslipmsg .= 'Please update Gratuity Status for ';
											$empnameCCount = count($empnameC);
											for($payC=0;$payC<$empnameCCount;$payC++){
												$payslipmsg .= $empnameC[$payC];
												if($payC != (count($empnameC)-1)){
													$payslipmsg .= ", ";
												}
													
											}
											$payslipmsg .= '  to generate the payslip';
										}

										/** If the house property declaration is not approved or if the house property declaration
										 * is 'Returned'/'Reopened' during the financial end month or if the employee resigned in 
										 * the payslip month
										 */
										if(count($monthlyPayslip[13])>0)
										{
											$empnameC = $this->_dbPayslip->employeename($monthlyPayslip[13]);
											$payslipmsg .= 'Please update the income under section24 declaration status that are requested by ';
											$empnameCCount = count($empnameC);
											for($payC=0;$payC<$empnameCCount;$payC++){
												$payslipmsg .= $empnameC[$payC];
												if($payC != (count($empnameC)-1)){
													$payslipmsg .= ", ";
												}
													
											}
											$payslipmsg .= ' to generate the payslip';
										}

										$this->view->result = array('success' => false, 'msg' => $payslipmsg, 'payslipGeneratedId' => $monthlyPayslip[11], 'payslipTemplateId' => $payslipTemplateId, 'type' => 'danger');
									}else{
										$this->view->result = array('success' => false, 'msg' => 'There seems to be some technical difficulties in generating the payslip. Please contact system administrator', 'payslipGeneratedId' => array(),'payslipTemplateId' => 0, 'type' => 'warning');
									}
								}
								elseif(!empty($monthlyPayslip))
								{
									$this->view->result = array('success' => false, 'msg' => $monthlyPayslip, 'payslipGeneratedId' => array(), 'payslipTemplateId' => 0, 'type' => 'warning');									
								}else{
									$this->view->result = array('success' => false, 'msg' => 'There seems to be some technical difficulties in generating the payslip. Please contact system administrator', 'payslipGeneratedId' => array(), 'payslipTemplateId' => 0,'type' => 'warning');
								}
							}

							if(!empty($empdept)&&!empty($empLocation)&&!empty($payslipMonth)&& empty($empType))
							{
								$hourlyPayslip = $this->_dbPayslip->generateHourly($payslipMonth, $this->_logEmpId, $empLocation, $empdept, $salaryMonth,$serviceProviderId);
								if(is_array($hourlyPayslip) && count($hourlyPayslip)>0)
								{
									$payslipTemplateId = $this->_dbPayslipTemplate->listTemplateId($serviceProviderId,"Hourly");										

									if($hourlyPayslip[0]=='success' && count($hourlyPayslip[1])==0 && count($hourlyPayslip[3])==0 )
									{
										$this->view->result = array('success' => true, 'msg' => 'Payslip Generated Successfully', 'payslipIds' => $hourlyPayslip[4],'payslipTemplateId' => $payslipTemplateId,'type' => 'success');
									}
									elseif($hourlyPayslip[0]=='success' && (count($hourlyPayslip[1])>0 || count($hourlyPayslip[3])>0))
									{									
										$payslipmsg = '';
										if(count($hourlyPayslip[1])>0)
										{
											$empnameA = $this->_dbPayslip->employeename($hourlyPayslip[1]);
											$payslipmsg .= 'Please update all the status that are requested by ';
											$empnameACount = count($empnameA);
											for($payA=0;$payA<$empnameACount;$payA++){
												$payslipmsg .= $empnameA[$payA];
												if($payA != (count($empnameA)-1)){
													$payslipmsg .= ", ";
												}

											}
											$payslipmsg .= ' to generate payslip.';
										}
										if(count($hourlyPayslip[3])>0)
										{
											$empnameB = $this->_dbPayslip->employeename($hourlyPayslip[3]);
											$payslipmsg .= ' Please update salary details for ';
											$empnameBCount = count($empnameB);
											for($payB=0;$payB<$empnameBCount;$payB++){
												$payslipmsg .= $empnameB[$payB];
												if($payB != (count($empnameB)-1)){
													$payslipmsg .= ", ";
												}
													
											}
											$payslipmsg .= ' to generate payslip.';
										}
										$this->view->result = array('success' => false, 'msg' => $payslipmsg, 'payslipTemplateId' => 0,'type' => 'danger');
										//$this->view->payslipmsg = array('f',$payslipmsg);
									}
									elseif ($hourlyPayslip[0]=='f' && (count($hourlyPayslip[1])>0 || count($hourlyPayslip[2])>0))
									{
										$payslipmsg = '';
										if(count($hourlyPayslip[1])>0)
										{
											$empnameA = $this->_dbPayslip->employeename($hourlyPayslip[1]);
											$payslipmsg .= 'Please update all the status that are requested by ';
											$empnameACount = count($empnameA);
											for($payA=0;$payA<$empnameACount;$payA++){
												$payslipmsg .= $empnameA[$payA];
												if($payA != (count($empnameA)-1)){
													$payslipmsg .= ", ";
												}

											}
											$payslipmsg .= ' to generate payslip.';
										}
										if(count($hourlyPayslip[2])>0)
										{
											$empnameB = $this->_dbPayslip->employeename($hourlyPayslip[2]);
											$payslipmsg .= 'Please update salary for ';
											$empnameBCount = count($empnameB);
											for($payB=0;$payB<$empnameBCount;$payB++){
												$payslipmsg .= $empnameB[$payB];
												if($payB != (count($empnameB)-1)){
													$payslipmsg .= ", ";
												}
													
											}
											$payslipmsg .= ' to generate payslip.';
										}
										
										$this->view->result = array('success' => false, 'msg' => $payslipmsg,'payslipTemplateId' => 0, 'type' => 'danger');
									}
								}
								elseif(!empty($hourlyPayslip))
								{									
									$this->view->result = array('success' => false, 'msg' => $hourlyPayslip,'payslipTemplateId' => 0, 'type' => 'warning');
								}
									
							}
						}
					}
					else
					{
						$this->view->result = array('success' => false, 'msg' => 'Invalid Data!', 'payslipTemplateId' => 0, 'type' => 'danger');						
					}
				}
				else
				{					
					$this->view->result = array('success' => false, 'msg' => 'Sorry, Access Denied...','payslipTemplateId' => 0, 'type' => 'danger');
				}
		}
		else
		{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}
	}
	
	/**
	 * Used for sending a mail for hourly wages employee and this action is used in SalaryWagePayslipAction
	 */
	public function wagePayslipMailAction($empId, $salaryMonth)
	{
		$layout = $this->_helper->layout();
		$layout->disableLayout('layout');

		$payslipInfo = $this->_dbPayslip->hourlyPayslipInfo($empId, $salaryMonth);

		$companyName = $this->_dbPayslip->companyInfo();
		$salarymonth = explode(',',$salaryMonth);
		$month = array('1'=>'January', '2'=>'February', '3'=>'March', '4'=>'April', '5'=>'May', '6'=>'June', '7'=>'July',
				'8'=>'August', '9'=>'September', '10'=>'October', '11'=>'November', '12'=>'December');
		foreach ($payslipInfo as $sendMail)
		{
			$incentives = $this->_dbPayslip->getHourlyIncentive($sendMail['Payslip_Id']);
			$deduction = $this->_dbPayslip->getHourlyDeduction($sendMail['Payslip_Id']);
			$currencySymbol = $this->_dbPersonal->currencySymbol($sendMail['Employee_Id']);
			$message = "<div style='text-align:right;font-size:15px;'>Report generated on " . $sendMail['Generated_On'] . "</div>
			<table style='font-size:15px;width:100%;border-spacing:0;background-color:none;'>
			<tr style='background:none;'>
			<td colspan='4'><div style='text-align:center;font-weight:bold;font-size:20px;'>PAYSLIP<br/> " . $month[$salarymonth[0]]  . " " .  $salarymonth[1] . " </div></td>
			</tr>
			<tr style='background:none;font-size:15px;'>
			<td style='width:15%'>Employee&nbsp;Name</td><td style='border-bottom:1px solid #000;'> " .$sendMail['Emp_First_Name'] . " " . $sendMail['Emp_Last_Name'] . "</td>
			<td style='width:15%'>Department</td><td style='border-bottom:1px solid #000;'> " . $sendMail['Department_Name'] . "</td>
			</tr>
			<tr style='background:none;font-size:15px;'>
			<td style='width:15%'>Designation</td><td style='border-bottom:1px solid #000;'> " . $sendMail['Designation_Name'] . "</td> ";
			if(empty($sendMail['User_Name']))
			{
				$message .= "<td colspan='2'></td>";
			}
			else
			{
				$message .= "<td style='width:15%'>User&nbsp;Name</td><td style='border-bottom:1px solid #000;'> ". $sendMail['User_Name'] ." </td>";

			}
			$message .= "</tr>
			</table>
			<div style='font-weight:bold;font-size:15px;'>Salary Details</div>
			<table style='font-size:10px;width:100%;border-spacing:15px;border: 1px solid #000;background-color:none;'>
			<tr style='background:none;'>
			<td colspan='2'>Total&nbsp;Hourly&nbsp;Wages</td><td style='text-align:right;' colspan='2'>$currencySymbol " . $sendMail['Total_HourlyWages'] . "</td>
			</tr>
			<tr style='background:none;'>
			<td colspan='2'>Total&nbsp;Overtime&nbsp;Wages</td><td style='text-align:right;' colspan='2'>$currencySymbol " .$sendMail['Total_OvertimeWages'] . "</td>
			</tr>";
			if(count($incentives)>0)
			{
				foreach ($incentives as $incentive )
				{

					$incentiveName = $incentive['Incentive_Name'];
					if(!empty($incentive['Description']) && $incentive['Description']!='Tax')
					{
						$incentiveName = $incentive['Incentive_Name'].'-'.$incentive['Description'];
					}
					$message .= "<tr style='background:none;'>
					<td colspan='2'>" . $incentiveName . "</td><td style='text-align:right;' colspan='2'>$currencySymbol " .  $incentive['Incentive_Amount'] . "</td>
					</tr>";


				}
			}
			if(count($deduction)>0)
			{
				foreach ($deduction as $deduct )
				{
					$deductionName = $deduct['Deduction_Name'];
					if(!empty($deduct['Description']))
					{
						$deductionName = $deduct['Deduction_Name'].'-'.$deduct['Description'];
					}
					$message .= "<tr style='background:none;'>
					<td colspan='2'>" . $deductionName . "</td><td style='text-align:right;' colspan='2'>$currencySymbol " .  $deduct['Deduction_Amount'] . "</td>
					</tr>" ;
				}
			}
			$totalSalary = $sendMail['Total_Salary'];
			if($sendMail['Total_Salary']<0)
			{
				$totalSalary = '('.$sendMail['Total_Salary'].')';
			}
			$message .= "<tr style='background:none;'>
			<td colspan='2' style='font-weight:bold;'>Total&nbsp;Salary</td><td colspan='2'  style='font-weight:bold;text-align:right;border-top:2px solid #000;'>$currencySymbol " . $totalSalary . "</td>
			</tr>

			</table><br/><br/>";
			$sendMessage = $this->_dbPersonal->emailAddress($this->_logEmpId, $sendMail['Employee_Id']);
			if(!empty($sendMessage[1]['Emp_Email']) && !empty($sendMessage[0]['Emp_Email']))
			{
				$customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
				$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
				
				$mailSend = $this->_dbCommonFun->communicateMail (array(
																		'employeeId'     => $this->_logEmpId,
																		'ModuleName'     => 'Payroll',
																		'formName'       => $this->_formNameA,
																		'successMsg'     => $formName,
																		'customFormName' => $formName,
																		'formUrl'        => '/payroll/salary-payslip',
																		'inboxTitle'     => $formName.' Notification',                                                                      
																		'mailContent'    => $sendMessage,
																		'action'         => 'payslip generated'));
			}
			$inboxRoles = $this->_dbAccessRights->employeeAccessRights($sendMail['Employee_Id'], $this->_inboxName);
			if($inboxRoles['Employee']['View'] == 1)
			{
				$this->_dbInbox->employeeInbox($sendMail['GeneratedBy_EmployeeId'], $sendMail['Employee_Id'],
						'Report from '. $companyName, htmlentities($message));
			}
		}

	}

	// lock the generate payslip during payslip type change event
	public function updatePaysliptypeAction()
	{
		$layout = $this->_helper->layout();
		$layout->disableLayout('layout');
		if(isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('update-paysliptype', 'json')->initContext();
			$payslipType = $this->_getParam('type', null);
			if(!empty($payslipType))
			{
				if($payslipType == 'a')
					$tableName = $this->_ehrTables->monthlyPayslip;
				else if($payslipType == 'b')
					$tableName = $this->_ehrTables->wagePayslip;
				$this->view->checkLock = $this->_dbPayslip->checkPayslipLock($this->_logEmpId, $tableName);
			}
		}
		else
		{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}
	}
	
	
	public function exportHourlyPayslipAction()
	{
		$this->_helper->layout()->disableLayout();
        if(isset($_SERVER['HTTP_REFERER']))
        {			
			$payslipId = $this->_getParam('payslipId', null);		
			$incentiveAmtInWords = $this->_getParam('Incentive_Amount_Words',null);
			$payslipId = explode(',',$payslipId);
			$incentiveAmtInWords = explode(',', $incentiveAmtInWords);
			
			$format = $this->_getParam('format', null);
			
			if(!empty($payslipId))
			{
				$exportStr = "";				
					
				if($this->_payslipAccessRights['Employee']['Optional_Choice']==1)
				{
					for($i = 0;$i<count($payslipId);$i++)
					{
						$orgCode = $this->_ehrTables->getOrgCode();
                        $payslip = $this->_dbPayslip->viewHoulryWages($payslipId[$i],$orgCode);						
						$payslip['Incentive_Amount_Words'] = $incentiveAmtInWords[$i];
                        array_unshift($payslip['Incentive'],array('Incentive_Name'=>'Total Overtime Wages','Incentive_Amount'=>$payslip['Payslip']['Total_OvertimeWages']));
						array_unshift($payslip['Incentive'],array('Incentive_Name'=>'Total Hourly Wages','Incentive_Amount'=>$payslip['Payslip']['Total_HourlyWages']));
						$salary = $this->view->payslip = $payslip;
						$currency = $this->view->currency = $this->_dbPersonal->currencySymbol($payslip['Payslip']['Employee_Id']);
						$exportStr .= $this->payslipHTML($salary, $currency, 'Hourly','','pdf');
				

						// require_once('tcpdf/config/lang/eng.php');
						// require_once('tcpdf/tcpdf.php');

						require_once realpath(APPLICATION_PATH . '/../vendor/tecnickcom/tcpdf/examples/tcpdf_include.php');
						require_once realpath(APPLICATION_PATH . '/../vendor/tecnickcom/tcpdf/examples/lang/eng.php');
						require_once realpath(APPLICATION_PATH . '/../vendor/tecnickcom/tcpdf/tcpdf.php');
		
						
						$pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
						// set document information
						$pdf->SetCreator(PDF_CREATOR);
						$pdf->SetAuthor('');
						$pdf->SetTitle('Monthly Salary Payslip');
						$pdf->SetSubject('Monthly Salary Payslip');
						$pdf->SetKeywords('monthly, salary, payslip');
						$pdf->SetFont('Courier','',9,'',true);
						
						$lc=array(0,64,128);
						
						// set default monospaced font
						$pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);

						//set margins
						$pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
						$pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
						$pdf->SetFooterMargin(PDF_MARGIN_FOOTER);

						//set auto page breaks
						$pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);

						//set image scale factor
						$pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);

						//set some language-dependent strings
						$pdf->setLanguageArray($lc);

						// ---------------------------------------------------------

						// set default font subsetting mode
						$pdf->setFontSubsetting(true);

						// Set font
						// dejavusans is a UTF-8 Unicode font, if you only need to
						// print standard ASCII chars, you can use core fonts like
						// helvetica or times to reduce file size.
						//$pdf->SetFont('helvetica', '', 20, '', false);

						 
						$pdf->setPrintHeader(false);
						$pdf->setPrintFooter(false); 
						$pdf->AddPage();
						$pdf->SetCreator(PDF_CREATOR);
						
						$pdf->setHeaderFont(Array(PDF_FONT_NAME_MAIN, '', PDF_FONT_SIZE_MAIN));
						$pdf->setFooterFont(Array(PDF_FONT_NAME_DATA, '', PDF_FONT_SIZE_DATA));
						$pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
						$pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
						$pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
						$pdf->SetFooterMargin(PDF_MARGIN_FOOTER);
						$pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);
						$pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);
						$pdf->setFontSubsetting(true);
						$pdf->writeHTML($exportStr, true, false, false, false, '');
					}
					$pdf->Output('HourlyPayslip.pdf', 'D');
				}
			}
		}
		else
		{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}
	}
	
    
	public function exportMonthlyPayslipAction()
	{
		$this->_helper->layout()->disableLayout();
		if(isset($_SERVER['HTTP_REFERER']) || $_COOKIE['isNativeMobileApp'] == 1)
		{
			$payslipId = $this->_getParam('payslipId', null);
			$incentiveAmtInWords = $this->_getParam('Incentive_Amount_Words',null);
			$payslipId = explode(',',$payslipId);
			$incentiveAmtInWords = explode(',', $incentiveAmtInWords);
			$format = $this->_getParam('format', null);
			
			if(!empty($payslipId))
			{
				$exportStr = " ";
				
				if($this->_payslipAccessRights['Employee']['View']==1)
				{				
					$orgCode = $this->_ehrTables->getOrgCode();
					
					// require_once('tcpdf/config/lang/eng.php');
                    // require_once('tcpdf/tcpdf.php');
					//require_once('FPDI/fpdi.php');
					
					require_once realpath(APPLICATION_PATH . '/../vendor/tecnickcom/tcpdf/examples/tcpdf_include.php');
					require_once realpath(APPLICATION_PATH . '/../vendor/tecnickcom/tcpdf/examples/lang/eng.php');
					require_once realpath(APPLICATION_PATH . '/../vendor/tecnickcom/tcpdf/tcpdf.php');
					// require_once realpath(APPLICATION_PATH . '/../vendor/setasign/fpdi/fpdi.php');
		
                    
                    $dirPath = $this->_ehrTables->filePath."pdfexport/";
                    $usrFolderName = $orgCode."-".$this->_logEmpId;
					$dirPath = $dirPath.$usrFolderName;

                    if(!is_dir($dirPath))
                    {
                        mkdir($dirPath,0777);
            
                        $payslipIdCnt = count($payslipId);
                        $generated = false;
                        if($payslipIdCnt <= 10)
                        {
                            //normal pdf generation process
                            $generated = $this->payslipPDF($payslipId,$incentiveAmtInWords,$payslipIdCnt,$orgCode,$dirPath,$usrFolderName);
                        }
                        else
                        {
                            //Pdf merge process
                            $generated = $this->payslipPDFMerge($payslipId,$incentiveAmtInWords,$payslipIdCnt,$orgCode,$dirPath,$usrFolderName);
                        }
                        
                        if($generated)
                        {
                            $this->view->usrDir = base64_encode(file_get_contents($dirPath.'/Payslip.pdf'));
                            unlink($dirPath.'/Payslip.pdf');
                            rmdir($dirPath);
                        }
                    }
                    else
                    {
                        $this->view->usrDir = "exists";
                    }
				}
			}
		}
		else
		{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}
	}

    /*For PDF export when payslip records are less than 10*/
    public function payslipPDF($payslipId,$incentiveAmtInWords,$payslipIdCnt,$orgCode,$dirPath,$usrFolderName)
    {
        $exportStr = " ";
        
        for($j=0;$j<count($payslipId);$j++)
        {
            $payslip = $this->_dbPayslip->viewMonthlySalary($payslipId[$j]);
			$payslip['IntermediatePaymentAmt'] = 0.00;
			$payslip['Incentive_Amount_Words'] = $incentiveAmtInWords[$j];
            array_unshift($payslip['Incentive'],array('Incentive_Name'=>'Basic Pay','Incentive_Amount'=>$payslip['Payslip']['Basic_Salary']));						
    
            $orgCode = $this->_ehrTables->getOrgCode();
            $salaryCalcDetail = $this->_dbOrgSettings->viewOrgDetail($orgCode);
            
			if(count($payslip['Incentive'] > 0))
			{
				for($i=0;$i<count($payslip['Incentive']);$i++)
				{
					if($payslip['Incentive'][$i]['Incentive_Name'] == 'Provident Fund' ||
						$payslip['Incentive'][$i]['Incentive_Name'] == 'Variable Insurance')
					{
						array_splice($payslip['Incentive'],$i,1);
					}
				}
			}
            
			// Get the employee form16 details
			$employeeForm16Details = $this->_dbPayslip->getMonthlyPayslipForm16Details($payslipId[$j],$payslip['Payslip']['Employee_Id'],$payslip['Payslip']['Salary_Month']);
			$payslip['Form16Exists'] = $employeeForm16Details['Form16Exists'];
            $payslip['Form16'] = $employeeForm16Details['Form16'];
            
            $newDeductionArr = array();
            /** hiding TDS row if TDS is 0 **/
            if(count($payslip['Deduction'] > 0))
            {
                $payCyclePmtDeductionAmt = 0;
                $immediatePmtDeductionAmt = 0;
                
                for($d=0;$d<count($payslip['Deduction']);$d++)
                {						
                    if($payslip['Deduction'][$d]['Deduction_Name'] == 'TDS' &&
                       $payslip['Deduction'][$d]['Deduction_Amount'] == 0)
                    {
                        
                    }
                    elseif($payslip['Deduction'][$d]['Deduction_Name'] == 'Advance Salary')
                    {
                        if($payslip['Deduction'][$d]['PaymentType'] == 'Salary Pay-cycle')
                        {
                            $payCyclePmtDeductionAmt += $payslip['Deduction'][$d]['Deduction_Amount'];			
                        }
                        elseif($payslip['Deduction'][$d]['PaymentType'] == 'Immediate')
                        {
                            $immediatePmtDeductionAmt += $payslip['Deduction'][$d]['Deduction_Amount'];			
                        }
                    }
                    else{								
                        array_push($newDeductionArr,$payslip['Deduction'][$d]);
                    }
                }
                
                if($payCyclePmtDeductionAmt > 0)
                {	
                    array_push($newDeductionArr, array('Deduction_Name' => 'Advance Salary - Salary Pay-cycle',
                                                       'Deduction_Amount' => number_format((float)$payCyclePmtDeductionAmt, 2, '.', '')));
                }
                
                if($immediatePmtDeductionAmt > 0)
                {
                    array_push($newDeductionArr, array('Deduction_Name' => 'Advance Salary - Immediate',
                                                       'Deduction_Amount' => number_format((float)$immediatePmtDeductionAmt, 2, '.', '')));
                }
                
                if($newDeductionArr != ''){
                    unset($payslip['Deduction']);
                    $payslip['Deduction'] = $newDeductionArr;
                }
            }
            
            if(count($payslip['Incentive'] > 0))
            {
                $payCyclePmtIncentiveAmt = $immediatePmtIncentiveAmt = $payCycleLoanIncentiveAmt = $immediateLoanIncentiveAmt = 0;
                
                $salaryIncentiveArray = array();
                
                foreach($payslip['Incentive'] as $key=>$val)
                {
                    if($val['Incentive_Name'] == 'Advance Salary')
                    {
                        if($val['PaymentType'] == 'Salary Pay-cycle')
                        {
                            $payCyclePmtIncentiveAmt += $val['Incentive_Amount'];			
                        }
                        elseif($val['PaymentType'] == 'Immediate')
                        {
                            $immediatePmtIncentiveAmt += $val['Incentive_Amount'];			
                        }
                    }
                    elseif($val['Incentive_Name'] == 'Loan')
                    {
                        if($val['PaymentType'] == 'Salary Pay-cycle')
                        {
                            $payCycleLoanIncentiveAmt += $val['Incentive_Amount'];			
                        }
                        elseif($val['PaymentType'] == 'Immediate')
                        {
                            $immediateLoanIncentiveAmt += $val['Incentive_Amount'];			
                        }
                    }
                    else
                    {
                        array_push($salaryIncentiveArray, $payslip['Incentive'][$key]);
                    }
                }
                
                if($payCyclePmtIncentiveAmt > 0)
                {
                    array_push($salaryIncentiveArray, array('Incentive_Name' => 'Advance Salary - Salary Pay-cycle',
                                                            'Incentive_Amount' => number_format((float)$payCyclePmtIncentiveAmt, 2, '.', '')));
                }
                
                if($immediatePmtIncentiveAmt > 0)
                {
                    $payslip['IntermediatePaymentAmt'] += number_format((float)$immediatePmtIncentiveAmt, 2, '.', '');
                }
                
                if($payCycleLoanIncentiveAmt > 0)
                {
                    array_push($salaryIncentiveArray, array('Incentive_Name' => 'Loan - Salary Pay-cycle',
                                                            'Incentive_Amount' => number_format((float)$payCycleLoanIncentiveAmt, 2, '.', '')));
                }
                
                if($immediateLoanIncentiveAmt > 0)
                {
                    $payslip['IntermediatePaymentAmt'] += number_format((float)$immediateLoanIncentiveAmt, 2, '.', '');
                }
                
                $payslip['Incentive'] = $salaryIncentiveArray;
            }
        
            $dbSalary = new Payroll_Model_DbTable_Salary();
            $payslip['salaryDetails'] = $dbSalary->getMonthlyBasicPayAmt($payslip['Payslip']['Employee_Id']);
            //$payslip['orgPfAmt'] = $payslip['etfAmt'] = 0;
            //
            //$empPfExists = 0;
            //foreach($payslip['Deduction'] as $deduction){
            //	if($deduction['Deduction_Name'] == 'Provident Fund' && $deduction['Deduction_Amount'] > 0){
            //		$empPfExists = 1;
            //	}
            //}
            //
            //if($payslip['salaryDetails']['Is_PfEmployee'] && $empPfExists == 1)
            //{
            //	$dbPF = new Payroll_Model_DbTable_ProvidentFund();
            //	$payslip['orgPfAmt'] = $dbPF->calculatePFAmt($payslip['Payslip']['Employee_Id'], $payslip['salaryDetails']['Basic_Pay'], $salaryCalcDetail['Retirals_Based_On_Basic'], $payslip['salaryDetails']['Annual_Gross_Salary']);
            //}
            
            /** Custom Header for org pf amount**/
            $orgPfAmountField = $orgPfCustomHeader = 'Org PF Amount';
            $pfcustomField = $this->_ehrTables->getCustomFields($orgPfAmountField);
            if($pfcustomField){
                $orgPfCustomHeader = (!empty($pfcustomField['New_Field_Name']))	? $pfcustomField['New_Field_Name'] : $orgPfAmountField;
            }
            $payslip['orgPfAmtHeader'] = $orgPfCustomHeader;
        
            //$payslip['orgPfAmt'] = number_format(round($payslip['orgPfAmt'],2),2,'.','');
            //$payslip['InsuranceDetails'] = array();
            //
            //if($payslip['salaryDetails']['Is_InsuranceEmployee'])
            //{
            //	$dbInsurance = new Payroll_Model_DbTable_Insurance();
            //	
            //	$variableOrgInsDetails = $dbInsurance->getVariableOrgInsuranceRecords($payslip['Payslip']['Employee_Id']); 
            //	$variableEmpInsDetails = $dbInsurance->getVariableEmpInsuranceDetails($payslip['Payslip']['Employee_Id']);
            //	
            //	//getting the total allowance amount that includes insurance
            //	$getAllowanceAmt = $dbSalary->getAllowances($payslip['Payslip']['Employee_Id'],NULL,NULL, $payslip['salaryDetails']['Basic_Pay'], 'Insurance');
            //	$basicPlusAllowance = $payslip['salaryDetails']['Basic_Pay'] + $getAllowanceAmt;
            //	
            //	if(count($variableOrgInsDetails)>0 && !is_null($variableOrgInsDetails))
            //	{
            //		foreach($variableOrgInsDetails as $variableOrgIns)
            //		{
            //			if($salaryCalcDetail['Retirals_Based_On_Basic'] == 1)
            //			{
            //				$premium = number_format(round(($variableOrgIns['Org_SharePercent']/100)*($basicPlusAllowance),2),2,'.','');
            //			}
            //			else
            //			{
            //				$premium = number_format(round(($variableOrgIns['Share']/100)*($basicPlusAllowance),2),2,'.','');
            //			}
            //			
            //			array_push($payslip['InsuranceDetails'], array('InsuranceName' => $variableOrgIns['Insurance_Name'],
            //												   'Amount'=> $premium));
            //		}
            //	}
            //	
            //	if(isset($variableEmpInsDetails) && !empty($variableEmpInsDetails))
            //	{
            //		foreach ($variableEmpInsDetails as $variableEmpIns)
            //		{
            //			if(!empty($variableEmpIns['Share']))
            //			{
            //				if($salaryCalcDetail['Retirals_Based_On_Basic'] == 1)
            //				{
            //					$premium = number_format(round(($variableEmpIns['Org_SharePercent']/100)*($basicPlusAllowance),2),2,'.','');
            //				}
            //				else
            //				{
            //					$premium = number_format(round(($variableEmpIns['Share']/100)*($basicPlusAllowance),2),2,'.','');
            //					
            //				}
            //				
            //				array_push($payslip['InsuranceDetails'], array('InsuranceName' => $variableEmpIns['Insurance_Name'],
            //											   'Amount'=> $premium));
            //				
            //			}
            //		}
            //	}
            //}
            
            //if($payslip['salaryDetails']['Is_ETFEmployee'])
            //{
            //	$dbETF = new Payroll_Model_DbTable_ETF();
            //	$payslip['etfAmt'] = $dbETF->calculateETFAmt($payslip['Payslip']['Employee_Id'], $payslip['salaryDetails']['Basic_Pay'], $salaryCalcDetail['Retirals_Based_On_Basic'], $payslip['salaryDetails']['Annual_Gross_Salary']);
            //}
            //
            //$payslip['etfAmt'] = number_format(round($payslip['etfAmt'],0),2,'.','');
            $payslip['orgLWFAmtHeader'] = 'Org LWF Amount';    
            $salary = $this->view->monthlyPayslip = $payslip;
    
            $currency = $this->view->currency = $this->_dbPersonal->currencySymbol($payslip['Payslip']['Employee_Id']);
            $exportStr .= $this->payslipHTML($salary, $currency, 'Monthly','','pdf');
            						
            //$pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
            $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'ISO-8859-1', false);
            //$pdf = new MYPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'ISO-8859-1', false);
            // set document information
            $pdf->SetCreator(PDF_CREATOR);
            $pdf->SetAuthor('');
            $pdf->SetTitle('Monthly Salary Payslip');
            $pdf->SetSubject('Monthly Salary Payslip');
            $pdf->SetKeywords('monthly, salary, payslip');
            $pdf->SetFont('dejavusans', '', 9,'',true);                        
            
            $lc=array(0,64,128);
            
            // set default monospaced font
            $pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
                    
            //set margins
            $pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
            $pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
            $pdf->SetFooterMargin(PDF_MARGIN_FOOTER);
    
            //set image scale factor
            $pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);

            //set some language-dependent strings
            $pdf->setLanguageArray($lc);						

            // set default font subsetting mode
            $pdf->setFontSubsetting(true);

            // Set font
            // dejavusans is a UTF-8 Unicode font, if you only need to
            // print standard ASCII chars, you can use core fonts like
            // helvetica or times to reduce file size.
            //$pdf->SetFont('helvetica', '', 20, '', false);

             
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(false); 
            //$pdf->AddPage();
            
            $pdf->AddPage('P', 'A4');                        
            
            //set auto page breaks
            $pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);
            
            $pdf->setHeaderFont(Array(PDF_FONT_NAME_MAIN, '', PDF_FONT_SIZE_MAIN));
            $pdf->setFooterFont(Array(PDF_FONT_NAME_DATA, '', PDF_FONT_SIZE_DATA));
            $pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
            $pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
            $pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
            $pdf->SetFooterMargin(PDF_MARGIN_FOOTER);
            $pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_FOOTER);
            $pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);
            $pdf->setFontSubsetting(true);
            
            $pdf->writeHTML($exportStr, true, false, false, false, '');                        
        }
        
        $pdf->Output($dirPath.'/Payslip.pdf', 'F');
        return true;
    }
    
    /*for bulk PDF export.will create file for each payslip and merge it finally*/
    public function payslipPDFMerge($payslipId,$incentiveAmtInWords,$payslipIdCnt,$orgCode,$dirPath,$usrFolderName)
    {
        $exportStr = " ";
        $files = array();
                
        for($j=0;$j<count($payslipId);$j++)
        {
            $payslip = $this->_dbPayslip->viewMonthlySalary($payslipId[$j]);
			$payslip['IntermediatePaymentAmt'] = 0.00;
			$payslip['Incentive_Amount_Words'] = $incentiveAmtInWords[$j];
            array_unshift($payslip['Incentive'],array('Incentive_Name'=>'Basic Pay','Incentive_Amount'=>$payslip['Payslip']['Basic_Salary']));						
    
            $orgCode = $this->_ehrTables->getOrgCode();
            $salaryCalcDetail = $this->_dbOrgSettings->viewOrgDetail($orgCode);
            
			if(count($payslip['Incentive'] > 0))
			{
				for($i=0;$i<count($payslip['Incentive']);$i++)
				{
					if($payslip['Incentive'][$i]['Incentive_Name'] == 'Provident Fund' ||
						$payslip['Incentive'][$i]['Incentive_Name'] == 'Variable Insurance')
					{
						array_splice($payslip['Incentive'],$i,1);
					}
				}
			}

			// Get the employee form16 details
			$employeeForm16Details = $this->_dbPayslip->getMonthlyPayslipForm16Details($payslipId[$j],$payslip['Payslip']['Employee_Id'],$payslip['Payslip']['Salary_Month']);
			$payslip['Form16Exists'] = $employeeForm16Details['Form16Exists'];
			$payslip['Form16'] = $employeeForm16Details['Form16'];
            
            $newDeductionArr = array();
            /** hiding TDS row if TDS is 0 **/
            if(count($payslip['Deduction'] > 0))
            {
                $payCyclePmtDeductionAmt = 0;
                $immediatePmtDeductionAmt = 0;
                
                for($d=0;$d<count($payslip['Deduction']);$d++)
                {						
                    if($payslip['Deduction'][$d]['Deduction_Name'] == 'TDS' &&
                       $payslip['Deduction'][$d]['Deduction_Amount'] == 0)
                    {
                        
                    }
                    elseif($payslip['Deduction'][$d]['Deduction_Name'] == 'Advance Salary')
                    {
                        if($payslip['Deduction'][$d]['PaymentType'] == 'Salary Pay-cycle')
                        {
                            $payCyclePmtDeductionAmt += $payslip['Deduction'][$d]['Deduction_Amount'];			
                        }
                        elseif($payslip['Deduction'][$d]['PaymentType'] == 'Immediate')
                        {
                            $immediatePmtDeductionAmt += $payslip['Deduction'][$d]['Deduction_Amount'];			
                        }
                    }
                    else{								
                        array_push($newDeductionArr,$payslip['Deduction'][$d]);
                    }
                }
                
                if($payCyclePmtDeductionAmt > 0)
                {	
                    array_push($newDeductionArr, array('Deduction_Name' => 'Advance Salary - Salary Pay-cycle',
                                                       'Deduction_Amount' => number_format((float)$payCyclePmtDeductionAmt, 2, '.', '')));
                }
                
                if($immediatePmtDeductionAmt > 0)
                {
                    array_push($newDeductionArr, array('Deduction_Name' => 'Advance Salary - Immediate',
                                                       'Deduction_Amount' => number_format((float)$immediatePmtDeductionAmt, 2, '.', '')));
                }
                
                if($newDeductionArr != ''){
                    unset($payslip['Deduction']);
                    $payslip['Deduction'] = $newDeductionArr;
                }
            }
            
            if(count($payslip['Incentive'] > 0))
            {
                $payCyclePmtIncentiveAmt = $immediatePmtIncentiveAmt = $payCycleLoanIncentiveAmt = $immediateLoanIncentiveAmt = 0;
                
                $salaryIncentiveArray = array();
                
                foreach($payslip['Incentive'] as $key=>$val)
                {
                    if($val['Incentive_Name'] == 'Advance Salary')
                    {
                        if($val['PaymentType'] == 'Salary Pay-cycle')
                        {
                            $payCyclePmtIncentiveAmt += $val['Incentive_Amount'];			
                        }
                        elseif($val['PaymentType'] == 'Immediate')
                        {
                            $immediatePmtIncentiveAmt += $val['Incentive_Amount'];			
                        }
                    }
                    elseif($val['Incentive_Name'] == 'Loan')
                    {
                        if($val['PaymentType'] == 'Salary Pay-cycle')
                        {
                            $payCycleLoanIncentiveAmt += $val['Incentive_Amount'];			
                        }
                        elseif($val['PaymentType'] == 'Immediate')
                        {
                            $immediateLoanIncentiveAmt += $val['Incentive_Amount'];			
                        }
                    }
                    else
                    {
                        array_push($salaryIncentiveArray, $payslip['Incentive'][$key]);
                    }
                }
                
                if($payCyclePmtIncentiveAmt > 0)
                {
                    array_push($salaryIncentiveArray, array('Incentive_Name' => 'Advance Salary - Salary Pay-cycle',
                                                            'Incentive_Amount' => number_format((float)$payCyclePmtIncentiveAmt, 2, '.', '')));
                }
                
                if($immediatePmtIncentiveAmt > 0)
                {
                    $payslip['IntermediatePaymentAmt'] += number_format((float)$immediatePmtIncentiveAmt, 2, '.', '');
                }
                
                if($payCycleLoanIncentiveAmt > 0)
                {
                    array_push($salaryIncentiveArray, array('Incentive_Name' => 'Loan - Salary Pay-cycle',
                                                            'Incentive_Amount' => number_format((float)$payCycleLoanIncentiveAmt, 2, '.', '')));
                }
                
                if($immediateLoanIncentiveAmt > 0)
                {
                    $payslip['IntermediatePaymentAmt'] += number_format((float)$immediateLoanIncentiveAmt, 2, '.', '');
                }
                
                $payslip['Incentive'] = $salaryIncentiveArray;
            }
        
            $dbSalary = new Payroll_Model_DbTable_Salary();
            $payslip['salaryDetails'] = $dbSalary->getMonthlyBasicPayAmt($payslip['Payslip']['Employee_Id']);
            //$payslip['orgPfAmt'] = $payslip['etfAmt'] = 0;
            //
            //$empPfExists = 0;
            //foreach($payslip['Deduction'] as $deduction){
            //	if($deduction['Deduction_Name'] == 'Provident Fund' && $deduction['Deduction_Amount'] > 0){
            //		$empPfExists = 1;
            //	}
            //}
            //
            //if($payslip['salaryDetails']['Is_PfEmployee'] && $empPfExists == 1)
            //{
            //	$dbPF = new Payroll_Model_DbTable_ProvidentFund();
            //	$payslip['orgPfAmt'] = $dbPF->calculatePFAmt($payslip['Payslip']['Employee_Id'], $payslip['salaryDetails']['Basic_Pay'], $salaryCalcDetail['Retirals_Based_On_Basic'], $payslip['salaryDetails']['Annual_Gross_Salary']);
            //}
            
            /** Custom Header for org pf amount**/
            $orgPfAmountField = $orgPfCustomHeader = 'Org PF Amount';
            $pfcustomField = $this->_ehrTables->getCustomFields($orgPfAmountField);
            if($pfcustomField){
                $orgPfCustomHeader = (!empty($pfcustomField['New_Field_Name']))	? $pfcustomField['New_Field_Name'] : $orgPfAmountField;
            }
            $payslip['orgPfAmtHeader'] = $orgPfCustomHeader;
        
            //$payslip['orgPfAmt'] = number_format(round($payslip['orgPfAmt'],2),2,'.','');
            //$payslip['InsuranceDetails'] = array();
            //
            //if($payslip['salaryDetails']['Is_InsuranceEmployee'])
            //{
            //	$dbInsurance = new Payroll_Model_DbTable_Insurance();
            //	
            //	$variableOrgInsDetails = $dbInsurance->getVariableOrgInsuranceRecords($payslip['Payslip']['Employee_Id']); 
            //	$variableEmpInsDetails = $dbInsurance->getVariableEmpInsuranceDetails($payslip['Payslip']['Employee_Id']);
            //	
            //	//getting the total allowance amount that includes insurance
            //	$getAllowanceAmt = $dbSalary->getAllowances($payslip['Payslip']['Employee_Id'],NULL,NULL, $payslip['salaryDetails']['Basic_Pay'], 'Insurance');
            //	$basicPlusAllowance = $payslip['salaryDetails']['Basic_Pay'] + $getAllowanceAmt;
            //	
            //	if(count($variableOrgInsDetails)>0 && !is_null($variableOrgInsDetails))
            //	{
            //		foreach($variableOrgInsDetails as $variableOrgIns)
            //		{
            //			if($salaryCalcDetail['Retirals_Based_On_Basic'] == 1)
            //			{
            //				$premium = number_format(round(($variableOrgIns['Org_SharePercent']/100)*($basicPlusAllowance),2),2,'.','');
            //			}
            //			else
            //			{
            //				$premium = number_format(round(($variableOrgIns['Share']/100)*($basicPlusAllowance),2),2,'.','');
            //			}
            //			
            //			array_push($payslip['InsuranceDetails'], array('InsuranceName' => $variableOrgIns['Insurance_Name'],
            //												   'Amount'=> $premium));
            //		}
            //	}
            //	
            //	if(isset($variableEmpInsDetails) && !empty($variableEmpInsDetails))
            //	{
            //		foreach ($variableEmpInsDetails as $variableEmpIns)
            //		{
            //			if(!empty($variableEmpIns['Share']))
            //			{
            //				if($salaryCalcDetail['Retirals_Based_On_Basic'] == 1)
            //				{
            //					$premium = number_format(round(($variableEmpIns['Org_SharePercent']/100)*($basicPlusAllowance),2),2,'.','');
            //				}
            //				else
            //				{
            //					$premium = number_format(round(($variableEmpIns['Share']/100)*($basicPlusAllowance),2),2,'.','');
            //					
            //				}
            //				
            //				array_push($payslip['InsuranceDetails'], array('InsuranceName' => $variableEmpIns['Insurance_Name'],
            //											   'Amount'=> $premium));
            //				
            //			}
            //		}
            //	}
            //}
            
            //if($payslip['salaryDetails']['Is_ETFEmployee'])
            //{
            //	$dbETF = new Payroll_Model_DbTable_ETF();
            //	$payslip['etfAmt'] = $dbETF->calculateETFAmt($payslip['Payslip']['Employee_Id'], $payslip['salaryDetails']['Basic_Pay'], $salaryCalcDetail['Retirals_Based_On_Basic'], $payslip['salaryDetails']['Annual_Gross_Salary']);
            //}
            //
            //$payslip['etfAmt'] = number_format(round($payslip['etfAmt'],0),2,'.','');
            $payslip['orgLWFAmtHeader'] = 'Org LWF Amount';    
            $salary = $this->view->monthlyPayslip = $payslip;
    
            $currency = $this->view->currency = $this->_dbPersonal->currencySymbol($payslip['Payslip']['Employee_Id']);
            $exportStr .= $this->payslipHTML($salary, $currency, 'Monthly','','pdf');
           
            //$pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
            $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'ISO-8859-1', false);
			//$pdf = new MYPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'ISO-8859-1', false);
            
            // set document information
            $pdf->SetCreator(PDF_CREATOR);
            $pdf->SetAuthor('');
            $pdf->SetTitle('Monthly Salary Payslip');
            $pdf->SetSubject('Monthly Salary Payslip');
            $pdf->SetKeywords('monthly, salary, payslip');
            $pdf->SetFont('dejavusans', '', 9,'',true);                        
            
            $lc=array(0,64,128);
            
            // set default monospaced font
            $pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
                    
            //set margins
            $pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
            $pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
            $pdf->SetFooterMargin(PDF_MARGIN_FOOTER);
    
            //set image scale factor
            $pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);

            //set some language-dependent strings
            $pdf->setLanguageArray($lc);						

            // set default font subsetting mode
            $pdf->setFontSubsetting(true);

            // Set font
            // dejavusans is a UTF-8 Unicode font, if you only need to
            // print standard ASCII chars, you can use core fonts like
            // helvetica or times to reduce file size.
            //$pdf->SetFont('helvetica', '', 20, '', false);

             
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(false); 
            //$pdf->AddPage();
            
            $pdf->AddPage('P', 'A4');                        
            
            //set auto page breaks
            $pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);
            
            $pdf->setHeaderFont(Array(PDF_FONT_NAME_MAIN, '', PDF_FONT_SIZE_MAIN));
            $pdf->setFooterFont(Array(PDF_FONT_NAME_DATA, '', PDF_FONT_SIZE_DATA));
            $pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
            $pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
            $pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
            $pdf->SetFooterMargin(PDF_MARGIN_FOOTER);
            $pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_FOOTER);
            $pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);
            $pdf->setFontSubsetting(true);
            
            $pdf->writeHTML($exportStr, true, false, false, false, '');                        
            
            $path = $dirPath."/payslip$j.pdf";
            array_push($files,$path);
            $pdf->Output($path, 'F');  //f,fd,FI
            $pdf = "";
            $exportStr = "";
        }
        
        $pageCount = 0;
        // initiate FPDI
        
        $pdf = new FPDI();
        
        //to remove the unwanted lines in the merged file
        $pdf->setPrintHeader(false);
        $pdf->setPrintFooter(false);
        
        // iterate through the files
        foreach ($files as $file) 
        {
            // get the page count
            $pageCount = $pdf->setSourceFile($file);
            // iterate through all pages
            if($pageCount == 1)
            {
                // import a page
                $templateId = $pdf->importPage(1);
                // get the size of the imported page
                $size = $pdf->getTemplateSize($templateId);
        
                // create a page (landscape or portrait depending on the imported page size)
                if ($size['w'] > $size['h']) {
                    $pdf->AddPage('L', array($size['w'], $size['h']));
                } else {
                    $pdf->AddPage('P', array($size['w'], $size['h']));
                }
        
                // use the imported page
                $pdf->useTemplate($templateId);
        
                //$pdf->SetFont('Helvetica');
                //$pdf->SetXY(5, 5);
                //$pdf->Write(8, 'Generated by TCPDF');
            }
            else
            {
                for ($pageNo = 1; $pageNo <= $pageCount; $pageNo++) {
                    // import a page
                    $templateId = $pdf->importPage($pageNo);
                    
                    // get the size of the imported page
                    $size = $pdf->getTemplateSize($templateId);

                    // create a page (landscape or portrait depending on the imported page size)
                    if ($size['w'] > $size['h']) {
                        $pdf->AddPage('L', array($size['w'], $size['h']));
                    } else {
                        $pdf->AddPage('P', array($size['w'], $size['h']));
                    }

                    // use the imported page
                    $pdf->useTemplate($templateId);

                    //$pdf->SetFont('Helvetica');
                    //$pdf->SetXY(5, 5);
                    //$pdf->Write(8, 'Generated by TCPDF');
                }
            }
            unlink($file);
        }
        $pdf->Output($dirPath.'/Payslip.pdf', 'F');

        return true;
    }
    
	//version 0.5=>added leave names as note in payslip pdf and payslip mail communication
	               //fixed the mail comm logo path issue
	public function payslipHTML($salary, $currency, $salStruct,$mailComm = NULL,$format)
	{
        // require_once('tcpdf/config/lang/eng.php');
		// require_once('tcpdf/tcpdf.php');

		require_once realpath(APPLICATION_PATH . '/../vendor/tecnickcom/tcpdf/examples/tcpdf_include.php');
		require_once realpath(APPLICATION_PATH . '/../vendor/tecnickcom/tcpdf/examples/lang/eng.php');
		require_once realpath(APPLICATION_PATH . '/../vendor/tecnickcom/tcpdf/tcpdf.php');
		

		$orgCode = $this->_ehrTables->getOrgCode();
		$img = '';
		$dbOrgSettings = new Organization_Model_DbTable_OrgSettings();
		$orgInfoView   = $dbOrgSettings->viewOrgDetail($orgCode);
		$leaveDetail   = '';
		
		$reportLogo  = $this->_dbCommonFun->getAwsSignedUrl('ReportLogo','','logoBucket');
		
		if($reportLogo !== ''){
			if(is_null($mailComm) && $format =='pdf')
			{				
				$img = '<img style="padding-left: 50px;" height = "70px" width = "70px" src="'.$reportLogo.'"/>';
			}
			else if($format == 'print')
			{				
				$basePath = new Zend_View_Helper_BaseUrl();
				$img = '<img class="reportLogoSize" style="padding-left: 1%;" src="'.$reportLogo.'"/>';
			}
			else
			{				
				$img = '<img style="padding-left: 1%;" height = "70px" width = "70px" src="'.$reportLogo.'"/>';					
			}			
		}
		
		if(!empty($salary))
		{
			$currencySymbol = !empty($currency) ? '('.$currency.')' : '';
			$pl = (!empty($salary['PaidLeave'][1]) ? $salary['PaidLeave'][1] : 0);
			if(count($salary['PaidLeave'][2]) > 0)
			{
				
				$pl .= " (";
				foreach($salary['PaidLeave'][2] as $paidLeave)
				{
					
					//$pl .= ucfirst(substr($paidLeave[0],0,1))."L Bal:".floatval($paidLeave[1])." ";//number_format(round($salary['Payslip']['Total_Salary'],0),2,'.','')
					//$leaveDetail .= ucfirst(substr($paidLeave[0],0,1))."L - ".$paidLeave[0].", ";
					
					$pLeave = explode(' ',$paidLeave[0]);
					$lName = '';
					
					if(count($pLeave)>1){						
						foreach($pLeave as $PL){
							$lName .= subStr($PL,0,1);
						}						
					}
					else{						
						$lName .= subStr($paidLeave[0],0,2);
					}
					
					$pl .= ucfirst($lName)." Bal:".floatval($paidLeave[1])." ";//number_format(round($salary['Payslip']['Total_Salary'],0),2,'.','')
					$leaveDetail .= ucfirst($lName)." - ".$paidLeave[0].", ";
				}
				$pl .= " )";
			}
            
			$upl = (!empty($salary['UnpaidLeave'][1]) ? $salary['UnpaidLeave'][1] : 0);			
			if(count($salary['UnpaidLeave'][2]) > 0)
			{
				$upl .= " (";
				foreach($salary['UnpaidLeave'][2] as $unPaidLeave)
				{
                
					//$upl .= ucfirst(substr($unPaidLeave[0],0,1))."L Bal:".floatval($unPaidLeave[1])." ";
					//$leaveDetail .= ucfirst(substr($unPaidLeave[0],0,1))."L - ".$unPaidLeave[0].", ";
					
					$uLeave = explode(' ',$unPaidLeave[0]);
					$uName = '';
					if(count($uLeave)>1){						
						foreach($uLeave as $ul){
							$uName .= subStr($ul,0,1);
						}						
					}
					else{
						$uName .= subStr($unPaidLeave[0],0,2);						
					}
					
					$upl .= ucfirst($uName)." Bal:".floatval($unPaidLeave[1])." ";
					$leaveDetail .= ucfirst($uName)." - ".$unPaidLeave[0].", ";
                
				}
				$upl .= " )";
                
			}
			$salarymonth = explode(',',$salary['Payslip']['Salary_Month']);
			$month = array('1'=>'January', '2'=>'February', '3'=>'March', '4'=>'April', '5'=>'May', '6'=>'June', '7'=>'July',
                            '8'=>'August', '9'=>'September', '10'=>'October', '11'=>'November', '12'=>'December');
			
			$empPan = (!empty($salary['Emp_Pan'])) ? $salary['Emp_Pan'] : '-';
			
			if($format == 'print')
				$fnt = 12;
			else
				$fnt = 11;
                
			if($format == 'print')
            {
                $payslipHead = "font-weight: bold;font-size: 14px;text-align:center;margin:0% 0% 0% 15%;";//;text-align:center
                $addrsty = "margin:0% 0% 0% 15%;";
                $wid = "width:70%;padding-left:20%;";
            }
            else
            {
                $payslipHead = "font-weight: bold;font-size: 14px;text-align:center;padding-left:28px;";//;text-align:center
                $addrsty = "padding-left:25px;padding-left:30%;";
                $wid = "width:80%;";
            }
			
            $totalIncentive = 0;
			$totalDeduction = 0;
			$payslipcss = 'width:101%;border-collapse: collapse;border-spacing: none;font-size:'.$fnt. 'px;border-left: 1px solid black;border-right: 1px solid black;border-bottom: none;border-top: 1px solid black;';
			$payslipTabthodd = "padding-left: 1%;padding-right: 1%;height: 25px;text-align: left;border-bottom: 1px solid black;border-top: 1px solid black;width:38%;font-weight:bold;";
			$contributionodd = "padding-left: 1%;padding-right: 1%;height: 25px;text-align: left;border-bottom: 1px solid black;border-top: 1px solid black;width:38%;";
			$payslipTabtheven = "padding-left: 1%;padding-right: 1%;height: 25px;text-align: right;border-bottom: 1px solid black;border-right: 1px solid black;border-top: 1px solid black;width:12%;font-weight:bold;";
			$contributionTabEven = "padding-left: 1%;padding-right: 1%;height: 25px;text-align: right;border-bottom: 1px solid black;border-right: 1px solid black;border-top: 1px solid black;width:12%;";
			if(!empty($salary['Form16Exists']) && !empty($salary['Form16'])){
				$noteCSS = "padding-left: 1%;padding-right: 1%;height: 25px;text-align: left;border-bottom: 1px solid black;border-left: 1px solid black;border-right: 1px solid black;";
			}
			else{
				$noteCSS = "padding-left: 1%;padding-right: 1%;height: 25px;text-align: left;border-bottom: 1px solid black;border-top: 1px solid black;";
			}
			$payslipTabtdodd = "height: 25px;padding-left: 1%;padding-right: 1%;";
			$payslipTabtdeven = "text-align: right;border-right: 1px solid black;height: 25px;padding-left: 1%;padding-right: 1%;";
			$paysliptrtdodd = "height: 25px;padding-left: 1%;padding-right: 1%;padding-top:2%;width:20%;";
			$paysliptrtdeven = "height: 25px;padding-left: 1%;padding-right: 1%;padding-top:2%;width:30%;";
			$payslipHead = "font-weight: bold;font-size: 13px;text-align:center;";//;text-align:center			
			
            $exportStr = '';   
			$exportStr .= '<div style = "width: 99%;page-break-inside: avoid;margin-top:-2%;">';
			$exportStr .= '<table align = \'center\' style = "'.$payslipcss.'margin-left:0%;border-bottom: none;page-break-inside: avoid;">';
			$exportStr .= '<tr><td colspan = "2" align = "center"><p style ="font-size:12px;font-weight:bold;text-align:center;" align = "center">Payslip for the Month of '.$month[$salarymonth[0]] . " - " . $salarymonth[1]. "</p></td></tr>";
			$exportStr .= '<tr><td align = "left" style = "width:30%;padding-top:1%">'.$img.'</td>';
			
		//If show payslip address flag is enabled, then only show the organization address in payslip
		$payslipAddressFlag = $this->_dbPayslip->getSettingsValues(array('Display_Payslip_Address'), 'fetchOne');
		if($payslipAddressFlag == 1 )
		{
            $exportStr .= '<td style = "width:70%;"  align = "right"><div style = ""></div><div style = "text-align:right;padding-right:20px">';
			$exportStr .= '<div  style="'. $payslipHead .'text-align:right;">'.$orgInfoView['Org_Name'].'</div>';
			$exportStr .= '<address style ="font-size: 12px;">'. $salary['Payslip']['Street1'].', '. $salary['Payslip']['Street2'];
			
            if($salary['Payslip']['City'] && $salary['Payslip']['State'] && $salary['Payslip']['Country'] && $salary['Payslip']['Pincode'])
                $exportStr .= '<br>'.$salary['Payslip']['City'].', '.$salary['Payslip']['State'].', '. $salary['Payslip']['Country'].' - '.$salary['Payslip']['Pincode'].'</address></div></td></tr>';
            else if($salary['Payslip']['State'] && $salary['Payslip']['Country'] && $salary['Payslip']['Pincode'])
                $exportStr .= '<br>'.$salary['Payslip']['State'].', '. $salary['Payslip']['Country'].' - '.$salary['Payslip']['Pincode'].'</address></div></td></tr>';
            else if($salary['Payslip']['Country'] && $salary['Payslip']['Pincode'])
                $exportStr .= '<br>'.$salary['Payslip']['Country'].' - '.$salary['Payslip']['Pincode'].'</address></div></td></tr>';
            else
                $exportStr .= '<br>'.$salary['Payslip']['Country'].'</address></div></td></tr>';			
		}
		else
		{
			$exportStr .= '<td style = "width:70%;"  align = "right"><div style = ""></div><div style = "text-align:right;padding-right:20px"></div></td></tr>';
		}    
		$empMiddleName = !empty($salary['Payslip']['Emp_Middle_Name']) ? ($salary['Payslip']['Emp_Middle_Name']. " ") : "";
		$exportStr .= '<tr><td></td><td></td></tr></table>';
		$exportStr .= '<table style = "'.$payslipcss.'border-bottom:none;border-top:none">';
		$exportStr .= '<tr><td style="'. $paysliptrtdodd .'">Employee Id</td><td style="'. $paysliptrtdeven .'">: '.$salary['Payslip']['User_Defined_EmpId'] .'</td><td style="'. $paysliptrtdodd .'">Employee Name</td><td style="'. $paysliptrtdeven .'">: '.$salary['Payslip']['Emp_First_Name'] . " ".$empMiddleName . $salary['Payslip']['Emp_Last_Name'].'</td></tr>';
		$exportStr .= '<tr><td style="'. $paysliptrtdodd .'">Department</td><td style="'. $paysliptrtdeven .'">: '.$salary['Payslip']['Department_Name'].'</td><td style="'. $paysliptrtdodd .'">Designation</td><td style="'. $paysliptrtdeven .'">: '.$salary['Payslip']['Designation_Name'] .'</td></tr>';
		$exportStr .= '<tr><td style="'. $paysliptrtdodd .'">Date Of Join</td><td style="'. $paysliptrtdeven .'">: '.$salary['Payslip']['Date_Of_Join'].'</td><td style="'. $paysliptrtdodd .'">ESI/Insurance No</td><td style="'. $paysliptrtdeven .'">: '.(!empty($salary['Payslip']['Policy_No']) ? $salary['Payslip']['Policy_No'] : " -").'</td></tr>';
		$exportStr .= '<tr><td style="'. $paysliptrtdodd .'">PF Number</td><td style="'. $paysliptrtdeven .'">: '.(!empty($salary['Payslip']['Pf_PolicyNo']) ? $salary['Payslip']['Pf_PolicyNo'] : " -").'</td>';
		$exportStr .= '<td style="'. $paysliptrtdodd .'">PF UAN No</td><td style="'. $paysliptrtdeven .'">: '.(!empty($salary['Payslip']['UAN'])? $salary['Payslip']['UAN'] :"-").'</td></tr>';        

		$exportStr .= '<tr><td style="'. $paysliptrtdodd .'">PAN Number</td><td style="'. $paysliptrtdeven .'">: '.$empPan.'</td><td style="'. $paysliptrtdodd .'">Bank Name</td><td style="'. $paysliptrtdeven .'">: '.(!empty($salary['Payslip']['Bank_Name']) ?$salary['Payslip']['Bank_Name']:"-").'</td></tr>';
		$exportStr .= '<tr><td style="'. $paysliptrtdodd .'">Bank Account Number</td><td style="'. $paysliptrtdeven .'">: '.(!empty($salary['Payslip']['Bank_Account_Number']) ? $salary['Payslip']['Bank_Account_Number']: "-").'</td><td style="'. $paysliptrtdodd .'">Paid Leave</td><td style="'. $paysliptrtdeven .'">: '.$pl.'</td></tr>';

		if($salStruct == 'Monthly')
			$exportStr .= '<tr><td style="'. $paysliptrtdodd .'">Unpaid Leave</td><td style="'. $paysliptrtdeven .'">: '.$upl.'</td><td style="'. $paysliptrtdodd .'">Days Worked</td><td style="'. $paysliptrtdeven .'">: '.$salary['WorkedDays'].'</td></tr>';
		else
		{
			$exportStr .= '<tr><td style="'. $paysliptrtdodd .'">Unpaid Leave</td><td style="'. $paysliptrtdeven .'">: '.$upl.'</td><td style="'. $paysliptrtdodd .'">Hours Worked</td><td style="'. $paysliptrtdeven .'">: '.$salary['WorkedHours'].'</td></tr>';
			$exportStr .= '<tr><td style="'. $paysliptrtdodd .'">Days Worked</td><td style="'. $paysliptrtdeven .'">: '.$salary['actualHours']['daysWorked'].'</td><td style="'. $paysliptrtdodd .'">Day Wages</td><td style="'. $paysliptrtdeven .'">: '.$salary['actualHours']['dayWage'].'</td></tr>';
			//display holidays count
			$exportStr .= '<tr><td style="'. $paysliptrtdodd .'">OT Hours</td><td style="'. $paysliptrtdeven .'">: '.(!empty($salary['OverTimeHours']) ?$salary['OverTimeHours']:"-").'</td><td style="'. $paysliptrtdodd .'">Holidays</td><td style="'. $paysliptrtdeven .'">: '.$salary['Holiday'].'</td></tr>';
		}
			
			
			$exportStr .= '</table>';
			$exportStr .= '<table style = "'.$payslipcss.'border-left: 1px solid black;border-right: 1px solid black;border-bottom: none;border-top: 1px solid black;float:left;">';
			$exportStr .= '<tr><th style="'. $payslipTabthodd .'">Earnings</th><th style="'. $payslipTabtheven .'">Amount'.$currencySymbol.'</th><th style="'. $payslipTabthodd.'">Deductions</th><th style="'. $payslipTabtheven.'">Amount'. $currencySymbol.'</th></tr>';
			$count = max(array(count($salary['Incentive']),count($salary['Deduction'])));
		
        	
				
            for($i = 0;$i< $count;$i++)
			{
                if(!empty($salary['Incentive'][$i]['Incentive_Name']))
				{
					$totalIncentive += $salary['Incentive'][$i]['Incentive_Amount'];
					$exportStr .= '<tr><td style="'. $payslipTabtdodd .'">';
					if(!empty($salary['Incentive'][$i]['Description']) && ($salary['Incentive'][$i]['Incentive_Name'] != 'Provident Fund' && $salary['Incentive'][$i]['Incentive_Name'] != 'Insurance' ))
					{
						$salary['Incentive'][$i]['Incentive_Name'] = /*$salary['Incentive'][$i]['Incentive_Name'].' - '.*/$salary['Incentive'][$i]['Description'];
					}
                    else if($salary['Incentive'][$i]['Incentive_Name'] == 'Provident Fund')
                    {
                        $salary['Incentive'][$i]['Incentive_Name'] = 'Allowance - Other';
                    }
                    else if( $salary['Incentive'][$i]['Incentive_Name'] == 'Variable Insurance' )
                    {
                        $salary['Incentive'][$i]['Incentive_Name'] = 'Allowance - Insurance';
                    }
                        
					$exportStr .= $salary['Incentive'][$i]['Incentive_Name'] .'</td><td style="'. $payslipTabtdeven .'">'.$salary['Incentive'][$i]['Incentive_Amount'].'</td>';
				}
				else {
					$exportStr .= '<tr><td style="'. $payslipTabtdodd .'"></td><td style="'. $payslipTabtdeven .'"></td>';
				}
                
				if(!empty($salary['Deduction'][$i]['Deduction_Name']))
				{
					$totalDeduction += $salary['Deduction'][$i]['Deduction_Amount'];
					$exportStr .= '<td style="'. $payslipTabtdodd .'">';
                    
					if(!empty($salary['Deduction'][$i]['Description']))
					{
						$salary['Deduction'][$i]['Deduction_Name'] = /*$salary['Deduction'][$i]['Deduction_Name'].' - '.*/$salary['Deduction'][$i]['Description'];
					}
					$exportStr .= $salary['Deduction'][$i]['Deduction_Name'].'</td><td style="'. $payslipTabtdeven .'">'.$salary['Deduction'][$i]['Deduction_Amount'].'</td></tr>';
				}
				else {
					$exportStr .= '<td style="'. $payslipTabtdodd .'"></td><td style="'. $payslipTabtdeven .'"></td></tr>';
				}
			}
			$exportStr .= '<tr><th style="'. $payslipTabthodd .'">Total Earnings</th><th style="'. $payslipTabtheven .'">'.number_format(round($totalIncentive,2),2).'</th><th style="'. $payslipTabthodd .'">Total Deductions</th><th style="'. $payslipTabtheven .'">'.number_format(round($totalDeduction,0),2).'</th></tr>';
			
			if($salStruct == 'Monthly')
			{
				if($salary['IntermediatePaymentAmt'] > 0)
				$exportStr .= '<tr><th style="'. $payslipTabthodd .'">Total Intermediate Payment</th><th style = "'. $payslipTabtheven .'">'.number_format(round($salary['IntermediatePaymentAmt'],0),2,'.','').'</th><th style="'. $payslipTabthodd .'"></th><th style="'. $payslipTabtheven .'"></th></tr> ';
				$exportStr .= '<tr><th style="'. $payslipTabthodd .'">Outstanding Amount</th><th style = "'. $payslipTabtheven .'">'.number_format(round($salary['Payslip']['Outstanding_Amt'],0),2,'.','').'</th><th style="'. $payslipTabthodd .'">Netpay</th><th style="'. $payslipTabtheven .'">'.$salary['Payslip']['Incentive_Amount'].'</th></tr> ';
			}
			else
			{
				$exportStr .= '<tr><th style="'. $payslipTabthodd .'">Outstanding Amount</th><th style = "'. $payslipTabtheven .'">'.number_format(round($salary['Payslip']['Outstanding_Amt'],0),2,'.','').'</th><th style="'. $payslipTabthodd .'">Netpay</th><th style="'. $payslipTabtheven .'">'.$salary['Payslip']['Total_Salary'].'</th></tr> ';
			}
			$exportStr .= '</table>';
			// adding netpay in words
			$exportStr .= '<table style = "'.$payslipcss.'border-left: 1px solid black;border-right: 1px solid black;border-bottom: 1px solid black;border-top: none;float:left;"><tr><td style="padding-left:18px;font-weight:bold">Netpay In Words : '.$currency.' '.$salary['Incentive_Amount_Words'].' Only</td></tr></table>';
			
			if($salStruct == 'Monthly')
			{

				if($salary['salaryDetails']['Is_PfEmployee'] == 1 || $salary['salaryDetails']['Is_InsuranceEmployee'] == 1 || $salary['salaryDetails']['Is_ETFEmployee'] == 1 || $salary['orgLwfAmount'] > 0)
				{
					// assign empty orgshare array
					$orgShareDetails = array();

					// check orgPfAmt is not empty 
					if($salary['orgPfAmt'] > 0){
						array_push($orgShareDetails, array('orgShareName' => $salary['orgPfAmtHeader'], 'Amount' => $salary['orgPfAmt']));
						// check adminCharge is not empty 
						if($salary['adminCharge'] > 0){
							array_push($orgShareDetails, array('orgShareName' => 'Admin Charge', 'Amount' => $salary['adminCharge']));
						}
						// check edliCharge is not empty 
						if($salary['edliCharge'] > 0){
							array_push($orgShareDetails, array('orgShareName' => 'EDLI Charge', 'Amount' => $salary['edliCharge']));
						}
					} 
					// check etfAmt is not empty 
					if($salary['etfAmt'] > 0){
						array_push($orgShareDetails, array('orgShareName' => 'Org ETF Amount', 'Amount' => $salary['etfAmt']));
					}
					// check orgLwfAmount is not empty 
					if($salary['orgLwfAmount'] > 0){
						array_push($orgShareDetails, array('orgShareName' => $salary['orgLWFAmtHeader'], 'Amount' => $salary['orgLwfAmount']));
					}
					// check InsuranceDetails is not empty 
					if(!empty($salary['InsuranceDetails'])) {
						// Push InsuranceDetails in insuranceDetails array
						foreach($salary['InsuranceDetails'] as $insurance){
							array_push($orgShareDetails, array('orgShareName' => $insurance['InsuranceName'], 'Amount' => $insurance['Amount']));
						}
					}
					// check FixedHealthInsurance is not empty
					if(!empty($salary['FixedHealthInsurance']))
					{
						// push FixedHealthInsurance in insuranceDetails array
						foreach($salary['FixedHealthInsurance'] as $FixedHealthInsurance){
							array_push($orgShareDetails, array('orgShareName' => $FixedHealthInsurance['Insurance_Name'], 'Amount' => $FixedHealthInsurance['Insurance_Amount']));
						}
					}

					// find maximum of orgShareDetails and insurance details
					$orgShareMaxCount = count($orgShareDetails);

					if($orgShareMaxCount > 0){
						$contributioncolLStyle = "height:25px;padding-left:1%;text-align:left;border-bottom: 1px solid black;font-weight:bold;";
						$contributioncolRStyle = "height:25px;padding-right:1%;text-align:right;border-bottom: 1px solid black;";
						$exportStr .= '<table style = "'.$payslipcss.'border-left: 1px solid black;border-right: 1px solid black;border-bottom: none;border-top: none;float:left;"><tr><td style="height:25px;border-bottom: 1px solid black;"></td><td style="border-bottom: 1px solid black;"></td><td style="border-bottom: 1px solid black;"></td><td style="border-bottom: 1px solid black;"></td></tr><tr><th colspan="4" style="'.$contributioncolLStyle.'">Organization Contribution </th></tr>';

						for($i=1;$i<$orgShareMaxCount+1; $i++)
						{
							// the structure is <tr><td></td><td></td><td></td><td></td></tr>.
							//  Example: consider orgsahremaxcount is 2. 
							//  if i == 1  add <tr>, then two td tags are added.
							//  if i == 2 add two td tags, then finaly add </tr>
							if($i % 2 != 0){
								$exportStr .= '<tr>';
							}
							// check orgShareDetails is exist, push orgShareDetails cell
							if (array_key_exists($i-1,$orgShareDetails)){
								$exportStr .= '<td style="'. $contributionodd .'">'.$orgShareDetails[$i-1]['orgShareName'].'</td><td style = "'. $contributionTabEven .'">'.$orgShareDetails[$i-1]['Amount'].'</td>';
							} 
							// add <tr> in even position
							if($i % 2 == 0 ){
								$exportStr .= '</tr>';
							}						
						}
						// max count is add, only three cells are present in table. so for this add extra empty cell in last position.
						if($orgShareMaxCount % 2 != 0){
							$exportStr .= '<td style="'. $contributionodd .'"></td><td style = "'. $contributionTabEven .'"></td></tr>';
						}
						$exportStr .= '</table>';
					}
				}
			}
			else{
				if($salary['salaryDetails']['Is_Pf'] == 1 || $salary['salaryDetails']['Is_Insurance'] == 1 )
				{
					// assign empty orgshare array
					$orgShareDetails = array();

					// check orgPfAmt is not empty 
					if($salary['orgPfAmt'] > 0){
						array_push($orgShareDetails, array('orgShareName' => $salary['orgPfAmtHeader'], 'Amount' => $salary['orgPfAmt']));
						// check adminCharge is not empty 
						if($salary['adminCharge'] > 0){
							array_push($orgShareDetails, array('orgShareName' => 'Admin Charge', 'Amount' => $salary['adminCharge']));
						}
						// check edliCharge is not empty 
						if($salary['edliCharge'] > 0){
							array_push($orgShareDetails, array('orgShareName' => 'EDLI Charge', 'Amount' => $salary['edliCharge']));
						}
					} 
					// check InsuranceDetails is not empty 
					if(!empty($salary['InsuranceDetails'])) {
						// Push InsuranceDetails in insuranceDetails array
						foreach($salary['InsuranceDetails'] as $insurance){
							array_push($orgShareDetails, array('orgShareName' => $insurance['InsuranceName'], 'Amount' => $insurance['Amount']));
						}
					}

					// find maximum of orgShareDetails and insurance details
					$orgShareMaxCount = count($orgShareDetails);

					if($orgShareMaxCount > 0){
						$contributioncolLStyle = "height:25px;padding-left:1%;text-align:left;border-bottom: 1px solid black;font-weight:bold;";
						$contributioncolRStyle = "height:25px;padding-right:1%;text-align:right;border-bottom: 1px solid black;";
						$exportStr .= '<table style = "'.$payslipcss.'border-left: 1px solid black;border-right: 1px solid black;border-bottom: none;border-top: none;float:left;"><tr><td style="height:25px;border-bottom: 1px solid black;"></td><td style="border-bottom: 1px solid black;"></td><td style="border-bottom: 1px solid black;"></td><td style="border-bottom: 1px solid black;"></td></tr><tr><th colspan="4" style="'.$contributioncolLStyle.'">Organization Contribution </th></tr>';

						for($i=1;$i<$orgShareMaxCount+1; $i++)
						{
							// the structure is <tr><td></td><td></td><td></td><td></td></tr>.
							//  Example: consider orgsahremaxcount is 2. 
							//  if i == 1  add <tr>, then two td tags are added.
							//  if i == 2 add two td tags, then finaly add </tr>
							if($i % 2 != 0){
								$exportStr .= '<tr>';
							}
							// check orgShareDetails is exist, push orgShareDetails cell
							if (array_key_exists($i-1,$orgShareDetails)){
								$exportStr .= '<td style="'. $contributionodd .'">'.$orgShareDetails[$i-1]['orgShareName'].'</td><td style = "'. $contributionTabEven .'">'.$orgShareDetails[$i-1]['Amount'].'</td>';
							} 
							// add <tr> in even position
							if($i % 2 == 0 ){
								$exportStr .= '</tr>';
							}						
						}
						// max count is add, only three cells are present in table. so for this add extra empty cell in last position.
						if($orgShareMaxCount % 2 != 0){
							$exportStr .= '<td style="'. $contributionodd .'"></td><td style = "'. $contributionTabEven .'"></td></tr>';
						}
						$exportStr .= '</table>';
					}
				}
			}
			//for displaying leave names at the bottom of the payslip (as note)
			$notestd = '';
		    if(!empty($leaveDetail))
			{ 
				$notestd .= '<tr><td colspan= "4" style = "'.$noteCSS.'">';
			    $notestd .= "Note: ";
				$notestd .= rtrim($leaveDetail,', ');
				$notestd .= '</td></tr>';
			    
			}
			
			/** Form16 summary **/
			if($salStruct == 'Monthly'){
				if(!empty($salary['Form16Exists']) && !empty($salary['Form16'])){
					
					$form16colLStyle = "height:25px;padding-left:1%;text-align:left;border-bottom: 1px solid black;font-weight:bold;";
					$form16colRStyle = "height:25px;padding-right:1%;text-align:right;border-bottom: 1px solid black;font-weight:bold;";
					$form16coltdStyle = "height:25px;padding-left:1%;text-align:left;";
					$form16coltdRStyle = "height:25px;padding-right:1%;text-align:right;";					
					
					$exportStr .= '<table style = "'.$payslipcss.'border-left: 1px solid black;border-right: 1px solid black;border-bottom: none;border-top: none;float:left;">';
					$exportStr .= '<tr><td style="height:25px;border-bottom: 1px solid black;"></td><td style="border-bottom: 1px solid black;"></td><td style="border-bottom: 1px solid black;"></td><td style="border-bottom: 1px solid black;"></td></tr>';
					$exportStr .= '<tr><th style="'.$form16colLStyle.'width:25%;">Perks/ Other Income/ Exemptions/ Rebate</th><th style="'.$form16colRStyle.'border-right: 1px solid black;">Amount'.$currencySymbol.'</th><th style="'.$form16colLStyle.'">Form16 Summary</th><th style="'.$form16colRStyle.'">Amount'.$currencySymbol.'</th></tr>';
					$form16Det = $salary['Form16'];
					$sec10Exemption = array();
					
					$form16TaxDetailsCount = count($form16Det['form16TaxDetails']);
					$sec10ExemptionCount = $form16TaxDetailsCount + count($form16Det['Sec10Exemption']);
					$form16SummaryCount = count($form16Det['form16Summary']);
					
					/** Push sec 10 exemption header **/
					array_push($form16Det['form16TaxDetails'],array('Sec 10 Exemption','Amount'));
					
					/** Push all sec 10 exemption details in an array **/
					for($e = 0;$e<count($form16Det['Sec10Exemption']);$e++)
					{
						array_push($form16Det['form16TaxDetails'],$form16Det['Sec10Exemption'][$e]);						
					}
					
					$form16divCount = max($form16SummaryCount,$sec10ExemptionCount);					
					
					for($i = 0;$i< $form16divCount;$i++)
					{	
						$lasttdStyle = '';
						if($i == $form16divCount-1){
							$lasttdStyle = 'border-bottom: 1px solid black;';
						}
						
						$exportStr .= '<tr>';
						
						/** Perks/Other Income/Exemptions/Rebates td **/
						$exemptionName = $exemptionAmount = '';
						if(!empty($form16Det['form16TaxDetails'][$i])){
							
							/** Perks/Other Income/Exemptions/Rebates td **/
							if(!empty($form16Det['form16TaxDetails'][$i]['ExemptionName']))
							{
								$exemptionName = $form16Det['form16TaxDetails'][$i]['ExemptionName'];
								$exemptionAmount = $form16Det['form16TaxDetails'][$i]['ExemptionAmount'];
							}							
							else{
								if($i==$form16TaxDetailsCount){
									$exemptionName = 'Sec 10 Exemption';
									$exemptionAmount = 'Amount';
								}
							}
							
							/** Section 10 Exemption details **/
							if(!empty($form16Det['form16TaxDetails'][$i]['Investment_Category']))
							{
								$exemptionName = $form16Det['form16TaxDetails'][$i]['Investment_Category'];// Section_Name
								$exemptionAmount = $form16Det['form16TaxDetails'][$i]['ExemptAmt'];// Section_Amount								
							}
						}
						else{
							if($i==$form16TaxDetailsCount){
									$exemptionName = 'Sec 10 Exemption';
									$exemptionAmount = 'Amount';
								}
						}
						
						if(!empty($exemptionName) && $i!=$form16TaxDetailsCount){
							$exportStr .= '<td style="'. $form16coltdStyle . $lasttdStyle .'">'.$exemptionName.'</td><td style="'. $form16coltdRStyle . $lasttdStyle .'">'.$exemptionAmount.'</td>';
						}
						elseif(!empty($exemptionName) && $i==$form16TaxDetailsCount){
							$exportStr .= '<th style="'.$form16colLStyle.'width:25%;border-top:1px solid black;">'.$exemptionName.'</th><th style="'.$form16colRStyle.'width:25%;border-top:1px solid black;">Amount'.$currencySymbol.'</th>';
						}
						else{
							$exportStr .= '<td style="'. $form16coltdStyle . $lasttdStyle. '"></td><td style="'. $form16coltdRStyle . $lasttdStyle. 'border-right: 1px solid black;"></td>';							
						}
						
						/** Form16 Summary td **/
						$summaryName = $summaryAmount = '';
						if(!empty($form16Det['form16Summary'][$i]['SummaryName']))
						{							
							$summaryName = $form16Det['form16Summary'][$i]['SummaryName'];
							$summaryAmount = $form16Det['form16Summary'][$i]['SummaryAmount'];
						}
							
						if(!empty($summaryName)){
							$exportStr .= '<td style="'. $form16coltdStyle . $lasttdStyle .'border-left: 1px solid black;">'.$summaryName.'</td><td style="'. $form16coltdRStyle . $lasttdStyle .'">'.$summaryAmount.'</td></tr>';	
						}
						else{							
							$exportStr .= '<td style="'. $form16coltdStyle . $lasttdStyle .'border-left: 1px solid black;"></td><td style="'. $form16coltdRStyle . $lasttdStyle .'"></td></tr>';
						}
					}
					
					$exportStr .= $notestd;					
					$exportStr .= '</table>';
				}
				else{
					$exportStr .= $notestd;
				}
			}
			else{
				$exportStr .= $notestd;
			}
            $exportStr .= '</div>';
			
            //$payslipcss = 'width:96%;border-collapse: collapse;border-spacing: none;font-size:'.$fnt. 'px;margin: 0% 1% 0% 2%;border-left: 1px solid black;border-right: 1px solid black;border-bottom: none;border-top: 1px solid black;';
            $exportStr .= '<table align = \'center\' style = "width:99%;border-collapse: collapse;border-spacing: none;font-size:'.$fnt. 'px;page-break-inside: avoid">';
            $exportStr .= '<tr><td colspan = "2" align = "right"><div style="text-align:right;border: none;font-size: 10px;">';
            $orgCode = $this->_ehrTables->getOrgCode();
		
		$dbOrgDetail = new Organization_Model_DbTable_OrgSettings();
		$showReporCreator = $dbOrgDetail->viewOrgDetail($orgCode);
		
		if($showReporCreator['Show_Report_Creator']==1)
		{
			$middleName = !empty($salary['Payslip']['Middle_Name']) ? ($salary['Payslip']['Middle_Name']. ' ') : '';
			$exportStr .= 'Report generated by '. $salary['Payslip']['First_Name'] .' '.$middleName .$salary['Payslip']['Last_Name'] .' on '. $salary['Payslip']['Generated_On'].'</div></td></tr><br>';
		}
		else
		{
			$exportStr.='</div></td></tr>';
			//'</div></td></tr></table>';
     		//$exportStr.='<div style="text-align:center; font-size:10px; margin-top:10px"> This is system generated report. No signature is required.</div></div></td></tr></table>';
		}
		$exportStr .='<tr><td colspan = "2" align = "center"><div style="text-align:center; font-size:10px; margin-top:10px">'."This is system generated report. No signature is required".'</div></td></tr></table>';
		
		$exportStr .= '<div style=\'page-break-after:always;\'>&nbsp;</div><br>';	   
		
		$exportStr .='<br><br><br>';			
		}
		return $exportStr;
	}
	
	
    public function exportPayslipCsvAction()
    {
     	$this->_helper->layout()->disableLayout();
        if(isset($_SERVER['HTTP_REFERER']))
        {			
			$payslipMonth      = $this->_getParam('Payslip_Month', null);
			$reportTitle       = $this->_getParam('Report_Title', null);

			if($reportTitle=='Consolidated Salary Statement')
			{
				$groupById = $this->_getParam('Group_By_Id', null);
				if($groupById=='Business_Unit_Id')
				{
					$groupByName = 'Business_Unit';
				}
				else if($groupById=='Location_Id')
				{
					$groupByName = 'Location_Name';
				}
				else if($groupById=='Department_Id')
				{
					$groupByName = 'Department_Name';
				}
				else if($groupById=='Designation_Id')
				{
					$groupByName = 'Designation_Name';
				}
				else if($groupById=='EmpType_Id')
				{
					$groupByName = 'Employee_Type';
				}
			}
			else
			{
				$groupById         = '';
				$groupByName 	   = '';
			}

			$businessUnitId    = $this->_getParam('Business_Unit_Id', null);
			$employeeTypeId    = $this->_getParam('Employee_Type_Id', null);
			$businessUnitId    = explode(',', $businessUnitId);
			$employeeTypeId    = explode(',', $employeeTypeId);
			$businessUnitId = array_filter($businessUnitId, function($value) {
				return !empty($value);
			});

			$employeeTypeId = array_filter($employeeTypeId, function($value) {
				return !empty($value);
			});
			
			$basicAndNetPayDetails = $this->_dbPayslip->getBasicSalaryAndNetPayDetails($payslipMonth,$businessUnitId,$employeeTypeId,$groupById,$groupByName);
		  	$payslipEarnings       = $this->_dbPayslip->getSalaryPayslipEarnings($payslipMonth,$businessUnitId,$employeeTypeId,$basicAndNetPayDetails,$groupById);
			$payslipDeductions     = $this->_dbPayslip->getSalaryPayslipDeduction($payslipMonth,$businessUnitId,$employeeTypeId,$groupById);

			if($reportTitle=='Consolidated Salary Statement')
			{
				$sumOfPayslipEarnings 		  = $this->_dbPayslip->sumOfPayslipEarnings($groupById,$groupByName,$payslipEarnings);
				$sumOfPayslipDeductions 	  = $this->_dbPayslip->sumOfPayslipDeductions($groupById,$groupByName,$payslipDeductions);
				$combineEarningsAndDeductions = $this->_dbPayslip->combineEarningsAndDeductions($groupByName,$sumOfPayslipEarnings,$sumOfPayslipDeductions);
				$this->_dbCommonFun->exportCsv($combineEarningsAndDeductions,$payslipMonth,$reportTitle,$this->_locale);
			}
			else
			{
				$payslipSummary    = $this->_dbPayslip->payslipSummaryReportDataFormation($payslipEarnings,$payslipDeductions,$this->_locale);
				$this->_dbCommonFun->exportCsv($payslipSummary,$payslipMonth,$reportTitle,$this->_locale);
			}
		}
		else
		{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}
    }
	
    public function exportHourlyPrintAction()
    {
        $this->_helper->layout()->disableLayout();
        if(isset($_SERVER['HTTP_REFERER']))
        {
			$payslipId = $this->_getParam('Payslip_Id', null);
			$payslipId = filter_var($payslipId, FILTER_SANITIZE_NUMBER_INT, FILTER_REQUIRE_ARRAY);
			$incentiveInWords = $this->_getParam('Incentive_Amount_Words', null);
			$incentiveInWords = filter_var($incentiveInWords, FILTER_SANITIZE_STRING, FILTER_REQUIRE_ARRAY);
			$format = $this->_getParam('format', null);
			
			if(!empty($payslipId))
			{
				$exportStr = "";
					
				if($this->_payslipAccessRights['Employee']['Optional_Choice']==1)
				{
					for($i = 0;$i<count($payslipId);$i++)
					{
                        $orgCode = $this->_ehrTables->getOrgCode();
                    
						$payslip = $this->_dbPayslip->viewHoulryWages($payslipId[$i],$orgCode);
					    $payslip['Incentive_Amount_Words'] = $incentiveInWords[$i];
                    	array_unshift($payslip['Incentive'],array('Incentive_Name'=>'Total Overtime Wages','Incentive_Amount'=>$payslip['Payslip']['Total_OvertimeWages']));
						array_unshift($payslip['Incentive'],array('Incentive_Name'=>'Total Hourly Wages','Incentive_Amount'=>$payslip['Payslip']['Total_HourlyWages']));
					
                    	$salary = $this->view->payslip = $payslip;
						$currency = $this->view->currency = $this->_dbPersonal->currencySymbol($payslip['Payslip']['Employee_Id']);
						$exportStr .= $this->payslipHTML($salary, $currency, 'Hourly','','print');
					}
				}
				
				$this->view->result = $exportStr;
			}
		}
		else
		{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}
    }
    
    public function getPaymentDayAction()
    {
        $this->_helper->layout()->disableLayout();
        if(isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('get-payment-day', 'json')->initContext();
            
			$this->view->result = $this->_dbPayslip->getPaymentDay();
        }
		else
		{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}
    }   
    
    public function updateQuarterClosureAction()
    {
        $this->_helper->layout()->disableLayout();
        if(isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('update-quarter-closure', 'json')->initContext();
            
            $assessYr = $this->_getParam('assessYr', null);
            $quarterMnth = $this->_getParam('quarterMnth', null);
            
            $updateArray = array('Financial_Year' => $assessYr,
                                 'Quarter_Month' => $quarterMnth);
            
            $this->view->result = $this->_dbPayslip->updateQuarterClosure($updateArray, $this->_logEmpId, $this->_formNameA);
        }
		else
		{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}
    }
    
    public function revertQuarterClosureAction()
    {
        $this->_helper->layout()->disableLayout();
        if(isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('revert-quarter-closure', 'json')->initContext();
            
            $assessYr = $this->_getParam('assessYr', null);
            $quarterMnth = $this->_getParam('quarterMnth', null);
            
            $revertArray = array('Financial_Year' => $assessYr,
                                 'Quarter_Month' => $quarterMnth);
            
            $this->view->result = $this->_dbPayslip->revertQuarterClosure($revertArray, $this->_logEmpId, $this->_formNameA);
        }
		else 
		{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}
    }
    
    public function getClosureMonthAction()
    {
        $this->_helper->layout()->disableLayout();
        if(isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('get-closure-month', 'json')->initContext();
            
            $payslipMnth = $this->_getParam('payslipMnth', null);
           
            $this->view->result = $this->_dbPayslip->getClosureMonth($payslipMnth);
        }
		else
		{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}
    }
	
	public function checkPreRequisiteAction()
    {
		$this->_helper->layout()->disableLayout();
        if(isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('check-pre-requisite', 'json')->initContext();
            
			$payslipMonth = $this->_getParam('month', null);
			$empType     = $this->_getParam('empType', null);
			$empLocation = $this->_getParam('location', null);
			$empdept     = $this->_getParam('department', null);
			$empIds 	 = $this->_getParam('employeeId', null);
			if(!empty($payslipMonth) && !empty($empLocation) && !empty($empdept) && !empty($empType))
			{
				$salaryMonth = $payslipMonth;	
				$payslipMonth   = explode('-', $payslipMonth);
				$payslipMonth   = round($payslipMonth[1]).','.$payslipMonth[0];
				$empLocation = explode(',', $empLocation);
				$empdept = explode(',', $empdept);
				$empType = explode(',', $empType);

				/** if dept id is parent then get its child also else pass dept Id**/
				$empdept=  $this->_dbPrerequisite->getDepartmentDetails($empdept);

				/** Get the current assessment year */
				$currentAssessmentYear = $this->_dbFinacialYr->getAssessmentYr();

				if(strtolower($this->_orgDetails['Payroll_Period'])=='bimonthly')
				{
					$payPeriod = $this->_getParam('payPeriod', null);
					$payPeriod = filter_var($payPeriod, FILTER_SANITIZE_STRIPPED);
				}
				else
				{
					$payPeriod = '';
				}
			
				$getApprovalDetails = $this->_dbPrerequisite->getApprovalsCount($payslipMonth,$empLocation,$empdept,$empType, $empIds, $currentAssessmentYear,$payPeriod);
				$approvalsCount = $getApprovalDetails['approvalsCount'];
				if($approvalsCount[0] == 0 && $approvalsCount[1] == 0 && $approvalsCount[2] == 0 && $approvalsCount[3] == 0 && $approvalsCount[4] == 0 && $approvalsCount[5] == 0 &&
				   $approvalsCount[6] == 0 && $approvalsCount[7] == 0 && $approvalsCount[8] == 0 && $approvalsCount[9] == 0 && $approvalsCount[10] == 0 && $approvalsCount[11] == 0 &&
				   empty($approvalsCount[12]) && $approvalsCount[13] == 0 && $approvalsCount[14] == 0 && $approvalsCount[15] == 0 && $approvalsCount[16] == 0  && $approvalsCount[17] == 0  && $approvalsCount[18] == 0
				   && $approvalsCount[20] == 0 && $approvalsCount[21] == 0 && $approvalsCount[22] == 0 && empty($approvalsCount[23]) && $approvalsCount[24]==0 && $approvalsCount[25]==0 && $approvalsCount[26]==0
				   && $approvalsCount[27]==0 && $approvalsCount[28]==0)
				{
					$this->view->result = array('success' => true, 'msg' => 'Payslip can be generated', 'type' => 'success');
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Pending approvals needs to be approved', 'type' => 'warning');						
				}
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Invalid Data!', 'type' => 'warning');						
			}
			
        }
		else
		{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}
	}
	public function preRequisiteAction()
    {
		$this->_helper->layout()->disableLayout()->setLayout('admin_layout');
		if(isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('pre-requisite', 'json')->initContext();
            
			$payslipMonth = $this->_getParam('month', null);
			$empType     = $this->_getParam('empType', null);
			$empLocation = $this->_getParam('location', null);
			$empdept     = $this->_getParam('department', null);
			$empIds 	 = $this->_getParam('employeeId', null);
			if(!empty($payslipMonth) && !empty($empLocation) && !empty($empdept) && !empty($empType))
			{
				$salaryMonth = $payslipMonth;	
				$splitPayslipMonth   = explode('-', $payslipMonth);
				$payslipMonth   = round($splitPayslipMonth[1]).','.$splitPayslipMonth[0];
				$empLocation = explode(',', $empLocation);
				$empdept = explode(',', $empdept);
				$empType = explode(',', $empType);
				$empIds  = explode(',', $empIds);
				/** if dept id is parent then get its child also else pass dept Id**/
				$empdept=  $this->_dbPrerequisite->getDepartmentDetails($empdept);

				/** Get the current assessment year */
				$currentAssessmentYear = $this->_dbFinacialYr->getAssessmentYr();

				if(strtolower($this->_orgDetails['Payroll_Period'])=='bimonthly')
				{
					$payPeriod = $this->_getParam('payPeriod', null);
					$payPeriod = filter_var($payPeriod, FILTER_SANITIZE_STRIPPED);
				}
				else
				{
					$payPeriod = '';
				}
				
				$getApprovalDetails = $this->_dbPrerequisite->getApprovalsCount($payslipMonth,$empLocation,$empdept,$empType,$empIds,$currentAssessmentYear,$payPeriod);
				$approvalsCount = $getApprovalDetails['approvalsCount'];
				
				$leaveSettings = $this->_dbCommonFun->getLeaveSettings();
				if(!empty($leaveSettings))
				{
					$enableWorkFlow = $leaveSettings['Enable_Workflow'];
				}
				else
				{
					$enableWorkFlow = 'No';
				}
				
				$this->view->advanceSalary	= $approvalsCount[0];
				$this->view->bonus			= $approvalsCount[1];
				$this->view->commission 	= $approvalsCount[2];
				$this->view->deduction 		= $approvalsCount[3];
				$this->view->loan 			= $approvalsCount[4];
				$this->view->reimbursement 	= $approvalsCount[5];
				$this->view->resignation 	= $approvalsCount[6];
				$this->view->shift 			= $approvalsCount[7];
				$this->view->taxDeclaration = $approvalsCount[8];
				$this->view->attendance 	= $approvalsCount[9];
				$this->view->leave 			= $approvalsCount[10];
				$this->view->salaryRecalc 	= $approvalsCount[11];
				$this->view->financialClosure= $approvalsCount[12];
				$this->view->attendanceEnforce= $approvalsCount[13];
				$this->view->assetManagement = $approvalsCount[14];
				$this->view->hraDeclaration = $approvalsCount[15];
				$this->view->compensatoryOff = $approvalsCount[16];
				$this->view->deferredLoan = $approvalsCount[17];
				$this->view->nonShiftDays = $approvalsCount[18];
				$this->view->payslipEmployeeIds = $approvalsCount[19];
				$this->view->leaveClosureEncashment = $approvalsCount[20];// Boolean value to determine whether leave closure has to be run or not
				$this->view->incomeUnderSection24 = $approvalsCount[21];
				$this->view->additionalWageClaimCount = $approvalsCount[22];
				$this->view->probationEmployee   = $approvalsCount[23];
				$this->view->fullAndFinalSettlement = $approvalsCount[24];
				$this->view->employeeLopRecovery = $approvalsCount[25];
				$this->view->taxReliefDeclaration = $approvalsCount[26];
				$this->view->shortTimeOff = $approvalsCount[27];
				$this->view->earlyCheckout = $approvalsCount[28];
				
				$paycycleDates = $this->_dbPayslip->getSalaryDay($splitPayslipMonth[1],$splitPayslipMonth[0], 190);// get paycycle(payslip) month - end date
				$this->view->paycycleStartDate = $paycycleDates['Salary_Date'];
				$this->view->paycycleEndDate = $paycycleDates['Last_SalaryDate'];
				$this->view->salaryMonth=$splitPayslipMonth[1];
				$this->view->salaryYear=$splitPayslipMonth[0];
				$this->view->enableWorkFlow	= $enableWorkFlow;
				$this->view->assessmentYear = $currentAssessmentYear;
			}
        }
		else
		{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}
	
    }

	// For deleting all sub folders and files in the given directory.
	public function rmrf($dirPath) {
		foreach (glob($dirPath) as $file) {
			if (is_dir($file)) { 
				$this->rmrf("$file/*");
				rmdir($file);
			} else {
				unlink($file);
			}
		}
	}

	// Deletes the existing folder and shows an success message
	// If not Failure Message will be Displayed.
    public function deletePdfFolderAction()
    {
		$this->_helper->layout()->disableLayout();
		if(isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('delete-pdf-folder', 'json')->initContext();
			
			$orgCode = $this->_ehrTables->getOrgCode();

			$dirPath = $this->_ehrTables->filePath."pdfexport/";
			$usrFolderName = $orgCode."-".$this->_logEmpId;
			$dirPath = $dirPath.$usrFolderName;

			// If directory already exsits then remove directory
			if((count(scandir($dirPath))) > 2)
			{
				$this->rmrf($dirPath);
			}
			else{
				rmdir($dirPath);
			}
			if(is_dir($dirPath)){ 
				$this->view->result = array('success'=>true, 'msg'=>"You have Already initiated a Bulk Monthly Payslip Export process. Please try again after its completed", 'type'=>'info');
			}
			else{
				$this->view->result = array('success'=>true, 'msg'=>'Pdf Export Restarted Successfully', 'type'=>'info');
			}
		}
		else{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}
	}
	// list employees who is eligible for selected salary month
	public function listPayslipEmployeesAction()
    {
		$this->_helper->layout()->disableLayout();
		if(isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('list-payslip-employees', 'json')->initContext();

            $searchAll = $this->_getParam('searchTransactionDetails', null);
            $searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
            
            $salaryMonth = $this->_getParam('Salary_Month', null);
            $salaryMonth = filter_var($salaryMonth, FILTER_SANITIZE_STRIPPED);
            
            $paysourceType = $this->_getParam('Paysource_Type', null);
            $paysourceType = filter_var($paysourceType, FILTER_SANITIZE_STRIPPED);

			$employeeType = $this->_getParam('Employee_Type', null);  
			$employeeType = filter_var($employeeType, FILTER_SANITIZE_STRIPPED);

			$location = $this->_getParam('location', null);
			$location = filter_var($location, FILTER_SANITIZE_STRIPPED);

			$department = $this->_getParam('department', null);
			$department = filter_var($department, FILTER_SANITIZE_STRIPPED);

			$location = explode(',', $location);
			$department = explode(',', $department);
			$employeeType = explode(',', $employeeType);

			$salRangeFrom = $this->_getParam('salaryRangeFrom',null);
			$salRangeFrom = filter_var($salRangeFrom, FILTER_SANITIZE_STRIPPED);

			$salRangeTo = $this->_getParam('salaryRangeTo',null);
			$salRangeTo = filter_var($salRangeTo, FILTER_SANITIZE_STRIPPED);

			$readyToGenerate = $this->_getParam('readyToGenerate',null);
			$payslipGenerated = $this->_getParam('payslipGenerated',null);
			$pendingApproval = $this->_getParam('pendingApproval',null);

			$serviceProviderId = $this->_getParam('serviceProviderId', null);
            $serviceProviderId = filter_var($serviceProviderId, FILTER_SANITIZE_NUMBER_INT);

			if(strtolower($this->_orgDetails['Payroll_Period'])=='bimonthly')
			{
				$payPeriod = $this->_getParam('payPeriod', null);
				$payPeriod = filter_var($payPeriod, FILTER_SANITIZE_STRIPPED);
			}
			else
			{
				$payPeriod = '';
			}
			/** Get the current assessment year */
			$currentAssessmentYear = $this->_dbFinacialYr->getAssessmentYr();
			
			$getApprovalDetails = $this->_dbPrerequisite->getApprovalsCount($salaryMonth,$location,$department,$employeeType, '',$currentAssessmentYear,$payPeriod);
			$approvalsPendingEmpIds = $getApprovalDetails['approvalEmpIds'];
			$searchArray  = array( 'Employee_Type'      => $employeeType,
                                   'Location'           => $location,
								   'Department'         => $department,
								   'salaryRangeFrom'    => $salRangeFrom,
								   'salaryRangeTo'      => $salRangeTo,
								   'readyToGenerate'    => $readyToGenerate,
								   'payslipGenerated'   => $payslipGenerated,
								   'pendingApproval'    => $pendingApproval,
								   'payPeriod'          => $payPeriod,
								   'serviceProviderId'  => $serviceProviderId);
		
			$this->view->paysourceType = $paysourceType;
			$this->view->result = $this->_dbPayslip->listPayslipEmployees($salaryMonth, $paysourceType, $searchAll, $searchArray,$this->_logEmpId,$approvalsPendingEmpIds,'No');
		}
		else{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}
	}  
	// get salary dates to display start date and end date
	public function getSalaryDayAction()
    {
        $this->_helper->layout()->disableLayout();
		
		$ajaxContext = $this->_helper->getHelper('AjaxContext');
		$ajaxContext->addActionContext('get-salary-day', 'json')->initContext();

		//when this function is called from vue app we are not able to get params using getPost()
		//so if getPost is empty used getRawBody to get params. 
		//If both are empty we present error to the user
		$formData = $this->getRequest()->getPost();

		if(empty($formData )){
			$requestBody =$this->getRequest()->getRawBody();
			if ($requestBody)
			{
				$formData = Zend_Json::decode($requestBody);
			}
			else{								
				$this->view->result = array('success'=>false, 'msg'=>'Invalid Data', 'type'=>'warning');				
			}						
		}
		$salaryMonthYear = $formData["salaryMonthYear"];
		$salaryMonthYear = filter_var($salaryMonthYear, FILTER_SANITIZE_STRIPPED);

		if(strtolower($this->_orgDetails['Payroll_Period'])=='bimonthly' && isset($formData["payPeriod"]))
		{
			$payPeriod = $formData["payPeriod"];
			$payPeriod = filter_var($payPeriod, FILTER_SANITIZE_STRIPPED);
		}
		else
		{
			$payPeriod = '';
		}

		$monthyear = explode(',', $salaryMonthYear);
		
		#Get the formId from the input if it exists or set it as 0.
		$formId = array_key_exists('formId', $formData) ? $formData["formId"] : 0;

		#Get the input date from the input
		$inputDate = array_key_exists('inputDate', $formData) ? $formData["inputDate"] : '';

		if(!empty($inputDate)){
			$salaryDates = $this->_dbPayslip->getSalaryDateRange($monthyear[0], $monthyear[1], $inputDate, $formId);
		}else{
			$salaryDates = $this->_dbPayslip->getSalaryDay($monthyear[0],$monthyear[1], $formId,$payPeriod);
		}

		$orgDF = $this->_ehrTables->orgDateformat('php');

		// organization date format to display salary ranges
		$salStartDate = date($orgDF['php'],strtotime($salaryDates['Salary_Date']));
		$salLastDate = date($orgDF['php'],strtotime($salaryDates['Last_SalaryDate']));
		
		$orgCode = $this->_ehrTables->getOrgCode();
		$dbOrgDetail = new Organization_Model_DbTable_OrgSettings();
		$showReporCreator = $dbOrgDetail->viewOrgDetail($orgCode);
		$financialClosureTracking = $showReporCreator['Financial_Closure_Tracking'];
		$this->view->result = array("salaryDates"=> $salaryDates, "salaryStartDate" => $salStartDate, "salaryLastDate"=>$salLastDate,'financialClosureTracking'=>$financialClosureTracking);
	}

	// function to get employee details for payslip generated employees
	public function listPayslipGeneratedEmployeesAction()
    {
        $this->_helper->layout()->disableLayout();
		if(isset($_SERVER['HTTP_REFERER']))
        {			
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('list-payslip-generated-employees', 'json')->initContext();

			$searchAll = $this->_getParam('searchTransactionDetails', null);
			$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
			
			$salaryMonth = $this->_getParam('Salary_Month', null);
			$salaryMonth = filter_var($salaryMonth, FILTER_SANITIZE_STRIPPED);
			
			$employeeType = $this->_getParam('Employee_Type', null);   
			$employeeType = filter_var($employeeType, FILTER_SANITIZE_STRIPPED);

			$location = $this->_getParam('location', null);
			$location = filter_var($location, FILTER_SANITIZE_STRIPPED);

			$department = $this->_getParam('department', null);
			$department = filter_var($department, FILTER_SANITIZE_STRIPPED);

			$location = explode(',', $location);
			$department = explode(',', $department);
			$employeeType = explode(',', $employeeType);

			$salRangeFrom = $this->_getParam('salaryRangeFrom',null);
			$salRangeFrom = filter_var($salRangeFrom, FILTER_SANITIZE_STRIPPED);

			$salRangeTo = $this->_getParam('salaryRangeTo',null);
			$salRangeTo = filter_var($salRangeTo, FILTER_SANITIZE_STRIPPED);

			$readyToGenerate = $this->_getParam('readyToGenerate',null);
			$payslipGenerated = $this->_getParam('payslipGenerated',null);
			$pendingApproval = $this->_getParam('pendingApproval',null);
			$employeeIds = $this->_getParam('employeeIds',null);
			$payPeriod = $this->_getParam('payPeriod',null);

			$searchArray  = array( 'Employee_Type'      => $employeeType,
									'Location'           => $location,
									'Department'         => $department,
									'salaryRangeFrom'    => $salRangeFrom,
									'salaryRangeTo'      => $salRangeTo,
									'readyToGenerate'    => $readyToGenerate,
									'payslipGenerated'   => $payslipGenerated,
									'pendingApproval'   => $pendingApproval,
									'employeeId'		 => $employeeIds,
									'payPeriod'          => $payPeriod);

			 $this->view->result = $this->_dbPayslip->getEmployeesDetails($salaryMonth, $searchArray, $searchAll);
			 $this->view->defaultUserProfileImage = $this->_basePath->baseUrl('images/defaultPhot.jpg');
       		 $this->view->payoutSearchAlertImage = $this->_basePath->baseUrl('images/payout-search-alert.png');
		}
		else{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}
	}  
	
	public function getEmployeeTaxDetailsAction()
    {
        $this->_helper->layout()->disableLayout();
        // if (isset($_SERVER['HTTP_REFERER']))
        // {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('get-employee-tax-details', 'json')->initContext();			

			$requestBody = $this->getRequest()->getRawBody();
			if ($requestBody)
			{
				$formData = Zend_Json::decode($requestBody);
				
				$employeeId = filter_var($formData['employeeId'], FILTER_SANITIZE_NUMBER_INT);
				// $assessmentYear = filter_var($formData['assessmentYear'], FILTER_SANITIZE_NUMBER_INT);

				if(!empty($employeeId)){
					$isEmpContractorTdsApplicable = $this->_dbPayslip->isContractorTdsApplicable($employeeId);
            
					/* If the employee is contractor employee then we should not present the tax details */
					if((int)$isEmpContractorTdsApplicable['Eligible_For_Contractor_Tds']===1)
					{
						$empTaxDetails = array();
						$empTaxDetails['Tax_Calculation_View'] = 0;
						$empTaxDetails['Tax_Calculation_Details'] = array(
							'Salary_Month' => '',
							'Tax_Regime' => '',
							'Annual_Taxable_Income' => '',
							'Annual_Tax' => '');
					}else{
						$assessmentYear = $this->_orgDetails['Assessment_Year'];

						if(isset($formData['taxRegime']))
						{
							$tdsSheetRegime = filter_var($formData['taxRegime'], FILTER_SANITIZE_STRIPPED);
						}
						else
						{
							$tdsSheetRegime = '';						
						}
						$empTaxDetails = $this->_dbPayslip->getEmpTaxDetails($employeeId,$assessmentYear,'tax-calculation-sheet','',$tdsSheetRegime);
					}

					if(!empty($empTaxDetails)){
						$dbPersonal = new Employees_Model_DbTable_Personal();
						$empTaxDetails['Tax_Calculation_Details']['Currency_Symbol']=$dbPersonal->currencySymbol($employeeId);
					}
					
					$this->view->result = array('success' => true, 'empTaxDetails' =>$empTaxDetails, 'msg' => 'Employee tax details retrieved Successfully', 'type' => 'success');			
				}else{
					$this->view->result = array('success' => false, 'empTaxDetails' =>array(), 'msg' => 'Unable to retrieve the employee tax details', 'type' => 'warning');	
				}
			}else{
				$this->view->result = array("success"=>false,"empTaxDetails"=>array(), 'msg'=>'Unable to retrieve the employee tax details', 'type'=>'warning');
			}
		// }
		// else
		// {
		// 	$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		// }
	}
	/*Get Payroll Estimation Details through this action*/
	public function getPayrollEstimationAction()
    {
		$this->_helper->layout()->disableLayout();
		$checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();
		if ($checkSessionAuth)
		{
			if(isset($_SERVER['HTTP_REFERER']))
			{			
				$ajaxContext = $this->_helper->getHelper('AjaxContext');
				$ajaxContext->addActionContext('get-payroll-estimation', 'json')->initContext();

				$payrollEstimation = '';
				$maxPayrollMonth = '';
				$currencySymbol = $this->_dbPersonal->currencySymbol($this->_logEmpId);
				/*when the currency symbol is not specified for the location we need to add indian currency as default*/
				if(empty($currencySymbol))
				{
					$currencySymbol = '₹';
				}
				if(!empty($this->_payslipAccessRights['Admin']))
				{
					$salaryDateDetails = $this->_dbCommonFun->getLastPayslipMonth('','Monthly');
					$payslipMonth = date('n,Y', strtotime($salaryDateDetails['lastSalaryDate'])); 
					$payrollEstimation = $this->_dbPayslip->getPayrollEstimation($payslipMonth);
					$maxPayrollMonth = date('M,Y', strtotime($salaryDateDetails['lastSalaryDate']));
				}
				$this->view->result = array('success' => true, 'payrollEstimation' =>$payrollEstimation,'payslipMonth'=>$maxPayrollMonth,'isAdmin'=>$this->_payslipAccessRights['Admin'],'type' => 'success','currencySymbol'=>$currencySymbol);			
			}
			else
			{
				$this->_helper->redirector('index', 'salary-payslip', 'payroll');
			}
		}
		else 
		{
			$this->_redirect('auth');		
		}
	}
	
	public function calculatePayrollEstimationAction()
    {
		$this->_helper->layout()->disableLayout();
		if(isset($_SERVER['HTTP_REFERER']))
        {			
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('calculate-payroll-estimation', 'json')->initContext();

			$salaryDateDetails = $this->_dbCommonFun->getLastPayslipMonth('','Monthly');
			$salaryMonth = date('n,Y', strtotime($salaryDateDetails['lastSalaryDate']));
			
			$locationPair     = $this->_dbLocation->getLocationPair();
			$departmentPair   = $this->_dbDept->getDeptPairs();
			$employeeTypePair = $this->_dbEmpType->getEmpTypePairs();
			
			$payrollEstimation = '';
			$maxPayrollMonth = date('M,Y', strtotime($salaryDateDetails['lastSalaryDate']));
			$currencySymbol = $this->_dbPersonal->currencySymbol($this->_logEmpId);
			/*when the currency symbol is not specified for the location we need to add indian currency as default*/
			if(empty($currencySymbol))
			{
				$currencySymbol = '₹';
			}
			if(!empty($locationPair)&&!empty($departmentPair)&&!empty($employeeTypePair)&&!empty($salaryMonth))
			{
				$location     = array_keys($locationPair);
				$department   = array_keys($departmentPair);
				$employeeType = array_keys($employeeTypePair);
				$currentAssessmentYear = $this->_dbFinacialYr->getAssessmentYr();
				$maxMonthSalary    = $this->_dbPayslip->getmaxMonthlyPayslipSalary('Monthly');

				$getApprovalDetails = $this->_dbPrerequisite->getApprovalsCount($salaryMonth,$location,$department,$employeeType, '',$currentAssessmentYear);
				$approvalsPendingEmpIds = $getApprovalDetails['approvalEmpIds'];

				//payroll estimation will be always run on monthly so payPeriod should be passed as empty
				$searchArray  = array( 'Employee_Type'      => $employeeType,
									'Location'          	=> $location,
									'Department'         	=> $department,
									'salaryRangeFrom'    	=> 0,
									'salaryRangeTo'      	=> $maxMonthSalary,
									'readyToGenerate'    	=> 'checked',
									'payslipGenerated'   	=> 'checked',
									'pendingApproval'   	=> 'checked',
									'payPeriod'   	        => '',
									'serviceProviderId'  	=> 0);

				$payslipAccess = array('Admin'      => $this->_payslipAccessRights['Admin'],
								       'LogId'      => $this->_logEmpId);					

				$paysourceType = 'Monthly Payslip';
				$searchAll = '';

				/*we need to get list the paysip employee based on ready to generate and already generated and pending approval employee id based on this function */
				$employeeDetails = $this->_dbPayslip->listPayslipEmployees($salaryMonth, $paysourceType, $searchAll, $searchArray,$this->_logEmpId,$approvalsPendingEmpIds,'Yes');

				$payslipMonth   =  date('Y-m', strtotime($salaryDateDetails['lastSalaryDate']));;

				
				if(!empty($employeeDetails['payslipPendingEmployeeIds']) || !empty($employeeDetails['pendingApprovalEmployeeIds']))
				{
					/*We need to get the ready to generate employee id and pending approval employee id combine the employee id and pass to generate monthly function*/
					$employeeIdList = array_unique(array_merge($employeeDetails['payslipPendingEmployeeIds'],$employeeDetails['pendingApprovalEmployeeIds']));

					if(!empty($employeeIdList))
					{
						$monthlyPayslip = $this->_dbPayslip->generateMonthly($salaryMonth, $this->_logEmpId, $location, $department, $employeeType, $payslipMonth, $employeeIdList,'Yes');
						
						if($monthlyPayslip > 0)
						{
							$payrollEstimation = $this->_dbPayslip->getPayrollEstimation($salaryMonth);
							
							$this->view->result = array('success' => true,'msg'=>"Payroll estimation for the month ".$maxPayrollMonth." is completed",'payslipMonth'=>$maxPayrollMonth,'payrollEstimation' =>$payrollEstimation,'type' => 'success','currencySymbol'=>$currencySymbol);
						}
						else 
						{
							$this->view->result = array('success' => false,'msg'=>"Leave closure and financial closure prerequisites should be run before monthly payroll estimation",'payslipMonth'=>$maxPayrollMonth,'payrollEstimation' =>$payrollEstimation,'type'=>'info','currencySymbol'=>$currencySymbol);
						}  
					}
					else 
					{
						$this->view->result = array('success' => false,'msg'=>"Payroll estimation couldn't be completed due to a technical error, please try after some time.",'payslipMonth'=>$maxPayrollMonth,'payrollEstimation' =>$payrollEstimation,'type'=>'info','currencySymbol'=>$currencySymbol);
					}  
				}
				else 
				{
					$this->view->result = array('success' => false,'msg'=>"Payroll estimation couldn't be completed due to a technical error, please try after some time.",'payslipMonth'=>$maxPayrollMonth,'payrollEstimation' =>$payrollEstimation,'type'=>'info','currencySymbol'=>$currencySymbol);
				}
			}
			else 
			{
				$this->view->result = array('success' => false,'msg'=>"Payroll estimation couldn't be completed due to a technical error, please try after some time.",'payslipMonth'=>$maxPayrollMonth,'payrollEstimation' =>$payrollEstimation,'type'=>'info','currencySymbol'=>$currencySymbol);
			}	
		}
		else
		{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}
    }

	public function checkAllPayslipGeneratedAction()
    {
		$this->_helper->layout()->disableLayout();
		if(isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('check-all-payslip-generated', 'json')->initContext();
	
			$action = $this->_getParam('Action');
		    $action = filter_var($action, FILTER_SANITIZE_STRIPPED);
			$payslipNotGeneratedMonth = $this->_dbLeave->checkAllPayslipGenerated($action);
			
			if(!empty($payslipNotGeneratedMonth))
			{
				if(strtotime($payslipNotGeneratedMonth['From'])==strtotime($payslipNotGeneratedMonth['To']))
				{
					$this->view->result = array('success' => false,'msg'=>"Payslip is not generated for few employees for ".$payslipNotGeneratedMonth['From'].". Please generate a payslip and try again",'type' =>'info');	
				}
				else 
				{
					$this->view->result = array('success' => false,'msg'=>"Payslip is not generated for few employees from ".$payslipNotGeneratedMonth['From']." to ".$payslipNotGeneratedMonth['To'].". Please generate a payslip and try again",'type' => 'info');	
				}
			}
			else 
			{
			   $this->view->result = array('success' => true,'msg'=>"",'type' => 'success');
			}
		}
		else
		{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}
        
    }

	public function getLastPayslipMonthAction()
    {
        $this->_helper->layout()->disableLayout();
		if(isset($_SERVER['HTTP_REFERER']))
		{			
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('get-last-payslip-month', 'json')->initContext();

			//this function is called from vue app we are not able to get params using getPost()
			//so if getPost is empty used getRawBody to get params. 
			//If both are empty we present error to the user
			$formData = $this->getRequest()->getPost();
			if(empty($formData )){
				$requestBody =$this->getRequest()->getRawBody();
				if ($requestBody)
				{
					$formData = Zend_Json::decode($requestBody);
				}
				else{								
					$this->view->result = array('success'=>false, 'msg'=>'Invalid Data', 'type'=>'warning', 'salaryDates' => array());				
				}						
			}
		
			$employeeId = $formData["employeeId"];
			$formName = $formData["formName"];
			$salaryDateDetails = $this->_dbCommonFun->getLastPayslipMonth($employeeId,null,$formName);
			$this->view->result = array('success' => true, 'msg'=>"",'type' => 'success', 'salaryDates' =>$salaryDateDetails);		
		}
		else
		{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}
    }

	public function getEmployeeSettlementDetailsAction()
	{
		$this->_helper->layout()->disableLayout();
       
		$ajaxContext = $this->_helper->getHelper('AjaxContext');
		$ajaxContext->addActionContext('get-employee-settlement-details', 'json')->initContext();
		
		$body = $this->getRequest()->getRawBody();
        $formData = Zend_Json::decode($body);
		$employeeDetails = $formData['employeeDetails'];
		$salaryMonth = $formData['salaryMonth'];
		$salaryYear = $formData['salaryYear'];
		$source = isset($formData['source']) ? $formData['source']: '';
		$payslipId = isset($formData['payslipId']) ? $formData['payslipId'] : 0;
		$externalIntegration = isset($formData['externalIntegration']) ? $formData['externalIntegration'] : 0;
		if($source === 'deductionsSummary'){
			if(!empty($employeeDetails) && !empty($salaryMonth) && !empty($salaryYear)){
				$deductionsSummaryDetails = $this->_dbPayslip->getSettlementDeductionSummary($employeeDetails,$salaryMonth,$salaryYear,$payslipId);
				$this->view->result = array('success'=>true,'message'=>'Employee deductions summary retrieved successfully.','settlementDetails'=>'','deductionsSummary'=>$deductionsSummaryDetails);
			}else{
				$this->view->result = array('success'=>false, 'message'=>'Invalid inputs','settlementDetails'=>'','deductionsSummary'=>'');
			}
		}else{
			if(!empty($employeeDetails) && !empty($salaryMonth) && !empty($salaryYear)){
				$finalSettlementDetails = $this->_dbPayslip->getEmpSettlementDetails($employeeDetails,$salaryMonth,$salaryYear,$externalIntegration);
				$this->view->result = array('success'=>true,'message'=>'Employee settlement details retrieved successfully.','settlementDetails'=>$finalSettlementDetails,'deductionsSummary'=>'');
			}else{
				$this->view->result = array('success'=>false, 'message'=>'Invalid inputs','settlementDetails'=>'','deductionsSummary'=>'');
			}
		}
	}

	public function getIndonesiaTaxReportAction()
	{
		$this->_helper->layout()->disableLayout();
		if ($this->_hrappMobile->checkAuth())
        {
			if(isset($_SERVER['HTTP_REFERER']))
			{
				$ajaxContext = $this->_helper->getHelper('AjaxContext');
				$ajaxContext->addActionContext('get-indonesia-tax-report', 'json')->initContext();
				$reportTitle 			= $this->_getParam('reportTitle', null);
				$reportTitle 			= filter_var($reportTitle, FILTER_SANITIZE_STRIPPED);
				$payslipMonth 			= $this->_getParam('payslipMonth', null);
				$payslipMonth 			= filter_var($payslipMonth, FILTER_SANITIZE_STRIPPED);
				$payslipYear 			= $this->_getParam('payslipYear', null);
				$payslipYear 			= filter_var($payslipYear, FILTER_SANITIZE_STRIPPED);
				
				$this->_dbHRReport 		= new Reports_Model_DbTable_HrReports();
				$monthlyTaxReport 		= trim(strtolower('MonthlyTaxReport'));
				$yearlyTaxReport 		= trim(strtolower('YearlyTaxReport'));

				if(trim(strtolower($reportTitle))==$monthlyTaxReport)
				{
					$result = $this->_dbHRReport->getMonthlyTaxReport($payslipMonth,$monthlyTaxReport);
				}
				else if(trim(strtolower($reportTitle))==$yearlyTaxReport)
				{
					$result = $this->_dbHRReport->getYearlyTaxReport($payslipYear,$yearlyTaxReport);
				}

				if(!empty($result))
				{
					$this->view->result = array('success' =>true, 'data'=>$result);
				}
				else
				{
					$this->view->result = array('success' =>false, 'msg'=>"unable to get the data");
				}
			}
			else
			{
				$this->_helper->redirector('index', 'salary-payslip', 'payroll');
			}
		}
		else
		{
			$this->_helper->redirector('index', 'salary-payslip', 'payroll');
		}
	}

	public function __destruct()
    {
        
    }
}