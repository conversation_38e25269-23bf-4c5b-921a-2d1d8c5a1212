<?php
class Default_BillingpaymentController extends Zend_Controller_Action
{

	public function init()
	{
		/* Initialize action controller here */
		$this->_ehrTables = new Application_Model_DbTable_Ehr();
		$this->_salesDb = Zend_Registry::get('Hrapp');
		$this->hrappMobile = new Application_Model_DbTable_HrappMobile();
		
	}

	public function indexAction()
	{
		// action body
	}
	
	public function encryptDecrypt($string, $decrypt)
	{
		if($decrypt)
		{
			$string = urldecode($string);
			$decrypted = base64_decode($string);
			return $decrypted;
		}
		else
		{
			$encrypted = base64_encode($string);
			return urlencode($encrypted);
		}
	}
	
	public function updatePaymentAction()
	{
		$layout = $this->_helper->layout();
		$layout->disableLayout();
		//$layout->setLayout('admin_script_layout');
		
		//$layout->disableLayout('layout');
		//$layout->setLayout('tab_layout');
		 
		$orgCode = $this->_getParam('OC', null);
		$orgCode = filter_var($orgCode, FILTER_SANITIZE_STRIPPED);
		$orgCode = $this->encryptDecrypt($orgCode, 1);
		
		$sessionTime = $this->_getParam('TS', null);
		$sessionTime = filter_var($sessionTime, FILTER_SANITIZE_STRIPPED);
		$sessionTime = $this->encryptDecrypt($sessionTime, 1);
		
		$street = $this->_getParam('ST', null);
		$street = filter_var($street, FILTER_SANITIZE_STRIPPED);
		$street = $this->encryptDecrypt($street, 1);
		
		$city = $this->_getParam('CI', null);
		$city = filter_var($city, FILTER_SANITIZE_STRIPPED);
		$city = $this->encryptDecrypt($city, 1);
		
		$state = $this->_getParam('STA', null);
		$state = filter_var($state, FILTER_SANITIZE_STRIPPED);
		$state = $this->encryptDecrypt($state, 1);
		
		$country = $this->_getParam('CO', null);
		$country = filter_var($country, FILTER_SANITIZE_STRIPPED);
		$country = $this->encryptDecrypt($country, 1);
		
		$pinCode = $this->_getParam('PI', null);
		$pinCode = filter_var($pinCode, FILTER_SANITIZE_STRIPPED);
		$pinCode = $this->encryptDecrypt($pinCode, 1);

		$mobileNo = $this->_getParam('MO', null);
		$mobileNo = filter_var($mobileNo, FILTER_SANITIZE_STRIPPED);
		$mobileNo = $this->encryptDecrypt($mobileNo, 1);
		
		$employeeName = $this->_getParam('EN', null);
		$employeeName = filter_var($employeeName, FILTER_SANITIZE_STRIPPED);
		$employeeName = $this->encryptDecrypt($employeeName, 1);
		
		$invoiceNo = $this->_getParam('IN', null);
		$invoiceNo = filter_var($invoiceNo, FILTER_SANITIZE_NUMBER_INT);
		
		$employeeEmail = $this->_getParam('EEM', null);
		$employeeEmail = filter_var($employeeEmail, FILTER_SANITIZE_STRIPPED);
		$employeeEmail = $this->encryptDecrypt($employeeEmail, 1);
		
		$date = new DateTime();
		$currTimeStamp = $date->getTimestamp();
		$diff = abs($currTimeStamp - $sessionTime);
		
		if (!empty($orgCode))
		{
			$qryBilling = $this->_salesDb->fetchRow($this->_salesDb->select()
															->from($this->_ehrTables->billingTransaction,
																   array('Invoice_No','Total_Amount','Billing_Date'))
															->where('Org_Code = ?', $orgCode));
			
			$this->_salesDb->update($this->_ehrTables->billingTransaction, array('Transaction_Status'=>'Initiated'), array('Invoice_No = '.$invoiceNo));
			
			
			
			
			if ($diff<=2000)
			{
				$sourceId= $this->_salesDb->fetchOne($this->_salesDb->select()->from($this->_ehrTables->registerUser,'Source_Id')->where('Org_Code = ?',$orgCode));
			        $this->view->licenseOrgName= $this->_salesDb->fetchOne($this->_salesDb->select()->from($this->_ehrTables->appLogin,'Organization_Name')->where('User_Id = ?',$sourceId)->where('Enable_Licensing = ?','1'));
				
				$this->view->Invoice_No = $qryBilling['Invoice_No'];
				$this->view->Total_Amount = $qryBilling['Total_Amount'];
				$this->view->Organization_Url = $orgCode.'.hrapp.co';
				$this->view->Customer_Name = $employeeName;
				$this->view->Billing_Date = $qryBilling['Billing_Date'];
				
				$paymentForm = new Default_Form_Payment();
				$this->view->paymentForm = $paymentForm;
				$paymentForm->Invoice_No->setValue($qryBilling['Invoice_No']);
				$paymentForm->Total_Amount->setValue($qryBilling['Total_Amount']);
				$paymentForm->Organization_Url->setValue($orgCode.'.hrapp.co');
				$paymentForm->Customer_Name->setValue($employeeName);
				$paymentForm->Billing_Date->setValue($qryBilling['Billing_Date']);
				$paymentForm->order_id->setValue($qryBilling['Invoice_No']);
				$paymentForm->amount->setValue($qryBilling['Total_Amount']);
				$paymentForm->currency->setValue('INR');
				
				$basePath = new Zend_View_Helper_BaseUrl();
		//		$redirectUrl = 'http://'.$orgCode.$basePath->baseUrl('default/billingpayment/redirect-url');
		//		$cancelUrl   = 'http://'.$orgCode.$basePath->baseUrl('default/billingpayment/cancel-url');
		            $redirectUrl = 'http://'.$orgCode.'.hrapp.co'.$basePath->baseUrl('default/billingpayment/redirect-url');
			    $cancelUrl   = 'http://'.$orgCode.'.hrapp.co'.$basePath->baseUrl('default/billingpayment/cancel-url'); 
				$paymentForm->redirect_url->setValue($redirectUrl);
				$paymentForm->cancel_url->setValue($cancelUrl);
				
				$paymentForm->billing_name->setValue($employeeName);
				$paymentForm->billing_address->setValue($street);
				$paymentForm->billing_city->setValue($city);
				$paymentForm->billing_state->setValue($state);
				
				$paymentForm->billing_zip->setValue($pinCode);
				$paymentForm->billing_country->setValue($country);
				$paymentForm->billing_tel->setValue($mobileNo);
				$paymentForm->billing_email->setValue($employeeEmail);
				
				if ($this->getRequest()->isPost())
				{
					if ($paymentForm->isValid($this->getRequest()->getPost()))
					{
						$formData = $paymentForm->getValues();
						
						error_reporting(0);
						
						$working_key = Zend_Registry::get('ccAvenueWorkingKey');
						$access_code = Zend_Registry::get('ccAvenueAccessCode');
						$merchant_data='';
						
						foreach ($_POST as $key => $value)
						{
							$merchant_data.=$key.'='.$value.'&';
						}
						
						$encrypted_data=$this->encrypt($merchant_data,$working_key); // Method for encrypting the data.
						$ccAvenue_url= Zend_Registry::get('ccAvenueURL');						
						?>
						<html>
						<body>
							<form method="post" name="redirect" action="<?php echo $ccAvenue_url; ?>">
								<input type="hidden" name="encRequest" value="<?php echo $encrypted_data; ?>" >
								<input type="hidden" name="access_code" value="<?php echo $access_code; ?>" >
							</form>
							<script language='javascript'>document.redirect.submit();</script>
						</body>
						</html>
						<?php
					}
				}
			}
			else
			{
				$this->_redirect('auth');
			}
		}
		else
		{
			$this->_redirect('auth');
		}
	}
	
	public function redirectUrlAction()
	{
		/* Confirm user is having a session */
		if ($this->hrappMobile->checkAuth())
		{
				$this->_helper->layout()->disableLayout()->setLayout('admin_layout');
				error_reporting(0);
				$workingKey= Zend_Registry::get('ccAvenueWorkingKey');		//Working Key should be provided here.
				$encResponse=$_POST["encResp"];			//This is the response sent by the CCAvenue Server
			//	$encResponse = $this->_getParam('Encrypted_Data');
				$rcvdString=$this->decrypt($encResponse,$workingKey);		//Crypto Decryption used as per the specified working key.
				$order_status="";
				$decryptValues=explode('&', $rcvdString);
				$dataSize=sizeof($decryptValues);
				for($i = 0; $i < $dataSize; $i++)
				{
					$information=explode('=',$decryptValues[$i]);
					$paymentDetails[$information[0]] =$information[1];
					if($i==3)	
				 		$order_status=$information[1];
				}
					$paymentId = $this->_getParam('Payment_Id');
					$commonFunction = new Application_Model_DbTable_CommonFunction(); 
					$userSession = $commonFunction->getUserDetails();
					$this->_loginEmpUserId = $userSession['logUserId'];
				
					$paymentDetails['Payment_Id']=$paymentId;
					$paymentDetails['Added_On'] = date('Y-m-d H:i:s');
					$paymentDetails['Added_By'] = $this->_loginEmpUserId;
					$this->_salesDb->insert($this->_ehrTables->paymentDetails, $paymentDetails);
				
			//	$order_status="Success";
				if($order_status=="Success")
				{
					/* $billingDetails = array('Transaction_Status'=>'Paid'); */
					$billingDetails = array('Transaction_Status'=>'Paid', 'Updated_By'=>$this->_loginEmpUserId, 'Updated_on'=>date('Y-m-d H:i:s'));
					$this->view->message = array('a'=>'Thank you for shopping with us. Your credit card has been charged and your transaction is successful. We will be shipping your order to you soon.','b'=>'success','c'=>'','d'=>'480', 'e'=>'');
				}
				else if($order_status=="Aborted")
				{
					$billingDetails =array('Transaction_Status'=>'Unpaid');
				    $this->view->message = array('a'=>'Thank you for shopping with us.We will keep you posted regarding the status of your order through Email','b'=>'warning','c'=>'','d'=>'480', 'e'=>'');
				}
				else if($order_status=="Failure")
				{
					$billingDetails =array('Transaction_Status'=>'Unpaid');
				    $this->view->message = array('a'=>'Thank you for shopping with us.However,the transaction has been declined.','b'=> 'warning', 'c'=>'warningboxid','d'=>480,'e'=>'');
				}
				else
				{
					$billingDetails =array('Transaction_Status'=>'Unpaid');
				    $this->view->message = array('a'=>'Security Error. Illegal access detected','b'=> 'warning', 'c'=>'warningboxid','d'=>300,'e'=>'');
				}
				$this->_salesDb->update($this->_ehrTables->billingTransaction, $billingDetails, array('Invoice_No = '.$paymentDetails['order_id']));
		
				$paymentId=$this->_salesDb->fetchOne($this->_salesDb->select()->from(array($this->_ehrTables->paymentDetails),
						new Zend_Db_Expr('Max(Payment_Id)')));
				
				$qryBilling = $this->_salesDb->fetchRow($this->_salesDb->select()->from($this->_ehrTables->paymentDetails,
						array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS Payment_Id as Count'),'tracking_id',
								'order_id',	'order_status', 'bank_ref_no','amount',
								'failure_message', 'payment_mode','card_name'))
								->where('Payment_Id = ?', $paymentId)); 
				$this->view->redirectUrl = $qryBilling;
		}
		else
		{
			//$this->view->message = array('a'=>'Session Expired','b'=> 'warning', 'c'=>'warningboxid','d'=>400,'e'=>'');
			$this->_redirect('auth');
		}	
		
	}
	public function cancelUrlAction()
	{
		/* Confirm user is having a session */
		if ($this->hrappMobile->checkAuth())
		{
			$this->_helper->layout()->disableLayout()->setLayout('admin_layout');
			$orgCode = $this->_ehrTables->getOrgCode();
			$billingDetails =array('Transaction_Status'=>'Unpaid');
			$qryBilling = $this->_salesDb->fetchRow($this->_salesDb->select()->from($this->_ehrTables->billingTransaction,
					array('Invoice_No','Total_Amount','Billing_Date','Transaction_Status'))
					->where('Org_Code = ?', $orgCode));
			if($qryBilling['Transaction_Status']=='Initiated')
			{	
				$this->_salesDb->update($this->_ehrTables->billingTransaction, $billingDetails, array('Invoice_No = '.$qryBilling['Invoice_No']));
			}
			$this->view->message = array('a'=>'Thank you for shopping with us.Your Order is Cancelled.','b'=> 'warning', 'c'=>'warningboxid','d'=>440,'e'=>'');
		}
		else 
		{
			$this->_redirect('auth');
			//$this->view->message = array('a'=>'Session Expired','b'=> 'warning', 'c'=>'warningboxid','d'=>400,'e'=>'');
		}
					
	}
	public function encrypt($plainText,$key)
	{
		$secretKey = $this->hextobin(md5($key));
		$initVector = pack("C*", 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f);
		$openMode = mcrypt_module_open(MCRYPT_RIJNDAEL_128, '','cbc', '');
		$blockSize = mcrypt_get_block_size(MCRYPT_RIJNDAEL_128, 'cbc');
		$plainPad = $this->pkcs5_pad($plainText, $blockSize);
		if (mcrypt_generic_init($openMode, $secretKey, $initVector) != -1)
		{
			$encryptedText = mcrypt_generic($openMode, $plainPad);
			mcrypt_generic_deinit($openMode);
		}
		return bin2hex($encryptedText);
	}
	
	public function decrypt($encryptedText,$key)
	{
		$secretKey = $this->hextobin(md5($key));
		$initVector = pack("C*", 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f);
		$encryptedText=$this->hextobin($encryptedText);
		$openMode = mcrypt_module_open(MCRYPT_RIJNDAEL_128, '','cbc', '');
		mcrypt_generic_init($openMode, $secretKey, $initVector);
		$decryptedText = mdecrypt_generic($openMode, $encryptedText);
		$decryptedText = rtrim($decryptedText, "\0");
		mcrypt_generic_deinit($openMode);
		return $decryptedText;
	
	}
	//*********** Padding Function *********************
	
	public function pkcs5_pad ($plainText, $blockSize)
	{
		$pad = $blockSize - (strlen($plainText) % $blockSize);
		return $plainText . str_repeat(chr($pad), $pad);
	}
	
	//********** Hexadecimal to Binary function for php 4.0 version ********
	
	public function hextobin($hexString)
	{
		$length = strlen($hexString);
		$binString="";
		$count=0;
		while($count<$length)
		{
			$subString =substr($hexString,$count,2);
			$packedString = pack("H*",$subString);
			if ($count==0)
			{
				$binString=$packedString;
			}
	
			else
			{
				$binString.=$packedString;
			}
	
			$count+=2;
		}
		return $binString;
	}

	public function __destruct()
    {
        
    }	
}