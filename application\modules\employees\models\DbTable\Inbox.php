<?php
//=========================================================================================
//=========================================================================================
/* Program : Inbox.php									                                 *		   				
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.							                             *								
 * All Rights Reserved.            							                             *							
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MYSQL Query for employee inbox.					                     *					
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Narmadha                Initial Version        	         *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Dhanabal                Changes in file for mobile app     *
 *                                                    1.Extra fields are added in        *
 *                                                    field list of list query.          *
 *                                                                                       *
 *  1.5        07-Jan-2016    Deepak               Changed in file for Bootstrap         *
 *                                                                                       */
//=========================================================================================
//=========================================================================================

class Employees_Model_DbTable_Inbox extends Zend_Db_Table_Abstract
{
    protected $_orgDF       = null;
    protected $_db          = null;
    protected $_ehrTables   = null;
    protected $_dbCommonFun = null;
	
    public function init()
    {
        $this->_ehrTables   = new Application_Model_DbTable_Ehr();
        $this->_dbCommonFun = new Application_Model_DbTable_CommonFunction();
		$this->_db          = Zend_Registry::get('subHrapp');
        $this->_orgDF       = $this->_ehrTables->orgDateformat();
    }
	
    // Display all the messages for the logged in employee
    public function listInbox ($page, $rows, $sortField, $sortOrder, $searchAll, $inboxUser)
    {
        /**
		 *	Sorting columns based on display column order in grid
		*/
		switch ($sortField)
		{
			case 1: $sortField = 'from.Emp_First_Name'; break;
			case 2: $sortField = 'I.Inbox_Subject'; break;
			case 3: $sortField = 'I.Inbox_Date'; break;
			default: case 0 : $sortField = 'I.Inbox_Date';$sortOrder = 'desc'; break;
		}
		
		/**
		 *	Query to fetch data from various tables
		*/
        $qryInbox = $this->_db->select()
							->from(array('I'=>$this->_ehrTables->empInbox),
								   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS I.Inbox_Id as Id'),
										 new Zend_Db_Expr("date_format(I.Inbox_Date, '".$this->_orgDF['sql']." %T') as inboxDate"),
										 'I.Inbox_Id', 'I.Inbox_Subject', 'I.Is_Read', 'I.To_Id', 'I.Description',
										 'Delete' => new Zend_Db_Expr($inboxUser['Delete']),
										 'Op_Choice' => new Zend_Db_Expr($inboxUser['Op_Choice']), 
										 'DT_RowClass' => new Zend_Db_Expr('"inbox"'), 
										 'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', I.Inbox_Id)")))
							
							->joinInner(array('from'=>$this->_ehrTables->empPersonal), 'I.From_Id=from.Employee_Id',
										array('Employee_Name'=>new Zend_Db_Expr("CONCAT(from.Emp_First_Name, ' ', from.Emp_Last_Name)")))
							
							->joinInner(array('to'=>$this->_ehrTables->empPersonal), 'I.To_Id = to.Employee_Id',
										array('Receiver_Name'=>new Zend_Db_Expr("CONCAT(to.Emp_First_Name, ' ', to.Emp_Last_Name)")))
							
							->where('To_Id = ?', $inboxUser['LogId'])
							->order("$sortField $sortOrder")
							->limit($rows, $page);
		if (!empty($searchAll) && $searchAll != null)
		{
			$conditions  = $this->_db->quoteInto(new Zend_Db_Expr('Concat(from.Emp_First_Name," ",from.Emp_Last_Name) Like ?'),"%$searchAll%");
			$conditions .= $this->_db->quoteInto('or I.Inbox_Subject Like ?',"%$searchAll%");
			$conditions .= $this->_db->quoteInto('or I.Inbox_Date Like ?', "%$searchAll%");
			
			$qryInbox->where($conditions);
		}
        
        /**
		 * SQL queries
		 * Get data to display
		*/
		$inbox = $this->_db->fetchAll($qryInbox);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		/* Total data set length */
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empInbox, new Zend_Db_Expr('COUNT(Inbox_Id)'))
									   ->where('To_Id = ?', $inboxUser['LogId']));
		
		foreach ($inbox as $key => $rows)
        {
            $inbox[$key]['Description'] = html_entity_decode($rows['Description']);
        }
		
		/**
		 * Output array with Json encode
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $inbox);
    }
	
	/**
	 *	to view a message with contains all the information
	*/
    public function viewMessage ($inboxId)
    {
        $where['Inbox_Id = (?)'] = $inboxId;
        $where['Is_Read=?'] = 0;
        
        $updated = $this->_db->update($this->_ehrTables->empInbox, array('Is_Read' => 1), $where);
		
		return array('success' => true, 'msg' => 'Inbox message viewed successfully', 'type' => 'sucess');
	}
    
    //to mark selected message as read
    public function markMessage ($inboxIds, $changeAction)
    {
        $where['Inbox_Id in (?)'] = $inboxIds;
		
		/**
		 *	If $action == 1, message mark as read
		 *	If $action == 0, message mark as unread
		*/
		if ($changeAction == "true")
		{
			$where['Is_Read=?'] = 0;
			$where['Inbox_Id in (?)'] = $inboxIds;
			
			$updateArr = array('Is_Read' => 1);
		}
		else
		{
			$where['Is_Read=?'] = 1;
			$where['Inbox_Id in (?)'] = $inboxIds;
			
			$updateArr = array('Is_Read' => 0);
		}
        
        $updated = $this->_db->update($this->_ehrTables->empInbox, $updateArr, $where);
		
		if ($updated)
		{
			return array('success' => true, 'msg' => 'Message(s) marked as '.(($changeAction=='false') ? 'unread' : 'read'), 'type' => 'success');			
		}
		else
		{
			return array('success' => false, 'msg' => 'Unable to mark message(s) as '.(($changeAction=='false') ? 'unread' : 'read'), 'type' => 'warning');			
		}
    }
	
    //to delete the selected message
    public function deleteMessage ($inboxIds, $sessionId)
    {
        $where['Inbox_Id in (?)'] = $inboxIds;
        
        $deleted = $this->_db->delete($this->_ehrTables->empInbox, $where);
		
        /**
		 *	delete activity for common function
		 *		1)check lock is exist or not.
		 *			If lock is exist then show error message like employee is open record for update.
		 *		2)If No lockflag then process delete activity
		 *		3)Update delete activity in system log
		 *		4)return success/failure message
		*/
		return $this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
														'tableName'      => $this->_ehrTables->empInbox,
														'lockFlag'       => 0,
														'formName'       => 'Inbox',
														'trackingColumn' => 'Inbox',
														'sessionId'      => $sessionId));
    }
    
    
    
    // to count no. of messages in the inbox for the logged in employee
    public function countInbox($loginEmpId)
    {
        return $this->_db->fetchOne($this->_db->select()
									->from($this->_ehrTables->empInbox, array(new Zend_Db_Expr("count(Inbox_Id) as Inbox")))
									->where('To_Id = ?', (int)$loginEmpId)
									->where('Is_Read = 0'));
    }
	
	// to send a meesage to the employee
    public function employeeInbox ($from, $to, $sub, $desc)
    {
        $insertMail = array("From_Id"       => $from,
							"To_Id"         => $to,
							"Inbox_Subject" => "$sub",
							"Inbox_Date"    => date('Y-m-d H:i:s'),
							"Description"   => "$desc");
		
        $this->_db->insert($this->_ehrTables->empInbox, $insertMail);
    }
	
	public function __destruct()
    {
        
    }	
}