<?php	
	$isProduction = Zend_Registry::get('Production');
	$licenseOrgName=$this->licenseOrgName;
	$isDomain = Zend_Registry::get('Domain');
	$isDomainArray = explode(".",$isDomain);
	
	$ehrTables    = new Application_Model_DbTable_Ehr();
	$domainDetails = $ehrTables->domainDetails();

	$termsOfUse     = $domainDetails['Terms_Link'];
	$privacyPolicy  = $domainDetails['Privacy_Policy_Link'];
?>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta content="" name="description" />
        <meta content="HRAPP" name="author" />
		
		<!--Favicon-->
		<link rel="apple-touch-icon" sizes="180x180" href="<?php echo $this->baseUrl('/apple-touch-icon.png?v=9B9bjrPr00'); ?>">
		<link rel="icon" type="image/png" sizes="32x32" href="<?php echo $this->baseUrl('/favicon-32x32.png?v=9B9bjrPr00'); ?>">
		<link rel="icon" type="image/png" sizes="16x16" href="<?php echo $this->baseUrl('/favicon-16x16.png?v=9B9bjrPr00'); ?>">
		<link rel="manifest" href="<?php echo $this->baseUrl('/site.webmanifest?v=9B9bjrPr00'); ?>">
		<link rel="mask-icon" href="<?php echo $this->baseUrl('/safari-pinned-tab.svg?v=9B9bjrPr00'); ?>" color="#5bbad5">
		<link rel="shortcut icon" href="<?php echo $this->baseUrl('/favicon.ico?v=9B9bjrPr00'); ?>">
		<meta name="apple-mobile-web-app-title" content="<?php echo strtoupper($isDomainArray[0]); ?>">
		<meta name="application-name" content="<?php echo strtoupper($isDomainArray[0]); ?>">
		<meta name="msapplication-TileColor" content="#da532c">
		<meta name="theme-color" content="#ffffff">
		
		<meta name="msapplication-TileImage" content="<?php echo $this->baseUrl('/mstile-70x70.png'); ?>">
		<meta name="msapplication-TileImage" content="<?php echo $this->baseUrl('/mstile-144x144.png'); ?>">
		<meta name="msapplication-TileImage" content="<?php echo $this->baseUrl('/mstile-150x150.png'); ?>">
		<meta name="msapplication-TileImage" content="<?php echo $this->baseUrl('/mstile-310x150.png'); ?>">
		<meta name="msapplication-TileImage" content="<?php echo $this->baseUrl('/mstile-310x310.png'); ?>">
		<meta name="msapplication-TileImage" content="<?php echo $this->baseUrl('/browserconfig.xml'); ?>">
		<!--Favicon END-->
		
		<!--Title Start-->
		<Title>Billing</Title>
		<!--Title End-->
		
		<link href="<?php echo $this->baseUrl('assets/global/css/style.css?v=4'); ?>" rel="stylesheet">
        <link href="<?php echo $this->baseUrl('assets/global/css/ui.css?v=2'); ?>" rel="stylesheet">
        <link href="<?php echo $this->baseUrl('assets/global/plugins/bootstrap-loading/lada.min.css'); ?>" rel="stylesheet">
        <link href="<?php echo $this->baseUrl('assets/md-layout1/material-design/css/material.css'); ?>" rel="stylesheet">
        <link href="<?php echo $this->baseUrl('assets/md-layout1/css/layout.css?v=1'); ?>" rel="stylesheet">
        <!-- BEGIN PAGE STYLE -->
        <link href="<?php echo $this->baseUrl('assets/global/plugins/metrojs/metrojs.min.css'); ?>" rel="stylesheet">
        <link href="<?php echo $this->baseUrl('assets/global/plugins/maps-amcharts/ammap/ammap.min.css'); ?>" rel="stylesheet">
	<link href="<?php echo $this->baseUrl('assets/global/js/popup/style_popup.css') ?>" rel="stylesheet">
	<link href="<?php echo $this->baseUrl('assets/global/js/popup/magnific-popup.css') ?>" rel="stylesheet">
        <!-- END PAGE STYLE -->
        <script src="<?php echo $this->baseUrl('assets/global/plugins/modernizr/modernizr-2.6.2-respond-1.1.0.min.js'); ?>"></script>
        
    </head>
    
    <body>
        <div class="main-content" >
            <!-- BEGIN PAGE CONTENT -->
            <div class="page-content page-thin" style="background: radial-gradient(ellipse at center, #1f3649 0%, #17253d 44%, #040d11 100%);">
                <div class="container" style="height:70%;margin-top:5%;"> 
					<!--<div class="well well-lg">-->
						<div class="row">
							<div class="col-md-12 col-lg-12">
								<div role="alert" style="background-color:#fff;border-radius: 5px;padding:3%">
									<!--<button type="button" class="close" id="previousPage" aria-label="Close"><span aria-hidden="true">x</span></button>-->
									<div class="media-body">
										<p class="md-title text-center" style="color:#0097a7">Payment Details</p>
										<form role="form" id="formPayment" >
											<table class="table table-bordered">
												<tbody>
													<tr>
														<td><label class="control-label">Invoice No</label></td>
														<td><label style="word-break: break-all;font-weight:300;"><?php echo $this->Invoice_No; ?></label></td>
													</tr>
													<tr>
														<td><label class="control-label">Amount</label></td>
														<td><label style="word-break: break-all;font-weight:300;"><?php echo $this->Total_Amount; ?></label></td>
													</tr>
													<tr>
														<td><label class="control-label">Organization Url</label></td>
														<td><label style="word-break: break-all;font-weight:300;"><?php echo $this->Organization_Url; ?></label></td>
													</tr>
													<tr>
														<td><label class="control-label">Customer Name</label></td>
														<td><label style="word-break: break-all;font-weight:300;"><?php echo $this->Customer_Name; ?></label></td>
													</tr>
													<tr>
														<td><label class="control-label">Billing Date</label></td>
														<td id="Billing_Date"><label style="word-break: break-all;font-weight:300;"><?php echo $this->Billing_Date; ?></label></td>
													</tr>
												</tbody>
											</table>
										</form>
										<div class="row">
											<button type="button" class="btn btn-secondary-default btn-embossed pull-left" id="closeFormPayment" style="bottom: 5px;">
												<i class="mdi-hardware-keyboard-backspace"></i> Back
											</button>
											<button type="submit" class="btn btn-secondary btn-embossed ladda-button pull-right" data-style="expand-left" style="bottom: 5px;" id="submitFormPayment">
												<i class="mdi-action-payment"></i> Pay
											</button>
										</div>
									</div>
								</div>
							</div>
						</div>
					<!--</div>-->
				</div>
				
				<?php if($domainDetails['Copy_Right']==1) { ?>
                <div class="footer">
                    <?php if (!empty($licenseOrgName)) { ?>
                    <div class="copyright">
                        <p class="pull-left sm-pull-reset"> <span>&copy; 2013-<?php echo date("Y"); ?></span><a href="http://www.capricetech.com" target="_blank"> Caprice Cloud Solutions Pvt Ltd</a>.<span>All rights reserved.</span> </p>			
					<?php if ($isDomain=="hrapp.co") { ?>	
						<p class="pull-right sm-pull-reset"> <span>Licensed To: </span><a href="javascript:void(0);"><?php echo $licenseOrgName; ?></a></p>
					<?php }
					else
					{?>
					<p class="pull-right sm-pull-reset"> <span>Powered By: </span>HRAPP</p>
					<?php }
					?>			
                    </div>
                    <div class="copyright" style="border-top: none; padding: 5px 0px;">
					<p class="sm-pull-reset" style="text-align: center;"> <span><span class="supportspan"><a href="<?php echo $domainDetails['Support_Link']; ?>" target="_blank" class="m-r-10">Support</a></span> | 
					<a href="<?php echo $termsOfUse; ?>" target="_blank" class="m-l-10 m-r-10">Terms of use</a> | 
					<a href="<?php echo $privacyPolicy; ?>" target="_blank" class="m-l-10">Privacy Policy</a></span></p>                        
                    </div>
                    <?php } else { ?>
                    <div class="copyright">
                        <p class="pull-left sm-pull-reset">
						<span>&copy; 2013-<?php echo date("Y"); ?> </span>
						<a href="http://www.capricetech.com" target="_blank">Caprice Cloud Solutions Pvt Ltd.</a>
						<span>All rights reserved.</span>
						</p>
						<p class="pull-right sm-pull-reset"> <span><span class="supportspan"><a href="<?php echo $domainDetails['Support_Link']; ?>" target="_blank" class="m-r-10">Support</a></span> | 
						<a href="<?php echo $termsOfUse; ?>" target="_blank" class="m-l-10 m-r-10">Terms of use</a> | 
						<a href="<?php echo $privacyPolicy; ?>" target="_blank" class="m-l-10">Privacy Policy</a></span></p>                         
                    </div>
		    
		    
                    <?php } ?>
		    
                </div>
				<?php } else { ?>
					<div class="copyright">                        
						<p class="pull-right sm-pull-reset"> <span><span class="supportspan"><a href="<?php echo $domainDetails['Support_Link']; ?>" target="_blank" class="m-r-10">Support</a></span> | 
						<a href="<?php echo $termsOfUse; ?>" target="_blank" class="m-l-10 m-r-10">Terms of use</a> | 
						<a href="<?php echo $privacyPolicy; ?>" target="_blank" class="m-l-10">Privacy Policy</a></span></p>                        
                    </div>
					
				<?php } ?>
            </div>
            <!-- END PAGE CONTENT -->
        </div>
        
        <!-- BEGIN PAGE SCRIPT -->
		<!-- <script src="<?php echo $this->baseUrl('assets/global/plugins/jquery/jquery-1.11.1.min.js'); ?>"></script>
        <script src="<?php echo $this->baseUrl('assets/global/plugins/jquery/jquery-migrate-1.2.1.min.js'); ?>"></script> -->
		<script src="https://code.jquery.com/jquery-3.7.1.js" integrity="sha256-eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=" crossorigin="anonymous"></script>
		<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-migrate/3.5.2/jquery-migrate.js"></script>
        <script src="<?php echo $this->baseUrl('assets/global/plugins/gsap/main-gsap.min.js'); ?>"></script>
        <script src="<?php echo $this->baseUrl('assets/global/plugins/bootstrap/js/bootstrap.min.js'); ?>"></script>
        <script src="<?php echo $this->baseUrl('assets/global/plugins/backstretch/backstretch.min.js'); ?>"></script>
        <script src="<?php echo $this->baseUrl('assets/global/plugins/bootstrap-loading/lada.min.js'); ?>"></script>
	<script src="<?php echo $this->baseUrl('assets/global/js/popup/jquery.magnific-popup.min.js'); ?>"></script>
	
		
        <script type="text/javascript">
		
		 $('.inline-popups').magnificPopup({
          delegate: 'a',
          removalDelay: 500, //delay removal by X to allow out-animation
          callbacks: {
            beforeOpen: function() {
               this.st.mainClass = this.st.el.attr('data-effect');
            }
          },
          midClick: true // allow opening popup on middle mouse click. Always set it to true if you don't provide alternative source.
        });
        
        // Image popups
        $('.image-popups').magnificPopup({
          delegate: 'a',
          type: 'image',
          removalDelay: 500, //delay removal by X to allow out-animation
          callbacks: {
            beforeOpen: function() {
              // just a hack that adds mfp-anim class to markup 
               this.st.image.markup = this.st.image.markup.replace('mfp-figure', 'mfp-figure mfp-with-anim');
               this.st.mainClass = this.st.el.attr('data-effect');
            }
          },
          closeOnContentClick: true,
          midClick: true // allow opening popup on middle mouse click. Always set it to true if you don't provide alternative source.
        });
        
        // Hinge effect popup
        $('a.hinge').magnificPopup({
          mainClass: 'mfp-with-fade',
          removalDelay: 1000, //delay removal by X to allow out-animation
          callbacks: {
            beforeClose: function() {
                this.content.addClass('hinge');
            }, 
            close: function() {
                this.content.removeClass('hinge'); 
            }
          },
          midClick: true
        });
		
        // <![CDATA[
            $(document).ready(function(){
                if (localStorage.getItem('production') === null) {
                    localStorage.setItem('production', <?php echo $isProduction; ?>);
				}
				
            });
        // ]]>
        </script>
        
		
      	<script src="<?php echo $this->baseUrl('assets/global/js/custom/billing-payment.js?v=1'); ?>"></script>
        <script src="<?php echo $this->baseUrl('assets/md-layout1/material-design/js/material.js?v=1'); ?>"></script>
        <script src="<?php echo $this->baseUrl('assets/layout1/js/layout.js?v=1'); ?>"></script>
        <script>
			$.material.init();
        </script>
        <!-- END PAGE SCRIPT -->
    </body>
</html>