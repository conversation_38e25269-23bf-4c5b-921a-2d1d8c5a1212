<?php
//=========================================================================================
//=========================================================================================
/* Program        : Ehr.php			 													 *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description    : Contains list of all table names and common functions		      	 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions      :                                                                    	 *
 *   Version         Date           Author                  Description                  *
 *     0.1        30-May-2013     Narmadha                Initial Version                *
 *                                                                                    	 *
 *     1.0        02-Feb-2015     Prasanth             Changed in file for mobile app    *
 *     1.5        13-Aug-2016     Shobana              added getOrgAddress function      *
 *                                                                                    	 */
//=========================================================================================
//=========================================================================================
class Application_Model_DbTable_Ehr extends Zend_Db_Table_Abstract
{
    protected $_ehrdb = null;
    public $_isProduction = null;
    public $_isDomain = null;
    public $uploadPath = null;    
  
    //AppManager table Names
    public $domainSettings = 'domain_settings';
    
    public $billingRate = 'billing_rate';
    
    public $planforms = 'hrapp_plan_form';
    
    public $billingAudit = 'audit_billing';
    
    public $settings = 'settings';
    
    public $customForm = 'customization_forms';
    
    public $customField = 'customization_fields';
    
    public $appForms = 'forms';
    
    public $appFields = 'fields';
    
    public $perquisites = 'perquisites';
    
    public $billingTransaction = 'billing_transaction';
    
    public $hraSettings = 'hra_settings';
    
    public $hraCity = 'hra_city';
    
    public $companyInfo = 'company_info';
    
    public $country = 'country'; // 2 db
    
    public $regUser =  'hrapp_registeruser';
    
    public $orgChoiceRate = 'org_rate_choice';

    public $orgRateChoiceAuthenticationMapping = 'org_rate_choice_authentication_mapping';
    
    public $orgAuthenticationMethods = 'org_authentication_methods';
    
    public $paymentDetails = 'payment_details';
    
    public $taxCategory = 'tax_category'; // 2 db
    
    public $appLogin = 'hrapp_login';
    
    public $registerUser = 'hrapp_registeruser';
    
    public $prerequisite = 'prerequisites';
    
    public $announcements = 'announcements';

    public $banksPaymentType ='banks_payment_type';

    public $accountCode = 'account_code';

    public $appManagerAccountType ='account_type'; 

    public $appManagerBankDetails ='bank_details';

    public $appManagerBankTransaction ='bank_transaction';

    public $transactionType = 'transaction_type';
    
    public $transactionAlias = 'transaction_alias';

    public $accountSchema = 'account_schema';

    public $hrappBankField = 'hrapp_bank_field_details';

    public $transactionCategory = 'transaction_category';

    public $transactionTypeDetails='transaction_type_details';
    
    public $transactionPreference='transaction_preference';
		
    //Hrapp Table Names
    public $departmentLevel ='department_level_access';
    
    public $fbpTaxDeclarationSettings = 'fbp_declaration_settings';

    public $bankDetails ='bank_details';
    
    public $organizationAccount ='organization_account';
    
    public $professionalTax = 'professional_tax';
    
    public $state = 'state';
      
    public $city = 'city';
    
    public $month = 'months';
    
    public $professionalTaxMonth='professional_tax_month';
  
    public $ptPayment='professional_tax_payment';
    
    public $tdsPayment='tds_payment';
    
    public $tdsPaymentTracker='tds_payment_tracker';
    
    public $oltas ='oltas_status';
    
    public $ptPaymentTracker='pt_payment_tracker';
    
    public $taxSectionAllowance='tax_section_allowance_mapping';
    
    public $auditReimbursement = 'audit_reimbursement';
    
    public $reimbursementFileUpload = 'reimbursement_upload_files';
    
    public $auditAllowance = 'audit_allowance';
    
    public $auditDeductions = 'audit_deductions';
    
    public $auditCommission = 'audit_commission';
    
    public $auditBonus = 'audit_empbonus';
    
    public $auditfixedInsurance = 'audit_fixedinsurance';

    public $salaryHistory = 'audit_salary';
    
    public $advanceSalaryHistory = 'audit_advancesalary';
    
    public $auditLoan = 'audit_emploan';
    
    public $auditAssignment = 'audit_assignment';
    
    public $auditTimesheet = 'audit_timesheet';
    
    public $auditShift = 'audit_empshift';
    
    public $auditWages = 'audit_hourly_wages';
    
    public $auditEmpTravels = 'audit_emptravels';
    
    public $taxDeclarationHistory = 'audit_tax_declaration';
    
    public $salaryOutstanding = 'salary_outstanding';

    public $transactionBalance = 'transaction_balancesheet';

    public $empPersonal = 'emp_personal_info';

    public $languages = 'languages';

    public $complaint = 'emp_complaints';

    public $attendanceHeader = 'attendance_header_format';

    public $attendanceStatus = 'attendance_status_pairs';

    public $attendanceWorkPlace = 'attendance_work_place';

    public $orgpolicyLocDept = 'org_policy_multi_loc_dept';

    public $warnings = 'warnings';

    public $memos = 'memos';

    public $attendanceImport = 'emp_attendance_import';
    
    public $employeeImport = 'employee_data_import';

    public $attendanceReport = 'attendance_report';

    public $holidays = 'holidays';
    
    public $holiday = 'holiday';
    
    public $holidayAssign = 'holiday_assignment';

    public $wageIncentive = 'hourlywage_incentive';
    
    public $wageDeduction = 'hourlywage_deduction';

    public $orgProfile = 'org_profile';

    public $orgPolicies = 'org_policies';
    
    public $orgPolicyFileUpload='org_polices_upload_files';

    public $attendance = 'emp_attendance';

    public $policyTypes = 'policy_types';

    public $reimbursement = 'reimbursement';

    public $expenseTypes = 'expense_head';
    
    public $expenseTypeGrade = 'expense_type_grade';

    public $empPF = 'emp_pf';

    public $deductions = 'deductions';

    public $orgPF = 'org_pf';

    public $pfArchive = 'pf_archive';

    public $providentFundSettings = 'provident_fund_settings';

    public $providentFund = 'provident_fund';

    public $empGrade = 'emp_grade';

    public $empBonus = 'emp_bonus';

    public $bonusType ='bonus_type';

    public $recruitment = 'recruitment';

    public $modules = 'modules';

    public $empJob = 'emp_job';

    public $empJobAuditHistory = 'emp_job_audit_history';
    
    public $skillMatrix = 'skill_matrix';
    
    public $skillset = 'skillset_assessment';

    public $allowances = 'allowances';
    
    public $benefitForms = 'benefit_forms';
    
    public $allowanceBenefitAssoc = 'allowance_type_benefit_association';

    public $dept = 'department';

    public $comment = 'comment_generation';

    public $sessionList = 'user_session_list';

    public $sysConfig = 'system_config';

    public $systemLog = 'system_log';

    public $sessionLock = 'user_session_throw_lock';

    public $salary = 'salary_details'; 

    public $resignation = 'emp_resignation';

    public $transfer = 'emp_transfer';

    public $fixedInsurance = 'fixed_insurance';

    public $variableInsurance = 'variable_insurance';

    public $insuranceType = 'insurance_type';

    public $insuranceGrade = 'insurance_grade';

    public $insurancetypeGrade = 'insurancetype_grade';

    public $wageInclusionHeader = 'wage_inclusion_header';

    public $insuranceWageInclusion = 'insurance_wage_inclusion';

    public $empLogin = 'emp_user';

    public $empLoan = 'emp_loan';

    public $maritalRelation = 'marital_status_relationship';

    public $loanType = 'loan_type';

    public $forms = 'ehr_forms';

    public $skills = 'skills';

    public $empType = 'employee_type';

    public $designation = 'designation';

    public $payment = 'payment_mode';

    public $roles = 'ehr_roles';

    public $role = 'roles';

    public $roleAccessControl = 'rolesbased_access_control';

    public $commission = 'commission';
    
    public $commissionTypes = 'commission_types';
    
    public $commissionPercentage = 'commission_percentage';

    public $empAccessRights = 'emp_accessrights';

    public $project = 'project_details';

    public $empProject = 'emp_project';

    public $empBank = 'emp_bankdetails';

    public $assignment = 'assignment';

    public $empAssets = 'emp_assets';

    public $empPassport = 'emp_passport';

    public $advanceSalary = 'advance_salary';

    public $timesheetActivity = 'project_activities';

    public $empTimesheet = 'emp_timesheet';
    
    public $empTimesheetTracking ='emp_timesheet_tracking';

    public $timesheetSettings='timesheet_settings';

    public $timezone = 'timezone';

    public $currency = 'currency';

    public $orgDetails = 'org_details';

    public $orgContact = 'org_contactdetails';
    
    public $taxConfiguration = 'tax_configuration';
    
    public $taxFormSectionMapping = 'taxform_section_mapping';

    public $empShift = 'emp_shift';

    public $shiftType = 'shift_type';

    public $appliedCandidate = 'applied_candidate';

    public $unit = 'unit_data';

    public $empLeaves = 'emp_leaves';

    public $alternatePerson= 'alternate_person_details';

    public $leavetype = 'leave_types';
    
    public $leavetypegrade = 'leavetype_grade';
    
    public $leaveFreeze = 'leave_freeze';

    public $encashedLeave = 'encashed_leaves';
    
    public $empServiceLeave = 'emp_service_leave';
    
    public $leaveQuarter = 'leave_quarter';
    
    public $maternitySlab = 'maternity_slab';
    
    public $empExperienceLeave = 'emp_experience_leave';

    public $skillLevelAssoc = 'skill_level_association';

    public $skillLevels = 'skill_levels';

    public $skillDefinition = 'skill_definition';
    
    public $performance = 'performance_assessment';
    
    public $destination = 'destination_details';
    
    public $performanceLevel = 'performance_assessment_level';
    
    public $specialWages = 'holiday_specialwages';

    public $location = 'location';

    public $monthlyPayslip = 'salary_payslip';

    public $salaryIncentive = 'taxable_earnings';
    
    public $nontaxEarnings = 'nontaxable_earnings';

    public $actualTaxAllowance = 'actual_taxable_allowance';
    
    public $actualNonTaxAllowance = 'actual_nontaxable_allowance';

    public $reimbursementAllowance = 'reimbursement_allowance';

    public $salaryDeduction = 'salary_deduction';

    //public $paycycle = 'salary_paycycle';

    public $empInbox = 'emp_inbox';

    public $empExperience = 'emp_experience';

    public $empCertifications = 'emp_certifications';

    public $empLang = 'emp_language';

    public $empHobbies = 'emp_hobbies';

    public $empDependent = 'emp_dependent';

    public $empEducation = 'emp_education';

    public $maritalStatus ='marital_status';

    public $empTraining = 'emp_training';

    public $empTravels = 'emp_travels';

    public $empSkillset = 'emp_skillset';

    public $empAward = 'emp_awards';

    public $awards = 'awards';

    public $awardtypes = 'award_types';

    public $taxDeclaration	= 'tax_declaration';

    public $taxExemptions = 'tax_exemption';

    public $hrReports = 'hr_reports';

    public $taxRebates = 'tax_rebate';

    public $taxSections = 'tax_section';

    public $sectionsInvestment = 'section_investment_category';

    public $empDrivingLicense = 'emp_drivinglicense';

    public $empContacts = 'contact_details';

    public $wagePayslip = 'hourlywages_payslip';

    public $hourlyWages = 'hourly_wages';

    public $taxEntity = 'tax_entity';
    
    public $taxSurcharge = 'tax_surcharge';

    public $taxRates = 'tax_rates';

    public $timesheetHrs = 'timesheet_hours';

    public $tmpPath = 'upload/';

    public $workSchedule = 'work_schedule';
    
    public $weekdays = 'weekdays';

    public $empInsurancePolicyNo = 'emp_insurancepolicyno';

    public $empPfPayment = 'emp_pf_payment';

    public $orgPfPayment = 'org_pf_payment';
    
    public $orgPfPaymentTracker = 'org_pf_payment_tracker';
    
    public $orgInsPaymentTracker = 'org_insurance_payment_tracker';

    public $orgInsurancePayment = 'org_insurance_payment';

    public $empInsurancePayment = 'emp_insurance_payment';

    public $deferredLoan = 'deferred_loan';
    
    public $empEligbleLeave = 'emp_eligible_leave';

    public $auditEmpEligbleLeave = 'audit_emp_eligible_leave';
    
    public $biometricErrorLog = 'biometric_error_log';

    public $errorCodes = 'error_codes';
    
    public $salaryDataImport = 'salary_data_import';
    
    public $esicReason = 'esic_reason';
    
    public $accountType = 'account_type';
    
    public $mailConf = 'mail_client_configuration';
    
   
    public $workScheduleWeekOff ='workschedule_weekoff';

    public $weekoffDates ='weekoff_dates';
    
    public $attendanceSettings = 'attendance_settings';
    public $loanSettings = 'loan_settings';
    public $gratuitySettings = 'gratuity_settings';
    public $gratuity = 'gratuity';
    public $gratuityHistory = 'gratuity_history';
    public $gratuityNomination = 'gratuity_nomination';
    public $allowanceTypes = 'allowance_type';
    
    public $hrGroup = 'hr_group';
    
    public $payrollGroup = 'payroll_group';
    
    public $mailCommunicationForms = 'mail_communication_forms';

    public $payslipBulkProcessing = 'payslip_bulk_processing';

    public $bulkProcessing = 'bulk_processing';
    
    public $leaveImport = 'leave_data_import';
    
    public $leavesImport = 'emp_leaves_import';
    
    public $lateAttendanceNotificationPerson = 'late_attendance_notification_person';
    
    public $alertTypes = 'alert_types';
    
    public $alertSettings = 'alert_settings';
    
    public $courseDetails = 'course_details';

    public $hraDeclaration = 'hra_declaration';
    
    public $landLord = 'landlord_details';
    
    public $hraMonthly = 'hra_monthly';
    
    public $perquisiteTracker = 'perquisite_tracker';
    
    public $prequisiteTrackerAmt = 'perquisite_tracker_amount';
    
    public $form16 = 'form16';
    
    public $form12ba = 'form12ba';
    
    public $form24q = 'form24q';
    
    public $form16DataImport = 'form16_data_import';
    
    public $form12baPerquisites = 'form12ba_perquisites';
    
    public $documentUpload = 'document_upload';
    
    public $payslipClosure = 'quarterly_payslip_closure';
    
    public $hradeclarationfiles = 'hra_declaration_upload_files';
    
    public $govtTaxSec = 'govt_tax_sections';
    
    public $taxdeclarationfiles = 'tax_declaration_upload_files';
    
    public $orgEtf = 'org_etf';
    
    public $empEtf = 'emp_etf';
    
    public $etfArchive = 'etf_archive';
    
    public $orgEtfPaymentTracker = 'org_etf_payment_tracker';
    
    public $orgEtfPayment = 'org_etf_payment';
    
    public $empEtfPayment = 'emp_etf_payment';
    
    public $shortLeaveRequestSettings = 'short_time_off_settings';

    public $onDutySettings = 'on_duty_settings';
    
    public $shortTimeOff = 'short_time_off';
    
    public $adhocAllowance = 'adhoc_allowance';
    
    public $adhocAllowanceImport = 'adhoc_allowance_import';
    
    public $auditAdhocAllowance = 'audit_adhoc_allowance';
    
    public $docTempEngine = 'document_template_engine';
    
    public $tempCustomField = 'template_custom_fields';
    
    public $dependentImport = 'dependent_import';
    
    public $empdocument = 'emp_generated_documents';
    
    public $allowanceImport = 'allowance_data_import';
    
    public $docTempFields = 'document_template_fields';
    
    public $tempAssocFields = 'template_associated_fields';
    
    public $adhocAllowBenefitAssoc = 'adhoc_allowance_benefit_association';
    
    public $adhocAllowImportBenefitAssoc = 'adhoc_allowance_import_benefit_association';
    
    public $taxDeclarationDataImport = 'tax_declaration_data_import';

    public $hraDeclarationDataImport = 'hra_declaration_data_import';
    
    public $empProfession = 'emp_profession';
    
    public $monthlyLeaveBalance = 'monthly_leave_balance';
    
    public $compOffBalance = 'compensatory_off_balance';
    
    public $compOff = 'compensatory_off';

    public $compOffEncashment = 'compensatory_off_encashment';
    
    public $empDesHistory = 'emp_designation_history';
    
    public $empManagerHistory = 'emp_manager_history';
    
    public $empLocHistory = 'emp_location_history';

    public $empDepHistory = 'emp_department_history';
    
    public $deductionsDataImport = 'deductions_data_import';
    
    public $datasetupDashboard = 'datasetup_dashboard';
    
    public $displayFieldGroup= 'display_field_group';
    
    public $displayField= 'display_fields';
 
    public $customReportFiltertypes='custom_report_filtertypes';
    
    public $customReport='custom_report';
    
    public $customReportField='custom_report_fields';
    
    public $filter  ='custom_report_filter';
    
    public $labourWelfareFund   ='labour_welfare_fund';
    
    public $labourWelfareFundDesignation  ='labour_welfare_fund_designation';
    
    public $labourWelfareFundMonths  ='labour_welfare_fund_months';
    
    public $empLwfPayment   ='emp_lwf_payment';
    
    public $orgLwfPayment   ='org_lwf_payment';
    
    public $orgLwfPaymentTracker   ='org_lwf_payment_tracker';
    
    public $documentCategory = 'document_category';

    public $documentType = 'document_type';

    public $documentSubType = 'document_sub_type';
    
    public $empDocumentCategory = 'emp_document_category';
    
    public $empDocuments = 'emp_documents';

    public $empTdsHistory = 'emp_tds_history';
    
    public $empTdsHistoryExemption = 'emp_tds_history_exemption';

    public $fixedHealthInsType = 'fixed_health_insurance_type';
    
    public $fixedHealthIns = 'fixed_health_insurance';
    
    public $orgFHInsPayment = 'org_fixed_health_insurance_payment';
    
    public $empFHInsPayment = 'emp_fixed_health_insurance_payment';
    
    public $tdsHistoryImport = 'tds_history_import';
    
    public $tdsHisImpExempt = 'tds_history_import_exemption';
    
    public $tdsHisImpPerquisites = 'tds_history_import_perquisites';
    
    public $orgStructure = 'org_structure';
    
    public $empExperienceDocuments = 'emp_experience_documents';

    public $leaveJoinQuarter = 'leave_join_quarter';

    public $custGroupSettings = 'custom_group_settings';  
 
    public $empShiftType = 'emp_shift_type';  
    public $custGroup = 'custom_group'; 
    public $custGroupEmployees = 'custom_group_employees'; 
    // public $shiftScheduling = 'shift_scheduling'; 
    public $shiftEmpMapping = 'shift_emp_mapping'; 

    public $deviceManagement = 'device_management';

    public $attendanceSyncHistory = 'attendance_sync_history';

    public $lateAttendanceLeaveTypes= 'late_attendance_leave_types';

    public $pricePlans = 'subscription_plans';

    public $subscriptions = 'subscribed_plans';

    public $serviceTypes = 'service_types';

    public $serviceUsage = 'service_usage'; 
    
    public $subscriptionOrder = 'subscription_order';

    public $subscriptionInvoice = 'subscription_invoice';

    public $subscriptionPayment = 'subscription_payment_details';
    
    public $taxCategories = 'tax_categories';
    
    public $taxCatClassMap = 'tax_category_class_mapping';
    
    public $taxClass = "tax_class";

    public $dreamfactory = "dreamfactory";

    public $hrappInvoiceLabels = "hrapp_invoice_labels";

    public $invoiceLabelOptions = "invoice_label_options";

    public $empLeaveDocuments = "emp_leave_documents";

    public $eftHeader = 'eft_header';

    public $bankTransaction = 'bank_transaction';

    public $aggregatorDetails = 'aggregator_details';

    public $bankTransactionMapping = 'bank_transaction_mapping';

    public $payslipTemplate = 'payslip_template';

    public $candidatePersonal = 'candidate_personal_info';

    public $candidateDependent = 'candidate_dependent';

    public $candidateLang = 'candidate_language';

    public $candidateHobbies = 'candidate_hobbies';

    public $candidateDrivingLicense = 'candidate_drivinglicense';

    public $candidatePassport = 'candidate_passport';

    public $candidateJob = 'candidate_job';

    public $candidateCertifications = 'candidate_certifications';

    public $candidateContacts = 'candidate_contact_details';

    public $candidateEducation = 'candidate_education';

    public $candidateExperience = 'candidate_experience';

    public $candidateTraining = 'candidate_training';

    public $candidateDocuments = 'candidate_documents';

    public $candidateBankDetail = 'candidate_bankdetails';

    public $candidateUrl = 'candidate_url';

    public $candidateExperienceDocument = 'candidate_experience_documents';

    public $candidateEducationDocument = 'candidate_education_documents';

    public $candidateCertificationsDocument = 'candidate_certifications_documents';

    public $candidateTrainingDocument = 'candidate_training_documents';

    public $candidateDocumentCategory = 'candidate_document_category';

    public $candidateDocumentCategoryList = 'candidate_document_category_list';

    public $candidateDocumentType = 'candidate_document_type';

    public $candidateDocumentSubtype = 'candidate_document_sub_type';
    
    public $organizationTransactionPreference= 'organization_transaction_preferences';
    
    public $taUserTask = 'ta_user_task';

    public $taUserTaskHistory='ta_user_task_history';

    public $leaveWorkflowHistory='leave_workflow_history';

    public $taProcessInstance = 'ta_process_instance';

    public $taProcessInstanceHistory = 'ta_process_instance_history';

    public $taWorkflowVersion = 'ta_workflow_version';

    public $taWorkflow = 'ta_workflow';

    public $taEvent = 'ta_event';

    public $workflows = 'workflows';

    public $workflowModule = 'workflow_module';

    public $customEmployeeGroup = 'custom_employee_group';

    public $customEmployeeGroupEmployees = 'custom_employee_group_employees';

    public $customGroupAssociateForm = 'custom_group_associated_forms';

    public $customEmployeeGroupModuleForms ='custom_employee_group_module_forms';
   
    public $specialwageConfiguration ='specialwage_configuration';
   
    public $shiftTypeConfiguration ='shift_type_configuration';

    public $whitelistedIpAddresses = 'whitelisted_ip_addresses';

    public $appConfiguration = 'app_configuration';

    public $housePropertyIncomeSources = 'house_property_income_sources';

    public $selfOccupiedPropertyIncome = 'self_occupied_property_income';

    public $rentedPropertyIncome = 'rented_property_income';

    public $monthlyForm16Snapshot = 'monthly_form16_snapshot';

    public $holidaySettings = 'holiday_settings';

    public $holidayCustomGroupAssign = 'holiday_customgroup_assignment';

    public $autoDeclarationConfig = 'auto_declaration_configuration';

    public $employeeSalaryConfiguration = 'employee_salary_configuration';

    public $employeeSalaryDetails = 'employee_salary_details';

    public $employeeSalaryAllowance = 'employee_salary_allowance';

    public $employeeSalaryRetirals = 'employee_salary_retirals';

    public $contractorTaxSection = 'contractor_tax_section';

    public $contractorTaxRates = 'contractor_tax_rates';

    public $contractorTaxConfiguration = 'contract_employee_tds_configuration';

    public $taxDeclarationAllowanceForm16Snapshot ='taxdeclaration_allowance_form16_snapshot';

    public $annexureTwo ='annexure_two';

    public $payrollEstimation = 'payroll_estimation';

    public $payrollRoundOffSettings = 'payroll_round_off_settings';

    public $payrollGeneralSettings = 'payroll_general_settings';

    public $hrappPlanDetails = 'hrapp_plan_details';

    public $leaveSettings = 'leave_settings';

    public $rosterManagementSettings = 'roster_management_settings';

    public $deductionSettings = 'deduction_settings';

    public $adhocAllowanceSettings = 'adhoc_allowance_settings';

    public $incometaxDeclarationSettings = 'incometax_declaration_settings';

    public $shiftAllowanceSettings = 'shift_allowance_settings';

    public $performanceManagementSettings = 'performance_management_settings';

    public $performanceGoalAchievement = 'performance_goal_achievement';

    public $overtimeClaims = 'overtime_claims';

    public $overtimeSettings = 'overtime_settings';

    public $redeemRewardsDetails = 'redeem_rewards_details';

    public $investmentAgeGroup = 'investment_age_group';

    public $emMembers = 'em_members';

    public $investmentPairConfiguration = 'investment_pair_configuration';

    public $serviceProvider             = 'service_provider';

    public $employeeInfoTimestampLog    = 'employee_info_timestamp_log';
    
    public $actualTax = 'actual_tax';

    public $trstscoreContactDetails='trstscore_contact_details';

    public $candidateAccreditationDetails='candidate_accreditation_details';

    public $employeeAccreditationDetails='employee_accreditation_details';

    public $candidateSuperAnnuation='candidate_superannuation';

    public $employeeSuperAnnuation='employee_superannuation';

    public $accreditationCategoryAndType ='accreditation_category_and_type';

    public $autoApprovalWorkPlace ='auto_approval_work_place';

    public $unpaidLeaveOverride ='unpaid_leave_override';

    public $basicComponents = 'basic_components';

    public $ignoreEmpAttshortagelist = 'ignore_emp_attshortagelist';

    public $marginalReleif = 'marginal_releif';

    public $employeeFullAndFinalSettlement = 'employee_full_and_final_settlement';

    public $finalSettlementEarningsDeductions = 'final_settlement_earnings_deductions';

    public $attendanceGeneralConfiguration = 'attendance_general_configuration';

    public $businessUnit ='business_unit';

    public $formLevelCoverage ='form_level_coverage';

    public $compoffConfiguration ='compoff_configuration';

    public $employeeLopRecovery ='employee_lop_recovery';

    public $employeeLopRecoveryAmountDetails ='employee_lop_recovery_amount_details';

    public $lopRecoverySettings ='lop_recovery_settings';

    public $socialSecuritySchemeSlabs = 'social_security_scheme_slabs';

    public $npsSlabs = 'nps_slabs';

    public $philHelathSlabs = 'philhelath_slabs';

    public $insuranceConfiguration = 'insurance_configuration';

    public $taxReliefDeclaration ='tax_relief_declaration';

    public $taxReliefCategories ='tax_relief_categories';

    public $projectSettings ='project_settings';

    public $activitiesMaster ='activities_master';

    public $archiveAttendance = 'archive_emp_attendance';
    
    public $archiveLeaves = 'archive_emp_leaves';

    public $archiveCompOff = 'archive_compensatory_off';

    public $archiveShortTimeOff = 'archive_short_time_off';

    public $archiveAttendanceImport = 'archive_emp_attendance_import';

    public $reimbursementSettings = 'reimbursement_settings';

    public $reportConfiguration = 'report_configuration';

    public $orgLeavePeriodEligibleDays = 'org_leave_period_eligible_days';

    public $statutoryPayment = 'statutory_payment';

    public $leaveTypesDuration = 'leave_types_duration';

    public $performanceRatingDescription = 'performance_rating_description';

    public $taxObject = 'tax_object';

    public $ptkpConfiguration = 'ptkp_configuration';

    public $formUplineApprovals = 'form_upline_approvals';

    public $restrictedAdjacentLeaveTypes = 'restricted_adjacent_leave_types';

    public $deductionTitle = 'deduction_title';

    public $tpfcpsspfConfiguration = 'tpf_cps_spf_configuration';

    public $empLeaveAccruals ='emp_leave_accruals';

    public $employeeAttendanceSummary ='employee_attendance_summary';

    public $compOffSettings = 'compoff_settings';

    public $externalApiSyncDetails = 'external_api_sync_details';

    public $externalAPISyncStatus = 'external_api_sync_status';

    public $leaveTypesPaymentConfigurationSlab = 'leave_types_payment_configuration_slab';

    public $timeOffClosure = 'timeoff_closure';

    public $salaryRevisionDetails = 'salary_revision_details';
    
    public $revisionPayslipDetails = 'revision_payslip_details';

    public $payslipRetiralsRevisionDetails = 'payslip_retirals_revision_details';
    public $bwdMonthlyPayslip                       = 'bwd_salary_payslip';
    public $bwdSalaryIncentive                      = 'bwd_taxable_earnings';
    public $bwdNontaxEarnings                       = 'bwd_nontaxable_earnings';
    public $bwdSalaryDeduction                      = 'bwd_salary_deduction';
    public $bwdActualTaxAllowance                   = 'bwd_actual_taxable_allowance';
    public $bwdActualNonTaxAllowance                = 'bwd_actual_nontaxable_allowance';
    public $bwdActualTax                            = 'bwd_actual_tax';
    public $bwdReimbursementAllowance               = 'bwd_reimbursement_allowance';
    public $bwdMonthlyForm16Snapshot                = 'bwd_monthly_form16_snapshot';
    public $bwdTaxDeclarationAllowanceForm16Snapshot= 'bwd_taxdeclaration_allowance_form16_snapshot';
    public $bwdEmpPfPayment                         = 'bwd_emp_pf_payment';
    public $bwdEmpEtfPayment                        = 'bwd_emp_etf_payment';
    public $bwdEmpLwfPayment                        = 'bwd_emp_lwf_payment';
    public $bwdStatutoryPayment                     = 'bwd_statutory_payment';
    public $bwdEmpInsurancePayment                  = 'bwd_emp_insurance_payment';
    public $bwdEmpFHInsPayment                      = 'bwd_emp_fixed_health_insurance_payment';
    public $bwdMonthlyLeaveBalance                  = 'bwd_monthly_leave_balance';
    public $overtimeConfiguration                   = 'overtime_configuration';
    public $payslipComponentName                    = 'payslip_component_name';
    public $preApprovalSettings                     = 'pre_approval_settings';
    public $preApprovalRequests                     = 'pre_approval_requests';
    public $empInsuranceRetiralPayment = 'emp_insurance_retirals_payment';
    public $orgInsuranceRetiralPayment = 'org_insurance_retirals_payment';
    public $employeeOldSalaryRetirals = 'employee_old_salary_retirals';
    

    /**
     *  init() function used to initialize variables.
    */
    public function init()
    {
        $this->_isProduction = Zend_Registry::get('Production');
        
        $this->_isDomain = Zend_Registry::get('Domain');
        
        if (Zend_Registry::isRegistered('orgDetails'))
             $this->_orgDetails = Zend_Registry::get('orgDetails');
        
        
        if (Zend_Registry::isRegistered('subHrapp'))
            $this->_ehrdb = Zend_Registry::get('subHrapp');
        
        $this->uploadPath = 'hrapp_upload/'.$this->getOrgCode().'_tmp/';
        $this->downloadPath = 'hrapp_download/';
        
        if ($this->_isProduction)
        {
            //codes for production
            $this->filePath = $_SERVER['DOCUMENT_ROOT'].'/';
        }
        else
        {
            //codes for development
            $this->filePath = $_SERVER['CONTEXT_DOCUMENT_ROOT'].'/';// /opt/bitnami/lampstack-5.6.32-1/apps/projects/hrapp/hrapp/public/';
        }
    }

    /**
     *  dateForPhp function used to set date format.
    */
    public function dateForPhp ($givenDate)
    {
        $orgDF = $this->orgDateFormat();
        return date($orgDF['php'],strtotime($givenDate));
    }
    
    /**
     *  dateTimeForPhp function used to set date and time format
    */
    public function dateTimeForPhp($givenDate = null)
    {
        $orgDF = $this->orgDateFormat();
        return ($givenDate != null) ? date($orgDF['php'].'\a\t H:i:s',strtotime($givenDate)) : date($orgDF['php'].'\a\t H:i:s');
    }
    
    /**
     *  orgDateformat used to get dateformat by organization for dates to display in form, view, validation,grid
    */
    public function orgDateformat()
    {
        $dateFormat = $this->_orgDetails['Date_Format'];
        switch ($dateFormat)
        {
            case 'MM/DD/YYYY':
                $zendDateFormat = 'MM/DD/yyyy';
                $sqlDateFormat = '%m/%d/%Y';
                $phpDateFormat = 'm/d/Y';
                $jqDateFormat = 'mm/dd/yy';
                $jqRegex =  '/^(\d{2})(\/)(\d{2})(\/)(\d{4})$/';
                $bsDateFormat = 'mm/dd/yyyy';
                break;
            
            case 'YYYY/MM/DD':
                $zendDateFormat = 'yyyy/MM/DD';
                $sqlDateFormat = '%Y/%m/%d';
                $phpDateFormat = 'Y/m/d';
                $jqDateFormat = 'yy/mm/dd';
                $jqRegex =  '/^(\d{4})(\/)(\d{2})(\/)(\d{2})$/';
                $bsDateFormat = 'yyyy/mm/dd';
                break;
            
            case 'YYYY/DD/MM':
                $zendDateFormat = 'yyyy/DD/MM';
                $sqlDateFormat = '%Y/%d/%m';
                $phpDateFormat = 'Y/d/m';
                $jqDateFormat = 'yy/dd/mm';
                $jqRegex =  '/^(\d{4})(\/)(\d{2})(\/)(\d{2})$/';
                $bsDateFormat = 'yyyy/dd/mm';
                break;
            
            case 'DD/MM/YYYY':
            default:
                $zendDateFormat = 'DD/MM/yyyy';
                $sqlDateFormat = '%d/%m/%Y';
                $phpDateFormat = 'd/m/Y';
                $jqDateFormat = 'dd/mm/yy';
                $jqRegex =  '/^(\d{2})(\/)(\d{2})(\/)(\d{4})$/';
                $bsDateFormat = 'dd/mm/yyyy';
                break;
        }
        
        return array('zend'=>$zendDateFormat, 'sql'=>$sqlDateFormat, 'php'=>$phpDateFormat, 'jq'=>$jqDateFormat, 'regex'=>$jqRegex, 'bs'=>$bsDateFormat);
    }
    
    /**
     * to track logged in user action
     * @param string $action
     * @param integer $empId
     */
    public function trackEmpSystemAction($action, $empId)
    {
        $ipAddress = isset($_COOKIE['userIpAddress']) ? $_COOKIE['userIpAddress'] : '';
        
        $ckLogExist = $this->_ehrdb->fetchOne($this->_ehrdb->select()
                                            ->from($this->systemLog, new Zend_Db_Expr('COUNT(Employee_Id)'))
                                            ->where('Employee_Id = ?', $empId)
                                            ->where('Ip_Address = ?', $ipAddress)
                                            ->where('Log_Timestamp = ?', date('Y-m-d H:i:s'))
                                            ->where('User_Action LIKE ?', htmlentities(strip_tags(trim($action)))));
        
        if ($ckLogExist==0)
        {
            $qrySystemlog = $this->_ehrdb->insert($this->systemLog, array('Employee_Id'=>$empId,
                                                                          'Ip_Address'=>$ipAddress,
                                                                          'Log_Timestamp'=>date('Y-m-d H:i:s'),
                                                                          'User_Action'=>htmlentities(strip_tags(trim($action)))));
        }
    }
    
    /**
     *  organizationName function used to return organization name.
    */
    public function organizationName()
    {
        $getname = $this->_ehrdb->select()->from($this->orgDetails, 'Org_Name')->where('Org_Code LIKE ?', $this->getOrgCode());
        $getCompanyName = $this->_ehrdb->fetchOne($getname);
        return $getCompanyName;
    }
    
    /**
     *  organizationSubDomain function used to return organization host name
    */
    public function organizationSubDomain()
    {
        $getname = $this->_ehrdb->select()->from($this->orgDetails, 'Org_Code');
        $orgName = $this->_ehrdb->fetchOne($getname);
        //return $orgName.'hrapp.co';
        return $orgName.'.'.$this->_isDomain;
    }
    
    /**
     *  changeDateformat function used to change date format.
    */
    public function changeDateformat($dateValue)
    {
        return implode('-',explode('/',$dateValue));
    }
    
	public function getSubDomain()
	{
		if ($this->_isProduction)
        {
			$orgcodeArray = explode("/",$_SERVER['REQUEST_URI']);
			$key = array_search('subdomain', $orgcodeArray); 
            if ($key < 0 || empty($key))
                return "nosubdomain";
            else
                return $orgcodeArray[$key+1];		
        }
	}
    /**
     *  getOrgCode function used to get organization code based on production or development.
    */
    public function getOrgCode()
    {
        if ($this->_isProduction)
        {
            $orgDomain = '';
            if (strpos($_SERVER['HTTP_HOST'],'.'.$this->_isDomain) !== false)
                $orgDomain = '.'.$this->_isDomain;
            
            if (!empty($orgDomain))
                return str_replace($orgDomain, '', $_SERVER['HTTP_HOST']);
        }
        else
        {
              return 'capricetest';
        }
    }

    function getChildInstance($orgCode = null){
        $this->_appManagerDb = Zend_Registry::get('Hrapp');
        $isDomain = Zend_Registry::get('Domain');
        $isDomainArray = explode(".",$isDomain);
        $instanceDetails = array();
        if ($orgCode == null) {
            $orgCode = $this->getOrgCode();
        }
        $parentInstance = $this->_appManagerDb->fetchAll($this->_appManagerDb->select()->from(array($this->registerUser),
                            array('Org_Code','Org_Name','Org_Description','Org_Logo'))
                    ->orwhere('Org_Code = ?', $orgCode));

        $childInstance = $this->_appManagerDb->fetchAll($this->_appManagerDb->select()->from(array($this->registerUser),
                                array('Org_Code','Org_Name','Org_Description','Org_Logo'))
                        ->where('Parent_Org_Code = ?',$orgCode));

        array_push($instanceDetails, $parentInstance[0]);
        foreach($childInstance as $key => $row) {
            array_push($instanceDetails, $row);
        }
        foreach($instanceDetails as $key => $row) {
            if (!empty($row['Org_Logo']))
            {
                $commonFunction = new Application_Model_DbTable_CommonFunction();
                $logoUrl = $commonFunction->getAwsSignedUrl('LandingPageOrgLogo',$isDomainArray[0]."_upload/".$row['Org_Code']."_tmp/logos/".$row['Org_Logo'],'logoBucket');
                $instanceDetails[$key]['Org_Logo'] = $logoUrl;
            } else {
                $instanceDetails[$key]['Org_Logo'] = '';
            }
        }
        return $instanceDetails;
    }

    function getAuthenticationMethods($orgCode = null){
        $this->_appManagerDb = Zend_Registry::get('Hrapp');
        $planStatus = array('Active','NonComplaint');
        
        if ($orgCode == null) {
            $orgCode = $this->getOrgCode();
        }

        $result = $this->_appManagerDb->fetchAll($this->_appManagerDb->select()->from(array('OCR'=>$this->orgChoiceRate),array('OAM.*'))
                            ->joinInner(array('OCAM'=>$this->orgRateChoiceAuthenticationMapping),'OCR.Org_Plan_Id=OCAM.Org_Plan_Id')
                            ->joinInner(array('OAM'=>$this->orgAuthenticationMethods),'OCAM.Authentication_Method_Id=OAM.Authentication_Method',array('OAM.*'))
                            ->where('OCR.Plan_Status IN (?)', $planStatus)
                            ->where('Org_Code = ?', $orgCode));
                            
        return $result;
    }

    function getSocialMediaURls() {
        return array ("facebookURL"=>Zend_Registry::get('facebookURL'),
        "twitterURL"=>Zend_Registry::get('twitterURL'),
        "linkedinURL"=>Zend_Registry::get('linkedinURL'),
        "googleURL"=>Zend_Registry::get('googleURL'),
        "websiteURL"=>Zend_Registry::get('websiteURL'));
    }
    /**
     * Inserts a table Muliple row with specified data.
     *
     * @param mixed $tableName The table to insert data into.
     * @param array $data Multidimention Column-value pairs.
     * @return int The number of affected rows.
     * @throws Zend_Db_Adapter_Exception
     */
    public function insertMultiple($tableName, array $data)
    {
        $allValues = array();
        $columns = array();
        $bind = array();
    
        // Extract and quote col names from the array keys
        // of the first row
        $first = current($data);
        $columns = array();
        foreach (array_keys($first) as $column)
        {
            $columns[] = $this->_ehrdb->quoteIdentifier($column, true);
        }
    
        // Loop through data to extract values for binding
        foreach ($data as $rowData)
        {
            if (count($rowData) != count($columns))
            {
                /** @see Zend_Db_Adapter_Exception */
                require_once 'Zend/Db/Adapter/Exception.php';
                throw new Zend_Db_Adapter_Exception('Each row must'
                        . ' have the same number of columns.');
            }
    
            $values = array();
    
            foreach ($rowData as $key => $value)
            {
                if ($value instanceof Zend_Db_Expr)
                {
                    $values[] = $value->__toString();
                }
                else
                {
                    $values[] = '?';
                    $bind[] = $value;
                }
            }
    
            $allValues[] = "(" . implode(', ', $values) . ")";
        }
    
       // $dbAdapter = new Zend_Db_Adapter_Abstract();
        // Build the insert statement
        $sql = "INSERT INTO "
        . $this->_ehrdb->quoteIdentifier($tableName, true)
        . " ("
        . implode(', ', $columns)
        . ") VALUES "
        . implode(', ', $allValues);
        // Execute the statement and return the number of affected rows
        $stmt = $this->_ehrdb->query($sql, $bind);
        $result = $stmt->rowCount();
    
        return $result;
    }
    
    
    /**
     *  For getting org's address details
    */
    public function getOrgAddress()
    {
        $addressQry = $this->_ehrdb->select()
                        ->from(array('l'=>$this->location),
                               array('Street1','Street2','Pincode','Phone','Location_Type'))
                        ->joinInner(array('c'=>$this->city), ' c.`City_Id` = l.`City_Id` ',array('City_Name'))
                        ->joinInner(array('s'=>$this->state), ' s.`State_Id` = l.`State_Id` ',array('State_Name'))
                        ->joinInner(array('co'=>$this->country), ' co.`Country_Code` = l.`Country_Code` ',array('Country_Name'))
                        ->where('l.`Location_Type` = ?', 'MainBranch');
        return $this->_ehrdb->fetchRow($addressQry);
    }
    
    /**
     *  For getting org's address details
    */
    public function getPanTan()
    {
        $tanPanQry = $this->_ehrdb->select()
                        ->from(array('l'=>$this->taxConfiguration),
                               array('TAN','PAN'));
                        
        return $this->_ehrdb->fetchRow($tanPanQry);
    }
    
    /**
     *  mailLayout function used to set mail layout.
    */
    public function mailLayout($content, $name, $urlstring=null, $showMobileAppLink=null)
    {
        $html = new Zend_View();
        $html->setScriptPath(APPLICATION_PATH . '/views/scripts/index/');
        $html->assign('name',$name);
        $html->assign('showMobileAppLink',$showMobileAppLink);
        $html->assign('domainName',$this->getOrgCode());
        
        $orgName = $this->organizationName();
        $domain = $this->domainDetails();
        
        if (!empty($orgName))
            $html->assign('orgname', $orgName);
        
        $html->assign('content',$content);
        $html->assign('filePath',$this->filePath);
        $html->assign('domain',$domain);
        $html->assign('urlstring',$urlstring);
        $html->assign('hrAdminEmailAddress', $this->_orgDetails['HR_Admin_Email_Address']);
        $bodyText = $html->render('index.phtml');
        
        return $bodyText;
    }
    
    /**
     * get the Domain details from App manager.
     **/
    public function domainDetails ()
    {
        $this->_salesDb = Zend_Registry::get('Hrapp');
        $domainDetails = $this->_salesDb->fetchRow($this->_salesDb->select()->from(array('D'=>$this->domainSettings),
                                                        array('D.Org_Email', 'D.Contact_No','D.Organization_Name','D.Support_Email','D.Copy_Right', 'D.Support_Link','D.Sales_Email','D.Product_Site',
                                                              'D.Street1','D.Street2', 'D.City','D.State','D.Country','D.Account_No',
                                                              'D.Pincode','D.Talk_Link','D.Product_Logo','D.Terms_Link','D.Privacy_Policy_Link','D.Chat_Bot','D.Connected_Banking'))
                    
                                ->joinInner(array('C'=>$this->country), 'D.country=C.Country_Code', array('Country_Name')));
        return $domainDetails;
    }
    
    
    
    /**
     *  fiscalMonth function used to get fiscal month based on organization code.
    */
    public function fiscalMonth()
    {
    	return $this->_ehrdb->fetchOne($this->_ehrdb->select()->from($this->orgDetails, 'Fiscal_StartMonth')->where('Org_Code LIKE ?', $this->getOrgCode()));
    }
    
    /**
     *  loggedEmpTimezone function used to get time zone based on logged employee's location.
    */
    public function loggedEmpTimezone($empId)
    {
    	$qryEmpTimezone = $this->_ehrdb->select()->from(array('J'=>$this->empJob), array())
    	->joinLeft(array('L'=>$this->location), 'L.Location_Id=J.Location_Id', array())
    	->joinInner(array('TZ'=>$this->timezone), 'L.Zone_Id = TZ.Zone_Id', 'TZ.TimeZone_Id')
    	->where('J.Employee_Id = ?', $empId);
     	return $this->_ehrdb->fetchOne($qryEmpTimezone);
    }
    
    /**
     * getEmployeeId to get employeeId by username.
     */
    public function getEmployeeId($empUser)
    {
        /* The user signs in with their email, we can retrieve the employee id by using the employee email id. */
        return $this->_ehrdb->fetchOne($this->_ehrdb->select()->from($this->empJob, array('Employee_Id'))
            ->where('Emp_Email = ?', $empUser)
            ->where('Emp_Status = ?', "Active"));
    }

    /**
     *  listFormName function used to get list of forms name.
    */
    public function listFormName($act, $isStore=null)
    {
        $frmNameQry = $this->_ehrdb->select()
                            ->from(array('m'=>$this->modules),
                                   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS m.Module_Id as Count'),'m.Module_Name','f.Form_Name',
                                         'Form_Icon_Cls' => 'LOWER(REPLACE(f.Form_Name," ",""))'))
                            
                            ->joinInner(array('f'=>$this->forms), ' m.`Module_Id` = f.`Module_Id` ',array())
                            
                            ->where('f.`Sub_Form` = ?', 0);
        
        if (!empty($act))
            $frmNameQry->group('m.Module_Id');
        
        $rowFrmNameLevel = $this->_ehrdb->fetchAll($frmNameQry);
        
        if ($isStore == null)
        {
            $rowCountFrmNameLevel = $this->_ehrdb->fetchOne('select FOUND_ROWS()');
            
            $rowArrDealerLevel = array('total'=> $rowCountFrmNameLevel , 'rows'=> $rowFrmNameLevel);
            
            return Zend_Json::encode($rowArrDealerLevel);
        }
        else
        {
            return $rowFrmNameLevel;
        }
    }
    
    /**
     *  fetchPairsTofetchAll function used to get pair values from array.
    */
    public function fetchPairsTofetchAll($fetchPairs)
    {
    	$fetchAll = array();
    	
    	foreach ($fetchPairs  as $key => $value)
        {
    		array_push($fetchAll, array('value'=>$key, 'text'=>$value));
    	}
    	
    	return $fetchAll;
    }
    
    /**
     *  get Flexi Benefit Plan Flag
    */
    public function getFlexiBenefitPlan()
    {
        $subDomain =  $this->getOrgCode();
        return $this->_ehrdb->fetchOne($this->_ehrdb->select()->from($this->orgDetails, 'FBP_Applicable')->where('Org_Code = ?', $subDomain));        
    }
    
    
    public function gettimezoneDateTime()
    {
        $dateformat = $this->orgDateformat();
    
        $qryTimezone = $this->_ehrdb->fetchOne($this->_ehrdb->select()->from($this->timezone, array('Offset_Time'))
                                                 ->where('TimeZone_Id = ?', date_default_timezone_get()));
    	
        if(!empty($dateformat))
        {
            $dformat = $dateformat['php'];		
        }
        else
        {
            $dformat = 'dd/mm/yyyy';
        }
        
        $dt = (explode(" ",date($dformat.' h:i:s a', time())));
        $tym = explode(":",$dt[1]);
        
        $tzTime = $tym[0]." : ".$tym[1]." : ".$tym[2]." ".$dt[2];
        
        $tzDate = date('l - d F Y');
        
        return array('Date'=>$tzDate,'Time'=>$tzTime,'TimeZone'=>$qryTimezone/*,'a'=>date("Y-m-d h:i:s")*/);
    }
    
    
    public function getCustomForms($formId)
    {
        $orgCode                       = $this->getOrgCode();
        $orgCodeLevelCustomFormDetails = $this->getCustomFormDetails($formId,$orgCode);
        if(empty($orgCodeLevelCustomFormDetails))
        {
            $nonOrgCodeLevelCustomFormDetails = $this->getCustomFormDetails($formId);
            return $nonOrgCodeLevelCustomFormDetails;
        }
        else
        {
            return $orgCodeLevelCustomFormDetails;
        }
    }

    public function getCustomFormDetails($formId,$orgCode=NULL)
    {
        $this->_appManagerDb = Zend_Registry::get('Hrapp');
        
        $customForm = $this->_appManagerDb->select()->from(array('CF'=>$this->customForm),
                                                            array('Form_Id','Enable', 'New_Form_Name' ))
                                            ->joinLeft(array('F'=>$this->appForms),'F.Form_Id=CF.Form_Id',
                                                        array('F.Form_Name'))
                                            ->where('F.Form_Name = ?',$formId);

        if(!empty($orgCode))
        {
            $customForm->where('CF.Org_Code = ?',$orgCode)
                       ->where('CF.Customization_Applicable_For = ?','Specific Organization');
        }
        else
        {
            $customForm->where('CF.Customization_Applicable_For = ?','All Organization');
        }                                    
                                                
        return  $this->_appManagerDb->fetchRow($customForm);

    }
    
    public function getCustomFields($fieldName=null)
    {
        $orgCode                       = $this->getOrgCode();
        $orgCodeLevelCustomFormDetails = $this->getCustomFieldDetails($fieldName,$orgCode);
        if(empty($orgCodeLevelCustomFormDetails))
        {
            $nonOrgCodeLevelCustomFormDetails = $this->getCustomFieldDetails($fieldName);
            return $nonOrgCodeLevelCustomFormDetails;
        }
        else
        {
            return $orgCodeLevelCustomFormDetails;
        }
    }

    public function getCustomFieldDetails($fieldName=null,$orgCode=NULL)
    {
        $this->_appManagerDb = Zend_Registry::get('Hrapp');
    
        $customForm = $this->_appManagerDb->select()->from(array('CF'=>$this->customField),
                                                         array('Field_Id','Enable', 'Required', 'New_Field_Name' ))
                                            ->joinLeft(array('F'=>$this->appFields),'F.Field_Id=CF.Field_Id',
                                                            array('F.Field_Name'));
        if($fieldName !== null)
        {
            if(!empty($orgCode))
            {
                $customForm->where('CF.Org_Code = ?',$orgCode)
                           ->where('F.Field_Name = ?',$fieldName)
                           ->where('CF.Customization_Applicable_For = ?','Specific Organization');
            }
            else
            {
                $customForm->where('F.Field_Name = ?',$fieldName)
                           ->where('CF.Customization_Applicable_For = ?','All Organization');
            }
            $customFormDetails = $this->_appManagerDb->fetchRow($customForm);
        }
        else 
        {
            if(!empty($orgCode))
            {
                $customForm->where('CF.Customization_Applicable_For = ?','Specific Organization')
                                                              ->where('CF.Org_Code = ?',$orgCode);
            }
            else
            {
                $customForm->where('CF.Customization_Applicable_For = ?','All Organization');   
            }
            $customFormDetails = $this->_appManagerDb->fetchAll($customForm);
        }
        return $customFormDetails;
    }
    
    public function assessMonths()
    {
        /** get all the fiscal months **/
        $dbFinancialYr = new Default_Model_DbTable_FinancialYear();
        $fiscalAllMonths = $dbFinancialYr->financialYr(); //array(4,5...12,1,2,3)
        
		$monthNameArr = $monthNameSplitArr = $QuarterMonthArr = array();
        
        /** convert month number to month name **/
		foreach($fiscalAllMonths as $key=>$row){
			$val = '';
			$val = date('F', mktime(0, 0, 0, $row, 10));
			array_push($monthNameArr,$val);
		}
		
        /** split the array into multiple arrays **/
		$monthNameSplitArr = array_chunk($monthNameArr,3);
		
        /** form an array like (January-March),..(October-December) based on the fiscal start month **/
		for($f=0;$f<count($monthNameSplitArr);$f++){
			array_push($QuarterMonthArr,$monthNameSplitArr[$f][0].'-'.$monthNameSplitArr[$f][2]);
		}
       
        return $QuarterMonthArr;
    }
    
    
    public function getFinancialClosure()
    {
       $subDomain =  $this->getOrgCode();
        return $this->_ehrdb->fetchOne($this->_ehrdb->select()->from($this->orgDetails, 'Financial_Closure_Tracking')               
                                       ->where('Org_Code = ?', $subDomain));
    }
    
    public function getPreviousQuarter($quarter,$year)
    {
        $getAssessMnth = $this->assessMonths();
        
        $previousKey = array_search($quarter,$getAssessMnth);
        if($previousKey !=0)
        {
            $previousAssess = $getAssessMnth[$previousKey-1];
            $prevMonth = explode("-",$previousAssess);
            
            $previousAssessYr = array();
            for($x=0;$x<count($prevMonth);$x++)
            {
                $month = $this->_ehrdb->fetchOne($this->_ehrdb->select()->from($this->month,
                                                array('Month_Id'))
                                                 ->where('Month_Name=?',$prevMonth[$x]));
                $dt = '1-'.$month.'-'.$year;
                
                array_push($previousAssessYr,($month.','.$year));
                if($x == 0)
                {
                    $month = ((int)date('m', strtotime("+1 months", strtotime($dt)))).','.$year;
                    array_push($previousAssessYr,$month);
                }
            }
            
             $payslip = $this->_ehrdb->fetchOne($this->_ehrdb->select()->from($this->monthlyPayslip, new Zend_Db_Expr("count('Payslip_Id')")   )            
                                       ->where('Salary_Month IN (?)', $previousAssessYr));
            
            if($payslip > 0)
                return $previousAssess;
        }
        else
        {
            return false;
        }
    }
    
    public function getQuarterTDS($quarterMnth,$financialYr,$paymentStatus)
    {
        $response = array();
        $isTdsPaymentExists = $isTdsPaid = 0;

        $quat = explode("-",$quarterMnth);
        $financeYr = explode("-",$financialYr);
        $this->_orgDF = $this->orgDateformat();
         
        $fiscalMnth = $this->fiscalMonth();
        
        $month = $this->_ehrdb->fetchPairs($this->_ehrdb->select()->from($this->month,
                                                array('Month_Id','Month_Name')));
        
        $mnth1 = ((array_search($quat[0],$month) < $fiscalMnth) ? array_search($quat[0],$month).','.$financeYr[0] : array_search($quat[0],$month).','.($financeYr[0]-1));
        $mnth2 = ((array_search($quat[0],$month)+1 < $fiscalMnth) ? (array_search($quat[0],$month)+1).','.$financeYr[0] : (array_search($quat[0],$month)+1).','.($financeYr[0]-1));
        $mnth3 = ((array_search($quat[1],$month) < $fiscalMnth) ? array_search($quat[1],$month).','.$financeYr[0] : array_search($quat[1],$month).','.($financeYr[0]-1));
        
        $mnths = array($mnth1,$mnth2,$mnth3);
        
        $tds = $this->_ehrdb->select()->from(array('TP'=>$this->tdsPayment),
                                            array('Payment_Id'))
                                        ->where('TP.Salary_Month IN (?)',$mnths);

        $tdsPaymentDetails = $this->_ehrdb->fetchCol($tds);

        if(!empty($paymentStatus))
        {
           $tds->where('TP.Payment_Status = ?',$paymentStatus);
        }

        $tdsDetails = $this->_ehrdb->fetchCol($tds);

        /** If tds payment exists in a quarter */
        if(!empty($tdsPaymentDetails) && count($tdsPaymentDetails) > 0){
            $isTdsPaymentExists = 1;

            /** If the tds payment status is paid */
            if(!empty($tdsDetails) && count($tdsDetails) > 0){                
                $isTdsPaid = (count($tdsPaymentDetails) == count($tdsDetails)) ? 1 : 0;
            }
        }

        $response['isTdsPaymentExists'] = $isTdsPaymentExists;
        $response['isTdsPaid'] = $isTdsPaid;
        $response['quarterMonths'] = $mnths;
        $response['quarterName'] = $quarterMnth;
        $response['tdsPaymentExistInQuarters'] = !empty($isTdsPaymentExists) ? $quarterMnth : array();

        return $response;
    }
    
    public function listDivisionDetails()
    {
        $divisionDetails = $this->_ehrdb->fetchPairs($this->_ehrdb->select()->from($this->dept,array('Department_Id','Department_Name')));
  //    ->where('Parent_Type_Id = 0 OR Parent_Type_Id IS NULL')
        return $divisionDetails;
    }
    
    
   
    
    
    public function getDivisionName($departmentId)
    {
        $divisionDetails = $this->_ehrdb->fetchOne($this->_ehrdb->select()->from($this->dept,array('Department_Name'))
                                                                             ->where('Department_Id = ?',$departmentId));
        return $divisionDetails;
    }
    
    /**
     *  get Report logo name
    */
    public function getOrgReportLogo($subDomain=null,$serviceProviderId=NULL)
    {
        if(is_null($subDomain))
        {
            $subDomain =  $this->getOrgCode();    
        }
       
        if(!empty($serviceProviderId))
        {
            return $this->_ehrdb->fetchOne($this->_ehrdb->select()->from($this->serviceProvider, 'Service_Provider_Logo')->where('Service_Provider_Id = ?', $serviceProviderId));
        }
        else
        {
            return $this->_ehrdb->fetchOne($this->_ehrdb->select()->from($this->orgDetails, 'Report_LogoPath')->where('Org_Code = ?', $subDomain));
        }
        
    }
    
    /**
    * to get employeeId by firebase uid
    * The user signs in with the firebase, we can retrieve the employee id by using the firebase uid.
    */
    public function employeeIdByUid($empUser)
    {
        return $this->_ehrdb->fetchOne($this->_ehrdb->select()->from(array('L'=>$this->empLogin), array('Employee_Id'))
                                                        ->joinInner(array('EJ'=>$this->empJob),'EJ.Employee_Id = L.Employee_Id')
                                                        ->where('EJ.Emp_Status = ?',"Active")
                                                        ->where('L.Firebase_Uid = ?', $empUser));
    }

    // get the API custom domain url
    public function getApiCustomDomainUrlPath($endPointName) {
        $apiCustomDomainUrl = '';
        $customDomainBaseUrl = "https://api.".$this->_isDomain;// form the API custom domain URL

        switch($endPointName) {
            case 'billing':
                $apiCustomDomainUrl = $customDomainBaseUrl."/billing/billinggraphql";// (https://api.hrapp.co.in/billing/billinggraphql)
                break;
            case 'billingRead':
                $apiCustomDomainUrl = $customDomainBaseUrl."/billing/rographql";// (https://api.hrapp.co.in/billing/rographql)
                break;
            case 'hrappbe' : 
            default:
                $apiCustomDomainUrl = $customDomainBaseUrl."/hrappBe/graphql"; // (https://api.hrapp.co.in/hrappBe/graphql)
                break;
        }

        return $apiCustomDomainUrl;
    }

    /** Clear the user session from the respective table and from the user session throw lock table. */
    public function clearUserSessionLockFlag ($logUserId)
    {
        $throwLock = $this->_ehrdb->fetchCol($this->_ehrdb->select()->from($this->sessionLock, array('Table_Name'))
                                            ->where('Session_Id = ?', $logUserId));
        
        if (!empty($throwLock))
        {
            $deleteLockTotalCount = 0;

            foreach($throwLock as $lockTable)
            {
                if ($lockTable != $this->monthlyPayslip && $lockTable != $this->wagePayslip)
                {
                    /** Set the lock flag to zero in the respective table using the login employee id */
                    $updateLockArr = array('Lock_Flag'=>0);
                    
                    if ($lockTable == 'Copy Pf')
                    {
                        $updateLockArr = array('Copy_Lock'=>0);
                        
                        $updated = $this->_ehrdb->update($this->empPF, $updateLockArr, 'Copy_Lock = '.$logUserId);
                    }
                    else
                    {
                        $updated = $this->_ehrdb->update($lockTable, $updateLockArr, 'Lock_Flag = '.$logUserId);
                    }

                    /** If the lock flag is reset to 0 in the respective table. */
                    if($updated){
                        /** delete the table name from the user session throw lock table. */
                        $deleteLock = $this->deleteUserSessionThrowLockTableName($logUserId,$lockTable);
                        $deleteLockTotalCount += $deleteLock;
                    }
                }else{
                    /** delete the table name from the user session throw lock table. */
                    $deleteLock = $this->deleteUserSessionThrowLockTableName($logUserId,$lockTable);
                    $deleteLockTotalCount += $deleteLock;
                }
            }

            if ($deleteLockTotalCount === count($throwLock))
                return true;
            else
                return false;
        } else {
            return false;
        }
    }

    /** Delete the table name from the user session throw lock table. */
    public function deleteUserSessionThrowLockTableName($logUserId,$lockTable){
        $deleteLock = 1;

        if(!empty($logUserId) && $lockTable)
        {
            $deleteThrow=[];
            $deleteThrow['Session_Id = ?'] = (int)$logUserId;
            $deleteThrow['Table_Name = ?'] = $lockTable;
            if(isset($deleteThrow['Session_Id']) && isset($deleteThrow['Table_Name']))
            {
                $deleteLock = $this->_ehrdb->delete($this->sessionLock, $deleteThrow);
            }
        }
        
        return $deleteLock;
    }

    public function __destruct()
    {
        
    }
}
