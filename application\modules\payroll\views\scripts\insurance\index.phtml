<?php
	$insuranceAccess        = $this->insuranceAccess;
	$insuranceTypeAccess    = $this->insuranceTypeAccess;
	$insuranceGradeAccess   = $this->insuranceGradeAccess;
	$insurancePaymentAccess = $this->insurancePaymentAccess;

	$insuranceTypesPair      = $this->insuranceTypesPair;
	$orgInsuranceTypesFIPair = $this->orgInsuranceTypesFIPair;
	$employeeNameFixedIns    = $this->employeeNameFixedInsurance;
	$employeeNameVariableIns = $this->employeeNameVariableInsurance;
	$fiscalStartMonth        = $this->fiscalMonth;
	$empInsuranceTypesPair   = $this->empInsuranceTypesPair;
	$gradesPair              = $this->gradesPair;
	$paymentModePair         = $this->paymentModePair;
	
	$listWageInclusion       = $this->listWageInclusion;
	
	$customFormNameA = $this->customFormNameA;
	$customFormNameB = $this->customFormNameB;
	$customFormNameC = $this->customFormNameC;
	$customFormNameD = $this->customFormNameD;
	$customFormNameE = $this->customFormNameE;
	$customFormNameF = $this->customFormNameF;

	$department = array();
	$variableInsDepartment = array();
	
	foreach ($employeeNameFixedIns as $key => $row) {
		if (!in_array($row['Department_Name'], $department)) {
			array_push($department, $row['Department_Name']);
		}
	}
	
	foreach ($employeeNameVariableIns as $key => $row) {
		if (!in_array($row['Department_Name'], $variableInsDepartment)) {
			array_push($variableInsDepartment, $row['Department_Name']);
		}
	}
	
	$finalFormName = ((!empty($customFormNameA) && !empty($customFormNameA['New_Form_Name'])) ? $customFormNameA['New_Form_Name'] : $this->formNameA);
	
	$this->headTitle($finalFormName);
	
	if ($insuranceAccess['View'] && ((!empty($customFormNameA) && array_key_exists("Enable",$customFormNameA) && $customFormNameA['Enable'] == 1) || empty($customFormNameA))) {
	
?>
<?php if((int)$insuranceTypeAccess['View'] === 1) {?>
<div class="col-md-12 portlets paddingCls tab-spacing-cls hidden" id="insuranceForm">
	<div class="col-md-12 paddingCls bg-f9f9f9 tab-wrapper">
		<div class="pointer-cls border-bottom-secondary tab-border-cls bg-f9f9f9 tab-body" id="insuranceTab">
			<div class="tab-active-text tab-text-font text-secondary custom-tab-content"  id="insuranceFormTab">
				<?php echo !empty($customFormNameA['New_Form_Name']) ? $customFormNameA['New_Form_Name'] : $this->formNameA ?>
			</div>
		</div>
		<?php if($insuranceTypeAccess['View']) {?>
		<div class="pointer-cls border-bottom-secondary bg-f9f9f9 tab-body" id="insuranceTypeTab">
			<div class="tab-text-font custom-tab-content" id="insuranceTypeFormTab">
				<a id="formTabLink" class="tab-a-tag-color" href=<?php echo $this->baseUrl('v3/tax-and-statutory-compliance/insurance'); ?>>
				<?php echo !empty($customFormNameB['New_Form_Name']) ? $customFormNameB['New_Form_Name'] : $this->formNameB ?>
				</a>
			</div>
		</div>
		<?php }	?>
	</div>
</div>
<?php }	?>
<!--Fixed Insurance Grid Panel-->
<div class="col-md-12 portlets add-panel-padding">
	<div class="panel" id="gridPanelFixedInsurance">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong id="lblFormNameF"><?php echo ((!empty($customFormNameF) && !empty($customFormNameF['New_Form_Name'])) ? $customFormNameF['New_Form_Name'] : $this->formNameF);?></strong></h3>
		</div>
		<div class="panel-content">
			<!--Fixed Insurance Grid Toolbar Icons-->
			<div class="m-b-10">
				<?php if ($insuranceAccess['Add']) { ?>
				<button type="button" class="btn btn-white btn-embossed toolbar-icons" data-toggle="modal" data-target="#modalFormFixedInsurance" id="addFixedInsurance" title="Add" >
					<i class="mdi-content-add"></i><span class="hidden-xs hidden-sm"> Add</span>
				</button>
				<?php  } ?>
				
				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="viewFixedInsurance" title="View">
					<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
				</button>
				
				<?php if ($insuranceAccess['Update']) { ?>
				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="editFixedInsurance" title="Edit">
					<i class="mdi-editor-mode-edit"></i><span class="hidden-xs hidden-sm"> Edit</span>
				</button>
				<?php } if ($insuranceAccess['Delete']) { ?>
				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="deleteFixedInsurance" title="Delete">
					<i class="mdi-action-delete"></i><span class="hidden-xs hidden-sm"> Delete</span>
				</button>
				<?php } ?>
				
				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="History" id="historyFixedInsurance">
					<i class="mdi-action-history"></i><span class="hidden-xs hidden-sm"> History</span>
				</button>
				
				<a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterFixedInsurance">
					<i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm"> Filter</span>
				</a>
			</div>
			
			<!-- Fixed Insurance Grid Table -->
			<table class="table dataTable table-striped table-dynamic table-hover" id="tableFixedInsurance">
				<thead>
					<tr>
						<th></th>
						<th id="fixedInsuranceEmployeeId">Employee Id</th>
						<th id="fixedInsuranceCoverage">Coverage</th>
						<th id="fixedInsuranceEmployeeName">Employee Name</th>
						<th id="fixedInsuranceInsuranceName">Insurance Name</th>
						<th id="fixedInsuranceInsuranceAmount">Insurance Amount</th>
						<th id="fixedInsuranceDurationYears">Duration(Years)</th>
						<th id="fixedInsurancePaymentFrequency">Payment Frequency</th>
						<th id="fixedInsurancePremium">Premium</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
		</div>
	</div>
</div>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="fixedInsurance-context-menu" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextFixedInsurance"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($insuranceAccess['Update']) { ?>
		<li><a tabindex="-1" id="editContextFixedInsurance"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<?php } if($insuranceAccess ['Delete']) { ?>
		<li><a tabindex="-1" id="deleteContextFixedInsurance"><i class="mdi-action-delete"></i> Delete</a></li>
		<?php } ?>
		<li><a tabindex="-1" id="historyContextFixedInsurance"><i class="mdi-action-history"></i> History</a></li>
	</ul>
</div>

<!--Add, Edit, View FOrm modal for fixed insurance-->
<div class="modal fade" id="modalFormFixedInsurance" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace" id="backFixedInsurance"></i>
				</button>
				
				<?php if ($insuranceAccess['Update']) { ?>
				<button type="button" class="close form-icons" aria-hidden="true" id="editInViewFixedInsurance">
					<i class="mdi-editor-mode-edit"></i>
				</button>
				<?php } ?>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<!--View Fixed Insurance Form-->
				<form role="form" id="viewFormFixedInsurance" >
					<div class="row">
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Coverage</label></div>
							<div class="col-md-7"><p id="viewFICoverage"></p></div>
						</div>
						<div class="form-group vCoverageEmployee">
							<div class="col-md-5"><label class="control-label">Employee Name</label></div>
							<div class="col-md-7"><p id="viewFIEmployeeName"></p></div>
						</div>
						<div class="form-group vCoverageEmployee" >
							<div class="col-md-5"><label class="control-label">Start Month</label></div>
							<div class="col-md-7"><p id="viewFIStartMonth"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Insurance Type</label></div>
							<div class="col-md-7"><p id="viewFIInsuranceType"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Payment Frequency</label></div>
							<div class="col-md-7"><p id="viewFIPaymentFrequency"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Insurance Duration(Years)</label></div>
							<div class="col-md-7"><p id="viewFIInsuranceDuration"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Insurance Amount</label></div>
							<div class="col-md-7"><p id="viewFIInsuranceAmount"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Premium</label></div>
							<div class="col-md-7"><p id="viewFIPremium"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Description</label></div>
							<div class="col-md-7"><p id="viewFIDescription"></p></div>
						</div>
					</div>
					
					<div class="row">
						<hr class="view-hr"/>
						
						<div class="form-group" style="font-size: large;margin-left: 13px;">
							<label class="control-label text-center">Additional Information</label>
						</div>
						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Added On</label></div>
							<div class="col-md-7"><p id="addedOnFixedInsurance"></p></div>
						</div>
						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Added By</label></div>
							<div class="col-md-7"><p id="addedByFixedInsurance"></p></div>
						</div>
						
						<div class="form-group updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated On</label></div>
							<div class="col-md-7"><p id="updatedOnFixedInsurance"></p></div>
						</div>
						
						<div class="form-group updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated By</label></div>
							<div class="col-md-7"><p id="updatedByFixedInsurance"></p></div>
						</div>
					</div>
				</form>
				
				<?php if ($insuranceAccess['Add'] == 1 || $insuranceAccess['Update'] == 1) { ?>
				
				<!--Add/Edit Fixed Insurance Form-->
				<form role="form" class="form-horizontal form-validation" id="editFormFixedInsurance" method="POST" action="">
					<input type="hidden" name="Fixed_Insurance_Id" id="formFixedInsuranceId" />
					
					<div class="row">
						<div class="form-group">
							<label class="col-md-3 control-label">Coverage</label>
							<div class="col-md-9">
								<select class="form-control vRequired" data-search="true" id="editformFICoverage" name="editformFICoverage" >
									<option value="0" selected="selected">Organization</option>
									<option value="1" >Employee</option>
								</select>
							</div>
                        </div>
						
						<div class="form-group coverageBasedHidden">
							<label class="col-md-3 control-label">Employee Name <span class="short_explanation">*</span></label>
							<div class="col-md-9">
								<select class="form-control" data-search="true" id="editformFIEmployeeName" name="editformFIEmployeeName">
									<?php
									foreach ($department as $key => $row) {
										echo '<optgroup label="'. $row .'">';
										
										foreach ($employeeNameFixedIns as $empKey => $empRow) {
											if ($row == $empRow['Department_Name']) {
												echo '<option value="'. $empRow['value'] .'">'. $empRow['text'] .'</option>';
											}
										}
										
										echo '</optgroup>';
									}
									?>
								</select>
							</div>
                        </div>
						
						<div class="form-group coverageBasedHidden">
							<label class="col-md-3 control-label">Start Month <span class="short_explanation">*</span></label>
							<div class="col-md-9">
								<label class="control-label" id="editformFIStartMonth" style="margin-top : 5px;"></label>
							</div>
                        </div>
						
						<div class="form-group">
							<label class="col-md-3 control-label">Insurance Type <span class="short_explanation">*</span></label>
							<div class="col-md-9">
								<select class="form-control vRequired" data-search="true" id="editformFIInsuranceType" name="editformFIInsuranceType" >
									
								</select>
							</div>
                        </div>
						
						<div class="form-group">
							<label class="col-md-3 control-label">Payment Frequency</label>
							<div class="col-md-9">
								<select class="form-control vRequired" data-search="true" id="editformFIPaymentFrequency" name="editformFIPaymentFrequency" >
									<option value="12">Annually</option>
									<option value="6">Half Yearly</option>
									<option value="1">Monthly</option>
									<option value="3">Quarterly</option>
								</select>
							</div>
                        </div>
						
						<div class="form-group">
							<label class="col-md-3 control-label">Insurance Duration <span class="short_explanation">*</span></label>
							<div class="col-md-9">
								<select class="form-control vRequired" data-search="true" id="editformFIInsuranceDuration" name="editformFIInsuranceDuration" >
									<option value="">-- Select --</option>
									<option value="1" >1 year</option>
									<option value="3" >3 years</option>
									<option value="5" >5 years</option>
									<option value="8" >8 years</option>
									<option value="10">10 years</option>
									<option value="12">12 years</option>
									<option value="15">15 years</option>
									<option value="20">20 years</option>
									<option value="25">25 years</option>
									<option value="30">30 years</option>
								</select>
							</div>
                        </div>
						
						<div class="form-group">
							<label class="col-md-3 control-label">Insurance Amount <span class="short_explanation">*</span></label>
							<div class="col-md-9">
								<input type="number" class="form-control vRequired convertDecimalPos1" min="0" max="99999999" step="0.01" id="editformFIInsuranceAmount" name="editformFIInsuranceAmount" placeholder="Insurance Amount">
							</div>
                        </div>
						
						<div class="form-group">
							<label class="col-md-3 control-label">Premium</label>
							<div class="col-md-9">
								<input type="number" class="form-control convertDecimalPos1" id="editformFIPremium" max="99999999" readonly="readonly" name="editformFIPremium" placeholder="Premium">
							</div>
                        </div>
						
						<div class="form-group">
							<label class="col-md-3 control-label">Description</label>
							<div class="col-md-9">
								<textarea name="description" id="editformFIDescription" rows="5" class="form-control vComments" placeholder="Write your Description..." ></textarea>
							</div>
                        </div>
					</div>
					
					<button type="reset" class="cancel" id="formFixedInsuranceReset" style="display: none;" ></button>
				</form>
				
				<?php } ?>
				
			</div>
			<div class="modal-footer text-center" id="formActionFixedInsurance">
				
				<?php if ($insuranceAccess['Add'] == 1 || $insuranceAccess['Update'] == 1) { ?>
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetFixedInsurance" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitFixedInsurance" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
				<?php } ?>
				
			</div>
		</div>
	</div>
</div>

<!--Filter Form-->
<div class="builder" id="filterPanelFixedInsurance">
	<div id="closeFilterFixedInsurance"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel" style="width: 100%;" id="filterResetFixedInsurance">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="filterApplyFixedInsurance">Apply</button>
			
			<div class="form-group">
				<label>Employee Name</label>
				<input type="text" class="form-control" id="filterEmployeeName" placeholder="Employee Name">
			</div>
			
			<div class="form-group">
				<label>Insurance Type</label>
				<select class="form-control" id="filterInsuranceTypeId" data-search="true" >
					<option value="">All</option>
					<?php
					foreach ($insuranceTypesPair as $key => $row)
					{
						echo '<option value="'.$key.'">'.$row.'</option>';
					}
					?>
				</select>
			</div>
			
			<div class="form-group">
				<label>Insurance Amount</label>
				<div class="input-group">
					<input type="number" class="input-md form-control" name="filterInsAmountBegin" id="filterInsAmountBegin" data-orientation="top" placeholder="Start"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="input-md form-control" name="filterInsAmountEnd"  id="filterInsAmountEnd" data-orientation="top" placeholder="End"/>
				</div>
			</div>
			
			<div class="form-group">
				<label>Duration</label>
				<div class="input-group">
					<input type="number" class="input-md form-control" name="filterInsDurationBegin" id="filterInsDurationBegin" data-orientation="top" placeholder="Start"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="input-md form-control" name="filterInsDurationEnd" id="filterInsDurationEnd" data-orientation="top" placeholder="End"/>
				</div>
			</div>
			
			<div class="form-group">
				<label>Premium</label>
				<div class="input-group">
					<input type="number" class="input-md form-control" name="filterInsPremiumBegin" id="filterInsPremiumBegin" data-orientation="top" placeholder="Start"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="input-md form-control" name="filterInsPremiumEnd" id="filterInsPremiumEnd" data-orientation="top" placeholder="End"/>
				</div>
			</div>
			
			<div class="form-group">
				<label>Payment Frequency</label>
				<select class="form-control" id="filterInsPaymentFrequency" data-search="true" >
					<option value="">All</option>
					<option value="12" >Annually</option>
					<option value="6" >Half Yearly</option>
					<option value="1" >Monthly</option>
					<option value="3" >Quarterly</option>
				</select>
			</div>
			
		</div>
	</div>
</div>

<!-- Modal for view fixed insurance history -->
<div class="modal fade" id="modalHistoryFixedInsurance" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<!--<button type="button" class="close form-icons pull-left" id="modalFormCloseHistory" style="margin-right: 10px;" aria-hidden="true"><i class="mdi-hardware-keyboard-backspace"></i></button>-->
				<!--<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="mdi-hardware-keyboard-backspace"></i></button>-->
                <button type="button" class="close form-icons pull-left" data-dismiss="modal" id="modalFormCloseFixedInsuranceHistory" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace"></i>
				</button>

                <h4 class="modal-title">View History</h4>
			</div>
			<div class="modal-body">
				<table class="table dataTable table-striped table-dynamic table-hover" id="tableFixedInsuranceHistory">
				<thead>
					<tr>
						<th></th>
						<th id="fixedInsHistoryPrevAmount">Previous Insurance Amount</th>
						<th id="fixedInsHistoryModifiedAmount">Modified Insurance Amount</th>
						<th id="fixedInsHistoryPrevPayementMode">Previous Payment Mode</th>
						<th id="fixedInsHistoryModifiedPaymentMode">Modified Payment Mode</th>
						<th id="fixedInsHistoryModifiedOn">Modified On</th>
						<th id="fixedInsHistoryModifiedBy">Modified By</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
			</div>
		</div>
	</div>
</div>

<!-- Form Dirty Confirmation Modal -->
<?php if ($insuranceAccess['Add'] || $insuranceAccess['Update']) { ?>
<div class="modal fade" id="modalDirtyFixedInsurance" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditConfFixedInsurance"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfFixedInsurance">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseConfirmFixedInsurance">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } if($insuranceAccess['Delete']==1)  { ?>
<!-- Delete COnfirmation Modal -->
<div class="modal fade" id="modalDeleteFixedInsurance" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteConfFixedInsurance"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfFixedInsurance">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConfirmFixedInsurance">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } ?>

<!--Variable Insurance Grid Panel-->
<div class="col-md-12 portlets add-panel-padding">
	<div class="panel" id="gridPanelVariableInsurance">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong id="lblFormNameE"><?php echo ((!empty($customFormNameE) && !empty($customFormNameE['New_Form_Name'])) ? $customFormNameE['New_Form_Name'] : $this->formNameE);?></strong></h3>
		</div>
		<div class="panel-content">
			<!--Variable Insurance Grid Toolbar Icons-->
			<div class="m-b-10">
				<?php if ($insuranceAccess['Add']) { ?>
				<button type="button" class="btn btn-white btn-embossed toolbar-icons" data-toggle="modal" data-target="#modalFormVariableInsurance" id="addVariableInsurance" title="Add" >
					<i class="mdi-content-add"></i><span class="hidden-xs hidden-sm"> Add</span>
				</button>
				<?php  } ?>
				
				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="viewVariableInsurance" title="View">
					<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
				</button>
				
				<?php if ($insuranceAccess['Update']) { ?>
				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="editVariableInsurance" title="Edit">
					<i class="mdi-editor-mode-edit"></i><span class="hidden-xs hidden-sm"> Edit</span>
				</button>
				<?php } if ($insuranceAccess['Delete']) { ?>
				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="deleteVariableInsurance" title="Delete">
					<i class="mdi-action-delete"></i><span class="hidden-xs hidden-sm"> Delete</span>
				</button>
				<?php } ?>
				
				<a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterVariableInsurance">
					<i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm"> Filter</span>
				</a>
			</div>
			
			<!-- Variable Insurance Grid Table -->
			<table class="table dataTable table-striped table-dynamic table-hover" id="tableVariableInsurance">
				<thead>
					<tr>
						<th></th>
						<th id="variableInsEmployeeId">Employee Id</th>
						<th id="variableInsCoverage">Coverage</th>
						<th id="variableInsEmployeeName">Employee Name</th>
						<th id="variableInsInsuranceName">Insurance Name</th>
						<th id="variableInsPaymentFrequency">Payment Frequency</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
		</div>
	</div>
</div>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="variable-insurance-context-menu" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextVariableInsurance"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($insuranceAccess['Update']) { ?>
		<li><a tabindex="-1" id="editContextVariableInsurance"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<?php } if($insuranceAccess ['Delete']) { ?>
		<li><a tabindex="-1" id="deleteContextVariableInsurance"><i class="mdi-action-delete"></i> Delete</a></li>
		<?php } ?>
	</ul>
</div>

<!--Add, Edit, View FOrm modal for variable insurance-->
<div class="modal fade" id="modalFormVariableInsurance" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace" id="backVariableInsurance"></i>
				</button>
				
				<?php if ($insuranceAccess['Update']) { ?>
				<button type="button" class="close form-icons" aria-hidden="true" id="editInViewVariableInsurance">
					<i class="mdi-editor-mode-edit"></i>
				</button>
				<?php } ?>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<!--View Variable Insurance Form-->
				<form role="form" id="viewFormVariableInsurance" >
					<div class="row">
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Coverage</label></div>
							<div class="col-md-7"><p id="viewVICoverage"></p></div>
						</div>
						<div class="form-group vICoverageEmployee">
							<div class="col-md-5"><label class="control-label">Employee Name</label></div>
							<div class="col-md-7"><p id="viewVIEmployeeName"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Insurance Type</label></div>
							<div class="col-md-7"><p id="viewVIInsuranceType"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Payment Frequency</label></div>
							<div class="col-md-7"><p id="viewVIPaymentFrequency"></p></div>
						</div>
						<div class="form-group vICoverageEmployee">
							<div class="col-md-5"><label class="control-label">Organisation Share</label></div>
							<div class="col-md-7"><p id="viewVIOrganisationShare"></p></div>
						</div>
						<div class="form-group vICoverageEmployee">
							<div class="col-md-5"><label class="control-label">Employee Share</label></div>
							<div class="col-md-7"><p id="viewVIEmployeeShare"></p></div>
						</div>
						<div class="form-group vICoverageOrganization">
							<div class="col-md-5"><label class="control-label">Define Salary Range</label></div>
							<div class="col-md-7"><p id="viewVIDefineSalaryRange"></p></div>
						</div>
						<div class="form-group vICoverageOrganization vISalRange">
							<div class="col-md-5"><label class="control-label">Minimum Salary</label></div>
							<div class="col-md-7"><p id="viewVIMinimumSalary"></p></div>
						</div>
						<div class="form-group vICoverageOrganization vISalRange">
							<div class="col-md-5"><label class="control-label">Maximum Salary</label></div>
							<div class="col-md-7"><p id="viewVIMaximumSalary"></p></div>
						</div>
						<div class="form-group vICoverageOrganization vISalRange">
							<div class="col-md-5"><label class="control-label">Contribution Period</label></div>
							<div class="col-md-7"><p id="viewVIContributionPeriod"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Wage Inclusion</label></div>
							<div class="col-md-7"><p id="viewWageInclusion"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Description</label></div>
							<div class="col-md-7"><p id="viewVIDescription"></p></div>
						</div>
					</div>
					
					<div class="row">
						<hr class="view-hr"/>
						
						<div class="form-group" style="font-size: large;margin-left: 13px;">
							<label class="control-label text-center">Additional Information</label>
						</div>
						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Added On</label></div>
							<div class="col-md-7"><p id="addedOnVariableInsurance"></p></div>
						</div>
						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Added By</label></div>
							<div class="col-md-7"><p id="addedByVariableInsurance"></p></div>
						</div>
						
						<div class="form-group updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated On</label></div>
							<div class="col-md-7"><p id="updatedOnVariableInsurance"></p></div>
						</div>
						
						<div class="form-group updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated By</label></div>
							<div class="col-md-7"><p id="updatedByVariableInsurance"></p></div>
						</div>
					</div>
				</form>
				
				<?php if ($insuranceAccess['Add'] == 1 || $insuranceAccess['Update'] == 1) { ?>
				
				<!--Add/Edit Variable Insurance Form-->
				<form role="form" class="form-horizontal form-validation" id="editFormVariableInsurance" method="POST" action="">
					<input type="hidden" name="Variable_Insurance_Id" id="formVariableInsuranceId" />
					<input type="hidden" name="Fiscal_Start_Month" id="formFiscalStartMonth" value="<?php echo $fiscalStartMonth; ?>"/>
					
					<div class="row">
						<div class="form-group">
							<label class="col-md-5 control-label">Coverage</label>
							<div class="col-md-7">
								<select class="form-control vRequired" data-search="true" id="editformVICoverage" name="editformVICoverage" >
									<option value="0" selected="selected">Organization</option>
									<option value="1" >Employee</option>
								</select>
							</div>
                        </div>
						
						<div class="form-group viCoverageBasedHidden">
							<label class="col-md-5 control-label">Employee Name <span class="short_explanation">*</span></label>
							<div class="col-md-7">
								<select class="form-control" data-search="true" id="editformVIEmployeeName" name="editformVIEmployeeName">
									<?php
									foreach ($variableInsDepartment as $key => $row) {
										echo '<optgroup label="'. $row .'">';
										
										foreach ($employeeNameVariableIns as $empKey => $empRow) {
											if ($row == $empRow['Department_Name']) {
												echo '<option value="'. $empRow['value'] .'">'. $empRow['text'] .'</option>';
											}
										}
										
										echo '</optgroup>';
									}
									?>
								</select>
							</div>
                        </div>
						
						<div class="form-group">
							<label class="col-md-5 control-label">Insurance Type <span class="short_explanation">*</span></label>
							<div class="col-md-7">
								<select class="form-control vRequired" data-search="true" id="editformVIInsuranceType" name="editformVIInsuranceType" >
									
								</select>
							</div>
                        </div>
						
						<div class="form-group">
							<label class="col-md-5 control-label">Payment Frequency</label>
							<div class="col-md-7">
								<select class="form-control vRequired" data-search="true" id="editformVIPaymentFrequency" name="editformVIPaymentFrequency" >
									<option value="12">Annually</option>
									<option value="6">Half Yearly</option>
									<option value="1">Monthly</option>
									<option value="3">Quarterly</option>
								</select>
							</div>
                        </div>
						
						<div class="form-group viPaymentFreqBasedHidden">
							<label class="col-md-5 control-label">Define Salary Range</label>
							<div class="col-md-7">
								<select class="form-control" id="editformVISalRange" name="editformVISalRange" >
									<option value="1">Yes</option>
									<option value="0" selected="selected">Ignore</option>
								</select>
							</div>
                        </div>
						
						<div class="form-group viSalRangeBasedHidden">
							<label class="col-md-5 control-label">Salary <span class="short_explanation">*</span></label>
							<div class="col-md-3">
								<input type="number" class="form-control convertDecimalPos1" id="editformVIMinSalary" name="editformVIMinSalary" placeholder="Minimum salary" min="1" max="9999999">
							</div>
							<span class="col-md-1">to</span>
							<div class="col-md-3">
								<input type="number" class="form-control convertDecimalPos1" id="editformVIMaxSalary" name="editformVIMaxSalary" placeholder="Maximum salary" min="1" max="9999999">
							</div>
						</div>

						<div class="form-group viSalRangeBasedHidden">
							<label class="col-md-5 control-label">Contribution Period <span class="short_explanation">*</span></label>
							<div class="col-md-7">
								<select class="form-control" id="editformVIContributionPeriod" name="editformVIContributionPeriod" >
									<option value="">-- Select --</option>
									<option value="1">Every month</option>
									<option value="2">Every 2 months</option>
									<option value="3">Every 3 months</option>
									<option value="4">Every 4 months</option>
									<option value="6">Every 6 months</option>
									<option value="12">Every 12 months</option>
								</select>
							</div>
                        </div>
						
						<div class="form-group viContributionPeriodBasedHidden" style=" margin-left: 5px; margin-right: 5px;">
							<table class="table dataTable table-striped table-dynamic table-hover" id="tableContributionPeriod">
								<thead>
									<tr>
										<th id="contributionPeriodContribution">Contribution</th>
										<th id="contributionPeriodStartMonth">Start Month</th>
										<th id="contributionPeriodEndMonth">End Month</th>
									</tr>
								</thead>
								<tbody>
								
								</tbody>
							</table>
						</div>

						<div class="form-group">
							<label class="col-md-5 control-label">Wage Inclusion
							<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
																data-placement="top" data-content="Inclusion of these wage header will be added to the gross wage for variable insurance calculation">
							</i>
							</label>									
							<div class="col-md-7">
								<select multiple="multiple" class="form-control selectAlll" data-search="true" id="formWageInclusion" name="Wage_Inclusion">
								<option value="selectAll">-- Select all--</option>
								<option value="clearAll">--Clear all--</option>
									<?php
									foreach ($listWageInclusion as $key => $row)
									{
										echo '<option value="'.$key.'">'.$row.'</option>';
									}
									?>
								</select>
							</div>
						</div>

						
						<div class="form-group">
							<label class="col-md-5 control-label">Description</label>
							<div class="col-md-7">
								<textarea name="description" id="editformVIDescription" rows="5" class="form-control vComments" placeholder="Write your Description..." ></textarea>
							</div>
                        </div>
					</div>
					
					<button type="reset" class="cancel" id="formVariableInsuranceReset" style="display: none;" ></button>
				</form>
				
				<?php } ?>
				
			</div>
			<div class="modal-footer text-center" id="formActionVariableInsurance">
				
				<?php if ($insuranceAccess['Add'] == 1 || $insuranceAccess['Update'] == 1) { ?>
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetVariableInsurance" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitVariableInsurance" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
				<?php } ?>
				
			</div>
		</div>
	</div>
</div>

<!--Filter Form-->
<div class="builder" id="filterPanelVariableInsurance">
	<div id="closeFilterVariableInsurance"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel" style="width: 100%;" id="filterResetVariableInsurance">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="filterApplyVariableInsurance">Apply</button>			
			
			<div class="form-group">
				<label>Employee Name</label>
				<input type="text" class="form-control" id="filterVIEmployeeName" placeholder="Employee Name">
			</div>
			
			<div class="form-group">
				<label>Insurance Type</label>
				<select class="form-control" id="filterVIInsuranceType" data-search="true" >
					<option value="">All</option>
					<?php
					foreach ($insuranceTypesPair as $key => $row)
					{
						echo '<option value="'.$key.'">'.$row.'</option>';
					}
					?>
				</select>
			</div>
			
			<div class="form-group">
				<label>Payment Frequency</label>
				<select class="form-control" id="filterVIPaymentFrequency" data-search="true" >
					<option value="">All</option>
					<option value="12" >Annually</option>
					<option value="6" >Half Yearly</option>
					<option value="1" >Monthly</option>
					<option value="3" >Quarterly</option>
				</select>
			</div>
		</div>
	</div>
</div>

<!-- Form Dirty Confirmation Modal -->
<?php if ($insuranceAccess['Add'] || $insuranceAccess['Update']) { ?>
<div class="modal fade" id="modalDirtyVariableInsurance" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditConfVariableInsurance"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfVariableInsurance">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseConfirmVariableInsurance">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } if($insuranceAccess['Delete']==1)  { ?>
<!-- Delete COnfirmation Modal -->
<div class="modal fade" id="modalDeleteVariableInsurance" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteConfVariableInsurance"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfVariableInsurance">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConfirmVariableInsurance">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } }

if ($insuranceGradeAccess['View'] && ((!empty($customFormNameC) && array_key_exists("Enable",$customFormNameC) && $customFormNameC['Enable'] == 1) || empty($customFormNameC))) {
	?>
<!--Insurance Grade Grid Panel-->
<div class="col-md-12 portlets add-panel-padding">
	<div class="panel" id="gridPanelInsuranceGrade">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong id="lblFormNameC"><?php echo ((!empty($customFormNameC) && !empty($customFormNameC['New_Form_Name'])) ? $customFormNameC['New_Form_Name'] : $this->formNameC);?></strong></h3>
		</div>
		<div class="panel-content">
			<!--Insurance Grade Grid Toolbar Icons-->
			<div class="m-b-10">
				<?php if ($insuranceAccess['Add']) { ?>
				<button type="button" class="btn btn-white btn-embossed toolbar-icons" data-toggle="modal" data-target="#modalFormInsuranceGrade" id="addInsuranceGrade" title="Add" >
					<i class="mdi-content-add"></i><span class="hidden-xs hidden-sm"> Add</span>
				</button>
				<?php  } ?>
				
				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="viewInsuranceGrade" title="View">
					<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
				</button>
				
				<?php if ($insuranceAccess['Update']) { ?>
				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="editInsuranceGrade" title="Edit">
					<i class="mdi-editor-mode-edit"></i><span class="hidden-xs hidden-sm"> Edit</span>
				</button>
				<?php } if ($insuranceAccess['Delete']) { ?>
				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="deleteInsuranceGrade" title="Delete">
					<i class="mdi-action-delete"></i><span class="hidden-xs hidden-sm"> Delete</span>
				</button>
				<?php } ?>
				
				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="History" id="historyInsuranceGrade">
					<i class="mdi-action-history"></i><span class="hidden-xs hidden-sm"> History</span>
				</button>
				
				<a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterInsuranceGrade">
					<i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm"> Filter</span>
				</a>
			</div>
			
			<!-- Insurance Grade Grid Table -->
			<table class="table dataTable table-striped table-dynamic table-hover" id="tableInsuranceGrade">
				<thead>
					<tr>
						<th></th>
						<th id="premiumContributionInsuranceType">Insurance Type</th>
						<th id="premiumContributionGrade">Grade</th>
						<th id="premiumContributionOrgShareAmount">Org Share Amount</th>
						<th id="premiumContributionOrgSharePercentage">Org Share (%)</th>
						<th id="premiumContributionEmpSharePercentage">Emp Share (%)</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
		</div>
	</div>
</div>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="insurance-grade-context-menu" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextInsuranceGrade"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($insuranceAccess['Update']) { ?>
		<li><a tabindex="-1" id="editContextInsuranceGrade"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<?php } if($insuranceAccess ['Delete']) { ?>
		<li><a tabindex="-1" id="deleteContextInsuranceGrade"><i class="mdi-action-delete"></i> Delete</a></li>
		<?php } ?>
	</ul>
</div>

<!--Add, Edit, View FOrm modal for insurance grade-->
<div class="modal fade" id="modalFormInsuranceGrade" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace" id="backInsuranceGrade"></i>
				</button>
				
				<?php if ($insuranceAccess['Update']) { ?>
				<button type="button" class="close form-icons" aria-hidden="true" id="editInViewInsuranceGrade">
					<i class="mdi-editor-mode-edit"></i>
				</button>
				<?php } ?>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<!--View Insurance Grade Form-->
				<form role="form" id="viewFormInsuranceGrade" >
					<div class="row">
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Insurance Type</label></div>
							<div class="col-md-7"><p id="viewIGInsuranceType"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Grade</label></div>
							<div class="col-md-7"><p id="viewIGGrade"></p></div>
						</div>
						<div class="form-group" >
							<div class="col-md-5"><label class="control-label">Organisation Share Amount</label></div>
							<div class="col-md-7"><p id="viewIGOrgShareAmount"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Organisation Share Percent</label></div>
							<div class="col-md-7"><p id="viewIGOrgSharePercent"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Employee Share Percent</label></div>
							<div class="col-md-7"><p id="viewIGEmpSharePercent"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Description</label></div>
							<div class="col-md-7"><p id="viewIGDescription"></p></div>
						</div>
					</div>
					
					<div class="row">
						<hr class="view-hr"/>
						
						<div class="form-group" style="font-size: large;margin-left: 13px;">
							<label class="control-label text-center">Additional Information</label>
						</div>
						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Added On</label></div>
							<div class="col-md-7"><p id="addedOnInsuranceGrade"></p></div>
						</div>
						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Added By</label></div>
							<div class="col-md-7"><p id="addedByInsuranceGrade"></p></div>
						</div>
						
						<div class="form-group updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated On</label></div>
							<div class="col-md-7"><p id="updatedOnInsuranceGrade"></p></div>
						</div>
						
						<div class="form-group updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated By</label></div>
							<div class="col-md-7"><p id="updatedByInsuranceGrade"></p></div>
						</div>
					</div>
				</form>
				
				<?php if ($insuranceAccess['Add'] == 1 || $insuranceAccess['Update'] == 1) { ?>
				
				<!--Add/Edit Insurance Grade Form-->
				<form role="form" class="form-horizontal form-validation" id="editFormInsuranceGrade" method="POST" action="">
					<input type="hidden" name="InsuranceGrade_Id" id="formInsuranceGradeId" />
					
					<div class="row">
						<div class="form-group">
							<label class="col-md-3 control-label">Insurance Type <span class="short_explanation">*</span></label>
							<div class="col-md-9">
								<select class="form-control vRequired" data-search="true" id="editformIGInsType" name="editformIGInsType" >
									
								</select>
							</div>
                        </div>
						
						<div class="form-group">
							<label class="col-md-3 control-label">Grade <span class="short_explanation">*</span></label>
							<div class="col-md-9">
								<select multiple="multiple" class="form-control vRequired selectAlll" data-search="true" id="editformIGGrade" name="editformIGGrade">
									<option value="selectAll">--Select all--</option>
									<option value="clearAll">--Clear all--</option>
									<?php
									foreach ($gradesPair as $key => $row)
									{
										echo '<option value="'.$key.'">'.$row.'</option>';
									}
									?>
								</select>
							</div>
                        </div>
						
						<div class="form-group">
							<label class="col-md-3 control-label">Coverage</label>
							<div class="col-md-9">
								<select class="form-control vRequired" data-search="true" id="editformIGCoverage" name="editformIGCoverage" >
									<option value="0" selected="selected">Fixed Share</option>
									<option value="1">Variable Share</option>
								</select>
							</div>
                        </div>
						
						<div class="form-group premiumVariableCoverageBasedHidden">
							<label class="col-md-3 control-label">Organisation Share Amount <span class="short_explanation">*</span></label>
							<div class="col-md-9">
								<input type="number" class="form-control convertDecimalPos1" step="0.01" max="99999999" id="editformIGOrgShareAmount" name="editformIGOrgShareAmount" placeholder="Organisation Share Amount">
							</div>
                        </div>
						
						<div class="form-group premiumFixedCoverageBasedHidden">
							<label class="col-md-3 control-label">Employee Share Percent <span class="short_explanation">*</span></label>
							<div class="col-md-9">
								<input type="number" class="form-control" id="editformIGEmpSharePercent" name="editformIGEmpSharePercent" placeholder="Employee Share Percent">
							</div>
                        </div>
						
						<div class="form-group premiumFixedCoverageBasedHidden">
							<label class="col-md-3 control-label">Organisation Share Percent <span class="short_explanation">*</span></label>
							<div class="col-md-9">
								<input type="number" class="form-control" id="editformIGOrgSharePercent" name="editformIGOrgSharePercent" placeholder="Organisation Share Percent">
							</div>
                        </div>
						
						<div class="form-group">
							<label class="col-md-3 control-label">Description</label>
							<div class="col-md-9">
								<textarea name="description" id="editformIGDescription" rows="5" class="form-control vComments" placeholder="Write your Description..." ></textarea>
							</div>
                        </div>
					</div>
					
					<button type="reset" class="cancel" id="formInsuranceGradeReset" style="display: none;" ></button>
				</form>
				
				<?php } ?>
				
			</div>
			<div class="modal-footer text-center" id="formActionInsuranceGrade">
				
				<?php if ($insuranceAccess['Add'] == 1 || $insuranceAccess['Update'] == 1) { ?>
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetInsuranceGrade" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitInsuranceGrade" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
				<?php } ?>
				
			</div>
		</div>
	</div>
</div>

<!--Filter Form-->
<div class="builder" id="filterPanelInsuranceGrade">
	<div id="closeFilterInsuranceGrade"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel" style="width: 100%;" id="filterResetInsuranceGrade">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="filterApplyInsuranceGrade">Apply</button>
			
			<div class="form-group">
				<label>Insurance Type</label>
				<select class="form-control" id="filterIGInsuranceTypeId" data-search="true" >
					<option value="">All</option>
					<?php
					foreach ($insuranceTypesPair as $key => $row)
					{
						echo '<option value="'.$key.'">'.$row.'</option>';
					}
					?>
				</select>
			</div>
			
			<div class="form-group">
				<label>Grade</label>
				<input type="text" class="form-control" id="filterIGGrade" name="filterIGGrade">
			</div>
			
			<div class="form-group">
				<label>Organisation Share Amount</label>
				<div class="input-group">
					<input type="number" class="input-md form-control" max="100" name="filterOrgShareAmountStart" id="filterOrgShareAmountStart" data-orientation="top" placeholder="Start"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="input-md form-control" max="100" name="filterOrgShareAmountEnd"  id="filterOrgShareAmountEnd" data-orientation="top" placeholder="End"/>
				</div>
			</div>
			
			<div class="form-group">
				<label>Organisation Share Percent</label>
				<div class="input-group">
					<input type="number" class="input-md form-control" max="100" name="filterOrgSharePercentStart" id="filterOrgSharePercentStart" data-orientation="top" placeholder="Start"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="input-md form-control" max="100" name="filterOrgSharePercentEnd"  id="filterOrgSharePercentEnd" data-orientation="top" placeholder="End"/>
				</div>
			</div>
			
			<div class="form-group">
				<label>Employee Share Percent</label>
				<div class="input-group">
					<input type="number" class="input-md form-control" max="100" name="filterEmpSharePercentStart" id="filterEmpSharePercentStart" data-orientation="top" placeholder="Start"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="input-md form-control" max="100" name="filterEmpSharePercentEnd"  id="filterEmpSharePercentEnd" data-orientation="top" placeholder="End"/>
				</div>
			</div>
			
		</div>
	</div>
</div>

<!-- Form Dirty Confirmation Modal -->
<?php if ($insuranceTypeAccess['Add'] || $insuranceTypeAccess['Update']) { ?>
<div class="modal fade" id="modalDirtyInsuranceGrade" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditConfInsuranceGrade"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfInsuranceGrade">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseConfirmInsuranceGrade">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } if($insuranceTypeAccess['Delete']==1)  { ?>
<!-- Delete COnfirmation Modal -->
<div class="modal fade" id="modalDeleteInsuranceGrade" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteConfInsuranceGrade"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfInsuranceGrade">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConfirmInsuranceGrade">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } }
if ($insuranceTypeAccess['View'] && ((!empty($customFormNameB) && array_key_exists("Enable",$customFormNameB) && $customFormNameB['Enable'] == 1) || empty($customFormNameB))) {
 ?>

<!--Insurance Type Grid Panel-->
<div class="col-md-12 portlets add-panel-padding">
	<div class="panel" id="gridPanelInsuranceType">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong id="lblFormNameB"><?php echo ((!empty($customFormNameB) && !empty($customFormNameB['New_Form_Name'])) ? $customFormNameB['New_Form_Name'] : $this->formNameB);?></strong></h3>	
		</div>
		<div class="panel-content">
			<!--Insurance Type Grid Toolbar Icons-->
			<div class="m-b-10">
				<?php if ($insuranceTypeAccess['Add']) { ?>
				<button type="button" class="btn btn-white btn-embossed toolbar-icons" data-toggle="modal" data-target="#modalFormInsuranceType" id="addInsuranceType" title="Add" >
					<i class="mdi-content-add"></i><span class="hidden-xs hidden-sm"> Add</span>
				</button>
				<?php  } ?>
				
				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="viewInsuranceType" title="View">
					<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
				</button>
				
				<?php if ($insuranceTypeAccess['Update']) { ?>
				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="editInsuranceType" title="Edit">
					<i class="mdi-editor-mode-edit"></i><span class="hidden-xs hidden-sm"> Edit</span>
				</button>
				<?php } if ($insuranceTypeAccess['Delete']) { ?>
				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="deleteInsuranceType" title="Delete">
					<i class="mdi-action-delete"></i><span class="hidden-xs hidden-sm"> Delete</span>
				</button>
				<?php } ?>
				
				<a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterInsuranceType">
					<i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm"> Filter</span>
				</a>
				
				<!-- Insurance Type Grid Table -->
				<table class="table dataTable table-striped table-dynamic table-hover" id="tableInsuranceType">
					<thead>
						<tr>
							<th id="insuranceTypeInsName">Insurance Name</th>
							<th id="insuranceTypeEmployeeStateInsurance">Employee State Insurance</th>
							<th id="insuranceTypeStatus">Status</th>
						</tr>
					</thead>
					<tbody>
					
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="context-menu2" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextInsuranceType"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($insuranceTypeAccess['Update']) { ?>
		<li><a tabindex="-1" id="editContextInsuranceType"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<?php } if($insuranceTypeAccess ['Delete']) { ?>
		<li><a tabindex="-1" id="deleteContextInsuranceType"><i class="mdi-action-delete"></i> Delete</a></li>
		<?php } ?>
	</ul>
</div>

<!--Add, Edit, View FOrm modal for insurance type-->
<div class="modal fade" id="modalFormInsuranceType" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace" id="backInsuranceType"></i>
				</button>
				
				<?php if ($insuranceAccess['Update']) { ?>
				<button type="button" class="close form-icons" aria-hidden="true" id="editInViewInsuranceType">
					<i class="mdi-editor-mode-edit"></i>
				</button>
				<?php } ?>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<!--View Variable Insurance Form-->
				<form role="form" id="viewFormInsuranceType" >
					<div class="row">
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Insurance Name</label></div>
							<div class="col-md-7"><p id="viewITInsuranceName"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Employee State Insurance</label></div>
							<div class="col-md-7"><p id="viewEmployeeStateInsurance"></p></div>
						</div>
						<div class="form-group vICoverageEmployee">
							<div class="col-md-5"><label class="control-label">Status</label></div>
							<div class="col-md-7"><p id="viewITStatus"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Description</label></div>
							<div class="col-md-7"><p id="viewITDescription"></p></div>
						</div>
					</div>
					
					<div class="row">
						<hr class="view-hr"/>
						
						<div class="form-group" style="font-size: large;margin-left: 13px;">
							<label class="control-label text-center">Additional Information</label>
						</div>
						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Added On</label></div>
							<div class="col-md-7"><p id="addedOnInsuranceType"></p></div>
						</div>
						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Added By</label></div>
							<div class="col-md-7"><p id="addedByInsuranceType"></p></div>
						</div>
						
						<div class="form-group updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated On</label></div>
							<div class="col-md-7"><p id="updatedOnInsuranceType"></p></div>
						</div>
						
						<div class="form-group updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated By</label></div>
							<div class="col-md-7"><p id="updatedByInsuranceType"></p></div>
						</div>
					</div>
				</form>
				
				<?php if ($insuranceAccess['Add'] == 1 || $insuranceAccess['Update'] == 1) { ?>
				
				<!--Add/Edit Insurance type Form-->
				<form role="form" class="form-horizontal form-validation" id="editFormInsuranceType" method="POST" action="">
					<input type="hidden" name="Insurance_Type_Id" id="formInsuranceTypeId" />
					
					<div class="row">
						<div class="form-group">
							<label class="col-md-3 control-label">Insurance Name <span class="short_explanation">*</span></label>
							<div class="col-md-9">
								<input type="text" class="form-control vName alphaNumSpCDotHySlash" id="editformITInsuranceName" name="editformITInsuranceName" placeholder="Insurance Name">
							</div>
                        </div>

						<div class="form-group">
							<label class="col-md-3 control-label">Employee State Insurance</label>
							
							<div class="col-md-9 togglebutton togglebutton-material-blue" style="margin-top: 15px;">
								<label>
										<input type="checkbox" class="md-checkbox" id="editEmployeeStateInsurance">
								</label>
							</div>
						 </div>
						
						<div class="form-group">
							<label class="col-md-3 control-label">Status</label>
							<div class="col-md-9">
								<select class="form-control" id="editformITStatus" name="editformITStatus" >
									
								</select>
							</div>
                        </div>
						
						<div class="form-group">
							<label class="col-md-3 control-label">Description</label>
							<div class="col-md-9">
								<textarea name="description" id="editformITDescription" rows="5" class="form-control vComments" placeholder="Write your Description..." ></textarea>
							</div>
                        </div>
					</div>
					
					<button type="reset" class="cancel" id="formInsuranceTypeReset" style="display: none;" ></button>
				</form>
				
				<?php } ?>
				
			</div>
			<div class="modal-footer text-center" id="formActionInsuranceType">
				
				<?php if ($insuranceAccess['Add'] == 1 || $insuranceAccess['Update'] == 1) { ?>
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetInsuranceType" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitInsuranceType" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
				<?php } ?>
				
			</div>
		</div>
	</div>
</div>

<!--Filter Form-->
<div class="builder" id="filterPanelInsuranceType">
	<div id="closeFilterInsuranceType"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel" style="width: 100%;" id="filterResetInsuranceType">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="filterApplyInsuranceType">Apply</button>
			
			<div class="form-group">
				<label>Insurance Name</label>
				<input type="text" class="form-control" id="filterITInsuranceName" placeholder="Insurance Name">
			</div>
			<div class="form-group">
				<label>Employee State Insurance</label>
				<select class="form-control" data-search="true" id="filterEmployeeStateInsurance" >
					<option value="">All</option>
					<option value="1">Yes</option>
					<option value="2">No</option>
				</select>
			</div>
			<div class="form-group">
				<label>Status</label>
				<select class="form-control" data-search="true" id="filterITInsuranceStatus">
					<option value="">All</option>
					<option value="Active">Active</option>
					<option value="Inactive">Inactive</option>
				</select>
			</div>
			
		</div>
	</div>
</div>

<!-- Form Dirty Confirmation Modal -->
<?php if ($insuranceTypeAccess['Add'] || $insuranceTypeAccess['Update']) { ?>
<div class="modal fade" id="modalDirtyInsuranceType" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditConfInsuranceType"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfInsuranceType">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseConfirmInsuranceType">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } if($insuranceTypeAccess['Delete']==1)  { ?>
<!-- Delete COnfirmation Modal -->
<div class="modal fade" id="modalDeleteInsuranceType" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteConfInsuranceType"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfInsuranceType">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConfirmInsuranceType">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } }
if ($insurancePaymentAccess['View'] && ((!empty($customFormNameD) && array_key_exists("Enable",$customFormNameD) && $customFormNameD['Enable'] == 1) || empty($customFormNameD))) {
 ?>

<!--Payment Tracker Grid Panel-->
<div class="col-md-12 portlets add-panel-padding">
	<div class="panel" id="gridPanelPaymentTracker">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong id="lblFormNameD"><?php echo ((!empty($customFormNameD) && !empty($customFormNameD['New_Form_Name'])) ? $customFormNameD['New_Form_Name'] : $this->formNameD);?></strong></h3>
		</div>
		<div class="panel-content">
			<!--Payment Tracker Grid Toolbar Icons-->
			<div class="m-b-10">
				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="viewPaymentTracker" title="View">
					<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
				</button>
				
				<?php if ($insurancePaymentAccess['Update']) { ?>
				<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="editPaymentTracker" title="Update">
					<i class="mdi-editor-mode-edit"></i><span class="hidden-xs hidden-sm"> Update</span>
				</button>
				<?php } ?>
				
				<a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterPaymentTracker">
					<i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm"> Filter</span>
				</a>
			</div>
			
			<!-- Payment Tracker Grid Table -->
			<table class="table dataTable table-striped table-dynamic table-hover" id="tablePaymentTracker">
				<thead>
					<tr>
						<th></th>
						<th id="insPaymentTrackerSalaryMonth">Salary Month</th>
						<th id="insPaymentTrackerInsuranceName">Insurance Name</th>
						<th id="insPaymentTrackerEmpShareAmount">Emp Share Amount</th>
						<th id="insPaymentTrackerOrgShareAmount">Org Share Amount</th>
						<th id="insPaymentTrackerTotalAmount">Total Amount</th>
						<th id="insPaymentTrackerAmountPaid">Amount Paid</th>
						<th id="insPaymentTrackerStatus">Status</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
		</div>
	</div>
</div>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="context-menu4" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextPaymentTracker"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($insuranceAccess['Update']) { ?>
		<li><a tabindex="-1" id="editContextPaymentTracker"><i class="mdi-editor-mode-edit"></i> Update</a></li>
		<?php } ?>
	</ul>
</div>

<!--Modal fot payment tracker view & edit form-->
<div class="modal fade" id="modalFormPaymentTracker" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" id="insPTCloseModal" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace" id="backInsurancePaymentTracker"></i>
				</button>
				
				<?php if ($insuranceAccess['Update']) { ?>
				<button type="button" class="close form-icons" aria-hidden="true" id="editInViewPaymentTracker">
					<i class="mdi-editor-mode-edit"></i>
				</button>
				<?php } ?>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<!--View PaymentTracker Form-->
				<form role="form" id="viewFormPaymentTracker" >
					<div class="row">
						<div class="form-group">
						<div class="col-md-5"><label class="control-label">Payslip Month</label></div>
						<div class="col-md-7"><p id="viewPTPayslipMonth"></p></div>
					</div>
					<div class="form-group">
						<div class="col-md-5"><label class="control-label">Employee Share Amount</label></div>
						<div class="col-md-7"><p id="viewPTEmpShareAmount"></p></div>
					</div>
					<div class="form-group">
						<div class="col-md-5"><label class="control-label">Organization Share Amount</label></div>
						<div class="col-md-7"><p id="viewPTOrgShareAmount"></p></div>
					</div>
					<div class="form-group">
						<div class="col-md-5"><label class="control-label">Total Amount</label></div>
						<div class="col-md-7"><p id="viewPTTotalAmount"></p></div>
					</div>
					<div class="form-group">
						<div class="col-md-5"><label class="control-label"> Status</label></div>
						<div class="col-md-7"><p id="viewPTStatus"></p></div>
					</div>
					<!-- Payment Tracker sub grid Grid Table -->
					<table class="table dataTable table-striped table-dynamic table-hover" id="tablePaymentTrackerSubGridView">
						<thead>
							<tr>
								<th></th>
								<th id="insPaymentTrackerSubViewModeofPayment">Mode of Payment</th>
								<th id="insPaymentTrackerSubViewPaymentDate">Payment Date</th>
								<th id="insPaymentTrackerSubViewAmountPaid">Amount Paid</th>
								<th id="insPaymentTrackerSubViewDescription">Description</th>
							</tr>
						</thead>
						<tbody>
						
						</tbody>
					</table>
					</div>
					
				</form>
				
				<?php if ($insuranceAccess['Update'] == 1) { ?>
				
				<!--Add/Edit Payment Tracker Form-->
				<form role="form" class="form-horizontal form-validation" id="editFormPaymentTracker" method="POST" action="">
					<input type="hidden" name="Payment_ID" id="formPaymentTrackerPaymentId" />
					<input type="hidden" name="Payment_Tracker_ID" id="formPaymentTrackerId" />
					<input type="hidden" name="Payslip_Month" id="formPTPayslipMonth" />
				
					<div class="panel-group panel-accordion" id="editAccordion">
						<div class="panel panel-default">
							<div class="panel-heading">                                
								 <h4>
									<a data-toggle="collapse" data-parent="#editAccordion" href="#editPTDetails" id="paymentSummaryPanel">
										Payment Summary
									</a>
								</h4>
							</div>
							<div id="editPTDetails" class="panel-collapse collapse in">
								<div class="panel-body">
									<div class="form-group">
										<label class="col-md-4 control-label">Payslip Month</label>
										<div class="col-md-8"><p id="formPaymentTrackerPayslipMonth"></p></div>
									</div>
									<div class="form-group">
										<label class="col-md-4 control-label">Insurance Name</label>
										<div class="col-md-8"><p id="formPaymentTrackerInsuranceName"></p></div>
									</div>
									<div class="form-group">
										<label class="col-md-4 control-label">Employee Share Amount</label>
										<div class="col-md-8"><p id="formPaymentTrackerEmpShareAmount"></p></div>
									</div>
									<div class="form-group">
										<label class="col-md-4 control-label">Orginsation Share Amount</label>
										<div class="col-md-8"><p id="formPaymentTrackerOrgShareAmount"></p></div>
									</div>
									<div class="form-group">
										<label class="col-md-4 control-label">Total Amount</label>
										<div class="col-md-8"><p id="formPaymentTrackerTotalAmount"></p></div>
									</div>
									<div class="form-group">
										<label class="col-md-4 control-label">Status</label>
										<div class="col-md-8"><p id="formPaymentTrackerStatus"></p></div>
									</div>
									<div class="form-group">
										<label class="col-md-4 control-label">Outstanding Amount</label>
										<div class="col-md-8"><p id="formPaymentTrackerOutstandingAmount"></p></div>
									</div>
								</div>
							</div>
						</div>
						<div class="panel panel-default">
							<div class="panel-heading">
								<h4>
									<a class="collapsed" data-toggle="collapse" id="onAddCollapse" data-parent="#editAccordion" href="#editPaymentTrackerDetails">
										Payment Details
									</a>
								</h4>
							</div>
							<div id="editPaymentTrackerDetails" class="panel-collapse collapse">
								<div class="panel-body">						
                                    
									<div class="form-group">
										<label class="col-md-4 control-label">Mode of Payment <span class="short_explanation">*</span></label>
										<div class="col-md-8">
											<select class="form-control vRequired" data-search="true" id="formPaymentTrackerModeOfPayment" name="PaymentTrackerModeOfPayment" >
												<?php
												foreach ($paymentModePair as $key => $row)
												{
													echo '<option value="'.$key.'">'.$row.'</option>';
												}
												?>
											</select>
										</div>
									</div>
									
									<div class="form-group">
										<label class="col-md-4 control-label">Payment Date <span class="short_explanation">*</span></label>
										<div class="col-md-8">
											<input type="text" id="formInsPaymentTrackerPaymentDate" class="date-picker form-control datePickerRead" required=true name="Payment_Date" placeholder="Payment Date">
										</div>
									</div>
									
									<div class="form-group paymentModeBasedHidden">
										<label class="col-md-4 control-label">Document No <span class="short_explanation">*</span></label>
										<div class="col-md-8">
											<input type="text" class="form-control numSpComma" maxlength="15" id="formPaymentTrackerDocumentNo" name="Document_No" placeholder="Document No">
										</div>
									</div>
									
									<div class="form-group paymentModeBasedHidden">
										<label class="col-md-4 control-label">Bank Name <span class="short_explanation">*</span></label>
										<div class="col-md-8">
											<input type="text" class="form-control" id="formPaymentTrackerBankName" name="Bank_Name" placeholder="Bank Name">
										</div>
									</div>
									
									<div class="form-group paymentModeBasedHidden">
										<label class="col-md-4 control-label">Branch Name <span class="short_explanation">*</span></label>
										<div class="col-md-8">
											<input type="text" class="form-control" id="formPaymentTrackerBranchName" name="Branch_Name" placeholder="Branch Name">
										</div>
									</div>
									
									<div class="form-group">
										<label class="col-md-4 control-label">Amount Paid <span class="short_explanation">*</span></label>
										<div class="col-md-8">
											<input type="number" class="form-control convertDecimalPos1" min="1" max="*************" step="0.01" required id="formPaymentTrackerAmountPaid" name="Amount Paid" placeholder="Amount Paid">
										</div>
									</div>
									
									<div class="form-group">
										<label class="col-md-4 control-label">Description</label>
										<div class="col-md-8">
											<textarea name="description" id="formPaymentTrackerDescription" rows="5" class="form-control vDescription" placeholder="Write your Description..." ></textarea>
										</div>
									</div>
									
									<div class="text-center"> 			
										<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetPaymentTracker" style="bottom: 5px;">
											<i class="mdi-action-restore"></i> Reset
										</button>
										<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitPaymentTracker" style="bottom: 5px;">
											<i class="mdi-content-send"></i> Add
										</button>
									</div>
									
								</div>
							</div>
						</div>
					</div>
					
					<button type="reset" class="cancel" id="formPaymentTrackerReset" style="display: none;" ></button>
				</form>
			<?php } ?>
			
				<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="addPaymentTracker" title="Add">
					<i class="mdi-content-add"></i><span> Add</span>
				</button>
				
				<div id="paymentTrackerGrid" >
					<table class="table dataTable table-striped table-dynamic table-hover" id="tablePaymentTrackerSubGrid">
						<thead>
							<tr>
								<th></th>
								<th></th>
								<th id="insPaymentTrackerSubModeofPayment">Mode of Payment</th>
								<th id="insPaymentTrackerSubPaymentDate">Payment Date</th>
								<th id="insPaymentTrackerSubAmountPaid">Amount Paid</th>
							</tr>
						</thead>
						<tbody>
						
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>

<!--Filter Form-->
<div class="builder" id="filterPanelPaymentTracker">
	<div id="closeFilterPaymentTracker"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel" style="width: 100%;" id="filterResetPaymentTracker">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="filterApplyPaymentTracker">Apply</button>
			
			<div class="form-group">
				<label>Salary Month</label>
				<!--<input type="month" class="form-control" name="filterPTSalaryMonth" id="filterPTSalaryMonth">-->
				<input type="text" class="b-datepicker vmonthMax vMonthClosure form-control vRequired closeMonthPicker" name="filterPTSalaryMonth" placeholder="Salary Month"
                data-date-format="MM,yyyy" data-view="1" data-date-min-view-mode=1 data-date-autoclose="true" id="filterPTSalaryMonth" data-orientation="top">
			</div>
			
			<div class="form-group">
				<label>Insurance Type</label>
				<select class="form-control" id="filterPTInsuranceTypeId" data-search="true" >
					<option value="">All</option>
					<?php
					foreach ($insuranceTypesPair as $key => $row)
					{
						echo '<option value="'.$key.'">'.$row.'</option>';
					}
					?>
				</select>
			</div>
			
			<div class="form-group">
				<label>Employee Share Amount</label>
				<div class="input-group">
					<input type="number" class="input-md form-control" name="filterPTEmpShareAmountStart" id="filterPTEmpShareAmountStart" data-orientation="top" placeholder="Start"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="input-md form-control" name="filterPTEmpShareAmountEnd"  id="filterPTEmpShareAmountEnd" data-orientation="top" placeholder="End"/>
				</div>
			</div>
			
			<div class="form-group">
				<label>Organization Share Amount</label>
				<div class="input-group">
					<input type="number" class="input-md form-control" name="filterPTOrgShareAmountStart" id="filterPTOrgShareAmountStart" data-orientation="top" placeholder="Start"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="input-md form-control" name="filterPTOrgShareAmountEnd"  id="filterPTOrgShareAmountEnd" data-orientation="top" placeholder="End"/>
				</div>
			</div>
			
			<div class="form-group">
				<label>Total Amount</label>
				<div class="input-group">
					<input type="number" class="input-md form-control" name="filterPTAmountPaidStart" id="filterPTAmountPaidStart" data-orientation="top" placeholder="Start"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="input-md form-control" name="filterPTAmountPaidEnd"  id="filterPTAmountPaidEnd" data-orientation="top" placeholder="End"/>
				</div>
			</div>
			
			<div class="form-group">
				<label>Amount Paid</label>
				<div class="input-group">
					<input type="number" class="input-md form-control" name="filterPTTotalAmountStart" id="filterPTTotalAmountStart" data-orientation="top" placeholder="Start"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="input-md form-control" name="filterPTTotalAmountEnd"  id="filterPTTotalAmountEnd" data-orientation="top" placeholder="End"/>
				</div>
			</div>
			
			
			<div class="form-group">
				<label>Status</label>
				<select class="form-control" data-search="true" id="filterPTStatus" >
					<option value="">All</option>
					<option value="Paid">Paid</option>
					<option value="Partially Paid">Partially Paid</option>
					<option value="Unpaid">Unpaid</option>
				</select>
			</div>
		</div>
	</div>
</div>

<!-- Form Dirty Confirmation Modal -->
<?php if ( $insurancePaymentAccess['Update']) { ?>
<div class="modal fade" id="modalDirtyPaymentTracker" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditConfInsurancePaymentTracker"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfInsurancePaymentTracker">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseConfirmPaymentTracker">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } } if($insuranceAccess['View'] != 1 && $insuranceTypeAccess['View'] != 1 && $insurancePaymentAccess['View'] != 1 && $insuranceGradeAccess!=1) { ?>
<div class="col-md-12 portlets add-panel-padding">
	<div class="txt_center">Sorry, Access Denied...</div>
</div>
<?php } ?>