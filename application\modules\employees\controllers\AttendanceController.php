<?php
//==========================================================================================
//==========================================================================================
/* Program : AttendanceController.php											          *
 * Property of Caprice Technologies Pvt Ltd,                                              *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                       *
 * Coimbatore, Tamilnadu, India.														  *
 * All Rights Reserved.            														  *
 * Use of this material without the express consent of Caprice Technologies               *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law.  *
 *                                                                                    	  *
 * Description    : Attendance is used for keeping track of every employee office in and  *
 * out time.Every employee can punch-in and punch-out the date and time they are entering *
 * and leaving the offfice.																  *
 *                                                                                   	  *
 *                                                                                    	  *
 * Revisions :                                                                    	      *
 *  Version    Date           Author               Description                            *
 *  0.1        30-May-2013    Shobana              Initial Version         	              *
 *  0.2        16-Sep-2013    Narmadha             Attendance Import                      *
 *  0.3        23-Jan-2014    Mahesh               Restrict adding attendance when leave  *
 *                                                 applied on that date                   *
 *  0.4        14-May-2014    Mahesh               Added action :                         *
 *       										   1.statusMultiApprovalAction	          *
 *                            Sandhosh             Modified Functions :                   *
 *								     	           schemaexistAction,mapheaderAction,	  *
 * 												   attendanceimportAction			      *
 * 																						  *
 *  0.5        25-Jul-2014    Mahesh			   1.Modified mail coding in add, update, *
 *												   status approval action and used the    *
 *                                                 common mail function.                  *
 *												   2.Added copyAttendanceAction			  *
 *																						  *
 *  0.6		   18-sep-2014    Mahesh			   Modified Actions						  *
 *												   1.addAttendanceAction	 			  *
 *												   2.updateAttendanceAction 			  *
 *												   Added Action							  *	
 *												   1.updateimportedDataAction			  *
 *																						  *
 *  1.0        02-Feb-2015    Dhanabal             Changed in file for mobile app         *
 *                                                                                        *
 *  1.5        20-Feb-2016    Deepak             Changed in file for Bootstrap            *
 *															                              */	
//==========================================================================================
//==========================================================================================

include APPLICATION_PATH."/validations/Validations.php";

class Employees_AttendanceController extends Zend_Controller_Action
{
	protected $_validation             = null;
	protected $_dbCommonFunction       = null;
	protected $_dbAttendance           = null;
	protected $_dbPersonal             = null;
	protected $_basePath               = null;
	protected $_dbJobDetail            = null;
	protected $_dbComment              = null;
	protected $_dbAccessRights         = null;
    protected $_dbLocation 			   = null;
    protected $_dbAnnouncements        = null;
	protected $_attendanceAccessRights = null;
	protected $_attendanceBoxRights    = null;
	protected $_importAccessRights     = null;
	protected $_attendanceSettingsAccessRights     = null;
	protected $_deviceManagementAccessRights     = null;
	protected $_attendanceFinalizationAccessRights     = null; // Attendance Finalization access variable
	protected $_attendanceRegularizationAccessRights = null;
	protected $_logEmpId               = null;
	protected $_ehrTables              = null;
	protected $_orgDateFormat = null;
	protected $_formNameA              = 'Attendance';
	protected $_formNameB              = 'Attendance Import';
	protected $_formNameC              = 'Attendance Settings';
	protected $_formNameD              = 'Attendance Box';
	protected $_formNameE			   = 'Device Management';
	protected $_formNameF			   = 'Dashboard Attendance';
	protected $_formNameG 			   = 'Attendance Finalization';
	protected $_dbAlerts 			   = null;
    protected $_hrappMobile            = null;
	protected $_dbEmployee             = null;
	protected $_attendanceSettings	   = null;
	protected $_dbDept				   = null;
	protected $_dbManager			   = null;
	protected $_myTeamAccess 			    = null;
    protected $_selfServiceAccess 		    = null;
    protected $_myTeamAttendanceFormId 	    = 304;
    protected $_selfServiceAttendanceFormId = 305;
	protected $_attendanceFormId = 29;

	public function init()
	{
        $this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
        if ($this->_hrappMobile->checkAuth())
        {
			$this->_validation 	      = new Validations();
			$this->_dbCommonFunction  = new Application_Model_DbTable_CommonFunction();
			$this->_basePath          = new Zend_View_Helper_BaseUrl();
			$this->_dbAttendance      = new Employees_Model_DbTable_Attendance();
			$this->_dbPersonal        = new Employees_Model_DbTable_Personal();
			$this->_dbJobDetail       = new Employees_Model_DbTable_JobDetail();
			$this->_dbComment         = new Payroll_Model_DbTable_PayrollComment();
			$this->_dbAccessRights    = new Default_Model_DbTable_AccessRights();
			$this->_ehrTables         = new Application_Model_DbTable_Ehr();
			$this->_dbOrgSettings     = new Organization_Model_DbTable_OrgSettings();
			$this->_dbAlerts          = new Default_Model_DbTable_Alerts();
			$this->_dbPayslip 		  = new Payroll_Model_DbTable_Payslip();
            $this->_dbLocation        = new Organization_Model_DbTable_Location();
			$this->_dbDept      	  = new Organization_Model_DbTable_Department();
			$this->_dbManager   	  = new Default_Model_DbTable_Manager();
            $this->_dbAnnouncements	  = new Organization_Model_DbTable_Announcements();
			$this->_orgDateFormat     = $this->_ehrTables->orgDateformat();
			$userSession              = $this->_dbCommonFunction->getUserDetails ();
			$this->_attendanceSettings= $this->_dbAttendance->getAttendanceSettings('Attendance Shortage',NULL);
			$this->_logEmpId          = $userSession['logUserId'];		
			$this->_dbEmployee        = new Employees_Model_DbTable_Employee();	
			// $this->_attendanceAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameA);

			$this->_attendanceAccessRights      = $this->_dbAccessRights->employeeAccessRightsBasedOnFormId($this->_logEmpId,$this->_attendanceFormId);

			$this->_importAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameB);
			$this->_attendanceSettingsAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameC);
			$this->_attendanceBoxRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameD);
			$this->_deviceManagementAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameE);
			$this->_dashboardAttendanceAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameF);
			$this->_attendanceFinalizationAccessRights    = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameG);
			$this->_attendanceRegularizationAccessRights  = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, 'Attendance Regularization');

			$this->_myTeamAccessRights      = $this->_dbAccessRights->employeeAccessRightsBasedOnFormId($this->_logEmpId,$this->_myTeamAttendanceFormId);
            $this->_selfServiceAccessRights = $this->_dbAccessRights->employeeAccessRightsBasedOnFormId($this->_logEmpId,$this->_selfServiceAttendanceFormId);
            $this->_myTeamAccess            = $this->_myTeamAccessRights['Employee'];
            $this->_selfServiceAccess       = $this->_selfServiceAccessRights['Employee'];
			$this->view->newImg             = $this->_basePath->baseUrl('images/new.png');
		}
		else
		{
			//if the request comes from vue app then on session expire we should not redirect to auth
			// instead we need to send session expired message in error response
			//in header if the requestResource is HRAPPUI consider it comes from vue app
			//as of now, for getEmployeeDetailsAction and updateAttendanceActions session handled 
			//inside actions because not able to return response inside init function

			$formData = array();
			$requestData =$this->getRequest()->getRawBody();
			if ($requestData)
			{
				$formData = Zend_Json::decode($requestData);
			}

			//if its not from vue app on session expire redirect to auth
            if(empty($formData)|| (!empty($formData) && $formData['requestResource'] !== 'HRAPPUI')){
				if (Zend_Session::namespaceIsset('lastRequest'))
				Zend_Session:: namespaceUnset('lastRequest');
			
				$session = new Zend_Session_Namespace('lastRequest');
				$session->lastRequestUri = 'v3/employee-self-service/attendance';
				$this->_redirect('auth');
			}
			
		}
	}

	public function indexAction()
	{
        $checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

        if ($checkSessionAuth)
		{
			$this->_helper->layout()->disableLayout()->setLayout('admin_layout');
			
			$this->view->formNameA = $this->_formNameA;
			$this->view->formNameB = $this->_formNameB;
			$this->view->formNameC = $this->_formNameC;
			$this->view->formNameD = $this->_formNameD;
			$this->view->formNameE = $this->_formNameE;
			$this->view->formNameG = $this->_formNameG; //send the attendance Finalization form name to view
	
	
			$this->view->logEmpId = $this->_logEmpId;		
			$this->view->customFormNameA = $this->_ehrTables->getCustomForms($this->_formNameA);
			$this->view->customFormNameB = $this->_ehrTables->getCustomForms($this->_formNameB);
			$this->view->customFormNameC = $this->_ehrTables->getCustomForms($this->_formNameC);
			$this->view->customFormNameD = $this->_ehrTables->getCustomForms($this->_formNameD);
			$this->view->customFormNameE = $this->_ehrTables->getCustomForms($this->_formNameE);
			$this->view->customFormNameG = $this->_ehrTables->getCustomForms($this->_formNameG);

			$this->view->mapAPIKey = Zend_Registry::get('googleMapAPIKey');
			
			$this->view->employeeName = $this->_dbPersonal->employeeDetail ('', '', 'Emp_First_Name', 'ASC', '', '','', '', $this->_logEmpId,
																	 '', '', '', '', 'Attendance', '', 1, 1, '');
			
			$this->view->workPlaceList    = $this->_dbAttendance->listWorkPlace();
			$this->view->formSourceList   = $this->_dbAttendance->listAttendanceFormSource();
			$this->view->importDataSource = $this->_dbAttendance->getAttendanceImportDataSource();
			$this->view->attendanceImportErrorCode = $this->_dbAttendance->getAttendanceImportErrorCode();
			$this->view->empLocation     = $this->_dbLocation->getLocationPair();
			$this->view->deptHierarchy   = $this->_dbDept->getDepartmentTypes();
			$this->view->managerNames = $this->_dbManager->managerName('', '', 'Emp_First_Name', 'ASC', '', '', '', '', '', 'Employees', 0);
	
			$this->view->attendanceAccess =  array( 'Is_Manager' => $this->_attendanceAccessRights['Employee']['Is_Manager'],
												   'View'        => $this->_attendanceAccessRights['Employee']['View'],
												   'AEdit'       => $this->_dbAttendance->editOption($this->_logEmpId),
												   'Update'      => $this->_attendanceAccessRights['Employee']['Update'],
												   'Delete'      => $this->_attendanceAccessRights['Employee']['Delete'],
												   'Op_Choice'   => $this->_attendanceAccessRights['Employee']['Optional_Choice'],
												   'Add'         => $this->_attendanceAccessRights['Employee']['Add'],
												   'Admin'       => $this->_attendanceAccessRights['Admin'],
												   'Employee_Name' => $this->_dbPersonal->employeeId($this->_logEmpId));
	
			$this->view->dashboardAttendanceAccess = array('Optional_Choice'       => $this->_dashboardAttendanceAccessRights['Employee']['Optional_Choice']);									   
			
			$this->view->importAccess = array( 'View'     => $this->_importAccessRights['Employee']['View'],
											  'Op_Choice' => $this->_importAccessRights['Employee']['Optional_Choice'],
											  'Is_Manager' => $this->_importAccessRights['Employee']['Is_Manager'],
											  'Admin'      => $this->_importAccessRights['Admin'],
											  'Delete'    => $this->_importAccessRights['Employee']['Delete']);
			
			$this->view->attendanceSettingsAccess =  array('Is_Manager' => $this->_attendanceSettingsAccessRights['Employee']['Is_Manager'],
														   'View'       => $this->_attendanceSettingsAccessRights['Employee']['View'],
														   'Update'     => $this->_attendanceSettingsAccessRights['Employee']['Update'],
														   'Delete'     => $this->_attendanceSettingsAccessRights['Employee']['Delete'],
														   'Add'        => $this->_attendanceSettingsAccessRights['Employee']['Add'],
														   'Admin'      => $this->_attendanceSettingsAccessRights['Admin']);
	
			
			$this->view->deviceManagementAccess =  array('Is_Manager' => $this->_deviceManagementAccessRights['Employee']['Is_Manager'],
														   'View'       => $this->_deviceManagementAccessRights['Employee']['View'],
														   'Update'     => $this->_deviceManagementAccessRights['Employee']['Update'],
														   'Delete'     => $this->_deviceManagementAccessRights['Employee']['Delete'],
														   'Add'        => $this->_deviceManagementAccessRights['Employee']['Add'],
														   'Admin'      => $this->_deviceManagementAccessRights['Admin']);
			
			$this->view->attendanceBoxAccess = array('Update'     => $this->_attendanceBoxRights['Employee']['Update']);

			// return the Attendance Finalization rights to view
			$this->view->attendanceFinalizationAccess = array( 'View'     => $this->_attendanceFinalizationAccessRights['Employee']['View'],
												'Op_Choice' => $this->_attendanceFinalizationAccessRights['Employee']['Optional_Choice']);
												
			$this->view->customGroup	= $this->_dbAnnouncements->listCustomGroups($this->_formNameC);
			
			$isAdmin = 1;
			$logEmpName = '';
			
			if(empty($this->_attendanceAccessRights['Admin']) && $this->_attendanceAccessRights['Employee']['View'] == 1
					&& $this->_attendanceAccessRights['Employee']['Is_Manager'] == 0)
			{
				$isAdmin = 0;
				$logEmployeeDetails = $this->_dbPersonal->employeeName($this->_logEmpId);
				$logEmpName = $logEmployeeDetails['Employee_Name'];
			}
			
			$this->view->isAdmin               = $isAdmin;
			$this->view->logEmployeeId         = $this->_logEmpId;
			$this->view->logEmployeeName       = $logEmpName;
			$this->view->importFormatTblHeader = $this->_dbAttendance->tableHeader();
			$this->view->schemaTitle           = $this->_dbAttendance->schemaPairs();
			$this->view->employeeDetails       = $this->_dbCommonFunction->listEmployeesDetails ('Attendance', '', $this->_logEmpId);
			$this->view->dateformat            = $this->_ehrTables->orgDateformat();
			$this->view->datetime              = $this->_ehrTables->gettimezoneDateTime();
			$this->view->getDeviceName     	   = $this->_dbAttendance->getDeviceName();
			$this->view->month	 			   = $this->_dbCommonFunction->getMonth();
			$this->view->attendanceYear 	   = $this->_dbCommonFunction->getAttendanceYear();
			$currentDate 					   = date('Y-m-d');
			$this->view->salaryDateDetails     = $this->_dbPayslip->getSalaryDateRange(date('m',strtotime($currentDate)),date('Y',strtotime($currentDate)),strtotime($currentDate), 29);
	
			$dbWorkSchedule           = new Organization_Model_DbTable_WorkSchedule();
			$this->view->workschedule = $dbWorkSchedule->getWorkSchedule();
			
			$dbLeaveType           = new Employees_Model_DbTable_Leave();
			$this->view->leaveType = $dbLeaveType->getLeaveType();			
			$this->view->unpaidLeaveType = $dbLeaveType->getUnpaidLeaveType();

			$subDomain = $this->_ehrTables->getOrgCode();
			$this->view->viewOrganizationDetails = $this->_dbOrgSettings->viewOrgDetail($subDomain);
			$this->view->serviceProvider 	     = $this->_dbEmployee->getEmployeeServiceProviderList($this->_logEmpId);
			$this->view->attendanceSettings 	 = $this->_attendanceSettings;
		} else {
			$this->_redirect('auth');
		}
	}

	/**
	 * Get attendance details to show in a grid
	 */
	public function showAttendanceAction()
	{
		$this->_helper->layout()->disableLayout();
		
		if(isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('show-attendance', 'json')->initContext();
			
			
			if($this->_attendanceAccessRights['Employee']['View']==1)
			{
				$sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
                
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$employeeId = $this->_getParam('sSearch_0', null);
				$employeeId = filter_var($employeeId, FILTER_SANITIZE_STRIPPED);
				
				$attendanceBeginDate = $this->_getParam('sSearch_1', null);
				$attendanceBeginDate = filter_var($attendanceBeginDate, FILTER_SANITIZE_STRIPPED);
				
				$attendanceEndDate = $this->_getParam('sSearch_2', null);
				$attendanceEndDate = filter_var($attendanceEndDate, FILTER_SANITIZE_STRIPPED);
				
				$breakHours = $this->_getParam('sSearch_5', null);
				$breakHours = filter_var($breakHours, FILTER_SANITIZE_STRIPPED);
				
				$status = $this->_getParam('sSearch_6', null);
				$status = filter_var($status, FILTER_SANITIZE_STRIPPED);
				
				$totalHours = $this->_getParam('sSearch_7', null);
				$totalHours = filter_var($totalHours, FILTER_SANITIZE_STRIPPED);
				
				$checkInWorkPlaceId = $this->_getParam('sSearch_8', null);
				$checkInWorkPlaceId = filter_var($checkInWorkPlaceId, FILTER_SANITIZE_NUMBER_INT);

				$checkOutWorkPlaceId = $this->_getParam('sSearch_9', null);
				$checkOutWorkPlaceId = filter_var($checkOutWorkPlaceId, FILTER_SANITIZE_NUMBER_INT);

				$checkInFormSourceId = $this->_getParam('sSearch_10', null);
				$checkInFormSourceId = filter_var($checkInFormSourceId, FILTER_SANITIZE_NUMBER_INT);

				$checkOutFormSourceId = $this->_getParam('sSearch_11', null);
				$checkOutFormSourceId = filter_var($checkOutFormSourceId, FILTER_SANITIZE_NUMBER_INT);

				$serviceProviderId = $this->_getParam('sSearch_12', null);
				$serviceProviderId = filter_var($serviceProviderId, FILTER_SANITIZE_NUMBER_INT);
				
				$location = $this->_getParam('sSearch_13',null);
				$location = filter_var($location, FILTER_SANITIZE_NUMBER_INT);

				$department = $this->_getParam('sSearch_14',null);
				$department = filter_var($department, FILTER_SANITIZE_NUMBER_INT);

				$managerId = $this->_getParam('sSearch_15', null);
				$managerId = filter_var($managerId, FILTER_SANITIZE_NUMBER_INT);

				$lateAttendance = $this->_getParam('sSearch_16', null);
				$lateAttendance = filter_var($lateAttendance, FILTER_SANITIZE_STRIPPED);

				$autoShortTimeOff = $this->_getParam('sSearch_17', null);
				$autoShortTimeOff = filter_var($autoShortTimeOff, FILTER_SANITIZE_STRIPPED);

				$searchArr = array( 'employeeId'        => $employeeId,
								    'attendanceBeginDate'  => $attendanceBeginDate,
									'attendanceEndDate'    => $attendanceEndDate,
									'breakHours'        => $breakHours,
									'totalHours'        => $totalHours,
									'status'            => $status,
									'checkInWorkPlaceId'  => $checkInWorkPlaceId,
									'checkOutWorkPlaceId' => $checkOutWorkPlaceId,
									'checkInFormSourceId'  => $checkInFormSourceId,
									'checkOutFormSourceId' => $checkOutFormSourceId,
									'serviceProviderId'    => $serviceProviderId,
									'Location_Id'    => $location,
									'Department_Id'  => $department,
									'Manager_Id'     => $managerId,
									'lateAttendance' => $lateAttendance,
									'autoShortTimeOff' => $autoShortTimeOff);
				
				$attendanceAccess = array( 'Is_Manager' => $this->_attendanceAccessRights['Employee']['Is_Manager'],
										  'Update'      => $this->_attendanceAccessRights['Employee']['Update'],
										  'Admin'       => $this->_attendanceAccessRights['Admin'],
										  'LogId'       => $this->_logEmpId);

				//to display all the attendance records in the data grid
				$this->view->result = $this->_dbAttendance->listAttendance($page, $rows, $sortField, $sortOrder, $searchAll, $searchArr,
																		   $attendanceAccess, $this->_formNameA);
            }
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}
	}
	
	public function checkGeoEnforceAction()
	{
		$this->_helper->layout()->disableLayout();
		if(isset($_SERVER['HTTP_REFERER']))
		{
			$attendanceDetails  = $this->_dbAttendance->checkEmployeeAttendance ($this->_logEmpId);
			if(!empty($attendanceDetails))
			{
				$attendanceId = $attendanceDetails['Attendance_Id'];
			}
			else 
			{
				$attendanceId = '';
			}
			
			
			if ((empty($attendanceId) &&  ($this->_attendanceAccessRights['Employee']['Add'] == 1 || $this->_myTeamAccess['Add']==1 || $this->_selfServiceAccess['Add']==1  ||  $this->_dashboardAttendanceAccessRights['Employee']['Optional_Choice'] == 1)) || 
			(!empty($attendanceId) && ($this->_attendanceAccessRights['Employee']['Update'] == 1 ||  $this->_myTeamAccess['Update']==1 || $this->_selfServiceAccess['Update']==1 ||  $this->_dashboardAttendanceAccessRights['Employee']['Optional_Choice'] == 1)))
			{
				$ajaxContext = $this->_helper->getHelper('AjaxContext');
				$ajaxContext->addActionContext('check-geo-enforce', 'json')->initContext();
	
				$this->view->result = $this->_dbAttendance->checkGeoEnforce($this->_logEmpId);
			}
			else
			{
				$this->view->result = array('success'=>false, 'msg'=>'Access denied', 'type'=>'danger');
			}
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}
	}
/*Function to check work place is enabled or not  */
	public function isEnableWorkPlaceAction()
	{
		$this->_helper->layout()->disableLayout();
		if(isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('is-enable-work-place', 'json')->initContext();

			$this->view->result = $this->_dbAttendance->isEnableWorkPlace($this->_logEmpId);
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}
	}

	/*Function to check work place is required or not based on isEnableWorkPlace variable */
	public function workPlaceValidate($isEnableWorkPlace,$isTheme,$PunchType,$PunchInWorkPlace,$PunchOutWorkPlace)
	{
		if($isEnableWorkPlace == 1){ 											// if isEnableWorkPlace is true then checks punch in and punch out are not empty
			if($isTheme == "Bootstrap" || $isTheme == "DashBoard" || $isTheme == "dashboardMissedCheckout"){
				if($PunchType == "Punch_In" && $PunchInWorkPlace != "" && $PunchInWorkPlace != null){
					return true;
				} else if($PunchType == "Punch_Out" && $PunchOutWorkPlace != "" && $PunchOutWorkPlace != null) {
					return true;
				} else {
					return false;
				}
			} else if($isTheme == "dashboardMissedCheckinCheckout" || $isTheme == "Attendance") {
				if($PunchType == "Punch_In" && $PunchInWorkPlace != "" && $PunchInWorkPlace != null){
					return true;
				} else if($PunchType == "Punch_Out" && $PunchInWorkPlace != "" && $PunchInWorkPlace != null &&  $PunchOutWorkPlace != "" && $PunchOutWorkPlace != null) {
					return true;
				} else {
					return false;
				}
			}
		} else {															// return true if isEnableWorkPlace flag is false (don't need to check work place)
			return true;
		}
	}

	public function updateAttendanceAction()
	{
		$this->_helper->layout()->disableLayout();
				
		if(isset($_SERVER['HTTP_REFERER']))
		{
			if($this->_hrappMobile->checkAuth())
			{
			
				$ajaxContext = $this->_helper->getHelper('AjaxContext');
				$ajaxContext->addActionContext('update-attendance', 'html')->initContext();
				
				$isTheme = $this->_getParam('istheme', null);
				$isTheme = filter_var($isTheme, FILTER_SANITIZE_STRIPPED);

				if (Zend_Registry::isRegistered('orgDetails'))
					$this->_orgDetails = Zend_Registry::get('orgDetails');
				
				$attendanceDetails = array();
				if ($isTheme == 'DashBoard')
				{
					$attendanceDetails  = $this->_dbAttendance->checkEmployeeAttendance ($this->_logEmpId);
					if(!empty($attendanceDetails))
					{
						$attendanceId = $attendanceDetails['Attendance_Id'];
					}
					else 
					{
						$attendanceId = '';
					}
				}
				else
				{
					$attendanceId = $this->_getParam('attendanceId',null);
					$attendanceId = filter_var($attendanceId, FILTER_SANITIZE_NUMBER_INT);
				}

				if(($isTheme == 'Attendance' && 
								((empty($attendanceId) && ($this->_attendanceAccessRights['Employee']['Add'] == 1 || $this->_myTeamAccess['Add']==1 || $this->_selfServiceAccess['Add']==1)) || 
								(!empty($attendanceId) && ($this->_attendanceAccessRights['Employee']['Update'] == 1 || $this->_myTeamAccess['Update']==1 || $this->_selfServiceAccess['Update']==1)))) ||

					($isTheme == 'Bootstrap' && $this->_attendanceBoxRights['Employee']['Update'] == 1) ||

					($isTheme == 'DashBoard' && $this->_dashboardAttendanceAccessRights['Employee']['Optional_Choice'] == 1) ||

					(($isTheme == 'dashboardMissedCheckout' || $isTheme == 'dashboardMissedCheckinCheckout') &&
								((empty($attendanceId) && $this->_attendanceRegularizationAccessRights['Employee']['Add'] == 1) || 
								(!empty($attendanceId) && $this->_attendanceRegularizationAccessRights['Employee']['Update'] == 1)))
				)
				{
					if ($this->getRequest()->isPost())
					{
						//In case of error need to show when this error has occurred whether while adding/updating attendance record
						if(empty($attendanceId) ){
							$errorMsg = "Something went wrong while adding your attendance record. Please contact system admin";
						}
						else{			
							$errorMsg = "Something went wrong while updating your attendance record. Please contact system admin";
						}
						//while updating attendance from vue app we are not able to get params using getPost()
						//so if getPost is empty used getRawBody to get params. 
						//If both are empty we present error to the user
						$formData = $this->getRequest()->getPost();
						if(empty($formData )){
							$requestBody =$this->getRequest()->getRawBody();
							if ($requestBody)
							{
								$formData = Zend_Json::decode($requestBody);
							}
							else{								
								$this->view->result = array('success'=>false, 'msg'=>$errorMsg, 'type'=>'warning');				
						    }						
						}

						$attendanceRow = '';
						if ($isTheme == 'Bootstrap' || $isTheme == 'DashBoard' || $isTheme =='dashboardMissedCheckout')
						{
							if( !empty($attendanceId) )
							{
								$attendanceRow = $this->_dbAttendance->viewAttendance($attendanceId);
								$employeeId = $attendanceRow['Employee_Id'];
							}else{
								if($isTheme == 'DashBoard' || $isTheme =='dashboardMissedCheckout')
								{
									$employeeId = $this->_logEmpId;
								}
								else
								{
									$employeeId = $this->_getParam('EmpHidden',null);
								}
							}
						}else{
							$employeeId = filter_var($formData['employeeId'], FILTER_SANITIZE_NUMBER_INT);
						}
						$employeeDetails     = $this->_dbAttendance->getEmployeeAttendanceDetails($employeeId);
						$isEmpFinalSettlementInitiated = $this->_dbCommonFunction->empFinalSettlementInitiated($employeeId);
						if($isEmpFinalSettlementInitiated == 1){
							$this->view->result = array('success' => false, 'msg' => 'Attendance cannot be added or updated as the full and final settlement is initiated or settled for the employee.Kindly delete the F & F settlement in order to make the necessary modifications.', 'type' => 'warning');
						}else{
						$latitude =  $formData['latitude'];
						$longitude = $formData['longitude'];

						if($isTheme == 'DashBoard')
						{
							$attendanceDuration = (!empty($formData['attendanceDuration'])) ? ($formData['attendanceDuration']) : (null);
							$attendancePeriod = (!empty($formData['attendancePeriod'])) ? ($formData['attendancePeriod']) : (null);
						}
						else
						{
							$attendanceDuration = null;
							$attendancePeriod 	= null;
						}
						$onDutyAttendanceDetails = array('Attendance_Duration'=>$attendanceDuration,
													     'Attendance_Period'=>$attendancePeriod,
														 'Is_Theme'=>$isTheme);

						if ($isTheme == 'Bootstrap' || $isTheme == 'DashBoard' || $isTheme =='dashboardMissedCheckout')
						{
							$checkInOut= $this->_dbAttendance->getDataSource($attendanceId,$formData['Checkin_Data_Source'],$formData['Checkout_Data_Source'],
																			'','','','',$isTheme);

							$checkInOutGeolocation= $this->_dbAttendance->getGeolocation($attendanceId,$latitude,$longitude,
							'','','','',$isTheme);

							switch($isTheme){
								case 'Bootstrap': $formSource = 'Attendance Box'; break;
								case 'DashBoard': $formSource = 'Dashboard Attendance'; break;
								case 'dashboardMissedCheckout': $formSource = 'Attendance Regularization';
							}
							
							$formSourceId = $this->_dbComment->getFormId($formSource);

								if( !empty($attendanceId) )
								{
									$workPlaceOut = (!empty($formData['workPlaceId'])) ? ($formData['workPlaceId']) : (null);
									$breakHours = $this->_dbAttendance->getEmpBreakDetails ($employeeId);

									if($isTheme == 'DashBoard')
									{
										$checkShiftExist 				   =  $this->_dbAttendance->checkShiftEnabled($employeeId); 
										if($checkShiftExist=='Shift Roster')
										{
											$businessWorkingDays = $this->_dbPayslip->getBusinessWorkingDays($attendanceRow['Attendance_Date'], $attendanceRow['Attendance_Date'], $employeeId,NULL,1,'leaves');
										}
										else 
										{
											$businessWorkingDays = $this->_dbPayslip->getBusinessWorkingDays($attendanceRow['Attendance_Date'], $attendanceRow['Attendance_Date'], $employeeId,'',1);
										}
										
										if($businessWorkingDays > 0)
										{
											$dashboardAttendanceApprovalStatus = $this->_dbAttendance->getDashboardAttendanceApprovalStatus($employeeId,$attendanceRow['Checkin_Work_Place_Id'],$workPlaceOut);
											$approvalStatus 				   = $dashboardAttendanceApprovalStatus;
										}
										else
										{
											$approvalStatus = "Applied";
										}
									}
									else
									{
										$approvalStatus = "Applied";
									}
						
									$formData = array('employeeId'    => $employeeId,
													'punchType'     => 'Punch_Out',
													'punchInDate'   => $attendanceRow['DPunchIn_Date'],
													'punchInTime'   => $attendanceRow['PunchIn_Time'],
													'punchInWorkPlace'=> $attendanceRow['Checkin_Work_Place_Id'],
													'punchOutWorkPlace'=> $workPlaceOut,
													'checkinFormSource'=> $attendanceRow['Checkin_Form_Source'],
													'checkoutFormSource'=> $formSourceId,
													'status'        => $approvalStatus,
													'punchOutDate'  => ($isTheme =='dashboardMissedCheckout')? $formData['punchOutDate'] : date("Y-m-d"),
													'punchOutTime'  => ($isTheme =='dashboardMissedCheckout')? $formData['punchOutTime'] : date('H:i'),
													'excludeBrkHrs' => $breakHours['breakHours'],
													'comment'       => '',
													'lateAttendance'=> $attendanceRow['Late_Attendance'],
													'Checkin_Latitude'  => $checkInOutGeolocation['Checkin_Latitude'],
													'Checkin_Longitude' => $checkInOutGeolocation['Checkin_Longitude'],
													'Checkin_Address' => $checkInOutGeolocation['Checkin_Address'],
													'Checkout_Latitude' => $checkInOutGeolocation['Checkout_Latitude'],
													'Checkout_Longitude'=> $checkInOutGeolocation['Checkout_Longitude'],
													'Checkout_Address'=> $checkInOutGeolocation['Checkout_Address'],
													'Checkin_Data_Source'=> $checkInOut['Checkin_Data_Source'],
													'Checkout_Data_Source'=>$checkInOut['Checkout_Data_Source']
												);
													
								}
								else // Add
								{
									$breakHours = $this->_dbAttendance->getEmpBreakDetails ($employeeId);
										
									$workPlaceIn = (!empty($formData['workPlaceId'])) ? ($formData['workPlaceId']) : (null);

									if($this->_orgDetails['Multiple_Source_Attendance_Same_Time']=='Yes' && !empty($employeeDetails) && !empty($employeeDetails['External_EmpId']) && $isTheme =='dashboardMissedCheckout')
									{
										$punchInDate = $formData['punchOutDate'];
										$punchInTime = $formData['punchOutTime'];
									}
									else
									{
										$punchInDate = date("Y-m-d");
										$punchInTime = date('H:i');
									}

									$formData = array('employeeId'    => $employeeId,
													'punchType'     => 'Punch_In',
													'punchInDate'   => $punchInDate,
													'punchInTime'   => $punchInTime,
													'punchOutDate'  => '',
													'punchOutTime'  => '',
													'punchInWorkPlace' => $workPlaceIn,
													'checkinFormSource'=> $formSourceId,
													'checkoutFormSource'=> null,
													'punchOutWorkPlace'=> null,
													'excludeBrkHrs' => $breakHours['breakHours'],
													'status'        => 'Draft',
													'comment'       => '',
													'lateAttendance'=> 0,
													'Checkin_Latitude'  => $checkInOutGeolocation['Checkin_Latitude'],
													'Checkin_Longitude' => $checkInOutGeolocation['Checkin_Longitude'],
													'Checkin_Address' => $checkInOutGeolocation['Checkin_Address'],
													'Checkout_Latitude' => $checkInOutGeolocation['Checkout_Latitude'],
													'Checkout_Longitude'=> $checkInOutGeolocation['Checkout_Longitude'],
													'Checkout_Address'=> $checkInOutGeolocation['Checkout_Address'],
													'Checkin_Data_Source'=> $checkInOut['Checkin_Data_Source'],
													'Checkout_Data_Source'=>$checkInOut['Checkout_Data_Source']
													);
								}
								$punchInTime = $formData['punchInTime'];
								$punchOutTime = $formData['punchOutTime'];

								
						}
						else
						{
							$punchInTime = $formData['punchInTime'];
							$punchOutTime = $formData['punchOutTime'];

							$checkInOut= $this->_dbAttendance->getDataSource($attendanceId,$formData['Checkin_Data_Source'],$formData['Checkout_Data_Source'],
							$formData['punchInDate'],$formData['punchInTime']
							,$formData['punchOutDate'],$formData['punchOutTime'],$isTheme);

							$checkInOutGeolocation= $this->_dbAttendance->getGeolocation($attendanceId,$latitude,$longitude,
							$formData['punchInDate'],$formData['punchInTime']
							,$formData['punchOutDate'],$formData['punchOutTime'],$isTheme);

							if($isTheme === 'Attendance')
							{
								$formSource = 'Attendance';
							}
							else if($isTheme === 'dashboardMissedCheckinCheckout')
							{
								$formSource = 'Attendance Regularization';
							}

							$checkinoutFormSourceId = $this->_dbComment->getFormId($formSource);

							if(!empty($attendanceId))
							{
								$attendanceDetails = $this->_dbAttendance->viewAttendance($attendanceId);

								$formData['checkinFormSource']  = $attendanceDetails['Checkin_Form_Source'];
								$formData['checkoutFormSource'] = $checkinoutFormSourceId;
							}
							else
							{
								if(empty($formData['punchOutDate']))
								{
									$formData['checkinFormSource']  = $checkinoutFormSourceId;
									$formData['checkoutFormSource'] = null;
								}
								else
								{
									$formData['checkinFormSource']  = $checkinoutFormSourceId;
									$formData['checkoutFormSource'] = $checkinoutFormSourceId;
								}
							}
						}

							$punchInDate = $this->_validation->dateValidation($formData['punchInDate']);
							
							if(!empty($formData['punchOutDate']))
							{
								$punchOutDateValidate = $this->_validation->dateValidation($formData['punchOutDate']);
								$punchOutDate = $punchOutDateValidate['value'];
							}
							else
							{
								$punchOutDate = '';
							}
							
							$comment 		  = $this->_validation->commonFilters($formData['comment']);
							$comment          = $this->_validation->alphaNumSpCDotHySlashValidation($comment);
							$comment['valid'] = $this->_validation->lengthValidation($comment, 5, 3000, false);
							
							$dbTimesheetHrs  = new Employees_Model_DbTable_TimesheetHours();
							
							$empTimesheetHrs = $dbTimesheetHrs->empTimesheetHoursExists($formData['employeeId']);
							
							if ($empTimesheetHrs > 0)
							{
								$currentDate = strtotime(date('Y-m-d'));
								$currentSec = strtotime(date('Y-m-d H:i'));
								
								$punchInSec = (date('Y-m-d H:i',strtotime($punchInDate['value'] .' '. $punchInTime)));
								$punchInSecToTime = strtotime($punchInSec);
								$punchOutSec = null;
								
								if (!empty($punchOutDate) && !empty($punchOutTime))
								{
									$punchOutSec = date('Y-m-d H:i',strtotime($punchOutDate .' '. $punchOutTime));
									$punchOutSecToTime = strtotime($punchOutSec);
								}
								
								$inTime = strtotime($punchInTime);
								$outTime = strtotime($punchOutTime);
								$dateValid = "";
								$timeValid = "";
								
								if ($formData['punchType'] == "Punch_Out")
								{
									if (strtotime($punchInDate['value']) <= $currentDate && (strtotime($punchInDate['value']) == strtotime($punchOutDate) ||
										((strtotime($punchOutDate .' '. $punchOutTime ) - strtotime($punchInDate['value'] .' '. $punchInTime)) <= 86400)))
									{
										if (strtotime($punchInDate['value']) == strtotime($punchOutDate))
										{
											if ($inTime < $outTime)
											{
												$dateValid = "1";
											}
										}
										else if ((strtotime($punchOutDate) - strtotime($punchInDate['value'])) == 86400)
										{
											if ($inTime < $outTime || $outTime < $inTime)
											{
												$dateValid = "1";
											}
										}
									}
									else
									{
										$dateValid = "";
									}
								}
								else
								{
									if (strtotime($punchInDate['value']) < $currentDate || (strtotime($punchInDate['value']) == $currentDate
																				&& $inTime < strtotime(date('H:i:s'))))
									{
										$dateValid = 1;
									}
									else
									{
										$dateValid = "";
									}
								}
								
								if (!empty($punchOutDate) && !empty($punchOutTime))
								{
									//if user is employee punchout should not exceed current second
									//admin or manager can add future time not for them
									if ($punchOutSecToTime <= $currentSec || ((($this->_attendanceAccessRights['Employee']['Is_Manager'] == 1 || $this->_myTeamAccess['Is_Manager']==1 || $this->_selfServiceAccess['Is_Manager']==1)&&
																			( $employeeId != $this->_logEmpId))||
																			( ($this->_attendanceAccessRights['Admin'] == 'admin' || $this->_myTeamAccessRights['Admin']=='admin' || $this->_selfServiceAccessRights['Admin']=='admin') &&
																			( $employeeId != $this->_logEmpId ))))
									{
										$timeValid = 1;
									}
									else
									{
										$timeValid = "";
									}
								}
								else
								{
									$timeValid = 1;
								}
								
								$empDOJ = $this->_dbJobDetail->getDateOfJoin($employeeId);
								
								if(!empty($punchOutDate))
								{
									$empDOJValid = (($empDOJ <= $punchInDate['value']) && ($empDOJ <= $punchOutDate));
								}
								else
								{
									$empDOJValid = ( $empDOJ <= $punchInDate['value'] );
								}
								
								
								if(isset($_SERVER['HTTP_X_FORWARDED_PORT']) && ($_SERVER['HTTP_X_FORWARDED_PORT'])== '443')
								{
									$positionLatIn = $this->_validation->geolocationValidation($latitude);
									$positionLongIn = $this->_validation->geolocationValidation($longitude);
								}
								else
								{
									$positionLatIn['value']="";
									$positionLongIn['value'] ="";
									$positionLatIn['valid'] = true;
									$positionLongIn['valid'] = true;
									
								}
								
								
								if(!empty($punchOutDate) && isset($_SERVER['HTTP_X_FORWARDED_PORT']) && ($_SERVER['HTTP_X_FORWARDED_PORT'])== '443')
								{
									$positionLatOut = $this->_validation->geolocationValidation($latitude);
									$positionLongOut = $this->_validation->geolocationValidation($longitude);
								}
								else
								{
									$positionLatOut['value']="";
									$positionLongOut['value'] ="";
									$positionLatOut['valid'] = true;
									$positionLongOut['valid'] = true;
									
								}
								

								$isEnableWorkPlace = $this->_dbAttendance->isEnableWorkPlace($employeeId);
								// To throw an error when work place flag is enabled in employee type and add attendance without work place. 
								if($isEnableWorkPlace){
									if($isTheme == 'Bootstrap'){

										$officeId = $this->_dbAttendance->getOfficeWorkPlaceId();

										if($formData['punchType'] == 'Punch_In'){ 
											$formData['punchInWorkPlace'] = $officeId;
										} else {
											$formData['punchOutWorkPlace'] = $officeId;
										}
									}
								}
								$workPlaceValidate = $this->workPlaceValidate($isEnableWorkPlace,$isTheme,$formData['punchType'],$formData['punchInWorkPlace'],$formData['punchOutWorkPlace']);

								if($workPlaceValidate)
								{
									if ((empty($punchOutTime) || ($punchInTime != $punchOutTime && $punchOutDateValidate['valid'])) && $dateValid && $timeValid &&
										((!empty($punchOutTime) && !empty($punchOutDate)) ||(empty($punchOutTime) && empty($punchOutDate)))
										&& $empDOJValid && $punchInDate['valid'] && $comment['valid'] && !empty($formData['employeeId'])
										&& (empty($positionLatIn) || $positionLatIn['valid'])
										&& (empty($positionLatOut) || $positionLatOut['valid']) && (empty($positionLongIn) || $positionLongIn['valid'])
										&& (empty($positionLongOut) || $positionLongOut['valid']))
									{
										if($isTheme == 'DashBoard')
										{
											if($formData['punchType'] == 'Punch_In')
											{
												$punchInTime = date('H:i:s');
											}
											else
											{
												if($attendanceId > 0)
												{
													$attendanceRow = $this->_dbAttendance->viewAttendance($attendanceId);
													$punchInTime = $attendanceRow['PunchIn_Time_Seconds'];
												}
												$formData['punchOutTime'] = date('H:i:s');
											}
										}

										$empDetails = $this->_dbAttendance->getEmpAttendanceDetails ($formData['employeeId']);
										if($empDetails['Manager_Id'] != 0)
										{
											//when the employee manager id exist we need to use that
											$approverIds = $empDetails['Manager_Id'];	
										}
										else
										{
											//when the employee doesnt have manager we need to first admin record as manager id
											$adminIds = $this->_dbCommonFunction->listAdmins();
											$approverIds = $adminIds[0];
											if(empty($approverIds))
											{
												$approverIds = $formData['employeeId'];
											}
										}
										
										$attendanceArray = array('Attendance_Id'       	=> $attendanceId,
																'Employee_Id'         	=> $formData['employeeId'],
																'Approver_Id'         	=> $approverIds,
																'Approval_Status'     	=> $formData['status'],
																'PunchIn_Date'        	=> $formData['punchInDate'],
																'Checkin_Work_Place_Id'=> (!empty($formData['punchInWorkPlace'])) ? ($formData['punchInWorkPlace']) : (null),
																'PunchOut_Date'       	=> (!empty($punchOutDate)) ? $punchOutDate : NULL,
																'PunchIn_Time'        	=> $punchInTime,
																'PunchOut_Time'       	=> (!empty($formData['punchOutTime'])) ? $formData['punchOutTime'] : NULL,
																'Checkout_Work_Place_Id'=> (!empty($formData['punchOutWorkPlace'])) ? ($formData['punchOutWorkPlace']) : null,
																'Checkin_Form_Source'   => $formData['checkinFormSource'] ? $formData['checkinFormSource'] : null,
																'Checkout_Form_Source'  => $formData['checkoutFormSource'] ? $formData['checkoutFormSource'] : null,
																'Exclude_Break_Hours' 	=> $formData['excludeBrkHrs'],
																'Late_Attendance'     	=> $formData['lateAttendance'],
																'Checkin_Latitude'		=> $checkInOutGeolocation['Checkin_Latitude'],
																'Checkin_Longitude'	=> $checkInOutGeolocation['Checkin_Longitude'],
																'Checkin_Address'       => $checkInOutGeolocation['Checkin_Address'],
																'Checkout_Latitude'	=> $checkInOutGeolocation['Checkout_Latitude'],
																'Checkout_Longitude'	=> $checkInOutGeolocation['Checkout_Longitude'],
																'Checkout_Address'	=> $checkInOutGeolocation['Checkout_Address'],
																'Checkin_Data_Source'	=> $checkInOut['Checkin_Data_Source'],
																'Checkout_Data_Source' => $checkInOut['Checkout_Data_Source'],
																'Actual_Punch_In_Time'=>'',//new Zend_Db_Expr('NULL'),
																'Actual_Total_Hours' => 0,//new Zend_Db_Expr('NULL')//to do
																'Actual_PunchOut_Time' => ''
																);
										
										

										$employeeDetails     = $this->_dbAttendance->getEmployeeAttendanceDetails($attendanceArray['Employee_Id']);
										if($this->_orgDetails['Multiple_Source_Attendance_Same_Time']=='Yes' && !empty($employeeDetails) && !empty($employeeDetails['External_EmpId']))
										{
											$checkInConsiderationTime = 0;
											$checkOutConsiderationTime = 0;
										}
										else
										{
											$checkInConsiderationTime = $this->_dbAttendance->getValidPunchIn($attendanceArray['Employee_Id'], $attendanceArray['PunchIn_Date'], date("H:i:s", strtotime($attendanceArray['PunchIn_Time'])));
											$checkOutConsiderationTime = $this->_dbAttendance->getValidPunchOut($attendanceArray['Employee_Id'], $attendanceArray['PunchOut_Date'], date("H:i:s", strtotime($attendanceArray['PunchOut_Time'])), $attendanceArray['PunchIn_Date'],date("H:i:s", strtotime($attendanceArray['PunchIn_Time'])));
										}

										if($checkInConsiderationTime == 0 && ($checkOutConsiderationTime==0 || empty($attendanceArray['PunchOut_Time'])))
										{
											$customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
											$customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
											
											if(strtolower($isTheme) == 'attendance')
											{
												$autoShortTimeOffLateAttendanceMessage = $this->_dbAttendance->getAutoShortTimeOffLateAttendanceExist($attendanceArray);
											}
											else
											{
												$autoShortTimeOffLateAttendanceMessage = '';	
											}
											
											if(!empty($autoShortTimeOffLateAttendanceMessage))
											{
												$this->view->result = $autoShortTimeOffLateAttendanceMessage;
											}
											else
											{
												$result = $this->_dbAttendance->updateAttendance($attendanceArray, $comment['value'], $this->_logEmpId,$this->_formNameA, $customFormNamee,$onDutyAttendanceDetails,$attendanceDetails,$isTheme);
												//if the employee entered both punchin and punchout time the attendance will be applied and a mail will be sent to that manager
												if($result['success'] && $result['status'] == "Applied")
												{
													if ($isTheme == 'DashBoard')
													{
														$result['attList'] = $this->_dbAttendance->getCurrentDateAttendance($this->_logEmpId);
													}
													$this->view->result = $result;	
												}
												else
												{
													if ($result['success'])
														$result['attList'] = $this->_dbAttendance->getCurrentDateAttendance($this->_logEmpId);
														
													$this->view->result = $result;	
												}
											}
										}
										else
										{
											if($checkInConsiderationTime == 1 && ($checkOutConsiderationTime==1 && !(empty($attendanceArray['PunchOut_Time']))))
											{
												$this->view->result = array('success' => false, 'msg'=>'Shift is not scheduled or check-in time and check-out time does not fall in work schedule configured time. Please check the consideration time configuration in work schedule or contact your HR administrator', 'type'=>'info');
											}
											else if($checkInConsiderationTime == 1)
											{
												$this->view->result = array('success' => false, 'msg'=>'Shift is not scheduled or check-in time does not fall in work schedule configured time. Please check the consideration time configuration in work schedule or contact your HR administrator', 'type'=>'info');
											}
											else
											{
												$this->view->result = array('success' => false, 'msg'=>'Shift is not scheduled or check-out time does not fall in work schedule configured time. Please check the consideration time configuration in work schedule or contact your HR administrator', 'type'=>'info');
											}
										}
									}
									else
									{
										if($punchInTime == $punchOutTime)
										{
											$this->view->result = array('success'=>false, 'msg'=>'Punch-in time and punch-out time should not be in the same time', 'type'=>'warning');
										}else if(!$empDOJValid){
											$this->view->result = array('success'=>false, 'msg'=>'Attendance cannot be added as the date of join is in the future', 'type'=>'warning');
										}else{
											$this->view->result = array('success'=>false, 'msg'=>'Invalid data', 'type'=>'warning');
										}
									}
								}
								else
								{
									$this->view->result = array('success'=>false, 'msg'=>"Could not add attendance with out work place", 'type'=>'info');
								}
							}
							else
							{
								if ($this->_logEmpId == $formData['employeeId'])
								{
									$this->view->result = array('success'=>false, 'msg'=>'No timesheet hours alloted for your grade', 'type'=>'info');
								}
								else
								{
									$this->view->result = array('success'=>false, 'msg'=>'No timesheet hours alloted for this employee\'s grade', 'type'=>'info');
								}
							}

						
					}
					}
				}
				else
				{
					$this->view->result = array('success'=>false, 'msg'=>'Access denied', 'type'=>'danger');
				}
		  	}
		  	else
			{
				$requestData =$this->getRequest()->getRawBody();
				if ($requestData)
				{
					$formData = Zend_Json::decode($requestData);
				}
				//if session expired, check for requestResource in header.If requestResource is HRAPP,
				// then return error response otherwise redirect to auth page
				if(empty($formData)|| (!empty($formData) && $formData['requestResource'] !== 'HRAPPUI')){
						if (Zend_Session::namespaceIsset('lastRequest'))
						Zend_Session:: namespaceUnset('lastRequest');
					
						$session = new Zend_Session_Namespace('lastRequest');
						$session->lastRequestUri = 'v3/employee-self-service/attendance';
						$this->_redirect('auth');
				}
				else{
					$this->view->result = array('success'=>false, 'msg'=>'Session Expired', 'type'=>'danger');

				}
			}
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}
	}
	
	/**
	 * Delete attendance
	 */
	public function deleteAttendanceAction()
	{
		$this->_helper->layout()->disableLayout();
		
		if(isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('delete-attendance', 'html')->initContext();
			if ($this->getRequest()->isPost())
			{
				if($this->_attendanceAccessRights['Employee']['Delete'] == 1 || $this->_myTeamAccess['Delete']==1)
				{
					$formData = $this->getRequest()->getPost();
					
					$attendanceId = filter_var($formData['attendanceId'], FILTER_SANITIZE_NUMBER_INT, FILTER_REQUIRE_ARRAY);
					$attendanceSummarySelectedDetails = [];

					if(count($attendanceId)>0)
					{
						$attendanceImportExist = filter_var($formData['attendanceImportExist'], FILTER_SANITIZE_NUMBER_INT);
					    //when the attendance import exist is one we need to display delete confirmation popup message based on below condition
						if($attendanceImportExist==1)
					 	{
							$updatedCount = 0;
							foreach($attendanceId as $val)
							{
								$result = $this->_dbAttendance->updateAttendanceImportData($val,$attendanceImportExist,$this->_logEmpId);

								if($result!=1)
								{
									$updatedCount++;
								}
							}
							if(count($attendanceId) == $updatedCount)
							{
								//No record is associated with attendance import we need to show that message.
								$this->view->result = array('msg'=>"Are you sure want to delete ?");
							}
							else
							{
								//atleast one attendance record is associated with attendance import we need to show the message.
								$this->view->result = array('msg'=>'Just to be sure you are doing the right thing here. Deletion of one or more records may revert the imported attendance data roll up status to "Unprocessed". Are you sure want to delete ?');
							}
						}
						else
						{
							//when the attendance import exist is zero regular delete operation has performed
							$updatedCount = 0;
							
							$customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
							$customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
							
							foreach($attendanceId as $val)
							{
								$result = $this->_dbAttendance->deleteAttendance($val, $this->_logEmpId, $this->_formNameA, $customFormNamee);
								if($result['success']){
									$updatedCount++;
									$attendanceSummarySelectedDetails[] = $result['attendanceDetails'];
								}
							}
							$this->_dbCommonFunction->triggerAttendanceSummaryStepFunction($attendanceSummarySelectedDetails,'attendance');
							if(count($attendanceId) == $updatedCount)
								$this->view->result = array('success' => true, 'msg'=>$customFormNamee." record deleted successfully", 'type'=>'success');
							elseif($updatedCount >= 1)
								$this->view->result = array('success' => true, 'msg'=>$customFormNamee." record deleted partially due to the presence of either compensatory off or compensatory off encashment or salary payslip or additional wage claim for this duration.", 'type'=>'info');
							else
								$this->view->result = array('success' => false, 'msg'=>"Unable to delete ".$customFormNamee." record due to the presence of either compensatory off or salary payslip or compensatory off encashment or additional wage claim for this duration", 'type'=>'info');
						}
					}
					else
					{
						$this->view->result = array('success' => false, 'msg'=>"No records found", 'type'=>'info');	
					}
				}
				else
				{
					$this->view->result = array('success'=>false, 'msg'=>'Access denied', 'type'=>'danger');
				}
			}
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}
	}
	
	/**
	 * Approving status for multiple rows at a time
	 */
	public function statusMultiApprovalAction()
	{
		$this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('status-multi-approval', 'json')->initContext();
				
			$attendanceIds = $this->_getParam('attendanceIdArr', null);
			$attendanceIds = filter_var($attendanceIds, FILTER_SANITIZE_NUMBER_INT, FILTER_REQUIRE_ARRAY);
			
			if (count($attendanceIds) > 0 && ($this->_attendanceAccessRights['Employee']['Is_Manager'] == 1 || $this->_attendanceAccessRights['Admin']=='admin'|| $this->_myTeamAccess['Is_Manager']==1 || $this->_myTeamAccessRights['Admin']=='admin'))
			{
				if ($this->getRequest()->isPost())
				{
					$formData = $this->getRequest()->getPost();
					$isValid = 1;
					
					$status = $this->_validation->alphaValidation($formData['status']);
					
					$actionType = $this->_validation->alphaValidation($formData['isAction']); // MultiStatusUpdate/StatusUpdate
					
					$comments           = $this->_validation->alphaNumSpCDotHySlashNewLineValidation($formData['comments']);
					$comments['valid'] 	= $this->_validation->lengthValidation($comments, 5, 3000, false);
					
					
					if( $actionType == 'StatusUpdate')
					{
						$isValid = ((($status == 'Rejected' || $status == 'Returned')&& !empty($comments['value'])) ||
									($status == 'Approved'));
					}
					
					if ( !empty($status) && $comments['valid'] && $isValid)
					{
						$updatedCount = 0;
						
						$empAttendanceAddedByArray = array();
						$empAttendanceEmpIdArray = array();
												
						$customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
						$customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);

						$attendanceSummarySelectedDetails = [];
						for ($i = 0; $i < count($attendanceIds); $i++)
						{
							$accessStatus = $this->_dbComment->payrollStatus($attendanceIds[$i], $this->_formNameA);
							$attendanceEmp = $this->_dbAttendance->attendanceEmployee($attendanceIds[$i]); //get employee name and employeeId
							
							if( $actionType == 'MultiStatusUpdate')
							{
								$isValid = ($accessStatus['Status'] == 'Applied' && ($accessStatus['Approver_Id'] == $this->_logEmpId || $this->_attendanceAccessRights['Admin']=='admin' || $this->_myTeamAccessRights['Admin']=='admin'));
							}
							
							if ( $isValid)
							{
								$commentArray = array( 'Approval_Status' => $status['value'],
													  'Emp_Comment'      => htmlentities($comments['value']),
													  'Parent_Id'        => $attendanceIds[$i],
													  'Employee_Id'      => $this->_logEmpId);
								
								// update status report
								$statusReport = $this->_dbAttendance->statusReport($commentArray, $this->_formNameA,$customFormNamee, $this->_logEmpId);
								
								if ($statusReport['success'])
								{
									if($commentArray['Approval_Status'] =='Rejected'){
										$attendanceSummarySelectedDetails[] = $statusReport['attendanceDetails'];
									}
									$updatedCount++;
								}
							}
						}
						$this->_dbCommonFunction->triggerAttendanceSummaryStepFunction($attendanceSummarySelectedDetails,'attendance');
						if( $updatedCount > 0)
						{
							$this->view->result = array('success' => true, 'msg'=>$customFormNamee.' Status Updated successfully', 'type'=>'success');
						}
						else
						{
							$this->view->result = array('success' => false, 'msg'=>'Unable to update '.$customFormNamee.' Status', 'type'=>'info');
						}
					}
					else
					{
						$this->view->result = array('success' => false, 'msg'=>"Invalid data", 'type'=>'info');
					}
				}
			}
			else
            {
				$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'info');
            }
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}
	}
	
	/**
	 * bulk attendance update
	 */ 
	public function copyAttendanceAction()
	{
		$this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('copy-attendance', 'html')->initContext();
			
			$attendanceId = $this->_getParam('attendanceId', null);
			$attendanceId = filter_var($attendanceId, FILTER_SANITIZE_NUMBER_INT);
			
			if ( !empty($attendanceId) && ($this->_attendanceAccessRights['Employee']['Optional_Choice'] == 1 || $this->_myTeamAccess['Optional_Choice']==1))
			{
				$attendanceRow = $this->_dbAttendance->viewAttendance($attendanceId);
						
				if ( $this->getRequest()->isPost() )
				{
					$formData = $this->getRequest()->getPost();
					
					$comment = $this->_validation->commonFilters($formData['comment']);
					$comment          = $this->_validation->alphaNumSpCDotHySlashValidation($comment);
					$comment['valid'] = $this->_validation->lengthValidation($comment, 5, 3000, false);
					
					if ($comment['valid'] && !empty($formData['copyTo']))
					{
						//getting the employeeId,employeename,approverId
						$employeeDetail = $this->_dbJobDetail->employeeApproverId($formData['copyTo']);
						$employeeName = $this->_dbPersonal->employeeName($this->_logEmpId);
						$status = !empty($attendanceRow['PunchOut_Date']) ? 'Applied' : 'Draft';
						
						$punchInDate = date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($attendanceRow['PunchIn_Date'])));
						$punchInTime = date('H:i:s',strtotime($attendanceRow['PunchIn_Time']));
						$punchOutDate = !empty($attendanceRow['PunchOut_Date']) ? date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($attendanceRow['PunchOut_Date']))) : NULL;
						$punchOutTime = !empty($attendanceRow['PunchOut_Time']) ? date('H:i:s',strtotime($attendanceRow['PunchOut_Time'])) : NULL;
						
						$customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
						$customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
						$attendanceArr = array();
						
						foreach($employeeDetail as $empDetail)
						{
							if(!empty($empDetail['Manager_Id']))
							{
								$approverId = $empDetail['Manager_Id'];
							}
							else
							{
								$approverId = $empDetail['Employee_Id'];
							}
							
							$attendanceArr[] = array('Attendance_Id' 	   => 0,
													 'Employee_Id'         => $empDetail['Employee_Id'],
													 'Approver_Id'  	   => $approverId,
													 'Approval_Status'     => $status,
													 'PunchIn_Date'  	   => $attendanceRow['DPunchIn_Date'],
													 'PunchIn_Time'  	   => $attendanceRow['DPunchIn_Time'],
													 'PunchOut_Date'  	   => $attendanceRow['DPunchOut_Date'],
													 'PunchOut_Time'  	   => $attendanceRow['DPunchOut_Time'],
													 'Total_Hours'  	   => $attendanceRow['Total_Hours'],
													 'Exclude_Break_Hours' => $attendanceRow['Exclude_Break_Hours'],
													 'Added_By'  		   => $this->_logEmpId,
													 'Added_Date'		   => date('Y-m-d H:i:s'),
													 'Actual_Punch_In_Time'=>'',//new Zend_Db_Expr('NULL'),
													 'Actual_Total_Hours'=> 0,
													 'Actual_PunchOut_Time'=>''
													);
							
							if (!is_null($punchOutDate))
							{
								$empArr[] = array('Employee_Id'=>$empDetail['Employee_Id'],
												  'Approver_Id'=>$approverId,
												  'Employee_Name'=>$empDetail['Employee_Name']);
							}
						}
						
						if (count($attendanceArr)>0)
						{
							$inserted = $this->_dbAttendance->bulkAttendanceUpdate($attendanceId, $attendanceArr,htmlentities($comment['value']), $this->_formNameA, $this->_logEmpId,$status, $customFormNamee);
							
							if ($inserted)
							{
								$this->view->result = $inserted;
							}
							else
							{
								$this->view->result = array('success'=>false, 'msg'=>'Error in bulk '.$customFormNamee.' updation', 'type'=>'info');
							}
						}
						else
						{
							$this->view->result = array('success'=>false, 'msg'=>'Invalid Data', 'type'=>'info');
						}
					}
					else
					{
						$this->view->result = array('success'=>false, 'msg'=>'Invalid Data', 'type'=>'info');
					}
				}
			}
			else
			{
				$this->view->result = array('success'=>false, 'msg'=>'Invalid Data', 'type'=>'info');
			}
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}
	}

	/**
	 * Get attendance import details to show in a grid
	 */
	public function importeddataAction()
	{
		$this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('importeddata', 'json')->initContext();
			
			if ($this->_importAccessRights['Employee']['View']==1)
			{
				$sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
                
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$employeeName = $this->_getParam('sSearch_0', null);
				$employeeName = filter_var($employeeName, FILTER_SANITIZE_STRIPPED);
				
				$employeeStartId = $this->_getParam('sSearch_1', null);
				$employeeStartId = filter_var($employeeStartId, FILTER_SANITIZE_STRIPPED);
				
				$employeeEndId = $this->_getParam('sSearch_2', null);
				$employeeEndId = filter_var($employeeEndId, FILTER_SANITIZE_STRIPPED);
				
				$beginDate = $this->_getParam('sSearch_3', null);
				$beginDate = filter_var($beginDate, FILTER_SANITIZE_STRIPPED);
				
				$endDate = $this->_getParam('sSearch_4', null);
				$endDate = filter_var($endDate, FILTER_SANITIZE_STRIPPED);
				
				$attendanceStatus = $this->_getParam('sSearch_5', null);
				$attendanceStatus = filter_var($attendanceStatus, FILTER_SANITIZE_STRIPPED);
				
				$rollupStatus = $this->_getParam('sSearch_6', null);
				$rollupStatus = filter_var($rollupStatus, FILTER_SANITIZE_STRIPPED);
				
				$finalExtEmpId = $this->_getParam('sSearch_7', null);
				$finalExtEmpId = filter_var($finalExtEmpId, FILTER_SANITIZE_STRIPPED);
				
				$errorMessage = $this->_getParam('sSearch_8', null);
				$errorMessage = filter_var($errorMessage, FILTER_SANITIZE_STRIPPED);

				$locationId = $this->_getParam('sSearch_9', null);
				$locationId = filter_var($locationId, FILTER_SANITIZE_NUMBER_INT);

				$serviceProviderId = $this->_getParam('sSearch_10', null);
				$serviceProviderId = filter_var($serviceProviderId, FILTER_SANITIZE_NUMBER_INT);

				$dataSource = $this->_getParam('sSearch_11', null);
				$dataSource = filter_var($dataSource, FILTER_SANITIZE_STRIPPED);

				$formSource = $this->_getParam('sSearch_12', null);
				$formSource = filter_var($formSource, FILTER_SANITIZE_STRIPPED);
				
				$searchArr = array( 'employeeName'     => $employeeName,
								    'employeeStartId'  => $employeeStartId,
									'employeeEndId'    => $employeeEndId,
									'beginDate'        => $beginDate,
									'endDate'          => $endDate,
									'attendanceStatus' => $attendanceStatus,
									'rollupStatus'     => $rollupStatus,
									'finalExtEmpId'    => $finalExtEmpId,
								    'errorMessage'     => $errorMessage,
									'locationId'	   => $locationId,
									'serviceProviderId'=> $serviceProviderId,
									'dataSource'       =>$dataSource,
									'formSource'       =>$formSource);
				
				$importAccess = array('Is_Manager' => $this->_importAccessRights['Employee']['Is_Manager'],
									  'Admin'      => $this->_importAccessRights['Admin'],
									  'LogId'      => $this->_logEmpId);

				//to display all the attendance records in the data grid
				$this->view->result = $this->_dbAttendance->listAttendanceImport($page, $rows, $sortField, $sortOrder, $searchAll,
																				 $searchArr, $importAccess);
            }
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}
	}
	
	//Delete attendance import data
	public function deleteAttendanceImportDataAction()
    {
        $this->_helper->layout()->disableLayout();
     
	    if(isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('delete-attendance-import-data', 'html')->initContext();
					
			if( $this->_importAccessRights['Employee']['Delete'] == 1 )
			{
				$formData = $this->getRequest()->getPost();
				
				$attendanceIds = filter_var($formData['attendanceIds'], FILTER_SANITIZE_NUMBER_INT, FILTER_REQUIRE_ARRAY);
					
				if(count($attendanceIds)>0)
				{
					$updatedCount = 0;
					
					$customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
					$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
					
					foreach($attendanceIds as $val)
					{
						$result = $this->_dbAttendance->deleteAttendanceimportData($val, $this->_logEmpId, $formName);
						
						if($result)
							$updatedCount++;
					}
					
					if(count($attendanceIds) == $updatedCount)
						$this->view->result = array('success' => true, 'msg'=>$formName." record deleted successfully", 'type'=>'success');
					elseif($updatedCount > 1)
						$this->view->result = array('success' => true, 'msg'=>$formName." record deleted partially", 'type'=>'success');
					else
						$this->view->result = array('success' => false, 'msg'=>"Unable to delete ".$formName." record", 'type'=>'success');
				}
				else
				{
					$this->view->result = array('success' => false, 'msg'=>"No records found", 'type'=>'info');	
				}
			}
			else
			{
				$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'info');	
			}
        }
        else
        {
            $this->_helper->redirector('index', 'attendance', 'employees');
        }
    }
	
	/**
	 * Check whether schema exists or not , get Status Pair , Rollup Status and
	 * update attendance import status */
	public function schemaexistAction()
	{
		$this->_helper->layout()->disableLayout();
		
		if(isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('schemaexist', 'json')->initContext();
			
			$title = $this->_getParam('title', null);
			$title = filter_var($title, FILTER_SANITIZE_STRIPPED);
			
			$schemaId = $this->_getParam('schemaId', null);
			$schemaId = filter_var($schemaId, FILTER_SANITIZE_NUMBER_INT);
			
			$attendanceId = $this->_getParam('attendanceId', null);
			$attendanceId = filter_var($attendanceId, FILTER_SANITIZE_NUMBER_INT);
			
			$rollupFlag = $this->_getParam('rollup', null);
			$rollupFlag = filter_var($rollupFlag, FILTER_SANITIZE_NUMBER_INT);
			
			$status = $this->_getParam('status', null);
			$status = filter_var(urldecode($status), FILTER_SANITIZE_STRIPPED);
			
			$lockflag = $this->_getParam('lock', null);
			$lockflag = filter_var($lockflag, FILTER_SANITIZE_NUMBER_INT);
			
			$importFlag = $this->_getParam('importflag', null);
			$importFlag = filter_var($importFlag, FILTER_SANITIZE_NUMBER_INT);
			
			$act = $this->_getParam('act', null);
			$act = filter_var($act, FILTER_SANITIZE_STRIPPED);
			
			if( $act != '')
			{
				switch ($act)
				{
					case 'Schema_Exists':
						$this->view->exist = $this->_dbAttendance->schemaExist($title);
						break;
					case 'Status_Pair':
						
						$statusPair = $this->_dbAttendance->getAttendanceImportStatus($schemaId);
						$rollupFlag = $this->_dbAttendance->getAttendanceRollUpFlag($attendanceId);
						
						$this->view->result = array('statusPair' => $statusPair,
													'rollupFlag' => $rollupFlag);
						break;
					case 'Rollup_Status':
						$this->view->flag = $this->_dbAttendance->getAttendanceRollUpFlag($attendanceId);
						break;
					case 'Update':
						$checkFormLock = $this->_dbAccessRights->checkLockFlag($attendanceId, $this->_ehrTables->attendanceImport, 'Attendance_Id');
						$recordLimit = $this->_dbAccessRights->checkOpenedRecordLimitJS($this->_logEmpId);
						$pageLimit = $recordLimit[1];

						if($checkFormLock != 0 && $checkFormLock == $this->_logEmpId)
						{
							$this->view->access = array('m','<div style="font-size:14px;width:250px;">Same record has been opened by your session in some other browser or system.<br/>
									If you still have problem in opening the form, contact System Admin</div>');

						}
						elseif($checkFormLock != 0 && $checkFormLock != $this->_logEmpId)
						{
							$editEmpName = $this->_dbPersonal->employeeName($checkFormLock);
							$this->view->access = array('m','<div style="font-size:14px">'.$editEmpName['Employee_Name'] . ' is updating this record. Please Wait...</div>');
						}
						else
						{
							if($recordLimit[0])
							{
					
								$setStypeLock = $this->_dbAccessRights->setLockFlag($this->_logEmpId, $attendanceId, $this->_ehrTables->attendanceImport, 'Attendance_Id');
								if($setStypeLock)
									$this->view->access = array('n','success');
								
								
							}
							else
								$this->view->access = array('m','<div style="font-size:14px">You don\'t have rights to edit more than '.$pageLimit.' records.</div>');
						}
						break;
					case 'Submit':
						$this->view->result = $this->_dbAttendance->updateAttendanceImportStatus($attendanceId, $status, $schemaId, $rollupFlag,$this->_logEmpId);
						break;
					default:
						break;
				}
			}
			else
			{
				$this->view->result = array('success'=>false, 'msg'=>'Invalid Data', 'type'=>'info');
			}
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}
	}
	
	/* process attendance import , import attendance details from attendance_import table to attendance table */
	public function processAttendanceAction()
	{
		$this->_helper->layout()->disableLayout();
		if(isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('process-attendance', 'json')->initContext();
			if($this->_importAccessRights['Employee']['Optional_Choice']==1)
			{
				$processMethod = $this->_getParam('Process_Method', null);
				$processMethod = filter_var($processMethod, FILTER_SANITIZE_STRIPPED);

				$processFrom = $this->_getParam('Process_From', null);
				$processFrom = filter_var($processFrom, FILTER_SANITIZE_STRIPPED);

				$processTo = $this->_getParam('Process_To', null);
				$processTo = filter_var($processTo, FILTER_SANITIZE_STRIPPED);

				$startDate = $this->_validation->dateValidation($processFrom);
				$endDate = $this->_validation->dateValidation($processTo);
				if(!empty($startDate['value']) && $startDate['valid'] && !empty($endDate['value']) && $endDate['valid'])
				{
					$processedInput = array('Process_Method'=>$processMethod,
											'Process_From'=>$startDate['value'],
											'Process_To'=>$endDate['value']);

					$this->view->result = $this->_dbAttendance->processAttendanceImportData($processedInput, $this->_logEmpId);
				}
				else
				{
					$this->view->result = array('success'=>false, 'msg'=>'Please choose from and to date', 'type'=>'warning');
				}
			}
			else
			{
				$this->view->result = array('success'=>false, 'msg'=>'Access denied', 'type'=>'danger');
			}
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}
	}
	
	public function syncIclockDataAction()
    {
		$this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('sync-iclock-data', 'json')->initContext();
			
			if ($this->_importAccessRights['Employee']['Optional_Choice'] == 1)
			{
				$deviceId = $this->_getParam('deviceId', null);
				$deviceId = filter_var($deviceId, FILTER_SANITIZE_NUMBER_INT);
				
				$fromDate = $this->_getParam('fromDate', null);
				$fromDate = filter_var($fromDate, FILTER_SANITIZE_STRIPPED);
				
				$toDate = $this->_getParam('toDate', null);
				$toDate = filter_var($toDate, FILTER_SANITIZE_STRIPPED);

				//passing deviceId to getDeviceDetails function in db table
				if (!empty($deviceId) && !empty($fromDate) && !empty($toDate))
				{
					$this->view->result = $this->_dbAttendance->syncIclockData($deviceId,$fromDate,$toDate);			
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
				}
			}
			else
			{
				$this->view->result = array('success'=>false, 'msg'=>'Access denied', 'type'=>'danger');
			}
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}
	}
	
	public function updateBiometricSyncHistoryAction()
	{
		$this->_helper->layout()->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('update-biometric-sync-history', 'json')->initContext();
			
            $requestId = $this->_getParam('requestId', 0);
			$requestId = filter_var($requestId, FILTER_SANITIZE_NUMBER_INT);
            
			if ($this->_importAccessRights['Employee']['Optional_Choice'] == 1)
			{
				if ($this->getRequest()->isPost())
				{
					$formData = $this->getRequest()->getPost();
					$deviceId = $this->_validation->intValidation($formData['deviceId']);
					$fromDate = $this->_validation->dateValidation($formData['fromDate']);
                    $toDate   = $this->_validation->dateValidation($formData['toDate']);
                    $status   = $this->_validation->alphaValidation($formData['status']);                

					if(!empty($deviceId) && !empty($fromDate) && !empty($toDate) && !empty($status))
					{
						$attendanceSyncArray = array('Request_Id'             			=> $requestId,
													'Device_Id'             			=> $formData['deviceId'],
													'Response_Id'             			=> $formData['responseId'],
													'From_Date'                    		=> $formData['fromDate'],
													'To_Date'                      		=> $formData['toDate'],
													'Status'        					=> $formData['status']);						
					}
					else
					{
						$this->view->result = array('success'=>false, 'msg'=>'Invalid Data', 'type'=>'warning');
					}
					
					$this->view->result = $this->_dbAttendance->updateBiometricSyncHistory($attendanceSyncArray);

				}
			}
            else
            {
				$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'info');
            }
        }
        else
        {
            $this->_helper->redirector('index', 'attendance', 'employees');
        }
	}

	/**
	 * for include / exclude the break hours in attendance imported data
	*/
	public function updateimportedDataAction()
	{
		$this->_helper->layout()->disableLayout();
		
		if(isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('updateimported-data', 'json')->initContext();
			
			if ($this->_importAccessRights['Employee']['Optional_Choice'] == 1 && $this->_attendanceAccessRights['Employee']['Is_Manager'] == 1)
			{
				$attendanceImportIds = $this->_getParam('attImportId', null);
				$attendanceImportIds = filter_var($attendanceImportIds, FILTER_SANITIZE_NUMBER_INT, FILTER_REQUIRE_ARRAY);
				
				$breakHourProcess = $this->_getParam('breakHrsProcess', null);
				$breakHourProcess = filter_var($breakHourProcess, FILTER_SANITIZE_STRIPPED);
				
				if(count($attendanceImportIds) > 0 && is_array($attendanceImportIds) && !empty($breakHourProcess))
				{
					$this->view->result = $this->_dbAttendance->updateImportedData($attendanceImportIds, $breakHourProcess, $this->_logEmpId);
				}
				else
				{
					$this->view->result = array('success'=>false, 'msg'=>'Invalid data', 'type'=>'info');
				}
			}
			else
			{
				$this->view->result = array('success'=>false, 'msg'=>'Access denied', 'type'=>'danger');
			}
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}
	}
	
	/**
	 * for cin/cout the status in attendance imported data
	 */
	public function importStatusChangeAction()
    {
		$this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('import-status-change', 'json')->initContext();
			
			if ($this->_importAccessRights['Employee']['Optional_Choice'] == 1 && $this->_attendanceAccessRights['Employee']['Is_Manager'] == 1)
			{
				$attendanceImportIds = $this->_getParam('attImportId', null);
				$attendanceImportIds = filter_var($attendanceImportIds, FILTER_SANITIZE_NUMBER_INT, FILTER_REQUIRE_ARRAY);
				
				$attendanceStatus = $this->_getParam('attendanceStatus', null);
				$attendanceStatus = filter_var($attendanceStatus, FILTER_SANITIZE_STRIPPED);
				
				if(count($attendanceImportIds) > 0 && is_array($attendanceImportIds) && !empty($attendanceStatus))
				{
					$this->view->result = $this->_dbAttendance->updateImportStatus($attendanceImportIds, $attendanceStatus, $this->_logEmpId);
				}
				else
				{
					$this->view->result = array('success'=>false, 'msg'=>'Invalid data', 'type'=>'info');
				}
			}
			else
			{
				$this->view->result = array('success'=>false, 'msg'=>'Access denied', 'type'=>'danger');
			}
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}
    }
	
	/**
	 * Get import format details to show in a grid
	 */
	public function importformatAction()
	{
		$this->_helper->layout()->disableLayout();
		
		if(isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('importformat', 'json')->initContext();
		
			if($this->_importAccessRights['Employee']['View']==1)
			{
				$sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
                
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				//to list the attendance schema in data grid
				$this->view->result = $this->_dbAttendance->listAttendanceSchema($page, $rows, $sortField, $sortOrder, $searchAll);
            }
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}
	}
	
	/**
	 * Attendance import
	 */
	public function attendanceimportAction()
	{
		$this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
    		$ajaxContext->addActionContext('attendanceimport', 'json')->initContext();
			
			$param = $this->_getParam('import',0);
			$param = filter_var($param, FILTER_SANITIZE_NUMBER_INT);
			
			$importMsg = $this->_getParam('msg',null);
			$importMsg = filter_var($importMsg, FILTER_SANITIZE_STRIPPED);
			
			if (empty($param))
			{
				if ($this->getRequest()->isPost())
				{
					$upload = new Zend_File_Transfer_Adapter_Http();
					$fileEx = 0;
					$titleExist = 0;
					
					$schemaName = $this->_getParam('schemaName',null);
					$schemaName = filter_var($schemaName, FILTER_SANITIZE_STRIPPED);
					
					if ($upload->isValid() && !empty($schemaName))
					{
						$uploadedField = $_FILES['fileToUpload'];
						$fileName      = $uploadedField['tmp_name'];
						$filePathInfo  = pathinfo($uploadedField['name']);
						
						$upload->setDestination($this->_ehrTables->uploadPath.'files/');
						$upload->addFilter('Rename', array('target' => $this->_ehrTables->uploadPath.'files/sampleAttendance.'.$filePathInfo['extension'], 'overwrite' => true));
						$isExist = file_exists($this->_ehrTables->filePath.'/'.$this->_ehrTables->uploadPath.'files/sampleAttendance.'.$filePathInfo['extension']);
						
						if ($isExist && !empty($fileName))
						{
							unlink($this->_ehrTables->uploadPath.'files/sampleAttendance.'.$filePathInfo['extension']);
						}
						
						$titleExist = $this->_dbAttendance->schemaExist($schemaName);
						
						if ($titleExist == 0)
						{
							try
							{
								if($upload->receive())
								{
									$fileEx = 1;
								}
							}
							catch(Zend_File_Transfer_Exception $fileEx)
							{
								$fileEx = 0;
							}
						}
						else
						{
							$this->view->result = array('success' => false, 'msg' => 'Title already exist', 'type' => 'warning');
						}
					}
					
					if($fileEx)
					{
						$this->view->result = array('success' => true, 'extension' => $filePathInfo['extension'],
													'fileHeader' => 1, 'schema' => $schemaName );
						//$this->_helper->redirector('mapheader', 'attendance', 'employees', array('ext'=>$filePathInfo['extension'], 'fh'=>1, 'schema'=>$schemaName));
					}
					else if( $fileEx == 0 && $titleExist == 0)
					{
						$this->view->result = array('success' => false, 'msg' => 'Upload only valid file', 'type' => 'info');
					}
				}
			}
			else
			{
				if ($this->getRequest()->isPost())
				{
					$upload = new Zend_File_Transfer_Adapter_Http();
					
					$schemaId = $this->_getParam('schemaId',0);
					$schemaId = filter_var($schemaId, FILTER_SANITIZE_NUMBER_INT);
					
					if ($upload->isValid() && !empty($schemaId))
					{
						$getAttFileInfo = $_FILES['fileToUpload'];
						$attTmpName      = $getAttFileInfo['tmp_name'];
						$filePathInfo  = pathinfo($getAttFileInfo['name']);
						
						$fileExtension = $filePathInfo['extension'];
						
						$isExist = file_exists($attTmpName);
						$attHeaderPair  = $this->_dbAttendance->getAttendanceHeaderData($schemaId);
						
						if($isExist)
						{
							$attimportRow = array();
							$attMapHeader = array();
							$importDataArr = array();
							
							if(!empty($attHeaderPair))
							{
								if($fileExtension == 'csv')
								{
									if (($handle = fopen($attTmpName, "r")) !== FALSE)
									{
										$csvHeader = fgetcsv($handle);
										
										if(!empty($csvHeader))
										{
											$headerExist = $this->_dbAttendance->attendanceFileHeader($csvHeader, $schemaId);
											
											if($headerExist == 0)
											{
												$this->_helper->redirector('attendanceimport', 'attendance', 'employees', array('import'=>0, 'msg'=>'valid'));
											}
											else
											{
												foreach($attHeaderPair as $k=>$c)
												{
													$attMapHeader[$k] = array_search($c, $csvHeader);
												}
												
												while (($data = fgetcsv($handle, 100000, ",")) !== FALSE)
												{
													if(!empty($data))
													{
														foreach($data as $key=>$val)
														{
															$rowId = isset($csvHeader[$key])?array_search($csvHeader[$key], $attHeaderPair):'';
															
															if(!empty($rowId))
															{
																if($rowId != 'Added_On')
																{
																	$attimportRow[$rowId] = (in_array($key, $attMapHeader) && $rowId)?$data[$key]:'';
																}
																else
																{
																	$attimportRow[$rowId] = (in_array($key, $atftMapHeader) && $rowId)?date('Y-m-d H:i:s',strtotime($data[$key])):'';
																}
															}
														}
													}
													
													$attimportRow['Schema_Id'] = $schemaId;

													// !empty row & 1exists
													if(!empty($attimportRow))
													{
														$attimportRow['Schema_Id'] = $schemaId;
														$importDataArr[] = $attimportRow;
													}
												}
											}
										}
										else
										{
											$this->view->result = array('success' => false, 'msg' => 'Upload only valid file', 'type' => 'info');
										}
										
										fclose($handle);
									}
								}
								else if($fileExtension == 'xls' || $fileExtension == 'xlsx')
								{
									try
									{
									//	require_once('phpexcel/PHPExcel/IOFactory.php');
									    require_once realpath(APPLICATION_PATH . '/../vendor/phpoffice/phpexcel/Classes/PHPExcel/IOFactory.php');
										
										if($fileExtension == 'xls')
											$inputFileType = 'Excel5';
										else
											$inputFileType = 'Excel2007';
										
										$objReader = PHPExcel_IOFactory::createReader($inputFileType);
										
										$objReader->setReadDataOnly(true);
										$objPHPExcel = $objReader->load($attTmpName);
										
										$objWorksheet = $objPHPExcel->getActiveSheet();
										$highestRow = $objWorksheet->getHighestRow(); // e.g. 10
										$highestColumn = $objWorksheet->getHighestColumn(); // e.g 'F'
										$highestColumnIndex = PHPExcel_Cell::columnIndexFromString($highestColumn); // e.g. 5
										$excelheader = array();
										
										for ($col = 0; $col <= $highestColumnIndex; ++$col)
										{
											$headerName = $objWorksheet->getCellByColumnAndRow($col, 1)->getValue();
											if(!empty($headerName))
												$excelheader[] = $headerName;
										}
										
										if(!empty($excelheader))
										{
											$headerExist = $this->_dbAttendance->attendanceFileHeader($excelheader, $schemaId);
											
											if($headerExist == 0)
											{
												$this->_helper->redirector('attendanceimport', 'attendance', 'employees', array('import'=>0, 'msg'=>'valid'));
											}
											else
											{
												$attendanceDateFormat = $this->_dbAttendance->attendanceDateFormat($schemaId);
												
												if(!empty($attendanceDateFormat))
												{
													$frmt = explode('#', $attendanceDateFormat);
													$format = $separator = array();
													
													foreach ($frmt as $k => $value)
													{
														if($k%2 == 0) {
															array_push($format, $value);
														}
														else {
															array_push($separator, $value);
														}
													}
													$pregStr = '/';
													$added = array();
													
													foreach ($separator as $sep)
													{
														if(!in_array($sep, $added)) {
															$pregStr .= '\\'.$sep.'|';
														}
														array_push($added, $sep);
													}
													$pregStr = substr($pregStr, 0, -1);
													$pregStr .= '/';
													$spIncluedInFormater = array_count_values($separator);
												}
								
												foreach($attHeaderPair as $k=>$c)
												{
													$attMapHeader[$k] = array_search($c, $excelheader);
												}
                                                
												for ($rows = 2; $rows <= $highestRow; ++$rows)
												{
                                                    for ($col = 0; $col <= $highestColumnIndex; ++$col)
													{
														$rowId = isset($excelheader[$col])?array_search($excelheader[$col], $attHeaderPair):'';
														
														if(!empty($rowId))
														{
															$cellValue = $objWorksheet->getCellByColumnAndRow($col, $rows)->getValue();
															
															if($rowId != 'Added_On')
															{
																$attimportRow[$rowId] = (in_array($col, $attMapHeader) && $rowId)?$cellValue:'';
															}
															elseif(in_array($rowId, array('Location_Id', 'Id_Number', 'WorkSchedule_Id', 'Card_No','Work_Place')))
															{
																$attimportRow[$rowId] = (int)$cellValue;
															}
															elseif(in_array($rowId, array('Employee_Id')))
															{
																$attimportRow[$rowId] = $cellValue;
															}
															else
															{
																if(in_array($col, $attMapHeader) && $rowId)
																{
																	$dateCellType = $objWorksheet->getCellByColumnAndRow($col, $rows)->getDataType();
																	
																	if(!empty($attendanceDateFormat))
																	{
																		if($dateCellType == 'n')
																		{
																			$attimportRow[$rowId] = PHPExcel_Style_NumberFormat::toFormattedString($objWorksheet->getCellByColumnAndRow($col, $rows)->getCalculatedValue(), 'Y-m-d H:i:s');
																		}
																		else
																		{
																			$dt = $cellValue;
																			
																			$output = preg_split($pregStr, $dt);
																			
																			$str = '';
																			$requireFormat = array('d','m','Y');
																			
																			foreach ($output as $i=>$val)
																			{
																				if(isset($requireFormat[$i]))
																				{
																					$k = array_search($requireFormat[$i], $format);
																					
																					if($i<2)//0,1
																					{
																						$str .= $output[$k].'-';
																					}
																					else if($i==2)
																					{
																						$str .= $output[$k].' ';
																					}
																					else if($i<5)
																					{//3,4
																						$str .= $output[$k].':';
																					}
																					else if($i<6)//5
																					{
																						$str .= $output[$k].' ';
																					}
																					else if($i==6)
																					{
																						$str .= $output[$k];
																					}
																				}
																			}
																			
																			if(isset($spIncluedInFormater[' ']))
																			{
																				preg_match_all('/\ /', $dt,$matches, PREG_OFFSET_CAPTURE);
																				$timeStartPos = $matches[0][$spIncluedInFormater[' ']][1]+1;
																			}
																			else
																			{
																				$timeStartPos = strpos($dt, ' ')+1;
																			}
																			
																			$attimportRow[$rowId] = date('Y-m-d',strtotime($str)).' '.date('H:i:s',strtotime(substr($dt,  $timeStartPos)));
																		}
																	}
																	else
																	{
																		if($dateCellType == 'n')
																		{
																			$attimportRow[$rowId] = date('Y-m-d H:i:s',PHPExcel_Shared_Date::ExcelToPHP($objWorksheet->getCellByColumnAndRow($col, $rows)->getValue()));
																		}
																		else
																		{
																			$attimportRow[$rowId] = date('Y-m-d H:i:s',strtotime($cellValue));
																		}
																	}
																}
																else {
																	$attimportRow[$rowId] =  '';
																}
															}
														}
													}
													
													$empBreakHours = $this->_dbAttendance->employeeBreakImport($attimportRow);

													// !empty row & 1exists
													if(!empty($attimportRow))
													{
														$attimportRow['Schema_Id']=$schemaId;
														$attimportRow['Exclude_Break_Hours']=$empBreakHours;
														$importDataArr[] = $attimportRow;
													}
												}
											}
										}
										else
										{
											$this->view->result = array('success' => false, 'msg' => 'Upload only valid file', 'type' => 'info');
										}
									}
									
									catch(Zend_Filter_Exception $fEx)
									{
										$this->view->result = array('success' => false, 'msg' => 'Upload only valid'. $fileExtension. ' file', 'type' => 'info');
									}
									
									catch(Zend_Exception $fEx)
									{
										$this->view->result = array('success' => false, 'msg' => 'Upload only valid'. $fileExtension. ' file', 'type' => 'info');
									}
								}
							}
							else
							{
								$this->_helper->redirector('attendanceimport', 'attendance', 'employees', array('import'=>0, 'msg'=>'valid'));
							}
						}
						else
						{
							$this->view->result = array('success' => false, 'msg' => 'Upload only valid file', 'type' => 'info');
						}
						
						if(!empty($importDataArr))
						{
							$inserted = $this->_dbAttendance->addAttendanceImport($importDataArr,$this->_logEmpId,$attendanceDateFormat);
							$this->view->result = $inserted;
						}
						else
						{
							//same set of record already imported
							$this->view->result = array('success' => false, 'msg' => 'Same set of file has been already imported', 'type' => 'warning');
						}
					}
					else
					{
						$this->view->result = array('success' => false, 'msg' => 'Invalid import file or scheme Id not exist', 'type' => 'info');
					}
				}
			}
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}
	}
	
	
	
	/**
	 * Map headers for attendance import
	 */
	public function mapheaderAction()
	{
		$this->_helper->layout()->disableLayout();
		
		if(isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
    		$ajaxContext->addActionContext('mapheader', 'json')->initContext();
			
			$fileExtension = $this->_getParam('extension', null);
			$fileExtension = filter_var($fileExtension, FILTER_SANITIZE_STRIPPED);
			
			$fileHeaderParam = $this->_getParam('fileHeader', 0);
			$fileHeaderParam = filter_var($fileHeaderParam, FILTER_SANITIZE_NUMBER_INT);
			
			$statusParam = $this->_getParam('statusParam', 0);
			$statusParam = filter_var($statusParam, FILTER_SANITIZE_NUMBER_INT);
			
			$schemaId = $this->_getParam('schemaId', 0);
			$schemaId = filter_var($schemaId, FILTER_SANITIZE_NUMBER_INT);
			
			$schemaName = $this->_getParam('schema', null);
			$schemaName = filter_var($schemaName, FILTER_SANITIZE_STRIPPED);
			
			$fileheader = array();
			$csvDataArr = array();
			$cellTypeArr = array();
			
			$fileName = $this->_ehrTables->uploadPath.'files/sampleAttendance.'.$fileExtension;
			$errMsg = 1;
			$isExist = file_exists($fileName);
			
			if(!empty($fileHeaderParam) && !empty($schemaName))
			{
				$errMsg = 0;
				
				if(empty($fileExtension) || !$isExist)
				{
					$errMsg = 1;
				}
				elseif($fileExtension == 'csv' && $isExist)
				{
					$errMsg = 0;
					$openFile = fopen($fileName,'r');
					$csvheader = fgetcsv($openFile);
					
					foreach($csvheader as $headerRow)
					{
						$fileheader[$headerRow] = $headerRow;
						$headerIndex[] = $headerRow;
					}
					
					if(isset($headerIndex))
					{
						while (($data = fgetcsv($openFile, 20000, ",", "'")) !== FALSE)
						{
							$csvDataArr = array_combine($headerIndex, $data);
							break;
						}
					}
					
					fclose($openFile);
				}
				elseif($isExist && ($fileExtension == 'xls' || $fileExtension == 'xlsx'))
				{
					$errMsg = 0;
				//	require_once('phpexcel/PHPExcel/IOFactory.php');
				require_once realpath(APPLICATION_PATH . '/../vendor/phpoffice/phpexcel/Classes/PHPExcel/IOFactory.php');
					
					if($fileExtension == 'xls')
						$inputFileType = 'Excel5';
					else
						$inputFileType = 'Excel2007';
						
					$objReader = PHPExcel_IOFactory::createReader($inputFileType);
					
					$objReader->setReadDataOnly(true);
					
					try
					{
						$objPHPExcel = $objReader->load($fileName);
						
						// throw new Zend_File_Transfer_Exception('sdfsdf');
						$objWorksheet = $objPHPExcel->getActiveSheet();
						$highestRow = $objWorksheet->getHighestRow(); // e.g. 10
						$highestColumn = $objWorksheet->getHighestColumn(); // e.g 'F'
						$excelHeader = array();
						$highestColumnIndex = PHPExcel_Cell::columnIndexFromString($highestColumn); // e.g. 5
						
						for ($col = 0; $col <= $highestColumnIndex; ++$col)
						{
							$headerName = $objWorksheet->getCellByColumnAndRow($col, 1)->getValue();//e.g. Employee_Id
							$csvData = $objWorksheet->getCellByColumnAndRow($col, 2)->getValue();//e.g. 5
							if(!empty($headerName))
							{
								$fileheader[$headerName] = $headerName;
								$csvDataArr[$headerName] = $csvData;
								$cellTypeArr[$headerName] = $objWorksheet->getCellByColumnAndRow($col, 2)->getDataType();
							}
						}
					}
					
					catch(PHPExcel_Reader_Exception $fEx)
					{
						$errMsg = 1;
					}
					
					catch(Zend_Controller_Exception $fEx)
					{
						$errMsg = 1;
					}
				}
				$getTableHeader = $this->_dbAttendance->tableHeader();
				
				if(!empty($fileheader) && !empty($getTableHeader) && $errMsg == 0 && !empty($csvDataArr))
				{
					$this->view->result = array('fileHeader' => $fileheader, 'tableHeader'=>$getTableHeader, 'csvDataArr'=>$csvDataArr,
												'cellType' => $cellTypeArr);
				}
			}
			elseif(!empty($statusParam) && !empty($schemaId))
			{
				$errMsg = 0;
				$statusFilename = $this->_dbAttendance->getAttendanceFileStatus($schemaId);
				$cntFileHeader = count(array_filter($this->_dbAttendance->getAttendanceHeaderData($schemaId)));
				$statusVal    = array();
				
				if(empty($fileExtension) || !$isExist)
				{
					$errMsg = 1; // upload valid file
				}
				elseif($fileExtension == 'csv' && $isExist)
				{
					$errMsg = 0;
					
					if (($handle = fopen($fileName, "r")) !== FALSE)
					{
						$csvHeader = fgetcsv($handle);
						$cntCsvheader = count($csvHeader);
						$statusIndex = array_search($statusFilename, $csvHeader);
						
						if(!empty($statusIndex))
						{
							$numCols = $cntCsvheader;
							if($cntFileHeader <= $cntCsvheader)
							{
								while (($data = fgetcsv($handle, 20000, ",", "'")) !== FALSE) {
									if(!empty($data[$statusIndex]))
									{
										$statusVal[$data[$statusIndex]] = $data[$statusIndex];
									}
								}
							}
							else
							{
								$errMsg = 1;
							}
						}
						else
						{
							$errMsg = 1;
						}
						fclose($handle);
					}
				}
				elseif($isExist && ($fileExtension == 'xls' || $fileExtension == 'xlsx'))
				{
					try
					{
						$errMsg = 0;
						//require_once('phpexcel/PHPExcel/IOFactory.php');
						require_once realpath(APPLICATION_PATH . '/../vendor/phpoffice/phpexcel/Classes/PHPExcel/IOFactory.php');
						
						if($fileExtension == 'xls')
							$inputFileType = 'Excel5';
						else
							$inputFileType = 'Excel2007';
						
						$objReader = PHPExcel_IOFactory::createReader($inputFileType);
						
						$objReader->setReadDataOnly(true);
						$objPHPExcel = $objReader->load($fileName);
						
						$objWorksheet = $objPHPExcel->getActiveSheet();
						$highestRow = $objWorksheet->getHighestRow(); // e.g. 10
						$highestColumn = $objWorksheet->getHighestColumn(); // e.g 'F'
						$excelHeader = array();
						$highestColumnIndex = PHPExcel_Cell::columnIndexFromString($highestColumn); // e.g. 5
						
						for ($col = 0; $col <= $highestColumnIndex; ++$col)
						{
							$headerName = $objWorksheet->getCellByColumnAndRow($col, 1)->getValue();
							
							if(!empty($headerName) && $headerName == $statusFilename)
								$statusIndex = $col;
						}
						
						if(isset($statusIndex))
						{
							for ($attimportRow = 2; $attimportRow <= $highestRow; ++$attimportRow)
							{
								$statusname = $objWorksheet->getCellByColumnAndRow($statusIndex, $attimportRow)->getValue();
								
								if(!empty($statusname))
									$statusVal[$statusname] = $statusname;
							}
						}
						else
						{
							$errMsg = 1;
						}
					}
					catch(Zend_Filter_Exception $fEx)
					{
						$errMsg = 1;
					}
					catch(Zend_Exception $fEx)
					{
						$errMsg = 1;
					}
				}
				
				if($errMsg == 0)
				{
					$statusVal = array_unique($statusVal);
					
					$this->view->result = array('success' => true, 'statusVal'=>$statusVal);
				}
			}
			
			if($errMsg == 1)
			{
				if(isset($schemaId))
				{
					$this->_dbAttendance->deleteSchema($schemaId, $this->_logEmpId);
				}
				
				$this->view->result = array('success' => false, 'msg'=>'Please, upload a valid '. $fileExtension. ' file...', 'type'=>'warning');
			}
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}
	}
	
	public function updateSchemaMapHeaderAction()
    {
		$this->_helper->layout()->disableLayout();
		
		if(isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
    		$ajaxContext->addActionContext('update-schema-map-header', 'json')->initContext();
			
			if ($this->getRequest()->isPost())
			{
				$formDataList = $this->getRequest()->getPost();
				
				$formData = $formDataList['dataLists'];
				$dateList = $formDataList['dateList'];
				$csvDataArr1 = $formDataList['csvArray'];
				$cellFormatArr = $formDataList['cellFormatArr'];
				
				$choosenDateFormat = array();
				
				for ($i=0;$i<3;$i++)
				{
					isset($dateList['DateFormat'.$i]) ? array_push($choosenDateFormat, $dateList['DateFormat'.$i]) : NULL;
					isset($dateList['DateSeperator']) ? array_push($choosenDateFormat, $dateList['DateSeperator']) : NULL;
				}
				
				$spIncluedInFormater = array_count_values($choosenDateFormat);
				$dtInvlidMsg = '';
				
				if(!isset($spIncluedInFormater['d']) || !isset($spIncluedInFormater['m'] ) || !isset($spIncluedInFormater['Y'] ) )
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid date format', 'type' => 'info');	
				}
				else
				{
					if($spIncluedInFormater['d'] > 1 || $spIncluedInFormater['m'] > 1 || $spIncluedInFormater['Y'] > 1)
					{
						$this->view->result = array('success' => false, 'msg' => 'Invalid date format', 'type' => 'info');	
					}
					else
					{
						if ( !empty($formDataList['schemaName']) && !empty($csvDataArr1))
						{
							$fileHeaderVal = array();
							$invalidDatatype = 0;
							
							$getTableHeader = $this->_dbAttendance->tableHeader();
							
							foreach ($csvDataArr1 as $key => $row)
							{
								$csvDataArr[str_replace(' ', '_',$key)] = $row;
							}
							
							foreach($getTableHeader as $tableheader)
							{
								$headerValue = $formData[$tableheader];
								
								if((in_array($tableheader, array('Location_Id', 'Id_Number', 'Card_No', 'WorkSchedule_Id')) &&
									(empty($csvDataArr[$headerValue]) || (int)$csvDataArr[$headerValue]>=0)) ||
								   ($tableheader == 'Employee_Id' && !empty($csvDataArr[$headerValue]) && (int)$csvDataArr[$headerValue]>0) ||
								   ($tableheader == 'Added_On' && !empty($csvDataArr[$headerValue]) /*&&*/
									/*(($cellFormatArr[$headerValue] == 'n' && ($csvDataArr[$headerValue])>0) || strtotime($csvDataArr[$headerValue])>0)*/) ||
								   (in_array($tableheader, array('Attendance_Status')) && !empty($csvDataArr[$headerValue])) ||
								   (in_array($tableheader, array('Employee_Name', 'Department', 'Verify_Code'))))
								{
									$headerValue1 = str_replace('_',' ',$headerValue);
									$fileHeaderVal[$tableheader] = $headerValue1;
								}
								else
								{
									$invalidDatatype+=1;
								}
							}
							
							$fileHeaderVal['Schema_Name'] = $formDataList['schemaName'];
							
							if($invalidDatatype > 0 )
							{
								$this->view->result = array('success' => false, 'msg' => 'Data type for the columns Employee Id, Location Id, WorkSchedule Id, Id Number and Card No should be interger and Added On in datatime format', 'type' => 'warning');
							}
							else if(count(array_filter($fileHeaderVal)) != count(array_unique(array_filter($fileHeaderVal))))
							{
								$this->view->result = array('success' => false, 'msg' => 'Duplicate value exists', 'type' => 'warning');
							}
							else
							{
								$fileHeaderVal['Date_Format'] = implode('#', $choosenDateFormat);
								
								$this->view->result = $this->_dbAttendance->addAttendanceFileHeader($fileHeaderVal, $this->_logEmpId);
							}
						}
						else
						{
							$this->view->result = array('success' => false, 'msg' => 'Scheme name or mapping field not found', 'type' => 'info');
						}
					}
					
				}
			}
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}
    }
	
	//  add/update status pair
	public function updateFieldMappingAction()
    {
        $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('update-field-mapping', 'json')->initContext();
			
            $schemaId = $this->_getParam('schemaId', 0);
            $schemaId = filter_var($schemaId, FILTER_SANITIZE_NUMBER_INT);
            
			if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				
				if ( !empty($formData['checkIn']) && !empty($formData['checkOut']) && !empty($schemaId))
				{
					$mappingArray = array('Schema_Id'    => $schemaId,
										  'Status_PairA' => $formData['checkIn'],
										  'Status_PairB' => $formData['checkOut']);
					
					$oldMappingValue = array('statusPairA' => $formData['statusPairA'],
											 'statusPairB' => $formData['statusPairB']);
					
					$this->view->result = $this->_dbAttendance->updateStatusPair($mappingArray, $oldMappingValue);
				}
				else
				{
					$this->view->result = array('success'=>false, 'msg'=>'Invalid status pair values', 'type'=>'warning');
				}
			}
		}
        else
        {
            $this->_helper->redirector('index', 'attendance', 'employees');
        }
	}
	
	// delete status pair
	public function deleteFieldMappingAction()
    {
        $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-field-mapping', 'json')->initContext();
			
			if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				
				if (!empty($formData['schemaId']) && $formData['schemaId'] > 0 && !empty($formData['checkIn']) && !empty($formData['checkOut']) )
				{
					$this->view->result = $this->_dbAttendance->deleteStatusPair($formData, $this->_logEmpId);
				}
				else
				{
					$this->view->result = array('success'=>false, 'msg'=>'Invalid data', 'type'=>'info');
				}
			}
		}
        else
        {
            $this->_helper->redirector('index', 'attendance', 'employees');
        }
    }
	
	/**
	 * Get schema details by schemaId
	 * to list in grid
	 */
	public function statuspairsAction()
	{
		$this->_helper->layout()->disableLayout('layout');
		
		if(isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('statuspairs', 'json')->initContext();
			
			if($this->_importAccessRights['Employee']['View']==1)
			{
				$sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
                
				$schemaId = $this->_getParam('schemaId', null);
				$schemaId = filter_var($schemaId, FILTER_SANITIZE_NUMBER_INT);
				
				//to display all the attendance records in the data grid
				$this->view->result = $this->_dbAttendance->listStatusPair($page, $rows, $sortField, $sortOrder, $schemaId);
            }
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}
	}
	
	/**
	 * Delete schema
	 */
	public function deleteSchemaAction()
	{
		$this->_helper->layout()->disableLayout();
		
		if(isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('delete-schema', 'json')->initContext();
			
			$schemaId = $this->_getParam('schemaId', null);
			$schemaId = filter_var($schemaId, FILTER_SANITIZE_NUMBER_INT);
			
			if( !empty($schemaId))
			{
				$this->view->result = $this->_dbAttendance->deleteSchema($schemaId, $this->_logEmpId);
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Invalid Data', 'type' => 'info');	
			}
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}
	}

	/**
	 * Get attendance details , lock , comments by employeeId and date.
	 */
	public function attendanceEmployeeAction()
	{
		$this->_helper->layout->disableLayout();
		if(isset($_SERVER['HTTP_REFERER']))
		{
			if ($this->_checkSession )
			{
				$ajaxContext = $this->_helper->getHelper('AjaxContext');
				$ajaxContext->addActionContext('attendance-employee', 'json')->initContext();
				
				$employeeId = $this->_getParam('employee', null);
				$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
				$date = $this->_getParam('date', null);
				$date = filter_var($date, FILTER_SANITIZE_STRIPPED);
				
				if(!empty($employeeId) && !empty($date))
				{
					//to fetch the attendance record of an employee based on date
					$result = $this->_dbAttendance->getEmpAttendanceByDate($employeeId, $date);

					if ($result['Status'] == "Draft" && count($result) > 0)
					{
						$result['lockatt'] = $result['Attendance_Id'];
						$checkFormLock = $result['Lock_Flag'];
						if($checkFormLock == 0) // if lockflag is set to 0 or his logId, employee can access the form
						{
							$setStypeLock = $this->_dbAccessRights->setLockFlag($this->_logEmpId, $result['Attendance_Id'], $this->_ehrTables->attendance, 'Attendance_Id');

							//User can acccess the form if lockflag is updated. Otherwise Logout
							if($setStypeLock)
							{
								$cntComment = $this->_dbComment->countComment($result['Attendance_Id'], $this->_formNameA);
								$result['CommentExists'] = $cntComment;

								if ($result['Employee_Id'] != $this->_logEmpId && $result['Added_By'] != $this->_logEmpId)
								{
									$result['msg'] = "Access Denied...!";
								}
								$result = Zend_Json::encode($result);
								$this->view->response = $result;
							}
						}

						else if($checkFormLock == $this->_logEmpId)
						{
							$result['Log'] = $this->_logEmpId;
							$result['Lock'] = $checkFormLock;
							$result['msg']="<div style='font-size:14px;width:250px;'>Same record has been opened by your session in some other browser or system.<br/>
							If you still having problem in opening the form, contact System Admin</div>";
							$this->view->response = Zend_Json::encode($result);
						}
						else
						{
							$result['Log'] = $this->_logEmpId;
							$result['Lock'] = $checkFormLock;
							$editEmpName = $this->_dbPersonal->employeeName($checkFormLock);
							$result['msg']=$editEmpName['Employee_Name'] . ' is updating this record.<br/> Choose some other employee...';
							$this->view->response = Zend_Json::encode($result);
						}
					}

				}
			}
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}
	}

	public function attendanceBoxAction()
    {
        $this->_helper->layout()->disableLayout();
		if(!empty($this->_attendanceBoxRights['Employee']['Update']) && 
		($this->_attendanceBoxRights['Employee']['Is_Manager'] || $this->_attendanceBoxRights['Admin']))	
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('attendance-box', 'json')->initContext();
			
			$isDate = $this->_getParam('isDate', null);
			$isDate = filter_var($isDate, FILTER_SANITIZE_STRIPPED);
			
			if ($isDate)
			{
				$this->view->isDate = true;
			}
			else
			{
				$this->view->baseUrl = $this->_basePath->baseUrl();
				$this->view->orgName = $this->_ehrTables->organizationName();
			}
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}		
    }
	
	public function getEmployeeDetailsAction()
    {
        $this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
		{
			if($this->_hrappMobile->checkAuth())
			{
				$ajaxContext = $this->_helper->getHelper('AjaxContext');
				$ajaxContext->addActionContext('get-employee-details', 'json')->initContext();
				
				$isAction = $this->_getParam('isAction', null);
				$isAction = filter_var($isAction, FILTER_SANITIZE_STRIPPED);

				$attendanceBoxAccess = array('Is_Manager'=>$this->_attendanceBoxRights['Employee']['Is_Manager'],
											 'Update'	 =>$this->_attendanceBoxRights['Employee']['Update'],
											 'Admin'	 =>$this->_attendanceBoxRights['Admin'],
											 'LogId'	 =>$this->_logEmpId);
				
				switch ($isAction)
				{
					
					case 'Check Employee':
						$employeeId = $this->_getParam('EmployeeId', null);
						$employeeId = $this->_dbAttendance->getEmployeeId($employeeId);
						if ($employeeId > 0)
						{
							$empDetails = $this->_dbAttendance->getEmployeeDetails($employeeId);
							$dbEmployee = new Employees_Model_DbTable_Employee();
							
							$profilePath = $dbEmployee->getEmpPhotoPath($employeeId);
							$imagePath      = $this->_ehrTables->uploadPath.'images/';
							
							if(!empty($profilePath))
							{
								$profilePicture=$imagePath.$profilePath;
								$defaultImage='';
							}
							else
							{
								$profilePicture ='';
								$defaultImage= $this->_basePath->baseUrl('images/defaultPhot.jpg');
							
							}
						$this->view->result = Zend_Json::encode(array('success'=>true, 'empDet'=>$empDetails,'defaultImage'=>$defaultImage, 'photo'=>$profilePicture));
						}
						else
						{
							$this->view->result = Zend_Json::encode(array('success'=>false, 'msg'=>'Invalid Employee Id', 'type'=>'info'));
						}
						break;
					
					case 'Present Employees':
						$startDate 				= date("Y-m-d");
						$endDate 				= date("Y-m-d");
						$checkDetails 			= $this->_dbAttendance->checkPresentEmployees($startDate,$endDate,$attendanceBoxAccess);
						$presents 				= $this->checkPicturePath ($checkDetails['Presents']);
						$absentees 				= $this->checkPicturePath ($checkDetails['Absentees']);
						$timeoffEmployees 		= $this->checkPicturePath ($checkDetails['TimeOff']);
						$shiftNotScheduled 		= $this->checkPicturePath ($checkDetails['ShiftNotScheduled']);
						$shortTimeOff 		    = $this->checkPicturePath ($checkDetails['ShortTimeOff']);

						$presentEmployeeCount 	= count($checkDetails['Presents']); 
						$absentEmployeeCount  	= count($checkDetails['Absentees']); 
						$timeOffEmployeeCount 	= count($checkDetails['TimeOff']); 
						$leaveEmployeeCount 	= count($checkDetails['Leave']); 
						$compOffEmployeeCount 	= count($checkDetails['CompOff']); 
						$shiftNotScheduledEmployeeCount = count($checkDetails['ShiftNotScheduled']);
						$shortTimeOffEmployeeCount = count($checkDetails['ShortTimeOff']);

						$this->view->result 	= Zend_Json::encode(array('success'=>true,'presentCount'=>$presentEmployeeCount,'absentCount'=>$absentEmployeeCount,
																	'timeOffCount'=>$timeOffEmployeeCount,'leaveCount'=>$leaveEmployeeCount,
																	'compOffCount'=>$compOffEmployeeCount,'shiftNotScheduledCount'=>$shiftNotScheduledEmployeeCount,
																	'empPresents'=>$presents, 'empAbsentees'=>$absentees,
																	'shortTimeOffEmployees'=>$shortTimeOff, 'shortTimeOffEmployeeCount'=>$shortTimeOffEmployeeCount,

																	'timeoffEmployees'=>$timeoffEmployees,'shiftNotScheduled'=>$shiftNotScheduled));
						break;
			
					case 'Team Availability':
						$endDate 				 = date("Y-m-d");
						$startDate 				 = date('Y-m-d', strtotime("-6 day", strtotime($endDate)));
						$checkDetails 			 = $this->_dbAttendance->checkPresentEmployees($startDate,$endDate,$attendanceBoxAccess);
						$presentEmployeeDetails  = array_count_values(array_column($checkDetails['Presents'],'Start_Date'));
						$absentEmployeeDetails   = array_count_values(array_column($checkDetails['Absentees'],'Start_Date'));
						$timeOffEmployeeDetails  = array_count_values(array_column($checkDetails['TimeOff'],'Start_Date'));
						$presentAbsentCountList  = array();
						while($startDate <= $endDate)
						{
							if(isset($presentEmployeeDetails[$startDate]))
							{
                              $presentCount = $presentEmployeeDetails[$startDate];
							}
							else
							{
								$presentCount = 0;
							}

							if(isset($absentEmployeeDetails[$startDate]))
							{
                              $absentCount = $absentEmployeeDetails[$startDate];
							}
							else
							{
							  $absentCount = 0;
							}

							if(isset($timeOffEmployeeDetails[$startDate]))
							{
                              $timeOffCount = $timeOffEmployeeDetails[$startDate];
							}
							else
							{
							  $timeOffCount = 0;
							}

							$presentAbsentCount = array('date'=>$startDate,
														'presentCount'	=> $presentCount,
														'absentCount'	=> $absentCount,
														'timeOffCount'	=> $timeOffCount);

							array_push($presentAbsentCountList, $presentAbsentCount);
							$startDate = date('Y-m-d', strtotime("+1 day", strtotime($startDate)));
						}

						$this->view->result = Zend_Json::encode(array('success'=>true,'presentAbsentCountList'=>$presentAbsentCountList));
						break;

					case 'Check_Attendance':
						$checkAtt = $this->_dbAttendance->checkEmployeeAttendance($this->_logEmpId);
						/*we need to return the attendanceId,PunchoutDate and Timer details as well*/
						if(!empty($checkAtt))
						{
							$checkAtt = $checkAtt;
						}
						else 
						{
							$checkAtt = '';
						}
						
						$this->view->result = Zend_Json::encode($checkAtt);
						break;
				}
		 	}
		    else
			{
				$formData = array();
				$requestData =$this->getRequest()->getRawBody();
				if ($requestData)
				{
					$formData = Zend_Json::decode($requestData);
				}
				//if session expired, check for requestResource in header.If requestResource is HRAPP,
				// then return error response otherwise redirect to auth page
				if(empty($formData)|| (!empty($formData) && $formData['requestResource'] !== 'HRAPPUI')){
						if (Zend_Session::namespaceIsset('lastRequest'))
						Zend_Session:: namespaceUnset('lastRequest');
					
						$session = new Zend_Session_Namespace('lastRequest');
						$session->lastRequestUri = 'v3/employee-self-service/attendance';
						$this->_redirect('auth');
				}
				else{
					$this->view->result = Zend_Json::encode(array('success'=>false, 'msg'=>'Session Expired', 'type'=>'danger'));

				}
			}
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}
    }
	
	public function checkPicturePath ($empArr)
	{
		foreach ($empArr as $key => $row)
		{
			$profilePath = $row['Photo_Path'];
			
			$profilePicture = $this->_basePath->baseUrl('images/defaultPhot.jpg');
			
			if (!empty($profilePath))
			{
				$profilePicture = $this->_ehrTables->uploadPath.'images/'.$profilePath;//.$employeeId.'.jpg';
				
				if (file_exists($this->_ehrTables->filePath.$profilePicture))
				{
					$profilePicture = $this->_basePath->baseUrl($profilePicture);
				}
			}
			
			$empArr[$key]['Photo'] = $profilePicture;
		}
		
		return $empArr;
	}
	
	public function getEmpAttendanceByDateAction()
    {
     	$this->_helper->layout->disableLayout();
	
		if(isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('get-emp-attendance-by-date', 'json')->initContext();
		
			$employeeId = $this->_getParam('employee', null);
			$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
			
			$date = $this->_getParam('date', null);
			$date = filter_var($date, FILTER_SANITIZE_STRIPPED);
			
			if(!empty($employeeId) && !empty($date))
			{
				//to fetch the attendance record of an employee based on date
				$this->view->result = $this->_dbAttendance->getEmpAttendanceByDate($employeeId, $date);
			}
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}
	}
	
	
    /********************************************* Attendance Settings ********************************************************/
	
	//list attendance settings data
	public function listAttendanceSettingsAction()
    {
        $this->_helper->layout()->disableLayout();
		
		if(isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('list-attendance-settings', 'json')->initContext();
			
			if($this->_attendanceSettingsAccessRights['Employee']['View']==1)
			{
				$sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
                
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$workScheduleId = $this->_getParam('workSchedule', null);
				$workScheduleId = filter_var($workScheduleId, FILTER_SANITIZE_STRIPPED);
				
				$delayedEntryStart = $this->_getParam('delayedEntryStart', null);
				$delayedEntryStart = filter_var($delayedEntryStart, FILTER_SANITIZE_STRIPPED);
				
				$delayedEntryEnd = $this->_getParam('delayedEntryEnd', null);
				$delayedEntryEnd = filter_var($delayedEntryEnd, FILTER_SANITIZE_STRIPPED);

				$delayedEntryUptoStart = $this->_getParam('delayedEntryUptoStart', null);
				$delayedEntryUptoStart = filter_var($delayedEntryUptoStart, FILTER_SANITIZE_STRIPPED);
				
				$delayedEntryUptoEnd = $this->_getParam('delayedEntryUptoEnd', null);
				$delayedEntryUptoEnd = filter_var($delayedEntryUptoEnd, FILTER_SANITIZE_STRIPPED);
				
				$delayedEntryMaxLimitStart = $this->_getParam('delayedEntryMaxLimitStart', null);
				$delayedEntryMaxLimitStart = filter_var($delayedEntryMaxLimitStart, FILTER_SANITIZE_STRIPPED);
				
				$delayedEntryMaxLimitEnd = $this->_getParam('delayedEntryMaxLimitEnd', null);
				$delayedEntryMaxLimitEnd = filter_var($delayedEntryMaxLimitEnd, FILTER_SANITIZE_STRIPPED);
				
				$maxLimitPeriod = $this->_getParam('maxLimitPeriod', null);
				$maxLimitPeriod = filter_var($maxLimitPeriod, FILTER_SANITIZE_STRIPPED);
				
				$leaveType = $this->_getParam('$leaveType', null);
				$leaveType = filter_var($leaveType, FILTER_SANITIZE_STRIPPED);
				
				$totalDaysStart = $this->_getParam('totalDaysStart', null);
				$totalDaysStart = filter_var($totalDaysStart, FILTER_SANITIZE_STRIPPED);
				
				$totalDaysEnd = $this->_getParam('totalDaysEnd', null);
				$totalDaysEnd = filter_var($totalDaysEnd, FILTER_SANITIZE_STRIPPED);
				
				$attendanceSettingStatus = $this->_getParam('attendanceSettingStatus', null);
				$attendanceSettingStatus = filter_var($attendanceSettingStatus, FILTER_SANITIZE_STRIPPED);
				
				$searchArr = array( 'workScheduleId'            => $workScheduleId,
								    'delayedEntryStart'         => $delayedEntryStart,
									'delayedEntryEnd'           => $delayedEntryEnd,
									'delayedEntryUptoStart'     => $delayedEntryUptoStart,
									'delayedEntryUptoEnd'       => $delayedEntryUptoEnd,
									'delayedEntryMaxLimitStart' => $delayedEntryMaxLimitStart,
									'delayedEntryMaxLimitEnd'   => $delayedEntryMaxLimitEnd,
									'maxLimitPeriod'            => $maxLimitPeriod,
									'leaveType'                 => $leaveType,
									'totalDaysStart'            => $totalDaysStart,
									'totalDaysEnd'              => $totalDaysEnd,
									'status'                    => $attendanceSettingStatus);
				
				//to display all the attendance settings records in the data grid
				$this->view->result = $this->_dbAttendance->listAttendanceSettings($page, $rows, $sortField, $sortOrder, $searchAll,
																				   $searchArr);
            }
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}
	}

	/**
     *  updateAttendanceSettingsAction() used to add/update attendance settings details
    */
	public function updateAttendanceSettingsAction()
    {
        $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('update-attendance-settings', 'json')->initContext();
			
            $attendanceSettingsId = $this->_getParam('attendanceSettingsId', 0);
			$attendanceSettingsId = filter_var($attendanceSettingsId, FILTER_SANITIZE_NUMBER_INT);
			$formData = $this->getRequest()->getPost();
			
			if ((empty($attendanceSettingsId) && $this->_attendanceSettingsAccessRights['Employee']['Add'] == 1) ||
				(!empty($attendanceSettingsId) && $this->_attendanceSettingsAccessRights['Employee']['Update'] == 1))
			{
				if ($this->getRequest()->isPost())
				{
					$formData = $this->getRequest()->getPost();

					if(!empty($formData['leaveTypeId']))
					{
						$leaveTypesArray	=	$formData['leaveTypeId'];
						$isFormValid;
						$fullDayHoursFrom = $formData['fullDayHoursFrom'];
						$fullDayHoursTo = $formData['fullDayHoursTo'];
						$halfDayHoursFrom = $formData['halfDayHoursFrom'];
						$halfDayHoursTo = $formData['halfDayHoursTo'];
						$quarterDayHoursFrom = $formData['quarterDayHoursFrom'];
						$quarterDayHoursTo = $formData['quarterDayHoursTo'];
						
						if($formData['configurationType'] === "Attendance Shortage") {
							$fullDayHoursFrom = $this->_dbCommonFunction->hoursToDecimal($formData['fullDayHoursFrom']);
							$fullDayHoursTo = $this->_dbCommonFunction->hoursToDecimal($formData['fullDayHoursTo']);
							$halfDayHoursFrom = $this->_dbCommonFunction->hoursToDecimal($formData['halfDayHoursFrom']);
							$halfDayHoursTo = $this->_dbCommonFunction->hoursToDecimal($formData['halfDayHoursTo']);
							$quarterDayHoursFrom = (!empty($formData['quarterDayHoursFrom'])) ? $this->_dbCommonFunction->hoursToDecimal($formData['quarterDayHoursFrom']) : new Zend_Db_Expr('NULL');;
							$quarterDayHoursTo =  (!empty($formData['quarterDayHoursTo']))  ?$this->_dbCommonFunction->hoursToDecimal($formData['quarterDayHoursTo']) : new Zend_Db_Expr('NULL');;
							
							$isFormValid = !empty($formData['customGroupId'])
								&& ($formData['shiftMargin'] == 'Yes' || $formData['shiftMargin'] == 'No')
								&& count($leaveTypesArray) > 0 && !empty($formData['alternateLeaveTypeId'])
								&& !empty($fullDayHoursFrom) && !empty($fullDayHoursTo) && !empty($halfDayHoursFrom) && !empty($halfDayHoursTo);
						} else {
							$isFormValid = !empty($formData['workscheduleId']) && ($formData['delayedEntry'] >=1 && $formData['delayedEntry'] <= 240) &&
							($formData['delayedEntryUpto'] >=1 && $formData['delayedEntryUpto'] <= 240) &&
							(($formData['delayedEntryPeriod'] == "Per Month" && $formData['delayedEntryMaxLimit'] <= 31) ||
							($formData['delayedEntryPeriod'] == "Per Year" && $formData['delayedEntryMaxLimit'] <= 366)) && 
							$formData['delayedEntryMaxLimit'] >= 0 && ($formData['totalDays'] == 0.25  || $formData['totalDays'] == 0.5 || $formData['totalDays'] == 1) &&
							count($leaveTypesArray) >0 && !empty($formData['alternateLeaveTypeId']);
						}

						if ($isFormValid) {
							if ($formData['configurationType'] === "Attendance Shortage" || 
								(intVal($formData['delayedEntryUpto']) > intVal($formData['delayedEntry'])) //If delayed entry upto minutes is less than or equal to delayedEntryFromMinutes
							) {
								$attendanceSettingArray = array('Attendance_Settings_Id'             => $attendanceSettingsId,
																'WorkSchedule_Id'                    => $formData['workscheduleId'],
																'Configuration_Type'				 => $formData['configurationType'],
																'Custom_Group_Id'					 => $formData['customGroupId'],
																'Shift_Margin'					 	 => $formData['shiftMargin'],
																'Shortage_From_For_Full_Day_Leave'	 => $fullDayHoursFrom,
																'Shortage_To_For_Full_Day_Leave'	 => $fullDayHoursTo,
																'Shortage_From_For_Half_Day_Leave'	 => $halfDayHoursFrom,
																'Shortage_To_For_Half_Day_Leave'     => $halfDayHoursTo,
																'Shortage_From_For_Quarter_Day_Leave'=> $quarterDayHoursFrom,
																'Shortage_To_For_Quarter_Day_Leave'	 => $quarterDayHoursTo,
																'Delayed_Entry_From'                 => $formData['delayedEntry'],
																'Delayed_Entry_Upto'                 => $formData['delayedEntryUpto'],
																'Delayed_Entry_Maximum_Limit'        => $formData['delayedEntryMaxLimit'],
																'Delayed_Entry_Maximum_Limit_Period' => $formData['delayedEntryPeriod'],
																'Late_Attendance_Leave_Frequency'    => $formData['lateAttendanceLeaveFrequency'],
																'Auto_Short_Time_Off'                => $formData['autoShortTimeOff'],
																'Total_Days'                         => $formData['totalDays'],
																'Alternate_LeaveType_Id'             => $formData['alternateLeaveTypeId'],
																'Enable_Notification'                => $formData['enableNotification'],
																'Attendance_Settings_Status'         => $formData['attendanceSettingsStatus']);
								
								$notificationArray = array('Employee_Id' => (empty($formData['notificationPerson']) || sizeof($formData['notificationPerson']) > 1)? $formData['notificationPerson'] : array_filter($formData['notificationPerson']));
								
								$customFormName = $this->_ehrTables->getCustomForms($this->_formNameC);
								$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameC);
								
								$this->view->result = $this->_dbAttendance->updateAttendanceSettings($attendanceSettingArray, $leaveTypesArray, $notificationArray, $this->_logEmpId, $formName);
							}else{
								$this->view->result = array('success'=>false, 'msg'=>'Delayed entry to minutes should be greater than the delayed entry from minutes', 'type'=>'warning');
							}
						}
						else
						{
							$this->view->result = array('success'=>false, 'msg'=>'Invalid Data', 'type'=>'warning');
						}
					}
					else
					{
						$this->view->result = array('success'=>false, 'msg'=>'Select atleast one leave type.', 'type'=>'warning');
					}
				}
			}
            else
            {
				$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'info');
            }
        }
        else
        {
            $this->_helper->redirector('index', 'attendance', 'employees');
        }
    }
	
	/**
     *  deleteAttendanceSettingsAction() used to delete attendance settings details
    */
	public function deleteAttendanceSettingsAction()
    {
        $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-attendance-settings', 'json')->initContext();
            
			if( $this->_attendanceSettingsAccessRights['Employee']['Delete'] == 1 )
			{
				$attendanceSettingsId = $this->_getParam('attendanceSettingsId', null);
				$attendanceSettingsId = filter_var($attendanceSettingsId, FILTER_SANITIZE_NUMBER_INT);
				
				if (!empty($attendanceSettingsId) && $attendanceSettingsId > 0 )
				{
					$customFormName = $this->_ehrTables->getCustomForms($this->_formNameC);
					$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameC);
						
					$this->view->result = $this->_dbAttendance->deleteAttendanceSettings($attendanceSettingsId, $this->_logEmpId, $formName);
				}
				else
				{
					$this->view->result = array('success'=>false, 'msg'=>'Invalid data', 'type'=>'info');
				}
			}
			else
			{
				$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'warning');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'attendance', 'employees');
        }
    }
	
	/**
	 *setBreakHoursAction() used to update break hours
      **/
	public function setBreakHoursAction()
    {
		$this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
		{
			
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('set-break-hours', 'json')->initContext();
			if ($this->getRequest()->isPost())
			{
				    $formData = $this->getRequest()->getPost();
					$attendanceIdArr = $formData['attendanceIdArr'];
					$statusId        = $formData['statusId'];
					$statusIdArray   = array('Exclude_Break_Hours'=>$statusId);
					$this->view->result = $this->_dbAttendance->setBreakHours($attendanceIdArr,$statusIdArray);
		   
			}
		   
		else
        {
            $this->_helper->redirector('index', 'attendance', 'employees');
        }
    }
 }
/**
	 *empBreakHoursAction() used to prefill the  break hours value while adding attendance in attendance form
      **/
 public function empBreakHoursAction()
    {
        $this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
		{
			
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('emp-break-hours', 'json')->initContext();
			if ($this->getRequest()->isPost())
			{
				    $formData = $this->getRequest()->getPost();
					$employeeId = $formData['employeeId'];
					$this->view->result = $this->_dbAttendance->getEmpBreakDetails($employeeId);
		   
			}
		   
			else
			{
				$this->_helper->redirector('index', 'attendance', 'employees');
			}
        }
	}
	
	//list device details
	public function listDeviceDetailsAction()
    {
        $this->_helper->layout()->disableLayout();
		
		if(isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('list-device-details', 'json')->initContext();
			
			if($this->_deviceManagementAccessRights['Employee']['View']==1)
			{
				$sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);

				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
                
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$deviceName = $this->_getParam('deviceName', null);
				$deviceName = filter_var($deviceName, FILTER_SANITIZE_STRIPPED);
				
				$machineType = $this->_getParam('machineType', null);
				$machineType = filter_var($machineType, FILTER_SANITIZE_STRIPPED);

				$connectionType = $this->_getParam('connectionType', null);
				$connectionType = filter_var($connectionType, FILTER_SANITIZE_STRIPPED);

				$serialNumber = $this->_getParam('serialNumber', null);
				$serialNumber = filter_var($serialNumber, FILTER_SANITIZE_STRIPPED);
				
				$ipAddress = $this->_getParam('ipAddress', null);
				$ipAddress = filter_var($ipAddress, FILTER_SANITIZE_STRIPPED);
				
				$port = $this->_getParam('port', null);
				$port = filter_var($port, FILTER_SANITIZE_STRIPPED);

				$timeout = $this->_getParam('timeout', null);
				$timeout = filter_var($timeout, FILTER_SANITIZE_STRIPPED);
				
				$attendanceParser = $this->_getParam('attendanceParser', null);
				$attendanceParser = filter_var($attendanceParser, FILTER_SANITIZE_STRIPPED);
				
				
				$searchArr = array( 'deviceName'          => $deviceName,
									'machineType'         => $machineType,
									'connectionType'      => $connectionType,
									'ipAddress'           => $ipAddress,
									'port' 				  => $port
									);			
									
				//to display all the device records in the data grid
				$this->view->result = $this->_dbAttendance->listDeviceDetails($page, $rows, $sortField, $sortOrder, $searchAll,
																				   $searchArr);
            }
		}
		else
		{
			$this->_helper->redirector('index', 'attendance', 'employees');
		}
	}

	// update device details action
	public function updateDeviceDetailsAction()
	{
		$this->_helper->layout()->disableLayout();
        if (isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('update-device-details', 'json')->initContext();
			
            $deviceId = $this->_getParam('deviceId', 0);
			$deviceId = filter_var($deviceId, FILTER_SANITIZE_NUMBER_INT);
            
			if ((empty($deviceId) && $this->_deviceManagementAccessRights['Employee']['Add'] == 1) ||
				(!empty($deviceId) && $this->_deviceManagementAccessRights['Employee']['Update'] == 1))
			{
				if ($this->getRequest()->isPost())
				{
					$formData = $this->getRequest()->getPost();

					$deviceName  	  = $this->_validation->onlyLetterNumberValidation($formData['deviceName']);
					$serialNumber     = $this->_validation->onlyLetterNumberValidation($formData['serialNumber']);
					$port     		  = $this->_validation->intValidation($formData['port']);
					$inport           = $this->_validation->intValidation($formData['inport']);
					$timeout          = $this->_validation->intValidation($formData['timeout']);
					$attendanceParser = $this->_validation->alphaNumSpDotValidation($formData['attendanceParser']);
					$connectionType   = $this->_validation->alphaNumValidation($formData['connectionType']);
					$machineType      = $this->_validation->alphaNumValidation($formData['machineType']);

					
					$deviceName['valid']  		= $this->_validation->lengthValidation($deviceName, 3, 50, false);
					$serialNumber['valid']		= $this->_validation->lengthValidation($serialNumber, 1, 50, false);
					$port['valid']        		= $this->_validation->lengthValidation($port, 4, 6, false);
					$inport['valid']      		= $this->_validation->lengthValidation($inport, 4, 6, false);
					$timeout['valid']     		= $this->_validation->lengthValidation($timeout, 0, 9, false);
				
					//$ipAddress = $this->_validation->IpAddressValidation($formData['ipAddress']);


					if ((!empty($deviceName['value']) && !empty($deviceName['valid'])) 
					//&& (!empty($ipAddress['value']) && $ipAddress['valid'])
					&& (!empty($port['value']) && $port['valid']) &&  (empty($inport['value']) || (!empty($inport['value']) && $inport['valid']))
					&& (!empty($timeout['value']) && $timeout['valid']) &&  (empty($attendanceParser['value']) || (!empty($attendanceParser['value'])))
					&& (empty($connectionType['value']) || (!empty($connectionType['value']))) && (!empty($machineType['value'])))
					{
						$deviceManagementArray = array('Device_Id'             				 => $deviceId,
														'Device_Name'                    	 => $formData['deviceName'],
														'Serial_Number'                      => $formData['serialNumber'],
														'IP_Address'        				 => $formData['ipAddress'],
														'Port' 								 => $formData['port'],
														'Inport'                       		 => $formData['inport'],
														'Timeout'                            => $formData['timeout'],
														'Attendance_Parser'             	 => $formData['attendanceParser'],
														'Connection_Type'                	 => $formData['connectionType'],
														'Machine_Type'        				 => $formData['machineType']);
						
														$customFormName = $this->_ehrTables->getCustomForms($this->_formNameE);
						$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameE);
						$this->view->result = $this->_dbAttendance->updateDeviceDetails($deviceManagementArray, $this->_logEmpId, $formName);
					}
					else
					{
						$this->view->result = array('success'=>false, 'msg'=>'Invalid Data', 'type'=>'warning');
					}
				}
			}
            else
            {
				$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'info');
            }
        }
        else
        {
            $this->_helper->redirector('index', 'attendance', 'employees');
        }
	}

	/**
     *  deleteDeviceDetailssAction() used to delete device details details
    */
	public function deleteDeviceDetailsAction()
    {
        $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-device-details', 'json')->initContext();
            
			if( $this->_deviceManagementAccessRights['Employee']['Delete'] == 1 )
			{
				$deviceId = $this->_getParam('deviceId', null);
				$deviceId = filter_var($deviceId, FILTER_SANITIZE_NUMBER_INT);
				
				if (!empty($deviceId) && $deviceId > 0 )
				{
					$customFormName = $this->_ehrTables->getCustomForms($this->_formNameE);
					$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameE);
						
					$this->view->result = $this->_dbAttendance->deleteDeviceDetails($deviceId, $this->_logEmpId, $formName);
				}
				else
				{
					$this->view->result = array('success'=>false, 'msg'=>'Invalid data', 'type'=>'info');
				}
			}
			else
			{
				$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'warning');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'attendance', 'employees');
        }
	}

	public function validateManagerAttendanceApprovalAction()
    {
        $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('validate-manager-attendance-approval', 'json')->initContext();
			
			$isEmployeeManagerNotAdmin = 0;
			if($this->_attendanceAccessRights['Employee']['Is_Manager'] == 1 || $this->_myTeamAccess['Is_Manager'] == 1){
				if($this->_attendanceAccessRights['Admin'] == 'admin' || $this->_myTeamAccessRights['Admin'] == 'admin'){
					$isEmployeeManagerNotAdmin = 0;
				}else{
					$isEmployeeManagerNotAdmin = 1;
				}
			}
			// $isEmployeeManagerNotAdmin=1;
			if($isEmployeeManagerNotAdmin == 1){
				$lastSalaryDate = $this->_getParam('lastSalaryDate', null);

				if(!empty($lastSalaryDate)){
					$statusUpdateApplicableResult = $this->_dbAttendance->validateManagerAttendanceApproval($lastSalaryDate);
					$this->view->result = $statusUpdateApplicableResult;
				}else{
					$this->view->result = array('updateStatus'=>2,'message'=>'Invalid input(s)','cutOffDate'=>'');					
				}
			}else{
				$this->view->result = array('updateStatus'=>1,'message'=>'Login employee is either manager or admin. So status can be updated.','cutOffDate'=>'');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'attendance', 'employees');
        }
	}

	/**
     *  listLeaveTypesAction() used to list leave types based on the input
    */
	public function listLeaveTypesAction()
    {
        $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-leave-types', 'json')->initContext();
			
			$listQuarterDaysLeaves = $this->_getParam('listQuarterDaysLeaves', 0);
			$listQuarterDaysLeaves = filter_var($listQuarterDaysLeaves, FILTER_SANITIZE_NUMBER_INT);			
			
			$dbLeaveType = new Employees_Model_DbTable_Leave();
			$leaveTypeResult = $dbLeaveType->getLeaveType();
			$this->view->result = array('success'=>true, 'msg'=>'Leave types retrieved successfully.','leaveTypes'=>$leaveTypeResult, 'type'=>'info');
        }
        else
        {
            $this->_helper->redirector('index', 'attendance', 'employees');
        }
	}

	public function __destruct()
    {
        
    }
	
}