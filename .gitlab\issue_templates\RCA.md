DEVELOPMENT TEAM :

- Description:

- Root Cause:

- Current Solution:

- How to avoid this in the future:

- [ ] I checked the impact on payslip:

- [ ] I checked the impact on existing reports:

- [ ] I checked the impact on data import modules:

- [ ] I checked the impact on data import template files:

- [ ] I checked the impacted module other than the above forms/steps:

- [ ] I checked the impact on access flow:  

       * Employee level access change:
       * Manager  level access change:
       * All the Admin level access changes:

- [ ] I checked the impact on the user interface:

- [ ] I prepared the MySQL query and attached it here:

          [ ] I updated the access rights query when the new form is added or removed:

          [ ] I updated the dependent form query when the new form is added or removed for plan details:

          [ ] I created the index for necessary fields :

- [ ] I updated/uploaded the reference link and other requirement files:

- [ ] I have done the code level analysis and updated the estimation:  

Whenever we have a form removed:

- [ ] I have confirmed that the notification is removed in all PHP, Vue2, and Vue 3 layouts.
- [ ] I have confirmed that the Form ID/Records are removed (App manager, All customer instances).
- [ ] I have confirmed that form-related details are updated/removed in payroll prerequisites.
- [ ] I have confirmed that form-related details are updated/removed in the datasetup dashboard.
- [ ] I have confirmed that form-related details are updated/removed in email templates.
- [ ] I have confirmed that form-related details are updated/removed in redirections from other forms.
- [ ] I have confirmed that form-related details are updated/removed in the Module and Forms table.
- [ ] I have confirmed that form-related details are updated/removed in URLs from the API (Refer to forms table cleanup).
- [ ] I have confirmed that form that following form is subform that form is added in appmanager plan details code. 
- [ ] Identify the data type for each column in the Excel sheet.
- [ ] Set the identified data type as the default data type for the respective column.
- [ ] Implement horizontal and vertical scroll for the Excel sheet.
- [ ] I confirmed that archived tables should not have unique keys because the same kind of records can be deleted multiple times.

TESTING TEAM :

- [ ]  I analyze this task and its impacts

- [ ]  I validate the analysis and prepare the test cases(#Description Mandatory)

- [ ]  I update the testCases in ticket and respective document(with ticket number):

- [ ] These test cases reviewed by the lead

- [ ]  I test all the test cases which you documented

- [ ]  I test the dependent changes of this task

- [ ]  I test the application in all(Chrome,Edge,FireFox,IE,Safari) the browser

- [ ]  The functionality covers the requirement

- [ ]  I closed this issue after completing the testing

- [ ] I tested the task for test suit(n-1) preparation.