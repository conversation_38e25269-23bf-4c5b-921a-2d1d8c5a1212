<?php
//=========================================================================================
//=========================================================================================
/* Program : Salary.php															         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MYSQL Query to retrive, add and update monthly salary and houry wages   *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                 Description                       	 *
 *  0.1        30-May-2013    Narmadha               Initial Version         	         *
 *  0.2        27-Dec-2013    Mahesh                 Changed Functions                   *
 *                                                   1.getBenefits                       *
 *                                                   2.getHourlyEmpBenefits              *
 *  0.3        09-Jan-2014    Mahesh                 Changed Functions                   *
 *                                                   1.getBenefits                       *
 *  0.4        29-Jan-2014    Mahesh                 Changed Function                    *
 *                                                   1.getbenefits                       *
 *                                                   (updated with Basic_Overwrite Flag) *
 *                                                                                       *
 *  0.5        05-Mar-2014    Mahesh                 (changed pf and insurance calc by   *
 *                                                   adding allowance amt with basicPay. *
 *                                                   Also changed basic pay calc )       *
 *                                                   Changed Functions:                  *
 *                                                   1.getBenefits                       *
 *                                                   2.getAllowances                     *
 *                                                   3.getHourlyEmpBenefits              *
 *                                                   4.getBasicPay                       *
 *																						 *
 *  0.6        15-Mar-2014    Mahesh                 Added Functions                     *
 *												     (for salary recalculation process)  *
 *                 								     1.showSalaryRecalcEmployees   	     *
 *                                                   2.getSalaryRecalcEmployeeDetails    *
 *												     3.recalculateSalary                 *
 *  0.7		   10-Apr-2014	  Sandhosh               Modified Functions                  *
 *                                        		     1.viewMonthlySalary,      			 *
 *	       										     2.addMonthlySalary					 *
 *                                        		     3.updateMonthlySalary           	 *
 *																						 *
 *                            Mahesh                 1.recalculateSalary()for adding     *
 *                                                   emp level overtime amount           *
 *  0.8        14-May-2014    Mahesh                 1.recalculateSalary()               *
 *																						 *
 *  0.9		   21-Jul-2014    Mahesh				 1.hourlyWagesEmployee	 			 *
 *  											     2.getBenefits						 *
 *												     3.getHourlyEmpBenefits				 *
 *												     4.recalculateSalary	 	 		 *
 *																						 *
 *  1.0		   18-Sep-2014    Mahesh				 1.showSalaryAudit					 *
 *												     2.showWagesAudit		 			 *
 *												     3.getHourlyEmpBenefits   			 *
 *																						 *
 *  2.0        02-Feb-2015    Nivethitha             Changes in file for mobile app      *
 *                                                   1.Extra fields are added in         *
 *                                                   field list of list query.           */
//=========================================================================================
//=========================================================================================
class Payroll_Model_DbTable_Salary extends Zend_Db_Table_Abstract
{
	protected $_db          = null;
	protected $_ehrTables   = null;
	protected $_dbPayslip   = null;
	protected $_dbPersonal  = null;
	protected $_dbOrgDetail = null;
	protected $_orgDF       = null;
	protected $_dbCommonFun = null;

	public function init()
	{
		$this->_ehrTables   = new Application_Model_DbTable_Ehr();
		$this->_dbPersonal  = new Employees_Model_DbTable_Personal();
		$this->_dbOrgDetail = new Organization_Model_DbTable_OrgSettings();
		$this->_dbPayslip   = new Payroll_Model_DbTable_Payslip();
		$this->_dbCommonFun = new Application_Model_DbTable_CommonFunction();
		$this->_db          = Zend_Registry::get('subHrapp');
		$this->_orgDF       = $this->_ehrTables->orgDateformat();
		
		if (Zend_Registry::isRegistered('orgDetails'))
			$this->_orgDetails = Zend_Registry::get('orgDetails');
	}
	
	/**
	 * to show monthly salary records in a grid
	 */
	public function listMonthlySalary ($page, $rows, $sortField, $sortOrder, $searchAll=null, $searchArr, $salaryAccess)
	{
		$employeeName            = $searchArr['employeeName'];
		$annualGrossSalaryStart  = $searchArr['annualGrossSalaryStart'];
		$annualGrossSalaryEnd    = $searchArr['annualGrossSalaryEnd'];
		$grossMonthlySalaryStart = $searchArr['grossMonthlySalaryStart'];
		$grossMonthlySalaryEnd   = $searchArr['grossMonthlySalaryEnd'];
		$monthlyBasicSalaryStart = $searchArr['monthlyBasicSalaryStart'];
		$monthlyBasicSalaryEnd   = $searchArr['monthlyBasicSalaryEnd'];
		$effectiveDateStart      = $searchArr['effectiveDateStart'];
		$effectiveDateEnd        = $searchArr['effectiveDateEnd'];
		$employeeStatus          = $searchArr['employeeStatus'];
		$serviceProviderId       = $searchArr['serviceProviderId'];
		/**
		 *	Sorting columns based on display column order in grid
		*/
		switch($sortField)
		{
			case 1: $sortField = 'EJ.User_Defined_EmpId'; break;
			case 2: $sortField = 'P.Emp_First_Name'; break;
			case 3: $sortField = 'S.Annual_Gross_Salary'; break;
			case 4: $sortField = 'S.Monthly_Gross_Salary'; break;
			case 5: $sortField = 'S.Basic_Pay'; break;
			case 6: $sortField = 'S.Effective_Date'; break;
			default: $sortField = 'S.Added_On'; $sortOrder = 'desc'; break;				
		}
		
		$fbpapplicable = $this->_ehrTables->getFlexiBenefitPlan();
		
		$qryMonthSalary = $this->_db->select()->distinct()->from(array('S'=>$this->_ehrTables->salary),
																 array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS S.Employee_Id as Count'),
																	   'S.Employee_Id','S.Annual_Gross_Salary', 'S.Basic_Pay', 'S.Gratuity_Amount','S.Exempt_EDLI',
																	   'S.Monthly_Gross_Salary','S.Is_PfEmployee', 'S.Overtime_Wage','S.Is_ETFEmployee',
																	   'S.Is_InsuranceEmployee','S.Is_OvertimeEmployee','S.Employee_Contribution_Rate','S.Employer_Contribution_Rate',
																	   'S.Is_GratuityEmployee','S.Salary_Recalc','S.Annual_Ctc','S.Represent_Basic_As_Multi_Components','S.Basic_Component_One','S.Basic_Component_Two',
																	   'Overtime_Level'=>new Zend_Db_Expr('CASE WHEN S.Is_OvertimeEmployee = "EMP" THEN "Employee" WHEN 
																		S.Is_OvertimeEmployee = "GRA" THEN "Grade" WHEN S.Is_OvertimeEmployee = "" THEN "No overtime" END'),
																		
																		'Salary_Overtime_Allocation'=>new Zend_Db_Expr('CASE WHEN S.Is_OvertimeEmployee = "EMP" THEN S.Overtime_Allocation WHEN 
																	    S.Is_OvertimeEmployee = "GRA" THEN G.Overtime_Allocation WHEN S.Is_OvertimeEmployee = "" THEN "" END'),

																		'Salary_Overtime_Wage_Index'=>new Zend_Db_Expr('CASE WHEN S.Is_OvertimeEmployee = "EMP" THEN S.Overtime_Wage_Index WHEN 
																	    S.Is_OvertimeEmployee = "GRA" THEN G.Overtime_Wage_Index WHEN S.Is_OvertimeEmployee = "" THEN "" END'),      

																		'Salary_Overtime_Fixed_Amount'=>new Zend_Db_Expr('CASE WHEN S.Is_OvertimeEmployee = "EMP" THEN S.Overtime_Wage WHEN 
																		S.Is_OvertimeEmployee = "GRA" THEN G.OvertimeFixedAmount WHEN S.Is_OvertimeEmployee = "" THEN "" END'), 

																	   'S.Flexible_Benefit_Plan_Type','S.Flexible_Benefit_Plan_Amount','S.Flexible_Benefit_Plan_Percentage',
																	   new Zend_Db_Expr("date_format(S.ESI_Contribution_End_Date, '".$this->_orgDF['sql']."') as View_ESI_Contribution_End_Date"),
																	   new Zend_Db_Expr("date_format(S.Effective_Date, '".$this->_orgDF['sql']."') as View_Effective_Date"),
																	   new Zend_Db_Expr("DATE_FORMAT(S.Added_On,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
																	   new Zend_Db_Expr("DATE_FORMAT(S.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On"),
																	   'Log_Id'=>new Zend_Db_Expr($salaryAccess['LogId']), 'S.Effective_Date',
																	   'FBPApplicable'=>new Zend_Db_Expr($fbpapplicable)))
		
								->joinInner(array('P'=>$this->_ehrTables->empPersonal),'P.Employee_Id=S.Employee_Id',
											array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name, ' ', P.Emp_Last_Name)")))
								
								->joinInner(array('EJ'=>$this->_ehrTables->empJob),'P.Employee_Id=EJ.Employee_Id',
											array('EJ.Emp_Status', 'User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN P.Employee_Id ELSE EJ.User_Defined_EmpId END')))
								
								->joinInner(array('L'=>$this->_ehrTables->location), 'L.Location_Id=EJ.Location_Id',
											array('L.Currency_Symbol'))
								
								->joinLeft(array('SH'=>$this->_ehrTables->salaryHistory), 'P.Employee_Id=SH.Employee_Id',
										   array('Audit'=>'SH.Employee_Id'))
								
								->joinInner(array('G'=>$this->_ehrTables->empGrade), 'S.Grade_Id=G.Grade_Id', array('G.OvertimeFixedAmount as Grade_Overtime_Fixed_Amount','G.Overtime_Allocation as Grade_Overtime_Allocation',
								'G.Overtime_Wage_Index as Grade_Overtime_Wage_Index'))
								
								->joinInner(array('P2'=>$this->_ehrTables->empPersonal), 'S.Added_By=P2.Employee_Id',
											array('Added_By_Name'=>new Zend_Db_Expr("CONCAT(P2.Emp_First_Name, ' ', P2.Emp_Last_Name)")))
								
								->joinLeft(array('P3'=>$this->_ehrTables->empPersonal), 'S.Updated_By=P3.Employee_Id',
										   array('Updated_By_Name'=>new Zend_Db_Expr("CONCAT(P3.Emp_First_Name, ' ', P3.Emp_Last_Name)")))
								
								->joinLeft(array('his'=>$this->_ehrTables->salaryHistory),'his.Employee_Id=S.Employee_Id',
										   array('his.Employee_Id as History'))
	
								->order("$sortField $sortOrder")
								->limit($rows, $page);
		
		if (empty($salaryAccess['Admin']))
		{
			$getEmployeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
												  ->where('Manager_Id = ?', $salaryAccess['LogId']));
			
			if( $salaryAccess['Is_Manager'] == 1 && !empty($getEmployeeId))
			{
				/* If the manager is having access to view his employee salary records */
				if(empty($this->_orgDetails['Restrict_Financial_Access_For_Manager'])){
					$qryMonthSalary->where('S.Employee_Id = :EmpId or S.Employee_Id IN (?)', $getEmployeeId)
								->bind(array('EmpId'=>$salaryAccess['LogId']));
				}else{
					$qryMonthSalary->where('S.Employee_Id = ?', $salaryAccess['LogId']);
				}
			}
			else
			{
				$qryMonthSalary->where('S.Employee_Id = ?', $salaryAccess['LogId']);
			}
		}
		
		/**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			//$conditions = $this->_db->quoteInto('P.Emp_First_Name Like ?', "%$searchAll%");
			//$conditions .= $this->_db->quoteInto('or P.Emp_Last_Name Like ?', "%$searchAll%");
            $conditions  = $this->_db->quoteInto(new Zend_Db_Expr('Concat(P.Emp_First_Name," ",P.Emp_Last_Name) Like ?'),"%$searchAll%");
			$conditions .= $this->_db->quoteInto('or S.Annual_Gross_Salary Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or S.Monthly_Gross_Salary Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or S.Basic_Pay Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or S.Effective_Date Like ?', "%$searchAll%");
            $conditions .= $this->_db->quoteInto('or EJ.User_Defined_EmpId Like ?', "%$searchAll%");
			
			$qryMonthSalary->where($conditions);
		}
		
		/* Filter for employee */       
        if (!empty($employeeName) && preg_match('/^[a-zA-Z]/', $employeeName))
        {
			$qryMonthSalary->where($this->_db->quoteInto(new Zend_Db_Expr('Concat(P.Emp_First_Name," ",P.Emp_Last_Name) Like ?'),"%$employeeName%"));
//                ->where($this->_db->quoteInto('P.Emp_First_Name Like ? or ', "%$employeeName%").
//							   $this->_db->quoteInto('P.Emp_Last_Name Like ?', "%$employeeName%"));
        }
		
		/* Filter for annual gross salary start */					
		if (($annualGrossSalaryStart >=0) && preg_match('/^[0-9*\.]/', $annualGrossSalaryStart) && !empty($annualGrossSalaryStart))
		{
		    $qryMonthSalary->where($this->_db->quoteInto('S.Annual_Gross_Salary >= ?', $annualGrossSalaryStart));
		}
		
		/* Filter for gross monthly salary end */					
		if (($annualGrossSalaryEnd >=0) && preg_match('/^[0-9*\.]/', $annualGrossSalaryEnd) && !empty($annualGrossSalaryEnd))
		{
		    $qryMonthSalary->where($this->_db->quoteInto('S.Annual_Gross_Salary <= ?', $annualGrossSalaryEnd));
		}
		
		/* Filter for gross monthly salary start */					
		if (($grossMonthlySalaryStart >=0) && preg_match('/^[0-9*\.]/', $grossMonthlySalaryStart) && !empty($grossMonthlySalaryStart))
		{
		    $qryMonthSalary->where($this->_db->quoteInto('S.Monthly_Gross_Salary >= ?', $grossMonthlySalaryStart));
		}
		
		/* Filter for annual gross salary end */					
		if (($grossMonthlySalaryEnd >=0) && preg_match('/^[0-9*\.]/', $grossMonthlySalaryEnd) && !empty($grossMonthlySalaryEnd))
		{
		    $qryMonthSalary->where($this->_db->quoteInto('S.Monthly_Gross_Salary <= ?', $grossMonthlySalaryEnd));
		}
		
		/* Filter for basic pay start */					
		if (($monthlyBasicSalaryStart >=0) && preg_match('/^[0-9*\.]/', $monthlyBasicSalaryStart) && !empty($monthlyBasicSalaryStart))
		{
		    $qryMonthSalary->where($this->_db->quoteInto('S.Basic_Pay >= ?', $monthlyBasicSalaryStart));
		}
		
		/* Filter for basic pay end */					
		if (($monthlyBasicSalaryEnd >=0) && preg_match('/^[0-9*\.]/', $monthlyBasicSalaryEnd) && !empty($monthlyBasicSalaryEnd))
		{
		    $qryMonthSalary->where($this->_db->quoteInto('S.Basic_Pay <= ?', $monthlyBasicSalaryEnd));
		}
		
		/* Filter for effective start */
		if ($effectiveDateStart != '')
		{
		    $qryMonthSalary->where($this->_db->quoteInto('S.Effective_Date >= ?', $effectiveDateStart));
		}
		
		/* Filter for effective end */					
		if ($effectiveDateEnd != '')
		{
		    $qryMonthSalary->where($this->_db->quoteInto('S.Effective_Date <= ?', $effectiveDateEnd));
		}
		
		if(!empty($employeeStatus) && preg_match('/^[a-zA-Z]/', $employeeStatus))
		{
			$qryMonthSalary->where('EJ.Emp_Status = ?', $employeeStatus);
		}

    	if(!empty($serviceProviderId)&& $this->_orgDetails['Field_Force']==1)
        {
            $qryMonthSalary->where('EJ.Service_Provider_Id = ?',$serviceProviderId);
        }

		$qryMonthSalary = $this->_dbCommonFun->getDivisionDetails($qryMonthSalary,'EJ.Department_Id');

		if(!empty($salaryAccess['Admin']))
		{
			$qryMonthSalary = $this->_dbCommonFun->formServiceProviderQuery($qryMonthSalary,'EJ.Service_Provider_Id',$salaryAccess['LogId']);
		}

		/**
		 * SQL queries
		 * Get data to display
		*/
		$monthlySalary = $this->_db->fetchAll($qryMonthSalary);
      /* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		$formName = ($fbpapplicable == 1) ? 'SAL' : NULL;
		
		if($this->_orgDetails['Provident_Fund_Configuration']=='Current')	
		{
			$providentFundDetails 	= $this->getProvidentFundDetails();
			$providentFundSettings 	= $this->getProvidentFundSettings();	
		}
		else
		{
			$providentFundDetails 	= '';
			$providentFundSettings 	= '';
		}

		foreach ($monthlySalary as $key => $row)
		{
			if($this->_orgDetails['Provident_Fund_Configuration']=='Current')	
			{
				$employeeContributionRate = $row['Employee_Contribution_Rate'];
				$employerContributionRate = $row['Employer_Contribution_Rate'];
			}
			else
			{
				$employeeContributionRate = '';
				$employerContributionRate = '';
			}
			$monthlySalary[$key]['Benefit'] = $this->getBenefits($row['Employee_Id'], $row['Annual_Gross_Salary'], $row['Is_PfEmployee'],
																  $row['Is_InsuranceEmployee'],'', NULL , NULL ,
																  $row['Effective_Date'], $row['Basic_Pay'],$formName,'','',$row['Is_ETFEmployee'],$employeeContributionRate,$employerContributionRate,$providentFundDetails,$providentFundSettings);
		}
		
		
		/* Total data set length */
		$qryGetCount = $this->_db->select()->from($this->_ehrTables->salary, new Zend_Db_Expr('COUNT(Employee_Id)'));
		
		if (empty($salaryAccess['Admin']))
		{
			$getEmployeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
												  ->where('Manager_Id = ?', $salaryAccess['LogId']));
			
			if( $salaryAccess['Is_Manager'] == 1 && !empty($getEmployeeId))
			{				
				/* If the manager is having access to view his employee salary records */
				if(empty($this->_orgDetails['Restrict_Financial_Access_For_Manager'])){
					$qryGetCount->where('Employee_Id = :EmpId or Employee_Id IN (?)', $getEmployeeId)
								->bind(array('EmpId'=>$salaryAccess['LogId']));
				}else{
					$qryGetCount->where('Employee_Id = ?', $salaryAccess['LogId']);
				}
			}
			else
			{
				$qryGetCount->where('Employee_Id = ?', $salaryAccess['LogId']);
			}
		}
		
		$iTotal = $this->_db->fetchOne($qryGetCount);
		
		/**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $monthlySalary);
	}

	/* Its a temprory solution once we update the employee ctc in table we will remove this function*/
	public function updateEmployeeAnnualCtc($sessionId,$salaryMigration=NULL)
	{

		$fbpapplicable = $this->_ehrTables->getFlexiBenefitPlan();
		/* Using below query we could list all the employee salary details.Its same as listMonthlySalary details*/
		$qryMonthSalary = $this->_db->select()->distinct()->from(array('S'=>$this->_ehrTables->salary),
																 array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS S.Employee_Id as Count'),
																	   'S.Employee_Id','S.Annual_Gross_Salary', 'S.Basic_Pay', 'S.Gratuity_Amount','S.Exempt_EDLI',
																	   'S.Monthly_Gross_Salary','S.Is_PfEmployee', 'S.Overtime_Wage','S.Is_ETFEmployee',
																	   'S.Is_InsuranceEmployee','S.Is_OvertimeEmployee',
																	   'S.Is_GratuityEmployee','S.Salary_Recalc','S.Overtime_Allocation','S.Overtime_Wage_Index',
																	   'S.Reason_Id','S.ESI_Contribution_End_Date','S.Annual_Ctc','S.Location_Id','S.Grade_Id',
																	   
																	   'Overtime_Level'=>new Zend_Db_Expr('CASE WHEN S.Is_OvertimeEmployee = "EMP" THEN "Employee" WHEN 
																		S.Is_OvertimeEmployee = "GRA" THEN "Grade" WHEN S.Is_OvertimeEmployee = "" THEN "No overtime" END'),
																		
																		'Salary_Overtime_Allocation'=>new Zend_Db_Expr('CASE WHEN S.Is_OvertimeEmployee = "EMP" THEN S.Overtime_Allocation WHEN 
																	    S.Is_OvertimeEmployee = "GRA" THEN G.Overtime_Allocation WHEN S.Is_OvertimeEmployee = "" THEN "" END'),

																		'Salary_Overtime_Wage_Index'=>new Zend_Db_Expr('CASE WHEN S.Is_OvertimeEmployee = "EMP" THEN S.Overtime_Wage_Index WHEN 
																	    S.Is_OvertimeEmployee = "GRA" THEN G.Overtime_Wage_Index WHEN S.Is_OvertimeEmployee = "" THEN "" END'),      

																		'Salary_Overtime_Fixed_Amount'=>new Zend_Db_Expr('CASE WHEN S.Is_OvertimeEmployee = "EMP" THEN S.Overtime_Wage WHEN 
																		S.Is_OvertimeEmployee = "GRA" THEN G.OvertimeFixedAmount WHEN S.Is_OvertimeEmployee = "" THEN "" END'), 

																	   'S.Flexible_Benefit_Plan_Type','S.Flexible_Benefit_Plan_Amount','S.Flexible_Benefit_Plan_Percentage',
																	   new Zend_Db_Expr("date_format(S.ESI_Contribution_End_Date, '".$this->_orgDF['sql']."') as View_ESI_Contribution_End_Date"),
																	   new Zend_Db_Expr("date_format(S.Effective_Date, '".$this->_orgDF['sql']."') as View_Effective_Date"),
																	   new Zend_Db_Expr("DATE_FORMAT(S.Added_On,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
																	   new Zend_Db_Expr("DATE_FORMAT(S.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On"),
																	   'Log_Id'=>new Zend_Db_Expr($sessionId), 'S.Effective_Date',
																	   'FBPApplicable'=>new Zend_Db_Expr($fbpapplicable)))
		
								->joinInner(array('P'=>$this->_ehrTables->empPersonal),'P.Employee_Id=S.Employee_Id',
											array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name, ' ', P.Emp_Last_Name)")))
								
								->joinInner(array('EJ'=>$this->_ehrTables->empJob),'P.Employee_Id=EJ.Employee_Id',
											array('EJ.Emp_Status', 'User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN P.Employee_Id ELSE EJ.User_Defined_EmpId END')))
								
								->joinInner(array('L'=>$this->_ehrTables->location), 'L.Location_Id=EJ.Location_Id',
											array('L.Currency_Symbol'))
								
								->joinLeft(array('SH'=>$this->_ehrTables->salaryHistory), 'P.Employee_Id=SH.Employee_Id',
										   array('Audit'=>'SH.Employee_Id'))
								
								->joinInner(array('G'=>$this->_ehrTables->empGrade), 'S.Grade_Id=G.Grade_Id', array('G.OvertimeFixedAmount as Grade_Overtime_Fixed_Amount','G.Overtime_Allocation as Grade_Overtime_Allocation',
								'G.Overtime_Wage_Index as Grade_Overtime_Wage_Index'))
								
								->joinInner(array('P2'=>$this->_ehrTables->empPersonal), 'S.Added_By=P2.Employee_Id',
											array('Added_By_Name'=>new Zend_Db_Expr("CONCAT(P2.Emp_First_Name, ' ', P2.Emp_Last_Name)")))
								
								->joinLeft(array('P3'=>$this->_ehrTables->empPersonal), 'S.Updated_By=P3.Employee_Id',
										   array('Updated_By_Name'=>new Zend_Db_Expr("CONCAT(P3.Emp_First_Name, ' ', P3.Emp_Last_Name)")))
								
								->joinLeft(array('his'=>$this->_ehrTables->salaryHistory),'his.Employee_Id=S.Employee_Id',
										   array('his.Employee_Id as History'));

		
		$monthlySalary = $this->_db->fetchAll($qryMonthSalary);
    	
		$formName = ($fbpapplicable == 1) ? 'SAL' : NULL;
		
		$updated = 0;
		$monthlySalaryCount = 0;
		

		if(!empty($monthlySalary))
		{
			if(!empty($salaryMigration))
			{
				$this->_db->query('TRUNCATE TABLE '.$this->_ehrTables->employeeSalaryConfiguration);
				$this->_db->query('TRUNCATE TABLE '.$this->_ehrTables->employeeSalaryDetails);
				$this->_db->query('TRUNCATE TABLE '.$this->_ehrTables->employeeSalaryAllowance);
				$this->_db->query('TRUNCATE TABLE '.$this->_ehrTables->employeeSalaryRetirals);
			}
			
			$monthlySalaryCount = count($monthlySalary);
			foreach ($monthlySalary as $key => $row)
			{
				/*get the employee benefits*/
				$monthlySalary[$key]['Benefit'] = $this->getBenefits($row['Employee_Id'], $row['Annual_Gross_Salary'], $row['Is_PfEmployee'],
																	$row['Is_InsuranceEmployee'],'', NULL , NULL ,
																	$row['Effective_Date'], $row['Basic_Pay'],$formName,'','',$row['Is_ETFEmployee']);

				$ctcAmount = $this->calculateCtcAmount($monthlySalary[$key]);													

				if(!empty($salaryMigration))
				{
					$updatedSalaryTemplate = $this->updateSalaryTemplate($monthlySalary[$key],$ctcAmount,$salaryMigration);
					$updated +=$updatedSalaryTemplate;
				}
				else 
				{
					$monthlySalaryArray['Updated_On'] = date('Y-m-d H:i:s');
					$monthlySalaryArray['Updated_By'] = $sessionId;
					$monthlySalaryArray['Annual_Ctc'] = $ctcAmount;
					$updateAnnualCtc = $this->_db->update($this->_ehrTables->salary,$monthlySalaryArray, array('Employee_Id = '. $monthlySalary[$key]['Employee_Id']));
					$updated +=$updateAnnualCtc;
				}	 
			}
		}

		if(!empty($salaryMigration))
		{
			if($updated==0)
			{
				return  array('success' => false, 'msg' => 'Unable to migrate employee salary details', 'type' => 'warning');
			}
			if($monthlySalaryCount==$updated)
			{
				return  array('success' => true, 'msg' => 'Salary migrated successfully for all the employees in your organization', 'type' => 'success');
			}
			elseif($monthlySalaryCount!=$updated)
			{
				return  array('success' => true, 'msg' => 'Salary migration done partially', 'type' => 'success');
			}
		}
		else 
		{
			if($updated==0)
			{
				return  array('success' => false, 'msg' => 'Unable to update the annual ctc', 'type' => 'warning');
			}
			if($monthlySalaryCount==$updated)
			{
				return  array('success' => true, 'msg' => 'Annual CTC updated successfully for all the employees in your organization', 'type' => 'success');
			}
			elseif($monthlySalaryCount!=$updated)
			{
				return  array('success' => true, 'msg' => 'Annual CTC  partially updated', 'type' => 'success');
			}
		}
		
		
	}

	/*Migrate old salary details and add it in new salary template tables for all the employees*/
	public function updateSalaryTemplate($record,$ctcAmount,$salaryMigration)
	{
		$this->_dbComment   = new Payroll_Model_DbTable_PayrollComment();		
		$this->_dbProvidentFund =new Payroll_Model_DbTable_ProvidentFund();
		$this->_dbEtf        = new Payroll_Model_DbTable_ETF();
		$this->_dbInsurance = new Payroll_Model_DbTable_Insurance();
		$this->_dbFHInsurance = new Payroll_Model_DbTable_FixedHealthInsurance();

		$updated = 0;
		$pfDetails =  $this->_db->fetchRow($this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),array('P.UAN','J.Pf_PolicyNo'))
									->joinInner(array('P'=>$this->_ehrTables->empPersonal),'J.Employee_Id = P.Employee_Id',array())
									->where('J.Employee_Id = ?', $record['Employee_Id']));

		if(isset($record['Benefit']['VariableInsurance']))							
		{
			$variableInsuranceCount = count($record['Benefit']['VariableInsurance']);
		}
		else
		{
			$variableInsuranceCount = 0;
		}
		
	
		$eligibleForEmployeeStateInsurance = 0;
		$esiNumber = new Zend_Db_Expr('NULL');
		$npsNumber = new Zend_Db_Expr('NULL');
		if($variableInsuranceCount > 0)
		{
			for ($i=0;$i<$variableInsuranceCount;$i++) 
			{
				if ($record['Benefit']['VariableInsurance'][$i]['Mode'] == 'Monthly') 
				{
					/*this query help us to check esi available for that organization or not*/
					$qryEmployeeStateInsurance = $this->_db->select()->from(array('I' => $this->_ehrTables->variableInsurance),array('IT.InsuranceType_Id'))
									->joinInner(array('IT'=>$this->_ehrTables->insuranceType),'IT.InsuranceType_Id = I.InsuranceType_Id',array(''))
									->joinInner(array('PM'=>$this->_ehrTables->unit), 'PM.Target_Value = I.PaymentMode_Id',array(''))
									->joinInner(array('IG'=>$this->_ehrTables->insurancetypeGrade), 'I.InsuranceType_Id=IG.InsuranceType_Id',array(''))
									->where('I.Coverage = 0')
									->where('IG.Org_ShareAmount IS NULL')
									->where('IT.Employee_State_Insurance = 1')
									->where('I.Contribution_Period = 6')
									->where('PM.Target_Unit LIKE ?', 'Month')
									->where('IT.Insurance_Name = ?',$record['Benefit']['VariableInsurance'][$i]['Insurance_Name'])
									->where('IG.Org_SharePercent >= 0');

					$getEmployeeStateInsurance = $this->_db->fetchOne($qryEmployeeStateInsurance);

					if(!empty($getEmployeeStateInsurance))
					{
						 $eligibleForEmployeeStateInsurance = 1;
						 $esiNumber = $this->_db->fetchOne($this->_db->select()->from(array('EIP' => $this->_ehrTables->empInsurancePolicyNo),array('Policy_No'))
																	->joinInner(array('IT'=>$this->_ehrTables->insuranceType),'EIP.InsuranceType_Id = IT.InsuranceType_Id', array(''))
																	->where('EIP.InsuranceType_Id = ?', $getEmployeeStateInsurance)
																	->where('EIP.Employee_Id = ?', $record['Employee_Id']));
					}
				}
			}	
		}

		$employeeSalaryConfiguration = array('Employee_Id'=>$record['Employee_Id'],
											'Eligible_For_Overtime'=>$record['Is_OvertimeEmployee'],
											'Overtime_Allocation'=>$record['Overtime_Allocation'],
											'Overtime_Wage_Index'=>$record['Overtime_Wage_Index'],
											'Overtime_Wage'=>$record['Overtime_Wage'],
											'Eligible_For_Pf'=>$record['Is_PfEmployee'],
											'Exempt_EDLI'=>$record['Exempt_EDLI'],
											'UAN'=>$pfDetails['UAN'],	
											'Pf_PolicyNo'=>$pfDetails['Pf_PolicyNo'],
											'Eligible_For_ESI'=>$eligibleForEmployeeStateInsurance,
											'ESI_Number'=>$esiNumber,
											'ESI_Contribution_End_Date'=>$record['ESI_Contribution_End_Date'],
											'Reason_Id'=>$record['Reason_Id'],
											'Eligible_For_Insurance'=>$record['Is_InsuranceEmployee'],
											'Eligible_For_Nps'=>$record['Is_ETFEmployee'],
											'Nps_Number'=>$npsNumber,
											'Eligible_For_Gratuity'=>$record['Is_GratuityEmployee'],
											'Added_On'=>date('Y-m-d H:i:s'),
											'Added_By'=>$record['Log_Id']);

											
		$employeeSalaryDetails = array('Employee_Id'=>$record['Employee_Id'],
										'Template_Id'=>new Zend_Db_Expr('NULL'),
										'Location_Id'=>$record['Location_Id'],
										'Grade_Id'=>$record['Grade_Id'],
										'Basic_Pay'=>$record['Basic_Pay'],
										'Effective_From'=>$record['Effective_Date'],
										'Effective_To'=>new Zend_Db_Expr('NULL'),
										'Annual_Ctc'=>$ctcAmount,	
										'Annual_Gross_Salary'=>$record['Annual_Gross_Salary'],	
										'Monthly_Gross_Salary'=>$record['Monthly_Gross_Salary'],	
										'Added_On'=>date('Y-m-d H:i:s'),	
										'Added_By'=>$record['Log_Id']);	

	
		if(!empty($employeeSalaryConfiguration) && !empty($employeeSalaryDetails))
		{
			$updated = $this->_db->insert($this->_ehrTables->employeeSalaryConfiguration,$employeeSalaryConfiguration);
			$updated = $this->_db->insert($this->_ehrTables->employeeSalaryDetails,$employeeSalaryDetails);

		}								
		$allowanceCount = count($record['Benefit']['Allowance']);									

		$employeeSalaryId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->employeeSalaryDetails, 
                                                                     array(new Zend_Db_Expr('Max(Employee_Salary_Id) as EmployeeSalaryId'))));
		if ($allowanceCount > 0)
		{
			for ($i=0;$i<$allowanceCount;$i++) 
			{
				$allowanceDetails = $this->getAllowanceDetails($record['Benefit']['Allowance'][$i][4]);
				if(!empty($allowanceDetails))
				{
					$allowanceId = $this->_db->fetchOne($this->_db->select()->from(array('A'=>$this->_ehrTables->allowances), array('A.Allowance_Id'))												   
											   ->joinLeft(array('AT'=>$this->_ehrTables->allowanceTypes), 'A.Allowance_Type_Id = AT.Allowance_Type_Id', array(''))
												->where('A.Coverage = ?','ORG')
												->where('AT.Period = ?',$allowanceDetails['Period'])												
												->where('A.Allowance_Type_Id = ?',$allowanceDetails['Allowance_Type_Id']));
					if(!empty($allowanceId))
					{
						if($allowanceDetails['Allowance_Type']=='Percentage')
						{
							$amount = new Zend_Db_Expr('NULL');
						}
						else 
						{
							$amount = $record['Benefit']['Allowance'][$i][1];
						}
						
						
						$employeeSalaryAllowance = array('Employee_Salary_Id'=>$employeeSalaryId,	
														'Allowance_Id'=>$allowanceDetails['Allowance_Id'],	
														'Allowance_Type'=>$allowanceDetails['Allowance_Type'],
														'Percentage'=>$allowanceDetails['Percentage'],	
														'Amount'=>$amount);

						$updateAllowance = $this->_db->insert($this->_ehrTables->employeeSalaryAllowance,$employeeSalaryAllowance);
					}														

				}
								
			}
		}

		$monthlyPayslipId = $pfGrossSalary = 0 ;
		$midJoin = NULL;
		
		
		$salaryDateDetails = $this->_dbPayslip->getSalaryDateRange(date('m',strtotime($record['Effective_Date'])),date('Y',strtotime($record['Effective_Date'])),strtotime($record['Effective_Date']));
		$selectedMonth = $salaryDateDetails['Last_SalaryDate'];
		

		$salaryMonth = date('n,Y', strtotime($selectedMonth));
		
		if($record['Is_PfEmployee']==1)
		{
			$pfIncludedAllowanceAmt = $this->_dbPayslip->allowances($record['Employee_Id'], $record['Grade_Id'], $record['Location_Id'], $monthlyPayslipId,$record['Basic_Pay'], $salaryMonth,'m', 'PF',$midJoin,NULL,null,null,$salaryMigration);												                                                                                                        
			
			$pfGrossSalary = $record['Basic_Pay']+$pfIncludedAllowanceAmt[0]+$pfIncludedAllowanceAmt[1];

			$providentFundDetails = $this->_dbProvidentFund->getPFShare($record['Employee_Id'], $pfGrossSalary,$salaryMigration);
	
		
			if(!empty($providentFundDetails))
			{
				$pfFormId = $this->_dbComment->getFormId('Provident Fund');
				
				if($providentFundDetails['Fixed_Variable_Flag'] == "0") 
				{
					$providentFundDetails['Retirals_Type']='Amount';
				}
				else 
				{
					$providentFundDetails['Retirals_Type']='Percentage';
				}
					
				if(isset($providentFundDetails['Statutory_Salary_Limit']))
				{
					if($providentFundDetails['Statutory_Salary_Limit'] > 0) 
					{
						$providentFundDetails['Employee_Contribution']='Restrict_Pf_Wages';
						$providentFundDetails['Employer_Contribution']='Restrict_Pf_Wages';
					}
					else 
					{
						$providentFundDetails['Employee_Contribution']='Actual_Pf_Wages';
						$providentFundDetails['Employer_Contribution']='Actual_Pf_Wages';
					}
				}
				else 
				{
					$providentFundDetails['Statutory_Salary_Limit']   = new Zend_Db_Expr('NULL'); //null
					$providentFundDetails['Employee_Contribution']	  = new Zend_Db_Expr('NULL');
					$providentFundDetails['Employer_Contribution']	  = new Zend_Db_Expr('NULL');
				}

				//we are going to create new table and we are going to use that reference that's why Retirasl id is hard coded as 1.

				$pfRetiralComponents = array('Employee_Salary_Id'=>$employeeSalaryId,	
											'Form_Id'=>	$pfFormId,
											'Retirals_Id'=>	1,
											'Retirals_Type'=>$providentFundDetails['Retirals_Type'],	
											'Employee_Share_Percentage'=>$providentFundDetails['Employee_Share'],	
											'Employer_Share_Percentage'=>$providentFundDetails['Company_Share'],	
											'Employee_Share_Amount'=>$providentFundDetails['Employee_Share_Amount'],	
											'Employer_Share_Amount'=>$providentFundDetails['Company_Share_Amount'],	
											'PF_Employee_Contribution'=>$providentFundDetails['Employee_Contribution'], 	
											'PF_Employer_Contribution'=>$providentFundDetails['Employer_Contribution'],
											'Employee_Statutory_Limit'=>$providentFundDetails['Statutory_Salary_Limit'],
											'Employer_Statutory_Limit'=>$providentFundDetails['Statutory_Salary_Limit'], 
											'Admin_Charge'=>$providentFundDetails['Admin_Charge'],	
											'EDLI_Charge'=>$providentFundDetails['EDLI_Charge']);

											
				$updatePfRetirals = $this->_db->insert($this->_ehrTables->employeeSalaryRetirals,$pfRetiralComponents);
							
			}
		}

		if($record['Is_ETFEmployee']==1)
		{
			// get all the allowance include with pf
			$etfIncludedAllowanceAmt = $this->_dbPayslip->allowances($record['Employee_Id'], $record['Grade_Id'], $record['Location_Id'], $monthlyPayslipId,$record['Basic_Pay'], $salaryMonth,'m', 'NPS',$midJoin,NULL,null,null,$salaryMigration);												                                                                                                        
			$etfGrossSalary = $record['Basic_Pay']+$etfIncludedAllowanceAmt[0]+$etfIncludedAllowanceAmt[1];

			$etfDetails = $this->_dbEtf->getETFShare($record['Employee_Id'], $etfGrossSalary,$salaryMigration);
	
			if(!empty($etfDetails))
			{
				$etfFormId = $this->_dbComment->getFormId('NPS');
				
				if($etfDetails['Fixed_Variable_Flag'] == "0") 
				{
					$etfDetails['Retirals_Type']='Amount';
				}
				else 
				{
					$etfDetails['Retirals_Type']='Percentage';
				}
				/*if organization level etf is not exist and employee level etf exist we are not going to migrate the details*/
				
				if(isset($etfDetails['EtfOrg_Id']))
				{
					$etfRetiralComponents = array('Employee_Salary_Id'=>$employeeSalaryId,	
												'Form_Id'=>	$etfFormId,
												'Retirals_Id'=>	$etfDetails['EtfOrg_Id'],
												'Retirals_Type'=>$etfDetails['Retirals_Type'],	
												'Employee_Share_Percentage'=>$etfDetails['Employee_Share'],	
												'Employer_Share_Percentage'=>$etfDetails['Company_Share'],	
												'Employee_Share_Amount'=>$etfDetails['Employee_Share_Amount'],	
												'Employer_Share_Amount'=>$etfDetails['Company_Share_Amount'],	
												'PF_Employee_Contribution'=>new Zend_Db_Expr('NULL'),
												'PF_Employer_Contribution'=>new Zend_Db_Expr('NULL'),
												'Employee_Statutory_Limit'=>new Zend_Db_Expr('NULL'),
												'Employer_Statutory_Limit'=>new Zend_Db_Expr('NULL'), 
												'Admin_Charge'=>new Zend_Db_Expr('NULL'),	
												'EDLI_Charge'=>new Zend_Db_Expr('NULL'));

					$updateEtfRetirals = $this->_db->insert($this->_ehrTables->employeeSalaryRetirals,$etfRetiralComponents);
				}
				
				
			}
		}

		$fHInsurance = array();
		$variableInsuranceDetails = array();
		$variableOrgInsDetails = array();
		$fixedInsuranceDetails = array();
	
		if($record['Is_InsuranceEmployee']==1)
		{
			
			$fHInsuranceName = array();
			$insuranceIncludedAllowanceAmt = $this->_dbPayslip->allowances($record['Employee_Id'], $record['Grade_Id'], $record['Location_Id'], $monthlyPayslipId,$record['Basic_Pay'], $salaryMonth,'m', 'Variable Insurance',$midJoin,NULL,null,null,$salaryMigration);
		
			$variableInsuranceGrossSalary = $record['Basic_Pay']+$insuranceIncludedAllowanceAmt[0]+$insuranceIncludedAllowanceAmt[1];

			$varOrgInsRecords = $this->_dbInsurance->getVariableOrgInsuranceRecords($record['Employee_Id'],  $variableInsuranceGrossSalary,NULL, $record['Effective_Date'],NULL,'MonthlySalary');//get variable org insurance details
			if(!is_null($varOrgInsRecords) && count($varOrgInsRecords)>0)
			{
				$variableOrgInsurance = $varOrgInsRecords[0];
				$variableOrgInsDetails = $varOrgInsRecords[1];	
				$esiContributionEndDate = $varOrgInsRecords[2];
			}
			
			$fixedEmpInsDetails = $this->_dbInsurance->getFixedEmpInsuranceDetails($record['Employee_Id'],$record['Effective_Date']); // get all the employee level fixed insurance premium
			$fixedOrgInsDetails = $this->_dbInsurance->getFixedOrgInsuranceDetails($record['Employee_Id']);// get all the org level fixed insurance premium
			
			$variableEmpInsDetails = $this->_dbInsurance->getVariableEmpInsuranceDetails($record['Employee_Id']); // get all the variable org+emp share percent at employee level
			

			//fixed health insurance details
			$getFHIEmpAmt = $this->_dbFHInsurance->getEmpFHInsuranceAmount($record['Employee_Id']);//Employee Share Amount
			$getFHIOrgAmt = $this->_dbFHInsurance->getOrgFHInsuranceAmount();//Org Share Amount
			

			$fixedHealthInsuranceFormId = $this->_dbComment->getFormId('Fixed Health Insurance');
			
			$variableInsuranceFormId 	= $this->_dbComment->getFormId('Insurance');

			$fixedInsuranceFormId 		= $this->_dbComment->getFormId('Premium Contribution');
		
			if(isset($variableOrgInsDetails) && !empty($variableOrgInsDetails))
			{
				foreach ($variableOrgInsDetails as $variableIns)
				{
					$variableInsuranceRetirals = array('Employee_Salary_Id'=>$employeeSalaryId,	
																'Form_Id'=>	$variableInsuranceFormId,
																'Retirals_Id'=>	$variableIns['Insurance_Id'],
																'Retirals_Type'=>'Percentage',	
																'Employee_Share_Percentage'=>$variableIns['Emp_SharePercent'],	
																'Employer_Share_Percentage'=>$variableIns['Org_SharePercent'],
																'Employee_Share_Amount'=>new Zend_Db_Expr('NULL'),		
																'Employer_Share_Amount'=>new Zend_Db_Expr('NULL'),	
																'PF_Employee_Contribution'=>new Zend_Db_Expr('NULL'),
																'PF_Employer_Contribution'=>new Zend_Db_Expr('NULL'),
																'Employee_Statutory_Limit'=>new Zend_Db_Expr('NULL'),
																'Employer_Statutory_Limit'=>new Zend_Db_Expr('NULL'), 
																'Admin_Charge'=>new Zend_Db_Expr('NULL'),	
																'EDLI_Charge'=>new Zend_Db_Expr('NULL'));
										
					$updateVariableInsuranceRetirals = $this->_db->insert($this->_ehrTables->employeeSalaryRetirals,$variableInsuranceRetirals);								
						
				}
			}

			
			/* when the employee level insurance record is present we cant migrate because employee level should be saved at salary template level itself*/
			// if(isset($variableEmpInsDetails) && !empty($variableEmpInsDetails))
			// {
			// 	foreach ($variableEmpInsDetails as $variableIns)
			// 	{
			// 		if(!empty($variableIns['Share']))
			// 		{
			// 			$variableInsuranceRetirals = array('Employee_Salary_Id'=>$employeeSalaryId,	
			// 												'Form_Id'=>	$variableInsuranceFormId,
			// 												'Retirals_Id'=>	$variableIns['Insurance_Id'],
			// 												'Retirals_Type'=>'Percentage',	
			// 												'Employee_Share_Percentage'=>$variableIns['Emp_SharePercent'],	
			// 												'Employer_Share_Percentage'=>$variableIns['Org_SharePercent'],
			// 												'Employee_Share_Amount'=>new Zend_Db_Expr('NULL'),		
			// 												'Employer_Share_Amount'=>new Zend_Db_Expr('NULL'),	
			// 												'PF_Employee_Contribution'=>new Zend_Db_Expr('NULL'),
			// 												'PF_Employer_Contribution'=>new Zend_Db_Expr('NULL'),
			// 												'Employee_Statutory_Limit'=>new Zend_Db_Expr('NULL'),
			// 												'Employer_Statutory_Limit'=>new Zend_Db_Expr('NULL'), 
			// 												'Employee_Pension_Share'=>new Zend_Db_Expr('NULL'),	
			// 												'Admin_Charge'=>new Zend_Db_Expr('NULL'),	
			// 												'EDLI_Charge'=>new Zend_Db_Expr('NULL'));

			// 			$updated = $this->_db->insert($this->_ehrTables->employeeSalaryRetirals,$variableInsuranceRetirals);
			// 		}
			// 	}
			// }

			if(!empty($getFHIEmpAmt))
			{
				for($k=0;$k<count($getFHIEmpAmt);$k++)
				{
					if(!in_array($getFHIEmpAmt[$k]['Insurance_Name'],$fHInsuranceName))
					{
						array_push($fHInsuranceName,$getFHIEmpAmt[$k]['Insurance_Name']);

						
						 $retiralsFixedHealthInsuranceId = $this->_db->fetchOne($this->_db->select()->from(array('FH'=>$this->_ehrTables->fixedHealthIns),array(''))
                                        ->joinInner(array('FHT' => $this->_ehrTables->fixedHealthInsType),'FHT.Insurance_Type_Id=FH.Insurance_Type_Id',array('FH.Insurance_Id'))
										->where('Coverage = 0')
										->where('Period = ?',$getFHIEmpAmt[$k]['Period'])
									    ->where('FH.Insurance_Type_Id = ?',$getFHIEmpAmt[$k]['Insurance_Type_Id'])
                                        ->group('Title'));
					
						/*organization level fixed health insurance is not exist we should not add that employee level fixed health insurance */
						if(!empty($retiralsFixedHealthInsuranceId))
						{
							$fixedHealthInsuranceRetirals = array('Employee_Salary_Id'=>$employeeSalaryId,	
																'Form_Id'=>	$fixedHealthInsuranceFormId,
																'Retirals_Id'=>	$retiralsFixedHealthInsuranceId,
																'Retirals_Type'=>'Amount',	
																'Employee_Share_Percentage'=>new Zend_Db_Expr('NULL'),	
																'Employer_Share_Percentage'=>new Zend_Db_Expr('NULL'),	
																'Employee_Share_Amount'=>$getFHIEmpAmt[$k]['Emp_ShareAmount'],	
																'Employer_Share_Amount'=>$getFHIEmpAmt[$k]['Org_ShareAmount'],	
																'PF_Employee_Contribution'=>new Zend_Db_Expr('NULL'),
																'PF_Employer_Contribution'=>new Zend_Db_Expr('NULL'),
																'Employee_Statutory_Limit'=>new Zend_Db_Expr('NULL'),
																'Employer_Statutory_Limit'=>new Zend_Db_Expr('NULL'), 
																'Admin_Charge'=>new Zend_Db_Expr('NULL'),	
																'EDLI_Charge'=>new Zend_Db_Expr('NULL'));

							$updateFixedHealthInsuranceRetirals = $this->_db->insert($this->_ehrTables->employeeSalaryRetirals,$fixedHealthInsuranceRetirals);
						}
					}
				}
			}                
		
			if(!empty($getFHIOrgAmt))
			{
				for($l=0;$l<count($getFHIOrgAmt);$l++)
				{
					if(!in_array($getFHIOrgAmt[$l]['Insurance_Name'],$fHInsuranceName))
					{
						array_push($fHInsuranceName,$getFHIOrgAmt[$l]['Insurance_Name']);


						$fixedHealthInsuranceRetirals = array('Employee_Salary_Id'=>$employeeSalaryId,	
															'Form_Id'=>	$fixedHealthInsuranceFormId,
															'Retirals_Id'=>	$getFHIOrgAmt[$l]['Insurance_Id'],
															'Retirals_Type'=>'Amount',	
															'Employee_Share_Percentage'=>new Zend_Db_Expr('NULL'),	
															'Employer_Share_Percentage'=>new Zend_Db_Expr('NULL'),	
															'Employee_Share_Amount'=>$getFHIOrgAmt[$l]['Emp_ShareAmount'],	
															'Employer_Share_Amount'=>$getFHIOrgAmt[$l]['Org_ShareAmount'],	
															'PF_Employee_Contribution'=>new Zend_Db_Expr('NULL'),
															'PF_Employer_Contribution'=>new Zend_Db_Expr('NULL'),
															'Employee_Statutory_Limit'=>new Zend_Db_Expr('NULL'),
															'Employer_Statutory_Limit'=>new Zend_Db_Expr('NULL'), 
															'Admin_Charge'=>new Zend_Db_Expr('NULL'),	
															'EDLI_Charge'=>new Zend_Db_Expr('NULL'));
						
						$updateFixedHealthInsuranceRetirals = $this->_db->insert($this->_ehrTables->employeeSalaryRetirals,$fixedHealthInsuranceRetirals);																			

					}
				}
			}

			/* when the employee level insurance record is present we cant migrate because employee level should be saved at salary template level itself*/
			// if(isset($fixedEmpInsDetails) && !empty($fixedEmpInsDetails))
			// {
			// 	foreach ($fixedEmpInsDetails as $fixedIns)
			// 	{
			// 		$fixedInsuranceRetirals = array('Employee_Salary_Id'=>$employeeSalaryId,	
			// 										'Form_Id'=>	$fixedInsuranceFormId,
			// 										'Retirals_Id'=>	$fixedIns['Insurance_Id'],
			// 										'Retirals_Type'=>'Amount',	
			// 										'Employee_Share_Percentage'=>new Zend_Db_Expr('NULL'),	
			// 										'Employer_Share_Percentage'=>new Zend_Db_Expr('NULL'),	
			// 										'Employee_Share_Amount'=>new Zend_Db_Expr('NULL'),	
			// 										'Employer_Share_Amount'=>$fixedIns['Org_ShareAmount'],	
			// 										'PF_Employee_Contribution'=>new Zend_Db_Expr('NULL'),
			// 										'PF_Employer_Contribution'=>new Zend_Db_Expr('NULL'),
			// 										'Employee_Statutory_Limit'=>new Zend_Db_Expr('NULL'),
			// 										'Employer_Statutory_Limit'=>new Zend_Db_Expr('NULL'), 
			// 										'Employee_Pension_Share'=>new Zend_Db_Expr('NULL'),	
			// 										'Admin_Charge'=>new Zend_Db_Expr('NULL'),	
			// 										'EDLI_Charge'=>new Zend_Db_Expr('NULL'));		
					
			// 		$updateFixedInsuranceRetirals = $this->_db->insert($this->_ehrTables->employeeSalaryRetirals,$fixedInsuranceRetirals);										
	
			// 	}
			// }

			if(isset($fixedOrgInsDetails) && !empty($fixedOrgInsDetails))
			{
				foreach ($fixedOrgInsDetails as $fixedIns)
				{
					$fixedInsuranceRetirals = array('Employee_Salary_Id'=>$employeeSalaryId,	
													'Form_Id'=>	$fixedInsuranceFormId,
													'Retirals_Id'=>	$fixedIns['Insurance_Id'],
													'Retirals_Type'=>'Amount',	
													'Employee_Share_Percentage'=>new Zend_Db_Expr('NULL'),	
													'Employer_Share_Percentage'=>new Zend_Db_Expr('NULL'),	
													'Employee_Share_Amount'=>new Zend_Db_Expr('NULL'),	
													'Employer_Share_Amount'=>$fixedIns['Org_ShareAmount'],	
													'PF_Employee_Contribution'=>new Zend_Db_Expr('NULL'),
													'PF_Employer_Contribution'=>new Zend_Db_Expr('NULL'),
													'Employee_Statutory_Limit'=>new Zend_Db_Expr('NULL'),
													'Employer_Statutory_Limit'=>new Zend_Db_Expr('NULL'), 
													'Admin_Charge'=>new Zend_Db_Expr('NULL'),	
													'EDLI_Charge'=>new Zend_Db_Expr('NULL'));	

					$updateFixedInsuranceRetirals = $this->_db->insert($this->_ehrTables->employeeSalaryRetirals,$fixedInsuranceRetirals);										
				}
			}
		}	

		$gratuityFormId 		= $this->_dbComment->getFormId('Gratuity');
		if($record['Is_GratuityEmployee'] == 1)
		{
			$allowanceCount = count($record['Benefit']['Allowance']);
			if($allowanceCount > 0)
			{
				$allowancenames = $record['Benefit']['Allowance'];
			}
			else 
			{
				$allowancenames = array();
			}
			$gratuityAmt = $this->_dbCommonFun->calculateGratuityAmount($record['Basic_Pay'],$allowancenames);

			$gratuityRetirals = array('Employee_Salary_Id'=>$employeeSalaryId,	
											'Form_Id'=>	$gratuityFormId,
											'Retirals_Id'=>	1,
											'Retirals_Type'=>'Amount',	
											'Employee_Share_Percentage'=>new Zend_Db_Expr('NULL'),	
											'Employer_Share_Percentage'=>new Zend_Db_Expr('NULL'),	
											'Employee_Share_Amount'=>new Zend_Db_Expr('NULL'),	
											'Employer_Share_Amount'=>$gratuityAmt,	
											'PF_Employee_Contribution'=>new Zend_Db_Expr('NULL'),
											'PF_Employer_Contribution'=>new Zend_Db_Expr('NULL'),
											'Employee_Statutory_Limit'=>new Zend_Db_Expr('NULL'),
											'Employer_Statutory_Limit'=>new Zend_Db_Expr('NULL'), 
											'Admin_Charge'=>new Zend_Db_Expr('NULL'),	
											'EDLI_Charge'=>new Zend_Db_Expr('NULL'));	

			$updateGratuityRetirals = $this->_db->insert($this->_ehrTables->employeeSalaryRetirals,$gratuityRetirals);
		}
			

		return $updated;

	}


	




	public function getAllowanceDetails($allowanceId)
    {
		$qryAllowance = $this->_db->fetchRow($this->_db->select()->from(array('A'=>$this->_ehrTables->allowances), array('A.Allowance_Id','A.Allowance_Type','A.Percentage','A.Allowance_Type_Id','AT.Period'))												   
											   ->joinLeft(array('AT'=>$this->_ehrTables->allowanceTypes), 'A.Allowance_Type_Id = AT.Allowance_Type_Id', array(''))
											   ->where('Allowance_Id = ?',$allowanceId)); 
		return $qryAllowance;
	}

	public function calculateCtcAmount($record)
	{
		$pfAmount = $this->_ehrTables->getCustomFields('PF Amount');
		$etfAmount = $this->_ehrTables->getCustomFields('ETF Amount');

		if(!empty($pfAmount))
		{
			$pfAmountEnable = $pfAmount['Enable'];
		}
		else 
		{
			$pfAmountEnable = 1;
		}

		if(!empty($etfAmount))
		{
			$etfAmountEnable = $etfAmount['Enable'];
		}
		else 
		{
			$etfAmountEnable = 1;
		}

        $ctcAmount = 0;

		$ctcAmount = floatval($record['Annual_Gross_Salary']);

		// $allowanceCount = count($record['Benefit']['Allowance']);
		// $fixedInsuranceCount = count($record['Benefit']['FixedInsurance']);
		// $variableInsuranceCount = count($record['Benefit']['VariableInsurance']);
	    // $fixedHealthInsuranceCount = count($record['Benefit']['FixedHealthInsurance']);

		if(isset($record['Benefit']['Allowance']))
		{
			$allowanceCount = count($record['Benefit']['Allowance']);
		}
		else
		{
			$allowanceCount =0;
		}

		if(isset($record['Benefit']['FixedInsurance']))
		{
			$fixedInsuranceCount = count($record['Benefit']['FixedInsurance']);
		}
		else
		{
			$fixedInsuranceCount =0;
		}


		if(isset($record['Benefit']['VariableInsurance']))
		{
			$variableInsuranceCount = count($record['Benefit']['VariableInsurance']);
		}
		else
		{
			$variableInsuranceCount =0;
		}

		if(isset($record['Benefit']['FixedHealthInsurance']))
		{
			$fixedHealthInsuranceCount = count($record['Benefit']['FixedHealthInsurance']);
		}
		else
		{
			$fixedHealthInsuranceCount =0;
		}
		
		/*viewMonthlySalary click is reused here to find the ctc amount*/
		if ($allowanceCount > 0 && $record['FBPApplicable'] == 0) // Allowance
		{
			for ($i=0;$i<$allowanceCount;$i++) {
				if ($record['Benefit']['Allowance'][$i][3] == 1) //Bonus
				{
					if ($record['Benefit']['Allowance'][$i][2] == 'Quarterly') {
						$ctcAmount += floatval($record['Benefit']['Allowance'][$i][1] * 4);
					} else if ($record['Benefit']['Allowance'][$i][2] == 'HalfYearly') {
						$ctcAmount += floatval($record['Benefit']['Allowance'][$i][1] * 2);
					} else if ($record['Benefit']['Allowance'][$i][2] == 'Monthly') {
						$ctcAmount += floatval($record['Benefit']['Allowance'][$i][1] * 12);
					} else // if annually
					{
						$ctcAmount += floatval($record['Benefit']['Allowance'][$i][1]);
					}
				}
			}

			if ($record['Gratuity_Amount']) {
				$ctcAmount = floatval($ctcAmount) + floatval($record['Gratuity_Amount']);
			}
		} 
		else if ($record['Gratuity_Amount']) {
			$ctcAmount = floatval($ctcAmount) + floatval($record['Gratuity_Amount']);
		}


		if ($fixedInsuranceCount > 0 || $variableInsuranceCount > 0 ||
			($record['Benefit']['Pf'] > 0 && $pfAmountEnable == 1) || ($record['Benefit']['NPS'] > 0 && $etfAmountEnable == 1)) {
			
			if ($record['Benefit']['Pf'] > 0) {
				if ($this->_orgDetails['Retirals_Based_On_Basic'] == 1) {
					$ctcAmount += floatval($record['Benefit']['Pf']) * 12;
				}

				if ($pfAmountEnable == 1) {

					if ($record['Benefit']['EmployeeAdminCharge'] > 0) {
						if ($this->_orgDetails['Retirals_Based_On_Basic'] == 1) {
							$ctcAmount += (floatval($record['Benefit']['EmployeeAdminCharge']) * 12);
						}
					}

					if ($record['Benefit']['EmployeeEdliCharge'] > 0) {
						if ($this->_orgDetails['Retirals_Based_On_Basic'] == 1) {
							$ctcAmount += (floatval($record['Benefit']['EmployeeEdliCharge']) * 12);
						}
					}
				}
			}

			if ($record['Benefit']['NPS'] > 0) {
				if ($this->_orgDetails['Retirals_Based_On_Basic'] == 1) {
					$ctcAmount += floatval($record['Benefit']['NPS']) * 12;
				}
			}
				for ($i=0;$i<$fixedInsuranceCount;$i++) {
				if ($this->_orgDetails['Retirals_Based_On_Basic'] == 1) {
					if ($record['Benefit']['FixedInsurance'][$i]['Mode'] == 'Quarterly') {
						$ctcAmount += floatval($record['Benefit']['FixedInsurance'][$i]['Premium']) * 4;
					} else if ($record['Benefit']['FixedInsurance'][$i]['Mode'] == 'HalfYearly') {
						$ctcAmount += floatval($record['Benefit']['FixedInsurance'][$i]['Premium']) * 2;
					} else if ($record['Benefit']['FixedInsurance'][$i]['Mode'] == 'Monthly') {
						$ctcAmount += floatval($record['Benefit']['FixedInsurance'][$i]['Premium']) * 12;
					} else // if annually
					{
						$ctcAmount += floatval($record['Benefit']['FixedInsurance'][$i]['Premium']);
					}
				}
			}
				for ($i=0;$i<$variableInsuranceCount;$i++) {
				if ($this->_orgDetails['Retirals_Based_On_Basic'] == 1) {
					if ($record['Benefit']['VariableInsurance'][$i]['Mode'] == 'Quarterly') {
						$ctcAmount += floatval($record['Benefit']['VariableInsurance'][$i]['Premium']) * 4;
					} else if ($record['Benefit']['VariableInsurance'][$i]['Mode'] == 'HalfYearly') {
						$ctcAmount += floatval($record['Benefit']['VariableInsurance'][$i]['Premium']) * 2;
					} else if ($record['Benefit']['VariableInsurance'][$i]['Mode'] == 'Monthly') {
						$ctcAmount += floatval($record['Benefit']['VariableInsurance'][$i]['Premium']) * 12;
					} else // if annually
					{
						$ctcAmount += floatval($record['Benefit']['VariableInsurance'][$i]['Premium']);
					}
				}
			}
		}
		for ($i=0;$i<$fixedHealthInsuranceCount;$i++) {
			if ($this->_orgDetails['Retirals_Based_On_Basic'] == 1) {
				if ($record['Benefit']['FixedHealthInsurance'][$i]['FHInsPeriod'] == 'Monthly') {
					$ctcAmount += floatval($record['Benefit']['FixedHealthInsurance'][$i]['FHInsAmt']) * 12;
				} else if ($record['Benefit']['FixedHealthInsurance'][$i]['FHInsPeriod'] == 'HalfYearly') {
					$ctcAmount += floatval($record['Benefit']['FixedHealthInsurance'][$i]['FHInsAmt']) * 2;
				} else if ($record['Benefit']['FixedHealthInsurance'][$i]['FHInsPeriod'] == 'Quarterly') {
					$ctcAmount += floatval($record['Benefit']['FixedHealthInsurance'][$i]['FHInsAmt']) * 4;
				} else {
					$ctcAmount += floatval($record['Benefit']['FixedHealthInsurance'][$i]['FHInsAmt']);
				}
			}
		}
		$ctcAmount = number_format($ctcAmount,2,'.','');
		
		return $ctcAmount;
	}
	
	//Update monthly salary details
	public function updateMonthlySalary ($monthlySalaryArray, $msEmployeeId, $sessionId, $allowance,$incrementArray=NULL)
	{
		$monthlySalaryExist = $this->_db->fetchRow($this->_db->select()->from(array('J'=>$this->_ehrTables->empJob), array())
												 
												 ->joinLeft(array('S'=>$this->_ehrTables->salary), 'J.Employee_Id = S.Employee_Id',
															array('Salary_EmpId'=>'S.Employee_Id'))
												 
												 ->joinLeft(array('H'=>$this->_ehrTables->hourlyWages), 'J.Employee_Id = H.Employee_Id',
															array('Wage_EmpId'=>'H.Employee_Id'))
												 
												 ->where('J.Employee_Id = ?', $monthlySalaryArray['Employee_Id']));
		
		if ((empty($msEmployeeId) && $monthlySalaryExist['Salary_EmpId'] == NULL) ||
			(!empty($msEmployeeId) && $monthlySalaryExist['Salary_EmpId'] != NULL) &&
			$monthlySalaryExist['Wage_EmpId'] == NULL)
		{
			$dbJobDetail = new Employees_Model_DbTable_JobDetail();
							
			$getGradeLocation = $dbJobDetail->getGradeLocation($monthlySalaryArray['Employee_Id']);
			
			$monthlySalaryArray['Location_Id']   = $getGradeLocation['Location_Id'];
			$monthlySalaryArray['Grade_Id']      = $getGradeLocation['Grade_Id'];
			$monthlySalaryArray['Salary_Recalc'] = 0;
			$monthlySalaryArray['Reason_Id']     = 0;
			
			if (!empty($msEmployeeId))
			{
				$action = 'Edit';
				
				$monthlySalaryArray['Updated_On'] = date('Y-m-d H:i:s');
				$monthlySalaryArray['Updated_By'] = $sessionId;
				
				if($monthlySalaryArray['Is_InsuranceEmployee'] == 1)
				{
					if(!empty($allowance['VariableInsurance']))
					{
						if($allowance['VariableInsurance'][0]['Premium'] != 0)
							$monthlySalaryArray['Reason_Id'] = 5; // check in esic reason table  5 => Out of coverage
					}
				}
				
				$rowAudit = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->salary, array('Employee_Id', 'Location_Id',
														'Represent_Basic_As_Multi_Components','Basic_Component_One','Basic_Component_Two',
														'Grade_Id', 'Basic_Pay', 'Effective_Date', 'Annual_Gross_Salary','Annual_Ctc','Is_ETFEmployee',
														'Monthly_Gross_Salary', 'Is_OvertimeEmployee', 'Overtime_Wage','Employee_Contribution_Rate','Employer_Contribution_Rate',
														'Is_PfEmployee', 'Is_InsuranceEmployee','Flexible_Benefit_Plan_Type',
														'Flexible_Benefit_Plan_Amount','Flexible_Benefit_Plan_Percentage'))
												 ->where('Employee_Id = ?', $monthlySalaryArray['Employee_Id']));
				
				if($rowAudit['Annual_Gross_Salary'] !=  $monthlySalaryArray['Annual_Gross_Salary'] ||
				   $rowAudit['Monthly_Gross_Salary'] != $monthlySalaryArray['Monthly_Gross_Salary'] ||
				   $rowAudit['Represent_Basic_As_Multi_Components'] != $monthlySalaryArray['Represent_Basic_As_Multi_Components'] ||
				   $rowAudit['Basic_Component_One'] != $monthlySalaryArray['Basic_Component_One'] ||
				   $rowAudit['Basic_Component_Two'] != $monthlySalaryArray['Basic_Component_Two'] ||
				   strtotime($rowAudit['Effective_Date']) != strtotime($monthlySalaryArray['Effective_Date']) ||
					$rowAudit['Basic_Pay'] != $monthlySalaryArray['Basic_Pay'] ||
					$rowAudit['Is_PfEmployee'] != $monthlySalaryArray['Is_PfEmployee'] ||
					$rowAudit['Is_ETFEmployee'] != $monthlySalaryArray['Is_ETFEmployee'] ||
					$rowAudit['Is_InsuranceEmployee']!= $monthlySalaryArray['Is_InsuranceEmployee'])
				{
					$archiveDate = date('Y-m-d', strtotime ( '-1 day' , strtotime ( $monthlySalaryArray['Effective_Date'])));
					$salaryHistory    = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->salaryHistory, array('Annual_Gross_Salary','Monthly_Gross_Salary','Archive_Date'))
																					->where('Employee_Id = ?', $monthlySalaryArray['Employee_Id'])
																					->where('Archive_Date = ?', $archiveDate));
				
					if(!empty($salaryHistory))
					{
						$incrementArray = $this->getIncrementDetails($monthlySalaryArray,$salaryHistory,$incrementArray['Increment_Type']);		
						$auditWhere['Employee_Id = ?'] = $monthlySalaryArray['Employee_Id'];
						$auditWhere['Archive_Date = ?'] = $archiveDate;
						$this->_db->update($this->_ehrTables->salaryHistory, array('Modified_On'        =>date('Y-m-d H:i:s'),
																					'Increment_Type'	=>$incrementArray['Increment_Type'],
																					'Increment_Amount'	=>$incrementArray['Increment_Amount'],
																					'Increment_Date'	=>$incrementArray['Increment_Date']),$auditWhere);
					}
					else
					{
						//when the existing salary effective date is equal to current monthlySalary effective we should not insert the history record.
						//if we do that then system could not find which salary details need to pick for salary calculation.
						if($rowAudit['Effective_Date']!=$monthlySalaryArray['Effective_Date'])
						{
							$incrementArray = $this->getIncrementDetails($monthlySalaryArray,$rowAudit,$incrementArray['Increment_Type']);

							$this->_db->insert($this->_ehrTables->salaryHistory, array('Employee_Id'          => $monthlySalaryArray['Employee_Id'],
																					'Location_Id'             => $rowAudit['Location_Id'],
																					'Grade_Id'                => $rowAudit['Grade_Id'],
																					'Represent_Basic_As_Multi_Components'=>$rowAudit['Represent_Basic_As_Multi_Components'],
																					'Basic_Component_One'				 =>$rowAudit['Basic_Component_One'],
																					'Basic_Component_Two'				 =>$rowAudit['Basic_Component_Two'],
																					'Basic_Pay'            => $rowAudit['Basic_Pay'],
																					'Effective_Date'       => $rowAudit['Effective_Date'],
																					'Annual_Gross_Salary'  => $rowAudit['Annual_Gross_Salary'],
																					'Monthly_Gross_Salary' => $rowAudit['Monthly_Gross_Salary'],
																					'Annual_Ctc'           => $rowAudit['Annual_Ctc'],
																					'Archive_Date'         => $archiveDate,
																					'Modified_By'          => $sessionId,
																					'Is_OvertimeEmployee'  => $rowAudit['Is_OvertimeEmployee'],
																					'Overtime_Wage'        => $rowAudit['Overtime_Wage'],
																					'Modified_On'          => date('Y-m-d H:i:s'),
																					'Is_PfEmployee'        => $rowAudit['Is_PfEmployee'],
																					'Is_ETFEmployee'       => $rowAudit['Is_ETFEmployee'],
																					'Employee_Contribution_Rate'        => $rowAudit['Employee_Contribution_Rate'],
																					'Employer_Contribution_Rate'        => $rowAudit['Employer_Contribution_Rate'],
																					'Is_InsuranceEmployee' => $rowAudit['Is_InsuranceEmployee'],
																					'Flexible_Benefit_Plan_Type'       => $rowAudit['Flexible_Benefit_Plan_Type'],
																					'Flexible_Benefit_Plan_Amount'     => $rowAudit['Flexible_Benefit_Plan_Amount'],
																					'Flexible_Benefit_Plan_Percentage' => $rowAudit['Flexible_Benefit_Plan_Percentage'],
																					'Increment_Type'=>$incrementArray['Increment_Type'],
																					'Increment_Amount'=>$incrementArray['Increment_Amount'],
																					'Increment_Date'=>$incrementArray['Increment_Date']));
						}
					}
				}
			
				if(!empty($allowance['ESI_Contribution_End_Date']))
				{
					$esiContributionEndDate = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->salary, array('ESI_Contribution_End_Date'))
																									->where('Employee_Id =?', $monthlySalaryArray['Employee_Id']));
					if(empty($esiContributionEndDate))
					{
						$monthlySalaryArray['ESI_Contribution_End_Date'] = $allowance['ESI_Contribution_End_Date'];
					}																				
				}
				
				$updated = $this->_db->update($this->_ehrTables->salary, $monthlySalaryArray, array('Employee_Id = '. $monthlySalaryArray['Employee_Id']));
			}
			else
			{
				$action = 'Add';
				
				$monthlySalaryArray['Added_On'] = date('Y-m-d H:i:s');
				$monthlySalaryArray['Added_By'] = $sessionId;
				
				$updated = $this->_db->insert($this->_ehrTables->salary, $monthlySalaryArray);
			}
			
			if($this->_orgDetails['Financial_Closure_Tracking']==1)
			{
				$this->_dbFinancialYear     = new Default_Model_DbTable_FinancialYear();
				$assessmentYear 			= $this->_orgDetails['Assessment_Year'];
				$financialStartEndDates 	= $this->_dbFinancialYear->fiscalStartEndDate('PHP',$assessmentYear);
				$fiscalStartDate 			= $financialStartEndDates['finstart'];
				$fiscalEndDate 				= $financialStartEndDates['finend'];
				if($fiscalEndDate >= $monthlySalaryArray['Effective_Date'])
				{
					$setFinancialClosure = $this->_db->update($this->_ehrTables->orgDetails, array('Financial_Closure_Tracking'=>0));
				}
			}
			

            /** Update the DataSetup status **/
            if($updated)
            {
                $updated = $this->_dbCommonFun->updateDataSetupDashboard('Completed','37');                
            }
     
            $salaryCnt = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->salary, new Zend_Db_Expr('COUNT(Employee_Id)')));
            
            if($salaryCnt == 1)
            {
                $act = (($action == 'Edit') ? 'updated' : 'added');
                return array('success' => true, 'msg'=>'Monthly Salary '.$act.' successfully and All the Prerequisites are added', 'type'=>'success');
            }
            else
            {
                /**
                 *	this function will handle
                 *		update system log function
                 *		clear submit lock fucntion
                 *		return success/failure array
                */
                return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
                                                                'action'         => $action,
                                                                'trackingColumn' => $monthlySalaryArray['Employee_Id'],
                                                                'formName'       => 'Monthly Salary',
                                                                'sessionId'      => $sessionId,
                                                                'tableName'      => $this->_ehrTables->salary));
            }
		}
		else
		{
			return array('success' => false, 'msg'=>'Salary already exists either in Monthly Salary or Hourly Wages', 'type'=>'info');
		}
	}

	public function getIncrementDetails($monthlySalaryArray,$rowAudit,$incrementType)
	{
		$incrementArray = array();

		if((empty($incrementType) ||  $incrementType='') || $incrementType=='Gross_Annual_Salary')
		{
			$incrementedSalary = $monthlySalaryArray['Annual_Gross_Salary'];
            $existingSalary    = $rowAudit['Annual_Gross_Salary']; 
			$incrementType     = 'Gross_Annual_Salary';
		}
		else
		{
			$incrementedSalary = $monthlySalaryArray['Monthly_Gross_Salary'];
			$existingSalary    = $rowAudit['Monthly_Gross_Salary']; 
			$incrementType     = 'Gross_Monthly_Salary';
		}

		$incrementAmount = $incrementedSalary - $existingSalary;
						
		if($incrementAmount > 0)
		{
			$incrementArray['Increment_Amount'] = $incrementAmount;     
			$incrementArray['Increment_Type']   = $incrementType;
			$incrementArray['Increment_Date']   = $monthlySalaryArray['Effective_Date'];
		}
		else
		{
			$incrementArray['Increment_Amount'] = 0;     
			$incrementArray['Increment_Type']   = 0;
			$incrementArray['Increment_Date']   = $monthlySalaryArray['Effective_Date'];
		}

		return $incrementArray;
	}
	
	
	//version 1.0=> added Is_PfEmployee, Is_InsuranceEmployee, Is_OvertimeEmployee, Overtime_Wage details
	/**
	 * to show monthly salary history for an employee
	 */
	public function listMonthlySalarysHistory($employeeId)
	{
		$monthlySalaryHistory = $this->_db->fetchAll($this->_db->select()
													 ->from(array('A' => $this->_ehrTables->salaryHistory),
															array('A.Audit_Id','A.Basic_Pay', 'A.Annual_Gross_Salary',
																  'A.Monthly_Gross_Salary',
																  'Is_PfEmployee'=>new Zend_Db_Expr('CASE when A.Is_PfEmployee = 1 THEN \'Yes\' ELSE \'No\' END'),
																  'Is_InsuranceEmployee'=>new Zend_Db_Expr('CASE when A.Is_InsuranceEmployee = 1 THEN \'Yes\' ELSE \'No\' END'),
																  'Is_OvertimeEmployee'=>new Zend_Db_Expr('CASE when A.Is_OvertimeEmployee is NULL THEN \'No Overtime\' WHEN A.Is_OvertimeEmployee = "GRA"
																										  THEN \'Grade\' ELSE \'Employee\' END'),
																  'Overtime_Wage'=>new Zend_Db_Expr('CASE when A.Overtime_Wage > 0 THEN A.Overtime_Wage ELSE "-" END'),
																  new Zend_Db_Expr("date_format(A.Effective_Date, '".$this->_orgDF['sql']."') as View_Effective_Date"),
																  new Zend_Db_Expr("date_format(A.Archive_Date, '".$this->_orgDF['sql']."') as Archive_Date"),
																  new Zend_Db_Expr("date_format(A.Modified_On, '".$this->_orgDF['sql']." %T') as ModifiedDate")))
													 
													 ->joinInner(array('P1'=>$this->_ehrTables->empPersonal),
																 'P1.Employee_Id=A.Employee_Id' ,
																 array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P1.Emp_First_Name, ' ', P1.Emp_Last_Name)")))
													 
													 ->joinInner(array('P2'=>$this->_ehrTables->empPersonal),
																 'P2.Employee_Id=A.Modified_By',
																 array('Modified_By'=>new Zend_Db_Expr("CONCAT(P2.Emp_First_Name, ' ', P2.Emp_Last_Name)")))
													 
													 ->where('A.Employee_Id = ?', $employeeId));
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->salaryHistory, new Zend_Db_Expr('COUNT(Employee_Id)'))
									   ->where('Employee_Id = ?', $employeeId));
		
		/**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $monthlySalaryHistory);
	}
	
	/**
	 * to delete monthly salary details for an employee
	 */
	public function deleteMonthlySalary($employeeId, $sessionId)
	{
		$exInBonus = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empBonus, new Zend_Db_Expr('COUNT(Bonus_Id)'))
										  ->where('Employee_Id = ?', $employeeId)
										  ->where("Approval_Status != 'Paid' AND Approval_Status != 'Rejected'"));
		
		$exInPayslip = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->monthlyPayslip, new Zend_Db_Expr('COUNT(Payslip_Id)'))
											 ->where('Employee_Id = ?', $employeeId));
		
		if($exInBonus == 0 && $exInPayslip == 0)
		{
			$deleted = 0;
			
			$salaryLock = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->salary, 'Lock_Flag')
					->where('Employee_Id = ?', $employeeId));
			
			if($salaryLock == 0)
			{
				$where['Employee_Id = ?'] = $employeeId;
				
				$deleted = $this->_db->delete($this->_ehrTables->salary,$where);
				
				if ( $deleted )
				{
					$this->_db->delete($this->_ehrTables->salaryHistory,$where);
				}
			}
			
			/**
			 *	delete activity for common function
			 *		1)check lock is exist or not.
			 *			If lock is exist then show error message like employee is open record for update.
			 *		2)If No lockflag then process delete activity
			 *		3)Update delete activity in system log
			 *		4)return success/failure message
			*/
			return $this->_dbCommonFun->deleteRecord (array('deleted'       => $deleted,
														   'lockFlag'       => $salaryLock,
														   'formName'       => 'Monthly Salary',
														   'trackingColumn' => $employeeId,
														   'sessionId'      => $sessionId));
		}
		else
		{
			return array('success'=>false, 'msg'=>'<div>Salary record cannot be deleted at the moment as the employee has got one or more of the following exists.
			<li>	1) Payslip has been generated for this employee.</li>
			<li>	2) Bonus has been added for this employee.</li>
			<li>	3) Flexible benefit plan declaration.</li></div>
			Please talk to your admin for next steps', 'type'=>'info');
		}
	}
	
	/**
	 * to show monthly salary detail for an employee
	 * V0.6 fetch salary level overtime wage for employee
	 */
	public function viewMonthlySalary($employeeId,$benefitRequired=1)
	{
		$qryViewSalary = $this->_db->select()
								->from(array('S'=>$this->_ehrTables->salary),
									   array('S.Added_By','S.Employee_Id','S.Grade_Id','S.Location_Id','S.Basic_Pay','S.Is_OvertimeEmployee',
											 'S.Overtime_Wage', 'S.Monthly_Gross_Salary', 'S.Annual_Gross_Salary',
											 'S.Is_PfEmployee', 'S.Is_InsuranceEmployee','S.Updated_By','S.Is_ETFEmployee','S.Salary_Recalc',
											 'S.Effective_Date as SPHP_Effective_Date',
											 new Zend_Db_Expr("date_format(S.Effective_Date, '".$this->_orgDF['sql']."') as Effective_Date"),
											 new Zend_Db_Expr("date_format(S.Added_On, '".$this->_orgDF['sql']." at %T') as Added_On"),
											 new Zend_Db_Expr("date_format(S.Updated_On, '".$this->_orgDF['sql']." at %T') as Updated_On")))
								
								->joinInner(array('P1'=>$this->_ehrTables->empPersonal), 'S.Employee_Id=P1.Employee_Id',
											array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P1.Emp_First_Name, ' ', P1.Emp_Last_Name)")))
								
								->joinInner(array('P2'=>$this->_ehrTables->empPersonal), 'S.Added_By=P2.Employee_Id',
											array('Added_ByName'=>new Zend_Db_Expr("CONCAT(P2.Emp_First_Name, ' ', P2.Emp_Last_Name)")))
								
								->joinLeft(array('P3'=>$this->_ehrTables->empPersonal), 'S.Updated_By=P3.Employee_Id',
										   array('Updated_ByName'=>new Zend_Db_Expr("CONCAT(P3.Emp_First_Name, ' ', P3.Emp_Last_Name)")))
								
								->joinInner(array('G'=>$this->_ehrTables->empGrade), 'S.Grade_Id=G.Grade_Id', array('OvertimeFixedAmount'))
								
								->where('S.Employee_Id = ?', $employeeId);
		
		$rowViewSalary = $this->_db->fetchRow($qryViewSalary);
		
		if (!empty($rowViewSalary) && $benefitRequired==1)
		{
			$fbpapplicable = $this->_ehrTables->getFlexiBenefitPlan();
			
			$formName = ($fbpapplicable == 1) ? 'SAL' : NULL;
			
			$benefit = $this->getBenefits($employeeId,
										  $rowViewSalary['Annual_Gross_Salary'],
										  $rowViewSalary['Is_PfEmployee'],
										  $rowViewSalary['Is_InsuranceEmployee'],
										  '',
                                          NULL ,
										  NULL ,
										  $rowViewSalary['Effective_Date'],
										  $rowViewSalary['Monthly_Gross_Salary'],
										  $formName,
                                          '',
                                          '',
                                          $rowViewSalary['Is_ETFEmployee']);
			//$benefit = $this->getBenefits($employeeId, $rowViewSalary['Annual_Gross_Salary'], $rowViewSalary['Is_PfEmployee'], $rowViewSalary['Is_InsuranceEmployee'], NULL, NULL, $rowViewSalary['Effective_Date']);
			
			$rowViewSalary['Benefit'] = $benefit;
		}
		
		return $rowViewSalary;
	}
	
	/**
     * get employee's date of join
     * @param unknown_type $empId
     */
	public function getDateOfJoin($empId, $salarytype, $action)
	{
		if($action == 'Edit')
		{
			//$maxPayslipMonth = $this->_dbPayslip->maxPayslipMonth($empId,"monthly"); //getting the last payslip month
			$maxPayslipMonth = $this->_dbPayslip->maxPayslipMonth($empId,$salarytype); //getting the last payslip month
			
			if(!empty($maxPayslipMonth)) //get the last salary day
			{
				$maxPayslipMonth = explode(',',$maxPayslipMonth);
				$lastSalaryDay = $this->_dbPayslip->getSalaryDay($maxPayslipMonth[0],$maxPayslipMonth[1]);
				
				return date('Y-m-d',strtotime(date("Y-m-d", strtotime($lastSalaryDay['Last_SalaryDate'])) . " + 1 day"));
			}
			else
			{
				//get the monthly and hourly salary details
				if($salarytype=='Monthly')
				{
					$viewSalary = $this->viewMonthlySalary($empId,0);
				}
				else 
				{
					$viewSalary = $this->viewHourlyWages($empId,0);
				}
				$dateofJoin =  $this->_db->fetchOne($this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),'J.Date_Of_Join')
									 ->joinInner(array('P'=>$this->_ehrTables->empPersonal),'J.Employee_Id = P.Employee_Id',array())
									 ->where('J.Employee_Id = ?', $empId)
									 ->where('P.Form_Status =1')
									 ->where('J.Emp_Status Like ?', 'Active'));

				if(!empty($dateofJoin) && !empty($viewSalary['SPHP_Effective_Date']))
				{
					//when the payslip is not exist but there might be chance of having effective date is greater than date of join.
					//when the effective date is greater than date of join we need to return as effective date.
					if($viewSalary['SPHP_Effective_Date'] > $dateofJoin)
					{
						return $viewSalary['SPHP_Effective_Date'];
					}
					else 
					{
						return $dateofJoin;
					}
				}
				elseif(!empty($viewSalary['SPHP_Effective_Date'])) 
				{
					return $viewSalary['SPHP_Effective_Date'];
				}
				elseif(!empty($dateofJoin)) 
				{
					return $dateofJoin;
				}					 
			}
		}
		else
		{
			return $this->_db->fetchOne($this->_db->select()->from(array('J'=>$this->_ehrTables->empJob), 'J.Date_Of_Join')
									 
									 ->joinInner(array('P'=>$this->_ehrTables->empPersonal),'J.Employee_Id = P.Employee_Id',array())
									 
									 ->where('J.Employee_Id = ?', $empId)
									 ->where('P.Form_Status =1'));									 
		}
	}
	
	public function getGradeWiseOvertimeDetails($empId)
	{

		 $overtimeDetails = $this->_db->fetchRow($this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),'')
									 ->joinInner(array('P'=>$this->_ehrTables->empPersonal),'J.Employee_Id = P.Employee_Id',array())
									 ->joinInner(array('D'=>$this->_ehrTables->designation),'D.Designation_Id = J.Designation_Id',array())
									 ->joinInner(array('EG'=>$this->_ehrTables->empGrade),'EG.Grade_Id = D.Grade_Id',array('Overtime_Allocation','Overtime_Wage_Index','OvertimeFixedAmount'))
									 ->where('J.Employee_Id = ?', $empId)
									 ->where('P.Form_Status =1')
									 ->where('J.Emp_Status Like ?', 'Active'));
		return	$overtimeDetails;						 


	}
	public function getSalaryDefinition()
	{
		$orgCode = $this->_ehrTables->getOrgCode();
		$salaryDefinition = $this->_dbOrgDetail->viewOrgDetail($orgCode);
		
		return $salaryDefinition;
	}

	//version 0.9=> changed the basic pay calc based on 'retirals based on basic' in org settings
	public function getBenefits($employeeId, $annualSalary, $pfVal, $insuranceVal, $gratuityVal='', $gradeId='', $locationId='',$effectiveDate = NULL,
								$definedBasic = NULL,$formName = NULL,$FBPAmount = NULL,$fbpAllowance=NULL,$etfVal=NULL,$employeeContributionRate=NULL,$employerContributionRate=NULL,$providentFundDetails=NULL,$providentFundSettings=NULL)
	{
		$FBPAmount = floatval($FBPAmount);
		$maxallowanceAmount = $allAllowanceAmount = 0;
		$orgCode = $this->_ehrTables->getOrgCode();
		$salaryCalcDetail = $this->_dbOrgDetail->viewOrgDetail($orgCode);
		$payrollGeneralSettings = $this->getPayrollGeneralSettings();
		// Safe check for Enable_Salary_Template flag to prevent null pointer exceptions
		$enableSalaryTemplate = (isset($payrollGeneralSettings['Enable_Salary_Template']) && !empty($payrollGeneralSettings['Enable_Salary_Template'])) ? 1 : 0;
		if(!empty($employeeId) && !empty($annualSalary))
		{
			if(!is_null($effectiveDate))
			{
				//changed 09/12/2013 format in to 2013-12-09
				$effectiveDate = date('Y-m-d', strtotime($this->_ehrTables->changeDateformat($effectiveDate)));
			}
			
			$benefitEmp = $this->_dbPersonal->isBenefitsApplicable($employeeId,$effectiveDate);
			$basicPay = 0;
			/** Based on the basic pay and gross monthly pay only, formula based allowance amount will to be calculated. So Gross basic pay should be zero
			incase of 'All' in salary definition **/
			$grossBasicpay = (!empty($annualSalary) && $salaryCalcDetail['Gross_Annual_Definition'] != 2) ? $annualSalary/12 : 0;
			$insuranceAmt = 0;
			$currencySymbol = $this->_dbPersonal->currencySymbol($employeeId);
			$variableInsuranceDetails = array();
			$variableOrgInsDetails = array();
			$fixedInsuranceDetails = array();
			$esiContributionEndDate = '';
			$allowancenames = array();
			$allowanceAmt = 0; //annual allowance amt
			$allowancePercent = 0;
			$amount = 0;
			$totalAllowanceAmt = 0;
			$pfPercent = 0;
            $etfPercent = 0;
			$pfValue = 0;
            $etfValue = 0;
			$fixedOrgInsurance = 0;
			$fixedEmpInsurance = 0;
			$variableOrgInsurance = 0;
			$variableEmpInsurance = 0;
            $gratuityAmt = 0;
            $FHInsPeriod = '';

			$pfIncludedAllowancePercent = 0;
            $etfIncludedAllowancePercent = 0;
			$insIncludedAllowancePercent = 0;
			$pfIncludedFixedAllowance = 0;
            $etfIncludedFixedAllowance = 0;
			$insIncludedFixedAllowance = 0;			
			
			$formulaBasedAllowanceAmount = number_format(0,2,'.','');
			$formulaBasedAllowanceArr = array();
			$fHInsurance = array();
            $fHInsAmt = 0;
            $arrAllowance = array();
			if($benefitEmp == 1)
			{
				if($formName == null){					
					$arrAllowance = $this->getAllowances($employeeId,'','',NULL,NULL,NULL,NULL,$effectiveDate);
				}

				if(!empty($insuranceVal) && $insuranceVal == 1 && $enableSalaryTemplate == 0)
				{
					$dbInsurance = new Payroll_Model_DbTable_Insurance();
                    $dbFHInsurance = new Payroll_Model_DbTable_FixedHealthInsurance();
					$fixedOrgInsurance += $dbInsurance->getFixedOrgInsuranceAmt($employeeId); //get fixed org insurance premium
					$fixedEmpInsurance += $dbInsurance->getFixedEmpInsuranceAmt($employeeId,$effectiveDate);//get fixed emp insurance premium
					//$variableOrgInsurance += $dbInsurance->getVariableOrgInsurancePercent($employeeId);//get variable org insurance percent
					
					
					//newly added function which return total variable org insurance percent and the variable org insurance details
						
					$varOrgInsRecords = $dbInsurance->getVariableOrgInsuranceRecords($employeeId,  $grossBasicpay,NULL, $effectiveDate,NULL,'MonthlySalary');//get variable org insurance details
					if(!is_null($varOrgInsRecords) && count($varOrgInsRecords)>0)
					{
						$variableOrgInsurance = $varOrgInsRecords[0];
						$variableOrgInsDetails = $varOrgInsRecords[1];	
						$esiContributionEndDate = $varOrgInsRecords[2];
					}
					
					$fixedEmpInsDetails = $dbInsurance->getFixedEmpInsuranceDetails($employeeId,$effectiveDate); // get all the employee level fixed insurance premium
					
					$fixedOrgInsDetails = $dbInsurance->getFixedOrgInsuranceDetails($employeeId);// get all the org level fixed insurance premium
					$variableEmpInsDetails = $dbInsurance->getVariableEmpInsuranceDetails($employeeId); // get all the variable org+emp share percent at employee level
                    
				}
                
				$grossMinusFixedAllowance = $grossBasicpay;
				$grossBasicpay -= ($fixedOrgInsurance+$fixedEmpInsurance);

			if(!empty($insuranceVal) && $insuranceVal == 1 && $enableSalaryTemplate == 0)
            {
                $dbFHInsurance = new Payroll_Model_DbTable_FixedHealthInsurance();
                //Get Fixed Health Insurance Premium.
                $getFHIEmpAmt = $dbFHInsurance->getEmpFHInsuranceAmount($employeeId);//Employee Share Amount
                $getFHIOrgAmt = $dbFHInsurance->getOrgFHInsuranceAmount();//Org Share Amount
			
		//	     $basicPay = $basicMonthlyPay;
                
                 $fHIEmpAmt = $fHIOrgAmt = 0;
                 $fHIEmpAmtPeriod = $fHIOrgAmtPeriod = '';
                
                 $fHInsuranceName = array();
                
                
                if(!empty($getFHIEmpAmt))
                {
                    for($k=0;$k<count($getFHIEmpAmt);$k++)
                    {
                        if(!in_array($getFHIEmpAmt[$k]['Insurance_Name'],$fHInsuranceName))
                        {
                            if($getFHIEmpAmt[$k]['Period'] == 'HalfYearly')
                            {
                                $fHIEmpAmt = number_format($getFHIEmpAmt[$k]['FHInsuranceEmpAmount']/6,2,'.','');
                            }
                            else if($getFHIEmpAmt[$k]['Period'] == 'Quarterly')
                            {
                                $fHIEmpAmt = number_format($getFHIEmpAmt[$k]['FHInsuranceEmpAmount']/3,2,'.','');
                            }
                            else if($getFHIEmpAmt[$k]['Period'] == 'Annually')
                            {
                                $fHIEmpAmt = number_format($getFHIEmpAmt[$k]['FHInsuranceEmpAmount']/12,2,'.','');
                            }
                            else
                            {
                                $fHIEmpAmt = number_format($getFHIEmpAmt[$k]['FHInsuranceEmpAmount'],2,'.','');
                            }
                            $fHIEmpAmtPeriod = $getFHIEmpAmt[$k]['Period'];
                            
                            
                            $fHInsEmpAmt = $getFHIEmpAmt[$k]['FHInsuranceEmpAmount'];
                            
                            $fHInsuranceEmpAmt = array('FHInsName'=> $getFHIEmpAmt[$k]['Insurance_Name'],
                                                       'FHInsAmt'=> $fHInsEmpAmt,
                                                       'FHInsPeriod' => $fHIEmpAmtPeriod);
                            
                            array_push($fHInsurance,$fHInsuranceEmpAmt);
                            
                            $fHInsAmt += $fHIEmpAmt;
                            array_push($fHInsuranceName,$getFHIEmpAmt[$k]['Insurance_Name']);
                        }
                    }
                }                
                
                if(!empty($getFHIOrgAmt))
                {
                    for($l=0;$l<count($getFHIOrgAmt);$l++)
                    {
                        if(!in_array($getFHIOrgAmt[$l]['Insurance_Name'],$fHInsuranceName))
                        {
                            if($getFHIOrgAmt[$l]['Period'] == 'HalfYearly')
                            {
                                $fHIOrgAmt = number_format($getFHIOrgAmt[$l]['FHInsuranceOrgAmount']/6,2,'.','');
                            }
                            else if($getFHIOrgAmt[$l]['Period'] == 'Quarterly')
                            {
                                $fHIOrgAmt = number_format($getFHIOrgAmt[$l]['FHInsuranceOrgAmount']/3,2,'.','');
                            }
                            else if($getFHIOrgAmt[$l]['Period'] == 'Annually')
                            {
                                $fHIOrgAmt = number_format($getFHIOrgAmt[$l]['FHInsuranceOrgAmount']/12,2,'.','');
                            }
                            else
                            {
                                $fHIOrgAmt = number_format($getFHIOrgAmt[$l]['FHInsuranceOrgAmount'],2,'.','');
                            }
                            $fHIOrgAmtPeriod = $getFHIOrgAmt[$l]['Period'];
                            
                            //if($salaryCalcDetail['Retirals_Based_On_Basic'] == 1)
                            //    $fHInsOrgAmt = $fHIOrgAmt;
                            //else
                                $fHInsOrgAmt = $getFHIOrgAmt[$l]['FHInsuranceOrgAmount'];
                            $fHInsuranceOrgAmt = array('FHInsName'=> $getFHIOrgAmt[$l]['Insurance_Name'],
                                                       'FHInsAmt'=> $fHInsOrgAmt,
                                                       'FHInsPeriod' => $fHIOrgAmtPeriod);
                            
                            array_push($fHInsurance,$fHInsuranceOrgAmt);
                            
                            $fHInsAmt += $fHIOrgAmt;
                            
                            array_push($fHInsuranceName,$getFHIOrgAmt[$l]['Insurance_Name']);
                        }
                    }
                }                
            }

				if($salaryCalcDetail['Retirals_Based_On_Basic'] == 1 && $salaryCalcDetail['Gross_Annual_Definition'] == 2)
				{
					$basicPay = $definedBasic;
				}
				

				if($formName == null && count($arrAllowance)>0)
				{
					$allowanceBonusAmt = $fbpamount = $empKittyExists = 0;
					
					foreach($arrAllowance as $allowance)
					{
						if($allowance['Allowance_Mode'] == 'Non Bonus') // non-bonus
						{
							if($allowance['Formula_Based'] =='No')
							{
								if($allowance['Percentage']!=NULL)
								{
									if($allowance['Period']=='Quarterly')
									{
										$amount = number_format(($basicPay*($allowance['Percentage']/100))*3,2,'.','');
									}
									elseif($allowance['Period']=='HalfYearly')
									{
										$amount = number_format(($basicPay*($allowance['Percentage']/100))*6,2,'.','');
									}
									elseif($allowance['Period']=='Monthly')
									{
										$amount = number_format(($basicPay*($allowance['Percentage']/100))*1,2,'.','');
									}
									else // if annually
									{
										$amount = number_format(($basicPay*($allowance['Percentage']/100))*12,2,'.','');
									}
								}
								else
								{
									$amount = number_format($allowance['Amount'],2,'.','');
								}
								

								
								//total allowance amount calculated only from allowance having non bonus mode
								if($allowance['Period']=='Quarterly')
								{
									$perMonthAllowance = ($amount/3);
									$allowanceAmt += $perMonthAllowance; //allowance amt for yr
								}
								elseif($allowance['Period']=='HalfYearly')
								{
									$perMonthAllowance = ($amount/6);
									$allowanceAmt += $perMonthAllowance;//allowance amt for yr
								}
								elseif($allowance['Period']=='Monthly')
								{
									$perMonthAllowance = $amount;
									$allowanceAmt += $perMonthAllowance;//allowance amt for yr
								}
								else // if annually
								{
									$perMonthAllowance = ($amount/12);
									$allowanceAmt += $perMonthAllowance;
								}
	
							
								//when the benfit association is tagged as bonus then those allowance amounts are considered for Bonus Calculation  

								 if(in_array('Bonus',explode(",",$allowance['BenefitForms'])))
								 {
									 $allowanceBonusAmt = $allowanceBonusAmt+$perMonthAllowance;
								 }
								
								$allowanceArr = array($allowance['Allowance_Name'], $amount, $allowance['Period'],
													$allowance['Allowance_Mode'], $allowance['Allowance_Id']);
								
								array_push($allowancenames, $allowanceArr);
							}
							else
							{
								
								$allowanceArr = array($allowance['Allowance_Name'], 0, $allowance['Period'],
														$allowance['Allowance_Mode'], $allowance['Allowance_Id']);

								//benefit association value for formula based allowance is stored in formulaBasedBenefitAssociation variable				  
								$formulaBasedBenefitAssociation = $allowance['BenefitForms'];  			
								array_push($formulaBasedAllowanceArr, $allowanceArr);
							}
						}
					}
					
					
					/** Formula based allowance calculation for normal salary **/
					/** If Retiral based on basic is true and salary definition is 2, then calculate the formula based allowance
					amount from basic pay + allowances amt **/
					if($salaryCalcDetail['Retirals_Based_On_Basic'] == 1 && $salaryCalcDetail['Gross_Annual_Definition'] == 2)
					{
						/** If formula based allowance exists **/
						if(!empty($formulaBasedAllowanceArr) && count($formulaBasedAllowanceArr) > 0)
						{
							/** Calculate the formula based allowance amount **/
							$formulaBasedAllowanceAmount = $this->calculateFormulaBasedAllowanceAmount($annualSalary,$basicPay,$allowanceAmt);
							
							/** sum of formula based allowance amount and rem allowances amount **/
							if($formulaBasedAllowanceAmount >0)
							{
								$allowanceAmt += $formulaBasedAllowanceAmount;
							}
							
							if($formulaBasedAllowanceArr[0] != ''){
								$formulaBasedAllowanceArr = $formulaBasedAllowanceArr[0];	
							}						
							
							/** assign the formula based allowance amount **/
							$formulaBasedAllowanceArr[1] = number_format($formulaBasedAllowanceAmount,2,'.','');
							
								//when the benfit association is tagged as bonus then those allowance amounts are considered for Bonus Calculation  
								if(in_array('Bonus',explode(",",$formulaBasedBenefitAssociation)))
								{
								$allowanceBonusAmt = $allowanceBonusAmt + $formulaBasedAllowanceArr[1];
								}	
							/** Include the formula based allowance arr in allowances **/
							array_push($allowancenames, $formulaBasedAllowanceArr);
						}
					}
					
					$allowanceBonusAmt = $allowanceBonusAmt + $basicPay;
					
					foreach($arrAllowance as $allowanceBonusType)
					{
						if($allowanceBonusType['Allowance_Mode'] == 'Bonus') //bonus
						{
							if($allowanceBonusType['Percentage']!=NULL)
							{
								if($allowanceBonusType['Period']=='Quarterly')
								{
									$amount = number_format(($allowanceBonusAmt*($allowanceBonusType['Percentage']/100))*3,2,'.','');
								}
								elseif($allowanceBonusType['Period']=='HalfYearly')
								{
									$amount = number_format(($allowanceBonusAmt*($allowanceBonusType['Percentage']/100))*6,2,'.','');
								}
								elseif($allowanceBonusType['Period']=='Monthly')
								{
									$amount = number_format(($allowanceBonusAmt*($allowanceBonusType['Percentage']/100))*1,2,'.','');
								}
								else // if annually
								{
									$amount = number_format(($allowanceBonusAmt*($allowanceBonusType['Percentage']/100))*12,2,'.','');
								}
							}
							else
							{
								$amount = number_format($allowanceBonusType['Amount'],2,'.','');
							}
						
							$allowanceBonusArr = array($allowanceBonusType['Allowance_Name'], $amount, $allowanceBonusType['Period'],
											  $allowanceBonusType['Allowance_Mode'], $allowanceBonusType['Allowance_Id']);							
							
							array_push($allowancenames, $allowanceBonusArr);
						}
					}
				}

				if(!empty($gratuityVal) && $gratuityVal == 1)
                {
					$gratuityAmt = $this->_dbCommonFun->calculateGratuityAmount($definedBasic,$allowancenames);
                }
            
				if(isset($fixedEmpInsDetails) && !empty($fixedEmpInsDetails))
				{
					foreach ($fixedEmpInsDetails as $fixedIns)
					{
						if($salaryCalcDetail['Retirals_Based_On_Basic'] == 1)
						{
							$insuranceAmt += ($fixedIns['Org_ShareAmount']/$fixedIns['PaymentMode_Id']);
							$fixedInsuranceDetails[]=array('Insurance_Name'=>$fixedIns['Insurance_Name'],
														   'Premium'=>$fixedIns['Org_ShareAmount'],
									'Mode'=>$fixedIns['Payment_Frequency']);
						}
						else
						{
							$insuranceAmt += ($fixedIns['Premium']/$fixedIns['PaymentMode_Id']);
							$fixedInsuranceDetails[]=array('Insurance_Name'=>$fixedIns['Insurance_Name'],
														   'Premium'=>$fixedIns['Premium'],
									'Mode'=>$fixedIns['Payment_Frequency']);
						}
					}
				}
			
				if(isset($fixedOrgInsDetails) && !empty($fixedOrgInsDetails))
				{
					foreach ($fixedOrgInsDetails as $fixedIns)
					{
						if($salaryCalcDetail['Retirals_Based_On_Basic'] == 1)
						{
							$insuranceAmt += ($fixedIns['Org_ShareAmount']/$fixedIns['PaymentMode_Id']);
							
							$fixedInsuranceDetails[]=array('Insurance_Name'=>$fixedIns['Insurance_Name'],
														   'Premium'=>$fixedIns['Org_ShareAmount'],
														   'Mode'=>$fixedIns['Payment_Frequency']);							
						}
						else
						{
							$insuranceAmt += ($fixedIns['Premium']/$fixedIns['PaymentMode_Id']);
							
							$fixedInsuranceDetails[]=array('Insurance_Name'=>$fixedIns['Insurance_Name'],
														   'Premium'=>$fixedIns['Premium'],
														   'Mode'=>$fixedIns['Payment_Frequency']);
						}
					}
				}
				
				
				if($formName == null){
					//getting the total allowance amount that includes insurance
					$getAllowanceAmt = $this->getAllowances($employeeId,NULL,NULL, $basicPay, 'Variable Insurance',NULL,NULL,$effectiveDate);

					//basic pay + allowance that included with insurance
					$basicPlusAllowance = $basicPay + $getAllowanceAmt;
					
					/** For contribution inside the gross **/
					if(($salaryCalcDetail['Retirals_Based_On_Basic'] == 1)
					   && $salaryCalcDetail['Gross_Annual_Definition'] == 2)
					{
						/** If formula based allowance exists **/
						if(!empty($formulaBasedAllowanceArr) && count($formulaBasedAllowanceArr) > 0)
						{
							$getFormulaBasedAllowancesBenefits = $this->_dbCommonFun->getAllowanceBenefitAssociation($formulaBasedAllowanceArr[4]);
							
							/** If insurance is tagged with formula based allowance, the include the formula based allownace amount with basicplusallowance amount **/	
							if(in_array('Variable Insurance',$getFormulaBasedAllowancesBenefits))
							{
								$basicPlusAllowance += $formulaBasedAllowanceAmount;
							}
						}
					}
				}
				else{
					$basicPlusAllowance = $basicPay;	
				}
				
				if(isset($variableEmpInsDetails) && !empty($variableEmpInsDetails))
				{
					foreach ($variableEmpInsDetails as $variableIns)
					{
						if(!empty($variableIns['Share']))
						{
							if($salaryCalcDetail['Retirals_Based_On_Basic'] == 1)
							{
								$premium = number_format(round(($variableIns['Org_SharePercent']/100)*$basicPlusAllowance,2),2,'.','');
								$insuranceAmt += ($premium/$variableIns['PaymentMode_Id']);
								$variableInsuranceDetails[]=array('Insurance_Name'=>$variableIns['Insurance_Name'],
																  'Premium'=>$premium,
										'Mode'=>$variableIns['Payment_Frequency']);
							}
							else
							{
								$premium = number_format(round(($variableIns['Share']/100)*$basicPlusAllowance,2),2,'.','');
								$insuranceAmt += ($premium/$variableIns['PaymentMode_Id']);
								$variableInsuranceDetails[]=array('Insurance_Name'=>$variableIns['Insurance_Name'],
																  'Premium'=>$premium,
										'Mode'=>$variableIns['Payment_Frequency']);
							}
						}
					}
				}
				
				//if 'Retirals Based On Basic' is true, then salary limit for getting the variable insurance at org level can be 'Basic + All Allowance Amount'
				if(!empty($insuranceVal) && $insuranceVal == 1 && $enableSalaryTemplate == 0)
				{
					$variableOrgInsurance = 0;
					$variableOrgInsDetails = array();
				
					if($formName == null){	
						$insuranceSalaryLimit = $basicPlusAllowance;
					}
					else{
						$insuranceSalaryLimit = $basicPay;
					}
					
					$varOrgInsRecords = $dbInsurance->getVariableOrgInsuranceRecords($employeeId, $insuranceSalaryLimit, NULL, $effectiveDate,NULL,'MonthlySalary');//get variable org insurance details
					if(!is_null($varOrgInsRecords) && count($varOrgInsRecords)>0)
					{
						$variableOrgInsurance = $varOrgInsRecords[0];
						$variableOrgInsDetails = $varOrgInsRecords[1];
						$esiContributionEndDate = $varOrgInsRecords[2];
							
					}
				}
				
				if(isset($variableOrgInsDetails) && !empty($variableOrgInsDetails))
				{
					foreach ($variableOrgInsDetails as $variableIns)
					{
						if($salaryCalcDetail['Retirals_Based_On_Basic'] == 1)
						{
							$premium = number_format(round(($variableIns['Org_SharePercent']/100)*$basicPlusAllowance,2),2,'.','');
							$insuranceAmt += ($premium/$variableIns['PaymentMode_Id']);
							$variableInsuranceDetails[]=array('Insurance_Name'=>$variableIns['Insurance_Name'], 'Premium'=>$premium,
									'Mode'=>$variableIns['Payment_Frequency']);
						}
					}
				}
				    
                    if(!empty($etfVal) && $etfVal==1)
					{
						$dbEtf = new Payroll_Model_DbTable_ETF();
						if($formName == null){							
							$etfAssocAllowance = $this->getAllowances($employeeId,NULL,NULL, $basicPay, 'NPS', NULL,NULL,$effectiveDate);
							
							$etfSalaryLimit = $basicPay + $etfAssocAllowance;
							
							/** If contribution outside the gross and the salary definition is 'ALL'
							include the formula based allowance amount if ETF tagged with formula allowance in etfsalary limit
							to calculate ETF percentage/amount **/
							if($salaryCalcDetail['Gross_Annual_Definition'] == 2)
							{
								/** If formula based allowance exists **/
								if(!empty($formulaBasedAllowanceArr) && count($formulaBasedAllowanceArr) > 0)
								{
									/** get Formula Based Allowance Benefits **/
									$getFormulaBasedAllowancesBenefits = $this->_dbCommonFun->getAllowanceBenefitAssociation($formulaBasedAllowanceArr[4]);
									
									/** If PF is tagged with formula based allowance, the include the formula based allownance amount with basicplusallowance amount **/	
									if(in_array('NPS',$getFormulaBasedAllowancesBenefits))
									{
										$etfSalaryLimit += $formulaBasedAllowanceAmount;
									}
								}
							}
						}
						else{
							$etfSalaryLimit = $basicPay;
						}
						
						if($salaryCalcDetail['Retirals_Based_On_Basic'] == 1)
						{
							$etfPercent = $dbEtf->getETFShare($employeeId, $etfSalaryLimit);
							$etfValue = $etfPercent[2]; // employeeshare+orgshare		
						}	
						else
						{
							$etfPercent = $dbEtf->getETFShare($employeeId, $etfSalaryLimit);
							$etfValue = $etfPercent[0]; // employeeshare+orgshare
						}
						
					}
				
			}
			else
			{
				/** Salary definition is 'Basic pay and Gross annual pay' for retirals part of gross and outside gross.
				As the employee is not eligible for benefits, there will be no allowances and retirals. So basic pay should be calculated
				from gross annual salary**/
				if(($salaryCalcDetail['Retirals_Based_On_Basic'] == 1)
				   && $salaryCalcDetail['Gross_Annual_Definition'] == 2)
				{
					$basicPay = $annualSalary/12;
				}
				else
				{
					$basicPay = $grossBasicpay;
				}	
			}
			
			$etfShareAmt								= 0;
			$getAllowanceAmt 							= 0;
			$pfShareAmt 	 							= 0;
			$adminEdliCharges['Employee_Admin_Charge'] 	= 0;
			$adminEdliCharges['Employee_Edli_Charge'] 	= 0;
			if(!empty($pfVal))
			{
				$allowanceDetails       = $this->getNonBonusAllowanceDetails($annualSalary,$basicPay,$arrAllowance);
				$employeePfWage   		= $this->getPfSalary($basicPay,$allowanceDetails,$providentFundSettings,$providentFundDetails,$basicPay);
				if($payrollGeneralSettings['Slab_Wise_PF']=='Yes')
        		{
					$socialSecuritySchemeSlabs = $this->getSocialSecuritySchemeSlabs();
					$providentFundDetails 	   = $this->calculateSlabWisePf($socialSecuritySchemeSlabs,$employeePfWage);
				}
				else
				{
					if($this->_orgDetails['Provident_Fund_Configuration']=='Previous')
					{
						$providentFundDetails 	= $this->calculatePreviousProvidentFundDetails($employeeId,$employeePfWage);
					}
					else
					{
						$providentFundDetails  	= $this->calculateCurrentProvidentFundDetails($employeePfWage,$providentFundSettings,$providentFundDetails,$employeeContributionRate,$employerContributionRate);
					}
				}
				
				$pfShareAmt 	 	  = $providentFundDetails['Employer_Share_Amount'];
				$adminEdliCharges     = $providentFundDetails['Admin_Edli_Charges'];
			}
		
			if($payrollGeneralSettings['Slab_Wise_NPS']=='Yes' && !empty($etfVal) && $etfVal==1)
			{
				if($formName == null){
					//getting the total allowance amount that includes pf
					$getAllowanceAmt = $this->getAllowances($employeeId,NULL,NULL, $basicPay, 'NPS',NULL,NULL,$effectiveDate);					
					$basicPlusAllowance = $basicPay + $getAllowanceAmt;
					
					/** If contribution inside the gross and outside the gross and the salary definition is 'ALL'
					include the formula based allowance amount if ETF tagged with formula allowance to calculate ETF amount **/
					if(($salaryCalcDetail['Retirals_Based_On_Basic'] == 1)
					&& $salaryCalcDetail['Gross_Annual_Definition'] == 2)
					{
						/** If formula based allowance exists **/
						if(!empty($formulaBasedAllowanceArr) && count($formulaBasedAllowanceArr) > 0)
						{
							$getFormulaBasedAllowancesBenefits = $this->_dbCommonFun->getAllowanceBenefitAssociation($formulaBasedAllowanceArr[4]);
							
							/** If PF is tagged with formula based allowance, the include the formula based allownance amount with basicplusallowance amount **/	
							if(in_array('NPS',$getFormulaBasedAllowancesBenefits))
							{
								$basicPlusAllowance += $formulaBasedAllowanceAmount;
							}
						}
					}
				}
				else{
					$basicPlusAllowance = $basicPay;
				}
				$npsSlabs 	 = $this->getNpsSlabs();
				$npsDetails  = $this->calculateSlabWiseNps($npsSlabs,$basicPlusAllowance);
				if(!empty($npsDetails))
				{
					$etfShareAmt = $npsDetails['Employer_Share_Amount'];
				}
				else
				{
					$etfShareAmt = 0;
				}
			}
			else
			{
				//if Etf contribution is percentage/Amount
				if($etfValue > 0 && ($etfPercent[1] == "1" || $payrollGeneralSettings['Slab_Wise_NPS']=='No')) //if Etf contribution is percentage
				{
					
					if($formName == null){
						//getting the total allowance amount that includes pf
						$getAllowanceAmt = $this->getAllowances($employeeId,NULL,NULL, $basicPay, 'NPS',NULL,NULL,$effectiveDate);					
						$basicPlusAllowance = $basicPay + $getAllowanceAmt;
						
						/** If contribution inside the gross and outside the gross and the salary definition is 'ALL'
						include the formula based allowance amount if ETF tagged with formula allowance to calculate ETF amount **/
						if(($salaryCalcDetail['Retirals_Based_On_Basic'] == 1)
						&& $salaryCalcDetail['Gross_Annual_Definition'] == 2)
						{
							/** If formula based allowance exists **/
							if(!empty($formulaBasedAllowanceArr) && count($formulaBasedAllowanceArr) > 0)
							{
								$getFormulaBasedAllowancesBenefits = $this->_dbCommonFun->getAllowanceBenefitAssociation($formulaBasedAllowanceArr[4]);
								
								/** If PF is tagged with formula based allowance, the include the formula based allownance amount with basicplusallowance amount **/	
								if(in_array('NPS',$getFormulaBasedAllowancesBenefits))
								{
									$basicPlusAllowance += $formulaBasedAllowanceAmount;
								}
							}
						}
					}
					else{
						$basicPlusAllowance = $basicPay;
					}
					$etfShareAmt = number_format(round($basicPlusAllowance*($etfValue/100),2),2,'.',''); // Emp+Org pf amount
				}
				else if($etfValue > 0 && ($etfPercent[1] == "0")) //if Etf contribution ia fixed amount
				{
					$etfShareAmt = number_format(round($etfValue,2),2,'.',''); // Emp+Org pf amount
				}
			}
			
			if($formName == null){
				$totalAllowanceAmt = $allowanceAmt+$pfShareAmt+$etfShareAmt+$insuranceAmt;
			}
			else{
				$totalAllowanceAmt = $FBPAmount;
			}
			$basicMonthlyPay = $basicPay;
				
			//If 'Retirals Based On Basic' is true, then gross monthly salary  = Basic + Allowance Amount
			if($salaryCalcDetail['Retirals_Based_On_Basic'] == 1)
			{
				if($formName == null){
					$avgMonthlySalary = $basicMonthlyPay+$allowanceAmt;
				}
				else{
					$avgMonthlySalary = $basicMonthlyPay + $FBPAmount;					
				}
			}
			else
			{				
				$avgMonthlySalary = $basicMonthlyPay+$totalAllowanceAmt;
			}
			
			/** Salary definition 'Basic pay and gross annual pay' is for formula based allowance only.
			If formula based allowance does not exists in this case then we should not allow them to add salary record **/
			if(($salaryCalcDetail['Retirals_Based_On_Basic'] == 1)&& $salaryCalcDetail['Gross_Annual_Definition'] == 2)
			{
				$formulaAllowanceExists = $this->_db->fetchOne($this->_db->select()->from(array('A'=>$this->_ehrTables->allowances), array('COUNT(Allowance_Id)'))												   
											   ->joinLeft(array('AT'=>$this->_ehrTables->allowanceTypes), 'A.Allowance_Type_Id = AT.Allowance_Type_Id', array(''))
											   ->where('AT.Formula_Based = ?','Yes')
											   ->where('A.Coverage LIKE ?', 'ORG')
											   ->where('A.Allowance_Status LIKE ?', 'Active'));
				
				if(empty($formulaAllowanceExists))
				{
					return array('Allowance'=>'','BPay'=>0, 'AGMS'=>0, 'Eligible'=>$benefitEmp, 'Pf'=>'', 'Gratuity'=>'', 'Symb'=>'', 'FixedInsurance'=>'', 'VariableInsurance'=>'','NPS'=>0,'Msg'=>'Please add formula based allowance or update the salary definition flag in organization settings!');
				}
			}
			

			/** If the salary definition is 'All' and contribution lies outside the gross **/
			if($salaryCalcDetail['Retirals_Based_On_Basic'] == 1 && $salaryCalcDetail['Gross_Annual_Definition'] == 2)
			{
				/** For normal salary modal **/
				if($salaryCalcDetail['FBP_Applicable'] != 1)
				{
					$basicAllowanceFloor = floor(number_format((($basicPay+$allowanceAmt) * 12),2,'.',''));

					$annualSalaryFloor = floor(number_format($annualSalary,2,'.',''));
					/** if basic + allowance is greater than gross annual salary **/
					if($basicAllowanceFloor > $annualSalaryFloor)
					{
						return array('Allowance'=>'','BPay'=>0, 'AGMS'=>0, 'Eligible'=>$benefitEmp, 'Pf'=>'', 'Gratuity'=>'', 'Symb'=>'', 'FixedInsurance'=>'', 'VariableInsurance'=>'','NPS'=>0,'Msg'=>'Please decrease the basic salary or increase the annual salary!');
					}
				}
			}

			if(!empty($insuranceVal) && $insuranceVal == 1)
			{
				// Extract common expensive operations outside conditional blocks for performance optimization
				$allowanceDetails         = $this->getNonBonusAllowanceDetails($annualSalary,$basicPay,$arrAllowance);
				$employeeInsuranceSalary  = $this->getStatutorySalary($basicPay,$allowanceDetails,'Variable Insurance');
				$philHealthSlabs 	      = $this->getPhilHealthSlabs();

				$slabWiseInsuranceDetails = $this->slabWiseInsuranceDetails();
				if(!empty($slabWiseInsuranceDetails) && $enableSalaryTemplate == 0)
				{
					// Traditional slab-wise insurance calculation
					foreach($slabWiseInsuranceDetails as $slabWiseInsurance)
					{
						$insuranceDetails  = $this->calculateSlabWiseInsurance($philHealthSlabs,$employeeInsuranceSalary);
						if(!empty($insuranceDetails))
						{
							$employerShareAmount = $insuranceDetails['Employer_Share_Amount'];
						}
						else
						{
							$employerShareAmount = 0;
						}
						$variableInsuranceDetails[]=array('Insurance_Name'=>$slabWiseInsurance['Insurance_Name'],'Premium'=>$employerShareAmount,
														  'Mode'=>$slabWiseInsurance['Payment_Frequency']);
					}
				}
				else if($enableSalaryTemplate == 1)
				{
					// Salary template-based insurance calculation with fallback handling
					try {
						$oldSalaryRetiralsData = $this->getInsuranceRetirlaDetails($employeeId);

						if (empty($oldSalaryRetiralsData) && $enableSalaryTemplate == 1) {
							// Log warning when salary template is enabled but no data exists
							error_log("Salary template enabled but no insurance retiral data found for employee: " . $employeeId);
							$variableInsuranceDetails = array();
						} else {
							$variableInsuranceDetails = $this->calculateInsuranceConfigurationDetails($oldSalaryRetiralsData,$employeeInsuranceSalary,$philHealthSlabs);
						}
					} catch (Exception $e) {
						error_log("Error in salary template insurance calculation for employee " . $employeeId . ": " . $e->getMessage());
						$variableInsuranceDetails = array();
					}
				}
			}
		
			//getting Basic_Overwrite flag either "0" or "1"
			$getBasicOverwriteRestriction = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->orgDetails, 'Basic_Overwrite'));
    
			//if basicpay greater than benefits || if basicpay less than benefits and basicpay greater than "0" and allowing basicOverwrite
			if ($basicMonthlyPay>$totalAllowanceAmt || (($basicMonthlyPay <= $totalAllowanceAmt) && ($getBasicOverwriteRestriction == 1) && ($basicMonthlyPay > 0) ))
			{
				
				if($salaryCalcDetail['Roundoff_Gross_Monthly_Salary'] == 1)
				{
					$AGMS = number_format(round($avgMonthlySalary,0),2,'.','');
				}
				else
				{
					$AGMS = number_format($avgMonthlySalary,2,'.','');
				}

                return array('Allowance'=> $allowancenames,
							 'BPay'=> number_format(round($basicMonthlyPay,2),2,'.',''),
							 'Msg'=> '',
							 'PfVal'=> $pfVal,
							 'InsVal'=> $insuranceVal,
							 'AGMS'=> $AGMS,
							 'GAS'=> number_format(round($annualSalary,2),2,'.',''),
							 'Eligible'=> $benefitEmp,
							 'FBPAmount' => $maxallowanceAmount,
							 'Pf'=> $pfShareAmt,
							 'Gratuity'=> $gratuityAmt,
							 'Symb'=> $currencySymbol,
							 'FixedInsurance'=> $fixedInsuranceDetails,
							 'VariableInsurance'=> $variableInsuranceDetails,
							 'ESI_Contribution_End_Date'=>$esiContributionEndDate,
                             'NPS'=> $etfShareAmt,
							 'Retirals_Based_On_Basic' => $salaryCalcDetail['Retirals_Based_On_Basic'],
							 'FixedHealthInsurance'=> $fHInsurance,
							 'EmployeeAdminCharge' => $adminEdliCharges['Employee_Admin_Charge'],
							 'EmployeeEdliCharge'  => $adminEdliCharges['Employee_Edli_Charge']);
			}
			//if benefits exceeds basicpay and basicpay less than or equal to "0"
			elseif($basicMonthlyPay <= $totalAllowanceAmt && ($getBasicOverwriteRestriction == 1) && ($basicMonthlyPay <= 0) )
			{
                return array('Allowance'=>'','BPay'=>0, 'AGMS'=>0, 'Eligible'=>$benefitEmp, 'Pf'=>0, 'Gratuity'=>$gratuityAmt, 'Symb'=>'', 'FixedInsurance'=>'', 'VariableInsurance'=>'','NPS'=>0,'Msg'=>'Basic salary cannot be zero. Please note Basic salary component is the base component for deriving the other salary component and statutory deductions');
			}
			//if benefits exceeds basic pay and not allowing basicOverwrite
			elseif($basicMonthlyPay <= $totalAllowanceAmt && $getBasicOverwriteRestriction == 0)
			{
                return array('Allowance'=>'','BPay'=>0, 'AGMS'=>0, 'Eligible'=>$benefitEmp, 'Pf'=>0, 'Gratuity'=>$gratuityAmt, 'Symb'=>'', 'FixedInsurance'=>'', 'VariableInsurance'=>'','NPS'=>0,'Msg'=>'Your Benefits exceeded basic salary!');
			}
		}
	}

	public function calculatePreviousProvidentFundDetails($employeeId,$employeePfWage)
	{
		$dbPf 				 = new Payroll_Model_DbTable_ProvidentFund();
		$pfPercent 			 = $dbPf->getPFShare($employeeId, $employeePfWage);
		$employerShare 		 = $pfPercent[2]; 
		$employeeShare 		 = $pfPercent[3]; 
		$employerShareAmount = 0;
		$employeeShareAmount = 0;
		
		if($employerShare > 0 && ($pfPercent[1] == "1")) //if pf contribution is percentage
		{
			$employerShareAmount = number_format(round($employeePfWage*($employerShare/100),2),2,'.',''); 
		}
		else if($employerShare > 0 && ($pfPercent[1] == "0")) //if pf contribution ia fixed amount
		{
			$employerShareAmount = number_format(round($employerShare,2),2,'.',''); 
		}

		if($employeeShare > 0 && ($pfPercent[1] == "1")) //if pf contribution is percentage
		{
			$employeeShareAmount = number_format(round($employeePfWage*($employeeShare/100),2),2,'.',''); 
		}
		else if($employeeShare > 0 && ($pfPercent[1] == "0")) //if pf contribution ia fixed amount
		{
			$employeeShareAmount = number_format(round($employeeShare,2),2,'.',''); 
		}
		
	
		if(!empty($employeeId) && !empty($employeePfWage))
		{
			$adminEdliCharges = $this->getAdminEdliCharges($employeeId,$employeePfWage);
		}
		else
		{
			$adminEdliCharges['Employee_Admin_Charge'] = 0;
			$adminEdliCharges['Employee_Edli_Charge'] = 0;
		}

		return array('Employer_Share_Amount'=>$employerShareAmount,'Employee_Share_Amount'=>$employeeShareAmount,'Admin_Edli_Charges'=>$adminEdliCharges);
   }

   public function calculateCurrentProvidentFundDetails($employeePfWage,$providentFundSettings,$providentFundDetails,$employeeContributionRate,$employerContributionRate)
   {
		$pfWageBasedOnContributionRate = $this->getProvidentFundWageBasedOnContributionRate($employeeContributionRate,$employerContributionRate,$employeePfWage,$providentFundSettings);
		$employeeWage 				   = $pfWageBasedOnContributionRate['Employee_Pf_Wage'] ;
		$employerWage 				   = $pfWageBasedOnContributionRate['Employer_Pf_Wage'] ;
		$employeeShareAmount 		   = ($employeeWage*$providentFundSettings['Actual_PF_Wage_Percentage'])/100;
		$employerShareAmount 		   = ($employerWage*$providentFundSettings['Actual_PF_Wage_Percentage'])/100;
		$employeeShareAmount 		   = $this->_dbCommonFun->getRoundOffValue('EPF',$employeeShareAmount);
		$employerShareAmount 		   = $this->_dbCommonFun->getRoundOffValue('EPF',$employerShareAmount);
		
		if($providentFundDetails['Admin_Charge_Part_Of_CTC']=='Yes')
		{
			$adminCharge  		   	= $providentFundSettings['Admin_Charge'];
			$adminChargeMaxAmount  	= $providentFundSettings['Admin_Charge_Max_Amount'];
			$adminChargeAmount 		= $this->_dbCommonFun->getRoundOffValue('EPF',MIN($employeeWage*($adminCharge/100),$adminChargeMaxAmount));		
		}
		else
		{
			$adminChargeAmount = 0; 
		}

		if($providentFundDetails['Edli_Charge_Part_Of_CTC']=='Yes')
		{
			$edliConfigurationEmployer 	= $providentFundSettings['EDLI_Charge'];
			$edliChargeMaxAmount   		= $providentFundSettings['EDLI_Charge_Max_Amount'];
			$edliChargeAmount 			= $this->_dbCommonFun->getRoundOffValue('EPF',MIN($employeeWage*($edliConfigurationEmployer/100),$edliChargeMaxAmount));
		}
		else
		{
			$edliChargeAmount = 0; 
		}
		$adminEdliCharges['Employee_Admin_Charge']  = $adminChargeAmount;
		$adminEdliCharges['Employee_Edli_Charge'] 	= $edliChargeAmount;
		return array('Employer_Share_Amount'=>$employerShareAmount,'Employee_Share_Amount'=>$employeeShareAmount,'Admin_Edli_Charges'=>$adminEdliCharges,'Employee_Provident_Fund_Wage'=>$employeeWage);
   }

   public function getProvidentFundWageBasedOnContributionRate($employeeContributionRate,$employerContributionRate,$employeePfWage,$providentFundSettings)
   {
		if($employeeContributionRate=='Restrict Contribution to ₹15,000 of PF Wage')
		{
			if($employeePfWage >= $providentFundSettings['Restricted_PF_Wage_Amount'])
			{
				$employeeWage = $providentFundSettings['Restricted_PF_Wage_Amount'];
			}
			else
			{
				$employeeWage = $employeePfWage;
			}
		}
		else
		{
			$employeeWage = $employeePfWage;
		}

		if($employerContributionRate=='Restrict Contribution to ₹15,000 of PF Wage')
		{
			if($employeePfWage >= $providentFundSettings['Restricted_PF_Wage_Amount'])
			{
				$employerWage = $providentFundSettings['Restricted_PF_Wage_Amount'];
			}
			else
			{
				$employerWage = $employeePfWage;
			}
		}
		else
		{
			$employerWage = $employeePfWage;
		}

		return array('Employee_Pf_Wage'=>$employeeWage,'Employer_Pf_Wage'=>$employerWage);
   }

	public function getProvidentFundSettings()
	{
		$providentFundSettings = $this->_db->fetchRow($this->_db->select()->from(array('PFS'=>$this->_ehrTables->providentFundSettings),array('*')));
		return $providentFundSettings;
	}

	public function getProvidentFundDetails()
	{
		$providentFundDetails = $this->_db->fetchRow($this->_db->select()->from(array('PF'=>$this->_ehrTables->providentFund),array('*')));
		return $providentFundDetails;
	}

	public function getNonBonusAllowanceDetails($annualSalary,$basicPay,$allowanceDetails)
	{
		$formulaBasedAllowanceAmount = 0;
		$nonBonusAllowanceDetails = array(); // New array to store non-bonus allowance details

		if (count($allowanceDetails) > 0) {
			foreach ($allowanceDetails as $key => $allowanceRow) {
				if ($allowanceRow['Formula_Based'] == 'No' && $allowanceRow['Allowance_Mode'] == 'Non Bonus') {
					$allowanceRow['Amount'] = $this->calculateAllowanceAmount($basicPay, $allowanceRow);
					$formulaBasedAllowanceAmount += $allowanceRow['Amount'];
					$nonBonusAllowanceDetails[] = $allowanceRow; // Add to the new array
				}
			}

			$formulaBasedAllowanceAmount = $this->calculateFormulaBasedAllowanceAmount($annualSalary, $basicPay, $formulaBasedAllowanceAmount);
			
			foreach ($allowanceDetails as $key => $allowanceRow) {
				if ($allowanceRow['Formula_Based'] == 'Yes' && $allowanceRow['Allowance_Mode'] == 'Non Bonus') {
					$allowanceRow['Amount'] = $formulaBasedAllowanceAmount;
					$nonBonusAllowanceDetails[] = $allowanceRow; // Add to the new array
				}
			}
		}

		return $nonBonusAllowanceDetails;
	}

	public function getMidJoinAllowanceDetails($allowanceDetails,$midJoin)
	{
		if(count($allowanceDetails) > 0)
		{
			foreach ($allowanceDetails as $key => $allowanceRow)
			{
				if($allowanceRow['As_Is_Payment']=='No')
				{
					$allowanceDetails[$key]['Amount'] = ($allowanceRow['Amount']/$midJoin[0])*$midJoin[1];
				}
			}
		}
		return $allowanceDetails;
	}

	public function getUnpaidLeaveAllowanceDetails($allowanceDetails,$unpaidArray,$paycyleDate)
	{
		if(count($allowanceDetails) > 0)
		{
			foreach ($allowanceDetails as $key => $allowanceRow)
			{
				if($allowanceRow['As_Is_Payment']=='No' && in_array('Unpaid Leave',explode(",",$allowanceRow['BenefitForms'])))
				{
					$unpaidLeaves = $this->_dbPayslip->unpaidLeaveDeduction($unpaidArray['Employee_Id'], $paycyleDate, $unpaidArray['Last_Salary_Date'], $allowanceRow['Amount'], $unpaidArray['Payslip_Id'], $unpaidArray['Working_Days'],$unpaidArray['Mid_Join']);
					
					if($unpaidLeaves != null && is_array($unpaidLeaves) && count($unpaidLeaves)>0)
					{
						$allowanceDetails[$key]['Amount'] = $allowanceRow['Amount']-$unpaidLeaves['Deduction_Amount'];
					}
					else
					{
						$allowanceDetails[$key]['Amount'] = $allowanceRow['Amount'];
					}
				}
			}
		}
		return $allowanceDetails;
	}

	public function getPfSalary($basicPay,$allowanceDetails,$providentFundSettings,$providentFundDetails,$actualBasicPay,$pfAdhocAllowanceAmount=0)
	{
		$basicPlusAllowance = $basicPay+$pfAdhocAllowanceAmount;

		if($this->_orgDetails['Provident_Fund_Configuration']=='Current' && $actualBasicPay >= $providentFundSettings['Restricted_PF_Wage_Amount'] && $providentFundDetails['PF_Calculated_As_Percentage_Of_Basic_Beyond_Statutory_Limit']=='Yes')	
		{
		    return $basicPlusAllowance;
		}
		else
		{
			$benefitFormName 		= 'Provident Fund';
			foreach ($allowanceDetails as $allowance)
			{
				if(in_array($benefitFormName,explode(",",$allowance['BenefitForms'])))
				{
					if($this->_orgDetails['Provident_Fund_Configuration']=='Previous')
					{
						$basicPlusAllowance+=$allowance['Amount'];
					}
					else
					{
						if($allowance['Consider_For_EPF_Contribution']=='Always')
						{
							$basicPlusAllowance+=$allowance['Amount'];
						}
					}
				}
			}

			if($this->_orgDetails['Provident_Fund_Configuration']=='Current' && $providentFundDetails['Consider_All_Salary_Components_For_LOP']=='Yes')
			{
				foreach($allowanceDetails as $allowance)
				{
					if(in_array($benefitFormName,explode(",",$allowance['BenefitForms'])))
					{
						if($allowance['Consider_For_EPF_Contribution']=='Only when PF Wage is less than ₹15,000' && $basicPlusAllowance<$providentFundSettings['Restricted_PF_Wage_Amount'])
						{
							$basicPlusAllowance+=$allowance['Amount'];
							if($basicPlusAllowance > $providentFundSettings['Restricted_PF_Wage_Amount'])
							{
								$basicPlusAllowance = $providentFundSettings['Restricted_PF_Wage_Amount'];
							}
						}
					}
				}
			}
			return $basicPlusAllowance;
		}
	}

	
	public function getStatutorySalary($basicPay,$allowanceDetails,$benefitFormName,$adhocAllowanceAmount=0)
	{
		$basicPlusAllowance = $basicPay+$adhocAllowanceAmount;
		foreach ($allowanceDetails as $allowance)
		{
			if(in_array($benefitFormName,explode(",",$allowance['BenefitForms'])))
			{
				$basicPlusAllowance+=$allowance['Amount'];
			}
		}
		return $basicPlusAllowance;
	}
	
	public function getAdminEdliCharges($employeeId,$basicPlusAllowance)
	{
		$employeePfExistsQry = $this->_db->select()->from(array('pf'=>$this->_ehrTables->empPF),array('Admin_Charge','Admin_Charge_Max_Amount','EDLI_Configuration_Employer','EDLI_Charge_Max_Amount','Admin_Charge_Part_Of_Ctc','Edli_Charge_Part_Of_Ctc'))
																->where('pf.Employee_Id =?',$employeeId);
		$employeePfexists    = $this->_db->fetchRow($employeePfExistsQry);
		
		if(!empty($employeePfexists))
		{
			$adminChargePartOfCtc = $employeePfexists['Admin_Charge_Part_Of_Ctc'];
			$edliChargePartofCtc  = $employeePfexists['Edli_Charge_Part_Of_Ctc'];
			$adminCharge  		   = $employeePfexists['Admin_Charge'];
			$adminChargeMaxAmount  = $employeePfexists['Admin_Charge_Max_Amount'];
			$edliConfigurationEmployer = $employeePfexists['EDLI_Configuration_Employer'];
			$edliChargeMaxAmount       = $employeePfexists['EDLI_Charge_Max_Amount'];
	        $basicPlusAllowance = MIN($basicPlusAllowance,15000);
		}
		else
		{
			$orgPfExistsQry       = $this->_db->select()->from(array('pf'=>$this->_ehrTables->orgPF),array('Admin_Charge','Admin_Charge_Max_Amount','EDLI_Configuration_Employer','EDLI_Charge_Max_Amount','Admin_Charge_Part_Of_Ctc','Edli_Charge_Part_Of_Ctc'));
			$orgPfExists		  = $this->_db->fetchRow($orgPfExistsQry);
			$adminChargePartOfCtc = $orgPfExists['Admin_Charge_Part_Of_Ctc'];
			$edliChargePartofCtc  = $orgPfExists['Edli_Charge_Part_Of_Ctc'];
			$adminCharge  		   = $orgPfExists['Admin_Charge'];
			$adminChargeMaxAmount  = $orgPfExists['Admin_Charge_Max_Amount'];
			$edliConfigurationEmployer = $orgPfExists['EDLI_Configuration_Employer'];
			$edliChargeMaxAmount  	   = $orgPfExists['EDLI_Charge_Max_Amount'];
		}

		if($adminChargePartOfCtc==1)
		{
			$adminChargeAmount = $this->_dbCommonFun->getRoundOffValue('EPF',MIN($basicPlusAllowance*($adminCharge/100),$adminChargeMaxAmount));		
		}
		else
		{
			$adminChargeAmount = 0; 
		}

		if($edliChargePartofCtc==1)
		{
			$edliChargeAmount = $this->_dbCommonFun->getRoundOffValue('EPF',MIN($basicPlusAllowance*($edliConfigurationEmployer/100),$edliChargeMaxAmount));
		}
		else
		{
			$edliChargeAmount = 0; 
		}

		return array('Employee_Admin_Charge'=>$adminChargeAmount,'Employee_Edli_Charge'=>$edliChargeAmount);
	}
	/**
	 * getting the employee names whose salary is to be recalculated //only active employees
	 * For showing employee names in salary recalculation grid
	 * (i,e)employee whose Salary_Recalc field is set to "1" in salary table
	 */
	public function showSalaryRecalcEmployees($page, $rows, $sortField, $sortOrder,$salaryAccess)
	{
		$qrySalaryRecalcEmpNames = $this->_db->select()->from(array('EP'=>$this->_ehrTables->empPersonal),
																				   array('EP.Employee_Id',
																						 'Employee_Name'=>new Zend_Db_Expr("CONCAT(EP.Emp_First_Name, ' ', EP.Emp_Last_Name)")))
														
														->joinInner(array('S'=>$this->_ehrTables->salary),'EP.Employee_Id = S.Employee_Id',array())
														
														->joinInner(array('EJ'=>$this->_ehrTables->empJob), 'EJ.Employee_Id = S.Employee_Id', array())
														
														->where('EJ.Emp_Status Like ?', 'Active')
														->where('S.Salary_Recalc >= ?',1)
														
														->order("EP.Emp_First_Name ASC")
														->limit($rows, $page);

		if(!empty($salaryAccess['Admin']))
		{
			$qrySalaryRecalcEmpNames = $this->_dbCommonFun->formServiceProviderQuery($qrySalaryRecalcEmpNames,'EJ.Service_Provider_Id',$salaryAccess['LogId']);
		}												
		
		$salaryRecalcEmpNames = $this->_db->fetchAll($qrySalaryRecalcEmpNames);
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		/* Total data set length */
		$iTotal = $this->_db->fetchOne($this->_db->select()->from(array('EP'=>$this->_ehrTables->empPersonal),
																  new Zend_Db_Expr('COUNT(EP.Employee_Id)'))
									   ->joinInner(array('S'=>$this->_ehrTables->salary),'EP.Employee_Id = S.Employee_Id',array())
														
														->joinInner(array('EJ'=>$this->_ehrTables->empJob), 'EJ.Employee_Id = S.Employee_Id', array())
														
														->where('EJ.Emp_Status Like ?', 'Active')
														->where('S.Salary_Recalc >= ?',1));
		
		/**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $salaryRecalcEmpNames);
	}
	
	//version:0.9 => added basicPay as parameter for getbenefits() which will be used when gross monthly derived from basicPay
	//version:0.8 =>checked whether gross annual salary is within the range defined for emp grade
	/**
	*for recalculating and updating the salary details
	*/
	public function recalculateSalary($effectiveDate = '', $sessionId,$salaryAccess)
	{
		$salaryRecalculation = array();
		$count = 0;
		
		//getting all salary recalculation employees
		$getAllSalaryRecalcEmp = $this->getSalaryRecalcEmployeeDetails(NULL,$salaryAccess);
		
		//getting the salary recalculation employees with lockflag "0" in salary table
		$getSalaryRecalcEmpDetails = $this->getSalaryRecalcEmployeeDetails("lockFlag",$salaryAccess);
		
		if(count($getSalaryRecalcEmpDetails) > 0 && !empty($effectiveDate))
		{
			$salaryDateDetails = $this->_dbPayslip->getSalaryDateRange(date('m',strtotime($effectiveDate)),date('Y',strtotime($effectiveDate)),strtotime($effectiveDate));
			//when the salary recalculation flag is set salary start date should be equal to effective date.
			if(strtotime($salaryDateDetails['Salary_Date'])==strtotime($effectiveDate))
			{
				foreach($getSalaryRecalcEmpDetails as $salaryDetail)
				{
					$salaryEmpName = $this->_dbPersonal->employeeName($salaryDetail['Employee_Id']);
					
					//getting the last payslip month
					$maxPayslipMonth = $this->_dbPayslip->maxPayslipMonth($salaryDetail['Employee_Id'],"Monthly");
					
					if(!empty($maxPayslipMonth))
					{
						$maxPayslipMonth = explode(',',$maxPayslipMonth);
						$lastPayslipDetail = $this->_dbPayslip->getSalaryDay($maxPayslipMonth[0],$maxPayslipMonth[1]);
						$lastPaymentDate = date('Y-m-d',strtotime(date("Y-m-d", strtotime($lastPayslipDetail['Last_SalaryDate'])) . " + 1 day"));
					}
					else
					{
						$lastPaymentDate = $salaryDetail['Effective_Date'];
					}
					
					$getSalaryChange = $this->_db->fetchOne($this->_db->select()->from(array('S'=>$this->_ehrTables->salary),
																					'S.Employee_Id')
															
															->joinInner(array('EJ'=>$this->_ehrTables->empJob),
																		'EJ.Employee_Id = S.Employee_Id', array())
															
															->joinInner(array('D'=>$this->_ehrTables->designation),
																		'EJ.Designation_Id = D.Designation_Id', array())
															
															->joinInner(array('G'=>$this->_ehrTables->empGrade),
																		'G.Grade_Id = D.Grade_Id', array())
															
															->where('EJ.Employee_Id = ?', $salaryDetail['Employee_Id'])
															->where('EJ.Emp_Status = ?', "Active")
															->where('S.Annual_Gross_Salary > G.Max_AnnualSalary OR
																	S.Annual_Gross_Salary < G.Min_AnnualSalary'));
					
					$fbpapplicable = $this->_ehrTables->getFlexiBenefitPlan();
					$formName = ($fbpapplicable == 1) ? 'SAL' : NULL;
			
					if($this->_orgDetails['Provident_Fund_Configuration']=='Current')	
					{
						$providentFundDetails 	= $this->getProvidentFundDetails();
						$providentFundSettings 	= $this->getProvidentFundSettings();	
					}
					else
					{
						$providentFundDetails 	= '';
						$providentFundSettings 	= '';
					}
		
	
					if($this->_orgDetails['Provident_Fund_Configuration']=='Current')	
					{
						$employeeContributionRate = $salaryDetail['Employee_Contribution_Rate'];
						$employerContributionRate = $salaryDetail['Employer_Contribution_Rate'];
					}
					else
					{
						$employeeContributionRate = '';
						$employerContributionRate = '';
					}
	
					$salaryRecalculation = $this->getBenefits($salaryDetail['Employee_Id'], $salaryDetail['Annual_Gross_Salary'],
															$salaryDetail['Is_PfEmployee'], $salaryDetail['Is_InsuranceEmployee'],'',
															NULL, NULL, $effectiveDate, $salaryDetail['Basic_Pay'],$formName,'','',
															$salaryDetail['Is_ETFEmployee'],$employeeContributionRate,$employerContributionRate,$providentFundDetails,$providentFundSettings);
					$record = $salaryDetail;										
				   
					$record['FBPApplicable']=$fbpapplicable;
					$record['Benefit']=$salaryRecalculation;
					$record['Gratuity_Amount']=$salaryRecalculation['Gratuity'];
					
					
					if(empty($salaryRecalculation['Msg']) && ($getSalaryChange == 0) &&
					//effectiveDate should greater than or equal to last payment date
					strtotime($effectiveDate) >= strtotime($lastPaymentDate)	&&
				   strtotime($effectiveDate) <= strtotime('+15 days', strtotime(date('Y-m-d'))) &&
				   strtotime($effectiveDate) >= strtotime($salaryDetail['Effective_Date']))
				{
					$ctcAmount = $this->calculateCtcAmount($record);																							
					$monthlySalaryArray = array('Employee_Id'          => $salaryDetail['Employee_Id'],
												'Basic_Pay'            => $salaryRecalculation['BPay'],
												'Effective_Date'       => $effectiveDate,
													'Annual_Gross_Salary'  => $salaryRecalculation['GAS'],
													'Monthly_Gross_Salary' => $salaryRecalculation['AGMS'],
													'Annual_Ctc'           => $ctcAmount,
													'Is_OvertimeEmployee'  => $salaryDetail['Is_OvertimeEmployee'],
													'Overtime_Wage'        => $salaryDetail['Overtime_Wage'],
													'Is_PfEmployee'        => $salaryDetail['Is_PfEmployee'],
													'Is_InsuranceEmployee' => $salaryDetail['Is_InsuranceEmployee']);
								
						$updatedSalary = $this->updateMonthlySalary($monthlySalaryArray, $salaryDetail['Employee_Id'], $sessionId,
																	$salaryRecalculation);
						
						if($updatedSalary['success'])
						{
							$count = $count+1;
						}
					}
				}
				
				//salary recalculated for all employees
				if(count($getAllSalaryRecalcEmp) == $count)
				{
					return array('success'=>true, 'msg'=>'Salary recalculated successfully', 'type'=>'success');
				}
				elseif($count == 0)  //no salary is recalculated
				{	
					return array('success'=>false, 'msg'=>'<div>We are unable to recalculate salary due to one of the reason.
					<li>	1) There could be salary band change for the employee grade; please edit the salary record.</li>
					<li>	2) There could be a mix of employees where some are having payslip for the effective date.</li>
					<li>	3) Effective date should not be less than last salaried date.</li>
					<li>	4) The effective date should be greater than or equal to previous effective date.</li></div>', 'type'=>'info');
				}
				else //salary recalculated for partial employees
				{
					return array('success'=>true, 'msg'=>'Salary recalculated for partial employees', 'type'=>'info');
				}
			} 
			else 
			{
				return array('success'=>false, 'msg'=>'Invalid Salary Effective Date', 'type'=>'info');
			}
		}
		else
		{
				return array('success'=>false, 'msg'=>'<div>We are unable to recalculate salary due to one of the reason.
				<li>	1) Effective date should not empty.</li>
				<li>	2) Employee has opened this record to update.</li></div>', 'type'=>'info');
		}
	}
	
	/**
	 * getting the salary details for the employees whose salary is to be recalculated //only active employees
	 * (i,e) Salary_Recalc field is set to "1" in salary table
	 */
	public function getSalaryRecalcEmployeeDetails($lockFlag = NULL,$salaryAccess=NULL)
	{
		$qrySalaryRecalcEmp = $this->_db->select()->from(array('S'=>$this->_ehrTables->salary),
														 array('S.Employee_Id','S.Effective_Date', 'S.Annual_Gross_Salary',
															   'S.Is_PfEmployee','S.Is_InsuranceEmployee','S.Is_OvertimeEmployee','S.Employee_Contribution_Rate','S.Employer_Contribution_Rate',
															   'S.Overtime_Wage','S.Basic_Pay','S.Is_ETFEmployee'))
		
									->joinInner(array('EJ'=>$this->_ehrTables->empJob), 'EJ.Employee_Id = S.Employee_Id', array())
									->where('EJ.Emp_Status Like ?', 'Active')
									->where('S.Salary_Recalc >= ?',1);

		if(!empty($salaryAccess['Admin']))
		{
			$qrySalaryRecalcEmp = $this->_dbCommonFun->formServiceProviderQuery($qrySalaryRecalcEmp,'EJ.Service_Provider_Id',$salaryAccess['LogId']);
		}							
		
		if(!is_null($lockFlag))
		{
			$qrySalaryRecalcEmp->where('Lock_Flag = ?',0);
		}
		
		return $this->_db->fetchAll($qrySalaryRecalcEmp);
	}

	//this function returns the effective date range based on current effective date and  maximum effective date
	public function getEffectiveDateDetails($employeeId,$salaryType,$effectiveDate)
	{
		$effectiveDateArr = array();

		if($salaryType=='Monthly')
		{
			//given effective date should be opened in date picker so effective date is added in this array
			$constEffectiveDate = $effectiveDate;
			$effectiveDateArr = array($effectiveDate);
			//$maximumEffectiveDate = date('Y-m-d',strtotime('+15 days', strtotime(date('Y-m-d'))));

			$maximumEffectiveDate = $effectiveDate;

			// $maximumEffectiveDate = $effectiveDate;
			//till the maximum effective date is greater than or equal to the effective date we need run this loop.
			while(strtotime($maximumEffectiveDate) >= strtotime($effectiveDate))
			{
				$effectiveDate = date('Y-m-d', strtotime('+1 month', strtotime($effectiveDate)));
				$salaryDateDetails = $this->_dbPayslip->getSalaryDateRange(date('m',strtotime($effectiveDate)),date('Y',strtotime($effectiveDate)),strtotime($effectiveDate));
				if(strtotime($maximumEffectiveDate) >= strtotime($effectiveDate)
					&& strtotime($constEffectiveDate) < strtotime($salaryDateDetails['Salary_Date']))
				{
					array_push($effectiveDateArr, $salaryDateDetails['Salary_Date']);
				}
			}
		}
		return $effectiveDateArr;
	}
	
	/**
	 * to show hourly wages records in a grid
	 */
	public function listHourlyWages ($page, $rows, $sortField, $sortOrder, $searchAll=null, $searchArr, $salaryAccess)
	{
		$employeeName       = $searchArr['employeeName'];
		$regularWageStart   = $searchArr['regularWageStart'];
		$regularWageEnd     = $searchArr['regularWageEnd'];
		$overtimeWageStart  = $searchArr['overtimeWageStart'];
		$overtimeWageEnd    = $searchArr['overtimeWageEnd'];
		$effectiveDateStart = $searchArr['effectiveDateStart'];
		$effectiveDateEnd   = $searchArr['effectiveDateEnd'];
		$employeeStatus     = $searchArr['employeeStatus'];
		$serviceProviderId  = $searchArr['serviceProviderId'];
		
		/**
		 *	Sorting columns based on display column order in grid
		*/
		switch($sortField)
		{
			case 1: $sortField = 'EJ.User_Defined_EmpId'; break;
			case 2: $sortField = 'P.Emp_First_Name'; break;
			case 3: $sortField = 'H.Regular_Hourly_Wages'; break;
			case 4: $sortField = 'H.Overtime_Hourly_Wages'; break;
			case 5: $sortField = 'H.Effective_Date'; break;
			default:
				$sortField = 'H.Added_On'; $sortOrder = 'desc'; break;
		}
        
		$qryHourlyWages = $this->_db->select()->distinct()->from(array('H'=>$this->_ehrTables->hourlyWages),
																 array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS H.Employee_Id as Count'),
																	   'H.Regular_Hourly_Wages','H.Overtime_Hourly_Wages','H.Is_GratuityEmployee',
																	   'H.Is_PfEmployee', 'H.Employee_Id', 'H.Is_InsuranceEmployee','H.Gratuity_Amount',
																	   new Zend_Db_Expr("date_format(H.Effective_Date, '".$this->_orgDF['sql']."') as View_Effective_Date"),
																	   new Zend_Db_Expr("DATE_FORMAT(H.Added_On,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
																	   new Zend_Db_Expr("DATE_FORMAT(H.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On"),
																	   'PfEmployee'=>new Zend_Db_Expr('CASE when H.Is_PfEmployee = 1 THEN \'Yes\' ELSE \'No\' END'),
																	   'Log_Id'=>new Zend_Db_Expr($salaryAccess['LogId']),
																	   'H.Effective_Date'))
		
								->joinInner(array('P'=>$this->_ehrTables->empPersonal),'P.Employee_Id=H.Employee_Id',
											array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name, ' ', P.Emp_Last_Name)")))
								
								->joinInner(array('EJ'=>$this->_ehrTables->empJob),'P.Employee_Id=EJ.Employee_Id', array('EJ.Emp_Status',
											'User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN P.Employee_Id ELSE EJ.User_Defined_EmpId END')))
								
								->joinInner(array('L'=>$this->_ehrTables->location), 'L.Location_Id=EJ.Location_Id', array('L.Currency_Symbol'))
								
								->joinLeft(array('A'=>$this->_ehrTables->auditWages), 'P.Employee_Id=A.Employee_Id', array('Audit'=>'A.Employee_Id'))
								
								->joinLeft(array('P1'=>$this->_ehrTables->empPersonal), 'P1.Employee_Id = H.Lock_Flag',
										   array('Lock_EmpName'=>new Zend_Db_Expr("CONCAT(P1.Emp_First_Name, ' ', P1.Emp_Last_Name)")))
								
								->joinInner(array('P2'=>$this->_ehrTables->empPersonal),'H.Added_By=P2.Employee_Id',
											array('Added_By_Name'=>new Zend_Db_Expr("CONCAT(P2.Emp_First_Name, ' ', P2.Emp_Last_Name)")))
								
								->joinLeft(array('P3'=>$this->_ehrTables->empPersonal), 'H.Updated_By=P3.Employee_Id',
										   array('Updated_By_Name'=>new Zend_Db_Expr("CONCAT(P3.Emp_First_Name, ' ', P3.Emp_Last_Name)")))
								
								->joinLeft(array('his'=>$this->_ehrTables->auditWages),'his.Employee_Id=H.Employee_Id',
										   array('his.Employee_Id as History'))
								
								->order("$sortField $sortOrder")
								->limit($rows, $page);
			
		if(empty($salaryAccess['Admin']))
		{
			$getEmployeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
												  ->where('Manager_Id = ?', $salaryAccess['LogId']));
		
			if( $salaryAccess['Is_Manager'] == 1 && !empty($getEmployeeId))
			{
				/* If the manager is having access to view his employee salary records */
				if(empty($this->_orgDetails['Restrict_Financial_Access_For_Manager'])){
					$qryHourlyWages->where('H.Employee_Id = :EmpId or H.Employee_Id IN (?)', $getEmployeeId)
					->bind(array('EmpId'=>$salaryAccess['LogId']));
				}else{
					$qryHourlyWages->where('H.Employee_Id = ?', $salaryAccess['LogId']);	
				}
			}
			else
			{
				$qryHourlyWages->where('H.Employee_Id = ?', $salaryAccess['LogId']);
			}
		}
		
		/**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			//$conditions = $this->_db->quoteInto('P.Emp_First_Name Like ?', "%$searchAll%");
			//$conditions .= $this->_db->quoteInto('or P.Emp_Last_Name Like ?', "%$searchAll%");
            $conditions  = $this->_db->quoteInto(new Zend_Db_Expr('Concat(P.Emp_First_Name," ",P.Emp_Last_Name) Like ?'),"%$searchAll%");
			$conditions .= $this->_db->quoteInto('or H.Regular_Hourly_Wages Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or H.Overtime_Hourly_Wages Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or H.Effective_Date Like ?', "%$searchAll%");
            $conditions .= $this->_db->quoteInto('or EJ.User_Defined_EmpId Like ?', "%$searchAll%");
			
			$qryHourlyWages->where($conditions);
		}

		/* Filter for employee */       
        if (!empty($employeeName) && preg_match('/^[a-zA-Z]/', $employeeName))
        {
			$qryHourlyWages->where($this->_db->quoteInto(new Zend_Db_Expr('Concat(P.Emp_First_Name," ",P.Emp_Last_Name) Like ?'),"%$employeeName%"));
//                        ->where($this->_db->quoteInto('P.Emp_First_Name Like ? or ', "%$employeeName%").
//							   $this->_db->quoteInto('P.Emp_Last_Name Like ?', "%$employeeName%"));
        }
		
		/* Filter for regular hourly wages start */					
		if (($regularWageStart >=0) && preg_match('/^[0-9*\.]/', $regularWageStart) && !empty($regularWageStart))
		{
		    $qryHourlyWages->where($this->_db->quoteInto('H.Regular_Hourly_Wages >= ?', $regularWageStart));
		}
		
		/* Filter for regular hourly wages end */					
		if (($regularWageEnd >=0) && preg_match('/^[0-9*\.]/', $regularWageEnd) && !empty($regularWageEnd))
		{
		    $qryHourlyWages->where($this->_db->quoteInto('H.Regular_Hourly_Wages <= ?', $regularWageEnd));
		}
        
		/* Filter for overtime wages start */					
		if (($overtimeWageStart >=0) && preg_match('/^[0-9*\.]/', $overtimeWageStart) && !empty($overtimeWageStart))
		{
		    $qryHourlyWages->where($this->_db->quoteInto('H.Overtime_Hourly_Wages >= ?', $overtimeWageStart));
		}
		
		/* Filter for overtime wages end */					
		if (($overtimeWageEnd >=0) && preg_match('/^[0-9*\.]/', $overtimeWageEnd) && !empty($overtimeWageEnd))
		{
		    $qryHourlyWages->where($this->_db->quoteInto('H.Overtime_Hourly_Wages <= ?', $overtimeWageEnd));
		}
		
		/* Filter for effective start */
		if ($effectiveDateStart != '')
		{
		    $qryHourlyWages->where($this->_db->quoteInto('H.Effective_Date >= ?', $effectiveDateStart));
		}
		
		/* Filter for effective end */					
		if ($effectiveDateEnd != '')
		{
		    $qryHourlyWages->where($this->_db->quoteInto('H.Effective_Date <= ?', $effectiveDateEnd));
		}
		
		if(!empty($employeeStatus) && preg_match('/^[a-zA-Z]/', $employeeStatus))
		{
			$qryHourlyWages->where('EJ.Emp_Status = ?', $employeeStatus);
		}

		if(!empty($serviceProviderId)&& $this->_orgDetails['Field_Force']==1)
        {
            $qryHourlyWages->where('EJ.Service_Provider_Id = ?',$serviceProviderId);
        }

		$qryHourlyWages = $this->_dbCommonFun->getDivisionDetails($qryHourlyWages,'EJ.Department_Id');

		if(!empty($salaryAccess['Admin']))
		{
			$qryHourlyWages = $this->_dbCommonFun->formServiceProviderQuery($qryHourlyWages,'EJ.Service_Provider_Id',$salaryAccess['LogId']);
		}  
 
		/**
		 * SQL queries
		 * Get data to display
		*/
		$hourlyWages = $this->_db->fetchAll($qryHourlyWages);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		foreach ($hourlyWages as $key=>$row)
		{
			$hourlyWages[$key]['Benefit'] = $this->getHourlyEmpBenefits($row['Employee_Id'], $row['Is_PfEmployee'],
																		$row['Is_InsuranceEmployee'], NULL, NULL, $row['Effective_Date']);
		}
		
		/* Total data set length */
		$qryGetCount = $this->_db->select()->from($this->_ehrTables->hourlyWages, new Zend_Db_Expr('COUNT(Employee_Id)'));
		
		if(empty($salaryAccess['Admin']))
		{
			$qryEmployeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
												  ->where('Manager_Id = ?', $salaryAccess['LogId']));
			
			if( $salaryAccess['Is_Manager'] == 1 && !empty($getEmployeeId))
			{
				/* If the manager is having access to view his employee salary records */
				if(empty($this->_orgDetails['Restrict_Financial_Access_For_Manager'])){
					$qryGetCount->where('Employee_Id = :EmpId or Employee_Id IN (?)', $getEmployeeId)
					->bind(array('EmpId'=>$salaryAccess['LogId']));
				}else{
					$qryGetCount->where('Employee_Id = ?', $salaryAccess['LogId']);
				}
			}
			else
			{
				$qryGetCount->where('Employee_Id = ?', $salaryAccess['LogId']);
			}
		}
		
		$iTotal = $this->_db->fetchOne($qryGetCount);
		
		/**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $hourlyWages);
	}
	
	// Update hourly wages employee salary
	public function updateHourlyWages($hourlyWagesArray, $hwEmployeeId, $sessionId )
	{
		$hourlyWagesExist = $this->_db->fetchRow($this->_db->select()->from(array('J'=>$this->_ehrTables->empJob), array())
												 
												 ->joinLeft(array('S'=>$this->_ehrTables->salary), 'J.Employee_Id = S.Employee_Id',
															array('Salary_EmpId'=>'S.Employee_Id'))
												 
												 ->joinLeft(array('H'=>$this->_ehrTables->hourlyWages), 'J.Employee_Id = H.Employee_Id',
															array('Wage_EmpId'=>'H.Employee_Id'))
												 
												 ->where('J.Employee_Id = ?', $hwEmployeeId));
		
		if ((empty($hwEmployeeId) && $hourlyWagesExist['Wage_EmpId'] == NULL && $hourlyWagesExist['Salary_EmpId'] == NULL) ||
			(!empty($hwEmployeeId) && $hourlyWagesExist['Wage_EmpId'] != NULL && $hourlyWagesExist['Salary_EmpId'] == NULL) &&
			$hourlyWagesExist['Salary_EmpId'] == NULL)
		{
			$dbJobDetail = new Employees_Model_DbTable_JobDetail();
							
			$getGradeLocation = $dbJobDetail->getGradeLocation($hourlyWagesArray['Employee_Id']);
			
			$hourlyWagesArray['Location_Id'] = $getGradeLocation['Location_Id'];
			$hourlyWagesArray['Grade_Id']    = $getGradeLocation['Grade_Id'];
			
			if (!empty($hwEmployeeId))
			{
				$action = 'Edit';
				
				$hourlyWagesArray['Updated_On'] = date('Y-m-d H:i:s');
				$hourlyWagesArray['Updated_By'] = $sessionId;
				
				$rowAudit = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->hourlyWages,
																			array('Employee_Id','Location_Id', 'Grade_Id',
																				  'Regular_Hourly_Wages', 'Overtime_Hourly_Wages',
																				  'Effective_Date', 'Is_PfEmployee',
																				  'Is_InsuranceEmployee'))
												 ->where('Employee_Id = ?', $hourlyWagesArray['Employee_Id']));
				
				if(strtotime($rowAudit['Effective_Date']) == strtotime($this->_ehrTables->changeDateformat($hourlyWagesArray['Effective_Date'])))
				{
					$archiveDate = date('Y-m-d', strtotime ($rowAudit['Effective_Date']));
				}
				else
				{
					$archiveDate = date('Y-m-d', strtotime ( '-1 day' , strtotime ( $this->_ehrTables->changeDateformat($hourlyWagesArray['Effective_Date'] ))));
				}
				
				$getArchiveDate = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->salaryHistory,
																				  new Zend_Db_Expr('Count(Audit_Id)'))
													   ->where('Archive_Date = ?', $archiveDate)
													   ->where('Employee_Id = ?', $hourlyWagesArray['Employee_Id']));
				
				if($rowAudit['Regular_Hourly_Wages'] != $hourlyWagesArray['Regular_Hourly_Wages'] ||
				   $rowAudit['Overtime_Hourly_Wages'] != $hourlyWagesArray['Overtime_Hourly_Wages'] ||
				   strtotime($rowAudit['Effective_Date']) !=strtotime($hourlyWagesArray['Effective_Date']) ||
				   $rowAudit['Is_PfEmployee'] != $hourlyWagesArray['Is_PfEmployee'] ||
				   $rowAudit['Is_InsuranceEmployee'] != $hourlyWagesArray['Is_InsuranceEmployee'])
				{
					if($getArchiveDate > 0)
					{
						$auditWhere['Employee_Id = ?'] = $hourlyWagesArray['Employee_Id'];
						$auditWhere['Archive_Date = ?'] = $archiveDate;
						
						$this->_db->update($this->_ehrTables->auditWages, array('Grade_Id'              => $rowAudit['Grade_Id'],
																				'Location_Id'           => $rowAudit['Location_Id'],
																				'Regular_Hourly_Wages'  => $rowAudit['Regular_Hourly_Wages'],
																				'Is_PfEmployee'         => $rowAudit['Is_PfEmployee'],
																				'Is_InsuranceEmployee'  => $rowAudit['Is_InsuranceEmployee'],
																				'Modified_On'           => date('Y-m-d H:i:s'),
																				'Overtime_Hourly_Wages' => $rowAudit['Overtime_Hourly_Wages'],
																				'Modified_By'           => $sessionId),
										   $auditWhere);
					}
					else
					{
						$this->_db->insert($this->_ehrTables->auditWages, array('Employee_Id'          => $rowAudit['Employee_Id'],
																				'Grade_Id'             => $rowAudit['Grade_Id'],
																				'Location_Id'          => $rowAudit['Location_Id'],
																				'Regular_Hourly_Wages' => $rowAudit['Regular_Hourly_Wages'],
																				'Is_PfEmployee'        => $rowAudit['Is_PfEmployee'],
																				'Is_InsuranceEmployee' => $rowAudit['Is_InsuranceEmployee'],
																				'Modified_On'          => date('Y-m-d H:i:s'),
																				'Overtime_Hourly_Wages'=> $rowAudit['Overtime_Hourly_Wages'],
																				'Effective_Date'       => $rowAudit['Effective_Date'],
																				'Archive_Date'         => $archiveDate,
																				'Modified_By'          => $sessionId));
					}
				}
				
				$updated = $this->_db->update($this->_ehrTables->hourlyWages, $hourlyWagesArray,
											  array('Employee_Id = '. $hourlyWagesArray['Employee_Id']));
			}
			else
			{
				$action = 'Add';
				
				$hourlyWagesArray['Added_On'] = date('Y-m-d H:i:s');
				$hourlyWagesArray['Added_By'] = $sessionId;
				
				$updated = $this->_db->insert($this->_ehrTables->hourlyWages, $hourlyWagesArray);
			}
			
			/**
			 *	this function will handle
			 *		update system log function
			 *		clear submit lock fucntion
			 *		return success/failure array
			*/
			return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
															'action'         => $action,
															'trackingColumn' => $hourlyWagesArray['Employee_Id'],
															'formName'       => 'Hourly Wage',
															'sessionId'      => $sessionId,
															'tableName'      => $this->_ehrTables->hourlyWages));
		}
		else
		{
			return array('success' => false, 'msg'=>'Salary already exists either in Monthly Salary or Hourly Wages', 'type'=>'info');
		}
		
	
		
		
		$monthlySalaryExist = $this->_db->fetchRow($this->_db->select()->from(array('J'=>$this->_ehrTables->empJob), array())
												 
												 ->joinLeft(array('S'=>$this->_ehrTables->salary), 'J.Employee_Id = S.Employee_Id',
															array('Salary_EmpId'=>'S.Employee_Id'))
												 
												 ->joinLeft(array('H'=>$this->_ehrTables->hourlyWages), 'J.Employee_Id = H.Employee_Id',
															array('Wage_EmpId'=>'H.Employee_Id'))
												 
												 ->where('J.Employee_Id = ?', $monthlySalaryArray['Employee_Id']));
		
		if ((empty($msEmployeeId) && $monthlySalaryExist['Salary_EmpId'] == NULL) ||
			(!empty($msEmployeeId) && $monthlySalaryExist['Salary_EmpId'] != NULL) &&
			$monthlySalaryExist['Wage_EmpId'] == NULL)
		{
			$dbJobDetail = new Employees_Model_DbTable_JobDetail();
							
			$getGradeLocation = $dbJobDetail->getGradeLocation($monthlySalaryArray['Employee_Id']);
			
			$monthlySalaryArray['Location_Id'] = $getGradeLocation['Location_Id'];
			$monthlySalaryArray['Grade_Id']    = $getGradeLocation['Grade_Id'];
			
			if (!empty($msEmployeeId))
			{
				$action = 'Edit';
				
				$monthlySalaryArray['Updated_On'] = date('Y-m-d H:i:s');
				$monthlySalaryArray['Updated_By'] = $sessionId;
				
				$rowAudit = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->salary, array('Employee_Id', 'Location_Id',
														'Grade_Id', 'Basic_Pay', 'Effective_Date', 'Annual_Gross_Salary',
														'Monthly_Gross_Salary', 'Is_OvertimeEmployee', 'Overtime_Wage',
														'Is_PfEmployee', 'Is_InsuranceEmployee'))
												 ->where('Employee_Id = ?', $monthlySalaryArray['Employee_Id']));
				
				if(strtotime($rowAudit['Effective_Date']) == strtotime($this->_ehrTables->changeDateformat($monthlySalaryArray['Effective_Date'])))
				{
					$archiveDate = date('Y-m-d', strtotime ($rowAudit['Effective_Date']));
				}
				else
				{
					$archiveDate = date('Y-m-d', strtotime ( '-1 day' , strtotime ( $this->_ehrTables->changeDateformat($effectiveDate ))));
				}
				
				$getArchiveDate = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->salaryHistory,
																				  new Zend_Db_Expr('Count(Audit_Id)'))
													   ->where('Archive_Date = ?', $archiveDate)
													   ->where('Employee_Id = ?', $monthlySalaryArray['Employee_Id']));
				
				if($rowAudit['Annual_Gross_Salary'] !=  $monthlySalaryArray['Annual_Gross_Salary'] ||
				   $rowAudit['Monthly_Gross_Salary'] != $monthlySalaryArray['Monthly_Gross_Salary'] ||
				   strtotime($rowAudit['Effective_Date']) != strtotime($monthlySalaryArray['Effective_Date']) ||
					$rowAudit['Basic_Pay'] != $monthlySalaryArray['Basic_Pay'] ||
					$rowAudit['Is_PfEmployee'] != $monthlySalaryArray['Is_PfEmployee'] ||
					$rowAudit['Is_InsuranceEmployee']!= $monthlySalaryArray['Is_InsuranceEmployee'])
				{
					if($getArchiveDate>0)
					{
						$auditWhere['Employee_Id = ?'] = $monthlySalaryArray['Employee_Id'];
						$auditWhere['Archive_Date = ?'] = $archiveDate;
						
						$this->_db->update($this->_ehrTables->salaryHistory, array('Location_Id'          => $rowAudit['Location_Id'],
																				   'Grade_Id'             => $rowAudit['Grade_Id'],
																				   'Basic_Pay'            => $rowAudit['Basic_Pay'],
																				   'Annual_Gross_Salary'  => $rowAudit['Annual_Gross_Salary'],
																				   'Monthly_Gross_Salary' => $rowAudit['Monthly_Gross_Salary'],
																				   'Modified_By'          => $sessionId,
																				   'Is_OvertimeEmployee'  => $rowAudit['Is_OvertimeEmployee'],
																				   'Overtime_Wage'        => $rowAudit['Overtime_Wage'],
																				   'Modified_On'          => date('Y-m-d H:i:s'),
																				   'Is_PfEmployee'        => $rowAudit['Is_PfEmployee'],
																				   'Is_InsuranceEmployee' => $rowAudit['Is_InsuranceEmployee']),
										   $auditWhere);
					}
					else
					{
						$this->_db->insert($this->_ehrTables->salaryHistory, array('Employee_Id'          => $monthlySalaryArray['Employee_Id'],
																				   'Location_Id'          => $rowAudit['Location_Id'],
																				   'Grade_Id'             => $rowAudit['Grade_Id'],
																				   'Basic_Pay'            => $rowAudit['Basic_Pay'],
																				   'Effective_Date'       => $rowAudit['Effective_Date'],
																				   'Annual_Gross_Salary'  => $rowAudit['Annual_Gross_Salary'],
																				   'Monthly_Gross_Salary' => $rowAudit['Monthly_Gross_Salary'],
																				   'Archive_Date'         => $archiveDate,
																				   'Modified_By'          => $sessionId,
																				   'Is_OvertimeEmployee'  => $rowAudit['Is_OvertimeEmployee'],
																				   'Overtime_Wage'        => $rowAudit['Overtime_Wage'],
																				   'Modified_On'          => date('Y-m-d H:i:s'),
																				   'Is_PfEmployee'        => $rowAudit['Is_PfEmployee'],
																				   'Is_InsuranceEmployee' => $rowAudit['Is_InsuranceEmployee']));
					}
				}
				
				$updated = $this->_db->update($this->_ehrTables->salary, $monthlySalaryArray, array('Employee_Id = '. $monthlySalaryArray['Employee_Id']));
			}
			else
			{
				$action = 'Add';
				
				$monthlySalaryArray['Added_On'] = date('Y-m-d H:i:s');
				$monthlySalaryArray['Added_By'] = $sessionId;
				
				$updated = $this->_db->insert($this->_ehrTables->salary, $monthlySalaryArray);
			}
			
			/**
			 *	this function will handle
			 *		update system log function
			 *		clear submit lock fucntion
			 *		return success/failure array
			*/
			return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
															'action'         => $action,
															'trackingColumn' => $monthlySalaryArray['Employee_Id'],
															'formName'       => 'Monthly Salary',
															'sessionId'      => $sessionId,
															'tableName'      => $this->_ehrTables->salary));
		}
		else
		{
			return array('success' => false, 'msg'=>'Salary already exists either in Monthly Salary or Hourly Wages', 'type'=>'info');
		}
	}
	
	/**
	 * to delete hourly wage details for an employee
	 */
	public function deleteHourlyWages($employeeId, $sessionId)
	{
		$payslipEmpId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->wagePayslip, new Zend_Db_Expr('COUNT(Payslip_Id)'))
											 ->where('Employee_Id = ?', $employeeId));
	
		$exInBonus = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empBonus, new Zend_Db_Expr('COUNT(Bonus_Id)'))
										  ->where('Employee_Id = ?', $employeeId)
										  ->where("Approval_Status != 'Rejected'"));

		if($payslipEmpId == 0 && $exInBonus==0)
		{
			$deleted = 0;
			
			$wageLock = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->hourlyWages, 'Lock_Flag')
										 ->where('Employee_Id = ?', $employeeId));
			
			if($wageLock == 0)
			{
				$where['Employee_Id = ?'] = $employeeId;
				
				$deleted = $this->_db->delete($this->_ehrTables->hourlyWages,$where);
					
				if ( $deleted)
				{
					$this->_db->delete($this->_ehrTables->auditWages,$where);
				}
			}	
			
			/**
			 *	delete activity for common function
			 *		1)check lock is exist or not.
			 *			If lock is exist then show error message like employee is open record for update.
			 *		2)If No lockflag then process delete activity
			 *		3)Update delete activity in system log
			 *		4)return success/failure message
			*/
			return $this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
														   'lockFlag'       => $wageLock,
														   'formName'       => 'Hourly Wage',
														   'trackingColumn' => $employeeId,
														   'sessionId'      => $sessionId));
		}
		else
		{
			return array('success'=>false, 'msg'=>'<div>Salary record cannot be deleted at the moment as the employee has got one or more of the following exists.
			<li>	1) Payslip has been generated for this employee.</li>
			<li>	2) Bonus has been added for this employee.</li></div>
			Please talk to your admin for next steps', 'type'=>'info');
		}
	}
	
	/**
	 * to show hourly wage information for an employee
	 */
	public function viewHourlyWages($employeeId,$benefitRequired=1)
	{
		$rowHourlyWages = $this->_db->fetchRow($this->_db->select()->from(array('H' => $this->_ehrTables->hourlyWages),
																		  array('H.Added_By','H.Employee_Id','H.Regular_Hourly_Wages',
																				'H.Overtime_Hourly_Wages', 'Is_PfEmployee', 'Is_InsuranceEmployee',
																				new Zend_Db_Expr("date_format(H.Effective_Date, '".$this->_orgDF['sql']."') as Effective_Date"),'H.Effective_Date as SPHP_Effective_Date',
																				new Zend_Db_Expr("date_format(H.Added_On, '".$this->_orgDF['sql']." at %T') as Added_On"), 
																				new Zend_Db_Expr("date_format(H.Updated_On, '".$this->_orgDF['sql']." at %T') as Updated_On"), 'H.Updated_By'))
											   
											   ->joinInner(array('P1'=>$this->_ehrTables->empPersonal),
														   'P1.Employee_Id=H.Employee_Id' ,
														   array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P1.Emp_First_Name, ' ', P1.Emp_Last_Name)")))
											   
											   ->joinInner(array('P2'=>$this->_ehrTables->empPersonal),
														   'P2.Employee_Id=H.Added_By',
														   array('Added_ByName'=>new Zend_Db_Expr("CONCAT(P2.Emp_First_Name, ' ', P2.Emp_Last_Name)")))
											   
											   ->joinLeft(array('P3'=>$this->_ehrTables->empPersonal), 'H.Updated_By=P3.Employee_Id',
														  array('Updated_ByName'=>new Zend_Db_Expr("CONCAT(P3.Emp_First_Name, ' ', P3.Emp_Last_Name)")))
											   
											   ->where('H.Employee_Id = ?', $employeeId));
		
		if(!empty($rowHourlyWages)&&$benefitRequired==1)
		{
			$rowHourlyWages['Benefit'] = $this->getHourlyEmpBenefits($employeeId, $rowHourlyWages['Is_PfEmployee'],
																	 $rowHourlyWages['Is_InsuranceEmployee'], NULL, NULL,
																	 $rowHourlyWages['Effective_Date']);
		}
		
		return $rowHourlyWages;
	}
	
	//version 1.0=> added Is_PfEmployee, Is_InsuranceEmployee details
	/**
	 * to show hourly wages history for an employee
	 */
	public function listHourlyWagesHistory($employeeId)
	{
		$hourlyWagesHistory = $this->_db->fetchAll($this->_db->select()->from(array('A' => $this->_ehrTables->auditWages),
															   array('A.Regular_Hourly_Wages', 'A.Overtime_Hourly_Wages',
																	 'Is_PfEmployee'=>new Zend_Db_Expr('CASE when A.Is_PfEmployee = 1 THEN \'Yes\' ELSE \'No\' END'),
																	 'Is_InsuranceEmployee'=>new Zend_Db_Expr('CASE when A.Is_InsuranceEmployee = 1 THEN \'Yes\' ELSE \'No\' END'),
																	 new Zend_Db_Expr("date_format(A.Effective_Date, '".$this->_orgDF['sql']."') as Effective_Date"),
																	 new Zend_Db_Expr("date_format(A.Archive_Date, '".$this->_orgDF['sql']."') as Archive_Date"),
																	 new Zend_Db_Expr("date_format(A.Modified_On, '".$this->_orgDF['sql']." %T') as ModifiedDate")))
									
									->joinInner(array('P1'=>$this->_ehrTables->empPersonal), 'P1.Employee_Id=A.Employee_Id' ,
												array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P1.Emp_First_Name, ' ', P1.Emp_Last_Name)")))
									
									->joinInner(array('P2'=>$this->_ehrTables->empPersonal), 'P2.Employee_Id=A.Modified_By',
												array('Modified_By'=>new Zend_Db_Expr("CONCAT(P2.Emp_First_Name, ' ', P2.Emp_Last_Name)")))
									
									->where('A.Employee_Id = ?', $employeeId));
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->auditWages, new Zend_Db_Expr('COUNT(Employee_Id)'))
									   ->where('Employee_Id = ?', $employeeId));
		
		/**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $hourlyWagesHistory);	
	}
	
	/**
	 * Get hourly employee benefits
	 */
	public function getHourlyEmpBenefits($employeeId, $pfVal, $insuranceVal, $gratuityVal, $gradeId='', $locationId='', $effectiveDate = NULL)
	{
		if(!empty($employeeId))
		{
			if(!is_null($effectiveDate))
			{
				//changed 09/12/2013 format in to 2013-12-09
				$effectiveDate = date('Y-m-d', strtotime($this->_ehrTables->changeDateformat($effectiveDate)));
			}
			
			$benefitEmp = $this->_dbPersonal->isBenefitsApplicable($employeeId);
			$currencySymbol = $this->_dbPersonal->currencySymbol($employeeId);
			$variableInsuranceDetails = array();
			$fixedInsuranceDetails = array();
			$allowanceAmt = array();
			$allowancePercent = array();
			$amount = 0;
			$pfPercent = 0;
			$gratuityAmt = 0;
            
			if($benefitEmp == 1)
			{
				$arrAllowance = $this->getAllowances($employeeId);
				
				if(!empty($pfVal) && $pfVal==1)
				{
					$dbPf = new Payroll_Model_DbTable_ProvidentFund();
					$pfPercent = $dbPf->getPFShare($employeeId); // employee%+orgshare%
				}
				
				if(!empty($insuranceVal) && $insuranceVal == 1)
				{
					$dbInsurance = new Payroll_Model_DbTable_Insurance();
					$fixedEmpDetails = $dbInsurance->getFixedEmpInsuranceDetails($employeeId, $effectiveDate); // get all the employee level fixed insurance premium
					$fixedOrgDetails = $dbInsurance->getFixedOrgInsuranceDetails($employeeId);// get all the org level fixed insurance premium
					$variableEmpDetails = $dbInsurance->getVariableEmpInsuranceDetails($employeeId,'Hourly'); // get all the variable org+emp share percent at employee level
					//$variableOrgDetails = $dbInsurance->getVariableOrgInsuranceDetails($employeeId);// get all the variable org+emp share percent at org level
					$variableOrgDetails = $dbInsurance->getVariableOrgInsuranceRecords($employeeId);// get all the variable org+emp share percent at org level
				}
				
				if(count($arrAllowance)>0)
				{
					foreach($arrAllowance as $allowance)
					{
						if($allowance['Percentage']!=NULL)
						{
							$amount = $allowance['Percentage'].'%'; // allowance percentage
							$allowanceArr=array($allowance['Allowance_Name'], $amount, $allowance['Period']);
							array_push($allowancePercent, $allowanceArr);
						}
						else
						{
							$amount = number_format($allowance['Amount'],2,'.','');
							$allowanceArr=array($allowance['Allowance_Name'], $amount, $allowance['Period']);
							array_push($allowanceAmt, $allowanceArr);
						}
					}
				}
				
				if(isset($fixedEmpDetails) && !empty($fixedEmpDetails))
				{
					foreach ($fixedEmpDetails as $fixedIns)
					{
						$fixedInsuranceDetails[] = array('Insurance_Name' => $fixedIns['Insurance_Name'],
														 'Premium'        => $fixedIns['Premium'],
														 'Mode'           => $fixedIns['Payment_Frequency']);
					}
				}
					
				if(isset($fixedOrgDetails) && !empty($fixedOrgDetails))
				{
					foreach ($fixedOrgDetails as $fixedIns)
					{
						$fixedInsuranceDetails[] = array('Insurance_Name' => $fixedIns['Insurance_Name'], 
														 'Premium'        => $fixedIns['Premium'],
														 'Mode'           => $fixedIns['Payment_Frequency']);
					}
				}
				
				if(isset($variableEmpDetails) && !empty($variableEmpDetails))
				{
					foreach ($variableEmpDetails as $variableIns)
					{
						if(!empty($variableIns['Share']))
						{
							$variableInsuranceDetails[] = array('Insurance_Name'=>$variableIns['Insurance_Name'],
																'Premium'=>$variableIns['Share'],
																'Mode'=>$variableIns['Payment_Frequency']);
						}
					}
				}
				
				if(isset($variableOrgDetails) && !empty($variableOrgDetails))
				{
					foreach ($variableOrgDetails as $variableIns)
					{
						if(!empty($variableIns['Share']))
						{
							$variableInsuranceDetails[]=array('Insurance_Name'=>$variableIns['Insurance_Name'], 'Premium'=>$variableIns['Share'],
									'Mode'=>$variableIns['Payment_Frequency']);
						}
					}
				}
			}
            
            
            if(!empty($gratuityVal) && $gratuityVal == 1)
            {
                $gratuityAmt = $this->_dbCommonFun->getGratuityAmount($employeeId,'','','HWSAL');
            }
            
			
			$getPayslipRecordCount = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empPfPayment,
																					 new Zend_Db_Expr('count(Employee_Id)'))
														  ->where('Employee_Id = ?',$employeeId));
			
			//if PF is not included for employee who has already paid PF
			if(empty($pfVal) && $getPayslipRecordCount > 0)
			{
				return array('AllowancePercent'=>'', 'AllowanceAmt'=> '', 'Eligible'=>$benefitEmp,'pfVal'=> $pfVal, 'Pf'=> "0",'pfFixedVariable'=>"",'Symb'=>$currencySymbol, 'Gratuity'=>$gratuityAmt, 'InsVal'=>$insuranceVal, 'FixedInsurance'=>'', 'VariableInsurance'=>'', 'Msg'=>'Please add the provident fund record for the employee!');
			}
			
			//if pf value exist
			if($pfPercent[0] != '0')
			{
				return array('AllowancePercent'=>$allowancePercent, 'AllowanceAmt'=> $allowanceAmt,'Msg'=>'', 'Eligible'=>$benefitEmp,'pfVal'=> $pfVal ,'Pf'=>$pfPercent[0],'pfFixedVariable'=>$pfPercent[1],'Symb'=>$currencySymbol, 'Gratuity'=>$gratuityAmt, 'InsVal'=>$insuranceVal, 'FixedInsurance'=>$fixedInsuranceDetails, 'VariableInsurance'=>$variableInsuranceDetails);
			}
			else
			{
				return array('AllowancePercent'=>$allowancePercent, 'AllowanceAmt'=> $allowanceAmt,'Msg'=>'', 'Eligible'=>$benefitEmp,'pfVal'=> $pfVal, 'Pf'=> "0",'pfFixedVariable'=>"",'Symb'=>$currencySymbol, 'Gratuity'=>$gratuityAmt, 'InsVal'=>$insuranceVal, 'FixedInsurance'=>$fixedInsuranceDetails, 'VariableInsurance'=>$variableInsuranceDetails);
			}
		}
	}
	
	/**
	 * Get basic pay by employeeId
	 */
	public function basicPay($employeeId)
	{
		return $this->_db->fetchRow($this->_db->select()->from(array('S'=>$this->_ehrTables->salary),
															   array('New_Date'=>'S.Effective_Date','New_BasicPay'=>'S.Basic_Pay'))
									
									->joinLeft(array('SH'=>$this->_ehrTables->salaryHistory), 'S.Employee_Id=SH.Employee_Id',
											   array('Old_Date'=>'SH.Effective_Date','SH.Archive_Date','Old_BasicPay'=>'SH.Basic_Pay'))
									
									->where('S.Employee_Id = ?',$employeeId));
	}
	
	//getting basic + allowance amount for validating advance salary
	public function getBasicPay($employeeId)
	{
		if(!empty($employeeId))
		{
			$basicPay = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->salary, 'Basic_Pay')
											 ->where('Employee_Id = ?',$employeeId));
			$allowanceAmount = 0;
			
			if(!empty($basicPay))
			{
				$employeeResignationDate = $this->_dbCommonFun->getEmployeeResignationDate($employeeId);
				$employeeResignationDate = (!empty($employeeResignationDate)) ? $employeeResignationDate : NULL;
				$allowanceAmount = $this->getAllowances($employeeId,NULL, NULL, $basicPay, "Advance Salary",NULL,NULL,$employeeResignationDate);
			}
			
			return ($basicPay + $allowanceAmount);
		}
	}
	
	/**
	 * to get allowances by employee's grade and location level or by organizational level
	 */
	public function getAllowances($employeeId, $gradeId = '', $locationId = '', $basicPay = NULL, $benefitAssociation = NULL, $isPayslipForm=NULL,$unpaidLeave=NULL,$effectiveDate=NULL,$formName=NULL)
	{
		$qryrowGradeLocation = $this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),'J.Location_Id')
												 
												 ->joinInner(array('P'=>$this->_ehrTables->empPersonal),
															 'P.Employee_Id = J.Employee_Id', array())
												 
												 ->joinLeft(array('D'=>$this->_ehrTables->designation),
															'D.Designation_Id=J.Designation_Id', 'D.Grade_Id')
												 
												 ->where('J.Employee_Id = ?', $employeeId)
												 ->where('P.Form_Status = 1');


		if ($formName != 'Tax Declaration' && $formName != 'Reimbursement')
		{
			/** get employee active status based on effective date **/
			$activeWhere = $this->_dbCommonFun->getActiveEmployees($effectiveDate);
			$qryrowGradeLocation->where($activeWhere);
		}										 
		
		$rowGradeLocation = $this->_db->fetchRow($qryrowGradeLocation);
		
		if(empty($gradeId))
		{
			$gradeId = $rowGradeLocation['Grade_Id'];
		}
		
		if(empty($locationId))
		{
			$locationId = $rowGradeLocation['Location_Id'];
		}
		
		$totalAllowanceAvaliable = array();
		$totalAllowanceTypeIdAvaliable = array();
		
		$empLevelAllowances = $this->_db->select()->from(array('A'=>$this->_ehrTables->allowances), array( 'Allowance_Id'))
												   
												   ->joinInner(array('AT'=>$this->_ehrTables->allowanceTypes), 'A.Allowance_Type_Id = AT.Allowance_Type_Id', array('A.Allowance_Type_Id'))
												   
												   ->where('A.Employee_Id = ?', $employeeId)
												   ->where('A.Coverage LIKE ?', 'EMP')
												   ->where('Allowance_Status LIKE ?', 'Active');
		
        $empLevelAllowances = $this->_db->fetchAll($empLevelAllowances);
        
		if (!empty($empLevelAllowances))
		{
			foreach($empLevelAllowances as $val)
			{
				array_push($totalAllowanceAvaliable, $val['Allowance_Id']);
				array_push($totalAllowanceTypeIdAvaliable, $val['Allowance_Type_Id']);
			}
		}
		
		$qryGradeLocLevelAllowances = $this->_db->select()->from(array('A'=>$this->_ehrTables->allowances),array( 'Allowance_Id','A.Allowance_Type_Id'))
														->joinInner(array('AT'=>$this->_ehrTables->allowanceTypes),
																	'A.Allowance_Type_Id = AT.Allowance_Type_Id', array())
														->where('A.Coverage LIKE ?', 'GRADE_LOC')
														->where('Allowance_Status LIKE ?', 'Active');

		if(!empty($gradeId))
			$qryGradeLocLevelAllowances->where('A.Grade_Id = ?', $gradeId);

		if(!empty($locationId))
			$qryGradeLocLevelAllowances->where('A.Location_Id = ?', $locationId);
														
		if(!empty($totalAllowanceAvaliable))
			$qryGradeLocLevelAllowances->where('A.Allowance_Id NOT IN (?)', $totalAllowanceAvaliable);
			
		if(!empty($totalAllowanceTypeIdAvaliable))
			$qryGradeLocLevelAllowances->where('A.Allowance_Type_Id NOT IN (?)', $totalAllowanceTypeIdAvaliable);
		
		$gradeLocLevelAllowances = $this->_db->fetchAll($qryGradeLocLevelAllowances);
		
		if (!empty($gradeLocLevelAllowances))
		{
			foreach($gradeLocLevelAllowances as $val)
			{
				array_push($totalAllowanceAvaliable, $val['Allowance_Id']);
				array_push($totalAllowanceTypeIdAvaliable, $val['Allowance_Type_Id']);
			}
		}	
		
		$qryGradeLevelAllowances = $this->_db->select()->from(array('A'=>$this->_ehrTables->allowances),array( 'Allowance_Id','A.Allowance_Type_Id'))
														->joinInner(array('AT'=>$this->_ehrTables->allowanceTypes),
																	'A.Allowance_Type_Id = AT.Allowance_Type_Id', array())
														->where('A.Coverage LIKE ?', 'GRADE')
														->where('Allowance_Status LIKE ?', 'Active');
		
		if(!empty($totalAllowanceAvaliable))
			$qryGradeLevelAllowances->where('A.Allowance_Id NOT IN (?)', $totalAllowanceAvaliable);
			
		if(!empty($totalAllowanceTypeIdAvaliable))
			$qryGradeLevelAllowances->where('A.Allowance_Type_Id NOT IN (?)', $totalAllowanceTypeIdAvaliable);

		if(!empty($gradeId))
			$qryGradeLevelAllowances->where('A.Grade_Id = ?', $gradeId);			
		
		$gradeLevelAllowances = $this->_db->fetchAll($qryGradeLevelAllowances);
		
		if (!empty($gradeLevelAllowances))
		{
			foreach($gradeLevelAllowances as $val)
			{
				array_push($totalAllowanceAvaliable, $val['Allowance_Id']);
				array_push($totalAllowanceTypeIdAvaliable, $val['Allowance_Type_Id']);
			}
		}	
		
		$qryOrgLevelAllowances = $this->_db->select()->from(array('A'=>$this->_ehrTables->allowances),array( 'Allowance_Id','A.Allowance_Type_Id'))
														->joinInner(array('AT'=>$this->_ehrTables->allowanceTypes),
																	'A.Allowance_Type_Id = AT.Allowance_Type_Id', array())
														
														->where('A.Coverage LIKE ?', 'ORG')
														->where('Allowance_Status LIKE ?', 'Active');
		
		if(!empty($totalAllowanceAvaliable))
			$qryOrgLevelAllowances->where('A.Allowance_Id NOT IN (?)', $totalAllowanceAvaliable);
			
		if(!empty($totalAllowanceTypeIdAvaliable))
			$qryOrgLevelAllowances->where('A.Allowance_Type_Id NOT IN (?)', $totalAllowanceTypeIdAvaliable);
		
		$orgLevelAllowances = $this->_db->fetchAll($qryOrgLevelAllowances);
		
		if (!empty($orgLevelAllowances))
		{
			foreach($orgLevelAllowances as $val)
			{
				array_push($totalAllowanceAvaliable, $val['Allowance_Id']);
				array_push($totalAllowanceTypeIdAvaliable, $val['Allowance_Type_Id']);
			}
		}
		
		$benefitAssociationArray =$finalAllowanceId= array();
		
		//getting the allowances include for pf or insurance or advance salary
		if(!is_null($benefitAssociation))
		{
			if($benefitAssociation != 'All')
			{
				foreach($totalAllowanceAvaliable as $allowanceId)
				{
					$getAllowancesBenefits = $this->_dbCommonFun->getAllowanceBenefitAssociation($allowanceId);

					if(!empty($getAllowancesBenefits))
					{
						$allowancesIds = '';
						if($benefitAssociation=='PF')
						{
							$benefitAssociation = 'Provident Fund';
						}

						if($benefitAssociation=='ETF')
						{
							$benefitAssociation = 'NPS';
						}
					
						if(in_array($benefitAssociation,$getAllowancesBenefits))
						{
							$allowancesIds = $allowanceId;
						}

						if($unpaidLeave == 'Unpaid Leave')
						{
							if(!empty($allowancesIds) && in_array($unpaidLeave,$getAllowancesBenefits))
							{
								$allowancesIds = $allowanceId;
							}
						}

						if(!empty($allowancesIds))
						{
							array_push($benefitAssociationArray, $allowancesIds);
						}
					}
				}
				
				if(!empty($benefitAssociationArray))
				{
					array_push($finalAllowanceId, $benefitAssociationArray);
				}
			}
			else
			{
				if(!empty($totalAllowanceAvaliable))
				{
					array_push($finalAllowanceId, $totalAllowanceAvaliable);
				}
			}
		}
		else
		{
			array_push($finalAllowanceId, $totalAllowanceAvaliable);
		}
		
		$allowancesDetails = array();
		
		if(count($finalAllowanceId) > 0)
		{
			foreach($finalAllowanceId[0] as $allownaces)
			{
				$qryAllowancesDetails = $this->_db->select()->from(array('A'=>$this->_ehrTables->allowances),array( 'A.Allowance_Id','A.Percentage',
																					'A.Amount','A.Coverage','A.As_Is_Payment','A.FBP_Max_Declaration'))
												
												->joinInner(array('AT'=>$this->_ehrTables->allowanceTypes),'A.Allowance_Type_Id = AT.Allowance_Type_Id', array('AT.Allowance_Name','AT.Workflow_Id',
																	'AT.Allowance_Type_Id','AT.Tax_Inclusion','AT.Formula_Based','AT.Period','AT.Allowance_Mode','AT.Is_Claim_From_Reimbursement',
																	'AT.Consider_For_EPF_Contribution','AT.Is_Flexi_Benefit_Plan','AT.Perquisites_Id'))
												->where('Allowance_Status LIKE ?', 'Active')
												->where('A.Allowance_Id = ?', $allownaces);
				
				if(is_null($benefitAssociation))
				{
					$qryAllowancesDetails->joinLeft(array('ABA'=>$this->_ehrTables->allowanceBenefitAssoc), 'ABA.Allowance_Type_Id = A.Allowance_Type_Id',
													array('BenefitForms'=>new Zend_Db_Expr('group_concat(BF.Form_Name)')))
							->joinLeft(array('BF'=>$this->_ehrTables->benefitForms), 'BF.Form_Id = ABA.Form_Id', array(''));
				}
				
                if($unpaidLeave == 'Unpaid Leave')
                {
                    $qryAllowancesDetails->joinInner(array('BA'=>$this->_ehrTables->allowanceBenefitAssoc),
                                                        'A.Allowance_Type_Id = BA.Allowance_Type_Id', array(''))
										 ->where('BA.Form_Id=?',31);
                }

				array_push($allowancesDetails, $this->_db->fetchRow($qryAllowancesDetails));
			}
		}
		
		$allowanceAmount = 0;
		
		//calculating allowance amount that includes pf or insurance or advance salary
		if($isPayslipForm != 1)
		{
			if(!is_null($benefitAssociation))
			{
				if(count($allowancesDetails) > 0)
				{
					foreach($allowancesDetails as $allowance)
					{
						$allowanceAmount+=$this->calculateAllowanceAmount($basicPay,$allowance);
					}
				}
				return $allowanceAmount;
			}
		}
		
        return $allowancesDetails;
	}

	public function calculateAllowanceAmount($basicPay,$allowance)
	{
		$allowanceAmount = 0;
		$period = array('Quarterly' => 3, 'HalfYearly' => 6, 'Monthly' => 1, 'Annually' => 12);
		if($allowance['Percentage']!=NULL)
		{
			$allowanceAmount = number_format(($basicPay*($allowance['Percentage']/100))*1,2,'.','');
		}
		else
		{
			$allowanceAmount = number_format($allowance['Amount']/$period[$allowance['Period']],2,'.','');
		}
		return $allowanceAmount;
	}
	
	//Get adhoc allowance only for insurance benefit assoisation
	public function getAdhocAllowance($employeeId, $benefitAssosiation, $salaryDate, $cutoffDate, $prevCutoffDate)
	{
		$empadhocAllowancesDetails = $adhocAllowanceDetails = array();
		if($benefitAssosiation == 'Variable Insurance' && $employeeId >0)
		{
			$empadhocAllowancesDetails = $this->_db->fetchAll($this->_db->select()->from(array('AA'=>$this->_ehrTables->adhocAllowance),
																			 array('Adhoc_Allowance_Id','Amount', 'Tax_Inclusion'))
												  
												  ->joinInner(array('AABA'=>$this->_ehrTables->adhocAllowBenefitAssoc),
															  'AABA.Adhoc_Allowance_Id = AA.Adhoc_Allowance_Id', array(''))
												  
												  ->joinInner(array('BF'=>$this->_ehrTables->benefitForms),
															  'BF.Form_Id = AABA.Form_Id', array(''))
												  
												  ->where('BF.Form_Name = ?', 'Variable Insurance')
												  ->where('BF.Form_Id = ?', 58)
												  ->where('AA.Employee_Id = ?', $employeeId)
												  ->where("(".new Zend_Db_Expr('DATE(Consideration_Date)') ." >= '".$salaryDate."' and ". new Zend_Db_Expr('DATE(Consideration_Date)') ." <= '".$cutoffDate."') or
                                                          (".new Zend_Db_Expr('DATE(Consideration_Date)') ." < '".$salaryDate."' and ". new Zend_Db_Expr('DATE(Consideration_Date)') ." > '".$prevCutoffDate."')"));
		}
		
			$orgadhocAllowancesDetails = $this->_db->fetchAll($this->_db->select()->from(array('AA'=>$this->_ehrTables->adhocAllowance),
																			 array('Adhoc_Allowance_Id','Amount'))
												  
												  ->joinInner(array('AABA'=>$this->_ehrTables->adhocAllowBenefitAssoc),
															  'AABA.Adhoc_Allowance_Id = AA.Adhoc_Allowance_Id', array(''))
												  
												  ->joinInner(array('BF'=>$this->_ehrTables->benefitForms),
															  'BF.Form_Id = AABA.Form_Id', array(''))
												  
												  ->where('BF.Form_Name = ?', 'Variable Insurance')
												  ->where('BF.Form_Id = ?', 58)
												  ->where('AA.Coverage = ?', "Organization")
												  ->where("(".new Zend_Db_Expr('DATE(Consideration_Date)') ." >= '".$salaryDate."' and ". new Zend_Db_Expr('DATE(Consideration_Date)') ." <= '".$cutoffDate."') or
                                                          (".new Zend_Db_Expr('DATE(Consideration_Date)') ." < '".$salaryDate."' and ". new Zend_Db_Expr('DATE(Consideration_Date)') ." > '".$prevCutoffDate."')"));
		
		
		if(!empty($empadhocAllowancesDetails))
			$adhocAllowanceDetails = array_merge($orgadhocAllowancesDetails, $empadhocAllowancesDetails);
		else
			$adhocAllowanceDetails = $orgadhocAllowancesDetails;
		
		return $adhocAllowanceDetails;
	}

	//Get adhoc allowance only for provident fund benefit assoisation
	public function getPfTaggedAdhocAllowance($employeeId, $benefitAssosiation, $salaryDate, $cutoffDate, $prevCutoffDate)
	{
		$empadhocAllowancesDetails = $adhocAllowanceDetails = array();
		if($benefitAssosiation == 'Provident Fund' && $employeeId >0)
		{
			$empadhocAllowancesDetails = $this->_db->fetchAll($this->_db->select()->from(array('AA'=>$this->_ehrTables->adhocAllowance),
																			 array('Adhoc_Allowance_Id','Amount', 'Tax_Inclusion'))
												  
												  ->joinInner(array('AABA'=>$this->_ehrTables->adhocAllowBenefitAssoc),
															  'AABA.Adhoc_Allowance_Id = AA.Adhoc_Allowance_Id', array(''))
												  
												  ->joinInner(array('BF'=>$this->_ehrTables->benefitForms),
															  'BF.Form_Id = AABA.Form_Id', array(''))
												  
												  ->where('BF.Form_Name = ?', 'Provident Fund')
												  ->where('BF.Form_Id = ?', 52)
												  ->where('AA.Employee_Id = ?', $employeeId)
												  ->where("(".new Zend_Db_Expr('DATE(Consideration_Date)') ." >= '".$salaryDate."' and ". new Zend_Db_Expr('DATE(Consideration_Date)') ." <= '".$cutoffDate."') or
                                                          (".new Zend_Db_Expr('DATE(Consideration_Date)') ." < '".$salaryDate."' and ". new Zend_Db_Expr('DATE(Consideration_Date)') ." > '".$prevCutoffDate."')"));
		}
		
			$orgadhocAllowancesDetails = $this->_db->fetchAll($this->_db->select()->from(array('AA'=>$this->_ehrTables->adhocAllowance),
																			 array('Adhoc_Allowance_Id','Amount'))
												  
												  ->joinInner(array('AABA'=>$this->_ehrTables->adhocAllowBenefitAssoc),
															  'AABA.Adhoc_Allowance_Id = AA.Adhoc_Allowance_Id', array(''))
												  
												  ->joinInner(array('BF'=>$this->_ehrTables->benefitForms),
															  'BF.Form_Id = AABA.Form_Id', array(''))
												  
												  ->where('BF.Form_Name = ?', 'Provident Fund')
												  ->where('BF.Form_Id = ?', 52)
												  ->where('AA.Coverage = ?', "Organization")
												  ->where("(".new Zend_Db_Expr('DATE(Consideration_Date)') ." >= '".$salaryDate."' and ". new Zend_Db_Expr('DATE(Consideration_Date)') ." <= '".$cutoffDate."') or
                                                          (".new Zend_Db_Expr('DATE(Consideration_Date)') ." < '".$salaryDate."' and ". new Zend_Db_Expr('DATE(Consideration_Date)') ." > '".$prevCutoffDate."')"));
		
		
		if(!empty($empadhocAllowancesDetails))
			$adhocAllowanceDetails = array_merge($orgadhocAllowancesDetails, $empadhocAllowancesDetails);
		else
			$adhocAllowanceDetails = $orgadhocAllowancesDetails;
		
		return $adhocAllowanceDetails;
	}

	/*get Nps tagged adhoc allowance for given duration */
	public function getNpsTaggedAdhocAllowance($employeeId, $benefitAssosiation, $salaryDate, $cutoffDate, $prevCutoffDate)
	{
		$empadhocAllowancesDetails = $adhocAllowanceDetails = array();
		if($benefitAssosiation == 'NPS' && $employeeId >0)
		{
			$empadhocAllowancesDetails = $this->_db->fetchAll($this->_db->select()->from(array('AA'=>$this->_ehrTables->adhocAllowance),
																			 array('Adhoc_Allowance_Id','Amount', 'Tax_Inclusion'))
												  
												  ->joinInner(array('AABA'=>$this->_ehrTables->adhocAllowBenefitAssoc),
															  'AABA.Adhoc_Allowance_Id = AA.Adhoc_Allowance_Id', array(''))
												  
												  ->joinInner(array('BF'=>$this->_ehrTables->benefitForms),
															  'BF.Form_Id = AABA.Form_Id', array(''))
												  
												  ->where('BF.Form_Name = ?', 'NPS')
												  ->where('BF.Form_Id = ?', 126)
												  ->where('AA.Employee_Id = ?', $employeeId)
												  ->where("(".new Zend_Db_Expr('DATE(Consideration_Date)') ." >= '".$salaryDate."' and ". new Zend_Db_Expr('DATE(Consideration_Date)') ." <= '".$cutoffDate."') or
                                                          (".new Zend_Db_Expr('DATE(Consideration_Date)') ." < '".$salaryDate."' and ". new Zend_Db_Expr('DATE(Consideration_Date)') ." > '".$prevCutoffDate."')"));
		}
		
		$orgadhocAllowancesDetails = $this->_db->fetchAll($this->_db->select()->from(array('AA'=>$this->_ehrTables->adhocAllowance),
																			array('Adhoc_Allowance_Id','Amount'))
												
												->joinInner(array('AABA'=>$this->_ehrTables->adhocAllowBenefitAssoc),
															'AABA.Adhoc_Allowance_Id = AA.Adhoc_Allowance_Id', array(''))
												
												->joinInner(array('BF'=>$this->_ehrTables->benefitForms),
															'BF.Form_Id = AABA.Form_Id', array(''))
												
												->where('BF.Form_Name = ?', 'NPS')
												->where('BF.Form_Id = ?', 126)
												->where('AA.Coverage = ?', "Organization")
												->where("(".new Zend_Db_Expr('DATE(Consideration_Date)') ." >= '".$salaryDate."' and ". new Zend_Db_Expr('DATE(Consideration_Date)') ." <= '".$cutoffDate."') or
														(".new Zend_Db_Expr('DATE(Consideration_Date)') ." < '".$salaryDate."' and ". new Zend_Db_Expr('DATE(Consideration_Date)') ." > '".$prevCutoffDate."')"));
		
		
		if(!empty($empadhocAllowancesDetails))
			$adhocAllowanceDetails = array_merge($orgadhocAllowancesDetails, $empadhocAllowancesDetails);
		else
			$adhocAllowanceDetails = $orgadhocAllowancesDetails;
		
		return $adhocAllowanceDetails;
	}

	/*Get Professional Tax tagged adhoc allowance for given duration */
	public function getPTTaggedAdhocAllowance($employeeId, $benefitAssosiation, $salaryDate, $cutoffDate, $prevCutoffDate)
	{
		$empadhocAllowancesDetails = $adhocAllowanceDetails = array();
		if($benefitAssosiation == 'Professional Tax' && $employeeId >0)
		{
			$empadhocAllowancesDetails = $this->_db->fetchAll($this->_db->select()->from(array('AA'=>$this->_ehrTables->adhocAllowance),
																			 array('Adhoc_Allowance_Id','Amount', 'Tax_Inclusion'))
												  
												  ->joinInner(array('AABA'=>$this->_ehrTables->adhocAllowBenefitAssoc),
															  'AABA.Adhoc_Allowance_Id = AA.Adhoc_Allowance_Id', array(''))
												  
												  ->joinInner(array('BF'=>$this->_ehrTables->benefitForms),
															  'BF.Form_Id = AABA.Form_Id', array(''))
												  
												  ->where('BF.Form_Name = ?', 'Professional Tax')
												  ->where('BF.Form_Id = ?', 90)
												  ->where('AA.Employee_Id = ?', $employeeId)
												  ->where("(".new Zend_Db_Expr('DATE(Consideration_Date)') ." >= '".$salaryDate."' and ". new Zend_Db_Expr('DATE(Consideration_Date)') ." <= '".$cutoffDate."') or
                                                          (".new Zend_Db_Expr('DATE(Consideration_Date)') ." < '".$salaryDate."' and ". new Zend_Db_Expr('DATE(Consideration_Date)') ." > '".$prevCutoffDate."')"));
		}
		
		$orgadhocAllowancesDetails = $this->_db->fetchAll($this->_db->select()->from(array('AA'=>$this->_ehrTables->adhocAllowance),
																			array('Adhoc_Allowance_Id','Amount'))
												
												->joinInner(array('AABA'=>$this->_ehrTables->adhocAllowBenefitAssoc),
															'AABA.Adhoc_Allowance_Id = AA.Adhoc_Allowance_Id', array(''))
												
												->joinInner(array('BF'=>$this->_ehrTables->benefitForms),
															'BF.Form_Id = AABA.Form_Id', array(''))
												
												->where('BF.Form_Name = ?', 'Professional Tax')
												->where('BF.Form_Id = ?', 90)
												->where('AA.Coverage = ?', "Organization")
												->where("(".new Zend_Db_Expr('DATE(Consideration_Date)') ." >= '".$salaryDate."' and ". new Zend_Db_Expr('DATE(Consideration_Date)') ." <= '".$cutoffDate."') or
														(".new Zend_Db_Expr('DATE(Consideration_Date)') ." < '".$salaryDate."' and ". new Zend_Db_Expr('DATE(Consideration_Date)') ." > '".$prevCutoffDate."')"));
		
		
		if(!empty($empadhocAllowancesDetails))
			$adhocAllowanceDetails = array_merge($orgadhocAllowancesDetails, $empadhocAllowancesDetails);
		else
			$adhocAllowanceDetails = $orgadhocAllowancesDetails;
		
		return $adhocAllowanceDetails;
	}


	public function getBenefitTaggedAdhocAllowance($employeeId, $benefitAssosiation, $salaryDate, $cutoffDate, $prevCutoffDate)
	{
		$adhocAllowanceDetails = array();
		$qryAdhocAllowanceDetails = $this->_db->select()->from(array('AA'=>$this->_ehrTables->adhocAllowance),
																			array('AA.Adhoc_Allowance_Id','AA.Amount', 'AA.Tax_Inclusion'))
												->joinInner(array('AABA'=>$this->_ehrTables->adhocAllowBenefitAssoc),
															'AABA.Adhoc_Allowance_Id = AA.Adhoc_Allowance_Id', array(''))
												->joinInner(array('BF'=>$this->_ehrTables->benefitForms),
															'BF.Form_Id = AABA.Form_Id', array(''))
												->where("(".new Zend_Db_Expr('DATE(Consideration_Date)') ." >= '".$salaryDate."' and ". new Zend_Db_Expr('DATE(Consideration_Date)') ." <= '".$cutoffDate."') or
														(".new Zend_Db_Expr('DATE(Consideration_Date)') ." < '".$salaryDate."' and ". new Zend_Db_Expr('DATE(Consideration_Date)') ." > '".$prevCutoffDate."')");
		if($employeeId >0)
		{
			$qryAdhocAllowanceDetails->where(new Zend_Db_Expr("AA.Employee_Id = $employeeId OR AA.Coverage = 'Organization'"));
		}
		else
		{
			$qryAdhocAllowanceDetails->where('AA.Coverage = ?', "Organization");
		}
		
		if(!empty($benefitAssosiation))
		{
			$qryAdhocAllowanceDetails->where('BF.Form_Name = ?',$benefitAssosiation);
		}

		$adhocAllowanceDetails = $this->_db->fetchAll($qryAdhocAllowanceDetails);
		
		return $adhocAllowanceDetails;
	}
	
	/**
	 * to validate hourly wage, overtime wage and annual salary in the server side while adding or updating
	 * salary for an employee.
	 */
	public function checkSalaryWages($employeeId)
	{
		return $this->_db->fetchRow($this->_db->select()
									->from(array('J'=>$this->_ehrTables->empJob), array())
									
									->joinInner(array('D'=>$this->_ehrTables->designation), 'D.Designation_Id=J.Designation_Id', array())
									
									->joinInner(array('G'=>$this->_ehrTables->empGrade), 'G.Grade_Id = D.Grade_Id',
												array('G.Min_HourlyWages', 'G.Max_HourlyWages', 'G.Min_OvertimeWages', 'G.Max_OvertimeWages',
													  'G.Min_AnnualSalary', 'G.Max_AnnualSalary'))
									
									->where('J.Employee_Id = ?', $employeeId));
	}
	
		//version 0.9=> changed the function parameter by adding = NULL
	public function hourlyWagesEmployee($employeeId, $location = NULL, $dept = NULL, $salaryDate, $lastDate,$serviceProviderId=NULL)
	{
		$qryWageHistory = $this->_db->select()->distinct()->from(array('Wh'=>$this->_ehrTables->auditWages), 'Employee_Id')
							->joinInner(array('J'=>$this->_ehrTables->empJob), 'Wh.Employee_Id = J.Employee_Id', array());
		
		if(!is_null($location) && !is_null($dept))
		{
			$qryWageHistory->where('Wh.Location_Id in (?)', $location)->where('J.Department_Id in (?)', $dept);
		}
		
		$qryWageHistory->where('Wh.Effective_Date <= :salaryDate or (Wh.Effective_Date >= :salaryDate and Wh.Effective_Date <= :lastSalaryDate)')
		->where('Wh.Archive_Date >= :salaryDate or (Wh.Archive_Date >= :salaryDate and Wh.Archive_Date <= :lastSalaryDate)')
		->bind(array(':salaryDate'=>$salaryDate, ':lastSalaryDate'=>$lastDate));
			
		if(count($employeeId)>0)
		{
			$qryWageHistory->where('Wh.Employee_Id not in (?)', $employeeId);
		}
		
		if($this->_orgDetails['Field_Force']==1 && !empty($serviceProviderId))
        {
            $qryWageHistory->where('J.Service_Provider_Id = ?', $serviceProviderId);
        }
		
		$rowWageHistory = $this->_db->fetchCol($qryWageHistory);
			
		$qryWages = $this->_db->select()->from(array('W'=>$this->_ehrTables->hourlyWages), 'Employee_Id')
					->joinInner(array('J'=>$this->_ehrTables->empJob), 'W.Employee_Id = J.Employee_Id', array());
		
		if(!is_null($location) && !is_null($dept))
		{
			$qryWages->where('W.Location_Id in (?)', $location)->where('J.Department_Id in (?)', $dept);
		}
		
		$qryWages->where('W.Effective_Date <= :salaryDate or (W.Effective_Date >= :salaryDate and W.Effective_Date <= :lastSalaryDate)')
		->bind(array(':salaryDate'=>$salaryDate, ':lastSalaryDate'=>$lastDate));
		
		if(count($employeeId)>0)
		{
			$qryWages->where('W.Employee_Id not in (?)', $employeeId);
		}

		if($this->_orgDetails['Field_Force']==1 && !empty($serviceProviderId))
        {
            $qryWages->where('J.Service_Provider_Id = ?', $serviceProviderId);
        }

		$rowWages = $this->_db->fetchCol($qryWages);
		
		if(!empty($rowWageHistory))
		{
			foreach ($rowWageHistory as $wageHistory)
			{
				array_push($rowWages, $wageHistory);
			}
		}
		
		if(!empty($rowWages))
		{
			$rowHourlyWages = array_unique($rowWages);
			return $rowHourlyWages;
		}
	}
	
	/**
	 * Get monthly salary employee details by employeeId
	 */
	public function monthlySalaryEmployee($monthlyEmpId, $location = NULL, $dept = NULL, $type = NULL, $salaryDate, $lastDate)
	{
		$qrySalaryHistory = $this->_db->select()->distinct()->from(array('Sh'=>$this->_ehrTables->salaryHistory), 'Employee_Id')
									->joinInner(array('J'=>$this->_ehrTables->empJob), 'Sh.Employee_Id = J.Employee_Id', array());
									
		if(!is_null($location) && !is_null($dept) && !is_null($type))
		{
			$qrySalaryHistory->where('Sh.Location_Id in (?)', $location)->where('J.Department_Id in (?)', $dept)
			->where('J.EmpType_Id in (?)', $type);
		}
		
		$qrySalaryHistory->where('Sh.Effective_Date <= :salaryDate or (Sh.Effective_Date >= :salaryDate and Sh.Effective_Date <= :lastSalaryDate)')
		->where('Sh.Archive_Date >= :salaryDate or (Sh.Archive_Date >= :salaryDate and Sh.Archive_Date <= :lastSalaryDate)')
		->order(array('Sh.Effective_Date ASC', 'Sh.Archive_Date ASC'))
		->bind(array(':salaryDate'=>$salaryDate, ':lastSalaryDate'=>$lastDate));
		
		if(count($monthlyEmpId)>0)
		{
			$qrySalaryHistory->where('Sh.Employee_Id not in (?)', $monthlyEmpId);
		}
		
		$rowSalaryHistory = $this->_db->fetchCol($qrySalaryHistory);
		
		$qrySalary = $this->_db->select()->from(array('S'=>$this->_ehrTables->salary), 'Employee_Id')
					->joinInner(array('J'=>$this->_ehrTables->empJob), 'S.Employee_Id = J.Employee_Id', array());
		
		if(!is_null($location) && !is_null($dept) && !is_null($type))
		{
			$qrySalary->where('S.Location_Id in (?)', $location)->where('J.Department_Id in (?)', $dept)
			->where('J.EmpType_Id in (?)', $type);
		}
		
		$qrySalary->where('S.Effective_Date <= :salaryDate or (S.Effective_Date >= :salaryDate and S.Effective_Date <= :lastSalaryDate)')
		->bind(array(':salaryDate'=>$salaryDate, ':lastSalaryDate'=>$lastDate));
		
		if(count($monthlyEmpId)>0)
		{
			$qrySalary->where('S.Employee_Id not in (?)', $monthlyEmpId);
		}
			
		$rowSalary = $this->_db->fetchCol($qrySalary);
		
		if(!empty($rowSalaryHistory))
		{
			foreach ($rowSalaryHistory as $salaryHistory)
			{
				array_push($rowSalary, $salaryHistory);
			}
		}

        //HRAPPDEV-1395 array_unique returns the wrong array count so we pushed the details into an tempsalary array and return the value 
		$temp = array_unique($rowSalary);
		$tempSalary = array();
        if(!empty($temp))
        {
            foreach ($temp as $tempHistory)
            {
                array_push($tempSalary, $tempHistory);
            }
        }

        return $tempSalary;
	}
	
	public function getMonthlyBasicPayAmt($employeeId)
	{
		$qrySalary = $this->_db->select()->from(array('S'=>$this->_ehrTables->salary), array('S.Basic_Pay','S.Is_PfEmployee','S.Is_InsuranceEmployee','Is_ETFEmployee','S.Annual_Gross_Salary'))
                                        ->joinLeft(array('SC'=>$this->_ehrTables->employeeSalaryConfiguration), 'S.Employee_Id = SC.Employee_Id', array('SC.Eligible_For_Contribution_Pension_Scheme'))
                                        ->where('S.Employee_Id = ?',$employeeId);
		$rowSalary = $this->_db->fetchRow($qrySalary);		
		return $rowSalary;							 
	}
	
	public function getEmployeesBasicSalary($employeeId)
	{
		if(!empty($employeeId) )
		{
			$employeeIds = explode(',',$employeeId);
			//get employees basic salary
			$basicSalary =  $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->salary, array('Basic_Pay'))
											->where('Employee_Id IN (?)',$employeeIds));
			//two or more employees exist in deductions pass the employee id paramater as 0.
			//get maximum payslip month and calculate the cutoff days. 
			if(count($employeeId) > 1){
				

				$payslipMonth = $this->_dbCommonFun->getLastPayslipMonth(0);
				$lastPayslipMonth = $payslipMonth['previousCutoffDate']; 
							
			}
			else
			{
			    //If single employee id exist in deduction, then find that employee maximum payslip month for calculating cutoff days. 	
				$payslipMonth = $this->_dbCommonFun->getLastPayslipMonth($employeeId);
				$lastPayslipMonth = $payslipMonth['previousCutoffDate']; 

			}
			return array('basicSalary'=>$basicSalary, 'previousCutoffDate' => $lastPayslipMonth);
		}

	}
	
	/** calculate formula based allowance amount **/	
	public function calculateFormulaBasedAllowanceAmount($annualSalary,$basicPay,$allowanceAmt)
	{
		$formulaBasedAllowanceAmount = 0;
		//*Formula Based Allowance Amount = Gross Annual Salary - ((Basic Pay + All the Benefits)*12 )
		$formulaBasedAllowanceAmount = ($annualSalary - ( ( $basicPay + $allowanceAmt ) * 12 ) );
		$formulaBasedAllowanceAmount = ($formulaBasedAllowanceAmount > 0) ? ($formulaBasedAllowanceAmount/12) : 0;	
		return $formulaBasedAllowanceAmount;		
	}

	public function calculateSlabWisePf($socialSecuritySchemeSlabs,$employeePfWage)
    {
		$employeePfWage = $this->_dbCommonFun->getRoundOffValue('EPF',$employeePfWage);
        $providentFundDetails = array();
		$adminEdliCharges     = array();
	    foreach($socialSecuritySchemeSlabs as $socialSecurityScheme)
        {
			if(empty($socialSecurityScheme['Range_To']))
			{
				$socialSecurityScheme['Range_To'] = '9999999999999.99';
			}

            if($socialSecurityScheme['Range_From'] <= $employeePfWage && $socialSecurityScheme['Range_To'] >= $employeePfWage)
            {

               $medianValue            =  $socialSecurityScheme['Median_Value'];
               $wisp                   =  $socialSecurityScheme['WISP'];
               
               $regularSSEEPercentage  =  $socialSecurityScheme['Regular_SS_EE_Percentage'];
               $wispEEPercentage       =  $socialSecurityScheme['WISP_EE_Percentage'];
            
               $regularSSERPercentage  =  $socialSecurityScheme['Regular_SS_ER_Percentage'];
               $wispERPercentage       =  $socialSecurityScheme['WISP_ER_Percentage'];
      
               $regularSSEE          = ($medianValue * $regularSSEEPercentage)/100;
               $wispEmployeeShare      = ($wisp * $wispEEPercentage)/100;
               $ECEE                   = $socialSecurityScheme['EC_EE'];
               $sumofEmployeeShare     = $regularSSEE+$wispEmployeeShare+$ECEE;
               
               $regularSSER          = ($medianValue * $regularSSERPercentage/100);
               $wispEmployerShare      = ($wisp * $wispERPercentage)/100;
               $ECER                   = $socialSecurityScheme['EC_ER'];
               $sumofEmployerShare     = $regularSSER+$wispEmployerShare+$ECER;

               $providentFundDetails = array('Regular_SS_EE'       	=>$this->_dbCommonFun->getRoundOffValue('EPF',$regularSSEE),
                                            'WISP_EE'   			=>$this->_dbCommonFun->getRoundOffValue('EPF',$wispEmployeeShare),
                                            'EC_EE'                 =>$this->_dbCommonFun->getRoundOffValue('EPF',$ECEE),
                                            'Employee_Share_Amount' =>$this->_dbCommonFun->getRoundOffValue('EPF',$sumofEmployeeShare),
                                            'Regular_SS_ER'        	=>$this->_dbCommonFun->getRoundOffValue('EPF',$regularSSER),
                                            'WISP_ER'   			=>$this->_dbCommonFun->getRoundOffValue('EPF',$wispEmployerShare),
                                            'EC_ER'                 =>$this->_dbCommonFun->getRoundOffValue('EPF',$ECER),
                                            'Employer_Share_Amount' =>$this->_dbCommonFun->getRoundOffValue('EPF',$sumofEmployerShare));

			   $providentFundDetails['Employee_Provident_Fund_Wage'] = $employeePfWage;	
			   $adminEdliCharges['Employee_Admin_Charge']            = 0;
			   $adminEdliCharges['Employee_Edli_Charge'] 	         = 0;

			   $providentFundDetails['Admin_Edli_Charges'] = $adminEdliCharges;	
		       return $providentFundDetails;
            }
        }
        return $providentFundDetails;
    }

	public function calculateSlabWiseNps($npsSlabDetails,$employeeNpsWage)
    {
		$employeeNpsWage = $this->_dbCommonFun->getRoundOffValue('EPF',$employeeNpsWage);
        $npsDetails 	 = array();
		
        foreach($npsSlabDetails as $npsSlab)
        {
			if(empty($npsSlab['Range_To']))
			{
				$npsSlab['Range_To'] = '9999999999999.99';
			}

            if($npsSlab['Range_From'] <= $employeeNpsWage && $npsSlab['Range_To'] >= $employeeNpsWage)
            {

               $hdmfSalaryLimit  =  $npsSlab['Hdmf_Salary_Limit'];
               $cappedValue      =  $npsSlab['Capped_Value'];
               $employeeShare    =  $npsSlab['Employee_Share'];
               $employerShare    =  $npsSlab['Employer_Share'];

			   if($hdmfSalaryLimit=='Actual')
			   {
					$employeeShareAmount  = ($employeeNpsWage * $employeeShare)/100;
					$employerShareAmount  = ($employeeNpsWage * $employerShare)/100;
			   }
			   else
			   {
					$employeeShareAmount  = ($cappedValue * $employeeShare)/100;
					$employerShareAmount  = ($cappedValue * $employerShare)/100;
			   }
			
               $npsDetails = array('Employee_Share_Amount' =>$this->_dbCommonFun->getRoundOffValue('EPF',$employeeShareAmount),
                                   'Employer_Share_Amount' =>$this->_dbCommonFun->getRoundOffValue('EPF',$employerShareAmount));

		       return $npsDetails;
            }
        }
        return $npsDetails;
    }

	public function calculateSlabWiseInsurance($insuranceSlabDetails,$employeeInsuranceWage)
	{
        $insuranceDetails 	 = array();
		
        foreach($insuranceSlabDetails as $insuranceSlab)
        {
			if(empty($insuranceSlab['Range_To']))
			{
				$insuranceSlab['Range_To'] = '9999999999999.99';
			}

            if($insuranceSlab['Range_From'] <= $employeeInsuranceWage && $insuranceSlab['Range_To'] >= $employeeInsuranceWage)
            {

               $salaryLimit      =  $insuranceSlab['Salary_Limit'];
               $cappedValue      =  $insuranceSlab['Capped_Value'];
               $employeeShare    =  $insuranceSlab['Employee_Share'];
               $employerShare    =  $insuranceSlab['Employer_Share'];

			   if($salaryLimit=='Actual')
			   {
					$employeeShareAmount  = ($employeeInsuranceWage * $employeeShare)/100;
					$employerShareAmount  = ($employeeInsuranceWage * $employerShare)/100;
			   }
			   else
			   {
					$employeeShareAmount  = ($cappedValue * $employeeShare)/100;
					$employerShareAmount  = ($cappedValue * $employerShare)/100;
			   }
			
               $insuranceDetails = array('Employee_Share_Amount' =>$this->_dbCommonFun->getRoundOffValue('EPF',$employeeShareAmount),
                                   'Employer_Share_Amount' =>$this->_dbCommonFun->getRoundOffValue('EPF',$employerShareAmount));

		       return $insuranceDetails;
            }
        }
    	return $insuranceDetails;
	}

	public function getPayrollGeneralSettings(){
        return $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->payrollGeneralSettings, array('*')));
    }

    public function getSocialSecuritySchemeSlabs(){
        return $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->socialSecuritySchemeSlabs, array('*')));
    }

    public function getNpsSlabs(){
        return $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->npsSlabs, array('*')));
    }

	public function getPhilHealthSlabs(){
        return $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->philHelathSlabs, array('*')));
    }

	public function slabWiseInsuranceDetails(){
        return $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->insuranceConfiguration, array('*'))
																	->where('Slab_Wise_Insurance = ?','Yes')
																	->where('InsuranceType_Status = ?','Active'));
    }
	
	public function getInsuranceRetirlaDetails($employeeId)
	{
		// Input validation to prevent SQL injection and invalid queries
		if (empty($employeeId) || !is_numeric($employeeId)) {
			error_log('getInsuranceRetirlaDetails: Invalid employee ID provided - ' . $employeeId);
			return array();
		}

		try {
			$qryOldSalaryRetirals = $this->_db->select()
	                ->from(array('EOSR' => $this->_ehrTables->employeeOldSalaryRetirals), array('*'))
	                ->joinInner(array('IC' => $this->_ehrTables->insuranceConfiguration),
	                    'IC.InsuranceType_Id = EOSR.Retirals_Id',
	                    array('IC.Insurance_Name', 'IC.Slab_Wise_Insurance', 'IC.Insurance_Type','IC.InsuranceType_Id','IC.Wage_Inclusion','IC.Pro_Rate_Fixed_Insurance','IC.Payment_Frequency'))
	                ->where('EOSR.Employee_Id = ?', $employeeId)
	                ->where('EOSR.Form_Id = ?', 58) // Form_Id 58 = Insurance
	                ->where('IC.InsuranceType_Status = ?', 'Active'); // Only fetch active insurance configurations

	            $oldSalaryRetiralsData = $this->_db->fetchAll($qryOldSalaryRetirals);

	            return empty($oldSalaryRetiralsData) ? array() : $oldSalaryRetiralsData;

		} catch (Exception $e) {
			error_log('Error in getInsuranceRetirlaDetails for employee ' . $employeeId . ': ' . $e->getMessage());
			return array();
		}
	}


	public function calculateInsuranceConfigurationDetails($oldSalaryRetiralsData,$insuranceRetiralWages,$insuranceSlabs)
	{
		$variableInsuranceDetails = array();

		// Input validation to prevent errors
		if (empty($oldSalaryRetiralsData) || !is_array($oldSalaryRetiralsData)) {
			error_log('calculateInsuranceConfigurationDetails: No insurance retiral data provided or invalid data format');
			return $variableInsuranceDetails;
		}

		// Ensure numeric values for calculations
		$insuranceRetiralWages = is_numeric($insuranceRetiralWages) ? floatval($insuranceRetiralWages) : 0;

		foreach ($oldSalaryRetiralsData as $insurance)
		{
			try {
				$employerShareAmount = 0;

				// Use null coalescing operators to prevent undefined array key errors
				$slabWiseInsurance = $insurance['Slab_Wise_Insurance'] ?? 'No';
				$insuranceType = $insurance['Insurance_Type'] ?? '';
				$insuranceName = $insurance['Insurance_Name'] ?? 'Unknown Insurance';
				$paymentFrequency = $insurance['Payment_Frequency'] ?? 'Monthly';

				if ($slabWiseInsurance == 'Yes')
				{
					if (!empty($insuranceSlabs) && is_array($insuranceSlabs)) {
						$insuranceDetails = $this->calculateSlabWiseInsurance($insuranceSlabs, $insuranceRetiralWages);
						$employerShareAmount = isset($insuranceDetails['Employer_Share_Amount']) ?
											 number_format(floatval($insuranceDetails['Employer_Share_Amount']),2,'.','') : 0;
					} else {
						error_log('calculateInsuranceConfigurationDetails: Insurance slabs data missing for slab-wise calculation');
						$employerShareAmount = 0;
					}
				}
				else
				{
					if ($insuranceType == 'Fixed') {
						$employerShareAmount = isset($insurance['Employer_Share_Amount']) ?
											 number_format(floatval($insurance['Employer_Share_Amount']),2,'.','') : 0;
					}
					else if ($insuranceType == 'Variable')
					{
						$employerSharePercentage = isset($insurance['Employer_Share_Percentage']) ?
												 floatval($insurance['Employer_Share_Percentage']) : 0;
						if ($employerSharePercentage > 0 && $insuranceRetiralWages > 0) {
							$employerShareAmount = number_format(($insuranceRetiralWages * $employerSharePercentage) / 100, 2, '.', '');
						}
					}
				}

				// Add insurance details with proper validation
				$variableInsuranceDetails[] = array(
					'Insurance_Name' => $insuranceName,
					'Premium' => $employerShareAmount,
					'Mode' => $paymentFrequency
				);

			} catch (Exception $e) {
				error_log('Error calculating insurance for ' . ($insurance['Insurance_Name'] ?? 'Unknown') . ': ' . $e->getMessage());
				// Continue processing other insurance records
				continue;
			}
		}

		return $variableInsuranceDetails;
	}

	public function __destruct()
    {
        
    }
    
}