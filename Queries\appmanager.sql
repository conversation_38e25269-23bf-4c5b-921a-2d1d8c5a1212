-- -- Revathi - #7598
-- INSERT INTO `modules` (`Module_Id`, `Module_Name`, `Module_Sequence`, `Is_Visible`) VALUES ('24', 'My Team', '6', '1');
-- UPDATE `modules` SET `Module_Sequence` = '7' WHERE `modules`.`Module_Id` = 4;
-- UPDATE `modules` SET `Module_Sequence` = '8' WHERE `modules`.`Module_Id` = 20;
-- UPDATE `modules` SET `Module_Sequence` = '9' WHERE `modules`.`Module_Id` = 15;
-- UPDATE `modules` SET `Module_Sequence` = '10' WHERE `modules`.`Module_Id` = 19;
-- UPDATE `modules` SET `Module_Sequence` = '11' WHERE `modules`.`Module_Id` = 22;
-- UPDATE `modules` SET `Module_Sequence` = '12' WHERE `modules`.`Module_Id` = 5;
-- UPDATE `modules` SET `Module_Sequence` = '13' WHERE `modules`.`Module_Id` = 18;
-- UPDATE `modules` SET `Module_Sequence` = '14' WHERE `modules`.`Module_Id` = 11;
-- UPDATE `modules` SET `Module_Sequence` = '15' WHERE `modules`.`Module_Id` = 21;
-- UPDATE `modules` SET `Module_Sequence` = '16' WHERE `modules`.`Module_Id` = 9;
-- UPDATE `modules` SET `Module_Sequence` = '17' WHERE `modules`.`Module_Id` = 13;
-- UPDATE `modules` SET `Module_Sequence` = '18' WHERE `modules`.`Module_Id` = 12;
-- UPDATE `modules` SET `Module_Sequence` = '19' WHERE `modules`.`Module_Id` = 16;
-- UPDATE `modules` SET `Module_Sequence` = '20' WHERE `modules`.`Module_Id` = 7;
-- UPDATE `modules` SET `Module_Sequence` = '21' WHERE `modules`.`Module_Id` = 14;
-- UPDATE `modules` SET `Module_Sequence` = '22' WHERE `modules`.`Module_Id` = 10;
-- UPDATE `modules` SET `Module_Sequence` = '23' WHERE `modules`.`Module_Id` = 17;
-- UPDATE `modules` SET `Module_Sequence` = '24' WHERE `modules`.`Module_Id` = 8;
-- UPDATE `forms` SET `Form_Name` = 'My Team', `Module_Id` = '24' WHERE `forms`.`Form_Id` = 243

-- 5 Aug 2023 Deployment Queries
-- -- Suganya - #7590
-- UPDATE `forms` SET `Form_Name` = 'Geo-Fencing & Selfie Attendance' WHERE `forms`.`Form_Id` = 227;
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('248', 'Attendance Configuration', '234', '14');
-- INSERT INTO `bank_details` (`Bank_Id`, `Bank_Name`) VALUES (NULL, 'India Post Payments Bank');

-- -- Suhan - #7681
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('249', 'Roster', '234', '14');

-- -- Shyam -#7692
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('250', 'Compensatory Off', '234', '14');

-- -- Shyam
-- UPDATE `forms` SET `Form_Name` = 'Comp Off' WHERE `forms`.`Form_Id` = 250;

-- -- Pasha #7715
-- CREATE TABLE `employee_history_summarization_manager` (
--  `Org_Code` varchar(50) NOT NULL,
--  `Status` enum('InProgress','Open','Success','Failed') NOT NULL,
--  `Error` varchar(500) DEFAULT NULL,
--  `Date` date NOT NULL
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- -- Run only for HRAPP
-- INSERT INTO `employee_history_summarization_manager` (`Org_Code`, `Status`, `Error`, `Date`) VALUES ('channelitix', 'Success', NULL, '2023-08-25')

-- Suganya - #7735
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('251', 'Business Unit / Cost Center', '234', '14');

-- -- 26Sep2023Deployment
-- -- Revathi
-- UPDATE `forms` SET `Sub_Form` = '236', `Module_Id` = '20' WHERE `forms`.`Form_Id` = 251;
-- Shyam - #7695
-- UPDATE `forms` SET `Sub_Form` = '234', `Module_Id` = '14' WHERE `forms`.`Form_Id` = 85;
-- -- 30th Sep Deployment
-- -- Pasha
-- UPDATE `forms` SET `Sub_Form` = '234', `Module_Id` = '14' WHERE `forms`.`Form_Id` = 224;
-- UPDATE `forms` SET `Sub_Form` = '234', `Module_Id` = '14' WHERE `forms`.`Form_Id` = 129;

-- -- Pasha
-- UPDATE `forms` SET `Form_Name` = 'Short Time Off (Permission)' WHERE `forms`.`Form_Id` = 129;
-- UPDATE `forms` SET `Form_Name` = 'Short Time Off (On Duty)' WHERE `forms`.`Form_Id` = 224;

-- -- Suganya #7755
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('252', 'LOP Recovery', '0', '25');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('253', 'LOP Recovery', '0', '24');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('254', 'Auto LOP (lop-recovery)', '253', '24'), ('255', 'Attendance Shortage(lop-recovery)', '253', '24'), ('256', 'Late Attendance(lop-recovery)', '253', '24');

-- -- Revathi #7857
-- UPDATE `forms` SET `Form_Name` = 'Team Summary' WHERE `forms`.`Form_Id` = 243;

-- -- Suganya #7755
-- DELETE FROM `forms` WHERE `forms`.`Form_Id` = 256;
-- DELETE FROM `forms` WHERE `forms`.`Form_Id` = 255;
-- DELETE FROM `forms` WHERE `forms`.`Form_Id` = 254;

-- 11 November 2023 Deployment Queries
-- Suhan #7595
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('256', 'LOP Recovery', '234', '14');

-- Pasha
-- ALTER TABLE `hrapp_registeruser` CHANGE `Partner_Integration` `Partner_Integration` ENUM('trulead','camu','','entomo') NULL DEFAULT '';

-- Revathi #7895
-- INSERT INTO `modules` (`Module_Id`, `Module_Name`, `Module_Sequence`, `Is_Visible`) VALUES ('26', 'Tax and Statutory Compliance', '13', '1');
-- UPDATE `modules` SET `Module_Sequence` = '14' WHERE `modules`.`Module_Id` = 18;
-- UPDATE `modules` SET `Module_Sequence` = '15' WHERE `modules`.`Module_Id` = 11;
-- UPDATE `modules` SET `Module_Sequence` = '16' WHERE `modules`.`Module_Id` = 21;
-- UPDATE `modules` SET `Module_Sequence` = '17' WHERE `modules`.`Module_Id` = 9;
-- UPDATE `modules` SET `Module_Sequence` = '18' WHERE `modules`.`Module_Id` = 13;
-- UPDATE `modules` SET `Module_Sequence` = '19' WHERE `modules`.`Module_Id` = 12;
-- UPDATE `modules` SET `Module_Sequence` = '20' WHERE `modules`.`Module_Id` = 16;
-- UPDATE `modules` SET `Module_Sequence` = '21' WHERE `modules`.`Module_Id` = 7;
-- UPDATE `modules` SET `Module_Sequence` = '22' WHERE `modules`.`Module_Id` = 14;
-- UPDATE `modules` SET `Module_Sequence` = '23' WHERE `modules`.`Module_Id` = 10;
-- UPDATE `modules` SET `Module_Sequence` = '24' WHERE `modules`.`Module_Id` = 17;
-- UPDATE `modules` SET `Module_Sequence` = '25' WHERE `modules`.`Module_Id` = 8;
-- -- tax rules
-- UPDATE `forms` SET `Module_Id` = '26' WHERE `forms`.`Form_Id` = 39;
-- -- tax rules subforms
-- UPDATE `forms` SET `Module_Id` = '26' WHERE `forms`.`Sub_Form` = 39;
-- -- tax declarations
-- UPDATE `forms` SET `Module_Id` = '26' WHERE `forms`.`Form_Id` = 61;
-- -- tax declarations subforms
-- UPDATE `forms` SET `Module_Id` = '26' WHERE `forms`.`Sub_Form` = 61;
-- -- tds history
-- UPDATE `forms` SET `Module_Id` = '26' WHERE `forms`.`Form_Id` = 156;
-- -- Labour Welfare Fund
-- UPDATE `forms` SET `Module_Id` = '26' WHERE `forms`.`Form_Id` = 150;
-- -- Labour Welfare Fund subforms
-- UPDATE `forms` SET `Module_Id` = '26' WHERE `forms`.`Sub_Form` = 150;
-- -- Perquisite Tracker
-- UPDATE `forms` SET `Module_Id` = '26' WHERE `forms`.`Form_Id` = 120;
-- -- pf
-- UPDATE `forms` SET `Module_Id` = '26' WHERE `forms`.`Form_Id` = 52;
-- -- pf subforms
-- UPDATE `forms` SET `Module_Id` = '26' WHERE `forms`.`Sub_Form` = 52;
-- -- insurance
-- UPDATE `forms` SET `Module_Id` = '26' WHERE `forms`.`Form_Id` = 58;
-- -- insurance subforms
-- UPDATE `forms` SET `Module_Id` = '26' WHERE `forms`.`Sub_Form` = 58;
-- -- nps
-- UPDATE `forms` SET `Module_Id` = '26' WHERE `forms`.`Form_Id` = 126;
-- -- nps subforms
-- UPDATE `forms` SET `Module_Id` = '26' WHERE `forms`.`Sub_Form` = 126;
-- -- fixed healthe insurance
-- UPDATE `forms` SET `Module_Id` = '26' WHERE `forms`.`Form_Id` = 152;
-- -- fixed healthe insurance subforms
-- UPDATE `forms` SET `Module_Id` = '26' WHERE `forms`.`Sub_Form` = 152;
-- -- inbox
-- DELETE FROM `forms` WHERE `forms`.`Form_Id` = 36;
-- -- job requisition
-- DELETE FROM `forms` WHERE `forms`.`Form_Id` = 14;
-- -- shortlisted candidate
-- DELETE FROM `forms` WHERE `forms`.`Form_Id` = 17;
-- -- skillset assessment
-- DELETE FROM `forms` WHERE `forms`.`Form_Id` = 86;
-- -- help
-- DELETE FROM `modules` WHERE `modules`.`Module_Id` = 8;

-- -- Pasha
-- DELETE FROM `forms` WHERE `forms`.`Module_Id` = 8;
-- DELETE FROM `hrapp_plan_form` WHERE `hrapp_plan_form`.`Form_Id` = 36;
-- DELETE FROM `hrapp_plan_form` WHERE `hrapp_plan_form`.`Form_Id` = 14;
-- DELETE FROM `hrapp_plan_form` WHERE `hrapp_plan_form`.`Form_Id` = 17;
-- DELETE FROM `hrapp_plan_form` WHERE `hrapp_plan_form`.`Form_Id` = 86;
-- DELETE FROM `hrapp_plan_form` WHERE `hrapp_plan_form`.`Form_Id` = 67;
-- DELETE FROM `hrapp_plan_form` WHERE `hrapp_plan_form`.`Form_Id` = 68;
-- DELETE FROM `hrapp_plan_form` WHERE `hrapp_plan_form`.`Form_Id` = 146;

-- -- Revathi #7895
-- UPDATE `forms` SET `Module_Id` = '26' WHERE `forms`.`Module_Id` = 9;
-- UPDATE `forms` SET `Module_Id` = '13' WHERE `forms`.`Form_Id` = 188;
-- DELETE FROM `modules` WHERE `modules`.`Module_Id` = 9;
-- UPDATE `modules` SET `Module_Sequence` = '17' WHERE `modules`.`Module_Id` = 13;
-- UPDATE `modules` SET `Module_Sequence` = '18' WHERE `modules`.`Module_Id` = 12;
-- UPDATE `modules` SET `Module_Sequence` = '19' WHERE `modules`.`Module_Id` = 16;
-- UPDATE `modules` SET `Module_Sequence` = '20' WHERE `modules`.`Module_Id` = 7;
-- UPDATE `modules` SET `Module_Sequence` = '21' WHERE `modules`.`Module_Id` = 14;
-- UPDATE `modules` SET `Module_Sequence` = '22' WHERE `modules`.`Module_Id` = 10;
-- UPDATE `modules` SET `Module_Sequence` = '23' WHERE `modules`.`Module_Id` = 17;
-- -- Revathi #7904
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('257', 'Pre Approval', '0', '24');

-- -- Revathi #7895
-- -- gratuity
-- UPDATE `forms` SET `Module_Id` = '26' WHERE `forms`.`Form_Id` = 110;
-- -- mail box
-- DELETE FROM `forms` WHERE `forms`.`Form_Id` = 99;
-- -- mail client config
-- DELETE FROM `forms` WHERE `forms`.`Form_Id` = 100;
-- -- tax -> tax-and-statutory-compliance
-- UPDATE `forms` SET `Form_Name` = 'Tax and Statutory Compliance' WHERE `forms`.`Form_Id` = 195;

-- Suganya - LOP recovery queries
-- UPDATE `forms` SET `Module_Id` = '14',`Sub_Form`=234 WHERE `forms`.`Form_Id` = 253;
-- UPDATE `forms` SET `Sub_Form` = '0',`Module_Id`=24 WHERE `forms`.`Form_Id` = 256;

-- -- Suresh - bank_details insertion query
-- INSERT INTO `bank_details` (`Bank_Id`, `Bank_Name`) VALUES (NULL, 'Fino Payments Bank');

-- -- Pasha
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('258', 'Tax Relief Declaration', '61', '26');

-- 30Nov2023 DeploymentQueries
-- abishek
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('259', 'Provident Fund Rules', '52', '26');

-- -- -- suhan
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('260', 'NPS Rules', '126', '26');
-- 30Nov2023 DeploymentQueries

-- 3Dec2023 DeploymentQueries
--  Revathi (ETF to NPS)
-- UPDATE `forms` SET `Form_Name` = 'NPS' WHERE `forms`.`Form_Id` = 126;
-- UPDATE `forms` SET `Form_Name` = 'NPS Payment Tracker' WHERE `forms`.`Form_Id` = 127;

-- ALTER TABLE `customization_fields` CHANGE `New_Field_Name` `New_Field_Name` VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL;

-- DELETE FROM customization_fields where Field_Id IN(1,2,3,13,14,16,17,76,77,78,79) AND Org_Code='demophilippines';

-- DELETE FROM customization_forms where Form_Id IN(259,260,52,126) AND Org_Code='demophilippines';

-- DELETE FROM customization_forms where Form_Id IN(58,59,60,127,72,127,209) AND Org_Code='demophilippines';

-- -- Suganya - Customization fields in salary form
-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`) VALUES (76, 'Eligible For Gratuity', '37'), (77, 'Exempt EDLI', '37');
-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`) VALUES (78, 'Provident Fund', '38');
-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`) VALUES (79, 'ETF', '38');
-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`) VALUES (80, 'Org ETF Amount', '38');

-- INSERT INTO `bank_details` (`Bank_Id`, `Bank_Name`) VALUES (NULL, 'Chaitanya Grameena Godavari Bank');
-- 3Dec2023 DeploymentQueries

-- -- 02Jan2023 Deployment Queries

-- suhan payroll general settings 

-- DELETE FROM fields where Field_Id IN(76,77,78,79,80);

-- DELETE FROM customization_fields where Field_Id IN(76,77,78,79,80);

-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('261', 'payroll', '0', '14');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('262', 'General', '261', '14');

-- ALTER TABLE `customization_fields` ADD `Country_Code` VARCHAR(5) NULL DEFAULT 'IN' AFTER `Required`;
-- ALTER TABLE `tax_rates` ADD `Country_Code` VARCHAR(5) NULL DEFAULT 'IN' AFTER `Assessment_Year`;
-- ALTER TABLE `tax_section` ADD `Country_Code` VARCHAR(5) NULL DEFAULT 'IN' AFTER `Assessment_Year`;
-- ALTER TABLE `tax_rebate` ADD `Country_Code` VARCHAR(5) NULL DEFAULT 'IN' AFTER `Assessment_Year`;
-- ALTER TABLE `tax_exemption` ADD `Country_Code` VARCHAR(5) NULL DEFAULT 'IN' AFTER `Assessment_Year`;
-- ALTER TABLE `tax_entity` ADD `Country_Code` VARCHAR(5) NULL DEFAULT 'IN' AFTER `Education_Cess`;
-- ALTER TABLE `tax_surcharge` ADD `Country_Code` VARCHAR(5) NULL DEFAULT 'IN' AFTER `Surcharge`;
-- ALTER TABLE `investment_age_group` ADD `Country_Code` VARCHAR(5) NULL DEFAULT 'IN' AFTER `Age_Group`;
-- ALTER TABLE `investment_pair_configuration` ADD `Country_Code` VARCHAR(5) NULL DEFAULT 'IN' AFTER `Maximum_Amount`;
-- ALTER TABLE `contractor_tax_rates` ADD `Country_Code` VARCHAR(5) NULL DEFAULT 'IN' AFTER `Resident_Threshold_Limit`;
-- ALTER TABLE `house_property_income_sources` ADD `Country_Code` VARCHAR(5) NULL DEFAULT 'IN' AFTER `Standard_Deduction_Percent`;
-- ALTER TABLE `govt_tax_sections` ADD `Country_Code` VARCHAR(5) NULL DEFAULT 'IN' AFTER `Section_Name`;
-- ALTER TABLE `govt_tax_category` ADD `Country_Code` VARCHAR(5) NULL DEFAULT 'IN' AFTER `Tax_Category_Name`;
-- ALTER TABLE `section_investment_category` ADD `Country_Code` VARCHAR(5) NULL DEFAULT 'IN' AFTER `Assessment_Year`;
-- ALTER TABLE `customization_fields` CHANGE `Customization_Applicable_For` `Customization_Applicable_For` ENUM('All Organization','Specific Organization','Payroll Country') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'All Organization';

-- -- suhan
-- ALTER TABLE `customization_fields` CHANGE `Org_Code` `Org_Code` VARCHAR(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL;
-- INSERT INTO `customization_fields`
--     (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`)
-- VALUES
--     (50, 'Payroll Country', NULL, 'Employee Share', '1', '1', 'IN', NOW(), 1),
--     (51, 'Payroll Country', NULL, 'Employer Share', '1', '1', 'IN', NOW(), 1),
--     (52, 'Payroll Country', NULL, 'Auto Declaration', '1', '1', 'IN', NOW(), 1),
--     (53, 'Payroll Country', NULL, 'Investment Category Id', '1', '1', 'IN', NOW(), 1),
--     (54, 'Payroll Country', NULL, 'NPS Deduction Percentage', '1', '1', 'IN', NOW(), 1),
--     (55, 'Payroll Country', NULL, 'Auto Declaration Applicable For', '1', '1', 'IN', NOW(), 1),
--     (57, 'Payroll Country', NULL, 'Range From', '0', '0', 'IN', NOW(), 1),
--     (58, 'Payroll Country', NULL, 'Range To', '0', '0', 'IN', NOW(), 1),
--     (59, 'Payroll Country', NULL, 'Hdmf Salary Limit', '0', '0', 'IN', NOW(), 1),
--     (60, 'Payroll Country', NULL, 'Capped Value', '0', '0', 'IN', NOW(), 1),
--     (61,'Payroll Country', NULL,'Employee Share','0', '0','IN',NOW(),1),
--     (62,'Payroll Country', NULL,'Employer Share','0', '0','IN',NOW(),1);

-- INSERT INTO `customization_fields`
--     (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`)
-- VALUES
--     (50, 'Payroll Country', NULL, 'Employee Share', '1', '1', 'GH', NOW(), 1),
--     (51, 'Payroll Country', NULL, 'Employer Share', '1', '1', 'GH', NOW(), 1),
--     (52, 'Payroll Country', NULL, 'Auto Declaration', '1', '1', 'GH', NOW(), 1),
--     (53, 'Payroll Country', NULL, 'Investment Category Id', '1', '1', 'GH', NOW(), 1),
--     (54, 'Payroll Country', NULL, 'NPS Deduction Percentage', '0', '0', 'GH', NOW(), 1),
--     (55, 'Payroll Country', NULL, 'Auto Declaration Applicable For', '1', '1', 'GH', NOW(), 1),
--     (57, 'Payroll Country', NULL, 'Range From', '0', '0', 'GH', NOW(), 1),
--     (58, 'Payroll Country', NULL, 'Range To', '0', '0', 'GH', NOW(), 1),
--     (59, 'Payroll Country', NULL, 'Hdmf Salary Limit', '0', '0', 'GH', NOW(), 1),
--     (60, 'Payroll Country', NULL, 'Capped Value', '0', '0', 'GH', NOW(), 1),
--     (61,'Payroll Country', NULL,'Employee Share','0', '0','GH',NOW(),1),
--     (62,'Payroll Country', NULL,'Employer Share','0', '0','GH',NOW(),1);


-- INSERT INTO `customization_fields`
--     (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`)
-- VALUES
--     (50, 'Payroll Country', NULL, 'Employee Share', '1', '1', 'PH', NOW(), 1),
--     (51, 'Payroll Country', NULL, 'Employer Share', '1', '1', 'PH', NOW(), 1),
--     (52, 'Payroll Country', NULL, 'Auto Declaration', '1', '1', 'PH', NOW(), 1),
--     (53, 'Payroll Country', NULL, 'Investment Category Id', '1', '1', 'PH', NOW(), 1),
--     (54, 'Payroll Country', NULL, 'NPS Deduction Percentage', '0', '0', 'PH', NOW(), 1),
--     (55, 'Payroll Country', NULL, 'Auto Declaration Applicable For', '1', '1', 'PH', NOW(), 1),
--     (57, 'Payroll Country', NULL, 'Range From', '1', '1', 'PH', NOW(), 1),
--     (58, 'Payroll Country', NULL, 'Range To', '1', '1', 'PH', NOW(), 1),
--     (59, 'Payroll Country', NULL, 'Hdmf Salary Limit', '1', '1', 'PH', NOW(), 1),
--     (60, 'Payroll Country', NULL, 'Capped Value', '1', '1', 'PH', NOW(), 1),
--     (61,'Payroll Country', NULL,'Employee Share','1', '1','PH',NOW(),1),
--     (62,'Payroll Country', NULL,'Employer Share','1', '1','PH',NOW(),1);

-- -- Employee Share,Employer Share not duplicated required same fields for different forms 126 and 260m slab wise grid

-- -- suhan 18/12/2023
-- DELETE FROM `forms` WHERE `forms`.`Form_Id` = 261;
-- UPDATE `forms` SET `Sub_Form` =  '205',`Form_Id`=261 WHERE `forms`.`Form_Id` = 262;

-- -- Abhishek #8021
-- INSERT INTO `customization_fields`
--     (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`)
-- VALUES
--     (37, 'Payroll Country', NULL, 'EPF Number', '1', '1', 'IN', NOW(), 1),
--     (38, 'Payroll Country', NULL, 'Employee Contribution Rate', '1', '1', 'IN', NOW(), 1),
--     (39, 'Payroll Country', NULL, 'Employer Contribution Rate', '1', '1', 'IN', NOW(), 1),
--     (40, 'Payroll Country', NULL, 'PF Calculated as Percentage of Basic Beyond Statutory Limit', '1', '1', 'IN', NOW(), 1),
--     (41, 'Payroll Country', NULL, 'Employer Contribution Part of CTC', '1', '1', 'IN', NOW(), 1),
--     (42, 'Payroll Country', NULL, 'Admin Charge Part of CTC', '1', '1', 'IN', NOW(), 1),
--     (43, 'Payroll Country', NULL, 'EDLI Charge Part of CTC', '1', '1', 'IN', NOW(), 1),
--     (44, 'Payroll Country', NULL, 'Override PF Contribution Rate at Employee Level', '1', '1', 'IN', NOW(), 1),
--     (45, 'Payroll Country', NULL, 'Pro Rate Restricted PF Wage', '1', '1', 'IN', NOW(), 1),
--     (46,'Payroll Country', NULL,'Consider All Salary Components For LOP','1', '1','IN',NOW(),1),
--     (28, 'Payroll Country', NULL, 'Restricted PF Wage Amount', '1', '1', 'IN', NOW(), 1),
--     (29, 'Payroll Country', NULL, 'Employee Share %', '1', '1', 'IN', NOW(), 1),
--     (30, 'Payroll Country', NULL, 'Employer Share %', '1', '1', 'IN', NOW(), 1),
--     (31, 'Payroll Country', NULL, 'Employees Pension Scheme(Employer Share %)', '1', '1', 'IN', NOW(), 1),
--     (32, 'Payroll Country', NULL, 'Employee Provident Fund(Employer Share %)', '1', '1', 'IN', NOW(), 1),
--     (33, 'Payroll Country', NULL, 'Admin Charge %', '1', '1', 'IN', NOW(), 1),
--     (34,'Payroll Country', NULL,'Admin Charge Max Amount','1', '1','IN',NOW(),1),
--     (35,'Payroll Country', NULL,'EDLI Charge %','1', '1','IN',NOW(),1),
--     (36, 'Payroll Country', NULL, 'EDLI Charge Max Amount', '1', '1', 'IN', NOW(), 1),
--     (47, 'Payroll Country', NULL, 'Auto Declaration', '1', '1', 'IN', NOW(), 1),
--     (48, 'Payroll Country', NULL, 'Investement Category Id', '1', '1', 'IN', NOW(), 1),
--     (49, 'Payroll Country', NULL, 'Auto Declaration Applicable For', '1', '1', 'IN', NOW(), 1),
--     (63,'Payroll Country', NULL,'Insurance Name','1', '1','IN',NOW(),1),
--     (64, 'Payroll Country', NULL, 'Slab Wise Insurance', '0', '0', 'IN', NOW(), 1),
--     (65, 'Payroll Country', NULL, 'Insurance Type', '1', '1', 'IN', NOW(), 1),
--     (66, 'Payroll Country', NULL, 'Employer Share Percentage', '1', '1', 'IN', NOW(), 1),
--     (67, 'Payroll Country', NULL, 'Employee Share Percentage', '1', '1', 'IN', NOW(), 1),
--     (68, 'Payroll Country', NULL, 'Employer Share Amount', '1', '1', 'IN', NOW(), 1),
--     (69, 'Payroll Country', NULL, 'Employee Share Amount', '1', '1', 'IN', NOW(), 1),
--     (70,'Payroll Country', NULL,'Auto Declaration','1', '1','IN',NOW(),1),
--     (71, 'Payroll Country', NULL, 'Auto Declaration Applicable For', '1', '1', 'IN', NOW(), 1),
--     (72,'Payroll Country', NULL,'Section Investment Category Id','1', '1','IN',NOW(),1),
--     (73, 'Payroll Country', NULL, 'Override Contribution At Employee Level', '1', '1', 'IN', NOW(), 1),
--     (74, 'Payroll Country', NULL, 'Payment Frequency', '1', '1', 'IN', NOW(), 1),
--     (75, 'Payroll Country', NULL, 'Employee State Insurance', '1', '1', 'IN', NOW(), 1),
--     (76, 'Payroll Country', NULL, 'Description', '1', '1', 'IN', NOW(), 1),
--     (77, 'Payroll Country', NULL, 'Insurance Status', '1', '1', 'IN', NOW(), 1);
        
-- INSERT INTO `customization_fields`
--     (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`)
-- VALUES
--     (37, 'Payroll Country', NULL, 'EPF Number', '1', '1', 'GH', NOW(), 1),
--     (38, 'Payroll Country', NULL, 'Employee Contribution Rate', '1', '1', 'GH', NOW(), 1),
--     (39, 'Payroll Country', NULL, 'Employer Contribution Rate', '1', '1', 'GH', NOW(), 1),
--     (40, 'Payroll Country', NULL, 'PF Calculated as Percentage of Basic Beyond Statutory Limit', '0', '0', 'GH', NOW(), 1),
--     (41, 'Payroll Country', NULL, 'Employer Contribution Part of CTC', '1', '1', 'GH', NOW(), 1),
--     (42, 'Payroll Country', NULL, 'Admin Charge Part of CTC', '0', '0', 'GH', NOW(), 1),
--     (43, 'Payroll Country', NULL, 'EDLI Charge Part of CTC', '0', '0', 'GH', NOW(), 1),
--     (44, 'Payroll Country', NULL, 'Override PF Contribution Rate at Employee Level', '0', '0', 'GH', NOW(), 1),
--     (45, 'Payroll Country', NULL, 'Pro Rate Restricted PF Wage', '0', '0', 'GH', NOW(), 1),
--     (46,'Payroll Country', NULL,'Consider All Salary Components For LOP','0', '0','GH',NOW(),1),
--     (28, 'Payroll Country', NULL, 'Restricted PF Wage Amount', '0', '0', 'GH', NOW(), 1),
--     (29, 'Payroll Country', NULL, 'Employee Share %', '1', '1', 'GH', NOW(), 1),
--     (30, 'Payroll Country', NULL, 'Employer Share %', '1', '1', 'GH', NOW(), 1),
--     (31, 'Payroll Country', NULL, 'Employees Pension Scheme(Employer Share %)', '0', '0', 'GH', NOW(), 1),
--     (32, 'Payroll Country', NULL, 'Employee Provident Fund(Employer Share %)', '0', '0', 'GH', NOW(), 1),
--     (33, 'Payroll Country', NULL, 'Admin Charge %', '0', '0', 'GH', NOW(), 1),
--     (34,'Payroll Country', NULL,'Admin Charge Max Amount','0', '0','GH',NOW(),1),
--     (35,'Payroll Country', NULL,'EDLI Charge %','0', '0','GH',NOW(),1),
--     (36, 'Payroll Country', NULL, 'EDLI Charge Max Amount', '0', '0', 'GH', NOW(), 1),
--     (47, 'Payroll Country', NULL, 'Auto Declaration', '1', '1', 'GH', NOW(), 1),
--     (48, 'Payroll Country', NULL, 'Investement Category Id', '1', '1', 'GH', NOW(), 1),
--     (49, 'Payroll Country', NULL, 'Auto Declaration Applicable For', '1', '1', 'GH', NOW(), 1),
--     (63,'Payroll Country', NULL,'Insurance Name','1', '1','GH',NOW(),1),
--     (64, 'Payroll Country', NULL, 'Slab Wise Insurance', '0', '0', 'GH', NOW(), 1),
--     (65, 'Payroll Country', NULL, 'Insurance Type', '1', '1', 'GH', NOW(), 1),
--     (66, 'Payroll Country', NULL, 'Employer Share Percentage', '1', '1', 'GH', NOW(), 1),
--     (67, 'Payroll Country', NULL, 'Employee Share Percentage', '1', '1', 'GH', NOW(), 1),
--     (68, 'Payroll Country', NULL, 'Employer Share Amount', '1', '1', 'GH', NOW(), 1),
--     (69, 'Payroll Country', NULL, 'Employee Share Amount', '1', '1', 'GH', NOW(), 1),
--     (70,'Payroll Country', NULL,'Auto Declaration','1', '1','GH',NOW(),1),
--     (71, 'Payroll Country', NULL, 'Auto Declaration Applicable For', '1', '1', 'GH', NOW(), 1),
--     (72,'Payroll Country', NULL,'Section Investment Category Id','1', '1','GH',NOW(),1),
--     (73, 'Payroll Country', NULL, 'Override Contribution At Employee Level', '1', '1', 'GH', NOW(), 1),
--     (74, 'Payroll Country', NULL, 'Payment Frequency', '1', '1', 'GH', NOW(), 1),
--     (75, 'Payroll Country', NULL, 'Employee State Insurance', '0', '0', 'GH', NOW(), 1),
--     (76, 'Payroll Country', NULL, 'Description', '1', '1', 'GH', NOW(), 1),
--     (77, 'Payroll Country', NULL, 'Insurance Status', '1', '1', 'GH', NOW(), 1);
       

-- INSERT INTO `customization_fields`
--     (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`)
-- VALUES
--     (37, 'Payroll Country', NULL, 'SSS Number', '1', '1', 'PH', NOW(), 1),
--     (38, 'Payroll Country', NULL, 'Employee Contribution Rate', '0', '0', 'PH', NOW(), 1),
--     (39, 'Payroll Country', NULL, 'Employer Contribution Rate', '0', '0', 'PH', NOW(), 1),
--     (40, 'Payroll Country', NULL, 'PF Calculated as Percentage of Basic Beyond Statutory Limit', '0', '0', 'PH', NOW(), 1),
--     (41, 'Payroll Country', NULL, 'Employer Contribution Part of CTC', '0', '0', 'PH', NOW(), 1),
--     (42, 'Payroll Country', NULL, 'Admin Charge Part of CTC', '0', '0', 'PH', NOW(), 1),
--     (43, 'Payroll Country', NULL, 'EDLI Charge Part of CTC', '0', '0', 'PH', NOW(), 1),
--     (44, 'Payroll Country', NULL, 'Override PF Contribution Rate at Employee Level', '0', '0', 'PH', NOW(), 1),
--     (45, 'Payroll Country', NULL, 'Pro Rate Restricted PF Wage', '0', '0', 'PH', NOW(), 1),
--     (46,'Payroll Country', NULL,'Consider All Salary Components For LOP','0', '0','PH',NOW(),1),
--     (28, 'Payroll Country', NULL, 'Restricted PF Wage Amount', '0', '0', 'PH', NOW(), 1),
--     (29, 'Payroll Country', NULL, 'Employee Share %', '0', '0', 'PH', NOW(), 1),
--     (30, 'Payroll Country', NULL, 'Employer Share %', '0', '0', 'PH', NOW(), 1),
--     (31, 'Payroll Country', NULL, 'Employees Pension Scheme(Employer Share %)', '0', '0', 'PH', NOW(), 1),
--     (32, 'Payroll Country', NULL, 'Employee Provident Fund(Employer Share %)', '0', '0', 'PH', NOW(), 1),
--     (33, 'Payroll Country', NULL, 'Admin Charge %', '0', '0', 'PH', NOW(), 1),
--     (34,'Payroll Country', NULL,'Admin Charge Max Amount','0', '0','PH',NOW(),1),
--     (35,'Payroll Country', NULL,'EDLI Charge %','0', '0','PH',NOW(),1),
--     (36, 'Payroll Country', NULL, 'EDLI Charge Max Amount', '0', '0', 'PH', NOW(), 1),
--     (47, 'Payroll Country', NULL, 'Auto Declaration', '1', '1', 'PH', NOW(), 1),
--     (48, 'Payroll Country', NULL, 'Investement Category Id', '1', '1', 'PH', NOW(), 1),
--     (49, 'Payroll Country', NULL, 'Auto Declaration Applicable For', '1', '1', 'PH', NOW(), 1),
--     (63,'Payroll Country', NULL,'Insurance Name','1', '1','PH',NOW(),1),
--     (64, 'Payroll Country', NULL, 'Slab Wise Insurance', '1', '1', 'PH', NOW(), 1),
--     (65, 'Payroll Country', NULL, 'Insurance Type', '0', '0', 'PH', NOW(), 1),
--     (66, 'Payroll Country', NULL, 'Employer Share Percentage', '0', '0', 'PH', NOW(), 1),
--     (67, 'Payroll Country', NULL, 'Employee Share Percentage', '0', '0', 'PH', NOW(), 1),
--     (68, 'Payroll Country', NULL, 'Employer Share Amount', '0', '0', 'PH', NOW(), 1),
--     (69, 'Payroll Country', NULL, 'Employee Share Amount', '0', '0', 'PH', NOW(), 1),
--     (70,'Payroll Country', NULL,'Auto Declaration','1', '1','PH',NOW(),1),
--     (71, 'Payroll Country', NULL, 'Auto Declaration Applicable For', '1', '1', 'PH', NOW(), 1),
--     (72,'Payroll Country', NULL,'Section Investment Category Id','1', '1','PH',NOW(),1),
--     (73, 'Payroll Country', NULL, 'Override Contribution At Employee Level', '0', '0', 'PH', NOW(), 1),
--     (74, 'Payroll Country', NULL, 'Payment Frequency', '1', '1', 'PH', NOW(), 1),
--     (75, 'Payroll Country', NULL, 'Employee State Insurance', '0', '0', 'PH', NOW(), 1),
--     (76, 'Payroll Country', NULL, 'Description', '1', '1', 'PH', NOW(), 1),
--     (77, 'Payroll Country', NULL, 'Insurance Status', '1', '1', 'PH', NOW(), 1);


-- -- suhan
--  INSERT INTO `customization_fields`
--     (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`)
-- VALUES
--     (78, 'Payroll Country', NULL, 'Payroll Country', '1', '1', 'IN', NOW(), 1),
--     (79, 'Payroll Country', NULL, 'Slab Wise PF', '0', '0', 'IN', NOW(), 1),
--     (80, 'Payroll Country', NULL, 'Slab Wise NPS', '0', '0', 'IN', NOW(), 1),
--     (81, 'Payroll Country', NULL, 'Include Currency Format Based On Country', '1', '1', 'IN', NOW(), 1);
    

-- INSERT INTO `customization_fields`
--     (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`)
-- VALUES
--     (78, 'Payroll Country', NULL, 'Payroll Country', '1', '1', 'GH', NOW(), 1),
--     (79, 'Payroll Country', NULL, 'Slab Wise PF', '0', '0', 'GH', NOW(), 1),
--     (80, 'Payroll Country', NULL, 'Slab Wise NPS', '0', '0', 'GH', NOW(), 1),
--     (81, 'Payroll Country', NULL, 'Include Currency Format Based On Country', '1', '1', 'GH', NOW(), 1);


-- INSERT INTO `customization_fields`
--     (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`)
-- VALUES
--     (78, 'Payroll Country', NULL, 'Payroll Country', '1', '1', 'PH', NOW(), 1),
--     (79, 'Payroll Country', NULL, 'Slab Wise PF', '1', '1', 'PH', NOW(), 1),
--     (80, 'Payroll Country', NULL, 'Slab Wise NPS', '1', '1', 'PH', NOW(), 1),
--     (81, 'Payroll Country', NULL, 'Include Currency Format Based On Country', '1', '1', 'PH', NOW(), 1);


-- -- SURESH Customization Field Update
-- UPDATE customization_fields SET Country_Code=NULL WHERE Customization_Applicable_For!='Payroll Country';

-- UPDATE customization_fields SET Field_Id=Field_Id+6 where Field_Id IN(76,77,78,79,80) AND Customization_Applicable_For='Specific Organization';

-- TRUNCATE fields;

-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`) VALUES
-- (1, 'Eligible for Insurance', 37),
-- (2, 'Eligible for Pf', 37),
-- (3, 'PF Amount', 37),
-- (4, 'PAN No.', 18),
-- (5, 'PAN No.', 38),
-- (6, 'PAN No.', 105),
-- (7, 'TAN No.', 105),
-- (8, 'Professional Tax Number', 105),
-- (9, 'TDS Payment Frequency', 105),
-- (10, 'TDS Assesment Range', 105),
-- (11, 'Caste', 18),
-- (12, 'Caste', 98),
-- (13, 'Ethnic Race', 18),
-- (14, 'Ethnic Race', 98),
-- (15, 'Aadhaar Number', 18),
-- (16, 'Aadhaar Number', 98),
-- (17, 'Pincode', 18),
-- (18, 'Pincode', 98),
-- (19, 'Eligible for ETF', 37),
-- (20, 'ETF Amount', 37),
-- (21, 'PF Number', 18),
-- (22, 'PF Number', 38),
-- (23, 'Org PF Amount', 38),
-- (24, 'TDS', 38),
-- (25, 'Other Exemptions', 38),
-- (26, 'Organization Insurance Name Prefix', 38),
-- (27, 'IFSC Code', 38),
-- (28, 'Restricted PF Wage Amount', 259),
-- (29, 'Employee Share %', 259),
-- (30, 'Employer Share %', 259),
-- (31, 'Employees Pension Scheme(Employer Share %)', 259),
-- (32, 'Employee Provident Fund(Employer Share %)', 259),
-- (33, 'Admin Charge %', 259),
-- (34, 'Admin Charge Max Amount', 259),
-- (35, 'EDLI Charge %', 259),
-- (36, 'EDLI Charge Max Amount', 259),
-- (37, 'EPF Number', 52),
-- (38, 'Employee Contribution Rate', 52),
-- (39, 'Employer Contribution Rate', 52),
-- (40, 'PF Calculated as Percentage of Basic Beyond Statutory Limit', 52),
-- (41, 'Employer Contribution Part of CTC', 52),
-- (42, 'Admin Charge Part of CTC', 52),
-- (43, 'EDLI Charge Part of CTC', 52),
-- (44, 'Override PF Contribution Rate at Employee Level', 52),
-- (45, 'Pro Rate Restricted PF Wage', 52),
-- (46, 'Consider All Salary Components For LOP', 52),
-- (47, 'Auto Declaration', 259),
-- (48, 'Investement Category Id', 259),
-- (49, 'Auto Declaration Applicable For', 259),
-- (50, 'Employee Share', 126),
-- (51, 'Employer Share', 126),
-- (52, 'Auto Declaration', 260),
-- (53, 'Investment Category', 260),
-- (54, 'NPS Deduction Percentage', 260),
-- (55, 'Auto Declaration Applicable For', 260),
-- (57, 'Range From', 260),
-- (58, 'Range To', 260),
-- (59, 'Hdmf Salary Limit', 260),
-- (60, 'Capped Value', 260),
-- (61, 'Employee Share', 260),
-- (62, 'Employer Share', 260),
-- (63, 'Insurance Name', 59),
-- (64, 'Slab Wise Insurance', 59),
-- (65, 'Insurance Type', 59),
-- (66, 'Employer Share Percentage', 59),
-- (67, 'Employee Share Percentage', 59),
-- (68, 'Employer Share Amount', 59),
-- (69, 'Employee Share Amount', 59),
-- (70, 'Auto Declaration', 59),
-- (71, 'Auto Declaration Applicable For', 59),
-- (72, 'Section Investment Category Id', 59),
-- (73, 'Override Contribution At Employee Level', 59),
-- (74, 'Payment Frequency', 59),
-- (75, 'Employee State Insurance', 59),
-- (76, 'Description', 59),
-- (77, 'Insurance Status', 59),
-- (78, 'Payroll Country', 261),
-- (79, 'Slab Wise PF', 261),
-- (80, 'Slab Wise NPS', 261),
-- (81, 'Include Currency Format Based On Country', 261),
-- (82, 'Eligible For Gratuity', 37),
-- (83, 'Exempt EDLI', 37),
-- (84, 'Provident Fund', 38),
-- (85, 'ETF', 38),
-- (86, 'Org ETF Amount', 38);

-- ALTER TABLE `fields` CHANGE `Field_Name` `Field_Name` VARCHAR(150) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL;

-- UPDATE fields SET Field_Name='PF Calculated as Percentage of Basic Beyond Statutory Limit' where Field_Id=40;
-- -- 02Jan2023 Deployment Queries

-- -- 06 Jan 2024 Deployment Queries
-- suhan timesheet
-- UPDATE `forms` SET `Module_Id` = '24' WHERE `forms`.`Form_Id` = 23;
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('262', 'Timesheets', '0', '24');
-- -- 06 Jan 2024 Deployment Queries

-- 13Jan2024 Deployment Queries
-- Pasha #8043
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('263', 'Pay Configuration', '243', '24');

-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`) VALUES
-- (87, 'Eligible For Overtime', 263),
-- (88, 'Overtime Allocation', 263),
-- (89, 'Overtime Wage Index', 263),
-- (90, 'Overtime Slab', 263),
-- (91, 'Overtime Wage', 263),
-- (92, 'Eligible For PF', 263),
-- (93, 'Eligible For Pension', 263),
-- (94, 'Exempt EDLI', 263),
-- (95, 'UAN', 263),
-- (96, 'PF PolicyNo', 263),
-- (97, 'Eligible For VPF', 263),
-- (98, 'VPF Type', 263),
-- (99, 'VPF Employee Share Percentage', 263),
-- (100, 'VPF Employee Share Amount', 263),
-- (101, 'Eligible For ESI', 263),
-- (102, 'ESI Number', 263),
-- (103, 'ESI Contribution End Date', 263),
-- (104, 'Reason', 263),
-- (105, 'Eligible For Insurance', 263),
-- (106, 'Eligible For NPS', 263),
-- (107, 'NPS Number', 263),
-- (108, 'Eligible For Gratuity', 263),
-- (109, 'Eligible For PT', 263),
-- (110, 'Bond Recovery Applicable', 263),
-- (111, 'Minimum Months To Be Served', 263),
-- (112, 'Bond Value', 263),
-- (113, 'Eligible for contractor TDS', 263),
-- (114, 'Tax Section', 263);

-- INSERT INTO `customization_fields` (`Custom_Id`, `Field_Id`, `Customization_Applicable_For`, `Org_Code`, `Enable`, `Required`, `Country_Code`, `New_Field_Name`, `Added_On`, `Added_By`, `Updated_On`, `Updated_By`) VALUES 
-- (NULL, 87, 'All Organization', NULL, '1', '1', 'IN', 'Eligible For Overtime', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 88, 'All Organization', NULL, '1', '1', 'IN', 'Overtime Allocation', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 89, 'All Organization', NULL, '1', '1', 'IN', 'Overtime Wage Index', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 90, 'All Organization', NULL, '1', '1', 'IN', 'Overtime Slab', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 91, 'All Organization', NULL, '1', '1', 'IN', 'Overtime Wage', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 92, 'All Organization', NULL, '1', '1', 'IN', 'Eligible For PF', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 93, 'All Organization', NULL, '1', '1', 'IN', 'Eligible For Pension', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 94, 'All Organization', NULL, '1', '1', 'IN', 'Exempt EDLI', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 95, 'All Organization', NULL, '1', '1', 'IN', 'UAN', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 96, 'All Organization', NULL, '1', '1', 'IN', 'PF PolicyNo', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 97, 'All Organization', NULL, '1', '1', 'IN', 'Eligible For VPF', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 98, 'All Organization', NULL, '1', '1', 'IN', 'VPF Type', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 99, 'All Organization', NULL, '1', '1', 'IN', 'VPF Employee Share Percentage', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 100, 'All Organization', NULL, '1', '1', 'IN', 'VPF Employee Share Amount', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 101, 'All Organization', NULL, '1', '1', 'IN', 'Eligible For ESI', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 102, 'All Organization', NULL, '1', '1', 'IN', 'ESI Number', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 103, 'All Organization', NULL, '1', '1', 'IN', 'ESI Contribution End Date', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 104, 'All Organization', NULL, '1', '1', 'IN', 'Reason', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 105, 'All Organization', NULL, '1', '1', 'IN', 'Eligible For Insurance', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 106, 'All Organization', NULL, '1', '1', 'IN', 'Eligible For NPS', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 107, 'All Organization', NULL, '1', '1', 'IN', 'NPS Number', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 108, 'All Organization', NULL, '1', '1', 'IN', 'Eligible For Gratuity', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 109, 'All Organization', NULL, '1', '1', 'IN', 'Eligible For PT', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 110, 'All Organization', NULL, '1', '1', 'IN', 'Bond Recovery Applicable', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 111, 'All Organization', NULL, '1', '1', 'IN', 'Minimum Months To Be Served', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 112, 'All Organization', NULL, '1', '1', 'IN', 'Bond Value', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 113, 'All Organization', NULL, '1', '1', 'IN', 'Eligible for contractor TDS', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 114, 'All Organization', NULL, '1', '1', 'IN', 'Tax Section', '2023-12-28 00:00:00', '1', NULL, NULL),

-- (NULL, 87, 'All Organization', NULL, '1', '1', 'GH', 'Eligible For Overtime', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 88, 'All Organization', NULL, '1', '1', 'GH', 'Overtime Allocation', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 89, 'All Organization', NULL, '1', '1', 'GH', 'Overtime Wage Index', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 90, 'All Organization', NULL, '1', '1', 'GH', 'Overtime Slab', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 91, 'All Organization', NULL, '1', '1', 'GH', 'Overtime Wage', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 92, 'All Organization', NULL, '1', '1', 'GH', 'Eligible For PF', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 93, 'All Organization', NULL, '0', '0', 'GH', 'Eligible For Pension', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 94, 'All Organization', NULL, '0', '0', 'GH', 'Exempt EDLI', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 95, 'All Organization', NULL, '1', '1', 'GH', 'UAN', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 96, 'All Organization', NULL, '0', '0', 'GH', 'PF PolicyNo', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 97, 'All Organization', NULL, '0', '0', 'GH', 'Eligible For VPF', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 98, 'All Organization', NULL, '0', '0', 'GH', 'VPF Type', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 99, 'All Organization', NULL, '0', '0', 'GH', 'VPF Employee Share Percentage', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 100, 'All Organization', NULL, '0', '0', 'GH', 'VPF Employee Share Amount', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 101, 'All Organization', NULL, '0', '0', 'GH', 'Eligible For ESI', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 102, 'All Organization', NULL, '0', '0', 'GH', 'ESI Number', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 103, 'All Organization', NULL, '0', '0', 'GH', 'ESI Contribution End Date', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 104, 'All Organization', NULL, '0', '0', 'GH', 'Reason', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 105, 'All Organization', NULL, '1', '1', 'GH', 'Eligible For Insurance', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 106, 'All Organization', NULL, '1', '1', 'GH', 'Eligible For NPS', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 107, 'All Organization', NULL, '1', '1', 'GH', 'NPS Number', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 108, 'All Organization', NULL, '0', '0', 'GH', 'Eligible For Gratuity', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 109, 'All Organization', NULL, '0', '0', 'GH', 'Eligible For PT', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 110, 'All Organization', NULL, '1', '1', 'GH', 'Bond Recovery Applicable', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 111, 'All Organization', NULL, '1', '1', 'GH', 'Minimum Months To Be Served', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 112, 'All Organization', NULL, '1', '1', 'GH', 'Bond Value', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 113, 'All Organization', NULL, '0', '0', 'GH', 'Eligible for contractor TDS', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 114, 'All Organization', NULL, '0', '0', 'GH', 'Tax Section', '2023-12-28 00:00:00', '1', NULL, NULL),

-- (NULL, 87, 'All Organization', NULL, '1', '1', 'PH', 'Eligible For Overtime', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 88, 'All Organization', NULL, '1', '1', 'PH', 'Overtime Allocation', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 89, 'All Organization', NULL, '1', '1', 'PH', 'Overtime Wage Index', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 90, 'All Organization', NULL, '1', '1', 'PH', 'Overtime Slab', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 91, 'All Organization', NULL, '1', '1', 'PH', 'Overtime Wage', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 92, 'All Organization', NULL, '1', '1', 'PH', 'Eligible For Social Security Scheme', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 93, 'All Organization', NULL, '0', '0', 'PH', 'Eligible For Pension', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 94, 'All Organization', NULL, '0', '0', 'PH', 'Exempt EDLI', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 95, 'All Organization', NULL, '1', '1', 'PH', 'Social Security Scheme Number', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 96, 'All Organization', NULL, '0', '0', 'PH', 'PF PolicyNo', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 97, 'All Organization', NULL, '0', '0', 'PH', 'Eligible For VPF', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 98, 'All Organization', NULL, '0', '0', 'PH', 'VPF Type', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 99, 'All Organization', NULL, '0', '0', 'PH', 'VPF Employee Share Percentage', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 100, 'All Organization', NULL, '0', '0', 'PH', 'VPF Employee Share Amount', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 101, 'All Organization', NULL, '0', '0', 'PH', 'Eligible For ESI', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 102, 'All Organization', NULL, '0', '0', 'PH', 'ESI Number', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 103, 'All Organization', NULL, '0', '0', 'PH', 'ESI Contribution End Date', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 104, 'All Organization', NULL, '0', '0', 'PH', 'Reason', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 105, 'All Organization', NULL, '1', '1', 'PH', 'Eligible For Insurance', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 106, 'All Organization', NULL, '1', '1', 'PH', 'Eligible For HDMF', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 107, 'All Organization', NULL, '1', '1', 'PH', 'HDMF Number', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 108, 'All Organization', NULL, '0', '0', 'PH', 'Eligible For Gratuity', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 109, 'All Organization', NULL, '0', '0', 'PH', 'Eligible For PT', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 110, 'All Organization', NULL, '1', '1', 'PH', 'Bond Recovery Applicable', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 111, 'All Organization', NULL, '1', '1', 'PH', 'Minimum Months To Be Served', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 112, 'All Organization', NULL, '1', '1', 'PH', 'Bond Value', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 113, 'All Organization', NULL, '0', '0', 'PH', 'Eligible for contractor TDS', '2023-12-28 00:00:00', '1', NULL, NULL),
-- (NULL, 114, 'All Organization', NULL, '0', '0', 'PH', 'Tax Section', '2023-12-28 00:00:00', '1', NULL, NULL);

-- -- SURESH 
-- INSERT INTO `tax_entity` (`Tax_Entity_Id`, `Tax_Entity_Name`, `Education_Cess`, `Country_Code`, `Entity_Status`, `Description`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`, `Lock_Flag`) VALUES
-- (3, 'Central Government Tax PH', 0, 'PH', 'Active', '', 1, '2019-04-25 13:56:03', NULL, NULL, 0),
-- (4, 'Ghana Government Tax', 0, 'GH', 'Active', '', 1, '2019-04-25 13:56:03', NULL, NULL, 0);

-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Country_Code`,`Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'4','0','250000','0','0','5','PH',2024,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Country_Code`,`Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'4','250001','400000','20','20','5','PH',2024,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Country_Code`,`Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'4','400001','800000','25','25','5','PH',2024,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Country_Code`,`Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'4','800001','2000000','30','30','5','PH',2024,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Country_Code`,`Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'4','2000001','8000000','32','32','5','PH',2024,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Country_Code`,`Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'4','8000001','NULL','35','35','5','PH',2024,'Active',0,1,Now(),NULL,NULL);

-- update tax_section set Tax_Status='InActive' where Country_Code='PH ';
-- update section_investment_category set Tax_Status='InActive' where Country_Code='PH ';
-- update tax_exemption set Tax_Status='InActive' where Country_Code='PH ';
-- update tax_rebate set Tax_Status='InActive' where Country_Code='PH ';

-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Country_Code`,`Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'3','0','4824','0','0','5','GH',2024,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Country_Code`,`Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'3','4825','6144','5','5','5','GH',2024,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Country_Code`,`Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'3','6145','7704','10','10','5','GH',2024,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Country_Code`,`Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'3','7705','43704','17.5','17.5','5','GH',2024,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Country_Code`,`Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'3','43705','240444','25','25','5','GH',2024,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Country_Code`,`Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'3','240445','600000','30','30','5','GH',2024,'Active',0,1,Now(),NULL,NULL);

-- update tax_section set Tax_Status='InActive' where Country_Code='GH';
-- update section_investment_category set Tax_Status='InActive' where Country_Code='GH';
-- update tax_exemption set Tax_Status='InActive' where Country_Code='GH';
-- update tax_rebate set Tax_Status='InActive' where Country_Code='GH';

-- -- Pasha #8043
-- UPDATE customization_fields SET Customization_Applicable_For = 'Payroll Country' WHERE Field_Id BETWEEN 87 AND 114;

-- update tax_rates set Tax_Entity_Id=4 where Country_Code='GH';

-- update tax_rates set Tax_Entity_Id=3 where Country_Code='PH';

-- -- suganya - #8098
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('264', 'Compensatory Off Balance', '139', '24');
-- UPDATE `forms` SET `Sub_Form` = '0', `Module_Id` = '25' WHERE `forms`.`Form_Id` = 140;
-- UPDATE `forms` SET `Sub_Form` = '0' WHERE `forms`.`Form_Id` = 264;

                                                    -- 02FEB2024 Deployment Queries
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('265', 'Internet access control ', '230', '14');

-- SURESH -#8368 09Feb2024 Deployment
-- revert the timesheet to old module till the vue timesheet module has been enabled timesheet will maintain under employee module
-- need to run this only in staging
-- UPDATE `forms` SET `Module_Id` = 4 WHERE `forms`.`Form_Id` = 23;

-- 10Feb2024 Deployment Queries
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('262', 'Timesheets', '0', '24');
-- 10Feb2024 Deployment Queries

                                            -- 17Feb2024 Deployment Queries
-- emp details remove added on feb-16 #8503 #17Feb2024 
-- DELETE FROM `forms` WHERE `forms`.`Form_Id` = 202;
-- DELETE FROM `hrapp_plan_form` WHERE `hrapp_plan_form`.`Form_Id` = 202;
                                            -- 17Feb2024 Deployment Queries

                                            -- 24Feb2024 Deployment Queries
-- Abhishek #8461
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('266', 'CXO Dashboard', '211', '15');
-- -- timesheet module updated wrongly feb 22
-- UPDATE `forms` SET `Module_Id` = '25' WHERE `forms`.`Form_Id` = 262;

                                            -- 24Feb2024 Deployment Queries

                                            -- 03Mar2024 Deployment Queries
-- Revathi - timesheet activities remove - added on feb-28 #8512 - issue 18 
-- DELETE FROM `forms` WHERE `forms`.`Form_Id` = 25;
-- DELETE FROM `hrapp_plan_form` WHERE `hrapp_plan_form`.`Form_Id` = 25;
                                            -- 03Mar2024 Deployment Queries
-- suganya #8597
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('267', 'Reimbursement', '205', '14');

                                        -- 23March2024 Deployment Queries
-- INSERT INTO `bank_details` (`Bank_Id`, `Bank_Name`) VALUES
-- (NULL, 'Saptagiri Grameena Bank'),
-- (NULL, 'Airtel Payment Bank');
                                        -- 23March2024 Deployment Queries
                                        -- 29March2024 Deployment Queries
-- timesheet form migrating to new my team module
-- UPDATE `forms` SET `Module_Id` = '24' WHERE `forms`.`Form_Id` = 23;

-- -- #8618 added on mar 23 timesheet workflow
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('268', 'Project Settings', '234', '14');

-- -- #8074 added on mar 25 timesheet hours
-- UPDATE `forms` SET `Sub_Form` = '0' WHERE `forms`.`Form_Id` = 24;
--                                     -- 29March2024 Deployment Queries


                            -- -- 02April2024 Deployment Queries

-- UPDATE `tax_section` SET `Assessment_Year` = '2025' WHERE 1;

-- UPDATE `section_investment_category` SET `Assessment_Year` = '2025' WHERE 1;

-- UPDATE `tax_exemption` SET `Assessment_Year` = '2025' WHERE 1;

-- UPDATE `tax_rebate` SET `Assessment_Year` = '2025' WHERE 1;

-- UPDATE `house_property_income_sources` SET `Assessment_Year` = '2025' WHERE 1;

-- UPDATE `contractor_tax_rates` SET `Assessment_Year` = '2025' WHERE 1;

-- UPDATE `tax_rates` SET `Assessment_Year` = '2025' WHERE 1;

-- ALTER TABLE `hrapp_registeruser` CHANGE `Org_Description` `Org_Description` VARCHAR(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT 'Org description will be used in multi instance landing page';
                                    -- -- 02April2024 Deployment Queries

                                    -- 05April2024 Deployment Queries
-- DELETE FROM customization_forms where Form_Id IN(82,83,84);
-- INSERT INTO `customization_forms` (`Custom_Id`, `Form_Id`, `Customization_Applicable_For`, `Org_Code`, `Enable`, `New_Form_Name`, `Added_On`, `Added_By`, `Updated_On`, `Updated_By`) VALUES
-- (NULL, 82, 'All Organization', '', 1, 'Key Result Area',NOW(), 1, NULL, NULL),
-- (NULL, 83, 'All Organization', '', 1, 'Key Result Area Mapping',NOW(), 1, NULL, NULL),
-- (NULL, 84, 'All Organization', '', 1, 'KPA & KRA', NOW(), 1, NULL, NULL);
                                    -- 05April2024 Deployment Queries

                                    -- 18April2024 Deployment Queries
                                    
-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`) VALUES ('115', 'Service Provider', '0');
-- INSERT INTO `customization_fields` (`Custom_Id`, `Field_Id`, `Customization_Applicable_For`, `Org_Code`, `Enable`, `Required`, `Country_Code`, `New_Field_Name`, `Added_On`, `Added_By`, `Updated_On`, `Updated_By`) VALUES (NULL, '115', 'All Organization', NULL, '0', '1', NULL, 'Organization Unit', '2024-04-17 00:00:00', '1', NULL, NULL);

-- suhan organization group 29th april #8785
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('269', 'Organization Group', '236', '20');

-- #8801 suhan customForm Feilds for self onboarding
-- ALTER TABLE `fields` ADD `Tab_Name` VARCHAR(50) NULL AFTER `Form_Id`;
-- INSERT INTO `fields` (`Field_Name`, `Form_Id`,`Tab_Name`) VALUES
--        ('Driving License Number', 178,'Driving License'),
--     ('Issue Date', 178,'Driving License'),
--     ('Expiry Date', 178,'Driving License'),
--     ('Issuing Authority', 178,'Driving License'),
--     ('Issuing Country', 178,'Driving License'),
--     ('Vehicle Type', 178,'Driving License'),
--     ('Issuing State', 178,'Driving License'),
--     ('Passport Number', 178,'Passport'),
--     ('Issue Date', 178,'Passport'),
--     ('Issuing Authority', 178,'Passport'),
--     ('Issuing Country', 178,'Passport'),
--     ('Expiry Date', 178,'Passport'),
--     ('School diploma or degree', 178,'Education'),
--     ('Specialization', 178,'Education'),
--     ('Institute Name', 178,'Education'),
--     ('University', 178,'Education'),
--     ('Percentage', 178,'Education'),
--     ('Grade', 178,'Education'),
--     ('Year of Passing', 178,'Education');
-- INSERT INTO `customization_fields`
--     (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`)
-- VALUES
--     (116, 'Payroll Country', NULL, 'Driving License Number', 1, 1, 'PH', NOW(), 1),
--     (117, 'Payroll Country', NULL, 'Issue Date', 1, 1, 'PH', NOW(), 1),
--     (118, 'Payroll Country', NULL, 'Expiry Date', 1, 1, 'PH', NOW(), 1),
--     (119, 'Payroll Country', NULL, 'Issuing Authority', 1, 1, 'PH', NOW(), 1),
--     (120, 'Payroll Country', NULL, 'Issuing Country', 1, 1, 'PH', NOW(), 1),
--     (121, 'Payroll Country', NULL, 'Vehicle Type', 1, 1, 'PH', NOW(), 1),
--     (122, 'Payroll Country', NULL, 'Issuing State', 1, 1, 'PH', NOW(), 1),
--     (123, 'Payroll Country', NULL, 'Passport Number', 1, 1, 'PH', NOW(), 1),
--     (124, 'Payroll Country', NULL, 'Issue Date', 1, 1, 'PH', NOW(), 1),
--     (125, 'Payroll Country', NULL, 'Issuing Authority', 1, 1, 'PH', NOW(), 1),
--     (126, 'Payroll Country', NULL, 'Issuing Country', 1, 1, 'PH', NOW(), 1),
--     (127, 'Payroll Country', NULL, 'Expiry Date', 1, 1, 'PH', NOW(), 1),
--     (128, 'Payroll Country', NULL, 'School diploma or degree', 1, 1, 'PH', NOW(), 1),
--     (129, 'Payroll Country', NULL, 'Specialization', 1, 1, 'PH', NOW(), 1),
--     (130, 'Payroll Country', NULL, 'Institute Name', 1, 1, 'PH', NOW(), 1),
--     (131, 'Payroll Country', NULL, 'University', 1, 1, 'PH', NOW(), 1),
--     (132, 'Payroll Country', NULL, 'Percentage', 1, 1, 'PH', NOW(), 1),
--     (133, 'Payroll Country', NULL, 'Grade', 1, 1, 'PH', NOW(), 1),
--     (134, 'Payroll Country', NULL, 'Year of Passing', 1, 1, 'PH', NOW(), 1);


--     INSERT INTO `customization_fields`
--     (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`)
-- VALUES
--     (116, 'Payroll Country', NULL, 'Driving License Number', 0, 0, 'GH', NOW(), 1),
--     (117, 'Payroll Country', NULL, 'Issue Date', 0, 0, 'GH', NOW(), 1),
--     (118, 'Payroll Country', NULL, 'Expiry Date', 0, 0, 'GH', NOW(), 1),
--     (119, 'Payroll Country', NULL, 'Issuing Authority', 0, 0, 'GH', NOW(), 1),
--     (120, 'Payroll Country', NULL, 'Issuing Country', 0, 0, 'GH', NOW(), 1),
--     (121, 'Payroll Country', NULL, 'Vehicle Type', 0, 0, 'GH', NOW(), 1),
--     (122, 'Payroll Country', NULL, 'Issuing State', 0, 0, 'GH', NOW(), 1),
--     (123, 'Payroll Country', NULL, 'Passport Number', 0, 0, 'GH', NOW(), 1),
--     (124, 'Payroll Country', NULL, 'Issue Date', 0, 0, 'GH', NOW(), 1),
--     (125, 'Payroll Country', NULL, 'Issuing Authority', 0, 0, 'GH', NOW(), 1),
--     (126, 'Payroll Country', NULL, 'Issuing Country', 0, 0, 'GH', NOW(), 1),
--     (127, 'Payroll Country', NULL, 'Expiry Date', 0, 0, 'GH', NOW(), 1),
--     (128, 'Payroll Country', NULL, 'School diploma or degree', 0, 0, 'GH', NOW(), 1),
--     (129, 'Payroll Country', NULL, 'Specialization', 0, 0, 'GH', NOW(), 1),
--     (130, 'Payroll Country', NULL, 'Institute Name', 0, 0, 'GH', NOW(), 1),
--     (131, 'Payroll Country', NULL, 'University', 0, 0, 'GH', NOW(), 1),
--     (132, 'Payroll Country', NULL, 'Percentage', 0, 0, 'GH', NOW(), 1),
--     (133, 'Payroll Country', NULL, 'Grade', 0, 0, 'GH', NOW(), 1),
--     (134, 'Payroll Country', NULL, 'Year of Passing', 0, 0, 'GH', NOW(), 1);

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`)
-- VALUES
-- (116, 'Payroll Country', NULL, 'Driving License Number', 0, 0, 'IN', NOW(), 1),
-- (117, 'Payroll Country', NULL, 'Issue Date', 0, 0, 'IN', NOW(), 1),
-- (118, 'Payroll Country', NULL, 'Expiry Date', 0, 0, 'IN', NOW(), 1),
-- (119, 'Payroll Country', NULL, 'Issuing Authority', 0, 0, 'IN', NOW(), 1),
-- (120, 'Payroll Country', NULL, 'Issuing Country', 0, 0, 'IN', NOW(), 1),
-- (121, 'Payroll Country', NULL, 'Vehicle Type', 0, 0, 'IN', NOW(), 1),
-- (122, 'Payroll Country', NULL, 'Issuing State', 0, 0, 'IN', NOW(), 1),
-- (123, 'Payroll Country', NULL, 'Passport Number', 0, 0, 'IN', NOW(), 1),
-- (124, 'Payroll Country', NULL, 'Issue Date', 0, 0, 'IN', NOW(), 1),
-- (125, 'Payroll Country', NULL, 'Issuing Authority', 0, 0, 'IN', NOW(), 1),
-- (126, 'Payroll Country', NULL, 'Issuing Country', 0, 0, 'IN', NOW(), 1),
-- (127, 'Payroll Country', NULL, 'Expiry Date', 0, 0, 'IN', NOW(), 1),
-- (128, 'Payroll Country', NULL, 'School diploma or degree', 0, 0, 'IN', NOW(), 1),
-- (129, 'Payroll Country', NULL, 'Specialization', 0, 0, 'IN', NOW(), 1),
-- (130, 'Payroll Country', NULL, 'Institute Name', 0, 0, 'IN', NOW(), 1),
-- (131, 'Payroll Country', NULL, 'University', 0, 0, 'IN', NOW(), 1),
-- (132, 'Payroll Country', NULL, 'Percentage', 0, 0, 'IN', NOW(), 1),
-- (133, 'Payroll Country', NULL, 'Grade', 0, 0, 'IN', NOW(), 1),
-- (134, 'Payroll Country', NULL, 'Year of Passing', 0, 0, 'IN', NOW(), 1);

-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('270', 'Paid Leave Deduction', '31', '4');
-- ALTER TABLE `customization_fields` ADD `Predefined` ENUM('Yes','No') NULL DEFAULT 'No' AFTER `Country_Code`;

-- 20May2024 Deployment Queries

-- INSERT INTO fields (Field_Name, Form_Id,Tab_Name)
-- VALUES 
--     ('IFSC Code',178,'Bank'),
--     ('IFSC Code',243,'Bank'),
--     ('City',178,'Address'),
--     ('City',178,'oAddress'),
--     ('City',178,'Bank'),
--     ('City',243,'Address'),
--     ('City',243,'oAddress'),
--     ('City',243,'Bank'),
--     ('City',16,'Profile'),
--     ('Pincode',178,'Address'),
--     ('Pincode',178,'oAddress'),
--     ('Pincode',178,'Bank'),
--     ('Pincode',243,'Address'),
--     ('Pincode',243,'oAddress'),
--     ('Pincode',243,'Bank'),
--     ('Pincode',16,'Profile'),
--     ('Organization Group', 269,null),
--     ('Probation Date', 243,'Job'),
--     ('Probation Date', 178,'Job'),
--     ('Organization Group', 15,null),
--     ('Job Title', 15,null)
--    ;
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`,`Added_On`, `Added_By`)
-- VALUES
-- (135,'Payroll Country', NULL,'Bank Branch Code',1,0,'PH','No',NOW(),1),
-- (136,'Payroll Country', NULL,'Bank Branch Code',1,0,'PH','No',NOW(),1),
-- (137,'Payroll Country', NULL,'City',1,0,'PH','Yes',NOW(),1),
-- (138,'Payroll Country', NULL,'City',1,0,'PH','Yes',NOW(),1),
-- (139,'Payroll Country', NULL,'City',1,0,'PH','Yes',NOW(),1),
-- (140,'Payroll Country', NULL,'City',1,0,'PH','Yes',NOW(),1),
-- (141,'Payroll Country', NULL,'City',1,0,'PH','Yes',NOW(),1),
-- (142,'Payroll Country', NULL,'City',1,0,'PH','Yes',NOW(),1),
-- (143,'Payroll Country', NULL,'City',1,0,'PH','Yes',NOW(),1),
-- (144,'Payroll Country', NULL,'Postal code',1,0,'PH','No',NOW(),1),
-- (145,'Payroll Country', NULL,'Postal code',1,0,'PH','No',NOW(),1),
-- (146,'Payroll Country', NULL,'Postal code',1,0,'PH','No',NOW(),1),
-- (147,'Payroll Country', NULL,'Postal code',1,0,'PH','No',NOW(),1),
-- (148,'Payroll Country', NULL,'Postal code',1,0,'PH','No',NOW(),1),
-- (149,'Payroll Country', NULL,'Postal code',1,0,'PH','No',NOW(),1),
-- (150,'Payroll Country', NULL,'Postal code',1,0,'PH','No',NOW(),1),
-- (151,'Payroll Country', NULL,'Organization Group',1,0,'PH','No',NOW(),1),
-- (152,'Payroll Country', NULL,'Probation Date',1,0,'PH','No',NOW(),1),
-- (153,'Payroll Country', NULL,'Probation Date',1,0,'PH','No',NOW(),1),
-- (154,'Payroll Country', NULL,'Organization Group',1,0,'PH','No',NOW(),1),
-- (155,'Payroll Country', NULL,'Job Title',1,1,'PH','Yes',NOW(),1);

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`,`Added_On`, `Added_By`)
-- VALUES
-- (135,'Payroll Country', NULL,'IFSC Code',1,1,'IN','No',NOW(),1),
-- (136,'Payroll Country', NULL,'IFSC Code',1,1,'IN','No',NOW(),1),
-- (137,'Payroll Country', NULL,'City',1,1,'IN','No',NOW(),1),
-- (138,'Payroll Country', NULL,'City',1,1,'IN','No',NOW(),1),
-- (139,'Payroll Country', NULL,'City',1,1,'IN','No',NOW(),1),
-- (140,'Payroll Country', NULL,'City',1,1,'IN','No',NOW(),1),
-- (141,'Payroll Country', NULL,'City',1,1,'IN','No',NOW(),1),
-- (142,'Payroll Country', NULL,'City',1,1,'IN','No',NOW(),1),
-- (143,'Payroll Country', NULL,'City',1,1,'IN','No',NOW(),1),
-- (144,'Payroll Country', NULL,'Pincode',1,1,'IN','No',NOW(),1),
-- (145,'Payroll Country', NULL,'Pincode',1,1,'IN','No',NOW(),1),
-- (146,'Payroll Country', NULL,'Pincode',1,1,'IN','No',NOW(),1),
-- (147,'Payroll Country', NULL,'Pincode',1,1,'IN','No',NOW(),1),
-- (148,'Payroll Country', NULL,'Pincode',1,1,'IN','No',NOW(),1),
-- (149,'Payroll Country', NULL,'Pincode',1,1,'IN','No',NOW(),1),
-- (150,'Payroll Country', NULL,'Pincode',1,1,'IN','No',NOW(),1),
-- (151,'Payroll Country', NULL,'Organization Group',1,0,'IN','No',NOW(),1),
-- (152,'Payroll Country', NULL,'Probation Date',1,1,'IN','No',NOW(),1),
-- (153,'Payroll Country', NULL,'Probation Date',1,1,'IN','No',NOW(),1),
-- (154,'Payroll Country', NULL,'Organization Group',1,0,'IN','No',NOW(),1),
-- (155,'Payroll Country', NULL,'Job Title',1,1,'IN','No',NOW(),1);


-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`,`Added_On`, `Added_By`)
-- VALUES 
-- (135,'Payroll Country', NULL,'IFSC Code',1,1,'GH','No',NOW(),1),
-- (136,'Payroll Country', NULL,'IFSC Code',1,1,'GH','No',NOW(),1),
-- (137,'Payroll Country', NULL,'City',1,1,'GH','No',NOW(),1),
-- (138,'Payroll Country', NULL,'City',1,1,'GH','No',NOW(),1),
-- (139,'Payroll Country', NULL,'City',1,1,'GH','No',NOW(),1),
-- (140,'Payroll Country', NULL,'City',1,1,'GH','No',NOW(),1),
-- (141,'Payroll Country', NULL,'City',1,1,'GH','No',NOW(),1),
-- (142,'Payroll Country', NULL,'City',1,1,'GH','No',NOW(),1),
-- (143,'Payroll Country', NULL,'City',1,1,'GH','No',NOW(),1),
-- (144,'Payroll Country', NULL,'Pincode',1,1,'GH','No',NOW(),1),
-- (145,'Payroll Country', NULL,'Pincode',1,1,'GH','No',NOW(),1),
-- (146,'Payroll Country', NULL,'Pincode',1,1,'GH','No',NOW(),1),
-- (147,'Payroll Country', NULL,'Pincode',1,1,'GH','No',NOW(),1),
-- (148,'Payroll Country', NULL,'Pincode',1,1,'GH','No',NOW(),1),
-- (149,'Payroll Country', NULL,'Pincode',1,1,'GH','No',NOW(),1),
-- (150,'Payroll Country', NULL,'Pincode',1,1,'GH','No',NOW(),1),
-- (151,'Payroll Country', NULL,'Organization Group',1,0,'GH','No',NOW(),1),
-- (152,'Payroll Country', NULL,'Probation Date',1,1,'GH','No',NOW(),1),
-- (153,'Payroll Country', NULL,'Probation Date',1,1,'GH','No',NOW(),1),
-- (154,'Payroll Country', NULL,'Organization Group',1,0,'GH','No',NOW(),1),
-- (155,'Payroll Country', NULL,'Job Title',1,1,'GH','No',NOW(),1);

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`,`Predefined`,`Added_On`, `Added_By`)
-- VALUES
-- (135,'Payroll Country', NULL,'BSB Code',0,0,'AU','No',NOW(),1),
-- (136,'Payroll Country', NULL,'BSB Code',0,0,'AU','No',NOW(),1),
-- (137,'Payroll Country', NULL,'City',1,1,'AU','No',NOW(),1),
-- (138,'Payroll Country', NULL,'City',1,1,'AU','No',NOW(),1),
-- (139,'Payroll Country', NULL,'City',1,1,'AU','No',NOW(),1),
-- (140,'Payroll Country', NULL,'City',1,1,'AU','No',NOW(),1),
-- (141,'Payroll Country', NULL,'City',1,1,'AU','No',NOW(),1),
-- (142,'Payroll Country', NULL,'City',1,1,'AU','No',NOW(),1),
-- (143,'Payroll Country', NULL,'City',1,1,'AU','No',NOW(),1),
-- (144,'Payroll Country', NULL,'Pincode',1,1,'AU','No',NOW(),1),
-- (145,'Payroll Country', NULL,'Pincode',1,1,'AU','No',NOW(),1),
-- (146,'Payroll Country', NULL,'Pincode',1,1,'AU','No',NOW(),1),
-- (147,'Payroll Country', NULL,'Pincode',1,1,'AU','No',NOW(),1),
-- (148,'Payroll Country', NULL,'Pincode',1,1,'AU','No',NOW(),1),
-- (149,'Payroll Country', NULL,'Pincode',1,1,'AU','No',NOW(),1),
-- (150,'Payroll Country', NULL,'Pincode',1,1,'AU','No',NOW(),1),
-- (151,'Payroll Country', NULL,'Organization Group',1,0,'AU','No',NOW(),1),
-- (152,'Payroll Country', NULL,'Probation Date',1,1,'AU','No',NOW(),1),
-- (153,'Payroll Country', NULL,'Probation Date',1,1,'AU','No',NOW(),1),
-- (154,'Payroll Country', NULL,'Organization Group',1,0,'AU','No',NOW(),1),
-- (155,'Payroll Country', NULL,'Job Title',1,1,'AU','No',NOW(),1);

-- -- #adding custom feilds for education may 16th suhan
--  INSERT INTO fields (Field_Name, Form_Id,Tab_Name)
-- VALUES 
--     ('Start Date' ,178,'Education'),
--     ('End Date',178,'Education'),
--     ('City',178,'Education'),
--     ('State',178,'Education'), 
--     ('Country',178,'Education'),
--     ('School diploma or degree', 243,'Education'),
--     ('Specialization', 243,'Education'),
--     ('Institute Name', 243,'Education'),
--     ('University', 243,'Education'),
--     ('Percentage', 243,'Education'),
--     ('Grade', 243,'Education'),
--     ('Year of Passing', 243,'Education'),
--     ('Start Date' ,243,'Education'),
--     ('End Date',243,'Education'),
--     ('City',243,'Education'),
--     ('State',243,'Education'), 
--     ('Country',243,'Education'),   
--     ('School diploma or degree', 16,'Education'),
--     ('Specialization', 16,'Education'),
--     ('Institute Name', 16,'Education'),
--     ('University', 16,'Education'),
--     ('Percentage', 16,'Education'),
--     ('Grade', 16,'Education'),
--     ('Year of Passing', 16,'Education'),
--     ('Start Date' ,16,'Education'),
--     ('End Date',16,'Education'),
--     ('City',16,'Education'),
--     ('State',16,'Education'), 
--     ('Country',16,'Education'),
--     ('Branch Email Address',178,Null),
--     ('Timekeeping PIC',178,Null),
--     ('Career PIC',178,Null),
--     ('Tax Code',178,Null);


-- INSERT INTO customization_fields
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES 
--     (156,'Payroll Country',NULL,'Start Date',1,0,'PH','No',NOW(),1),
--     (157,'Payroll Country',NULL,'End Date',1,0,'PH','No',NOW(),1),
--     (158,'Payroll Country',NULL,'City',1,0,'PH','Yes',NOW(),1),
--     (159,'Payroll Country',NULL,'State',1,0,'PH','No',NOW(),1),
--     (160,'Payroll Country',NULL,'Country',1,0,'PH','No',NOW(),1),
--     (161,'Payroll Country',NULL,'School diploma or degree',1,0,'PH','No',NOW(),1),
--     (162,'Payroll Country',NULL,'Specialization',1,1,'PH','Yes',NOW(),1),
--     (163,'Payroll Country',NULL,'Institute Name',1,0,'PH','Yes',NOW(),1),
--     (164,'Payroll Country',NULL,'University',0,0,'PH','No',NOW(),1),
--     (165,'Payroll Country',NULL,'Percentage',0,0,'PH','No',NOW(),1),
--     (166,'Payroll Country',NULL,'Grade',0,0,'PH','No',NOW(),1),
--     (167,'Payroll Country',NULL,'Year of Passing',0,0,'PH','No',NOW(),1),
--     (168,'Payroll Country',NULL,'Start Date',1,0,'PH','No',NOW(),1),
--     (169,'Payroll Country',NULL,'End Date',1,0,'PH','No',NOW(),1),
--     (170,'Payroll Country',NULL,'City',1,0,'PH','Yes',NOW(),1),
--     (171,'Payroll Country',NULL,'State',1,0,'PH','No',NOW(),1),
--     (172,'Payroll Country',NULL,'Country',1,0,'PH','No',NOW(),1),
--     (173,'Payroll Country',NULL,'School diploma or degree',1,0,'PH','No',NOW(),1),
--     (174,'Payroll Country',NULL,'Specialization',1,1,'PH','Yes',NOW(),1),
--     (175,'Payroll Country',NULL,'Institute Name',1,0,'PH','Yes',NOW(),1),
--     (176,'Payroll Country',NULL,'University',0,0,'PH','No',NOW(),1),
--     (177,'Payroll Country',NULL,'Percentage',0,0,'PH','No',NOW(),1),
--     (178,'Payroll Country',NULL,'Grade',0,0,'PH','No',NOW(),1),
--     (179,'Payroll Country',NULL,'Year of Passing',0,1,'PH','No',NOW(),1),
--     (180,'Payroll Country',NULL,'Start Date',1,0,'PH','No',NOW(),1),
--     (181,'Payroll Country',NULL,'End Date',1,0,'PH','No',NOW(),1),
--     (182,'Payroll Country',NULL,'City',1,0,'PH','Yes',NOW(),1),
--     (183,'Payroll Country',NULL,'State',1,0,'PH','No',NOW(),1),
--     (184,'Payroll Country',NULL,'Country',1,0,'PH','No',NOW(),1),
--     (185,'Payroll Country',NULL,'Branch Email Address',1,0,'PH','No',NOW(),1),
--     (186,'Payroll Country',NULL,'Timekeeping PIC',1,0,'PH','No',NOW(),1),
--     (187,'Payroll Country',NULL,'Career PIC',1,0,'PH','No',NOW(),1),
--     (187,'Payroll Country',NULL,'Tax Code',1,0,'PH','No',NOW(),1);


-- INSERT INTO customization_fields
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES 
--     (156,'Payroll Country',NULL,'Start Date',0,0,'IN','No',NOW(),1),
--     (157,'Payroll Country',NULL,'End Date',0,0,'IN','No',NOW(),1),
--     (158,'Payroll Country',NULL,'City',0,0,'IN','No',NOW(),1),
--     (159,'Payroll Country',NULL,'State',0,0,'IN','No',NOW(),1),
--     (160,'Payroll Country',NULL,'Country',0,0,'IN','No',NOW(),1),
--     (161,'Payroll Country',NULL,'School diploma or degree',1,0,'IN','No',NOW(),1),
--     (162,'Payroll Country',NULL,'Specialization',1,1,'IN','No',NOW(),1),
--     (163,'Payroll Country',NULL,'Institute Name',1,0,'IN','No',NOW(),1),
--     (164,'Payroll Country',NULL,'University',1,0,'IN','No',NOW(),1),
--     (165,'Payroll Country',NULL,'Percentage',1,0,'IN','No',NOW(),1),
--     (166,'Payroll Country',NULL,'Grade',1,0,'IN','No',NOW(),1),
--     (167,'Payroll Country',NULL,'Year of Passing',1,1,'IN','No',NOW(),1),
--     (168,'Payroll Country',NULL,'Start Date',0,0,'IN','No',NOW(),1),
--     (169,'Payroll Country',NULL,'End Date',0,0,'IN','No',NOW(),1),
--     (170,'Payroll Country',NULL,'City',0,0,'IN','No',NOW(),1),
--     (171,'Payroll Country',NULL,'State',0,0,'IN','No',NOW(),1),
--     (172,'Payroll Country',NULL,'Country',0,0,'IN','No',NOW(),1),
--     (173,'Payroll Country',NULL,'School diploma or degree',1,0,'IN','No',NOW(),1),
--     (174,'Payroll Country',NULL,'Specialization',1,1,'IN','No',NOW(),1),
--     (175,'Payroll Country',NULL,'Institute Name',1,0,'IN','No',NOW(),1),
--     (176,'Payroll Country',NULL,'University',1,0,'IN','No',NOW(),1),
--     (177,'Payroll Country',NULL,'Percentage',1,0,'IN','No',NOW(),1),
--     (178,'Payroll Country',NULL,'Grade',1,0,'IN','No',NOW(),1),
--     (179,'Payroll Country',NULL,'Year of Passing',1,1,'IN','No',NOW(),1),
--     (180,'Payroll Country',NULL,'Start Date',0,0,'IN','No',NOW(),1),
--     (181,'Payroll Country',NULL,'End Date',0,0,'IN','No',NOW(),1),
--     (182,'Payroll Country',NULL,'City',0,0,'IN','No',NOW(),1),
--     (183,'Payroll Country',NULL,'State',0,0,'IN','Yes',NOW(),1),
--     (184,'Payroll Country',NULL,'Country',0,0,'IN','No',NOW(),1),
--     (185,'Payroll Country',NULL,'Branch Email Address',0,0,'IN','No',NOW(),1),
--     (186,'Payroll Country',NULL,'Timekeeping PIC',0,0,'IN','No',NOW(),1),
--     (187,'Payroll Country',NULL,'Career PIC',0,0,'IN','No',NOW(),1),
--     (187,'Payroll Country',NULL,'Tax Code',0,0,'IN','No',NOW(),1);

-- INSERT INTO customization_fields
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES 
--     (156,'Payroll Country',NULL,'Start Date',0,0,'GH','No',NOW(),1),
--     (157,'Payroll Country',NULL,'End Date',0,0,'GH','No',NOW(),1),
--     (158,'Payroll Country',NULL,'City',0,0,'GH','No',NOW(),1),
--     (159,'Payroll Country',NULL,'State',0,0,'GH','No',NOW(),1),
--     (160,'Payroll Country',NULL,'Country',0,0,'GH','No',NOW(),1),
--     (161,'Payroll Country',NULL,'School diploma or degree',1,0,'GH','No',NOW(),1),
--     (162,'Payroll Country',NULL,'Specialization',1,1,'GH','No',NOW(),1),
--     (163,'Payroll Country',NULL,'Institute Name',1,0,'GH','No',NOW(),1),
--     (164,'Payroll Country',NULL,'University',1,0,'GH','No',NOW(),1),
--     (165,'Payroll Country',NULL,'Percentage',1,0,'GH','No',NOW(),1),
--     (166,'Payroll Country',NULL,'Grade',1,0,'GH','No',NOW(),1),
--     (167,'Payroll Country',NULL,'Year of Passing',1,1,'GH','No',NOW(),1),
--     (168,'Payroll Country',NULL,'Start Date',0,0,'GH','No',NOW(),1),
--     (169,'Payroll Country',NULL,'End Date',0,0,'GH','No',NOW(),1),
--     (170,'Payroll Country',NULL,'City',0,0,'GH','No',NOW(),1),
--     (171,'Payroll Country',NULL,'State',0,0,'GH','No',NOW(),1),
--     (172,'Payroll Country',NULL,'Country',0,0,'GH','No',NOW(),1),
--     (173,'Payroll Country',NULL,'School diploma or degree',1,0,'GH','No',NOW(),1),
--     (174,'Payroll Country',NULL,'Specialization',1,1,'GH','No',NOW(),1),
--     (175,'Payroll Country',NULL,'Institute Name',1,0,'GH','No',NOW(),1),
--     (176,'Payroll Country',NULL,'University',1,0,'GH','No',NOW(),1),
--     (177,'Payroll Country',NULL,'Percentage',1,0,'GH','No',NOW(),1),
--     (178,'Payroll Country',NULL,'Grade',1,0,'GH','No',NOW(),1),
--     (179,'Payroll Country',NULL,'Year of Passing',1,1,'GH','No',NOW(),1),
--     (180,'Payroll Country',NULL,'Start Date',0,0,'GH','No',NOW(),1),
--     (181,'Payroll Country',NULL,'End Date',0,0,'GH','No',NOW(),1),
--     (182,'Payroll Country',NULL,'City',0,0,'GH','No',NOW(),1),
--     (183,'Payroll Country',NULL,'State',0,0,'GH','Yes',NOW(),1),
--     (184,'Payroll Country',NULL,'Country',0,0,'GH','No',NOW(),1),
--     (185,'Payroll Country',NULL,'Branch Email Address',0,0,'GH','No',NOW(),1),
--     (186,'Payroll Country',NULL,'TimekeepGHg PIC',0,0,'GH','No',NOW(),1),
--     (187,'Payroll Country',NULL,'Career PIC',0,0,'GH','No',NOW(),1),
--     (187,'Payroll Country',NULL,'Tax Code',0,0,'GH','No',NOW(),1);
    
-- INSERT INTO fields (Field_Name, Form_Id,Tab_Name)
-- VALUES 
--     ('Driving License Number', 243,'Driving License'),
--     ('Issue Date', 243,'Driving License'),
--     ('Expiry Date', 243,'Driving License'),
--     ('Issuing Authority', 243,'Driving License'),
--     ('Issuing Country', 243,'Driving License'),
--     ('Vehicle Type', 243,'Driving License'),
--     ('Issuing State', 243,'Driving License'),
--     ('Passport Number', 243,'Passport'),
--     ('Issue Date', 243,'Passport'),
--     ('Issuing Authority', 243,'Passport'),
--     ('Issuing Country', 243,'Passport'),
--     ('Expiry Date', 243,'Passport'),
--     ('Priority', 15, NULL),
--     ('Experience level', 15, NULL),
--     ('Workplace type', 15, NULL),
--     ('Job Location Type', 15, NULL);
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (189, 'Payroll Country', NULL, 'Driving License Number', 1, 1, 'PH', NULL, NOW(), 1),
-- (190, 'Payroll Country', NULL, 'Issue Date', 1, 1, 'PH', NULL, NOW(), 1),
-- (191, 'Payroll Country', NULL, 'Expiry Date', 1, 1, 'PH', NULL, NOW(), 1),
-- (192, 'Payroll Country', NULL, 'Issuing Authority', 1, 1, 'PH', NULL, NOW(), 1),
-- (193, 'Payroll Country', NULL, 'Issuing Country', 1, 1, 'PH', NULL, NOW(), 1),
-- (194, 'Payroll Country', NULL, 'Vehicle Type', 1, 1, 'PH', NULL, NOW(), 1),
-- (195, 'Payroll Country', NULL, 'Issuing State', 1, 1, 'PH', NULL, NOW(), 1),
-- (196, 'Payroll Country', NULL, 'Passport Number', 1, 1, 'PH', NULL, NOW(), 1),
-- (197, 'Payroll Country', NULL, 'Issue Date', 1, 1, 'PH', NULL, NOW(), 1),
-- (198, 'Payroll Country', NULL, 'Issuing Authority', 1, 1, 'PH', NULL, NOW(), 1),
-- (199, 'Payroll Country', NULL, 'Issuing Country', 1, 1, 'PH', NULL, NOW(), 1),
-- (200, 'Payroll Country', NULL, 'Expiry Date', 1, 1, 'PH', NULL, NOW(), 1),
-- (201, 'Payroll Country', NULL, 'Priority', 1, 0, 'PH', NULL, NOW(), 1),
-- (202, 'Payroll Country', NULL, 'Experience level', 1, 1, 'PH', NULL, NOW(), 1),
-- (203, 'Payroll Country', NULL, 'Workplace type', 1, 1, 'PH', NULL, NOW(), 1),
-- (204, 'Payroll Country', NULL, 'Job Location Type', 1, 1, 'PH', 'Yes', NOW(), 1);

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (189, 'Payroll Country', NULL, 'Driving License Number', 0, 0, 'IN', NULL, NOW(), 1),
-- (190, 'Payroll Country', NULL, 'Issue Date', 0, 0, 'IN', NULL, NOW(), 1),
-- (191, 'Payroll Country', NULL, 'Expiry Date', 0, 0, 'IN', NULL, NOW(), 1),
-- (192, 'Payroll Country', NULL, 'Issuing Authority', 0, 0, 'IN', NULL, NOW(), 1),
-- (193, 'Payroll Country', NULL, 'Issuing Country', 0, 0, 'IN', NULL, NOW(), 1),
-- (194, 'Payroll Country', NULL, 'Vehicle Type', 0, 0, 'IN', NULL, NOW(), 1),
-- (195, 'Payroll Country', NULL, 'Issuing State', 0, 0, 'IN', NULL, NOW(), 1),
-- (196, 'Payroll Country', NULL, 'Passport Number', 0, 0, 'IN', NULL, NOW(), 1),
-- (197, 'Payroll Country', NULL, 'Issue Date', 0, 0, 'IN', NULL, NOW(), 1),
-- (198, 'Payroll Country', NULL, 'Issuing Authority', 0, 0, 'IN', NULL, NOW(), 1),
-- (199, 'Payroll Country', NULL, 'Issuing Country', 0, 0, 'IN', NULL, NOW(), 1),
-- (200, 'Payroll Country', NULL, 'Expiry Date', 0, 0, 'IN', NULL, NOW(), 1),
-- (201, 'Payroll Country', NULL, 'Priority', 1, 0, 'IN', NULL, NOW(), 1),
-- (202, 'Payroll Country', NULL, 'Experience level', 1, 1, 'IN', NULL, NOW(), 1),
-- (203, 'Payroll Country', NULL, 'Workplace type', 1, 1, 'IN', NULL, NOW(), 1),
-- (204, 'Payroll Country', NULL, 'Job Location Type', 1, 1, 'IN', 'Yes', NOW(), 1);

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (189, 'Payroll Country', NULL, 'Driving License Number', 0, 0, 'GH', NULL, NOW(), 1),
-- (190, 'Payroll Country', NULL, 'Issue Date', 0, 0, 'GH', NULL, NOW(), 1),
-- (191, 'Payroll Country', NULL, 'Expiry Date', 0, 0, 'GH', NULL, NOW(), 1),
-- (192, 'Payroll Country', NULL, 'Issuing Authority', 0, 0, 'GH', NULL, NOW(), 1),
-- (193, 'Payroll Country', NULL, 'Issuing Country', 0, 0, 'GH', NULL, NOW(), 1),
-- (194, 'Payroll Country', NULL, 'Vehicle Type', 0, 0, 'GH', NULL, NOW(), 1),
-- (195, 'Payroll Country', NULL, 'Issuing State', 0, 0, 'GH', NULL, NOW(), 1),
-- (196, 'Payroll Country', NULL, 'Passport Number', 0, 0, 'GH', NULL, NOW(), 1),
-- (197, 'Payroll Country', NULL, 'Issue Date', 0, 0, 'GH', NULL, NOW(), 1),
-- (198, 'Payroll Country', NULL, 'Issuing Authority', 0, 0, 'GH', NULL, NOW(), 1),
-- (199, 'Payroll Country', NULL, 'Issuing Country', 0, 0, 'GH', NULL, NOW(), 1),
-- (200, 'Payroll Country', NULL, 'Expiry Date', 0, 0, 'GH', NULL, NOW(), 1),
-- (201, 'Payroll Country', NULL, 'Priority', 1, 0, 'GH', NULL, NOW(), 1),
-- (202, 'Payroll Country', NULL, 'Experience level', 1, 1, 'GH', NULL, NOW(), 1),
-- (203, 'Payroll Country', NULL, 'Workplace type', 1, 1, 'GH', NULL, NOW(), 1),
-- (204, 'Payroll Country', NULL, 'Job Location Type', 1, 1, 'GH', 'Yes', NOW(), 1);


-- INSERT INTO fields (Field_Name, Form_Id,Tab_Name)
-- VALUES 
--     ('Pronoun' ,178,'Personal'),
--     ('Gender Orientations',178,'Personal'),
--     ('Pronoun' ,223,'Personal'),
--     ('Gender Orientations',223,'Personal'),
--     ('Pronoun' ,16,'Personal'),
--     ('Gender Orientations',16,'Personal');


-- INSERT INTO customization_fields
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES 
--     (205,'Pronoun',NULL,'Pronoun',1,1,'PH','Yes',NOW(),1),
--     (206,'Gender Orientations',NULL,'Gender Orientations',1,1,'PH','Yes',NOW(),1),
--     (207,'Pronoun',NULL,'Pronoun',1,1,'PH','Yes',NOW(),1),
--     (208,'Gender Orientations',NULL,'Gender Orientations',1,1,'PH','Yes',NOW(),1),
--     (209,'Pronoun',NULL,'Pronoun',1,1,'PH','Yes',NOW(),1),
--     (210,'Gender Orientations',NULL,'Gender Orientations',1,1,'PH','Yes',NOW(),1);

-- INSERT INTO customization_fields
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES 
--     (205,'Pronoun',NULL,'Pronoun',0,0,'IN','Yes',NOW(),1),
--     (206,'Gender Orientations',NULL,'Gender Orientations',0,0,'IN','Yes',NOW(),1),
--     (207,'Pronoun',NULL,'Pronoun',0,0,'IN','Yes',NOW(),1),
--     (208,'Gender Orientations',NULL,'Gender Orientations',0,0,'IN','Yes',NOW(),1),
--     (209,'Pronoun',NULL,'Pronoun',0,0,'IN','Yes',NOW(),1),
--     (210,'Gender Orientations',NULL,'Gender Orientations',0,0,'IN','Yes',NOW(),1);

-- INSERT INTO customization_fields
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES 
--     (205,'Pronoun',NULL,'Pronoun',0,0,'GH','Yes',NOW(),1),
--     (206,'Gender Orientations',NULL,'Gender Orientations',0,0,'GH','Yes',NOW(),1),
--     (207,'Pronoun',NULL,'Pronoun',0,0,'GH','Yes',NOW(),1),
--     (208,'Gender Orientations',NULL,'Gender Orientations',0,0,'GH','Yes',NOW(),1),
--     (209,'Pronoun',NULL,'Pronoun',0,0,'GH','Yes',NOW(),1),
--     (210,'Gender Orientations',NULL,'Gender Orientations',0,0,'GH','Yes',NOW(),1);

-- update customization_fields set Customization_Applicable_For = 'Payroll Country' where Field_Id between 205 and 210;

-- -- Abhishek #8861
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('275', 'Send Assessment', '16', '3');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('271', 'Assessment Feedback', '16', '3');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('272', 'Schedule Interview', '16', '3');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('273', 'Interview Feedback', '16', '3');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('274', 'Candidate Verification', '178', '23');

-- INSERT INTO fields (Field_Name, Form_Id,Tab_Name)
-- VALUES 
--     ('Availablity To Join' ,15,null),
--     ('National Identity Number (Aadhar/Social Security)', 243, NULL),
--     ('Tax Identification Number (PAN/TFN/SSN)', 243, NULL),
--     ('HDMF Id', 243, NULL),
--     ('UAN', 243, NULL),
--     ('National Identity Number (Aadhar/Social Security)', 178, NULL),
--     ('Tax Identification Number (PAN/TFN/SSN)', 178, NULL),
--     ('Insurance No', 178, NULL),
--     ('NPS', 178, NULL),
--     ('HDMF Id', 178, NULL),
--     ('UAN', 178, NULL),
--     ('National Identity Number (Aadhar/Social Security)', 16, NULL),
--     ('Tax Identification Number (PAN/TFN/SSN)', 16, NULL),
--     ('HDMF Id', 16, NULL),
--     ('UAN', 16, NULL),
--     ('Insurance No', 243, NULL),
--     ('NPS', 243, NULL),
--     ('Insurance No', 16, NULL),
--     ('NPS', 16, NULL);

-- INSERT INTO customization_fields
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES 
-- (211,'Payroll Country',NULL, 'Availablity To Join', 1, 0,'PH',NULL, NOW(), 1),
-- (212, 'Payroll Country',NULL,'National Identity Number', 1, 0,'PH',NULL, NOW(), 1),
-- (213, 'Payroll Country',NULL,'Tax Identification Number', 1, 0,'PH',NULL, NOW(), 1),
-- (214, 'Payroll Country',NULL,'SSS', 1, 0,'PH',NULL, NOW(), 1),
-- (215,'Payroll Country',NULL, 'National Identity Number', 1, 0,'PH',NULL, NOW(), 1),
-- (216, 'Payroll Country',NULL,'Tax Identification Number', 1, 0,'PH',NULL, NOW(), 1),
-- (217, 'Payroll Country',NULL,'Philhealth Id', 1, 0,'PH',NULL, NOW(), 1),
-- (218, 'Payroll Country',NULL,'HDMF Id', 1, 0,'PH',NULL, NOW(), 1),
-- (219, 'Payroll Country',NULL,'SSS', 1, 0,'PH',NULL, NOW(), 1),
-- (220, 'Payroll Country',NULL,'National Identity Number', 1, 0,'PH',NULL, NOW(), 1),
-- (221, 'Payroll Country',NULL,'Tax Identification Number', 1, 0,'PH',NULL, NOW(), 1),
-- (222, 'Payroll Country',NULL,'SSS', 1, 0,'PH',NULL, NOW(), 1),
-- (223,'Payroll Country',NULL,'Philhealth Id', 1, 0,'PH',NULL, NOW(), 1),
-- (224,'Payroll Country',NULL,'HDMF Id', 1, 0,'PH',NULL, NOW(), 1),
-- (225,'Payroll Country',NULL,'Philhealth Id', 1, 0,'PH',NULL, NOW(), 1),
-- (226,'Payroll Country',NULL,'HDMF Id', 1, 0,'PH',NULL, NOW(), 1);

-- INSERT INTO customization_fields
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES 
-- (211,'Payroll Country',NULL, 'Availablity To Join', 1, 1,'IN',NULL, NOW(), 1),
-- (212, 'Payroll Country',NULL,'National Identity Number (Aadhar/Social Security)', 1, 0,'IN',NULL, NOW(), 1),
-- (213, 'Payroll Country',NULL,'Tax Identification Number (PAN/TFN/SSN)', 1, 0,'IN',NULL, NOW(), 1),
-- (214, 'Payroll Country',NULL,'UAN', 1, 0,'IN',NULL, NOW(), 1),
-- (215,'Payroll Country',NULL, 'National Identity Number (Aadhar/Social Security)', 1, 0,'IN',NULL, NOW(), 1),
-- (216, 'Payroll Country',NULL,'Tax Identification Number (PAN/TFN/SSN)', 1, 0,'IN',NULL, NOW(), 1),
-- (217, 'Payroll Country',NULL,'Insurance No', 1, 0,'IN',NULL, NOW(), 1),
-- (218, 'Payroll Country',NULL,'NPS', 1, 0,'IN',NULL, NOW(), 1),
-- (219, 'Payroll Country',NULL,'UAN', 1, 0,'IN',NULL, NOW(), 1),
-- (220, 'Payroll Country',NULL,'National Identity Number (Aadhar/Social Security)', 1, 0,'IN',NULL, NOW(), 1),
-- (221, 'Payroll Country',NULL,'Tax Identification Number (PAN/TFN/SSN)', 1, 0,'IN',NULL, NOW(), 1),
-- (222, 'Payroll Country',NULL,'UAN', 1, 0,'IN',NULL, NOW(), 1),
-- (223,'Payroll Country',NULL,'Insurance No', 1, 0,'IN',NULL, NOW(), 1),
-- (224,'Payroll Country',NULL,'NPS', 1, 0,'IN',NULL, NOW(), 1),
-- (225,'Payroll Country',NULL,'Insurance No', 1, 0,'IN',NULL, NOW(), 1),
-- (226,'Payroll Country',NULL,'NPS', 1, 0,'IN',NULL, NOW(), 1);


-- INSERT INTO customization_fields
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES 
-- (211,'Payroll Country',NULL, 'Availablity To Join', 1, 1,'GH',NULL, NOW(), 1),
-- (212, 'Payroll Country',NULL,'National Identity Number (Aadhar/Social Security)', 1, 0,'GH',NULL, NOW(), 1),
-- (213, 'Payroll Country',NULL,'Tax Identification Number (PAN/TFN/SSN)', 1, 0,'GH',NULL, NOW(), 1),
-- (214, 'Payroll Country',NULL,'UAN', 1, 0,'GH',NULL, NOW(), 1),
-- (215,'Payroll Country',NULL, 'National Identity Number (Aadhar/Social Security)', 1, 0,'GH',NULL, NOW(), 1),
-- (216, 'Payroll Country',NULL,'Tax Identification Number (PAN/TFN/SSN)', 1, 0,'GH',NULL, NOW(), 1),
-- (217, 'Payroll Country',NULL,'Insurance No', 1, 0,'GH',NULL, NOW(), 1),
-- (218, 'Payroll Country',NULL,'NPS', 1, 0,'GH',NULL, NOW(), 1),
-- (219, 'Payroll Country',NULL,'UAN', 1, 0,'GH',NULL, NOW(), 1),
-- (220, 'Payroll Country',NULL,'National Identity Number (Aadhar/Social Security)', 1, 0,'GH',NULL, NOW(), 1),
-- (221, 'Payroll Country',NULL,'Tax Identification Number (PAN/TFN/SSN)', 1, 0,'GH',NULL, NOW(), 1),
-- (222, 'Payroll Country',NULL,'UAN', 1, 0,'GH',NULL, NOW(), 1),
-- (223,'Payroll Country',NULL,'Insurance No', 1, 0,'GH',NULL, NOW(), 1),
-- (224,'Payroll Country',NULL,'NPS', 1, 0,'GH',NULL, NOW(), 1),
-- (225,'Payroll Country',NULL,'Insurance No', 1, 0,'GH',NULL, NOW(), 1),
-- (226,'Payroll Country',NULL,'NPS', 1, 0,'GH',NULL, NOW(), 1);

--     DELETE FROM `fields` WHERE `fields`.`Field_Id` >210;

--     INSERT INTO fields (Field_Id, Field_Name, Form_Id, Tab_Name)
-- VALUES 
--     (211, 'Availablity To Join', 15, NULL),
--     (212, 'National Identity Number (Aadhar/Social Security)', 243, NULL),
--     (213, 'Tax Identification Number (PAN/TFN/SSN)', 243, NULL),
--     (214, 'UAN', 243, NULL),
--     (215, 'National Identity Number (Aadhar/Social Security)', 178, NULL),
--     (216, 'Tax Identification Number (PAN/TFN/SSN)', 178, NULL),
--     (217, 'Insurance No', 178, NULL),
--     (218, 'NPS', 178, NULL),
--     (219, 'UAN', 178, NULL),
--     (220, 'National Identity Number (Aadhar/Social Security)', 16, NULL),
--     (221, 'Tax Identification Number (PAN/TFN/SSN)', 16, NULL),
--     (222, 'UAN', 16, NULL),
--     (223, 'Insurance No', 243, NULL),
--     (224, 'NPS', 243, NULL),
--     (225, 'Insurance No', 16, NULL),
--     (226, 'NPS', 16, NULL);

-- #added on may23
--       INSERT INTO fields (Field_Id, Field_Name, Form_Id, Tab_Name)
-- VALUES 
--     (227, 'Availablity To Join', 16, NULL);

--     INSERT INTO customization_fields
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES 
-- (227,'Payroll Country',NULL, 'Availablity To Join', 1, 0,'PH',NULL, NOW(), 1);

-- INSERT INTO customization_fields
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES 
-- (227,'Payroll Country',NULL, 'Availablity To Join', 1, 1,'IN',NULL, NOW(), 1);

-- INSERT INTO customization_fields
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES 
-- (227,'Payroll Country',NULL, 'Availablity To Join', 1, 1,'GH',NULL, NOW(), 1);

-- #added on may23 for priority
-- UPDATE customization_fields SET New_Field_Name='Experience Level',Predefined='Yes' where Field_Id=201;
-- UPDATE customization_fields SET Required=1 where Field_Id=201 AND Country_Code='PH';

-- added on may24 suhan
--      INSERT INTO fields (Field_Id, Field_Name, Form_Id, Tab_Name)
-- VALUES (228,'Priority', 15, NULL);

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES (228, 'Payroll Country', NULL, 'Priority', 1, 1, 'GH', 'Yes', NOW(), 1);
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES (228, 'Payroll Country', NULL, 'Priority', 1, 1, 'PH', 'Yes', NOW(), 1);
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES (228, 'Payroll Country', NULL, 'Priority', 1, 1, 'IN', 'Yes', NOW(), 1);

-- -- -- 07June2024Evening Deployment Queries
-- -- suganya - #8901
-- ALTER TABLE `house_property_income_sources` ADD `Tax_Regime` ENUM('Old Regime','New Regime','All Regime') NOT NULL DEFAULT 'Old Regime' AFTER `Tax_Section_Id`;
-- -- update tax regime for the assessment year - 2025 in the house property income sources table
-- UPDATE `house_property_income_sources` SET `Tax_Regime` = 'All Regime' WHERE `house_property_income_sources`.`Source` = 'Rented Property' AND Assessment_Year = 2025;

-- -- 07June2024Evening Deployment Queries

-- -- MISSING QUERIES APPMANAGER ADDED ON JUNE 7
-- INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name)
-- VALUES (229,'Accreditation Type',243,NULL),
-- (230,'Accreditation Type',178,NULL),
-- (231,'known as',243,NULL),
-- (232,'known as',178,NULL),
-- (233,'street',243,'Bank'),
-- (234,'street',178,'Address'),
-- (235,'street',178,'oAddress'),
-- (236,'street',243,'Address'),
-- (237,'street',178,'Bank'),
-- (238,'street',243,'oAddress'),
-- (239,'Street 2',243,NULL),
-- (240,'Street 2',178,NULL),
-- (241,'Caste',178,NULL),
-- (242,'Caste',243,NULL),
-- (243,'Certificate Tab',178,'Certificate Tab'),
-- (244,'Training Tab',178,'Training Tab'),
-- (245,'Received Date',243,NULL),
-- (246,'Received Date',178,NULL),
-- (247,'Expiry Date',243,NULL),
-- (248,'Expiry Date',178,NULL),
-- (249,'Timekeeing PIC',178,NULL),
-- (250,'Career PIC',178,NULL),
-- (251,'Second Line Manager',178,NULL),
-- (252,'State', 15, NULL),
-- (253,'City/Municipality', 15, NULL),
-- (254,'Group', 15, NULL),
-- (255,'Tax Code', 243, NULL),
-- (256,'Replacement For', 15, NULL),
-- (257,'Job Location', 15, NULL),
-- (258,'Job Type', 15, NULL),
-- (259,'Reason for Vacancy', 15, NULL),
-- (260,'Position', 15, NULL),
-- (261,'Work Schedule', 15, NULL),
-- (262,'Blood Group', 16, NULL),
-- (263,"Mother's Name", 16, NULL),
-- (264,"Father's Name", 16, NULL),
-- (265,'Work Permit', 16, NULL),
-- (266,'Other Work Permit', 16, NULL),
-- (267,'Gender', 16, NULL),
-- (268,'Suffix', 16, NULL),
-- (269,'Passport Number', 16, NULL),
-- (270,'Marital Status', 16, NULL),
-- (271,'Nationality', 16, NULL),
-- (272,'Gender', 178, NULL),
-- (273,'Gender', 243, NULL);



-- -- SQL insert with Field_Id starting from 229
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (229, 'Payroll Country', NULL, 'Accreditation Type', 1, 1, 'IN', 'Yes', '2024-05-24 07:38:08', 1),
-- (230, 'Payroll Country', NULL, 'Accreditation Type', 1, 1, 'IN', 'Yes', '2024-05-24 07:38:08', 1),
-- (231, 'Payroll Country', NULL, 'known as', 1, 0, 'IN', NULL, '2024-05-24 07:38:08', 1),
-- (232, 'Payroll Country', NULL, 'known as', 1, 0, 'IN', NULL, '2024-05-24 07:38:08', 1),
-- (233, 'Payroll Country', NULL, 'street', 1, 1, 'IN', NULL, '2024-05-24 07:38:08', 1),
-- (234, 'Payroll Country', NULL, 'street', 1, 1, 'IN', NULL, '2024-05-24 07:38:08', 1),
-- (235, 'Payroll Country', NULL, 'street', 1, 1, 'IN', NULL, '2024-05-24 07:38:08', 1),
-- (236, 'Payroll Country', NULL, 'street', 1, 1, 'IN', NULL, '2024-05-24 07:38:08', 1),
-- (237, 'Payroll Country', NULL, 'street', 1, 1, 'IN', NULL, '2024-05-24 07:38:08', 1),
-- (238, 'Payroll Country', NULL, 'street', 1, 1, 'IN', NULL, '2024-05-24 07:38:08', 1),
-- (239, 'Payroll Country', NULL, 'Street 2', 1, 0, 'IN', 'No', '2024-05-27 07:25:30', 1),
-- (240, 'Payroll Country', NULL, 'Street 2', 1, 0, 'IN', 'No', '2024-05-27 07:25:30', 1),
-- (241, 'Payroll Country', NULL, 'Caste', 1, 0, 'IN', 'No', '2024-05-28 01:23:15', 1),
-- (242, 'Payroll Country', NULL, 'Caste', 1, 0, 'IN', 'No', '2024-05-28 01:23:15', 1),
-- (243, 'Payroll Country', NULL, 'Certificate Tab', 1, 0, 'IN', 'No', '2024-05-28 01:23:15', 1),
-- (244, 'Payroll Country', NULL, 'Training Tab', 1, 0, 'IN', 'No', '2024-05-28 01:23:15', 1),
-- (245, 'Payroll Country', NULL, 'Received Date', 1, 1, 'IN', 'No', '2024-05-29 08:26:36', 1),
-- (246, 'Payroll Country', NULL, 'Received Date', 1, 1, 'IN', 'No', '2024-05-29 08:26:36', 1),
-- (247, 'Payroll Country', NULL, 'Expiry Date', 1, 1, 'IN', 'No', '2024-05-29 08:26:36', 1),
-- (248, 'Payroll Country', NULL, 'Expiry Date', 1, 1, 'IN', 'No', '2024-05-29 08:26:36', 1),
-- (249, 'Payroll Country', NULL, 'Timekeeing PIC', 0, 0, 'IN', 'No', '2024-06-01 05:01:28', 1),
-- (250, 'Payroll Country', NULL, 'Career PIC', 0, 0, 'IN', 'No', '2024-06-01 05:01:28', 1),
-- (251, 'Payroll Country', NULL, 'Second Line Manager', 0, 0, 'IN', 'No', '2024-06-01 05:01:28', 1);



-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (229, 'Payroll Country', NULL, 'Professional License', 1, 1, 'PH', 'Yes', '2024-05-24 07:38:03', 1),
-- (230, 'Payroll Country', NULL, 'Professional License', 1, 1, 'PH', 'Yes', '2024-05-24 07:38:03', 1),
-- (231, 'Payroll Country', NULL, 'Nickname', 1, 0, 'PH', NULL, '2024-05-24 07:38:03', 1),
-- (232, 'Payroll Country', NULL, 'Nickname', 1, 0, 'PH', NULL, '2024-05-24 07:38:03', 1),
-- (233, 'Payroll Country', NULL, 'House/Building/Unit', 1, 1, 'PH', NULL, '2024-05-24 07:38:03', 1),
-- (234, 'Payroll Country', NULL, 'House/Building/Unit', 1, 1, 'PH', NULL, '2024-05-24 07:38:03', 1),
-- (235, 'Payroll Country', NULL, 'House/Building/Unit', 1, 1, 'PH', NULL, '2024-05-24 07:38:03', 1),
-- (236, 'Payroll Country', NULL, 'House/Building/Unit', 1, 1, 'PH', NULL, '2024-05-24 07:38:03', 1),
-- (237, 'Payroll Country', NULL, 'House/Building/Unit', 1, 1, 'PH', NULL, '2024-05-24 07:38:03', 1),
-- (238, 'Payroll Country', NULL, 'House/Building/Unit', 1, 1, 'PH', NULL, '2024-05-24 07:38:03', 1),
-- (239, 'Payroll Country', NULL, 'Street', 1, 0, 'PH', 'No', '2024-05-27 07:25:18', 1),
-- (240, 'Payroll Country', NULL, 'Street', 1, 0, 'PH', 'No', '2024-05-27 07:25:18', 1),
-- (241, 'Payroll Country', NULL, 'Caste', 0, 0, 'PH', 'No', '2024-05-28 01:23:10', 1),
-- (242, 'Payroll Country', NULL, 'Caste', 0, 0, 'PH', 'No', '2024-05-28 01:23:10', 1),
-- (243, 'Payroll Country', NULL, 'Certificate Tab', 0, 0, 'PH', 'No', '2024-05-28 01:23:10', 1),
-- (244, 'Payroll Country', NULL, 'Training Tab', 0, 0, 'PH', 'No', '2024-05-28 01:23:10', 1),
-- (245, 'Payroll Country', NULL, 'Received Date', 1, 1, 'PH', 'No', '2024-05-29 07:23:51', 1),
-- (246, 'Payroll Country', NULL, 'Received Date', 1, 1, 'PH', 'No', '2024-05-29 07:23:51', 1),
-- (247, 'Payroll Country', NULL, 'Expiry Date', 1, 1, 'PH', 'No', '2024-05-29 07:23:51', 1),
-- (248, 'Payroll Country', NULL, 'Expiry Date', 1, 1, 'PH', 'No', '2024-05-29 07:23:51', 1),
-- (249, 'Payroll Country', NULL, 'Timekeeing PIC', 1, 0, 'PH', 'No', '2024-06-01 05:00:33', 1),
-- (250, 'Payroll Country', NULL, 'Career PIC', 1, 0, 'PH', 'No', '2024-06-01 05:00:33', 1),
-- (251, 'Payroll Country', NULL, 'Second Line Manager', 1, 0, 'PH', 'No', '2024-06-01 05:00:33', 1);


-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (229, 'Payroll Country', NULL, 'Accreditation Type', 1, 1, 'GH', 'Yes', '2024-05-24 07:38:08', 1),
-- (230, 'Payroll Country', NULL, 'Accreditation Type', 1, 1, 'GH', 'Yes', '2024-05-24 07:38:08', 1),
-- (231, 'Payroll Country', NULL, 'known as', 1, 0, 'GH', NULL, '2024-05-24 07:38:08', 1),
-- (232, 'Payroll Country', NULL, 'known as', 1, 0, 'GH', NULL, '2024-05-24 07:38:08', 1),
-- (233, 'Payroll Country', NULL, 'street', 1, 1, 'GH', NULL, '2024-05-24 07:38:08', 1),
-- (234, 'Payroll Country', NULL, 'street', 1, 1, 'GH', NULL, '2024-05-24 07:38:08', 1),
-- (235, 'Payroll Country', NULL, 'street', 1, 1, 'GH', NULL, '2024-05-24 07:38:08', 1),
-- (236, 'Payroll Country', NULL, 'street', 1, 1, 'GH', NULL, '2024-05-24 07:38:08', 1),
-- (237, 'Payroll Country', NULL, 'street', 1, 1, 'GH', NULL, '2024-05-24 07:38:08', 1),
-- (238, 'Payroll Country', NULL, 'street', 1, 1, 'GH', NULL, '2024-05-24 07:38:08', 1),
-- (239, 'Payroll Country', NULL, 'Street 2', 1, 0, 'GH', 'No', '2024-05-27 07:25:30', 1),
-- (240, 'Payroll Country', NULL, 'Street 2', 1, 0, 'GH', 'No', '2024-05-27 07:25:30', 1),
-- (241, 'Payroll Country', NULL, 'Caste', 1, 0, 'GH', 'No', '2024-05-28 01:23:15', 1),
-- (242, 'Payroll Country', NULL, 'Caste', 1, 0, 'GH', 'No', '2024-05-28 01:23:15', 1),
-- (243, 'Payroll Country', NULL, 'Certificate Tab', 1, 0, 'GH', 'No', '2024-05-28 01:23:15', 1),
-- (244, 'Payroll Country', NULL, 'Training Tab', 1, 0, 'GH', 'No', '2024-05-28 01:23:15', 1),
-- (245, 'Payroll Country', NULL, 'Received Date', 1, 1, 'GH', 'No', '2024-05-29 08:26:36', 1),
-- (246, 'Payroll Country', NULL, 'Received Date', 1, 1, 'GH', 'No', '2024-05-29 08:26:36', 1),
-- (247, 'Payroll Country', NULL, 'Expiry Date', 1, 1, 'GH', 'No', '2024-05-29 08:26:36', 1),
-- (248, 'Payroll Country', NULL, 'Expiry Date', 1, 1, 'GH', 'No', '2024-05-29 08:26:36', 1),
-- (249, 'Payroll Country', NULL, 'Timekeeing PIC', 0, 0, 'GH', 'No', '2024-06-01 05:01:28', 1),
-- (250, 'Payroll Country', NULL, 'Career PIC', 0, 0, 'GH', 'No', '2024-06-01 05:01:28', 1),
-- (251, 'Payroll Country', NULL, 'Second Line Manager', 0, 0, 'GH', 'No', '2024-06-01 05:01:28', 1);


-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (252, 'Payroll Country', NULL, 'State', 0, 0, 'IN', NULL, NOW(), 1),
-- (253, 'Payroll Country', NULL, 'City', 0, 0, 'IN', NULL, NOW(), 1),
-- (254, 'Payroll Country', NULL, 'Group', 0, 0, 'IN', NULL, NOW(), 1),
-- (255, 'Payroll Country', NULL, 'Tax Code', 0, 0, 'IN', NULL, NOW(), 1),
-- (256, 'Payroll Country', NULL, 'Replacement For', 1, 1, 'IN', NULL, NOW(), 1),
-- (257, 'Payroll Country', NULL, 'Job Location', 1, 1, 'IN', NULL, NOW(), 1),
-- (258, 'Payroll Country', NULL, 'Job Type', 1, 1, 'IN', NULL, NOW(), 1),
-- (259, 'Payroll Country', NULL, 'Reason for Vacancy', 1, 0, 'IN', NULL, NOW(), 1),
-- (260, 'Payroll Country', NULL, 'Position', 1, 1, 'IN', NULL, NOW(), 1),
-- (261, 'Payroll Country', NULL, 'Work Schedule', 1, 1, 'IN', NULL, NOW(), 1),
-- (262, 'Payroll Country', NULL, 'Blood Group', 1, 0, 'IN', NULL, NOW(), 1),
-- (263, 'Payroll Country', NULL, "Mother's Name", 1, 0, 'IN', NULL, NOW(), 1),
-- (264, 'Payroll Country', NULL, "Father's Name", 1, 0, 'IN', NULL, NOW(), 1),
-- (265, 'Payroll Country', NULL, 'Work Permit', 1, 0, 'IN', NULL, NOW(), 1),
-- (266, 'Payroll Country', NULL, 'Other Work Permit', 1, 0, 'IN', NULL, NOW(), 1),
-- (267, 'Payroll Country', NULL, 'Gender', 1, 1, 'IN', NULL, NOW(), 1),
-- (268, 'Payroll Country', NULL, 'Suffix', 0, 0, 'IN', NULL, NOW(), 1),
-- (269, 'Payroll Country', NULL, 'Passport Number', 1, 0, 'IN', NULL, NOW(), 1),
-- (270, 'Payroll Country', NULL, 'Marital Status', 1, 0, 'IN', NULL, NOW(), 1),
-- (271, 'Payroll Country', NULL, 'Nationality', 1, 0, 'IN', NULL, NOW(), 1),
-- (272, 'Payroll Country', NULL, 'Gender', 1, 1, 'IN', NULL, NOW(), 1),
-- (273, 'Payroll Country', NULL, 'Gender', 1, 1, 'IN', NULL, NOW(), 1);



-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (252, 'Payroll Country', NULL, 'Region/Province', 1, 0, 'PH', 'No', '2024-06-05 13:53:03', 1),
-- (253, 'Payroll Country', NULL, 'City/Municipality', 1, 0, 'PH', 'No', '2024-06-05 13:53:03', 1),
-- (254, 'Payroll Country', NULL, 'Group', 1, 1, 'PH', 'Yes', '2024-06-05 13:53:03', 1),
-- (255, 'Payroll Country', NULL, 'Tax Code', 0, 0, 'PH', 'No', '2024-06-05 13:53:03', 1),
-- (256, 'Payroll Country', NULL, 'Replacement For', 1, 0, 'PH', 'No', '2024-06-05 13:53:03', 1),
-- (257, 'Payroll Country', NULL, 'Job Location', 0, 0, 'PH', 'No', '2024-06-05 13:53:03', 1),
-- (258, 'Payroll Country', NULL, 'Job Type', 1, 1, 'PH', 'Yes', '2024-06-05 13:53:03', 1),
-- (259, 'Payroll Country', NULL, 'Reason for Vacancy', 1, 1, 'PH', 'Yes', '2024-06-05 13:53:03', 1),
-- (260, 'Payroll Country', NULL, 'Position', 1, 0, 'PH', 'No', '2024-06-05 13:53:03', 1),
-- (261, 'Payroll Country', NULL, 'Work Schedule', 0, 0, 'PH', 'No', '2024-06-05 13:53:03', 1),
-- (262, 'Payroll Country', NULL, 'Blood Group', 0, 0, 'PH', 'No', '2024-06-05 13:53:03', 1),
-- (263, 'Payroll Country', NULL, "Mother\'s Name", 0, 0, 'PH', 'No', '2024-06-05 13:53:03', 1),
-- (264, 'Payroll Country', NULL, "Father\'s Name", 0, 0, 'PH', 'No', '2024-06-05 13:53:03', 1),
-- (265, 'Payroll Country', NULL, 'Work Permit', 0, 0, 'PH', 'No', '2024-06-05 13:53:03', 1),
-- (266, 'Payroll Country', NULL, 'Other Work Permit', 0, 0, 'PH', 'No', '2024-06-05 13:53:03', 1),
-- (267, 'Payroll Country', NULL, 'Sex', 1, 1, 'PH', 'No', '2024-06-05 13:53:03', 1),
-- (268, 'Payroll Country', NULL, 'Suffix', 1, 0, 'PH', 'No', '2024-06-05 13:53:03', 1),
-- (269, 'Payroll Country', NULL, 'Passport Number', 0, 0, 'PH', 'No', '2024-06-05 13:53:03', 1),
-- (270, 'Payroll Country', NULL, 'Marital Status', 1, 1, 'PH', 'Yes', '2024-06-05 13:53:03', 1),
-- (271, 'Payroll Country', NULL, 'Nationality', 1, 1, 'PH', 'Yes', '2024-06-05 13:53:03', 1),
-- (272, 'Payroll Country', NULL, 'Sex', 1, 1, 'PH', 'No', '2024-06-05 13:53:03', 1),
-- (273, 'Payroll Country', NULL, 'Sex', 1, 1, 'PH', 'No', '2024-06-05 13:53:03', 1);


-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (252, 'Payroll Country', NULL, 'State', 0, 0, 'IN', 'No', '2024-06-05 13:52:12', 1),
-- (253, 'Payroll Country', NULL, 'City', 0, 0, 'IN', 'No', '2024-06-05 13:52:12', 1),
-- (254, 'Payroll Country', NULL, 'Group', 0, 0, 'IN', 'No', '2024-06-05 13:52:12', 1),
-- (255, 'Payroll Country', NULL, 'Tax Code', 0, 0, 'IN', 'No', '2024-06-05 13:52:12', 1),
-- (256, 'Payroll Country', NULL, 'Replacement For', 1, 1, 'IN', 'No', '2024-06-05 13:52:12', 1),
-- (257, 'Payroll Country', NULL, 'Job Location', 1, 1, 'IN', 'No', '2024-06-05 13:52:12', 1),
-- (258, 'Payroll Country', NULL, 'Job Type', 1, 1, 'IN', 'No', '2024-06-05 13:52:12', 1),
-- (259, 'Payroll Country', NULL, 'Reason for Vacancy', 1, 0, 'IN', 'No', '2024-06-05 13:52:12', 1),
-- (260, 'Payroll Country', NULL, 'Position', 1, 1, 'IN', 'No', '2024-06-05 13:52:12', 1),
-- (261, 'Payroll Country', NULL, 'Work Schedule', 1, 1, 'IN', 'No', '2024-06-05 13:52:12', 1),
-- (262, 'Payroll Country', NULL, 'Blood Group', 1, 0, 'IN', 'No', '2024-06-05 13:52:12', 1),
-- (263, 'Payroll Country', NULL, "Mother\'s Name", 1, 0, 'IN', 'No', '2024-06-05 13:52:12', 1),
-- (264, 'Payroll Country', NULL, "Father\'s Name", 1, 0, 'IN', 'No', '2024-06-05 13:52:12', 1),
-- (265, 'Payroll Country', NULL, 'Work Permit', 1, 0, 'IN', 'No', '2024-06-05 13:52:12', 1),
-- (266, 'Payroll Country', NULL, 'Other Work Permit', 1, 0, 'IN', 'No', '2024-06-05 13:52:12', 1),
-- (267, 'Payroll Country', NULL, 'Gender', 1, 1, 'IN', 'No', '2024-06-05 13:52:12', 1),
-- (268, 'Payroll Country', NULL, 'Suffix', 0, 0, 'IN', 'No', '2024-06-05 13:52:12', 1),
-- (269, 'Payroll Country', NULL, 'Passport Number', 1, 0, 'IN', 'No', '2024-06-05 13:52:12', 1),
-- (270, 'Payroll Country', NULL, 'Marital Status', 1, 0, 'IN', 'No', '2024-06-05 13:52:12', 1),
-- (271, 'Payroll Country', NULL, 'Nationality', 1, 0, 'IN', 'No', '2024-06-05 13:52:12', 1),
-- (272, 'Payroll Country', NULL, 'Gender', 1, 1, 'IN', 'No', '2024-06-05 13:52:12', 1),
-- (273, 'Payroll Country', NULL, 'Gender', 1, 1, 'IN', 'No', '2024-06-05 13:52:12', 1);


-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (252, 'Payroll Country', NULL, 'State', 0, 0, 'GH', 'No', '2024-06-05 13:52:12', 1),
-- (253, 'Payroll Country', NULL, 'City', 0, 0, 'GH', 'No', '2024-06-05 13:52:12', 1),
-- (254, 'Payroll Country', NULL, 'Group', 0, 0, 'GH', 'No', '2024-06-05 13:52:12', 1),
-- (255, 'Payroll Country', NULL, 'Tax Code', 0, 0, 'GH', 'No', '2024-06-05 13:52:12', 1),
-- (256, 'Payroll Country', NULL, 'Replacement For', 1, 1, 'GH', 'No', '2024-06-05 13:52:12', 1),
-- (257, 'Payroll Country', NULL, 'Job Location', 1, 1, 'GH', 'No', '2024-06-05 13:52:12', 1),
-- (258, 'Payroll Country', NULL, 'Job Type', 1, 1, 'GH', 'No', '2024-06-05 13:52:12', 1),
-- (259, 'Payroll Country', NULL, 'Reason for Vacancy', 1, 0, 'GH', 'No', '2024-06-05 13:52:12', 1),
-- (260, 'Payroll Country', NULL, 'Position', 1, 1, 'GH', 'No', '2024-06-05 13:52:12', 1),
-- (261, 'Payroll Country', NULL, 'Work Schedule', 1, 1, 'GH', 'No', '2024-06-05 13:52:12', 1),
-- (262, 'Payroll Country', NULL, 'Blood Group', 1, 0, 'GH', 'No', '2024-06-05 13:52:12', 1),
-- (263, 'Payroll Country', NULL, "Mother's Name", 1, 0, 'GH', 'No', '2024-06-05 13:52:12', 1),
-- (264, 'Payroll Country', NULL, "Father's Name", 1, 0, 'GH', 'No', '2024-06-05 13:52:12', 1),
-- (265, 'Payroll Country', NULL, 'Work Permit', 1, 0, 'GH', 'No', '2024-06-05 13:52:12', 1),
-- (266, 'Payroll Country', NULL, 'Other Work Permit', 1, 0, 'GH', 'No', '2024-06-05 13:52:12', 1),
-- (267, 'Payroll Country', NULL, 'Gender', 1, 1, 'GH', 'No', '2024-06-05 13:52:12', 1),
-- (268, 'Payroll Country', NULL, 'Suffix', 0, 0, 'GH', 'No', '2024-06-05 13:52:12', 1),
-- (269, 'Payroll Country', NULL, 'Passport Number', 1, 0, 'GH', 'No', '2024-06-05 13:52:12', 1),
-- (270, 'Payroll Country', NULL, 'Marital Status', 1, 0, 'GH', 'No', '2024-06-05 13:52:12', 1),
-- (271, 'Payroll Country', NULL, 'Nationality', 1, 0, 'GH', 'No', '2024-06-05 13:52:12', 1),
-- (272, 'Payroll Country', NULL, 'Gender', 1, 1, 'GH', 'No', '2024-06-05 13:52:12', 1),
-- (273, 'Payroll Country', NULL, 'Gender', 1, 1, 'GH', 'No', '2024-06-05 13:52:12', 1);



-- INSERT INTO fields (Field_Id, Field_Name, Form_Id, Tab_Name)
-- VALUES
-- (278,'Client', 15, NULL);

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (278, 'Payroll Country', NULL, 'Client', 0, 0, 'GH', 'No', Now(), 1);
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (278, 'Payroll Country', NULL, 'Client', 1, 0, 'IN', 'No', Now(), 1);
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (278, 'Payroll Country', NULL, 'Client', 1, 0, 'GH', 'No', Now(), 1);

-- -- june 11 queries added by suhan
-- INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name)
-- VALUES
-- (279, 'Company', 16, NULL),
-- (280, 'Designation', 16, NULL),
-- (281, 'From', 16, NULL),
-- (282, 'To', 16, NULL),
-- (283, 'Duration', 16, NULL),
-- (284, 'Certificate', 16, NULL),
-- (285, 'Received On', 16, NULL),
-- (286, 'Received From', 16, NULL);
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (279, 'Payroll Country', NULL, 'Company', 1, 0, 'PH', NULL, NOW(), 1),
-- (280, 'Payroll Country', NULL, 'Designation', 1, 0, 'PH', NULL, NOW(), 1),
-- (281, 'Payroll Country', NULL, 'From', 1, 0, 'PH', NULL, NOW(), 1),
-- (282, 'Payroll Country', NULL, 'To', 1, 0, 'PH', NULL, NOW(), 1),
-- (283, 'Payroll Country', NULL, 'Duration', 1, 0, 'PH', NULL, NOW(), 1),
-- (284, 'Payroll Country', NULL, 'Certificate', 1, 0, 'PH', NULL, NOW(), 1),
-- (285, 'Payroll Country', NULL, 'Received On', 1, 0, 'PH', NULL, NOW(), 1),
-- (286, 'Payroll Country', NULL, 'Received From', 1, 0, 'PH', NULL, NOW(), 1);
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (279, 'Payroll Country', NULL, 'Company', 1, 0, 'IN', NULL, NOW(), 1),
-- (280, 'Payroll Country', NULL, 'Designation', 1, 0, 'IN', NULL, NOW(), 1),
-- (281, 'Payroll Country', NULL, 'From', 1, 0, 'IN', NULL, NOW(), 1),
-- (282, 'Payroll Country', NULL, 'To', 1, 0, 'IN', NULL, NOW(), 1),
-- (283, 'Payroll Country', NULL, 'Duration', 1, 0, 'IN', NULL, NOW(), 1),
-- (284, 'Payroll Country', NULL, 'Certificate', 1, 0, 'IN', NULL, NOW(), 1),
-- (285, 'Payroll Country', NULL, 'Received On', 1, 0, 'IN', NULL, NOW(), 1),
-- (286, 'Payroll Country', NULL, 'Received From', 1, 0, 'IN', NULL, NOW(), 1);
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (279, 'Payroll Country', NULL, 'Company', 1, 0, 'GH', NULL, NOW(), 1),
-- (280, 'Payroll Country', NULL, 'Designation', 1, 0, 'GH', NULL, NOW(), 1),
-- (281, 'Payroll Country', NULL, 'From', 1, 0, 'GH', NULL, NOW(), 1),
-- (282, 'Payroll Country', NULL, 'To', 1, 0, 'GH', NULL, NOW(), 1),
-- (283, 'Payroll Country', NULL, 'Duration', 1, 0, 'GH', NULL, NOW(), 1),
-- (284, 'Payroll Country', NULL, 'Certificate', 1, 0, 'GH', NULL, NOW(), 1),
-- (285, 'Payroll Country', NULL, 'Received On', 1, 0, 'GH', NULL, NOW(), 1),
-- (286, 'Payroll Country', NULL, 'Received From', 1, 0, 'GH', NULL, NOW(), 1);

-- INSERT INTO `customization_forms` (`Custom_Id`, `Form_Id`, `Customization_Applicable_For`, `Org_Code`, `Enable`, `New_Form_Name`, `Added_On`, `Added_By`, `Updated_On`, `Updated_By`) VALUES  (NULL, 269, 'Specific Organization', 'cebprouat', 1, 'Core Group',NOW(), 1, NULL, NULL),  (NULL, 269, 'Specific Organization', 'cebpro', 1, 'Core Group',NOW(), 1, NULL, NULL);

-- suganya - #8928 - need to run in production
-- INSERT INTO `forms`(`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('276', 'Tme Off Management', '0', '20'),('277', 'Leave Override', '276', '20');

-- -- custom fields added on june13 suhan
-- INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name)
-- VALUES
-- (287, 'Source of Application', 16, NULL),
-- (288, 'Expected Qualification', 15, NULL),
-- (289, 'Currency', 16, NULL),
-- (290, 'Preferred Location',16, NULL);
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (287, 'Payroll Country', NULL,'Source of Application', 1, 1, 'PH', 'Yes', NOW(), 1),
-- (288, 'Payroll Country', NULL,'Educational Attainment', 1, 1,'PH','Yes', NOW(), 1),
-- (289, 'Payroll Country', NULL,'Currency',1, 1,'GH','Yes', NOW(), 1),
-- (290, 'Payroll Country', NULL,'Preferred Location',0, 0,'GH','Yes', NOW(), 1);

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (287, 'Payroll Country', NULL,'Source of Application', 1, 0, 'IN', 'Yes', NOW(), 1),
-- (288, 'Payroll Country', NULL,'Expected Qualification', 1, 1,'IN','Yes', NOW(), 1),
-- (289, 'Payroll Country', NULL,'Currency',1, 0,'GH','Yes', NOW(), 1),
-- (290, 'Payroll Country', NULL,'Preferred Location',1, 1,'GH','Yes', NOW(), 1);


-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (287, 'Payroll Country', NULL,'Source of Application', 1, 0, 'GH','Yes', NOW(), 1),
-- (288, 'Payroll Country', NULL,'Expected Qualification', 1, 1,'GH','Yes', NOW(), 1),
-- (289, 'Payroll Country', NULL,'Currency',1, 0,'GH','Yes', NOW(), 1),
-- (290, 'Payroll Country', NULL,'Preferred Location',1, 1,'GH','Yes', NOW(), 1);

-- UPDATE fields set Field_Name='Gender Orientation'
-- WHERE Field_Name = 'Gender Orientations';

-- UPDATE customization_fields
-- SET New_Field_Name = 'Gender Orientation'
-- WHERE New_Field_Name = 'Gender Orientations';

-- UPDATE customization_fields
-- SET  Required = 1
-- WHERE Field_Id IN (252)
-- AND Country_Code = 'PH';

-- -- need to run in production
-- UPDATE customization_fields
-- SET Enable = 1
-- WHERE Field_Id IN (175,179)
--  AND Country_Code = 'PH';

--  UPDATE customization_fields
-- SET Required = 0
-- WHERE Field_Id IN (175,179)
--  AND Country_Code = 'PH';

-- INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name)
-- VALUES
-- (291, 'Hiring Manager', 16, NULL);

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (291, 'Payroll Country', NULL,'Hiring Manager', '0', '0','GH',Null,NOW(), 1);
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (291, 'Payroll Country', NULL,'Hiring Manager', '0', '0','IN',Null,NOW(), 1);
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (291, 'Payroll Country', NULL,'Hiring Manager', '1', '1','PH',Null,NOW(), 1);

-- UPDATE customization_fields
--   set Predefined='Yes'
--   where Field_Id=271;

-- -- suganya - #8928
-- UPDATE `forms` SET `Form_Name` = 'Time Off Management' WHERE `forms`.`Form_Id` = 276;

-- -- revathi - #9036
-- INSERT INTO `forms`(`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('278', 'Recruitment Dashboard', '0', '3');

-- -- soundar #8765
-- ALTER TABLE `data_integration_schedule` CHANGE `Report_Type` `Report_Type` ENUM('EMPSR','EMPUSR','EMCAUR','EMAR','HRAR','HREAR','HREUR','HRLR','HRRR', 'BID')  CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL;
-- ALTER TABLE data_integration_schedule ADD COLUMN TRIGGER_TYPE INT(3) NOT NULL DEFAULT 1 AFTER Report_Type;

-- -- suhan july 9
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('279', 'Candidate Rejection', '178', '23');

-- -- Revathi #9081 - July 11
-- DELETE FROM `forms` WHERE `forms`.`Form_Id` = 186;
-- DELETE FROM `forms` WHERE `forms`.`Form_Id` = 278;
-- DELETE FROM `hrapp_plan_form` WHERE `hrapp_plan_form`.`Form_Id` = 186;
-- DELETE FROM `hrapp_plan_form` WHERE `hrapp_plan_form`.`Form_Id` = 278;

-- UPDATE customization_fields
-- SET Required = 0
-- WHERE Field_Id IN (144,145,146,147,148,149,150)
--  AND Country_Code != 'PH';

--  INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name)
-- VALUES
-- (292, 'Year Of Start', 178, NULL);
--  INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (292, 'Payroll Country', NULL, 'Year Of Start', 1, 1,'PH','Yes' ,NOW(), 1);
--  INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (292, 'Payroll Country', NULL, 'Year Of Start', 0, 0,'IN','Yes' ,NOW(), 1);
--  INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (292, 'Payroll Country', NULL, 'Year Of Start', 0, 0,'GH','Yes' ,NOW(), 1);

-- INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name)
-- VALUES
-- (293, 'Year Of Start', 243, NULL),
-- (294, 'Year Of Start', 16, NULL);
--  INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (293, 'Payroll Country', NULL, 'Year Of Start', 1, 1,'PH','Yes' ,NOW(), 1),
-- (294, 'Payroll Country', NULL, 'Year Of Start', 1, 1,'PH','Yes' ,NOW(), 1);
--  INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (293, 'Payroll Country', NULL, 'Year Of Start', 0, 0,'IN','Yes' ,NOW(), 1),
-- (294, 'Payroll Country', NULL, 'Year Of Start', 0, 0,'IN','Yes' ,NOW(), 1);
--  INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (293, 'Payroll Country', NULL, 'Year Of Start', 0, 0,'GH','Yes' ,NOW(), 1),
-- (294, 'Payroll Country', NULL, 'Year Of Start', 0, 0,'GH','Yes' ,NOW(), 1);

-- Suhan 22 July 2024 need to run in production

-- INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name)
-- VALUES
-- (295, 'Birth Place', 243, NULL),
-- (296, 'Religion', 243, NULL),
-- (297, 'Tax identification number (Tax file no)', 243, NULL),
-- (298, 'Birth Place', 16, NULL),
-- (299, 'Religion', 16, NULL),
-- (300, 'Tax identification number (Tax file no)', 16, NULL),
-- (301, 'Birth Place', 178, NULL),
-- (302, 'Religion', 178, NULL),
-- (303, 'Tax identification number (Tax file no)', 178, NULL);
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (295, 'Payroll Country', NULL, 'Birth Place', 1, 1,'PH' ,NOW(), 1,'Yes'),
-- (296, 'Payroll Country', NULL, 'Religion',  1, 1,'PH', NOW(), 1,'Yes'),
-- (297, 'Payroll Country', NULL, 'Tax identification number (Tax file no)',  1, 1,'PH', NOW(), 1,'No'),
-- (298, 'Payroll Country', NULL, 'Birth Place', 1, 1,'PH', NOW(), 1,'No'),
-- (299, 'Payroll Country', NULL, 'Religion',  1, 1,'PH', NOW(), 1,'Yes'),
-- (300, 'Payroll Country', NULL, 'Tax identification number (Tax file no)', 1, 1,'PH', NOW(), 1,'No'),
-- (301, 'Payroll Country', NULL, 'Birth Place', 1, 1,'PH', NOW(), 1,'No'),
-- (302, 'Payroll Country', NULL, 'Religion',  1, 1,'PH',NOW(), 1,'Yes'),
-- (303, 'Payroll Country', NULL, 'Tax identification number (Tax file no)',  1, 1,'PH', NOW(), 1,'No');
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (295, 'Payroll Country', NULL, 'Birth Place', 1, 0, 'IN', NOW(), 1, 'Yes'),
-- (296, 'Payroll Country', NULL, 'Religion',  1, 0, 'IN', NOW(), 1, 'Yes'),
-- (297, 'Payroll Country', NULL, 'Tax identification number (Tax file no)',  1, 0, 'IN', NOW(), 1, 'No'),
-- (298, 'Payroll Country', NULL, 'Birth Place', 1, 0, 'IN', NOW(), 1, 'No'),
-- (299, 'Payroll Country', NULL, 'Religion',  1, 0, 'IN', NOW(), 1, 'Yes'),
-- (300, 'Payroll Country', NULL, 'Tax identification number (Tax file no)', 1, 0, 'IN', NOW(), 1, 'No'),
-- (301, 'Payroll Country', NULL, 'Birth Place', 1, 0, 'IN', NOW(), 1, 'No'),
-- (302, 'Payroll Country', NULL, 'Religion',  1, 0, 'IN', NOW(), 1, 'Yes'),
-- (303, 'Payroll Country', NULL, 'Tax identification number (Tax file no)',  1, 0, 'IN', NOW(), 1, 'No');
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (295, 'Payroll Country', NULL, 'Birth Place', 1, 0, 'GH', NOW(), 1, 'Yes'),
-- (296, 'Payroll Country', NULL, 'Religion',  1, 0, 'GH', NOW(), 1, 'Yes'),
-- (297, 'Payroll Country', NULL, 'Tax identification number (Tax file no)',  1, 0, 'GH', NOW(), 1, 'No'),
-- (298, 'Payroll Country', NULL, 'Birth Place', 1, 0, 'GH', NOW(), 1, 'No'),
-- (299, 'Payroll Country', NULL, 'Religion',  1, 0, 'GH', NOW(), 1, 'Yes'),
-- (300, 'Payroll Country', NULL, 'Tax identification number (Tax file no)', 1, 0, 'GH', NOW(), 1, 'No'),
-- (301, 'Payroll Country', NULL, 'Birth Place', 1, 0, 'GH', NOW(), 1, 'No'),
-- (302, 'Payroll Country', NULL, 'Religion',  1, 0, 'GH', NOW(), 1, 'Yes'),
-- (303, 'Payroll Country', NULL, 'Tax identification number (Tax file no)',  1, 0, 'GH', NOW(), 1, 'No');

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- SELECT 115, 'Payroll Country', NULL, 'Core Group', 1, 1, 'PH', NOW(), 1, 'Yes'
-- WHERE NOT EXISTS (
--     SELECT 1 
--     FROM `customization_fields` 
--     WHERE `Field_Id` = 115
-- );

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- SELECT 115, 'Payroll Country', NULL, 'Service Provider', 0, 0, 'GH', NOW(), 1, 'Yes'
-- WHERE NOT EXISTS (
--     SELECT 1 
--     FROM `customization_fields` 
--     WHERE `Field_Id` = 115
-- );

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- SELECT 115, 'Payroll Country', NULL, 'Service Provider', 0, 0, 'IN', NOW(), 1, 'Yes'
-- WHERE NOT EXISTS (
--     SELECT 1 
--     FROM `customization_fields` 
--     WHERE `Field_Id` = 115
-- );

-- DELETE FROM fields WHERE Field_Id in (297,300,303);
-- DELETE FROM `customization_fields` WHERE Field_Id in (297,300,303);

-- -- 02August2024 Deployment Queries
-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`) VALUES
-- (308, 'Eligible For Teacher Provident Fund',37),
-- (309, 'TPF Employee Share',37),
-- (310, 'Eligible For Contribution Pension Fund',37),
-- (311, 'CPS Employee Share',37),
-- (312, 'CPS Employer Share',37),
-- (313, 'Eligible For Special Provident Fund',37),
-- (314, 'SPF Employee Share',37),
-- (315, 'SPF End Month',37);

-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`) VALUES
-- (316, 'Teacher Provident Fund',38),
-- (317, 'Contributory Pension Scheme',38),
-- (318, 'Org Contributory Pension Scheme',38),
-- (319, 'Special Provident Fund',38);

-- DELETE FROM fields WHERE Field_Id in (297,300,303);
-- DELETE FROM `customization_fields` WHERE Field_Id in (297,300,303);

-- INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name)
-- VALUES
-- (303, 'Mandatory Documents', 178, NULL);
--  INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (303, 'Payroll Country', NULL, 'Mandatory Documents', 0, 0, 'GH', NOW(), 1, 'Yes');
--  INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (303, 'Payroll Country', NULL, 'Mandatory Documents', 0, 0, 'IN', NOW(), 1, 'Yes');
--  INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (303, 'Payroll Country', NULL, 'Mandatory Documents', 0, 0, 'PH', NOW(), 1, 'Yes');


-- Aug 5 Suhan
-- INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name)
-- VALUES
-- (304, 'PF number', 178, NULL),
-- (305, 'PF number', 243, NULL),
-- (306, 'TDS exemption', 178, NULL),
-- (307, 'TDS exemption', 243, NULL);
--  INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`)
-- VALUES
-- (304, 'Payroll Country', NULL, 'PF number', 1, 0, 'GH', NOW(), 1),
-- (305, 'Payroll Country', NULL, 'PF number', 1, 0, 'GH', NOW(), 1),
-- (306, 'Payroll Country', NULL, 'TDS exemption', 1, 0, 'GH', NOW(), 1),
-- (307, 'Payroll Country', NULL, 'TDS exemption', 1, 0, 'GH', NOW(), 1);
-- INSERT INTO `customization_fields` (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`)
-- VALUES
-- (304, 'Payroll Country', NULL, 'PF number', 0, 0, 'PH', NOW(), 1),
--  (305, 'Payroll Country', NULL, 'PF number', 0, 0, 'PH', NOW(), 1),
--  (306, 'Payroll Country', NULL, 'Tax Withholding', 0, 0, 'PH', NOW(), 1),
--  (307, 'Payroll Country', NULL, 'Tax Withholding', 0, 0, 'PH', NOW(), 1);
--  INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`)
-- VALUES
-- (304, 'Payroll Country', NULL, 'PF number', 1, 0, 'IN', NOW(), 1),
-- (305, 'Payroll Country', NULL, 'PF number', 1, 0, 'IN', NOW(), 1),
-- (306, 'Payroll Country', NULL, 'TDS exemption', 1, 0, 'IN', NOW(), 1),
-- (307, 'Payroll Country', NULL, 'TDS exemption', 1, 0, 'IN', NOW(), 1);


-- -- -- -- TPF and CPS and SPF Query Changes 08Aug2024QueryChanges
-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`) VALUES
-- (308, 'Eligible For Teacher Provident Fund',37),
-- (309, 'TPF Employee Share',37),
-- (310, 'Eligible For Contribution Pension Fund',37),
-- (311, 'CPS Employee Share',37),
-- (312, 'CPS Employer Share',37),
-- (313, 'Eligible For Special Provident Fund',37),
-- (314, 'SPF Employee Share',37),
-- (315, 'SPF End Month',37);

-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`) VALUES
-- (316, 'Teacher Provident Fund',38),
-- (317, 'Contributory Pension Scheme',38),
-- (318, 'Org Contributory Pension Scheme',38),
-- -- (319, 'Special Provident Fund',38);

-- -- 9156 tax slabs and standard deduction changes
-- UPDATE `tax_section` set Max_Limit=75000 WHERE `Section_Id` = 34 AND `Assessment_Year` = '2025';

-- ALTER TABLE `tax_exemption`  ADD `New_Regime_Exemption_Amount` DECIMAL(10,2) NULL DEFAULT NULL  AFTER `Exemption_Amount`;

-- UPDATE `tax_exemption` set New_Regime_Exemption_Amount =75000 WHERE Tax_Section_ID=(SELECT Tax_Section_ID from tax_section where Section_Id=34 AND Assessment_Year=2025) AND Assessment_Year=2025;

-- DELETE FROM tax_rates where Country_Code='IN';

-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Country_Code`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES
-- (NULL, 1, '1.00', '250000.00', 0, 0, 1, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '250001.00', '300000.00', 5, 0, 1, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '300001.00', '500000.00', 5, 5, 1, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '500001.00', '700000.00', 20, 5, 1, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '700001.00', '1000000.00', 20, 10, 1, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '1000001.00', '1200000.00', 30, 15, 1, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '1200001.00', '1500000.00', 30, 20, 1, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '1500001.00', NULL, 30, 30, 1, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '1.00', '250000.00', 0, 0, 2, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '250001.00', '300000.00', 5, 0, 2, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '300001.00', '500000.00', 5, 5, 2, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '500001.00', '700000.00', 20, 5, 2, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '700001.00', '1000000.00', 20, 10, 2, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '1000001.00', '1200000.00', 30, 15, 2, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '1200001.00', '1500000.00', 30, 20, 2, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '1500001.00', NULL, 30, 30, 2, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '1.00', '300000.00', 0, 0, 3, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '300001.00', '500000.00', 5, 5, 3, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '500001.00', '700000.00', 20, 5, 3, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '700001.00', '1000000.00', 20, 10, 3, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '1000001.00', '1200000.00', 30, 15, 3, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '1200001.00', '1500000.00', 30, 20, 3, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '1500001.00', NULL, 30, 30, 3, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '1.00', '500000.00', 0, 0, 4, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '500001.00', '700000.00', 20, 5, 4, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '700001.00', '1000000.00', 20, 10, 4, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '1000001.00', '1200000.00', 30, 15, 4, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '1200001.00', '1500000.00', 30, 20, 4, 2025,'IN', 'Active', 0,1, NOW(), NULL, NULL),
-- (NULL, 1, '1500001.00', NULL, 30, 30, 4, 2025,'IN','Active', 0, 1, NOW(), NULL, NULL);

-- -- Pasha #9246

-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('280', 'Room', '3', '20');

-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`, `Tab_Name`) VALUES (NULL, 'Room No', '262', NULL);

-- INSERT INTO `customization_fields` (`Custom_Id`, `Field_Id`, `Customization_Applicable_For`, `Org_Code`, `Enable`, `Required`, `Country_Code`, `Predefined`, `New_Field_Name`, `Added_On`, `Added_By`, `Updated_On`, `Updated_By`) VALUES (NULL, '308', 'All Organization', NULL, '0', '0', NULL, 'No', 'Room No', '2024-08-22 00:00:00', '1', NULL, NULL);

-- 26Deployment Queries
-- -- update room form as subform for employee data management
-- update forms set Sub_Form=236 where Form_Id=280;

-- -- Aug 27 #9257 Soundar
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('281', 'Hiring Flows', '242', '14');

-- UPDATE `forms` SET Form_Name='Recruitment', Sub_Form='0' WHERE Form_Id='281';
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) 
-- VALUES ('282', 'Hiring Flows', '281', '14');

-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('283', 'Org structure', '0', '20');
-- update forms set Form_Name = 'Org Structure' where Form_Id=283;

-- ALTER TABLE hrapp_registeruser 
-- CHANGE COLUMN Partner_Integration Partner_Integration ENUM('trulead', 'camu', '', 'entomo', 'ums') DEFAULT NULL;

-- -- *****************Location form queries*************** ---------

-- INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name)
-- VALUES
-- (309, 'Pincode', 1, NULL);
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (309, 'Payroll Country', NULL, 'Zip Code', 1, 1,'PH' ,NOW(), 1,'No');
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (309, 'Payroll Country', NULL, 'Pincode', 1, 1,'IN' ,NOW(), 1,'No');
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (309, 'Payroll Country', NULL, 'Pincode', 1, 1,'GH' ,NOW(), 1,'No');

-- INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name)
-- VALUES
-- (310, 'Location Code', 1, NULL);
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (310, 'Payroll Country', NULL, 'Location Code', 0, 0,'PH' ,NOW(), 1,'No');
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (310, 'Payroll Country', NULL, 'Location Code', 0, 0,'IN' ,NOW(), 1,'No');
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (310, 'Payroll Country', NULL, 'Pincode', 0, 0,'GH' ,NOW(), 1,'No');

-- -- Location form query
-- UPDATE `forms` SET Module_Id='20', Sub_Form='283' WHERE Form_Id='1';
-- -- *****************Location form queries*************** ---------



-- ALTER TABLE `tax_section` CHANGE `Max_Limit` `Max_Limit` DECIMAL(15,2) NULL DEFAULT NULL;

-- ALTER TABLE `tax_exemption` CHANGE `Exemption_Amount` `Exemption_Amount` DECIMAL(15,2) NULL DEFAULT NULL;

-- ALTER TABLE `tax_exemption` CHANGE `New_Regime_Exemption_Amount` `New_Regime_Exemption_Amount` DECIMAL(15,2) NULL DEFAULT NULL;

-- ALTER TABLE `section_investment_category` CHANGE `Max_Limit` `Max_Limit` DECIMAL(15,2) NULL DEFAULT NULL;

-- ALTER TABLE `tax_rebate` CHANGE `Tax_Rebate_Amount` `Tax_Rebate_Amount` DECIMAL(15,2) NULL DEFAULT NULL COMMENT 'future enhancement', CHANGE `Maximum_Tax_Rebate` `Maximum_Tax_Rebate` DECIMAL(15,2) NULL DEFAULT NULL, CHANGE `Income_Threshold` `Income_Threshold` DECIMAL(15,2) NULL DEFAULT NULL, CHANGE `Maximum_Tax_Rebate_New_Regime` `Maximum_Tax_Rebate_New_Regime` DECIMAL(15,2) NULL DEFAULT '12500.00', CHANGE `Income_Threshold_New_Regime` `Income_Threshold_New_Regime` DECIMAL(15,2) NULL DEFAULT '500000.00';

-- -- suhan 9325 ip address tracking
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('284', 'Location Tracking', '230', '14');

-- delete from fields where Field_Id > 307;
-- delete from customization_fields where Field_Id > 307;

-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`, `Tab_Name`) VALUES (308, 'Room No', '262', NULL);
-- INSERT INTO `customization_fields` (`Custom_Id`, `Field_Id`, `Customization_Applicable_For`, `Org_Code`, `Enable`, `Required`, `Country_Code`, `Predefined`, `New_Field_Name`, `Added_On`, `Added_By`, `Updated_On`, `Updated_By`) VALUES (NULL, '308', 'All Organization', NULL, '0', '0', NULL, 'No', 'Room No', '2024-08-22 00:00:00', '1', NULL, NULL);

-- INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name)
-- VALUES
-- (309, 'Pincode', 1, NULL);
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (309, 'Payroll Country', NULL, 'Zip Code', 1, 1,'PH' ,NOW(), 1,'No');
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (309, 'Payroll Country', NULL, 'Pincode', 1, 1,'IN' ,NOW(), 1,'No');
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (309, 'Payroll Country', NULL, 'Pincode', 1, 1,'GH' ,NOW(), 1,'No');

-- INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name)
-- VALUES
-- (310, 'Location Code', 1, NULL);
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (310, 'Payroll Country', NULL, 'Location Code', 0, 0,'PH' ,NOW(), 1,'No');
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (310, 'Payroll Country', NULL, 'Location Code', 0, 0,'IN' ,NOW(), 1,'No');
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (310, 'Payroll Country', NULL, 'Location Code', 0, 0,'GH' ,NOW(), 1,'No');

-- INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name)
-- VALUES
-- (311, 'Grade Code', 20, NULL);
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (311, 'Payroll Country', NULL, 'Grade Code', 1, 0,'PH' ,NOW(), 1,'No');
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (311, 'Payroll Country', NULL, 'Grade Code', 1, 0,'IN' ,NOW(), 1,'No');
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (311, 'Payroll Country', NULL, 'Grade Code', 1, 0,'GH' ,NOW(), 1,'No');
-- UPDATE fields SET Form_Id = 20 WHERE Field_Id = 311;


-- UPDATE customization_fields SET Required = 0 WHERE Field_Id IN (144,145,147,148) AND Country_Code = 'IN';
-- UPDATE customization_fields SET Required = 1 WHERE Field_Id IN (115);

-- -- September 9 Soundar #9298

-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('285', 'Man Power Planning', '0', '14');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('286', 'Hiring Fore Cast', '285', '14');

-- UPDATE modules SET Module_Sequence = Module_Sequence + 1 WHERE Module_Id > 2;
-- INSERT INTO `modules` (`Module_Id`, `Module_Name`, `Module_Sequence`, `Is_Visible`) VALUES ('27', 'Man Power Planning', '3', '1');

-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('287', 'Hiring Forecast', '0', '27');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('288', 'Table of Organization', '0', '27');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('289', 'Job Requisition', '0', '27');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('290', 'New Position', '289', '27'); 
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('291', 'Recruitment request', '289', '27');


-- Pasha #9299 #9297
-- UPDATE `forms` SET `Sub_Form` = '283', `Module_Id` = '20' WHERE `forms`.`Form_Id` = 27;
-- -- Department
-- UPDATE `forms` SET `Sub_Form` = '283', `Module_Id` = '20' WHERE `forms`.`Form_Id` = 2;
-- -- Business Unit
-- UPDATE `forms` SET `Sub_Form` = '283', `Module_Id` = '20' WHERE `forms`.`Form_Id` = 251;
-- -- Organization Group
-- UPDATE `forms` SET `Sub_Form` = '283', `Module_Id` = '20' WHERE `forms`.`Form_Id` = 269;
-- -- Designation
-- UPDATE `forms` SET `Sub_Form` = '283', `Module_Id` = '20' WHERE `forms`.`Form_Id` = 21;
-- -- Work Schedule
-- UPDATE `forms` SET `Sub_Form` = '283', `Module_Id` = '20' WHERE `forms`.`Form_Id` = 221;

-- -- Abhishek #9255
-- UPDATE `forms` SET `Sub_Form` = '283', `Module_Id` = '20' WHERE `forms`.`Form_Id` = 20;

-- -- Pasha #9343
-- -- Role (Access Rights)
-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`, `Tab_Name`) VALUES ('312', 'Role (Access Rights)', '178', NULL);
-- INSERT INTO `customization_fields` (`Custom_Id`, `Field_Id`, `Customization_Applicable_For`, `Org_Code`, `Enable`, `Required`, `Country_Code`, `Predefined`, `New_Field_Name`, `Added_On`, `Added_By`, `Updated_On`, `Updated_By`) VALUES (NULL, '312', 'All Organization', NULL, '1', '1', NULL, 'No', 'Role (Access Rights)', '2024-09-12 00:00:00', '1', NULL, NULL);
-- INSERT INTO `customization_fields` (`Custom_Id`, `Field_Id`, `Customization_Applicable_For`, `Org_Code`, `Enable`, `Required`, `Country_Code`, `Predefined`, `New_Field_Name`, `Added_On`, `Added_By`, `Updated_On`, `Updated_By`) VALUES (NULL, '312', 'Payroll Country', NULL, '1', '0', 'PH', 'No', 'Role (Access Rights)', '2024-09-13 00:00:00', '1', NULL, NULL)

-- Migration Queries (Run on 20th September 2024)

-- -- Resignation, Exit management
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('292', 'Separation', '0', '24');
-- UPDATE `forms` SET `Form_Name` = 'Exit Management' WHERE `forms`.`Form_Id` = 34;
-- UPDATE `forms` SET `Sub_Form` = 243  WHERE `forms`.`Form_Id` = 292;
-- UPDATE `forms` SET `Module_Id` = 24 WHERE `forms`.`Form_Id` = 34;  
-- INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name) VALUES (313, 'Employee Remarks',84, NULL);

-- Pasha #9350
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('293', 'Shift Rotation', '163', '11');
-- -- suhan #9341
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('294', 'Location Tracking Dashboard', '211', '15');

-- -- suhan #9341
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('295', 'Location Insights',  '230', '14');

-- -- suhan #9341
-- -- update `forms` set Sub_Form = 0, Module_Id = 22 , Form_Name = 'Location Intelligence' where Form_Id =295;

-- -- update `forms` set Sub_Form = 0, Module_Id = 2 where Form_Id=2;

-- ALTER TABLE `hrapp_registeruser`  ADD `Enable_Roster_Routine` ENUM('Yes','No') NOT NULL DEFAULT 'No'  AFTER `Data_Migration`;

-- -- Pasha -  Department Code
-- INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name)
-- VALUES
-- (314, 'Department Code', 2, NULL);

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (314, 'Payroll Country', NULL, 'Department Code', 1, 1,'PH' ,NOW(), 1,'No');

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (314, 'Payroll Country', NULL, 'Department Code', 1, 0,'IN' ,NOW(), 1,'No');

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (314, 'Payroll Country', NULL, 'Department Code', 1, 0,'GH' ,NOW(), 1,'No');

-- -- Pasha - Employee Type Code
-- INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name)
-- VALUES
-- (315, 'Employee Type Code', 27, NULL);

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (315, 'Payroll Country', NULL, 'Employee Type Code', 1, 1,'PH' ,NOW(), 1,'No');

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (315, 'Payroll Country', NULL, 'Employee Type Code', 1, 0,'IN' ,NOW(), 1,'No');

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (315, 'Payroll Country', NULL, 'Employee Type Code', 1, 0,'GH' ,NOW(), 1,'No');

-- -- Shanthi
-- UPDATE modules SET Module_Sequence = 4 WHERE Module_Id = 3;

-- -- Pasha #9404
-- UPDATE `prerequisites` SET Form_Id = 237 where Form_Id = 21;
-- DELETE FROM `forms` WHERE `forms`.`Form_Id` = 21;
-- DELETE FROM `hrapp_plan_form` WHERE `hrapp_plan_form`.`Form_Id` = 21;
-- UPDATE `forms` SET `Sub_Form` = '283', `Module_Id` = '20' WHERE `Form_Id` = 237;

-- -- Soundar #9435
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('296', 'To Status Approval', '289', '27');
-- UPDATE forms SET Form_Name='Hiring Forecast' WHERE Form_Id='286';

-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('297', 'Talent Pool', '16', '3');

-- Pasha 8th Oct

-- -- Employees Form rename to My Profile
-- UPDATE `forms` SET `Form_Name` = 'My Profile', `Module_Id` = '25' WHERE `forms`.`Form_Id` = 18;

-- -- Update Module_Id to 25 for Sub Forms of 18
-- UPDATE `forms` SET `Module_Id` = '25' WHERE `forms`.`Sub_Form` = 18;

-- -- Separation Sub Form of My Profile
-- UPDATE `forms` SET `Sub_Form` = '18', `Module_Id` = '25' WHERE `forms`.`Form_Id` = 292;

-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('298', 'Background Investigation', '16', '3');

-- -- Organization Chart to Employee Self Service
-- UPDATE `forms` SET `Module_Id` = '25' WHERE `forms`.`Form_Id` = 187;
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('299', 'Key Logging', '230', '14');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('300', 'Additional Screenshots', '230', '14');

-- -- Pasha - Designation Code, Organization Group Code, Business Unit Code
-- INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name) VALUES (316, 'Designation Code', 237, NULL);
-- INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name) VALUES (317, 'Organization Group Code', 269, NULL);
-- INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name) VALUES (318, 'Business Unit Code', 251, NULL);

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (316, 'Payroll Country', NULL, 'Designation Code', 1, 1,'PH' ,NOW(), 1,'No'),
-- (317, 'Payroll Country', NULL, 'Organization Group Code', 1, 1,'PH' ,NOW(), 1,'No'),
-- (318, 'Payroll Country', NULL, 'Business Unit Code', 1, 1,'PH' ,NOW(), 1,'No');

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (316, 'Payroll Country', NULL, 'Designation Code', 1, 0,'IN' ,NOW(), 1,'No'),
-- (317, 'Payroll Country', NULL, 'Organization Group Code', 1, 0,'IN' ,NOW(), 1,'No'),
-- (318, 'Payroll Country', NULL, 'Business Unit Code', 1, 0,'IN' ,NOW(), 1,'No');

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (316, 'Payroll Country', NULL, 'Designation Code', 1, 0,'GH' ,NOW(), 1,'No'),
-- (317, 'Payroll Country', NULL, 'Organization Group Code', 1, 0,'GH' ,NOW(), 1,'No'),
-- (318, 'Payroll Country', NULL, 'Business Unit Code', 1, 0,'GH' ,NOW(), 1,'No');

-- UPDATE forms
-- SET Form_Name = "Settings", Module_Id = 27
-- WHERE Form_Id = 285;
-- delete from forms WHERE Form_Id = 286;
-- Pasha #9474

-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) 
-- VALUES ('301', 'On Duty (pre-approval)', '234', '14');

-- ALTER TABLE `data_integration_schedule` CHANGE `Report_Type` `Report_Type` ENUM('EMPSR','EMPUSR','EMCAUR','EMAR','HRAR','HREAR','HREUR','HRLR','HRRR','BID','MPP') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL;

-- INSERT INTO `data_integration_schedule`(`Org_Code`, `Storage_Platform`, `Data_Push_Pull`, `Report_Type`, `TRIGGER_TYPE`, `File_Format`, `Frequency`, `Upload_In_Root_Folder`, `Status`, `Added_By`, `Added_On`) VALUES ('cebprouat', 'AWS', 'Push', 'MPP', 2, 'xlsx', 'Daily', 'No', 'Open', 0, now())

-- -- Shanthi Oct 30
-- update forms set Form_Name = 'Approved & Forecasted Positions' where Form_Id=290;
-- update forms set Form_Name = 'New Position & Additional Headcount' where Form_Id=291;

-- -- Suhan Nov 04
-- UPDATE `customization_fields`
-- SET `Field_Id` = 322
-- WHERE `New_Field_Name` = 'Name Of Character Reference';
-- UPDATE `customization_fields`
-- SET `Field_Id` = 323
-- WHERE `New_Field_Name` = 'Character Reference Mobile Number';
-- UPDATE `customization_fields`
-- SET `Field_Id` = 324
-- WHERE `New_Field_Name` = 'Character Reference Email';
-- INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name) VALUES (325, 'Languages Known', 16, NULL);
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (325, 'Payroll Country', NULL, 'Languages Known', 1, 1,'PH' ,NOW(), 1,'Yes');
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (325, 'Payroll Country', NULL, 'Languages Known', 1, 1,'IN' ,NOW(), 1,'Yes');
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (325, 'Payroll Country', NULL, 'Languages Known', 1, 1,'GH' ,NOW(), 1,'Yes');

-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('302', 'Candidate Withdrawal', '178', '23');

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (326, 'Payroll Country', NULL, 'Employee Id Override', 1, 0, 'PH', NOW(), 1, 'No');
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (326, 'Payroll Country', NULL, 'Employee Id Override', 1, 0, 'IN', NOW(), 1, 'No');
-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`, `Predefined`)
-- VALUES
-- (326, 'Payroll Country', NULL, 'Employee Id Override', 1, 0, 'GH', NOW(), 1, 'No');

-- -- Nov 11 Shanthi 
-- update customization_fields set Field_Id = 188 where Field_Id=188 and New_Field_Name = 'Tax Code';

-- update customization_fields set Enable= 1 where Field_Id between 116 and 127;
-- update customization_fields set Required= 1 where Field_Id between 116 and 127 and Country_Code = 'PH';

-- update customization_fields set Enable= 1, Required = 1 where Field_Id IN (128,129,130,134);
-- update customization_fields set Enable= 1, Required = 0 where Field_Id in (133);
-- update customization_fields set Enable= 0, Required = 0 where Field_Id IN (131,132,156,157,158,159,160);

-- UPDATE forms SET Sub_Form=236, Module_Id=20 WHERE Form_Id=177;

-- -- Shanthi Nov14
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('303', 'Key Logger',  '230', '14');
-- update `forms` set Sub_Form = 0, Module_Id = 22 where Form_Id =303;
-- 30 Nov 2024 Deployment Queries
-- #9617 Adding attendance form in employee self service and my team module
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('304', 'Attendance',  '0', '24');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('305', 'Attendance',  '0', '25');

-- -- suhan #9647
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('306', 'Shift Swap', '0', '11');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('307', 'Approvals', '306', '11');

-- Pasha #9662

-- -- Update length for domain_settings
-- ALTER TABLE domain_settings 
--   MODIFY COLUMN Support_Email VARCHAR(320),
--   MODIFY COLUMN Sales_Email VARCHAR(320),
--   MODIFY COLUMN Org_Email VARCHAR(320);

-- -- Update length for hrapp_registeruser
-- ALTER TABLE hrapp_registeruser 
--   MODIFY COLUMN Email_Id VARCHAR(320);

-- -- Update length for hrapp_registeruser_archive
-- ALTER TABLE hrapp_registeruser_archive 
--   MODIFY COLUMN Email_Id VARCHAR(320);

-- -- Update length for hrapp_userdetails
-- ALTER TABLE hrapp_userdetails 
--   MODIFY COLUMN Email VARCHAR(320);

-- -- Update length for payment_details
-- ALTER TABLE payment_details 
--   MODIFY COLUMN billing_email VARCHAR(320);

-- -- Update length for paypal_transactions
-- ALTER TABLE paypal_transactions 
--   MODIFY COLUMN Email_Id VARCHAR(320);

-- -- Update length for subscription_payment_details
-- ALTER TABLE subscription_payment_details 
--   MODIFY COLUMN billing_email VARCHAR(320);


-- -- Pasha #9320
-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`, `Tab_Name`) VALUES ('327', 'Required Certification', '15', NULL);
-- INSERT INTO `customization_fields` (`Custom_Id`, `Field_Id`, `Customization_Applicable_For`, `Org_Code`, `Enable`, `Required`, `Country_Code`, `Predefined`, `New_Field_Name`, `Added_On`, `Added_By`, `Updated_On`, `Updated_By`) VALUES (NULL, '327', 'All Organization', NULL, '0', '0', NULL, 'No', 'Required Certification', NOW(), '1', NULL, NULL);

-- --Soundar Dec-16 #9704
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('308', 'My Integration', 0, '3');

-- -- Pasha #9719
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('309', 'Custom Fields', '201', '14');

-- -- suhan #9756
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('310', 'Email Templates', '201', '14');

-- -- Pasha #9612

-- -- Job Candidates Public Form
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('311', 'Career Site', '16', '3');

-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('312', 'Careers', '0', '3');

-- -- Job Candidates

-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`, `Tab_Name`) 
-- VALUES 
-- ('328', 'Nickname', '16', NULL),
-- ('329', 'Gender Identity', '16', NULL),
-- ('330', 'Gender Expression', '16', NULL),
-- ('331', 'Do you need special assistance?', '16', NULL),
-- ('332', 'Barangay', '16', NULL),
-- ('333', 'Region', '16', NULL),
-- ('334', 'Emergency Contact Name', '16', NULL),
-- ('335', 'Emergency Contact Number', '16', NULL),
-- ('336', 'Location', '16', NULL),
-- ('337', 'Ranking', '16', NULL);

-- INSERT INTO `customization_fields` 
-- (`Custom_Id`, `Field_Id`, `Customization_Applicable_For`, `Org_Code`, `Enable`, `Required`, `Country_Code`, `Predefined`, `New_Field_Name`, `Added_On`, `Added_By`, `Updated_On`, `Updated_By`) 
-- VALUES 
-- (NULL, '328', 'All Organization', NULL, '0', '0', NULL, 'No', 'Nickname', NOW(), '1', NULL, NULL),
-- (NULL, '329', 'All Organization', NULL, '0', '0', NULL, 'No', 'Gender Identity', NOW(), '1', NULL, NULL),
-- (NULL, '330', 'All Organization', NULL, '0', '0', NULL, 'No', 'Gender Expression', NOW(), '1', NULL, NULL),
-- (NULL, '331', 'All Organization', NULL, '0', '0', NULL, 'No', 'Do you need special assistance?', NOW(), '1', NULL, NULL),
-- (NULL, '332', 'All Organization', NULL, '0', '0', NULL, 'No', 'Barangay', NOW(), '1', NULL, NULL),
-- (NULL, '333', 'All Organization', NULL, '0', '0', NULL, 'No', 'Region', NOW(), '1', NULL, NULL),
-- (NULL, '334', 'All Organization', NULL, '0', '0', NULL, 'No', 'Emergency Contact Name', NOW(), '1', NULL, NULL),
-- (NULL, '335', 'All Organization', NULL, '0', '0', NULL, 'No', 'Emergency Contact Number', NOW(), '1', NULL, NULL),
-- (NULL, '336', 'All Organization', NULL, '0', '0', NULL, 'No', 'Location', NOW(), '1', NULL, NULL),
-- (NULL, '337', 'All Organization', NULL, '0', '0', NULL, 'No', 'Ranking', NOW(), '1', NULL, NULL);

-- -- Individuals

-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`, `Tab_Name`) 
-- VALUES 
-- ('338', 'Gender Identity', '178', NULL),
-- ('339', 'Gender Expression', '178', NULL),
-- ('340', 'Barangay', '178', NULL),
-- ('341', 'Region', '178', NULL),
-- ('342', 'Barangay', '178', NULL),
-- ('343', 'Region', '178', NULL),
-- ('344', 'Barangay', '178', NULL),
-- ('345', 'Region', '178', NULL),
-- ('346', 'Account Name', '178', NULL);

-- INSERT INTO `customization_fields` 
-- (`Custom_Id`, `Field_Id`, `Customization_Applicable_For`, `Org_Code`, `Enable`, `Required`, `Country_Code`, `Predefined`, `New_Field_Name`, `Added_On`, `Added_By`, `Updated_On`, `Updated_By`) 
-- VALUES 
-- (NULL, '338', 'All Organization', NULL, '0', '0', NULL, 'No', 'Gender Identity', NOW(), '1', NULL, NULL),
-- (NULL, '339', 'All Organization', NULL, '0', '0', NULL, 'No', 'Gender Expression', NOW(), '1', NULL, NULL),
-- (NULL, '340', 'All Organization', NULL, '0', '0', NULL, 'No', 'Barangay', NOW(), '1', NULL, NULL),
-- (NULL, '341', 'All Organization', NULL, '0', '0', NULL, 'No', 'Region', NOW(), '1', NULL, NULL),
-- (NULL, '342', 'All Organization', NULL, '0', '0', NULL, 'No', 'Barangay', NOW(), '1', NULL, NULL),
-- (NULL, '343', 'All Organization', NULL, '0', '0', NULL, 'No', 'Region', NOW(), '1', NULL, NULL),
-- (NULL, '344', 'All Organization', NULL, '0', '0', NULL, 'No', 'Barangay', NOW(), '1', NULL, NULL),
-- (NULL, '345', 'All Organization', NULL, '0', '0', NULL, 'No', 'Region', NOW(), '1', NULL, NULL),
-- (NULL, '346', 'All Organization', NULL, '0', '0', NULL, 'No', 'Account Name', NOW(), '1', NULL, NULL);

-- -- Team Summary

-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`, `Tab_Name`) 
-- VALUES 
-- ('347', 'Gender Identity', '243', NULL),
-- ('348', 'Gender Expression', '243', NULL),
-- ('349', 'Barangay', '243', NULL),
-- ('350', 'Region', '243', NULL),
-- ('351', 'Barangay', '243', NULL),
-- ('352', 'Region', '243', NULL),
-- ('353', 'Barangay', '243', NULL),
-- ('354', 'Region', '243', NULL),
-- ('355', 'Account Name', '243', NULL);

-- INSERT INTO `customization_fields` 
-- (`Custom_Id`, `Field_Id`, `Customization_Applicable_For`, `Org_Code`, `Enable`, `Required`, `Country_Code`, `Predefined`, `New_Field_Name`, `Added_On`, `Added_By`, `Updated_On`, `Updated_By`) 
-- VALUES 
-- (NULL, '347', 'All Organization', NULL, '0', '0', NULL, 'No', 'Gender Identity', NOW(), '1', NULL, NULL),
-- (NULL, '348', 'All Organization', NULL, '0', '0', NULL, 'No', 'Gender Expression', NOW(), '1', NULL, NULL),
-- (NULL, '349', 'All Organization', NULL, '0', '0', NULL, 'No', 'Barangay', NOW(), '1', NULL, NULL),
-- (NULL, '350', 'All Organization', NULL, '0', '0', NULL, 'No', 'Region', NOW(), '1', NULL, NULL),
-- (NULL, '351', 'All Organization', NULL, '0', '0', NULL, 'No', 'Barangay', NOW(), '1', NULL, NULL),
-- (NULL, '352', 'All Organization', NULL, '0', '0', NULL, 'No', 'Region', NOW(), '1', NULL, NULL),
-- (NULL, '353', 'All Organization', NULL, '0', '0', NULL, 'No', 'Barangay', NOW(), '1', NULL, NULL),
-- (NULL, '354', 'All Organization', NULL, '0', '0', NULL, 'No', 'Region', NOW(), '1', NULL, NULL),
-- (NULL, '355', 'All Organization', NULL, '0', '0', NULL, 'No', 'Account Name', NOW(), '1', NULL, NULL);

-- -- Missed Fields

-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`, `Tab_Name`) 
-- VALUES 
--     ('356', 'Language Spoken', '16', NULL), 
--     ('357', 'Language Read/Write', '16', NULL), 
--     ('358', 'Language Proficiency', '16', NULL), 
--     ('359', 'Emergency Contact Relation', '16', NULL), 
--     ('360', 'Reference Name', '16', NULL), 
--     ('361', 'Reference Email', '16', NULL), 
--     ('362', 'Reference Number', '16', NULL), 

--     ('363', 'Language Spoken', '178', NULL), 
--     ('364', 'Language Read/Write', '178', NULL), 
--     ('365', 'Language Proficiency', '178', NULL), 
--     ('366', 'Reference Name', '178', NULL), 
--     ('367', 'Reference Email', '178', NULL), 
--     ('368', 'Reference Number', '178', NULL), 

--     ('369', 'Language Spoken', '243', NULL), 
--     ('370', 'Language Read/Write', '243', NULL), 
--     ('371', 'Language Proficiency', '243', NULL), 
--     ('372', 'Reference Name', '243', NULL), 
--     ('373', 'Reference Email', '243', NULL), 
--     ('374', 'Reference Number', '243', NULL),
--     ('375', 'Language', '243', NULL);

-- INSERT INTO `customization_fields` 
-- (`Custom_Id`, `Field_Id`, `Customization_Applicable_For`, `Org_Code`, `Enable`, `Required`, `Country_Code`, `Predefined`, `New_Field_Name`, `Added_On`, `Added_By`, `Updated_On`, `Updated_By`) 
-- VALUES 
--     (NULL, '356', 'All Organization', NULL, '0', '0', NULL, 'No', 'Language Spoken', NOW(), '1', NULL, NULL),
--     (NULL, '357', 'All Organization', NULL, '0', '0', NULL, 'No', 'Language Read/Write', NOW(), '1', NULL, NULL),
--     (NULL, '358', 'All Organization', NULL, '0', '0', NULL, 'No', 'Language Proficiency', NOW(), '1', NULL, NULL),
--     (NULL, '359', 'All Organization', NULL, '0', '0', NULL, 'No', 'Emergency Contact Relation', NOW(), '1', NULL, NULL),
--     (NULL, '360', 'All Organization', NULL, '0', '0', NULL, 'No', 'Reference Name', NOW(), '1', NULL, NULL),
--     (NULL, '361', 'All Organization', NULL, '0', '0', NULL, 'No', 'Reference Email', NOW(), '1', NULL, NULL),
--     (NULL, '362', 'All Organization', NULL, '0', '0', NULL, 'No', 'Reference Number', NOW(), '1', NULL, NULL),

--     (NULL, '363', 'All Organization', NULL, '0', '0', NULL, 'No', 'Language Spoken', NOW(), '1', NULL, NULL),
--     (NULL, '364', 'All Organization', NULL, '0', '0', NULL, 'No', 'Language Read/Write', NOW(), '1', NULL, NULL),
--     (NULL, '365', 'All Organization', NULL, '0', '0', NULL, 'No', 'Language Proficiency', NOW(), '1', NULL, NULL),
--     (NULL, '366', 'All Organization', NULL, '0', '0', NULL, 'No', 'Reference Name', NOW(), '1', NULL, NULL),
--     (NULL, '367', 'All Organization', NULL, '0', '0', NULL, 'No', 'Reference Email', NOW(), '1', NULL, NULL),
--     (NULL, '368', 'All Organization', NULL, '0', '0', NULL, 'No', 'Reference Number', NOW(), '1', NULL, NULL),

--     (NULL, '369', 'All Organization', NULL, '0', '0', NULL, 'No', 'Language Spoken', NOW(), '1', NULL, NULL),
--     (NULL, '370', 'All Organization', NULL, '0', '0', NULL, 'No', 'Language Read/Write', NOW(), '1', NULL, NULL),
--     (NULL, '371', 'All Organization', NULL, '0', '0', NULL, 'No', 'Language Proficiency', NOW(), '1', NULL, NULL),
--     (NULL, '372', 'All Organization', NULL, '0', '0', NULL, 'No', 'Reference Name', NOW(), '1', NULL, NULL),
--     (NULL, '373', 'All Organization', NULL, '0', '0', NULL, 'No', 'Reference Email', NOW(), '1', NULL, NULL),
--     (NULL, '374', 'All Organization', NULL, '0', '0', NULL, 'No', 'Reference Number', NOW(), '1', NULL, NULL),
--     (NULL, '375', 'Language', NULL, '0', '0', NULL, 'No', 'Reference Number', NOW(), '1', NULL, NULL);

-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`, `Tab_Name`) 
-- VALUES 
--     ('376', 'Language', '178', NULL),
--     ('377', 'Ranking', '178', NULL),
--     ('378', 'Do you identify as a person with a disability?', '178', NULL);

-- INSERT INTO `customization_fields` 
-- (`Custom_Id`, `Field_Id`, `Customization_Applicable_For`, `Org_Code`, `Enable`, `Required`, `Country_Code`, `Predefined`, `New_Field_Name`, `Added_On`, `Added_By`, `Updated_On`, `Updated_By`) 
-- VALUES 
--     (NULL, '376', 'All Organization', NULL, '0', '0', NULL, 'No', 'Language', NOW(), '1', NULL, NULL),
--     (NULL, '377', 'All Organization', NULL, '0', '0', NULL, 'No', 'Ranking', NOW(), '1', NULL, NULL),
--     (NULL, '378', 'All Organization', NULL, '0', '0', NULL, 'No', 'Do you identify as a person with a disability?', NOW(), '1', NULL, NULL);

-- -- #9774 shanthi 
-- ALTER TABLE `data_integration_schedule` CHANGE `Report_Type` `Report_Type` ENUM('EMPSR','EMPUSR','EMCAUR','EMAR','HRAR','HREAR','HREUR','HRLR','HRRR','BID','MPP','POSMASTER') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL;
-- INSERT INTO `data_integration_schedule`(`Org_Code`, `Storage_Platform`, `Data_Push_Pull`, `Report_Type`, `TRIGGER_TYPE`, `File_Format`, `Frequency`, `Upload_In_Root_Folder`, `Status`, `Added_By`, `Added_On`) VALUES ('cebpro', 'AWS', 'Pull', 'POSMASTER', 2, 'xlsx', 'Daily', 'No', 'Open', 0, now());

-- -- Pasha #9612
-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`, `Tab_Name`) VALUES 
-- ('379', 'Languages Known', '178', NULL), 
-- ('380', 'Languages Known', '243', NULL), 
-- ('381', 'Ranking', '243', NULL), 
-- ('382', 'Do you identify as a person with a disability', '243', NULL),
-- ('383', 'Language', '16', NULL),
-- ('384', 'Business Unit', '178', NULL),
-- ('385', 'Business Unit', '243', NULL);

-- INSERT INTO `customization_fields` (`Custom_Id`, `Field_Id`, `Customization_Applicable_For`, `Org_Code`, `Enable`, `Required`, `Country_Code`, `Predefined`, `New_Field_Name`, `Added_On`, `Added_By`, `Updated_On`, `Updated_By`) 
-- VALUES 
-- (NULL, '379', 'All Organization', NULL, '1', '0', NULL, 'No', 'Languages Known', NOW(), '1', NULL, NULL),
-- (NULL, '380', 'All Organization', NULL, '1', '0', NULL, 'No', 'Languages Known', NOW(), '1', NULL, NULL),
-- (NULL, '381', 'All Organization', NULL, '0', '0', NULL, 'No', 'Ranking', NOW(), '1', NULL, NULL),
-- (NULL, '382', 'All Organization', NULL, '1', '0', NULL, 'No', 'Do you identify as a person with a disability?', NOW(), '1', NULL, NULL),
-- (NULL, '383', 'All Organization', NULL, '0', '0', NULL, 'No', 'Language', NOW(), '1', NULL, NULL),
-- (NULL, '384', 'All Organization', NULL, '1', '0', NULL, 'No', 'Business Unit', NOW(), '1', NULL, NULL),
-- (NULL, '385', 'All Organization', NULL, '1', '0', NULL, 'No', 'Business Unit', NOW(), '1', NULL, NULL);

-- -- 9514 Adding attendance approval form in my team module
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('313', 'Attendance Approval', '304', '24');

-- INSERT INTO `customization_fields` (`Custom_Id`, `Field_Id`, `Customization_Applicable_For`, `Org_Code`, `Enable`, `Required`, `Country_Code`, `Predefined`, `New_Field_Name`, `Added_On`, `Added_By`, `Updated_On`, `Updated_By`) 
-- VALUES 
-- (NULL, '386', 'All Organization', NULL, '1', '0', NULL, 'No', 'Identifier', NOW(), '1', NULL, NULL),
-- (NULL, '387', 'All Organization', NULL, '0', '0', NULL, 'No', 'Exam Rating', NOW(), '1', NULL, NULL),
-- (NULL, '388', 'All Organization', NULL, '0', '0', NULL, 'No', 'Exam Date Year', NOW(), '1', NULL, NULL),
-- (NULL, '389', 'All Organization', NULL, '0', '0', NULL, 'No', 'Exam Date Month', NOW(), '1', NULL, NULL),
-- (NULL, '390', 'All Organization', NULL, '1', '0', NULL, 'No', 'Identifier', NOW(), '1', NULL, NULL),
-- (NULL, '391', 'All Organization', NULL, '0', '0', NULL, 'No', 'Exam Rating', NOW(), '1', NULL, NULL),
-- (NULL, '392', 'All Organization', NULL, '0', '0', NULL, 'No', 'Exam Date Year', NOW(), '1', NULL, NULL),
-- (NULL, '393', 'All Organization', NULL, '0', '0', NULL, 'No', 'Exam Date Month', NOW(), '1', NULL, NULL);

-- -- Pasha

-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`, `Tab_Name`) VALUES ('394', 'Department', '231', NULL), ('395', 'Account Manager', '231', NULL);
-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`, `Tab_Name`) VALUES ('396', 'Industry Type', '231', 'Invited_Vendor'), ('397', 'Service Priority', '231', 'Invited_Vendor');

-- INSERT INTO `customization_fields` 
-- (`Custom_Id`, `Field_Id`, `Customization_Applicable_For`, `Org_Code`, `Enable`, `Required`, `Country_Code`, `Predefined`, `New_Field_Name`, `Added_On`, `Added_By`, `Updated_On`, `Updated_By`) 
-- VALUES 
-- (NULL, '394', 'All Organization', NULL, '0', '0', NULL, NULL, 'Department', NOW(), '1', NULL, NULL),
-- (NULL, '395', 'All Organization', NULL, '0', '0', NULL, NULL, 'Account Manager', NOW(), '1', NULL, NULL),
-- (NULL, '396', 'All Organization', NULL, '0', '0', NULL, NULL, 'Industry Type', NOW(), '1', NULL, NULL),
-- (NULL, '397', 'All Organization', NULL, '0', '0', NULL, NULL, 'Service Priority', NOW(), '1', NULL, NULL);

-- -- soundar
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('314', 'Payroll management', 0, '5');

-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('304', 'Attendance',  '0', '24');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('305', 'Attendance',  '0', '25');

-- -- 9514 Adding attendance approval form in my team module
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('313', 'Attendance Approval', '304', '24');

-- -- Pasha #9612

-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`, `Tab_Name`) 
-- VALUES ('398', 'Marital Status', '178', NULL), 
-- ('399', 'Marital Status', '243', NULL), 
-- ('400', 'Blood Group', '178', NULL), 
-- ('401', 'Blood Group', '243', NULL), 
-- ('402', 'State', '178', NULL), 
-- ('403', 'State', '243', NULL);

-- INSERT INTO `customization_fields` 
--     (`Custom_Id`, `Field_Id`, `Customization_Applicable_For`, `Org_Code`, `Enable`, `Required`, `Country_Code`, `Predefined`, `New_Field_Name`, `Added_On`, `Added_By`, `Updated_On`, `Updated_By`) 
-- VALUES
--     (NULL, '398', 'All Organization', NULL, '1', '1', NULL, NULL, 'Marital Status', NOW(), '1', NULL, NULL),
--     (NULL, '399', 'All Organization', NULL, '1', '1', NULL, NULL, 'Marital Status', NOW(), '1', NULL, NULL),
--     (NULL, '400', 'All Organization', NULL, '1', '1', NULL, NULL, 'Blood Group', NOW(), '1', NULL, NULL),
--     (NULL, '401', 'All Organization', NULL, '1', '1', NULL, NULL, 'Blood Group', NOW(), '1', NULL, NULL),
--     (NULL, '402', 'All Organization', NULL, '1', '1', NULL, NULL, 'State', NOW(), '1', NULL, NULL),
--     (NULL, '403', 'All Organization', NULL, '1', '1', NULL, NULL, 'State', NOW(), '1', NULL, NULL);

-- -- Pasha #9890
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('315', 'Electronic Fund Transfer', '0', '5');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('316', 'Payroll Reconciliation', '0', '5');

-- -- suganya - #9908
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('317', 'Salary Review', '38', '5');
-- -- suhan #9922
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('318', 'Payroll Data Management', 0, '20');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('319', 'Air Ticketing Policy', '318', '20');

-- -- suhan#9617
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('320', 'Attendance Entries', '304', '24');

-- -- suhan #9943
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('321', 'Air Ticket Settlement Summary', '318', '20');

-- -- suhan #9943
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('322', 'Air Ticketing Policy', '243', '24');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('323', 'Air Ticketing Policy', '18', '25');
-- suganya - #9708
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('324', 'Early Checkout', '29', '4');

-- Pasha #9964
-- ALTER TABLE `data_integration_schedule` ADD `Email_TO` JSON NULL DEFAULT NULL AFTER `Upload_In_Root_Folder`, ADD `Email_CC` JSON NULL DEFAULT NULL AFTER `Email_TO`, ADD `Email_BCC` JSON NULL DEFAULT NULL AFTER `Email_CC`;

-- FEB 22 Deployment Queries
-- Shanthi #9979
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('325', 'Dashboard', '0', '3');

-- Soundar Feb 17 2025 #9650
-- ALTER TABLE hrapp_registeruser ADD COLUMN Scheduler_Integration_Type ENUM('pagt') NULL AFTER Partner_Integration;

-- 20Feb2025 Deployment Queries
-- suhan #9992

-- INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name)
-- VALUES
-- (404, 'Eligible For Teacher Provident Fund', 263, NULL),
-- (405, 'TPF Number', 263, NULL),
-- (406, 'TPF Type', 263, NULL),
-- (407, 'TPF Employee Share Amount', 263, NULL),
-- (408, 'TPF Employee Share', 263, NULL),
-- (409, 'Eligible For Provident Fund', 263, NULL),
-- (410, 'CPS Number', 263, NULL),
-- (411, 'CPS Type', 263, NULL),
-- (412, 'CPS Employee Share Amount', 263, NULL),
-- (413, 'CPS Employer Share Amount', 263, NULL),
-- (414, 'CPS Employee Share', 263, NULL),
-- (415, 'CPS Employer Share', 263, NULL),
-- (416, 'Eligible For Special Provident Fund', 263, NULL),
-- (417, 'SPF Number', 263, NULL),
-- (418, 'SPF Employee Share', 263, NULL),
-- (419, 'SPF End Month', 263, NULL);

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (404, 'Payroll Country', NULL, 'Eligible For Teacher Provident Fund', 0, 0, 'TH', 'No', NOW(), 0),
-- (405 ,'Payroll Country', NULL, 'TPF Number', 0, 0, 'TH', 'No', NOW(), 0),
-- (406, 'Payroll Country', NULL, 'TPF Type', 0, 0, 'TH', 'Yes', NOW(), 1),
-- (407, 'Payroll Country', NULL, 'TPF Employee Share Amount', 0, 0, 'TH', 'No', NOW(), 0),
-- (408, 'Payroll Country', NULL, 'TPF Employee Share', 0, 0, 'TH', 'No', NOW(), 0),
-- (409, 'Payroll Country', NULL, 'Eligible For Provident Fund', 1, 1, 'TH', 'No', NOW(), 0),
-- (410, 'Payroll Country', NULL, 'Provident Fund Number', 1, 0, 'TH', 'No', NOW(), 0),
-- (411, 'Payroll Country', NULL, 'Provident Fund Type', 1, 0, 'TH', 'Yes', NOW(), 1),
-- (412, 'Payroll Country', NULL, 'Provident Fund Employee Share Amount', 1, 0, 'TH', 'No', NOW(), 0),
-- (413, 'Payroll Country', NULL, 'Provident Fund Employer Share Amount', 1, 0, 'TH', 'No', NOW(), 0),
-- (414, 'Payroll Country', NULL, 'Provident Fund Employee Share', 1, 0, 'TH', 'No', NOW(), 0),
-- (415, 'Payroll Country', NULL, 'Provident Fund Employer Share', 1, 0, 'TH', 'No', NOW(), 0),
-- (416, 'Payroll Country', NULL, 'Eligible For Special Provident Fund', 0, 0, 'TH', 'No', NOW(), 0),
-- (417, 'Payroll Country', NULL, 'SPF Number', 0, 0, 'TH', 'No', NOW(), 0),
-- (418, 'Payroll Country', NULL, 'SPF Employee Share', 0, 0, 'TH', 'No', NOW(), 0),
-- (419, 'Payroll Country', NULL, 'SPF End Month', 0, 0, 'TH', 'No', NOW(), 0);

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (404, 'Payroll Country', NULL, 'Eligible For Teacher Provident Fund', 0, 1, 'IN', 'No', NOW(), 0),
-- (405 ,'Payroll Country', NULL, 'TPF Number', 0, 0, 'IN', 'No', NOW(), 0),
-- (406, 'Payroll Country', NULL, 'TPF Type', 0, 0, 'IN', 'Yes', NOW(), 1),
-- (407, 'Payroll Country', NULL, 'TPF Employee Share Amount', 0, 0, 'IN', 'No', NOW(), 0),
-- (408, 'Payroll Country', NULL, 'TPF Employee Share', 0, 0, 'IN', 'No', NOW(), 0),
-- (409, 'Payroll Country', NULL, 'Eligible For Contribution Pension Scheme', 0, 1, 'IN', 'No', NOW(), 0),
-- (410, 'Payroll Country', NULL, 'CPS Number', 0, 0, 'IN', 'No', NOW(), 0),
-- (411, 'Payroll Country', NULL, 'CPS Type', 0, 0, 'IN', 'Yes', NOW(), 1),
-- (412, 'Payroll Country', NULL, 'CPS Employee Share Amount', 0, 0, 'IN', 'No', NOW(), 0),
-- (413, 'Payroll Country', NULL, 'CPS Employer Share Amount', 0, 0, 'IN', 'No', NOW(), 0),
-- (414, 'Payroll Country', NULL, 'CPS Employee Share', 0, 0, 'IN', 'No', NOW(), 0),
-- (415, 'Payroll Country', NULL, 'CPS Employer Share', 0, 0, 'IN', 'No', NOW(), 0),
-- (416, 'Payroll Country', NULL, 'Eligible For Special Provident Fund', 0, 1, 'IN', 'No', NOW(), 0),
-- (417, 'Payroll Country', NULL, 'SPF Number', 0, 0, 'IN', 'No', NOW(), 0),
-- (418, 'Payroll Country', NULL, 'SPF Employee Share', 0, 0, 'IN', 'No', NOW(), 0),
-- (419, 'Payroll Country', NULL, 'SPF End Month', 0, 0, 'IN', 'No', NOW(), 0);

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (404, 'Payroll Country', NULL, 'Eligible For Teacher Provident Fund', 0, 0, 'PH', 'No', NOW(), 0),
-- (405, 'Payroll Country', NULL, 'TPF Number', 0, 0, 'PH', 'No', NOW(), 0),
-- (406, 'Payroll Country', NULL, 'TPF Type', 0, 0, 'PH', 'Yes', NOW(), 1),
-- (407, 'Payroll Country', NULL, 'TPF Employee Share Amount', 0, 0, 'PH', 'No', NOW(), 0),
-- (408, 'Payroll Country', NULL, 'TPF Employee Share', 0, 0, 'PH', 'No', NOW(), 0),
-- (409, 'Payroll Country', NULL, 'Eligible For Contribution Pension Scheme', 0, 0, 'PH', 'No', NOW(), 0),
-- (410, 'Payroll Country', NULL, 'CPS Number', 0, 0, 'PH', 'No', NOW(), 0),
-- (411, 'Payroll Country', NULL, 'CPS Type', 0, 0, 'PH', 'Yes', NOW(), 1),
-- (412, 'Payroll Country', NULL, 'CPS Employee Share Amount', 0, 0, 'PH', 'No', NOW(), 0),
-- (413, 'Payroll Country', NULL, 'CPS Employer Share Amount', 0, 0, 'PH', 'No', NOW(), 0),
-- (414, 'Payroll Country', NULL, 'CPS Employee Share', 0, 0, 'PH', 'No', NOW(), 0),
-- (415, 'Payroll Country', NULL, 'CPS Employer Share', 0, 0, 'PH', 'No', NOW(), 0),
-- (416, 'Payroll Country', NULL, 'Eligible For Special Provident Fund', 0, 0, 'PH', 'No', NOW(), 0),
-- (417, 'Payroll Country', NULL, 'SPF Number', 0, 0, 'PH', 'No', NOW(), 0),
-- (418, 'Payroll Country', NULL, 'SPF Employee Share', 0, 0, 'PH', 'No', NOW(), 0),
-- (419, 'Payroll Country', NULL, 'SPF End Month', 0, 0, 'PH', 'No', NOW(), 0);

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (404, 'Payroll Country', NULL, 'Eligible For Teacher Provident Fund', 0, 0, 'ID', 'No', NOW(), 0),
-- (405, 'Payroll Country', NULL, 'TPF Number', 0, 0, 'ID', 'No', NOW(), 0),
-- (406, 'Payroll Country', NULL, 'TPF Type', 0, 0, 'ID', 'Yes', NOW(), 1),
-- (407, 'Payroll Country', NULL, 'TPF Employee Share Amount', 0, 0, 'ID', 'No', NOW(), 0),
-- (408, 'Payroll Country', NULL, 'TPF Employee Share', 0, 0, 'ID', 'No', NOW(), 0),
-- (409, 'Payroll Country', NULL, 'Eligible For Contribution Pension Scheme', 0, 0, 'ID', 'No', NOW(), 0),
-- (410, 'Payroll Country', NULL, 'CPS Number', 0, 0, 'ID', 'No', NOW(), 0),
-- (411, 'Payroll Country', NULL, 'CPS Type', 0, 0, 'ID', 'Yes', NOW(), 1),
-- (412, 'Payroll Country', NULL, 'CPS Employee Share Amount', 0, 0, 'ID', 'No', NOW(), 0),
-- (413, 'Payroll Country', NULL, 'CPS Employer Share Amount', 0, 0, 'ID', 'No', NOW(), 0),
-- (414, 'Payroll Country', NULL, 'CPS Employee Share', 0, 0, 'ID', 'No', NOW(), 0),
-- (415, 'Payroll Country', NULL, 'CPS Employer Share', 0, 0, 'ID', 'No', NOW(), 0),
-- (416, 'Payroll Country', NULL, 'Eligible For Special Provident Fund', 0, 0, 'ID', 'No', NOW(), 0),
-- (417, 'Payroll Country', NULL, 'SPF Number', 0, 0, 'ID', 'No', NOW(), 0),
-- (418, 'Payroll Country', NULL, 'SPF Employee Share', 0, 0, 'ID', 'No', NOW(), 0),
-- (419, 'Payroll Country', NULL, 'SPF End Month', 0, 0, 'ID', 'No', NOW(), 0);


-- -- SURESH #9990
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('326', 'Teacher Provident Fund', '0', '5');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('327', 'Contributory Pension Scheme', '0', '5');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('328', 'Special Provident Fund', '0', '5');

-- INSERT INTO `section_investment_category` (`Investment_Cat_Id`, `Tax_Section_ID`, `Investment_Category`, `Child_Investment_Category_Id`, `Pair_Configuration_Id`, `Investment_Percentage`, `Investment_Contribution`, `Allowance_Type_Id`, `Max_Limit`, `Assessment_Year`, `Description`, `Form_Id`, `Auto_Declaration`, `Document_Required`, `Added_On`, `Updated_On`, `Added_By`, `Updated_By`, `Lock_Flag`, `Tax_Status`)
-- select NULL,Tax_Section_ID,'TPF', NULL, NULL, NULL, '1.00', NULL, '150000.00',Assessment_Year, '', '326', '1', '0',NOW(), NULL,Added_By, NULL, '0', 'Active' FROM `tax_section` where Section_Id=4 AND Tax_Status='Active';


-- INSERT INTO `section_investment_category` (`Investment_Cat_Id`, `Tax_Section_ID`, `Investment_Category`, `Child_Investment_Category_Id`, `Pair_Configuration_Id`, `Investment_Percentage`, `Investment_Contribution`, `Allowance_Type_Id`, `Max_Limit`, `Assessment_Year`, `Description`, `Form_Id`, `Auto_Declaration`, `Document_Required`, `Added_On`, `Updated_On`, `Added_By`, `Updated_By`, `Lock_Flag`, `Tax_Status`)
-- select NULL,Tax_Section_ID,'CPS', NULL, NULL, NULL, '1.00', NULL, '150000.00',Assessment_Year, '', '327', '1', '0',NOW(), NULL, Added_By, NULL, '0', 'Active' FROM `tax_section` where Section_Id=4 AND Tax_Status='Active';


-- INSERT INTO `section_investment_category` (`Investment_Cat_Id`, `Tax_Section_ID`, `Investment_Category`, `Child_Investment_Category_Id`, `Pair_Configuration_Id`, `Investment_Percentage`, `Investment_Contribution`, `Allowance_Type_Id`, `Max_Limit`, `Assessment_Year`, `Description`, `Form_Id`, `Auto_Declaration`, `Document_Required`, `Added_On`, `Updated_On`, `Added_By`, `Updated_By`, `Lock_Flag`, `Tax_Status`)
-- select NULL,Tax_Section_ID,'SPF', NULL, NULL, NULL, '1.00', NULL, '150000.00',Assessment_Year, '', '328', '1', '0',NOW(), NULL,Added_By, NULL, '0', 'Active' FROM `tax_section` where Section_Id=4 AND Tax_Status='Active';

-- 20Feb2025 Deployment Queries

-- -- SURESH #9990
-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`) VALUES
-- (420, 'Teacher Provident Fund',38),
-- (421, 'Contributory Pension Scheme',38),
-- (422, 'Org Contributory Pension Scheme',38),
-- (423, 'Special Provident Fund',38);

-- -- Pasha #10031

-- -- Job Roles Form
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('329', 'Job Roles', '283', '20');

-- -- Fields
-- INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name) VALUES
-- (424, 'Job Roles', 243, NULL),
-- (425, 'Job Roles', 178, NULL);

-- -- Customization Fields
-- INSERT INTO `customization_fields` (`Custom_Id`, `Field_Id`, `Customization_Applicable_For`, `Org_Code`, `Enable`, `Required`, `Country_Code`, `Predefined`, `New_Field_Name`, `Added_On`, `Added_By`, `Updated_On`, `Updated_By`)
-- VALUES
-- (NULL, 424, 'All Organization', NULL, '0', '0', NULL, 'No', 'Job Roles', NOW(), '1', NULL, NULL),
-- (NULL, 425, 'All Organization', NULL, '0', '0', NULL, 'No', 'Job Roles', NOW(), '1', NULL, NULL);


-- -- March 08 Deployment Queries 
-- -- Soundar Feb 25 2025 #9650
-- CREATE TABLE `api_integration_schedule` (
-- `Schedule_Id` INT(11) NOT NULL AUTO_INCREMENT, 
-- `Org_Code` VARCHAR(30) NOT NULL, 
-- `Data_Push_Pull` ENUM('Push', 'Pull') NOT NULL DEFAULT 'Pull', 
-- `Frequency` ENUM('Daily', 'Weekly', 'Monthly') NOT NULL DEFAULT 'Daily',
-- `Integration_Type` ENUM('pagt') NOT NULL,
-- `Trigger_Type` VARCHAR(30) NOT NULL,  
-- `Email_TO` LONGTEXT NULL, 
-- `Email_CC` LONGTEXT NULL, 
-- `Email_BCC` LONGTEXT NULL, 
-- `Status` ENUM('Open', 'InProgress', 'Success', 'Failure') NOT NULL DEFAULT 'Open', 
-- `Updated_On` DATETIME NULL DEFAULT NULL, 
-- PRIMARY KEY (`Schedule_Id`)) ENGINE = InnoDB;

-- -- Pasha - Job Role Code #10031
-- INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name) VALUES
-- (426, 'Job Role Code', 329, NULL);

-- -- Customization Fields
-- INSERT INTO `customization_fields` (`Custom_Id`, `Field_Id`, `Customization_Applicable_For`, `Org_Code`, `Enable`, `Required`, `Country_Code`, `Predefined`, `New_Field_Name`, `Added_On`, `Added_By`, `Updated_On`, `Updated_By`)
-- VALUES
-- (NULL, 426, 'All Organization', NULL, '0', '0', NULL, 'No', 'Job Role Code', NOW(), '1', NULL, NULL);

-- -- Pasha #10056
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES 
-- ('330', 'Time Off', '0', '24'), 
-- ('331', 'Time Off', '0', '25'), 
-- ('332', 'Leave Request', '330', '24'), 
-- ('333', 'Leave Request', '331', '25'),
-- ('334', 'Compensatory Off Request', '330', '24'), 
-- ('335', 'Compensatory Off Request', '331', '25');

-- -- Sanket #10059
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) 
-- VALUES 
-- ('336', 'My Finances', '0', '24'),
-- ('337', 'Payroll Management', '0', '25'),
-- ('338', 'Expanse Claim', '336', '24'),
-- ('339', 'Expanse Claim', '337', '25');

-- -- Sanket #10059
-- UPDATE `forms` SET `Form_Name` = 'Payroll Management' WHERE `forms`.`Form_Id` = 336;
-- UPDATE `forms` SET `Form_Name` = 'My Finance' WHERE `forms`.`Form_Id` = 337;
-- UPDATE `forms` SET `Form_Name` = 'Reimbursement' WHERE `forms`.`Form_Id` = 338;
-- UPDATE `forms` SET `Form_Name` = 'Reimbursement' WHERE `forms`.`Form_Id` = 339;

-- -- suhan#10074
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('340', 'Travel & Expenses', '0', '24');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('341', 'Travel Request', '340', '24');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('342', 'Claim Request', '340', '24');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('343', 'Approval', '340', '24');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('344', 'Travel Request', '337', '25');

-- -- suhan#10074
-- UPDATE modules SET Module_Sequence = Module_Sequence + 1 WHERE Module_Sequence BETWEEN 7 AND 26;
-- INSERT INTO `modules` (`Module_Id`, `Module_Name`, `Module_Sequence`, `Is_Visible`) VALUES ('28', ' My Finance', '7', '1');
-- DELETE FROM `forms` WHERE `forms`.`Form_Id` IN ('343','342','337');
-- UPDATE `forms` SET `Module_Id` = '28', `Sub_Form` = '0',`Form_Id` = '342' WHERE `Form_Id` = '344';
-- UPDATE `forms` SET `Module_Id` = '28', `Sub_Form` = '0', `Form_Name` = "Claim Request" WHERE `Form_Id` = '339';
-- UPDATE `forms` SET `Sub_Form` = '340', `Form_Name` = "Claim Request" WHERE `Form_Id` = '338';

-- UPDATE modules 
-- SET Module_Name = 'My Finance' 
-- WHERE Module_Id = '28';

-- -- suhan#10074
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('343', 'Travel and Expenses', '0', '28');
-- UPDATE `forms` SET  `Sub_Form` = '343' WHERE `Form_Id` = '339';
-- UPDATE `forms` SET  `Sub_Form` = '343' WHERE `Form_Id` = '342';
-- UPDATE `forms` SET  `Form_Name` = 'Travel and Expenses' WHERE `Form_Id` = '340';

-- -- sai nitish #10058
-- INSERT INTO `fields` (Field_Id, Field_Name, Form_Id, Tab_Name) VALUES
-- (427, 'Reason Type', 333, NULL),
-- (428, 'Alternative Person', 332, NULL);

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (427, 'All Organization', NULL, 'Reason Type', '1', '0', NULL, 'Yes', NOW(), 1),
-- (428 ,'All Organization', NULL, 'Alternative Person', '1', '0', NULL, 'Yes', NOW(), 1);

-- -- Pasha #10100
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES 
-- ('344', 'My Pay', '0', '28'), 
-- ('345', 'My Payslip', '344', '28'), 
-- ('346', 'My Salary', '344', '28');

-- -- abhishek #10057
-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`, `Tab_Name`) 
-- VALUES 
-- (429, 'Documents ', 334, NULL),
-- (430, 'Documents ', 335, NULL);

-- INSERT INTO `customization_fields` 
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`) 
-- VALUES 
-- (429, 'All Organization', NULL, 'Documents ', '1', '0', NULL, NOW(), 1),
-- (430, 'All Organization', NULL, 'Documents ', '1', '0', NULL, NOW(), 1);

-- INSERT INTO `customization_fields` 
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Added_On`, `Added_By`) 
-- VALUES 
-- (188,'All Organization',NULL,'Tax Code',0,0,NULL,NOW(),1);

-- -- Pasha #10114
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES 
-- ('347', 'Leave Policy', '276', '20');

-- SURESH #10123
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES 
-- ('348', 'Reports', '330', '24');

-- SURESH #10123
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES 
-- ('350', 'Reports', '243', '24');

-- SURESH #10152
-- Tax Slab Query Update
-- UPDATE `tax_section` SET `Assessment_Year` = '2026' WHERE 1;

-- UPDATE `section_investment_category` SET `Assessment_Year` = '2026' WHERE 1;

-- UPDATE `tax_exemption` SET `Assessment_Year` = '2026' WHERE 1;

-- UPDATE `tax_rebate` SET `Assessment_Year` = '2026' WHERE 1;

-- UPDATE `house_property_income_sources` SET `Assessment_Year` = '2026' WHERE 1;

-- UPDATE `contractor_tax_rates` SET `Assessment_Year` = '2026' WHERE 1;

-- UPDATE `tax_rates` SET `Assessment_Year` = '2026' WHERE 1;

-- UPDATE tax_exemption as TE INNER JOIN tax_section as TS ON TS.Tax_Section_Id=TE.Tax_Section_Id INNER JOIN govt_tax_sections as GTS ON GTS.Section_Id=TS.Section_Id set Exemption_Amount=125000 where Section_Name='Section 80U' AND TE.Assessment_Year='2026' AND TE.Tax_Status='Active';

-- DELETE FROM tax_rates where Country_Code='IN';

-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','1','250000','0','0','1',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','250001','400000','5','0','1',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','400001','500000','5','5','1',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','500001','800000','20','5','1',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','800001','1000000','20','10','1',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','1000001','1200000','30','10','1',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','1200001','1600000','30','15','1',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','1600001','2000000','30','20','1',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','2000001','2400000','30','25','1',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','2400001','NULL','30','30','1',2026,'Active',0,1,Now(),NULL,NULL);

-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','1','250000','0','0','2',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','250001','400000','5','0','2',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','400001','500000','5','5','2',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','500001','800000','20','5','2',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','800001','1000000','20','10','2',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','1000001','1200000','30','10','2',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','1200001','1600000','30','15','2',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','1600001','2000000','30','20','2',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','2000001','2400000','30','25','2',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','2400001','NULL','30','30','2',2026,'Active',0,1,Now(),NULL,NULL);

-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','1','300000','0','0','3',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','300001','400000','5','0','3',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','400001','500000','5','5','3',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','500001','800000','20','5','3',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','800001','1000000','20','10','3',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','1000001','1200000','30','10','3',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','1200001','1600000','30','15','3',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','1600001','2000000','30','20','3',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','2000001','2400000','30','25','3',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','2400001','NULL','30','30','3',2026,'Active',0,1,Now(),NULL,NULL);

-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','1','400000','0','0','4',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','400001','500000','0','5','4',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','500001','800000','20','5','4',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','800001','1000000','20','10','4',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','1000001','1200000','30','10','4',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','1200001','1600000','30','15','4',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','1600001','2000000','30','20','4',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','2000001','2400000','30','25','4',2026,'Active',0,1,Now(),NULL,NULL);
-- INSERT INTO `tax_rates` (`Tax_Rate_Id`, `Tax_Entity_Id`, `Min_Range`, `Max_Range`, `Tax_Rate`, `New_Tax_Rate`, `Tax_Category_Id`, `Assessment_Year`, `Tax_Status`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES(NULL,'1','2400001','NULL','30','30','4',2026,'Active',0,1,Now(),NULL,NULL);

-- UPDATE `tax_rates` set Max_Range=NULL WHERE `Country_Code` = 'IN' AND Max_Range=0;

-- UPDATE tax_exemption te JOIN tax_section ts ON te.Tax_Section_ID = ts.Tax_Section_ID AND ts.Assessment_Year = 2026 SET te.New_Regime_Exemption_Amount = 75000 WHERE ts.Section_Id = 34 AND te.Assessment_Year = 2026;

-- UPDATE tax_rebate tr JOIN tax_section ts ON tr.Tax_Section_ID = ts.Tax_Section_ID AND ts.Assessment_Year = 2026 SET tr.Maximum_Tax_Rebate_New_Regime = 60000,tr.Income_Threshold_New_Regime=1200000 WHERE ts.Section_Id = 6 AND tr.Assessment_Year = 2026;

-- UPDATE tax_section SET Max_Limit = 60000 WHERE Section_Id = 6 AND Assessment_Year = 2026;

-- UPDATE tax_section SET Max_Limit = 75000 WHERE Section_Id = 34 AND Assessment_Year = 2026;

-- CREATE TABLE `marginal_releif` (
--   `Min_Range` float NOT NULL,
--   `Max_Range` float NOT NULL,
--   `Threshold` float NOT NULL,
--   `Tax_Regime` enum('Old Regime','New Regime') NOT NULL
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ALTER TABLE `marginal_releif`
--   ADD PRIMARY KEY (`Min_Range`,`Max_Range`,`Threshold`,`Tax_Regime`);

-- INSERT INTO `marginal_releif` (`Min_Range`, `Max_Range`, `Threshold`, `Tax_Regime`) VALUES
-- (120000,1270588,60000,'New Regime');

-- -- suhan #10139
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('349', 'General', '285', '27');


-- -- shanthi April 05 Deployment queries
-- update forms set Form_Name='Compensatory Off' where Form_Name='Compensatory Off Request';

-- -- suganya #10167
-- CREATE TABLE `attendance_summary_manager` (
--   `Org_Code` varchar(50) NOT NULL,
--   `Summary_Status` enum('Open','In Progress','Success','Failed') NOT NULL,
--   `Summarization_Date` date NOT NULL
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
-- ALTER TABLE `attendance_summary_manager` ADD INDEX( `Summary_Status`);

-- -- Pasha #10144
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES 
-- ('351', 'Accreditation Category and Type', '236', '20');

-- -- suhan 10173
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES
-- ('352', 'Short Time Off', '330', '24'),
-- ('353', 'Short Time Off', '331', '25');

-- UPDATE `forms` SET `Sub_Form` = '330' WHERE `Form_Id` = 264;
-- UPDATE `forms` SET `Sub_Form` = '331' WHERE `Form_Id` = 140;

-- -- suhan #9414
-- UPDATE `forms` SET `Form_Name` = 'Attendance Bulk Action' WHERE `Form_Id` = 320;

-- UPDATE `forms`
-- SET `Form_Name` = 'Travel and Expenses'
-- WHERE `Form_Id` = '340';

-- -- suhan#10209
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES
-- ('354', 'Travel and Expenses', '318', '20'),
-- ('355', 'Currency Exchange', '354', '20');

-- -- suhan #10182
-- INSERT INTO `fields` (`Field_Id`, `Field_Name`, `Form_Id`, `Tab_Name`) 
-- VALUES 

-- ('431', 'Barangay', '1', NULL),
-- ('432', 'Region', '1', NULL);

-- -- shanthi April 19 deployment queries
-- update forms set Sub_Form= 318 where form_id=355;


-- UPDATE customization_fields
-- SET New_Field_Name = CASE
--     WHEN LOWER(New_Field_Name) = 'street' THEN 'Street'
--     WHEN LOWER(New_Field_Name) = 'street 1' THEN 'Street 1'
--     WHEN LOWER(New_Field_Name) = 'street 2' THEN 'Street 2'
--     ELSE New_Field_Name
-- END
-- WHERE LOWER(New_Field_Name) IN ('street', 'street 1', 'street 2');


-- -- Soundar April 16 #10206
-- CREATE TABLE scheduler_engine_manager (
--     Org_Code VARCHAR(50) PRIMARY KEY,
--     Complete_Status ENUM('InProgress', 'Open', 'Success', 'Failed'),
--     Load_TimeStamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- -- suhan#10227
-- UPDATE modules
-- SET Module_Sequence = Module_Sequence + 1
-- WHERE Module_Sequence >= 9;
-- INSERT INTO `modules` (`Module_Id`, `Module_Name`, `Module_Sequence`, `Is_Visible`) VALUES ('29', 'Approvals', '9', '1');
-- update forms set Module_Id = 29 where Form_Id=184;
-- -- sai#10058
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES
-- ('356', 'Leave Closure', '276', '20');

-- -- sanket#10012
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('357', 'Attendance Finalization', '304', '24');

-- -- suhan#10243
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES  ('358', 'Employee Number Series', '234', '14');
-- -- 28Apr2025 Deployment Queries
-- UPDATE marginal_releif SET Min_Range = 1200000 WHERE Min_Range = 120000;

-- -- suhan#10342
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES
-- ('359', 'Time Off Closure', '276', '20');

-- insert into customization_forms values (NULL,247,'Specific Organization','adeera',1,'Work From Home',Now(),1,NULL,NULL);
-- insert into customization_forms values (NULL,257,'Specific Organization','adeera',1,'Work From Home',Now(),1,NULL,NULL);

-- insert into customization_forms values (NULL,247,'Specific Organization','hmcgroupuat',1,'Work From Home',Now(),1,NULL,NULL);
-- insert into customization_forms values (NULL,257,'Specific Organization','hmcgroupuat',1,'Work From Home',Now(),1,NULL,NULL);ss

-- delete from customization_fields where Customization_Applicable_For='All Organization';
-- delete FROM customization_fields where Customization_Applicable_For='Specific Organization' and Org_Code in ('cebpro', 'cebprouat');
-- delete from customization_fields where Field_Id > 116 and Field_Id < 404 and Field_Id != 313;
-- delete from customization_fields where Field_Id in (select Field_Id from fields where Form_Id=263);

-- -- insert into customization_forms values (NULL,257,'Specific Organization','hmcgroupuat',1,'Work From Home',Now(),1,NULL,NULL);

-- -- suhan#10387

-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('360', ' Salary Info', '318', '20');

-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('361', ' Salary Revision', '360', '20');

-- UPDATE `forms`
-- SET `Sub_Form` = '360', `Module_Id` = '20'
-- WHERE `Form_Id` IN (207);

-- UPDATE `forms`
-- SET `Sub_Form` = '205', `Module_Id` = '14'
-- WHERE `Form_Id` IN (206);

-- -- sai#10115
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES('362', 'Reports', '318', '20');


-- DELETE FROM forms WHERE Form_Id = 360;

-- UPDATE forms 
-- SET Form_Id = 360, Sub_Form = 318 
-- WHERE Form_Id = 361;

-- UPDATE forms 
-- SET Sub_Form = 318 
-- WHERE Form_Id = 207;

-- DROP PROCEDURE IF EXISTS createorgDB;
-- DELIMITER //

-- CREATE PROCEDURE createorgDB(IN oldDbName VARCHAR(64), IN newDbName VARCHAR(64), OUT success INT)
-- BEGIN
--     DECLARE newDbSql, newDbTableSql, newTableRecSql TEXT;
--     DECLARE no_more_tables INT DEFAULT 0;
--     DECLARE tblName VARCHAR(255);
--     DECLARE tblCursor CURSOR FOR SELECT table_name FROM information_schema.tables WHERE table_schema = oldDbName;
--     DECLARE CONTINUE HANDLER FOR NOT FOUND SET no_more_tables = 1;
--     DECLARE EXIT HANDLER FOR SQLEXCEPTION ROLLBACK;

--     START TRANSACTION;
--     SET success = 0;

--     SET @newDbSql = CONCAT("CREATE DATABASE ", newDbName, " CHARACTER SET utf8 COLLATE utf8_general_ci");
--     PREPARE stmt FROM @newDbSql;
--     EXECUTE stmt;
--     DEALLOCATE PREPARE stmt;
    
--     OPEN tblCursor;
--     createdatabase: LOOP
--         FETCH tblCursor INTO tblName;
--         IF no_more_tables THEN
--             CLOSE tblCursor;
--             LEAVE createdatabase;
--         END IF;

--         SET @newDbTableSql = CONCAT("CREATE TABLE ", newDbName, ".", tblName, " LIKE ", oldDbName, ".", tblName);
--         PREPARE stmt FROM @newDbTableSql;
--         EXECUTE stmt;
--         DEALLOCATE PREPARE stmt;
        
--         SET @newTableRecSql = CONCAT("INSERT INTO ", newDbName, ".", tblName, " SELECT * FROM ", oldDbName, ".", tblName);
--         PREPARE stmt FROM @newTableRecSql;
--         EXECUTE stmt;
--         DEALLOCATE PREPARE stmt;
--     END LOOP createdatabase;

--     SET success = 1;
--     COMMIT;
-- END //

-- DELIMITER ;

-- DELETE from forms where Form_Id=336;
-- -- suhan#10446
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('363', 'Over Time', '234', '14');

-- -- suhan#10446
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('364', 'Bimonthly Payslip', '38', '5');
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('365', 'Monthly and Hourly Payslip', '38', '5');

-- -- suhan#10472
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) 
-- VALUES ('366', 'Over Time Work', '234', '14');

-- -- Sanket  July 1st 2025 #10012
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES
-- ('367', 'Attendance Incomplete', '304', '24'),
-- ('368', 'No Attendance', '304', '24'),
-- ('369', 'Attendance Shortage', '304', '24');

-- -- Abhishek #10510
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('370', 'Asset Management', '0', '16');

-- -- Pasha
-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('371', 'Career Page Designer', '281', '14');


-- -- suhan#10572
-- DELETE FROM forms WHERE Form_Id = 183; -- Schedule Interviews
-- -- Delete Clients Master
-- DELETE FROM forms WHERE Form_Id = 172; -- Clients Master
-- -- Delete Interview Calendar
-- DELETE FROM forms WHERE Form_Id = 185; -- Interview Calendar
-- UPDATE forms 
-- SET Sub_Form = 236,
--     Module_Id = 20
-- WHERE Form_Id = 226;

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (478, 'Payroll Country', NULL, 'Salary Configuration Schema', 0, 0, 'PH', 'Yes', NOW(), 0);

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (479, 'Payroll Country', NULL, 'Minimum Wage', 0, 0, 'PH', 'No', NOW(), 0);

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (480, 'Payroll Country', NULL, 'TDS to be Deducted', 0, 0, 'PH', 'Yes', NOW(), 0);

-- INSERT INTO `customization_fields`
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`)
-- VALUES
-- (481, 'Payroll Country', NULL, 'FRRO Registration Completed', 0, 0, 'PH', 'No', NOW(), 0);

-- -- SSS Number to PF Number (Form 178)
-- INSERT INTO `customization_fields` 
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`) 
-- VALUES 
-- (219, 'Payroll Country', NULL, 'SSS Number', 1, 0, 'PH', 'No', NOW(), 1);

-- INSERT INTO `customization_fields` 
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`) 
-- VALUES 
-- (214, 'Payroll Country', NULL, 'SSS Number', 1, 0, 'PH', 'No', NOW(), 1);


-- -- Insurance No to PhilHealth Number (Form 178)
-- INSERT INTO `customization_fields` 
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`) 
-- VALUES 
-- (217, 'Payroll Country', NULL, 'PhilHealth Number', 1, 0, 'PH', 'No', NOW(), 1);

-- -- Insurance No to PhilHealth Number (Form 243)
-- INSERT INTO `customization_fields` 
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`) 
-- VALUES 
-- (223, 'Payroll Country', NULL, 'PhilHealth Number', 1, 0, 'PH', 'No', NOW(), 1);

-- -- NPS to Pag-IBIG/HDMF Number (Form 178)
-- INSERT INTO `customization_fields` 
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`) 
-- VALUES 
-- (218, 'Payroll Country', NULL, 'Pag-IBIG/HDMF Number', 1, 0, 'PH', 'No', NOW(), 1);

-- -- NPS to Pag-IBIG/HDMF Number (Form 243)
-- INSERT INTO `customization_fields` 
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`) 
-- VALUES 
-- (224, 'Payroll Country', NULL, 'Pag-IBIG/HDMF Number', 1, 0, 'PH', 'No', NOW(), 1);

-- -- IFSC Code to BIC (Form 178 - Bank Info)
-- INSERT INTO `customization_fields` 
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`) 
-- VALUES 
-- (135, 'Payroll Country', NULL, 'BIC', 1, 0, 'PH', 'No', NOW(), 1);

-- -- IFSC Code to BIC (Form 243 - Bank Info Self Service)
-- INSERT INTO `customization_fields` 
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`) 
-- VALUES 
-- (136, 'Payroll Country', NULL, 'BIC', 1, 0, 'PH', 'No', NOW(), 1);

-- -- Employee Profession - HIDDEN (Form 178)
-- INSERT INTO `customization_fields` 
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`) 
-- VALUES 
-- (446, 'Payroll Country', NULL, 'Employee Profession', 0, 0, 'PH', 'No', NOW(), 1);

-- -- Employee Profession - HIDDEN (Form 243)
-- INSERT INTO `customization_fields` 
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`) 
-- VALUES 
-- (447, 'Payroll Country', NULL, 'Employee Profession', 0, 0, 'PH', 'No', NOW(), 1);

-- -- TDS exemption - HIDDEN (Form 178)
-- INSERT INTO `customization_fields` 
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`) 
-- VALUES 
-- (306, 'Payroll Country', NULL, 'TDS exemption', 0, 0, 'PH', 'No', NOW(), 1);

-- -- TDS exemption - HIDDEN (Form 243)
-- INSERT INTO `customization_fields` 
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`) 
-- VALUES 
-- (307, 'Payroll Country', NULL, 'TDS exemption', 0, 0, 'PH', 'No', NOW(), 1);

-- -- PF number - HIDDEN (Form 178)
-- INSERT INTO `customization_fields` 
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`) 
-- VALUES 
-- (304, 'Payroll Country', NULL, 'PF number', 0, 0, 'PH', 'No', NOW(), 1);

-- -- PF number - HIDDEN (Form 243)
-- INSERT INTO `customization_fields` 
-- (`Field_Id`, `Customization_Applicable_For`, `Org_Code`, `New_Field_Name`, `Enable`, `Required`, `Country_Code`, `Predefined`, `Added_On`, `Added_By`) 
-- VALUES 
-- (305, 'Payroll Country', NULL, 'PF number', 0, 0, 'PH', 'No', NOW(), 1);

-- -- Soundar July 11 2025 #10577
-- INSERT INTO forms (Form_Id, Form_Name, Sub_Form, Module_Id) VALUES (372, 'Recruitment Settings', 281, 14);

-- -- Soundar July 16 #10577
-- UPDATE forms SET Form_Name='General' WHERE Form_Id=372;

-- -- Soundar July 18 #10602
-- INSERT INTO forms (Form_Id, Form_Name, Sub_Form, Module_Id) VALUES (374, 'Audit Log', 200, 15), (375, 'Audit Log', 196, 14),(376, 'Audit Log', 230, 14);

-- -- Sanket July 22 2025 #10609
-- INSERT INTO forms (Form_Id, Form_Name, Sub_Form, Module_Id) VALUES (373, 'Tax Liabilities', 124, 26);
-- UPDATE forms SET Sub_Form=304, Module_Id=24 WHERE Form_Id=324;

-- suhan#10387
-- INSERT INTO forms (Form_Id, Form_Name, Sub_Form, Module_Id)
-- VALUES (377, 'Statutory Components', 0, 26);

-- UPDATE forms SET Sub_Form = 377, Module_Id = 26 WHERE Form_Id IN (52, 126, 58, 90, 150, 71, 259, 127);


-- INSERT INTO forms (Form_Id, Form_Name, Sub_Form, Module_Id)
-- VALUES (378, 'Insurance Configuration', 377, 26);

-- update forms set Sub_Form=377 where Form_Id=260;


-- -- Pasha #10735 - Per Diem Configuration

-- INSERT INTO `forms` (`Form_Id`, `Form_Name`, `Sub_Form`, `Module_Id`) VALUES ('379', 'Per Diem', '354', '20');

-- -- Soundar Aguest 07 #10713
-- INSERT INTO forms (Form_Id, Form_Name, Sub_Form, Module_Id) 
-- VALUES (380, 'Report', 201, 14);

-- -- Pasha #10735 - Per Diem Configuration
-- UPDATE `forms` SET `Sub_Form` = '318' WHERE `Form_Id` = 379;
-- -- suhan#10387
-- update forms set Sub_Form=377 where Form_Id=110;


-- Run On Aug13 Deployment Queries
-- update forms set Sub_Form=0 where Form_Id=58;
-- Run On Aug13 Deployment Queries

-- AUG15 Deployment Queries
-- suhan#10769
-- INSERT INTO forms (Form_Id, Form_Name, Sub_Form, Module_Id) VALUES ('381', 'Earnings', '205', '14');
-- INSERT INTO forms (Form_Id, Form_Name, Sub_Form, Module_Id) VALUES ('382', 'Reimbursement', '205', '14');
-- INSERT INTO forms (Form_Id, Form_Name, Sub_Form, Module_Id) VALUES ('383', 'Bonus', '205', '14');

-- Pasha #10796
INSERT INTO forms (Form_Id, Form_Name, Sub_Form, Module_Id) 
VALUES (384, 'Toxicity Identifier', 230, 14);

INSERT INTO forms (Form_Id, Form_Name, Sub_Form, Module_Id) 
VALUES (385, 'Custom Alerts', 230, 14);

INSERT INTO forms (Form_Id, Form_Name, Sub_Form, Module_Id) 
VALUES (386, 'Toxicity Intelligence', 0, 22);
