<?php
//=========================================================================================
//=========================================================================================
/* Program : Awards.php											   			             *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MQL Query to retrive, add, update and delete employee awards and        *
 *                     award types.		                                                 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-Aug-2013    Narmadha, Mahesh        Initial Version        	         *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Dhanabal                Changes in file for mobile app     *
 *                                                    1.Extra fields are added in        *
 *                                                    field list of list query.          *
 *                                                                                       *
 *  1.5        10-Feb-2016    Prasanth             Changed in file for Bootstrap         *
 *                                                                                       */
//=========================================================================================
//=========================================================================================
class Employees_Model_DbTable_Awards extends Zend_Db_Table_Abstract
{
    protected $_orgDF      = null;
    protected $_db         = null;
    protected $_ehrTables  = null;
	protected $_dbCommonFun = null;
	
    public function init()
    {
        $this->_ehrTables   = new Application_Model_DbTable_Ehr();
        $this->_db          = Zend_Registry::get('subHrapp');
        $this->_orgDF       = $this->_ehrTables->orgDateformat();
		$this->_dbCommonFun = new Application_Model_DbTable_CommonFunction();
    }
	
	/**
	 * Get award type details to load in combo
	 */
    public function getAwardTypes()
    {
        return $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->awardtypes, array('AwardType_Id', 'Award_Name'))
									  ->order('Award_Name ASC'));
    }
	
	/**
	 * list awards details
	 */
    public function listAwards ($page, $rows, $sortField, $sortOrder, $searchAll, $searchArr, $awardUserArr)
    {
		$empFirstName   = $searchArr['EmpFirstName'];
		//$empLastName    = $searchArr['EmpLastName'];
		$awardTypeId    = $searchArr['AwardTypeId'];
		$awardBeginDate = $searchArr['AwardBeginDate'];
		$awardEndDate   = $searchArr['AwardEndDate'];
		
        /**
		 *	Sorting columns based on display column order in grid
		*/
		switch ($sortField)
		{
			case 1: $sortField = 'EJ.User_Defined_EmpId'; break;
            case 2: $sortField = 'P.Emp_First_Name'; break;			
			case 3: $sortField = 'AT.Award_Name'; break;
			case 4: $sortField = 'A.Award_Date'; break;
			default:
				$sortField = 'A.Added_On'; $sortOrder = 'desc'; break;
		}
		
		/**
		 *	Query to fetch data from various tables
		*/
        $qryAwards = $this->_db->select()
							->from(array('A'=>$this->_ehrTables->awards),
								   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS A.Award_Id as Count'),
										 'A.Award_Id', 'AwardTo_Id', 'A.Award_Date', 'Added_By', 'A.Description', 'A.AwardTo_Id as Emp_Id',
										 new Zend_Db_Expr("DATE_FORMAT(A.Added_On,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
										 new Zend_Db_Expr("DATE_FORMAT(A.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On"),
										 new Zend_Db_Expr("DATE_FORMAT(A.Award_Date,'".$this->_orgDF['sql']."') as AwardDate"),
										 'Log_Id'=>new Zend_Db_Expr($awardUserArr['LogId']),
										 'DT_RowClass' => new Zend_Db_Expr('"awards"'),
										 'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', A.Award_Id)")))
							
							->joinInner(array('AT'=>$this->_ehrTables->awardtypes), 'AT.AwardType_Id = A.AwardType_Id',
										array('AT.Award_Name', 'AT.AwardType_Id'))
							
							->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id = A.AwardTo_Id',
										array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name, ' ', P.Emp_Last_Name)")))
                            
                            ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'P.Employee_Id=EJ.Employee_Id',
                                            array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN P.Employee_Id ELSE EJ.User_Defined_EmpId END')))

							->joinInner(array('emp1'=>$this->_ehrTables->empPersonal),'emp1.Employee_Id=A.Added_By',
										array(new Zend_Db_Expr("CONCAT(emp1.Emp_First_Name, ' ', emp1.Emp_Last_Name) as Added_By_Name")))
							
							->joinLeft(array('emp2'=>$this->_ehrTables->empPersonal),'emp2.Employee_Id=A.Updated_By',
									   array(new Zend_Db_Expr("CONCAT(emp2.Emp_First_Name, ' ', emp2.Emp_Last_Name) as Updated_By_Name")))
							
							->order("$sortField $sortOrder")
							->limit($rows, $page);
        	
        if (empty($awardUserArr['Admin']))
        {
            $employeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
											   ->where('Manager_Id = ?', $awardUserArr['LogId']));
			
            if ( $awardUserArr['Is_Manager'] == 1 && !empty($employeeId) )
			{
                $qryAwards
					->where('A.AwardTo_Id = :EmpId or A.AwardTo_Id IN (?)', $employeeId)
					->bind(array('EmpId'=>$awardUserArr['LogId']));
            }
            else
			{
                $qryAwards->where('A.AwardTo_Id = ?', $awardUserArr['LogId']);
            }
        }
        
        /**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			 $conditions = $this->_db->quoteInto(new Zend_Db_Expr('Concat(P.Emp_First_Name," ",P.Emp_Last_Name) Like ?'),"%$searchAll%");
			$conditions .= $this->_db->quoteInto('or A.Award_Date Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or AT.Award_Name Like ?', "%$searchAll%");
		    $conditions .= $this->_db->quoteInto('or EJ.User_Defined_EmpId Like ?', "%$searchAll%");	
			$qryAwards->where($conditions);
		}
		
		/* Filter for Employee Name */
		if ( ! empty($empFirstName) /*&& preg_match('/^[a-zA-Z]+$/', $empFirstName)*/ )
		{
		    $qryAwards->where(new Zend_Db_Expr('Concat(P.Emp_First_Name," ",P.Emp_Last_Name) Like ?'),"%$empFirstName%");
		}
		
		
		/* Filter for Award Date */
		if ($awardBeginDate != '')
			$qryAwards->where($this->_db->quoteInto('A.Award_Date >= ?', $awardBeginDate));
		
		if ($awardEndDate != '')
			$qryAwards->where($this->_db->quoteInto('A.Award_Date <= ?', $awardEndDate));
		
		if ($awardTypeId != '')
			$qryAwards->where($this->_db->quoteInto('AT.AwardType_Id = ?', $awardTypeId));
		
			$qryAwards = $this->_dbCommonFun->getDivisionDetails($qryAwards,'EJ.Department_Id');
		/**
		 * SQL queries
		 * Get data to display
		*/
		$awards = $this->_db->fetchAll($qryAwards);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		/* Total data set length */
		$qryAwardsCnt = $this->_db->select()->from($this->_ehrTables->awards, new Zend_Db_Expr('COUNT(Award_Id)'));
		
		if (empty($awardUserArr['Admin']))
        {
            $employeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
											   ->where('Manager_Id = ?', $awardUserArr['LogId']));
			
            if ( $awardUserArr['Is_Manager'] == 1 && !empty($employeeId) )
			{
                $qryAwardsCnt
					->where('AwardTo_Id = :EmpId or AwardTo_Id IN (?)', $employeeId)
					->bind(array('EmpId'=>$awardUserArr['LogId']));
            }
            else
			{
                $qryAwardsCnt->where('AwardTo_Id = ?', $awardUserArr['LogId']);
            }
        }
		
		$iTotal = $this->_db->fetchOne($qryAwardsCnt);
		
		/**
		 * Output array with Json encode
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $awards);
    }
	
	/**
	 *	add/update award Details
	*/
	public function updateAward ($awardData, $awardId, $sessionId,$customFormName,$formName)
	{
		/**
		 *	Check Award is already exists or not in database
		*/
		$qryAward = $this->_db->select()
								->from($this->_ehrTables->awards, new Zend_Db_Expr('count(Award_Id)'))
								->where('AwardTo_Id = ?', $awardData['AwardTo_Id'])
								->where('AwardType_Id = ?', $awardData['AwardType_Id'])
								->where('Award_Date = ?', $awardData['Award_Date']);
		
        if (! empty($awardId))
        {
            $qryAward->where('Award_Id != ?',$awardId);
        }
		
        $checkExists = $this->_db->fetchOne($qryAward);
		
		/**
		 *	Check award details if exists return error message or process add/update function
		*/
		if ($checkExists == 0)
		{
			/**
			 *	Check Award id is greater than zero for run update process
			*/
			if ($awardId > 0)
			{
				$action = 'Edit';
				
				$awardData['Lock_Flag'] = 0;
				$awardData['Updated_By'] = $sessionId;
				$awardData['Updated_On'] = date('Y-m-d H:i:s');
				
				$updated = $this->_db->update($this->_ehrTables->awards, $awardData, array('Award_Id = ?'=>$awardId));
			}
			/**
			 *	If award id is empty then we process insertion
			*/
			else
			{
				$action = 'Add';
				
				$awardData['Added_By'] = $sessionId;
				$awardData['Added_On'] = date('Y-m-d H:i:s');
				
				$updated = $this->_db->insert($this->_ehrTables->awards, $awardData);
				
				if ($updated)
					$awardId = $this->_db->lastInsertId();
			}
			
			/**
			 *	this function will handle
			 *		update system log function
			 *		clear submit lock fucntion
			 *		return success/failure array
			*/
			$result = $this->_dbCommonFun->updateResult (array('updated'        => $updated,
															   'action'         => $action,
															   'trackingColumn' => $awardId,
															   'formName'       => $customFormName,
															   'sessionId'      => $sessionId,
															   'tableName'      => $this->_ehrTables->awards));
			
			if ($result['success'])
			{
				if ($action == 'Add')
				{
					return $this->_dbCommonFun->communicateMail (array('employeeId' => $awardData['AwardTo_Id'],
																	   'ModuleName' => 'Employees',
																		 'formName'    => $formName,
																		  'successMsg'  => $customFormName,
																		  'customFormName' => $customFormName,
																		  'formUrl'    => '/employees/awards',
																		  'inboxTitle' => $customFormName.' Notification',
																		'action'     => 'added'));
				}
				else
				{
					return array('success' => true, 'msg' => $customFormName.' updated successfully', 'type' => 'success');
				}
			}
			else
			{
				return $result;
			}
		}
		else
		{
			return array('success' => false, 'msg' => 'Award already exists', 'type' => 'info');
		}
	}
	
	/**
	 * Delete award
	 */
    public function deleteAward ($awardId, $sessionId,$customFormName)
    {
		$deleted = 0;
		
		/**
		 *	Get Lock Flag for award id
		*/
        $awardLock = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->awards, array('Lock_Flag'))
											->where('Award_Id = ?', $awardId));
		
		/**
		 *	Check Lock flag is empty or not.
		*/
		if ($awardLock == 0)
		{
			$deleted = $this->_db->delete($this->_ehrTables->awards, 'Award_Id='.$awardId);
		}
		
		/**
		 *	delete activity for common function
		 *		1)check lock is exist or not.
		 *			If lock is exist then show error message like employee is open record for update.
		 *		2)If No lockflag then process delete activity
		 *		3)Update delete activity in system log
		 *		4)return success/failure message
		*/
		return $this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
														'tableName'      => $this->_ehrTables->awards,
														'lockFlag'       => $awardLock,
														'formName'       => $customFormName,
														'trackingColumn' => $awardId,
														'sessionId'      => $sessionId));
    }
	
	
	/************************************************** Award Types ************************************************************/
	
	/**
	 * list award type details to show in a grid
	 */
    public function listAwardTypes ($page, $rows, $sortField, $sortOrder, $searchAll, $searchArr)
    {
		$awardName = $searchArr['AwardName'];
		$awardType = $searchArr['AwardType'];
		
        /**
		 *	Sorting columns based on display column order in grid
		*/
		switch ($sortField)
		{
			case 0: $sortField = 'AT.Award_Name'; break;
			case 1: $sortField = 'AT.Award_Type'; break;
		}
		
		/**
		 *	Query to fetch data from various tables
		*/
		$qryAwardTypes = $this->_db->select()
								->from(array('AT'=>$this->_ehrTables->awardtypes),
									   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS AT.AwardType_Id as Count'),
											 'AT.Award_Name', 'AT.AwardType_Id', 'AT.Description', 'AT.Award_Type',
											 'AwardType' => new Zend_Db_Expr('CASE
																				 WHEN Award_Type = 0 then "Cashless Award"
																				Else "Cash Award"
																			 END'),
											 new Zend_Db_Expr("DATE_FORMAT(AT.Added_On,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
											 new Zend_Db_Expr("DATE_FORMAT(AT.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On"),
											 'DT_RowClass' => new Zend_Db_Expr('"awardTypes"'),
											 'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', AT.AwardType_Id)")))
								
								->joinInner(array('AE'=>$this->_ehrTables->empPersonal), 'AE.Employee_Id=AT.Added_By',
										   array(new Zend_Db_Expr("CONCAT(AE.Emp_First_Name, ' ', AE.Emp_Last_Name) as Added_By_Name")))
								
								->joinLeft(array('UE'=>$this->_ehrTables->empPersonal), 'UE.Employee_Id=AT.Updated_By',
										   array(new Zend_Db_Expr("CONCAT(UE.Emp_First_Name, ' ', UE.Emp_Last_Name) as Updated_By_Name")))
								
								->order("$sortField $sortOrder")
								->limit($rows, $page);
		
        /**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			$conditions = $this->_db->quoteInto('AT.Award_Name Like ?', "%$searchAll%");
			//$conditions .= $this->_db->quoteInto('or AT.Award_Type Like ?', "%$searchAll%");
			
			if(substr_count("cashless award",strtolower($searchAll)) > 0)
			{
				$conditions .= $this->_db->quoteInto(' or AT.Award_Type Like ?', 0);
			}
			
			if(substr_count("cash award",strtolower($searchAll)) > 0)
			{
				$conditions .= $this->_db->quoteInto(' or AT.Award_Type Like ?', 1);
			}
			
			$qryAwardTypes->where($conditions);
		}
		
		/* Filter for Employee First Name */
		//if ($awardName != '' && $awardName != null && preg_match('/^[a-zA-Z]/', $awardName))
		if ($awardName != '')
			$qryAwardTypes->where($this->_db->quoteInto('AT.Award_Name Like ?', "%$awardName%"));
		
		if ($awardType != '')
			$qryAwardTypes->where($this->_db->quoteInto('AT.Award_Type = ?', $awardType));		
		
		
		/**
		 * SQL queries
		 * Get data to display
		*/
		$awardTypes = $this->_db->fetchAll($qryAwardTypes);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		/* Total data set length */
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->awardtypes, new Zend_Db_Expr('COUNT(AwardType_Id)')));
		
		/**
		 * Output array with Json encode
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $awardTypes);
    }
	
	/**
	 *	add/update award Details
	*/
	public function updateAwardType ($awardTypeData, $awardTypeId, $sessionId,$customFormName)
	{
		/**
		 *	Check Award Type is already exists or not in database
		*/
		$qryAward = $this->_db->select()
								->from($this->_ehrTables->awardtypes, new Zend_Db_Expr('count(AwardType_Id)'))
								->where('Award_Name Like ?', $awardTypeData['Award_Name']);
		
        if (! empty($awardTypeId))
        {
            $qryAward->where('AwardType_Id != ?',$awardTypeId);
        }
		
        $checkExists = $this->_db->fetchOne($qryAward);
		
		/**
		 *	Check award type details if exists return error message or process add/update function
		*/
		if ($checkExists == 0)
		{
			/**
			 *	Check Award Type id is greater than zero for run update process
			*/
			if ($awardTypeId > 0)
			{
				$action = 'Edit';
				
				$awardTypeData['Lock_Flag'] = 0;
				$awardTypeData['Updated_By'] = $sessionId;
				$awardTypeData['Updated_On'] = date('Y-m-d H:i:s');
				
				$updated = $this->_db->update($this->_ehrTables->awardtypes, $awardTypeData, array('AwardType_Id = ?'=>$awardTypeId));
			}
			/**
			 *	If award type id is empty then we process insertion
			*/
			else
			{
				$action = 'Add';
				
				$awardTypeData['Added_By'] = $sessionId;
				$awardTypeData['Added_On'] = date('Y-m-d H:i:s');
				
				$updated = $this->_db->insert($this->_ehrTables->awardtypes, $awardTypeData);
				
				if ($updated)
					$awardTypeId = $this->_db->lastInsertId();
			}
			
			
				
			/**
			 *	this function will handle
			 *		update system log function
			 *		clear submit lock fucntion
			 *		return success/failure array
			*/
			return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
									'action'         => $action,
									'trackingColumn' => $awardTypeId,
									'formName'       => $customFormName,
									'sessionId'      => $sessionId,
									'comboPair'      => $this->getAwardTypes(),
									'tableName'      => $this->_ehrTables->awardtypes));
		}
		else
		{
			return array('success' => false, 'msg' => $customFormName.' already exists', 'type' => 'info');
		}
	}
	
	/**
	 * Delete award
	 */
    public function deleteAwardType ($awardTypeId, $sessionId, $customFormName)
    {
		/**
		 *	Check Award type is mapped with award table
		*/
		$checkExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->awards, new Zend_Db_Expr('COUNT(Award_Id)'))
										   ->where('AwardType_Id= ?', $awardTypeId));
		
		/**
		 *	If Award type is didn't mapped with award table then process delete action
		*/
		if ($checkExist == 0)
		{
			$deleted = 0;
			
			/**
			 *	Get Lock Flag for award type id
			*/
			$awardTypeLock = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->awardtypes, array('Lock_Flag'))
												  ->where('AwardType_Id = ?', $awardTypeId));
			
			/**
			 *	Check Lock flag is empty or not.
			*/
			if ($awardTypeLock == 0)
			{
				$deleted = $this->_db->delete($this->_ehrTables->awardtypes, 'AwardType_Id='.$awardTypeId);
			}
			
			/**
			 *	delete activity for common function
			 *		1)check lock is exist or not.
			 *			If lock is exist then show error message like employee is open record for update.
			 *		2)If No lockflag then process delete activity
			 *		3)Update delete activity in system log
			 *		4)return success/failure message
			*/
			return $this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
															'tableName'      => $this->_ehrTables->awardtypes,
															'lockFlag'       => $awardTypeLock,
															'formName'       => $customFormName,
															'trackingColumn' => $awardTypeId,
															'sessionId'      => $sessionId,
															'comboPair'      => $this->getAwardTypes()));
		}
		else
		{
			return array('success' => false, 'msg' => 'Unable to delete award type. <br>Please, contact your system admin', 'type' => 'warning');
		}
    }
	
	public function __destruct()
    {
        
    }
	
}