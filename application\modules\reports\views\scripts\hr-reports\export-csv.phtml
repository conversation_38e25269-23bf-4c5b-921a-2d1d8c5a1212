<?php
use PhpOffice\PhpSpreadsheet\Helper\Sample;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
ob_end_clean();
$linkValue         	= $this->linkValue;
$isFilterGroupByExist = $this->isFilterGroupByExist;

if ($linkValue == 'Tds' || $linkValue === 'Payment Register' || $linkValue == 'Insurance Statement'
|| $linkValue == 'Ssnit Tier 1' || $linkValue == 'Ssnit Tier 2' || $linkValue === 'Provident Fund Detailed Report'
|| $linkValue == 'Reimbursement Allowances' || $linkValue == 'Hourly Wage Payslip' || $linkValue == 'Monthly Master Report'
|| $linkValue == 'Hourly Master Report' || $linkValue == "ESI Monthly" || $linkValue == "ESI Hourly"
|| $linkValue == 'ESIC Monthly' || $linkValue == 'ESIC Hourly' || $linkValue == 'Attendance Summary Hourly'
|| $linkValue == 'Attendance Summary Monthly' || $linkValue == 'Employee Utilization' || $linkValue == 'Additional Wage Summary'
|| $linkValue == 'Uan Based Ecr' || $linkValue == 'Uan Based Ecr Hourly' || strtolower($linkValue)== 'uan based ecr(arrear)'
|| $linkValue == 'Eft Monthly' || $linkValue == 'Eft Hourly' || $linkValue == 'Loan Amortization'
|| $linkValue == 'Attendance Shortage' || $linkValue == 'Employee Wise Expenses' || $linkValue == 'Reimbursement'
|| $linkValue === 'Pay Bill' || $linkValue === 'Monthly Payslip Comprehensive' || $linkValue==='Monthly Salary'
|| $linkValue === 'Employee Status' || $linkValue === 'Lop Recovery' || $linkValue == 'Employee Step Increment'
|| $linkValue === 'Bank Salary Statement' || $linkValue === 'Lwf' || $linkValue === 'Professional Tax Monthly'
|| $linkValue === 'Pt Annual Return(Form 5a)')  
{
	$reportRow 			= $this->table_data;
	$data 				= $this->data;
	$locale             = $this->employeeLocale;
	$filterGroupBy      = $this->filterGroupBy;
	
	if(isset($this->footer))
	{
		$footer = $this->footer;
	}
	else
	{
		$footer = array();
	}
	
	$resultArr 		   	= array();
	$organizationName  	= $this->organizationName;
	$showReportCreator 	= $this->showReportCreator;
	unset($this->table_data,$this->data,$this->employeeLocale,$this->filterGroupBy);
	unset($this->organizationName,$this->showReportCreator,$this->linkValue);
	if(isset($this->keyValues))
	{
		$reportHeader = $this->keyValues;
	}
	else
	{
		$heading = array_keys($data[0]);
		$reportHeader = $heading;
	}

	ob_end_clean();
	$reportTitle 	  			= $this->reportTitle;
	if(empty($reportTitle))
	{
		$reportTitle 	  		= $linkValue;
	}

	$titleStyle = array(
		'font' => array(
			'bold' => true
		),
		'borders' => array(
			'allBorders' => array(
				'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
				'color' => array('argb' => '********'),
			),
		)
	);

	$headerStyle = array(
		'font' => array(
			'bold' => true
		),
		'alignment' => array(
			'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,	
		),
		'borders' => array(
			'allBorders' => array(
				'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
				'color' => array('argb' => '********'),
			),
		),
		'fill' => array(
			'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
			'startColor' => array('argb' => '92CDDC')
		)
	);

	$dataBorder = array(
		'borders' => array(
			'allBorders' => array(
				'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
				'color' => array('argb' => '********'),
			),
		)
	);

	$dbHRReport 				= new Reports_Model_DbTable_HrReports();
	$dbCommonFunction 			= new Application_Model_DbTable_CommonFunction();
	$startColumnName 			= 0;
	$endColumnNamePosition   	= count($reportHeader);
	$startColumnName 			= $dbHRReport->getNameFromNumber($startColumnName);
	$endColumnName   			= $dbHRReport->getNameFromNumber($endColumnNamePosition-1);
	$autoResizeColumnName   	= $dbHRReport->getNameFromNumber($endColumnNamePosition);
	if($linkValue == 'Monthly Payslip Comprehensive'){
		$freezeFrom  			  	= 8;   
	}
	else
	{
		$freezeFrom  			  	= 2;   
	}	
	$freezePositionColumnName 	= $dbHRReport->getNameFromNumber($freezeFrom);

	$spreadsheet = new Spreadsheet();
	$activeSheet = $spreadsheet->getActiveSheet();
	$fontSize = array('font'  => array('size'  => 10));
	$spreadsheet->getDefaultStyle()->applyFromArray($fontSize);

	//If the report is not eft monthly and eft hourly then present organization name and report title
	if($linkValue != 'Eft Monthly' && $linkValue != 'Eft Hourly'){
		$rowStart = 1;
		$organizationNameStartPosition 	= $startColumnName.$rowStart;
		$organizationNameEndPosition 	= $endColumnName.$rowStart;
		$rowStart++;
		$reportNameStartPosition 		= $startColumnName.$rowStart;
		$reportNameEndPosition 		    = $endColumnName.$rowStart;
		$rowStart++;

		$spreadsheet->setActiveSheetIndex(0)->setCellValue("$organizationNameStartPosition", $organizationName)->setCellValue("$reportNameStartPosition", $reportTitle);
		$activeSheet->mergeCells("$organizationNameStartPosition:$organizationNameEndPosition");
		$activeSheet->mergeCells("$reportNameStartPosition:$reportNameEndPosition");
		$activeSheet->getStyle("$organizationNameStartPosition:$organizationNameEndPosition")->applyFromArray($titleStyle);
		$activeSheet->getStyle("$reportNameStartPosition:$reportNameStartPosition")->applyFromArray($titleStyle);
	}else{
		$rowStart = 0;
	}

	//Present created by and created on except for these reports
	if($showReportCreator==1 && ($linkValue != 'Pay Bill'
	&& $linkValue != 'Eft Monthly' && $linkValue != 'Eft Hourly'))
	{
		$createdBy = $this->createdBy;
		$createdOn = $this->createdOn;
		$createdByStartPosition = $startColumnName.$rowStart;
		$createdByEndPosition 	= $endColumnName.$rowStart;
		$rowStart++;
		$createdOnStartPosition = $startColumnName.$rowStart;
		$createdOnEndPosition 	= $endColumnName.$rowStart;
		$rowStart++;
		$spreadsheet->setActiveSheetIndex(0)->setCellValue("$createdByStartPosition", $createdBy)->setCellValue("$createdOnStartPosition", $createdOn);
		$activeSheet->mergeCells("$createdByStartPosition:$createdByEndPosition");
		$activeSheet->mergeCells("$createdOnStartPosition:$createdOnEndPosition");
		$activeSheet->getStyle("$createdByStartPosition:$createdByEndPosition")->applyFromArray($titleStyle);
		$activeSheet->getStyle("$createdOnStartPosition:$createdOnEndPosition")->applyFromArray($titleStyle);
	}

	if($linkValue == 'Employee Step Increment')
	{
		$employeeDetails 	= $this->commonData;
		$employeeId 		= "Employee Code : ".$employeeDetails['Employee_Id'];
		$employeeName 		= "Employee Name : ".$employeeDetails['Name'];
		$designationName 	= "Designation : ".$employeeDetails['Employee_Position'];
		$stepIncrementBasisDate = "Step Increment Basis Date : ".$employeeDetails['Step_Increment_Basis_Date'];
		$dateRange 				= "Date Range ";
		
		$rowStart++;
		$employeeIdStartPosition 		= $startColumnName.$rowStart;
		$employeeIdEndPosition 			= $endColumnName.$rowStart;
		$rowStart++;
		$employeeNameStartPosition 		= $startColumnName.$rowStart;
		$employeeNameEndPosition 		= $endColumnName.$rowStart;
		$rowStart++;
		$designationNameStartPosition 	= $startColumnName.$rowStart;
		$designationNameEndPosition 	= $endColumnName.$rowStart;
		$rowStart++;
		$stepIncrementBasisDateStartPosition 	= $startColumnName.$rowStart;
		$stepIncrementBasisDateEndPosition 		= $endColumnName.$rowStart;
		$rowStart++;
		$dateRangeStartPosition 			= $startColumnName.$rowStart;
		$dateRangeEndPosition 				= $endColumnName.$rowStart;
		
		$spreadsheet->setActiveSheetIndex(0)->setCellValue("$employeeIdStartPosition", $employeeId)
		->setCellValue("$employeeNameStartPosition", $employeeName)
		->setCellValue("$designationNameStartPosition", $designationName)
		->setCellValue("$stepIncrementBasisDateStartPosition", $stepIncrementBasisDate)
		->setCellValue("$dateRangeStartPosition", $dateRange);

		$activeSheet->mergeCells("$employeeIdStartPosition:$employeeIdEndPosition");
		$activeSheet->mergeCells("$employeeNameStartPosition:$employeeNameEndPosition");
		$activeSheet->mergeCells("$designationNameStartPosition:$designationNameEndPosition");
		$activeSheet->mergeCells("$stepIncrementBasisDateStartPosition:$stepIncrementBasisDateEndPosition");
		$activeSheet->mergeCells("$dateRangeStartPosition:$dateRangeEndPosition");

		$activeSheet->getStyle("$employeeIdStartPosition:$employeeIdEndPosition")->applyFromArray($titleStyle);
		$activeSheet->getStyle("$employeeNameStartPosition:$employeeNameEndPosition")->applyFromArray($titleStyle);
		$activeSheet->getStyle("$designationNameStartPosition:$designationNameEndPosition")->applyFromArray($titleStyle);
		$activeSheet->getStyle("$stepIncrementBasisDateStartPosition:$stepIncrementBasisDateEndPosition")->applyFromArray($titleStyle);
		$activeSheet->getStyle("$dateRangeStartPosition:$dateRangeEndPosition")->applyFromArray($titleStyle);

	}

	
	if($linkValue == 'Tds')
	{
		$address1 	= $this->address1;
		$address2 	= $this->address2;
		$pantan 	= $this->pantan;

		$address1StartPosition 	= $startColumnName.$rowStart;
		$address1EndPosition 	= $endColumnName.$rowStart;
		$rowStart++;
		$address2StartPosition 	= $startColumnName.$rowStart;
		$address2EndPosition 	= $endColumnName.$rowStart;
		$rowStart++;
		$pantanStartPostion 	= $startColumnName.$rowStart;
		$pantanEndPosition 		= $endColumnName.$rowStart;
		$rowStart++;

		$spreadsheet->setActiveSheetIndex(0)->setCellValue("$address1StartPosition", $address1)->setCellValue("$address2StartPosition", $address2)->setCellValue("$pantanStartPostion", $pantan);
		$activeSheet->mergeCells("$address1StartPosition:$address1EndPosition");
		$activeSheet->mergeCells("$address2StartPosition:$address2EndPosition");
		$activeSheet->mergeCells("$pantanStartPostion:$pantanEndPosition");
		$activeSheet->getStyle("$address1StartPosition:$address1EndPosition")->applyFromArray($titleStyle);
		$activeSheet->getStyle("$address2StartPosition:$address2EndPosition")->applyFromArray($titleStyle);
		$activeSheet->getStyle("$pantanStartPostion:$pantanEndPosition")->applyFromArray($titleStyle);

	}
	elseif($linkValue == 'Employee Wise Expenses')
	{
		$employeeDetails	= $this->getExpenseEmployeeDetails; 
		$employeeId 		= "Employee Code : ".$employeeDetails['User_Defined_EmpId'];
		$employeeName 		= "Employee Name : ".$employeeDetails['Employee_Name'];
		$designationName 	= "Designation : ".$employeeDetails['Designation_Name'];
		$locationName 		= "Location : ".$employeeDetails['Location_Name'];
		$startDate 			= $this->sdate;
		$endDate 			= $this->ldate;
		$expenseStatment    = "Expense Statement for the Period from ".$startDate." to ".$endDate;

		$employeeIdStartPosition 		= $startColumnName.$rowStart;
		$employeeIdEndPosition 			= $endColumnName.$rowStart;
		$rowStart++;
		$employeeNameStartPosition 		= $startColumnName.$rowStart;
		$employeeNameEndPosition 		= $endColumnName.$rowStart;
		$rowStart++;
		$designationNameStartPosition 	= $startColumnName.$rowStart;
		$designationNameEndPosition 	= $endColumnName.$rowStart;
		$rowStart++;
		$locationNamePostion 			= $startColumnName.$rowStart;
		$locationNamePosition 			= $endColumnName.$rowStart;
		$rowStart++;
		$expenseStatmentStartPosition 	= $startColumnName.$rowStart;
		$expenseStatmentEndPosition 	= $endColumnName.$rowStart;
		$rowStart++;
		
		$spreadsheet->setActiveSheetIndex(0)->setCellValue("$employeeIdStartPosition", $employeeId)
		->setCellValue("$employeeNameStartPosition", $employeeName)
		->setCellValue("$designationNameStartPosition", $designationName)
		->setCellValue("$locationNamePostion", $locationName)
		->setCellValue("$expenseStatmentStartPosition", $expenseStatment);
		$activeSheet->mergeCells("$employeeIdStartPosition:$employeeIdEndPosition");
		$activeSheet->mergeCells("$employeeNameStartPosition:$employeeNameEndPosition");
		$activeSheet->mergeCells("$designationNameStartPosition:$designationNameEndPosition");
		$activeSheet->mergeCells("$locationNamePostion:$locationNamePosition");
		$activeSheet->mergeCells("$expenseStatmentStartPosition:$expenseStatmentEndPosition");
		$activeSheet->getStyle("$employeeIdStartPosition:$employeeIdEndPosition")->applyFromArray($titleStyle);
		$activeSheet->getStyle("$employeeNameStartPosition:$employeeNameEndPosition")->applyFromArray($titleStyle);
		$activeSheet->getStyle("$designationNameStartPosition:$designationNameEndPosition")->applyFromArray($titleStyle);
		$activeSheet->getStyle("$locationNamePostion:$locationNamePosition")->applyFromArray($titleStyle);
		$activeSheet->getStyle("$expenseStatmentStartPosition:$expenseStatmentEndPosition")->applyFromArray($titleStyle);
	}
	elseif($linkValue == 'Loan Amortization')
	{
		$loanDetails		= $this->loanData;
		$employeeId 		= "Employee Id:".$loanDetails['User_Defined_EmpId'];
		$employeeName 		= "Employee Name :".$loanDetails['Employee_Name'];
		$loanAmount 		= "Loan Amount:".$loanDetails['Loan_Amount'];
		$durationInMonths 	= "Loan Tenure :".$loanDetails['Duration_In_Months'];
		$interest 			= "Interest :".$loanDetails['Interest'];

		$employeeIdStartPosition 	= $startColumnName.$rowStart;
		$employeeIdEndPosition 		= $endColumnName.$rowStart;
		$rowStart++;
		$employeeNameStartPosition 	= $startColumnName.$rowStart;
		$employeeNameEndPosition 	= $endColumnName.$rowStart;
		$rowStart++;
		$loanAmountStartPosition 	= $startColumnName.$rowStart;
		$loanAmountEndPosition 		= $endColumnName.$rowStart;
		$rowStart++;
		$durationInMonthsPostion 	= $startColumnName.$rowStart;
		$durationInMonthsPosition 	= $endColumnName.$rowStart;
		$rowStart++;
		$interestStartPosition 		= $startColumnName.$rowStart;
		$interestEndPosition 		= $endColumnName.$rowStart;
		$rowStart++;
		
		$spreadsheet->setActiveSheetIndex(0)->setCellValue("$employeeIdStartPosition", $employeeId)
		->setCellValue("$employeeNameStartPosition", $employeeName)
		->setCellValue("$loanAmountStartPosition", $loanAmount)
		->setCellValue("$durationInMonthsPostion", $durationInMonths)
		->setCellValue("$interestStartPosition", $interest);
		$activeSheet->mergeCells("$employeeIdStartPosition:$employeeIdEndPosition");
		$activeSheet->mergeCells("$employeeNameStartPosition:$employeeNameEndPosition");
		$activeSheet->mergeCells("$loanAmountStartPosition:$loanAmountEndPosition");
		$activeSheet->mergeCells("$durationInMonthsPostion:$durationInMonthsPosition");
		$activeSheet->mergeCells("$interestStartPosition:$interestEndPosition");
		$activeSheet->getStyle("$employeeIdStartPosition:$employeeIdEndPosition")->applyFromArray($titleStyle);
		$activeSheet->getStyle("$employeeNameStartPosition:$employeeNameEndPosition")->applyFromArray($titleStyle);
		$activeSheet->getStyle("$loanAmountStartPosition:$loanAmountEndPosition")->applyFromArray($titleStyle);
		$activeSheet->getStyle("$durationInMonthsPostion:$durationInMonthsPosition")->applyFromArray($titleStyle);
		$activeSheet->getStyle("$interestStartPosition:$interestEndPosition")->applyFromArray($titleStyle);
	}
	elseif($linkValue=='Pay Bill')
	{
		$totalEarningsStartPosition  	= array_search('Basic_Pay+AGP', $reportHeader); 
		$totalDeductionStartPosition 	= array_search('Loss_of_Pay', $reportHeader);
		$totalEarningsEndPosition 		= $totalDeductionStartPosition-1; 
		$totalDeductionEndPosition 		= array_search('Total_Earnings', $reportHeader);
	
		$totalEarningsStartPosition 	= $dbHRReport->getNameFromNumber($totalEarningsStartPosition);
		$totalEarningsEndPosition 		= $dbHRReport->getNameFromNumber($totalEarningsEndPosition);
		$totalDeductionStartPosition	= $dbHRReport->getNameFromNumber($totalDeductionStartPosition);
		$totalDeductionEndPosition 		= $dbHRReport->getNameFromNumber($totalDeductionEndPosition-1);
		
		$totalEarningsStartPlace 		= $totalEarningsStartPosition.$rowStart;
		$totalEarningsEndPlace 			= $totalEarningsEndPosition.$rowStart;
		$totalDeductionStartPlace 		= $totalDeductionStartPosition.$rowStart;
		$totalDeductionEndPlace 		= $totalDeductionEndPosition.$rowStart;
	
	}
	else if($linkValue == 'Pt Annual Return(Form 5a)')
	{
		$address1 	= $this->address1;

		$address1StartPosition 	= $startColumnName.$rowStart;
		$address1EndPosition 	= $endColumnName.$rowStart;
		$rowStart++;
		
		$spreadsheet->setActiveSheetIndex(0)->setCellValue("$address1StartPosition", $address1);
		$activeSheet->mergeCells("$address1StartPosition:$address1EndPosition");
		$activeSheet->getStyle("$address1StartPosition:$address1EndPosition")->applyFromArray($titleStyle);
	}
	$emptyRowStartPosition 		= $startColumnName.$rowStart;
	$emptyRowEndPosition 		= $endColumnName.$rowStart;
	$activeSheet->getStyle("$emptyRowStartPosition:$emptyRowEndPosition")->applyFromArray($titleStyle);
	$rowStart++;
	$header	= array();

	if(isset($this->presentationHeaderDetails))
	{
		$presentationHeader = $this->presentationHeaderDetails;
		foreach($presentationHeader as $keys)
		{
			$keyVal = str_replace('_', ' ', $keys);
			$header[] = $keyVal; 	
		}
	}
	else
	{
		foreach($reportHeader as $keys)
		{
			$keyVal = str_replace('_', ' ', $keys);
			$header[] = $keyVal; 	
		}
	}

	$activeSheet->fromArray($header, null, $startColumnName.$rowStart);	
	$headerFrom = $startColumnName.$rowStart;
	$headerTo   = $endColumnName.$rowStart; 
	$activeSheet->getStyle("$headerFrom:$headerTo")->applyFromArray($headerStyle);
	$groupByReportTitles = array('Pay Bill','Payment Register','Bank Salary Statement','ESI Monthly','Lwf','Professional Tax Monthly');
	if(in_array($linkValue,$groupByReportTitles) && $isFilterGroupByExist)
	{
		if($linkValue=='Pay Bill')
		{
			$borderBold = array(
									'font' => array(
										'bold' => true
									),
									'alignment' => array(
										'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,	
									),
									'borders' => array(
										'allBorders' => array(
											'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
											'color' => array('argb' => '********'),
										),
									),
								);

			$payBillHeaderStyle = array(
				'font' => array(
					'bold' => true
				),
				'alignment' => array(
					'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,	
				),
				'borders' => array(
					'allBorders' => array(
						'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
						'color' => array('argb' => '********'),
					),
				),
				'fill' => array(
					'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
					'startColor' => array('argb' => '92CDDC')
				)
			);					

			$activeSheet->getStyle("$organizationNameStartPosition:$organizationNameEndPosition")->getAlignment()->setHorizontal('center');
			$activeSheet->getStyle("$reportNameStartPosition:$reportNameEndPosition")->getAlignment()->setHorizontal('center');
			$spreadsheet->setActiveSheetIndex(0)->setCellValue("$totalEarningsStartPlace",'Total Earnings')->setCellValue("$totalDeductionStartPlace", 'Total Deductions');
			$activeSheet->mergeCells("$totalEarningsStartPlace:$totalEarningsEndPlace");
			$activeSheet->mergeCells("$totalDeductionStartPlace:$totalDeductionEndPlace");
			$activeSheet->getStyle("$totalEarningsStartPlace:$totalEarningsEndPlace")->applyFromArray($payBillHeaderStyle);
			$activeSheet->getStyle("$totalDeductionStartPlace:$totalDeductionEndPlace")->applyFromArray($payBillHeaderStyle);
			
			$totalEarningsSubColumnStart  = $totalEarningsStartPosition.$rowStart;
			$totalEarningsSubColumnEnd 	  = $totalEarningsEndPosition.$rowStart;
			$totalDeductionSubColumnStart = $totalDeductionStartPosition.$rowStart;
			$totalDeductionSubColumnEnd   = $totalDeductionEndPosition.$rowStart;
			$activeSheet->getStyle("$totalEarningsSubColumnStart:$totalEarningsSubColumnEnd")->applyFromArray($borderBold);
			$activeSheet->getStyle("$totalDeductionSubColumnStart:$totalDeductionSubColumnEnd")->applyFromArray($borderBold);
		}
		$rowStart++;

		$previousGroupByName = '';
		if($linkValue=='Pay Bill')
		{
			$filterGroupBy = 'Department';
		}
		foreach ($data as $row)
		{
			$newRow = array();
			$currentGroupByName = $row[$filterGroupBy];
			if($previousGroupByName != $currentGroupByName)
			{
				if($linkValue=='Pay Bill')
				{
					$groupBy = array($filterGroupBy=>strtoupper('DEPARTMENT OF '.$currentGroupByName));
				}
				else
				{
					$groupBy = array($filterGroupBy=>strtoupper($currentGroupByName));					
				}
				$activeSheet->fromArray($groupBy, null, $startColumnName.$rowStart);
				$activeSheet->getStyle($startColumnName.$rowStart)->getFont()->setBold(true);
				$rowStart++;									
			}
			foreach($reportHeader as $k)
			{
				if($k=='Employee_Signature')
				{
					$newRow[$k] = '';
				}
				else if(isset($row[$k]))
				{
					if($row[$k] == '0')
					{
						$newRow[$k] = '0.00';
					}
					elseif($row[$k] == '-')
					{
						$newRow[$k] = '--';
					}
					elseif($row[$k] == "00/00/0000" || $row[$k] == "0000/00/00")
					{
						$newRow[$k] = '--';
					}
					else
					{
						$row[$k] = str_replace("\n", '', $row[$k]);
						$newRow[$k] = $row[$k];
					}
				}
				else 
				{
					$newRow[$k] = '--';
				}
				$dataFrom = $startColumnName.$rowStart;
				$dataTo   = $endColumnName.$rowStart; 
				$activeSheet->getStyle("$dataFrom:$dataTo")->applyFromArray($dataBorder);
			}
			$previousGroupByName = $row[$filterGroupBy];
			// $activeSheet->fromArray($newRow, null, $startColumnName.$rowStart);		
			$col = $startColumnName;
			foreach ($newRow as $key => $value) {
				$cell = $col . $rowStart;
				$activeSheet->setCellValue($cell, $value);

				// List of fields that should have 2 decimal formatting
				$decimalFields = array('Basic_Pay', 'ESI_EE', 'ESI_ER', 'ESI_Wages', 'Unpaid_Deduction', 'Sum_of_allowance', 'Sum_of_adhoc_allowance', 'Total_Overtime_Wages', 'Holiday_Special_Wages',
									'Employer_Contribution','Employee_Contribution','Total_Contribution','Total_Earnings','Amount','Total_Earnings','Total_Deductions','Net_Pay');

				// Apply formatting only to selected fields
				if (in_array($key, $decimalFields) && is_numeric($value)) {
					$activeSheet->getStyle($cell)
						->getNumberFormat()
						->setFormatCode('0.00');
				}

				$col++;
			}

			$dbHRReport->convertExcelSheetNumberFormatToString($reportHeader,$activeSheet,$rowStart);

			if($linkValue=='Payment Register')
			{
				$employeeSignaturePosition = array_search('Employee_Signature', $reportHeader);
				if($employeeSignaturePosition > 0)
				{
					$newHeight = 50; // Set the desired height in points
					// Set the new height for the entire row
					$activeSheet->getRowDimension($rowStart)->setRowHeight($newHeight);	
				}
				
			}
			$rowStart++;	
		}
	}
	else
	{
		$rowStart++;
		if($linkValue !='Loan Amortization' && $linkValue != 'Employee Wise Expenses' && $linkValue != 'Employee Step Increment')
		{
			$freezePosition 		  = $freezePositionColumnName.$rowStart;
			$activeSheet->freezePane($freezePosition); 
		}

		foreach ($data as $row) {
			// Your existing code logic here
			$newRow = array();
			foreach ($reportHeader as $k) {
				// Your existing code logic here

				if (isset($row[$k])) {
					if ($row[$k] == '0') {
						$newRow[$k] = '0.00';
					} elseif ($row[$k] == '-') {
						$newRow[$k] = '--';
					} elseif ($row[$k] == "00/00/0000" || $row[$k] == "0000/00/00") {
						$newRow[$k] = '--';
					} else {
						$row[$k] = str_replace("\n", '', $row[$k]);
						if ($linkValue == 'Monthly Salary Payslip') {
							$keysToCheck = ['PAN_No', 'UAN', 'ESIC_Number', 'Bank_Account_Number', 'Bank_Name', 'IFSC_Code'];
							if (in_array($k, $keysToCheck)) {
								$newRow[$k] = $row[$k];
							} else {
								if (is_numeric($row[$k])) {
									if($k=='Employee_Id')
									{
										$newRow[$k] = $row[$k];
									}
									else
									{
										$newRow[$k] = $dbCommonFunction->formatCurrency($row[$k], $locale);	
									}
								} else {
									$newRow[$k] = $row[$k];
								}
							}
						} else {
							$newRow[$k] = $row[$k];
						}
					}
				} else {
					$newRow[$k] = '--';
				}

				$dataFrom = $startColumnName . $rowStart;
				$dataTo = $endColumnName . $rowStart;
				$activeSheet->getStyle("$dataFrom:$dataTo")->applyFromArray($dataBorder);
			}

			$activeSheet->fromArray($newRow, null, $startColumnName . $rowStart);
			$dbHRReport->convertExcelSheetNumberFormatToString($reportHeader, $activeSheet, $rowStart);
			$rowStart++;
			
			// Unset $newRow to free up memory
			unset($newRow);
		}
}

foreach ($footer as $foot) {
	$footerRow = array();
	foreach ($reportHeader as $k) {
		if (!isset($foot[$k]) || $foot[$k] == '-') {
			$footerRow[$k] = '';
		} elseif ($foot[$k] == '') {
			$footerRow[$k] = '';
		} else {
			if ($linkValue == 'Monthly Salary Payslip') {
				if (is_numeric($foot[$k])) {
					$footerRow[$k] = $dbCommonFunction->formatCurrency($foot[$k], $locale);
				} else {
					$footerRow[$k] = $foot[$k];
				}
			} else {
				$footerRow[$k] = $foot[$k];
			}
		}
	}

	$footerFrom = $startColumnName . $rowStart;
	$footerTo = $endColumnName . $rowStart;
	$activeSheet->getStyle("$footerFrom:$footerTo")->applyFromArray($titleStyle);
	$activeSheet->fromArray($footerRow, null, $startColumnName . $rowStart);

	//To show the amount with two decimal places
	$footerCol = $startColumnName;
	foreach ($footerRow as $value) {
		if (is_numeric($value)) {
			$cell = $footerCol . $rowStart;
			$activeSheet->getStyle($cell)->getNumberFormat()->setFormatCode('0.00');
		}
		$footerCol++;
	}
	// Unset $footerRow to free up memory
	unset($footerRow);
}
	

	$activeSheetTitleLength = strlen($linkValue);
	if($activeSheetTitleLength > 31)
	{
		$activeSheetTitle = substr($linkValue, 0, 31);
	}
	else
	{
		$activeSheetTitle = $linkValue;
	}
	
	$activeSheet->setTitle($activeSheetTitle);
	// Set active sheet index to the first sheet, so Excel opens this as the first sheet
	$spreadsheet->setActiveSheetIndex(0);

	for($col = $startColumnName ; $col !== $autoResizeColumnName; $col++)
	{
		$activeSheet->getColumnDimension($col)->setAutoSize(true);
	}
	

	ob_end_clean();
	// Redirect output to a client’s web browser (Xlsx)
	header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
	header('Content-Disposition: attachment;filename='.$reportTitle.'.xlsx');
	header('Cache-Control: max-age=0');
	// If you're serving to IE 9, then the following may be needed
	header('Cache-Control: max-age=1');

	// If you're serving to IE over SSL, then the following may be needed
	header('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // Date in the past
	header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // always modified
	header('Cache-Control: cache, must-revalidate'); // HTTP/1.1
	header('Pragma: public'); // HTTP/1.0
	// ob_end_clean();
	$writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
	$writer->save('php://output');
	exit;
}
else
{
	echo Zend_Json::encode($this->result);
}
?>