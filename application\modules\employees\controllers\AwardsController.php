 <?php
//=========================================================================================
//=========================================================================================
/* Program : AwardsController.php							 					         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.                                                         *														                                  *
 * All Rights Reserved.                                                                  *      														                          *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : Awards are used within organizations to instruct employees about        *
 * policies, disseminate information and delegate responsibilities. Awards are useful    *
 * for manager to inform and motivate employees.					                     *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-Aug-2013    Narmadha, Mahesh        Initial Version        	         *
 *  0.2        06-Jul-2014    Mahesh                  Modified Function			         *
 *                                                    1.addawardAction                	 *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Dhanabal                Changed in file for mobile app     *
 *                                                                                       *
 *  1.5        10-Feb-2016    Prasanth             Changed in file for Bootstrap         *
 *											 				                             */
//=========================================================================================
//=========================================================================================

include APPLICATION_PATH."/validations/Validations.php";
class Employees_AwardsController extends Zend_Controller_Action
{
    protected $_dbCommonFun     = null;
	protected $_dbAwards        = null;
    protected $_dbPersonal      = null;
    protected $_dbAccessRights  = null;
    protected $_awardAccess     = null;
    protected $_awardTypeAccess = null;
	protected $_awardUsers      = null;
    protected $_awardTypeUsers  = null;
    protected $_logEmpId        = null;
    protected $_validation      = null;
	protected $_ehrTables          = null;
    protected $_formNameA       = 'Awards';
    protected $_formNameB       = 'Award Types';
	protected $_hrappMobile = null;
    
    public function init()
    {
		$this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
		if ($this->_hrappMobile->checkAuth())
        {
			$this->_dbCommonFun    = new Application_Model_DbTable_CommonFunction();
            $this->_dbAwards       = new Employees_Model_DbTable_Awards();
            $this->_dbPersonal     = new Employees_Model_DbTable_Personal();
            $this->_dbAccessRights = new Default_Model_DbTable_AccessRights();
            $this->_validation 	   = new Validations();
            $this->_ehrTables 	   = new Application_Model_DbTable_Ehr();
			
			$userSession            = $this->_dbCommonFun->getUserDetails ();
            $this->_logEmpId        = $userSession['logUserId'];
            $this->_awardAccess     = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameA);
            $this->_awardTypeAccess = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameB);
			$this->_awardUsers      = $this->_awardAccess['Employee'];
			$this->_awardTypeUsers  = $this->_awardTypeAccess['Employee'];
            //$this->_dbAccessRights->refreshUserSessionTimestamp($this->_logEmpId);
        }
        else
        {
            if (Zend_Session::namespaceIsset('lastRequest'))
                Zend_Session:: namespaceUnset('lastRequest');
            
            $sessionUrl = new Zend_Session_Namespace('lastRequest');
            $sessionUrl->lastRequestUri = 'employees/awards';
            $this->_redirect('auth');
        }
    }

    public function indexAction()
    {
		$checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

		if ($checkSessionAuth)
		{
			$this->_helper->layout()->disableLayout()->setLayout('admin_layout');
		
			$this->view->formNameA  = $this->_formNameA;
			$this->view->formNameB  = $this->_formNameB;
			
			$this->view->customFormNameA = $this->_ehrTables->getCustomForms($this->_formNameA);
			$this->view->customFormNameB = $this->_ehrTables->getCustomForms($this->_formNameB);
			
			$this->view->awardTypes = $this->_dbAwards->getAwardTypes();
			$this->view->employeeDetails = $this->_dbCommonFun->listEmployeesDetails ('Awards', '', $this->_logEmpId);
			
			$this->view->awardsUser = array('Is_Manager'    => $this->_awardUsers['Is_Manager'],
											'View'          => $this->_awardUsers['View'],
											'Update'        => $this->_awardUsers['Update'],
											'Delete'        => $this->_awardUsers['Delete'],
											'Add'           => $this->_awardUsers['Add'],
											'Admin'         => $this->_awardAccess['Admin'],
											'Employee_Name' => $this->_dbPersonal->employeeId($this->_logEmpId));
			
			$this->view->awardTypeUser = array('View'   => $this->_awardTypeUsers['View'],
											   'Update' => $this->_awardTypeUsers['Update'],
											   'Delete' => $this->_awardTypeUsers['Delete'],
											   'Add'    => $this->_awardTypeUsers['Add']);
			
			$ehrTables         = new Application_Model_DbTable_Ehr();
			$this->view->dateformat = $ehrTables->orgDateformat();
		} else {
			$this->_redirect('auth');
		}
    }

    /**
     * Get awards details to show in a grid
     */
    public function listAwardsAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('list-awards', 'json')->initContext();
			
            if ($this->_awardUsers['View'] == 1)
            {	
                $sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
				
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$empFirstName = $this->_getParam('Emp_First_Name', null);
				$empFirstName = filter_var($empFirstName, FILTER_SANITIZE_STRIPPED);
				
				//$empLastName = $this->_getParam('Emp_Last_Name', null);
				//$empLastName = filter_var($empLastName, FILTER_SANITIZE_STRIPPED);
				
				$awardTypeId = $this->_getParam('AwardType_Id', null);
				$awardTypeId = filter_var($awardTypeId, FILTER_SANITIZE_NUMBER_INT);
				
				$awardBeginDate = $this->_getParam('AwardsBeginDate', null);
				$awardBeginDate = filter_var($awardBeginDate, FILTER_SANITIZE_STRIPPED);
				
				$awardEndDate = $this->_getParam('AwardsEndDate', null);
				$awardEndDate = filter_var($awardEndDate, FILTER_SANITIZE_STRIPPED);
				
                $awardUserArr = array('Is_Manager' => $this->_awardUsers['Is_Manager'],
									  'Admin'      => $this->_awardAccess['Admin'],
									  'LogId'      => $this->_logEmpId);
				
				$searchArr = array('EmpFirstName'   => $empFirstName,
								   //'EmpLastName'    => $empLastName,
								   'AwardTypeId'    => $awardTypeId,
								   'AwardBeginDate' => $awardBeginDate,
								   'AwardEndDate'   => $awardEndDate);
				
				$this->view->result = $this->_dbAwards->listAwards ($page, $rows, $sortField, $sortOrder, $searchAll, $searchArr, $awardUserArr);
            }
        }
        else
        {
            $this->_helper->redirector ('index', 'awards', 'employees');
        }
    }
	
	/**
	 *	update award details
	*/
	public function updateAwardAction()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('update-award', 'json')->initContext();
			
			$awardId = $this->_getParam('awardId', null);
			$awardId = filter_var($awardId, FILTER_SANITIZE_NUMBER_INT);
			
            if (($this->_awardUsers['Is_Manager'] == 1 || !empty($this->_awardAccess['Admin'])) &&
				($this->_awardUsers['Add'] == 1 || ( $this->_awardUsers['Update'] == 1 && !empty($awardId) && $awardId > 0)))
			{
				if ($this->getRequest()->isPost())
				{
					$formData = $this->getRequest()->getPost();
					
					$employeeId  = $this->_validation->intValidation($formData['employeeId']);
					$awardTypeId = $this->_validation->intValidation($formData['awardTypeId']);
					$awardDate   = $this->_validation->dateValidation($formData['awardDate']);
					
					$description['value'] = $this->_validation->commonFilters($formData['description']);
					$description['valid'] = $this->_validation->lengthValidation($description, 5, 500, true);
					
					if (!empty($employeeId['value']) && $employeeId['valid'] && !empty($awardTypeId['value']) && $awardTypeId['valid'] &&
						!empty($awardDate['value']) && $awardDate['valid'] && !empty($description['value']) && $description['valid'])
					{
						$employeeId  = $employeeId['value'];
						$awardTypeId = $awardTypeId['value'];
						$awardDate   = $awardDate['value'];
						$description = $description['value'];
						$ckAddedBy   = $this->_dbPersonal->ckAddedById ($employeeId);
						$ehrTables   = new Application_Model_DbTable_Ehr();
						$dateOfJoin  = date('Y-m-d',strtotime($ehrTables->changeDateformat($this->_dbPersonal->getDateOfJoin($employeeId))));
						
						if (($employeeId != $this->_logEmpId) && ($ckAddedBy == $this->_logEmpId || !empty($this->_awardAccess['Admin'])) &&
							(($awardId == 0 && strtotime($awardDate) >= strtotime(date('Y-m-d'))) ||
							 ($awardId > 0 && strtotime($awardDate) >= strtotime($dateOfJoin))))
						{
							$awardData = array('AwardTo_Id'   => $employeeId,
											   'Award_Date'   => $awardDate,
											   'AwardType_Id' => $awardTypeId,
											   'Description'  => htmlentities($description));
							
                            $customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
                            $customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
                            
							$this->view->result = $this->_dbAwards->updateAward ($awardData, $awardId, $this->_logEmpId, $customFormNamee,$this->_formNameA );
						}
						else
						{
							$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
						}
					}
					else
					{
						$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
					}
				}
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Access denied', 'type' => 'danger');
			}
        }
		else
        {
            $this->_helper->redirector ('index', 'awards', 'employees');
        }
    }
	
	/**
     *	Delete Award details
     */
    public function deleteAwardAction()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('delete-award', 'json')->initContext();
			
			if ($this->_awardUsers['Delete'] == 1 && ($this->_awardUsers['Is_Manager'] == 1 || !empty($this->_awardAccess['Admin'])))
            {
				$awardId = $this->_getParam('awardId', null);
				$awardId = filter_var($awardId, FILTER_SANITIZE_NUMBER_INT);
				
				if (!empty($awardId) && $awardId > 0)
				{
                    $customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
					$customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
                    
					$this->view->result = $this->_dbAwards->deleteAward ($awardId, $this->_logEmpId, $customFormNamee);
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
				}
            }
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Access denied', 'type' => 'warning');
			}
        }
        else
        {
            $this->_helper->redirector ('index', 'awards', 'employees');
        }
    }
	
	
	
	/******************************************** Award Type *****************************************************/
	
    /**
     * list award type details to show in a grid
     */
    public function listAwardTypesAction()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('list-award-types', 'json')->initContext();
			
            if ($this->_awardTypeUsers['View'] == 1)
            {
                $sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
				
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$awardName = $this->_getParam('Award_Name', null);
				$awardName = filter_var($awardName, FILTER_SANITIZE_STRIPPED);
				
				$awardType = $this->_getParam('Award_Type', null);
				$awardType = filter_var($awardType, FILTER_SANITIZE_STRIPPED);
				
                $searchArr = array('AwardName' => $awardName,
								   'AwardType' => $awardType);
				
				$this->view->result = $this->_dbAwards->listAwardTypes ($page, $rows, $sortField, $sortOrder, $searchAll, $searchArr);
            }
        }
        else
        {
            $this->_helper->redirector ('index', 'awards', 'employees');
        }
    }
	
	/**
     * list award type details to show in a grid
     */
    public function updateAwardTypeAction()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('update-award-type', 'json')->initContext();
			
            $awardTypeId = $this->_getParam('awardTypeId', null);
			$awardTypeId = filter_var($awardTypeId, FILTER_SANITIZE_NUMBER_INT);
			
            if ($this->_awardTypeUsers['Add'] == 1 || ( $this->_awardTypeUsers['Update'] == 1 && !empty($awardTypeId) && $awardTypeId > 0))
            {
                if ($this->getRequest()->isPost())
				{
					$formData = $this->getRequest()->getPost();
					
					$awardName = $this->_validation->alnumValidation($formData['awardName']);
					$awardName['valid'] = $this->_validation->lengthValidation($awardName, 3, 50, true);
					
					$awardType = $this->_validation->intValidation($formData['awardType']);
					
					$description['value'] = $this->_validation->commonFilters($formData['description']);
					$description['valid'] = $this->_validation->lengthValidation($description, 5, 500, false);
					
					if (!empty($awardName['value']) && $awardName['valid'] && (!empty($awardType['value']) || $awardType['value'] == 0) &&
						$awardType['valid'] && $description['valid'])
					{
						$awardTypeData = array('Award_Name'  => $awardName['value'],
											   'Award_Type'  => $awardType['value'],
											   'Description' => htmlentities ($description['value']));
						
                        $customFormName = $this->_ehrTables->getCustomForms($this->_formNameB);
                        $customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameB);
                        
						$this->view->result = $this->_dbAwards->updateAwardType ($awardTypeData, $awardTypeId, $this->_logEmpId,$customFormNamee);
					}
					else
					{
						$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
					}
				}
            }
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Access denied', 'type' => 'danger');
			}
        }
        else
        {
            $this->_helper->redirector ('index', 'awards', 'employees');
        }
    }
	
	/**
     *	Delete Award Type details
     */
    public function deleteAwardTypeAction()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('delete-award-type', 'json')->initContext();
			
			if ($this->_awardTypeUsers['Delete'] == 1)
            {
				$awardTypeId = $this->_getParam('awardTypeId', null);
				$awardTypeId = filter_var($awardTypeId, FILTER_SANITIZE_NUMBER_INT);
				
				if (!empty($awardTypeId) && $awardTypeId > 0)
				{
                    $customFormName = $this->_ehrTables->getCustomForms($this->_formNameB);
                    $customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameB);
                        
					$this->view->result = $this->_dbAwards->deleteAwardType ($awardTypeId, $this->_logEmpId,$customFormNamee);
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
				}
            }
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Access denied', 'type' => 'warning');
			}
        }
        else
        {
            $this->_helper->redirector ('index', 'awards', 'employees');
        }
    }

	public function __destruct()
    {
        
    }
}