<?php

class FormsManager_Model_DbTable_Form16 extends Zend_Db_Table_Abstract
{
    protected $_db             = null;
    protected $_ehrTables      = null;
    protected $_commonFunction = null;
    protected $_dbFinancialYr = null;
    protected $_dbPaySlip = null;
    
    public function init()
    {
        $this->_ehrTables      = new Application_Model_DbTable_Ehr();
        $this->_db             = Zend_Registry::get('subHrapp');
		$this->_dbBilling      = new Default_Model_DbTable_Billing();
        $this->_commonFunction = new Application_Model_DbTable_CommonFunction();
		$this->_dbFinancialYr  = new Default_Model_DbTable_FinancialYear();
		$this->_dbPaySlip      = new Payroll_Model_DbTable_Payslip();

		if (Zend_Registry::isRegistered('orgDetails'))
			$this->_orgDetails = Zend_Registry::get('orgDetails');
    }
	
	/** Search form 16**/
	public function searchForm16($page,$rows,$sortField,$sortOrder,$form16User,$searchAll,$searchDetails)
    {
		$orgDF = $this->_ehrTables->orgDateformat('php');
		
		/**
		 *	Declare sorting fields columns
		*/
        switch ($sortField)
        {
			case 0: $sortField = 'P.Emp_First_Name'; break;
			case 1: $sortField = 'F.Assessment_Year'; break;
        }
			
		$qryForm16 = $this->_db->select()
                                     ->from(array('F'=>$this->_ehrTables->form16),
                                            array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS F.Form_Id as Count'),'F.Form_Id','F.Employee_Id',
											'F.Assessment_Year','F.Generated_On as Generated_Date',
											new Zend_Db_Expr("DATE_FORMAT(F.Generated_On,'".$orgDF['sql']." %H:%i:%s') as Generated_On"),
											'Admin'=>new Zend_Db_Expr("'".$form16User['Admin']."'"), 'Payroll_Group'=>new Zend_Db_Expr("'".$form16User['Payroll_Group']."'"),
											'Op_Choice'=>new Zend_Db_Expr("'".$form16User['Op_Choice']."'")))
                                  
                                     ->joinInner(array('P'=>$this->_ehrTables->empPersonal),'P.Employee_Id=F.Employee_Id',
                                            array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name, ' ', P.Emp_Last_Name)")))                                    
									 ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'P.Employee_Id=EJ.Employee_Id',
											array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN P.Employee_Id ELSE EJ.User_Defined_EmpId END')))
							


									->where('F.Generated_On IS NOT ?', new Zend_Db_Expr('NULL'))
									->where('F.Generated_By IS NOT ?', new Zend_Db_Expr('NULL'))
									
                                    ->order("$sortField $sortOrder")
                            
                                    ->limit($rows, $page);
									
        if(empty($form16User['Admin']) && empty($form16User['Payroll_Group']))
		{
			$qryEmployeeId = $this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
								->where('Manager_Id = ?', $form16User['LogId']);
			$getEmployeeId = $this->_db->fetchCol($qryEmployeeId);
        	if( $form16User['Is_Manager'] == 1 && !empty($getEmployeeId))
			{
				$qryForm16->where('F.Employee_Id = :EmpId or F.Employee_Id IN (?)', $getEmployeeId)
                                ->bind(array('EmpId'=>$form16User['LogId']));
            }	
			else
			{
				$qryForm16->where('F.Employee_Id = ?', $form16User['LogId']);
			}
		}
				
		/**
		 *	Search All columns using single input
		*/								
		if (!empty($searchAll) && $searchAll != null)
		{
            $conditions  = $this->_db->quoteInto(new Zend_Db_Expr('Concat(P.Emp_First_Name," ",P.Emp_Last_Name) Like ?'),"%$searchAll%");
            $conditions .= $this->_db->quoteInto('or F.Assessment_Year Like ?', "%$searchAll%");            
            
            $qryForm16->where($conditions);
        }
		
		/* Filter for Employee Name */
		if ( ! empty($searchDetails['Employee_Name']) /*&& preg_match('/^[a-zA-Z]+$/', $searchDetails['Employee_Name'])*/ )
		{
			$employeeName = $searchDetails['Employee_Name'];	
			$qryForm16->where($this->_db->quoteInto(new Zend_Db_Expr('Concat(P.Emp_First_Name," ",P.Emp_Last_Name) Like ?'),"%$employeeName%"));
		}
		
		/* Filter for Total Amount */
		if ( $searchDetails['Assessment_Year_Start'] !='' && $searchDetails['Assessment_Year_Start'] >=0 &&
			preg_match('/^[0-9*\.]/', $searchDetails['Assessment_Year_Start']))
		{
			$qryForm16->where('F.Assessment_Year >= ?', $searchDetails['Assessment_Year_Start']);
		}
		
		if ( $searchDetails['Assessment_Year_End'] !='' && $searchDetails['Assessment_Year_End'] >=0 &&
			preg_match('/^[0-9*\.]/', $searchDetails['Assessment_Year_End']))
		{
			$qryForm16->where('F.Assessment_Year <= ?', $searchDetails['Assessment_Year_End']);
		}
		
        $qryForm16 = $this->_commonFunction->getDivisionDetails($qryForm16,'EJ.Department_Id');

        /**
		 * SQL queries
		 * Get data to display
		*/
		$form16Details = $this->_db->fetchAll($qryForm16);
		
        /* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		
		/** get financial start year **/
		$financialStartYear = $this->_dbFinancialYr->financialStartYear(date('n'),date('Y'));
		
		/** Financial Year 2016-2017 records will be generated and deleted only in the financial year 2017-2018. **/
		foreach($form16Details as $key=>$row){
			if(($row['Assessment_Year'] == $financialStartYear) &&  ($row['Admin']=='admin' || $row['Payroll_Group'] == 1)){
				$form16Details[$key]['Delete_Form16'] = 1;
			}
			else{
				$form16Details[$key]['Delete_Form16'] = 0;
			}  		
		}
		
		/* Total data set length */
		$iTotalQry = $this->_db->select()->from($this->_ehrTables->form16, new Zend_Db_Expr('COUNT(Form_Id)'))
											-> where('Generated_On IS NOT NULL')								
											-> where('Generated_By != ?', 0);
		if(empty($form16User['Admin']))
		{
			$qryEmployeeId = $this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
								->where('Manager_Id = ?', $form16User['LogId']);
			$getEmployeeId = $this->_db->fetchCol($qryEmployeeId);
        	if( $form16User['Is_Manager'] == 1 && !empty($getEmployeeId))
			{
				$iTotalQry->where('Employee_Id = :EmpId or Employee_Id IN (?)', $getEmployeeId)
                                ->bind(array('EmpId'=>$form16User['LogId']));                                
            }	
			else
			{
				$iTotalQry->where('Employee_Id = ?', $form16User['LogId']);
			}
		}
		
		$iTotal = $this->_db->fetchOne($iTotalQry);
		
		/**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $form16Details);
	}
	
	/**
	 * Get form16 employee details by employeeId
	 */
	public function generateForm16($assessmentYear, $location, $department, $employeeType, $customFormName, $logEmpId)
	{
		$orgPanTan = $this->_ehrTables->getPanTan();
		
		$taxConfigDetails = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->taxConfiguration, array('Form16_Signatory','CIT')));
		
		/** Get the current assessment year form16 employee **/
		$form16Employee = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->form16, 'Employee_Id')
								->where('Assessment_Year = ?', $assessmentYear));
		
		/** Form 16 has to be generated only after form12ba is generated. **/
		$form12BAEmployeeId =  $this->_db->fetchCol($this->_db->select()->from(array('FT'=>$this->_ehrTables->form12ba),array('Employee_Id'))
								->where('FT.Financial_Year = ?',$assessmentYear));
		
		if(!empty($form12BAEmployeeId))
		{			
            if(!empty($orgPanTan['PAN']) && !empty($orgPanTan['TAN']) && !empty($taxConfigDetails['Form16_Signatory']) && !empty($taxConfigDetails['CIT']))
            {
                $form16UniqueId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->form16, 'Form_Id')
                                    ->where('Assessment_Year = ?', $assessmentYear)
                                    ->where('Generated_On IS ?', new Zend_Db_Expr('NULL'))
                                    ->where('Generated_By IS ?', new Zend_Db_Expr('NULL')));
                
                $fiscalMonth = $this->_dbFinancialYr->getFiscalMonth();
                $financialYear = $financialYearStart = ($assessmentYear-1); $financialYearEnd = $assessmentYear;
				
				/** get the financial year salary months in an array. For ex, (4,2016)...(3,2017) **/
				$salaryMonths = $this->_dbFinancialYr->getFiscalMonthYear(null,$financialYearEnd,'Monthly',1);	
				
				/** Get the employee who is having tds and tds amount should be greater than zero **/                
                $qryemployeeId =  $this->_db->select()->from(array('SI'=>$this->_ehrTables->salaryDeduction),
                                                                array(''))
                                    ->joinLeft(array('MP'=>$this->_ehrTables->monthlyPayslip),'MP.Payslip_Id=SI.Payslip_Id',
                                                    array('MP.Employee_Id'))							
                                    ->joinLeft(array('EJ'=>$this->_ehrTables->empJob),'EJ.Employee_Id=MP.Employee_Id',
                                               array(''))
                                    ->joinLeft(array('Dep'=>$this->_ehrTables->dept),'EJ.Department_Id=Dep.Department_Id',
                                               array(''))
                                    ->joinLeft(array('ET'=>$this->_ehrTables->empType),'EJ.EmpType_Id=ET.EmpType_Id',
                                               array(''))
                                    ->joinLeft(array('Loc'=>$this->_ehrTables->location),'EJ.Location_Id=Loc.Location_Id',
                                               array(''))											
                                    ->where('Dep.Department_Id IN (?)',$department)
                                    ->where('ET.EmpType_Id IN (?)',$employeeType)
                                    ->where('Loc.Location_Id IN (?)',$location)
                                    ->where('MP.Salary_Month IN (?)',$salaryMonths)
                                    ->where('SI.Deduction_Name = ?','Tax')
									->where('SI.Description != ?','Contractor_Tax')
                                    ->group('MP.Employee_Id');
                                    
                /** remove the employee if form 16 is already generated for that assessment year **/
                if(!empty($form16Employee)){
                    $qryemployeeId->where('MP.Employee_Id NOT IN (?)',$form16Employee);
                }
                
                /** get the tax employee for the assessment year for those form 16 has to be generated **/
                $taxEmployeeId =  $this->_db->fetchCol($qryemployeeId);
                
                $empPANCount = 0; $mergedEmpId = $employeeDetails = array();			
                
                /** compare tax employee with form12ba employee, if tax employees alreadys exists in form 12ba means,
                  that employee will be taken to generate form 16 **/
                if(!empty($taxEmployeeId)){
                    foreach($taxEmployeeId as $taxEmp){
                        if(in_array($taxEmp,$form12BAEmployeeId)){
                            array_push($mergedEmpId,$taxEmp);
                        }
                    }
                    
                    if(!empty($mergedEmpId)){
                        $employeeDetails = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->empPersonal, array('PAN','Employee_Id'))
                                                ->where('Employee_Id IN (?)',$mergedEmpId));
                    }
                }
			   $panErrorMessage = 'Please update pan details for following employee(s) ';
			   
			   
				/** Check PAN exists for all employees**/
				for($e=0;$e<count($employeeDetails);$e++)
				{
					if($employeeDetails[$e]['PAN'] == '' && $employeeDetails[$e]['PAN'] == NULL){
							
						$empnameA = $this->_dbPaySlip->employeename($employeeDetails[$e]['Employee_Id']);
						
						$empnameACount = count($empnameA);
				
						$panErrorMessage .= $empnameA[0].',';
					
						$empPANCount += 1;
					}
				}
			
			
				
                /** check employee details or form16 employees exists **/
                if(/*!empty($form16Employee)*/ !empty($form16UniqueId) || !empty($employeeDetails)){
                    if(empty($empPANCount)){
                        if(!empty($employeeDetails))
                        {
                            for($i=0;$i<count($employeeDetails);$i++)
                            {
                                unset($employeeDetails[$i]['PAN']);
                                
                                $employeeDetails[$i]['Assessment_Year'] = $assessmentYear;
                                $employeeDetails[$i]['Generated_On'] = date('Y-m-d H:i:s');
                                $employeeDetails[$i]['Generated_By'] = $logEmpId;
                            }
                            
                            $updated = $this->_ehrTables->insertMultiple($this->_ehrTables->form16, $employeeDetails);						
                            
                            if(!empty($form16UniqueId))
                            {
                                foreach($form16UniqueId as $key=>$row){							
                                    $form16UpdateArr['Generated_On'] = date('Y-m-d H:i:s');
                                    $form16UpdateArr['Generated_By'] = $logEmpId;
                                    
                                    $updated = $this->_db->update($this->_ehrTables->form16, $form16UpdateArr, array('Form_Id = '. $row));
                                }
                            }
                            
                            $trackSysLog =  $this->_commonFunction->updateResult (array('updated'        => $updated,
                                                                        'action'         => 'generated',
                                                                        'trackingColumn' => $assessmentYear,
                                                                        'formName'       => $customFormName,
                                                                        'sessionId'      => $logEmpId,
                                                                        'tableName'      => $this->_ehrTables->form16));		
                        }
                        else
                        {
                            foreach($form16UniqueId as $key=>$row){							
                                $form16UpdateArr['Generated_On'] = date('Y-m-d H:i:s');
                                $form16UpdateArr['Generated_By'] = $logEmpId;
                                
                                $updated = $this->_db->update($this->_ehrTables->form16, $form16UpdateArr, array('Form_Id = '. $row));
                            }
                            
                            $trackSysLog =  $this->_commonFunction->updateResult (array('updated'        => $updated,
                                                                    'action'         => 'generated',
                                                                    'trackingColumn' => $assessmentYear,
                                                                    'formName'       => $customFormName,
                                                                    'sessionId'      => $logEmpId,
                                                                    'tableName'      => $this->_ehrTables->form16));
                        }
                        
                        return $trackSysLog;
                    }
                    else{
						
						$panErrorMessage = rtrim($panErrorMessage,',');
		                return array('success' => false, 'msg' => $panErrorMessage, 'type' => 'warning');
                    }
                }
                else{
                    return array('success' => false, 'msg' => 'No employees found to generate form 16', 'type' => 'warning');
                }
            }
            else{
				return array('success' => false, 'msg' => 'Please update PAN,TAN,FORM16 Signatory,CIT in tax configuration details', 'type' => 'info');
            }
		}
		else{
			return array('success' => false, 'msg' => 'Please generate form12BA', 'type' => 'warning');
		}
	}

	/** Get employee and designation level - form 16 access employee names list **/
    public function getForm16AccessEmployee(){
        $response = $this->_commonFunction->executeCommonLibraryFunction(null, 'func', 'getEmployeeNamesBasedOnFormId', null);
        return $response;
	}	
	
	/** converting amount in words**/
	public function convertNumber($num)
	{
        $output = "";
        
        $num = str_pad($num, 36, "0", STR_PAD_LEFT);
        $group = rtrim(chunk_split($num, 3, " "), " ");
        $groups = explode(" ", $group);
        
        $groups2 = array();
        foreach($groups as $g) $groups2[] = $this->convertThreeDigit($g{0}, $g{1}, $g{2});
	
        for($z = 0; $z < count($groups2); $z++)
        {
            if($groups2[$z] != "")
            {
                $output .= $groups2[$z].$this->convertGroup(11 - $z).($z < 11 && !array_search('', array_slice($groups2, $z + 1, -1))
                && $groups2[11] != '' && $groups[11]{0} == '0' ? " and " : ", ");
            }
        }
        
        $output = rtrim($output, ", ");
        return $output;
	}
	
	public function convertGroup($index)
	{
        switch($index)
        {
            case 11: return " decillion";
            case 10: return " nonillion";
            case 9: return " octillion";
            case 8: return " septillion";
            case 7: return " sextillion";
            case 6: return " quintrillion";
            case 5: return " quadrillion";
            case 4: return " trillion";
            case 3: return " billion";
            case 2: return " million";
            case 1: return " thousand";
            case 0: return "";
        }
    }
	
	public function convertThreeDigit($dig1, $dig2, $dig3)
	{
        $output = "";
        
        if($dig1 == "0" && $dig2 == "0" && $dig3 == "0") return "";
        
        if($dig1 != "0")
        {
            $output .= $this->convertDigit($dig1)." hundred";
            if($dig2 != "0" || $dig3 != "0") $output .= " and ";
        }
        
        if($dig2 != "0") $output .= $this->convertTwoDigit($dig2, $dig3);
        else if($dig3 != "0") $output .= $this->convertDigit($dig3);
        
        return $output;
	}
	
	public function convertTwoDigit($dig1, $dig2)
	{
        if($dig2 == "0")
        {
            switch($dig1)
            {
                case "1": return "ten";
                case "2": return "twenty";
                case "3": return "thirty";
                case "4": return "forty";
                case "5": return "fifty";
                case "6": return "sixty";
                case "7": return "seventy";
                case "8": return "eighty";
                case "9": return "ninety";
            }
        }
        else if($dig1 == "1")
        {
            switch($dig2)
            {
                case "1": return "eleven";
                case "2": return "twelve";
                case "3": return "thirteen";
                case "4": return "fourteen";
                case "5": return "fifteen";
                case "6": return "sixteen";
                case "7": return "seventeen";
                case "8": return "eighteen";
                case "9": return "nineteen";
            }
        }
        else
        {
            $temp = $this->convertDigit($dig2);
            switch($dig1)
            {
                case "2": return "twenty-$temp";
                case "3": return "thirty-$temp";
                case "4": return "forty-$temp";
                case "5": return "fifty-$temp";
                case "6": return "sixty-$temp";
                case "7": return "seventy-$temp";
                case "8": return "eighty-$temp";
                case "9": return "ninety-$temp";
            }
        }
	}
	
	public function convertDigit($digit)
	{
        switch($digit)
        {
            case "0": return "zero";
            case "1": return "one";
            case "2": return "two";
            case "3": return "three";
            case "4": return "four";
            case "5": return "five";
            case "6": return "six";
            case "7": return "seven";
            case "8": return "eight";
            case "9": return "nine";
        }
	}
    
	/** get form 16 required details
	* 1. Employer address
	* 2. Tax configuration details - TDS_Deposit_Method,Org_Type,TDS_Payment_Frequency,Form16_Signatory,
	*   Signatory designation, Signatory father name
	* 3. Employee Job Details
	* 3. Form 16 Distributed Assessment Year (Ex: Assessment Year 2016 -2017)
	* 4. Employee From and To period in the format 'date-Monthname-Year' **/
    public function getOrgEmployeeDetails ($employeeId,$fiscalYrDetails){
		$orgName = $employeeFromPeriod = $employeeToPeriod = $form16DistributedAssYearRange = '';
		$orgAddressDetails = $empDetails = $taxConfigurationDetails = array();
		if(!empty($employeeId) && !empty($fiscalYrDetails)){
			/** Get Org Details **/
			$orgName = isset($this->_orgDetails['Org_Name']) ? $this->_orgDetails['Org_Name'] : '';
			
			/** Get the main branch address */
			$orgAddressDetails = $this->_ehrTables->getOrgAddress();
			
			$fiscalFromDate = $fiscalYrDetails['Fiscal_Start_Date'];
			$fiscalEndDate = $fiscalYrDetails['Fiscal_End_Date'];
			$fiscalEndYear = $fiscalYrDetails['Fiscal_End_Year'];

			/** Get the tax configuration details */
			$taxConfigurationDetails = $this->_db->fetchRow($this->_db->select()->from(array('TC'=>$this->_ehrTables->taxConfiguration),
											array('PAN', 'CIT', 'TAN','TDS_Deposit_Method','Org_Type','TDS_Payment_Frequency','Form16_Signatory'))
											->joinLeft(array('EP'=>$this->_ehrTables->empPersonal), 'EP.Employee_Id=TC.Form16_Signatory',
											array('Sig_Employee_Name'=>new Zend_Db_Expr("CONCAT(EP.Emp_First_Name,' ',EP.Emp_Last_Name)")))
											->joinLeft(array('ED'=>$this->_ehrTables->empDependent), 'EP.Employee_Id=ED.Employee_Id',
											array('Sig_Father_Name'=>new Zend_Db_Expr("CONCAT(ED.Dependent_First_Name,' ',ED.Dependent_Last_Name)")))
											->joinLeft(array('EJ'=>$this->_ehrTables->empJob), 'EJ.Employee_Id=TC.Form16_Signatory',
											array('EJ.Designation_Id'))
											->joinLeft(array('D'=>$this->_ehrTables->designation), 'D.Designation_Id=EJ.Designation_Id',
											array('D.Designation_Name')));
			
			$resignationWhere = $this->_db->quoteInto('J.Emp_Status LIKE ?','Active').
			' OR '.$this->_db->quoteInto('J.Emp_InActive_Date >= ?', $fiscalFromDate).
			' OR '.($this->_db->quoteInto("(R.Approval_Status = 'Approved' AND R.Resignation_Date >= ?)", $fiscalFromDate));

			/** Get employee Details **/
			$empDetails = $this->_db->fetchRow($this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal), array('PAN',
						'Employee_Id','Employee_Name'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name, ' ', P.Emp_Last_Name)")))				
					->joinLeft(array('J'=>$this->_ehrTables->empJob), 'J.Employee_Id=P.Employee_Id',
							array('Date_Of_Join','Designation_Id'))				
					->joinLeft(array('D'=>$this->_ehrTables->designation), 'D.Designation_Id=J.Designation_Id',
							array('Designation_Name'))				
					->joinLeft(array('L'=>$this->_ehrTables->location), 'L.Location_Id=J.Location_Id',
							array('Location_Name','Street1','Street2','Phone','Pincode'))
					->joinLeft(array('EC'=>$this->_ehrTables->empContacts), 'EC.Employee_Id=J.Employee_Id',
							array('cApartment_Name','cStreet_Name','cCity','cState','cCountry','Mobile_No','cPincode'))
					->joinLeft(array('C'=>$this->_ehrTables->city),'C.City_Id=L.City_Id', array('C.City_Name as City'))
					->joinLeft(array('R'=>$this->_ehrTables->resignation), 'J.Employee_Id = R.Employee_Id',array('Resignation_Date'))
					->where('P.Employee_Id = ?', $employeeId)
					->where('P.Form_Status = ?', 1)
					->where($resignationWhere));

			$dateofJoin = $empDetails['Date_Of_Join'];
			$empResignationDate = $empDetails['Resignation_Date'];

			$employeeFromPeriod = date('d-M-Y', strtotime($fiscalFromDate));
			$employeeToPeriod = date('t-M-Y', strtotime($fiscalEndDate));

			/** If date of join is greater than the fiscal start date, employee from period will be the date of join */
    		if(!empty($dateofJoin) && strtotime($dateofJoin) >= strtotime($fiscalFromDate))
			{    				
				$employeeFromPeriod = date('d-M-Y', strtotime($dateofJoin));
			}    		
			
			/** If resignation date is less than the fiscal end date, employee to period will be the resignation date */			
    		if(!empty($empResignationDate) && strtotime($empResignationDate) <= strtotime($fiscalEndDate))
			{
				$employeeToPeriod = date('d-M-Y', strtotime($empResignationDate));
			}			

			/** Assessment year range. If the form16 for the assessment year 2020 will be distributed on the assessment year 2020-2021. So it will
			 * be presented in the form 16
			 */
			$form16DistributedAssYearRange = $fiscalEndYear.'-'.($fiscalEndYear+1);
		}

		$result = array('Org_Name'=>$orgName,'Employer_Address'=>$orgAddressDetails,'Tax_Config_Details'=>$taxConfigurationDetails,
					'Employee_Job_Details'=>$empDetails,'Form16_Distributed_Ass_Yr'=>$form16DistributedAssYearRange,
					'Form16_Emp_From_Date'=>$employeeFromPeriod,'Form16_Emp_To_Date'=>$employeeToPeriod);
		
		return $result;		
	}
	
    /** Form16 - annexure details **/
	public function getForm16TaxableData($employeeId,$fiscalYrDetails){
		$incomeTaxHistoryAmount = 0;

		/** Get the max payslip id and the salary month for the selected assessment year */
		$maxpayslipSO = 'STR_TO_DATE(Salary_Month,"%m,%Y")';

		$empMaxPayMonthInFiscalYear = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->monthlyPayslip, array('Salary_Month','Payslip_Id'))
											->where('Salary_Month IN (?)',$fiscalYrDetails['Fiscal_Months'])
											->where('Employee_Id = ?',$employeeId)
											->order("$maxpayslipSO DESC"));
											
		$empForm16Details = $this->_dbPaySlip->getForm16SummaryDetails($employeeId,$empMaxPayMonthInFiscalYear['Salary_Month'],
								$empMaxPayMonthInFiscalYear['Payslip_Id'],'Monthly',null, $fiscalYrDetails['Fiscal_Months'], 'compliance-form16');

		$empForm16SummaryDetails = $empForm16Details['complianceForm16Summary']['beForm16Summary'];

		$section10ExemptionsFirstPart = array();
		$section10ExemptionsFirstPart[] = array('Investment_Category' => 'House rent allowance',
								'ExemptAmt' => $empForm16SummaryDetails['HRA_Exemption_Amount']);

		/** Get the tax declaration allowance details */
		$dbTaxCalculation = new Default_Model_DbTable_TaxCalculation();
		$section10ExemptionsSecondPart = $dbTaxCalculation->getForm16TaxDeclarationAllowance($employeeId,$empMaxPayMonthInFiscalYear['Salary_Month']);
						
		$empGrossAnnualSalary = $empForm16SummaryDetails['A. Gross Salary'];
		$empAnnualPerquisites = $empForm16SummaryDetails['B. Perks'];
		$empStdDeductionAmount = $empForm16SummaryDetails['Std_Deduction_Amount'];		
		$empAnnualPT = $empForm16SummaryDetails['Annual_Professional_Tax'];
		$empAnnHousePropertyIncome = $empForm16SummaryDetails['Emp_Actual_House_Property_Income'];
		$empCommonExemptionDetails = $empForm16SummaryDetails['Emp_Common_Exemption_Details'];
		$empDecExemptionDetails = $empForm16SummaryDetails['Emp_Declaration_Exemption_Details'];
		$empAnnualTaxWithoutEduCess = $empForm16SummaryDetails['Annual_Tax_Without_Edu_Cess'];
		$empAnnualTaxEduCess = $empForm16SummaryDetails['Annual_Tax_Edu_Cess'];

		/** Get the organization and employee tds details */
		$empTaxableIncomeDetails = $empForm16SummaryDetails['Taxable_Income_Details'];

		$orgEmpTdsDetails = $this->getOrgEmpTdsPaymentDetails($employeeId,$empTaxableIncomeDetails,$fiscalYrDetails['Fiscal_Months']);
		
		return array('Annual_Gross_Salary' => $empGrossAnnualSalary,
			'Annual_Perquisites_Amount' => $empAnnualPerquisites,
			'Gratuity_Amount' => number_format(0.00,2,'.',''),
			'Std_Deduction_Amount' => $empStdDeductionAmount,			
			'Annual_Professional_Tax' => $empAnnualPT,
			'Emp_Actual_House_Property_Income' => $empAnnHousePropertyIncome,
			'Emp_Declaration_Exemption_Details'=>$empDecExemptionDetails,
			'Emp_Common_Exemption_Details'=>$empCommonExemptionDetails,
			'Annual_Tax_Without_Edu_Cess' => $empAnnualTaxWithoutEduCess,
			'Annual_Tax_Edu_Cess' =>$empAnnualTaxEduCess,
			'Section10_Exemptions_First_Part' => $section10ExemptionsFirstPart,
			'Section10_Exemptions_Second_Part' => $section10ExemptionsSecondPart,
			'Emp_Tds_Details' => $orgEmpTdsDetails,
			'Prev_Employment_Gross_Salary'=>$empForm16SummaryDetails['Prev_Employment_Gross_Salary']);
	}

	/**
	 * 	1. Get the employee monthly taxable income, tax deducted
	 *  2. Get the tds payment details for the salary month
	 *  3. And merge the above details based on the salary month
	 */
	public function getOrgEmpTdsPaymentDetails($employeeId,$empTaxableIncomeDetails,$tdsMonthArray){
		$orgEmpTdsPaymentDetails = array();

		/** Get the org tds payment details */
		$empTdsPaymentDetailsQry = $this->_db->select()->from(array('P'=>$this->_ehrTables->tdsPayment), array('P.Salary_Month'))
		->joinLeft(array('MSP'=>$this->_ehrTables->monthlyPayslip),'MSP.Salary_Month = P.Salary_Month',
			array(new Zend_Db_Expr('MAX(STR_TO_DATE(MSP.Salary_Month,"%m,%Y")) as Salary_Month_Date')))
		->joinLeft(array('PT'=>$this->_ehrTables->tdsPaymentTracker),'P.Payment_Id = PT.Payment_Id',
			array('PT.BSR_Code','PT.Challan_SNo','PT.Payment_Status','PT.Receipt_No',
			new Zend_Db_Expr("date_format(PT.Payment_Date, '%d/%m/%Y') as Payment_Date")))
		->where('MSP.Salary_Month IN (?)',$tdsMonthArray)
		->where('MSP.Employee_Id = ?',$employeeId)
		->where('P.Payment_Status = ?','Paid')
		->group('P.Payment_Id');

		$empTdsPaymentDetails = $this->_db->fetchAll($empTdsPaymentDetailsQry);
		$quarterNo = 0;

		$tdsPaymentFrequency = $this->_dbFinancialYr->getTdsPaymentFrequency();
		$tdsPaymentFrequency = !empty($tdsPaymentFrequency) ? strtolower($tdsPaymentFrequency):'';

		if(!empty($tdsPaymentFrequency)){
			$tdsMonthArrayChunks = array_chunk($tdsMonthArray,3);
		}
		
		/** Iterate the fiscal months - tds payment tracker details */
		foreach($empTdsPaymentDetails as $key=>$empSalMonthTdsDetails){
			$empTdsPaymentDetails[$key]['Current_Month_Taxable_Salary'] = 0;
			$empTdsPaymentDetails[$key]['TDS_Paid'] = 0;
			
			$quarterNo = $quarterNo+1;
			$empTdsPaymentDetails[$key]['Quarter_No'] = $quarterNo;
			
			$empTdsPaymentSalMonth = $empSalMonthTdsDetails['Salary_Month'];
			$empTdsPaymentSalMonthInDate = $empSalMonthTdsDetails['Salary_Month_Date'];

			/** Iterate the fiscal months - taxable income details */
			foreach($empTaxableIncomeDetails  as $empSalMonthTaxDetails){
				$empCurrentMonthTaxableSalary = $empSalMonthTaxDetails['Current_Month_Taxable_Salary'];
				$empCurrentMonthTdsPaid = $empSalMonthTaxDetails['TDS_Paid'];
				$empSalMonthInDate = $empSalMonthTaxDetails['Salary_Month_Date'];

				/** If the tds payment frequency is monthly, check the tds payment tracker and the taxable income record month is same */
				if($empTdsPaymentSalMonth === $empSalMonthTaxDetails['Salary_Month'] && $tdsPaymentFrequency === 'monthly'){
					/** Assign the monthly taxable salary and monthly tds paid in the tds payment tracker details array */
					$empTdsPaymentDetails[$key]['Current_Month_Taxable_Salary'] = $empCurrentMonthTaxableSalary;
					$empTdsPaymentDetails[$key]['TDS_Paid'] = $empCurrentMonthTdsPaid;

					$getTdsSummaryQuarterNo = $this->getForm16TdSummaryQuarterNo($empTdsPaymentSalMonth,$tdsMonthArrayChunks);

					if(!empty($getTdsSummaryQuarterNo)){
						$empTdsPaymentDetails[$key]['Quarter_No'] = $getTdsSummaryQuarterNo;
					}
					break;
				}else if($tdsPaymentFrequency === 'quarterly'){
					/** check the taxable income record - salary month is lesser than or equal to the tds payment tracker month */
					if(strtotime($empSalMonthInDate) <= strtotime($empTdsPaymentSalMonthInDate)){
						/** Assign the sum of the all the fiscal month's taxable salary and tds paid which exists for the quarter
						 * in the tds payment tracker details array
						 */
						$empTdsPaymentDetails[$key]['Current_Month_Taxable_Salary'] += $empCurrentMonthTaxableSalary;
						$empTdsPaymentDetails[$key]['TDS_Paid'] += $empCurrentMonthTdsPaid;

						$getTdsSummaryQuarterNo = $this->getForm16TdSummaryQuarterNo($empTdsPaymentSalMonth,$tdsMonthArrayChunks);
						if(!empty($getTdsSummaryQuarterNo)){
							$empTdsPaymentDetails[$key]['Quarter_No'] = $getTdsSummaryQuarterNo;
						} 
					}
				}
			}
		}

		$orgEmpTdsPaymentDetails = $empTdsPaymentDetails;
		return $orgEmpTdsPaymentDetails;
	}

	/** Find the Quarter number using the salary month and the fiscal month,year 
	 * empTdsPaymentSalMonth => 4,2019
	 * tdsMonthArrayChunks => array(4,2019,5,2019,6,2019)
	*/
	public function getForm16TdSummaryQuarterNo($empTdsPaymentSalMonth,$tdsMonthArrayChunks){
		$quarterNo = 0;
		foreach($tdsMonthArrayChunks as $currentTdsMonthsChunksKey => $currentTdsMonthsChunks){
			if(in_array($empTdsPaymentSalMonth,$currentTdsMonthsChunks)){
				$quarterNo = 'Quarter '.($currentTdsMonthsChunksKey+1);
				break;
			}
		}
		return $quarterNo;
	}
	
	/** Get Exemptions/ rebate amount mapped with tax declarations for the assessement year **/
	public function getAggofChapterVIDeductions($taxableSalary, $empId, $salaryMonth, $hourlyEmp = NULL, $sec = NULL)
	{
		$rebateSection = $exemptionSection = $rebateSectionId = $exemptSectionId = array();		
		$empExempt = $arrEmpInvest= $empRebate = $netRebateAmt = $taxDeductAmt = array();
		
		$month = explode(',', $salaryMonth);
		$dbFinanceYr = new Default_Model_DbTable_FinancialYear();
		
		//getting the assessment year based on the payslip month
		$assessmentYear = $dbFinanceYr->getAssessmentYr($month[0], $month[1]);

		$getAge = $this->_db->select()->from($this->_ehrTables->empPersonal, array('Age'=>new Zend_Db_Expr('TIMESTAMPDIFF( YEAR, DOB , curdate( ) )'), 'Gender'))
		->where('Employee_Id = ?', $empId);
		$rowAge = $this->_db->fetchRow($getAge);
		$secId = '';
        if($sec == '')
        {
    		$sectionNameArray = $this->_db->fetchAll($this->_db->select()->from(array('TS'=>$this->_ehrTables->taxSections),
                                                                                array('Section_Id'))
                                                                        ->joinInner(array('GTS'=>$this->_ehrTables->govtTaxSec), 'GTS.Section_Id=TS.Section_Id',
                                                                                    array('Section_Name'=>new Zend_Db_Expr('SUBSTRING_INDEX(Section_Name," ",-1)')))
                                                                        ->where('TS.Agg_Of_ChapterVI =?', 1));
        }
        else
        {
            if( $sec == 'Chapter VI-A')
            {
				$remainingSections = array('Section 16(ii)','Section 16(iii)','Section 80C','Section 80CCC','Section 80CCD');
                $sectionNameArray = $this->_db->fetchAll($this->_db->select()->from(array('GTS'=>$this->_ehrTables->govtTaxSec),
                                                                                array('GTS.Section_Id','GTS.Section_Name'))
                                                        ->joinInner(array('TS'=>$this->_ehrTables->taxSections), 'GTS.Section_Id=TS.Section_Id', array())
                                                        ->where('Section_Name NOT IN (?)',$remainingSections)
                                                        ->where('TS.Agg_Of_ChapterVI =?', 1));
			}
            else{
                $sectionNameArray = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->govtTaxSec,
                                                                                array('Section_Id','Section_Name'))
                                                     ->where('Section_Name =?',$sec));                
            }
        }
        
		foreach($sectionNameArray as $secNameArray) {
			$getEmpDeclaration[$secNameArray['Section_Name']] = array();
			$qryDeclaration = $this->_db->select()->distinct()
												   ->from(array('D'=>$this->_ehrTables->taxDeclaration),
													   array('Assessment_Year','Amount_Declared', 'Amount_Approved', 'D.Approval_Status','D.Employee_Id',
															 'Declaration_Amt'=>new Zend_Db_Expr('Case when D.Approval_Status="Approved"
																										then Amount_Approved
                                                                                                        else Amount_Declared End')))
																										//when D.Approval_Status = "Applied"
																										//then Amount_Declared End')))
											->joinInner(array('I'=>$this->_ehrTables->sectionsInvestment), 'I.Investment_Cat_Id=D.Investment_Cat_Id',
													array('Investment_Category', 'Tax_Section_ID','I.Investment_Cat_Id', 'I.Max_Limit', 'Investment_Percentage',
															'Max_InvestAmt'=> new Zend_Db_Expr('Case when I.Max_Limit IS NOT NULL Then I.Max_Limit else
																	(Investment_Percentage*'.$taxableSalary.')/100 End')))
											->joinLeft(array('E'=>$this->_ehrTables->taxExemptions), 'E.Tax_Section_ID = I.Tax_Section_ID', array('Exemption_SectionId'=>'E.Tax_Section_ID'))
											->joinLeft(array('R'=>$this->_ehrTables->taxRebates), 'R.Tax_Section_ID = I.Tax_Section_ID', array('Rebate_SectionId'=>'R.Tax_Section_ID'))
											->joinLeft(array('TS'=>$this->_ehrTables->taxSections), 'TS.Tax_Section_ID = I.Tax_Section_ID', array(''))
											->joinLeft(array('GTS'=>$this->_ehrTables->govtTaxSec), 'GTS.Section_Id=TS.Section_Id', array('GTS.Section_Name as Tax_Section_Name'))
											->where('I.Assessment_Year <= ?', $assessmentYear)//->where('I.Tax_Status LIKE ?','Active')
											->where('D.Employee_Id = ?',$empId)
											->where('TS.Section_Id = ?', $secNameArray['Section_Id'])
											//->where('TS.Tax_Section_Name LIKE ?', "%$secNameArray%")											
											->where('D.Assessment_Year = ?', $assessmentYear)
											->where('D.Approval_Status != ?','Rejected');
											
			if($sec == '')
				$qryDeclaration->where('TS.Agg_Of_ChapterVI = ?', 1);
			
			$declarationDetails = $this->_db->fetchAll($qryDeclaration);
			
			array_push($getEmpDeclaration[$secNameArray['Section_Name']],$declarationDetails);
			$getEmpDeclaration[$secNameArray['Section_Name']] = $getEmpDeclaration[$secNameArray['Section_Name']][0];
		}
		
		$totalTaxableSalary = $grossAnnualSalary = 0;
		$previousMonthSalary = 0;
		$totalPaidTax= array();
		$dbFinancialYr = new Default_Model_DbTable_FinancialYear();
		$fiscalStartMonth = $dbFinancialYr->getFiscalMonth();

		//getting the financial year based on the payslip month
		//ex: finstartMonth Aprl and finendMonth March, then if payslip generated for jan 2013.
		//then we are taking the finPeriod as Aprl 2012 to Mar 2013.
		$fiscalStartYear = $dbFinancialYr->financialStartYear($month[0], $month[1]);
		$finStartMonthYear = date('Y-m',strtotime($fiscalStartYear.'-'.$fiscalStartMonth['Start_Month']));
		$finEndMonthYear = date('Y-m',strtotime($finStartMonthYear. " + 11 month"));
		$payslipYearMonth = date('Y-m',strtotime($month[1].'-'.$month[0]));

		
		//from financial start month to payslip month, getting the taxable salary for each month
		while(strtotime($finStartMonthYear) < strtotime($payslipYearMonth))
		{
			if(is_null($hourlyEmp))
			{                
				$taxableIncome = $this->_dbPaySlip ->getTaxableIncome(date('n,Y',strtotime($finStartMonthYear)), $empId);
			}
			else
			{
				$taxableIncome = $this->_dbPaySlip ->getTaxableWage(date('n,Y',strtotime($finStartMonthYear)), $empId);
			}
                
			$previousMonthSalary += $taxableIncome[0];
			//storing paid tax amount for past months
			$totalPaidTax[] = $taxableIncome[1];
			$finStartMonthYear = date('Y-m',strtotime($finStartMonthYear. " + 1 month"));
		}
		

		//12-2014 - 07-2014 => 5 months
		$futureMonthCount = ((date('Y',strtotime($finEndMonthYear)) - $month[1]) * 12) + (date('m',strtotime($finEndMonthYear)) - $month[0]);
        
		//$taxableSalary => taxable salary for payslip month
		$futureMonthSalary = $taxableSalary * $futureMonthCount;
    
		//total taxable salary for the whole financial year
		$totalTaxableSalary = $grossAnnualSalary =  ($taxableSalary + $futureMonthSalary) + $previousMonthSalary;    
		
		/** If financial year end month,year is equal to payslip month,year then include the perquisite in taxable salary **/
        $finEndMonthYearSplit = explode('-',$finEndMonthYear);        
        if($month[0] == intval($finEndMonthYearSplit[1]))
        {
            /** get the perquisite amount from the perquisite tracker for an employee based on the assessment year **/
            $YearlyPerquisiteTrackerAmt = $this->_db->fetchOne($this->_db->select()->from(array('PTA'=>$this->_ehrTables->prequisiteTrackerAmt),
                                                                       array('SUM(Perquisite_Amount)'))
                                                                ->joinLeft(array('P'=>$this->_ehrTables->perquisiteTracker),'P.Perquisite_Tracker_Id=PTA.Perquisite_Tracker_Id',
                                                                        array(''))
                                                                ->where('P.Assessment_Year = ?', $assessmentYear)
                                                                ->where('P.Employee_Id = ?',$empId));
            
            $totalTaxableSalary += $YearlyPerquisiteTrackerAmt;
            $grossAnnualSalary  += $YearlyPerquisiteTrackerAmt;
        }
		
        //getting all sectionId from section of investment
        $getInvestSection =  $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->sectionsInvestment, array('Tax_Section_ID'))
                ->where('Assessment_Year <= ?', $assessmentYear)
				/*->where('Tax_Status LIKE ?','Active')*/);


		$taxRegime = $this->_db->fetchOne($this->_db->select()->from(array('SP'=>$this->_ehrTables->monthlyPayslip),array('MFS.Tax_Regime'))
						->joinInner(array('MFS'=>$this->_ehrTables->monthlyForm16Snapshot), 'SP.Payslip_Id=MFS.Payslip_Id',array(''))
						->where('SP.Employee_Id = ?', $empId)
						->where('SP.Salary_Month = ?', $payslipMonth));

		if($taxRegime=='New Regime')						
		{
			//getting the exemption amount, where tax section has no section of investment
			$qryCommonExemption = $this->_db->select()->from(array('E'=>$this->_ehrTables->taxExemptions),
			array('E.Tax_Entity_Id','ExemptionAmount'=> new Zend_Db_Expr('CASE WHEN E.New_Regime_Exemption_Amount > S.Max_Limit
														 THEN S.Max_Limit
														 ELSE E.New_Regime_Exemption_Amount END')));
		}
		else
		{
			//getting the exemption amount, where tax section has no section of investment
			$qryCommonExemption = $this->_db->select()->from(array('E'=>$this->_ehrTables->taxExemptions),
			array('E.Tax_Entity_Id',
				'ExemptionAmount'=> new Zend_Db_Expr('CASE WHEN E.Exemption_Amount > S.Max_Limit
														THEN S.Max_Limit
														ELSE E.Exemption_Amount END')));
		}	
        
		$qryCommonExemption->joinInner(array('S'=>$this->_ehrTables->taxSections), 'S.Tax_Section_ID=E.Tax_Section_ID',array())
							->joinInner(array('TC'=>$this->_ehrTables->taxCategory), 'TC.Tax_Category_Id=E.Tax_Category_ID',array())
							->where('TC.Min_Age <= ?', $rowAge['Age'])
							->where('TC.Max_Age >= ?', $rowAge['Age'])
							->where('S.Assessment_Year <= ?', $assessmentYear)
							//->where('S.Tax_Status LIKE ?','Active')
							->where('E.Assessment_Year <= ?', $assessmentYear)
							//->where('E.Tax_Status LIKE ?','Active')
							->where('TC.Tax_Category_Name IN (?)',array($rowAge['Gender'],'All'));
                                            
        if(!empty($getInvestSection))
		{
			$qryCommonExemption->where('E.Tax_Section_ID NOT IN (?)', $getInvestSection);
		}
		
        $commonExemption = $this->_db->fetchAll($qryCommonExemption);
        
		$taxDeclarationExists = 0;
		if(!empty($getEmpDeclaration) && count($getEmpDeclaration)>0)
		{
			$taxDeclarationExists = 1;
            //for each runs for every section of investment
			foreach($getEmpDeclaration as $empTaxdeclaration)
			{
				$arrEmpInvest = array();
				
				if(!empty($empTaxdeclaration) && count($empTaxdeclaration)>0)
				{
					$totinvestAmt = $totDeclarationAmount = 0;
					
					for($t=0;$t<count($empTaxdeclaration);$t++){
				
						$investmentArray = '';
						if(!empty($empTaxdeclaration[$t]['Max_Limit']))
						{
							//declaration amount = (Amount Approved/Amount_Declared in tax declaration)
							//comparing declaration amount with max limit of section of investment
							if($empTaxdeclaration[$t]['Declaration_Amt']>$empTaxdeclaration[$t]['Max_Limit'])
							{
								$investAmt = $empTaxdeclaration[$t]['Max_Limit'];
							}
							else
							{
								$investAmt = $empTaxdeclaration[$t]['Declaration_Amt'];
							}
						}
						elseif(!empty($empTaxdeclaration[$t]['Investment_Percentage']))
						{						
							$investAmt = ($empTaxdeclaration[$t]['Declaration_Amt']*$empTaxdeclaration[$t]['Investment_Percentage'])/100;						
						}
						$totinvestAmt += $investAmt;
						$totDeclarationAmount += $empTaxdeclaration[$t]['Declaration_Amt'];
						
						$investmentArray = array('Tax_Section_ID'=>$empTaxdeclaration[$t]['Tax_Section_ID'],
										  'Tax_Section_Name'=>$empTaxdeclaration[$t]['Tax_Section_Name'],
										  'Declaration_Amount'=>$empTaxdeclaration[$t]['Declaration_Amt'],
										  'Investment_Category'=>$empTaxdeclaration[$t]['Investment_Category'],
							'Invest_Amt'=>$investAmt, 'Employee_Id'=>$empTaxdeclaration[$t]['Employee_Id']);
						
						if(!empty($empTaxdeclaration[0]['Exemption_SectionId']))
						{
							$investmentArray['Exemption_Id'] = $empTaxdeclaration[0]['Exemption_SectionId'];
							$exemptSectionId[] = $empTaxdeclaration[0]['Exemption_SectionId'];
						}
						
						array_push($arrEmpInvest,$investmentArray);
						
						//$arrEmpInvest = array('Tax_Section_ID'=>$empTaxdeclaration[0]['Tax_Section_ID'],
						//				  'Tax_Section_Name'=>$empTaxdeclaration[0]['Tax_Section_Name'],
						//				  'Declaration_Amount'=>$totDeclarationAmount,
						//				  'Investment_Category'=>$empTaxdeclaration[0]['Investment_Category'],
						//	'Invest_Amt'=>$totinvestAmt, 'Employee_Id'=>$empTaxdeclaration[0]['Employee_Id']);
					}
					
                    
                    
					//it checks whether exemption added for that section
					if(!empty($empTaxdeclaration[0]['Exemption_SectionId']))
					{
						//$arrEmpInvest['Exemption_Id'] = $empTaxdeclaration[0]['Exemption_SectionId'];
						//$exemptSectionId[] = $empTaxdeclaration[0]['Exemption_SectionId'];
						$exemptionSection[] = $arrEmpInvest;
					}
					////it checks whether rebate added for that section
					//if(!empty($empTaxdeclaration[0]['Rebate_SectionId']))
					//{
					//	$arrEmpInvest['Rebate_Id'] = $empTaxdeclaration[0]['Rebate_SectionId'];
					//	$rebateSectionId[] = $empTaxdeclaration[0]['Rebate_SectionId'];
					//	$rebateSection[] = $arrEmpInvest;
					//}
				}
			}
			
			//if exemption added for that section
			if(!empty($exemptSectionId) && count($exemptSectionId)>0 && !empty($exemptionSection) && count($exemptionSection)>0)
			{
				$exemptSectionId = array_unique($exemptSectionId);
			
				foreach($exemptionSection as $exemptInfo)
				{					
					for($e=0;$e<count($exemptInfo);$e++){					
						$sectionExemptAmt = 0;
						
						/** Section Id**/
						$sectionId = $exemptInfo[$e]['Tax_Section_ID'];
				
						if($taxRegime=='New Regime')						
						{
							$qryExemption = $this->_db->select()->from(array('E'=>$this->_ehrTables->taxExemptions), array('New_Regime_Exemption_Amount as Exemption_Amount',
							'E.Tax_Section_ID', 'E.Tax_Entity_ID',
							'Exempted_Amt'=>new Zend_Db_Expr('case when '.$exemptInfo[$e]['Invest_Amt'].' > New_Regime_Exemption_Amount Then New_Regime_Exemption_Amount
									else '.$exemptInfo[$e]['Invest_Amt'] .' End')));						
						}
						else
						{
							// Exempted_Amt = min(Exemption_Amount, total invested amount calculated)
							$qryExemption = $this->_db->select()->from(array('E'=>$this->_ehrTables->taxExemptions), array('Exemption_Amount',
							'E.Tax_Section_ID', 'E.Tax_Entity_ID',
							'Exempted_Amt'=>new Zend_Db_Expr('case when '.$exemptInfo[$e]['Invest_Amt'].' > Exemption_Amount Then Exemption_Amount
									else '.$exemptInfo[$e]['Invest_Amt'] .' End')));
						}
						// Exempted_Amt = min(Exemption_Amount, total invested amount calculated)
						$qryExemption ->joinInner(array('TC'=>$this->_ehrTables->taxCategory), 'TC.Tax_Category_Id=E.Tax_Category_ID',
												array())
												->joinInner(array('TE'=>$this->_ehrTables->taxEntity), 'E.Tax_Entity_ID=TE.Tax_Entity_Id', array('TE.Tax_Entity_Name','TE.Education_Cess'))
												->joinInner(array('S'=>$this->_ehrTables->taxSections), 'S.Tax_Section_ID=E.Tax_Section_ID', array('Max_Limit'))
												->joinLeft(array('GTS'=>$this->_ehrTables->govtTaxSec), 'GTS.Section_Id=S.Section_Id', array('GTS.Section_Name as Tax_Section_Name'))
												->where('TC.Min_Age <= ?', $rowAge['Age'])->where('Max_Age >= ?', $rowAge['Age'])
												->where('E.Tax_Section_ID = ?', $sectionId)
												->where('S.Assessment_Year <= ?', $assessmentYear)//->where('S.Tax_Status LIKE ?','Active')
												->where('E.Assessment_Year <= ?', $assessmentYear)//->where('E.Tax_Status LIKE ?','Active')
												->where('TC.Tax_Category_Name IN (?)',array($rowAge['Gender'],'All'));
												//->where('TC.Tax_Category_Name = ?',$rowAge['Gender']);
						$rowExemption = $this->_db->fetchRow($qryExemption);
						
						if(!empty($rowExemption))
						{
							//comparing Exempted_Amt and maxlimit of that section
							if($rowExemption['Exempted_Amt']>$rowExemption['Max_Limit'])
							{
								$sectionExemptAmt = $rowExemption['Max_Limit'];
							}
							else
							{
								$sectionExemptAmt = $rowExemption['Exempted_Amt'];
							}
							
							/** Exemption Array **/
							$exemptionArray = array('ExemptAmt'=>$sectionExemptAmt,'TaxSectionName'=>$rowExemption['Tax_Section_Name'],
												 'Tax_Section_ID' => $sectionId,'Investment_Category'=>$exemptInfo[$e]['Investment_Category'],
												 'Declaration_Amount'=>$exemptInfo[$e]['Declaration_Amount'],
												 'Invest_Amt'=>$exemptInfo[$e]['Invest_Amt'],
									'Entity'=>$rowExemption['Tax_Entity_ID'],
									'Education_Cess'=>$rowExemption['Education_Cess']);
							
							array_push($empExempt,$exemptionArray);							
							
							/*$empExempt[] = array('ExemptAmt'=>$sectionExemptAmt,'TaxSectionName'=>$rowExemption['Tax_Section_Name'],
												 'Tax_Section_ID' => $sectionId,'Investment_Category'=>$exemptInfo[$e]['Investment_Category'],
												 'Declaration_Amount'=>$exemptInfo[$e]['Declaration_Amount'],
												 'Invest_Amt'=>$exemptInfo[$e]['Invest_Amt'],
									'Entity'=>$rowExemption['Tax_Entity_ID'],
									'Education_Cess'=>$rowExemption['Education_Cess']);*/
						}
					}
				}
				$exemptedAnnualSalary = array();
		
				$totalExempAmt = 0;
				$qryEntity = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->taxEntity, 'Tax_Entity_Id'));
				if(!empty($empExempt) && count($empExempt)>0 && count($qryEntity)>0 && !empty($qryEntity))
				{
					$totalExempAmt = 0;
					
					foreach($qryEntity as $entity)
					{
						foreach($empExempt as $key=>$row)
						{
							if($entity == $row['Entity'])
							{
								$totalExempAmt += $row['ExemptAmt'];
								//$empExempt[$key]['ExemptAmt'] = $totalExempAmt;
							}
						}
					}
					
					$grossAnnualSalary = $totalTaxableSalary - $totalExempAmt;
				}
			}                        
		}
        
		$totalCommonExempAmt = 0;
		//if there are any common exemption
		if(!empty($commonExemption) && count($commonExemption) > 0)
		{
			foreach($commonExemption as $commExemption)
			{
				foreach($empExempt as $key=>$val)
				{
					if($commExemption['Tax_Entity_Id'] == $val['Entity'])
					{
						//subtracting the exemption(which has no section of investment) for the tax entity
						$totalCommonExempAmt += $commExemption['ExemptionAmount'];
					}
				}
			}
			
			$grossAnnualSalary = $grossAnnualSalary - $totalCommonExempAmt;
		}
		
		//if($sec == 'Chapter VI-A')
		//{
			$Conditions =$this->_db->quoteInto('?',$grossAnnualSalary);
			$Conditions .=$this->_db->quoteInto('?',new Zend_Db_Expr(' BETWEEN TR.Min_Range AND TR.Max_Range'));
			
			$getAge = $this->_db->select()->from($this->_ehrTables->empPersonal, array('Age'=>new Zend_Db_Expr('TIMESTAMPDIFF( YEAR, DOB , curdate( ) )'), 'Gender'))
			->where('Employee_Id = ?', $empId);
			$empAge = $this->_db->fetchRow($getAge);
			
			$educationCess = $this->_db->fetchOne($this->_db->select()->from(array('TR'=>$this->_ehrTables->taxRates),
															array(''))
													  ->joinInner(array('TC'=>$this->_ehrTables->taxCategory), 'TC.Tax_Category_Id=TR.Tax_Category_Id',
															array(''))
													  ->joinInner(array('TE'=>$this->_ehrTables->taxEntity), 'TR.Tax_Entity_Id=TE.Tax_Entity_Id',
																  array('Education_Cess'))
													  ->where('TR.Assessment_Year <= ?', "$assessmentYear")
													  //->where('TR.Tax_Status LIKE ?', "Active")
													  ->where('TC.Min_Age <= ?', $empAge['Age'])
													  ->where('TC.Max_Age >= ?', $empAge['Age'])
													  ->where($Conditions)
													  ->where('TE.Entity_Status = ?','Active'));
			
			
		//	return array('TaxDeclarationExists'=>$taxDeclarationExists,'EmpSectionExemptions'=>$empExempt,'EmpSectionRebates'=>$empRebate,'Education_Cess'=>$educationCess);
		//}
		//else{
			return array('TaxDeclarationExists'=>$taxDeclarationExists,'EmpSectionExemptions'=>$empExempt,'EmpSectionRebates'=>$empRebate,'Education_Cess'=>$educationCess);	
		//}
    }
	
	/** get form16 details based on assessment year, employee id or form id
	* 1. Employee Id , Assessment year based on form id
	* 2. Certificate no, last updated on based on employee id and assessment year **/
	public function getForm16Details ($employeeId='', $assessmentYear='', $formId = null){
		/** to get employee id and assessment year in form 16 view, print, pdf **/
		if($formId > 0 && empty($employeeId) && empty($assessmentYear)){			
			$employeeForm16 = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->form16,array('Employee_Id','Assessment_Year'))
									->where('Form_Id = ?', $formId));
		}
		elseif($employeeId > 0 && $assessmentYear > 0 && is_null($formId)){
			/** to get certificate no, last updated on from employee id and assessment year in form 16 view, print, pdf **/
			$employeeForm16 = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->form16,array('Certificate_Number',
																		new Zend_Db_Expr("date_format(Last_Updated_On, '%d-%b-%Y') as Last_Updated_On")))
									->where('Employee_Id = ?', $employeeId)
									->where('Assessment_Year = ?', $assessmentYear));			
		}
		else{			
			$employeeForm16 = '';
		}
		
		return $employeeForm16;
	}
	
	/** form 16 in view, print, pdf**/
	public function form16TDSDetailsPdf($employeeId,$assessmentYear,$format=null){
		$taxConfigDetails = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->taxConfiguration, array('Form16_Signatory','CIT')));
			
		if(!empty($taxConfigDetails['Form16_Signatory']) && !empty($taxConfigDetails['CIT']))
		{
			$fiscalStartEndDates = $this->_dbFinancialYr->fiscalStartEndDate('PHP',$assessmentYear);
			$fiscalMonthYear = $this->_dbFinancialYr->getFiscalMonthYear(null,$assessmentYear,null,1);

			if(!isset($fiscalStartEndDates['finstart']) || !isset($fiscalStartEndDates['finend']) || 
			!((count($fiscalMonthYear) > 0) && is_array($fiscalMonthYear))){
				return array('success' => false, 'msg' => "There seem to be some technical difficulties. Please contact system admin", 'type' => 'warning');
			}

			$fiscalYrDetails = array('Fiscal_Start_Year' => ($assessmentYear-1),
										'Fiscal_End_Year' => $assessmentYear,
										'Fiscal_Start_Date' => $fiscalStartEndDates['finstart'],
										'Fiscal_End_Date' => $fiscalStartEndDates['finend'],
										'Fiscal_Months' => $fiscalMonthYear);

			$orgEmpDetails = $this->getOrgEmployeeDetails ($employeeId,$fiscalYrDetails);
			$empForm16JobDetails = $orgEmpDetails['Employee_Job_Details'];
			$orgTaxConfigDetails = $orgEmpDetails['Tax_Config_Details'];
			$organizationName = $orgEmpDetails['Org_Name'];
			$form16EmployerAddress = $orgEmpDetails['Employer_Address'];
			$form16DistributedAssYrRange = $orgEmpDetails['Form16_Distributed_Ass_Yr'];
			$form16EmpFromDate = $orgEmpDetails['Form16_Emp_From_Date'];
			$form16EmpToDate = $orgEmpDetails['Form16_Emp_To_Date'];

			$form16Details = $this->getForm16Details ($employeeId,$assessmentYear);		
			
			$taxableData = $this->getForm16TaxableData($employeeId,$fiscalYrDetails);
			$empTdsDetails = $taxableData['Emp_Tds_Details'];
			$stdDeductionAmount = $taxableData['Std_Deduction_Amount'];
			$professionalTax = $taxableData['Annual_Professional_Tax'];
			$empGrossAnnualSalary = $taxableData['Annual_Gross_Salary'];
			$empAnnualGratuityAmount = $taxableData['Gratuity_Amount'];
			$empAnnualPerquisitesAmount = $taxableData['Annual_Perquisites_Amount'];
			$empIncomeFromOtherEmployer = $taxableData['Prev_Employment_Gross_Salary'];
			$empSection10ExemptionsI = $taxableData['Section10_Exemptions_First_Part']; //Section 10 Exempted allowance
			$empSection10ExemptionsII = $taxableData['Section10_Exemptions_Second_Part']; //Section 10 Exempted allowance
			$empHousePropertyIncome = $taxableData['Emp_Actual_House_Property_Income'];
			$empOtherIncome = number_format(0.00,2,'.','');
			$taxOnTotalIncome = $taxableData['Annual_Tax_Without_Edu_Cess'];
        	$edForTax = $taxableData['Annual_Tax_Edu_Cess'];
			$aggDeductionDetails = $taxableData['Emp_Declaration_Exemption_Details'];
			$sec89ReliefAmt = number_format(0.00,2,'.','');

			$dbPersonal = new Employees_Model_DbTable_Personal();
			$currencySymbol = $dbPersonal->currencySymbol($employeeId);
			$currency = '';
			if(!empty($currencySymbol)){
				$currency = '( '.$currencySymbol.' )';
				$currencySymbol = $currencySymbol.' ';
			}else{
				$currencySymbol = '';
			}
			
			if($format == 'print'){
				$fontsize ="12";
			}
			else{
				$fontsize ="11";
			}

			$totSalMonthTaxableSalary = number_format(0.00,2,'.','');
			$totTdsDeductedDeposited = number_format(0.00,2,'.','');
			
			/** Style for table **/
			$form16BoldStyle = "font-weight: bold;";
			$tablePdfcss = 'border-collapse: collapse;border-spacing: none;font-size:'.$fontsize.'px;border-left: 1px solid black;border-right: 1px solid black;border-bottom: none;border-top: 1px solid black;';
			$tdbtRtcss = 'border-right: 1px solid black;border-top: 1px solid black;';
			$tdbtRtHeaderscss = 'border-right:none;border-bottom: none;border-top: none;border-left: none;';

			$form16BFooterCss="border-bottom: 1px solid black;border-right: 1px solid black;".$form16BoldStyle;
			$form16BFooterValcss = 'border-right: 1px solid black;border-bottom: 1px solid black;';
			
			$exportStr = '';
			$exportStr .= '<div style = "margin-left:3%;width: 98%">';
			//$exportStr .=  '<p style="font-size:16px;text-align:center;"> FORM NO. 16</p>';
			//$exportStr .=  '<p style="font-size:15px;text-align:center;"> [See rule 31(1)(a)] </p>';
			//$exportStr .=  '<p style="font-size:16px;text-align:center;"> <b>PART A</b> </p>';
			
			$exportStr .= '<div style = "margin-bottom:3%;">';
			$exportStr .= '<table style="width:98%;font-size:14px;" >';		
			$exportStr .= '<tr><td colspan="6" style ="height:20px;'.$tdbtRtHeaderscss.'text-align:center;"><b>FORM NO. 16</b></td></tr>';		
			$exportStr .= '<tr><td colspan="6" style ="'.$tdbtRtHeaderscss.'text-align:center;">[See rule 31(1)(a)] <br> <b>PART A</b></td></tr>';
			$exportStr .= '</table></div>';				
			
			/** Certificate no and last updated on **/
			//$exportStr .= '<table style="border-collapse:collapse;border-color:1px solid #000;width:98%" cellpadding="0" border=1>';
			$exportStr .= '<div><table style="'.$tablePdfcss.'width:98%;" >';		
			$exportStr .= '<tr><th colspan="6" style ="'.$tdbtRtcss.'text-align:center;">Certificate under section 203 of the Income-tax Act, 1961 for tax deducted at source on salary</th></tr>';		
			$exportStr .= '<tr><th colspan="3" style ="'.$tdbtRtcss.'text-align:center;">Certificate No.</th><th colspan="3" style ="'.$tdbtRtcss.'text-align:center;">Last updated on</th></tr>';
			$exportStr .= '<tr height="20px"><td colspan="3" style ="'.$tdbtRtcss.'text-align:center;">'.$form16Details['Certificate_Number'].'</td><td colspan="3" style ="'.$tdbtRtcss.'text-align:center;">'.$form16Details['Last_Updated_On'].'</td></tr>';
			$exportStr .= '<tr><th colspan="3" style ="'.$tdbtRtcss.'text-align:center;">Name and address of the Employer </th><th colspan="3" style ="'.$tdbtRtcss.'text-align:center;">Name and address of the Employee</th></tr>';		
			
			/** Employer and Employee address **/
			if(!empty($form16EmployerAddress) && !empty($empForm16JobDetails)){
				if(!empty($empForm16JobDetails['cApartment_Name'])){
					$employeeStreet = $empForm16JobDetails['cApartment_Name'].', '.$empForm16JobDetails['cStreet_Name'];
				}
				else{
					$employeeStreet = $empForm16JobDetails['cStreet_Name'];
				}
			
			$employerPincode = (!empty($empForm16JobDetails['Pincode'])) ? '- '.$empForm16JobDetails['Pincode'] : '';	
			$employeePincode = (!empty($empForm16JobDetails['cPincode'])) ? '- '.$empForm16JobDetails['cPincode'] : '';		
			
			$widthEmpStreet = ($format == 'print') ? "width: 55.2%;" : '';

			$exportStr .= '<tr><td colspan="3" style="'.$tdbtRtcss.'" >'.$organizationName.
							','.$form16EmployerAddress['Street1'].', '.$form16EmployerAddress['Street2'].
							',<br>'.$form16EmployerAddress['City_Name'].$employerPincode.
							'. <br>Mobile No: '.$form16EmployerAddress['Phone'].'</td>';
			$exportStr .= '<td colspan="3" style="'.$tdbtRtcss.$widthEmpStreet.'">'.$empForm16JobDetails['Employee_Name'].'<br>'.$employeeStreet.', 
								<br>'.$empForm16JobDetails['cCity'].$employeePincode.'. </td></tr>';
			}					
			else{
				$exportStr .= '<tr height =20px><td colspan="3" style="'.$tdbtRtcss.'"></td>';
				$exportStr .= '<td colspan="3" style="'.$tdbtRtcss.'"></td></tr>';							
			}
			
			/** Employer PAN, TAN and Employee PAN **/
			$exportStr .= '<tr><th style ="'.$tdbtRtcss.'text-align:center;">PAN of the Deductor</th>';
			$exportStr .= '<th colspan="2" style ="'.$tdbtRtcss.'text-align:center;">TAN of the Deductor</th>';
			$exportStr .= '<th style ="'.$tdbtRtcss.'text-align:center;">PAN of the Employee</th>';
			$exportStr .= '<th colspan="2" style ="'.$tdbtRtcss.'text-align:center;">Employee Reference No.<br>provided by the Employer<br>(If available)</th></tr>';
			if(!empty($orgTaxConfigDetails)){
				$exportStr .= '<tr height =20px ><td style ="'.$tdbtRtcss.'text-align:center;">'.$orgTaxConfigDetails['PAN'].'</td><td colspan="2" style ="'.$tdbtRtcss.'text-align:center;">'.$orgTaxConfigDetails['TAN'].'</td>';
			}
			else{
				$exportStr .= '<tr height =20px ><td style="'.$tdbtRtcss.'"></td><td style="'.$tdbtRtcss.'" colspan="2"></td>';
			}
			if(!empty($orgTaxConfigDetails)){
				$exportStr .= '<td style ="'.$tdbtRtcss.'text-align:center;">'.$empForm16JobDetails['PAN'].'</td><td colspan="2" style ="'.$tdbtRtcss.'text-align:center;">'.$empForm16JobDetails['Employee_Id'].'</td></tr>';
			}
			else{
				$exportStr .= '<td style="'.$tdbtRtcss.'"></td><td colspan="2" style="'.$tdbtRtcss.'"></td></tr>';
			}
			
			/** Income Tax Office Address **/
			$exportStr .= '<tr><th colspan="3" style="'.$tdbtRtcss.'">CIT (TDS)</th><th style ="'.$tdbtRtcss.'text-align:center;">Assessment Year</th><th colspan="2" style ="'.$tdbtRtcss.'text-align:center;">Period with the Employer</th></tr>';
			if(!empty($orgTaxConfigDetails)){
				$exportStr .= '<tr><td colspan="3" style="'.$tdbtRtcss.'border-bottom: none;text-align:center;"></td>
								<td style="'.$tdbtRtcss.'border-bottom: none;"></td>
								<th style ="'.$tdbtRtcss.'text-align:center;">From</th>
								<th style ="'.$tdbtRtcss.'text-align:center;">To</th></tr>';							
				
				if($format == 'print'){
					$exportStr .= '<tr><td colspan="3" style="width: 45.2%;border-top: none;border-right: 1px solid black;border-bottom: none;">'.$orgTaxConfigDetails['CIT'].'</td>';
				}
				else{
					$exportStr .= '<tr><td colspan="3" style="border-top: none;border-right: 1px solid black;border-bottom: none;">'.$orgTaxConfigDetails['CIT'].'</td>';
				}
				
				$exportStr .= '	<td style="text-align:center;border-right: 1px solid black;border-top: none;border-bottom: none;">'.$form16DistributedAssYrRange.'</td>
								<td style ="'.$tdbtRtcss.'text-align:center;border-bottom: none;">'.$form16EmpFromDate.'</td>
								<td style ="'.$tdbtRtcss.'text-align:center;border-bottom: none;">'.$form16EmpToDate.'</td></tr>';
			}
			else{
				$exportStr .= '<tr><td colspan="3" style="border-top: none;border-bottom: none;"></td><td style="'.$tdbtRtcss.'border-top: none;border-bottom: none;"></td><th style ="text-align:center;">From</th><th style ="text-align:center;">To</th></tr>';
				$exportStr .= '<tr><td colspan="3" style="border-top: none;border-bottom: none;"><br></td><td style="'.$tdbtRtcss.'text-align:center;border-top: none;border-bottom: none;"></td><td style ="text-align:center;border-bottom: none;"></td><td style ="text-align:center;border-bottom: none;"></td></tr>';
			}

			/** Summary of amount paid - Taxable Salary, Total tax deposited **/
			$exportStr .= '<tr><td colspan="3" style="'.$tdbtRtcss.'border-top: none;"><br></td>
							<td style="'.$tdbtRtcss.'border-top: none;"></td>
							<td style ="'.$tdbtRtcss.'border-top: none;"></td>
							<td style ="'.$tdbtRtcss.'border-top: none;"></td></tr>';		
			$exportStr .= '<tr><th colspan="6" style ="'.$tdbtRtcss.'text-align:center;">Summary of amount paid/credited and tax deducted at source thereon in respect of the employee</th></tr>';

			$exportStr .= '<tr><th colspan="2" style ="'.$tdbtRtcss.'text-align:center;">Quarter(s)</th>';
			$exportStr .= '<th style ="'.$tdbtRtcss.'text-align:center;">Receipt Numbers of<br>original quarterly<br>statements of TDS<br>under sub-section (3) of<br>section 200</th>';
			$exportStr .= '<th style ="'.$tdbtRtcss.'text-align:center;">Amount<br>paid/credited</th>';
			$exportStr .= '<th style ="'.$tdbtRtcss.'text-align:center;">Amount of tax<br>deducted'.$currency.'</th>';

			if($format == 'print'){
				$exportStr .= '<th style ="border-bottom: 1px solid black;text-align:center;">Amount of tax <br> deposited/remitted'.$currency.'</th></tr>';
			}
			else{
				$exportStr .= '<th style ="border-bottom: 1px solid black;border-top: 1px solid black;text-align:center;">Amount of tax deposited/remitted'.$currency.'</th></tr>';
			}

			/** TDS deducted, deposited Summary */
			if(!empty($empTdsDetails)){
				$sumCount = count($empTdsDetails);
				for($sd=0;$sd<$sumCount;$sd++){					
					$tdsDeductedDeposited = $empTdsDetails[$sd]['TDS_Paid'];
					$tdsDeductedDeposited = (!empty($tdsDeductedDeposited)) ? number_format($tdsDeductedDeposited,2,'.','') : number_format(0.00,2,'.','');

					$salMonthTaxableSalary = $empTdsDetails[$sd]['Current_Month_Taxable_Salary'];
					$salMonthTaxableSalary = (!empty($salMonthTaxableSalary)) ? number_format($salMonthTaxableSalary,2,'.','') : number_format(0.00,2,'.','');

					$totSalMonthTaxableSalary += $salMonthTaxableSalary;
					$totTdsDeductedDeposited += $tdsDeductedDeposited;

					$summaryQuarterNo = $empTdsDetails[$sd]['Quarter_No'];
					$summaryTdsReceiptNo = $empTdsDetails[$sd]['Receipt_No'];
	
					$exportStr .= '<tr height =20px><td colspan="2" style ="'.$tdbtRtcss.'text-align:center;">'.$summaryQuarterNo.'</td><td style ="'.$tdbtRtcss.'text-align:center;">'.$summaryTdsReceiptNo.'</td><td style ="'.$tdbtRtcss.'text-align:center;">'.$salMonthTaxableSalary.'</td><td style ="'.$tdbtRtcss.'text-align:center;">'.$tdsDeductedDeposited.'</td><td style ="'.$tdbtRtcss.'text-align:center;">'.$tdsDeductedDeposited.'</td></tr>';					
				}
			}

			$exportStr .= '<tr><th colspan="2" style ="'.$tdbtRtcss.'text-align:center;">Total'.$currency.'</th><td style="'.$tdbtRtcss.'"></td><td style ="'.$tdbtRtcss.'text-align:center;">'.$totSalMonthTaxableSalary.'</td><td style ="'.$tdbtRtcss.'text-align:center;">'.$totTdsDeductedDeposited.'</td><td style ="'.$tdbtRtcss.'text-align:center;">'.$totTdsDeductedDeposited.'</td></tr>';
			$exportStr .= '</table>';

			/** Now form24G is not created - future enhancement **/
			$exportStr .= '<table style="'.$tablePdfcss.'width:98%;" >';
			$exportStr .= '<tr height =20px><td colspan="6" style ="'.$tdbtRtcss.'"> </td></tr>';
			$exportStr .= '</table>';
			
			/** Details of Tax deducted through Challan - Receipt No, BSR code, Tax Deposited Date, Challan Serial Number
			* OLTAS status **/
			$exportStr .= '<table style="'.$tablePdfcss.'width:98%;" >';					
			$exportStr .= '<tr><th colspan="6" style ="'.$tdbtRtcss.'text-align:center;">II. DETAILS OF TAX DEDUCTED AND DEPOSITED IN THE<br>
								CENTRAL GOVERNMENT ACCOUNT THROUGH CHALLAN (The deductor to provide payment wise details of tax deducted and deposited with respect to the deductee)</th></tr>';
			
			if($format=='print'){
				$exportStr .= '<tr><th style ="'.$tdbtRtcss.'text-align:center;width:20.5%;border-bottom:none;">Sl. No.</th>';
				$exportStr .= '<th style ="'.$tdbtRtcss.'text-align:center;width:25%;border-bottom:none;">Tax Deposited in</th>';
			}
			else{
				$exportStr .= '<tr><th style ="'.$tdbtRtcss.'text-align:center;border-bottom:none;">Sl. No.</th>';
				$exportStr .= '<th style ="'.$tdbtRtcss.'text-align:center;border-bottom:none;">Tax Deposited in</th>';
			}
			$exportStr .= '<th colspan="4" style ="'.$tdbtRtcss.'text-align:center;">Challan Identification Number (CIN)</th></tr>';
			$exportStr .= '<tr><td style="'.$tdbtRtcss.'border-top:none;"></td><th style ="'.$tdbtRtcss.'border-top:none;text-align:center;">respect of the deductee'.$currency.'</th>';
			$exportStr .= '<th style ="'.$tdbtRtcss.'text-align:center;">BSR Code of the Bank Branch</th>';
			$exportStr .= '<th style ="'.$tdbtRtcss.'text-align:center;">Date on which tax deposited (dd/mm/yyyy)</th>';//   
			$exportStr .= '<th style ="'.$tdbtRtcss.'text-align:center;">Challan Serial Number</th>';
			$exportStr .= '<th style ="'.$tdbtRtcss.'text-align:center;">Status of matching with OLTAS</th></tr>';

			$totTdsDeductedDeposited = number_format(0,2,'.','');;

			/** Present the details of Tax deducted through Challan */
			if(!empty($empTdsDetails)){
				$sno = $totalTaxDepositedAmount = 0; 

				for($t=0;$t<count($empTdsDetails);$t++){
					$tdsPaymentBsrCode = $empTdsDetails[$t]['BSR_Code'];
					$tdsPaymentPaidDate = $empTdsDetails[$t]['Payment_Date'];
					$tdsDeductedDeposited = $empTdsDetails[$t]['TDS_Paid'];
					$tdsPaymentChallanSno = $empTdsDetails[$t]['Challan_SNo'];
					$tdsPaymentPaidStatus = $empTdsDetails[$t]['Payment_Status'];
					$tdsDeductedDeposited = (!empty($tdsDeductedDeposited)) ? number_format($tdsDeductedDeposited,2,'.','') : number_format(0.00,2,'.','');

					$totTdsDeductedDeposited += $tdsDeductedDeposited;

					$sno += 1;
						
					$exportStr .= '<tr height =20px style ="text-align:center;"><td style="'.$tdbtRtcss.'">'.$sno.'</td><td style ="'.$tdbtRtcss.'text-align:center;">'.$tdsDeductedDeposited.'</td><td style ="'.$tdbtRtcss.'text-align:center;">'.$tdsPaymentBsrCode.'</td><td style ="'.$tdbtRtcss.'text-align:center;">'.$tdsPaymentPaidDate.'</td><td style ="'.$tdbtRtcss.'text-align:center;">'.$tdsPaymentChallanSno.'</td><td style ="'.$tdbtRtcss.'text-align:center;">'.$tdsPaymentPaidStatus.'</td></tr>';
				}
			}
			$exportStr .= '<tr><th style ="'.$tdbtRtcss.'text-align:center;">Total '.$currency.'</th><td style ="text-align:center;'.$tdbtRtcss.'">'.$totTdsDeductedDeposited.'</td><td colspan="4" style ="'.$tdbtRtcss.'"></td></tr>';
			
			$city = (!empty($empForm16JobDetails['City'])) ? $empForm16JobDetails['City'] : '';		 
			$date = (!empty($empForm16JobDetails)) ? date('d/m/Y') : '';
			$totdepositAmount = (!empty($empForm16JobDetails)) ? round($totTdsDeductedDeposited) : 0;
			$totdepositAmountwords = $this->convertNumber($totdepositAmount);
			$totDepositedAmountInWords = (!empty($totdepositAmountwords) ? $totdepositAmountwords :'Zero');
			$signatoryEmployeeName = (!empty($orgTaxConfigDetails['Sig_Employee_Name'])) ? $orgTaxConfigDetails['Sig_Employee_Name'] : '……………………………';
			$signatoryDependentName = (!empty($orgTaxConfigDetails['Sig_Father_Name'])) ? $orgTaxConfigDetails['Sig_Father_Name'] : '……………………………';
			$signatoryDesignationName = (!empty($orgTaxConfigDetails['Designation_Name'])) ? $orgTaxConfigDetails['Designation_Name'] : '……………………………';
			
			$exportStr .= '</table>';
			
			/** Verification of Form 16 - Part A**/
			$verificationWidth = ($format == 'print') ? "width:45.5%;" : "width:50%;";

			$exportStr .= '<table style="'.$tablePdfcss.'width:98%;" >';		
			//$exportStr .= '<table style="border-collapse:collapse;border-bottom:1px solid #000;width:98%;" cellpadding="0" border=1>';
			$exportStr .= '<tr><th colspan="6" style ="text-align:center;border-bottom: none;">Verification</th></tr>';
			$exportStr .= '<tr><td style="'.$tdbtRtcss.'" colspan="6">I, '.$signatoryEmployeeName.' son/daughter of '. $signatoryDependentName .' working in the capacity of '
							.$signatoryDesignationName .' (designation) do hereby certify that a sum of '.$currencySymbol.$totdepositAmount.' ['
							.$currencySymbol.$totDepositedAmountInWords.' (in words)]
							has been deducted and deposited to the credit of the Central Government. I further certify that
							the information given above is true, complete and correct and is based on the books of
							account, documents, TDS statements, TDS deposited and other available records.</td></tr>';
			$exportStr .= '<tr><td colspan="3" style="'.$verificationWidth.'border-right: 1px solid black;border-left: 1px solid black;border-top: 1px solid black;">Place:  '.$city.'</td>
						   <td colspan="3" style ="text-align:center;border-right: 1px solid black;border-left: 1px solid black;border-top: 1px solid black;">'.$signatoryEmployeeName.'</td></tr>';
			$exportStr .= '<tr><td colspan="3" style="'.$verificationWidth.'border-right: 1px solid black;border-left: 1px solid black;border-top: 1px solid black;">Date:  '.$date.'</td>
						   <td colspan="3" style ="text-align:center;border-right: 1px solid black;border-left: 1px solid black;">(Signature of person responsible for deduction of tax)</td></tr>';
			$exportStr .= '<tr><td colspan="3" style="'.$verificationWidth.'border-right: 1px solid black;border-bottom: 1px solid black;border-left: 1px solid black;border-top: 1px solid black;">Designation:   '.$signatoryDesignationName.'</td>
						   <td colspan="3" style="border-right: 1px solid black;border-bottom: 1px solid black;border-left: 1px solid black;border-top: 1px solid black;">Full Name:    '.$signatoryEmployeeName.'</td></tr>';
			$exportStr .= '</table></div>';
			
			$exportStr .= '<div class="form16VNotes" style = "margin-top:3%;margin-left:2%;font-size:'.$fontsize.'px;">';		
			$exportStr .= '<table style="width:98%;" >';
			$exportStr .= '<tr><td style="font-size:12px;"><b>Notes</b></td></tr>';		
			$exportStr .= '<tr><td>1. Government deductors to fill information in item <b>I</b> if tax is paid without production of an
								income-tax challan and in item <b>II</b> if tax is paid accompanied by an income-tax challan.</td></tr>';
			$exportStr .= '<tr><td>2. Non-Government deductors to fill information in item <b>II</b>.</td></tr>';
			$exportStr .= '<tr><td>3. The deductor shall furnish the address of the Commissioner of Income-tax (TDS) having
								jurisdiction as regards TDS statements of the assessee.</td></tr>';
			$exportStr .= '<tr><td>4. If an assessee is employed under one employer only during the year, certificate in Form No.
								16 issued for the quarter ending on 31st March of the financial year shall contain the details of tax deducted and deposited for all the quarters of the financial year. </td></tr>';
			$exportStr .= '<tr><td>5. If an assessee is employed under more than one employer during the year, each of the
								employers shall issue Part A of the certificate in Form No. 16 pertaining to the period for
								which such assessee was employed with each of the employers. Part B (Annexure) of the
								certificate in Form No.16 may be issued by each of the employers or the last employer at
								the option of the assessee. </td></tr>';
			$exportStr .= '<tr><td>1. Government deductors to fill information in item <b>I</b> if tax is paid without production of an
								income-tax challan and in item <b>II</b> if tax is paid accompanied by an income-tax challan.</td></tr>';
			$exportStr .= '<tr><td>6. In items <b>I</b> and <b>II</b>, in column for tax deposited in respect of deductee,
								furnish total amount of TDS and education cess.</td></tr>';		
			$exportStr .= '</table></div>';
			
			$exportStr .= '</div>';
			//$exportStr .= '<div style=\'page-break-after:always;\'>&nbsp;</div>';
			//$exportStr .='<br>';			
			
			// Form 16 Annexure - Part B
			$annexureTotal1 = $annexureTotal2 = $annexureTotal3 = $annexureTotal5 = $entertaimentAllowance = $annexureTotal6 = $annexureTotal5 = $annexureTotal7 = $annexureTotal8 = $annexureTotal9 = $annexureTotal11 = number_format(0.00,2,'.','');			
			$aggTotalAmount = $totPartISec10ExemptionAmount = $totPartIISec10ExemptionAmount = number_format(0.00,2,'.','');

			//Sum the gross salary, perquisites and gratuity
			$annexureTotal1 = number_format(($empGrossAnnualSalary + $empAnnualGratuityAmount + $empAnnualPerquisitesAmount),2,'.','');

			//orginal css
			$borderbt="border-bottom: none;border-top: none;border-right: 1px solid black;padding-top: 2px;";
			$borderValbt="border-bottom: none;border-top: none;border-right: 1px solid black;padding-top: 2px;".$form16BoldStyle;
			$borderbtAllowanceHeader ="border-bottom: none;border-top: 1px solid black;border-right: 1px solid black;padding-top: 2px;";
			$borderb="border-bottom: none;padding-top: 2px;border-right: 1px solid black;";//padding-left:10px;			
			
			$exportStr .= '<div class="form16VHead" style = "margin-left:3%;width: 95%;page-break-inside: avoid;">';
			$exportStr .=  '<p style="font-size:14px;text-align:center;"> <b>PART B (Annexure)</b> </p>';
			
			$exportStr .= '<table style="'.$tablePdfcss.'width:98%;" >';	
			
			//$exportStr .= '<table style="margin-left: 3%;border-collapse:collapse;border-bottom:1px solid #000;width:98%;" cellpadding="0" border=1>';			
			$exportStr .= '<tr><th colspan="5" style ="padding-top: 2px;border-bottom: 1px solid black;border-right: 1px solid black;">Details of Salary paid and any other income and tax deducted</th></tr>';
			$exportStr .= '<tr><td colspan="2" style ="'.$borderb.'">1. Gross Salary</td><td style ="'.$borderb.'"></td><td style ="'.$borderb.'"></td><td style ="'.$borderb.'"></td></tr>';
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'padding-left:10px;">(a) Salary as per provisions contained in sec.17(1)</td><td style ="'.$borderValbt.'">'.$currencySymbol.$empGrossAnnualSalary.'</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td></tr>';
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'padding-left:10px;">(b) Value of perquisites u/s 17(2) (as per Form No.12BA, wherever applicable)</td><td style ="'.$borderValbt.'">'.$currencySymbol.$empAnnualPerquisitesAmount.'</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td></tr>';
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'padding-left:10px;">(c) Profits in lieu of salary under section 17(3)(as per Form No.12BA, wherever applicable)</td><td style ="'.$borderValbt.'">'.$currencySymbol.$empAnnualGratuityAmount.'</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td></tr>';
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'padding-left:10px;">(d) Total</td><td style ="'.$borderbt.'"></td><td style ="'.$borderValbt.'">'.$currencySymbol.$annexureTotal1.'</td><td style ="'.$borderbt.'"></td></tr>';
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'padding-left:10px;">(e) Reported total amount of salary received from other employer(s)</td><td style ="'.$borderbt.'"></td><td style ="'.$borderValbt.'">'.$currencySymbol.$empIncomeFromOtherEmployer.'</td><td style ="'.$borderbt.'"></td></tr>';
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbtAllowanceHeader.'">2. Less: Allowance to the extent exempt u/s 10</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td></tr>';
			$exportStr .= '<tr><td colspan="2" style ="'.$tdbtRtcss.'border-top: 1px solid black;">Allowance</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td></tr>';			

			$sec10AlphabetsStr = 'a';
			$allowancesSummaryFormula = '';

			/** Section 10 Exemptions details - Part I*/
			for($tsa=0;$tsa<count($empSection10ExemptionsI);$tsa++){
				$allowanceExemptAmtPartI = $empSection10ExemptionsI[$tsa]['ExemptAmt'];
				$totPartISec10ExemptionAmount += $allowanceExemptAmtPartI;

				$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'padding-left:10px;">('.$sec10AlphabetsStr.') '.$empSection10ExemptionsI[$tsa]['Investment_Category'].'</td><td style ="'.$borderValbt.'">'.$currencySymbol.$allowanceExemptAmtPartI.'</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td></tr>';
				//Formula for the section 10 exemption part-I allowances
				if($tsa==0){
					$allowancesSummaryFormula .= '2('.$sec10AlphabetsStr.')';//2(a)
				}else{
					$allowancesSummaryFormula .= '+2('.$sec10AlphabetsStr.')';//2(a)
				}
				$sec10AlphabetsStr++;
			}

			/** Section 10 Exemptions details - Part II */
			for($tsa=0;$tsa<count($empSection10ExemptionsII);$tsa++){
				$allowanceExemptAmtPartII = $empSection10ExemptionsII[$tsa]['ExemptAmt'];
				$totPartIISec10ExemptionAmount += $allowanceExemptAmtPartII;
				
				$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'padding-left:10px;">('.$sec10AlphabetsStr.') '.$empSection10ExemptionsII[$tsa]['Investment_Category'].'</td><td style ="'.$borderValbt.'">'.$currencySymbol.$allowanceExemptAmtPartII.'</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td></tr>';
				//Formula for the section 10 exemption part-II allowances
				if($tsa==0 && empty($allowancesSummaryFormula)){
					$allowancesSummaryFormula .= '2('.$sec10AlphabetsStr.')';//2(a)
				}else{
					$allowancesSummaryFormula .= '+2('.$sec10AlphabetsStr.')';//2(a)
				}
				$sec10AlphabetsStr++;
			}

			$totPartIISec10ExemptionAmount = number_format($totPartIISec10ExemptionAmount,2,'.','');

			$annexureTotal2 = number_format(($totPartISec10ExemptionAmount + $totPartIISec10ExemptionAmount),2,'.',''); //sum of section 10 exemption amount
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'padding-left:10px;">('.$sec10AlphabetsStr.')  Total amount of exemption claimed under section 10 ['.$allowancesSummaryFormula.']</td><td style ="'.$borderbt.'"></td><td style ="'.$borderValbt.'">'.$currencySymbol.$annexureTotal2.'</td><td style ="'.$borderbt.'"></td></tr>';

			/** Gross salary - sec 10 Exemptions **/
			$annexureTotal3 = (number_format($annexureTotal1 - $annexureTotal2,2,'.',''));	
			$totalAmtOfSalaryReceivedFromCurrentEmployerFormula = '1(d)-2('.$sec10AlphabetsStr.')';//1(d)-2(d)
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'padding-top:2px;">3. Total amount of salary received from current employer ['.$totalAmtOfSalaryReceivedFromCurrentEmployerFormula.']</td><td style ="'.$borderbt.'"></td><td style ="'.$borderValbt.'">'.$currencySymbol.$annexureTotal3.'</td><td style ="'.$borderbt.'"></td></tr>';

			/** Standard deduction + Professional Tax + Entertainment Allowance **/
			$annexureTotal5 = number_format(($professionalTax + $entertaimentAllowance + $stdDeductionAmount),2,'.','');			
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'">4. Deductions :</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td></tr>';
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'padding-left:10px;">(a) Standard Deduction under section 16(ia)</td><td style ="'.$borderValbt.'">'.$currencySymbol.$stdDeductionAmount.'</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td></tr>';
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'padding-left:10px;">(b) Entertainment allowance under section 16(ii)</td><td style ="'.$borderValbt.'">'.$currencySymbol.$entertaimentAllowance.'</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td></tr>';
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'padding-left:10px;">(c) Tax on employment under section 16(iii)</td><td style ="'.$borderValbt.'">'.$currencySymbol.$professionalTax.'</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td></tr>';
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'">5. Total amount of deductions under section 16[4(a)+4(b)+4(c)]</td><td style ="'.$borderbt.'"></td><td style ="'.$borderValbt.'">'.$currencySymbol.$annexureTotal5.'</td><td style ="'.$borderbt.'"></td></tr>';

			/** (Gross salary - sec 10 Exemptions) + Income from other employer - (Std Deduction + Professional Tax + Entertainment Allowance) **/
			$annexureTotal6 = number_format(($annexureTotal3 + $empIncomeFromOtherEmployer - $annexureTotal5),2,'.','');
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'padding-top:3px;">6. Income chargeable under the head ‘Salaries’ [3+1(e)-5]</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td><td style ="'.$borderValbt.'">'.$currencySymbol.$annexureTotal6.'</td></tr>';

			/** Income from house property + Income from other sources */
			$annexureTotal8 = $empHousePropertyIncome + $empOtherIncome;

			/** If house propery amount is negative we need to display the amount inside the parenthesis */
			if($empHousePropertyIncome < 0){
				$displayEmpHPIncome = (number_format(abs($empHousePropertyIncome),2,'.','') > 0) ? ("(".number_format(abs($empHousePropertyIncome),2,'.','').")") : number_format(0,2,'.','');
			}else{
				$displayEmpHPIncome = number_format($empHousePropertyIncome,2,'.','');
			}

			/** If total income under section24 amount is negative we need to display the amount inside the parenthesis */
			if($annexureTotal8 < 0){
				$displayAnnexureTotal8 = (number_format(abs($annexureTotal8),2,'.','') > 0) ? ("(".number_format(abs($annexureTotal8),2,'.','').")") : number_format(0,2,'.','');
			}else{
				$displayAnnexureTotal8 = number_format($annexureTotal8,2,'.','');
			}

			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'">7. Add: Any other income reported by the employee as per section 192(2B)</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td></tr>';
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'padding-left:10px;">(a) Income (or admissible loss) from house property reported by employee offered for TDS</td><td style ="'.$borderValbt.'">'.$currencySymbol.$displayEmpHPIncome.'</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td></tr>';
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'padding-left:10px;">(b) Income under the head Other Sources offered for TDS</td><td style ="'.$borderValbt.'">'.$currencySymbol.$empOtherIncome.'</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td></tr>';
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'">8. Total amount of other income reported by the employee[7(a)+7(b)]</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td><td style ="'.$borderValbt.'">'.$currencySymbol.$displayAnnexureTotal8.'</td></tr>';

			/** (Gross salary - sec 10 Exemptions) - (Professional Tax + Entertainment Allowance) + Income from house property + Income from other sources */
			$annexureTotal9 = number_format(($annexureTotal6 + $annexureTotal8),2,'.','');
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'">9. Gross total income (6+8)</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td><td style ="'.$borderValbt.'">'.$currencySymbol.$annexureTotal9.'</td></tr>';
			
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'padding-top:2px;">10. Deductions under Chapter VI-A</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td></tr>';

			$form16TableStyle = array('borderbt' => $borderbt,
			'borderValbt' => $borderValbt,
			'currencySymbol' => $currencySymbol);

			/** Prefill the declaration amount section 80c,80ccd,80ccd */
			$aggOfChapterVITrResult = $this->prefillAggOfChapterVISections($aggDeductionDetails,$form16TableStyle,'a','aggFirstSection');

			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'padding-left:10px;"></td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'">Gross amount</td><td style ="'.$borderbt.'">Deductible amount</td></tr>';

			if(!empty($aggOfChapterVITrResult['aggOfChapterVITr'])){
				$aggOfChapterVISecIAmount = $aggOfChapterVITrResult['aggTotalAmount'];
				$exportStr .= $aggOfChapterVITrResult['aggOfChapterVITr'];
			}else{
				$aggOfChapterVISecIAmount = 0;
			}

			$aggOfChapterVISecIAmount = number_format($aggOfChapterVISecIAmount,2,'.','');
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'padding-left:10px;">('.$aggOfChapterVITrResult['lastUsedAlphabet'].') Total deduction under Section 80C, 80CCC, 80CCD</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td><td style ="'.$borderValbt.'">'.$currencySymbol.$aggOfChapterVISecIAmount.'</td></tr>';

			/** Prefill the other chapter VIA - declaration amount */
			$aggOfChapterVISecIIAlphabet = ++$aggOfChapterVITrResult['lastUsedAlphabet'];
			$aggOfChapterVITrSecResult = $this->prefillAggOfChapterVISections($aggDeductionDetails,$form16TableStyle,$aggOfChapterVISecIIAlphabet,'aggSecondSection');

			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'padding-left:10px;"> </td><td style ="'.$borderbt.'padding-left:10px;"> </td><td style ="'.$borderbt.'padding-left:10px;"> </td><td style ="'.$borderbt.'padding-left:10px;"> </td></tr>';
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'padding-left:10px;"></td><td style ="'.$borderbt.'">Gross amount</td><td style ="'.$borderbt.'">Qualifying amount</td><td style ="'.$borderbt.'">Deductible amount</td></tr>';

			if(!empty($aggOfChapterVITrSecResult['aggOfChapterVITr'])){
				$aggOfChapterVISecIIAmount = $aggOfChapterVITrSecResult['aggTotalAmount'];
				$exportStr .= $aggOfChapterVITrSecResult['aggOfChapterVITr'];
			}else{
				$aggOfChapterVISecIIAmount = 0;
			}

			$aggOfChapterVISecIIAmount = number_format($aggOfChapterVISecIIAmount,2,'.','');
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'padding-left:10px;">('.$aggOfChapterVITrSecResult['lastUsedAlphabet'].') Amount deductible under any other provisions of chapter VI-A</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td><td style ="'.$borderValbt.'">'.$currencySymbol.'0.00</td></tr>';

			$aggTotalAmount = $aggOfChapterVISecIAmount + $aggOfChapterVISecIIAmount;
			$aggTotalAmount = number_format($aggTotalAmount,2,'.','');
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'">11. Aggregate of deductible amount under Chapter VI-A</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td><td style ="'.$borderValbt.'">'.$currencySymbol.$aggTotalAmount.'</td></tr>';

			/** Get Taxable income from total income and agg deductions amount **/
			$annexureTotal11 = $annexureTotal9 - $aggTotalAmount;
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'">12. Total taxable income (9-11)</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td><td style ="'.$borderValbt.'">'.$currencySymbol.number_format($annexureTotal11,2,'.','').'</td></tr>';
			
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'">13. Tax on total income</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td><td style ="'.$borderValbt.'">'.$currencySymbol.$taxOnTotalIncome.'</td></tr>';
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'">14. Health and education cess </td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td><td style ="'.$borderValbt.'">'.$currencySymbol.$edForTax.'</td></tr>';
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'">15. Tax Payable (12+13)</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td><td style ="'.$borderValbt.'">'.$currencySymbol.number_format(($taxOnTotalIncome + $edForTax),2,'.','').'</td></tr>';
			$exportStr .= '<tr><td colspan="2" style ="'.$borderbt.'">16. Less: Relief under section 89 (attach details)</td><td style ="'.$borderbt.'"></td><td style ="'.$borderbt.'"></td><td style ="'.$borderValbt.'">'.$currencySymbol.$sec89ReliefAmt.'</td></tr>';
			$exportStr .= '<tr><td colspan="2" style ="border-top:none;border-bottom: 1px solid black;padding-top:2px;border-right: 1px solid black;">17. Net tax payable (15-16)</td>
							<td style ="border-bottom: 1px solid black;border-right: 1px solid black;border-top:none;"></td>
							<td style ="border-right: 1px solid black;border-top:none;padding-top:2px;border-bottom: 1px solid black;"></td>
							<td style ="border-right: 1px solid black;border-bottom: 1px solid black;border-top:none;padding-top:2px;'.$form16BoldStyle.'">'.$currencySymbol.number_format(($taxOnTotalIncome + $edForTax),2,'.','').'</td></tr>';
			$exportStr .= '<tr><th colspan="5" style ="text-align:center;border-bottom: 1px solid black;">Verification</th></tr>';
			$exportStr .= '<tr><td colspan="5" style ="text-align:left;border-bottom: 1px solid black;">I, '.$signatoryEmployeeName.' son/daughter of '. $signatoryDependentName .' working in
							the capacity of '.$signatoryDesignationName .' (designation) do hereby certify that the information
							given above is true, complete and correct and is based on the books of account, documents, TDS
							statements, and other available records.</td></tr>';
			$exportStr .= '<tr><td colspan="2" style="'.$form16BFooterCss.'">Place: '.$city.'</td><td colspan="3" style ="border-right: 1px solid black;'.$borderb.'text-align:center;">'.$signatoryEmployeeName.'</td></tr>';
			$exportStr .= '<tr><td colspan="2" style="'.$form16BFooterCss.'">Date: '.$date.'</td><td colspan="3" style ="border-right: 1px solid black;border-bottom: 1px solid black;border-top: none;padding-top: 2px;text-align:center;">(Signature of person responsible for deduction of tax)</td></tr>';
			$exportStr .= '<tr><td colspan="2" style="'.$form16BFooterCss.'">Designation: '.$signatoryDesignationName.'</td><td colspan="3" style="'.$form16BFooterValcss.'">Full Name : '.$signatoryEmployeeName.'</td></tr>';		
			$exportStr .= '</table>';
	
			$exportStr .= '</div>';
			$exportStr .= '<div style=\'page-break-after:always;\'>&nbsp;</div>';
			$exportStr .='<br><br><br>';
			return $exportStr;
		}
		else{
			return array('success' => false, 'msg' => "Please update FORM16 Signatory,CIT in tax configuration details", 'type' => 'info');
		}
	}
	
	/** delete form 16 **/
	public function deleteForm16($formIds, $logEmpId, $formName)
	{
		/** multiple form ids**/
		if(!empty($formIds) && count($formIds) > 0){
			foreach ($formIds as $delFormId){
				$deleted = $this->_db->delete($this->_ehrTables->form16, 'Form_Id='.$delFormId);
				$formId = $delFormId;
			}
		}
		else{
			$deleted = $formId = 0;
		}
		/**
		 *	delete activity for common function
		 *		1)check lock is exist or not.
		 *			If lock is exist then show error message like employee is open record for update.
		 *		2)If No lockflag then process delete activity
		 *		3)Update delete activity in system log
		 *		4)return success/failure message
		*/
		return $this->_commonFunction->deleteRecord (array('deleted'        => $deleted,
														'tableName'      => $this->_ehrTables->form16,
														'lockFlag'       => 0,
														'formName'       => $formName,
														'trackingColumn' => $formId,
														'sessionId'      => $logEmpId));
		
	}	
		
	/** get TDS history details for the new registration **/
	public function getTdsHistoryDetails($employeeId,$assessmentYear)
	{
		/** Get the TDS history for the employees with history type 'New Registration' **/
		$fiscalStartEndDate = $this->_dbFinancialYr->fiscalStartEndDate('PHP');
		$fiscalStartDate = $fiscalStartEndDate['finstart'];
		$fiscalEndDate   = $fiscalStartEndDate['finend'];
		
		/** get the history Id for the employee from financial start date to end of the previous payslip date **/
		$qryTdsHistoryExists = $this->_db->select()->from($this->_ehrTables->empTdsHistory,array('History_Id'))
											 ->where('Employee_Id = ?', $employeeId)
											 ->where('From_Date >= ?', $fiscalStartDate)
											 ->where('To_Date <= ?', $fiscalEndDate)
											 ->where('History_Type = ?', 'New Registration');
		
		$tdsHistoryIds = $this->_db->fetchCol($qryTdsHistoryExists);
		
		/** If the TDS history exists for the 'New registration' **/
		if(!empty($tdsHistoryIds))
		{
			/** get Gross salary, Perquisites and Income tax **/
			$taxableSalHistory = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->empTdsHistory,
										array('Taxable_Salary'=>new Zend_Db_Expr('SUM(Gross_Salary)'),
											  'Perquisites'=>new Zend_Db_Expr('SUM(Perquisites)'),
											  'Income_Tax'=>new Zend_Db_Expr('SUM(Income_Tax)')))
									->where('History_Id IN (?)', $tdsHistoryIds));
			
			return $taxableSalHistory;			
		}
		else
		{
			return '';
		}
	}

	/** Prefill the agg of chapterVI deductions by the grouping type */
	public function prefillAggOfChapterVISections($aggDeductionDetails,$form16TableStyle,$aggAlphabetStr,$aggViewType){
		$borderbt = $form16TableStyle['borderbt'];
		$borderValbt = $form16TableStyle['borderValbt'];
		$currencySymbol = $form16TableStyle['currencySymbol'];	

		$aggOfChapterVITr = '';
		$invEmpDecExemptAmount = $aggTotalAmount = 0;

		$dbTaxSection = new Payroll_Model_DbTable_TaxSection();
		if($aggViewType === 'aggFirstSection'){
			$parentTaxSectionIdArray = $dbTaxSection->getParentTaxSectionIds('Section 80C','fetchPairs'); // get the section 80C,80CCC,80CCD(1) ids,Name
			$parentTaxSectionIdKeys = (!empty($parentTaxSectionIdArray)) ? array_keys($parentTaxSectionIdArray) : array();
		}else{
			$parentTaxSectionIdKeys = $parentTaxSectionIdArray = $dbTaxSection->getParentTaxSectionIds('Section 80C','fetchCol'); // get the section 80C,80CCC,80CCD(1) ids
		}
		
		for($d=0;$d<count($aggDeductionDetails);$d++){
			$invEmpDecExemptAmount = number_format($aggDeductionDetails[$d]['ExemptAmt'],2,'.','');// Qualifying/Deductible amount
			$empInvestmentDetails = $aggDeductionDetails[$d]['Investment_Details'];
			$govtTaxSectionId = $aggDeductionDetails[$d]['Govt_Tax_Section_Id'];

			if(($aggViewType === 'aggFirstSection' && in_array($govtTaxSectionId,$parentTaxSectionIdKeys))){
				$ExistInvestmentsName = array();
				$aggOfChapterVITrInvDetails = '';

				/** Iteration the section 80C, 80CCC, 80CCD */
				foreach($parentTaxSectionIdArray as $parentSecId=>$parentSecName){
					$empInvIterateCount = 0;
					$aggOfChapterVITrInvDetails = '';

					//Present the investment details
					foreach($empInvestmentDetails as $empSectionInvestmentRec){
						
						$empInvIterateCount += 1;
						$empDecInvName = $empSectionInvestmentRec['Investment_Name'];
						$invEmpDecAmount = number_format($empSectionInvestmentRec['Emp_Declaration_Amount'],2,'.','');//Gross amount show approved amount
						/** If govt section id  and the investment govt section id matches */
						if($parentSecId == $empSectionInvestmentRec['Inv_Govt_Tax_Section_Id'] && !in_array($empDecInvName,$ExistInvestmentsName) ){
							array_push($ExistInvestmentsName,$empDecInvName);

							/** Present the gross amount and deductible amount if the section has a subsection */						
							$aggOfChapterVITrInvDetails .= '<tr><td colspan="2" style ="'.$borderbt.'padding-left:40px;">'.$empDecInvName.'</td><td style ="'.$borderValbt.'"></td><td style ="'.$borderValbt.'">'.$currencySymbol.$invEmpDecAmount.'</td><td style ="'.$borderValbt.'"></td></tr>';
						}

						if(count($empInvestmentDetails) == $empInvIterateCount){
							if(!empty($aggOfChapterVITrInvDetails)){
								/** Present parent section name if the investment exist */
								$aggOfChapterVITr .= '<tr><td colspan="2" style ="'.$borderbt.'padding-left:10px;">('.$aggAlphabetStr.') Deduction under '.$parentSecName.'</td><td style ="'.$borderValbt.'"></td><td style ="'.$borderValbt.'"></td><td style ="'.$borderValbt.'"></td></tr>';
								$aggOfChapterVITr .= $aggOfChapterVITrInvDetails;
								$aggAlphabetStr++;
							}else{
								/** Present parent section name and the zero amount if the investment does not exist for a section */
								$aggOfChapterVITr .= '<tr><td colspan="2" style ="'.$borderbt.'padding-left:10px;">('.$aggAlphabetStr.') Deduction under '.$parentSecName.'</td><td style ="'.$borderValbt.'"></td><td style ="'.$borderValbt.'">0.00</td><td style ="'.$borderValbt.'"></td></tr>';
								$aggAlphabetStr++;
							}
						}
					}
				}

				$aggTotalAmount += $invEmpDecExemptAmount;
			}else if(($aggViewType === 'aggSecondSection' && !in_array($govtTaxSectionId,$parentTaxSectionIdArray))){
				//Present the investment details
				foreach($empInvestmentDetails as $empSectionInvestmentRec){
					$empDecInvName = $empSectionInvestmentRec['Investment_Name'];
					$invEmpDecAmount = number_format($empSectionInvestmentRec['Emp_Declaration_Amount'],2,'.','');//Gross amount show approved amount
					
					/** Present the gross amount,qualifying and deductible amount if the section do not have any subsection */
					$aggOfChapterVITr .= '<tr><td colspan="2" style ="'.$borderbt.'padding-left:10px;">('.$aggAlphabetStr.') '.$empDecInvName.'</td><td style ="'.$borderValbt.'">'.$currencySymbol.$invEmpDecAmount.'</td><td style ="'.$borderValbt.'">'.$currencySymbol.$invEmpDecExemptAmount.'</td><td style ="'.$borderValbt.'">'.$currencySymbol.$invEmpDecExemptAmount.'</td></tr>';
					$aggAlphabetStr++;
				}
				$aggTotalAmount += $invEmpDecExemptAmount;
			}
		}
		
		$prefillAggResponse = array('aggTotalAmount' => $aggTotalAmount,
									'lastUsedAlphabet' => $aggAlphabetStr,
									'aggOfChapterVITr' => $aggOfChapterVITr);
		return $prefillAggResponse;
	}
}
