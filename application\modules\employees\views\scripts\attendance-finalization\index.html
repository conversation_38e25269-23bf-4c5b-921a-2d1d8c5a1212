<!DOCTYPE html>
<html>

<head>
	<!-- common scripts -->
	<script src="https://cdn.jsdelivr.net/npm/vue@2/dist/vue.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/vuetify@2.2.3/dist/vuetify.min.js"></script>
	<script src="https://cdn.jsdelivr.net/gh/f/graphql.js@master/graphql.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/vue-cookies@1.6.1/vue-cookies.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/v-tooltip@2.0.2"></script>
	<!-- specific scripts -->
	<script src="https://cdn.jsdelivr.net/npm/exceljs@4.3.0/dist/exceljs.min.js"></script>
	<!-- plugin styles -->
	<link href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900"
	rel="stylesheet" />
	<link href="https://cdn.jsdelivr.net/npm/@mdi/font@4.x/css/materialdesignicons.min.css"
		rel="stylesheet" />
	<link href="https://cdn.jsdelivr.net/npm/vuetify@2.2.11/dist/vuetify.min.css" rel="stylesheet" />
	<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.4.3/css/flag-icon.min.css">
	<!--  own styles -->
	<style scoped lang="css">

		.tab-card-cls{
			padding: 6px;
			margin: 10px;
			height: 5em;
			display: flex !important;
			align-items: center;
			background: #fbfbfb !important;
		}
		.tab_alignment_class{
			display: flex;
			justify-content: center;
			width: 100%;
		}
		.theme--light.v-tabs-items {
			background: none !important;
		}
		/* css overrides */
		.v-list-item .v-list-item__title {
			line-height: 1.7em;
		}
		.v-banner {
			position: fixed !important;
			bottom: 0px !important;
			top: unset !important;
		}

		.v-banner--single-line .v-banner__text {
			background-color: #ffbdd3;
		}

		/* end of css overrides */

		/* own css classes */
		.sort_btn_property{
			max-width: 8.5em;
		}
		.v-menu__content {
				position: fixed !important;
		}
		@media (max-width: 1264px) {
			.sort_btn_property{
				max-width: 5em;
			}
			.checkbox_property{
				margin-left: 20px;
			}
			.avatar_property{
				margin: 0px !important;
			}
			.customAlert {
				font-size: 0.8em !important
			}
			.v-application--is-ltr .v-alert__dismissible {
				bottom : 53px !important
			}
		}
		@media (max-width: 960px) {
			.v-menu__content {
				max-width: 100%;
			}

			.activeTab {
				height: 3.5em !important;
			}

			.notActiveTab {
				height: 3.5em !important;
			}
			.attendance_action_buttons{
				margin-top: 50px !important;
			}
			.pagination_items_class{
				justify-content: center;
			}
			.att_noAtt_btn_cls{
				justify-content: center !important;
			}
		}
		@media (max-width: 600px) {
			.attendance_action_buttons{
				flex-direction: column !important;
			}
			.pagination_items_class{
				flex-direction: column;
			}
			.att_noAtt_btn_cls{
				flex-direction: column;
			}
		}
		.v-pagination {
			width: auto !important;
		}

		.height_30em {
			height: 30em;
		}

		.v-input--selection-controls__ripple {
			opacity: 0;
		}

		.col_property{
			min-width: 94px;
		}

		.checkbox_property{
			margin-top: 1.5em;
		}

		.avatar_property{
			margin: 15px;
		}
		
		.v-application .accent--text {
			display: flex !important;
			justify-content: center !important;
		}

		.filter-menu-position {
			margin-top : 28px !important;
		}

		.pointer-cursor {
			cursor: pointer !important;
		}

		.attendance_action_buttons {
			display: flex;
			flex-direction: row;
			margin: 0px;
			align-items: center;
			justify-content: flex-end;
		}

		.attendance_action_buttons_icon {
			margin-left: 5px;
		}

		.attendance_action_buttons_padding {
			margin: 8px 16px;
		}

		.v-btn {
			text-transform: none !important;
		}

		.attendance-count {
			height: 30px;
			min-width: 30px;
			width: auto;
			border-radius: 25px;
			margin-right: 15px;
			padding: 7px;
			background-color: var(--primary-color);
			display: flex;
			justify-content: center;
			color: #fff;
			font-weight: bold;
			vertical-align: middle;
			margin-left: -10px;
		}


		.sticky-notes-img {
			height: 180px;
			width: 180px;
		}
		.speakerImage{
			position: absolute;
      bottom: 5px;
			height : 150px;
		}
		.customAlert {
			border-radius: 4px;
			width: 87%;
			bottom: 0;
			position: fixed;
			padding: 10px;
			z-index:100;
			margin-left: 44px;
			padding: 4px;
			font-size: 1em
		}

		.v-alert__wrapper { 
			background: #f1c4c4
		}

		.v-application--is-ltr .v-alert__dismissible {
			margin: 0px;
			bottom: 58px;
			right : -18px;
		}
		.v-btn--fab.v-size--default .v-icon, .v-btn--fab.v-size--small .v-icon, .v-btn--icon.v-size--default .v-icon, .v-btn--icon.v-size--small .v-icon {
			font-size: 28px !important;
			color: var(--primary-color) !important;
		}
		.align-alert-box-content {
			margin-top: -16px;
    		margin-bottom: -10px;
		}
		.pagination_items_class{
			display: flex;
		}
		.att_noAtt_btn_cls{
			display: flex;
			justify-content: flex-end;
		}
		.v-data-table tbody .v-data-table__mobile-table-row td:nth-child(2) {
            justify-content: center;
		}
		.v-data-table tbody .v-data-table__mobile-table-row td:nth-child(3) {
            justify-content: center;
		}
		.v-data-table tbody .v-data-table__mobile-table-row td:nth-child(4) {
            justify-content: center;
		}
		.v-data-table tbody td:nth-child(2) .v-data-table__mobile-row__header {
		    	display: none
		}
		.v-data-table tbody td:nth-child(3) .v-data-table__mobile-row__header {
		    	display: none
		}
		.v-data-table tbody td:nth-child(4) .v-data-table__mobile-row__header {
		    	display: none
		}
		.v-data-table {
			background: #f1f1f1 !important;
		}
		.mobile_view_filter_icon{
			border-radius: 50% !important;
			min-width: 40px !important;
			min-height: 40px;
		}
		.v-avatar{
			overflow: visible !important;
		}
		.v-data-table table {
			padding: 0 20px !important;
		}
		.v-data-table thead tr:last-child th{
			font-size: 12px;
		}
		@media screen and (min-width:1500px) and (max-width:1900px) {
			.customAlert {
				margin-right: 2%
			}
		}
		@media screen and (max-width:1499px) {
			.customAlert {
				margin-right: 3%
			}
		}
		@media screen and (min-width:1900px) {
			.customAlert {
				margin-right: 3%;
				width: 91% !important;
			}
		}
	</style>

</head>

<body>
	<script>	
		// base url
		function base_url() {
			var pathParts = location.pathname.split('/');
			if (localStorage.getItem('production') == 0) {
				var url = location.origin + '/' + pathParts[1].trim('/') + '/'; // http://localhost/hrapponline/
			} else {
				var url = location.origin + '/'; // http://subdomain.hrapp.co
			}
			return url;
		}
		var base_url_final = base_url();
		addScript(base_url_final);

		function addScript(src, callback) {
			var s = document.createElement('base');
			s.setAttribute('src', src);
			s.onload = callback;
			document.head.appendChild(s);
		}
	</script>

	<!--  html content -->
	<div id="attendanceFinalization" class="col-md-12 portlets p-10">
		<template>
			<v-app class="bg_grey_lighten1">
				<v-bottom-navigation v-if="!showLoadingCard" fixed class="hidden-md-and-up" color="teal" elevation="15">
					<v-col cols="8">
						<v-text-field dense v-model="searchInput" placeholder="Search" prepend-icon="search"></v-text-field>
					</v-col>
					<v-col cols="4" class="text-xs-center justify-center d-flex">
							<v-menu top offset-y offset-x v-model="filterOpenInMobile" :close-on-content-click="false" 
								class="filter-menu-position">
								<template v-slot:activator="{ on }">
									<v-btn fab small v-on="on" color="primary" class="mobile_view_filter_icon">
										<i class="fa fa-filter filter-icon-property"></i>
									</v-btn>
								</template>
								<v-card class="p-10">
									<div class="d-flex justify-end">
										<v-icon color="primary" @click="filterOpenInMobile=!filterOpenInMobile" class="p-r-10 p-t-10">
											close</v-icon>
									</div>
									<v-row>
										<v-col cols="6">
											<v-autocomplete background-color="white" multiple @change="empTypeOnChange()"
												v-model="empTypeSelectedInFilter" :items="empTypeList" dense chips filled
												label="Employee Type">
											</v-autocomplete>
										</v-col>
										<v-col cols="6">
											<v-autocomplete background-color="white" multiple @change="deptOnChange()"
												v-model="deptSelectedInFilter" :items="deptList" dense small-chips filled
												label="Department">
											</v-autocomplete>
										</v-col>
									</v-row>
									<v-row class="m-t-n4">
										<v-col cols="6">
											<v-autocomplete background-color="white" multiple @change="locationOnChange()"
												v-model="locationSelectedInFilter" :items="locationList" dense small-chips
												filled label="Location">
											</v-autocomplete>
										</v-col>
										<v-col cols="6">
											<v-dialog ref="startDateValue" v-model="startDateModal"
												:return-value.sync="startDate" persistent width="290px">
												<template v-slot:activator="{ on }">
													<v-text-field v-model="formatedStartDate" label="Start Date"
														prepend-icon="event" readonly v-on="on"></v-text-field>
												</template>
												<v-date-picker :max="maxStartDate" v-model="startDate" scrollable>
													<v-spacer></v-spacer>
													<v-btn text color="primary" @click="startDateModal = false">Cancel
													</v-btn>
													<v-btn text color="primary" @click="$refs.startDateValue.save(startDate)">OK
													</v-btn>
												</v-date-picker>
											</v-dialog>
										</v-col>
							
							
									</v-row>
									<v-row class="m-t-n4">
							
										<v-col cols="6">
											<v-dialog ref="endDateValue" v-model="endDateModal" :return-value.sync="endDate"
												persistent width="290px">
												<template v-slot:activator="{ on }">
													<v-text-field v-model="formatedEndDate" label="End Date"
														prepend-icon="event" readonly v-on="on"></v-text-field>
												</template>
												<v-date-picker :min="minEndDate" :max="maxEndDate" v-model="endDate" scrollable>
													<v-spacer></v-spacer>
													<v-btn text color="primary" @click="endDateModal = false">Cancel</v-btn>
													<v-btn text color="primary" @click="$refs.endDateValue.save(endDate)">OK
													</v-btn>
												</v-date-picker>
											</v-dialog>
										</v-col>
										<v-col v-if="finalizationSubTab === 'attTab'" cols="6">
											<v-autocomplete background-color="white" multiple @change="statusOnChange()"
												v-model="statusSelectedInFilter" :items="statusList" dense small-chips filled
												label="Status">
											</v-autocomplete>
										</v-col>
									</v-row>
									<v-btn color="primary white--text" rounded @click="fnApplyFilter()"> Apply
									</v-btn>
									<v-btn color="primary" rounded outlined @click="resetFilterValues(1)"> Reset
									</v-btn>
								</v-card>
							</v-menu>
					</v-col>
				</v-bottom-navigation>

					<!-- if we have access we can show lists of attendance/noAttendance -->
					<div v-if="!accessDenied">
						<v-card class="bg_grey_lighten1 hidden-sm-and-down" flat
							height="5em">
							<!-- top bar design -->
							<v-toolbar color="grey lighten-5">
								<v-row class="d-flex p-l-10">
									<v-col xlg="4" lg="5" md="6" class="notActiveTab d-flex align-center justify-center"
										@click="redirectToEmpModule()">
										Attendance
									</v-col>
									<v-col xlg="4" lg="5" md="6" class="activeTab d-flex align-center justify-center">
										Attendance Finalization
									</v-col>
								</v-row>
								<v-spacer></v-spacer>
								<!-- search bar -->
								<v-row class="d-flex align-center mt-4">
									<v-col :lg="showSearch ? 4 : 9" :md="showSearch ? 2 : 8">
									</v-col>
									<v-col :lg="showSearch ? 8 : 3" :md="showSearch ? 10 : 4"
										class="d-flex justify-end m-t-10">
										<v-text-field
											v-if="!showLoadingCard"
											placeholder="Search"
											prepend-inner-icon="search" single-line
											@focus="showSearch = true" @blur="showSearch = false"
											v-model="searchInput" aria-label="search">
										</v-text-field>
									</v-col>
								</v-row>
								<span class="padding-2em"></span>
								<!-- filter button -->
								<v-menu v-if="!showLoadingCard" left offset-y v-model="filterOpen" class="filter-menu-position" :close-on-content-click="false">
									<template v-slot:activator="{ on }">
										<v-btn fab small v-on="on" color="primary" aria-label="filter" class="mt-4">
											<i class="fa fa-filter filter-icon-property"></i>
										</v-btn>
									</template>
									<v-card class="p-15">
										<div class="d-flex justify-end">
											<v-icon color="primary" @click="filterOpen=!filterOpen"
												class="p-r-10 p-t-10">close</v-icon>
										</div>
										<v-row>
											<v-col cols="6">
												<v-autocomplete background-color="white" multiple @change="empTypeOnChange()"
													v-model="empTypeSelectedInFilter" :items="empTypeList" dense chips
													filled label="Employee Type">
												</v-autocomplete>
											</v-col>
											<v-col cols="6">
												<v-autocomplete background-color="white" multiple @change="deptOnChange()"
													v-model="deptSelectedInFilter" :items="deptList" dense small-chips
													filled label="Department">
												</v-autocomplete>
											</v-col>
										</v-row>
										<v-row class="m-t-n4">
											<v-col cols="6">
													<v-autocomplete background-color="white" multiple @change="locationOnChange()"
														v-model="locationSelectedInFilter" :items="locationList" dense
														small-chips filled label="Location">
													</v-autocomplete>
											</v-col>
											<v-col cols="6">
												<v-dialog ref="startDateValue" v-model="startDateModal" :return-value.sync="startDate" persistent width="290px">
													<template v-slot:activator="{ on }">
														<v-text-field
														v-model="formatedStartDate"
														label="Start Date"
														prepend-icon="event"
														readonly
														v-on="on"
														></v-text-field>
													</template>
													<v-date-picker :max= "maxStartDate" v-model="startDate" scrollable>
														<v-spacer></v-spacer>
														<v-btn text color="primary" @click="startDateModal = false">Cancel</v-btn>
														<v-btn text color="primary" @click="$refs.startDateValue.save(startDate)">OK</v-btn>
													</v-date-picker>
													</v-dialog>
											</v-col>
											
											
										</v-row>
										<v-row class="m-t-n4">
											
											<v-col cols="6">
												<v-dialog ref="endDateValue" v-model="endDateModal" :return-value.sync="endDate" persistent width="290px">
													<template v-slot:activator="{ on }">
														<v-text-field
														v-model="formatedEndDate"
														label="End Date"
														prepend-icon="event"
														readonly
														v-on="on"
														></v-text-field>
													</template>
													<v-date-picker :min= "minEndDate" :max="maxEndDate" v-model="endDate" scrollable>
														<v-spacer></v-spacer>
														<v-btn text color="primary" @click="endDateModal = false">Cancel</v-btn>
														<v-btn text color="primary" @click="$refs.endDateValue.save(endDate)">OK</v-btn>
													</v-date-picker>
												</v-dialog>
											</v-col>
											<v-col v-if="finalizationSubTab === 'attTab'" cols="6">
												<v-autocomplete background-color="white" multiple @change="statusOnChange()"
													v-model="statusSelectedInFilter" :items="statusList" dense
													small-chips filled label="Status">
												</v-autocomplete>
											</v-col>
										</v-row>
										<v-btn color="primary white--text" rounded  @click="fnApplyFilter()"> Apply
										</v-btn>
										<v-btn color="primary" rounded outlined @click="resetFilterValues(1)"> Reset
										</v-btn>
									</v-card>
								</v-menu>
							</v-toolbar>
						</v-card>
						<v-card elevation="15" color="white hidden-md-and-up mt-4" flat height="4em" class="mobile-top-bar-card">
							<v-row>
								<v-col cols="6" class="justify-center d-flex">
									<div class="notActiveTab" @click="redirectToEmpModule()">Attendance</div>
								</v-col>
								<v-col cols="6" class="justify-center d-flex">
									<div class="activeTab">Attendance Finalization</div>
								</v-col>
							</v-row>
						</v-card>
						<v-container fluid class="pl-20 pr-20 mt-4">
							<v-row v-if="showLoadingCard" class="m-10">
								<v-col v-for="i in 3" :key="i" cols="12">
									<v-skeleton-loader :loading="true" transition="scale-transition" height="94"
										type="table-thead" v-if=" i == 1">
									</v-skeleton-loader>
									<v-skeleton-loader :loading="true" transition="scale-transition" height="94"
										type="list-item-avatar-two-line" v-else>
									</v-skeleton-loader>
								</v-col>
							</v-row>
							<v-row v-else class="mx-md-5 mt-n8">
								<v-col cols="12">
									<v-card
										v-if="notesAlert"
										class="d-flex align-center pa-2 common-box-shadow"
										:class="{ 'flex-column': windowWidth <= 600 }"
										style="background: linear-gradient(to left, #f7f793 0%, #fff9d1 100%)"
										width="100%"
									>
										<img
											v-if="windowWidth > 600"
											width="50"
											height="auto"
											class="ml-n3 mr-2"
											alt="idea-bulb"
											:src="basePath+'images/idea-bulb.webp'"
											@error="notesImageFormatSwap"
										>
										<span class="font-weight-medium caption mr-1 text-center">
											<span v-if="finalizationSubTab === 'attTab'">
												The attendance auto closure process helps HRs to close all outstanding draft attendance records that are within the work schedule configuration and approve all outstanding attendance records in bulk without any user intervention.
											</span>
											<span v-else>
												<div>
													The "LOP Initiation" process assists HRs in streamlining the employee's attendance data by 
													allowing them to initiate the automatic creation of Loss of Pay(Unpaid leave) records. 
													This process applies to employees, who do not have attendance or full leave record. 
													It is a mandatory process for employees with attendance enforcement configuration, otherwise, it's an optional process.
													<span>This may be your last opportunity to add <span><a @click="redirectToLeaveAttendance('employees/leaves')">leaves</a></span> or </span> <span><a @click="redirectToLeaveAttendance('employees/attendance')">attendance</a></span> <span>before initiating the LOP creation process. Give it a go!</span>
												</div>
											</span>
										</span>
										<v-spacer></v-spacer>
										<v-icon @click="notesAlert = false">close</v-icon>
									</v-card>
									<v-card class="card-radius tab-card-cls white" :style="windowWidth <= 600 ? 'margin-top: 7em' : ''"
										:height="windowWidth <= 600 && displayedListCount > 0 ? 180 : windowWidth <= 900 && displayedListCount > 0 ? 150 : 60">
										<v-row style="overflow: hidden;">
											<v-col cols="12" md="6" class="pb-0 mt-3">
												<v-tabs v-model="finalizationSubTab" :centered ="windowWidth <= 700" show-arrows style="margin: -5px 0px 7px 0px;">
													<v-tabs-slider></v-tabs-slider>
													<v-tab href="#attTab" style="padding-bottom:20px">
														<div style="text-transform: capitalize !important;" class="font-weight-bold">Attendance (Incomplete)
															<v-chip class="mt-1" v-if="attendanceCount" color="primary">{{attendanceCount}}</v-chip>
														</div>
													</v-tab>
													<v-tab href="#noAttTab" style="padding-bottom:20px">
														<div style="text-transform: capitalize !important;" class="font-weight-bold">No Attendance 
															<v-chip class="mt-1" v-if="noAttendanceCount" color="primary">{{noAttendanceCount}}</v-chip>
														</div>
													</v-tab>
													<v-tab v-if="showEarlyCheckout" href="#earlyCheckoutTab" style="padding-bottom:20px">
														<div style="text-transform: capitalize !important;" class="font-weight-bold">Early Checkout</div>
													</v-tab>
													<v-tab href="#shortageTab" style="padding-bottom:20px" v-if="showAttendanceShortageTab">
														<div style="text-transform: capitalize !important;" class="font-weight-bold">Shortage
															<v-chip class="mt-1" v-if="shortageCount" color="primary">{{shortageCount}}</v-chip>
														</div>
													</v-tab>
												</v-tabs>
											</v-col>
											<v-col class="att_noAtt_btn_cls">
												<v-menu offset-y v-if="finalizationSubTab === 'noAttTab' && noAttendanceCount > 0"
													:disabled="LOPTypes.length == 0" v-model="selectLOPType">
													<template v-slot:activator="{ on }">
														<v-btn rounded color="primary" dark
															v-on="on" class="my-2 mx-4" @click="fnSelectLOPList()">
															{{ AutoLOPOptionsText }}
															<i class="fa fa-caret-down attendance_action_buttons_icon"></i>
														</v-btn>
													</template>
													<v-list>
														<v-list-item v-for="(LOPType, index) in LOPTypes" :key="index">
															<v-list-item-title class="pointer-cursor"
																@click="selectAutoLOPType(LOPType.LeaveType_Id)">{{ LOPType.Leave_Name }}
															</v-list-item-title>
														</v-list-item>
													</v-list>
												</v-menu>
												<v-btn rounded v-if="finalizationSubTab === 'shortageTab' && shortageCount > 0" class="m-t-10" color="primary"
													@click="initiateAttendanceShortageLeave()">
													Initiate As per Attendance Shortage Configuration 
												</v-btn>
												<v-btn rounded v-if="finalizationSubTab === 'shortageTab' && shortageCount > 0" class="m-t-10" color="primary" style="margin-left: 1em;"
													@click="ignoreAttendanceShortage()">
													Ignore 
												</v-btn>
												<v-btn v-if="finalizationSubTab === 'attTab' && attendanceCount>0" rounded color="primary" class="ma-2 white--text" @click="fnValidateAutoClosure()">
													{{ approveAutoClosureBtnText }}
												</v-btn>
											</v-col>
										</v-row>
									</v-card>
								</v-col>
								<v-col v-if="displayedListCount>0 && pageCount > 0"  cols="12">
									<v-card
										v-if="finalizationSubTab === 'noAttTab' && listOfNoAttendanceEmployees.length > 0"
										class="mt-n5 ml-3 d-flex align-center pa-2 common-box-shadow"
										:class="{ 'flex-column': windowWidth <= 600 }"
										style="background: linear-gradient(to left, #f7f793 0%, #fff9d1 100%)"
										width="97%"
									>
										<img
											v-if="windowWidth > 600"
											width="50"
											height="auto"
											class="ml-n3 mr-2"
											alt="idea-bulb"
											:src="basePath+'images/idea-bulb.webp'"
											@error="notesImageFormatSwap"
										>
										<span class="font-weight-medium caption mr-1 text-center">
											Please note that it is only applicable to standard business days and excludes weekends and holidays. 
											If you wish to request LOP for a weekend or holiday, you must utilize the <span><a @click="redirectToLeaveAttendance('employees/leaves')">leave</a></span> module.
										</span>
									</v-card>
									<div v-else class="mt-n5"></div>
									<div
										class="mt-n1 d-flex flex-wrap align-center mt-7"
										:class="windowWidth <= 600 ? 'justify-center' : 'justify-end'"
									>
										<v-pagination
											v-model="page" 
											:length="pageCount" 
											:total-visible="5" 
											circle
											class="pl-6"
											style="box-shadow: none !important;">
										</v-pagination>
										<v-menu offset-y v-model="openPagesMenu">
											<template v-slot:activator="{ on }">
												<v-btn rounded color="white" dark v-on="on" class="primary--text my-2">
													{{ pageNumber }}
													<i class="fa fa-caret-down attendance_action_buttons_icon"></i>
												</v-btn>
											</template>
											<v-list>
												<v-list-item v-for="item in items" :key="item" @click="selectPageNumber(item)">
													<v-list-item-title>{{item}}</v-list-item-title>
												</v-list-item>
											</v-list>
										</v-menu>
										<v-menu v-model="openMoreMenu" offset-y>
											<template #activator="{ on }">
											  <v-btn color="primary" text v-on="on" small class="mr-n3">
												<v-icon v-if="!openMoreMenu" class="pl-1">more_vert</v-icon>
												<v-icon v-else class="pl-1">arrow_drop_up</v-icon>
											  </v-btn>
											</template>
											<v-list>
											  <v-list-item
												@click="exportExcelFile()"
											  >
												<v-hover>
												  <template #default="{ hover }">
													<v-list-item-title
													  class="pa-3"
													  :class="{
														'primary lighten-5': hover,
													  }"
													  >Export {{finalizationSubTab === "attTab" ? 'Incomplete Attendance' : finalizationSubTab === 'shortageTab' ? 'Attendance Shortage' : 'Missed Attendance and Leave' }}
													  </v-list-item-title>
												  </template>
												</v-hover>
											  </v-list-item>
											</v-list>
										</v-menu>
										<v-avatar color="grey lighten-5" :size="40" class="avatar_property">
											<v-icon color="grey" @click="fnRefreshAttendanceEmployees()" aria-label="refresh">
												mdi-replay</v-icon>
										</v-avatar>
									</div>
								</v-col>
								<v-col cols="12">
									<v-tabs-items v-model="finalizationSubTab">
										<v-tab-item value="attTab" class="tab_alignment_class">
											<v-col style="padding: 0px" transition="scale-transition">
												<div v-if="listOfAttendEmployees.length > 0">
													<v-data-table id="gridView" v-model="selectedInAttendance" :single-select=false :page.sync="page"
														:items-per-page="parseInt(itemsPerPage, 10)" show-select :headers="headersAttendance"
														:items="listOfAttendEmployees" item-key="attendance_id" hide-default-footer fixed-header height="600px"
														@page-count="pageCount = $event" :search="searchInput" fix-header
													>
														<template v-slot:item.employee_name="{ item }">
															<span
																class="caption primary--text font-weight-bold">{{item.employee_name}}</span>

														</template>
														<template v-slot:header.data-table-select="{ on, props }">
															<div class="d-flex">
																<v-simple-checkbox color="primary" v-bind="props" v-on="on" on-icon="check_circle"
																	indeterminate-icon="remove_circle" off-icon="radio_button_unchecked">
																</v-simple-checkbox>
																<v-icon color="grey" v-if="selectedInAttendance.length > 0" @click="openDeleteConfirmation = true" class="pl-2">
																	delete
																</v-icon>
															</div>
														</template>
														<template v-slot:item.data-table-select="{ isSelected, select }">

															<v-simple-checkbox color="primary" :value="isSelected" @input="select($event)"
																on-icon="check_circle" off-icon="radio_button_unchecked"></v-simple-checkbox>
														</template>
														<div slot="no-data" class="d-flex justify-center primary--text">
															<div class="hidden-md-and-up margin-top-4em"></div>
															<div class="d-flex justify-center align-center flex-column height_30em">
																<img alt="noRecordFound" class="candidatesImage" :src="basePath+'vue/assets/images/emptyImgInAttendanceFinal.webp'" @error="imageFormatSwap">
																<div class="text_align_center grey--text title p-t-20">
																	No records found
																</div>
															</div>
														</div>
														<div slot="no-results" class="d-flex justify-center primary--text">
															<div class="hidden-md-and-up margin-top-4em"></div>
															<div class="d-flex justify-center align-center flex-column height_30em">
																<img alt="emptySearchImage" class="candidatesImage" :src="basePath+'vue/assets/images/emptyImgInAttendanceFinal.webp'" @error="imageFormatSwap">
																<div class="text_align_center grey--text title p-t-20">
																	No records found
																</div>
															</div>
														</div>
													</v-data-table>
												</div>
												<div v-else class="mx-12">
													<no-record-initial-screen button-text="" 
														main-title="No Records Found" image-name="emptyImgInAttendanceFinal"
													></no-record-initial-screen>
												</div>
											</v-col>
										</v-tab-item>
										<v-tab-item value="noAttTab">
											<v-col style="padding: 0px" transition="scale-transition">
												<div v-if="listOfNoAttendanceEmployees.length > 0">
													<v-data-table id="gridView" v-model="selectedInNoAttendance" :single-select=false :page.sync="page"
														:items-per-page="parseInt(itemsPerPage, 10)" show-select :headers="headersNoAttendance"
														:items="listOfNoAttendanceEmployees" item-key="absent_rec_index" hide-default-footer fixed-header height="600px"
														@page-count="pageCount = $event" :search="searchInput" fix-header
													>
														<template v-slot:item.leave_period="{ item }">
															<span>{{item.leave_period ? item.leave_period : "-"}}</span>
														</template>
														<template v-slot:header.data-table-select="{ on, props }">
															<v-simple-checkbox color="primary" v-bind="props" v-on="on" on-icon="check_circle"
																indeterminate-icon="remove_circle" off-icon="radio_button_unchecked">
															</v-simple-checkbox>
														</template>
														<template v-slot:item.data-table-select="{ isSelected, select }">

															<v-simple-checkbox color="primary" :value="isSelected" @input="select($event)"
																on-icon="check_circle" off-icon="radio_button_unchecked"></v-simple-checkbox>
														</template>

														<div slot="no-results" class="d-flex justify-center primary--text">
															<div class="hidden-md-and-up margin-top-4em"></div>
															<div class="d-flex justify-center align-center flex-column height_30em">
																<img alt="noRecordFound" class="candidatesImage" :src="basePath+'vue/assets/images/emptyImgInAttendanceFinal.webp'" @error="imageFormatSwap">
																<div class="text_align_center grey--text title p-t-20">
																	No records found
																</div>
															</div>
														</div>

														<div slot="no-data" class="d-flex justify-center primary--text">
															<div class="hidden-md-and-up margin-top-4em"></div>
															<div class="d-flex justify-center align-center flex-column height_30em">
																<img alt="emptySearchImage" class="candidatesImage" :src="basePath+'vue/assets/images/emptyImgInAttendanceFinal.webp'" @error="imageFormatSwap">
																<div class="text_align_center grey--text title p-t-20">
																	No records found
																</div>
															</div>
														</div>
													</v-data-table>
												</div>
												<no-record-initial-screen
													v-else
													button-text="" 
													:main-title="
														isAttendanceEnforcedPayment !== 'No'
														? 'No Records Found'
														: ''
													"
													image-name="emptyImgInAttendanceFinal"
												>
													<template v-if="isAttendanceEnforcedPayment === 'No'" slot="mainTitleContent">
														The feature to enforce payment based on attendance is currently disabled. If you would like to activate this feature, you have the option to configure it at either the 
														<a @click="redirectToLeaveAttendance('organization/organization-settings')">organization</a>, 
														<a @click="redirectToLeaveAttendance('v3/my-team/team-summary')">employee</a>, or 
														<a @click="redirectToLeaveAttendance('in/core-hr/designations')">designation</a> level.
													</template>
												</no-record-initial-screen>
											</v-col>
										</v-tab-item>
										<v-tab-item value="shortageTab">
											<v-col style="padding: 0px" transition="scale-transition">
												<div v-if="shortageEmployees.length > 0">
													<v-data-table id="gridView" v-model="selectedInNoAttendance" :single-select=false :page.sync="page"
														:items-per-page="parseInt(itemsPerPage, 10)" show-select :headers="shortageHeaders"
														:items="shortageEmployees" item-key="absent_rec_index" hide-default-footer fixed-header height="600px"
														@page-count="pageCount = $event" :search="searchInput" fix-header
													>
														<template v-slot:item.employee_name="{ item }">
															<span
																class="caption primary--text font-weight-bold">{{item.employee_name}}</span>
														</template>
														<template v-slot:item.leave_period="{ item }">
															<span>{{item.leave_period ? item.leave_period : "-"}}</span>
														</template>
														<template v-slot:item.attendance_hours="{ item }">
															<span>{{item.attendance_hours ? item.attendance_hours : "-"}}</span>
														</template>
														<template v-slot:item.deviation_hours="{ item }">
															<span>{{item.deviation_hours ? item.deviation_hours : "-"}}</span>
														</template>
														<template v-slot:header.data-table-select="{ on, props }">
															<v-simple-checkbox color="primary" v-bind="props" v-on="on" on-icon="check_circle"
																indeterminate-icon="remove_circle" off-icon="radio_button_unchecked">
															</v-simple-checkbox>
														</template>
														<template v-slot:item.data-table-select="{ isSelected, select }">

															<v-simple-checkbox color="primary" :value="isSelected" @input="select($event)"
																on-icon="check_circle" off-icon="radio_button_unchecked"></v-simple-checkbox>
														</template>

														<div slot="no-results" class="d-flex justify-center primary--text">
															<div class="hidden-md-and-up margin-top-4em"></div>
															<div class="d-flex justify-center align-center flex-column height_30em">
																<img alt="noRecordFound" class="candidatesImage" :src="basePath+'vue/assets/images/emptyImgInAttendanceFinal.webp'" @error="imageFormatSwap">
																<div class="text_align_center grey--text title p-t-20">
																	No records found
																</div>
															</div>
														</div>

														<div slot="no-data" class="d-flex justify-center primary--text">
															<div class="hidden-md-and-up margin-top-4em"></div>
															<div class="d-flex justify-center align-center flex-column height_30em">
																<img alt="emptySearchImage" class="candidatesImage" :src="basePath+'vue/assets/images/emptyImgInAttendanceFinal.webp'" @error="imageFormatSwap">
																<div class="text_align_center grey--text title p-t-20">
																	No records found
																</div>
															</div>
														</div>
													</v-data-table>
												</div>
												<no-record-initial-screen v-else button-text="" 
													main-title="No Records Found" image-name="emptyImgInAttendanceFinal"
												></no-record-initial-screen>
											</v-col>
										</v-tab-item>
									</v-tabs-items>
								</v-col>
							</v-row>
						</v-container>

						<!-- Attendance Finalization modal -->
						<alert-modal :open-modal="openAlertModal" 
							:show-heading="true" 
							:valid-records="popUpButtonText === 'Proceed' ? 1 : (finalizationSubTab === 'attTab' ? validAttendanceId.length : validNoAttendanceRecordCount)"
							:total-records="finalizationSubTab === 'attTab' ? selectedInAttendance.length : selectedInNoAttendance.length"
							:pop-heading="popupMessage1"  :button-text="finalizationSubTab === 'attTab' ? popUpButtonText : AutoLOPOptionsText" 
							:side-notes="invalidReasonArray" :side-notes-heading="sideNotesHeading"
							@closemodal="fnCancelModal()" @applyclosure="fnApproveAutoClosure()" 
							:disable-button="validAttendanceId.length === 0 ? true : false">
								<template slot="bodyContent">
									<span  style="text-align: center;">
										*By clicking the ' {{finalizationSubTab === 'attTab' ? popUpButtonText : AutoLOPOptionsText}} ' button, you can
										<span class="content-highlighted-text">
											{{popupMessage2}}
										</span>                                 
										for eligible record(s).
									</span>
								</template>
						</alert-modal>
					
						<!-- custom modal component -->
						<custom-modal :open-modal="openCustomModal" avatar-color="amber lighten-1"  icon-name="fa fa-exclamation"
							:valid-records="popUpButtonText === 'Proceed' ? 1 : (finalizationSubTab === 'attTab' ? validAttendanceId.length : validNoAttendanceRecordCount)"
							:total-records="finalizationSubTab === 'attTab' ? selectedInAttendance.length : selectedInNoAttendance.length" :show-actions="popUpButtonText !== 'Proceed'"
							@closemodal="fnCancelModal()" @applyclosure="fnApproveAutoClosure()" :show-heading="popUpButtonText == 'Proceed' ? false : true"
							:pop-heading="popupMessage1" :button-text="finalizationSubTab === 'attTab' ? popUpButtonText : AutoLOPOptionsText">
							<template slot="bodyContent">
								<span v-if="popUpButtonText != 'Proceed'" style="text-align: center;">
									<div v-if="finalizationSubTab === 'noAttTab'" class="text-center mt-n3 mb-3">
										<span class="font-weight-medium caption">
											Please note that it is only applicable to standard business days and excludes weekends and holidays. 
											If you wish to request LOP for a weekend or holiday, you must utilize the <span><a @click="redirectToLeaveAttendance('employees/leaves')">leave</a></span> module.
										</span>
									</div>
									*By clicking the ' {{finalizationSubTab === 'attTab' ? popUpButtonText : AutoLOPOptionsText}} ' button, you can
									<span class="modal-records modal-text-style2">
										{{popupMessage2}}
									</span>
									<span v-show="popUpButtonText != 'Approve'" class="p-r-5">for</span>
									eligible record(s).
								</span>
								<span v-else class="font-weight-bold" style="text-align: center;">
									<span>You have got 
										<span>
											<a @click="redirectToLeaveAttendance('employees/leaves')">leaves</a>
										</span> or 
									</span> 
									<span>
										<a @click="redirectToLeaveAttendance('employees/attendance')">attendance</a>
									</span> or 
									<span>
										<a @click="redirectToLeaveAttendance('employees/compensatory-off')">compensatory off</a>
									</span>
									<span>to be reviewed and approved.
									</span>
								</span>
							</template>
						</custom-modal>
						<!-- Delete Confirmation modal -->
						<custom-delete-confirmation-modal :open-delete-confirmation="openDeleteConfirmation" avatar-color="red lighten-1" icon-name="far fa-trash-alt"
							@close-modal="openDeleteConfirmation = false" @accept-modal="fnDeleteAttendance()">
						</custom-delete-confirmation-modal>
					</div>
					<!-- access is denied for users -->
					<div v-else class="d-flex justify-center align-center flex-column">
						<access-denied-screen></access-denied-screen>
					</div>
				<!-- snack bars for handle success and error messages -->
				<custom-snack-bar v-if="snackbar"
					:show-snack-bar="snackbar" 
					:snack-bar-msg="snackbar_msg" 
					show-emoji="false" 
					emoji=""
					class="pt-12"
					:snack-bar-type="snackbarColor"
					:snack-bar-position-top = "true"
					@close-snack-bar="snackbar = false">
				</custom-snack-bar>
			</v-app>
			<!-- custom loading -->
			<custom-loading-screen v-if="loadingScreen"></custom-loading-screen>
		</template>
	</div>
	
	<!-- script content -->
	<script>
		// filter variables
		var dept_id_list = [], location_id_list = [], emp_type_id_list = [];
		var status_id_list = ['Applied','Draft'];
		let primaryColor, secondaryColor;
		if (!localStorage.getItem("brand_color")) {
        	const { Primary_Color, Secondary_Color} = JSON.parse(localStorage.getItem("brand_color"));
			primaryColor = Primary_Color;
			secondaryColor = Secondary_Color;
		} else {
			primaryColor = '#260029';
			secondaryColor = '#ec407a';
		}
		// veu instance declarations
		var app = new Vue({
			el: '#attendanceFinalization',
			vuetify: new Vuetify(
				{
					theme: {
						options: {
							customProperties: true,
						},
						themes: {
							light: {
								primary: primaryColor,
								secondary: secondaryColor,
								grey: '#9E9E9E'
							}
						}
					}
				}
			),
			data() {
				return {
					finalizationSubTab: "attTab",
					// main variables
					listOfAttendEmployees: [],
					listOfNoAttendanceEmployees:[],
					shortageEmployees: [],
					filterOpen: false,
					filterOpenInMobile:false,
					selectLOPType: false,
					showEarlyCheckout: false,

					// credentials
					atsBaseURL: 'https://api.'+localStorage.getItem('domain')+'/ats/graphql',
					employee_id: parseInt(localStorage.getItem('LoginEmpId'), 10),
					
					// others
					snackbar_msg: "",
					snackbar: false,
					snackbarColor: "warning",
					accessDenied: false,
					approveAutoClosureBtnText: "Initiate auto closure & approval",
					loadingScreen: false,
					pre_req: 0,
					payslipArgs: '',
					notesAlert: false,

					// rights variable
					attendanceFinalizationOptionalChoice: 0,

					// show variables
					showSearch: false,
					showLoadingCard: true,

					// attendance/no attendance actions variables 
					LOPTypes: [],
					isLopTypeRetrieved: false,
					AutoLOPOptionsText : "Initiate LOP",
					openCustomModal : false,
					openAlertModal : false,
					sideNotesHeading : '',
					invalidReasonArray : [],
					popUpButtonText: "",
					isLeavesCompOffAttendanceOpen: 0,
					isAttendanceEnforcedPayment: "",
					// filter variables
					deptList: [],
					locationList:[],
					empTypeList:[],
					deptSelectedInFilter:[],
					statusList : ['Applied','Draft'],
					locationSelectedInFilter:[],
					empTypeSelectedInFilter:[],
					statusSelectedInFilter : [],
					deptIdSelectedInFilter: [],
					locationIdSelectedInFilter: [],
					empTypeIDSelectedInFilter: [],
					statusIDSelectedInFilter : [],

					// filter - start and end date variables
					startDate : "",
					maxStartDate : "",
					formatedStartDate : "",
					startDateModal : false,
					endDate : "",
					minEndDate : "",
					maxEndDate : "",
					endDateModal : false,
					formatedEndDate : "",
					filterMinDate : '',
					filterMaxDate: '',
					isResetFilter: false,
					orgDateFormat: '',
					
					// search variable
					searchInput: "",

					// popup variables
					validAttendanceId:[],
					popupMessage1: "",
					popupMessage2: "",

					// variables to validate AUTO LOP
					autoLOPDetails : [],
					LeaveTypeId : '',
					validNoAttendanceRecordCount : 0,
					validAutoLOPRecords : [],
			
					// data grid variables
					openPagesMenu: false,
					selectedAttendanceId:[],
					selectedInAttendance: [],
					selectedStatusInAttendance: [],
					selectedInNoAttendance: [],
					page: 1,
					pageCount: 1,
					itemsPerPage: 10,
					items: [10, 50, 100, 'All'],
					pageNumber: 'All',
					openMoreMenu: false,
					headersAttendance: [
						{
							text: 'Employee Id',
							align: 'left',
							value: 'user_defined_empid'
						},
						{
							text: 'Employee Name',
							value: 'employee_name'
						},
						{
							text: 'Date In',
							value: 'punchin_date'
						},
						{
							text: 'Time In',
							value: 'punchin_time'
						},
						{
							text: 'Date Out',
							value: 'punchout_date'
						},
						{
							text: 'Time Out',
							value: 'punchout_time'
						},
						{
							text: 'Total Hours',
							value: 'total_hours'
						},
						{
							text: 'Status',
							value: 'approval_status'
						},
					],
					headersNoAttendance: [
						{
							text: 'Employee Id',
							align: 'left',
							value: 'user_defined_empid'
						},
						{
							text: 'Employee Name',
							value: 'employee_name'
						},
						{
							text: 'Date',
							value: 'd_absent_date'
						},
						{
							text: 'Duration',
							value: 'leave_duration'
						},
						{
							text: 'Leave period',
							value: 'leave_period'
						},
					],
					shortageHeaders: [
						{
							text: 'Employee Id',
							align: 'left',
							value: 'user_defined_empid'
						},
						{
							text: 'Employee Name',
							value: 'employee_name'
						},
						{
							text: 'Date',
							value: 'd_absent_date'
						},
						{
							text: 'Duration',
							value: 'leave_duration'
						},
						{
							text: 'Leave period',
							value: 'leave_period'
						},
						{
							text: 'Attendance Hours',
							value: 'attendance_hours'
						},
						{
							text: 'Shortage Hours',
							value: 'deviation_hours'
						},
					],
					showAttendanceShortageTab: false,

					// delete confirmation
					openDeleteConfirmation : false,
					windowWidth: 0,

					// headers
					ipAddress: "",
					partnerid: $cookies.get("partnerid"),
					dCode: $cookies.get("d_code"),
					bCode: $cookies.get("b_code"),
				}
			},
			computed: {
				isLocalEnv() {
					let currentUrl = window.location.href;
					if (
						parseInt(localStorage.getItem("isProduction"), 10) === 0 ||
						currentUrl.includes("hrapponline")
					) {
						return true;
					} else {
						return false;
					}
				},
				//to get orgCode dynamically from the current url
				orgCode() {
					if (this.isLocalEnv) {
						return "happy"; // local db connection
					} else {
						let oCode1 = localStorage.getItem("orgCode");
						if (oCode1) {
							return oCode1;
						} else {
							let url = window.location.href;
							let urlNoProtocol = url.replace(/^https?:\/\//i, "");
							let oCode = urlNoProtocol.split(".");
							oCode = oCode[0];
							return oCode;
						}
					}
				},
				basePath() {
					if (localStorage.getItem('production') == 0) {
						return '/hrapponline/'
					} else {
						return '/'
					}
				},
				displayedListCount() {
					if(this.finalizationSubTab === "attTab")
						return this.attendanceCount;
					else if(this.finalizationSubTab === "shortageTab")
						return this.shortageCount;
					else return this.noAttendanceCount;
				},
				attendanceCount() {
					return this.listOfAttendEmployees ? this.listOfAttendEmployees.length : 0;
				},
				noAttendanceCount() {
					return this.listOfNoAttendanceEmployees ? this.listOfNoAttendanceEmployees.length : 0;
				},
				shortageCount() {
					return this.shortageEmployees ? this.shortageEmployees.length : 0;
				},
				apiHeaders(){
					let authorizationHeader = $cookies.get("accessToken") ? $cookies.get("accessToken") : null;
					let refreshTokenHeader = $cookies.get("refreshToken") ? $cookies.get("refreshToken") : null;
					return {
						'Content-Type': 'application/json',
						org_code: this.orgCode,
						Authorization: authorizationHeader,
						refresh_token: refreshTokenHeader,
						user_ip: this.ipAddress,
						partnerid: this.partnerid ? this.partnerid : "-",
						additional_headers: JSON.stringify(
							{
								org_code: this.orgCode,
								Authorization: authorizationHeader,
								refresh_token: refreshTokenHeader,
								user_ip: this.ipAddress,
								partnerid: this.partnerid ? this.partnerid : "-",
								d_code: this.dCode,
								b_code: this.bCode,
							}
						)
					}
				},
			},
			created() {
				// get pre-req value from url
				var url_string = window.location.href;
				var url = new URL(url_string);
				this.pre_req = url.searchParams.get("pre-req");
				this.payslipArgs = url.searchParams.get("data");				
				try{
					axios.get('https://api.ipify.org?format=json').then(response => { 
						this.ipAddress = response.data.ip;
					}).catch(error => {
						/* If the IP address API is not available, API URL is wrong or internet connection is not available,
						then the error.readyState will be 0 or 4  */
						if((error.readyState === 0 || error.readyState === 4) && ipAddressRestriction == 1) {
							this.snackbar_msg = "Unable to get the IP address. Please contact system Administrator."
							this.snackbarColor = "warning";
							this.snackbar = true;
						} else {
							this.ipAddress = "IP Blocked by user";
						}
					})
				}catch{
					this.ipAddress = "";
				}
				window.$cookies.set("userIpAddress", this.ipAddress);
			},
			watch : {
				finalizationSubTab(val) {
					if(val?.toLowerCase() == "earlycheckouttab") {
						let earlyCheckoutUrl = base_url_final+ "v3/my-team/early-checkout";
						if(this.payslipArgs){
							earlyCheckoutUrl += '?data='+this.payslipArgs;
							window.open(earlyCheckoutUrl, '_blank');
						}else{
							window.location.href = earlyCheckoutUrl;
						}
					} else {
						this.notesAlert = false;
						this.isLeavesCompOffAttendanceOpen = 0;
						this.isAttendanceEnforcedPayment = "";
						//  change to noAttendance, we have to reset attendance selected lists
						this.selectedInNoAttendance = [];
						this.selectedInAttendance = [];
						this.pageNumber = 50;
						if(val === "noAttTab" && !this.isLopTypeRetrieved) {
							this.fetchUnpaidLeaveList();
						}
						this.listAttendanceEmployees(val === 'attTab' ? "attendance" : "noAttendance", "showAlert");
					}
				},
				startDate (val) {
					// update the date values only when the function is not filter reset 
					if(this.isResetFilter === false) {
						this.formatedStartDate = this.formatDate(this.startDate) //change the date chosen to show as org date format
						this.minEndDate = this.startDate; //set the min end date as start date
					}
					
				},
				endDate (val) {
				// update the date values only when the function is not filter reset 
				if(this.isResetFilter === false) {

					this.formatedEndDate = this.formatDate(this.endDate);
					this.maxStartDate = this.endDate;
				}
				},
				filterOpen (val) {
				//   reset the isResetFilter to false when filter modal is opened
					if(val === true) {
					this.isResetFilter = false;
					}

				this.fnToDisableScroll(this.filterOpen);
				},
				filterOpenInMobile(val) {
					//   reset the isResetFilter to false when filter modal is opened
					if (val === true) {
						this.isResetFilter = false;
					}

					this.fnToDisableScroll(this.filterOpenInMobile);
				},
				openPagesMenu(val){
					this.fnToDisableScroll(this.openPagesMenu);
				},
				 selectLOPType(val){
					this.fnToDisableScroll(this.selectLOPType);
				 },
				 selectedInAttendance(val) {
					this.selectedAttendanceId = [];
					this.selectedStatusInAttendance = [];
					for(var sel = 0; sel < val.length; sel++){
						this.selectedAttendanceId.push(val[sel].attendance_id);
						this.selectedStatusInAttendance.push(val[sel].approval_status);
					}
					this.fnDefineButtonText();
				 },
				 pageNumber(val) {
					 var table_list = this.finalizationSubTab === 'attTab' ? this.listOfAttendEmployees :
					 this.finalizationSubTab === "shortageTab" ? this.shortageEmployees :
					  this.listOfNoAttendanceEmployees;
					if (val === 'All') {
						this.pageCount = 1;
						this.itemsPerPage = table_list.length;
						this.page = 1;
					}
					else {
						var page_count = table_list.length / this.pageNumber;
						this.pageCount = page_count <= 1 ? 1 : page_count;
						this.itemsPerPage = this.pageNumber;
						this.page = 1;
					}
				},
			},
			mounted() {
				setTimeout(()=>{
					this.loadingScreen = true
					this.fnInitialCall();
				},2000);

				this.$nextTick(function () {
					window.addEventListener('resize', this.getWindowWidth);
					//Init
					this.getWindowWidth()
				})
			},
			beforeDestroy() {
				window.removeEventListener('resize', this.getWindowWidth);
			},
			methods: {
				// initial call
				fnInitialCall(){
					// check attendance-finalization form access
					this.fnCheckAccessRights();
					this.fnCheckAccessRightsEarlyCheckout();
					//get the org date format
					axios.post(base_url_final + `employee-info/org-date-format`, {
					}).then(response => {
						// org date format
						this.orgDateFormat = response.data[0];
					})
					.catch(function (Err) {
						this.orgDateFormat = "YYYY/MM/DD"; //by default we have this format.Change based on org format
					});
				},
				fetchDropDownBoxDetails() {
					// fetch the dropdown data
					var self = this;
					self.loadingScreen = true;
					// graphql url configuration
					var graph1 = graphql(this.atsBaseURL, {
						method: 'POST',
						headers: this.apiHeaders,
						asJSON: true
					});


					// dropdown query
					var fetchDropdownDetails = graph1.query(`samplequery { getDropDownBoxDetails { errorCode message departments { Department_Id Department_Name} locations { Location_Id Location_Name} employeeType { EmpType_Id Employee_Type}}}`);
					// calling backend to get designation, department, location, employee type
					fetchDropdownDetails().then(function (response){
					var res_data = response.getDropDownBoxDetails;
					var department_list = [], location_list = [],emp_type_list = [];
					// for department
					for (var dept = 0; dept < res_data.departments.length; dept++) {
						department_list.push(res_data.departments[dept].Department_Name);
						dept_id_list.push(res_data.departments[dept].Department_Id);
					}
					// for location
					for (var loc = 0; loc < res_data.locations.length; loc++) {
						location_list.push(res_data.locations[loc].Location_Name);
						location_id_list.push(res_data.locations[loc].Location_Id);
					}
					// for emp type
					for (var type = 0; type < res_data.employeeType.length; type++) {
						emp_type_list.push(res_data.employeeType[type].Employee_Type);
						emp_type_id_list.push(res_data.employeeType[type].EmpType_Id);
					}
					
					self.deptList = department_list;
					self.locationList = location_list;
					self.empTypeList = emp_type_list;
					self.loadingScreen = false;
					}).catch(function (){
						self.snackbar_msg = "Something went wrong while retrieving dropdown details. Please try after some time."
						self.snackbar = true;
						self.snackbarColor = "warning";
						self.loadingScreen = false;
					});
				},
				fetchUnpaidLeaveList() {
					let self = this;
					self.loadingScreen = true;
					// list the types of LOP
					axios.post(base_url_final + `employees/attendance-finalization/get-unpaid-leaves`)
					.then(response => {
						var result = response.data;
						if(result.success === true) {
							var leaveTypes = result.leaveTypes;
							self.LOPTypes = leaveTypes;
							self.isLopTypeRetrieved = true;
						}
						self.loadingScreen = false;
					})
					.catch(function () {
						self.snackbar = true;
						self.snackbarColor = "warning";
						self.snackbar_msg = "Something went wrong while retrieving unpaid leaves. Please try after some time.";
						self.loadingScreen = false;
					});
				},
				//function to switch image to png format if browser not support webp
				imageFormatSwap(e){
					e.target.src=this.basePath+'images/emptyImgInAttendanceFinal.png';
				},
				notesImageFormatSwap(e){
					e.target.src=this.basePath+'images/idea-bulb.png';
				},
				// hide and show of filter based on window size
				getWindowWidth(event) {
					this.windowWidth = document.documentElement.clientWidth;
					if (this.filterOpen && this.windowWidth <= 960) {
						this.filterOpen = false;
					}
					if (this.filterOpenInMobile && this.windowWidth > 960) {
						this.filterOpenInMobile = false;
					}

				},
				selectPageNumber(val) {
					this.pageNumber = val
				},
				// fn to disable scroll when menu is opened
				fnToDisableScroll(val){
					if (val) {
						document.documentElement.style.overflow = 'hidden';
						return;
					}
					document.documentElement.style.overflow = 'auto';
				},
				// define button text names
				fnDefineButtonText(){
					if (this.selectedStatusInAttendance.length && this.selectedStatusInAttendance.every(v => v === 'Draft')) {
						this.approveAutoClosureBtnText = "Initiate auto closure"
					} else if (this.selectedStatusInAttendance.length && this.selectedStatusInAttendance.every(v => v === 'Applied')) {
						this.approveAutoClosureBtnText = "Approve"
					} else {
						this.approveAutoClosureBtnText = "Initiate auto closure & approval"
					}
				},
				// function To Set Initial Value
				fnSetInitialValue(){
					this.isLeavesCompOffAttendanceOpen = 0;
					this.isAttendanceEnforcedPayment = "";
					this.approveAutoClosureBtnText = "Initiate auto closure & approval";
					this.selectedStatusInAttendance = [];
					this.selectedInAttendance = [];
					this.selectedInNoAttendance = [];
					this.searchInput = '';
				},
				// check access rights
				fnCheckAccessRights() {
					var self = this;
					self.loadingScreen = true;
					var graph1 = graphql(self.atsBaseURL, {
						method: 'POST',
						headers: this.apiHeaders,
						asJSON: true
					});
					var accessRights = graph1(`mutation(
                    $formName: String,
                    $employeeId: Int!,
					$formId: Int) {
						getAccessRights
							(
								formName: $formName,
								employeeId:$employeeId
								formId: $formId
							) 
							{
								errorCode message rights {
									Role_View Role_Add Role_Update Role_Delete Role_Optional_Choice Role_Hr_Group Role_Payroll_Group Is_Manager
							    }
							}
						}
                	`);
					accessRights({
						formId: 190,
						employeeId: self.employee_id
					})
					.then(function (response) {
						if (response.getAccessRights && response.getAccessRights.rights && response.getAccessRights.rights.Role_View === 1) {
							self.fetchDropDownBoxDetails();
							self.listAttendanceEmployees("attendance","initialCall");
							self.attendanceFinalizationOptionalChoice = response.getAccessRights.rights.Role_Optional_Choice;
							self.loadingScreen = false;
						} else {
							self.accessDenied = true;
							self.loadingScreen = false;
						}
					})
					.catch(function () {
						self.accessDenied = true;
						self.loadingScreen = false;
					});
				},
				fnCheckAccessRightsEarlyCheckout() {
					var self = this;
					self.loadingScreen = true;
					var graph1 = graphql(self.atsBaseURL, {
						method: 'POST',
						headers: this.apiHeaders,
						asJSON: true
					});
					var accessRights = graph1(`mutation(
                    $formName: String,
                    $employeeId: Int!) {
						getAccessRights
							(
								formName: $formName,
								employeeId:$employeeId
							) 
							{
								errorCode message rights {
									Role_View Role_Add Role_Update Role_Delete Role_Optional_Choice Role_Hr_Group Role_Payroll_Group Is_Manager
							    }
							}
						}
                	`);
					accessRights({
						formName: "Early Checkout",
						employeeId: self.employee_id
					})
					.then(function (response) {
						if (response.getAccessRights && response.getAccessRights.rights && response.getAccessRights.rights.Role_View === 1) {
							self.showEarlyCheckout = true;
							self.loadingScreen = false;
						} else {
							self.showEarlyCheckout = false;
							self.handleApiErrors();
							self.loadingScreen = false;
						}
					})
					.catch(function (err) {
						self.showEarlyCheckout = false;
						self.handleApiErrors(err);
						self.loadingScreen = false;
					});
				},
				handleApiErrors(err = ""){
					let errorCode;
					if(error && error.errors && error.errors.length > 0) {
						errorCode = error.errors[0].extensions.code;
					} else if(error && error.length > 0 && error[0].message) {
						let errors = JSON.parse(error[0].message);
						errorCode = errors.errorCode;
					}
					if (errorCode) {
						switch (errorCode) {
							//technical errors 
							case '_DB0000': // db connection
							case 705:
							case 706:
								this.snackbar_msg = 'There seems to be some technical issues. Please try after some time.';
								break;
							case 751: // Could not check access rights
							case "_UH0001": // unhandled error
							default:
								this.snackbar_msg = 'Something went wrong. If you continue to see this issue please contact system administrator.';
								break;
						}
					} else {
						this.snackbar_msg = 'Something went wrong. Please contact system administrator.';
					}
					this.snackbarColor = "warning";
					this.snackbar = true;
				},
				// list of attendance/no attendance employees
				listAttendanceEmployees(type,operationType = ""){
					this.showLoadingCard = true;
					let filterStartDate = this.pre_req == 1 ?  localStorage.getItem('paycycleStartDate') : (this.formatedStartDate ? fnServerDateFormatter(this.startDate) : '');
					let filterEndDate = this.pre_req == 1 ?   localStorage.getItem('paycycleEndDate') : (this.formatedEndDate ? fnServerDateFormatter(this.endDate) : '');
					let validationErrorMessage = '';
					//If the attendance finalization list is not called for the first time, then validate the filter dates
					if(operationType !== "initialCall"){
						if(filterStartDate && filterEndDate){
							let filterStartEndDateDiff = new Date(filterEndDate) - new Date(filterStartDate);
							let filterDatesDiff = (Math.ceil(filterStartEndDateDiff / (1000 * 60 * 60 * 24)))+1; 
							//If the filter start date and end date difference is greater than 31 days
							if(filterDatesDiff > 31){
								if(type === 'noAttendance'){
									validationErrorMessage = "No Attendance details cannot be filtered more than 31 days. Please update either the start date or end date in the filter.";
								}else{
									validationErrorMessage = "Attendance details cannot be filtered more than 31 days. Please update either the start date or end date in the filter.";
								}
							}
						}else{
							validationErrorMessage = "Please select start date and end date in the filter.";
						}
					}

					if(validationErrorMessage === ''){
						var payslipEmpIds = localStorage.getItem('payslipEmployeeIds') ? localStorage.getItem('payslipEmployeeIds').split(",") : [];
						axios.post(base_url_final + `employees/attendance-finalization/list-attendance-finalization`, { 
							finalizationMethod: type,
							startDate: filterStartDate,
							endDate: filterEndDate,
							employeeId: this.pre_req == 1 ? payslipEmpIds : [], 
							status: type === "noAttendance" ? [] : (this.statusIDSelectedInFilter.length === 0 ? this.statusList : this.statusIDSelectedInFilter),
							employeeType: this.empTypeIDSelectedInFilter,
							location: this.locationIdSelectedInFilter,
							department: this.deptIdSelectedInFilter,
							actualSubTab: this.finalizationSubTab
						}).then(response => {
							if(response.data){
								if (response.data.success) {
									this.notesAlert = true;
									setTimeout(()=>{
										this.notesAlert = false;
									},60000);
									// attendance list
									if(type === "attendance"){
										this.listOfAttendEmployees = response.data.attendanceData.aaData;
										this.isAttendanceEnforcedPayment = response.data.attendanceData.attendanceEnforcedPayment;
									}
									// no attendance list
									else  {
										this.isLeavesCompOffAttendanceOpen = response.data.noAttendanceData.isLeavesCompOffAttendanceOpen;
										this.isAttendanceEnforcedPayment = response.data.noAttendanceData.attendanceEnforcedPayment;
										let resData = response.data.noAttendanceData.aaData;
										let resDataLength = 0;
										if(this.finalizationSubTab === "noAttTab") {
											this.listOfNoAttendanceEmployees = resData.filter(
												el => this.convertHMToSecs(el.attendance_hours) === 0
											);
											resDataLength = this.listOfNoAttendanceEmployees.length;
										}
										if(this.finalizationSubTab === 'shortageTab') {
											this.shortageEmployees = resData.filter(
												el => this.convertHMToSecs(el.attendance_hours) > 0
											);
											resDataLength = this.shortageEmployees.length;
											this.notesAlert = false;
										}
										if(this.finalizationSubTab === "noAttTab" && this.isLeavesCompOffAttendanceOpen == 1 && operationType === "showAlert" && resDataLength >0) {
											this.popupMessage1 = "";
											this.popUpButtonText = "Proceed";
											this.openCustomModal = true;
										}
									}
									this.showAttendanceShortageTab = response.data.strictModeEmployees ? response.data.strictModeEmployees.length > 0 : false;

									// minimum ana maximum date to set in filter
									this.filterMinDate = this.pre_req == 1 ? localStorage.getItem('paycycleStartDate') : response.data.filterMinDate;
									this.filterMaxDate = this.pre_req == 1 ? localStorage.getItem('paycycleEndDate') : response.data.filterMaxDate;
									// reset the search and filter fields in any other operations - sort, refresh, attendance/no attendance
									if(operationType === "initialCall") {
										this.showSearch = false;
										this.setResetDateFilters(0);
									}									
									this.selectPageNumber(50);
								} else{
									this.snackbar_msg = "Something went wrong. Please try after some time."
									this.snackbarColor = "warning";
									this.snackbar = true;
								}
							} else{
								this.snackbar_msg = "Something went wrong. Please try after some time."
								this.snackbarColor = "warning";
								this.snackbar = true;
							}
							this.showLoadingCard = false;
						}).catch(Err => {
							this.snackbar_msg = "Something went wrong. Please try after some time."
							this.snackbarColor = "warning";
							this.snackbar = true;
							this.showLoadingCard = false;
						})
					}else{
						this.snackbar_msg = validationErrorMessage;
						this.snackbarColor = "warning";
						this.snackbar = true;
						this.showLoadingCard = false;
					}
				},

				convertHMToSecs(hmTime) {
					let isNumberValue = Number.isInteger(hmTime); // checking the value is integer or not
					if(!hmTime) { // if the value is empty/0/null/undefined then we can return it as 0
						return 0;
					} else if (!isNumberValue) { // if the value is coming as non-integer then we need to convert it to seconds
						if(hmTime.includes(":")) { // if the value is in hh:mm format, then we need to split the hours and minutes using : and we can convert it to secs
							var timeSplit = hmTime.split(':');
							if(timeSplit && timeSplit.length > 0) {
								var seconds = (+timeSplit[0]) * 60 * 60 + (+timeSplit[1]) * 60;
								return seconds;
							} else return hmTime;
						} else if(hmTime.includes(".")) { // if the value is decimal, then we need to split the hours and minutes using . and we can convert it to secs
							var timeSplit = hmTime.split('.');
							if(timeSplit && timeSplit.length > 0) {
								var seconds = (+timeSplit[0]) * 60 * 60 + (+timeSplit[1]) * 60;
								return seconds;
							} else return hmTime;
						} else return parseInt(hmTime); // when none of the conditions meet then we can return as integer
					} else return hmTime; // when the value is coming as integer then no need to convert it to seconds
				},

				// function to validate or apply auto closure
				fnValidateAutoClosure(){
					this.loadingScreen = true;
					// check attendance finalization access rights 
					if(this.attendanceFinalizationOptionalChoice === 1) {
						if(this.selectedAttendanceId.length > 0){
							axios.post(base_url_final + `employees/attendance-finalization/validate-attendance-finalization/`, {
								attendanceId: this.selectedAttendanceId,
							}).then(response => {
								if (response.data) {
									if (response.data.success) {
										var text = this.approveAutoClosureBtnText === "Approve" ? "approved" : (this.approveAutoClosureBtnText === "Initiate auto closure" ? "initiated for auto closure" : "auto closed and approved");
										this.validAttendanceId = response.data.validatedResult.validAttendanceIds;
										this.popupMessage1 = "record(s), can be " + text;
										this.popupMessage2 = this.approveAutoClosureBtnText;
										if(this.selectedAttendanceId.length === this.validAttendanceId.length){
											this.fnApproveAutoClosure();
										}else{
											this.popUpButtonText = this.approveAutoClosureBtnText;
											this.invalidReasonArray = response.data.validatedResult.invalidReason;
											this.openAlertModal = true;
											this.sideNotesHeading = "The attendance record(s) cannot be processed because of one or more of the following reason(s).";
											this.loadingScreen = false;
										}
									} else {
										this.snackbar_msg = response.data.msg;
										this.snackbarColor = "warning";
										this.snackbar = true;
										this.loadingScreen = false;
									}
								} else {
									this.snackbar_msg = "Something went wrong. Please try after some time."
									this.snackbarColor = "warning";
									this.snackbar = true;
									this.loadingScreen = false;
								}
							}).catch(Err => {
								this.snackbar_msg = "Something went wrong. Please try after some time."
								this.snackbarColor = "warning";
								this.snackbar = true;
								this.loadingScreen = false;
							})
						}else{
							this.snackbar_msg = this.listOfAttendEmployees.length == 0 ? "There are no record(s) exist to initiate auto closure" : "Please select minimum one record"
							this.snackbarColor = "warning";
							this.snackbar = true;
							this.loadingScreen = false;
						}
					}
					else {
						this.snackbar_msg = "Sorry, Access Denied."
						this.snackbarColor = "warning";
						this.snackbar = true;
						this.loadingScreen = false;
					}
				},
				initiateAttendanceShortageLeave() {
					this.loadingScreen = true;
					if(this.selectedInNoAttendance.length > 0) {
						let noAttDetails = [];
						for(var noAttendanceRec of this.selectedInNoAttendance) {
							let noAttObj = {
								Absent_Date: noAttendanceRec.absent_date,
								Contact_Details: noAttendanceRec.contact_details,
								Employee_Id: noAttendanceRec.employee_id,
								Hours: noAttendanceRec.hours,
								Leave_Duration: noAttendanceRec.leave_duration,
								Leave_Period: noAttendanceRec.leave_period,
							};
							noAttDetails.push(noAttObj);
						}
						axios.post(base_url_final + `employees/attendance-finalization/initiate-attendance-shortage-leave/`, {
							noAttendanceDetails: noAttDetails,
						}).then(response => {
							if (response.data) {
								if (response.data.success) {
									this.snackbarColor = "success";
									this.snackbar_msg = "Attendance record(s) are initiated as per the attendance shortage configuration successfully";
									this.snackbar = true;
									this.fnSetInitialValue();
									this.listAttendanceEmployees('noAttendance');
								} else {
									this.snackbar_msg = response.data.msg;
									this.snackbarColor = "warning";
									this.snackbar = true;
								}
							} else {
								this.snackbar_msg = "Something went wrong. Please try after some time."
								this.snackbarColor = "warning";
								this.snackbar = true;
							}
							this.loadingScreen = false;
						}).catch(Err => {
							this.snackbar_msg = "Something went wrong. Please try after some time."
							this.snackbarColor = "warning";
							this.snackbar = true;
							this.loadingScreen = false;
						})
					} else {
						this.snackbar_msg = "Kindly select the attendance record(s) to initiate the attendance shortage leave."
						this.snackbarColor = "warning";
						this.snackbar = true;
						this.loadingScreen = false;
					}
				},
				//Function to ignore the attendance shortage records
				ignoreAttendanceShortage(){
					this.loadingScreen = true;
					if(this.selectedInNoAttendance.length > 0) {
						let ignoreAttShortageEmpDetails = [];
						for(let attShortageRec of this.selectedInNoAttendance) {
							let ignoreEmp = {
								Employee_Id: attShortageRec.employee_id,
								Attendance_Date: attShortageRec.absent_date,
								Shortage_Hours: attShortageRec.actual_deviation_hours	
							};
							ignoreAttShortageEmpDetails.push(ignoreEmp);
						}
						axios.post(base_url_final + `employees/attendance-finalization/initiate-ignore-attendance-shortage/`, {
							ignoreAttShortageEmp: ignoreAttShortageEmpDetails,
						}).then(response => {
							if (response.data) {
								if (response.data.success) {
									this.snackbarColor = "success";
									this.snackbar_msg = "Attendance shortage record(s) ignored successfully";
									this.snackbar = true;
									this.fnSetInitialValue();
									this.listAttendanceEmployees('noAttendance');
								} else {
									this.snackbar_msg = response.data.msg;
									this.snackbarColor = "warning";
									this.snackbar = true;
								}
							} else {
								this.snackbar_msg = "Something went wrong. Please try after some time."
								this.snackbarColor = "warning";
								this.snackbar = true;
							}
							this.loadingScreen = false;
						}).catch(Err => {
							this.snackbar_msg = "Something went wrong. Please try after some time."
							this.snackbarColor = "warning";
							this.snackbar = true;
							this.loadingScreen = false;
						})
					} else {
						this.snackbar_msg = "Kindly select the attendance record(s) to ignore."
						this.snackbarColor = "warning";
						this.snackbar = true;
						this.loadingScreen = false;
					}
				},
				// function to approve auto closure/ apply LOP - common function
				fnApproveAutoClosure(){
					this.loadingScreen = true;
					if( this.popUpButtonText === "Proceed"){
						this.popUpButtonText = this.approveAutoClosureBtnText;
						this.openCustomModal = false;
						this.loadingScreen = false;
					}else{
						// check if the action is for attendance
						if (this.finalizationSubTab === 'attTab') {
							axios.post(base_url_final + `employees/attendance-finalization/initiate-auto-closure-and-approval/`, {
								attendanceId: this.validAttendanceId,
							}).then(response => {
								if (response.data) {
									if (response.data.success) {
										this.snackbarColor = "success";
										var successText = this.approveAutoClosureBtnText === "Approve" ? "approved" : (this.approveAutoClosureBtnText === "Initiate auto closure" ?
											"auto closed" : "approved and auto closed");
										this.snackbar_msg = "Attendance record(s) are " + successText + " successfully";
										this.snackbar = true;
										this.popUpButtonText = this.approveAutoClosureBtnText;
										this.openCustomModal = false;
										this.openAlertModal  = false;
										this.fnSetInitialValue();
										this.listAttendanceEmployees('attendance');
									} else {
										this.snackbar_msg = response.data.msg;
										this.snackbarColor = "warning";
										this.snackbar = true;
									}
								} else {
									this.snackbar_msg = "Something went wrong. Please try after some time."
									this.snackbarColor = "warning";
									this.snackbar = true;
								}
								this.loadingScreen = false;
							}).catch(Err => {
								this.snackbar_msg = "Something went wrong. Please try after some time."
								this.snackbarColor = "warning";
								this.snackbar = true;
								this.loadingScreen = false;
							})
						}
						// if action is no attendance - Apply LOP
						else {
							// apply lop for validate records
							axios.post(base_url_final + `employees/attendance-finalization/initiate-auto-lop/`, {
								Auto_LOP_Details: this.validAutoLOPRecords,
								LeaveType_Id: this.LeaveTypeId,
								startDate: this.startDate,
								endDate: this.endDate

							}).then(response => {
								if (response.data) {
									if (response.data.success) {
										// if success, show the success message
										this.snackbarColor = "success";
										this.snackbar_msg = "Loss of pay initiated successfully";
										this.snackbar = true;
										this.popUpButtonText = this.approveAutoClosureBtnText;
										this.openCustomModal = false;
										// refresh the list
										this.fnSetInitialValue();
										this.listAttendanceEmployees('noAttendance');
									} else {
										this.snackbar_msg = response.data.msg;
										this.snackbarColor = "warning";
										this.snackbar = true;
									}
								} else {
									this.snackbar_msg = "Something went wrong. Please try after some time."
									this.snackbarColor = "warning";
									this.snackbar = true;
								}
								this.loadingScreen = false;
							}).catch(Err => {
								this.snackbar_msg = "Something went wrong. Please try after some time."
								this.snackbarColor = "warning";
								this.snackbar = true;
								this.loadingScreen = false;
							})
						}
					}
					
				},
				// delete action for attendance
				fnDeleteAttendance(){
					this.loadingScreen = true;
					this.openDeleteConfirmation = false;
					axios.post(base_url_final + `employees/attendance-finalization/delete-attendance-finalization/`, {
						attendanceId: this.selectedAttendanceId,
						attendanceImportExist: 0
					}).then(response => {
						if(response.data){
							if (response.data.success) {
								this.fnSetInitialValue();
								this.snackbarColor = "success";
								this.snackbar_msg = "Attendance record(s) deleted successfully"
								this.snackbar = true;
								this.listAttendanceEmployees('attendance');
							} else {
								this.snackbar_msg = response.data.msg;
								this.snackbarColor = "warning";
								this.snackbar = true;
							}
						}else{
							this.snackbar_msg = "Something went wrong. Please try after some time."
							this.snackbarColor = "warning";
							this.snackbar = true;	
						}
						this.loadingScreen = false;
					}).catch(Err =>{
						this.snackbar_msg = "Something went wrong. Please try after some time."
						this.snackbarColor = "warning";
						this.snackbar = true;
						this.loadingScreen = false;
					})
				},

				// redirect to attendance module
				redirectToEmpModule() {
					window.location.href = base_url_final + "employees/attendance";
				},

				// function to refresh attendance employees details
				fnRefreshAttendanceEmployees(){
					this.fnResetStartEndDates();
					this.resetFilterValues(0);
					this.fnSetInitialValue();
					this.listAttendanceEmployees(this.finalizationSubTab === 'attTab' ? "attendance" : "noAttendance");
				},
				// fucntion to reset start and end dates based on local storage
				fnResetStartEndDates(){
					if (localStorage.getItem('paycycleStartDate') && localStorage.getItem('paycycleEndDate')) {
						this.startDate = localStorage.getItem('paycycleStartDate');
						this.endDate = localStorage.getItem('paycycleEndDate');
						this.pre_req = 1;
					} else {
						this.pre_req = 0;
					}
				},
				// function to close modal
				fnCancelModal(){
					this.approveAutoClosureBtnText = "Initiate auto closure & approval";
					this.AutoLOPOptionsText = "Initiate LOP";
					this.popUpButtonText = this.approveAutoClosureBtnText;
					this.openCustomModal = false;
					this.openAlertModal = false;
				},

				fnSelectLOPList(){
					if(this.LOPTypes.length == 0){
						if(this.selectedInNoAttendance.length > 0){
							this.snackbar_msg = "Please add Unpaid leave type to initiate LOP";
						}else if(this.selectedInNoAttendance == 0){
							this.snackbar_msg = "Please select minimum one record"
						}else{
							this.snackbar_msg = "There are no record(s) exist to initiate LOP"
						}
						this.snackbarColor = "warning";
						this.snackbar = true;
					}
				},

				// format the date to org format
				formatDate (date) {
					if (!date) return null;
					// get the date in organization date format.
					var date =  this.fnFormatDate(date);
					return date;
				},

				//  department onchange function  
				deptOnChange(){
					var deptArray = [];
					for (var i = 0; i < this.deptSelectedInFilter.length; i++) {
						deptArray.push(dept_id_list[this.deptList.indexOf(this.deptSelectedInFilter[i])]);
					}
					this.deptIdSelectedInFilter = deptArray;
				},
				//  location onchange function
				locationOnChange(){
					var locIdArray = [];
					for (var i = 0; i < this.locationSelectedInFilter.length; i++) {
						locIdArray.push(location_id_list[this.locationList.indexOf(this.locationSelectedInFilter[i])]);
					}
					this.locationIdSelectedInFilter = locIdArray;
				},
				//  employee type onchange function
				empTypeOnChange(){
					var empTypeArray = [];
					for (var i = 0; i < this.empTypeSelectedInFilter.length; i++) {
						empTypeArray.push(emp_type_id_list[this.empTypeList.indexOf(this.empTypeSelectedInFilter[i])]);
					}
					this.empTypeIDSelectedInFilter = empTypeArray;
				},
				// status onchange function
				statusOnChange() {
					var statusArray = [];
					for(var i =0 ; i<this.statusSelectedInFilter.length;i++){
						statusArray.push(status_id_list[this.statusList.indexOf(this.statusSelectedInFilter[i])]);
					}
					this.statusIDSelectedInFilter = statusArray;
				},
				// onclick function for filter apply button
				fnApplyFilter(){
					this.pre_req = 0;
					// reset the initial values
					this.fnSetInitialValue();
					this.filterOpen = false;
					this.filterOpenInMobile = false;
					var type = this.finalizationSubTab === 'attTab' ? 'attendance' : 'noAttendance';
					this.listAttendanceEmployees(type);
				},
				// onclick function for reset filter button
				resetFilterValues(isResetList) {

					// set the reset filter
					this.isResetFilter = true;

					// reset the filter values
					this.deptSelectedInFilter = this.statusSelectedInFilter = this.locationSelectedInFilter = this.empTypeSelectedInFilter = [];
					this.deptIdSelectedInFilter = this.statusIDSelectedInFilter = this.locationIdSelectedInFilter = this.empTypeIDSelectedInFilter = [];
					
					// reset the date filters
					this.setResetDateFilters(isResetList);

					// close the filter menu
					this.filterOpen = false;
					this.filterOpenInMobile = false;
					
				},
				setResetDateFilters(isResetList) {

					this.filterMinDate = this.pre_req == 1 ? localStorage.getItem('paycycleStartDate') : this.filterMinDate;
					this.filterMaxDate = this.pre_req == 1 ? localStorage.getItem('paycycleEndDate') : this.filterMaxDate;

					// set/reset the start date fields
					this.startDate = this.filterMinDate;
					this.maxStartDate = new Date().toISOString().substr(0, 10);
					this.formatedStartDate = this.formatDate(this.filterMinDate);
					this.startDateModal = false;

					const currentDate = moment().format("YYYY-MM-DD");
					const maxDate = moment(this.filterMaxDate).format("YYYY-MM-DD");
					if (currentDate == maxDate) {
						const previousDate = moment(currentDate).subtract(1, 'days').format("YYYY-MM-DD");
						this.endDate = previousDate;
					} else {
						this.endDate = this.filterMaxDate;
					}
					// set/reset the end date fields
					this.minEndDate = this.startDate;
					this.maxEndDate = new Date().toISOString().substr(0, 10);
					this.endDateModal = false;
					this.formatedEndDate = this.formatDate(this.endDate);

					// reset the filter
					this.isResetFilter = true;

					if(isResetList) {
						var type = this.finalizationSubTab === 'attTab' ? 'attendance' : 'noAttendance';
						this.fnResetStartEndDates();
						// reset the limit values
						this.fnSetInitialValue();
						this.listAttendanceEmployees(type);
					}
				},
				// select the leave type from the LOP list
				selectAutoLOPType(LeaveTypeId) {
					this.loadingScreen = true;
					// check attendance finalization optional choice access rights
					if(this.attendanceFinalizationOptionalChoice === 1) {
						// check if the employee details have chosen
						if(this.selectedInNoAttendance.length > 0) {
							var autoLOPDetails = [] ;
							// form the chosen records to validate LOP
							for(var i in this.selectedInNoAttendance) {							
								var employeeId = this.selectedInNoAttendance[i].employee_id;
								var absentDate = this.selectedInNoAttendance[i].absent_date;
								var leaveDuration = this.selectedInNoAttendance[i].leave_duration;
								var leavePeriod = this.selectedInNoAttendance[i].leave_period;
								var hours = this.selectedInNoAttendance[i].hours;
								var contactDetails = this.selectedInNoAttendance[i].contact_details;

								autoLOPDetails.push({
										Employee_Id  		: employeeId,
										Absent_Date  		: absentDate,
										Leave_Duration  : leaveDuration,
										Leave_Period    : leavePeriod,
										Hours						: hours,
										Contact_Details : contactDetails
								});
							}

							this.autoLOPDetails = autoLOPDetails;
							this.LeaveTypeId  = LeaveTypeId;
							this.loadingScreen = false;
							// validate LOP
							this.applyOrValidateAutoLOP();
						}
						else {
							let employees = this.finalizationSubTab === "shortageTab" ? this.shortageEmployees : this.listOfNoAttendanceEmployees;
							this.snackbar_msg = employees.length == 0 ? "There are no record(s) exist to initiate LOP" : "Please select minimum one record"
							this.snackbarColor = "warning";
							this.snackbar = true;
							this.loadingScreen = false;
						}
					}
					else {
						this.snackbar_msg = "Sorry, Access Denied"
						this.snackbarColor = "warning";
						this.snackbar = true;
						this.loadingScreen = false;
					}
				},
				// apply LOP
				applyOrValidateAutoLOP() {
					this.loadingScreen = true;
					axios.post(base_url_final + `employees/attendance-finalization/validate-auto-lop/`, {
							Auto_LOP_Details: this.autoLOPDetails,
							LeaveType_Id    : this.LeaveTypeId,
							startDate       : this.startDate,
							endDate					: this.endDate,
							employeeType		: this.empTypeIDSelectedInFilter,
						  location			  : this.locationIdSelectedInFilter,
						  department			: this.deptIdSelectedInFilter

						}).then(response => {
							if (response.data) {
								if (response.data.success) {
										// show the validated result in popup
										this.validAutoLOPRecords = response.data.validatedResult.Valid_Auto_LOP_Records;
										this.validNoAttendanceRecordCount = response.data.validatedResult.Valid_Auto_LOP_Records.length;
										this.popupMessage1 = "record(s), are eligible for this loss of pay ";
										this.popupMessage2 = "apply loss of pay";
										this.popUpButtonText = this.approveAutoClosureBtnText;
										this.openCustomModal = true;
										this.loadingScreen = false;
								} else {
							 		this.snackbar_msg = response.data.msg;
							 		this.snackbarColor = "warning";
									this.snackbar = true;
									this.loadingScreen = false;
								}
							} else {
								this.snackbar_msg = "Something went wrong. Please try after some time."
								this.snackbarColor = "warning";
								this.snackbar = true;
								this.loadingScreen = false;
							}
						}).catch(Err => {
							this.snackbar_msg = "Something went wrong. Please try after some time."
							this.snackbarColor = "warning";
							this.snackbar = true;
							this.loadingScreen = false;
						})
				},
				// change the date to org date format
				fnFormatDate (dateValue) {
					if (dateValue != '' && dateValue != null){
						var jsDateFormat;

						dateValue = dateValue.split('-');
			
						switch(this.orgDateFormat)
						{    
							case 'mm/dd/yy' : jsDateFormat = dateValue[1] + '/' + dateValue[2] + '/' + dateValue[0]; break;
							case 'yy/mm/dd' : jsDateFormat = dateValue[0] + '/' + dateValue[1] + '/' + dateValue[2]; break;
							case 'yy/dd/mm' : jsDateFormat = dateValue[0] + '/' + dateValue[2] + '/' + dateValue[1]; break;
							case 'dd/mm/yy' : 
							default         :
							
							
							jsDateFormat = dateValue[2] + '/' + dateValue[1] + '/' + dateValue[0];
						}        
						return jsDateFormat;
        			}
					else
					{
						return '';
					}        

				},
				// redirect to leaves/attendance from notes hyperlink
				redirectToLeaveAttendance(redirectionUrl) {
					window.open( base_url_final + redirectionUrl , '_blank' );
				},
				// export xlsx file
				exportExcelFile() {
					let sheetFileName =
						this.finalizationSubTab === "attTab" ? "Incomplete Attendance" : this.finalizationSubTab === "shortageTab" ? "Attendance Shortage" : "Missed Attendance and Leave";
					let fileExportData =
						this.finalizationSubTab === "noAttTab"
						? this.listOfNoAttendanceEmployees
						: this.finalizationSubTab === "shortageTab"
						? this.shortageEmployees
						: this.listOfAttendEmployees;
					let fileHeader =
						this.finalizationSubTab === "noAttTab"
						? [
							{
								header: "Employee Id",
								key: "user_defined_empid",
							},
							{
								header: "Employee Name",
								key: "employee_name",
							},
							{
								header: "Designation",
								key: "designation_name",
							},
							{
								header: "Department",
								key: "department_name",
							},
							{
								header: "Location",
								key: "location_name",
							},
							{
								header: "Contact Number",
								key: "contact_details",
							},
							{
								header: "Date",
								key: "d_absent_date",
							},
							{
								header: "Duration",
								key: "leave_duration",
							},
							{
								header: "Leave period",
								key: "leave_period",
							},
						  ]
						: this.finalizationSubTab === "shortageTab"
						? [
							{
								header: "Employee Id",
								key: "user_defined_empid",
							},
							{
								header: "Employee Name",
								key: "employee_name",
							},
							{
								header: "Designation",
								key: "designation_name",
							},
							{
								header: "Department",
								key: "department_name",
							},
							{
								header: "Location",
								key: "location_name",
							},
							{
								header: "Contact Number",
								key: "contact_details",
							},
							{
								header: "Date",
								key: "d_absent_date",
							},
							{
								header: "Duration",
								key: "leave_duration",
							},
							{
								header: "Leave period",
								key: "leave_period",
							},
							{
								header: "Attendance Hours",
								key: "attendance_hours",
							},
							{
								header: "Shortage Hours",
								key: "deviation_hours",
							},
						  ]
						: [
							{
								header: "Employee Id",
								key: "user_defined_empid",
							},
							{
								header: "Employee Name",
								key: "employee_name",
							},
							{
								header: "Designation",
								key: "designation_name",
							},
							{
								header: "Department",
								key: "department_name",
							},
							{
								header: "Location",
								key: "location_name",
							},
							{
								header: "Date In",
								key: "punchin_date",
							},
							{
								header: "Time In",
								key: "punchin_time",
							},
							{
								header: "Date Out",
								key: "punchout_date",
							},
							{
								header: "Time Out",
								key: "punchout_time",
							},
							{
								header: "Total Hours",
								key: "total_hours",
							},
							{
								header: "Status",
								key: "approval_status",
							},
						  ];
					// Create workbook and worksheet
					let workbook = new ExcelJS.Workbook();
					let worksheet = workbook.addWorksheet(sheetFileName);
					worksheet.columns = fileHeader;
					// assign data in each row
					if (fileExportData && fileExportData.length > 0) {
						worksheet.addRows(fileExportData);
					}
					// header styles (background and border)
					const row = worksheet.getRow(1);
					worksheet.getRow(1).eachCell((cell) => {
						cell.fill = {
							type: "pattern",
							pattern: "solid",
							fgColor: { argb: "92CDDC" },
						};
						cell.alignment = { vertical: "middle", horizontal: "center" };
						cell.font = {
							name: "Calibri",
							size: 11,
							bold: true,
						};
						cell.border = {
							top: { style: "thin" },
							left: { style: "thin" },
							bottom: { style: "thin" },
							right: { style: "thin" },
						};
					});

					// freeze column and row
					worksheet.views = [
						{
							state: "frozen",
							xSplit: 2,
							ySplit: 1,
						},
					];

					// auto width of column based on value length
					worksheet.columns.forEach((column) => {
						const lengths = column.values.map((v) => v.toString().length);
						const maxLength = Math.max(
						...lengths.filter((v) => typeof v === "number")
						);
						column.width = maxLength + 4;
					});

					// generate excel file with given file name
					workbook.xlsx.writeBuffer().then((data) => {
						this.convertToBlobAndDownload(data, sheetFileName);
					});
				},

				// convert the passed data as blob and download the files
				convertToBlobAndDownload(data, filename) {
					const url = window.URL.createObjectURL(
						new Blob([data], {
						type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
						})
					);
					const link = document.createElement("a");
					link.href = url;
					link.setAttribute("download", filename);
					document.body.appendChild(link);
					link.click();
					link.remove();
				}
			},
		})
	</script>
</body>

</html>
