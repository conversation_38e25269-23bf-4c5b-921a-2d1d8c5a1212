<?php
//=========================================================================================
//=========================================================================================
/* Program : WarningsController.php												         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description :  Warnings are issued against an employee by manager for his/her         *
 * misbehavior in the organization. 													 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        17-Aug-2013    Narmadha		          Initial Version        	         *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Devirani                Changed in file for mobile app     *
 *                                                                                       *
 *  1.5        03-Feb-2016    Prasanth               Changed in file for Bootstrap       *
 *							  				               		                         */
//=========================================================================================
//=========================================================================================

include APPLICATION_PATH."/validations/Validations.php";
class Employees_WarningsController extends Zend_Controller_Action
{
    protected $_dbWarnings          = null;
    protected $_dbPersonal          = null;
	protected $_dbCommonFun         = null;
    protected $_dbAccessRights      = null;
    protected $_warningAccessRights = null;
	protected $_warningEmployee     = null;
    protected $_logEmpId            = null;
    protected $_validation          = null;
	protected $_ehrTables           = null;
	protected $_formName            = 'Warnings';
	protected $_hrappMobile         = null;
	
    public function init()
    {
		$this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
		if ($this->_hrappMobile->checkAuth())
        {
			$this->_dbCommonFun    = new Application_Model_DbTable_CommonFunction();
			$this->_dbWarnings     = new Employees_Model_DbTable_Warnings();
			$this->_dbPersonal     = new Employees_Model_DbTable_Personal();
            $this->_dbAccessRights = new Default_Model_DbTable_AccessRights();
            $this->_validation 	   = new Validations();
			$this->_ehrTables 	   = new Application_Model_DbTable_Ehr();
			
			$userSession                = $this->_dbCommonFun->getUserDetails ();
            $this->_logEmpId            = $userSession['logUserId'];
			$this->_warningAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formName);
			$this->_warningEmployee     = $this->_warningAccessRights['Employee'];
            //$this->_dbAccessRights->refreshUserSessionTimestamp($this->_logEmpId);
        }
        else
        {
            if (Zend_Session::namespaceIsset('lastRequest'))
                Zend_Session:: namespaceUnset('lastRequest');
            
            $sessionUrl = new Zend_Session_Namespace('lastRequest');
            $sessionUrl->lastRequestUri = 'employees/warnings';
            $this->_redirect('auth');
        }
    }
	
    public function indexAction()
    {
		$checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

		if ($checkSessionAuth)
        {
			$this->_helper->layout()->disableLayout()->setLayout('admin_layout');
		
			$this->view->formNameA = $this->_formName;
			$this->view->customFormNameA = $this->_ehrTables->getCustomForms($this->_formName);
			
			$this->view->employeeDetails = $this->_dbCommonFun->listEmployeesDetails ('Warnings', '', $this->_logEmpId);
			
			$this->view->warningUser = array('Is_Manager' => $this->_warningEmployee['Is_Manager'],
											 'View'       => $this->_warningEmployee['View'],
											 'Update'     => $this->_warningEmployee['Update'],
											 'Delete'     => $this->_warningEmployee['Delete'],
											 'Add'        => $this->_warningEmployee['Add'],
											 'Admin'      => $this->_warningAccessRights['Admin'],
											 'Employee_Name' => $this->_dbPersonal->employeeId($this->_logEmpId));
			
			$ehrTables         = new Application_Model_DbTable_Ehr();
			$this->view->dateformat = $ehrTables->orgDateformat();
		} else {
			$this->_redirect('auth');
		}
    }

	/**
	 * Get warning details to show in grid
	 */
    public function listWarningsAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('list-warnings', 'json')->initContext();
			
            if ($this->_warningEmployee['View'] == 1)
            {
                $sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
				
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$employeeName = $this->_getParam('Employee_Name', null);
				$employeeName = filter_var($employeeName, FILTER_SANITIZE_STRIPPED);
				
				$warningBeginDate = $this->_getParam('WarningBeginDate', null);
				$warningBeginDate = filter_var($warningBeginDate, FILTER_SANITIZE_STRIPPED);
				
				$warningEndDate = $this->_getParam('WarningEndDate', null);
				$warningEndDate = filter_var($warningEndDate, FILTER_SANITIZE_STRIPPED);
				
                $warningUserArr = array('Is_Manager' => $this->_warningEmployee['Is_Manager'],
										'Admin'      => $this->_warningAccessRights['Admin'],
										'LogId'      => $this->_logEmpId);
				
				$searchArr = array('EmployeeName'     => $employeeName,
								   'WarningBeginDate' => $warningBeginDate,
								   'WarningEndDate'   => $warningEndDate);
				
				$this->view->result = $this->_dbWarnings->listWarnings ($page, $rows, $sortField, $sortOrder, $searchAll, $searchArr,
																		$warningUserArr);
            }
        }
        else
        {
            $this->_helper->redirector ('index', 'warnings', 'employees');
        }
    }
	
	/**
	 *	add or update warning details
	*/
	public function updateWarningsAction()
	{
		$this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('update-warnings', 'json')->initContext();
			
			$warningId = $this->_getParam('warningId', null);
			$warningId = filter_var($warningId, FILTER_SANITIZE_NUMBER_INT);
			
			if (($this->_warningEmployee['Is_Manager'] == 1 || !empty($this->_warningAccessRights['Admin'])) && 
				($this->_warningEmployee['Add'] == 1 || ($this->_warningEmployee['Update'] == 1 && !empty($warningId))))
			{
				if ( $this->getRequest()->isPost() )
				{
					$formData = $this->getRequest()->getPost();
					
					$employeeId  = $this->_validation->intValidation($formData['employeeId']);
					$warnignDate = $this->_validation->dateValidation($formData['warningDate']);
					
					$description['value'] = $this->_validation->commonFilters($formData['description']);
					$description['valid'] = $this->_validation->lengthValidation($description, 5, 500, true);
					
					if ($employeeId['valid'] && !empty($employeeId['value']) && $warnignDate['valid'] && !empty($warnignDate['value']) &&
						$description['valid'] && !empty($description['value']))
					{
						$warningEmpId = $employeeId['value'];
						$warningDate  = $warnignDate['value'];
						$warningDesc  = $description['value'];
						$ckAddedBy    = $this->_dbPersonal->ckAddedById($warningEmpId);
						$ehrTables    = new Application_Model_DbTable_Ehr();
						$dateOfJoin   = date('Y-m-d',strtotime($ehrTables->changeDateformat($this->_dbPersonal->getDateOfJoin($warningEmpId))));
						
						if ($warningEmpId != $this->_logEmpId && ($ckAddedBy == $this->_logEmpId || !empty($this->_warningAccessRights['Admin'])) &&
							(($warningId == 0 && strtotime($warningDate) >= strtotime(date('Y-m-d'))) ||
							 ($warningId > 0 && strtotime($warningDate) >= strtotime($dateOfJoin))))
						{
							$warningData = array('WarningTo_Id' => $warningEmpId,
												 'Warning_Date' => $warningDate,
												 'Description'  => htmlentities($warningDesc));
							
                            $customFormName = $this->_ehrTables->getCustomForms($this->_formName);
                            $customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formName);
                            
							$this->view->result = $this->_dbWarnings->updateWarning ($warningData, $warningId, $this->_logEmpId,$customFormNamee);
						}
						else
						{
							$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
						}
					}
					else
					{
						$this->view->result = array('success'=> false, 'msg' => 'Invalid data', 'type' => 'info');
					}
				}
			}
			else
			{
				$this->view->result = array('success'=> false, 'msg' => 'Invalid data', 'type' => 'warning');
			}
		}
		else
		{
			$this->_helper->redirector ('index', 'warnings', 'employees');
		}
	}
	
	/**
	 * Delete warnings
	 */
    public function deleteWarningAction()
    {
        $this->_helper->layout->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('delete-warning', 'json')->initContext();
			
			if ($this->_warningEmployee['Delete'] == 1 &&
				($this->_warningEmployee['Is_Manager'] == 1 || !empty($this->_warningAccessRights['Admin'])))
            {
				$warningId = $this->_getParam('warningId', null);
				$warningId = filter_var($warningId, FILTER_SANITIZE_NUMBER_INT);
				
				if (!empty($warningId) && $warningId > 0)
				{
                    $customFormName = $this->_ehrTables->getCustomForms($this->_formName);
					$customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formName);
                    
					$this->view->result = $this->_dbWarnings->deleteWarning ($warningId, $this->_logEmpId,$customFormNamee);
				}
                else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
				}
            }
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Access denied', 'type' => 'warning');
			}
        }
        else
        {
            $this->_helper->redirector ('index', 'warnings', 'employees');
        }
    }

	public function __destruct()
    {
        
    }
	
	
}