<?php
//=========================================================================================
//=========================================================================================
/* Program : ResignationController.php											         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : Employee resignation is when an employee gives notice to an employer    *
 * that he/she is going to leave their job. After resignation date employee status will  *
 * be “InActive”. HRAPP gives the flexibility to the employees to submit their           *
 * resignations online, rather than going through a long conventional methods.			 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Prasanth                Initial Version        	         *
 *  0.2        06-Jul-2014    Mahesh                  Modified mail coding and used      *                                                                              	
 *                                                    the common mail function           *                                                                                    	
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Dhanabal                Changed in file for mobile app     *
 *                                                                                       *
 *  1.5        20-Feb-2016    Suresh               Changed in file for Bootstrap         *
 *							  				                 		                     */
//=========================================================================================
//=========================================================================================

class Employees_ResignationController extends Zend_Controller_Action
{
    protected $_dbAccessRights = null;

    protected $_logEmpId = null;

    protected $_dbCommonFunction = null;

    protected $_hrappMobile = null;

	
	public function init()
    {
		$this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
		if ($this->_hrappMobile->checkAuth())
        {
			$this->_dbAccessRights              = new Default_Model_DbTable_AccessRights();

            $this->_dbCommonFunction            = new Application_Model_DbTable_CommonFunction();
            /**get the current user session */
            $userSession                        = $this->_dbCommonFunction->getUserDetails (); 
            /**get the employee id who is currently logged in */          
            $this->_logEmpId                    = $userSession['logUserId'];
			
			/**refresh the time stamp */
            //$this->_dbAccessRights->refreshUserSessionTimestamp($this->_logEmpId);        
        }
        else
       	{
       	    if (Zend_Session::namespaceIsset('lastRequest'))
       	        Zend_Session:: namespaceUnset('lastRequest');
       	    
       	    $sessionUrl = new Zend_Session_Namespace('lastRequest');
       	    $sessionUrl->lastRequestUri = 'employees/resignation';
       	    $this->_redirect('auth');
		}
    }

    /**
     * to show resignation based on employee access rights
     * with an option to view employee's resignation information, view employee's
     * resignation history
     * if exists , add and update
     */
    public function indexAction()
    {
        $checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

        if ($checkSessionAuth)
        {
            // index action triggered initially
            $this->_helper->layout()->disableLayout()->setLayout('admin_layout');
		    $this->getHelper('viewRenderer')->setViewSuffix('html');		
        } else {
			$this->_redirect('auth');
		}
    }

    public function __destruct()
    {
        
    }

}

