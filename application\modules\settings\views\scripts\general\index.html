<!DOCTYPE html>
<html>
	<head>
		<!-- common scripts -->
		<script src="https://cdn.jsdelivr.net/npm/vue@2/dist/vue.min.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/vuetify@2.2.3/dist/vuetify.min.js"></script>
		<script src="https://cdn.jsdelivr.net/gh/f/graphql.js@master/graphql.min.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/vue-cookies@1.6.1/vue-cookies.min.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/v-tooltip@2.0.2"></script>
		<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
		<!-- specific scripts -->
		<script src="https://cdn.jsdelivr.net/npm/vue-upload-component@2.8.23/dist/vue-upload-component.min.js"></script>
		<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vue-upload-component@2.8.23/dist/vue-upload-component.part.min.css">
		<script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.6/cropper.min.js" integrity="sha256-EuV9YMxdV2Es4m9Q11L6t42ajVDj1x+6NZH4U1F+Jvw=" crossorigin="anonymous"></script>
		<!-- styles -->
		<link href="https://cdn.jsdelivr.net/npm/vuetify@2.2.11/dist/vuetify.min.css" rel="stylesheet" />
		<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.6/cropper.min.css" integrity="sha256-cZDeXQ7c9XipzTtDgc7DML5txS3AkSj0sjGvWcdhfns=" crossorigin="anonymous" />
	</head>
	<body>
		<!--  html content -->
		<div id="general" class="col-md-12 portlets p-10">
			<template>
				<v-app class="bg_grey_lighten1">
					<!-- desktop topbar design -->
					<v-card class="bg_grey_lighten1 hidden-sm-and-down org-top-bar" flat height="4em">
						<v-toolbar color="grey lighten-5">
							<span class="padding-2em"></span>
							<v-tabs v-model="currentTab" background-color="#f9f9f9">
								<v-tab href="#tab-1" class="active_tab_bg" style="text-transform: capitalize !important;" class="font-weight-bold">
									General
								</v-tab>
								<v-tab v-if="!isCustomFieldAccessDenied" href="#tab-2" class="active_tab_bg" style="text-transform: capitalize !important;" class="font-weight-bold"
								@click="redirectToCustomFieldForm()">
									Custom Fields
								</v-tab>
								<v-tab v-if="!isEmailTemplateAccessDenied" href="#tab-3" class="active_tab_bg" style="text-transform: capitalize !important;" class="font-weight-bold"
								@click="redirectToEmailTemplateForm()">
									Email Templates
								</v-tab>
							</v-tabs>
						</v-toolbar>
					</v-card>
					<!-- mobile topbar design -->
					<v-card class="bg_grey_lighten1 hidden-md-and-up org-top-bar" flat height="4em">
						<v-toolbar color="grey lighten-5">
							<v-tabs v-model="currentTab" centered>
								<v-tab href="#tab-1" class="active_tab_bg" style="text-transform: capitalize !important;" class="font-weight-bold">
									General
								</v-tab>
								<v-tab v-if="!isCustomFieldAccessDenied" href="#tab-2" class="active_tab_bg" style="text-transform: capitalize !important;" class="font-weight-bold"
								@click="redirectToCustomFieldForm()">
									Custom Fields
								</v-tab>
								<v-tab v-if="!isEmailTemplateAccessDenied" href="#tab-3" class="active_tab_bg" style="text-transform: capitalize !important;" class="font-weight-bold"
								@click="redirectToEmailTemplateForm()">
									Email Templates
								</v-tab>
							</v-tabs>
						</v-toolbar>
					</v-card>
					
					<section v-if="!isAccessDenied && !orgFetchError ">
						
						<!-- show statistics page -->
						<view-org-details
							v-if="showViewPage"
							:ats-base-url ="atsBaseUrl"
							:org-code ="orgCode"
							:employee-id ="employeeId"
							:render-count = "renderCount"
							:roles-response = "rolesResponse"
							:base-path = "basePath()"
							:logo-url="reportLogoPresignedURL"
							:use-Report-Logo-As-Product-Logo ="useReportLogoAsProductLogo"
							:report-logo-path ="reportLogoPath"
							:api-headers="apiHeaders"
							@handle-success="handleSuccess($event)"
							@handle-error="handleError($event)"
							@handle-custom-error = "handleCustomError($event)"
							>
						</view-org-details>

					</section>
					<!--Access Denied-->
					<access-denied-screen v-if="isAccessDenied"></access-denied-screen> 
					<!--Fetching Error-->
					<div v-if="orgFetchError">
							<fetching-error-screen 
								button-text="Retry" 
								:content="errorContent" 
								:main-title="errorTitle" 
								image-name="initial-fetch-error-image" 
								@button-click="retryFetching"></fetching-error-screen>
					</div>
					<!--custom snack bars for showing success and error messages -->
					<custom-snack-bar v-if="snackbar"
						:show-snack-bar="snackbar" 
						:snack-bar-msg="snackBarMsg" 
						show-emoji="false" 
						emoji=""
						:snack-bar-type="snackBarType"
						:snack-bar-position-top = "true"
						@close-snack-bar="snackbar = false">
					</custom-snack-bar>		
					<!-- custom loading -->
					<custom-loading-screen v-if="loadingScreen"></custom-loading-screen>
				</v-app>
			</template>
		</div>
		<!-- script content -->
		<script>
					Vue.component('file-upload', VueUploadComponent)
			let primaryColor, secondaryColor;
			if (!localStorage.getItem("brand_color")) {
				const { Primary_Color, Secondary_Color} = JSON.parse(localStorage.getItem("brand_color"));
				primaryColor = Primary_Color;
				secondaryColor = Secondary_Color;
			} else {
				primaryColor = '#260029';
				secondaryColor = '#ec407a';
			}
			// vue instance general settings
			var app = new Vue({
				el: '#general',
				vuetify: new Vuetify(
					{
						theme: {
							options: {
								customProperties: true,
							},
							themes: {
								light: {
									primary: primaryColor,
									secondary: secondaryColor,
									grey: '#9E9E9E',
								}
							}
						}
					}
				),
				data() {
					return {
						atsBaseUrl : 'https://api.'+localStorage.getItem('domain')+'/ats/graphql',
						partnerid: $cookies.get("partnerid"),
						dCode: $cookies.get("d_code"),
						bCode: $cookies.get("b_code"),
						employeeId: parseInt(localStorage.getItem('LoginEmpId'), 10),
						userIp : '',
						rolesResponse : {},
						loadingScreen: false,
						windowWidth: 0,
						currentTab:"tab-1",

						// initial check data
						orgFetchError : false,
						isAccessDenied : false,

						//snackbar props
						snackbar :false,
						snackBarType : '',
						snackBarMsg : '',

						// variables
						reportLogoPath: '',
						reportLogoPresignedURL: '',
						useReportLogoAsProductLogo: '',
						renderCount: 0,
						showViewPage: false,
						isCustomFieldAccessDenied:true,
						isEmailTemplateAccessDenied:true
					}
				},
				computed: {
					isLocalEnv() {
						let currentUrl = window.location.href;
						if (
							parseInt(localStorage.getItem("isProduction"), 10) === 0 ||
							currentUrl.includes("hrapponline")
						) {
							return true;
						} else {
							return false;
						}
					},
					//to get orgCode dynamically from the current url
					orgCode() {
						if (this.isLocalEnv) {
							return "happy"; // local db connection
						} else {
							let oCode1 = localStorage.getItem("orgCode");
							if (oCode1) {
								return oCode1;
							} else {
								let url = window.location.href;
								let urlNoProtocol = url.replace(/^https?:\/\//i, "");
								let oCode = urlNoProtocol.split(".");
								oCode = oCode[0];
								return oCode;
							}
						}
					},
					apiHeaders() {
						let authorizationHeader = $cookies.get("accessToken") ? $cookies.get("accessToken") : null;
						let refreshTokenHeader = $cookies.get("refreshToken") ? $cookies.get("refreshToken") : null;
						return {
							'Content-Type': 'application/json',
							org_code: this.orgCode,
							Authorization: authorizationHeader,
							refresh_token: refreshTokenHeader,
							user_ip: this.userIp,
							partnerid: this.partnerid ? this.partnerid : "-",
							additional_headers: JSON.stringify(
								{
									org_code: this.orgCode,
									Authorization: authorizationHeader,
									refresh_token: refreshTokenHeader,
									user_ip: this.ipAddress,
									partnerid: this.partnerid ? this.partnerid : "-",
									d_code: this.dCode,
									b_code: this.bCode,
								}
							)
						}						
					},
				},
				created () {
					// function to get ip
					try{
						axios.get('https://api.ipify.org?format=json').then(response => { 
							this.userIp = response.data.ip;
						}).catch(error => {
							/* If the IP address API is not available, API URL is wrong or internet connection is not available,
							then the error.readyState will be 0 or 4  */
							if((error.readyState === 0 || error.readyState === 4) && ipAddressRestriction == 1) {
								this.snackBarMsg = "Unable to get the IP address. Please contact system Administrator.";
								this.snackBarType = "warning";
								this.snackbar = true;
							} else {
								this.userIp = "IP Blocked by user";
							}
						})
					}catch{
						this.userIp = "";
					}
					window.$cookies.set("userIpAddress", this.ipAddress);
				},
				// common error handling function
				errorCaptured(err, vm, info) {
					this.orgFetchError = true;
					this.errorTitle = "Oops";
					this.errorContent = "This issue seems to be from our side. Would you mind trying it after some time or talk to your administrator?"									
					return false;
				},
				mounted() {
					this.$nextTick(function () {
						window.addEventListener('resize', this.getWindowWidth);
						//Init
						this.getWindowWidth();
					});

					this.initializeAccessRights();
				},
				beforeDestroy() {
					window.removeEventListener('resize', this.getWindowWidth);
				},
				methods: {
					// base url
					base_url() {
						var pathParts = location.pathname.split('/');
						if (localStorage.getItem('production') == 0) {
							var url = location.origin + '/' + pathParts[1].trim('/') + '/'; // http://localhost/hrapponline/
						} else {
							var url = location.origin + '/'; // http://subdomain.hrapp.co
						}
						return url;
					},

					getWindowWidth(event) {
						this.windowWidth = document.documentElement.clientWidth;
					},

					atsGraphQl(){
						return graphql(this.atsBaseUrl, {
							method: 'POST',
							headers: this.apiHeaders,
							asJSON: true
						});
					},
					
					basePath() {
						if (localStorage.getItem('production') == 0) {
							return '/hrapponline/'
						} else {
							return '/'
						}
					},

					baseUrl(){
						var pathParts = location.pathname.split('/');
						if (localStorage.getItem('production') == 0) {
							var url = location.origin + '/' + pathParts[1].trim('/') + '/'; // http://localhost/hrapponline/
						} else {
							var url = location.origin + '/'; // http://subdomain.hrapp.co
						}
						return url;
					},
					//Method to handle initialization
					initializeAccessRights() {
						// Set loading screen
						this.loadingScreen = true;
						
						// Create a promise to track all access rights checks
						Promise.all([
							new Promise(resolve => {
								this.fnCheckAccessRights()
								.then(() => {
									resolve();
								})
								.catch(error => {
									resolve(); // Still resolve to not block the other checks
								});
							}),
							new Promise(resolve => {
								this.fnCustomFieldsCheckAccessRights()
								.then(() => {
									resolve();
								})
								.catch(error => {
									resolve(); // Still resolve to not block the other checks
								});
							}),
							new Promise(resolve => {
								this.fnEmailTemplatesCheckAccessRights()
								.then(() => {
									resolve();
								})
								.catch(error => {
									resolve(); // Still resolve to not block the other checks
								});
							})
						]).then(() => {
							// All access rights checks completed
							this.loadingScreen = false;
						}).catch(error => {
							// Handle any errors
							this.loadingScreen = false;
						});
					},
					
					fnCheckAccessRights() {
						try {
							var self = this;
							var atsGraphQl = self.atsGraphQl();
							
							var accessRights = atsGraphQl(`mutation(
								$formName: String,
								$employeeId: Int!) {
									getAccessRights(
										formName: $formName,
										employeeId:$employeeId
									) {
										errorCode message rights {
											Role_View Role_Update
										}
									}
								}
							`); 
							
							return accessRights({
								formName: 'General',
								employeeId: self.employeeId
							})
							.then(function (accessRightsResponse) {
								if (accessRightsResponse && accessRightsResponse.getAccessRights) {
									var response = accessRightsResponse.getAccessRights.rights;
									self.rolesResponse = response;
									if(response.Role_View) {
										self.isAccessDenied = false;
										self.taxConfigFetchError = false;    
										self.fngetOrgDetails();
									} else {
										self.isAccessDenied = true;
									}
								} else {
									self.snackbar = true;
									self.snackBarType = "warning";
									self.snackBarMsg = "Unable to verify access rights. Please try again.";
								}
							})
							.catch(function (error) {
								self.fetchErrorAccessRights = true;
								self.handleError(error, 1);
								throw error; // Re-throw to be caught by the caller
							});
						} catch(error) {
							self.fetchErrorAccessRights = true;
							self.handleError(error, 1);
							return Promise.reject(error); // Return a rejected promise
						}
					},
					
					fnCustomFieldsCheckAccessRights() {
						try {
							var self = this;							
							var atsGraphQl = self.atsGraphQl();
							var accessRights = atsGraphQl(`mutation(
								$formName: String,
								$employeeId: Int!) {
									getAccessRights(
										formName: $formName,
										employeeId:$employeeId
									) {
										errorCode message rights {
											Role_View Role_Update Employee_Role
										}
									}
								}
							`); 
							
							return accessRights({
								formName: 'Custom Fields',
								employeeId: self.employeeId
							})
							.then(function (accessRightsResponse) {
								if (accessRightsResponse && accessRightsResponse.getAccessRights) {
									var response = accessRightsResponse.getAccessRights.rights;
									if(response.Employee_Role && response.Role_View == 1 && response.Employee_Role.toLowerCase() === 'admin') {
										self.isCustomFieldAccessDenied = false;
									} else {
										self.isCustomFieldAccessDenied = true;
									}
								} else {
									self.isCustomFieldAccessDenied = true;
								}
							})
							.catch(function (error) {
								self.isCustomFieldAccessDenied = true;
								throw error; // Re-throw to be caught by the caller
							});
						} catch(error) {
							self.isCustomFieldAccessDenied = true;
							return Promise.reject(error); // Return a rejected promise
						}
					},
					
					//redirect to Email Template form
					fnEmailTemplatesCheckAccessRights() {
						try {
							var self = this;
							var atsGraphQl = self.atsGraphQl();
							var accessRights = atsGraphQl(`mutation(
								$formName: String,
								$employeeId: Int!) {
									getAccessRights(
										formName: $formName,
										employeeId:$employeeId
									) {
										errorCode message rights {
											Role_View Role_Update Employee_Role
										}
									}
								}
							`); 
							
							return accessRights({
								formName: 'Email Templates',
								employeeId: self.employeeId
							})
							.then(function (accessRightsResponse) {
								if (accessRightsResponse && accessRightsResponse.getAccessRights) {
									var response = accessRightsResponse.getAccessRights.rights;
									if(response.Employee_Role && response.Role_View == 1 && response.Employee_Role.toLowerCase() === 'admin') {
										self.isEmailTemplateAccessDenied = false;
									} else {
										self.isEmailTemplateAccessDenied = true;
									}
								} else {
									self.isEmailTemplateAccessDenied = true;
								}
							})
							.catch(function (error) {
								self.isEmailTemplateAccessDenied = true;
								throw error; // Re-throw to be caught by the caller
							});
						} catch(error) {
							self.isEmailTemplateAccessDenied = true;
							return Promise.reject(error); // Return a rejected promise
						}
					},
					
					//redirect to custom field form
					redirectToCustomFieldForm() {
						let base_url = this.baseUrl();
						window.location.href = base_url + "v3/settings/general/custom-fields";
					},
					
					//redirect to email templates form
					redirectToEmailTemplateForm() {
						let base_url = this.baseUrl();
						window.location.href = base_url + "v3/settings/general/email-templates";
					},

					handleError(err,isListPage) {
						var self = this;
						// handle BE error codes
						if (err && err[0]) {
							// error returned from backend
							var error = JSON.parse(err[0].message);
							var errorCode = error.errorCode;
							var errorMessage = error.message;

							switch (errorCode) {
								// technical errors
								case 705:
                    			case 706:
								case 751:
									if(isListPage){
										self.orgFetchError = true;
										self.errorTitle = "Oops";
										self.errorContent = "It's us ! There seems to be some technical difficulties. Please try after some time."									
									}
									else{
										self.snackbar = true;
										self.snackBarType = "warning";
										self.snackBarMsg = "Oops ! Sorry, This issue seems to be from our side. Would you mind trying it after some time or talk to your administrator ?"
									}
									break;
								// access denied
								case 752 :
									self.isAccessDenied = true;
									break;
								// set lock errors
								case 712:
								case 714:
								case 715:
								case 716:
									self.snackbar = true;
									self.snackBarType = "warning";
									self.snackBarMsg = errorMessage;
								break;
								default :
									if(isListPage) {
										self.orgFetchError = true;
										self.errorTitle = "Oops";
										self.errorContent = "Something went wrong. Please try after some time."									
									}
									else {
										self.snackbar = true;
										self.snackBarType = "warning";
										self.snackBarMsg =  "Something went wrong. Please contact system administrator."
									}
								break;
							}
						}
						else {
							// if error in list page, show the fetch error screen
							if(isListPage) {
								self.orgFetchError = true;
								self.errorTitle = 'Oops',
								self.errorContent = 'Something went wrong. Please try after some time.'
							}
							else {
								self.snackbar = true;
								self.snackBarType = "warning";
								self.snackBarMsg = "Something went wrong. Please contact system administrator."

							}
						}
					},
					// function used to get organization details
					fngetOrgDetails(){
						let self = this;
						self.renderCount++;
						// list the types of LOP
						axios.post(self.baseUrl() + `settings/general/get-organization-details`, {

						}).then(getOrgDetailsResponse => {
							if(getOrgDetailsResponse.data){
								self.reportLogoPath = getOrgDetailsResponse.data.Report_LogoPath;
								self.reportLogoPresignedURL = getOrgDetailsResponse.data.Report_Logo_PresignedUrl;
								self.useReportLogoAsProductLogo = getOrgDetailsResponse.data.Use_Report_Logo_As_Product_Logo;
								self.showViewPage = true;
							}else{
								self.snackbar = true;
								self.snackBarType = "warning";
								self.snackBarMsg = "Something went wrong. Please contact system admin.";
							}
						})
						.catch(function (Err) {
							self.snackbar = true;
							self.snackBarType = "warning";
							self.snackBarMsg = "There seems to be some technical issue. Please try after some time.";
						});
					},
					// retry fetching access rights/statistics data page from error page
					retryFetching() {
						this.fnCheckAccessRights();
					},

					// handle update success
					handleSuccess(msg) {
						this.snackbar = true;
						this.snackBarType = "success";
						this.snackBarMsg = msg;
						this.fngetOrgDetails();
					},


					// handle custom message in snack bar
					handleCustomError(msg) {
						this.snackbar = true;
						this.snackBarType = "warning";
						this.snackBarMsg = msg;
					}
				}
			})
		</script>
		</body>
	</html>
<!--  own styles -->
<style scoped lang="css">
	.org-top-bar {
		height: 5em;
		position: fixed !important;
		width: 100% !important;
		z-index: 2000;
		top : 50px;
	}
	.active_tab_bg {
		background:#f9f9f9 !important;
	}
	.v-tab{
		font-size: 1.1em;
	}
	.v-tabs-slider-wrapper{
		height: 3px !important;
	}
	.topbar-right-action{
		margin-right: 100px;
	}
	.v-application--is-ltr .v-chip .v-chip__close.v-icon.v-icon--right{
		margin-right: 0px !important;
	}			
	@media screen and (max-width:1024px){
		.topbar-right-action{
			margin-right: 0px !important;
		}
	}	
</style>
