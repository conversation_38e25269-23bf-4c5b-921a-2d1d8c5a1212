<?php
//=========================================================================================
//=========================================================================================
/* Program : SkillDefinition.php												         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MYSQL query for skill definition           							 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        16-Oct-2013    Mahesh                  Initial Version         	         *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Dhanabal.M              Changes in file for mobile app     *
 *                                                    1.Extra fields are added in        *
 *                                                    field list of list query.          */
//=========================================================================================
//=========================================================================================
class Employees_Model_DbTable_SkillDefinition extends Zend_Db_Table_Abstract
{

    protected $_db = null;

    protected $_dbPersonal = null;

    protected $_ehrTables = null;

    protected $_orgDF = null;
    
    protected $_commonFunction = null;
 
    public function init()
    {
        $this->_ehrTables = new Application_Model_DbTable_Ehr();
        $this->_db = Zend_Registry::get('subHrapp');
        $this->_dbPersonal = new Employees_Model_DbTable_Personal();
        $this->_orgDF = $this->_ehrTables->orgDateformat();
        $this->_commonFunction = new Application_Model_DbTable_CommonFunction();
    }
    
    /* Search Complaints */
      public function searchSkillDefinition($page,$rows,$sortField,$sortOrder,$searchAll=null,$searchDetails,$userDetails)
    {
        switch ($sortField)
        {
                default:
                case 0: $sortField = 'SD.Title'; break;
                case 1: $sortField = 'SD.Skill_Type'; break;    
        }
        $qrySkillDefinition = $this->_db->select()->from(array('SD'=>$this->_ehrTables->skillDefinition),
                                                    array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS SD.SkillDefinition_Id as count'),
                                                                  'SD.SkillDefinition_Id','SD.Title','SD.Skill_Type','SD.Description',
                                                                  new Zend_Db_Expr("DATE_FORMAT(SD.Added_On,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
								  new Zend_Db_Expr("DATE_FORMAT(SD.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On"),
                                                                  'DT_RowClass' => new Zend_Db_Expr('"SkillDefinition"'),
                                                                  'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', SD.SkillDefinition_Id)")))
                                                  
                                                    ->joinInner(array('EP'=>$this->_ehrTables->empPersonal),'SD.Added_By=EP.Employee_Id',
                                                                            array(new Zend_Db_Expr("CONCAT(EP.Emp_First_Name, ' ', EP.Emp_Last_Name) as Added_By")))
                                                    
                                                    ->joinLeft(array('EP1'=>$this->_ehrTables->empPersonal),'SD.Updated_By=EP1.Employee_Id',
                                                                            array(new Zend_Db_Expr("CONCAT(EP1.Emp_First_Name, ' ', EP1.Emp_Last_Name) as Updated_By")))
                                                     
                                                    ->order("$sortField $sortOrder")
                                                    ->limit($rows, $page);
 
        	if (!empty($searchAll) && $searchAll != null)
		{
			$qrySkillDefinition->where('SD.Title Like ?', "%$searchAll%")
                                    ->orwhere('SD.Skill_Type Like ?', "%$searchAll%");
		}
                
               $title = $searchDetails['Title'];
               $skillType = $searchDetails['Skill_Type'];
             
                if (!empty($title))
		{
			$qrySkillDefinition->where($this->_db->quoteInto('SD.Title Like ?',"%$title%"));
		}
                
                if (!empty($skillType))
		{
			$qrySkillDefinition->where($this->_db->quoteInto('SD.Skill_Type = ?',$skillType));
		}
        
        	$skillDefinitionDetails = $this->_db->fetchAll($qrySkillDefinition);
                
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
                
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->skillDefinition, new Zend_Db_Expr('COUNT(SkillDefinition_Id)')));
                
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $skillDefinitionDetails);
    }
    
    /* Update Complaints */
    public function updateSkillDefinition($skillDefinitionDetails,$sessionId,$customFormName)
    {
        $qrySkillDefinition = $this->_db->select()->from($this->_ehrTables->skillDefinition, new Zend_Db_Expr('count(SkillDefinition_Id)'))
                                        ->where("Title = ?",$skillDefinitionDetails['Title']);


         if (!empty($skillDefinitionDetails['SkillDefinition_Id']))
                $qrySkillDefinition->where('SkillDefinition_Id != ?', $skillDefinitionDetails['SkillDefinition_Id']);

        $skillDefinitionExists = $this->_db->fetchOne($qrySkillDefinition);
        
        if(empty($skillDefinitionExists))
        {
            if(!empty($skillDefinitionDetails['SkillDefinition_Id']))
            {
                $action = 'Edit';
                $skillDefinitionDetails['Updated_On'] = date('Y-m-d H:i:s');
                $skillDefinitionDetails['Updated_By'] = $sessionId;
                $skillDefinitionDetails['Lock_Flag']  = 0;
                $updated = $this->_db->update($this->_ehrTables->skillDefinition, $skillDefinitionDetails, array('SkillDefinition_Id = '.$skillDefinitionDetails['SkillDefinition_Id']));
                $skillDefinitionId = $skillDefinitionDetails['SkillDefinition_Id'];
                
            }
            else
            {
                $action = 'Add';
                $skillDefinitionDetails['Added_On'] = date('Y-m-d H:i:s');
                $skillDefinitionDetails['Added_By'] = $sessionId;
                $updated =  $this->_db->insert($this->_ehrTables->skillDefinition, $skillDefinitionDetails);
                $skillDefinitionId = $this->_db->lastInsertId();
            }
            return $this->_commonFunction->updateResult (array('updated'    => $updated,
                            'action'         => $action,
                            'trackingColumn' => $skillDefinitionId,
                            'formName'       => $customFormName,
                            'sessionId'      => $sessionId,
                            'comboPair'	 => $this->_commonFunction->getskillDefinition(),
                            'tableName'      => $this->_ehrTables->skillDefinition));
        }
        else
        {
            return array('success' => false, 'msg'=>$customFormName.' Already Exist', 'type'=>'info');
        } 
    }
    
    
    public function deleteSkillDefinition($skillDefinitionId, $logEmpId,$customFormName)
    {
        $ckSkillLevel = $this->_db->fetchOne($this->_db->select()
                                             ->from($this->_ehrTables->skillLevelAssoc, new Zend_Db_Expr('COUNT(SkillDefinition_Id)'))
                                            ->where('SkillDefinition_Id= ?', $skillDefinitionId));
        
        $ckPerformanceSkillLevel = $this->_db->fetchOne($this->_db->select()->from(array('pLevel'=>$this->_ehrTables->performanceLevel),
                                                                                   new Zend_Db_Expr('COUNT(Performance_Id)'))
                                                                    ->where('pLevel.SkillDefinition_Id = ?', $skillDefinitionId));
        
        if (!empty($skillDefinitionId) && $ckSkillLevel == 0 && $ckPerformanceSkillLevel == 0)
        {
      	    $skillDefinitionLock = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->skillDefinition, array('Lock_Flag', 'SkillDefinition_Id'))
                                         ->where('SkillDefinition_Id = ?', $skillDefinitionId));
			
		    if ($skillDefinitionLock['Lock_Flag'] == 0)
		    {
			    $deleted = $this->_db->delete($this->_ehrTables->skillDefinition, 'SkillDefinition_Id='.(int)$skillDefinitionId);
			    
                            return $this->_commonFunction->deleteRecord (array('deleted'        => $deleted,
								    'tableName'      => $this->_ehrTables->skillDefinition,
								    'lockFlag'       => $skillDefinitionLock['Lock_Flag'],
								    'formName'       => $customFormName,
								    'trackingColumn' => $skillDefinitionLock['SkillDefinition_Id'],
								    'sessionId'      => $logEmpId,
								    'comboPair'	     =>$this->_commonFunction->getskillDefinition()));
		    }
	}
        else
        {
            return array('success'=>false, 'msg'=>'Unable to delete '.$customFormName.'. Please, contact system admin', 'type'=>'info');
        }
    } 
    

//    /**
//     * Get skill definition details to load in combo
//     */
//    public function skillDefinitionpairs()
//    {
//        return $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->skillDefinition,
//        array('SkillDefinition_Id', 'Title'))->order('Title ASC'));
//    }
//    
    /**
     * Get technical details to show in combo
     */
    public function getTechnicalSkills ()
    {
        return $this->_db->fetchPairs($this->_db->select()
                                      ->from($this->_ehrTables->skillDefinition, array('SkillDefinition_Id', 'Title'))
                                      ->where('Skill_Type = ?','Core Competencies')
                                      ->order('Title ASC'));
    }
//
//    /**
//     * Check whether skill definition already exists or not
//     */
//    public function checkSkillDefinitionExist($formdata,$skillDefinitionId='')
//    {
//        $ckSkillDefinition = $this->_db->select()->from($this->_ehrTables->skillDefinition, new Zend_Db_Expr('count(Title)'))
//        ->where("Title Like ?",$formdata['Title']);
//
//        if(! empty($skillDefinitionId))
//        {
//            $ckSkillDefinition->where('SkillDefinition_Id !=?',$skillDefinitionId);
//        }
//
//        $rowCkSkillDefinition = $this->_db->fetchOne($ckSkillDefinition);
//        return $rowCkSkillDefinition;
//    }
//    
//    /**
//     * Add skill definition
//     */
//    public function addSkillDefinition($formdata, $sessionEmp)
//    {
//        $insertSkillDefinition=array(
//                'Title'=>$formdata['Title'],
//                'Skill_Type'=>$formdata['Skill_Type'],
//                'Description'=>htmlentities($formdata['Description']),
//                'Added_On'=>date('Y-m-d H:i:s'),
//                'Updated_By'=>$sessionEmp
//        );
//
//        $inserted=$this->_db->insert($this->_ehrTables->skillDefinition,$insertSkillDefinition);
//
//        $skillDefinitionId = $this->_db->lastInsertId();
//        if ($inserted) {
//            $this->_ehrTables->trackEmpSystemAction('Add Skill Definition - '.$formdata['Title'], $sessionEmp);
//            return $skillDefinitionId;
//
//        } else {
//            return false;
//        }
//    }
//    
//    /**
//     * Get skill definition details by id to view
//     */
//    public function viewSkillDefinition($skillDefinitionId)
//    {
//        $this->_db->setFetchMode(Zend_Db::FETCH_ASSOC);
//        $skillDefinitionQry=$this->_db->select()
//        ->from(array('skillDefinition'=>$this->_ehrTables->skillDefinition),array('skillDefinition.Title','skillDefinition.Updated_By',
//                'skillDefinition.Skill_Type','skillDefinition.Description',
//        new Zend_Db_Expr("date_format(skillDefinition.Added_On, '".$this->_orgDF['sql']." at %T') as Added_On"),
//        new Zend_Db_Expr("date_format(skillDefinition.Updated_On, '".$this->_orgDF['sql']." at %T') as Updated_On")))
//        ->joinLeft(array('emp3'=>$this->_ehrTables->empPersonal),'emp3.Employee_Id=skillDefinition.Updated_By',
//        array(new Zend_Db_Expr("CONCAT(emp3.Emp_First_Name, ' ', emp3.Emp_Last_Name) as UpdatedBy_Name")))
//        ->where('skillDefinition.SkillDefinition_Id =?', $skillDefinitionId);
//        $skillDefinitionRow=$this->_db->fetchRow($skillDefinitionQry);
//        return $skillDefinitionRow;
//
//    }
//
//    /**
//     * Delete skill definition
//     */
//    public function deleteSkillDefinition($skillDefinitionId, $sessionEmp)
//    {
//        $ckSkillLevel = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->skillLevelAssoc, new Zend_Db_Expr('COUNT(SkillDefinition_Id)'))
//        ->where('SkillDefinition_Id= ?', $skillDefinitionId));
//        $ckPerformanceSkillLevel = $this->_db->fetchOne($this->_db->select()->from(array('pLevel'=>$this->_ehrTables->performanceLevel), new Zend_Db_Expr('COUNT(Performance_Id)'))
//                        ->where('pLevel.SkillDefinition_Id = ?', $skillDefinitionId));
//        if (!empty($skillDefinitionId) && $ckSkillLevel == 0 && $ckPerformanceSkillLevel == 0)
//        {
//            $skillDefinitionLock = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->skillDefinition, array('Lock_Flag','Title'))
//            ->where('SkillDefinition_Id = ?', $skillDefinitionId));
//            if($skillDefinitionLock['Lock_Flag'] == 0)
//            {
//                $deleted=$this->_db->delete($this->_ehrTables->skillDefinition,'SkillDefinition_Id='.(int)$skillDefinitionId);
//                if ( $deleted ) {
//
//                    $this->_ehrTables->trackEmpSystemAction('Delete skill Definition - '.$skillDefinitionLock['Title'], $sessionEmp);
//                    return Zend_Json::encode(array('success'=>'true'));
//                } else {
//                    return Zend_Json::encode(array('failure'=>'Unable to delete skill definition.'));
//                }
//            }
//            else
//            {
//                if($skillDefinitionLock['Lock_Flag'] == $sessionEmp)
//                {
//                    $message = 'Unable to delete skill definition.<br/>You have opened this record to update.';
//                }
//                else
//                {
//                    $editEmpName = $this->_dbPersonal->employeeName($skillDefinitionLock['Lock_Flag']);
//                    $message = 'Unable to delete skill definition.<br/>'.$editEmpName['Employee_Name'] .' has opened this record to update.';
//                }
//                return Zend_Json::encode(array('failure'=>$message));
//            }
//        }
//        else
//        {
//            return Zend_Json::encode(array('failure'=>'Unable to delete skill definition.<br/>Please, contact system admin.'));
//        }
//    }
//    
//    /**
//     * Get skill definition details to show in a grid
//     */
//    public function searchSkillDefinition($title,$skillType,$page, $rows, $sortField, $sortOrder)
//    {
//        if($sortField == 'Title_Name')
//        {
//            $sortField = 'skillDefinition.Title';
//        }
//        
//        $this->_db->setFetchMode(Zend_Db::FETCH_ASSOC);
//        $searchSkillDefinition = $this->_db->select()
//        ->from(array('skillDefinition'=>$this->_ehrTables->skillDefinition),array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS skillDefinition.SkillDefinition_Id as count'),
//               'skillDefinition.SkillDefinition_Id','skillDefinition.Description','skillDefinition.Added_On','skillDefinition.Updated_On','skillDefinition.Title',
//			   'skillDefinition.Title as Title_Name','skillDefinition.Skill_Type'))
//        
//        ->joinInner(array('pa'=>$this->_ehrTables->empPersonal), 'pa.Employee_Id = skillDefinition.Updated_By',
//                    array('Updated_By_Name'=>new Zend_Db_Expr("CONCAT(pa.Emp_First_Name, ' ', pa.Emp_Last_Name)")))
//        
//        ->order("$sortField $sortOrder")
//        ->limit($rows,($page-1)*$rows);
//
//         
//        if ( ! empty($title) && preg_match('/^[0-9a-zA-Z ]+$/', $title) )
//        {
//            $searchSkillDefinition->where("skillDefinition.Title Like ?", "$title%");
//        }
//        if ( ! empty($skillType) && preg_match('/^[a-zA-Z ]/', $skillType) )
//        {
//            $searchSkillDefinition->where("skillDefinition.Skill_Type Like ?", $skillType);
//
//        }
//
//        $skillDefinitionResult=$this->_db->fetchAll($searchSkillDefinition);
//        $countSkillDefinition=$this->_db->fetchOne('select FOUND_ROWS()');
//        $searchedSkillDefinition=array("total"=>$countSkillDefinition,"rows"=>$skillDefinitionResult);
//
//        return Zend_Json::encode($searchedSkillDefinition);
//    }
//
//    /**
//     * Update skill definition
//     */
//    public function updateSkillDefinition($skillDefinitionId,$formdata,$sessionEmp)
//    {
//        $updateSkillDefinition = array(
//                'Title'  => $formdata['Title'],
//                'Skill_Type'  => $formdata['Skill_Type'],
//                'Description'  => htmlentities($formdata['Description']),
//                'Updated_On' => date('Y-m-d H:i:s'),
//                'Updated_By'=>$sessionEmp,
//                'Lock_Flag'=>0);
//        $updated = $this->_db->update($this->_ehrTables->skillDefinition,$updateSkillDefinition,array('SkillDefinition_Id=?'=>(int)$skillDefinitionId));
//        if ($updated) {
//            $this->_ehrTables->trackEmpSystemAction('Update skill definition - '.$formdata['Title'], $sessionEmp);
//            return $skillDefinitionId;
//
//        }
//        else {
//
//            return false;
//        }
//    }

    public function __destruct()
    {
        
    }	
}

