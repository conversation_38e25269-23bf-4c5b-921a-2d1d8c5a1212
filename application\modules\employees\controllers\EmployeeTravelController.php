<?php
//=========================================================================================
//=========================================================================================
/* Program : EmployeeTravelController.php										         * 
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : An Employee can apply for travel benefits, if the travel is             *
 * official travel. Employee travel request made with the details of EmpName, forward    *
 * to(manager), travel purpose, start date, end date, expected Budget, place of visit,   *
 * travel mode (car, bus, plane), description etc. Finally the travel request may be     *
 * approved/rejected by approver. 														 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Sandhosh		          Initial Version        	         *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Sandhosh                Changed in file for mobile app     *
 *							  Nivethitha						                         *
 *                                                                                       *
 *  1.5        22-Feb-2016    Suresh               Changed in file for Bootstrap         *
 *							                                                             */
//=========================================================================================
//=========================================================================================
include APPLICATION_PATH."/validations/Validations.php";
class Employees_EmployeeTravelController extends Zend_Controller_Action
{
    protected $_validation          = null;
    protected $_dbTravel	    = null;
    protected $_dbAccessRights      = null;
    protected $_employeeAccess      = null;
    protected $_logEmpId            = null;
    protected $_dbCommonFunction    = null;
	protected $_dbComment           = null;
    protected $_formName            = 'Employee Travel';
    protected $_ehrTables = null;
    protected $_dbPersonal = null;
	protected $_hrappMobile = null;
    
    public function init()
    {
		$this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
		if ($this->_hrappMobile->checkAuth())
        {
		    $this->_validation 	        = new Validations();
		    $this->_dbCommonFunction    = new Application_Model_DbTable_CommonFunction();
            $this->_dbTravel            = new Employees_Model_DbTable_Travels();
			$this->_dbComment = new Payroll_Model_DbTable_PayrollComment();
            $this->_dbAccessRights      = new Default_Model_DbTable_AccessRights();
		    $this->_dbPersonal          = new Employees_Model_DbTable_Personal();
            $userSession                = $this->_dbCommonFunction->getUserDetails ();
            $this->_logEmpId            = $userSession['logUserId'];
            $this->_employeeAccess      = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formName);
            //$this->_dbAccessRights->refreshUserSessionTimestamp($this->_logEmpId);
		    $this->_ehrTables = new Application_Model_DbTable_Ehr();
        }
        else
       	{
       	    if (Zend_Session::namespaceIsset('lastRequest'))
       	        Zend_Session:: namespaceUnset('lastRequest');
       	    
       	    $sessionUrl = new Zend_Session_Namespace('lastRequest');
       	    $sessionUrl->lastRequestUri = 'employees/employee-travel';
       	    $this->_redirect('auth');
        }
    }

    /**
     * to show resignation based on employee access rights
     * with an option to view employee's resignation information, view employee's
     * resignation history
     * if exists , add and update
     */
    public function indexAction()
    {
		$checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

		if ($checkSessionAuth)
        {
			$this->_helper->layout()->disableLayout()->setLayout('admin_layout');
				
			$this->view->formNameA = $this->_formName;
			$this->view->customFormNameA = $this->_ehrTables->getCustomForms($this->_formName);
		
			$this->view->employeeName = $this->_dbCommonFunction->listEmployeesDetails('Employee Travel', '', $this->_logEmpId);
			
			$formId     = $this->_dbComment->getFormId($this->_formName);
			
			$this->view->approverName = $this->_dbPersonal->payrollEmpDetail('', '', 'Emp_First_Name', 'ASC','', '', '', '','', $formId, '',1);
		
			$this->view->userAccessA =  array('Is_Manager'=>$this->_employeeAccess['Employee']['Is_Manager'],
							'View'=>$this->_employeeAccess['Employee']['View'],
							'Update'=>$this->_employeeAccess['Employee']['Update'],
							'Add'=>$this->_employeeAccess['Employee']['Add'],
							'Delete'=>$this->_employeeAccess['Employee']['Delete'],
							'Op_Choice'=>$this->_employeeAccess['Employee']['Optional_Choice'],
							'Admin'=>$this->_employeeAccess['Admin'],
							'Session_Id'    => $this->_logEmpId,
							'Employee_Name' => $this->_dbPersonal->employeeId($this->_logEmpId));
	
			$this->view->dateformat = $this->_ehrTables->orgDateformat();
        } else {
			$this->_redirect('auth');
		}
    }
	public function listEmployeeTravelAction()
	{
	    $this->_helper->layout->disableLayout();
	    if (isset($_SERVER['HTTP_REFERER']))
	    {
		if ($this->_employeeAccess['Employee']['View'] == 1)
		{
		    $ajaxContext = $this->_helper->getHelper('AjaxContext');
		    $ajaxContext->addActionContext('list-employee-travel', 'json')->initContext();
		    
		    $employeeName  		= $this->_getParam('Employee_Name', null);
		    $employeeName        	= filter_var($employeeName, FILTER_SANITIZE_STRIPPED);
		    
		    $startDate          	= $this->_getParam('Start_Date', null);
		    $startDate          	= filter_var($startDate, FILTER_SANITIZE_STRIPPED);
		    
		    $endDate            	= $this->_getParam('End_Date', null);
		    $endDate            	= filter_var($endDate, FILTER_SANITIZE_STRIPPED);
		    
		    $approvalStatus           = $this->_getParam('Approval_Status', null);
		    $approvalStatus           = filter_var($approvalStatus, FILTER_SANITIZE_STRIPPED);
		    
		    $searchArray  = array('Employee_Name'=>$employeeName,
					 'Start_Date'=>$startDate,
					 'End_Date'=>$endDate,
					 'Approval_Status'=>$approvalStatus);
		    
		    $userDetailsArray = array('Is_Manager' => $this->_employeeAccess['Employee']['Is_Manager'],
					      'Op_Choice'=>$this->_employeeAccess['Employee']['Optional_Choice'],
					      'Admin'      => $this->_employeeAccess['Admin'],
					      'Session_Id' => $this->_logEmpId,
					      'Form_Name'=>$this->_formName);
		    
		    
		    $sortField  = $this->_getParam('iSortCol_0', null);
		    $sortField  = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
		    
		    $sortOrder  = $this->_getParam('sSortDir_0', null);
		    $sortOrder  = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
		    
		    $page       = $this->_getParam('iDisplayStart', null);
		    $page       = isset($page) ? intval($page) : 0;
		    $page       = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
		    
		    $rows       = $this->_getParam('iDisplayLength', null);
		    $rows       = isset($rows) ? intval($rows) : 10;
		    $rows       = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
    
		    $searchAll  = $this->_getParam('sSearch', null);
		    $searchAll  = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
		    
		    $this->view->result = $this->_dbTravel->searchEmployeeTravel($page,$rows,$sortField,$sortOrder,$searchAll,$searchArray,$userDetailsArray);
		}
	    }
	    else
	    {
		$this->_helper->redirector('index', 'employee-travel', 'employees');
	    }
	    
	}
	public function updateEmployeeTravelAction()
	{
		$this->_helper->layout->disableLayout();
		if (isset($_SERVER['HTTP_REFERER']))
		{
		    $ajaxContext = $this->_helper->getHelper('AjaxContext');
		    $ajaxContext->addActionContext('update-employee-travel', 'json')->initContext();
				
		    $requestId = $this->_getParam('Request_Id');
		    $requestId = $this->_validation->intValidation($requestId);
		    
		    if ((!empty($requestId['value']) && $this->_employeeAccess['Employee']['Update'] == 1 ) ||
					(empty($requestId['value']) && $this->_employeeAccess['Employee']['Add'] == 1 ))
		    {
			if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
                
				$employeeId        = $this->_validation->intValidation($formData['Employee_Id']);
				$approverId        = $this->_validation->intValidation($formData['Approver_Id']);
				
				$approvalStatus    = $this->_validation->alphaValidation($formData['Approval_Status']);
				
				$mailUsr           = $this->_getParam('mailSend', null);
				
                $startDate     = $this->_validation->dateValidation($formData['Start_Date']);
                $endDate   	   = $this->_validation->dateValidation($formData['End_Date']);
                
                $customFormName = $this->_ehrTables->getCustomForms($this->_formName);
                $customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formName);
                 
                    if($mailUsr == 'true')
                    {
                        if($requestId['value'] != 0 && $approvalStatus['value']=='Pending Approval' || $approvalStatus['value'] == 'Returned')
                        {
                            $travelEmpName = $this->_dbPersonal->employeeName($employeeId['value']);
                            $logEmpName = $this->_dbPersonal->employeeName($this->_logEmpId);						
                            
                     
                                if($approvalStatus['value'] == 'Pending Approval')
                                {
                                    $comments['value'] = $this->_validation->commonFilters($formData['Comments']);
                                    $comments['valid'] = $this->_validation->lengthValidation($comments, 5, 600, false);
                                    
                                    $startDate     = $this->_validation->dateValidation($formData['Start_Date']);
                                    $endDate   	   = $this->_validation->dateValidation($formData['End_Date']);
                                    
                                    $budget    	   = $this->_validation->amountValidation($formData['Budget']);
                                    $purpose   	   = $this->_validation->alphaValidation($formData['Purpose']);
                                    
                                    $employeeTravel  = array('Request_Id'     	=> $requestId['value'],
                                                            'Employee_Id'    	=> $employeeId['value'],
                                                            'Purpose'      	    => $purpose['value'],
                                                            'Start_Date'   	    => date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($startDate['value']))),
                                                            'End_Date' 		    => date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($endDate['value']))),
                                                            'Budget'    	    => $budget['value'],
                                                            'Approval_Status'   => $approvalStatus['value']);
                            
                                    $this->view->result = $this->_dbTravel->updateEmployeeTravel($employeeTravel,$this->_logEmpId,$comments['value'],'',true,$customFormNamee);    
                                    
                                }
                                
                                
                                if( $approverId['value'] != $this->_logEmpId)
                                {
                                    
                                    if( $employeeId['value'] == $this->_logEmpId)
                                    {
                                        $msgDescA = "<p>".$customFormNamee." Notification forwarded from ". $travelEmpName['Employee_Name'] ." is waiting for your approval.</p>";
                                
                                    }
                                    else
                                    {
                                        $msgDescA = "<p>".$customFormNamee." Notification forwarded from " .$logEmpName['Employee_Name'] . " for the employee ". $travelEmpName['Employee_Name'] ." is waiting for your approval.</p>";
                                    }
                                
                                    $mailSend = $this->_dbCommonFunction->communicateMail (array(
                                                                                            'employeeId'     => $approverId['value'],
                                                                                            'ModuleName'     => 'Employees',
																							'formName'    => $this->_formName,
																							'successMsg'  => $customFormNamee,
																							'customFormName' => $customFormNamee,
																							'formUrl'        => '/employees/employee-travel',
																							'inboxTitle' => $customFormNamee.' Notification',
                                                                                            'mailContent'    => $msgDescA,
                                                                                            'action'         => 'edit'));
                                                    
                                    if($mailSend['success'] == 1)
                                    {
                                        $this->view->result = array('success'=> true, 'msg' => $mailSend['msg'], 'type' => 'info');
                                    }
                                    else
                                    {
                                        $this->view->result = array('success'=> true, 'msg' => $customFormNamee.' updated successfully,unable to send email..', 'type' => 'info');
                                    }							
                                }
                                else{
                                    $this->view->result = array('success'=> true, 'msg' => $customFormNamee.' updated successfully', 'type' => 'info');
                                }
                        }
                        else
                        {
                            $this->view->result = array('success'=> false, 'msg' => 'Invalid data', 'type' => 'warning');
                        }
                    }
                    else
                    {
                        $startDate         = $this->_validation->dateValidation($formData['Start_Date']);
                        $endDate   	   = $this->_validation->dateValidation($formData['End_Date']);
                        
                        $budget    	   = $this->_validation->amountValidation($formData['Budget']);
						$budget['valid'] = $this->_validation->minMaxValueValidation($budget['value'],1,'');
			
                        $purpose   	   = $this->_validation->alphaNumSpValidation($formData['Purpose']);						
                        $purpose['valid'] = $this->_validation->lengthValidation($purpose, 3,50,true);
			
                        $comments['value'] = $this->_validation->commonFilters($formData['Comments']);
                        $comments['valid'] = $this->_validation->lengthValidation($comments, 5, 600, false);
                    
                        $requestId       = $this->_validation->intValidation($formData['Request_Id']);
			
                        $place          = $this->_validation->alphaNumValidation($formData['Place']);
						$place['valid'] = $this->_validation->lengthValidation($purpose,3,75,true);
			
                        $travelMode     = $this->_validation->alphaValidation($formData['Travel_Mode']);
                        $accomodation   = $this->_validation->alphaValidation($formData['Arrange']);
                        
                        $itineraryId        = $this->_validation->intValidation($formData['Itinerary_Id']);
						
						//get employee resignation date
						$employeeResignationDate = $this->_dbCommonFunction->getEmployeeResignationDate($employeeId['value']);
                        
                if ($employeeId['valid'] && $approverId['valid'] && $budget['valid']  && $purpose['valid'] &&
					$approvalStatus['valid'] && $requestId['valid'] && $place['valid'] && $travelMode['valid'] &&
					$accomodation['valid'] && $startDate['valid']  && $endDate['valid'] &&
					!empty($employeeId['value']) && !empty($approverId['value']) &&!empty($purpose['value']) && !empty($budget['value'])
					&& !empty($startDate['value']) && !empty($endDate['value']) && !empty($place['value']) && !empty($travelMode['value']) &&
					!empty($accomodation['value']) && !empty($approvalStatus['value']) &&
					($startDate['value'] <= $employeeResignationDate || empty($employeeResignationDate)) &&
					($endDate['value'] <= $employeeResignationDate || empty($employeeResignationDate)) )                            
                        {
                            $auditTravel['Update'] = 0;
                            $auditTravel['Data'] = array();
                            
                            if(!empty($itineraryId['value']) && !empty($requestId['value']))
                            {
                                $travelDetails = $this->_dbTravel->viewEmpTravel($itineraryId['value']);
                                $audittravelDetails = $travelDetails[0];
                                
                                if ($audittravelDetails['Place']!= $place['value'] ||
                                $audittravelDetails['Travel_Mode']!= $travelMode['value'] ||
                                $audittravelDetails['Arrange']!= $accomodation['value'])
                                {
                                    $auditTravel['Update'] = 1;
                                    $auditTravel['Data'] = array('Previous_Place'=>$audittravelDetails['Place'],
                                                                 'Updated_Place'=>$place['value'],
                                                                 'Previous_Mode'=>$audittravelDetails['Travel_Mode'],
                                                                 'Updated_Mode'=>$travelMode['value'],
                                                                 'Previous_Arrange'=>$audittravelDetails['Arrange'],
                                                                 'Updated_Arrange'=>$accomodation['value'],
                                                                 'Request_Id'=> $requestId['value'],
                                                                 'Modified_By'=>$this->_logEmpId,
                                                                 'Modified_On'=>date('Y-m-d H:i:s'));
                            
                                }
                                
                                $approvalStatus['value'] = ( $audittravelDetails['Approval_Status'] == 'Returned' ) ? "Pending Approval" : $audittravelDetails['Approval_Status'];
                            }
                            else{
                                $approvalStatus['value'] = "Pending Approval";
                            }
                            $employeeTravel  = array('Itinerary_Id'  	=> $itineraryId['value'],
                                            'Request_Id'     	=> $requestId['value'],
                                            'Employee_Id'    	=> $employeeId['value'],
                                            'Purpose'      	=> $purpose['value'],
                                            'Start_Date'   	=> date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($startDate['value']))),
                                            'End_Date' 		=> date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($endDate['value']))),
                                            'Budget'    	=> $budget['value'],
                                            'Place'          	=> $place['value'],
                                            'Travel_Mode'    	=> $travelMode['value'],
                                            'Arrange'   	=> $accomodation['value'],
                                            'Approver_Id'       => $approverId['value'],
                                            'Submission_Date'   => date('Y-m-d H:i:s'),
                                            'Approval_Status'   => $approvalStatus['value']);
                                
                                
                            
                            $this->view->result = $this->_dbTravel->updateEmployeeTravel($employeeTravel,$this->_logEmpId,$comments['value'],$auditTravel,'',$customFormNamee);
                        }
                        else
                        {
                            $this->view->result = array('success' => false, 'msg'=>'Invalid data', 'type'=>'info');
                        }
                    }
			}
					
		    }
		    else
		    {
					$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'info');
		    }
		}
		else
		{
		    $this->_helper->redirector('index', 'employee-travel', 'employees');
		}
	}
    
	public function deleteEmployeeTravelAction()
	{
		$this->_helper->layout->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
		{
		    $ajaxContext = $this->_helper->getHelper('AjaxContext');
		    $ajaxContext->addActionContext('delete-employee-travel', 'json')->initContext();
		    
		    $requestId = $this->_getParam('Request_Id', null);
		    $requestId = filter_var($requestId, FILTER_SANITIZE_NUMBER_INT);
		    
		    if (!empty($requestId) && $requestId > 0 && $this->_employeeAccess['Employee']['Delete'] == 1)
		    {
                $customFormName = $this->_ehrTables->getCustomForms($this->_formName);
                $customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formName);
                                
                $this->view->result = $this->_dbTravel->deleteEmployeeTravel($requestId, $this->_logEmpId,$customFormNamee);
		    }
		    else
		    {
                $this->view->result = array('success'=>false, 'msg'=>'Invalid data','type'=>'info');
		    }
		}
		else
		{
		    $this->_helper->redirector('index', 'employee-travel', 'employees');
		}
		
	}
	public function listDestinationDetailsAction()
	{
		 $this->_helper->layout->disableLayout();
		if (isset($_SERVER['HTTP_REFERER']))
		{
		    if ($this->_employeeAccess['Employee']['View'] == 1)
		    {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('list-destination-details', 'json')->initContext();
			
			$requestId 	 = $this->_getParam('Request_Id', null);
			$requestId        = filter_var($requestId, FILTER_SANITIZE_NUMBER_INT);
			
			$this->view->result = $this->_dbTravel->searchDestinationDetails($requestId);
		    }
		}
		else
		{
		    $this->_helper->redirector('index', 'employee-travel', 'employees');
		}
		
	}
	public function updateDestinationDetailsAction()
	{
		$this->_helper->layout->disableLayout();
		if (isset($_SERVER['HTTP_REFERER']))
		{
		    $ajaxContext = $this->_helper->getHelper('AjaxContext');
		    $ajaxContext->addActionContext('update-destination-details', 'json')->initContext();
				
		    $lineItemId = $this->_getParam('LineItem_Id');
		    $lineItemId = $this->_validation->intValidation($lineItemId);
		    
		    if ((!empty($lineItemId['value']) && $this->_employeeAccess['Employee']['Update'] == 1 ) ||
					(empty($lineItemId['value']) && $this->_employeeAccess['Employee']['Add'] == 1 ))
		    {
			if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
	     
				$requestId       = $this->_validation->intValidation($formData['Request_Id']);
				$place          = $this->_validation->alphaValidation($formData['Place']);
				$travelMode     = $this->_validation->alphaValidation($formData['Travel_Mode']);
				$accomodation   = $this->_validation->alphaValidation($formData['Accomodation']);
				
				$travelDetails = $this->_dbTravel->viewEmpTravel($lineItemId['value']);
				$audittravelDetails = $travelDetails[0];
				
				if ($requestId['valid'] && $place['valid'] && $travelMode['valid'] && $accomodation['valid'])
				{
					$destinationDetails  = array('LineItem_Id'		=> $lineItemId['value'],
								     'Request_Id'       => $requestId['value'],
								     'Place'           => $place['value'],
								     'Travel_Mode'     => $travelMode['value'],
								     'Accomodation'    => $accomodation['value']);					
					
					$auditTravel['Update'] = 0;
					$auditTravel['Data'] = array();
					
					if ($audittravelDetails['Place']!= $place['value'] ||
						$audittravelDetails['Travel_Mode']!= $travelMode['value'] ||
						$audittravelDetails['Accomodation']!= $accomodation['value'])
					{
						$auditTravel['Update'] = 1;
						$auditTravel['Data'] = array('Previous_Place'=>$audittravelDetails['Place'],
											   'Updated_Place'=>$place['value'],
								               'Previous_Mode'=>$audittravelDetails['Travel_Mode'],
											   'Updated_Mode'=>$travelMode['value'],
								               'Previous_Arrange'=>$audittravelDetails['Accomodation'],
											   'Updated_Arrange'=>$accomodation['value'],
								               'Request_Id'=> $requestId['value'],
											   'Modified_By'=>$this->_logEmpId,
											   'Modified_On'=>date('Y-m-d H:i:s'));

					}
					
					$this->view->result = $this->_dbTravel->updateDestinationDetails($destinationDetails,$auditTravel,$this->_logEmpId);
				}
				else
				{
					$this->view->result = array('success' => false, 'msg'=>'Invalid data', 'type'=>'info');
				}
			}
					
		    }
		    else
		    {
					$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'info');
		    }
		}
		else
		{
		    $this->_helper->redirector('index', 'employee-travel', 'employees');
		}    
	}
    
	public function deleteDestinationDetailsAction()
	{
		$this->_helper->layout->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
		{
		    $ajaxContext = $this->_helper->getHelper('AjaxContext');
		    $ajaxContext->addActionContext('delete-destination-details', 'json')->initContext();
		    
		    $lineItemId = $this->_getParam('LineItem_Id', null);
		    $lineItemId = filter_var($lineItemId, FILTER_SANITIZE_NUMBER_INT);
		    
    	    if (!empty($lineItemId) && $lineItemId > 0 && $this->_employeeAccess['Employee']['Delete'] == 1)
		    {
                $this->view->result = $this->_dbTravel->deleteDestinationDetails($lineItemId, $this->_logEmpId);
		    }
		    else
		    {
                $this->view->result = array('success'=>false, 'msg'=>'Invalid data','type'=>'info');
		    }
		}
		else
		{
		    $this->_helper->redirector('index', 'employee-travel', 'employees');
		}
		
	}

	/**
	 * Get employee travel details to show in a grid
	 */
	public function listTravelAction()
	{
		$layout = $this->_helper->layout();
		$layout->disableLayout('layout');
		if(isset($_SERVER['HTTP_REFERER']))
		{
			if($this->_checkSession && $this->_travelAccessRights['Employee']['View']==1)
			{
				$ajaxContext = $this->_helper->getHelper('AjaxContext');
				$ajaxContext->addActionContext('emptravelsreload','json')->initContext();

				$firstName = $this->_getParam('firstName', null);
				$lastName = $this->_getParam('lastName', null);
 				$amountCon = $this->_getParam('amountCon', null);
				$amounttxt = $this->_getParam('amounttxt', null);
				$status = $this->_getParam('status', null);
 				$startDateCon = $this->_getParam('startDateCon', null);
				$startDatetxt = $this->_getParam('startDatetxt', null);
 				$endDateCon = $this->_getParam('endDateCon', null);
				$endDatetxt = $this->_getParam('endDatetxt', null);
				 
				$firstName = filter_var($firstName,FILTER_SANITIZE_STRIPPED);
				$lastName = filter_var($lastName,FILTER_SANITIZE_STRIPPED);
				$amounttxt = filter_var($amounttxt,FILTER_SANITIZE_STRIPPED);
				$startDatetxt = filter_var($startDatetxt,FILTER_SANITIZE_STRIPPED);
				$endDatetxt = filter_var($endDatetxt,FILTER_SANITIZE_STRIPPED);
				$status = filter_var($status,FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('page', null);
				$rows = $this->_getParam('rows', null);
				$sortField = $this->_getParam('sort', null);
				$sortOrder = $this->_getParam('order', null);
				if ($this->_isMobile)//mobile
				{
					$sortArr = json_decode($sortField, true);
					$sortField = $sortArr[0]['property'];
					$sortOrder = $sortArr[0]['direction'];
					$rows = $this->_getParam('limit', null);
					$amountCon = $this->_getParam('amounttxt-comparison', null);
					$startDateCon = $this->_getParam('startDatetxt-comparison', null);
					$endDateCon = $this->_getParam('endDatetxt-comparison', null);
				}
				$sortField = isset($sortField)?filter_var($sortField,FILTER_SANITIZE_STRIPPED):'Added_On';
				$sortOrder = isset($sortOrder)?filter_var($sortOrder,FILTER_SANITIZE_STRIPPED):'DESC';
				$page = isset($page)?intval($page):1;
				$rows = isset($rows)?intval($rows):10;

				$travelUser = array('Is_Manager'=>$this->_travelAccessRights['Employee']['Is_Manager'], 'Op_Choice'=>$this->_travelAccessRights['Employee']['Optional_Choice'], 'Update'=>$this->_travelAccessRights['Employee']['Update'],
						'Admin'=>$this->_travelAccessRights['Admin'], 'LogId'=>$this->_logEmpId);
				$this->view->empTravelDetails = $this->_dbTravel->searchEmpTravels($firstName,$lastName,$amountCon,$amounttxt,$status,$page, $rows, $sortField, $sortOrder,$travelUser,$startDateCon,$startDatetxt,$endDateCon,$endDatetxt, $this->_formName);
			}
		}
		else
		{
			$this->_helper->redirector('index', 'employee-travel', 'employees');
		}
	}

	/**
	 * Get employee travel details to view
	 */
	public function viewTravelAction()
	{
		$this->_helper->layout->disableLayout();
		if(isset($_SERVER['HTTP_REFERER']))
		{
			$requestId = $this->_getParam("_rId", null);
			$requestId = filter_var($requestId, FILTER_SANITIZE_NUMBER_INT);
			 
			$this->_helper->layout->setLayout('tab_layout');

			if ( ! empty($requestId) && $this->_checkSession)
			{
				if($this->_travelAccessRights['Employee']['View'] == 1)
				{
					$empTravelDetail = $this->_dbTravel->viewEmpTravel($requestId);
					$this->view->viewEmpTravel = $empTravelDetail;
					$this->view->currency = $this->_dbPersonal->currencySymbol($empTravelDetail[0]['Employee_Id']); // to get currency symbol for the employee
					$this->view->formName = $this->_formName;
					$this->view->cntComment = $this->_dbComment->countComment($requestId, $this->_formName); // to check whether comment exists
				}
				else
				{
					$this->view->access = 'Sorry, Access Denied...';
				}
			}
		}
		else
		{
			$this->_helper->redirector('index', 'employee-travel', 'employees');
		}
	}

	/**
	 * Check employee view access rights to show travel audit details
	 */
	public function auditTravelAction()
	{
		$layout = $this->_helper->layout();
		$layout->disableLayout('layout');
		if(isset($_SERVER['HTTP_REFERER']))
		{
			$layout->setLayout('tab_layout');
			if($this->_checkSession)
			{
				if($this->_travelAccessRights['Employee']['View']!=1)
				{
					$this->view->access = 'Sorry, Access Denied...';
				}
			}
		}
		else
		{
			$this->_helper->redirector('index', 'employee-travel', 'employees');
		}
	}

	/**
	 * Get employee audit travel details to show in a grid
	 */
	public function showAuditTravelAction()
	{
		$this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('show-audit-travel', 'json')->initContext();
			
			$auditRequestId = $this->_getParam('requestId', null);			 
			$auditRequestId = filter_var($auditRequestId, FILTER_SANITIZE_NUMBER_INT);
			
			if($this->_employeeAccess['Employee']['View']==1)
			{
				$this->view->result=$this->_dbTravel->empTravelAudit($auditRequestId);
			}
			else{
				$this->view->result = array('success'=> false, 'msg' => 'Sorry, Access Denied...', 'type' => 'warning');
			}
		}
		else
		{
			$this->_helper->redirector('index', 'employee-travel', 'employees');
		}
	}

	/**
	 * Update travel status
	 */
	public function travelStatusAction()
	{
		$this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
		{			
			$requestId = $this->_getParam('requestId', null);
			$requestId = filter_var($requestId, FILTER_SANITIZE_NUMBER_INT);

			if(!empty($requestId))
			{
				$accessStatus = $this->_dbComment->payrollStatus($requestId, $this->_formName);
				
				
				
				if($this->_employeeAccess['Admin'] || (($this->_employeeAccess['Employee']['Optional_Choice'] == 1 && $this->_logEmpId == $accessStatus['Payroll_Mid'])
						|| ($this->_employeeAccess['Employee']['Is_Manager'] == 1 && $accessStatus['Approver_Id'] == $this->_logEmpId)))
				{
					$jobIsManager = $this->_employeeAccess['Employee']['Is_Manager'];
					
					if ( $this->getRequest()->isPost())
					{
						$formStatus = $this->getRequest()->getPost();
						
						$forwardTo           = $this->_validation->intValidation($formStatus['forwardTo']);
						$approvalStatus        = $this->_validation->alphaValidation($formStatus['status']);
						$txtComment['value'] = $this->_validation->commonFilters($formStatus['comments']);
                        $txtComment['valid'] = $this->_validation->lengthValidation($txtComment, 5, 250, true);						
						
						$validApprHidden = true;
						$formId = $this->_dbComment->getFormId($this->_formName); // get formId from form name

						if(!empty($forwardTo['value']) && !empty($formData['emp_payroll']))
						{
							$ckFwderAccess = $this->_dbPersonal->checkPayrollEmployee($forwardTo['value'], $formId);
						}

						$travelEmpId=$this->_dbTravel->travelEmployee($requestId); //get employee id from traveltable using request Id
						$travelEmpName = $this->_dbPersonal->employeeName($travelEmpId['Employee_Id']);
						
						$customFormName = $this->_ehrTables->getCustomForms($this->_formName);
						$customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formName);
						
						if (($this->_employeeAccess['Admin'] || $jobIsManager == 1) && (!empty($forwardTo['value']) && $approvalStatus['value'] == 'In Process'))
						{
							$cntMgrStatus = $this->_dbTravel->ckTravelStatus($requestId, $this->_logEmpId,$approvalStatus['value'],'1');
							if ( $cntMgrStatus > 0 )
							{
								$this->view->result = array('success'=> false, 'msg' => $customFormNamee.' request status already updated.', 'type' => 'warning');
							}
							else
							{
								$statusReport = $this->_dbComment->statusReport($approvalStatus['value'], $txtComment['value'], $requestId, $this->_logEmpId, $this->_formName, $forwardTo['value']);
								
								if($statusReport =='1')
								{									
									if ($forwardTo['value'] != $this->_logEmpId)
									{
										$logEmpName = $this->_dbPersonal->employeeName($this->_logEmpId);
										if($travelEmpId['Employee_Id'] == $this->_logEmpId)
										{
											$msgDescA = "<p>".$customFormNamee." Notification forwarded from ". $logEmpName['Employee_Name'] ." is waiting for your approval.</p>";
										}
										else
										{
											$msgDescA = "<p>".$customFormNamee." Notification forwarded from ". $logEmpName['Employee_Name'] ." for the employee ". $travelEmpName['Employee_Name'] ." is waiting for your approval.</p>";
										}

										$mailSend = $this->_dbCommonFunction->communicateMail (array(
																					'employeeId'     => $forwardTo['value'],
																					'ModuleName'     => 'Employees',
																					'formName'    => $this->_formName,
																					 'successMsg'  => $customFormNamee,
																					 'customFormName' => $customFormNamee,
																					 'formUrl'        => '/employees/employee-travel',
																					 'inboxTitle' => $customFormNamee.' Notification',
																					'mailContent'    => $msgDescA,
																					'action'         => 'request status updated'));
											
										$this->view->result = array('success'=> true, 'msg' => $mailSend['msg'], 'type' => 'info');
									}
									else
									{
										$this->view->result = array('success'=> true, 'msg' => $customFormNamee.' request status updated successfully.', 'type' => 'info');
									}	
								}
								else{
									$this->view->result = array('success'=> false, 'msg' => 'Unable to update '.$customFormNamee.' request status', 'type' => 'warning');
								}
							}
						}
						elseif(($approvalStatus['value'] == 'Rejected' || $approvalStatus['value'] == 'Returned') && !empty($txtComment['value']) && ($this->_employeeAccess['Employee']['Optional_Choice'] == 1 || $jobIsManager == 1 || $this->_employeeAccess['Admin']))
						{
							$cntMgrStatus = $this->_dbTravel->ckTravelStatus($requestId, $this->_logEmpId,$approvalStatus['value'],'3');
							if ( $cntMgrStatus > 0 )
							{
								$this->view->result = array('success'=> false, 'msg' => $customFormNamee.' request status already Updated.', 'type' => 'warning');
							}
							else
							{
								$statusReport = $this->_dbComment->statusReport($approvalStatus['value'], $txtComment['value'], $requestId, $this->_logEmpId, $this->_formName, '');
								
								if($statusReport =='1')
								{
									$sucessMail = 'do-not-send-email';
									
									$subject = strtolower($approvalStatus['value']);
									if($travelEmpId['Employee_Id']!=$this->_logEmpId )
									{	 
										$msgDescA = "<p>Your Travel request has been $subject.</p>";																				
											
										$mailSend = $this->_dbCommonFunction->communicateMail (array(
																						'employeeId'     => $travelEmpId['Employee_Id'],
																						'ModuleName'     => 'Employees',
																					'formName'    => $this->_formName,
																					 'successMsg'  => $customFormNamee,
																					 'customFormName' => $customFormNamee,
																					 'formUrl'        => '/employees/employee-travel',
																					 'inboxTitle' => $customFormNamee.' Notification',
																						'mailContent'    => $msgDescA,
																						'action'         => 'request status updated'));
										$sucessMail = 'mail-notification-triggered';
									}
									
									if( $accessStatus['Added_By'] != $this->_logEmpId && !in_array($accessStatus['Added_By'], array($travelEmpId['Employee_Id'],$accessStatus['Approver_Id'])))
									{
										if($accessStatus['Added_By'] == $travelEmpId['Employee_Id'])
										{
											$msgDescX = "<p>Your ".$customFormNamee." request has been $subject.</p>";
										}
										else{
											$msgDescX = "<p>Your ".$customFormNamee." request for the employee ". $travelEmpName['Employee_Name'] ." has been $subject.</p>";
										}
										
										$mailSend = $this->_dbCommonFunction->communicateMail (array(
																						'employeeId'     => $accessStatus['Added_By'],
																						'ModuleName'     => 'Employees',
																					'formName'    => $this->_formName,
																					 'successMsg'  => $customFormNamee,
																					 'customFormName' => $customFormNamee,
																					 'formUrl'        => '/employees/employee-travel',
																					 'inboxTitle' => $customFormNamee.' Notification',
																						'mailContent'    => $msgDescX,
																						'action'         => 'request status updated'));
										$sucessMail = 'mail-notification-triggered';
									}
									
									if($accessStatus['Approver_Id'] != $this->_logEmpId && !in_array($accessStatus['Approver_Id'], array($travelEmpId['Employee_Id'],$accessStatus['Added_By'])) )
									{
										if($accessStatus['Approver_Id'] == $travelEmpId['Employee_Id'])
											$msgDescN = "<p>Your ".$customFormNamee." request has been $subject.</p>";
										else
											$msgDescN = "<p>Your ".$customFormNamee." request for the employee ". $travelEmpName['Employee_Name'] ." has been $subject.</p>";
											
										$mailSend = $this->_dbCommonFunction->communicateMail (array(
																					'employeeId'     => $accessStatus['Approver_Id'],
																					'ModuleName'     => 'Employees',
																					'formName'    => $this->_formName,
																					 'successMsg'  => $customFormNamee,
																					 'customFormName' => $customFormNamee,
																					 'formUrl'        => '/employees/employee-travel',
																					 'inboxTitle' => $customFormNamee.' Notification',
																					'mailContent'    => $msgDescN,
																					'action'         => 'request status updated'));
										$sucessMail = 'mail-notification-triggered';
									}
									
									if($sucessMail == 'do-not-send-email'){
										$this->view->result = array('success'=> true, 'msg' => $customFormNamee.' request status updated successfully', 'type' => 'info');
									}else{
										$this->view->result = array('success'=> true, 'msg' => $mailSend['msg'], 'type' => 'info');
									}
								}
								else{
									$this->view->result = array('success'=> false, 'msg' => 'Unable to update '.$customFormNamee.' request status', 'type' => 'warning');
								}
							}
						}
						elseif(($this->_employeeAccess['Employee']['Optional_Choice'] == 1 || $this->_employeeAccess['Admin'])  && $approvalStatus['value'] == 'Complete')
						{
							$cntMgrStatus = $this->_dbTravel->ckTravelStatus($requestId, $this->_logEmpId,$approvalStatus['value'],'2');
							if ( $cntMgrStatus > 0 )
							{								
								$this->view->result = array('success'=> false, 'msg' => $customFormNamee.' request status already updated.', 'type' => 'warning');
							}
							else
							{
								$statusReport = $this->_dbComment->statusReport($approvalStatus['value'], $txtComment['value'], $requestId, $this->_logEmpId, $this->_formName, '');
								
								if($statusReport =='1')
								{
									$sucessMail = 'do-not-send-email';
									
									if($this->_logEmpId != $travelEmpId['Employee_Id'] && $accessStatus['Added_By']!=$travelEmpId['Employee_Id'])
									{
										$msgDescA = "<p>Your ".$customFormNamee." request has been approved.</p>";
										
										$mailSend = $this->_dbCommonFunction->communicateMail (array(
																					'employeeId'     => $travelEmpId['Employee_Id'],
																					'ModuleName'     => 'Employees',
																					'formName'    => $this->_formName,
																					 'successMsg'  => $customFormNamee,
																					 'customFormName' => $customFormNamee,
																					 'formUrl'        => '/employees/employee-travel',
																					 'inboxTitle' => $customFormNamee.' Notification',
																					'mailContent'    => $msgDescA,
																					'action'         => 'status updated'));
										
										$sucessMail = 'mail-notification-triggered';										
									}
									
									if( $accessStatus['Added_By'] != $this->_logEmpId && $accessStatus['Added_By']!=$travelEmpId['Employee_Id'])
									{
										$msgDescX = "<p>Your ".$customFormNamee." request for the employee ". $travelEmpName['Employee_Name'] ." has been approved.</p>";
										
										$mailSend = $this->_dbCommonFunction->communicateMail (array(
																					'employeeId'     => $accessStatus['Added_By'],
																					'ModuleName'     => 'Employees',
																					'formName'    => $this->_formName,
																					 'successMsg'  => $customFormNamee,
																					 'customFormName' => $customFormNamee,
																					 'formUrl'        => '/employees/employee-travel',
																					 'inboxTitle' => $customFormNamee.' Notification',
																					'mailContent'    => $msgDescX,
																					'action'         => 'status updated'));
										
										$sucessMail = 'mail-notification-triggered';
									}
									
									if($sucessMail == 'do-not-send-email'){
										$this->view->result = array('success'=> true, 'msg' => $customFormNamee.' request status updated successfully', 'type' => 'info');
									}else{
										$this->view->result = array('success'=> true, 'msg' => $mailSend['msg'], 'type' => 'info');
									}
								}
								else{
									$this->view->result = array('success'=> false, 'msg' => 'Unable to update status', 'type' => 'warning');
								}
							}
						}
						else 
						{
							$this->view->result = array('success'=> false, 'msg' => 'Unable to update status.Please, contact admin', 'type' => 'warning');
						}
					}
				}
				else
				{
					$this->view->result = array('success'=> false, 'msg' => 'Sorry, Access Denied', 'type' => 'warning');
				}
			}
			else{
				$this->view->result = array('success'=> false, 'msg' => 'Invalid Data', 'type' => 'warning');
			}
		}
		else
		{
			$this->_helper->redirector('index', 'employee-travel', 'employees');
		}
	}

	/**
	 * Get travel details to show in sub grid
	 */
	public function travelSubgridAction()
	{
		$this->_helper->layout->disableLayout();
		
		if(isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('travel-subgrid','json')->initContext();
			
			$requestId = $this->_getParam('_rId', null);
			$requestId = filter_var($requestId,FILTER_SANITIZE_NUMBER_INT);
			
			if($this->_checkSession && !empty($requestId) &&  $this->_travelAccessRights['Employee']['View']==1)
			{
				$sortField = $this->_getParam('sort', null);
				$sortOrder = $this->_getParam('order', null);
				
				if ($this->_isMobile)
                {
                    $sortArr   = json_decode($sortField, true);
                    $sortField = $sortArr[0]['property'];
                    $sortOrder = $sortArr[0]['direction'];
                    
                    $page      = $this->_getParam('page', null); 
                    $page      = isset($page)?intval($page):1;
                    $page      = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
                    
                    $rows      = $this->_getParam('limit', null);
                    $rows      = isset($rows)?intval($rows):10;
                    $rows      = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
					
					$isView = $this->_getParam('isView', null);
					$isView = filter_var($isView, FILTER_SANITIZE_NUMBER_INT);
                }
				
				$sortField = isset($sortField)?filter_var($sortField,FILTER_SANITIZE_STRIPPED):'Place';
				$sortOrder = isset($sortOrder)?filter_var($sortOrder,FILTER_SANITIZE_STRIPPED):'ASC';
				
				if ($this->_isMobile)
				{
					$this->view->empTravelData = $this->_dbTravel->travelSubGrid($requestId, $sortField, $sortOrder,
																				 $this->_isMobile, $page, $rows, $isView);
				}
				else
				{
					$this->view->empTravelData = $this->_dbTravel->travelSubGrid($requestId, $sortField, $sortOrder);
				}
			}
		}
		else
		{
			$this->_helper->redirector('index', 'employee-travel', 'employees');
		}
	}
	
	/*
	 * creating dynamic element for add and edit Travels
	* @param int $dynCount - row count of dynamically created element
	* @return HTML
	*/
	 
	public function createDestinationfieldAction()
	{
		$this->_helper->layout->disableLayout();
		if(isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('create-destinationfield','html')->initContext();

			$dynCount = $this->_getParam('_dynCount', null);
			$dynCount = filter_var($dynCount,FILTER_SANITIZE_NUMBER_INT);
			if($this->_checkSession)
			{
				$nxtDynCount = $dynCount+1;
				$arrDynElement = array();
				$modeMultiOpt = array(
						'' =>'--Select--',
						'car' => 'By Car',
						'bus' => 'By Bus',
						'train' => 'By Train',
						'plane' => 'By Plane'
				);
				$arrangeMultiOpt = array(
						'' =>'--Select--',
						'personal' => 'Personal Arrangement',
						'hotel' => 'HOTEL'
				);

				$place = new Zend_Form_Element_Text('place'.$nxtDynCount.'');
				$place->setAttrib('class', 'place'.$nxtDynCount.' dynPlace dynElem1 text_name')
				->addValidator('NotEmpty');
				$place->setDecorators(array('ViewHelper',array(array('data'=>'HtmlTag'), array('tag' => 'td','class'=>'place')),array(array('row'=>'HtmlTag'), array('tag' => 'tr','class' => 'dynAdd'.$nxtDynCount.' dynRow', 'openOnly' => true))));
				 
				$mode = new Zend_Form_Element_Select('mode'.$nxtDynCount.'');
				$mode->addMultiOptions($modeMultiOpt)->setAttribs(array('class'=> 'dynMode dynElem2'));
				$mode->setDecorators(array('ViewHelper',array('Description', array('escape' => false, 'tag' => false),array('placement' => Zend_Form_Decorator_Abstract::APPEND, 'tag' => 'em')),array(array('data'=>'HtmlTag'), array('tag' => 'td','class'=>'mode'))));

				$arrange = new Zend_Form_Element_Select('arrange'.$nxtDynCount.'');
				$arrange
				->setDescription('<img src="'.$this->_basePath->baseUrl('images/add.png').'" id="amtAddDynRowFieldfrmAddTravel" class="show"><img src="'.$this->_basePath->baseUrl('images/cancel.png').'" id="amtRemDynRowFieldfrmAddTravel" class="show">')
				->setAttribs(array('class'=> 'dynArrange dynElem3'))
				->addMultiOptions($arrangeMultiOpt)
				->setDecorators(array('ViewHelper',array('Description', array('escape' => false, 'tag' => false), array('placement' => Zend_Form_Decorator_Abstract::APPEND, 'tag' => 'em')),array(array('data'=>'HtmlTag'), array('tag' => 'td','colspan'=>2,'class'=>'arrange')),array(array('row'=>'HtmlTag'), array('tag' => 'tr', 'closeOnly' => true))));

				$arrDynElement[] = $place->__toString();
				$arrDynElement[] = $mode->__toString();
				$arrDynElement[] = $arrange->__toString();
				$this->view->arrDynElement = $arrDynElement;
			}
		}
		else
		{
			$this->_helper->redirector('index', 'employee-travel', 'employees');
		}
	}

	/**
	 * Server side validation for dynamic travel details
	 */
	public function dynamicCheckingFuncion($formData)
	{		 
		$trimFilter = new Zend_Filter_StringTrim();
		$tagsFilter = new Zend_Filter_StripTags();
		$alnumValidate = new Zend_Validate_Alnum(array('allowWhiteSpace' => true));
		$rowWithDynFields = array();
		$dupexist = array();

		for($m = 0;$m <= $formData['amtDynFldCount']; $m++ )
		{
			$formData['place'.$m] = $trimFilter->filter($formData['place'.$m]);
			$formData['place'.$m] = $tagsFilter->filter($formData['place'.$m]);
			$formData['mode'.$m] = $trimFilter->filter($formData['mode'.$m]);
			$formData['mode'.$m] = $tagsFilter->filter($formData['mode'.$m]);
			$formData['arrange'.$m] = $trimFilter->filter($formData['arrange'.$m]);
			$formData['arrange'.$m] = $tagsFilter->filter($formData['arrange'.$m]);

			if($alnumValidate->isValid($formData['place'.$m]) &&  !empty($formData['place'.$m]) && $alnumValidate->isValid($formData['mode'.$m]) &&  $formData['mode'.$m] != "" && $alnumValidate->isValid($formData['arrange'.$m]) &&  $formData['arrange'.$m] != "")
			{
				$rowWithDynFields[$m] = array(strtolower($formData['place'.$m]));
			}
			else
			{
				return false;
			}
		}
		$currentRow = current($rowWithDynFields);
		foreach ($rowWithDynFields as $key => $row)
		{
			if($key > 0 )
			{
				count(array_diff_assoc($currentRow,$row)) < 1 ? array_push($dupexist, 1) : null;
			}
		}
		return (count($rowWithDynFields) < 1) ||  count($dupexist) > 0 ? false : true;
	}
	
	/**
	 * To get formatted date
	 */
	public function dateForPhp($givenDate)
	{
		return date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($givenDate)));
	}
	   
	public function __destruct()
    {
        
    }	
}
