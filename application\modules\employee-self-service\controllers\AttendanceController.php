<?php

include APPLICATION_PATH."/validations/Validations.php"; 
class EmployeeSelfService_AttendanceController extends Zend_Controller_Action
{

    protected $_formName = 'Data Setup Dashboard';

    protected $_dbCommonFunction = null;

    protected $_hrappMobile = null;

    public function init()
    {
        $this->_hrappMobile = new Application_Model_DbTable_HrappMobile();

        if ($this->_hrappMobile->checkAuth())
        {
            $this->_dbCommonFunction   = new Application_Model_DbTable_CommonFunction();
        }
        else
        {
            if (Zend_Session::namespaceIsset('lastRequest'))
                Zend_Session:: namespaceUnset('lastRequest');
            
            $session = new Zend_Session_Namespace('lastRequest');
            $session->lastRequestUri = 'v3/employee-self-service/attendance';
            $this->_redirect('auth');
        }

    }

    public function indexAction()
    {
        $checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();
        
        if ($checkSessionAuth)
        {
            $this->_helper->layout->disableLayout();
            $this->_helper->layout->setLayout('admin_layout');
            $this->_redirect('v3/employee-self-service/attendance');
        } else {
			$this->_redirect('auth');
		}
    }

   
}



