<?php
//=========================================================================================
//=========================================================================================
/* Program : Timesheet.php											   			         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MQL Query to retrive, add, update timesheet details and also to         *
 * update status reports.																 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Devirani            	  Initial Version        	         *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Saranya                 Changes in file for mobile app     *
 *                                                    1.Extra fields are added in        *
 *                                                    field list of list query.          */
//=========================================================================================
//=========================================================================================
class Employees_Model_DbTable_Timesheet extends Zend_Db_Table_Abstract
{
    protected $_dbComment = null;
    protected $_dbPersonal = null;
    protected $_orgDF = null;
    protected $_db = null;
    protected $_ehrTables = null;
    protected $_hrappMobile = null;
    protected $_isMobile = null;
    protected $_commonFunction		= null;
    protected $_eftConfiguration	= null;
	protected $_orgDetails    = null;

    public function init()
    {
        $this->_ehrTables   = new Application_Model_DbTable_Ehr();
        $this->_db          = Zend_Registry::get('subHrapp');
        $this->_dbComment   = new Payroll_Model_DbTable_PayrollComment();
        $this->_dbPersonal  = new Employees_Model_DbTable_Personal();
        $this->_orgDF       = $this->_ehrTables->orgDateformat();
		$this->_commonFunction = new Application_Model_DbTable_CommonFunction();
		$this->_eftConfiguration =new Organization_Model_DbTable_EftConfiguration();

		if (Zend_Registry::isRegistered('orgDetails'))
		$this->_orgDetails = Zend_Registry::get('orgDetails');
    }
    
    
    public function searchTimesheetDetails($page,$rows,$sortField,$sortOrder,$searchAll=null,$searchDetails,$userDetails)
    {
		switch ($sortField)
        {
        	case 1: $sortField = 'EJ.User_Defined_EmpId'; break;
            case 2: $sortField = 'EP.Emp_First_Name'; break;
			case 3: $sortField = 'TS.Week_Ending_Date'; break;
			case 4: $sortField = 'TS.Approval_Status'; break;
			default:
				$sortField = 'TS.Request_Id'; $sortOrder = 'desc'; break;
        }
		
		if (!empty($userDetails['Form_Name']))
        {
        	$formId = $this->_dbComment->getFormId($userDetails['Form_Name']);
        	 
        	$qryComment = $this->_db->select()->distinct()->from(array('Cm'=>$this->_ehrTables->comment), 'Parent_Id')
												->where('Cm.Parent_Id = TS.Request_Id AND Cm.Form_Id='.$formId);
        }
	
	
		$qryTimesheet = $this->_db->select()->from(array('TS'=>$this->_ehrTables->empTimesheet),
                                                    array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS TS.Request_Id as count'),
                                                                  'TS.Request_Id','TS.Employee_Id','TS.Approver_Id','TS.Week_Ending_Date',
								  new Zend_Db_Expr("DATE_FORMAT(TS.Week_Ending_Date,'".$this->_orgDF['sql']."') as WeekEndingDate"),
								  new Zend_Db_Expr("DATE_FORMAT(TS.Added_Date,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
								  'TS.Approval_Status', 'TS.Added_By',
								  'Role'=>new Zend_Db_Expr("'".$userDetails['Admin']."'"),
								  'Comment'=> new Zend_Db_Expr('('.$qryComment.')'),
								 'Session_Id'=>new Zend_Db_Expr($userDetails['Session_Id']),
                                                                  'DT_RowClass' => new Zend_Db_Expr('"TimesheetDetails"'),
                                                                  'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', TS.Request_Id)")))
						
						->joinLeft(array('A'=>$this->_ehrTables->auditTimesheet),'TS.Request_Id=A.Request_Id',
										   array('A.Request_Id as Audit'))
								     
					    ->joinInner(array('EP'=>$this->_ehrTables->empPersonal),
								    'TS.Employee_Id=EP.Employee_Id',
								    array('Employee_Name'=>new Zend_Db_Expr("CONCAT(EP.Emp_First_Name,' ',EP.Emp_Last_Name)")))
                        
                         ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'EP.Employee_Id=EJ.Employee_Id',
                                            array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN EP.Employee_Id ELSE EJ.User_Defined_EmpId END')))
					    
					    ->joinInner(array('EP1'=>$this->_ehrTables->empPersonal),
								    'TS.Approver_Id = EP1.Employee_Id',
								    array(new Zend_Db_Expr("Concat(EP1.Emp_First_Name,' ',EP1.Emp_Last_Name) as Approver_Name")))
					    
					    ->joinInner(array('EP2'=>$this->_ehrTables->empPersonal),
							       'TS.Added_By = EP2.Employee_Id',
							       array(new Zend_Db_Expr("Concat(EP2.Emp_First_Name,' ',EP2.Emp_Last_Name) as Added_By"),'TS.Added_By as Added_By_Id'))
						
					    ->group('TS.Request_Id')
					    ->order("$sortField $sortOrder")
					    ->limit($rows, $page);

		if(empty($userDetails['Admin']))
        {
            $qryEmployeeId = $this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))->where('Manager_Id = ?', $userDetails['Session_Id']);
		    $getEmployeeId = $this->_db->fetchCol($qryEmployeeId);
            if ($userDetails['Is_Manager'] == 1 && !empty($getEmployeeId)) 
			{
				if($this->_orgDetails['Immediate_Reportees_View_Only']==0)
				{
					$getEmployeeId = $this->_commonFunction->getMultiLevelManagerIds($userDetails['Session_Id'],1);
					array_push($getEmployeeId,$userDetails['Session_Id']);
					$qryTimesheet->where('TS.Employee_Id IN (?)', $getEmployeeId);
				}
				else
				{
					$qryTimesheet->where('TS.Employee_Id = :EmpId or TS.Approver_Id = :EmpId or TS.Employee_Id IN (?)', $getEmployeeId)
					->bind(array('EmpId'=>$userDetails['Session_Id']));
				}
            }
            else
	    	{
                $qryTimesheet->where('TS.Employee_Id = ?', $userDetails['Session_Id']);
            }
        }				
	
		if (!empty($searchAll) && $searchAll != null)
		{
			$conditions = $this->_db->quoteInto(new Zend_Db_Expr('Concat(EP.Emp_First_Name," ",EP.Emp_Last_Name) Like ?'),"%$searchAll%");
			$conditions .= $this->_db->quoteInto('or TS.Week_Ending_Date Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or TS.Approval_Status Like ?', "%$searchAll%");
             $conditions .= $this->_db->quoteInto('or EJ.User_Defined_EmpId Like ?', "%$searchAll%");	
			
			$qryTimesheet->where($conditions);		
		}		
		
		$employeeName=$searchDetails['Employee_Name'];
		$weekendDateBegin=$searchDetails['Week_Ending_Date_Begin'];
		$weekendDateEnd=$searchDetails['Week_Ending_Date_End'];
		$approvalStatus=$searchDetails['Approval_Status'];
		$serviceProviderId=$searchDetails['serviceProviderId'];
	
	
	
		if ($employeeName != '' && $employeeName != null && preg_match('/^[a-zA-Z]/', $employeeName))
		{
				$qryTimesheet->where($this->_db->quoteInto(new Zend_Db_Expr('Concat(EP.Emp_First_Name," ",EP.Emp_Last_Name) Like ?'),"%$employeeName%"));
		}
	
		if ($weekendDateBegin != '' && $weekendDateEnd != '') {
			$qryTimesheet->where($this->_db->quoteInto('TS.Week_Ending_Date >= ?', $weekendDateBegin));
			$qryTimesheet->where($this->_db->quoteInto('TS.Week_Ending_Date <= ?', $weekendDateEnd));
		}
		
		if (!empty($approvalStatus))
		{
			$qryTimesheet->where($this->_db->quoteInto('TS.Approval_Status = ?',$approvalStatus));
		}

		if(!empty($serviceProviderId)&& $this->_orgDetails['Field_Force']==1)
        {
            $qryTimesheet->where('EJ.Service_Provider_Id = ?',$serviceProviderId);
        }
		 
		$qryTimesheet = $this->_commonFunction->getDivisionDetails($qryTimesheet,'EJ.Department_Id');

		if(!empty($userDetails['Admin']))
		{			
			$qryTimesheet = $this->_commonFunction->formServiceProviderQuery($qryTimesheet,'EJ.Service_Provider_Id',$userDetails['Session_Id']);
		}

		$timesheetDetails = $this->_db->fetchAll($qryTimesheet);
		
		
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		$iTotal = $this->_db->select()->from(array('TS'=>$this->_ehrTables->empTimesheet),
						     new Zend_Db_Expr('COUNT(Request_Id)'))->group('Request_Id');
		
		if(empty($userDetails['Admin']))
		{
		    $qryEmployeeId = $this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))->where('Manager_Id = ?', $userDetails['Session_Id']);
		    $getEmployeeId = $this->_db->fetchCol($qryEmployeeId);
		    if ( $userDetails['Is_Manager'] == 1 && !empty($getEmployeeId)) 
			{
				if($this->_orgDetails['Immediate_Reportees_View_Only']==0)
				{
					$getEmployeeId = $this->_commonFunction->getMultiLevelManagerIds($userDetails['Session_Id'],1);
					array_push($getEmployeeId,$userDetails['Session_Id']);
					$iTotal->where('TS.Employee_Id IN (?)', $getEmployeeId);
				}
				else
				{
					$iTotal->where('TS.Employee_Id = :EmpId or TS.Approver_Id = :EmpId or TS.Employee_Id IN (?)', $getEmployeeId)
																			   ->bind(array('EmpId'=>$userDetails['Session_Id']));
				}
		    }
		    else
		    {
			$iTotal->where('TS.Employee_Id = ?', $userDetails['Session_Id']);
		    }
		}			
		//$iTotal = fetchaAll
		
		$iTotal = $this->_db->fetchAll($iTotal);
		
		return array("iTotalRecords" => count($iTotal), "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $timesheetDetails);	
    }
    
    public function updateTimesheetDetails($timesheetDetails,$action,$comments, $formName)
    {
		$qryTimesheetDetails = $this->_db->select()->from($this->_ehrTables->empTimesheet, new Zend_Db_Expr('count(Request_Id)'))
												->where('Employee_Id = ?', $timesheetDetails['Employee_Id'])
												->where('Approval_Status != ?','Rejected')
												->where('Week_Ending_Date = ?', $timesheetDetails['Week_Ending_Date']);

		if (!empty($timesheetDetails['Request_Id']))
		{
			$qryTimesheetDetails->where('Request_Id != ?', $timesheetDetails['Request_Id']);
		}	

		$requestId = $this->_db->fetchOne($qryTimesheetDetails);
		if(empty($requestId))
		{
			$qryActivityDetails = $this->_db->select()->from($this->_ehrTables->empTimesheet, new Zend_Db_Expr('count(Request_Id)'))
												->where('Employee_Id = ?', $timesheetDetails['Employee_Id'])
												->where('Approval_Status != ?','Rejected')
												->where('Week_Ending_Date = ?', $timesheetDetails['Week_Ending_Date'])
												->where('Project_Id = ?', $timesheetDetails['Project_Id'])
												->where('Project_Activity_Id = ?', $timesheetDetails['Project_Activity_Id'])
												->where('Timesheet_Type = ?', $timesheetDetails['Timesheet_Type']);

			if($action=='Update')
			{
				$qryActivityDetails->where('Project_Activity_Id != ?', $timesheetDetails['Project_Activity_Id'])
									->where('Timesheet_Type != ?', $timesheetDetails['Timesheet_Type']);
			}									

			$activityExist = $this->_db->fetchOne($qryActivityDetails);
												
			if(empty($activityExist))
			{
				$validateTimesheetHours = $this->validateTimesheetHours($timesheetDetails,$action);
				if(!empty($validateTimesheetHours))
				{
					if($action=='Update')
					{
						$totalHrsOld = $this->getTimeSheetTotalHrs($requestId);
						
						$where['Request_Id = ?'] = $timesheetDetails['Request_Id'];
						$where['Employee_Id = ?'] = $timesheetDetails['Employee_Id'];
						$where['Week_Ending_Date = ?'] = $timesheetDetails['Week_Ending_Date'];
						$where['Project_Id = ?'] = $timesheetDetails['Project_Id'];
						$where['Project_Activity_Id = ?'] = $timesheetDetails['Project_Activity_Id'];
						$where['Timesheet_Type = ?'] = $timesheetDetails['Timesheet_Type'];
						$action = 'Edit';
						$updated = $this->_db->update($this->_ehrTables->empTimesheet, $timesheetDetails,$where);
						$employeeId= $timesheetDetails['Employee_Id'];
						
						if($updated){
							$totalHrsNew = $this->getTimeSheetTotalHrs($requestId);
							
							if ($totalHrsOld != $totalHrsNew)
							{
								$this->addTimesheetHistory($requestId,$totalHrsOld,$totalHrsNew,$timesheetDetails['Added_By']);
							}
						}
					}
					elseif($action=='Add')
					{
						$action = 'Add';
						$timesheetDetails['Lock_Flag']  = 0;
						$updated =  $this->_db->insert($this->_ehrTables->empTimesheet, $timesheetDetails);
						$employeeId= $timesheetDetails['Employee_Id'];
					}
				}
				else 
				{
					return array('success' => false, 'msg'=>'Total activity hours for this weekend should not exceed the regular/overtime hours limit', 'type'=>'info');	
				}
			}
			else 
			{
				return array('success' => false, 'msg'=>'Activity already exist for this timesheet type and weekend date', 'type'=>'info');
			}
		}	
		else
		{
			return array('success' => false, 'msg'=>'Timesheets already exist for this weekend date', 'type'=>'info');
		}
		
		if(!empty($comments) && !empty($updated))
		{
			$formId = $this->_dbComment->getFormId('Timesheets');
			$addComment = array('Form_Id'=>$formId,
					'Emp_Comment'=>$comments,
					'Approval_Status'=>$timesheetDetails['Approval_Status'],
					'Parent_Id'=>$timesheetDetails['Request_Id'],
					'Employee_Id'=>$timesheetDetails['Added_By'],
					'Added_On'=>date('Y-m-d H:i:s'));
			$this->_db->insert($this->_ehrTables->comment, $addComment);
		}
			
		return $this->_eftConfiguration->updateResult (array('updated'    => $updated,
						'action'         => $action,
						'trackingColumn' => $timesheetDetails['Request_Id'],
						'formName'       => $formName,
						'sessionId'      => $timesheetDetails['Added_By'],
						'tableName'      => $this->_ehrTables->empTimesheet));
    }

	//Based on employee timesheet detail and action we need to check whether timesheet hours valid or not.
	public function validateTimesheetHours($timesheetDetails,$action)
	{
		//Based on weekend date,employee id and timesheet type get the sum of activity hours for each day.
		$timesheetHours 	= $this->getTimeSheetDayHrs($timesheetDetails);
		
		$gradeId 			= $this->getEmpTsGrade($timesheetDetails['Employee_Id']);
		$timesheetHrsLimit 	= $this->getTimesheetHrsLimit($gradeId);

		if($timesheetDetails['Timesheet_Type']=='Regular')
		{
           $perDayMaxHourLimit =  $timesheetHrsLimit['Regular_Hours'];
		}
		else 
		{
			$perDayMaxHourLimit = $timesheetHrsLimit['OverTime_Hours'];
		}

		if($action=='Add')
		{
			//when the action is add we need to add the current activity hours for each day with total timesheet hours
			$dayOneTotalHours 	= $timesheetHours['day1']+$timesheetDetails['Day1'];
			$dayTwoTotalHours 	= $timesheetHours['day2']+$timesheetDetails['Day2'];
			$dayThreeTotalHours = $timesheetHours['day3']+$timesheetDetails['Day3'];
			$dayFourTotalHours 	= $timesheetHours['day4']+$timesheetDetails['Day4'];
			$dayFiveTotalHours 	= $timesheetHours['day5']+$timesheetDetails['Day5'];
			$daySixTotalHours 	= $timesheetHours['day6']+$timesheetDetails['Day6'];
			$daySevenTotalHours = $timesheetHours['day7']+$timesheetDetails['Day7'];
		}
		elseif($action=='Update') 
		{
			$timesheetActivity 	= $this->getTimeSheetDayHrs($timesheetDetails,$action);
			//when the action is update we need to subtract the existing activity hours and add the current activity hours for each day  with total timesheet hours
			$dayOneTotalHours 	= $timesheetHours['day1']-$timesheetActivity['day1']+$timesheetDetails['Day1'];
			$dayTwoTotalHours 	= $timesheetHours['day2']-$timesheetActivity['day2']+$timesheetDetails['Day2'];
			$dayThreeTotalHours = $timesheetHours['day3']-$timesheetActivity['day3']+$timesheetDetails['Day3'];
			$dayFourTotalHours 	= $timesheetHours['day4']-$timesheetActivity['day4']+$timesheetDetails['Day4'];
			$dayFiveTotalHours 	= $timesheetHours['day5']-$timesheetActivity['day5']+$timesheetDetails['Day5'];
			$daySixTotalHours 	= $timesheetHours['day6']-$timesheetActivity['day6']+$timesheetDetails['Day6'];
			$daySevenTotalHours = $timesheetHours['day7']-$timesheetActivity['day7']+$timesheetDetails['Day7'];
		}

		//Each day hours should not exceed the perDayMaxHour Limit for the particular weekend.
		if($perDayMaxHourLimit >= $dayOneTotalHours && $perDayMaxHourLimit >= $dayTwoTotalHours && $perDayMaxHourLimit >= $dayThreeTotalHours && $perDayMaxHourLimit >= $dayFourTotalHours &&
		   $perDayMaxHourLimit >= $dayFiveTotalHours && $perDayMaxHourLimit >= $daySixTotalHours && $perDayMaxHourLimit >= $daySevenTotalHours)
		{
              return true;
		}   
		else 
		{
			return false;
		}
	}

    public function deleteTimesheetDetails($timesheetDetails, $logEmpId, $customFormName, $formName)
    {
	    if (!empty($timesheetDetails))
        {
		    $timesheetDetailsLock = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->empTimesheet, array('Lock_Flag', 'Approval_Status'))
		     												->where('Request_Id = ?', $timesheetDetails['Request_Id'])
															->where('Employee_Id = ?', $timesheetDetails['Employee_Id'])
															->where('Week_Ending_Date = ?', $timesheetDetails['Week_Ending_Date'])
															->where('Project_Id = ?', $timesheetDetails['Project_Id'])
															->where('Project_Activity_Id = ?', $timesheetDetails['Activity_Id'])
															->where('Timesheet_Type = ?', $timesheetDetails['Timesheet_Type']));
			
		    if (/*$timesheetDetailsLock['Lock_Flag'] == 0 &&*/ ($timesheetDetailsLock['Approval_Status'] == 'Draft' || $timesheetDetailsLock['Approval_Status'] == 'Applied'))
		    {
				$where['Request_Id = ?'] = $timesheetDetails['Request_Id'];
				$where['Employee_Id = ?'] = $timesheetDetails['Employee_Id'];
				$where['Week_Ending_Date = ?'] = $timesheetDetails['Week_Ending_Date'];
				$where['Project_Id = ?'] = $timesheetDetails['Project_Id'];
				$where['Project_Activity_Id = ?'] = $timesheetDetails['Activity_Id'];
				$where['Timesheet_Type = ?'] = $timesheetDetails['Timesheet_Type'];

				$deleted = $this->_db->delete($this->_ehrTables->empTimesheet,$where);

				$fetchTimeSheetRequestId =  $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empTimesheet,
											    new Zend_Db_Expr('COUNT(Request_Id)'))
											->where('Request_Id = ?', $timesheetDetails['Request_Id']));

				/* If all the timesheet record associated to the employee for the 'Week_Ending_Date' is deleted,
				then comments should be deleted */
				if ($deleted && empty($fetchTimeSheetRequestId))
				{
					/**
					 *	Delete Comments
					*/
					$this->_dbComment->deleteComment($timesheetDetails['Request_Id'], $formName);
				}
			    
			    
				return $this->_commonFunction->deleteRecord (array('deleted'        => $deleted,
								    'tableName'      => $this->_ehrTables->empTimesheet,
								    'lockFlag'       => 0,
								    'formName'       => $customFormName,
								    'trackingColumn' => $timesheetDetailsLock['Approval_Status'],
								    'sessionId'      => $logEmpId));
			    
			}
			else if ($timesheetDetailsLock['Approval_Status'] == 'Returned')
		    {
				return array('success'=>false, 'msg'=>'Returned '.$customFormName.' cannot be deleted.Please update the '.$customFormName, 'type'=>'info');
		    }
		    else
		    {
				return array('success'=>false, 'msg'=>'Unable to delete '.$customFormName, 'type'=>'info');
		    }
		}
        else
        {
            return array('success'=>false, 'msg'=>'Unable to delete '.$customFormName.'. Please, contact system admin', 'type'=>'info');
        }
    }
    
    public function searchTimesheetTracking($timesheetId,$employeeId,$weekEndingDate,$approvalStatus,$userAccess)
    {
	 
	  $timesheetQry = $this->_db->select()->from(array('TS'=>$this->_ehrTables->empTimesheet),
								       array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS TS.Request_Id as Count'),
									   'Request_Id','Timesheet_Type','Day1','Project_Id','TS.Project_Activity_Id as Activity_Id',
									   'Day2', 'Day3', 'Day4', 'Day5','Day6','Day7','Description',
									   new Zend_Db_Expr("(TS.Day1 + TS.Day2 + TS.Day3 + TS.Day4 + TS.Day5 + TS.Day6 + TS.Day7) as Timesheet_Total_Hours"),
									   'DT_RowClass' => new Zend_Db_Expr('"empTimesheetTracking"'),
									   'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', TS.Request_Id)"),
									   'Edit_Access'=>new Zend_Db_Expr($userAccess['Update']),
									   'Delete_Access'=>new Zend_Db_Expr($userAccess['Delete'])
									   ))
								
								->joinLeft(array('P'=>$this->_ehrTables->project), 'TS.Project_Id=P.Project_Id',
										   array('Project_Name'))
								
								->joinLeft(array('A'=>$this->_ehrTables->timesheetActivity),'TS.Project_Activity_Id=A.Project_Activity_Id', array(''))
										   
								->joinLeft(array('AM'=>$this->_ehrTables->activitiesMaster),'A.Activity_Id=AM.Activity_Id', array('AM.Activity_Name'));
								
		if(!empty($timesheetId))
		{
			$timesheetQry->where('TS.Request_Id = ?', $timesheetId);
		}
		else{
			if(!empty($weekEndingDate))
			{
				$weekEndDate = $weekEndingDate;
			}
			else{
				$weekEndDate = date("Y-m-d", strtotime('next Saturday'));
			}		
		
			$timesheetQry->where('TS.Employee_Id = ?', $employeeId)
						 ->where('TS.Week_Ending_Date = ?', $weekEndDate)
						 ->where('TS.Approval_Status = ?', $approvalStatus);
						 
		}

		$timesheetDetails = $this->_db->fetchAll($timesheetQry);
	               
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
	               
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empTimesheet, new Zend_Db_Expr('COUNT(Request_Id)'))->where('Request_Id = ?', $timesheetId));
	               
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $timesheetDetails);
    }
    
     public function updateTimesheetStatus($requestId,$status, $formName)
    {
		if($requestId>0)
		{
			$approvalStatus['Approval_Status'] = $status;
			$updated = $this->_db->update($this->_ehrTables->empTimesheet, $approvalStatus, array('Request_Id = '.$requestId));
			$updated = 1;
			if($status=='Draft')
					return array('success' => true, 'msg'=>$formName.' Details Updated As Draft', 'type'=>'info');
            else
				    return array('success' => true, 'msg'=>$formName.' Details Updated Successfully', 'type'=>'success');
		}
		else
		{
			return array('success' => false, 'msg'=>'Invalid Data', 'type'=>'info');
		}
    }		

    public function deleteTimesheetTracking($lineItemId,$logEmpId)
    {
        if (!empty($lineItemId))
        {
            $deleted = $this->_db->delete($this->_ehrTables->empTimesheetTracking, 'LineItem_Id='.(int)$lineItemId);
            return $this->_commonFunction->deleteRecord (array( 'deleted'        => $deleted,
                                                                'tableName'      => $this->_ehrTables->empTimesheetTracking,
                                                                'lockFlag'       => 0,
                                                                'formName'       => 'Timesheet Schedule Details',
                                                                'trackingColumn' => $lineItemId,
                                                                'sessionId'      => $logEmpId));
		}
        else
        {
            return array('success'=>false, 'msg'=>'Unable to delete Timesheet Schedule Details. Please, contact system admin', 'type'=>'info');
        }
    }
    
	/**
     *This function is for fetch timesheet history details for a particular employeeId and weekend in a grid.
     */
    public function listTimesheetAudit($requestId)
    {
        $tsHistory = $this->_db->select()
								->from(array('history'=>$this->_ehrTables->auditTimesheet),
									   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS history.Request_Id as Count'),'Total_Hrs_Old as Previous_Total_Hours', 'Total_Hrs_New as Total_Hours', 'Request_Id',
											 new Zend_Db_Expr("date_format(history.Modified_On, '".$this->_orgDF['sql']." %T') as Modified_On")))
								
								->joinInner(array('timesheet'=>$this->_ehrTables->empTimesheet),
											'timesheet.Request_Id = history.Request_Id', array())
								
								->joinInner(array('P1'=>$this->_ehrTables->empPersonal), 'P1.Employee_Id=timesheet.Employee_Id',
											array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P1.Emp_First_Name,' ',P2.Emp_Last_Name)")))
								
								->joinInner(array('P2'=>$this->_ehrTables->empPersonal), 'P2.Employee_Id=history.Modified_By',
											array('Modified_By'=>new Zend_Db_Expr("CONCAT(P2.Emp_First_Name,' ',P2.Emp_Last_Name)")))
								
								->where('timesheet.Request_Id = ?',$requestId)
								->group('history.Audit_Id');
		
	    
	    $timesheetDetails = $this->_db->fetchAll($tsHistory);
                
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
                
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->auditTimesheet, new Zend_Db_Expr('COUNT(Request_Id)')));
                
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $timesheetDetails);
    }
    
	/**
     * *
     * This function is to find project activities.
     *
     */
    public function getActivity()
    {
		$projActQry = $this->_db->select()->from(array('TA'=>$this->_ehrTables->timesheetActivity),array('Project_Activity_Id', 'AM.Activity_Name'))
								->joinInner(array('AM'=>$this->_ehrTables->activitiesMaster),'TA.Activity_Id = AM.Activity_Id', array())
								->order('AM.Activity_Name ASC');
        $projActResult=$this->_db->fetchPairs($projActQry);
        return $projActResult;
    }
	
	/**
	 * Get requestId by employeeId , weekend
	 */
    public function getRequestId($empId, $weekEnd)
    {
        $weekEndingDate = date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($weekEnd)));
		
        $timesheetQry = $this->_db->select()
							->from(array('timesheet'=>$this->_ehrTables->empTimesheet),array('Request_Id'))
							->where('Employee_Id = ?', $empId)
							->where('Week_Ending_Date = ?', $weekEndingDate)
							->group('Request_Id');
        
		$getReqId = $this->_db->fetchOne($timesheetQry);
        
		return $getReqId;         
    }
	
	/**
	 * Get timesheet status by requestId
	 */
    public function getTimesheetStatus($reqId)
    {
        $timesheetQry = $this->_db->select()
        ->from(array('timesheet'=>$this->_ehrTables->empTimesheet),array('Status'=>'timesheet.Approval_Status',new Zend_Db_Expr('COUNT(timesheet.Request_Id) as tsCount')))
        ->joinLeft(array('ts_history'=>$this->_ehrTables->auditTimesheet), 'timesheet.Request_Id = ts_history.Request_Id',new Zend_Db_Expr('COUNT(Audit_Id) as historyCount'))
        ->where('timesheet.Request_Id = ?', $reqId);
        $getStatus = $this->_db->fetchAll($timesheetQry);
        return $getStatus;         
    }
	
	/**
	 * Get activity details by projectId
	 */
    public function selectActivity($projId, $isMobile = null, $weekEndDate = null)
    {
        $projId = (int)$projId;
		$weekStartDate = date('Y-m-d', strtotime("-6 day",strtotime($weekEndDate)));
		
		$qryActivities = $this->_db->select()->from(array('TA'=>$this->_ehrTables->timesheetActivity),array('Project_Activity_Id', 'AM.Activity_Name'))
								->joinInner(array('AM'=>$this->_ehrTables->activitiesMaster),'TA.Activity_Id = AM.Activity_Id', array())
								->where('Project_Id = ?', $projId)
								->order('AM.Activity_Name ASC');
									
		if(!empty($weekStartDate))
		{
			$qryCondition = $this->_db->quoteInto(new Zend_Db_Expr('( ? BETWEEN Activity_From AND Activity_To '), $weekStartDate);
			$qryCondition .= $this->_db->quoteInto(new Zend_Db_Expr(' or ? BETWEEN Activity_From AND Activity_To )'), $weekEndDate);

			$qryCondition .= ' or ('.$this->_db->quoteInto(" (Activity_From IS NULL or Activity_From = ?)", '0000-00-00'). ' AND ('.$this->_db->quoteInto(" Activity_To IS NULL or Activity_To = ?", '0000-00-00').'))';

			$qryActivities->where($qryCondition);
		}
		return $this->_db->fetchPairs($qryActivities);							
    }
	
	/**
	 * Check whether timesheet already exists or not by given data
	 */
    public function ckTimesheetStatus($requestId, $logEmpId, $status)
    {
        $qryCountEmployee = $this->_db->select()->from($this->_ehrTables->empTimesheet, new Zend_Db_Expr('count(Employee_Id)'))
        ->where('Approval_Status = ?', $status)->where('Approver_Id = ?', $logEmpId)->group('Request_Id')
        ->where('Request_Id = ?', $requestId);

        $rowCountEmployee = $this->_db->fetchOne($qryCountEmployee);
        return $rowCountEmployee;
    }
	
    public function timesheetEmployee($requestId)
    {
        $qryEmpId = $this->_db->select()->from($this->_ehrTables->empTimesheet, array('Employee_Id'))->group('Request_Id')->where('Request_Id = ?', $requestId);
        $qryEmpName = $this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal), array('Employee_Id','Employee_Name'=>new Zend_Db_Expr("CONCAT(Emp_First_Name, ' ', Emp_Last_Name)")))
        ->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.Employee_Id=P.Employee_Id', array())
        ->where('P.Employee_Id = ?', $qryEmpId)->where('P.Form_Status = 1')->where('J.Emp_Status Like ?', 'Active');
        $rowEmpName = $this->_db->fetchRow($qryEmpName);
        return $rowEmpName;
    }
    
    	
    public function statusReport($commentArray, $sessionId, $formName)
    {
        $transferStatus = array('Approval_Status'=>$commentArray['Approval_Status']);
        $updateStatus = $this->_db->update($this->_ehrTables->empTimesheet, $transferStatus, 'Request_Id = ' . $commentArray['Parent_Id']);
        $formId = $this->_dbComment->getFormId($formName);
		
	if(!empty($commentArray['Emp_Comment']))
        {
	    $commentArray['Form_Id']     = $formId;
	    $commentArray['Employee_Id'] = $sessionId;
	    $commentArray['Added_On']    = date('Y-m-d H:i:s');
	    $insertStatus = $this->_db->insert($this->_ehrTables->comment,$commentArray);
        }
		
	if($updateStatus)
        {
            $this->_ehrTables->trackEmpSystemAction('Update Timesheet Status - '.$commentArray['Parent_Id'], $sessionId);
            return true;
        }
	else
	{
            return false;
        }
    }

    /**
     *
     * This function is to delete timesheet.
     */
    public function deleteTimeSheetValue($requestId)
    {
        $where = array('Request_Id= ?' => (int)$requestId);
        $this->_db->delete($this->_ehrTables->empTimesheet,$where);
    }
	
    /**
     * This function is to add Timesheet history.
     */
    public function addTimesheetHistory($requestId,$totalHrsOld,$totalHrsNew,$logEmpId)
    {
        $data = array(
    			'Request_Id'  => $requestId,
    			'Total_Hrs_Old'  => $totalHrsOld,
    			'Total_Hrs_New'  => $totalHrsNew,
    			'Modified_By'  => $logEmpId,
    			'Modified_On'  => date('Y-m-d H:i:s')
        );
        $inserted = $this->_db->insert($this->_ehrTables->auditTimesheet,$data);
        
		return $inserted;
	}
    
	/**
     *
     * This function is to find timesheet totalhrs.
     *
     * */
    public function getTimeSheetTotalHrs($requestId)
    {
        $tsTotalHrsQry = $this->_db->select()
        ->from($this->_ehrTables->empTimesheet, array(new Zend_Db_Expr('sum(Day1+Day2+Day3+Day4+Day5+Day6+Day7) as total_hrs')))
        ->where('Request_Id = ?',$requestId);
        		
        $tsTotalHrsResult = $this->_db->fetchone($tsTotalHrsQry);
        return $tsTotalHrsResult;

    }

    /**
     *
     * Find Timesheet hours limit of a employee gradeId.
     *
     */
    public function getTimesheetHrsLimit($gradeId)
    {
        $srTsLmtQry = $this->_db->select()
        ->from(array('tsHrs'=>$this->_ehrTables->timesheetHrs),array('Regular_Hours', 'OverTime_Hours'))
        ->where('tsHrs.Grade_Id = ?', $gradeId);
        $srTsLmt = $this->_db->fetchRow($srTsLmtQry);
        return $srTsLmt;
    }
    
	/**
     *
     *This function is to find projectName of a projectId.
     *
     *
     *
     */
    public function getProjectName($pid)
    {
		//This function is to find projectName of a projectId.
        $srProjQry = $this->_db->select()
        ->from(array('project'=>$this->_ehrTables->project),array('Project_Name'))
        ->where('project.Project_Id = ?', $pid);
        $srProjresult = $this->_db->fetchAll($srProjQry);
        return $srProjresult;
    }

    /**
     *This function is to find activityName of a activityId.
     */
    public function getActivityName($aid)
    {
		return $this->_db->fetchAll($this->_db->select()->from(array('TA'=>$this->_ehrTables->timesheetActivity),array('AM.Activity_Name'))
						->joinInner(array('AM'=>$this->_ehrTables->activitiesMaster),'TA.Activity_Id = AM.Activity_Id', array())
						->where('TA.Project_Activity_Id = ?', $aid));
								
    }
    
	//to list the projects for the activities
    public function getprojectList()
    {
        $this->_db->setFetchMode(Zend_Db::FETCH_OBJ);
		
        return $this->_db->fetchPairs($this->_db->select()->from(array('proj'=>$this->_ehrTables->project),
																 array('proj.Project_Id', 'proj.Project_Name'))
										
										->joinInner(array('A'=>$this->_ehrTables->timesheetActivity), 'A.Project_Id=proj.Project_Id',
													array())
										
										->order('proj.Project_Name ASC'));
    }
    
	/**
     *This function is for obtained employee have timesheethrslimit grade or not.
     */
    public function getEmpTsGrade($employeeId)
    {
        $employeeId = (int)$employeeId;
		
        $srEmployeeQry = $this->_db->select()->distinct()
								->from(array('tsHrsLimit'=>$this->_ehrTables->timesheetHrs),array('tsHrsLimit.Grade_Id'))
								->joinInner(array('empDes'=>$this->_ehrTables->designation),'tsHrsLimit.Grade_Id=empDes.Grade_Id',
											array())
								->joinInner(array('empJob'=>$this->_ehrTables->empJob),'empDes.Designation_Id=empJob.Designation_Id',
											array())
								->where('empJob.Employee_Id = ?', $employeeId);
        $srEmployeeResult = $this->_db->fetchOne($srEmployeeQry);
        return $srEmployeeResult;
    }
    
	/**
     *
     * This function is to find Maximum requestId of a Timesheet.
     */
    public function maxRequestId()
    {
        //This function is to find Maximum requestId of a Timesheet.
        $qryRequest = $this->_db->select()->from($this->_ehrTables->empTimesheet, array(new Zend_Db_Expr('Max(Request_Id) as MaxId')));
        $rowRequest = $this->_db->fetchOne($qryRequest);
        return $rowRequest;
    }
	
	/**
     *
     * This function is to find timesheet dayhrs.
     *
     * */
    public function getTimeSheetDayHrs($timesheetDetails,$action=null)
    {
        $dayHrsQry = $this->_db->select()->from($this->_ehrTables->empTimesheet, array(new Zend_Db_Expr('sum(Day1) as day1'),
																					new Zend_Db_Expr('sum(Day2) as day2'),
																					new Zend_Db_Expr('sum(Day3) as day3'),
																					new Zend_Db_Expr('sum(Day4) as day4'),
																					new Zend_Db_Expr('sum(Day5) as day5'),
																					new Zend_Db_Expr('sum(Day6) as day6'),
																					new Zend_Db_Expr('sum(Day7) as day7')))
											->where('Employee_Id = ?',$timesheetDetails['Employee_Id'])
											->where('Week_Ending_Date = ?',$timesheetDetails['Week_Ending_Date'])
											->where('Timesheet_Type = ?',$timesheetDetails['Timesheet_Type'])
											->where('Approval_Status != ?','Rejected');
		if(!empty($action))
		{
			$dayHrsQry->where('Project_Activity_Id = ?',$timesheetDetails['Project_Activity_Id']);
		}									
        
        $dayHrsResult = $this->_db->fetchRow($dayHrsQry);
        return $dayHrsResult;

    }

	/**
     *
     *This function is to find employee timesheet details for a specified requestId.
     */
    public function getTimeSheetDetails($requestId)
    {
		$timesheetDetails = $this->_db->fetchAll($this->_db->select()->from(array('TS'=>$this->_ehrTables->empTimesheet),array('*',new Zend_Db_Expr("DATE_FORMAT(TS.Week_Ending_Date,'".$this->_orgDF['sql']."') as WeekEndingDate")))
								->where('TS.Request_Id = ?', $requestId));

		return $timesheetDetails;
    }

	public function getTimesheetSettings()
    {
        $projectSettings =$this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->projectSettings, array('*')));
        return $projectSettings;
    }

	public function __destruct()
    {
        
    }	
	
}