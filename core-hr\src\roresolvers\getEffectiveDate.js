// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../common/appconstants');
const moment = require('moment');


let organizationDbConnection;
module.exports.getEffectiveDate = async (parent, args, context, info) => {
    try {
        console.log("Inside getEffectiveDate function.")
        let employeeId = context.Employee_Id;
        let orgCode = context.Org_Code;
        let employeeIdArray = args.employeeIds
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, formName.teamSummary, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            return (
                organizationDbConnection(ehrTables.empJob + ' as EJ')
                    .select("EJ.User_Defined_EmpId as Employee_Id", "EJ.Date_Of_Join", organizationDbConnection.raw("MAX(STR_TO_DATE(SP.Salary_Month,'%m,%Y')) as maxSalaryMonthYear"), "EJ.Date_Of_Join", "EJ.Business_Unit_Id_Effective_Date as Business_Unit_Id_End_Date",  "EJ.Designation_Id_Effective_Date as Designation_Id_End_Date", "EJ.Department_Id_Effective_Date as Department_Id_End_Date", "EJ.Location_Id_Effective_Date as Location_Id_End_Date","EJ.Manager_Id_Effective_Date as Manager_Id_End_Date","EJ.EmpType_Id_Effective_Date as EmpType_Id_End_Date","EJ.Work_Schedule_Effective_Date as Work_Schedule_End_Date")
                    .leftJoin(ehrTables.salaryPayslip + " as SP", 'SP.Employee_Id', 'EJ.Employee_Id')
                    .whereIn('EJ.User_Defined_EmpId', employeeIdArray)
                    .groupBy('EJ.User_Defined_EmpId')
                    .then(async (data) => {                        
                        if(data && data.length){
                            // Create an object to store the pre-filled salary dates
                            const preFilledSalaryDates = {};
    
                            for (let i = 0; i < data.length; i++) {
                                const maxDate = data[i].maxSalaryMonthYear;
    
                                if (maxDate) {
                                    const month = maxDate.slice(5, 7);
                                    const year = maxDate.slice(0, 4);
    
                                    // Check if the pre-filled salary date for the month and year already exists
                                    if (preFilledSalaryDates[`${month}-${year}`]) {
                                        // If it exists, assign it directly to the Effective_Date property
                                        data[i].Effective_Date = preFilledSalaryDates[`${month}-${year}`];
                                    } else {
                                        // If it doesn't exist, call the getSalaryDay function
                                        const {Salary_Date, Last_SalaryDate } = await commonLib.func.getSalaryDay(
                                            orgCode,
                                            organizationDbConnection,
                                            month,
                                            year
                                        );
    
                                        // Store the retrieved salary date in the preFilledSalaryDates object
                                        preFilledSalaryDates[`${month}-${year}`] = Last_SalaryDate;
    
                                        // Assign the salary date to the Effective_Date property
                                        data[i].Effective_Date = moment(Last_SalaryDate).add(1, 'day').format('YYYY-MM-DD');
                                    }
                                }
                            }
    
                            //destroy the connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: "", message: "Employee payslip data retrieved successfully.", getEffectiveDate: data };
                        }else{
                            throw 'EDM0104'
                        }
                    })
                    .catch((err) => {
                        console.log('Error in getEffectiveDate .catch() block', err);
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        let errResult = commonLib.func.getError(err, 'EDM0104');
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        }
        else {
            throw '_DB0100';
        }

    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in getEffectiveDate function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'EDM0004');
        throw new ApolloError(errResult.message, errResult.code);
    }
}