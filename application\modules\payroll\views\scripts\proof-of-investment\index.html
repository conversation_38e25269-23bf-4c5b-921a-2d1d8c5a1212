<!DOCTYPE html>
<html>
	<head>
		<!-- common scripts -->
		<script src="https://cdn.jsdelivr.net/npm/vue@2/dist/vue.min.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/vuetify@2.2.3/dist/vuetify.min.js"></script>
		<script src="https://cdn.jsdelivr.net/gh/f/graphql.js@master/graphql.min.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/vue-cookies@1.6.1/vue-cookies.min.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/v-tooltip@2.0.2"></script>
		<script src="https://cdn.jsdelivr.net/npm/vee-validate@2.x/dist/vee-validate.js"></script>
		<!-- styles -->
		<link href="https://cdn.jsdelivr.net/npm/vuetify@2.2.11/dist/vuetify.min.css" rel="stylesheet" />
		<script>
		</script>
	</head>
	<body>
		<!--  html content -->
		<div id="proofOfInvestment" class="col-md-12 portlets p-10">
			<template>
				<v-app class="bg_grey_lighten1">
					<!-- desktop topbar design -->
					<v-card class="bg_grey_lighten1 hidden-sm-and-down" :class="topBarClass" flat height="4em">
						<v-toolbar color="grey lighten-5">
							<span class="padding-2em"></span>
							<v-tabs v-model="currentTab" background-color="#f9f9f9">
									<v-tab v-if="isTaxDeclarationHasAccess" href="#tab-1" class="active_tab_bg" @click="fnRedirect('tax-declarations')">
										<div style="text-transform: capitalize !important;" class="font-weight-bold">Tax Declarations</div>
									</v-tab>
									<v-tab v-if="isIncomeUS24HasAccess" href="#tab-2" class="active_tab_bg" @click="fnRedirect('income-under-section24')">
										<div style="text-transform: capitalize !important;" class="font-weight-bold">Income Under Section24</div>
									</v-tab>
									<v-tab href="#tab-3" class="active_tab_bg">
										<div style="text-transform: capitalize !important;" class="font-weight-bold">Proof Of Investment</div>
									</v-tab>
									<v-row style="margin-right: 100px;" class="topbar-right-action">
										<v-col v-if="showFilterOption" cols="12" class="d-flex justify-end align-center" style="margin-top: -10px;">
											<div class="secondaryColor p-r-10 font-weight-bold">Assessment Year:</div>
											<div class="m-r-10">
												<v-autocomplete
													v-model="selectedAssessmentYear"
													item-text="Year"
													item-value="Year"
													style="max-width: 70px;margin-top: 12px;"
													:items="assessmentYearList"
												></v-autocomplete>
											</div>
											
										</v-col>
									</v-row>
							</v-tabs>
						</v-toolbar>
					</v-card>
					<!-- mobile topbar design -->
					<v-card class="bg_grey_lighten1 hidden-md-and-up" :class="topBarClass" flat height="4em">
						<v-toolbar color="grey lighten-5">
							<v-tabs v-model="currentTab" centered show-arrows>
								<v-tab v-if="isTaxDeclarationHasAccess" href="#tab-1" class="active_tab_bg" @click="fnRedirect('tax-declarations')">
									<div style="text-transform: capitalize !important;" class="font-weight-bold">Tax Declarations</div>
								</v-tab>
								<v-tab v-if="isIncomeUS24HasAccess" href="#tab-2" class="active_tab_bg" @click="fnRedirect('income-under-section24')">
									<div style="text-transform: capitalize !important;" class="font-weight-bold">Income Under Section24</div>
								</v-tab>
								<v-tab href="#tab-3" class="active_tab_bg">
									<div style="text-transform: capitalize !important;" class="font-weight-bold">Proof Of Investment</div>
								</v-tab>
							</v-tabs>
						</v-toolbar>
					</v-card>
					<!-- mobile footer design with filter-->
					<v-bottom-navigation fixed class="hidden-md-and-up" color="teal" elevation="15" v-if="showFilterOption" >
						<v-row class="d-flex justify-center">
							<v-col cols="12" class='d-flex align-center justify-center' style="margin-top: -20px">
								<div class="secondaryColor p-r-10 font-weight-bold" style="padding: 10px;">Assessment Year:</div>
								<div class="m-r-10">
									<v-autocomplete
										v-model="selectedAssessmentYear"
										item-text="Year"
										item-value="Year"
										style="max-width: 100px;margin-top: 10px;"
										:items="assessmentYearList"
									></v-autocomplete>
								</div>
							</v-col>
						</v-row>
					</v-bottom-navigation>
					<section v-if="!isAccessDenied && !poiFetchError && poiStatisticsRenderCount >=1 ">
						<!-- show statistics page -->
						<view-poi-statistics 
							:api-headers = "apiHeaders"
							:org-code ="orgCode"
							:employee-id ="employeeId"
							:tax-declaration-base-url = "taxDeclarationBaseUrl"
							:ats-base-url = "atsBaseURL"
							:selected-assessment-year ="selectedAssessmentYear"
							:current-assessment-year = "currentAssessmentYear"
							:income-source-types = "incomeSourceTypes"
							:static-data = "staticData"
							:roles-response = "rolesResponse"
							:form-source = "formSource"
							:poi-statistics-render-count = "poiStatisticsRenderCount"
							:base-path = "basePath()"
							:base-url = "baseUrl()"
							:tax-declaration-graphql = "graphQl()"
							:tax-statutory-graphql ="taxStatutoryGraphql()"
							:ats-graphql = "atsGraphQl()"
							:success-type="successType"
                    		:success-data = "successData"
							:retrieve-statistics = "retrieveStatistics"
							:window-width="windowWidth"
							@show-filter="showFilterOption = $event"
							@open-close-view-modal="openCloseViewModal($event)"
							@handle-error="handleError($event)"
							@handle-document-error="handleDocumentError($event)"
							@handle-poi-error = "handlePOIError($event)"
							@show-hide-statistics ="showHideStatistics($event)"
							@handle-warning-msg="handleWarningMessages($event)"
							@handle-success="handleSuccessMessages($event)">
						</view-poi-statistics>
					</section>
					<!--Access Denied-->
					<access-denied-screen v-if="isAccessDenied"></access-denied-screen> 
					<!--Fetching Error-->
					<div v-if="poiFetchError">
							<fetching-error-screen 
								button-text="Retry" 
								:content="errorContent" 
								:main-title="errorTitle" 
								image-name="initial-fetch-error-image" 
								@button-click="retryFetching"></fetching-error-screen>
					</div>
					<!--custom snack bars for showing success and error messages -->
					<custom-snack-bar v-if="snackbar"
						:show-snack-bar="snackbar" 
						:snack-bar-msg="snackBarMsg" 
						show-emoji="false" 
						emoji=""
						:snack-bar-type="snackBarType"
						:snack-bar-position-top = "true"
						@close-snack-bar="snackbar = false">
					</custom-snack-bar>		
					<!-- custom loading -->
					<custom-loading-screen v-if="loadingScreen"></custom-loading-screen>
				</v-app>
			</template>
		</div>


		<!-- script content -->
		<script>
			let primaryColor, secondaryColor;
			if (!localStorage.getItem("brand_color")) {
				const { Primary_Color, Secondary_Color} = JSON.parse(localStorage.getItem("brand_color"));
				primaryColor = Primary_Color;
				secondaryColor = Secondary_Color;
			} else {
				primaryColor = '#260029';
				secondaryColor = '#ec407a';
			}
			// vue instance declarations
			var app = new Vue({
				el: '#proofOfInvestment',
				vuetify: new Vuetify(
					{
						theme: {
							options: {
								customProperties: true,
							},
							themes: {
								light: {
									primary: primaryColor,
									secondary: secondaryColor,
									grey: '#9E9E9E',
									green : '#41ae57',
									blue : '#63759f'
								}
							}
						}
					}
				),
				data() {
					return {
						taxDeclarationBaseUrl:  'https://api.'+localStorage.getItem('domain')+'/employee-taxation/graphql',
						taxStatutoryBaseUrl:  'https://api.'+localStorage.getItem('domain')+'/taxAndStatutory/rographql',
						atsBaseURL : 'https://api.'+localStorage.getItem('domain')+'/ats/graphql',
						partnerid: $cookies.get("partnerid"),
						dCode: $cookies.get("d_code"),
						bCode: $cookies.get("b_code"),
						employeeId: parseInt(localStorage.getItem('LoginEmpId'), 10),
						userIp : '',
						rolesResponse : {},
						isIncomeUS24HasAccess: false,
						isTaxDeclarationHasAccess: false,
						loadingScreen: false,
						windowWidth: 0,
						currentTab:"tab-3",
						formSource : 'POI',
						poiStatisticsRenderCount : 0,


						// initial check data
						poiFetchError : false,
						poiStatisticsError : false,
						retrieveStatistics : false,
						fetchErrorAccessRights : false,
						topBarClass : 'poi-top-bar',
						isAccessDenied : false,

						//snackbar props
						snackbar :false,
						snackBarType : '',
						snackBarMsg :'',
						
						// POI data
						selectedAssessmentYear : 0,
						assessmentYearList: [],
						currentAssessmentYear: 0,
						incomeSourceTypes : [],
						staticData : '',
						showFilterOption : true,
						successType: '',
        				successData: {}
					}
				},
				computed: {
					isLocalEnv() {
						let currentUrl = window.location.href;
						if (
							parseInt(localStorage.getItem("isProduction"), 10) === 0 ||
							currentUrl.includes("hrapponline")
						) {
							return true;
						} else {
							return false;
						}
					},
					//to get orgCode dynamically from the current url
					orgCode() {
						if (this.isLocalEnv) {
							return "happy"; // local db connection
						} else {
							let oCode1 = localStorage.getItem("orgCode");
							if (oCode1) {
								return oCode1;
							} else {
								let url = window.location.href;
								let urlNoProtocol = url.replace(/^https?:\/\//i, "");
								let oCode = urlNoProtocol.split(".");
								oCode = oCode[0];
								return oCode;
							}
						}
					},
					apiHeaders() {
						let authorizationHeader = $cookies.get("accessToken") ? $cookies.get("accessToken") : null;
						let refreshTokenHeader = $cookies.get("refreshToken") ? $cookies.get("refreshToken") : null;
						return {
							'Content-Type': 'application/json',
							org_code: this.orgCode,
							Authorization: authorizationHeader,
							refresh_token: refreshTokenHeader,
							user_ip: this.userIp,
							partnerid: this.partnerid ? this.partnerid : "-",
							additional_headers: JSON.stringify(
								{
									org_code: this.orgCode,
									Authorization: authorizationHeader,
									refresh_token: refreshTokenHeader,
									user_ip: this.ipAddress,
									partnerid: this.partnerid ? this.partnerid : "-",
									d_code: this.dCode,
									b_code: this.bCode,
								}
							)
						}					
					},
				},
				created () {
					// function to get ip
					try{
						axios.get('https://api.ipify.org?format=json').then(response => { 
							this.userIp = response.data.ip;
						}).catch(error => {
							/* If the IP address API is not available, API URL is wrong or internet connection is not available,
							then the error.readyState will be 0 or 4  */
							if((error.readyState === 0 || error.readyState === 4) && ipAddressRestriction == 1) {
								this.snackBarMsg = "Unable to get the IP address. Please contact system Administrator.";
								this.snackBarType = "warning";
								this.snackbar = true;
							} else {
								this.userIp = "IP Blocked by user";
							}
						})
					}catch{
						this.userIp = "";
					}
					window.$cookies.set("userIpAddress", this.ipAddress);
				},
				// common error handling function
				errorCaptured(err, vm, info) {
					this.poiFetchError = true;
					this.showFilterOption = false;
					this.errorTitle = "Oops";
					this.errorContent = "This issue seems to be from our side. Would you mind trying it after some time or talk to your administrator?"									
					return false;
				},
				mounted() {
					
					this.$nextTick(function () {
						window.addEventListener('resize', this.getWindowWidth);
						//Init
						this.getWindowWidth();
					});

					setTimeout(()=>{
						this.fetchIncomeUS24FormAccess();
						this.fetchTaxDeclarationsFormAccess();
						this.fnCheckIsAdmin();
					},2000)	
					
				},
				beforeDestroy() {
					window.removeEventListener('resize', this.getWindowWidth);
				},
				methods: {
					// function to redirect
					fnRedirect(path){
						this.loadingScreen = true;
						window.location.href = this.baseUrl() + "payroll/" + path;
					},
					getWindowWidth(event) {
						this.windowWidth = document.documentElement.clientWidth;
					},
					graphQl(){
						return graphql(this.taxDeclarationBaseUrl, {
							method: 'POST',
							headers: this.apiHeaders,
							asJSON: true
						});
					},
					taxStatutoryGraphql() {
						return graphql(this.taxStatutoryBaseUrl, {
							method: 'POST',
							headers: this.apiHeaders,
							asJSON: true
						});
					},

					atsGraphQl(){
						return graphql(this.atsBaseURL, {
							method: 'POST',
							headers: this.apiHeaders,
							asJSON: true
						});
					},
					
					basePath() {
						if (localStorage.getItem('production') == 0) {
							return '/hrapponline/'
						} else {
							return '/'
						}
					},

					baseUrl(){
						var pathParts = location.pathname.split('/');
						if (localStorage.getItem('production') == 0) {
							var url = location.origin + '/' + pathParts[1].trim('/') + '/'; // http://localhost/hrapponline/
						} else {
							var url = location.origin + '/'; // http://subdomain.hrapp.co
						}
						return url;
					},

					// check income u/s24 form access rights
					fetchIncomeUS24FormAccess() {
						var self = this;
						var atsGraphQl = self.atsGraphQl();
						var accessRights = atsGraphQl(`mutation(
						$formName: String,
						$employeeId: Int!) {
							getAccessRights
								(
									formName: $formName,
									employeeId:$employeeId
								) 
								{
									errorCode message rights {
										Role_View Role_Add Role_Update Role_Delete Role_Optional_Choice Role_Hr_Group Role_Payroll_Group Is_Manager
									}
								}
							}
						`);
						accessRights({
							formName: 'Income Under Section24',
							employeeId: self.employeeId
						})
						.then(function (response) {
							if (response) {
								var response = response.getAccessRights.rights;
								if(response.Role_View) {
									self.isIncomeUS24HasAccess = true;
								}
							}
						})
					},
					// check tax-declarations form access rights
					fetchTaxDeclarationsFormAccess() {
						var self = this;
						var atsGraphQl = self.atsGraphQl();
						var accessRights = atsGraphQl(`mutation(
						$formName: String,
						$employeeId: Int!) {
							getAccessRights
								(
									formName: $formName,
									employeeId:$employeeId
								) 
								{
									errorCode message rights {
										Role_View Role_Add Role_Update Role_Delete Role_Optional_Choice Role_Hr_Group Role_Payroll_Group Is_Manager
									}
								}
							}
						`);
						accessRights({
							formName: 'Tax Declarations',
							employeeId: self.employeeId
						})
						.then(function (response) {
							if (response) {
								var response = response.getAccessRights.rights;
								if(response.Role_View) {
									self.isTaxDeclarationHasAccess = true;
								}
							}
						})
					},

					fnCheckIsAdmin() {
						try {
							var self = this;
							self.loadingScreen = true;
							self.fetchErrorAccessRights = false;
							var atsGraphQl = self.atsGraphQl();
							var accessRights = atsGraphQl(`mutation(
							$formName: String,
							$employeeId: Int!) {
								getAccessRights
									(
										formName: $formName,
										employeeId:$employeeId
									) 
									{
										errorCode message rights {
											Role_View Role_Add Role_Update Role_Delete Role_Optional_Choice Role_Hr_Group Role_Payroll_Group Is_Manager
										}
									}
								}
							`);
							accessRights({
								formName: 'Super Admin',
								employeeId: self.employeeId
							})
							.then(function (response) {
								if (response) {
									var response = response.getAccessRights.rights;
									if(response.Role_Optional_Choice) {
										self.fnCheckAccessRights();
									}
									else {
										self.fnCheckPayrollAdmin();
									}
								}
							})
							.catch(function () {
								self.fnCheckPayrollAdmin();
							});
						}
						catch {
							self.fnCheckPayrollAdmin();
						}
					},

					fnCheckPayrollAdmin() {
						try {
							var self = this;
							self.loadingScreen = true;
							self.fetchErrorAccessRights = false;
							var atsGraphQl = self.atsGraphQl();
							var accessRights = atsGraphQl(`mutation(
							$formName: String,
							$employeeId: Int!) {
								getAccessRights
									(
										formName: $formName,
										employeeId:$employeeId
									) 
									{
										errorCode message rights {
											Role_View Role_Add Role_Update Role_Delete Role_Optional_Choice Role_Hr_Group Role_Payroll_Group Is_Manager
										}
									}
								}
							`);
							accessRights({
								formName: 'Payroll Admin',
								employeeId: self.employeeId
							})
							.then(function (response) {
								if (response) {
									var response = response.getAccessRights.rights;
									if(response.Role_Update) {
										self.fnCheckAccessRights();
									}
									else {
										self.isAccessDenied = true; //set access denied
										self.showFilterOption = false;
										self.loadingScreen = false;
									}
								}
							})
							.catch(function (accessRightsFetchError) {
								self.fetchErrorAccessRights = true;
								self.handleError(accessRightsFetchError,1);
							});
						}
						catch {
							self.fetchErrorAccessRights = true;
							self.handleError("",1);
						}
					},

					fnCheckAccessRights() {
						try {
							var self = this;
							self.loadingScreen = true;
							self.fetchErrorAccessRights = false;
							var atsGraphQl = self.atsGraphQl();
							var accessRights = atsGraphQl(`mutation(
							$formName: String,
							$employeeId: Int!) {
								getAccessRights
									(
										formName: $formName,
										employeeId:$employeeId
									) 
									{
										errorCode message rights {
											Role_View Role_Add Role_Update Role_Delete Role_Optional_Choice Role_Hr_Group Role_Payroll_Group Is_Manager
										}
									}
								}
							`);
							accessRights({
								formName: 'Proof Of Investment',
								employeeId: self.employeeId
							})
							.then(function (response) {
								if (response) {
									self.loadingScreen = false;

									// rights retrieved successfully
									var response = response.getAccessRights.rights;
									self.rolesResponse = response;
									// check if the user has view access for Whitelisted IP
									if(response.Role_View) {
										self.isAccessDenied = false;
										self.poiFetchError = false;
										self.showFilterOption = true;

										// function to retrieve static data 
										self.fnRetrieveStaticData();
									}
									else {
										self.isAccessDenied = true; //set access denied
										self.showFilterOption = false;
									}
								}
							})
							.catch(function (accessRightsFetchError) {
								self.fetchErrorAccessRights = true;
								self.handleError(accessRightsFetchError,1);
							});
						}
						catch(accessRightsFetchError) {
							self.fetchErrorAccessRights = true;
							self.handleError(accessRightsFetchError,1);
						}
					},
					handleError(err,isListPage = 0,action='') {
						var self = this;
						self.loadingScreen = false;
						// handle BE error codes
						if (err && err[0]) {
							// error returned from backend
							var error = JSON.parse(err[0].message);
							var errorCode = error.errorCode;
							var errorMessage = error.message;
							switch (errorCode) {
								// technical errors
								case 705:
                    			case 706:
								case "_DB0000":
								case "PR0113":
									if(isListPage){
										self.showFilterOption = false;
										self.poiFetchError = true;
										self.errorTitle = "Oops";
										self.errorContent = "It’s us ! There seems to be some technical difficulties while fetching the POI statistics. Please try after some time."									
									}
									else{
										self.snackbar = true;
										self.snackBarType = "warning";
										self.snackBarMsg = "Oops ! Sorry, This issue seems to be from our side. Would you mind trying it after some time or talk to your administrator ?"
									}
									break;
								
								// access denied
								case 752 :
									self.isAccessDenied = true;
									self.showFilterOption = false;
									break;
								case 'DB0100' : 
								case '_DB0100' :
									if(isListPage) {
										self.isAccessDenied = true;
										self.showFilterOption = false;
									}
									else {
										self.snackbar = true;
										self.snackBarType = "warning";
										self.snackBarMsg = "Sorry, you dont have access to view the declaration. Please contact system administrator."
									}
									break;
								
								// approval actions
								case '_DB0105':	
									self.snackbar = true;
									self.snackBarType = "warning";
									self.snackBarMsg = "Sorry, you don't have rights for any approval actions. Please contact system administrator."
									break;
								case 'PR0013':	
								case 'PR0117' : //need to confirm message	
									self.snackbar = true;
									self.snackBarType = "warning";
									self.snackBarMsg = "Declaration status updated successfully but some of the declaration records are already updated."
									break;
								case 'PR0017':		
									self.snackbar = true;
									self.snackBarType = "warning";
									self.snackBarMsg = "Declaration status not updated as the records are opened or already updated in the same or some other user session."
									break;
								case 'PR0111':		
									self.snackbar = true;
									self.snackBarType = "warning";
									self.snackBarMsg = "Assessment year is closed and hence no approvals are allowed."
									break;	
								case 'PR0115':	
									self.snackbar = true;
									self.snackBarType = "warning";
									self.snackBarMsg = "Something went wrong while processing the declaration approval process."
									break;
								case 'PR0116':
									self.snackbar = true;
									self.snackBarType = "warning";
									self.snackBarMsg = "Declaration status updated successfully. But unable to notify employee(s)."
									break;	
									
								// notification errors
								case "PR0014":
								case "PR0114":
									self.snackbar = true;
									self.snackBarType = "warning";
									self.snackBarMsg = "Sorry, Unable to notify employee(s). Please try after sometime."
									break;	
								case 'PR0015':	
									self.snackbar = true;
									self.snackBarType = "warning";
									self.snackBarMsg = "There are no eligible employees in selected declaration status to send notification."
									break;
								case 'PR0016':	
									self.snackbar = true;
									self.snackBarType = "warning";
									self.snackBarMsg = "No employees are having an email address."
									break;

								// functional errors 
								case 751 :
								case "PR0103":
								case "PR0003":
								case "PR0101":
								case "PR0001":
								case 'PR0006' :
								case 'PR0106' :
								case '_DB0001' :
								case '_DB0002' :
								case '_DB0104' :
								case '_UH0001' :
								case "SGE0110": // Error while getting the field force details.
								case "SGE0111": // Error while getting the service provider employeeIds.
								default :
									if(isListPage) {
										self.showFilterOption = false;
										self.poiFetchError = true;
										self.errorTitle = "Oops";
										self.errorContent = "Something went wrong while fetching the POI statistics. Please try after some time."									
									}
									else {
										self.snackbar = true;
										self.snackBarType = "warning";
										self.snackBarMsg = "Something went wrong. Please contact system administrator."
									}
									break;
							}
						}
						else {
							// if error in list page, show the fetch error screen
							if(isListPage) {
								self.showFilterOption = false;
								self.poiFetchError = true;
								self.errorTitle = 'Oops',
								self.errorContent = 'Something went wrong while fetching the POI statistics. Please try after some time.'
							}							
							else {
								self.snackbar = true;
								self.snackBarType = "warning";
								self.snackBarMsg = "Something went wrong. Please contact system administrator."
							}
						}
					},

					// handle Error from POI
					handlePOIError(error) {
						this.poiStatisticsError = true;
						this.retrieveStatistics = false;
						this.handleError(error,1);
					},
					// handle error during doc view
					handleDocumentError(fetchPresignedUrlError) {
						this.documentError = true;
						this.topBarClass = 'income-under-top-bar';
						this.handleError(fetchPresignedUrlError,0)
					},
					// retry fetching access rights/statistics data page from error page
					retryFetching() {
						// check if the fetch error while retrieving access rights/ statistics data
						if(this.fetchErrorAccessRights) 
						{
							this.fnCheckIsAdmin();
						}
						else if(this.poiStatisticsError) {
							this.poiFetchError = false;
							this.showFilterOption = true;
							this.retrieveStatistics = true;
						}
						else {
							this.fnRetrieveStaticData();
						}
					},

					// retrieve the static data for the Income US 24
					fnRetrieveStaticData() {
						try {
							var self = this;
							self.loadingScreen = true;
							var taxdeclarationGraphql = self.graphQl();

							var staticDataList = taxdeclarationGraphql.query(`getHousePropertyStaticData { getHousePropertyStaticData { errorCode message housePropertyStaticData}}`);
							staticDataList().then(function (response) {
								if(response.getHousePropertyStaticData.housePropertyStaticData){

										self.staticData = JSON.parse(response.getHousePropertyStaticData.housePropertyStaticData);
										// current assessment year
										self.currentAssessmentYear = self.staticData.currentAssessmentYear;
										self.selectedAssessmentYear = self.staticData.currentAssessmentYear;
										self.incomeSourceTypes = self.staticData.incomeSourceTypes;

									    // set the assessment year in dropdown 
										self.assessmentYearList = self.staticData.minAndMaxAssessmentYear;

										self.poiStatisticsRenderCount++;
										self.loadingScreen = false;
								}else{
									self.poiFetchError = true;
									self.showFilterOption = false;
									self.handleError(null, 1);
								}
							})
							.catch(function (staticDataFetchError) {
								self.poiFetchError = true;
								self.showFilterOption = false;
								self.handleError(staticDataFetchError,1);
							});
						}
						catch(staticDataFetchError) {
							self.poiFetchError = true;
							self.showFilterOption = false;
							self.handleError(staticDataFetchError, 1);
						}
					},	

					// change the class of the top bar
					openCloseViewModal(value) {
						if(value) {
							this.topBarClass = 'income-under-top-bar-index';

						}
						else {
							this.topBarClass = 'income-under-top-bar';
						}
					},
					handleWarningMessages(message){
						this.snackBarMsg = message;
						this.snackBarType = "warning";
						this.snackbar = true;
					},
					// handle success message while approval actions triggered
					handleSuccessMessages(approvalSuccess){
						this.snackBarMsg = approvalSuccess[0];
						this.snackBarType = "success";
						this.snackbar = true;
						if(approvalSuccess[1]){
							this.successType = approvalSuccess[1];
							this.successData = {
								'isApproval' : 1
							};
						}
					},


					// to show/hide the assessment year filter
					showHideStatistics(value) {
						if(value) {
							this.showFilterOption = true; 
						}else {
							this.showFilterOption = false;
						}
					}
				}
			})
		</script>
	</body>
</html>

<!--  own styles -->
<style scoped lang="css">
	.income-under-top-bar-index {
		height: 5em;
		position: fixed !important;
		width: 100% !important;
		top : 50px;
	}
	.income-under-top-bar {
		height: 5em;
		position: fixed !important;
		width: 100% !important;
		z-index: 2000;
		top : 50px;
	}
	.poi-top-bar-index {
		height: 5em;
		position: fixed !important;
		width: 100% !important;
		top : 50px;
	}
	.poi-top-bar {
		height: 5em;
		position: fixed !important;
		width: 100% !important;
		z-index: 2000;
		top : 50px;
	}
	.active_tab_bg {
		background:#f9f9f9 !important;
	}
	.topbar-right-action{
		margin-right: 100px;
	}
	.v-data-table thead tr:last-child th{
		font-size: 15px;
	}
	.v-data-table tbody tr{
		cursor: pointer;
	}
	.v-application--is-ltr .v-chip .v-chip__close.v-icon.v-icon--right{
		margin-right: 0px !important;
	}			
	@media screen and (max-width:1024px){
		.topbar-right-action{
			margin-right: 0px !important;
		}
	}	
</style>
