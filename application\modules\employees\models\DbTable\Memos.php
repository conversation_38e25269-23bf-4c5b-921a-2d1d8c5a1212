<?php
//=========================================================================================
//=========================================================================================
/* Program : Memos.php											   				         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MQL Query to retrive, add, update and delete employee memos.		     *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        17-Aug-2013    Narmadha		          Initial Version        	         *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Dhanabal                Changes in file for mobile app     *
 *                                                    1.Extra fields are added in        *
 *                                                    field list of list query.          *
 *                                                                                       *
 *  1.5        09-Feb-2016    Prasanth               Changed in file for Bootstrap       *
 *                                                                                       */
//=========================================================================================
//=========================================================================================
class Employees_Model_DbTable_Memos extends Zend_Db_Table_Abstract
{
    protected $_db          = null;
    protected $_ehrTables   = null;
    protected $_orgDF       = null;
	protected $_dbCommonFun = null;
	
    public function init()
    {
        $this->_ehrTables   = new Application_Model_DbTable_Ehr();
        $this->_db          = Zend_Registry::get('subHrapp');
        $this->_orgDF       = $this->_ehrTables->orgDateformat();
		$this->_dbCommonFun = new Application_Model_DbTable_CommonFunction();
    }
	
	/**
	 * Get memo details to show in a grid
	 */
    public function listMemos ($page, $rows, $sortField, $sortOrder, $searchAll, $searchArr, $memoUserArr)
    {
        
        $empFirstName  = $searchArr['EmpFirstName'];
		$memoBeginDate = $searchArr['MemoBeginDate'];
		$memoEndDate   = $searchArr['MemoEndDate'];
		
        /**
		 *	Sorting columns based on display column order in grid
		*/
		switch ($sortField)
		{
			case 1: $sortField = 'EJ.User_Defined_EmpId'; break;
            case 2: $sortField = 'P.Emp_First_Name'; break;
			case 3: $sortField = 'M.Memo_Date'; break;			
		}
		
        $qryMemos = $this->_db->select()
								->from(array('M'=>$this->_ehrTables->memos),
									   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS M.Memo_Id as Count'),
											 'M.Memo_Id','M.MemoTo_Id', 'M.Added_By', 'M.Description', 'M.Memo_Date',
											 new Zend_Db_Expr("DATE_FORMAT(M.Added_On,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
											 new Zend_Db_Expr("DATE_FORMAT(M.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On"),
											 new Zend_Db_Expr("DATE_FORMAT(M.Memo_Date,'".$this->_orgDF['sql']."') as MemoDate"),
											 'Log_Id'=>new Zend_Db_Expr($memoUserArr['LogId']),
											 'DT_RowClass' => new Zend_Db_Expr('"memos"'),
											 'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', M.Memo_Id)")))
								
								->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id = M.MemoTo_Id',
											array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name, ' ', P.Emp_Last_Name)")))
                                
                                ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'P.Employee_Id=EJ.Employee_Id',
                                            array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN P.Employee_Id ELSE EJ.User_Defined_EmpId END')))
								
								->joinInner(array('emp1'=>$this->_ehrTables->empPersonal),'emp1.Employee_Id = M.Added_By',
											array(new Zend_Db_Expr("CONCAT(emp1.Emp_First_Name, ' ', emp1.Emp_Last_Name) as Added_By_Name")))
								
								->joinLeft(array('emp2'=>$this->_ehrTables->empPersonal),'emp2.Employee_Id = M.Updated_By',
										   array(new Zend_Db_Expr("CONCAT(emp2.Emp_First_Name, ' ', emp2.Emp_Last_Name) as Updated_By_Name")))
								
								->order("$sortField $sortOrder")
								->limit($rows, $page);
        
        if (empty($memoUserArr['Admin']))
        {
            $employeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
												  ->where('Manager_Id = ?', $memoUserArr['LogId']));
			
            if ( $memoUserArr['Is_Manager'] == 1 && !empty($employeeId)) {
                $qryMemos
					->where('M.MemoTo_Id = :EmpId or M.MemoTo_Id IN (?)', $employeeId)
					->bind(array('EmpId'=>$memoUserArr['LogId']));
            }
            else
			{
                $qryMemos->where('M.MemoTo_Id = ?', $memoUserArr['LogId']);
            }
        }
        	
        /**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			//$conditions = $this->_db->quoteInto('P.Emp_First_Name Like ?', "%$searchAll%");
			//$conditions .= $this->_db->quoteInto('or P.Emp_Last_Name Like ?', "%$searchAll%");
            $conditions = $this->_db->quoteInto(new Zend_Db_Expr('Concat(P.Emp_First_Name," ",P.Emp_Last_Name) Like ?'),"%$searchAll%");
			$conditions .= $this->_db->quoteInto('or M.Memo_Date Like ?', "%$searchAll%");
            $conditions .= $this->_db->quoteInto('or EJ.User_Defined_EmpId Like ?', "%$searchAll%");
			
			$qryMemos->where($conditions);
		}
		
        
		/* Filter for Employee Name */
		if ( ! empty($empFirstName) /*&& preg_match('/^[a-zA-Z]+$/', $empFirstName)*/ )
		{
            $qryMemos->where($this->_db->quoteInto(new Zend_Db_Expr('Concat(P.Emp_First_Name," ",P.Emp_Last_Name) Like ?'),"%$empFirstName%"));
		}
		
		/* Filter for Memo Date */
		if ($memoBeginDate != '') {
			$qryMemos->where($this->_db->quoteInto('M.Memo_Date >= ?', $memoBeginDate));
		}
		
		if ($memoEndDate != '') {
			$qryMemos->where($this->_db->quoteInto('M.Memo_Date <= ?', $memoEndDate));
		}
		
		$qryMemos = $this->_dbCommonFun->getDivisionDetails($qryMemos,'EJ.Department_Id');

		/**
		 * SQL queries
		 * Get data to display
		*/
		$memos = $this->_db->fetchAll($qryMemos);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		/* Total data set length */
		$qryMemosCnt = $this->_db->select()->from($this->_ehrTables->memos, new Zend_Db_Expr('COUNT(Memo_Id)'));
		
		if (empty($memoUserArr['Admin']))
        {
            $employeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
												  ->where('Manager_Id = ?', $memoUserArr['LogId']));
            
			if ( $memoUserArr['Is_Manager'] == 1 && !empty($employeeId)) {
                $qryMemosCnt
					->where('W.MemoTo_Id = :EmpId or W.MemoTo_Id IN (?)', $employeeId)
					->bind(array('EmpId' => $memoUserArr['LogId']));
            }
            else
			{
                $qryMemosCnt->where('MemoTo_Id = ?', $memoUserArr['LogId']);
            }
        }
		
        $iTotal = count($memos);
		
		/**
		 * Output array with Json encode
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $memos);
    }

	/**
	 *	add/update Memo Details
	*/
	public function updateMemo ($memoData, $memoId, $sessionId,$customFormName)
	{
		/**
		 *	Check Memo is already exists or not in database
		*/
		$qryMemo = $this->_db->select()
									->from($this->_ehrTables->memos, array(new Zend_Db_Expr('COUNT(Memo_Id)')))
									->where('MemoTo_Id = ?', $memoData['MemoTo_Id'])
									->where('Memo_Date = ?', $memoData['Memo_Date']);
        
		if (!empty($memoId))
        {
            $qryMemo->where('Memo_Id != ?', $memoId);
        }
        
		$checkExists = $this->_db->fetchOne($qryMemo);
		
		/**
		 *	Check memo details if exists return error message or process add/update function
		*/
		if ($checkExists == 0)
		{
			/**
			 *	Check memo id is greater than zero for run update process
			*/
			if ($memoId > 0)
			{
				$action = 'Edit';
				
				$memoData['Lock_Flag'] = 0;
				$memoData['Updated_By'] = $sessionId;
				$memoData['Updated_On'] = date('Y-m-d H:i:s');
				
				$updated = $this->_db->update($this->_ehrTables->memos, $memoData, array('Memo_Id = ?'=>$memoId));
			}
			/**
			 *	If memo id is empty then we process insertion
			*/
			else
			{
				$action = 'Add';
				
				$memoData['Added_By'] = $sessionId;
				$memoData['Added_On'] = date('Y-m-d H:i:s');
				
				$updated = $this->_db->insert($this->_ehrTables->memos, $memoData);
				
				if ($updated)
					$memoId = $this->_db->lastInsertId();
			}
			
			/**
			 *	this function will handle
			 *		update system log function
			 *		clear submit lock fucntion
			 *		return success/failure array
			*/
			$result = $this->_dbCommonFun->updateResult (array('updated'        => $updated,
															   'action'         => $action,
															   'trackingColumn' => $memoId,
															   'formName'       => $customFormName,
															   'sessionId'      => $sessionId,
															   'tableName'      => $this->_ehrTables->memos));
			
			if ($result['success'])
			{
				if ($action == 'Add')
				{
					return array('success' => true, 'msg' => $customFormName.' added successfully', 'type' => 'success');
				}
				else
				{
					return array('success' => true, 'msg' => $customFormName.' updated successfully', 'type' => 'success');
				}
			}
			else
			{
				return $result;
			}
		}
		else
		{
			return array('success' => false, 'msg' => $customFormName.' already exists', 'type' => 'info');
		}
	}
	
	/**
	 *	Delete Memo details
	 */
    public function deleteMemo ($memoId, $sessionId,$customFormName)
    {
		$deleted = 0;
		
		/**
		 *	Get Lock Flag for memo id
		*/
        $memoLock = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->memos, array('Lock_Flag'))
											->where('Memo_Id = ?', $memoId));
		
		/**
		 *	Check Lock flag is empty or not.
		*/
		if ($memoLock == 0)
		{
			$deleted = $this->_db->delete($this->_ehrTables->memos, 'Memo_Id='.$memoId);
		}
		
		/**
		 *	delete activity for common function
		 *		1)check lock is exist or not.
		 *			If lock is exist then show error message like employee is open record for update.
		 *		2)If No lockflag then process delete activity
		 *		3)Update delete activity in system log
		 *		4)return success/failure message
		*/
		return $this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
														'tableName'      => $this->_ehrTables->memos,
														'lockFlag'       => $memoLock,
														'formName'       => $customFormName,
														'trackingColumn' => $memoId,
														'sessionId'      => $sessionId));
    }

	public function __destruct()
    {
        
    }
	
}