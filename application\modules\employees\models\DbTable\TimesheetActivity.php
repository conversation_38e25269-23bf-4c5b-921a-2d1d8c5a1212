<?php
//=========================================================================================
//=========================================================================================
/* Program : TimesheetActivity.php										   		         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MQL Query to retrive, add, update timesheet activity				     *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Shobana            	  Initial Version        	         *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Saranya                 Changes in file for mobile app     *
 *                                                    1.Extra fields are added in        *
 *                                                    field list of list query.          */
//=========================================================================================
//=========================================================================================
class Employees_Model_DbTable_TimesheetActivity extends Zend_Db_Table_Abstract
{
    protected $_orgDF = null;
    protected $_dbPersonal = null;
    protected $_dbProject;
    protected $_db = null;
    protected $_ehrTables = null;
    protected $_hrappMobile = null;
    protected $_isMobile = null;
    protected $_commonFunction= null;
    public function init()
    {
        $this->_ehrTables   = new Application_Model_DbTable_Ehr();
        $this->_db          = Zend_Registry::get('subHrapp');
        $this->_dbPersonal  = new Employees_Model_DbTable_Personal();
        $this->_orgDF       = $this->_ehrTables->orgDateformat();
        $this->_dbProject   = new Organization_Model_DbTable_Project();
		$this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
        $this->_isMobile    = $this->_hrappMobile->checkDevice();
	$this->_commonFunction = new Application_Model_DbTable_CommonFunction();
    }

	/**
	 * Get activity name by activityId
	 */
    public function activityName($activityId)
    {
        $activityQry=$this->_db->select()->from(array('act'=>$this->_ehrTables->timesheetActivity),array('act.Activity_Name'))
        ->where('act.Activity_Id = ?', $activityId);
        $activityName=$this->_db->fetchOne($activityQry);
        return $activityName;
    }
	


	

    public function searchTimesheetActivityProjects($page,$rows,$sortField,$sortOrder,$logEmpId,$searchAll=null)
    {
	
        switch ($sortField)
        {
                default:
                case 0: $sortField = 'P.Project_Name'; break;
        }
        $qryTimesheetActivity = $this->_db->select()->from(array('TA'=>$this->_ehrTables->timesheetActivity),
					array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS TA.Project_Id as count'), 'P.Project_Name',
						    'TA.Project_Id',new Zend_Db_Expr("DATE_FORMAT(TA.Added_Date,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
							new Zend_Db_Expr("DATE_FORMAT(TA.Modified_Date,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On"),))
					->joinInner(array('P'=>$this->_ehrTables->project),'TA.Project_Id=P.Project_Id',
								array('P.Project_Name'))
					->joinLeft(array('EP'=>$this->_ehrTables->empPersonal),'EP.Employee_Id=TA.Updated_By',
							array(new Zend_Db_Expr("CONCAT(EP.Emp_First_Name, ' ',EP.Emp_Last_Name) as Updated_By"),new Zend_Db_Expr("CONCAT(EP.Emp_First_Name, ' ',EP.Emp_Last_Name) as Added_By")))
					
					->group('TA.Project_Id')	
					->order("$sortField $sortOrder")
					->limit($rows, $page);

		if (!empty($searchAll) && $searchAll != null)
		{			
			$qryTimesheetActivity->where($this->_db->quoteInto('P.Project_Name Like ?', "%$searchAll%"));
		}
	
                
              
		
		$timesheetHoursDetails = $this->_db->fetchAll($qryTimesheetActivity);
                
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		        
		$iTotal = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->timesheetActivity, new Zend_Db_Expr('COUNT(Project_Id)'))->group('Project_Id'));
                
		return array("iTotalRecords" =>count($iTotal), "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $timesheetHoursDetails);
    }
    
    public function searchTimesheetActivity($projectId)
    {
        $qryTimesheetActivity = $this->_db->select()->from(array('TA'=>$this->_ehrTables->timesheetActivity),
					array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS TA.Activity_Id as count'),
                              'TA.Activity_Id', 'TA.Activity_Name','TA.Project_Id','TA.Description','TA.Is_Billable',
                              'TA.Activity_From as Cal_Activity_From', 'TA.Activity_To as Cal_Activity_To',
                              new Zend_Db_Expr("DATE_FORMAT(TA.Activity_From,'".$this->_orgDF['sql']."') as Activity_From"),
                              new Zend_Db_Expr("DATE_FORMAT(TA.Activity_To,'".$this->_orgDF['sql']."') as Activity_To"),
                              'TA.Added_Date as Added_On','TA.Modified_Date as Updated_On'))
					->joinInner(array('P'=>$this->_ehrTables->project),'TA.Project_Id=P.Project_Id',
								array('P.Project_Name'))
					->joinLeft(array('EP'=>$this->_ehrTables->empPersonal),'EP.Employee_Id=TA.Updated_By',
							array(new Zend_Db_Expr("CONCAT(EP.Emp_First_Name, ' ',EP.Emp_Last_Name) as Updated_By")))
					
					->where('TA.Project_Id = ?',$projectId);

		$timesheetHoursDetails = $this->_db->fetchAll($qryTimesheetActivity);
                
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
                
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->timesheetActivity, new Zend_Db_Expr('COUNT(Activity_Id)'))->where('Project_Id = ?',$projectId));
                
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $timesheetHoursDetails);
    }
	
	
	
    //to list the activities in dropdown based on the project
    public function getActivityPairs($projectId)
    {
        if (!empty($projectId))
        {
            $qryActivities = $this->_db->select()->from(array('TA'=>$this->_ehrTables->timesheetActivity),array('Project_Activity_Id as Activity_Id', 'AM.Activity_Name'))
								->joinInner(array('AM'=>$this->_ehrTables->activitiesMaster),'TA.Activity_Id = AM.Activity_Id', array())
								->where('Project_Id = ?', $projectId)
								->order('AM.Activity_Name ASC');

            $activities=$this->_db->fetchPairs($qryActivities);
             
            return $activities;
        }
    }
	
    //to fetch the activities based on the project
    public function viewActivityByProject($projectId)
    {         
        $activityQry = $this->_db->select()
        ->from(array('timesheet'=>$this->_ehrTables->timesheetActivity),
        array('timesheet.Activity_Name','timesheet.Description','timesheet.Activity_Id','timesheet.Updated_By',
        new Zend_Db_Expr("DATE_FORMAT(timesheet.Modified_Date,'".$this->_orgDF['sql']." at %T') as Modified_Date"),
        new Zend_Db_Expr("DATE_FORMAT(timesheet.Added_Date,'".$this->_orgDF['sql']." at %T') as Added_Date")))
        ->joinInner(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=timesheet.Updated_By ',
        new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as EmployeeName"))
        ->joinInner(array('project'=>$this->_ehrTables->project),'timesheet.Project_Id=project.Project_Id',array('project.Project_Name'))
        ->joinLeft(array('ass'=>$this->_ehrTables->assignment),'timesheet.Activity_Id=ass.Activity_Id and
    									timesheet.Project_Id =ass.Project_Id',array('ass.Task_Id'))
    	->joinLeft(array('empT'=>$this->_ehrTables->empTimesheet),'timesheet.Activity_Id=empT.Activity_Id and
    									timesheet.Project_Id =empT.Project_Id',array('empT.Request_Id'))
        ->group('timesheet.Activity_Id')->where('timesheet.Project_Id = ?', $projectId);
         
        $activities=$this->_db->fetchAll($activityQry);

        return $activities;
    }
	

    
     public function deleteActivity($projectId,$activityId,$logEmpId, $formName)
    {
        $assignmentExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->assignment, new Zend_Db_Expr('Count(Task_Id)'))
										->where('Activity_Id = ?',$activityId));
		
        $auditAssignmentExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->auditAssignment,new Zend_Db_Expr('Count(Audit_Id)'))
										->where('Activity_Id = ? or ActivityId_ChangedTo = ?',$activityId));
		
        $timesheetExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empTimesheet,new Zend_Db_Expr('Count(Activity_Id)'))
										->where('Activity_Id = ?',$activityId));
		
        if (!empty($activityId) && $assignmentExist == 0 && $auditAssignmentExist == 0 && $timesheetExist == 0)
        {
            $activityLock = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->timesheetActivity, array('Lock_Flag','Activity_Id'))
            ->where('Project_Id = ?', $projectId));
			
            $activityName = $this->activityName($activityId);
	    
            $deleted = $this->_db->delete($this->_ehrTables->timesheetActivity, 'Activity_Id='.(int)$activityId);
            
		    return $this->_commonFunction->deleteRecord (array('deleted'        => $deleted,
								    'tableName'      => $this->_ehrTables->timesheetActivity,
								    'lockFlag'       => 0,
								    'formName'       => $formName,
								    'trackingColumn' => $activityName,
								    'sessionId'      => $logEmpId));
	    }
        else
        {
            return array('success'=>false, 'msg'=>'Unable to delete '.$formName.' as it is associated with the assignment or employee timesheet', 'type'=>'info');
        }
    }


    //to delete all the activities comes under a project
    public function deleteActivityByProject($projectId, $logEmpId, $formName)
    {
        /** Check the timesheet activity associated to the project is mapped in the assigment table */
        $projAssignmentExist = $this->_db->fetchOne($this->_db->select()
        ->from(array('ass'=>$this->_ehrTables->assignment), new Zend_Db_Expr('Count(Task_Id)'))
        ->joinInner(array('act'=>$this->_ehrTables->timesheetActivity),'act.Activity_Id=ass.Activity_Id ',array(''))
        ->where('act.Project_Id = ?',$projectId));
    
        /** Check the timesheet activity associated to the project is mapped in the audit_assigment table */
        $auditAssignmentExist = $this->_db->fetchOne($this->_db->select()->from(array('ass'=>$this->_ehrTables->auditAssignment),
                                    new Zend_Db_Expr('Count(ass.Audit_Id)'))
                                ->joinInner(array('act'=>$this->_ehrTables->timesheetActivity),
                                    '(act.Activity_Id=ass.Activity_Id OR act.Activity_Id=ass.ActivityId_ChangedTo)',array(''))
                                ->where('act.Project_Id = ?',$projectId));
        
        /** Check the timesheet activity associated to the project is mapped in the emp_timesheet table */
        $activityExistInempTimesheet = $this->_db->fetchOne($this->_db->select()->from(array('et'=>$this->_ehrTables->empTimesheet),
                                        new Zend_Db_Expr('Count(et.Activity_Id)'))
                                        ->joinInner(array('act'=>$this->_ehrTables->timesheetActivity),
                                            'act.Activity_Id=et.Activity_Id',array(''))
                                        ->where('act.Project_Id = ?',$projectId));

        /** If the timesheet activity associated to the project is not mapped in the assigment, audit_assigment, timesheet table */
        if (!empty($projectId) && empty($projAssignmentExist) && empty($auditAssignmentExist) && empty($activityExistInempTimesheet))
        {
            $actLock = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->timesheetActivity, 'Lock_Flag')
                           ->where('Project_Id = ?', $projectId));
            if($actLock == 0)
            {
                $deleted=$this->_db->delete($this->_ehrTables->timesheetActivity,'Project_Id='.(int)$projectId);
            }

            return $this->_commonFunction->deleteRecord (array('deleted'        => $deleted,
								    'tableName'      => $this->_ehrTables->timesheetActivity,
								    'lockFlag'       => $actLock,
								    'formName'       => $formName,
								    'trackingColumn' => $projectId,
								    'sessionId'      => $logEmpId));
        }
        else
        {
            return array('success'=>false, 'msg'=>'Unable to delete '.$formName.' as it is associated with the assignment or employee timesheet', 'type'=>'info');
        }
    }
    
	//to get the activity's taskids which were used in assingment based on the project id
    public function getTaskActivities($projectId)
    {
        $activityQry = $this->_db->select()
								->from(array('timesheet'=>$this->_ehrTables->timesheetActivity),array(''))
								
								->joinInner(array('ass'=>$this->_ehrTables->assignment),'timesheet.Activity_Id=ass.Activity_Id',
											array('ass.Activity_Id'))
								
								->where('timesheet.Project_Id = ?', $projectId);
		
		return $this->_db->fetchCol($activityQry);
    }
	
	/**
	 * Get activities by projectId
	 */
    public function getProjectActivities($projectId)
    {
        $activityQry = $this->_db->select()
        ->from(array('timesheet'=>$this->_ehrTables->timesheetActivity),array('Activity_Id'))
        ->where('timesheet.Project_Id = ?', $projectId);
        $activities=$this->_db->fetchCol($activityQry);

        return $activities;
    }
    
	//to delete the activities during updation
    public function deleteRemovedActivity($activityId)
    {
        if (!empty($activityId))
        {
            $deleted=$this->_db->delete($this->_ehrTables->timesheetActivity,'Activity_Id ='.(int)$activityId);
        }
    }
    
    public function updateTimesheetActivity($timesheetActivity,$sessionId, $formName)
    {
        $qryTimesheetActivity = $this->_db->select()->from($this->_ehrTables->timesheetActivity, new Zend_Db_Expr('count(Activity_Id)'))
                                                                                 ->where('Project_Id = ?', $timesheetActivity['Project_Id'])
                                                                                 ->where('Activity_Name = ?', $timesheetActivity['Activity_Name']);

         if (!empty($timesheetActivity['Activity_Id']))
                $qryTimesheetActivity->where('Activity_Id != ?', $timesheetActivity['Activity_Id']);

        $timesheetActivityNameExists = $this->_db->fetchOne($qryTimesheetActivity);

        /** If the timesheet activity name does not exists */
        if(empty($timesheetActivityNameExists))
        {
                if(!empty($timesheetActivity['Activity_Id']))
                {
                    /** Get the old is_billable flag for the timesheet activity record */
                    $timesheetActivityOldIsBillabeFlag = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->timesheetActivity, 'Is_Billable')
                                                        ->where('Project_Id = ?', $timesheetActivity['Project_Id'])
                                                        ->where('Activity_Id = ?', $timesheetActivity['Activity_Id']));

                    /** Update the timesheet activity if the old and the new billable flag is same */
                    if($timesheetActivityOldIsBillabeFlag === $timesheetActivity['Is_Billable']){
                        $action = 'Edit';
                        $timesheetActivity['Modified_Date'] = date('Y-m-d H:i:s');
                        $timesheetActivity['Updated_By'] = $sessionId;
                        $timesheetActivity['Lock_Flag']  = 0;
                        $updated = $this->_db->update($this->_ehrTables->timesheetActivity, $timesheetActivity, array('Activity_Id = '.$timesheetActivity['Activity_Id']));
                    }else{
                        return array('success' => false, 'msg'=>'Billable value cannot be changed for the timesheet activity', 'type'=>'info');
                    }
                }
                else
                {
                        $action = 'Add';
                        $timesheetActivity['Added_Date'] = date('Y-m-d H:i:s');
                        $timesheetActivity['Updated_By'] = $sessionId;
                        $updated =  $this->_db->insert($this->_ehrTables->timesheetActivity, $timesheetActivity);
                }
                
              return $this->_commonFunction->updateResult (array('updated'    => $updated,
								'action'         => $action,
								'trackingColumn' => $timesheetActivity['Activity_Id'],
								'formName'       => $formName,
								'sessionId'      => $sessionId,
								'tableName'      => $this->_ehrTables->timesheetActivity));
        }
        else
        {
            return array('success' => false, 'msg'=>'Activity Name Already Exist', 'type'=>'info');
        } 
    }
    
    
//	//to insert timesheet activity
//    public function addUpdateActivity($formdata, $sessionEmp, $projectId)
//    {
//        $activities=array();
//        $insertupdatecnt=0;
//        $inserted = 0;
//        $updated = 0;
//
//        for($num = 1; $num <= $formdata['dynamicform']['Hidden']; $num++)
//        {
//            $activityres=$this->activityExists($formdata['dynamicform']['ActivityName'.$num],$projectId,$formdata['dynamicform']['ActivityId'.$num]);
//
//            if ($activityres == 0)
//            {
//                $activityArray = array('Activity_Name'=>$formdata['dynamicform']['ActivityName'.$num],
//    			'Description'=>$formdata['dynamicform']['Description'.$num],
//    			'Project_Id'=>$projectId,
//    			'Updated_By'=>$sessionEmp,
//    			'Lock_Flag'=>0);
//                //if the activity id in the hidden field exists it will be updated
//                if (!empty($formdata['dynamicform']['ActivityId'.$num]))
//                {
//                    $activityArray['Modified_Date'] = date('Y-m-d H:i:s');
//                    $updated=$this->_db->update($this->_ehrTables->timesheetActivity,$activityArray,'Activity_Id='.(int)$formdata['dynamicform']['ActivityId'.$num]);
//                    if($num <= $formdata['dynamicform']['Hidden'])
//                    $tsTitle = 'Edit';
//                }
//                else
//                {
//                    $activityArray['Added_Date'] = date('Y-m-d H:i:s');
//                    //	if the activity id in the hidden field doesn't exists it will be inserted
//                    $inserted=$this->_db->insert($this->_ehrTables->timesheetActivity,$activityArray);
//                    if($num <= $formdata['dynamicform']['Hidden'])
//                    $tsTitle = 'Add';
//                }
//                if ($inserted || $updated)
//                {
//
//                    $activities[$insertupdatecnt]['Activity_Name']=$formdata['dynamicform']['ActivityName'.$num];
//                    $activities[$insertupdatecnt]['Description']=$formdata['dynamicform']['Description'.$num];
//                    $insertupdatecnt++;
//                }
//            }
//        }
//
//        if ($inserted || $updated)
//        {
//            $projectName = $this->_dbProject->getProjectById($projectId);
//            $this->_ehrTables->trackEmpSystemAction($tsTitle.' Timesheet Activities - '.$projectName, $sessionEmp);
//            return $activities;
//        }
//        else
//        {
//            return null;
//        }
//    }
    
	//to fetch activity based on the name
    public function activityExists($activityName, $projectId, $activityId)
    {
        $activityName=(string)$activityName;

        $activityQry = $this->_db->select()
        ->from(array('act'=>$this->_ehrTables->timesheetActivity), new Zend_Db_Expr('Count(Project_Id)'))
        ->where('Activity_Name = ?', $activityName)
        ->where('Project_Id = ?',$projectId);

        if (!empty($activityId))
        {
            $activityQry->where('Activity_Id != ?',$activityId);
        }
        $activityCnt = $this->_db->fetchOne($activityQry);

        if ($activityCnt == 0) {

            return 0;

        } else {

            return $activityCnt;
        }
    }
    
	/**
	 * Get activity count for a projectID
	 */
	public function checkActivityExist($projectId)
    {
        $activityExists = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->timesheetActivity, new Zend_Db_Expr('Count(Project_Id)'))
        ->where('Project_Id = ?', $projectId));
        return $activityExists;
        	
    }

    public function __destruct()
    {
        
    }	
}

