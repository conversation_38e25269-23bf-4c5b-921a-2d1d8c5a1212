<?php

class DatasetupDashboard_Model_DbTable_DataSetupDashboard extends Zend_Db_Table_Abstract
{
    protected $_db = null;
	protected $_ehrTables = null;
	
    public function init()
    {
        $this->_ehrTables = new Application_Model_DbTable_Ehr();
        $this->_db = Zend_Registry::get('subHrapp');
        $this->_salesDb = Zend_Registry::get('Hrapp');
        //$this->_dbOrgDetail = new Organization_Model_DbTable_OrgSettings();
	}
    
    /**Get the Data Setup Form Details based on the associated Plan Details **/
    public function getDataSetupForms()
    {
        $orgCode = $this->_ehrTables->getOrgCode();
        
        //Get the Form Ids based on the Plan associated with Org Code.
        $getPlan = $this->_salesDb->fetchCol($this->_salesDb->select()
                                                        ->from(array('R'=>$this->_ehrTables->orgChoiceRate),
                                                               array(''))
                                                        ->joinInner(array('BR'=>$this->_ehrTables->billingRate),'R.Billing_Id=BR.Billing_Id',
                                                                array(''))
                                                        ->joinInner(array('PF'=>$this->_ehrTables->planforms),'BR.Plan_Id=PF.Plan_Id',
                                                                array('PF.Form_Id'))
                                                        ->where('R.Org_Code = ?',$orgCode));
                                                        
       //Get Prerequisite Form Details if it is added in Plan
       $dataSetupForms = $this->_salesDb->fetchAll($this->_salesDb->select()
                                                        ->from(array('P'=>$this->_ehrTables->prerequisite),
                                                               array('Form_Id','Prev_Form_Id','Table_Name',
                                                                     'Description','Document_Link','Youtube_Link','Is_Required'))
                                                        ->where('Form_Id IN (?)',$getPlan));
                                                      
                        
        for($i=0;$i<count($dataSetupForms);$i++)
        {
            //Get the Prerequisite Form Names and corresponding Module Name.
            $dataForms = $this->_db->fetchRow($this->_db->select()
                                                        ->from(array('F'=>$this->_ehrTables->forms),
                                                                        array('Form_Name'))
                                                            ->joinInner(array('M'=>$this->_ehrTables->modules), 'F.Module_Id=M.Module_Id',
                                                                        array('Module_Name'))
                                                            ->where('F.Form_Id = ?',$dataSetupForms[$i]['Form_Id']));
            //When the Form & Module names are returned as stdclass object
            if(is_object($dataForms))
            {                
               $dataSetupForms[$i]['Form_Name'] = $dataForms->Form_Name;
               $dataSetupForms[$i]['Module_Name'] = $dataForms->Module_Name;
            }
            //When the Form & Module names are returned as array
            else
            {
                $dataSetupForms[$i]['Form_Name'] = $dataForms['Form_Name'];
                $dataSetupForms[$i]['Module_Name'] = $dataForms['Module_Name'];
            }
            
            //Get the Previous Form Id of Prerequisite form
            $getPrevForm = $this->_salesDb->fetchOne($this->_salesDb->select()
                                                        ->from(array('P'=>$this->_ehrTables->prerequisite),
                                                               array('Prev_Form_Id'))
                                                        ->where('Form_Id IN (?)',$dataSetupForms[$i]['Form_Id']));
            
            //Get the Previous Form's Status.
            $dataSetupForms[$i]['Prev_Form_Status'] = $this->_db->fetchOne($this->_db->select()
                                                        ->from($this->_ehrTables->datasetupDashboard,
                                                                        array('Status'))
                                                        ->where('Form_Id = ?',$getPrevForm));
            
            //Get the Prerequisite Form Status in Data Setup table.
            $dataSetupForms[$i]['Form_Status'] = $this->_db->fetchOne($this->_db->select()
                                                        ->from($this->_ehrTables->datasetupDashboard,
                                                                        array('Status'))
                                                        ->where('Form_Id = ?',$dataSetupForms[$i]['Form_Id']));
            
            if($dataSetupForms[$i]['Table_Name'] != 'employees')
            {
                //Check the Data exists condition for organization settings based on Start Year value.
                if($dataSetupForms[$i]['Table_Name'] == 'org_details')
                {
                    $dataFormsCont = $this->_db->fetchOne($this->_db->select()
                                                                ->from($dataSetupForms[$i]['Table_Name'],
                                                                                array('Start_Year')));
                    if($dataFormsCont)
                        $dataSetupForms[$i]['Table_Content'] = 1;
                    else
                        $dataSetupForms[$i]['Table_Content'] = 0;
                }
                //Check the Data exists condition for Locations if Location Name exists.
                else if($dataSetupForms[$i]['Table_Name'] == 'location')
                {
                    $dataFormsCont = $this->_db->fetchOne($this->_db->select()
                                                                ->from($dataSetupForms[$i]['Table_Name'],
                                                                                array('Location_Name')));
                    if($dataFormsCont)
                        $dataSetupForms[$i]['Table_Content'] = 1;
                    else
                        $dataSetupForms[$i]['Table_Content'] = 0;
                }
                //For other forms data exists condition is based on record count.
                else
                {
                    $dataFormsCont = $this->_db->fetchAll($this->_db->select()
                                                                ->from($dataSetupForms[$i]['Table_Name'],
                                                                                array('*')));
                    
                    $dataSetupForms[$i]['Table_Content'] = count($dataFormsCont);
                }
            }
            //When the Prerequisite form is Employees.
            else
            {
                //Required field tables of Employees.
                $empTableNames = array('emp_personal_info','emp_job','emp_language','emp_user','contact_details');
                
                $empDataCont = 0;
                
                for($j=0;$j<count($empTableNames);$j++)
                {
                    $empCont = $this->_db->fetchAll($this->_db->select()
                                                                ->from($empTableNames[$j],
                                                                                array('*')));
                    if($empCont > 0)
                        $empDataCont++;
                }
                

                /** Employees value already exists will be checked based on DOB,Emp Status & Form Status,
                         some of the Employee data have already added on the application Registration.**/
                if($empDataCont)
                {
                    $empStatus = $this->_db->fetchRow($this->_db->select()
                                                            ->from(array('EP'=>$this->_ehrTables->empPersonal),
                                                                            array('Form_Status','DOB'))
                                                            ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'EJ.Employee_Id=EP.Employee_Id',
                                                                array('Emp_Status')));
                    
                    //Employee Count
                    $empCnt = $this->_db->fetchOne($this->_db->select()
                                                            ->from(array('EP'=>$this->_ehrTables->empPersonal),
                                                                            array(new Zend_Db_Expr('count(Employee_Id)'))));
                    
                    //If only one Employee exists, then check Employee's DOB & Status.
                    if($empCnt == 1)
                    {
                        $dataSetupForms[$i]['Table_Content'] = 0;    
                        if(is_object($empStatus))
                        {
                            if(!empty($empStatus->Form_Status) && !empty($empStatus->DOB) && !empty($empStatus->Emp_Status))
                                $dataSetupForms[$i]['Table_Content'] = 1;                            
                        }
                        else
                        {
                            if(!empty($empStatus['Form_Status']) && !empty($empStatus['DOB']) && !empty($empStatus['Emp_Status']))
                                $dataSetupForms[$i]['Table_Content'] = 1;                       
                        }
                    }
                    else{
                        $dataSetupForms[$i]['Table_Content'] = 1;    
                    }
                }
            }
            
            //Sub Forms for which the Main form name has to be used in url.
            $subForms = array('Holiday Types', 'Employee Type', 'Grades', 'Timesheet Hours',
                              'Leave Types','Allowance Types','Bonus Types','Department Hierarchy');
            if(in_array($dataSetupForms[$i]['Form_Name'], $subForms))
            {
                $dataSetupForms[$i]['Url_Form'] = ($dataSetupForms[$i]['Form_Name']=='Holiday Types'?'Holidays':
                                                   (($dataSetupForms[$i]['Form_Name']=='Employee Type'||$dataSetupForms[$i]['Form_Name']=='Grades')?'Employees':
                                                    ($dataSetupForms[$i]['Form_Name']=='Timesheet Hours'?'Timesheets':($dataSetupForms[$i]['Form_Name']=='Leave Types'?'Leaves':
                                                    ($dataSetupForms[$i]['Form_Name']=='Allowance Types'?'Allowances':($dataSetupForms[$i]['Form_Name']=='Bonus Types'?'Bonus':
                                                    ($dataSetupForms[$i]['Form_Name']=='Department Hierarchy'?'Departments':'')))))));
            }
            else
            {
                $dataSetupForms[$i]['Url_Form'] = $dataSetupForms[$i]['Form_Name'];
            }
            // If Table Content of the form is empty then the status should be updated as "Open"
            if($dataSetupForms[$i]['Table_Content'] == 0)
            {
                $updated = $this->_db->update($this->_ehrTables->datasetupDashboard, array('Status'=> "Open"), array('Form_Id = '.$dataSetupForms[$i]['Form_Id']));
            }    
        }
        
        return $dataSetupForms;
    }
        
    /** Check the status for each Prerequisite Form. If all the Prerequisite form details are filled then other forms will be enabled
     *  else will be disabled. */
    public function dataSetupValue()
    {
        $orgDataSetupEnabled = $this->getDataSetupDashboardOrgSettingsVal();

        if($orgDataSetupEnabled){
            $dataVal = $this->getDataSetupForms();
            
            $dataCnt = 0;
            $totFrms = 0;
            $empImp = 0;
            
            for($a=0;$a<count($dataVal);$a++)
            {
                //Default count of Mandatory Prerequisites.
                if($dataVal[$a]['Is_Required'] == 1)
                {
                    $totFrms++;
                }
                
                //Get the existing count of Mandatory Forms based on Form status, its table data count
                //and check whether the Form is Mandatory/Optional. 
                if($dataVal[$a]['Form_Status'] == 'Completed' && $dataVal[$a]['Table_Content'] > 0
                   && $dataVal[$a]['Is_Required'] == 1)
                {
                    $dataCnt++;
                }
                
                if($dataVal[$a]['Form_Id'] == 24  && $dataVal[$a]['Form_Status'] == 'Completed' && $dataVal[$a]['Table_Content'] > 0 )
                {
                   $empImp++; 
                }
            }
            
            //Compare Default count & Existing Count of Mandatory Prerequisites.
            if($totFrms == $dataCnt)
                return 1;
            else
                return 0;
        }
        else
            return 2;//org-data-setup-disabled
    }
    
    
    public function dataSetupImportValue()
    {
        $orgDataSetupEnabled = $this->getDataSetupDashboardOrgSettingsVal();

        if($orgDataSetupEnabled){
            $dataVal = $this->getDataSetupForms();
            
            $dataCnt = 0;
            $totFrms = 0;
            $empImp = 0;
            
            for($a=0;$a<count($dataVal);$a++)
            {
                if($dataVal[$a]['Form_Id'] == 32  && $dataVal[$a]['Form_Status'] == 'Completed' && $dataVal[$a]['Table_Content'] > 0 )
                {
                    return 1;
                }
            }
            
            ////Compare Default count & Existing Count of Mandatory Prerequisites.
            //if($totFrms == $dataCnt)
            //    return array('DataCount'=>$dataCnt, 'Import'=> $empImp);
            //    //return true;
            //else
                return 0;
        }else{
            return 2;//org-data-setup-disabled
        }
    }
    
    /** Get the Payslip Count for Data Setup icon hide & show. **/
    public function getPayslipCount()
    {
        return $this->_db->fetchOne($this->_db->select()
                                                    ->from($this->_ehrTables->monthlyPayslip,
                                                                        array(new Zend_Db_Expr('COUNT(Payslip_Id)'))));
    }

    /** Get the Data setup dashboard prerequisite value from the organization settings form */
    public function getDataSetupDashboardOrgSettingsVal(){
        $orgCode = $this->_ehrTables->getOrgCode();
        
        return $this->_db->fetchOne($this->_db->select()
                                                    ->from($this->_ehrTables->orgDetails,
                                                                        array('Data_Setup_Prerequisites'))
                                                    ->where('Org_Code = ?',$orgCode));
        
    }

    /** Validate the Data Setup prerequisites is enabled in the organization settings. If enabled return the
     * data setup forms otherwise return the empty array
    */
    public function validateRetrieveDataSetupForms(){         
         /** Get the Data setup dashboard prerequisite value from the organization settings form */
        $isOrgDataSetupPrereqEnabled = $this->getDataSetupDashboardOrgSettingsVal();

        /** If the Data setup prerequisites is enabled in the organization settings */
        if($isOrgDataSetupPrereqEnabled){
            // To Mask/Unmask user visited menu/forms if the mandatory prerequisites are not completed
            return $this->getDataSetupForms();
        }else{
            return  array();
        }
    }
}

