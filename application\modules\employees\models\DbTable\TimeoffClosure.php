<?php
class Employees_Model_DbTable_TimeoffClosure extends Zend_Db_Table_Abstract
{
	protected $_db = null;
	protected $_ehrTables   = null;
	protected $_dbLeave     = null;
	protected $_orgDetails = null;
    protected $_dbPrerequisite = null;
    protected $_logEmpId           = null;
    protected $_dbPayslip   = null;
	public function init()
	{
		$this->_db 					= Zend_Registry::get('subHrapp');
  	    $this->_ehrTables   		= new Application_Model_DbTable_Ehr();
		$this->_dbPrerequisite   	= new Payroll_Model_DbTable_Prerequisite();
		$this->_dbCommonFun 		= new Application_Model_DbTable_CommonFunction();
        $userSession               	= $this->_dbCommonFun->getUserDetails ();
        $this->_dbPayslip   		= new Payroll_Model_DbTable_Payslip();
		$this->_dbLeave 			= new Employees_Model_DbTable_Leave();
		$this->_logEmpId           	= $userSession?$userSession['logUserId']:1;
        if (Zend_Registry::isRegistered('orgDetails'))
             $this->_orgDetails = Zend_Registry::get('orgDetails');

	}


    public function getTimeoffClosureDetails($timeoffClosureFilter=NULL,$timeoffClosureEmployeeIds=NULL,$timeOffClosureMonth)
	{
		$month = explode(',', $timeOffClosureMonth);
		$paycyleDate =  $this->_dbPayslip->getSalaryDay($month[0], $month[1]);

		#If the Consider cutoff day for attendance and timeoff is enabled, then attendance and timeoff should be considered based on the cutoff day
		$paycyleDateWithCutoff =  $this->_dbPayslip->getSalaryDay($month[0], $month[1], 31);
		$currentdate = date('Y-m-1');
		$payslipdate = date($month[1].'-'.$month[0].'-1');


		$timeoffClosureEmployeeDetails = $this->timeoffClosureEmployeeDetails($timeoffClosureFilter,$timeoffClosureEmployeeIds,$paycyleDate['Salary_Date'], $paycyleDate['Last_SalaryDate']);
		$timeoffClosureEmployees  = array_column($timeoffClosureEmployeeDetails, 'Employee_Id');
        $arrayEmployee 			  = array_unique($timeoffClosureEmployees);

		$payslipExist =  $this->_dbPayslip->payslipExistEmployeeIds($arrayEmployee,$timeOffClosureMonth);
		if(empty($payslipExist))
		{
			$approvalsCount = array();
			$approvalPendingEmployeeId = array();
			$arrayEmployeeCount = count($arrayEmployee);
			if($arrayEmployeeCount > 0)
			{
				$closureCompletedEmployeeIds = $this->getTimeoffClosureCompletedEmployeeIds($timeOffClosureMonth,$arrayEmployee);
				if(!empty($closureCompletedEmployeeIds))
				{
					$arrayEmployee = array_diff($arrayEmployee, $closureCompletedEmployeeIds);
				}
				$arrayEmployeeCount = count($arrayEmployee);
			}

			$payslipNotGeneratedEmployeeCount = 0;
			$payslipEmployeeDetails = array();
			if($arrayEmployeeCount > 0)
			{
				$payslipEmployeeDetails 		  		= $this->_dbPrerequisite->getPayslipEmployeeDetails($timeOffClosureMonth,$arrayEmployee);
				$arrayEmployee 					  		= $payslipEmployeeDetails['salaryEmployeeIds'];
				$futureMonthClosureGeneratedEmployeeIds = $this->getFutureMonthClosureCompletedEmployeeIds($timeOffClosureMonth,$arrayEmployee);
				if(!empty($futureMonthClosureGeneratedEmployeeIds))
				{
					$arrayEmployee = array_diff($arrayEmployee,$futureMonthClosureGeneratedEmployeeIds);
				}
				$payslipNotGeneratedEmployeeCount = count($arrayEmployee);
			}

			/** To fetch employees count in pending approvals based on the choosed fields employees in salary payslip form **/
				// Define approval types with meaningful names
				$approvalTypes = [
					'resignation'      => 1,
					'shift'            => 2,
					'attendance'       => 3,
					'leave'            => 4,
					'assetManagement'  => 5,
					'compOff'          => 6,
					'unscheduledShift' => 7,
					'leaveClosure'     => 8,
					'additionalWage'   => 9,
					'probation'        => 10,
					'lopRecovery'      => 11,
					'shortTimeOff'     => 12,
					'earlyCheckout'    => 13
				];


			if($payslipNotGeneratedEmployeeCount > 0)
			{
				// Fetch all prerequisite data in a single batch operation
				$prerequisiteData = [
					// Core approval data
					'resignation'      => $this->_dbPrerequisite->getResignationCount($paycyleDate, $arrayEmployee),
					'shift'            => $this->_dbPrerequisite->getShiftCount($paycyleDateWithCutoff, $arrayEmployee),
					'attendance'       => $this->_dbPrerequisite->getAttendanceCount($paycyleDateWithCutoff, $arrayEmployee),
					'leave'            => $this->_dbPrerequisite->getLeaveCount($timeOffClosureMonth, $arrayEmployee, $paycyleDateWithCutoff),
					'assetManagement'  => $this->_dbPrerequisite->getAssetManagement($payslipEmployeeDetails),
					'compOff'          => $this->_dbPrerequisite->getCompOffCount($paycyleDateWithCutoff, $arrayEmployee),
					'unscheduledShift' => $this->_dbPrerequisite->getShiftNotScheduledDays($payslipEmployeeDetails, $paycyleDateWithCutoff),

					// Additional data
					'additionalWage'   => $this->_dbPrerequisite->getAdditionalWageClaimCount($paycyleDate, $arrayEmployee),
					'probation'        => $this->_dbPrerequisite->getProbationEmployeeDetails($paycyleDate, $arrayEmployee, $payslipEmployeeDetails),
					'lopRecovery'      => $this->_dbPrerequisite->getLOPRecoveryEmployeeDetails($timeOffClosureMonth, $arrayEmployee),
					'shortTimeOff'     => $this->_dbPrerequisite->getShortTimeOffCount($paycyleDate, $arrayEmployee),
					'earlyCheckout'    => $this->_dbPrerequisite->getEarlyCheckoutCount($paycyleDateWithCutoff, $arrayEmployee),

					// Special cases
					'attendanceEnforce' => $this->_dbPrerequisite->getAttendanceEnforcePayment($payslipEmployeeDetails, $paycyleDateWithCutoff)
				];



				// Get leave closure existence
				$dbLeave = new Employees_Model_DbTable_Leave();
				$prerequisiteData['leaveClosure'] = $dbLeave->getLeaveClosureExist($paycyleDate['Salary_Date']);

				// Initialize approval counts array with meaningful keys
				$approvalsCount = [];

				// Extract counts from prerequisite data
				foreach ($approvalTypes as $type => $index) {
					if ($type === 'leaveClosure') {
						$approvalsCount[$index] = !empty($prerequisiteData[$type]) ? 1 : 0;
					} else if ($type === 'probation') {
						$approvalsCount[$index] = $prerequisiteData[$type]; // Special case - full array
					} else if ($type === 'attendanceEnforce') {
						$approvalsCount[14] = $prerequisiteData[$type]; // Special index
					} else {
						$approvalsCount[$index] = $prerequisiteData[$type]['Count'];
					}
				}

				// Collect all employee IDs that need to be merged
				$employeeIdsToMerge = [];

				// Define which approval types have employee IDs to collect
				$typesWithEmployeeIds = [
					'resignation', 'shift', 'attendance', 'leave',
					'assetManagement', 'compOff', 'unscheduledShift',
					'additionalWage', 'shortTimeOff', 'earlyCheckout'
				];

				// Collect employee IDs from all relevant sources in one pass
				foreach ($typesWithEmployeeIds as $type) {
					$index = $approvalTypes[$type];
					if ($approvalsCount[$index] > 0 && isset($prerequisiteData[$type]['EmployeeIds'])) {
						$employeeIdsToMerge[] = $prerequisiteData[$type]['EmployeeIds'];
					}
				}

				// Special case for leave closure
				if ($approvalsCount[$approvalTypes['leaveClosure']] > 0) {
					$employeeIdsToMerge[] = $arrayEmployee;
				}

				// Special case for LOP recovery
				if ($prerequisiteData['lopRecovery']['Count'] > 0 && isset($prerequisiteData['lopRecovery']['EmployeeIds'])) {
					$employeeIdsToMerge[] = $prerequisiteData['lopRecovery']['EmployeeIds'];
				}

				// Merge all collected employee IDs at once
				if (!empty($employeeIdsToMerge)) {
					$approvalPendingEmployeeId = array_merge($approvalPendingEmployeeId, ...$employeeIdsToMerge);
				}
			}
			else
			{
				// Initialize all approval counts to zero in one go
				$approvalsCount = array_fill_keys([1,2,3,4,5,6,7,8,9,10,11,12,13,14,15], 0);
			}

			// Handle attendance enforcement
			if (!empty($approvalsCount[14]) && count($approvalsCount[14]) > 0) {
				$attEnforcedEmpId = array_column($approvalsCount[14], 'Employee_Id');
				if (!empty($attEnforcedEmpId)) {
					$approvalPendingEmployeeId = array_merge($approvalPendingEmployeeId, array_unique($attEnforcedEmpId));
				}
			}

			// Handle probation employee details
			if (!empty($approvalsCount[$approvalTypes['probation']])) {
				// Use array_column instead of loop
				$probationEmployeeId = array_column($prerequisiteData['probation'], 'Employee_Id');
				if (!empty($probationEmployeeId)) {
					$approvalPendingEmployeeId = array_merge($approvalPendingEmployeeId, array_unique($probationEmployeeId));
				}
			}

			// Set special case values
			if ($approvalsCount[$approvalTypes['assetManagement']] != 0) {
				$approvalsCount[$approvalTypes['assetManagement']] = $prerequisiteData['assetManagement']['employeeAssetDetails'];
			}

			if ($approvalsCount[$approvalTypes['unscheduledShift']] != 0) {
				$approvalsCount[$approvalTypes['unscheduledShift']] = $prerequisiteData['unscheduledShift']['unScheduledShiftDetails'];
			}

			//$arrayEmployeeId is not but payslip not generated employee id that may include the approval pending employees as well
			$readyForClosureEmployeeIds   = array_diff($arrayEmployee,$approvalPendingEmployeeId);

			$timeoffClosureByEmployeeId = $this->_dbCommonFun->organizeDataByEmployeeIdAndDate($timeoffClosureEmployeeDetails,'Employee_Id');

			$employeeDetailsWithClosureStatus = array();

			foreach ($approvalPendingEmployeeId as $employeeId)
			{
				$pendingApprovalEmployeeDetails = $this->_dbCommonFun->ensureArray($timeoffClosureByEmployeeId,$employeeId);
				$pendingApprovalEmployeeDetails[0]['Closure_Status']='Pending Approval';
				array_push($employeeDetailsWithClosureStatus,$pendingApprovalEmployeeDetails[0]);
			}

			foreach ($readyForClosureEmployeeIds as $employeeId)
			{
				$readyForClosureEmployeeDetails = $this->_dbCommonFun->ensureArray($timeoffClosureByEmployeeId,$employeeId);
				$readyForClosureEmployeeDetails[0]['Closure_Status']='Ready For Closure';
				array_push($employeeDetailsWithClosureStatus,$readyForClosureEmployeeDetails[0]);
			}

			foreach ($closureCompletedEmployeeIds as $employeeId)
			{
				$closureCompletedEmployeeDetails= $this->_dbCommonFun->ensureArray($timeoffClosureByEmployeeId,$employeeId);
				$closureCompletedEmployeeDetails[0]['Closure_Status']='Closure Completed';
				array_push($employeeDetailsWithClosureStatus,$closureCompletedEmployeeDetails[0]);
			}

			// ,"approvalPendingEmployeeId" => $approvalPendingEmployeeId
			return array('success'=>true,'msg'=>'', 'type'=>'success',"approvalsCount"=>$approvalsCount,'readyForClosureEmployeeIds'=>$readyForClosureEmployeeIds,
			'timeoffClosureEmployeeDetails'=>$employeeDetailsWithClosureStatus,'paycyleDate'=>$paycyleDate);
		}
		else
		{
			return array('success'=>false,'msg'=>'payslip is already generated for this time off closure month','type'=>'info');	
		}
	}


	public function generateTimeOffClosure($timeoffClosureEmployeeIds,$timeOffClosureMonth)
	{
		$eligibleLeaveData 	   = array();
		$timeOffClosureDetails = $this->getTimeoffClosureDetails(NULL,$timeoffClosureEmployeeIds,$timeOffClosureMonth);
		if(!empty($timeOffClosureDetails) && !empty($timeOffClosureDetails['readyForClosureEmployeeIds']))
		{
			$month 								 = explode(',', $timeOffClosureMonth);
			$leaveBalanceAsOf 			   		 = date('Y-m-d', strtotime(01 . '-' . $month[0] . '-' . $month[1]));
			$timeoffClosureEmployeeDetails		 = $timeOffClosureDetails['timeoffClosureEmployeeDetails'];
			$readyForClosureEmployeeIds    		 = $timeOffClosureDetails['readyForClosureEmployeeIds'];
			$paycyleDate 						 = $timeOffClosureDetails['paycyleDate'];
			if ($paycyleDate['Consider_Cutoff_Days_For_Attendance_And_Timeoff'] === 'yes'){
				$timeOffStartDate = $paycyleDate['Prev_CutoffDate'];
				$timeOffEndDate = $paycyleDate['Cutoff_Date'];
			} else {
				$timeOffStartDate = $paycyleDate['Salary_Date'];
				$timeOffEndDate = $paycyleDate['Last_SalaryDate'];
			}

			$employeeWorkScheduleShiftDetails	 = $this->_dbCommonFun->getWorkScheduleAndShiftRosterDetails($readyForClosureEmployeeIds,$paycyleDate['Salary_Date'],$paycyleDate['Last_SalaryDate']);	
			$timeoffClosureByEmployeeId     	 = $this->_dbCommonFun->organizeDataByEmployeeIdAndDate($timeoffClosureEmployeeDetails,'Employee_Id');
			$empEligibleLeaves 					 = $this->getEmployeeEligibleLeaveDetails($readyForClosureEmployeeIds,$paycyleDate);
			$previousTimeOffClosureMonth         = date('Y-m-01',strtotime($month[1].'-'.$month[0] . ' -1 month'));
			$monthlyLeaveBalanceDetails  		 = $this->getMonthlyLeaveBalanceDetails($readyForClosureEmployeeIds,$previousTimeOffClosureMonth);
			$previousmonthLeaveBalancDetails     = $this->_dbCommonFun->organizeDataByEmployeeIdAndDate($monthlyLeaveBalanceDetails,'Employee_Id','LeaveType_Id');
			foreach($empEligibleLeaves as $eligibleLeave)
			{
				$readyForClosureEmployeeDetails = $this->_dbCommonFun->ensureArray($timeoffClosureByEmployeeId,$eligibleLeave['Employee_Id']);
				$employeeDetails 			    = $readyForClosureEmployeeDetails[0];
				$monthlyLeaveBalanceInsertion = 'yes';
				if($eligibleLeave['Leave_Closure_Start_Date'] <= $paycyleDate['Salary_Date'] && $eligibleLeave['Leave_Closure_End_Date'] >= $paycyleDate['Salary_Date'] && $eligibleLeave['Leave_Closure_Based_On'] =='Selected Month')
				{
					if($eligibleLeave['Applicable_During_Probation'] === 'After Probation')
					{
						// Add 1 day to the probation date using proper date manipulation
						$leaveActivationDate = date('Y-m-d', strtotime($employeeDetails['Probation_Date'] . ' +1 day'));
					}
					else if($eligibleLeave['Applicable_During_Probation'] === 'Custom Configuration')
					{
						$proRateAfter = $eligibleLeave['Prorate_After'];
						// Properly add the number of days from $proRateAfter to the date of join
						$leaveActivationDate = date('Y-m-d', strtotime($employeeDetails['Date_Of_Join'] . " +{$proRateAfter} days"));
					}
					else
					{
						$leaveActivationDate = $employeeDetails['Date_Of_Join'];
					}

					// Check if leave activation date is before or equal to the timeoff closure start date
					if($leaveActivationDate <= $paycyleDate['Salary_Date'])
					{
						if(date('Y-m', strtotime($eligibleLeave['Leave_Closure_Start_Date'])) == date('Y-m', strtotime($paycyleDate['Salary_Date'])))
						{
							$openingBalance = $eligibleLeave['Eligible_Days']+$eligibleLeave['No_Of_Days'];
						}
						else
						{
							$leaveBalanceKey 				  = $eligibleLeave['Employee_Id'] . '|' . $eligibleLeave['LeaveType_Id'];
							$previousmonthLeaveBalanceDetails = $this->_dbCommonFun->ensureArray($previousmonthLeaveBalancDetails,$leaveBalanceKey);
							if(!empty($previousmonthLeaveBalanceDetails))
							{
								$openingBalance = $previousmonthLeaveBalanceDetails[0]['Closing_Leave_Balance'];
							}
							else
							{
								$openingBalance = $eligibleLeave['Eligible_Days']+$eligibleLeave['No_Of_Days'];
							}
						}
					}
					else
					{
						$monthlyLeaveBalanceInsertion = 'no';
					}
				}
				else
				{
					$monthlyLeaveBalanceInsertion = 'no';
				}

				if($monthlyLeaveBalanceInsertion =='yes')
				{
					$totalleaveTaken = $this->_dbPayslip->getLeaveFrequency($eligibleLeave['Employee_Id'],$timeOffStartDate,$timeOffEndDate,$eligibleLeave['LeaveType_Id']);
					$closingBalance  = $openingBalance - $totalleaveTaken;
					$eligibleLeaveData[] = array('Employee_Id' => $eligibleLeave['Employee_Id'],
											'Leave_Balance_As_Of' => $leaveBalanceAsOf,
											'LeaveType_Id' => $eligibleLeave['LeaveType_Id'],
											'Opening_Leave_Balance' => $openingBalance,
											'Leaves_Taken' => $totalleaveTaken,
											'Closing_Leave_Balance' => $closingBalance);
				}
			}
			

			$businessWorkingDays = $this->_dbCommonFun->getBusinessWorkingDays($paycyleDate['Salary_Date'], $paycyleDate['Last_SalaryDate'], $eligibleLeave['Employee_Id'],NULL,1,NULL,NULL,$employeeWorkScheduleShiftDetails);

			$timeOffClosureData[] = array('Employee_Id'=>$eligibleLeave['Employee_Id'],'Timeoff_Closure_Month'=> $timeOffClosureMonth,'Paid_Leave_Days'=>0,'Unpaid_Leave_Days'=>0,
											'Onduty_Days'=>0,'Business_Working_Days'=>$businessWorkingDays,'Overtime_Hours'=>0,
											'Generated_By'=>$this->_logEmpId,'Generated_On'=>date('Y-m-d H:i:s'));
			$updateMonthlyLeaveAccrual = $this->_dbLeave->updateMonthlyLeaveAccrual($month[0],$month[1]);
			$insertTimeOffClosureDetails = $this->insertTimeOffClosureDetails($timeOffClosureData,$eligibleLeaveData);
			if($insertTimeOffClosureDetails)
			{
				$this->_ehrTables->trackEmpSystemAction('Timeoff Closure Generation - '.$timeOffClosureMonth, $this->_logEmpId);
				return array('success'=>true,'msg'=>'timeoff closure generated successfully','type'=>'success');
			}
			else
			{
				return array('success'=>false,'msg'=>'Unable to generate timeoff closure','type'=>'info');
			}
		}
		else
		{
			return array('success'=>false,'msg'=>'there are no employees available ready for closure status','type'=>'info');
		}
	}

	public function insertTimeOffClosureDetails($timeOffClosureDetails,$monthlyLeaveBalance)
	{
        $insertTimeOffClosureDetails = $this->_ehrTables->insertMultiple($this->_ehrTables->timeOffClosure, $timeOffClosureDetails);
		if($insertTimeOffClosureDetails)
		{
            if(!empty($monthlyLeaveBalance) && count($monthlyLeaveBalance)>0)
            {
                $this->_ehrTables->insertMultiple($this->_ehrTables->monthlyLeaveBalance, $monthlyLeaveBalance);
            }
		}
		return true;
	}

	public function timeoffClosureEmployeeDetails($timeoffClosureFilter=NULL,$employeeIds=NULL,$salaryDate, $lastSalaryDate)
	{
		$qryTimeOffClosureEmployeeIds = $this->_db->select()->from(array('ET'=>$this->_ehrTables->empType), array('EJ.Date_Of_Join','EJ.TDS_Exemption','EJ.Service_Provider_Id','EJ.Probation_Date','EJ.Tax_Regime','ET.Benefits_Applicable','EJ.Manager_Id','EJ.Work_Schedule as WorkSchedule_Id','ET.Display_Total_Hours_In_Minutes','ET.Work_Schedule','EJ.Employee_Id','Salary_Date'=>new Zend_Db_Expr('(CASE WHEN (`EJ`.`Date_Of_Join` >= "'.$salaryDate.'" AND `EJ`.`Date_Of_Join` <= "'.$lastSalaryDate.'") THEN EJ.Date_Of_Join ELSE  "'.$salaryDate.'" END)'),'Last_Salary_Date'=>new Zend_Db_Expr('(CASE WHEN (`R`.`Resignation_Date` >= "'.$salaryDate.'" AND `R`.`Resignation_Date` <= "'.$lastSalaryDate.'") THEN R.Resignation_Date ELSE  "'.$lastSalaryDate.'" END)')))
                                                    ->joinInner(array('EJ'=>$this->_ehrTables->empJob), 'ET.EmpType_Id=EJ.EmpType_Id', array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN EP.Employee_Id ELSE EJ.User_Defined_EmpId END')))
                                                    ->joinInner(array('EP'=>$this->_ehrTables->empPersonal), 'EP.Employee_Id = EJ.Employee_Id', array('Employee_Name'=>new Zend_Db_Expr("CONCAT(EP.Emp_First_Name,' ',EP.Emp_Last_Name)")))
                                                    ->joinLeft(array('R'=>$this->_ehrTables->resignation), 'EJ.Employee_Id=R.Employee_Id AND R.Approval_Status="Approved" AND `R`.`Resignation_Date` >= "'.$salaryDate.'"', array(''))
													->joinInner(array('DT'=>$this->_ehrTables->dept), 'DT.Department_Id=EJ.Department_Id', array('DT.Department_Name as Department'))
                                                    ->joinInner(array('DS'=>$this->_ehrTables->designation), 'DS.Designation_Id=EJ.Designation_Id', array('DS.Designation_Name as Designation'))
													->joinInner(array('L'=>$this->_ehrTables->location), 'L.Location_Id=EJ.Location_Id', array('L.Location_Name as Location'))
													->joinLeft(array('BU'=>$this->_ehrTables->businessUnit),'EJ.Business_Unit_Id=BU.Business_Unit_Id',array('BU.Business_Unit'))
												    ->joinInner(array('T'=>$this->_ehrTables->timesheetHrs), 'T.Grade_Id = DS.Grade_Id',array('T.Regular_Hours'))
                                                    ->where("EJ.Emp_Status='Active' OR (EJ.Emp_Status='InActive' AND EJ.Emp_InActive_Date >= '$salaryDate')")
                                                    ->where('EP.Form_Status = 1')
                                                    ->where('EJ.Date_Of_Join <= ?', $lastSalaryDate);

		if(!empty($timeoffClosureFilter))
		{
			$designationId=$timeoffClosureFilter['designationId'];
			$departmentId=$timeoffClosureFilter['departmentId'];
			$locationId=$timeoffClosureFilter['locationId'];
			$employeeTypeId=$timeoffClosureFilter['employeeTypeId'];
			$businessUnitId=$timeoffClosureFilter['businessUnitId'];
			$serviceProviderId=$timeoffClosureFilter['serviceProviderId'];

			/* Filter for Designation Id */
			if (!empty($designationId))
			{
				$qryTimeOffClosureEmployeeIds->where('EJ.Designation_Id = ?', $designationId);
			}

			/* Filter for Department Id */
			if (!empty($departmentId))
			{
				$qryTimeOffClosureEmployeeIds->where('EJ.Department_Id = ?', $departmentId);
			}

			if (!empty($locationId))
			{
				$qryTimeOffClosureEmployeeIds->where('EJ.Location_Id = ?', $locationId);
			}

			/* Filter for Work Schedule*/
			if (!empty($employeeType))
			{
				$qryTimeOffClosureEmployeeIds->where('EJ.EmpType_Id = ?', $employeeType);
			}

			if (!empty($businessUnitId))
			{
				$qryTimeOffClosureEmployeeIds->where('EJ.Business_Unit_Id = ?', $businessUnitId);
			}

			if($this->_orgDetails['Field_Force']==1)
			{
				if(!empty($serviceProviderId))
				{
					$qryTimeOffClosureEmployeeIds->where('EJ.Service_Provider_Id = ? ', $serviceProviderId);
				}
			}

			$qryTimeOffClosureEmployeeIds = $this->_dbCommonFun->formServiceProviderQuery($qryTimeOffClosureEmployeeIds,'EJ.Service_Provider_Id',$this->_logEmpId);
		}

		if (!empty($employeeIds))
		{
			$qryTimeOffClosureEmployeeIds->where('EJ.Employee_Id IN (?)', $employeeIds);
		}

		$timeOffClosureEmployeeIds = $this->_db->fetchAll($qryTimeOffClosureEmployeeIds);

		return $timeOffClosureEmployeeIds;
	}


	public function getTimeoffClosureCompletedEmployeeIds($timeOffClosureMonth,$employeeIds)
	{
		$qryMonthlyEmp = $this->_db->select()->from($this->_ehrTables->timeOffClosure, 'Employee_Id')
											->where('Timeoff_Closure_Month = ?', $timeOffClosureMonth)
											->where('Employee_Id IN (?)',$employeeIds);
		$employeeIds = $this->_db->fetchCol($qryMonthlyEmp);
		return $employeeIds;
	}

	public function getFutureMonthClosureCompletedEmployeeIds($timeOffClosureMonth,$employeeIds)
    {
        $month                          = explode(',', $timeOffClosureMonth);
        $timeOffClosureMonthFormat      = $month[1].'-'.$month[0];
        $qryFutureMonthlyEmp            = $this->_db->select()->from($this->_ehrTables->timeOffClosure, 'Employee_Id')
							                   ->where(new Zend_Db_Expr('DATE_FORMAT(STR_TO_DATE(Timeoff_Closure_Month,  "%m,%Y" ) ,  "%Y-%m" )')." > ?",date('Y-m',strtotime($timeOffClosureMonthFormat)))
											   ->where('Employee_Id IN (?)',$employeeIds)
                                               ->group('Employee_Id');
        $futureMonthClosureCompletedEmployeeIds        = $this->_db->fetchCol($qryFutureMonthlyEmp);
        return $futureMonthClosureCompletedEmployeeIds;
    }


	public function getEmployeeEligibleLeaveDetails($employeeId)
	{
		$qryEligibleDays = $this->_db->select()->from(array('EL'=>$this->_ehrTables->empEligbleLeave),array('EL.*'))
								->joinInner(array('LT'=>$this->_ehrTables->leavetype),'EL.LeaveType_Id = LT.LeaveType_Id',array('LT.Applicable_During_Probation','LT.Leave_Closure_Based_On','LT.Prorate_After','EL.Leave_Closure_Start_Date as finstart','EL.Leave_Closure_End_Date as finend'))
								->where('LT.Leave_Closure_Based_On = ?','Selected Month')
								->where('LT.Leave_Status = ?','Active')
								->where('EL.Employee_Id IN (?)',$employeeId)
								->order('EL.Employee_Id ASC')
								->order('EL.Leave_Closure_End_Date ASC');
		$leaveClosureDetails = $this->_db->fetchAll($qryEligibleDays);
		return $leaveClosureDetails;
	}

	public function getMonthlyLeaveBalanceDetails($employeeId,$closureMonth)
	{
		$monthlyLeaveBalanceDetails =  $this->_db->fetchAll($this->_db->select()->from(array('LB'=>$this->_ehrTables->monthlyLeaveBalance),array('*'))
										->where('LB.Leave_Balance_As_Of = ?',$closureMonth)
										->where('LB.Employee_Id IN (?)',$employeeId));

		return $monthlyLeaveBalanceDetails;
	}


    public function __destruct()
    {

    }

}