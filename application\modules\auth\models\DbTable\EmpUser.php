<?php
//=========================================================================================
//=========================================================================================
/* Program : EmpUser.php													             *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MYSQL query for login Details, forgot password and reset password	     *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 * Version    Date           Author                  Description                       	 *
 *  0.1       30-May-2013    Narmadha                Initial Version         	         *
 *                                                                                    	 */
//=========================================================================================
//=========================================================================================
class Auth_Model_DbTable_EmpUser extends Zend_Db_Table_Abstract
{

    protected $_db = null;

    protected $_ehrTables = null;

    public function init()
    {
        $this->_ehrTables = new Application_Model_DbTable_Ehr();
        $this->_db = Zend_Registry::get('subHrapp');
		$this->_salesDb = Zend_Registry::get('Hrapp');
    }
	
	public function checkBillingStatus()
	{
        $orgCode = $this->_ehrTables->getOrgCode();
        
        //get the latest plan id
        $orgPlanId = $this->_salesDb->fetchOne($this->_salesDb->select()->from($this->_ehrTables->orgChoiceRate,
                                array('Org_Plan_Id'=>new Zend_Db_Expr('MAX(Org_Plan_Id)')))
                                ->where('Org_Code = ?',$orgCode));

        //get the plan status for latest plan
        $orgPlanStatus = $this->_salesDb->fetchOne($this->_salesDb->select()->from($this->_ehrTables->orgChoiceRate,array('Plan_Status'))
                                    ->where('Org_Plan_Id = ?',$orgPlanId)
                                    ->where('Plan_End_Date IS NULL')
                                    ->where('Org_Code = ?',$orgCode));
        
		return $orgPlanStatus;
	}
    
    public function getPaymentUrl()
	{
		$orgCode = $this->_ehrTables->getOrgCode();
		
		$orgPlanId = $this->_salesDb->fetchOne($this->_salesDb->select()->from($this->_ehrTables->orgChoiceRate,array('Payment_Url'))
																						->where('Plan_Status = ?', 'NonComplaint')
																						->where('Org_Code = ?',$orgCode));
		return $orgPlanId;
	}
    
    /**
     * to get employeeId by firebase uid
     * The user signs in with the firebase, we can retrieve the employee id by using the firebase uid.
     */
    public function employeeId($empUser)
	{
        return $this->_db->fetchOne($this->_db->select()->from(array('L'=>$this->_ehrTables->empLogin), array('Employee_Id'))
                                                        ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'EJ.Employee_Id = L.Employee_Id')
                                                        ->where('EJ.Emp_Status = ?',"Active")
                                                        ->where('L.Firebase_Uid = ?', $empUser));
    }

	// /**
	//  * Set user session by username
	//  */
	// public function setUserSession($userName)
    // {
    //     $employeeId = $this->employeeId($userName);
		
    //     if ($employeeId)
    //     {
    //         $getSessionId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->sessionList , 'Session_Id')
	// 											 ->where('Session_Id = ?', $employeeId));
             
    //         if (empty($getSessionId))
    //         {
    //             $getSysConfig = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->sysConfig, array('Sys_Value'))
	// 												 ->where('Config_Param = ?', 'SESSION_TIMEOUT'));
                
	// 			$addSession = $this->_db->insert($this->_ehrTables->sessionList, array('Session_Id'=> $employeeId,
	// 																				   'Timeout_Sec'=>$getSysConfig,
	// 																				   'Refresh_Timestamp'=> date('Y-m-d H:i:s') /*new Zend_Db_Expr('NOW()')*/ ));
                
	// 			if ($addSession)
    //                 return true;
    //             else
	// 				return false;
    //         }
    //         else
    //         {
    //             $updateSession = $this->_db->update($this->_ehrTables->sessionList, array('Refresh_Timestamp'=> date('Y-m-d H:i:s') /*new Zend_Db_Expr('NOW()')*/),
	// 												'Session_Id = '. $getSessionId);
                
	// 			if ($updateSession)
    //                 return true;
    //             else
	// 				return false;
    //         }
    //     }
		
	// 	return false;
    // }
   	
	/**
	 * clear session list by user name
	 */
	public function clearSessionList($employeeId, $systemLog=null)
    {
		if ($employeeId)
        {
            $this->_ehrTables->trackEmpSystemAction($systemLog, $employeeId);
			
            /** Clear the user session from the respective table and from the user session throw lock table. */
            $this->_ehrTables->clearUserSessionLockFlag($employeeId);
        }
    }
	
	/**
	 * Get login details by mailid
	 */
    public function getLoginDetailsByMail($emailId)
    {
        $empLoginQry = $this->_db->select()->from(array('job'=>$this->_ehrTables->empJob),array('Emp_Email'))
        ->joinInner(array('login'=>$this->_ehrTables->empLogin), ' job.Employee_Id = login.Employee_Id ', array('Username'=>'User_Name', 'Random_Salt', 'Employee_Id'))
        ->joinInner(array('empPersonal'=>$this->_ehrTables->empPersonal),'job.Employee_Id = empPersonal.Employee_Id',array('employee'=>new Zend_Db_Expr("CONCAT(empPersonal.Emp_First_Name,' ',empPersonal.Emp_Last_Name)")))
        ->where('job.Emp_Status = ?', 'Active')
		->where('job.Emp_Email = ?', $emailId);
        $empLoginDet = $this->_db->fetchRow($empLoginQry);
         
        return $empLoginDet;
    }
	
    /**
     *  check for valid username and salt while reset password
     */
    function checkUserDetails($username, $salt)
    {
        $qryUser = $this->_db->select()->from($this->_ehrTables->empLogin, array(new Zend_Db_Expr('count(User_Name)')))->where('User_Name = ?', $username)
        ->where(new Zend_Db_Expr('SUBSTRING(Random_Salt,4,8)').' = ?', $salt);
        $resultUser = $this->_db->fetchOne($qryUser);
		
        if ($resultUser)
        {
            return true;
        }
        return false;
    }
	
	/**
	 * Update password
	 */
    public function updateUserPassword ($newPassword, $employeeId)
    {
		$updated = $this->_db->update($this->_ehrTables->empLogin, $newPassword, 'Employee_Id = '.(int)$employeeId);
		
		if ($updated)
		{
			return array('success' => true, 'msg' => 'Password reset successfully', 'type' => 'success');
		}
		else
		{
			return array('success' => false, 'msg' => 'Unable to reset password', 'type' => 'warning');
		}
    }
	
	/**
	 * Update password
	 */
    public function updatePwd($data,$userName)
    {
        $employeeId = $this->employeeId($userName);
        
		if(!empty($employeeId))
        {
            $data['Hrapp_Password'] = new Zend_Db_Expr('CONCAT("'.$data['Hrapp_Password'].'",Random_Salt)');
            $updatepwd = $this->_db->update($this->_ehrTables->empLogin, $data, array('Employee_Id = ?'=>$employeeId));
			
			if($updatepwd)
		        return true;
        }
        else
        {
            return false;
        }
    }
	
	/**
	 * Get username by employeeId
	 */
    public function getUserName($empId)
    {
        return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empLogin, array('User_Name'))->where('Employee_Id = ?', $empId));
    }
	
	/**
	 * Update last logged in date by employeeId
	 */
    public function updateLastLoggedIn($empId, $loginUid, $userName,  $invitationStatus)
    {
        $isExistEmployeeId = $this->_db->fetchOne($this->_db->select()
        ->from($this->_ehrTables->empLogin, array(new Zend_Db_Expr('COUNT(Employee_Id)')))
        ->where('Employee_Id = ?', $empId));

        if($isExistEmployeeId > 0){
            $this->_db->update($this->_ehrTables->empLogin, array('Last_LoggedIn'=> date('Y-m-d H:i:s'), 'Firebase_Uid'=>$loginUid, 'User_Name'=>$userName), array('Employee_Id = ?'=>$empId) );
        } else {
            $this->_db->insert($this->_ehrTables->empLogin, array('Employee_Id'=> $empId,
                                                    'Firebase_Uid'=>$loginUid,
                                                    'User_Name'=>$userName,
                                                    'Created_Date'=>date('Y-m-d H:i:s'),
                                                    'Last_LoggedIn'=> date('Y-m-d H:i:s')));
        }
        if( $invitationStatus != "Signed Up"){
            $this->_db->update($this->_ehrTables->empJob, array('Invitation_Status'=> 'Signed Up'), array('Employee_Id = ?'=>$empId));
        }
    }
}

