<!DOCTYPE html>
<html>

<head>
	<!-- common scripts -->
	<script src="https://cdn.jsdelivr.net/npm/vue@2/dist/vue.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/vuetify@2.2.3/dist/vuetify.min.js"></script>
	<script src="https://cdn.jsdelivr.net/gh/f/graphql.js@master/graphql.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue-cookies@1.6.1/vue-cookies.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/v-tooltip@2.0.2"></script>
	<script src="https://cdn.jsdelivr.net/npm/vee-validate@2.x/dist/vee-validate.js"></script>
	<!-- plugin styles -->
	<link href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900"
		rel="stylesheet" />
	<link href="https://cdn.jsdelivr.net/npm/@mdi/font@4.x/css/materialdesignicons.min.css"
		rel="stylesheet" />
	<link href="https://cdn.jsdelivr.net/npm/vuetify@2.2.11/dist/vuetify.min.css" rel="stylesheet" />
	<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />

	<!--  own styles -->
	<style scoped lang="css">
		/* css overrides */
		.v-input__slot {
			margin-bottom: 0px !important
		}
		.v-application--is-ltr .v-chip .v-chip__close.v-icon.v-icon--right {
			margin-right: 0px !important; 
		}
		.custom-group-list-card-row {
			padding: 3.5em !important;
			padding-top: 0em !important;
		}
		.custom-group-btn {
			justify-content: flex-end;
			display: flex;
			padding-right: 3.5em;
			flex: none !important
		}
		.custom-group-add-icon {
			color: #ffffff;
			padding-right: 0.3em
		}
		.custom-group-list-card-pagination-row {
			flex: 0 !important;
			padding: 2em !important;
			padding-top: 0em !important;
			margin-top: 6em !important;
		}
		.active_tab_bg {
			background:#f9f9f9 !important;
		}
		.filter-menu-position {
			margin-top : 28px !important;
		}
		.mobile_view_filter_icon{
			border-radius: 50% !important;
			min-width: 40px !important;
			min-height: 40px;
		}
		.custom-group-top-bar {
			height: 5em;
			position: fixed !important;
			width: 100% !important;
			z-index : 2000;
			top : 50px;
		}
		.custom-group-no-records {
			width: 600px;
			height: auto;
		}
		.custom-group-no-records-txt {
			display: flex;
			justify-content: center;
			margin-top: 2em;
			font-size: 1.2em; 
		}
		.display-flex-common {
			display: flex;
			justify-content: center;
			align-items: center;
		}
		.v-pagination {
			justify-content: flex-end !important;
		}
		.custom-group-view-group {
			margin-top: 3em;
		}
		
		.custom-group-top-bar-index {
			height: 5em;
			position: fixed !important;
			width: 100% !important;
			top : 50px;
		}
		.search-row {
			margin-right: 1em
		}
		.show-search-input-font {
			font-size: 1.0em !important;

		}
		.search-input-font {
			font-size: 1.1em !important;

		}
		.show-filter-icon {
			padding-left: 0em !important;
			padding-right: 2em;
		}
		.tooltip .tooltip-inner{
			max-width:800px !important;
		}
		.search-row-view {
			margin-right: 1em !important;
		}
		.v-data-table thead tr:last-child th{
			font-size: 12px;
		}
		@media screen and (min-width:1263px) and (max-width:1599px){
			.search-row-view {
				margin-right: 4em !important;
			}
		}
		@media screen and (max-width : 1262px) {
			.search-row-view {
				margin-right: 0em !important;
			}
		}
		@media screen and (max-width:1100px) {
			.search-row-view {
				margin-right: 1.8em !important;
			}
		}
		@media (max-width: 600px) {
			.custom-group-no-records-txt {
				font-size: 1em !important;
				margin-top: 0em !important
			}
			.custom-group-no-records {
				width: 300px;
				height: auto;
			}
		}

		@media screen and (max-width:960px)  {
			.custom-group-list-card-row {
				padding: 0em !important;
				margin-top: 1em !important;
			}
			.custom-group-btn {
				justify-content: center;
				display: flex;
				padding-right: 1em;
				flex: none !important

			}
			.v-pagination {
				justify-content: center !important;
			}
			.v-menu__content {
				max-width: 100%;
			}
		}
	</style>

</head>

<body>
	<!--  html content -->
	<div id="customEmployeeGroup" class="col-md-12 portlets p-10">
		<template>
			<div v-if="!accessDenied">
			<v-app class="bg_grey_lighten1">
				<!-- check if the showCustomGroup is enabled - will not be enabled if any errors from access rights API / list group API -->
				<div>
					<!-- desktop topbar design -->
						 <v-card class="bg_grey_lighten1 hidden-sm-and-down" :class="topBarClass" flat height="4em">
								<v-toolbar color="grey lighten-5">
									<span class="padding-2em"></span>
									<v-tabs v-model="currentTab" background-color="#f9f9f9">
											<v-tab href="#tab-1" class="active_tab_bg">
												<div style="text-transform: capitalize !important;" class="font-weight-bold">Custom Employee Group</div>
											</v-tab>
											<!-- <v-spacer></v-spacer> -->
											<v-row v-if="((isSearch || displayCustomGroupArray.length>0) && showList) || showView" class="d-flex align-center" :class="showView ? 'search-row-view' : 'search-row'">
												
												<v-col :lg="showView ? showSearch ? 7 : 9 : showSearch ? 7 : 9" :md="showView ? showSearch ? 4 : 7 : showSearch ? 6 : 8">

												</v-col>
												<v-col :lg="showView ? showSearch ? 4 : 2 : showSearch ? 4 : 2" :md="showView ? showSearch ?  6 : 3 : showSearch ? 6 : 4" class="d-flex justify-end" :class="showView ? 'text-field' : ''">
													<v-text-field :placeholder="showSearch? searchPlaceholder :'Search'"
														prepend-inner-icon="search" single-line @focus="showSearch = true"
														@blur="showSearch = false" @change="searchCustomGroup()" v-model="searchInput" :class="showSearch ? 'show-search-input-font' : 'search-input-font' " >
													</v-text-field>
												</v-col>
												<v-col xl="showView ? 1 : 0" :lg="showView ? 1 : 0" :md="showView ? 1 : 0" class="d-flex justify-flex-end show-filter-icon" v-if="showView">
													<!-- filter button -->
													<v-menu fixed left offset-y v-model="filterOpen" class="filter-menu-position" 
													:close-on-content-click="false" max-height="400">
														<template v-slot:activator="{ on }">
															<v-btn fab small v-on="on" color="primary" @click="fetchDropDownData()">
																<i class="fa fa-filter filter-icon-property"></i>
															</v-btn>
														</template>
														<v-card class="p-15" >
															<div class="d-flex justify-end">
																<v-icon color="primary" @click="filterOpen=!filterOpen"
																	class="p-r-10 p-t-10">close</v-icon>
															</div>
															<v-row>
																<v-col cols="6">
																	<v-autocomplete background-color="white" deletable-chips multiple
																		v-model="deptSelectedInFilter" :items="departmentList" dense chips
																		filled label="Department">
																	</v-autocomplete>
																</v-col>
																<v-col cols="6">
																	<v-autocomplete background-color="white" deletable-chips multiple
																		v-model="designSelectedInFilter" :items="designationList" dense chips
																		filled label="Designation">
																	</v-autocomplete>
																</v-col>
															</v-row>
															<v-row class="m-t-n4">
																<v-col cols="6">
																	<v-autocomplete background-color="white" deletable-chips multiple
																		v-model="locSelectedInFilter" :items="locationList" dense
																		chips filled label="Location">
																	</v-autocomplete>
																</v-col>
																<v-col cols="6">
																	<v-autocomplete background-color="white" deletable-chips multiple
																		v-model="workSchSelectedInFilter" :items="workScheduleList" dense
																		chips filled label="Work Schedule">
																	</v-autocomplete>
																</v-col>
																
																
															</v-row>
															<v-row class="m-t-n4">
																<v-col cols="6">
																	<v-autocomplete background-color="white" deletable-chips multiple
																		v-model="empTypeSelectedInFilter" :items="empTypeList" dense
																		chips filled label="Employee type">
																	</v-autocomplete>
																</v-col>
																<v-col cols="6">
																	<v-autocomplete background-color="white" deletable-chips multiple
																		v-model="stateSelectedInFilter" :items="stateList" dense chips
																		filled label="Location State">
																	</v-autocomplete>
																</v-col>
															</v-row>
															<v-row class="m-t-n4">
																<v-col v-if="fieldForce" cols="6">
																	<v-autocomplete background-color="white" deletable-chips multiple
																		v-model="serviceProviderSelectedInFilter" :items="serviceProviderList" dense chips
																		filled label="Service provider">
																	</v-autocomplete>
																</v-col>
																<v-col cols="6">
																	<v-autocomplete background-color="white" deletable-chips multiple
																		v-model="employeeStatusSelectedInFilter" :items="employeeStatusList" dense chips
																		filled label="Employee Status">
																	</v-autocomplete>
																</v-col>
															</v-row>
															<v-btn color="primary white--text" rounded  @click="fnApplyFilter()"> Apply
															</v-btn>
															<v-btn color="primary" rounded outlined @click="resetFilterValues(1)"> Reset
															</v-btn>
														</v-card>
													</v-menu>
												</v-col>
												<span class="padding-2em"></span>
											</v-row>
											
											
									</v-tabs>
								</v-toolbar>
							</v-card>
							<!-- mobile topbar design -->
							<v-card class="bg_grey_lighten1 hidden-md-and-up" :class="topBarClass" flat height="4em">
								<v-toolbar color="grey lighten-5">
									<v-tabs v-model="currentTab" centered>
										<v-tab href="#tab-1" class="active_tab_bg">
											<div style="text-transform: capitalize !important;" class="font-weight-bold">Custom Employee Group</div>
										</v-tab>
									</v-tabs>
								</v-toolbar>
							</v-card>
							<!-- mobile footer design -->
							<v-bottom-navigation v-if="((isSearch || displayCustomGroupArray.length>0) && showList) || showView" fixed class="hidden-md-and-up" color="teal" elevation="15">
								<v-col :cols="showView ? 8 : 10">
									<v-text-field dense v-model="searchInput" @change="searchCustomGroup()" placeholder="Search" prepend-icon="search">
									</v-text-field>
								</v-col>
								<v-col v-if="showView" cols="4" class="text-xs-center justify-center d-flex">
									<v-menu fixed top offset-y offset-x v-model="filterOpenInMobile" :close-on-content-click="false" 
										class="filter-menu-position" max-height="400">
											<template v-slot:activator="{ on }">
												<v-btn fab small v-on="on" color="primary" class="mobile_view_filter_icon">
													<i class="fa fa-filter filter-icon-property"></i>
												</v-btn>
											</template>
											<v-card class="p-10" >
												<div class="d-flex justify-end">
													<v-icon color="primary" @click="filterOpenInMobile=!filterOpenInMobile" class="p-r-10 p-t-10">
														close</v-icon>
												</div>
												<v-row>
														<v-col cols="12">
															<v-autocomplete background-color="white" deletable-chips multiple
																v-model="deptSelectedInFilter" :items="departmentList" dense chips
																filled label="Department">
															</v-autocomplete>
														</v-col>
														<v-col cols="12">
															<v-autocomplete background-color="white" deletable-chips multiple
																v-model="designSelectedInFilter" :items="designationList" dense chips
																filled label="Designation">
															</v-autocomplete>
														</v-col>
													</v-row>
													<v-row class="m-t-n4">
														<v-col cols="12">
															<v-autocomplete background-color="white" deletable-chips multiple
																v-model="locSelectedInFilter" :items="locationList" dense
																chips filled label="Location">
															</v-autocomplete>
														</v-col>
														<v-col cols="12">
															<v-autocomplete background-color="white" deletable-chips multiple
																v-model="workSchSelectedInFilter" :items="workScheduleList" dense
																chips filled label="Work Schedule">
															</v-autocomplete>
														</v-col>														
													</v-row>
													<v-row class="m-t-n4">
														<v-col cols="12">
															<v-autocomplete background-color="white" deletable-chips multiple
																v-model="empTypeSelectedInFilter" :items="empTypeList" dense
																chips filled label="Employee type">
															</v-autocomplete>
														</v-col>
														<v-col cols="12">
															<v-autocomplete background-color="white" deletable-chips multiple
																v-model="stateSelectedInFilter" :items="stateList" dense
																chips filled label="Location State">
															</v-autocomplete>
														</v-col>
													</v-row>
													<v-row class="m-t-n4">
														<v-col v-if="fieldForce" cols="12">
															<v-autocomplete background-color="white" deletable-chips multiple
																v-model="serviceProviderSelectedInFilter" :items="serviceProviderList" dense
																chips filled label="Service provider">
															</v-autocomplete>
														</v-col>
														<v-col cols="12">
															<v-autocomplete background-color="white" deletable-chips multiple
																v-model="employeeStatusSelectedInFilter" :items="employeeStatusList" dense chips
																filled label="Employee Status">
															</v-autocomplete>
														</v-col>
													</v-row>
												<v-btn color="primary white--text" rounded @click="fnApplyFilter()"> Apply
												</v-btn>
												<v-btn color="primary" rounded outlined @click="resetFilterValues(1)"> Reset
												</v-btn>
										</v-card>
									</v-menu>
								</v-col>
							</v-bottom-navigation>
							<!--Fetching Error-->
						<div v-if="!loadingScreen">
							<v-row v-if="fetchError" class="custom-group-list-card-row"> 
								<v-col cols="12">
									<div>
										<fetching-error-screen button-text="Retry" :content="errorMessage" 
										main-title="" image-name="initial-fetch-error-image" 
										@button-click="retryFetching"></fetching-error-screen>
									</div>
								</v-col>
							</v-row>
							<div v-else-if="showList">
								<v-row class="custom-group-list-card-pagination-row">
									<v-col xl="10" lg="10" md="9" sm="12" cols="12" >
											<v-pagination v-if="displayCustomGroupArray.length>0" v-model="page" :length="paginationLength" :total-visible="totalVisiblePages" circle></v-pagination>
									</v-col>
									<v-col v-if="isSearch || displayCustomGroupArray.length>0" xl="2" lg="2" md="3" sm="12" cols="12" class="custom-group-btn">
											<v-btn class="m-t-5" rounded color="primary" @click="fnShowAddForm()" dark>
													<v-icon class="custom-group-add-icon">
														add
													</v-icon>Add New
											</v-btn>
									</v-col>
								</v-row>
								<v-row v-if="displayCustomGroupArray.length > 0" class="custom-group-list-card-row"> 
									<v-col v-for="(customGroup, index) in displayCustomGroupArray" :key="index" cols="12" xl="3" lg="3" md="6" sm="6" class="custom-group-list-card">
										<list-employee-group :index=index :unique-id="displayCustomGroupArray[index].Group_Id" :custom-group="displayCustomGroupArray" :card-heading="displayCustomGroupArray[index].Group_Name" :card-chip-data1 ="displayCustomGroupArray[index].linkedToForms"
										:card-chip-data2 ="displayCustomGroupArray[index].linkedFromForms" :card-content1 = displayCustomGroupArray[index].empDetails.totalEmpCount 
										:card-content2 = displayCustomGroupArray[index].empDetails.excludedCount @showcarddata="viewCustomGroup($event)"
										@showeditdata="fnEditCustomGroup($event)" @handle-error="handleErrorMessages($event)" @handle-msg="fnHandleMsgInSnackbar($event)"
										@open-add-remove-emp="openAddRemoveEmp($event)">
										</list-employee-group>
									</v-col>
								</v-row>
								<v-row v-else>
										<v-col v-if="isSearch" cols="12">
												<div class="display-flex-common">
														<img class="custom-group-no-records" :src="basePath+'images/custom-group-no-records.webp'" @error="imageFormatSwap" alt="No Records">
												</div>
												<div class="custom-group-no-records-txt primary--text display-flex-common">
														No custom employee group match your search.
												</div>
										</v-col>
										<v-col v-else cols="12">
												<div class="display-flex-common">
														<img class="custom-group-no-records" :src="basePath+'images/custom-group-no-records.webp'" @error="imageFormatSwap" alt="No Records">
												</div>
												<div class="custom-group-no-records-txt primary--text display-flex-common">
														No custom employee group created yet.
												</div>
												<div class="display-flex-common">
														<v-btn class="m-t-5" rounded color="primary" @click="fnShowAddForm()" dark>
																<v-icon class="custom-group-add-icon">
																	add
																</v-icon>Add Custom Employee Group
														</v-btn>
												</div>
										</v-col>
								</v-row>
							</div>
							<div v-else-if="showView" class="custom-group-view-group">
								  <view-employee-group :group-data="customGroupRetrieveData" :base-path="basePath" :view-search-value="viewSearchValue" :group-id = "groupId"
								  :group-id-index="groupIdIndex" @back-to-list="backToList()" @modal-opened="fnModalOpened($event)" :api-headers="apiHeaders"
								  @load-screen="loadingScreen = $event" @handle-error="handleErrorMessages($event)" :department-list="departmentList" :designation-list="designationList" 
								  :location-list = "locationList" :emp-type-list="empTypeList" :work-schedule-list="workScheduleList" :service-provider-list="serviceProviderList" :state-list="stateList" :field-force="fieldForce" :role-edit ="roleEdit" :current-tab-to-show = "currentTabToShow"
								  @handle-msg="fnHandleMsgInSnackbar($event)" @employee-update-success="employeeUpdateSuccess($event)" @group-added="fnAfterAdd($event)"></view-employee-group>
							</div>
						</div>
							

				</div>
				
				<div v-if="addGroupModal">
					<add-edit-group :open-modal="true" @closemodal="addGroupModal = false"  :group-data="customGroupRetrieveData" :api-headers="apiHeaders"
					@group-added="fnAfterAdd($event)" @handle-error="handleAddEditCustomGroupError($event)" ref="addEditComponent" :action-type="actionType"
					@operands-formed="filterOperands = $event" :filter-operands="filterOperands" @module-retrieved="modulesAndForms = $event" :modulesAndForms="modulesAndForms"
					@handle-msg="fnHandleMsgInSnackbar($event)" tab-to-display="tab-1" @load-screen="loadingScreen = $event" from="main-parent">
					</add-edit-group>
				</div>
				<!-- add-exclusive-inclusive-employees-modal component -->
				<add-exclusive-inclusive-employees-modal ref="addEmpModal" :retrieve-type="retrieveType" :group-id="groupId" :show-modal="showModal"
					:department-list="departmentList" :designation-list="designationList" :api-headers="apiHeaders"
					:location-list="locationList" :emp-type-list="empTypeList" :work-schedule-list="workScheduleList" :state-list="stateList" :service-provider-list="serviceProviderList" :field-force="fieldForce"
					:modal-type="(retrieveType === 'unmatched' || retrieveType === 'all') ? 'bulk' : 'single' "
					@handle-msg="fnHandleMsgInSnackbar($event)" @handle-error="handleErrorMessages($event)" @modal-opened="fnModalOpened($event)"
					@load-screen="loadingScreen = $event" @employee-update-success="employeeUpdateSuccess($event)" @show-addpopup="showAddPopUp($event)"
					img-path="custom-group-delete-image.webp" alt-img-path="custom-group-delete-image.png" :linked-from-forms = "linkedFromForms" :group-name="selectedGroupName">
				</add-exclusive-inclusive-employees-modal>
				<!-- snack bars for handle success and error messages -->
				<v-snackbar v-model="snackbar" :color="snackbarColor">
					{{ snackBarMsg }}
					<v-btn color="white" text @click="snackbar = false">
						X
					</v-btn>
				</v-snackbar>
					
			</v-app>
		</div>
		<div v-else class="d-flex justify-center align-center flex-column access-denied">
				<access-denied-screen></access-denied-screen> 
		</div>
			<!-- custom loading -->
			<custom-loading-screen v-if="loadingScreen"></custom-loading-screen>
		</template>
	</div>
		
	<!-- script content -->
	<script>
		Vue.use(VeeValidate)

		let primaryColor, secondaryColor;
		if (!localStorage.getItem("brand_color")) {
        	const { Primary_Color, Secondary_Color} = JSON.parse(localStorage.getItem("brand_color"));
			primaryColor = Primary_Color;
			secondaryColor = Secondary_Color;
		} else {
			primaryColor = '#260029';
			secondaryColor = '#ec407a';
		}
		// vue instance declarations
		var app = new Vue({
			el: '#customEmployeeGroup',
			vuetify: new Vuetify(
				{
					theme: {
						options: {
							customProperties: true,
						},
						themes: {
							light: {
								primary: primaryColor,
								secondary: secondaryColor,
								grey: '#9E9E9E',
								green : '#41ae57',
								blue : '#63759f'
							}
						}
					}
				}
			),
			data() {
				return {
					atsBaseURL : 'https://api.'+localStorage.getItem('domain')+'/ats/graphql',
					orgCode: localStorage.getItem("orgCode"),
					employeeId: parseInt(localStorage.getItem('LoginEmpId'), 10),

					addGroupModal: false,
					actionType: 'add',
					// list custom employee group - search, pagination
					customGroupArray : [],
					displayCustomGroupArray : [],
					searchedCustomGroup : [],
					filterOperands: [],
					modulesAndForms: [],
					dropDownDetailsRetrieved: false,

					// pagination
					lastIndex      : 0,
					page           : 1,
					currentPage    : 1,
					previousPage   : 1,
					paginationLength  : 0,
					totalVisiblePages : 7,
					limitPerPage   : 8,

					// search values
					showSearch: false,
					searchInput: '',

					// access rights
					roleView	 : 0,
					roleAdd 	 : 0,
					roleEdit     : 0,
					roleDelete   : 0,
					accessDenied   : false,

					// error msg
					snackBarMsg : '',
					snackbar    : false,
					snackbarColor : '',
					fetchError: false,
					errorMessage: "",

					loadingScreen  : false,
					showCustomGroup       : false,
					isSearch       : false,
					currentTab: 'tab-1',
					showList    : true,
					showView    : false,
					showDeleteModal : false,
					selectedGroupName: "",
					searchPlaceholder : 'Search by Group Name/Linked To/Linked From',
					viewSearchValue : '',
					listSearchVal : '',


					// view state values
					customGroupRetrieveData : {},
					groupId           : 0,
					groupIdIndex      : 0,
					topBarClass : 'custom-group-top-bar',


					// filter values
					filterOpen: false,
					filterOpenInMobile:false,
					isResetFilter: false,
					departmentList : [],
					designationList : [],
					locationList  : [],
					empTypeList : [],
					workScheduleList : [],
					stateList :[],
					serviceProviderList : [],
					fieldForce : 0,
					employeeStatusList: [],

					empTypeSelectedInFilter : [],
					serviceProviderSelectedInFilter : [],
					workSchSelectedInFilter : [],
					locSelectedInFilter     : [],
					designSelectedInFilter   : [],
					deptSelectedInFilter    : [],
					stateSelectedInFilter : [],
					employeeStatusSelectedInFilter: [],
					// include exclude employees
					succesMsg : '',
					isOpenView : true,
					retrieveType : '',
					showModal  : false,
					linkedFromForms: [],
					selectedGroupName: "",
					currentTabToShow : 'tab-1',
					// headers
					ipAddress: "",
					partnerid: $cookies.get("partnerid"),
					dCode: $cookies.get("d_code"),
					bCode: $cookies.get("b_code"),
				}
			},
			computed: {
				basePath() {
					if (localStorage.getItem('production') == 0) {
						return '/hrapponline/'
					} else {
						return '/'
					}
				},
				apiHeaders(){
					let authorizationHeader = $cookies.get("accessToken") ? $cookies.get("accessToken") : null;
					let refreshTokenHeader = $cookies.get("refreshToken") ? $cookies.get("refreshToken") : null;
					return{
						'Content-Type': 'application/json',
						org_code: this.orgCode,
						Authorization: authorizationHeader,
						refresh_token: refreshTokenHeader,
						user_ip: this.ipAddress,
						partnerid: this.partnerid ? this.partnerid : "-",
						additional_headers: JSON.stringify(
							{
								org_code: this.orgCode,
								Authorization: authorizationHeader,
								refresh_token: refreshTokenHeader,
								user_ip: this.ipAddress,
								partnerid: this.partnerid ? this.partnerid : "-",
								d_code: this.dCode,
								b_code: this.bCode,
							}
						)
					}
				},
			},
			created() {
				if($cookies.get('userIpAddress')) {
					this.ipAddress = $cookies.get('userIpAddress');
				} else {
					try{
						axios.get('https://api.ipify.org?format=json').then(response => { 
							this.ipAddress = response.data.ip;
						}).catch(error => {
							/* If the IP address API is not available, API URL is wrong or internet connection is not available,
							then the error.readyState will be 0 or 4  */
							if((error.readyState === 0 || error.readyState === 4) && ipAddressRestriction == 1) {
								this.snackBarMsg = "Unable to get the IP address. Please contact system Administrator."
								this.snackbarColor = "warning";
								this.snackbar = true;
							} else {
								this.ipAddress = "IP Blocked by user";
							}
						})
					}catch{
						this.ipAddress = "";
					}
				    window.$cookies.set("userIpAddress", this.ipAddress);
				}
			},
			watch : {
				page(val) {
					// change pagination
					this.formDisplayCustomGroupList(val);
				},
				addGroupModal(val) {
					// change the top bar class
					this.fnChangeTopBarClass(val);
				},
				filterOpen (val) {
				//   reset the isResetFilter to false when filter modal is opened
					if(val === true) {
					this.isResetFilter = false;
					}

				this.fnToDisableScroll(this.filterOpen);
				},
				filterOpenInMobile(val) {
					//   reset the isResetFilter to false when filter modal is opened
					if (val === true) {
						this.isResetFilter = false;
					}

					this.fnToDisableScroll(this.filterOpenInMobile);
				},
			},
			mounted() {
				setTimeout(()=>{
					this.loadingScreen = true;
					this.fnCheckAccessRights();
				},2000);

				this.$nextTick(function () {
					window.addEventListener('resize', this.getWindowWidth);
					//Init
					this.getWindowWidth()
				});
			},
			beforeDestroy() {
				window.removeEventListener('resize', this.getWindowWidth);
			},
			methods: {
				//function to switch image to png format if browser not support webp
				imageFormatSwap(e){
					e.target.src=this.basePath+'images/custom-group-no-records.png';
				},
				// hide and show of filter based on window size
				getWindowWidth(event) {
					this.windowWidth = document.documentElement.clientWidth;
					if (this.filterOpen && this.windowWidth <= 960) {
						this.filterOpen = false;
					}
					if (this.filterOpenInMobile && this.windowWidth > 960) {
						this.filterOpenInMobile = false;
					}

				},
				// check access rights
				fnCheckAccessRights() {
					var self = this;

					self.loadingScreen =true;
					var graph1 = graphql(self.atsBaseURL, {
						method: 'POST',
						headers: this.apiHeaders,
						asJSON: true
					});
					var accessRights = graph1(`mutation(
                    $formName: String,
                    $employeeId: Int!) {
						getAccessRights
							(
								formName: $formName,
								employeeId:$employeeId
							) 
							{
								errorCode message rights {
									Role_View Role_Add Role_Update Role_Delete Role_Optional_Choice Role_Hr_Group Role_Payroll_Group Is_Manager
							    }
							}
						}
                	`);
					accessRights({
						formName: 'Custom Employee Groups',
						employeeId: self.employeeId
					})
					.then(function (response) {
						if (response) {
							// rights retrieved successfully
							var response = response.getAccessRights.rights;
							self.roleView = response.Role_View;
							self.roleAdd = response.Role_Add;
							self.roleEdit = response.Role_Update;
							self.roleDelete = response.Role_Delete;
							self.loadingScreen = false;

							// check if the view access exists for Custom Employee Group form
							if(response.Role_View) {
								self.listCustomGroups(); //list Custom Employee groups
							}
							else {
								self.accessDenied = true; //set access denied
							}
						}
					})
					.catch(function (Err) {
						self.handleAccessRightsAPIError(Err);
					});
				},
				// list custom employee group
				listCustomGroups() {
						// fetch the custom employee group list
						var self = this;

						self.loadingScreen = true;

						// graphql url configuration
						var graph1 = graphql(self.atsBaseURL, {
							method: 'POST',
							headers: self.apiHeaders,
							asJSON: true
						});


						// query to list Custom Employee Group data
						var listCustomGroupData = graph1.query(`query ($isDropDownCall :Int! ,$employeeId : Int!) { listCustomGroups (isDropDownCall:$isDropDownCall ,employeeId:$employeeId) { errorCode message listCustomGroups }}`);

						// call listCustomGroupData to get the list of Custom Employee Groups
						listCustomGroupData({
							isDropDownCall : 0,
							employeeId   : self.employeeId,
						}).then(function (response){

							// list of custom Employee Group Array
							customGroupArray =  response.listCustomGroups;
							customGroupArray = JSON.parse(customGroupArray.listCustomGroups);

							// set the pagination length depending upon the custom employee group list length
							self.paginationLength = Math.ceil(customGroupArray.length / self.limitPerPage);
							self.customGroupArray = customGroupArray;
							// list to be displayed based on pagination
							self.loadingScreen = false;
							self.showCustomGroup = true;

							self.formDisplayCustomGroupList(self.currentPage);

						})
						.catch(function (Err) {
							self.handleListError(Err);
						});
						
				},
				
				// pagination
				formDisplayCustomGroupList(currentPage) {

					var indexToAdd = 0; // limit up to which the current page should show

					// check if the previous page is greater than current page
					if(this.previousPage < currentPage) {
						// add the limit to the previous limit
						indexToAdd = currentPage - this.previousPage;
						this.lastIndex = this.lastIndex + (this.limitPerPage * indexToAdd);
					}
					else if(this.previousPage > currentPage) {
						// remove the limit to the previous limit
						indexToAdd = this.previousPage - currentPage;
						this.lastIndex = this.lastIndex - (this.limitPerPage * indexToAdd);
					}
					else {
						this.lastIndex = this.lastIndex;
					}

					// check if the action is search
					if(this.searchInput) {
						var customGroupArray = this.searchedCustomGroup;
						this.displayCustomGroupArray = [];
						// loop through the data to show based on pagination
						for(var i = this.lastIndex ; i < customGroupArray.length ; i++) {
							if(i < this.lastIndex+this.limitPerPage) {
								this.displayCustomGroupArray.push(customGroupArray[i]);
							}
						}
					}
					else {
						// the data to be displayed
						this.displayCustomGroupArray = [];
						// loop through the data to show based on pagination
						for(var i = this.lastIndex ; i < this.customGroupArray.length ; i++) {
							if(i < this.lastIndex+this.limitPerPage) {
								this.displayCustomGroupArray.push(this.customGroupArray[i]);
							}
						}
					}

					

					this.previousPage = currentPage;
					this.currentPage = currentPage;
				},
				// clients side search
				searchCustomGroup() {

					if(this.showList) {
						// search input
						var searchValue = this.searchInput.toLowerCase();

						// check if any search value exists
						if(searchValue) {
							// enable the isSearch flag
							this.isSearch = true;

							this.searchedCustomGroup = [];
							// loop and check throughout the array
							for (var i in customGroupArray) {
								var GroupName = customGroupArray[i].Group_Name.toLowerCase();
								// check if group name match the search value
								if(GroupName.includes(searchValue)) {
									this.searchedCustomGroup.push(customGroupArray[i]);
								}
								// check linked to forms matches the search value
								else {
									var linkedToForms = customGroupArray[i].linkedToForms;
									var linkedFromForms = customGroupArray[i].linkedFromForms;
									for(var j in linkedToForms) {
										var linkedToName = linkedToForms[j].toLowerCase();
										
										if(linkedToName.includes(searchValue)) {
											this.searchedCustomGroup.push(customGroupArray[i])
											break;
										}
										else {
											for(var k in linkedFromForms) {
												var linkedFromName = linkedFromForms[k].toLowerCase();
										
												if(linkedFromName.includes(searchValue)) {
													this.searchedCustomGroup.push(customGroupArray[i])
													break;
												}
											}
										}
									}

									
								}
							}

							// reset the pagination according to new array
							if(this.searchedCustomGroup.length > 0) {
								this.resetPagination(1);
							}
							else {
								this.displayCustomGroupArray = [];
							}
						}
						// if no search value
						else {
							this.isSearch = false;

							// reset the pagination
							this.resetPagination(0)
							
						}
					}
					else {
						this.viewSearchValue = this.searchInput.toLowerCase();
					}
				},
				// reset the pagination to new array of values
				resetPagination(hasSearchValue) {
					this.lastIndex = 0,
					this.currentPage    = 1;
					this.previousPage   = 1;
					// set the pagination length depending upon the custom employee group list length
					hasSearchValue ? this.paginationLength = Math.ceil(this.searchedCustomGroup.length / this.limitPerPage) : this.paginationLength = Math.ceil(this.customGroupArray.length / this.limitPerPage);

					this.page == 1 ? this.formDisplayCustomGroupList(1) : this.page = 1 ;
				},

				handleAccessRightsAPIError(error) {
					var self = this;
					self.loadingScreen = false;
					let errorCode;
					if(error && error.errors && error.errors.length > 0) {
						errorCode = error.errors[0].extensions.code;
					} else if(error && error.length > 0 && error[0].message) {
						let errors = JSON.parse(error[0].message);
						errorCode = errors.errorCode;
					}
					if (errorCode) {
						switch (errorCode) {
							//technical errors 
							case '_DB0000': // db connection
							case 705:
							case 706:
								this.errorMessage = 'There seems to be some technical issues. Please try after some time.';
								break;
							case 752: //access denied 
								this.accessDenied = true;
							break;
							case 751: // Could not check access rights
							case "_UH0001": // unhandled error
							default:
								this.errorMessage = 'Something went wrong. If you continue to see this issue please contact system administrator.';
								break;
						}
					} else {
						this.errorMessage = 'Something went wrong. Please contact system administrator.';
					}
					this.fetchError = true;
				},

				handleListError(error) {
					var self = this;
					self.loadingScreen = false;
					let errorCode;
					if(error && error.errors && error.errors.length > 0) {
						errorCode = error.errors[0].extensions.code;
					} else if(error && error.length > 0 && error[0].message) {
						let errors = JSON.parse(error[0].message);
						errorCode = errors.errorCode;
					}
					if (errorCode) {
						switch (errorCode) {
							//technical errors 
							case '_DB0000': // db connection
								this.errorMessage = 'There seems to be some technical issues. Please try after some time.';
								break;
              				case "_DB0100": // view access denied
								this.accessDenied = true;
							break;
							case "_UH0001": // unhandled error
							case "ERE0115": // Error while process the request for listing the custom employee groups.
							case "ERE0015": // Error while listing the custom employee groups.
							default:
								this.errorMessage = 'Something went wrong. If you continue to see this issue please contact system administrator.';
								break;
						}
					} else {
						this.errorMessage = 'Something went wrong. Please contact system administrator.';
					}
					this.fetchError = true;
				},

				handleCustomGroupRetrieveError() {
					var self = this;
					self.loadingScreen = false;
					let errorCode;
					if(error && error.errors && error.errors.length > 0) {
						errorCode = error.errors[0].extensions.code;
					} else if(error && error.length > 0 && error[0].message) {
						let errors = JSON.parse(error[0].message);
						errorCode = errors.errorCode;
					}
					if (errorCode) {
						switch (errorCode) {
							//technical errors 
							case '_DB0000': // db connection
								self.snackBarMsg = 'There seems to be some technical issues. Please try after some time.';
								break;
              				case "_DB0100": // view access denied
							  	self.snackBarMsg = "Sorry, you don't have access to view the custom group. Please contact HR administrator.";
								break;
							case "_EC0001": // No record found
								self.snackBarMsg = "Unable to retrieve the selected group details as the records is deleted in same or some other user session."
								break;
							case "_UH0001": // unhandled error
							case "ERE0018": // Error while retrieving the custom employee groups.
							case "ERE0118": // Error while process the request for retrieving the custom employee groups.
							default:
								self.snackBarMsg = 'Something went wrong. If you continue to see this issue please contact system administrator.';
								break;
						}
					} else {
						self.snackBarMsg = 'Something went wrong. Please contact system administrator.';
					}
					self.snackbar = true;
					self.snackbarColor = "red lighten-1";
				},

				handleAddEditCustomGroupError(error) {
					var self = this;
					self.loadingScreen = false;
					let errorCode, errors;
					if(error && error.errors && error.errors.length > 0) {
						errorCode = error.errors[0].extensions.code;
					} else if(error && error.length > 0 && error[0].message) {
						errors = JSON.parse(error[0].message);
						errorCode = errors.errorCode;
					}
					if (errorCode) {
						switch (errorCode) {
							//technical errors 
							case '_DB0000': // db connection
								self.snackBarMsg = 'There seems to be some technical issues. Please try after some time.';
								break;
              				case "_DB0101": // add access denied
							  	self.snackBarMsg = "Sorry, you don't have access to create a custom group. Please contact HR administrator.";
								break;
              				case "_DB0102": // update access denied
							  	self.snackBarMsg = "Sorry, you don't have access to update a custom group. Please contact HR administrator.";
								break;
							case "_EC0001": // No record found
								self.snackBarMsg = "Unable to retrieve the selected group details as the records is deleted in same or some other user session."
								break;
							case "IVE0000": // validation error
								let validationErrors = Object.keys(errors.validationError), validationErrorMessage = "";
								for(var validationError of validationErrors) {
									validationErrorMessage += errors.validationError[validationError];
								}
								if(validationErrorMessage)
									self.snackBarMsg = validationErrorMessage;
								else
									self.snackBarMsg = 'Something went wrong. If you continue to see this issue please contact system administrator.';
								break;
							case "_UH0001": // unhandled error
							case "ERE0116": // Error while process the request for creating the custom employee groups.
							case "ERE0016": // Error while creating the custom employee groups.
							case 'ERE0014': // Error while updating the custom employee groups.
							case 'ERE0114': // Error while process the request for updating the custom employee groups.
							default:
								self.snackBarMsg = 'Something went wrong. If you continue to see this issue please contact system administrator.';
								break;
						}
					} else {
						self.snackBarMsg = 'Something went wrong. Please contact system administrator.';
					}
					self.snackbar = true;
					self.snackbarColor = "red lighten-1";
				},

				retryFetching() {
					this.fetchError = false;
					this.fnCheckAccessRights();
				},

				// handle error messages from backend
				handleErrorMessages(Err) {
					console.log("Err:", Err);
					var self = this;
					self.loadingScreen = false;
						if (Err[0]) {
							var accessRightsErr = JSON.parse(Err[0].message);
							var errorCode = accessRightsErr.errorCode;
							switch (errorCode) {
								case '_DB0000':
									self.snackbar = true;
									self.snackbarColor = "red lighten-1";
									self.snackBarMsg = "There seems to be some technical issue. Please try after some time."
									if(!self.isOpenView) {
										self.isOpenView = true;
									}
								break;
								case 'ERE0110' :
									// check error is in view page
								    if(!self.isOpenView) {
										self.showView = false;
										self.snackbar = true;
										self.snackbarColor = "success lighten-1";
										self.snackBarMsg = "Default Employee(s) moved to Exclusion list and Additional Employee(s) removed from the group successfully. But some of the employee(s) not removed as Leaves/Leave Balance/Defered Leave is updated for them."
										self.isOpenView = true;
									}
									else {
										self.snackbar = true;
										self.snackbarColor = "red lighten-1";
										self.snackBarMsg = "This employee is not removed as Leaves/Leave Balance/Deferred Leave is updated for them."
									}
									break;
								case 'ERE0125':
									self.snackbar = true;
									self.snackbarColor = "orange lighten-1";
									self.snackBarMsg = "Employee cannot be removed from a default/additional inclusion list since the group is associated with the forms."
								break;
								default:
									self.snackbar = true;
									self.snackbarColor = "red lighten-1";
									self.snackBarMsg = "Something went wrong. Please contact system administrator."
									if(!self.addGroupModal || !self.isOpenView || !self.showModal) {
										self.isOpenView = true;
									}
								break;
                			}
						}
						else {
							self.snackbar = true;
							self.snackbarColor = "red lighten-1";
							self.snackBarMsg = "Something went wrong. Please contact system administrator."
						}
						// close the opened modal if the action is delete
						if(self.retrieveType == 'delete') {
							self.$refs.addEmpModal.openModal = false;
							self.showModal = false;
						}
				},
				// handle messages in snackbar
				fnHandleMsgInSnackbar(msg){
					this.snackbar = true;
					this.snackbarColor = "orange lighten-1";
					this.snackBarMsg = msg;
				},
				viewCustomGroup(value,isView=true) {
					if(this.isOpenView) {
						// check if the user has the view access rights
						if(this.roleView) {
							var self = this;
							var groupIdIndex = ''
							// check if the view action is from list page or after add
							if(isView) {
								// set the group Id and group index(for avatar)
								self.groupId = value[0];
								groupIdIndex = value[1];
								self.groupIdIndex = groupIdIndex;

							}
							else {
								// set the group Id and group index(for avatar)
								self.groupId =  value;
								groupIdIndex = 0;
								self.groupIdIndex = groupIdIndex;
							}
							//Set the default value
							self.employeeStatusSelectedInFilter = ["Active"];
							self.loadingScreen = true;

							// retrieve custom employee group data
							self.fnRetrieveCustomGroupData(isView,'view');						
						}
						// show access denied alert
						else {
							this.snackbar = true;
							this.snackbarColor = "orange lighten-1";
							this.snackBarMsg = "You don't have access to view group details. Please contact system admin to get access for this form."
						}
					}
				},
				// show list page
				backToList() {
					$(window).scrollTop(0);
					this.showView = false;
					this.showList = true;
					this.searchPlaceholder = "Search by Group Name/Linked to/Linked From";
					this.searchInput = this.listSearchVal;
					this.retrieveType = '';
					// reset the filter when view page is closed
					this.resetFilterValues(0);

				},
				// fn to disable scroll when menu is opened
				fnToDisableScroll(val){
					if (val) {
						document.documentElement.style.overflow = 'hidden';
						return;
					}
					document.documentElement.style.overflow = 'auto';
				},
				fnRetrieveCustomGroupData(isView,action) {

					var self = this;

					// graphql url configuration
					var graph1 = graphql(self.atsBaseURL, {
							method: 'POST',
							headers: this.apiHeaders,
							asJSON: true
						});


						// query to retrieve custom employee group data
						var retrieveCustomGroupData = graph1.query(`query($groupId: Int!,$action : String!,$designation:[Int], $department:[Int],$employeeType:[Int],$workSchedule:[Int],$location:[Int], $serviceProvider:[Int], $state:[Int], $employeeId:Int!, $employeeStatus:[String]) { retrieveCustomGroup (groupId:$groupId, action:$action,designation:$designation,department:$department, employeeType:$employeeType, workSchedule:$workSchedule, location:$location, serviceProvider:$serviceProvider, state:$state, employeeId:$employeeId, employeeStatus: $employeeStatus) { errorCode message customGroupData }}`);

					// call retrieveCustomGroupData to get the retrieve Custom Employee Groups data
					retrieveCustomGroupData({
							groupId : self.groupId,
							action   : action == 'edit' ? 'update' :'view',
							employeeId  : self.employeeId,
							designation : self.designSelectedInFilter,
							department  : self.deptSelectedInFilter,
							employeeType : self.empTypeSelectedInFilter,
							workSchedule : self.workSchSelectedInFilter,
							location : self.locSelectedInFilter,
							serviceProvider : self.serviceProviderSelectedInFilter,
							state : self.stateSelectedInFilter,
							fieldForce : self.fieldForce,
							employeeStatus: self.employeeStatusSelectedInFilter
						}).then(function (response){
							// success response
							var response = response.retrieveCustomGroup;

							response = JSON.parse(response.customGroupData);

							self.customGroupRetrieveData = response;

							self.currentTabToShow = self.retrieveType === 'unmatched' ? 'tab-2' : self.retrieveType === 'all' ? 'tab-3' : 'tab-1';


							// hide the list and show the view page
							$(window).scrollTop(0);

							// close the add modal if the view action is not from view 
							if(action == 'view' || action == 'success' || action == 'filter') {
								self.showList = false;
								self.showView = true;
								self.listSearchVal = self.searchInput;
								self.searchInput = '';
								self.searchPlaceholder = "Search by Employee Name/Employee Id";

								// close the add modal if the view action is not from view 
								if(!isView) {
									self.addGroupModal = false;					
								}	
								if(action == 'success') {
								self.snackbar = true;
								self.snackbarColor = "success lighten-1";
								self.snackBarMsg = self.succesMsg;

								}			
							}else{
								self.actionType = 'edit';
								self.addGroupModal = true;
							}
							
							self.loadingScreen = false;

						})
						.catch(function (Err) {
							self.handleCustomGroupRetrieveError(Err);
						});
				},
				fetchDropDownData() {
					var self = this;
					if(!self.dropDownDetailsRetrieved) {
						// graphql url configuration
					    var graph2 = graphql(self.atsBaseURL, {
							method: 'POST',
							headers: this.apiHeaders,
							asJSON: true
						});

						// dropdown query
						var fetchDropdownDetails = graph2.query(`samplequery { getDropDownBoxDetails { errorCode message designations { Designation_Id Designation_Name } departments { Department_Id Department_Name} workSchedules { WorkSchedule_Id Title} locations { Location_Id Location_Name} employeeType { EmpType_Id Employee_Type} serviceProvider { Service_Provider_Id Service_Provider_Name} state {State_Id State_Name} fieldForce}}`);
						fetchDropdownDetails().then(dropDownData => {
							var deptList = dropDownData.getDropDownBoxDetails.departments;
							var designList = dropDownData.getDropDownBoxDetails.designations;
							var locList = dropDownData.getDropDownBoxDetails.locations;
							var empTypeLists = dropDownData.getDropDownBoxDetails.employeeType;
							var workSchList = dropDownData.getDropDownBoxDetails.workSchedules;
							var stateLists = dropDownData.getDropDownBoxDetails.state;
							var servProviderList = dropDownData.getDropDownBoxDetails.serviceProvider;
							var fieldForce = dropDownData.getDropDownBoxDetails.fieldForce;
							self.fieldForce = fieldForce;
							let employeeStatusList = [{
									Emp_Status_Id: 'Active',                                                                                                                                                          Emp_Status_Id: 'Active',
									Emp_Status: 'Active'
								},
								{
									Emp_Status_Id: 'InActive',
									Emp_Status: 'InActive'
								}
							];

							// change value and text property for the select field props
							self.departmentList = replaceKeyInObjectArray(deptList, { "Department_Id": "value" });
							self.departmentList = replaceKeyInObjectArray(self.departmentList, { "Department_Name": "text" });

							self.designationList = replaceKeyInObjectArray(designList, { "Designation_Id": "value" });
							self.designationList = replaceKeyInObjectArray(self.designationList, { "Designation_Name": "text" });
							self.locationList = replaceKeyInObjectArray(locList, { "Location_Id": "value" });
							self.locationList = replaceKeyInObjectArray(self.locationList, { "Location_Name": "text" });
							self.empTypeList = replaceKeyInObjectArray(empTypeLists, { "EmpType_Id": "value" });
							self.empTypeList = replaceKeyInObjectArray(self.empTypeList, { "Employee_Type": "text" });
							self.workScheduleList = replaceKeyInObjectArray(workSchList, { "WorkSchedule_Id": "value" });
							self.workScheduleList = replaceKeyInObjectArray(self.workScheduleList, { "Title": "text" });
							self.stateList= replaceKeyInObjectArray(stateLists, { "State_Id": "value" });
							self.stateList = replaceKeyInObjectArray(self.stateList, { "State_Name": "text" });
							self.serviceProviderList= replaceKeyInObjectArray(servProviderList, { "Service_Provider_Id": "value" });
							self.serviceProviderList = replaceKeyInObjectArray(self.serviceProviderList, { "Service_Provider_Name": "text" });
							self.employeeStatusList= replaceKeyInObjectArray(employeeStatusList, { "Emp_Status_Id": "value" });
							self.employeeStatusList = replaceKeyInObjectArray(self.employeeStatusList, { "Emp_Status": "text" });							
							self.dropDownDetailsRetrieved = true;
						}).catch(Err => {
							if(!self.dropDownDetailsRetrieved) {
								self.departmentList = [];
								self.departmentList = [];
								self.designationList = [];
								self.designationList = [];
								self.locationList = [];
								self.locationList = [];
								self.empTypeList = [];
								self.empTypeList = [];
								self.workScheduleList = [];
								self.workScheduleList = [];
								self.stateList= [];
								self.stateList = [];
								self.serviceProviderList= [];
								self.serviceProviderList = [];
								self.employeeStatusList= [];
								self.employeeStatusList = [];
							}
						})
					}
				},
				// when filter is applied
				fnApplyFilter() {
					var isView = true;

					// close the filter menu
					this.filterOpen = false;
					this.filterOpenInMobile = false;
					this.fnRetrieveCustomGroupData(isView,'view');
				},
				resetFilterValues(value) {
					// close the filter menu
					this.filterOpen = false;
					this.filterOpenInMobile = false;

					// reset the filter values to initial values
					this.empTypeSelectedInFilter = [];
					this.workSchSelectedInFilter = [];
					this.locSelectedInFilter     = [];
					this.designSelectedInFilter   = [];
					this.deptSelectedInFilter    = [];
					this.serviceProviderSelectedInFilter = [];
					this.stateSelectedInFilter =[];
					this.employeeStatusSelectedInFilter=[];

					// check if the reset is from filter popup (value - 1) or during back to list (value - 0)
					if(value) {
						var isView = true;
						//Set the default value during reset
						this.employeeStatusSelectedInFilter = ["Active"];
						this.fnRetrieveCustomGroupData(isView,'filter');
					}
				},
				// fucntion to change tob bar class
				fnChangeTopBarClass(val){
					// change the top bar class
					if(val == true) {
						this.topBarClass = 'custom-group-top-bar-index'
					}
					else {
						this.topBarClass = 'custom-group-top-bar'
					}
				},
				// function to decrease opacity topbar when modal is opened
				fnModalOpened(val){
					this.fnChangeTopBarClass(val);
				},
						
				// function to show add form
				fnShowAddForm(){
					// to check the user have add access rights
					if(this.roleAdd == 1){
						this.actionType = 'add';
						this.customGroupRetrieveData = {};
						this.addGroupModal = true
					}else{
						this.snackbar = true;
						this.snackbarColor = "orange lighten-1";
						this.snackBarMsg = "You don't have access to create group. Please contact system admin to get access for this form."
					}
				},
				// function to call after add is succeed
				fnAfterAdd(val){
					if(val[1] == "fromView"){
						this.snackbar = true;
						this.snackbarColor = "success lighten-1";
						this.snackBarMsg = "Custom employee group updated successfully.";
						this.viewCustomGroup(val[0],false);
					}
					else if(((this.$refs.addEditComponent && this.$refs.addEditComponent.tabButton != "Next") && val[1] == "fromButton")){
						this.snackbar = true;
						this.snackbarColor = "success lighten-1";
						this.snackBarMsg = this.actionType == 'add' ? "Custom employee group added successfully." : "Custom employee group updated successfully.";
						this.viewCustomGroup(val[0],false);
					}
					this.listCustomGroups();
				},
				// function to show edit form
				fnEditCustomGroup(value){
					if(this.roleEdit){
						this.loadingScreen = true;
						this.groupId = value[0];
						this.fnRetrieveCustomGroupData(value[0],'update')
					}
					// show access denied alert
					else {
						this.snackbar = true;
						this.snackbarColor = "orange lighten-1";
						this.snackBarMsg = "You don't have access to edit group details. Please contact system admin to get access for this form."
					}
				},
				employeeUpdateSuccess(succesMsg) {
					if(this.retrieveType != 'delete'){
						this.loadingScreen = true;

						var isView = true;

						this.succesMsg = succesMsg;
						// retrieve the custom employee group data
						this.fnRetrieveCustomGroupData(isView,'success');
					}else{
						this.snackbar = true;
						this.snackbarColor = "success lighten-1";
						this.snackBarMsg = "Custom employee group deleted successfully."
						this.$refs.addEmpModal.openModal = false;
						this.showModal = false;
						this.listCustomGroups();
					}
				},
				openAddRemoveEmp(groupDetails) {
				
					var retrieveType = groupDetails[0];
					this.groupId = groupDetails[1];
					var index =groupDetails[2];
				  	this.isOpenView = false;
				  	if(retrieveType === 'unmatched' || retrieveType === 'all') {
						if(this.roleEdit) {
							this.fetchDropDownData();
							this.retrieveType = retrieveType;
							this.showModal = true;
						}
						else {
							this.snackbar = true;
							this.snackbarColor = "orange lighten-1";
							this.snackBarMsg = "You don't have access to edit group details. Please contact system admin to get access for this form."
							this.isOpenView = true;
						}
					}
					else {
						if(this.roleDelete) {
							this.retrieveType = retrieveType;
							this.linkedFromForms = this.displayCustomGroupArray[index].linkedFromForms;
							this.selectedGroupName = this.displayCustomGroupArray[index].Group_Name;
							this.showModal = true;
						}
						else {
							this.snackbar = true;
							this.snackbarColor = "orange lighten-1";
							this.snackBarMsg = "You don't have access to delete group details. Please contact system admin to get access for this form."
							this.isOpenView = true;
						}
					}
					
				},
				showAddPopUp(val) {
		            this.fnModalOpened(val);
					if(!val) {
						this.showModal = false;
						this.isOpenView = true;
					}
					else {
						this.isOpenView = false;
					}
					
        		}
			}

		})
		// rename key in object
		let replaceKeyInObjectArray = (a, r, selValue = []) => a.map(o => {
			if(selValue.length > 0) {
				let value = o ? o.value : "";
				Object.assign(o, {disabled: selValue && selValue.includes(value) ? true : false});
			}
			return Object.keys(o).map((key) => ({ [r[key] || key]: o[key] })
			).reduce((a, b) => Object.assign({}, a, b))
		});



	</script>
</body>

</html>
