<?php
    $this->headTitle($this->formNameA);
	
	$pfUser          = $this->pfUser;
	$pfPaymentUser   = $this->pfPaymentUser;
	$pfRulesUser     = $this->pfRulesUser;
	$employeeName    = $this->employeeName;
	$paymentModePair = $this->paymentMode;	
    
    $empDepartment = array();
	
	foreach ($employeeName as $key => $row) {
		if (!in_array($row['Department_Name'], $empDepartment)) {
			array_push($empDepartment, $row['Department_Name']);
		}
	}
	
	$customFormNameA = $this->customFormName;
	$customFormNameB = $this->customFormNameB;
	$customFormNameC = $this->customFormNameC;

if($pfUser['View'] || $pfRulesUser['View']) {?>
	<div class="col-md-12 portlets paddingCls tab-spacing-cls">
		<div class="col-md-12 paddingCls bg-f9f9f9 tab-wrapper">
			<?php if ($pfUser['View']) { ?>
				<div class="pointer-cls border-bottom-secondary bg-f9f9f9 tab-body" id="pfTab">
					<div class="tab-text-font custom-tab-content" id="pfFormTab">
						<a id="formTabLink1" class="tab-a-tag-color" href=<?php echo $this->baseUrl('/v3/tax-and-statutory-compliance/statutory-components/provident-fund?tab=providentFund'); ?>>
							<?php echo !empty($customFormNameA['New_Form_Name']) ? $customFormNameA['New_Form_Name'] : $this->formNameA ?>
						</a>
					</div>
				</div>
			<?php } ?>
			<div class="pointer-cls border-bottom-secondary tab-border-cls bg-f9f9f9 tab-body" id="pfTrackerTab">
				<div class="tab-active-text tab-text-font text-secondary custom-tab-content"  id="pfTrackerFormTab">
					<?php echo !empty($customFormNameB['New_Form_Name']) ? $customFormNameB['New_Form_Name'] : $this->formNameB ?>
				</div>
			</div>
			<?php if ($pfRulesUser['View']) { ?>
				<div class="pointer-cls border-bottom-secondary bg-f9f9f9 tab-body" id="pfRulesTab">
					<div class="tab-text-font custom-tab-content" id="pfRulesFormTab">
						<a id="formTabLink2" class="tab-a-tag-color" href=<?php echo $this->baseUrl('/v3/tax-and-statutory-compliance/statutory-components/provident-fund?tab=providentFundRules'); ?>>
							<?php echo !empty($customFormNameC['New_Form_Name']) ? $customFormNameC['New_Form_Name'] : $this->formNameC ?>
						</a>
					</div>
				</div>
			<?php } ?>
		</div>
	</div>
<?php }	?>

<?php
    if ($pfUser['View'] == 1 && ((!empty($customFormNameA) && array_key_exists("Enable",$customFormNameA) && $customFormNameA['Enable'] == 1) || empty($customFormNameA))) {
		$this->headTitle($customFormNameA['New_Form_Name']!='' ? $customFormNameA['New_Form_Name'] : $this->formNameA);
?>

<div class="col-md-12 portlets hidden">
	<div class="panel" id="gridPanelProvidentFund">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong id="lblFormNameA"><?php echo ($customFormNameA['New_Form_Name']!='' ? $customFormNameA['New_Form_Name'] : $this->formNamePf);?></strong></h3>
		</div>
		<div class="panel-content">			
			<div class="m-b-10">
					<!-- Add Button in Grid Toolbar -->
                    <?php if ($pfUser['Add']) { ?>
					<button type="button" class="btn btn-white btn-embossed toolbar-icons" title="Add" data-toggle="modal" data-target="#modalFormProvidentFund" id="addProvidentFund">
						<i class="mdi-content-add"></i><span class="hidden-xs hidden-sm"> Add</span>
					</button>
					
					<?php }?>
					
                    <!-- View Button in Grid Toolbar -->
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="viewProvidentFund" title="View">
						<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
					</button>
                    
					<!-- Update Button in Grid Toolbar -->
					<?php if ($pfUser['Update']) { ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="editProvidentFund" title="Edit">
						<i class="mdi-editor-mode-edit"></i><span class="hidden-xs hidden-sm"> Edit</span>
					</button>
					<?php } ?>
					
					<!-- Delete Button in Grid Toolbar -->
					<?php if ($pfUser['Delete']) { ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="deleteProvidentFund" title="Delete">
						<i class="mdi-action-delete"></i><span class="hidden-xs hidden-sm"> Delete</span>
					</button>
					<?php } ?>
					
					<!-- Copy Pf Button in Grid Toolbar -->
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="Bulk copy" id="copyProvidentFund">
						<i class="mdi-action-info-outline"></i><span class="hidden-xs hidden-sm"> Bulk Copy</span>
					</button>
					
                    <!-- Filter Button in Grid Toolbar -->
                    <a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterProvidentFund">
                        <i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm">Filter</span>
                    </a>					
			</div>
			
            <!--Provident Fund Grid -->			
			<table class="table dataTable table-striped table-dynamic table-hover" id="tableProvidentFund">
				<thead>
					<tr>
						<th></th>
						<th id="providentFundEmpId">Employee Id</th>
						<th id="providentFundCoverage">Coverage</th>
						<th id="providentFundSalaryType">Salary Type</th>
						<th id="providentFundEmpName">Employee Name</th>
						<th id="providentFundEmpShareAmount">Emp Share Amount</th>
						<th id="providentFundOrgShareAmount">Org Share Amount</th>
						<th id="providentFundEmpSharePercentage">Emp Share (%)</th>
						<th id="providentFundOrgSharePercentage">Org Share (%)</th>
						<th id="providentFundStatutoryLimit">Statutory Limit</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
		</div>
	</div>
</div>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="providentfund-context-menu" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextProvidentFund"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($pfUser['Update']) { ?>
		<li><a tabindex="-1" id="editContextProvidentFund"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<?php } if ($pfUser['Delete']) { ?>
		<li><a tabindex="-1" id="deleteContextProvidentFund"><i class="mdi-action-delete"></i> Delete</a></li>
		<?php } ?>
		<li><a tabindex="-1" id="copyContextProvidentFund"><i class="mdi-action-info-outline"></i> Bulk Copy</a></li>
	</ul>
</div>

<!--Filter Form-->
<div class="builder" id="filterPanelProvidentFund">
	<div id="closeFilterProvidentFund"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel filterReset" style="width: 100%;" id="cancelProvidentFund">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="applyProvidentFund">Apply</button>
						
			<?php if ($pfUser['View'] == 1 && $pfUser['Is_Manager'] == 0 && empty($pfUser['Admin'])) { ?>			
			<!--Filter for Employee Name-->
			<div class="form-group">
				<label>Employee Name</label>
				<!--<input type="text" class="form-control" id="filterEmployeeName" readonly="readonly" value="<php echo $pfUser['Employee_Name']['Emp_First_Name'] .' '. $pfUser['Employee_Name']['Emp_Last_Name']; ?>" >-->				
				<input type="text" class="form-control" id="filterEmployeeName" placeholder="Employee Name" >
			</div>
			
			<?php } else { ?>
			
			<div class="form-group">
				<label>Employee Name</label>
				<input type="text" class="form-control" id="filterEmployeeName" placeholder="Employee Name" >
			</div>
			
			<?php } ?>
			
			<!--Filter For Employee Share Percent -->
			<div class="form-group">
				<label>Employee Share Percent</label>
				<div class="input-group">
					<input type="number" class="form-control" name="pfemployeeShareStart" id="filterpfempShareStart" min="0" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="form-control" name="pfemployeeShareEnd" id="filterpfempShareEnd" min="0" placeholder="End"/>
				</div>
			</div>
			
			<!--Filter For Company Share Percent -->
			<div class="form-group">
				<label>Company Share Percent</label>
				<div class="input-group">
					<input type="number" class="form-control" name="pfcompanyShareStart" id="filterpfcomShareStart" min="0" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="form-control" name="pfcompanyShareEnd" id="filterpfcomShareEnd" min="0" placeholder="End"/>
				</div>
			</div>
			
			<!--Filter For Employee Share Amount -->
			<div class="form-group">
				<label>Employee Share Amount</label>
				<div class="input-group">
					<input type="number" class="form-control" name="pfemployeeShareAmountStart" id="filterpfempShareAmountStart" min="0" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="form-control" name="pfemployeeShareAmountEnd" id="filterpfempShareAmountEnd" min="0" placeholder="End"/>
				</div>
			</div>
			
			<!--Filter For Company Share Amount -->
			<div class="form-group">
				<label>Company Share Amount</label>
				<div class="input-group">
					<input type="number" class="form-control" name="pfcompanyShareAmountStart" id="filterComShareAmountStart" min="0" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="form-control" name="pfcompanyShareAmountEnd" id="filterComShareAmountEnd" min="0" placeholder="End"/>
				</div>
			</div>			
		</div>
	</div>
</div>

<!--Modal for provident fund view,add & edit form-->
<div class="modal fade" id="modalFormProvidentFund" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace" id="backPF"></i>
				</button>
				
				<?php if ($pfUser['Update']) { ?>
				<button type="button" class="close form-icons" aria-hidden="true" id="editInViewProvidentFund">
					<i class="mdi-editor-mode-edit"></i>
				</button>
				<?php } ?>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<!--View ProvidentFund Form-->
				<form role="form" id="viewFormProvidentFund" >
					<div class="row">
						<div class="form-group">
							<div class="col-md-5"><label class="control-label"> Coverage</label></div>
							<div class="col-md-7"><p id="viewPfCoverage"></p></div>
						</div>
						<div class="form-group employeeBasedHidden">
							<div class="col-md-5"><label class="control-label"> Employee Name</label></div>
							<div class="col-md-7"><p id="viewPfEmployeeName"></p></div>
						</div>
						<div class="form-group companyBasedHidden">
							<div class="col-md-5"><label class="control-label"> Salary Type</label></div>
							<div class="col-md-7"><p id="viewPfSalaryType"></p></div>
						</div>
						<div class="form-group companyBasedHidden">
							<div class="col-md-5"><label class="control-label"> Define Statutory Salary Limit</label></div>
							<div class="col-md-7"><p id="viewPfStatutoryLimit"></p></div>
						</div>
						<div class="form-group companyBasedHidden SSalaryBasedHidden">
							<div class="col-md-5"><label class="control-label"> Statutory Salary Limit</label></div>
							<div class="col-md-7"><p id="viewPfStatutorySalary"></p></div>
						</div>
						<div class="form-group companyBasedHidden SSalaryBasedHidden">
							<div class="col-md-5"><label class="control-label"> Statutory Limit Comparison</label></div>
							<div class="col-md-7"><p id="viewPfStatutoryLimitCompare"></p></div>
						</div>
						<div class="form-group viewAllowEpsEpfExcessContribution">
							<div class="col-md-5"><label class="control-label"> Allow EPF Excess Contribution</label></div>
							<div class="col-md-7"><p id="viewAllowEpfExcessContribution"></p></div>
						</div>
						<div class="form-group viewAllowEpsEpfExcessContribution">
							<div class="col-md-5"><label class="control-label"> Allow EPS Excess Contribution</label></div>
							<div class="col-md-7"><p id="viewAllowEpsExcessContribution"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label"> Company Share</label></div>
							<div class="col-md-7"><p id="viewPfCompanyShare"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label"> Employee Share</label></div>
							<div class="col-md-7"><p id="viewPfEmployeeShare"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label"> Admin Charge(%)</label></div>
							<div class="col-md-7"><p id="viewPfAdminChargePercent"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label"> Admin Charge Maxmium Amount</label></div>
							<div class="col-md-7"><p id="viewPfAdminMaxAmt"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label"> EDLI Configuration By Employer(%)</label></div>
							<div class="col-md-7"><p id="viewPfEDLIConfiguration"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label"> EDLI Configuration Maxmium Amount</label></div>
							<div class="col-md-7"><p id="viewPfEDLIMaxAmt"></p></div>
						</div>						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label"> Include Admin Charge As Part Of CTC</label></div>
							<div class="col-md-7"><p id="viewAdminChargePartOfCTC"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label"> Include EDLI Charge As Part Of CTC</label></div>
							<div class="col-md-7"><p id="viewEdliChargePartOfCTC"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label"> Description</label></div>
							<div class="col-md-7"><p id="viewPfDescription">-</p></div>
						</div>
					</div>
					
					<div class="row">
						<hr class="view-hr"/>
						
						<div class="form-group" style="font-size: large;margin-left: 13px;">
							<label class="control-label text-center">Additional Information</label>
						</div>
						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Added On</label></div>
							<div class="col-md-7"><p id="addedOnProvidentFund"></p></div>
						</div>
						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Added By</label></div>
							<div class="col-md-7"><p id="addedByProvidentFund"></p></div>
						</div>
						
						<div class="form-group updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated On</label></div>
							<div class="col-md-7"><p id="updatedOnProvidentFund"></p></div>
						</div>
						
						<div class="form-group updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated By</label></div>
							<div class="col-md-7"><p id="updatedByProvidentFund"></p></div>
						</div>
					</div>
				</form>
				<?php if ($pfUser['Add'] == 1 || $pfUser['Update'] == 1) { ?>
				
				<!--Add/Edit provident fund Form-->
				<form role="form" class="form-horizontal form-validation" id="editFormProvidentFund" method="POST" action="">
					<input type="hidden" name="PfOrg_Id" id="PfOrgId" />
					<input type="hidden" name="Employee_Id" id="EmployeeId" />					
					<div class="row">
						<div class="form-group">
							<label class="col-md-4 control-label">Coverage <span class="short_explanation">*</span></label>							
							<div class="col-md-8">								
								<select class="form-control vRequired" data-search="true" name="Coverage" id="formCoverage">
									<option value="">--Select--</option>
									<option value="O">Organization</option>
									<option value="E">Employee</option>
								</select>
							</div>
						</div>
						<div class="form-group formCoverageEmpHidden">
							<label class="col-md-4 control-label">Employee Name <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control" name="Employee_Name" id="formEmployeeName" data-search="true">
									<option value="">--Select--</option>
									<?php
										foreach ($empDepartment as $key => $row) {
											echo '<optgroup label="'. $row .'">';
											
											foreach ($employeeName as $empKey => $empRow) {
												if ($row == $empRow['Department_Name']) {
													echo '<option value="'. $empRow['value'] .'">'. $empRow['text'] .'</option>';
												}
											}
											
											echo '</optgroup>';
										}
									?>
								</select>
							</div>							
						</div>
						<div class="form-group formCoverageOrgHidden">
							<label class="col-md-4 control-label">Salary Type <span class="short_explanation">*</span></label>							
							<div class="col-md-8">								
								<select class="form-control" name="SalaryType" id="formSalaryType" data-search="true">
									<option value="" selected="selected">--Select--</option>
									<option value="MON">Monthly</option>
									<option value="HOU">Hourly</option>
								</select>
							</div>
						</div>
						
						<div class="form-group formCoverageOrgHidden">
							 <label class="col-md-4 control-label">Define Statutory Salary Limit </label>
							<div class="col-md-8 togglebutton togglebutton-material-blue" style="padding-left: 15px; padding-top: 10px;">
								<label>
									<input id="formDefineStatutorySalary" type="checkbox" checked="checked" class="md-checkbox">
								</label>
							</div>
						</div>
						
						<div class="form-group formStatutoryLimitHidden">
							<label class="col-md-4 control-label">Statutory Salary Limit <span class="short_explanation">*</span></label>
							<div class="col-md-8">								
								<input type="number" class="form-control" min=1 id="formStatutorySalaryLimit" name="Statutory_Salary_Limit" placeholder="Statutory Salary Limit" >
							</div>
						</div>
						
						<div class="form-group formStatutoryLimitHidden">
							<label class="col-md-4 control-label">Statutory Limit Comparison <span class="short_explanation">*</span></label>							
							<div class="col-md-8">								
								<select class="form-control" name="StatutoryLimitCompare" id="formStatutoryLimitCompare">
									<option value=0>Less than or Equal to Statutory Limit</option>
									<option value=1>Greater than statutory Limit</option>
								</select>
							</div>
						</div>
						<!--Start Allow EPF Excess Contribution Field Set-->
						<div class="form-group editAllowEpsEpfExcessContribution">
							<label class="col-md-4 control-label">Allow EPF Excess Contribution
							<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
                                                                       data-placement="top" data-content="Enable this flag will increase employee's contribution of EPF beyond statutory limit">
							</i>
							</label>
							<div class="col-md-8 togglebutton togglebutton-material-blue" style="padding-left: 15px; padding-top: 10px;">
								<label>
									<input id="formAllowEpfExcessContribution" type="checkbox" class="col-sm-9 md-checkbox">
								</label>
							</div>
                        </div>
						<!--End Allow EPF Excess Contribution Field Set-->
						<!--Start Allow Eps Excess Contribution Field Set-->
						<div class="form-group editAllowEpsEpfExcessContribution">
							<label class="col-md-4 control-label">Allow EPS Excess Contribution
							<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
                                                                       data-placement="top" data-content="Enable this flag will increase employer's contribution of EPS beyond statutory limit">
							</i>
							</label>
							<div class="col-md-8 togglebutton togglebutton-material-blue" style="padding-left: 15px; padding-top: 10px;">
								<label>
									<input id="formAllowEpsExcessContribution" type="checkbox" class="col-sm-9 md-checkbox">
								</label>
							</div>
                        </div>
						<!--End Allow Eps Excess Contribution Field Set-->
						<div class="form-group">
							<label class="col-md-4 control-label">Contribution <span class="short_explanation">*</span></label>							
							<div class="col-md-8">								
								<select class="form-control vRequired" name="Contribution" id="formContribution" data-search="true">									
									<option value=0 >Fixed Amount</option>
									<option value=1 selected="selected">Percentage</option>
								</select>
							</div>
                        </div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Company Share <span class="short_explanation">*</span></label>
							<div class="col-md-8">																
								<input type="number" class="form-control companyShare" id="formCompanyShare" name="CompanyShare">
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Employee Share <span class="short_explanation">*</span></label>
							<div class="col-md-8">																
								<input type="number" class="form-control employeeShare" id="formEmployeeShare" name="EmployeeShare">
							</div>
						</div>	
						
						<div class="form-group">
							<label class="col-md-4 control-label">Admin Charge(%) <span class="short_explanation">*</span></label>
							<div class="col-md-8">								
								<input type="number" class="form-control" min=0.1 max=0.5 id="formAdminChargePercent" name="Admin_Charge_Limit" >
							</div>
						</div>

						<div class="form-group">
							<label class="col-md-4 control-label">Admin Charge Maximum Amount <span class="short_explanation">*</span></label>
							<div class="col-md-8">								
								<input type="number" class="form-control" min=1 max=500 id="formAdminChargeAmt" name="Admin_Charge_Amt" >
							</div>
						</div>

						<div class="form-group">
							<label class="col-md-4 control-label">EDLI Configuration By Employer(%) <span class="short_explanation">*</span></label>
							<div class="col-md-8">								
								<input type="number" class="form-control" min=0.1 max=0.5 id="formEdliPercent" name="EDLI_Configuration_By_Employer"  >
							</div>
						</div>

						<div class="form-group">
							<label class="col-md-4 control-label">EDLI Configuration Maximum Amount<span class="short_explanation">*</span></label>
							<div class="col-md-8">								
								<input type="number" class="form-control" min=0.1 max=75 id="formEdliLimitAmt" name="EDLI_Configuration_Maximum_Limit">
							</div>
						</div>
						<!--Start Admin Charge Part Of CTC Field Set-->
						<div class="form-group">
							<label class="col-md-4 control-label">Include Admin Charge As Part Of CTC</label>
							<div class="col-md-8 togglebutton togglebutton-material-blue" style="padding-left: 15px; padding-top: 10px;">
								<label>
									<input id="formAdminChargePartOfCTC" type="checkbox" class="col-sm-9 md-checkbox">
								</label>
							</div>
                        </div>
						<!--End Admin Charge Part Of CTC Field Set-->
						<!--Start EDLI Charge Part Of CTC Field Set-->
						<div class="form-group">
							<label class="col-md-4 control-label">Include EDLI Charge As Part Of CTC</label>
							<div class="col-md-8 togglebutton togglebutton-material-blue" style="padding-left: 15px; padding-top: 10px;">
								<label>
									<input id="formEdliChargePartOfCTC" type="checkbox" class="col-sm-9 md-checkbox">
								</label>
							</div>
                        </div>
						<!--End EDLI Charge Part Of CTC Field Set-->
						<div class="form-group">
							<label class="col-md-4 control-label">Description </label>
							<div class="col-md-8">
								<textarea name="Description" id="formEditDescription" rows="5" class="form-control vComments" placeholder="Description..." ></textarea>
							</div>                           
						</div>
					</div>
					
					<button type="reset" class="cancel" id="formProvidentFundReset" style="display: none;" ></button>
				</form>
				
				<?php } ?>				
			</div>
			<div class="modal-footer text-center" id="formActionProvidentFund">				
				<?php if ($pfUser['Add'] == 1 || $pfUser['Update'] == 1) { ?>
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetProvidentFund" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitProvidentFund" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
				<?php } ?>
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="modalcopyProvidentFund" aria-hidden="true" style="z-index: 10;">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" id="modalCopyFormClose" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace"></i>
				</button>
				<h4 class="modal-title"> Copy Provident Fund</h4>
			</div>
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="copyFormProvidentFund" method="POST" action="">
					<input type="hidden" name="CopyEmployee_Id" id="formCopyEmployeeId" />
					<input type="hidden" name="CopyFixedVariableFlag" id="formCopyContribution" />
					<div class="row">
						<div class="form-group">
							<label class="col-md-4 control-label">Employee Name <span class="short_explanation">*</span></label>
							<div class="col-md-8">								
								<select multiple="multiple" class="form-control vRequired selectAlll" data-search="true" name="copyEmployeeName" id="formcopyEmployeeName">
									<option value="selectAll">--Select all--</option>
									<option value="clearAll">--Clear all--</option>
									<?php
										foreach ($employeeName as $empKey => $empRow) {
												echo '<option value="'. $empRow['value'] .'">'. $empRow['text'] .'</option>';
											}
									?>
								</select>
							</div>
						</div>
						<div class="form-group">
							<label class="col-md-4 control-label">Coverage <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<input type="text" class="form-control vRequired" readonly="readonly" id="formCopyCoverage" name="CopyCoverage">
							</div>
						</div>
						<div class="form-group">
							<label class="col-md-4 control-label">Company Share <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<input type="number" class="form-control vRequired" id="formCopyCompanyShare" name="CopyCompanyShare">
							</div>
						</div>
						<div class="form-group">
							<label class="col-md-4 control-label">Employee Share  <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<input type="number" class="form-control vRequired" id="formCopyEmployeeShare" name="CopyEmployeeShare">
							</div>
						</div>
						<div class="form-group">
							<label class="col-md-4 control-label">Admin Charge(%) <span class="short_explanation">*</span></label>
							<div class="col-md-8">								
								<input type="number" class="form-control" min=0.1 max=0.5 id="formCopyAdminChargePercent" name="Admin_Charge_Limit" >
							</div>
						</div>

						<div class="form-group">
							<label class="col-md-4 control-label">Admin Charge Maximum Amount <span class="short_explanation">*</span></label>
							<div class="col-md-8">								
								<input type="number" class="form-control" min=1 max=500 id="formCopyAdminChargeAmt" name="Admin_Charge_Amt" >
							</div>
						</div>

						<div class="form-group">
							<label class="col-md-4 control-label">EDLI Configuration By Employer(%) <span class="short_explanation">*</span></label>
							<div class="col-md-8">								
								<input type="number" class="form-control" min=0.1 max=0.5 id="formCopyEdliPercent" name="EDLI_Configuration_By_Employer"  >
							</div>
						</div>

						<div class="form-group">
							<label class="col-md-4 control-label">EDLI Configuration Maximum Amount<span class="short_explanation">*</span></label>
							<div class="col-md-8">								
								<input type="number" class="form-control" min=0.1 max=75 id="formCopyEdliLimitAmt" name="EDLI_Configuration_Maximum_Limit">
							</div>
						</div>
						<div class="form-group">
							<label class="col-md-4 control-label" id="labelCopyDescription">Description </label>
							<div class="col-md-8">
								<textarea name="Description" id="formCopyDescription" rows="5" class="form-control vDescription"
										  placeholder="Description..." ></textarea>
							</div>
						</div>	
					</div>
					
					<button type="reset" class="cancel" id="formCopyPfReset" style="display: none;" ></button>
				</form>				
			</div>
			<div class="modal-footer text-center" id="formActionCopyPf">				
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetCopyPf" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button"  data-style="expand-left" data-style="expand-left" id="formCopyProvidentFund" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
			</div>
		</div>
	</div>
</div>

<!-- Form Dirty Copy Provident Fund Modal -->
<?php if ($pfUser['Update'] == 1 && ($pfUser['Is_Manager'] == 1 || !empty($pfUser['Admin']))) { ?>
<div class="modal fade" id="dirtyCopyProvidentFund" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditCopyConfPF"></i></button>
                <h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
            </div>
            
            <div class="modal-body">Are you sure want to close this form?<br></div>
            
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditCopyConfPF">No</button>
              <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="closeCopyProvidentFund">Yes</button>
            </div>
        </div>
    </div>
</div>


<!-- Form Dirty Confirmation Modal -->
<?php } if (($pfUser['Add'] == 1 || $pfUser['Update'] == 1) && ($pfUser['Is_Manager'] == 1 || !empty($pfUser['Admin']))) { ?>
<div class="modal fade" id="dirtyProvidentFund" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditConfPF"></i></button>
                <h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
            </div>
            
            <div class="modal-body">Are you sure want to close this form?<br></div>
            
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfPF">No</button>
              <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="closeProvidentFund">Yes</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalPFExistsConfirmation" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditExixtsConfPF"></i></button>
                <h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
            </div>
            
            <div class="modal-body" id="modalPFExistsMsg">PF already exists for organization coverage. Are you sure you want to edit it?<br></div>
            
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditExistsConfPF">No</button>
              <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editExistsPf">Yes</button>
            </div>
        </div>
    </div>
</div>

<!-- Form statuatory salary limit ignore Confirmation Modal -->
<?php  } if ($pfUser['Update'] == 1 && ($pfUser['Is_Manager'] == 1 || !empty($pfUser['Admin']))) { ?>
<div class="modal fade" id="statuatorySalaryIgnoreConfirmation" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeStatuatorySalaryConfPF"></i></button>
                <h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
            </div>
            
            <div class="modal-body">Are you sure want to ignore all salary limit specified records?<br></div>
            
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="closestatuatorySalaryCancel">Cancel</button>
              <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="closestatuatorySalaryConfirm">Ok</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete COnfirmation Modal -->
<?php } if ($pfUser['Delete'] == 1 && ($pfUser['Is_Manager'] == 1 || !empty($pfUser['Admin']))) { ?>
<div class="modal fade" id="modalDeleteProvidentFund" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteConfPF"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfPF">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConfirmProvidentFund">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } }if ($pfPaymentUser['View'] == 1 && ((!empty($customFormNameB) && array_key_exists("Enable",$customFormNameB) && $customFormNameB['Enable'] == 1) || empty($customFormNameB))) { ?>		
<!-- Payment Tracker Grid Panel -->
<div class="col-md-12 portlets add-panel-padding">
	<div class="panel" id="gridPanelPaymentTracker">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong id="lblFormNameB"><?php echo ($customFormNameB['New_Form_Name']!='' ? $customFormNameB['New_Form_Name'] : $this->formNameB);?></strong></h3>
		</div>
		<div class="panel-content">			
			<div class="m-b-10">
                    <!-- View Button in Grid Toolbar -->
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="viewPfPaymentTracker" title="View">
						<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
					</button>
                    
					<!-- Update Button in Grid Toolbar -->
					<?php if ($pfPaymentUser['Update']) { ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="editPfPaymentTracker" title="Update">
						<i class="mdi-editor-mode-edit"></i><span class="hidden-xs hidden-sm"> Update</span>
					</button>
					<?php } ?>
					
                    <!-- Filter Button in Grid Toolbar -->
                    <a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterPaymentTracker">
                        <i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm">Filter</span>
                    </a>					
			</div>
			
            <!-- Payment Tracker Grid -->			
			<table class="table dataTable table-striped table-dynamic table-hover" id="tablePfPaymentTracker">
				<thead>
					<tr>
						<th></th>
						<th id="pfPaymentTrackerSalaryMonth">Salary Month</th>
						<th id="pfPaymentTrackerEmpShareAmount">Emp Share Amount</th>
						<th id="pfPaymentTrackerOrgShareAmount">Org Share Amount</th>
						<th id="pfPaymentTrackerTotalAmount">Total Amount</th>
						<th id="pfPaymentTrackerAmountPaid">Amount Paid</th>
						<th id="pfPaymentTrackerStatus">Status</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
		</div>
	</div>
</div>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="pfPaymentTracker-context-menu" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextPfPaymentTracker"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($pfPaymentUser['Update']) { ?>
		<li><a tabindex="-1" id="editContextPfPaymentTracker"><i class="mdi-editor-mode-edit"></i> Update</a></li>		
		<?php } ?>		
	</ul>
</div>

<!--Modal for payment tracker view & edit form-->
<div class="modal fade" id="modalFormPfPaymentTracker" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" id="paymentTrackerClose" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace"></i>
				</button>
				
				<?php if ($pfPaymentUser['Update']) { ?>
				<button type="button" class="close form-icons" aria-hidden="true" id="editInViewPfPaymentTracker">
					<i class="mdi-editor-mode-edit"></i>
				</button>
				<?php } ?>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<!--View Pf PaymentTracker Form-->
				<form role="form" id="viewFormPfPaymentTracker" >
					<div class="row">
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Payslip Month</label></div>
							<div class="col-md-7"><p id="viewPaymentTrackerPayslipMonth"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Organization Share Amount</label></div>
							<div class="col-md-7"><p id="viewPaymentTrackerOrgShareAmount"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Employee Share Amount</label></div>
							<div class="col-md-7"><p id="viewPaymentTrackerEmpShareAmount"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Total Amount</label></div>
							<div class="col-md-7"><p id="viewPaymentTrackerTotalAmount"></p></div>
						</div>						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label"> Status</label></div>
							<div class="col-md-7"><p id="viewPaymentTrackerStatus"></p></div>
						</div>
						<!-- Payment Tracker sub grid Grid Table -->
						<table class="table dataTable table-striped table-dynamic table-hover" id="tablePFPaymentTrackerSubGridView">
							<thead>
								<tr>
									<th></th>
									<th id="pfPaymentTrackerViewModeOfPayment">Mode of Payment</th>
									<th id="pfPaymentTrackerViewPaymentDate">Payment Date</th>
									<th id="pfPaymentTrackerViewAmountPaid">Amount Paid</th>
									<th id="pfPaymentTrackerViewDescription">Description</th>
								</tr>
							</thead>
							<tbody>
							
							</tbody>
						</table>
					</div>
					
				</form>
				
				<?php if ($pfPaymentUser['Update'] == 1) { ?>
				
				<!--Add/Edit Payment Tracker Form-->
				<form role="form" class="form-horizontal form-validation" id="editFormPfPaymentTracker" method="POST" action="">
					<input type="hidden" name="	Payment_Id" id="formPfPaymentId" />
					<input type="hidden" name="PF_Payment_Tracker_Id" id="formPfPaymentTrackerId" />
				
					<div class="panel-group panel-accordion" id="editAccordion">
						<div class="panel panel-default">
							<div class="panel-heading">                                
								 <h4>
									<a data-toggle="collapse" data-parent="#editAccordion" href="#editPFDetails" id="pfPaymentSummaryPanel">
										Payment Summary
									</a>
								</h4>
							</div>
							<div id="editPFDetails" class="panel-collapse collapse in">
								<div class="panel-body">								
									<div class="form-group">										
										<div class="col-md-4"><label class="control-label">Payslip Month</label></div>
										<div class="col-md-8"><p id="formPfPaymentTrackerPayslipMonth"></p></div>
									</div>
									<div class="form-group">										
										<div class="col-md-4"><label class="control-label">Organization Share Amount</label></div>
										<div class="col-md-8"><p id="formPfPaymentTrackerOrgShareAmount"></p></div>
									</div>
									<div class="form-group">										
										<div class="col-md-4"><label class="control-label">Employee Share Amount</label></div>
										<div class="col-md-8"><p id="formPfPaymentTrackerEmpShareAmount"></p></div>
									</div>								
									
									<div class="form-group">
										<div class="col-md-4"><label class="control-label">Total Amount</label></div>
										<div class="col-md-8"><p id="formPfPaymentTrackerTotalAmount"></p></div>
									</div>
									<div class="form-group">										
										<div class="col-md-4"><label class="control-label">Status</label></div>
										<div class="col-md-8"><p id="formPfPaymentTrackerStatus"></p></div>
									</div>
									<div class="form-group">										
										<div class="col-md-4"><label class="control-label">Outstanding Amount</label></div>
										<div class="col-md-8"><p id="formPfPaymentTrackerOutstandingAmount"></p></div>
									</div>
								</div>							
							</div>
						</div>
						<div class="panel panel-default" id="editPfPaymentTrackerPanel">
							<div class="panel-heading">
								<h4>
									<a class="collapsed" data-toggle="collapse" data-parent="#editAccordion" id="onPFAddCollapse" href="#editPfPaymentTrackerDetails">
										Payment Details
									</a>
								</h4>
							</div>							
							<div id="editPfPaymentTrackerDetails" class="panel-collapse collapse">
								<div class="panel-body">						
                                    
									<div class="form-group">
										<label class="col-md-4 control-label">Mode of Payment <span class="short_explanation">*</span></label>
										<div class="col-md-8">
											<select class="form-control vRequired" data-search="true" id="formPfPaymentTrackerModeOfPayment" name="PaymentTrackerModeOfPayment" >
												<option value="">-- Select --</option>
												<?php
												foreach ($paymentModePair as $key => $row)
												{
													echo '<option value="'.$key.'">'.$row.'</option>';
												}
												?>
											</select>
										</div>
									</div>
									
									<div class="form-group">
										<label class="col-md-4 control-label">Payment Date <span class="short_explanation">*</span></label>
										<div class="col-md-8">
											<input type="text" id="formPfPaymentTrackerPaymentDate" class="date-picker form-control vRequired datePickerRead" name="Payment_Date" placeholder="Payment Date">
										</div>
									</div>
									
									<div class="form-group paymentModeBasedHidden">
										<label class="col-md-4 control-label">Document No <span class="short_explanation">*</span></label>
										<div class="col-md-8">
											<input type="text" class="form-control numSpComma" maxlength="15" id="formPfPaymentTrackerDocumentNo" name="Document_No" placeholder="Document No">
										</div>
									</div>
									
									<div class="form-group paymentModeBasedHidden">
										<label class="col-md-4 control-label">Bank Name <span class="short_explanation">*</span></label>
										<div class="col-md-8">
											<input type="text" class="form-control onlyLetterSp" minlength="3" maxlength="30" id="formPfPaymentTrackerBankName" name="Bank_Name" placeholder="Bank Name">
										</div>
									</div>
									
									<div class="form-group paymentModeBasedHidden">
										<label class="col-md-4 control-label">Branch Name <span class="short_explanation">*</span></label>
										<div class="col-md-8">
											<input type="text" class="form-control onlyLetterSp" id="formPfPaymentTrackerBranchName" name="Branch_Name" placeholder="Branch Name">
										</div>
									</div>
									
									<div class="form-group">
										<label class="col-md-4 control-label">Amount Paid <span class="short_explanation">*</span></label>
										<div class="col-md-8">
											<input type="number" class="form-control convertDecimalPos1 vRequired" min="1" max="*************" required="true" id="formPfPaymentTrackerAmountPaid" name="Amount Paid" placeholder="Amount Paid">
										</div>
									</div>
									
									<div class="form-group">
										<label class="col-md-4 control-label">Description</label>
										<div class="col-md-8">
											<textarea name="description" id="formPfPaymentTrackerDescription" rows="5" class="form-control vComments" placeholder="Write your Description..." ></textarea>
										</div>
									</div>
									
									<div class="text-center"> 			
										<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetPfPaymentTracker" style="bottom: 5px;">
											<i class="mdi-action-restore"></i> Reset
										</button>
										<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitPfPaymentTracker" style="bottom: 5px;">
											<i class="mdi-content-send"></i> Add
										</button>
									</div>
									
								</div>
							</div>
						</div>
					</div>
					
					<button type="reset" class="cancel" id="formPfPaymentTrackerReset" style="display: none;" ></button>
				</form>
				
				<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="addPFPaymentTracker" title="Add">
					<i class="mdi-content-add"></i><span> Add</span>
				</button>
				
				<div id="pfPaymentTrackerGrid" >
					<table class="table dataTable table-striped table-dynamic table-hover" id="tablePfPaymentTrackerSubGrid">
						<thead>
							<tr>
								<th></th>
								<th id="pfPaymentTrackerSubModeOfPayment">Mode of Payment</th>
								<th id="pfPaymentTrackerSubPaymentDate">Payment Date</th>
								<th id="pfPaymentTrackerSubAmountPaid">Amount Paid</th>
							</tr>
						</thead>
						<tbody>
						
						</tbody>
					</table>
				</div>
				<?php } ?>
				
				</div>
			</div>			
		</div>
	</div>
</div>

<!--Filter Form-->
<div class="builder" id="filterPanelPfPaymentTracker">
	<div id="closeFilterPfPaymentTracker"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel filterReset" style="width: 100%;" id="cancelPaymentTracker">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="applyPaymentTracker">Apply</button>
			
			<!--Filter For Payslip Month -->
			<div class="form-group">
				<label>Salary Month</label>
                <!--<input type="month" class="form-control" name="paySlipMonthStart" id="filterPaySlipMonth">-->
				<input type="text" class="b-datepicker vmonthMax vMonthClosure form-control vRequired closeMonthPicker" name="yearMonthStart" name="paySlipMonthStart"
                data-date-format="MM,yyyy" data-view="1" data-date-min-view-mode=1 data-date-autoclose="true" id="filterPaySlipMonth" data-orientation="top">
			</div>
			
			<!--Filter For Employee Share Amount -->
			<div class="form-group">
				<label>Employee Share Amount</label>
				<div class="input-group">
					<input type="number" class="form-control" name="employeeShareAmountStart" id="filterempShareAmountStart" min="0" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="form-control" name="employeeShareAmountEnd" id="filterempShareAmountEnd" min="0" placeholder="End"/>
				</div>
			</div>
			
			<!--Filter For Organization Share Amount -->
			<div class="form-group">
				<label>Organization Share Amount</label>
				<div class="input-group">
					<input type="number" class="form-control" name="OrgShareAmountStart" id="filterOrgShareAmountStart" min="0" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="form-control" name="OrgShareAmountEnd" id="filterOrgShareAmountEnd" min="0" placeholder="End"/>
				</div>
			</div>
			
			<!--Filter For Total Amount -->
			<div class="form-group">
				<label>Total Amount</label>
				<div class="input-group">
					<input type="number" class="form-control" name="TotalAmountStart" id="filterTotalAmountStart" min="0" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="form-control" name="TotalAmountEnd" id="filterTotalAmountEnd" min="0" placeholder="End"/>
				</div>
			</div>
			
			<!--Filter For Paid Amount -->
			<div class="form-group">
				<label>Paid Amount</label>
				<div class="input-group">
					<input type="number" class="form-control" name="PaidAmountStart" id="filterPaidAmountStart" min="0" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="form-control" name="PaidAmountEnd" id="filterPaidAmountEnd" min="0" placeholder="End"/>
				</div>
			</div>
			
			<!--Filter For Status-->
			<div class="form-group">
				<label>Status</label>
				<select class="form-control" data-search="true" id="filterStatus" >
					<option value="">All</option>
					<option value="Paid" >Paid</option>
					<option value="Partially Paid" >Partially Paid</option>
					<option value="Unpaid" >Unpaid</option>
				</select>
			</div>
			
		</div>
	</div>
</div>

<!-- Form Dirty Confirmation Modal -->
<?php if ( $pfPaymentUser['Update']) { ?>
<div class="modal fade" id="modalDirtyPfPaymentTracker" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditConfPFTracker"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfPFTracker">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseConfirmPfPaymentTracker">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php }} if($pfUser['View'] !=1 && $pfPaymentUser['View'] !=1) { ?>

<div class="col-md-12 portlets add-panel-padding">
	<div class="txt_center">Sorry, Access Denied...</div>
</div>

<?php } ?>