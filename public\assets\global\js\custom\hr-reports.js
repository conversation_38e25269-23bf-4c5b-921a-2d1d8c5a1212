$(function () {
    //*********************Getting linkValue, Module Name from Url***********************//
    $.fn.dataTable.ext.errMode = 'none';

    /* Alert when the DataSetup is not completed */
    dataSetup('datasetup');


    var oTable = null;
    var url = window.location.href;
    var moduleId = '_mId';
    var moduleName = '_mNme';
    var linkVal = 'linkValue';
    var urlVariables = url.split('/');

    for (var i = 0; i < urlVariables.length; i++) {
        if (urlVariables[i] == moduleId) {
            var mId = urlVariables[i + 1];
            var modName = mId.split("-");
            modName = modName[0];
        }

        if (urlVariables[i] == moduleName) {
            var mNme = urlVariables[i + 1];
        }

        if (urlVariables[i] == linkVal) {
            var linkValue = urlVariables[i + 1];

            linkValue = linkValue.replace(/-/g, ' ');

            linkValue = linkValue.toLowerCase().replace(/\b[a-z]/g, function (letter) {
                return letter.toUpperCase();
            });
        }
    }

    if (linkValue != undefined) {
        var explodeStr = linkValue.split(" ");

        if (explodeStr.length > 1) {
            linkId = explodeStr[0].toUpperCase();

        }
        else {
            linkId = linkValue;
            linkId = linkId.toLowerCase().replace(/\b[a-z]/g, function (letter) {
                return letter.toUpperCase();
            });
        }


        $(document).ready(function () {
            createCharts('');
        });
        //*********************To Design a Grid Dynamically***********************//
        // tableReimbursement.fnReloadAjax(pageUrl() + 'payroll/reimbursement/show-reimbursement');

            $.ajax({
                type: 'POST',
                dataType: 'json',
                async: false,
                url: pageUrl() + 'reports/hr-reports/dyn-filters/linkValue/' + linkValue + '/_modName/' + modName,//
                //data     : { _modName : modName, linkValue : linkValue},
                success: function (result) {
                    var reqArray = result.grid;
                    var colArray = [];
                    var gridKey = reqArray.grid_key;
                    var aoColumns = [];
                    var aoColumnDefs = [];
                   
                    if (gridKey != undefined) {
                        //loan id should be removed from loan amortization report
                        if (linkValue == 'Loan Amortization') {
                            gridKey.splice(2, 1);
                        }
                        for (var i = 0; i < gridKey.length; i++) {
                            colArray.push(gridKey[i]);
                        }
                    }

                    var columnLength = colArray.length;

                    //********* if More than 5 fields then show extra columns on expand ******//
                    if (columnLength > 2)  //if(colArray.length >5)
                    {
                        if (linkValue == 'Attendance Shortage' || linkValue == 'Eft Monthly' || linkValue == 'Eft Hourly' || linkValue == 'Attendance Summary Hourly' || linkValue == 'Attendance Summary Monthly' || linkValue == 'Employee Utilization' || linkValue == 'Additional Wage Summary') {
                            aoColumnDefs.push({ 'bSortable': false,'aTargets': ['_all'] });
                        }
                        else {
                            aoColumnDefs.push({ "targets": 0, "orderable": false });
                        }
                        if(linkValue == 'Reimbursement')
                        {
                            colArray.push('Documents');
                            colArray.splice(colArray.indexOf('Line_Item_Id'),1);
                        }

                        if (linkValue === 'Tds Override')
                        {
                            var sortColumnIndex = 3;
                            var sortOrder = 'desc';
                        }
                        else if (linkValue === 'Pay Bill')
                        {
                            var sortColumnIndex = 3;
                            var sortOrder = 'asc';
                        }
                        else if (linkValue === 'Salary Register' || linkValue === 'Payment Register' || linkValue=='Bimonthly Salary Register')
                        {
                            var sortColumnIndex = 3;
                            var sortOrder = 'asc';
                        }
                        else if (linkValue === 'Monthly Payslip Comprehensive')
                        {
                            var sortColumnIndex = 3;
                            var sortOrder = 'asc';
                        }
                        else if (linkValue === 'Monthly Salary Payslip' || linkValue === 'Insurance Statement' || linkValue === 'Ssnit Tier 1' 
                        || linkValue === 'Ssnit Tier 2' || linkValue === 'Provident Fund Detailed Report')
                        {
                            var sortColumnIndex = 5;
                            var sortOrder = 'desc';
                        }
                        else
                        {
                            /** Add Plus button with empty header for expand, So default sorting column index will be 1 **/
                            var sortColumnIndex = 1;
                            var sortOrder = 'asc';
                        }

                        aoColumns.push({
                            "mData": function (row, type, set) {
                                return '<i class="fa fa-plus-square-o"></i>';
                            }
                        });

                        if (columnLength <= 6) {
                            aoColumnDefs.push({ "sClass": "hidden-md hidden-lg visible-sm visible-xs", "aTargets": [0] });
                        }
                        for (var x in colArray) {
                            if (x < 6) {
                                aoColumns.push({ "mData": fnCheckNull(colArray[x]) });
                            }
                        }
                        if (columnLength > 4) {
                            columnMdLength = 4;
                        }
                        else {
                            columnMdLength = columnLength;
                        }

                        if (columnLength > 6) {
                            columnLen = 6;
                        }
                        else {
                            columnLen = columnLength;
                        }

                        for (var y = 3; y <= columnMdLength; y++) {
                            aoColumnDefs.push({ "sClass": "hidden-sm hidden-xs visible-md visible-lg", "aTargets": [parseInt(y)] });
                        }
                        for (var y = 5; y <= columnLen; y++) {
                            aoColumnDefs.push({ "sClass": "hidden-sm hidden-xs hidden-md visible-lg", "aTargets": [parseInt(y)] });
                        }
                    }
                    else {
                        /*** Default column index will be zero***/
                        var sortColumnIndex = 0;
                        for (var x in colArray) {
                            aoColumns.push({ "mData": fnCheckNull(colArray[x]) });
                        }
                    }
                    var oTable = null;//
                    var filterServiceProvider = '';
                    if (linkValue == 'Attendance Shortage') {
                        ///************ Data Table **************/
                        oTable = $('#tableReports').dataTable({
                            "lengthMenu": [5, 10, 25, 50, 100],
                            "bPaginate": false,
                            "bDestroy": true,
                            "bAutoWidth": false,
                            "bFilter": false, // to hide search all
                            "bServerSide": true,
                            "bDeferRender": true,
                            "sServerMethod": "POST",
                            "sAjaxSource": pageUrl() + "reports/hr-reports/bind-reports/linkValue/" + linkValue + "/_modName/" + modName,
                            "sAjaxDataProp": "aaData",
                            "aoColumnDefs": aoColumnDefs,
                            "aaSorting": [],
                            "aoColumns": aoColumns
                        });
                    }
                    else if (linkValue == 'Employee Wise Expenses') {
                        var filterArray = [];
                        var filterValue4 = $('#filterid0').val();
                        var filterValue5 = fnFormatDate($('#filterDateBeginid1').val());
                        var filterValue6 = fnFormatDate($('#filterDateEndid1').val());
                        filterArray.push(filterValue4, filterValue5, filterValue6, filterServiceProvider);

                        ///************ Data Table **************/
                        oTable = $('#tableReports').dataTable({
                            "lengthMenu": [5, 10, 25, 50, 100],
                            "iDisplayLength": 10,
                            "bDestroy": true,
                            "bAutoWidth": false,
                            "bServerSide": true,
                            "bDeferRender": true,
                            "bFilter": false,
                            "sServerMethod": "POST",
                            "sAjaxSource": pageUrl() + "reports/hr-reports/bind-reports/linkValue/" + linkValue + "/_modName/" + modName + "/filterArray/" + filterArray,
                            "sAjaxDataProp": "aaData",
                            "aoColumnDefs": aoColumnDefs,
                            "aaSorting": [sortColumnIndex, 'asc'],
                            "aoColumns": aoColumns
                        });

                    }
                    else if (linkValue == 'Eft Monthly' || linkValue == 'Eft Hourly') {//
                        var filterArray = [];
                        //By default, empty the debit account number and transaction type
                        $("#filterid1").val('');
                        $("#filterid2").val('');

                        $filterValue1 = $('#filterid0').val();
                        $filterValue2 = $('#filterid1').val();
                        $filterValue3 = $('#filterid2').val();
                        $filterValue4 = fnServerMonthFormatter($('#filterid3').val());
                        let filterValue5 = $('#filterid4').val();
                        let filterValue6 = $('#filterid5').val();
                        let filterValue7 = $('#filterid6').val();
                        filterArray.push($filterValue1, $filterValue2, $filterValue3, $filterValue4, filterValue5, filterValue6, filterValue7, filterServiceProvider);
                        ///************ Data Table **************/
                        oTable = $('#tableReports').dataTable({
                            "lengthMenu": [5, 10, 25, 50, 100],
                            "iDisplayLength": 10,
                            "bDestroy": true,
                            "bAutoWidth": false,
                            "bServerSide": true,
                            "bDeferRender": true,
                            "bFilter": false,
                            "sServerMethod": "POST",
                            "sAjaxSource": pageUrl() + "reports/hr-reports/bind-reports/linkValue/" + linkValue + "/_modName/" + modName + "/filterArray/" + filterArray,
                            "sAjaxDataProp": "aaData",
                            "aoColumnDefs": aoColumnDefs,
                            "aaSorting": [],
                            "aoColumns": aoColumns
                        });
                    }
                    else if (linkValue == 'Esic Monthly' || linkValue == 'Esic Hourly') {
                        var filterArray = [];
                        filterValue1 = $('#filterid0').val();
                        filterValue2 = fnServerMonthFormatter($('#filterid1').val());
                        filterValue3 = $('#filterid2').val();
                        filterValue4 = $('#filterid3').val();
                        filterArray.push(filterValue1,filterValue2,filterValue3,filterValue4,filterServiceProvider);
                        ///************ Data Table **************/
                        oTable = $('#tableReports').dataTable({
                            "lengthMenu": [5, 10, 25, 50, 100],
                            "iDisplayLength": 10,
                            "bDestroy": true,
                            "bAutoWidth": false,
                            "bServerSide": true,
                            "bDeferRender": true,
                            "bFilter": false,
                            "sServerMethod": "POST",
                            "sAjaxSource": pageUrl() + "reports/hr-reports/bind-reports/linkValue/" + linkValue + "/_modName/" + modName + "/filterArray/" + filterArray,
                            "sAjaxDataProp": "aaData",
                            "aoColumnDefs": aoColumnDefs,
                            "aaSorting": [],
                            "aoColumns": aoColumns
                        });
                    }
                    else if (linkValue == 'Uan Based Ecr' || linkValue == "Uan Based Ecr Hourly" || linkValue.toLowerCase() === 'uan based ecr(arrear)') {
                        var filterArray = [];
                        filterValue1 = $('#filterid0').val();
                        filterValue2 = fnServerMonthFormatter($('#filterid1').val());
                        filterValue3 = $('#filterid2').val();
                        filterArray.push(filterValue1,filterValue2,filterValue3,filterServiceProvider);
                        ///************ Data Table **************/
                        oTable = $('#tableReports').dataTable({
                            "lengthMenu": [5, 10, 25, 50, 100],
                            "iDisplayLength": 10,
                            "bDestroy": true,
                            "bAutoWidth": false,
                            "bServerSide": true,
                            "bDeferRender": true,
                            "bFilter": false,
                            "sServerMethod": "POST",
                            "sAjaxSource": pageUrl() + "reports/hr-reports/bind-reports/linkValue/" + linkValue + "/_modName/" + modName + "/filterArray/" + filterArray,
                            "sAjaxDataProp": "aaData",
                            "aoColumnDefs": aoColumnDefs,
                            "aaSorting": [],
                            "aoColumns": aoColumns
                        });
                    }
                    else if (linkValue == "Attendance Summary Hourly" || linkValue == "Attendance Summary Monthly"  || linkValue == "Attendance Muster Info" 
                    || linkValue == 'Employee Utilization' || linkValue == 'Additional Wage Summary' || linkValue == 'Absentees'  || linkValue == 'Attendance Register' || linkValue == 'Attendance And Absence Overview') {
                        
                        if ($('#fieldForce').val() == 1) 
                        {
                            if(linkValue != "Attendance Muster Info" && linkValue != "Attendance Register")
                            {
                               var filterServiceProvider = $('#serviceProviderId').val();
                            }
                        }
                        
                        var filterArray = [];
                        filterValue1 = $('#filterid0').val();
                        var finalBeginDate = $('#filterDateBeginid1').val();
                        var finalEndDate = $('#filterDateEndid1').val();
                        if (finalBeginDate !== '') {
                            finalBeginDate = fnFormatDate(finalBeginDate);
                        }

                        if (finalEndDate !== '') {
                            finalDate = fnFormatDate(finalEndDate);
                            var finalEndDate = finalBeginDate + '&' + finalDate;
                        }
                        filterValue2 = $('#filterid2').val();
                        filterValue3 = $('#filterid3').val();

                        filterValue4 = $('#filterid4').val();
                        filterValue5 = $('#filterid5').val();

                        if(linkValue == 'Additional Wage Summary')
                        {
                            filterArray.push(filterValue1, finalEndDate, filterValue2, filterServiceProvider);
                        }
                        else
                        {
                            filterArray.push(filterValue1, finalEndDate, filterValue2, filterValue3, filterValue4, filterServiceProvider);
                        }
                        ///************ Data Table **************/
                        oTable = $('#tableReports').dataTable({
                            // "lengthMenu": [5, 10, 25, 50, 100],
                            // "iDisplayLength": 10,
                            "bPaginate": false,
                            "bDestroy": true,
                            "bAutoWidth": false,
                            "bServerSide": true,
                            "bDeferRender": true,
                            "bFilter": false,
                            "sServerMethod": "POST",
                            "sAjaxSource": pageUrl() + "reports/hr-reports/bind-reports/linkValue/" + linkValue + "/_modName/" + modName + "/filterArray/" + filterArray,
                            "sAjaxDataProp": "aaData",
                            "aoColumnDefs": aoColumnDefs,
                            "aaSorting": [],
                            "aoColumns": aoColumns
                        });
                    }
                    else if (linkValue == "Employee Status") {
                        var filterArray = [];
                        filterValue0 = $('#filterid0').val();
                        filterValue1 = $('#filterid1').val();
                        filterValue2 = $('#filterid2').val();
                        filterValue3 = $('#filterid3').val();
                        filterValue4 = $('#filterid4').val();
                        var finalBeginDate = $('#filterDateBeginid5').val();
                        var finalEndDate = $('#filterDateEndid5').val();
                        if (finalBeginDate !== '') {
                            finalBeginDate = fnFormatDate(finalBeginDate);
                        }

                        if (finalEndDate !== '') {
                            finalDate = fnFormatDate(finalEndDate);
                            var finalEndDate = finalBeginDate + '&' + finalDate;
                        }

                        filterValue6 = $('#filterid6').val();


                        filterArray.push(filterValue0,filterValue1,filterValue2, filterValue3, filterValue4,finalEndDate,filterValue6);
                        
                        ///************ Data Table **************/
                        oTable = $('#tableReports').dataTable({
                            "bPaginate": false,
                            "bDestroy": true,
                            "bAutoWidth": false,
                            "bServerSide": true,
                            "bDeferRender": true,
                            "sServerMethod": "POST",
                            "sAjaxSource": pageUrl() + "reports/hr-reports/bind-reports/linkValue/" + linkValue + "/_modName/" + modName + "/filterArray/" + filterArray,
                            "sAjaxDataProp": "aaData",
                            "aoColumnDefs": aoColumnDefs,
                            "aaSorting": [],
                            "bFilter": false,
                            "aoColumns": aoColumns
                        });
                    }
                    else if (linkValue == 'Esi Monthly' || linkValue == 'Esi Hourly') {
                        var filterArray = [];
                        if(linkValue == 'Esi Monthly') {
                            filterValue1 = $('#filterid0').val();
                            filterValue2 = $('#filterid1').val();
                            filterValue3 = $('#filterid2').val();
                            filterValue4 = $('#filterid3').val();
                            filterValue5 = fnServerMonthFormatter($('#filterid4').val());
                            filterValue6  = $('#filterid5').val();
                            filterArray.push(filterValue1,filterValue2,filterValue3,filterValue4,filterValue5,filterValue6,filterServiceProvider);
                        } else {
                            filterValue1 = $('#filterid0').val();
                            filterValue2 = $('#filterid1').val();
                            filterValue3 = $('#filterid2').val();
                            filterValue4 = fnServerMonthFormatter($('#filterid3').val());
                            filterArray.push(filterValue1,filterValue2,filterValue3,filterValue4,filterServiceProvider);
                        }
                        ///************ Data Table **************/
                        oTable = $('#tableReports').dataTable({
                            "lengthMenu": [5, 10, 25, 50, 100],
                            "iDisplayLength": 10,
                            "bDestroy": true,
                            "bAutoWidth": false,
                            "bServerSide": true,
                            "bDeferRender": true,
                            "bFilter": false,
                            "sServerMethod": "POST",
                            "sAjaxSource": pageUrl() + "reports/hr-reports/bind-reports/linkValue/" + linkValue + "/_modName/" + modName + "/filterArray/" + filterArray,
                            "sAjaxDataProp": "aaData",
                            "aoColumnDefs": aoColumnDefs,
                            "aaSorting": [],
                            "aoColumns": aoColumns
                        });
                    }
                    else if (linkValue === 'Hourly Wage Payslip' || linkValue === 'Monthly Salary Payslip' || linkValue === 'Reimbursement Allowances' || 
                            linkValue === 'Insurance Statement' || linkValue === 'Ssnit Tier 1' || linkValue === 'Salary Register'
                            || linkValue === 'Ssnit Tier 2'|| linkValue === 'Provident Fund Detailed Report' || linkValue == 'Bank Salary Statement'
                            || linkValue == 'Fixed Health Insurance' || linkValue == 'Bimonthly Salary Register') {
                        /** Load the Hourly payslip report, Monthly payslip report with the 'payslip from month' and 'payslip to month' values
                         *  which is set in the filter */
                        let filterArray = [];
                        let filterValue0 = filterValue3 = filterValue4 = filterValue5 = filterValue6 ='', filterValue7 ='',filterValue8 ='',filterValue9 ='';
                        
                        /** Payslip from month */
                        let filterValue1 = ($('#filterid1').val());
                        filterValue1 = (filterValue1) ? (fnServerMonthFormatter(filterValue1)) : '';

                        /** Payslip to month */
                        let filterValue2 = $('#filterid2').val();
                        filterValue2 = (filterValue2) ? (fnServerMonthFormatter(filterValue2)) : '';
                        
                        if (linkValue === 'Monthly Salary Payslip' || linkValue === 'Salary Register' || linkValue == 'Bank Salary Statement' || linkValue == 'Fixed Health Insurance')
                        {
                            filterArray.push(filterValue0, filterValue1, filterValue2, filterValue3, filterValue4, filterValue5,filterValue6,filterValue7,filterValue8,filterServiceProvider);
                        }
                        else if (linkValue === 'Bimonthly Salary Register')
                        {
                            filterArray.push(filterValue0, filterValue1, filterValue2, filterValue3, filterValue4, filterValue5,filterValue6,filterValue7,filterValue8,filterValue9,filterServiceProvider);
                        }
                        else if (linkValue==='Reimbursement Allowances')
                        {
                            filterArray.push(filterValue0, filterValue1, filterValue2, filterValue3, filterValue4, filterValue5,filterValue6,filterValue7,filterServiceProvider);
                        }
                        else
                        {
                            filterArray.push(filterValue0, filterValue1, filterValue2, filterValue3, filterValue4, filterValue5, filterServiceProvider);
                        }
                        

                        ///************ Data Table **************/
                        oTable = $('#tableReports').dataTable({
                            "lengthMenu": [5, 10, 25, 50, 100],
                            "iDisplayLength": 10,
                            "bDestroy": true,
                            "bAutoWidth": false,
                            "bServerSide": true,
                            "bDeferRender": true,
                            "bFilter": false,
                            "sServerMethod": "POST",
                            "sAjaxSource": pageUrl() + "reports/hr-reports/bind-reports/linkValue/" + linkValue + "/_modName/" + modName + "/filterArray/" + filterArray,
                            "sAjaxDataProp": "aaData",
                            "aoColumnDefs": aoColumnDefs,
                            "aaSorting": [sortColumnIndex,sortOrder],
                            "aoColumns": aoColumns
                        });
                    }
                    else if (linkValue === 'Form16 Summary(Monthly)') {
                        let filterArray = [];
                        
                        /** Assessment Year */
                        let filterValue1 = $('#filterid1').val();
                        
                        /** Push the default filter values in an array */
                        filterArray.push('', filterValue1, filterServiceProvider);

                        ///************ Data Table **************/
                        oTable = $('#tableReports').dataTable({
                            "lengthMenu": [5, 10, 25, 50, 100],
                            "iDisplayLength": 10,
                            "bDestroy": true,
                            "bAutoWidth": false,
                            "bServerSide": true,
                            "bDeferRender": true,
                            "bFilter": false,
                            "sServerMethod": "POST",
                            "sAjaxSource": pageUrl() + "reports/hr-reports/bind-reports/linkValue/" + linkValue + "/_modName/" + modName + "/filterArray/" + filterArray,
                            "sAjaxDataProp": "aaData",
                            "aoColumnDefs": aoColumnDefs,
                            "aaSorting": [],
                            "aoColumns": aoColumns
                        });
                    }
                    else if (linkValue === 'Tds') {
                        /** Load the TDS report always for the last payslip generated month. */
                        let filterArray = [];
                        let lastpayslipGeneratedMonthYear = $('#filterid1').val();
                        lastpayslipGeneratedMonthYear = (lastpayslipGeneratedMonthYear) ? (fnServerMonthFormatter(lastpayslipGeneratedMonthYear)) : '';
                        let filterValue2 = $('#filterid2').val();
                        /** Push the default filter values in an array */
                        filterArray.push('', lastpayslipGeneratedMonthYear, lastpayslipGeneratedMonthYear,filterValue2,filterServiceProvider);//Filter fields:Employee name, Payslip month from, Payslip month to

                        oTable = $('#tableReports').dataTable({
                            "lengthMenu": [5, 10, 25, 50, 100],
                            "iDisplayLength": 10,
                            "bDestroy": true,
                            "bAutoWidth": false,
                            "bServerSide": true,
                            "bDeferRender": true,
                            "bFilter": false,
                            "sServerMethod": "POST",
                            "sAjaxSource": pageUrl() + "reports/hr-reports/bind-reports/linkValue/" + linkValue + "/_modName/" + modName + "/filterArray/" + filterArray,
                            "sAjaxDataProp": "aaData",
                            "aoColumnDefs": aoColumnDefs,
                            "aaSorting": [sortColumnIndex, 'asc'],
                            "aoColumns": aoColumns
                        });
                    }
                    else if (linkValue === 'Timesheet' || linkValue === 'Timesheet Comprehensive') 
                    {
                        var filterArray = [];
                        filterValue1 = $('#filterid0').val();
                        
                        var finalBeginDate = $('#filterDateBeginid1').val();
                        var finalEndDate = $('#filterDateEndid1').val();

                        if (finalBeginDate !== '') {
                            finalBeginDate = fnFormatDate(finalBeginDate);
                        }

                        if (finalEndDate !== '') {
                            finalDate = fnFormatDate(finalEndDate);
                            var finalEndDate = finalBeginDate + '&' + finalDate;
                        }

                        filterValue3 = $('#s2id_filterid3').val();
                        filterValue4 = $('#s2id_filterid4').val();
                        filterValue5 = $('#s2id_filterid5').val();
                        filterValue6 = $('#s2id_filterid6').val();
                        filterValue7 = $('#s2id_filterid7').val();
                        
                        filterArray.push(filterValue1, finalEndDate, filterValue3, filterValue4, filterValue5, filterValue6, filterValue7);

                        oTable = $('#tableReports').dataTable({
                            "lengthMenu": [5, 10, 25, 50, 100],
                            "iDisplayLength": 10,
                            "bDestroy": true,
                            "bAutoWidth": false,
                            "bServerSide": true,
                            "bDeferRender": true,
                            "bFilter": false,
                            "sServerMethod": "POST",
                            "sAjaxSource": pageUrl() + "reports/hr-reports/bind-reports/linkValue/" + linkValue + "/_modName/" + modName + "/filterArray/" + filterArray,
                            "sAjaxDataProp": "aaData",
                            "aoColumnDefs": aoColumnDefs,
                            "aaSorting": [sortColumnIndex, 'asc'],
                            "aoColumns": aoColumns
                        });
                    }
                    else if (linkValue == 'Pt Annual Return(Form 5a)') {
                        let filterArray = [];
                        
                        /** Assessment Year */
                        let filterValue0 = $('#filterid0').val();
                        
                        /** Push the default filter values in an array */
                        filterArray.push(filterValue0,'');

                        ///************ Data Table **************/
                        oTable = $('#tableReports').dataTable({
                            "lengthMenu": [5, 10, 25, 50, 100],
                            "iDisplayLength": 10,
                            "bDestroy": true,
                            "bAutoWidth": false,
                            "bServerSide": true,
                            "bDeferRender": true,
                            "bFilter": false,
                            "sServerMethod": "POST",
                            "sAjaxSource": pageUrl() + "reports/hr-reports/bind-reports/linkValue/" + linkValue + "/_modName/" + modName + "/filterArray/" + filterArray,
                            "sAjaxDataProp": "aaData",
                            "aoColumnDefs": aoColumnDefs,
                            "aaSorting": [],
                            "aoColumns": aoColumns
                        });
                    }
                    else if (linkValue != '') {

                        let filterArray = [];
                        if(linkValue==='Pay Bill' || linkValue === 'Monthly Payslip Comprehensive')
                        {
                            let filterValue0 = filterValue2 = filterValue3 = filterValue4 = filterValue5 ='';
                            /** Payslip from month */
                            let filterValue1 = ($('#filterid1').val());
                            filterValue1 = (filterValue1) ? (fnServerMonthFormatter(filterValue1)) : '';
                            filterArray.push(filterValue0, filterValue1, filterValue2, filterValue3, filterValue4, filterValue5,filterServiceProvider);
                        }

                        if(linkValue === 'Payment Register' || linkValue.toLowerCase() === 'wps')
                        {
                            let filterValue0 = filterValue2 = filterValue3 = filterValue4 = filterValue5 = filterValue6 = filterValue7 ='';
                            /** Payslip from month */
                            let filterValue1 = ($('#filterid1').val());
                            filterValue1 = (filterValue1) ? (fnServerMonthFormatter(filterValue1)) : '';
                            filterArray.push(filterValue0, filterValue1, filterValue2, filterValue3, filterValue4, filterValue5,filterValue6,filterValue7,filterServiceProvider);
                        }
                        if(linkValue === 'Lop Recovery')
                        {
                            let filterValue0 = filterValue2 = filterValue3 = '';
                            $('#filterid2').val('');
                            /** Payslip from month */
                            let filterValue1 = ($('#filterid1').val());
                            filterValue1 = (filterValue1) ? (fnServerMonthFormatter(filterValue1)) : '';
                            filterArray.push(filterValue0, filterValue1, filterValue2, filterValue3,filterServiceProvider);
                        }

                        if(linkValue === 'Employee Step Increment')
                        {
                            let filterValue0 = ($('#filterid0').val());
                            filterArray.push(filterValue0);
                        }

                        ///************ Data Table **************/
                        oTable = $('#tableReports').dataTable({
                            "lengthMenu": [5, 10, 25, 50, 100],
                            "iDisplayLength": 10,
                            "bDestroy": true,
                            "bAutoWidth": false,
                            "bServerSide": true,
                            "bDeferRender": true,
                            "bFilter": false,
                            "sServerMethod": "POST",
                            "sAjaxSource": pageUrl() + "reports/hr-reports/bind-reports/linkValue/" + linkValue + "/_modName/" + modName+ "/filterArray/" + filterArray,
                            "sAjaxDataProp": "aaData",
                            "aoColumnDefs": aoColumnDefs,
                            "aaSorting": [sortColumnIndex,sortOrder],
                            "aoColumns": aoColumns
                        });
                    }

                    //*********** Expand hidden columns **************//        
                    function fnGridExpan(nTr,colArray) {
                        if (oTable.fnIsOpen(nTr)) {
                            /* This row is already open - close it */
                            $($(nTr).children().children()[0]).removeClass().addClass('fa fa-plus-square-o');

                            oTable.fnClose(nTr);
                        }
                        else {
                            if ($($(nTr).children().children()[0]).hasClass('fa fa-plus-square-o')) {
                                var columnsList = [];
                                classHiddenMd = '';
                                classHiddenLg = '';



                                for (var x in colArray) {
                                    if (x >= 2) {
                                        columnsList.push(colArray[x]);
                                    }
                                }


                                if (colArray.length >= 2 && colArray.length <= 4) {
                                    classHiddenMd = 'hidden-md hidden-lg';
                                }
                                if (colArray.length >= 4 && colArray.length <= 6) {
                                    classHiddenLg = 'hidden-lg vissible-md';
                                }
                                /* Open this row */
                                $($(nTr).children().children()[0]).removeClass().addClass('fa fa-minus-square-o');
                                if (colArray.length > 2) {
                                    oTable.fnOpen(nTr, createHiddenTable(oTable, nTr, columnsList), 'details vissible-sm vissible-xs' + classHiddenMd + ' ' + classHiddenLg);
                                }
                            }
                        }
                    }

                    $(document).on('click', '#tableReports tbody td', function () {
                        var selectRow = $(this).parent();
                        oTable.$('tr.row_selected').removeClass('row_selected');

                        if (!selectRow.hasClass('row_selected')) {
                            selectRow.addClass('row_selected');
                        }

                        if (linkValue == 'Loan Amortization')
                            $('#exportPrint,#exportPdf,#exportExcel,#exportAsText').show();
                        fnGridExpan(selectRow, colArray);
                    });
                    
                    /****************  Apply Filter Form ******************/
                    $('#filterApplyReports').on('click', function () {
                        fnFilterClose('Reports');
                        setMask('#wholepage');
                        $.ajax({
                            type: 'POST',
                            dataType: 'json',
                            async: true,
                            url: pageUrl() + 'reports/hr-reports/create-barchart/tableData/tableData',
                            data: { _modName: modName, linkValue: linkValue, _ctHt: '338', _ctWt: '500', _ctRent: 'bar' },
                            success: function (tableData) {
                                var filterTable = tableData.Filter_Table;
                                var RepFilter = tableData.Rep_Filter;
                                var res = filterTable.split(",");
                                var filterHeading = RepFilter.split("-");

                                var filterArray = [];

                                for (var i in res) {
                                    if (filterHeading[i] == 'Employee Name' || filterHeading[i] == 'Manager Name') {
                                        res[i] = 'Employee Name';
                                    }
                                    switch (res[i]) {
                                        case 'DPicker':
                                            var finalBeginDate = $('#filterDateBeginid' + i).val();
                                            var finalEndDate = $('#filterDateEndid' + i).val();

                                            if (finalBeginDate !== '') {
                                                finalBeginDate = fnFormatDate(finalBeginDate);
                                            }

                                            if (finalEndDate !== '') {
                                                finalDate = fnFormatDate(finalEndDate);
                                                var finalEndDate = finalBeginDate + '&' + finalDate;
                                            }

                                            filterArray.push(finalEndDate);
                                            break;

                                        case 'MPicker':
                                            filterValue = fnServerMonthFormatter($('#filterid' + i).val());
                                            filterArray.push(filterValue);
                                            break;
                                        case 'Employee Name':
                                            filterValue = $('#filterid' + i).val();
                                            filterArray.push(filterValue);
                                            break;
                                        case 'Amount':
                                            var filterAmountStart = $('#filterAmountStartid' + i).val();
                                            var filterAmountEnd = $('#filterAmountEndid' + i).val();
                                            var finalAmount = '',
                                                finalBegin = '',
                                                finalEnd = '';

                                            if (filterAmountStart !== '') {
                                                finalBegin = filterAmountStart;
                                            }

                                            if (filterAmountEnd !== '') {
                                                finalEnd = filterAmountEnd;
                                            }
                                            finalAmount = finalBegin + '&' + finalEnd;

                                            filterArray.push(finalAmount);
                                            break;
                                        default:
                                            filterValue = $('#filterid' + i).select2('val');
                                            if (typeof filterValue == 'object') {
                                                filterValue = '';
                                            }
                                            filterArray.push(filterValue);
                                            break;
                                    }
                                }

                                if (linkValue == 'Employee Wise Expenses') {
                                    var filterArray = [];
                                    var filterValue4 = $('#filterid0').val();
                                    var filterValue5 = fnFormatDate($('#filterDateBeginid1').val());
                                    var filterValue6 = fnFormatDate($('#filterDateEndid1').val());
                                    filterArray.push(filterValue4, filterValue5, filterValue6);

                                    $.ajax({
                                        type: 'POST',
                                        dataType: 'json',
                                        async: true,
                                        url: pageUrl() + 'reports/hr-reports/dyn-filters/linkValue/' + linkValue + '/_modName/' + modName + "/filterArray/" + filterArray,
                                        success: function (result) {
                                            var reqArray = result.grid;
                                            var colArray = [];
                                            var gridKey = reqArray.grid_key;
                                            var aoColumns = [];
                                            var aoColumnDefs = [];

                                            if (gridKey != undefined) {
                                                for (var i = 0; i < gridKey.length; i++) {
                                                    colArray.push(gridKey[i]);
                                                }
                                            }

                                            var columnLength = colArray.length;

                                            //********* if More than 5 fields then show extra columns on expand ******//
                                            if (columnLength > 2)  //if(colArray.length >5)
                                            {

                                                aoColumnDefs.push({ "targets": 0, "orderable": false });
                                                /** Add Plus button with empty header for expand, So default sorting column index will be 1 **/
                                                var sortColumnIndex = 1;
                                                aoColumns.push({
                                                    "mData": function (row, type, set) {
                                                        return '<i class="fa fa-plus-square-o"></i>';
                                                    }
                                                });

                                                if (columnLength <= 6) {
                                                    aoColumnDefs.push({ "sClass": "hidden-md hidden-lg visible-sm visible-xs", "aTargets": [0] });
                                                }
                                                for (var x in colArray) {
                                                    if (x < 6) {
                                                        aoColumns.push({ "mData": fnCheckNull(colArray[x]) });
                                                    }
                                                }
                                                if (columnLength > 4) {
                                                    columnMdLength = 4;
                                                }
                                                else {
                                                    columnMdLength = columnLength;
                                                }

                                                if (columnLength > 6) {
                                                    columnLen = 6;
                                                }
                                                else {
                                                    columnLen = columnLength;
                                                }
                                                for (var y = 3; y <= columnMdLength; y++) {
                                                    aoColumnDefs.push({ "sClass": "hidden-sm hidden-xs visible-md visible-lg", "aTargets": [parseInt(y)] });
                                                }
                                                for (var y = 5; y <= columnLen; y++) {
                                                    aoColumnDefs.push({ "sClass": "hidden-sm hidden-xs hidden-md visible-lg", "aTargets": [parseInt(y)] });
                                                }
                                            }
                                            else {
                                                /*** Default column index will be zero***/
                                                var sortColumnIndex = 0;
                                                for (var x in colArray) {
                                                    aoColumns.push({ "mData": fnCheckNull(colArray[x]) });
                                                }
                                            }

                                            //Remove previously generated custom report data table form the grid panel
                                            $("#divGrid").html('');
                                            var customTable = '';
                                            customTable = '<table class="table table-hover table-dynamic table-striped" id="tableCustomReports">' +
                                                '<thead>' +
                                                '<tr>';
                                            if (gridKey.length > 2) {
                                                customTable += '<th></th>';
                                            }
                                            for (var x in gridKey) {
                                                //we can show maximum 6 columns in the grid.
                                                if (x < 6)

                                                    customTable += '<th>' + gridKey[x].replace(/_/g, ' '); + '</th>';
                                            }

                                            customTable += '</tr>' +
                                                '</thead>' +
                                                '<tbody>' +
                                                '</tbody>' +
                                                '</table>';

                                            $("#divGrid").append(customTable);


                                            $('#tableCustomReports').dataTable({
                                                "lengthMenu": [5, 10, 25, 50, 100],
                                                "iDisplayLength": 10,
                                                "bDestroy": true,
                                                "bAutoWidth": false,
                                                "bServerSide": true,
                                                "bDeferRender": true,
                                                "bFilter": false,
                                                "sServerMethod": "POST",
                                                "sAjaxSource": pageUrl() + "reports/hr-reports/bind-reports/linkValue/" + linkValue + "/_modName/" + modName + "/filterArray/" + filterArray,
                                                "sAjaxDataProp": "aaData",
                                                "aoColumnDefs": aoColumnDefs,
                                                "aaSorting": [sortColumnIndex, 'asc'],
                                                "aoColumns": aoColumns,
                                                "fnInitComplete": function (oSettings, json) {
                                                    removeMask();
                                                }
                                            });
                                        },
                                        error: function (error) {
                                            if (error.status == 200) {
                                                sessionExpired();
                                            }
                                            else {
                                                /* To handle internal server error */
                                                jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                                            }
                                            removeMask();
                                        }
                                    });

                                }
                                else if (linkValue == 'Employee Utilization' || linkValue == 'Additional Wage Summary' || linkValue == 'Attendance Muster Info' 
                                || linkValue == 'Absentees' || linkValue == 'Attendance Register')
                                {
                                        var dateArr = filterArray[1].split('&');
                                        var days = fnDifferBwDates(dateArr[0], dateArr[1]);
                                        if (days <= 31 && days >= 0) 
                                        {
                                            oTable.fnReloadAjax(pageUrl() + "reports/hr-reports/bind-reports/linkValue/" + linkValue + "/_modName/" + modName + "/filterArray/" + filterArray);
                                            createCharts(filterArray);
                                            removeMask();
                                        }
                                        else 
                                        {
                                            jAlert({ msg: 'Attendance Start date and End date Should not be greater than 31 days', type: 'info' });
                                            removeMask();
                                        }
                                }
                                else if(linkValue == 'Attendance And Absence Overview')
                                {
                                    var dateArr = filterArray[1].split('&');
                                    var days = fnDifferBwDates(dateArr[0], dateArr[1]);
                                    if (dateArr[0]==dateArr[1]) 
                                    {
                                        oTable.fnReloadAjax(pageUrl() + "reports/hr-reports/bind-reports/linkValue/" + linkValue + "/_modName/" + modName + "/filterArray/" + filterArray);
                                        createCharts(filterArray);
                                        removeMask();
                                    }
                                    else 
                                    {
                                        jAlert({ msg: 'Attendance Start date and End date should be same', type: 'info' });
                                        removeMask();
                                    }
                                }
                                else if (linkValue == 'Timesheet' || linkValue === 'Timesheet Comprehensive') {
                                    var dateArr = filterArray[1].split('&');
                                    var days = fnDifferBwDates(dateArr[0], dateArr[1]);
                                    var timesheetPeriod = parseInt($('#timesheetPeriod').val(),10);

                                    if (days < timesheetPeriod && days >= 0) {
                                        oTable.fnReloadAjax(pageUrl() + "reports/hr-reports/bind-reports/linkValue/" + linkValue + "/_modName/" + modName + "/filterArray/" + filterArray);
                                        createCharts(filterArray);
                                        removeMask();
                                    }
                                    else 
                                    {
                                        jAlert({ msg: 'Weekend Start and End date should not be greater than '+timesheetPeriod+' days', type: 'info' });
                                        removeMask();
                                    }
                                }
                                else if (linkValue == 'Eft Monthly' || linkValue == 'Eft Hourly') {
                                    let filterArray = [];
        
                                    let filterValue1 = $('#filterid0').val();
                                    let filterValue2 = $('#filterid1').val();
                                    let filterValue3 = $('#filterid2').val();
                                    let filterValue4 = fnServerMonthFormatter($('#filterid3').val());
                                    let filterValue5 = $('#filterid4').val();
                                    let filterValue6 = $('#filterid5').val();
                                    let filterValue7 = $('#filterid6').val();
                                    filterArray.push(filterValue1, filterValue2, filterValue3, filterValue4, filterValue5, filterValue6, filterValue7, filterServiceProvider);
                                    $.ajax({
                                        type: 'POST',
                                        dataType: 'json',
                                        async: false,
                                        url: pageUrl() + 'reports/hr-reports/dyn-filters/linkValue/' + linkValue + '/_modName/' + modName+'/filterArray/'+filterArray,
                                        success: function (result) {
                                            var reqArray = result.grid;
                                            var colArray = [];
                                            var gridKey = reqArray.grid_key;
                                            var aoColumns = [];
                                            var aoColumnDefs = [];
                                        
                                            if (gridKey != undefined) {
                                                for (var i = 0; i < gridKey.length; i++) {
                                                    colArray.push(gridKey[i]);
                                                }
                                            }

                                            var columnLength = colArray.length;

                                            //********* if More than 5 fields then show extra columns on expand ******//
                                            if (columnLength > 2)  //if(colArray.length >5)
                                            {
                                                aoColumnDefs.push({ "targets": 0, "orderable": false });

                                                /** Add Plus button with empty header for expand, So default sorting column index will be 1 **/
                                                var sortColumnIndex = 1;
                                                var sortOrder = 'asc';

                                                aoColumns.push({
                                                    "mData": function (row, type, set) {
                                                        return '<i class="fa fa-plus-square-o"></i>';
                                                    }
                                                });

                                                if (columnLength <= 6) {
                                                    aoColumnDefs.push({ "sClass": "hidden-md hidden-lg visible-sm visible-xs", "aTargets": [0] });
                                                }
                                                for (var x in colArray) {
                                                    if (x < 6) {
                                                        aoColumns.push({ "mData": fnCheckNull(colArray[x]) });
                                                    }
                                                }
                                                if (columnLength > 4) {
                                                    columnMdLength = 4;
                                                }
                                                else {
                                                    columnMdLength = columnLength;
                                                }

                                                if (columnLength > 6) {
                                                    columnLen = 6;
                                                }
                                                else {
                                                    columnLen = columnLength;
                                                }

                                                for (var y = 3; y <= columnMdLength; y++) {
                                                    aoColumnDefs.push({ "sClass": "hidden-sm hidden-xs visible-md visible-lg", "aTargets": [parseInt(y)] });
                                                }
                                                for (var y = 5; y <= columnLen; y++) {
                                                    aoColumnDefs.push({ "sClass": "hidden-sm hidden-xs hidden-md visible-lg", "aTargets": [parseInt(y)] });
                                                }
                                            }
                                            else {
                                                /*** Default column index will be zero***/
                                                var sortColumnIndex = 0;
                                                for (var x in colArray) {
                                                    aoColumns.push({ "mData": fnCheckNull(colArray[x]) });
                                                }
                                            }

                                            //Remove previously generated custom report data table form the grid panel
                                            $("#divGrid").html('');
                                            var customTable = '';
                                            let dynamicTableId = 'tableReports'+filterValue1;

                                            customTable = '<table class="table table-hover table-dynamic table-striped" id="'+dynamicTableId+'">' +
                                                '<thead>' +
                                                '<tr>';
                                            if (gridKey.length > 2) {
                                                customTable += '<th></th>';
                                            }
                                            for (var x in gridKey) {
                                                //we can show maximum 6 columns in the grid.
                                                if (x < 6)

                                                    customTable += '<th>' + gridKey[x].replace(/_/g, ' '); + '</th>';
                                            }

                                            customTable += '</tr>' +
                                                '</thead>' +
                                                '<tbody>' +
                                                '</tbody>' +
                                                '</table>';

                                            $("#divGrid").append(customTable);


                                            oTable = $('#'+dynamicTableId).dataTable({
                                                "lengthMenu": [5, 10, 25, 50, 100],
                                                "iDisplayLength": 10,
                                                "bDestroy": true,
                                                "bAutoWidth": false,
                                                "bServerSide": true,
                                                "bDeferRender": true,
                                                "sServerMethod": "POST",
                                                "bFilter": false,
                                                "sAjaxSource": pageUrl() + "reports/hr-reports/bind-reports/linkValue/" + linkValue + "/_modName/" + modName + "/filterArray/" + filterArray,
                                                "sAjaxDataProp": "aaData",
                                                "aoColumnDefs": aoColumnDefs,
                                                "aaSorting": [sortColumnIndex, 'asc'],
                                                "aoColumns": aoColumns,
                                                "fnInitComplete": function (oSettings, json) {
                                                    removeMask();

                                                    if (json.iTotalRecords == 0) {
                                                        $('#exportExcel,#exportAsText').hide();
                                                    }
                                                    else {
                                                        $('#exportExcel,#exportAsText').show();
                                                    }

                                                    if(linkValue === 'Eft Monthly' || linkValue === 'Eft Monthly'){
                                                        //Hide the text button when the payment type is icici without beneficiary and HDFC with beneficiary
                                                        if($('#filterid0').val()=='3' || $('#filterid0').val()=='2'){
                                                            $('#exportAsText').hide();
                                                        }
                                                    }
                                                }
                                            });

                                            
                                            $(document).on('click', '#'+dynamicTableId +' tbody td', function () {
                                                var selectRow = $(this).parent();
                                                oTable.$('tr.row_selected').removeClass('row_selected');
                        
                                                if (!selectRow.hasClass('row_selected')) {
                                                    selectRow.addClass('row_selected');
                                                }
                                                fnGridExpan(selectRow, colArray);
                                            });
                                            
                                            createCharts(filterArray);
                                            removeMask();
                                        }
                                    })
                                }
                                else
                                {
                                    oTable.fnReloadAjax(pageUrl() + "reports/hr-reports/bind-reports/linkValue/" + linkValue + "/_modName/" + modName + "/filterArray/" + filterArray);
                                    createCharts(filterArray);
                                    removeMask();
                                }
                            },
                            error: function (error) {
                                if (error.status == 200) {
                                    sessionExpired();
                                }
                                else {
                                    /* To handle internal server error */
                                    jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                                }
                                removeMask();
                            }
                        });
                       
                    });

                    /****************  Reset Filter Form ******************/

                    $('#filterResetReports,#closeFilterReports').on('click', function () {
                        if (linkValue == 'Eft Monthly' || linkValue == 'Eft Hourly') {
                            //Reset bulk upload file for value
                            $("#filterid0").val($("#filterid0 option:first").val());
                            $('#filterid1').select2('val', '');
                            $('#filterid2').select2('val', '');
                            //Trigger bulk upload file for
                            $('#filterid0').trigger('change');
                            $('#filterid4').select2('val', '');
                            $('#filterid5').select2('val', '');
                            $('#filterid6').select2('val', '');
                            // var lastDate = $('#lastPaySlipMonth').val();
                            // $('#filterid3').val(lastDate);
                            $('#filterid3').val('');
                            $('#filterApplyReports').trigger('click');
                        }else {
                            $.ajax(
                                {
                                    type: 'POST',
                                    dataType: 'json',
                                    async: false,
                                    url: pageUrl() + 'reports/hr-reports/create-barchart/tableData/tableData',
                                    data: { _modName: modName, linkValue: linkValue, _ctHt: '338', _ctWt: '500', _ctRent: 'bar' },
                                    success: function (tableData) {
                                        var filterTable = tableData.Filter_Table;
                                        var RepFilter = tableData.Rep_Filter;
                                        var res = filterTable.split(",");
                                        var filterHeading = RepFilter.split("-");

                                        var filterArray = [];
                                        for (var i in res) {

                                            if (filterHeading[i] == 'Employee Name' || (filterHeading[i] == 'Manager Name' && linkValue!='Attendance' && linkValue!='Leaves' && linkValue!='Short Time Off' && linkValue!='Compensatory Off' && linkValue!='Compensatory Off Balance' )) {
                                                res[i] = 'Employee Name';                                                
                                            }
                                            switch (res[i]) {
                                                case 'DPicker':
                                                    var dateBegin = '#filterDateBeginid' + i;
                                                    var dateEnd = '#filterDateEndid' + i;
                                                    var dateBeginDPicker = $('#filterDateBeginid' + i).val();
                                                    var dateEndDPicker = $('#filterDateBeginid' + i).val();
                                                    $('#filterDateBeginid' + i + ' , #filterDateEndid' + i).val('');
                                                    break;
                                                case 'MPicker':
                                                case 'Employee Name':
                                                    $('#filterid' + i).val('');
                                                    break;
                                                case 'Amount':
                                                    $('#filterAmountStartid' + i).val('');
                                                    $('#filterAmountEndid' + i).val('');
                                                    break;
                                                default:
                                                    $('#filterid' + i).select2('val', '');
                                                    break;
                                            }
                                        }
                                        createCharts('');
                                    }
                                });

                                  
                            if (linkValue == 'Esic Monthly' || linkValue == 'Esic Hourly' || linkValue == 'Pay Bill' || 
                                linkValue == 'Monthly Payslip Comprehensive'|| linkValue == 'Uan Based Ecr' || linkValue == "Uan Based Ecr Hourly" || linkValue.toLowerCase() === 'uan based ecr(arrear)'
                                || linkValue === 'Salary Register' || linkValue === 'Payment Register' || linkValue === 'Insurance Statement' 
                                || linkValue === 'Ssnit Tier 1' || linkValue === 'Ssnit Tier 2' || linkValue === 'Provident Fund Detailed Report' 
                                || linkValue.toLowerCase() === 'wps' || linkValue === 'Bimonthly Salary Register') {

                                var lastDate = $('#lastPaySlipMonth').val();
                                $('#filterid1').val(lastDate);
                                $('#filterApplyReports').trigger('click');
                            }

                            /**for setting punchin from and to date in attendance filter**/
                            if (linkValue == 'Attendance') {
                                var d = new Date(tzDate());
                                var firstDate = new Date(d.getFullYear(), d.getMonth(), 1);
                                $('#filterDateBeginid3').val($.datepicker.formatDate(dateFormat[0], firstDate));

                                var lastDate = new Date(d.getFullYear(), d.getMonth() + 1, 0);
                                $('#filterDateEndid3').val($.datepicker.formatDate(dateFormat[0], lastDate));
                                $('#filterApplyReports').trigger('click');

                            }
                            if (linkValue == 'Reimbursement Bank Statement') {
                                var d = new Date(tzDate());
                                var firstDate = new Date(d.getFullYear(), d.getMonth(), 1);
                                $('#filterDateBeginid1').val($.datepicker.formatDate(dateFormat[0], firstDate));

                                var lastDate = new Date(d.getFullYear(), d.getMonth() + 1, 0);
                                $('#filterDateEndid1').val($.datepicker.formatDate(dateFormat[0], lastDate));
                                $('#filterApplyReports').trigger('click');

                            }
                            if (linkValue == 'Attendance Summary Hourly' || linkValue == 'Attendance Summary Monthly' 
                            || linkValue == 'Employee Utilization' || linkValue == 'Additional Wage Summary' 
                            || linkValue == 'Attendance Muster Info' || linkValue == 'Attendance Register') {
                                var d = new Date(tzDate());
                                var firstDate = new Date(d.getFullYear(), d.getMonth(), 1);
                                $('#filterDateBeginid1').val($.datepicker.formatDate(dateFormat[0], firstDate));
                              
                                var lastDate = new Date(d.getFullYear(), d.getMonth() + 1, 0);
                                $('#filterDateEndid1').val($.datepicker.formatDate(dateFormat[0], d));

                                if(linkValue == 'Additional Wage Summary')
                                {
                                    $('#filterid0').select2('val', $('#loginEmpId').val());
                                }
                                $('#filterApplyReports').trigger('click');

                            }

                            if (linkValue == 'Reimbursement Expenses') {
                                var d = new Date(tzDate());
                                var firstDate = new Date(d.getFullYear(), d.getMonth(), 1);
                                $('#filterDateBeginid1').val($.datepicker.formatDate(dateFormat[0], firstDate));

                                var lastDate = new Date(d.getFullYear(), d.getMonth() + 1, 0);
                                $('#filterDateEndid1').val($.datepicker.formatDate(dateFormat[0], lastDate));
                                $('#filterApplyReports').trigger('click');

                            }
                            

                            if (linkValue == 'Esi Monthly' || linkValue == 'Esi Hourly') {
                                var lastDate = $('#lastPaySlipMonth').val();
                                if(linkValue == 'Esi Monthly') {
                                    $('#filterid4').val(lastDate);
                                } else {
                                    $('#filterid3').val(lastDate);
                                }
                                $('#filterApplyReports').trigger('click');
                            }

                            /** For the hourly wage payslip, monthly salary payslip report get the last payslip month value and set it for 
                             * 'Payslip from' and 'Payslip To' month picker in the filter during filter reset & filter close event */
                            if (linkValue === 'Hourly Wage Payslip' || linkValue === 'Monthly Salary Payslip'|| linkValue === 'Reimbursement Allowances' || linkValue == 'Bank Salary Statement'
                                || linkValue == 'Fixed Health Insurance') {
                                var lastDate = $('#lastPaySlipMonth').val();
                                $('#filterid1,#filterid2').val(lastDate);
                                $('#filterApplyReports').trigger('click');
                            }

                            //If the link value is 'Form16 Summary(Monthly)' reset the max assessment year
                            if (linkValue == 'Form16 Summary(Monthly)') {
                                $('#filterid1').select2('val', $('#form16SummaryMonthlyMaxAssessmentYear').val());
                                $('#filterApplyReports').trigger('click');
                            }

                            if (linkValue == 'Pt Annual Return(Form 5a)') {
                                $('#filterid0').select2('val', $('#form16SummaryMonthlyMaxAssessmentYear').val());
                                $('#filterApplyReports').trigger('click');
                            }
                            
                            //If the link value is 'tds' reset it to the last payslip month
                            if (linkValue == 'Tds') {
                                var lastDate = $('#lastPaySlipMonth').val();
                                $('#filterid1,#filterid2').val(lastDate);
                                $('#filterApplyReports').trigger('click');
                            }

                            if ($('#fieldForce').val() == 1 && (linkValue == "Attendance Summary Hourly" || linkValue == "Attendance Summary Monthly" 
                            || linkValue == "Attendance Muster Info" || linkValue == 'Employee Utilization' || linkValue == 'Additional Wage Summary'
                            || linkValue == 'Absentees' || linkValue == 'Attendance And Absence Overview' || linkValue == "Attendance Register"))
                            {
                                $('.ServiceProvider').select2('val', $('#serviceProviderId').val());
                            }

                            //If the link value is 'timesheet' reset it to the current week end date
                            if (linkValue == 'Timesheet' || linkValue == 'Absentees'|| linkValue == 'Timesheet Comprehensive' || linkValue == 'Attendance And Absence Overview') {
                                var startDate = $('#startDate').val();
                                $('#filterDateBeginid1,#filterDateEndid1').val(startDate);
                                $('#filterApplyReports').trigger('click');
                            }
                            else
                            {
                                oTable.fnReloadAjax(pageUrl() + "reports/hr-reports/bind-reports/linkValue/" + linkValue + "/_modName/" + modName);
                            }
                        }
                        fnFilterClose('Reports');
                    });

                    if (linkValue == 'New Joinees' || linkValue == 'Probation' || linkValue == 'Attendance Shortage' || linkValue == 'Attendance Summary Hourly' 
                        || linkValue == 'Attendance' || linkValue == 'Reimbursement Bank Statement' || linkValue == 'Shift Report' || linkValue == 'Shift Unassigned') {
                        $('#filterApplyReports').trigger('click');
                    }
                    /**
                     * Department wise segregation
                    */
                    $('#formDivisionDetails').on('change', function () {
                        var formDivisionId = $('#s2id_formDivisionDetails').select2('val');
                        hrappDepartmentClassification(formDivisionId, oTable, '');
                    });

                    /**
                     * Grid Refresh
                    **/

                    $('#gridPanelReports .panel-reload').on('click', function () {
                        fnRefreshTable(oTable);
                        fnFilterClose('Reports');
                    });

                    /**
                     *  close filter form and clear enabled buttons based on selection record
                     *  while search all, sorting, pagination, redraw events
                     *  it will work in search.dt order.dt page.dt, length.dt those events
                    */
                    $('#tableReports').on('draw.dt', function () {
                        fnFilterClose('Reports');
                        var oSettings = oTable.fnSettings();
                        var iTotalRecords = oSettings.fnRecordsTotal();

                        if (iTotalRecords == 0) {
                            $('#exportPrint,#exportPdf,#exportExcel,#exportAsText').hide();
                        }
                        else {
                            $('#exportPrint,#exportPdf,#exportExcel,#exportAsText').show();
                        }

                        if(linkValue === 'Eft Monthly' || linkValue === 'Eft Monthly'){
                            //Hide the text button when the payment type is icici without beneficiary and HDFC with beneficiary
                            if($('#filterid0').val()=='3' || $('#filterid0').val()=='2'){
                                $('#exportAsText').hide();
                            }
                        }

                        if (linkValue == 'Loan Amortization')
                            $('#exportPrint,#exportPdf,#exportExcel,#exportAsText').hide();

                    });

                    $('#exportPrint').off('click').on('click', function () {
                        $('#PrintScreen,#exitPrint').removeClass('hidden');
                        $("#printReportsTable").remove();
                        fnFilterClose('Reports');
                        $.ajax({
                            type: 'POST',
                            dataType: 'json',
                            async: false,
                            url: pageUrl() + 'reports/hr-reports/create-barchart/tableData/tableData',
                            data: { _modName: modName, linkValue: linkValue },
                            success: function (tableData) {
                                var filterArray = [];
                                var filterTable = tableData.Filter_Table;
                                var RepFilter = tableData.Rep_Filter;
                                var res = filterTable.split(",");
                                var filterHeading = RepFilter.split("-");

                                for (var i in res) {
                                    if (filterHeading[i] == 'Employee Name' || filterHeading[i] == 'Manager Name') {
                                        res[i] = 'Employee Name';
                                    }
                                    switch (res[i]) {
                                        case 'DPicker':
                                            var dateBegin = '#filterDateBeginid' + i;
                                            var dateEnd = '#filterDateEndid' + i;
                                            var finalBeginDate = $(dateBegin).val();
                                            if (finalBeginDate != '') {
                                                finalBeginDate = fnFormatDate(finalBeginDate);
                                            }
                                            var finalEndDate = $(dateEnd).val();
                                            if (finalEndDate != '') {
                                                finalDate = fnFormatDate(finalEndDate);
                                                var finalEndDate = finalBeginDate + '&' + finalDate;
                                            }
                                            filterArray.push(finalEndDate);
                                            break;
                                        case 'MPicker':
                                            filterValue = fnServerMonthFormatter($('#filterid' + i).val());
                                            filterArray.push(filterValue);
                                            break;
                                        case 'Employee Name':
                                            filterValue = $('#filterid' + i).val();
                                            filterArray.push(filterValue);
                                            break;
                                        case 'Amount':
                                            var filterAmountStart = $('#filterAmountStartid' + i).val();
                                            var filterAmountEnd = $('#filterAmountEndid' + i).val();
                                            var finalAmount = '',
                                                finalBegin = '',
                                                finalEnd = '';

                                            if (filterAmountStart !== '') {
                                                finalBegin = filterAmountStart;
                                            }

                                            if (filterAmountEnd !== '') {
                                                finalEnd = filterAmountEnd;
                                            }
                                            finalAmount = finalBegin + '&' + finalEnd;

                                            filterArray.push(finalAmount);
                                            break;
                                        default:
                                            filterValue = $('#filterid' + i).select2('val');
                                            filterArray.push(filterValue);
                                            break;
                                    }
                                }
                                var barX = tableData.Bar_X
                                var barY = tableData.Stack_Categ_Point;
                                var pieField = tableData.Pie_Field;
                                var checkedValue = [];
                                var exportArray = [];


                                if (linkValue == 'Employee Wise Expenses') {
                                    var filterArray = [];
                                    var filterValue4 = $('#filterid0').val();
                                    var filterValue5 = fnFormatDate($('#filterDateBeginid1').val());
                                    var filterValue6 = fnFormatDate($('#filterDateEndid1').val());
                                    filterArray.push(filterValue4, filterValue5, filterValue6);

                                    $.ajax({
                                        type: "POST",
                                        url: pageUrl() + 'reports/hr-reports/preview/filterArray/' + filterArray,
                                        data: {
                                            'linkValue': linkValue,
                                            '_modName': modName,
                                        },
                                        success: function (response) {
                                            $(".printable_portion").html('');
                                            $(".printable_portion").append("<div style ='width:100%;float:left;margin:5px;text-align:center;font-weight:bold;font-size:18px'>" + linkValue + " Reports</div><br>");
                                            responseArr = JSON.parse(response);
                                            dynTableForPreview(responseArr, exportArray, 'N');
                                        }
                                    });

                                }
                                else if (linkValue == 'Loan Amortization') {
                                    var selectedRow = fnGetSelected(oTable);
                                    var record = oTable.fnGetData(selectedRow[0]);

                                    $.ajax({
                                        type: "POST",
                                        url: pageUrl() + 'reports/hr-reports/preview/filterArray/' + filterArray,
                                        data: {
                                            'linkValue': linkValue,
                                            '_modName': modName,
                                            'loanId': record['Loan_Id']
                                        },
                                        success: function (response) {
                                            $(".printable_portion").html('');
                                            $(".printable_portion").append("<div style ='width:100%;float:left;margin:5px;text-align:center;font-weight:bold;font-size:18px'>" + linkValue + " Reports</div><br>");
                                            responseArr = JSON.parse(response);
                                            dynTableForPreview(responseArr, exportArray, 'N');
                                        }
                                    });
                                }
                                else {
                                    $.ajax({
                                        type: 'POST',
                                        dataType: 'json',
                                        async: false,
                                        url: pageUrl() + 'reports/hr-reports/create-barchart/tableData/tableData',
                                        data: { _modName: modName, linkValue: linkValue },
                                        success: function (result) {
                                            var datatableMenuLength = oTable.fnGetData().length;
                                            rtwoLength = 0;
                                            if (result[2] != undefined && result[2] != '') {
                                                rtwoLength = result[2].length;
                                            }
                                            /***** if charts are not require then directly print Grid *****/
                                            if (($.inArray(barX, [null, '', 0]) !== -1 || $.inArray(barY, [null, '', 0]) !== -1) || ((tableData['Flag_Stack'] == 1 && rtwoLength == 1) ||
                                                (tableData['Flag_Stack'] == 0 && result.length == 1) || datatableMenuLength == 1)) {

                                                if ((linkValue == 'Esic Hourly' || linkValue == 'Esic Monthly') && (filterArray[2] < 0 || filterArray[2] == '')) {
                                                    jAlert({ msg: 'Please select the Insurance Type And Salary Month before exporting this report', type: 'info' });

                                                }
                                                else {
                                                    $.ajax({
                                                        type: "POST",
                                                        url: pageUrl() + 'reports/hr-reports/preview/filterArray/' + filterArray,
                                                        data: {
                                                            'linkValue': linkValue,
                                                            '_modName': modName,
                                                        },
                                                        success: function (response) {
                                                            responseArr = JSON.parse(response);
                                                            dynTableForPreview(responseArr, exportArray, 'N');
                                                        }
                                                    });
                                                }
                                            }
                                            else {
                                                $('#formPrintPieChart,#formPrintBarChart,#formPrintGrid,#formPrintAll').prop('checked', false);
                                                $('#modalExportPrint').modal('toggle');
                                                $('#formPrintAll').on('click', function () {
                                                    var isCheckedAll = $('#formPrintAll').is(':checked');
                                                    if (isCheckedAll === true) {
                                                        $('#formPrintPieChart,#formPrintBarChart,#formPrintGrid').prop('checked', true);
                                                    }
                                                    else {
                                                        $('#formPrintPieChart,#formPrintBarChart,#formPrintGrid').prop('checked', false);
                                                    }
                                                });

                                                $('#formPrintPieChart,#formPrintBarChart,#formPrintGrid').on('click', function () {
                                                    var isCheckedBar = $('#formPrintBarChart').is(':checked');
                                                    var isCheckedPie = $('#formPrintPieChart').is(':checked');
                                                    var isCheckedGrid = $('#formPrintGrid').is(':checked');
                                                    if (isCheckedBar === false || isCheckedPie === false || isCheckedGrid === false) {
                                                        $('#formPrintAll').prop('checked', false);
                                                    }
                                                    else if (isCheckedBar === true && isCheckedPie === true && isCheckedGrid === true) {
                                                        $('#formPrintAll').prop('checked', true);
                                                    }
                                                });
                                                $('#exportAsPrint').on('click', function () {
                                                    var printItems = [];
                                                    var isCheckedBar = $('#formPrintBarChart').is(':checked');
                                                    var isCheckedPie = $('#formPrintPieChart').is(':checked');
                                                    var isCheckedGrid = $('#formPrintGrid').is(':checked');

                                                    if (isCheckedBar === true) {
                                                        printItems.push('Bar');
                                                    }
                                                    if (isCheckedPie === true) {
                                                        printItems.push('Pie');
                                                    }
                                                    if (isCheckedGrid === true) {
                                                        printItems.push('Grid');
                                                    }
                                                    //printItems = ['Grid','Bar','Pie'];
                                                    if (printItems.length > 0) {

                                                        $(".printable_portion").html('');
                                                        $(".printable_portion").append("<div style ='width:100%;float:left;margin:5px;text-align:center;font-weight:bold;font-size:18px'>" + linkValue + " Reports</div><br>");
                                                        if ($.inArray('Grid', printItems) != -1) {
                                                            $.ajax({
                                                                type: "POST",
                                                                url: pageUrl() + 'reports/hr-reports/preview/filterArray/' + filterArray,
                                                                data: {
                                                                    'linkValue': linkValue,
                                                                    '_modName': modName,
                                                                    'filterArray': filterArray
                                                                },
                                                                success: function (response) {
                                                                    responseArr = JSON.parse(response);
                                                                    dynTableForPreview(responseArr, printItems, 'N');
                                                                }
                                                            });
                                                        }
                                                        else {
                                                            cloneCharts(printItems, 'portrait');
                                                        }
                                                    }
                                                    else {

                                                    }
                                                });
                                            }
                                        }
                                    });
                                }
                            }
                        });

                        function dynTableForPreview(responseArr, printItems, printView) {
                            var tableStr = '';
                            var checkedValue = '';
                            headArr = responseArr[0];
                            bodyArr = responseArr[1];
                            rowData = responseArr[2];

                            var selectColumnsFor = ['Hourly Wage Payslip', 'Monthly Master Report', 'Hourly Master Report'];
                            if ($.inArray(linkValue, selectColumnsFor) != -1) {
                                var checkedValue = [];
                                $('#modalPrintColumnsList').modal('toggle');

                                var filterArray = chartFilters(responseArr[2]);
                                $('#submitPrintColumnsList').on('click', function () {
                                    checkedValue = $('#selectPrintColumnsList').select2('val');
                                    if (checkedValue != '') {
                                        $.ajax({
                                            type: 'POST',
                                            url: pageUrl() + 'reports/hr-reports/print-report',
                                            data:
                                            {
                                                'linkValue': linkValue,
                                                '_ckedValue': checkedValue,
                                                '_filterArray': filterArray,
                                                '_modName': modName
                                            },
                                            async: false,
                                            //async: true,
                                            success: function (resultTblStr) {
                                                resultTblStr = JSON.parse(resultTblStr);
                                                tableStr = resultTblStr;


                                                $(".printable_portion").html('');
                                                $(".printable_portion").append("<div style ='width:100%;float:left;margin:5px;text-align:center;font-weight:bold;font-size:18px'>" + linkValue + " Reports</div><br>");
                                                $(".printable_portion").append(tableStr);

                                                var printView = $("input[type='radio'][name='printView']:checked").val();
                                                cloneCharts(printItems, printView);
                                                $('#modalPrintColumnsList').modal('hide');
                                            }
                                        });
                                    }
                                });
                            }
                            else if (linkValue == 'Loan Amortization') {
                                filterVisibleData = responseArr[3];
                                tabVal = bodyArr[0];
                                tableStr = tableStr + '<table border="0" width="98%"><tr><td style="padding-left: 3%;width: 15%;">Employee Id :</td><td>' + tabVal['User_Defined_EmpId'] + '</td></tr>' +
                                    '<tr><td style="padding-left: 3%;width: 15%;">Employee Name :</td><td>' + tabVal['Employee_Name'] + '</td></tr>' +
                                    '<tr><td style="padding-left: 3%;width: 15%;">Loan Amount :</td><td> Rs.' + tabVal['Loan_Amount'] + '</td></tr>' +
                                    '<tr><td style="padding-left: 3%;width: 15%;">Loan Tenure :</td><td>' + tabVal['Duration_In_Months'] + ' months</td></tr>' +
                                    '<tr><td style="padding-left: 3%;width: 15%;">Interest :</td><td>' + tabVal['Interest'] + '% </td></tr></table>';

                                tableStr = tableStr + '<table id="printReportsTable" class="reports_grid" border="0" width="98%"><tr bgcolor="#BDBDBD" style="font-weight:bold;border-bottom:1px solid #BDBDBD; font-size:14px; padding-bottom:2px">';

                                bodyArr = bodyArr[1];
                                for (var i = 0; i < headArr.length; i++) {
                                    tableStr = tableStr + '<th bgcolor="#BDBDBD" class = "fontSize" height="40" style="text-align:left;border-bottom:1px solid #BDBDBD; padding-bottom:10px; padding:2px; font-size:14px">' + headArr[i].replace(/[_]/g, " ") + '</th>';
                                }
                                tableStr = tableStr + '</tr>';
                                for (var i = 0; i < bodyArr.length; i++) {
                                    tableStr += '<tr style="text-align:left;">';
                                    for (var j = 0; j < headArr.length; j++) {
                                        var arr = bodyArr[i][headArr[j]];
                                        if (arr != undefined && arr != null) {
                                            tableStr = tableStr + '<td height="40" class = "fontSize" style="border-bottom:1px solid #BDBDBD; padding:2px; text-align:left;">' + bodyArr[i][headArr[j]] + '</td>';
                                        }
                                        else {
                                            tableStr = tableStr + '<td height="40" class = "fontSize" style="border-bottom:1px solid #BDBDBD; text-align:left;">-</td>';
                                        }
                                    }
                                }
                                tableStr = tableStr + '</tr></table><br><br>';
                                $(".printable_portion").append(tableStr);
                                var $table = $(".print-preview").css({ position: "absolute", visibility: "hidden", display: "block" });;
                                var tableWidth = $table.find('.reports_grid').width();
                                cloneCharts(printItems, 'portrait');
                            }
                            else if (linkValue == 'Employee Wise Expenses') {
                                var employeeDetails = responseArr[3];
                                var employeeDetails = '<div style="font-size:15px;margin-left:15px;">Employee Code :' + employeeDetails["User_Defined_EmpId"] + '</div>'
                                    + '<div style="font-size:15px;margin-left:15px;">Employee Name:' + employeeDetails["Employee_Name"] + '</div>'
                                    + '<div style="font-size:15px;margin-left:15px;">Designation :' + employeeDetails["Designation_Name"] + '</div>'
                                    + '<div style="font-size:15px;margin-left:15px;">Location :' + employeeDetails["Location_Name"] + '</div>'
                                    + '<div style="font-size:15px;margin-left:15px;">Expense Statement for the Period from ' + employeeDetails["Start_Date"] + ' ' + 'to' + ' ' + employeeDetails["End_Date"] + '<br/></div>'
                                    + '<br/>';
                                filterVisibleData = responseArr[3];
                                tableStr = tableStr + '<table id="printReportsTable" class="reports_grid" border="0" width="98%"><tr bgcolor="#BDBDBD" style="font-weight:bold;border-bottom:1px solid #BDBDBD; font-size:14px; padding-bottom:2px">';
                                tableStr = tableStr + employeeDetails;
                                for (var i = 0; i < headArr.length; i++) {
                                    tableStr = tableStr + '<th bgcolor="#BDBDBD" class = "fontSize" height="40" style="text-align:left;border-bottom:1px solid #BDBDBD; padding-bottom:10px; padding:2px; font-size:14px">' + headArr[i].replace(/[_]/g, " ") + '</th>';
                                }
                                tableStr = tableStr + '</tr>';
                                for (var i = 0; i < bodyArr.length; i++) {
                                    tableStr += '<tr style="text-align:left;">';
                                    for (var j = 0; j < headArr.length; j++) {
                                        var arr = bodyArr[i][headArr[j]];
                                        if (arr != undefined && arr != null) {
                                            tableStr = tableStr + '<td height="40" class = "fontSize" style="border-bottom:1px solid #BDBDBD; padding:2px; text-align:left;">' + bodyArr[i][headArr[j]] + '</td>';
                                        }
                                        else {
                                            tableStr = tableStr + '<td height="40" class = "fontSize" style="border-bottom:1px solid #BDBDBD; text-align:left;">-</td>';
                                        }
                                    }
                                }
                                tableStr = tableStr + '</tr></table><br><br>';
                                $(".printable_portion").append(tableStr);
                                var $table = $(".print-preview").css({ position: "absolute", visibility: "hidden", display: "block" });;
                                var tableWidth = $table.find('.reports_grid').width();
                                cloneCharts(printItems, 'portrait');
                            }
                            //monthly esi
                            else if (linkValue == 'Esi Monthly'|| linkValue == 'Esi Hourly') {
                                var filterArray = chartFilters(responseArr[2]);
                                var checkedValue = [];
                                if (linkValue == 'Esi Hourly') {
                                    checkedValue = ['Employee_Id', 'Employee_Name', 'ESI_No', 'Salary_Month', 'Total_HourlyWages', 'Total_OvertimeWages', 'Holiday_SpecialWages', 'Sum_of_allowance', 'Sum_of_adhoc_allowance','ESI_Wages', 'ESI_EE', 'ESI_ER'];
                                }
                                else {
                                    checkedValue = ['Employee_Id', 'Employee_Name', 'ESI_No', 'Salary_Month', 'Basic_Pay', 'Sum_of_allowance', 'Sum_of_adhoc_allowance', 'Holiday_Special_Wages','Total_Overtime_Wages','Unpaid_Deduction', 'ESI_Wages', 'ESI_EE', 'ESI_ER'];
                                }

                                $.ajax({
                                    type: 'POST',
                                    url: pageUrl() + 'reports/hr-reports/print-report',
                                    data:
                                    {
                                        'linkValue': linkValue,
                                        '_ckedValue': checkedValue,
                                        '_filterArray': filterArray,
                                        '_modName': modName
                                    },
                                    async: false,
                                    //async: true,
                                    success: function (resultTblStr) {
                                        resultTblStr = JSON.parse(resultTblStr);
                                        tableStr = resultTblStr;
                                        $(".printable_portion").html('');
                                        $(".printable_portion").append("<div style ='width:100%;float:left;margin:5px;text-align:center;font-weight:bold;font-size:18px'>" + linkValue + " Reports</div><br>");
                                        $(".printable_portion").append(tableStr);

                                        cloneCharts(printItems, 'portrait');
                                    }
                                });
                            }
                            else {
                                filterVisibleData = responseArr[3];
                                tableStr = tableStr + '<table id="printReportsTable" class="reports_grid" border="0" width="98%"><tr bgcolor="#BDBDBD" style="font-weight:bold;border-bottom:1px solid #BDBDBD; font-size:14px; padding-bottom:2px">';
                                for (var i = 0; i < headArr.length; i++) {
                                    tableStr = tableStr + '<th bgcolor="#BDBDBD" class = "fontSize" height="40" style="text-align:left;border-bottom:1px solid #BDBDBD; padding-bottom:10px; padding:2px; font-size:14px">' + headArr[i].replace(/[_]/g, " ") + '</th>';
                                }
                                tableStr = tableStr + '</tr>';
                                for (var i = 0; i < bodyArr.length; i++) {
                                    tableStr += '<tr style="text-align:left;">';
                                    for (var j = 0; j < headArr.length; j++) {
                                      
                                        //when the given value is string in that time we need to replace new line character with break tag
                                        if ($.type(bodyArr[i][headArr[j]]) =="string")
                                        {
                                            var arr = bodyArr[i][headArr[j]].replace(/\n/g, '<br/>');
                                        }
                                        else
                                        {
                                            var arr = bodyArr[i][headArr[j]];
                                        }
                                      
                                        if (arr != undefined && arr != null) {
                                            tableStr = tableStr + '<td height="40" class = "fontSize" style="border-bottom:1px solid #BDBDBD; padding:2px; text-align:left;">' + arr + '</td>';
                                        }
                                        else {
                                            tableStr = tableStr + '<td height="40" class = "fontSize" style="border-bottom:1px solid #BDBDBD; text-align:left;">-</td>';
                                        }
                                    }
                                }
                                tableStr = tableStr + '</tr></table><br><br>';
                                $(".printable_portion").append(tableStr);
                                var $table = $(".print-preview").css({ position: "absolute", visibility: "hidden", display: "block" });
                                var tableWidth = $table.find('.reports_grid').width();
                                cloneCharts(printItems, 'portrait');
                            }

                        }
                        function cloneCharts(printItems, printview) {
                            setTimeout(function () {
                                $('.sidebar, .topbar, .header, .hrsubmenu_img, .footer, #barChartPanel, #pieChartPanel, #gridPanelReports, #filterPanelReports, #modalExportPrint').addClass('hidden');
                                $('#modalExportPrint').modal('hide');
                                $('#printPanel').removeClass('hidden').addClass('panel');
                                $('.main-content').addClass('DTTT_Print');
                                $('#printHeader').removeClass('hidden');

                                var tablewidth = $('#printReportsTable').width();
                                var repTablePrint = $('.reports_grid').width();
                                var panelwidth = $('#printPanel').width();
                                if (repTablePrint > panelwidth) {
                                    adjustPrintView(panelwidth);
                                }

                                var chartWidth = panelwidth - ((panelwidth / 100) * 10);

                                if ($.inArray('Bar', printItems) != -1) {

                                    bsvg = $('#barChartReports').highcharts().getSVG({ exporting: { sourceWidth: chartWidth } });
                                    $('.printable_portion')
                                        .append(bsvg)
                                        .append('<br>');
                                }

                                if ($.inArray('Pie', printItems) != -1) {
                                    psvg = $('#pieChartReports').highcharts().getSVG({ exporting: { sourceWidth: chartWidth, } });
                                    $('.printable_portion')
                                        .append(psvg)
                                        .append('<br>');
                                }

                                $('#PrintScreen').on('click', function() {
                                    $('#PrintScreen,#exitPrint').addClass('hidden');

                                    $(".printable_portion svg").remove();

                                    if ($.inArray('Bar', printItems) != -1) {
                                        bsvg = $('#barChartReports').highcharts().getSVG();
                                        $('.printable_portion')
                                            .append(bsvg)
                                            .append('<br>');
                                    }

                                    if ($.inArray('Pie', printItems) != -1) {
                                        psvg = $('#pieChartReports').highcharts().getSVG();
                                        $('.printable_portion')
                                            .append(psvg)
                                            .append('<br>');
                                    }

                                    var selectColumnsFor = ['Hourly Wage Payslip', 'Monthly Master Report', 'Hourly Master Report'];
                                    if ($.inArray(linkValue, selectColumnsFor) != -1) {
                                        if (printview == 'landscape') {
                                            $('<style>@media print{@page {size: landscape;}}</style>').appendTo('.printable_portion');
                                        }
                                        if (printview == 'portrait') {
                                            $('<style>@media print{@page {size: portrait;}}</style>').appendTo('.printable_portion');
                                        }
                                    }

                                    if (repTablePrint > panelwidth) {
                                        var panelfont = adjustPrint(panelwidth, printview);
                                    }
                                    setTimeout(function () {
                                        window.print();
                                        $("#PrintScreen").unbind("click");
                                        $("#exportAsPrint").unbind("click");
                                        $("#submitPrintColumnsList").unbind("click");
                                        $('.sidebar, .topbar, .header, .hrsubmenu_img, .footer, #barChartPanel, #pieChartPanel, #gridPanelReports, #filterPanelReports, #modalExportPrint').removeClass('hidden');
                                        $('#printPanel').removeClass('panel').addClass('hidden');
                                        $('#printHeader').addClass('hidden');
                                        $('.main-content').removeClass('DTTT_Print');
                                    }, 100);
                                });

                                $('#exitPrint').on('click', function() {
                                    $("#PrintScreen").unbind("click");
                                    $("#exportAsPrint").unbind("click");
                                    $('.sidebar, .topbar, .header, .hrsubmenu_img, .footer, #barChartPanel, #pieChartPanel, #gridPanelReports, #filterPanelReports, #modalExportPrint').removeClass('hidden');
                                    $('#printPanel').removeClass('panel').addClass('hidden');
                                    $('#printHeader').addClass('hidden');
                                    $('.main-content').removeClass('DTTT_Print');
                                    /* to reload whole page */
                                    location.reload();
                                });
                            }, 500);
                            $('#selectPrintColumnsList').select2('val', '');

                        }
                    });
                    $('#generateExcel').on('click', function(){
                        exportExcel();
                    })
                    /************** Export Data table values as Csv file***************/
                    $('#exportExcel').on('click', function() {
                        fnFilterClose('Reports');
                        if(linkValue == 'Reimbursement')
                        {
                            $('#modalExcelWarning').modal('toggle');
                        }
                        else 
                        {
                            exportExcel();
                        }
                    });

                    function exportExcel() {
                        /***** Get Filter Values *****/
                        setMask('#wholepage');
                        $.ajax({
                            type: 'POST',
                            dataType: 'json',
                            async: true,
                            url: pageUrl() + 'reports/hr-reports/create-barchart/tableData/tableData',
                            data: { _modName: modName, linkValue: linkValue },
                            success: function (tableData) {
                                var filterArray = [];
                                var res = [];
                                var filterHeading = [];
                                var filterTable = tableData.Filter_Table;
                                var RepFilter = tableData.Rep_Filter;
                                res = filterTable.split(",");
                                filterHeading = RepFilter.split("-");
                                removeMask();

                                for (var i in res) {
                                    if (filterHeading[i] == 'Employee Name' || filterHeading[i] == 'Manager Name') {
                                        res[i] = 'Employee Name';
                                    }
                                    switch (res[i]) {
                                        case 'DPicker':
                                            var dateBegin = '#filterDateBeginid' + i;
                                            var dateEnd = '#filterDateEndid' + i;
                                            var finalBeginDate = $(dateBegin).val();
                                            if (finalBeginDate !== '') {
                                                finalBeginDate = fnFormatDate(finalBeginDate);
                                            }
                                            var finalEndDate = $(dateEnd).val();
                                            if (finalEndDate !== '') {
                                                finalDate = fnFormatDate(finalEndDate);
                                                finalEndDate = finalBeginDate + '&' + finalDate;
                                            }
                                            filterArray.push(finalEndDate);
                                            break;
                                        case 'MPicker':
                                            filterValue = fnServerMonthFormatter($('#filterid' + i).val());
                                            filterArray.push(filterValue);
                                            break;
                                        case 'Employee Name':
                                            filterValue = $('#filterid' + i).val();
                                            filterArray.push(filterValue);
                                            break;
                                        case 'Amount':
                                            var filterAmountStart = $('#filterAmountStartid' + i).val();
                                            var filterAmountEnd = $('#filterAmountEndid' + i).val();
                                            var finalAmount = '',
                                                finalBegin = '',
                                                finalEnd = '';

                                            if (filterAmountStart !== '') {
                                                finalBegin = filterAmountStart;
                                            }

                                            if (filterAmountEnd !== '') {
                                                finalEnd = filterAmountEnd;
                                            }
                                            finalAmount = finalBegin + '&' + finalEnd;

                                            filterArray.push(finalAmount);
                                            break;
                                        default:
                                            filterValue = $('#filterid' + i).select2('val');
                                            filterArray.push(filterValue);
                                            break;

                                    }
                                }

                                var loanId = '';
                                if (linkValue == 'Loan Amortization') {
                                    var selectedRow = fnGetSelected(oTable);
                                    var record = oTable.fnGetData(selectedRow[0]);

                                    loanId = record['Loan_Id'];
                                }

                                    if (linkValue == 'Employee Wise Expenses') {
                                        var filterArray = [];
                                        var filterValue4 = $('#filterid0').val();
                                        var filterValue5 = fnFormatDate($('#filterDateBeginid1').val());
                                        var filterValue6 = fnFormatDate($('#filterDateEndid1').val());
                                        filterArray.push(filterValue4, filterValue5, filterValue6);
                                        
                                        document.location.href = pageUrl () + 'reports/hr-reports/export-csv/disGrid/s/loanId/'+loanId+'/linkValue/'+linkValue+'/_modName/'+modName+'/filterArray/'+filterArray;
                                    }
                                    else if (linkValue == 'Pt Annual Return(Form 5a)') {
                                        var filterArray = [];
                                        var filterValue1 = $('#filterid0').select2('val');
                                        var filterValue2 = $('#filterid1').select2('val');
                                        filterArray.push(filterValue1, filterValue2);

                                        document.location.href = pageUrl () + 'reports/hr-reports/export-csv/disGrid/s/loanId/'+loanId+'/linkValue/'+linkValue+'/_modName/'+modName+'/filterArray/'+filterArray;
                                    }
                                    else if (linkValue == 'Monthly Salary Payslip' || linkValue == 'Insurance Statement' || linkValue == 'Ssnit Tier 1' 
                                    || linkValue === 'Ssnit Tier 2'|| linkValue === 'Provident Fund Detailed Report' || linkValue === 'Payment Register'
                                    || linkValue == 'Attendance And Absence Overview' || linkValue == 'Timesheet Comprehensive' || linkValue == 'Fixed Health Insurance' || linkValue == 'Bank Salary Statement'
                                    || linkValue == 'Esi Monthly'  || linkValue == 'Lwf' || linkValue == 'Professional Tax Monthly') {
                                        $.ajax({
                                            type: "POST",
                                            url: pageUrl() + 'reports/hr-reports/preview/filterArray/' + filterArray,
                                            data: {
                                                'linkValue': linkValue,
                                                '_modName': modName,
                                            },
                                            success: function (response) {
                                                responseArr = JSON.parse(response);
                                                $('#selectExcelColumnsList').find('option').remove();
                                                $('#selectExcelColumnsList').select2('val', '');
                                                var columnlist = '';
                                                columnlist += '<option value="selectAll">--Select all--</option>';
                                                columnlist += '<option value="clearAll">--Clear all--</option>';
                                                result = responseArr[0];

                                                
                                                for (var x in result)
                                                {
                                                    columnlist += '<option value='+x+'>'+result[x].replace(/_/g, " ")+'</option>'
                                                }
                                                
                                                $('#selectExcelColumnsList').append(columnlist);
                                            
                                                if(linkValue === 'Payment Register' || linkValue === 'Bank Salary Statement' || linkValue == 'Attendance And Absence Overview'
                                                || linkValue == 'Esi Monthly' || linkValue == 'Lwf' || linkValue == 'Professional Tax Monthly')
                                                {
                                                    $('#filterGroupBy').select2('val', '');
                                                    if(linkValue === 'Payment Register'){
                                                        $('#groupByLabel').html('Group By<span class="short_explanation">*</span>');
                                                    }
                                                    $('.groupBy').show();
                                                }
                                                else
                                                {
                                                    $('#groupByLabel').html('Group By');
                                                    $('.groupBy').hide();
                                                }

                                                if(linkValue == 'Attendance And Absence Overview')
                                                {
                                                    $('#selectExcelColumnsList').select2('val', '');
                                                    $('.selectExcelColumnsListPanel').hide();
                                                }
                                                else
                                                {
                                                    $('.selectExcelColumnsListPanel').show();
                                                }


                                                reportConfiguration = responseArr[4];
                                                if(reportConfiguration!='')
                                                {
                                                    selectElement = $('#selectExcelColumnsList').select2();
                                                    valuesToSelect = reportConfiguration['Report_Header_Position'];
                                                    selectElement.val(valuesToSelect).trigger('change');
                                                }

                                                

                                                $('#modalExcelColumnsList').modal('toggle');

                                                //generate csv based on selected columns
                                                $('#submitExcelColumnsList').unbind().on('click', function() {
                                                    var checkedColumns = $('#selectExcelColumnsList').select2('val');
                                                    /**it is done to bind the Employee_Id along with singles fields selected **/
                                                    if(checkedColumns.length>0)
                                                    {
                                                        //Group by field mandatory report
                                                        let filterGroupByReports = ['Payment Register'];
                                                        //Group by field optional report list
                                                        let groupByUserSelectionReports = ['Bank Salary Statement','Lwf', 'Professional Tax Monthly'];
                                                        let backendHandledReports = ['Esi Monthly'];
                                                        if(filterGroupByReports.includes(linkValue) || groupByUserSelectionReports.includes(linkValue)
                                                        || backendHandledReports.includes(linkValue))
                                                        {
                                                            filterGroupBy = $('#filterGroupBy').select2('val');

                                                            //If group by manadatory then validate group by value exist
                                                            if(filterGroupByReports.includes(linkValue)){
                                                                // Validate group by field for required reports
                                                                if(!filterGroupBy) {
                                                                    jAlert({ msg: 'Group by is mandatory for this report', type: 'info' });
                                                                    removeMask();
                                                                    return;
                                                                }
                                                            }
                                                        }
                                                        else
                                                        {
                                                            filterGroupBy = '';
                                                        }

                                                        $('#modalExcelColumnsList').modal('hide');

                                                        // Group By is optional in some reports. If Group By values are present, data will be processed and prepared in the backend; otherwise, export the data from here
                                                        if(linkValue == 'Monthly Salary Payslip' || linkValue == 'Timesheet Comprehensive' ||  linkValue == 'Fixed Health Insurance'
                                                        || (!filterGroupBy && groupByUserSelectionReports.includes(linkValue))){
                                                            exportExcelFrontEnd(loanId,linkValue,modName,filterArray,checkedColumns,filterGroupBy)    
                                                        }
                                                        else
                                                        {
                                                            document.location.href = pageUrl () + 'reports/hr-reports/export-csv/disGrid/s/loanId/'+loanId+'/linkValue/'+linkValue+'/_modName/'+modName+'/filterArray/'+filterArray+'/checkedColumns/'+checkedColumns+'/filterGroupBy/'+filterGroupBy;
                                                        }
                                                    }
                                                    else
                                                    {
                                                        if(linkValue == 'Attendance And Absence Overview')
                                                        {
                                                            var dateArr = filterArray[1].split('&');
                                                            if (dateArr[0]==dateArr[1])  
                                                            {
                                                                filterGroupBy = $('#filterGroupBy').select2('val');
                                                                if(filterGroupBy!='')
                                                                {
                                                                    exportExcelFrontEnd(loanId,linkValue,modName,filterArray,checkedColumns,filterGroupBy)  
                                                                }
                                                                else
                                                                {
                                                                    jAlert({ msg: 'group by field is mandatory to download the report', type: 'info' });
                                                                    removeMask();
                                                                }
                                                                $('#modalExcelColumnsList').modal('hide');
                                                            }
                                                            else 
                                                            {
                                                                jAlert({ msg: 'Attendance Start date and End date should be same', type: 'info' });
                                                                removeMask();
                                                                $('#modalExcelColumnsList').modal('hide');
                                                            }
                                                        }
                                                    }
                                                });
                                            }
                                        });
                                    }
                                    else if(linkValue == 'Attendance Muster Info' || linkValue == 'Absentees' ||  linkValue == 'Attendance Register')
                                    {
                                        var dateArr = filterArray[1].split('&');
                                        var days = fnDifferBwDates(dateArr[0], dateArr[1]);
                                        if (days <= 31 && days >= 0) 
                                        {
                                            exportExcelFrontEnd(loanId,linkValue,modName,filterArray)
                                        }
                                        else 
                                        {
                                            jAlert({ msg: 'Attendance Start date and End date Should not be greater than 31 days', type: 'info' });
                                            removeMask();
                                        }
                                    }
                                    else
                                    {
                                        exportExcelFrontEnd(loanId,linkValue,modName,filterArray);
                                    }
                            },
                            error: function (error) {
                                if (error.status == 200) {
                                    sessionExpired();
                                }
                                else {
                                    /* To handle internal server error */
                                    jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                                }
                                removeMask();
                            }
                        });
                    }
                    $('#saveReportStructure').on('click', function() {
                        setMask('#wholepage');
                        $.ajax({
                            type: 'POST',
                            dataType: 'json',
                            async: true,
                            url: pageUrl() + 'reports/hr-reports/create-barchart/tableData/tableData',
                            data: { _modName: modName, linkValue: linkValue },
                            success: function (tableData) {
                                var filterArray = [];
                                var res = [];
                                var filterHeading = [];
                                var filterTable = tableData.Filter_Table;
                                var RepFilter = tableData.Rep_Filter;
                                res = filterTable.split(",");
                                filterHeading = RepFilter.split("-");
                                removeMask();

                                for (var i in res) {
                                    if (filterHeading[i] == 'Employee Name' || filterHeading[i] == 'Manager Name') {
                                        res[i] = 'Employee Name';
                                    }
                                    switch (res[i]) {
                                        case 'DPicker':
                                            var dateBegin = '#filterDateBeginid' + i;
                                            var dateEnd = '#filterDateEndid' + i;
                                            var finalBeginDate = $(dateBegin).val();
                                            if (finalBeginDate !== '') {
                                                finalBeginDate = fnFormatDate(finalBeginDate);
                                            }
                                            var finalEndDate = $(dateEnd).val();
                                            if (finalEndDate !== '') {
                                                finalDate = fnFormatDate(finalEndDate);
                                                finalEndDate = finalBeginDate + '&' + finalDate;
                                            }
                                            filterArray.push(finalEndDate);
                                            break;
                                        case 'MPicker':
                                            filterValue = fnServerMonthFormatter($('#filterid' + i).val());
                                            filterArray.push(filterValue);
                                            break;
                                        case 'Employee Name':
                                            filterValue = $('#filterid' + i).val();
                                            filterArray.push(filterValue);
                                            break;
                                        case 'Amount':
                                            var filterAmountStart = $('#filterAmountStartid' + i).val();
                                            var filterAmountEnd = $('#filterAmountEndid' + i).val();
                                            var finalAmount = '',
                                                finalBegin = '',
                                                finalEnd = '';

                                            if (filterAmountStart !== '') {
                                                finalBegin = filterAmountStart;
                                            }

                                            if (filterAmountEnd !== '') {
                                                finalEnd = filterAmountEnd;
                                            }
                                            finalAmount = finalBegin + '&' + finalEnd;

                                            filterArray.push(finalAmount);
                                            break;
                                        default:
                                            filterValue = $('#filterid' + i).select2('val');
                                            filterArray.push(filterValue);
                                            break;

                                    }
                                }
                                $.ajax({
                                    type: "POST",
                                    url: pageUrl() + 'reports/hr-reports/preview/filterArray/' + filterArray,
                                    data: {
                                        'linkValue': linkValue,
                                        '_modName': modName,
                                    },
                                    success: function (response) {
                                        responseArr = JSON.parse(response);
                                        $('#selectReportStructureColumnsList').find('option').remove();
                                        $('#selectReportStructureColumnsList').select2('val', '');
                                        var columnlist = '';
                                        columnlist += '<option value="selectAll">--Select all--</option>';
                                        columnlist += '<option value="clearAll">--Clear all--</option>';
                                        result = responseArr[0];
                                        
                                        for (var x in result)
                                        {
                                            columnlist += '<option value='+result[x]+'>'+result[x].replace(/_/g, " ")+'</option>'
                                        }
                                        
                                        $('#selectReportStructureColumnsList').append(columnlist);
                                    
                                        $('#modalReportStructureColumnsList').modal('toggle');

                                        //generate csv based on selected columns
                                        $('#submitReportStructureColumnsList').unbind().on('click', function() {
                                            var checkedColumns = $('#selectReportStructureColumnsList').select2('val');
                                            /**it is done to bind the Employee_Id along with singles fields selected **/
                                            if(checkedColumns.length>0)
                                            {
                                                saveReportStructure(linkValue,checkedColumns);
                                                $('#modalReportStructureColumnsList').modal('hide');
                                            }
                                            else
                                            {
                                                jAlert({ msg: 'Please select the columns to save the report structure', type: 'info' });
                                                removeMask();
                                            }
                                        });
                                    }
                                });
                            },
                            error: function (error) 
                            {
                                if (error.status == 200) {
                                    sessionExpired();
                                }
                                else 
                                {
                                    /* To handle internal server error */
                                    jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                                }
                                removeMask();
                            }
                        });
                    });

                    function saveReportStructure(linkValue,reportHeaders)
                    {
                        setMask('#wholepage');
                        $.ajax({
                            type: 'POST',
                            dataType: 'json',
                            async: false,
                            url: pageUrl() + 'reports/hr-reports/update-report-structure',
                            data: {linkValue: linkValue,'reportHeaders':reportHeaders},
                            success: function (result) {
                                removeMask();
                                jAlert ({msg:result.msg, type:result.type});
                            },
                            error : function(errorResult){
                                removeMask();
                                if(errorResult.status == 200)
                                {
                                    sessionExpired();
                                }else
                                {
                                    /* To handle internal server error */
                                    jAlert ({ msg : 'There seems to be some technical difficulties while saving report structure. Please try after sometime.', type : 'warning' });
                                }
                            }
                        });
                    }

                    $('#exportPdf').on('click', function() {
                        $.ajax({
                            type: 'POST',
                            dataType: 'json',
                            async: false,
                            url: pageUrl() + 'reports/hr-reports/create-barchart/tableData/tableData',
                            data: { _modName: modName, linkValue: linkValue },
                            success: function (tableData) {

                                var barX = tableData.Bar_X
                                var barY = tableData.Stack_Categ_Point;
                                var pieField = tableData.Pie_Field;

                                /***** if charts are not require hide chart panels *****/
                                if ($.inArray(barX, [null, '', 0]) !== -1 || $.inArray(barY, [null, '', 0]) !== -1) {
                                    $("#barChartPanel,#pieChartPanel").hide();

                                    //$('#exportPdf').on('click', function()
                                    //{                        
                                    fnFilterClose('Reports');
                                    var filterArray = chartFilters(tableData);
                                    //
                                    var selectColumnsFor = ['Hourly Wage Payslip', 'Monthly Master Report', 'Hourly Master Report'];
                                    var checkedValue = [];

                                    if ($.inArray(linkValue, selectColumnsFor) != -1) {
                                        $('#modalExportColumnsList').modal('toggle');

                                        $('#submitExportColumnsList').on('click', function () {
                                            checkedValue = $('#selectExportColumnsList').select2('val');

                                            if (checkedValue != '') {
                                                $('#modalExportColumnsList').modal('hide');
                                                document.location.href = pageUrl () + 'reports/hr-reports/export-pdf/disGrid/s/linkValue/'+linkValue+'/_modName/'+modName+'/filterArray/'+filterArray+'/_ckedValue/'+checkedValue;
                                            }
                                        });
                                        $('#selectExportColumnsList').select2('val', '');
                                    }
                                    else {
                                        var checkedValue = [];
                                        if (linkValue == 'Loan Amortization') {
                                            var selectedRow = fnGetSelected(oTable);
                                            var record = oTable.fnGetData(selectedRow[0]);
                                            document.location.href = pageUrl () + 'reports/hr-reports/export-pdf/disGrid/s/loanId/'+record['Loan_Id']+'/linkValue/'+linkValue+'/_modName/'+modName+'/filterArray/'+filterArray+'/_ckedValue/'+checkedValue;
                                        }
                                        else {
                                            if ((linkValue == 'Esic Hourly' || linkValue == 'Esic Monthly') && (filterArray[2] < 0 || filterArray[2] == '')) {
                                                jAlert({ msg: 'Please select the Insurance Type And Salary Month before exporting it as pdf ', type: 'info' });

                                            }
                                            else {
                                                if (linkValue == 'Employee Wise Expenses') {
                                                    var filterArray = [];
                                                    var filterValue4 = $('#filterid0').val();
                                                    var filterValue5 = fnFormatDate($('#filterDateBeginid1').val());
                                                    var filterValue6 = fnFormatDate($('#filterDateEndid1').val());
                                                    filterArray.push(filterValue4, filterValue5, filterValue6);
                                                }
                                                exportPdf(filterArray, checkedValue);
                                            }
                                        }
                                    }
                                    //});                    
                                }
                            }
                        });
                    });
                }
            });
        
        /*********************** Filter Open/Close ************************/
        $('#filterHrReports').on('click', function () {
            if ($('#filterPanelReports').hasClass('open')) {
                $('#filterPanelReports').removeClass('open');
                $('#filterPanelReports').hide();
            }
            else {
                $('#filterPanelReports').addClass('open');
                $('#filterPanelReports').show();
            }
        });

        $('#filterid0').on('change', function () {
            if(linkValue === 'Eft Monthly' || linkValue === 'Eft Monthly'){
                //If bank payment type id is changed get the debit account numbers
                $.ajax ({
                    type     : 'POST',
                    dataType : 'json',
                    async    : false,
                    url      : pageUrl () +'reports/hr-reports/get-eft-filter-details/bankPaymentTypeId/'+ $('#filterid0').val(),
                    success  : function (result)
                    {
                        if (isJson (result))
                        {
                            let debitAccountNumberField = $("#filterid1");
                            debitAccountNumberField.find('option').remove();

                            let transactionTypeField = $("#filterid2");
                            transactionTypeField.find('option').remove();

                            $("#filterid1").select2('val','');
                            $("#filterid2").select2('val','');

                            let debitAccountNumbersList = result.debitAccountNumbersList;
                            for (let accountkey in debitAccountNumbersList)
                            {
                                debitAccountNumberField.append("<option value='" + accountkey + "'>" + accountkey+ "</option>");
                            }
                            let transactionTypeList = result.transactionTypeList;
                            for (let txnkey in transactionTypeList)
                            {
                                transactionTypeField.append("<option value='" + txnkey + "'>" + transactionTypeList[txnkey]+ "</option>");
                            }
                        }
                        else
                        {
                            jAlert ({ msg : 'There seems to be some technical difficulties while getting debit account numbers. Please try after sometime.', type : 'warning' });
                        }
                    },
                    error : function(errorResult){
                        if(errorResult.status == 200){
                            sessionExpired();
                        }else{
                            /* To handle internal server error */
                            jAlert ({ msg : 'There seems to be some technical difficulties while getting debit account numbers. Please try after sometime.', type : 'warning' });
                        }
                    }
                });

            }

            
        });

        $('#exportAsText').on('click', function() {
            fnFilterClose('Reports');

            var exportText = this;
            /***** Get Filter Values *****/
            $.ajax(
                {
                    type: 'POST',
                    dataType: 'json',
                    async: false,
                    url: pageUrl() + 'reports/hr-reports/create-barchart/tableData/tableData',
                    data: { _modName: modName, linkValue: linkValue },
                    success: function (tableData) {
                        var filterArray = [];

                        var filterTable = tableData.Filter_Table;
                        var RepFilter = tableData.Rep_Filter;
                        var res = filterTable.split(",");
                        var filterHeading = RepFilter.split("-");
                        for (var i in res) {
                            if (filterHeading[i] == 'Employee Name' || filterHeading[i] == 'Manager Name') {
                                res[i] = 'Employee Name';
                            }
                            switch (res[i]) {
                                case 'DPicker':
                                    var dateBegin = '#filterDateBeginid' + i;
                                    var dateEnd = '#filterDateEndid' + i;
                                    var finalBeginDate = $(dateBegin).val();
                                    if (finalBeginDate != '') {
                                        finalBeginDate = fnFormatDate(finalBeginDate);
                                    }
                                    var finalEndDate = $(dateEnd).val();
                                    if (finalEndDate != '') {
                                        finalDate = fnFormatDate(finalEndDate);
                                        var finalEndDate = finalBeginDate + '&' + finalDate;
                                    }
                                    filterArray.push(finalEndDate);
                                    break;
                                case 'MPicker':
                                    filterValue = fnServerMonthFormatter($('#filterid' + i).val());
                                    filterArray.push(filterValue);
                                    break;
                                case 'Employee Name':
                                    filterValue = $('#filterid' + i).val();
                                    filterArray.push(filterValue);
                                    break;
                                default:
                                    filterValue = $('#filterid' + i).select2('val');
                                    filterArray.push(filterValue);
                                    break;
                            }
                        }

                        if (linkValue === 'Uan Based Ecr' || linkValue === 'Uan Based Ecr Hourly' || linkValue.toLowerCase() === 'uan based ecr(arrear)')
                        {
                            $.ajax({
                                type: 'POST',
                                dataType: 'json',
                                async: false,
                                url: pageUrl() + 'reports/hr-reports/download-text/linkValue/' + linkValue + '/_modName/' + modName + '/filterArray/' + filterArray,
                                success: function (result) {
                                    if(result)
                                    {
                                        downloadText = result.ecrTextDetails;
                                        if (downloadText != '') {
                                            var downloadTextDetails = '';
                                            for (var i = 0; i < downloadText.length; i++) {
                                                downloadTextDetails += downloadText[i] + "\n";
                                            }
                                            exportText.href = "data:text/plain;charset=UTF-8," + encodeURIComponent(downloadTextDetails);
                                        }
                                        else
                                        {
                                            jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                                        }
                                    }
                                    else
                                    {
                                        jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                                    }    
                                },
                                error: function (downloadTextErrorCode) {
                                    if (downloadTextErrorCode.status == 200) {
                                        sessionExpired();
                                    } else {
                                        /* To handle internal server error */
                                        jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                                    }
                                }
                            });
                        }
                        else
                        {
                            document.location.href = pageUrl () + 'reports/hr-reports/download-text/linkValue/'+linkValue+'/_modName/'+modName+'/filterArray/'+filterArray;
                        }  
                            
                        
                    }
                });
        });



        function createCharts(filterArray) {
            /******Create Charts *****/
            $.ajax({
                type: 'POST',
                dataType: 'json',
                async: false,
                url: pageUrl() + 'reports/hr-reports/create-barchart/tableData/tableData',
                data: { _modName: modName, linkValue: linkValue },
                success: function (tableData) {
                    var barX = tableData.Bar_X;
                    var barY = tableData.Stack_Categ_Point;
                    var pieField = tableData.Pie_Field;

                    $("#barChartPanel,#pieChartPanel").hide();
                    /** If Charts are require then Create chart */
                    if (!($.inArray(barX, [null, '', 0]) !== -1 || $.inArray(barY, [null, '', 0]) !== -1)) {
                        $("#barChartPanel,#pieChartPanel").show();
                        $.ajax({
                            type: 'POST',
                            dataType: 'json',
                            async: false,
                            url: pageUrl() + 'reports/hr-reports/create-barchart/filterArray/' + filterArray,
                            data: { _modName: modName, linkValue: linkValue },
                            success: function (result) {
                                if (result != '' && result != null && ((tableData['Flag_Stack'] == 1 && result[2].length > 1) || (tableData['Flag_Stack'] == 0 && result.length > 1))) {
                                    var resultLength = result.length;
                                    var barDataArray = [];
                                    var pieDataArray = [];
                                    var stackDataArray = [];
                                    /****** To Align Bar Width and Lengend  Rotate Angel******/
                                    if (resultLength < 5) {
                                        var pointPadding = 0.4;
                                        var rotation = 0;
                                    }
                                    else if (resultLength <= 10) {
                                        var pointPadding = 0.35;
                                        var rotation = 0;
                                    }
                                    else if (resultLength <= 15) {
                                        var pointPadding = 0.25;
                                        var rotation = 0;
                                    }
                                    else if (resultLength <= 20) {
                                        var pointPadding = 0.2;
                                        var rotation = 0;
                                    }
                                    else if (resultLength <= 30) {
                                        var pointPadding = 0.18;
                                        var rotation = 0;
                                    }
                                    else if (resultLength <= 40) {
                                        var pointPadding = 0.15;
                                        var rotation = 0;
                                    }
                                    else if (resultLength <= 50) {
                                        var pointPadding = 0.15;
                                        var rotation = 0;
                                    }
                                    else {
                                        var pointPadding = 0.05;
                                        var rotation = 0;
                                    }

                                    if (tableData['Flag_Stack'] != 1) {
                                        for (var i = 0; i < resultLength; i++) {
                                            barDataArray.push({ data: [parseInt(result[i][barY])], name: result[i][barX] });
                                            pieDataArray.push({ y: parseInt(result[i][barY]), name: result[i][pieField] });
                                        }

                                        /******** Create Bar Chart ********/
                                        $('#barChartReports').highcharts({
                                            chart: {
                                                type: 'column'
                                            },
                                            credits: {
                                                enabled: false // Remove highchart.com link credits
                                            },
                                            exporting: {
                                                enabled: false
                                            },
                                            title: {
                                                text: linkValue + ' Report - Bar Chart'
                                            },
                                            legend: {
                                                //itemDistance : 0,
                                                itemWidth: 200,
                                                //itemMarginBottom: 5
                                            },
                                            xAxis: {
                                                categories: [barX],
                                                crosshair: false // center line on vertical 
                                            },
                                            yAxis: {
                                                min: 0,
                                                title: {
                                                    text: barY.replace(/_/g, ' ')
                                                }
                                            },
                                            tooltip: {
                                                headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
                                                pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
                                                    '<td style="padding:0"><b>{point.y:.1f}</b></td></tr>',
                                                footerFormat: '</table>',
                                                shared: false,            // if true it will show category wise
                                                useHTML: true              // if false tool tip text will be show in black color
                                            },
                                            plotOptions: {
                                                column: {
                                                    pointPadding: pointPadding, //padding b/w series
                                                    borderWidth: 1,
                                                    borderColor: '#D8D8D8',
                                                    groupPadding: 0,
                                                    cropThreshold: 0,
                                                    colorByPoint: false         // if true columns of single category will be in same color
                                                }
                                            },
                                            series: barDataArray
                                        });
                                    }
                                    else {
                                        var catogries = result[1];
                                        var locationArr = result[2];
                                        var series = result[3];
                                        for (var i = 0; i < resultLength; i++) {
                                            var genArray = [];
                                            var genArray = catogries;

                                            var locationArr = [];
                                            var locationArr = result[2];
                                            //var locationArr = locationArr; ;

                                            var series = [];
                                            var series = result[3];
                                            //var series = series;

                                            finalarr = [];
                                            seriesData = [];//for chart we should bind values like series:[{name:male,data:[2,1]},{name:female,data:[3,2]}],with below for loop making this.

                                            var genArrayLength = genArray.length;
                                            var locationArrLength = locationArr.length;

                                            for (var k = 0; k < genArrayLength; k++) {
                                                for (var j = 0; j < locationArrLength; j++) {
                                                    if ((parseInt(series[genArray[k]][j])) == 0) {
                                                        seriesData.push(null);
                                                    }
                                                    else {
                                                        seriesData.push(parseInt(series[genArray[k]][j]));
                                                    }
                                                }
                                                if ((seriesData[0] != null && seriesData[1] == null) || (seriesData[0] == null && seriesData[1] != null)
                                                    || (seriesData[0] != null && seriesData[1] != null)) {
                                                    finalarr.push({ name: genArray[k], data: seriesData, pointWidth: 30 });
                                                }
                                                seriesData = [];
                                            }

                                            //barDataArray.push({ data: [parseInt(result[i][barY])] , name:result[i][barX]});

                                            //pieDataArray.push({ y: parseInt(finalarr[i]['data']), name: finalarr[i]['name'] });
                                        }
                                        /******** Create Bar Chart ********/

                                        var seriLen = locationArr.length;
                                        if (seriLen <= 8) {
                                            dynWidth = 400;
                                            $('div#bar').css({ 'padding-left': '180px' });
                                        }
                                        else {
                                            dynWidth = parseInt(seriLen * 50);
                                            $('div#bar').css({ 'padding-left': '30px' });
                                        }

                                        $('#barChartReports').highcharts({
                                            chart: {
                                                renderTo: 'bar',
                                                //height: parseInt(338),
                                                //width: dynWidth,
                                                type: 'column'
                                            },
                                            credits: {
                                                enabled: false // Remove highchart.com link credits
                                            },
                                            exporting: {
                                                enabled: false
                                            },
                                            title: {
                                                text: linkValue + ' Report - Bar Chart'
                                            },
                                            xAxis:
                                            {
                                                categories: locationArr,
                                                labels:
                                                {
                                                    rotation: -90,
                                                    align: 'right'
                                                }
                                            },
                                            series: finalarr,
                                            yAxis:
                                            {
                                                min: 0,
                                                allowDecimals: false,
                                                lineWidth: 1,
                                                title:
                                                {
                                                    text: barY.replace(/_/g, ' ')
                                                },
                                                stackLabels:
                                                {
                                                    enabled: true,
                                                    style:
                                                    {
                                                        fontWeight: 'bold',
                                                        color: (Highcharts.theme && Highcharts.theme.textColor) || 'gray'
                                                    }
                                                }
                                            },
                                            //exporting: 
                                            //{
                                            //    url:'',
                                            //    buttons: 
                                            //    {
                                            //        exportButton: 
                                            //        {
                                            //            enabled:false
                                            //        },
                                            //        printButton: 
                                            //        {
                                            //            enabled:false,
                                            //            symbol: 'circle'
                                            //        }
                                            //    }
                                            //},
                                            legend:
                                            {
                                                align: 'center',
                                                verticalAlign: 'top',
                                                y: 30
                                            },
                                            tooltip: {
                                                //headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
                                                //pointFormat : '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
                                                //              '<td style="padding:0"><b>{point.y:.1f}</b></td></tr>',
                                                //footerFormat: '</table>',
                                                //shared      : false,            // if true it will show category wise
                                                //useHTML     : true              // if false tool tip text will be show in black color
                                                formatter: function () {
                                                    return '<b>' + this.x + '</b><br/>' +
                                                        this.series.name + ': ' + this.y + '<br/>' +
                                                        'Total: ' + this.point.stackTotal;

                                                }
                                            },
                                            plotOptions: {
                                                column:
                                                {
                                                    animation: false,
                                                    stacking: 'normal',
                                                    dataLabels:
                                                    {
                                                        enabled: true,
                                                        color: (Highcharts.theme && Highcharts.theme.dataLabelsColor) || 'white'
                                                    }
                                                }
                                            }
                                        });
                                    }
                                    /******** Create Pie Chart********/
                                    $.ajax({
                                        type: 'POST',
                                        dataType: 'json',
                                        async: false,
                                        url: pageUrl() + 'reports/hr-reports/create-piechart/filterArray/' + filterArray,
                                        data: { _modName: modName, linkValue: linkValue },
                                        success: function (result) {
                                            if (result != '' && result != null && (result[0].length) >= 2) {
                                                var gen = [];
                                                var gen = result[1];
                                                var num = [];
                                                var num = result[2];

                                                var dataArray = [];
                                                var numLength = num.length;
                                                for (var i = 0; i < numLength; i++) {
                                                    dataArray.push([gen[i], num[i] * num.length / num.length]);
                                                }

                                                var pieChart = $('#pieChartReports').highcharts(
                                                    {
                                                        chart:
                                                        {
                                                            //renderTo: 'pie',
                                                            //height: parseInt(<?php echo $this->chartHt;?>),
                                                            //width: parseInt(<?php echo $this->chartWt;?>),
                                                            //type: 'pie',
                                                            plotBackgroundColor: null,
                                                            plotBorderWidth: null,
                                                            plotShadow: false,
                                                            type: 'pie'
                                                        },
                                                        title:
                                                        {
                                                            text: linkValue + ' Report - Pie Chart'
                                                        },
                                                        credits: {
                                                            enabled: false          // Remove highchart.com link credits
                                                        },
                                                        exporting: {
                                                            enabled: false
                                                        },
                                                        //exporting: {
                                                        //    url:'',
                                                        //    buttons: {
                                                        //        exportButton: {
                                                        //        enabled:false
                                                        //        },
                                                        //        printButton: {
                                                        //        enabled:false,
                                                        //        symbol: 'circle'
                                                        //        }
                                                        //
                                                        //    }
                                                        //},

                                                        plotOptions:
                                                        {
                                                            series: {
                                                                animation: false
                                                            }
                                                        },

                                                        series: [
                                                            {
                                                                showInLegend: true,
                                                                data: /*[
                                                        [gen[0], parseInt(num[0])],[gen[1], parseInt(num[1])],
                                                      ],*/
                                                                    //arr,
                                                                    dataArray,
                                                                dataLabels:
                                                                {
                                                                    enabled: true,
                                                                    color: '#000000',
                                                                    connectorColor: '#000000',
                                                                    formatter: function () {
                                                                        return this.point.name + ' : ' + '<b>' + this.y + '</b>';
                                                                    }
                                                                },
                                                            }],
                                                        tooltip:
                                                        {
                                                            formatter: function () {
                                                                return '<b>' + this.point.name + '</b>: ' + this.percentage.toFixed(2) + ' %';
                                                            }
                                                        }
                                                    });
                                            }
                                            else {
                                                $("#pieChartPanel").hide();
                                            }
                                        }
                                    });
                                }
                                /******** If No Records in table then hide Chart Panels********/
                                else {
                                    $("#barChartPanel,#pieChartPanel").hide();
                                }
                            }
                        });
                        $('#generatePdf').on('click', function () {
                            exportPdfFunction();
                        });

                        /***** If Grid Pie chart and Bar Charts are available then allow user to select items to be export*****/
                        $('#exportPdf').off('click').on('click', function () {
                            fnFilterClose('Reports');

                            if( linkValue == 'Reimbursement')
                            {
                                $('#modalPdfWarning').modal('toggle');
                            }
                            else
                            {
                                exportPdfFunction();
                            }
                        });
                        function exportPdfFunction()
                        {
                            setMask('#wholepage');
                                /***** Get filter Value******/
                                $.ajax(
                                    {
                                        type: 'POST',
                                        dataType: 'json',
                                        async: true,
                                        url: pageUrl() + 'reports/hr-reports/create-barchart/tableData/tableData',
                                        data: { _modName: modName, linkValue: linkValue, _ctHt: '338', _ctWt: '500', _ctRent: 'bar' },
                                        success: function (tableData) {
                                            var filterArray = [];
                                            var filterTable = tableData.Filter_Table;
                                            var RepFilter = tableData.Rep_Filter;
                                            var res = filterTable.split(",");
                                            var filterHeading = RepFilter.split("-");
                                            var filterArray = [];
                                            for (var i in res) {
    
                                                if (filterHeading[i] == 'Employee Name' || filterHeading[i] == 'Manager Name') {
                                                    res[i] = 'Employee Name';
                                                }
                                                switch (res[i]) {
                                                    case 'DPicker':
                                                        var dateBegin = '#filterDateBeginid' + i;
                                                        var dateEnd = '#filterDateEndid' + i;
                                                        var finalBeginDate = $(dateBegin).val();
                                                        if (finalBeginDate != '') {
                                                            finalBeginDate = fnFormatDate(finalBeginDate);
                                                        }
                                                        var finalEndDate = $(dateEnd).val();
                                                        if (finalEndDate != '') {
                                                            finalDate = fnFormatDate(finalEndDate);
                                                            var finalEndDate = finalBeginDate + '&' + finalDate;
                                                        }
                                                        filterArray.push(finalEndDate);
                                                        break;
                                                    case 'MPicker':
                                                        filterValue = fnServerMonthFormatter($('#filterid' + i).val());
                                                        filterArray.push(filterValue);
                                                        break;
                                                    case 'Employee Name':
                                                        filterValue = $('#filterid' + i).val();
                                                        filterArray.push(filterValue);
                                                        break;
                                                    case 'Amount':
                                                        var filterAmountStart = $('#filterAmountStartid' + i).val();
                                                        var filterAmountEnd = $('#filterAmountEndid' + i).val();
                                                        var finalAmount = '',
                                                            finalBegin = '',
                                                            finalEnd = '';
    
                                                        if (filterAmountStart !== '') {
                                                            finalBegin = filterAmountStart;
                                                        }
    
                                                        if (filterAmountEnd !== '') {
                                                            finalEnd = filterAmountEnd;
                                                        }
                                                        finalAmount = finalBegin + '&' + finalEnd;
    
                                                        filterArray.push(finalAmount);
                                                        break;
                                                    default:
                                                        filterValue = $('#filterid' + i).select2('val');
                                                        filterArray.push(filterValue);
                                                        break;
    
                                                }
                                            }
    
                                            $.ajax({
                                                type: 'POST',
                                                dataType: 'json',
                                                async: true,
                                                url: pageUrl() + 'reports/hr-reports/create-barchart/filterArray/' + filterArray,
                                                data: { _modName: modName, linkValue: linkValue },
                                                success: function (result) {
                                                    var filterArray = chartFilters(tableData);
                                                    if ((tableData['Flag_Stack'] == 1 && result[2].length == 1) || (tableData['Flag_Stack'] == 0 && result.length == 1)) {
                                                        //var filterArray = chartFilters(tableData);
                                                        var selectColumnsFor = ['Hourly Wage Payslip', 'Monthly Master Report', 'Hourly Master Report'];
                                                        var checkedValue = [];
    
                                                        if ($.inArray(linkValue, selectColumnsFor) != -1) {
                                                            $('#modalExportColumnsList').modal('toggle');
    
                                                            $('#submitExportColumnsList').on('click', function () {
                                                                checkedValue = $('#selectExportColumnsList').select2('val');
    
                                                                if (checkedValue != '') {
                                                                    $('#modalExportColumnsList').modal('hide');
                                                                    document.location.href = pageUrl () + 'reports/hr-reports/export-pdf/disGrid/s/linkValue/'+linkValue+'/_modName/'+modName+'/filterArray/'+filterArray+'/_ckedValue/'+checkedValue;
                                                                }
                                                            });
                                                            $('#selectExportColumnsList').select2('val', '');
                                                        }
                                                        else {
                                                            document.location.href = pageUrl () + 'reports/hr-reports/export-pdf/disGrid/s/linkValue/'+linkValue+'/_modName/'+modName+'/filterArray/'+filterArray+'/_ckedValue/'+checkedValue;
                                                        }
                                                    }
                                                    else {
                                                        ///**** Uncheck before closing the modal ****/
                                                        $('#formExportPie,#formExportBar,#formExportGrid,#formExportAll').prop('checked', false);
                                                        setTimeout(function () {
                                                            $('#modalExportPdf').modal('toggle');
                                                            $('#formExportAll').on('click', function () {
                                                                var isCheckedAll = $('#formExportAll').is(':checked');
                                                                if (isCheckedAll === true) {
                                                                    $('#formExportPie,#formExportBar,#formExportGrid').prop('checked', true);
                                                                }
                                                                else {
                                                                    $('#formExportPie,#formExportBar,#formExportGrid').prop('checked', false);
                                                                }
                                                            });
                                                            $('#formExportPie,#formExportBar,#formExportGrid').on('click', function () {
                                                                var isCheckedBar = $('#formExportBar').is(':checked');
                                                                var isCheckedPie = $('#formExportPie').is(':checked');
                                                                var isCheckedGrid = $('#formExportGrid').is(':checked');
                                                                if (isCheckedBar === false || isCheckedPie === false || isCheckedGrid === false) {
                                                                    $('#formExportAll').prop('checked', false);
                                                                }
                                                                else if (isCheckedBar === true && isCheckedPie === true && isCheckedGrid === true) {
                                                                    $('#formExportAll').prop('checked', true);
                                                                }
                                                            });
    
                                                            $('#exportReport').on('click', function () {
                                                                var exportArray = [];
                                                                var isCheckedBar = $('#formExportBar').is(':checked');
                                                                var isCheckedPie = $('#formExportPie').is(':checked');
                                                                var isCheckedGrid = $('#formExportGrid').is(':checked');
    
                                                                if (isCheckedBar == true) {
                                                                    exportArray.push('Bar');
                                                                }
                                                                if (isCheckedPie == true) {
                                                                    exportArray.push('Pie');
                                                                }
                                                                if (isCheckedGrid == true) {
                                                                    exportArray.push('Grid');
                                                                }
    
    
    
                                                                var checkedValue = [];
                                                                /* Now we are getting error in the PDF download with the Bar chart and Pie chart, 
                                                                * we will download the grid if any of the option is selected. We will fix it later */
                                                                var pdfDownloadURL = pageUrl () + 'reports/hr-reports/export-pdf/disGrid/s/linkValue/'+linkValue+'/_modName/'+modName+'/filterArray/'+filterArray+'/_ckedValue/'+checkedValue;
                                                               
                                                                /******Get SVG image of Bar Chart and append it******/
                                                                if ($.inArray('Bar', exportArray) != -1) {
                                                                    bsvg = $('#barChartReports').highcharts().getSVG();
                                                                    /* Now we are getting error in the PDF download with the Bar chart, we will fix it later. */
                                                                    // pdfDownloadURL = pdfDownloadURL+"/bsvg/"+bsvg;
                                                                }
    
                                                                /****** Get SVG image of pie chart and append it******/
                                                                if ($.inArray('Pie', exportArray) != -1) {
                                                                    /* Now we are getting error in the PDF download with the Pie chart, we will fix it later. */
                                                                    psvg = $('#pieChartReports').highcharts().getSVG();
                                                                    // pdfDownloadURL = pdfDownloadURL+"/psvg/"+psvg;
                                                                }

                                                                document.location.href = pdfDownloadURL;
                                                                
                                                                $('#modalExportPdf').modal('hide');
                                                            });
                                                        }, 200);
                                                    }
                                                    removeMask();
                                                },
                                                error:function(err){
                                                    removeMask();
                                                    if(err.status == 200){
                                                        sessionExpired();
                                                    }else{
                                                        /* To handle internal server error */
                                                        jAlert({ msg : "Something went wrong. Please contact system admin", type : "warning" });
                                                    }
                                                }
                                            });
                                        },
                                        error : function(err){
                                            removeMask();
                                            if(err.status == 200){
                                                sessionExpired();
                                            }else{
                                                /* To handle internal server error */
                                                jAlert({ msg : "Something went wrong. Please contact system admin", type : "warning" });
                                            }
                                        }
                                    });
                        }
                    }
                }
            });
        }

        function exportPdf(filterArray, checkedValue) {
             document.location.href = pageUrl () + 'reports/hr-reports/export-pdf/disGrid/s/linkValue/'+linkValue+'/_modName/'+modName+'/filterArray/'+filterArray+'/_ckedValue/'+checkedValue;
        }

        function adjustPrintView(maxTableWidth) {
            var fontSize = 10;
            var textWidth;
            do {
                $('.fontSize').css({ 'font-size': fontSize });
                textWidth = $('.reports_grid').width();
                fontSize = fontSize - 0.5;
            } while ((textWidth > maxTableWidth));
            return this;
        }
        function adjustPrint(maxTableWidth, printview) {
            var fontSize = 10;
            var textWidth;
            do {
                $('.fontSize').css({ 'font-size': fontSize });
                textWidth = $('.reports_grid').width();
                fontSize = fontSize - 0.5;
            } while ((textWidth > maxTableWidth));

            
            if (printview == 'landscape') {
                fontSize = fontSize - 2;
            }
            else if (printview == 'portrait') {
                fontSize = fontSize - 4;
            }
            

            $('.fontSize').css({ 'font-size': fontSize });

            return this;

        }
        /****** Function to Show Expanded Columns******/
        function createHiddenTable(oTable, nTr, dataArray) {

            //hidden-sm hidden-xs visible-md visible-lg
            var aData = oTable.fnGetData(nTr);

            var sOut = '<div class="row" >';

            for (var x in dataArray) {
                if (x <= 1) {
                    sOut += '<div class="col-md-6 col-sm-6 col-xs-12 row hidden-md hidden-lg visible-sm visible-xs" style="margin: 5px 0px;">' +
                        '<div class="col-md-5 col-sm-5 col-xs-4">' +
                        dataArray[x].replace(/_/gi, ' ') +
                        '</div>' +
                        '<div class="col-md-1 col-sm-1 col-xs-1">' +
                        ' : ' +
                        '</div>' +
                        '<div class="col-md-6 col-sm-6 col-xs-6">' +
                        fnCheckNull(aData[dataArray[x]]) +
                        '</div>' +
                        '</div>';
                }
                else if (x <= 3) {
                    sOut += '<div class="col-md-6 col-sm-6 col-xs-12 row visible-md hidden-lg visible-sm visible-xs" style="margin: 5px 0px;">' +
                        '<div class="col-md-5 col-sm-5 col-xs-4">' +
                        dataArray[x].replace(/_/gi, ' ') +
                        '</div>' +
                        '<div class="col-md-1 col-sm-1 col-xs-1">' +
                        ' : ' +
                        '</div>' +
                        '<div class="col-md-6 col-sm-6 col-xs-6">' +
                        fnCheckNull(aData[dataArray[x]]) +
                        '</div>' +
                        '</div>';
                }
                else {
                    sOut += '<div class="col-md-6 col-sm-6 col-xs-12 row" style="margin: 5px 0px;">' +
                        '<div class="col-md-5 col-sm-5 col-xs-4">' +
                        dataArray[x].replace(/_/gi, ' ') +
                        '</div>' +
                        '<div class="col-md-1 col-sm-1 col-xs-1">' +
                        ' : ' +
                        '</div>' +
                        '<div class="col-md-6 col-sm-6 col-xs-6">' +
                        fnCheckNull(aData[dataArray[x]]) +
                        '</div>' +
                        '</div>';
                }
            }
            sOut += '</div>';

            return sOut;
        }

        function chartFilters(tableData) {
            var filterArray = [];
            var filterTable = tableData.Filter_Table;
            var RepFilter = tableData.Rep_Filter;
            var res = filterTable.split(",");
            var filterHeading = RepFilter.split("-");

            for (var i in res) {
                if (filterHeading[i] == 'Employee Name' || filterHeading[i] == 'Manager Name') {
                    res[i] = 'Employee Name';
                }
                switch (res[i]) {
                    case 'DPicker':
                        var dateBegin = '#filterDateBeginid' + i;
                        var dateEnd = '#filterDateEndid' + i;
                        var finalBeginDate = $(dateBegin).val();
                        if (finalBeginDate != '') {
                            finalBeginDate = fnFormatDate(finalBeginDate);
                        }
                        var finalEndDate = $(dateEnd).val();
                        if (finalEndDate != '') {
                            finalDate = fnFormatDate(finalEndDate);
                            var finalEndDate = finalBeginDate + '&' + finalDate;
                        }
                        filterArray.push(finalEndDate);
                        break;
                    case 'MPicker':
                        filterValue = fnServerMonthFormatter($('#filterid' + i).val());
                        filterArray.push(filterValue);
                        break;
                    case 'Employee Name':
                        filterValue = $('#filterid' + i).val();
                        filterArray.push(filterValue);
                        break;
                    case 'Amount':
                        var filterAmountStart = $('#filterAmountStartid' + i).val();
                        var filterAmountEnd = $('#filterAmountEndid' + i).val();
                        var finalAmount = '',
                            finalBegin = '',
                            finalEnd = '';

                        if (filterAmountStart !== '') {
                            finalBegin = filterAmountStart;
                        }

                        if (filterAmountEnd !== '') {
                            finalEnd = filterAmountEnd;
                        }
                        finalAmount = finalBegin + '&' + finalEnd;

                        filterArray.push(finalAmount);
                        break;

                    default:
                        filterValue = $('#filterid' + i).select2('val');
                        filterArray.push(filterValue);
                        break;

                }
            }

            return filterArray;
        }
    }
});
