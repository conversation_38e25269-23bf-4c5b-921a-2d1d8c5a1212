[production]
phpSettings.display_startup_errors = 0
phpSettings.display_errors = 0
includePaths.library = APPLICATION_PATH "/../library"
bootstrap.path = APPLICATION_PATH "/Bootstrap.php"
bootstrap.class = "Bootstrap"
appnamespace = "Application"
resources.frontController.params.displayExceptions = 0

resources.modules[]=""
resources.frontController.params.prefixDefaultModule = "1"
resources.frontController.defaultModule = "default"
resources.view.doctype = "XHTML1_TRANSITIONAL"

resources.db.adapter = "PDO_MYSQL"
resources.db.params.charset = "utf8"
resources.db.params.port = 3306

resources.db.params.persistent = true
resources.db.params.compress = true

resources.mail.transport.type     = smtp
resources.mail.transport.auth     = "login"
resources.mail.transport.ssl      = "ssl"
resources.mail.transport.port     = 465
resources.mail.transport.register = true

resources.session.cache_limiter = must-revalidate
resources.view.charset = "UTF-8"
resources.view.helperPath.View_Helper = APPLICATION_PATH "/views/helpers"
resources.layout.layoutPath = APPLICATION_PATH "/layouts/scripts/"

mobileapps.production = 1
resources.frontController.moduleDirectory = APPLICATION_PATH "/modules"

mobileapps.domain = cannyhr.com
mobileapps.productlogo=0

mobileapps.region = ap-south-1
mobileapps.bucketName = s3.taxdocs.cannyhr.com
mobileapps.version = 2006-03-01
mobileapps.imageBucket = s3.images.cannyhr.com
mobileapps.logoBucket = s3.logos.cannyhr.com
mobileapps.smregion = ap-south-1
mobileapps.signedURLValidity = '1200 seconds'
mobileapps.reimbursementSignedURLValidity = '604800 seconds'
mobileapps.secretname = PROD/CANNY/PGACCESS
mobileapps.ocrapiurlprefix = ""
mobileapps.iciciApiBaseUrl = ""
mobileapps.iciciCIBBaseURL = ""
mobileapps.ccAvenueWorkingKey =""
mobileapps.ccAvenueAccessCode = ""
mobileapps.ccAvenueURL = ""
mobileapps.facebookURL = https://www.facebook.com/HRAPPONCLOUD/
mobileapps.twitterURL = https://twitter.com/hrapponcloud
mobileapps.linkedinURL = https://www.linkedin.com/showcase/13191342
mobileapps.googleURL = "https://www.google.com/maps/dir//hrapp/data=!4m6!4m5!1m1!4e2!1m2!1m1!1s0x3ba8562e7d3f6c1b:0xb8f93ff5aa0725c8?sa=X&ved=2ahUKEwiw59nourbiAhVZT30KHVnQBjsQ9RcwFXoECAkQDg"
mobileapps.websiteURL = https://hrapp.in/
mobileapps.atsBaseURL= https://api.cannyhr.com/ats/graphql
mobileapps.clientipUrl= "https://api.ipify.org?format=json"
mobileapps.integrationBaseURL= https://api.cannyhr.com/integration/rographql
mobileapps.trstscoreBaseURL= https://api.cannyhr.com/trstscore/wographql
mobileapps.workflowEngineInitiateBaseUrl=https://api.cannyhr.com/workflowEngine/workflow/initiate
mobileapps.coreHrRoBaseUrl= https://api.cannyhr.com/coreHr/rographql
mobileapps.hrappBeRoBaseUrl=https://api.cannyhr.com/hrappBe/roGraphql
mobileapps.employeeSelfServiceRoBaseURL=https://api.cannyhr.com/employeeSelfService/rographql
mobileapps.employeeSelfServiceWoBaseURL=https://api.cannyhr.com/employeeSelfService/wographql
mobileapps.coreHrWoBaseUrl= https://api.cannyhr.com/coreHr/wographql
mobileapps.payrollAdminWoBaseUrl= https://api.cannyhr.com/payrollAdmin/woGraphql
mobileapps.integrationWoExternalBaseURL= https://api.cannyhr.com/integration/externalauth
mobileapps.batchProcessingExternalBaseURL=https://api.cannyhr.com/batchProcessing/external

mobileapps.firebaseApiKey = AIzaSyAz3TaOCY3KlDFyrNZ_feZe9dENgG1rU7g
mobileapps.firebaseAuthDomain = cannyhr-identity.firebaseapp.com
mobileapps.firebaseDatabaseURL = https://cannyhr-identity.firebaseio.com
mobileapps.firebaseProjectId = cannyhr-identity
mobileapps.firebaseStorageBucket = cannyhr-identity.appspot.com
mobileapps.firebaseMessagingSenderId = 769444509795
mobileapps.firebaseAppId = 1:769444509795:web:3e06faed9dc2f05759a056
mobileapps.refreshTokenAPIUrl = https://securetoken.googleapis.com/v1/

mobileapps.entomoAccessTokenUrl=epms/noAuth/token
mobileapps.entomoLoginUrl=epms/oatlogin
mobileapps.entomoRefreshTokenUrl=epms/refresh

mobileapps.appSecretKey = a60be24833cbd6f0e424e133b0f55c96
mobileapps.appVersion[] = 120201912
mobileapps.appVersion[] = 121202001
mobileapps.appVersion[] = 122202002
mobileapps.appVersion[] = 123202002
mobileapps.appVersion[] = 124202003
mobileapps.appVersion[] = 125202003
mobileapps.appVersion[] = 132200000
mobileapps.appVersion[] = 132300000
mobileapps.appVersion[] = 133000000
mobileapps.appVersion[] = 134000000
mobileapps.appVersion[] = 135000000
mobileapps.appVersion[] = 136000000
mobileapps.appVersion[] = 137000000

mobileapps.redirectionurl= https://www.cannyhr.com/appmanager/mobilemanager
resources.db.params.username = ""
resources.db.params.encrypt = 0
resources.db.params.password = ""
resources.db.params.dbname = cannyhr_managerdb
resources.db.profiler.enabled = false
resources.mail.transport.host     = ""
resources.mail.defaultfrom.email = "<EMAIL>"