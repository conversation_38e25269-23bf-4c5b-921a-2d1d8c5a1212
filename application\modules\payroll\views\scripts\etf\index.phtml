<?php
    $this->headTitle($this->formNameA);
	
	$etfUser       = $this->etfUser;
	$employeeName  = $this->employeeName;
	$etfPaymentUser   = $this->etfPaymentUser;
	$etfRulesUser = $this->etfRulesUser;
    $paymentModePair = $this->paymentMode;	
    $empDepartment = array();
	
	foreach ($employeeName as $key => $row) {
		if (!in_array($row['Department_Name'], $empDepartment)) {
			array_push($empDepartment, $row['Department_Name']);
		}
	}
    
    $customFormNameA = $this->customFormNameA;
    $customFormNameB = $this->customFormNameB;
	$customFormNameC = $this->customFormNameC;
	
    
	if ($etfUser['View']|| $etfRulesUser['View']) {?>
<div class="col-md-12 portlets paddingCls tab-spacing-cls">
	<div class="col-md-12 paddingCls bg-f9f9f9 tab-wrapper">
		<?php if ($etfUser['View']) { ?>
			<div class="pointer-cls border-bottom-secondary bg-f9f9f9 tab-body" id="npsConfTab">
				<div class="tab-text-font custom-tab-content" id="npsConfFormTab">
					<a id="formTabLink1" class="tab-a-tag-color" href=<?php echo $this->baseUrl('/v3/tax-and-statutory-compliance/statutory-components/nps?tab=nps'); ?>>
						<?php echo !empty($customFormNameA['New_Form_Name']) ? $customFormNameA['New_Form_Name'] : $this->formNameA ?>
					</a>
				</div>
			</div>
		<?php } ?>
		<div class="pointer-cls border-bottom-secondary tab-border-cls bg-f9f9f9 tab-body" id="npsTrackerTab">
			<div class="tab-active-text tab-text-font text-secondary custom-tab-content"  id="npsTrackerFormTab">
				<?php echo !empty($customFormNameB['New_Form_Name']) ? $customFormNameB['New_Form_Name'] : $this->formNameB ?>
			</div>
		</div>
		<?php if ($etfRulesUser['View']) { ?>
			<div class="pointer-cls border-bottom-secondary bg-f9f9f9 tab-body" id="npsTab">
				<div class="tab-text-font custom-tab-content" id="npsFormTab">
					<a id="formTabLink2" class="tab-a-tag-color" href=<?php echo $this->baseUrl('/v3/tax-and-statutory-compliance/statutory-components/nps?tab=npsRules'); ?>>
						<?php echo !empty($customFormNameC['New_Form_Name']) ? $customFormNameC['New_Form_Name'] : $this->formNameC ?>
					</a>
				</div>
			</div>
		<?php } ?>
	</div>
</div>
<?php }	?>
<?php
    if ($etfUser['View'] == 1 && ((!empty($customFormNameA) && array_key_exists("Enable",$customFormNameA) && $customFormNameA['Enable'] == 1) || empty($customFormNameA))) {
		$this->headTitle($customFormNameA['New_Form_Name']!='' ? $customFormNameA['New_Form_Name'] : $this->formNameA);
?>
<div class="col-md-12 portlets hidden">
	<div class="panel" id="gridPanelETF">
		<div class="panel-header md-panel-controls">
			<!--<h3><i class="icon-list"></i> <strong><?php echo $this->formNameA; ?> </strong></h3>-->
            <h3><i class="icon-list"></i> <strong id="lblFormNameA"><?php echo ($customFormNameA['New_Form_Name']!='' ? $customFormNameA['New_Form_Name'] : $this->formNameA);?></strong></h3>
		</div>
		<div class="panel-content">			
			<div class="m-b-10">
                    
                    <!-- Add Button in Grid Toolbar -->
					<?php if ($etfUser['Add'] == 1) { ?>
					<button type="button" class="btn btn-white btn-embossed toolbar-icons" title="Add" data-toggle="modal" data-target="#modalFormETF" id="addETF">
						<i class="mdi-content-add"></i><span class="hidden-xs hidden-sm"> Add</span>
					</button>
                    <?php } ?>
                    
                    <!-- View Button in Grid Toolbar -->
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="viewETF" title="View">
						<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
					</button>
					
                    <!-- Update Button in Grid Toolbar -->
					<?php if ($etfUser['Update']) { ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="editETF" title="Edit">
						<i class="mdi-editor-mode-edit"></i><span class="hidden-xs hidden-sm"> Edit</span>
					</button>
					<?php } ?>
					
					<!-- Delete Button in Grid Toolbar -->
					<?php if ($etfUser['Delete']) { ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="deleteETF" title="Delete">
						<i class="mdi-action-delete"></i><span class="hidden-xs hidden-sm"> Delete</span>
					</button>
					<?php } ?>
					
					<!-- Copy Pf Button in Grid Toolbar -->
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="Bulk copy" id="copyETF">
						<i class="mdi-action-info-outline"></i><span class="hidden-xs hidden-sm"> Bulk Copy</span>
					</button>
                    
                    <!-- Filter Button in Grid Toolbar -->
                    <a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterETF">
                        <i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm">Filter</span>
                    </a>					
			</div>
            
            <!-- MonthlySalary Grid -->			
			<table class="table dataTable table-striped table-dynamic table-hover" id="tableETF">
				<thead>
					<tr>
                        <th></th>
						<th id="etfEmpId">Employee Id</th>
						<th id="etfCoverage">Coverage</th>
                        <th id="etfSalaryType">Salary Type</th>
                        <th id="etfEmpName">Employee Name</th>
                        <!--<th>Emp Share Amount</th>-->
                        <th id="etfOrgShareAmount">Org Share Amount</th>
                        <!--<th>Emp Share(%)</th>-->
                        <th id="etfOrgSharePercentage">Org Share(%)</th>
                        <th id="etfStatutoryLimit">Statutory Limit</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
            
        </div>
    </div>
</div>

<!--Filter Form-->
<div class="builder" id="filterPanelETF">
	<div id="closeFilterETF"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel filterReset" style="width: 100%;" id="cancelETF">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="applyETF">Apply</button>
            
			<!--<php if ($etfUser['View'] == 1 && $etfUser['Is_Manager'] == 0 && empty($etfUser['Admin'])) { >			-->
			<!--Filter for Employee Name-->
			<!--<div class="form-group">-->
			<!--	<label>Employee Name</label>-->
			<!--	<input type="text" class="form-control" id="filterEmployeeName" placeholder="Employee Name" >-->
			<!--</div>-->
			
			<!--<php } else { ?>-->
			
			<!--<div class="form-group">-->
			<!--	<label>Employee Name</label>-->
			<!--	<input type="text" class="form-control" id="filterEmployeeName" placeholder="Employee Name" >-->
			<!--</div>-->
			
			<!--<php } >-->
			
			<!--Filter For Employee Share Percent -->
			<!--<div class="form-group">
				<label>Employee Share Percent</label>
				<div class="input-group">
					<input type="number" class="form-control" name="etfemployeeShareStart" id="filteretfempShareStart" min="0" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="form-control" name="etfemployeeShareEnd" id="filteretfempShareEnd" min="0" placeholder="End"/>
				</div>
			</div>-->
			
			<!--Filter For Company Share Percent -->
			<div class="form-group">
				<label>Company Share Percent</label>
				<div class="input-group">
					<input type="number" class="form-control" name="etfcompanyShareStart" id="filteretfcomShareStart" min="0" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="form-control" name="etfcompanyShareEnd" id="filteretfcomShareEnd" min="0" placeholder="End"/>
				</div>
			</div>
			
			<!--Filter For Employee Share Amount -->
			<!--<div class="form-group">-->
			<!--	<label>Employee Share Amount</label>-->
			<!--	<div class="input-group">-->
			<!--		<input type="number" class="form-control" name="etfemployeeShareAmountStart" id="filteretfempShareAmountStart" min="0" placeholder="Begin"/>-->
			<!--		<span class="input-group-addon">to</span>-->
			<!--		<input type="number" class="form-control" name="etfemployeeShareAmountEnd" id="filteretfempShareAmountEnd" min="0" placeholder="End"/>-->
			<!--	</div>-->
			<!--</div>-->
			
			<!--Filter For Company Share Amount -->
			<div class="form-group">
				<label>Company Share Amount</label>
				<div class="input-group">
					<input type="number" class="form-control" name="etfcompanyShareAmountStart" id="filterComShareAmountStart" min="0" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="form-control" name="etfcompanyShareAmountEnd" id="filterComShareAmountEnd" min="0" placeholder="End"/>
				</div>
			</div>			
            
		</div>
	</div>
</div>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="etf-context-menu" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextETF"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($etfUser['Update']) { ?>
		<li><a tabindex="-1" id="editContextETF"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<?php } if ($etfUser['Delete']) { ?>
		<li><a tabindex="-1" id="deleteContextETF"><i class="mdi-action-delete"></i> Delete</a></li>
		<?php } ?>
		<li><a tabindex="-1" id="copyContextETF"><i class="mdi-action-info-outline"></i> Bulk Copy</a></li>
	</ul>
</div>

<!--Modal for provident fund view,add & edit form-->
<div class="modal fade" id="modalFormETF" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace" id="backETF"></i>
				</button>
				
				<?php if ($etfUser['Update']) { ?>
				<button type="button" class="close form-icons" aria-hidden="true" id="editInViewETF">
					<i class="mdi-editor-mode-edit"></i>
				</button>
				<?php } ?>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<!--View ETF Form-->
				<form role="form" id="viewFormETF" >
					<div class="row">
						<div class="form-group">
							<div class="col-md-5"><label class="control-label"> Coverage</label></div>
							<div class="col-md-7"><p id="viewEtfCoverage"></p></div>
						</div>
						<div class="form-group employeeBasedHidden">
							<div class="col-md-5"><label class="control-label"> Employee Name</label></div>
							<div class="col-md-7"><p id="viewEtfEmployeeName"></p></div>
						</div>
						<!--<div class="form-group">-->
						<!--	<div class="col-md-5"><label class="control-label"> Employee Share</label></div>-->
						<!--	<div class="col-md-7"><p id="viewEtfEmployeeShare"></p></div>-->
						<!--</div>-->
						<div class="form-group">
							<div class="col-md-5"><label class="control-label"> Company Share</label></div>
							<div class="col-md-7"><p id="viewEtfCompanyShare"></p></div>
						</div>						
						<div class="form-group companyBasedHidden">
							<div class="col-md-5"><label class="control-label"> Salary Type</label></div>
							<div class="col-md-7"><p id="viewEtfSalaryType"></p></div>
						</div>
						<div class="form-group companyBasedHidden">
							<div class="col-md-5"><label class="control-label"> Define Statutory Salary Limit</label></div>
							<div class="col-md-7"><p id="viewEtfStatutoryLimit"></p></div>
						</div>
						<div class="form-group companyBasedHidden SSalaryBasedHidden">
							<div class="col-md-5"><label class="control-label"> Statutory Salary Limit</label></div>
							<div class="col-md-7"><p id="viewEtfStatutorySalary"></p></div>
						</div>
						<div class="form-group companyBasedHidden SSalaryBasedHidden">
							<div class="col-md-5"><label class="control-label"> Statutory Limit Comparison</label></div>
							<div class="col-md-7"><p id="viewEtfStatutoryLimitCompare"></p></div>
						</div>						
					</div>
					
					<div class="row">
						<hr class="view-hr"/>
						
						<div class="form-group" style="font-size: large;margin-left: 13px;">
							<label class="control-label text-center">Additional Information</label>
						</div>
						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Added On</label></div>
							<div class="col-md-7"><p id="addedOnETF"></p></div>
						</div>
						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Added By</label></div>
							<div class="col-md-7"><p id="addedByETF"></p></div>
						</div>
						
						<div class="form-group updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated On</label></div>
							<div class="col-md-7"><p id="updatedOnETF"></p></div>
						</div>
						
						<div class="form-group updatedPanel">
							<div class="col-md-5"><label class="control-label">Updated By</label></div>
							<div class="col-md-7"><p id="updatedByETF"></p></div>
						</div>
					</div>
				</form>
				<?php if ($etfUser['Add'] == 1 || $etfUser['Update'] == 1) { ?>
				
				<!--Add/Edit provident fund Form-->
				<form role="form" class="form-horizontal form-validation" id="editFormETF" method="POST" action="">
					<input type="hidden" name="EtfOrg_Id" id="EtfOrgId" />
					<input type="hidden" name="Employee_Id" id="EmployeeId" />					
					<div class="row">
						<div class="form-group">
							<label class="col-md-4 control-label">Coverage <span class="short_explanation">*</span></label>							
							<div class="col-md-8">								
								<select class="form-control vRequired" data-search="true" name="Coverage" id="formCoverage">
									<option value="O"selected="selected" >Organization</option>
									<option value="E">Employee</option>
								</select>
							</div>
						</div>
						
						<div class="form-group formCoverageEmpHidden">
							<label class="col-md-4 control-label">Employee Name <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control" name="Employee_Name" id="formEmployeeName" data-search="true">
									<option value="">--Select--</option>
									<?php
										foreach ($empDepartment as $key => $row) {
											echo '<optgroup label="'. $row .'">';
											
											foreach ($employeeName as $empKey => $empRow) {
												if ($row == $empRow['Department_Name']) {
													echo '<option value="'. $empRow['value'] .'">'. $empRow['text'] .'</option>';
												}
											}
											
											echo '</optgroup>';
										}
									?>
								</select>
							</div>							
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Contribution <span class="short_explanation">*</span></label>							
							<div class="col-md-8">								
								<select class="form-control vRequired" name="Contribution" id="formContribution" data-search="true">									
									<option value=0 >Fixed Amount</option>
									<option value=1 selected="selected">Percentage</option>
								</select>
							</div>
                        </div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Company Share <span class="short_explanation">*</span></label>
							<div class="col-md-8">																
								<!--<input type="number" class="form-control companyShare" id="formCompanyShare" name="CompanyShare">-->
                                <input type="number" class="form-control companyShare convertDecimalPos1" step="0.01" min="1" max="*************" id="formCompanyShare" name="CompanyShare" >
							</div>
						</div>
						
						<!--<div class="form-group">-->
						<!--	<label class="col-md-4 control-label">Employee Share <span class="short_explanation">*</span></label>-->
						<!--	<div class="col-md-8">																-->
						<!--<!--		<input type="number" class="form-control employeeShare" id="formEmployeeShare" name="EmployeeShare">-->
                        <!--        <input type="number" class="form-control employeeShare convertDecimalPos1" step="0.01" min="1" max="*************" id="formEmployeeShare" name="EmployeeShare" >-->
						<!--	</div>-->
						<!--</div>						-->
						
                        <div class="form-group formCoverageOrgHidden">
							<label class="col-md-4 control-label">Salary Type <span class="short_explanation">*</span></label>							
							<div class="col-md-8">								
								<select class="form-control" name="SalaryType" id="formSalaryType" data-search="true">
									<option value="" selected="selected">--Select--</option>
									<option value="MON">Monthly</option>
									<option value="HOU">Hourly</option>
								</select>
							</div>
						</div>
						
						<div class="form-group formCoverageOrgHidden">
							 <label class="col-md-4 control-label">Define Statutory Salary Limit </label>
							<div class="col-md-8 togglebutton togglebutton-material-blue" style="padding-left: 15px; padding-top: 10px;">
								<label>
									<input id="formDefineStatutorySalary" type="checkbox" checked="checked" class="md-checkbox">
								</label>
							</div>
						</div>
						
						<div class="form-group formStatutoryLimitHidden">
							<label class="col-md-4 control-label">Statutory Salary Limit <span class="short_explanation">*</span></label>
							<div class="col-md-8">								
								<!--<input type="number" class="form-control" min=1 id="formStatutorySalaryLimit" name="Statutory_Salary_Limit" placeholder="Statutory Salary Limit" >-->
                                <input type="number" class="form-control convertDecimalPos1" step="0.01" min="1" max="*************" id="formStatutorySalaryLimit" name="Statutory_Salary_Limit" placeholder="Statutory Salary Limit" >
							</div>
						</div>
						
						<div class="form-group formStatutoryLimitHidden">
							<label class="col-md-4 control-label">Statutory Limit Comparison <span class="short_explanation">*</span></label>							
							<div class="col-md-8">								
								<select class="form-control" name="StatutoryLimitCompare" id="formStatutoryLimitCompare">
									<option value=0>Less than or Equal to Statutory Limit</option>
									<option value=1>Greater than statutory Limit</option>
								</select>
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Description </label>
							<div class="col-md-8">
								<textarea name="Description" id="formEditDescription" rows="5" class="form-control vComments" placeholder="Description..." ></textarea>
							</div>                           
						</div>                        
					</div>
					
					<button type="reset" class="cancel" id="formETFReset" style="display: none;" ></button>
				</form>
				
				<?php } ?>				
			</div>
			<div class="modal-footer text-center" id="formActionETF">				
				<?php if ($etfUser['Add'] == 1 || $etfUser['Update'] == 1) { ?>
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetETF" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitETF" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
				<?php } ?>
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="modalcopyETF" aria-hidden="true" style="z-index: 10;">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" id="modalCopyFormClose" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace"></i>
				</button>
				<h4 class="modal-title"> Copy ETF</h4>
			</div>
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="copyFormETF" method="POST" action="">
					<input type="hidden" name="CopyEmployee_Id" id="formCopyEmployeeId" />
					<input type="hidden" name="CopyFixedVariableFlag" id="formCopyContribution" />
					<div class="row">
						<div class="form-group">
							<label class="col-md-4 control-label">Employee Name <span class="short_explanation">*</span></label>
							<div class="col-md-8">								
								<select multiple="multiple" class="form-control vRequired selectAlll" data-search="true" name="copyEmployeeName" id="formcopyEmployeeName">
									<option value="selectAll">--Select all--</option>
									<option value="clearAll">--Clear all--</option>
									<?php
										foreach ($employeeName as $empKey => $empRow) {
												echo '<option value="'. $empRow['value'] .'">'. $empRow['text'] .'</option>';
											}
									?>
								</select>
							</div>
						</div>
						<div class="form-group">
							<label class="col-md-4 control-label">Coverage <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<input type="text" class="form-control vRequired" readonly="readonly" id="formCopyCoverage" name="CopyCoverage">
							</div>
						</div>
						<div class="form-group">
							<label class="col-md-4 control-label">Company Share <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<input type="number" class="form-control vRequired convertDecimalPos1" id="formCopyCompanyShare" name="CopyCompanyShare">
							</div>
						</div>
						<div class="form-group">
							<label class="col-md-4 control-label">Employee Share  <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<input type="number" class="form-control vRequired convertDecimalPos1" id="formCopyEmployeeShare" name="CopyEmployeeShare">
							</div>
						</div>
						<div class="form-group">
							<label class="col-md-4 control-label" id="labelCopyDescription">Description </label>
							<div class="col-md-8">
								<textarea name="Description" id="formCopyDescription" rows="5" class="form-control vDescription"
										  placeholder="Description..." ></textarea>
							</div>
						</div>						
					</div>
					
					<button type="reset" class="cancel" id="formCopyEtfReset" style="display: none;" ></button>
				</form>				
			</div>
			<div class="modal-footer text-center" id="formActionCopyEtf">				
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetCopyEtf" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button"  data-style="expand-left" data-style="expand-left" id="formCopyEtf" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
			</div>
		</div>
	</div>
</div>

<?php if ( $etfPaymentUser['Update']) { ?>
<div class="modal fade" id="modalDirtyEtf" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditConfETF"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfETF">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseConfirmEtf">Yes</button>
			</div>
		</div>
	</div>
</div>



<!-- Form Dirty Copy ETF Modal -->
<?php } if ($etfUser['Update'] == 1 && ($etfUser['Is_Manager'] == 1 || !empty($etfUser['Admin']))) { ?>
<div class="modal fade" id="dirtyCopyETF" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeCopyConfETF"></i></button>
                <h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
            </div>
            
            <div class="modal-body">Are you sure want to close this form?<br></div>
            
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noCopyConfETF">No</button>
              <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="closeCopyEtf">Yes</button>
            </div>
        </div>
    </div>
</div>

<?php } ?>

<div class="modal fade" id="modalDeleteETF" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteConfETF"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfETF">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConfirmETF">Yes</button>
			</div>
		</div>
	</div>
</div>


<?php  } if ($etfPaymentUser['View'] == 1 && ((!empty($customFormNameB) && array_key_exists("Enable",$customFormNameB) && $customFormNameB['Enable'] == 1) || empty($customFormNameB))) { ?>		
<!-- Payment Tracker Grid Panel -->
<div class="col-md-12 portlets add-panel-padding">
	<div class="panel" id="gridPanelPaymentTracker">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong id="lblFormNameB"><?php echo ($customFormNameB['New_Form_Name']!='' ? $customFormNameB['New_Form_Name'] : $this->formNameB);?></strong></h3>
		</div>
		<div class="panel-content">			
			<div class="m-b-10">
                    <!-- View Button in Grid Toolbar -->
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="viewEtfPaymentTracker" title="View">
						<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
					</button>
                    
					<!-- Update Button in Grid Toolbar -->
					<?php if ($etfPaymentUser['Update']) { ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="editEtfPaymentTracker" title="Update">
						<i class="mdi-editor-mode-edit"></i><span class="hidden-xs hidden-sm"> Update</span>
					</button>
					<?php } ?>
					
                    <!-- Filter Button in Grid Toolbar -->
                    <a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterPaymentTracker">
                        <i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm">Filter</span>
                    </a>					
			</div>
			
            <!-- Payment Tracker Grid -->			
			<table class="table dataTable table-striped table-dynamic table-hover" id="tableEtfPaymentTracker">
				<thead>
					<tr>
						<th></th>
						<th id="etfPaymentTrackerSalaryMonth">Salary Month</th>
						<th id="etfPaymentTrackerEmpShareAmount">Emp Share Amount</th>
						<th id="etfPaymentTrackerOrgShareAmount">Org Share Amount</th>
						<th id="etfPaymentTrackerTotalAmount">Total Amount</th>
						<th id="etfPaymentTrackerAmountPaid">Amount Paid</th>
						<th id="etfPaymentTrackerStatus">Status</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
		</div>
	</div>
</div>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="etfPaymentTracker-context-menu" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextEtfPaymentTracker"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($etfPaymentUser['Update']) { ?>
		<li><a tabindex="-1" id="editContextEtfPaymentTracker"><i class="mdi-editor-mode-edit"></i> Update</a></li>		
		<?php } ?>		
	</ul>
</div>

<!--Modal for payment tracker view & edit form-->
<div class="modal fade" id="modalFormEtfPaymentTracker" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" id="paymentTrackerClose" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace"></i>
				</button>
				
				<?php if ($etfPaymentUser['Update']) { ?>
				<button type="button" class="close form-icons" aria-hidden="true" id="editInViewEtfPaymentTracker">
					<i class="mdi-editor-mode-edit"></i>
				</button>
				<?php } ?>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<!--View Pf PaymentTracker Form-->
				<form role="form" id="viewFormEtfPaymentTracker" >
					<div class="row">
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Payslip Month</label></div>
							<div class="col-md-7"><p id="viewPaymentTrackerPayslipMonth"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Organization Share Amount</label></div>
							<div class="col-md-7"><p id="viewPaymentTrackerOrgShareAmount"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Employee Share Amount</label></div>
							<div class="col-md-7"><p id="viewPaymentTrackerEmpShareAmount"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Total Amount</label></div>
							<div class="col-md-7"><p id="viewPaymentTrackerTotalAmount"></p></div>
						</div>						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label"> Status</label></div>
							<div class="col-md-7"><p id="viewPaymentTrackerStatus"></p></div>
						</div>
						<!-- Payment Tracker sub grid Grid Table -->
						<table class="table dataTable table-striped table-dynamic table-hover" id="tableEtfPaymentTrackerSubGridView">
							<thead>
								<tr>
									<th></th>
									<th id="etfPaymentTrackerSubViewModeOfPayment">Mode of Payment</th>
									<th id="etfPaymentTrackerSubViewPaymentDate">Payment Date</th>
									<th id="etfPaymentTrackerSubViewAmountPaid">Amount Paid</th>
									<th id="etfPaymentTrackerSubViewDescription">Description</th>
								</tr>
							</thead>
							<tbody>
							
							</tbody>
						</table>
					</div>
					
				</form>
				
				<?php if ($etfPaymentUser['Update'] == 1) { ?>
				
				<!--Add/Edit Payment Tracker Form-->
				<form role="form" class="form-horizontal form-validation" id="editFormEtfPaymentTracker" method="POST" action="">
					<input type="hidden" name="	Payment_Id" id="formEtfPaymentId" />
					<input type="hidden" name="ETF_Payment_Tracker_Id" id="formEtfPaymentTrackerId" />
				
					<div class="panel-group panel-accordion" id="editAccordion">
						<div class="panel panel-default">
							<div class="panel-heading">                                
								 <h4>
									<a data-toggle="collapse" data-parent="#editAccordion" href="#editPFDetails" id="etfPaymentSummaryPanel">
										Payment Summary
									</a>
								</h4>
							</div>
							<div id="editPFDetails" class="panel-collapse collapse in">
								<div class="panel-body">								
									<div class="form-group">										
										<div class="col-md-4"><label class="control-label">Payslip Month</label></div>
										<div class="col-md-8"><p id="formEtfPaymentTrackerPayslipMonth"></p></div>
									</div>
									<div class="form-group">										
										<div class="col-md-4"><label class="control-label">Organization Share Amount</label></div>
										<div class="col-md-8"><p id="formEtfPaymentTrackerOrgShareAmount"></p></div>
									</div>
									<div class="form-group">										
										<div class="col-md-4"><label class="control-label">Employee Share Amount</label></div>
										<div class="col-md-8"><p id="formEtfPaymentTrackerEmpShareAmount"></p></div>
									</div>								
									
									<div class="form-group">
										<div class="col-md-4"><label class="control-label">Total Amount</label></div>
										<div class="col-md-8"><p id="formEtfPaymentTrackerTotalAmount"></p></div>
									</div>
									<div class="form-group">										
										<div class="col-md-4"><label class="control-label">Status</label></div>
										<div class="col-md-8"><p id="formEtfPaymentTrackerStatus"></p></div>
									</div>
									<div class="form-group">										
										<div class="col-md-4"><label class="control-label">Outstanding Amount</label></div>
										<div class="col-md-8"><p id="formEtfPaymentTrackerOutstandingAmount"></p></div>
									</div>
								</div>							
							</div>
						</div>
						<div class="panel panel-default" id="editEtfPaymentTrackerPanel">
							<div class="panel-heading">
								<h4>
									<a class="collapsed" data-toggle="collapse" data-parent="#editAccordion" id="onEtfAddCollapse" href="#editEtfPaymentTrackerDetails">
										Payment Details
									</a>
								</h4>
							</div>							
							<div id="editEtfPaymentTrackerDetails" class="panel-collapse collapse">
								<div class="panel-body">						
                                    
									<div class="form-group">
										<label class="col-md-4 control-label">Mode of Payment <span class="short_explanation">*</span></label>
										<div class="col-md-8">
											<select class="form-control vRequired" data-search="true" id="formEtfPaymentTrackerModeOfPayment" name="PaymentTrackerModeOfPayment" >
												<option value="">-- Select --</option>
												<?php
												foreach ($paymentModePair as $key => $row)
												{
													echo '<option value="'.$key.'">'.$row.'</option>';
												}
												?>
											</select>
										</div>
									</div>
									
									<div class="form-group">
										<label class="col-md-4 control-label">Payment Date <span class="short_explanation">*</span></label>
										<div class="col-md-8">
											<input type="text" id="formEtfPaymentTrackerPaymentDate" class="date-picker form-control vRequired datePickerRead" name="Payment_Date" placeholder="Payment Date" >
										</div>
									</div>
									
									<div class="form-group paymentModeBasedHidden">
										<label class="col-md-4 control-label">Document No <span class="short_explanation">*</span></label>
										<div class="col-md-8">
											<input type="text" class="form-control numSpComma" maxlength="15" id="formEtfPaymentTrackerDocumentNo" name="Document_No" placeholder="Document No">
										</div>
									</div>
									
									<div class="form-group paymentModeBasedHidden">
										<label class="col-md-4 control-label">Bank Name <span class="short_explanation">*</span></label>
										<div class="col-md-8">
											<input type="text" class="form-control onlyLetterSp" minlength="3" maxlength="30" id="formEtfPaymentTrackerBankName" name="Bank_Name" placeholder="Bank Name">
										</div>
									</div>
									
									<div class="form-group paymentModeBasedHidden">
										<label class="col-md-4 control-label">Branch Name <span class="short_explanation">*</span></label>
										<div class="col-md-8">
											<input type="text" class="form-control onlyLetterSp" id="formEtfPaymentTrackerBranchName" name="Branch_Name" placeholder="Branch Name">
										</div>
									</div>
									
									<div class="form-group">
										<label class="col-md-4 control-label">Amount Paid <span class="short_explanation">*</span></label>
										<div class="col-md-8">
											<input type="number" class="form-control convertDecimalPos1 vRequired" min="1" max="*************" required="true" id="formEtfPaymentTrackerAmountPaid" name="Amount Paid" placeholder="Amount Paid">
										</div>
									</div>
									
									<div class="form-group">
										<label class="col-md-4 control-label">Description</label>
										<div class="col-md-8">
											<textarea name="description" id="formEtfPaymentTrackerDescription" rows="5" class="form-control vDescription" placeholder="Write your Description..." ></textarea>
										</div>
									</div>
									
									<div class="text-center"> 			
										<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetEtfPaymentTracker" style="bottom: 5px;">
											<i class="mdi-action-restore"></i> Reset
										</button>
										<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitEtfPaymentTracker" style="bottom: 5px;">
											<i class="mdi-content-send"></i> Add
										</button>
									</div>
									
								</div>
							</div>
						</div>
					</div>
					
					<button type="reset" class="cancel" id="formEtfPaymentTrackerReset" style="display: none;" ></button>
				</form>
				
				<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="addEtfPaymentTracker" title="Add">
					<i class="mdi-content-add"></i><span> Add</span>
				</button>
				
				<div id="etfPaymentTrackerGrid" >
					<table class="table dataTable table-striped table-dynamic table-hover" id="tableEtfPaymentTrackerSubGrid">
						<thead>
							<tr>
								<th></th>
								<th id="etfPaymentTrackerSubModeOfPayment">Mode of Payment</th>
								<th id="etfPaymentTrackerSubPaymentDate">Payment Date</th>
								<th id="etfPaymentTrackerSubAmountPaid">Amount Paid</th>
							</tr>
						</thead>
						<tbody>
						
						</tbody>
					</table>
				</div>
				<?php } ?>
				
				</div>
			</div>			
		</div>
	</div>
</div>

<!--Filter Form-->
<div class="builder" id="filterPanelEtfPaymentTracker">
	<div id="closeFilterEtfPaymentTracker"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel filterReset" style="width: 100%;" id="cancelPaymentTracker">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="applyPaymentTracker">Apply</button>
			
			<!--Filter For Payslip Month -->
			<div class="form-group">
				<label>Salary Month</label>
                <!--<input type="month" class="form-control" name="paySlipMonthStart" id="filterPaySlipMonth">-->
				<input type="text" class="b-datepicker vmonthMax vMonthClosure form-control vRequired closeMonthPicker" name="paySlipMonthStart"
                data-date-format="MM,yyyy" data-view="1" data-date-min-view-mode=1 data-date-autoclose="true" id="filterPaySlipMonth" data-orientation="top" autocomplete="off">
			</div>
			
			<!--Filter For Employee Share Amount -->
			<div class="form-group">
				<label>Employee Share Amount</label>
				<div class="input-group">
					<input type="number" class="form-control" name="employeeShareAmountStart" id="filterempShareAmountStart" min="0" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="form-control" name="employeeShareAmountEnd" id="filterempShareAmountEnd" min="0" placeholder="End"/>
				</div>
			</div>
			
			<!--Filter For Organization Share Amount -->
			<div class="form-group">
				<label>Organization Share Amount</label>
				<div class="input-group">
					<input type="number" class="form-control" name="OrgShareAmountStart" id="filterOrgShareAmountStart" min="0" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="form-control" name="OrgShareAmountEnd" id="filterOrgShareAmountEnd" min="0" placeholder="End"/>
				</div>
			</div>
			
			<!--Filter For Total Amount -->
			<div class="form-group">
				<label>Total Amount</label>
				<div class="input-group">
					<input type="number" class="form-control" name="TotalAmountStart" id="filterTotalAmountStart" min="0" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="form-control" name="TotalAmountEnd" id="filterTotalAmountEnd" min="0" placeholder="End"/>
				</div>
			</div>
			
			<!--Filter For Paid Amount -->
			<div class="form-group">
				<label>Paid Amount</label>
				<div class="input-group">
					<input type="number" class="form-control" name="PaidAmountStart" id="filterPaidAmountStart" min="0" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="form-control" name="PaidAmountEnd" id="filterPaidAmountEnd" min="0" placeholder="End"/>
				</div>
			</div>
			
			<!--Filter For Status-->
			<div class="form-group">
				<label>Status</label>
				<select class="form-control" data-search="true" id="filterStatus" >
					<option value="">All</option>
					<option value="Paid" >Paid</option>
					<option value="Partially Paid" >Partially Paid</option>
					<option value="Unpaid" >Unpaid</option>
				</select>
			</div>
			
		</div>
	</div>
</div>

<!-- Form Dirty Confirmation Modal -->
<?php if ( $etfPaymentUser['Update']) { ?>
<div class="modal fade" id="modalDirtyEtfPaymentTracker" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditConfFinalSettlement"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfFinalSettlement">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseConfirmEtfPaymentTracker">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php }} if($etfUser['View'] !=1 && $etfPaymentUser['View'] !=1) { ?>

<div class="col-md-12 portlets add-panel-padding">
	<div class="txt_center">Sorry, Access Denied...</div>
</div>

<?php } ?>