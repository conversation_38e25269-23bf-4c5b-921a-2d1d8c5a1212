<?php
//=========================================================================================
//=========================================================================================
/* Program        : Bootstrap.php	 													 *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                       *
 * Description    :  Database Connectivity for subdomain and session                     *
 * handling in a datatable      														 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions      :                                                                    	 *
 *   Version          Date           Author                  Description                 *
 *     0.1        30-May-2013      Narmadha                 Initial Version              *
 *                                                                                    	 *
 *     1.0        02-Feb-2015     Prasanth             Changed in file for mobile app    *
 *                                                                                    	 */
//=========================================================================================
//=========================================================================================
class Bootstrap extends Zend_Application_Bootstrap_Bootstrap
{
	protected $_secretManagerValues = null;
	protected $_appmanagerDbConnection = null;

	public function _initSetup()
	{
		$this->bootstrap('session');
		
		$confMobileApps = $this->getOption('mobileapps');

		require_once APPLICATION_PATH."/../vendor/autoload.php";

		//retrive rds database, S3 bucket and Firebase configs secretkey and accesskey
		$secretManagerValues    = $this->getSecretManagerDetails($confMobileApps['smregion'],$confMobileApps['secretname']);
		$this->_secretManagerValues = $secretManagerValues;

		if(isset($secretManagerValues['entomo_clientid'])){
			Zend_Registry::set('EntomoClientId', $this->_secretManagerValues['entomo_clientid']);
		}
		else
		{
			Zend_Registry::set('EntomoClientId', '');
		}
		
		if(isset($secretManagerValues['entomo_clientsecret'])){
			Zend_Registry::set('EntomoClientSecret', $this->_secretManagerValues['entomo_clientsecret']);
		}
		else
		{
			Zend_Registry::set('EntomoClientSecret', '');
		}
		
		Zend_Registry::set('Production', $confMobileApps['production']);
		Zend_Registry::set('Domain', $confMobileApps['domain']);

		Zend_Registry::set('ProductLogo', $confMobileApps['productlogo']);
		Zend_Registry::set('RedirectionUrl', $confMobileApps['redirectionurl']);
		
        Zend_Registry::set('TZDate', $confMobileApps['production']);
        Zend_Registry::set('TZTime', $confMobileApps['production']);
		
		Zend_Registry::set('region', $confMobileApps['region']);
		Zend_Registry::set('bucketName', $confMobileApps['bucketName']);

		Zend_Registry::set('key1', $secretManagerValues['accesskey']);
		Zend_Registry::set('key2', $secretManagerValues['secretkey']);

		if(isset($secretManagerValues['google_coordinates_address_apikey']))
		{
			Zend_Registry::set('googleMapAPIKey', $secretManagerValues['google_map_api_key']);
		}
		else
		{
			Zend_Registry::set('googleMapAPIKey', '');
		}

		if(isset($secretManagerValues['google_coordinates_address_apikey']))
		{
			Zend_Registry::set('googleAddressAPIKey', $secretManagerValues['google_coordinates_address_apikey']);
		}
		else
		{
			Zend_Registry::set('googleAddressAPIKey', '');
		}

		//SMTP credentials
		Zend_Registry::set('smtpHostName', $secretManagerValues['smtpHostName']);
		Zend_Registry::set('mailUserName', $secretManagerValues['mailUserName']);
		Zend_Registry::set('mailPassword', $secretManagerValues['mailPassword']);

		Zend_Registry::set('version', $confMobileApps['version']);
		Zend_Registry::set('imageBucket', $confMobileApps['imageBucket']);        
		Zend_Registry::set('logoBucket', $confMobileApps['logoBucket']);        
		Zend_Registry::set('signedURLValidity', $confMobileApps['signedURLValidity']);        
		Zend_Registry::set('reimbursementSignedURLValidity', $confMobileApps['reimbursementSignedURLValidity']);        

		Zend_Registry::set('ocrapiurlprefix', $confMobileApps['ocrapiurlprefix']);

		Zend_Registry::set('iciciApiBaseUrl', $confMobileApps['iciciApiBaseUrl']);

		Zend_Registry::set('iciciCIBBaseURL', $confMobileApps['iciciCIBBaseURL']);

		Zend_Registry::set('ccAvenueWorkingKey', $confMobileApps['ccAvenueWorkingKey']);

		Zend_Registry::set('ccAvenueAccessCode', $confMobileApps['ccAvenueAccessCode']);

		Zend_Registry::set('ccAvenueURL', $confMobileApps['ccAvenueURL']);

		Zend_Registry::set('facebookURL', $confMobileApps['facebookURL']);

		Zend_Registry::set('twitterURL', $confMobileApps['twitterURL']);

		Zend_Registry::set('linkedinURL', $confMobileApps['linkedinURL']);

		Zend_Registry::set('googleURL', $confMobileApps['googleURL']);

		Zend_Registry::set('websiteURL', $confMobileApps['websiteURL']);

		Zend_Registry::set('atsBaseURL',$confMobileApps['atsBaseURL']);

		Zend_Registry::set('integrationBaseURL',$confMobileApps['integrationBaseURL']);

		Zend_Registry::set('integrationWoExternalBaseURL',$confMobileApps['integrationWoExternalBaseURL']);

		Zend_Registry::set('trstscoreBaseURL',$confMobileApps['trstscoreBaseURL']);

		Zend_Registry::set('coreHrRoBaseUrl',$confMobileApps['coreHrRoBaseUrl']);

		Zend_Registry::set('coreHrWoBaseUrl',$confMobileApps['coreHrWoBaseUrl']);

		Zend_Registry::set('hrappBeRoBaseUrl',$confMobileApps['hrappBeRoBaseUrl']);

		Zend_Registry::set('payrollAdminWoBaseUrl',$confMobileApps['payrollAdminWoBaseUrl']);

		Zend_Registry::set('employeeSelfServiceRoBaseURL',$confMobileApps['employeeSelfServiceRoBaseURL']);

		Zend_Registry::set('batchProcessingExternalBaseURL',$confMobileApps['batchProcessingExternalBaseURL']);

		Zend_Registry::set('employeeSelfServiceWoBaseURL',$confMobileApps['employeeSelfServiceWoBaseURL']);

		Zend_Registry::set('workflowEngineInitiateBaseUrl',$confMobileApps['workflowEngineInitiateBaseUrl']);
		
		Zend_Registry::set('clientipUrl', $confMobileApps['clientipUrl']);
				
		Zend_Registry::set('signedURLValidity', $confMobileApps['signedURLValidity']);
		Zend_Registry::set('appSecretKey', $confMobileApps['appSecretKey']); 
		Zend_Registry::set('appVersion', $confMobileApps['appVersion']); 
		Zend_Registry::set('refreshTokenAPIUrl', $confMobileApps['refreshTokenAPIUrl']); 
		
		Zend_Registry::set('entomoAccessTokenUrl', $confMobileApps['entomoAccessTokenUrl']); 
		Zend_Registry::set('entomoLoginUrl', $confMobileApps['entomoLoginUrl']); 
		Zend_Registry::set('entomoRefreshTokenUrl', $confMobileApps['entomoRefreshTokenUrl']); 

		require_once APPLICATION_PATH."/validations/Mobile_Detect.php";

		$detect = new Mobile_Detect();
		
		$device = ($detect->isMobile()) ? (($detect->isTablet()) ? 'Tablet' : 'Mobile') : 'Desktop';
		
		Zend_Registry::set('DeviceType', $device);
	}

	public function getSecretManagerDetails($region,$secretName)
	{
		$client = new Aws\SecretsManager\SecretsManagerClient([
			//	'profile' => 'default',
				'version' => '2017-10-17',
				'region' => $region
			]);
		
	
			try {
				$result = $client->getSecretValue([
					'SecretId' => $secretName,
				]);
			} catch (Aws\Exception\AwsException $e) {
				$error = $e->getAwsErrorCode();
				if ($error == 'DecryptionFailureException') {
					// Secrets Manager can't decrypt the protected secret text using the provided KMS key.
					// Deal with the exception here, and/or rethrow at your discretion.
					throw $e;
				}
				if ($error == 'InternalServiceErrorException') {
					// An error occurred on the server side.
					// Deal with the exception here, and/or rethrow at your discretion.
					throw $e;
				}
				if ($error == 'InvalidParameterException') {
					// You provided an invalid value for a parameter.
					// Deal with the exception here, and/or rethrow at your discretion.
					throw $e;
				}
				if ($error == 'InvalidRequestException') {
					// You provided a parameter value that is not valid for the current state of the resource.
					// Deal with the exception here, and/or rethrow at your discretion.
					throw $e;
				}
				if ($error == 'ResourceNotFoundException') {
					// We can't find the resource that you asked for.
					// Deal with the exception here, and/or rethrow at your discretion.
					throw $e;
				}
			}
			// Decrypts secret using the associated KMS CMK.
			// Depending on whether the secret is a string or binary, one of these fields will be populated.
			if (isset($result['SecretString'])) 
			{
				$secret = $result['SecretString'];
			} 
			else 
			{
				$secret = base64_decode($result['SecretBinary']);
			}
			
			$arr = json_decode(($secret));

			$secretManagerValues = json_decode(json_encode($arr), True);
			
			return $secretManagerValues;
	}
	
	protected function _initDatabase ()
	{
		$confMobileApps = $this->getOption('mobileapps');
		$isDomain     = Zend_Registry::get('Domain');
		$isProduction = Zend_Registry::get('Production');

		$redirectionurl = Zend_Registry::get('RedirectionUrl');
		$isDomainArray = explode(".",$isDomain);
		try{
				$appDb = $this->getPluginResource('db');
				$appDb->init();
				$dbParams = $appDb->getParams();
				$dbParams['host']   	= $this->_secretManagerValues['sa_hostname'];
				$dbParams['username']   = $this->_secretManagerValues['sa_username'];
				$dbParams['password']   = $this->_secretManagerValues['sa_password'];
				require_once APPLICATION_PATH."/../vendor/autoload.php";
				$appmanagerDbConnection = Zend_Db::factory('PDO_MYSQL', $dbParams);
				$ehrTables = new Application_Model_DbTable_Ehr();
				$orgCode 				= $ehrTables->getOrgCode();
				$registerUserDetails 	= $appmanagerDbConnection->fetchRow($appmanagerDbConnection->select()->from($ehrTables->regUser, array('Partner_Integration','Data_Region','Bucket_Region'))
																 ->where('Org_Code = ?', $orgCode));
					//Check the partner integration is not empty and not null
				if(!is_null($registerUserDetails['Partner_Integration']) && !empty($registerUserDetails['Partner_Integration'])){
					$partnerid = $registerUserDetails['Partner_Integration'];
				}else{
					$partnerid = '-';
				}

				//Set the partner integration in the zend registry
				Zend_Registry::set('partnerid', $partnerid);
				//Set the firebase credentials based on the project
				if($partnerid === 'trulead'){
					$firebaseData = array();
					$encodePvtKey = json_encode($this->_secretManagerValues['trulead_firebase_privatekey']);
					$firebaseTruleadPvtKey = json_decode(stripslashes($encodePvtKey));

					$firebaseData['type'] = $this->_secretManagerValues['trulead_type'];
					$firebaseData['private_key'] = $firebaseTruleadPvtKey;
					$firebaseData['client_email'] = $this->_secretManagerValues['trulead_firebase_client_email'];
					$firebaseData['client_id'] = $this->_secretManagerValues['trulead_firebase_client_id'];
					$firebaseData['project_id'] = $this->_secretManagerValues['trulead_firebase_project_id'];
					$firebaseData['private_key_id'] = $this->_secretManagerValues['trulead_firebase_private_key_id'];

					file_put_contents('../application/trulead_firebase_credentials.json', json_encode($firebaseData));

					Zend_Registry::set('firebaseApiKey', $confMobileApps['truleadFirebaseApiKey']);    
					Zend_Registry::set('firebaseAuthDomain', $confMobileApps['truleadFirebaseAuthDomain']);    
					Zend_Registry::set('firebaseDatabaseURL', $confMobileApps['truleadFirebaseDatabaseURL']);    
					Zend_Registry::set('firebaseProjectId', $confMobileApps['truleadFirebaseProjectId']);    
					Zend_Registry::set('firebaseStorageBucket', $confMobileApps['truleadFirebaseStorageBucket']);    
					Zend_Registry::set('firebaseMessagingSenderId', $confMobileApps['truleadFirebaseMessagingSenderId']);    
					Zend_Registry::set('firebaseAppId', $confMobileApps['truleadFirebaseAppId']);
				}else{
					$firebaseData = array();
					$encodePvtKey = json_encode($this->_secretManagerValues['firebase_privatekey']);
					$firebasePvtKey = json_decode(stripslashes($encodePvtKey));
					
					$firebaseData['type'] = 'service_account';
					$firebaseData['private_key'] = ($firebasePvtKey);
					$firebaseData['client_email'] = $this->_secretManagerValues['firebase_client_email'];
					$firebaseData['client_id'] = $this->_secretManagerValues['firebase_client_id'];
					$firebaseData['project_id'] = $this->_secretManagerValues['firebase_project_id'];
					$firebaseData['private_key_id'] = $this->_secretManagerValues['firebase_private_key_id'];

					file_put_contents('../application/firebase_credentials.json', json_encode($firebaseData));
	
					Zend_Registry::set('firebaseApiKey', $confMobileApps['firebaseApiKey']);    
					Zend_Registry::set('firebaseAuthDomain', $confMobileApps['firebaseAuthDomain']);    
					Zend_Registry::set('firebaseDatabaseURL', $confMobileApps['firebaseDatabaseURL']);    
					Zend_Registry::set('firebaseProjectId', $confMobileApps['firebaseProjectId']);    
					Zend_Registry::set('firebaseStorageBucket', $confMobileApps['firebaseStorageBucket']);    
					Zend_Registry::set('firebaseMessagingSenderId', $confMobileApps['firebaseMessagingSenderId']);    
					Zend_Registry::set('firebaseAppId', $confMobileApps['firebaseAppId']);
				}
				
				if(!is_null($registerUserDetails['Data_Region']) && !empty($registerUserDetails['Data_Region'])){
					$dataRegion 			= $registerUserDetails['Data_Region'];
					$dbParams['host']   	= $this->_secretManagerValues[$dataRegion.'_'.'hostname'];
					$dbParams['username']   = $this->_secretManagerValues[$dataRegion.'_'.'username'];
					$dbParams['password']   = $this->_secretManagerValues[$dataRegion.'_'.'password'];
					$dbParams['dbname']     = '';
				}else{
					$dataRegion = '-';
				}

				if(!is_null($registerUserDetails['Bucket_Region']) && !empty($registerUserDetails['Bucket_Region'])){
					$bucketRegion 			= $registerUserDetails['Bucket_Region'];
				}else{
					$bucketRegion 			= '-';
				}
				Zend_Registry::set('d_code', $dataRegion);
				Zend_Registry::set('b_code', $bucketRegion);
				$clientDbConnection = null;
				try {
					$clientDbConnection = Zend_Db::factory('PDO_MYSQL', $dbParams);
					$dbExists = $clientDbConnection->fetchOne($clientDbConnection->select()->from('INFORMATION_SCHEMA.SCHEMATA', 'SCHEMA_NAME')->where('SCHEMA_NAME = ?', $isDomainArray[0].'_'.$orgCode));

					if ($orgCode!='secure' && !empty($dbExists))
					{
						$params = array('host'       => $dbParams['host'],
										'username'   => $dbParams['username'],
										'password'   => $dbParams['password'],
										'encrypt'	 => $dbParams['encrypt'],
										'charset'	 => $dbParams['charset'],
	                                    'port'       => $dbParams['port'],
										'dbname'     => $isDomainArray[0].'_'.$orgCode
										);
						Zend_Registry::set('dbUserName', $dbParams['username']);
						Zend_Registry::set('dbPassword', $dbParams['password']);
						Zend_Registry::set('dbHost', $dbParams['host']);
						Zend_Registry::set('dbPort', $dbParams['port']);
						Zend_Registry::set('dbName', $isDomainArray[0].'_'.$orgCode);

						try{
							$orgDbConn = Zend_Db::factory('PDO_MYSQL', $params);

							/*when the subHrapp is not added in zend registry in that time only we need to add that in zend registry*/
							if (!Zend_Registry::isRegistered('subHrapp'))
							Zend_Registry::set('subHrapp', $orgDbConn);

							$orgDetails = $orgDbConn->fetchRow($orgDbConn->select()->from($ehrTables->orgDetails, '*')
	                                                                                 ->where('Org_Code = ?', $orgCode));

							/*when the orgDetails is not added in zend registry in that time only we need to add that in zend registry*/
							if (!Zend_Registry::isRegistered('orgDetails'))
							Zend_Registry::set('orgDetails', $orgDetails);
						}catch(PDOException $pdoEx)
						{
							$front = Zend_Controller_Front::getInstance();
							$response = new Zend_Controller_Response_Http();
							$response->setRedirect($redirectionurl);
							$front->setResponse($response);
						}
					}
					else if($orgCode=='secure')
					{
						if(isset($_SERVER['HTTP_REFERER']))
						{
								$subdomain = $ehrTables->getSubDomain();

								if($subdomain !== "nosubdomain")
								{
									$params = array('host'       => $dbParams['host'],
													'username'   => $dbParams['username'],
													'password'   => $dbParams['password'],
													'encrypt'	 => $dbParams['encrypt'],
													'charset'	 => $dbParams['charset'],
													'port'       => $dbParams['port'],
													'dbname'     => $isDomainArray[0].'_'.$subdomain
													);

									try{
										$orgDbConn = Zend_Db::factory('PDO_MYSQL', $params);
										Zend_Registry::set('subHrapp', $orgDbConn);
										$orgDetails = $orgDbConn->fetchRow($orgDbConn->select()->from($ehrTables->orgDetails, '*')
																		->where('Org_Code = ?', $subdomain));
										Zend_Registry::set('orgDetails', $orgDetails);
									}catch(PDOException $pdoEx)
									{
										$front = Zend_Controller_Front::getInstance();
										$response = new Zend_Controller_Response_Http();
										$response->setRedirect($redirectionurl);
										$front->setResponse($response);
									}
								}
						}
						else
						{
							$front = Zend_Controller_Front::getInstance();
							$response = new Zend_Controller_Response_Http();
							$response->setRedirect($redirectionurl);
							$front->setResponse($response);
						}
					}
					else
					{
						$front = Zend_Controller_Front::getInstance();
		            	$response = new Zend_Controller_Response_Http();
		            	$response->setRedirect($redirectionurl);
		            	$front->setResponse($response);
					}
				} catch(Exception $ex) {
					// Log the error and redirect
					error_log('Bootstrap database connection error: ' . $ex->getMessage());
					$front = Zend_Controller_Front::getInstance();
					$response = new Zend_Controller_Response_Http();
					$response->setRedirect($redirectionurl);
					$front->setResponse($response);
				} finally {
					// Clean up the temporary client connection
					if ($clientDbConnection) {
						try {
							$clientDbConnection->closeConnection();
						} catch(Exception $closeEx) {
							// Log but don't throw - we're cleaning up
							error_log('Error closing client database connection: ' . $closeEx->getMessage());
						}
						$clientDbConnection = null;
					}
				}
		}catch(Exception $innerEx){
			// Log inner bootstrap errors
			error_log('Inner bootstrap error: ' . $innerEx->getMessage());
		}

		// Store connection as instance variable for proper cleanup and set in registry
		if (isset($appmanagerDbConnection) && $appmanagerDbConnection) {
			$this->_appmanagerDbConnection = $appmanagerDbConnection;
			Zend_Registry::set('Hrapp', $appmanagerDbConnection);

			// Register shutdown function to ensure cleanup happens
			register_shutdown_function(array($this, 'cleanup'));
		} else {
			error_log('Bootstrap: App manager database connection is not properly initialized');
			// Set null in registry to prevent errors
			Zend_Registry::set('Hrapp', null);
		}

		if ($orgCode!='secure')
		{
			if (Zend_Registry::isRegistered('subHrapp'))
			{
				try{
					//start your session!
					Zend_Session::start();
				}
				catch (Zend_Session_Exception $e) {
					session_regenerate_id(true);
				}
				Zend_Session::registerValidator(new Zend_Session_Validator_HttpUserAgent());
			}
		}
}
	
	protected function _initTimezone ()
	{
		if(isset($_COOKIE['empUid']))
		{
			$ehrTables = new Application_Model_DbTable_Ehr();
			$loginEmpUserId = $ehrTables->employeeIdByUid($_COOKIE['empUid']);
			$tz = $ehrTables->loggedEmpTimezone($loginEmpUserId); 
		}
		
		if(!empty($tz))
		{
			date_default_timezone_set($tz);
		}
		else
		{
			date_default_timezone_set('Asia/Kolkata');
		}
	}

	/**
	 * Close the app manager database connection with proper error handling
	 * This is a centralized method to avoid code duplication
	 *
	 * @param string $context Context information for logging (e.g., 'cleanup', 'destructor')
	 */
	private function _closeAppManagerConnection($context = '')
	{
		if ($this->_appmanagerDbConnection) {
			try {
				$this->_appmanagerDbConnection->closeConnection();
				$logContext = !empty($context) ? " during {$context}" : '';
				error_log("Bootstrap: App manager database connection closed successfully{$logContext}");
			} catch(Exception $closeEx) {
				// Log but don't throw - we're cleaning up
				$logContext = !empty($context) ? " during {$context}" : '';
				error_log("Bootstrap: Error closing app manager database connection{$logContext} - " . $closeEx->getMessage());
			}
			$this->_appmanagerDbConnection = null;
		}
	}

	/**
	 * Clean up database connections and resources
	 * This method can be called explicitly during application shutdown
	 */
	public function cleanup()
	{
		// Clean up the app manager database connection
		$this->_closeAppManagerConnection('cleanup');

		// Remove from registry to prevent further usage
		if (Zend_Registry::isRegistered('Hrapp')) {
			Zend_Registry::set('Hrapp', null);
		}
	}

	public function __destruct()
    {
        // Clean up the app manager database connection
        $this->_closeAppManagerConnection('destructor');
    }
}
