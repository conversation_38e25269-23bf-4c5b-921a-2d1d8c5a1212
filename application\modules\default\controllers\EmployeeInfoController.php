<?php
//=========================================================================================
//=========================================================================================
/* Program : EmployeeInfoController.php											         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : List all the employee job information, manager job information in       *
 * popup window to select particular employee and will show particular                   *
 * employee job details in the tooltip.								                     *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Narmadha                Initial Version         	         *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Prasanth                Changes in file for mobiles app  	 *
 *  												  Added actions :                    *
 *                                                    1.payrollEmployeeAction()          *
 *                                                    2.showPayrollEmployeeAction()      *
 *                                                    3.clearUserSessionAction()		 *
 *                                                    4.managerCountAction()             *
 *                                                    5.alternateEmployeeAction()        *
 *                                                    6.checkHrappuserAction             *
 *                                                    7.orgDateFormatAction              *
 *                                                    9.getTouchComboValuesAction        *
 *                                                    Modified actions :                 *
 *                                                    1.isMobile parameter is added in   *
 *                                                    some actions.                      */
//=========================================================================================
//=========================================================================================

include APPLICATION_PATH."/validations/Validations.php";

class Default_EmployeeInfoController extends Zend_Controller_Action
{
	protected $_validation          = null;
    protected $_dbJobDetail = null;
    protected $_dbPersonal = null;
    protected $_dbManager = null;
    protected $_dbAccessRights = null;
    protected $_logEmpId = null;
    protected $_dbComment = null;
    protected $_ehrTables = null;
    protected $_url = null;
    protected $_dbCommonFun         = null;
	protected $_dbOrgSettings  	    = null;
	protected $_dbPayslip   = null;
    protected $_hrappMobile = null;
        
    public function init()
    {
        $this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
        // if ($this->_hrappMobile->checkAuth())
        // {
			$this->_validation 	        = new Validations();
			$this->_dbCommonFun    = new Application_Model_DbTable_CommonFunction();
       	    $this->_url = new Zend_View_Helper_Url();
       	    $this->_dbJobDetail = new Employees_Model_DbTable_JobDetail();
       	    $this->_dbPersonal = new Employees_Model_DbTable_Personal();
       	    $this->_dbManager = new Default_Model_DbTable_Manager();
       	    $this->_dbAccessRights = new Default_Model_DbTable_AccessRights();
       	    $this->_dbComment = new Payroll_Model_DbTable_PayrollComment();
       	    $this->_ehrTables = new Application_Model_DbTable_Ehr();
       	    $this->_dbAdvSalary = new Payroll_Model_DbTable_AdvanceSalary();
			$this->_dbDeductions = new Payroll_Model_DbTable_Deductions();
			$this->_dbLoan = new Payroll_Model_DbTable_Loan();
			$this->_dbOrgSettings       = new Organization_Model_DbTable_OrgSettings();
			$this->_dbPayslip = new Payroll_Model_DbTable_Payslip ();
			
			$userSession                = $this->_dbCommonFun->getUserDetails ();
            $this->_logEmpId            = $userSession?$userSession['logUserId']:1;
			//$this->_dbAccessRights->refreshUserSessionTimestamp($this->_logEmpId);
       	// }
       	// else
       	// {
        //     //if the request comes from vue app then on session expire we should not redirect to auth
		// 	// instead we need to send session expired message in error response
		// 	//in header if the requestResource is HRAPPUI consider it comes from vue app
		// 	//as of now, for getConsiderationDateAction session handled 
		// 	//inside actions because not able to return response inside init function

        //     $formData = array();
        //     $requestData =$this->getRequest()->getRawBody(); 
		// 	if ($requestData)
		// 	{
		// 		$formData = Zend_Json::decode($requestData);
		// 	}

		// 	//if its not from vue app on session expire redirect to auth
        //     if(empty($formData)|| (!empty($formData) && $formData['requestResource'] !== 'HRAPPUI')){
		// 		$this->_redirect('auth');
        //     }
       	// }
    }
	
    /**
     * to show the employee's information such as employee type, department,
     * designation,
     * contact details and mail id
     */
    public function indexAction()
    {
        $checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

        if ($checkSessionAuth)
		{
			$this->_helper->layout()->disableLayout('layout');
			
			if (isset($_SERVER['HTTP_REFERER']))
			{
				$ajaxContext = $this->_helper->getHelper('AjaxContext');
				$ajaxContext->addActionContext('index', 'json')->initContext();
				
				$empId = $this->_getParam('empId', null);
				
				if (!empty($empId))
				{
					$empId = filter_var($empId, FILTER_SANITIZE_NUMBER_INT);
					$this->view->empInfo = $this->_dbJobDetail->empJobInfo($empId);
				}
			}
			else
			{
				$this->_redirect('');
			}
		} else {
			$this->_redirect('auth');
		}
    }

    /**
     * form to choose manager from the popup
     */
    public function managerInfoAction()
    {
        $layout = $this->_helper->layout();
        $layout->disableLayout('layout');
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $layout->setLayout('tab_layout');
			
            $managerForm = new Default_Form_Manager();
			
			$this->view->fwdForm = $managerForm;
        }
        else
        {
            $this->_redirect('');
        }
    }

    /**
     * get all the manager details in the grid
     */
    public function showManagerAction()
    {
        $layout = $this->_helper->layout();
        $layout->disableLayout();
        
		$ajaxContext = $this->_helper->getHelper('AjaxContext');
        $ajaxContext->addActionContext('show-manager', 'json')->initContext();
		
        if (isset($_SERVER['HTTP_REFERER']))
		{
			$empId = $this->_getParam('EId', null);
			$empId = filter_var($empId, FILTER_SANITIZE_NUMBER_INT);
			//$mPage = $this->_getParam('page', null);
			//$mRows = $this->_getParam('rows', null);
			//$mPage = isset ( $mPage ) ? intval ( $mPage ) : 1;
			//$mRows = isset ( $mRows ) ? intval ( $mRows ) : 10;
			//$sortField = $this->_getParam('sort', null);
			//$sortOrder = $this->_getParam('order', null);
			//$sortField = isset ( $sortField ) ? $sortField : 'Emp_First_Name';
			//$sortOrder = isset ( $sortOrder ) ? $sortOrder :  'ASC';
			//$mFirst = $this->_getParam('first_name', null);
			//$mLast = $this->_getParam('last_name', null);
			//$mDept = $this->_getParam('m_dept', null);
			//$mMail = $this->_getParam('m_mail', null);
			//$mDesignation = $this->_getParam('m_design',null);
			//$formName = $this->_getParam('_fName',null);
			//$formName = filter_var($formName, FILTER_SANITIZE_STRIPPED);
			//$sortField = filter_var($sortField, FILTER_SANITIZE_STRIPPED);
			//$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
			//$mPage = filter_var($mPage, FILTER_SANITIZE_NUMBER_INT);
			//$mRows = filter_var($mRows, FILTER_SANITIZE_NUMBER_INT);
			//$mFirst = filter_var($mFirst, FILTER_SANITIZE_STRIPPED);
			//$mLast = filter_var($mLast, FILTER_SANITIZE_STRIPPED);
			//$mDesignation = filter_var($mDesignation, FILTER_SANITIZE_STRIPPED);
			//$mDept = filter_var($mDept, FILTER_SANITIZE_STRIPPED);
			//$mMail = filter_var($mMail, FILTER_SANITIZE_EMAIL);
            
            $this->view->result = $this->_dbManager->managerName('', '', 'Emp_First_Name', 'ASC', '', '', '', '', '', 'Employees', $empId);
			//$this->view->getManager = $this->_dbManager->managerName($mPage, $mRows, $sortField, $sortOrder, $mFirst, $mLast, $mDept, $mMail,
			//$mDesignation, $formName, $empId);
		}
        else
        {
            $this->_redirect('');
        }
    }

    /**
     * form to choose employee from the popup
     */
    public function empInfoAction()
    {
        $layout = $this->_helper->layout();
        $layout->disableLayout('layout');
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $layout->setLayout('tab_layout');
            
			$formName = $this->_getParam('_fId', null);
			$this->view->formName = $formName;
			
			$employeeForm = new Default_Form_Manager();
			$this->view->empForm = $employeeForm;
        }
        else
        {
            $this->_redirect('');
        }
    }

    /**
     * get all the employee details in the grid
     */
    public function showEmpInfoAction()
    {
        if (isset($_SERVER['HTTP_REFERER']))
        {
            if (1==1)
            {
                $layout = $this->_helper->layout();
                $layout->disableLayout();
				
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
                $ajaxContext->addActionContext('show-emp-info', 'json')->initContext();
				
                $formName = $this->_getParam('_fId', null);
                $formName = filter_var($formName, FILTER_SANITIZE_STRIPPED);
                $manager = $this->_getParam('_M', null);
                $manager = filter_var($manager, FILTER_SANITIZE_NUMBER_INT);
                
                $adminAccess = '';
                $isManager = '';
                
                if (!empty($formName) && $formName!='Employee Roles')
                {
                	//if( !empty($manager) && $formName == 'Bonus')
					//if ((!empty($manager) && $formName == 'Bonus') || (!empty($manager) && $formName == 'Assignments'))
					if (!empty($manager) && $formName == 'Assignments')
					{
                		$isManager=$manager;
					}
                	else
                	{
                		$accessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $formName);
						$adminAccess = $accessRights['Admin'];
                		$isManager = $accessRights['Employee']['Is_Manager'];
                	}
                }
                
				$punchInDate = $this->_getParam('_punchInDate', null);
                $punchInDate = filter_var($punchInDate, FILTER_SANITIZE_STRIPPED);
                $empPage = $this->_getParam('page', null);
                $empRows = $this->_getParam('rows', null);
                $empPage = isset( $empPage ) ? intval ( $empPage ) : 1;
                $empRows = isset( $empRows ) ? intval ( $empRows ) : 10;
                $sortField = $this->_getParam('sort', null);
                $sortOrder = $this->_getParam('order', null);
                $sortField = isset( $sortField ) ? $sortField : 'Emp_First_Name';
                $sortOrder = isset( $sortOrder ) ? $sortOrder : 'ASC';
                $empFirst = $this->_getParam('first_name', null);
                $empLast = $this->_getParam('last_name', null);
                $empDept = $this->_getParam('m_dept', null);
                $empMail = $this->_getParam('m_mail', null);
                $mDesignation = $this->_getParam('m_design',null);
				$mWorkschedule = '';
				if($formName == 'Bulk Attendance Update')
				{
					$mWorkschedule = $this->_getParam('m_workschedule',null);	
				}
				$complaintFromId='';
				if($formName=='Complaints')
				{
				    $complaintFromId = $this->_getParam('EId',null);
				}
                $sortField = filter_var($sortField, FILTER_SANITIZE_STRIPPED);
                $sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
                $empPage = filter_var($empPage, FILTER_SANITIZE_NUMBER_INT);
                $empRows = filter_var($empRows, FILTER_SANITIZE_NUMBER_INT);
                $empFirst = filter_var($empFirst, FILTER_SANITIZE_STRIPPED);
                $empLast = filter_var($empLast, FILTER_SANITIZE_STRIPPED);
                $mDesignation = filter_var($mDesignation, FILTER_SANITIZE_STRIPPED);
                $empDept = filter_var($empDept, FILTER_SANITIZE_STRIPPED);
                $empMail = filter_var($empMail, FILTER_SANITIZE_EMAIL);
				
                $this->view->employeeInfo = $this->_dbPersonal->employeeDetail($empPage, $empRows, $sortField, $sortOrder, $empFirst,
																			   $empLast,$empDept, $empMail, $this->_logEmpId, $mDesignation,
																			   $mWorkschedule, $adminAccess, $isManager, $formName,
																			   $punchInDate,$this->_isMobile,'',$complaintFromId);
            }
        }
        else
        {
            $this->_redirect('');
        }
    }

    /**
     * form to choose upline manager from the popup
     */
    public function srManagerInfoAction()
    {
        $layout = $this->_helper->layout();
        $layout->disableLayout('layout');
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $layout->setLayout('tab_layout');
            
			$srManagerForm = new Default_Form_Manager();
			$srManagerForm->setAttrib('id', 'Sr_ManagerForm');
			$this->view->srMangerForm = $srManagerForm;
        }
        else
        {
            $this->_redirect('');
        }
    }

    /**
     * get all the upline manager in the grid
     */
    public function showSrManagerInfoAction()
    {
        $layout = $this->_helper->layout();
        $layout->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
            if (1==1)
            {
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
                $ajaxContext->addActionContext('show-sr-manager-info', 'json')->initContext();
				
                $empId = $this->_getParam('EId', null);
                $mPage = $this->_getParam('page', null);
                $mRows = $this->_getParam('rows', null);
                $mPage = isset ( $mPage ) ? intval ( $mPage ) : 1;
                $mRows = isset ( $mRows ) ? intval ( $mRows ) : 10;
                $sortField = $this->_getParam('sort', null);
                $sortOrder = $this->_getParam('order', null);
                $sortField = isset ( $sortField ) ? $sortField : 'Emp_First_Name';
                $sortOrder = isset ( $sortOrder ) ? $sortOrder :  'ASC';
                $mFirst = $this->_getParam('first_name', null);
                $mLast = $this->_getParam('last_name', null);
                $mDept = $this->_getParam('m_dept', null);
                $mDesignation = $this->_getParam('m_design',null);
                $mMail = $this->_getParam('m_mail', null);
                $jobIsManager = $this->_dbJobDetail->isManager($this->_logEmpId);
                $empId = filter_var($empId, FILTER_SANITIZE_NUMBER_INT);
                $sortField = filter_var($sortField, FILTER_SANITIZE_STRIPPED);
                $sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
                $mPage = filter_var($mPage, FILTER_SANITIZE_NUMBER_INT);
                $mRows = filter_var($mRows, FILTER_SANITIZE_NUMBER_INT);
                $mFirst = filter_var($mFirst, FILTER_SANITIZE_STRIPPED);
                $mLast = filter_var($mLast, FILTER_SANITIZE_STRIPPED);
                $mDesignation = filter_var($mDesignation, FILTER_SANITIZE_STRIPPED);
                $mDept = filter_var($mDept, FILTER_SANITIZE_STRIPPED);
                $mMail = filter_var($mMail, FILTER_SANITIZE_EMAIL);

                $this->view->getSrManager = $this->_dbManager->srManagerName($mPage, $mRows, $sortField, $sortOrder, $mFirst, $mLast, $mDept, $mMail, $mDesignation, $this->_logEmpId, $jobIsManager, $empId, $this->_isMobile);
            }
        }
        else
        {
            $this->_redirect('');
        }
    }

    /**
     * form to choose employee based on salary for bonus form from the popup
     */
    public function salaryEmployeeAction()
    {
        $layout = $this->_helper->layout();
        $layout->disableLayout('layout');
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $layout->setLayout('tab_layout');
			
            if (1==1)
            {
                $employeeForm = new Default_Form_Manager();
                
				$employeeForm->setAttrib('id', 'SalaryEmployee_Form');
				
                $this->view->salaryEmployeeForm = $employeeForm;
            }
        }
        else
        {
            $this->_redirect('');
        }
    }

    /**
     * get all the employees based on salary in the grid
     */
    public function showSalaryEmployeeAction()
    {
        $layout = $this->_helper->layout();
        $layout->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
            if (1==1)
            {
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
                $ajaxContext->addActionContext('show-salary-employee', 'json')->initContext();
				
                $formName = $this->_getParam('_fId', null);
                $manager = $this->_getParam('_M', null);
                $manager = filter_var($manager, FILTER_SANITIZE_NUMBER_INT);
                $empPage = $this->_getParam('page', null);
                $empRows = $this->_getParam('rows', null);
                $empPage = isset( $empPage ) ? intval ( $empPage ) : 1;
                $empRows = isset( $empRows ) ? intval ( $empRows ) : 10;
                $sortField = $this->_getParam('sort', null);
                $sortOrder = $this->_getParam('order', null);
                $sortField = isset( $sortField ) ? $sortField : 'Emp_First_Name';
                $sortOrder = isset( $sortOrder ) ? $sortOrder : 'ASC';
                $empFirst = $this->_getParam('first_name', null);
                $empLast = $this->_getParam('last_name', null);
                $empDept = $this->_getParam('m_dept', null);
                $empMail = $this->_getParam('m_mail', null);
                $mDesignation = $this->_getParam('m_design',null);
                $sortField = filter_var($sortField, FILTER_SANITIZE_STRIPPED);
                $sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
                $empPage = filter_var($empPage, FILTER_SANITIZE_NUMBER_INT);
                $empRows = filter_var($empRows, FILTER_SANITIZE_NUMBER_INT);
                $empFirst = filter_var($empFirst, FILTER_SANITIZE_STRIPPED);
                $empLast = filter_var($empLast, FILTER_SANITIZE_STRIPPED);
                $mDesignation = filter_var($mDesignation, FILTER_SANITIZE_STRIPPED);
                $empDept = filter_var($empDept, FILTER_SANITIZE_STRIPPED);
                $empMail = filter_var($empMail, FILTER_SANITIZE_EMAIL);
                $adminAccess = '';
                $isManager = '';
				
                if (!empty($formName))
                {
                    if (!empty($manager) && $formName == 'Commission')
					{
                        $isManager=$manager;
                    }
                    else
                    {
                        $accessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $formName);
                        $adminAccess = $accessRights['Admin'];
                        $isManager = $accessRights['Employee']['Is_Manager'];
                    }
                }
				
                $this->view->employeeInfo = $this->_dbPersonal->salaryEmployee($empPage, $empRows, $sortField, $sortOrder, $empFirst, $empLast, $empDept, $empMail, $this->_logEmpId, $mDesignation, $adminAccess, $isManager, $formName, $this->_isMobile);
            }
        }
        else
        {
            $this->_redirect('');
        }
    }

	/**
	 * Get payroll employee details to show in popup
	 * to select employee
	 */
    public function payrollEmployeeAction()
    {
        $layout = $this->_helper->layout();
        $layout->disableLayout('layout');
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $layout->setLayout('tab_layout');
			
            $employeeForm = new Default_Form_Manager();
			$this->view->empForm = $employeeForm;
        }
        else
        {
            $this->_redirect('');
        }
    }

	/**
	 * Get payroll employee details to show in popup
	 * to select employee
	 */
    public function showPayrollEmployeeAction()
    {
        $layout = $this->_helper->layout();
        $layout->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
            if(1==1)
            {
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
                $ajaxContext->addActionContext('show-payroll-employee', 'json')->initContext();
				
                $formName = $this->_getParam('_fId', null);
                $empPage = $this->_getParam('page', null);
                $empRows = $this->_getParam('rows', null);
                $empPage = isset( $empPage ) ? intval ( $empPage ) : 1;
                $empRows = isset( $empRows ) ? intval ( $empRows ) : 10;
                $sortField = $this->_getParam('sort', null);
                $sortOrder = $this->_getParam('order', null);
                $sortField = isset( $sortField ) ? $sortField : 'Emp_First_Name';
                $sortOrder = isset( $sortOrder ) ? $sortOrder : 'ASC';
                $empFirst = $this->_getParam('first_name', null);
                $empLast = $this->_getParam('last_name', null);
                $empDept = $this->_getParam('m_dept', null);
                $empMail = $this->_getParam('m_mail', null);
                $mDesignation = $this->_getParam('m_design',null);
                $sortField = filter_var($sortField, FILTER_SANITIZE_STRIPPED);
                $sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
                $empPage = filter_var($empPage, FILTER_SANITIZE_NUMBER_INT);
                $empRows = filter_var($empRows, FILTER_SANITIZE_NUMBER_INT);
                $empFirst = filter_var($empFirst, FILTER_SANITIZE_STRIPPED);
                $empLast = filter_var($empLast, FILTER_SANITIZE_STRIPPED);
                $mDesignation = filter_var($mDesignation, FILTER_SANITIZE_STRIPPED);
                $empDept = filter_var($empDept, FILTER_SANITIZE_STRIPPED);
                $empMail = filter_var($empMail, FILTER_SANITIZE_EMAIL);
                
                if (!empty($formName))
                {
                    $formId = $this->_dbComment->getFormId($formName);
                    
					$this->view->payrollEmpInfo = $this->_dbPersonal->payrollEmpDetail($empPage, $empRows, $sortField, $sortOrder, $empFirst,
																					   $empLast, $empDept, $empMail, $mDesignation, $formId,
																					   $this->_isMobile);
                }
            }
        }
        else
        {
            $this->_redirect('');
        }
    }

    /**
     * Clear user session lock if time expires and clear lock records if user closes
     * the tab
     */
    public function checkSessionAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('check-session', 'json')->initContext();
			
			// clear lock if time expires
			$this->_dbAccessRights->clearTimeoutSession($this->_logEmpId);
			
			// clear lock records if user closes the tab
			$this->view->sessionLock = $this->_dbAccessRights->checkSessionLock($this->_logEmpId);
        }
        else
        {
            $this->_redirect('auth');
        }
    }

    /**
     * Clear lock during form submission
     */
    public function clearSessionAction()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('clear-session', 'json')->initContext();
			
			$title = trim($this->_getParam('title', null));
			$title = filter_var($title, FILTER_SANITIZE_STRIPPED);
			
			$txtValue = trim($this->_getParam('txt', null));
			$txtValue = filter_var($txtValue, FILTER_SANITIZE_STRIPPED);
			
			$uniqueId = trim($this->_getParam('id', null));
			
			switch($title)
			{
				case 'Contact Person':
				case 'Organization Details':
					$uniqueId = filter_var($uniqueId, FILTER_SANITIZE_STRIPPED);
					break;
				
				default:
					$uniqueId = filter_var($uniqueId, FILTER_SANITIZE_NUMBER_INT);
					break;
			}
			
			if (!empty($title))
			{
				switch ($title)
				{
					case 'Location':
						$tableName = $this->_ehrTables->location;
						$fieldName = 'Location_Id';
						break;
					
					case 'Fixed Insurance':
						$tableName = $this->_ehrTables->fixedInsurance;
						$fieldName = 'Insurance_Id';
						break;
					
					case 'Variable Insurance':
						$tableName = $this->_ehrTables->variableInsurance;
						$fieldName = 'Insurance_Id';
						break;
					
					case 'Roles':
						$tableName = $this->_ehrTables->roles;
						$fieldName = 'Designation_Id';
						break;
					
					case 'Employee Travel':
						$tableName = $this->_ehrTables->empTravels;
						$fieldName = 'Request_Id';
						break;
					
					case 'Skillset Assessment':
						$tableName = $this->_ehrTables->skillset;
						$fieldName = 'Skillset_Id';
						break;
					
					case 'Employee Roles':
						$tableName = $this->_ehrTables->empAccessRights;
						$fieldName = 'Employee_Id';
						break;
					
					case 'Employee':
						$tableName = $this->_ehrTables->empPersonal;
						$fieldName = 'Employee_Id';
						break;
					
					case 'Designation':
						$tableName = $this->_ehrTables->designation;
						$fieldName = 'Designation_Id';
						break;
					
					case 'Skill Definition':
						$tableName = $this->_ehrTables->skillDefinition;
						$fieldName = 'SkillDefinition_Id';
						break;
					
					case 'Performance Assessment':
						$tableName = $this->_ehrTables->performance;
						$fieldName = 'Performance_Id';
						break;
					
					case 'Skill Level Association':
						$tableName = $this->_ehrTables->skillLevelAssoc;
						$fieldName = 'SkillDefinition_Id';
						break;
					
					case 'Attendance':
						$tableName = $this->_ehrTables->attendance;
						$fieldName = 'Attendance_Id';
						break;
					
					case 'Bulk Attendance Update':
						$tableName = $this->_ehrTables->attendance;
						$fieldName = 'Attendance_Id';
						break;
					
					case 'Department':
						$tableName = $this->_ehrTables->dept;
						$fieldName = 'Department_Id';
						break;
					
					case 'Work Schedule':
						$tableName = $this->_ehrTables->workSchedule;
						$fieldName = 'Title';
						break;
					
					case 'Timesheet Hour':
						$tableName = $this->_ehrTables->timesheetHrs;
						$fieldName = 'Grade_Id';
						break;
					
					case 'Resignation':
						$tableName = $this->_ehrTables->resignation;
						$fieldName = 'Resignation_Id';
						break;
					
					case 'Timesheet Activity':
						$tableName = $this->_ehrTables->timesheetActivity;
						$fieldName = 'Project_Id';
						break;
					
					case 'Reimbursement':
						$tableName = $this->_ehrTables->reimbursement;
						$fieldName = 'Request_Id';
						break;
					
					case 'Insurance Type':
						$tableName = $this->_ehrTables->insuranceType;
						$fieldName = 'InsuranceType_Id';
						break;
					
					case 'Tax Slab':
						$tableName = $this->_ehrTables->taxRates;
						$fieldName = 'Tax_Rate_Id';
						break;
					
					case 'Tax Section':
						$tableName = $this->_ehrTables->taxSections;
						$fieldName = 'Tax_Section_ID';
						break;
					
					case 'Section Investment':
						$tableName = $this->_ehrTables->sectionsInvestment;
						$fieldName = 'Investment_Cat_Id';
						break;
					
					case 'Tax Declaration':
						$tableName = $this->_ehrTables->taxDeclaration;
						$fieldName = 'Declaration_Id';
						break;
					
					case 'Tax Entity':
						$tableName = $this->_ehrTables->taxEntity;
						$fieldName = 'Tax_Entity_Id';
						break;
					
					case 'Expense Type':
						$tableName = $this->_ehrTables->expenseTypes;
						$fieldName = 'Expense_Id';
						break;
					
					case 'Attendance File':
						$tableName = $this->_ehrTables->attendanceHeader;
						$fieldName = 'Schema_Id';
						break;
					
					case 'Project':
						$tableName = $this->_ehrTables->project;
						$fieldName = 'Project_Id';
						break;
					
					case 'Grade';
						$tableName = $this->_ehrTables->empGrade;
						$fieldName = 'Grade_Id';
						break;
					
					//case 'Skills':
					//    $tableName = $this->_ehrTables->skills;
					//    $fieldName = 'Skill_Id';
					//    break;
					
					case 'Employee Type':
						$tableName = $this->_ehrTables->empType;
						$fieldName = 'EmpType_Id';
						break;
					
					case 'Monthly Salary':
						$tableName = $this->_ehrTables->salary;
						$fieldName = 'Employee_Id';
						break;
					
					case 'Hourly Wages':
						$tableName = $this->_ehrTables->hourlyWages;
						$fieldName = 'Employee_Id';
						break;
					
					case 'Bonus Type':
						$tableName = $this->_ehrTables->bonusType;
						$fieldName = 'BonusType_Id';
						break;
					
					case 'Allowance':
						$tableName = $this->_ehrTables->allowances;
						$fieldName = 'Allowance_Id';
						break;
					
					case 'Loan Type':
						$tableName = $this->_ehrTables->loanType;
						$fieldName = 'LoanType_Id';
						break;
					
					case 'Assignment':
						$tableName = $this->_ehrTables->assignment;
						$fieldName = 'Task_Id';
						break;
					
					case 'Shift Type':
						$tableName = $this->_ehrTables->shiftType;
						$fieldName = 'ShiftType_Id';
						break;
					
					case 'Shift Allowance':
						$tableName = $this->_ehrTables->empShift;
						$fieldName = 'Request_Id';
						break;
					
					case 'Commission':
						$tableName = $this->_ehrTables->commission;
						$fieldName = 'Commission_Id';
						break;
					
					case 'Commission Type':
						$tableName = $this->_ehrTables->commissionTypes;
						$fieldName = 'Commission_Type_Id';
						break;
					
					case 'Commission Percentage':
						$tableName = $this->_ehrTables->commissionPercentage;
						$fieldName = 'Commission_Type_Id';
						break;
					
					case 'Timesheet':
						$tableName = $this->_ehrTables->empTimesheet;
						$fieldName = 'Request_Id';
						break;
					
					case 'Bonus':
						$tableName = $this->_ehrTables->empBonus;
						$fieldName = 'Bonus_Id';
						break;
					
					case 'Loan':
					case 'Loan PreClosure':
						$tableName = $this->_ehrTables->empLoan;
						$fieldName = 'Loan_Id';
						break;
					
					case 'Deferred Loan':
						$tableName = $this->_ehrTables->deferredLoan;
						$fieldName = 'Employee_Id';
						
					case 'Contact Person':
						$tableName = $this->_ehrTables->orgContact;
						$fieldName = 'Contact_Id';
						break;
					
					case 'Organization Details':
						$tableName = $this->_ehrTables->orgDetails;
						$fieldName = 'Org_Code';
						break;
					
					case 'Holiday':
						$tableName = $this->_ehrTables->holiday;
						$fieldName = 'Holiday_Id';
						break;
					
					case 'Holiday Assign':
						$tableName = $this->_ehrTables->holidayAssign;
						$fieldName = 'Holiday_Assign_Id';
						break;
					
					case 'Deduction':
						$tableName = $this->_ehrTables->deductions;
						$fieldName = 'Deduction_Id';
						break;
					
					case 'Requisition':
						$tableName = $this->_ehrTables->recruitment;
						$fieldName = 'Job_Code';
						break;
					
					case 'Advance Salary':
						$tableName = $this->_ehrTables->advanceSalary;
						$fieldName = 'AdvSalary_Id';
						break;
					
					case 'Tax Exemption':
						$tableName = $this->_ehrTables->taxExemptions;
						$fieldName = 'Tax_Exemption_ID';
						break;
					
					case 'Premium Contribution':
						$tableName = $this->_ehrTables->insurancetypeGrade;
						$fieldName = 'Insurance_Grade_Id';
						break;
					
					case 'Generate':
						if	($txtValue == 'a')
							$tableName = $this->_ehrTables->monthlyPayslip;
						else if	($txtValue == 'b')
							$tableName = $this->_ehrTables->wagePayslip;
						break;
					
					case 'Leave':
						$tableName = $this->_ehrTables->empLeaves;
						$fieldName = 'Leave_Id';
						break;
					
					case 'Leave Type':
						$tableName = $this->_ehrTables->leavetype;
						$fieldName = 'LeaveType_Id';
						break;
					
					case 'Profile':
						$tableName = $this->_ehrTables->orgProfile;
						$fieldName = 'OrgProfile_Id';
						break;
					
					case 'Transfer':
						$tableName = $this->_ehrTables->transfer;
						$fieldName = 'Transfer_Id';
						break;
					
					case 'Policy':
						$tableName = $this->_ehrTables->orgPolicies;
						$fieldName = 'OrgPolicy_Id';
						break;
					
					case 'Tax Rebate':
						$tableName = $this->_ehrTables->taxRebates;
						$fieldName = 'Tax_Rebate_ID';
						break;
					
					case 'Policy Type':
						$tableName = $this->_ehrTables->policyTypes;
						$fieldName = 'PolicyType_Id';
						break;
					
					case 'Copy PF':
						$tableName = $title;
						$fieldName = 'Employee_Id';
						break;
					
					case 'Org Provident Fund':
						$dbPf = new Payroll_Model_DbTable_ProvidentFund();
						$tableName = $this->_ehrTables->orgPF;
						$fieldName = 'PfOrg_Id';
						break;
					
					case 'Emp Provident Fund':
						$dbPf = new Payroll_Model_DbTable_ProvidentFund();
						$tableName = $this->_ehrTables->empPF;
						$fieldName = 'Employee_Id';
						break;
					case 'Final Settlement':
						$tableName = $this->_ehrTables->transactionBalance;
						$fieldName = 'Transaction_Id';
						break;
					case 'PF Payment':
						$tableName = $this->_ehrTables->orgPfPaymentTracker;
						$fieldName = 'PF_Payment_Tracker_Id';
						break;
					case 'Insurance Payment':
						$tableName = $this->_ehrTables->orgInsPaymentTracker;
						$fieldName = 'Ins_Pay_Tracker_Id';
						break;
					case 'Warning':
						$tableName = $this->_ehrTables->warnings;
						$fieldName = 'Warning_Id';
						break;
					case 'Memo':
						$tableName = $this->_ehrTables->memos;
						$fieldName = 'Memo_Id';
						break;
					case 'Award':
						$tableName = $this->_ehrTables->awards;
						$fieldName = 'Award_Id';
						break;
					case 'Complaint':
						$tableName = $this->_ehrTables->complaint;
						$fieldName = 'Complaint_Id';
						break;
					case 'Award Type':
						$tableName = $this->_ehrTables->awardtypes;
						$fieldName = 'AwardType_Id';
						break;
				}
				
				if (!empty($fieldName) && !empty($uniqueId)) // if table has LOCK_FLAG column update lock flag to 0
				{
					$updateTable = $this->_dbAccessRights->clearLockFlag($tableName, $fieldName, $uniqueId);
					
					if ($updateTable)
					{
						$this->_dbAccessRights->clearSubmitLock($this->_logEmpId, $tableName); // clear lock for the table
					}
				}
				else
				{
					$this->_dbAccessRights->clearSubmitLock($this->_logEmpId, $tableName); // clear lock for the table
				}
			}
        }
        else
        {
            $this->_redirect('');
        }
    }

	/**
	 * Clear user session on browser window or tab close
	 */
    public function clearUserSessionAction()
    {
        $layout = $this->_helper->layout();
        $layout->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('clear-user-session', 'json')->initContext();
            
			$this->_dbAccessRights->clearUserSession($this->_logEmpId);
			// clear lock on browser window or tab close
        }
        else
        {
            $this->_redirect('');
        }

    }

	/**
	 * Check lock in form
	 */
    public function checkLockFormAction()
    {
        $layout = $this->_helper->layout();
        $layout->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
            if (1==1)
            {
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
                $ajaxContext->addActionContext('check-lock-form', 'json')->initContext();
				
                $title = $this->_getParam('title', null);
                $title = urldecode($title);
                $title = filter_var($title, FILTER_SANITIZE_STRIPPED);
                $uniqueId = $this->_getParam('referenceId', null);

                $textName = $this->_getParam('name', null);
                $textName = filter_var($textName, FILTER_SANITIZE_STRIPPED);
                $getCopyLock = '';
                switch($textName)
                {
                    case 'Holiday':
                        $uniqueId = urldecode($uniqueId);
                        $uniqueId = filter_var($uniqueId, FILTER_SANITIZE_STRIPPED);
                        $uniqueId = str_replace('@', '/', $uniqueId);
                        break;
                    case 'Contact Person':
                    case 'Organization Details':
                        $uniqueId = urldecode($uniqueId);
                        $uniqueId = filter_var($uniqueId, FILTER_SANITIZE_STRIPPED);
                        break;
                    default:
                        $uniqueId = filter_var($uniqueId, FILTER_SANITIZE_NUMBER_INT);
                    break;
                }
                $arrReferenceId = $this->_getParam('arrId', null);
                $arrReferenceId = filter_var($arrReferenceId, FILTER_SANITIZE_STRIPPED);
                $arrRefId = array();
                if(!empty($arrReferenceId))
                {
                    $arrRefId = explode(',',$arrReferenceId);
                }
                
                if (!empty($title) && !empty($uniqueId) && !empty($textName))
                {
                    // if there is more than one tab use eA, eB, e1,e2, etc in $addNewTab.If not leave it blank
                    switch($textName)
                    {
                        case 'Attendance File':
                            $tableName = $this->_ehrTables->attendanceHeader;
                            $fieldName = 'Schema_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'></span><span class='nodisplay'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'employees','controller'=>'attendance','action'=>'attendanceimport','xId'=>$uniqueId))));
                            break;
                        case 'Skillset Assessment':
                            $tableName = $this->_ehrTables->skillset;
                            $fieldName = 'Skillset_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'></span><span class='nodisplay'>$uniqueId</span>",
                                    $this->_url->url(array('module'=>'employees','controller'=>'skillset-assessment','action'=>'update-skillset','_dId'=>$uniqueId))));
                            break;
                        case 'Resignation':
                            $tableName = $this->_ehrTables->resignation;
                            $fieldName = 'Resignation_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'></span><span class='nodisplay'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'employees','controller'=>'resignation','action'=>'update-resignation','_resId'=>$uniqueId))));
                            break;
                        case 'Location':
                            $tableName = $this->_ehrTables->location;
                            $fieldName = 'Location_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'></span><span class='nodisplay'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'organization','controller'=>'locations','action'=>'update-location','locationid'=>$uniqueId))));
                            break;
                        case 'Roles':
                            $tableName = $this->_ehrTables->roles;
                            $fieldName = 'Designation_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eRA</span><span class='nodisplay eRA'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'employees','controller'=>'employees','action'=>'designation-roles','_dId'=>$uniqueId))));
                            break;
                        case 'Employee Travel':
                            $tableName = $this->_ehrTables->empTravels;
                            $fieldName = 'Request_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'></span><span class='nodisplay'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'employees','controller'=>'employee-travel','action'=>'add-travel','_rId'=>$uniqueId))));
                            break;
                        case 'Transfer':
                            $tableName = $this->_ehrTables->transfer;
                            $fieldName = 'Transfer_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'></span><span class='nodisplay'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'employees','controller'=>'transfer','action'=>'update-transfer','_traId'=>$uniqueId))));
                            break;
                        case 'Premium Contribution':
                            $tableName = $this->_ehrTables->insurancetypeGrade;
                            $fieldName = 'Insurance_Grade_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eC</span><span class='nodisplay eC'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'insurance','action'=>'update-insurancegrade','gradeId'=>$uniqueId))));
                            break;
                        case 'Fixed Insurance':
                            $tableName = $this->_ehrTables->fixedInsurance;
                            $fieldName = 'Insurance_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eA</span><span class='nodisplay eA'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'insurance','action'=>'update-fixedinsurance','insId'=>$uniqueId))));
                            break;
                        case 'Variable Insurance':
                            $tableName = $this->_ehrTables->variableInsurance;
                            $fieldName = 'Insurance_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eD</span><span class='nodisplay eD'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'insurance','action'=>'update-variableinsurance','insId'=>$uniqueId))));
                            break;
                        case 'Insurance Type':
                            $tableName = $this->_ehrTables->insuranceType;
                            $fieldName = 'InsuranceType_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eB</span><span class='nodisplay eB'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'insurance','action'=>'update-insurancetype','typeId'=>$uniqueId))));
                            break;
                        case 'Reimbursement':
                            $tableName = $this->_ehrTables->reimbursement;
                            $fieldName = 'Request_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eA</span><span class='nodisplay eA'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'reimbursement','action'=>'update-reimbursement','requestId'=>$uniqueId))));
                            break;
                        case 'Tax Slab':
                            $tableName = $this->_ehrTables->taxRates;
                            $fieldName = 'Tax_Rate_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eA</span><span class='nodisplay eA'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'tax-rules','action'=>'update-taxslabs','slabId'=>$uniqueId))));
                            break;
                        case 'Work Schedule':
                            $tableName = $this->_ehrTables->workSchedule;
                            $fieldName = 'Title';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'></span><span class='nodisplay'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'organization','controller'=>'work-schedule','action'=>'update-workschedule','WId'=>$uniqueId))));
                            break;
                        case 'Tax Entity':
                            $tableName = $this->_ehrTables->taxEntity;
                            $fieldName = 'Tax_Entity_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eC</span><span class='nodisplay eC'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'organization','controller'=>'organization-settings','action'=>'update-taxentity','entityId'=>$uniqueId))));
                            break;
                        case 'Timesheet':
                            $tableName = $this->_ehrTables->empTimesheet;
                            $fieldName = 'Request_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eA</span><span class='nodisplay eA'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'employees','controller'=>'timesheets','action'=>'update-timesheet','reqId'=>$uniqueId))));
                            break;
                        case 'Expense Type':
                            $tableName = $this->_ehrTables->expenseTypes;
                            $fieldName = 'Expense_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eB</span><span class='nodisplay eB'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'reimbursement','action'=>'update-expensetypes','expenseId'=>$uniqueId))));
                            break;
                        case 'Employee Roles':
                            $tableName = $this->_ehrTables->empAccessRights;
                            $fieldName = 'Employee_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eRB</span><span class='nodisplay eRB'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'employees','controller'=>'employees','action'=>'employee-roles','_eId'=>$uniqueId))));
                            break;
                        case 'Employee':
                            $tableName = $this->_ehrTables->empPersonal;
                            $fieldName = 'Employee_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eA</span><span class='nodisplay eA'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'employees','controller'=>'employees','action'=>'update-employees','empId'=>$uniqueId))));
                            break;
                        case 'Contact Person':
                            $tableName = $this->_ehrTables->orgContact;
                            $fieldName = 'Contact_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eB</span><span class='nodisplay eB'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'organization','controller'=>'organization-settings','action'=>'update-contactdetails','code'=>$uniqueId))));
                            break;
                            
                        case 'Organization Details':
                            $tableName = $this->_ehrTables->orgDetails;
                            $fieldName = 'Org_Code';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eA</span><span class='nodisplay eA'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'organization','controller'=>'organization-settings','action'=>'update-orgdetails','code'=>$uniqueId))));
                            break;
                        case 'Designation':
                            $tableName = $this->_ehrTables->designation;
                            $fieldName = 'Designation_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eB</span><span class='nodisplay eB'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'employees','controller'=>'employees','action'=>'update-designation','_dId'=>$uniqueId))));
                            break;
                        case 'Skill Definition':
                            $tableName = $this->_ehrTables->skillDefinition;
                            $fieldName = 'SkillDefinition_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eC</span><span class='nodisplay eC'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'performance-management','controller'=>'performance-evaluation','action'=>'update-skilldef','skilldefId'=>$uniqueId))));
                            break;

                        case 'Performance Assessment':
                            $tableName = $this->_ehrTables->performance;
                            $fieldName = 'Performance_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eC</span><span class='nodisplay eC'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'performance-management','controller'=>'performance-evaluation','action'=>'update-performance','performanceId'=>$uniqueId))));
                            break;
                            
                        case 'Skill Level':
                            $tableName = $this->_ehrTables->skillLevelAssoc;
                            $fieldName = 'SkillDefinition_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eC</span><span class='nodisplay eC'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'performance-management','controller'=>'performance-evaluation','action'=>'updateskill-level','levelId'=>$uniqueId))));
                            break;
                            
                        case 'Attendance':
                            $tableName = $this->_ehrTables->attendance;
                            $fieldName = 'Attendance_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eA</span><span class='nodisplay eA'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'employees','controller'=>'attendance','action'=>'update-attendance','aId'=>$uniqueId))));
                            break;
                            
                        case 'Bulk Attendance Update':
                            $tableName = $this->_ehrTables->attendance;
                            $fieldName = 'Attendance_Id';
                            $addNewTab = array('n',array("Bulk Attendance Update - $title <span class='Update'></span><span class='nodisplay'>$uniqueId</span>",
                            			$this->_url->url(array('module'=>'employees','controller'=>'attendance','action'=>'copy-attendance','aid'=>$uniqueId))));
                            break;
                            
                        case 'Department':
                            $tableName = $this->_ehrTables->dept;
                            $fieldName = 'Department_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'></span><span class='nodisplay'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'organization','controller'=>'departments','action'=>'update-department','deptid'=>$uniqueId))));
                            break;
                        case 'Project':
                            $tableName = $this->_ehrTables->project;
                            $fieldName = 'Project_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'></span><span class='nodisplay'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'organization','controller'=>'projects','action'=>'update-project','projectid'=>$uniqueId))));
                            break;
                        case 'Timesheet Hour':
                            $tableName = $this->_ehrTables->timesheetHrs;
                            $fieldName = 'Grade_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eB</span><span class='nodisplay eB'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'employees','controller'=>'timesheets','action'=>'update-timesheet-hours','gradeid'=>$uniqueId))));
                            break;
                        case 'Timesheet Activity':
                            $tableName = $this->_ehrTables->timesheetActivity;
                            $fieldName = 'Project_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eC</span><span class='nodisplay eC'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'employees','controller'=>'timesheets','action'=>'update-timesheet-activity','projectid'=>$uniqueId))));
                            break;
                        case 'Grade':
                            $tableName = $this->_ehrTables->empGrade;
                            $fieldName = 'Grade_Id';

                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eC</span><span class='nodisplay eC'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'employees','controller'=>'employees','action'=>'update-grade','gradeid'=>$uniqueId))));
                            break;
                        //case 'Skills':
                        //    $tableName = $this->_ehrTables->skills;
                        //    $fieldName = 'Skill_Id';
                        //    $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eB</span><span class='nodisplay eB'>$uniqueId</span>",
                        //    $this->_url->url(array('module'=>'employees','controller'=>'miscellaneous','action'=>'update-skills','skillid'=>$uniqueId))));
                        //    break;
                        case 'Employee Type':
                            $tableName = $this->_ehrTables->empType;
                            $fieldName = 'EmpType_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eA</span><span class='nodisplay eA'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'employees','controller'=>'employees','action'=>'update-employeetype','typeid'=>$uniqueId))));
                            break;
                        case 'Monthly Salary':
                            $tableName = $this->_ehrTables->salary;
                            $fieldName = 'Employee_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eA</span><span class='nodisplay eA'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'salary','action'=>'update-monthly-salary','_EId'=>$uniqueId))));
                            break;
                        case 'Tax Section':
                            $tableName = $this->_ehrTables->taxSections;
                            $fieldName = 'Tax_Section_ID';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eB</span><span class='nodisplay eB'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'tax-rules','action'=>'update-section','_secId'=>$uniqueId))));

                            break;
                        case 'Tax Exemption':
                            $tableName = $this->_ehrTables->taxExemptions;
                            $fieldName = 'Tax_Exemption_ID';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eD</span><span class='nodisplay eD'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'tax-rules','action'=>'update-exemption','_exId'=>$uniqueId))));

                            break;
                        case 'Tax Rebate':
                            $tableName = $this->_ehrTables->taxRebates;
                            $fieldName = 'Tax_Rebate_ID';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eE</span><span class='nodisplay eE'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'tax-rules','action'=>'update-rebate','_rId'=>$uniqueId))));
                            break;
                        case 'Section Investment':
                            $tableName = $this->_ehrTables->sectionsInvestment;
                            $fieldName = 'Investment_Cat_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eC</span><span class='nodisplay eC'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'tax-rules','action'=>'update-sectioninvestment','_invId'=>$uniqueId))));

                            break;
                        case 'Tax Declaration':
                            $tableName = $this->_ehrTables->taxDeclaration;
                            $fieldName = 'Declaration_Id';
			    //Edit Tax Declaration- 
                            $addNewTab = array('n',array("$title <span class='Update'></span><span class='nodisplay'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'tax-declarations','action'=>'update-tax-declarations','_decId'=>$uniqueId))));
                            break;

                        case 'Hourly Wages':
                            $tableName = $this->_ehrTables->hourlyWages;
                            $fieldName = 'Employee_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eB</span><span class='nodisplay eB'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'salary','action'=>'update-hourly-wage','_EId'=>$uniqueId))));
                            break;
                        case 'Bonus Type':
                            $tableName = $this->_ehrTables->bonusType;
                            $fieldName = 'BonusType_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eB</span><span class='nodisplay eB'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'bonus','action'=>'update-bonus-type','_typeId'=>$uniqueId))));
                            break;
                        case 'Loan Type':
                            $tableName = $this->_ehrTables->loanType;
                            $fieldName = 'LoanType_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eB</span><span class='nodisplay eB'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'loan','action'=>'update-loan-type','loantypeid'=>$uniqueId))));
                            break;
                        case 'Shift Type':
                            $tableName = $this->_ehrTables->shiftType;
                            $fieldName = 'ShiftType_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eB</span><span class='nodisplay eB'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'shift-allowance','action'=>'update-shift-type','typeid'=>$uniqueId))));
                            break;
                        case 'Shift Allowance':
                            $tableName = $this->_ehrTables->empShift;
                            $fieldName = 'Request_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eA</span><span class='nodisplay eA'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'shift-allowance','action'=>'update-shift','request'=>$uniqueId))));
                            break;
                        case 'Bonus':
                            $tableName = $this->_ehrTables->empBonus;
                            $fieldName = 'Bonus_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eA</span><span class='nodisplay eA'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'bonus','action'=>'update-bonus','_bId'=>$uniqueId))));
                            break;
                        case 'Commission':
                            $tableName = $this->_ehrTables->commission;
                            $fieldName = 'Commission_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'></span><span class='nodisplay'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'commission','action'=>'update-commission','_cId'=>$uniqueId))));
                            break;                            
                        case 'Commission Type':
                            $tableName = $this->_ehrTables->commissionTypes;
                            $fieldName = 'Commission_Type_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'></span><span class='nodisplay'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'commission','action'=>'update-commission-type','_cTypeId'=>$uniqueId))));
                            break;
                        case 'Commission Percentage':
                        	$tableName = $this->_ehrTables->commissionPercentage;
                            $fieldName = 'Commission_Type_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eA</span><span class='nodisplay eA'>$uniqueId</span>",
                            			$this->_url->url(array('module'=>'payroll','controller'=>'commission','action'=>'update-commission-percentage','comTypeId'=>$uniqueId))));
                            break;                            
                        case 'Advance Salary':
                            $tableName = $this->_ehrTables->advanceSalary;
                            $fieldName = 'AdvSalary_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'></span><span class='nodisplay'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'advance-salary','action'=>'update-advance-salary','_advSId'=>$uniqueId))));
                            break;
                        case 'Loan PreClosure':
                            $tableName = $this->_ehrTables->empLoan;
                            $fieldName = 'Loan_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eC</span><span class='nodisplay eC'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'loan','action'=>'loan-pre-closure','_LId'=>$uniqueId))));
                            break;
                        case 'Loan':
                            $tableName = $this->_ehrTables->empLoan;
                            $fieldName = 'Loan_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eA</span><span class='nodisplay eA'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'loan','action'=>'update-loan','_lId'=>$uniqueId))));
                            break;
                        case 'Deferred Loan':
                            $tableName = $this->_ehrTables->deferredLoan;
                            $fieldName = 'Employee_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eC</span><span class='nodisplay eC'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'loan','action'=>'update-deferred-loan','_lId'=>$uniqueId))));
                            break;
                        case 'Assignment':
                            $tableName = $this->_ehrTables->assignment;
                            $fieldName = 'Task_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'></span><span class='nodisplay'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'employees','controller'=>'assignments','action'=>'update-assignment','tId'=>$uniqueId))));
                            break;
                        case 'Warning':
                            $tableName = $this->_ehrTables->warnings;
                            $fieldName = 'Warning_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'></span><span class='nodisplay'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'employees','controller'=>'warnings','action'=>'update-warnings','_wId'=>$uniqueId))));
                            break;
                        case 'Complaint':
                            $tableName = $this->_ehrTables->complaint;
                            $fieldName = 'Complaint_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'></span><span class='nodisplay'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'employees','controller'=>'complaints','action'=>'update-complaint','aId'=>$uniqueId))));
                            break;
                        case 'Award':
                            $tableName = $this->_ehrTables->awards;
                            $fieldName = 'Award_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eA</span><span class='nodisplay eA'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'employees','controller'=>'awards','action'=>'updateaward','_aId'=>$uniqueId))));
                            break;
                        case 'Award Type':
                            $tableName = $this->_ehrTables->awardtypes;
                            $fieldName = 'AwardType_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eB</span><span class='nodisplay eB'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'employees','controller'=>'awards','action'=>'updateawardtype','_typeId'=>$uniqueId))));
                            break;
                        case 'Memo':
                            $tableName = $this->_ehrTables->memos;
                            $fieldName = 'Memo_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'></span><span class='nodisplay'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'employees','controller'=>'memos','action'=>'update-memos','_mId'=>$uniqueId))));
                            break;
                        case 'Leave':
                            $tableName = $this->_ehrTables->empLeaves;
                            $fieldName = 'Leave_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eA</span><span class='nodisplay eA'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'employees','controller'=>'leaves','action'=>'update-leave','_lId'=>$uniqueId))));
                            break;
                        case 'Leave Type':
                            $tableName = $this->_ehrTables->leavetype;
                            $fieldName = 'LeaveType_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eB</span><span class='nodisplay eB'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'employees','controller'=>'leaves','action'=>'update-leavetype','_typeId'=>$uniqueId))));
                            break;
                        case 'Holiday':
                            $tableName = $this->_ehrTables->holiday;
                            $fieldName = 'Holiday_Id';
                            $addNewTab = array('n',array("Edit $textName - ".str_replace('@', '/', $title) ."<span class='Update'>eA</span><span class='nodisplay eA'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'organization','controller'=>'holidays','action'=>'update-holiday','holidayid'=>$uniqueId))));
                            break;
                        case 'Holiday Assign':
                         	$tableName = $this->_ehrTables->holidayAssign;
                           	$fieldName = 'Holiday_Assign_Id';
                           	$addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eA</span><span class='nodisplay eA'>$uniqueId</span>",
                           			$this->_url->url(array('module'=>'organization','controller'=>'holidays','action'=>'update-holiday-assign','holidayassignid'=>$uniqueId))));
                           	break;                            
                            
                        case 'Allowance':

                            $tableName = $this->_ehrTables->allowances;
                            $fieldName = 'Allowance_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'></span><span class='nodisplay'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'allowances','action'=>'update-allowance','allowanceid'=>$uniqueId))));
                            break;
                        case 'Deduction':
                            $tableName = $this->_ehrTables->deductions;
                            $fieldName = 'Deduction_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'></span><span class='nodisplay'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'deductions','action'=>'update-deduction','_dId'=>$uniqueId))));
                            break;
                       
                        case 'Profile':
                            $tableName = $this->_ehrTables->orgProfile;
                            $fieldName = 'OrgProfile_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'></span><span class='nodisplay'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'organization','controller'=>'organization-profile','action'=>'update-orgprofile','_pId'=>$uniqueId))));
                            break;
                        case 'Policy':
                            $tableName = $this->_ehrTables->orgPolicies;
                            $fieldName = 'OrgPolicy_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eA</span><span class='nodisplay eA'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'organization','controller'=>'organization-policies','action'=>'update-orgpolicy','_pId'=>$uniqueId))));
                            break;
                        case 'Policy Type':
                            $tableName = $this->_ehrTables->policyTypes;
                            $fieldName = 'PolicyType_Id';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'>eB</span><span class='nodisplay eB'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'organization','controller'=>'organization-policies','action'=>'update-policy-type','_pTypeId'=>$uniqueId))));
                            break;
                        case 'Requisition':
                            $tableName = $this->_ehrTables->recruitment;
                            $fieldName = 'Job_Code';
                            $addNewTab = array('n',array("Edit $textName - $title <span class='Update'></span><span class='nodisplay'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'recruitment','controller'=>'job-post-requisition','action'=>'add-requisition','_code'=>$uniqueId))));
                            break;
                        case 'PF Emp':
                            $tableName = $this->_ehrTables->empPF;
                            $fieldName = 'Employee_Id';
                            $dbPf = new Payroll_Model_DbTable_ProvidentFund();
                            $getCopyLock = $dbPf->getCopyEmpLock();
                            $addNewTab = array('n',array("Edit Emp Provident Fund - $title <span class='Update'>eA</span><span class='nodisplay eA'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'provident-fund','action'=>'update-providentfund','empid'=>$uniqueId))));
                            break;
                        case 'PF Org':
                            $tableName = $this->_ehrTables->orgPF;
                            $fieldName = 'PfOrg_Id';
                            $addNewTab = array('n',array("Edit Org Provident Fund - $title <span class='Update'>eA</span><span class='nodisplay eA'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'provident-fund','action'=>'update-providentfund','pfid'=>$uniqueId))));
                            break;
                        case 'Copy PF':
                            $tableName = $this->_ehrTables->empPF;
                            $fieldName = 'Employee_Id';
                            $addNewTab = array('n',array("Copy Provident Fund - $title <span class='Update'>eB</span><span class='nodisplay eB'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'provident-fund','action'=>'copy-providentfund','empid'=>$uniqueId))));
                            break;
                        case 'Final Settlement':
                            $tableName = $this->_ehrTables->transactionBalance;
                            $fieldName = 'Transaction_Id';
                            $addNewTab = array('n',array("Update $textName - $title <span class='Update'></span><span class='nodisplay'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'final-settlement','action'=>'update-final-settlement','_transId'=>$uniqueId))));
                            break;
                        case 'PF Payment':
                            $tableName = $this->_ehrTables->orgPfPaymentTracker;
                            $fieldName = 'PF_Payment_Tracker_Id';
                            $addNewTab = array('n',array("Update $textName - $title <span class='Update'>eC</span><span class='nodisplay eC'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'provident-fund','action'=>'edit-pf-payment','_paymentId'=>$uniqueId))));
                            break;
			case 'PT Payment':
                            $tableName = $this->_ehrTables->ptPaymentTracker;
                            $fieldName = 'PT_Payment_Tracker_Id';
                            $addNewTab = array('n',array("Update $textName - $title <span class='Update'>eC</span><span class='nodisplay eC'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'tax-rules','action'=>'update-pt-payment','_paymentTrackerId'=>$uniqueId))));
                            break;
                        case 'Insurance Payment':
                            $tableName = $this->_ehrTables->orgInsPaymentTracker;
                            $fieldName = 'Ins_Pay_Tracker_Id';
                            $addNewTab = array('n',array("Update $textName - <span class='Update'>eF</span><span class='nodisplay eF'>$uniqueId</span>",
                            $this->_url->url(array('module'=>'payroll','controller'=>'insurance','action'=>'edit-insurance-payment','_paymentId'=>$uniqueId))));
                            break;
						
						
						
                    }
                    if(!empty($uniqueId) && !empty($tableName) && !empty($fieldName))
                    {
                        $checkFormLock = $this->_dbAccessRights->checkLockFlag($uniqueId, $tableName, $fieldName);
                        $recordLimit = $this->_dbAccessRights->checkOpenedRecordLimitJS($this->_logEmpId);
                        $pageLimit = $recordLimit[1];
                        if(!empty($getCopyLock))
                        {
                            $pageLimit = $recordLimit[1] - $getCopyLock;
                        }
                        if((($checkFormLock != 0 && $checkFormLock == $this->_logEmpId) || $checkFormLock == 0) && in_array($uniqueId, $arrRefId))
                        {
                            if($recordLimit[0] || in_array($uniqueId, $arrRefId))
                            $this->view->access = $addNewTab;
                            	
                            else
                            $this->view->access = array('m','<div style="font-size:14px">You don\'t have rights to edit more than '.$pageLimit.' records.</div>');
                        }
                        elseif($checkFormLock != 0 && $checkFormLock == $this->_logEmpId && !in_array($uniqueId, $arrRefId))
                        {
                            $this->view->access = array('m','<div style="font-size:14px;width:250px;">Same record has been opened by your session in some other browser or system.<br/>
    								If you still have problem in opening the form, contact System Admin</div>');
    
                        }
                        elseif($checkFormLock != 0 && $checkFormLock != $this->_logEmpId)
                        {
                            $editEmpName = $this->_dbPersonal->employeeName($checkFormLock);
                            $this->view->access = array('m','<div style="font-size:14px">'.$editEmpName['Employee_Name'] . ' is updating this record. Please Wait...</div>');
                        }
                        else
                        {
                            if($recordLimit[0])
                            $this->view->access = $addNewTab;
                            else
                            $this->view->access = array('m','<div style="font-size:14px">You don\'t have rights to edit more than '.$pageLimit.' records.</div>');
                        }
                    }
                }
            }
        }
        else
        {
            $this->_redirect('');
        }
    }

	/**
	 * Reload grid on update
	 */
    public function beforeGridReloadAction()
    {
        $this->_helper->layout->disableLayout();
        
		$ajaxContext = $this->_helper->getHelper('AjaxContext');
        $ajaxContext->addActionContext('before-grid-reload','json')->initContext();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
            $this->view->data = 'true';
        }
        else
        {
            $this->_redirect('');
        }
    }

	/**
	 * Get upline manager count by employeeId
	 */
    public function managerCountAction()
    {
        $this->_helper->layout->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('manager-count','json')->initContext();
            
			$employeeId = $this->_getParam('empId',null);
            $employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
            
			if (!empty($employeeId))
            {
                $approverDetails = $this->_dbCommonFun->listApproverDetails ($employeeId, '', $this->_logEmpId,NULL,1);
                $validApproverIds = $approverDetails['validApproverDetails'];
                $this->view->cntManager = count($validApproverIds);
            }
			else
			{
				$this->view->cntManager = 0;
			}
        }
        else
        {
            $this->_redirect('');
        }
    }

	/**
	 * Get alternate employee details to show in popup
	 */
    public function alternateEmployeeAction()
    {
        $layout = $this->_helper->layout();
        $layout->disableLayout('layout');
        if(isset($_SERVER['HTTP_REFERER']))
        {
            $layout->setLayout('tab_layout');
            
			$employeeForm = new Default_Form_Manager();
			$this->view->empForm = $employeeForm;
        }
        else
        {
            $this->_redirect('');
        }
    }
	
	/**
	 * Get accessrights for a given employeeId , formName
	 */
    public function checkHrappuserAction()
    {
        $this->_helper->layout->disableLayout();
        if(isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('check-hrappuser','html')->initContext();
            $formName = $this->_getParam('fname',null);
            $formName = filter_var($formName, FILTER_SANITIZE_STRIPPED);
            if(!empty($formName))
            {
                $formAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $formName);
                $request = 0;
                switch ($formName)
                {
                    case 'Timesheets':
                    case 'Commission':
                    case 'Warning':
                    case 'Memo':
                    case 'Awards':
                    case 'Complaint':
                    case 'Advance Salary':
                    case 'Deductions':
                    case 'Attendance':
                    case 'Leaves':
                    case 'Salary':
                    case 'Resignation':
                    case 'Attendance Reports':
		case 'Absentees Report':	
                    case 'Tax Declaration':
                    case 'Transfer':
                    case 'Insurance':
                    case 'Deferred Loan';
                    case 'Final Settlement':
                    case 'Performance Assessment':
                    case 'Skillset Assessment':
                        if(empty($formAccessRights['Admin']) && $formAccessRights['Employee']['View'] == 1
                        && $formAccessRights['Employee']['Is_Manager'] == 0)
                        {
                            $request = 1;
                        }
                        break;

                    case 'Salary Payslip':
                    case 'Bonus':
                    case 'Reimbursement':
                    case 'Loan':
                    case 'Shift Allowance':
                    case 'Employee Travel':
                    case 'Reimbursement':
                        if(empty($formAccessRights['Admin']) && $formAccessRights['Employee']['View'] == 1
                        && $formAccessRights['Employee']['Is_Manager'] == 0 && $formAccessRights['Employee']['Optional_Choice'] == 0)
                        {
                            $request = 1;
                        }
                        break;
                }
                	
                $this->view->request = $request;
            }
        }
        else
        {
            $this->_redirect('');
        }
    }
    
	/**
	 * Get organization date format
	 */
	public function orgDateFormatAction()
    {
        $this->_helper->layout->disableLayout();
        if(isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('org-date-format','json')->initContext();
            $orgDF = $this->_ehrTables->orgDateformat();
            if(!empty($orgDF))
            {
                $this->view->format = array($orgDF['jq'], $orgDF['regex']);
            }
            else
            {
                $this->view->format = array('dd/mm/yy', '/^(\d{2})(\/)(\d{2})(\/)(\d{4})$/');
            }
        }
        else
        {
            $this->_redirect('');
        }
    }
	
	public function listEmployeeDetailsAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('list-employee-details', 'json')->initContext();
			
			$formName = $this->_getParam('_fId', null);
			$formName = filter_var($formName, FILTER_SANITIZE_STRIPPED);
			
			$manager = $this->_getParam('_M', null);
			$manager = filter_var($manager, FILTER_SANITIZE_NUMBER_INT);
			
			$punchInDate = $this->_getParam('_punchInDate', null);
			$punchInDate = filter_var($punchInDate, FILTER_SANITIZE_STRIPPED);
			
			$tdsHistoryType = $this->_getParam('_tdsHistoryType', null);
			$tdsHistoryType = filter_var($tdsHistoryType, FILTER_SANITIZE_STRIPPED);

            $formId = (int) $this->_getParam('formId', null);
			$formId = filter_var($formId, FILTER_SANITIZE_NUMBER_INT);
			
			$adminAccess = $isManager = $mWorkschedule = $complaintFromId = '';
			
			if (!empty($formName) && $formName!='Employee Roles')
			{
				if (!empty($manager) && $formName == 'Assignments')
				{
					$isManager = $manager;
				}
				else
				{
					$accessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $formName);
					$adminAccess  = $accessRights['Admin'];
					$isManager    = $accessRights['Employee']['Is_Manager'];
				}
			}
			
			if ($formName == 'Complaints')
                $manager = $this->_getParam('employeeId',null);
			
			$this->view->result = $this->_dbCommonFun->listEmployeesDetails ($formName, $manager, $this->_logEmpId, $punchInDate, $tdsHistoryType,'',$formId);
		}
		else
		{
			$this->_redirect('');
		}
    }
	
	public function listsalaryEmployeeAction()
    {
        $this->_helper->layout()->disableLayout();
			
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('listsalary-employee', 'json')->initContext();
			
			$formName = $this->_getParam('_fId', null);
			$formName = filter_var($formName, FILTER_SANITIZE_STRIPPED);
			
			if (!empty($formName))
			{
				if (!empty($manager) && $formName == 'Commission')
				{
					$isManager=$manager;
				}
				else
				{
					$accessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $formName);
					$adminAccess = $accessRights['Admin'];
					$isManager = $accessRights['Employee']['Is_Manager'];
				}
			}
			
			$this->view->result = $this->_dbCommonFun->listSalaryEmployee ($formName, $isManager, $adminAccess, $this->_logEmpId);
		}
		else
		{
			$this->_redirect('');
		}
    }

	
	public function listApproverDetailsAction()
    {
        $this->_helper->layout()->disableLayout();
		
        // if (isset($_SERVER['HTTP_REFERER']))
        // {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('list-approver-details', 'json')->initContext();
			
			$employeeId = $this->_getParam('employeeId', 0);
			$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
			
			$isManager = $this->_getParam('isManager', 0);
			$isManager = filter_var($isManager, FILTER_SANITIZE_NUMBER_INT);
			
			$formName = $this->_getParam('formName', 0);
			$formName = filter_var($formName, FILTER_SANITIZE_STRIPPED);
			
            $approverId = $this->_getParam('approverId', 0);
			$approverId = filter_var($approverId, FILTER_SANITIZE_NUMBER_INT);

            $inputLoginEmployeeId = $this->_getParam('loginEmployeeId',0);
            $loginEmployeeId = $inputLoginEmployeeId ? $inputLoginEmployeeId : $this->_logEmpId;

			if(!empty($employeeId))
			{
			    $result = $this->_dbCommonFun->listApproverDetails ($employeeId, $isManager, $loginEmployeeId,$formName,0,$approverId); 
				
				$resignationDate = $this->_dbCommonFun->getEmployeeResignationDate($employeeId);
				//If employee has resignation then set maximum date as employee resignation date
				if(!empty($resignationDate))
				{
                    $result['resignationDate']  =  $this->_ehrTables->dateForPhp($resignationDate);
                    if($formName == 'Attendance')
                    {
                        $result['resignationDateFormat'] = date('m/d/Y',strtotime($resignationDate));
                    }
                    else 
                    {
                        $result['resignationDateFormat'] = null;
                    }
         		}
				else
				{
                    $result['resignationDate'] = null;
                    $result['resignationDateFormat'] = null;
				}
				
				if( $formName == 'Deductions'  || $formName == 'Bonus' || $formName == 'Advance Salary' || $formName == 'Commission' || $formName == 'Loan'
				 || $formName == 'Reimbursement' || $formName == 'Shift Allowance' || $formName == 'Deferred Loan')
				{
					$result1= $this->_dbCommonFun->getLastPayslipMonth($employeeId); 
					$result['previousCutoffDate'] = $this->_ehrTables->dateForPhp($result1['previousCutoffDate']); 
					$result['cutoffDate'] = $this->_ehrTables->dateForPhp($result1['cutoffDate']); 
                    $result['salaryDate']= $this->_ehrTables->dateForPhp($result1['salaryDate']); 
                    
                
			        if(!empty($employeeId)&&$formName == 'Deductions')
                    {
                        $allowFutureMonthCalendar=$this->_dbCommonFun->getDeductionSettings();
                        if(!empty($allowFutureMonthCalendar))
                        {
                            //If employee has resisgnation then set maximum date as employee resignation date
                            $resignationDate = $this->_dbCommonFun->getEmployeeResignationDate($employeeId);
                            if(!empty($resignationDate))
                            {
                                $result['cutoffDate']  =  $this->_ehrTables->dateForPhp($resignationDate);
                            }
                            else
                            {
                                $dbFinancialYr = new Default_Model_DbTable_FinancialYear();
                                $fiancialDate = $dbFinancialYr->fiscalStartEndDate('PHP');
                                $result['cutoffDate'] = $this->_ehrTables->dateForPhp($fiancialDate['finend']);
                            }
                        }
                    }
                    if($formName == 'Reimbursement') {
                        $dbSalary = new Payroll_Model_DbTable_Salary();
                        $allowances = $dbSalary->getAllowances($employeeId,'','',NULL,NULL,NULL,NULL,NULL,$formName);
                        $reimbursementAllowance = array();
                        foreach($allowances as $allowance) {
                            if($allowance["Is_Claim_From_Reimbursement"] === "Yes"){
                                array_push($reimbursementAllowance, $allowance);
                            }
                        }
                        $result["allowances"] = $reimbursementAllowance;
                    }
				}

				// check whether the form name is resignation
				if($formName == 'Resignation') {
					// get the payslip details
					$result1= $this->_dbCommonFun->getLastPayslipMonth($employeeId,null,$formName); 
					$result['previousCutoffDate'] = $this->_ehrTables->dateForPhp($result1['previousCutoffDate']); 
					$result['cutoffDate'] = $this->_ehrTables->dateForPhp($result1['cutoffDate']); 
					$result['salaryDate']= $this->_ehrTables->dateForPhp($result1['salaryDate']); 

					// whether the Resignation_Date_Override flag is enabled/disabled in org settings
					$result['resignationDateOverride'] = $result1['resignationDateOverride'];

					// whether any payslip is generated for that employee
					$result['payslipGenerated'] = $result1['payslipGenerated'];

                    $result['fullAndFinalSettlementInitiated'] = $this->_dbCommonFun->empFinalSettlementInitiated($employeeId);
				}
				
				if($formName == 'Short Time Off')
				{
                    $dbShortTimeOff = new Employees_Model_DbTable_ShortTimeOff();
                    $shortTimeOffSettingsResponse = $dbShortTimeOff->listShortLeaveRequestSettings($employeeId);    
                    $shortTimeOffCoverageForAlternatePerson = '';
                    $minimumDateToApply = '';
                    $shortTimeOffActivationDate  = '';
                    $shortTimeOffActivationInfo = array();

                    if(!empty($shortTimeOffSettingsResponse))   
                    {   
                        $shortTimeSettings = $shortTimeOffSettingsResponse['Settings_Details'];   
                        if(!empty($shortTimeSettings)){ 
                            $shortTimeOffCoverageForAlternatePerson = $shortTimeSettings['Coverage_For_Alternate_Person']; 
                            $shortTimeOffActivationInfo = $dbShortTimeOff->getShortTimeOffActivationDate($employeeId, $shortTimeSettings);

                            $advanceNotificationDetails = $this->_dbCommonFun->getAdvanceNotificationMinDate($shortTimeSettings,'',$loginEmployeeId,array(352,128));
                            $minimumDateToApply = $advanceNotificationDetails['minimumDateToApply'];

                            // Handle activation date logic with proper error checking
                            if ($shortTimeOffActivationInfo['success']) {
                                $shortTimeOffActivationDate = $shortTimeOffActivationInfo['activationDate'];
                                // If activation date exists and is later than advance notification date, use activation date
                                if (!empty($shortTimeOffActivationDate) && !empty($minimumDateToApply)) {
                                    if (strtotime($shortTimeOffActivationDate) > strtotime($minimumDateToApply)) {
                                        $minimumDateToApply = $shortTimeOffActivationDate;
                                    }
                                } elseif (!empty($shortTimeOffActivationInfo['activationDate'])) {
                                    // If no advance notification date but activation date exists
                                    $minimumDateToApply = $shortTimeOffActivationDate;
                                }
                            }
                        }  
                    }
                    $result['minimumDateToApply'] = $minimumDateToApply;
                    $result['alternatePersonDetails'] = $this->_dbCommonFun->listEmployeeAndManagerDetails('alternatePersonDetails',$employeeId,$shortTimeOffCoverageForAlternatePerson);
                    $result['fieldValidation'] = $dbShortTimeOff->getValidateShortTimeOff($employeeId);
                    $result['shortTimeOffActivationDate'] = $shortTimeOffActivationDate  ?? '';

					$result1= $this->_dbCommonFun->getLastPayslipMonth($employeeId, null, null, 128); 
					$result['previousCutoffDate'] = $this->_ehrTables->dateForPhp($result1['salaryDate']); 
				}
				
				if($formName == 'Performance Evaluation')
				{
					$result1= $this->_dbCommonFun->getLastPayslipMonth($employeeId); 
					
					$result['previousCutoffDate'] = date('Y-m', strtotime('+1 month', strtotime($result1['previousCutoffDate'])));
					
				    if(!empty($result['resignationDate']))
					$result['resignationDate'] = date('Y-m',strtotime($result['resignationDate']));
					
                }

                if($formName == 'leaves')
                {
                    $leaveSettings = $this->_dbCommonFun->getLeaveSettings();
                    if(!empty($leaveSettings))
                    {
                        $coverageForAlternatePerson     = $leaveSettings['Coverage_For_Alternate_Person'];
                        $enforceAlternatePersonForLeave = $leaveSettings['Enforce_Alternate_Person_For_Leave'];
                    }
                    else
                    {
                        $coverageForAlternatePerson = '';
                        $enforceAlternatePersonForLeave = 'No';
                    }
                    $result['alternatePersonDetails']         = $this->_dbCommonFun->listEmployeeAndManagerDetails('alternatePersonDetails',$employeeId,$coverageForAlternatePerson);
                    $result['enforceAlternatePersonForLeave'] = $enforceAlternatePersonForLeave;
                }

                $this->view->result = $result;
			}
		// }
        // else
		// {
		// 	$this->_redirect('');
		// }
    }
	
	//get organization settings details
	public function getOrgSettingsDetailsAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('get-org-settings-details', 'json')->initContext();
			
			$orgCode = $this->_ehrTables->getOrgCode();
			
			$this->view->result = $this->_dbOrgSettings->viewOrgDetail ($orgCode);	
		}
        else
		{
			$this->_redirect('');
		}
	}
	
	//Get consideration date for shift scheduling and adhoc allowance, dont need approver details.
	public function getConsiderationDateAction()
    {
        $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            if($this->_hrappMobile->checkAuth()) {
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
                $ajaxContext->addActionContext('get-consideration-date', 'json')->initContext(); 
                
                $employeeId = $this->_getParam('employeeId', 0);
                $employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
                
                $formName = $this->_getParam('formName', 0);
                $formName = filter_var($formName, FILTER_SANITIZE_STRIPPED);
                
                if($formName == 'Shift Scheduling')
                {
                    
                    $lastPayslipMonth = $this->_dbCommonFun->getLastPayslipMonth($employeeId, null, null, 163);
                    $result['salaryDate'] = $this->_ehrTables->dateForPhp($lastPayslipMonth['salaryDate']);
                    if(!empty($employeeId))
                    {
                        //If employee has resisgnation then set maximum date as employee resignation date
                        $resignationDate = $this->_dbCommonFun->getEmployeeResignationDate($employeeId);
                        if(!empty($resignationDate))
                        {
                            $result['resignationDate']  =  $this->_ehrTables->dateForPhp($resignationDate);
                        }
                        else
                        {
                            $result['resignationDate'] = null;
                        }
                    }
                    else
                    {
                        $result['resignationDate'] = null;
                    }
                    
                }
                elseif($formName == 'Allowances')
                {
                    $lastPayslipMonth = $this->_dbCommonFun->getLastPayslipMonth($employeeId);
                    $result['previousCutoffDate'] = $this->_ehrTables->dateForPhp($lastPayslipMonth['previousCutoffDate']); 
                    $result['cutoffDate'] = $this->_ehrTables->dateForPhp($lastPayslipMonth['cutoffDate']); 

                    $allowFutureMonthCalendar=$this->_dbCommonFun->getAdhocAllowanceSettings();
                    if(!empty($employeeId)&&!empty($allowFutureMonthCalendar))
                    {
                        //If employee has resisgnation then set maximum date as employee resignation date
                        $resignationDate = $this->_dbCommonFun->getEmployeeResignationDate($employeeId);
                        if(!empty($resignationDate))
                        {
                            $result['cutoffDate']  =  $this->_ehrTables->dateForPhp($resignationDate);
                        }
                        else
                        {
                            $dbFinancialYr = new Default_Model_DbTable_FinancialYear();
                            $fiancialDate = $dbFinancialYr->fiscalStartEndDate('PHP');
                            $result['cutoffDate'] = $this->_ehrTables->dateForPhp($fiancialDate['finend']);
                        }
                    }
                }
                $this->view->result = $result;       
            } else {
                $formData = array();
				$requestData =$this->getRequest()->getRawBody();
				if ($requestData)
				{
					$formData = Zend_Json::decode($requestData);
				}
				//if session expired, check for requestResource in header.If requestResource is HRAPP,
				// then return error response otherwise redirect to auth page
				if(empty($formData)|| (!empty($formData) && $formData['requestResource'] !== 'HRAPPUI')){
						$this->_redirect('auth');
				}
				else{
					$this->view->result = Zend_Json::encode(array('success'=>false, 'msg'=>'Session Expired', 'type'=>'danger'));
				}
            }    
        }
        else
        {
            $this->_redirect('');
        }
    }
	
	/** Get payment min date for all payment tracker **/
	public function getPaymentTrackerDateAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('get-payment-tracker-date', 'json')->initContext();
			
			$salaryMonth = $this->_getParam('salaryMonth', null);			

            $this->view->result = $this->_ehrTables->dateForPhp($this->_dbCommonFun->getPaymentTrackerMinDate($salaryMonth));			
		}
        else
		{
			$this->_redirect('');
		}
    }

    public function __destruct()
    {
        
    }	
}