<?php
//=========================================================================================
//=========================================================================================
/* Program : FinancialYear.php									   				         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 * 
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MQL Query to financial year and leave closure month					 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Narmadha                Initial Version         	         *
 *  0.2        25-Jul-2014    Mahesh                  Added Function               	     *
 *                                                    1.financialStartYear               *
 *                                                    Modified Function                  *
 *												      1.getAssessmentYr					 * 
 *                                                                                    	 */
//=========================================================================================
//=========================================================================================
class Default_Model_DbTable_FinancialYear extends Zend_Db_Table_Abstract
{

    protected $_db = null;

    protected $_ehrTables = null;
	
	protected $_hrappMobile = null;

    protected $_isMobile = null;
    
    protected $_orgDetails = null;

    public function init()
    {
        $this->_ehrTables = new Application_Model_DbTable_Ehr();
        $this->_db = Zend_Registry::get('subHrapp');
		$this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
        $this->_isMobile = $this->_hrappMobile->checkDevice();
        if (Zend_Registry::isRegistered('orgDetails'))
             $this->_orgDetails = Zend_Registry::get('orgDetails');
    }
     
    /**
     * to get the financial start month and end month
     */
    public function financialYr()
    {
        // $qryFiscal = $this->_db->select()->from(array('F'=>$this->_ehrTables->orgDetails),array('F.Fiscal_StartMonth'));
        	
        // $rowFiscal = $this->_db->fetchOne($qryFiscal);
        $rowFiscal = $this->_orgDetails['Fiscal_StartMonth'];
        if(!empty($rowFiscal))
        {
            if($rowFiscal>1 && $rowFiscal<=12)
            {
                $end = $rowFiscal-1;
            }
            else
            {
                $end = 12;
            }
            $fiscalMonth=array();
            $start = $rowFiscal;


            if($start==1)
            {
                for($fiscal= $start;$fiscal<=$end;$fiscal++)
                {
                    array_push($fiscalMonth,$fiscal);
                }
            }
            elseif($start>1)
            {
                while($start <= 12)
                {
                    array_push($fiscalMonth, $start);
                    $start++;
                }
                if($end==1)
                {
                    array_push($fiscalMonth, $end);
                }
                else
                {
                    $fiscal=1;
                    while($fiscal<=$end)
                    {
                        array_push($fiscalMonth, $fiscal);
                        $fiscal++;
                    }
                }
            }

            return $fiscalMonth;
        }
    }
	
	/**
	 * Get fiscal start , end month from org details
	 */
    public function getFiscalMonth()
    {
        // $qryFiscal = $this->_db->select()->from(array('F'=>$this->_ehrTables->orgDetails),array('F.Fiscal_StartMonth'));
        	
        // $rowFiscal = $this->_db->fetchOne($qryFiscal);
        $rowFiscal = $this->_orgDetails['Fiscal_StartMonth'];
    
        if(!empty($rowFiscal))
        {
            if($rowFiscal>1 && $rowFiscal<=12)
            {
                $end = $rowFiscal-1;
            }
            else
            {
                $end = 12;
            }
        }
        return array('Start_Month'=>$rowFiscal, 'End_Month'=>$end);
    }
	
	
    
	//version 0.2=> Modified code to get the assessment year for month and year passed as parameter
    public function getAssessmentYr($currMon = NULL, $currYr = NULL)
    {
          $assessmentYear = $this->_orgDetails['Assessment_Year'];
          return $assessmentYear;
    }
    
	//version 0.2 => getting the financial start year
    public function financialStartYear($month, $year)
    {
    	//return (date('n') <= $finEndMonth) ? date('Y') - 1 : date('Y');
    	$fiscalMonth = $this->financialYr(); //array(4,5...12,1,2,3)
    	$janIndex = array_keys($fiscalMonth,1);
    	$uptoDec = array_slice($fiscalMonth,0,$janIndex[0]);//array(4,5,...12)
    	$uptoEnd = array_slice($fiscalMonth,$janIndex[0]);//array(1,2,3)
    	$currMon = $month;
		if(empty($uptoDec))
		{
			return $year;
		}
    	else if(in_array($currMon, $uptoEnd))
    	{
    		return ($year-1);
    	}
    	else if(in_array($currMon, $uptoDec))
    	{
    		return $year;
    	}
    }
    
    /** Get the fiscal month year of the financial year
     * If allFiscalMonths is 1 => Return all the fiscal months('4,2019',...,'3,2020')
     * If allFiscalMonths is 0 => Return the fiscal months('4,2019',...,'1,2020') till the salary month */
	public function getFiscalMonthYear($salaryMonth=null,$assessmentYear=null,$paymentFrequency=null,$allFiscalMonths=0)
	{
		$tdsMonthArray = array();
		
		if(empty($salaryMonth) || is_null($salaryMonth))
		{
            /** If assessment year is not sent */
            if(is_null($assessmentYear)){
                $assessmentYear = $this->getAssessmentYr();
            }

			$fiscalSEMonth = $this->getFiscalMonth();
			$getSalaryMonth = $fiscalSEMonth['End_Month'].','.$assessmentYear;
			$salaryMonth = explode(',',$getSalaryMonth);
		}
		
		/** Get the financial start year based on the salary month **/
        $financialYearStart = $this->financialStartYear($salaryMonth[0],$salaryMonth[1]);
        $financialYearEnd = $financialYearStart + 1;
        
        /** Get all fiscal months **/
        $fiscalAllMonths = $this->financialYr(); //array(4,5...12,1,2,3)
        
        /** get the index of the jan month **/
    	$janIndex = array_keys($fiscalAllMonths,1);
        
        /** Get all the fiscal months till end month of financial start year  **/
    	$uptoDec = array_slice($fiscalAllMonths,0,$janIndex[0]);//array(4,5,...12)
        
        /** Get all the fiscal months till end month of financial End year  **/
    	$uptoEnd = array_slice($fiscalAllMonths,$janIndex[0]);//array(1,2,3)        
        
        /** Return the fiscal months('4,2019',...,'1,2020') till the salary month */
        if((int)$allFiscalMonths === 0){
            /* If the selected salary month lies in uptoDec array means, tds month array is formed till the salary month
                Ex : If the salary month is 7,2016 and Fiscal start month is 4.
                uptoDec will have an array like this (4,5,...12)
                So Tds month array will be formed (4,2016)....(7,2016) */
            if(in_array($salaryMonth[0],$uptoDec)){
                foreach($uptoDec as $key=>$row){
                    $tdMonths = '';
                    if($row <= $salaryMonth[0]){
                    $tdMonths = $row.','.$financialYearStart;
                    array_push($tdsMonthArray,$tdMonths);
                    }
                }
            }
            else{
                /* Otherwise tds month array is formed for the uptodec array first. And
                for the remaining months it it formed till the salary month
                Ex : If the salary month is 2,2017 and Fiscal start month is 4.
                uptoDec will have an array like this (4,5,...12)
                So Tds month array will be formed (4,2016)....(12,2016) first. 
                Then Tds month array will be formed (1,2017),(2,2017) */
                
                foreach($uptoDec as $key=>$row){
                    $tdMonths = '';                
                    $tdMonths = $row.','.$financialYearStart;
                    array_push($tdsMonthArray,$tdMonths);                
                }
                
                foreach($uptoEnd as $key=>$row){
                    $tdEndMonths = '';
                    if($row <= $salaryMonth[0]){
                    $tdEndMonths = $row.','.$financialYearEnd;
                    array_push($tdsMonthArray,$tdEndMonths);
                    }
                }
            }
        }else{
            /** Push the tds months from fiscal start month to december */
            foreach($uptoDec as $key=>$row){
                $tdsStartMonthVal = '';			
                $tdsStartMonthVal = $row.','.$financialYearStart;
                array_push($tdsMonthArray,$tdsStartMonthVal);
            }
            
            /** Push the tds months from december to fiscal end month */
            foreach($uptoEnd as $key=>$row){
                $tdsEndMonthVal = '';
                
                $tdsEndMonthVal = $row.','.$financialYearEnd;
                array_push($tdsMonthArray,$tdsEndMonthVal);
            }
        }

        if(!is_null($paymentFrequency)){
            $paymentFrequency = strtolower($paymentFrequency);

            if($paymentFrequency == 'quarterly')
            {
                $tdsMonthArray = array_chunk($tdsMonthArray,3);
                
                return $tdsMonthArray;	
            }
            else if($paymentFrequency == 'halfyearly')
            {
                $tdsMonthArray = array_chunk($tdsMonthArray,6);
                
                return $tdsMonthArray;	
            }
            else
            {
                return $tdsMonthArray;
            }
        }
		else
		{
			return $tdsMonthArray;
		}
	}
	
	/** get financial Year start and End Date for the current financial Year **/
	public function fiscalStartEndDate($dformat,$assessmentYear=null)
    {
        /** Financial start year **/
        if(is_null($assessmentYear)){
            $financialStartYear = $this->financialStartYear(date('n'),date('Y'));

            //Get the current assessment year from the org details table
            $assessmentYear = $this->getAssessmentYr();
        }else{
            $financialStartYear = $assessmentYear - 1;
        }
		
		/** Fiscal start month and end month **/
		$fiscalmonths = $this->getFiscalMonth();
		$start = $fiscalmonths['Start_Month'];
        $end   = $fiscalmonths['End_Month'];

        $dbPayslip = new Payroll_Model_DbTable_Payslip();
        $financialClosureStartDates = $dbPayslip->getSalaryDay($start, $assessmentYear-1);//Get the financial start month paycycle dates
        $financialClosureEndDates = $dbPayslip->getSalaryDay($end, $assessmentYear);//Get the financial end month paycycle dates

        $fiscalFromdate = $financialClosureStartDates['Salary_Date'];
        $fiscalEnddate = $financialClosureEndDates['Last_SalaryDate'];
        
		$startEndDates = array();
		if($dformat == 'JS')
		{
			/** return in org date format for min and max date validation **/
			$startEndDates['finstart'] = $this->_ehrTables->dateForPhp( $fiscalFromdate);
			$startEndDates['finend'] = $this->_ehrTables->dateForPhp ($fiscalEnddate );	
		}
		else
		{
			/** return in 'Y-m-d' format **/
			$startEndDates['finstart'] = $fiscalFromdate;
			$startEndDates['finend']   = $fiscalEnddate;
		}

        return $startEndDates;
    }

    /**Get the employee age in the previous assessment years */
    public function getAssYrTaxStatusAndAge($employeeId,$paycycleDate,$assYrDetails){
        $currAssYear = $this->_orgDetails['Assessment_Year'];
        $inpAssYear = $assYrDetails['Assessment_Year'];

        $empTaxCalcDetails = array();

        /** We are not using common function as it will be called during payslip generation. To get DOJ we need to initialise the db file for
         * every iteration. That may takes to timeout issue. Either we need to get doj as input or get the doj here. In future we can
         * sent the doj as input
         */
        $employeeDateOfJoin = $this->_db->fetchOne($this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),'Date_Of_Join')
								->joinInner(array('P'=>$this->_ehrTables->empPersonal),'J.Employee_Id = P.Employee_Id',array())
                                ->where('J.Employee_Id = ?', $employeeId)
                                ->where('P.Form_Status =1')
                                ->where('J.Emp_Status Like ?', 'Active'));

        if(!empty($paycyleDate['Salary_Date'])){
            if(!empty($employeeDateOfJoin) && strtotime($employeeDateOfJoin) > strtotime($paycyleDate['Salary_Date'])){
                $empStartDate = $employeeDateOfJoin;
            }else{
                $empStartDate = $paycyleDate['Salary_Date'];
            }
        }else{
            $empStartDate = '';
        }

        if(!empty($empStartDate)){
            $employeeAgeGender = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->empPersonal, array('Age'=>new Zend_Db_Expr("TIMESTAMPDIFF( YEAR, DOB , $empStartDate )"), 'Gender'))
                                ->where('Employee_Id = ?', $employeeId));
        }else{
            $employeeAgeGender = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->empPersonal, array('Age'=>new Zend_Db_Expr("TIMESTAMPDIFF( YEAR, DOB , curdate( ) )"), 'Gender'))
                                ->where('Employee_Id = ?', $employeeId));
        }

        // when the employee is age is 60 to  79 consider them as "Senior Citizen"
        // when the employee is age is 80 to  100 consider them as "Super Senior Citizen"
        // when the employee age is less than 60 we need to consider the employees original gender like male,female
        if(!empty($employeeAgeGender) && $employeeAgeGender['Age'] >=60)
        {
            $taxCategoryName = array('Senior Citizen','Super Senior Citizen');
            
            $gender = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->taxCategory,array('Tax_Category_Name'))
                                                                            ->where('Min_Age <= ?', $employeeAgeGender['Age'])
                                                                            ->where('Max_Age >= ?', $employeeAgeGender['Age'])
                                                                            ->where('Tax_Category_Name IN (?)',$taxCategoryName));
            $employeeAgeGender['Gender'] = $gender;                                                                
        }

        /** If the current assessment year and the input assessment year is same we should fetch the active tax records by using '<=' operator */
        if((int)$currAssYear === (int)$inpAssYear){
            $whereCondTaxStatus = array('Active');
            $whereCondAssYrBasedOperator = '<=';
        }else{
            $whereCondTaxStatus = array('InActive');
            $whereCondAssYrBasedOperator = '=';
        }

        $empTaxCalcDetails['Emp_Age_Gender_Details'] = $employeeAgeGender;
        $empTaxCalcDetails['WhereCond_Tax_Status'] = $whereCondTaxStatus;
        $empTaxCalcDetails['Assessment_Year_Operator'] = $whereCondAssYrBasedOperator;

        return $empTaxCalcDetails;
    }

    /** Get the employee all the payslip months for the current assessment year or given financial year range */
    public function getEmpPayrunFinMonthsForAssYr($employeeId,$assessmentYear,$payslipType){
        $payslipSalMonths = $fiscalMonthArray = array();

        if(!empty($assessmentYear)){
            /** Get all the salary months (4,2019),....,(3,2020) based on the assessment year */
            $fiscalMonthArray = $this->getFiscalMonthYear(null,$assessmentYear);
        }

        if(!empty($fiscalMonthArray) && count($fiscalMonthArray) > 0 && !empty($employeeId)){
            $payslipSalMonths = $this->getFinYrEmpPayslipMonths($employeeId,$payslipType,$fiscalMonthArray);
        }

        $empPayrunAndFinMonthsRes = array('Fin_Month_Year' => $fiscalMonthArray,
                                        'Emp_Payslip_Sal_Months' => $payslipSalMonths);

        return $empPayrunAndFinMonthsRes;
    }

    /** Get the employee generated payslip months in a financial year */
    public function getFinYrEmpPayslipMonths($employeeId,$payslipType,$fiscalMonthsArr){
        if($payslipType === 'monthly'){
            $payslipTableName = $this->_ehrTables->monthlyPayslip;
        }else{
            $payslipTableName = $this->_ehrTables->wagePayslip;
        }

        /** get payslip ids which exist in between the financial start and the end date **/
        $payslipSalMonths =  $this->_db->fetchCol($this->_db->select()->from($payslipTableName, array('Salary_Month'))
        ->where('Salary_Month IN (?)',$fiscalMonthsArr)
        ->where('Employee_Id = ?',$employeeId));

        return $payslipSalMonths;
    }

    

    /** Check TDS history exists from the financial start month and before current payslip start date and return the tds history ids */
	public function validateTdsHistoryExistForAssYear($assessmentYear=null,$paycycleStartDate, $employeeId, $financialStartEndDates=null){
		$tdsHistoryIds = array();

        //Get the financial start and end date
        if(is_null($financialStartEndDates)){
            /** If the assessment year is null then the current assesment year will be considered in the fiscalStartEndDate
             * function. Otherwise the input assessment year will be considered.
             */
            $financialStartEndDates = $this->fiscalStartEndDate('PHP',$assessmentYear);
            $fiscalStartDate = (isset($financialStartEndDates['finstart'])) ? $financialStartEndDates['finstart'] : '';
        }else{
            $fiscalStartDate = $financialStartEndDates['finstart'];
        }

		if(!empty($fiscalStartDate) && !empty($paycycleStartDate) && !empty($employeeId)){
			/** get the history Id for the employee from financial start date to end of the previous payslip date **/
			$qryTdsHistoryExists = $this->_db->select()->from($this->_ehrTables->empTdsHistory,array('History_Id'))
													->where('Employee_Id = ?', $employeeId)
													->where('From_Date >= ?', $fiscalStartDate)
													->where('To_Date < ?', $paycycleStartDate);

			$tdsHistoryIds = $this->_db->fetchCol($qryTdsHistoryExists);
		}

		return $tdsHistoryIds;
    }

    /** Get the tds payment frequency */
    public function getTdsPaymentFrequency(){
        return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->taxConfiguration, 'TDS_Payment_Frequency'));
    }

    /** Get the fiscal months name array */
    public function getFiscalMonthsName(){
        $fiscalMonthsNameArr = array();
        $fiscalMonthsArr = $this->financialYr();

        /** Convert month number to month name and push it in an array */
        foreach($fiscalMonthsArr as $fiscalMonth){
            array_push($fiscalMonthsNameArr,(date('F', mktime(0, 0, 0, $fiscalMonth, 10))));
        }
        return $fiscalMonthsNameArr;
    }

    public function __destruct()
    {
        
    }
}

