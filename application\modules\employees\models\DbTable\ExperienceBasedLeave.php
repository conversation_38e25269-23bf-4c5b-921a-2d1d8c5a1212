<?php

class Employees_Model_DbTable_ExperienceBasedLeave extends Zend_Db_Table_Abstract
{
    protected $_db            = null;
    protected $_ehrTables     = null;
    protected $_dbJob         = null;
    protected $_dbCommonFun   = null;
    protected $_dbLeave   = null;
  
    public function init()
    {
        $this->_ehrTables     = new Application_Model_DbTable_Ehr();
        $this->_db            = Zend_Registry::get('subHrapp');
        $this->_dbJob         = new Employees_Model_DbTable_JobDetail();
        $this->_dbCommonFun   = new Application_Model_DbTable_CommonFunction();
       
    
    }

    /** the list function for Employee Experience leave **/       
    public function searchEmployeeExperience($leaveTypeId)
    {
        $qryEmployeeExperience = $this->_db->select()->from(array('EL'=>$this->_ehrTables->empExperienceLeave),
                                                    array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS EL.Experience_Id as count'),'EL.Experience_Id',
                                                                  'EL.LeaveType_Id','EL.Experience_From','EL.Experience_To','EL.Total_Days',
                                                                  
                                                                  'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', EL.Experience_Id)")))
                                                   
                                                   	->joinInner(array('LT'=>$this->_ehrTables->leavetype),"LT.LeaveType_Id = EL.LeaveType_Id", array('LT.Leave_Enforcement_Configuration'))                                                      
                                                    ->where('EL.LeaveType_Id = ?', $leaveTypeId);
 
        	
        $employeeExperienceDetails = $this->_db->fetchAll($qryEmployeeExperience);
                
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
                
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empExperienceLeave, new Zend_Db_Expr('COUNT(Experience_Id)'))->where('LeaveType_Id = ?', $leaveTypeId));
                
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $employeeExperienceDetails);
    }


    /** the update function for Employee Experience leave **/       
    public function updateEmployeeExperience($empExperienceLeave,$sessionId, $formName)
    {
        $qryEmployeeExperienceSlab = $this->_db->select()->from($this->_ehrTables->empExperienceLeave, new Zend_Db_Expr('COUNT(Experience_Id)'))
                                                                            ->where('LeaveType_Id = ?', $empExperienceLeave['LeaveType_Id']);
                                                                        
        if (!empty($empExperienceLeave['Experience_Id']))
        {
            $qryEmployeeExperienceSlab->where('Experience_Id != ?', $empExperienceLeave['Experience_Id']);
        }
        
        if ($empExperienceLeave['Experience_To'] != '' && !empty($empExperienceLeave['Experience_To']) && preg_match('/^[0-9*\.]/', $empExperienceLeave['Experience_To']))
        {
            $rangeConditions = $this->_db->quoteInto(new Zend_Db_Expr('( Experience_From BETWEEN ?'),$empExperienceLeave['Experience_From']);
            $rangeConditions .= $this->_db->quoteInto(new Zend_Db_Expr(' AND ? )'), $empExperienceLeave['Experience_To']);
            $rangeConditions .= ' OR '. $this->_db->quoteInto(new Zend_Db_Expr('( Experience_To BETWEEN  ?'),$empExperienceLeave['Experience_From']);
            $rangeConditions .= $this->_db->quoteInto(new Zend_Db_Expr(' AND ? )'), $empExperienceLeave['Experience_To']);
            $rangeConditions .= ' OR '. $this->_db->quoteInto(new Zend_Db_Expr('( ? BETWEEN Experience_From AND Experience_To )'),$empExperienceLeave['Experience_From']);
            $rangeConditions .= ' OR '. $this->_db->quoteInto(new Zend_Db_Expr('( ? BETWEEN Experience_From AND Experience_To )'),$empExperienceLeave['Experience_To']);
            $qryEmployeeExperienceSlab->where($rangeConditions);
        }
        else
        {
            $qryEmployeeExperienceSlab ->where($this->_db->quoteInto(new Zend_Db_Expr('( ? BETWEEN Experience_From AND Experience_To )'),$empExperienceLeave['Experience_From']));
        }

        $empExperienceLeaveExists = $this->_db->fetchOne($qryEmployeeExperienceSlab);

        if(empty($empExperienceLeaveExists))
        {
            if(!empty($empExperienceLeave['Experience_Id']))
            {
                $action = 'Edit';
                $updated = $this->_db->update($this->_ehrTables->empExperienceLeave, $empExperienceLeave, array('Experience_Id = '.$empExperienceLeave['Experience_Id'])); 
                $experienceId = $empExperienceLeave['Experience_Id'];   
            }
            else
            {
                $action = 'Add';
                $updated =  $this->_db->insert($this->_ehrTables->empExperienceLeave, $empExperienceLeave);
                $experienceId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empExperienceLeave,new Zend_Db_Expr('Max(Experience_Id)')));
            }
            
            $result = $this->_dbCommonFun->updateResult (array('updated'         => $updated,
                                                                'action'         => $action,
                                                                'trackingColumn' => $empExperienceLeave['Experience_Id'],
                                                                'formName'       => $formName,
                                                                'sessionId'      => $sessionId,
                                                                'tableName'      => $this->_ehrTables->empExperienceLeave));

            if($updated){
                $expLvEnforcemntDet = array (
                    'Experience_Id' => $experienceId,
                    'LeaveType_Id' => $empExperienceLeave['LeaveType_Id']
                );
                
                /** Update the eligible days for the experience based leave */
                $leaveBalUpdated   = $this->getEmployeeExperienceRange($expLvEnforcemntDet, 'leave-enforcement', null);

                /** If the eligible days not updated or partially updated for the experience based leave */
                if(empty($leaveBalUpdated)){ 
                    $trackingMsg = $formName.' eligible leave days not updated - ';

                    /** Log the message only when the leave balance is not updated completely for the eligible employees */
                    $trackEligDaysUpdateResult = $this->_dbCommonFun->updateResult (array('updated' => 1,
                                                                        'action'         => '',
                                                                        'trackingColumn' => $empExperienceLeave['Experience_Id'],
                                                                        'formName'       => $formName,
                                                                        'trackingMsg' => $trackingMsg,
                                                                        'sessionId'      => $sessionId,
                                                                        'tableName'      => $this->_ehrTables->empExperienceLeave));

                    $result['msg']  = $result['msg'].'.But leave balance not updated for the eligible employees. Please contact the system admin';
                }
            }

            return $result;
        }
        else
        {
            return array('success'=>false, 'msg'=>'Employee Experience Already Exist for this level', 'type'=>'info');
        }
    }

    /** Update the experience leave for the eligible employees in the emp eligible leaves table
     *  during leave closure, employee add/update or from the leave enforcement form
     */
    public function getEmployeeExperienceRange($expLvEnforcemntDet,$expDetailsUpdateSource,$expSourceDetails=null)
    {
        $dbLeave = new  Employees_Model_DbTable_Leave();
        $activeEmployeeDetails = array();
        $employeeEligibleLeave = array();

        if(!is_null($expSourceDetails)){
            $expDetailsUpdateSingEmpId = $expSourceDetails['Employee_Id'];
            $expLvUpEmpDOJ = (!empty($expSourceDetails['Date_Of_Join'])) ? $expSourceDetails['Date_Of_Join'] : null;
        }else{
            $expDetailsUpdateSingEmpId = '';
            $expLvUpEmpDOJ = null;
        }

        $experienceTo = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empExperienceLeave,'Experience_To')
                                                                                ->where('Experience_Id = ?',$expLvEnforcemntDet['Experience_Id']));

        /** If the experience slab is defined for all the ranges */
        if(is_null($experienceTo))
        {
            $empExpLeaveDetails             = $dbLeave->getLeaveTypeRow($expLvEnforcemntDet['LeaveType_Id']);
            $allEmpEligDaysUpdateSource     = array('leave-closure','leave-enforcement');
            $singleEmpEligDaysUpdateSource  = array('employees');

            /** If the exp based leave days has to be updated when the employee form is added/ updated or when the employee is imported,cloned */
            if(in_array($expDetailsUpdateSource,$singleEmpEligDaysUpdateSource)){
                
                $activeEmployeeDetails 	= $dbLeave->getActiveEmployeesDetails($empExpLeaveDetails,$expDetailsUpdateSingEmpId);
            }else{                                        
                /** Get the employee details till the current leave closure year based on the leave coverage */
                $activeEmployeeDetails 	= $dbLeave->getActiveEmployeesDetails($empExpLeaveDetails);
            }
            
            if(!empty($activeEmployeeDetails))
            {
                foreach($activeEmployeeDetails as $activeEmployee)
                {
                    //Calculate the proration from date based on the configuration
                    $prorateFromDate        = $dbLeave->calculateFromDate($empExpLeaveDetails,$activeEmployee['Employee_Id']);
                    $employeeTotalLeaveDays = $dbLeave->getExperienceLeave($activeEmployee['Employee_Id'],$empExpLeaveDetails['LeaveType_Id'],$prorateFromDate,$activeEmployee);
                    /** Push the emp eligible leave details in an array */
                    $employeeEligibleLeave[] = array('LeaveType_Id'             => $activeEmployee['LeaveType_Id'],
                                                    'Employee_Id'               => $activeEmployee['Employee_Id'],
                                                    'Eligible_Days'             => $employeeTotalLeaveDays,
                                                    'CO_Year'                   => $activeEmployee['coyear'],
                                                    'LE_Year'       			=> $activeEmployee['coyear'],
                                                    'Leave_Closure_Start_Date'  => $activeEmployee['finstart'],
                                                    'Leave_Closure_End_Date'    => $activeEmployee['finend']);
                }
            }
  
            if(!empty($employeeEligibleLeave))
            {
                $updated   = $dbLeave->insertEmployeeEligibleLeave($employeeEligibleLeave,NULL,'update-service-based-leave2');
            }
            else
            {
                $updated = 0;
            }
        }else{
            // If the experience to is not null return the success response
            $updated = 1;
        }

        return $updated;
    }

    //Delete the employee experience leave delete
    public function deleteEligibleExperienceLeave($experienceId,$leaveTypeId)
    {
        $experienceDetailQry =$this->_db->select()->from($this->_ehrTables->empExperienceLeave,array('Experience_From','Experience_To'))
                            ->where('Experience_Id = ?',$experienceId);
        $experienceDetail = $this->_db->fetchRow($experienceDetailQry);
        
        $dbLeave                = new  Employees_Model_DbTable_Leave();
        $leaveTypeDetails       = $dbLeave->getLeaveTypeRow($leaveTypeId);
        $activeEmployeeDetails 	= $dbLeave->getActiveEmployeesDetails($leaveTypeDetails);
            
        
        $maxExperienceFrom = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empExperienceLeave,array(new Zend_Db_Expr('Max(Experience_From)')))
                            ->where('LeaveType_Id = ?',$leaveTypeId));

            /** the maximum experience value should be deleted first & that max condition is checked here **/       
        if($maxExperienceFrom == $experienceDetail['Experience_From']) 
        {  
            foreach($activeEmployeeDetails as $activeEmployee)
            {
                if(is_null($experienceDetail['Experience_To']))
                {
                        $whereCondition['Employee_Id = ?']  = $activeEmployee['Employee_Id'];
                        $whereCondition['LeaveType_Id = ?'] = $activeEmployee['LeaveType_Id'];
                        $deleted=$this->_db->delete($this->_ehrTables->empEligbleLeave,$whereCondition);
                }
            }
            $deleted = $this->_db->delete($this->_ehrTables->empExperienceLeave, 'Experience_Id='.(int)$experienceId);            
            return $deleted;
        }
        else
        {
            return 0;
        } 
    }

    public function __destruct()
    {
        
    }



}

