var listIncomeUnderSection = Vue.component('list-income-under-section', {
    template: `
            <section class="component-padding" style="margin-top:45px">
                <v-row v-if="formSource === 'POI'">
                    <v-col cols="12" class="poi-statistics-view-back-btn-col">
                        <v-btn class="poi-statistics-view-back-btn" text color="primary" @click="$emit('back-to-statistics')">
                            <i class="fa fa-chevron-left poi-statistics-view-back-icon"></i> Back
                        </v-btn>
                    </v-col>
                </v-row>
                <v-row>
                    <v-col cols="12" v-if="!loadSkeleton" class="m-10">
                        <v-card class="card-radius">
                            <v-row>
                                <v-col :xlg="(viewTaxCalculation && showTaxRegimeChange && windowWidth >= 1545 && formSource === 'IncomeUS24' ) ? '1' : '2'" :lg="(viewTaxCalculation && showTaxRegimeChange && windowWidth >= 1545 && formSource === 'IncomeUS24') ? '2' : '2'" md="3" sm="6" cols="12">
                                    <v-list>
                                        <v-list-item class="p-r-0">
                                            <v-list-item-content>
                                                <v-list-item-title class="primary--text body-1 font-weight-bold ">
                                                </v-list-item-title>
                                                <v-list-item-title class="primary--text body-1 font-weight-bold ">
                                                    {{listResponse.empDetails.employee_name ? listResponse.empDetails.employee_name : '-'}}
                                                </v-list-item-title>
                                                <v-list-item-subtitle class="grey--text text--darken-2 caption emp-details-padding">
                                                    E-ID: {{listResponse.empDetails.user_defined_empId ? listResponse.empDetails.user_defined_empId : '-'}}
                                                </v-list-item-subtitle>
                                                <v-list-item-subtitle class="grey--text text--darken-2 body-2 emp-details-padding">
                                                    {{listResponse.empDetails.department_name ? listResponse.empDetails.department_name : '-'}}
                                                </v-list-item-subtitle>
                                                <v-list-item-subtitle class="secondaryColor  body-2 emp-details-padding">
                                                    {{listResponse.empDetails.designation_name ? listResponse.empDetails.designation_name : '-'}}
                                                </v-list-item-subtitle>
                                            </v-list-item-content>
                                            <!-- only on xs screens -->
                                            <v-list-item-action v-if="(windowWidth < 600 && rolesResponse.Role_Optional_Choice)">
                                                <v-btn fab small dark color="primary" v-tooltip="{content: 'Change Employee',trigger: 'hover',placement: 'bottom'}" @click="changeEmployee()">
                                                    <v-icon>fa-refresh</v-icon>
                                                </v-btn>
                                            </v-list-item-action>
                                        </v-list-item>
                                    </v-list>
                                </v-col>
                                <!-- only on sm screens -->
                                <v-col sm="6" cols="12" class="add-declare-btn d-flex align-center p-r-30" v-if="(windowWidth < 960 && windowWidth >= 600 && rolesResponse.Role_Optional_Choice)">
                                    <v-btn class="tab-action-btn" rounded color="primary" dark @click="changeEmployee()">
                                        <v-icon class="change-emp-icon">fa-refresh</v-icon>Change Employee
                                    </v-btn>
                                </v-col>
                                <v-col xlg="2" lg="2" md="3" sm="6" cols="12" class="emp-details-cls">
                                    <custom-statistics-card view-data1 ="Taxable Income" :view-data-txt1="taxableIncome" 
                                    avatar-color="blue darken-3" :is-icon="false" image-name="taxable_income_avatar.webp" error-image="taxable_income_avatar.png">
                                    </custom-statistics-card>
                                </v-col>
                                <v-col xlg="2" lg="2" md="3" sm="6" cols="12" class="emp-details-cls" v-if="viewTaxCalculation">
                                    <custom-statistics-card image-name="annual_tax_avatar.webp" error-image="annual_tax_avatar.png" :is-slot-content=true avatar-color="purple darken-3">
                                        <template slot="statisticsontent">
                                            <v-row class="custom-statistics-content-line-height">
                                                <div class="custom-statistics-number" :class="windowWidth > 1263 ? 'custom-statistics-number' : 'd-flex align-baseline justify-center'">
                                                    <span class="font-weight-bold secondaryColor">Annual Tax</span> <span :class="windowWidth > 1263 ? '' : 'ml-2' " style="color:gray;"class="custom-statistics-text" v-tooltip="{content: annualTax,trigger: 'hover',placement: 'top'}">{{annualTax}}</span>
                                                </div>
                                            </v-row>
                                            <v-row class="custom-statistics-content-line-height">
                                                <div class="custom-statistics-text">
                                                    {{taxCalculationSheetData.Tax_Regime}}
                                                </div>
                                            </v-row>
                                        </template>
                                    </custom-statistics-card>
                                </v-col>
                                <v-col xlg="2" lg="2" md="3" sm="6" cols="12" class="emp-details-cls" v-else>
                                    <custom-statistics-card view-data1 ="Annual Tax" :view-data-txt1="annualTax" 
                                    avatar-color="blue darken-3" :is-icon="false" image-name="annual_tax_avatar.webp" error-image="annual_tax_avatar.png">
                                    </custom-statistics-card>
                                </v-col>
                                <v-col xlg="2" lg="2" md="3" sm="6" cols="12" class="emp-details-cls" :style="windowWidth < 1264 && windowWidth >=960 ? 'margin-left:1em' : ''" v-if="(formSource === 'IncomeUS24' && showTaxRegimeChange && payrollCountry === 'in')">
                                    <custom-statistics-card image-name="taxable_income_avatar.webp" error-image="taxable_income_avatar.png" :is-slot-content=true avatar-color="purple darken-3">
                                         <template slot="statisticsontent">
                                         <v-row class="custom-statistics-content-line-height">
                                            <div class="custom-statistics-number">
                                                <span class="font-weight-bold secondaryColor">Regime Change</span>
                                            </div>
                                        </v-row>
                                        
                                        <v-row class="custom-statistics-content-line-height">
                                            <div class="custom-statistics-text">
                                                <v-btn v-if="enableTaxRegimeChangeButton" style="margin-top :0px !important ; height :2em !important; font-size: 11px; !important" small class="tab-action-btn" rounded color="primary" dark @click="showTaxRegimeComparisonModal()">
                                                    Change
                                                </v-btn>
                                                <v-btn v-else style="margin-top :0px !important ; height :2em !important; font-size: 11px; !important" small class="tab-action-btn tab-action-btn-disabled m-r-5" 
                                                    rounded color="grey" v-tooltip="{content: taxRegimeChangeRestrictionMsg,trigger: 'hover',placement: 'top' }">
                                                    <span style="color: white;">
                                                        Change
                                                    </span>
                                                </v-btn>
                                            </div>
                                        </v-row>
                                        </template>
                                    </custom-statistics-card>
                                </v-col>
                                <v-col xlg="2" lg="2" md="3" sm="6" cols="12" class="emp-details-cls" :style="windowWidth < 1264 && windowWidth >=960 && !showTaxRegimeChange ? 'margin-left:1em' : ''" v-if="(formSource === 'IncomeUS24' && viewTaxCalculation && windowWidth > 650)">
                                    <custom-statistics-card image-name="taxable_income_avatar.webp" error-image="taxable_income_avatar.png" :is-slot-content=true avatar-color="purple darken-3">
                                         <template slot="statisticsontent">
                                         <v-row class="custom-statistics-content-line-height">
                                            <div class="custom-statistics-number" v-tooltip="{content: tdssheetTooltip,trigger: 'hover',placement: 'top'}" >
                                                <span class="pr-1">TDS for</span><span class="font-weight-bold secondaryColor pr-0">{{taxCalculationSheetData.Salary_Month}}</span>
                                            </div>
                                        </v-row>
                                        <v-row class="custom-statistics-content-line-height">
                                                <div class="custom-statistics-text">
                                                    <v-btn v-if="enableViewTdsButton" style="margin-top :0px !important ; height :2em !important; font-size: 11px; !important" small class="tab-action-btn" rounded color="primary" dark @click="downloadPdf()">
                                                        View TDS
                                                    </v-btn>
                                                    <v-btn v-else style="margin-top :0px !important ; height :2em !important; font-size: 11px; !important" small class="tab-action-btn tab-action-btn-disabled m-r-5" 
                                                        rounded color="grey" v-tooltip="{content: 'Please note that viewing the TDS worksheet is restricted until the financial closure process is finalized.',trigger: 'hover',placement: 'top' }">
                                                        <span style="color: white;">
                                                            View TDS
                                                        </span>
                                                    </v-btn>
                                                </div>
                                        </v-row>
                                        </template>
                                    </custom-statistics-card>
                                </v-col>
                                <v-col xlg="2" lg="2" md="3" sm="6" cols="12" class="emp-details-cls" :style="windowWidth < 1264 && windowWidth >=960 ? 'margin-left:1em' : 'padding-right: 1.5em;'" v-if="(formSource === 'IncomeUS24' && viewTaxCalculation && windowWidth > 650 && payrollCountry === 'in')">
                                    <custom-statistics-card :is-slot-content=true avatar-color="purple darken-3">
                                         <template slot="statisticsontent">
                                         <v-row class="custom-statistics-content-line-height">
                                            <div style="font-size: 14px; text-overflow: ellipsis; white-space: nowrap !important; overflow: hidden !important;">
                                                <span class="font-weight-bold secondaryColor">Compare Tax Details</span>
                                            </div>
                                        </v-row>
                                        
                                        <v-row class="custom-statistics-content-line-height">
                                            <div :style="windowWidth > 1853 ? 'margin-top: 5%; margin-bottom: 5%;': ''">
                                                <v-btn v-if="enableViewTdsButton" style="margin-top :0px !important ; height :2em !important; font-size: 11px; !important" small class="tab-action-btn" rounded color="primary" dark @click="showCompareTaxRegimeModal('Old Regime')">
                                                    Old Regime
                                                </v-btn>
                                                <v-btn v-else style="margin-top :0px !important ; height :2em !important; font-size: 11px; !important" small class="tab-action-btn tab-action-btn-disabled m-r-5" 
                                                    rounded color="grey" v-tooltip="{content: 'Please note that viewing the Old Regime is restricted until the financial closure process is finalized.',trigger: 'hover',placement: 'top' }">
                                                    <span style="color: white;">
                                                        Old Regime
                                                    </span>
                                                </v-btn>
                                                <v-btn v-if="enableViewTdsButton" style="margin-top :0px !important ; height :2em !important; font-size: 11px; !important" small class="tab-action-btn" rounded color="primary" dark @click="showCompareTaxRegimeModal('New Regime')">
                                                    New Regime
                                                </v-btn>
                                                <v-btn v-else style="margin-top :0px !important ; height :2em !important; font-size: 11px; !important" small class="tab-action-btn tab-action-btn-disabled m-r-5" 
                                                    rounded color="grey" v-tooltip="{content: 'Please note that viewing the New Regime is restricted until the financial closure process is finalized.',trigger: 'hover',placement: 'top' }">
                                                    <span style="color: white;">
                                                        New Regime
                                                    </span>
                                                </v-btn>
                                            </div>
                                        </v-row>
                                        </template>
                                    </custom-statistics-card>
                                </v-col>
                                <!-- only on md screens -->
                                <!-- if the rights exists and either the viewTaxCalculation details or tax regime change card is set to 1 or both cards are set to 1 (from source = Income U/S 24), show the change employee button -->
                                <v-col md="3" class="change-filter-fab-btn" v-if="(formSource === 'IncomeUS24' && windowWidth < 1264 && windowWidth >= 960 && rolesResponse.Role_Optional_Choice && ((viewTaxCalculation && !showTaxRegimeChange) || (showTaxRegimeChange && !viewTaxCalculation) || (viewTaxCalculation && showTaxRegimeChange) ))">
                                    <v-btn class="tab-action-btn" rounded color="primary" dark @click="changeEmployee()">
                                        <v-icon class="change-emp-icon">fa-refresh</v-icon>Change Employee
                                    </v-btn>
                                </v-col>
                                <!-- if the rights exists and both the viewTaxCalculation details or tax regime change card is set to 0 (from source = Income U/S 24), show the change employee button -->
                                <v-col md="2" class="change-filter-fab-btn testing" v-else-if="(formSource === 'IncomeUS24' && windowWidth < 1264 && windowWidth >= 960 && rolesResponse.Role_Optional_Choice && !viewTaxCalculation && !showTaxRegimeChange)">
                                    <v-btn fab small dark color="primary" v-tooltip="{content: 'Change Employee',trigger: 'hover',placement: 'bottom'}" @click="changeEmployee()">
                                        <v-icon>fa-refresh</v-icon>
                                    </v-btn>
                                </v-col>
                                <!-- if the rights exists and when the source is not Income U/S 24 show the change employee button -->
                                <v-col md="2" class="change-filter-fab-btn" v-else-if="(windowWidth < 1264 && windowWidth >= 960 && rolesResponse.Role_Optional_Choice)">
                                    <v-btn fab small dark color="primary" v-tooltip="{content: 'Change Employee',trigger: 'hover',placement: 'bottom'}" @click="changeEmployee()">
                                        <v-icon>fa-refresh</v-icon>
                                    </v-btn>
                                </v-col>
                                <!-- only on lg and xlg screens -->
                                <!-- if the rights exists and the source is incomeus24 and when either taxcalculation sheet card or tax regime change card has to be presented. -->
                                <v-col cols="12" xlg="3" lg="3" class="p-l-30 d-flex align-center p-r-30" v-if="(formSource === 'IncomeUS24' && windowWidth >= 1264 && rolesResponse.Role_Optional_Choice && ((viewTaxCalculation && !showTaxRegimeChange) || (showTaxRegimeChange && !viewTaxCalculation)))">
                                    <v-btn class="tab-action-btn" rounded color="primary" dark @click="changeEmployee()">
                                        <v-icon class="change-emp-icon">fa-refresh</v-icon>Change Employee
                                    </v-btn>
                                </v-col>
                                <!-- if the rights exists and the source is incomeus24 and when both taxcalculation sheet card and the tax regime change card should be presented if the window width is from 1264 to 1545. -->
                                <v-col cols="12" xlg="3" lg="3" class="p-l-30 d-flex align-center p-r-30" v-else-if="(formSource === 'IncomeUS24' && windowWidth >= 1264 && windowWidth < 1545 && rolesResponse.Role_Optional_Choice && viewTaxCalculation && showTaxRegimeChange)">
                                    <v-btn class="tab-action-btn" rounded color="primary" dark @click="changeEmployee()">
                                        <v-icon class="change-emp-icon">fa-refresh</v-icon>Change Employee
                                    </v-btn>
                                </v-col>
                                <!-- if the rights exists and the source is incomeus24 and when both taxcalculation sheet card and the tax regime change card should be presented if the window width is greater than or equal to 1545. -->
                                <v-col cols="12" xlg="3" lg="2" class="p-l-30 d-flex align-center p-r-30" v-else-if="(formSource === 'IncomeUS24' && windowWidth >= 1545 && rolesResponse.Role_Optional_Choice && viewTaxCalculation && showTaxRegimeChange)">
                                    <v-btn class="tab-action-btn" rounded color="primary" dark @click="changeEmployee()">
                                        <v-icon class="change-emp-icon">fa-refresh</v-icon>Change Employee
                                    </v-btn>
                                </v-col>
                                <!-- if the rights exists and the source is incomeus24 and when both taxcalculation sheet card and the tax regime change card should not be presented. -->
                                <v-col cols="12" xlg="4" lg="5" class="p-l-30 d-flex align-center p-r-30" v-else-if="(formSource === 'IncomeUS24' && windowWidth >= 1264 && rolesResponse.Role_Optional_Choice && !viewTaxCalculation && !showTaxRegimeChange)">
                                    <v-btn class="tab-action-btn" rounded color="primary" dark @click="changeEmployee()">
                                        <v-icon class="change-emp-icon">fa-refresh</v-icon>Change Employee
                                    </v-btn>
                                </v-col>
                                <!-- if the rights exists and when the source is not incomeus24 -->
                                <v-col cols="12" xlg="4" lg="5" class="p-l-30 d-flex align-center p-r-30" v-else-if="(windowWidth >= 1264 && rolesResponse.Role_Optional_Choice)">
                                    <v-btn class="tab-action-btn" rounded color="primary" dark @click="changeEmployee()">
                                        <v-icon class="change-emp-icon">fa-refresh</v-icon>Change Employee
                                    </v-btn>
                                </v-col>
                            </v-row>
                        </v-card>
                    </v-col>
                    <v-col :cols="isSmall ? 5 : 12" v-if="!loadSkeleton" :style="windowWidth <= 600 ? 'margin-bottom: -33px' : ''">
                        <v-card class="card-radius tab-card-cls">
                            <v-row style="overflow: hidden;">
                                <v-col class="p-l-20 p-t-30">
                                    <v-tabs v-model="currentItem" background-color="#fbfbfb" 
                                    :centered ="windowWidth <= 700 || isSmall" show-arrows>
                                        <v-tabs-slider></v-tabs-slider>
                                        <v-tab href="#tab-1" style="padding-bottom:20px">
                                            <div style="text-transform: capitalize !important;" class="font-weight-bold">Self Occupied</div>
                                        </v-tab>
                                        <v-tab href="#tab-2" style="padding-bottom:20px">
                                            <div style="text-transform: capitalize !important;" class="font-weight-bold">Rented</div>
                                        </v-tab>
                                    </v-tabs>
                                </v-col>
                                <v-col v-show="windowWidth > 700 && !isSmall" class="add-declare-btn">
                                    <div v-if="formSource === 'POI'">
                                        <v-btn v-if="enableApproveAllButton[0]" class="tab-action-btn m-r-5" rounded @click="fnApproveAllDeclarations()">
                                            <v-icon color="green accent-4" >check_circle</v-icon>Approve All
                                        </v-btn>
                                        <v-btn v-else class="tab-action-btn tab-action-btn-disabled m-r-5" rounded color ="grey lighten-4" v-tooltip="{content: enableApproveAllButton[1],trigger: 'hover',placement: 'top' }">
                                            <v-icon color="grey lighten-2" >check_circle</v-icon>Approve All
                                        </v-btn>
                                    </div>
                                    <div v-else>
                                        <v-btn v-if="enableAddShowMsg[0]" :class="showAddBtnInGrid ? 'tab-action-btn' : 'hidden'" rounded color="primary" dark @click="fnAddDeclaration()">
                                            <v-icon>add</v-icon>Add Declarations
                                        </v-btn>
                                        <v-btn v-else class="tab-action-btn tab-action-btn-disabled" rounded color="grey lighten-4" v-tooltip="{content: enableAddShowMsg[1],trigger: 'hover',placement: 'top' }">
                                            <v-icon>add</v-icon>Add Declarations
                                        </v-btn>
                                    </div>
                                </v-col>
                            </v-row>
                        </v-card>
                        <v-row v-show="windowWidth <= 700" >
                            <v-col class="d-flex justify-center">
                                <div v-if="formSource === 'POI'">
                                    <v-btn v-if="enableApproveAllButton[0]" class="tab-action-btn m-r-5" rounded @click="fnApproveAllDeclarations()">
                                        <v-icon color="green accent-4" >check_circle</v-icon>Approve All
                                    </v-btn>
                                    <v-btn v-else class="tab-action-btn tab-action-btn-disabled m-r-5" rounded color ="grey lighten-4" v-tooltip="{content: enableApproveAllButton[1],trigger: 'hover',placement: 'top' }">
                                        <v-icon color="grey lighten-2" >check_circle</v-icon>Approve All
                                    </v-btn>
                                </div>
                                <div v-else>
                                    <v-btn v-if="enableAddShowMsg[0]" :class="showAddBtnInGrid ? 'tab-action-btn' : 'hidden'" rounded color="primary" dark @click="fnAddDeclaration()">
                                        <v-icon>add</v-icon>Add Declarations
                                    </v-btn>
                                    <v-btn v-else class="tab-action-btn tab-action-btn-disabled" rounded color="grey lighten-4" v-tooltip="{content: enableAddShowMsg[1],trigger: 'hover',placement: 'top' }">
                                        <v-icon>add</v-icon>Add Declarations
                                    </v-btn>
                                </div>
                            </v-col>
                        </v-row>
                    </v-col>
                    <v-col cols="6" v-show="isSmall"></v-col>
                        <v-col :cols="isSmall ? 5 : 12" v-if="!loadSkeleton">
                            <v-tabs-items v-model="currentItem">
                                <v-tab-item value="tab-1" class="tab_alignment_class">
                                    <v-col  style="padding: 0px" transition="scale-transition">
                                        <self-occupied-data-table v-if="selfOccupiedTableItems.length > 0" 
                                            :smallTable="isSmall" 
                                            :user-type="userType" 
                                            ref="selfOccupiedTable" 
                                            :headers="selfOccupiedHeader" 
                                            :items="selfOccupiedTableItems" 
                                            :currency-symbol="currencySymbol"
                                            :window-width="windowWidth" 
                                            :selected-item = "selectedItem"
                                            :roles-response = "rolesResponse" 
                                            :selected-assessment-year="selectedAssessmentYear" 
                                            :current-assessment-year = "currentAssessmentYear" 
                                            :form-source = "formSource" 
                                            :chosen-status = "chosenStatus"
                                            :it-declaration-settings-status="itDeclarationSettingsStatus"
                                            @show-small="fnViewDeclaration($event)" 
                                            @doc-viewer="openDocViewer($event)"
                                            @delete-declarations="deleteDeclarations"
                                            @getSelectedRecords="selfOccupiedSelectedRecords = $event"
                                            @single-approval="fnStatusApprovalSingle($event)"
                                            :income-source-details = "incomeSourceTypes && incomeSourceTypes.length > 0 ?incomeSourceTypes[0] : null">
                                        </self-occupied-data-table>

                                        <!--Initial no record-->
                                        <no-record-initial-screen v-else button-text="Add Declarations" :add-rights="enableAddShowMsg[0] && !isSmall" :content="enableAddShowMsg[0] ? 
                                        noContentMessage : ''" main-title="No Records Found" image-name="income_initial_img" @button-click="fnAddDeclaration()"
                                        ></no-record-initial-screen>
                                    </v-col>
                                </v-tab-item>
                                <v-tab-item value="tab-2">
                                    <v-col style="padding: 0px" transition="scale-transition">
                                        <rented-data-table v-if="rentedTableItems.length > 0" 
                                            :smallTable="isSmall" 
                                            :user-type="userType" 
                                            ref="rentedDataTable" 
                                            :headers="rentedHeaders" 
                                            :items="rentedTableItems" 
                                            :currency-symbol="currencySymbol" 
                                            :window-width="windowWidth"
                                            :selected-item = "selectedItem"
                                            :roles-response = "rolesResponse"  
                                            :selected-assessment-year="selectedAssessmentYear" 
                                            :current-assessment-year = "currentAssessmentYear"  
                                            :form-source = "formSource"
                                            :chosen-status = "chosenStatus"
                                            :it-declaration-settings-status="itDeclarationSettingsStatus"
                                            @show-small="fnViewDeclaration($event)" 
                                            @doc-viewer="openDocViewer($event)"
                                            @delete-declarations="deleteDeclarations"
                                            @getSelectedRecords="rentedSelectedRecords = $event"
                                            @single-approval="fnStatusApprovalSingle($event)">
                                        </rented-data-table>
                                        <!--Initial no record-->
                                        <no-record-initial-screen v-else button-text="Add Declarations" :add-rights="enableAddShowMsg[0] && !isSmall" :content="enableAddShowMsg[0] ? 
                                        noContentMessage : ''" main-title="No Records Found" image-name="income_initial_img" @button-click="fnAddDeclaration()"></no-record-initial-screen>
                                    </v-col>
                                </v-tab-item>
                            </v-tabs-items>
                        </v-col>
                        <v-col cols="7" class="view-property-details" xl="7" lg="7" md="12" sm="12" v-if="showAddForm">
                            <div v-if="!openAddModal">
                                <add-edit-self-occupied-details 
                                    v-if="currentItem == 'tab-1'"
                                    :tax-declaration-base-url = "taxDeclarationBaseUrl"
                                    :ats-base-url = "atsBaseUrl"
                                    :api-headers="apiHeaders"
                                    :org-code = "orgCode"
                                    :employee-id = "employeeId" 
                                    :currency-symbol="currencySymbol" 
                                    :income-source-details = "incomeSourceTypes && incomeSourceTypes.length > 0 ?incomeSourceTypes[0] : null"
                                    :current-assessment-year = "currentAssessmentYear"
                                    :isEdit = "isEdit"
                                    :add-edit-self-occupied-render-count = 'addEditSelfOccupiedRenderCount'
                                    :self-occupied-data = "selectedProperty"
                                    :user-type="userType"
                                    :form-source = "formSource"
                                    :it-declaration-settings-status="itDeclarationSettingsStatus"
                                    @close-add-form="closeAddPropertyDetails()"
                                    @handle-add-edit-error="handleAddEditError($event)" 
                                    @handle-add-edit-success = "handleAddEditSuccess($event)"
                                    @handle-warning-msg = "$emit('handle-warning-msg', $event)">
                                </add-edit-self-occupied-details>
                                <add-edit-rented-details
                                    v-else 
                                    :tax-declaration-base-url = "taxDeclarationBaseUrl" 
                                    :ats-base-url = "atsBaseUrl"
                                    :org-code = "orgCode"
                                    :employee-id = "employeeId"
                                    :api-headers="apiHeaders"
                                    :current-assessment-year="currentAssessmentYear"
                                    :render-count="addEditRentedRenderCount"
                                    :currency-symbol="currencySymbol"
                                    :isEdit = "isEdit"
                                    :rented-data = "selectedProperty"
                                    :user-type="userType"
                                    :form-source = "formSource"
                                    :it-declaration-settings-status="itDeclarationSettingsStatus"
                                    @close-add-form="closeAddPropertyDetails()"
                                    @handle-add-edit-error="handleAddEditError($event)"
                                    @handle-add-edit-success = "handleAddEditSuccess($event)"
                                    @handle-warning-msg = "$emit('handle-warning-msg', $event)">
                                </add-edit-rented-details>
                            </div>
                            <v-dialog scrollable v-model="openAddModal" persistent max-width="700" min-width="600" v-else>
                                <add-edit-self-occupied-details 
                                    v-if="currentItem == 'tab-1'"
                                    :tax-declaration-base-url = "taxDeclarationBaseUrl"
                                    :ats-base-url = "atsBaseUrl"
                                    :api-headers="apiHeaders"
                                    :org-code = "orgCode"
                                    :employee-id = "employeeId" 
                                    :currency-symbol="currencySymbol" 
                                    :income-source-details = "incomeSourceTypes && incomeSourceTypes.length > 0 ?incomeSourceTypes[0] :null"
                                    :current-assessment-year = "currentAssessmentYear"
                                    :isEdit = "isEdit"
                                    :self-occupied-data = "selectedProperty"
                                    :user-type="userType"
                                    :form-source = "formSource"
                                    :roles-response = "rolesResponse"
                                    :add-edit-self-occupied-render-count = 'addEditSelfOccupiedRenderCount'
                                    :chosen-status = "chosenStatus"
                                    :it-declaration-settings-status="itDeclarationSettingsStatus"
                                    @close-add-form="closeAddPropertyDetails()"
                                    @handle-add-edit-error="handleAddEditError($event)" 
                                    @handle-add-edit-success = "handleAddEditSuccess($event)"
                                    @handle-warning-msg = "$emit('handle-warning-msg', $event)">
                                </add-edit-self-occupied-details>
                                <add-edit-rented-details 
                                    v-else
                                    :tax-declaration-base-url = "taxDeclarationBaseUrl" 
                                    :ats-base-url = "atsBaseUrl"
                                    :org-code = "orgCode"
                                    :employee-id = "employeeId" 
                                    :api-headers="apiHeaders"
                                    :current-assessment-year="currentAssessmentYear"
                                    :render-count="addEditRentedRenderCount"
                                    :currency-symbol="currencySymbol"
                                    :isEdit = "isEdit"
                                    :rented-data = "selectedProperty"
                                    :user-type="userType"
                                    :form-source = "formSource"
                                    :roles-response = "rolesResponse"
                                    :chosen-status = "chosenStatus"
                                    :it-declaration-settings-status="itDeclarationSettingsStatus"
                                    @close-add-form="closeAddPropertyDetails()"
                                    @handle-add-edit-error="handleAddEditError($event)"
                                    @handle-add-edit-success = "handleAddEditSuccess($event)"
                                    @handle-warning-msg = "$emit('handle-warning-msg', $event)">
                                </add-edit-rented-details>
                            </v-dialog>
                        </v-col>
                        <v-col v-if="loadSkeleton" cols="12">
							<v-skeleton-loader
								ref="skeleton1"
								type="list-item-avatar-three-line"
								class="mx-auto"
							></v-skeleton-loader>
							<br>
							<br>
							<br>
							<v-skeleton-loader
								ref="skeleton2"
								type="table-thead"
								class="mx-auto"
							></v-skeleton-loader>
							<br>
							<div v-for="i in 3" :key="i">
								<v-skeleton-loader
									ref="skeleton2"
									type="list-item-avatar"
									class="mx-auto"
								></v-skeleton-loader>
								<br>
							</div>
                        </v-col>
                    <!-- show the view property details -->
                    <v-col xl="7" lg="7" md="12" sm="12" cols="12" v-if="showPropertyDetails" class="view-property-details">
                        <div v-if="!openModal">
                            <view-rented-details
                                ref="viewRentedForm"
                                v-if="currentItem == 'tab-2'"
                                :tax-declaration-base-url = "taxDeclarationBaseUrl"
                                :ats-base-url = "atsBaseUrl"
                                :api-headers="apiHeaders"
                                :org-code = "orgCode"
                                :employee-id = "employeeId" 
                                :selected-item = 'selectedItem' 
                                :currency-symbol="currencySymbol" 
                                :income-source-details = "incomeSourceTypes && incomeSourceTypes.length > 1 ?incomeSourceTypes[1] : null"
                                :view-rented-render-count = "viewRentedRenderCount"  
                                :selected-assessment-year="selectedAssessmentYear" 
                                :current-assessment-year = "currentAssessmentYear"   
                                :roles-response = "rolesResponse"  
                                :form-source = "formSource" 
                                :chosen-status = "chosenStatus"
                                :it-declaration-settings-status="itDeclarationSettingsStatus"                                    
                                @close-view-modal="closeViewPropertyDetails()" 
                                @handle-error="handleError($event)"
                                @handle-document-error="handleDocumentError($event)"
                                @open-close-view-modal="openCloseViewModal($event)"
                                @handle-view-edit-error = "handleViewEditError($event)"
                                @open-edit-form="fnOpenEditForm($event)"
                                @single-approval="fnStatusApprovalSingle($event)"
                            ></view-rented-details>
                            <view-self-occupied-details 
                                ref="viewSelfOccupiedForm"
                                v-else
                                :tax-declaration-base-url = "taxDeclarationBaseUrl" 
                                :ats-base-url = "atsBaseUrl"
                                :api-headers="apiHeaders"
                                :org-code = "orgCode"
                                :employee-id = "employeeId" 
                                :selected-item = 'selectedItem'
                                :currency-symbol="currencySymbol"  
                                :income-source-details = "incomeSourceTypes && incomeSourceTypes.length > 0 ?incomeSourceTypes[0] : null"    
                                :view-self-occupied-render-count = 'viewSelfOccupiedRenderCount'
                                :selected-assessment-year="selectedAssessmentYear"
                                :current-assessment-year = "currentAssessmentYear"
                                :roles-response = "rolesResponse"
                                :form-source = "formSource"
                                :chosen-status = "chosenStatus"
                                :it-declaration-settings-status="itDeclarationSettingsStatus"
                                @close-view-modal="closeViewPropertyDetails()"
                                @open-close-view-modal="openCloseViewModal($event)"
                                @handle-document-error="handleDocumentError($event)"
                                @handle-error="handleError($event)"
                                @handle-view-edit-error = "handleViewEditError($event)"
                                @single-approval="fnStatusApprovalSingle($event)"
                            ></view-self-occupied-details>
                        </div>
                    </v-col>
                    <v-dialog v-model="openModal" persistent max-width="700" min-width="600" v-if="showPropertyDetails">
                        <view-rented-details
                            ref="viewRentedForm"                                         
                            v-if="currentItem == 'tab-2'"
                            :tax-declaration-base-url = "taxDeclarationBaseUrl"
                            :ats-base-url = "atsBaseUrl"
                            :api-headers="apiHeaders"
                            :org-code = "orgCode"
                            :employee-id = "employeeId" 
                            :selected-item = 'selectedItem'
                            :currency-symbol="currencySymbol"
                            :income-source-details = "incomeSourceTypes && incomeSourceTypes.length > 1 ?incomeSourceTypes[1] : null"
                            :view-rented-render-count = "viewRentedRenderCount"
                            :current-assessment-year = "currentAssessmentYear"
                            :selected-assessment-year="selectedAssessmentYear"
                            :roles-response = "rolesResponse"
                            :form-source = "formSource"
                            :chosen-status = "chosenStatus"
                            :it-declaration-settings-status="itDeclarationSettingsStatus"
                            @close-view-modal="closeViewPropertyDetails()"
                            @handle-error="handleError($event)"
                            @handle-document-error="handleDocumentError($event)"
                            @open-close-view-modal="openCloseViewModal($event)"
                            @handle-view-edit-error = "handleViewEditError($event)"
                            @open-edit-form="fnOpenEditForm($event)"
                            @single-approval="fnStatusApprovalSingle($event)"
                        ></view-rented-details>
                        <view-self-occupied-details 
                            ref="viewSelfOccupiedForm"
                            v-else
                            :tax-declaration-base-url = "taxDeclarationBaseUrl"
                            :ats-base-url = "atsBaseUrl"
                            :api-headers="apiHeaders"
                            :org-code = "orgCode"
                            :employee-id = "employeeId" 
                            :selected-item = 'selectedItem' 
                            :currency-symbol="currencySymbol"
                            :income-source-details = "incomeSourceTypes && incomeSourceTypes.length > 0 ?incomeSourceTypes[0] : null"
                            :view-self-occupied-render-count = 'viewSelfOccupiedRenderCount'
                            :current-assessment-year = "currentAssessmentYear"
                            :selected-assessment-year="selectedAssessmentYear"
                            :roles-response = "rolesResponse"
                            :form-source = "formSource"
                            :chosen-status = "chosenStatus"
                            :it-declaration-settings-status="itDeclarationSettingsStatus"
                            @close-view-modal="closeViewPropertyDetails()" 
                            @handle-error="handleError($event)"
                            @handle-document-error="handleDocumentError($event)"
                            @open-close-view-modal="openCloseViewModal($event)"
                            @handle-view-edit-error = "handleViewEditError($event)"
                            @single-approval="fnStatusApprovalSingle($event)"
                        ></view-self-occupied-details>
                    </v-dialog>
                </v-row>
                <view-property-proof-documents
                    v-if="openDocumentViewModal"
                    :ats-base-url = "atsBaseUrl"
                    :open-document-view-modal="openDocumentViewModal" 
                    :api-headers="apiHeaders"
                    :employee-id = "employeeId" 
                    :is-multiple-documents = "isMultipleDocuments"
                    :multi-document-data = "multiDocumentData"
                    :single-document-data = "singleDocData"
                    @handle-document-error="handleDocumentError($event)"
                    @open-close-view-modal="openCloseViewModal($event)"
                ></view-property-proof-documents>

                <!-- open tha tax sheet details -->
                <div v-if="(windowWidth > 600)" >
                    <v-dialog scrollable persistent v-model="openTaxSheetModal" persistent max-width="1200" min-width="700" >
                        <v-card class="card-radius view-details-card pa-4">
                            <view-tax-sheet-details
                             
                             :window-width="windowWidth"
                             :tax-sheet-data="taxCalculationSheetData"
                             :currency-symbol="currencySymbol" 
                             :employee-name="listResponse.empDetails.employee_name"
                             :employee-id="listResponse.empDetails.user_defined_empId"
                             :payroll-country="payrollCountry"
                             @close-tax-sheet="closeTaxSheet"
                            >
                            </view-tax-sheet-details>
                        </v-card>
                    </v-dialog>
                </div>
                <div v-else>
                    <view-tax-sheet-details
                        :window-width="windowWidth"
                        :tax-sheet-data="taxCalculationSheetData"
                        :currency-symbol="currencySymbol" 
                        :employee-name="listResponse.empDetails.employee_name"
                        :employee-id="listResponse.empDetails.user_defined_empId"
                        :is-mobile="isMobile"
                        :tax-sheet-render-count="taxSheetRenderCount"
                        :payroll-country="payrollCountry"
                        @is-mobile="taxSheetRendered($event)"
                    ></view-tax-sheet-details>
                </div>

                <!-- Tax regime comparison Modal -->
                <tax-regime-comparison-modal
                  v-if="openTaxRegimeModal"
                  :regime-comparison-details="compareTaxRegimeDetails"
                  :window-width="windowWidth"
                  :hrapp-base-url="hrappBaseUrl"
                  :choosen-employee-id="employeeId"
                  @handle-tax-regime-response="handleTaxRegimeResponse($event)"
                  @close-tax-notify-modal="closeTaxComparisonModal"
                ></tax-regime-comparison-modal>

            <!-- delete popup for mixed status records chosen -->
            <custom-modal
                :open-modal="showMixedStatusRecordsDelPopup"
                :valid-records="invalidRecordCount"
                :total-records="totalSelectedRecords.length"
                :show-icon= "showDeleteIcon"
                image-name = "delete-bin-image"
                @closemodal="fnCancelModal()" 
                @applyclosure="fnConfirmDelete()" 
                show-heading="true"
                pop-heading="are not eligible for deletion." 
                button-text="Proceed"
            >
                <template slot="bodyContent">
                    <span style="text-align: center;">
                        *By clicking the 'Proceed' button, you can delete the tax declarations in 
                    </span>&nbsp;
                    <span class="font-weight-bold">
                    Declared
                    </span>&nbsp;
                    <span>
                    and
                    </span>&nbsp;
                    <span class="font-weight-bold">
                    Returned
                    </span>&nbsp;
                    <span>
                    status.
                    </span>
                </template>
            </custom-modal>

            <custom-delete-confirmation-modal 
                :open-delete-confirmation="showValidStatusRecordsDelPopup"
                :confirmation-content = "'Are you sure want to ' + actionType + ' the selected declaration(s)?'"
                :confirmation-sub-content = "actionType === 'delete' ? '' : 'It is employers responsibility to ensure investment proofs are reviewed against the declaration before the approval. *By clicking the Yes button, you can Approve the eligible declarations. The declared amount will be considered as Approval Amount for the selected declarations which has no Approval Amount filled in.'"
                @close-modal="fnCancelModal()" 
                @accept-modal="actionType === 'delete' ? fnConfirmDelete() : fnStatusApprovalMultiple()"
                :avatar-color="actionType === 'delete' ? 'red lighten-1' : 'green accent-4'"  
                :icon-name="actionType === 'delete' ? 'far fa-trash-alt' : 'check'"
            ></custom-delete-confirmation-modal>

            <!-- custom loading -->
            <custom-loading-screen v-if="loadingScreen"></custom-loading-screen>
        </section>
        `,

    props: {
        userType: {
            type: String,
            default: ''
        },
        listResponse: {
            type: Object,
            required: true
        },
        renderCount: {
            type: Number,
            required: true
        },
        currencySymbol: {
            type: String,
            required: true
        },
        incomeSourceTypes : {
            type : Array,
            required : true
        },
        taxDeclarationBaseUrl : {
            type : String,
            required : true
        },
        apiHeaders: {
            type : Object,
            required: true,
        },    
        orgCode : {
            type : String,
            required : true
        },
        employeeId : {
            type : Number,
            required : true
        },
        atsBaseUrl : {
            type : String,
            required : true
        },
        empTaxableIncome : {
            type : Number,
            required : true
        },
        empAnnualTax : {
            type : Number,
            required : true
        },
        currentAssessmentYear : {
            type : Number,
            required : true
        },
        rolesResponse : {
            type : Object,
            required : true
        },
        isAddSuccess : {
            type : Boolean,
            default : false
        },
        isEditSuccess : {
            type : Boolean,
            default : false
        },
        successType : {
            type : String,
            default : ''
        },
        selectedAssessmentYear: {
            type: Number,
            required: true
        },
        successData : {
            type : Object,
            default : {}
        },
        selfOccupiedInitialCount: {
            type: Number,
            default: 0 
        },
        formSource : {
            type : String,
            required : true
        },
        chosenStatus : {
            type : String,
            default : ''
        },
        chosenEmpIds: {
            type: Array,
            default: []
        },
        chosenType: {
            type: String,
            default: ''
        },
        viewTaxCalculation : {
            type : Number,
            default : 0
        },
        taxSheetData : {
            type : Object,
            default : {}
        },
        itDeclarationSettingsStatus: {
            type: String,
            default: ""
        },
        compareTaxRegimeDetails : {
            type : Object,
            default : {}
        },
        showTaxRegimeChange : {
            type : Boolean,
            default : true
        },
        enableViewTdsButton : {
            type : Boolean,
            default : true
        },
        taxRegimeChangeRestrictionMsg:{
            type : String,
            default : ""
        },
        hrappBaseUrl : {
            type : String,
            required : true
        },
        enableTaxRegimeChangeButton:{
            type : Boolean,
            default : false   
        },
        payrollCountry: {
            type: String,
            default: 'in'
        }
    },
    data: function () {
        return {
            currentItem: "tab-1",
            windowWidth: 0,
            isSmall: false,
            selfOccupiedSmallTableHeaders: [
                {
                    text: 'Avatar',
                    sortable: false,
                    value: 'photo_path'
                },
                {
                    text: 'Interest on Housing Loan',
                    value: 'house_loan_interest',
                },
                { text: 'Approved Amount', value: 'approved_amount' },
                { text: 'Status', value: 'approval_status' },
            ],
            selfOccupiedLargeTableHeaders: [
                {
                    text: 'Avatar',
                    sortable: false,
                    value: 'photo_path'
                },
                {
                    text: 'Interest on Housing Loan',
                    value: 'house_loan_interest',
                },
                { text: 'Approved Amount', value: 'approved_amount' },
                { text: 'Documents', value: 'documents' },
                { text: 'Lenders Name', value: 'lender_name' },
                { text: 'Status', value: 'approval_status' },
            ],
            selfOccupiedTableItems: [],
            rentedSmallTableHeaders: [
                {
                    text: 'Avatar',
                    sortable: false,
                    value: 'photo_path'
                },
                {
                    text: 'Income/Loss from Let Out Property',
                    value: 'house_property_income_loss',
                },
                { text: 'Approved Amount', value: 'approved_amount' },
                { text: 'Status', value: 'approval_status' },
            ],
            rentedLargeTableHeaders: [
                {
                    text: 'Avatar',
                    sortable: false,
                    value: 'photo_path'
                },
                {
                    text: 'Income/Loss from Let Out Property',
                    value: 'house_property_income_loss',
                },
                { text: 'Approved Amount', value: 'approved_amount' },
                { text: 'Documents', value: 'documents' },
                { text: 'Lenders Name', value: 'lender_name' },
                { text: 'Status', value: 'approval_status' },
            ],
            rentedTableItems:[],
            selfOccupiedHeader: [],
            rentedHeaders:[],
            selfOccupiedSelectedRecords: [],
            rentedSelectedRecords: [],
            actionType: '',

            // view variables
            openModal : false,
            showPropertyDetails: false,
            selectedItem : 0,
            viewSelfOccupiedRenderCount : 0,
            viewRentedRenderCount : 0,


            showAddForm: false,
            openAddModal: false,
            isEdit : false,
            noContentMessage: "Are you the owner of a house and having outstanding loans or generating rental income from multiple house properties ? Declare it here and we will take care of your tax adjustments.",
            addEditSelfOccupiedRenderCount : 0,
            addEditRentedRenderCount : 0,

            //view documents
            openDocumentViewModal : false,
            isMultipleDocuments : true, 
            multiDocumentData: [],
            singleDocData: [],
            // edit data
            selectedProperty : {},
            autoTrigger : false,

            // delete data
            showMixedStatusRecordsDelPopup : false,
            showDeleteIcon : false,
            showValidStatusRecordsDelPopup : false,
            validRecordCount : 0,
            invalidRecordCount : 0,
            recordsToDelete : [],
            totalSelectedRecords : [],
            loadingScreen: false,
            loadingScreen : false,
            loadSkeleton: false,
            showConfirmationInSelfOccupied: false,
            showConfirmationInRented: false,
            maxLimitOfSelfOccupied: 0,

            // view tax sheet
            openTaxSheetModal : false,
            isMobile : false,
            taxSheetRenderCount : 0,

            // tax regime comparsion modal
            openTaxRegimeModal: false
        };
    },
    computed: {
        basePath() {
            if (localStorage.getItem('production') == 0) {
                return '/hrapponline/'
            } else {
                return '/'
            }
        },
        // show this letter avatar while profile is not provided by employee
        empNameLetterAvatar() {
            var firstNameChar = this.listResponse.empDetails.emp_first_name ? this.listResponse.empDetails.emp_first_name.charAt(0).toUpperCase() : '';
            var lastNameChar = this.listResponse.empDetails.emp_last_name ? this.listResponse.empDetails.emp_last_name.charAt(0).toUpperCase() : '';
            
            return firstNameChar + lastNameChar;
        },
        // tooltip messages for add button when it is disabled
        // enable add declarations button
        enableAddShowMsg() {
            let returnResponse = [];
            if (this.formSource === 'POI') {
                returnResponse = [false, ""];
            } else {
                if(this.itDeclarationSettingsStatus === "lock") {
                    returnResponse =  [false, "Tax declaration is locked and hence no amendments are allowed."]; 
                }
                else if (this.selectedAssessmentYear != this.currentAssessmentYear) {
                    returnResponse =  [false, "Assessment year is closed and hence no amendments are allowed."];
                } else if (!this.rolesResponse.Role_Add) {
                    returnResponse =  [false, "Sorry, you don't have access to add the declaration. Please contact your administrator."];
                } else if ((this.incomeSourceTypes && this.incomeSourceTypes.length > 0 && this.incomeSourceTypes[0].Max_No_Of_Property <= this.selfOccupiedInitialCount) && this.currentItem == 'tab-1') {
                    returnResponse =  [false, "Maximum limit has been reached to add your declarations."];
                } else {
                    returnResponse =  [true, ""];
                }
            }
            return returnResponse;
        },
        // avoid showing two add declarations button on screen
        showAddBtnInGrid() {
            if (this.currentItem === 'tab-1') {
                // to check self occupied record is available
                if(this.enableAddShowMsg[0] && this.listResponse.selfoccupiedrecords && this.listResponse.selfoccupiedrecords.length > 0)
                return true;
            }
            else {
                // to check rented record is available
                if(this.enableAddShowMsg[0] && this.listResponse.rentedpropertyrecords && this.listResponse.rentedpropertyrecords.length > 0)
                return true;
            }
            return false;
        },
        // taxable income of the Employee - form with currency symbol
        taxableIncome() {
            let empTaxIncome = 0 ;

            if(this.empTaxableIncome) {
                let taxableIncome = parseFloat(this.empTaxableIncome);

                if(Math.sign(this.empTaxableIncome) === -1) {
                   empTaxIncome = this.currencySymbol ? this.currencySymbol + ''  + '('+Math.abs(taxableIncome.toFixed(2))+')' : '('+Math.abs(taxableIncome.toFixed(2))+')'
                }
                else {
                    empTaxIncome = this.currencySymbol ? this.currencySymbol + ' ' + taxableIncome.toFixed(2) : taxableIncome.toFixed(2);
                }
            }
            else {
                empTaxIncome = '-';
            }
            return empTaxIncome;
        },
        // annual tax of the Employee - form with currency symbol
        annualTax() {
            let empTax = 0;

            if(this.empAnnualTax) {
                let empAnnualTax = parseFloat(this.empAnnualTax);
                empTax =  this.currencySymbol ? this.currencySymbol + ' ' + empAnnualTax.toFixed(2) : empAnnualTax.toFixed(2)
            }
            else {
                empTax = '-';
            }
            return empTax;
        },

        graphQl(){
            return graphql(this.taxDeclarationBaseUrl, {
                method: 'POST',
                headers: this.apiHeaders,
                asJSON: true
            });
        },

        enableApproveAllButton() {
            let returnResponse = [];
            if (this.formSource === 'POI') {
                if(!this.rolesResponse.Role_Optional_Choice) {
                    returnResponse = [false, "Sorry, you don't have access to approve the declaration. Please contact your administrator."];
                }
                else if(this.selectedAssessmentYear != this.currentAssessmentYear) {
                    returnResponse = [false, "Assessment year is closed and hence no approvals are allowed."];
                }
                else if(this.chosenStatus !== 'Applied') {
                    returnResponse = [false, "Only applied status declarations can be approved."];
                }
                else {
                    var selectedRecords = this.currentItem === 'tab-1' ? this.selfOccupiedSelectedRecords : this.rentedSelectedRecords;
                    if(selectedRecords.length == 0) {
                        returnResponse = [false, "For Approve, you have to select atleast one of the 'Applied' status record."];
                    }
                    returnResponse = [true, ""];                    
                }
            }
            return returnResponse;
        },
        //TDS sheet tooltip
        tdssheetTooltip() {
            return ((this.taxCalculationSheetData) ? ('TDS For '+ this.taxCalculationSheetData.Salary_Month) : 'TDS For the salary month');
        },
    },
    watch : {
        // watch if the tab is shifted
        currentItem(val) {
            let self = this;
            // check if the small grid is shown
            if(self.isSmall && self.showPropertyDetails && !self.autoTrigger) {
                // open and show the first record of particular tab
                if (val === 'tab-1') {
                    // to check self occupied record is available, else expand the grid as normal
                    self.fnCheckAvailability(self.listResponse.selfoccupiedrecords);
                }
                else {
                    // to check rented record is available, else expand the grid as normal
                    self.fnCheckAvailability(self.listResponse.rentedpropertyrecords);
                }
                
            }

            self.autoTrigger = false;
        },
        taxSheetData(val) {
            this.taxCalculationSheetData = val;
        }
    },
    created() {

        this.taxCalculationSheetData = this.taxSheetData; //assign the tax calculation sheet data
 

        if(this.formSource === 'POI' && this.chosenType === 'rented-property') {
            this.$nextTick(function () {
                this.currentItem = 'tab-2'
            });
        }
        // table items
        this.selfOccupiedTableItems = this.listResponse.selfoccupiedrecords;
        this.rentedTableItems = this.listResponse.rentedpropertyrecords;
        // table headers
        this.selfOccupiedHeader = this.selfOccupiedLargeTableHeaders;
        this.rentedHeaders = this.rentedLargeTableHeaders;
        // assign photo path variable in table items to display default avatar in table
        for (var i in this.selfOccupiedTableItems) {
            this.selfOccupiedTableItems[i]['photo_path'] = this.basePath + "vue/assets/images/self_occupied_avatar.webp";
        }    
        for (var i in this.rentedTableItems) {
            this.rentedTableItems[i]['photo_path'] = this.basePath + "vue/assets/images/rented_avatar.webp";
        }
    },
    mounted: function () {
        if (this.renderCount == 1) {
            let styleElem = document.createElement('style');
            styleElem.textContent = `
                .component-padding{
                    padding: 50px;
                    overflow-x: auto;
                }
                .emp-details-cls{
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                }
                .change-filter-fab-btn{
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                    padding: 30px !important;
                }
                .tab-card-cls{
                    padding: 6px;
                    margin: 10px;
                    height: 5em;
                    display: flex !important;
                    align-items: center;
                    background: #fbfbfb !important;
                }
                .emp-details-padding{
                    padding-top: 5px;
                }
                .add-declare-btn{
                    display: flex;
                    justify-content: flex-end;
                }
                .view-property-details {
                    margin-top : -7.5em
                }
                .change-emp-icon{
                    font-size: 20px !important;
                    padding-right: 10px;
                }
                .poi-statistics-view-back-btn-col {
                    padding-left: 2em !important;
                }
                .poi-statistics-view-back-btn {
                    font-size: 1.3em !important;
                    text-transform: initial !important;
                }
                .poi-statistics-view-back-icon {
                    margin-right: 5px;
                    margin-top: 4px;
                }
    
                @media screen and (max-width:600px)  {
                    .add-declare-btn{
                        justify-content: center !important;
                    }
                    .component-padding{
                        padding: 35px;
                    }
                    .v-data-table thead tr:last-child th {
                        display: flex !important;
                    }
                    .v-data-table thead tr:last-child th {
                        background: none !important;
                    }
                }
                @media screen and (max-width:960px)  {
                    .emp-details-cls{
                        padding: 20px !important;
                    }
                }
                .tab-action-btn{
                    margin-top: 15px;
                }
                .tab-action-btn-disabled {
                    cursor: not-allowed !important;
                    color: #949494 !important;
                }
                .v-data-table table {
                    padding: 0 10px !important;
                }
                .v-data-table{
                    background: #f1f1f1 !important;
                }
                .v-data-table table{
                    background: #f1f1f1;
                }
                .v-data-table elevation-1 theme--light{
                    box-shadow: none!important;
                }
                .v-data-table-header-mobile__wrapper .v-select {
                    display: none;
                }
                .v-data-table-header-mobile__select{
                    width: 100%;
                }
                .v-data-table-header-mobile__wrapper{
                    width: 100%;
                }
                .no-record-bg{
                    background: #f1f1f1;
                }
                .custom-table-headers {
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    max-width: 120px;
                    display: inline-block;
                }
                .annual-tax-and-regime-change-card{
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    width: 70px;
                    font-size: 14px;
                    color: gray;
                }
                @media screen and (max-width: 1464px) and (min-width: 1264px) {
                    .annual-tax-and-regime-change-card{
                        width: 55px !important;
                    }
                }
                @media screen and (max-width: 1264px) and (min-width: 960px) {
                    .annual-tax-and-regime-change-card{
                        width: 65px !important;
                    }
                }
                @media screen and (max-width: 960px) {
                    .annual-tax-and-regime-change-card{
                        width: 80px !important;
                    }
                }
            `;
            document.head.appendChild(styleElem);    
        }
        let styleElements = document.createElement('style');

        // check if the form source is income U/S 24, or if form source is POI wil Applied status records
        if((this.formSource === 'POI' && (this.rolesResponse.Role_Optional_Choice && this.chosenStatus === 'Applied')) 
            || this.formSource === 'IncomeUS24') {
            
            this.maxLimitOfSelfOccupied = this.incomeSourceTypes && this.incomeSourceTypes.length > 0 ? this.incomeSourceTypes[0].House_Loan_Interest_Max_Limit : 0;
            styleElements.textContent = `
                .v-data-table tbody .v-data-table__mobile-table-row td:nth-child(2) {
                    justify-content: flex-end;
                }
                .v-data-table tbody td:nth-child(2) .v-data-table__mobile-row__header {
                    display: none
                }
            `;
        }
        else {
            styleElements.textContent = `
                .v-data-table tbody .v-data-table__mobile-table-row td:nth-child(1) {
                    justify-content: flex-end;
                }
                .v-data-table tbody td:nth-child(1) .v-data-table__mobile-row__header {
                    display: none
                }
            `;
        
        }
        document.head.appendChild(styleElements);

        this.$nextTick(function () {
            window.addEventListener('resize', this.getWindowWidth);
            //Init
            this.getWindowWidth()
        });

    },
    beforeDestroy() {
        window.removeEventListener('resize', this.getWindowWidth);
        window.removeEventListener("scroll", this.onScroll)

    },
    methods: {
        closeTaxSheet() {
            this.openModal = false;
            this.openTaxSheetModal = false;
            this.$emit('open-close-view-modal',false)
        },

        taxSheetRendered(value) {
            this.loadingScreen = false;
            this.isMobile = false;

            if(value) {
                this.$emit('handle-tax-sheet-error')

            }
        },

        // view tds sheet
        downloadPdf() {
            if(this.windowWidth > 600) {
                this.openTaxSheetModal = true;
                this.$emit('open-close-view-modal',true)
            }
            else {
                this.loadingScreen = true;
                this.taxSheetRenderCount ++;
                this.isMobile = true;
            }
        },

        // check available of self-occupied/rented records while switching the tab in small view, if no records, show large grid
        fnCheckAvailability(records, action) {
            let self = this;
            var selectedId = '';
            if (records && records.length > 0) {
                var unique_id = self.currentItem == 'tab-1' ? 'self_occupied_property_id' : 'rented_Property_id';
                selectedId = records[0][unique_id];
                self.fnViewDeclaration(selectedId);
            } else {
                self.isSmall = false;
                self.showPropertyDetails = false;    
            }
        },
        // hide and show of filter based on window size
        getWindowWidth(event) {
            // check if the width of the screen is changed or not
            if(this.windowWidth !== document.documentElement.clientWidth) {
                
                this.windowWidth = document.documentElement.clientWidth;

                // check if property details is shown or not
                if(this.showPropertyDetails) {
                    // check the window width and show the details in a modal or in the right side
                    if (this.windowWidth <= 1263) {
                        this.openModal = true;
                        this.isSmall = false;
                        this.$emit('open-close-view-modal',true);

                    }
                    else {
                        this.openModal = false;
                        this.isSmall = true;
                        this.$emit('open-close-view-modal',false);

                    }
                }
                if (this.showAddForm) {
                    if (this.windowWidth <= 1263) {
                        this.openAddModal = true;
                        this.isSmall = false;
                        this.$emit('open-close-view-modal',true);
                    }
                    else {
                        this.openAddModal = false;
                        this.isSmall = true;
                        this.$emit('open-close-view-modal',false);
                    }
                }
                // check if the add success is returned to open the current tab as self occupied/rented 
                if(this.isAddSuccess || this.isEditSuccess) {

                    var selectedId = '';
                        // open and show the first record of particular tab
                        if(this.successType == 'selfOccupied') {
                            selectedId = this.isEditSuccess ? this.successData.Self_Occupied_Property_Id : this.listResponse.selfoccupiedrecords[0].self_occupied_property_id;
                        }
                        else {
                            this.autoTrigger = true;
                            selectedId = this.isEditSuccess ? this.successData.Rented_Property_Id : this.listResponse.rentedpropertyrecords[0].rented_Property_id ;
                            this.$nextTick(function () {
                                this.currentItem = 'tab-2'
                            });
                        }
                        this.fnViewDeclaration(selectedId);
                }
                else if(this.successData.isDelete || this.successData.isApproval) {
                    // check if the delete is made in rented and redirect to the rented tab.
                    if(this.successType === 'rented') {
                        this.$nextTick(function () {
                            this.currentItem = 'tab-2'
                        });
                    }
                }
            }
        },

        // Add declaration button is clicked
        fnAddDeclaration() {

            this.showPropertyDetails = false;
            this.openModal = false;
            this.isEdit = false;
            this.currentItem === 'tab-1' ? this.addEditSelfOccupiedRenderCount++ : this.addEditRentedRenderCount++;
            
            // has to move this to the individual record view
            if(this.windowWidth <=1263) {
                this.isSmall = false;
                this.openAddModal = true;
                this.$emit('open-close-view-modal',true);

                this.selfOccupiedHeader = this.selfOccupiedLargeTableHeaders;
                this.rentedHeaders = this.rentedLargeTableHeaders;
            }
            else {
                this.isSmall = true;
                this.openAddModal = false;
                this.$emit('open-close-view-modal',false);

                this.selfOccupiedHeader = this.selfOccupiedSmallTableHeaders;
                this.rentedHeaders = this.rentedSmallTableHeaders;
            }

            this.showAddForm = true;

        },
        fnViewDeclaration(selectedId) {
            this.showAddForm = false;
            this.openAddModal = false;

            this.selectedItem = selectedId;

            this.currentItem === 'tab-1' ? this.viewSelfOccupiedRenderCount++ : this.viewRentedRenderCount++;
            
            // // has to move this to the individual record view
            if(this.windowWidth <=1263) {
                this.isSmall = false;
                this.openModal = true;
                this.$emit('open-close-view-modal',true);

                this.selfOccupiedHeader = this.selfOccupiedLargeTableHeaders;
                this.rentedHeaders = this.rentedLargeTableHeaders;
            }
            else {
                this.isSmall = true;
                this.openModal = false;
                this.$emit('open-close-view-modal',false);

                this.selfOccupiedHeader = this.selfOccupiedSmallTableHeaders;
                this.rentedHeaders = this.rentedSmallTableHeaders;
            }
    
            this.showPropertyDetails = true;
        },

        onScroll(e) {
            this.windowTop = window.top.scrollY; /* or: e.target.documentElement.scrollTop */
        },

         // close the self occupied/rented view form 
        closeViewPropertyDetails() {
            window.scrollTo(0,0);
            this.openModal = false;
            this.isSmall = false;
            this.showPropertyDetails = false;
            this.selfOccupiedHeader = this.selfOccupiedLargeTableHeaders;
            this.rentedHeaders = this.rentedLargeTableHeaders;
            this.selectedItem = 0;
            this.openCloseViewModal(false);
        },

        // handle error from view page
        handleError(err) {
            // check the error code
            if (err[0]) {
                // error returned from backend
                var error = JSON.parse(err[0].message);
                var errorCode = error.errorCode;
                // if the error is access denied , then close the view page
                if(errorCode === 'DB0100') {
                    this.closeViewPropertyDetails();
                }
            }
    
            this.$emit('handle-error', err);
        },
        // open doc viewer function
        openDocViewer(docParam) {
            // check document count is greater than zero
            if (docParam[1] && docParam[1].length) {
                // check tab is self occupied / rented
                if(this.currentItem === 'tab-1') {
                    var chosenDocument = [];
                    // loop through the list data and get selected doc details
                    for(var i in this.listResponse.selfoccupiedrecords) {
                        if(this.listResponse.selfoccupiedrecords[i].self_occupied_property_id  == docParam[0]) {
                            chosenDocument = this.listResponse.selfoccupiedrecords[i].documents;
                            break;
                        }
                    }
                    // check the uploaded doc count is one. If yes open single doc viewer
                    if (chosenDocument.length === 1) {
                        this.isMultipleDocuments = false;
                        var docData = this.formDocumentData(chosenDocument);
                        this.singleDocData = docData[0];
                    } else {
                        this.isMultipleDocuments = true;
                        this.multiDocumentData =  this.formDocumentData(chosenDocument);  
                    }
                    this.openDocumentViewModal = true;              
                }
                else {
                    var chosenDocument = [];
                    // loop through the list data and get selected doc details
                    for(var i in this.listResponse.rentedpropertyrecords) {
                        if(this.listResponse.rentedpropertyrecords[i].rented_Property_id  == docParam[0]) {
                            chosenDocument = this.listResponse.rentedpropertyrecords[i].documents;
                            break;
                        }
                    }
                    // check the uploaded doc count is one. If yes open single doc viewer
                    if (chosenDocument.length === 1) {
                        this.isMultipleDocuments = false;
                        var docData = this.formDocumentData(chosenDocument);
                        this.singleDocData = docData[0];
                    } else {
                        this.isMultipleDocuments = true;
                        this.multiDocumentData =  this.formDocumentData(chosenDocument);  
                    }
                    this.openDocumentViewModal = true;              
                }
            }

        },

        // form the document data
        formDocumentData(chosenDocument) {
            var docData = [];
    
                // split and get the doc name
                for (var i in chosenDocument) {
                    var docName = chosenDocument[i].Document_Name;
    
                    docName = docName.split('?');
                    docName = docName[docName.length - 1];

                    var docType = docName.split('.');
                    docType = docType[docType.length-1];
    
                    docData.push({
                        Doc_Id        : i,
                        Document_Name : docName,
                        Document_Type : docType,
                        S3_File_Name : chosenDocument[i].S3_File_Name,
                        Document_Size : chosenDocument[i].Document_Size,
                    })
                }
    
                return docData;
        },

        // handle error from document viewer
        handleDocumentError(fetchPresignedUrlError) {
            // close doc viewer
            this.openDocumentViewModal = false;
            // handle error
            this.$emit('handle-document-error',fetchPresignedUrlError);
        },

        openCloseViewModal(value) 
        {
            value ? this.$emit('open-close-view-modal',true) : (this.openModal ? this.$emit('open-close-view-modal',true) :  this.$emit('open-close-view-modal',false) ,this.openDocumentViewModal = false) ;
        },

        // close Add/Edit form
        closeAddPropertyDetails(action='') {
            // if the error is in edit form and not bcos of set lock, show the view form
            if(this.isEdit && action != 'clearLock') {
                this.showAddForm = false;
                this.openAddModal = false;

                this.selectedItem = this.currentItem === 'tab-1' ? this.selectedProperty.Self_Occupied_Property_Id : this.selectedProperty.Rented_Property_Id;

                this.currentItem === 'tab-1' ? this.viewSelfOccupiedRenderCount++ : this.viewRentedRenderCount++;
                
                // // has to move this to the individual record view
                if(this.windowWidth <=1263) {
                    this.isSmall = false;
                    this.openModal = true;
                    this.$emit('open-close-view-modal',true);

                    this.selfOccupiedHeader = this.selfOccupiedLargeTableHeaders;
                    this.rentedHeaders = this.rentedLargeTableHeaders;
                }
                else {
                    this.isSmall = true;
                    this.openModal = false;
                    this.$emit('open-close-view-modal',false);

                    this.selfOccupiedHeader = this.selfOccupiedSmallTableHeaders;
                    this.rentedHeaders = this.rentedSmallTableHeaders;
                }
        
                this.showPropertyDetails = true;
            
            }
            // if the error is in Add/Edit
            else {
                window.scrollTo(0,0);
                this.openAddModal = false;
                this.isSmall = false;
                this.showAddForm = false;
                this.selfOccupiedHeader = this.selfOccupiedLargeTableHeaders;
                this.rentedHeaders = this.rentedLargeTableHeaders;
                this.isEdit = false;
    
                this.openCloseViewModal(false);
            }
        },

        // handle error in Add/Edit
        handleAddEditError(errorEvent) {
            var action = errorEvent[1];
            var addEditError = errorEvent[0];
            
            this.closeAddPropertyDetails(action);
               
            this.$emit('handle-add-edit-error', [addEditError, action]);

        },

        // handle success from the add/edit
        handleAddEditSuccess(successEvent) {
            // if edit success, this pre filled data is used to show the data in view
            var successData = this.selectedProperty;
            successEvent.push(successData);
            this.$emit('handle-add-edit-success',successEvent);
        },

        // handle the error in edit set lock
        handleViewEditError(event) {
            var success = event[0];
            var successOrErrorData = event[1];

            // check if there is any error in opening th edit form from the view form
            if(success) {
                // show the view form
                this.showPropertyDetails = false;
                this.openModal = false;

                this.isEdit = true;
                this.currentItem === 'tab-1' ? this.addEditSelfOccupiedRenderCount++ : this.addEditRentedRenderCount++;
            
                // has to move this to the individual record view
                if(this.windowWidth <=1263) {
                    this.isSmall = false;
                    this.openAddModal = true;
                    this.$emit('open-close-view-modal',true);

                    this.selfOccupiedHeader = this.selfOccupiedLargeTableHeaders;
                    this.rentedHeaders = this.rentedLargeTableHeaders;
                }
                else {
                    this.isSmall = true;
                    this.openAddModal = false;
                    this.$emit('open-close-view-modal',false);

                    this.selfOccupiedHeader = this.selfOccupiedSmallTableHeaders;
                    this.rentedHeaders = this.rentedSmallTableHeaders;
                }

                this.selectedProperty = successOrErrorData;
                this.showAddForm = true;

            }
            else {
                // if error, handle it
                this.$emit('handle-error',successOrErrorData);

            }
        },

        // delete declarations
        deleteDeclarations(selectedRecords,declarationIdToDelete,validCount,invalidCount) {
            this.actionType = 'delete';
            this.totalSelectedRecords = selectedRecords;
            this.validRecordCount = validCount;
            this.invalidRecordCount = invalidCount;
            this.recordsToDelete = declarationIdToDelete;

            // check if any invalid records chosen and open the respective popup
            if(this.invalidRecordCount > 0) {
                this.showMixedStatusRecordsDelPopup =true;
                this.openCloseViewModal(true);
            }
            else {
                this.showValidStatusRecordsDelPopup = true;
                this.openCloseViewModal(true);

            }
        },

        // confirm delete
        fnConfirmDelete() {

            let self = this;

            self.loadingScreen = true;

            try {
                var deletePropertyDeclarations = self.graphQl(`mutation deleteHouseProperties($sourceType:String!,$declarationPropertyId:[Int]!) { deleteHouseProperties(sourceType:$sourceType,declarationPropertyId:$declarationPropertyId) { errorCode message } }`);
                deletePropertyDeclarations({
                    "sourceType": self.currentItem == 'tab-1' ? 'self-occupied-property' : 'rented-property',
                    "declarationPropertyId" : self.recordsToDelete,
                }).then(deletedResponse => {
                    self.loadingScreen = false;
                    var successType = self.currentItem == 'tab-1' ? 'selfOccupied' : 'rented';
                    self.$emit('handle-add-edit-success',[successType,'delete']);            
                }).catch(deleteRecordsError => {
                    self.loadingScreen = false;
                    self.$emit('handle-error', deleteRecordsError);
                });
            }
            catch(deleteRecordsError) {
                self.loadingScreen = false;
                self.$emit('handle-error', deleteRecordsError);
            }

            self.showMixedStatusRecordsDelPopup =false;
            self.showValidStatusRecordsDelPopup = false;
            self.openCloseViewModal(false);
    


        },

        // close the delete popup
        fnCancelModal() {
            this.openCloseViewModal(false);
            this.showMixedStatusRecordsDelPopup =false;
            this.showValidStatusRecordsDelPopup = false;
        },

        // to chose any other employee
        changeEmployee() {
            this.closeViewPropertyDetails();
            var propertyType = this.formSource === 'POI' ? (this.currentItem === 'tab-1' ? 'self-occupied-property' : 'rented-property') : 'get-all-employees';
            var statusType = this.formSource === 'POI' ? this.chosenStatus : '';
            var empIds = this.formSource === 'POI' ? this.chosenEmpIds : [];
            this.isSmall = false;
            this.showPropertyDetails = false;
            this.$emit('change-employee', [statusType, empIds, propertyType]);
        },
        
        // approve all declarations button click function
        fnApproveAllDeclarations() {
            this.actionType = 'approve';
            var declarationToApprove = this.currentItem == 'tab-1' ? this.selfOccupiedSelectedRecords : this.rentedSelectedRecords;
            this.showConfirmationInSelfOccupied = true; this.showConfirmationInRented = true;

            for (var i in declarationToApprove) { 
                if (declarationToApprove[i].approved_amount) {
                    var amt = parseFloat(declarationToApprove[i].approved_amount);
                    if (this.currentItem === 'tab-1' && !(amt >= 0 && amt <= this.maxLimitOfSelfOccupied)) {
                        this.showConfirmationInSelfOccupied = false;
                        break;
                    } else  if( !(amt >= -9999999999999.99 && amt <= 9999999999999.99) ){
                        this.showConfirmationInRented = false;
                        break;
                    }  
                }
            }

            var defineItem = this.currentItem === 'tab-1' ? this.showConfirmationInSelfOccupied : this.showConfirmationInRented;

            if (defineItem) {
                this.showValidStatusRecordsDelPopup = true;
                this.openCloseViewModal(true);
            }else {
                var range = this.currentItem === 'tab-1' ? '0 to ' + this.maxLimitOfSelfOccupied + '.' : '-9999999999999.99 to 9999999999999.99.';
                this.$emit('handle-warning-msg', "Please check you have entered a valid Approval amount. The Approval amount range should be between " + range)
            }
        },


        // triggered when multiple approval process
        fnStatusApprovalMultiple() {
            let self = this;
            self.fnCancelModal();
            self.loadSkeleton = true;
            var declarationId = self.currentItem == 'tab-1' ? 'self_occupied_property_id' : 'rented_Property_id';
            var declarationToApprove = self.currentItem == 'tab-1' ? self.selfOccupiedSelectedRecords : self.rentedSelectedRecords;
            try {
                var approvalRecDetails = [];
                for (var i in declarationToApprove) {
                    var approvalRecDetail = {
                        declarationId: declarationToApprove[i][declarationId],
                        approvedAmount: parseFloat(!declarationToApprove[i].approved_amount ? declarationToApprove[i]['house_property_income_loss'] : declarationToApprove[i].approved_amount),
                        approvalStatus: "Approved", // multiple only for approval. so always this is Approved
                        comment: "", // multiple only for approval. so always this is empty
                    }
                    approvalRecDetails.push(approvalRecDetail);
                }
                self.fnProceedStatusApprovalActions(approvalRecDetails, 'Approved');
            }
            catch (actionError) {
                self.loadingScreen = false;
                self.$emit('handle-error', actionError);
            }
        },
        // triggered when single approval process
        fnStatusApprovalSingle(actionParam) {
            if (actionParam[4] === 'grid') {
                this.loadSkeleton = true;
            }
            var approvalRecDetail = [{
                declarationId: actionParam[0],
                approvedAmount: parseFloat(actionParam[1]),
                approvalStatus: actionParam[2],
                comment: actionParam[3],
            }]
            this.fnProceedStatusApprovalActions(approvalRecDetail, actionParam[2]);
        },
        fnProceedStatusApprovalActions(approvalRecDetails, type) {
            let self = this;
            try {
                var formRef = self.currentItem === 'tab-1' ? 'viewSelfOccupiedForm' : 'viewRentedForm';
                var statusApprovalActions = self.graphQl(`mutation (
                    $declarationType:String!,
                    $declarationSourceType:String!,
                    $approvalRecDetails:[approvalRecDetails]!) {
                        poiStatusUpdate
                            (
                                declarationType:$declarationType,
                                declarationSourceType:$declarationSourceType,
                                approvalRecDetails:$approvalRecDetails
                            )
                            { errorCode message validationError}
                    }`);
                
                statusApprovalActions({
                    "approvalRecDetails": approvalRecDetails,
                    "declarationSourceType": self.currentItem == 'tab-1' ? 'self-occupied-property' : 'rented-property',
                    "declarationType": "Income Under Section24"
                }).then(actionResponse => {
                    var defineMsg = (approvalRecDetails && approvalRecDetails.length > 1) ? 'Declarations' : 'Declaration';
                    var message = defineMsg + ' ' + type + ' successfully';
                    var successType = self.currentItem == 'tab-1' ? 'selfOccupied' : 'rented';
                    self.$emit('handle-approval-success', [message, successType]);
                    self.isSmall = false;
                    self.loadSkeleton = false;
                }).catch(actionError => {
                    self.loadSkeleton = false;
                    self.handleStatusUpdateError(actionError);
                    // set loading as false in child component using refs
                    if (self.$refs[formRef]) {
                        self.$refs[formRef].isLoading = false;
                    }
                });
            }
            catch (actionError) {
                self.loadSkeleton = false;
                self.handleStatusUpdateError();
                // set loading as false in child component using refs
                if (self.$refs[formRef]) {
                    self.$refs[formRef].isLoading = false;
                }
            }
        },

         // handle status approval error from backend
         handleStatusUpdateError(error = "") {
            if(error && error.length > 0 && error[0] && error[0].message) {
                // error returned from backend
                var approvalActionError = JSON.parse(error[0].message);
                var errorCode = approvalActionError.errorCode;
  
                // check if the error code is validation error
                if(errorCode === 'IVE0000') {

                    var validationError = error.validationError;
                    var validationErrorCode = Object.keys(validationError);

                    switch (validationErrorCode) {
                        case 'IVE0035':
                            // approval amount
                            this.$emit('handle-warning-msg', "Please enter values between -9999999999999.99 to 9999999999999.99");
                        break;
                        case 'IVE0036':
                            // approval amount
                            this.$emit('handle-warning-msg', "Please enter values between -9999999999999.99 to 0.");
                            break;
                        case 'IVE0037':
                            // comment validation
                            this.$emit('handle-warning-msg', "Invalid comment.");
                        break;
                        case 'IVE0029':
                        case 'IVE0031':
                        case 'IVE0032':
                        case 'IVE0033':
                        case 'IVE0034':
                        default:
                            this.$emit('handle-warning-msg', "Oops ! Sorry, This issue seems to be from our side. Would you mind trying it after some time or talk to your administrator?");
                            break;
                    }
                    
                }
                else {
                    this.$emit('handle-error', error);
                }
            }

        },

        // open tax regime comparison modal
        showTaxRegimeComparisonModal() {
            this.openTaxRegimeModal = true;
            this.$emit('open-close-view-modal',true)
        },

        //open tax regime comparison modal (old/new)
        showCompareTaxRegimeModal(value) {
            this.$emit("handle-tax-compare", value);
        },

        // close the tax regime comparison modal
        closeTaxComparisonModal() {
            this.openTaxRegimeModal = false;
            this.$emit('open-close-view-modal',false)
        },

        //send the tax regime update response back to the parent
        handleTaxRegimeResponse(responseEvent){
            this.$emit('handle-tax-regime-update-response',responseEvent);
        }
    },
});

// compare two objects function
Object.compare = function (obj1, obj2) {
	//Loop through properties in object 1
	for (var p in obj1) {
		//Check property exists on both objects
		if (obj1.hasOwnProperty(p) !== obj2.hasOwnProperty(p)) return false;
 
		switch (typeof (obj1[p])) {
			//Deep compare objects
			case 'object':
				if (!Object.compare(obj1[p], obj2[p])) return false;
				break;
			//Compare function code
			case 'function':
				if (typeof (obj2[p]) == 'undefined' || (p != 'compare' && obj1[p].toString() != obj2[p].toString())) return false;
				break;
			//Compare values
			default:
				if (obj1[p] != obj2[p]) return false;
		}
	}
 
	//Check object 2 for any extra properties
	for (var p in obj2) {
		if (typeof (obj1[p]) == 'undefined') return false;
	}
	return true;
};

 // form the file Size of the file
 getFileSize = function (originalSize) {
    /**to find the size of file in Bytes,KB,MB or GB */
    var _size = originalSize;
    var fSExt = new Array('Bytes', 'KB', 'MB', 'GB'),
        i = 0;
    while (_size > 900) {
        _size /= 1000;
        i++;
    }
    var fileSize = Math.round(_size * 100) / 100;
    fileSize = fileSize.toFixed(1) + ' ' + fSExt[i];

    return fileSize;
};
