$(function () {
    /* Alert when the DataSetup is not completed */
    dataSetup('datasetup');
    $.fn.dataTable.ext.errMode = 'none';

    // trigger close confirmation model when any change occur in form
    var isDirtyFormPayslipTemplate = false;

    // payslip type
    var payslipType = 'Monthly';

    // default image for logo
    var image_path = pageUrl() + "images/payslip_template_default_logo.png";

    // uploaded file content
    let fileResult;

    // define grapesjs editor globaly
    var editor;
    // get domainName and orgCode
    var domainName = fnIsDomain(),
        orgCode = fngetOrgCode(),
        domainName = domainName.split('.')[0], fileData, templateOperationType;

    // to find availability of monthly/hourly payslip templates
    let monthlyPayslipTemplateAvailable = false, hourlyPayslipTemplateAvailable = false;

    /** Create Payslip Template Grid**/
    var tablePayslipTemplate = $('#tablePayslipTemplate').dataTable({
        "lengthMenu": [5, 10, 25, 50, 100],
        "iDisplayLength": 10,
        "bDestroy": true,
        "bAutoWidth": false,
        "bServerSide": true,
        "bDeferRender": true,
        "sServerMethod": "POST",
        "sAjaxSource": pageUrl() + "payroll/payslip-template/list-payslip-template",
        "sAjaxDataProp": "aaData",
        "aaSorting": [[0, 'asc']],
        "fnRowCallback": function (nRow, aData, iDisplayIndex) {
            if (aData.Payslip_Type === 'Monthly') {
                monthlyPayslipTemplateAvailable = true; // set true when the monthly payslip template is available
            } else if(aData.Payslip_Type === 'Hourly') {
                hourlyPayslipTemplateAvailable = true; // set true when the hourly payslip template is available
            }
        },
        "fnCreatedRow": function (nRow, aData, iDataIndex) {
            $(nRow).attr({"data-toggle": "context", "data-target": "#PayslipTemplate-context-menu" });
        },
        "aoColumns": [
            {
                "mData": function (row, type, set) {
                    if (row.Set_As_Default === '1') {
                        return `<div>${row['Template_Name']} <span class="payslip-template-default-tag">Default</span></div>`;
                    } else {
                        return `<div>${row['Template_Name']}</div>`;
                    }
                }
            },
            {
                "mData" : function (row, type, set) {
                    return '<div>'+ row['Payslip_Type'] +'</div>';
                }
            },
            {
                "mData": function (row, type, set) {
                    return '<div>' + fnCheckNull(row['Service_Provider_Name']) + '</div>';
                }
            }]
    });

    // when the field force is enabled we need to display the service provider column else we need need to hide that column
    if ($('#fieldForce').val() == 1) 
    {
        tablePayslipTemplate.fnSetColumnVis(2, true);
    }
    else
    {
        tablePayslipTemplate.fnSetColumnVis(2, false);
    }

    /*  Add event listener for select and unselect details  */
    $(document).on('click contextmenu', '#tablePayslipTemplate tbody td div', function () {
        var selectRow = $(this).parent().parent();
        tablePayslipTemplate.$('tr.row_selected').removeClass('row_selected');
        if (!selectRow.hasClass('row_selected')) {
            selectRow.addClass('row_selected');
            fnActionButtonsPayslipTemplate(true);
        }
        else {
            fnActionButtonsPayslipTemplate(false);
        }
    });
    /**
     *  Grid Refresh
    */
    $('#gridPanelPayslipTemplate .panel-reload').on('click', function () {
        fnRefreshTable(tablePayslipTemplate);
    });
    // click event for view payslip template
    $('#viewPayslipTemplate, #viewContextPayslipTemplate').on('click', function () {
        var selectedRow = fnGetSelected(tablePayslipTemplate);
        var record = tablePayslipTemplate.fnGetData(selectedRow[0]);
        view_pdf_payslip_template(null, 'Xxx yyy', record.Template_Id, 'view', fnGetResult(), "payslip template",record.Payslip_Type,domainName, orgCode);
    });
    $('#exitPreview').on('click', function () {
        $('#the-canvas').addClass('hidden');
    })
    //click event for add template button
    $('#addPayslipTemplate').on('click', function () {
        addUpdatePayslipTemplate();
    });
    // function triggered when add payslip template button clicked after retrieved report logo
    function addUpdatePayslipTemplate() {
        var winSize = $(window).width();
        $(window).on('resize', function () {
            winSize = $(window).width();
        });
        if (winSize < 1270) {
            $('#modalWarningPayslipTemplate').modal('show');
        }
        else {
            // hide view form 
            $('#viewFormPayslipTemplate').html('');
            // reset form
            $('#addEditFormPayslipTemplate').validate().resetForm();
            // clear templateName because Previous templateName pre-filled 
            $('#payslipTemplateName').val('');
            // assign a initial value to template id
            $('#formPayslipTemplateId').val(0);
            // enable / disable payslip type based on the previously added payslip template
            $('#s2id_filterPayslipType').select2('val', "Monthly");
            $('#s2id_formServiceProvider').select2('val', '');
            $('#modalFormPayslipTemplate').modal('show')
            // assign modal title
            $('#modalFormPayslipTemplate .modal-title').html("<strong>Add</strong> " + $('#lblFormNameA').html());
            // show add form
            $('#addEditFormPayslipTemplate').show();
        }
    }
    //click event for set as default template button
    $('#setAsDefaultTemplate').on('click', function () { 
        var selectedRow = fnGetSelected(tablePayslipTemplate);
        if (selectedRow.length) {
            setMask('#wholepage');
            var record = tablePayslipTemplate.fnGetData(selectedRow[0]);
            $.ajax({
                type: 'POST',
                async: true,
                dataType: "json",
                url: pageUrl() + 'payroll/payslip-template/set-as-default-template',
                data: {
                    templateId: record.Template_Id,
                    payslipType: record.Payslip_Type,
                    serviceProviderId: record.Service_Provider_Id,
                },
                success: function (result) {
                    if (result && isJson(result)) {
                        if (result.success) {
                            fnRefreshTable(tablePayslipTemplate); 
                        }
                        jAlert({ msg: result.msg, type: result.type });
                    } else {
                        jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                    }
                    removeMask();
                }, error: function (deleteErrorResult) {
                    if (deleteErrorResult.status == 200) {
                        sessionExpired();
                    } else {
                        /* To handle internal server error */
                        jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                    }
                    removeMask();
                }
            });
        } else {
            jAlert({ msg: "Kindly select the record to set as default", type: "warning" });
        }
    })
    // click event in close confirmation yes button 
    $('#editCloseConfirmPayslipTemplate, #warningConfirmPayslipTemplate').on('click', function () {
        $('#grapesJsModule').addClass('hidden');
        image_path = pageUrl() + "images/payslip_template_default_logo.png"; // reset image
        logoUploadTriggered = false; // reset to false
        fnCloseTemplate(true);
    });
    /** Hide Expense Type Modal **/
    $('#modalFormPayslipTemplate').on('hide.bs.modal', function (e) {
        if (isDirtyFormPayslipTemplate) {
            e.preventDefault();
            e.stopImmediatePropagation();
            $('#modalDirtyPayslipTemplate').modal('toggle');
        }
        else {
            fnCloseTemplate(false);
        }
    });
    // On form field change in add template name modal
    $('#addEditFormPayslipTemplate').on('change', function () {
        isDirtyFormPayslipTemplate = true;
    });
    // click event for create buttn in add template modal
    $('#formCreatePayslipTemplate').on('click', function () {
        var l = Ladda.create(this);
        getReportLogoPath(function (result){
            if (result === 'success'){
                // for loading in button
                l.start();
                setMask('#wholepage');
                // if changes occur
                if (isDirtyFormPayslipTemplate) {
                    // validate add form
                    if ($("#addEditFormPayslipTemplate").valid()) {
                        fnDefineAction('add');
                        isDirtyFormPayslipTemplate = false;
                        $('#modalFormPayslipTemplate').modal('hide');
                    }
                    l.stop();
                    removeMask();
                }
                // if form has no changes
                else {
                    // stop loader in button
                    l.stop();
                    removeMask();
                    jAlert({ panel: $('#addEditFormPayslipTemplate'), msg: 'Form has no changes', type: 'info' });
                }
            }
        })
    })

    // click event for edit payslip template button
    $('#editPayslipTemplate, #editContextPayslipTemplate').on('click', function () {
        // retrieve report logo url
        getReportLogoPath(function (result) {
            if (result === 'success') {
                $('#formPayslipTemplateId').val(0);
                fnDefineAction('edit');
            }
        })
    });
    // function retrieve report logo path 
    function getReportLogoPath(callback) {
        if ($('#fieldForce').val() == 1) {
            var selectedRow = fnGetSelected(tablePayslipTemplate);
            if (selectedRow.length) {
                var record = tablePayslipTemplate.fnGetData(selectedRow[0]);
                var serviceProviderId = record.Service_Provider_Id;
            }
            else
            {
                var serviceProviderId = $('#s2id_formServiceProvider').select2('val');
            }
        }
        else
        {
            var serviceProviderId = 0;
        }
        
        setMask('#wholepage');
        $.ajax({
            type: 'POST',
            async: true,
            dataType: "json",
            url: pageUrl() + 'payroll/payslip-template/get-report-logo-path',
            data: {
                serviceProviderId: serviceProviderId,
            },
            success: function (logoPathResult) {
                if (logoPathResult) {
                    // in payslip template editor, image is saved as base64, so we also conver s3 public url to base64
                    getBase64Image(logoPathResult, function (result) {
                        image_path = "data:image/png;base64," + result;
                        callback('success');
                    });
                } else {
                    callback('success'); // return success when report logo not exist
                }                
                removeMask();
            }, error: function (logoPathError) {
                if (logoPathError.status == 200) {
                    sessionExpired();
                } else {
                    /* To handle internal server error */
                    jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                }
                removeMask();
            }
        });
    }
    // function used to convert remote image url to base64
    function getBase64Image(imgUrl, callback) {

        var img = new Image();
    
        // onload fires when the image is fully loadded, and has width and height
        img.onload = function(){
          var canvas = document.createElement("canvas");
          canvas.width = img.width;
          canvas.height = img.height;
          var ctx = canvas.getContext("2d");
          ctx.drawImage(img, 0, 0);
          var dataURL = canvas.toDataURL("image/png"),
              dataURL = dataURL.replace(/^data:image\/(png|jpg);base64,/, "");
          callback(dataURL); // the base64 string
        };
    
        // set attributes and src 
        img.setAttribute('crossOrigin', 'anonymous'); //
        img.src = imgUrl;
    
    }
    // define globally to avoid dublication
    let fileExtension = '';
    // function to base64 to file object, to upload the file object into s3
    function dataURLtoFile(dataurl, callback) {
 
        var arr = dataurl.split(','),
            mime = arr[0].match(/:(.*?);/)[1],
            bstr = atob(arr[1]), 
            n = bstr.length, 
            u8arr = new Uint8Array(n);
        
        while(n--){
            u8arr[n] = bstr.charCodeAt(n);
        }
        
        fileExtension = arr[0].replace("data:image/", '').replace(";base64", '');
        let fileName = "Report_LogoPath." + fileExtension; // file extention is vary, but the file name is constant

        // uploaded image in payslip template is base64 format. but we need to pass file object to s3. 
        fileResult = new File([u8arr], fileName, {type:mime});
    }
    
    // called when the image uploaded in tha template editor while cliking the image in assest manager in grapes js module
    $(document).on('dblclick', '.gjs-am-asset-image', function () {
        var imageBase64 = $('.gjs-am-preview').css('background-image');
        imageBase64 = imageBase64.replace('url(', '').replace(')', '').replace(/\"/gi, "");

        dataURLtoFile(imageBase64);
        
        // show warning popup, when they upload report logo in payslip template module
        $('#modalWarningUploadReportLogo').modal('show');
    });
    // to check logo upload action is triggered or not
    let logoUploadTriggered = false;
    // click ok in warning popup, we have to upload report logo in payslip template form
    $('#warningUploadReportLogo').on('click', function () {
        uploadReportLogo();
    });
    /** when the warning modal is closed we have to reset the image as previous one **/
    $('#modalWarningUploadReportLogo').on('hide.bs.modal', function (e) {
        if(!logoUploadTriggered) // reset previous image if upload action is not triggered while the warning modal is closed
            fnSetPreviousImage(); // set previous image in editor if any error occur while uploading
    });
    // upload report logo in s3 and update filename in organization settings form
    function uploadReportLogo() {
        if (fileResult) {
            logoUploadTriggered = true;
            setMask('#wholePage');
            let reportLogoFileName;
            if ($('#fieldForce').val() == 1) {
                var ts = Math.round((new Date()).getTime() / 1000);
                var selectedRow = fnGetSelected(tablePayslipTemplate);
                if (selectedRow.length) {
                    var record = tablePayslipTemplate.fnGetData(selectedRow[0]);
                    var serviceProviderId = record.Service_Provider_Id;
                }
                else
                {
                    var serviceProviderId = $('#s2id_formServiceProvider').select2('val');
                }
                reportLogoFileName = ts + "." +fileExtension;
                // get presigned put url
                var sUrl = fngetSignedPutUrl("hrapp_upload/" + orgCode + "_tmp/logos/service_provider/" + reportLogoFileName, 'logoBucket')    
            }
            else
            {
                reportLogoFileName = orgCode + "-Report_LogoPath" + "." + fileExtension;
                // get presigned put url
                var sUrl = fngetSignedPutUrl("hrapp_upload/" + orgCode + "_tmp/logos/" + reportLogoFileName, 'logoBucket')
                var serviceProviderId = 0;
            }
            deletePhpActionHeaders();
            if (sUrl && sUrl !== 'sessionexpired' && sUrl !== 'internalerror') {
                $.ajax({
                    url: sUrl, //update in s3
                    type: 'PUT',
                    contentType: fileResult.type,
                    processData: false,
                    data: fileResult,
                    async: true
                }).success(function (updateErr, updateresult) {
                    if (updateresult === "success") {
                        $.ajax({
                            async: true,
                            type: 'POST',
                            dataType: 'json',
                            url: pageUrl() + "organization/organization-settings/update-logo-name/", // update in table
                            data: {
                                uploadSuccessFilesArr: reportLogoFileName,
                                serviceProviderId:serviceProviderId
                            },
                            success: function (updateresult) {
                                if (updateresult) {
                                    jAlert({ msg: "Report logo updated successfully", type: "success" }); 
                                } else {
                                    fnSetPreviousImage(); // set previous image in editor if any error occur while uploading
                                    jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                                }
                                removeMask();
                            },
                            error: function (updateLogoError) {
                                fnSetPreviousImage(); // set previous image in editor if any error occur while uploading
                                if (updateLogoError.status == 200) {
                                    sessionExpired();
                                } else {
                                    /* To handle internal server error */
                                    jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                                }
                                removeMask();
                            }
                        });
                    }
                    else {
                        jAlert({ msg: "Unable to upload report logo", type: updateresult.type });
                        fnSetPreviousImage(); // set previous image in editor if any error occur while uploading
                        removeMask();
                    }
                 })
            } else {
                jAlert({ msg: "Unable to upload report logo", type: "warning" });
                fnSetPreviousImage(); // set previous image in editor if any error occur while uploading
            }            
                
            setPhpActionHeaders();
        } else {
            jAlert({ msg: "Unable to upload report logo", type: "warning" });
            fnSetPreviousImage(); // set previous image in editor if any error occur while uploading
        }
    }
    // set previous image in editor if any error occur while uploading
    function fnSetPreviousImage(){
        editor.DomComponents.getWrapper().find('#payslipLogo')[0] ?
            editor.DomComponents.getWrapper().find('#payslipLogo')[0].set('content', ` <img src="${image_path}" style="width:200px"/>`) : null;
    };
    // click event for delete payslip template button
    $('#deletePayslipTemplate, #deleteContextPayslipTemplate').on('click', function () {
        // get the value of selected row
        var selectedRow = fnGetSelected(tablePayslipTemplate);
        var record = tablePayslipTemplate.fnGetData(selectedRow[0]);

        // Check whether the seleted template is default or not
        if((parseInt(record.Set_As_Default)))
        {
            // If it is default template show warning popup
            $('#modalDeleteTemplateWarning').modal('toggle');
        }
        else{
           $('#modalDeletePayslipTemplate').modal('toggle');
        }
    });

    // click event for confirm delete template button
    $('#deleteConfirmPayslipTemplate').on('click', function () {
        var selectedRow = fnGetSelected(tablePayslipTemplate);
        if (selectedRow.length) {
            var record = tablePayslipTemplate.fnGetData(selectedRow[0]);
            $.ajax({
                type: 'POST',
                async: false,
                dataType: "json",
                url: pageUrl() + 'payroll/payslip-template/delete-payslip-template',
                data: {
                    templateId: record.Template_Id,
                    payslipType: record.Payslip_Type,
                    actionKey: 'deleteTemplate'
                },
                success: function (result) {
                    if (isJson(result)) {
                        if (result.success) {
                            fnRefreshTable(tablePayslipTemplate);
                        }
                        jAlert({ msg: result.msg, type: result.type });
                    } else {
                        jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                    }
                }, error: function (deleteErrorResult) {
                    if (deleteErrorResult.status == 200) {
                        sessionExpired();
                    } else {
                        /* To handle internal server error */
                        jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                    }
                }
            });
        } else {
            jAlert({ msg: "Kindly select the record to delete", type: "warning" });
        }
    });

    // function to define add or edit
    function fnDefineAction(type) {
        var winSize = $(window).width();
        $(window).on('resize', function () {
            winSize = $(window).width();
        });
        if (winSize < 1270) {
            $('#modalWarningPayslipTemplate').modal('show');
        }
        else {
            if (type == 'add') {
                payslipType = $('#s2id_filterPayslipType').select2('val');
                templateOperationType = 'add';
                fnConfigGrapesJs('add', null, null);
            }
            else {
                templateOperationType = 'edit';
                // to get selected row details
                var selectedRow = fnGetSelected(tablePayslipTemplate);
                if (selectedRow.length) {
                    var record = tablePayslipTemplate.fnGetData(selectedRow[0]);

                    // assign templateId 
                    $('#formPayslipTemplateId').val(record.Template_Id);

                    // assign templateName
                    $('#payslipTemplateName').val(record.Template_Name);

                    if (record.Template_Id > 0 && !isNaN(record.Template_Id)) {
                        payslipType = record.Payslip_Type;
                        // get file content by passing template location
                        var file = fngetFileContent(domainName + "/" + orgCode + "/" + "Payslip Template/" + record.Payslip_Type +"/" + record.Template_Id, "payslip template", 'bucketName');
                        if ($.inArray(file, ['', null, 'sessionexpired', 'internalerror']) === -1) {
                            // set lock
                            setLock({
                                'formName': 'Payslip Template',
                                'uniqueId': record.Template_Id,
                                'callback': function (result) {
                                    fnConfigGrapesJs('edit', record, file)
                                }
                            });
                        }
                        else if (file == '' || file == null) {
                            jAlert({ msg: "File not found", type: "info" });
                        }
                        else if (file == 'internalerror') {
                            jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                        }
                    } else {
                        jAlert({ msg: 'Kindly select ' + $('#lblFormNameA').html() + ' record', type: 'info' });
                    }
                } else {
                    jAlert({ msg: 'Kindly select ' + $('#lblFormNameA').html() + ' record', type: 'info' });
                }
            }
        }

    }
    function checkFullscreenMode() {
        if (document.fullscreenElement ||
            document.webkitFullscreenElement ||
            document.mozFullScreenElement) {
            if (doc.exitFullscreen) {
                doc.exitFullscreen();
            } else if (doc.webkitExitFullscreen) {
                doc.webkitExitFullscreen();
            } else if (doc.webkitCancelFullScreen) {
                doc.webkitCancelFullScreen();
            } else if (doc.msExitFullscreen) {
                doc.msExitFullscreen();
            } else if (doc.mozCancelFullScreen) {
                doc.mozCancelFullScreen();
            }
        }
    }
    function updataAddTemplate() {
        var selectedRow = fnGetSelected(tablePayslipTemplate);
        var record = tablePayslipTemplate.fnGetData(selectedRow[0]);
        if ($('#fieldForce').val() == 1) {
            if (templateOperationType == 'add')
            {
                var serviceProviderId = $('#s2id_formServiceProvider').select2('val');
            }
            else
            {
                var serviceProviderId = record.Service_Provider_Id;
            }
        }
        else {
            var serviceProviderId = 0;
        }
        // store a text file in s3
        $.ajax({
            type: 'POST',
            dataType: 'json',
            url: pageUrl() + 'payroll/payslip-template/update-payslip-template',
            data: {
                payslipTemplateId: templateOperationType == 'add' ? $('#formPayslipTemplateID').val() : record.Template_Id,
                templateName: $("#payslipTemplateName").val(),
                payslipType: payslipType,
                serviceProviderId: serviceProviderId
            },
            success: function (result) {
                if (isJson(result)) {
                    if (result.success) {
                        deletePhpActionHeaders();
                        $.ajax({
                            url: result.sUrl,
                            type: 'PUT',
                            contentType: "txt",
                            processData: false,
                            data: fileData,
                            success: function (err, res) {
                                if (res == "success") {
                                    fnRefreshTable(tablePayslipTemplate);
                                    // force-table-responsive class is removed because when we open form scroll bar is set
                                    $('#tablePayslipTemplate_wrapper').removeClass('force-table-responsive');
                                    fnCloseTemplate(true);
                                    // check fullscreen is mode, if its true then minimize to normal mode
                                    checkFullscreenMode();
                                    jAlert({ msg: result.msg, type: result.type });
                                }
                            },
                            error: function (jqXHR, exception) {
                                if (templateOperationType == 'add') {
                                    var templateId = result.templateId;
                                    $.ajax({
                                        type: 'POST',
                                        async: false,
                                        dataType: "json",
                                        url: pageUrl() + 'payroll/payslip-template/delete-payslip-template',
                                        data: {
                                            templateId: templateId,
                                            payslipType: record.Payslip_Type,
                                            actionKey: 'addTemplate'
                                        },
                                        success: function (result) {
                                            if (isJson(result)) {
                                                if (result.success) {
                                                    fnRefreshTable(tablePayslipTemplate);
                                                }
                                            }
                                            else {
                                                // check fullscreen is mode, if its true then minimize to normal mode
                                                checkFullscreenMode();
                                                sessionExpired();
                                            }
                                        }
                                    });
                                }
                                jAlert({ msg: 'Sorry, there seems to be some technical difficulties. Please try after some time or contact system admin.', type: result.type });
                            }
                        })
                        setPhpActionHeaders();
                    }
                    else {
                        jAlert({ panel: $('#gjs'), msg: result.msg, type: result.type });
                    }
                }
                else {
                    // check fullscreen is mode, if its true then minimize to normal mode
                    checkFullscreenMode();
                    sessionExpired();
                }
            },
            error: function (error) {
                if (error.status == 200) {
                    // check fullscreen is mode, if its true then minimize to normal mode
                    checkFullscreenMode();
                    sessionExpired();
                } else {
                    /* To handle internal server error */
                    jAlert({ msg: 'Sorry, there seems to be some technical difficulties. Please try after some time or contact system admin.', type: 'warning', panel: $('#gjs') });
                }
            }
        });
    }
    $('#confirmAddTemplate').on('click', function () {
        updataAddTemplate();
    });
    // onchange function for payslip type
    $('#filterPayslipType').on('change',function(){
        payslipType = $('#s2id_filterPayslipType').select2('val');
    });

    // grapesjs configuration
    function fnConfigGrapesJs(type, record, fileContent) {
        // hide/show of grapes-js modules
        fnOpenGrapesJsModule();
        $(window).on('resize', function () {
            winSize = $(window).width();
            if (winSize < 1270) {
                if ($('#gjs').is(':visible')) {
                    $('#gjs').hide();
                    $("#grapesJsModule").get(0).style.setProperty('height', '0px');
                    $("#gridPanelPayslipTemplate").show();
                    $("#backToPayslipTemplateGrid").hide();
                    $('#modalWarningPayslipTemplate').modal('show');
                }
            }
        });
        // grapes-js module start & grapes-js configuration
        editor = grapesjs.init({
            container: "#gjs",
            height: "100%",
            showOffsets: 1,
            noticeOnUnload: 0,
            avoidInlineStyle: 1,
            storageManager: { autoload: 0 },
            fromElement: true,
            allowScripts: 1,
            plugins: ['gjs-preset-webpage'],
            styleManager: { clearProperties: 1 },
        });

        // clear canvas when initially loaded
        editor.DomComponents.clear();

        // for retrieve content
        if (type == 'edit') {
            editor.setComponents(fileContent);
            if ($('#displayPayslipAddress').val() == 1) {
                // display address when flag is enabled
                editor.DomComponents.getWrapper().find('#payslipTemplateAddress')[0] ? editor.DomComponents.getWrapper().find('#payslipTemplateAddress')[0].setStyle({ display: 'block' }) : null;
            }
            else {
                // hide address when flag is disabled
                editor.DomComponents.getWrapper().find('#payslipTemplateAddress')[0] ? editor.DomComponents.getWrapper().find('#payslipTemplateAddress')[0].setStyle({ display: 'none' }) : null;
            }
        } else {
            editor.addComponents(
                '<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.0/css/bootstrap.min.css">'
            );
        };
        // clear undo manager, because it clear bootstrap style which is added in components
        editor.UndoManager.clear();
        //  add a save html & css button in panel
        editor.Panels.addButton("options", [
            {
                id: "save-db",
                className: "fa fa-floppy-o",
                command: "save-db",
                attributes: { title: "Save DB" }
            }
        ]);

        // save html and css code
        editor.Commands.add("save-db", {
            run: function (editor, sender) {
                sender && sender.set("active", 0); // turn off the button
                editor.store();

                // form data which is send to s3. form html amd css in single data
                fileData = editor.getHtml() + `<style>${editor.getCss()}</style>`;
                if (type == 'add') {
                    // show the warning popup only when the template is added first time based on the payslip type, otherwise call the update function directly
                    if (payslipType === 'Monthly' && !monthlyPayslipTemplateAvailable) {
                        $('#modalAddConfirmation').modal('show');
                    } else if (payslipType === 'Hourly' && !hourlyPayslipTemplateAvailable) {
                        $('#modalAddConfirmation').modal('show');
                    } else {
                        updataAddTemplate();
                    }
                    checkFullscreenMode();
                }
                else {
                    updataAddTemplate();
                }
            }
        });

        // assign a blockManager to create custom component 
        var blockManager = editor.BlockManager;

        // variable declaration
        var Earnings_items = ``,
            Deduction_items = ``,
            Form16_items = ``,
            Sec10_items = ``,
            Other_Income_items = ``,
            Contribution1_Items = ``,
            Contribution2_Items = ``;
        // when clear canvas it removes html also styles. so again add it.
        $('.fa-trash').on('click', function () {

            //  add bootstrap component in grapes-js
            editor.addComponents(
                '<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.0/css/bootstrap.min.css">'
            );

        });
        // preview with values when click eye icon
        editor.on('run:preview', () => {
            //  calling backEnd
            var result = fnGetResult();
            if (result) {
                // retrieve table contents through calling this function
                if (editor.DomComponents.getWrapper().find('#earningsTab')[0] || editor.DomComponents.getWrapper().find('#deductTab')[0] || editor.DomComponents.getWrapper().find('#earningsDeductionsTab')[0] || editor.DomComponents.getWrapper().find('#form16SummaryTab')[0] || editor.DomComponents.getWrapper().find('#exemptionsPanel')[0] || editor.DomComponents.getWrapper().find('#sec10ExemptionsPanel')[0] || editor.DomComponents.getWrapper().find('#contributionTab2')[0] || editor.DomComponents.getWrapper().find('#contributionTab1')[0]) {
                    var table_contents = fngetTableContent(``, ``, ``, ``, ``, ``, ``, result, true,payslipType);
                    Earnings_items = table_contents[0];
                    Deduction_items = table_contents[1];
                    Other_Income_items = table_contents[2];
                    Form16_items = table_contents[3];
                    Sec10_items = table_contents[4];
                    Contribution1_Items = table_contents[5];
                    Contribution2_Items = table_contents[6];
                }
                preview_remove_values(result);
            }
        });
        // remove values again go to canvas, return to edit mode
        editor.on('stop:preview', () => {
            Earnings_items = ``;
            Deduction_items = ``;
            Form16_items = ``;
            Other_Income_items = ``;
            Sec10_items = ``;
            Contribution1_Items = ``;
            Contribution2_Items = ``;
            preview_remove_values();

        });

        // function to preview and remove values
        function preview_remove_values(result) {
            var emp_name = result ? result.Payslip.Emp_First_Name + ' ' + result.Payslip.Emp_Last_Name : 'Name',
                paidLeaveContent = ``, unpaidLeaveContent = ``;
            if (result) {
                var pLeave = result.PaidLeave[2];
                var uPLeave = result.UnpaidLeave[2];
                var prevMonthUPLeave = result.PrevMonthUnpaidLeave[2];
                if (pLeave.length > 0) {
                    for (var x = 0; x < pLeave.length; x++) {
                        paidLeaveContent += '<tr class="child"><td class="col-xs-10 text-left" id="pltype" style="height:40px;word-break: break-word;">' + pLeave[x][0] +
                            '</td><td class="text-right" id="plbalance" style="height:40px">' + pLeave[x][1] + '</td></tr>'
                    }
                }
                if (uPLeave.length > 0) {
                    for (var x = 0; x < uPLeave.length; x++) {
                        unpaidLeaveContent += '<tr class="child"><td class="col-xs-10 text-left" id="upltype" style="height:40px;word-break: break-word;">' + uPLeave[x][0] +
                            '</td><td class="text-right" id="uplbalance" style="height:40px">' + uPLeave[x][1] + '</td></tr>'
                    }
                }
            }
            // assign values to each id
            editor.DomComponents.getWrapper().find('#viewMonthlyEmployeeId')[0] ? editor.DomComponents.getWrapper().find('#viewMonthlyEmployeeId')[0].set('content', result ? result.Payslip.User_Defined_EmpId : 'Id') : null;

            editor.DomComponents.getWrapper().find('#viewMonthlyEmployeeName')[0] ? editor.DomComponents.getWrapper().find('#viewMonthlyEmployeeName')[0].set('content', result ? emp_name : 'Name') : null;

            editor.DomComponents.getWrapper().find('#viewMonthlyDepartment')[0] ? editor.DomComponents.getWrapper().find('#viewMonthlyDepartment')[0].set('content', result ? result.Payslip.Department_Name : 'Dept') : null;

            editor.DomComponents.getWrapper().find('#viewMonthlyDesignation')[0] ? editor.DomComponents.getWrapper().find('#viewMonthlyDesignation')[0].set('content', result ? result.Payslip.Designation_Name : 'Designation') : null;

            editor.DomComponents.getWrapper().find('#viewMonthlyDateOfJoin')[0] ? editor.DomComponents.getWrapper().find('#viewMonthlyDateOfJoin')[0].set('content', result ? result.Payslip.Date_Of_Join : 'dd/mm/yyyy') : null;

            editor.DomComponents.getWrapper().find('#viewMonthlyESI')[0] ? editor.DomComponents.getWrapper().find('#viewMonthlyESI')[0].set('content', result ? fnCheckNull(result.Payslip.Policy_No) : 'ESI') : null;

            editor.DomComponents.getWrapper().find('#viewMonthlyPFAccountNumber')[0] ? editor.DomComponents.getWrapper().find('#viewMonthlyPFAccountNumber')[0].set('content', result ? fnCheckNull(result.Payslip.Pf_PolicyNo) : 'PF') : null;

            editor.DomComponents.getWrapper().find('#viewMonthlUAN')[0] ? editor.DomComponents.getWrapper().find('#viewMonthlUAN')[0].set('content', result ? fnCheckNull(result.Payslip.UAN) : 'UAN') : null;

            editor.DomComponents.getWrapper().find('#viewMonthlyPANNo')[0] ? editor.DomComponents.getWrapper().find('#viewMonthlyPANNo')[0].set('content', result ? fnCheckNull(result.Payslip.Emp_Pan) : 'PAN') : null;

            editor.DomComponents.getWrapper().find('#viewMonthlyBankName')[0] ? editor.DomComponents.getWrapper().find('#viewMonthlyBankName')[0].set('content', result ? fnCheckNull(result.Payslip.Bank_Name) : 'Bank') : null;

            editor.DomComponents.getWrapper().find('#viewMonthlyBankAccountNo')[0] ? editor.DomComponents.getWrapper().find('#viewMonthlyBankAccountNo')[0].set('content', result ? fnCheckNull(result.Payslip.Bank_Account_Number) : 'Acc No') : null;

            editor.DomComponents.getWrapper().find('#viewMonthlyAadhaarNo')[0] ? editor.DomComponents.getWrapper().find('#viewMonthlyAadhaarNo')[0].set('content', result ? fnCheckNull(result.Payslip.Aadhaar_Card_Number) : 'Acc No') : null;

            editor.DomComponents.getWrapper().find('#viewMonthlyPaidLeave')[0] ? editor.DomComponents.getWrapper().find('#viewMonthlyPaidLeave')[0].set('content', result ? ((result.PaidLeave[1] != null) ? result.PaidLeave[1] : 0) : '0') : null;

            editor.DomComponents.getWrapper().find('#viewMonthlyUnpaidLeave')[0] ? editor.DomComponents.getWrapper().find('#viewMonthlyUnpaidLeave')[0].set('content', result ? ((result.UnpaidLeave[1] != null) ? result.UnpaidLeave[1] : 0) : '0') : null;

            editor.DomComponents.getWrapper().find('#viewMonthlyPrevMonthUnpaidLeave')[0] ? editor.DomComponents.getWrapper().find('#viewMonthlyPrevMonthUnpaidLeave')[0].set('content', result ? ((result.PrevMonthUnpaidLeave[1] != null) ? result.PrevMonthUnpaidLeave[1] : 0) : '0') : null;

            editor.DomComponents.getWrapper().find('#viewMonthlyOnDuty')[0] ? editor.DomComponents.getWrapper().find('#viewMonthlyOnDuty')[0].set('content', result ? ((result.OnDuty[1] != null) ? result.OnDuty[1] : 0) : '0') : null;

            editor.DomComponents.getWrapper().find('#viewMonthlyDaysWorked')[0] ? editor.DomComponents.getWrapper().find('#viewMonthlyDaysWorked')[0].set('content', result ? result.WorkedDays : '0') : null;

            editor.DomComponents.getWrapper().find('#viewMonthlyPaidDays')[0] ? editor.DomComponents.getWrapper().find('#viewMonthlyPaidDays')[0].set('content', result ? result.PaidDays : '0') : null;

            editor.DomComponents.getWrapper().find('#viewMonthlyDateOfRelieving')[0] ? editor.DomComponents.getWrapper().find('#viewMonthlyDateOfRelieving')[0].set('content', result ? result.Payslip.Emp_Relieving_Date : 'dd/mm/yyyy') : null;
            
            editor.DomComponents.getWrapper().find('#payMonth')[0] ? editor.DomComponents.getWrapper().find('#payMonth')[0].set('content', result ? 'Payslip Generated for the month of ' + result.Payslip_Month : 'Title') : null;

            editor.DomComponents.getWrapper().find('#formHeader')[0] ? editor.DomComponents.getWrapper().find('#formHeader')[0].set('content', result ? result.Org_Name.Org_Name : 'Company Name') : null;

            editor.DomComponents.getWrapper().find('#viewBusinessUnit')[0] ? editor.DomComponents.getWrapper().find('#viewBusinessUnit')[0].set('content', result ? result.Payslip.Business_Unit : 'Business Unit') : null;

            editor.DomComponents.getWrapper().find('#payslipStreet1')[0] ? editor.DomComponents.getWrapper().find('#payslipStreet1')[0].set('content', result ? result.Payslip['Street1'] + ',' : 'Street1,') : null;

            editor.DomComponents.getWrapper().find('#payslipStreet2')[0] ? editor.DomComponents.getWrapper().find('#payslipStreet2')[0].set('content', result ? result.Payslip['Street2'] + ',' : 'Street2,') : null;

            editor.DomComponents.getWrapper().find('#payslipCity')[0] ? editor.DomComponents.getWrapper().find('#payslipCity')[0].set('content', result ? result.Payslip['City'] + ',' : 'City,') : null;

            editor.DomComponents.getWrapper().find('#payslipState')[0] ? editor.DomComponents.getWrapper().find('#payslipState')[0].set('content', result ? result.Payslip['State'] + ',' : 'State,') : null;

            editor.DomComponents.getWrapper().find('#payslipCountry')[0] ? editor.DomComponents.getWrapper().find('#payslipCountry')[0].set('content', result ? result.Payslip['Country'] + ',' : 'Country,') : null;

            editor.DomComponents.getWrapper().find('#payslipPinCode')[0] ? editor.DomComponents.getWrapper().find('#payslipPinCode')[0].set('content', result ? result.Payslip['Pincode'] + ',' : 'Pincode,') : null;

            editor.DomComponents.getWrapper().find('#earnAmt')[0] ? editor.DomComponents.getWrapper().find('#earnAmt')[0].set('content', result ? 'Amount(' + result.Currency + ')' : 'Amount') : null;

            editor.DomComponents.getWrapper().find('#deductAmt')[0] ? editor.DomComponents.getWrapper().find('#deductAmt')[0].set('content', result ? 'Amount(' + result.Currency + ')' : 'Amount') : null;

            editor.DomComponents.getWrapper().find('#exemptionsAmt')[0] ? editor.DomComponents.getWrapper().find('#exemptionsAmt')[0].set('content', result ? 'Amount(' + result.Currency + ')' : 'Amount') : null;

            editor.DomComponents.getWrapper().find('#sectionsAmt')[0] ? editor.DomComponents.getWrapper().find('#sectionsAmt')[0].set('content', result ? 'Amount(' + result.Currency + ')' : 'Amount') : null;

            editor.DomComponents.getWrapper().find('#form16SummaryAmt')[0] ? editor.DomComponents.getWrapper().find('#form16SummaryAmt')[0].set('content', result ? 'Amount(' + result.Currency + ')' : 'Amount') : null;

            editor.DomComponents.getWrapper().find('#earningsTab')[0] ? editor.DomComponents.getWrapper().find('#earningsTab')[0].set('content', result ? Earnings_items : '') : null;

            editor.DomComponents.getWrapper().find('#contributionTab1')[0] ? editor.DomComponents.getWrapper().find('#contributionTab1')[0].set('content', result ? Contribution1_Items : '') : null;

            editor.DomComponents.getWrapper().find('#contributionTab2')[0] ? editor.DomComponents.getWrapper().find('#contributionTab2')[0].set('content', result ? Contribution2_Items : '') : null;

            editor.DomComponents.getWrapper().find('#deductTab')[0] ? editor.DomComponents.getWrapper().find('#deductTab')[0].set('content', result ? Deduction_items : '') : null;

            // Handle the new combined earnings and deductions table
            if (editor.DomComponents.getWrapper().find('#earningsDeductionsTab')[0]) {
                var combinedContent = fnGetCombinedEarningsDeductionsContent(result ? Earnings_items : '', result ? Deduction_items : '');
                editor.DomComponents.getWrapper().find('#earningsDeductionsTab')[0].set('content', combinedContent);
            }

            editor.DomComponents.getWrapper().find('#form16SummaryTab')[0] ? editor.DomComponents.getWrapper().find('#form16SummaryTab')[0].set('content', result ? Form16_items : `<tr style="height:60px"></tr>
            <tr style="height:60px"></tr>`) : null;

            editor.DomComponents.getWrapper().find('#exemptionsPanel')[0] ? editor.DomComponents.getWrapper().find('#exemptionsPanel')[0].set('content', result ? Other_Income_items : '') : null;

            editor.DomComponents.getWrapper().find('#sec10ExemptionsPanel')[0] ? editor.DomComponents.getWrapper().find('#sec10ExemptionsPanel')[0].set('content', result ? Sec10_items : '') : null;

            editor.DomComponents.getWrapper().find('#netpayInWord')[0] ? editor.DomComponents.getWrapper().find('#netpayInWord')[0].set('content', result ? '<b>: ' + result.Currency + '</b> ' + ' ' + result.Payslip.Incentive_Amount_Words + ' Only' : '') : null;
            
            editor.DomComponents.getWrapper().find('#taxReliefInWord')[0] ? editor.DomComponents.getWrapper().find('#taxReliefInWord')[0].set('content', result ? '<b>: ' + result.Currency + '</b> ' + ' ' + result.Payslip.Tax_Relief_Amount : '') : null;

            editor.DomComponents.getWrapper().find('#viewMonthlyPaidLeaveDetails')[0] ? editor.DomComponents.getWrapper().find('#viewMonthlyPaidLeaveDetails')[0].set('content', result ? paidLeaveContent : '') : null;

            editor.DomComponents.getWrapper().find('#viewMonthlyUnpaidLeaveDetails')[0] ? editor.DomComponents.getWrapper().find('#viewMonthlyUnpaidLeaveDetails')[0].set('content', result ? unpaidLeaveContent : '') : null;

            // clear undo when preview open and stop
            editor.UndoManager.clear();
        }

        // Function to combine earnings and deductions content for the new single table layout
        function fnGetCombinedEarningsDeductionsContent(earningsContent, deductionsContent) {
            // Parse earnings and deductions HTML content to extract rows
            var earningsRows = [];
            var deductionsRows = [];

            // Extract earnings rows
            if (earningsContent) {
                var earningsTemp = $('<div>').html(earningsContent);
                earningsTemp.find('tr').each(function() {
                    var $row = $(this);
                    if (!$row.hasClass('child') || $row.find('td').length === 2) {
                        var cells = $row.find('td');
                        if (cells.length >= 2) {
                            earningsRows.push({
                                name: cells.eq(0).html(),
                                amount: cells.eq(1).html()
                            });
                        }
                    }
                });
            }

            // Extract deductions rows
            if (deductionsContent) {
                var deductionsTemp = $('<div>').html(deductionsContent);
                deductionsTemp.find('tr').each(function() {
                    var $row = $(this);
                    if (!$row.hasClass('child') || $row.find('td').length === 2) {
                        var cells = $row.find('td');
                        if (cells.length >= 2) {
                            deductionsRows.push({
                                name: cells.eq(0).html(),
                                amount: cells.eq(1).html()
                            });
                        }
                    }
                });
            }

            // Determine the maximum number of rows
            var maxRows = Math.max(earningsRows.length, deductionsRows.length);
            var combinedContent = '';

            // Generate combined rows
            for (var i = 0; i < maxRows; i++) {
                var earningsName = earningsRows[i] ? earningsRows[i].name : '';
                var earningsAmount = earningsRows[i] ? earningsRows[i].amount : '';
                var deductionsName = deductionsRows[i] ? deductionsRows[i].name : '';
                var deductionsAmount = deductionsRows[i] ? deductionsRows[i].amount : '';

                combinedContent += '<tr class="child">' +
                    '<td class="text-left" style="min-height:40px;word-break: break-word;color: #000;vertical-align: top;padding: 8px;">' + earningsName + '</td>' +
                    '<td class="text-right" style="min-height:40px;border-right: 1px solid #ddd;color: #000;vertical-align: top;padding: 8px;">' + earningsAmount + '</td>' +
                    '<td class="text-left" style="min-height:40px;word-break: break-word;color: #000;vertical-align: top;padding: 8px;">' + deductionsName + '</td>' +
                    '<td class="text-right" style="min-height:40px;color: #000;vertical-align: top;padding: 8px;">' + deductionsAmount + '</td>' +
                    '</tr>';
            }

            return combinedContent;
        }

        // styles
        var Common_Style = `<style>
                        .span4 img {
                            margin-right: 10px;
                        }
                        .span4 .img-left {
                            float: left;
                            padding-left: 5%;
                        }
                        .template-field-label {
                            color: #000;
                            font-weight:600;
                        }
                        .payslip-template-1-form16{
                            border-top: 2px solid #807c7c;
                            padding: 0px;
                        }
                        .payslip-template-1-orgCon{
                            border-top: 2px solid #807c7c;
                            border-bottom: 2px solid #807c7c;
                            margin-right: 0; 
                            margin-left: 0;
                        }
                        .payslip-template-2-orgCon{
                            padding: 0px;
                            margin-top: 30px;
                            border-top:1px solid black;
                        }
                        .paddingZeroTemplate{
                            padding:0px;
                        }
                        .payslip-template-3-table{
                            padding: 0px;
                            border:1px solid black;
                            border-top:none
                        }
                        #earningsTab{
                            color: #000;
                        }
                        #deductTab{
                            color: #000;
                        }
                        #earningsDeductionsTab{
                            color: #000;
                        }
                        #earningsDeductionsTab td{
                            color: #000;
                            vertical-align: middle;
                        }
                        #earningsDeductionsTabFooter{
                            color: #000;
                        }
                        #earningsDeductionsTabFooter td{
                            color: #000;
                            vertical-align: middle;
                        }
                        #earningsDeductionsTabFooter p{
                            color: #000;
                            margin: 0;
                            padding: 0;
                        }
                        #exemptionsPanel{
                            color: #000;
                        }
                        #sec10ExemptionsPanel{
                            color: #000;
                        }
                        #form16SummaryTab{
                            color: #000;
                        }
                        #viewMonthlyPaidLeaveDetails{
                            color: #000;
                        }
                        #viewMonthlyUnpaidLeaveDetails{
                            color: #000;
                        }
                        #contributionTab1{
                            color:#000;
                        }
                        #contributionTab2{
                            color: #000;
                        }
                        .table {
                            width: 100%;
                            max-width: 100%;
                            margin-bottom: 0px;
                        }
                        p {
                            margin: 0 0 10px;
                            color: #000;
                        }
                        .trClass{
                            color: #000;
                        }
                        #formHeader{
                            font-weight:600;
                        }
                        .h4style{
                            margin-top: 0px;
                            margin-bottom: 0px;
                            font-weight:600;
                        }
                        .span4 .img-right {
                            float: right;
                        }
                        .template-text-center {
                            text-align: center !important;
                        }
                        .emp_name_work_break_cls{
                            width: 250px;
                            word-break: break-all;
                        }
                        #payslipStreet1 {display:inline-block;} 
                        #payslipStreet2 {display:inline-block; } 
                        #payslipCity {display:inline-block;} 
                        #payslipState {display:inline-block; } 
                        #payslipCountry {display:inline-block;} 
                        #payslipPinCode {display:inline-block; } 
                
                        .print-container {
                            min-width: 900px;
                            margin: 30px auto;
                            background: white;
                            padding: 10px 30px;
                
                            .template-header {
                                margin-bottom: 20px;
                                border-bottom: 1px solid #dbdbdb;
                                padding-bottom: 20px;
                            }
                            .header-payslip4{
                                padding-top:10px;
                                padding-bottom: 10px;
                            }
                            table {
                                margin-top: 30px;
                
                                tbody tr.no-border:first-child {
                                    opacity: 0.6 !important;
                                }
                            }
                
                            .summary-table {
                                border: 1px solid #DDE1E4;
                
                                & tr td:last-child {
                                    text-align: right;
                                }
                
                                & tr th:last-child {
                                    text-align: right;
                                }
                
                                td {
                                    border-left: 1px solid #dbdbdb;
                                }
                
                                th {
                                    border-left: 1px solid #dbdbdb;
                                }
                
                                thead {
                                    color: #737F8B;
                                    text-transform: uppercase;
                                }
                
                            }
                
                            .ft-18 {
                                font-size: 20px;
                                margin-bottom: 10px;
                            }
                
                            .adder {
                                font-size: 16px;
                                font-weight: 500;
                                text-align: right;
                                border-left: 0;
                                border-right: 0;
                                border-bottom: 0;
                            }
                
                            .total {
                                font-size: 22px;
                            }
                        }
                
                        .mega {
                            font-size: 33px;
                            margin-bottom: 10px;
                            font-weight: 600;
                        }
                
                        .invoice-logo {
                            height: 5 0px;
                            width: 110px;
                        }
                
                        .other-rates {
                            float: right;
                            width: 350px;
                            text-align: right;
                
                            dl {
                                width: 100%;
                                margin-bottom: 5px;
                
                                &.total {
                                    border-top: 1px solid #dbdbdb;
                                    padding-top: 10px;
                                }
                            }
                
                            dt {
                                width: 50%;
                                float: left;
                
                            }
                
                            dd {
                                width: 50%;
                                float: left;
                                padding-right: 10px;
                                margin: 0;
                            }
                        }
                
                        .invoice-from {
                            float: right;
                            text-align:right;
                        }
                
                        .summary-info {
                            margin-bottom: 20px;
                            padding-bottom: 10px;
                        }
                
                        .right-size {
                            float: right;
                            font-size: 10px;
                        }
                
                        @media print {
                            .print-container {}

                            h1,
                            h2,
                            h3,
                            h4,
                            h5,
                            h6 {
                                font-weight: bold;

                                &:first-letter {
                                    font-size: inherit;
                                }
                            }

                            #earningsDeductionsTab td,
                            #earningsDeductionsTabFooter td {
                                color: #000 !important;
                                border-color: #807c7c !important;
                            }
                        }

                        @media (max-width: 768px) {
                            #earningsDeductionsTab th,
                            #earningsDeductionsTab td {
                                font-size: 12px;
                                padding: 4px;
                            }

                            #earningsDeductionsTabFooter th,
                            #earningsDeductionsTabFooter td {
                                font-size: 12px;
                                padding: 4px;
                            }
                        }

                        .row-column1 {
                            display: flex;
                            justify-content: flex-start;
                            align-items: stretch;
                            flex-wrap: nowrap;
                        }
                    
                        .row-cell1 {
                            min-height: 75px;
                            flex-grow: 1;
                            flex-basis: 100%;
                        }
                    </style>`;

        // custom blocks

        // Company Name
        blockManager.add("company name", {
            label: 'Company Name',
            content: `<h3 data-gjs-editable="false" style="text-align:center;color: #000;font-weight:600" id="formHeader">Company Name</h3>`
        });

        // Business Unit
        blockManager.add("business unit", {
            label: 'Business Unit',
            content: `<h3 data-gjs-editable="false" style="text-align:center;color: #000;font-weight:400" id="viewBusinessUnit">Business Unit</h3>`
        });

        // Payslip Header
        blockManager.add("Header", {
            label: 'Payslip Heading',
            content: `<h3 data-gjs-editable="false" style="text-align:center;font-weight:600" id="payMonth">Title</h3>`
        });

        // Logo
        blockManager.add("Logo", {
            label: 'Logo',
            content: `<div class = "col-xs-4" id="payslipLogo">
                <img alt="logo" src="${image_path}" style="width:200px"/>
            </div>`
        });
        if ($('#displayPayslipAddress').val() == 1) {
            // address
            blockManager.add('address', {
                label: 'Address',
                content: `<div id="payslipTemplateAddress"> 
            <div style="text-align:right">
                <p id="payslipStreet1">Street1,</p> 
                <p id="payslipStreet2">Street2,</p> 
            </div>
            <div style="text-align:right">
                <p id="payslipCity">City,</p> 
                <p id="payslipState">State,</p> 
            </div>
            <div style="text-align:right">
                <p id="payslipCountry">Country,</p> 
                <p id="payslipPinCode">Pin</p> 
            </div>
            </div>
        ${Common_Style}`
            });
        }
        // Employee ID block
        blockManager.add("employee_id", {
            label: 'Employee Id',
            content: `<div class="col-xs-5">
            <p><b>Employee Id</b></p>
        </div>
        <div class="col-xs-1 ">:</div>
        <div class="col-xs-6">
            <p data-gjs-editable="false" id="viewMonthlyEmployeeId">Id</p>
        </div>`
        });
        // Employee Name block
        blockManager.add("employee_name", {
            label: 'Employee Name',
            content: `<div class="col-xs-5">
            <p><b>Employee Name</b></p>
        </div>
        <div class="col-xs-1 ">:</div>
        <div class="col-xs-6 emp_name_work_break_cls">
            <p data-gjs-editable="false" id="viewMonthlyEmployeeName">Name</p>
        </div>`
        });
        // Designation block
        blockManager.add("designation", {
            label: 'Designation',
            content: `<div class="col-xs-5">
            <b>Designation</b>
        </div>
        <div class="col-xs-1 ">:</div>
        <div class="col-xs-6">
            <p data-gjs-editable="false" id="viewMonthlyDesignation">Designation</p>
        </div>`
        });
        // Designation block
        blockManager.add("date_of_joining", {
            label: 'Date Of Join',
            content: `<div class="col-xs-5">
            <b>Date Of Join</b>
        </div>
        <div class="col-xs-1 ">:</div>
        <div class="col-xs-6">
            <p data-gjs-editable="false" id="viewMonthlyDateOfJoin">dd/mm/yyyy</p>
        </div>`
        });
        // Department block
        blockManager.add("department", {
            label: 'Department',
            content: `<div class="col-xs-5">
            <p><b>Department</b></p>
        </div>
        <div class="col-xs-1 ">:</div>
        <div class="col-xs-6">
            <p data-gjs-editable="false" id="viewMonthlyDepartment">Dept</p>
        </div>`
        });
        // ESI block
        blockManager.add("ESI/Insurance Number", {
            label: 'ESI/Insurance',
            content: `<div class="col-xs-5">
            <b>ESI/Insurance No</b>
        </div>
        <div class="col-xs-1 ">:</div>
        <div class="col-xs-6">
            <p data-gjs-editable="false" id="viewMonthlyESI">ESI</p>
        </div>`
        });
        // PF block
        blockManager.add("PF", {
            label: 'PF Number',
            content: `<div class="col-xs-5">
            <p><b>PF Number</b></p>
        </div>
        <div class="col-xs-1 ">:</div>
        <div class="col-xs-6">
            <p data-gjs-editable="false" id="viewMonthlyPFAccountNumber">PF</p>
        </div>`
        });
        // PF UAN block
        blockManager.add("PF_UAN", {
            label: 'PF UAN Number',
            content: ` <div class="col-xs-5">
            <p><b>PF UAN No</b></p>
        </div>
        <div class="col-xs-1 ">:</div>
        <div class="col-xs-6">
            <p data-gjs-editable="false" id="viewMonthlUAN">UAN</p>
        </div>`
        });
        // PAN block
        blockManager.add("PAN", {
            label: 'PAN Number',
            content: `<div class="col-xs-5">
            <p><b>PAN Number</b></p>
        </div>
        <div class="col-xs-1 ">:</div>
        <div class="col-xs-6">
            <p data-gjs-editable="false" id="viewMonthlyPANNo">PAN</p>
        </div>`
        });
        // bank acc no block
        blockManager.add("aadhaar_no", {
            label: 'Aadhaar Number',
            content: ` <div class="col-xs-5">
            <p><b>Aadhaar Number</b></p>
        </div>
        <div class="col-xs-1 ">:</div>
        <div class="col-xs-6">
            <p data-gjs-editable="false" id="viewMonthlyAadhaarNo">Aadhaar</p>
        </div>`
        });
        // bank name block
        blockManager.add("bank_name", {
            label: 'Bank Name',
            content: `<div class="col-xs-5">
            <p><b>Bank Name</b></p>
        </div>
        <div class="col-xs-1 ">:</div>
        <div class="col-xs-6">
            <p data-gjs-editable="false" id="viewMonthlyBankName">Bank</p>
        </div>`
        });
        // bank acc no block
        blockManager.add("bank_acc_no", {
            label: 'Bank Account Number',
            content: ` <div class="col-xs-5">
            <p><b>Bank Account No</b></p>
        </div>
        <div class="col-xs-1 ">:</div>
        <div class="col-xs-6">
            <p data-gjs-editable="false" id="viewMonthlyBankAccountNo">Acc No</p>
        </div>`
        });
        // Employee Status
        blockManager.add("employee_status", {
            label: 'Employee Status',
            content: `<div class="col-xs-5">
            <p><b>Employee Status</b></p>
        </div>
        <div class="col-xs-1 ">:</div>
        <div class="col-xs-6">
            <p id="viewMonthlyEmployeeStatus">Confirmed/Probationary</p>
        </div>`
        });
        // paid leave block
        blockManager.add("paid_leave", {
            label: 'Paid Leave',
            content: `<div class="col-xs-5">
            <p><b>Paid Leave</b></p>
        </div>
        <div class="col-xs-1 ">:</div>
        <div class="col-xs-6">
            <p data-gjs-editable="false" id="viewMonthlyPaidLeave">0</p>
        </div>`
        });
        // unpaid leave block
        blockManager.add("unpaid_leave", {
            label: 'Unpaid Leave',
            content: `  <div class="col-xs-5">
            <p><b id="viewMonthlyUnpaidLeaveLabel">Unpaid Leave</b></p>
        </div>
        <div class="col-xs-1 ">:</div>
        <div class="col-xs-6">
            <p data-gjs-editable="false" id="viewMonthlyUnpaidLeave">0</p>
        </div>`
        });
        if($("#considerCutoffDaysForAttendanceAndTimeoff").val() === "Yes") {
            // prev month unpaid leave block
            blockManager.add("prev_month_unpaid_leave", {
                label: 'Previous Month Unpaid Leave',
                content: `  <div id="viewMonthlyPrevMonthUnpaidLeaveElement" class="col-xs-5">
                <p><b id="viewMonthlyPrevMonthUnpaidLeaveLabel">Previous Month Unpaid Leave</b></p>
            </div>
            <div class="col-xs-1 ">:</div>
            <div class="col-xs-6">
                <p data-gjs-editable="false" id="viewMonthlyPrevMonthUnpaidLeave">0</p>
            </div>`
            });
        }

        blockManager.add("On Duty", {
            label: 'On Duty',
            content: `<div class="col-xs-5">
            <p><b>On Duty</b></p>
        </div>
        <div class="col-xs-1 ">:</div>
        <div class="col-xs-6">
            <p data-gjs-editable="false" id="viewMonthlyOnDuty">0</p>
        </div>`
        });
        // days worked block
        blockManager.add("Days Worked", {
            label: 'Days Worked',
            content: `<div class="col-xs-5">
            <p><b>Days Worked</b></p>
        </div>
        <div class="col-xs-1 ">:</div>
        <div class="col-xs-6">
            <p data-gjs-editable="false" id="viewMonthlyDaysWorked">0</p>
        </div>`
        });

        // paid days block.its used to present single component in payslip template
        blockManager.add("Paid Days", {
            label: 'Paid Days',
            content: `<div class="col-xs-5">
            <p><b>Paid Days</b></p>
        </div>
        <div class="col-xs-1 ">:</div>
        <div class="col-xs-6">
            <p data-gjs-editable="false" id="viewMonthlyPaidDays">0</p>
        </div>`
        });

        // date of Relieving block
        blockManager.add("Date Of Relieving", {
            label: 'Date Of Relieving',
            content: `<div class="col-xs-5">
            <p><b>Date Of Relieving</b></p>
        </div>
        <div class="col-xs-1 ">:</div>
        <div class="col-xs-6">
            <p data-gjs-editable="false" id="viewMonthlyDateOfRelieving">dd/mm/yyyy</p>
        </div>`
        });
        if( payslipType == 'Hourly'){
            // hours worked block
            blockManager.add("Hours Worked", {
                label: 'Hours Worked',
                content: `<div class="col-xs-5">
                <p><b>Hours Worked</b></p>
            </div>
            <div class="col-xs-1 ">:</div>
            <div class="col-xs-6">
                <p data-gjs-editable="false" id="viewHoursWorked">0</p>
            </div>`
            });
            // day wages block
            blockManager.add("Day Wages", {
                label: 'Day Wages',
                content: `<div class="col-xs-5">
            <p><b>Day Wages</b></p>
            </div>
            <div class="col-xs-1 ">:</div>
            <div class="col-xs-6">
                <p data-gjs-editable="false" id="viewDayWages">0</p>
            </div>`
            });
            // OT hours block
            blockManager.add("OT Hours", {
                label: 'OT Hours',
                content: `<div class="col-xs-5">
            <p><b>OT Hours</b></p>
            </div>
            <div class="col-xs-1 ">:</div>
            <div class="col-xs-6">
                <p data-gjs-editable="false" id="viewOTHours">0</p>
            </div>`
            });
            // holidays block
            blockManager.add("Holidays", {
                label: 'Holidays',
                content: `<div class="col-xs-5">
            <p><b>Holidays</b></p>
            </div>
            <div class="col-xs-1 ">:</div>
            <div class="col-xs-6">
                <p data-gjs-editable="false" id="viewHoliday">0</p>
            </div>`
            });
        }
        // Personal details alone block
        let personalDetailsContent =
        `<div class="row" id="personal_details_alone" style="margin-top:30px" data-gjs-custom-name="Personal-form">
            <div class="col-xs-6 form-group">
                <label class="col-xs-5 template-field-label"> Employee Id</label>
                <div class="col-xs-1 ">:</div>
                <div class="col-xs-6 " data-gjs-custom-name="Text">
                    <p data-gjs-editable="false" id="viewMonthlyEmployeeId">Id</p>
                </div>
            </div>
            <div class="col-xs-6 form-group">
                <label class="col-xs-5 template-field-label"> Employee Name</label>
                <div class="col-xs-1 ">:</div>
                <div class="col-xs-6 emp_name_work_break_cls" data-gjs-custom-name="Text">
                    <p data-gjs-editable="false" id="viewMonthlyEmployeeName">Name</p>
                </div>
            </div>
            <div class="col-xs-6 form-group">
                <label class="col-xs-5 template-field-label"> Department </label>
                <div class="col-xs-1 ">:</div>
                <div class="col-xs-6 " data-gjs-custom-name="Text">
                    <p data-gjs-editable="false" id="viewMonthlyDepartment">Dept</p>
                </div>
            </div>
            <div class="col-xs-6 form-group">
                <label class="col-xs-5 template-field-label"> Designation</label>
                <div class="col-xs-1 ">:</div>
                <div class="col-xs-6 " data-gjs-custom-name="Text">
                    <p data-gjs-editable="false" id="viewMonthlyDesignation">Designation</p>
                </div>
            </div>
            <div class="col-xs-6 form-group">
                <label class="col-xs-5 template-field-label">Date Of Join</label>
                <div class="col-xs-1 ">:</div>
                <div class="col-xs-6 " data-gjs-custom-name="Text">
                    <p data-gjs-editable="false" id="viewMonthlyDateOfJoin">dd/mm/yyyy</p>
                </div>
            </div>
            <div class="col-xs-6 form-group">
                <label class="col-xs-5 template-field-label"> ESI/Insurance No</label>
                <div class="col-xs-1 ">:</div>
                <div class="col-xs-6 " data-gjs-custom-name="Text">
                    <p data-gjs-editable="false" id="viewMonthlyESI">ESI</p>
                </div>
            </div>
            <div class="col-xs-6 form-group">
                <label class="col-xs-5 template-field-label"> PF Number </label>
                <div class="col-xs-1 ">:</div>
                <div class="col-xs-6 " data-gjs-custom-name="Text">
                    <p data-gjs-editable="false" id="viewMonthlyPFAccountNumber">PF</p>
                </div>
            </div>
            <div class="col-xs-6 form-group">
                <label class="col-xs-5 template-field-label"> PF UAN No </label>
                <div class="col-xs-1 ">:</div>
                <div class="col-xs-6 " data-gjs-custom-name="Text">
                    <p data-gjs-editable="false" id="viewMonthlUAN">UAN</p>
                </div>
            </div>
            <div class="col-xs-6 form-group">
                <label class="col-xs-5 template-field-label"> PAN Number </label>
                <div class="col-xs-1 ">:</div>
                <div class="col-xs-6 " data-gjs-custom-name="Text">
                    <p data-gjs-editable="false" id="viewMonthlyPANNo">PAN</p>
                </div>
            </div>
            <div class="col-xs-6 form-group">
                <label class="col-xs-5 template-field-label"> Aadhaar Number </label>
                <div class="col-xs-1 ">:</div>
                <div class="col-xs-6 " data-gjs-custom-name="Text">
                    <p data-gjs-editable="false" id="viewMonthlyAadhaarNo">Aadhaar</p>
                </div>
            </div>
            <div class="col-xs-6 form-group">
                <label class="col-xs-5 template-field-label"> Bank Name </label>
                <div class="col-xs-1 ">:</div>
                <div class="col-xs-6 " data-gjs-custom-name="Text">
                    <p data-gjs-editable="false" id="viewMonthlyBankName">Bank</p>
                </div>
            </div>
            <div class="col-xs-6 form-group">
                <label class="col-xs-5 template-field-label"> Bank Account No </label>
                <div class="col-xs-1 ">:</div>
                <div class="col-xs-6 " data-gjs-custom-name="Text">
                    <p data-gjs-editable="false" id="viewMonthlyBankAccountNo">Acc No</p>
                </div>
            </div>
            <div class="col-xs-6 form-group">
                <label class="col-xs-5 template-field-label"> Paid Leave </label>
                <div class="col-xs-1 ">:</div>
                <div class="col-xs-6 " data-gjs-custom-name="Text">
                    <p data-gjs-editable="false" id="viewMonthlyPaidLeave">0</p>
                </div>
            </div>
            <div class="col-xs-6 form-group">
                <label class="col-xs-5 template-field-label" id="viewMonthlyUnpaidLeaveLabel"> Unpaid Leave </label>
                <div class="col-xs-1 ">:</div>
                <div class="col-xs-6 " data-gjs-custom-name="Text">
                    <p data-gjs-editable="false" id="viewMonthlyUnpaidLeave">0</p>
                </div>
            </div>
        `;
        personalDetailsContent += $("#considerCutoffDaysForAttendanceAndTimeoff").val() === "Yes" ? 
            `<div class="col-xs-6 form-group">
                    <label class="col-xs-5 template-field-label" id="viewMonthlyPrevMonthUnpaidLeaveLabel">Previous Month  Unpaid Leave </label>
                    <div class="col-xs-1 ">:</div>
                    <div class="col-xs-6 " data-gjs-custom-name="Text">
                        <p data-gjs-editable="false" id="viewMonthlyPrevMonthUnpaidLeave">0</p>
                    </div>
                </div>` : '';
        personalDetailsContent += `
            <div class="col-xs-6 form-group">
                <label class="col-xs-5 template-field-label"> On Duty </label>
                <div class="col-xs-1 ">:</div>
                <div class="col-xs-6 " data-gjs-custom-name="Text">
                    <p data-gjs-editable="false" id="viewMonthlyOnDuty">0</p>
                </div>
            </div>
            <div class="col-xs-6 form-group">
                <label class="col-xs-5 template-field-label"> Days Worked </label>
                <div class="col-xs-1 ">:</div>
                <div class="col-xs-6 " data-gjs-custom-name="Text">
                    <p data-gjs-editable="false" id="viewMonthlyDaysWorked">0</p>
                </div>
            </div>
            <div class="col-xs-6 form-group">
                <label class="col-xs-5 template-field-label">Paid Days</label>
                <div class="col-xs-1 ">:</div>
                <div class="col-xs-6 " data-gjs-custom-name="Text">
                    <p data-gjs-editable="false" id="viewMonthlyPaidDays">0</p>
                </div>
            </div>
            <div class="col-xs-6 form-group">
                <label class="col-xs-5 template-field-label"> Date Of Relieving </label>
                <div class="col-xs-1 ">:</div>
                <div class="col-xs-6 " data-gjs-custom-name="Text">
                    <p data-gjs-editable="false" id="viewMonthlyDateOfRelieving">dd/mm/yyyy</p>
                </div>
            </div>
            <div style=${payslipType == "Monthly" ? 'display:none' : 'display:block'}>
                <div class="col-xs-6 form-group">
                    <label class="col-xs-5 template-field-label"> Hours Worked </label>
                    <div class="col-xs-1 ">:</div>
                    <div class="col-xs-6 " data-gjs-custom-name="Text">
                        <p data-gjs-editable="false" id="viewHoursWorked">0</p>
                    </div>
                </div>
                <div class="col-xs-6 form-group">
                    <label class="col-xs-5 template-field-label"> Day Wages </label>
                    <div class="col-xs-1 ">:</div>
                    <div class="col-xs-6 " data-gjs-custom-name="Text">
                        <p data-gjs-editable="false" id="viewDayWages">0</p>
                    </div>
                </div>
                <div class="col-xs-6 form-group">
                    <label class="col-xs-5 template-field-label"> OT Hours </label>
                    <div class="col-xs-1 ">:</div>
                    <div class="col-xs-6 " data-gjs-custom-name="Text">
                        <p data-gjs-editable="false" id="viewOTHours">0</p>
                    </div>
                </div>
                <div class="col-xs-6 form-group">
                    <label class="col-xs-5 template-field-label"> Holiday </label>
                    <div class="col-xs-1 ">:</div>
                    <div class="col-xs-6 " data-gjs-custom-name="Text">
                        <p data-gjs-editable="false" id="viewHoliday">0</p>
                    </div>
                </div>
            </div>
        </div>`;

        blockManager.add("payslip-personal-details", {
            label: 'Personal Details',
            content: personalDetailsContent + Common_Style
        });
        
        blockManager.add("Earnings and Deductions", {
            label: 'Earnings & Deductions',
            content: `<div class="row" style="margin-right: 0; margin-left: 0;">
            <div class="col-xs-12" style="padding: 0px;">
                <table class="table" style="width: 100%;">
                    <thead>
                        <tr class="trClass">
                            <th style="width: 25%;"><h4 class="h4style">Earnings</h4></th>
                            <th style="width: 25%; text-align: right; border-right: 1px solid #ddd;"><h4 class="h4style" id="earnAmt">Amount</h4></th>
                            <th style="width: 25%;"><h4 class="h4style">Deductions</h4></th>
                            <th style="width: 25%; text-align: right;"><h4 class="h4style" id="deductAmt">Amount</h4></th>
                        </tr>
                    </thead>
                    <tbody id="earningsDeductionsTab">
                        <!-- Dynamic content will be populated here -->
                    </tbody>
                    <tfoot id="earningsDeductionsTabFooter">
                        <tr class="child" style="font-weight:bold;border-top:1px solid;height:49px;color: #000;">
                            <td class="text-left" style="color: #000;"><p style="color: #000;">Total Earnings</p></td>
                            <td style="text-align: right; border-right: 1px solid #ddd; color: #000;"><p id="totalEarningsAmount" style="color: #000; margin: 0; text-align: right;"></p></td>
                            <td class="text-left" style="color: #000;"><p style="color: #000;">Total Deductions</p></td>
                            <td style="text-align: right; color: #000;"><p id="totalDeductionsAmount" style="color: #000; margin: 0; text-align: right;"></p></td>
                        </tr>
                        <tr class="child" style="font-weight:bold;border-top:1px solid;height:49px;color: #000;">
                            <td class="text-left" style="color: #000;"><p style="color: #000;">Outstanding Amount</p></td>
                            <td style="text-align: right; border-right: 1px solid #ddd; color: #000;"><p id="totalOutstandingAmount" style="color: #000; margin: 0; text-align: right;"></p></td>
                            <td class="text-left" style="color: #000;"><p style="color: #000;">Net Pay</p></td>
                            <td style="text-align: right; color: #000;"><p id="totalNetPay" style="color: #000; margin: 0; text-align: right;"></p></td>
                        </tr>
                        <tr class="child totalIntermediatePaymentPanel" style="font-weight:bold;border-top:1px solid;height:49px;color: #000;">
                            <td class="text-left" style="color: #000;"><p style="color: #000;">Total Intermediate Payment</p></td>
                            <td style="text-align: right; border-right: 1px solid #ddd; color: #000;"><p id="totalIntermediatePayment" style="color: #000; margin: 0; text-align: right;"></p></td>
                            <td class="text-left" style="color: #000;"><p style="color: #000;"></p></td>
                            <td style="text-align: right; color: #000;"><p style="color: #000;"></p></td>
                        </tr>
                    </tfoot>
                </table>

                <!-- Hidden containers to maintain backward compatibility with existing JavaScript -->
                <div style="display: none;">
                    <div id="earningsTab"></div>
                    <div id="deductTab"></div>
                    <div id="earningsTabFooter"></div>
                    <div id="deductionsTabFooter"></div>
                </div>
            </div>
            <div class="col-xs-12" style="border:1px solid black;padding: 0px;">
                <div style="display: inline-block;color: #000;font-size: 16px;padding: 8px;" >
                <b>Netpay In Words </b>
                <div id="netpayInWord" style="display: inline;"></div>
                </div>
            </div>
        </div>
        ${Common_Style}`
        });


        // organization contribution block
        blockManager.add("Organization Contribution", {
            label: 'Organization Contribution',
            content: `<div class="row" style="padding:20px" id="empContribution">
            <h4 class="h4style" style="text-align:left;height:40px;border-bottom:1px solid black;padding-top: 10px;padding-left: 10px;color: #000;">Organization Contribution</h4>
            <div class="col-xs-6" style="padding: 0px;">
                <table class="table" >
                    <tbody id="contributionTab1"></tbody>
                </table>
            </div>
            <div class="col-xs-6" style="padding: 0px;">
                <table class="table" >
                    <tbody id="contributionTab2"></tbody>
                </table>
            </div>
        </div>
        </div>
        ${Common_Style}`
        });

        blockManager.add("Total Earnings And Organization Contribution In Words", {
            label: 'Total Earnings And Organization Contribution In Words',
            content: `<div class="col-xs-12" style="border:1px solid black;padding: 0px;">
            <div style="display: inline-block;color: #000;font-size: 16px;padding: 8px;" >
                <b>Total Earnings And Organization Contribution In Words</b>
                <div id="totalEarningsAndOrgShareAmountInWord" style="display: inline;">
           </div>
        </div>
        ${Common_Style}`
        });

        if(payslipType == "Monthly"){
            // Form16 block
            blockManager.add("Form16, Exemptions & Sec 10", {
                label: 'Form16, Exemptions & Sec 10',
                content: `<div class="row" style="padding:20px" id="empForm16Table">
                <div class="col-xs-6 form-group" style="padding: 0px;">
                    <table class="table">
                        <thead>
                            <tr class="trClass" style="height:61px">
                                <th style="width: 77%;"><h4 class="h4style">Perks/ Other Income/ Exemptions/
                                    Rebate</h4></th>
                                <th class="text-right" ><h4 class="h4style" id="exemptionsAmt">Amount</h4></th>
                            </tr>
                        </thead>
                        <tbody id="exemptionsPanel"></tbody>
                    </table>
                    <table class="table">
                        <thead>
                            <tr class="trClass">
                                <th style="width: 50%;"><h4 class="h4style">Sec 10 Exemption</h4></th>
                                <th class="text-right" ><h4 class="h4style" id="sectionsAmt">Amount</h4></th>
                            </tr>
                        </thead>
                        <tbody id="sec10ExemptionsPanel"></tbody>
                    </table>
                </div>
                <div class="col-xs-6 form-group" style="padding: 0px;">
                    <table class="table">
                        <thead>
                            <tr class="trClass" style="height:61px">
                                <th style="width: 50%;"><h4 class="h4style">Form16 Summary</h4></th>
                                <th class="text-right" ><h4 class="h4style" id="form16SummaryAmt">Amount</h4></th>
                            </tr>
                        </thead>
                        <tbody id="form16SummaryTab">
                            <tr class="trClass" style="height:120px"></tr>
                        </tbody>
                    </table>
                </div>
            </div>
            ${Common_Style}`
            });
        }
        let leaveDetailsContent = `<div class="panel-body" style="padding: 0px;border: 2px solid #807c7c;width:75%; margin:auto" id="leaveDetails">
            <h3 class="template-text-center" style="margin: 0 0 0px;color: #000;"><b>Leave Details</b></h3>
            <div class="row" style="border-top: 3px solid #807c7c;margin-right: 0; margin-left: 0;">
                <div class="col-xs-6" style="padding: 0px;border-right: 2px solid #807c7c;min-height: 110px">
                    <p class="template-text-center" style="margin: 0 0 0px;border-bottom: 2px solid #807c7c"><b>Paid Leave</b></p>
                    <table class="table" style="margin-bottom:0px">
                        <thead style="border-bottom: 3px solid #807c7c">
                            <tr class="trClass">
                                <th><h4 class="h4style">Leave Type</h4></th>
                                <th class="text-right"><h4 class="h4style">Balance</h4></th>
                            </tr>
                        </thead>
                        <tbody id="viewMonthlyPaidLeaveDetails">
                        <tr class="trClass">
                            <td class="col-xs-10 text-left" id="upltype" style="height:40px;word-break: break-word;">
                            -
                            </td>
                            <td class="text-right" id="uplbalance" style="height:40px">
                            -
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-xs-6" style="padding: 0px;min-height: 110px;">
                    <p class="template-text-center" style="margin: 0 0 0px;border-bottom: 2px solid #807c7c"><b>Unpaid Leave</b></p>
                    <table class="table" style="margin-bottom:0px">
                        <thead style="border-bottom: 3px solid #807c7c">
                            <tr class="trClass">
                                <th><h4 class="h4style">Leave Type</h4></th>
                                <th class="text-right"><h4 class="h4style">Balance</h4></th>
                            </tr>
                        </thead>
                        <tbody id="viewMonthlyUnpaidLeaveDetails">
                        <tr class="trClass">
                            <td class="col-xs-10 text-left" id="upltype" style="height:40px;word-break: break-word;">
                            -
                            </td>
                            <td class="text-right" id="uplbalance" style="height:40px">
                            -
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        `;
        // Leave Table
        blockManager.add("Leave Details", {
            label: 'Leave Details',
            content: leaveDetailsContent + Common_Style
        })
        // payslip1 design block
        let payslipSample1Content = `<div class="print-container clearfix" id="payslip1">
            <div class="template-header">
                <h2 class="text-right">PaySlip</h2>
                <div class="panel panel-default" style="margin-bottom:0px">
                    <div class="panel-heading" style="border: 2px solid #807c7c;">
                        <div class="row">
                            <div class="col-xs-9">
                                <div class="col-xs-6">
                                    <div id="payslipLogo">
                                        <img alt="logo" src="${image_path}" style="width:200px"/>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div class="template-text-center">
                                        <h3 id="formHeader">Company Name</h3>
                                        <h4 id="payMonth">Title</h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xs-3 invoice-from"  style="${$('#displayPayslipAddress').val() == 0 ? 'display:none' : null}" id="payslipTemplateAddress">
                                <br />     
                                <div style="text-align:right">
                                    <p id="payslipStreet1">Street1,</p> 
                                    <p id="payslipStreet2">Street2,</p> 
                                </div>
                                <div style="text-align:right">
                                    <p id="payslipCity">City,</p> 
                                    <p id="payslipState">State,</p> 
                                </div>
                                <div style="text-align:right">
                                    <p id="payslipCountry">Country,</p> 
                                    <p id="payslipPinCode">Pin</p> 
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="body" style="padding: 0px;">
                <div class="panel-body" style="padding: 0px;border: 2px solid #807c7c;">
                    <br>
                    <div class="row">
                        <div class="col-xs-6">
                            <div class="col-xs-6">
                            <p><b>Employee Name</b></p>
                            </div>
                            <div class="col-xs-6 emp_name_work_break_cls">
                                <p data-gjs-editable="false" id="viewMonthlyEmployeeName">Name</p>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="col-xs-6">
                                <p><b>Employee Id</b></p>
                            </div>
                            <div class="col-xs-6">
                                <p data-gjs-editable="false" id="viewMonthlyEmployeeId">Id</p>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="col-xs-6">
                                <p><b>Department</b></p>
                            </div>
                            <div class="col-xs-6">
                                <p data-gjs-editable="false" id="viewMonthlyDepartment">Dept</p>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="col-xs-6">
                                <p><b>Designation</b></p>
                            </div>
                            <div class="col-xs-6">
                                <p data-gjs-editable="false" id="viewMonthlyDesignation">Designation</p>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="col-xs-6">
                                <p><b>Date Of Join</b></p>
                            </div>
                            <div class="col-xs-6">
                                <p data-gjs-editable="false" id="viewMonthlyDateOfJoin">dd/mm/yyyy</p>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="col-xs-6">
                               <p><b>ESI/Insurance No</b></p>
                            </div>
                            <div class="col-xs-6">
                                <p data-gjs-editable="false" id="viewMonthlyESI">ESI</p>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="col-xs-6">
                                <p><b>PF Number</b></p>
                            </div>
                            <div class="col-xs-6">
                                <p data-gjs-editable="false" id="viewMonthlyPFAccountNumber">PF</p>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="col-xs-6">
                                <p><b>PF UAN No</b></p>
                            </div>
                            <div class="col-xs-6">
                                <p data-gjs-editable="false" id="viewMonthlUAN">UAN</p>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="col-xs-6">
                                <p><b>PAN Number</b>
                            </div>
                            <div class="col-xs-6">
                                <p data-gjs-editable="false" id="viewMonthlyPANNo">PAN</p>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="col-xs-6">
                                <p><b>Aadhaar Number</b></p>
                            </div>
                            <div class="col-xs-6">
                                <p data-gjs-editable="false" id="viewMonthlyAadhaarNo">Aadhaar</p>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="col-xs-6">
                                <p><b>Bank Name</b></p>
                            </div>
                            <div class="col-xs-6">
                                <p data-gjs-editable="false" id="viewMonthlyBankName">Bank</p>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="col-xs-6">
                                <p><b>Bank Account No</b></p>
                            </div>
                            <div class="col-xs-6">
                                <p data-gjs-editable="false" id="viewMonthlyBankAccountNo">Acc No</p>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="col-xs-6">
                                <p><b>Paid Leave</b></p>
                            </div>
                            <div class="col-xs-6">
                                <p data-gjs-editable="false" id="viewMonthlyPaidLeave">0</p>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="col-xs-6">
                                <p><b id="viewMonthlyUnpaidLeaveLabel">Unpaid Leave</b></p>
                            </div>
                            <div class="col-xs-6">
                                <p data-gjs-editable="false" id="viewMonthlyUnpaidLeave">0</p>
                            </div>
                        </div>
                        `;
                        payslipSample1Content += $("#considerCutoffDaysForAttendanceAndTimeoff").val() === "Yes"  ?
                        `<div class="col-xs-6" id="viewMonthlyPrevMonthUnpaidLeaveElement">
                            <div class="col-xs-6">
                                <p><b id="viewMonthlyPrevMonthUnpaidLeaveLabel">Previous Month Unpaid Leave</b></p>
                            </div>
                            <div class="col-xs-6">
                                <p data-gjs-editable="false" id="viewMonthlyPrevMonthUnpaidLeave">0</p>
                            </div>
                        </div>` : '';
                        payslipSample1Content += `<div class="col-xs-6">
                            <div class="col-xs-6">
                                <p><b>On Duty</b></p>
                            </div>
                            <div class="col-xs-6">
                                <p data-gjs-editable="false" id="viewMonthlyOnDuty">0</p>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="col-xs-6">
                               <p><b>Days Worked</b></p>
                            </div>
                            <div class="col-xs-6">
                                <p data-gjs-editable="false" id="viewMonthlyDaysWorked">0</p>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="col-xs-6">
                               <p><b>Paid Days</b></p>
                            </div>
                            <div class="col-xs-6">
                                <p data-gjs-editable="false" id="viewMonthlyPaidDays">0</p>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="col-xs-6">
                               <p><b>Date Of Relieving</b></p>
                            </div>
                            <div class="col-xs-6">
                                <p data-gjs-editable="false" id="viewMonthlyDateOfRelieving">dd/mm/yyyy</p>
                            </div>
                        </div>
                        <div style="${payslipType == "Monthly" ? 'display:none' : 'display:block' }">
                            <div class="col-xs-6">
                                <div class="col-xs-6">
                                <p><b>Hours Worked</b></p>
                                </div>
                                <div class="col-xs-6">
                                    <p data-gjs-editable="false" id="viewHoursWorked">0</p>
                                </div>
                            </div>
                            <div class="col-xs-6">
                                <div class="col-xs-6">
                                <p><b>Day Wages</b></p>
                                </div>
                                <div class="col-xs-6">
                                    <p data-gjs-editable="false" id="viewDayWages">0</p>
                                </div>
                            </div>
                            <div class="col-xs-6">
                                <div class="col-xs-6">
                                <p><b>OT Hours</b></p>
                                </div>
                                <div class="col-xs-6">
                                    <p data-gjs-editable="false" id="viewOTHours">0</p>
                                </div>
                            </div>
                            <div class="col-xs-6">
                                <div class="col-xs-6">
                                <p><b>Holiday</b></p>
                                </div>
                                <div class="col-xs-6">
                                    <p data-gjs-editable="false" id="viewHoliday">0</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <hr style="border-top: 2px solid #807c7c;">
                    <div class="row" style="border-top: 2px solid #807c7c;border-bottom: 2px solid #807c7c;margin-right: 0; margin-left: 0;">
                        <div class="col-xs-12" style="padding: 0px;">
                            <table class="table" style="width: 100%;">
                                <thead style="border-bottom: 3px solid #807c7c">
                                    <tr class="trClass">
                                        <th style="width: 25%;"><h4 class="h4style">Earnings</h4></th>
                                        <th style="width: 25%; text-align: right; border-right: 2px solid #807c7c;"><h4 class="h4style" id="earnAmt">Amount</h4></th>
                                        <th style="width: 25%;"><h4 class="h4style">Deductions</h4></th>
                                        <th style="width: 25%; text-align: right;"><h4 class="h4style" id="deductAmt">Amount</h4></th>
                                    </tr>
                                </thead>
                                <tbody id="earningsDeductionsTab">
                                    <!-- Dynamic content will be populated here by JavaScript -->
                                </tbody>
                                <tfoot id="earningsDeductionsTabFooter">
                                    <tr class="child" style="font-weight:bold;border-top:1px solid;height:49px;color: #000;">
                                        <td class="text-left" style="color: #000;"><p style="color: #000;">Total Earnings</p></td>
                                        <td style="text-align: right; border-right: 2px solid #807c7c; color: #000;"><p id="totalEarningsAmount" style="color: #000; margin: 0; text-align: right;"></p></td>
                                        <td class="text-left" style="color: #000;"><p style="color: #000;">Total Deductions</p></td>
                                        <td style="text-align: right; color: #000;"><p id="totalDeductionsAmount" style="color: #000; margin: 0; text-align: right;"></p></td>
                                    </tr>
                                    <tr class="child" style="font-weight:bold;border-top:1px solid;height:49px;color: #000;">
                                        <td class="text-left" style="color: #000;"><p style="color: #000;">Outstanding Amount</p></td>
                                        <td style="text-align: right; border-right: 2px solid #807c7c; color: #000;"><p id="totalOutstandingAmount" style="color: #000; margin: 0; text-align: right;"></p></td>
                                        <td class="text-left" style="color: #000;"><p style="color: #000;">Net Pay</p></td>
                                        <td style="text-align: right; color: #000;"><p id="totalNetPay" style="color: #000; margin: 0; text-align: right;"></p></td>
                                    </tr>
                                    <tr class="child totalIntermediatePaymentPanel" style="font-weight:bold;border-top:1px solid;height:49px;color: #000;">
                                        <td class="text-left" style="color: #000;"><p style="color: #000;">Total Intermediate Payment</p></td>
                                        <td style="text-align: right; border-right: 2px solid #807c7c; color: #000;"><p id="totalIntermediatePayment" style="color: #000; margin: 0; text-align: right;"></p></td>
                                        <td class="text-left" style="color: #000;"><p style="color: #000;"></p></td>
                                        <td style="text-align: right; color: #000;"><p style="color: #000;"></p></td>
                                    </tr>
                                </tfoot>
                            </table>

                            <!-- Hidden containers to maintain backward compatibility with existing JavaScript -->
                            <div style="display: none;">
                                <div id="earningsTab"></div>
                                <div id="deductTab"></div>
                                <div id="earningsTabFooter"></div>
                                <div id="deductionsTabFooter"></div>
                            </div>
                        </div>
                        <div class="col-xs-12" style="border-top:1px solid black;padding: 0px;">
                            <div style="display: inline-block;color: #000;font-size: 16px;padding: 8px;" >
                                <b>Netpay In Words </b>
                                <div id="netpayInWord" style="display: inline;"></div>
                            </div>
                        </div>
                    </div>
                    <br />
                    <div class="row payslip-template-1-orgCon" id="empContribution">
                        <h4 class="h4style" style="text-align:left;height:40px;border-bottom:1px solid black;color: #000;padding-top: 10px;padding-left: 10px">Organization Contribution</h4>
                        <div class="col-xs-6" style="padding: 0px;border-right:2px solid #807c7c">
                            <table class="table">
                                <tbody id="contributionTab1"></tbody>
                            </table>
                        </div>
                        <div class="col-xs-6" style="padding: 0px;">
                            <table class="table">
                                <tbody id="contributionTab2"></tbody>
                            </table>
                        </div>
                    </div>
                    <br />
                    <div class="col-xs-12 payslip-template-1-form16" style="${payslipType == "Hourly" ? 'display:none' : 'display:block' }" id="empForm16Table">
                        <div class="col-xs-6" style="padding: 0px;">
                            <table class="table">
                                <thead style="border-bottom: 3px solid #807c7c">
                                    <tr class="trClass" style="height:61px">
                                        <th style="width: 77%;"><h4 class="h4style">Perks/Other Income/ Exemptions/
                                            Rebate</h4></th>
                                        <th class="text-right" ><h4 id="exemptionsAmt"class="h4style">Amount</h4></th>
                                    </tr>
                                </thead>
                                <tbody id="exemptionsPanel"></tbody>
                            </table>
                            <table class="table">
                                <thead style="border-top: 2px solid #807c7c;border-bottom: 3px solid #807c7c">
                                    <tr class="trClass">
                                        <th style="width: 60%;"><h4 class="h4style">Sec 10 Exemption</h4></th>
                                        <th class="text-right" ><h4 id="sectionsAmt" class="h4style">Amount</h4></th>
                                    </tr>
                                </thead>
                                <tbody id="sec10ExemptionsPanel"></tbody>
                            </table>
                        </div>
                        <div class="col-xs-6" style="padding: 0px;border-left: 2px solid #807c7c">
                            <table class="table">
                                <thead style="border-bottom: 3px solid #807c7c">
                                    <tr class="trClass" style="height:61px">
                                        <th style="width: 60%;"><h4 class="h4style">Form16 Summary</h4></th>
                                        <th class="text-right" ><h4 id="form16SummaryAmt" class="h4style">Amount</h4></th>
                                    </tr>
                                </thead>
                                <tbody id="form16SummaryTab">
                                    <tr class="trClass" style="height:120px"></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <br />
                <div class="panel-body" style="padding: 0px;border: 2px solid #807c7c;width:75%; margin:auto" id="leaveDetails">
                    <h3 class="template-text-center" style="margin: 0 0 0px;"><b>Leave Details</b></h3>
                    <div class="row" style="border-top: 3px solid #807c7c;margin-right: 0; margin-left: 0;">
                        <div class="col-xs-6" style="padding: 0px;border-right: 2px solid #807c7c;min-height: 110px">
                            <p class="template-text-center" style="margin: 0 0 0px;border-bottom: 2px solid #807c7c"><b>Paid Leave</b></p>
                            <table class="table" style="margin-bottom:0px">
                                <thead style="border-bottom: 3px solid #807c7c">
                                    <tr class="trClass">
                                        <th><h4 class="h4style">Leave Type</h4></th>
                                        <th class="text-right"><h4 class="h4style">Balance</h4></th>
                                    </tr>
                                </thead>
                                <tbody id="viewMonthlyPaidLeaveDetails">
                                </tbody>
                            </table>
                        </div>
                        <div class="col-xs-6" style="padding: 0px;min-height: 110px;">
                            <p class="template-text-center" style="margin: 0 0 0px;border-bottom: 2px solid #807c7c"><b>Unpaid Leave</b></p>
                            <table class="table" style="margin-bottom:0px">
                                <thead style="border-bottom: 3px solid #807c7c">
                                    <tr class="trClass">
                                        <th><h4 class="h4style">Leave Type</h4></th>
                                        <th class="text-right"><h4 class="h4style">Balance</h4></th>
                                    </tr>
                                </thead>
                                <tbody id="viewMonthlyUnpaidLeaveDetails">
                                </tbody>
                            </table>
                        </div>
                </div>
            </div>
        </div>
        `;
        blockManager.add("Payslip-Design-1", {
            label: 'Payslip Sample 1',
            content: payslipSample1Content + Common_Style
        });
        // payslip2 design block
        let payslipSample2Content = `<div class="print-container clearfix" id="payslip2">
            <div class="template-header">
                <div class="template-text-center">
                    <h3 id="formHeader">Company Name</h3>
                    <h4 style="font-weight:600" id="payMonth">Title</h4>
                </div>
                <div class="row">
                    <div class="col-xs-4">
                        <div id="payslipLogo">
                            <img alt="logo" src="${image_path}" style="width:200px"/>
                        </div>
                    </div>
                    <div class="col-xs-5">
                    </div>
                    <div class="col-xs-3 invoice-from" style="${$('#displayPayslipAddress').val() == 0 ? 'display:none' : null}" id="payslipTemplateAddress">        
                        <div style="text-align:right">
                            <p id="payslipStreet1">Street1,</p> 
                            <p id="payslipStreet2">Street2,</p> 
                        </div>
                        <div style="text-align:right">
                            <p id="payslipCity">City,</p> 
                            <p id="payslipState">State,</p> 
                        </div>
                        <div style="text-align:right">
                            <p id="payslipCountry">Country,</p> 
                            <p id="payslipPinCode">Pin</p> 
                        </div>
                    </div>
                </div>
            </div>
            <br>
            <div class="body">
                <div class="row">
                    <div class="col-xs-6" style="padding-bottom:8px">
                        <div class="col-xs-6">
                            <p><b>Employee Name</b></p>
                        </div>
                        <div class="col-xs-6 emp_name_work_break_cls">
                            <p data-gjs-editable="false" id="viewMonthlyEmployeeName">Name</p>
                        </div>
                    </div>
                    <div class="col-xs-6" style="padding-bottom:8px">
                        <div class="col-xs-6">
                            <p><b>Employee Id</b></p>
                        </div>
                        <div class="col-xs-6">
                            <p data-gjs-editable="false" id="viewMonthlyEmployeeId">Id</p>
                        </div>
                    </div>
                    <div class="col-xs-6" style="padding-bottom:8px">
                        <div class="col-xs-6">
                            <p><b>Department</b></p>
                        </div>
                        <div class="col-xs-6">
                            <p data-gjs-editable="false" id="viewMonthlyDepartment">Dept</p>
                        </div>
                    </div>
                    <div class="col-xs-6" style="padding-bottom:8px">
                        <div class="col-xs-6">
                            <p><b>Designation</b></p>
                        </div>
                        <div class="col-xs-6">
                            <p data-gjs-editable="false" id="viewMonthlyDesignation">Designation</p>
                        </div>
                    </div>
                    <div class="col-xs-6" style="padding-bottom:8px">
                        <div class="col-xs-6">
                            <p><b>Date Of Join</b></p>
                        </div>
                        <div class="col-xs-6">
                            <p data-gjs-editable="false" id="viewMonthlyDateOfJoin">dd/mm/yyyy</p>
                        </div>
                    </div>
                    <div class="col-xs-6" style="padding-bottom:8px">
                        <div class="col-xs-6">
                            <p><b>ESI/Insurance No</b></p>
                        </div>
                        <div class="col-xs-6">
                            <p data-gjs-editable="false" id="viewMonthlyESI">ESI</p>
                        </div>
                    </div>
                    <div class="col-xs-6" style="padding-bottom:8px">
                        <div class="col-xs-6">
                            <p><b>PF Number</b></p>
                        </div>
                        <div class="col-xs-6">
                            <p data-gjs-editable="false" id="viewMonthlyPFAccountNumber">PF</p>
                        </div>
                    </div>
                    <div class="col-xs-6" style="padding-bottom:8px">
                        <div class="col-xs-6">
                            <p><b>PF UAN No</b></p>
                        </div>
                        <div class="col-xs-6">
                            <p data-gjs-editable="false" id="viewMonthlUAN">UAN</p>
                        </div>
                    </div>
                    <div class="col-xs-6" style="padding-bottom:8px">
                        <div class="col-xs-6">
                            <p><b>PAN Number</b></p>
                        </div>
                        <div class="col-xs-6">
                            <p data-gjs-editable="false" id="viewMonthlyPANNo">PAN</p>
                        </div>
                    </div>
                    <div class="col-xs-6" style="padding-bottom:8px">
                        <div class="col-xs-6">
                            <p><b>Aadhaar Number</b></p>
                        </div>
                        <div class="col-xs-6">
                            <p data-gjs-editable="false" id="viewMonthlyAadhaarNo">Aadhaar</p>
                        </div>
                    </div>
                    <div class="col-xs-6" style="padding-bottom:8px">
                        <div class="col-xs-6">
                            <p><b>Bank Name</b></p>
                        </div>
                        <div class="col-xs-6">
                            <p data-gjs-editable="false" id="viewMonthlyBankName">Bank</p>
                        </div>
                    </div>
                    <div class="col-xs-6" style="padding-bottom:8px">
                        <div class="col-xs-6">
                            <p><b>Bank Account No</b></p>
                        </div>
                        <div class="col-xs-6">
                            <p data-gjs-editable="false" id="viewMonthlyBankAccountNo">Acc No</p>
                        </div>
                    </div>
                    <div class="col-xs-6" style="padding-bottom:8px">
                        <div class="col-xs-6">
                            <p><b>Paid Leave</b></p>
                        </div>
                        <div class="col-xs-6">
                            <p data-gjs-editable="false" id="viewMonthlyPaidLeave">0</p>
                        </div>
                    </div>
                    <div class="col-xs-6" style="padding-bottom:8px">
                        <div class="col-xs-6">
                            <p><b id="viewMonthlyUnpaidLeaveLabel">Unpaid Leave</b></p>
                        </div>
                        <div class="col-xs-6">
                            <p data-gjs-editable="false" id="viewMonthlyUnpaidLeave">0</p>
                        </div>
                    </div>
                    `;
                    payslipSample2Content += $("#considerCutoffDaysForAttendanceAndTimeoff").val() === "Yes"  ?
                    `<div class="col-xs-6" style="padding-bottom:8px" id="viewMonthlyPrevMonthUnpaidLeaveElement">
                        <div class="col-xs-6">
                            <p><b id="viewMonthlyPrevMonthUnpaidLeaveLabel">Previous Month Unpaid Leave</b></p>
                        </div>
                        <div class="col-xs-6">
                            <p data-gjs-editable="false" id="viewMonthlyPrevMonthUnpaidLeave">0</p>
                        </div>
                    </div>` : '';
                    payslipSample2Content += `<div class="col-xs-6" style="padding-bottom:8px">
                        <div class="col-xs-6">
                            <p><b>On Duty</b></p>
                        </div>
                        <div class="col-xs-6">
                            <p data-gjs-editable="false" id="viewMonthlyOnDuty">0</p>
                        </div>
                    </div>
                    <div class="col-xs-6" style="padding-bottom:8px">
                        <div class="col-xs-6">
                            <p><b>Days Worked</b></p>
                        </div>
                        <div class="col-xs-6">
                            <p data-gjs-editable="false" id="viewMonthlyDaysWorked">0</p>
                        </div>
                    </div>
                    <div class="col-xs-6" style="padding-bottom:8px">
                        <div class="col-xs-6">
                            <p><b>Paid Days</b></p>
                        </div>
                        <div class="col-xs-6">
                            <p data-gjs-editable="false" id="viewMonthlyPaidDays">0</p>
                        </div>
                    </div>
                    <div class="col-xs-6" style="padding-bottom:8px">
                        <div class="col-xs-6">
                            <p><b>Date Of Relieving</b></p>
                        </div>
                        <div class="col-xs-6">
                            <p data-gjs-editable="false" id="viewMonthlyDateOfRelieving">dd/mm/yyyy</p>
                        </div>
                    </div>
                    <div style="${payslipType == "Monthly" ? 'display:none' : 'display:block' }">
                        <div class="col-xs-6" style="padding-bottom:8px">
                            <div class="col-xs-6">
                                <p><b>Hours Worked</b></p>
                            </div>
                            <div class="col-xs-6">
                                <p data-gjs-editable="false" id="viewHoursWorked">0</p>
                            </div>
                        </div>
                        <div class="col-xs-6" style="padding-bottom:8px">
                            <div class="col-xs-6">
                                <p><b>Day Wages</b></p>
                            </div>
                            <div class="col-xs-6">
                                <p data-gjs-editable="false" id="viewDayWages">0</p>
                            </div>
                        </div>
                        <div class="col-xs-6" style="padding-bottom:8px">
                            <div class="col-xs-6">
                                <p><b>OT Hours</b></p>
                            </div>
                            <div class="col-xs-6">
                                <p data-gjs-editable="false" id="viewOTHours">0</p>
                            </div>
                        </div>
                        <div class="col-xs-6" style="padding-bottom:8px">
                            <div class="col-xs-6">
                                <p><b>Holiday</b></p>
                            </div>
                            <div class="col-xs-6">
                                <p data-gjs-editable="false" id="viewHoliday">0</p>
                            </div>
                        </div>                        
                    </div>
                </div>
                <br />
                <div class="col-xs-12" style="border: 1px solid black;padding:0px">
                    <div class="col-xs-12" style="padding: 0px;">
                        <table class="table table-striped" style="width: 100%;">
                            <thead>
                                <tr class="trClass" style="background:lightgray !important; border-bottom: 3px solid #7d7d7d;">
                                    <th style="width: 25%;"><h4 class="h4style">Earnings</h4></th>
                                    <th style="width: 25%; text-align: right; border-right: 1px solid black;"><h4 class="h4style" id="earnAmt">Amount</h4></th>
                                    <th style="width: 25%;"><h4 class="h4style">Deductions</h4></th>
                                    <th style="width: 25%; text-align: right;"><h4 class="h4style" id="deductAmt">Amount</h4></th>
                                </tr>
                            </thead>
                            <tbody id="earningsDeductionsTab">
                                <!-- Dynamic content will be populated here by JavaScript -->
                            </tbody>
                            <tfoot id="earningsDeductionsTabFooter">
                                <tr class="child" style="font-weight:bold;border-top:1px solid;min-height:49px;color: #000;">
                                    <td class="text-left" style="color: #000; vertical-align: top; padding: 8px;"><p style="color: #000; margin: 0;">Total Earnings</p></td>
                                    <td style="text-align: right; border-right: 1px solid black; color: #000; vertical-align: top; padding: 8px;"><p id="totalEarningsAmount" style="color: #000; margin: 0; text-align: right;"></p></td>
                                    <td class="text-left" style="color: #000; vertical-align: top; padding: 8px;"><p style="color: #000; margin: 0;">Total Deductions</p></td>
                                    <td style="text-align: right; color: #000; vertical-align: top; padding: 8px;"><p id="totalDeductionsAmount" style="color: #000; margin: 0; text-align: right;"></p></td>
                                </tr>
                                <tr class="child" style="font-weight:bold;border-top:1px solid;min-height:49px;color: #000;">
                                    <td class="text-left" style="color: #000; vertical-align: top; padding: 8px;"><p style="color: #000; margin: 0;">Outstanding Amount</p></td>
                                    <td style="text-align: right; border-right: 1px solid black; color: #000; vertical-align: top; padding: 8px;"><p id="totalOutstandingAmount" style="color: #000; margin: 0; text-align: right;"></p></td>
                                    <td class="text-left" style="color: #000; vertical-align: top; padding: 8px;"><p style="color: #000; margin: 0;">Net Pay</p></td>
                                    <td style="text-align: right; color: #000; vertical-align: top; padding: 8px;"><p id="totalNetPay" style="color: #000; margin: 0; text-align: right;"></p></td>
                                </tr>
                                <tr class="child totalIntermediatePaymentPanel" style="font-weight:bold;border-top:1px solid;min-height:49px;color: #000;">
                                    <td class="text-left" style="color: #000; vertical-align: top; padding: 8px;"><p style="color: #000; margin: 0;">Total Intermediate Payment</p></td>
                                    <td style="text-align: right; border-right: 1px solid black; color: #000; vertical-align: top; padding: 8px;"><p id="totalIntermediatePayment" style="color: #000; margin: 0; text-align: right;"></p></td>
                                    <td class="text-left" style="color: #000; vertical-align: top; padding: 8px;"><p style="color: #000; margin: 0;"></p></td>
                                    <td style="text-align: right; color: #000; vertical-align: top; padding: 8px;"><p style="color: #000; margin: 0;"></p></td>
                                </tr>
                            </tfoot>
                        </table>

                        <!-- Hidden containers to maintain backward compatibility with existing JavaScript -->
                        <div style="display: none;">
                            <div id="earningsTab"></div>
                            <div id="deductTab"></div>
                            <div id="earningsTabFooter"></div>
                            <div id="deductionsTabFooter"></div>
                        </div>
                    </div>
                    <div class="col-xs-12" style="border-top:1px solid black;border-bottom:1px solid black;padding: 0px;">
                        <div style="display: inline-block;color: #000;font-size: 16px;padding: 8px;" >
                            <b>Netpay In Words </b>
                            <div id="netpayInWord" style="display: inline;"></div>
                        </div>
                    </div>
                    <div class="col-xs-12 payslip-template-2-orgCon" id="empContribution">
                         <h4 class="h4style" style="text-align:left;height:40px;border-bottom:1px solid black;padding-top: 10px;background:lightgray !important;color: #000;padding-left: 10px">Organization Contribution</h4>
                        <div class="col-xs-6" style="padding: 0px;border-right:1px solid black;border-bottom:1px solid black">
                            <table class="table table-striped" style="margin-bottom:0px">
                                <tbody id="contributionTab1"></tbody>
                            </table>
                        </div>
                        <div class="col-xs-6" style="padding: 0px;border-bottom:1px solid black">
                            <table class="table table-striped">
                                <tbody id="contributionTab2"></tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-xs-12 paddingZeroTemplate" style="${payslipType == "Hourly" ? 'display:none' : 'display:block' }" id="empForm16Table">
                        <div class="col-xs-6" style="padding: 0px;margin-top: 30px;border-top:1px solid black">
                            <table class="table table-striped">
                                <thead>
                                    <tr class="trClass" style="background:lightgray !important; border-bottom: 3px solid #7d7d7d;height:61px">
                                        <th style="width: 77%;"><h4 class="h4style">Perks/ Other Income/ Exemptions/
                                            Rebate</h4></th>
                                        <th class="text-right" ><h4 id="exemptionsAmt" class="h4style">Amount</h4></th>
                                    </tr>
                                </thead>
                                <tbody id="exemptionsPanel"></tbody>
                            </table>
                            <table class="table  table-striped">
                                <thead>
                                    <tr class="trClass" style="background:lightgray !important; border-bottom: 3px solid #7d7d7d;border-top:1px solid black;">
                                        <th style="width: 50%;"><h4 class="h4style">Sec 10 Exemption</h4></th>
                                        <th class="text-right" ><h4 id="sectionsAmt" class="h4style">Amount</h4></th>
                                    </tr>
                                </thead>
                                <tbody id="sec10ExemptionsPanel"></tbody>
                            </table>
                        </div>
                        <div class="col-xs-6" style="padding: 0px;margin-top: 30px;border-left:1px solid black;border-top:1px solid black;">
                            <table class="table table-striped">
                                <thead>
                                    <tr class="trClass" style="background:lightgray !important; border-bottom: 3px solid #7d7d7d;height:61px">
                                        <th style="width: 50%;"><h4 class="h4style">Form16 Summary</h4></th>
                                        <th class="text-right" ><h4 id="form16SummaryAmt" class="h4style">Amount<h4></th>
                                    </tr>
                                </thead>
                                <tbody id="form16SummaryTab">
                                    <tr class="trClass" style="height:120px"></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        `;
        blockManager.add("Payslip-Design-2", {
            label: 'Payslip Sample 2',
            content: payslipSample2Content + Common_Style
        });
        // payslip3 design block
        let payslipSample3Content = `<div class="print-container clearfix" id="payslip3">
            <h3 style="text-align:center;background-color:#2d530d !important;color: #fff;font-weight:600;margin-bottom: 0px"
                id="formHeader">Company
                Name</h3>
            <div style="border: 1px solid black">
                <table style="width:100%;">
                    <tr style="width:100%" class="heading trClass">
                        <td style="width:30%">
                            <div id="payslipLogo" style="padding:5px">
                                <img alt="logo" src="${image_path}" style="width:200px"/>
                            </div>
                        </td>
                        <td style="width:40%">
                            <h3 style="text-align:center;font-weight:600;" id="payMonth">Title</h3>
                        </td>
                        <td style="width:40%">
                            <div class="invoice-from" style="max-width:400px">
                                <div style="${$('#displayPayslipAddress').val() == 0 ? 'display:none' : null}" id="payslipTemplateAddress">
                                    <div style="text-align:right">
                                        <p id="payslipStreet1">Street1,</p>
                                        <p id="payslipStreet2">Street2,</p>
                                    </div>
                                    <div style="text-align:right">
                                        <p id="payslipCity">City,</p>
                                        <p id="payslipState">State,</p>
                                    </div>
                                    <div style="text-align:right">
                                        <p id="payslipCountry">Country,</p>
                                        <p id="payslipPinCode">Pin</p>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="col-xs-12" style="border:1px solid black;padding: 0px;border-top: none"
                data-gjs-custom-name="Personal-form">
                <div class="col-xs-6" style="padding: 0px;">
                    <table class="table table-bordered" style="margin-bottom:0px">
                        <tbody>
                            <tr class="trClass">
                                <td style="width:50%;background:#85b96e !important;color: #000;">
                                    <p> Employee Name</p>
                                </td>
                                <td class="emp_name_work_break_cls">
                                    <p data-gjs-editable="false" id="viewMonthlyEmployeeName">Name</p>
                                </td>
                            </tr>
                            <tr class="trClass">
                                <td style="width:50%;background:#85b96e !important;color: #000;">
                                    <p> Department</p>
                                </td>
                                <td>
                                    <p data-gjs-editable="false" id="viewMonthlyDepartment">Dept</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-xs-6" style="padding: 0px;">
                    <table class="table table-bordered" style="margin-bottom:0px">
                        <tbody>
                            <tr class="trClass">
                                <td style="width:50%;background:#85b96e !important;color: #000;">
                                    <p> Employee Id</p>
                                </td>
                                <td>
                                    <p data-gjs-editable="false" id="viewMonthlyEmployeeId">Id</p>
                                </td>
                            </tr>
                            <tr class="trClass">
                                <td style="width:50%;background:#85b96e !important;color: #000;">
                                    <p> Designation</p>
                                </td>
                                <td>
                                    <p data-gjs-editable="false" id="viewMonthlyDesignation">Designation</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-xs-6" style="padding: 0px;">
                    <table class="table table-bordered" style="margin-bottom:0px">
                        <tbody>
                            <tr class="trClass">
                                <td style="width:50%;background:#85b96e !important;color: #000;">
                                    <p>Date Of Join</p>
                                </td>
                                <td>
                                    <p data-gjs-editable="false" id="viewMonthlyDateOfJoin">dd/mm/yyyy</p>
                                </td>
                            </tr>
                            <tr class="trClass">
                                <td style="width:50%;background:#85b96e !important;color: #000;">
                                    <p> PF Number</p>
                                </td>
                                <td>
                                    <p data-gjs-editable="false" id="viewMonthlyPFAccountNumber">PF</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-xs-6" style="padding: 0px;">
                    <table class="table table-bordered" style="margin-bottom:0px">
                        <tbody>
                            <tr class="trClass">
                                <td style="width:50%;background:#85b96e !important;color: #000;">
                                    <p> ESI/Insurance No.</p>
                                </td>
                                <td>
                                    <p data-gjs-editable="false" id="viewMonthlyESI">ESI No</p>
                                </td>
                            </tr>
                            <tr class="trClass">
                                <td style="width:50%;background:#85b96e !important;color: #000;">
                                    <p> PF UAN No.</p>
                                </td>
                                <td>
                                    <p data-gjs-editable="false" id="viewMonthlUAN">UAN</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-xs-6" style="padding: 0px;">
                    <table class="table table-bordered" style="margin-bottom:0px">
                        <tbody>
                            <tr class="trClass">
                                <td style="width:50%;background:#85b96e !important;color: #000;">
                                    <p> PAN Number</p>
                                </td>
                                <td>
                                    <p data-gjs-editable="false" id="viewMonthlyPANNo">PAN</p>
                                </td>
                            </tr>
                            <tr class="trClass">
                                <td style="width:50%;background:#85b96e !important;color: #000;">
                                    <p> Bank Account No. </p>
                                </td>
                                <td>
                                    <p data-gjs-editable="false" id="viewMonthlyBankAccountNo">Acc No.</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-xs-6" style="padding: 0px;">
                    <table class="table table-bordered" style="margin-bottom:0px">
                        <tbody>
                            <tr class="trClass">
                                <td style="width:50%;background:#85b96e !important;color: #000;">
                                    <p> Bank Name</p>
                                </td>
                                <td>
                                    <p data-gjs-editable="false" id="viewMonthlyBankName">Bank</p>
                                </td>
                            </tr>
                            <tr class="trClass">
                                <td style="width:50%;background:#85b96e !important;color: #000;">
                                    <p> Paid Leave</p>
                                </td>
                                <td>
                                    <p data-gjs-editable="false" id=" viewMonthlyPaidLeave">0</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-xs-6" style="padding: 0px;">
                    <table class="table table-bordered" style="margin-bottom:0px">
                        <tbody>
                            <tr class="trClass">
                                <td style="width:50%;background:#85b96e !important;color: #000;">
                                    <p id="viewMonthlyUnpaidLeaveLabel">Unpaid Leave</p>
                                </td>
                                <td>
                                    <p data-gjs-editable="false" id="viewMonthlyUnpaidLeave">0</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                `;
                payslipSample3Content += $("#considerCutoffDaysForAttendanceAndTimeoff").val() === "Yes"  ?
                `<div class="col-xs-6" style="padding: 0px;" id="viewMonthlyPrevMonthUnpaidLeaveElement">
                    <table class="table table-bordered" style="margin-bottom:0px">
                        <tbody>
                            <tr class="trClass">
                                <td style="width:50%;background:#85b96e !important;color: #000;">
                                    <p id="viewMonthlyPrevMonthUnpaidLeaveLabel">Previous Month Unpaid Leave</p>
                                </td>
                                <td>
                                    <p data-gjs-editable="false" id="viewMonthlyPrevMonthUnpaidLeave">0</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>` : "";
                payslipSample3Content += `<div class="col-xs-6" style="padding: 0px;">
                    <table class="table table-bordered" style="margin-bottom:0px">
                        <tbody>
                            <tr class="trClass">
                                <td style="width:50%;background:#85b96e !important;color: #000;">
                                    <p>On Duty</p>
                                </td>
                                <td>
                                    <p data-gjs-editable="false" id="viewMonthlyOnDuty">0</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-xs-6" style="padding: 0px;">
                    <table class="table table-bordered" style="margin-bottom:0px">
                        <tbody>
                            <tr class="trClass">
                                <td style="width:50%;background:#85b96e !important;color: #000;">
                                    <p>Days Worked</p>
                                </td>
                                <td>
                                    <p data-gjs-editable="false" id="viewMonthlyDaysWorked">0</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-xs-6" style="padding: 0px;">
                    <table class="table table-bordered" style="margin-bottom:0px">
                        <tbody>
                            <tr class="trClass">
                                <td style="width:50%;background:#85b96e !important;color: #000;">
                                    <p>Paid Days</p>
                                </td>
                                <td>
                                    <p data-gjs-editable="false" id="viewMonthlyPaidDays">0</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-xs-6" style="padding: 0px;">
                    <table class="table table-bordered" style="margin-bottom:0px">
                        <tbody>
                            <tr class="trClass">
                                <td style="width:50%;background:#85b96e !important;color: #000;">
                                    <p>Date Of Relieving</p>
                                </td>
                                <td>
                                    <p data-gjs-editable="false" id="viewMonthlyDateOfRelieving">dd/mm/yyyy</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div style="${payslipType == "Monthly" ? 'display:none' : 'display:block' }">
                    <div class="col-xs-6" style="padding: 0px;">
                        <table class="table table-bordered" style="margin-bottom:0px">
                            <tbody>
                                <tr class="trClass">
                                    <td style="width:50%;background:#85b96e !important;color: #000;">
                                        <p>Hours Worked</p>
                                    </td>
                                    <td>
                                        <p data-gjs-editable="false" id="viewHoursWorked">0</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="col-xs-6" style="padding: 0px;">
                        <table class="table table-bordered" style="margin-bottom:0px">
                            <tbody>
                                <tr class="trClass">
                                    <td style="width:50%;background:#85b96e !important;color: #000;">
                                        <p>Day Wages</p>
                                    </td>
                                    <td>
                                        <p data-gjs-editable="false" id="viewDayWages">0</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="col-xs-6" style="padding: 0px;">
                        <table class="table table-bordered" style="margin-bottom:0px">
                            <tbody>
                                <tr class="trClass">
                                    <td style="width:50%;background:#85b96e !important;color: #000;">
                                        <p>OT Hours</p>
                                    </td>
                                    <td>
                                        <p data-gjs-editable="false" id="viewOTHours">0</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="col-xs-6" style="padding: 0px;">
                        <table class="table table-bordered" style="margin-bottom:0px">
                            <tbody>
                                <tr class="trClass">
                                    <td style="width:50%;background:#85b96e !important;color: #000;">
                                        <p>Holiday</p>
                                    </td>
                                    <td>
                                        <p data-gjs-editable="false" id="viewHoliday">0</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="col-xs-12"></div>
            </div>
            <div class="col-xs-12" style="padding: 0px;margin-top: 20px;border:1px solid black;">
                <div class="col-xs-12" style="padding: 0px;">
                    <table class="table" style="width: 100%;">
                        <thead style="background:#85b96e !important">
                            <tr class="trClass">
                                <th style="width: 25%; color: #000;"><h4 class="h4style">Earnings</h4></th>
                                <th style="width: 25%; color: #000; text-align: right; border-right: 1px solid black;"><h4 class="h4style" id="earnAmt">Amount</h4></th>
                                <th style="width: 25%; color: #000;"><h4 class="h4style">Deductions</h4></th>
                                <th style="width: 25%; color: #000; text-align: right;"><h4 class="h4style" id="deductAmt">Amount</h4></th>
                            </tr>
                        </thead>
                        <tbody id="earningsDeductionsTab">
                            <!-- Dynamic content will be populated here by JavaScript -->
                        </tbody>
                        <tfoot id="earningsDeductionsTabFooter">
                            <tr class="child" style="font-weight:bold;border-top:1px solid;min-height:49px;color: #000;">
                                <td class="text-left" style="color: #000; vertical-align: top; padding: 8px;"><p style="color: #000; margin: 0;">Total Earnings</p></td>
                                <td style="text-align: right; border-right: 1px solid black; color: #000; vertical-align: top; padding: 8px;"><p id="totalEarningsAmount" style="color: #000; margin: 0; text-align: right;"></p></td>
                                <td class="text-left" style="color: #000; vertical-align: top; padding: 8px;"><p style="color: #000; margin: 0;">Total Deductions</p></td>
                                <td style="text-align: right; color: #000; vertical-align: top; padding: 8px;"><p id="totalDeductionsAmount" style="color: #000; margin: 0; text-align: right;"></p></td>
                            </tr>
                            <tr class="child" style="font-weight:bold;border-top:1px solid;min-height:49px;color: #000;">
                                <td class="text-left" style="color: #000; vertical-align: top; padding: 8px;"><p style="color: #000; margin: 0;">Outstanding Amount</p></td>
                                <td style="text-align: right; border-right: 1px solid black; color: #000; vertical-align: top; padding: 8px;"><p id="totalOutstandingAmount" style="color: #000; margin: 0; text-align: right;"></p></td>
                                <td class="text-left" style="color: #000; vertical-align: top; padding: 8px;"><p style="color: #000; margin: 0;">Net Pay</p></td>
                                <td style="text-align: right; color: #000; vertical-align: top; padding: 8px;"><p id="totalNetPay" style="color: #000; margin: 0; text-align: right;"></p></td>
                            </tr>
                            <tr class="child totalIntermediatePaymentPanel" style="font-weight:bold;border-top:1px solid;min-height:49px;color: #000;">
                                <td class="text-left" style="color: #000; vertical-align: top; padding: 8px;"><p style="color: #000; margin: 0;">Total Intermediate Payment</p></td>
                                <td style="text-align: right; border-right: 1px solid black; color: #000; vertical-align: top; padding: 8px;"><p id="totalIntermediatePayment" style="color: #000; margin: 0; text-align: right;"></p></td>
                                <td class="text-left" style="color: #000; vertical-align: top; padding: 8px;"><p style="color: #000; margin: 0;"></p></td>
                                <td style="text-align: right; color: #000; vertical-align: top; padding: 8px;"><p style="color: #000; margin: 0;"></p></td>
                            </tr>
                        </tfoot>
                    </table>

                    <!-- Hidden containers to maintain backward compatibility with existing JavaScript -->
                    <div style="display: none;">
                        <div id="earningsTab"></div>
                        <div id="deductTab"></div>
                        <div id="earningsTabFooter"></div>
                        <div id="deductionsTabFooter"></div>
                    </div>
                </div>
                <div class="col-xs-12" style="border-top:1px solid black;border-bottom:1px solid black;padding: 0px;">
                    <div style="display: inline-block;color: #000;font-size: 16px;padding: 8px;" >
                        <b>Netpay In Words </b>
                        <div id="netpayInWord" style="display: inline;"></div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 payslip-template-3-table" id="empContribution">
                <h4 class="h4style" style="text-align:left;height:40px;border-bottom:1px solid black;padding-top: 10px;background:#85b96e !important;color: #000;padding-left: 10px">Organization Contribution</h4>
                <div class="col-xs-6" style="padding: 0px;">
                    <table class="table">
                        <tbody id="contributionTab1"></tbody>
                    </table>
                </div>
                <div class="col-xs-6" style="padding: 0px;border-left:1px solid black">
                    <table class="table">
                        <tbody id="contributionTab2"></tbody>
                    </table>
                </div>
            </div>
            <div class="col-xs-12 payslip-template-3-table" style="${payslipType == "Hourly" ? 'display:none' : 'display:block' }" id="empForm16Table">
                <div class="col-xs-6" style="padding: 0px;">
                    <table class="table">
                        <thead style="background:#85b96e !important">
                            <tr class="trClass" style="height:61px">
                                <th style="color: #000;;width: 77%;">
                                    <h4 class="h4style">Perks/ Other Income/ Exemptions/Rebate</h4>
                                </th>
                                <th style="color: #000;" class="text-right">
                                    <h4 id="exemptionsAmt" class="h4style">Amount</h4>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="exemptionsPanel"></tbody>
                    </table>
                    <table class="table">
                        <thead style="background:#85b96e !important">
                            <tr class="trClass">
                                <th style="color: #000;;width: 50%;">
                                    <h4 class="h4style">Sec 10 Exemption</h4>
                                </th>
                                <th style="color: #000;" class="text-right">
                                    <h4 id="sectionsAmt" class="h4style">Amount</h4>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="sec10ExemptionsPanel"></tbody>
                    </table>
                </div>
                <div class="col-xs-6" style="padding: 0px;border-left:1px solid black">
                    <table class="table">
                        <thead style="background:#85b96e !important">
                            <tr class="trClass" style="height:61px">
                                <th style="color: #000;;width: 50%;">
                                    <h4 class="h4style">Form16 Summary</h4>
                                </th>
                                <th style="color: #000;" class="text-right">
                                    <h4 id="form16SummaryAmt" class="h4style">Amount</h4>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="form16SummaryTab">
                            <tr class="trClass" style="height:120px"></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        `;
        blockManager.add("Payslip-Design-3", {
            label: 'Payslip Sample 3',
            content: payslipSample3Content + Common_Style
        });
        // payslip4 design block
        let payslipSample4Content = `<div class="print-container clearfix" id="payslip4">
            <h3 style="text-align:center;font-weight:600;" id="payMonth">Title</h3>
            <div class="header-payslip2">
                <div class="content">
                    <h3 style="text-align:center;background-color:rgb(0, 172, 193) !important;color: #FFF;font-weight:600;" id="formHeader">Company Name</h3>
                    <table style="width:100%">
                        <tr style="width:100%" class="heading trClass">
                            <td style="width:50%">
                             <div id="payslipLogo">
                                <img alt="logo" src="${image_path}" style="width:200px"/>
                            </div>
                            </td>
                            <td style="width:50%" class="text-right">
                                <div class="invoice-from" style="max-width:400px">
                                    <div style="${$('#displayPayslipAddress').val() == 0 ? 'display:none' : null}" id="payslipTemplateAddress">
                                        <div style="text-align:right">
                                            <p id="payslipStreet1">Street1,</p>
                                            <p id="payslipStreet2">Street2,</p>
                                        </div>
                                        <div style="text-align:right">
                                            <p id="payslipCity">City,</p>
                                            <p id="payslipState">State,</p>
                                        </div>
                                        <div style="text-align:right">
                                            <p id="payslipCountry">Country,</p>
                                            <p id="payslipPinCode">Pin</p>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>     
            <h3 style="text-align:center;color: #FFF;font-weight:600;background-color:rgb(0, 172, 193) !important" id="personalDetails">Personal
                Details</h3>
            <div class="body">
                <div class="summary-info">
                    <div class="row" style="margin-top:30px" data-gjs-custom-name="Personal-form">
                       <div class="col-xs-3">
                            <p class="template-field-label"> Employee Name</p>
                            <p class="emp_name_work_break_cls" data-gjs-editable="false" id="viewMonthlyEmployeeName">Name</p>
                        </div>
                       <div class="col-xs-3">
                            <p class="template-field-label"> Employee Id</p>
                            <p data-gjs-editable="false" id="viewMonthlyEmployeeId">Id</p>
                        </div>
                       <div class="col-xs-3">
                            <p class="template-field-label"> Department</p>
                            <p data-gjs-editable="false" id="viewMonthlyDepartment">Dept</p>
                        </div>
                       <div class="col-xs-3">
                            <p class=" template-field-label"> Designation</p>
                            <p data-gjs-editable="false" id="viewMonthlyDesignation">Designation</p>
                        </div>
                        <div class="col-xs-3">
                            <p class=" template-field-label">Date Of Join</p>
                            <p data-gjs-editable="false" id="viewMonthlyDateOfJoin">dd/mm/yyyy</p>
                        </div>
                       <div class="col-xs-3">
                            <p class=" template-field-label"> ESI/Insurance No</p>
                            <p data-gjs-editable="false" id="viewMonthlyESI">ESI</p>
                        </div>
                       <div class="col-xs-3">
                            <p class=" template-field-label"> PF Number</p>
                            <p data-gjs-editable="false" id="viewMonthlyPFAccountNumber">PF</p>
                        </div>
                       <div class="col-xs-3">
                            <p class=" template-field-label"> PF UAN No </p>
                            <p data-gjs-editable="false" id="viewMonthlUAN">UAN</p>
                        </div>
                       <div class="col-xs-3">
                            <p class=" template-field-label"> PAN Number</p>
                            <p data-gjs-editable="false" id="viewMonthlyPANNo">PAN</p>
                        </div>
                        <div class="col-xs-3">
                             <p class=" template-field-label"> Aadhaar Number</p>
                             <p data-gjs-editable="false" id="viewMonthlyAadhaarNo">Aadhaar</p>
                         </div>
                       <div class="col-xs-3">
                            <p class=" template-field-label"> Bank Name</p>
                            <p data-gjs-editable="false" id="viewMonthlyBankName">Bank</p>
                        </div>
                       <div class="col-xs-3">
                            <p class=" template-field-label"> Bank Account No</p>                        
                            <p data-gjs-editable="false" id="viewMonthlyBankAccountNo">Acc No</p>
                        </div>
                       <div class="col-xs-3">
                            <p class=" template-field-label"> Paid Leave</p>                          
                            <p data-gjs-editable="false" id="viewMonthlyPaidLeave">0</p>
                        </div>
                       <div class="col-xs-3">
                            <p class=" template-field-label" id="viewMonthlyUnpaidLeaveLabel"> Unpaid Leave</p>                          
                            <p data-gjs-editable="false" id="viewMonthlyUnpaidLeave">0</p>
                        </div>
                        `;
                        payslipSample4Content += $("#considerCutoffDaysForAttendanceAndTimeoff").val() === "Yes"  ?
                        `<div class="col-xs-3" id="viewMonthlyPrevMonthUnpaidLeaveElement">
                             <p class=" template-field-label" id="viewMonthlyPrevMonthUnpaidLeaveLabel"> Previous Month Unpaid Leave</p>                          
                             <p data-gjs-editable="false" id="viewMonthlyPrevMonthUnpaidLeave">0</p>
                         </div>` : '';
                         payslipSample4Content += `<div class="col-xs-3">
                            <p class=" template-field-label"> Days Worked</p>                          
                            <p data-gjs-editable="false" id="viewMonthlyDaysWorked">0</p>
                        </div>
                        <div class="col-xs-3">
                            <p class=" template-field-label"> Paid Days</p>                          
                            <p data-gjs-editable="false" id="viewMonthlyPaidDays">0</p>
                        </div>
                        <div class="col-xs-3">
                            <p class=" template-field-label"> Date Of Relieving</p>                          
                            <p data-gjs-editable="false" id="viewMonthlyDateOfRelieving">dd/mm/yyyy</p>
                        </div>
                        <div style="${payslipType == "Monthly" ? 'display:none' : 'display:block' }">
                            <div class="col-xs-3">
                                <p class=" template-field-label"> Hours Worked</p>                          
                                <p data-gjs-editable="false" id="viewHoursWorked">0</p>
                            </div>
                            <div class="col-xs-3">
                                <p class=" template-field-label"> Day Wages </p>                          
                                <p data-gjs-editable="false" id="viewDayWages">0</p>
                            </div>
                            <div class="col-xs-3">
                                <p class=" template-field-label"> OT Hours </p>                          
                                <p data-gjs-editable="false" id="viewOTHours">0</p>
                            </div>
                            <div class="col-xs-3">
                                <p class=" template-field-label"> Holiday </p>                          
                                <p data-gjs-editable="false" id="viewHoliday">0</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xs-12" style="padding: 0px; margin: 0;">
                    <div class="col-xs-12" style="padding: 0px; margin: 0;">
                        <style>
                            #payslip4 table, #payslip4 table * {
                                margin: 0 !important;
                                border: none !important;
                            }
                            #payslip4 table td, #payslip4 table th {
                                padding: 4px !important;
                            }
                            #payslip4 table thead th {
                                padding: 8px 6px !important;
                            }
                            #payslip4 table thead h4 {
                                margin: 0 !important;
                                padding: 0 !important;
                                font-size: 18px !important;
                                font-weight: 700 !important;
                                line-height: 1.2 !important;
                                color: #FFF !important;
                            }
                            #payslip4 table p {
                                margin: 0 !important;
                                padding: 0 !important;
                                font-size: 14px !important;
                                line-height: 1.4 !important;
                            }
                            #payslip4 table tbody td p {
                                font-size: 14px !important;
                            }
                            #payslip4 table tfoot td p {
                                font-size: 14px !important;
                                font-weight: 600 !important;
                            }
                        </style>
                        <table style="width: 100%; margin: 0 !important; padding: 0 !important; border-collapse: collapse; border-spacing: 0; border: none;">
                            <thead style="background-color:rgb(0, 172, 193) !important; margin: 0 !important; padding: 0 !important;">
                                <tr style="margin: 0 !important; padding: 0 !important; border: none !important;">
                                    <th style="width: 25%; color: #FFF; margin: 0 !important; padding: 8px 6px !important; border: none !important;"><h4 style="margin: 0 !important; padding: 0 !important; font-size: 18px; font-weight: 700;">Earnings</h4></th>
                                    <th style="width: 25%; color: #FFF; text-align: right; margin: 0 !important; padding: 8px 6px !important; border: none !important;"><h4 style="margin: 0 !important; padding: 0 !important; font-size: 18px; font-weight: 700;" id="earnAmt">Amount</h4></th>
                                    <th style="width: 25%; color: #FFF; margin: 0 !important; padding: 8px 6px !important; border: none !important;"><h4 style="margin: 0 !important; padding: 0 !important; font-size: 18px; font-weight: 700;">Deductions</h4></th>
                                    <th style="width: 25%; color: #FFF; text-align: right; margin: 0 !important; padding: 8px 6px !important; border: none !important;"><h4 style="margin: 0 !important; padding: 0 !important; font-size: 18px; font-weight: 700;" id="deductAmt">Amount</h4></th>
                                </tr>
                            </thead>
                            <tbody id="earningsDeductionsTab" style="margin: 0 !important; padding: 0 !important;">
                                <!-- Dynamic content will be populated here by JavaScript -->
                                <!-- Note: All dynamic rows must use style="margin: 0 !important; padding: 4px !important; border: none !important;" -->
                                <!-- Note: All dynamic content paragraphs must use style="margin: 0 !important; padding: 0 !important; font-size: 14px !important;" -->
                            </tbody>
                            <tfoot id="earningsDeductionsTabFooter" style="margin: 0 !important; padding: 0 !important;">
                                <tr style="font-weight:bold; border:none !important; margin: 0 !important; padding: 0 !important; height: 35px; color: #000;">
                                    <td style="color: #000; vertical-align: top; margin: 0 !important; padding: 4px !important; border: none !important;"><p style="color: #000; margin: 0 !important; padding: 0 !important; font-size: 14px !important; font-weight: 600 !important; line-height: 1.4;">Total Earnings</p></td>
                                    <td style="text-align: right; color: #000; vertical-align: top; margin: 0 !important; padding: 4px !important; border: none !important;"><p id="totalEarningsAmount" style="color: #000; margin: 0 !important; padding: 0 !important; text-align: right; font-size: 14px !important; font-weight: 600 !important; line-height: 1.4;"></p></td>
                                    <td style="color: #000; vertical-align: top; margin: 0 !important; padding: 4px !important; border: none !important;"><p style="color: #000; margin: 0 !important; padding: 0 !important; font-size: 14px !important; font-weight: 600 !important; line-height: 1.4;">Total Deductions</p></td>
                                    <td style="text-align: right; color: #000; vertical-align: top; margin: 0 !important; padding: 4px !important; border: none !important;"><p id="totalDeductionsAmount" style="color: #000; margin: 0 !important; padding: 0 !important; text-align: right; font-size: 14px !important; font-weight: 600 !important; line-height: 1.4;"></p></td>
                                </tr>
                                <tr style="font-weight:bold; border:none !important; margin: 0 !important; padding: 0 !important; height: 35px; color: #000;">
                                    <td style="color: #000; vertical-align: top; margin: 0 !important; padding: 4px !important; border: none !important;"><p style="color: #000; margin: 0 !important; padding: 0 !important; font-size: 14px !important; font-weight: 600 !important; line-height: 1.4;">Outstanding Amount</p></td>
                                    <td style="text-align: right; color: #000; vertical-align: top; margin: 0 !important; padding: 4px !important; border: none !important;"><p id="totalOutstandingAmount" style="color: #000; margin: 0 !important; padding: 0 !important; text-align: right; font-size: 14px !important; font-weight: 600 !important; line-height: 1.4;"></p></td>
                                    <td style="color: #000; vertical-align: top; margin: 0 !important; padding: 4px !important; border: none !important;"><p style="color: #000; margin: 0 !important; padding: 0 !important; font-size: 14px !important; font-weight: 600 !important; line-height: 1.4;">Net Pay</p></td>
                                    <td style="text-align: right; color: #000; vertical-align: top; margin: 0 !important; padding: 4px !important; border: none !important;"><p id="totalNetPay" style="color: #000; margin: 0 !important; padding: 0 !important; text-align: right; font-size: 14px !important; font-weight: 600 !important; line-height: 1.4;"></p></td>
                                </tr>
                                <tr class="totalIntermediatePaymentPanel" style="font-weight:bold; border:none !important; margin: 0 !important; padding: 0 !important; height: 35px; color: #000;">
                                    <td style="color: #000; vertical-align: top; margin: 0 !important; padding: 4px !important; border: none !important;"><p style="color: #000; margin: 0 !important; padding: 0 !important; font-size: 14px !important; font-weight: 600 !important; line-height: 1.4;">Total Intermediate Payment</p></td>
                                    <td style="text-align: right; color: #000; vertical-align: top; margin: 0 !important; padding: 4px !important; border: none !important;"><p id="totalIntermediatePayment" style="color: #000; margin: 0 !important; padding: 0 !important; text-align: right; font-size: 14px !important; font-weight: 600 !important; line-height: 1.4;"></p></td>
                                    <td style="color: #000; vertical-align: top; margin: 0 !important; padding: 4px !important; border: none !important;"><p style="color: #000; margin: 0 !important; padding: 0 !important; font-size: 14px !important; font-weight: 600 !important; line-height: 1.4;"></p></td>
                                    <td style="text-align: right; color: #000; vertical-align: top; margin: 0 !important; padding: 4px !important; border: none !important;"><p style="color: #000; margin: 0 !important; padding: 0 !important; text-align: right; font-size: 14px !important; font-weight: 600 !important; line-height: 1.4;"></p></td>
                                </tr>
                            </tfoot>
                        </table>

                        <!-- Hidden containers to maintain backward compatibility with existing JavaScript -->
                        <div style="display: none;">
                            <div id="earningsTab"></div>
                            <div id="deductTab"></div>
                            <div id="earningsTabFooter"></div>
                            <div id="deductionsTabFooter"></div>
                        </div>
                    </div>
                    <div class="col-xs-12" style="padding: 0px;">
                        <div style="display: inline-block;color: #000;font-size: 16px;padding: 8px;" >
                            <b>Netpay In Words </b>
                            <div id="netpayInWord" style="display: inline;"></div>
                        </div>
                    </div>
                </div>
                <div class="col-xs-12 paddingZeroTemplate" id="empContribution">
                     <h4 class="h4style" style="text-align:left;height:40px;padding-top: 10px;background:rgb(0, 172, 193) !important;color:#FFF;padding-left: 10px">Organization Contribution</h4>
                    <div class="col-xs-6" style="padding: 0px;">
                        <table class="table table-striped">
                            <tbody id="contributionTab1"></tbody>
                        </table>
                    </div>
                    <div class="col-xs-6" style="padding: 0px;">
                        <table class="table table-striped">
                            <tbody id="contributionTab2"></tbody>
                        </table>
                    </div>
                </div>
                <div class="col-xs-12 paddingZeroTemplate" style="${payslipType == "Hourly" ? 'display:none' : 'display:block' }" id="empForm16Table">
                    <div class="col-xs-6" style="padding: 0px;">
                        <table class="table table-striped">
                            <thead style="background-color:rgb(0, 172, 193) !important">
                                <tr class="trClass" style="height:61px">
                                    <th style="color:#FFF;width: 77%;">
                                        <h4 class="h4style">Perks/ Other Income/ Exemptions/Rebate</h4>
                                    </th>
                                    <th style="color: #FFF;" class="text-right">
                                        <h4 id="exemptionsAmt" class="h4style">Amount</h4>
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="exemptionsPanel"></tbody>
                        </table>
                        <table class="table  table-striped">
                            <thead style="background-color:rgb(0, 172, 193) !important">
                                <tr class="trClass" >
                                    <th style="color:#FFF;width: 50%;">
                                        <h4 class="h4style">Sec 10 Exemption</h4>
                                    </th>
                                    <th style="color: #FFF;" class="text-right">
                                        <h4 id="sectionsAmt" class="h4style">Amount</h4>
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="sec10ExemptionsPanel"></tbody>
                        </table>
                    </div>
                    <div class="col-xs-6" style="padding: 0px;">
                        <table class="table table-striped">
                            <thead style="background-color:rgb(0, 172, 193) !important">
                                <tr class="trClass" style="height:61px">
                                    <th style="color:#FFF;width: 50%;">
                                        <h4 class="h4style">Form16 Summary</h4>
                                    </th>
                                    <th style="color: #FFF;" class="text-right">
                                        <h4 id="form16SummaryAmt" class="h4style">Amount</h4>
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="form16SummaryTab">
                               <tr class="trClass" style="height:120px"></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <hr style="background:2px solid #e4e4e4">
            </div>
        </div>
        `;
        blockManager.add("Payslip-Design-4", {
            label: 'Payslip Sample 4',
            content: payslipSample4Content + Common_Style
        });
        // payslipGhana design block
        let payslipSampleGhanaContent = `
            <div id="payslipGhana" class="print-container clearfix">
                <div class="template-header">
                    <div class="text-center">
                    </div>
                </div>
                <br/>
                <div data-highlightable="1" class="row-column1">
                    <div data-highlightable="1" class="row-cell1" style="text-align:center;margin-bottom:16px;">
                    <h3 id="formHeader">Company Name
                    </h3>
                    <h4 id="payMonth">Title
                    </h4>
                    <div style="${$('#displayPayslipAddress').val() == 0 ? 'display:none' : 'display:inline;position:static;float:none;'}" id="payslipTemplateAddress">
                        <p id="payslipStreet1">Street1,
                        </p>
                        <p id="payslipStreet2">Street2,
                        </p>
                        <p id="payslipCity">City,
                        </p>
                        <p id="payslipState">State,
                        </p>
                        <p id="payslipCountry">Country,
                        </p>
                        <p id="payslipPinCode">Pincode,
                        </p>
                    </div>
                    </div>
                </div>
                <div class="body">
                    <div style="margin:0px;border: 1px solid rgb(51, 51, 51);padding-top:11px;" class="row">
                    <div style="padding-bottom:8px;" class="col-xs-6">
                        <div class="col-xs-6">
                        <p>
                            <b>Employee Name
                            </b>
                        </p>
                        </div>
                        <div class="col-xs-6">
                        <p id="viewMonthlyEmployeeName">Name
                        </p>
                        </div>
                    </div>
                    <div style="padding-bottom:8px;" class="col-xs-6">
                        <div class="col-xs-6">
                        <p>
                            <b>Employee Id
                            </b>
                        </p>
                        </div>
                        <div class="col-xs-6">
                        <p id="viewMonthlyEmployeeId">Id
                        </p>
                        </div>
                    </div>
                    <div style="padding-bottom:8px;" class="col-xs-6">
                        <div class="col-xs-6">
                        <p>
                            <b>Department
                            </b>
                        </p>
                        </div>
                        <div class="col-xs-6">
                        <p id="viewMonthlyDepartment">Dept
                        </p>
                        </div>
                    </div>
                    <div style="padding-bottom:8px;" class="col-xs-6">
                        <div class="col-xs-6">
                        <p>
                            <b>Designation
                            </b>
                        </p>
                        </div>
                        <div class="col-xs-6">
                        <p id="viewMonthlyDesignation">Designation
                        </p>
                        </div>
                    </div>
                    <div style="padding-bottom:8px;" class="col-xs-6">
                        <div class="col-xs-6">
                        <p>
                            <b>Date Of Join
                            </b>
                        </p>
                        </div>
                        <div class="col-xs-6">
                        <p id="viewMonthlyDateOfJoin">dd/mm/yyyy
                        </p>
                        </div>
                    </div>
                    <div style="padding-bottom:8px;" class="col-xs-6">
                        <div class="col-xs-6">
                        <p>
                            <b>ESI/Insurance No
                            </b>
                        </p>
                        </div>
                        <div class="col-xs-6">
                        <p id="viewMonthlyESI">ESI
                        </p>
                        </div>
                    </div>
                    <div style="padding-bottom:8px;" class="col-xs-6">
                        <div class="col-xs-6">
                        <p>
                            <b>PF Number
                            </b>
                        </p>
                        </div>
                        <div class="col-xs-6">
                        <p id="viewMonthlyPFAccountNumber">PF
                        </p>
                        </div>
                    </div>
                    <div style="padding-bottom:8px;" class="col-xs-6">
                        <div class="col-xs-6">
                        <p>
                            <b>PF UAN No
                            </b>
                        </p>
                        </div>
                        <div class="col-xs-6">
                        <p id="viewMonthlUAN">UAN
                        </p>
                        </div>
                    </div>
                    <div style="padding-bottom:8px;" class="col-xs-6">
                        <div class="col-xs-6">
                        <p>
                            <b>PAN Number
                            </b>
                        </p>
                        </div>
                        <div class="col-xs-6">
                        <p id="viewMonthlyPANNo">PAN
                        </p>
                        </div>
                    </div>
                    <div class="col-xs-6" style="padding-bottom:8px">
                        <div class="col-xs-6">
                            <p><b>Aadhaar Number</b></p>
                        </div>
                        <div class="col-xs-6">
                            <p data-gjs-editable="false" id="viewMonthlyAadhaarNo">Aadhaar</p>
                        </div>
                    </div>
                    <div style="padding-bottom:8px;" class="col-xs-6">
                        <div class="col-xs-6">
                        <p>
                            <b>Bank Name
                            </b>
                        </p>
                        </div>
                        <div class="col-xs-6">
                        <p id="viewMonthlyBankName">Bank
                        </p>
                        </div>
                    </div>
                    <div style="padding-bottom:8px;" class="col-xs-6">
                        <div class="col-xs-6">
                        <p>
                            <b>Bank Account No
                            </b>
                        </p>
                        </div>
                        <div class="col-xs-6">
                        <p id="viewMonthlyBankAccountNo">Acc No
                        </p>
                        </div>
                    </div>
                    <div style="padding-bottom:8px;" class="col-xs-6">
                        <div class="col-xs-6">
                        <p>
                            <b>Paid Leave
                            </b>
                        </p>
                        </div>
                        <div class="col-xs-6">
                        <p id="viewMonthlyPaidLeave">0
                        </p>
                        </div>
                    </div>
                    <div style="padding-bottom:8px;" class="col-xs-6">
                        <div class="col-xs-6">
                        <p>
                            <b id="viewMonthlyUnpaidLeaveLabel">Unpaid Leave
                            </b>
                        </p>
                        </div>
                        <div class="col-xs-6">
                        <p id="viewMonthlyUnpaidLeave">0
                        </p>
                        </div>
                    </div>
        `;
        payslipSampleGhanaContent += $("#considerCutoffDaysForAttendanceAndTimeoff").val() === "Yes"  ?
            `<div class="col-xs-6" style="padding-bottom:8px" id="viewMonthlyPrevMonthUnpaidLeaveElement">
                <div class="col-xs-6">
                    <p><b id="viewMonthlyPrevMonthUnpaidLeaveLabel">Previous Month Unpaid Leave</b></p>
                </div>
                <div class="col-xs-6">
                    <p data-gjs-editable="false" id="viewMonthlyPrevMonthUnpaidLeave">0</p>
                </div>
            </div>` : '';
        payslipSampleGhanaContent += `
            <div class="col-xs-6" style="padding-bottom:8px">
                <div class="col-xs-6">
                    <p><b>On Duty</b></p>
                </div>
                <div class="col-xs-6">
                    <p data-gjs-editable="false" id="viewMonthlyOnDuty">0</p>
                </div>
            </div>
            <div style="padding-bottom:8px;"class="col-xs-6">
                <div class="col-xs-6">
                <p>
                    <b>Days Worked
                    </b>
                </p>
                </div>
                <div class="col-xs-6">
                <p id="viewMonthlyDaysWorked">0
                </p>
                </div>
            </div>
            <div class="col-xs-6" style="padding-bottom:8px">
                <div class="col-xs-6">
                    <p><b>Paid Days</b></p>
                </div>
                <div class="col-xs-6">
                    <p data-gjs-editable="false" id="viewMonthlyPaidDays">0</p>
                </div>
            </div>
            <div style="padding-bottom:8px;" class="col-xs-6">
                <div class="col-xs-6">
                <p>
                    <b>Date Of Relieving
                    </b>
                </p>
                </div>
                <div class="col-xs-6">
                <p id="viewMonthlyDateOfRelieving">dd/mm/yyyy
                </p>
                </div>
            </div>
            <div style="${payslipType == "Monthly" ? 'display:none' : 'display:block' }">
                <div style="padding-bottom:8px;" class="col-xs-6">
                <div class="col-xs-6">
                    <p>
                    <b>Hours Worked
                    </b>
                    </p>
                </div>
                <div class="col-xs-6">
                    <p id="viewHoursWorked">0
                    </p>
                </div>
                </div>
                <div style="padding-bottom:8px;" class="col-xs-6">
                <div class="col-xs-6">
                    <p>
                    <b>Day Wages
                    </b>
                    </p>
                </div>
                <div class="col-xs-6">
                    <p id="viewDayWages">0
                    </p>
                </div>
                </div>
                <div style="padding-bottom:8px;" class="col-xs-6">
                <div class="col-xs-6">
                    <p>
                    <b>OT Hours
                    </b>
                    </p>
                </div>
                <div class="col-xs-6">
                    <p id="viewOTHours">0
                    </p>
                </div>
                </div>
                <div style="padding-bottom:8px;" class="col-xs-6">
                <div class="col-xs-6">
                    <p>
                    <b>Holiday
                    </b>
                    </p>
                </div>
                <div class="col-xs-6">
                    <p id="viewHoliday">0
                    </p>
                </div>
                </div>
            </div>
            </div>
            <br/>
            <div class="col-xs-12" style="border: 1px solid black;padding:0px">
            <div class="col-xs-12" style="padding: 0px;">
                <table class="table table-striped" style="width: 100%;">
                <thead>
                    <tr style="background:lightgray !important; border-bottom: 3px solid #7d7d7d;" class="trClass">
                    <th style="width: 25%;">
                        <h4 class="h4style">Earnings
                        </h4>
                    </th>
                    <th style="width: 25%; text-align: right; border-right: 1px solid black;">
                        <h4 id="earnAmt" class="h4style">Amount
                        </h4>
                    </th>
                    <th style="width: 25%;">
                        <h4 class="h4style">Deductions
                        </h4>
                    </th>
                    <th style="width: 25%; text-align: right;">
                        <h4 id="deductAmt" class="h4style">Amount
                        </h4>
                    </th>
                    </tr>
                </thead>
                <tbody id="earningsDeductionsTab">
                    <!-- Dynamic content will be populated here by JavaScript -->
                </tbody>
                <tfoot id="earningsDeductionsTabFooter">
                    <tr class="child" style="font-weight:bold;border-top:1px solid;min-height:49px;color: #000;">
                        <td class="text-left" style="color: #000; vertical-align: top; padding: 8px;"><p style="color: #000; margin: 0;">Total Earnings</p></td>
                        <td style="text-align: right; border-right: 1px solid black; color: #000; vertical-align: top; padding: 8px;"><p id="totalEarningsAmount" style="color: #000; margin: 0; text-align: right;"></p></td>
                        <td class="text-left" style="color: #000; vertical-align: top; padding: 8px;"><p style="color: #000; margin: 0;">Total Deductions</p></td>
                        <td style="text-align: right; color: #000; vertical-align: top; padding: 8px;"><p id="totalDeductionsAmount" style="color: #000; margin: 0; text-align: right;"></p></td>
                    </tr>
                    <tr class="child" style="font-weight:bold;border-top:1px solid;min-height:49px;color: #000;">
                        <td class="text-left" style="color: #000; vertical-align: top; padding: 8px;"><p style="color: #000; margin: 0;">Outstanding Amount</p></td>
                        <td style="text-align: right; border-right: 1px solid black; color: #000; vertical-align: top; padding: 8px;"><p id="totalOutstandingAmount" style="color: #000; margin: 0; text-align: right;"></p></td>
                        <td class="text-left" style="color: #000; vertical-align: top; padding: 8px;"><p style="color: #000; margin: 0;">Net Pay</p></td>
                        <td style="text-align: right; color: #000; vertical-align: top; padding: 8px;"><p id="totalNetPay" style="color: #000; margin: 0; text-align: right;"></p></td>
                    </tr>
                    <tr class="child totalIntermediatePaymentPanel" style="font-weight:bold;border-top:1px solid;min-height:49px;color: #000;">
                        <td class="text-left" style="color: #000; vertical-align: top; padding: 8px;"><p style="color: #000; margin: 0;">Total Intermediate Payment</p></td>
                        <td style="text-align: right; border-right: 1px solid black; color: #000; vertical-align: top; padding: 8px;"><p id="totalIntermediatePayment" style="color: #000; margin: 0; text-align: right;"></p></td>
                        <td class="text-left" style="color: #000; vertical-align: top; padding: 8px;"><p style="color: #000; margin: 0;"></p></td>
                        <td style="text-align: right; color: #000; vertical-align: top; padding: 8px;"><p style="color: #000; margin: 0;"></p></td>
                    </tr>
                </tfoot>
                </table>

                <!-- Hidden containers to maintain backward compatibility with existing JavaScript -->
                <div style="display: none;">
                    <div id="earningsTab"></div>
                    <div id="deductTab"></div>
                    <div id="earningsTabFooter"></div>
                    <div id="deductionsTabFooter"></div>
                </div>
            </div>
            <div style="border-top:1px solid black;border-bottom:1px solid black;padding: 0px;" class="col-xs-12">
                <div style="display: inline-block;color: #000;font-size: 16px;padding: 8px;">
                <b>Netpay In Words 
                </b>
                <div id="netpayInWord" style="display: inline;">
                </div>
                </div>
            </div>
            <div id="taxReliefDiv" style="border-bottom:1px solid black;padding: 0px;" class="col-xs-12">
                <div style="display: inline-block;color: #000;font-size: 16px;padding: 8px;">
                    <b id="viewMonthlyTaxReliefLabel">Tax Relief</b>
                    <div id="taxReliefInWord" style="display: inline;"></div>
                </div>
            </div>
            <div id="empContribution" class="col-xs-12 payslip-template-2-orgCon">
                <h4 style="text-align:left;height:40px;border-bottom:1px solid black;padding-top: 10px;background:lightgray !important;color: #000;padding-left: 10px" class="h4style">Employer Contribution
                </h4>
                <div style="padding: 0px;border-right:1px solid black;border-bottom:1px solid black" class="col-xs-6">
                <table style="margin-bottom:0px" class="table table-striped">
                    <tbody id="contributionTab1">
                    <tr class="row">
                        <td class="cell">
                        </td>
                    </tr>
                    </tbody>
                </table>
                </div>
                <div style="padding: 0px;border-bottom:1px solid black" class="col-xs-6">
                <table class="table table-striped">
                    <tbody id="contributionTab2">
                    <tr class="row">
                        <td class="cell">
                        </td>
                    </tr>
                    </tbody>
                </table>
                </div>
            </div>
            <div id="empForm16Table" class="col-xs-12 paddingZeroTemplate" style="${payslipType == "Hourly" ? 'display:none' : 'display:block' }">
                <div style="padding: 0px;margin-top: 30px;border-top:1px solid black" class="col-xs-6">
                <table class="table table-striped">
                    <thead>
                    <tr style="background:lightgray !important; border-bottom: 3px solid #7d7d7d;height:61px" class="trClass">
                        <th style="width: 77%;">
                        <h4 class="h4style">Perks/ Other Income/ Exemptions/
                            Rebate
                        </h4>
                        </th>
                        <th class="text-right">
                        <h4 id="exemptionsAmt" class="h4style">Amount
                        </h4>
                        </th>
                    </tr>
                    </thead>
                    <tbody id="exemptionsPanel">
                    <tr class="row">
                        <td class="cell">
                        </td>
                    </tr>
                    </tbody>
                </table>
                <table class="table table-striped">
                    <thead>
                    <tr style="background:lightgray !important; border-bottom: 3px solid #7d7d7d;height:61px" class="trClass">
                        <th style="width: 50%;">
                        <h4 class="h4style">Sec 10 Exemption
                        </h4>
                        </th>
                        <th class="text-right">
                        <h4 id="sectionsAmt" class="h4style">Amount
                        </h4>
                        </th>
                    </tr>
                    </thead>
                    <tbody id="sec10ExemptionsPanel">
                    <tr class="row">
                        <td class="cell">
                        </td>
                    </tr>
                    </tbody>
                </table>
                </div>
                <div style="padding: 0px;margin-top: 30px;border-left:1px solid black;border-top:1px solid black;" class="col-xs-6">
                <table class="table table-striped">
                    <thead>
                    <tr style="background:lightgray !important; border-bottom: 3px solid #7d7d7d;border-top:1px solid black;" class="trClass">
                        <th style="width: 50%;">
                        <h4 class="h4style">Form16 Summary
                        </h4>
                        </th>
                        <th class="text-right">
                        <h4 id="form16SummaryAmt" class="h4style">Amount
                        </h4>
                        <h4>
                        </h4>
                        </th>
                    </tr>
                    </thead>
                    <tbody id="form16SummaryTab">
                    <tr style="height:60px">
                    </tr>
                    <tr style="height:60px">
                    </tr>
                    <tr style="height:60px">
                    </tr>
                    <tr style="height:60px">
                    </tr>
                    <tr style="height:120px" class="trClass">
                    </tr>
                    </tbody>
                </table>
                </div>
            </div>
            </div>
            </div>
         </div>`;

        blockManager.add("Payslip-Design-Ghana", {
            label: 'Payslip Sample(Ghana)',
            content: payslipSampleGhanaContent + Common_Style
        });
        // Divider block
        blockManager.add("divider", {
            label: 'Divider',
            content: `<hr style="border:1px solid #807c7c">`
        });
        // column block
        blockManager.add('1-row-block', {
            label: '1 Column',
            content: `<div data-gjs-type="default" data-highlightable="1" class="row-column1">
            <div data-gjs-type="default" data-highlightable="1" class="row-cell1"></div>
            </div>
            <style>
                .row-column1 {
                    display: flex;
                    justify-content: flex-start;
                    align-items: stretch;
                    flex-wrap: nowrap;
                }
            
                .row-cell1 {
                    min-height: 75px;
                    flex-grow: 1;
                    flex-basis: 100%;
                }
            </style>`,
        })
        blockManager.add('2column', {
            label: '2 Columns',
            content: `<div data-gjs-type="default" data-highlightable="1" class="row-column2">
                <div data-gjs-type="default" data-highlightable="1" class="row-cell2"></div>
                <div data-gjs-type="default" data-highlightable="1" class="row-cell2"></div>
            </div>
            <style>
                .row-column2 {
                    display: flex;
                    justify-content: flex-start;
                    align-items: stretch;
                    flex-wrap: nowrap;
                }
            
                .row-cell2 {
                    min-height: 35px;
                    flex-grow: 1;
                    flex-basis: 100%;
                }
            </style>`
        });
        blockManager.add('3column', {
            label: '3 Columns',
            content: `<div data-gjs-type="default" data-highlightable="1" class="row-column3">
                <div data-gjs-type="default" data-highlightable="1" class="row-cell3"></div>
                <div data-gjs-type="default" data-highlightable="1" class="row-cell3"></div>
                <div data-gjs-type="default" data-highlightable="1" class="row-cell3"></div>
            </div>
            <style>
                .row-column3 {
                    display: flex;
                    justify-content: flex-start;
                    align-items: stretch;
                    flex-wrap: nowrap;
                }
            
                .row-cell3 {
                    min-height: 35px;
                    flex-grow: 1;
                    flex-basis: 100%;
                }
            </style>`
        });
        blockManager.add('4column', {
            label: '4 Columns',
            content: `<div data-gjs-type="default" data-highlightable="1" class="row-column4">
                <div data-gjs-type="default" data-highlightable="1" class="row-cell4"></div>
                <div data-gjs-type="default" data-highlightable="1" class="row-cell4"></div>
                <div data-gjs-type="default" data-highlightable="1" class="row-cell4"></div>
                <div data-gjs-type="default" data-highlightable="1" class="row-cell4"></div>
            </div>
            <style>
                .row-column4 {
                    display: flex;
                    justify-content: flex-start;
                    align-items: stretch;
                    flex-wrap: nowrap;
                }
            
                .row-cell4 {
                    min-height: 35px;
                    flex-grow: 1;
                    flex-basis: 100%;
                }
            </style>`
        });
        // grapesjs ends here

    }


    // click event for close button top of the grapes-js module
    $('#backToPayslipTemplateGrid').on('click', function (e) {
        e.preventDefault();
        e.stopImmediatePropagation();
        $('#modalDirtyPayslipTemplate').modal('toggle');
    })

    /** Set enable / disable toolbar icons **/
    function fnActionButtonsPayslipTemplate(action) {
        fnGridButtons($('#viewPayslipTemplate, #editPayslipTemplate, #deletePayslipTemplate'), action);
        // get selected records to check set as default flag
        var selectedRow = fnGetSelected(tablePayslipTemplate);
        var record = tablePayslipTemplate.fnGetData(selectedRow[0]);
        // check to display set as default button in grid
        if (record.Set_As_Default === '0' && action) {
            fnGridButtons($('#setAsDefaultTemplate'), true);
        } else {
            fnGridButtons($('#setAsDefaultTemplate'), false);
        }
    };

    /** Search OrganizationAccount Grid **/
    $('#tablePayslipTemplate').on('draw.dt', function () {
        fnActionButtonsPayslipTemplate(false);
    });

    function fnCloseTemplate(hideAction) {
        isDirtyFormPayslipTemplate = false;
        var payslipTemplateId = $('#formPayslipTemplateId').val();

        if (payslipTemplateId > 0 && !isNaN(payslipTemplateId)) {
            clearLock({
                'formName': 'Payslip Template',
                'uniqueId': payslipTemplateId,
                'callback': function () {
                    $('#formPayslipTemplateId').val(0);

                    if (hideAction)
                        $('#modalFormPayslipTemplate').modal('hide');
                }
            });
        }
        if (hideAction) {
            $('#modalFormPayslipTemplate').modal('hide');
            $('#gjs').hide();
            $("#grapesJsModule").get(0).style.setProperty('height', '0px');
            $("#gridPanelPayslipTemplate").show();
            $("#backToPayslipTemplateGrid").hide();
            tablePayslipTemplate.$('tr.row_selected').removeClass('row_selected');
            fnActionButtonsPayslipTemplate(false);
        }
    }
    // function open grapes-js module
    function fnOpenGrapesJsModule() {
        isDirtyFormPayslipTemplate = false;
        $('#grapesJsModule').removeClass('hidden');
        $('#modalFormPayslipTemplate').hide();
        $("#grapesJsModule").get(0).style.setProperty('height', '1000px');
        $("#gridPanelPayslipTemplate").hide();
        $("#backToPayslipTemplateGrid").show();
        $('#gjs').show();
        isDirtyFormPayslipTemplate = true;
    }
    // function getmonth and year
    function fngetMonthYear() {
        var date = new Date();
        var month = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"]
        var monthYear = month[date.getMonth()] + ',' + date.getFullYear();
        return monthYear;
    }
    // function to get result
    function fnGetResult() {
        return {
            Currency: "Rs",
            Deduction: [
                { Deduction_Name: "Provident Fund", Deduction_Amount: "123.00", Description: "" },
                { Deduction_Name: "TDS", Deduction_Amount: "0.00", Description: "" },
                { Deduction_Name: "Professional Tax", Deduction_Amount: "500.00", Description: "" }
            ],
            Emp_Pan: "**********",
            FixedHealthInsurance: [{ Insurance_Amount: "1200.00", Insurance_Name: "health" }],
            Form16: {
                Sec10Exemption: [],
                form16Summary: [
                    { SummaryName: "A. Gross Salary", SummaryAmount: "238332.88" },
                    { SummaryName: "B. Perks", SummaryAmount: "0.00" },
                    { SummaryName: "C. Exemptions U/S 10", SummaryAmount: "0.00" },
                    { SummaryName: "D. Balance (A+B-C)", SummaryAmount: "238332.88" },
                    { SummaryName: "E. Deductions U/S 16", SummaryAmount: "50000.00" },
                    { SummaryName: "F. Income under Head Salary (D-E)", SummaryAmount: "188332.88" },
                    { SummaryName: "G. Income U/S 24", SummaryAmount: "0.00" },
                    { SummaryName: "H. Gross Total Income (F+G)", SummaryAmount: "188332.88" },
                    { SummaryName: "I. Aggregate of Chapter VI", SummaryAmount: "0.00" },
                    { SummaryName: "J. Tax Relief", SummaryAmount: "12000.00" },
                    { SummaryName: "K. Pre-Tax Deduction", SummaryAmount: "0.00" },                    
                    { SummaryName: "L. Total Income (H-(I+J+K))", SummaryAmount: "188332.88" },
                    { SummaryName: "M. Tax on Total Income", SummaryAmount: "200.00" },
                    { SummaryName: "N. Tax deducted so far", SummaryAmount: "200.00" },
                    { SummaryName: "O. Balance Tax Deductible (M-N)", SummaryAmount: "0.00" },
                ],
                form16TaxDetails: [
                    { ExemptionName: "Exemptions U/S 10", ExemptionAmount: "0.00" },
                    { ExemptionName: "Aggregate of Chapter VI", ExemptionAmount: "0.00" },
                    { ExemptionName: "Perks", ExemptionAmount: "0.00" }
                ]
            },
            Form16Exists: 1,
            Image: null,
            Incentive: [
                { Incentive_Name: "Basic Pay", Incentive_Amount: "25000.00" },
                { Incentive_Name: "Allowance", Incentive_Amount: "10000.00", Description: "HRA" },
                { Incentive_Name: "Allowance", Incentive_Amount: "5066.00", Description: "Special Allowance" },
                { Incentive_Name: "Allowance", Incentive_Amount: "1600.00", Description: "Conveyance" }
            ],
            InsuranceDetails: [],
            IntermediatePaymentAmt: '1000.00',
            Org_Name: {
                Org_Name: "Demo Company Pvt Ltd",
            },
            PaidLeave: [null, null, [["compensatory OFF", "41"],
            ["Sick Leave", "12"],
            ["Casual Leave", "12"],
            ["Earned Leave", "10"]]],
            Payslip: {
                Business_Unit : 'Demo Business Unit',
                Aadhaar_Card_Number: "************",
                Bank_Account_Number: "*****************",
                Bank_Name: "State Bank",
                Basic_Salary: "25000.00",
                City: "Coimbatore",
                Country: "India",
                Date_Of_Join: "06/05/2010",
                Department_Name: "development",
                Designation_Name: "Admin",
                Emp_First_Name: "Xxx",
                Emp_Last_Name: "Yyy",
                Employee_Id: "1",
                First_Name: "Super",
                Incentive_Amount: "41043.00",
                Total_Salary:"41043.00",
                Incentive_Amount_Words: "Forty One Thousand Forty Three",
                Tax_Relief_Amount: "1000.00",
                Outstanding_Amt: "0.00",
                Pf_PolicyNo: "122xsd212",
                Pincode: "641622",
                Policy_No: "1232312",
                State: "TamilNadu",
                State_Id: "1430",
                Street1: "11/32, KVP Street",
                Street2: "Hopes",
                UAN: "************",
                User_Defined_EmpId: "Emp0978",
                Emp_Relieving_Date: "15/08/2019"
            },
            Payslip_Month: fngetMonthYear(),
            UnpaidLeave: [null, null, [["Leave Of Absence", "365"]]],
            PrevMonthUnpaidLeave: [null, null, [["Leave Of Absence", "3"]]],
            OnDuty: [null,4],
            WorkedDays: 20,
            PaidDays: 20,
            etfAmt: false,
            orgLWFAmtHeader: "Org LWF Amount",
            orgLwfAmount: false,
            orgPfAmt: "123.00",
            orgPfAmtHeader: "Org PF Amount",
            totalEarningsAndOrgShareAmount:'42989.00',
            totalEarnings:'41666.00',
            totalDeductions:'623.00',
            salaryDetails: {
                Annual_Gross_Salary: "500000.00",
                Basic_Pay: "28619.05",
                Is_ETFEmployee: "0",
                Is_InsuranceEmployee: "1",
                Is_PfEmployee: "1",
                Is_Pf: "1"
            },
            actualHours:{
                daysWorked: "20",
                dayWage: "",
            },
            OverTimeHours: "0.8Hr(s)",
            Holiday: "1",
            success: true
        };
    };
    //On second modal close, first modal scroll will not work. To avoid that,using this
    $('#modalDirtyPayslipTemplate').on('hidden.bs.modal', function () {
        if ($('#modalFormPayslipTemplate').is(':visible')) {
            $('body').addClass('modal-open');
        }
    });

});
