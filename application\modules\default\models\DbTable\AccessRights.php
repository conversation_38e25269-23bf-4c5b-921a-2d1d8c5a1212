<?php
//=========================================================================================
//=========================================================================================
/* Program : AccessRights.php									   				         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MQL Query to provide access rights to the employee for all the          *
 * sub-modules and to update the designation roles.					                     *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Narmadha                Initial Version         	         *
 *  0.2        06-Jul-2014    Mahesh				  1.updateEmpAccessRights            *
 *  											      2.employeeAccessRights             *
 *  1.0        02-Feb-2015    Prasanth                Changes in file for mobile app     *
 *                                                    1.Extra fields are added in        *
 *                                                    field list of list query.          */
//=========================================================================================
//=========================================================================================
class Default_Model_DbTable_AccessRights extends Zend_Db_Table_Abstract
{
    protected $_db = null;
	protected $_ehrTables = null;
	protected $_isMobile = null;
	protected $_hrappMobile = null;

    public function init()
    {
        $this->_ehrTables = new Application_Model_DbTable_Ehr();
        $this->_db = Zend_Registry::get('subHrapp');
		$this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
		$this->_isMobile = $this->_hrappMobile->checkDevice();
    }
    //version 0.2 => added hr group and payroll group
    /**
     * to get employee access rights based on their designation or employee Id
     */
    public function employeeAccessRights ($empId, $formName)
    {
        $empRole = '';
		$formAccess = '';
        $adminFormName = array('Admin','Employee Admin','Payroll Admin','Service Provider Admin','Roster Admin','Productivity Monitoring Admin');                                        

        // check for roles access rights
        $getRolesAccessCnt = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob, array('Roles_Id'))
        ->where('Employee_Id = ?', $empId)
        );

        if(empty($getRolesAccessCnt)){
            // check for employee access rights
            $getEmpAccessCnt = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empAccessRights, new Zend_Db_Expr('Count(Form_Id)'))
                                                    ->where('Employee_Id = ?', $empId));
            
            // if employee level access exist and if all the form view, add, update and optional choice are 1 he will be admin
            if($getEmpAccessCnt>0)
            {
                if ($formName != 'Super Admin')
                {
                    /** check Admin access */
                    $qryEmployeeRole = $this->_db->select()->from(array('A'=>$this->_ehrTables->empAccessRights),array('F.Form_Name'))
                                        ->joinInner(array('F'=>$this->_ehrTables->forms), 'F.Form_Id=A.Form_Id',array())
                                        ->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.Employee_Id=A.Employee_Id', array())
                                        ->where('A.Employee_Id = ?', $empId)
                                        ->where('J.Emp_Status Like ?', 'Active')
                                        ->where('A.Role_Update = 1')
                                        ->where('F.Form_Name IN (?)',$adminFormName);
    
                    $employeeRole = $this->_db->fetchCol($qryEmployeeRole);
                    if($formName!='')
                    {
                        /** get the module name from the formname */
                        $qryGetModule = $this->_db->select()->from(array('EA'=>$this->_ehrTables->empAccessRights), array())
                                            ->joinInner(array('FO'=>$this->_ehrTables->forms), 'FO.Form_Id=EA.Form_Id',array())
                                            ->joinInner(array('M'=>$this->_ehrTables->modules), 'M.Module_Id=FO.Module_Id',array('M.Module_Name'))
                                            ->joinInner(array('JO'=>$this->_ehrTables->empJob), 'JO.Employee_Id=EA.Employee_Id', array())
                                            ->where('FO.Form_Name = ?', $formName)
                                            ->where('EA.Employee_Id = ?', $empId)
                                            ->where('JO.Emp_Status Like ?', 'Active');
                                            
                        $getModule = $this->_db->fetchOne($qryGetModule);
                        
                        $empRole = $this->getEmployeeRole($employeeRole,$getModule,$formName);
    
                        $formAccess = $this->getEmployeeLevelFormAccess($empId,$formName);// Get the form access
                    }
                }
                else
                {
                    //If the form name is 'Super admin', return the form access and return the 'Admin' param as empty string('')
                    $formAccess = $this->getEmployeeLevelFormAccess($empId,$formName);// Get the super admin form access
                }
            }
            else
            {
                if ($formName != 'Super Admin')
                {
                    // if access is based on employee designation and if all the form view, add, update and optional choice are 1 he will be admin
                    $qryDesignationRoles = $this->_db->select()->from(array('R'=>$this->_ehrTables->roles), array('F.Form_Name'))
                                                ->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.Designation_Id=R.Designation_Id', array())
                                                ->joinInner(array('F'=>$this->_ehrTables->forms), 'F.Form_Id=R.Form_Id',array())
                                                ->where('J.Employee_Id = ?', $empId)
                                                ->where('J.Emp_Status Like ?', 'Active')
                                                ->where('R.Role_Update = 1')
                                                ->where('F.Form_Name IN (?)',$adminFormName);
                    
                    $designationRoles = $this->_db->fetchCol($qryDesignationRoles);
                    
                    if ($formName != '')
                    {
                        /** get the module name from the formname */
                        $qryGetModule = $this->_db->select()->from(array('RO'=>$this->_ehrTables->roles), array())
                                            ->joinInner(array('FO'=>$this->_ehrTables->forms), 'FO.Form_Id=RO.Form_Id',array())
                                            ->joinInner(array('M'=>$this->_ehrTables->modules), 'M.Module_Id=FO.Module_Id',array('M.Module_Name'))
                                            ->joinInner(array('JO'=>$this->_ehrTables->empJob), 'JO.Designation_Id=RO.Designation_Id', array())
                                            ->where('FO.Form_Name = ?', $formName)
                                            ->where('JO.Employee_Id = ?', $empId)
                                            ->where('JO.Emp_Status Like ?', 'Active');
                                            
                        $getModule = $this->_db->fetchOne($qryGetModule);
    
                        $empRole = $this->getEmployeeRole($designationRoles,$getModule,$formName);
                        
                        $formAccess = $this->getDesignationLevelFormAccess($empId,$formName);// Get the form access
                    }
                }
                else
                {
                    //If the form name is 'Super admin', return the form access and return the 'Admin' param as empty string('')
                    $formAccess = $this->getDesignationLevelFormAccess($empId,$formName);// Get the super admin form access
                }
            }

        }else{
            if ($formName != 'Super Admin')
            {
                // if access is based on employee roles and if all the form view, add, update and optional choice are 1 he will be admin
                $qryRoles = $this->_db->select()->from(array('R'=>$this->_ehrTables->roleAccessControl), array('F.Form_Name'))
                                            ->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.Roles_Id=R.Roles_Id', array())
                                            ->joinInner(array('F'=>$this->_ehrTables->forms), 'F.Form_Id=R.Form_Id',array())
                                            ->where('J.Employee_Id = ?', $empId)
                                            ->where('J.Emp_Status Like ?', 'Active')
                                            ->where('R.Role_Update = 1')
                                            ->where('F.Form_Name IN (?)',$adminFormName);
                
                $employeeRoles = $this->_db->fetchCol($qryRoles);
                
                if ($formName != '')
                {
                    /** get the module name from the formname */
                    $qryGetModule = $this->_db->select()->from(array('RO'=>$this->_ehrTables->roleAccessControl), array())
                                        ->joinInner(array('FO'=>$this->_ehrTables->forms), 'FO.Form_Id=RO.Form_Id',array())
                                        ->joinInner(array('M'=>$this->_ehrTables->modules), 'M.Module_Id=FO.Module_Id',array('M.Module_Name'))
                                        ->joinInner(array('JO'=>$this->_ehrTables->empJob), 'JO.Roles_Id=RO.Roles_Id', array())
                                        ->where('FO.Form_Name = ?', $formName)
                                        ->where('JO.Employee_Id = ?', $empId)
                                        ->where('JO.Emp_Status Like ?', 'Active');
                                        
                    $getModule = $this->_db->fetchOne($qryGetModule);

                    $empRole = $this->getEmployeeRole($employeeRoles,$getModule,$formName);
                    
                    $formAccess = $this->getEmployeeRoleLevelFormAccess($empId,$formName);// Get the form access
                }
            }
            else
            {
                //If the form name is 'Super admin', return the form access and return the 'Admin' param as empty string('')
                $formAccess = $this->getEmployeeRoleLevelFormAccess($empId,$formName);// Get the super admin form access
            }
        }
        

        return array('Admin'=>$empRole, 'Employee'=>$formAccess);
    }

    public function employeeAccessRightsBasedOnFormId ($empId, $formId)
    {
        $empRole = '';
		$formAccess = '';
        // 'Admin','Employee Admin','Payroll Admin','Service Provider Admin','Roster Admin','Productivity Monitoring Admin'                                      
        $adminFormIds = array(22, 148, 149, 219, 222, 225);
        // check for roles access rights
        $getRolesAccessCnt = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob, array('Roles_Id'))
        ->where('Employee_Id = ?', $empId)
        );

        if(empty($getRolesAccessCnt)){
            // check for employee access rights
            $getEmpAccessCnt = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empAccessRights, new Zend_Db_Expr('Count(Form_Id)'))
                                                    ->where('Employee_Id = ?', $empId));
            
            // if employee level access exist and if all the form view, add, update and optional choice are 1 he will be admin
            if($getEmpAccessCnt>0)
            {
                if ($formId != 147)
                {
                    /** check Admin access */
                    $qryEmployeeRole = $this->_db->select()->from(array('A'=>$this->_ehrTables->empAccessRights),array('F.Form_Name'))
                                                ->joinInner(array('F'=>$this->_ehrTables->forms), 'F.Form_Id=A.Form_Id',array())
                                                ->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.Employee_Id=A.Employee_Id', array())
                                                ->where('A.Employee_Id = ?', $empId)
                                                ->where('J.Emp_Status Like ?', 'Active')
                                                ->where('A.Role_Update = 1')
                                                ->where('A.Form_Id IN (?)',$adminFormIds);
    
                    $employeeRole = $this->_db->fetchCol($qryEmployeeRole);
                    if(!empty($formId))
                    {
                        /** get the module name from the formId */
                        $qryGetModule = $this->_db->select()->from(array('EA'=>$this->_ehrTables->empAccessRights), array())
                                                ->joinInner(array('FO'=>$this->_ehrTables->forms), 'FO.Form_Id=EA.Form_Id',array())
                                                ->joinInner(array('M'=>$this->_ehrTables->modules), 'M.Module_Id=FO.Module_Id',array('M.Module_Name'))
                                                ->joinInner(array('JO'=>$this->_ehrTables->empJob), 'JO.Employee_Id=EA.Employee_Id', array())
                                                ->where('FO.Form_Id = ?', $formId)
                                                ->where('EA.Employee_Id = ?', $empId)
                                                ->where('JO.Emp_Status Like ?', 'Active');
                                            
                        $getModule = $this->_db->fetchOne($qryGetModule);
                        
                        $empRole = $this->getEmployeeRoleBasedOnFormId($employeeRole,$getModule,$formId);
    
                        $formAccess = $this->getEmployeeLevelFormAccessBasedOnFormId($empId,$formId);// Get the form access
                    }
                }
                else
                {
                    //If the form name is 'Super admin', return the form access and return the 'Admin' param as empty string('')
                    $formAccess = $this->getEmployeeLevelFormAccessBasedOnFormId($empId,$formId);// Get the super admin form access
                }
            }
            else
            {
                if ($formId != 147)
                {
                    // if access is based on employee designation and if all the form view, add, update and optional choice are 1 he will be admin
                    $qryDesignationRoles = $this->_db->select()->from(array('R'=>$this->_ehrTables->roles), array('F.Form_Name'))
                                                    ->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.Designation_Id=R.Designation_Id', array())
                                                    ->joinInner(array('F'=>$this->_ehrTables->forms), 'F.Form_Id=R.Form_Id',array())
                                                    ->where('J.Employee_Id = ?', $empId)
                                                    ->where('J.Emp_Status Like ?', 'Active')
                                                    ->where('R.Role_Update = 1')
                                                    ->where('R.Form_Id IN (?)',$adminFormIds);
                    
                    $designationRoles = $this->_db->fetchCol($qryDesignationRoles);
                    
                    if (!empty($formId))
                    {
                        /** get the module name from the formId */
                        $qryGetModule = $this->_db->select()->from(array('RO'=>$this->_ehrTables->roles), array())
                                                ->joinInner(array('FO'=>$this->_ehrTables->forms), 'FO.Form_Id=RO.Form_Id',array())
                                                ->joinInner(array('M'=>$this->_ehrTables->modules), 'M.Module_Id=FO.Module_Id',array('M.Module_Name'))
                                                ->joinInner(array('JO'=>$this->_ehrTables->empJob), 'JO.Designation_Id=RO.Designation_Id', array())
                                                ->where('RO.Form_Id = ?', $formId)
                                                ->where('JO.Employee_Id = ?', $empId)
                                                ->where('JO.Emp_Status Like ?', 'Active');
                                            
                        $getModule = $this->_db->fetchOne($qryGetModule);
    
                        $empRole = $this->getEmployeeRoleBasedOnFormId($designationRoles,$getModule,$formId);
                        
                        $formAccess = $this->getDesignationLevelFormAccessBasedOnFormId($empId,$formId);// Get the form access
                    }
                }
                else
                {
                    //If the form name is 'Super admin', return the form access and return the 'Admin' param as empty string('')
                    $formAccess = $this->getDesignationLevelFormAccessBasedOnFormId($empId,$formId);// Get the super admin form access
                }
            }

        }else{
            if ($formId != 147)
            {
                // if access is based on employee roles and if all the form view, add, update and optional choice are 1 he will be admin
                $qryRoles = $this->_db->select()->from(array('R'=>$this->_ehrTables->roleAccessControl), array('F.Form_Name'))
                                            ->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.Roles_Id=R.Roles_Id', array())
                                            ->joinInner(array('F'=>$this->_ehrTables->forms), 'F.Form_Id=R.Form_Id',array())
                                            ->where('J.Employee_Id = ?', $empId)
                                            ->where('J.Emp_Status Like ?', 'Active')
                                            ->where('R.Role_Update = 1')
                                            ->where('F.Form_Id IN (?)',$adminFormIds);
                
                $employeeRoles = $this->_db->fetchCol($qryRoles);
                
                if (!empty($formId))
                {
                    /** get the module name from the formId */
                    $qryGetModule = $this->_db->select()->from(array('RO'=>$this->_ehrTables->roleAccessControl), array())
                                            ->joinInner(array('FO'=>$this->_ehrTables->forms), 'FO.Form_Id=RO.Form_Id',array())
                                            ->joinInner(array('M'=>$this->_ehrTables->modules), 'M.Module_Id=FO.Module_Id',array('M.Module_Name'))
                                            ->joinInner(array('JO'=>$this->_ehrTables->empJob), 'JO.Roles_Id=RO.Roles_Id', array())
                                            ->where('RO.Form_Id = ?', $formId)
                                            ->where('JO.Employee_Id = ?', $empId)
                                            ->where('JO.Emp_Status Like ?', 'Active');
                                        
                    $getModule = $this->_db->fetchOne($qryGetModule);

                    $empRole = $this->getEmployeeRoleBasedOnFormId($employeeRoles,$getModule,$formId);
                    
                    $formAccess = $this->getEmployeeRoleLevelFormAccessBasedOnFormId($empId,$formId);// Get the form access
                }
            }
            else
            {
                //If the form name is 'Super admin', return the form access and return the 'Admin' param as empty string('')
                $formAccess = $this->getEmployeeRoleLevelFormAccessBasedOnFormId($empId,$formId);// Get the super admin form access
            }
        }
        

        return array('Admin'=>$empRole, 'Employee'=>$formAccess);
    }


    public function getEmployeeRole($employeeRole,$getModule,$formName)
    {
        $empRole = '';
        if (in_array('Admin', $employeeRole)) 
        {
            $empRole = 'admin';
        }
        elseif(in_array('Employee Admin', $employeeRole) && ($getModule == 'Employees' || in_array($formName, array('Employee Import','Leave Balance Import','Dependent Import','Leave Import','Employees Reports','Custom Report','HR Reports',
            'Performance Evaluation','Performance Assessment','Skill Level Association','Skill Definition','Goals And Achievement'))))
        {
            /** If the form lies in employee module or  when the form is employee import,leave balance import,dependent import,leave import
            * in that time we need consider that employee as admin for those forms and module */
            $empRole = 'admin';
        }
        elseif(in_array('Payroll Admin', $employeeRole) && ($getModule == 'Payroll'|| $getModule == 'Tax and Statutory Compliance' || in_array($formName, array('Timesheets', 'Salary Import','Form16 Import','Allowance Import','Tax Declaration Import','Deduction Import','Adhoc Allowance Import','TDS History Import','Payroll Reports','Tax Entities','Tax Configuration','Gratuity Settings','Organization Account','Form16','Form12BA','Form24Q'))))
        {
            /** If the form lies in payroll module  or when the form is salary import,form16 balance import,allowance import,tax declaration import,deduction import
             * adhoc allowance import,tds history import in that time we need consider that employee as admin for those forms and module */
            $empRole = 'admin';
        }
        elseif(in_array('Roster Admin', $employeeRole)&& $getModule == 'Roster Management')
        {
                $empRole = 'admin';
        }
        elseif(in_array('Productivity Monitoring Admin', $employeeRole) && ($getModule == 'Productivity Monitoring' || $getModule == 'Core HR' || in_array($formName, array('Productivity Monitoring')) ))
        {
                $empRole = 'admin';
        }
        elseif(in_array('Service Provider Admin', $employeeRole))
        {
            if(in_array($formName, array('Employees','Leaves','Attendance','Attendance Finalization','Attendance Import','Dashboard Attendance','Employee Bank Account',
            'Employees Document Upload','Reimbursement','Resignation','Approval Management','Allowances','Adhoc Allowance','Salary',
            'Employees Reports','Payroll Reports','Tax Declarations','HRA Declarations','TDS History','Salary Payslip','Bonus','Deductions',
            'Advance Salary','Loan','Deferred Loan','Fixed Health Insurance','Compensatory Off','Compensatory Off Balance','Short Time Off',
            'Custom Report','Timeline','Adhoc Allowance Import','Deduction Import','Leave Import','Payslip Template','Income Under Section24',
            'Proof Of Investment','Assignments','Timesheets','Performance Assessment','Performance Evaluation','Transfer','Individuals','Candidates',
            'Tax Declaration Import','Leave Balance Import','Allowance Import','Dependent Import','Employee Import','Form16 Import','Salary Import','Accreditation')))
            {
                $empRole = 'admin';
            }
        }
        else 
        {
            $empRole = '';
        }

        return $empRole;
    }

    public function getEmployeeRoleBasedOnFormId($employeeRole,$getModule,$formId)
    {
        $empRole = '';
        if (in_array('Admin', $employeeRole)) 
        {
            $empRole = 'admin';
        }
        elseif(in_array('Employee Admin', $employeeRole) && ($getModule == 'Employees' || 
            in_array($formId, array(29,98, 117, 133, 138, 64,141,63, 81,84,82,833,214,304,332,334,352,367,368,369))))
        {
            /** If the form lies in employee module or  when the form is employee import,leave balance import,dependent import,leave import
            * in that time we need consider that employee as admin for those forms and module */
            $empRole = 'admin';
        }
        elseif(in_array('Payroll Admin', $employeeRole) && ($getModule == 'Payroll'|| $getModule == 'Tax and Statutory Compliance' || 
            in_array($formId, array(97, 125, 80, 137, 143, 144, 233, 65, 13, 105, 107, 94,108, 121, 122))))
        {
            /** If the form lies in payroll module  or when the form is salary import,form16 balance import,allowance import,tax declaration import,deduction import
             * adhoc allowance import,tds history import in that time we need consider that employee as admin for those forms and module */
            $empRole = 'admin';
        }
        elseif(in_array('Roster Admin', $employeeRole)&& $getModule == 'Roster Management')
        {
                $empRole = 'admin';
        }
        elseif(in_array('Productivity Monitoring Admin', $employeeRole) && ($getModule == 'Productivity Monitoring' || $getModule == 'Core HR' ||
            in_array($formId, array(196)) ))
        {
                $empRole = 'admin';
        }
        elseif(in_array('Service Provider Admin', $employeeRole))
        {
            if(in_array($formId, array(18,29,98, 31, 29, 80, 174, 159, 155, 50, 34, 184, 45, 131, 37, 64, 65, 61, 119, 156, 38, 46, 49,
            53, 54, 73, 152, 139, 140, 128, 141, 142, 144, 143, 138, 176, 192, 194, 30, 23, 84, 81, 33, 178, 179, 137, 117, 80, 133, 125, 97, 232,304,332,334,352,367,368,369)))
            {
                $empRole = 'admin';
            }
        }
        else 
        {
            $empRole = '';
        }

        return $empRole;
    }
	
	/**
	 * Refresh user session timestamp for logged in employee
	 */
    public function refreshUserSessionTimestamp($logEmpId)
    {
        // if (!empty($logEmpId))
        // {
        //     $userSession = $this->_db->fetchRow($this->_db->select()
		// 										->from($this->_ehrTables->sessionList,
		// 											   array('Session_Id', 'Timeout_Sec', 'Ts_Diff'=>new Zend_Db_Expr('TIMESTAMPDIFF(SECOND,Refresh_Timestamp,NOW())')))
		// 										->where('Session_Id = ?', $logEmpId));
            
        //     if (!empty($userSession))
        //     {
        //         if ($userSession['Ts_Diff']<=$userSession['Timeout_Sec'] && !empty($userSession['Session_Id']))
        //         {
        //             $this->_db->update($this->_ehrTables->sessionList, array('Refresh_Timestamp'=>date('Y-m-d H:i:s')/*new Zend_Db_Expr('NOW()')*/), 'Session_Id = '. $logEmpId);
        //         }
        //     }
		// 	else
		// 	{
		// 		//if ($this->_hrappMobile->checkAuth ())
		// 		//{
		// 		//	$getSysConfig = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->sysConfig, array('Sys_Value'))
		// 		//										 ->where('Config_Param = ?', 'SESSION_TIMEOUT'));
		// 		//	
		// 		//	$this->_db->insert($this->_ehrTables->sessionList, array('Session_Id'=> $logEmpId,
		// 		//															 'Timeout_Sec'=>$getSysConfig,
		// 		//															 'Refresh_Timestamp'=> new Zend_Db_Expr('NOW()') ));
		// 		//}
		// 	}
        // }
        
        return 1;
    }
	
    /**
     *
     * To lock the edit form so that single user can access it
     * @param int $lockFlag - Employee login Id
     * @param int $uniqueId - table primary key value
     * @param string $tableName - table name
     * @param string $fieldName - primary key field name
     * @return boolean
     */
    public function setLockFlag($lockFlag, $uniqueId, $tableName, $fieldName)
    {
        $this->_db->setFetchMode(Zend_Db::FETCH_ASSOC);
		
        if (!empty($fieldName) && !empty($uniqueId))
        {
            if ($tableName == 'Copy Pf')
            {
                $where[$fieldName . ' = ?'] = $uniqueId;
                
                $this->_db->update($this->_ehrTables->empPF, array('Copy_Lock'=> $lockFlag), $where);
            }
            else
            {
                $where[$fieldName . ' = ?'] = $uniqueId;
                $this->_db->update($tableName, array('Lock_Flag'=> $lockFlag), $where);
            }
        }		
                
        $getThrowLock = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->sessionLock, array('No_LockRecords'))
                                                ->where('Session_Id = ?', $lockFlag)
                                                ->where('Table_Name = ?', $tableName));
        
        if(empty($getThrowLock))
        {
            //insert
            $sessionLock = $this->_db->insert($this->_ehrTables->sessionLock, array('Table_Name'=>$tableName,
                                                                                    'Session_Id'=>$lockFlag,
                                                                                    'No_LockRecords'=>1));
            return true;
        }
        else
        {
            if(!empty($fieldName) && !empty($uniqueId))
            {
                //timesheet,reimbursement,travel,shift,holiday,activity,accessrights, tax declaration - add group by
                if ($tableName == $this->_ehrTables->empShift || $tableName == $this->_ehrTables->empTimesheet ||
                    $tableName == $this->_ehrTables->reimbursement || $tableName == $this->_ehrTables->empTravels ||
                    $tableName == $this->_ehrTables->timesheetActivity || $tableName == $this->_ehrTables->empAccessRights ||
                    $tableName == $this->_ehrTables->roles || $tableName == $this->_ehrTables->workSchedule ||
                    $tableName == $this->_ehrTables->taxDeclaration)
                {
                    $ckLockTable = count($this->_db->fetchCol($this->_db->select()->from($tableName, 'Lock_Flag')
                                                                ->where('Lock_Flag = ?', $lockFlag)
                                                                ->group($fieldName)));
                }
                else if($tableName == 'holidays')
                {
                    $ckLockTable = count($this->_db->fetchCol($this->_db->select()->from($tableName, 'Lock_Flag')
                                                                ->where('Lock_Flag = ?', $lockFlag)
                                                                ->group('Holiday_Name')
                                                                ->group('YEAR(Holiday_Date)')));
                }
                elseif($tableName == 'Copy Pf')
                {
                    $ckLockTable = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empPF, new Zend_Db_Expr('COUNT(Copy_Lock)'))->where('Copy_Lock = ?', $lockFlag));
                }
                else
                {
                    $ckLockTable = $this->_db->fetchOne($this->_db->select()->from($tableName, new Zend_Db_Expr('COUNT(Lock_Flag)'))->where('Lock_Flag = ?', $lockFlag));
                }
                
                if($ckLockTable != $getThrowLock)
                {
                    //update
                    $throwLockCondition['Session_Id = ?'] = $lockFlag;
                    $throwLockCondition['Table_Name = ?'] = $tableName;
                    $incLockRecords = $getThrowLock+1;
                    $sessionLock = $this->_db->update($this->_ehrTables->sessionLock, array('No_LockRecords'=> $incLockRecords), $throwLockCondition);
                }
            }
            
            return true;
        }
    }
	
    /**
     * to clear lock session after form updation with lock flag which is set to 0 so that other user who who waits for updation or deletion can access it.
     * @param integer $lockFlag - logged in empId
     * @param string $tableName - table name of a particular form.
     */
    public function clearSubmitLock($lockFlag, $tableName)
    {
        $userSession = $this->_hrappMobile->getUserDetails();
        
        if(!empty($userSession)){
            $getSessionId = $userSession['logUserId'];
            
            if (!empty($getSessionId))
            {
                $getThrowLock = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->sessionLock, array('No_LockRecords'))
                                                    ->where('Session_Id = ?', $getSessionId)
                                                    ->where('Table_Name = ?', $tableName));
                
                if ($getThrowLock == 1)
                {
                    $throwLock['Table_Name = ?'] = $tableName;
                    $throwLock['Session_Id = ?'] = $getSessionId;
                    
                    $this->_db->delete($this->_ehrTables->sessionLock, $throwLock);
                    
                    return true;
                }
                elseif($getThrowLock > 1)
                {
                    $throwLock['Session_Id = ?'] = $getSessionId;
                    $throwLock['Table_Name = ?'] = $tableName;
                    $decLockRecords = $getThrowLock-1;
                    
                    $this->_db->update($this->_ehrTables->sessionLock, array('No_LockRecords'=> $decLockRecords), $throwLock);
                    
                    return true;
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        } else
        {
            return false;
        }
    }
     
	/**
	 * Check lock flag whether set or not for a given table by unigueId
	 */
    public function checkLockFlag($uniqueId, $tableName, $fieldName)
    {
        
    	$condition = $this->_db->quoteInto($fieldName . ' = ?', $uniqueId);
        
        return $this->_db->fetchOne($this->_db->select()->from($tableName, 'Lock_Flag')->where($condition));
    }
	
	/**
	 * Check lock flag whether set or not for a given table by unigueId
	 */
    public function checkUserLockFlag ($logEmpId, $tableName, $fieldName)
    {
    	$condition = $this->_db->quoteInto('Lock_Flag = ?', $logEmpId);
    	
        return $this->_db->fetchOne($this->_db->select()->from($tableName, $fieldName)->where($condition));
    }
	
	/**
	 * Check whether LockRecords limit exceeds or not
	 */
    public function checkOpenedRecordLimit($logEmpId)
    {
        $getSysConfig = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->sysConfig, array('LockRecords_Limit'))
											 ->where('Config_Param = ?', 'SESSION_TIMEOUT'));
		
        $countLockFlag = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->sessionLock, new Zend_Db_Expr('SUM(No_LockRecords)'))
											  ->where('Session_Id = ?', $logEmpId));
		
        if (($countLockFlag-1) >= $getSysConfig)
			return array(false, $getSysConfig);
        else
			return array(true, $getSysConfig);
    }
	
	/**
	 * Check whether LockRecords limit exceeds or not
	 */
    public function checkOpenedRecordLimitJS($logEmpId)
    {
        $getSysConfig = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->sysConfig, array('LockRecords_Limit'))
											 ->where('Config_Param = ?', 'SESSION_TIMEOUT'));
		
        $countLockFlag = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->sessionLock, new Zend_Db_Expr('SUM(No_LockRecords)'))
											  ->where('Session_Id = ?', $logEmpId));
		
		if ($countLockFlag >= $getSysConfig)
			return array(false, $getSysConfig);
        else
			return array(true, $getSysConfig);
    }
	
	/**
	 * Fet multi lock flags for table list
	 */
    public function checkMultiLockFlag($tableList)
    {
        if(count($tableList)>0)
        {
            foreach($tableList as $tableValue)
            {
                $getLockFlag = $this->_db->fetchOne($this->_db->select()->from($tableValue['TableName'], 'Lock_Flag')->where($tableValue['FieldName'] . ' = ?', $tableValue['FieldValue']));
                $lockEmpId[] = $getLockFlag;
            }
            return array_unique($lockEmpId);
        }
    }
	
    /**
     *
     * To lock the edit form so that single user can access it
     * @param int $lockFlag - Employee login Id
     * @param array $tableList - contains table name, field name and field value
     * @return boolean
     */
    public function setMultiLockFlag($lockFlag, $tableList)
    {
        $userSession = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->sessionList, array('Session_Id', 'Timeout_Sec', 'Ts_Diff'=>new Zend_Db_Expr('TIMESTAMPDIFF(SECOND,Refresh_Timestamp,NOW())')))
        ->where('Session_Id = ?', $lockFlag));
        if($userSession['Ts_Diff']<=$userSession['Timeout_Sec'] && !empty($userSession['Session_Id']))
        {
            $getSessionId = $userSession['Session_Id'];
            $this->_db->update($this->_ehrTables->sessionList, array('Refresh_Timestamp'=> date('Y-m-d H:i:s') /*new Zend_Db_Expr('NOW()')*/ ), 'Session_Id = '. $getSessionId);
             
            if(count($tableList)>0)
            {
                foreach($tableList as $tableValue)
                {

                    if(!empty($tableValue['FieldName']) && !empty($tableValue['FieldValue']))
                    {
                        $where['Lock_Flag = ?'] = 0;
                        $where[$tableValue['FieldName'] . ' = ?'] = $tableValue['FieldValue'];
                        $this->_db->update($tableValue['TableName'], array('Lock_Flag'=> $lockFlag), $where);
                    }

                    	
                    $getThrowLock = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->sessionLock, array('No_LockRecords'))
                    ->where('Session_Id = ?', $lockFlag)->where('Table_Name = ?', $tableValue['TableName']));
                    if(empty($getThrowLock))
                    {
                        //insert
                        $this->_db->insert($this->_ehrTables->sessionLock, array('Table_Name'=>$tableValue['TableName'], 'Session_Id'=>$lockFlag, 'No_LockRecords'=>1));
                        return true;
                    }
                    else
                    {
                        if(!empty($tableValue['FieldName']) && !empty($tableValue['FieldValue']))
                        {
                            $ckLockTable = $this->_db->fetchOne($this->_db->select()->from($tableValue['TableName'], new Zend_Db_Expr('COUNT(Lock_Flag)'))->where('Lock_Flag = ?', $lockFlag));
                            	
                            if($ckLockTable != $getThrowLock)
                            {
                                //update
                                $throwLockCondition['Session_Id = ?'] = $lockFlag;
                                $throwLockCondition['Table_Name = ?'] = $tableValue['TableName'];
                                $incLockRecords = $getThrowLock+1;
                                $this->_db->update($this->_ehrTables->sessionLock, array('No_LockRecords'=> $incLockRecords), $throwLockCondition);
                            }
                        }
                        return true;
                    }
                }
            }
        }
        else
        {
            return false;
        }
    }
   
    /**
     * to clear lock session after form updation with lock flag which is set to 0 so that other user who who waits for updation or deletion can access it.
     * @param integer $lockFlag - logged in empId
     * @param array $tableList - contains table name, field name, field value
     */
    public function clearMultipleLock($lockFlag, $tableList)
    {
        $getSessionId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->sessionList, 'Session_Id')->where('Session_Id = ?', $lockFlag));
        if(!empty($getSessionId))
        {
            $sessionList = $this->_db->update($this->_ehrTables->sessionList, array('Refresh_Timestamp'=> date('Y-m-d H:i:s') /*new Zend_Db_Expr('NOW()')*/ ), 'Session_Id = '. $getSessionId);
            if(count($tableList)>0)
            {
                foreach($tableList as $tableValue)
                {
                    $getThrowLock = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->sessionLock, array('No_LockRecords'))
                    ->where('Session_Id = ?', $getSessionId)->where('Table_Name = ?', $tableValue['TableName']));
                    if($getThrowLock == 1)
                    {
                        $throwLock['Table_Name = ?'] = $tableValue['TableName'];
                        $throwLock['Session_Id = ?'] = $getSessionId;
                        $this->_db->delete($this->_ehrTables->sessionLock, $throwLock);
                        return true;
                    }
                    elseif($getThrowLock > 1)
                    {
                        $throwLock['Table_Name = ?'] = $tableValue['TableName'];
                        $throwLock['Session_Id = ?'] = $getSessionId;
                        $decLockRecords = $getThrowLock-1;
                        $this->_db->update($this->_ehrTables->sessionLock, array('No_LockRecords'=> $decLockRecords), $throwLock);
                        return true;
                    }
                    else
                    {
                        return false;
                    }

                }
            }
        }
        else
        return false;

    }
	
	/**
	 * Check session lock exists or not by employeeId
	 */
    public function checkSessionLock($employeeId)
    {
        return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->sessionLock, new Zend_Db_Expr('COUNT(Session_Id)'))
									->where('Session_Id = ?', $employeeId));
    }
	
	/**
	 * Clear lock flag for the selected record
	 */
    public function clearLockFlag($tableName, $fieldName, $uniqueId)
    {
		if ($tableName == 'Copy PF')
		{
			$updLockArr = array('Copy_Lock'=>0);
			
			$updateLock = $this->_db->update($this->_ehrTables->empPF, $updLockArr, array($fieldName ." = ?"=> $uniqueId) );
		}
		else
		{
            $updLockArr = array('Lock_Flag'=>0);
			$updateLock = $this->_db->update($tableName, $updLockArr, array($fieldName ." = ?"=> $uniqueId) );
		}
		
		if ($updateLock)
            return true;
        else
            return false;
    }
	
	/**
	 * Clear multi lock flag
	 */
    public function clearMultiLockFlag($tableList)
    {
        if(count($tableList)>0)
        {
            foreach($tableList as $tableValue)
            {
                $this->_db->update($tableValue['TableName'], array('Lock_Flag'=>0), $tableValue['FieldName'] ." = ". $tableValue['FieldValue']);
            }
        }
    }
	
	/**
	 * Clear timeout session for logged in employee
	 */
    public function clearTimeoutSession($logEmpId)
    {
        $userSession = $this->_hrappMobile->getUserDetails();
        
        if(!empty($userSession)){
            $throwLock = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->sessionLock, array('Table_Name'))
												  ->where('Session_Id = ?', $userSession['logUserId']));
                
            if (!empty($throwLock))
            {
                foreach($throwLock as $lockTable)
                {
                    if ($lockTable != 'SALARY_PAYSLIP' && $lockTable != 'HOURLYWAGES_PAYSLIP')
                    {
                        $updateLockArr = array('Lock_Flag'=>0);
                        
                        if ($lockTable == 'EMP_PF')
                        {
                            $updateLockArr = array('Lock_Flag'=>0,'Copy_Lock'=>0);
                        }
                        $this->_db->update($lockTable, $updateLockArr, 'Lock_Flag = '.$userSession['logUserId']);
                    }
                }
                
                $deleteThrow['Session_Id = ?'] = $userSession['logUserId'];
                $deleteThrow['Table_Name IN (?)'] = $throwLock;
                
                $deleteLock = $this->_db->delete($this->_ehrTables->sessionLock, $deleteThrow);
                
                $deleteList = $this->_db->delete($this->_ehrTables->sessionList, 'Session_Id = '. $userSession['logUserId']);
                
                if ($deleteLock && $deleteList)
                    return true;
            }
            return true;
        }
    }

	/**
	 * Check user session expires or not
	 */
    public function checkTimeoutSession($logEmpId)
    {
        $userSession = $this->_hrappMobile->getUserDetails();

        if(!empty($userSession))
            return false;
        else
            return true;        
    }
	
    /**
     * Access rights Based on designation
     */
    //to list all the main form roles
    public function formRoles($designationId)
    {
        if ($designationId != "")
        {
            return $this->_db->fetchAll($this->_db->select()
								->from(array('role'=>$this->_ehrTables->roles),
									   array('View'=>'Role_View', 'Add'=>'Role_Add', 'Update'=>'Role_Update', 'Delete'=>'Role_Delete',
											 'Optional_Choice'=>'Role_Optional_Choice', 'Hr_Group'=>'Role_Hr_Group',
											 'Payroll_Group'=>'Role_Payroll_Group','role.Form_Id', 'role.Designation_Id'))
								
								->joinInner(array('f'=>$this->_ehrTables->forms),'f.Form_Id=role.Form_Id',array('f.Sub_Form'))
								
								->where('role.Designation_Id = ?', $designationId)
								->where('f.Sub_Form = ?', 0)
								->order("f.Form_Id Asc"));
        }
    }
    
    //to list all the sub form roles
    public function subFormRoles($designationId)
    {
        if ($designationId != "")
        {
            return $this->_db->fetchAll($this->_db->select()
									->from(array('role'=>$this->_ehrTables->roles),
										   array('View'=>'Role_View', 'Add'=>'Role_Add', 'Update'=>'Role_Update',
												 'Delete'=>'Role_Delete', 'Optional_Choice'=>'Role_Optional_Choice',
												 'Hr_Group'=>'Role_Hr_Group', 'Payroll_Group'=>'Role_Payroll_Group', 'role.Form_Id',
												 'role.Designation_Id'))
									
									->joinInner(array('f'=>$this->_ehrTables->forms),'f.Form_Id=role.Form_Id',array('f.Sub_Form'))
									
									->where('role.Designation_Id = ?', $designationId)
									->where('f.Sub_Form > ?', 0)
									->order("f.Form_Id Asc"));
        }
    }
	
    //to check whether roles already exists for a designation
    public function roleExists($designationId)
    {
        $roleExists = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->roles, new Zend_Db_Expr('Count(Designation_Id)'))
										   ->where('Designation_Id = ?',$designationId));
        
		if ($roleExists > 0)
        {
            return $roleExists;
        }
        else
        {
            return null;
        }
    }
     
    //version 0.2=> added hr group and payroll group
    public function updateEmpAccessRights($designation, $formData,$designationId,$addedBy,$modules)
    {
        $formcnt = 0;
        $formRoles = array();
        $data = array();
        $savedRows = 0;
        $roleExists = $this->roleExists($designationId);
        $dbModule = new Application_Model_DbTable_Modules();
		
        $forms = $dbModule->getAllForms();
        
        if ($roleExists == 0)
        {
            foreach ($modules as $module)
            {
                $subFormName = $module['Module_Name'].'Form';
				
                foreach ($forms as $form)
                {
                    if ($form['Module_Id'] == $module['Module_Id'])
                    {
                        if (!empty($formData[$subFormName]['View'.$form['Form_Id']]))
                        {
                            $view = $formData[$subFormName]['View'.$form['Form_Id']];
                        }
                        else
                        {
                            $view = 0;
                        }
                        
						if (!empty($formData[$subFormName]['Add'.$form['Form_Id']]))
                        {
                            $add = $formData[$subFormName]['Add'.$form['Form_Id']];
                        }
                        else
                        {
                            $add = 0;
                        }
                        
						if (!empty($formData[$subFormName]['Update'.$form['Form_Id']]))
                        {
                            $update = $formData[$subFormName]['Update'.$form['Form_Id']];
                        }
                        else
                        {
                            $update = 0;
                        }
                        
						if (!empty($formData[$subFormName]['Delete'.$form['Form_Id']]))
                        {
                            $delete = $formData[$subFormName]['Delete'.$form['Form_Id']];
                        }
                        else
                        {
                            $delete = 0;
                        }
                        
						if (!empty($formData[$subFormName]['OptionalChoice'.$form['Form_Id']]))
                        {
                            $optionalChoice = $formData[$subFormName]['OptionalChoice'.$form['Form_Id']];
                        }
                        else
                        {
                            $optionalChoice = 0;
                        }
                        
						//formData['EmployeeForm']['HrGroup18']
                        if (!empty($formData[$subFormName]['HrGroup'.$form['Form_Id']]))
                        {
                        	$hrGroup = $formData[$subFormName]['HrGroup'.$form['Form_Id']];
                        }
                        else
                        {
                        	$hrGroup = 0;
                        }
                        
						//formData['EmployeeForm']['PayrollGroup18']
                        if (!empty($formData[$subFormName]['PayrollGroup'.$form['Form_Id']]))
                        {
                        	$payrollGroup = $formData[$subFormName]['PayrollGroup'.$form['Form_Id']];
                        }
                        else
                        {
                        	$payrollGroup = 0;
                        }
                        
						$data[$formcnt] = array('Role_View' => $view,
												'Role_Add' => $add,
												'Role_Update' => $update,
												'Role_Delete' => $delete,
												'Role_Optional_Choice' => $optionalChoice,
												'Role_Hr_Group' => $hrGroup,
												'Role_Payroll_Group' => $payrollGroup,
												'Form_Id'=>$form['Form_Id'],
												'Designation_Id'=>$designationId);
                        
						$formRoles[$formcnt] = $data;
                        $formcnt++;
                    }
                }
            }
            
			$insertResult = $this->_ehrTables->insertMultiple($this->_ehrTables->roles,$data);
            
			if ($insertResult)
            {
                $savedRows = $savedRows + 1;
            }
        }
        else
        {
            $roleExistsQry = $this->_db->select()->from(array('role'=>$this->_ehrTables->roles), array('role.Form_Id'))
												->where('role.Designation_Id = ?',$designationId);
            
            $roleForms = $this->_db->fetchCol($roleExistsQry);
			
            $formForms = array();
            $insertData = array();
            
			foreach ($modules as $module)
            {
                $subFormName = $module['Module_Name'].'Form';
                
				foreach ($forms as $form)
                {
                    array_push($formForms,$form['Form_Id']);
                    
					if ($form['Module_Id'] == $module['Module_Id'])
                    {
                        if (!empty($formData[$subFormName]['View'.$form['Form_Id']]))
                        {
                            $view = $formData[$subFormName]['View'.$form['Form_Id']];
                        }
                        else
                        {
                            $view = 0;
                        }
                        
						if (!empty($formData[$subFormName]['Add'.$form['Form_Id']]))
                        {
                            $add = $formData[$subFormName]['Add'.$form['Form_Id']];
                        }
                        else
                        {
                            $add = 0;
                        }
                        
						if (!empty($formData[$subFormName]['Update'.$form['Form_Id']]))
                        {
                            $update = $formData[$subFormName]['Update'.$form['Form_Id']];
                        }
                        else
                        {
                            $update = 0;
                        }
                        
						if (!empty($formData[$subFormName]['Delete'.$form['Form_Id']]))
                        {
                            $delete = $formData[$subFormName]['Delete'.$form['Form_Id']];
                        }
                        else
                        {
                            $delete = 0;
                        }
                        
						if (!empty($formData[$subFormName]['OptionalChoice'.$form['Form_Id']]))
                        {
                            $optionalChoice = $formData[$subFormName]['OptionalChoice'.$form['Form_Id']];
                        }
                        else
                        {
                            $optionalChoice = 0;
                        }
                        
						if (!empty($formData[$subFormName]['HrGroup'.$form['Form_Id']]))
                        {
                        	$hrGroup = $formData[$subFormName]['HrGroup'.$form['Form_Id']];
                        }
                        else
                        {
                        	$hrGroup = 0;
                        }
                        
						if (!empty($formData[$subFormName]['PayrollGroup'.$form['Form_Id']]))
                        {
                        	$payrollGroup = $formData[$subFormName]['PayrollGroup'.$form['Form_Id']];
                        }
                        else
                        {
                        	$payrollGroup = 0;
                        }
                        
						$data = array('Role_View' => $view,
									  'Role_Add' => $add,
									  'Role_Update' => $update,
									  'Role_Delete' => $delete,
									  'Role_Optional_Choice' => $optionalChoice,
									  'Role_Hr_Group' => $hrGroup,
									  'Role_Payroll_Group' => $payrollGroup,
									  'Form_Id'=>$form['Form_Id'],
									  'Designation_Id'=>$designationId,
									  'Lock_Flag'=>0);
						
                        if (in_array($form['Form_Id'], $roleForms))
                        {
                            $where = array('Designation_Id = ?' => (int)$designationId, 'Form_Id = ?' => $form['Form_Id']);
                            
							$updateRoles = $this->_db->update($this->_ehrTables->roles,$data, $where);
                            
							if ($updateRoles)
                            {
                                $savedRows = $savedRows + 1;
                            }
                            $formRoles[$formcnt] = $data;
                            $formcnt++;
                        }
                        else if (!in_array($form['Form_Id'], $roleForms))
                        {
                            $insertData[] = $data;
                            $formRoles[$formcnt] = $data;
                            $formcnt++;
                        }
                    }
                }
            }
            
			if (!empty($insertData))
            {
                $insertRoles = $this->_ehrTables->insertMultiple($this->_ehrTables->roles, $insertData);
				
                if ($insertRoles)
                {
                    $savedRows = $savedRows + 1;
                }
            }
            
			foreach ($roleForms as $role)
            {
                if (!in_array($role, $formForms))
                {
                    $where = array('Designation_Id = ?' => (int)$designationId, 'Form_Id = ?' => $role);
                    $this->_db->delete($this->_ehrTables->roles, $where);
                }
            }
			
            if ($savedRows>0)
            {
                $this->_ehrTables->trackEmpSystemAction('Edit Roles - '.$designation, $addedBy);
            }
        }
        return $savedRows;
    }
	
	
	/**
     *   formDetails () used to define table name & field name for each forms.
    */
    public function formDetails ($title)
    {
        switch($title)
        {
	    
	   
	     case 'Employee Roles':
                $tableName = $this->_ehrTables->empAccessRights;
                $fieldName = 'Employee_Id';
                break;
	     
         case 'Announcements':
                $tableName = $this->_ehrTables->announcements;
                $fieldName = 'Announcement_Id';
                break;
	   
	   
	    
		
	     case 'Designation Roles':
                $tableName = $this->_ehrTables->roles;
                $fieldName = 'Designation_Id';
                break;
	    
			//Recruitment Module			
			case 'Job Post Requisition':
                $tableName = $this->_ehrTables->recruitment;
                $fieldName = 'Job_Code';
                break;
			
            //Organization Module
            case 'Locations':
                $tableName = $this->_ehrTables->location;
                $fieldName = 'Location_Id';
                break;
            
            case 'Departments':
                $tableName = $this->_ehrTables->dept;
                $fieldName = 'Department_Id';
                break;
            
            case 'Projects':
                $tableName = $this->_ehrTables->project;
                $fieldName = 'Project_Id';
                break;
            
            case 'Organization Profile':
                $tableName = $this->_ehrTables->orgProfile;
                $fieldName = 'OrgProfile_Id';
                break;
            
            case 'Organization Policies':
                $tableName = $this->_ehrTables->orgPolicies;
                $fieldName = 'OrgPolicy_Id';
                break;
            
            case 'Policy Types':
                $tableName = $this->_ehrTables->policyTypes;
                $fieldName = 'PolicyType_Id';
                break;
            
            case 'Work Schedule':
                $tableName = $this->_ehrTables->workSchedule;
                $fieldName = 'WorkSchedule_Id';
                break;
            
            case 'Workschedule Weekoff':
                $tableName = $this->_ehrTables->workScheduleWeekOff;
                $fieldName = 'WorkSchedule_Id';
                //$fieldName = 'Line_Item_Id';
                break;
            
            case 'Holidays':
                $tableName = $this->_ehrTables->holidayAssign;
                $fieldName = 'Holiday_Assign_Id';
                break;
            
            case 'Holiday':
                $tableName = $this->_ehrTables->holiday;
                $fieldName = 'Holiday_Id';
                break;
            
            case 'Tax Entities':
                $tableName = $this->_ehrTables->taxEntity;
                $fieldName = 'Tax_Entity_Id';
                break;
            
            case 'Employee Import':
                $tableName = $this->_ehrTables->employeeImport;
                $fieldName = 'Emp_Import_Id';
                break;
			
            case 'Leave Import':
                $tableName = $this->_ehrTables->leaveImport;
                $fieldName = 'Leave_Import_Id';
                break;
			
            case 'Form16 Import':
                $tableName = $this->_ehrTables->form16DataImport;
                $fieldName = 'Form16_Import_Id';
                break;
            
            case 'Contact Details':
                $tableName = $this->_ehrTables->orgContact;
                $fieldName = 'Contact_Id';
                break;
            
            case 'Organization Details':
                $tableName = $this->_ehrTables->orgDetails;
                $fieldName = 'Org_Code';
                break;
            
            case 'Mail Client Configuration':
                $tableName = $this->_ehrTables->mailConf;
                $fieldName = 'Mail_Account_Type';
                break;
            
            case 'Gratuity Settings':
                $tableName = $this->_ehrTables->gratuitySettings;
                $fieldName = 'Minimum_Tenure';
                break;
			
			case 'Tax Configuration':
                $tableName = $this->_ehrTables->taxConfiguration;
                $fieldName = 'Tax_Configuration_Id';
                break;
            
            case 'Employees' :
            $tableName = $this->_ehrTables->empPersonal;
            $fieldName = 'Employee_Id';
            break;
			
            case 'Designations':
                $tableName = $this->_ehrTables->designation;
                $fieldName = 'Designation_Id';
                break;
            
            case 'Grades':
                $tableName = $this->_ehrTables->empGrade;
                $fieldName = 'Grade_Id';
                break;
            
            case 'Employee Type':
                $tableName = $this->_ehrTables->empType;
                $fieldName = 'EmpType_Id';
                break;
            
            case 'Timesheets':
                $tableName = $this->_ehrTables->empTimesheet;
                $fieldName = 'Request_Id';
                break;
            
            case 'Timesheet Hours':
                $tableName = $this->_ehrTables->timesheetHrs;
                $fieldName = 'Timesheet_Id';
                break;
            
            case 'Timesheet Activities':
                $tableName = $this->_ehrTables->timesheetActivity;
                $fieldName = 'Project_Id';
                break;
            
            case 'Attendance':
                $tableName = $this->_ehrTables->attendance;
                $fieldName = 'Attendance_Id';
                break;
			
            case 'Attendance Settings':
                $tableName = $this->_ehrTables->attendanceSettings;
                $fieldName = 'Attendance_Settings_Id';
                break;
            
            case 'Assignments':
                $tableName = $this->_ehrTables->assignment;
                $fieldName = 'Task_Id';
                break;
            
            case 'Leaves':
                $tableName = $this->_ehrTables->empLeaves;
                $fieldName = 'Leave_Id';
                break;
            
            case 'Compensatory Off':
                $tableName = $this->_ehrTables->compOff;
                $fieldName = 'Compensatory_Off_Id';
                break;
            
            case 'Leave Types':
                $tableName = $this->_ehrTables->leavetype;
                $fieldName = 'LeaveType_Id';
                break;
	    
			case 'Leave Freeze':
					$tableName = $this->_ehrTables->leaveFreeze;
					$fieldName = 'LeaveFreeze_Id';
					break;
			
			case 'Short Time Off':
				$tableName = $this->_ehrTables->shortTimeOff;
				$fieldName = 'Short_Time_Off_Id';
				break;	
			
			case 'Permission Settings':
				$tableName = $this->_ehrTables->shortLeaveRequestSettings;
				$fieldName = 'Short_Leave_settings_Id';
				break;	
            
            case 'Transfer':
                $tableName = $this->_ehrTables->transfer;
                $fieldName = 'Transfer_Id';
                break;
            
            case 'Resignation':
                $tableName = $this->_ehrTables->resignation;
                $fieldName = 'Resignation_Id';
                break;
            
            case 'Employee Travel':
                $tableName = $this->_ehrTables->empTravels;
                $fieldName = 'Request_Id';
                break;
            
            case 'Warnings':
                $tableName = $this->_ehrTables->warnings;
                $fieldName = 'Warning_Id';
                break;
            
            case 'Memos':
                $tableName = $this->_ehrTables->memos;
                $fieldName = 'Memo_Id';
                break;
            
            case 'Awards':
                $tableName = $this->_ehrTables->awards;
                $fieldName = 'Award_Id';
                break;
            
            case 'Award Types':
                $tableName = $this->_ehrTables->awardtypes;
                $fieldName = 'AwardType_Id';
                break;
            
            case 'Complaints':
                $tableName = $this->_ehrTables->complaint;
                $fieldName = 'Complaint_Id';
                break;
			
			case 'Performance Assessment':
                $tableName = $this->_ehrTables->performance;
                $fieldName = 'Performance_Id';
                break;
            
            case 'Skill Definition':
                $tableName = $this->_ehrTables->skillDefinition;
                $fieldName = 'SkillDefinition_Id';
                break;
			
			case 'Skillset Assessment':
				$tableName = $this->_ehrTables->skillset;
                $fieldName = 'Skillset_Id';
                break;
			
			case 'Skill Level Association':
				$tableName = $this->_ehrTables->skillLevelAssoc;
                $fieldName = 'SkillDefinition_Id';
                break;
            
            // Payroll Module
			case 'Monthly Salary':
                $tableName = $this->_ehrTables->salary;
                $fieldName = 'Employee_Id';
                break;
			
			case 'Hourly Wages':
                $tableName = $this->_ehrTables->hourlyWages;
                $fieldName = 'Employee_Id';
                break;
            
            case 'Tax Slabs':
                $tableName = $this->_ehrTables->taxRates;
                $fieldName = 'Tax_Rate_Id';
                break;
            
            case 'Tax Sections':
                $tableName = $this->_ehrTables->taxSections;
                $fieldName = 'Tax_Section_ID';
                break;
            
            case 'Section Investments':
                $tableName = $this->_ehrTables->sectionsInvestment;
                $fieldName = 'Investment_Cat_Id';
                break;
            
            case 'Tax Exemptions':
                $tableName = $this->_ehrTables->taxExemptions;
                $fieldName = 'Tax_Exemption_ID';
                break;
            
            case 'Tax Rebates':
                $tableName = $this->_ehrTables->taxRebates;
                $fieldName = 'Tax_Rebate_ID';
                break;
			
            case 'Professional Tax':
                $tableName = $this->_ehrTables->professionalTax;
                $fieldName = 'PT_Slab_Id';
                break;
			
	    case 'PT Payment Tracker':
                $tableName = $this->_ehrTables->ptPaymentTracker;
                $fieldName = 'Pt_Payment_Tracker_Id';
                break;
	     
		case 'Organization Account':
				$tableName = $this->_ehrTables->organizationAccount;
				$fieldName = 'Organization_Account_Id';
				break;
		
            case 'Allowances':
                $tableName = $this->_ehrTables->allowances;
                $fieldName = 'Allowance_Id';
                break;
			
            case 'Allowance Types':
                $tableName = $this->_ehrTables->allowanceTypes;
                $fieldName = 'Allowance_Type_Id';
                break;
			
			case 'Adhoc Allowance':
				$tableName = $this->_ehrTables->adhocAllowance;
				$fieldName = 'Adhoc_Allowance_Id';
				break;	
            
            case 'Bonus':
                $tableName = $this->_ehrTables->empBonus;
                $fieldName = 'Bonus_Id';
                break;
            
            case 'Bonus Types':
                $tableName = $this->_ehrTables->bonusType;
                $fieldName = 'BonusType_Id';
                break;
            
            case 'Commission':
                $tableName = $this->_ehrTables->commission;
                $fieldName = 'Commission_Id';
                break;
			
			case 'Commission Percentage':
                $tableName = $this->_ehrTables->commissionPercentage;
                $fieldName = 'Commission_Type_Id';
                break;
            
            case 'Commission Types':
                $tableName = $this->_ehrTables->commissionTypes;
                $fieldName = 'Commission_Type_Id';
                break;
			
			case 'Emp Provident Fund':
                $tableName = $this->_ehrTables->empPF;
                $fieldName = 'Employee_Id';
                break;
			
			case 'Provident Fund':
                $tableName = $this->_ehrTables->orgPF;
                $fieldName = 'PfOrg_Id';
                break;
            
            case 'NPS':
                $tableName = $this->_ehrTables->orgEtf;
                $fieldName = 'ETFOrg_Id';
                break;
            
            case 'Emp ETF':
                $tableName = $this->_ehrTables->empEtf;
                $fieldName = 'Employee_Id';
                break;
            
            case 'ETF Payment Tracker':
                $tableName = $this->_ehrTables->orgEtfPaymentTracker;
                $fieldName = 'ETF_Payment_Tracker_Id';
                break;
            
			case 'PF Payment Tracker':
			//case 'Payment Tracker':
                $tableName = $this->_ehrTables->orgPfPaymentTracker;
                $fieldName = 'PF_Payment_Tracker_Id';
                break;
            
			case 'Insurance Payment Tracker':
			//case 'Payment Tracker Sub Grid':
                $tableName = $this->_ehrTables->orgInsPaymentTracker;
                $fieldName = 'Ins_Pay_Tracker_Id';
                break;
			
            case 'Deductions':
                $tableName = $this->_ehrTables->deductions;
                $fieldName = 'Deduction_Id';
                break;
            
            case 'Reimbursement':
                $tableName = $this->_ehrTables->reimbursement;
                //$fieldName = 'Line_Item_Id';
                $fieldName = 'Request_Id';
                break;
            
            case 'Expense Types':
                $tableName = $this->_ehrTables->expenseTypes;
                $fieldName = 'Expense_Id';
                break;
            
            case 'Advance Salary':
                $tableName = $this->_ehrTables->advanceSalary;
                $fieldName = 'AdvSalary_Id';
                break;
            
            case 'Loan':
                $tableName = $this->_ehrTables->empLoan;
                $fieldName = 'Loan_Id';
                break;
            
            case 'Deferred Loan':
                $tableName = $this->_ehrTables->deferredLoan;
                $fieldName = 'DeferredLoan_Id';
                break;
            
            case 'Loan Types':
                $tableName = $this->_ehrTables->loanType;
                $fieldName = 'LoanType_Id';
                break;
            
            case 'Loan Settings':
                $tableName = $this->_ehrTables->loanSettings;
                $fieldName = 'Loan_Amount_Threshold';
                break;
            
            case 'Shift Allowance':
                $tableName = $this->_ehrTables->empShift;
                $fieldName = 'Request_Id';
                break;
            
            case 'Shift Types':
                $tableName = $this->_ehrTables->shiftType;
                $fieldName = 'ShiftType_Id';
                break;
            
			case 'Fixed Insurance' :
                $tableName = $this->_ehrTables->fixedInsurance;
                $fieldName = 'Insurance_Id';
                break;
			
			case 'Insurance' :
                $tableName = $this->_ehrTables->variableInsurance;
                $fieldName = 'Insurance_Id';
                break;
			
            case 'Insurance Types':
                $tableName = $this->_ehrTables->insuranceType;
                $fieldName = 'InsuranceType_Id';
                break;
			
			case 'Tax Declarations':
                $tableName = $this->_ehrTables->taxDeclaration;
                $fieldName = 'Declaration_Id';
                break;
            
            case 'Premium Contribution':
                $tableName = $this->_ehrTables->insurancetypeGrade;
                $fieldName = 'Insurance_Grade_Id';
                break;
            
            case 'Final Settlement':
                $tableName = $this->_ehrTables->transactionBalance;
                $fieldName = 'Transaction_Id';
                break;
            
            case 'Gratuity':
                $tableName = $this->_ehrTables->gratuity;
                $fieldName = 'Gratuity_Id';
                break;
            
			case 'Tax Section Allowance Mapping':
                $tableName = $this->_ehrTables->taxSectionAllowance;
                $fieldName = 'Allowance_Type_Id';
                break;

			case 'Alert Settings':
                $tableName = $this->_ehrTables->alertSettings;
                $fieldName = 'Alert_Type_Id';
                break;
			
			case 'HRA Declarations':
                $tableName = $this->_ehrTables->landLord;
                $fieldName = 'Declaration_Id';
                break;
            
            case 'Perquisite Tracker':
                $tableName = $this->_ehrTables->prequisiteTrackerAmt;
                $fieldName = 'Line_Item_Id';
                break;
            
            case 'Document Template Engine':
                $tableName = $this->_ehrTables->docTempEngine;
                $fieldName = 'Document_Template_Id';
                break;
            
            case 'Template Custom Field':
                $tableName = $this->_ehrTables->tempCustomField;
                $fieldName = 'Custom_Field_Id';
                break;
			
			 case 'Custom Report':
                $tableName = $this->_ehrTables->customReport;
                $fieldName = 'Report_Id';
                break;
			
			 case 'Labour Welfare Fund':
                $tableName = $this->_ehrTables->labourWelfareFund;
                $fieldName = 'Labour_Welfare_Fund_Id';
                break;
			
			 case 'LWF Payment Tracker':
                $tableName = $this->_ehrTables->orgLwfPaymentTracker;
                $fieldName = 'LWF_Payment_Tracker_Id';
                break;
			
			 case 'Employees Document Upload':
                $tableName = $this->_ehrTables->empDocumentCategory;
                $fieldName = 'Document_Id';
                break;
                
            case 'Document Sub Type':
                $tableName = $this->_ehrTables->documentSubType;
                $fieldName = 'Document_Sub_Type_Id';
				break;

             case 'TDS History':
                $tableName = $this->_ehrTables->empTdsHistory;
                $fieldName = 'History_Id';
				break;

            case 'Fixed Health Insurance':
                $tableName = $this->_ehrTables->fixedHealthIns;
                $fieldName = 'Insurance_Id';
                break;
            
            case 'Fixed Health Insurance Type':
                $tableName = $this->_ehrTables->fixedHealthInsType;
                $fieldName = 'Insurance_Type_Id';
                break;

            case 'CustomGroupSettings': 
                $tableName = $this->_ehrTables->custGroupSettings; 
                $fieldName = 'Custom_Group_Settings_Id'; 
                break; 
 
            case 'CustomGroup': 
                    $tableName = $this->_ehrTables->custGroup; 
                    $fieldName = 'Custom_Group_Id'; 
                    break; 

            case 'ShiftScheduling': 
                    $tableName = $this->_ehrTables->shiftEmpMapping; 
                    $fieldName = 'Shift_Schedule_Id'; 
                    break; 
            
            case 'Payslip Template': 
                $tableName = $this->_ehrTables->payslipTemplate; 
                $fieldName = 'Template_Id'; 
                break; 

            case 'Device Management':
            $tableName = $this->_ehrTables->deviceManagement;
            $fieldName = 'Device_Id';
            break;

            case 'Clients' : 
                $tableName = $this->_ehrTables->clients;
                $fieldName = 'Client_Id';
                break;

            case 'Candidates' :
                $tableName = $this->_ehrTables->candidatePersonal;
                $fieldName = 'Candidate_Id';
            break;
        }
        
        return array('tableName' => $tableName, 'fieldName' => $fieldName);
    }
	
	/**
	 *	formCheckLock function used to set lock flag to that record if there is no lock.
	*/
	public function formCheckLock ($uniqueId, $sessionId, $formName)
	{
		$tableDet = $this->formDetails ($formName);
		
		$recordLimit = $this->checkOpenedRecordLimit($sessionId);
		
		if ($recordLimit[0])
		{
			$checkFormLock = $this->checkLockFlag($uniqueId, $tableDet['tableName'], $tableDet['fieldName']);
			
			if ($checkFormLock == $sessionId || $checkFormLock == 0)
			{
				$setLock = $this->setLockFlag($sessionId, $uniqueId, $tableDet['tableName'], $tableDet['fieldName']);
				
				if ($setLock)
				{
					return array('success'=>true);
				}
				else
				{
					return array('success' => false, 'msg'=>'Unable to set lock', 'type'=>'info');
				}
			}
			else
			{
				$editEmpName = $this->_dbPersonal->employeeName($checkFormLock);
				
				return array('success' => false, 'msg'=>$editEmpName['Employee_Name'] . ' is updating this record. Please Wait', 'type'=>'info');
			}
		}
		else
		{
			return array('success' => false, 'msg'=>"You don't have rights to edit more than $recordLimit[1] records.", 'type'=>'info');
		}
    }
    
    /**
     * 1. Validate the login employee is payroll admin or employee admin based on the input during the approval notification
     * 2. Return the eligible approvers for the given form name
     */
    public function validateFormLevelApprovers($formName,$loginEmpId,$adminFormName=''){
        $dbCommonFunction   = new Application_Model_DbTable_CommonFunction();
        $inputs = "{\"formName\":\"$formName\",\"adminFormName\":\"$adminFormName\"}";
        $response = $dbCommonFunction->executeCommonLibraryFunction(null, 'func', 'validateFormLevelApprovers', $inputs);
        return $response;
    }

    // Function to get the form access for the employee id and form name at the employee level
    public function getEmployeeLevelFormAccess($empId,$formName){
        $qryFormAccess =  $this->_db->select()
                    ->from(array('A'=>$this->_ehrTables->empAccessRights), array('View'=>'Role_View', 'Add'=>'Role_Add', 'Update'=>'Role_Update',
                                                                                'Delete'=>'Role_Delete', 'Optional_Choice'=>'Role_Optional_Choice',
                                                                                'Hr_Group'=>'Role_Hr_Group','Payroll_Group'=>'Role_Payroll_Group'))
                    ->joinInner(array('F'=>$this->_ehrTables->forms), 'F.Form_Id= A.Form_Id' , array())
                    ->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id=A.Employee_Id', 'Is_Manager')
                    ->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.Employee_Id=A.Employee_Id', array())
                    ->where('A.Employee_Id = ?', $empId)
                    ->where('F.Form_Name = ?', $formName)
                    ->where('J.Emp_Status Like ?', 'Active')
                    ->where('P.Form_Status = 1');
                        
        $formAccess = $this->_db->fetchRow($qryFormAccess);

        return $formAccess;
    }

    // Function to get the form access for the employee id and form name at the employee level
    public function getEmployeeLevelFormAccessBasedOnFormId($empId,$formId){
        $qryFormAccess =  $this->_db->select()
                    ->from(array('A'=>$this->_ehrTables->empAccessRights), array('View'=>'Role_View', 'Add'=>'Role_Add', 'Update'=>'Role_Update',
                                                                                'Delete'=>'Role_Delete', 'Optional_Choice'=>'Role_Optional_Choice',
                                                                                'Hr_Group'=>'Role_Hr_Group','Payroll_Group'=>'Role_Payroll_Group'))
                    ->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id=A.Employee_Id', 'Is_Manager')
                    ->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.Employee_Id=A.Employee_Id', array())
                    ->where('A.Employee_Id = ?', $empId)
                    ->where('A.Form_Id = ?', $formId)
                    ->where('J.Emp_Status Like ?', 'Active')
                    ->where('P.Form_Status = 1');
                        
        $formAccess = $this->_db->fetchRow($qryFormAccess);

        return $formAccess;
    }

    // Function to get the form access for the employee id and form name at the designation level
    public function getDesignationLevelFormAccess($empId,$formName){
        $qryFormAccess =  $this->_db->select()
        ->from(array('R'=>$this->_ehrTables->roles), array('View'=>'Role_View', 'Add'=>'Role_Add', 'Update'=>'Role_Update',
                                                        'Delete'=>'Role_Delete', 'Optional_Choice'=>'Role_Optional_Choice',
                                                        'Hr_Group'=>'Role_Hr_Group','Payroll_Group'=>'Role_Payroll_Group'))
        ->joinInner(array('F'=>$this->_ehrTables->forms), 'F.Form_Id= R.Form_Id' , array())
        ->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.Designation_Id=R.Designation_Id', array())
        ->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id=J.Employee_Id', 'Is_Manager')
        ->where('J.Emp_Status Like ?', 'Active')
        ->where('J.Employee_Id = ?', $empId)
        ->where('F.Form_Name = ?', $formName)
        ->where('P.Form_Status = 1');

        $formAccess = $this->_db->fetchRow($qryFormAccess);

        return $formAccess;
    }

    // Function to get the form access for the employee id and form name at the designation level
    public function getDesignationLevelFormAccessBasedOnFormId($empId,$formId){
        $qryFormAccess =  $this->_db->select()
        ->from(array('R'=>$this->_ehrTables->roles), array('View'=>'Role_View', 'Add'=>'Role_Add', 'Update'=>'Role_Update',
                                                        'Delete'=>'Role_Delete', 'Optional_Choice'=>'Role_Optional_Choice',
                                                        'Hr_Group'=>'Role_Hr_Group','Payroll_Group'=>'Role_Payroll_Group'))
        ->joinInner(array('F'=>$this->_ehrTables->forms), 'F.Form_Id= R.Form_Id' , array())
        ->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.Designation_Id=R.Designation_Id', array())
        ->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id=J.Employee_Id', 'Is_Manager')
        ->where('J.Emp_Status Like ?', 'Active')
        ->where('J.Employee_Id = ?', $empId)
        ->where('R.Form_Id = ?', $formId)
        ->where('P.Form_Status = 1');

        $formAccess = $this->_db->fetchRow($qryFormAccess);

        return $formAccess;
    }

    // Function to get the form access for the employee id and form name at the roles level
    public function getEmployeeRoleLevelFormAccess($empId,$formName){
        $qryFormAccess =  $this->_db->select()
        ->from(array('R'=>$this->_ehrTables->roleAccessControl), array('View'=>'Role_View', 'Add'=>'Role_Add', 'Update'=>'Role_Update',
                                                        'Delete'=>'Role_Delete', 'Optional_Choice'=>'Role_Optional_Choice',
                                                        'Hr_Group'=>'Role_Hr_Group','Payroll_Group'=>'Role_Payroll_Group'))
        ->joinInner(array('F'=>$this->_ehrTables->forms), 'F.Form_Id= R.Form_Id' , array())
        ->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.Roles_Id=R.Roles_Id', array())
        ->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id=J.Employee_Id', 'Is_Manager')
        ->where('J.Emp_Status Like ?', 'Active')
        ->where('J.Employee_Id = ?', $empId)
        ->where('F.Form_Name = ?', $formName)
        ->where('P.Form_Status = 1');

        $formAccess = $this->_db->fetchRow($qryFormAccess);

        return $formAccess;
    }

    // Function to get the form access for the employee id and form name at the roles level
    public function getEmployeeRoleLevelFormAccessBasedOnFormId($empId,$formId){
        $qryFormAccess =  $this->_db->select()
        ->from(array('R'=>$this->_ehrTables->roleAccessControl), array('View'=>'Role_View', 'Add'=>'Role_Add', 'Update'=>'Role_Update',
                                                        'Delete'=>'Role_Delete', 'Optional_Choice'=>'Role_Optional_Choice',
                                                        'Hr_Group'=>'Role_Hr_Group','Payroll_Group'=>'Role_Payroll_Group'))
        ->joinInner(array('F'=>$this->_ehrTables->forms), 'F.Form_Id= R.Form_Id' , array())
        ->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.Roles_Id=R.Roles_Id', array())
        ->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id=J.Employee_Id', 'Is_Manager')
        ->where('J.Emp_Status Like ?', 'Active')
        ->where('J.Employee_Id = ?', $empId)
        ->where('R.Form_Id = ?', $formId)
        ->where('P.Form_Status = 1');

        $formAccess = $this->_db->fetchRow($qryFormAccess);

        return $formAccess;
    }

    /** This function will be called when the lock flag exist for selected record in the table. In this function, it will be 
     * validated that whether the lock exist for that table name in the user session throw lock table for the lock flag-employee id. */
    public function validateSessionLockExistAndClearLock($lockFlagEmployeeId,$tableName,$fieldName,$uniqueId){
        $isSessionThrowLockExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->sessionLock, array('Session_Id'))
                                    ->where('Session_Id = ?', $lockFlagEmployeeId)
                                    ->where('Table_Name = ?', $tableName));
        if(!empty($isSessionThrowLockExist)){
            return 'edited';// return the record is edited as the table name exist for the lock flag-employee id
        }else{
            return 'not-edited';// return the record is edited as the table name exist for the lock flag-employee id
        }
    }

    public function __destruct()
    {
        
    }
}