<?php

class Employees_Model_DbTable_QuarterWiseLeave extends Zend_Db_Table_Abstract
{

    protected $_dbPersonal    = null;
    protected $_dbFinancialYr = null;
    protected $_dbComment     = null;
    protected $_orgDF         = null;
    protected $_db            = null;
    protected $_ehrTables     = null;
    protected $_dbJob         = null;
	protected $_dbCommonFun   = null;
    protected $_finYear       = null;
    protected $_dbServiceLeave   = null;

	
    public function init()
    {
        $this->_ehrTables     = new Application_Model_DbTable_Ehr();
        $this->_db            = Zend_Registry::get('subHrapp');
        $this->_dbPersonal    = new Employees_Model_DbTable_Personal();
        $this->_dbFinancialYr = new Default_Model_DbTable_FinancialYear();
        $this->_dbComment     = new Payroll_Model_DbTable_PayrollComment();
        $this->_dbJob         = new Employees_Model_DbTable_JobDetail();
        $this->_dbCommonFun   = new Application_Model_DbTable_CommonFunction();	
        $this->_dbServiceLeave = new Employees_Model_DbTable_ServiceBasedLeave();
		$this->_orgDF          = $this->_ehrTables->orgDateformat();
    }

   
    public function searchLeaveQuarter($leaveTypeId)
    {
        $qryLeaveQuarter = $this->_db->select()->from(array('LQ'=>$this->_ehrTables->leaveQuarter),
                                                    array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS LQ.Leave_Quarter_Id as count'),'LQ.Leave_Quarter_Id',
                                                    'LQ.Period','LQ.LeaveType_Id','LQ.Employment_Year_From','LQ.Employment_Year_To',
													'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', LQ.Leave_Quarter_Id)")))
																  
													->joinInner(array('PM'=>$this->_ehrTables->unit), 'PM.Target_Value = LQ.Period',
														array('Period'=>'PM.Unit_Tag'))

                                                    ->joinInner(array('LT'=>$this->_ehrTables->leavetype),"LT.LeaveType_Id = LQ.LeaveType_Id", array('LT.Leave_Enforcement_Configuration'))

                                                    ->where('LQ.LeaveType_Id = ?', $leaveTypeId);
                                                    
		$leaveQuarterDetails = $this->_db->fetchAll($qryLeaveQuarter);
       
        $iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
        
    /** It is to push the join from,join to & total days into the array**/
        foreach ($leaveQuarterDetails as $key => $row)
		{
            $empJoinQuarter=$row['Leave_Quarter_Id'];
            $leaveQuarterId=$empJoinQuarter;

    /** the function is called to get the details of the join from & to month**/
            $joinMonth = $this->searchLeaveJoinQuarter($leaveQuarterId);
            $leaveQuarterDetails[$key]['joinDetails'] = $joinMonth;      
        }		
          
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->leaveQuarter, new Zend_Db_Expr('COUNT(Leave_Quarter_Id)'))->where('LeaveType_Id = ?', $leaveTypeId));
                
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $leaveQuarterDetails);
    }

    public function searchLeaveJoinQuarter($leaveQuarterId )
    {
            $qryLeaveJoinQuarter = $this->_db->select()->from(array('JQ'=>$this->_ehrTables->leaveJoinQuarter),
                                               array('JQ.Total_Days'))                                            
																  								  
                                                    ->joinInner(array('FM'=>$this->_ehrTables->month),"FM.Month_Id = JQ.Join_From", array('FM.Month_Name as JoinFromMonth'))
									  
                                                    ->joinInner(array('TM'=>$this->_ehrTables->month),"TM.Month_Id = JQ.Join_To", array('TM.Month_Name as JoinToMonth'))
									    
                                                    
                                                    ->where('JQ.Leave_Quarter_Id = ?', $leaveQuarterId)
                                                    ->group('JQ.Join_From','LQ.Join_To')
                                                    ->order('Join_From ASC');
																				
		$leaveJoinQuarterDetails = $this->_db->fetchAll($qryLeaveJoinQuarter); 
		
      return  $leaveJoinQuarterDetails;
}


    public function updateLeaveQuarter($leaveQuarter,$leaveJoinQuarter,$sessionId, $formName)
    {
   
        $qryLeaveQuarterSlab = $this->_db->select()->from($this->_ehrTables->leaveQuarter, new Zend_Db_Expr('COUNT(Leave_Quarter_Id)'))
                                ->where('LeaveType_Id = ?', $leaveQuarter['LeaveType_Id'])
                                ->where('Employment_Year_From < ?',$leaveQuarter['Employment_Year_To'])
                                ->where('Employment_Year_To  >= ?', $leaveQuarter['Employment_Year_To']);


                               

        if (!empty($leaveQuarter['Leave_Quarter_Id']))
		{
			$qryLeaveQuarterSlab->where('Leave_Quarter_Id != ?', $leaveQuarter['Leave_Quarter_Id']);
		}
            $leaveQuarterExists =$this->_db->fetchOne($qryLeaveQuarterSlab);

            if(empty($leaveQuarterExists))
          {
            if(!empty($leaveQuarter['Leave_Quarter_Id']))
            {
                    $action = 'Edit';
                    $updated = $this->_db->update($this->_ehrTables->leaveQuarter, $leaveQuarter, array('Leave_Quarter_Id = '.$leaveQuarter['Leave_Quarter_Id']));
                                   
                    $deleted = $this->_db->delete($this->_ehrTables->leaveJoinQuarter, 'Leave_Quarter_Id ='.(int)$leaveQuarter['Leave_Quarter_Id']);
                    $updated =  $this->_ehrTables->insertMultiple($this->_ehrTables->leaveJoinQuarter, $leaveJoinQuarter);

                    $leaveQuarterId = $leaveQuarter['Leave_Quarter_Id'];   
                    $updated   = $this->getEmploymentYearRange($leaveQuarterId,$leaveQuarter);
                                   
                }
            else
            {
                    $action = 'Add';
                    $updated =  $this->_db->insert($this->_ehrTables->leaveQuarter, $leaveQuarter);

                    if( $updated )
                    {
                        $leaveQuarterId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->leaveQuarter,new Zend_Db_Expr('MAX(Leave_Quarter_Id)')));
                                    
                            foreach($leaveJoinQuarter as $key=>$row)
                            {
                                $leaveJoinQuarter[$key]['Leave_Quarter_Id'] = $leaveQuarterId;	
                            }
                                
                         $updated =  $this->_ehrTables->insertMultiple($this->_ehrTables->leaveJoinQuarter, $leaveJoinQuarter);
                     }

                     $leaveQuarterId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->leaveQuarter,new Zend_Db_Expr('Max(Leave_Quarter_Id)')));
                     $updated   = $this->getEmploymentYearRange($leaveQuarterId,$leaveQuarter);
            }
            $updated=1;

            $result = $this->_dbCommonFun->updateResult (array('updated'    => $updated,
                                'action'         => $action,
                                'trackingColumn' => $leaveQuarter['Leave_Quarter_Id'],
                                'formName'       => $formName,
                                'sessionId'      => $sessionId,
                                'tableName'      => $this->_ehrTables->leaveQuarter));                        
                                
             return $result;
       }
        else
        {
                return array('success'=>false, 'msg'=>'Leave Quarter Period is Already Exist', 'type'=>'info');
        }

    }

    
    /** to print the employeejoinfrom & employeejointo month automatically in quarter wise leave form **/
    public function getEmployeeJoinFromTo()
    {
        $qrygetLeaveClosureMonth=$this->_db->select()->from(array('L'=>$this->_ehrTables->leavetype),array('Leave_Closure_Month'))
                                		->where('L.Leave_Enforcement_Configuration =?',3);
		
        $rowLeaveClosureMonth = $this->_db->fetchOne($qrygetLeaveClosureMonth);
     
        if( $rowLeaveClosureMonth==12)
        {
            $leaveClosureMonth =1;
        }
        else
        {
            $leaveClosureMonth =$rowLeaveClosureMonth+1;
        }

        $quarterPeriod =3;
        $rowCount = (12/ $quarterPeriod);
        $empMonthArray  = array('Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec');
    
        $empJoinFromMonth = $leaveClosureMonth - 1;
        $empJoinToMonth = 0;
        $differenceMonth = $quarterPeriod  - 1;
       
        $empDataSetArray = array();

        for($i=1;$i<=$rowCount;$i++)
        {
            if ($empJoinFromMonth == 0 && $empJoinToMonth == 0 && $i!=1)
            {
                $empJoinFromMonth = $empJoinToMonth + 1;
            }
            else
            {
                if ($empJoinToMonth >= 11)
                {
                    $empJoinFromMonth = $empJoinToMonth - 11;
                }
                else if ($empJoinToMonth >= 0 && $i!=1)
                {
                    $empJoinFromMonth = $empJoinToMonth + 1;
                }
            }
            
            $empJoinToMonth = $empJoinFromMonth + $differenceMonth;
            
            if ($empJoinToMonth > 11)
            {
                $empJoinToMonth = $empJoinToMonth - 12;
            }
            array_push($empDataSetArray,$i,$empMonthArray[$empJoinFromMonth], $empMonthArray[$empJoinToMonth],($empJoinFromMonth+1),($empJoinToMonth+1));

            $empDataSetArrayChunk= array_chunk($empDataSetArray,5);
            
        }

        return  $empDataSetArrayChunk;
    }



    public function getEmploymentYearRange($leaveQuarterId,$leaveQuarter)
    {
        $employeeEligibleLeave  = array();
        $dbLeave                = new Employees_Model_DbTable_Leave();
        if(!empty($leaveQuarter['LeaveType_Id']))
        {
            $updated            = $dbLeave->empDOJUpdateEligibleDays(NULL,'update-quarter-wise-leave',NULL,$leaveQuarter['LeaveType_Id'],0,'Yes');
        }
        else
        {
            $updated =  0;
        }
        return $updated;
    }

    public function deleteEligibleQuarterLeave($quarterId,$leaveTypeId)
    {
        $quarterDetailQry =$this->_db->select()->from($this->_ehrTables->leaveQuarter,array('Employment_Year_From','Employment_Year_To','Leave_Quarter_Id'))
                         ->where('Leave_Quarter_Id = ?',$quarterId);
        $quarterDetail = $this->_db->fetchRow($quarterDetailQry);
      
        $dbLeave                = new Employees_Model_DbTable_Leave();
        $leaveTypeDetails       = $dbLeave->getLeaveTypeRow($leaveTypeId);
        $activeEmployeeDetails  = $dbLeave->getActiveEmployeesDetails($leaveTypeDetails);
        
        $leaveJoinQry =$this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->leaveJoinQuarter,array('Leave_Quarter_Id'))
                        ->where('Leave_Quarter_Id = ?', $quarterId));


        $maxEmploymentFrom = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->leaveQuarter,array(new Zend_Db_Expr('Max(Employment_Year_From)')))
                            ->where('LeaveType_Id = ?',$leaveTypeId));
        if($maxEmploymentFrom == $quarterDetail['Employment_Year_From']) 
        {  
            foreach($activeEmployeeDetails as $activeEmployee)
            {
                $employmentYear = $activeEmployee['Employment_Year'];
               
                if($quarterDetail['Employment_Year_From'] < $employmentYear && ($quarterDetail['Employment_Year_To'] >=$employmentYear || is_null($quarterDetail['Employment_Year_To'])))
                {
                        $whereCondition['Employee_Id = ?']  = $activeEmployee['Employee_Id'];
                        $whereCondition['LeaveType_Id = ?'] = $activeEmployee['LeaveType_Id'];
                        $deleted=$this->_db->delete($this->_ehrTables->empEligbleLeave,$whereCondition);
                       
                }
            }
            if($leaveJoinQry==$quarterDetail['Leave_Quarter_Id'])
            {
                $wCondition['Leave_Quarter_Id = ?'] = $quarterId;
                $deleted=$this->_db->delete($this->_ehrTables->leaveJoinQuarter,$wCondition);
            }
            $deleted = $this->_db->delete($this->_ehrTables->leaveQuarter, 'Leave_Quarter_Id='.(int)$quarterId);   
            return $deleted;
        }
        else
        {
            return 0;
        } 
    }

    public function __destruct()
    {
        
    }	
}


