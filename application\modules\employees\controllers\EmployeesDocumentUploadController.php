<?php

include APPLICATION_PATH."/validations/Validations.php";

class Employees_EmployeesDocumentUploadController extends Zend_Controller_Action
{

    protected $_dbEmployeesDocumentUpload = null;

    protected $_dbAccessRights = null;

    protected $_logEmpId = null;

    protected $_dbCommonFun = null;

    protected $_validation = null;

    protected $_orgDateFormat = null;

    protected $_ehrTables = null;

    protected $_formName = 'Employees Document Upload';

    protected $_subFormName = 'Document Sub Type';

    protected $_employeesDocumentUploadAccessRights = null;

    protected $_documentSubTypeAccessRights = null;

	protected $_hrappMobile = null;
	
	protected $_orgDetails                      = null;

    public function init()
    {
		$this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
		if ($this->_hrappMobile->checkAuth())
        {
			$this->_dbCommonFun    		= new Application_Model_DbTable_CommonFunction();
			$userSession        = $this->_dbCommonFun->getUserDetails ();
			$this->_logEmpId    = $userSession['logUserId'];
			
            $this->_dbEmployeesDocumentUpload = new Employees_Model_DbTable_EmployeesDocumentUpload();
            $this->_dbAccessRights 		= new Default_Model_DbTable_AccessRights();
            
            $this->_dbPersonal       = new Employees_Model_DbTable_Personal();
			$this->_orgDetails       = Zend_Registry::get('orgDetails');
			$this->_employeesDocumentUploadAccessRights   = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formName);
			$this->_employeesDocumentUploadEmployeeAccess = $this->_employeesDocumentUploadAccessRights['Employee'];	
			
			$this->_documentSubTypeAccessRights   = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_subFormName);
			$this->_documentSubTypeEmployeeAccess = $this->_documentSubTypeAccessRights['Employee'];	
			
            $this->_validation 	   		= new Validations();
			$this->_ehrTables   		= new Application_Model_DbTable_Ehr();
			
            $userSession             = $this->_dbCommonFun->getUserDetails ();
            $this->_logEmpId         = $userSession['logUserId']; 
        }
        else
        {
            if (Zend_Session::namespaceIsset('lastRequest'))
                Zend_Session::namespaceUnset('lastRequest');
            
            $session = new Zend_Session_Namespace('lastRequest');
            $session->lastRequestUri = 'employees/employees-document-upload';
            $this->_redirect('auth');
		}
    }

    public function indexAction()
    {
		$checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

		if ($checkSessionAuth)
		{
			$this->_helper->layout()->disableLayout()->setLayout('admin_layout');
        
			$this->view->formName = $this->_formName;
			
			$this->view->subFormName = $this->_subFormName;
	
			$this->view->customFormName = $this->_ehrTables->getCustomForms($this->_formName);
			
			$this->view->customSubFormName = $this->_ehrTables->getCustomForms($this->_subFormName);
			
			$this->view->category	    = $this->_dbEmployeesDocumentUpload->getCategory();
			
			$this->view->restrictEmpAccessForManager = $this->_orgDetails['Restrict_Emp_Access_For_Manager'];
	
			$this->view->employeeName   = $this->_dbPersonal->employeeDetail('', '', 'Emp_First_Name', 'ASC', '', '','', '',$this->_logEmpId,
																	'', '', '', '', 'Employees Document Upload', '', 1, 1, '');
			
			$this->view->employeesDocumentUploadUser =  array('Is_Manager'=> $this->_employeesDocumentUploadAccessRights['Employee']['Is_Manager'],
													'View'      => $this->_employeesDocumentUploadAccessRights['Employee']['View'],
													'Update'	=> $this->_employeesDocumentUploadAccessRights['Employee']['Update'],
													'Add'		=> $this->_employeesDocumentUploadAccessRights['Employee']['Add'],
													'Delete'	=> $this->_employeesDocumentUploadAccessRights['Employee']['Delete'],												
													'Admin'		=> $this->_employeesDocumentUploadAccessRights['Admin'],
													'Employee_Name' => $this->_dbPersonal->employeeId($this->_logEmpId),
													'logEmpId'     => $this->_logEmpId);
	
			$this->view->documentSubTypeUser = array('Is_Manager'=> $this->_documentSubTypeAccessRights['Employee']['Is_Manager'],
													'View'      => $this->_documentSubTypeAccessRights['Employee']['View'],
													'Update'	=> $this->_documentSubTypeAccessRights['Employee']['Update'],
													'Add'		=> $this->_documentSubTypeAccessRights['Employee']['Add'],
													'Delete'	=> $this->_documentSubTypeAccessRights['Employee']['Delete'],												
													'Admin'		=> $this->_documentSubTypeAccessRights['Admin'],
													'Employee_Name' => $this->_dbPersonal->employeeId($this->_logEmpId),
													'logEmpId'     => $this->_logEmpId);
		
		} else {
			$this->_redirect('auth');
		}
    }

    public function listEmployeesDocumentUploadAction()
    {
        $this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('list-employees-document-upload', 'json')->initContext();
			
			if($this->_employeesDocumentUploadEmployeeAccess['View']==1)
			{
				$sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
	
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);				
				
				$userDetails =  array('Is_Manager'=> $this->_employeesDocumentUploadAccessRights['Employee']['Is_Manager'],
												'View'      => $this->_employeesDocumentUploadAccessRights['Employee']['View'],
												'Update'	=> $this->_employeesDocumentUploadAccessRights['Employee']['Update'],
												'Add'		=> $this->_employeesDocumentUploadAccessRights['Employee']['Add'],
												'Delete'	=> $this->_employeesDocumentUploadAccessRights['Employee']['Delete'],
												'Admin'		=> $this->_employeesDocumentUploadAccessRights['Admin'],
												'Employee_Name' => $this->_dbPersonal->employeeId($this->_logEmpId),
												'logEmpId'     => $this->_logEmpId);
	
				$this->view->result = $this->_dbEmployeesDocumentUpload->listEmployeesDocumentUpload($page, $rows, $sortField, $sortOrder,
												$searchAll,$userDetails, $this->_formName);
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees-document-upload', 'employees');
        }
    }

    public function updateEmployeesDocumentUploadAction()
    {
		$this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('update-employees-document-upload', 'json')->initContext();
			
            $documentId = $this->_getParam('documentId', null);
			$documentId = filter_var($documentId, FILTER_SANITIZE_NUMBER_INT);
                         
			if ( $this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				
				if ( $this->_employeesDocumentUploadEmployeeAccess['Update'] == 1  || $this->_employeesDocumentUploadEmployeeAccess['Add'] == 1 )
				{
					$customFormName = $this->_ehrTables->getCustomForms($this->_formName);
					$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formName);
					
					$employeeId            = $this->_validation->intValidation($formData['employeeId']);
					$documentSubType	   = $this->_validation->intValidation($formData['documentSubType']);
					$documentName          = $this->_validation->alphaNumSpValidation($formData['documentName']);
					$documentName['valid'] = $this->_validation->lengthValidation($documentName,1,50,true);					
						
					$employeesDocumentUploadFilesArr = array();				
					
					if(!empty($formData['declarationFiles']))
					{
						$uploadPathExists = 1;
						
						foreach($formData['declarationFiles'] as $key=>$row){
							$employeesDocumentUploadFilesArr[$key]['File_Name'] = $row['Name'];
							$employeesDocumentUploadFilesArr[$key]['File_Size'] = $row['Size'];
						}							
					}
					else
					{
						if(empty($documentName['value']))
						{
							$uploadPathExists = 0;	
						}
						else
						{
							$checkEmpUploadFilesExists = $this->_dbEmployeesDocumentUpload->checkEmpUploadFilesExists($documentId);
							$uploadPathExists = !empty($checkEmpUploadFilesExists) ? 1 : 0;
						}
					}
					
					if(!empty($uploadPathExists))
					{
						if ($employeeId['valid'] &&!empty($documentSubType['value']) && $documentSubType['valid']
						&& !empty($documentName['value']) && $documentName['valid']  )
						{	
							$employeesDocumentUploadDetails = array(
														  'Employee_Id'    		=> $employeeId['value'],
														  'Document_Sub_Type_Id'=> $documentSubType['value'],
														  'Document_Name'   	=> $documentName['value']);                    
							
							$this->view->result = $this->_dbEmployeesDocumentUpload->updateEmployeesDocumentUpload($employeesDocumentUploadDetails,$employeesDocumentUploadFilesArr,$documentId, $this->_logEmpId, $formName);
						}
						else
						{
							$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
						}
					}
					else
					{
						$this->view->result = array('success' => false, 'msg' => 'Please Upload the Document(s)', 'type' => 'info');
					}
				}
				else
				{
					$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'info');
				}
			}
        }
		else
		{
			$this->_helper->redirector('index', 'employee-document-upload', 'employees');
		}
    }

    public function deleteEmployeesDocumentUploadAction()
    {
        $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-employees-document-upload', 'json')->initContext();
			
			if ($this->getRequest()->isPost())
			{
				if($this->_employeesDocumentUploadEmployeeAccess['Delete'] == 1)
				{
					$formData = $this->getRequest()->getPost();
					
					$documentId = $this->_validation->intValidation($formData['documentId']);
					
					/** To check that documents exists in table for the emp **/
					$docExists = $this->_validation->intValidation($formData['docExists']);
					
					if (!empty($documentId['value']) && $documentId['valid'] && $docExists['valid'])
					{		
						$customFormName = $this->_ehrTables->getCustomForms($this->_formName);
						$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formName);
							
						$this->view->result = $this->_dbEmployeesDocumentUpload->deleteEmployeesDocumentUpload($documentId['value'], $docExists['value'], $formData['deleteDocs'], $this->_logEmpId, $formName);
					}
					else
					{
						$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
					}
				}
				else
				{
					$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'info');
				}
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employees-document-upload', 'employees');
		}
    }

    /**
     * sub grid list
     */
    public function employeesDocumentUploadSubGridAction()
    {
		$this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('employees-document-upload-sub-grid', 'json')->initContext();
            
			$sortField = $this->_getParam('iSortCol_0', null);
			$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
			
			$sortOrder = $this->_getParam('sSortDir_0', null);
			$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
			
			$page = $this->_getParam('iDisplayStart', null);
			$page = isset($page) ? intval($page) : 0;
			$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
			
			$rows = $this->_getParam('iDisplayLength', null);
			$rows = isset($rows) ? intval($rows) : 10;
			$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
			
			$employeeId = $this->_getParam('employeeId', null);
			$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
			
			$this->view->result = $this->_dbEmployeesDocumentUpload->searchEmployeeData($page, $rows, $sortField, $sortOrder,
																						$employeeId);
		}
		else
		{
			$this->_helper->redirector('index', 'employees-document-upload', 'employees');
		}
    }

    /**
     * delete only the documents in edit form
     */
    public function removeUploadedFilesAction()
    {
		$this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('remove-uploaded-files', 'json')->initContext();
			
            $documentId = $this->_getParam('documentId');
    	    $documentId = $this->_validation->intValidation($documentId);
			
			$employeesDocumentUploadFileName = $this->_getParam('_employeesDocumentUploadFileName');
			
			if(!empty($documentId['value']) && !empty($employeesDocumentUploadFileName)){
				$this->view->result = $this->_dbEmployeesDocumentUpload->deleteEmployeesDocumentUploadFiles($documentId['value'],$employeesDocumentUploadFileName);
			}
			else{
				$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
			}	
        }
		else{
			$this->_helper->redirector('index', 'employees-document-upload', 'employees');
		}
    }

    /**
     * To List Document Type in Dropdown
     */
    public function listDocumentTypeAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-document-type', 'json')->initContext();
			
            $categoryId = $this->_getParam('categoryId');
    	    $categoryId = $this->_validation->intValidation($categoryId);
			
			if(!empty($categoryId['value']) && $categoryId['valid'])
			{
				$this->view->result = $this->_dbEmployeesDocumentUpload->getDocumentTypes($categoryId['value']);
			}
			else{
				$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
			}	
        }
		else{
			$this->_helper->redirector('index', 'employees-document-upload', 'employees');
		}
    }

    /**
     * To List Document Sub Type in Dropdown
     */
    public function listDocumentSubTypeAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-document-sub-type', 'json')->initContext();
			
            $docTypeId = $this->_getParam('docTypeId');
    	    $docTypeId = $this->_validation->intValidation($docTypeId);
			
			if(!empty($docTypeId['value']) && $docTypeId['valid'] )
			{
				$documentSubTypeList = $this->_dbEmployeesDocumentUpload->getDocumentSubTypes($docTypeId['value']);
				$this->view->result = array('success' => true, 'documentSubTypeList' => $documentSubTypeList, 'msg' => '', 'type' => '');
			}
			else{
				$this->view->result = array('success' => false, 'documentSubTypeList' => '', 'msg' => 'Invalid document type', 'type' => 'info');
			}
        }
		else{
			$this->_helper->redirector('index', 'employees-document-upload', 'employees');
		}
    }

    /**
     * To list Document sub types in Grid
     */
    public function listEmployeeDocumentSubTypeAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('list-employee-document-sub-type', 'json')->initContext();
			
			if($this->_documentSubTypeEmployeeAccess['View']==1)
			{
				$sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
	
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
	
				$this->view->result = $this->_dbEmployeesDocumentUpload->listEmployeeDocumentSubType($page, $rows, $sortField, $sortOrder,
												$searchAll, $this->_logEmpId, $this->_subFormName);
			}	
        }
		else{
			$this->_helper->redirector('index', 'employees-document-upload', 'employees');
		}
    }

    /**
     * To Update Document Sub Type
     */
    public function updateDocumentSubTypeAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('update-document-sub-type', 'json')->initContext();

			$subFormName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_subFormName);

			$documentSubTypeId = $this->_getParam('documentSubTypeId');
			$documentSubTypeId = $this->_validation->intValidation($documentSubTypeId);
			
			$documentTypeId = $this->_getParam('documentTypeId');
			$documentTypeId = $this->_validation->intValidation($documentTypeId);
			
			$documentSubTypeName          = $this->_getParam('documentSubTypeName');
			$documentSubTypeName          = $this->_validation->alphaNumSpCDotHySlashValidation($documentSubTypeName);
			$documentSubTypeName['valid'] = $this->_validation->lengthValidation($documentSubTypeName,1,100,true);

			if(($this->_documentSubTypeEmployeeAccess['Add']==1 && $documentSubTypeId['value'] == 0) || ($this->_documentSubTypeEmployeeAccess['Update']==1 && $documentSubTypeId['value'] > 0))
			{
				if($documentSubTypeId['value'] == 0 )
				{
					$documentSubTypeId['value'] = "";
				}

				if($documentTypeId['valid'] && $documentSubTypeName['valid'])
				{
					$documentSubTypeDetails = array(
						'Document_Sub_Type_Id' => $documentSubTypeId['value'],
						'Document_Type_Id'     =>  $documentTypeId['value'],
						'Document_Sub_Type'    =>  $documentSubTypeName['value']);
					
					$this->view->result = $this->_dbEmployeesDocumentUpload->updateDocumentSubType($documentSubTypeDetails, $this->_logEmpId, $subFormName);

				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
				}	
			}
		}
		else
		{
			$this->_helper->redirector('index', 'employees-document-upload', 'employees');
		}
    }

	/**
	 * Delete Document Sub Type
	 */
    public function deleteDocumentSubTypeAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('delete-document-sub-type', 'json')->initContext();

			$documentSubTypeId = $this->_getParam('documentSubTypeId');
			$documentSubTypeId = $this->_validation->intValidation($documentSubTypeId);

			if($this->_documentSubTypeEmployeeAccess['Delete']==1 )
			{

				if($documentSubTypeId['value'] > 0 && $documentSubTypeId['valid'])
				{
					$this->view->result = $this->_dbEmployeesDocumentUpload->deleteDocumentSubType($documentSubTypeId['value'], $this->_logEmpId, $this->_subFormName);
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Unable to delete document sub type', 'type' => 'info');
				}
			}

		}
		else
		{
			$this->_helper->redirector('index', 'employees-document-upload', 'employees');
		}
    }

	public function __destruct()
    {
        
    }
}





