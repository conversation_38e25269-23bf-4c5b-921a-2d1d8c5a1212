<?php
//=========================================================================================
//=========================================================================================
/* Program : PerformanceAssessment.php											         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MYSQL query for performance assessment        						     *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        16-Oct-2013    Mahesh                  Initial Version                    *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Dhanabal                Changes in file for mobile app     *
 *                                                    1.Extra fields are added in        *
 *                                                    field list of list query.          *
 *                                                                                       *
 *  1.5        05-Mar-2016    Suresh               Changed in file for Bootstrap         *
 *                                                                                       */
//=========================================================================================
//=========================================================================================
class Employees_Model_DbTable_PerformanceAssessment extends Zend_Db_Table_Abstract
{

    protected $_db = null;

    protected $_dbPersonal = null;

    protected $_ehrTables = null;

    protected $_orgDF = null;

    protected $_dbComment = null;
    
    protected $_eftConfiguration = null;
    
    protected $_commonFunction = null;

    protected $_orgDetails    = null;
    
    public function init()
    {
        $this->_ehrTables = new Application_Model_DbTable_Ehr();
        $this->_db = Zend_Registry::get('subHrapp');
        $this->_dbPersonal = new Employees_Model_DbTable_Personal();
        $this->_orgDF = $this->_ehrTables->orgDateformat();
        $this->_dbComment = new Payroll_Model_DbTable_PayrollComment();
        $this->_eftConfiguration =new Organization_Model_DbTable_EftConfiguration();
        $this->_commonFunction = new Application_Model_DbTable_CommonFunction();
        if (Zend_Registry::isRegistered('orgDetails'))
			$this->_orgDetails = Zend_Registry::get('orgDetails');
    }
    
     public function searchPerformanceAssessment($page,$rows,$sortField,$sortOrder,$searchAll=null,$searchDetails,$userDetails)
    {
        switch ($sortField)
        {
            default:
                case 1: $sortField = 'EJ.User_Defined_EmpId'; break;            
                case 2: $sortField = 'EP2.Emp_First_Name'; break; 
                case 3: $sortField = 'EP3.Emp_First_Name'; break;                 
                case 4: $sortField = 'P.Assessment_From'; break;
                case 5: $sortField = 'P.Assessment_To'; break;
				case 6: $sortField = 'P.Performance_Status'; break;
        }
	
        if (!empty($userDetails['Form_Name']))
        {
        	$formId = $this->_dbComment->getFormId($userDetails['Form_Name']);
        	 
        	$qryComment = $this->_db->select()->distinct()->from(array('Cm'=>$this->_ehrTables->comment), 'Parent_Id')
												->where('Cm.Parent_Id = P.Performance_Id AND Cm.Form_Id='.$formId);
        }
	
	
        $qryPerformanceAssessment = $this->_db->select()
                                              ->from(array('P'=>$this->_ehrTables->performance),
                                                    array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS P.Performance_Id as count'),
                                                                  'P.Performance_Id','P.Employee_Id','P.Designation_Id','P.Approver_Id','P.Second_Level_Approver',
                                                                  'P.Achievement','P.Training_Required',
                                                    new Zend_Db_Expr("DATE_FORMAT(P.Added_On,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
                                                    new Zend_Db_Expr("DATE_FORMAT(P.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On"),
                                                    new Zend_Db_Expr("date_format(P.Assessment_From, '%b, %Y') as AssessmentFrom"),
                                                    new Zend_Db_Expr("date_format(P.Assessment_From, '%M,%Y') as Assessment_From"),
                                                    new Zend_Db_Expr("date_format(P.Assessment_To, '%M,%Y') as Assessment_To"),
                                                    new Zend_Db_Expr("date_format(P.Assessment_To, '%b, %Y') as AssessmentTo"),
                                                                  'P.Performance_Status','P.Added_By as Added_By_Id',
                                                    'Comment'=> new Zend_Db_Expr('('.$qryComment.')'),
                                                    'Log_Id'=>new Zend_Db_Expr($userDetails['Session_Id']),
                                                    'Role'=>new Zend_Db_Expr("'".$userDetails['Admin']."'"),
                                                    'DT_RowClass' => new Zend_Db_Expr('"performanceAssessment"'),
                                                    'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', P.Performance_Id)")))
                                           
                                            ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'P.Employee_Id=EJ.Employee_Id',
                                            array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN EP2.Employee_Id ELSE EJ.User_Defined_EmpId END')))
                                                    
                                            ->joinInner(array('EP'=>$this->_ehrTables->empPersonal),'P.Added_By=EP.Employee_Id',
                                                        array(new Zend_Db_Expr("CONCAT(EP.Emp_First_Name, ' ', EP.Emp_Last_Name) as Added_By")))
                                                    
                                            ->joinLeft(array('EP1'=>$this->_ehrTables->empPersonal),'P.Updated_By=EP1.Employee_Id',
                                                        array(new Zend_Db_Expr("CONCAT(EP1.Emp_First_Name, ' ', EP1.Emp_Last_Name) as Updated_By")))
                                            
                                            ->joinLeft(array('EP2'=>$this->_ehrTables->empPersonal),'P.Employee_Id=EP2.Employee_Id',
                                            		    array(new Zend_Db_Expr("CONCAT(EP2.Emp_First_Name, ' ', EP2.Emp_Last_Name) as Employee_Name")))
                                            
                                            ->joinLeft(array('EP3'=>$this->_ehrTables->empPersonal),'P.Approver_Id=EP3.Employee_Id',
                                                        array(new Zend_Db_Expr("CONCAT(EP3.Emp_First_Name, ' ', EP3.Emp_Last_Name) as Approver_Name")))
                                                        
                                            ->joinLeft(array('EP4'=>$this->_ehrTables->empPersonal),'P.Second_Level_Approver=EP4.Employee_Id',
                                            		    array(new Zend_Db_Expr("CONCAT(EP4.Emp_First_Name, ' ', EP4.Emp_Last_Name) as Second_Approver_Name")))            
                                              
                                            
                    						->group('P.Performance_Id')
                                            ->order("$sortField $sortOrder")
                                            ->limit($rows, $page);        

        if(empty($userDetails['Admin']))
        {
            $getEmployeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
												  ->where('Manager_Id = ?', $userDetails['Session_Id']));
            if($userDetails['Is_Manager'] == 1 && !empty($getEmployeeId))
			{
				if($this->_orgDetails['Immediate_Reportees_View_Only']==0)
				{
					$getEmployeeId = $this->_commonFunction->getMultiLevelManagerIds($userDetails['Session_Id'],1);
					array_push($getEmployeeId,$userDetails['Session_Id']);
					$qryPerformanceAssessment->where('P.Approver_Id IN (?)', $getEmployeeId)
                                            ->orwhere('P.Second_Level_Approver IN (?)', $getEmployeeId)
                                            ->orwhere('P.Employee_Id = ?', $userDetails['Session_Id']);
				}
				else
				{
					$qryPerformanceAssessment->where('P.Employee_Id = :EmpId or P.Approver_Id = :EmpId or P.Second_Level_Approver = :EmpId or P.Employee_Id IN (?)', $getEmployeeId)
																							   ->bind(array('EmpId'=>$userDetails['Session_Id']));
				}
            }
            else
			{
                $qryPerformanceAssessment->where('P.Employee_Id = ?', $userDetails['Session_Id']);
            }
        }
        
        if (!empty($searchAll) && $searchAll != null)
		{
            $conditions = $this->_db->quoteInto(new Zend_Db_Expr('Concat(EP2.Emp_First_Name," ",EP2.Emp_Last_Name) Like ?'),"%$searchAll%");
            $conditions .= $this->_db->quoteInto('or P.Performance_Status Like ?', "%$searchAll%");
            $conditions .= $this->_db->quoteInto('or EJ.User_Defined_EmpId Like ?', "%$searchAll%");
            $conditions .= $this->_db->quoteInto(new Zend_Db_Expr('or Concat(EP3.Emp_First_Name," ",EP3.Emp_Last_Name) Like ?'),"%$searchAll%");
            $qryPerformanceAssessment->where($conditions);   
		}
	
		$employeeName=$searchDetails['Employee_Name'];
        
        if ($employeeName != '' && $employeeName != null /*&& preg_match('/^[a-zA-Z]/', $employeeName)*/)
        {
            $qryPerformanceAssessment->where($this->_db->quoteInto(new Zend_Db_Expr('Concat(EP2.Emp_First_Name," ",EP2.Emp_Last_Name) Like ?'),"%$employeeName%"));
        }

        $managerName=$searchDetails['Manager_Name'];
        if(!empty($searchDetails['Manager_Name']) /*&& preg_match('/^[a-zA-Z]+$/', $managerName)*/)
        {
            $qryPerformanceAssessment->where($this->_db->quoteInto(new Zend_Db_Expr('Concat(EP3.Emp_First_Name," ",EP3.Emp_Last_Name) Like ?'),"%$managerName%"));
        }

                

		if (!empty($searchDetails['Assessment_From']))
		{
			$assessStart = explode('-',$searchDetails['Assessment_From']);
            $assessStartMonth =date('Y-m-d', strtotime($assessStart[0]."-".$assessStart[1]."-1"));
			
            $qryPerformanceAssessment->where($this->_db->quoteInto('P.Assessment_From = ?', $assessStartMonth));
		}
		
		if(!empty($searchDetails['Assessment_To']))
		{
			$assessEnd = explode('-',$searchDetails['Assessment_To']);
            $assessEndMonth =date('Y-m-t', strtotime($assessEnd[0]."-".$assessEnd[1]."-1"));
			
			$qryPerformanceAssessment->where($this->_db->quoteInto('P.Assessment_To = ?', $assessEndMonth));
		}
		
		if (!empty($searchDetails['Performance_Status']))
		{
			$qryPerformanceAssessment->where($this->_db->quoteInto('P.Performance_Status = ?',$searchDetails['Performance_Status']));
        }

        if(!empty($searchDetails['serviceProviderId'])&& $this->_orgDetails['Field_Force']==1)
        {
            $qryPerformanceAssessment->where('EJ.Service_Provider_Id = ?',$searchDetails['serviceProviderId']);
        }
        
        $qryPerformanceAssessment = $this->_commonFunction->getDivisionDetails($qryPerformanceAssessment,'EJ.Department_Id');
		
        if(!empty($userDetails['Admin']))
		{			
			$qryPerformanceAssessment = $this->_commonFunction->formServiceProviderQuery($qryPerformanceAssessment,'EJ.Service_Provider_Id',$userDetails['Session_Id']);
		}

		$accountCodeDetails = $this->_db->fetchAll($qryPerformanceAssessment);
        
        $iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
        
        
          /** It is to push the Performance_Id, LineItem_Id,SkillDefinition_Id,Self_Rating,Employee_Remarks,Title & Expected_Skill_Level into the array**/
          foreach ($accountCodeDetails as $key => $row)
          {
            $empperformanceId=$row['Performance_Id'];
            $performanceId=$empperformanceId;
  
            /** the function is called to get the details of the Performance_Id, LineItem_Id,SkillDefinition_Id,Self_Rating,Employee_Remarks,Title & Expected_Skill_Level for an employee**/
            $achievedSkill = $this->getAchievedSkillLevel($performanceId);
            $accountCodeDetails[$key]['achievedSkillLevel'] = $achievedSkill;      
          }		

		$qryiTotal = $this->_db->select()->from($this->_ehrTables->performance, new Zend_Db_Expr('COUNT(Performance_Id)'));
        
		
        if(empty($userDetails['Admin']))
        {
            $qryPerformanceId = $this->_db->select()->from($this->_ehrTables->performance, array('Performance_Id'))
            ->where('Approver_Id = ?', $userDetails['Session_Id'])
            ->orwhere('Second_Level_Approver = ?', $userDetails['Session_Id'])
            ->orwhere('Employee_Id = ?', $userDetails['Session_Id']);
            $getPerformanceId = $this->_db->fetchCol($qryPerformanceId);
            
            if ( $userDetails['Is_Manager'] == 1 && !empty($getPerformanceId))
            {
                $qryPerformanceAssessment->where('P.Performance_Id IN (?)', $getPerformanceId);
            }
            else
            {
                $qryiTotal->where('Employee_Id = ?', $userDetails['Session_Id']);
            }
            
        }
		
		$iTotal = $this->_db->fetchOne($qryiTotal);
		        
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $accountCodeDetails);
    }

    public function getAchievedSkillLevel($performanceId)
    {
        $qrySkillDefinition = $this->_db->select()->from(array('PL'=>$this->_ehrTables->performanceLevel),
                                                    array('PL.Performance_Id','PL.LineItem_Id','PL.SkillDefinition_Id','PL.Weightage','PL.Self_Rating','PL.Employee_Score','PL.Employee_Remarks','PL.Manager_Rating','PL.Manager_Score','PL.Manager_Remarks'))
        
                                                    ->joinInner(array('SD'=>$this->_ehrTables->skillDefinition), 'SD.SkillDefinition_Id = PL.SkillDefinition_Id',
                                                               array('SD.Title','SD.Description'))
                                                    
                                                     ->joinInner(array('P'=>$this->_ehrTables->performance), 'P.Performance_Id = PL.Performance_Id',
                                                               array(''))
                                                    
                                                     ->joinInner(array('SL'=>$this->_ehrTables->skillLevels), 'SL.SkillDefinition_Id = PL.SkillDefinition_Id AND P.Designation_Id=SL.Designation_Id',
                                                                array('SL.Expected_Skill_Level'))
                                                    ->where('PL.Performance_Id = ?', $performanceId);
 
        	
        $skillDefinitionDetails = $this->_db->fetchAll($qrySkillDefinition);
        return  $skillDefinitionDetails;

    }
    
    public function updatePerformanceAssessment($performanceDetails,$empSkillDefinition,$sessionId,$customFormName)
    {
        $assessFromDuration =$performanceDetails['Assessment_From'];
        $assessToDuration = $performanceDetails['Assessment_To'];
    
        $assessStartMonth =date('Y-m-d', strtotime($performanceDetails['Assessment_From']."-1"));

        $assessEndMonth = date('Y-m-t', strtotime($performanceDetails['Assessment_To']."-1"));
        
        $performanceDetails['Assessment_From'] = $assessStartMonth;
        
        $performanceDetails['Assessment_To']   = $assessEndMonth;         
    
        if(!empty($performanceDetails['Performance_Id']))
        {
            $action = 'Edit';
            $performanceDetails['Updated_On'] = date('Y-m-d H:i:s');
            $performanceDetails['Updated_By'] = $sessionId;
            $performanceDetails['Lock_Flag']  = 0;
            $updated = $this->_db->update($this->_ehrTables->performance, $performanceDetails, array('Performance_Id = '.$performanceDetails['Performance_Id']));
            $performanceId = $performanceDetails['Performance_Id'];		

            /**  Performance_Id records are deleted & then multiple records are updated **/
            $deleted = $this->_db->delete($this->_ehrTables->performanceLevel, 'Performance_Id ='.(int)$performanceId);
            foreach($empSkillDefinition as $key=>$row)
            {                           
                
                $empSkillDefinition[$key]['Performance_Id'] = $performanceId;   
            }

            $updated =  $this->_ehrTables->insertMultiple($this->_ehrTables->performanceLevel, $empSkillDefinition);

        }
        else
        {
            $action = 'Add';
            $performanceDetails['Added_On'] = date('Y-m-d H:i:s');
            $performanceDetails['Added_By'] = $sessionId;
            $updated =  $this->_db->insert($this->_ehrTables->performance, $performanceDetails);
            $performanceId=$this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->performance,new Zend_Db_Expr('Max(Performance_Id)')));

            if($updated)
            {
                /** inside foreach the line item id & Performance_Id are added for each record then updated using insert multiple **/
                foreach($empSkillDefinition as $key=>$row)
                {                           
                    $empSkillDefinition[$key]['Performance_Id'] = $performanceId;   
                }

                $updated =  $this->_ehrTables->insertMultiple($this->_ehrTables->performanceLevel, $empSkillDefinition);
            }

        }
                
        return $this->_eftConfiguration->updateResult (array('updated'    => $updated,
                        'action'         => $action,
                        'trackingColumn' => $performanceId,
                        'formName'       => $customFormName,
                        'sessionId'      => $sessionId,
                        'tableName'      => $this->_ehrTables->performance));
    }
    
     public function deletePerformanceAssessment($performanceId, $logEmpId)
    {
       if (!empty($performanceId))
       {
		    $performanceLock = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->performance, array('Lock_Flag', 'Performance_Status'))
		     ->where('Performance_Id = ?', $performanceId));
			
		    if ($performanceLock['Lock_Flag'] == 0 && ($performanceLock['Performance_Status'] == 'Rejected' || $performanceLock['Performance_Status'] == 'In Review with Employee'))
		    {
			    $deleted = $this->_db->delete($this->_ehrTables->performance, 'Performance_Id='.(int)$performanceId);
                            $deleted1 = $this->_db->delete($this->_ehrTables->performanceLevel, 'Performance_Id='.(int)$performanceId);
			    if ($deleted)
	                    {
    					$this->_dbComment->deleteComment($performanceId,'Performance Assessment');
			    }
			      return $this->_commonFunction->deleteRecord (array('deleted'        => $deleted,
								    'tableName'      => $this->_ehrTables->performance,
								    'lockFlag'       => $performanceLock['Lock_Flag'],
								    'formName'       => 'Performance Assessment',
								    'trackingColumn' => $performanceLock['Performance_Status'],
								    'sessionId'      => $logEmpId));
			    
		    }
		    else
		    {
			return array('success'=>false, 'msg'=>'Unable to delete Performance Assessment', 'type'=>'info');
		    }
        }
        else
        {
            return array('success'=>false, 'msg'=>'Unable to delete Performance Assessment. Please, contact system admin', 'type'=>'info');
        }
    }

    public function searchAchievedSkillLevel($performanceId)
    {
        $qrySkillDefinition = $this->_db->select()->from(array('PL'=>$this->_ehrTables->performanceLevel),
                                                    array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS PL.LineItem_Id as count'),
                                                                  'PL.Performance_Id','PL.LineItem_Id','PL.SkillDefinition_Id','PL.Weightage','PL.Self_Rating','PL.Employee_Score','PL.Employee_Remarks','PL.Manager_Rating','PL.Manager_Score','PL.Manager_Remarks',
                                                                  'DT_RowClass' => new Zend_Db_Expr('"performanceLevel"'),
                                                                  'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', PL.LineItem_Id)")))
        
                                                        ->joinInner(array('SD'=>$this->_ehrTables->skillDefinition), 'SD.SkillDefinition_Id = PL.SkillDefinition_Id',
                                                               array('SD.Title'))
                                                    
                                                     ->joinInner(array('P'=>$this->_ehrTables->performance), 'P.Performance_Id = PL.Performance_Id',
                                                               array(''))
                                                    
                                                     ->joinInner(array('SL'=>$this->_ehrTables->skillLevels), 'SL.SkillDefinition_Id = PL.SkillDefinition_Id AND P.Designation_Id=SL.Designation_Id',
                                                                array('SL.Expected_Skill_Level'))
                                                    ->where('PL.Performance_Id = ?', $performanceId)
                                                    ->order('SD.Skill_Type Asc')
                                                    ->order('SD.Title Asc');
 
        	
        $skillDefinitionDetails = $this->_db->fetchAll($qrySkillDefinition);
                
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
                
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->performanceLevel, new Zend_Db_Expr('COUNT(LineItem_Id)'))->where('Performance_Id = ?', $performanceId));
                
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $skillDefinitionDetails);
    }

    public function deleteAchievedSkillLevel($lineItemId,$logEmpId)
    {
        if (!empty($lineItemId))
        {
        
            $performanceId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->performanceLevel,array('Performance_Id'))
                                                                             ->where("LineItem_id = ?",$lineItemId));
            
            
            $lineItemIdCount = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->performanceLevel,new Zend_Db_Expr('count(LineItem_id)'))
                                                                             ->where("Performance_Id = ?",$performanceId));
            
            
            if($lineItemIdCount==1)
            {
                $deleteTrue = $this->_db->delete($this->_ehrTables->performance, 'Performance_Id='.(int)$performanceId);
                $deleteTrue =1;
            }
            else
            {
                 $deleteTrue =0;
            }
            
            
            $deleted = $this->_db->delete($this->_ehrTables->performanceLevel, 'LineItem_id='.(int)$lineItemId);
            $deleted = 1;
            $result = $this->_commonFunction->deleteRecord (array( 'deleted'        => $deleted,
                                                                'tableName'      => $this->_ehrTables->performanceLevel,
                                                                'lockFlag'       => 0,
                                                                'formName'       => 'Key Result Area Mapping',
                                                                'trackingColumn' => $lineItemId,
                                                                'sessionId'      => $logEmpId));
            $result['count'] = $deleteTrue;
            return $result;
            
        }
        else
        {
            return array('success'=>false, 'msg'=>'Unable to delete Achieved Key Result Area Mapping. Please, contact system admin', 'type'=>'info');
        }
    }
    
  
    public function getSkillDefinition($employeeId,$performanceId)
    {
        $qrySkilldef = $this->_db->fetchAll($this->_db->select()->from(array('skilldef'=>$this->_ehrTables->skillDefinition),array('skilldef.SkillDefinition_Id', 'skilldef.Title','skilldef.Description'))
                                        ->joinInner(array('skillLevel'=>$this->_ehrTables->skillLevels), 'skillLevel.SkillDefinition_Id = skilldef.SkillDefinition_Id', array('Expected_Skill_Level','Weightage'))
                                        ->joinInner(array('empJob'=>$this->_ehrTables->empJob), 'empJob.Designation_Id = skillLevel.Designation_Id', array())
                                        ->order('skilldef.Skill_Type ASC')
                                        ->order('skilldef.Title ASC')
                                        ->where('empJob.Employee_Id =?', $employeeId));

        if($performanceId>0)
        {
            $skilldefId = $this->_db->fetchCol($this->_db->select()->from(array('perf'=>$this->_ehrTables->performanceLevel),array('SkillDefinition_Id'))
                                                                                                ->where("perf.Performance_Id = ?",$performanceId));
        }
        else
        {
            $skilldefId = $this->_db->fetchCol($this->_db->select()->from(array('skilldef1'=>$this->_ehrTables->skillDefinition),array('skilldef1.SkillDefinition_Id'))
                                        ->joinInner(array('skillLevel1'=>$this->_ehrTables->skillLevels), 'skillLevel1.SkillDefinition_Id = skilldef1.SkillDefinition_Id', array())
                                        ->joinInner(array('empJob1'=>$this->_ehrTables->empJob), 'empJob1.Designation_Id = skillLevel1.Designation_Id', array())
                                        ->where('empJob1.Employee_Id =?', $employeeId));

        }     

        return array($qrySkilldef,$skilldefId);

    }

    
    /**to load skill definition in sub grid -add **/
    public function addSkillDefinition($employeeId,$performanceId)
    {
        $qryAddSkilldef = $this->_db->fetchCol($this->_db->select()->from(array('skilldef'=>$this->_ehrTables->skillDefinition),array('skilldef.SkillDefinition_Id'))
                                        ->joinInner(array('skillLevel'=>$this->_ehrTables->skillLevels), 'skillLevel.SkillDefinition_Id = skilldef.SkillDefinition_Id', array())
                                        ->joinInner(array('empJob'=>$this->_ehrTables->empJob), 'empJob.Designation_Id = skillLevel.Designation_Id', array())
                                        ->where('empJob.Employee_Id =?', $employeeId));


        $qrySkillDefinition = $this->_db->fetchCol($this->_db->select()->from(array('perf'=>$this->_ehrTables->performanceLevel),array('SkillDefinition_Id'))
                                        ->where("perf.Performance_Id = ?",$performanceId));

    /**to load skill definition which is not there or deleted for the employee in edit **/
        $result=array_diff($qryAddSkilldef,$qrySkillDefinition);
        $result1=array_values($result);

        if($result!=null)
        {
            $qrySkilldef1 = $this->_db->fetchAll($this->_db->select()->from(array('skill'=>$this->_ehrTables->skillDefinition),array('skill.SkillDefinition_Id','skill.Title','skill.Description'))
                                            ->joinInner(array('skillLevel1'=>$this->_ehrTables->skillLevels), 'skillLevel1.SkillDefinition_Id = skill.SkillDefinition_Id', array('skillLevel1.Expected_Skill_Level','skillLevel1.Weightage'))
                                            ->joinInner(array('empJob1'=>$this->_ehrTables->empJob), 'empJob1.Designation_Id = skillLevel1.Designation_Id', array())
                                            ->where('empJob1.Employee_Id =?', $employeeId)
                                            ->order('Title ASC')
                                            ->where('skill.SkillDefinition_Id  IN (?)', $result));  
            return array($qrySkilldef1, $result1);
        }
        else
        {
            return array('success' => false, 'msg'=>"There are no Skill definition for the employee", 'type'=>'warning');;
        }
    }
    
    public function performanceEmployee($performanceId)
    {
        $qryEmpName=$this->_db->select()
        ->from(array('Performance'=>$this->_ehrTables->performance),array('Performance.Employee_Id'))
        ->joinInner(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=Performance.Employee_Id',
        array(new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as Employee_Name")))
        
        
        
        ->joinInner(array('emp2'=>$this->_ehrTables->empPersonal),'emp2.Employee_Id=Performance.Added_By',
        array(new Zend_Db_Expr("CONCAT(emp2.Emp_First_Name, ' ', emp2.Emp_Last_Name) as AddedBy_Name")))
        ->where('Performance.Performance_Id =?', $performanceId);
        $rowEmpName = $this->_db->fetchRow($qryEmpName);
        return $rowEmpName;
    }
      public function statusReport($commentArray, $sessionId, $formName)
    {
        $transferStatus = array('Performance_Status'=>$commentArray['Approval_Status']);
        $updateStatus = $this->_db->update($this->_ehrTables->performance, $transferStatus, 'Performance_Id = ' . $commentArray['Parent_Id']);
        $formId = $this->_dbComment->getFormId($formName);
		
	if(!empty($commentArray['Emp_Comment']))
        {
	    $commentArray['Form_Id']     = $formId;
	    $commentArray['Employee_Id'] = $sessionId;
	    $commentArray['Added_On']    = date('Y-m-d H:i:s');
	    $insertStatus = $this->_db->insert($this->_ehrTables->comment,$commentArray);
        }
		
	if($updateStatus)
        {
            $this->_ehrTables->trackEmpSystemAction('Update Performance Status - '.$commentArray['Parent_Id'], $sessionId);
            return true;
        }
	else
	{
            return false;
        }
    }
	
    public function getPerformanceMonth()
    {
        $qryPerformanceMonth = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->orgDetails,'Performance_StartMonth'));

        return $qryPerformanceMonth;
    }
    public function getPerformanceDetails($performanceId)
    {
        
          $getPerformanceDetails = $this->_db->fetchRow($this->_db->select()
                                              ->from(array('P'=>$this->_ehrTables->performance),
                                                    array('P.Performance_Id','P.Employee_Id','P.Approver_Id',
                                                                  'P.Performance_Status as Status','P.Added_By'))
                                              ->where("Performance_Id = ?",$performanceId));
                                        
         return $getPerformanceDetails;                                
        
    }

      
    public function exportcsvPerformanceAssessment($performanceId,$employeeId)
    {
        $performanceAssessmentReport = $this->_db->fetchRow($this->_db->select()
                                            ->from(array('P'=>$this->_ehrTables->performance),array('P.Achievement','P.Training_Required',
                                                    new Zend_Db_Expr("date_format(P.Assessment_From, '%b, %Y') as AssessmentFrom"),
                                                    new Zend_Db_Expr("date_format(P.Assessment_To, '%b, %Y') as AssessmentTo")))

                                        ->joinInner(array('EP'=>$this->_ehrTables->empPersonal),'EP.Employee_Id=P.Employee_Id',
                                                    array(new Zend_Db_Expr("CONCAT(EP.Emp_First_Name, ' ', EP.Emp_Last_Name) as Employee_Name")))
                                    
                                        ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'EP.Employee_Id=EJ.Employee_Id',
                                            array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN EP.Employee_Id ELSE EJ.User_Defined_EmpId END')))
                                                    
                                        ->joinInner(array('D'=>$this->_ehrTables->dept), 'D.Department_Id=EJ.Department_Id',
                                                    array('D.Department_Name')) 

                                        ->joinInner(array('PL'=>$this->_ehrTables->performanceLevel), 'PL.Performance_Id = P.Performance_Id',
                                                    array(new Zend_Db_Expr('sum(PL.Manager_Rating) as ManagerRating'),new Zend_Db_Expr('sum(PL.Self_Rating) as SelfRating'),
                                                    new Zend_Db_Expr('sum(PL.Employee_Score) as EmployeeScore'),new Zend_Db_Expr('sum(PL.Manager_Score) as ManagerScore')))             
                                        
                                        ->joinInner(array('DE'=>$this->_ehrTables->designation), 'DE.Designation_Id=EJ.Designation_Id',array('DE.Designation_Name'))             
                                        
                                        ->where('P.Performance_Id = ?',$performanceId)
                                        
                                        ->where('EJ.Employee_Id = ?',$employeeId));

        return $performanceAssessmentReport;
    }

    public function getPerformanceLevelAndGrade($performanceAssesment,$performanceMatrixDetails)
    {
        $managerScore = $performanceAssesment['ManagerScore'];
        $performanceAssesment['Performance_Level']           = '';
        $performanceAssesment['Performance_Grade']           = '';
        $performanceAssesment['Performance_Description']     = '';
        foreach($performanceMatrixDetails as $performanceMatrix)
        {
            $rangeFrom = $performanceMatrix['Range_From'];
            $rangeTo   = $performanceMatrix['Range_To'];
            if($rangeFrom <= $managerScore &&  $rangeTo >= $managerScore)
            {
                $performanceAssesment['Performance_Level']       = $performanceMatrix['Rating_Description'];
                $performanceAssesment['Performance_Grade']       = $performanceMatrix['Performance_Grade'];
                $performanceAssesment['Performance_Description'] = $performanceMatrix['Review_Description'];
            }
        }
        return $performanceAssesment;
    }

    public function getPerformanceMatrix()
    {
        $performanceMatrixDetails = $this->_db->fetchAll($this->_db->select()->from(array('P'=>$this->_ehrTables->performanceRatingDescription),array('*'))
                                                                                    -> where('P.Range_From IS NOT NULL'));
        return $performanceMatrixDetails;
    }
    
    public function getEmployeeAchivedSkill($employeeId,$designationId,$skillDefinitionId,$getPerformanceMonth,$assessmentMonth=null)
    {      
       
       /** to display in single csv report upto the selected record Assessment from month only Future Month details should not be shown **/
       if(!empty($assessmentMonth))
       {  
         $qryGetEmployeeAchivedSkill=$this->_db->select()->from(array('P'=>$this->_ehrTables->performance),array('P.Employee_Id','P.Performance_Id','P.Assessment_From','SD.Title'))
              
                                            ->joinInner(array('PL'=>$this->_ehrTables->performanceLevel), 'PL.Performance_Id = P.Performance_Id',
                                                        array('PL.Self_Rating','PL.SkillDefinition_Id'))
                                   
                                            ->joinInner(array('SD'=>$this->_ehrTables->skillDefinition), 'SD.SkillDefinition_Id=PL.SkillDefinition_Id',
                                                        array())
                                            ->joinInner(array('S'=>$this->_ehrTables->skillLevels), 'S.SkillDefinition_Id=PL.SkillDefinition_Id',
                                                        array('S.Expected_Skill_Level'))
                                  
                                            ->where('Assessment_From >= ?', $getPerformanceMonth['performanceStartDate'])
                                            ->where('Assessment_From <= ?', $getPerformanceMonth['performanceEndDate'])
             /** condition to display in single csv report upto the selected record Assessment from month only Future Month details should not be shown**/
                                            ->where('Assessment_From <= ?', $assessmentMonth['Assessment_From'])

                                            ->where('P.Employee_Id = ?',$employeeId)
                                            ->group('P.Performance_Id')
                                            ->where("S.Designation_Id = ?",$designationId)
                                            ->where("PL.SkillDefinition_Id = ?",$skillDefinitionId);
                                                                               
         return  $this->_db->fetchAll($qryGetEmployeeAchivedSkill);
    }
     else
     {
        $qryGetEmployeeAchivedSkill=$this->_db->select()->from(array('P'=>$this->_ehrTables->performance),array('P.Employee_Id','P.Performance_Id','P.Assessment_From','SD.Title'))
              
                                            ->joinInner(array('PL'=>$this->_ehrTables->performanceLevel), 'PL.Performance_Id = P.Performance_Id',
                                                        array('PL.Self_Rating','PL.SkillDefinition_Id'))

                                            ->joinInner(array('SD'=>$this->_ehrTables->skillDefinition), 'SD.SkillDefinition_Id=PL.SkillDefinition_Id',
                                                        array())
                                            ->joinInner(array('S'=>$this->_ehrTables->skillLevels), 'S.SkillDefinition_Id=PL.SkillDefinition_Id',
                                                        array('S.Expected_Skill_Level'))

                                            ->where('Assessment_From >= ?', $getPerformanceMonth['performanceStartDate'])
                                            ->where('Assessment_From <= ?', $getPerformanceMonth['performanceEndDate'])

                                            ->where('P.Employee_Id = ?',$employeeId)
                                            ->group('P.Performance_Id')
                                            ->where("S.Designation_Id = ?",$designationId)
                                            ->where("PL.SkillDefinition_Id = ?",$skillDefinitionId);
                                                                            
        return  $this->_db->fetchAll($qryGetEmployeeAchivedSkill);

     }
    }

    public function getCustomRemarksValidation($customEmployeeRemarks)
    {
        if(!empty($customEmployeeRemarks))
        {
            $customEmployeeRemarkFieldName = $customEmployeeRemarks['New_Field_Name'];
            if($customEmployeeRemarks['Required']==1)
            {
                $employeeRemarksRequired = 'required';
            }
            else
            {
                $employeeRemarksRequired = '';
            }
        }
        else
        {
            $customEmployeeRemarkFieldName = 'Employee Remarks';
            $employeeRemarksRequired = '';
        }

        return array('employeeRemarksRequired'=>$employeeRemarksRequired,'customEmployeeRemarkFieldName'=>$customEmployeeRemarkFieldName);
    }
  
    public function __destruct()
    {
        
    }
   
    
  }