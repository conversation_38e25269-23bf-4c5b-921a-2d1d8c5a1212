.tab-border-cls {
    border-bottom: 3px solid;
}

.tab-text-font a:focus {
    color: #333333 !important;
    text-decoration: none;
}

.custom-tab {
    background: #f1f1f1 !important;
    padding: 0px !important;
    margin-top:1em !important;
}

.pointer-cls{
    cursor: pointer;
}

.tab-text-font{
    font-size: 1em;
    margin-top: 1em;
    display: flex;
    justify-content: center;
}

.tab-wrapper {
    display: flex;
}

@media screen and (max-width:500px) {
    .tab-wrapper {
        flex-wrap: wrap;
        justify-content: center;
        padding: 20px 20px 0px;
    }
    .tab-body {
        margin-left: 0em !important;
        min-height: 0px !important;
    }
    .custom-tab-content{
        padding-top: 15px !important;
        padding-bottom: 12px !important;
    }
    .tab-text-fon {
        margin-top: 0px !important;
    }
}

.tab-active-text{
    font-weight: 600 !important;
}

.padding-class {
    padding: 4em;
    padding-bottom: 0px;
}

.bg-f9f9f9{
    background: #f9f9f9;
}
.bg-ffffff{
    background: #ffffff;
}

.custom-tab-content{
    text-align: center;
    padding-top: 25px;
    padding-bottom: 5px;
    background: #f9f9f9;
}

.custom-sub-tab-content {
    text-align: center;
    padding-bottom: 5px;
    background: #ffffff;
}

.sub-tab-card{
    overflow: hidden;
    background-color: #ffffff;
}

.sub-tab-body {
    margin-left: 2em;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    min-width: 130px;
    padding: 2px 15px;
}


.tab-body {
    margin-left: 2em;
    min-height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    min-width: 130px;
    padding: 2px 15px;
}

.custom-tab-actions {
    justify-content: flex-end;
    display: flex;
    width: 70%;
}

.tab-a-tag-color {
    color: #333333 !important;
}

@media screen and (max-width:990px) {
    .tab-spacing-cls{
        margin-bottom: 20px;
    }
    .custom-tab-actions {
        width: 55% !important;
    }
}

@media screen and (max-width:990px) {
    .custom-tab-actions {
        width: 45% !important;
    }
}