$(function () {
    var getDomainName = fnIsDomain();
    var orgCode = fngetOrgCode();
    var domainName = getDomainName.split('.')[0] + '_';

    // Global variable declarations
    var AddResumeFileNameArray = [];  // Array contains Resume file name
    var AddProfileFileNameArray = []; // Array contains profile file name
    var educationDetailsArray = []; // Array contains candidates education details
    var experienceDetailsArray = []; // Array contains candidates experience details
    var skillsDetailsArray = []; // Array contains candidates skilld details
    var certificationsDetailsArray = []; // Array contains candidates certifications details
    var jobCandidates = []; // Array contains candidates details
    var educationrowcount = 1;
    var experiencerowcount = 1;
    var skillsrowcount = 1;
    var certificationrowcount = 1;
    var tabName,
        currentEditRowId,
        colId,
        editColId,
        currentEditRow,
        resumeFileName,
        profileFileName,
        changeimage,
        tab,
        editFlag,
        nEditing,
        eduLastRow,
        expLastRow,
        skiLastRow,
        cerLastRow,
        eduCurrentRow,
        expCurrentRow,
        skiCurrentRow,
        cerCurrentRow,
        s3ProfileFileName,
        s3ResumeFileName,
        addFlag,
        chosenCandidatesIndexValues,
        lastInsertedId,
        resumeFileSize,
        candidateId,
        courseList,
        deleteTab,
        deleteRow;
    var changeResumeFlag = 0;
    var changeProfileFlag = 0;
    var removeProfileFlag = 0;
    var oTable1;
    var oTable2;
    var oTable3;
    var oTable4;
    var educationJson = {};  // Candidate can have multiple education details
    var experienceJson = {};  // Candidates can have multiple experience details
    var skillsJson = {}; // Candidates can have multiple skills details
    var certificationsJson = {};  // Candidates can have multiple certifications details
    var currentEle, profileURL, resumeURL = '';
    var editEduCurrentEle,
        editExpCurrentEle,
        editSkiCurrentEle,
        editCerCurrentEle;
    var listCandidatesCount = 0;
    var prefillCandidateInfo = '';
    var isDirtyFormCandidates = false;
    var editeducationrowcount = 1;
    var editexperiencerowcount = 1;
    var editskillsrowcount = 1;
    var editcertificationrowcount = 1;
    var accessRightsParams      = {}; //access rights
    var filterValues = {
        candidatejobpost : [],
        candidatePreferedJobpostLocation : [],
        candidateStatusFilter : []
    }

    // flags to identify that the current rows are saved.
    var educationRowSaved = 0; 
    var workExpRowSaved = 0;
    var skillRowSaved = 0;
    var certRowSaved = 0;

    var addByTab = 0; //flag to identify first tab (basic details tab)

    var timer = null; //timer variable for search fields

    var candidateCurrentStatus = 10;
    var candidateNewStatus  = 10;

    setMask('wholepage');
    var filterDropDownArray = ['candidatePreferedJobpostLocation1','candidatePreferedJobpostLocation','candidatejobpost1','candidatejobpost','candidateStatusFilter','candidateStatusFilter1'];


    $('#educationForm,#experienceForm,#skillForm,#certificateForm').on('change', ':input', function(e) {
        isDirtyFormCandidates = true;
    });


    /**access rights of the employees */
    var employeeId = parseInt(localStorage.getItem('LoginEmpId'), 10);
    
    var atsBaseURL = getApiCustomDomains('ats');


    // Hide the change and remove button in both resume and profile upload
    $('#changeResumeFile,#removeResumeFile').hide();
    $('#removeProfileFile,#changeProfileFile').hide();
    $('#AddEditJobCandidates').hide();
    $('#ViewJobCandidates').hide();
    $('#listJobCandidates').show();
    $('#access-denied').hide();

     // get the client ip address by using the common function in custom.js
     var ipAddress = '';
     getClientIp(function(ipAdd) {
         ipAddress = ipAdd;
     });

    // function to Get the organization date format
    function fnOrgDate() {
        var dateformat = '';
        if ($('body').data('page') != 'salary-payslip' && $('body').data('page') != 'login') {
            $.ajax({
                type: 'POST',
                dataType: 'json',
                async: false,
                url: pageUrl() + 'employee-info/org-date-format',
                success: function (result) {
                    dateformat = result;
                }
            });
        }
        return dateformat;
    }
    var dateFormat = fnOrgDate();

        //  check access rights
    checkAccessRights(orgCode,atsBaseURL,'Job Candidates',employeeId,'#listJobCandidates',function(error, accessRights){
        // check if access rights are retrieved successfully
        if(accessRights)
        {
            // assign the access rights
            accessRightsParams['viewAccessRights'] = accessRights.Role_View;
            accessRightsParams['addAccessRights'] = accessRights.Role_Add;
            accessRightsParams['updateAccessRights'] = accessRights.Role_Update;
            accessRightsParams['deleteAccessRights'] = accessRights.Role_Delete;

            // check view access rights exists
            if(accessRightsParams['viewAccessRights'])
            {
                    // list all candidates details
                    $('#listJobCandidates').show();
                    $('#access-denied').hide(); 

                listJobCandidates(0);
                getDropDownData();
            }
            else 
            {
                // hide list and show access denied
                $('#listJobCandidates').hide();
                $('#access-denied').show(); 


            }
        }
    });
   


function getDropDownData()
{
    /**list the countries */
    $.ajax({
        method: 'POST',
        url: atsBaseURL,
        headers: getGraphqlAPIHeaders(ipAddress),
        data: JSON.stringify({
            query: `query CommentQuery(
                $searchString: String
                $designation: Int
                $location: [Int]
                $functionalArea: Int
                $jobType: Int
                $closingDate: Date
                $status: Int
                $skills: [Int]
                $qualification: [Int]
                $isDropDownCall: Int!
              ) {
                listJobPost(
                  searchString: $searchString
                  designation: $designation
                  location: $location
                  functionalArea: $functionalArea
                  jobType: $jobType
                  closingDate: $closingDate
                  status: $status
                  skills: $skills
                  qualification: $qualification
                  isDropDownCall: $isDropDownCall
                ) {
                  errorCode
                  message
                  JobpostDetails {
                    Job_Post_Id
                    Job_Post_Name
                    Client_Name
                    No_Of_Vacancies
                    Event_Id
                    Job_Post_Status
                    Status_Id
                    Interview_Status
                  }
                }
              }`,
            variables: {
                "searchString": "",
                "designation": null,
                "functionalArea": null,
                "jobType": null,
                "closingDate": "",
                "status": null,
                "location": [],
                "isDropDownCall": 1,
                "skills": [],
                "qualification": [],
            } 
        }),
        success: function (result3) {
            if(result3.data)
            {
                removeMask();
                var jobPosts = result3.data.listJobPost.JobpostDetails;
                for (var y in jobPosts) {
                    $('#Jobpost,#candidatejobpost,#candidatejobpost1').append("<option value='" + jobPosts[y].Job_Post_Id + "'>" + jobPosts[y].Job_Post_Name + '</option>');
                }
                /**list the countries */
                $.ajax({
                    method: 'POST',
                    url: atsBaseURL,
                    headers: getGraphqlAPIHeaders(ipAddress),
                    data: JSON.stringify({
                        query: `query samplequery { getDropDownBoxDetails { errorCode message  locations { Location_Id Location_Name} 
                        currency { Currency_Id Currency_Name } workAuthorizations { Work_Authorization_Id Work_Authorization_Name } status { Id Status } }}`
                    }),
                    success: function (result3) {
                        if (result3.data) {
                            // load workpermit dropdown data
                            var workPermits = result3.data.getDropDownBoxDetails.workAuthorizations;
                            for (var x in workPermits) {
                                $('#WorkPermit').append("<option value='" + workPermits[x].Work_Authorization_Id + "'>" + workPermits[x].Work_Authorization_Name + '</option>');
                            }

                            // append the location data for dropdown
                            var locations = result3.data.getDropDownBoxDetails.locations;
                            for (var x in locations) {
                                $('#candidatePreferedJobpostLocation,#candidatePreferedJobpostLocation1').append("<option value='" + locations[x].Location_Id + "'>" + locations[x].Location_Name + '</option>');
                            }

                            // append the currecy
                            var currency = result3.data.getDropDownBoxDetails.currency;
                            for(var z in currency)
                            {
                                $('#currency').append("<option value='" + currency[z].Currency_Id + "'>" + currency[z].Currency_Name + '</option>');
                            }

                            // status dropdown data
                            var status =  result3.data.getDropDownBoxDetails.status
                            // load the status dropdown
                            for( var k in status)
                            {
                                // check if the status is not Completed/In-Progress(Interview status)
                                if(status[k].Id == 3 || status[k].Id === 10 || status[k].Id === 11 || status[k].Id === 12)
                                {
                                    $('#candidateStatusFilter,#candidateStatusFilter1').append(
                                        "<option value='" +
                                                status[k].Id +
                                            "'>" +
                                            status[k].Status +                                               
                                            '</option>'
                                    )
                                }
                            }
                        } else {
                            removeMask();
                            resetDataTable();

                            addFlag = 0;
                            editFlag = 0;

                            errorAlertMessage($('#listJobCandidates'));
                        }
                    }
                });
            }
            else 
            {
                resetDataTable();
                removeMask();
                addFlag = 0;
                editFlag = 0;
                var error = JSON.parse(result3.errors[0].message);
                var errorCode = error.errorCode;
                $(window).scrollTop(0);
                switch (errorCode) {
                    case 705:
                    case 706:
                        commonAlertMessage('#listJobCandidates');
                        break;
                    case 719:
                    case 733:
                    default:
                        errorAlertMessage($('#listJobCandidates'));
                        break;
                }
            }
            
        },
        error: function(result3){
            removeMask();
            resetDataTable();

            addFlag = 0;
            editFlag = 0;

            errorAlertMessage($('#listJobCandidates'));
        }
    });
}

 


    // Data table definition
    oTable1 = $('#table-editable1').dataTable({
        "bDestroy": true,
        "aLengthMenu": [
        ],
        "sDom": "",
        "oTableTools": {
            "sSwfPath": "",
            "aButtons": [

            ]
        }
    });

    oTable2 = $('#table-editable2').dataTable({
        "bDestroy": true,
        "aLengthMenu": [
        ],
        "sDom": "",
        "oTableTools": {
            "sSwfPath": "",
            "aButtons": [

            ]
        }
    });

    oTable3 = $('#table-editable3').dataTable({
        "bDestroy": true,
        "aLengthMenu": [
        ],
        "sDom": "",
        "oTableTools": {
            "sSwfPath": "",
            "aButtons": [

            ]
        }
    });

    oTable4 = $('#table-editable4').dataTable({
        "bDestroy": true,
        "aLengthMenu": [
        ],
        "sDom": "",
        "oTableTools": {
            "sSwfPath": "",
            "aButtons": [

            ]
        }
    });

    // work authorizations input
    $('#WorkPermit').on('change',function(e,initialCheck=false){
            if(initialCheck) {
                isDirtyFormCandidates = false;
            }
            else 
            {
                isDirtyFormCandidates = true;
                // addRemoveValidationErrors('remove',e.target.id,'');
                var workAuthorization = $('#WorkPermit').select2('val').map(function(v) {
                    return parseInt(v, 10);
                });
                if(workAuthorization.length > 0){
                    for(var i in workAuthorization)
                    {
                        // if work authorization chosen is 'Other'
                        if(workAuthorization[i]===3)
                        {

                            // make 'Other' field editable
                            $('#otherworkpermits').prop('readonly',false)
                                                .addClass('vRequired')
                                                .addClass('alphahNumHyDotBrackSpc');
                        }
                        else 
                        {
                            // make 'Other' field readonly
                            $('#otherworkpermits').prop('readOnly',true)
                                                .val('')
                                                .removeClass('vRequired')
                                                .removeClass('alphahNumHyDotBrackSpc')
                                                .trigger('change');
                        }
                    }
                }
                else 
                {
                    $('#WorkPermit').addClass('empty');

                    $('#otherworkpermits').prop('readOnly',true)
                    .val('')
                    .removeClass('vRequired')
                    .removeClass('alphahNumHyDotBrackSpc')
                    .trigger('change');
                }
            }
            
            
        
    });

    // CandiateDOB picker
    $('#CandidateDOB').datepicker({
        dateFormat: dateFormat[0],
        changeYear: true,
        yearRange: yearrange,
        maxDate: new Date(new Date(tzDate()).getFullYear() - 15, 11, 31)
    });

    // On click the jobcandidates button
    $('#addJobCandidates').on('click', function () {
        if (accessRightsParams['addAccessRights']) {
            removeFormClassErrorForForms('#formJobCandidateBioData');
            removeFormClassErrorForForms('#formJobCandidateBioData');


            $('#formheading').html('')
                             .append('ADD JOB CANDIDATES');
            $('#tab2').on('show.bs.tab', 'a', function(event) {
                event.preventDefault();
            });
            $('#tab2').on('hide.bs.tab', 'a', function(event) {
                event.preventDefault();
            });

            $('#tab3').hide();

            // show the status of the Candidate as applied during add
            $('#candidateStatus').val('Applied')
                                .trigger('change');

            isDirtyFormCandidates = false;
            resetCandidatesAddEditForm();
            addFlag = 1;
            // Loading the drop down data
            getLanguageCoursecountry();
            
        }
        else {
            commonAccessDeniedAlert($('#listJobCandidates'));
        }
    });

    //Add new line in Edducation tab
    $('#table-edit_new1').on('click', function (e) {
        e.preventDefault();

        if(educationRowSaved)
        {
            addNewEducationRow();
        }
        else if ($('#educationForm').valid()) {
            var educationTable = $('#table-editable1').DataTable();
            var educationTableData = educationTable.rows().data();
            //If education record exist
            if(educationTableData.length > 0)
            {
                saveEductionRow();
            }
            addNewEducationRow();
        }
        
    });

    // Add new row in Experience tab
    $('#table-edit_new2').on('click', function (e) {
        e.preventDefault();

        if(workExpRowSaved)
        {
            addNewWorkExpRow();
        }
        else if ($('#experienceForm').valid()) {
            var experienceTable = $('#table-editable2').DataTable();
            var experienceTableData = experienceTable.rows().data();
            if(experienceTableData.length > 0)
            {
                saveWorkExpRow();
            }
    
            addNewWorkExpRow();
            
        }
    });

    //Add new row in Skills tab
    $('#table-edit_new3').on('click', function (e) {
        e.preventDefault();

        if(skillRowSaved)
        {
            addNewSkillsRow();

        }
        if ($('#skillForm').valid()) {
            var skillsTable = $('#table-editable3').DataTable();
            var skillsTableData = skillsTable.rows().data();
            //If skill level record exist
            if(skillsTableData.length > 0)
            {
                saveSkillsRow();
            }
            addNewSkillsRow();
        }
    });

    $('#table-edit_new4').on('click', function (e) {
        e.preventDefault();

        if(certRowSaved)
        {
            addNewCertRow();
        }
        if ($('#certificateForm').valid()) {
            var certificateTable = $('#table-editable4').DataTable();
            var certificateTableData = certificateTable.rows().data();
            if(certificateTableData.length > 0)
            {
                saveCertificationsRow();
            }
            addNewCertRow();
        }
    })

    function addNewEducationRow() {
        if (editFlag) {
            isDirtyFormCandidates = true;
        }
        educationrowcount++;
        var aiNew = oTable1.fnAddData(['', '', '', '', '',
            '<div class = "row"> <div class= "text-center col-lg-6 col-md-6 col-sm-12"> <a class="tabSaveIcon fa fa-check" aria-hidden="true" href=""></a> </div> <div class="text-center col-lg-6 col-md-6 col-sm-12"><a class="fa fa-trash-o delete" href=""></a></div></div>'
        ]);
        var nRow = oTable1.fnGetNodes(aiNew[0]);
        educationEditRow(oTable1, nRow);
        educationRowSaved = 0;
    }

    function addNewWorkExpRow(){
        if (editFlag) {
            isDirtyFormCandidates = true;
        }
        experiencerowcount++;
        var aiNew = oTable2.fnAddData(['', '', '', '',
            '<p class = "row"> <div class= "text-center col-lg-6 col-md-6 col-sm-12"> <i class="tabSaveIcon fa fa-check" aria-hidden="true"></i> </div> <div class="text-center col-lg-6 col-md-6 col-sm-12"><a class="fa fa-trash-o delete" href=""></a></div></p>'
        ]);
        var nRow = oTable2.fnGetNodes(aiNew[0]);
        experienceEditRow(oTable2, nRow);
        workExpRowSaved = 0;
    }

    function addNewSkillsRow() {
        if (editFlag) {
            isDirtyFormCandidates = true;
        }
        skillsrowcount++;
        var aiNew = oTable3.fnAddData(['', '', '', '',
            '<div class = "row"> <div class= "text-center col-lg-6 col-md-6 col-sm-12" id="tabRowSaveButton' + skillsrowcount + '"> <i class="tabSaveIcon fa fa-check" aria-hidden="true"></i> </div> <div class="text-center col-lg-6 col-md-6 col-sm-12"><a class="fa fa-trash-o delete text-secondary" id= "deletebtn' + skillsrowcount + '"href=""></a></div>'                // '<div class = "row"> <div class= "text-center col-lg-6 col-md-6 col-sm-12"> <i class="tabSaveIcon fa fa-check" aria-hidden="true"></i> </div> <div class="text-center col-lg-6 col-md-6 col-sm-12"><a class="fa fa-trash-o delete" href=""></a></div></div>'
        ]);
        var nRow = oTable3.fnGetNodes(aiNew[0]);
        skillsEditRow(oTable3, nRow);
        skillRowSaved = 0;
    }

    function addNewCertRow()
    {
        if (editFlag) {
            isDirtyFormCandidates = true;
        }
        certificationrowcount++;
        var aiNew = oTable4.fnAddData(['', '', '',
            '<div class = "row"> <div class= "text-center col-lg-6 col-md-6 col-sm-12" id="tabRowSaveButton' + certificationrowcount + '"> <i class="tabSaveIcon fa fa-check" aria-hidden="true"></i> </div> <div class="text-center col-lg-6 col-md-6 col-sm-12"><a class="fa fa-trash-o delete text-secondary" id= "deletebtn' + certificationrowcount + '"href=""></a></div></div>'
        ]);
        var nRow = oTable4.fnGetNodes(aiNew[0]);
        certificationsEditRow(oTable4, nRow);
        certRowSaved = 1;
    }

    function saveWorkExpRow() {
        var jqInputs = $('input', expCurrentRow);
            oTable2.fnUpdate(jqInputs[0].value, expCurrentRow, 0, true);
            oTable2.fnUpdate(jqInputs[1].value, expCurrentRow, 1, false);
            oTable2.fnUpdate(jqInputs[2].value, expCurrentRow, 2, false);
            oTable2.fnUpdate(jqInputs[3].value, expCurrentRow, 3, false);
            oTable2.fnUpdate('<div class="text-center"><a class="fa fa-trash-o delete text-secondary" id= "deletebtn' + educationrowcount + '" href=""></a></div>', expCurrentRow, 4, false);
            oTable2.fnDraw();
            // Increment the expLastRow count
            expLastRow++;
    }

    function saveEductionRow()
    {
        var jqInputs = $('input', eduCurrentRow);
            // Get the dropdown text using id
            var dropDownText = '';
            for (var i = 0; i <= courseList.length - 1; i++) {
                if (courseList[i].Course_Id == $('#academy' + educationrowcount).val()) {
                    dropDownText = courseList[i].Course_Name;
                }
            }
            oTable1.fnUpdate(dropDownText, eduCurrentRow, 0, true);
            oTable1.fnUpdate(jqInputs[0].value, eduCurrentRow, 1, false);
            oTable1.fnUpdate(jqInputs[1].value, eduCurrentRow, 2, false);
            oTable1.fnUpdate(jqInputs[2].value, eduCurrentRow, 3, false);
            oTable1.fnUpdate(jqInputs[3].value, eduCurrentRow, 4, false);
            oTable1.fnUpdate('<div class="text-center"><a class="fa fa-trash-o delete text-secondary" id= "deletebtn' + educationrowcount + '" href=""></a></div>', eduCurrentRow, 5, false);
            oTable1.fnDraw();
            // Increment the educationRow count
            eduLastRow++;
    }

    function saveSkillsRow()
    {
        var jqInputs = $('input', skiCurrentRow);
        oTable3.fnUpdate(jqInputs[0].value, skiCurrentRow, 0, true);
        oTable3.fnUpdate(jqInputs[1].value, skiCurrentRow, 1, false);
        oTable3.fnUpdate(jqInputs[2].value, skiCurrentRow, 2, false);
        oTable3.fnUpdate(jqInputs[3].value, skiCurrentRow, 3, false);
        oTable3.fnUpdate('<div class="text-center"><a class="fa fa-trash-o delete text-secondary" id= "deletebtn' + educationrowcount + '" href=""></a></div>', skiCurrentRow, 4, false);
        oTable3.fnDraw();
        // Increment the skiLastRow count
        skiLastRow++;

    }

    function saveCertificationsRow() {
        var jqInputs = $('input', cerCurrentRow);
        oTable4.fnUpdate(jqInputs[0].value, cerCurrentRow, 0, true);
        oTable4.fnUpdate(jqInputs[1].value, cerCurrentRow, 1, false);
        oTable4.fnUpdate(jqInputs[2].value, cerCurrentRow, 2, false);
        oTable4.fnUpdate('<div class="text-center"><a class="fa fa-trash-o delete text-secondary" id= "deletebtn' + educationrowcount + '" href=""></a></div>', cerCurrentRow, 3, false);
        oTable4.fnDraw();
        // Increment the cerLastRow
        cerLastRow++;

    }

    // Education tab row save
    $('#table-editable1').on('click', 'a.save', function (e) {
        // Define tab number as 1
        tab = 1;
        if ($('#educationForm').valid()) {
            educationRowSaved = 1;
            saveEductionRow();
        }
    });
    

    // Experience tab row save
    $('#table-editable2').on('click', 'a.save', function (e) {
        // Define tab number as 2
        tab = 2;
        if ($('#experienceForm').valid()) {
            workExpRowSaved =1;
            saveWorkExpRow();
        }
    });
    // Skills tab row save
    $('#table-editable3').on('click', 'a.save', function (e) {
        // Define tab number as 3
        tab = 3;
        if ($('#skillForm').valid()) {
            skillRowSaved = 1;
            saveSkillsRow();
        }

    });
    // Certifications tab row save
    $('#table-editable4').on('click', 'a.save', function (e) {
        // Define tab number as 4
        tab = 4;
        if ($('#certificateForm').valid()) {
            certRowSaved = 1;
            saveCertificationsRow();
        }
    });

    //Education tab row delete cancel 
    $('#table-editable1').on('click', 'a.cancel', function (e) {
        e.preventDefault();
        if ($(this).attr("data-mode") == "new") {
            var nRow = $(this).parents('tr')[0];
            oTable1.fnDeleteRow(nRow);
        } else {
            restoreRow(oTable1, nEditing);
            nEditing = null;
        }
    });

    //Experience tab row delete cancel 
    $('#table-editable2').on('click', 'a.cancel', function (e) {
        e.preventDefault();
        if ($(this).attr("data-mode") == "new") {
            var nRow = $(this).parents('tr')[0];
            oTable2.fnDeleteRow(nRow);
        } else {
            restoreRow(oTable2, nEditing);
            nEditing = null;
        }
    });

    //Skills tab row delete cancel  
    $('#table-editable3').on('click', 'a.cancel', function (e) {
        e.preventDefault();
        if ($(this).attr("data-mode") == "new") {
            var nRow = $(this).parents('tr')[0];
            oTable3.fnDeleteRow(nRow);
        } else {
            restoreRow(oTable3, nEditing);
            nEditing = null;
        }
    });

    // Certifications tab row delete cancel
    $('#table-editable4').on('click', 'a.cancel', function (e) {
        e.preventDefault();
        if ($(this).attr("data-mode") == "new") {
            var nRow = $(this).parents('tr')[0];
            oTable3.fnDeleteRow(nRow);
        } else {
            restoreRow(oTable3, nEditing);
            nEditing = null;
        }
    });
    /**delete job candidates */
    $('#confirmDeleteRow').on('click', function () {
        var table, tableData;
        if (deleteTab == 1) {
            oTable1.fnDeleteRow(deleteRow);
            table = $('#table-editable1').DataTable();
            tableData = table
                .rows()
                .data();
            // Check no of rows in particular table
            if (tableData.length == 0) {
                // If the no of rows is 0, set last row is 0
                eduLastRow = 0;
            }
        }
        else if (deleteTab == 2) {
            oTable2.fnDeleteRow(deleteRow);
            table = $('#table-editable2').DataTable();
            tableData = table
                .rows()
                .data();
            // Check no of rows in particular table
            if (tableData.length == 0) {
                // If the no of rows is 0, set last row is 0
                eduLastRow = 0;
            }
        }
        else if (deleteTab == 3) {
            oTable3.fnDeleteRow(deleteRow);
            table = $('#table-editable3').DataTable();
            tableData = table
                .rows()
                .data();
            // Check no of rows in particular table
            if (tableData.length == 0) {
                // If the no of rows is 0, set last row is 0
                eduLastRow = 0;
            }
        }
        else {
            oTable4.fnDeleteRow(deleteRow);
            table = $('#table-editable4').DataTable();
            tableData = table
                .rows()
                .data();
            // Check no of rows in particular table
            if (tableData.length == 0) {
                // If the no of rows is 0, set last row is 0
                eduLastRow = 0;
            }
        }
    });
    // Education tab row delete
    $('#table-editable1').on('click', 'a.delete', function (e) {

        e.preventDefault();

        if (editFlag) {
            isDirtyFormCandidates = true;
        }
        deleteTab = 1;
        deleteRow = $(this).parents('tr')[0];
        $('#modalDeleterows').modal('toggle');

        var deleteCount = e.target.id;

        deleteCount = parseInt(deleteCount.substring(10-1),10);
        
        if(deleteCount ===educationrowcount ) {
            educationRowSaved = 1;
        }
    });

    //Experience tab row delete
    $('#table-editable2').on('click', 'a.delete', function (e) {
        if (editFlag) {
            isDirtyFormCandidates = true;
        }
        e.preventDefault();
        deleteTab = 2;
        deleteRow = $(this).parents('tr')[0];
        $('#modalDeleterows').modal('toggle');
        var deleteCount = e.target.id;

        deleteCount = parseInt(deleteCount.substring(10-1),10);
        
        if(deleteCount === experiencerowcount ) {
            workExpRowSaved = 1;
        }

    });

    // Skills tab row delete
    $('#table-editable3').on('click', 'a.delete', function (e) {
        e.preventDefault();

        if (editFlag) {
            isDirtyFormCandidates = true;
        }
        deleteTab = 3;
        deleteRow = $(this).parents('tr')[0];
        $('#modalDeleterows').modal('toggle');

        var deleteCount = e.target.id;

        deleteCount = parseInt(deleteCount.substring(10-1),10);
        
        if(deleteCount === skillsrowcount ) {
            skillRowSaved = 1;
        }
    });

    // Certifications tab row delete
    $('#table-editable4').on('click', 'a.delete', function (e) {
        if (editFlag) {
            isDirtyFormCandidates = true;
        }
        e.preventDefault();
        deleteTab = 4;
        deleteRow = $(this).parents('tr')[0];
        $('#modalDeleterows').modal('toggle');

        var deleteCount = e.target.id;

        deleteCount = parseInt(deleteCount.substring(10-1),10);
        
        if(deleteCount === certificationrowcount ) {
            certRowSaved = 1;
        }

    });

  

    $('.table-field').on('change','input',function(){
        isDirtyFormCandidates = true;

    });

    $('.table-field').on('change','select',function(){
        isDirtyFormCandidates = true;

    });


    //Education row create
    function educationEditRow(oTable, nRow) {
        eduCurrentRow = nRow;
        var aData = oTable.fnGetData(nRow);
        var jqTds = $('>td', nRow);

        jqTds[0].innerHTML = '<select name="academy "id= "academy' + educationrowcount + '"class="tab-fileds form-control select-fields vRequired datatable-dropdown table-field" > <option value="" disabled selected>Academy</option> value="' + aData[0] + '">';
        jqTds[1].innerHTML = '<input type="text" maxlength = "50" minlength="5" name="institutename" id= "institutename' + educationrowcount + '"class="tab-fileds form-control vRequired onlyLetterSp table-field" placeholder="Institute Name" value="' + aData[0] + '">';
        jqTds[2].innerHTML = '<input type="text" maxlength = "50" minlength="3" name="course" id= "course' + educationrowcount + '" placeholder="Course" class="tab-fileds form-control alphaNumHypBrack vRequired table-field" value="' + aData[1] + '">';
        jqTds[3].innerHTML = '<input type="number" step="1" min = "1900" maxlength="4" name="year" id= "year' + educationrowcount + '"class="tab-fileds form-control  vRequired vYearPassing table-field" placeholder="Year" value="' + aData[2] + '">';
        jqTds[4].innerHTML = '<input type="number" step="0.01" name="grade" min="0" max = 100 id= "grade' + educationrowcount + '" class="tab-fileds form-control  vRequired table-field" placeholder="Grade" value="' + aData[3] + '">';
        jqTds[5].innerHTML = '<div class = "row"> <div class= "text-center col-lg-6 col-md-6 col-sm-12" style="width: 100%;"> <a class="tabSaveIcon fa fa-check save text-secondary" id= "savebtn' + educationrowcount + '></a></div> <div class= "text-center col-lg-6 col-md-6 col-sm-12"> <a class="fa fa-trash-o delete text-secondary" id= "deletebtn' + educationrowcount + '"href=""></a></div></div>';
        for (i = 0; i <= 4; i++) {
            jqTds[i].id = 'edu' + educationrowcount + i;
            jqTds[i].className = 'editable';
        }
        listCourses(educationrowcount);
    }

    //Experience row create
    function experienceEditRow(oTable, nRow) {
        expCurrentRow = nRow;
        var aData1 = oTable.fnGetData(nRow);
        var jqTds = $('>td', nRow);
        jqTds[0].innerHTML = '<input type="text" name="company" maxlength = "50" minlength="5" id= "company' + experiencerowcount + '"class="tab-fileds form-control vRequired alSpDotAndHypen table-field" placeholder="Company Name" value="' + aData1[0] + '">';
        jqTds[1].innerHTML = '<input type="text" name="designation" maxlength = "50" minlength="5" id= "designation' + experiencerowcount + '"class=" tab-fileds form-control onlyAlnumHypAndSp vRequired vName table-field" placeholder="Designation" value="' + aData1[1] + '">';
        jqTds[2].innerHTML = '<input type="text"  name="startyear" id= "startyear' + experiencerowcount + '" data-mask="99/99/9999" class="tab-fileds form-control vRequired vDateMonth vExperienceStartDate table-field" placeholder="DD/MM/YYYY" value="' + aData1[1] + '">';
        jqTds[3].innerHTML = '<input type="text"  name ="endyear" id= "endyear' + experiencerowcount + '" data-mask="99/99/9999" class="tab-fileds form-control vRequired vDateMonth vExperienceEndDate table-field" placeholder="DD/MM/YYYY" value="' + aData1[2] + '">';
        jqTds[4].innerHTML = '<div class = "row"> <div class= "text-center col-lg-6 col-md-6 col-sm-12" style="width: 100%;"> <a class="tabSaveIcon fa fa-check save text-secondary" id= "savebtn' + experiencerowcount + '></a></div> <div class= "text-center col-lg-6 col-md-6 col-sm-12"> <a class="fa fa-trash-o delete text-secondary" id= "deletebtn' + experiencerowcount + '"href=""></a></div></div>';
        for (i = 0; i <= 3; i++) {
            jqTds[i].id = 'exp' + experiencerowcount + i;
            jqTds[i].className = 'editable';
        }
    }
    //Skills row create
    function skillsEditRow(oTable, nRow) {
        skiCurrentRow = nRow;
        var aData2 = oTable.fnGetData(nRow);
        var jqTds = $('>td', nRow);
        jqTds[0].innerHTML = '<input type="text" maxlength = "50" minlength="5" name="skills" id= "skills' + skillsrowcount + '"class=" tab-fileds form-control vskillValidation vRequired table-field" placeholder="Skills" value="' + aData2[0] + '">';
        jqTds[1].innerHTML = '<input type="text" name="description" maxlength="500" minlength="3" id= "description' + skillsrowcount + '"class=" tab-fileds form-control  vRequired table-field" placeholder="Description" value="' + aData2[1] + '">';
        jqTds[2].innerHTML = '<input type="text" name="proficiency" maxlength = "50" minlength="3"id= "proficiency' + skillsrowcount + '"class=" tab-fileds form-control onlyLetterSp vRequired table-field" placeholder="Proficiency" value="' + aData2[2] + '">';
        jqTds[3].innerHTML = '<input type="text" name="primary" maxlength = "50" minlength="3" id= "primary' + skillsrowcount + '"class=" tab-fileds form-control onlyLetterSp vRequired table-field" placeholder="Primary" value="' + aData2[3] + '">';
        jqTds[4].innerHTML = '<div class = "row"> <div class= "text-center col-lg-6 col-md-6 col-sm-12" style="width: 100%;"> <a class="tabSaveIcon fa fa-check save text-secondary" id= "savebtn' + skillsrowcount + '></a></div> <div class= "text-center col-lg-6 col-md-6 col-sm-12"> <a class="fa fa-trash-o delete text-secondary" id= "deletebtn' + skillsrowcount + '"href=""></a></div></div>';
        for (i = 0; i <= 3; i++) {
            jqTds[i].id = 'ski' + skillsrowcount + i;
            jqTds[i].className = 'editable';
        }
    }

    // Certifications row create
    function certificationsEditRow(oTable, nRow) {
        cerCurrentRow = nRow;
        var aData2 = oTable.fnGetData(nRow);
        var jqTds = $('>td', nRow);
        certificationsId = jqTds;
        jqTds[0].innerHTML = '<input type="text" name="certificationname" minlength="5" maxlength="50" id= "certificationname' + certificationrowcount + '"class="tab-fileds form-control vRequired vCertification table-field" placeholder="Certification Name"value="' + aData2[0] + '">';
        jqTds[1].innerHTML = '<input type="text"  id= "certificationsreceiveddate' + certificationrowcount + '" data-mask="99/99/9999" class="tab-fileds form-control form-control vRequired vCerificationReceivedDate vDateMonth table-field" placeholder="DD/MM/YYYY" value="' + aData2[2] + '">';
        jqTds[2].innerHTML = '<input type="text" name="certificatereceivedfrom" minlength="5"maxlength="50" id= "certificatereceivedfrom' + certificationrowcount + '"class="tab-fileds form-control vRequired onlyLetterSp table-field" placeholder="Certification Received From" value="' + aData2[2] + '">';
        jqTds[3].innerHTML = '<div class = "row"> <div class= "text-center col-lg-6 col-md-6 col-sm-12" style="width: 100%;"> <a class="tabSaveIcon fa fa-check save text-secondary" id= "savebtn' + certificationrowcount + '></a></div> <div class= "text-center col-lg-6 col-md-6 col-sm-12"> <a class="fa fa-trash-o delete text-secondary" id= "deletebtn' + cerCurrentRow + '"href=""></a></div></div>';
        for (i = 0; i <= 2; i++) {
            jqTds[i].id = 'cer' + certificationrowcount + i;
            jqTds[i].className = 'editable';
        }
    }

    //Restore row
    function restoreRow(oTable, nRow) {
        var aData = oTable.fnGetData(nRow);
        var jqTds = $('>td', nRow);
        for (var i = 0, iLen = jqTds.length; i < iLen; i++) {
            oTable.fnUpdate(aData[i], nRow, i, false);
        }
        oTable.fnDraw();
    }

    // Add and remove validations error for the inline editor fields
    function addRemoveValidationErrors(action, fieldId) {
        if (action === 'add') {
            $(fieldId).addClass('validation');
            $.validator.addMethod(
                fieldId,
                function () {
                    return false;
                },

                "This field is required."
            );
            var id = fieldId.replace(/"/g, '');
            $.validator.addClassRules('validation', { [id]: true });
            $(fieldId).valid();
        }
        else {
            $(fieldId).removeClass('validation');
            $.validator.addMethod(
                fieldId,
                function () {
                    return true;
                },
                ''
            );
            var id = fieldId.replace(/"/g, '');
            $.validator.addClassRules('validation', { [id]: false });
            $(fieldId).valid();
        }
    }

    // Inline validations for education tab
    $('#educationForm').on('input', function (e) {
        tab = 1;
        if ($('#' + e.target.id).val()) {
            addRemoveValidationErrors("remove", '#' + e.target.id);
        }
        else {
            addRemoveValidationErrors("add", '#' + e.target.id);
        }
    });
    // Inline validations for experience tab
    $('#experienceForm').on('input', function (e) {
        tab = 2;
        if ($('#' + e.target.id).val()) {
            addRemoveValidationErrors("remove", '#' + e.target.id);
        }
        else {
            addRemoveValidationErrors("add", '#' + e.target.id);
        }
    });
    // Inline validations for ceritification tab
    $('#certificateForm').on('input', function (e) {
        tab = 4;
        if ($('#' + e.target.id).val()) {
            addRemoveValidationErrors("remove", '#' + e.target.id);
        }
        else {
            addRemoveValidationErrors("add", '#' + e.target.id);
        }
    });

    // Inline validations for skills tab
    $('#skillForm').on('input', function (e) {
        tab = 3;
        if ($('#' + e.target.id).val()) {
            addRemoveValidationErrors("remove", '#' + e.target.id);
        }
        else {
            addRemoveValidationErrors("add", '#' + e.target.id);
        }
    });

    // Validation for Year of Passing
    $.validator.addMethod('vYearPassing' ,function (value)
    {
        var passYear = $('#year' + educationrowcount).val();
        passYear = parseInt(passYear,10);
        var currentdate = new Date();
        var currentYear = currentdate.getFullYear();
        if ($('#CandidateDOB').val()) {
            var dobYear = ($('#CandidateDOB').datepicker('getDate')).getFullYear();
                if (dobYear > passYear || passYear > currentYear) {
                    return false;
                }
                else 
                {
                    return true;
                }
        }
        else 
        {
            $(window).scrollTop(0);

                addRemoveValidationErrors("add", '#' + 'CandidateDOB');

                $('#year'+educationrowcount).val('');

                return false;
        }
    }, 'Year of Passing should be greater than DOB and less than or equal to current year.'
    );

    //Validation for Experience start year

    $.validator.addMethod('vExperienceStartDate',function (value)
    {
        if ($('#CandidateDOB').val()) {
            value = value.toString();
                var startYear = value.split('/');
                var dobYear = ($('#CandidateDOB').datepicker('getDate')).getFullYear();
                var currentdate = new Date();
                var currentYear = currentdate.getFullYear();
                if (dobYear >= startYear[2] || startYear[2] > currentYear) {
                    return false;
                }
                else 
                {
                    return true;
                }
        }
        else 
        {
            $(window).scrollTop(0);

            addRemoveValidationErrors("add", '#' + 'CandidateDOB');

            $('#startyear'+experiencerowcount).val('');

            return false;

        }
    },'Experience start year should be greater than DOB and should not be greater than current year'
    );

    //Validation for experience end year
    $.validator.addMethod('noticeperiod', function (value) {
        if ($.inArray(value, ['', null]) === -1) {
            if ($('#currentemployer').val() === '') {
                addRemoveValidationErrors("add", '#' + 'currentemployer');
            }
        }

        return true;
    }, 'Certification received year should be greater than date of birth.');

    $.validator.addMethod('vDateMonth', function (value) {
        value = value.split('/');
        var yearInString = value[2].toString();
        if (yearInString.length == 4) {
            var IsLeapYear = (value[2] % 100 === 0) ? (value[2] % 400 === 0) : (value[2] % 4 === 0)
            if (IsLeapYear) {
                if (value[1] <= 12 && value[0] <= 31) {
                    if (value[1] !== undefined && ['01', '03', '05', '07', '08', '10', '12'].indexOf(value[1]) !== -1) {
                        if (value[0] > 31) {
                            return false;
                        }
                        else {
                            return true;
                        }
                    }
                    else if (value[1] !== undefined && ['04', '06', '09', '11'].indexOf(value[1]) !== -1){
                        if (value[0] > 30) {
                            return false;
                        }
                        else {
                            return true;
                        }
                    }
                    else if (value[1] !== undefined && ['02'].indexOf(value[1]) !== -1) {
                        if (value[0] > 29) {
                            return false;
                        }
                        else {
                            return true;
                        }
                    }
                }
                else {
                    return false;
                }

            }
            else {
                if (value[1] <= 12 && value[0] <= 31) {
                    if (jQuery.inArray(value[1], ['01', '03', '05', '07', '08', '10', '12']) !== -1) {
                        if (value[0] > 31) {
                            return false;
                        }
                        else {
                            return true;
                        }
                    }
                    else if (value[1] !== undefined && ['04', '06', '09', '11'].indexOf(value[1]) !== -1) {
                        if (value[0] > 30) {
                            return false;
                        }
                        else {
                            return true;
                        }
                    }
                    else if (value[1] !== undefined && ['02'].indexOf(value[1]) !== -1) {
                        if (value[0] > 28) {
                            return false;
                        }
                        else {
                            return true;
                        }
                    }
                }
                else {
                    return false;
                }
            }
        }
        else {
            return false;
        }
    }, 'Invalid Date');

    

    //Validation for  Experience end year
    $.validator.addMethod('vExperienceEndDate',function(value)
    {
        // check if Candidate DOB is given
        if ($('#CandidateDOB').val()) {
            // end Date Value
            var endDate = value.split('/');
            endDate = new Date(endDate[1] + '/' + endDate[0] + '/' + endDate[2]);
            endDate.setHours(0,0,0,0);

            // today
            var today = new Date();
            today.setHours(0,0,0,0);

            // start Date
            var startDate = $('#startyear' + experiencerowcount).val();
            startDate = startDate.split('/');
            startDate = new Date(startDate[1] + '/' + startDate[0]+'/'+startDate[2]);
            startDate.setHours(0,0,0,0);

            // check if start date is not greater/equal to start date and not greater than current date 
            if(endDate <= startDate || endDate > today)
            {
                return false;
            }
            else 
            {
                return true;
            }
        }
        // set validation for Candidate DOB if its value is empty
        else 
        {
            $(window).scrollTop(0);

            addRemoveValidationErrors("add", '#' + 'CandidateDOB');

            $('#endyear'+experiencerowcount).val('');

            return false;
        }
    },'Experience End year should be greater than DOB and should not be greater than current year')

    //Validation for  certification received date
    $.validator.addMethod('vCerificationReceivedDate', function (value) {
        if ($.inArray(value, ['', null]) === -1) {
            if ($('#CandidateDOB').val()) {
                value = value.toString();
                var certificationYear = value.split('/');
                var dobYear = ($('#CandidateDOB').datepicker('getDate')).getFullYear();
                var currentdate = new Date();
                var currentYear = currentdate.getFullYear();
                if (dobYear >= certificationYear[2] || certificationYear[2] > currentYear) {
                    return false;
                }
            }
            else {
                addRemoveValidationErrors("add", '#' + 'CandidateDOB');
            }
        }

        return true;
    }, 'Certification Date should be greater than DOB and should not be greater than current year');
    // load jobpost preferred location based on jobpost chosen
    $('#Jobpost').on('change', function (event, check) {
        if (check) {
            isDirtyFormCandidates = false;
            var changedJobPost = 0;

            if(editFlag)
            {

                setPreferredLocation(changedJobPost);
            }
            else 
            {
                $('#PreferredLocation').html("");

                $('#s2id_PreferredLocation').select2('val', []);
                $('#PreferredLocation').trigger('change', [{ initialCheck: true }]);
                $('#PreferredLocation').addClass('empty');
    
            }
        }
        else {
            var changedJobPost = 1;
            // check if action is edit
            if(editFlag)
            {
                // check the candidate status is in "Applied/Rejected"
                if(candidateCurrentStatus ===10 || candidateCurrentStatus === 3) 
                {
                    // now, the status is changed to applied, if the job post is changed during edit. Have to configure this during cooling period config.
                    candidateNewStatus = 10;
                    $('#candidateStatus').val('Applied');
                    /**list the preferred location */
                    setPreferredLocation(changedJobPost);

                }
                // candidate may be in Scheduled for Interview/Shortlisted
                else 
                {
                    candidateNewStatus = candidateCurrentStatus;

                    $(window).scrollTop(0);

                    jAlert({
                        panel: $("#addJobCandidatesPanel"),
                        msg: "Cannot change Job post for Candidates in 'Scheduled for Interview/Shortlisted' status",
                        type: "info"
                    });
        
                    $('#Jobpost').select2('val',prefillCandidateInfo.Job_Post_Id);
                }
            }
            else 
            {
                candidateNewStatus = candidateCurrentStatus;

                /**list the preferred location */
                setPreferredLocation(changedJobPost);
            }
            isDirtyFormCandidates = true;
        }
        addRemoveValidationErrors("remove", '#' + event.target.id);
    });

    /** load preferred location in dropdown according to chosen Job Post*/ 
    function setPreferredLocation(changedJobPost)
    {
        setMask('#wholepage');
        // check if the Job post is chosen in dropdown
        if($('#s2id_Jobpost').select2('val')) {
            $.ajax({
                method: 'POST',
                url: atsBaseURL,
                headers: getGraphqlAPIHeaders(ipAddress),
                data: JSON.stringify({
                    query: `query CommentQuery($jobpostId:Int!) { listJobPostLocations (jobpostId:$jobpostId) { errorCode message jobpostLocations {Location_Id Location_Name }}}`,
                    variables: {
                        jobpostId: parseInt($('#s2id_Jobpost').select2('val'))
                    }
                
                }),
                success: function (result3) {
                    removeMask();
                    var jobPostLocations = result3.data.listJobPostLocations.jobpostLocations;
                    for (var x in jobPostLocations) {
                        $('#PreferredLocation').html("");
                        $('#PreferredLocation').append("<option value='" + jobPostLocations[x].Location_Id + "'>" + jobPostLocations[x].Location_Name + '</option>');
                    }
                    if(editFlag){
                        var preferredLocationIds = [];
                        for (var i = 0; i <= prefillCandidateInfo.Preferred_Location.length - 1; i++) {
                            preferredLocationIds.push(prefillCandidateInfo.Preferred_Location[i].Location_Id);
                        }
                        $('#s2id_PreferredLocation').select2('val', preferredLocationIds);
                        $('#PreferredLocation').trigger('change', [{ initialCheck: true }])
                        PreferredLocation = [];
                    }
                    if(changedJobPost)
                    {
                        $('#s2id_PreferredLocation').select2('val', []);
                        $('#PreferredLocation').trigger('change', [{ initialCheck: true }]);
                        $('#PreferredLocation').addClass('empty')
                                               .addClass('vRequired');
                    }
                },
                error: function (result3) {
                    removeMask();
                    errorAlertMessage($('#listJobCandidates'));
                }
            });
        }
        // remove the preferred location data and remove validation for preferred location
        else 
        {
            removeMask();
            $('#PreferredLocation').html("");

            $('#s2id_PreferredLocation').select2('val', []);
            $('#PreferredLocation').trigger('change', [{ initialCheck: true }]);
            $('#PreferredLocation').addClass('empty')
                                    .removeClass('vRequired');

        }
        
    }

    // On Change validations error remove
    $('#firstName,#Salutation,#contactNumber,#Gender,#LanguagesKnown,#lastName,#CandidateDOB,#email,#currentemployer,' +
        '#BloodGroup,#MaritalStatus,#Nationality,#MotherName,#FatherName,#PreferredLocation,#WorkPermit,#NoticePeriod' +
        '#ApartmentName,#Street,#City,#State,#PinCode,#Country,PassportNumber,#NationalIdentificationNumber,#VerifierName,#VerifierEmailId,#VerifierPhoneNumber,#otherworkpermits').on('change', function (event, check) {
            if (check) {
                isDirtyFormCandidates = false;
            }
            else {
                isDirtyFormCandidates = true;
            }


            if(event.target.id === "PreferredLocation") {
                if($('#s2id_Jobpost').select2('val'))
                {
                    $('#PreferredLocation')
                    .addClass('vRequired');
                }
                else 
                {
                    $('#PreferredLocation').removeClass('vRequired');
                }
            }
            addRemoveValidationErrors("remove", '#' + event.target.id);
        });

    $('#CurrentCTC,#ExpectedCTC').on('input','change',function(event,check){
        if (check) {
            isDirtyFormCandidates = false;
        }
        else {
            isDirtyFormCandidates = true;
            if($('#CurrentCTC').val() || $('#ExpectedCTC').val() ) {
                $('#currency').addClass('vRequired');
            }
            else 
            {
                $('#currency').removeClass('vRequired');
                addRemoveValidationErrors("remove", '#currency');

 
            }

        }
        addRemoveValidationErrors("remove", '#' + event.target.id);
    });

    // Function to append list of course 
    function listCourses(currenteducationrowId) {
        /**list the courselist */
        $.ajax({
            method: 'POST',
            url: atsBaseURL,
            headers: getGraphqlAPIHeaders(ipAddress),
            data: JSON.stringify({
                query: `{ getCourseList {errorCode message Courses { Course_Id Course_Name}}}`
            }),
            success: function (result5) {
                if (result5.data) {
                    courseList = result5.data.getCourseList.Courses //It has to be amended with inline editor
                    if (currenteducationrowId) {
                        for (var x in courseList) {
                            $('#academy' + currenteducationrowId).append("<option value='" + courseList[x].Course_Id + "'>" + courseList[x].Course_Name + '</option>');
                        }
                    }
                }
                else {
                    var error = JSON.parse(result5.errors[0].message);
                    var errorCode = error.errorCode;
                    $(window).scrollTop(0);
                    switch (errorCode) {
                        case 705:
                        case 706:
                            commonAlertMessage('#listJobCandidates');
                            break;
                        case 726:
                        default:
                            errorAlertMessage($('#listJobCandidates'));
                            break
                    }
                }
            },
            error: function (result6) {
                errorAlertMessage($('#listJobCandidates'));
            }
        });
    }

    // Set default resume file preview    
    function setDefaultFilePreview(file) {
        if (file === 'resume') {
            if (AddResumeFileNameArray.length == 0) {
                setTimeout(function () {
                    $('#candidateResumePreview').html('');
                    $('#candidateResumePreview').width('');
                    $('#candidateResumePreview').height('');
                    $('#changeResumeFile,#removeResumeFile').hide();
                    $('#showContentDiv1,#cloudIcon1').show();
                    $('#innerRectangle').css('background-color', 'rgb(240,240,248)');
                }, 150);
            }
        }
        else {
            if (AddProfileFileNameArray.length == 0) {
                setTimeout(function () {
                    $('#candidateProfilePreview').html('');
                    $('#candidateProfilePreview').width('');
                    $('#candidateProfilePreview').height('');
                    $('#changeProfileFile,#removeProfileFile').hide();
                    $('#showProfileContentDiv1,#cloudIcon2').show();
                    $('#profileInnerRectangle').css('background-color', 'rgb(240,240,248)');
                }, 150);
            }
        }

    }

    /** set the width and height while selecting the resume file **/
    function fnsetSelectImgWidth(filetype, fileName) {
        if (filetype === 'resume') {
            $("#showContentDiv1,#cloudIcon1").hide();
            if (fileName != undefined && fileName != "") {
                var dfileName = fileName.split(".");
                var filename = dfileName[0];
                var fileExtension = dfileName[1];
                displayFileName = displayFileNameWithDot(filename, fileExtension);
                if (fileExtension == "png" || fileExtension == "jpg" || fileExtension == "jpeg") {
                    var reader = new FileReader();

                    reader.onload = function (e) {
                        setTimeout(function () {
                            $('#candidateResumePreview').html('');
                            $('#candidateResumePreview').width('');
                            $('#candidateResumePreview').height('');
                            $('#candidateResumePreview').append('<img src="' + e.target.result + '" alt="' + fileName + '" style="max-width:100%;height:auto;max-height: 220px;">'); /**show cropping preview */
                        }, 150);
                    };

                    if (changeimage == 0) {
                        reader.readAsDataURL($('input[id=selectCandidateResumeFile]')[0].files[0]);
                    } else {
                        reader.readAsDataURL($('input[id=selectChangeResumeFile]')[0].files[0]);
                    }

                    $('#innerRectangle').css('background-color', '#ffffff');
                }
                else {
                    setFileTypes(dfileName[2], 'addEdit', displayFileName);
                }
            }
        }
        else {
            $("#showProfileContentDiv1,#cloudIcon2").hide();
            if (fileName != undefined && fileName != "") {
                var dfileName = fileName.split(".");
                var filename = dfileName[0];
                var fileExtension = dfileName[1];
                displayFileName = displayFileNameWithDot(filename, fileExtension);
                if (fileExtension == "png" || fileExtension == "jpg" || fileExtension == "jpeg") {
                    var reader = new FileReader();

                    reader.onload = function (e) {
                        setTimeout(function () {
                            $('#candidateProfilePreview').html('');
                            $('#candidateProfilePreview').width('');
                            $('#candidateProfilePreview').height('');
                            $('#candidateProfilePreview').append('<img src="' + e.target.result + '" alt="' + fileName + '" style="max-width:100%;height:auto;max-height: 220px;">'); /**show cropping preview */
                        }, 150);
                    };
                    if (changeimage == 0) {
                        reader.readAsDataURL($('input[id=selectCandidateProfileFile]')[0].files[0]);
                    }
                    else {
                        reader.readAsDataURL($('input[id=selectChangeProfileFile]')[0].files[0]);
                    }

                    $('#profileInnerRectangle').css('background-color', '#ffffff');
                }
            }
        }
    }

    // Function to display file name with dot if character exceeds the count
    function displayFileNameWithDot(filename, fileExtension) {
        // var dfileName = dfileName.split(".");
        /* Function to display file name with three dots */
        if (filename.length > 20) {
            displayFileName = filename.substring(0, 20);
            displayFileName = displayFileName + '...' + fileExtension;
            return displayFileName;
        }
        else {
            displayFileName = filename + '.' + fileExtension;
            return displayFileName;
        }
    }

    // Check resume file format
    function fncheckResumeFileFormat(value) {
        if (
            $.inArray(value, [
                "png",
                "jpg",
                "jpeg",
                "doc",
                "docx",
                "pdf"
            ]) == -1
        ) {
            return false;
        } else {
            return true;
        }
    }

    // Check resume file format
    function fncheckProfileFileFormat(value) {
        if (
            $.inArray(value, [
                "png",
                "jpg",
                "jpeg"
            ]) == -1
        ) {
            return false;
        } else {
            return true;
        }
    }

    //Select resume file
    $("#selectCandidateResumeFile").on("change", function () {
        if ($(this).val() !== "") {
            isDirtyFormCandidates = true;  // Set form dirty
            if (editFlag) {
                changeResumeFlag = 1;
            }
            var files = $("input[id=selectCandidateResumeFile]")[0].files[0];
            var fileSize = parseFloat(files.size) / 1000;
            /**to find the size of file in Bytes,KB,MB or GB */
            var _size = files.size;
            var fSExt = new Array('Bytes', 'KB', 'MB', 'GB'),
                i = 0;
            while (_size > 900) {
                _size /= 1000;
                i++;
            }
            resumeFileSize = Math.round(_size * 100) / 100;
            resumeFileSize = resumeFileSize.toFixed(1) + ' ' + fSExt[i];

            var format = fncheckResumeFileFormat(
                $(this)
                    .val()
                    .split(".")
                    .pop()
                    .toLowerCase()
            );
            if (fileSize > 3000) {
                setDefaultFilePreview('resume');
                jAlert({
                    panel: $("#addJobCandidatesPanel"),
                    msg: "File size should be less than 3Mb",
                    type: "warning"
                });
            } else if (!format) {
                setDefaultFilePreview('resume');
                jAlert({
                    panel: $("#addJobCandidatesPanel"),
                    msg: "This format is not supported",
                    type: "warning"
                });
            }
            else {
                changeimage = 0;
                AddResumeFileNameArray = [];
                AddResumeFileNameArray.push(files);
                fnsetSelectImgWidth('resume', files.name);
                $('#removeResumeFile').show();
                $('#changeResumeFile').show();
                $('#showContentDiv1,#cloudIcon1').hide();
            }
        }
        else {
            if (AddResumeFileNameArray.lsetDefaultFilePreviewength == 0) {
                setDefaultFilePreview('resume');
                $("#showContentDiv1,#cloudIcon1").show();
            }
            else {
                changeimage = 0;
                fnsetSelectImgWidth('resume', AddResumeFileNameArray[0].name);
            }
        }
        $('#selectCandidateResumeFile').val('');
    });

    // Change resume file
    $('#selectChangeResumeFile').on('change', function () {
        if ($(this).val() !== "") {
            isDirtyFormCandidates = true;  // Set form dirty
            if (editFlag) {
                changeResumeFlag = 1;
            }
            var files = $("input[id=selectChangeResumeFile]")[0].files[0];
            var fileSize = parseFloat(files.size) / 1000;

            /**to find the size of file in Bytes,KB,MB or GB */
            var _size = files.size;
            var fSExt = new Array('Bytes', 'KB', 'MB', 'GB'),
                i = 0;
            while (_size > 900) {
                _size /= 1000;
                i++;
            }
            resumeFileSize = Math.round(_size * 100) / 100;
            resumeFileSize = resumeFileSize.toFixed(1) + ' ' + fSExt[i];

            var format = fncheckResumeFileFormat(
                $(this)
                    .val()
                    .split(".")
                    .pop()
                    .toLowerCase()
            );
            if (fileSize > 3000) {
                setDefaultFilePreview('resume');
                jAlert({
                    panel: $("#addJobCandidatesPanel"),
                    msg: "File size should be less than 3Mb",
                    type: "warning"
                });

            } else if (!format) {
                setDefaultFilePreview('resume');
                jAlert({
                    panel: $("#addJobCandidatesPanel"),
                    msg: "This format is not supported",
                    type: "warning"
                });
            } else {
                changeimage = 1;
                AddResumeFileNameArray = [];
                AddResumeFileNameArray.push(files);
                fnsetSelectImgWidth('resume', files.name);
                $('#removeResumeFile').show();
                $('#changeResumeFile').show();
                $('#showContentDiv1,#cloudIcon1').hide();
            }
        }
        else {
            changeimage = 1;
            fnsetSelectImgWidth('resume', AddResumeFileNameArray[0].name);
        }
        $('#selectChangeResumeFile').val('');
    });

    //Remove resume file 
    $('#removeResumeFile').on('click', function () {
        isDirtyFormCandidates = true;  // Set form dirty  
        AddResumeFileNameArray = [];
        resumeFileName = null;
        if (editFlag) {
            changeResumeFlag = 1;
        }
        setTimeout(function () {
            $('#candidateResumePreview').html('');
            $('#candidateResumePreview').width('');
            $('#candidateResumePreview').height('');
            $('#removeResumeFile').hide();
            $('#changeResumeFile').hide();
            $('#showContentDiv1,#cloudIcon1').show();
            $('#innerRectangle').css('background-color', '#f0f0f8');
        }, 150);

    });

    //Select profile file
    $("#selectCandidateProfileFile").on("change", function () {
        if ($(this).val() !== "") {
            isDirtyFormCandidates = true;  // Set form dirty
            if (editFlag) {
                changeProfileFlag = 1;
            }
            var files = $("input[id=selectCandidateProfileFile]")[0].files[0];
            var fileSize = parseFloat(files.size) / 1000;
            var format = fncheckProfileFileFormat(
                $(this)
                    .val()
                    .split(".")
                    .pop()
                    .toLowerCase()
            );
            if (fileSize > 3000) {
                setDefaultFilePreview('profile');
                jAlert({
                    panel: $("#addJobCandidatesPanel"),
                    msg: "File size should be less than 3Mb",
                    type: "warning"
                });
            } else if (!format) {
                setDefaultFilePreview('profile');
                jAlert({
                    panel: $("#addJobCandidatesPanel"),
                    msg: "This format is not supported",
                    type: "warning"
                });
            }
            else {
                changeimage = 0;
                AddProfileFileNameArray = [];
                AddProfileFileNameArray.push(files);
                fnsetSelectImgWidth('profile', files.name);
                $('#removeProfileFile').show();
                $('#changeProfileFile').show();
                $('#showProfileContentDiv1,#cloudIcon2').hide();
            }
        }
        else {
            if (AddProfileFileNameArray.lsetDefaultFilePreviewength == 0) {
                setDefaultFilePreview('profile');
                $("#showProfileContentDiv1,#cloudIcon2").show();
            }
            else {
                changeimage = 0;
                fnsetSelectImgWidth('profile', AddResumeFileNameArray[0].name);
            }
        }
        $('#selectCandidateProfileFile').val('');
    });

    // Change profile file
    $('#selectChangeProfileFile').on('change', function () {
        if ($(this).val() !== "") {
            isDirtyFormCandidates = true;  // Set form dirty
            if (editFlag) {
                changeProfileFlag = 1;
            }
            var files = $("input[id=selectChangeProfileFile]")[0].files[0];
            var fileSize = parseFloat(files.size) / 1000;

            var format = fncheckResumeFileFormat(
                $(this)
                    .val()
                    .split(".")
                    .pop()
                    .toLowerCase()
            );
            if (fileSize > 3000) {
                setDefaultFilePreview('profile');
                jAlert({
                    panel: $("#addJobCandidatesPanel"),
                    msg: "File size should be less than 3Mb",
                    type: "warning"
                });

            } else if (!format) {
                setDefaultFilePreview('profile');
                jAlert({
                    panel: $("#addJobCandidatesPanel"),
                    msg: "This format is not supported",
                    type: "warning"
                });
            } else {
                changeimage = 1;
                AddProfileFileNameArray = [];
                AddProfileFileNameArray.push(files);
                fnsetSelectImgWidth('profile', files.name);
                $('#removeResumeFile').show();
                $('#changeResumeFile').show();
                $('#showContentDiv1,#cloudIcon1').hide();
            }
        }
        else {
            changeimage = 1;
            fnsetSelectImgWidth('profile', AddProfileFileNameArray[0].name);
        }
        $('#selectChangeResumeFile').val('');
    });

    //Remove profile file 
    $('#removeProfileFile').on('click', function () {
        isDirtyFormCandidates = true;  // Set form dirty
        if (editFlag) {
            removeProfileFlag = 1;
        }

        AddProfileFileNameArray = [];
        setTimeout(function () {
            $('#candidateProfilePreview').html('');
            $('#candidateProfilePreview').width('');
            $('#candidateProfilePreview').height('');
            $('#removeProfileFile').hide();
            $('#changeProfileFile').hide();
            $('#showProfileContentDiv1,#cloudIcon2').show();
            $('#profileInnerRectangle').css('background-color', '#f0f0f8');
        }, 150);

    });
    //Education tab inline edit
    $('#table-editable1 tbody').on('dblclick', '.editable', function () {
        if($('#educationForm').valid()) {
            tabName = 'edu'
            tab = 1;
            var value = $(this).html();
            var inlineid = $(this).parents('td').context.id;
            var testing = $(this).children("input");
            
            var element = this;
            var currentTable = oTable1;
            formInlineEdit(tabName,tab,value,inlineid,testing,element,currentTable);
    
        }

        
    });

    //Experience tab inline edit
    $('#table-editable2 tbody').on('dblclick', '.editable', function () {
        if($('#experienceForm').valid())
        {

            tabName = 'exp';
            tab = 2;
            var value = $(this).html();
            var inlineid = $(this).parents('td').context.id;
            var testing = $(this).children("input");

            var element = this;
            var currentTable = oTable2;

            formInlineEdit(tabName,tab,value,inlineid,testing,element,currentTable);
        }

    });

    //Skills tab inline edit
    $('#table-editable3 tbody').on('dblclick', '.editable', function () {
        if($('#skillForm').valid()) {
            tabName = 'ski';
            tab = 3;
            var value = $(this).html();
            var inlineid = $(this).parents('td').context.id;
            var testing = $(this).children("input");
            var element = this;
            var currentTable = oTable3;


            formInlineEdit(tabName,tab,value,inlineid,testing,element,currentTable);
        }

    });

    //Certifications tab inline edit
    $('#table-editable4 tbody').on('dblclick', '.editable', function () {
        if($('#certificateForm').valid()) {
        tabName = 'cer';
        tab = 4;
        var value = $(this).html();
        var inlineid = $(this).parents('td').context.id;
        var testing = $(this).children("input");
        
        var element = this;
        var currentTable = oTable4;

        formInlineEdit(tabName,tab,value,inlineid,testing,element,currentTable);
        }
    });
    

    // set the inline editor field
    function formInlineEdit(tabName,tab,value,inlineid,testing,element,currentTable)
    {
        var tabRowId;
        if (editFlag) {
            var inlineColId = String(inlineid);
            editColId = inlineColId[inlineColId.length - 1];
            var inlineRowId = String(inlineid).slice(3);
            tabRowId = inlineRowId.slice(0, -1);
            currentEditRow = currentTable.fnGetNodes(tabRowId - 1);
        }
        else {
            var inlineColId = String(inlineid);
            colId = inlineColId[inlineColId.length - 1];
            var inlineRowId = String(inlineid).slice(3);
            tabRowId = inlineRowId.slice(0, -1);
            currentEditRow = currentTable.fnGetNodes(tabRowId - 1);
        }
        if (testing[0] == undefined) {
            if (editFlag) {
                editEduCurrentEle = element;
                isDirtyFormCandidates = true;
                updateVal(element, value, tabName, editColId, tabRowId);
            }
            else {
                currentEle = element;

                updateVal(element, value, tabName, colId, tabRowId);
            }
        }
    }


    //Update value in Inlineedit
    function updateVal(currentElement, value, tabName, columnId, rowId) {
        currentEle = currentElement;
        if (tabName == 'edu') {
            if (columnId == 0) {
                listCourses(rowId);
                // Set currently edited rowId values globally
                currentEditRowId = rowId;
                // consider this
                // $('select option[value="1"]').attr("selected",true);
                $(currentEle).html('<select name="academy "id= "academy' + rowId + '"class="tab-fileds pagerlink form-control  vRequired inlineedit"> <option value="" disabled selected>Select</option></select>')
            }
            else if (columnId == 1) {
                $(currentEle).html('<input type="text" name="institutename" id= "institutename' + rowId + '"class="pagerlink form-control  vRequired inlineedit" value="' + value + '" />');
            }
            else if (columnId == 2) {
                $(currentEle).html('<input type="text" name="course" id= "course' + rowId + '"class="tab-fileds form-control vRequired inlineedit" value="' + value + '" />');
            }
            else if (columnId == 3) {
                $(currentEle).html('<input type="number" name="year" min="1900" maxlength="4" max="2050" id= "year' + rowId + '"class="tab-fileds form-control  vRequired vYearPassing inlineedit" value="' + value + '" />');
            }
            else {
                $(currentEle).html('<input type="number" step="0.01" name="grade min="0" max = 100 id= "grade' + rowId + '" class="tab-fileds form-control  vRequired inlineedit" value="' + value + '" />');
            }
        }
        else if (tabName == 'exp') {
            if (columnId == 0) {
                $(currentEle).html('<input type="text" name="company" id= "company' + rowId + '"class="form-control alSpDotAndHypen vName inlineedit" value="' + value + '" />');
            }
            else if (columnId == 1) {
                $(currentEle).html('<input type="text" name="designation" id= "designation' + rowId + '"class="tab-fileds form-control vName inlineedit" value="' + value + '" />');
            }
            else if (columnId == 2) {

                $(currentEle).html('<input type="text" name="startyear" id= "startyear' + rowId + '" data-mask="99/99/9999" class="tab-fileds form-control vRequired vDateMonth vExperienceStartDate  inlineedit" placeholder="DD/MM/YYYY" value="' + value + '"></input>');
            }
            else {
                $(currentEle).html('<input type="text"  name="endyear" id= "endyear' + rowId + '" data-mask="99/99/9999" class="tab-fileds form-control vRequired vDateMonth vExperienceEndDate inlineedit" placeholder="DD/MM/YYYY" value="' + value + '"></input>');
            }
        }
        else if (tabName == 'ski') {
            if (columnId == 0) {
                $(currentEle).html('<input type="text" name="skills" id= "skills' + rowId + '"class="form-control  vRequired inlineedit" value="' + value + '" />');
            }
            else if (columnId == 1) {
                $(currentEle).html('<input type="text" name="description" maxlength="500" id= "description' + rowId + '"class="tab-fileds form-control  vRequired inlineedit" value="' + value + '" />');
            }
            else if (columnId == 2) {
                $(currentEle).html('<input type="text" name="proficiency" id= "proficiency' + rowId + '"class="tab-fileds form-control  vRequired inlineedit" value="' + value + '" />');
            }
            else {
                $(currentEle).html('<input type="text" name="primary" id= "primary' + rowId + '"class="tab-fileds form-control  vRequired inlineedit" value="' + value + '" />');
            }
        }
        else {
            if (columnId == 0) {
                $(currentEle).html('<input type="text" name="certificationname" maxlength="50" id= "certificationname' + rowId + '"class="tab-fileds form-control inlineedit" value="' + value + '" />');
            }
            else if (columnId == 1) {

                $(currentEle).html('<input type="text"  id= "certificationsreceiveddate' + rowId + '" data-mask="99/99/9999" class="tab-fileds form-control form-control inlineedit vCerificationReceivedDate vDateMonth vRequired" placeholder="DD/MM/YYYY" value="' + value + '"></input>');
            }
            else {
                $(currentEle).html('<input type="text" name="certificatereceivedfrom" maxlength="50" id= "certificatereceivedfrom' + rowId + '"class="tab-fileds form-control inlineedit" value="' + value + '" />');
            }
        }

        $(".inlineedit", currentEle).focus().keyup(function (event) {
        }).on('click', function (e) {
            e.stopPropagation();
        });

    }
    // Inline edito onclick
    $(document).on('click', function() {
        $(".inlineedit").replaceWith(function () {
            if (editFlag) {
                colId = editColId
            }
            // Save data in Jquerydatatable
            if (tabName == 'edu') {
                if($('#educationForm').valid()){
                    if (colId == 0) {
                        // Get drop down Id using text
                        var dropDownText = '';
                        for (var i = 0; i <= courseList.length - 1; i++) {
                            if (courseList[i].Course_Id == $('#academy' + currentEditRowId).val()) {
                                dropDownText = courseList[i].Course_Name;
                            }
                        }
                        oTable1.fnUpdate(dropDownText, currentEditRow, 0, true);
                        oTable1.fnDraw();
                        return dropDownText;
                    }
                    else if (colId == 1) {
                        oTable1.fnUpdate(this.value, currentEditRow, 1, false);
                        oTable1.fnDraw();
                        return this.value;
                    }
                    else if (colId == 2) {
                        oTable1.fnUpdate(this.value, currentEditRow, 2, false);
                        oTable1.fnDraw();
                        return this.value;
                    }
                    else if (colId == 3) {
                        oTable1.fnUpdate(this.value, currentEditRow, 3, false);
                        oTable1.fnDraw();
                        return this.value;
                    }
                    else if (colId == 4) {
                        oTable1.fnUpdate(this.value, currentEditRow, 4, false);
                        oTable1.fnDraw();
                        return this.value;
                    }
                }
                else 
                {
                    var value = this.value;
                    var inlineid = $(this).closest('td').prop('id');
                    var testing = [];
                    testing[0] = undefined;

                    var element = document.getElementById(inlineid);
                    tab = 1;

                    var currentTable = oTable1;
                    
                    formInlineEdit(tabName,tab,value,inlineid,testing,element,currentTable);
                    $('#educationForm').valid();

                }
            }
            else if (tabName == 'exp') {
                if($('#experienceForm').valid())
                {
                    if (colId == 0) {
                        oTable2.fnUpdate(this.value, currentEditRow, 0, true);
                        oTable2.fnDraw();
                        return this.value;
                    }
                    else if (colId == 1) {
                        oTable2.fnUpdate(this.value, currentEditRow, 1, false);
                        oTable2.fnDraw();
                        return this.value;
                    }
                    else if (colId == 2) {
                        oTable2.fnUpdate(this.value, currentEditRow, 2, false);
                        oTable2.fnDraw();
                        return this.value;
                    }
                    else if (colId == 3) {
                        oTable2.fnUpdate(this.value, currentEditRow, 3, false);
                        oTable2.fnDraw();
                        return this.value;
                    }
                }
                else 
                {
                    var value = this.value;
                        var inlineid = $(this).closest('td').prop('id');;
                        var testing = [];
                        testing[0] = undefined;

                        var element = document.getElementById(inlineid);
                        tab = 1;
                        var currentTable = oTable1;
                    
                        formInlineEdit(tabName,tab,value,inlineid,testing,element,currentTable);
                            $('#experienceForm').valid();
                }
            }
            else if (tabName == 'ski') {
                if($('#skillForm').valid()) {
                    if (colId == 0) {
                        oTable3.fnUpdate(this.value, currentEditRow, 0, true);
                        oTable3.fnDraw();
                        return this.value;
                    }
                    else if (colId == 1) {
                        oTable3.fnUpdate(this.value, currentEditRow, 1, false);
                        oTable3.fnDraw();
                        return this.value;
                    }
                    else if (colId == 2) {
                        oTable3.fnUpdate(this.value, currentEditRow, 2, false);
                        oTable3.fnDraw();
                        return this.value;
                    }
                    else if (colId == 3) {
                        oTable3.fnUpdate(this.value, currentEditRow, 3, false);
                        oTable3.fnDraw();
                        return this.value;
                    }
                 }
                 else 
                 {
                    var value = this.value;
                    var inlineid = $(this).closest('td').prop('id');;
                    var testing = [];
                    testing[0] = undefined;

                    var element = document.getElementById(inlineid);
                    tab = 1;
                    var currentTable = oTable1;
                    
                    formInlineEdit(tabName,tab,value,inlineid,testing,element,currentTable);
                    $('#skillForm').valid();
                 }

            }
            else {
                if($('#certificateForm').valid()) {
                    if (colId == 0) {
                        oTable4.fnUpdate(this.value, currentEditRow, 0, true);
                        oTable4.fnDraw();
                        return this.value;
                    }
                    else if (colId == 1) {
                        oTable4.fnUpdate(this.value, currentEditRow, 1, false);
                        oTable4.fnDraw();
                        return this.value;
                    }
                    else if (colId == 2) {
                        oTable4.fnUpdate(this.value, currentEditRow, 2, false);
                        oTable4.fnDraw();
                        return this.value;
                    }
                }
                else 
                {
                    var value = this.value;
                    var inlineid = $(this).closest('td').prop('id');;
                    var testing = [];
                    testing[0] = undefined;

                    var element = document.getElementById(inlineid);
                    tab = 1;
                    var currentTable = oTable1;
                    
                    formInlineEdit(tabName,tab,value,inlineid,testing,element,currentTable);
                    $('#certificateForm').valid();
                }
            }
        });
    });

    //Enter key submit for Inlineedit
    $(document).on('keypress', function (e) {
        if (e.keyCode == 13) {
            $(".inlineedit").replaceWith(function () {
                return this.value;
            });
        }
    });
    /**add or remove validation errors from backend */
    function setValidationErrors(action, fieldId, message) {
        if (action === 'add') {
            $('#' + fieldId).addClass('validation');
            $.validator.addMethod(
                fieldId,
                function () {
                    return false;
                },
                message
            );
            var id = fieldId.replace(/"/g, '');
            $.validator.addClassRules('validation', { [id]: true });

            $('#' + fieldId).valid();
        } else {
            $('#' + fieldId).removeClass('validation');
            $.validator.addMethod(
                fieldId,
                function () {
                    return true;
                },
                ''
            );
            var id = fieldId.replace(/"/g, '');
            $.validator.addClassRules('validation', { [id]: false });

            $('#' + fieldId).valid();
        }
    }


   

function submitJobCandidates(tab){
    getValuesFromDataTable();

    if(addFlag && tab ===1)
    {

        var mutation = `mutation CommentQuery($salutation: String!,$firstName:String!,$lastName:String!,$gender: String!,$dob: String!,
            $bloodGroup: String,$maritalStatus: String,$nationality:String,$emailId:String!,$languagesKnown:[String]!,
            $mobileNo:String!,$apartmentName:String,$street:String,$city:String,$state:String,$country:String,$pincode:String,
            $preferredLocation:[Int],$candidateEducation:[educationDetails],$candidateExperience:[experienceDetails],
            $candidateSkills:[skillDetails],$passportNo:String,$candidateDependent:[String],
            $candidateCertification: [certificationDetails],$candidateWorkPermit:[Int],$candidateOtherWorkPermit:String,
            $verifierName:String,$verifierPhoneNo:String,$verifierEmailId:String,$jobPost:Int,$currentEmployer:String,
            $noticePeriod:Int,$currentCTC:Float,$expectedCTC:Float,$currency:Int,$resume:String!,$nationalIdentificationNumber:String,
            $candidateProfilePicture:String,$resumeFileSize: String!,$status:Int!) 
            { insertJobCandidates(salutation: $salutation,firstName:$firstName,lastName:$lastName,gender: $gender,dob:$dob,
                bloodGroup:$bloodGroup, maritalStatus:$maritalStatus,nationality:$nationality,emailId:$emailId,
                languagesKnown:$languagesKnown,mobileNo:$mobileNo,apartmentName:$apartmentName,street:$street,
                city:$city,state:$state,country:$country,pincode:$pincode,preferredLocation:$preferredLocation,
                candidateEducation:$candidateEducation,candidateExperience:$candidateExperience,candidateSkills:$candidateSkills,
                passportNo:$passportNo,candidateDependent:$candidateDependent,candidateCertification:$candidateCertification,
                candidateWorkPermit:$candidateWorkPermit,candidateOtherWorkPermit:$candidateOtherWorkPermit,verifierName:$verifierName,
                verifierPhoneNo:$verifierPhoneNo,verifierEmailId:$verifierEmailId,jobPost:$jobPost,currentEmployer:$currentEmployer,
                noticePeriod:$noticePeriod,currentCTC:$currentCTC,expectedCTC:$expectedCTC,currency:$currency,resume:$resume,
                nationalIdentificationNumber:$nationalIdentificationNumber,candidateProfilePicture:$candidateProfilePicture,
                resumeFileSize:$resumeFileSize,status:$status) {errorCode message validationError data}
            }`;

            var preferredLocation =[];
            var workPermit = [];
            
            preferredLocation = $('#PreferredLocation').select2('val').map(function(v) {
                return parseInt(v, 10);
            });
            
            workPermit = $('#WorkPermit').select2('val').map(function(v) {
                return parseInt(v, 10);
            });
        
            //  Form dependent details
            var dependentDetails = [];
            if($('#FatherName').val()) {
                dependentDetails.push($('#FatherName').val());
                if($('#MotherName').val()) {
                    dependentDetails.push($('#MotherName').val());
                }
                else 
                {
                    dependentDetails.push('');

                }
                
            }
            else if($('#MotherName').val())
            {
                dependentDetails.push('');
                dependentDetails.push($('#MotherName').val());

            }            var variables = {
                "salutation": $('#Salutation').val(),
                "firstName": $('#firstName').val(),
                "lastName": $('#lastName').val(),
                "gender": $('#Gender').val(),
                "dob": fnServerDateFormatter($('#CandidateDOB').datepicker('getDate')),
                "bloodGroup": $('#BloodGroup').val(),
                "maritalStatus": $('#MaritalStatus').val(),
                "nationality": $('#Nationality').val(),
                "emailId": $('#email').val(),
                "languagesKnown": jQuery.uniqueSort($('#LanguagesKnown').val()),
                "mobileNo": $('#contactNumber').val(),
                "apartmentName": $('#ApartmentName').val(),
                "street": $('#Street').val(),
                "city": $('#City').val(),
                "state": $('#State').val(),
                "country": $('#Country').val(),
                "pincode": $('#PinCode').val(),
                "preferredLocation": preferredLocation,
                "candidateEducation": educationDetailsArray,
                "candidateExperience": experienceDetailsArray,
                "passportNo": $('#PassportNumber').val() ? $('#PassportNumber').val() : "",
                "candidateDependent": dependentDetails,
                "candidateCertification": certificationsDetailsArray,
                "candidateSkills": skillsDetailsArray,
                "candidateWorkPermit": workPermit,
                "verifierName": $('#VerifierName').val() ? $('#VerifierName').val() : "",
                "verifierPhoneNo": $('#VerifierPhoneNumber').val() ? $('#VerifierPhoneNumber').val() : "",
                "verifierEmailId": $('#VerifierEmailId').val() ? $('#VerifierEmailId').val() : "",
                "jobPost": parseInt($('#Jobpost').val()),
                "currentEmployer": $('#currentemployer').val(),
                "noticePeriod": parseFloat($('#NoticePeriod').val()),
                "currentCTC": parseFloat($('#CurrentCTC').val()), 
                "expectedCTC": parseFloat($('#ExpectedCTC').val()),
                "resume": resumeFileName,
                "nationalIdentificationNumber"  : $('#NationalIdentificationNumber').val() ? $('#NationalIdentificationNumber').val() : "",
                "candidateProfilePicture": profileFileName,
                "resumeFileSize": resumeFileSize,
                "currency"                    : parseInt($('#s2id_currency').select2('val')),
                "candidateOtherWorkPermit"      : $('#otherworkpermits').val(),
                "status"                :    10,
            };

    }
    else
    {
        var mutation = `
                        mutation CommentQuery(
                            $candidateId: Int!
                            $salutation: String!
                            $firstName: String!
                            $lastName: String!
                            $gender: String!
                            $dob: String!
                            $bloodGroup: String
                            $maritalStatus: String
                            $nationality: String
                            $emailId: String!
                            $languagesKnown: [String]!
                            $mobileNo: String!
                            $apartmentName: String
                            $street: String
                            $city: String
                            $state: String
                            $country: String
                            $pincode: String
                            $preferredLocation: [Int]
                            $candidateEducation: [educationDetails]
                            $candidateExperience: [experienceDetails]
                            $candidateSkills: [skillDetails]
                            $passportNo: String
                            $candidateDependent: [String]
                            $candidateCertification: [certificationDetails]
                            $candidateWorkPermit: [Int]
                            $candidateOtherWorkPermit: String
                            $verifierName: String
                            $verifierPhoneNo: String
                            $verifierEmailId: String
                            $jobPost: Int
                            $currentEmployer: String
                            $noticePeriod: Int
                            $currentCTC: Float
                            $expectedCTC: Float
                            $currency: Int
                            $resume: String!
                            $nationalIdentificationNumber: String
                            $candidateProfilePicture: String
                            $resumeFileSize: String!
                            $status: Int!
                            $totalExperienceInYears: Int
                            $totalExperienceInMonths: Int
                            $source: String
                        ) {
                            updateJobCandidates(
                            candidateId: $candidateId
                            salutation: $salutation
                            firstName: $firstName
                            lastName: $lastName
                            gender: $gender
                            dob: $dob
                            bloodGroup: $bloodGroup
                            maritalStatus: $maritalStatus
                            nationality: $nationality
                            emailId: $emailId
                            languagesKnown: $languagesKnown
                            mobileNo: $mobileNo
                            apartmentName: $apartmentName
                            street: $street
                            city: $city
                            state: $state
                            country: $country
                            pincode: $pincode
                            preferredLocation: $preferredLocation
                            candidateEducation: $candidateEducation
                            candidateExperience: $candidateExperience
                            candidateSkills: $candidateSkills
                            passportNo: $passportNo
                            candidateDependent: $candidateDependent
                            candidateCertification: $candidateCertification
                            candidateWorkPermit: $candidateWorkPermit
                            candidateOtherWorkPermit: $candidateOtherWorkPermit
                            verifierName: $verifierName
                            verifierPhoneNo: $verifierPhoneNo
                            verifierEmailId: $verifierEmailId
                            jobPost: $jobPost
                            currentEmployer: $currentEmployer
                            noticePeriod: $noticePeriod
                            currentCTC: $currentCTC
                            expectedCTC: $expectedCTC
                            currency: $currency
                            resume: $resume
                            nationalIdentificationNumber: $nationalIdentificationNumber
                            candidateProfilePicture: $candidateProfilePicture
                            resumeFileSize: $resumeFileSize
                            status: $status
                            totalExperienceInYears: $totalExperienceInYears
                            totalExperienceInMonths: $totalExperienceInMonths
                            source: $source
                            ) {
                            errorCode
                            message
                            validationError
                            }
                        }
                    `;
                var preferredLocation =[];
                var workPermit = [];
                
                preferredLocation = $('#PreferredLocation').select2('val').map(function(v) {
                    return parseInt(v, 10);
                });
                
                workPermit = $('#WorkPermit').select2('val').map(function(v) {
                    return parseInt(v, 10);
                });
                //  Form dependent details
                var dependentDetails = [];
                if($('#FatherName').val()) {
                    dependentDetails.push($('#FatherName').val());
                    if($('#MotherName').val()) {
                        dependentDetails.push($('#MotherName').val());
                    }
                    else 
                    {
                        dependentDetails.push('');

                    }
                    
                }
                else if($('#MotherName').val())
                {
                    dependentDetails.push('');
                    dependentDetails.push($('#MotherName').val());
  
                }

                var variables = {
                    "candidateId": (addFlag && tab ===2) ? lastInsertedId :  parseInt(candidateId),
                    "salutation": $('#Salutation').val(),
                    "firstName": $('#firstName').val(),
                    "lastName": $('#lastName').val(),
                    "gender": $('#Gender').val(),
                    "dob": fnServerDateFormatter($('#CandidateDOB').datepicker('getDate')),
                    "bloodGroup": $('#BloodGroup').val(),
                    "maritalStatus": $('#MaritalStatus').val(),
                    "nationality": $('#Nationality').val(),
                    "emailId": $('#email').val(),
                    "languagesKnown": jQuery.uniqueSort($('#LanguagesKnown').val()),
                    "mobileNo": $('#contactNumber').val(),
                    "apartmentName": $('#ApartmentName').val(),
                    "street": $('#Street').val(),
                    "city": $('#City').val(),
                    "state": $('#State').val(),
                    "country": $('#Country').val(),
                    "pincode": $('#PinCode').val(),
                    "preferredLocation": preferredLocation,
                    "candidateEducation": educationDetailsArray,
                    "candidateExperience": experienceDetailsArray,
                    "passportNo": $('#PassportNumber').val() ? $('#PassportNumber').val() : "",
                    "candidateDependent": dependentDetails,
                    "candidateCertification": certificationsDetailsArray,
                    "candidateSkills": skillsDetailsArray,
                    "candidateWorkPermit" : workPermit, 
                    "candidateOtherWorkPermit" : "",
                    "verifierName": $('#VerifierName').val() ? $('#VerifierName').val() : "",
                    "verifierPhoneNo": $('#VerifierPhoneNumber').val() ? $('#VerifierPhoneNumber').val() : "",
                    "verifierEmailId": $('#VerifierEmailId').val() ? $('#VerifierEmailId').val() : "",
                    "jobPost" : parseInt($('#Jobpost').val()),
                    "currentEmployer": $('#currentemployer').val(),
                    "noticePeriod": parseInt($('#NoticePeriod').val()) ? parseInt($('#NoticePeriod').val()) : null,
                    "currentCTC": parseInt($('#CurrentCTC').val()) ? parseInt($('#CurrentCTC').val()) : null,
                    "expectedCTC": parseInt($('#ExpectedCTC').val()) ? parseInt($('#ExpectedCTC').val()) : null,
                    "resume": resumeFileName,
                    "nationalIdentificationNumber": $('#NationalIdentificationNumber').val() ? $('#NationalIdentificationNumber').val() : "",
                    "candidateProfilePicture": profileFileName,
                    "resumeFileSize": resumeFileSize,
                    "currency"                    : parseInt($('#s2id_currency').select2('val')),
                    "otherWorkAuthorization"      : $('#otherworkpermits').val(),
                    "status"                      : candidateNewStatus,
                    "totalExperienceInYears": 0,
                    "totalExperienceInMonths": 0,
                    "source": ""
                };
    }
    setMask('#wholepage');

    /**add/update client data in backend */
    $.ajax({
        method: 'POST',
        url: atsBaseURL,
        contentType: 'application/json',
        headers: getGraphqlAPIHeaders(ipAddress),
        data: JSON.stringify({
            query: mutation,
            variables: variables
        }),
        success: function (result) {
            if (!result.data) {
                // empty the array details if any error occurs
                educationDetailsArray = [];
                experienceDetailsArray = [];
                skillsDetailsArray = [];
                certificationsDetailsArray = [];
                /**failure response */
                var error = JSON.parse(result.errors[0].message);
                var errorCode = error.errorCode;
                $(window).scrollTop(0);
                switch (errorCode) {
                    case 900 /**input validation */:
                        displayValidationErrors(error.validationError);
                        break;
                    case 705:
                    case 706:
                        commonAlertMessage($('#addJobCandidatesPanel'));
                        break;
                    case 719:
                        commonAccessDeniedAlert('#addJobCandidatesPanel');
                    case 720:
                    default:
                        errorAlertMessage($('#addJobCandidatesPanel'));
                        break;
                }
                removeMask();
            }
            else {
                removeMask();

                // false the isDirtyFormCandidates in form submit success
                if(tab===1)
                {
                        $('#tab1').removeClass('active');
                        $('#tab5_1').removeClass('active');

                        $('#tab3').removeClass('active');
                        $('#tab5_3').removeClass('active');
                
                        $('#tab2').addClass('active');
                        $('#tab5_2').addClass('active');
                        if(editFlag)
                        {
                            isDirtyFormCandidates = true;

                            jAlert({
                                panel: $('#addJobCandidatesPanel'),
                                msg: "Candidate's basic details updated successfully",
                                type: 'info'
                            });
                        }
                        else {
                            lastInsertedId = result.data.insertJobCandidates.data;
                            addByTab = 1;
                            isDirtyFormCandidates = true;

                            jAlert({
                                panel: $('#addJobCandidatesPanel'),
                                msg: "Candidate's basic details added successfully",
                                type: 'info'
                            });
                        }
                }
                else
                {
                    isDirtyFormCandidates = false;
                    
                    $(window).scrollTop(0);

                    $('#AddEditJobCandidates').hide();
                    $('#listJobCandidates').show();
                    listJobCandidates(0);
                }
            }
        },
        error: function (result) {
            removeMask();
            errorAlertMessage($('#addJobCandidatesPanel'));
        }
    });
           
}

$('#tab1').on('click',function(){
    
        isDirtyFormCandidates = 0;
        $('#tab2').removeClass('active');
        $('#tab5_2').removeClass('active');

        $('#tab3').removeClass('active');
        $('#tab5_3').removeClass('active');


        $('#tab1').addClass('active');
        $('#tab5_1').addClass('active');
    
});


// when tab 2 -> bio data tab is clicked
$('#tab2').on('click',function(){
    if(addFlag)
    {

        // check if the form is dirty
        if(isDirtyFormCandidates)
        {
            // if first tab is already saved
            if(addByTab) {
                // update candidates 
                editFlag = 1;
                addCandidateDetails();
                editFlag = 0;
            }
            else 
            {
                // add candidates
                addCandidateDetails();
            }
        }
        else 
        {
            if(addByTab)
            {
                // show tab 2 -> Biodata
                $('#tab1').removeClass('active');
                $('#tab5_1').removeClass('active');
    
                $('#tab2').addClass('active');
                $('#tab5_2').addClass('active');

                $('#tab3').removeClass('active');
                $('#tab5_3').removeClass('active');
        
            }
            else 
            {
                // show tab 1 -> Basic
                $('#tab2').removeClass('active');
                $('#tab5_2').removeClass('active');
    
                $('#tab1').addClass('active');
                $('#tab5_1').addClass('active');

                $('#tab3').removeClass('active');
                $('#tab5_3').removeClass('active');
        
                $(window).scrollTop(0);
                jAlert({ panel: $('#addJobCandidatesPanel'), msg: 'Form has no changes', type: 'info' });
   
            }
            isDirtyFormCandidates = 0;

        }
    }
    else 
    {
        // if form is dirty
        if(isDirtyFormCandidates)
        {
            // update data
            addCandidateDetails();
        }
        else 
        {
            // show tab 2 -> Biodata
            $('#tab1').removeClass('active');
            $('#tab5_1').removeClass('active');
            $('#tab2').addClass('active');
            $('#tab5_2').addClass('active');
            $('#tab3').removeClass('active');
            $('#tab5_3').removeClass('active');
    
            isDirtyFormCandidates = false;

        }

        
    }
});


function addCandidateDetails()
{
    removeFormClassErrorForForms('#formJobCandidateBasicData');
         // Validate invisible tabs
         $("#educationForm").validate({
            ignore: ""
        });
        // Validate invisible tabs
        $("#skillForm").validate({
            ignore: ""
        });
        var basicTabValid = $('#formJobCandidateBasicData').valid();
        var eduTabValid = $('#educationForm').valid();
        var skillTabValid = $('#skillForm').valid();
        var resumePath;
        if (eduTabValid && skillTabValid && basicTabValid) {
            if (addFlag) {

                if (AddResumeFileNameArray.length === 0) {
                    removeMask();
                    $(window).scrollTop(0);
                    /**error while ajax call */
                    jAlert({
                        panel: $('#addJobCandidatesPanel'),
                        msg: 'Please upload resume',
                        type: 'warning'
                    });

                    $('#tab1').addClass('active');
                    $('#tab5_1').addClass('active');
                    $('#tab2').removeClass('active');
                    $('#tab5_2').removeClass('active');

                    // //  ---> scoreupdation
                    // $('#tab3').removeClass('active');
                    // $('#tab5_3').removeClass('active');
            
        
                }
                else {
                    // // make the Address field validation as required
                    // $('#ApartmentName,#Street,#City,#State,#PinCode,#Country').addClass('vRequired');
                    if (AddProfileFileNameArray.length === 0) {
                        profileFileName = "";
                        resumeFileName = creates3FileName('resume', AddResumeFileNameArray);
                        resumePath = prepareS3Path('resume', resumeFileName, domainName, orgCode, 'Job Candidates', 1);
                    }
                    else {
                        profileFileName = creates3FileName('profile', AddProfileFileNameArray);
                        resumeFileName = creates3FileName('resume', AddResumeFileNameArray);
                        resumePath = prepareS3Path('resume', resumeFileName, domainName, orgCode, 'Job Candidates', 1);
                    }
                    if(resumeFileName){
                        // To create a space in S3 for given path 
                        var sUrl = fngetSignedPutUrl (domainName+"/"+orgCode+"/"+"resume/"+resumeFileName,'bucketName');
                        if(sUrl){
                            deletePhpActionHeaders();
                            $.ajax({
                                url: sUrl,
                                type: 'PUT',
                                contentType: AddResumeFileNameArray[0].type,
                                processData: false,
                                data: AddResumeFileNameArray[0],
                                }).done(function(err,res ) {
                                    if(res == "success"){
                                        jAlert ({ panel : $('#addJobCandidatesPanel'), msg : 'Document uploaded successfully.', type : 'info' });
                                    }
                                    else{
                                        jAlert ({ panel : $('#addJobCandidatesPanel'), msg : 'Unable to upload the document', type : 'warning' });
                                    }
                                })
                            setPhpActionHeaders();
                        }
                    }
                    var tab = 1;

                    submitJobCandidates(tab);
                }
                
            }
            else if(editFlag)
            {
                if (changeResumeFlag) {
                    if (AddResumeFileNameArray.length == 0) {
                        removeMask();
                        $(window).scrollTop(0);
                        /**error while ajax call */
                        jAlert({
                            panel: $('#addJobCandidatesPanel'),
                            msg: 'Please upload resume',
                            type: 'warning'
                        });
                        $('#tab1').addClass('active');
                        $('#tab5_1').addClass('active');
                        $('#tab2').removeClass('active');
                        $('#tab5_2').removeClass('active');

                        //  ----> scoreupdation
                        // $('#tab3').addClass('active');
                        // $('#tab5_3').addClass('active');
                
                    } else {
                        var res = fndeleteS3Document(domainName+"/"+orgCode+"/"+"resume/"+prefillCandidateInfo.Resume, 'bucketName');
                        if (res && res == 'Document deleted') {
                            jAlert ({ panel : $('#addJobCandidatesPanel'), msg : 'Document deleted successfully', type : 'info' });
                        }
                        else{
                            jAlert ({ panel : $('#addJobCandidatesPanel'), msg : 'Unable to delete document', type : 'warning' });
                        }
                        if (changeProfileFlag) {
                            if (AddProfileFileNameArray.length > 0) {
                                profileFileName = creates3FileName('profile', AddProfileFileNameArray);
                                resumeFileName = creates3FileName('resume', AddResumeFileNameArray);
                                resumePath = prepareS3Path('resume', resumeFileName, domainName, orgCode, 'Job Candidates', 1);
                            }
                            else {
                                profileFileName = "";
                                resumeFileName = creates3FileName('resume', AddResumeFileNameArray);
                                resumePath = prepareS3Path('resume', resumeFileName, domainName, orgCode, 'Job Candidates', 1);
                            }
                        }
                        else if (removeProfileFlag) {
                            profileFileName = '';
                            resumeFileName = creates3FileName('resume', AddResumeFileNameArray);
                            resumePath = prepareS3Path('resume', resumeFileName, domainName, orgCode, 'Job Candidates', 1);
                        }
                        else {
                            profileFileName = prefillCandidateInfo.Photo_Path ? prefillCandidateInfo.Photo_Path : '';
                            resumeFileName = creates3FileName('resume', AddResumeFileNameArray);
                            resumePath = prepareS3Path('resume', resumeFileName, domainName, orgCode, 'Job Candidates', 1);
                        }
                        if(resumeFileName){
                            // To create a space in S3 for given path 
                            var sUrl = fngetSignedPutUrl (domainName+"/"+orgCode+"/"+"resume/"+resumeFileName,'bucketName');
                            if(sUrl){
                                deletePhpActionHeaders();
                                $.ajax({
                                    url: sUrl,
                                    type: 'PUT',
                                    contentType: AddResumeFileNameArray[0].type,
                                    processData: false,
                                    data: AddResumeFileNameArray[0],
                                    }).done(function(err,res ) {
                                        if(res == "success"){
                                            jAlert ({ panel : $('#addJobCandidatesPanel'), msg : 'Document uploaded successfully.', type : 'info' });
                                        }
                                        else{
                                            jAlert ({ panel : $('#addJobCandidatesPanel'), msg : 'Unable to upload the document', type : 'warning' });
                                        }
                                    })
                                setPhpActionHeaders();
                            }
                        }
                        var tab = 1;

                        submitJobCandidates(tab);

                    }
                    changeResumeFlag = 0;
                }
                else {
                    if (changeProfileFlag) {
                        if (AddProfileFileNameArray.length > 0) {
                            profileFileName = creates3FileName('profile', AddProfileFileNameArray);
                            resumeFileName = prefillCandidateInfo.Resume;
                            resumeFileSize = prefillCandidateInfo.Resume_File_Size;

                        }
                        else {
                            profileFileName = prefillCandidateInfo.Photo_Path ? prefillCandidateInfo.Photo_Path : '';
                            resumeFileName = prefillCandidateInfo.Resume;
                            resumeFileSize = prefillCandidateInfo.Resume_File_Size;
                        }
                    }
                    else if (removeProfileFlag) {
                        profileFileName = '';
                        resumeFileName = prefillCandidateInfo.Resume;
                        resumeFileSize = prefillCandidateInfo.Resume_File_Size;
                    }
                    else {
                        profileFileName = prefillCandidateInfo.Photo_Path ? prefillCandidateInfo.Photo_Path : '';
                        resumeFileName = prefillCandidateInfo.Resume;
                        resumeFileSize = prefillCandidateInfo.Resume_File_Size;
                    }
                    // call function to update the candidate details
                    // updateJobCandidates();
                    var tab = 1;
                    submitJobCandidates(tab);

                }
            }

        }
        else 
        {
            $('#tab1').addClass('active');
            $('#tab5_1').addClass('active');
            $('#tab2').removeClass('active');
            $('#tab5_2').removeClass('active');

            // score updation
            $('#tab3').removeClass('active');
            $('#tab5_3').removeClass('active');
    

        }
}


// submit first tab
$('#jobCandidateSubmit1').on('click',function(){
    var l = Ladda.create(this);

    if(addFlag)
    {
        if(isDirtyFormCandidates)
        {
            if(addByTab) {
                editFlag = 1;
                addCandidateDetails();
                editFlag = 0;
            }
            else 
            {
                addCandidateDetails();
            }
        }
        else 
        {
            if(addByTab)
            {
                $('#tab1').removeClass('active');
                $('#tab5_1').removeClass('active');
    
                $('#tab2').addClass('active');
                $('#tab5_2').addClass('active');

                $('#tab3').removeClass('active');
                $('#tab5_3').removeClass('active');
        
            }
            else 
            {
                $('#tab2').removeClass('active');
                $('#tab5_2').removeClass('active');
    
                $('#tab1').addClass('active');
                $('#tab5_1').addClass('active');

                $('#tab3').removeClass('active');
                $('#tab5_3').removeClass('active');
        
                $(window).scrollTop(0);
                jAlert({ panel: $('#addJobCandidatesPanel'), msg: 'Form has no changes', type: 'info' });
   
            }
            isDirtyFormCandidates = 0;

        }
        

    }
    else 
    {
        if(isDirtyFormCandidates)
        {
            addCandidateDetails();
        }
        else 
        {
            $('#tab1').removeClass('active');
            $('#tab5_1').removeClass('active');
            $('#tab2').addClass('active');
            $('#tab5_2').addClass('active');

            $('#tab3').removeClass('active');
            $('#tab5_3').removeClass('active');
    
            isDirtyFormCandidates = false;

        }
    }





});

// submit secind tab
$('#jobCandidateSubmit2').on('click',function(){
        removeFormClassErrorForForms('#formJobCandidateBioData');
        var bioDataTabValid = $('#formJobCandidateBioData').valid();
        if (bioDataTabValid) {
            var tab = 2;
            submitJobCandidates(tab);


        }


});



    // Get values from data table
    function getValuesFromDataTable() {
        var educationTable = $('#table-editable1').DataTable();
        var educationTableData = educationTable.rows().data();
        educationDetailsArray = [];
        if (educationTableData.length > 0) {
            for (var i = 0; i <= educationTableData.length - 1; i++) {
                var courseListId = '';
                for (var j = 0; j <= courseList.length - 1; j++) {
                    if (educationTableData[i][0] == courseList[j].Course_Name) {
                        courseListId = courseList[j].Course_Id
                    }
                }
                educationJson['educationType'] = courseListId;
                educationJson['institute'] = educationTableData[i][1];
                educationJson['speacialisation'] = educationTableData[i][2];
                educationJson['yearOfPassing'] = parseInt(educationTableData[i][3]);
                educationJson['percentage'] = parseInt(educationTableData[i][4]);
                educationDetailsArray.push(educationJson);
                educationJson = {};
            }
        }

        var experienceTable = $('#table-editable2').DataTable();
        var experienceTableData = experienceTable.rows().data();
        experienceDetailsArray = [];
        if (experienceTableData.length > 1) {
            for (var i = 0; i <= experienceTableData.length - 1; i++) {
                experienceJson['companyName'] = experienceTableData[i][0];
                experienceJson['designation'] = experienceTableData[i][1];
                experienceJson['startDate'] = experienceTableData[i][2];
                experienceJson['endDate'] = experienceTableData[i][3];
                experienceDetailsArray.push(experienceJson);
                experienceJson = {};
            }
        }
        else if (experienceTableData.length == 1) {
            experienceTableData = experienceTableData[0];
            // Check whether the experience data is exist or not
            if (experienceTableData[0] && experienceTableData[2] && experienceTableData[3] && experienceTableData[4]) {
                experienceJson['companyName'] = experienceTableData[0];
                experienceJson['designation'] = experienceTableData[1];
                experienceJson['startDate'] = experienceTableData[2];
                experienceJson['endDate'] = experienceTableData[3];
                experienceDetailsArray.push(experienceJson);
                experienceJson = {};
            }
        }

        var skillsTable = $('#table-editable3').DataTable();
        var skillsTableData = skillsTable.rows().data();
        skillsDetailsArray = [];
        if (skillsTableData.length > 0) {
            for (var i = 0; i <= skillsTableData.length - 1; i++) {
                skillsJson['skills'] = skillsTableData[i][0];
                skillsJson['description'] = skillsTableData[i][1];
                skillsJson['proficiency'] = skillsTableData[i][2];
                skillsJson['primary'] = skillsTableData[i][3];
                skillsDetailsArray.push(skillsJson);
                skillsJson = {};
            }
        }

        var certificationsTable = $('#table-editable4').DataTable();
        var certificationsTableData = certificationsTable.rows().data();
        certificationsDetailsArray = [];
        if (certificationsTableData.length > 1) {
            for (var i = 0; i <= certificationsTableData.length - 1; i++) {
                certificationsJson['certificationName'] = certificationsTableData[i][0]
                certificationsJson['receivedDate'] = certificationsTableData[i][1]
                certificationsJson['certificateReceivedFrom'] = certificationsTableData[i][2]
                // In future we are going to get the certifications from candidates for that we are sending the filename as '' here
                certificationsJson['certificationFileName'] = ""
                certificationsDetailsArray.push(certificationsJson);
                certificationsJson = {};
            }
        }
        else if (certificationsTableData.length == 1) {
            certificationsTableData = certificationsTableData[0];
            // Check whether the certification table data is exist or not
            if (certificationsTableData[0] && certificationsTableData[2] && certificationsTableData[3]) {
                certificationsJson['certificationName'] = certificationsTableData[0];
                certificationsJson['receivedDate'] = certificationsTableData[1]
                certificationsJson['certificateReceivedFrom'] = certificationsTableData[2];
                // In future we are going to get the certifications from candidates for that we are sending the filename as '' here
                certificationsJson['certificationFileName'] = ""
                certificationsDetailsArray.push(certificationsJson);
                certificationsJson = {};
            }
        }
    }
    // function to update jobcandidates
    function updateJobCandidates() {
        // Get and assign values from data table
        getValuesFromDataTable();
        var mutation = `
            mutation CommentQuery(
                $candidateId: Int!
                $salutation: String!
                $firstName: String!
                $lastName: String!
                $gender: String!
                $dob: String!
                $bloodGroup: String
                $maritalStatus: String
                $nationality: String
                $emailId: String!
                $languagesKnown: [String]!
                $mobileNo: String!
                $apartmentName: String
                $street: String
                $city: String
                $state: String
                $country: String
                $pincode: String
                $preferredLocation: [Int]
                $candidateEducation: [educationDetails]
                $candidateExperience: [experienceDetails]
                $candidateSkills: [skillDetails]
                $passportNo: String
                $candidateDependent: [String]
                $candidateCertification: [certificationDetails]
                $candidateWorkPermit: [Int]
                $candidateOtherWorkPermit: String
                $verifierName: String
                $verifierPhoneNo: String
                $verifierEmailId: String
                $jobPost: Int
                $currentEmployer: String
                $noticePeriod: Int
                $currentCTC: Float
                $expectedCTC: Float
                $currency: Int
                $resume: String!
                $nationalIdentificationNumber: String
                $candidateProfilePicture: String
                $resumeFileSize: String!
                $status: Int!
                $totalExperienceInYears: Int
                $totalExperienceInMonths: Int
                $source: String
            ) {
                updateJobCandidates(
                candidateId: $candidateId
                salutation: $salutation
                firstName: $firstName
                lastName: $lastName
                gender: $gender
                dob: $dob
                bloodGroup: $bloodGroup
                maritalStatus: $maritalStatus
                nationality: $nationality
                emailId: $emailId
                languagesKnown: $languagesKnown
                mobileNo: $mobileNo
                apartmentName: $apartmentName
                street: $street
                city: $city
                state: $state
                country: $country
                pincode: $pincode
                preferredLocation: $preferredLocation
                candidateEducation: $candidateEducation
                candidateExperience: $candidateExperience
                candidateSkills: $candidateSkills
                passportNo: $passportNo
                candidateDependent: $candidateDependent
                candidateCertification: $candidateCertification
                candidateWorkPermit: $candidateWorkPermit
                candidateOtherWorkPermit: $candidateOtherWorkPermit
                verifierName: $verifierName
                verifierPhoneNo: $verifierPhoneNo
                verifierEmailId: $verifierEmailId
                jobPost: $jobPost
                currentEmployer: $currentEmployer
                noticePeriod: $noticePeriod
                currentCTC: $currentCTC
                expectedCTC: $expectedCTC
                currency: $currency
                resume: $resume
                nationalIdentificationNumber: $nationalIdentificationNumber
                candidateProfilePicture: $candidateProfilePicture
                resumeFileSize: $resumeFileSize
                status: $status
                totalExperienceInYears: $totalExperienceInYears
                totalExperienceInMonths: $totalExperienceInMonths
                source: $source
                ) {
                errorCode
                message
                validationError
                }
            }
        `;
        var preferredLocation =[];
        var workPermit = [];
        
        preferredLocation = $('#PreferredLocation').select2('val').map(function(v) {
            return parseInt(v, 10);
        });
        
        workPermit = $('#WorkPermit').select2('val').map(function(v) {
            return parseInt(v, 10);
        });
        //  Form dependent details
        var dependentDetails = [];
        dependentDetails.push($('#FatherName').val(), $('#MotherName').val())
        var variables = {
            "candidateId": parseInt(candidateId),
            "salutation": $('#Salutation').val(),
            "firstName": $('#firstName').val(),
            "lastName": $('#lastName').val(),
            "gender": $('#Gender').val(),
            "dob": fnServerDateFormatter($('#CandidateDOB').datepicker('getDate')),
            "bloodGroup": $('#BloodGroup').val(),
            "maritalStatus": $('#MaritalStatus').val(),
            "nationality": $('#Nationality').val(),
            "emailId": $('#email').val(),
            "languagesKnown": jQuery.uniqueSort($('#LanguagesKnown').val()),
            "mobileNo": $('#contactNumber').val(),
            "apartmentName": $('#ApartmentName').val(),
            "street": $('#Street').val(),
            "city": $('#City').val(),
            "state": $('#State').val(),
            "country": $('#Country').val(),
            "pincode": $('#PinCode').val(),
            "preferredLocation": preferredLocation,
            "candidateEducation": educationDetailsArray,
            "candidateExperience": experienceDetailsArray,
            "passportNo": $('#PassportNumber').val() ? $('#PassportNumber').val() : "",
            "candidateDependent": dependentDetails,
            "candidateCertification": certificationsDetailsArray,
            "candidateSkills": skillsDetailsArray,
            "candidateWorkPermit" : workPermit, 
            "candidateOtherWorkPermit" : "",
            "verifierName": $('#VerifierName').val() ? $('#VerifierName').val() : "",
            "verifierPhoneNo": $('#VerifierPhoneNumber').val() ? $('#VerifierPhoneNumber').val() : "",
            "verifierEmailId": $('#VerifierEmailId').val() ? $('#VerifierEmailId').val() : "",
            "jobPost" : parseInt($('#Jobpost').val()),
            "currentEmployer": $('#currentemployer').val(),
            "noticePeriod": parseInt($('#NoticePeriod').val()) ? parseInt($('#NoticePeriod').val()) : null,
            "currentCTC": parseInt($('#CurrentCTC').val()) ? parseInt($('#CurrentCTC').val()) : null,
            "expectedCTC": parseInt($('#ExpectedCTC').val()) ? parseInt($('#ExpectedCTC').val()) : null,
            "resume": "wedewdew",
            "nationalIdentificationNumber": $('#NationalIdentificationNumber').val() ? $('#NationalIdentificationNumber').val() : "",
            "candidateProfilePicture": profileFileName,
            "resumeFileSize": resumeFileSize,
            "currency"                    : parseInt($('#s2id_currency').select2('val')),
            "otherWorkAuthorization"      : $('#otherworkpermits').val(),
            "totalExperienceInYears": 0,
            "totalExperienceInMonths": 0,
            "source": ""
        };

        /**add/update client data in backend */
        $.ajax({
            method: 'POST',
            url: atsBaseURL,
            contentType: 'application/json',
            headers: getGraphqlAPIHeaders(ipAddress),
            data: JSON.stringify({
                query: mutation,
                variables: variables
            }),
            success: function (result) {
                if (!result.data) {
                    // empty the array details if any error occurs
                    educationDetailsArray = [];
                    experienceDetailsArray = [];
                    skillsDetailsArray = [];
                    certificationsDetailsArray = [];
                    /**failure response */
                    var error = JSON.parse(result.errors[0].message);
                    var errorCode = error.errorCode;
                    $(window).scrollTop(0);
                    switch (errorCode) {
                        case 900 /**input validation */:
                            displayValidationErrors(error.validationError);
                            break;
                        case 705:
                        case 706:
                            commonAlertMessage($('#addJobCandidatesPanel'));
                            break;
                        case 719:
                            commonAccessDeniedAlert('#addJobCandidatesPanel');
                        case 721:
                        default:
                            errorAlertMessage($('#addJobCandidatesPanel'));
                            break;
                    }
                    clearLockAPI(orgCode, 'JobCandidates', parseInt(candidateId), employeeId, '#listJobCandidates', atsBaseURL, function (error, data) { });
                    removeMask();
                }
                else {
                    $(window).scrollTop(0);
                    $('#AddEditJobCandidates').hide();
                    $('#listJobCandidates').show();
                    isDirtyFormCandidates = false;
                    // Referesh the list of cendidates details 
                    listJobCandidates(0);
                }
            },
            error: function (result) {
                clearLockAPI(orgCode, 'JobCandidates', parseInt(candidateId), employeeId, '#listJobCandidates', atsBaseURL, function (error, data) { });
                removeMask();
                errorAlertMessage($('#addJobCandidatesPanel'));
            }
        });
    }
    // function to reset data table
    function resetDataTable() {
        // Reseting the education data table
        if (oTable1) {
            var table = $('#table-editable1').DataTable();
            table.clear().draw();
        }
        $("#table-editable1 tbody").empty();

        // Reseting the experience data table
        if (oTable2) {
            var table = $('#table-editable2').DataTable();
            table.clear().draw();
        }
        $("#table-editable2 tbody").empty();

        // Reseting the skills data table
        if (oTable3) {
            var table = $('#table-editable3').DataTable();
            table.clear().draw();
        }
        $("#table-editable3 tbody").empty();

        // Reseting the certifications data table
        if (oTable4) {
            var table = $('#table-editable4').DataTable();
            table.clear().draw();
        }
        $("#table-editable4 tbody").empty();
        // Reset table data array
        educationDetailsArray = [];
        experienceDetailsArray = [];
        skillsDetailsArray = [];
        certificationsDetailsArray = [];
    }
    // Jobcandidates view page cancel
    $('#viewJobCandidateCancel1,#viewJobCandidateCancel2').on('click', function () {
        resetDataTable();
        $('#listJobCandidates').show();
        $('#AddEditJobCandidates').hide();
        $('#ViewJobCandidates').hide();
    });

    /**cancel close confirmation */
    $('#cancelCloseForms,#cancelFormClose').on('click',function() {
        $('#modalCloseJobCandidatesForm').modal('hide');
    });


    // Add and edit for cancel
    $('#jobCandidateCancel1,#jobCandidateCancel2').on('click', function () {
        
        if(isDirtyFormCandidates)
        {
            $('#modalCloseJobCandidatesForm').modal({
                backdrop: 'static',
                keyboard: false
            });
        }
        else 
        {
            closeForms();
        }
        
    });

    /**confirm close of the create job post modal */
    $('#confirmFormClose').on('click',function() {
        closeForms();
        
        $('#modalCloseJobCandidatesForm').modal('hide');



    });

    /**close the add/edit forms */
    function closeForms() {
        if (editFlag) {
            setMask('#wholepage');
            clearLockAPI(orgCode, 'JobCandidates', parseInt(candidateId), employeeId, '#listJobCandidates', atsBaseURL, function (error, data) {
                removeMask();
                addFlag = 0;
                editFlag = 0;
                addByTab = 0;
                $('#tab2').removeClass('active');
                $('#tab5_2').removeClass('active');
                $('#tab1').addClass('active');
                $('#tab5_1').addClass('active');

                $('#tab3').removeClass('active');
                $('#tab5_3').removeClass('active');
        
                isDirtyFormCandidates = false;
                resetDataTable();
                $('#AddEditJobCandidates').hide();
                $('#ViewJobCandidates').hide();
                $('#listJobCandidates').show();
                listJobCandidates(0);
            });
        }
        else 
        {
            addFlag = 0;
            editFlag
            addByTab = 0;
            $('#tab2').removeClass('active');
            $('#tab5_2').removeClass('active');
            $('#tab1').addClass('active');
            $('#tab5_1').addClass('active');

            $('#tab3').removeClass('active');
            $('#tab5_3').removeClass('active');
    
            isDirtyFormCandidates = false;
            resetDataTable();
            $('#AddEditJobCandidates').hide();
            $('#ViewJobCandidates').hide();
            $('#listJobCandidates').show();
            listJobCandidates(0);
        }
    }

    /**clicking edit button in view form */
    $('#viewJobCandidateEdit1,#viewJobCandidateEdit2').on('click', function () {
        $('#editCandidates').trigger('click', [{ EditFlag: 1 }]);
    });

    /**action to download the MOU attachment file */
    $(document).on('click', '.downloadresumefile ', function (e) {
        var path = $(this).attr('resumedownloadfilepath');
        e.preventDefault(); //stop the browser from following
        window.open(path);
    });

    // Onclick edit functions
    $(document).on('click', '#editCandidates', function (e, EditFlag) {
        $('#tab2').on('show.bs.tab', 'a', function(event) {
            event.preventDefault();
        });
        $('#tab2').on('hide.bs.tab', 'a', function(event) {
            event.preventDefault();
        });

        $('#tab3').show();
        $('#tab3').on('show.bs.tab', 'a', function(event) {
            event.preventDefault();
        });
        $('#tab3').on('hide.bs.tab', 'a', function(event) {
            event.preventDefault();
        });


        setMask('#wholepage');
        // Check whether the employee have an edit rights
        if (accessRightsParams['updateAccessRights']) {


            $('#formheading').html('')
            .append('EDIT JOB CANDIDATES');


            var errorPage = "";
            // edit from view page 
            if (EditFlag) {
                candidateId = chosenCandidatesIndexValues;
                errorPage = "#ViewJobCandidates"

                // ahalya ---> have to check
                var setProfileUrl = $(this).attr('profileurl');
                var setResumeUrl = $(this).attr('resumeurl');
                // ahalya ---> have to check

    
            }
            // edit form
            else {
                candidateId = $(this).attr('candidateid');
                errorPage = "#listJobCandidates";

                // ahalya ---> have to check
                var setProfileUrl = $(this).attr('profileurl');
                var setResumeUrl = $(this).attr('resumeurl');
                // ahalya ---> have to check
    
            }


            // Set default tab
            $('#tab5_2').removeClass('active');
            $('#mainTab a:first').tab('show');

            // Set default tab for sub tab
            $('#tab2_2,#tab2_3,#tab2_4').removeClass('active');
            $('#subTab a:first').tab('show');

            setLockAPI(orgCode, 'JobCandidates', parseInt(candidateId), employeeId, errorPage, atsBaseURL, function (error, data) {
                if (data) {
                    // Function to retrieve the candidate details
                    retrieveCandidateDetails(candidateId, 'update', function (error, result) {
                        if (result) {
                            resetCandidatesAddEditForm();
                            editFlag = 1;

                            listCourses();
                            getLanguageCoursecountry();

                            prefillCandidateInfo = result.data.retrieveJobCandidates.jobCandidateDetails;
                            resumeFileName = result.data.retrieveJobCandidates.jobCandidateDetails.Resume;
                            resumeFileSize = result.data.retrieveJobCandidates.jobCandidateDetails.Resume_File_Size
                            // can be used in future 
                            // var splitResumeFileName = resumeFileName.split(/\.(?=[^\.]+$)/);
                            // splitResumeFileName = splitResumeFileName[0].split('??');
                            // resumeFileName = splitResumeFileName[1];
                            
                            /** hide the candidates list page show add candidates page **/
                            $('#AddEditJobCandidates').show();
                            $('#listJobCandidates').hide();
                            $('#ViewJobCandidates').hide();

                            $(window).scrollTop(0);
                            editFlag = 1;
                            $('#candidateResumePreview').html('');
                            $('#candidateResumePreview').width('');
                            $('#candidateResumePreview').height('');
                            $('#candidateProfilePreview').html('');
                            $('#candidateProfilePreview').width('');
                            $('#candidateProfilePreview').height('');

                            setTimeout(function () {
                                $('#candidateResumePreview').append("<img src='" + setResumeUrl + "' alt='" + resumeFileName + "'>");
                                $('#candidateResumePreview img').css('max-width', '100%');
                                $('#candidateResumePreview img').css('max-height', '230px');
                                $('#changeResumeFile,#removeResumeFile').show();
                                $('#showContentDiv1,#cloudIcon1').hide();
                                $('#innerRectangle').css('background-color', 'white');
                            }, 150);


                        // Call function setFileTypes to set appropirated image for the document
                        // setFileTypes(resumeFileName[1], 'addEdit', resumeFileName); //ahalya

                        // Set if profile is updated already
                            if (setProfileUrl != '') {
                                profileFileName = result.data.retrieveJobCandidates.jobCandidateDetails.Photo_Path;
                                var splitProfileFileName = profileFileName.split(/\.(?=[^\.]+$)/);
                                splitProfileFileName = splitProfileFileName[0].split('??');
                                profileFileName = splitProfileFileName[1];
                                setTimeout(function () {
                                    $('#candidateProfilePreview').append("<img src='" + setProfileUrl + "' alt='" + profileFileName + "'>");
                                    $('#candidateProfilePreview img').css('max-width', '100%');
                                    $('#candidateProfilePreview img').css('max-height', '230px');
                                    $('#changeProfileFile,#removeProfileFile').show();
                                    $('#showProfileContentDiv1,#cloudIcon2').hide();
                                    $('#profileInnerRectangle').css('background-color', 'white');
                                }, 150);
                            }
                            else {
                                $('#showContentDiv1,#cloudIcon1').show();
                            }
                            
                            // set the values for the feilds
                            $('#firstName').val(prefillCandidateInfo.Candidate_First_Name).trigger('change', [{ initialCheck: true }]);
                            $('#lastName').val(prefillCandidateInfo.Candidate_Last_Name).trigger('change', [{ initialCheck: true }]);
                            $('#s2id_Salutation').select2('val', prefillCandidateInfo.Salutation);
                            $('#Salutation').trigger('change', [{ initialCheck: true }]);

                            if(prefillCandidateInfo.DOB != "NaN/NaN/NaN"){
                                $('#CandidateDOB').datepicker('setDate', new Date(prefillCandidateInfo.DOB))
                                                    .trigger('change',true);
                            }else{
                                $('#CandidateDOB').datepicker('setDate', '');
                                $('#CandidateDOB').trigger('change', [{ initialCheck: true }]);
                            }
                            $('#email').val(prefillCandidateInfo.Personal_Email).trigger('change', [{ initialCheck: true }])
                            $('#contactNumber').val(prefillCandidateInfo.Mobile_No).trigger('change', [{ initialCheck: true }]);
                            $('#s2id_Gender').select2('val', prefillCandidateInfo.Gender);
                            $('#Gender').trigger('change', [{ initialCheck: true }])



                            $('#Nationality').val(prefillCandidateInfo.Nationality).trigger('change', [{ initialCheck: true }])
                            $('#ApartmentName').val(prefillCandidateInfo.pApartment_Name).trigger('change', [{ initialCheck: true }])
                            $('#Street').val(prefillCandidateInfo.pStreet_Name).trigger('change', [{ initialCheck: true }])
                            $('#City').val(prefillCandidateInfo.pCity).trigger('change', [{ initialCheck: true }])
                            $('#State').val(prefillCandidateInfo.pState).trigger('change', [{ initialCheck: true }])
                            $('#PinCode').val(prefillCandidateInfo.pPincode).trigger('change', [{ initialCheck: true }])
                            $('#NoticePeriod').val(prefillCandidateInfo.Notice_Period).trigger('change', [{ initialCheck: true }])
                            $('#CurrentCTC').val(prefillCandidateInfo.Current_CTC).trigger('change', [{ initialCheck: true }])
                            $('#ExpectedCTC').val(prefillCandidateInfo.Expected_CTC).trigger('change', [{ initialCheck: true }])
                            $('#NationalIdentificationNumber').val(prefillCandidateInfo.National_Identification_Number).trigger('change', [{ initialCheck: true }])
                            $('#VerifierName').val(prefillCandidateInfo.Verifier_Name).trigger('change', [{ initialCheck: true }])
                            $('#VerifierPhoneNumber').val(prefillCandidateInfo.Verifier_Phone_Number).trigger('change', [{ initialCheck: true }])
                            $('#VerifierEmailId').val(prefillCandidateInfo.Verifier_Email_Id).trigger('change', [{ initialCheck: true }])
                            $('#currentemployer').val(prefillCandidateInfo.Current_Employer).trigger('change', [{ initialCheck: true }]);
                            $('#candidateStatus').val(prefillCandidateInfo.Status).trigger('change');


                            candidateCurrentStatus = prefillCandidateInfo.Status_Id;

                            var candidateDependents = prefillCandidateInfo.Candidate_Dependent;


                            if(candidateDependents.length > 0)
                            {

                                for(var i in candidateDependents)
                                {
                                    if(candidateDependents[i].Relationship == "Father")
                                    {
                                        $('#FatherName').val(candidateDependents[i].Dependent_First_Name ? candidateDependents[i].Dependent_First_Name : '').trigger('change', [{ initialCheck: true }])

                                    }
                                    else 
                                    {
                                        $('#MotherName').val(candidateDependents[i].Dependent_First_Name ? candidateDependents[i].Dependent_First_Name : '').trigger('change', [{ initialCheck: true }])

                                    }
                                }


                            }
                            $('#PassportNumber').val(prefillCandidateInfo.Passport_No).trigger('change', [{ initialCheck: true }])
                            $('#s2id_BloodGroup').select2('val', prefillCandidateInfo.Blood_Group);
                            $('#BloodGroup').trigger('change', [{ initialCheck: true }])
                            $('#s2id_MaritalStatus').select2('val', prefillCandidateInfo.Marital_Status);
                            $('#MaritalStatus').trigger('change', [{ initialCheck: true }])
                            $('#s2id_currency').select2('val',prefillCandidateInfo.Currency);
                            $('#currency').trigger('change',[{initialCheck : true}]);
                            $('#otherworkpermits').val(prefillCandidateInfo.Other_Work_Permit)
                                                .trigger('change',[{initialCheck : true}]);


                            // Prefilling the inline editor
                            oTable1 = $('#table-editable1').dataTable({
                                "bDestroy": true,
                                "aLengthMenu": [
                                ],
                                "sDom": "",
                                "oTableTools": {
                                    "sSwfPath": "",
                                    "aButtons": [

                                    ]
                                }
                            });
                            for (var i = 0; i <= prefillCandidateInfo.Candidate_Education.length - 1; i++) {
                                var aiNew = oTable1.fnAddData([
                                    prefillCandidateInfo.Candidate_Education[i].Education_Type,
                                    prefillCandidateInfo.Candidate_Education[i].Institute_Name,
                                    prefillCandidateInfo.Candidate_Education[i].Specialisation,
                                    prefillCandidateInfo.Candidate_Education[i].Year_Of_Passing,
                                    prefillCandidateInfo.Candidate_Education[i].Percentage,
                                    '<div class="text-center"><a class="fa fa-trash-o delete" href=""></a></div>'
                                ]);
                                var nRow = oTable1.fnGetNodes(aiNew[0]);
                                var jqTds = $('>td', nRow);
                                setIdsForInlineEditor(jqTds, 'edu');
                            }
                            oTable2 = $('#table-editable2').dataTable({
                                "bDestroy": true,
                                "aLengthMenu": [
                                ],
                                "sDom": "",
                                "oTableTools": {
                                    "sSwfPath": "",
                                    "aButtons": [

                                    ]
                                }
                            });
                            for (var i = 0; i <= prefillCandidateInfo.Candidate_Experience.length - 1; i++) {
                                var aiNew = oTable2.fnAddData([
                                    prefillCandidateInfo.Candidate_Experience[i].Designation,
                                    prefillCandidateInfo.Candidate_Experience[i].Prev_Company_Name,
                                    setInlineEditorDateFormat(prefillCandidateInfo.Candidate_Experience[i].Start_Date),
                                    setInlineEditorDateFormat(prefillCandidateInfo.Candidate_Experience[i].End_Date),
                                    '<div class="text-center"><a class="fa fa-trash-o delete" href=""></a></div>'
                                ]);
                                var nRow = oTable2.fnGetNodes(aiNew[0]);
                                var jqTds = $('>td', nRow);
                                setIdsForInlineEditor(jqTds, 'exp');
                            }
                            oTable3 = $('#table-editable3').dataTable({
                                "bDestroy": true,
                                "aLengthMenu": [
                                ],
                                "sDom": "",
                                "oTableTools": {
                                    "sSwfPath": "",
                                    "aButtons": [

                                    ]
                                }
                            });
                            for (var i = 0; i <= prefillCandidateInfo.Candidate_Skills.length - 1; i++) {
                                var aiNew = oTable3.fnAddData([
                                    prefillCandidateInfo.Candidate_Skills[i].Skills,
                                    prefillCandidateInfo.Candidate_Skills[i].Description,
                                    prefillCandidateInfo.Candidate_Skills[i].Proficiency,
                                    prefillCandidateInfo.Candidate_Skills[i].Primary,
                                    '<div class="text-center"><a class="fa fa-trash-o delete" href=""></a></div>'
                                ]);
                                var nRow = oTable3.fnGetNodes(aiNew[0]);
                                var jqTds = $('>td', nRow);
                                setIdsForInlineEditor(jqTds, 'ski');
                            }
                            oTable4 = $('#table-editable4').dataTable({
                                "bDestroy": true,
                                "aLengthMenu": [
                                ],
                                "sDom": "",
                                "oTableTools": {
                                    "sSwfPath": "",
                                    "aButtons": [

                                    ]
                                }
                            });
                            for (var i = 0; i <= prefillCandidateInfo.Candidate_Certifications.length - 1; i++) {
                                var aiNew = oTable4.fnAddData([
                                    prefillCandidateInfo.Candidate_Certifications[i].Certification_Name,
                                    setInlineEditorDateFormat(prefillCandidateInfo.Candidate_Certifications[i].Received_Date),
                                    prefillCandidateInfo.Candidate_Certifications[i].Certificate_Received_From,
                                    '<div class="text-center"><a class="fa fa-trash-o delete" href=""></a></div>'
                                ]);
                                var nRow = oTable4.fnGetNodes(aiNew[0]);
                                var jqTds = $('>td', nRow);
                                setIdsForInlineEditor(jqTds, 'cer');
                            }
                            // set candidate Id in the tab3
                            $('#tab3').attr('candidateId',candidateId);
                            removeMask();
                        }
                        else {
                            $(window).scrollTop(0);
                            removeMask();
                            commonAlertMessage($('#listJobCandidates'));
                        }
                    });
                }
                else {
                    clearLockAPI(orgCode, 'JobCandidates', parseInt(candidateId), employeeId, errorPage, atsBaseURL, function (error, data) { });
                    removeMask();
                }
            });
        }
        else {
            removeMask();
            commonAccessDeniedAlert($('#listJobCandidates'));
        }
        e.stopPropagation();
    })

    // function to set ids for the inline editor
    function setIdsForInlineEditor(jqTds, tabName) {
        if (tabName == 'edu') {
            for (i = 0; i <= 4; i++) {
                jqTds[i].id = 'edu' + editeducationrowcount + i;
                jqTds[i].className = 'editable';
            }
            educationrowcount += 1;
            educationRowSaved = 1;
        }
        else if (tabName == 'exp') {
            for (i = 0; i <= 3; i++) {
                jqTds[i].id = 'exp' + editexperiencerowcount + i;
                jqTds[i].className = 'editable';
            }
            experiencerowcount += 1;
            workExpRowSaved = 1;
        }
        else if (tabName == 'ski') {
            for (i = 0; i <= 3; i++) {
                jqTds[i].id = 'ski' + editskillsrowcount + i;
                jqTds[i].className = 'editable';
            }
            skillsrowcount += 1;
            skillRowSaved = 1;
        }
        else {
            for (i = 0; i <= 2; i++) {
                jqTds[i].id = 'cer' + editcertificationrowcount + i;
                jqTds[i].className = 'editable';
            }
            certificationrowcount += 1;
            certRowSaved =1;
        }
    }
    // Onclick delte functions
    $(document).on('click', '#deleteCandidates', function (e) {
        if (accessRightsParams['deleteAccessRights']) {
            chosenCandidatesIndexValues = $(this).attr('candidateid');
            $('#modalDeleteCandidates').modal('toggle');
        } else {
            commonAccessDeniedAlert($('#listJobCandidates'));
        }
        e.stopPropagation();
    });


    function setInlineEditorDateFormat(dateValue)
    {
        var dateValue = dateValue.split('/');
        return (dateValue[2] + '/' + dateValue[1] + '/' + dateValue[0])
       
    }

    // Set Candidate view details
    $(document).on('click', '.candidatesdetails ', function () {
        setMask('#wholepage');
        // Get candidateId, candidate profile url and resume url
        chosenCandidatesIndexValues = $(this).attr('candidateid');
        var candidateProfile = $(this).attr('profileurl');
                
        // Function to retrieve the candidate details
        retrieveCandidateDetails(chosenCandidatesIndexValues, 'view', function (error1, result1) {
            if (result1 && result1.data) {
                // Hide and show the panel
                $('#AddEditJobCandidates').hide();
                $('#listJobCandidates').hide();
                $('#ViewJobCandidates').show();

                // Set default tab for main tab
                $('#tab3_2,#tab3_3').removeClass('active');
                $('#viewMainTab a:first').tab('show');

                // Set default tab for sub tab
                $('#tab4_2,#tab4_3,#tab4_4').removeClass('active');
                $('#viewSubTab a:first').tab('show');

                //  Get the candidate details
                var candidateDetails = result1.data.retrieveJobCandidates.jobCandidateDetails;
                resumeFileName = result1.data.retrieveJobCandidates.jobCandidateDetails.Resume;
                var candidateResume = fngetSignedUrl(domainName+"/"+orgCode+"/"+"resume/"+resumeFileName,'bucketName');
                if(resumeFileName){
                    var splitResumeFileName =resumeFileName.split(/\.(?=[^\.]+$)/);
                    splitResumeFileName = splitResumeFileName[0].split('??');
                    resumeFileName = splitResumeFileName[1];
                }

                // Empty the view form before setting the values
                resetCandidatesViewForm();

                /**set the resume in view page */
                if (candidateDetails.Resume != '') {
                    $('#resumeUrl').append('<img src="' + candidateResume + '" alt="' + s3ResumeFileName + '" style="max-width:100%;max-height:210px;margin-left: 10%;">');
                }
                /**set the profile in view page */
                if (candidateDetails.Photo_Path) {
                    $('#profileUrl').html('');
                    $('#profileUrl').append('<img src="' + candidateProfile + '" alt="' + s3ProfileFileName + '" style="max-width:100%;max-height:210px;margin-left: 10%;">');
                } else {
                    var img = pageUrl() + 'images/defaultPhot.jpg';
                    var altname = 'avatar';
                    $('#profileUrl').append('<img src="' + img + '" alt="' + altname + '" style="width:200px;height:200px;border-radius:100px">');
                }

                // Set the resume type images
                // setFileTypes(resumeFileName[1], 'view', resumeFileName);

                /**file to download */
                $('#downLoadResume').append(
                    '<button class="btn btn-secondary btn-embossed downloadresumefile" data-style="expand-left" style="border-radius:10px;" id="" style="bottom: 5px;" resumedownloadfilepath = "' +
                    candidateResume +
                    '"> Download</button>'
                );

                // Append languages known
                if(candidateDetails.Lang_Known)
                {
                    for (var i = 0; i <= candidateDetails.Lang_Known.length - 1; i++) {
                        if (i == candidateDetails.Lang_Known.length - 1) {
                            $('#viewLanguagesKnown').append(candidateDetails.Lang_Known[i].Language_Name);
                        }
                        else {
                            $('#viewLanguagesKnown').append(candidateDetails.Lang_Known[i].Language_Name, ',');
                        }
                    }
                }
                else 
                {
                    $('#viewLanguagesKnown').append('-');

                }

                // Append preferred location
                if(candidateDetails.Preferred_Location) {
                    for (var i = 0; i <= candidateDetails.Preferred_Location.length - 1; i++) {
                        if (i == candidateDetails.Preferred_Location.length - 1) {
                            $('#viewPreferredLocation').append(candidateDetails.Preferred_Location[i].Location_Name);
                        }
                        else {
                            $('#viewPreferredLocation').append(candidateDetails.Preferred_Location[i].Location_Name, ',');
                        }
                    }
                }
                else 
                {
                    $('#viewPreferredLocation').append('-');
  
                }
                

                // Uncomment once the workpermit API is integrated
                // Append work permit
                if(candidateDetails.Work_Permit.length>0)
                {
                    for (var i = 0; i <= candidateDetails.Work_Permit.length - 1; i++) {
                        if (i == candidateDetails.Work_Permit.length - 1) {
                            $('#viewWorkPermit').append(candidateDetails.Work_Permit[i].Work_Permit);
                        }
                        else {
                            $('#viewWorkPermit').append(candidateDetails.Work_Permit[i].Work_Permit, ',');
                        }
                    }
                }
                else 
                {
                    $('#viewWorkPermit').append('-');

                }
                

                $('#viewFirstName').append(candidateDetails.Candidate_First_Name);
                $('#viewSalutation').append(candidateDetails.Salutation)
                $('#viewContactNumber').append(candidateDetails.Mobile_No);
                $('#viewGender').append(candidateDetails.Gender);
                $('#viewLastName').append(candidateDetails.Candidate_Last_Name);
                if(candidateDetails.DOB != "NaN/NaN/NaN"){
                    $('#viewDOB').append(fnFormatDate(candidateDetails.DOB));
                }else{
                    $('#viewDOB').append('-');
                }
                $('#viewEmail').append(candidateDetails.Personal_Email);
                candidateDetails.Job_Post_Name ?  $('#viewJobPost').append(candidateDetails.Job_Post_Name) : $('#viewJobPost').append('-');
                candidateDetails.Current_Employer ?  $('#viewCurrentEmployer').append(candidateDetails.Current_Employer) : $('#viewCurrentEmployer').append('-');
                var candidateDependents = candidateDetails.Candidate_Dependent

                if(candidateDependents.length > 0)
                {
                    if(candidateDependents.length === 1) {
                        if(candidateDependents[0].Relationship == "Father")
                        {
                            $('#viewFathersName').append(candidateDependents[0].Dependent_First_Name);
                            $('#viewMothersName').append('-')
                        }
                        else 
                        {
                            $('#viewFathersName').append('-');
                            $('#viewMothersName').append(candidateDependents[0].Dependent_First_Name)
                        }
                    }
                    else 
                    {
                        for(var i in candidateDependents)
                        {
                            if(candidateDependents[i].Relationship == "Father")
                            {
                                $('#viewFathersName').append(candidateDependents[0].Dependent_First_Name ? candidateDependents[0].Dependent_First_Name : '-');
                            }
                            else 
                            {
                                $('#viewMothersName').append(candidateDependents[1].Dependent_First_Name ? candidateDependents[1].Dependent_First_Name : '-')
    
                            }
                        }
    
                    }
                }
                else 
                {
                    $('#viewFathersName').append('-');
                    $('#viewMothersName').append('-')

                }


                $('#viewBloodGroup').append(candidateDetails.Blood_Group ? candidateDetails.Blood_Group : '-');
                $('#viewMaritalStatus').append(candidateDetails.Marital_Status ? candidateDetails.Marital_Status : '-');
                $('#viewNationality').append(candidateDetails.Nationality ? candidateDetails.Nationality : '-');

                candidateDetails.Notice_Period ?  $('#viewNoticePeriod').append(candidateDetails.Notice_Period+' days') : $('#viewNoticePeriod').append('-'); 
                
                $('#viewApartmentName').append(candidateDetails.pApartment_Name ? candidateDetails.pApartment_Name : '-');
                $('#viewStreet').append(candidateDetails.pStreet_Name ? candidateDetails.pStreet_Name : '-');
                $('#viewCity').append(candidateDetails.pCity ? candidateDetails.pCity : '-');
                $('#viewState').append(candidateDetails.pState ? candidateDetails.pState : '-');
                $('#viewPincode').append(candidateDetails.pPincode ? candidateDetails.pPincode : '-');
                $('#viewCountry').append(candidateDetails.Country_Name ? candidateDetails.Country_Name : '-');
                candidateDetails.Passport_No ? $('#viewPassportNumber').append(candidateDetails.Passport_No) : $('#viewPassportNumber').append('-');
                candidateDetails.National_Identification_Number ? $('#viewNationalIdentificationNumber').append(candidateDetails.National_Identification_Number) : $('#viewNationalIdentificationNumber').append('-');
                candidateDetails.Verifier_Name ?  $('#viewVerifierName').append(candidateDetails.Verifier_Name) : $('#viewVerifierName').append('-');
                candidateDetails.Verifier_Email_Id ?  $('#viewVerifierEmail').append(candidateDetails.Verifier_Email_Id) : $('#viewVerifierEmail').append('-');;
                candidateDetails.Verifier_Phone_Number ?  $('#viewVerifierPhoneNumber').append(candidateDetails.Verifier_Phone_Number) : $('#viewVerifierPhoneNumber').append('-');
                $('#viewResumeFileName').append(resumeFileName);
                candidateDetails.Currency_Name ?  $('#viewCurrency').append(candidateDetails.Currency_Name) : $('#viewCurrency').append('-');
                candidateDetails.Current_CTC ?  $('#viewCurrentCTC').append(candidateDetails.Current_CTC) : $('#viewCurrentCTC').append('-');
                candidateDetails.Expected_CTC ?  $('#viewExpectedCTC').append(candidateDetails.Expected_CTC) : $('#viewExpectedCTC').append('-');
                candidateDetails.Other_Work_Permit ?  $('#viewOtherWorkPermits').append(candidateDetails.Other_Work_Permit) : $('#viewOtherWorkPermits').append('-');


                // Prefilling the inline editor                                                                    
                var educationViewTable = $('#view_table_editable1').dataTable({
                    "bDestroy": true,
                    "aLengthMenu": [
                    ],
                    "sDom": "",
                    "oTableTools": {
                        "sSwfPath": "",
                        "aButtons": [

                        ]
                    }
                });
                var experienceViewTable = $('#view_table_editable2').dataTable({
                    "bDestroy": true,
                    "aLengthMenu": [
                    ],
                    "sDom": "",
                    "oTableTools": {
                        "sSwfPath": "",
                        "aButtons": [

                        ]
                    }
                });
                var skillsViewTable = $('#view_table_editable3').dataTable({
                    "bDestroy": true,
                    "aLengthMenu": [
                    ],
                    "sDom": "",
                    "oTableTools": {
                        "sSwfPath": "",
                        "aButtons": [

                        ]
                    }
                });
                var certificationsViewTable = $('#view_table_editable4').dataTable({
                    "bDestroy": true,
                    "aLengthMenu": [
                    ],
                    "sDom": "",
                    "oTableTools": {
                        "sSwfPath": "",
                        "aButtons": [

                        ]
                    }
                });
                for (var i = 0; i <= candidateDetails.Candidate_Education.length - 1; i++) {
                    educationViewTable.fnAddData([
                        candidateDetails.Candidate_Education[i].Education_Type,
                        candidateDetails.Candidate_Education[i].Institute_Name,
                        candidateDetails.Candidate_Education[i].Specialisation,
                        candidateDetails.Candidate_Education[i].Year_Of_Passing,
                        candidateDetails.Candidate_Education[i].Percentage
                    ]);
                }

                // Check if the experience details is exist or not. If exist add it in data table
                if (candidateDetails.Candidate_Experience.length > 0) {
                    for (var j = 0; j <= candidateDetails.Candidate_Experience.length - 1; j++) {
                        candidateDetails.Candidate_Experience[j].Start_Date = setInlineEditorDateFormat(candidateDetails.Candidate_Experience[j].Start_Date);
                        candidateDetails.Candidate_Experience[j].End_Date = setInlineEditorDateFormat(candidateDetails.Candidate_Experience[j].End_Date);
                    }
                    for (var j = 0; j <= candidateDetails.Candidate_Experience.length - 1; j++) {
                        experienceViewTable.fnAddData([
                            candidateDetails.Candidate_Experience[j].Prev_Company_Name,
                            candidateDetails.Candidate_Experience[j].Designation,
                            candidateDetails.Candidate_Experience[j].Start_Date,
                            candidateDetails.Candidate_Experience[j].End_Date,
                        ]);
                    }
                }
                // Add the candidates skills details in data table
                for (var k = 0; k <= candidateDetails.Candidate_Skills.length - 1; k++) {
                    skillsViewTable.fnAddData([
                        candidateDetails.Candidate_Skills[k].Skills,
                        candidateDetails.Candidate_Skills[k].Description,
                        candidateDetails.Candidate_Skills[k].Primary,
                        candidateDetails.Candidate_Skills[k].Proficiency,
                    ]);
                }
                // Check if the certification details are exist. If exist add it in data table
                if (candidateDetails.Candidate_Certifications.length > 0) {
                    for (var l = 0; l <= candidateDetails.Candidate_Certifications.length - 1; l++) {
                        candidateDetails.Candidate_Certifications[l].Received_Date = setInlineEditorDateFormat(candidateDetails.Candidate_Certifications[l].Received_Date);
                    }
                    for (var l = 0; l <= candidateDetails.Candidate_Certifications.length - 1; l++) {
                        certificationsViewTable.fnAddData([
                            candidateDetails.Candidate_Certifications[l].Certification_Name,
                            candidateDetails.Candidate_Certifications[l].Received_Date,
                            candidateDetails.Candidate_Certifications[l].Certificate_Received_From,
                        ]);
                    }
                }
                removeMask();
            }
            else {
                $(window).scrollTop(0);
                removeMask();
                commonAlertMessage($('#listJobCandidates'));
            }
        })
    });

    // function to set default file types in View, Add, Edit
    function setFileTypes(fileType, action, displayFileName) {
        switch (fileType) {
            case 'pdf':
                var url = pageUrl() + 'images/pdflogo.png';
                var altname = 'pdflogo';
                break;
            case 'doc':
                var url = pageUrl() + 'images/doclogo.jpeg';
                var altname = 'doclogo';
                break;
            case 'docx':
                var url = pageUrl() + 'images/docxlogo.png';
                var altname = 'docxlogo';
                break;
            default:
                var url = pageUrl() + 'images/pdflogo.png';
                var altname = 'pdflogo';
                break;
        }
        if (action == 'view') {
            $('#viewResumeFileType').append('<img class="candidate-file-resume" style="height:50px" src="' + url + '" alt="' + altname + '"/>');
        }
        else {
            setTimeout(function () {
                $('#candidateResumePreview').html('');
                $('#candidateResumePreview').width('');
                $('#candidateResumePreview').height('');
                $("#candidateResumePreview").append(displayFileName);
                $('#candidateResumePreview').append("<img src='" + url + "' alt='" + altname + "'>");
                $('#candidateResumePreview img').css('max-width', '100%');
                $('#candidateResumePreview img').height('155px');
                $('#innerRectangle').css('background-color', '#ffffff');
            }, 150);
        }
    }

    /**delete job candidates */
    $('#confirmDelete').on('click', function () {
        deleteJobCandidates(chosenCandidatesIndexValues);
    });

    /**search job candidates input */
    $('#search,#searchcandidates').on('input', function () {
        $('#search,#searchcandidates').val($(this).val());
        if (timer) {
            clearTimeout(timer);
        }
        timer = setTimeout(function() {
            listJobCandidates(1);

        },1000);


    });

    // retrieve candidate details 
    function retrieveCandidateDetails(chosenCandidatesIndexValues, action, fn) {
      
        var retrieveMutation = `  mutation CommentQuery($candidateId: Int!, $action: String!) {
            retrieveJobCandidates(candidateId: $candidateId, action: $action) {
              errorCode
              message
              validationError
              jobCandidateDetails {
                Candidate_Id
                Candidate_First_Name
                Candidate_Last_Name
                Salutation
                Gender
                Personal_Email
                DOB
                Marital_Status
                Nationality
                Blood_Group
                pApartment_Name
                pStreet_Name
                pCity
                pState
                pCountry
                pPincode
                Country_Name
                Current_Employer
                Notice_Period
                Current_CTC
                Expected_CTC
                Resume
                Cover_Letter
                National_Identification_Number
                Passport_No
                Verifier_Name
                Verifier_Phone_Number
                Verifier_Email_Id
                Source_Type
                Photo_Path
                Resume_File_Size
                Mobile_No
                Job_Post_Id
                Job_Post_Name
                Currency
                Currency_Name
                Status_Id
                Status
                Total_Experience_In_Years
                Total_Experience_In_Months
                Source
                Preferred_Location {
                  Location_Id
                  Location_Name
                }
                Lang_Known {
                  Lang_Id
                  Language_Name
                }
                Candidate_Dependent {
                  Dependent_Id
                  Dependent_First_Name
                  Relationship
                  Gender
                }
                Work_Permit {
                  Work_Permit_Id
                  Work_Permit
                }
                Other_Work_Permit
                Candidate_Education {
                  Education_Id
                  Education_Type_Id
                  Education_Type
                  Specialisation
                  Institute_Name
                  Year_Of_Passing
                  Percentage
                }
                Candidate_Experience {
                  Experience_Id
                  Prev_Company_Name
                  Prev_Company_Location
                  Designation
                  Start_Date
                  End_Date
                  Duration
                  Years
                  Months
                }
                Candidate_Skills {
                  Skill_Id
                  Skills
                  Description
                  Primary
                  Proficiency
                }
                Candidate_Certifications {
                  Certification_Id
                  Certification_Name
                  Received_Date
                  Certificate_Received_From
                  Certificate_File_Name
                }
              }
            }
          }`;
        /** retrieve query variables **/
        var retrieveParams = {
            "candidateId": parseInt(chosenCandidatesIndexValues),
            "action": action
        };

        /**list the countries */
        $.ajax({
            method: 'POST',
            url: atsBaseURL,
            headers: getGraphqlAPIHeaders(ipAddress),
            data: JSON.stringify({
                query: retrieveMutation,
                variables: retrieveParams
            }),
            success: function (result1) {
                if (result1.data != null) {
                    return fn(0, result1)
                }
                else {
                    return fn(1, result1);
                }
            },
            error: function (result2) {
                return (1, error);
            }
        });
    }

    $('#resetCandidatesFilter1,#resetCandidatesFilter2').on('click', function () {
        setMask('#wholepage');
        resetFilter();
    })

    

     // Filter details
     $('#candidatejobpost,#candidatePreferedJobpostLocation,#candidateStatusFilter').on('change', function(e,changeFlag=true) {

        var idValue = e.target.id;

        if(changeFlag)
        {
            //get the value of the current dropdown 
            var value = $('#'+idValue).select2('val');
            if(value[0])
            {
                //change all the elements in an array as integer 
                filterValues[idValue] = $('#'+idValue).select2('val').map(function(v) {
                    return parseInt(v, 10);
                });
                $('#'+idValue+'1').select2('val', filterValues[idValue]);
                $('#'+idValue+'1').trigger('change',false);
                listJobCandidates(1);

    
            }
            else 
            {
                //if no values are available for the dropdown, make the variable as empty for filter 
                filterValues[idValue] = [];

                $('#s2id_'+idValue+'1').select2('val', '');
                $('#'+idValue+'1'+' option:selected').prop('selected', false);
                $('#'+idValue+'1').addClass('empty');



                listJobCandidates(1);

            }
        }
        
    });

    // Filter details
    $('#candidatejobpost1,#candidatePreferedJobpostLocation1,#candidateStatusFilter1').on('change',function(e,changeFlag=true){

        var idValue = e.target.id;
        idValue = idValue.slice(0,-1);

        if(changeFlag){
            var value = $('#'+idValue+'1').select2('val');
            if(value[0])
            {
                //change all the elements in an array as integer 
                filterValues[idValue] = $('#'+e.target.id).select2('val').map(function(v) {
                    return parseInt(v, 10);
                });
                $('#'+idValue).select2('val', filterValues[idValue]);
                $('#'+idValue).trigger('change',false);
                setMask('#wholepage');

                listJobCandidates(1);

    
            }
            else 
            {
                //if no values are available for the dropdown, make the variable as empty for filter 
                filterValues[idValue] = [];
                $('#s2id_'+idValue).select2('val', '');
                $('#'+idValue+' option:selected').prop('selected', false);
                $('#'+idValue).addClass('empty');


                setMask('#wholepage');

                listJobCandidates(1);

            }
        }
    });
    // function to reset filters
    function resetFilter() {

        for(var i in filterDropDownArray)
            {
                $('#s2id_'+filterDropDownArray[i]).select2('val','');
                $('#'+filterDropDownArray[i]+' option:selected').prop('selected', false);
                $('#'+filterDropDownArray[i]).addClass('empty');
            }
        filterValues = {
            candidatejobpost : [],
            candidatePreferedJobpostLocation : [],
            candidateStatusFilter : []
        }
    

        listJobCandidates(0);
    }

    // Reset the view form
    function resetCandidatesViewForm() {
        $('#viewWorkPermit,#viewFirstName,#viewSalutation,#viewContactNumber,#viewGender,#viewLastName,#viewDOB,#viewEmail,#viewJobPost,#viewCurrentEmployer,' +
            '#viewBloodGroup,#viewMaritalStatus,#viewNationality,#viewFathersName,#viewMothersName,#viewNoticePeriod,#viewCurrentCTC,#viewExpectedCTC,#viewApartmentName,' +
            '#viewStreet,#viewCity,#viewState,#viewPincode,#viewCountry,#viewPassportNumber,#viewNationalIdentificationNumber,#viewVerifierName,#viewVerifierEmail,#viewVerifierPhoneNumber,' +
            '#viewPreferredLocation,#viewLanguagesKnown,#resumeUrl,#profileUrl,#viewResumeFileName,#downLoadResume,#viewResumeFileType,#viewCurrency,#viewOtherWorkPermits').html('');

        // Rest the data tables of the sub tabs
        var table1 = $('#view_table_editable1').DataTable();
        table1.clear().draw();

        var table2 = $('#view_table_editable2').DataTable();
        table2.clear().draw();

        var table3 = $('#view_table_editable3').DataTable();
        table3.clear().draw();

        var table4 = $('#view_table_editable4').DataTable();
        table4.clear().draw();
    }

    // function to rest candidates add edit form
    function resetCandidatesAddEditForm() {

        // Set default tab for main tab
        $('#tab5_2').removeClass('active');
        $('#mainTab a:first').tab('show');

        // Set default tab for sub tab
        $('#tab2_2,#tab2_3,#tab2_4').removeClass('active');
        $('#subTab a:first').tab('show');

        // reseting the form and removing the validation errors //
        $('#s2id_currency,#s2id_Country,#s2id_Salutation,#s2id_Gender,#s2id_Jobpost,#s2id_BloodGroup,' +
            '#s2id_MaritalStatus,#s2id_PreferredLocation,#s2id_WorkPermit').removeClass('form-error');

        $('#currency,#s2id_Country,#s2id_Salutation,#s2id_Gender,#s2id_Jobpost,#s2id_BloodGroup,' +
            '#s2id_MaritalStatus').select2('val', '').trigger('change', [{ initialCheck: true }]);

        $('#CandidateDOB').datepicker('setDate', '');

        $('#Country,#Salutation,#Gender,#Jobpost,#BloodGroup,#MaritalStatus,#CandidateDOB').trigger('change', [{ initialCheck: true }]);

        $('#s2id_LanguagesKnown,#s2id_PreferredLocation,#s2id_WorkPermit').removeClass('form-error');

        $('#s2id_LanguagesKnown,#s2id_PreferredLocation,#s2id_WorkPermit').select2('val', '');

        $('#LanguagesKnown option:selected').prop('selected', false);
        $('#PreferredLocation option:selected').prop('selected', false);
        $('#WorkPermit option:selected').prop('selected', false);

        $('#LanguagesKnown,#PreferredLocation,#WorkPermit').addClass('empty');

        $('#firstName,#contactNumber,#lastName,#email,#currentemployer,#MotherName,#FatherName,#NoticePeriod,#CurrentCTC,#Nationality,#ExpectedCTC,' +
            '#ApartmentName,#Street,#City,#State,#PinCode,#PassportNumber,#NationalIdentificationNumber,#VerifierName,#VerifierEmailId,#VerifierPhoneNumber,#otherworkpermits')
            .val('')
            .trigger('change', [{ initialCheck: true }]);


        // Empting the resume file array and profile file array
        AddResumeFileNameArray = [];
        AddProfileFileNameArray = [];
        educationrowcount = 1;
        experiencerowcount = 1;
        skillsrowcount = 1;
        certificationrowcount = 1;
        addFlag = 0;
        editFlag = 0;
        isDirtyFormCandidates = false;
        // Resetting the resume and profile file area
        setDefaultFilePreview('resume');
        setDefaultFilePreview('profile');

        // Restting the tabs
        $("#formJobCandidateBasicData").validate().resetForm();
        $("#formJobCandidateBioData").validate().resetForm();
    }
    /**create file names for resume and logo files to store in s3 bucket */
    function creates3FileName(filetoupload, uploadFilesForJobcandidates) {
        d = new Date();
        dformat = [d.getFullYear(), d.getMonth() + 1, d.getDate()].join('-') + '.' + [d.getHours(), d.getMinutes(), d.getSeconds()].join(':');
        uploadFilesForJobcandidates = uploadFilesForJobcandidates[0];
        file = uploadFilesForJobcandidates;

        /** file name creation **/
        if (filetoupload == 'resume') {
            bucketFileName = 'resume' + '?' + dformat + '?' + '?' + file.name;
            return bucketFileName;
        } else {
            bucketFileName = 'profile' + '?' + dformat + '?' + '?' + file.name;
            return bucketFileName;
        }
    }
    // Common alert message
    function commonAlertMessage(panel) {
        $(window).scrollTop(0);
        jAlert({
            panel: panel,
            msg: 'There seems to be some technical issues. Please try after some time.',
            type: 'warning'
        });
    }
    // Common access denied alert message
    function commonAccessDeniedAlert(panel) {
        removeMask();
        $(window).scrollTop(0);
        jAlert({
            msg: 'Sorry, Access denied',
            type: 'warning'
        });
    }
    // Functional error alert message
    function errorAlertMessage(panel) {
        $(window).scrollTop(0);
        jAlert({
            panel: panel,
            msg: 'Something went wrong. Please contact system administrator.',
            type: 'warning'
        });
    }
    // To display validation errors
    function displayValidationErrors(validationErrors) {
        for (var key in validationErrors) {
            switch (key) {
                case 'ERR-607':
                    setValidationErrors('add', 'ApartmentName', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-604':
                    setValidationErrors('add', 'contactNumber', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-605':
                    setValidationErrors('add', 'email', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-608':
                    setValidationErrors('add', 'Street', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-609':
                    setValidationErrors('add', 'City', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-610':
                    setValidationErrors('add', 'State', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-611':
                    setValidationErrors('add', 'Country', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-612':
                    setValidationErrors('add', 'PinCode', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-616':
                    setValidationErrors('add', 'Salutation', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-617':
                    setValidationErrors('add', 'firstName', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-618':
                    setValidationErrors('add', 'lastName', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-619':
                    setValidationErrors('add', 'Gender', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-620':
                    setValidationErrors('add', 'CandidateDOB', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-621':
                    setValidationErrors('add', 'BloodGroup', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-622':
                    setValidationErrors('add', 'MaritalStatus', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-623':
                    setValidationErrors('add', 'Nationality', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-624':
                    setValidationErrors('add', 'PreferredLocation', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-626':
                    setValidationErrors('add', 'PassportNumber', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-627':
                    setValidationErrors('add', 'FatherName', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-628':
                    setValidationErrors('add', 'MotherName', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-629':
                    setValidationErrors('add', 'VerifierName', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-630':
                    setValidationErrors('add', 'VerifierEmailId', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-631':
                    setValidationErrors('add', 'VerifierPhoneNumber', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-632':
                    setValidationErrors('add', 'Jobpost', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-633':
                    setValidationErrors('add', 'NoticePeriod', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-634':
                    setValidationErrors('add', 'CurrentCTC', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-635':
                    setValidationErrors('add', 'ExpectedCTC', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-636':
                    setValidationErrors('add', 'LanguagesKnown', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-637':
                    $(window).scrollTop(0);
                    jAlert({
                        panel: $('#addJobCandidatesPanel'),
                        msg: validationErrors[key],
                        type: 'info'
                    });
                    break;
                case 'ERR-638':
                    setValidationErrors('add', 'NationalIdentificationNumber', validationErrors[key]);
                    commonAlertMessage($('#addJobCandidatesPanel'));
                    break;
                case 'ERR-625':
                    $(window).scrollTop(0);
                    jAlert({
                        panel: $('#addJobCandidatesPanel'),
                        msg: validationErrors[key],
                        type: 'warning'
                    });
                    break;
                case 'ERR-639':
                    $(window).scrollTop(0);
                    jAlert({
                        panel: $('#addJobCandidatesPanel'),
                        msg: validationErrors[key],
                        type: 'warning'
                    });
                    break;
                case 'ERR-640':
                    $(window).scrollTop(0);
                    jAlert({
                        panel: $('#addJobCandidatesPanel'),
                        msg: validationErrors[key],
                        type: 'warning'
                    });
                    break;
                case 'ERR-641':
                    $(window).scrollTop(0);
                    jAlert({
                        panel: $('#addJobCandidatesPanel'),
                        msg: validationErrors[key],
                        type: 'warning'
                    });
                    break;
                default:
                    $(window).scrollTop(0);
                    jAlert({
                        panel: $('#addJobCandidatesPanel'),
                        msg: 'Invalid Data',
                        type: 'warning'
                    });
                    break;
            }
        }
    }
    // Function to list candidates details
    function listJobCandidates(isSearch) {
        var mutation = `  mutation CommentQuery(
            $searchString: String!
            $isDropDownCall: Int!
            $jobPostId: [Int]
            $preferredLocation: [Int]
            $status: [Int]
          ) {
            listJobCandidates(
              searchString: $searchString
              isDropDownCall: $isDropDownCall
              jobPostId: $jobPostId
              preferredLocation: $preferredLocation
              status: $status
            ) {
              errorCode
              message
              jobCandidates {
                Candidate_Id
                Source_Type
                Photo_Path
                First_Name
                Last_Name
                Mobile_No
                Job_Post_Id
                Job_Post_Name
                Resume
                Candidate_Status
                Status_Id
                Preferred_Location
                First_Interview_Date
                Last_Interview_Date
                Last_Stage
                Personal_Email
              }
            }
          }`;
        /** Enable the code after jobpost api integration */
        var  tmpJobPostId = filterValues.candidatejobpost; // enable after jobpost API is integration
        var tmpPreferredLocation = filterValues.candidatePreferedJobpostLocation;
        var tmpSatus  = filterValues.candidateStatusFilter;
        var variables =
        {
            "searchString": $('#search').val() ? $('#search').val() : $('#searchcandidates').val() ? $('#searchcandidates').val() : '',
            "jobPostId": tmpJobPostId,
            "status"   : tmpSatus,
            "isDropDownCall": 0,
            "preferredLocation": tmpPreferredLocation
        };
 
        /**list Job Candidates */
        $.ajax({
            method: 'POST',
            url: atsBaseURL,
            headers: getGraphqlAPIHeaders(ipAddress),
            data: JSON.stringify({
                query: mutation,
                variables: variables
            }),

            success: function (result) {
                if (result.data) {
                    $('#tab2').removeClass('active');
                    $('#tab5_2').removeClass('active');
                    $('#tab1').addClass('active');
                    $('#tab5_1').addClass('active');

                            $('#tab3').removeClass('active');
                            $('#tab5_3').removeClass('active');
                    
                            /**if data retrieved successfully */
                            jobCandidates = result.data.listJobCandidates.jobCandidates;
                            if (jobCandidates.length === 0) {
                                $('#listJobCandidatesId').html('');
                                var img = pageUrl() + 'images/records-not-found.png';
                                $('#listJobCandidatesId').append(
                                    '<img class="recruitment-search-not-found" src="' + img + '" alt="searchnotfound">'
                                );
                                if (!isSearch) {
                                    $('#searchfilter,#paginationsm,#searchfiltersm,.client-verticalstyle').hide();

                                    $('#listJobCandidatesId').append('<div class="recruitment-records-not-found">No candidates created yet.</div>');
                                    removeMask();
                                } else {
                                    // $('#paginationsm').hide();
                                    $('#listJobCandidatesId').append('<div class="recruitment-records-not-found">No candidates match your search.</div>');
                                    removeMask();
                                }
                                var paginationId = '#listJobCandidatesId';
                                var dataclass = '.candidatesdetails';
                                var paginationSize = 7;
                
                                $.pagination(1, 6, paginationId, dataclass,paginationSize);
                
                            }
                            else {
                                $('#searchfilter').show();
                                $('#paginationsm').show();
                                $('#searchfiltersm').show();
                                if (addFlag) {
                                    // Reset data table after candidates added successfully
                                    resetDataTable();
                                    removeMask();
                                    $(window).scrollTop(0);
                                    jAlert({
                                        panel: $('#listJobCandidates'),
                                        msg: 'Candidates added successfully.',
                                        type: 'info'
                                    });
                                    addFlag = 0;
                                    addByTab = 0;
                                }
                        if (editFlag) {
                            // Reset data table after candidates added successfully
                            resetDataTable();
                            clearLockAPI(orgCode, 'JobCandidates', parseInt(candidateId), employeeId, '#listJobCandidates', atsBaseURL, function (error, data) {
                                if (data) {
                                    removeMask();
                                    $(window).scrollTop(0);
                                    jAlert({
                                        panel: $('#listJobCandidates'),
                                        msg: 'Candidates updated successfully',
                                        type: 'info'
                                    });
                                    editFlag = 0;
                                }
                            });


                        }
                        $('#listJobCandidatesId').html('');
                        /**list all job candidates */
                        for (var details in jobCandidates) {
                            // retrieveS3Bucketfiles(jobCandidates[details]);   
                            setCandidateInfomations(jobCandidates[details], '', '', '', '',details);

                        }
                        if(isSearch)
                        {
                            for(var i in filterValues)
                            {
                                if(filterValues[i].length===0)
                                {
                                    
                                    var dropdown = i ; 
                                    $('#'+dropdown).addClass('empty');
                                    $('#'+dropdown+'1').addClass('empty');

                                }
                            }
                        }
                        
                            

                        
                    }
                }
                else {
                    var error = JSON.parse(result.errors[0].message);
                    var errorCode = error.errorCode;
                    $(window).scrollTop(0);

                    switch (errorCode) {
                        case 705:
                        case 706:
                            commonAlertMessage($('#listJobCandidates'));
                            break;
                        case 719:
                            commonAccessDeniedAlert($('#listJobCandidates'));
                            break;
                        default:
                            errorAlertMessage($('#listJobCandidates'));
                            break;
                    }
                    removeMask();
                }
            },
            error: function (error) {
                removeMask();
                errorAlertMessage($('#listJobCandidates'));
            }
        });
           
        $('.client-search-btn').removeClass('open');
        $('.client-search-option').removeClass('open');
        

    }
    // Function to set candidates details in cards
    function setCandidateInfomations(candidateInfo, profileImg, profilealtname, resumeURL, profileURL,details) {
        var style = '';
        listCandidatesCount++;

        var profileImg = pageUrl() + 'images/defaultPhot.jpg';
        var profilealtname = 'avatar';
        var style = "";

        var colorName = candidateInfo.Status_Id === 10 ? '#01579b' : candidateInfo.Status_Id === 11 ? '#4a148c' : candidateInfo.Status_Id === 3 ? '#f44336' : '#1b5e20';

        $('#listJobCandidatesId').append('<div candidateid= "' +
            candidateInfo.Candidate_Id + '"profileURL= "' 
            + profileURL + '"resumeURL="' 
            + resumeURL +
            '" style="cursor:pointer" class="col-lg-6 col-md-6 col-sm-6 candidatesdetails"> <div style="border-left:10px solid '+colorName+'" class="panel widget-member2 card-list"> <div class = "row"><span class="list-icons" candidateid= "' +
            + candidateInfo.Candidate_Id +             
            '" id = "deleteCandidates"><i class="fa fa-times edit-icons" title="Delete" aria-hidden="true"></i></span><span class="list-icons" candidateid= "' +
            candidateInfo.Candidate_Id +
            '" profileURL= "' 
            + profileURL + '"resumeURL="' 
            + resumeURL +
            '"key="' +
            details +
            '" id = "editCandidates"><i class="fa fa-pencil edit-icons" title="Edit" aria-hidden="true"></i></span></div><div class="row cardsclass"><div class="col-lg-2 col-xs-3"><img style="' +
            style +
            '" src="' +
            profileImg +
            '" alt="' +
            profilealtname +
            '" class="pull-left img-responsive logo-image"> </div> <div class="col-lg-10 col-xs-9"> <div class="clearfix"> <h3 class="m-t-0 member-name workflow-card-heading"><strong>' +
            candidateInfo.First_Name + ' ' + candidateInfo.Last_Name +
            '</strong> </h3> </div><div class="row"> <div class="col-sm-12 m-t-10"> '+
            "Contact No. : "+ candidateInfo.Mobile_No +
             ' </div></div><div class="row"><div class="col-sm-12 m-t-10">' +
            "Job Post : "+ fnCheckNull(candidateInfo.Job_Post_Name) +
             ' </div> </div> <div class="row"><div class="col-sm-12 m-t-10">' +
             "Status : "+ fnCheckNull(candidateInfo.Candidate_Status) +
              ' </div> </div> </div> </div><div class= "row candidate-panel-content" style="margin-top:10px; border-top : 1px solid lightsteelblue;"> <div><i style="font-size:20px; float: left; margin-top: 10px" class="fa fa-envelope-o client-mail-icon recruitment-mail-icons recruitement-feature-cursor-none" aria-hidden="true"></i> </div> </div> </div> </div>'
        );
        // Empty profileURL,resumeURL after setting as attributes of Cards
        profileURL = '';
        resumeURL = '';
        /**search and pagination */
        if (jobCandidates.length > 0) {
            if (jobCandidates.length === listCandidatesCount) {
                listCandidatesCount = 0;
                /**call the pagination function in customPagination.js */
                var paginationId = '#listJobCandidatesId';
                var dataclass = '.candidatesdetails';
                var paginationSize = 7;

                $.pagination($('#listJobCandidatesId .candidatesdetails').length, 6, paginationId, dataclass,paginationSize); /** total No.of Records and max limit per page*/
            }
        } else {
            $('searchfilter').hide();
            $('#paginationsm').hide();
        }
        removeMask();

    }
    // Function to get languages list,country list and course list
    function getLanguageCoursecountry() {
        setMask('#wholepage');

        /**list the countries */
        $.ajax({
            method: 'POST',
            url: atsBaseURL,
            headers: getGraphqlAPIHeaders(ipAddress),
            data: JSON.stringify({
                query: `{getCountries { errorCode message Countries { Country_Code Country_Name}}}`
            }),
            success: function (result1) {
                if (result1.data) {
                    // resetting the country select html tag
                    $('#Country').html('')
                    var countries = result1.data.getCountries.Countries;
                    for (var x in countries) {
                        $('#Country').append("<option value='" + countries[x].Country_Code + "'>" + countries[x].Country_Name + '</option>');
                    }
                    if (editFlag == 1) {
                        $('#s2id_Country').select2('val', prefillCandidateInfo.pCountry);
                        $('#Country').trigger('change', [{ initialCheck: true }])
                    }
                    /**list the countries */
                    $.ajax({
                        method: 'POST',
                        url: atsBaseURL,
                        headers: getGraphqlAPIHeaders(ipAddress),
                        data: JSON.stringify({
                            query: `{
                                getLanguages {errorCode message Languages { Lang_Id Language_Name}}
                            }`
                        }),
                        success: function (result2) {

                                    if (result2.data) {
                                        // resetting the language select html tag
                                        $('#LanguagesKnown').html('')
                                        removeMask();
                                        var languages = result2.data.getLanguages.Languages;
                                        for (var x in languages) {
                                            $('#LanguagesKnown').append("<option value='" + languages[x].Lang_Id + "'>" + languages[x].Language_Name + '</option>');
                                        }
                                        if (editFlag == 1) {
                                            var setLanguageId = [];
                                            for (var i = 0; i <= prefillCandidateInfo.Lang_Known.length - 1; i++) {
                                                setLanguageId.push(prefillCandidateInfo.Lang_Known[i].Lang_Id);
                                            }
                                            $('#s2id_LanguagesKnown').select2('val', setLanguageId);
                                            $('#LanguagesKnown').trigger('change', [{ initialCheck: true }])
                                            setLanguageId = [];
                                            // 
                                            $('#s2id_Jobpost').select2('val', prefillCandidateInfo.Job_Post_Id);
                                            $('#Jobpost').trigger('change', [{ initialCheck: true }]);

                                    var workPermitIds = [];
                                    if(prefillCandidateInfo.Work_Permit.length > 0){
                                        for (var i = 0; i <= prefillCandidateInfo.Work_Permit.length - 1; i++) {
                                            workPermitIds.push(prefillCandidateInfo.Work_Permit[i].Work_Permit_Id);
                                        }
                                        $('#s2id_WorkPermit').select2('val', workPermitIds);
                                        $('#WorkPermit').trigger('change', [{ initialCheck: true }])

                                    }
                                    
                                    workPermitIds = [];
                                }
                                
                                /** hide the candidates list page show add candidates page **/
                                $('#AddEditJobCandidates').show();
                                $('#listJobCandidates').hide();
                            }
                            else {
                                removeMask();
                                resetDataTable();

                                addFlag = 0;
                                editFlag = 0;
                                var error = JSON.parse(result2.errors[0].message);
                                var errorCode = error.errorCode;
                                $(window).scrollTop(0);
                                switch (errorCode) {
                                    case 705:
                                    case 706:
                                        commonAlertMessage('#listJobCandidates');
                                        break;
                                    case 725:
                                    default:
                                        errorAlertMessage($('#listJobCandidates'));
                                        break;
                                }
                            }
                        },
                        error: function (result3) {
                            removeMask();
                            resetDataTable();

                            addFlag = 0;
                            editFlag = 0;

                            errorAlertMessage($('#listJobCandidates'));
                        }
                    });
                }
                else {
                    removeMask();
                    resetDataTable();

                    addFlag = 0;
                    editFlag = 0;

                    var error = JSON.parse(result1.errors[0].message);
                    var errorCode = error.errorCode;
                    $(window).scrollTop(0);
                    switch (errorCode) {
                        case 705:
                        case 706:
                            commonAlertMessage('#listJobCandidates');
                            break;
                        case 708:
                        default:
                            errorAlertMessage($('#listJobCandidates'));
                            break;
                    }
                }
            },
            error: function (result4) {
                removeMask();
                resetDataTable();

                addFlag = 0;
                editFlag = 0;

                errorAlertMessage($('#listJobCandidates'));
            }
        });
    }
    // Function  to delete candidates
    function deleteJobCandidates(candidateId) {
        setMask('#wholepage');
        if (candidateId > 0 && !isNaN(candidateId)) {
            if (accessRightsParams['deleteAccessRights']) {
                var mutation = `mutation CommentQuery($candidateId: Int!) {
                    deleteJobCandidates(candidateId: $candidateId) {
                      errorCode
                      message
                      validationError
                    }
                  }`;
                var variable = {
                    candidateId: parseInt(candidateId),
                };
                setLockAPI(orgCode, 'JobCandidates', parseInt(candidateId), employeeId, '#listJobCandidates', atsBaseURL, function (error, data) {
                    if (data) {
                        $.ajax({
                            type: 'POST',
                            async: false,
                            url: atsBaseURL,
                            contentType: 'application/json',
                            headers: getGraphqlAPIHeaders(ipAddress),
                            data: JSON.stringify({
                                query: mutation,
                                variables: variable
                            }),
                            success: function (result) {
                                if (result.data !== null) {
                                    listJobCandidates(0);
                                    // Show alert when files are unable to upload
                                    if (addFlag) {
                                        $(window).scrollTop(0);
                                        jAlert({
                                            panel: $('#panelstart'),
                                            msg: 'Could not be able to upload the file. Please try again.',
                                            type: 'info'
                                        });
                                    }
                                    else {
                                        jAlert({
                                            panel: $('#listJobCandidates'),
                                            msg: 'Candidates deleted successfully',
                                            type: 'info'
                                        });
                                    }
                                } else {
                                    /**handle errors while delete */
                                    var error = JSON.parse(result.errors[0].message);
                                    var errorCode = error.errorCode;
                                    $(window).scrollTop(0);
                                    switch (errorCode) {
                                        case 775:
                                            jAlert({
                                                panel: $('#listJobCandidates'),
                                                msg: 'We regret to inform you that deletion is not possible at this time, as the candidate is currently in either an interview scheduled or shortlisted status.',
                                                type: 'warning'
                                            });
                                            break;
                                        case 705:
                                        case 706:
                                            commonAlertMessage($('#listJobCandidates'));
                                            break;
                                        case 719:
                                            commonAccessDeniedAlert($('#listJobCandidates'));
                                            break;
                                        case 703:
                                        default:
                                            errorAlertMessage($('#listJobCandidates'));
                                            break;
                                    }
                                }
                                /**clear lock after delte */
                                clearLockAPI(orgCode, 'JobCandidates', parseInt(candidateId), employeeId, '#listJobCandidates', atsBaseURL, function (error, data) { });
                                removeMask();
                            },
                            error: function (result) {
                                clearLockAPI(orgCode, 'JobCandidates', parseInt(candidateId), employeeId, '#listJobCandidates', atsBaseURL, function (error, data) { });
                                removeMask();
                                errorAlertMessage($('#listJobCandidates'));
                            }
                        });
                    }
                });
            }
            else {
                removeMask();
                commonAccessDeniedAlert($('#listJobCandidates'));
            }
        }
        else {
            removeMask();
            jAlert({ msg: 'Kindly select ' + $('#lblFormName').html(), type: 'info' });
        }
    }



    $('#tab3').on('click',function(e){
        if (resumeFileName || AddResumeFileNameArray.length > 0) {
            var interviewCandidateId = parseInt($(this).attr('candidateid'),10);
            var action = 'edit' ;
            listCandidatesInterview(interviewCandidateId,action);
        }
        else{
            removeMask();
            $(window).scrollTop(0);
            /**error while ajax call */
            jAlert({
                panel: $('#addJobCandidatesPanel'),
                msg: 'Please upload resume',
                type: 'warning'
            });

            $('#tab1').addClass('active');
            $('#tab5_1').addClass('active');
            $('#tab2').removeClass('active');
            $('#tab5_2').removeClass('active');

            // //  ---> scoreupdation
            // $('#tab3').removeClass('active');
            // $('#tab5_3').removeClass('active');
        }
    });


    $('#tab_view_3').on('click',function() {
        var interviewCandidateId = parseInt(chosenCandidatesIndexValues);
        var action = 'view' ;
        listCandidatesInterview(interviewCandidateId,action);
    })
});