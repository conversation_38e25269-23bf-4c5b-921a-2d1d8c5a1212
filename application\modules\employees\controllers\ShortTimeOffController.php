<?php

include APPLICATION_PATH."/validations/Validations.php";

class Employees_ShortTimeOffController extends Zend_Controller_Action
{
    protected $_basePath       = null;
    protected $_dbAccessRights = null;
    protected $_logEmpId       = null;
    protected $_ehrTables      = null;
    protected $_orgDateFormat  = null;
	protected $_validation     = null;
    protected $_dbShortTimeOff = null;
    protected $_dbLeave        = null;
	protected $_dbComment      = null;
	protected $_dbPersonal     = null;
	protected $_formNameA      = 'Short Time Off';
	protected $_formNameB      = 'Permission Settings';
	protected $_hrappMobile    = null;
	protected $_dbEmployee     = null;
    protected $_orgDetails     = null;
	protected $_dbLocation 	   = null;
	protected $_dbDept 		   = null;
	protected $_dbManager 	   = null;
	protected $_myTeamAccess 			    = null;
    protected $_selfServiceAccess 		    = null;
    protected $_myTeamShortTimeoffFormId 	    = 352;
    protected $_selfServiceShortTimeoffFormId   = 353;

	
    public function init()
    {
		$this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
		if ($this->_hrappMobile->checkAuth())
        {
			$this->_dbCommonFun    = new Application_Model_DbTable_CommonFunction();
            $this->_basePath       = new Zend_View_Helper_BaseUrl();
            $this->_dbAccessRights = new Default_Model_DbTable_AccessRights();
            $this->_ehrTables      = new Application_Model_DbTable_Ehr();
            $this->_dbEmployee     = new Employees_Model_DbTable_Employee();
			$this->_validation 	   = new Validations();
            $this->_dbLeave        = new Employees_Model_DbTable_Leave();
			$this->_dbShortTimeOff = new Employees_Model_DbTable_ShortTimeOff();
            $this->_dbComment      = new Payroll_Model_DbTable_PayrollComment();
			$this->_dbPersonal     = new Employees_Model_DbTable_Personal();
			$this->_dbLocation     = new Organization_Model_DbTable_Location();
			$this->_dbDept         = new Organization_Model_DbTable_Department();
			$this->_dbManager      = new Default_Model_DbTable_Manager();
			
			$userSession                   = $this->_dbCommonFun->getUserDetails ();
            $this->_logEmpId               = $userSession['logUserId'];
			$this->_shortTimeOff = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameA);
			$this->_shortLeaveRequestSettings = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameB);


			$this->_myTeamAccessRights      = $this->_dbAccessRights->employeeAccessRightsBasedOnFormId($this->_logEmpId,$this->_myTeamShortTimeoffFormId);
            $this->_selfServiceAccessRights = $this->_dbAccessRights->employeeAccessRightsBasedOnFormId($this->_logEmpId,$this->_selfServiceShortTimeoffFormId);
            $this->_myTeamAccess            = $this->_myTeamAccessRights['Employee'];
            $this->_selfServiceAccess       = $this->_selfServiceAccessRights['Employee'];

			if (Zend_Registry::isRegistered('orgDetails'))
				$this->_orgDetails = Zend_Registry::get('orgDetails');
        }
        else
        {
            if (Zend_Session::namespaceIsset('lastRequest'))
                Zend_Session:: namespaceUnset('lastRequest');
            
            $session = new Zend_Session_Namespace('lastRequest');
            $session->lastRequestUri = 'v3/employee-self-service/short-time-off';
            $this->_redirect('auth');
        }
    }

    public function indexAction()
    {
		$checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

		if ($checkSessionAuth)
		{
			$this->_helper->layout()->disableLayout()->setLayout('admin_layout');
		
			$this->view->formNameA       = $this->_formNameA;
			$this->view->formNameB       = $this->_formNameB;
			
			$this->view->customFormNameA = $this->_ehrTables->getCustomForms($this->_formNameA);
			$this->view->customFormNameB = $this->_ehrTables->getCustomForms($this->_formNameB);
			
			$this->view->shortTimeOffUser = array('Is_Manager' => $this->_shortTimeOff['Employee']['Is_Manager'],
													   'View'       => $this->_shortTimeOff['Employee']['View'],
													   'Add'        => $this->_shortTimeOff['Employee']['Add'],
													   'Update'     => $this->_shortTimeOff['Employee']['Update'],
													   'Delete'     => $this->_shortTimeOff['Employee']['Delete'],
													   'Op_Choice'  => $this->_shortTimeOff['Employee']['Optional_Choice'],
													   'Admin'      => $this->_shortTimeOff['Admin'],
													   'Session_Id' => $this->_logEmpId,
													   'Employee_Name' => $this->_dbPersonal->employeeId($this->_logEmpId));
			
			$this->view->employeeDetails = $this->_dbCommonFun->listEmployeesDetails ($this->_formNameA, '', $this->_logEmpId);
			
			$this->view->shortLeaveSettingsUser = array('View' => $this->_shortLeaveRequestSettings['Employee']['View'],
														'Update' => $this->_shortLeaveRequestSettings['Employee']['Update']);
			
			$this->view->orgFormat = $this->_ehrTables->orgDateformat();

			$this->view->orgDetails      = $this->_orgDetails;
			$this->view->serviceProvider = $this->_dbEmployee->getEmployeeServiceProviderList($this->_logEmpId);

			$this->view->empLocation     = $this->_dbLocation->getLocationPair();
			$this->view->deptHierarchy   = $this->_dbDept->getDepartmentTypes();
			$this->view->managerNames 	 = $this->_dbManager->managerName('', '', 'Emp_First_Name', 'ASC', '', '', '', '', '', 'Employees', 0);
			$this->view->newImg          = $this->_basePath->baseUrl('images/new.png');
		} else {
			$this->_redirect('auth');
		}
	}
    
	//list short leave request
	public function listShortTimeOffAction()
    {
		$this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-short-time-off', 'json')->initContext();
			
            if ($this->_shortTimeOff['Employee']['View'] == 1 )
            {
                $sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
				
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$employeeName = $this->_getParam('employeeName', null);
				$employeeName = filter_var($employeeName, FILTER_SANITIZE_STRIPPED);

				$requestFor = $this->_getParam('RequestFor', null);
				$requestFor = filter_var($requestFor, FILTER_SANITIZE_STRIPPED);
				
				$startDateBegin = $this->_getParam('StartDateBegin', null);
				$startDateBegin = filter_var($startDateBegin, FILTER_SANITIZE_STRIPPED);
				
				$startDateEnd = $this->_getParam('StartDateEnd', null);
				$startDateEnd = filter_var($startDateEnd, FILTER_SANITIZE_STRIPPED);
				
				$endDateBegin = $this->_getParam('EndDateBegin', null);
				$endDateBegin = filter_var($endDateBegin, FILTER_SANITIZE_STRIPPED);
				
				$endDateEnd = $this->_getParam('EndDateEnd', null);
				$endDateEnd = filter_var($endDateEnd, FILTER_SANITIZE_STRIPPED);
				
				$status = $this->_getParam('Status', null);
				$status = filter_var($status, FILTER_SANITIZE_STRIPPED);

				$serviceProviderId = $this->_getParam('ServiceProviderId', null);
				$serviceProviderId = filter_var($serviceProviderId, FILTER_SANITIZE_NUMBER_INT);

				$location = $this->_getParam('Location',null);
                $location = filter_var($location, FILTER_SANITIZE_NUMBER_INT);

                $department = $this->_getParam('Department',null);
                $department = filter_var($department, FILTER_SANITIZE_NUMBER_INT);
                
                $managerId = $this->_getParam('Manager_Id', null);
				$managerId = filter_var($managerId, FILTER_SANITIZE_NUMBER_INT);

				$earlyCheckoutShortTimeOff = $this->_getParam('Early_Checkout_Short_Time_Off', null);
				$earlyCheckoutShortTimeOff = filter_var($earlyCheckoutShortTimeOff, FILTER_SANITIZE_NUMBER_INT);
				
				$lateAttendanceShortTimeOff = $this->_getParam('Late_Attendance_Short_Time_Off', null);
				$lateAttendanceShortTimeOff = filter_var($lateAttendanceShortTimeOff, FILTER_SANITIZE_NUMBER_INT);

				$encodedParams = $this->_getParam('prerequisiteData',null); // Retrieve the encoded value from URL
				$salaryStartDate = NULL;
				$salaryEndDate = NULL;
				$payslipEmployeeIds = NULL;
				if(!is_null($encodedParams)){
					$decodedParams = base64_decode($encodedParams);
					parse_str($decodedParams, $params);

					if(isset($params['payslipEmployeeIds']) && isset($params['salaryStartDate']) && $params['salaryEndDate']){
						$salaryStartDate = $params['salaryStartDate'];
						$salaryEndDate = $params['salaryEndDate'];
						$payslipEmployeeIds = explode(',',$params['payslipEmployeeIds']);
						$status = array('Applied', 'Returned', 'Cancel Applied');
					}
				}

                $searchArr = array('employeeName'   => $employeeName,
								   'requestFor'		=> $requestFor,
								   'status'         => $status,
								   'startDateBegin' => $startDateBegin,
								   'startDateEnd'   => $startDateEnd,
								   'endDateBegin'   => $endDateBegin,
								   'endDateEnd'     => $endDateEnd,
								   'serviceProviderId' => $serviceProviderId,
								   'Location_Id'    => $location,
								   'Department_Id'  => $department,
								   'Manager_Id'     => $managerId,
								   'salaryStartDate' => $salaryStartDate,
								   'salaryEndDate' => $salaryEndDate,
								   'payslipEmployeeIds' => $payslipEmployeeIds,
								   'Early_Checkout_Short_Time_Off' => $earlyCheckoutShortTimeOff,
								   'Late_Attendance_Short_Time_Off' => $lateAttendanceShortTimeOff);
								   
				$shortTimeOffAccess = array( 'Is_Manager' => $this->_shortTimeOff['Employee']['Is_Manager'],
										  'Admin'       => $this->_shortTimeOff['Admin'],
										  'LogId'       => $this->_logEmpId,
										  'FormName'    => $this->_formNameA);
				
				$this->view->result = $this->_dbShortTimeOff->listShortTimeOff ($page, $rows, $sortField, $sortOrder, $searchAll, $searchArr,$shortTimeOffAccess);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'short-time-off', 'employees');
        }
    }
	
	//Update Short time off
	public function updateShortTimeOffAction()
    {
        $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('update-short-time-off', 'json')->initContext();
			
			$shortTimeOffId = $this->_getParam('shortTimeOffId', 0);
            $shortTimeOffId = filter_var($shortTimeOffId, FILTER_SANITIZE_NUMBER_INT);
            
			if ((empty($shortTimeOffId) && $this->_shortTimeOff['Employee']['Add'] == 1 || $this->_myTeamAccess['Add'] == 1 || $this->_selfServiceAccess['Add'] == 1) || (!empty($shortTimeOffId) && $this->_shortTimeOff['Employee']['Update'] == 1 || $this->_myTeamAccess['Update'] == 1 || $this->_selfServiceAccess['Update'] == 1))
			{
				if ($this->getRequest()->isPost())
				{
					$formData = $this->getRequest()->getPost();
					
					$employeeId      = $this->_validation->intValidation($formData['employeeId']);
					
					$reason          = $this->_validation->alphaNumSpCDotHySlashNewLineValidation($formData['reason']);
					$reason['valid']   = $this->_validation->lengthValidation($reason,2, 600, true);					
					$alternatePerson = $this->_validation->intValidation($formData['alternatePerson']);
					$contactNo       = $this->_validation->phoneValidation($formData['contactNumber']);
					$contactNo['valid'] = $this->_validation->maxLengthValidation($contactNo, 15, true);
					$comments 			= $this->_validation->alphaNumSpCDotHySlashNewLineValidation($formData['comments']);
					$comments['valid'] 	= $this->_validation->lengthValidation($comments, 5, 3000, false);
					$requestFor     	= $this->_validation->alphaValidation($formData['requestFor']);

					if(isset($formData['forwardTo']) && !empty($formData['forwardTo']))
					{
						$forwardTo  = $this->_validation->intValidation($formData['forwardTo']);
						$approverId = $forwardTo['value'];
					}
					else
					{
						$approverId = new Zend_Db_Expr('NULL');
					}

					if (!empty($employeeId['value']) && $employeeId['valid'] &&
						!empty($reason['value']) && $reason['valid'] && $comments['valid'] && 
						!empty($requestFor['value']) && $requestFor['valid'] && ($requestFor['value'] == 'Permission' || $requestFor['value'] == 'On Duty') &&
						strtotime($formData['startDateTime']) <= strtotime($formData['endDateTime']))
					{
						$isValid = false;
						$shortTimeOffArr = array('Short_Time_Off_Id' => $shortTimeOffId,
														'Employee_Id'       => $employeeId['value'],
														'Approver_Id'       => $approverId,
														'Request_For'       => $requestFor['value'],
														'Start_Date_Time'   => $formData['startDateTime'],
														'End_Date_Time'     => $formData['endDateTime'],
														'Total_Hours'       => $formData['totalHrs'],
														'Reason'            => $reason['value'],
														'Alternate_Person'  => $alternatePerson['value'],
														'Contact_Details'   => $contactNo['value'],
														'Approval_Status'   => "Applied");
						$dbAttendance = new Employees_Model_DbTable_Attendance();;
						$shiftDetails = $dbAttendance->getCurrentWorkScheduleDetails($shortTimeOffArr['Employee_Id'],$shortTimeOffArr['Start_Date_Time']);

						// Comprehensive validation: Check shift details and all critical datetime fields
						$requiredDateTimeFields = array('Regular_From', 'Regular_To', 'Consideration_From', 'Consideration_To');
						$isValidShiftDetails = !empty($shiftDetails);

						// Validate all required datetime fields using array operations
						if ($isValidShiftDetails) {
							foreach ($requiredDateTimeFields as $field) {
								if ((!array_key_exists($field, $shiftDetails)) || empty($shiftDetails[$field]) || strtotime($shiftDetails[$field]) === false) {
									$isValidShiftDetails = false;
									break;
								}
							}
						}

						if (!$isValidShiftDetails) {
							$this->view->result = array('success' => false, 'msg'=>'Shift is not scheduled. Please contact your reporting manager / administrator.', 'type'=>'warning');
						}else{
						if($requestFor['value'] === "On Duty") {
							$isValid = false;	
							$validateShortTimeOff = $this->_dbShortTimeOff->getValidateShortTimeOff($employeeId['value']);
							$shortTimeOffSettingsResponse = $validateShortTimeOff['onDutySettings'];
							if(isset($validateShortTimeOff['SettingsList']))
							{
								if (in_array("On Duty", $validateShortTimeOff['SettingsList'])) {
									$isValid = true;	
								}
								else
								{
									$this->view->result = array('success'=>false, 'msg'=>'Short-time off request cannot be submitted at the moment since the On Duty configuration is currently unavailable. Please consider trying again later or reaching out to the system administrator for assistance.', 'type'=>'warning');		
								}	
							}
						} else {
							$shortTimeOffSettingsResponse = $this->_dbShortTimeOff->listShortLeaveRequestSettings($employeeId['value']);
							if(!empty($shortTimeOffSettingsResponse['Settings_Details'])){
								$shortTimeOffSettingsDetails = $shortTimeOffSettingsResponse['Settings_Details'];
								$maxShortTimePerRequest 	 = date('H:i', strtotime("00:00 +{$shortTimeOffSettingsDetails['Max_Short_Time_Per_Request']} minutes"));
								$minShortTimePerRequest 	 = date('H:i', strtotime("00:00 +{$shortTimeOffSettingsDetails['Min_Short_Time_Per_Request']} minutes"));
								$shortTimeOffDetails         = $this->_dbShortTimeOff->getShortTimeOffEmpDetails($shortTimeOffArr,$shortTimeOffSettingsDetails['Period'],'No');
								$totalHours                  = $this->_dbShortTimeOff->calculateShortTimeOffHours($formData['startDateTime'],$formData['endDateTime']);
								// maximum duration in minutes. to convert it to secs just multiply by 60
								$minDurationForRequest 	 		 = $shortTimeOffSettingsDetails['Minimum_Duration'];
								$minDurationInSecs 	 		 = date('H:i', strtotime("00:00 +{$minDurationForRequest} minutes"));
								$settinggMaximumDuration =  $shortTimeOffSettingsDetails['Maximum_Duration'];
								$maxDurationInSecs 			 = $settinggMaximumDuration * 60;
								$totalTimeInSecs   			 = $shortTimeOffDetails['totalTimeInSecs'];
								$totalDurationInSecs 		 = $shortTimeOffSettingsDetails['Total_Duration'] * 60;
								$appliedTotalHoursInSecs     = $shortTimeOffDetails['appliedTotalHoursInSecs'];
								if($shortTimeOffDetails) {
									$shortTimeOffDate = date('Y-m-d',strtotime($shiftDetails['Regular_From']));
									$activationInfo = $this->_dbShortTimeOff->getShortTimeOffActivationDate(
										$shortTimeOffArr['Employee_Id'],
										$shortTimeOffSettingsDetails,
										$shortTimeOffDetails['employeeDateOfJoin'],
										$shortTimeOffDate
									);

									$isShortTimeActivated = ($activationInfo['success'] && $activationInfo['isActivated']) ? 1 : 0;

									if($isShortTimeActivated == 1){
										$minDurationvalidation = strtotime($formData['totalHrs']) >= strtotime($minDurationInSecs);
										if(($shortTimeOffSettingsDetails['Limit_By'] == 'Duration' && ($minDurationvalidation && ($totalTimeInSecs <= $totalDurationInSecs))) ||
										($shortTimeOffSettingsDetails['Limit_By'] == 'Request'  && ((strtotime($formData['totalHrs']) >= strtotime($minShortTimePerRequest) && strtotime($formData['totalHrs']) <= strtotime($maxShortTimePerRequest)))))
										{
											if(($shortTimeOffSettingsDetails['Limit_By'] == 'Request' && (($shortTimeOffDetails['totalCount']) <= $shortTimeOffSettingsDetails['Maximum_Limit'])) ||
											($shortTimeOffSettingsDetails['Limit_By'] == 'Duration' && ($appliedTotalHoursInSecs <= $maxDurationInSecs))) 
											{
												$isValid = true;
											}
											else
											{
												$maxDurationHoursMinutes = $this->_dbCommonFun->convertMinutesToHoursMins($settinggMaximumDuration);
												$validationErrorMesage = ($shortTimeOffSettingsDetails['Limit_By'] == 'Duration') ? "The total applied short time hours should be lesser than or equal to the maximum duration($maxDurationHoursMinutes)" : 'Short Time Off quota has been completed for the selected duration';
												$this->view->result = array('success'=>false, 'msg'=>$validationErrorMesage, 'type'=>'warning');	
											}
										}
										else
										{
											$validationErrorMesage = ($shortTimeOffSettingsDetails['Limit_By'] == 'Duration' && empty($minDurationvalidation)) ? "The total applied short time hours should be greater than or equal to the minimum duration($minDurationForRequest minutes)" : 'The requested duration is outside the allowed range';

											$this->view->result = array('success'=>false, 'msg'=>$validationErrorMesage, 'type'=>'warning');	
										}
									}else{
										$errorMessage = $activationInfo['message'] ?? 'Short Time Off is not yet activated for this employee';
										$this->view->result = array('success'=>false, 'msg'=>$errorMessage, 'type'=>'warning');
									}
								} else {
									$this->view->result = array('success'=>false, 'msg'=>'Unable to apply short time off due to the pending leave closure process', 'type'=>'warning');	
								}
							} else {
								if($shortTimeOffSettingsResponse['Coverage'] && strtolower($shortTimeOffSettingsResponse['Coverage']) === 'custom group'){
									$this->view->result = array('success' => false, 'msg'=>"The employee isn't part of the short time off custom group.As a result, they are not allowed to apply for short time off.", 'type'=>'warning');
								}else{
									$this->view->result = array('success' => false, 'msg'=>"Kindly update permission settings", 'type'=>'warning');
								}
							}
						}
						}
						if($isValid) {
							if ($shortTimeOffId > 0)
							{
								$shortTimeOffRec = $this->_dbShortTimeOff->getShortTimeOffApproval($shortTimeOffId);
							}
							
							$customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
							$customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
							
							if($this->_myTeamAccessRights['Admin']=="admin" || $this->_shortTimeOff['Admin']=="admin")
							{
								$adminRole = "admin";
							}
							else
							{
								$adminRole = '';
							}
							

							$otherDetailsForUpdate = array(
								'sessionId' => $this->_logEmpId,
								'formName' => $this->_formNameA,
								'customFormName' => $customFormNamee,
								'adminRole' => $adminRole,
								'shortTimeOffSettings' => $shortTimeOffSettingsResponse,
								'shiftDetails' => $shiftDetails);
							$result = $this->_dbShortTimeOff->updateShortTimeOff($shortTimeOffArr, $comments['value'],$otherDetailsForUpdate);
							
							if ($result['success'] && isset($formData['forwardTo']) && !empty($formData['forwardTo']))
							{
								$employeeName        = $this->_dbPersonal->employeeName ($employeeId['value']);
								$sessionEmployeeName = $this->_dbPersonal->employeeName ($this->_logEmpId);

								$orgDateFormat = $this->_ehrTables->orgDateformat();
								$startDateTime = date($orgDateFormat['php'],strtotime($formData['startDateTime']));
								$endDateTime = date($orgDateFormat['php'],strtotime($formData['endDateTime']));

								if(!$result['Process_Instance_Id']){
								if ($shortTimeOffId == 0)
								{
									if ($employeeId['value'] == $this->_logEmpId)
									{
										$msgDescA = "<p>".$customFormNamee." Notification forwarded from ". $employeeName['Employee_Name'] ." is waiting for your approval.</p>";
									}
									else
									{
										$msgDescA = "<p>".$customFormNamee." Notification forwarded from ". $sessionEmployeeName['Employee_Name'] ."for the employee".
													$employeeName['Employee_Name'] ." is waiting for your approval.</p>";
									}
									
									$result = $this->_dbCommonFun->communicateMail (array('employeeId'     => $forwardTo['value'],
																							'ModuleName'     => 'Employees',
																							'formName'       => $this->_formNameA,
																							'successMsg'     => $customFormNamee,
																							'customFormName' => $customFormNamee,
																							'formUrl'        => '/v3/employee-self-service/short-time-off',
																							'inboxTitle'     => $customFormNamee.' Notification',
																							'inboxCondition' => ($forwardTo['value'] != $this->_logEmpId && $forwardTo['value'] != $employeeId['value']),
																							'mailContent'    => $msgDescA,
																							'action'         => 'added'));
									
									if (!empty($alternatePerson['value']) && $alternatePerson['value'] != $employeeId['value'] && $alternatePerson['value'] != $this->_logEmpId)
									{
										$msgDescX = "<p>". $employeeName['Employee_Name'] ."  who is on ". $requestFor['value'] ." has assigned you as backup from ". $startDateTime ." to ". $endDateTime .".</p>";
										
										$result = $this->_dbCommonFun->communicateMail (array('employeeId'     => $alternatePerson['value'],
																								'ModuleName'     => 'Employees',
																								'formName'       => $this->_formNameA,
																								'successMsg'     => $customFormNamee,
																								'customFormName' => $customFormNamee,
																								'formUrl'        => '/v3/employee-self-service/short-time-off',
																								'inboxTitle'     => $customFormNamee.' Notification from '. $employeeName['Employee_Name'],
																								'mailContent'    => $msgDescX,
																								'action'         => 'added'));
									}
								}
								else
								{
									if ($forwardTo['value'] != $shortTimeOffRec['Approver_Id'] || $shortTimeOffRec['Approval_Status'] == "Approved")
									{
										if ($shortTimeOffRec['Approval_Status'] != "Approved")
										{
											if ($employeeId['value'] == $this->_logEmpId)
											{
												$msgDescA = "<p>".$customFormNamee." Notification forwarded from ". $employeeName['Employee_Name'] ." is waiting for your approval.</p>";
											}
											else
											{
												$msgDescA = "<p>".$customFormNamee." Notification forwarded from ". $sessionEmployeeName['Employee_Name'] ."for the employee".
															$employeeName['Employee_Name'] ." is waiting for your approval.</p>";
											}
										}
										else
										{
											if ($employeeId['value'] == $this->_logEmpId)
											{
												$msgDescA = "<p>".$customFormNamee." Notification forwarded from ". $employeeName['Employee_Name'] ." is modified and waiting for your approval.</p>";
											}
											else
											{
												$msgDescA = "<p>".$customFormNamee." Notification forwarded from ". $sessionEmployeeName ."for the employee".
															$employeeName['Employee_Name'] ." is modified and waiting for your approval.</p>";
											}
										}
											
										$result = $this->_dbCommonFun->communicateMail (array('employeeId'     => $forwardTo['value'],
																								'ModuleName'     => 'Employees',
																								'formName'       => $this->_formNameA,
																								'successMsg'     => $customFormNamee,
																								'customFormName' => $customFormNamee,
																								'formUrl'        => '/v3/employee-self-service/short-time-off',
																								'inboxTitle'     => $customFormNamee.' Notification',
																								'inboxCondition' => ($forwardTo['value'] != $this->_logEmpId && $forwardTo['value'] != $employeeId['value']),
																								'mailContent'    => $msgDescA,
																								'action'         => 'updated'));
											
									}
										
									if ($alternatePerson['value'] != $employeeId && $alternatePerson['value'] != $this->_logEmpId)
									{
										$msgDescX = "<p>". $employeeName['Employee_Name'] ."  who is on ". $requestFor['value'] ." has assigned you as backup from ". $startDateTime ." to ". $endDateTime .".</p>";
										
										$result = $this->_dbCommonFun->communicateMail (array('employeeId'     => $alternatePerson['value'],
																								'ModuleName'     => 'Employees',
																								'formName'       => $this->_formNameA,
																								'successMsg'     => $customFormNamee,
																								'customFormName' => $customFormNamee,
																								'formUrl'        => '/v3/employee-self-service/short-time-off',
																								'inboxTitle'     => $customFormNamee.' Notification from '. $employeeName['Employee_Name'],
																								'mailContent'    => $msgDescX,
																								'action'         => 'updated'));
									}
								}
								}
							}
							
							$this->view->result = $result;
						}
					}
					else
					{
						$this->view->result = array('success'=>false, 'msg'=>'Invalid Data', 'type'=>'warning');	
					}
				}
			}
            else
            {
				$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'info');
            }
		}
        else
        {
            $this->_helper->redirector('index', 'short-time-off', 'employees');
        }	
    }
	
	public function deleteShortTimeOffAction()
    {
        $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-short-time-off', 'json')->initContext();
            
			if( $this->_shortTimeOff['Employee']['Delete'] == 1 || $this->_myTeamAccess['Delete'] == 1 || $this->_selfServiceAccess['Delete'] == 1)
			{
				$attendanceSummaryDetails = [];
				
				$shortTimeOffIds = $this->_getParam('shortTimeOffIdArr', null);
				$shortTimeOffIds = filter_var($shortTimeOffIds, FILTER_SANITIZE_NUMBER_INT, FILTER_REQUIRE_ARRAY);
				
				$customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
				$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
				
				$totalSuccessDelete = 0;
				
				for ($i = 0; $i < count($shortTimeOffIds); $i++)
				{
					$result = $this->_dbShortTimeOff->deleteShortTimeOff($shortTimeOffIds[$i], $this->_logEmpId, $this->_formNameA, $formName);
					
					if($result['success']){
						$totalSuccessDelete++;		
						$attendanceSummaryDetails[] = $result['shortTimeOffDetails'];
					}
				}

				$this->_dbCommonFun->triggerAttendanceSummaryStepFunction($attendanceSummaryDetails,'shorttimeoff');
					
				if($totalSuccessDelete > 0)
				{
					if(count($shortTimeOffIds) == $totalSuccessDelete)
						$this->view->result = array('success'=>true, 'msg'=>$formName.' deleted successfully', 'type'=>'info');
					else
						$this->view->result = array('success'=>true, 'msg'=>$formName.' record partially deleted successfully', 'type'=>'info');
				}
				else
				{
					$this->view->result = array('success'=>false, 'msg'=>'Unable to delete '.$formName.' record(s)', 'type'=>'info');
				}
			}
			else
			{
				$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'warning');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'short-time-off', 'employees');
        }
    }
	
	public function statusUpdateShortTimeOffAction()
    {
		$this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('status-update-short-time-off', 'json')->initContext();
				
			$shortTimeOffIds = $this->_getParam('shortTimeOffIdArr', null);
			$shortTimeOffIds = filter_var($shortTimeOffIds, FILTER_SANITIZE_NUMBER_INT, FILTER_REQUIRE_ARRAY);
			
			if (count($shortTimeOffIds) > 0 && ($this->_shortTimeOff['Employee']['Is_Manager'] == 1 || $this->_shortTimeOff['Admin'] == "admin" || $this->_myTeamAccessRights['Employee']['Is_Manager'] == 1 || $this->_myTeamAccessRights['Admin']== "admin"))
			{
				if ($this->getRequest()->isPost())
				{
					$formData = $this->getRequest()->getPost();
					$attendanceSummaryDetails = [];
					$isValid = 1;
					
					$status = $this->_validation->alphaValidation($formData['status']);
					
					$actionType = $this->_validation->alphaValidation($formData['isAction']); // MultiStatusUpdate/StatusUpdate
					
					$comments 		   = $this->_validation->alphaNumSpCDotHySlashNewLineValidation($formData['comments']);
					$comments['valid'] = $this->_validation->lengthValidation($comments, 5, 3000, false);
					
					if( $actionType == 'StatusUpdate')
					{
						$isValid = ((($status == 'Rejected' || $status == 'Returned' || $status == 'Cancel Applied')&& !empty($comments['value'])) ||
									($status == 'Approved' || $status == 'Cancelled'));
					}
					
					if ( !empty($status) && $comments['valid'] && $isValid)
					{
						$updatedCount = 0;
						
						$empshortTimeOffAddedByArray = array();
						$empshortTimeOffEmpIdArray = array();
												
						$customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
						$customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);

						$allShortTimeOffDetails = $this->_dbShortTimeOff->getShortTimeOffDetails($shortTimeOffIds);
						$dbHRReport = new Reports_Model_DbTable_HrReports();
						$organizeShortTimeOffDetails = $dbHRReport->organizeDataByEmployeeIdAndDate($allShortTimeOffDetails,'Short_Time_Off_Id');

						for ($i = 0; $i < count($shortTimeOffIds); $i++)
						{
							$accessStatus = $this->_dbComment->payrollStatus($shortTimeOffIds[$i], $this->_formNameA);
							$shortTimeOffEmp = $this->_dbShortTimeOff->shortTimeOffEmployee($shortTimeOffIds[$i]); //get employee name and employeeId
							
							if( $actionType == 'MultiStatusUpdate')
							{
								$isValid = ($accessStatus['Status'] == 'Applied' && $accessStatus['Approver_Id'] == $this->_logEmpId);
							}
								
							if ( $isValid)
							{
								$commentArray = array( 'Approval_Status' => $status['value'],
													  'Emp_Comment'      => htmlentities($comments['value']),
													  'Parent_Id'        => $shortTimeOffIds[$i],
													  'Employee_Id'      => $this->_logEmpId);
								
								// update status report
								$statusReport = $this->_dbShortTimeOff->statusUpdateShortTimeOff($commentArray, $this->_formNameA, $customFormNamee);
								
								if ($statusReport['success'])
								{
									$attendanceSummaryTriggerStatus = array('Rejected','Cancelled');
									if(in_array($status['value'],$attendanceSummaryTriggerStatus)){
										$shortTimeArray = $dbHRReport->ensureArray($organizeShortTimeOffDetails, $shortTimeOffIds[$i]);
										if($shortTimeArray && isset($shortTimeArray[0]) && $shortTimeArray[0]['Request_For'] == 'Permission'){
											$attendanceSummaryDetails[] = $shortTimeArray[0];
										}
									}

									$updatedCount++;
								
									if(!$statusReport['Enable_Workflow']){
									if ($this->_logEmpId != $accessStatus['Added_By'])
									{
										if (!(in_array($accessStatus['Added_By'], $empshortTimeOffAddedByArray)))
										{
											array_push($empshortTimeOffAddedByArray, $accessStatus['Added_By']);
											
											if ($accessStatus['Added_By'] == $shortTimeOffEmp['Employee_Id'])
												$msgDescA = "<p>Your ".$customFormNamee." request has been ".strtolower($status['value'])."</p>";
											else
												$msgDescA = "<p>Your ".$customFormNamee." request for the employee ". $shortTimeOffEmp['Employee_Name'] ." has been ".strtolower($status['value'])."</p>";
											
											$this->_dbCommonFun->communicateMail (array('employeeId'  => $accessStatus['Added_By'],
																							 'ModuleName'  => 'Employees',
																							 'formName'    => $this->_formNameA,
																							 'successMsg'  => $customFormNamee,
																							 'customFormName' => $customFormNamee,
																							 'formUrl'     => '/v3/employee-self-service/short-time-off',
																							 'inboxTitle'  => $customFormNamee.' Notification',
																							 'mailContent' => $msgDescA,
																							 'action'      => 'updated'));
										}
									}
									
									if ($this->_logEmpId != $shortTimeOffEmp['Employee_Id'] &&
										$shortTimeOffEmp['Employee_Id'] != $accessStatus['Added_By'])
									{
										if (!(in_array($shortTimeOffEmp['Employee_Id'], $empshortTimeOffEmpIdArray)))
										{
											array_push($empshortTimeOffEmpIdArray, $shortTimeOffEmp['Employee_Id']);
											
											$msgDescX = "<p>Your ".$customFormNamee." request has been ".strtolower($status['value'])."</p>";
											
											$this->_dbCommonFun->communicateMail (array('employeeId'  => $shortTimeOffEmp['Employee_Id'],
																							 'ModuleName'  => 'Employees',
																							 'formName'    => $this->_formNameA,
																							 'successMsg'  => $customFormNamee,
																							 'customFormName' => $customFormNamee,
																							 'formUrl'     => '/v3/employee-self-service/short-time-off',
																							 'inboxTitle'  => $customFormNamee.' Notification',
																							 'mailContent' => $msgDescX,
																							 'action'      => 'updated'));
										}
									}
									}
								}
							}
						}
						
						$this->_dbCommonFun->triggerAttendanceSummaryStepFunction($attendanceSummaryDetails,'shorttimeoff');
						if( $updatedCount > 0)
						{
							$this->view->result = array('success' => true, 'msg'=>$customFormNamee.' Status Updated successfully', 'type'=>'success');
						}
						else
						{
							$this->view->result = array('success' => false, 'msg'=>'Unable to update '.$customFormNamee.' Status', 'type'=>'info');
						}
					}
					else
					{
						$this->view->result = array('success' => false, 'msg'=>"Invalid data", 'type'=>'info');
					}
				}
			}
			else
            {
				$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'info');
            }
		}
		else
		{
			$this->_helper->redirector('index', 'short-time-off', 'employees');
		}	
    }
	
	//Update permission settings details
    public function getShortTimeOffBalanceAction()
    {
        $this->_helper->layout()->disableLayout();
        if(isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('get-short-time-off-balance', 'json')->initContext();
			
			$shortTimeOffId = $this->_getParam('shortTimeOffId', null);
			$shortTimeOffId = filter_var($shortTimeOffId, FILTER_SANITIZE_NUMBER_INT);

			$employeeId 	= $this->_getParam('employeeId', null);
			$employeeId 	= filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);

			$startDateTime 	= $this->_getParam('startDateTime', null);
			$totalHours    	= $this->_getParam('totalHours', null);

			$shortTimeOffArr = array('Short_Time_Off_Id' => $shortTimeOffId,
									'Employee_Id'        => $employeeId,
									'Start_Date_Time'    => $startDateTime,
									'Total_Hours'        => $totalHours);
						
			$shortTimeOffSettingsResponse = $this->_dbShortTimeOff->listShortLeaveRequestSettings($employeeId);
			
			if(!empty($shortTimeOffSettingsResponse['Settings_Details']))
			{
				$shortTimeOffSettingsDetails = $shortTimeOffSettingsResponse['Settings_Details'];
				$shortTimeOffDetails  = $this->_dbShortTimeOff->getShortTimeOffEmpDetails($shortTimeOffArr,$shortTimeOffSettingsDetails['Period'],'Yes');
				
				if(!empty($shortTimeOffDetails))
				{
					if($shortTimeOffSettingsDetails['Limit_By']==='Request')
					{
						if($shortTimeOffSettingsDetails['Maximum_Limit'] >= $shortTimeOffDetails['totalCount'])
						{
							$shortTimeOffBalanceRequest = $shortTimeOffSettingsDetails['Maximum_Limit']-$shortTimeOffDetails['totalCount'];
						}
						else
						{
							$shortTimeOffBalanceRequest = 0;
						}
						$shortTimeOffBalanceRequest = $shortTimeOffBalanceRequest.' Request(s) (Including current request)';

						$shortTimeOffRules = $shortTimeOffSettingsDetails['Maximum_Limit'].' Request(s) '.$shortTimeOffSettingsDetails['Period'];

						$this->view->result = array('success' => true,'shortTimeOffBalance'=>$shortTimeOffBalanceRequest,'shortTimeOffRules'=>$shortTimeOffRules,'type'=>'success');
					}
					else
					{
						$maxDurationInMinutes  = $shortTimeOffSettingsDetails['Total_Duration'];
						$totalTimeInMinutes    = $shortTimeOffDetails['totalTimeInSecs']/60;
						
						if($maxDurationInMinutes >= $totalTimeInMinutes)
						{
							$shortTimeOffBalanceMinutes = $maxDurationInMinutes-$totalTimeInMinutes;
						}
						else
						{
							$shortTimeOffBalanceMinutes = 0;
						}
						$shortTimeOffBalanceMinutes = $shortTimeOffBalanceMinutes.' Minute(s) (Including current request)';
						$shortTimeOffRules = $shortTimeOffSettingsDetails['Total_Duration'].' Minute(s) '.$shortTimeOffSettingsDetails['Period'];
						$this->view->result = array('success' => true, 'shortTimeOffBalance'=>$shortTimeOffBalanceMinutes,'shortTimeOffRules'=>$shortTimeOffRules,'type'=>'success');
					}
				}
				else
				{
					$this->view->result = array('success' => false, 'msg'=>"Invalid data", 'type'=>'info');
				}
			}
			else
			{
				if($shortTimeOffSettingsResponse['Coverage'] && strtolower($shortTimeOffSettingsResponse['Coverage']) === 'custom group'){
					$this->view->result = array('success' => false, 'msg'=>"The employee isn't part of the short time off custom group.As a result, they are not allowed to apply for short time off.", 'type'=>'warning');
				}else{
					$this->view->result = array('success' => false, 'msg'=>"Invalid data", 'type'=>'info');
				}
			}
	    }
        else
        {
            $this->_helper->redirector('index', 'short-time-off', 'employees');
        }
    }

	public function __destruct()
    {
        
    }

}

