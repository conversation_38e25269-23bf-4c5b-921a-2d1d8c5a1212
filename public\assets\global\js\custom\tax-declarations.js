/*=========================================================================================*/
/*=========================================================================================*/
/* Program        : taxdeclarations.js													*/
/* Property of Caprice Technologies Pvt Ltd,
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,
 * Coimbatore, Tamilnadu, India.															*/
/* All Rights Reserved.            														*/
/* Use of this material without the express consent of Caprice Technologies
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law.*/
/*                                                                                    	*/
/* Description    :jquery for tax declarations											*/
/*                                                                                   	*/
/*                                                                                    	*/
/*Revisions      :                                                                    	*/
/*Version    Date           Author                  Description                       	*/
/*0.1        22-Apr-2016    Suganya                 Initial Version                 	*/
/*                                                                                    	*/
/*                                                                                    	*/
/*=========================================================================================*/
/*=========================================================================================*/
$(function () {    
    var isDirtyFormTaxDelcaration = false;   
    
    /* Alert when the DataSetup is not completed */
    dataSetup('datasetup');
    
    $.fn.dataTable.ext.errMode = 'none';
    
    var domainName = fnIsDomain (),
        orgCode       = fngetOrgCode(),
        domainName    = domainName.split('.')[0];

    prerequisiteData = getPayrollPrerequisiteData();   
    
    
    /** Create Tax Declaration Grid**/ 
    var tableTaxDeclaration = $('#tableTaxDeclaration').dataTable({
        "lengthMenu"     : [ 5, 10, 25, 50, 100 ], 
        "iDisplayLength" : 10,
        "bDestroy"       : true,
        "bAutoWidth"     : false,
        "bServerSide"    : true,
        "bDeferRender"   : true,
        "sServerMethod"  : "POST", 
        "sAjaxSource": pageUrl() + "payroll/tax-declarations/show-tax-declarations/prerequisiteData/" +prerequisiteData,
        "sAjaxDataProp"  : "aaData",
        "aaSorting"      : [],
        "aoColumnDefs"   : [{"targets": 0, "orderable": false},
                            { "sClass" : "visible-xs visible-sm  hidden-md hidden-lg", "aTargets" : [0] },
                            { "sClass" : "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets" : [1] },
                            { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [4] },
                            { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [5] }],
        "fnRowCallback"  : function ( nRow, aData, iDisplayIndex ) {
            if (aData['Employee_Id'] != aData['Added_By'] && aData['Employee_Id'] == aData['Log_Id']) {
                $(nRow).addClass('addedBy');
            }
        },
        "fnCreatedRow": function( nRow, aData, iDataIndex ) {
            $(nRow).attr({"data-toggle":"context", "data-target":"#tax-declaration-context-menu" });
        },
        "aoColumns"      :[
                           {
            "mData" : function (row, type, set) {
                return '<i class="fa fa-plus-square-o"></i>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                return '<div >'+ row['User_Defined_EmpId'] +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                return '<div>'+ row['Employee_Name'] +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                return '<div class="text-center" >'+ row['Assessment_Year'] +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                return '<div class="text-center" >'+ row['Total_Declared_Amount'] +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                return '<div >'+ fnCheckNull(row['Total_Approved_Amount']) +'</div>';
            }
        }]
    });
    
    
    //On + icon click in mobile & tablet view
    $(document).on('click', '#tableTaxDeclaration i', function () {
        var nTr = $(this).parents('tr')[0];
        
        fnFilterClose ('TaxDeclaration');
        
        if ( tableTaxDeclaration.fnIsOpen(nTr) )
        {
            /* This row is already open - close it */
            $(this).removeClass().addClass('fa fa-plus-square-o');
            tableTaxDeclaration.fnClose(nTr);
        }
        else
        {
            var record = tableTaxDeclaration.fnGetData( nTr );
            var nRow =  $('#tableTaxDeclaration thead tr')[0];
            
            /* Open this row */
            $(this).removeClass().addClass('fa fa-minus-square-o');
            
            valueArray = [];
            headerArray=[];
            
            valueArray.Value_One = record.Total_Declared_Amount;
            valueArray.Value_Two = record.Total_Approved_Amount;
            
            //get grid headers
            gridHeader(nRow.cells);
            
            tableTaxDeclaration.fnOpen(nTr, fnDeviceColumnDetails(headerArray,valueArray), 'details hidden-lg');
        }
    });
    
    /*  Add event listener for select and unselect details  */
    $(document).on('click contextmenu', '#tableTaxDeclaration tbody td div', function () {
        fnFilterClose ('TaxDeclaration');
        
        var selectRow = $(this).parent().parent();
        
        tableTaxDeclaration.$('tr.row_selected').removeClass('row_selected');
        
        if (!selectRow.hasClass('row_selected'))
        {
            selectRow.addClass('row_selected');
            
            fnActionButtons (true);
        }
        else
        {
            fnActionButtons (false);
        }
    });
    
    //** Refresh The Tax Declaration Grid**/ 
    $('#gridPanelTaxDeclaration .panel-reload').on('click', function () {
        fnRefreshTable (tableTaxDeclaration);
    });
    
    /**
     *  close filter form and clear enabled buttons based on selection record
     *  while search all, sorting, pagination, redraw events
     *  it will work in search.dt order.dt page.dt, length.dt those events
    */
    $('#tableTaxDeclaration').on( 'draw.dt', function () {
        fnActionButtons (false);

        fnFilterClose ('TaxDeclaration');
    });
   
    /** View Tax Declaration Form **/
    $('#viewTaxDeclaration,#viewContextTaxDeclaration').on('click', function () {
        var selectedRow = fnGetSelected (tableTaxDeclaration);
        
        fnFilterClose ('TaxDeclaration');
       
        if (selectedRow.length)
        {
            var record = tableTaxDeclaration.fnGetData(selectedRow[0]);
            
            if (record.Declaration_Id > 0)
            {            
                $('#modalTaxDeclaration .modal-title').html('<strong>View <strong> '+$('#lblFormNameA').html());
                
                $('#viewEmployeeName').text(record.Employee_Name);
                $('#viewAssessmentYear').text(record.Assessment_Year);
                $('#viewTaxForwardedTo').text(fnCheckNull(record.Forward_Name));
                
                //$('#DeclarationId').val(record.Declaration_Id);
                
                tableTaxDeclarationSubGridView.fnReloadAjax( pageUrl () + "payroll/tax-declarations/tax-declarations-subgrid/declarationId/"+record.Declaration_Id);
               
                /** Check Edit Permission **/
                if(((record['Log_Id']=== record['Added_By'] && record['isEmpEdit'] > 0 && record['Update'] == 1) ||			
                (record['Log_Id']=== record['Employee_Id'] && record['isEmpEdit'] > 0 && record['Update'] == 1) ||
                (record['Log_Id']=== record['Approver_Id'] && record['isApprEdit'] > 0 && record['Update'] == 1) ||
                $('#IsEmpAdmin').val() === 'admin') && $('#assessmentBasedEdit').val() == record.Assessment_Year)
                {
                    $('#editInViewTaxDeclaration').show();
                }
                else{
                    $('#editInViewTaxDeclaration').hide();
                }
                       
                $('#modalTaxDeclaration').modal('toggle');
                //$('#editFormTaxDeclaration,#addDeclarationDetails,#taxDeclarationSubGrid,#formSubmitDeclarationMail, .modal-footer').hide();
                $('#editFormTaxDeclaration,#addDeclarationDetails,#taxDeclarationSubGrid,#formActionTaxDeclaration,#revertTaxDeclaration').hide();
                $('#formProofSubmitDeclarationMail,#formCloseSubmit,#formStatusSubmitDeclarationMail').hide();
                $('#viewFormTaxDeclaration').show();
                
                $('#tableTaxDeclarationSubGridView').parent().removeClass('force-table-responsive');
                $('#tableTaxDeclarationSubGridView thead tr th').removeClass('sorting_asc');
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+$('#lblFormNameA').html()+' record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameA').html()+' record', type : 'info' });
        }
    });
    
    /** Tax Declaration SubGrid In View Modal
      *  Initialse DataTables, with no sorting on the 'details' column
    **/
    tableTaxDeclarationSubGridView = $('#tableTaxDeclarationSubGridView').dataTable({
        "iDisplayLength" : 10,
        "lengthMenu"     : [ 5, 10, 25, 50, 100 ], 
        "bDestroy"       : true,
        "bAutoWidth"     : false,
        "bServerSide"    : true,
        "bDeferRender"   : true,
        "sServerMethod"  : 'POST',
        "sAjaxSource"    : pageUrl () + 'payroll/tax-declarations/tax-declarations-subgrid/',
        "sAjaxDataProp"  : 'aaData',
        "info"           : false,
        "bPaginate"      : false, // Remove pagination
        "bFilter"        : false,
        "aaSorting"      : [],
        "aoColumnDefs"   : [{ 'bSortable': false, 'aTargets': ['_all'] },
                            { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [3] },
                            { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [4] }],
        "aoColumns"      : [{
            "mData" : function (row, type, set) {
                return '<i class="fa fa-plus-square-o"></i>';
            }
        },
        {
            "mData" : function (row, type, set) {
                return '<div >'+ row['Investment_Category'] +'</div>';
            }
        },
        {
            "mData" : function (row, type, set) {
                return '<div class="text-center" >'+ row['Amount_Declared'] +'</div>';
            }
        },
        {
            "mData" : function (row, type, set) {
                return '<div class="text-center" >'+ row['Status'] +'</div>';
            }
        },
        {
            "mData" : function (row, type, set) {
                return '<div class="text-center" >'+ fnCheckNull(row['Amount_Approved']) +'</div>';
            }
        }
       ]
    });
    
    // Tax Declaration view - On + button click - get more information by form expand
    $(document).on('click', '#tableTaxDeclarationSubGridView i', function () {
        var nTr = $(this).parents('tr')[0];
        
        if ( tableTaxDeclarationSubGridView.fnIsOpen(nTr) )
        {
            /* This row is already open - close it */
            $(this).removeClass().addClass('fa fa-plus-square-o');
            
            tableTaxDeclarationSubGridView.fnClose(nTr);
        }
        else
        {
            /* Open this row */
            $(this).removeClass().addClass('fa fa-minus-square-o');
            
            tableTaxDeclarationSubGridView.fnOpen(nTr, fnShowHiddenTaxDeclarationDetails(nTr), 'details');
        }
    });
    
    /**
     * Department wise segregation
    */ 
    $('#formDivisionDetails').on('change', function () {
        var formDivisionId = $('#s2id_formDivisionDetails').select2('val');
        hrappDepartmentClassification(formDivisionId, tableTaxDeclaration, tableHraDeclaration);
    });

    $('#formInvestmentCategory').on('change', function () {
        var formInvestmentCategoryId = $('#s2id_formInvestmentCategory').select2('val');
        if (formInvestmentCategoryId > 0)
        {
            $.ajax({
                type: 'POST',
                url: pageUrl() + 'payroll/tax-declarations/investment-age-group/formInvestmentCategoryId/' + formInvestmentCategoryId,
                async: false,
                dataType: "json",
                success: function (result) {
                    $('#s2id_formInvestmentAgeGroup').select2('val', '');
                    $('#formInvestmentAgeGroup').find('option').remove();
                    if (result != '' && result != null) {
                        $('.formInvestmentAgeGroupPanel').show();
                        $('#formInvestmentAgeGroup').addClass('vRequired');
                        $('#formInvestmentAgeGroup').append("<option value=''>-- Select --</option>");
                        $.each(result, function (key, row) {
                            $('#formInvestmentAgeGroup').append('<option value=' + key + '>' + row + '</option>');
                        });
                    }
                    else
                    {
                        $('#formInvestmentAgeGroup').removeClass('vRequired');
                        $('.formInvestmentAgeGroupPanel').hide();
                    }
                },
                error: function (ageGroupComboErrorResult) {
                    if (ageGroupComboErrorResult.status === 200) {
                        sessionExpired();
                    } else {
                        /* To handle internal server error */
                        jAlert({ panel: $('#editFormTaxDeclaration'), msg: "Something went wrong. Please contact system admin.", type: "warning" });
                        fnModalScrollTop('modalTaxDeclaration');
                    }
                }
            });
        }
        else
        {
            jAlert({ panel: $('#editFormTaxDeclaration'), msg: "Something went wrong. Please contact system admin.", type: "warning" });
            fnModalScrollTop('modalTaxDeclaration');
        }
    });

    function listSectionInvestmentCategory(employeeId)
    {
        if (employeeId > 0) {
            $.ajax({
                type: 'POST',
                url: pageUrl() + 'payroll/tax-declarations/list-section-investment-category/employeeId/' + employeeId +'/sectionName/yes',
                async: false,
                dataType: "json",
                success: function (result) {
                    $('#s2id_formInvestmentCategory').select2('val', '');
                    $('#formInvestmentCategory').find('option').remove();
                    if (result != '' && result != null) {
                        
                        $('#formInvestmentCategory').append("<option value=''>-- Select --</option>");
                        $.each(result, function (key, row) {
                            $('#formInvestmentCategory').append('<option value=' + key + '>' + row + '</option>');
                        });
                    }
                },
                error: function (sectionInvestmentCategoryComboErrorResult) {
                    if (sectionInvestmentCategoryComboErrorResult.status === 200) {
                        sessionExpired();
                    } else {
                        /* To handle internal server error */
                        jAlert({ panel: $('#editFormTaxDeclaration'), msg: "Something went wrong. Please contact system admin.", type: "warning" });
                        fnModalScrollTop('modalTaxDeclaration');
                    }
                }
            });
        }
        else {
            jAlert({ panel: $('#editFormTaxDeclaration'), msg: "Something went wrong. Please contact system admin.", type: "warning" });
            fnModalScrollTop('modalTaxDeclaration');
        }
    }

    /** Add Tax Declaration **/
    $('#addTaxDeclaration').on('click', function (e) {
        tableTaxDeclaration.$('tr.row_selected').removeClass('row_selected');
        fnActionButtons (false);
        fnFilterClose ('TaxDeclaration');
        
        $('#s2id_formEmployeeName, #s2id_formInvestmentCategory,#s2id_formDeclarationStatus,#s2id_formAssessmentYear').removeClass('form-error');
        
        $('#editFormTaxDeclaration').validate().resetForm();
        
        $('#modalTaxDeclaration .modal-title').html('<strong>Add</strong> '+$('#lblFormNameA').html());
        
        if ($("#assessmentDetailsPanel").hasClass('collapsed'))
        {
            $( "#assessmentDetailsPanel" ).trigger( "click" );
        }
    
        $('#onAddCollapse').addClass('collapsed');
        
        /** Reset the values **/
        $('#declarationId').val(0);
        $('#lineItemId').val(0);
        
        var employeeField = $('#formEmployeeName');
        
        $.ajax ({
            type     : 'POST',
            dataType : 'json',
            async    : false,
            url      : pageUrl () +'default/employee-info/listsalary-employee',
            data     : {
                _fId   : 'Tax Declarations',
            },
            success  : function (result)
            {
                if (isJson (result))
                {
                    if (result.length > 0)
                    {
                        employeeArray = '<option value="">--Select--</option>';
                        
                        employeeField.find('option').remove();
                        
                        var empDepartment = [];
	
                        for (var row in result) {
                           if($.inArray(result[row]['Department_Name'],empDepartment) == -1)
                            {
                                empDepartment.push(result[row]['Department_Name']);
                            }
                        }
                        
                        for(var y in empDepartment)
                        {
                            employeeArray += '<optgroup label="'+empDepartment[y]+'">';
                            
                            for(var x in result)
                            {
                                if(result[x]['Department_Name'] == empDepartment[y])
                                {
                                    employeeArray += '<option value='+result[x]['Employee_Id']+'>'+result[x]['Employee_Name']+'</option>'
                                }
                            }
                            employeeArray += '</optgroup>';
                        }
                    
                        employeeField.append(employeeArray);
                        
                        employeeField.select2('val', '');
                    }
                    else
                    {
                        jAlert ({ msg : 'No employees found', type : 'info' });
                    }
                }
                else
                {
                    sessionExpired ();
                }
            }
        });
        
        employeeField.prop('readOnly', false);
        employeeField.select2('val', $('#logemployeeId').val());       
        
        $("#s2id_formAssessmentYear").select2('val', $('#assessmentBasedEdit').val());
        
        /** To prefill the log employee and subgrid **/
        $('#formEmployeeName').trigger('change');
        $('.formInvestmentAgeGroupPanel').hide();
        $('#formInvestmentAgeGroup').removeClass('vRequired');
        $('#s2id_formInvestmentCategory,#formDeclarationStatus,#formInvestmentAgeGroup').select2('val', '');
        $('#formInvestmentCategory').prop('readOnly', false);
        $('#formAmountDeclared').prop('readOnly', false);       
        $("#formAmountDeclared,#formAmountApproved,#formComment").val('');        
        $('.ApprovalBasedHidden').hide();        
        
        $('#formAmountApproved').removeClass('vRequired');
        $('#formAmountApproved').valid();
                
        fnsetApprovalStatusField('',$("#formDeclarationStatus"));        
        
        //$("#s2id_formDeclarationStatus").select2('val', 'Applied');
        $("#s2id_formDeclarationStatus").select2('val', 'Declared');
        
        $('#s2id_formEmployeeName, #s2id_formInvestmentCategory,#s2id_formDeclarationStatus,#s2id_formAssessmentYear').removeClass('form-error');
        $('#formEmployeeName, #formInvestmentCategory,#formDeclarationStatus,#formAssessmentYear').removeClass('form-error');        
        
        $('#viewFormTaxDeclaration, #editInViewTaxDeclaration,#revertTaxDeclaration').hide();
        $('#formStatusSubmit,#formStatusSubmitDeclarationMail,#formResetStatus').hide();
        
        aftDelNewTaxFiles =[], bucketUploadTaxSuccessFiles = [], bucketUploadTaxFailureFiles = [], newTaxFiles = [], oldTaxFiles = [];
        
        $('#taxDeclarationFileSize').val(0);
                
        $('#formActionTaxDeclaration, #editFormTaxDeclaration,#addDeclarationDetails,#taxDeclarationSubGrid,#formResetDeclare,#formDeclareSubmit').show();
        $('#formProofSubmitDeclarationMail,#formCloseSubmit').show();
    });
    
    /** Edit Tax Declaration Form **/    
    $('#editTaxDeclaration, #editInViewTaxDeclaration, #editContextTaxDeclaration').on('click', function () {
        var button      = $(this).prop('id');
        var selectedRow = fnGetSelected (tableTaxDeclaration);
        
        fnFilterClose ('TaxDeclaration');
        
        if (selectedRow.length)
        {
            var record         = tableTaxDeclaration.fnGetData(selectedRow[0]);
            var declarationId  = record.Declaration_Id;
            
            if (declarationId > 0 && !isNaN(declarationId))
            {
                setLock({
                    'formName'  : 'Tax Declarations',
                    'uniqueId'  : declarationId,
                    'callback'  : function(result)
                    {
                        if (button != 'editInViewTaxDeclaration')
                            $('#modalTaxDeclaration').modal('toggle');
                        
                        $('#modalTaxDeclaration .modal-title').html('<strong>Edit</strong> '+$('#lblFormNameA').html());
                        
                        fnsetApprovalStatusField('',$("#formDeclarationStatus"));
                        
                        fnPreFillFormTaxDeclaration('');                        
                        
                        $('#declarationId').val(record.Declaration_Id);
                        
                        $('#TaxAttachmentsList').html("");                        
                        
                        /** update the total allowed file size **/
                        //getUploadedTotalFileSize('Tax Declarations');
                        
                        var employeeField = $('#formEmployeeName');
                        employeeField.find('option').remove();
                        employeeField.append('<option value="'+ record.Employee_Id +'">'+ record.Employee_Name +'</option>');
                        employeeField.select2('val', record.Employee_Id);
                        employeeField.prop('readonly', true);            
                        
                        $("#formAssessmentYear").select2('val', record.Assessment_Year);
                        listSectionInvestmentCategory(record.Employee_Id);
                        aftDelNewTaxFiles =[], bucketUploadTaxSuccessFiles = [], bucketUploadTaxFailureFiles = [], newTaxFiles = [], oldTaxFiles = [];
                        
                        $('#viewFormTaxDeclaration, #editInViewTaxDeclaration,.ApprovalBasedHidden,#revertTaxDeclaration').hide();
                        $('#formStatusSubmit,#formStatusSubmitDeclarationMail,#formResetStatus').hide();
                        
                        $("#formDeclareSubmit").html('<i class="mdi-content-send"></i> Declare');
                        $('#formActionTaxDeclaration, #editFormTaxDeclaration, #addDeclarationDetails,#taxDeclarationSubGrid').show();
                        $('#formResetDeclare,#formDeclareSubmit,#formProofSubmitDeclarationMail,#formCloseSubmit').show();                        
                        
                        /** To load Declaration Sub Grid **/                        
                        fnDeclarationSubGrid(declarationId,0);
                        fnTaxlockConfiguration();
                        getEmployeeTaxRegime($('#s2id_formEmployeeName').select2('val'),'#editFormTaxDeclaration','#modalTaxDeclaration');
                    }
                });
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+$('#lblFormNameA').html()+' record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameA').html()+' record', type : 'info' });
        }
    });
    
    // Add button for Tax Declaration Sub-grid in Add/Edit form.
    $('#addDeclarationDetails').on('click', function(){
    
        if ($("#onAddCollapse").hasClass('collapsed'))
        {
            $( "#onAddCollapse" ).trigger( "click" );
        }
        
        var declarationId = $('#declarationId').val();        
        var employeeId    = $('#formEmployeeName').select2('val');        
             
        if (declarationId > 0 && !isNaN(declarationId))
        {
            clearLock ({
                'formName' : 'Tax Declarations',
                'uniqueId' : declarationId,
                'callback' : function ()
                {
                    //$('#RequestId').val(0);
                    $('#lineItemId').val(0);
                }
            });
        }
        //fnPreFillFormTaxDeclaration('');
        
        //$('#declarationId').val(0);
        $('#lineItemId').val(0);        
        
        $('#s2id_formInvestmentCategory,#formDeclarationStatus').select2('val', '');            
            
        $("#formAmountDeclared,#formAmountApproved").val('');
        $('#formInvestmentCategory').prop('readOnly', false);
        $('#formAmountDeclared').prop('readOnly', false);       
        $('.ApprovalBasedHidden').hide();
        $('#formComment').val('');
        
        
        //$("#s2id_formDeclarationStatus").select2('val', 'Applied');
        $("#s2id_formDeclarationStatus").select2('val', 'Declared');
        
        $('#TaxAttachmentsList').html("");
        
        aftDelNewTaxFiles =[], bucketUploadTaxSuccessFiles = [], bucketUploadTaxFailureFiles = [], newTaxFiles = [], oldTaxFiles = [];
        
        $("#formResetDeclare").trigger('click');
        isDirtyFormTaxDelcaration = false;
    });
   
    ///** Tax Declaration SubGrid In Add/Edit Modal
    //*  Initialse DataTables, with no sorting on the 'details' column
    //**/        
    //var tableTaxDeclarationSubGrid = $('#tableTaxDeclarationSubGrid').dataTable({
    //    "lengthMenu"     : [ 5, 10, 25, 50, 100 ], 
    //    "iDisplayLength" : 10,
    //    "bDestroy"       : true,
    //    "bAutoWidth"     : false,
    //    "bServerSide"    : true,
    //    "bDeferRender"   : true,
    //    "sServerMethod"  : "POST", 
    //    "sAjaxSource"    : pageUrl() + "payroll/tax-declarations/tax-declarations-subgrid/",
    //    "sAjaxDataProp"  : "aaData",
    //    "aoColumnDefs"   : [{"sClass" : "text-center","aTargets": [2, 4]}],
    //    "bSort"          : false,
    //    "info"           : false,
    //    "bPaginate"      : false, // Remove pagination
    //    "bFilter"        : false,
    //    "aoColumns"      :[{
    //        "mData"  : function (row, type, set) {
    //            return '<div>'+ row['Investment_Category'] +'</div>';
    //        }
    //    },
    //    {
    //        "mData"  : function (row, type, set) {
    //            return '<div>'+ row['Amount_Declared'] +'</div>';
    //        }
    //    },
    //    {
    //        "mData"  : function (row, type, set) {
    //            return '<div>'+ row['Status'] +'</div>';
    //        }
    //    },
    //    {
    //        "mData"  : function (row, type, set) {
    //            return '<div>'+ fnCheckNull(row['Amount_Approved']) +'</div>';
    //        }
    //    }]
    //});
    
    /*  Add event listener for select and unselect details for investment sub grid */
    $(document).on('click contextmenu', '#tableTaxDeclarationSubGrid tbody td div', function () {
        fnFilterClose ('TaxDeclaration');
        
        var selectRow = $(this).parent().parent();
        
        tableTaxDeclarationSubGrid.$('tr.row_selected').removeClass('row_selected');
        
        if (!selectRow.hasClass('row_selected'))
        {
            selectRow.addClass('row_selected');
        }
    });
    
    //Edit Investment Details Sub grid.
    $(document).on('click', '.editDeclarationDetailsSubGrid,.statusUpdateDeclarationDetails', function(){
        // var buttonId    = $(this).context['id'];        
        var buttonId = $(this).attr('id');
        var selectedRow = fnGetSelected ( tableTaxDeclarationSubGrid );
       
        if (selectedRow.length)
        {
            var record = tableTaxDeclarationSubGrid.fnGetData (selectedRow[0]);            
            
            $('#TaxAttachmentsList').html("");
            
            if (record.Line_Item_Id > 0 ) 
            {
                clearLockFlag = 0;
            
                $('#declarationId').val(record.Declaration_Id);
                
                if ($('#declarationId').val() > 0)
                {                    
                    clearLock ({
                        'formName' : 'Tax Declarations',
                        'uniqueId' :  $('#declarationId').val(),
                        'callback' : function ()
                        {
                            clearLockFlag = 1;                           
                            
                            $('#declarationId').val(record.Declaration_Id);                            
                            
                            if (buttonId != 'declarationDetails_'+record.Line_Item_Id){                            
                                //Prefill while status update form                                
                                fnPreFillStatusFormDeclaration(record);
                            }
                            else
                            {                                
                                fnPreFillFormTaxDeclaration (record);    
                            }
                        }
                    });
                }
                else
                {                    
                    clearLockFlag = 1;
                }
                
                if (clearLockFlag == 1)
                {                   
                    setLock ({
                        'formName' : 'Tax Declarations',
                        'uniqueId' : record.Declaration_Id,
                        'panel'    : $('#editFormTaxDeclaration'),
                        'callback' : function (result)
                        {
                            
                            if ($("#onAddCollapse").hasClass('collapsed'))
                            {
                                $( "#onAddCollapse" ).trigger( "click" );
                            }
                        }
                    });
                }
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+$('#lblFormNameA').html()+' record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameA').html()+' record', type : 'info' });
        }
    });    
    
    //Alert while Edit Approved Details in investment Sub Grid
    $(document).on('click','.approvedDetailsAlert', function(){
        //if ($("#onAddCollapse").hasClass('collapsed'))
        //{
        //    $( "#onAddCollapse" ).trigger( "click" );
        //}
        
        fnModalScrollTop('modalTaxDeclaration');
        
        var record = tableTaxDeclarationSubGrid.fnGetData (fnGetSelected ( tableTaxDeclarationSubGrid )[0]);
        
        if (record.Status == "Approved")
        {
             jAlert ({ panel : $('#editFormTaxDeclaration'), msg : "You can't update approved status record", type : 'warning' });
        }
        
        if(record.Status == "Rejected")
        {
             jAlert ({ panel : $('#editFormTaxDeclaration'), msg : "You can't update rejected status record", type : 'warning' });
        }
    
        
        var record = tableTaxDeclarationSubGrid.fnGetData (fnGetSelected ( tableTaxDeclarationSubGrid )[0]);
        $('#declarationId').val(record.Declaration_Id);
        $('#lineItemId').val(record.Line_Item_Id);
        
        $("#formInvestmentCategory,#formDeclarationStatus").select2('val','');
        $('#formAmountDeclared,#formAmountApproved').val('');
        $('#TaxAttachmentsList').html("");
        $('.ApprovalBasedHidden').show();
    });
    
    //delete Investment Details Sub grid.
    $(document).on('click', '.deleteDeclarationDetailsSubGrid', function(){
        var record = tableTaxDeclarationSubGrid.fnGetData (fnGetSelected ( tableTaxDeclarationSubGrid )[0]);
        
        if (record)
        {
            var requestId = record.Line_Item_Id;
            
            if (requestId > 0 && !isNaN(requestId))
            {
                $('#modalDeleteTaxDeclareSub').modal('toggle');
            }
            else
            {
                jAlert ({ msg : 'Kindly select a record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select a record', type : 'info' });
        }        
        //deleteConfirmTaxDeclareSub
    });
    
    $(document).on('click', '#deleteConfirmTaxDeclareSub', function(){
        var selectedRow = fnGetSelected ( tableTaxDeclarationSubGrid );
        
        if (selectedRow.length)
        {
            var record = tableTaxDeclarationSubGrid.fnGetData (selectedRow[0]);
            var lineItemId = record.Line_Item_Id;            
            
            deleteFileSuccess  = 0;
            
            if(record.Declaration_Path.length > 0){
 
                for(i=0;i<record.Declaration_Path.length;i++){
                    bucketFileName = '';
                    bucketFileName = record.Declaration_Path[i]['File_Name'];                            
                    
                var res = fndeleteS3Document(domainName+"/"+orgCode+"/"+"Tax Declarations/"+bucketFileName,'bucketName');

                if(res == 'Document deleted') {
                    deleteFileSuccess += 1;
                } else {
                    deleteFileSuccess -= 1;                                    
                }

                }
                
                if (lineItemId > 0 && (deleteFileSuccess == 0 || (deleteFileSuccess == record.Declaration_Path.length)))
                {
                    $('#TaxAttachmentsList').html("");
                    deleteTaxSubRecord(lineItemId,record.Declaration_Id);
                }
                else{
                    jAlert ({ panel : $('#editFormTaxDeclaration'), msg : 'Unable to delete few files', type : 'warning' });
                }
            }
            else{
                deleteTaxSubRecord(lineItemId,record.Declaration_Id);
            }            
        }
    });
    
    $(document).on('click', '#revertTaxDeclaration', function(){
        var selectedRow = fnGetSelected ( tableTaxDeclarationSubGrid);
        
        if (selectedRow.length)
        {
            var record     = tableTaxDeclarationSubGrid.fnGetData (selectedRow[0]);
            var lineItemId = record.Line_Item_Id;
            var employeeId = record.Employee_Id;
            var action     = 'taxDeclaration';
            revertDeclarationDetails(lineItemId,employeeId,action,tableTaxDeclarationSubGrid,'#editFormTaxDeclaration')
        }
    });

    function deleteTaxSubRecord(lineItemId){
        $.ajax({
            type     : 'POST',
            url      : pageUrl()+'payroll/tax-declarations/delete-tax-declaration/lineItemId/'+lineItemId+'/declarationId/'+$('#declarationId').val(),
            async    : false,
            dataType :"json",
            success  : function(result)
            {
                /** update the total allowed file size **/
                //getUploadedTotalFileSize('Tax Declarations');
                if(result && isJson(result)){
                    if (result.success)
                    {                    
                        if(result.declarationExists === "0")
                        {                        
                            $('#declarationId').val(0);
                        }
                        
                        fnPreFillFormTaxDeclaration('');
                        
                        //$('#s2id_formDeclarationStatus').select2('val','Applied');
                        $('#s2id_formDeclarationStatus').select2('val','Declared');
                        
                        fnRefreshTable(tableTaxDeclarationSubGrid);

                        if(result.declarationExists === "0")
                        {
                            $('#modalTaxDeclaration').modal('toggle');
                            
                            fnRefreshTable(tableTaxDeclaration);
                            
                            jAlert({ msg : result.msg, type : result.type });
                        }else{
                            jAlert({ panel : $('#editFormTaxDeclaration'),msg : result.msg, type : result.type });
                            fnModalScrollTop('modalTaxDeclaration');
                        }
                    }else{
                        jAlert({ panel : $('#editFormTaxDeclaration'),msg : result.msg, type : result.type });
                        fnModalScrollTop('modalTaxDeclaration');
                    }
                }else{
                    jAlert({ panel : $('#editFormTaxDeclaration'),msg : "Something went wrong. Please contact system admin.", type : "warning" });
                    fnModalScrollTop('modalTaxDeclaration'); 
                }
            },
            error  : function (taxDecDeleteErrorResult){
                if(taxDecDeleteErrorResult.status === 200){
                    sessionExpired ();
                }else{
                    /* To handle internal server error */
                    jAlert({ panel : $('#editFormTaxDeclaration'),msg : "Something went wrong. Please contact system admin.", type : "warning" });
                    fnModalScrollTop('modalTaxDeclaration');
                }
            }
        });
    }
    //Alert while delete Approved,Applied Details in investment Sub Grid
    $(document).on('click', '.deleteAlertSubGrid', function(){
        //if ($("#onAddCollapse").hasClass('collapsed'))
        //{
        //    $( "#onAddCollapse" ).trigger( "click" );
        //}
        fnModalScrollTop('modalTaxDeclaration');
        
        jAlert ({ panel : $('#editFormTaxDeclaration'), msg : "You can't delete the record", type : 'warning' });
        
        //var record = tableTaxDeclarationSubGrid.fnGetData (fnGetSelected ( tableTaxDeclarationSubGrid )[0]);
        //$('#declarationId').val(record.Declaration_Id);
        //$('#lineItemId').val(record.Line_Item_Id);
        //
        //$("#formInvestmentCategory,#formDeclarationStatus").select2('val','');
        //$('#formAmountDeclared,#formAmountApproved').val('');        
        //$('.ApprovalBasedHidden').show();
    });
    
    
    
    /** Status Update - Tax Declaration Form **/    
    $('#statusApprovalDeclaration,#statusUpdateContextDeclaration').on('click', function () {
        var button      = $(this).prop('id');
        var selectedRow = fnGetSelected (tableTaxDeclaration);
        
        fnFilterClose ('TaxDeclaration');
        
        if (selectedRow.length)
        {
            var record         = tableTaxDeclaration.fnGetData(selectedRow[0]);
            var declarationId  = record.Declaration_Id;
            
            if (declarationId > 0 && !isNaN(declarationId))
            {
                setLock({
                    'formName'  : 'Tax Declarations',
                    'uniqueId'  : declarationId,
                    'callback'  : function(result)
                    {
                        $('#modalTaxDeclaration').modal('toggle');
                        
                        $('#modalTaxDeclaration .modal-title').html("<strong>Update</strong> Status");
                        
                        fnPreFillStatusFormDeclaration('');
                        
                        var employeeField = $('#formEmployeeName');
                        employeeField.find('option').remove();
                        employeeField.append('<option value="'+ record.Employee_Id +'">'+ record.Employee_Name +'</option>');
                        employeeField.select2('val', record.Employee_Id);
                        employeeField.prop('readonly', true);
                        listSectionInvestmentCategory(record.Employee_Id);
                        $('#editFormTaxDeclaration').validate().resetForm();
                        
                        $('#viewFormTaxDeclaration, #editInViewTaxDeclaration,#addDeclarationDetails,#formDeclareSubmit,#revertTaxDeclaration').hide();
                        $('#formProofSubmitDeclarationMail,#formCloseSubmit,#formResetDeclare').hide();
                        
                        $('#editFormTaxDeclaration, #taxDeclarationSubGrid,#formStatusSubmit').show();
                        $('#formStatusSubmitDeclarationMail,#formResetStatus').show();
                        
                        /** To load Declaration Sub Grid **/
                        
                        fnDeclarationSubGrid(declarationId,1);
                        getEmployeeTaxRegime($('#s2id_formEmployeeName').select2('val'),'#editFormTaxDeclaration','#modalTaxDeclaration');
                    }
                });
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+$('#lblFormNameA').html()+' record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameA').html()+' record', type : 'info' });
        }
    });
    
    /** Change Event For Tax Declaration Form **/
    $('#editFormTaxDeclaration').on('change', function () {            
        isDirtyFormTaxDelcaration = true;    
    });    
    
    /** Change event for declaration status **/
     $('#formEmployeeName').on('change', function () {
        if ($(this).val() > 0) {            
            fnprefillEmployee($('#s2id_formEmployeeName').select2('val'));
            listSectionInvestmentCategory($('#s2id_formEmployeeName').select2('val'));
            getEmployeeTaxRegime($('#s2id_formEmployeeName').select2('val'),'#editFormTaxDeclaration','#modalTaxDeclaration');
        }
        else
        {
            if($('#IsEmpAdmin').val() === 'admin')
            {
                jAlert({ panel: $('#editFormTaxDeclaration'), msg: "Please create your salary record  before declaring the investment category", type: "warning" });
            }
            else
            {
                jAlert({ panel: $('#editFormTaxDeclaration'), msg: "You cannot declare your investment category because your salary details have not been configured. Please contact your HR or Payroll Admin", type: "warning" });
            }
            
        }
     });
    
    /** Change event for declaration status **/
     $('#formDeclarationStatus').on('change', function () {
        var status = $(this).val();
        
        if (status == 'Approved' || status == 'Rejected') {
            $('#ApprovalBasedHidden').show();
            
            if (status == 'Approved') {
                $('#labelAmountApproved').html('Amount Approved<span class="short_explanation">*</span>');
                $('#formAmountApproved').addClass('vRequired');
                $('#formAmountApproved').prop('readOnly', false);
                $('#formAmountApproved').val('');
            }
            else if (status == 'Rejected') {
                $('#labelAmountApproved').html('Amount Approved');
                $('#formAmountApproved').removeClass('vRequired');
                $('#formAmountApproved').prop('readOnly',true);
                $('#formAmountApproved').val('');
            }
        }
        else{
            $('#formAmountApproved').prop('readOnly',true);
            $('#labelAmountApproved').html('Amount Approved');
            $('#formAmountApproved').removeClass('vRequired');
            $('#formAmountApproved').val('');
            $('#formAmountApproved').valid();
            $('#ApprovalBasedHidden').hide();
        }
    });
     
     /** Change event for approved amount status **/
     $('#formAmountApproved').on('change keyup', function () {        
        if ($(this).val() != 0) {            
             $("#formAmountApproved").prop({
                 "min" : 1,
                 "max": $('#formAmountDeclared').val()
            });
        }
     });
    
    
    var aftDelNewTaxFiles =[], bucketUploadTaxSuccessFiles = [], bucketUploadTaxFailureFiles = [], newTaxFiles = [], oldTaxFiles = [];
    
    /** change event while select the file **/
    $('#uploadTaxFiles').on('change', function(){
        var files = $("#uploadTaxFiles")[0].files;        
        
        if(files != '' && files.length > 0){
            isDirtyFormTaxDelcaration = true;
                
            for (var i = 0; i < files.length; i++)
            {                
                var format = fncheckFileFormat(files[i].name.split('.').pop().toLowerCase());
                
                if (format)
                {   
                    /** push the files into newHrafiles **/
                    newTaxFiles.push(files[i]);
                    fileSize = parseFloat(files[i].size) / 1000;
                    
                    /** If File name length is greater than 10, then first 10 characters of file name only will be displayed and dot will
                        be concat at the end of the file name **/                        
                    displayFileName = files[i].name;
                    if(displayFileName.length > 10)
                    {
                        displayFileName = displayFileName.substring(0,10);
                        displayFileName = displayFileName+'...';
                    }
                    else
                    {
                        displayFileName = displayFileName;
                    }
                    
                    $('#TaxAttachmentsList').append('<div class="taxAttachmentDiv" style="max-width:100%;margin-bottom:15px;background:#dfdfdf;color:white;padding:5px;border-radius:3px;">'+
                                                    '<div style="text-overflow:ellipsis;display:inline-block;vertical-align:bottom;overflow:hidden;color: #555;"' +
                                                    '"max-width:75%;white-space:nowrap;cursor:pointer;">' + displayFileName
                                                    + '<div style="display:inline-block;cursor:pointer;color: #555;">' + '( ' + fileSize.toFixed(2) +
                                                    'K ) </div>'
                                                    +'</div><button type="button" style="margin-top:-2px;" id="'+ files[i].name +'" class="close attachmentdelTax removeTaxFile" aria-hidden='+true+
                                                    '> <i class="fa fa-close" style="color:black;font-size:20px"></i>' +
                                                    '</button> </div>');
                    
                }
                else{
                    jAlert ({ panel : $('#editFormTaxDeclaration'), msg : 'This format is not supported', type : 'warning' });
                }
            }
            
        }
    });

    /** download the file from s3 bucket **/
    $(document).on('click', '.taxFileDownload', function (e) {
        bucketFileName = $(this).prop('id');
        
        if(bucketFileName != undefined){            
            //var link = document.createElement("a");
            //////link.download = filename;
            //link.href = url;            
            //link.click();            
            
            //e.preventDefault();
            
            //this will open in a new window
            //$('#TaxAttachmentsList').append('<a id="downloadlink" href="' + url + '" target="_blank"></a>');
            $("#"+bucketFileName)[0].click();
            //$("#downloadlink").remove();
        }
        
    });
    
    /** Tax Declarations - delete the file from s3 bucket and remove that file name in table **/
    $(document).on('click', '.attachmentdelTax', function () {
        var attachment = $(this);        
        var bucketFileName = attachment.prop('id');
        
        /** If already uploaded files are deleted means, request will be passed to remove that file. Files will be uploaded only when submit **/
        if ($.inArray(bucketFileName, oldTaxFiles) !== -1)
        {
        var res = fndeleteS3Document(domainName+"/"+orgCode+"/"+"Tax Declarations/"+bucketFileName,'bucketName');

            if (res == 'Document deleted') {
                attachment.closest('.taxAttachmentDiv').remove();
                
                $.ajax ({
                type        : 'POST',
                dataType    : 'json',
                url      : pageUrl () +'payroll/tax-declarations/remove-uploaded-files/line_Item_Id/'+$('#lineItemId').val(),
                data     : {
                        _declarationFileName : bucketFileName,
                        _formName : 'Tax Declarations'
                },
                success     : function(result)
                {
                    if (isJson (result))
                    {
                        if (result.success)
                        {
                            /** update the total allowed file size **/
                            //getUploadedTotalFileSize('Tax Declarations');
                            
                            jAlert ({ panel : $('#editFormTaxDeclaration'), msg : result.msg, type : result.type });
                        }
                        else{
                            jAlert ({ panel : $('#editFormTaxDeclaration'), msg : 'Unable to delete file', type : 'warning' });
                        }
                        
                        //fnRefreshTable(tableTaxDeclarationSubGrid);
                    }
                    else
                    {
                        sessionExpired ();
                    }
                }
                });
                    
            }
            else {
                jAlert ({ panel : $('#editFormTaxDeclaration'), msg : 'Unable to delete file', type : 'warning' });
            }
              
        }
        else{
            /** remove only the selected file div and remove the deleted files by creating a new array **/
            attachment.closest('.taxAttachmentDiv').remove();            
            
            /** Removing the deleted files in uploaded file list in array **/
            for(var r=0;r<newTaxFiles.length;r++){
               if(newTaxFiles[r].name == bucketFileName){
                  newTaxFiles[r] = '';
               }
               else{
                  
               }
            }            
        }
    });
    
     
    /** Form submit - update investment Details , update investment status and approved amount **/
    $('#formDeclareSubmit,#formStatusSubmit').on('click', function (e) {
        var button      = $(this).prop('id');       
        var l = Ladda.create(this);
        l.start();
        
        $('#s2id_formEmployeeName, #s2id_formInvestmentCategory,#s2id_formDeclarationStatus,#s2id_formAssessmentYear').removeClass('form-error');
        //$('#formEmployeeName, #formInvestmentCategory,#formDeclarationStatus,#formAssessmentYear').removeClass('form-error');
        
        if (isDirtyFormTaxDelcaration)
        {   
            totalTaxUploadFileSize = uploadTaxSuccessCount = taxDeclarationFileSize = 0;
            bucketUploadTaxSuccessFiles = []; bucketUploadTaxFailureFiles = []; uploadTaxFiles = ''; bucketTaxFileNameArr = [] ;aftDelNewTaxFiles = [];
            
            if (button == 'formDeclareSubmit')
            {
                var formValid = ($('#formEmployeeName').valid() && $('#formAssessmentYear').valid() && $('#formComment').valid()
                                 && $('#formInvestmentCategory').valid() && $('#formDeclarationStatus').valid()
                                 && $('#formAmountDeclared').valid() && $('#formAmountApproved').valid());
                
                if(formValid)
                {
                    if(newTaxFiles.length > 0){
                        /** If some of the newly added files are deleted means, remaining added files will be deleted in
                        newHraFiles array. Index will be empty. So while upload, we will compare files array length with success count. It may differ.
                        So it is taken in another array**/                    
                        for(var r=0;r<newTaxFiles.length;r++){
                            if(newTaxFiles[r] != ''){
                               aftDelNewTaxFiles.push(newTaxFiles[r]);
                            }
                        }    
                    }
                    else{
                       aftDelNewTaxFiles= ''; 
                    }
                    
                    /** File upload - If old files(already uploaded files) are only exists no need to made a request for upload **/
                    if((aftDelNewTaxFiles.length > 0)){

                            uploadTaxFiles = aftDelNewTaxFiles;
                            TotalAllowedFileSize = 0;
                            
                            /** For each file, allowed file size is 3MB. Here file size will be in KB only. 3MB equals 3000KB.
                            By using the each files length we are calculating allowed file size **/
                            for(var nt=0;nt<uploadTaxFiles.length;nt++){                                
                                totalTaxUploadFileSize = parseFloat(uploadTaxFiles[nt].size) / 1000;
                                
                                if(totalTaxUploadFileSize > 3000)
                                {
                                    TotalAllowedFileSize += 1;
                                }
                            }
                            
                            if(TotalAllowedFileSize > 0/* && TotalAllowedFileSize!= 'NAN'*/){
                                jAlert({ panel : $('#editFormTaxDeclaration'),msg : 'Each file size should be less than or equal to 3MB', type : 'info' });
                            }
                            else{
                                for(var u=0;u<uploadTaxFiles.length;u++){
                                    var orgFileName = bucketFileName = file = dformat = '',uniqueId = 0,
                                    d = new Date();
                                    /** date is appended to file name to differentiate the same file names selected from
                                    different folders**/
                                    dformat = [d.getFullYear(),
                                               d.getMonth()+1,
                                                d.getDate()].join('-')+'.'+
                                               [d.getHours(),
                                                d.getMinutes(),
                                                d.getSeconds()].join(':'),                                
                                    file    = uploadTaxFiles[u];
                                    
                                    uniqueId = parseInt(u)+parseInt(1);
                                    
                                    /** File name format: EmpId_Investmentcategory_AssessmentYear_Filename_date_uniqueid . This is a wrong format as
                                     * date and unique id was appended after filename. So While download error will occur.
                                     * The correct file format is EmpId_Investmentcategory_AssessmentYear_date_uniqueid_Filename. **/
                                    
                                    /** old file format **/
                                    //bucketFileName = $('#formEmployeeName').select2('val')+'?'+$('#formInvestmentCategory').select2('val')+'?'+$('#formAssessmentYear').select2('val')+'?'+file.name+'?'+dformat+'?'+uniqueId;
                                    
                                    /** correct file format **/
                                    bucketFileName = $('#formEmployeeName').select2('val')+'?'+$('#formInvestmentCategory').select2('val')+'?'+$('#formAssessmentYear').select2('val')+'?'+dformat+'?'+uniqueId+'?'+file.name;
                                    
                                    bucketTaxFileNameArr.push(bucketFileName);
                                    
                                    if (uploadTaxFiles[u]) {
                                        var sUrl = fngetSignedPutUrl(domainName+"/"+orgCode+"/"+"Tax Declarations/"+bucketFileName,'bucketName');
                                        deletePhpActionHeaders();
                                        $.ajax({
                                            url: sUrl,
                                            type: 'PUT',
                                            contentType: file.type,
                                            processData: false,
                                            data: file,
                                          }).done(function(err,res ){
                                              if(res == "success") {
                                                /** push the uploaded files to update in table **/
                                                bucketUploadTaxSuccessFiles.push({Name:bucketTaxFileNameArr[uploadTaxSuccessCount],
                                                    Size:parseFloat(uploadTaxFiles[uploadTaxSuccessCount].size) / 1000})
                                                    uploadTaxSuccessCount += 1;
                                              } else {
                                                bucketUploadTaxFailureFiles.push({Name:bucketTaxFileNameArr[uploadTaxSuccessCount],
                                                    Size:parseFloat(uploadTaxFiles[uploadTaxSuccessCount].size) / 1000})
                                                    uploadTaxSuccessCount += 1;  
                                              }
                                              if(uploadTaxSuccessCount == (uploadTaxFiles.length)){                                                
                                                if(bucketUploadTaxFailureFiles != '' && bucketUploadTaxFailureFiles.length > 0){
                                                    /** If some of the files were not uploaded, message will be shown, and then it will
                                                     be updated **/
                                                    jAlert({ panel : $('#editFormTaxDeclaration'),msg : 'Few files were not uploaded', type : 'info' });
                                                }
                                                
                                                fnSubmitTaxDeclaration(button);
                                            }
                                        })
                                        setPhpActionHeaders();
                                    }
                                }
                            }
                    }
                    else{
                        fnSubmitTaxDeclaration(button);
                    }    
                }
                else
                {
                    $('#formEmployeeName').valid(); $('#formAssessmentYear').valid();
                    $('#formComment').valid(); $('#formInvestmentCategory').valid();
                    $('#formDeclarationStatus').valid(); $('#formAmountDeclared').valid(); $('#formAmountApproved').valid();
                    
                    fnModalScrollTop('modalTaxDeclaration');
                }
            }
            else
            {                
                var formValid = ($('#formEmployeeName').valid() && $('#formAssessmentYear').valid() && $('#formComment').valid()
                                 && $('#formInvestmentCategory').valid() && $('#formDeclarationStatus').valid()
                                 && $('#formAmountDeclared').valid() && $('#formAmountApproved').valid());
                
                if(formValid)
                {
                    fnSubmitTaxDeclaration(button);
                }
                else
                {
                    $('#formEmployeeName').valid(); $('#formAssessmentYear').valid();
                    $('#formComment').valid(); $('#formInvestmentCategory').valid();
                    $('#formDeclarationStatus').valid(); $('#formAmountDeclared').valid(); $('#formAmountApproved').valid();
                    
                    fnModalScrollTop('modalTaxDeclaration');
                }
            }
            
            l.stop();
            
            e.preventDefault();
        }
        else
        {
            l.stop();
            e.preventDefault();
            jAlert ({ panel : $('#editFormTaxDeclaration'), msg : 'Form has no changes', type : 'info' });
        }
    });
    
    
    function fnSubmitTaxDeclaration(button){
        setMask('#wholepage');
        if($('#s2id_formDeclarationStatus').select2('val')=='Declared' && bucketUploadTaxSuccessFiles!='')
        {
            var declarationStatus = "Applied";
        }
        else
        {
            var declarationStatus = $('#s2id_formDeclarationStatus').select2('val');
        }
    
        $.ajax ({
            type     : 'POST',
            dataType : 'json',
//          async    : false,
            url      : pageUrl () +'payroll/tax-declarations/update-tax-declarations',
            data     : {
                buttonId             : button,
                requestId            : $('#declarationId').val(),
                lineId               : $('#lineItemId').val(),
                employeeId           : $('#s2id_formEmployeeName').select2('val'),
                assessmentYear       : $('#s2id_formAssessmentYear').select2('val'),
                investmentCategory   : $('#s2id_formInvestmentCategory').select2('val'),
                investmentAgeGroup   : $('#s2id_formInvestmentAgeGroup').select2('val'),
                amountDeclared       : $('#formAmountDeclared').val(),
                status               : declarationStatus,//$('#s2id_formDeclarationStatus').select2('val'),
                amountApproved       : $('#formAmountApproved').val(),
                declarationFiles     : bucketUploadTaxSuccessFiles,
                comment              : $('#formComment').val(),
                statusMailUsr        : false
            },
            success  : function (result)
            {
                if (result && isJson (result))
                {
                    if (result.success)
                    {   
                        isDirtyFormTaxDelcaration = false;
                        
                        $('#lineItemId').val(0);
                        
                        fnModalScrollTop('modalTaxDeclaration');
                        
                        $('#TaxAttachmentsList').html("");
                        
                        if (button == 'formDeclareSubmit') {
                            $('#formResetDeclare').trigger('click');
                            
                            //$("#s2id_formDeclarationStatus").select2('val', 'Applied');
                            $("#s2id_formDeclarationStatus").select2('val', 'Declared');
                                      
                            //tableTaxDeclarationSubGrid
                            fnDeclarationSubGrid(result.Request_Id,0);
                        }
                        else{
                            $('#formResetStatus').trigger('click');
                            
                            //tableTaxDeclarationSubGrid
                            fnDeclarationSubGrid(result.Request_Id,1);
                        }
                        $('#declarationId').val(result.Request_Id);
                        $("#s2id_formEmployeeName").select2('val', result.Employee_Id);
                        $('#formEmployeeName').prop('readonly', true);
                        $("#formAssessmentYear").select2('val', result.Assessment_Year);
                        
                        /** update the total allowed file size **/
                        //getUploadedTotalFileSize('Tax Declarations');
                        
                        aftDelNewTaxFiles =[], bucketUploadTaxSuccessFiles = [], bucketUploadTaxFailureFiles = [], newTaxFiles = [], oldTaxFiles = [];
                           
                        jAlert ({ panel : $('#editFormTaxDeclaration'), msg : result.msg, type : result.type });                        
                        
                    }
                    else
                    {
                        fnModalScrollTop('modalTaxDeclaration');
                        
                        jAlert ({ panel : $('#editFormTaxDeclaration'), msg : result.msg, type : result.type });
                    }
                }
                else
                {
                    jAlert({ panel : $('#editFormTaxDeclaration'), msg : "Something went wrong. Please contact system admin", type : "warning" });
                }
                removeMask();
            },
            error : function(updateTaxDecErrorResult){
                removeMask();
                if(updateTaxDecErrorResult.status === 200){
                    sessionExpired();
                }else{
                    /* To handle internal server error */
                    jAlert({ panel : $('#editFormTaxDeclaration'), msg : "Something went wrong. Please contact system admin", type : "warning" });
                }
            }
        });
    }
    
    /**Submit Proof - Tax Declaration**/
    $('#formProofSubmitDeclarationMail').on('click', function () {
        var button      = $(this).prop('id');        
        var l = Ladda.create(this);
        l.start();
        
        $('#s2id_formEmployeeName, #s2id_formInvestmentCategory').removeClass('form-error');
        
        var rec = tableTaxDeclarationSubGrid.fnGetData ();
        
        if(rec != '')
        {
            if (!isDirtyFormTaxDelcaration)
            {            
                if ($('#declarationId').val() != 0 && $('#s2id_formEmployeeName').select2('val') != 0)
                {                
                    $.ajax ({
                        type     : 'POST',
                        dataType : 'json',
        //              async    : false,
                        url      : pageUrl () +'payroll/tax-declarations/add-tax-declaration',
                         data     : {                           
                            requestId            : $('#declarationId').val(),
                            employeeId           : $('#s2id_formEmployeeName').select2('val')
                        },
                        success  : function (result)
                        {
                            if (isJson (result))
                            {
                                if (result.success)
                                {
                                    isDirtyFormTaxDelcaration = false;
                            
                                    $('#modalTaxDeclaration').modal('toggle');
                                    
                                    fnRefreshTable (tableTaxDeclaration);
                                    
                                    jAlert ({ msg : result.msg, type : result.type });
                                  
                                }
                                else
                                {
                                    fnModalScrollTop('modalTaxDeclaration');
                                    
                                    jAlert ({ panel : $('#editFormTaxDeclaration'), msg : result.msg, type : result.type });
                                }
                            }
                            else
                            {
                                sessionExpired ();
                            }
                            
                            l.stop();
                        }
                    });
                    //jAlert ({ panel : $('#editFormTaxDeclaration'), msg : 'coore', type : 'info' });
                }
                else
                {
                    //jAlert ({ panel : $('#editFormTaxDeclaration'), msg : 'Invalid form', type : 'info' });
                    l.stop();
                }
            }
            else
            {
                jAlert ({ panel : $('#editFormTaxDeclaration'), msg : 'Kindly update the investment details', type : 'info' });
                l.stop();
            }
        }
        else{
            l.stop();            
            $('#declarationId').val(0);
            $('#lineItemId').val(0);
            jAlert ({ panel : $('#editFormTaxDeclaration'), msg : 'You should have atleast one investment detail to update', type : 'warning' });
        }
    });
    
    /** mail @ Form Status Submit - Declaration **/
    $('#formStatusSubmitDeclarationMail').on('click', function () {
        setMask('#wholepage');
        var button      = $(this).prop('id');        
        var l = Ladda.create(this);
        l.start();
        
        $('#s2id_formEmployeeName, #s2id_formInvestmentCategory').removeClass('form-error');
        
        if (!isDirtyFormTaxDelcaration)
        {            
            if ($('#declarationId').val() != 0)
            {
                $.ajax ({
                    type     : 'POST',
                    dataType : 'json',
    //              async    : false,
                    url      : pageUrl () +'payroll/tax-declarations/update-tax-declarations',
                    data     : {
                        buttonId             : button,
                        requestId            : $('#declarationId').val(),
                        statusMailUsr        : true
                    },
                    success  : function (result)
                    {
                        if (result && isJson (result))
                        {
                            if (result.success)
                            {
                                isDirtyFormTaxDelcaration = false;
                                
                                $('#modalTaxDeclaration').modal('toggle');
                                
                                fnRefreshTable (tableTaxDeclaration);                                
                                
                                jAlert ({ msg : result.msg, type : result.type });                                
                            }
                            else
                            {
                                jAlert ({ panel : $('#editFormTaxDeclaration'), msg : result.msg, type : result.type });
                            }
                        }
                        else
                        {
                            jAlert({ panel : $('#editFormTaxDeclaration'), msg : "Something went wrong. Please contact system admin", type : "warning" });
                        }
                            
                        l.stop();
                        removeMask();
                    },
                    error : function(taxDecStatusMailErrorResult){
                        l.stop();
                        removeMask();
                        if(taxDecStatusMailErrorResult.status === 200){
                            sessionExpired();
                        }else{
                            /* To handle internal server error */
                            jAlert({ panel : $('#editFormTaxDeclaration'), msg : "Something went wrong. Please contact system admin", type : "warning" });
                        }
                    }
               });
            }
            else
            {
                l.stop();
                removeMask();
            }
        }
        else
        {
            l.stop();
            removeMask();
            jAlert ({ panel : $('#editFormTaxDeclaration'), msg : 'Kindly update the status', type : 'info' });
        }
    });
    
    /** Close Tax Declaration Form **/
    $('#closeDeclaration,#formCloseSubmit').on('click', function () {
        var button      = $(this).prop('id');
        
        isDirtyFormTaxDelcaration = false;
        
        if (button == 'formCloseSubmit') {
            var rec = tableTaxDeclarationSubGrid.fnGetData ();
            
            if(rec != '')
            {
                $('#TaxAttachmentsList').html("");
                
                jAlert ({ msg : 'Tax declaration updated successfully', type : 'info' });
                
                fnRefreshTable (tableTaxDeclaration);
        
                fnFormCloseDeclaration (true);      
            }
            else{
                $('#declarationId').val(0);
                $('#lineItemId').val(0);
                jAlert ({ panel : $('#editFormTaxDeclaration'), msg : 'You should have atleast one investment detail to update', type : 'warning' });
            }
        }
        else{
            $('#TaxAttachmentsList').html("");
            
            fnRefreshTable (tableTaxDeclaration);
        
            fnFormCloseDeclaration (true);      
        }
        
    });
    
    /** Hide Tax Declaration Modal **/
    $('#modalTaxDeclaration').on('hide.bs.modal', function (e) {
        if (isDirtyFormTaxDelcaration)
        {
            e.preventDefault();
            e.stopImmediatePropagation();
            
            $('#dirtyTaxDeclaration').modal('toggle');
        }
        else
        {            
            fnFormCloseDeclaration (false);            
        }
    });    
    
    /** Reset The Values In Tax Declaration Form **/
    $('#formResetDeclare').on('click', function (e) {
        var l = Ladda.create(this);
        
        l.start();
        
        if ($('#lineItemId').val() > 0)
        {
            fnPreFillFormTaxDeclaration (tableTaxDeclarationSubGrid.fnGetData(fnGetSelected(tableTaxDeclarationSubGrid)[0]));
        }
        else
        {         
            fnPreFillFormTaxDeclaration('');
            e.preventDefault();
        }
        
        l.stop();
    });
    
    /** Reset The Values In Tax Declaration Status Form **/
    $('#formResetStatus').on('click', function (e) {
        var l = Ladda.create(this);
        
        l.start();
        //$('#formAmountApproved').removeClass('vRequired');
        
        if ($('#lineItemId').val() > 0)
        {            
            fnPreFillStatusFormDeclaration (tableTaxDeclarationSubGrid.fnGetData(fnGetSelected(tableTaxDeclarationSubGrid)[0]));
        }
        else
        {         
            fnPreFillStatusFormDeclaration('');
            e.preventDefault();
        }
        
        l.stop();
    });
    
    /**
     *  Show history details for selection record
    */
    $('#historyTaxDeclaration, #historyContextTaxDeclaration').on('click', function(){
        var selectedRow = fnGetSelected ( tableTaxDeclaration );
        
        fnFilterClose ('TaxDeclaration');
        
        if (selectedRow.length)
        {            
            var record = tableTaxDeclaration.fnGetData (selectedRow[0]);
            
            if (record.Declaration_Id > 0)
            {
                $('#modalHistoryTaxDeclaration').modal('toggle');                
                
                $('#taxDeclarationHistoryTitle').html("<strong>View<strong> History - " +record.Employee_Name);
                
                tableTaxDeclarationHistory = $('#tableTaxDeclarationHistory').dataTable ({
                    "bPaginate"      : false,
                    "bLengthChange"  : false,
                    "bFilter"        : false,
                    "bDestroy"       : true,
                    "bAutoWidth"     : false,
                    "bServerSide"    : true,
                    "bDeferRender"   : true,
                    "sServerMethod"  : "POST",
                    "sAjaxSource"    : pageUrl ()+'payroll/tax-declarations/show-audit-tax-declarations/declarationId/'+record.Declaration_Id,
                    "sAjaxDataProp"  : "aaData",
                    "aaSorting"      : [],
                    "aoColumnDefs"   : [{ 'bSortable': false, 'aTargets': ['_all'] },
                                        { "sClass": "visible-xs visible-sm  visible-md visible-lg", "aTargets" : [0] },
                                        { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [3] },
                                        { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [4] },
                                        { "sClass" : "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets" : [5] },
                                        { "sClass" : "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets" : [6] },
                                        { "sClass": "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets": [7] },
                                        { "sClass": "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets": [8] }],
                    "aoColumns"      : [{
                        "mData" : function (row, type, set) {
                            return '<i class="fa fa-plus-square-o"></i>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            return '<div >'+ row['Old_Investment_Category'] +'</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            return '<div>'+ row['New_Investment_Category'] +'</div>';
                        }
                    },
                     {
                        "mData" : function (row, type, set) {
                            return '<div>'+ row['Old_Amount_Declared'] +'</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            return '<div >'+ row['New_Amount_Declared'] +'</div>';
                        }
                    },
                    {
                        "mData": function (row, type, set) {
                            return '<div>' + fnCheckNull(row['Old_Age_Group']) + '</div>';
                        }
                    },
                    {
                        "mData": function (row, type, set) {
                            return '<div >' + fnCheckNull(row['New_Age_Group']) + '</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            return '<div>'+ row['ModifiedBy'] +'</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            return '<div >'+ row['ModifiedDate'] +'</div>';
                        }
                    }]
                });
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+$('#lblFormNameA').html()+' record', type : 'info' });
            }
        }
        else
        {            
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameA').html()+' record', type : 'info' });
        }
    });
    
    
    //On + icon click in mobile & tablet view
    $(document).on('click', '#tableTaxDeclarationHistory i', function () {
        var nTr = $(this).parents('tr')[0];
        
        fnFilterClose ('TaxDeclaration');
        
        if ( tableTaxDeclarationHistory.fnIsOpen(nTr) )
        {
            /* This row is already open - close it */
            $(this).removeClass().addClass('fa fa-plus-square-o');
            tableTaxDeclarationHistory.fnClose(nTr);
        }
        else
        {
            var record = tableTaxDeclarationHistory.fnGetData( nTr );
            var nRow =  $('#tableTaxDeclarationHistory thead tr')[0];
            
            /* Open this row */
            $(this).removeClass().addClass('fa fa-minus-square-o');
            
            valueArray = [];
            headerArray=[];
        
            valueArray.Value_One = record.Old_Amount_Declared;
            valueArray.Value_Two = record.New_Amount_Declared;
            valueArray.Value_Three = fnCheckNull(record.Old_Age_Group);
            valueArray.Value_Four = fnCheckNull(record.New_Age_Group);
            valueArray.Value_Five = record.ModifiedBy;
            valueArray.Value_Six = record.ModifiedDate;
            
            $.each(nRow.cells, function(i,v) {
                headerArray['Header'+i] = v.innerText;
            });
            
            tableTaxDeclarationHistory.fnOpen(nTr, fnDeviceColumnDetails(headerArray,valueArray), 'details');
        }
    });
    
    /**
     *  Click to close add/edit modal form back button
    */
    $('#modalFormCloseTaxDeclarationHistory').on('click', function () {        
         $('#modalHistoryTaxDeclaration').modal('hide');    
    });
    
     /**
     *  Show comments details for selection record
    */
    $('#commentTaxDeclaration, #commentContextTaxDeclaration').on('click', function(){
        var selectedRow = fnGetSelected (tableTaxDeclaration );
        
        fnFilterClose ('TaxDeclaration');
         
        if (selectedRow.length)
        {            
            var record = tableTaxDeclaration.fnGetData (selectedRow[0]);
        
            if (record.Declaration_Id > 0)
            {
                $('#modalCommentTaxDeclaration').modal('toggle');
                
                tableCommentTaxDeclaration = $('#tableCommentTaxDeclaration').dataTable ({
                    "bPaginate"      : false,
                    "bLengthChange"  : false,
                    "bFilter"        : false,
                    "bDestroy"       : true,
                    "bAutoWidth"     : false,
                    "bServerSide"    : true,
                    "bDeferRender"   : true,
                    "sServerMethod"  : "POST",
                    "sAjaxSource"    : pageUrl ()+'default/index/list-comments/formName/Tax Declarations/parentId/'+record.Declaration_Id,
                    "sAjaxDataProp"  : "aaData",
                    "aaSorting"      : [],
                    "aoColumnDefs"   : [{ 'bSortable': false, 'aTargets': ['_all'] },
                                        { "sClass" : "visible-xs visible-sm  hidden-md hidden-lg", "aTargets" : [0] },
                                        { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [3] }],
                    "aoColumns"      : [{
                        "mData" : function (row, type, set) {
                            return '<i class="fa fa-plus-square-o"></i>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            return '<div class="'+ row['DT_RowId'] +'" >'+ row['Employee_Name'] +'</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            return '<div class="'+ row['DT_RowId'] +'" >'+ row['Emp_Comment'] +'</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            return '<div class="'+ row['DT_RowId'] +'text-center" >'+ row['Added_On'] +'</div>';
                        }
                    }]
                });                    
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+$('#lblFormNameA').html()+' record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameA').html()+' record', type : 'info' });
        }
    });
    
    //On + icon click in mobile & tablet view
    $(document).on('click', '#tableCommentTaxDeclaration i', function () {
        var nTr = $(this).parents('tr')[0];
        
        if ( tableCommentTaxDeclaration.fnIsOpen(nTr) )
        {
            /* This row is already open - close it */
            $(this).removeClass().addClass('fa fa-plus-square-o');
            tableCommentTaxDeclaration.fnClose(nTr);
        }
        else
        {
            var record = tableCommentTaxDeclaration.fnGetData( nTr );
            var nRow =  $('#tableCommentTaxDeclaration thead tr')[0];
            
            /* Open this row */
            $(this).removeClass().addClass('fa fa-minus-square-o');
            
            valueArray = [];
            headerArray=[];
        
            valueArray.Value_One = record.Added_On;
            
            $.each(nRow.cells, function(i,v) {
                headerArray['Header'+i]=v.innerText;
            });
            
            tableCommentTaxDeclaration.fnOpen(nTr, fnDeviceColumnDetails(headerArray,valueArray), 'details hidden-lg hidden-md');
        }
    });
    
    /** Show&Hide the Tax Declaration Filter **/
    $('#filterTaxDeclaration,#closeFilterTaxDeclaration').on('click', function() {
        if ($('#filterPanelTaxDeclaration').hasClass('open'))
        {
            $('#filterPanelTaxDeclaration').removeClass('open');
            $('#filterPanelTaxDeclaration').hide();
        }
        else
        {
            $('#filterPanelTaxDeclaration').addClass('open');
            $('#filterPanelTaxDeclaration').show();
        }
    });
    
    /** Reset The Values In Tax Declaration Filter **/
    $('#cancelTaxDeclaration,#closeFilterTaxDeclaration').on('click', function () {
         if (!$('#filterEmployeeName').is('[readonly]')) {
            $('#filterEmployeeName').val('');
        }
        
        $('#filterAssesmentYearStart, #filterAssesmentYearEnd, #filterDeclaredAmountStart,#filterDeclaredAmountEnd,#filterApprovedAmountStart,#filterApprovedAmountEnd').val('');        

        if ($('#fieldForce').val() == 1) {
            $('#s2id_filterTaxDeclarationServiceProvider').select2('val', '');
        }
        tableTaxDeclaration.fnReloadAjax( pageUrl() +'payroll/tax-declarations/show-tax-declarations' );
    });
    
     /** Apply The Values In Tax Declaration Filter **/
    $('#applyTaxDeclaration').on('click', function () {        
        filterEmployeeName        = $('#filterEmployeeName').val();        
        filterAssesmentYearStart  = $('#filterAssesmentYearStart').val();
        filterAssesmentYearEnd    = $('#filterAssesmentYearEnd').val();
        filterDeclaredAmountStart = $('#filterDeclaredAmountStart').val();
        filterDeclaredAmountEnd   = $('#filterDeclaredAmountEnd').val();
        filterApprovedAmountStart = $('#filterApprovedAmountStart').val();
        filterApprovedAmountEnd   = $('#filterApprovedAmountEnd').val();

        if ($('#fieldForce').val() == 1) {
            var ftServiceProvider = $('#s2id_filterTaxDeclarationServiceProvider').select2('val');
        }
        else {
            var ftServiceProvider = '';
        }
        
        tableTaxDeclaration.fnReloadAjax(pageUrl() +'payroll/tax-declarations/show-tax-declarations'+
                                                  '/employeeName/'+ filterEmployeeName
                                                  +'/assessmentYearStart/'+ filterAssesmentYearStart
                                                  +'/assessmentYearEnd/'+ filterAssesmentYearEnd
                                                  +'/declarationAmountStart/'+ filterDeclaredAmountStart
                                                  +'/declarationAmountEnd/'+ filterDeclaredAmountEnd
                                                  +'/approvedAmountStart/'+ filterApprovedAmountStart
                                                  +'/approvedAmountEnd/'+ filterApprovedAmountEnd
                                                  +'/serviceProviderId/' + ftServiceProvider);
    });
    
    /* Formating function for row details */
    function fnShowHiddenTaxDeclarationDetails ( nTr ) {
        var aData = tableTaxDeclarationSubGridView.fnGetData( nTr );
            filesArr= aData['Declaration_Path'];
            sOut = '';
        
        sOut += '<div class="row hidden-md hidden-lg">';
        sOut += '<div class="col-xs-4">Amount Approved</div>'+
                    '<div class="col-xs-1"> : </div>'+
                    '<div class="col-xs-6">'+ fnCheckNull (aData['Amount_Approved']) +'</div>'+
                '</div>'+
                '<div class="row hidden-md hidden-lg">'+
                    '<div class="col-xs-4">Status</div>'+
                    '<div class="col-xs-1"> : </div>'+
                    '<div class="col-xs-6">'+ fnCheckNull (aData['Status']) +'</div>'+
                '</div>';
        
        for (var i = 0; i < filesArr.length; i++)
        {            
            sOut +='<div class="row">';
            if(i == 0){                
                sOut +='<div class="col-xs-4">Documents</div>'
                    +'<div class="col-xs-1"> : </div>';
            }
            else{
                sOut +='<div class="col-xs-5"></div>';
            }
                    
                displayFileName = filesArr[i]['File_Name'].split('?')[5];
                        
                /** get the display File name for the old format(EmpId_InvestmentCategory_AssessmentYear_Filename_date_uniqueid)
                and new format(EmpId_InvestmentCategory_AssessmentYear_date_uniqueid_Filename) **/
                if(isNaN(Date.parse(displayFileName)))
                {                            
                    displayFileName = filesArr[i]['File_Name'].split('?')[5];
                }
                else
                {                            
                    displayFileName = filesArr[i]['File_Name'].split('?')[3];
                }
                
                /** If File name length is greater than 15, then first 15 characters of file name only will be displayed and dot will
                    be concat at the end of the file name **/
                
                if(displayFileName.length > 15)
                {
                    displayFileName = displayFileName.substring(0,15);
                    displayFileName = displayFileName+'...';
                }
                else
                {
                    displayFileName = displayFileName;
                }
                
                /** file link **/
                    var sUrl = fngetSignedUrl(domainName+"/"+orgCode+"/"+"Tax Declarations/"+filesArr[i]['File_Name'],'bucketName');
                    
                    sOut +='<div class="col-xs-6" id = "TaxAttachmentsViewList">'+
                    '<a target="_blank" style="padding:0px;cursor: pointer;text-decoration:none;" title="'+filesArr[i]['File_Name'].split('?')[3]+'" id="taxdownloadlink'+i+'" href="' + sUrl + '" class="taxFileDownload">'
                        + displayFileName +'</a> </div></div>';
        
            //sOut +='<div class="col-xs-6" id = "TaxAttachmentsViewList">'+
            //        '<a target="_blank" style="padding:0px;cursor: pointer;" id="'+ filesArr[i]['File_Name'] +'" class="taxFileDownload">'
            //            + filesArr[i]['File_Name'].split('?')[3] +'</a> </div></div>';
        
        }
        
        sOut += '<div class="row">'+
                    '<div class="col-xs-4">Added On</div>'+
                    '<div class="col-xs-1"> : </div>'+
                    '<div class="col-xs-6">'+ fnCheckNull (aData['Added_On']) +'</div>'+
                '</div>'+
                '<div class="row">'+
                    '<div class="col-xs-4">Added By</div>'+
                    '<div class="col-xs-1"> : </div>'+
                    '<div class="col-xs-6">'+ fnCheckNull (aData['Added_By_Name']) +'</div>'+
                '</div>';         
            
            if (aData['Approved_By_Name'] != null && aData['Approved_On'] != null)
        {
            sOut += '<div class="row">'+
                        '<div class="col-xs-4">Approved By</div>'+
                        '<div class="col-xs-1"> : </div>'+
                        '<div class="col-xs-6">'+ fnCheckNull (aData['Approved_By_Name']) +'</div>'+
                    '</div>'+
                    '<div class="row">'+
                        '<div class="col-xs-4">Approved On</div>'+
                        '<div class="col-xs-1"> : </div>'+
                        '<div class="col-xs-6">'+ fnCheckNull (aData['Approved_On']) +'</div>'+
                    '</div>';
        }
             
        return sOut;

    }
    
    /** Prefill Log Employee Name and reload the subgrid if the employee already Exists **/ 
    function fnprefillEmployee (employeeId) {
        $("#formProofSubmitDeclarationMail").show();
        $("#formProofSubmitDeclarationMail").html('<i class="mdi-content-send"></i> Submit Proof');
        $("#formDeclareSubmit").html('<i class="mdi-content-send"></i> Declare');
        
        if (employeeId != 0 ) {            
            $.ajax ({
                    type     : 'POST',
                    async    : false,
                    dataType : "json",
                    url      : pageUrl ()+'payroll/tax-declarations/partial-form/employeeId/'+ employeeId, 
                    success  : function(result)
                    {                        
                        if (result.requestId != 0) {
                            $('#declarationId').val(result.requestId);
                            fnDeclarationSubGrid(result.requestId,0);
                        }
                        else
                        {
                            $('#declarationId').val(result.requestId);
                            fnDeclarationSubGrid(result.requestId,0);
                        }
                    }
                });
        }
        
        if($('#AccessEmpCheck').val()==1){
            $('#formEmployeeName').prop('readOnly', false);
        }
        else{
            $('#formEmployeeName').prop('readonly', true);            
        }
    }
    
    /** Close Function For All the Forms **/ 
    function fnFormCloseDeclaration (hideAction) {
        var uniqueId = $('#declarationId').val();
        
        if (uniqueId > 0 && !isNaN(uniqueId))
        {            
            clearLock ({
                'formName' : 'Tax Declarations',
                'uniqueId' : uniqueId,
                'callback' : function ()
                {
                    $('#declarationId').val(0);
         
                    if (hideAction)
                        $('#modalTaxDeclaration').modal('hide');
                }
            });
        }
        else
        {
            if (hideAction)
                $('#modalTaxDeclaration').modal('hide');
        }
        $('#gridPanelTaxDeclaration .panel-reload').trigger('click');
    }
    

    
    /**
     *  Declaration Sub Grid in add, edit, status update modal
    */ 
    function fnDeclarationSubGrid(declarationId,status)
    {
         tableTaxDeclarationSubGrid = $('#tableTaxDeclarationSubGrid').dataTable({
            "lengthMenu"     : [ 5, 10, 25, 50, 100 ], 
            "iDisplayLength" : 10,
            "bDestroy"       : true,
            "bAutoWidth"     : false,
            "bServerSide"    : true,
            "bDeferRender"   : true,
            "sServerMethod"  : "POST", 
            "sAjaxSource"    : pageUrl() + "payroll/tax-declarations/tax-declarations-subgrid/declarationId/"+declarationId,
            "sAjaxDataProp"  : "aaData",
            "info"           : false,
            "bPaginate"      : false, // Remove pagination
            "bFilter"        : false,
            "aaSorting"      : [],
            "aoColumnDefs"   : [{ 'bSortable': false, 'aTargets': ['_all'] },
                                { "sClass" : "visible-xs visible-sm  hidden-md hidden-lg", "aTargets" : [0] },
                                { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [4] },
                                { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [5] }],
            "aoColumns"      :[{
            "mData" : function (row, type, set) {
                    return '<i class="fa fa-plus-square-o"></i>';
                }
            },
            {
                "mData" : function (row, type, set) {
                    
                    
                    if (row.Status == 'Applied' && status == 1)
                    {
                        /** enable edit and disable delete **/
                        return '<div class="btn btn-sm btn-embossed btn-white statusUpdateDeclarationDetails" title="Edit" id="statusdeclarationDetails_'+row['Line_Item_Id']+'"><i class="mdi-editor-mode-edit"></i></div>';
                    }
                    else if(row.Status == 'Applied' && status == 0)
                    {
                        if ($('#lockSettings').val() == 'unlock')
                        {
                            /** enable edit and disable delete **/
                            return '<div class="btn btn-sm btn-embossed btn-white editDeclarationDetailsSubGrid" title="Edit" id="declarationDetails_' + row['Line_Item_Id'] + '"><i class="mdi-editor-mode-edit"></i></div> <div class="btn btn-sm btn-embossed btn-white deleteDeclarationDetailsSubGrid" title="Delete" id="deleteDeclarationDetails_' + row['Line_Item_Id'] + '" ><i class="mdi-action-delete"></i></div>';
                        }
                        else
                        {
                            return '<div class="btn btn-sm btn-embossed btn-white editDeclarationDetailsSubGrid" title="Edit" id="declarationDetails_' + row['Line_Item_Id'] + '"><i class="mdi-editor-mode-edit"></i></div>';
                        }
                       
                    }
                    
                    if(row.Status == 'Declared' && status == 0)
                    {
                        if ($('#lockSettings').val() == 'unlock')
                        {
                            /** enable edit and delete **/
                            return '<div class="btn btn-sm btn-embossed btn-white editDeclarationDetailsSubGrid" title="Edit" id="declarationDetails_'+row['Line_Item_Id']+'"><i class="mdi-editor-mode-edit"></i></div> <div class="btn btn-sm btn-embossed btn-white deleteDeclarationDetailsSubGrid" title="Delete" id="deleteDeclarationDetails_'+row['Line_Item_Id']+'" ><i class="mdi-action-delete"></i></div>';
                        }
                        else
                        {
                            return '<div class="btn btn-sm btn-embossed btn-white editDeclarationDetailsSubGrid" title="Edit" id="declarationDetails_' + row['Line_Item_Id'] + '"><i class="mdi-editor-mode-edit"></i></div>';
                        }
                    }
                    else if(row.Status == 'Declared' && status == 1)
                    {
                        /** enable edit and delete **/
                        return '<div class="btn btn-sm btn-embossed btn-white editDeclarationDetailsSubGrid" title="Edit" id="declarationDetails_'+row['Line_Item_Id']+'"><i class="mdi-editor-mode-edit"></i></div>';
                    }
                    
                    if (row.Status == 'Rejected' && status == 0)
                    {
                        if ($('#lockSettings').val() == 'unlock') 
                        {
                            /** enable delete in subgrid **/
                            return '<div class="btn btn-sm btn-embossed btn-white approvedDetailsAlert" title="Edit" id="disabledeclarationDetails_'+row['Line_Item_Id']+'"><i class="mdi-editor-mode-edit"></i></div> <div class="btn btn-sm btn-embossed btn-white deleteDeclarationDetailsSubGrid" title="Delete" id="deleteDeclarationDetails_'+row['Line_Item_Id']+'" ><i class="mdi-action-delete"></i></div>';
                        }
                        else
                        {
                            return '<div class="btn btn-sm btn-embossed btn-white approvedDetailsAlert" title="Edit" id="disabledeclarationDetails_' + row['Line_Item_Id'] + '"><i class="mdi-editor-mode-edit"></i></div>';
                        }
                    }
                    else if(row.Status == 'Rejected' && status == 1)
                    {
                        /** enable edit and delete **/
                        return '<div class="btn btn-sm btn-embossed btn-white approvedDetailsAlert" title="Edit" id="disabledeclarationDetails_'+row['Line_Item_Id']+'"><i class="mdi-editor-mode-edit"></i></div>';
                    }
                    
                    if (row.Status == 'Approved' && status == 0)
                    {
                         return '<div class="btn btn-sm btn-embossed btn-white approvedDetailsAlert" title="Edit" id="disabledeclarationDetails_'+row['Line_Item_Id']+'"><i class="mdi-editor-mode-edit"></i></div> <div class="btn btn-sm btn-embossed btn-white deleteAlertSubGrid" title="Delete" id="deleteAlertSubGrid'+row['Line_Item_Id']+'" ><i class="mdi-action-delete"></i></div>';
                    }
                    else if (row.Status == 'Approved' && status == 1)
                    {
                        //code
                        return '<div class="btn btn-sm btn-embossed btn-white approvedDetailsAlert" title="Edit" id="disabledeclarationDetails_'+row['Line_Item_Id']+'"><i class="mdi-editor-mode-edit"></i></div>';
                    }
                }
            },            
            {
                "mData"  : function (row, type, set) {
                    return '<div>'+ row['Investment_Category'] +'</div>';
                }
            },
            {
                "mData"  : function (row, type, set) {
                    return '<div>'+ row['Amount_Declared'] +'</div>';
                }
            },
            {
                "mData"  : function (row, type, set) {
                    return '<div>'+ row['Status'] +'</div>';
                }
            },
            {
                "mData"  : function (row, type, set) {
                    return '<div>'+ fnCheckNull(row['Amount_Approved']) +'</div>';
                }
            }]
        });
    }

    $(document).on('click contextmenu', '#tableTaxDeclarationSubGrid tbody td div', function () {
        var selectRow = $(this).parent().parent();
        tableTaxDeclarationSubGrid.$('tr.row_selected').removeClass('row_selected');
        if (!selectRow.hasClass('row_selected'))
        {
            selectRow.addClass('row_selected');
            fnActionButtonsTaxDeclarationSubGrid (true);
        }
        else
        {
            fnActionButtonsTaxDeclarationSubGrid (false);
        }
    });

    function fnActionButtonsTaxDeclarationSubGrid (action) {
        if (action)
        {
            var record          = tableTaxDeclarationSubGrid.fnGetData (fnGetSelected (tableTaxDeclarationSubGrid)[0]);
            var approvalStatus  = record['Status'];
            var autoDeclaration = parseInt(record['Auto_Declaration']);
            console.log(approvalStatus);
            console.log(autoDeclaration);
            if(approvalStatus=='Approved' && autoDeclaration==0)
            {
                $('#revertTaxDeclaration').show();
                fnGridButtons ($('#revertTaxDeclaration'), true);
            }
            else
            {
                $('#revertTaxDeclaration').hide();
                fnGridButtons ($('#revertTaxDeclaration'), false);
            }
        }
        else
        {
            $('#revertTaxDeclaration').hide();
            fnGridButtons ($('#revertTaxDeclaration'), false);
        }
    }
    
    //On + icon click in mobile & tablet view
    $(document).on('click', '#tableTaxDeclarationSubGrid i', function () {
        var nTr = $(this).parents('tr')[0];
        
        if ( tableTaxDeclarationSubGrid.fnIsOpen(nTr) )
        {
            if($(this).hasClass('fa-minus-square-o'))
            {
                /* This row is already open - close it */
                $(this).removeClass().addClass('fa fa-plus-square-o');
                tableTaxDeclarationSubGrid.fnClose(nTr);
            }
        }
        else
        {
            /* Open this row */
            if($(this).hasClass('fa-plus-square-o'))
            {
                var record = tableTaxDeclarationSubGrid.fnGetData( nTr );
                var nRow =  $('#tableTaxDeclarationSubGrid thead tr')[0];
                
                /* Open this row */
                $(this).removeClass().addClass('fa fa-minus-square-o');
                
                valueArray = [];
                headerArray=[];
            
                valueArray.Value_One = record.Status;
                valueArray.Value_Two = record.Amount_Approved;
                
                headerArray['Header3'] = 'Status';
                headerArray['Header4'] = 'Amount Approved';
                
                tableTaxDeclarationSubGrid.fnOpen(nTr, fnDeviceColumnDetails(headerArray,valueArray), 'details hidden-lg hidden-md');
            }
        }
    });
   
    /**
     *  Prefill Tax Declaration form values in add, edit, reset events
    */
    function fnPreFillFormTaxDeclaration(record) {
        //$('#s2id_formEmployeeName,#s2id_formInvestmentCategory,#s2id_formAssessmentYear').removeClass('form-error');
        $('#s2id_formEmployeeName, #s2id_formInvestmentCategory,#s2id_formDeclarationStatus,#s2id_formAssessmentYear,#s2id_formInvestmentAgeGroup').removeClass('form-error');
     
        $('#formInvestmentCategory,#formAmountDeclared,#formDeclarationStatus,#formAmountApproved').prop('readOnly', false);        
        
        aftDelNewTaxFiles =[], bucketUploadTaxSuccessFiles = [], bucketUploadTaxFailureFiles = [], newTaxFiles = [], oldTaxFiles = [];
        
        $('#buttonTaxUploadFile').show();
        
        /** To prefill investment details **/
        if (record != '')
        {            
            /** To prefill employee details while edit form **/
            var mainGridRecord = tableTaxDeclaration.fnGetData (fnGetSelected ( tableTaxDeclaration )[0]);
           
            $("#s2id_formEmployeeName").select2('val', record.Employee_Id);
            $('#formEmployeeName').prop('readonly', true);            
            $("#s2id_formAssessmentYear").select2('val', record.Assessment_Year);
            
            $('#declarationId').val(record.Declaration_Id);
            $('#lineItemId').val(record.Line_Item_Id);
            
            $("#formInvestmentCategory").select2('val', record.Investment_Category_Id);
            if (record.Age_Group_Id > 0)
            {
                $("#formInvestmentCategory").trigger('change');
                $("#formInvestmentAgeGroup").select2('val', record.Age_Group_Id);    
            }
            else
            {
                $('#formInvestmentAgeGroup').removeClass('vRequired');
                $('.formInvestmentAgeGroupPanel').hide();
                $("#formInvestmentAgeGroup").select2('val', '');
            }
            $('#formAmountDeclared').val(record.Amount_Declared);
            $("#s2id_formDeclarationStatus").select2('val', record.Status);            
            
            $('#formAmountApproved').val('');
            $('.ApprovalBasedHidden').hide();
            
            fnsetApprovalStatusField(record,$("#formDeclarationStatus"));
            
            if(record.Declaration_Path.length > 0){
               fnPrefillUploadFiles('edit', record.Declaration_Path, 'Tax Declarations'); 
            }
            else{
                $('#TaxAttachmentsList').html("");    
            }
        }
        else
        {            
            //$('#declarationId').val(0);
            $('#lineItemId').val(0);
            
            $('#s2id_formInvestmentCategory,#formDeclarationStatus,#formInvestmentAgeGroup').select2('val', '');
            
            $("#formAmountDeclared,#formAmountApproved").val('');
            
            $('.ApprovalBasedHidden').hide();
            
            $('#TaxAttachmentsList').html("");
            $('#formInvestmentAgeGroup').removeClass('vRequired');   
            $('.formInvestmentAgeGroupPanel').hide();
            fnsetApprovalStatusField('',$("#formDeclarationStatus"));
        }
        
        $('#formAmountApproved').removeClass('vRequired');
        $('#formAmountApproved').valid();
                
        $('#formComment').val('');

        fnTaxlockConfiguration();
 
        $("#editFormTaxDeclaration").validate().resetForm();
        

        isDirtyFormTaxDelcaration = false;
    }
    
    function fnsetApprovalStatusField(record,field)
    {        
        var statusArray=[];
        field.find('option').remove();
        if (record != '') {
            //while edit form
            if (record.Approver_Id != null) {                
                if (record.Status == 'Applied') {
                    statusArray = ['Applied'];
                }
                else if (record.Status == 'Approved') {
                    statusArray = ['Approved'];
                }
                else
                {
                    //statusArray = ['Rejected'];
                    statusArray = ['Declared','Applied','Rejected'];
                }
            }
            else
            {
                if (record.Status == 'Rejected') {                    
                    statusArray = ['Applied','Rejected'];
                }
                else{
                    //statusArray = ['Applied','Approved','Rejected'];
                    statusArray = ['Declared','Applied','Approved','Rejected'];
                }
            }
        }
        else{
            //statusArray = ['Applied'];
            statusArray = ['Declared'];
        }
        
        field.append( "<option value=''>-- Select --</option>");
            
        for (var x in statusArray)
        {
            field.append("<option value='" + statusArray[x] + "'>" + statusArray[x] + "</option>");
        }
        
        if (record != '')
            field.select2( 'val', record.Status);
    }
    
    
    
    /**
     *  Prefill Tax Declaration form values while Status update
    */
    function fnPreFillStatusFormDeclaration(record) {
        $('#s2id_formEmployeeName,#s2id_formInvestmentCategory,#s2id_formAssessmentYear,#s2id_formInvestmentAgeGroup').removeClass('form-error');
        
        $('#formInvestmentCategory,#formAmountDeclared').prop('readOnly',true);
        $('#formDeclarationStatus,#formAmountApproved').prop('readOnly', false);
        
        var mainGridRecord = tableTaxDeclaration.fnGetData (fnGetSelected ( tableTaxDeclaration )[0]);
        
        $("#s2id_formEmployeeName").select2('val', mainGridRecord.Employee_Id);
        $('#formEmployeeName').prop('readonly', true);
        $("#formAssessmentYear").select2('val', mainGridRecord.Assessment_Year);
        $('#declarationId').val(mainGridRecord.Declaration_Id);
        
        var statusArray=[];
        
        $('#TaxAttachmentsList').html("");
        
        $('#buttonTaxUploadFile').hide();
        
        aftDelNewTaxFiles =[], bucketUploadTaxSuccessFiles = [], bucketUploadTaxFailureFiles = [], newTaxFiles = [], oldTaxFiles = [];
        
        if (record != '')
        {            
            $('#declarationId').val(record.Declaration_Id);
            $('#lineItemId').val(record.Line_Item_Id);
            
            $("#s2id_formInvestmentCategory").select2('val', record.Investment_Category_Id);
            $('#formAmountDeclared').val(record.Amount_Declared);
            $("#formDeclarationStatus").select2('val', record.Status);            
            
            $('#labelAmountApproved').html('Amount Approved');
            $('#formAmountApproved').removeClass('vRequired');
            $('#formAmountApproved').prop('readOnly',true);
            $('#formAmountApproved').val('');
            $('.ApprovalBasedHidden').show();
            
            var field = $("#formDeclarationStatus");
            
            //field.prop('disabled', false).find('option').remove();
            field.find('option').remove();
            
            statusArray = ['Applied','Approved','Rejected'];            
            
            field.append( "<option value=''>-- Select --</option>");
                
            for (var x in statusArray)
            {
                field.append("<option value='" + statusArray[x] + "'>" + statusArray[x] + "</option>");
            }
    
            field.select2( 'val', record.Status);
            
            if(record.Declaration_Path.length > 0){
                fnPrefillUploadFiles('status', record.Declaration_Path, 'Tax Declarations');
            }
            else{
                $('#TaxAttachmentsList').html("");
            }

            if (record.Age_Group_Id > 0) {
                $("#formInvestmentCategory").trigger('change');
                $("#formInvestmentAgeGroup").select2('val', record.Age_Group_Id);
            }
            else
            {
                $('#formInvestmentAgeGroup').removeClass('vRequired');
                $('.formInvestmentAgeGroupPanel').hide();
                $("#formInvestmentAgeGroup").select2('val','');
            }
        }
        else
        {            
            $('#lineItemId').val(0);            
            
            $("#s2id_formInvestmentCategory,#s2id_formDeclarationStatus,#formInvestmentAgeGroup").select2('val', '');
            
            $('#formAmountDeclared,#formAmountApproved').val('');
            
            $('#labelAmountApproved').html('Amount Approved');
            $('#formAmountApproved').removeClass('vRequired');
            $('#formAmountApproved').prop('readOnly',true);
            $('#formAmountApproved').val('');
            $('.ApprovalBasedHidden').show();            
            
            var field = $("#formDeclarationStatus");
            
            //field.prop('disabled', false).find('option').remove();
            field.find('option').remove();
            
            statusArray = ['Applied', 'Approved', 'Rejected'];            
            
            field.append( "<option value=''>-- Select --</option>");
                
            for (var x in statusArray)
            {
                field.append("<option value='" + statusArray[x] + "'>" + statusArray[x] + "</option>");
            }
            
            field.select2( 'val', '');
            $('#formInvestmentAgeGroup').removeClass('vRequired');
            $('.formInvestmentAgeGroupPanel').hide();
        }
        
        $('#formComment').val('');
        
        $('#taxDeclarationFileSize').val(0);
        
        isDirtyFormTaxDelcaration = false;
    }
    
    /** Function To Show And Hide Action Buttons  For All the Forms **/ 
    function fnActionButtons (action) {
        if (action)
        {
            var record = tableTaxDeclaration.fnGetData (fnGetSelected (tableTaxDeclaration)[0]);
            var assessmentBasedEdit = $('#assessmentBasedEdit').val();
            var logId = record['Log_Id'];
            var approverId = record['Approver_Id'];
            
          
            fnGridButtons ($('#viewTaxDeclaration'), true);
                    
            /** Check Edit Permission **/
            if(((logId=== record['Added_By'] && record['isEmpEdit'] > 0 && record['Update'] == 1) ||			
			(logId=== record['Employee_Id'] && record['isEmpEdit'] > 0 && record['Update'] == 1) ||
			(logId=== approverId && record['isApprEdit'] > 0 && record['Update'] == 1) ||
            $('#IsEmpAdmin').val() === 'admin') && assessmentBasedEdit == record.Assessment_Year)
            {
                fnGridButtons ($('#editTaxDeclaration'), true);
                $('#editContextTaxDeclaration').parent().show();                
            }
            else{                
                fnGridButtons ($('#editTaxDeclaration'), false);
                $('#editContextTaxDeclaration').parent().hide();
            }
                        
            /** Check Status Update Permission **/
            if(((logId === approverId  ||  $('#IsEmpAdmin').val() === 'admin')  && record['isApprEdit'] > 0
                && assessmentBasedEdit == record.Assessment_Year))
            {
                $('#statusUpdateContextDeclaration').parent().show();
                fnGridButtons ($('#statusApprovalDeclaration'), true);
            }
            else
            {
                $('#statusUpdateContextDeclaration').parent().hide();
                fnGridButtons ($('#statusApprovalDeclaration'), false);   
            }
            
            /** Check History Permission **/
            if($.inArray(record.Audit, [null, undefined]) == -1)
            {
                $('#historyContextTaxDeclaration').parent().show();
                fnGridButtons ($('#historyTaxDeclaration'), true);
            }
            else
            {
                $('#historyContextTaxDeclaration').parent().hide();
                fnGridButtons ($('#historyTaxDeclaration'), false);
            }
            
            if (record.Comment > 0)
            {
                $('#commentContextTaxDeclaration').parent().show();
                fnGridButtons ($('#commentTaxDeclaration'), true);
            }
            else
            {
                $('#commentContextTaxDeclaration').parent().hide();
                fnGridButtons ($('#commentTaxDeclaration'), false);
            }
            
        }
        else
        {            
            fnGridButtons ($('#viewTaxDeclaration, #editTaxDeclaration,#statusApprovalDeclaration,#historyTaxDeclaration,#commentTaxDeclaration'), false);
        }     
    }
   
   
   
   
   /***************************************** HRA Declarations *****************************************/
    var isDirtyFormHraDeclaration = false;
    
    var lPanError = function()
    {
       $('#formHraLandLordPAN').val($('#formHraLandLordPAN').val().toUpperCase());
       if(!(/^[0-9a-zA-Z ]+$/.test($('#formHraLandLordPAN').val())))
           return 'Only Alphanumeric characters are allowed';
       else if (!(/^[A-Z]{5}\d{4}[A-Z]{1}$/.test($('#formHraLandLordPAN').val())))
       {
           return 'Invalid PAN Number';
       }
    };
   
    //Validation for PAN No
    $.validator.addMethod('validPAN', function (value) {
        if ($.inArray(value, ['', null]) === -1)
            return /^[A-Z]{5}\d{4}[A-Z]{1}$/.test(value);
        
        return true;
    }, lPanError);    
    
    /** Create Tax Declaration Grid**/ 
    var tableHraDeclaration = $('#tableHraDeclaration').dataTable({
        "lengthMenu"     : [ 5, 10, 25, 50, 100 ], 
        "iDisplayLength" : 10,
        "bDestroy"       : true,
        "bAutoWidth"     : false,
        "bServerSide"    : true,
        "bDeferRender"   : true,
        "sServerMethod"  : "POST", 
        "sAjaxSource"    : pageUrl() + "payroll/tax-declarations/list-hra-declaration/prerequisiteData/" +prerequisiteData,
        "sAjaxDataProp"  : "aaData",
        "aaSorting"      : [0,'asc'],
        "aoColumnDefs"   : [{"targets": 0, "orderable": false},
                            { "sClass" : "visible-xs visible-sm  hidden-md hidden-lg", "aTargets" : [0] },
                            { "sClass" : "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets" : [1] },
                            { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [4] }],
        "fnRowCallback"  : function ( nRow, aData, iDisplayIndex ) {
            if (aData['Employee_Id'] != aData['Added_By'] && aData['Employee_Id'] == aData['Log_Id']) {
                $(nRow).addClass('addedBy');
            }
        },
        "fnCreatedRow": function( nRow, aData, iDataIndex ) {
            $(nRow).attr({"data-toggle":"context", "data-target":"#hra-declaration-context-menu" });
        },
        "aoColumns"      :[
                           {
            "mData" : function (row, type, set) {
                return '<i class="fa fa-plus-square-o"></i>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                return '<div >'+ row['User_Defined_EmpId'] +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                return '<div >'+ row['Employee_Name'] +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                return '<div class="text-center" >'+ row['Assessment_Year'] +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                return '<div class="text-center" >'+ fnCheckNull(row['Total_Amount']) +'</div>';
            }
        }]
    });
    
    
    //On + icon click in mobile & tablet view
    $(document).on('click', '#tableHraDeclaration i', function () {
        var nTr = $(this).parents('tr')[0];
        
        fnFilterClose ('HraDeclaration');
        
        if ( tableHraDeclaration.fnIsOpen(nTr) )
        {
            /* This row is already open - close it */
            $(this).removeClass().addClass('fa fa-plus-square-o');
            tableHraDeclaration.fnClose(nTr);
        }
        else
        {
            var record = tableHraDeclaration.fnGetData( nTr );
            var nRow =  $('#tableHraDeclaration thead tr')[0];
            
            /* Open this row */
            $(this).removeClass().addClass('fa fa-minus-square-o');
            
            valueArray = [];
            headerArray=[];
        
            valueArray.Value_One = record.Total_Amount;
            
            //get grid headers
            gridHeader(nRow.cells);
            
            tableHraDeclaration.fnOpen(nTr, fnDeviceColumnDetails(headerArray,valueArray), 'details hidden-lg hidden-md');
        }
    });
    
    /*  Add event listener for select and unselect details  */
    $(document).on('click contextmenu', '#tableHraDeclaration tbody td div', function () {
        fnFilterClose ('HraDeclaration');
        
        var selectRow = $(this).parent().parent();
        
        tableHraDeclaration.$('tr.row_selected').removeClass('row_selected');
        
        if (!selectRow.hasClass('row_selected'))
        {
            selectRow.addClass('row_selected');
            
            fnHraActionButtons (true);
        }
        else
        {
            fnHraActionButtons (false);
        }
    });
    
    //** Refresh The Hra Declaration Grid**/ 
    $('#gridPanelHraDeclaration .panel-reload').on('click', function () {
        fnRefreshTable (tableHraDeclaration);
    });
    
    /**
     *  close filter form and clear enabled buttons based on selection record
     *  while search all, sorting, pagination, redraw events
     *  it will work in search.dt order.dt page.dt, length.dt those events
    */
    $('#tableHraDeclaration').on( 'draw.dt', function () {
        fnHraActionButtons (false);

        fnFilterClose ('HraDeclaration');
    }); 
   
   /** View Hra Declaration Form **/
    $('#viewHraDeclaration,#viewContextHraDeclaration').on('click', function () {
        var selectedRow = fnGetSelected (tableHraDeclaration);
        
        fnFilterClose ('HraDeclaration');
       
        if (selectedRow.length)
        {
            var record = tableHraDeclaration.fnGetData(selectedRow[0]);
            
            if (record.Declaration_Id > 0)
            {            
                $('#modalFormHraDeclaration .modal-title').html("<strong>View <strong>" + $('#lblFormNameB').html());
                
                $('#viewHEmployeeName').text(record.Employee_Name);
                $('#viewHAssessmentYear').text(record.Assessment_Year);
                $('#viewHForwardedTo').text(fnCheckNull(record.Forward_Name));
                $('#viewRentDuration').text(fnCheckNull (record.Rent_Duration));
                
                /**
                 *  Prefill addional information values based on record with form name
                */
                fnPreFillAdditionalPanel ('HraDeclaration', record);
                             
                tableHRADeclarationSubGridView.fnReloadAjax( pageUrl () + "payroll/tax-declarations/list-landlord-details/Declaration_Id/"+record.Declaration_Id);
             
                $('#modalFormHraDeclaration').modal('toggle');
                $('#editFormHraDeclaration,#addHraLandlordDetails,#hraDeclarationSubGrid,#formResetHraStatus,#formStatusSubmitHraDeclarationMail,#revertHraDeclaration').hide(); //, .modal-footer
                $('#formActionTaxDeclaration,#formSubmitStatusHraDeclaration,#formHraCloseSubmit,#formHraSubmitProof, #formSubmitHraDeclaration, #formResetHraDeclaration').hide();
                $('#viewFormHraDeclaration').show();
            
                var logId = record['Log_Id'],
                approverId = record['Approver_Id'],
                assessmentBasedHraEdit = $('#hraAssessmentBasedEdit').val();
                
                /** Check Edit Permission **/
                if(((logId=== record['Added_By'] && record['isEmpEdit'] > 0 && record['Update'] == 1) ||			
                (logId=== record['Employee_Id'] && record['isEmpEdit'] > 0 && record['Update'] == 1) ||
                (logId=== approverId && record['isApprEdit'] > 0 && record['Update'] == 1) || (record['Admin'] == 'admin')
                ) && assessmentBasedHraEdit == record.Assessment_Year)
                {
                    $('#editInViewHraDeclaration').show();
                }
                else{
                    $('#editInViewHraDeclaration').hide();
                }
                
                $('#tableHRADeclarationSubGridView thead tr th').removeClass('sorting_asc');
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+ $('#lblFormNameB').html(), type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+ $('#lblFormNameB').html(), type : 'info' });
        }
    });
    
    /** Add HRA Declaration **/
    $('#addHraDeclaration').on('click', function (e) {
        tableHraDeclaration.$('tr.row_selected').removeClass('row_selected');
        
        fnHraActionButtons (false);
        
        fnFilterClose ('HraDeclaration');
        
        $('#s2id_formHraEmployeeName, #s2id_formHraAssessmentYear').removeClass('form-error');
        
        $('#editFormHraDeclaration').validate().resetForm();        
        
        $('#modalFormHraDeclaration .modal-title').html("<strong>Add </strong> "+ $('#lblFormNameB').html());
        
        /** Reset the values **/
        $('#HDeclaration_Id').val(0);
        $('#HLine_Item_Id').val(0);
        
        var employeeField = $('#formHraEmployeeName');

        if (!$("#onHRAAddCollapse").hasClass('collapsed'))
        {
            $('#hraDeclarationSummaryPanel').trigger('click');
        }

        $.ajax ({
            type     : 'POST',
            dataType : 'json',
            async    : false,
            url      : pageUrl () +'default/employee-info/listsalary-employee',
            data     : {
                _fId   : 'HRA Declarations',
            },
            success  : function (result)
            {
                if (isJson (result))
                {
                    if (result.length > 0)
                    {
                        employeeArray = '<option value="">--Select--</option>';
                        
                        employeeField.find('option').remove();
                        
                        var empDepartment = [];
	
                        for (var row in result) {
                           if($.inArray(result[row]['Department_Name'],empDepartment) == -1)
                            {
                                empDepartment.push(result[row]['Department_Name']);
                            }
                        }
                        
                        for(var y in empDepartment)
                        {
                            employeeArray += '<optgroup label="'+empDepartment[y]+'">';
                            
                            for(var x in result)
                            {
                                if(result[x]['Department_Name'] == empDepartment[y])
                                {
                                    employeeArray += '<option value='+result[x]['Employee_Id']+'>'+result[x]['Employee_Name']+'</option>'
                                }
                            }
                            employeeArray += '</optgroup>';
                        }
                    
                        employeeField.append(employeeArray);
                        
                        employeeField.select2('val', '');
                    }
                    else
                    {
                        jAlert ({ msg : 'No employees found', type : 'info' });
                    }
                }
                else
                {
                    sessionExpired ();
                }
            }
        });
        
        
        fnPreFillFormValuesHraDeclaration('', 0);
        
        employeeField.prop('readOnly', false);
        employeeField.select2('val', $('#logemployeeId').val());       
        
        $("#s2id_formHraAssessmentYear").select2('val', $('#assessmentBasedEdit').val());
        
        /** To prefill the log employee and subgrid **/        
        fnprefillHraEmployee($('#s2id_formHraEmployeeName').select2('val'));                
        
        $('#hraDeclarationFileSize').val(0);
        
        $('#s2id_formHraDeclarationStatus').select2('val', 'Applied');

        $("#formSubmitHraDeclaration").html('<i class="mdi-content-send"></i> Add'); // reset the second panel button name to add
            
        $('#tableHraDeclarationSubGrid').parent().removeClass('force-table-responsive');
        $('#editInViewHraDeclaration, #viewFormHraDeclaration,#formResetHraStatus,#formStatusSubmitHraDeclarationMail,#formSubmitStatusHraDeclaration,#revertHraDeclaration').hide();
        $('#editFormHraDeclaration, #hraDeclarationSubGrid, #formSubmitHraDeclaration, #formResetHraDeclaration, #addHraLandlordDetails,#formHraCloseSubmit,#formHraSubmitProof').show();
    });
    
    $('#editHraDeclaration, #editContextHraDeclaration, #editInViewHraDeclaration').on('click', function () {
        var selectedRow = fnGetSelected (tableHraDeclaration );
        
        fnFilterClose ('HraDeclaration');
        
        if (selectedRow.length)
        {
            var buttonId = $(this).prop('id');
            
            var record        = tableHraDeclaration.fnGetData (selectedRow[0]);
                declarationId = record.Declaration_Id;
                
            $('#HDeclaration_Id').val(declarationId);
            $('#HLine_Item_Id').val(0);
            
            if (declarationId > 0 && !isNaN(declarationId))
            {
                // force-table-responsive class is removed because when we open form scroll bar is set
                // $('#tableHraDeclarationSubGrid').parent().removeClass('force-table-responsive');
                
                // Lock is not set becoz no lock flag to main grid                
                $('#modalFormHraDeclaration .modal-title').html("<strong>Edit </strong> "+ $('#lblFormNameB').html());                
                
                $('#formHraLandLordName,#formHraLandLordAddress,#formHraLandLordPAN,#formHraRentalAmount,#formHraRentMonths').prop('readOnly', false);
                $('#formHraDeclarationStatus,#formHraAmountApproved,#formHraEmployeeName,#formHraAssessmentYear').prop('readOnly', false);
                
                fnHraStatusFieldUpdate($("#formHraDeclarationStatus"),0);
                                   
                $('#HDeclaration_Id').val(record.Declaration_Id);
                // $("#s2id_formHraEmployeeName").select2('val', record.Employee_Id);

                
                var employeeField = $('#formHraEmployeeName');
                employeeField.find('option').remove();        
                employeeField.append('<option value="'+ record.Employee_Id +'">'+ record.Employee_Name +'</option>');
                employeeField.select2('val', record.Employee_Id);
                employeeField.prop('readonly', true);            

                $("#s2id_formHraAssessmentYear").select2('val', record.Assessment_Year);
                $("#s2id_formHraDeclarationStatus").select2('val', 'Applied');
                $('#formHraAssessmentYear').prop('readOnly',true);                
                
                $('.hraApprovalBasedHidden').hide();
                $('#s2id_formHraRentMonths').select2('val','');
                $('#formHraRentalAmount,#formHraLandLordName,#formHraLandLordAddress,#formHraLandLordPAN,#HformComment').val('');
                
                fnPrefillHraRentalMonths(record.Employee_Id);                                
                
                $('#HraAttachmentsList').html("");
                $('#buttonUploadFile').show();//Show the add document button
                       
                fnHraDeclarationSubGrid(record.Declaration_Id,0);                                
                    
                $( "#editFormHraDeclaration").validate().resetForm();
        
                isDirtyFormHraDeclaration = false;
                
                /** update the total allowed file size **/
                //getUploadedTotalFileSize('HRA Declarations');

                $("#formSubmitHraDeclaration").html('<i class="mdi-content-send"></i> Add'); // reset the second panel button name to add
                
                $('#editInViewHraDeclaration, #viewFormHraDeclaration,#formResetHraStatus,#formStatusSubmitHraDeclarationMail,#formSubmitStatusHraDeclaration,#revertHraDeclaration').hide();                
                $('#editFormHraDeclaration, #hraDeclarationSubGrid,#formSubmitHraDeclaration,#formResetHraDeclaration, #addHraLandlordDetails,#formHraCloseSubmit,#formHraSubmitProof').show();
                
                if (buttonId != 'editInViewHraDeclaration')
                    $('#modalFormHraDeclaration').modal('toggle');
                    
                if ($("#onHRAAddCollapse").hasClass('collapsed'))
                {
                   $( "#onHRAAddCollapse" ).trigger( "click" );
                }
                fnHralockConfiguration();
                getEmployeeTaxRegime($('#s2id_formHraEmployeeName').select2('val'),'#editFormHraDeclaration','#modalFormHraDeclaration');
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+ $('#lblFormNameB').html(), type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+ $('#lblFormNameB').html(), type : 'info' });
        }
    });
    
    // On form field change
    $('#editFormHraDeclaration').on('change', function() {        
        isDirtyFormHraDeclaration = true;
    });
    
    //Employee name change event
    $('#formHraEmployeeName').on('change', function() {
        /** Reset the hra declaration when the employee name is changed or selected as empty */
        fnPreFillFormValuesHraDeclaration('',1);

        /** In add form, while change employee name, if hra declaration exists, hra declaration subgrid has to be loaded **/
        if($(this).val() > 0){
            fnprefillHraEmployee ($(this).val());
        }
    });
    
    /** Declaring the new array **/
    var aftDelNewHraFiles =[], bucketUploadSuccessFiles = [], bucketUploadFailureFiles = [], newHraFiles = [], oldHraFiles = [];
    
    /** change event while select the file **/
    $('#uploadHraFiles').on( 'change', function(){
        var files = $("#uploadHraFiles")[0].files;
        
        if(files != '' && files.length > 0){
            
            isDirtyFormHraDeclaration = true;
                
            for (var i = 0; i < files.length; i++)
            {
                var format = fncheckFileFormat($(this).val().split('.').pop().toLowerCase());
                
                if (format)
                {
                    /** push the files into newHrafiles **/
                    newHraFiles.push(files[i]);
                    fileSize = parseFloat(files[i].size) / 1000;                    
                    
                    /** If File name length is greater than 10, then first 10 characters of file name only will be displayed and dot will
                    be concat at the end of the file name **/
                    displayFileName = files[i].name;
                    if(displayFileName.length > 10)
                    {
                        displayFileName = displayFileName.substring(0,10);
                        displayFileName = displayFileName+'...';
                    }
                    else
                    {
                        displayFileName = displayFileName;
                    }
                    
                    
                    $('#HraAttachmentsList').append('<div class="hraAttachmentDiv" style="max-width:100%;margin-bottom:15px;background:#dfdfdf;color:white;padding:5px;border-radius:3px;">'+
                                                    '<div style="text-overflow:ellipsis;display:inline-block;vertical-align:bottom;overflow:hidden;color: #555;"' +
                                                    '"max-width:75%;white-space:nowrap;cursor:pointer;">' + displayFileName
                                                    + '<div style="display:inline-block;cursor:pointer;color: #555;">' + '( ' + fileSize.toFixed(2) +
                                                    'K ) </div>'
                                                    +'</div><button type="button" style="margin-top:-2px;" id="'+ files[i].name +'" class="close attachmentdelHra removeHraFile" aria-hidden='+true+
                                                    '> <i class="fa fa-close" style="color:black;font-size:20px"></i>' +
                                                    '</button> </div>');
                    
                }
                else
                {
                    jAlert ({ panel : $('#editFormHraDeclaration'), msg : 'This format is not supported', type : 'warning' });
                }
            }
            
        }
    });    
    
    /** download the file from s3 bucket **/
    $(document).on('click', '.hraFileDownload', function (e) {        
        bucketFileName = $(this).prop('id');
        if(bucketFileName != undefined){
            //e.preventDefault();
            $("#"+bucketFileName)[0].click();
        }
        
    });
    
    /** delete the file from s3 bucket and remove that file name in table **/
    $(document).on('click', '.attachmentdelHra', function () {
        var attachment = $(this);        
        var bucketFileName = attachment.prop('id');
        
        /** If already uploaded files are deleted means, request will be passed to remove that file. Files will be uploaded only when submit **/
        if ($.inArray(bucketFileName, oldHraFiles) !== -1)
        {

            var res = fndeleteS3Document(domainName+"/"+orgCode+"/"+"Hra Declarations/"+bucketFileName,'bucketName');
            if(res == 'Document deleted'){
                attachment.closest('.hraAttachmentDiv').remove();
                
                $.ajax ({
                type        : 'POST',
                dataType    : 'json',
                url      : pageUrl () +'payroll/tax-declarations/remove-uploaded-files/line_Item_Id/'+$('#HLine_Item_Id').val(),
                data     : {
                        _declarationFileName : bucketFileName,
                        _formName : 'HRA Declarations'
                },
                success     : function(result)
                {
                    if (isJson (result))
                    {
                        if (result.success)
                        {
                            /** update the total allowed file size **/
                            //getUploadedTotalFileSize('HRA Declarations');
                            
                            jAlert ({ panel : $('#editFormHraDeclaration'), msg : result.msg, type : result.type });
                        }
                        else{
                            jAlert ({ panel : $('#editFormHraDeclaration'), msg : 'Unable to delete file', type : 'warning' });
                        }
                        
                        //fnRefreshTable(tableHraDeclarationSubGrid);
                    }
                    else
                    {
                        sessionExpired ();
                    }
                }
                });
                    
            }
            else {
                jAlert ({ panel : $('#editFormHraDeclaration'), msg : 'Unable to delete file', type : 'warning' });
            }
        }
        else{
            /** remove only the selected file div and remove the deleted files by creating a new array **/
            attachment.closest('.hraAttachmentDiv').remove();            
            
            /** Removing the deleted files in uploaded file list in array **/
            for(var r=0;r<newHraFiles.length;r++){
               if(newHraFiles[r].name == bucketFileName){
                  newHraFiles[r] = '';
               }
               else{
                  
               }
            }
            
        }
    });
   
   
   /** check the selected File Format **/
    function fncheckFileFormat(value){
        if($.inArray(value, ['png','jpg','jpeg','doc','txt','pdf','docx','csv','xls','xlsx','tif','tiff','bmp']) == -1)
        {
            return false;
        }
        else{
            return true;            
        }
    }
    
    $('#formSubmitHraDeclaration,#formHraSubmitProof').on('click', function (e) {
        $('#s2id_formHraEmployeeName, #s2id_formHraRentMonths, #s2id_formHraDeclarationStatus, #s2id_formHraAssessmentYear').removeClass('form-error');
        
        var l = Ladda.create(this),
            button = $(this).prop('id');            
        
        l.start();
        
        /** submit proof will be done comman to all sub records. If the form is dirty, they are updating some rental/ declaration details.
        So warning will be shown **/
        if(isDirtyFormHraDeclaration && button=='formHraSubmitProof'){
            jAlert ({ panel : $('#editFormHraDeclaration'), msg : 'Kindly update rental details', type : 'warning' });
            fnModalScrollTop('modalFormHraDeclaration');
            l.stop();
        }
        else if(!isDirtyFormHraDeclaration && button=='formHraSubmitProof'){
            fnSubmithra(button);
        }
        else 
        {
            if( isDirtyFormHraDeclaration )
            {
                var formValid = ($('#formHraEmployeeName').valid() && $('#formHraAssessmentYear').valid() && $('#HformComment').valid()
                                && $('#formHraRentMonths').valid() && $('#formHraRentalAmount').valid() && $('#formHraAmountApproved').valid()
                                 && $('#formHraLandLordName').valid() && $('#formHraLandLordAddress').valid() && $('#formHraDeclarationStatus').valid());
                
                if (formValid)
                {
                    totalUploadFileSize = uploadSuccessCount = hraDeclarationFileSize = 0;
                    bucketUploadSuccessFiles = []; bucketUploadFailureFiles = []; uploadFiles = ''; bucketFileNameArr = [] ;aftDelNewHraFiles = [];
                    
                    if(!isDirtyFormHraDeclaration && button=='formHraSubmitProof'){
                        $("#editFormHraDeclaration").validate().resetForm();
                    }
                    
                    if(newHraFiles.length > 0){
                        for(var r=0;r<newHraFiles.length;r++){
                            if(newHraFiles[r] != ''){
                               aftDelNewHraFiles.push(newHraFiles[r]);
                            }
                        }    
                    }
                    else{
                       aftDelNewHraFiles= ''; 
                    }
                    
                    /** File upload - If old files(already uploaded files) are only exists no need to made a request for upload **/
                    if((aftDelNewHraFiles.length > 0 && button !='formHraSubmitProof')){
                      
                            /** If some of the newly added files are deleted means, remaining added files will be updated in
                            aftDelNewHraFiles array. If it not empty means it has to be taken **/                                
                            uploadFiles = aftDelNewHraFiles;                                
                            
                            TotalAllowedFileSize = 0;
                            
                            /** For each file, allowed file size is 3MB. Here file size will be in KB only. 3MB equals 3000KB.
                            By using the each files length we are calculating allowed file size **/                            
                            for(var nt=0;nt<uploadFiles.length;nt++){                                
                                totalUploadFileSize = parseFloat(uploadFiles[nt].size) / 1000;
                                
                                if(totalUploadFileSize > 3000)
                                {
                                    TotalAllowedFileSize += 1;
                                }                                
                            }
                            
                            if(TotalAllowedFileSize > 0 /*&& TotalAllowedFileSize != 'NAN'*/){
                                jAlert({ panel : $('#editFormHraDeclaration'),msg : 'Each file size should be less than or equal to 3MB', type : 'info' });
                            }
                            else{
                                for(var u=0;u<uploadFiles.length;u++){
                                    var orgFileName = bucketFileName = file = dformat = '',uniqueId = 0,
                                    d = new Date();
                                    /** date is appended to file name to differentiate the same file names selected from
                                    different folders**/
                                    dformat = [d.getFullYear(),
                                               d.getMonth()+1,
                                                d.getDate()].join('-')+'.'+
                                               [d.getHours(),
                                                d.getMinutes(),
                                                d.getSeconds()].join(':'),                                
                                    file    = uploadFiles[u];
                                    
                                    uniqueId = parseInt(u)+parseInt(1);
                                    
                                    /** File name format: EmpId_LandLordname_AssessmentYear_Filename_date_uniqueid . This is a wrong format as
                                     * date and unique id was appended after filename. So While download error will occur.
                                     * The correct file format is EmpId_LandLordname_AssessmentYear_date_uniqueid_Filename. **/
                                    
                                    /** old file format **/
                                    //bucketFileName   = $('#formHraEmployeeName').select2('val')+'?'+$('#formHraLandLordName').val()+'?'+$('#formHraAssessmentYear').select2('val')+'?'+file.name+'?'+dformat+'?'+uniqueId;
                                    
                                    /** new file format **/
                                    bucketFileName   = $('#formHraEmployeeName').select2('val')+'?'+$('#formHraLandLordName').val()+'?'+$('#formHraAssessmentYear').select2('val')+'?'+dformat+'?'+uniqueId+'?'+file.name;
                                    
                                    bucketFileNameArr.push(bucketFileName);
                                    
                                    if (uploadFiles[u]) {
                                    var sUrl = fngetSignedPutUrl(domainName+"/"+orgCode+"/"+"Hra Declarations/"+bucketFileName,'bucketName');
                                    deletePhpActionHeaders();
                                    $.ajax({
                                        url: sUrl,
                                        type: 'PUT',
                                        contentType: file.type,
                                        processData: false,
                                        data: file,
                                      }).done(function(err,res ){
                                          if(res == "success") {
                                               /** push the uploaded files to update in table **/
                                               bucketUploadSuccessFiles.push({Name:bucketFileNameArr[uploadSuccessCount],
                                                Size:parseFloat(uploadFiles[uploadSuccessCount].size) / 1000})
                                                uploadSuccessCount += 1;
                                          } else {
                                            bucketUploadFailureFiles.push({Name:bucketFileNameArr[uploadSuccessCount],
                                                Size:parseFloat(uploadFiles[uploadSuccessCount].size) / 1000})                                                
                                                uploadSuccessCount += 1; 
                                          }
                                          if(uploadSuccessCount == (uploadFiles.length)){
                                                
                                            if(bucketUploadFailureFiles != '' && bucketUploadFailureFiles.length > 0){                                                        
                                                /** If some of the files were not uploaded, message will be shown, and then it will
                                                 be updated **/
                                                jAlert({ panel : $('#editFormHraDeclaration'),msg : 'Few files were not uploaded', type : 'info' });
                                            }
                                            
                                            fnSubmithra(button);
                                        }
                                    })
                                    setPhpActionHeaders();
                                    }
                                }
                            }
                    }
                    else{                        
                        //bucketUploadSuccessFiles = oldHraFiles;
                        fnSubmithra(button);
                    }                       
                    
                    e.preventDefault();
                }
                else{                    
                    $('#formHraEmployeeName').valid();$('#formHraDeclarationStatus').valid();
                    $('#formHraAssessmentYear').valid(); $('#HformComment').valid();
                    $('#formHraRentMonths').valid(); $('#formHraRentalAmount').valid(); $('#formHraAmountApproved').valid();
                    $('#formHraLandLordName').valid(); $('#formHraLandLordAddress').valid();
                    
                    fnModalScrollTop('modalFormHraDeclaration');                        
                }                    
            }
            else{
                jAlert ({ panel : $('#editFormHraDeclaration'), msg : 'Form has no changes', type : 'info' });
            }
            
            l.stop();
        }
       
    });
    
    /** Reset The Values In Hra Declaration Form **/
    $('#formResetHraDeclaration').on('click', function (e) {
        $('#s2id_formHraEmployeeName, #s2id_formHraRentMonths, #s2id_formHraDeclarationStatus, #s2id_formHraAssessmentYear').removeClass('form-error');
        
        var l = Ladda.create(this);
        
        l.start();

        if ($('#HLine_Item_Id').val() > 0)
        {
            var subGridRecord = tableHraDeclarationSubGrid.fnGetData (tableHraDeclarationSubGrid.$('tr.hraDeclaration_editing_row'));
            
            fnPreFillFormValuesHraDeclaration (subGridRecord,1);
        }
        else
        {
            e.preventDefault();
            fnPreFillFormValuesHraDeclaration('',1);            
        }       
            
        l.stop();
    });
    
    /** HRA Declaration SubGrid In View Modal
      *  Initialse DataTables, with no sorting on the 'details' column
    **/
    tableHRADeclarationSubGridView = $('#tableHRADeclarationSubGridView').dataTable({
        "iDisplayLength" : 10,
        "lengthMenu"     : [ 5, 10, 25, 50, 100 ], 
        "bDestroy"       : true,
        "bAutoWidth"     : false,
        "bServerSide"    : true,
        "bDeferRender"   : true,
        "sServerMethod"  : 'POST',
        "sAjaxSource"    : pageUrl () + 'payroll/tax-declarations/list-landlord-details/',
        "sAjaxDataProp"  : 'aaData',
        "info"           : false,
        "bPaginate"      : false, // Remove pagination
        "bFilter"        : false,
        "aaSorting"      : [],
        "aoColumnDefs"   : [{ 'bSortable': false, 'aTargets': ['_all'] },
                            { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [3] },
                            { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [4] }
                            ],
        "aoColumns"      : [{
            "mData" : function (row, type, set) {
                return '<i class="fa fa-plus-square-o"></i>';
            }
        },
        {
            "mData" : function (row, type, set) {
                return '<div>'+ row['Landlord_Name'] +'</div>';
            }
        },
        {
            "mData" : function (row, type, set) {
                return '<div class="text-center" >'+ row['View_Month_Name'] +'</div>';
            }
        },
        {
            "mData" : function (row, type, set) {
                return '<div class="text-center" >'+ row['Total_Rental_Amount'] +'</div>';
            }
        },
        {
            "mData" : function (row, type, set) {
                return '<div>'+ fnCheckNull(row['Approval_Status']) +'</div>';
            }
        }]
    });
    
    // HRA Declaration view - On + button click - get more information by form expand
    $(document).on('click', '#tableHRADeclarationSubGridView i', function () {
        var nTr = $(this).parents('tr')[0];
        
        if ( tableHRADeclarationSubGridView.fnIsOpen(nTr) )
        {
            /* This row is already open - close it */
            $(this).removeClass().addClass('fa fa-plus-square-o');
            
            tableHRADeclarationSubGridView.fnClose(nTr);
        }
        else
        {
            /* Open this row */
            $(this).removeClass().addClass('fa fa-minus-square-o');
            
            tableHRADeclarationSubGridView.fnOpen(nTr, fnShowHiddenHRADeclarationDetails(nTr), 'details');
        }
    });
    
    
    /* Formating function for row details */
    function fnShowHiddenHRADeclarationDetails ( nTr ) {
        var aData = tableHRADeclarationSubGridView.fnGetData( nTr );
            filesArr= aData['Declaration_Path'];
            sOut = '';
            
        sOut += '<div class="row hidden-md hidden-lg">'+
                    '<div class="col-xs-4">Total Rental Amount</div>'+
                    '<div class="col-xs-1"> : </div>'+
                    '<div class="col-xs-6">'+ fnCheckNull (aData['Total_Rental_Amount']) +'</div>'+
                '</div>'+
                '<div class="row hidden-md hidden-lg">'+
                    '<div class="col-xs-4">Status</div>'+
                    '<div class="col-xs-1"> : </div>'+
                    '<div class="col-xs-6">'+ fnCheckNull (aData['Approval_Status']) +'</div>'+
                '</div>'+        
                '<div class="row">'+
                    '<div class="col-xs-4">Landlord Address</div>'+
                    '<div class="col-xs-1"> : </div>'+
                    '<div class="col-xs-6">'+ fnCheckNull (aData['Landlord_Address']) +'</div>'+
                '</div>'+
                '<div class="row">'+
                    '<div class="col-xs-4">Landlord PAN</div>'+
                    '<div class="col-xs-1"> : </div>'+
                    '<div class="col-xs-6">'+ fnCheckNull (aData['Landlord_PAN']) +'</div>'+
                '</div>';
                
                for (var i = 0; i < filesArr.length; i++)
                {
                    sOut +='<div class="row">';
                    if(i == 0){                
                        sOut +='<div class="col-xs-4">Documents </div>'
                            +'<div class="col-xs-1"> : </div>';
                    }
                    else{
                        sOut +='<div class="col-xs-5"></div>';
                    }
                    
                        displayFileName = filesArr[i]['File_Name'].split('?')[5];
                        
                        /** get the display File name for the old format(EmpId_LandLordname_AssessmentYear_Filename_date_uniqueid)
                        and new format(EmpId_LandLordname_AssessmentYear_date_uniqueid_Filename) **/
                        if(isNaN(Date.parse(displayFileName)))
                        {                            
                            displayFileName = filesArr[i]['File_Name'].split('?')[5];
                        }
                        else
                        {                            
                            displayFileName = filesArr[i]['File_Name'].split('?')[3];
                        }
                        
                        /** If File name length is greater than 15, then first 15 characters of file name only will be displayed and dot will
                        be concat at the end of the file name **/                    
                        if(displayFileName.length > 15)
                        {
                            fileNameSplit = displayFileName.split('.');
                            getFileType = fileNameSplit.pop();
                            
                            displayFileName = displayFileName.substring(0,15);
                            displayFileName = displayFileName+'...';
                        }
                        else
                        {
                            displayFileName = displayFileName;
                        }
                           
                        /** file link **/
                        var sUrl = fngetSignedUrl(domainName+"/"+orgCode+"/"+"Hra Declarations/"+filesArr[i]['File_Name'],'bucketName');
                            
                        sOut +='<div class="col-xs-6" id = "HraAttachmentsViewList">'+
                        '<a target="_blank" style="padding:0px;cursor: pointer;text-decoration:none;" title="'+filesArr[i]['File_Name'].split('?')[3]+'" id="hradownloadlink'+i+'" href="' + sUrl + '" class="hraFileDownload">'
                            + displayFileName +'</a> </div></div>';
                    
                }
                
                sOut += '<div class="row">'+
                    '<div class="col-xs-4">Added On</div>'+
                    '<div class="col-xs-1"> : </div>'+
                    '<div class="col-xs-6">'+ fnCheckNull (aData['Added_On']) +'</div>'+
                '</div>'+
                '<div class="row">'+
                    '<div class="col-xs-4">Added By</div>'+
                    '<div class="col-xs-1"> : </div>'+
                    '<div class="col-xs-6">'+ fnCheckNull (aData['Added_By_Name']) +'</div>'+
                '</div>';
        if (aData['Updated_By_Name'] != null && aData['Updated_On'] != null)
        {
            sOut += '<div class="row">'+
                        '<div class="col-xs-4">Updated On</div>'+
                        '<div class="col-xs-1"> : </div>'+
                        '<div class="col-xs-6">'+ fnCheckNull (aData['Updated_On']) +'</div>'+
                    '</div>'+
                    '<div class="row">'+
                        '<div class="col-xs-4">Updated By</div>'+
                        '<div class="col-xs-1"> : </div>'+
                        '<div class="col-xs-6">'+ fnCheckNull (aData['Updated_By_Name']) +'</div>'+
                    '</div>';
        }
        if (aData['Approved_By_Name'] != null && aData['Approved_On'] != null)
        {
            sOut += '<div class="row">'+
                        '<div class="col-xs-4">Approved On</div>'+
                        '<div class="col-xs-1"> : </div>'+
                        '<div class="col-xs-6">'+ fnCheckNull (aData['Approved_On']) +'</div>'+
                    '</div>'+
                    '<div class="row">'+
                        '<div class="col-xs-4">Approved By</div>'+
                        '<div class="col-xs-1"> : </div>'+
                        '<div class="col-xs-6">'+ fnCheckNull (aData['Approved_By_Name']) +'</div>'+
                    '</div>';
        }
        return sOut;
    }
    
    /** reload the hra declaration subgrid **/
    function fnHraDeclarationSubGrid(declarationId,status){        
        tableHraDeclarationSubGrid = $('#tableHraDeclarationSubGrid').dataTable({
            "iDisplayLength" : 10,
            "lengthMenu"     : [ 5, 10, 25, 50, 100 ], 
            "bDestroy"       : true,
            "bAutoWidth"     : false,
            "bServerSide"    : true,
            "bDeferRender"   : true,
            "sServerMethod"  : 'POST',
            "sAjaxSource"    : pageUrl () + 'payroll/tax-declarations/list-landlord-details/Declaration_Id/'+declarationId,
            "sAjaxDataProp"  : 'aaData',            
            "info"           : false,
            "bPaginate"      : false, // Remove pagination
            "bFilter"        : false,
            "aaSorting"      : [],
            "aoColumnDefs"   : [{"targets": 0, "orderable": false},
                            { "sClass" : "visible-xs visible-sm  hidden-md hidden-lg", "aTargets" : [0] },
                            { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [4] },
                            { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [5] }],
            "aoColumns"      : [{
                "mData" : function (row, type, set) {
                    return '<i class="fa fa-plus-square-o"></i>';
                }
            },
            {
                "mData" : function (row, type, set) {
                    /** status approval form **/
                    if(status == 1){                        
                        /** HRA declaration edit form **/
                        if (row.Approval_Status == 'Approved' || row.Approval_Status == 'Rejected') {                            
                            /** Sub grid records will not be edited in approved/rejected status. It will be edited **/
                            return '<div class="btn btn-sm btn-embossed btn-white editAlertHraDeclarationSubgrid" title="Edit" id="alertHraDeclaration_'+row['Line_Item_Id']+'"><i class="mdi-editor-mode-edit"></i></div>';
                        }
                        else{
                            return '<div class="btn btn-sm btn-embossed btn-white statusUpdateHraDeclarationSubgrid" title="Edit" id="statusHraDeclaration_'+row['Line_Item_Id']+'"><i class="mdi-editor-mode-edit"></i></div>';
                        }
                    }
                    else{                        
                        /** HRA declaration edit form **/
                        if(row.Approver_Id !=0){
                            $("#formHraSubmitProof").hide();
                        }
                        else{
                            $("#formHraSubmitProof").show();
                        }
                        
                        if (row.Approval_Status == 'Approved') {
                            /** Sub grid records will not be edited/deleted in approved status **/
                            return '<div class="btn btn-sm btn-embossed btn-white editAlertHraDeclarationSubgrid" title="Edit" id="alertHraDeclaration_'+row['Line_Item_Id']+'"><i class="mdi-editor-mode-edit"></i></div> <div class="btn btn-sm btn-embossed btn-white deleteAlertHraDeclarationDetailsSubGrid" title="Delete" id="deleteAlertHraDeclarationDetails_'+row['Line_Item_Id']+'" ><i class="mdi-action-delete"></i></div>';
                        }
                        else if(row.Approval_Status == 'Rejected'){                            
                            /** Sub grid records will not be deleted in rejected status. It will be edited **/
                            return '<div class="btn btn-sm btn-embossed btn-white editHraDeclarationSubgrid" title="Edit" id="hraDeclaration_'+row['Line_Item_Id']+'"><i class="mdi-editor-mode-edit"></i></div> <div class="btn btn-sm btn-embossed btn-white deleteAlertHraDeclarationDetailsSubGrid" title="Delete" id="deleteAlertHraDeclarationDetails_'+row['Line_Item_Id']+'" ><i class="mdi-action-delete"></i></div>';
                        }
                        else{
                            if ($('#lockSettings').val() == 'unlock')
                            {
                                return '<div class="btn btn-sm btn-embossed btn-white editHraDeclarationSubgrid" title="Edit" id="hraDeclaration_' + row['Line_Item_Id'] + '"><i class="mdi-editor-mode-edit"></i></div> <div class="btn btn-sm btn-embossed btn-white deleteHraDeclarationDetailsSubGrid" title="Delete" id="deleteHraDeclarationDetails_' + row['Line_Item_Id'] + '" ><i class="mdi-action-delete"></i></div>';
                            }
                            else
                            {
                                return '<div class="btn btn-sm btn-embossed btn-white editHraDeclarationSubgrid" title="Edit" id="hraDeclaration_' + row['Line_Item_Id'] + '"><i class="mdi-editor-mode-edit"></i></div>';
                            }
                            
                        }
                        
                    }
                    if ($('#lockSettings').val() == 'unlock') {
                    return '<div class="btn btn-sm btn-embossed btn-white editHraDeclarationSubgrid" title="Edit" id="hraDeclaration_'+row['Line_Item_Id']+'"><i class="mdi-editor-mode-edit"></i></div> <div class="btn btn-sm btn-embossed btn-white deleteHraDeclarationDetailsSubGrid" title="Delete" id="deleteHraDeclarationDetails_'+row['Line_Item_Id']+'" ><i class="mdi-action-delete"></i></div>';
                    }
                    else
                    {
                        return '<div class="btn btn-sm btn-embossed btn-white editHraDeclarationSubgrid" title="Edit" id="hraDeclaration_' + row['Line_Item_Id'] + '"><i class="mdi-editor-mode-edit"></i></div>';
                    }
                }
            },
            {
                "mData" : function (row, type, set) {
                    return '<div>'+ row['Landlord_Name'] +'</div>';
                }
            },
            {
                "mData" : function (row, type, set) {
                    return '<div class="text-center" >'+ fnCheckNull(row['View_Month_Name']) +'</div>';
                }
            },
            {
                "mData" : function (row, type, set) {
                    return '<div class="text-center" >'+ fnCheckNull(row['Total_Rental_Amount']) +'</div>';
                }
            },
            {
                "mData" : function (row, type, set) {
                    return '<div>'+ fnCheckNull(row['Approval_Status']) +'</div>';
                }
            }]
        });
    }
    
    $(document).on('click', '#revertHraDeclaration', function(){
        var selectedRow = fnGetSelected ( tableHraDeclarationSubGrid);
        
        if (selectedRow.length)
        {
            var record     = tableHraDeclarationSubGrid.fnGetData (selectedRow[0]);
            var lineItemId = record.Line_Item_Id;
            var employeeId = record.Employee_Id;
            var action     = 'hraDeclaration';
            revertDeclarationDetails(lineItemId,employeeId,action,tableHraDeclarationSubGrid,'#editFormHraDeclaration')
            
        }
    });

    //On + icon click in mobile & tablet view
    $(document).on('click', '#tableHraDeclarationSubGrid i', function () {
        var nTr = $(this).parents('tr')[0];
        
        if ( tableHraDeclarationSubGrid.fnIsOpen(nTr) )
        {
            if($(this).hasClass('fa-minus-square-o'))
            {
                /* This row is already open - close it */
                $(this).removeClass().addClass('fa fa-plus-square-o');
                tableHraDeclarationSubGrid.fnClose(nTr);
            }
        }
        else
        {
            /* Open this row */
            if($(this).hasClass('fa-plus-square-o'))
            {
                var record = tableHraDeclarationSubGrid.fnGetData( nTr );
                var nRow =  $('#tableHraDeclarationSubGrid thead tr')[0];
                
                /* Open this row */
                $(this).removeClass().addClass('fa fa-minus-square-o');
                
                valueArray = [];
                headerArray=[];
            
                valueArray.Value_One = record.Total_Rental_Amount;
                valueArray.Value_Two = record.Approval_Status;
                
                headerArray['Header3'] = 'Total Rental Amount ';
                headerArray['Header4'] = 'Approval Status ';
                
                tableHraDeclarationSubGrid.fnOpen(nTr, fnDeviceColumnDetails(headerArray,valueArray), 'details hidden-lg hidden-md');
            }
        }
    });

    /*  Add event listener for select and unselect details  */
    $(document).on('click','#tableHraDeclarationSubGrid tbody td div', function () {
        var selectRow = $(this).parent().parent();
        
        tableHraDeclarationSubGrid.$('tr.row_selected').removeClass('row_selected');
        
        if (!selectRow.hasClass('row_selected'))
        {
            selectRow.addClass('row_selected');
            fnActionButtonsHraDeclarationSubGrid (true);
        }
        else
        {
            fnActionButtonsHraDeclarationSubGrid (false);
        }
    });

    function fnActionButtonsHraDeclarationSubGrid (action) {
        if (action)
        {
            var record          = tableHraDeclarationSubGrid.fnGetData (fnGetSelected (tableHraDeclarationSubGrid)[0]);
            var approvalStatus  = record['Approval_Status'];
            if(approvalStatus=='Approved')
            {
                $('#revertHraDeclaration').show();
                fnGridButtons ($('#revertHraDeclaration'), true);
            }
            else
            {
                $('#revertHraDeclaration').hide();
                fnGridButtons ($('#revertHraDeclaration'), false);
            }
        }
        else
        {
            $('#revertHraDeclaration').hide();
            fnGridButtons ($('#revertHraDeclaration'), false);
        }
    }
    
    // On HRA subgrid add button click
    $('#addHraLandlordDetails').on('click', function(){
        $("#formSubmitHraDeclaration").html('<i class="mdi-content-send"></i> Add');
        
        if ($("#onHRAAddCollapse").hasClass('collapsed'))
        {
            $( "#onHRAAddCollapse" ).trigger( "click" );
        } 
        
        var declarationId = $('#HDeclaration_Id').val();
             
        if (declarationId > 0 && !isNaN(declarationId))
        {
            clearLock ({
                'formName' : 'HRA Declarations',
                'uniqueId' : $('#HDeclaration_Id').val(),
                'callback' : function ()
                {
                    $('#HLine_Item_Id').val(0);
                }
            });
        }
        
        $('#HraAttachmentsList').html("");
        
        tableHraDeclarationSubGrid.$('tr.hraDeclaration_editing_row').removeClass('hraDeclaration_editing_row');
                                
        $('#s2id_formHraRentMonths').select2('val','');
        $('#s2id_formHraRentMonths').removeClass('form-error');
        $('#formHraRentalAmount,#formHraLandLordName,#formHraLandLordAddress,#formHraLandLordPAN').val('');        
        
        fnHraStatusFieldUpdate($("#formHraDeclarationStatus"),0);
        
        aftDelNewHraFiles =[], bucketUploadSuccessFiles = [], bucketUploadFailureFiles = [], newHraFiles = [], oldHraFiles = [];  
              
    });
    
    /** edit HRA Declaration sub grid **/
    $(document).on('click', '.editHraDeclarationSubgrid,.statusUpdateHraDeclarationSubgrid', function() {
        $("#formSubmitHraDeclaration").html('<i class="mdi-content-send"></i> Update');
        
        var buttonId    = $(this).prop('id');
        var selectedRow = fnGetSelected ( tableHraDeclarationSubGrid );
        
        aftDelNewHraFiles =[], bucketUploadSuccessFiles = [], bucketUploadFailureFiles = [], newHraFiles = [], oldHraFiles = [];
        
        if (selectedRow.length)
        {
            var mainGridRecord = tableHraDeclaration.fnGetData (fnGetSelected ( tableHraDeclaration )[0]),
                record = tableHraDeclarationSubGrid.fnGetData (selectedRow[0]);            
            
            $('#HDeclaration_Id').val(record.Declaration_Id)
            
            //if (record.Line_Item_Id > 0 && mainGridRecord.Declaration_Id > 0) 
            if (record.Line_Item_Id > 0 && $('#HDeclaration_Id').val() > 0)             
            {
                clearLockFlag = 0;
                
                if ($('#HDeclaration_Id').val() > 0)
                {                    
                    clearLock ({
                        'formName' : 'HRA Declarations',
                        'uniqueId' : $('#HDeclaration_Id').val(),
                        'callback' : function ()
                        {
                            clearLockFlag = 1;
                            
                            tableHraDeclarationSubGrid.$('tr.hraDeclaration_editing_row').removeClass('hraDeclaration_editing_row');
                            
                            $('#HLine_Item_Id').val(0);
                            
                            $('#HraAttachmentsList').html("");
                            
                            if (buttonId != 'hraDeclaration_'+record.Line_Item_Id){
                                fnPreFillFormValuesStatusHraDeclaration(record);
                            }
                            else{
                                fnPreFillFormValuesHraDeclaration(record,1);
                            }                            
                        }
                    });
                }
                else
                {
                    clearLockFlag = 1;
                }
                
                if (clearLockFlag == 1)
                {                    
                    setLock ({
                        'formName' : 'HRA Declarations',
                        'uniqueId' : $('#HDeclaration_Id').val(),
                        'panel'    : $('#editFormHraDeclaration'),
                        'callback' : function (result)
                        {                            
                            if ($("#onHRAAddCollapse").hasClass('collapsed'))
                            {
                                $( "#onHRAAddCollapse" ).trigger( "click" );
                            }
                                        
                            var rows = $('tbody td div#hraDeclaration_'+record.Line_Item_Id);
                             
                            var editRow = rows.parent().parent();
                            
                            if(!(editRow.hasClass('hraDeclaration_editing_row')))
                            {
                                editRow.addClass('hraDeclaration_editing_row');
                            }                            
                            
                            $('#HraAttachmentsList').html("");
                            
                            if (buttonId != 'hraDeclaration_'+record.Line_Item_Id){
                                fnPreFillFormValuesStatusHraDeclaration(record);
                            }
                            else{
                                fnPreFillFormValuesHraDeclaration(record,1);                                
                            }
                            
                            if(!(editRow.hasClass('row_selected')))
                            {
                                editRow.addClass('row_selected');
                            }                                
                        }
                    });
                }
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+ $('#lblFormNameB').html()+' sub grid', type : 'info' });
            }            
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+ $('#lblFormNameB').html()+' sub grid', type : 'info' });
        }
    
    });    
    
    
    //Alert while Edit Approved Details in HRA Declaration Sub Grid
    $(document).on('click', '.editAlertHraDeclarationSubgrid', function(){
        fnModalScrollTop('modalFormHraDeclaration');
        
        var record = tableHraDeclarationSubGrid.fnGetData (fnGetSelected ( tableHraDeclarationSubGrid )[0]);
        
        if(record.Approval_Status == 'Rejected'){
            jAlert ({ panel : $('#editFormHraDeclaration'), msg : "You can't update rejected status record", type : 'warning' });    
        }
        else{
            jAlert ({ panel : $('#editFormHraDeclaration'), msg : "You can't update approved status record", type : 'warning' });
        }
        
        $('#declarationId').val(record.Declaration_Id);
        $('#lineItemId').val(record.Line_Item_Id);
        
        $('#s2id_formHraRentMonths,#s2id_formHraDeclarationStatus').select2('val','');
        $('#formHraRentalAmount,#formHraLandLordName,#formHraLandLordAddress,#formHraLandLordPAN,#HformComment').val('');
    });    
    
    //Alert while delete Approved Details in HRA Declaration Sub Grid
    $(document).on('click', '.deleteAlertHraDeclarationDetailsSubGrid', function(){
        fnModalScrollTop('modalFormHraDeclaration');
        
        var record = tableHraDeclarationSubGrid.fnGetData (fnGetSelected ( tableHraDeclarationSubGrid )[0]);
        $('#declarationId').val(record.Declaration_Id);
        $('#lineItemId').val(record.Line_Item_Id);
        
        if(record != undefined && record != '' && record.Approval_Status == 'Rejected'){
            jAlert ({ panel : $('#editFormHraDeclaration'), msg : "You can't delete rejected status record", type : 'warning' });
        }
        else{
            jAlert ({ panel : $('#editFormHraDeclaration'), msg : "You can't delete approved status record", type : 'warning' });
        }
        
        $('#s2id_formHraRentMonths,#s2id_formHraDeclarationStatus').select2('val','');
        $('#formHraRentalAmount,#formHraLandLordName,#formHraLandLordAddress,#formHraLandLordPAN,#HformComment').val('');        
    });
    
    //delete Landlord Details Sub grid.
    $(document).on('click', '.deleteHraDeclarationDetailsSubGrid', function(){
        var record = tableHraDeclarationSubGrid.fnGetData (fnGetSelected ( tableHraDeclarationSubGrid )[0]);
        
        if (record)
        {
            var lineItemId = record.Line_Item_Id;
            
            if (lineItemId > 0 && !isNaN(lineItemId))
            {
                $('#modalDeleteHraDeclareSub').modal('toggle');
            }
            else
            {
                jAlert ({ msg : 'Kindly select a record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select a record', type : 'info' });
        }                
    });
    
    $(document).on('click', '#deleteConfirmHraDeclareSub', function(){
        var selectedRow = fnGetSelected ( tableHraDeclarationSubGrid );
        
        if (selectedRow.length)
        {
            var record = tableHraDeclarationSubGrid.fnGetData (selectedRow[0]);
            var lineItemId = record.Line_Item_Id;
            deleteSuccess  = 0;
            
            if(record.Declaration_Path.length > 0){
       
                for(i=0;i<record.Declaration_Path.length;i++){
                    bucketFileName = '';
                    bucketFileName = record.Declaration_Path[i]['File_Name'];
                    
                var res = fndeleteS3Document(domainName+"/"+orgCode+"/"+"Hra Declarations/"+bucketFileName,'bucketName');
                if(res == 'Document deleted') {
                    deleteSuccess += 1;
                } else {
                    deleteSuccess -= 1;                                    
                }

                }
                
                if (lineItemId > 0 && (deleteSuccess == 0 || (deleteSuccess == record.Declaration_Path.length)))
                {
                    $('#HraAttachmentsList').html("");
                    deleteHraSubRecord(lineItemId,record.Declaration_Id);
                }
                else{
                    jAlert ({ panel : $('#editFormHraDeclaration'), msg : 'Unable to delete few files', type : 'warning' });
                }
            
            }
            else
            {
                deleteHraSubRecord(lineItemId,record.Declaration_Id)
            }
        }
    });
    
    
    function deleteHraSubRecord(lineItemId,DeclarationId)
    {
        $.ajax({
            type     : 'POST',
            url      : pageUrl()+'payroll/tax-declarations/delete-hra-declaration-subgrid/lineItemId/'+lineItemId+'/declarationId/'+DeclarationId,
            async    : false,
            dataType :"json",
            success  : function(result)
            {
                if (result){
                    if (result.success)
                    {
                        /** reload month combo **/
                        var comboField = [$('#formHraRentMonths')];
                        
                        for (var x in comboField)
                        {
                            fnReloadCombo(comboField[x],result.comboPair,'multiselect','form');
                        }
                        
                        /** If all the hra declaration records for an employee is deleted, close the modal*/
                        if(result.declarationExists == 0)
                        {
                            $('#HDeclaration_Id').val(0);
                            $('#modalFormHraDeclaration').modal('toggle');
                            fnRefreshTable(tableHraDeclaration);
                            jAlert({ msg : result.msg, type : result.type });
                        }else{
                            jAlert({ panel : $('#editFormHraDeclaration'),msg : result.msg, type : result.type });
                    
                            fnModalScrollTop('modalFormHraDeclaration');
                        }
                                                                
                        fnPreFillFormValuesHraDeclaration('',0);
                        
                        $('#s2id_formHraDeclarationStatus').select2('val','Applied');
                        
                        fnRefreshTable(tableHraDeclarationSubGrid);
                    }else{
                        jAlert({ panel : $('#editFormHraDeclaration'),msg : result.msg, type : result.type });
                    
                        fnModalScrollTop('modalFormHraDeclaration');
                    }
                }
                else{
                    jAlert({ panel : $('#editFormHraDeclaration'),msg : "Something went wrong. Please contact system admin.", type : "warning" });
                }
            },
            error  : function (hraDecDeleteErrorResult){
                if(hraDecDeleteErrorResult.status == 200){
                    sessionExpired ();
                }else{
                    /* To handle internal server error */
                    jAlert({ panel : $('#editFormHraDeclaration'),msg : "Something went wrong. Please contact system admin.", type : "warning" });
                    fnModalScrollTop('modalFormHraDeclaration');
                }
            }
        });
    }
    
    /** Status Update - HRA Declaration Form **/    
    $('#statusApprovalHraDeclaration,#statusUpdateContextHraDeclaration').on('click', function () {
        var button      = $(this).prop('id');
        var selectedRow = fnGetSelected (tableHraDeclaration);
        
        fnFilterClose ('HraDeclaration');
        
        if (selectedRow.length)
        {
            var record         = tableHraDeclaration.fnGetData(selectedRow[0]);
            var declarationId  = record.Declaration_Id;
            $('#HLine_Item_Id').val(0);
            $('#HDeclaration_Id').val(declarationId);
            
            if (declarationId > 0 && !isNaN(declarationId))
            {
                setLock({
                    'formName'  : 'HRA Declarations',
                    'uniqueId'  : record.Declaration_Id,
                    'callback'  : function(result)
                    {
                        $('#modalFormHraDeclaration').modal('toggle');
                        
                        $('#modalFormHraDeclaration .modal-title').html("<strong>Update</strong> Status");
                        
                        fnPreFillFormValuesStatusHraDeclaration('');
                        
                        var employeeField = $('#formHraEmployeeName');
                        employeeField.find('option').remove();
                        employeeField.append('<option value="'+ record.Employee_Id +'">'+ record.Employee_Name +'</option>');
                        employeeField.select2('val', record.Employee_Id);
                        employeeField.prop('readonly', true);
                        
                        $('#editFormHraDeclaration').validate().resetForm();
                        
                        $('#viewFormHraDeclaration, #editInViewHraDeclaration,#hraDeclarationSubGrid,#formSubmitHraDeclaration,#revertHraDeclaration').hide();
                        $('#formResetHraDeclaration,#addHraLandlordDetails,#formHraCloseSubmit,#formHraSubmitProof').hide();
                        
                        $('#editFormHraDeclaration, #hraDeclarationSubGrid,#formResetHraStatus,#formStatusSubmitHraDeclarationMail,#formSubmitStatusHraDeclaration').show();

                        /** To load landlord details Sub Grid **/                        
                        fnHraDeclarationSubGrid(declarationId,1);
                        getEmployeeTaxRegime($('#s2id_formHraEmployeeName').select2('val'),'#editFormHraDeclaration','#modalFormHraDeclaration');
                    }
                });
            }
            else
            {
                jAlert ({ msg : 'Kindly select landlord details record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select landlord details record', type : 'info' });
        }
    });
    
    /** Change event for HRA declaration status **/
     $('#formHraDeclarationStatus').on('change', function () {
        var status = $(this).val();
        
        if (status == 'Approved' || status == 'Rejected') {
            $('.hraApprovalBasedHidden').show();
            
            if (status == 'Approved') {
                $('#labelHraAmountApproved').html('Amount Approved<span class="short_explanation">*</span>');
                $('#formHraAmountApproved').addClass('vRequired');
                $('#formHraAmountApproved').prop('readOnly', false);
                $('#formHraAmountApproved').val('');
            }
            else if (status == 'Rejected') {
                $('#labelHraAmountApproved').html('Amount Approved');
                $('#formHraAmountApproved').removeClass('vRequired');
                $('#formHraAmountApproved').prop('readOnly',true);
                $('#formHraAmountApproved').val('');
                $('#formHraAmountApproved').valid('');
            }
        }
        else{            
            $('#formHraAmountApproved').prop('readOnly',true);
            $('#labelHraAmountApproved').html('Amount Approved');
            $('#formHraAmountApproved').removeClass('vRequired');
            $('#formHraAmountApproved').val('');
            $('.hraApprovalBasedHidden').hide();
        }
    });
     
     /** Change event for approved amount status **/
     $('#formHraAmountApproved').on('change keyup', function () {        
        if ($(this).val() != 0) {            
             $("#formAmountApproved").prop({
                 "min" : 1,
                 "max": $('#formHraRentalAmount').val()
            });
        }
     });
    
    
    /** mail , status update  @ Form Status Submit - HRA Declaration **/
    $('#formSubmitStatusHraDeclaration,#formStatusSubmitHraDeclarationMail').on('click', function () {
        setMask('#wholepage');
        var button      = $(this).prop('id');        
        var l = Ladda.create(this);
        l.start();
        
        $('#s2id_formHraEmployeeName, #s2id_formHraRentMonths, #s2id_formHraDeclarationStatus').removeClass('form-error');
        $('#s2id_formHraAssessmentYear').removeClass('form-error');
        
        $('#uploadHraFiles').removeClass('vUploader');
        $('#uploadHraFiles').removeClass('custom-file');
        
        if(button == 'formStatusSubmitHraDeclarationMail' && (isDirtyFormHraDeclaration))
        {
            removeMask();
            jAlert ({ panel : $('#editFormHraDeclaration'), msg : 'Kindly update the status', type : 'warning' });    
        }
        else if (( button == 'formSubmitStatusHraDeclaration' && $('#s2id_formHraDeclarationStatus').select2('val') != 'Applied' && $('#editFormHraDeclaration').valid()
             && $('#HLine_Item_Id').val() > 0 ) || ( button == 'formStatusSubmitHraDeclarationMail' && ((isDirtyFormHraDeclaration
             && $('#editFormHraDeclaration').valid() && $('#HLine_Item_Id').val() > 0) || !(isDirtyFormHraDeclaration))))
        {
            if ($('#HDeclaration_Id').val() > 0)
            {
                isMailSend = (button == 'formSubmitStatusHraDeclaration') ? 0 : 1;
                
                $.ajax ({
                    type     : 'POST',
                    dataType : 'json',
    //              async    : false,
                    url      : pageUrl () +'payroll/tax-declarations/status-update-hra-declaration',
                    data     : {
                        _buttonId            : button,
                        _declarationId       : $('#HDeclaration_Id').val(),
                        _lineItemId          : $('#HLine_Item_Id').val(),
                        _rentalAmount        : $('#formHraRentalAmount').val(),
                        _approvalStatus      : $('#s2id_formHraDeclarationStatus').select2('val'),
                        _approvedAmount      : $('#formHraAmountApproved').val(),                        
                        _comment             : $('#HformComment').val(),
                        _mailSend            : isMailSend
                    },
                    success  : function (result)
                    {
                        if (result && isJson (result))
                        {
                            if (result.success)
                            {
                                isDirtyFormHraDeclaration = false;
                                
                                fnRefreshTable (tableHraDeclaration);
                                
                                if(button == 'formSubmitStatusHraDeclaration'){
                                    
                                    fnPreFillFormValuesStatusHraDeclaration('');
                                    
                                    fnHraDeclarationSubGrid($('#HDeclaration_Id').val(),1);                                    
                                    
                                    jAlert ({ panel : $('#editFormHraDeclaration'), msg : result.msg, type : result.type });    
                                }
                                else{
                                    $('#modalFormHraDeclaration').modal('toggle');
                                    
                                    jAlert ({ msg : result.msg, type : result.type });
                                }
                            }
                            else
                            {
                                jAlert ({ panel : $('#editFormHraDeclaration'), msg : result.msg, type : result.type });
                            }
                        }
                        else
                        {
                            jAlert({ panel : $('#editFormHraDeclaration'), msg : "Something went wrong. Please contact system admin", type : "warning" });
                        }
                            
                        l.stop();
                        removeMask();
                    },
                    error : function(updateHraDecStatusErrorResult){
                        l.stop();
                        removeMask();
                        if(updateHraDecStatusErrorResult.status === 200){
                            sessionExpired();
                        }else{
                            /* To handle internal server error */
                            jAlert({ panel : $('#editFormHraDeclaration'), msg : "Something went wrong. Please contact system admin", type : "warning" });
                        }
                    }
               });
            }
            else
            {
                l.stop();
                removeMask();
            }
        }
        else
        {
            l.stop();
            
            if( $('#HLine_Item_Id').val() == 0 ){
                jAlert ({ panel : $('#editFormHraDeclaration'), msg : 'Kindly select the record', type : 'info' });
            }
            else if( $('#s2id_formHraDeclarationStatus').select2('val') == 'Applied' ){
                jAlert ({ panel : $('#editFormHraDeclaration'), msg : 'Kindly update the status', type : 'info' });
            }
            removeMask();
        }
         $('#uploadHraFiles').addClass('vUploader');
         $('#uploadHraFiles').addClass('custom-file');
  
    });
    
    /** HRA Declaration filters **/
    $('#filterHraDeclaration,#closeFilterHraDeclaration').on('click', function() {
        if ($('#filterPanelHraDeclaration').hasClass('open'))
        {
            $('#filterPanelHraDeclaration').removeClass('open');
            $('#filterPanelHraDeclaration').hide();
        }
        else
        {
            $('#filterPanelHraDeclaration').addClass('open');
            $('#filterPanelHraDeclaration').show();
        }
    });
    
    /** Reset The Values In Hra Declaration Filter **/
    $('#cancelHraDeclaration,#closeFilterHraDeclaration').on('click', function () {
         if (!$('#filterHEmployeeName').is('[readonly]')) {
            $('#filterHEmployeeName').val('');
       }       
        
        $('#filterHAssessmentYearStart,#filterHAssessmentYearEnd,#filterHTotalAmountStart,#filterHTotalAmountEnd').val('');
        
        if ($('#fieldForce').val() == 1) {
            $('#s2id_filterHraDeclarationServiceProvider').select2('val', '');
        }

        tableHraDeclaration.fnReloadAjax( pageUrl() +'payroll/tax-declarations/list-hra-declaration' );
    });
    
     /** Apply The Values In Hra Declaration Filter **/
    $('#applyHraDeclaration').on('click', function () {        
        filterHEmployeeName         = $('#filterHEmployeeName').val();      
        filterHAssessmentYearStart  = $('#filterHAssessmentYearStart').val();
        filterHAssessmentYearEnd    = $('#filterHAssessmentYearEnd').val();
        filterHTotalAmountStart     = $('#filterHTotalAmountStart').val();
        filterHTotalAmountEnd       = $('#filterHTotalAmountEnd').val();

        if ($('#fieldForce').val() == 1) {
            var ftServiceProvider = $('#s2id_filterHraDeclarationServiceProvider').select2('val');
        }
        else {
            var ftServiceProvider = '';
        }
        
        tableHraDeclaration.fnReloadAjax(pageUrl() +'payroll/tax-declarations/list-hra-declaration'+
                                                  '/Search_1/'+ filterHEmployeeName +
                                                  '/Search_2/'+ filterHAssessmentYearStart +
                                                  '/Search_3/'+ filterHAssessmentYearEnd +
                                                  '/Search_4/'+ filterHTotalAmountStart+
                                                  '/Search_5/'+ filterHTotalAmountEnd+
                                                  '/Search_6/' + ftServiceProvider);
    });
   
   /**
     *  click to close add/edit modal
    */
    $('#editCloseConfirmHraDeclaration,#formHraCloseSubmit').on('click', function () {
        var button      = $(this).prop('id');
        
        if (button == 'formHraCloseSubmit') {
            if(isDirtyFormHraDeclaration){
                    jAlert ({ panel : $('#editFormHraDeclaration'), msg : 'Kindly update landlord details', type : 'warning' });    
            }
            else{
                var rec = tableHraDeclarationSubGrid.fnGetData ();
                
                if(rec != '')
                {
                    $('#HraAttachmentsList').html("");
                    
                    jAlert ({ msg :  $('#lblFormNameB').html()+' updated successfully', type : 'info' });
                    
                    fnRefreshTable (tableHraDeclaration);
            
                    fnFormCloseHraDeclaration (true);
                    isDirtyFormHraDeclaration = false;
                }
                else{                
                    $('#declarationId').val(0);
                    $('#lineItemId').val(0);
                    jAlert ({ panel : $('#editFormHraDeclaration'), msg : 'You should have atleast one landlord detail to update', type : 'warning' });                
                }
            }
        }
        else{
            $('#HraAttachmentsList').html("");
            
            isDirtyFormHraDeclaration = false;
            
            fnRefreshTable (tableHraDeclaration);
        
            fnFormCloseHraDeclaration(true);
        }
    });
    
    /**
     *  Add,Edit form modal hide event
    */
    $('#modalFormHraDeclaration').on('hide.bs.modal', function (e) {
        if (isDirtyFormHraDeclaration)
        {
            e.preventDefault();
            e.stopImmediatePropagation();
            
            $('#modalDirtyHraDeclaration').modal('toggle');
        }
        else
        {
            fnFormCloseHraDeclaration(false);
        }
    });
    
    /** update hra Declaration**/
    function fnSubmithra(button){
        setMask('#wholepage');
        //mailSent = (button == 'formHraSubmitProof') ? 1 : 0;
        $.ajax ({
            type     : 'POST',
            dataType : 'json',
            cache    : false,
            url      : pageUrl () +'payroll/tax-declarations/update-hra-declaration/HDeclaration_Id/'+$('#HDeclaration_Id').val()+'/upload/0',
            data     : {
                _lineItemId          : $('#HLine_Item_Id').val(),
                _hraEmployee         : $('#formHraEmployeeName').select2('val'),
                _assessmentYear      : $('#formHraAssessmentYear').select2('val'),
                _months              : $('#formHraRentMonths').select2('val'),
                _rentalAmount        : $('#formHraRentalAmount').val(),
                _approvalStatus      : $('#s2id_formHraDeclarationStatus').select2('val'),
                _landLordName        : $('#formHraLandLordName').val(),
                _landLordAddress     : $('#formHraLandLordAddress').val(),
                _landLordPAN         : $('#formHraLandLordPAN').val(),
                _landLordDeclaration : bucketUploadSuccessFiles,
                _comment             : $('#HformComment').val(),
                _buttonId            : button
            },
            success  : function (result)
            {
                if (result && isJson (result))
                {
                    if (result.success)
                    {
                        lockClear = 0;
                        
                        if ($('#HLine_Item_Id').val() > 0)
                        {
                            clearLock ({
                                'formName' : 'HRA Declarations',
                                'uniqueId' :  $('#HDeclaration_Id').val(),
                                'callback' : function ()
                                {
                                    lockClear = 1;
                                }
                            });
                        }
                        else
                        {
                            lockClear = 1;
                        }
                        
                        if (lockClear == 1)
                        {
                            $("#formSubmitHraDeclaration").html('<i class="mdi-content-send"></i> Add');                           
                            
                            $('#HLine_Item_Id').val(0);
                            
                            //tableHraDeclarationSubGrid.$('tr.hraDeclaration_editing_row').removeClass('hraDeclaration_editing_row');
                            
                            //$('#formResetHraDeclaration').trigger('click');            
                                                    
                            $('#HDeclaration_Id').val(result.Declaration_Id)
                            
                            $('#HraAttachmentsList').html("");
                                
                            isDirtyFormHraDeclaration = false;
                            
                            aftDelNewHraFiles =[], bucketUploadSuccessFiles = [], bucketUploadFailureFiles = [], newHraFiles = [], oldHraFiles = [];
                            
                            /** update the total allowed file size **/       
                            //getUploadedTotalFileSize('HRA Declarations');
                                                    
                            if(button == 'formHraSubmitProof'){
                                $('#modalFormHraDeclaration').modal('toggle');
                                
                                fnRefreshTable (tableHraDeclaration);
                                
                                jAlert ({ msg : result.msg, type : result.type });
                            }
                            else{
                                /** reload month combo **/
                                var comboField = [$('#formHraRentMonths')];
                                
                                for (var x in comboField)
                                {
                                   fnReloadCombo(comboField[x],result.comboPair,'multiselect','form');
                                }
                                
                                $("#s2id_formHraRentMonths,#s2id_formHraDeclarationStatus").select2('val', '');
                                
                                $("#s2id_formHraDeclarationStatus").select2('val', 'Applied');
                                
                                $('#HLine_Item_Id').val(0);
                                $('#formHraLandLordName,#formHraLandLordAddress,#formHraLandLordPAN,#formHraRentalAmount').val('');
                            
                                fnHraDeclarationSubGrid($('#HDeclaration_Id').val(),0)
                                
                                fnModalScrollTop('modalFormHraDeclaration');
                               
                                jAlert ({ panel : $('#editFormHraDeclaration'), msg : result.msg, type : result.type });
                            }
                        }
                    }
                    else
                    {
                        jAlert ({ panel : $('#editFormHraDeclaration'), msg : result.msg, type : result.type });
                    }
                }
                else
                {
                    jAlert({ panel : $('#editFormHraDeclaration'), msg : "Something went wrong. Please contact system admin", type : "warning" });
                }
                removeMask();
            },
            error : function(updateHraErrorResult){
                removeMask();
                if(updateHraErrorResult.status === 200){
                    sessionExpired();
                }else{
                    /* To handle internal server error */
                    jAlert({ panel : $('#editFormHraDeclaration'), msg : "Something went wrong. Please contact system admin", type : "warning" });
                }
            }
        });
    }
    
    /** Close Function For HRA declaration Forms **/ 
    function fnFormCloseHraDeclaration (hideAction) {
        var uniqueId = $('#HDeclaration_Id').val();
        
        if (uniqueId > 0 && !isNaN(uniqueId))
        {
            clearLock ({
                'formName' : 'HRA Declarations',
                'uniqueId' : uniqueId,
                'callback' : function ()
                {
                    $('#HDeclaration_Id').val(0);
                    
                    if (hideAction)
                        $('#modalFormHraDeclaration').modal('hide');
                }
            });
        }
        else
        {
            if (hideAction)
                $('#modalFormHraDeclaration').modal('hide');
        }
        $('#gridPanelHraDeclaration .panel-reload').trigger('click');
    }
    
   /** Prefill Log Employee Name and reload the subgrid if the employee already Exists **/ 
    function fnprefillHraEmployee (employeeId) {
        if (employeeId != 0 && employeeId != null) {
            $.ajax ({
            type     : 'POST',
            async    : false,
            dataType : "json",
            url      : pageUrl ()+'payroll/tax-declarations/get-employee-hra-declaration/EmployeeId/'+ employeeId, 
            success  : function(requestId)
            {
                if(requestId != null && requestId != false){
                    $('#HDeclaration_Id').val(requestId);
                    fnHraDeclarationSubGrid(requestId,0);
                }
                else{
                    $('#HDeclaration_Id').val(0);
                    fnHraDeclarationSubGrid(0,0);
                }
                fnPrefillHraRentalMonths(employeeId);
            },
            error : function(hraDeclarationSubGridError)
            {
                /** If the session expired */
                if (hraDeclarationSubGridError.status === 200) {
                    sessionExpired();
                }else{
                    jAlert ({ panel : $('#editFormHraDeclaration'), msg : 'Something went wrong wrong while fetching the landlord detail(s).Please try after sometime', type : 'warning' });
                }
            }
            });
        }else{
            /** Reset the hra declaration when the employee name is selected as empty */
            fnPreFillFormValuesHraDeclaration('',1);
        }       
        
        if($('#AccessEmpCheckB').val()==1){
            $('#formHraEmployeeName').prop('readOnly', false);
        }
        else{
            $('#formHraEmployeeName').prop('readonly', true);            
        }
        getEmployeeTaxRegime(employeeId,'#editFormHraDeclaration','#modalFormHraDeclaration');   
    }
    
    function fnPrefillHraRentalMonths(employeeId){
        $("#formHraRentMonths").select2('val','');
        let assessmentYear = $('#s2id_formHraAssessmentYear').select2('val');
        if(employeeId >= 0 && assessmentYear){
            $.ajax ({
                type     : 'POST',
                async    : false,
                dataType : "json",
                url      : pageUrl ()+'payroll/tax-declarations/get-emp-rental-months/employeeId/'+ employeeId+'/assessmentYear/'+assessmentYear,
                success  : function(monthsPair)
                {
                    fnReloadCombo($("#formHraRentMonths"),monthsPair,'multiselect','form');
                },
                error : function(hraRentalMonthResError){
                    /** If the session expired */
                    if (hraRentalMonthResError.status === 200) {
                        sessionExpired();
                    }else{
                        jAlert ({ panel : $('#editFormHraDeclaration'), msg : 'Something went wrong wrong while fetching the rental month(s).Please try after sometime', type : 'warning' });
                    }
                }
            });
        }else{
            jAlert ({ panel : $('#editFormHraDeclaration'), msg : 'Please select employee name and the assessment year to get the rental month(s)', type : 'warning' });
        }
    }
   
   function fnHraStatusFieldUpdate(field,status){
        if(field != ''){
            if(status == 0){
                field.find('option').remove();
                
                statusArray = ['Applied'];
                
                field.append( "<option value=''>-- Select --</option>");
                    
                for (var x in statusArray)
                {
                    field.append("<option value='" + statusArray[x] + "'>" + statusArray[x] + "</option>");
                }
                
                field.select2( 'val', '');
            }    
        }
   }

    function fnPrefillUploadFiles(mode,filesArr,formName){        
        if(formName == 'HRA Declarations'){
            $('#HraAttachmentsList').html("");
            
            oldHraFiles = [];
            
            if(mode=='edit'){
                for (var i = 0; i < filesArr.length; i++)
                {
                    $('#buttonUploadFile').show();
                    
                    /** seperate arrays are formed to compare the file names while delete and to check the file size update while update**/
                    oldHraFiles.push(filesArr[i]['File_Name']);                
                    
                    displayFileName = filesArr[i]['File_Name'].split('?')[5];
                    
                    /** get the display File name for the old format(EmpId_LandLordname_AssessmentYear_Filename_date_uniqueid)
                    and new format(EmpId_LandLordname_AssessmentYear_date_uniqueid_Filename) **/
                    if(isNaN(Date.parse(displayFileName)))
                    {
                        displayFileName = filesArr[i]['File_Name'].split('?')[5];
                    }
                    else
                    {
                        displayFileName = filesArr[i]['File_Name'].split('?')[3];
                    }
                    
                    /** If File name length is greater than 10, then first 10 characters of file name only will be displayed and dot will
                    be concat at the end of the file name **/                                                
                    if(displayFileName.length > 10)
                    {
                        displayFileName = displayFileName.substring(0,10);
                        displayFileName = displayFileName+'...';
                    }
                    else
                    {
                        displayFileName = displayFileName;
                    }
                    
                    /** file link **/
                    var sUrl = fngetSignedUrl(domainName+"/"+orgCode+"/"+"Hra Declarations/"+filesArr[i]['File_Name'],'bucketName');
                        
                        $('#HraAttachmentsList').append('<div class="hraAttachmentDiv" style="max-width:100%;margin-bottom:15px;background:#dfdfdf;color:white;padding:5px;border-radius:3px;">'                                                    
                                                +'<a target="_blank" style="padding:0px;cursor: pointer;background-color: transparent;color: #555;text-decoration:none;" id="hradownloadlink'+i+'" href="' + sUrl + '" class="hraFileDownload">'
                                                + displayFileName + '( ' + filesArr[i]['File_Size'] +' K )</a>'
                                                +'<button type="button" style="margin-top: -1px;margin-left: -37px;margin-right: 18px;float: right;padding: 0;cursor: pointer;border: 0;font-size: 21px;font-weight: 700;line-height: 1;opacity: .2;"'
                                                +'id="hradownloadlink'+i+'" class="hraFileDownload" aria-hidden='+true+'> <i class="fa fa-arrow-down" style="color:black;font-size:18px"></i>' +
                                                '</button>'
                                                +'<button type="button" style="margin-top:-22px;" id="'+ filesArr[i]['File_Name'] +'" class="close attachmentdelHra removeHraFile" aria-hidden='+true+
                                                '> <i class="fa fa-close" style="color:black;font-size:20px"></i>'
                                                +'</button> </div>');

                                                    //$('#HraAttachmentsList').append('<div class="hraAttachmentDiv" style="max-width:100%;margin-bottom:15px;background:#dfdfdf;color:white;padding:5px;border-radius:3px;">'+
                    //                                '<div style="text-overflow:ellipsis;display:inline-block;vertical-align:bottom;overflow:hidden;color: #555;" max-width:75%;white-space:nowrap;cursor:pointer;"><a target="_blank" style="background:#dfdfdf;padding:0px;cursor: pointer;color: #555;" id="'+ filesArr[i]['File_Name'] +'" class="hraFileDownload">'
                    //                                + filesArr[i]['File_Name'].split('?')[3] + '( ' + filesArr[i]['File_Size'] +' K ) </a> '
                    //                                +'</div>'
                    //                                +'<button type="button" style="margin-top: -1px;margin-left: -37px;margin-right: 18px;float: right;padding: 0;cursor: pointer;border: 0;font-size: 21px;font-weight: 700;line-height: 1;opacity: .2;"'
                    //                                +'id="'+ filesArr[i]['File_Name'] +'" class="hraFileDownload" aria-hidden='+true+'> <i class="fa fa-arrow-down" style="color:black;font-size:18px"></i>' +
                    //                                '</button>'
                    //                                +'<button type="button" style="margin-top:-2px;" id="'+ filesArr[i]['File_Name'] +'" class="close attachmentdelHra removeHraFile" aria-hidden='+true+
                    //                                '> <i class="fa fa-close" style="color:black;font-size:20px"></i>'
                    //                                +'</button> </div>');
                }
            }
            else{
                $('#buttonUploadFile').hide();
                // In status form
                for (var i = 0; i < filesArr.length; i++)
                {
                    displayFileName = filesArr[i]['File_Name'].split('?')[5];
                    
                    /** get the display File name for the old format(EmpId_LandLordname_AssessmentYear_Filename_date_uniqueid)
                    and new format(EmpId_LandLordname_AssessmentYear_date_uniqueid_Filename) **/
                    if(isNaN(Date.parse(displayFileName)))
                    {
                        displayFileName = filesArr[i]['File_Name'].split('?')[5];
                    }
                    else
                    {
                        displayFileName = filesArr[i]['File_Name'].split('?')[3];
                    }       
                    
                    /** If File name length is greater than 20, then first 20 characters of file name only will be displayed and dot will
                    be concat at the end of the file name **/                                                
                    if(displayFileName.length > 25)
                    {
                        displayFileName = displayFileName.substring(0,25);
                        displayFileName = displayFileName+'...';
                    }
                    else
                    {
                        displayFileName = displayFileName;
                    }
                    
                    /** file link **/
                    var sUrl = fngetSignedUrl(domainName+"/"+orgCode+"/"+"Hra Declarations/"+filesArr[i]['File_Name'],'bucketName');
                        
                        
                    $('#HraAttachmentsList').append('<div class="hraAttachmentDiv" style="margin-top: 2px;">'
                                +'<a target="_blank" style="padding:0px;cursor: pointer;background-color: transparent;color: #0097a7;text-decoration:none;" title="'+filesArr[i]['File_Name'].split('?')[3]+'" id="hradownloadlink'+i+'" href="' + sUrl + '" class="hraFileDownload">'
                                + displayFileName +'</a></div>');
                }            
            }
        }
        else{            
            $('#TaxAttachmentsList').html("");
            
            oldTaxFiles = [];
            
            if(mode=='edit'){
                for (var i = 0; i < filesArr.length; i++)
                {
                    $('#buttonTaxUploadFile').show();
                    
                    /** seperate arrays are formed to compare the file names while delete and to check the file size update while update**/
                    oldTaxFiles.push(filesArr[i]['File_Name']);
                    
                        displayFileName = filesArr[i]['File_Name'].split('?')[5];
                        
                        /** get the display File name for the old format(EmpId_InvestmentCategory_AssessmentYear_Filename_date_uniqueid)
                        and new format(EmpId_InvestmentCategory_AssessmentYear_date_uniqueid_Filename) **/
                        if(isNaN(Date.parse(displayFileName)))
                        {                            
                            displayFileName = filesArr[i]['File_Name'].split('?')[5];
                        }
                        else
                        {                            
                            displayFileName = filesArr[i]['File_Name'].split('?')[3];
                        }
                        
                        /** If File name length is greater than 10, then first 10 characters of file name only will be displayed and dot will
                        be concat at the end of the file name **/
                        if(displayFileName.length > 10)
                        {
                            displayFileName = displayFileName.substring(0,10);
                            displayFileName = displayFileName+'...';
                        }
                        else
                        {
                            displayFileName = displayFileName;
                        }
                        
                        /** file link **/
                        var sUrl = fngetSignedUrl(domainName+"/"+orgCode+"/"+"Tax Declarations/"+filesArr[i]['File_Name'],'bucketName');
                        
                            $('#TaxAttachmentsList').append('<div class="taxAttachmentDiv" style="max-width:100%;margin-bottom:15px;background:#dfdfdf;color:white;padding:5px;border-radius:3px;">'+
                                                '<a target="_blank" style="padding:0px;cursor: pointer;background-color: transparent;color: #555;text-decoration:none;" id="taxdownloadlink'+i+'" href="' + sUrl + '" class="taxFileDownload">'
                                                + displayFileName + '( ' + filesArr[i]['File_Size'] +' K )</a>'
                                                +'<button type="button" style="margin-top: -1px;margin-left: -37px;margin-right: 18px;float: right;padding: 0;cursor: pointer;border: 0;font-size: 21px;font-weight: 700;line-height: 1;opacity: .2;"'
                                                +'id="taxdownloadlink'+i+'" class="taxFileDownload" aria-hidden='+true+'> <i class="fa fa-arrow-down" style="color:black;font-size:18px"></i>' +
                                                '</button>'
                                                +'<button type="button" style="margin-top:-22px;" id="'+ filesArr[i]['File_Name'] +'" class="close attachmentdelTax removeTaxFile" aria-hidden='+true+
                                                '> <i class="fa fa-close" style="color:black;font-size:20px"></i>'
                                                +'</button> </div>');
                    //$('#TaxAttachmentsList').append('<div class="taxAttachmentDiv" style="max-width:100%;margin-bottom:15px;background:#dfdfdf;color:white;padding:5px;border-radius:3px;">'+
                    //                                '<div style="text-overflow:ellipsis;display:inline-block;vertical-align:bottom;overflow:hidden;color: #555;" max-width:75%;white-space:nowrap;cursor:pointer;"><a target="_blank" style="background:#dfdfdf;padding:0px;cursor: pointer;color: #555;" id="'+ filesArr[i]['File_Name'] +'" class="taxFileDownload">'
                    //                                + filesArr[i]['File_Name'].split('?')[3] + '( ' + filesArr[i]['File_Size'] +' K ) </a> '
                    //                                +'</div>'
                    //                                +'<button type="button" style="margin-top: -1px;margin-left: -37px;margin-right: 18px;float: right;padding: 0;cursor: pointer;border: 0;font-size: 21px;font-weight: 700;line-height: 1;opacity: .2;"'
                    //                                +'id="'+ filesArr[i]['File_Name'] +'" class="taxFileDownload" aria-hidden='+true+'> <i class="fa fa-arrow-down" style="color:black;font-size:18px"></i>' +
                    //                                '</button>'
                    //                                +'<button type="button" style="margin-top:-2px;" id="'+ filesArr[i]['File_Name'] +'" class="close attachmentdelTax removeTaxFile" aria-hidden='+true+
                    //                                '> <i class="fa fa-close" style="color:black;font-size:20px"></i>'
                    //                                +'</button> </div>');
                }
            }
            else{
                $('#buttonTaxUploadFile').hide();
                // In status form
                for (var i = 0; i < filesArr.length; i++)
                {
                        
                    displayFileName = filesArr[i]['File_Name'].split('?')[5];
                    
                    /** get the display File name for the old format(EmpId_InvestmentCategory_AssessmentYear_Filename_date_uniqueid)
                    and new format(EmpId_InvestmentCategory_AssessmentYear_date_uniqueid_Filename) **/
                    if(isNaN(Date.parse(displayFileName)))
                    {
                        displayFileName = filesArr[i]['File_Name'].split('?')[5];
                    }
                    else
                    {
                        displayFileName = filesArr[i]['File_Name'].split('?')[3];
                    }
                    
                    /** If File name length is greater than 25, then first 25 characters of file name only will be displayed and dot will
                    be concat at the end of the file name **/                        
                    
                    if(displayFileName.length > 25)
                    {
                        displayFileName = displayFileName.substring(0,25);
                        displayFileName = displayFileName+'...';
                    }
                    else
                    {
                        displayFileName = displayFileName;
                    }
                    
                    /** file link **/
                    var sUrl = fngetSignedUrl(domainName+"/"+orgCode+"/"+"Tax Declarations/"+filesArr[i]['File_Name'],'bucketName');
                    
                        $('#TaxAttachmentsList').append('<div class="taxAttachmentDiv" style="margin-top: 2px;">'
                                                +'<a target="_blank" style="padding:0px;cursor: pointer;background-color: transparent;color: #0097a7;text-decoration:none;" title="'+filesArr[i]['File_Name'].split('?')[3]+'" id="taxdownloadlink'+i+'" href="' + sUrl + '" class="taxFileDownload">'
                                                + displayFileName +'</a></div>');
                    
                }            
            }
        }
    }
   
    function fnPreFillFormValuesHraDeclaration(record,isSubGridNeedToRefresh){
        $('#s2id_formHraEmployeeName,#s2id_formHraAssessmentYear,#s2id_formHraDeclarationStatus').removeClass('form-error');
        
        $('#formHraLandLordName,#formHraLandLordAddress,#formHraLandLordPAN,#formHraRentalAmount,#formHraRentMonths').prop('readOnly', false);
        $('#formHraDeclarationStatus,#formHraAmountApproved,#formHraEmployeeName,#formHraAssessmentYear').prop('readOnly', false);
        
        $('#buttonUploadFile').show();
        
        fnHraStatusFieldUpdate($("#formHraDeclarationStatus"),0);
        
        aftDelNewHraFiles =[], bucketUploadSuccessFiles = [], bucketUploadFailureFiles = [], newHraFiles = [], oldHraFiles = [];
        
        if (record != '')
        {
            $('#formHraEmployeeName,#formHraAssessmentYear').prop('readOnly',true);
            
            $("#s2id_formHraEmployeeName").select2('val', record.Employee_Id);
            $("#s2id_formHraAssessmentYear").select2('val', record.Assessment_Year);            
            
            
            if(record.Approval_Status == 'Rejected'){
                $("#formHraDeclarationStatus").append("<option value='Rejected'>Rejected</option>");
            }
            $("#s2id_formHraDeclarationStatus").select2('val', record.Approval_Status);
            
            fnPrefillHraRentalMonths($('#s2id_formHraEmployeeName').select2('val'));
            
            $('#hraDeclarationSummaryPanel')[0].scrollIntoView();
            
            $('#HDeclaration_Id').val(record.Declaration_Id);
            $('#HLine_Item_Id').val(record.Line_Item_Id);                
            $('#formHraLandLordName').val(record.Landlord_Name);                
            $('#formHraLandLordAddress').val(record.Landlord_Address);
            $('#formHraLandLordPAN').val(record.Landlord_PAN);
            
            var monthsPairs = record.Month_Pairs;
            
            $.each(monthsPairs, function(key, row){
                $('#formHraRentMonths').append('<option value='+row+'>'+key+'</option>');
            });                
            
            $('#s2id_formHraRentMonths').select2('val',record.Rental_Month_Id);
            $('#formHraRentalAmount').val(record.Total_Rental_Amount);
            
            if(record.Declaration_Path.length > 0){
                fnPrefillUploadFiles('edit', record.Declaration_Path,'HRA Declarations');
            }
            else{
                $('#HraAttachmentsList').html("");
            }
            
        }
        else{
            //$('#formHraDeclarationReset').trigger('click');            
            
            if($('#HDeclaration_Id') == 0 ){
                $("#s2id_formHraEmployeeName").select2('val', '');
                $("#s2id_formHraAssessmentYear").select2('val', '');
            }
            
            $("#s2id_formHraRentMonths").select2('val', '');            
            $('#HLine_Item_Id').val(0);            
            $('#formHraLandLordName,#formHraLandLordAddress,#formHraLandLordPAN,#formHraRentalAmount,#HformComment').val('');
  
            $('#HraAttachmentsList').html("");
            
            $("#s2id_formHraDeclarationStatus").select2('val', 'Applied');
        }
        
        $('.hraApprovalBasedHidden').hide();
        
        if (isSubGridNeedToRefresh == 0)
        {
            fnHraDeclarationSubGrid($('#HDeclaration_Id').val(),0);
        }

        fnHralockConfiguration();
        $( "#editFormHraDeclaration").validate().resetForm();
        
        isDirtyFormHraDeclaration = false;
   }
   
    /** prefill form values in HRA declaration status approval form **/   
    function fnPreFillFormValuesStatusHraDeclaration(record){        
        $('#s2id_formHraEmployeeName,#s2id_formHraAssessmentYear').removeClass('form-error');        
        $('#s2id_formHraRentMonths, #s2id_formHraDeclarationStatus').removeClass('form-error');
        
        $('#formHraLandLordName,#formHraLandLordAddress,#formHraLandLordPAN,#formHraRentalAmount,#formHraRentMonths').prop('readOnly',true);
        $('#formHraDeclarationStatus,#formHraAmountApproved').prop('readOnly', false);
        
        $('#buttonUploadFile').hide();
        
        var mainGridRecord = tableHraDeclaration.fnGetData (fnGetSelected ( tableHraDeclaration )[0]);
        
        $("#s2id_formHraEmployeeName").select2('val', mainGridRecord.Employee_Id);        
        $("#s2id_formHraAssessmentYear").select2('val', mainGridRecord.Assessment_Year);
        $('#formHraEmployeeName,#formHraAssessmentYear').prop('readonly', true);
        $('#declarationId').val(mainGridRecord.Declaration_Id);
        
        var statusArray=[];
        
        $('#HraAttachmentsList').html("");
        
        if (record != '')
        {
            //$('#HDeclaration_Id').val(record.Declaration_Id);
            $('#HLine_Item_Id').val(record.Line_Item_Id);
            
            $('#formHraLandLordName').val(record.Landlord_Name);
            $('#formHraLandLordAddress').val(record.Landlord_Address);
            //$("#formDeclarationStatus").select2('val', record.Approval_Status);      
            $('#formHraLandLordPAN').val(record.Landlord_PAN);
           
            var monthsPairs = record.Month_Pairs;
          
            $.each(monthsPairs, function(key, row){
                $('#formHraRentMonths').append('<option value='+row+'>'+key+'</option>');
            });                
            
            $('#s2id_formHraRentMonths').select2('val',record.Rental_Month_Id);
            $('#formHraRentalAmount').val(record.Total_Rental_Amount);
             
            if(record.Declaration_Path.length > 0){
                fnPrefillUploadFiles('status', record.Declaration_Path, 'HRA Declarations');
            }
            else{
                $('#HraAttachmentsList').html("");
            }
            $('#labelHraAmountApproved').html('Amount Approved');
            $('#formHraAmountApproved').removeClass('vRequired');
            $('#formHraAmountApproved').prop('readOnly',true);
            $('#formHraAmountApproved').val('');
            $('.hraApprovalBasedHidden').show();
            
            var field = $("#formHraDeclarationStatus");
            
            field.find('option').remove();
            
            statusArray = ['Applied','Approved','Rejected'];            
            
            field.append( "<option value=''>-- Select --</option>");
                
            for (var x in statusArray)
            {
                field.append("<option value='" + statusArray[x] + "'>" + statusArray[x] + "</option>");
            }
    
            field.select2( 'val', record.Approval_Status);
        }
        else
        {            
            $('#lineItemId').val(0);            
            
            $("#s2id_formHraRentMonths,#s2id_formHraDeclarationStatus").select2('val', '');
            $('#formHraLandLordName,#formHraLandLordAddress,#formHraLandLordPAN,#formHraRentalAmount,#formHraAmountApproved').val('');        
                
            $('#labelHraAmountApproved').html('Amount Approved');
            $('#formHraAmountApproved').removeClass('vRequired');
            $('#formHraAmountApproved').prop('readOnly',true);
            $('#formHraAmountApproved').val('');
            $('.hraApprovalBasedHidden').show();
            
            var field = $("#formHraDeclarationStatus");
            
            //field.prop('disabled', false).find('option').remove();
            field.find('option').remove();
            
            statusArray = ['Applied', 'Approved', 'Rejected'];            
            
            field.append( "<option value=''>-- Select --</option>");
                
            for (var x in statusArray)
            {
                field.append("<option value='" + statusArray[x] + "'>" + statusArray[x] + "</option>");
            }
            
            field.select2( 'val', '');
        }
        
        $("#s2id_formHraDeclarationStatus").select2('val', 'Applied');
        
        $('#HformComment').val('');
        
        $('#hraDeclarationFileSize').val(0);
        
        isDirtyFormHraDeclaration = false;        
    }
   
   /**
     *  Show comments details for selection record
    */
    $('#commentHraDeclaration, #commentContextHraDeclaration').on('click', function(){
        var selectedRow = fnGetSelected (tableHraDeclaration );
        
        fnFilterClose ('HraDeclaration');
         
        if (selectedRow.length)
        {            
            var record = tableHraDeclaration.fnGetData (selectedRow[0]);            
            commentTable = viewCommentGrid(record.Declaration_Id,'Hra Declarations','HraDeclaration');
        }
        else
        {            
            jAlert ({ msg : 'Kindly select'+ $('#lblFormNameB').html()+' record', type : 'info' });
        }
    });
    
    //On + icon click in mobile & tablet view
    $(document).on('click', '#tableCommentHraDeclaration i', function () {
        var nTr = $(this).parents('tr')[0];
        
        if ( tableCommentTaxDeclaration.fnIsOpen(nTr) )
        {
            /* This row is already open - close it */
            $(this).removeClass().addClass('fa fa-plus-square-o');
            tableCommentHraDeclaration.fnClose(nTr);
        }
        else
        {
            var record = tableCommentHraDeclaration.fnGetData( nTr );
            var nRow =  $('#tableCommentHraDeclaration thead tr')[0];
            
            /* Open this row */
            $(this).removeClass().addClass('fa fa-minus-square-o');
            
            valueArray = [];
            headerArray=[];
        
            valueArray.Value_One = record.Added_On;
            
            $.each(nRow.cells, function(i,v) {
                headerArray['Header'+i]=v.innerText;
            });
            
            tableCommentHraDeclaration.fnOpen(nTr, fnDeviceColumnDetails(headerArray,valueArray), 'details hidden-lg hidden-md');
        }
    });
   
   function fnHraActionButtons(action){
        if (action)
        {            
            var record = tableHraDeclaration.fnGetData (fnGetSelected (tableHraDeclaration)[0]),
                logId = record['Log_Id'],
                approverId = record['Approver_Id'],
                assessmentBasedHraEdit = $('#hraAssessmentBasedEdit').val();
      
            fnGridButtons ($('#viewHraDeclaration'), true);            

            /** Check Edit Permission **/
            if(((logId=== record['Added_By'] && record['isEmpEdit'] > 0 && record['Update'] == 1) ||			
			(logId=== record['Employee_Id'] && record['isEmpEdit'] > 0 && record['Update'] == 1) ||
			(logId=== approverId && record['isApprEdit'] > 0 && record['Update'] == 1) || (record['Admin'] == 'admin')
            ) && assessmentBasedHraEdit == record.Assessment_Year)
            {
                fnGridButtons ($('#editHraDeclaration'), true);
                $('#editContextHraDeclaration').parent().show();                
            }
            else{
                fnGridButtons ($('#editHraDeclaration'), false);
                $('#editContextHraDeclaration').parent().hide();
            }
            
            /** Check Status Update Permission **/
            if(((logId === approverId ||  $('#IsEmpAdminHra').val() === 'admin') && record['isApprEdit'] > 0
                && assessmentBasedHraEdit == record.Assessment_Year))
            {
                $('#statusUpdateContextHraDeclaration').parent().show();
                fnGridButtons ($('#statusApprovalHraDeclaration'), true);
            }
            else
            {
                $('#statusUpdateContextHraDeclaration').parent().hide();
                fnGridButtons ($('#statusApprovalHraDeclaration'), false);   
            }
 
            if (record.Comment > 0)
            {                
                $('#commentContextHraDeclaration').parent().show();
                fnGridButtons ($('#commentHraDeclaration'), true);
            }
            else
            {                
                $('#commentContextHraDeclaration').parent().hide();
                fnGridButtons ($('#commentHraDeclaration'), false);
            }
        }
        else
        {            
            fnGridButtons ($('#viewHraDeclaration, #editHraDeclaration, #statusApprovalHraDeclaration, #commentHraDeclaration'), false);
        }    
   }
    
    //On second modal close, first modal scroll will not work. To avoid that,using this
    $('#dirtyTaxDeclaration,#modalDeleteTaxDeclareSub,#sessionExipredModel,#modalTaxRegime').on('hidden.bs.modal', function () {
        if($('#modalTaxDeclaration').is(':visible'))
            $('body').addClass('modal-open');
    });
    
    //In HRA Declaration - On second modal close, first modal scroll will not work. To avoid that,using this
    $('#modalDeleteHraDeclareSub,#modalDirtyHraDeclaration,#sessionExipredModel,#modalTaxRegime').on('hidden.bs.modal', function () {
        if($('#modalFormHraDeclaration').is(':visible'))
            $('body').addClass('modal-open');
    });
    
    // check if the incomeU/S24 or POI forms view access exits for the Employee
    if ($('#incomeUnderSectionAccess').val() == 1 || $('#proofOfInvestmentAccess').val() == 1) {
        // check the view access rights for the income u/s 24 and then append these classes
        $('.page-content').addClass('custom-tab');
        $('.add-panel-padding').addClass('padding-class');
    }

    // If only vue forms incomeU/S24 or POI has view access and not all the other sub-forms in the Tax Declaration has view access, directly redirect to the income u/s 24 form
    if ($('#taxDeclarationAccess').val() != 1 && $('#hraDeclarationAccess').val() != 1) {
        if($('#incomeUnderSectionAccess').val() == 1)
            window.location.href = pageUrl() + "payroll/income-under-section24";
        else if($('#proofOfInvestmentAccess').val() == 1)
            window.location.href = pageUrl() + "payroll/proof-of-investment";
    }

    // to check tab is clicked
    var tabClicked = false;
    
    // when the income u/s 24 tab is hovered
    $('#incomeUS24Tab').on('mouseenter', function () {
        fnTabHighlight("#incomeUS24Tab", "#incomeUnderSection24Tab");
    });

    // when the POI tab is hovered
    $('#poiTab').on('mouseenter', function () {
        fnTabHighlight("#poiTab", "#proofOfInvestmentTab");
    });

    // function add tab highlighted
    function fnTabHighlight(tabId, tabBorderId){
        $('#taxDeclarationTab').removeClass('tab-active-text text-secondary');
        $('#taxDeclarationMainTab').removeClass('tab-border-cls');
        $(tabId).addClass('tab-active-text text-secondary');
        $(tabBorderId).addClass('tab-border-cls')
    };
     
    // when mouse is out of income u/s 24 tab
    $('#incomeUS24Tab').on('mouseleave', function () {
        // to check finalization tab is clicked. If yes we don't remove tab active class
        if(!tabClicked){
            $("#taxDeclarationTab").addClass('tab-active-text text-secondary');
            $('#taxDeclarationMainTab').addClass('tab-border-cls');
            $('#incomeUS24Tab').removeClass('tab-active-text text-secondary');
            $('#incomeUnderSection24Tab').removeClass('tab-border-cls')
        }
    });

    // when mouse is out of POI
    $('#poiTab').on('mouseleave', function () {
        // to check finalization tab is clicked. If yes we don't remove tab active class
        if(!tabClicked){
            $("#taxDeclarationTab").addClass('tab-active-text text-secondary');
            $('#taxDeclarationMainTab').addClass('tab-border-cls');
            $('#poiTab').removeClass('tab-active-text text-secondary');
            $('#proofOfInvestmentTab').removeClass('tab-border-cls')
        }
    });

    // income u/s 24 tab onclick function
    $('#incomeUS24Tab').on('click', function () {
        tabClicked = true;
        setMask('#wholepage');
        fnTabHighlight();
        // redirect to the income u/s 24 form
        window.location.href = pageUrl() + "payroll/income-under-section24";
    });

    // POI tab onclick function
    $('#poiTab').on('click', function () {
        tabClicked = true;
        setMask('#wholepage');
        fnTabHighlight();
        // redirect to the income u/s 24 form
        window.location.href = pageUrl() + "payroll/proof-of-investment";
    });

    function fnTaxlockConfiguration() {
        if ($('#lockSettings').val() == 'unlock') {
            $('#formInvestmentCategory,#formAmountDeclared,#formDeclarationStatus').prop('readOnly', false);
        }
        else {
            $('#formInvestmentCategory,#formAmountDeclared,#formDeclarationStatus').prop('readonly', true);
        }
    }

    function fnHralockConfiguration() { 
        if ($('#lockSettings').val() == 'unlock') {
            $('#formHraRentMonths,#formHraLandLordName,#formHraLandLordAddress,#formHraLandLordPAN,#formHraRentalAmount,#formHraDeclarationStatus').prop('readOnly', false);
        }
        else {
            $('#formHraRentMonths,#formHraLandLordName,#formHraLandLordAddress,#formHraLandLordPAN,#formHraRentalAmount,#formHraDeclarationStatus').prop('readonly', true);
        }
    }


    function revertDeclarationDetails(lineItemId,employeeId,action,gridName,formId)
    {
        $.ajax({
            type     : 'POST',
            url      : pageUrl()+'payroll/tax-declarations/revert-declaration-status/lineItemId/'+lineItemId+'/employeeId/'+employeeId+'/actionName/'+action,
            async    : false,
            dataType :"json",
            success  : function(result)
            {
                fnRefreshTable(gridName);
                if(result && isJson(result)){
                    jAlert({ panel : $(formId),msg : result.msg, type : result.type });
                }else{
                    jAlert({ panel : $(formId),msg : "Something went wrong. Please contact system admin.", type : "warning" });
                }
            },
            error  : function (revertDeclarationErrorResult){
                if(revertDeclarationErrorResult.status === 200){
                    sessionExpired ();
                }else{
                    /* To handle internal server error */
                    jAlert({ panel : $(formId),msg : "Something went wrong. Please contact system admin.", type : "warning" });
                }
            }
        });
    }


    function getEmployeeTaxRegime(employeeId,formId,modalId)
    {
        $.ajax({
            type     : 'POST',
            url      : pageUrl()+'payroll/tax-declarations/get-employee-tax-regime/employeeId/'+employeeId,
            async    : false,
            dataType :"json",
            success  : function(result)
            {
                if(result && isJson(result)){
                    taxRegime        = result.taxRegime;
                    if(taxRegime!='Old Regime')
                    {
                        taxRegimeMessage = result.msg;
                        $("#taxRegimeMessage").text(taxRegimeMessage);
                        $('#modalTaxRegime').modal('toggle');
                        //for now only for cannyhr domain we are going to change this ui 
                        // when the employee is new regime we should not allow the employee to add hra declaration
                        if(modalId=='#modalFormHraDeclaration' && domainName=='cannyhr')
                        {
                            $(modalId).modal('toggle');
                        }
                    }
                }else{
                    jAlert({ panel : $(formId),msg : "Something went wrong. Please contact system admin.", type : "warning" });
                }
            },
            error  : function (employeeTaxRegimeErrorResult){
                if(employeeTaxRegimeErrorResult.status === 200){
                    sessionExpired ();
                }else{
                    /* To handle internal server error */
                    jAlert({ panel : $(formId),msg : "Something went wrong. Please contact system admin.", type : "warning" });
                }
            }
        });
    }
});