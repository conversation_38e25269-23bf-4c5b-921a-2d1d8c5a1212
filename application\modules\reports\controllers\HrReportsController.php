<?php
//=========================================================================================
//=========================================================================================
/* Program : HrReportsController.php												     *
 * Property of Caprice Technologies Pvt Ltd,											 *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,										 *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies				 *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : HR Report controller Generate pie,bar,grid reports for HR,Employee,     *
 * Payroll modules based on details stroed in Database also can export those reports	 *
 * as pdf,csv formats and can be printable.             	 							 *
 * 																						 *
 * Revisions :                                                                    	     *
 *  Version    Date          Author                  Description                       	 *
 *  0.1       30-May-2013    Sandhosh                Initial Version         	         *
 *  0.2		  14-Apr -2014   Sandhosh                Added funtions:					 *
 *										     	     mainTable							 *
 *                                                   Modified Functions:                 *
 *                                        		     tableHeader,                        *
 *                                        		     getAttendanceImportStatus           *
 *																						 *	
 *  0.3		  04-Aug -2014	Mahesh				     Modified grid, print and pdf based	 *
 * 												     on "Retirals based on basic" flag   *
 *						    Sandhosh			   						   		         *
 *					  																	 *
 *  0.4		  01-sep-2014   Mahesh				     Modified Functions					 *
 *												     1.mainTable						 *
 *												   										 *
 *  1.0       02-Feb-2015   Prasanth                 Changed in file for mobiles app     *
 *                                                                                       *
 *  1.5       10-Feb-2016   Shanthi                  Changes in file for Bootstrap       */
//=========================================================================================
//=========================================================================================
class Reports_HrReportsController extends Zend_Controller_Action
{
    protected $_dbHRReport 			= null;
    protected $_basePath 			= null;
    protected $_reportAccessRights 	= null;
    protected $_dbAccessRights 		= null;
    protected $_logEmpId 			= null;
    protected $_dbPersonal 			= null;
    protected $_ehrTables 			= null;
    protected $_orgDateFormat 		= null;
    protected $_subHrappDb 			= null;
    protected $_dbBilling 			= null;
    protected $_orgCode 			= null;
    protected $_hrappMobile 		= null;
	protected $_dbCommonFun         = null;
    protected $_formName 			= null;
	protected $_showReportCreator   = null;
	protected $_dbPayslip 			= null;
	protected $_dbLeave   			= null;
	protected $_orgDetails   		= null;
	protected $_eftDefaultBankId    = null;
	protected $_dbManager 			= null; 
	protected $_locale              = null;
	protected $_payrollGeneralSettings  = null;
	protected $_timeOffReportsFormId = 348;
	protected $_teamSummaryFormId = 350;
	
    public function init()
    {
        $this->_hrappMobile 		= new Application_Model_DbTable_HrappMobile();
		
        if ($this->_hrappMobile->checkAuth())
        {
            $this->_dbCommonFun    	= new Application_Model_DbTable_CommonFunction();
			$userSession 			= $this->_dbCommonFun->getUserDetails ();
			$this->_logEmpId 		= $userSession['logUserId'];
            $this->_dbPersonal 		= new Employees_Model_DbTable_Personal();
            $this->_dbAccessRights 	= new Default_Model_DbTable_AccessRights();
            $this->_ehrTables 		= new Application_Model_DbTable_Ehr();
            $this->_basePath 		= new Zend_View_Helper_BaseUrl();
            $this->_dbHRReport 		= new Reports_Model_DbTable_HrReports();
			$this->_dbAttendanceReport 	= new Reports_Model_DbTable_Attendance();
            $this->_dbBilling 		= new Default_Model_DbTable_Billing();
			$this->_orgDateFormat 	= $this->_ehrTables->orgDateformat();
            $this->_orgCode 		= $this->_ehrTables->getOrgCode();
			$this->_dbPayslip = new Payroll_Model_DbTable_Payslip();
			$this->_dbLeave = new  Employees_Model_DbTable_Leave();
			$this->_dbManager           = new Default_Model_DbTable_Manager();
			$this->_locale              = $this->_dbCommonFun->getEmployeeLocale($this->_logEmpId);
			$dbSalary 				   		  = new Payroll_Model_DbTable_Salary();
			$this->_payrollGeneralSettings    = $dbSalary->getPayrollGeneralSettings();

            $this->_timeOffReportAccessRights = $this->_dbAccessRights->employeeAccessRightsBasedOnFormId($this->_logEmpId,$this->_timeOffReportsFormId);
            $this->_timeOffReportAccess       = $this->_timeOffReportAccessRights['Employee'];
            
			$this->_teamSummaryAccessRights     = $this->_dbAccessRights->employeeAccessRightsBasedOnFormId($this->_logEmpId,$this->_teamSummaryFormId);
            $this->_teamSummaryReportAccess     = $this->_teamSummaryAccessRights['Employee'];

			if (Zend_Registry::isRegistered('orgDetails'))
              $this->_orgDetails = Zend_Registry::get('orgDetails');
        }
        // else
        // {
        //     if (Zend_Session::namespaceIsset('lastRequest'))
        //         Zend_Session:: namespaceUnset('lastRequest');
            
        //     $sessionUrl = new Zend_Session_Namespace('lastRequest');
        //     $sessionUrl->lastRequestUri = '';
        //     $this->_redirect('auth');
        // }
    }

    public function indexAction()
    {
		$checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

		if ($checkSessionAuth)
		{
			$this->_helper->layout()->disableLayout()->setLayout('admin_layout');
		
			$modName = $this->_getParam('_mId', null);
			$modName = filter_var($modName,FILTER_SANITIZE_STRIPPED);
			
			$modId = $this->_getParam('_mNme', null);
			$modId = filter_var($modId, FILTER_SANITIZE_NUMBER_INT);
		   
			$linkvalue = $this->_getParam('linkValue', null);
			$linkvalue = ucwords(str_replace('-', ' ', $linkvalue));
			
			$modName = ucwords(str_replace('-', ' ', $modName));
		   
			if(!empty($modId) && !empty($modName))
			{
				$this->_reportAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $modName);
		
				if($linkvalue == 'Timesheet' || $linkvalue == 'Timesheet Comprehensive')
				{
					$dbTimesheet 	                 = new Employees_Model_DbTable_Timesheet();
					$this->view->timesheetSettings   = $dbTimesheet->getTimesheetSettings();
				}
				

				if($linkvalue == 'Esic Hourly' || $linkvalue == "Uan Based Ecr Hourly" || $linkvalue == 'Esi Hourly' || $linkvalue == 'Eft Hourly'
				|| $linkvalue === 'Hourly Wage Payslip')
				{
					/** Get the last payslip 'month' and 'year' and set in the month picker for these reports */
					$payslipMonth = $this->_dbCommonFun->getLastPayslipMonth(0,'Hourly');
					$this->view->lastPayslipMonth = $payslipMonth['previousCutoffDate'];
				}
				elseif($linkvalue == 'Esic Monthly' ||   $linkvalue == 'Uan Based Ecr' || strtolower($linkvalue)== 'uan based ecr(arrear)' || $linkvalue == 'Esi Monthly' || $linkvalue == 'Eft Monthly'  || 
				$linkvalue === 'Monthly Salary Payslip' || $linkvalue === 'Pay Bill' || $linkvalue==='Monthly Payslip Comprehensive' || $linkvalue === 'Bimonthly Salary Register' ||
				$linkvalue === 'Salary Register' || $linkvalue === 'Payment Register' || $linkvalue === 'Reimbursement Allowances' || $linkvalue === 'Insurance Statement' 
				|| $linkvalue === 'Ssnit Tier 1' || $linkvalue === 'Ssnit Tier 2' || $linkvalue === 'Provident Fund Detailed Report' || strtolower($linkvalue) === 'wps')
				{
					$payslipMonth = $this->_dbCommonFun->getLastPayslipMonth(0, 'Monthly');
					$this->view->lastPayslipMonth = $payslipMonth['previousCutoffDate'];
				}
				else
				{
					$payslipMonth = $this->_dbCommonFun->getLastPayslipMonth(0);
				   $this->view->lastPayslipMonth = $payslipMonth['previousCutoffDate'];
				}
				
				if ($this->_reportAccessRights['Employee']['View'] == 1 || $this->_timeOffReportAccess['View'] == 1 || $this->_teamSummaryReportAccess['View'] == 1)
				{
					$dbModules = new Application_Model_DbTable_Modules();
					
					$this->view->orgForm = $dbModules->formByModuleName('Organization');
					$this->view->empForm = $dbModules->formByModuleName('Employees');
					$this->view->modNme = $modName;
					$this->view->repTitles = $this->_dbHRReport->repTitles($modId , $modName);//default Driven Report,returns report titles
					
					$this->view->formName = $linkvalue;

					//we are not having the accessrights at report level
					if(in_array($linkvalue, array('Additional Wage Summary','Employee Wise Expenses','Employee Step Increment')))
					{
						$formName='Payroll Reports';
						$this->view->employeeName = $this->_dbPersonal->employeeDetail('', '', 'Emp_First_Name', 'ASC', '', '','', '', $this->_logEmpId,
																	  '', '', '', '', $formName, '', 1, 1, '');
					}
					else
					{
						$this->view->employeeName = '';
					}
					
					// display the list of manager name in drop down of filter option in leave,comp off,short time off and attendance reports
					if(in_array($linkvalue, array('Attendance','Leaves','Short Time Off','Compensatory Off','Compensatory Off Balance')))
					{					
						$this->view->managerNames = $this->_dbManager->managerName('', '', 'Emp_First_Name', 'ASC', '', '', '', '', '', 'Employees', 0);
					}
					else{
						$this->view->managerNames = '';
					}

					
					//get login employee name
					$this->view->loginEmpId= $this->_logEmpId;
					$logEmployeeDetails = $this->_dbPersonal->employeeName($this->_logEmpId);
					$logEmpName = $logEmployeeDetails['Employee_Name'];
					
					$selectedFilterVal = array();
					$selectedRelationVal = array();
				
					$selctRwData        = $this->_dbHRReport->rowDriRep($linkvalue);
					
					$this->view->bankId = $this->_dbHRReport->getActiveBankPaymentTypeId();
			
				if($linkvalue != 'Employee Wise Expenses')
					{$hrReportData = $this->_dbHRReport->filter($selectedFilterVal, $selectedRelationVal, $selctRwData,1);
					}
					else{
						$sdate = date('Y-m-01');
						$ldate = date('Y-m-t');
						$empId= $this->_logEmpId;
						//$f=array($empId,$sdate,$ldate );
						//array_push($selectedFilterVal,$f);
						$selectedFilterVal[0]=$empId;
						$selectedFilterVal[1]=$sdate;
						$selectedFilterVal[2]=$ldate;
						
						$result=$this->_dbHRReport->getEmployeeWiseExpenses($selectedFilterVal);
						$hrReportData =$result['aaData'];
					}
				
			
					if( $linkvalue == "Eft Monthly" || $linkvalue == "Eft Hourly") {
						$labelArray = array( '0' => 'Bulk Upload File For',
											 '1' => 'Debit Account Number',
											 '2' => 'Transaction Type',
											 '3' => 'Payslip Month',
											 '4' => 'Location',
											 '5' => 'Department',
											 '6' => 'Employee Type');
						$tableArray = array( '0' => 'Bulk_Upload_File_For',
											 '1' => 'Debit_Account_Number',
											 '2' => 'Transaction_Type',
											 '3' => 'MPicker',
											 '4' => 'Location',
											 '5' => 'Department',
											 '6' => 'Employee_Type');
						$filterData = $this->_dbHRReport->getEftFilterData();
						if(!empty($filterData[0])){
							$bankPaymentTypeKeys = array_keys($filterData[0]);
							//Assign this value to fetch the grid keys associated with the bank
							$eftDefaultBankId = $bankPaymentTypeKeys[0];
						}
						else
						{
							$eftDefaultBankId = 0;
						}
						
						$this->view->labelArray = $labelArray;
						$this->view->tableArray = $tableArray;
						$this->view->filterData = $filterData;
					} else {
						
						$this->view->labelArray = explode("-",$selctRwData['Rep_Filter']);
						$this->view->tableArray = explode(",",$selctRwData['Filter_Table']);
						$filterData = $this->_dbHRReport->repFilters($selctRwData,$this->_logEmpId);
						$this->view->filterData = $filterData;
					}
					
						$this->view->rowDriRep = $hrReportData;				
				
					
					
					$customPAN = $this->_ehrTables->getCustomFields('PAN No.');
					
					$pan = array();
					$pan['Enable'] =  1;
					$pan['Required'] = 0;
					$pan['Field_Name'] = 'PAN';
					
					if($customPAN['Field_Name'] == 'PAN No.')
					{
						$pan['Enable'] = $customPAN['Enable'];
						$pan['Required'] =  $customPAN['Required'];
						$pan['Field_Name'] = ($customPAN['New_Field_Name'] != '' ?  $customPAN['New_Field_Name'] : 'PAN');
					}
					
					$reqArr['grid_key'] = array();
				
					if (!empty($hrReportData[0]) && is_array($hrReportData[0]))
					{					
						unset($hrReportData[0][$selctRwData['Bar_Y']]);
						
						$arrkeys = array_keys($hrReportData[0]);
						
						//foreach($keys as $shd => $hrRept)
						for($i=0;$i<count($arrkeys);$i++)
						{
							if($arrkeys[$i] == 'PAN')
							{
								if($pan['Enable'])
								{
									$arrkeys[$i] = $pan['Field_Name'];
								}
								else
								{
									unset($arrkeys[$i]);
								}
							}
						}
						$gridKeyResponse = $this->_dbHRReport->getReportKeysUsingCount($arrkeys,$linkvalue);
						$reqArr['grid_key'] = $gridKeyResponse;
					}
					$columnArr = array();
					if($linkvalue == 'Employee Wise Expenses')
					{
						$this->view->rowDriRep = 1;
						$dbOrgDetail = new Organization_Model_DbTable_OrgSettings();
						$orgCode = $this->_ehrTables->getOrgCode();
						$sdate = date('Y-m-01');
						$ldate = date('Y-m-t');
						$empId= $this->_logEmpId;
						$expenseArr = array();
						$reqArr1 = array('Date');
	
						$expenseTitleArr = $this->_dbHRReport->getEmployeeWiseExpensesTitle($empId,$sdate,$ldate);
						
						// $reqArr2 =array('Others','Total');
						$reqArr2 =array('Total');
						
						$expenseDeductionArr = $this->_dbHRReport->getEmployeeWiseExpensesDeductionTitle($empId,$sdate,$ldate);
						
						foreach($expenseDeductionArr as $title)
						{
							array_push($expenseArr,$title." ".'Deduction');
						}
						$reqArr3=array('Total_Deduction','Payable_Amount');
						$reqArr['grid_key'] = array_merge($reqArr1,$expenseTitleArr, $reqArr2,$expenseArr,$reqArr3);
						$columnArr =$reqArr['grid_key'];
					}
					elseif($linkvalue == 'Hourly Wage Payslip')
					{
						$this->view->rowDriRep = 1;
						$reqArr['grid_key'] = array('Employee_Id','Name','Month','Total_Earnings','Total_Deductions','Net_Pay','Designation',
								'Department','Account_Number','Bank_Name','Branch_Name', 'IFSC_Code','Regular_Hours', 'OverTime_Hours','Day_Wage','Days_Worked','Holidays','Basic_Pay','Total_HourlyWages',
								'Total_OvertimeWages','Allowance','Adhoc_Allowance','Bonus','Compoff_Encashment','Shift_Allowance','Reimbursement','Bonus_Allowance','Holiday_Special_Wages',
								'Loan','Provident_Fund','Voluntary_Provident_Fund','Insurance','One-Month_Deduction','Recurring_Deduction','Group_Deduction',
								'Loan_Deduction','Professional_Tax','Tax','Outstanding_Amount');
						$columnArr = array('Employee_Id','Employee_Name','Payslip_Month','Designation','Department','Bank_Account_Number','Bank_Name','Branch_Name',
						'IFSC_Code','Days_Worked','Holidays','Total_HourlyWages','Total_OvertimeWages','Allowance','Adhoc_Allowance',
						'Bonus','Shift_Allowance','Reimbursement','Bonus_Allowance','Holiday_Special_Wages','Loan', 'Provident_Fund','Voluntary_Provident_Fund','Insurance','One-Month_Deduction','Recurring_Deduction','Group_Deduction',
						'Loan_Deduction','Professional_Tax','Tax','Total_Earnings','Total_Deductions','Outstanding_Amount','Net_Pay');
					}
					elseif($linkvalue == 'Hourly Master Report')
					{
						$this->view->rowDriRep = 1;
						$reqArr['grid_key']= array('Employee_Id', 'Name','Date_Of_Birth','Date_Of_Join','Confirmation_Date','Resignation_Date',
								'Designation', 'Department', 'Location', 'Mobile_No', 'Manager_Name', 'Regular_Wage', 'Overtime_Wage',  'Provident_Fund',
								'Variable_Insurance', 'Fixed_Insurance', 'Fixed_Allowance', 'Variable_Allowance', 'Regular_Hours',
								'OverTime_Hours', 'External_EmpId','User_Name');
						$columnArr = array('Employee_Id', 'Employee_Name', 'Date_Of_Birth','Date_Of_Join','Confirmation_Date','Resignation_Date',
								'Designation', 'Department', 'Location', 'Mobile_No', 'Manager_Name', 'Regular_Wage', 'Overtime_Wage',  'Provident_Fund',
								'Variable_Insurance', 'Fixed_Insurance', 'Fixed_Allowance', 'Variable_Allowance', 'Regular_Hours',
						
								'OverTime_Hours', 'External_EmpId','User_Name');
					}
					elseif($linkvalue == 'Monthly Master Report')
					{
						$this->view->rowDriRep = 1;
						/** Default grid keys */				
						$reqArr['grid_key'] = array('Employee_Id', 'Name','Date_Of_Birth','Probation_Date', 'Confirmation_Date','Resignation_Date',
								'Designation', 'Department', 'Location', 'Mobile_No', 'Manager_Name','Annual_Ctc','Annual_Gross_Salary',
								'Working_Days', 'Worked_Days','Paid_Unpaid_Leave', 'OverTime_Hours', 'Shortage_Hours');
						/** To present this values in print export modal */
						$columnArr1 = array('Employee_Id', 'Employee_Name','Date_Of_Birth', 'Probation_Date', 'Confirmation_Date','Resignation_Date','Designation');
						
						$columnArr2 = array('Location', 'Tax_Regime','Previous_Experience','Current_Experience','Total_Experience', 'Mobile_No', 'Manager_Name', 'Annual_Ctc', 'Annual_Gross_Salary',
											'Working_Days', 'Worked_Days','Paid_Unpaid_Leave', 'OverTime_Hours', 'Shortage_Hours');
						
						$orgStruct = $this->_dbHRReport->getOrgStructure();
						
						foreach($orgStruct as $orgDiv)
						{
							array_push($reqArr['grid_key'],$orgDiv);
							array_push($columnArr1,$orgDiv);
						}
						
						$columnArr = array_merge($columnArr1,$columnArr2);
						
					}
					elseif($linkvalue == 'Esi Monthly')
					{
						$this->view->rowDriRep = 1;
						$reqArr['grid_key'] =array('Employee_Id','Employee_Name','ESI_No','ESI_Wages','ESI_EE','ESI_ER');
					}
					elseif($linkvalue == 'Esi Hourly')
					{
						$this->view->rowDriRep = 1;
						$reqArr['grid_key'] =array('Employee_Id','Employee_Name','ESI_No','ESI_Wages','ESI_EE','ESI_ER');
					}
					elseif($linkvalue == 'Esic Monthly' || $linkvalue == 'Esic Hourly')
					{
						$this->view->rowDriRep = 1;
						$reqArr['grid_key'] =array('IP_Number','IP_Name','No_of_Days_for_which_wages_paid','Reason_Code','Total_Monthly_Wages','Last_Working_Day');
					}
					elseif($linkvalue == 'Attendance Summary Hourly')
					{
						$this->view->rowDriRep = 1;
						$reqArr['grid_key'] =array('Employee_Id','External_Employee_Id','Employee_Name','Shift_Start_Date','Shift_End_Date','Minimum_Punch_In','Maximum_Punch_Out','Regular_Hours','Overtime_Hours','Description');
					}
					elseif($linkvalue == 'Attendance Summary Monthly')
					{
						$this->view->rowDriRep = 1;
						$reqArr['grid_key'] =array('Employee_Id','External_Employee_Id','Employee_Name','Work_Schedule','Shift_Date','Day_Type','Minimum_Shift_Start_Time','Maximum_Shift_End_Time','Minimum_Punch_In','Maximum_Punch_Out','Regular_Hours','Overtime_Hours','Total_Working_Hours','Shift_Exists','Attendance_Exist','Leave_Exist','CompensatoryOff_Exist','ShortTimeOff_Exist');
					}
					elseif($linkvalue == 'Employee Utilization')
					{
						$this->view->rowDriRep = 1;
						$reqArr['grid_key'] = $this->_dbHRReport->getReportHeaders($linkvalue);
					}
					elseif($linkvalue == 'Additional Wage Summary')
					{
						$this->view->rowDriRep = 1;
						$reqArr['grid_key'] = $this->_dbHRReport->getReportHeaders($linkvalue);
					}
					elseif($linkvalue == 'Uan Based Ecr')
					{
						$this->view->rowDriRep = 1;
						$reqArr['grid_key'] =array('Uan','Member_Name','Gross_Wages','Epf_Wages','Eps_Wages','Edli_Wages','Epf_Contribution_Remitted',
							   'Eps_Contribution_Remitted','Epf_Eps_Contribution_Remitted','Ncp_Days','Refund_Of_Advances');
						$columnArr = $reqArr['grid_key'];
					}
					elseif(strtolower($linkvalue)== 'uan based ecr(arrear)')
					{
						$this->view->rowDriRep = 1;
						$reqArr['grid_key'] =array('Uan','Member_Name','Epf_Wages','Eps_Wages','Edli_Wages','Epf_Contribution_Remitted',
							   'Eps_Contribution_Remitted','Epf_Eps_Contribution_Remitted');
						$columnArr = $reqArr['grid_key'];
					}
					elseif($linkvalue == 'Uan Based Ecr Hourly')
					{
						$this->view->rowDriRep = 1;
						$reqArr['grid_key'] =array('Uan','Member_Name','Gross_Wages','Epf_Wages','Eps_Wages','Edli_Wages','Epf_Contribution_Remitted',
							   'Eps_Contribution_Remitted','Epf_Eps_Contribution_Remitted','Ncp_Days','Refund_Of_Advances');
						$columnArr = $reqArr['grid_key'];
					}
					elseif($linkvalue == 'Attendance Shortage')
					{
						$this->view->rowDriRep = 1;
						$reqArr['grid_key'] =array('Employee_Id','Name','Department','Total_Working_Hours','Total_Worked_Hours');
						$columnArr = $reqArr['grid_key'];
					}
					elseif($linkvalue == 'Absentees')
					{
						$this->view->rowDriRep = 1;
						$reqArr['grid_key'] =array('Employee_Id','Name','Department','Designation','Location','Date_Of_Absence','Absent_Duration','Absent_Period');
						$columnArr = $reqArr['grid_key'];
					}
					else if($linkvalue == 'Loan Amortization') {
						$this->view->rowDriRep = 1;
						unset($reqArr['grid_key'][2]);
						$reqArr['grid_key'] = array_values($reqArr['grid_key']);
						$columnArr = $reqArr['grid_key'];
					}
					else if($linkvalue == 'Eft Monthly' || $linkvalue=='Eft Hourly') {
						$this->view->rowDriRep = 1;
						
						$reqArr['grid_key']  = $this->_dbHRReport->eftHeader($eftDefaultBankId);
						$columnArr = $reqArr['grid_key'];
					}elseif($linkvalue == 'Lop Recovery') {
						$this->view->rowDriRep = 1;
						$reqArr['grid_key'] =array('Employee_ID','Employee_Name','Leave_Date','Deduction_Month','LOP_Recovery_Processing_Month','Status','Reason',
								'Duration','Leave_Period');
						$columnArr = $reqArr['grid_key'];
					}
					else if($linkvalue == 'Attendance And Absence Overview')
					{
						$this->view->rowDriRep = 1;
						$reqArr['grid_key']    = $this->_dbHRReport->getReportHeaders($linkvalue);
						$columnArr 			   = $reqArr['grid_key'];
					}
	
					$this->view->gridKeyArr = $reqArr['grid_key'];
				
					$this->view->columnArr	= $columnArr;
				}
				$this->view->reportUser =  $this->_reportAccessRights['Employee'];		   
				
				$basePath  = new Zend_View_Helper_BaseUrl();
				$getOrg = $this->_dbBilling->getOrgName($this->_orgCode);
				$employeeName = $this->_dbPersonal->employeeId($this->_logEmpId);
				
				$this->view->headerLogo = $this->view->headerTitle = $this->view->headerString = '';
				$this->view->headerLogoWidth = 0;
							
				if(!empty($getOrg) && count($getOrg)>0)
				{
					//$linkvalue = strtolower($linkvalue);
					$address = '';
					$panTan = '';
					if ($linkvalue == 'Tds')
					{
						$addressArr = $this->_ehrTables->getOrgAddress();
						$panTanArr = $this->_ehrTables->getPanTan();
						$customPAN = $this->_ehrTables->getCustomFields('PAN No.');
						$customTAN = $this->_ehrTables->getCustomFields('TAN No.');
						
						$tan = $pan = array();
						$tan['Enable'] = $pan['Enable'] =  1;
						$tan['Required'] = $pan['Required'] = 0;
						$tan['Field_Name'] = 'TAN :';
						$pan['Field_Name'] = 'PAN :';
						
						if($customTAN['Field_Name'] == 'TAN No.')
						{
							$tan['Enable'] = $customTAN['Enable'];
							$tan['Required'] =  $customTAN['Required'];
							$tan['Field_Name'] = ($customTAN['New_Field_Name'] != '' ?  $customTAN['New_Field_Name'].' : ' : 'TAN : ');
						}
						
						if($customPAN['Field_Name'] == 'PAN No.')
						{
							$pan['Enable'] = $customPAN['Enable'];
							$pan['Required'] =  $customPAN['Required'];
							$pan['Field_Name'] = ($customPAN['New_Field_Name'] != '' ?  $customPAN['New_Field_Name'].' : ' : 'PAN : ');
						}
						
						$brk = '';
						$address = '<div style="text-align: right;">';
						$address .= $addressArr['Street1'].', '. $addressArr['Street2'];
				
						if($addressArr['City_Name'] && $addressArr['State_Name'] && $addressArr['Country_Name'] && $addressArr['Pincode'])
							$address .= '<br>'.$addressArr['City_Name'].', '.$addressArr['State_Name'].', '. $addressArr['Country_Name'].' - '.$addressArr['Pincode'].'.</div>';
						else if($addressArr['State'] && $addressArr['Country_Name'] && $addressArr['Pincode'])
							$address .= '<br>'.$addressArr['State_Name'].', '. $addressArr['Country_Name'].' - '.$addressArr['Pincode'].'.</div>';
						else if($addressArr['Country_Name'] && $addressArr['Pincode'])
							$address .= '<br>'.$addressArr['Country_Name'].' - '.$addressArr['Pincode'].'.</div>';
						else if($addressArr['Country_Name'])
							$address .= '<br>'.$addressArr['Country_Name'].'</div>';
						else
							$address = '</div><br>';
							
						$panTan = '<div>';
						
						if($pan['Enable'] == 1)
						{
							if($panTanArr['PAN'] != null && !empty($panTanArr['PAN']))
								 $panTan .= $pan['Field_Name'].$panTanArr['PAN'];
								 //$panTan .= 'PAN : '.$panTanArr['PAN'];
						}
						if($tan['Enable'] == 1)
						{
							if($panTanArr['TAN'] != null && !empty($panTanArr['TAN']))
								$panTan .= ' '.$tan['Field_Name'].$panTanArr['TAN'];
								//$panTan .= ' TAN : '.$panTanArr['TAN'];
						}
						$panTan .= '</div><br>';
					}
					
					$this->view->address = $address;
					$this->view->panTan = $panTan;
					$this->view->headerTitle = $getOrg['RegOrg']['Org_Name'];
					
					$commonFunction = new Application_Model_DbTable_CommonFunction();
					$signedUrl = $commonFunction->getAwsSignedUrl('ReportLogo','','logoBucket');
					$dbOrgDetail = new Organization_Model_DbTable_OrgSettings();
					if(!empty($signedUrl))
					{
						$this->view->headerLogo = $signedUrl;	
					}
					$orgCode = $this->_ehrTables->getOrgCode();		
					$orgDetails = $dbOrgDetail->viewOrgDetail($orgCode);
					$this->view->orgDetails = $orgDetails;
					if($orgDetails['Show_Report_Creator']==1)
					{
					$this->view->headerString = 'Created by - '.$employeeName.'<br>';
					$this->view->headerString .= "\nCreated On - ".date($this->_orgDateFormat['php'] .' \a\t H:i:s').'<br>';
					}
					$this->view->headerString .= '<br><div style="text-align:center; font-size:15px; margin-top:10px"> This is system generated report. No signature is required.</div>'; 
				}
			}
		
			$this->view->serviceProviderId = $this->_dbCommonFun->getEmployeeServiceProviderId($this->_logEmpId,1);
			$this->view->dateformat = $dateformat =  $this->_ehrTables->orgDateformat();
		} else {
			$this->_redirect('auth');
		}
    }
	
    /**
     * in this action we creating two types of bar chart[multistack,normal] chart
     *
     * @param string $linkValue The selected navication value.
     * @param array $selectedFilterVal Available filters values.
     * @return Bar chart HTML By using High charts js plugin type:column,type:multistack
     *
     */

    public function createBarchartAction()
    {
        $this->_helper->layout()->disableLayout();
        
        if(isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('create-barchart', 'html')->initContext();
			
			$modName = $this->_getParam('_modName', null);
			$modName = filter_var($modName, FILTER_SANITIZE_STRING);
			
			$modName = $modName.' reports';
			
			$this->_reportAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $modName);
			
			if ($this->_reportAccessRights['Employee']['View'] == 1 || $this->_timeOffReportAccess['View'] == 1 || $this->_teamSummaryReportAccess['View'] == 1)
			{
				
				$linkvalue = $this->_getParam('linkValue', null);
				$linkvalue = filter_var($linkvalue,FILTER_SANITIZE_STRIPPED);
				$linkvalue = ucwords(str_replace('-', ' ', $linkvalue));				
                //$filtervalue = $this->_getParam('filterValue',null);                
                
				$tableData = $this->_getParam('tableData', null);
				if($tableData != '')
				{
					$this->view->data = $this->_dbHRReport->rowDriRep($linkvalue);
                }
				else
				{
					$filterArray = $this->_getParam('filterArray', null);
					
                    if(empty($filterArray))
                    {
                        //$filterArray = 'All';
                        $selectedFilterVal = $this->_getParam('_fv', null);
                    
                        $selectedRelationVal = $this->_getParam('_rv', null);
                    }
                    else
                    {
                        //$filterArray = implode($filterArray);
                        if($filterArray != '')
                        {
                            $selectedFilterVal = explode(',',$filterArray);
                        }
                        else
                        {
                            $selectedFilterVal = '';
                        }
                        $selectedRelationVal = array();
                        for($p=0;$p<=15;$p++)
                        {
                            array_push($selectedRelationVal,'');
                        }
                    }
					
					
					$chartHt = $this->_getParam('_ctHt', null);
					
					$chartWt = $this->_getParam('_ctWt', null);
					
					$this->view->chartHt = ($chartHt != null) ? $chartHt : 338;
					$this->view->chartWt = ($chartWt != null) ? $chartWt : 'auto';
					
					$chartRender = $this->_getParam('_ctRent', null);
					$chartRender = filter_var($chartRender, FILTER_SANITIZE_STRING);
				
					$this->view->chartRender = ($chartRender != null) ? $chartRender : 'bar';					
					
					
					$tableData = $this->_dbHRReport->rowDriRep($linkvalue);					
					
					$this->view->table_data = $tableData;
					
					//***** not statcked bar (stackedBar == false) *****//
					if ($tableData['Flag_Stack'] == 0)
					{
					   $this->view->data  = $this->_dbHRReport->filter($selectedFilterVal,$selectedRelationVal, $tableData,1);
					   
					   $this->view->a = $selectedFilterVal;
					}
					//stacked bar
					elseif ($tableData['Flag_Stack'] == 1)
					{
					   //***** specify y axis value condition get field name *****//
					   $barField = $this->_dbHRReport->barField($tableData['Bar_Field_Tbl'],$tableData['Stack_Categ_Name']);
					    $this->view->barField  = $barField;
					   // ***** sorting is different *******//
					   $this->view->data = $this->_dbHRReport->stakedBarData($selectedFilterVal, $selectedRelationVal, $tableData,$barField);
					}
				}
			}
        }
        else
        {
            $this->_helper->redirector('index', 'hr-reports', 'reports',array('_mId'=>'hr-reports','_mNme'=>10));
        }
    }
    
    /**
     * json format data to bind dynamic created grid.
     *
     * @param string $linkValue The selected navication value.
     * @param array $selectedFilterVal Available filters values.
     * @return json array
     */
    public function bindReportsAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if(isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('bind-reports', 'json')->initContext();
			
			$modName = $this->_getParam('_modName', null);
			$modName = filter_var($modName, FILTER_SANITIZE_STRING);
			$modName = $modName.' reports';
			
			$this->_reportAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $modName);
			if($this->_reportAccessRights['Employee']['View'] == 1 || !empty($this->_reportAccessRights['Admin']) || $this->_timeOffReportAccess['View'] == 1 || $this->_teamSummaryReportAccess['View'] == 1)
			{
				$linkvalue = $this->_getParam('linkValue', null);
				$linkvalue = filter_var($linkvalue,FILTER_SANITIZE_STRIPPED);
				$linkvalue = ucwords(str_replace('-', ' ', $linkvalue));
				
				$filterArray = $this->_getParam('filterArray', null);
			
				$sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
			
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
			 
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$selFilterVal = array();
				$selRelationVal = array();
				$selRwData = $this->_dbHRReport->rowDriRep($linkvalue);
			if($linkvalue != 'Employee Wise Expenses')
			{
				$hrReportData = $this->_dbHRReport->filter(array(), array(), $selRwData,1);
				
				$reqArr = array();
			 
				if (!empty($hrReportData[0]) && is_array($hrReportData[0]))
				{
					unset($hrReportData[0][$selRwData['Bar_Y']]);
					$reqArr = array_keys($hrReportData[0]);
				}
			}	
				if($filterArray != '')
				{
					$selectedFilterVal = explode(',',$filterArray);
				}
				else
				{
					$selectedFilterVal = '';
				}
				
				$selectedRelationVal = array();
			 
				//15 filter values can be addded.data pushed
			   	for($p=0;$p<=15;$p++)
			  	{
					array_push($selectedRelationVal,'');
				}
				
				$check = $this->_getParam('check', null);
			   
				$page = $this->_getParam('iDisplayStart', null);
                
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
				if(!empty($linkvalue))
				{
					if($linkvalue == "Esi Monthly"){
						$linkvalue = "ESI Monthly";
					}else if($linkvalue == "Esi Hourly")
					{
						$linkvalue = "ESI Hourly";
					}
					else if($linkvalue == "Esic Monthly") {
						$linkvalue = "ESIC Monthly";
					} else if($linkvalue == "Esic Hourly") {
						$linkvalue = "ESIC Hourly";
					} else if($linkvalue == "Eft Monthly") {
						$linkvalue = "Eft Monthly";
					} else if($linkvalue == "Eft Hourly") {
						$linkvalue = "Eft Hourly";
					} 

				
					$this->view->table_data = $tableData = $this->_dbHRReport->rowDriRep($linkvalue);
					 
					if($linkvalue == 'Hourly Wage Payslip')
					{
						
						//$reqArr = array('','Employee_Name','Payslip_Month','Net_Pay','Net_Pay' ,'Net_Pay','Designation');
						$reqArr = array('','P.Employee_Id','P.Emp_First_Name','STR_TO_DATE(WP.Salary_Month,"%m,%Y")','Total_Earnings','Total_Deductions' ,'WP.Total_Salary','D.Designation');
						for($i=0;$i<count($reqArr);$i++)
						{
							switch ($sortField)
							{
								case $i:  $sortField = $reqArr[$i]; break;
							}
						}
						$this->view->data = $this->_dbHRReport->getWagePayslipDetails($selectedFilterVal,$tableData,$page,$rows,$sortField,$sortOrder,$searchAll);
					}	
					elseif($linkvalue === 'Employee Status')
					{
						$employeeStatus 	  		= $this->_dbHRReport->getEmployeeStatusDetails($selectedFilterVal);
						$this->view->data 			= $employeeStatus;
					}
					else if($linkvalue == 'Monthly Master Report')
					{
						$reqArr = array('','Employee_Id', 'Employee_Name','STR_TO_DATE(job.Date_Of_Join,"%Y-%m-%d")', 'STR_TO_DATE(job.Confirmation_Date,"%Y-%m-%d")', 'STR_TO_DATE(Res.Resignation_Date,"%Y-%m-%d")');
						for($i=0;$i<count($reqArr);$i++)
						{
							switch ($sortField)
							{
								case $i:  $sortField = $reqArr[$i]; break;
							}
						}
				
						$this->view->data = $this->_dbHRReport->getMasterDetails($selectedFilterVal,$tableData,$page,$rows,$sortField,$sortOrder,$searchAll);
					}
					elseif($linkvalue == 'Hourly Master Report')
					{
						$reqArr = array('','Employee_Id', 'Employee_Name', 'Date_Of_Birth','Date_Of_Join', 'Resignation_Date','Confirmation_Date');
						for($i=0;$i<count($reqArr);$i++)
						{
							switch ($sortField)
							{
								case $i:  $sortField = $reqArr[$i]; break;
							}
						}
						$this->view->data = $this->_dbHRReport->getHourlyDetails($selectedFilterVal,$tableData,$page,$rows,$sortField,$sortOrder,$searchAll);
					}
					elseif($linkvalue == 'ESI Monthly')
					{
						$reqArr = array('Employee_Id','Employee_Id','Employee_Name','ESI_No','ESI_Wages','ESI_EE','ESI_ER');
						for($i=0;$i<count($reqArr);$i++)
						{
							switch ($sortField)
							{
								case $i:  $sortField = $reqArr[$i]; break;
							}
						}
						$this->view->data =$this->_dbHRReport->getEsiDetails($selectedFilterVal,$tableData,$page,$rows,$sortField,$sortOrder,$linkvalue,$searchAll);
					}
					elseif($linkvalue == 'ESI Hourly')
					{
						$reqArr = array('Employee_Id','Employee_Id','Employee_Name','ESI_No','ESI_Wages','ESI_EE','ESI_ER');
						for($i=0;$i<count($reqArr);$i++)
						{
							switch ($sortField)
							{
								case $i:  $sortField = $reqArr[$i]; break;
							}
						}
						$this->view->data =$this->_dbHRReport->getEsiHourlyDetails($selectedFilterVal,$tableData,$page,$rows,$sortField,$sortOrder,$linkvalue,$searchAll);
					}
					elseif($linkvalue == 'ESIC Monthly' || $linkvalue == 'ESIC Hourly')
					{
						$reqArr = array('IP_Number','IP_Number','IP_Name','No_of_Days_for_which_wages_paid','Reason_Code','Total_Monthly_Wages');
						for($i=0;$i<count($reqArr);$i++)
						{
							switch ($sortField)
							{
								case $i:  $sortField = $reqArr[$i]; break;
							}
						}
						$this->view->data =$this->_dbHRReport->getEsicDetails($selectedFilterVal,$tableData,$page,$rows,$sortField,$sortOrder,$linkvalue);
					}
					elseif($linkvalue == 'Attendance Summary Hourly')
					{
						$reqArr =array('Employee_Id','External_Employee_Id','Employee_Name','Shift_Start_Date','Shift_End_Date','Minimum_Punch_In','Maximum_Punch_Out','Regular_Hours','Overtime_Hours','Description');
						for($i=0;$i<count($reqArr);$i++)
						{
							switch ($sortField)
							{
								case $i:  $sortField = $reqArr[$i]; break;
							}
						}
						$this->view->data =$this->_dbHRReport->getAttendanceSummaryHourly($selectedFilterVal,$tableData,$page,$rows,$sortField,$sortOrder,$linkvalue);
					}
					elseif($linkvalue == 'Attendance Summary Monthly')
					{
						$reqArr=array('Employee_Id','External_Employee_Id','Employee_Name','Work_Schedule','Shift_Date','Day_Type','Minimum_Shift_Start_Time','Maximum_Shift_End_Time','Minimum_Punch_In','Maximum_Punch_Out','Regular_Hours','Overtime_Hours','Total_Working_Hours','Shift_Exists','Attendance_Exist','Leave_Exist','CompensatoryOff_Exist','ShortTimeOff_Exist');

						for($i=0;$i<count($reqArr);$i++)
						{
							switch ($sortField)
							{
								case $i:  $sortField = $reqArr[$i]; break;
							}
						}
						$this->view->data =$this->_dbHRReport->getAttendanceSummaryMonthly($selectedFilterVal,$tableData,$page,$rows,$sortField,$sortOrder,$linkvalue);
					}
					elseif($linkvalue == 'Employee Utilization')
					{
						$this->view->data =$this->_dbHRReport->getEmployeeUtilization($selectedFilterVal);
					}
					elseif($linkvalue == 'Additional Wage Summary')
					{
						$this->view->data =$this->_dbHRReport->getAdditionalWageSummary($selectedFilterVal);
					}
					elseif($linkvalue == 'Uan Based Ecr')
					{
						$reqArr=array('Uan','Uan','Member_Name','Gross_Wages','Epf_Wages','Eps_Wages','Edli_Wages');
						
						for($i=0;$i<count($reqArr);$i++)
						{
							switch ($sortField)
							{
								case $i:  $sortField = $reqArr[$i]; break;
							}
						}
                        
                        if(empty($selectedFilterVal))
                        {
                            $curMnth = ((date('m')- 1) < 10 ) ? "0".(date('m')- 1) : (date('m')- 1);
                            $currentMonth = (date('Y')).'-'.$curMnth;
                            //$selectedFilterVal = ",".$currentMonth;
                            $selectedFilterVal = array();
                            array_push($selectedFilterVal,"",$currentMonth);
                        }
                        
						$result = $this->_dbHRReport->getUanEcrDetails($selectedFilterVal,$tableData,$page,$rows,$sortField,$sortOrder,$linkvalue);
						$this->view->data = $result;	
						
					}
					elseif(strtolower($linkvalue)== 'uan based ecr(arrear)')
					{
						$reqArr=array('Uan','Uan','Member_Name','Gross_Wages','Epf_Wages','Eps_Wages','Edli_Wages');
						
						for($i=0;$i<count($reqArr);$i++)
						{
							switch ($sortField)
							{
								case $i:  $sortField = $reqArr[$i]; break;
							}
						}
                        
                        if(empty($selectedFilterVal))
                        {
                            $curMnth = ((date('m')- 1) < 10 ) ? "0".(date('m')- 1) : (date('m')- 1);
                            $currentMonth = (date('Y')).'-'.$curMnth;
                            //$selectedFilterVal = ",".$currentMonth;
                            $selectedFilterVal = array();
                            array_push($selectedFilterVal,"",$currentMonth);
                        }
                        
						$result = $this->_dbHRReport->getArrearProvidentFundEcrReport($selectedFilterVal);
						$this->view->data = $result;	
						
					}
					elseif($linkvalue == 'Uan Based Ecr Hourly')
					{
						$reqArr=array('Uan','Uan','Member_Name','Gross_Wages','Epf_Wages','Eps_Wages','Edli_Wages');
						
						for($i=0;$i<count($reqArr);$i++)
						{
							switch ($sortField)
							{
								case $i:  $sortField = $reqArr[$i]; break;
							}
						}
                        
                        if(empty($selectedFilterVal))
                        {
                            $curMnth = ((date('m')- 1) < 10 ) ? "0".(date('m')- 1) : (date('m')- 1);
                            $currentMonth = (date('Y')).'-'.$curMnth;
                            //$selectedFilterVal = ",".$currentMonth;
                            $selectedFilterVal = array();
                            array_push($selectedFilterVal,"",$currentMonth);
                        }
                        
						$result = $this->_dbHRReport->getUanBasedEcrHourly($selectedFilterVal,$tableData,$page,$rows,$sortField,$sortOrder,$linkvalue);
						$this->view->data = $result;	
						
					}
					else if($linkvalue == 'Absentees')
					{
						$result =	$this->_dbHRReport->getAttendanceMusterReportDetails($selectedFilterVal,$linkvalue);
						$this->view->data = $result;
					}
					else if($linkvalue == 'Attendance Muster Info')
					{
						$reqArr=array('','User_Defined_EmpId','Emp_First_Name','Department_Name','Designation_Name','Employee_Email','Mobile_No');

						for($i=0;$i<count($reqArr);$i++)
						{
							switch ($sortField)
							{
								case $i:  $sortField = $reqArr[$i]; break;
							}
						}
						$result =	$this->_dbHRReport->getAttendanceMusterReportDetails($selectedFilterVal,$linkvalue,'No',$sortField,$sortOrder);
						$this->view->data = $result;
					}
					elseif($linkvalue == 'Attendance Register')
					{
						$reqArr=array('','User_Defined_EmpId','Emp_First_Name','Department_Name','Designation_Name','Employee_Email','Mobile_No');

						for($i=0;$i<count($reqArr);$i++)
						{
							switch ($sortField)
							{
								case $i:  $sortField = $reqArr[$i]; break;
							}
						}
						$result =	$this->_dbHRReport->getAttendanceMusterReportDetails($selectedFilterVal,$linkvalue,'No',$sortField,$sortOrder);
						$this->view->data = $result;
					}
					else if($linkvalue == 'Attendance And Absence Overview')
					{
						$filterGroupBy=NULL;
						$result =	$this->_dbHRReport->getAttendanceComprehensive($selectedFilterVal,$filterGroupBy);
						$this->view->data = $result;
					}
					/**
					 * Expenses report for employee wise
					 */
					else if($linkvalue == 'Employee Wise Expenses')
					{
						$result= $this->_dbHRReport->getEmployeeWiseExpenses($selectedFilterVal,$tableData,$page,$rows,$sortField,$sortOrder,$searchAll,null,$linkvalue);
						
						$this->view->data = $result;
					}
					elseif($linkvalue == 'Attendance Shortage')
					{
						$reportUser  = array('Is_Manager'  	=> $this->_reportAccessRights['Employee']['Is_Manager'],
										 'View'       	 	=> $this->_reportAccessRights['Employee']['View'],
										 'Optional_Choice'	=> $this->_reportAccessRights['Employee']['Optional_Choice'],
										 'Admin'      		=> $this->_reportAccessRights['Admin'],
										 'LogId' 	        => $this->_logEmpId,
										 'Employee_Name' 	=> $this->_dbPersonal->employeeId($this->_logEmpId));
						
						$curMnth = ((date('m')- 1) < 10 ) ? "0".(date('m')- 1) : (date('m')- 1);
                        $currentMonth = (date('Y')).'-'.$curMnth;		
						
						if($filterArray != '')
						{
						$result = $this->_dbAttendanceReport->attendanceShortageReport($page, $rows, $sortField, $sortOrder, $this->_logEmpId,
																			 $searchAll, $filterArray, $reportUser, $selectedFilterVal[1]);
						}
						else
						{
							$result = $this->_dbAttendanceReport->attendanceShortageReport($page, $rows, $sortField, $sortOrder, $this->_logEmpId,
																			 $searchAll, $filterArray, $reportUser, $currentMonth);
						}
						
						$this->view->data = $result;
					}
					elseif($linkvalue == 'Eft Monthly' || $linkvalue == 'Eft Hourly') {
						$eftHeader = $this->_dbHRReport->eftHeader($selectedFilterVal[0]);
						
						$reqArr = array();
						for($i=0;$i<count($eftHeader);$i++) 
						{
							array_push($reqArr, $eftHeader[$i]);
						}
						for($i=0;$i<count($reqArr);$i++)
						{
							switch ($sortField)
							{
								case $i:  $sortField = $reqArr[$i]; break;
							}
						}
						$this->view->data = $this->_dbHRReport->getEftDetails($selectedFilterVal,$reqArr,$page,$rows,$sortField,$sortOrder,$linkvalue,$searchAll);
					}else if($linkvalue == 'Lop Recovery') {
						$result= $this->_dbHRReport->getLOPRecoveryReportDetails($selectedFilterVal,$tableData,$page,$rows,$sortField,$sortOrder,$searchAll,$linkvalue);
						$this->view->data = $result;
					}
					elseif($linkvalue == 'Pt Annual Return(Form 5a)')
					{						
						$this->view->data = $this->_dbHRReport->getProfessionalTaxSummary($selectedFilterVal, $sortField, $sortOrder, $page, $rows);
					}
					else 
					{
						//if we have more then 2 columns then we have a expand button at first position
						if(count($reqArr)> 2) {
							array_unshift($reqArr, " ");
						}
						
						for($i=0;$i<count($reqArr);$i++)
						{
                            if($reqArr[$i] == 'PAN')
                            {
                                $customPAN = $this->_ehrTables->getCustomFields('PAN No.');
                
                                $pan = array();
                                $pan['Enable'] =  1;
                                $pan['Required'] = 0;
                                $pan['Field_Name'] = 'PAN';
                                
                                if($customPAN['Field_Name'] == 'PAN No.')
                                {
                                    $pan['Enable'] = $customPAN['Enable'];
                                    $pan['Required'] =  $customPAN['Required'];
                                    $pan['Field_Name'] = ($customPAN['New_Field_Name'] != '' ?  $customPAN['New_Field_Name'] : 'PAN');
                                }
                    
                                if($pan['Enable'])
                                {
                                    $reqArr[$i] =  $pan['Field_Name'];
                                }
                                else
                                {
                                    unset( $reqArr[$i]);
                                }
                            }
                             
							if($linkvalue == 'attendance')
							{
								if($reqArr[$i] == 'Date_In')
									$reqArr[$i] = 'PunchIn_Date';
								elseif($reqArr[$i] == 'Date_Out')
									$reqArr[$i] = 'PunchOut_Date';
							}
							 
							switch ($sortField)
							{
								case $i:  $sortField = $reqArr[$i]; break;
							}
						}
						$this->view->data = $this->_dbHRReport->filterWtPagination($selectedFilterVal,$selectedRelationVal,$tableData,$page,$rows,$sortField,$sortOrder,$searchAll,$filterArray,$this->_locale);
					}	
				}
				else
				{
					$this->view->data = array("iTotalRecords" => 0, "iTotalDisplayRecords" => 0, "aaData" => array());
				}
			}
			else
			{
				$this->view->data = array("iTotalRecords" => 0, "iTotalDisplayRecords" => 0, "aaData" => array());
			}
        }
        else
        {
            $this->_helper->redirector('index', 'hr-reports', 'reports',array('_mId'=>'hr-reports','_mNme'=>10));
        }
    }

	/**
	 * Export as csv
	 */
    public function exportCsvAction()
    {
        $this->_helper->layout()->disableLayout();
		if(isset($_SERVER['HTTP_REFERER']) || $_COOKIE['isNativeMobileApp'] == 1)
        {
            
			$modName = $this->_getParam('_modName', null);
			$modName = filter_var($modName, FILTER_SANITIZE_STRING);
			$modName = $modName.' reports';
			
			$this->_reportAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $modName);
			$linkvalue = $this->_getParam('linkValue', null);
			$linkvalue = filter_var($linkvalue,FILTER_SANITIZE_STRIPPED);
			$linkvalue = ucwords(str_replace('-', ' ', $linkvalue));

			$reportTitle = '';
			//we are calling the export csv function from the timesheet form.
			if(($linkvalue=='Timesheet' || $linkvalue == 'Timesheet Comprehensive')&& $this->_reportAccessRights['Employee']['Optional_Choice'] == 0)
			{
				$this->_reportAccessRights['Employee']['Optional_Choice'] = 1;
			}

			if($this->_reportAccessRights['Employee']['Optional_Choice'] == 1 || $this->_timeOffReportAccess['View'] == 1 || $this->_teamSummaryReportAccess['View'] == 1)
			{
				$filterArray = $this->_getParam('filterArray', null);;
				$selectedFilterVal = explode(',',$filterArray);
		
				$checkedColumns    = $this->_getParam('checkedColumns', null);
				if(!empty($checkedColumns))
					$selectedColumnList = explode(',',$checkedColumns);
				else
					$selectedColumnList = array();

				$selectedRelationVal = array();
				for($p=0;$p<=15;$p++)
				{
					array_push($selectedRelationVal,'');
				}
				$filterGroupBy = $this->_getParam('filterGroupBy', null);
				$filterGroupBy = filter_var($filterGroupBy,FILTER_SANITIZE_STRIPPED);

				if(!empty($linkvalue))
				{
					if($linkvalue == "Esi Monthly")
					{
						$linkvalue = "ESI Monthly";
					}else if($linkvalue == "Esi Hourly") {
						$linkvalue = "ESI Hourly";
					}else if($linkvalue == "Esic Monthly") {
						$linkvalue = "ESIC Monthly";
					} else if($linkvalue == "Esic Hourly") {
						$linkvalue = "ESIC Hourly";
					} elseif($linkvalue == "Eft Monthly") {
						$linkvalue = "Eft Monthly";
					} elseif($linkvalue == "Eft Hourly") {
						$linkvalue = "Eft Hourly";
					}

					

                    $address1 = '';
                    $address2 = '';
                    $panTan = '';
                    if ($linkvalue == 'Tds')
                    {
                        $addressArr = $this->_ehrTables->getOrgAddress();
                        $panTanArr = $this->_ehrTables->getPanTan();
                        
                        $customPAN = $this->_ehrTables->getCustomFields('PAN No.');
                        $customTAN = $this->_ehrTables->getCustomFields('TAN No.');
                        
                        $tan = $pan = array();
                        $tan['Enable'] = $pan['Enable'] =  1;
                        $tan['Required'] = $pan['Required'] = 0;
                        $tan['Field_Name'] = 'TAN :';
                        $pan['Field_Name'] = 'PAN :';
                        
                        if($customTAN['Field_Name'] == 'TAN No.')
                        {
                            $tan['Enable'] = $customTAN['Enable'];
                            $tan['Required'] =  $customTAN['Required'];
                            $tan['Field_Name'] = ($customTAN['New_Field_Name'] != '' ?  $customTAN['New_Field_Name'].' : ' : 'TAN : ');
                        }
                        
                        if($customPAN['Field_Name'] == 'PAN No.')
                        {
                            $pan['Enable'] = $customPAN['Enable'];
                            $pan['Required'] =  $customPAN['Required'];
                            $pan['Field_Name'] = ($customPAN['New_Field_Name'] != '' ?  $customPAN['New_Field_Name'].' : ' : 'PAN : ');
                        }
                        
                        $address1 = $addressArr['Street1'].', '. $addressArr['Street2'];
                
                        if($addressArr['City_Name'] && $addressArr['State_Name'] && $addressArr['Country_Name'] && $addressArr['Pincode'])
                            $address2 .= $addressArr['City_Name'].', '.$addressArr['State_Name'].', '. $addressArr['Country_Name'].' - '.$addressArr['Pincode'];
                        else if($addressArr['State'] && $addressArr['Country_Name'] && $addressArr['Pincode'])
                            $address2 .= $addressArr['State_Name'].', '. $addressArr['Country_Name'].' - '.$addressArr['Pincode'];
                        else if($addressArr['Country_Name'] && $addressArr['Pincode'])
                            $address2 .= $addressArr['Country_Name'].' - '.$addressArr['Pincode'];
                        else if($addressArr['Country_Name'])
                            $address2 .= $addressArr['Country_Name'];
                        
                        if($pan['Enable'] == 1)
                        {    
                            if($panTanArr['PAN'] != null && !empty($panTanArr['PAN']))
                                $panTan .= $pan['Field_Name'].$panTanArr['PAN'];
                                //$panTan .= 'PAN : '.$panTanArr['PAN'];
                        }
                        if($tan['Enable'] == 1)
                        {
                            if($panTanArr['TAN'] != null && !empty($panTanArr['TAN']))
                                $panTan .= ' '.$tan['Field_Name'].$panTanArr['TAN'];
                                //$panTan .= ' TAN : '.$panTanArr['TAN'];
                        }
                            
                    }
                    $this->view->address1 = $address1;
                    $this->view->address2 = $address2;
                    $this->view->pantan = $panTan;
					$tableData = $this->_dbHRReport->rowDriRep($linkvalue);
                    
					
					if($linkvalue == 'Monthly Salary Payslip')
					{
						$reportHeader           = array();
						$payslipId 			  	= $this->_dbHRReport->getMonthlyPayslipId($selectedFilterVal,$linkvalue);
						$headerDetails 			= $this->_dbHRReport->getReportHeaders($linkvalue,$payslipId,NULL,$this->_payrollGeneralSettings);
						$getPayslipDetails 		= $this->_dbHRReport->getPayslipDetails($payslipId,$headerDetails,$this->_payrollGeneralSettings);
					  	$data 					= $getPayslipDetails['aaData'];
						$footer 				= $getPayslipDetails['footer'];
						if(count($selectedColumnList) > 0){
							foreach ($selectedColumnList as $k=>$col) {
								$reportHeader[$k] = $headerDetails[$col];
							}
						}

						if($selectedFilterVal[1]==$selectedFilterVal[2])
						{
							$salaryMonthTo 	 = '';
						}
						else
						{
							$salaryMonthTo 	 = date('F Y', strtotime($selectedFilterVal[2]));
						}

						$salaryMonthFrom = date('F Y', strtotime($selectedFilterVal[1]));    
						$reportTitle 	 = strtoupper($linkvalue.' FOR THE MONTH OF '.$salaryMonthFrom);
						if(!empty($salaryMonthTo))
						{
							$reportTitle = strtoupper($reportTitle.' TO '.$salaryMonthTo);
						}
						$customFieldDetails = $this->_dbHRReport->getCustomFieldReportHeader($reportHeader,$data);
						unset($reportHeader);
						unset($data);
						$reportHeader 		= $customFieldDetails['reportHeader'];
						$data 		  		= $customFieldDetails['reportValue'];
						$this->view->reportTitle 	= $reportTitle;
						$this->view->data 			= $data;
						$this->view->keyValues 		= $reportHeader;
						$this->view->footer 		= $footer;
						unset($getPayslipDetails);
						unset($headerDetails);
						unset($customFieldDetails);
					}
					elseif($linkvalue === 'Payment Register')
					{
						$payslipId 	  				= $this->_dbHRReport->getMonthlyPayslipId($selectedFilterVal,$linkvalue,$filterGroupBy);
						$headerDetails 				= $this->_dbHRReport->getReportHeaders($linkvalue,$payslipId,NULL,$this->_payrollGeneralSettings);
						$getPayslipDetails 			= $this->_dbHRReport->getPaymentRegisterDetails($payslipId,$this->_payrollGeneralSettings);
						if(count($selectedColumnList) > 0){
						
							foreach ($selectedColumnList as $k=>$col) {
								$reportHeader[$k] = $headerDetails[$col];
							}
						}
						$customFieldDetails 		= $this->_dbHRReport->getCustomFieldReportHeader($reportHeader,$getPayslipDetails['aaData']);
						$reportHeader 				= $customFieldDetails['reportHeader'];
						$data 		  				= $customFieldDetails['reportValue'];
						$salaryMonth 				= strtoupper(date('F Y', strtotime($selectedFilterVal[1])));
						$payslipMonth 				= 'PAYMENT REGISTER FOR THE MONTH OF '.$salaryMonth;
						$reportTitle 				= 'PAYMENT REGISTER '.$salaryMonth;
						$this->view->reportTitle 	= $reportTitle;
						$this->view->data 			= $data;
						$this->view->keyValues 		= $reportHeader;
						$this->view->footer 		= $getPayslipDetails['footer'];
						unset($getPayslipDetails);
						unset($headerDetails);
						unset($customFieldDetails);
					}
					else if($linkvalue == 'Insurance Statement')
					{
						$reportHeader              = array();
					    $payslipId 			  	   = $this->_dbHRReport->getMonthlyPayslipId($selectedFilterVal,$linkvalue);
                        $headerDetails 			   = $this->_dbHRReport->getReportHeaders($linkvalue,$payslipId);
						$actualHeaderDetails 	   = $headerDetails['Actual_Header_Details'];
						$presentationHeaderDetails = $headerDetails['Presentation_Header_Details'];
						$presentationReportHeader  = array();
						if(count($selectedColumnList) > 0){
							foreach ($selectedColumnList as $k=>$col) {
								$reportHeader[$k] = $actualHeaderDetails[$col];
								$presentationReportHeader[$k] = $presentationHeaderDetails[$col];
							}
						}

						$getPayslipDetails 		= $this->_dbHRReport->getFixedInsuranceDetails($payslipId,$reportHeader);
					  	$data 					= $getPayslipDetails['aaData'];
						$footer 				= $getPayslipDetails['footer'];
						$salaryMonthFrom 		= date('F Y', strtotime($selectedFilterVal[1]));    
						$reportTitle 	 		= strtoupper($linkvalue.' FOR THE MONTH OF '.$salaryMonthFrom);
						
						$this->view->reportTitle 	= $reportTitle;
						$this->view->data 			= $data;
						$this->view->keyValues 		= $reportHeader;
						$this->view->presentationHeaderDetails = $presentationReportHeader;
						$this->view->footer 		= $footer;
					}
					else if($linkvalue == 'Ssnit Tier 1' || $linkvalue == 'Ssnit Tier 2')
					{
						$reportHeader              = array();
					    $payslipId 			  	   = $this->_dbHRReport->getMonthlyPayslipId($selectedFilterVal,$linkvalue);
                        $headerDetails 			   = $this->_dbHRReport->getReportHeaders($linkvalue,$payslipId);
						
						if(count($selectedColumnList) > 0){
							foreach ($selectedColumnList as $k=>$col) {
								$reportHeader[$k] = $headerDetails[$col];
							}
						}

						$getPayslipDetails 		= $this->_dbHRReport->getVariableInsuranceDetails($payslipId,$reportHeader);
					  	$data 					= $getPayslipDetails['aaData'];
						$footer 				= $getPayslipDetails['footer'];
						$salaryMonthFrom 		= date('F Y', strtotime($selectedFilterVal[1]));    
						$reportTitle 	 		= strtoupper($linkvalue.' FOR THE MONTH OF '.$salaryMonthFrom);
						
						$this->view->reportTitle 	= $reportTitle;
						$this->view->data 			= $data;
						$this->view->keyValues 		= $headerDetails;
						$this->view->footer 		= $footer;
					}
					else if($linkvalue === 'Provident Fund Detailed Report')
					{
						$reportHeader              = array();
					    $payslipId 			  	   = $this->_dbHRReport->getMonthlyPayslipId($selectedFilterVal,$linkvalue);
                        $headerDetails 			   = $this->_dbHRReport->getReportHeaders($linkvalue,$payslipId);
						
						if(count($selectedColumnList) > 0){
							foreach ($selectedColumnList as $k=>$col) {
								$reportHeader[$k] = $headerDetails[$col];
							}
						}

						$getPayslipDetails 		= $this->_dbHRReport->getProvidentFundReport($payslipId,$reportHeader);
					  	$data 					= $getPayslipDetails['aaData'];
						$footer 				= $getPayslipDetails['footer'];
						$salaryMonthFrom 		= date('F Y', strtotime($selectedFilterVal[1]));    
						$reportTitle 	 		= strtoupper($linkvalue.' FOR THE MONTH OF '.$salaryMonthFrom);
						
						$this->view->reportTitle 	= $reportTitle;
						$this->view->data 			= $data;
						$this->view->keyValues 		= $headerDetails;
						$this->view->footer 		= $footer;
					}
					else if($linkvalue == 'Reimbursement Allowances')
					{
						$reportHeader           		= array();
					    $reimbursementAllowanceDetails 	= $this->_dbHRReport->getReimbursementAllowances($selectedFilterVal,$linkvalue);
						$this->view->data 				= $reimbursementAllowanceDetails;
						$this->view->keyValues			= array_keys($reimbursementAllowanceDetails[0]);
						$this->view->reportTitle 		= $linkvalue;
						$this->view->footer 			= '';
					}
					else if($linkvalue == 'Hourly Wage Payslip')
					{
						$getWagePayslipDetails = $this->_dbHRReport->getWagePayslipDetails($selectedFilterVal, null, null, null, null, null);
						$keyValues = array('Employee_Id','Employee_Name','Payslip_Month','Designation','Department','Bank_Account_Number','Bank_Name','Branch_Name',
										   'IFSC_Code','Regular_Hours', 'OverTime_Hours','Day_Wage','Days_Worked','Holidays','Total_HourlyWages','Total_OvertimeWages',
										   'Allowance','Adhoc_Allowance','Bonus','Compoff_Encashment','Shift_Allowance','Reimbursement',
										   'Bonus_Allowance','Holiday_Special_Wages','Loan','Provident_Fund','Voluntary_Provident_Fund','Insurance','One-Month_Deduction',
										   'Recurring_Deduction','Group_Deduction','Loan_Deduction','Professional_Tax','Tax','Total_Earnings','Total_Deductions',
										   'Outstanding_Amount','Net_Pay', 'Payment_Status');
						$this->view->keyValues = $keyValues;
						$data = array();
					
						$data = $getWagePayslipDetails['aaData'];
						$this->view->data = $data;
						
						//for setting the footer row
						$getWagePayslipDetails['footer'][0]['Department'] = 'Total';
						$this->view->footer =  $getWagePayslipDetails['footer'];
						
					}
					else if($linkvalue == 'Monthly Master Report')
					{
						$getMasterDetails = $this->_dbHRReport->getMasterDetails($selectedFilterVal, null, null, null, null, null);
						
						$keyValues1 = array('Employee_Id', 'Employee_Name','Date_Of_Birth','Probation_Date', 'Confirmation_Date','Resignation_Date','Designation');
										   
						$keyValues2 = array('Location','Tax_Regime','Previous_Experience','Current_Experience','Total_Experience','Mobile_No', 'Manager_Name',
									 'Annual_Ctc','Annual_Gross_Salary','Working_Days', 'Worked_Days','Paid_Unpaid_Leave', 'OverTime_Hours', 'Shortage_Hours');
						
						$orgStruct = $this->_dbHRReport->getOrgStructure();
					
						foreach($orgStruct as $orgDiv)
						{
							array_push($keyValues1,$orgDiv);
						}
						
						$keyValues = array_merge($keyValues1,$keyValues2);

						$this->view->keyValues = $keyValues;
						$data = array();
						
						$data = $getMasterDetails['aaData'];
						$this->view->data = $data;
						//for setting the footer row
						$getMasterDetails['footer'][0]['Manager_Name'] = 'Total';
						$this->view->footer =  $getMasterDetails['footer'];
					}	
					
					elseif($linkvalue == 'Hourly Master Report')
					{
						$getMasterDetails = $this->_dbHRReport->getHourlyDetails($selectedFilterVal, null, null, null, null, null);
						$this->view->keyValues = array('Employee_Id', 'Employee_Name','Date_Of_Birth','Date_Of_Join','Resignation_Date','Confirmation_Date', 
								'Designation', 'Department', 'Location','Mobile_No', 'Manager_Name', 'Regular_Wage', 'Overtime_Wage',
								'Provident_Fund','Variable_Insurance', 'Fixed_Insurance','Regular_Hours', 'OverTime_Hours', 'External_EmpId','User_Name');
						$this->view->data = $getMasterDetails['aaData'];
						//for setting the footer row
						$getMasterDetails['footer'][0]['Manager_Name'] = 'Total';
						$this->view->footer =  $getMasterDetails['footer'];
					}

					elseif($linkvalue == "ESI Monthly")
					{
						$getEsiDetails = $this->_dbHRReport->getEsiDetails($selectedFilterVal, null, null, null, null, null,$linkvalue,null,null,$filterGroupBy);
						$payslipIds = array_column($getEsiDetails['aaData'],'Payslip_Id');
						$finalKeyValue = $this->_dbHRReport->getReportHeaders($linkvalue,$payslipIds);
										
						if(count($selectedColumnList) > 0){
							foreach ($selectedColumnList as $k=>$col) {
								$reportHeader[$k] = $finalKeyValue[$col];
							}
						}else{
							$reportHeader = $finalKeyValue;
						}
						$this->view->keyValues = $reportHeader;
						$result= $getEsiDetails['aaData'];
						$this->view->data = $result;

						//for setting the footer row
						$getEsiDetails['footer'][0]['Employee_Id'] = '';
						$getEsiDetails['footer'][0]['Employee_Name'] = '';
						$getEsiDetails['footer'][0]['ESI_No'] = '';

						$getEsiDetails['footer'][0]['Salary_Month'] = 'Total';
						$this->view->footer =  $getEsiDetails['footer'];

					}
					elseif($linkvalue == "ESI Hourly")
					{
						$getEsiDetails = $this->_dbHRReport->getEsiHourlyDetails($selectedFilterVal, null, null, null, null, null,$linkvalue);
						$keyValueOne = $data = array('Employee_Id','Employee_Name','ESI_No','Salary_Month','Total_HourlyWages','Total_OvertimeWages');
						$keyValueTwo = array('Holiday_SpecialWages','ESI_Wages','ESI_EE','ESI_ER');

						$payslipIds = array_column($getEsiDetails['aaData'],'Payslip_Id');
						$getAllowanceDetails = $this->_dbHRReport->getPayslipAllowances('Hourly',$payslipIds,'Variable Insurance');
						$getAdhocAllowanceDetails = $this->_dbHRReport->getPayslipAdhocAllowances('Hourly',$payslipIds,'Variable Insurance');
                        if(empty($getAdhocAllowanceDetails))
                            $getAdhocAllowanceDetails = array();
                        
                        if(empty($getAllowanceDetails))
							$getAllowanceDetails = array();
							
						for($i=0;$i<count($getAdhocAllowanceDetails);$i++)
						{                            
							if(in_array($getAdhocAllowanceDetails[$i],$getAllowanceDetails))
							{
								$getAdhocAllowanceDetails[$i] = $getAdhocAllowanceDetails[$i].'_Adhoc';
							}                            
						}

						$finalKeyValue = array_merge($keyValueOne, $getAllowanceDetails, $getAdhocAllowanceDetails,$keyValueTwo);
						$this->view->keyValues = $finalKeyValue;
						$result= $getEsiDetails['aaData'];
						$this->view->data = $result;

						//for setting the footer row
						$getEsiDetails['footer'][0]['Employee_Id'] = '';
						$getEsiDetails['footer'][0]['Employee_Name'] = '';
						$getEsiDetails['footer'][0]['ESI_No'] = '';

						$getEsiDetails['footer'][0]['Salary_Month'] = 'Total';
						$this->view->footer =  $getEsiDetails['footer'];

					} 
					elseif($linkvalue == 'ESIC Monthly' || $linkvalue == 'ESIC Hourly')
					{
						$getEsicDetails = $this->_dbHRReport->getEsicDetails($selectedFilterVal, null, null, null, null, null,$linkvalue);
						$this->view->keyValues = $data = array('IP_Number','IP_Name','No_of_Days_for_which_wages_paid','Reason_Code','Total_Monthly_Wages','Last_Working_Day');
						$this->view->data = $getEsicDetails['aaData'];
					}
					elseif($linkvalue == 'Attendance Summary Hourly')
					{
						$getAttendanceSummaryHourly =$this->_dbHRReport->getAttendanceSummaryHourly($selectedFilterVal,null, null, null, null, null,$linkvalue);
						$this->view->keyValues = $data = array('Employee_Id','External_Employee_Id','Employee_Name','Shift_Start_Date','Shift_End_Date','Minimum_Punch_In','Maximum_Punch_Out','Regular_Hours','Overtime_Hours','Description');
						$this->view->data = $getAttendanceSummaryHourly['aaData'];
					}
					elseif($linkvalue == 'Attendance Summary Monthly')
					{
						$getAttendanceSummaryMonthly =$this->_dbHRReport->getAttendanceSummaryMonthly($selectedFilterVal,null, null, null, null, null,$linkvalue);
						$this->view->keyValues = $data = array('Employee_Id','External_Employee_Id','Employee_Name','Work_Schedule','Shift_Date','Day_Type','Minimum_Shift_Start_Time','Maximum_Shift_End_Time','Minimum_Punch_In','Maximum_Punch_Out','Regular_Hours','Overtime_Hours','Total_Working_Hours','Shift_Exists','Attendance_Exist','Leave_Exist','CompensatoryOff_Exist','ShortTimeOff_Exist');
						$this->view->data = $getAttendanceSummaryMonthly['aaData'];
					}
					elseif($linkvalue == 'Employee Utilization')
					{
						$getEmployeeUtilization =$this->_dbHRReport->getEmployeeUtilization($selectedFilterVal);
						$this->view->keyValues = $data = $this->_dbHRReport->getReportHeaders($linkvalue);
						$this->view->data = $getEmployeeUtilization['aaData'];
					}
					elseif($linkvalue == 'Additional Wage Summary')
					{
						$getAdditionalWageSummary =$this->_dbHRReport->getAdditionalWageSummary($selectedFilterVal);
						$this->view->keyValues = $data = $this->_dbHRReport->getReportHeaders($linkvalue);
						$this->view->data = $getAdditionalWageSummary['aaData'];
						$this->view->footer =  $getAdditionalWageSummary['footer'];
					}
					elseif($linkvalue == 'Attendance Muster Info')
					{
						$getAttendanceSummaryMonthly =$this->_dbHRReport->getAttendanceMusterReportDetails($selectedFilterVal,$linkvalue,'Yes');
						$attendanceMusterKeys = array_keys($getAttendanceSummaryMonthly['aaData'][0]);
						$this->view->keyValues = $data = $attendanceMusterKeys;
						$this->view->data = $getAttendanceSummaryMonthly['aaData'];
					}
					elseif($linkvalue == 'Attendance Register')
					{
						$getAttendanceSummaryMonthly =$this->_dbHRReport->getAttendanceMusterReportDetails($selectedFilterVal,$linkvalue,'Yes');
						$attendanceRegisterKeys = array_keys($getAttendanceSummaryMonthly['aaData'][0]);
						$this->view->keyValues = $data = $attendanceRegisterKeys;
						$this->view->data = $getAttendanceSummaryMonthly['aaData'];
					}
					elseif($linkvalue == 'Attendance And Absence Overview')
					{
						$getAttendanceSummaryMonthly 	= $this->_dbHRReport->getAttendanceComprehensive($selectedFilterVal,$filterGroupBy);
						$attendanceMusterKeys 			= array_keys($getAttendanceSummaryMonthly['aaData'][0]);
						$date 							= explode('&',$selectedFilterVal[1]);
						$attendanceDate     			= date($this->_orgDateFormat['php'], strtotime($date[0]));
						$reportTitle 					= $linkvalue.' as on '.$attendanceDate;
						$this->view->keyValues 			= $data = $attendanceMusterKeys;
						$this->view->data 				= $getAttendanceSummaryMonthly['aaData'];
						$this->view->reportTitle 	    = $reportTitle;
					}
					elseif($linkvalue == 'Uan Based Ecr')
					{
						$getUanEcrDetails = $this->_dbHRReport->getUanEcrDetails($selectedFilterVal, null, null, null, null, null,$linkvalue);
						
						$this->view->keyValues = $data = array('Uan','Member_Name','Gross_Wages','Epf_Wages','Eps_Wages','Edli_Wages','Epf_Contribution_Remitted',
						   'Eps_Contribution_Remitted','Epf_Eps_Contribution_Remitted','Ncp_Days','Refund_Of_Advances');
						
						$this->view->data = $getUanEcrDetails['aaData'];
					}
					elseif(strtolower($linkvalue)== 'uan based ecr(arrear)')
					{
						$getUanEcrDetails = $this->_dbHRReport->getArrearProvidentFundEcrReport($selectedFilterVal);
						
						$this->view->keyValues = $data = array('Uan','Member_Name','Epf_Wages','Eps_Wages','Edli_Wages','Epf_Contribution_Remitted',
						   'Eps_Contribution_Remitted','Epf_Eps_Contribution_Remitted');
						
						$this->view->data = $getUanEcrDetails['aaData'];
					}
					elseif($linkvalue == 'Uan Based Ecr Hourly')
					{
						$getUanEcrDetails = $this->_dbHRReport->getUanBasedEcrHourly($selectedFilterVal, null, null, null, null, null,$linkvalue);
						
						$this->view->keyValues = $data = array('Uan','Member_Name','Gross_Wages','Epf_Wages','Eps_Wages','Edli_Wages','Epf_Contribution_Remitted',
						   'Eps_Contribution_Remitted','Epf_Eps_Contribution_Remitted','Ncp_Days','Refund_Of_Advances');
						
						$this->view->data = $getUanEcrDetails['aaData'];
					}
					else if($linkvalue == 'Absentees')
					{
						$getAttendanceSummaryMonthly =$this->_dbHRReport->getAttendanceMusterReportDetails($selectedFilterVal,$linkvalue);
						$this->view->data = $getAttendanceSummaryMonthly['aaData'];
						$this->view->keyValues = array_keys($getAttendanceSummaryMonthly['aaData'][0]);
					}
					elseif($linkvalue == 'Eft Monthly' || $linkvalue == 'Eft Hourly')
					{
						$eftHeader = $this->_dbHRReport->eftHeader($selectedFilterVal[0]);
						$reqArr = array();
						for($i=0;$i<count($eftHeader);$i++) 
						{
							array_push($reqArr, $eftHeader[$i]);
						}
						$this->view->keyValues  = $reqArr;
						$dataValues = $this->_dbHRReport->getEftDetails($selectedFilterVal,$reqArr,null,null,null,null,$linkvalue,null);
						$this->view->data = $dataValues['aaData'];
					}
                    elseif($linkvalue == 'Loan Amortization')
					{
                        $loanId = $this->_getParam('loanId', null);
						$getLoanDetails = $this->_dbHRReport->loanAmortization($loanId);
						
						$this->view->keyValues = $data = array('Month','Loan_Balance','Interest_Amount','Principal_Amount','EMI','Outstanding_Amount');
						
						$this->view->data = $getLoanDetails[1];

						$this->view->loanData = $getLoanDetails[0];
					}
					elseif($linkvalue == 'Attendance Shortage')
					{						
						$reqArr =array('Employee_Id','Name','Department','Total_Working_Hours','Total_Worked_Hours');
												
						$this->view->keyValues = $reqArr;
						
						$reportUser = array('Is_Manager'  	=> $this->_reportAccessRights['Employee']['Is_Manager'],
										 'View'       	 	=> $this->_reportAccessRights['Employee']['View'],
										 'Optional_Choice'	=> $this->_reportAccessRights['Employee']['Optional_Choice'],
										 'Admin'      		=> $this->_reportAccessRights['Admin'],
										 'LogId' 	        => $this->_logEmpId,
										 'Employee_Name' 	=> $this->_dbPersonal->employeeId($this->_logEmpId));
                
						$attendanceReport = $this->_dbAttendanceReport->attendanceShortageReport('','','','','','',$filterArray,$reportUser,'');
						
						$this->view->data = $attendanceReport['aaData'];
					}
						
					//get Employeewise expenses for give date range
					elseif($linkvalue == 'Employee Wise Expenses')
					{
						$empId=$selectedFilterVal[0];
						$sdate =$selectedFilterVal[1];
						$ldate =$selectedFilterVal[2];

						$this->view->sdate = date('d/m/Y',strtotime($this->_ehrTables->changeDateformat($sdate)));
						$this->view->ldate = date('d/m/Y',strtotime($this->_ehrTables->changeDateformat($ldate)));
						$getExpenseEmployeeDetails=$this->_dbHRReport->getExpenseEmployeeDetails($empId);
						$this->view->getExpenseEmployeeDetails = $getExpenseEmployeeDetails;

						$expenseArr = array();
						$reqArr1 = array('Date');
						$expenseTitleArr = $this->_dbHRReport->getEmployeeWiseExpensesTitle($empId,$sdate,$ldate);
						// $reqArr2 =array('Others','Total');
						$reqArr2 =array('Total');
						
						 $expenseDeductionArr = $this->_dbHRReport->getEmployeeWiseExpensesDeductionTitle($empId,$sdate,$ldate);
						
						foreach($expenseDeductionArr as $title)
						{
							array_push($expenseArr,$title." ".'Deduction');
						}
						$reqArr3=array('Total_Deduction','Payable_Amount');
						$reqArr = array_merge($reqArr1,$expenseTitleArr, $reqArr2,$expenseArr,$reqArr3);

						$this->view->keyValues = $reqArr;
					
						$expensesReport=$this->_dbHRReport->getEmployeeWiseExpenses($selectedFilterVal);
						
						$this->view->data = $expensesReport['aaData'];

					}
					else if($linkvalue == 'Reimbursement' )
					{
								
						$result = $this->_dbHRReport->filterWtFooter($selectedFilterVal,$selectedRelationVal,$tableData);
					
						foreach($result as $key => $value)
						{
							if(isset($result[$key]['Documents']))
							{
								$documentNames = $result[$key]['Documents'];
								unset($result[$key]['Documents']);
								if(!empty($documentNames))
								{
									foreach($documentNames as $index => $res)
									{
										$result[$key]['Documents'] = '=HYPERLINK('.'"'. $res . '","' . $index . '")';
									}
								}
							}
						}
						 $this->view->data = $result;
					}
					elseif($linkvalue==='Pay Bill')
					{
						$payslipId 	  				= $this->_dbHRReport->getMonthlyPayslipId($selectedFilterVal,$linkvalue);
						$headerDetails 				= $this->_dbHRReport->getReportHeaders($linkvalue,$payslipId);
						$getPayslipDetails 			= $this->_dbHRReport->getPayBillDetails($payslipId,$headerDetails);
						$salaryMonth 				= strtoupper(date('F Y', strtotime($selectedFilterVal[1])));
						$payslipMonth 				= 'PAY BILL FOR THE MONTH OF '.$salaryMonth;
						$reportTitle 				= 'PAY_BILL_'.$salaryMonth;
						$this->view->reportTitle 	= $reportTitle;
						$this->view->data 			= $getPayslipDetails['aaData'];
						$this->view->keyValues 		= $headerDetails;
						$this->view->footer 		= $getPayslipDetails['footer'];
					}
					elseif($linkvalue==='Monthly Payslip Comprehensive')
					{
						$payslipId 	  				= $this->_dbHRReport->getMonthlyPayslipId($selectedFilterVal,$linkvalue);
						$headerDetails 				= $this->_dbHRReport->getReportHeaders($linkvalue,$payslipId);
						$getPayslipDetails 			= $this->_dbHRReport->getMonthlyPayslipComprehensiveDetails($payslipId);
					  	$salaryMonth 				= strtoupper(date('F Y', strtotime($selectedFilterVal[1])));
						$payslipMonth 				= 'MONTHLY PAYSLIP COMPREHENSIVE FOR THE MONTH OF '.$salaryMonth;
						$reportTitle 				= 'MONTHLY_PAYSLIP_COMPREHENSIVE '.$salaryMonth;
						$this->view->reportTitle 	= $reportTitle;
						$this->view->data 			= $getPayslipDetails['aaData'];
						$this->view->keyValues 		= $headerDetails;
						$this->view->footer 		= $getPayslipDetails['footer'];
					}
					elseif($linkvalue==='Monthly Salary')
					{
						$getMonthlySalary 	  		= $this->_dbHRReport->getMonthlySalary($selectedFilterVal);
						$headerDetails 				= $this->_dbHRReport->getReportHeaders($linkvalue,NULL);
						$this->view->reportTitle 	= $linkvalue;
						$this->view->data 			= $getMonthlySalary['aaData'];
						$this->view->keyValues 		= $headerDetails;
						$this->view->footer 		= $getMonthlySalary['footer'];
					}
					elseif($linkvalue === 'Salary Register')
					{
						$payslipId 	  				= $this->_dbHRReport->getMonthlyPayslipId($selectedFilterVal,$linkvalue);
						$headerDetails 				= $this->_dbHRReport->getReportHeaders($linkvalue,$payslipId,NULL,$this->_payrollGeneralSettings);
						$getPayslipDetails 			= $this->_dbHRReport->getSalaryRegisterDetails($payslipId,$headerDetails,$this->_payrollGeneralSettings);
						$salaryMonth 				= strtoupper(date('F Y', strtotime($selectedFilterVal[1])));
						
						if($selectedFilterVal[1]==$selectedFilterVal[2])
						{
							$salaryMonthTo 	 = '';
						}
						else
						{
							$salaryMonthTo 	 = date('F Y', strtotime($selectedFilterVal[2]));
						}

						$salaryMonthFrom = date('F Y', strtotime($selectedFilterVal[1]));    
						$reportTitle 	 = strtoupper($linkvalue.' FOR THE MONTH OF '.$salaryMonthFrom);
						if(!empty($salaryMonthTo))
						{
							$reportTitle = strtoupper($reportTitle.' TO '.$salaryMonthTo);
						}


						$this->view->reportTitle 	= $reportTitle;
						$this->view->data 			= $getPayslipDetails['aaData'];
						$this->view->keyValues 		= $headerDetails;
						$this->view->footer 		= $getPayslipDetails['footer'];
						unset($getPayslipDetails);
						unset($headerDetails);
					}
					elseif($linkvalue === 'Bimonthly Salary Register')
					{
						$payrollPeriod 				= strtolower($this->_orgDetails['Payroll_Period']);
						$payslipId 	  				= $this->_dbHRReport->getMonthlyPayslipId($selectedFilterVal,$linkvalue,NULL,$payrollPeriod);
						$headerDetails 				= $this->_dbHRReport->getReportHeaders($linkvalue,$payslipId,NULL,$this->_payrollGeneralSettings);
						$getPayslipDetails 			= $this->_dbHRReport->getSalaryRegisterDetails($payslipId,$headerDetails,$this->_payrollGeneralSettings,$payrollPeriod);
						$salaryMonth 				= strtoupper(date('F Y', strtotime($selectedFilterVal[1])));
						
						if($selectedFilterVal[1]==$selectedFilterVal[2])
						{
							$salaryMonthTo 	 = '';
						}
						else
						{
							$salaryMonthTo 	 = date('F Y', strtotime($selectedFilterVal[2]));
						}

						$salaryMonthFrom = date('F Y', strtotime($selectedFilterVal[1]));    
						$reportTitle 	 = strtoupper($linkvalue.' FOR THE MONTH OF '.$salaryMonthFrom);
						if(!empty($salaryMonthTo))
						{
							$reportTitle = strtoupper($reportTitle.' TO '.$salaryMonthTo);
						}


						$this->view->reportTitle 	= $reportTitle;
						$this->view->data 			= $getPayslipDetails['aaData'];
						$this->view->keyValues 		= $headerDetails;
						$this->view->footer 		= $getPayslipDetails['footer'];
						unset($getPayslipDetails);
						unset($headerDetails);
					}
					elseif($linkvalue === 'Employee Status')
					{
						$employeeStatus 	  		= $this->_dbHRReport->getEmployeeStatusDetails($selectedFilterVal);
						$headerDetails 				= $this->_dbHRReport->getReportHeaders($linkvalue,NULL);
						$this->view->reportTitle 	= $linkvalue;
						$this->view->data 			= $employeeStatus['aaData'];
						$this->view->keyValues 		= $headerDetails;
					}
					else if($linkvalue == 'Lop Recovery')
					{
						$lopRecoveryDetails= $this->_dbHRReport->getLOPRecoveryReportDetails($selectedFilterVal,'','','','','','',$linkvalue);
						$this->view->data = $lopRecoveryDetails['aaData'];
						$this->view->keyValues = array('Employee_ID','Employee_Name','Leave_Date','Deduction_Month','LOP_Recovery_Processing_Month','Status','Reason',
						'Duration','Leave_Period');
					}
					elseif(strtolower($linkvalue) ==='wps')
					{
						$wpsReportDetails =$this->_dbHRReport->getWPSReportDetails($selectedFilterVal,$linkvalue);
						$wpsReportKeys = array_keys($wpsReportDetails['aaData'][0]);
						$this->view->keyValues = $data = $wpsReportKeys;
						$this->view->data = $wpsReportDetails['aaData'];
					}
					else if($linkvalue == 'Bank Salary Statement') {
						$payslipId 			  	= $this->_dbHRReport->getMonthlyPayslipId($selectedFilterVal,$linkvalue,$filterGroupBy);
						$headerDetails 			= $this->_dbHRReport->getReportHeaders($linkvalue,$payslipId,NULL,NULL);
						$getPayslipDetails 		= $this->_dbHRReport->getBankSalaryStatement($payslipId);

						if(count($selectedColumnList) > 0){
							foreach ($selectedColumnList as $k=>$col) {
								$reportHeader[$k] = $headerDetails[$col];
							}
						}
						$data 					= $getPayslipDetails['aaData'];
						$footer 				= $getPayslipDetails['footer'];						

						if($selectedFilterVal[1]==$selectedFilterVal[2])
						{
							$salaryMonthTo 	 = '';
						}
						else
						{
							$salaryMonthTo 	 = date('F Y', strtotime($selectedFilterVal[2]));
						}

						$salaryMonthFrom = date('F Y', strtotime($selectedFilterVal[1]));    
						$reportTitle 	 = strtoupper($linkvalue.' FOR THE MONTH OF '.$salaryMonthFrom);
						if(!empty($salaryMonthTo))
						{
							$reportTitle = strtoupper($reportTitle.' TO '.$salaryMonthTo);
						}
					
						$this->view->reportTitle 	= $reportTitle;
						$this->view->data 			= $data;
						$this->view->keyValues 		= $reportHeader;
						$this->view->footer 		= $footer;
						unset($getPayslipDetails);
						unset($headerDetails);
					}
					elseif(strtolower($linkvalue) == 'pt annual return(form 5a)')
					{
						$professionalTaxSummary =$this->_dbHRReport->getProfessionalTaxSummary($selectedFilterVal);
						$professionalTaxSummaryKeys = array_keys($professionalTaxSummary['aaData'][0]);
						$this->view->keyValues = $data = $professionalTaxSummaryKeys;
						$this->view->data = $professionalTaxSummary['aaData'];
						$this->view->footer = $professionalTaxSummary['footerDetails'];
						$this->view->address1 = $professionalTaxSummary['mainBranchAddress'];
					}
					else
					{
						$result = $this->_dbHRReport->filterWtFooter($selectedFilterVal,$selectedRelationVal,$tableData,$filterGroupBy);
						if($linkvalue == 'Timesheet Comprehensive')
						{
							$result= $this->_dbHRReport->getTimesheetComprehensiveFooter($result);
						}

						if($linkvalue == 'Employee Step Increment')
						{
							$employeeStepIncrementDetails = $this->_dbHRReport->getEmployeeStepIncrementHeader($result);
							$result= $employeeStepIncrementDetails['data'];
							$this->view->commonData= $employeeStepIncrementDetails['common'];
						}

						//If the report is Fixed Health Insurance, add the serial number
						if($linkvalue == 'Fixed Health Insurance')
						{
							foreach ($result as $index => &$item) {
								$item = array_merge(['S.No' => $index + 1], $item);
							}							
						}

						$this->view->data = $result;

						//Report have the selected columns to export
						$exportReportWithSelectedColumns = array('Lwf','Professional Tax Monthly');
						if(in_array($linkvalue,$exportReportWithSelectedColumns)){
							$headerDetails = $this->_dbHRReport->getReportHeaders($linkvalue);

							if(count($selectedColumnList) > 0){
								foreach ($selectedColumnList as $k=>$col) {
									$reportHeader[$k] = $headerDetails[$col];
								}
							}else{
								$reportHeader = $headerDetails;
							}
							$this->view->keyValues 		= $reportHeader;
						}
					}

					$this->view->linkvalue 		= $linkvalue;
					$showReportCreator          = $this->_orgDetails['Show_Report_Creator'];
					if($showReportCreator==1)
					{
						$createdByDetails  = $this->_dbPersonal->employeeName($this->_logEmpId);
						$createdBy 	       = 'Created by - '.$createdByDetails['Employee_Name'];
						$createdOn 		   = 'Created On - '.date($this->_orgDateFormat['php'] .' \a\t H:i:s');
					}
					else
					{
						$createdBy='';
						$createdOn='';
					}
					
					$organizationName 	   = strtoupper($this->_orgDetails['Org_Name']);
					
					// Group By is optional in some reports. If Group By values are present, data will be processed and prepared in the backend; otherwise, raw data will be sent to the UI for export.
					// Group By is non mandatory in ESI Monthly report. But the excel data will be formed in the backend
					$groupByNonMandatoryReports = array('Bank Salary Statement' , 'Lwf'  , 'Professional Tax Monthly');
					$isFilterGroupByExist = (!empty($filterGroupBy) && !is_null($filterGroupBy) && ($filterGroupBy!='null')); 

					if ($linkvalue == 'Tds' || $linkvalue === 'Payment Register' || $linkvalue == 'Insurance Statement' ||  $linkvalue == 'ESI Monthly'
					|| $linkvalue == 'Ssnit Tier 1' || $linkvalue == 'Ssnit Tier 2' || $linkvalue === 'Provident Fund Detailed Report'
					|| $linkvalue == 'Reimbursement Allowances' || $linkvalue == 'Hourly Wage Payslip' || $linkvalue == 'Monthly Master Report'
					|| $linkvalue == 'Hourly Master Report' || $linkvalue == "ESI Hourly"
					|| $linkvalue == 'ESIC Monthly' || $linkvalue == 'ESIC Hourly' || $linkvalue == 'Attendance Summary Hourly'
					|| $linkvalue == 'Attendance Summary Monthly' || $linkvalue == 'Employee Utilization' || $linkvalue == 'Additional Wage Summary'
					|| $linkvalue == 'Uan Based Ecr'|| strtolower($linkvalue)== 'uan based ecr(arrear)' || $linkvalue == 'Uan Based Ecr Hourly'
					|| $linkvalue == 'Eft Monthly' || $linkvalue == 'Eft Hourly' || $linkvalue == 'Loan Amortization'
					|| $linkvalue == 'Attendance Shortage' || $linkvalue == 'Employee Wise Expenses' || $linkvalue == 'Reimbursement'
					|| $linkvalue === 'Pay Bill' || $linkvalue === 'Monthly Payslip Comprehensive' || $linkvalue==='Monthly Salary'
					|| $linkvalue === 'Employee Status' || $linkvalue === 'Lop Recovery' || $linkvalue === 'Employee Step Increment'
					||  strtolower($linkvalue) === 'pt annual return(form 5a)'
					|| ($isFilterGroupByExist && in_array($linkvalue,$groupByNonMandatoryReports))) 
					{
						$this->view->linkValue = $linkvalue;
						$this->view->employeeLocale 	= $this->_locale;
						$this->view->filterGroupBy  	= $filterGroupBy;
						$this->view->table_data 		= $tableData;
						$this->view->createdBy 			= $createdBy;
						$this->view->createdOn 			= $createdOn;
						$this->view->organizationName 	= $organizationName;
						$this->view->showReportCreator 	= $showReportCreator;
						$this->view->isFilterGroupByExist = $isFilterGroupByExist;
						// unset($this->view->createdBy, $this->view->createdOn, $this->view->organizationName,$this->view->showReportCreator,$this->view->data);
						// unset($this->view->employeeLocale, $this->view->filterGroupBy, $this->view->table_data);
						// unset($this->_locale, $filterGroupBy, $tableData);
					}
					else
					{
						$fileExportData = $this->view->data;
						if (isset($this->view->footer)) 
						{
							$footerData     = $this->view->footer;
							unset($this->view->footer);
						}
						else
						{
							$footerData     = '';
						}

						if (isset($this->keyValues)) {
							$headerDetails = $this->keyValues;
							unset($this->keyValues);
						} else {
							// Extract headers directly from the first row of fileExportData
							$headerDetails = array_keys($fileExportData[0]);
						}

						$fileHeader = [];

						if(count($selectedColumnList) > 0){
							foreach ($selectedColumnList as $k=>$col) {
								$reportHeader[$k] = $headerDetails[$col];
							}
						}
						else
						{
							$reportHeader = $headerDetails;
						}

						foreach ($reportHeader as $header) {
							// Create fileHeader directly without creating a temporary array ($heading)
							$fileHeader[] = [
								'header' => str_replace('_', ' ', $header),
								'key' => $header
							];
						}

						if(strtolower($linkvalue)==strtolower('Form 24Q Annexure II'))
						{
							$annexture2Details = array('(330)','(331)','(332A)','(332B)','(333)','(334)','(335)','(336)','(337)','(338)','(339)','(340)','(341)','(342)','(343)','(344)','(345)','(346A)','(346B)','(347)','(348)','(349)','(350)','(351)','(352)','(353)','(354)','(355)','(356)','(357)','(358)','(359)','(360)','(361)','(362)','(363)','(364A)','(364B)','(365A)','(365B)','(366A)','(366B)','(367A)','(367B)','(368)','(369)','(370)','(371)','(372)','(373)','(374)','(375)','(376)','(377)','(378)','(379)','(380)','(381)','(382)','(383)','(384)','(385)','(386)','(387)','(388)','(389)','(390)');
							$annexture2FormulaDetails = array('','','','','','','','','','','','','','','','','','','','(340+341+342+343+344+346A+346B)','','','','[338+339-(347+348+349+350)]','','','(354)','(351+352+354)','','','','','','','','','','','','','','','','','','','','','','','','','','','','[357+359+361(limited to Rs.1,50,000)+363+364B+365B+366B+367B+369+372+375+378]','(355-379)','','','','','','[381+383+384-(382+385)]','','','(387+388)','(386-389)');
							
							$headerReference = [];
							foreach ($annexture2Details as $annexture2) {
								// Create array with header and key
								$headerReference[] = [
									'header' => $annexture2,
									'key' => $annexture2
								];
							}

							$headerFormula = [];
							foreach ($annexture2FormulaDetails as $annexture2Formula) {
								// Create array with header and key
								$headerFormula[] = [
									'header' => $annexture2Formula,
									'key' => $annexture2Formula
								];
							}
						}
						else
						{
							$headerReference = array();
							$headerFormula = array();
						}

						$firstHeaderDetails = array();
						$firstHeaderData 	= array();
						if(strtolower($linkvalue)=='wps')
						{
							if(!empty($wpsReportDetails))
							{
								$firstHeaderDetails = str_replace("_"," ",array_keys($wpsReportDetails['employerAccountDetails']));
								$firstHeaderData    = array_values($wpsReportDetails['employerAccountDetails']);
							}
						}
						
						if(empty($reportTitle))
						{
						   $reportTitle = $linkvalue;
						}

						$result = array('fileExportData'   => $fileExportData,
										'fileHeader'       => $fileHeader,
										'headerReference'  => $headerReference,
										'headerFormula'    => $headerFormula,
										'footerData'       => $footerData,
										'createdBy'        => $createdBy,
										'createdOn'        => $createdOn,
										'reportTitle'      => $reportTitle,
										'organizationName' => $organizationName,
										'firstHeaderDetails'=> $firstHeaderDetails,
										'firstHeaderData' 	=> $firstHeaderData,
										'showReportCreator'=> $showReportCreator);

						// Unset variables that are no longer needed
						unset($fileExportData, $reportHeader, $fileHeader);
						$this->view->result = $result;
					}
					
				}
			}
			else
			{
				$sessionUrl = new Zend_Session_Namespace('lastRequest');
				$this->_redirect($sessionUrl->lastRequestUri);
			}
        } else
        {
            $this->_helper->redirector('index', 'hr-reports', 'reports',array('_mId'=>'hr-reports','_mNme'=>10));
        }
    }
    
	/**
     * in this action we exporting selected things (pie,bar,chart) from Reports->graphs->export as pdf
     * if user slect either one chart type and grid ,this action is will get load only after cretion of HTML Chart to .png img after that ,
     *
     * in this action
     * we'l create pdf write gereated image.
     *
     * then download as pdf user can save them
     * @param string $linkValue The selected navication value.
     * @param array $selectedFilterVal Available filters values.
     * @param char $disGrid should display grid in pdf (y/n)
     */
    public function exportPdfAction()
    {
        $this->_helper->layout()->disableLayout();
        
		if(isset($_SERVER['HTTP_REFERER']) || $_COOKIE['isNativeMobileApp'] == 1)
        {
            $modName = $this->_getParam('_modName', null);
			$modName = filter_var($modName, FILTER_SANITIZE_STRING);
			$modName = $modName.' reports';
			
			$this->_reportAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $modName);
			
			if($this->_reportAccessRights['Employee']['Optional_Choice'] == 1)
			{
				$linkvalue = $this->_getParam('linkValue', null);
				$linkvalue = filter_var($linkvalue,FILTER_SANITIZE_STRIPPED);
				$linkvalue = ucwords(str_replace('-', ' ', $linkvalue));
				
				$disGrid   = $this->_getParam('disGrid', null);
				$disGrid   = filter_var($disGrid, FILTER_SANITIZE_STRIPPED);
				
				$bsvg      = $this->_getParam('bsvg', null);
				$psvg      = $this->_getParam('psvg', null);
				
				$selectedFields   	 = $this->_getParam('_ckedValue', null);
				
				$filterArray    	 = $this->_getParam('filterArray', null);
				
				$selectedFilterVal 	 = explode(',',$filterArray);
				$selectedRelationVal = array();
				for($p=0;$p<=15;$p++)
				{
					array_push($selectedRelationVal,'');
				}
				if(!empty($linkvalue))
				{	
					if($linkvalue == "Esi Monthly")
					{
						$linkvalue = "ESI Monthly";
					}else if($linkvalue == "Esi Hourly")
					{
						$linkvalue = "ESI Hourly";
					}else if($linkvalue == "Esic Monthly") {
						$linkvalue = "ESIC Monthly";
					} else if($linkvalue == "Esic Hourly") {
						$linkvalue = "ESIC Hourly";
					} elseif($linkvalue == 'Eft Monthly') {
						$linkvalue = "Eft Monthly";
					}
					
					$logEmpName    = $this->_dbPersonal->employeeName($this->_logEmpId);//getting the details of logged employee to display report generated by ;
					$getOrg 	   = $this->_dbBilling->getOrgName($this->_orgCode);
					$orgCode = $this->_ehrTables->getOrgCode();
					//To do footer
					//$this->view->footerString = '';
					$dbOrgDetail = new Organization_Model_DbTable_OrgSettings();
					$showReporCreator = $dbOrgDetail->viewOrgDetail($orgCode);
					
					if($showReporCreator['Show_Report_Creator']==1)
					{
						
						$headerString  = 'Created by - '.$logEmpName['Employee_Name'];
						$headerString .= "\nCreated On - ".date($this->_orgDateFormat['php'] .' \a\t H:i:s');
					}
					else
					{
						$headerString  = '';
					}
			
				    //$linkvalue = strtolower($linkvalue);
                    $address = '';
                    $panTan = '';
					
					$customPAN = $this->_ehrTables->getCustomFields('PAN No.');
					$customTAN = $this->_ehrTables->getCustomFields('TAN No.');
						
                    if ($linkvalue == 'Tds')
                    {
                        $addressArr = $this->_ehrTables->getOrgAddress();
                        $panTanArr = $this->_ehrTables->getPanTan();
                        
                        $tan = $pan = array();
                        $tan['Enable'] = $pan['Enable'] =  1;
                        $tan['Required'] = $pan['Required'] = 0;
                        $tan['Field_Name'] = 'TAN :';
                        $pan['Field_Name'] = 'PAN :';
                        
                        if($customTAN['Field_Name'] == 'TAN No.')
                        {
                            $tan['Enable'] = $customTAN['Enable'];
                            $tan['Required'] =  $customTAN['Required'];
                            $tan['Field_Name'] = ($customTAN['New_Field_Name'] != '' ?  $customTAN['New_Field_Name'].' : ' : 'TAN :');
                        }
                        
                        if($customPAN['Field_Name'] == 'PAN No.')
                        {
                            $pan['Enable'] = $customPAN['Enable'];
                            $pan['Required'] =  $customPAN['Required'];
                            $pan['Field_Name'] = ($customPAN['New_Field_Name'] != '' ?  $customPAN['New_Field_Name'].' : ' : 'PAN :');
                        }
                        
                        $address = $addressArr['Street1'].', '. $addressArr['Street2'];
                
                        if($addressArr['City_Name'] && $addressArr['State_Name'] && $addressArr['Country_Name'] && $addressArr['Pincode'])
                            $address .= "\n".$addressArr['City_Name'].', '.$addressArr['State_Name'].', '. $addressArr['Country_Name'].' - '.$addressArr['Pincode'];
                        else if($addressArr['State'] && $addressArr['Country_Name'] && $addressArr['Pincode'])
                            $address .= "\n".$addressArr['State_Name'].', '. $addressArr['Country_Name'].' - '.$addressArr['Pincode'];
                        else if($addressArr['Country_Name'] && $addressArr['Pincode'])
                            $address .= "\n".$addressArr['Country_Name'].' - '.$addressArr['Pincode'];
                        else if($addressArr['Country_Name'])
                            $address .= "\n".$addressArr['Country_Name'];
                        else
                            $address .= "\n";
                            
                        $panTan = "\n";
                        
                        if($pan['Enable'] == 1)
                        {
                            if($panTanArr['PAN'] != null && !empty($panTanArr['PAN']))
                                $panTan .= $pan['Field_Name'].$panTanArr['PAN'];
                                //$panTan .= 'PAN : '.$panTanArr['PAN'];
                        }
                        
                        if($tan['Enable'] == 1)
                        {
                            if($panTanArr['TAN'] != null && !empty($panTanArr['TAN']))
                                $panTan .= ' '.$tan['Field_Name'].$panTanArr['TAN'];
                                //$panTan .= ' TAN : '.$panTanArr['TAN'];
                        }
                        $panTan .= "\n\n";
                    }
                    
					if(!empty($getOrg) && count($getOrg)>0)
					{
						$headerTitle     = $getOrg['RegOrg']['Org_Name'];
						//$headerLogo      = '';
						$headerLogoWidth = 0;
						
						$commonFunction = new Application_Model_DbTable_CommonFunction();
						$headerLogo = $commonFunction->getAwsSignedUrl('ReportLogo','','logoBucket');
						if(!empty($headerLogo))
						{
							$headerLogoWidth = 30;	
						}
							
						
					}
					
					$resultArr = array();
					
					if ($linkvalue != "Eft Monthly" && $linkvalue != "Eft Hourly" && $linkvalue != "Attendance Shortage" && $linkvalue != 'Absentees')
					{
					
							$tableData     = $this->_dbHRReport->rowDriRep($linkvalue);						
						if($linkvalue != 'Employee Wise Expenses')
						{
							$filterVisible = $this->_dbHRReport->filterVisible($selectedFilterVal,$selectedRelationVal,$tableData);
							$queryForExpt  = $this->_dbHRReport->forExpt($selectedFilterVal,$selectedRelationVal,$tableData);// will retrun array and datatype array
						}
						$mainTableStr = $this->mainTable($linkvalue, explode(',',$selectedFields), $selectedFilterVal, $selectedRelationVal, $tableData);
					}
                   
					if (in_array($linkvalue, array('Eft Hourly','Eft Monthly','Employee Wise Expenses','Hourly Wage Payslip', 'Monthly Master Report', 'Hourly Master Report'))) 
					{
						
						//create print preview description table not required while change filter values
						$heaterStr  = '<div><table width="100%" style="border-collapse:collapse;border-bottom:1px solid #000;" cellpadding="0"><tr>';
						$org 	    = $this->_dbBilling->getOrgName($this->_orgCode);
						$logEmpName = $this->_dbPersonal->employeeName($this->_logEmpId);
						
                        if(!empty($org['RegOrg']['Report_LogoPath']))
						{
							$heaterStr .= '<td style="width:140px;"><img style="width:120px;" src="'.$headerLogo.'"></td>';
						}
                        else
                        {
                            $heaterStr .= '<td></td>';
                        }
						
						$heaterStr .= '<td><div style="font-weight:bold;font-size:18px;">'.$org['RegOrg']['Org_Name'].'</div>';
					
						if($showReporCreator['Show_Report_Creator']==1)
						{
							$heaterStr .= '<div style="font-size:15px;">created by '.$logEmpName['Employee_Name'].'</div>
									   <div style="font-size:15px;">created on '.date($this->_orgDateFormat['php'] .' \a\t H:i:s').'</div>';
						}

						$heaterStr .= '</td></tr></table></div>';			   
									
						$footer    ='<br><div width="100%" style="border-top: 1px solid #000;" align="right">{PAGENO}</div>';
						
					    require_once realpath(APPLICATION_PATH . '/../vendor/mpdf/mpdf/src/Mpdf.php');
						$mpdf=new \Mpdf\Mpdf();
						$mpdf->SetHTMLHeader($heaterStr);
						
                        if(strpos($linkvalue, 'Report') !== false)
                            $repTitle   = '<br><div align="center" style="width:100%;font-weight:bold; font-size:30px; margin-top:30px"> '.$linkvalue.'</div><br>';
						else
                            $repTitle   = '<br><div align="center" style="width:100%;font-weight:bold; font-size:30px; margin-top:30px"> '.$linkvalue.' Report'.'</div><br>';
                        
                         $mpdf->WriteHTML($repTitle);
					
						 if(  $linkvalue != 'Employee Wise Expenses' && $linkvalue!='Eft Monthly'  && $linkvalue!='Eft Hourly')
						 {
							$mpdf->writeHTML($mainTableStr);
						 }


						

						$mpdf->SetHTMLFooter('<div style="text-align:center; font-size:15px; margin-top:10px"> This is system generated report. No signature is required.</div>'.$footer);
						if(!empty($linkvalue) && $linkvalue == 'Employee Wise Expenses')
						{
							$empId=$selectedFilterVal[0];
							$sdate =$selectedFilterVal[1];
							$ldate =$selectedFilterVal[2];
	
							$sdate = date('d/m/Y',strtotime($this->_ehrTables->changeDateformat($sdate)));
							$ldate = date('d/m/Y',strtotime($this->_ehrTables->changeDateformat($ldate)));
							$getExpenseEmployeeDetails=$this->_dbHRReport->getExpenseEmployeeDetails($empId);
							$employeeDetails = $getExpenseEmployeeDetails;
							$bindOutput = 'EmployeeWiseExpense.pdf';
							
							$mpdf->writeHTML('<div style="font-size:15px;margin-left:15px;">'."Employee Code : ".$employeeDetails['User_Defined_EmpId'].'</div>');
							$mpdf->writeHTML('<div style="font-size:15px;margin-left:15px;">'."Employee Name: ".$employeeDetails['Employee_Name'].'</div>');
							$mpdf->writeHTML('<div style="font-size:15px;margin-left:15px;">'."Designation : ".$employeeDetails['Designation_Name'].'</div>');
							$mpdf->writeHTML('<div style="font-size:15px;margin-left:15px;">'."Location : ".$employeeDetails['Location_Name'].'</div>');
							$mpdf->writeHTML('<div style="font-size:15px;margin-left:15px;">'."Expense Statement for the Period from ".$sdate.' '.'to'.' '.$ldate.'<br/></div>');
							$mpdf->writeHTML('<br/>');

							$mpdf->writeHTML($mainTableStr);
							$mpdf->Output($bindOutput, 'D');
						
						}
						else if($linkvalue == 'Eft Monthly' || $linkvalue == 'Eft Hourly')
						{
							$eftHeader = $this->_dbHRReport->eftHeader($selectedFilterVal[0]);
							$reqArr = array();
							for($i=0;$i<count($eftHeader);$i++) 
							{
								array_push($reqArr, $eftHeader[$i]);
							}
							$headerArr = $reqArr;
							$result = $this->_dbHRReport->getEftDetails($selectedFilterVal,$reqArr,null,null,null,null,$linkvalue,null);
							$resultArr = $result['aaData'];
							
							$tabstr .=  '<table>';
						
							$tabstr .= '<thead><tr style="font-weight:bold; font-size:15px; "height="100" align="center" bgcolor="#BDBDBD">';
							
							foreach ($headerArr as $head)
							{
								$tabstr .= '<th height="50" style="text-align:left;">'.str_replace('_', ' ', $head).'</th>';
							}
							$tabstr .= '</tr></thead>';
							
							foreach ($resultArr as $row) {
								$tabstr .= '<tr>';
								foreach ($row as $field) {
									$tabstr .= '<td height="40" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:left;">'.str_replace('/', '-', $field).'</td>';
							
								}
								$tabstr .= '</tr>';
							}
							$tabstr .= '</table>';

							$mpdf->writeHTML($tabstr);
							$bindOutput = 'EFT.pdf';
							$mpdf->Output($bindOutput, 'D');
						}
						else if(!empty($linkvalue) && $linkvalue == 'Hourly Wage Payslip')
						{
							$bindOutput = 'HourlyPayslipReport.pdf';
							$mpdf->Output($bindOutput, 'D');
						}
						else if(!empty($linkvalue) && $linkvalue == 'Monthly Master Report')
						{
							$bindOutput = $this->_orgCode.'-MonthlyMasterReport.pdf';
							$mpdf->Output($bindOutput, 'D');
						}
						else if(!empty($linkvalue) && $linkvalue == 'Hourly Master Report')
						{
							$bindOutput = $this->_orgCode.'-HourlyMasterReport.pdf';
							$mpdf->Output($bindOutput, 'D');
						}
						else if(!empty($linkvalue) && $linkvalue == 'ESI Monthly')
						{
							$bindOutput = $this->_orgCode.'-ESIMonthlyReport.pdf';
							$mpdf->Output($bindOutput, 'D');
						}
						else if(!empty($linkvalue) && $linkvalue == 'ESI Hourly')
						{
							$bindOutput = $this->_orgCode.'-ESIHourlyReport.pdf';
							$mpdf->Output($bindOutput, 'D');
						}
						else
						{
							$mpdf->Output();
						}
					}
                    else 
					{
                        define('K_TCPDF_EXTERNAL_CONFIG',true);
                        
						// require_once('tcpdf/config/custom_config.php');
						// require_once('tcpdf/config/lang/eng.php');
						// require_once('tcpdf/tcpdf.php');
					
						require_once realpath(APPLICATION_PATH . '/../vendor/tecnickcom/tcpdf/config/tcpdf_autoconfig.php');
						require_once realpath(APPLICATION_PATH . '/../vendor/tecnickcom/tcpdf/examples/lang/eng.php');
						require_once realpath(APPLICATION_PATH . '/../vendor/tecnickcom/tcpdf/tcpdf.php');
					
											
						// create new PDF document
					//	$pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
						$pdf = new MYPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
						// set document information
						$pdf->SetCreator(PDF_CREATOR);
						$pdf->SetAuthor('');
						$pdf->SetTitle('Report');
						$pdf->SetSubject('Organization Report');
						$pdf->SetKeywords('invoice, generation, organization');
					
						$footerString = '<div style="text-align:center; font-size:15px; margin-top:10px"> This is system generated report. No signature is required.</div>'; 
						// set default header data
						$pdf->SetHeaderData($headerLogo, $headerLogoWidth, $headerTitle,$address.$panTan.$headerString);
						 //$footertext='This is system generated report. No signature is required.';
						 //  $pdf->writeHTMLCell(0, 0, '', '', $footertext, 0, 0, false,true, "C", true);
						$pdf->setFooterData();
						
						// set header and footer fonts
						$pdf->setHeaderFont(Array(PDF_FONT_NAME_MAIN, '', PDF_FONT_SIZE_MAIN));
						$pdf->setFooterFont(Array(PDF_FONT_NAME_DATA, '', PDF_FONT_SIZE_DATA));
						
						// set default monospaced font
						$pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
						
						//set margins
                        if($linkvalue == 'Tds')
                            $pdf->SetMargins(PDF_MARGIN_LEFT, 50, PDF_MARGIN_RIGHT);
                        else
                            $pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
						$pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
						$pdf->SetFooterMargin(PDF_MARGIN_FOOTER);
						
						//set auto page breaks
						$pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);
						
						//set image scale factor
						$pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);
						
						//set some language-dependent strings
						$pdf->setLanguageArray($l);
						
						// ---------------------------------------------------------
						
						// set default font subsetting mode
						$pdf->setFontSubsetting(true);
						
						// Set font
						// dejavusans is a UTF-8 Unicode font, if you only need to
						// print standard ASCII chars, you can use core fonts like
						// helvetica or times to reduce file size.
						$pdf->SetFont('helvetica', 'B', 14, '', true);
						
						
						// add a page
						$pdf->AddPage('L', 'A4');
						if(!empty($disGrid))
						{
							$pdf->Write(5, $linkvalue.' Report', '', 0, 'C', true, 0, false, false, 0);
						}
						
						$pdf->SetFont('helvetica', 'B', 12);
						$pdf->Ln();
						
						if($linkvalue == 'Uan Based Ecr' || strtolower($linkvalue)== 'uan based ecr(arrear)')
						{
							$result = $this->_dbHRReport->getUanEcrDetails($selectedFilterVal, null, null, null, null, null,$linkvalue);
							$resultArr=$result['aaData'];
							$headerArr = array_keys($resultArr[0]);
							
							$tabstr =  '<table cellpadding="1">';
							
							$tabstr .= '<thead><tr style="font-weight:bold; font-size:5; "height="100" align="center" bgcolor="#BDBDBD">';
							
							foreach ($headerArr as $head)
							{
								$tabstr .= '<th height="50" style="text-align:left; padding-left: 1%;">'.str_replace('_', ' ', $head).'</th>';
							}
							$tabstr .= '</tr></thead>';
							
							foreach ($resultArr as $row) {
								$tabstr .= '<tr>';
								foreach ($row as $field) {
									$tabstr .= '<td height="40" style="border-bottom:1px solid #BDBDBD; font-size:5;  padding-left: 1%; text-align:left;">'.str_replace('/', '-', $field).'</td>';
							
								}
								$tabstr .= '</tr>';
							}
							$tabstr .= '</table>';
						}
						else if($linkvalue =="ESI Monthly")
                        {
						
                            $result = $this->_dbHRReport->getEsiDetails($selectedFilterVal, null, null, null, null, null,$linkvalue);
							$resultArr=$result['viewPdf'];
							

							$headerArr =  array('Employee_Id','Employee_Name','ESI_No','Salary_Month','Basic_Pay','Sum_of_allowance','Sum_of_adhoc_allowance','Holiday_Special_Wages','Total_Overtime_Wages','Unpaid_Deduction','ESI_Wages','ESI_EE','ESI_ER');
							
                            $tabstr = '<table cellpadding="1">';
                            
                            $tabstr .= '<thead><tr style="font-weight:bold; font-size:5; "height="100" align="center" bgcolor="#BDBDBD">';
                            
                            foreach ($headerArr as $head)
                            {
                                $tabstr .= '<th height="50" style="text-align:left; padding-left: 1%;">'.str_replace('_', ' ', $head).'</th>';
                            }
                            $tabstr .= '</tr></thead>';
                            
                            foreach ($resultArr as $row) {
								$tabstr .= '<tr>';
									
								foreach ($row as $field) {
									$tabstr .= '<td height="40" style="border-bottom:1px solid #BDBDBD; font-size:5; padding-left: 1%; text-align:left;">'.str_replace('/', '-', $field).'</td>';
							
								}
								$tabstr .= '</tr>';
							}
							$footerArr = $result['footer'][0];
 							$footerArrayKeys =  array('Employee_Id','Employee_Name','ESI_No','Salary_Month','Basic_Pay','Sum_of_allowance','Sum_of_adhoc_allowance','Holiday_Special_Wages','Total_Overtime_Wages','Unpaid_Deduction','ESI_Wages','ESI_EE','ESI_ER');

							$tabstr .='<tr>';
							foreach($footerArrayKeys as $footer)
							{
								$tabstr .= ' <td height="40" style="border-bottom:1px solid #BDBDBD; font-size:5; padding-left: 1%; text-align:left;">'.str_replace('/', '-', $footerArr[$footer]).'</td>';
							}
                            $tabstr .= '</tr></table>';
						}
						else if($linkvalue =="ESI Hourly")
                        {
						
                            $result = $this->_dbHRReport->getEsiHourlyDetails($selectedFilterVal, null, null, null, null, null,$linkvalue);
							$resultArr=$result['viewPdf'];
							

							$headerArr =  array('Employee_Id','Employee_Name','ESI_No','Salary_Month','Total_HourlyWages','Total_Overtime_Wages','Holiday_SpecialWages','Sum_of_allowance','Sum_of_adhoc_allowance','ESI_Wages','ESI_EE','ESI_ER');
							
                            $tabstr = '<table cellpadding="1">';
                            
                            $tabstr .= '<thead><tr style="font-weight:bold; font-size:5; "height="100" align="center" bgcolor="#BDBDBD">';
                            
                            foreach ($headerArr as $head)
                            {
                                $tabstr .= '<th height="50" style="text-align:left; padding-left: 1%;">'.str_replace('_', ' ', $head).'</th>';
                            }
                            $tabstr .= '</tr></thead>';
                            
                            foreach ($resultArr as $row) {
								$tabstr .= '<tr>';
									
								foreach ($row as $field) {
									$tabstr .= '<td height="40" style="border-bottom:1px solid #BDBDBD; font-size:5; padding-left: 1%; text-align:left;">'.str_replace('/', '-', $field).'</td>';
							
								}
								$tabstr .= '</tr>';
							}
							$footerArr = $result['footer'][0];
 							$footerArrayKeys =  array('Employee_Id','Employee_Name','ESI_No','Salary_Month','Total_HourlyWages','Total_OvertimeWages','Holiday_SpecialWages','Sum_of_allowance','Sum_of_adhoc_allowance','ESI_Wages','ESI_EE','ESI_ER');

							$tabstr .='<tr>';
							foreach($footerArrayKeys as $footer)
							{
								$tabstr .= ' <td height="40" style="border-bottom:1px solid #BDBDBD; font-size:5; padding-left: 1%; text-align:left;">'.str_replace('/', '-', $footerArr[$footer]).'</td>';
							}
                            $tabstr .= '</tr></table>';
						}
						else if($linkvalue == 'Uan Based Ecr Hourly')
						{
							$result = $this->_dbHRReport->getUanBasedEcrHourly($selectedFilterVal, null, null, null, null, null,$linkvalue);
							$resultArr=$result['aaData'];
							$headerArr = array_keys($resultArr[0]);
							
							$tabstr =  '<table cellpadding="1">';
							
							$tabstr .= '<thead><tr style="font-weight:bold; font-size:5; "height="100" align="center" bgcolor="#BDBDBD">';
							
							foreach ($headerArr as $head)
							{
								$tabstr .= '<th height="50" style="text-align:left; padding-left: 1%;">'.str_replace('_', ' ', $head).'</th>';
							}
							$tabstr .= '</tr></thead>';
							
							foreach ($resultArr as $row) {
								$tabstr .= '<tr>';
								foreach ($row as $field) {
									$tabstr .= '<td height="40" style="border-bottom:1px solid #BDBDBD; font-size:5;  padding-left: 1%; text-align:left;">'.str_replace('/', '-', $field).'</td>';
							
								}
								$tabstr .= '</tr>';
							}
							$tabstr .= '</table>';
						}
						else if($linkvalue == 'Attendance Shortage')
						{						
							$reqArr =array('Employee_Id','Name','Department','Total_Working_Hours','Total_Worked_Hours');
													
							$this->view->keyArr = $reqArr;
							
							$reportUser = array('Is_Manager'  	=> $this->_reportAccessRights['Employee']['Is_Manager'],
											 'View'       	 	=> $this->_reportAccessRights['Employee']['View'],
											 'Optional_Choice'	=> $this->_reportAccessRights['Employee']['Optional_Choice'],
											 'Admin'      		=> $this->_reportAccessRights['Admin'],
											 'LogId' 	        => $this->_logEmpId,
											 'Employee_Name' 	=> $this->_dbPersonal->employeeId($this->_logEmpId));
					
							$attendanceReport = $this->_dbAttendanceReport->attendanceShortageReport('','','','','','',$filterArray,$reportUser,'');
							
							$tabstr = '<table style="width:100%;" cellpadding="10"><thead>
										<tr bgcolor="#BDBDBD"><th style="border-bottom:1px solid #BDBDBD; font-size:15px; font-weight:bold;">Employee Id</th><th style="border-bottom:1px solid #BDBDBD; font-size:15px; font-weight:bold;">Employee Name</th><th style="border-bottom:1px solid #BDBDBD; font-size:15px; font-weight:bold;">Department</th>
										<th style="border-bottom:1px solid #BDBDBD; font-size:15px; font-weight:bold;" align="center">Total Working Hours</th><th style="border-bottom:1px solid #BDBDBD; font-size:15px; font-weight:bold;" align="center">Total Worked Hours</th></tr></thead>';
							foreach($attendanceReport['aaData'] as $report)
							{
								//$salaryMonth=$report['Salary_Month'];
								//$month = explode(",", $salaryMonth, 2);
								//$salaryMonth=$this->_monthArray[$month[0]].",".$month[1];
								$tabstr .= '<tr><td style="border-bottom:1px solid #BDBDBD; font-size:15px;">'.$report['Employee_Id'].'</td><td style="border-bottom:1px solid #BDBDBD; font-size:15px;">'.$report['Name'].'</td><td style="border-bottom:1px solid #BDBDBD; font-size:15px;" align="center">'.$report['Department'].'</td>
											<td style="border-bottom:1px solid #BDBDBD; font-size:15px;" align="center">'.$report['Total_Working_Hours'].'</td><td style="border-bottom:1px solid #BDBDBD; font-size:15px;" align="center">'.$report['Total_Worked_Hours'].'</td></tr>';
	
									
							}
							$tabstr .= '</table>';
						}
						else {
							$loanId = $this->_getParam('loanId', null);
							$tabstr =  '';

							if($linkvalue == 'ESI Monthly')
							{
								$result = $this->_dbHRReport->getEsiDetails($selectedFilterVal, null, null, null, null, null,$linkvalue);
								$resultArr=$result['aaData'];
								$headerArr = array_keys($resultArr[0]);
							}
							if($linkvalue == 'ESI Hourly')
							{
								$result = $this->_dbHRReport->getEsiHourlyDetails($selectedFilterVal, null, null, null, null, null,$linkvalue);
								$resultArr=$result['aaData'];
								$headerArr = array_keys($resultArr[0]);
							}
						    elseif($linkvalue == 'ESIC Monthly' || $linkvalue == 'ESIC Hourly')
							{
								$result = $this->_dbHRReport->getEsicDetails($selectedFilterVal, null, null, null, null, null,$linkvalue);
								$resultArr=$result['aaData'];
								$headerArr = array_keys($resultArr[0]);    
							}
							elseif($linkvalue == 'Attendance Summary Hourly')
							{
								$result    = $this->_dbHRReport->getAttendanceSummaryHourly($selectedFilterVal,null, null, null, null, null,$linkvalue);
								$resultArr = $result['aaData'];
								$headerArr = array_keys($resultArr[0]);    
							}
							elseif($linkvalue == 'Attendance Summary Monthly')
							{
								$result    = $this->_dbHRReport->getAttendanceSummaryMonthly($selectedFilterVal,null, null, null, null, null,$linkvalue);
								$resultArr = $result['aaData'];
								$headerArr = array_keys($resultArr[0]);    
							}
							elseif($linkvalue == 'Additional Wage Summary')
							{
								$result    = $this->_dbHRReport->getAdditionalWageSummary($selectedFilterVal);
								$resultArr = $result['aaData'];
								$headerArr = array_keys($resultArr[0]);    
							}
							elseif($linkvalue == 'Eft Monthly' || $linkvalue == 'Eft Hourly')
							{
								$eftHeader = $this->_dbHRReport->eftHeader($selectedFilterVal[0]);
								$reqArr = array();
								for($i=0;$i<count($eftHeader);$i++) 
								{
									array_push($reqArr, $eftHeader[$i]);
								}
								$headerArr = $reqArr;
								$result = $this->_dbHRReport->getEftDetails($selectedFilterVal,$reqArr,null,null,null,null,$linkvalue,null);
								$resultArr = $result['aaData'];
							}
                            elseif($linkvalue == 'Loan Amortization' && !empty($loanId))
                            {
                                $loanId = $this->_getParam('loanId', null);
                                //$this->view->valueArr = $this->_dbHRReport->loanAmortization($loanId);
                            	$reqArr = array('Month','Loan_Balance','Interest_Amount','Principal_Amount','EMI','Outstanding_Amount');

								$headerArr = $reqArr;
								$result = $this->_dbHRReport->loanAmortization($loanId);
                                
								$resultArr = $result[1];
                                $tabstr .=  '<table><tr style="text-align:left"><td style="padding-left:3%;width:15%;">Employee Id:</td><td>'.$result[0]['User_Defined_EmpId'].'</td></tr>';
                                $tabstr .=  '<table><tr style="text-align:left"><td style="padding-left:3%;width:15%;">Employee Name:</td><td>'.$result[0]['Employee_Name'].'</td></tr>';
                                $tabstr .=  '<tr style="text-align:left"><td style="padding-left:3%;width:15%;">Loan Amount:</td><td>'.$result[0]['Loan_Amount'].'</td></tr>';
                                $tabstr .=  '<tr style="text-align:left"><td style="padding-left:3%;width:15%;">Loan Tenure:</td><td>'.$result[0]['Duration_In_Months'].' months</td></tr>';
                                $tabstr .=  '<tr style="text-align:left"><td style="padding-left:3%;width:15%;">Interest:</td><td>'.$result[0]['Interest'].'%</td></tr></table>';
                            }
							else {
								$data = $this->_dbHRReport->filterWtFooter($selectedFilterVal,$selectedRelationVal,$tableData);//will return result set
						
                                $pan = array();
                                $pan['Enable'] =  1;
                                $pan['Required'] = 0;
                                $pan['Field_Name'] = 'PAN';
                                
                                if($customPAN['Field_Name'] == 'PAN No.')
                                {
                                    $pan['Enable'] = $customPAN['Enable'];
                                    $pan['Required'] =  $customPAN['Required'];
                                    $pan['Field_Name'] = ($customPAN['New_Field_Name'] != '' ?  $customPAN['New_Field_Name'] : 'PAN');
                                }
                        
								foreach ($data as $row)
								{
									unset($row[$tableData['Bar_Y']]);
                                    if($pan['Enable'])
                                    {
                                        $keys = array_keys($row);
                                        $index = array_search('PAN', $keys);
                                    
                                        if ($index !== false) {
                                            $keys[$index] = $pan['Field_Name'];
                                            $row = array_combine($keys, $row);
                                        }
                                    }
                                    else
                                    {
                                        unset($row['PAN']);
                                    }
									$resultArr[] = $row;
								}
								$headerArr = array_keys($resultArr[0]);
							}
							
                            $tabstr .=  '<table cellpadding="10">';
							
							$tabstr .= '<thead><tr style="font-weight:bold; font-size:12; "height="100" align="center" bgcolor="#BDBDBD">';
							
							foreach ($headerArr as $head) // form the headers
							{
								$tabstr .= '<th height="50" style="text-align:left; padding-left: 10%;">'.str_replace('_', ' ', $head).'</th>';
							}
							$tabstr .= '</tr></thead>';

							foreach ($resultArr as $key =>$row) {

								$tabstr .= '<tr>';
								foreach ($row as $field => $value) {
									if($linkvalue == 'Reimbursement' && $field == 'Documents')
									{
										$htmlCode = '';
										if(!empty($value))
										{
											foreach($value as $index => $url)
											{
												$htmlCode = $htmlCode. '<div class="col-xs-6" >'. '<a target="_blank" style="padding:0px;cursor: pointer;text-decoration:none;" title="' . $index. '" href="' . $url . '">' . $index . '</a> </div>';
											}
											$tabstr .= '<td height="40" style="border-bottom:1px solid #BDBDBD; font-size:12;  padding-left: 10%; text-align:left;">'. $htmlCode .'</td>';
										}
									}
									else
									{
										$tabstr .= '<td height="40" style="border-bottom:1px solid #BDBDBD; font-size:12;  padding-left: 10%; text-align:left;">'. $value .'</td>';
									}
								}
								$tabstr .= '</tr>';
							}
							$tabstr .= '</table>';
						}		 
						if(!empty($disGrid)) {
							$pdf->SetFont('helvetica', '', 12);
							$tblStartY = $pdf->GetY();
							$pdf->writeHTML($tabstr, true, false, false, false, 'C');
						}
						
						/* if(!empty($bsvg)) {
							file_put_contents($this->_ehrTables->uploadPath.'images/bar.svg', $bsvg);
							$pdf->ImageSVG($file = $this->_ehrTables->uploadPath.'images/bar.svg', $x = -20, $y = '', $w=420, $h=200, $link='', $align='N', $resize=false, $palign='', $border=0, $fitbox=true, $fitonpage=true);
						}
						if(!empty($psvg)) {
							file_put_contents($this->_ehrTables->uploadPath.'images/pie.svg', $psvg);
							$pdf->Ln();
							$pdf->ImageSVG($file = $this->_ehrTables->uploadPath.'images/pie.svg', $x = 0, $y = '', $w=350, $h=200, $link='', $align='N', $resize=true, $palign='', $border=0, $fitbox=true, $fitonpage=true);
						} */
						
						$fileName = (!empty($linkvalue)) ? $linkvalue.'.pdf' : 'Doc.pdf';
						
						$pdf->Output($fileName, 'D'); // Use I for preview (file will not download)
					}
				}
			}
			else
			{
				$sessionUrl = new Zend_Session_Namespace('lastRequest');
				$this->_redirect($sessionUrl->lastRequestUri);
			}
        }
        else
        {
            $this->_helper->redirector('index', 'hr-reports', 'reports',array('_mId'=>'hr-reports','_mNme'=>10));
        }
    }
    
	/**
	 * Print report
	 */
	public function printReportAction()
    {
        $this->_helper->layout()->disableLayout();
        if(isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('print-report', 'html')->initContext();
			
			$modName = $this->_getParam('_modName', null);
			$modName = filter_var($modName, FILTER_SANITIZE_STRING);
			$modName = $modName.' reports';
			
			$this->_reportAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $modName);
			if($this->_reportAccessRights['Employee']['Optional_Choice'] == 1)
			{
				$linkvalue = $this->_getParam('linkValue', null);
				$linkvalue = filter_var($linkvalue,FILTER_SANITIZE_STRIPPED);
				$linkvalue = ucwords(str_replace('-', ' ', $linkvalue));
				
				$selectedFilterVal = $this->_getParam('_fv', null);
				$selectedRelationVal = $this->_getParam('_rv', null);
				$selectedFields = $this->_getParam('_ckedValue', null);
				
                $filterArray    	 = $this->_getParam('_filterArray', null);				
				$selectedFilterVal 	 = /*explode(',',*/$filterArray;

				if(!empty($linkvalue))
				{
					if($linkvalue == "Esi Monthly")
					{
						$linkvalue = "ESI Monthly";
					}else if($linkvalue == "Esi Hourly")
					{
						$linkvalue = "ESI Hourly";
					}else if($linkvalue == "Esic Monthly") {
						$linkvalue = "ESIC Monthly";
					} else if($linkvalue == "Esic Hourly") {
						$linkvalue = "ESIC Hourly";
					}
					
					$tableData = $this->_dbHRReport->rowDriRep($linkvalue);
                    
                    //$mainTableStr = $this->mainTable($linkvalue, explode(',',$selectedFields), $selectedFilterVal, $selectedRelationVal, $tableData);
                    //$this->view->mainTableStr = $this->mainTable($linkvalue, explode(',',$selectedFields), $selectedFilterVal,$selectedRelationVal, $tableData);
					$mainTableStr = $this->mainTable($linkvalue, $selectedFields, $selectedFilterVal,$selectedRelationVal, $tableData);
                    
                    if($mainTableStr)
                        $this->view->mainTableStr = $mainTableStr;
                    
                    //$this->view->mainTableStr = $this->mainTable($linkvalue, $selectedFields, $selectedFilterVal,$selectedRelationVal, $tableData);
				}
			}
        }
        else
        {
            $this->_helper->redirector('index', 'hr-reports', 'reports',array('_mId'=>'hr-reports','_mNme'=>10));
        }
    }
    
	/**
     * creating dynamic filters for report
     *
     * @param string $linkValue The selected navigation value.
     * @return string  filter element like <select>....</select>,<input type ="text">.
     */
    //version:0.3 =>Exclude pf and insurance from earnings in grid view when "retirals based on basic" is true  
    //version:0.2 =>added monthly and hourly payslip report
    public function dynFiltersAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if(isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('dyn-filters', 'html')->initContext();
			
			$modName = $this->_getParam('_modName', null);
			$modName = filter_var($modName, FILTER_SANITIZE_STRING);
			$modName = $modName.' reports';	
			
			$this->_reportAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $modName);
			
			if($this->_reportAccessRights['Employee']['View'] == 1)
			{
				$linkvalue = $this->_getParam('linkValue', null);
				$linkvalue = filter_var($linkvalue ,FILTER_SANITIZE_STRIPPED);
				$linkvalue = ucwords(str_replace('-', ' ', $linkvalue));
				$formattedLinkValue = strtolower($linkvalue);
				
				$filterArray = $this->_getParam('filterArray', null);

				if(!empty($linkvalue))
				{
					if($linkvalue == "Esi Monthly"){
						$linkvalue = "ESI Monthly";
					}else if($linkvalue == "Esi Hourly")
					{
						$linkvalue = "ESI Hourly";
					}
					else if($linkvalue == "Esic Monthly") {
						$linkvalue = "ESIC Monthly";
					} else if($linkvalue == "Esic Hourly") {
						$linkvalue = "ESIC Hourly";
					}
					
					$dbOrgDetail = new Organization_Model_DbTable_OrgSettings();
					$orgCode = $this->_ehrTables->getOrgCode();
					$salaryCalcDetail = $dbOrgDetail->viewOrgDetail($orgCode);
					
					$selctRwData = $this->_dbHRReport->rowDriRep($linkvalue);
				
				if($linkvalue != 'Employee Wise Expenses')
				{
						$recForLink = $this->_dbHRReport->filter('', '', $selctRwData,1);
					
					$reqArr = array('Flag_Pie'=>$selctRwData['Flag_Pie'],'cnt'=> count($recForLink),'Optional_Choice' =>$this->_reportAccessRights['Employee']['Optional_Choice']);
					if(!empty($this->_orgDateFormat))
					{
						$reqArr['OrgJqryformat'] = array($this->_orgDateFormat['jq'], $this->_orgDateFormat['regex']);
					}
					else
					{
						$reqArr['OrgJqryformat'] = array('dd/mm/yy', '/^(\d{2})(\/)(\d{2})(\/)(\d{4})$/');
					}
					if (!empty($recForLink[0]) && is_array($recForLink[0]))
					{						
						unset($recForLink[0][$selctRwData['Bar_Y']]);
						$allGridKeysToPresent = array_keys($recForLink[0]);
						$gridKeyResponse = $this->_dbHRReport->getReportKeysUsingCount($allGridKeysToPresent,$linkvalue);
						$reqArr['grid_key'] = $gridKeyResponse;
					}
				}	
					//create print preview description table not required while change filter values
					$org = $this->_dbBilling->getOrgName($this->_orgCode);
					$logEmpName = $this->_dbPersonal->employeeName($this->_logEmpId);
					$printHead = array('employeeName'=>$logEmpName['Employee_Name'],
										'createdOn'=>date($this->_orgDateFormat['php'] .' \a\t H:i:s'),'Org_Name'=>$org['RegOrg']['Org_Name']);
					
					$commonFunction = new Application_Model_DbTable_CommonFunction();
					$signedUrl = $commonFunction->getAwsSignedUrl('ReportLogo','','logoBucket');
					if(!empty($signedUrl))
					{
						$printHead['img_path'] = $signedUrl;	
					}
				if($linkvalue != 'Employee Wise Expenses')
				{
					if(count($recForLink)>0)
					{
						$bindData = $this->_dbHRReport->filterWtPagination('', '', $selctRwData, 1, 10, null, null);
					}
					else 
					{
						$bindData = array("iTotalRecords" => 0, "iTotalDisplayRecords" => 0, "aaData" => array());
					}
				}
				
					if($linkvalue == 'Employee Wise Expenses')
					{
						
					
						$expenseArr = array();
						$reqArr1 = array('Date');
			
						$filterArray = $this->_getParam('filterArray', null);

						if($filterArray != '')
						{
							$selectedFilterVal = explode(',',$filterArray);
							$empId	   = $selectedFilterVal[0];
							$sdate 	   = $selectedFilterVal[1];
							$ldate     = $selectedFilterVal[2];
						}
						else
						{
							$selectedFilterVal = '';
							$sdate = date('Y-m-01');
							$ldate = date('Y-m-t');
							$empId= $this->_logEmpId;
			
						}
					
						
						$expenseTitleArr = $this->_dbHRReport->getEmployeeWiseExpensesTitle($empId,$sdate,$ldate);
						
						// $reqArr2 =array('Others','Total');
						$reqArr2 =array('Total');
						
						$expenseDeductionArr = $this->_dbHRReport->getEmployeeWiseExpensesDeductionTitle($empId,$sdate,$ldate);
						
						foreach($expenseDeductionArr as $title)
						{
							array_push($expenseArr,$title." ".'Deduction');
						}
						$reqArr3=array('Total_Deduction','Payable_Amount');
						$reqArr['grid_key'] = array_merge($reqArr1,$expenseTitleArr, $reqArr2,$expenseArr,$reqArr3);
					}
					elseif($linkvalue == 'Hourly Wage Payslip')
					{
							$reqArr['grid_key'] = array('Employee_Id','Employee_Name','Payslip_Month','Total_Earnings','Total_Deductions','Net_Pay','Designation',
							'Department','Bank_Account_Number','Bank_Name','Branch_Name', 'IFSC_Code','Regular_Hours', 'OverTime_Hours','Day_Wage','Days_Worked','Holidays','Basic_Pay','Total_HourlyWages',
							'Total_OvertimeWages','Allowance','Adhoc_Allowance','Bonus','Compoff_Encashment','Shift_Allowance','Reimbursement','Bonus_Allowance','Holiday_Special_Wages',
							'Loan','Provident_Fund','Voluntary_Provident_Fund','Insurance','One-Month_Deduction','Recurring_Deduction','Group_Deduction',
							'Loan_Deduction','Professional_Tax','Tax','Outstanding_Amount');
							
							//$bindData = $this->_dbHRReport->getWagePayslipDetails('', null, null, null, null, null);
							//$bindData = $bindData;
					}
					elseif($linkvalue == 'Hourly Master Report')
					{
						$reqArr['grid_key']= array('Employee_Id', 'Employee_Name','Date_Of_Birth','Date_Of_Join','Confirmation_Date','Resignation_Date',
							'Designation', 'Department', 'Location', 'Mobile_No', 'Manager_Name', 'Regular_Wage', 'Overtime_Wage',  'Provident_Fund',
							'Variable_Insurance', 'Fixed_Insurance','Regular_Hours','OverTime_Hours', 'External_EmpId','User_Name');
						
						//$bindData = $this->_dbHRReport->getHourlyDetails('', null, null, null, null, null);
						//$bindData = $bindData;
					}
					elseif($linkvalue == 'Monthly Master Report')
					{
						/** Form the grid keys */
						$reqArr1 = array('Employee_Id', 'Employee_Name','Date_Of_Birth','Probation_Date', 'Confirmation_Date','Resignation_Date','Designation');
						
						$reqArr2 = array('Location', 'Tax_Regime','Previous_Experience','Current_Experience','Total_Experience','Mobile_No', 'Manager_Name',
										'Annual_Ctc','Annual_Gross_Salary','Monthly_Gross_Salary', 'Basic_Pay','Working_Days', 'Worked_Days','Paid_Unpaid_Leave', 'OverTime_Hours', 'Shortage_Hours');
						
						$orgStruct = $this->_dbHRReport->getOrgStructure();
					
						foreach($orgStruct as $orgDiv)
						{
							array_push($reqArr1,$orgDiv);
						}
						
						$reqArr['grid_key'] = array_merge($reqArr1,$reqArr2);
						
					//	 $bindData = $this->_dbHRReport->getMasterDetails('', null, null, null, null, null);
					//     $bindData = $bindData;
					}
					elseif($linkvalue == 'ESI Monthly')
					{
						$reqArr['grid_key']=array('Employee_Id','Employee_Name','ESI_No','ESI_Wages','ESI_EE','ESI_ER');

					}
					elseif($linkvalue == 'ESI Hourly')
					{
						$reqArr['grid_key']=array('Employee_Id','Employee_Name','ESI_No','ESI_Wages','ESI_EE','ESI_ER','Salary_Month','Total_HourlyWages','Total_OvertimeWages','Holiday_SpecialWages','Sum_of_allowance','Sum_of_adhoc_allowance','Total_Salary');

					}
					elseif($linkvalue == 'ESIC Monthly' || $linkvalue == 'ESIC Hourly')
					{
						$reqArr['grid_key'] =array('IP_Number','IP_Name','No_of_Days_for_which_wages_paid','Reason_Code','Total_Monthly_Wages','Last_Working_Day');
					}
					elseif($linkvalue == 'Attendance Summary Hourly')
					{
						$reqArr['grid_key'] = array('Employee_Id','External_Employee_Id','Employee_Name','Shift_Start_Date','Shift_End_Date','Minimum_Punch_In','Maximum_Punch_Out','Regular_Hours','Overtime_Hours','Description');
					}
					elseif($linkvalue == 'Attendance Summary Monthly')
					{
						$reqArr['grid_key'] =array('Employee_Id','External_Employee_Id','Employee_Name','Work_Schedule','Shift_Date','Day_Type','Minimum_Shift_Start_Time','Maximum_Shift_End_Time','Minimum_Punch_In','Maximum_Punch_Out','Regular_Hours','Overtime_Hours','Total_Working_Hours','Shift_Exists','Attendance_Exist','Leave_Exist','CompensatoryOff_Exist','ShortTimeOff_Exist');
					}
					elseif($linkvalue == 'Employee Utilization')
					{
						$reqArr['grid_key'] = $this->_dbHRReport->getReportHeaders($linkvalue);
					}
					elseif($linkvalue == 'Additional Wage Summary')
					{
						$reqArr['grid_key'] = $this->_dbHRReport->getReportHeaders($linkvalue);
					}
					elseif($linkvalue == 'Uan Based Ecr')
					{
						$reqArr['grid_key'] =array('Uan','Member_Name','Gross_Wages','Epf_Wages','Eps_Wages','Edli_Wages','Epf_Contribution_Remitted',
						   'Eps_Contribution_Remitted','Epf_Eps_Contribution_Remitted','Ncp_Days','Refund_Of_Advances');
						
						//$bindData = $this->_dbHRReport->getUanEcrDetails('', null, null, null, null, null,$linkvalue);
					}
					elseif(strtolower($linkvalue)== 'uan based ecr(arrear)')
					{
						$reqArr['grid_key'] =array('Uan','Member_Name','Epf_Wages','Eps_Wages','Edli_Wages','Epf_Contribution_Remitted',
						   'Eps_Contribution_Remitted','Epf_Eps_Contribution_Remitted');
					}
					elseif($linkvalue == 'Uan Based Ecr Hourly')
					{
						$reqArr['grid_key'] =array('Uan','Member_Name','Gross_Wages','Epf_Wages','Eps_Wages','Edli_Wages','Epf_Contribution_Remitted',
						   'Eps_Contribution_Remitted','Epf_Eps_Contribution_Remitted','Ncp_Days','Refund_Of_Advances');
						
						//$bindData = $this->_dbHRReport->getUanEcrDetails('', null, null, null, null, null,$linkvalue);
					}
					elseif($linkvalue == 'Eft Monthly' || $linkvalue == 'Eft Hourly')
					{
						$selectedFilterVal = explode(',',$filterArray);
						$reqArr['grid_key']  = $this->_dbHRReport->eftHeader($selectedFilterVal[0]);
					}
					elseif($linkvalue == 'Absentees')
					{
						$reqArr['grid_key'] =array('Employee_Id','Name','Department','Designation','Location','Date_Of_Absence','Absent_Duration','Absent_Period');
					}
					else if($linkvalue == 'Attendance And Absence Overview')
					{
						$reqArr['grid_key'] = $this->_dbHRReport->getReportHeaders($linkvalue);
					}
					elseif($formattedLinkValue === 'lop recovery') {
						$reqArr['grid_key'] =array('Employee_ID','Employee_Name','Leave_Date','Deduction_Month','LOP_Recovery_Processing_Month','Status','Reason',
								'Duration','Leave_Period');
					}
					$this->view->countArr = array('grid'=>$reqArr);
				}
			}
        }
        else
        {
            $this->_helper->redirector('index', 'hr-reports', 'reports',array('_mId'=>'hr-reports','_mNme'=>10));
        }
    }
    
	/**
	 * Print preview
	 */
	public function previewAction()
    {
        $this->_helper->layout()->disableLayout();
        if(isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('preview', 'json')->initContext();
			
			$modName = $this->_getParam('_modName', null);
			$modName = filter_var($modName, FILTER_SANITIZE_STRING);
			$modName = $modName.' reports';
			
			$this->_reportAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $modName);
			if($this->_reportAccessRights['Employee']['Optional_Choice'] == 1)
			{
				$linkvalue = $this->_getParam('linkValue', null);
				$linkvalue = filter_var($linkvalue,FILTER_SANITIZE_STRIPPED);
				$linkvalue = ucwords(str_replace('-', ' ', $linkvalue));
				
				$format = $this->_getParam('_format',null);
				
				$filterArray = $this->_getParam('filterArray', null);
			
				if($filterArray != '')
				{
					$selectedFilterVal = explode(',',$filterArray);
				}
				else
				{
					$selectedFilterVal = '';
				}
				
				$selectedRelationVal = array();
				for($p=0;$p<=15;$p++)
				{
					array_push($selectedRelationVal,'');
				}
			
				if(!empty($linkvalue))
				{
					if($linkvalue == "Esi Monthly")
					{
						$linkvalue = "ESI Monthly";
					}else if($linkvalue == "Esi Hourly")
					{
						$linkvalue = "ESI Hourly";
					}else if($linkvalue == "Esic Monthly") {
						$linkvalue = "ESIC Monthly";
					} else if($linkvalue == "Esic Hourly") {
						$linkvalue = "ESIC Hourly";
					} else if($linkvalue == "Eft Monthly") {
						$linkvalue = "Eft Monthly";
					}
					$this->view->reportConfiguration 	='';
					if($linkvalue == 'Monthly Salary Payslip')
					{
						$payslipId 			  	= $this->_dbHRReport->getMonthlyPayslipId($selectedFilterVal,$linkvalue);
                        $headerDetails 			= $this->_dbHRReport->getReportHeaders($linkvalue,$payslipId,NULL,$this->_payrollGeneralSettings);
						$this->view->keyArr 	= $data = $headerDetails;
						$this->view->table_data = $tableData = $this->_dbHRReport->rowDriRep($linkvalue);
						$this->view->valueArr 	= array();
						$this->view->reportConfiguration 	= $this->_dbHRReport->getReportConfiguration($linkvalue,$headerDetails);
					}
					else if($linkvalue == 'Payment Register')
					{
						$payslipId 			  	= $this->_dbHRReport->getMonthlyPayslipId($selectedFilterVal,$linkvalue);
                        $headerDetails 			= $this->_dbHRReport->getReportHeaders($linkvalue,$payslipId,NULL,$this->_payrollGeneralSettings);
						$this->view->keyArr 	= $data = $headerDetails;
						$this->view->table_data = $tableData = $this->_dbHRReport->rowDriRep($linkvalue);
						$this->view->valueArr 	= array();
					}
					else if($linkvalue == 'Attendance And Absence Overview')
					{
						$headerDetails = $this->_dbHRReport->getReportHeaders($linkvalue);
						$this->view->keyArr 	= $data = $headerDetails;
						$this->view->table_data = $tableData = $this->_dbHRReport->rowDriRep($linkvalue);
						$this->view->valueArr 	= array();
					}
					else if($linkvalue == 'Insurance Statement')
					{
						$payslipId 			  	= $this->_dbHRReport->getMonthlyPayslipId($selectedFilterVal,$linkvalue);
                        $headerDetails 			= $this->_dbHRReport->getReportHeaders($linkvalue,$payslipId);
						$this->view->keyArr 	= $data =  $headerDetails['Actual_Header_Details'];
						$this->view->table_data = $tableData = $this->_dbHRReport->rowDriRep($linkvalue);
						$this->view->valueArr 	= array();
					}
					else if($linkvalue == 'Ssnit Tier 1' || $linkvalue == 'Ssnit Tier 2' || $linkvalue === 'Provident Fund Detailed Report')
					{
						$payslipId 			  	= $this->_dbHRReport->getMonthlyPayslipId($selectedFilterVal,$linkvalue);
                        $headerDetails 			= $this->_dbHRReport->getReportHeaders($linkvalue,$payslipId);
						$this->view->keyArr 	= $headerDetails;
						$this->view->table_data = $tableData = $this->_dbHRReport->rowDriRep($linkvalue);
						$this->view->valueArr 	= array();
					}
					elseif($linkvalue == 'Employee Wise Expenses')
					{
						$empId=  $selectedFilterVal[0];
						$sdate = $selectedFilterVal[1];//date('Y-m-01');
						$ldate =  $selectedFilterVal[2];
						$expenseArr = array();
						$reqArr1 = array('Date');

						$expenseTitleArr = $this->_dbHRReport->getEmployeeWiseExpensesTitle($empId,$sdate,$ldate);
						
						// $reqArr2 =array('Others','Total');
						$reqArr2 =array('Total');
						
						$expenseDeductionArr = $this->_dbHRReport->getEmployeeWiseExpensesDeductionTitle($empId,$sdate,$ldate);
						
						foreach($expenseDeductionArr as $title)
						{
							array_push($expenseArr,$title." ".'Deduction');
						}
						$reqArr3=array('Total_Deduction','Payable_Amount');
						$reqArr = array_merge($reqArr1,$expenseTitleArr, $reqArr2,$expenseArr,$reqArr3);
						
						$this->view->keyArr = $reqArr;
						
						$expensesReport=$this->_dbHRReport->getEmployeeWiseExpenses($selectedFilterVal);
						
						$this->view->valueArr = $expensesReport['aaData'];

						$empData = $this->_dbHRReport->getExpenseEmployeeDetails($empId);
						$empData['Start_Date'] = $sdate; 
						$empData['End_Date']   = $ldate;

						$this->view->filterTableData = $empData;		
					}
					else if($linkvalue == 'Hourly Wage Payslip')
					{
						$this->view->keyArr = $data = array('Employee_Id','Employee_Name','Payslip_Month','Designation','Department','Bank_Account_Number',
								'Bank_Name','Branch_Name','IFSC_Code','Regular_Hours', 'OverTime_Hours','Day_Wage','Days_Worked','Holidays','Total_HourlyWages','Total_OvertimeWages','Allowance','Adhoc_Allowance','Provident_Fund',
								'Voluntary_Provident_Fund','Insurance','Bonus','Compoff_Encashment','Shift_Allowance','Reimbursement','Bonus_Allowance','Holiday_Special_Wages','Loan',
								'One-Month_Deduction','Recurring_Deduction','Group_Deduction','Loan_Deduction','Professional_Tax','Tax','Total_Earnings',
								'Total_Deductions','Outstanding_Amount','Net_Pay');
						 
						 
						$this->view->table_data = $tableData = $this->_dbHRReport->rowDriRep($linkvalue);
						$dataValues = $this->_dbHRReport->getWagePayslipDetails($selectedFilterVal, null, null, null, null, null);
						
						$this->view->valueArr = $dataValues['aaData'];
					}
					
					else if($linkvalue == 'Monthly Master Report')
					{
						/** Form the columns array to present in the Print Export modal */
						$data1 = array('Employee_Id', 'Employee_Name','Date_Of_Birth','Probation_Date', 'Confirmation_Date','Resignation_Date','Designation');
						
						$data2 = array('Location', 'Tax_Regime','Previous_Experience','Current_Experience','Total_Experience','Mobile_No', 'Manager_Name',
										'Annual_Ctc','Annual_Gross_Salary','Working_Days', 'Worked_Days','Paid_Unpaid_Leave', 'OverTime_Hours', 'Shortage_Hours');
						
						$orgStruct = $this->_dbHRReport->getOrgStructure();
					
						foreach($orgStruct as $orgDiv)
						{
							array_push($data1,$orgDiv);
						}
						
						$data = array_merge($data1,$data2);
						
						$this->view->keyArr = $data;
						
						$this->view->table_data = $tableData = $this->_dbHRReport->rowDriRep($linkvalue);
						$dataValues = $this->_dbHRReport->getMasterDetails($selectedFilterVal, null, null, null, null, null);
						$this->view->valueArr = $dataValues['aaData'];
					}
					elseif($linkvalue == 'Hourly Master Report')
					{
						$this->view->keyArr = $data = array('Employee_Id', 'Employee_Name','Date_Of_Birth','Date_Of_Join','Confirmation_Date','Resignation_Date',
								'Designation', 'Department', 'Location','Mobile_No', 'Manager_Name', 'Regular_Wage', 'Overtime_Wage','Provident_Fund',
								'Variable_Insurance', 'Fixed_Insurance','Regular_Hours','OverTime_Hours', 'External_EmpId','User_Name');
						$this->view->table_data = $tableData = $this->_dbHRReport->rowDriRep($linkvalue);
						$dataValues = $this->_dbHRReport->getHourlyDetails($selectedFilterVal, null, null, null, null, null);
						$this->view->valueArr = $dataValues['aaData'];
					}
					elseif($linkvalue == "ESI Monthly")
					{
						$result = $this->_dbHRReport->getEsiDetails($selectedFilterVal, null, null, null, null, null,$linkvalue);
						$payslipIds = array_column($result['aaData'],'Payslip_Id');
						$headerArr = $this->_dbHRReport->getReportHeaders($linkvalue,$payslipIds);

						$this->view->keyArr = $data = $headerArr; 
						$this->view->table_data = $tableData = $this->_dbHRReport->rowDriRep($linkvalue);
						$this->view->valueArr = array();
						$this->view->reportConfiguration = $this->_dbHRReport->getReportConfiguration($linkvalue,$data);
					}
					elseif($linkvalue == "ESI Hourly")
					{
						$this->view->keyArr = $data = array('Employee_Id','Employee_Name','ESI_No','Salary_Month','Total_HourlyWages','Total_OvertimeWages','Holiday_SpecialWages','Sum_of_allowance','Sum_of_adhoc_allowance','ESI_Wages','ESI_EE','ESI_ER');
						$this->view->table_data = $tableData = $this->_dbHRReport->rowDriRep($linkvalue);
						$dataValues = $this->_dbHRReport->getEsiHourlyDetails($selectedFilterVal, null, null, null, null, null,$linkvalue);
						$this->view->valueArr = $dataValues['aaData'];
					}
					elseif($linkvalue == 'ESIC Monthly' || $linkvalue == 'ESIC Hourly')
					{
						$this->view->keyArr = $data = array('IP_Number','IP_Name','No_of_Days_for_which_wages_paid','Reason_Code','Total_Monthly_Wages','Last_Working_Day');
						$this->view->table_data = $tableData = $this->_dbHRReport->rowDriRep($linkvalue);
						$dataValues = $this->_dbHRReport->getEsicDetails($selectedFilterVal, null, null, null, null, null,$linkvalue);
						$this->view->valueArr = $dataValues['aaData'];
					}
					elseif($linkvalue == 'Attendance Summary Hourly')
					{
						$this->view->keyArr     = $data = array('Employee_Id','External_Employee_Id','Employee_Name','Shift_Start_Date','Shift_End_Date','Minimum_Punch_In','Maximum_Punch_Out','Regular_Hours','Overtime_Hours','Description');
						$this->view->table_data = $tableData = $this->_dbHRReport->rowDriRep($linkvalue);
						$dataValues             = $this->_dbHRReport->getAttendanceSummaryHourly($selectedFilterVal,null, null, null, null, null,$linkvalue);
						$this->view->valueArr   = $dataValues['aaData'];
					}
					elseif($linkvalue == 'Attendance Summary Monthly')
					{
						$this->view->keyArr     = $data = array('Employee_Id','External_Employee_Id','Employee_Name','Work_Schedule','Shift_Date','Day_Type','Minimum_Shift_Start_Time','Maximum_Shift_End_Time','Minimum_Punch_In','Maximum_Punch_Out','Regular_Hours','Overtime_Hours','Total_Working_Hours','Shift_Exists','Attendance_Exist','Leave_Exist','CompensatoryOff_Exist','ShortTimeOff_Exist');
						$this->view->table_data = $tableData = $this->_dbHRReport->rowDriRep($linkvalue);
						$dataValues             = $this->_dbHRReport->getAttendanceSummaryMonthly($selectedFilterVal,null, null, null, null, null,$linkvalue);
						$this->view->valueArr   = $dataValues['aaData'];
					}
					elseif($linkvalue == 'Additional Wage Summary')
					{
						$this->view->keyArr     = $data = $this->_dbHRReport->getReportHeaders($linkvalue);
						$this->view->table_data = $tableData = $this->_dbHRReport->rowDriRep($linkvalue);
						$dataValues             = $this->_dbHRReport->getAdditionalWageSummary($selectedFilterVal);
						$this->view->valueArr   = $dataValues['aaData'];
					}
					elseif($linkvalue == 'Uan Based Ecr')
					{
						
						$this->view->keyArr =array('Uan','Member_Name','Gross_Wages','Epf_Wages','Eps_Wages','Edli_Wages','Epf_Contribution_Remitted',
						   'Eps_Contribution_Remitted','Epf_Eps_Contribution_Remitted','Ncp_Days','Refund_Of_Advances');
						
						$this->view->table_data = $tableData = $this->_dbHRReport->rowDriRep($linkvalue);
						$dataValues = $this->_dbHRReport->getUanEcrDetails($selectedFilterVal, null, null, null, null, null,$linkvalue);
						$this->view->valueArr = $dataValues['aaData'];
					}
					elseif(strtolower($linkvalue)== 'uan based ecr(arrear)')
					{
						
						$this->view->keyArr =array('Uan','Member_Name','Epf_Wages','Eps_Wages','Edli_Wages','Epf_Contribution_Remitted',
						   'Eps_Contribution_Remitted','Epf_Eps_Contribution_Remitted');
						
						$this->view->table_data = $tableData = $this->_dbHRReport->rowDriRep($linkvalue);
						$dataValues = $this->_dbHRReport->getArrearProvidentFundEcrReport($selectedFilterVal);
						$this->view->valueArr = $dataValues['aaData'];
					}
					elseif($linkvalue == 'Uan Based Ecr Hourly')
					{
						
						$this->view->keyArr =array('Uan','Member_Name','Gross_Wages','Epf_Wages','Eps_Wages','Edli_Wages','Epf_Contribution_Remitted',
						   'Eps_Contribution_Remitted','Epf_Eps_Contribution_Remitted','Ncp_Days','Refund_Of_Advances');
						
						$this->view->table_data = $tableData = $this->_dbHRReport->rowDriRep($linkvalue);
						$dataValues = $this->_dbHRReport->getUanBasedEcrHourly($selectedFilterVal, null, null, null, null, null,$linkvalue);
						$this->view->valueArr = $dataValues['aaData'];
					}
					elseif($linkvalue == 'Eft Monthly' || $linkvalue == 'Eft Hourly')
					{
						$eftHeader = $this->_dbHRReport->eftHeader($selectedFilterVal[0]);
						$reqArr = array();
						for($i=0;$i<count($eftHeader);$i++) 
						{
							array_push($reqArr, $eftHeader[$i]);
						}
						$this->view->keyArr = $reqArr;
						$this->view->table_data = $eftHeader;
						$dataValues = $this->_dbHRReport->getEftDetails($selectedFilterVal,$reqArr,null,null,null,null,$linkvalue,null);
						$this->view->valueArr = $dataValues['aaData'];
					}
				    elseif($linkvalue == 'Loan Amortization')
                    {
                        $loanId = $this->_getParam('loanId', null);
                        
                        $this->view->keyArr =array('Month','Loan_Balance','Interest_Amount','Principal_Amount','EMI','Outstanding_Amount');
                        
                        $this->view->valueArr = $this->_dbHRReport->loanAmortization($loanId);
                        
                        $this->view->table_data = $tableData = $this->_dbHRReport->rowDriRep($linkvalue);
				    }
					elseif($linkvalue == 'Attendance Shortage')
					{						
						$reqArr =array('Employee_Id','Name','Department','Total_Working_Hours','Total_Worked_Hours');
												
						$this->view->keyArr = $reqArr;
						
						$reportUser = array('Is_Manager'  	=> $this->_reportAccessRights['Employee']['Is_Manager'],
										 'View'       	 	=> $this->_reportAccessRights['Employee']['View'],
										 'Optional_Choice'	=> $this->_reportAccessRights['Employee']['Optional_Choice'],
										 'Admin'      		=> $this->_reportAccessRights['Admin'],
										 'LogId' 	        => $this->_logEmpId,
										 'Employee_Name' 	=> $this->_dbPersonal->employeeId($this->_logEmpId));
                
						$attendanceReport = $this->_dbAttendanceReport->attendanceShortageReport('','','','','','',$filterArray,$reportUser,'');
						
						$this->view->valueArr = $attendanceReport['aaData'];
					}
					elseif($linkvalue == "Bank Salary Statement" || $linkvalue == "Fixed Health Insurance")
					{
						$this->view->keyArr     = $data = $this->_dbHRReport->getReportHeaders($linkvalue);
						$this->view->table_data = $tableData = $this->_dbHRReport->rowDriRep($linkvalue);
						$this->view->valueArr 	= array();
						$this->view->reportConfiguration 	= $this->_dbHRReport->getReportConfiguration($linkvalue,$data);
					}
					else
					{
                        $this->view->table_data = $tableData = $this->_dbHRReport->rowDriRep($linkvalue);
						
						$data = $this->_dbHRReport->forExpt($selectedFilterVal,$selectedRelationVal,$tableData);
                        
						$dataArray = explode("-",$tableData['Rep_Filter_Visible']);
                        
                        $customPAN = $this->_ehrTables->getCustomFields('PAN No.');
                        
                        $pan = array();
                        $pan['Enable'] =  1;
                        $pan['Required'] = 0;
                        $pan['Field_Name'] = 'PAN';
                        
                        if($customPAN['Field_Name'] == 'PAN No.')
                        {
                            $pan['Enable'] = $customPAN['Enable'];
                            $pan['Required'] =  $customPAN['Required'];
                            $pan['Field_Name'] = ($customPAN['New_Field_Name'] != '' ?  $customPAN['New_Field_Name'] : 'PAN');
                        }
                        			
						if(!empty($data))
						{
							foreach ($data[0] as $row)
							{								
									unset($row[$tableData['Bar_Y']]);
									
									if($pan['Enable'])
									{
										$keys = array_keys($row);
										$index = array_search('PAN', $keys);
										
										if ($index !== false) {
											$keys[$index] = $pan['Field_Name'];
											$row = array_combine($keys, $row);
										}
									}
									else
									{
										unset($row['PAN']);
									}
									
									$resultArr[] = $row;
							}

							if($linkvalue == 'Reimbursement')
							{
								foreach($resultArr as $key => $row)
								{
									unset($resultArr[$key]['Line_Item_Id']);
								}
							}
							$this->view->keyArr = array_keys($resultArr[0]);
							$this->view->valueArr = array_values($resultArr);
							$filterVisible = $this->_dbHRReport->filterVisible($selectedFilterVal,$selectedRelationVal,$tableData);
							$prevArr = array();
							
							for($i = 0;$i<count($filterVisible);$i++)//filter portion
							{
								$prevArr[$dataArray[$i]] = $filterVisible[$i];
							}

							//Get the report configuration details to allow the user to select the columns in the export excel popup
							$reportConfigurationTitles = array('Timesheet Comprehensive','Lwf','Professional Tax Monthly');
							if(in_array($linkvalue,$reportConfigurationTitles))
							{
								$this->view->reportConfiguration 	= $this->_dbHRReport->getReportConfiguration($linkvalue,array_keys($resultArr[0]));
							}

							$this->view->filterTableData = $prevArr;						
						} 
					}			
				}
			}
		}
		else
		{
			$this->_helper->redirector('index', 'hr-reports', 'reports',array('_mId'=>'hr-reports','_mNme'=>10));
		}
    }
    
    //version: 0.4 =>Added footer for the reports(non table driven reports)
	//version: 0.3 =>Exclude pf and insurance from earnings in print and pdf when "retirals based on basic" is true    
    /**
     * return HTML table for print and pdf 
     * @param str $linkvalue - selected report title
     * @param array $headerArr - selected table header
     * @param array $selectedFilterVal - selected fileters
     * @param array $tableData - record from database
     * @return HTML
     */
	public function mainTable($linkvalue, $headerArr , $selectedFilterVal, $selectedRelationVal, $tableData)
	{
		
		$dbOrgDetail = new Organization_Model_DbTable_OrgSettings();
		$orgCode = $this->_ehrTables->getOrgCode();
		$salaryCalcDetail = $dbOrgDetail->viewOrgDetail($orgCode);
		
		if($linkvalue == 'Employee Wise Expenses')
		{
			$data = $this->_dbHRReport->getEmployeeWiseExpenses($selectedFilterVal, null, null, null, null, null,null, 'export',$linkvalue);
			$resultArr = $data['aaData'];
		}
		else if($linkvalue == 'Hourly Wage Payslip')
		{
			$data = $this->_dbHRReport->getWagePayslipDetails($selectedFilterVal, null, null, null, null, null);
		}
		else if($linkvalue == 'Monthly Master Report')
		{
			$data = $this->_dbHRReport->getMasterDetails($selectedFilterVal, null, null, null, null, null);
			$resultArr = $data['aaData'];
		}
		else if($linkvalue == 'Hourly Master Report')
		{
			$data = $this->_dbHRReport->getHourlyDetails($selectedFilterVal, null, null, null, null, null);
			$resultArr = $data['aaData'];
		}
		else if($linkvalue == 'ESI Monthly')
		{
			$data = $this->_dbHRReport->getEsiDetails($selectedFilterVal, null, null, null, null, null,$linkvalue);
			$resultArr = $data['aaData'];
		}
		else if($linkvalue == 'ESI Hourly')
		{
			$data = $this->_dbHRReport->getEsiHourlyDetails($selectedFilterVal, null, null, null, null, null,$linkvalue);
			$resultArr = $data['aaData'];
		}
		else if($linkvalue == 'ESIC Monthly' || $linkvalue == 'ESIC Hourly')
		{
			$data = $this->_dbHRReport->getEsicDetails($selectedFilterVal, null, null, null, null, null,$linkvalue);
			$resultArr = $data['aaData'];
		}
		elseif($linkvalue == 'Attendance Summary Hourly')
		{
			$data = $this->_dbHRReport->getAttendanceSummaryHourly($selectedFilterVal, null, null, null, null, null,$linkvalue);
			$resultArr = $data['aaData'];
		}
		elseif($linkvalue == 'Attendance Summary Monthly')
		{
			$data = $this->_dbHRReport->getAttendanceSummaryMonthly($selectedFilterVal, null, null, null, null, null,$linkvalue);
			$resultArr = $data['aaData'];
		}
		elseif($linkvalue == 'Additional Wage Summary')
		{
			$data = $this->_dbHRReport->getAdditionalWageSummary($selectedFilterVal);
			$resultArr = $data['aaData'];
		}
		else if($linkvalue == 'Uan Based Ecr')
		{
			$data = $this->_dbHRReport->getUanEcrDetails($selectedFilterVal, null, null, null, null, null,$linkvalue);
			$resultArr = $data['aaData'];
		}
		else if(strtolower($linkvalue)== 'uan based ecr(arrear)')
		{
			$data = $this->_dbHRReport->getArrearProvidentFundEcrReport($selectedFilterVal);
			$resultArr = $data['aaData'];
		}
		else if($linkvalue == 'Uan Based Ecr Hourly')
		{
			$data = $this->_dbHRReport->getUanBasedEcrHourly($selectedFilterVal, null, null, null, null, null,$linkvalue);
			$resultArr = $data['aaData'];
		}
		else
		{
			$data = $this->_dbHRReport->filter($selectedFilterVal,$selectedRelationVal,$tableData);//will return result set
            foreach ($data as $row)
			{
				unset($row[$tableData['Bar_Y']]);
				$resultArr[] = $row;
			}
		}
		if($linkvalue== 'Employee Wise Expenses')
		{
			$empId= $selectedFilterVal[0];//$this->_logEmpId;
			$sdate = $selectedFilterVal[1];//date('Y-m-01');
			$ldate = $selectedFilterVal[2];//date('Y-m-t');
			
			$expenseArr = array();
			$reqArr1 = array('Date');
	
			$expenseTitleArr = $this->_dbHRReport->getEmployeeWiseExpensesTitle($empId,$sdate,$ldate);
			
			// $reqArr2 =array('Others','Total');
			$reqArr2 =array('Total');
			
			$expenseDeductionArr = $this->_dbHRReport->getEmployeeWiseExpensesDeductionTitle($empId,$sdate,$ldate);
			
			foreach($expenseDeductionArr as $title)
			{
				array_push($expenseArr,$title." ".'Deduction');
			}
			$reqArr3=array('Total_Deduction','Payable_Amount');
			$headerArr = array_merge($reqArr1,$expenseTitleArr, $reqArr2,$expenseArr,$reqArr3);
			
		}
	 
		$tabstr =  '<table style="width:98%;margin:0px auto;margin-bottom:20px;border-collapse: collapse;" border="1" class="reports_grid"><thead><tr style="font-weight:bold;background:rgb(190, 195, 201);">';
		foreach ($headerArr as $head) 
		{
            $headVal = 	str_replace('_', ' ', $head);
			$tabstr .= '<th class = "fontSize">'.($headVal == 'Paid Unpaid Leave' ? 'Paid/Unpaid Leave':$headVal).'</th>';
		}
		
		if ($linkvalue == 'Hourly Wage Payslip')
		{
			$inscentiveFields = array('Total_HourlyWages','Total_OvertimeWages','Allowance','Adhoc_Allowance',
					'Bonus','Compoff_Encashment','Shift_Allowance','Reimbursement','Bonus_Allowance','Holiday_Special_Wages','Loan');
			$deductionFields = array('Provident_Fund','Voluntary_Provident_Fund','Insurance','One-Month_Deduction','Recurring_Deduction','Group_Deduction',
					'Loan_Deduction','Professional_Tax','Tax');
			
			$headerFirstHalfFields = array('Employee_Id','Employee_Name','Payslip_Month','Designation','Department','Bank_Account_Number','Bank_Name','Branch_Name',
										   'IFSC_Code','Regular_Hours', 'OverTime_Hours','Day_Wage','Days_Worked','Holidays');
			$headerSecondHalfFields = array('Total_Earnings','Total_Deductions','Outstanding_Amount','Net_Pay');
			 
			$selectedInscentiveFields = array_intersect($inscentiveFields,$headerArr);
			$selectedDeductionFields = array_intersect($deductionFields,$headerArr);
			$selectedHeaderFirstHalfFields = array_intersect($headerFirstHalfFields,$headerArr);
			$selectedHeaderSecondHalfFields = array_intersect($headerSecondHalfFields,$headerArr);
			 
			$cntSelectedInscentiveFields = count($selectedInscentiveFields);
			$cntSelectedDeductionFields = count($selectedDeductionFields);
			 
			$tabstr =  '<table style="width:98%;margin-bottom:20px;border-collapse: collapse;" border="1" class="reports_grid"><thead><tr style="font-weight:bold;background:rgb(190, 195, 201);" class = "fontSize">';
			foreach ($selectedHeaderFirstHalfFields as $fld) 
			{
				$tabstr .= '<th rowspan=2 class = "fontSize">'.str_replace('_', ' ', $fld).'</th>';;
			}
			$inscentiveStr = '';
			if( $cntSelectedInscentiveFields > 0) 
			{
				$tabstr .= "<th colspan='$cntSelectedInscentiveFields' class = 'fontSize'>Earnings</th>";
				foreach ($selectedInscentiveFields as $fld) 
				{
					if($fld == 'Holiday_Special_Wages') 
					{
						$fld = 'Holiday_Wages';
					}
					$inscentiveStr.='<th class = "fontSize" style="font-weight:bold;background:rgb(190, 195, 201);">'.str_replace('_', ' ', $fld).'</th>';;
				}
			}
			$deductionStr = '';
			if( $cntSelectedDeductionFields > 0) 
			{
				$tabstr .= "<th colspan='$cntSelectedDeductionFields' class = 'fontSize'>Deductions</th>";
				foreach ($selectedDeductionFields as $fld) 
				{
					if($fld == 'Loan_Deduction') 
					{
						$fld = 'Loan';
					}
					$deductionStr.='<th class = "fontSize" style="font-weight:bold;background:rgb(190, 195, 201);">'.str_replace('_', ' ', $fld).'</th>';
				}
			}
			foreach ($selectedHeaderSecondHalfFields as $fld) 
			{
				$tabstr .= '<th rowspan=2 class = "fontSize">'.str_replace('_', ' ', $fld).'</th>';
			}
			$tabstr .= '</tr><tr class = "fontSize">'.$inscentiveStr.$deductionStr;
			foreach($data['aaData'] as $row)
			{
				unset($row[$tableData['Bar_Y']]);
				$resultArr[] = $row;
			}
			/* if(in_array('Insurance', $headerArr)) {
				$k = array_search(end($selectedInscentiveFields), $headerArr);
				array_splice( $headerArr, $k+1, 0,  'Insurance Deduction');
				if(in_array('Provident_Fund', $headerArr)) {
					array_splice( $headerArr, $k+1, 0,  'Provident Fund Deduction');
				}
			}
			else if(in_array('Provident_Fund', $headerArr)) {
				$k = array_search(end($selectedInscentiveFields), $headerArr);
				array_splice( $headerArr, $k+1, 0,  'Provident Fund Deduction');
			} */
			
		}
		
		$tabstr .= '</tr></thead>';
		
		foreach ($resultArr as $row) 
		{
           
			$tabstr .= '<tr class = "fontSize">';
			foreach ($headerArr as $hArr) 
			{
				if($linkvalue != 'Employee Wise Expenses')
				{
                	$hArr = str_replace(' ', '_', $hArr);
				}
                if(!isset($row[$hArr])) {
                    $tabstr .= '<td style="text-align:left;">';
					//$tabstr .= '<td  class = "fontSize" style="border-bottom:1px solid #BDBDBD; text-align:left;">';
				}
				else if($row[$hArr] == '-')
				{
                    $tabstr .= '<td style="text-align:center;">';
					//$tabstr .= '<td  class = "fontSize" style="border-bottom:1px solid #BDBDBD; text-align:center;">';
				}
				else if(is_numeric($row[$hArr]))
				{
                    $tabstr .= '<td style="text-align:left;">';
					//$tabstr .= '<td  class = "fontSize" style="border-bottom:1px solid #BDBDBD; text-align:left;">';
				}
				else {
                    $tabstr .= '<td style="text-align:left;">';
					//$tabstr .= '<td  class = "fontSize" style="border-bottom:1px solid #BDBDBD; text-align:left;">';
				}
                
				@$tabstr .= $row[$hArr].'</td>';
			}
			$tabstr .= '</tr>';
		}
		
		//for adding footer for the report
		if(in_array($linkvalue,array('ESI Hourly','ESI Monthly','Hourly Wage Payslip','Monthly Master Report','Hourly Master Report')))
		{	
			$tabstr .= '<tr class = "fontSize">';
			foreach($data['footer'] as $footerValues)
			{
				foreach ($headerArr as $hArr) 
				{
					$hArr = str_replace(' ', '_', $hArr);
					$tabstr .= '<td style="text-align:right">';
					if(isset($footerValues[$hArr]) && $footerValues[$hArr]>0)
					{	
						@$tabstr .= $footerValues[$hArr].'</td>';
					}
					elseif(isset($footerValues[$hArr]) && $footerValues[$hArr] == '-')
					{
						@$tabstr .= '<center>-</center></td>';
					}
					else
					{
						@$tabstr .= '</td>';
					}	 
				}
				
			}
			$tabstr .= '</tr>';
		}
		
		$tabstr .= '</table>';
        return $tabstr;
	}

	public function downloadTextAction()
    {
	    $this->_helper->layout()->disableLayout();
		
		if(isset($_SERVER['HTTP_REFERER']) || $_COOKIE['isNativeMobileApp'] == 1)
	    {
			$linkvalue = $this->_getParam('linkValue', null);
			$linkvalue = filter_var($linkvalue,FILTER_SANITIZE_STRIPPED);
			$linkvalue = ucwords(str_replace('-', ' ', $linkvalue));
			
			$filterArray = $this->_getParam('filterArray', null);
			
			if($filterArray != '')
			{
				$selectedFilterVal = explode(',',$filterArray);
			}
			else
			{
				$selectedFilterVal = '';
			}
			
			$selectedRelationVal = array();
			for($p=0;$p<=15;$p++)
			{
				array_push($selectedRelationVal,'');
			}		
			
			$modName = $this->_getParam('_modName', null);
			$modName = filter_var($modName, FILTER_SANITIZE_STRING);
			$modName = $modName.' reports';
			$this->_reportAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $modName);
		    
		    if($this->_reportAccessRights['Employee']['Optional_Choice'] == 1)
		    {
				if(!empty($linkvalue))
				{
					if($linkvalue == "Eft Monthly") {
						$linkvalue = "Eft Monthly";
					}
					else if($linkvalue == "Eft Hourly") {
						$linkvalue = "Eft Hourly";
					}
					else if($linkvalue == "Esi Monthly")
					{
						$linkvalue = "ESI Monthly"; 
					}
					else if($linkvalue == "Esi Hourly")
					{
						$linkvalue = "ESI Hourly"; 
					}
					else if($linkvalue == "Esic Monthly")
					{
                         $linkvalue = "ESIC Monthly"; 
					}
					else if($linkvalue == "Esic Hourly")
					{
						$linkvalue = "ESIC Hourly";
					}
				
					if($linkvalue == 'Uan Based Ecr')
					{
						$ecr =array('Uan','Member_Name','Gross_Wages','Epf_Wages','Eps_Wages','Edli_Wages','Epf_Contribution_Remitted',
					   'Eps_Contribution_Remitted','Epf_Eps_Contribution_Remitted','Ncp_Days','Refund_Of_Advances');
						
					   $this->view->result = $this->_dbHRReport->getUanEcrDetails($selectedFilterVal, null, null, null, null, null,$linkvalue,$ecr);
					}
					if(strtolower($linkvalue)== 'uan based ecr(arrear)')
					{
						$ecr =array('Uan','Member_Name','Epf_Wages','Eps_Wages','Edli_Wages','Epf_Contribution_Remitted',
					   'Eps_Contribution_Remitted','Epf_Eps_Contribution_Remitted');
						
					   $this->view->result = $this->_dbHRReport->getArrearProvidentFundEcrReport($selectedFilterVal,$ecr);
					}
					if($linkvalue == 'Uan Based Ecr Hourly')
					{
						$ecr =array('Uan','Member_Name','Gross_Wages','Epf_Wages','Eps_Wages','Edli_Wages','Epf_Contribution_Remitted',
					   'Eps_Contribution_Remitted','Epf_Eps_Contribution_Remitted','Ncp_Days','Refund_Of_Advances');
						
						$this->view->result = $this->_dbHRReport->getUanBasedEcrHourly($selectedFilterVal, null, null, null, null, null,$linkvalue,$ecr);
					}
					if($linkvalue == "ESI Monthly")
					{
						$esi = array('Employee_Id','Employee_Name','ESI_No','ESI_Wages','ESI_EE','ESI_ER');
						$this->_dbHRReport->getEsiDetails($selectedFilterVal, null, null, null, null, null,$linkvalue,$esi);
					}
					if($linkvalue == "ESI Hourly")
					{
						$esi = array('Employee_Id','Employee_Name','ESI_No','ESI_Wages','ESI_EE','ESI_ER');
						$this->_dbHRReport->getEsiHourlyDetails($selectedFilterVal, null, null, null, null, null,$linkvalue,$esi);
					}
					if($linkvalue == 'ESIC Monthly' || $linkvalue == 'ESIC Hourly')
					{
						$esic = array('IP_Number','IP_Name','No_of_Days_for_which_wages_paid','Reason_Code','Total_Monthly_Wages','Last_Working_Day');
			     		$this->_dbHRReport->getEsicDetails($selectedFilterVal, null, null, null, null, null,$linkvalue,$esic);
					}
					if($linkvalue == 'Attendance Summary Hourly')
					{
						$data = $this->_dbHRReport->getAttendanceSummaryHourly($selectedFilterVal, null, null, null, null, null,$linkvalue);
						$resultArr = $data['aaData'];
					}
					if($linkvalue == 'Attendance Summary Monthly')
					{
						$data = $this->_dbHRReport->getAttendanceSummaryMonthly($selectedFilterVal, null, null, null, null, null,$linkvalue);
						$resultArr = $data['aaData'];
					}
					if($linkvalue == 'Additional Wage Summary')
					{
						$data = $this->_dbHRReport->getAdditionalWageSummary($selectedFilterVal);
						$resultArr = $data['aaData'];
					}
				
					if($linkvalue=='Eft Monthly' || $linkvalue=='Eft Hourly')
					{
						$eftHeader = $this->_dbHRReport->eftHeader($selectedFilterVal[0]);
						$eftData = array();
						for($i=0;$i<count($eftHeader);$i++) 
						{
							array_push($eftData, $eftHeader[$i]);
						}
						$seperator = $this->_dbHRReport->getEftSeperator($selectedFilterVal[0]);
						$getEftHeaderSchema = $this->_dbHRReport->getEftHeaderSchema($selectedFilterVal[0]);
						
						$dataValues = $this->_dbHRReport->getEftDetails($selectedFilterVal,$eftData,null,null,null,null,$linkvalue,null);
						$rowEFT = $dataValues['aaData'];
						if(!empty($rowEFT))
						{	
							$eftCount=count($rowEFT);
							$eftDataCount=count($eftData);
							if($eftCount>0)
							{
								$handle = fopen("EFT.txt","w");
								if(!empty($getEftHeaderSchema))
								{
									$eftHeaderCount=count($getEftHeaderSchema);
									$getEftHeaderDetails =array();
									if($eftHeaderCount>0)
									{
										if($linkvalue=='Eft Hourly')
										{
											$tableName= $this->_ehrTables->wagePayslip;
										}
										else 
										{
											$tableName= $this->_ehrTables->monthlyPayslip; 
										}

										for($i=0;$i<$eftHeaderCount;$i++)
										{
											if($getEftHeaderSchema[$i]['Hrapp_Bank_Field_Id']=='17')
											{
												$getEftHeaderDetails = $getEftHeaderSchema[$i]['EFT_Header_Value'];
											}
											else
											{
												$getEftHeaderDetails = $this->_dbHRReport->getEftMainHeader($getEftHeaderSchema[$i]['Hrapp_Bank_Field_Id'],$tableName,$selectedFilterVal);
											}
											fwrite($handle,$getEftHeaderDetails);
											if($eftHeaderCount!=($i+1))
												fwrite($handle,$seperator);
											
										}
											fwrite($handle, "\r\n");
									}
								}

							    for ($i=0;$i<$eftCount;$i++)
								{
									for($j=0;$j<$eftDataCount;$j++)
									{
										fwrite($handle,$rowEFT[$i][$eftData[$j]]);
										if($eftDataCount!=($j+1))
										fwrite($handle,$seperator);
									}
									fwrite($handle, "\r\n");
								}
								fclose($handle);
								header('Content-Type: application/octet-stream');
								header('Content-Disposition: attachment; filename='.basename('EFT.txt'));
								header('Expires: 0');
								header('Cache-Control: must-revalidate');
								header('Pragma: public');
								header('Content-Length: ' . filesize('EFT.txt'));
								readfile('EFT.txt');
								exit;
							}
						}
					}
				}
		    }
	    } else {
			$this->_helper->redirector('index', 'hr-reports', 'reports',array('_mId'=>'hr-reports','_mNme'=>10));
		}
    }
    
    public function createPiechartAction()
    {
        $this->_helper->layout()->disableLayout();
        
        if(isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('create-piechart', 'html')->initContext();
			
			$modName = $this->_getParam('_modName', null);
			$modName = filter_var($modName, FILTER_SANITIZE_STRING);
			
			$modName = $modName.' reports';
			
			$this->_reportAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $modName);
			
			if ($this->_reportAccessRights['Employee']['View'] == 1)
			{
				
				$linkvalue = $this->_getParam('linkValue', null);
				$linkvalue = filter_var($linkvalue,FILTER_SANITIZE_STRIPPED);
				$linkvalue = ucwords(str_replace('-', ' ', $linkvalue));
				
                //$filtervalue = $this->_getParam('filterValue',null);
                
                
				$tableData = $this->_getParam('tableData', null);
				if($tableData != '')
				{
					$this->view->data = $this->_dbHRReport->rowDriRep($linkvalue);
				}
				else
				{
					$filterArray = $this->_getParam('filterArray', null);
                    
                    if(empty($filterArray))
                    {
                        //$filterArray = 'All';
                        $selectedFilterVal = $this->_getParam('_fv', null);
                    
                        $selectedRelationVal = $this->_getParam('_rv', null);
                    }
                    else
                    {
                        if($filterArray != '')
                        {
                            $selectedFilterVal = explode(',',$filterArray);
                        }
                        else
                        {
                            $selectedFilterVal = '';
                        }
                        
                        $selectedRelationVal = array();
                        for($p=0;$p<=15;$p++)
                        {
                            array_push($selectedRelationVal,'');
                        }
                    }
					
					
					$chartHt = $this->_getParam('_ctHt', null);
					
					$chartWt = $this->_getParam('_ctWt', null);
					
					$this->view->chartHt = ($chartHt != null) ? $chartHt : 338;
					$this->view->chartWt = ($chartWt != null) ? $chartWt : 'auto';
					
					$chartRender = $this->_getParam('_ctRent', null);
					$chartRender = filter_var($chartRender, FILTER_SANITIZE_STRING);
				
					$this->view->chartRender = ($chartRender != null) ? $chartRender : 'bar';
					
					$tableData = $this->_dbHRReport->rowDriRep($linkvalue);
					
					$this->view->table_data = $tableData;
					
                    $data = $this->_dbHRReport->filter($selectedFilterVal,$selectedRelationVal, $tableData);
                    
                    $gen = array();
                    $num = array();
                    
                    if(count($data) >= 2)
                    {
                        foreach($data as $row)
                        {
                            if(isset($row) && isset($tableData['Pie_Field']) && isset($tableData['Stack_Categ_Point']))
                            {
                                if($row[$tableData['Pie_Field']]!=null && $row[$tableData['Stack_Categ_Point']])
                                {
                                    $gen[] = ucfirst($row[$tableData['Pie_Field']]);
                                    $num[] = $row[$tableData['Stack_Categ_Point']];
                                }
                            }
                        }
                    }
                    $this->view->data = array($data,$gen,$num);
					//$this->view->data  = $this->_dbHRReport->filter($selectedFilterVal,$selectedRelationVal, $tableData);
					   
				}
			}			
        }
        else
        {
            $this->_helper->redirector('index', 'hr-reports', 'reports',array('_mId'=>'hr-reports','_mNme'=>10));
        }
    }
    
    public function checkPanTanAction()
    {
        $this->_helper->layout()->disableLayout();
        $this->view->valid = $this->_dbHRReport->checkPanTan();    
    }
    
    public function loanAmortizationAction()
    {
        $this->_helper->layout()->disableLayout();
        if(isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('loan-amortization', 'json')->initContext();
			
			$modName = $this->_getParam('_modName', null);
			$modName = filter_var($modName, FILTER_SANITIZE_STRING);
			$modName = $modName.' reports';
			
			$this->_reportAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $modName);
			
            if($this->_reportAccessRights['Employee']['Optional_Choice'] == 1)
			{
                $loanId = $this->_getParam('loanId', null);
                
                 
                $loanVal = $this->_dbHRReport->loanAmortization($loanId);
                
                $payslipcss = 'width:101%;border-collapse: collapse;border-spacing: none;font-size:10px;border-left: 1px solid black;border-right: 1px solid black;border-bottom: none;border-top: 1px solid black;';
                $payslipTabthodd = "padding-left: 1%;padding-right: 1%;height: 25px;text-align: left;border-bottom: 1px solid black;border-top: 1px solid black;width:38%";
                $payslipTabtheven = "padding-left: 1%;padding-right: 1%;height: 25px;text-align: right;border-bottom: 1px solid black;border-right: 1px solid black;border-top: 1px solid black;width:12%";
                $payslipTabtdodd = "height: 25px;padding-left: 1%;padding-right: 1%;";
                $payslipTabtdeven = "text-align: right;border-right: 1px solid black;height: 25px;padding-left: 1%;padding-right: 1%;";
                $paysliptrtdodd = "height: 25px;padding-left: 1%;padding-right: 1%;padding-top:2%;width:20%;";
                $paysliptrtdeven = "height: 25px;padding-left: 1%;padding-right: 1%;padding-top:2%;width:30%;";
                $payslipHead = "font-weight: bold;font-size: 15px;text-align:center;";//;text-align:center			
                
                $exportStr = '';   
                //$exportStr .= '<div style = "width: 99%;page-break-inside: avoid;margin-top:10%;">';
            
                $exportStr .= '<table style = "'.$payslipcss.'border-bottom:none;border-top:none">';
                $exportStr .= '<tr><td style="'. $paysliptrtdodd .'">Employee Name</td><td style="'. $paysliptrtdeven .'">: '.$loanVal[0]['Employee_Name'] .'</td><td style="'. $paysliptrtdodd .'">Loan Amount</td><td style="'. $paysliptrtdeven .'">: '.$loanVal[0]['Loan_Amount'].'</td></tr>';
                $exportStr .= '<tr><td style="'. $paysliptrtdodd .'">Interest Rate</td><td style="'. $paysliptrtdeven .'">: '.$loanVal[0]['Interest'] .'</td><td style="'. $paysliptrtdodd .'">Tenure</td><td style="'. $paysliptrtdeven .'">: '.(!empty($salary['Payslip']['Pf_PolicyNo']) ? $salary['Payslip']['Pf_PolicyNo'] : " -").'</td></tr>';
                
                //if($salStruct == 'Monthly')
                //    $exportStr .= '<tr><td style="'. $paysliptrtdodd .'">Days Worked</td><td style="'. $paysliptrtdeven .'">: '.$salary['WorkedDays'].'</td><td style="'. $paysliptrtdodd .'">ESI Account Number</td><td style="'. $paysliptrtdeven .'">: '.(!empty($salary['Payslip']['Policy_No']) ? $salary['Payslip']['Policy_No'] : " -").'</td></tr>';
                //else
                    //$exportStr .= '<tr><td style="'. $paysliptrtdodd .'">Hours Worked</td><td style="'. $paysliptrtdeven .'">: '.$salary['WorkedHours'].'</td><td style="'. $paysliptrtdodd .'">ESI Account Number</td><td style="'. $paysliptrtdeven .'">: '.(!empty($salary['Payslip']['Policy_No']) ? $salary['Payslip']['Policy_No'] : " -").'</td></tr>';
                
                //$exportStr .= '<tr><td style="'. $paysliptrtdodd .'">PAN Number</td><td style="'. $paysliptrtdeven .'">: '.$empPan.'</td><td style="'. $paysliptrtdodd .'">Paid Leave</td><td style="'. $paysliptrtdeven .'">: '.$pl.'</td></tr>';			
                //$exportStr .= '<tr><td style="'. $paysliptrtdodd .'">Unpaid Leave</td><td style="'. $paysliptrtdeven .'">: '.$upl.'</td><td></td><td></td></tr>';
                //$exportStr .= '<tr><td style="'. $paysliptrtdodd .'">Paid Leave</td><td style="'. $paysliptrtdeven .'">: '.$pl.'</td><td style="'. $paysliptrtdodd .'">Unpaid Leave</td><td style="'. $paysliptrtdeven .'">: '.$upl.'</td></tr>';
                $exportStr .= '</table>';
                
                $this->view->result = $exportStr;
            //return $exportStr;
            }
        }
    }
	
	//Landing page for all the hr reports
	public function viewHrreportsAction()
	{
        $this->_helper->layout()->disableLayout()->setLayout('admin_layout');
        
		$this->_reportAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, 'HR Reports');
		$this->view->reportUser =  $this->_reportAccessRights['Employee'];	
		
		$Report['Organization & HR Reports']	=	$this->_dbHRReport->repTitleAndHeader('Organization & HR Reports');

		$array = array('Organization & HR Reports');
		$this->view->Data = $Report;
		$this->view->key = $array;
    }
	
	//Landing page for all the employees reports
	public function viewEmployeesReportsAction()
    {
        $this->_helper->layout()->disableLayout()->setLayout('admin_layout');
        
		$this->_reportAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, 'Employees Reports');
		$this->view->reportUser =  $this->_reportAccessRights['Employee'];	
		
		$Report['Attendance']	=	$this->_dbHRReport->repTitleAndHeader('Attendance');
		$Report['Career']		=	$this->_dbHRReport->repTitleAndHeader('Career Details');
		$Report['Employee']		=	$this->_dbHRReport->repTitleAndHeader('Employee Profile');
		$Report['Leaves']		=	$this->_dbHRReport->repTitleAndHeader('Leaves');
		
		$array = array('Attendance','Career','Employee','Leaves');
		$this->view->Data = $Report;
		$this->view->key = $array;
		
    }
	
	//Landing page for all the payroll reports
	 public function viewPayrollReportsAction()
    {
        $this->_helper->layout()->disableLayout()->setLayout('admin_layout');
        
		$this->_reportAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, 'Payroll Reports');
		$this->view->reportUser =  $this->_reportAccessRights['Employee'];	
		
		$Report['Salary']	=	$this->_dbHRReport->repTitleAndHeader('Salary & Payslip');
		// $Report['PF']		=	$this->_dbHRReport->repTitleAndHeader('Provident Fund');
		// $Report['Ins']		=	$this->_dbHRReport->repTitleAndHeader('Insurance');
		if($this->_orgCode=='demoindia' || $this->_orgCode=='demopioneer' || $this->_orgCode=='demophilippines' || $this->_orgCode=='philippines')
		{
			$Report['PF']		=	$this->_dbHRReport->repTitleAndHeader('Social Security Scheme');
			$Report['Ins']		=	$this->_dbHRReport->repTitleAndHeader('Home Development Mutual Fund');
		}
		else
		{
			$Report['PF']		=	$this->_dbHRReport->repTitleAndHeader('Provident Fund');
			$Report['Ins']		=	$this->_dbHRReport->repTitleAndHeader('Insurance');
		}
		$Report['Loan']		=	$this->_dbHRReport->repTitleAndHeader('Loan & Deductions');
		$Report['Earning']	=	$this->_dbHRReport->repTitleAndHeader('Earnings & Expense Claim');
		$Report['Tax']		=	$this->_dbHRReport->repTitleAndHeader('Tax And Electronic Fund Transfer');
		$countryCode		=	$this->_dbHRReport->getEmployeeCountryCode($this->_logEmpId);
		if(!empty($countryCode))
		{
			if($countryCode=='GH')
			{
				$Report['Tax'] = $this->_dbHRReport->repTitleAndHeader('PAYE And Electronic Fund Transfer');
			}
		}
		
		$array = array('Salary','PF','Ins','Loan','Earning','Tax');
		$this->view->Data = $Report;
		$this->view->key = $array;
    }
	
	//Landing page for all the recruitment reports
	 public function viewRecruitmentReportsAction()
    {
        $this->_helper->layout()->disableLayout()->setLayout('admin_layout');
		
        $this->_reportAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, 'Recruitment Reports');
		$this->view->reportUser =  $this->_reportAccessRights['Employee'];	

		$Report['Recruitment']	=	$this->_dbHRReport->repTitleAndHeader('Recruitment');
		$array = array('Recruitment');
		$this->view->Data = $Report;
		$this->view->key = $array;
    }

	public function getEftFilterDetailsAction(){
		$this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('get-eft-filter-details', 'json')->initContext();
			
			$bankPaymentTypeId = $this->_getParam('bankPaymentTypeId', null);
			$bankPaymentTypeId = filter_var($bankPaymentTypeId, FILTER_SANITIZE_STRING);

			$transactionTypeList = $this->_dbHRReport->getEftTransactionType($bankPaymentTypeId);
			$debitAccountNumbersList = $this->_dbHRReport->getEftActiveAccountNumbers(0,$bankPaymentTypeId);
			$this->view->result = array('transactionTypeList' => $transactionTypeList, 'debitAccountNumbersList' => $debitAccountNumbersList);
		}
	}

	public function updateReportStructureAction()
	{
		$this->_helper->layout()->disableLayout();
    	if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('update-report-structure', 'json')->initContext();
			$linkValue 			= $this->_getParam('linkValue', null);
			$linkValue 			= filter_var($linkValue, FILTER_SANITIZE_STRIPPED);
			$checkedColumns     = $this->_getParam('reportHeaders', null);
			$selectedColumnList = json_encode($checkedColumns);

			$result = $this->_dbHRReport->updateReportStructure($linkValue,$selectedColumnList);
			if(!empty($result))
			{
				$this->view->result = array('success' =>true, 'msg'=>"report structure added successfully.", 'type'=>'success');
			}
			else
			{
				$this->view->result = array('success' =>true, 'msg'=>"unable to insert the report structure.", 'type'=>'sucess');
			}
		}
	}

	public function listPayrollReconciliationAction()
	{
		$this->_helper->layout()->disableLayout();
    	if (isset($_SERVER['HTTP_REFERER']))
        {
			if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				$ajaxContext = $this->_helper->getHelper('AjaxContext');
				$ajaxContext->addActionContext('list-payroll-reconciliation', 'json')->initContext();
				$payslipMonth 			= filter_var($formData['payslipMonth'], FILTER_SANITIZE_NUMBER_INT);
				$payslipYear 			= filter_var($formData['payslipYear'], FILTER_SANITIZE_NUMBER_INT);
				if(!empty($payslipMonth) && !empty($payslipYear))
				{
					$salaryMonth = $payslipMonth . ',' . $payslipYear;
					$result = $this->_dbHRReport->getPayrollReconciliation($salaryMonth);
					if(!empty($result))
					{
						$this->view->result =$result;
					}
					else
					{
						$this->view->result = array('success' =>false, 'msg'=>"unable to retrivie payroll reconciliation.", 'type'=>'info');
					}
				}
				else
				{
					$this->view->result = array('success' =>true, 'msg'=>"payslip month and year should not be empty.", 'type'=>'info');
				}
			}
		}
	}


	public function listReportDetailsAction()
	{
		$this->_helper->layout()->disableLayout();
    	if (isset($_SERVER['HTTP_REFERER']))
        {
			if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				$ajaxContext = $this->_helper->getHelper('AjaxContext');
				$ajaxContext->addActionContext('list-report-details', 'json')->initContext();
				$formId = array_map('intval', $formData['formId']);
				if(!empty($formId))
				{
					$result = $this->_dbHRReport->getReportDetailsByFormId($formId);
					if(!empty($result))
					{
						$this->view->result = array('success' =>true, 'msg'=>"report details retrieved successfully",'reportDetails'=>$result,'type'=>'success');
					}
					else
					{
						$this->view->result = array('success' =>false, 'msg'=>"unable to retrieve report details", 'type'=>'info');
					}
				}
				else
				{
					$this->view->result = array('success' =>true, 'msg'=>"form id should not be empty", 'type'=>'info');
				}
			}
		}
	}

	public function getReportFilterDetailsAction()
	{
		$this->_helper->layout()->disableLayout();
    	if (isset($_SERVER['HTTP_REFERER']))
        {
			if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				$ajaxContext = $this->_helper->getHelper('AjaxContext');
				$ajaxContext->addActionContext('get-report-filter-details', 'json')->initContext();
				$reportId = array_map('intval', $formData['reportId']);
				if(!empty($reportId))
				{
					$result = $this->_dbHRReport->getReportFilterDetails($reportId);
					if(!empty($result))
					{
						$this->view->result = array('success' =>true, 'msg'=>"report filter details retrived successfully",'reportDetails'=>$result,'type'=>'success');
					}
					else
					{
						$this->view->result = array('success' =>false, 'msg'=>"unable to retrivie report filter details", 'type'=>'info');
					}
				}
				else
				{
					$this->view->result = array('success' =>true, 'msg'=>"report id should not be empty", 'type'=>'info');
				}
			}
		}
	}

	public function __destruct()
    {
        
    }
}