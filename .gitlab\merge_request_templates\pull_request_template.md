# Checklist:
- [ ] I provided a valid issue number for this request
- [ ] My code follows the style guidelines of this project
- [ ] I have performed a self-review of my code
- [ ] I have added comments to my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes  should not generate new warnings
- [ ] Dependent changes have been merged and published
- [ ] Remove Commented Code,Console.log,Print_r,Echo Statements,Unused Codes, Unnecessary Files, Hard Code Codes
- [ ] I have reused the functions and methods in all the places
- [ ] I have handled the session expiration for all the ajax/API requests
- [ ] I have set the lock flag in form edit and reset it during the login and the page refresh
- [ ] I added/updated all the id's related to testing
- [ ] I checked table alias name/table name is in the same case(Aurora DB is case sensitive)
- [ ] I run the developer test before sending the code to review
- [ ] I created the index for necessary fields in mysql table