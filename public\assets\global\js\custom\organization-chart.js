$(function () {
    "use strict";
    var orgCode = fngetOrgCode(),
        loggedInUserId = localStorage.getItem("LoginEmpId"),
        atsBaseUrl = getApiCustomDomains('ats'),
        formName = "Organization Chart",
        orgchartBaseUrl = null,
        reportImageDataUrl = null,
        reportLogoName = null,
        domain = null,
        pdfFooterText = null,
        mutation = null,
        serviceURLs = null,
        datascource = null,
        OrgChart = null,
        orgChartOptions = null,
        bottomEdgeClicked = false,
        triggerBottomEdge = true;

    //employee access rights for each action
    var accessRightsParams = {
        addAccessRights: 0,
        updateAccessRights: 0,
        deleteAccessRights: 0,
        viewAccessRights: 0
    };

    //show the mask on load data.
    setMask("#chart-container");

    //variables to get the employee access rights
    var variables = {
        formName: formName,
        employeeId: parseInt(loggedInUserId, 10)
    };

    //mutation to get the employee access rights
    var mutation = `mutation 
    (
        $formName: String,
        $employeeId : Int!,
        
    ) 
    { getAccessRights
        (
            formName:$formName,
            employeeId:$employeeId,
            
        ) { errorCode message rights {Role_View Role_Add Role_Update Role_Delete Role_Optional_Choice Role_Hr_Group Role_Payroll_Group Is_Manager}}}`;

    //get the access rights from the backend
    $.ajax({
        method: "POST",
        url: atsBaseUrl,
        headers: getGraphqlAPIHeaders(),
        data: JSON.stringify({
            query: mutation,
            variables: variables
        }),

        success: function (result) {
            //if data listed successfully
            if (result.data) {
                //Access rights
                var accessRights = result.data.getAccessRights.rights;

                //assign the access rights
                accessRightsParams["viewAccessRights"] = accessRights.Role_View;
                accessRightsParams["addAccessRights"] = accessRights.Role_Add;
                accessRightsParams["updateAccessRights"] = accessRights.Role_Update;
                accessRightsParams["deleteAccessRights"] = accessRights.Role_Delete;

                //check if view/list access rights exists and show the list form
                if (accessRightsParams["viewAccessRights"]) {
                    enableViewToUser();
                }
                //show access denied if there is no access
                else {
                    $("#org-chart-container").css("display", "none");
                    $("#access-denied").css("display", "block");
                }
            } else {
                removeMask();
                //error occured while listing the modules
                var error = JSON.parse(result.errors[0].message);
                var errorCode = error.errorCode;
                $(window).scrollTop(0);
                switch (errorCode) {
                    //technical errors
                    case 705:
                    case 706:
                        jAlert({
                            panel: $("#chart-container"),
                            msg: "There seems to be some technical issues. Please try after some time.",
                            type: "warning"
                        });
                        break;
                        //access denied
                    case 752:
                        $("#org-chart-container").css("display", "none");
                        $("#access-denied").css("display", "block");
                        break;
                        //functional errors
                    case 751:
                    default:
                        jAlert({
                            panel: $("#chart-container"),
                            msg: "Something went wrong. Please contact system administrator.",
                            type: "warning"
                        });
                        break;
                }
            }
        },
        //error while listing modules
        error: function (result) {
            removeMask();
            $(window).scrollTop(0);
            jAlert({
                panel: $("#chart-container"),
                msg: "Something went wrong. Please contact system administrator.",
                type: "warning"
            });
        }
    });
       

    function enableViewToUser() {
        // init values
        orgchartBaseUrl = getApiCustomDomains('orgChart');
        domain = localStorage.getItem("domain");
        pdfFooterText = `Created using ${domain.split(".")[0].toLocaleUpperCase()}`;

        mutation = `mutation 
    GetOrganizationChart($orgCode: String!, $loggedInUserId: String!, $managerId: String) { 
        getOrganizationChart(
            orgCode: $orgCode, 
            loggedInUserId:$loggedInUserId, 
            managerId: $managerId
        ) { 
            error { 
                code 
                message 
            } 
            result {
                orgDetail 
                {
                    orgReportPath 
                    orgName
                } 
                children {
                    id 
                    name 
                    title 
                    gender
                    isManager
                    filePath 
                    relationship 
                    className
                }
            }
        }
    }`;

        serviceURLs = {
            parent: function () {
                return {
                    url: orgchartBaseUrl,
                    data: JSON.stringify({
                        variables: {
                            orgCode: orgCode,
                            loggedInUserId: loggedInUserId
                        },
                        query: mutation
                    })
                };
            },
            children: function (nodeData) {
                let variables = {
                    orgCode: orgCode,
                    loggedInUserId: loggedInUserId
                };
                if (nodeData.id && nodeData.id != "top") {
                    variables["managerId"] = nodeData.id;
                }
                return {
                    url: orgchartBaseUrl,
                    data: JSON.stringify({
                        variables,
                        query: mutation
                    })
                };
            },
            siblings: function (nodeData) {
                return orgchartBaseUrl;
            },
            families: function (nodeData) {
                return orgchartBaseUrl;
            }
        };

        // default values of the orgchart
        datascource = {
            id: "top",
            name: "Organization",
            title: "-",
            className: "top-level",
            relationship: "001", // 001 - top most parent element
            collapsed: false,
            children: []
        };

        orgChartOptions = {
            id: "orgChart",
            data: datascource,
            ajaxURL: serviceURLs,
            nodeContent: "title",
            nodeId: "id",
            exportButton: false,
            pan: true,
            zoom: true,
            zoominLimit: 5,
            zoomoutLimit: 0.7,
            draggable: false,
            exportFilename: "OrgChart",
            exportFileextension: "pdf",
            parentNodeSymbol: "fa-users"
        };

        // check form has custom name
        checkCustomFormDetail();

        OrgChart = $("#chart-container").orgchart(orgChartOptions);

        OrgChart.init = function (opts) {
            var that = this;
            this.options = $.extend({}, this.defaultOptions, this.opts, opts);

            // build the org-chart
            var $chartContainer = this.$chartContainer;
            if (this.$chart) {
                this.$chart.remove();
            }
            var data = this.options.data;
            // var $chart = (this.$chart = $("<div>", {
            //     data: {
            //         options: this.options
            //     },
            //     id: this.options.id,
            //     class: "orgchart" +
            //         (this.options.chartClass !== "" ?
            //             " " + this.options.chartClass :
            //             "") +
            //         (this.options.direction !== "t2b" ?
            //             " " + this.options.direction :
            //             ""),
            //     click: function (event) {
            //         if (!$(event.target).closest(".node").length) {
            //             $chart.find(".node.focused").removeClass("focused");
            //         }
            //     }
            // }));

            // Create the $chart element without the 'click' event
            var $chart = $("<div>", {
                data: {
                    options: this.options
                },
                id: this.options.id,
                class: "orgchart" +
                    (this.options.chartClass !== "" ? " " + this.options.chartClass : "") +
                    (this.options.direction !== "t2b" ? " " + this.options.direction : "")
            });

            // Bind the 'click' event separately
            $chart.on('click', function(event) {
                if (!$(event.target).closest(".node").length) {
                    $chart.find(".node.focused").removeClass("focused");
                }
            });

            // Assign $chart to this.$chart
            this.$chart = $chart;


            if (typeof MutationObserver !== "undefined") {
                this.triggerInitEvent();
            }
            if (typeof data === "object") {
                if (data instanceof $) {
                    // ul datasource
                    this.buildHierarchy(
                        $chart,
                        this.buildJsonDS(data.children()),
                        0,
                        this.options
                    );
                } else {
                    // local json datasource
                    this.buildHierarchy(
                        $chart,
                        this.options.ajaxURL ? data : this.attachRel(data, "00")
                    );
                }
            } else {
                $chart.append('<i class="fa fa-circle-o-notch fa-spin spinner"></i>');

                $.ajax({
                        url: data,
                        headers: getGraphqlAPIHeaders(),
                        dataType: "json"
                    })
                    .done(function (data, textStatus, jqXHR) {
                        that.buildHierarchy(
                            $chart,
                            that.options.ajaxURL ? data : that.attachRel(data, "00"),
                            0,
                            that.options
                        );
                    })
                    .fail(function (jqXHR, textStatus, errorThrown) {
                        console.log(errorThrown);
                    })
                    .always(function () {
                        $chart.children(".spinner").remove();
                    });
            }
            $chartContainer.append($chart);

            var actionButtons = $("#action-button-container");
            var actionButtonsView = actionButtons.clone(true);
            actionButtonsView.id = "action-btn-container";
            $chartContainer.after(actionButtonsView);
            actionButtons.remove();
            if (this.options.pan) {
                this.bindPan();
            }

            if (this.options.zoom) {
                this.bindZoom();
            }

            // append the export button
            if (this.options.exportButton) {
                this.attachExportButton();
            } else {
                $("btn-export").hide();
            }

            return this;
        };
        // create nodes
        OrgChart.createNode = function (data) {
            var that = this;
            var opts = this.options;
            var level = data.level;
            if (data.children) {
                $.each(data.children, function (index, child) {
                    child.parentId = data.id;
                });
            }
            // construct the content of node
            var $nodeDiv = $(
                "<div" +
                (opts.draggable ? ' draggable="true"' : "") +
                (data[opts.nodeId] ? ' id="' + data[opts.nodeId] + '"' : "") +
                (data.parentId ? ' data-parent="' + data.parentId + '"' : "") +
                ">"
            ).addClass(
                "node " +
                (data.className || "") +
                (level > opts.visibleLevel ? " slide-up" : "")
            );
            if (opts.nodeTemplate) {
                $nodeDiv.append(opts.nodeTemplate(data));
            } else {
                var imageURL = '';
                // If the employee gender exist then get the avatar based on the gender
                if(data.gender){
                    if (data.isManager) {
                        imageURL+= data.id !== "top" ? pageUrl() + `images/${(data.gender && data.gender.toLowerCase()) == 'male'? 'male-manager.png': 'female-manager.png'}`: '';
                    } else {
                        imageURL+= data.id !== "top" ? pageUrl() + `images/${(data.gender && data.gender.toLowerCase()) == 'male'? 'male-emp.png': 'female-emp.png'}` : '';
                    }
                }
                let employeeDetailsInCard = '<div class="tooltipcard">';
                //If the avatar image URL exists
                if(imageURL){
                    employeeDetailsInCard +=
                    `<div style="flex: 1">
                        <img
                            id="node-profile-img"
                            class="img-circle tooltip-card-img" src="${imageURL}" 
                            filePath="${data.filePath}" 
                            imgLoaded=${false}
                            alt="Profile Image" 
                        />
                    </div>
                    `
                }
                employeeDetailsInCard += `
                <div style="flex: 2 ">
                    <h6 class="tooltip-card-title"> Name: ${data.name}</h6>
                    <p class="tooltip-card-content"> Designation: ${
                    data.title
                    }</p>
                    <p class="tooltip-card-content"> Employee-ID: ${
                    data.id
                    }</p>
                </div>`;
                employeeDetailsInCard +=`</div>`;//tooltipcard div close tag

                $nodeDiv.append(`<div class="title">${data.name.length > 12 ? data.name.substr(0, 12)+'...': data.name} ${
                data.id !== "top" ? employeeDetailsInCard  : "" } </div>`);

                $nodeDiv.append(
                    typeof opts.nodeContent !== "undefined" ?
                    '<div class="content">' +
                    (data[opts.nodeContent] || "") +
                    "</div>" :
                    ""
                );
            }
            var nodeData = $.extend({}, data);
            delete nodeData.children;
            $nodeDiv.data("nodeData", nodeData);
            // append 4 direction arrows or expand/collapse buttons
            var flags = data.relationship || "";
            if (opts.verticalLevel && level >= opts.verticalLevel) {
                if (level + 1 > opts.verticalLevel && Number(flags.substr(2, 1))) {
                    var icon = level + 1 > opts.visibleLevel ? "plus" : "minus";
                    $nodeDiv.append(
                        '<i class="toggleBtn fa fa-' + icon + '-square"></i>'
                    );
                }
            } else {
                if (Number(flags.substr(0, 1))) {
                    $nodeDiv.append('<i class="edge verticalEdge topEdge fa"></i>');
                }
                if (Number(flags.substr(1, 1))) {
                    $nodeDiv.append(
                        '<i class="edge horizontalEdge rightEdge fa"></i>' +
                        '<i class="edge horizontalEdge leftEdge fa"></i>'
                    );
                }
                if (Number(flags.substr(2, 1))) {
                    $nodeDiv.append('<i class="edge verticalEdge bottomEdge fa"></i>');
                }
                var icon = "fa-user";
                if (data.className === "top-level") {
                    icon = "fa-building";
                } else if (data.className === "manager-level") {
                    icon = "fa-users";
                }

                $nodeDiv
                    .children(".title")
                    .prepend(
                        '<i class="fa ' +
                        icon +
                        ' symbol" style="padding-left: 4px; padding-top: 1px;"></i>'
                    );
            }

            // $nodeDiv.on(
            //     "mouseenter mouseleave",
            //     this.nodeEnterLeaveHandler.on(this)
            // );
            if (this.nodeEnterLeaveHandler && typeof this.nodeEnterLeaveHandler.on === 'function') {
                $nodeDiv.on("mouseenter", this.nodeEnterLeaveHandler.on.bind(this.nodeEnterLeaveHandler));
            } else {
                // Handle the scenario when nodeEnterLeaveHandler or its on method is undefined
                // or not a function
            }

            if (this.nodeEnterLeaveHandler && typeof this.nodeEnterLeaveHandler.on === 'function') {
                $nodeDiv.on("mouseleave", this.nodeEnterLeaveHandler.on.bind(this.nodeEnterLeaveHandler));
            } else {
                // Handle the scenario when nodeEnterLeaveHandler or its on method is undefined
                // or not a function
            }
            
            
            // $nodeDiv.on("mouseenter", this.nodeEnterLeaveHandler.on.bind(this.nodeEnterLeaveHandler));
            // $nodeDiv.on("mouseleave", this.nodeEnterLeaveHandler.on.bind(this.nodeEnterLeaveHandler));

            // $nodeDiv.on("click", this.nodeClickHandler.on(this));
            // $nodeDiv.on("click", ".topEdge", this.topEdgeClickHandler.on(this));
            // $nodeDiv.on(
            //     "click",
            //     ".bottomEdge",
            //     this.bottomEdgeClickHandler.on(this)
            // );
            // $nodeDiv.on(
            //     "click",
            //     ".leftEdge, .rightEdge",
            //     this.hEdgeClickHandler.on(this)
            // );
            // $nodeDiv.on("click", ".toggleBtn", this.toggleVNodes.on(this));

            $nodeDiv.on("click", this.nodeClickHandler.bind(this));
            $nodeDiv.on("click", ".topEdge", event => this.topEdgeClickHandler(event));
            $nodeDiv.on("click", ".bottomEdge, .content, .title", event => this.bottomEdgeClickHandler(event));
            $nodeDiv.on("click", ".leftEdge, .rightEdge", event => this.hEdgeClickHandler(event));
            $nodeDiv.on("click", ".toggleBtn", event => this.toggleVNodes(event));

            
            if (opts.draggable) {
                this.bindDragDrop($nodeDiv);
                this.touchHandled = false;
                this.touchMoved = false;
                this.touchTargetNode = null;
            }
            // allow user to append dom modification after finishing node create of orgchart
            if (opts.createNode) {
                opts.createNode($nodeDiv, data);
            }

            return $nodeDiv;
        };

        // to show the export options
        OrgChart.attachExportButton = function () {
            var that = this;
            $("#btn-export").show();
            $("#btn-export").on("click", function () {
                that.export();
            });
        };
        // to export the canvas data to pdf format
        OrgChart.exportPDF = function (canvas, exportFilename) {
            var doc = {};
            var docWidth = Math.floor(canvas.width * 0.2646);
            var docHeight = Math.floor(canvas.height * 0.2646);
            docWidth += 30;
            if (docWidth > docHeight) {
                doc = new jsPDF("l", "mm", [docWidth, docHeight]);
            } else {
                doc = new jsPDF("p", "mm", [docHeight, docWidth]);
            }
            doc.addImage(canvas.toDataURL(), "png", 12, 0);
            doc.setFontSize(8);
            doc.setTextColor(40);
            doc.setFontStyle("normal");
            if (pdfFooterText) {
                doc.text(pdfFooterText, docWidth / 2 - 15, docHeight - 2);
            }
            if (reportLogoName && reportImageDataUrl) {
                var reportImage = new Image();
                reportImage.onload = function () {
                    // prepare width & height of the the original report logo image
                    var width = (reportImage.width / 100) * 1.8; 
                    var height = (reportImage.height / 100) * 1.8; 
                    doc.addImage(
                        reportImageDataUrl,
                        reportLogoName.split(".")[1],
                        2,
                        2,
                        width,
                        height
                    );
                    doc.save(exportFilename + ".pdf");
                };
                reportImage.src = reportImageDataUrl;
            } else {
                doc.save(exportFilename + ".pdf");
            }
        };
        // to export the canvas data to png format
        OrgChart.exportPNG = function (canvas, exportFilename) {
            var isWebkit = "WebkitAppearance" in document.documentElement.style;
            var isFf = !!window.sidebar;
            var isEdge =
                navigator.appName === "Microsoft Internet Explorer" ||
                (navigator.appName === "Netscape" &&
                    navigator.appVersion.indexOf("Edge") > -1);

            if ((!isWebkit && !isFf) || isEdge) {
                window.navigator.msSaveBlob(canvas.msToBlob(), exportFilename + ".png");
            } else {
                var selector =
                    ".oc-download-btn" +
                    (that.options.chartClass !== "" ? "." + that.options.chartClass : "");
                if (!$chartContainer.find(selector).length) {
                    $chartContainer.append(
                        '<a class="oc-download-btn' +
                        (that.options.chartClass !== "" ?
                            " " + that.options.chartClass :
                            "") +
                        '"' +
                        ' download="' +
                        exportFilename +
                        '.png"></a>'
                    );
                }
                $chartContainer
                    .find(selector)
                    .attr("href", canvas.toDataURL())[0]
                    .click();
            }
        };
        // to export the html content
        OrgChart.export = function (exportFilename, exportFileextension) {
            var that = this;
            exportFilename =
                typeof exportFilename !== "undefined" ?
                exportFilename :
                this.options.exportFilename;
            exportFileextension =
                typeof exportFileextension !== "undefined" ?
                exportFileextension :
                this.options.exportFileextension;
            if ($(this).children(".spinner").length) {
                return false;
            }
            var $chartContainer = this.$chartContainer;
            // show mask
            setMask("#wholepage");

            var transform = OrgChart.$chart.css("transform");
            OrgChart.$chart.css("transform", "");
            var sourceChart = $chartContainer
                .addClass("canvasContainer")
                .find('.orgchart:not(".hidden")')
                .get(0);
            var flag =
                that.options.direction === "l2r" || that.options.direction === "r2l";

            html2canvas(sourceChart, {
                scale: 1,
                width: flag ? sourceChart.clientHeight : sourceChart.clientWidth,
                height: flag ? sourceChart.clientWidth : sourceChart.clientHeight,
                onclone: function (cloneDoc) {
                    $(cloneDoc)
                        .find(".canvasContainer")
                        .css("overflow", "visible")
                        .find('.orgchart:not(".hidden"):first')
                        .css("transform", "");
                    OrgChart.$chart.css("transform", transform);
                }
            }).then(
                function (canvas) {
                    if (exportFileextension.toLowerCase() === "pdf") {
                        that.exportPDF(canvas, exportFilename);
                    } else {
                        that.exportPNG(canvas, exportFilename);
                    }
                    $chartContainer.removeClass("canvasContainer");
                    removeMask();
                },
                function () {
                    // error part
                    removeMask();
                    $chartContainer.removeClass("canvasContainer");
                }
            );
        };
        // on click bottom edge to show children datas
        OrgChart.bottomEdgeClickHandler = function (event) {
            event.stopPropagation();

            var that = this;
            var $bottomEdge = $(event.target);
            var $node = $(event.delegateTarget);
            // close other siblings children views
            if (triggerBottomEdge && !bottomEdgeClicked) {
                bottomEdgeClicked = true;
                $node
                    .closest(".nodes")
                    .children()
                    .each(function (index, td) {
                        var $sibling = $(td).find(".manager-level");
                        if ($sibling.length > 0) {
                            var siblingChildrenState = that.getNodeState(
                                $($sibling.get(0)),
                                "children"
                            );
                            if (siblingChildrenState.visible) {
                                var $siblingBottomEdge = $sibling.find(".bottomEdge");
                                if ($siblingBottomEdge) {
                                    $siblingBottomEdge.trigger("click");
                                }
                            }
                        }
                    });
                bottomEdgeClicked = false;
            }

            var childrenState = this.getNodeState($node, "children");
            if (childrenState.exist) {
                var $children = $node.closest("tr").siblings(":last");
                if ($children.find(".sliding").length) {
                    return;
                }
                // hide the descendant nodes of the specified node
                if (childrenState.visible) {
                    $node.children('i.edge.verticalEdge.bottomEdge').addClass('childrensHidden')
                    $node.children('i.edge.verticalEdge.bottomEdge').removeClass('childrensVisible');
                    this.hideChildren($node);
                } else {
                    // show the descendants
                    $node.children('i.edge.verticalEdge.bottomEdge.childrensHidden').removeClass('childrensHidden');
                    $node.children('i.edge.verticalEdge.bottomEdge').addClass('childrensVisible');
                    this.showChildren($node);
                }
            } else {
                // load the new children nodes of the specified node by ajax request
                if (this.startLoading($bottomEdge)) {
                    $node.children('i.edge.verticalEdge.bottomEdge.childrensHidden').removeClass('childrensHidden');
                    $node.children('i.edge.verticalEdge.bottomEdge').addClass('childrensVisible');
                    var opts = this.options;
                    var request = opts.ajaxURL.children($node.data("nodeData"));
                    this.loadChildrenNodeDatas("children", request, $bottomEdge);
                }
            }
        };
        // to load children datas
        OrgChart.loadChildrenNodeDatas = function (rel, request, $edge) {
            var that = this;
            var opts = this.options;

            $.ajax({
                url: request.url,
                method: "POST",
                headers: getGraphqlAPIHeaders(),
                data: request.data,
                success: function (result) {
                    if (!result.data.getOrganizationChart.error) {
                        var resultData = result.data.getOrganizationChart.result;
                        if (that.$chart.data("inAjax")) {
                            if (resultData.children.length) {
                                that.addChildren($edge.parent(), resultData[rel]);
                            }
                        }
                    } else {
                        // show alert message
                        jAlert({
                            panel: $("#chart-container"),
                            msg: "Something went wrong. Please contact system administrator.",
                            type: "warning"
                        });
                    }
                },
                error: function (error) {
                    // show alert message
                    jAlert({
                        panel: $("#chart-container"),
                        msg: "Something went wrong. Please contact system administrator.",
                        type: "warning"
                    });
                }
            }).always(function () {
                that.endLoading($edge);
            });
        };

        // when user select the node this event will be trigger
        OrgChart.nodeClickHandler = function (event) {
            this.$chart.find(".focused").removeClass("focused");
            $(event.delegateTarget).addClass("focused");
            $("#btn-report-path").attr("disabled", false);
        };

        $.ajax({
            url: serviceURLs.parent().url,
            method: "POST",
            headers: getGraphqlAPIHeaders(),
            data: serviceURLs.parent().data,
            success: function (response) {
                if (!response.data.getOrganizationChart.error) {
                    var data = response.data.getOrganizationChart.result;
                    if (data.orgDetail) {
                        if (data.orgDetail.orgName) {
                            // set organization name for top level node
                            datascource.title = data.orgDetail.orgName;
                        }
                        if (data.orgDetail.orgReportPath) {
                            reportLogoName = data.orgDetail.orgReportPath;
                            // get report logo url from s3 bucket
                            getImageUrl();
                        }
                    }
                    if (data.children && data.children.length > 0) {
                        datascource.children = data.children;
                    } else {
                        datascource.relationship = "000"; // 000 - Empty relationship
                    }
                    // enable export option
                    orgChartOptions.exportButton = true;
                    // re-initialize datas
                    orgChartOptions.data = datascource;
                    // apply the changes to org chart instance
                    initializeChartEvents();
                } else {
                    handleError(response.data.getOrganizationChart.error);
                }
                // remove the spinner
                removeMask();
            },
            error: function (err) {
                handleError(err);
                // remove the spinner
                removeMask();
            }
        });
    }

    function handleError(error) {
        // show alert message
        jAlert({
            panel: $("#chart-container"),
            msg: `Some technical issue occurred. Please contact your administrator`,
            type: "warning"
        });
        // hide the organization chart
        $(".orgchart").css("display", "none");
        // disable the action buttons
        $("#btn-reset-path").attr("disabled", true);
        $("#btn-report-path").attr("disabled", true);
        $("#btn-fith").attr("disabled", true);
        $("#btn-fitv").attr("disabled", true);
        $("#btn-export").attr("disabled", true);
        $("#btn-export").css("background-color", "grey");
    }

    function initializeChartEvents() {
        OrgChart.init(orgChartOptions);
        $(".orgchart").css("display:block");
        // initially disable action buttons
        $("#btn-reset-path").attr("disabled", true);
        $("#btn-report-path").attr("disabled", true);

        // when the action `Fit to horizontally` clicked this event will be call
        $("#btn-fith").on("click", function () {
            var $container = OrgChart.$chartContainer;
            var $chart = OrgChart.$chart;
            if ($chart.width() > $container.width()) {
                OrgChart.$chart.css("transform", "none");
                var scale = $container.width() / $chart.outerWidth(true);
                var x =
                    (($container.width() - $chart.outerWidth(true)) / 2) * (1 / scale);
                var y =
                    (($container.height() - $chart.outerHeight(true)) / 2) * (1 + scale);
                OrgChart.setChartScale($chart, scale);
                var val = $chart.css("transform");
                $chart.css("transform", val + " translate(" + x + "px," + y + "px)");

                $("#btn-fith").attr("disabled", true);
            } else {
                return false;
            }
        });

        // when the action `Fit to vertically` clicked this event will be call
        $("#btn-fitv").on("click", function () {
            var $container = OrgChart.$chartContainer;
            var $chart = OrgChart.$chart;
            if ($chart.height() > $container.height()) {
                var scale = $container.height() / $chart.outerHeight(true);
                var x =
                    (($container.width() - $chart.outerWidth(true)) / 2) * (1 + scale);
                var y =
                    (($container.height() - $chart.outerHeight(true)) / 2) * (1 / scale);
                OrgChart.setChartScale($chart, scale);
                var val = $chart.css("transform");
                $chart.css("transform", val + " translate(" + x + "px," + y + "px)");
              
                $("#btn-fitv").attr("disabled", true);
            } else {
                return false;
            }
        });

        // when the action `Reset the chart` clicked this event will be call
        $("#btn-reset-chart").on("click", function () {
            if (OrgChart.$chart.css("transform") === "none") {
                // hide all children for the top-level node
                var $topLevelNode = $(".orgchart").find(".top-level");
                if ($topLevelNode.length > 0) {
                    var childrenState = OrgChart.getNodeState(
                        $($topLevelNode.get(0)),
                        "children"
                    );
                    if (childrenState.visible) {
                        var $bottomEdge = $topLevelNode.find(".bottomEdge");
                        if ($bottomEdge) {
                            $bottomEdge.trigger("click");
                        }
                    }
                    // for show top-level view
                    $topLevelNode.removeClass('slide-down')
                    $topLevelNode.parents('tr').removeClass('hidden')
                }
            }
            OrgChart.$chart.css("transform", "none");
            $("#btn-fith").attr("disabled", false);
            $("#btn-fitv").attr("disabled", false);
        });

        // when user click inside the chart container
        OrgChart.$chartContainer.on("click", function () {
            var $selected = $("#chart-container").find(".node.focused");
            if (!$selected.length) {
                $("#btn-report-path").attr("disabled", true);
            }
        });
        // when the user want to see the `Report-path` (top-bottom hierarcy of the selected node) this event will be call
        $("#btn-report-path").on("click", function () {
            var $selected = $("#chart-container").find(".node.focused");
            if ($selected.length) {
                OrgChart.$chart.css("transform", "none");
                $selected.children('i.bottomEdge.childrensVisible').trigger('click')
                $selected
                    .parents(".nodes")
                    .children(":has(.focused)")
                    .find(".node:first")
                    .each(function (index, superior) {
                        if (
                            !$(superior)
                            .find(".horizontalEdge:first")
                            .closest("table")
                            .parent()
                            .siblings()
                            .is(".hidden")
                        ) {
                            $(superior)
                                .find(".horizontalEdge:first")
                                .trigger("click");
                        }
                    });
                $(this).prop("disabled", true);
                $("#btn-reset-path").prop("disabled", false);
            } else {
                alert("please select one employee");
            }
        });

        // to reset the report path data
        $("#btn-reset-path").on("click", function () {
            $("#chart-container")
                .find(".hidden")
                .removeClass("hidden")
                .end()
                .find(".slide-up, .slide-right, .slide-left, .focused")
                .removeClass("slide-up slide-right slide-left focused");
            triggerBottomEdge = false;
            $("#chart-container")
                .find('i.edge.verticalEdge.bottomEdge.childrensHidden')
                .trigger('click')
            triggerBottomEdge = true;
            $("#btn-report-path").prop("disabled", false);
            $("#btn-reset-path").prop("disabled", true);
        });

        // add mouse over event
        $(document).on('mouseover', '.title', function () {
            var imgElement = $(this)
                .find("#node-profile-img")
                .get(0);
            if (imgElement) {
                var filePath = imgElement.getAttribute("filePath");
                var imgLoaded = imgElement.getAttribute("imgLoaded");
                if (filePath && filePath !== "null" && !JSON.parse(imgLoaded)) {
                    imgElement.setAttribute(
                        "src",
                        fngetSignedUrl(
                            "hrapp_upload/" + orgCode + "_tmp/images/" + filePath,
                            "imageBucket"
                        )
                    );
                    imgElement.setAttribute("imgLoaded", true);
                }
            }
        });
    }

    function getImageUrl() {
        // prepare report logo path
        const pathUrl = fngetSignedUrl(
            `hrapp_upload/${orgCode}_tmp/logos/${reportLogoName}`,
            "logoBucket"
        );

        if (pathUrl && pathUrl.length > 0) {
            // convert report logo image url to data url
            toDataUrl(pathUrl, function (base64Data) {
                reportImageDataUrl = base64Data;
            });
        } else {
            reportLogoName = null;
        }
    }

    function toDataUrl(url, callback) {
        var xhr = new XMLHttpRequest();
        xhr.onload = function () {
            var reader = new FileReader();
            reader.onloadend = function () {
                callback(reader.result);
            };
            reader.readAsDataURL(xhr.response);
        };
        xhr.open("GET", url);
        xhr.responseType = "blob";
        xhr.send();
    }

    function checkCustomFormDetail() {
        getCustomFormDetail(orgchartBaseUrl, formName, function(form) {
            if (form && form.isEnabled) {
                $('#org-chart-title').text(form.customFormName)
            }
        })
    }

});