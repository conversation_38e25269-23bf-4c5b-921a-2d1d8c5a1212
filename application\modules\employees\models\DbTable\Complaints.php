<?php
//=========================================================================================
//=========================================================================================
/* Program : Complaints.php											   			         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MQL Query to retrive, add, update and approve complaints. 	             *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        16-Sep-2013    Narmadha	              Initial Version        	         *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Nivethitha              Changes in file for mobile app     *
 *                                                    1.Extra fields are added in        *
 *                                                    field list of list query.          *
 *                                                                                       *
 *  1.5        02-Feb-2016    Prasanth             Changed in file for Bootstrap         *
 *                                                                                       */
//=========================================================================================
//=========================================================================================
class Employees_Model_DbTable_Complaints extends Zend_Db_Table_Abstract
{

    protected $_dbComment = null;

    protected $_orgDF = null;

    protected $_dbPersonal = null;

    protected $_db = null;

    protected $_ehrTables = null;
    
     protected $_commonFunction = null;

    public function init()
    {
        $this->_ehrTables = new Application_Model_DbTable_Ehr();
        $this->_db = Zend_Registry::get('subHrapp');
        $this->_dbPersonal = new Employees_Model_DbTable_Personal();
        $this->_orgDF = $this->_ehrTables->orgDateformat();
        $this->_dbComment = new Payroll_Model_DbTable_PayrollComment();
        $this->_commonFunction = new Application_Model_DbTable_CommonFunction();
    }
    
    /* Search Complaints */
      public function searchComplaints($page,$rows,$sortField,$sortOrder,$searchAll=null,$searchDetails,$userDetails)
    {
        switch ($sortField)
        {			
			case 1: $sortField = 'EP2.Emp_First_Name'; break;
			case 2: $sortField = 'EP4.Emp_First_Name'; break;
			case 3: $sortField = 'C.Title'; break;                
			case 4: $sortField = 'C.Complaint_Date'; break;
			case 5: $sortField = 'C.Approval_Status'; break;
			default:
				$sortField = 'C.Added_On'; $sortOrder = 'desc'; break;
        }
        if (!empty($userDetails['Form_Name']))
        {
        	$formId = $this->_dbComment->getFormId($userDetails['Form_Name']);
        	 
        	$qryComment = $this->_db->select()->distinct()->from(array('Cm'=>$this->_ehrTables->comment), 'Parent_Id')
												->where('Cm.Parent_Id = C.Complaint_Id AND Cm.Form_Id='.$formId);
        }
        $qryComplaint = $this->_db->select()->from(array('C'=>$this->_ehrTables->complaint),
                                                    array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS C.Complaint_Id as count'),
                                                                  'C.Complaint_Id','C.ComplaintFrom_Id','C.ComplaintAgainst_Id','C.Approver_Id','C.Title','C.Complaint_Date',
								  new Zend_Db_Expr("DATE_FORMAT(C.Complaint_Date,'".$this->_orgDF['sql']."') as ComplaintDate"),
                                                                  new Zend_Db_Expr("DATE_FORMAT(C.Added_On,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
								  new Zend_Db_Expr("DATE_FORMAT(C.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On"),
                                                                  'C.Approval_Status', 'C.Added_By',
                                                                  'Comment'=> new Zend_Db_Expr('('.$qryComment.')'),
                                                                  'Session_Id'=>new Zend_Db_Expr($userDetails['Session_Id']),
                                                                  'DT_RowClass' => new Zend_Db_Expr('"Complaint"'),
                                                                  'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', C.Complaint_Id)")))
                                                  
                                                    ->joinInner(array('EP'=>$this->_ehrTables->empPersonal),'C.Added_By=EP.Employee_Id',
                                                                            array(new Zend_Db_Expr("CONCAT(EP.Emp_First_Name, ' ', EP.Emp_Last_Name) as Added_By"),'C.Added_By as Added_By_Id'))
                                                    
                                                    ->joinLeft(array('EP1'=>$this->_ehrTables->empPersonal),'C.Updated_By=EP1.Employee_Id',
                                                                            array(new Zend_Db_Expr("CONCAT(EP1.Emp_First_Name, ' ', EP1.Emp_Last_Name) as Updated_By")))
                                                    
						    ->joinLeft(array('EP2'=>$this->_ehrTables->empPersonal),'C.ComplaintFrom_Id=EP2.Employee_Id',
									    array(new Zend_Db_Expr("CONCAT(EP2.Emp_First_Name, ' ', EP2.Emp_Last_Name) as Complaint_From")))
                                                    
                                                    ->joinLeft(array('EP4'=>$this->_ehrTables->empPersonal),'C.ComplaintAgainst_Id=EP4.Employee_Id',
									    array(new Zend_Db_Expr("CONCAT(EP4.Emp_First_Name, ' ', EP4.Emp_Last_Name) as Complaint_Against")))
                                                    
						     ->joinLeft(array('EP3'=>$this->_ehrTables->empPersonal),'C.Approver_Id=EP3.Employee_Id',
                                        array(new Zend_Db_Expr("CONCAT(EP3.Emp_First_Name, ' ', EP3.Emp_Last_Name) as Approver_Name")))
                                        
                              ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'C.ComplaintFrom_Id=EJ.Employee_Id',
                                            array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN EP.Employee_Id ELSE EJ.User_Defined_EmpId END')))                        
                                                    ->order("$sortField $sortOrder")
                                                    ->limit($rows, $page);

						    
						    
        
        if (!empty($searchAll) && $searchAll != null)
		{
			$conditions  = $this->_db->quoteInto(new Zend_Db_Expr('Concat(EP2.Emp_First_Name," ",EP2.Emp_Last_Name) Like ?'),"%$searchAll%");
			$conditions .= $this->_db->quoteInto(new Zend_Db_Expr('or Concat(EP4.Emp_First_Name," ",EP4.Emp_Last_Name) Like ?'),"%$searchAll%");
			$conditions .= $this->_db->quoteInto('or C.Complaint_Date Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or C.Title Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or C.Approval_Status Like ?', "%$searchAll%");
			
			$qryComplaint->where($conditions);
		}

            if($userDetails['Is_Manager'] == 1 || ! empty($userDetails['Admin']))
            {
                     $qryComplaint->where('C.ComplaintFrom_Id = :EmpId  or C.Approver_Id = :EmpId or C.Added_By =:EmpId')
                     ->bind(array('EmpId'=>$userDetails['Session_Id']));
            }
            else
            {
                 $qryComplaint->where('C.ComplaintFrom_Id = ?', $userDetails['Session_Id']);
            }
            
               $complaintFromName = $searchDetails['Complaint_From_Name'];
               $complaintAgainstName = $searchDetails['Complaint_Against_Name'];
               $title                   =$searchDetails['Title'];
            
	
	if ($complaintFromName != '' && $complaintFromName != null && preg_match('/^[a-zA-Z]/', $complaintFromName))
	{
            $qryComplaint->where($this->_db->quoteInto(new Zend_Db_Expr('Concat(EP2.Emp_First_Name," ",EP2.Emp_Last_Name) Like ?'),"%$complaintFromName%"));
	}
        
    if ($complaintAgainstName != '' && $complaintAgainstName != null && preg_match('/^[a-zA-Z]/', $complaintAgainstName))
	{
            $qryComplaint->where($this->_db->quoteInto(new Zend_Db_Expr('Concat(EP4.Emp_First_Name," ",EP4.Emp_Last_Name) Like ?'),"%$complaintAgainstName%"));
	}       
               
                
        if (!empty($title))
		{
			$qryComplaint->where($this->_db->quoteInto('C.Title Like ?',"%$title%"));
		}
                

		if ($searchDetails['Complaint_Date_Begin'] != '' && $searchDetails['Complaint_Date_End'] != '')
		{
			$qryComplaint->where($this->_db->quoteInto('C.Complaint_Date >= ?', $searchDetails['Complaint_Date_Begin']));
			$qryComplaint->where($this->_db->quoteInto('C.Complaint_Date <= ?', $searchDetails['Complaint_Date_End']));
		}
                
				
		if (!empty($searchDetails['Approval_Status']))
		{
			$qryComplaint->where($this->_db->quoteInto('C.Approval_Status = ?',$searchDetails['Approval_Status']));
        }
        
        $qryComplaint = $this->_commonFunction->getDivisionDetails($qryComplaint,'EJ.Department_Id');
		
		$complaintDetails = $this->_db->fetchAll($qryComplaint);
                
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
                
		$qryiTotal = $this->_db->select()->from($this->_ehrTables->complaint, new Zend_Db_Expr('COUNT(Complaint_Id)'));
        
		if($userDetails['Is_Manager'] == 1 || ! empty($userDetails['Admin']))
		{
				 $qryiTotal->where('ComplaintFrom_Id = :EmpId  or Approver_Id = :EmpId or Added_By =:EmpId')
				 ->bind(array('EmpId'=>$userDetails['Session_Id']));
		}
		else
		{
			 $qryiTotal->where('ComplaintFrom_Id = ?', $userDetails['Session_Id']);
        }
		
		$iTotal = $this->_db->fetchOne($qryiTotal);
		        
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $complaintDetails);
    }
    
    /* Update Complaints */
    public function updateComplaints($complaintDetails,$sessionId,$comments,$customFormName)
    {
    $qryComplaint = $this->_db->select()->from($this->_ehrTables->complaint, new Zend_Db_Expr('count(Complaint_Id)'))
                                        ->where("Title Like ?",$complaintDetails['Title'])
                                        ->where('ComplaintAgainst_Id = ?', $complaintDetails['ComplaintAgainst_Id'])
                                        ->where('Complaint_Date = ?', $complaintDetails['Complaint_Date'])
                                        ->where('ComplaintFrom_Id = ?', $complaintDetails['ComplaintFrom_Id']);


         if (!empty($complaintDetails['Complaint_Id']))
                $qryComplaint->where('Complaint_Id != ?', $complaintDetails['Complaint_Id']);

        $complaintExists = $this->_db->fetchOne($qryComplaint);
        if(empty($complaintExists))
        {
                if(!empty($complaintDetails['Complaint_Id']))
                {
                        $action = 'Edit';
                        $complaintDetails['Updated_On'] = date('Y-m-d H:i:s');
                        $complaintDetails['Updated_By'] = $sessionId;
                        $complaintDetails['Lock_Flag']  = 0;
                        $updated = $this->_db->update($this->_ehrTables->complaint, $complaintDetails, array('Complaint_Id = '.$complaintDetails['Complaint_Id']));
                        $complaintId = $complaintDetails['Complaint_Id'];
                        
                }
                else
                {
                        $action = 'Add';
                        $complaintDetails['Added_On'] = date('Y-m-d H:i:s');
                        $complaintDetails['Added_By'] = $sessionId;
                        $updated =  $this->_db->insert($this->_ehrTables->complaint, $complaintDetails);
			$complaintId = $this->_db->lastInsertId();
                }
		if(!empty($comments))
		{
		    $formId = $this->_dbComment->getFormId('Complaints');
		    $addComment = array('Form_Id'=>$formId,
					'Emp_Comment'=>$comments,
					'Approval_Status'=>$complaintDetails['Approval_Status'],
					'Parent_Id'=>$complaintId,
					'Employee_Id'=>$sessionId,
					'Added_On'=>date('Y-m-d H:i:s'));
		    $this->_db->insert($this->_ehrTables->comment, $addComment);
		}    
                
              return $this->_commonFunction->updateResult (array('updated'    => $updated,
								'action'         => $action,
								'trackingColumn' => $complaintDetails['Complaint_Id'],
								'formName'       => $customFormName,
								'sessionId'      => $sessionId,
								'tableName'      => $this->_ehrTables->complaint));
        }
        else
        {
            return array('success' => false, 'msg'=>$customFormName.' Already Exist', 'type'=>'info');
        } 
    }
    
    
    public function statusReport($commentArray, $sessionId, $formName, $customFormName)
    {
        $transferStatus = array('Approval_Status'=>$commentArray['Approval_Status']);
        $updateStatus = $this->_db->update($this->_ehrTables->complaint, $transferStatus, 'Complaint_Id = ' . $commentArray['Parent_Id']);
        $formId = $this->_dbComment->getFormId($formName);
		
        if(!empty($commentArray['Emp_Comment']))
        {
            $commentArray['Form_Id']     = $formId;
            $commentArray['Employee_Id'] = $sessionId;
            $commentArray['Added_On']    = date('Y-m-d H:i:s');
            $insertStatus = $this->_db->insert($this->_ehrTables->comment,$commentArray);
        }
            
        if($updateStatus)
        {
            $this->_ehrTables->trackEmpSystemAction('Update '.$customFormName.' Status - '.$commentArray['Parent_Id'], $sessionId);
            return true;
        }
        else
        {
            return false;
        }
    }
    
    
    public function complaintEmployee($complaintId)
    {
        $qryEmpName=$this->_db->select()
                            ->from(array('com'=>$this->_ehrTables->complaint),array('com.ComplaintFrom_Id','com.ComplaintAgainst_Id'))
                            ->joinInner(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=com.ComplaintFrom_Id',
                                        array(new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as Complaint_From_Name")))
                            ->joinInner(array('emp1'=>$this->_ehrTables->empPersonal),'emp1.Employee_Id=com.ComplaintAgainst_Id',
                                        array(new Zend_Db_Expr("CONCAT(emp1.Emp_First_Name, ' ', emp1.Emp_Last_Name) as Complaint_Against_Name")))
                            ->joinInner(array('emp2'=>$this->_ehrTables->empPersonal),'emp2.Employee_Id=com.Added_By',
                                        array(new Zend_Db_Expr("CONCAT(emp2.Emp_First_Name, ' ', emp2.Emp_Last_Name) as AddedBy_Name")))
                            ->where('com.Complaint_Id =?', $complaintId);
        $rowEmpName = $this->_db->fetchRow($qryEmpName);
    
        return $rowEmpName;
    }

    public function __destruct()
    {
        
    }

}

