
**Database Schema Changes:**

- [ ] **Identify Relevant Tables:**
   - [ ] Review PHP model files (e.g., Employee.php) to identify tables.
   
- [ ] **Validate Existing Schema:**
   - [ ] Assess if current schema meets new design or requirements.
   - [ ] Determine if schema changes are necessary.
   
- [ ] **Ensure Indexes and Fields (Existing Schema):**
   - [ ] Check indexes on join and WHERE clause fields.
   - [ ] Create indexes where needed.
   - [ ] Check for "added on," "added by," "updated on," and "updated by" fields.
   - [ ] Remove "lock flag" field if unnecessary.
   
   - [ ] **Identify Possibilities for Schema Changes:**
   - [ ] Evaluate possibilities for schema changes:
     - [ ] Creating a new table with join keys.
     - [ ] Altering existing tables fields(we need to assure there should not be any data loss).

 Changing table structure:
- [ ] **Backup Data:**
   - [ ] Create backup of existing data.
   
- [ ] **Migration Planning:**
   - [ ] Develop migration plan.how to migrate the old data to new data
   
- [ ] **Execute Schema Changes:**
   - [ ] Implement schema changes.
   
- [ ] **Data Migration:**
   - [ ] Write migration scripts.
   - [ ] Ensure data integrity.
   
- [ ] **Testing:**
   - [ ] Test modified schema and data migration.
   
- [ ] **Documentation and Training:**
   - [ ] Update documentation.
   - [ ] Provide training to users or developers.