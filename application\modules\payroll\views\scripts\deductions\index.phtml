<?php
  	$deductionUser            = $this->deductionUser;
	$employeeName             = $this->employeeName;
	$employeeDepartment       = $this->depts;
    $empDepartment = array();
	
	foreach ($employeeName as $key => $row) {
		if (!in_array($row['Department_Name'], $empDepartment)) {
			array_push($empDepartment, $row['Department_Name']);
		}
	}
	
	$dateformat = $this->dateformat;
	
	if(!empty($dateformat))
	{
		$dformat = $dateformat['bs'];		
	}
	else
	{
		$dformat = 'dd/mm/yyyy';
	}	
    
	$customFormName = $this->customFormNameA;
	$allowFutureMonthCalendar = $this->allowFutureMonthCalendar;
	$orgDetails		 = $this->orgDetails;
	$serviceProvider = $this->serviceProvider;
	$reserveKeyWords = $this->reserveKeyWords;
	$deductionTitleList = $this->deductionTitleList;
	// Serialize the array
	$serializedArray = json_encode($reserveKeyWords);
	?>
	<input type="hidden" id="reserveKeyWords" value="<?php echo htmlspecialchars($serializedArray); ?>">
	<input type="hidden" name="fieldForce" id="fieldForce" value="<?php echo $orgDetails['Field_Force']; ?>" />
	<?php
	if ($deductionUser['View'] == 1 && ((!empty($customFormName) && array_key_exists("Enable",$customFormName) && $customFormName['Enable'] == 1) || empty($customFormName))) {
		$this->headTitle($customFormName['New_Form_Name']!='' ? $customFormName['New_Form_Name'] : $this->formName);
?>
<div class="col-md-12 portlets">
	<div class="panel" id="gridPanelDeductions">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong id="lblFormNameA"><?php echo ($customFormName['New_Form_Name']!='' ? $customFormName['New_Form_Name'] : $this->formName);?></strong></h3>
		</div>
		<div class="panel-content">			
			<div class="m-b-10">
					
                    <?php   if ($deductionUser['Delete'] || $deductionUser['Is_Manager']==1 ||$deductionUser['Admin']) { ?>
                    <button type="button" class="btn btn-white btn-embossed toolbar-icons" title="Check All" id="deductionCheckAll">
						<i class="hr-check-all"></i><span class="hidden-xs hidden-sm">Check All</span>
					</button>
                    
                    <!-- Add Button in Grid Toolbar -->
                    <?php } if ($deductionUser['Add'] && ($deductionUser['Is_Manager']==1 || !empty($deductionUser['Admin']))) { ?>
					<button type="button" class="btn btn-white btn-embossed toolbar-icons" title="Add" data-toggle="modal" data-target="#modalFormDeductions" id="addDeductions">
						<i class="mdi-content-add"></i><span class="hidden-xs hidden-sm"> Add</span>
					</button>
					
					<?php }?>
					
                    <!-- View Button in Grid Toolbar -->
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="viewDeductions" title="View">
						<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
					</button>
                    
					<!-- Update Button in Grid Toolbar -->
					<?php if ($deductionUser['Update']) { ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="editDeductions" title="Edit">
						<i class="mdi-editor-mode-edit"></i><span class="hidden-xs hidden-sm"> Edit</span>
					</button>
					<?php } if ($deductionUser['Is_Manager'] || $deductionUser['Admin']) { ?>			
					<!-- Status Update Button in Grid Toolbar -->					
					<button type="button" class="btn btn-secondary-default btn-embossed btn-off disabled toolbar-icons" id="statusApprovalDeductions" title="Status Approval" >
						<i class="mdi-action-info-outline"></i><span class="hidden-xs hidden-sm"> Status Update</span>
					</button>
                    
                    <button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="Status Approval" data-toggle="modal" data-target="#modalMultiStatusDeductions" id="multistatusDeductions">
						<i class="hr-status-approval"></i><span class="hidden-xs hidden-sm"> Status Approval</span>
					</button>
					
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="Stop Recurring Deduction" data-toggle="modal" data-target="#modalCancelRecurringDeduction" id="cancelRecurringDeduction">
						<i class="mdi-content-block"></i><span class="hidden-xs hidden-sm">Stop Recurring Deduction</span>
					</button>
                    
					<?php }  if ($deductionUser['Delete']) { ?>
					
					<button type="button" class="btn btn-secondary-default btn-embossed btn-off disabled toolbar-icons" id="deleteDeduction" title="Delete">
						<i class="mdi-action-delete"></i><span class="hidden-xs hidden-sm"> Delete</span>
					</button>
					<?php }  ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="History" id="historyDeductions">
					<i class="mdi-action-history"></i><span class="hidden-xs hidden-sm"> History</span>
					</button>
					
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="Comment" id="commentDeductions">
						<i class="mdi-communication-comment"></i><span class="hidden-xs hidden-sm"> Comment</span>
					</button>
					
                    <!-- Filter Button in Grid Toolbar -->
                    <a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterDeductions">
                        <i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm">Filter</span>
                    </a>					
			</div>
			
            <!--Provident Fund Grid -->			
			<table class="table dataTable table-striped table-dynamic table-hover" id="tableDeductions">
				<thead>
					<tr>
						<th></th>
						<th id="deductionEmpId">Employee Id</th>
						<th id="deductionEmpName">Employee Name</th>
						<th id="deductionDeductionType">Deduction Type</th>
						<th id="deductionDeductionAmount">Amount</th>
						<th id="deductionEffectiveDate">Effective Date</th>
						<th id="deductionEndDate">End Date</th>
						<th id="deductionDeductionStatus">Status</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
		</div>
	</div>
</div>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="deduction-context-menu" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextDeductions"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($deductionUser['Update']) { ?>
		<li><a tabindex="-1" id="editContextDeductions"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<?php }  if ($deductionUser['Is_Manager']  || $deductionUser['Admin']) { ?>		
		<li><a tabindex="-1" id="statusContextDeductions"><i class="mdi-action-info-outline"></i> Status Update</a></li>
		<li><a tabindex="-1" id="cancelContextDeductions"><i class="mdi-content-block"></i> Stop Recurring Deduction</a></li>
		<?php }  if ($deductionUser['Delete']) { ?>
		<li><a tabindex="-1" id="deleteContextDeductions"><i class="mdi-action-delete"></i> Delete</a></li>
		<?php }  ?>
		<li><a tabindex="-1" id="historyContextDeductions"><i class="mdi-action-history"></i> History</a></li>
		<li><a tabindex="-1" id="CommentContextDeductions"><i class="mdi-action-history"></i> Comment</a></li>
	</ul>
</div>

<!--Filter Form-->
<div class="builder" id="filterPanelDeductions">
	<div id="closeFilterDeductions"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel filterReset" style="width: 100%;" id="cancelDeductions">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="applyDeductions">Apply</button>
			
			<!--Filter for Employee Name-->
			<?php if ($deductionUser['View'] == 1 && $deductionUser['Is_Manager'] == 0 && empty($deductionUser['Admin'])) { ?>
			
			<div class="form-group">
				<label>Employee Name</label>
				<!--<input type="text" class="form-control" id="filterEmployeeName" readonly="readonly" value="<php echo $deductionUser['Employee_Name']['Emp_First_Name'] .' '. $deductionUser['Employee_Name']['Emp_Last_Name']; ?>" >-->
				<input type="text" class="form-control" id="filterEmployeeName" readonly="readonly" value="<?php echo $deductionUser['Employee_Name']; ?>" >
			</div>
			
			<?php } else { ?>
			
			<div class="form-group">
				<label>Employee Name</label>
				<input type="text" class="form-control" id="filterEmployeeName" placeholder="Employee Name" >
			</div>
			
			<?php } ?>
			
			<!--Filter For Deductions Type -->
			<div class="form-group">
				<label>Deductions Type</label>
				<select class="form-control" data-search="true" id="filterDeductionTypeSelect" >					
					<option value="">All</option>
					<option value="One-Month Deduction" >One-Month Deduction</option>
					<option value="Recurring Deduction" >Recurring Deduction</option>					
				</select>
			</div>

			<!--Filter For Deductions Title -->
			<div class="form-group">
				<label>Deductions Title</label>
				<select class="form-control" data-search="true" id="filterDeductionTitleSelect" >					
					<option value="">All</option>
					<?php
						foreach ($deductionTitleList as $key => $row) {
							echo '<option value="'.$key.'">'.$row.'</option>';
						}
					?>					
				</select>
			</div>
			
			<!--Filter For Pre-Tax Deduction-->
			<div class="form-group">
				<label>Pre-Tax Deduction</label>
				<select class="form-control" data-search="true" id="filterPreTaxDeduction">
					<option value="">All</option>				
					<option value="1" >Yes</option>
					<option value="2" >No</option>
				</select>
			</div>

			<!--Filter For Effective Date -->
			<div class="form-group">
				<label>Effective Date</label>
				<div class="input-daterange b-datepicker input-group" data-date-format="<?php echo $dformat; ?>" id="datepicker">
					<input type="text" class="input-md form-control" name="effectivestart" id="filterEffectiveDateBegin" data-orientation="top" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="text" class="input-md form-control" name="effectiveend" id="filterEffectiveDateEnd" data-orientation="top" placeholder="End"/>
				</div>
			</div>
			
			<!--Filter For Deduction Amount -->
			<div class="form-group">
				<label>Deduction Amount</label>
				<div class="input-group">
					<input type="number" class="form-control" name="DeductionAmountStart" id="filterDeductionAmountStart" min="0" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="form-control" name="DeductionAmountEnd" id="filterDeductionAmountEnd" min="0" placeholder="End"/>
				</div>
			</div>
			
			<!--Filter For End Date-->
			<div class="form-group">
				<label>End Date</label>
				<div class="input-daterange b-datepicker input-group" data-date-format="<?php echo $dformat; ?>" id="datepicker">
					<input type="text" class="input-md form-control" name="start" id="filterEndDateBegin" data-orientation="top" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="text" class="input-md form-control" name="end" id="filterEndDate" data-orientation="top" placeholder="End"/>
				</div>
			</div>
			
			<!--Filter For Status-->
			<div class="form-group">
				<label>Status</label>
				<select class="form-control" data-search="true" id="filterStatusSelect" >					
					<option value="">All</option>
					<option value="Applied" >Applied</option>
					<option value="Approved" >Approved</option>
					<option value="Returned" >Returned</option>
					<option value="Rejected" >Rejected</option>
				</select>
			</div>

			<?php if ($orgDetails['Field_Force'] == 1) { ?>
				<div class="form-group">
					<label>Service Provider</label>
					<select class="form-control" data-search="true" id="filterServiceProvider">
						<option value="">All</option>
						<?php
						foreach ($serviceProvider as $key => $row)
						{
							echo '<option value="'. $key .'">'. $row .'</option>';
						}
						?>
					</select>												
				</div>
			<?php } ?>
			
		</div>
	</div>
</div>

<!--Modal for Deductions view,add & edit form-->
<div class="modal fade" id="modalFormDeductions" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace" id="backDeduction"></i>
				</button>
				
				<?php if ($deductionUser['Update']) { ?>
				<button type="button" class="close form-icons" aria-hidden="true" id="editInViewDeductions">
					<i class="mdi-editor-mode-edit"></i>
				</button>
				<?php } ?>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<!--View Deductions Form-->
				<form role="form" id="viewFormDeductions" >
					<div class="row">						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label"> Employee Name</label></div>
							<div class="col-md-7"><p id="viewEmployeeName"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label"> Deduction Type</label></div>
							<div class="col-md-7"><p id="viewDeductionType"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label"> Deduction Title</label></div>
							<div class="col-md-7"><p id="viewDeductionTitle"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label"> Pre-Tax Deduction</label></div>
							<div class="col-md-7"><p id="viewPreTaxDeduction"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label"> Effective Date</label></div>
							<div class="col-md-7"><p id="viewDeductionEffectiveDate"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label"> End Date</label></div>
							<div class="col-md-7"><p id="viewDeductionEndDate"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label"> Amount</label></div>
							<div class="col-md-7"><p id="viewDeductionAmount"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label"> Forwarded To</label></div>
							<div class="col-md-7"><p id="viewDeductionForwardTo"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Perquisites</label></div>
							<div class="col-md-7"><p id="viewPerquisites"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label"> Status</label></div>
							<div class="col-md-7"><p id="viewDeductionStatus"></p></div>
						</div>						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Approved By</label></div>
							<div class="col-md-7"><p id="viewDeductionApprovedBy"></p></div>
						</div>
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Approved On</label></div>
							<div class="col-md-7"><p id="viewDeductionApprovedOn"></p></div>
						</div>
					</div>
					
					<div class="row">
						<hr class="view-hr"/>
						
						<div class="form-group" style="font-size: large;margin-left: 13px;">
							<label class="control-label text-center">Additional Information</label>
						</div>
						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Added On</label></div>
							<div class="col-md-7"><p id="addedOnDeductions"></p></div>
						</div>
						
						<div class="form-group">
							<div class="col-md-5"><label class="control-label">Added By</label></div>
							<div class="col-md-7"><p id="addedByDeductions"></p></div>
						</div>
					</div>
				</form>
				<?php if ($deductionUser['Add'] == 1 || $deductionUser['Update'] == 1) { ?>
				
				<!--Add/Edit Deductions Form-->
				<form role="form" class="form-horizontal form-validation" id="editFormDeductions" method="POST" action="">					
					<input type="hidden" name="Deduction_Id" id="DeductionId" />
					<input type="hidden" name="Session_Id" id="Session_Id" value="<?php echo $deductionUser['Session_Id'];?>" />
					<input type="hidden" name="loginEmpDeleteRights" id="loginEmpDeleteRights" value="<?php echo ($deductionUser['Delete'] == 1); ?>" />
                    
					<div class="row">					
						<div class="form-group">
							<label class="col-md-4 control-label">Employee Name <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<?php
									if ($deductionUser['Is_Manager'] == 1 || $deductionUser['Admin'] == 'admin')
									{
										echo '<select multiple="multiple" class="form-control vRequired selectAlll" name="Employee_Name" id="formEmployeeName" data-search="true">';
										
										echo '<option value="selectAll">--Select all--</option>';
                                        echo '<option value="clearAll">--Clear all--</option>';
										
                                        foreach ($empDepartment as $key => $row) {
											echo '<optgroup label="'. $row .'">';
											
											foreach ($employeeName as $empKey => $empRow) {
												if ($row == $empRow['Department_Name']) {
													echo '<option value="'. $empRow['value'] .'">'. $empRow['text'] .'</option>';
												}
											}
											
											echo '</optgroup>';
										}
                                        
										//foreach ($employeeName as $empKey => $empRow) {
										//		echo '<option value="'. $empRow['value'] .'">'. $empRow['text'] .'</option>';
										//	}
											
										echo '</select>';
									}
									else{
										echo '<select class="form-control vRequired" name="Employee_Name" id="formEmployeeName" data-search="true" disabled="true">';
											echo '<option value="'. $deductionUser['Session_Id'] .'">'. $deductionUser['Employee_Name'] .'</option>';
										echo '</select>';
									}
								?>								
							</div>							
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Deduction Type <span class="short_explanation">*</span></label>							
							<div class="col-md-8">								
								<select class="form-control vRequired" data-search="true" name="DeductionType" id="formDeductionType">									
									<option value="">--Select--</option>
									<option value="One-Month Deduction" selected="selected">One-Month Deduction</option>
									<option value="Recurring Deduction" >Recurring Deduction</option>									
								</select>
							</div>
                        </div>

						<div class="form-group">
							<label class="col-md-4 control-label">Deduction Title <span class="short_explanation">*</span></label>
							<div class="col-md-7">																
								<select class="form-control vRequired" data-search="true" name="formDeductionTitle" id="formDeductionTitle">
									<option value="">--Select--</option>
									<?php
										foreach ($deductionTitleList as $key => $row) {
											echo '<option value="'.$key.'">'.$row.'</option>';
										}
									?>								
								</select>
							</div>
							<div class="col-md-1">
								<!-- Refresh Deduction Title -->
								<span style="color: #9e9e9e; font-size: 18px;  cursor: pointer; "><i class="fa fa-refresh" id ="refreshDeductionTitleList"></i></span>
							</div>
						</div>

						<div class="form-group addDeductionTitleButtonField">
							<label class="col-md-4 control-label"></label>
							<div class="col-md-8">
								<div id="addDeductionTitle" style="display: flex; align-items: center;cursor: pointer;" title="Add Deduction Title">
									<i class="icon hr-add" style="font-size: 15px; color: #337ab7;"> 
									</i>
									<span style="margin-left: 3px; color: #337ab7; font-size: 15px;"> Deduction Title </span>
								</div>
							</div>
						</div>

						
						<div class="form-group deductionTitleTextField" style="display: none;">
							<label class="col-md-4 control-label"></label>
							<div class="col-md-6" style="position: relative; align-items: center;">																
								<input type="text" class="form-control allCommonInputAlphaNumericValidation vDeductionTitle" id="formTextDeductionTitle" name="formTextDeductionTitle" minlength=3 maxlength=50  placeholder="Deduction Title">
							</div>
							<div class="col-md-2">
								<!-- Submit Deduction Title -->
								<span id="submitDeductionTitle" style="color: green; font-size: 24px; margin-left: 5px; cursor: pointer; display: inline; ">✔</span>
								<!-- Cancel Add Deduction Title Action -->
								<span id="cancelDeductionTitleAddAction" style="color: red; font-size: 24px; margin-left: 10px; cursor: pointer; display: inline;">×</span>
							</div>
						</div>

						<div class="form-group">
							<label class="col-md-4 control-label">Pre-Tax Deduction
							<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
							data-placement="top" data-content="Enabling this flag will deduct the amount without considering it for taxable income during the payroll">
							</i>
							</label>
							<div class="col-md-8 togglebutton togglebutton-material-blue" style="padding-left: 15px; padding-top: 10px;">
								<label>
									<input id="formPreTaxDeduction" type="checkbox" class="col-sm-9 md-checkbox">
								</label>
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Effective Date<span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<input type="text" class="date-picker form-control vRequired datePickerRead" name="EffectiveDate" id="deductionFormEffectiveDate" placeholder="Select a date...">
							</div>
						</div>
					
						<?php
						if(!empty($allowFutureMonthCalendar))
						{?>
							<div class="form-group showLConsiderationDateRangeInfo">
								<div class="col-md-4"></div>
								<div class="col-md-8">
								<div class="column-cus-info-bar">
									<i class="fa fa-info-circle column-cus-info-icon" aria-hidden="true"></i>
									<span id="considerationDateRangeMsg">Deduction created for future month payroll needs to be adjusted during the full and final settlement manually based on its applicability for employees leaving the organization.</span>
								</div>
								</div>
							</div>
						<?php 
					    } ?>                        
						<div class="form-group deductionTypeBasedShow">
							<label class="col-md-4 control-label">End Date<span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<input type="text" class="form-control" name="EndDate" id="deductionFormEndDate" placeholder="Select a date..." data-rule-checkPastDate="true" data-msg-checkPastDate="Date is in past">								
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Amount <span class="short_explanation">*</span></label>
							<div class="col-md-8">																
								<input type="number" class="form-control vRequired convertDecimalPos1" id="formDeductionAmount" name="Amount" min=1 max="99999999">
							</div>
						</div>						
						
						<div class="form-group" id="approverPanel">
							<label class="col-md-4 control-label">Forward To <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select class="form-control" name="Approver_Name" id="formApproverName" readonly=readonly>
									<option value="">--Select--</option>
									
								</select>
							</div>							
						</div>

						<div class="form-group">
							<label class="col-md-4 control-label">Status</label>
							<div class="col-md-8">								
								<p id="formDeductionStatus" style="margin-top:10px;"></p>
							</div>
						</div>
						
						<div class="form-group">
							<label class="col-md-4 control-label">Comment</label>
							<div class="col-md-8">
								<textarea name="Description" id="formComment" rows="5" class="form-control vComments" placeholder="Write your comment..." ></textarea>
							</div>
                        </div>
					</div>
					<button type="reset" class="cancel" id="formDeductionsReset" style="display: none;" ></button>
				</form>				
				<?php } ?>				
			</div>
			<div class="modal-footer text-center" id="formActionDeductions">				
				<?php if ($deductionUser['Add'] == 1 || $deductionUser['Update'] == 1) { ?>
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetDeductions" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitDeductions" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
				<?php } ?>
			</div>
		</div>
	</div>
</div>
<?php  if ($deductionUser['Delete']) { ?>
<div class="modal fade" id="modalDeleteDeduction" aria-hidden="true" >
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteConfDeduction"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfDeduction">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConformDeduction">Yes</button>
			</div>
		</div>
	</div>
</div>
<?php } ?>
<!-- Modal for view audit history in Deduction -->
<div class="modal fade" id="modalHistoryDeduction" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" id="modalFormCloseDeductionHistory" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace"></i>
				</button>
				
				<h4 class="modal-title" id="deductionHistoryTitle"></h4>
			</div>
			<div class="modal-body">
				<table class="table dataTable table-striped table-dynamic table-hover" id="tableDeductionHistory">
				<thead>
					<tr>
						<th></th>
						<th id="deductionHistoryPreviousAmount">Previous Deduction Amount</th>
						<th id="deductionHistoryUpdatedAmount">Updated Deduction Amount</th>						
						<th id="deductionHistoryModifiedBy">Modified By</th>
						<th id="deductionHistoryModifiedOn">Modified On</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
			</div>
		</div>
	</div>
</div>

<!-- Comment Form -->
<div class="modal fade" id="modalCommentDeductions" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeCommentDeduction"></i></button>
				<h4 class="modal-title"><strong>Comment</strong></h4>
			</div>
			
			<div class="modal-body">
				<!-- comment Grid Table -->
				<table class="table dataTable table-striped table-dynamic table-hover tableCommentDeductions">
					<thead>
						<tr>
							<th></th>
							<th id="deductionCommentEmpName">Employee Name</th>
							<th id="deductionDeductionComment">Comment</th>
							<th id="deductionCommentStatus">Status</th>
							<th id="deductionCommentAddedOn">Added On</th>
						</tr>
					</thead>
					<tbody>
					
					</tbody>
				</table>
			</div>
			
		</div>
	</div>
</div>

<!-- Form Dirty Confirmation Modal -->
<?php  if (($deductionUser['Add'] == 1 || $deductionUser['Update'] == 1) && ($deductionUser['Is_Manager'] == 1 || !empty($deductionUser['Admin']))) { ?>
<div class="modal fade" id="dirtyDeductions" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditConfDeduction"></i></button>
                <h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
            </div>
            
            <div class="modal-body">Are you sure want to close this form?<br></div>
            
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfDeduction">No</button>
              <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="closeDeductions">Yes</button>
            </div>
        </div>
    </div>
</div>

<!-- Status Approval Modal For Deductions -->
<?php if ($deductionUser['Is_Manager']||$deductionUser['Admin']) {?>
<div class="modal fade" id="modalStatusDeductions" aria-hidden="true" style="z-index: 10;">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" id="modalStatusFormClose" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace"></i>
				</button>
				<h4 class="modal-title"> Update Status</h4>
			</div>
			<div class="modal-body">
				<form role="form" class="form-horizontal form-validation" id="statusFormDeductions" method="POST" action="">
					<input type="hidden" name="Deduction_Id" id="formStatusDeductionId" />
					<div class="row">
						<div class="form-group">
							<label class="col-md-3 control-label">Status <span class="short_explanation">*</span></label>
							<div class="col-md-9">
								<select class="form-control vRequired" name="Status" id="statusApproval">
									<option value="Approved">Approved</option>
									<option value="Returned">Returned</option>
									<option value="Rejected">Rejected</option>
								</select>
							</div>
						</div>
												
						<div class="form-group">
							<label class="col-md-3 control-label" id="labelStatusComment">Comment </label>
							<div class="col-md-9">
								<textarea name="comment" id="formStatusComment" rows="5" class="form-control vDescription"
										  placeholder="Write your Comment..." ></textarea>
							</div>
						</div>
						
					</div>
				</form>
				
			</div>
			<div class="modal-footer text-center">				
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetStatusDeductions" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formStatusDeductions" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="modalMultiStatusDeductions" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeMultiStatusConfDeduction"></i></button>
				<h4 class="modal-title"><strong>Approval</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure you want to approve the records?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noMultiStatusConfDeduction">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" data-style="expand-left"  id="multiStatusApprovalDeductions">Yes</button>
			</div>
		</div>
	</div>
</div>


<!-- Form Dirty Confirmation Modal For Status Approval-->
<div class="modal fade" id="modalDirtyStatusDeductions" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeStatusConfDeduction"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noStatusConfDeduction">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseConformStatusDeductions">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php } } }if($deductionUser['View']!=1) {?>

<div class="col-md-12 portlets">
	<div class="txt_center">Sorry, Access Denied...</div>
</div>

<?php } ?>


