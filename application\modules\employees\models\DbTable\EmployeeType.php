<?php
//=========================================================================================
//=========================================================================================
/* Program : EmployeeType.php											   			     *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MYSQL Query to retrive, add, update employee types.					 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Shobana	              Initial Version        	         *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Saranya                 Changes in file for mobile app     *
 *                                                    1.Extra fields are added in        *
 *                                                    field list of list query.          *
 *                                                                                       *
 *  1.4        25-Feb-2016    Deepak              Changes in file for Bootstrap          *
 *                                                                                       */
//=========================================================================================
//=========================================================================================
class Employees_Model_DbTable_EmployeeType extends Zend_Db_Table_Abstract
{
    protected $_db = null;
	protected $_ehrTables = null;
	protected $_dbCommonFun = null;
	protected $_orgDF       = null;
	
    public function init()
    {
        $this->_ehrTables   = new Application_Model_DbTable_Ehr();
        $this->_db          = Zend_Registry::get('subHrapp');
    	$this->_dbCommonFun = new Application_Model_DbTable_CommonFunction();
		$this->_orgDF       = $this->_ehrTables->orgDateformat();
    }
    
	//to list and search employeetypes
    public function listEmployeeType($page, $rows, $sortField, $sortOrder,$searchArr, $searchAll=null, $logEmpId)
	{
		$empType=$searchArr['empType'];
	 	$benefitsApplicable=$searchArr['benefitsApplicable'];
		$holidayEligiblity=$searchArr['holidayEligiblity'];
		$salaryCalcDays=$searchArr['salaryCalcDays'];
		$excludeBreakHours=$searchArr['excludeBreakHours'];
		$attendanceProcessStatus=$searchArr['attendanceProcessStatus'];
		$enableWorkPlace=$searchArr['enableWorkPlace'];
		$status = $searchArr['status'];
		
		switch($sortField)
        {
			case 1: $sortField = 'type.Employee_Type'; break;
			case 2: $sortField = 'type.Benefits_Applicable'; break;
			case 3: $sortField = 'type.Holiday_Eligiblity'; break;
			case 4: $sortField = 'type.Salary_Calc_Days'; break;
			case 5: $sortField = 'type.Exclude_Break_Hours'; break;
			case 6: $sortField = 'type.Attendance_Process_Status'; break;
			case 7: $sortField = 'type.Enable_Work_Place'; break;
		}
		
		$qryEmployeeType = $this->_db->select()
								->from(array('type'=>$this->_ehrTables->empType),
									   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS type.EmpType_Id as count'),
											 'type.EmpType_Id','type.Employee_Type', 'type.Holiday_Eligiblity','type.Enable_Work_Place','type.Approve_Dashboard_Attendance',
											 'type.Description','type.Lock_Flag','type.Fixed_Days','type.Salary_Calc_Days','type.Benefits_Applicable',
											 'type.Display_Total_Hours_In_Minutes', 'type.Exclude_Break_Hours','type.Work_Schedule','type.Attendance_Process_Status',
											 'Benefits_Applicable_Flag'=>new Zend_Db_Expr("CASE WHEN Benefits_Applicable='1' Then 'Yes' Else 'No' End"),
											 'Holiday_Eligiblity_Flag'=>new Zend_Db_Expr("CASE WHEN Holiday_Eligiblity='1' Then 'Yes' Else 'No' End"),
											 'Exclude_Break_Hours_Flag'=>new Zend_Db_Expr("CASE WHEN Exclude_Break_Hours='1' Then 'Yes' Else 'No' End"),
											 'Enable_Work_Place_Flag'=>new Zend_Db_Expr("CASE WHEN Enable_Work_Place='1' Then 'Yes' Else 'No' End"),
											 'Salary_Calc_Days_Flag'=>new Zend_Db_Expr('CASE WHEN Salary_Calc_Days = "0" THEN "Business Working Days"
											 WHEN Salary_Calc_Days = "1" THEN "All Days of Salary Month"
											 WHEN Salary_Calc_Days = "2" THEN "Average Days in a Month"
											 WHEN Salary_Calc_Days = "3" THEN "Fixed Days of Salary Month"
										 END'),
										 'Comp_Off_Days_Flag'=>new Zend_Db_Expr('CASE WHEN Comp_Off_Days = "0" THEN "Business Working Days"
											 WHEN Comp_Off_Days = "1" THEN "All Days of Salary Month"
											 WHEN Comp_Off_Days = "2" THEN "Average Days in a Month"
											 WHEN Comp_Off_Days = "3" THEN "Fixed Days of Salary Month"
										 END'),'type.Comp_Off_Days','type.Comp_Off_Fixed_Days',
											 'Added_On'=>new Zend_Db_Expr("DATE_FORMAT(type.Added_On,'".$this->_orgDF['sql']." %H:%i:%s')"),
											 'Updated_On'=>new Zend_Db_Expr("DATE_FORMAT(type.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s')"),
											 'Log_Id'=>new Zend_Db_Expr($logEmpId),'type.EmployeeType_Status'))
								
								->joinLeft(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id = type.Lock_Flag',
										   array('Lock_EmpName'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name, ' ', P.Emp_Last_Name)")))
								
								->joinLeft(array('emp1'=>$this->_ehrTables->empPersonal),'emp1.Employee_Id=type.Added_By',
										   new Zend_Db_Expr("CONCAT(emp1.Emp_First_Name, ' ', emp1.Emp_Last_Name) as Added_By_Name"))
								
								->joinLeft(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=type.Updated_By',
										   new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as Updated_By_Name"))

								->joinLeft(array('AAWP'=>$this->_ehrTables->autoApprovalWorkPlace),'type.EmpType_Id=AAWP.Employee_Type_Id',
										   new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as Updated_By_Name"))

								->joinLeft(array('AWP'=>$this->_ehrTables->attendanceWorkPlace),'FIND_IN_SET(AWP.Work_Place_Id,AAWP.Work_Place_Id)',array(new Zend_Db_Expr('Group_CONCAT(AWP.Work_Place ORDER BY AWP.Work_Place ASC) as Work_Place'),
								new Zend_Db_Expr('Group_CONCAT(AWP.Work_Place_Id ORDER BY AWP.Work_Place_Id ASC) as Work_Place_Id')))		   

								->order("$sortField $sortOrder")
								->limit($rows, $page)
								->group('type.EmpType_Id');
		
		/**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			$conditions = $this->_db->quoteInto('type.Employee_Type Like ?', "%$searchAll%");
			
			$conditions .= $this->_db->quoteInto(' or type.Attendance_Process_Status Like ?', "%$searchAll%");
			
			if(substr_count("yes",strtolower($searchAll)) > 0)
			{
				$conditions .= $this->_db->quoteInto(' or type.Benefits_Applicable = ?', 1);
				$conditions .= $this->_db->quoteInto(' or type.Holiday_Eligiblity = ?', 1);
				$conditions .= $this->_db->quoteInto(' or type.Exclude_Break_Hours = ?', 1);
				$conditions .= $this->_db->quoteInto(' or type.Enable_Work_Place = ?', 1);
			}
			if(substr_count("no",strtolower($searchAll)) > 0)
			{
				$conditions .= $this->_db->quoteInto(' or type.Benefits_Applicable = ?', 0);
				$conditions .= $this->_db->quoteInto(' or type.Holiday_Eligiblity = ?', 0);
				$conditions .= $this->_db->quoteInto(' or type.Exclude_Break_Hours = ?', 0);
				$conditions .= $this->_db->quoteInto(' or type.Enable_Work_Place = ?', 0);
			}
			if(substr_count("all days of salary month",strtolower($searchAll)) > 0)
			{
				$conditions .= $this->_db->quoteInto(' or  type.Salary_Calc_Days = ?', 1);
			}
			if(substr_count("average days in a month",strtolower($searchAll)) > 0)
			{
				$conditions .= $this->_db->quoteInto(' or  type.Salary_Calc_Days = ?', 2);
			}
			if(substr_count("fixed days of salary month",strtolower($searchAll)) > 0)
			{
				$conditions .= $this->_db->quoteInto(' or  type.Salary_Calc_Days = ?', 3);
			}
			if(substr_count("business working days",strtolower($searchAll)) > 0)
			{
				$conditions .= $this->_db->quoteInto(' or  type.Salary_Calc_Days = ?', 0);
			}
			$conditions .= $this->_db->quoteInto(' or type.EmployeeType_Status Like ?', "%$searchAll%");
			$qryEmployeeType->where($conditions);
		}
		
	
		if (!empty($empType) && preg_match('/^[0-9a-zA-Z ]+$/', $empType) )
        {	
            $qryEmployeeType->where('type.Employee_Type Like ?', "$empType%");
		}
		
		if (!empty($benefitsApplicable))
        {
			if($benefitsApplicable == 2)
			{
				$benefitsApplicable = 0;
			}
            $qryEmployeeType->where('type.Benefits_Applicable = ?',$benefitsApplicable);
        }
		if (!empty($holidayEligiblity))
        {
			if($holidayEligiblity == 2)
			{
				$holidayEligiblity = 0;
			}
            $qryEmployeeType->where('type.Holiday_Eligiblity = ?', $holidayEligiblity);
		}
		if (!empty($salaryCalcDays))
        {
			if($salaryCalcDays == 4)
			{
				$salaryCalcDays = 0;
			}
		
            $qryEmployeeType->where('type.Salary_Calc_Days = ?', $salaryCalcDays);
		}
		if (!empty($excludeBreakHours))
        {
			if($excludeBreakHours == 2)
			{
				$excludeBreakHours = 0;
			}
            $qryEmployeeType->where('type.Exclude_Break_Hours = ?', $excludeBreakHours);
		}
		if (!empty($attendanceProcessStatus))
        {	
            $qryEmployeeType->where('type.Attendance_Process_Status = ?',$attendanceProcessStatus);
		}
		if (!empty($enableWorkPlace))
        {
			if($enableWorkPlace == 2)
			{
				$enableWorkPlace = 0;
			}
            $qryEmployeeType->where('type.Enable_Work_Place = ?', $enableWorkPlace);
		}
		if (!empty($status))
        {	
            $qryEmployeeType->where('type.EmployeeType_Status = ?', "$status");
		}
		/**
		 * SQL queries
		 * Get data to display
		*/
		$employeeType = $this->_db->fetchAll($qryEmployeeType);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		/* Total data set length */
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empType, new Zend_Db_Expr('COUNT(EmpType_Id)')));
		
		/**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $employeeType);
	}
	
	/**
	 *	Update Employee Type details in EmployeeType table and update system log too
    */	
	public function updateEmployeeType($EmployeeTypeArray, $sessionId, $formName,$workPlaceIds, $employeeTypeOldStatus)
	{
		/**
		 *	Check Employee Type is Already exist or not
		*/
		$qryEmployeeType = $this->_db->select()->from($this->_ehrTables->empType, new Zend_Db_Expr('Count(EmpType_Id)'))
												->where('Employee_Type = ?', $EmployeeTypeArray['Employee_Type']);
		
		/**
		 *	if Employee Type Id exist then check Employee Type is present other than that Employee Type id
		*/
		if ($EmployeeTypeArray['EmpType_Id'])
		{
			$qryEmployeeType->where('EmpType_Id != ?', $EmployeeTypeArray['EmpType_Id']);
		}
		
        $isExist = $this->_db->fetchOne($qryEmployeeType);
		
		/**
		 *	If Employee Type isn't exist then process add/update 
		*/
		if ($isExist == 0)
		{
			//If the old status is active and the new status is inactive then check whether employee type is used or not
			if($EmployeeTypeArray['EmployeeType_Status'] == 'InActive' && $employeeTypeOldStatus == 'Active'){
				$isEmployeeTypeUsed = $this->isEmployeeTypeUsed($EmployeeTypeArray['EmpType_Id'], $EmployeeTypeArray['Employee_Type'],'edit');
        	}else{
				$isEmployeeTypeUsed = 0;
			}

			if( $isEmployeeTypeUsed == 0)
			{
				/**
				 *	If Employee Type id exist then process update action
				*/
				if (!empty($EmployeeTypeArray['EmpType_Id']))
				{
					$action = 'Edit';
					
					$tableBenefit = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empType, 'Benefits_Applicable')
													->where('Employee_Type = ?', $EmployeeTypeArray['Employee_Type']));
					
					$EmployeeTypeArray['Updated_On'] = date('Y-m-d H:i:s');
					$EmployeeTypeArray['Updated_By'] = $sessionId;
					
					$updated = $this->_db->update($this->_ehrTables->empType, $EmployeeTypeArray, array('EmpType_Id = '. $EmployeeTypeArray['EmpType_Id']));
					
					if ($updated)
					{
						if($EmployeeTypeArray['Benefits_Applicable'] != $tableBenefit)
						{
							$salRecalEmployees = $this->_db->fetchCol($this->_db->select()->from(array('S'=>$this->_ehrTables->salary),
																								'Employee_Id')
																	->joinInner(array('J'=>$this->_ehrTables->empJob),
																				'S.Employee_Id = J.Employee_Id', array())
																	
																	->where('J.EmpType_Id = ?', $EmployeeTypeArray['EmpType_Id']));
							
							if(count($salRecalEmployees) > 0)
							{
								$dbAllowance = new Payroll_Model_DbTable_Allowances();
								$dbAllowance->setSalRecal($salRecalEmployees, 1);
							}        		
						}
					}
					$employeeTypeId = $EmployeeTypeArray['EmpType_Id'];
				}
				else
				{
					/**
					 *	If Employee Type id is empty then process add action
					*/
					$action = 'Add';
					$EmployeeTypeArray['Added_On'] = date('Y-m-d H:i:s');
					$EmployeeTypeArray['Added_By'] = $sessionId;
					$updated = $this->_db->insert($this->_ehrTables->empType, $EmployeeTypeArray);
					$employeeTypeId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empType,array('MAX(EmpType_Id)')));
				}

				$autoApprovalWorkPlace = array();
				if(!empty($workPlaceIds))
				{
					foreach($workPlaceIds as $workPlaceId)
					{
						$autoApprovalWorkPlace[] = array('Employee_Type_Id' => $employeeTypeId,
														'Work_Place_Id' => $workPlaceId);
					}
				}
				
				if(!empty($employeeTypeId))
				{
					$deleted = $this->_db->delete($this->_ehrTables->autoApprovalWorkPlace,'Employee_Type_Id='.(int)$employeeTypeId);
				}
				
				if(!empty($autoApprovalWorkPlace))
				{
					$this->_ehrTables->insertMultiple($this->_ehrTables->autoApprovalWorkPlace,$autoApprovalWorkPlace);
				}
				
				/** Update the DataSetup status **/
				if($updated)
				{
					$updated = $this->_dbCommonFun->updateDataSetupDashboard('Completed','27');                
				}
				
				
				/**
				 *	this function will handle
				*		update system log function
				*		clear submit lock fucntion
				*		return success/failure array
				*/
				return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
																'action'         => $action,
																'trackingColumn' => $EmployeeTypeArray['Employee_Type'],
																'formName'       => $formName,
																'sessionId'      => $sessionId,
																'comboPair'      => $this->getEmpTypePairs(),
																'tableName'      => $this->_ehrTables->empType));
			}else{
				return array('success'=>false, 'msg'=>'Unable to update '.$formName.' as it is used in either employee job, recruitment, individuals, candidate, employee import or custom employee group', 'type'=>'info');
			}
		}
		else
		{
			return array('success' => false, 'msg'=>$formName.' already exist', 'type'=>'info');
		}
	}

	public function isEmployeeTypeUsed($empTypeId,$employeeType,$source){
		$exInEmpJobQuery = $this->_db->select()->from($this->_ehrTables->empJob, new Zend_Db_Expr('COUNT(EmpType_Id)'))
								->where('EmpType_Id = ?', $empTypeId);

		if($source == 'edit'){
			$exInEmpJobQuery = $exInEmpJobQuery->where('Emp_Status = ?', 'Active');
		}
		$exInEmpJob = $this->_db->fetchOne($exInEmpJobQuery);

        $exInRecruitment = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->recruitment,
																		   new Zend_Db_Expr('COUNT(Job_Type)'))
												->where('Job_Type = ?', $empTypeId));
		
		$exInCandidateQuery = $this->_db->select()->from($this->_ehrTables->candidateJob,
								new Zend_Db_Expr('COUNT(EmpType_Id)'))
							->where('EmpType_Id = ?', $empTypeId);
		if($source == 'edit'){
			$exInCandidateQuery = $exInCandidateQuery->where('Emp_Status = ?', 'Active');
		}
		$exInCandidate = $this->_db->fetchOne($exInCandidateQuery);

		$exInCustomGroup = $this->_dbCommonFun->keyExistInCustomGroup('EmpType_Id',$empTypeId);

		if( $exInEmpJob == 0 && $exInRecruitment == 0
		&& $exInCandidate == 0 && $exInCustomGroup ==0){
			return 0;
		}else{
			return 1;
		}
	}
	
	public function deleteEmployeeType($empTypeId, $logEmpId, $formName)
    {
		$empTypeLock = $this->_db->fetchRow($this->_db->select()
												->from($this->_ehrTables->empType, array('Lock_Flag', 'Employee_Type'))
												->where('EmpType_Id = ?', $empTypeId));
		$isEmployeeTypeUsed = $this->isEmployeeTypeUsed($empTypeId,$empTypeLock['Employee_Type'],'delete');
        
		if( $isEmployeeTypeUsed == 0)
		{
			$deleted = 0;
					
			/**
			 *	Check lockflag is empty continue to delete or not show error message like employee is editing that record
			 */
			if ($empTypeLock['Lock_Flag'] == 0)
			{
				/* Delete activity */
				$deleted = $this->_db->delete($this->_ehrTables->empType,'EmpType_Id='.(int)$empTypeId);
			}
			
			/**
			 *	delete activity for common function
			 *		1)check lock is exist or not.
			 *			If lock is exist then show error message like employee is open record for update.
			 *		2)If No lockflag then process delete activity
			 *		3)Update delete activity in system log
			 *		4)return success/failure message
			*/
			return $this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
															'lockFlag'       => $empTypeLock['Lock_Flag'],
															'formName'       => $formName,
															'trackingColumn' => $empTypeLock['Employee_Type'],
															'sessionId'      => $logEmpId,
															'comboPair'      => $this->getEmpTypePairs()));
		}
		else
        {
            return array('success'=>false, 'msg'=>'Unable to delete '.$formName.' as it is used in either employee job, recruitment, individuals, candidate, employee import or custom employee group.Please, contact system admin', 'type'=>'info');
        }
    }
	
	//to fetch employee type based on id
    public function viewEmployeeType ($typeId=null, $calledbySalRecal=null, $employeeId=null)
    {
		$reqFields = array('emptype.Benefits_Applicable');
    	
		if (is_null($calledbySalRecal))
    	{
    		$reqFields = array_merge($reqFields, array('emptype.EmpType_Id', 'emptype.Employee_Type', 'emptype.Description','emptype.Salary_Calc_Days','emptype.Fixed_Days',
													   'emptype.Updated_By', 
													   'Added_Date' => new Zend_Db_Expr("DATE_FORMAT(emptype.Added_On,'".$this->_orgDF['sql']." at %T')"),
													   'Modified_Date' => new Zend_Db_Expr("DATE_FORMAT(emptype.Updated_On,'".$this->_orgDF['sql']." at %T')")));
			
    		return $this->_db->fetchRow( $this->_db->select()->from(array('emptype'=>$this->_ehrTables->empType),$reqFields)
										
										//->joinInner(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=emptype.Updated_By',
                                        ->joinInner(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=emptype.Added_By',
													new Zend_Db_Expr("CONCAT(Emp_First_Name, ' ', Emp_Last_Name) as Employee_Name"))
										
										->where('emptype.EmpType_Id = ?', $typeId));
    	}
    	else 
    	{
			/** If employee id is given **/
			//if(!is_null($employeeId))

			if(!empty($employeeId))
			{
				$empTypeQry = $this->_db->select()->from(array('emptype'=>$this->_ehrTables->empType),$reqFields)
											 ->joinLeft(array('job'=>$this->_ehrTables->empJob),'job.EmpType_Id=emptype.EmpType_Id',
													array(''))
											->where('job.Employee_Id = ?', $employeeId);
											

				return $this->_db->fetchOne($empTypeQry);
			
			}

			/** If employee type is given **/
			else
			{
				$empTypeQry = $this->_db->select()->from(array('emptype'=>$this->_ehrTables->empType),$reqFields)
											->where('emptype.EmpType_Id = ?', $typeId);
    		
				return $this->_db->fetchOne($empTypeQry);
			}
    	}
    }
    
	/**
     * This function is to get all employee type details of employees
     */
    public function getEmpTypePairs($typeId = null)
    {
        $emptypeQry = $this->_db ->select()->from($this->_ehrTables->empType,array('EmpType_Id','Employee_Type'))
									->where('EmployeeType_Status = ?','Active')
									->order('Employee_Type ASC');
        if($typeId != null)
        {
        	$emptypeQry->where('EmpType_Id = ?', $typeId);
        }
        
		return $this->_db->fetchPairs($emptypeQry);
    }
    
	/**
     * This function is to list all shift roaster employee types
     */
    public function listShiftRoasterEmpTypePairs()
    {
		return $this->_db->fetchPairs($this->_db ->select()->from($this->_ehrTables->empType,array('EmpType_Id','Employee_Type'))
									->where('Work_Schedule = ?','Shift Roster')
									->where('EmployeeType_Status = ?','Active')
									->order('Employee_Type ASC'));
    }
    
	//to list EmpTypes in employee import inline edit
    public function getEmpTypes()
    {
    	return $this->_db->fetchAll($this->_db ->select()->from(array('emp'=>$this->_ehrTables->empType),
    																array('value'=> 'emp.Employee_Type','text'=>'emp.Employee_Type'))
									->where('EmployeeType_Status = ?','Active')
									->order('Employee_Type ASC'));
    }

	public function __destruct()
    {
        
    }
}

