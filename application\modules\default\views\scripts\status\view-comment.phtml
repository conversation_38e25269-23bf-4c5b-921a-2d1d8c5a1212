<div style="padding-left: 50px; padding-right: 50px; padding-top: 20px;">
	<!-- <table id="status_tab" class="easyui-datagrid" style="width:500px;height:auto;"
				url="<?php echo $this->url(array('module'=>'default','controller'=>'status', 'action'=>'show-comment'));?>"
				  fitColumns="true" sortName='Added_On' striped='true' rownumbers='true'
					sortOrder='asc' idField='Added_On' singleSelect="true">
			<thead>
				<tr>
					<th field="Employee_Name" width="80" sortable="true">Employee&nbsp;Name</th>
					<th field="Comment" width="80" sortable="true" formatter="format_comment">Comment</th>
				   
					<th field="Added_On" width="80" sortable="true">Date/Time</th>
				</tr>
			 </thead>
	 </table>-->
	
	 <?php if(count($this->comment)>0)
	 {?>
		 <table class='showComment'>
		 <tr><th>Employee Name</th><th>Comment</th>
		 <?php
		    $comment = $this->comment;
		 	if(!empty($comment[0]['Approval_Status']))
		 	{?>
		 	
		 	<th>Status</th>
		 	
		 <?php } ?><th>Added On</th></tr>
		 <?php foreach ($this->comment as $comment)
		 {?>
		 	<tr><td><div style='text-align: center;display:block;'><?php echo $comment['Employee_Name']?></div> </td>
		 	<td><div style='display:block;'><?php echo wordwrap(nl2br(html_entity_decode($comment['Emp_Comment'])), 40, '<br/>', TRUE);?></div> </td>
		 	<?php
		 	if(!empty($comment['Approval_Status']))
		 	{?>
		 	<td><div style='text-align: center;display:block;'><?php echo $comment['Approval_Status']?></div></td>
       <?php } ?>
		 	<td><div style='text-align: center;display:block;'><?php echo $comment['Added_On']?></div> </td></tr>
		 <?php }?>
		 </table>
	<?php }?>
 </div>
