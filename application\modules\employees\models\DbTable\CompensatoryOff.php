<?php

class Employees_Model_DbTable_CompensatoryOff extends Zend_Db_Table_Abstract
{
    protected $_db        = null;
    protected $_ehrTables = null;
	protected $_orgDF     = null;
	protected $_dbOrgDetail = null;
	protected $_dbFinancialYr = null;
    protected $_finYear       = null;
    protected $_orgDetails  = null;
	
    public function init()
    {
        $this->_ehrTables = new Application_Model_DbTable_Ehr();
        $this->_dbCommonFun = new Application_Model_DbTable_CommonFunction();
		$this->_dbOrgDetail = new Organization_Model_DbTable_OrgSettings();
		$this->_dbEmployee = new Employees_Model_DbTable_Employee();
        $this->_dbLeave = new Employees_Model_DbTable_Leave();
        $this->_dbAttendance = new Employees_Model_DbTable_Attendance();
        $this->_dbPayslip = new Payroll_Model_DbTable_Payslip();
        $this->_dbComment = new Payroll_Model_DbTable_PayrollComment();
        
        $this->_db        = Zend_Registry::get('subHrapp');
		$this->_orgDF     = $this->_ehrTables->orgDateformat('php');
		$this->_orgDFDate     = $this->_ehrTables->orgDateformat('php');
        if (Zend_Registry::isRegistered('orgDetails'))
			$this->_orgDetails = Zend_Registry::get('orgDetails');		
    }

    public function listCompensatoryOff($page, $rows, $sortField, $sortOrder, $sessionId, $searchAll,$compOffArr,$compOffUser)
    {
        switch ($sortField)
        {
            case 1: $sortField = 'EJ.User_Defined_EmpId'; break;
            case 2: $sortField = 'P.Emp_First_Name'; break;
            case 3: $sortField = 'CB.Worked_Date'; break;
            case 4: $sortField = 'C.Duration'; break;
            case 5: $sortField = 'C.Compensatory_Date'; break;
            case 6: $sortField = 'C.Approval_Status'; break;
			default:
				$sortField = 'C.Compensatory_Date'; $sortOrder = 'DESC'; break;
        }
    
    	$qryCompOff = $this->_db->select()->distinct()
                                ->from(array('C'=>$this->_ehrTables->compOff),
                                       array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS Compensatory_Off_Id as count'),
                                             'C.Compensatory_Off_Id','C.Employee_Id','C.Approver_Id','C.Comp_Off_Balance_Id',
                                             'C.Compensatory_Date','C.Approval_Status','C.Process_Instance_Id',
                                             'C.Duration as Duration_Id','C.Period', 'C.Compensatory_Date as CompOff_Date',
                                             'Duration'=>new zend_Db_Expr('case when C.Duration = 1 then "Full Day" else "Half Day" end'),
                                             new Zend_Db_Expr("DATE_FORMAT(C.Compensatory_Date,'".$this->_orgDF['sql']."') as Compensatory_Date"),
                                             'Log_Id'=>new Zend_Db_Expr("'".$sessionId."'"),
                                             'Role'=>new Zend_Db_Expr("'".$compOffUser['Admin']."'"),
                                             new Zend_Db_Expr("DATE_FORMAT(C.Added_On,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
                                             new Zend_Db_Expr("DATE_FORMAT(C.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On")))
                                ->joinInner(array('CB'=>$this->_ehrTables->compOffBalance),'CB.Comp_Off_Balance_Id=C.Comp_Off_Balance_Id',
                                            array('Compensated_Date'=>new Zend_Db_Expr("DATE_FORMAT(CB.Worked_Date,'".$this->_orgDF['sql']."')")))
                                ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'EJ.Employee_Id=C.Employee_Id',
										   array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN EJ.Employee_Id ELSE EJ.User_Defined_EmpId END')))
                                ->joinInner(array('P'=>$this->_ehrTables->empPersonal),'P.Employee_Id=C.Employee_Id',
                                            array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name, ' ', P.Emp_Last_Name)")))
                                ->joinInner(array('EP'=>$this->_ehrTables->empPersonal),'EP.Employee_Id=C.Approver_Id',
                                            array('Approver_Name'=>new Zend_Db_Expr("CONCAT(EP.Emp_First_Name, ' ', EP.Emp_Last_Name)")))
                                ->joinLeft(array('AB'=>$this->_ehrTables->empPersonal),'AB.Employee_Id=C.Added_By',
										   array('Added_By_Name'=>new Zend_Db_Expr("CONCAT(AB.Emp_First_Name, ' ', AB.Emp_Last_Name)")))
                                ->joinLeft(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=C.Updated_By',
										   array('Updated_By_Name'=>new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name)")))                                
                                ->group('C.Compensatory_Off_Id')
                                ->order("$sortField $sortOrder")
                                ->limit($rows, $page);
                                
        if (empty($compOffUser['Admin']))
        {
            $getEmployeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
												  ->where('Manager_Id = ?', $sessionId));
            
			if ( $compOffUser['Is_Manager'] == 1 && !empty($getEmployeeId))
			{
				if($this->_orgDetails['Immediate_Reportees_View_Only']==0)
				{
					$getEmployeeId = $this->_dbCommonFun->getMultiLevelManagerIds($sessionId,1);
					array_push($getEmployeeId,$sessionId);
					$qryCompOff->where('C.Employee_Id IN (?)', $getEmployeeId);
				}
				else
				{
                    $qryCompOff->where('C.Employee_Id = :EmpId or C.Approver_Id = :EmpId or C.Employee_Id IN (?)', $getEmployeeId)
                               ->bind(array('EmpId'=>$sessionId));
                }
            }
            else
			{
                $qryCompOff->where('C.Employee_Id = ?', $sessionId);
            }
        }
            
            
        /**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
            $conditions = $this->_db->quoteInto(new Zend_Db_Expr('Concat(P.Emp_First_Name," ",P.Emp_Last_Name) Like ?'),"%$searchAll%");
			$conditions .= $this->_db->quoteInto('or C.Employee_Id Like ?', "%$searchAll%");
            $conditions .= $this->_db->quoteInto('or CB.Worked_Date Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or C.Duration Like ?', "%$searchAll%");
            $conditions .= $this->_db->quoteInto('or C.Compensatory_Date Like ?', "%$searchAll%");
            $conditions .= $this->_db->quoteInto('or C.Approval_Status Like ?', "%$searchAll%");
			
			$qryCompOff->where($conditions);
		}
		
        /* Filter for Employee Name */
		if ( !empty($compOffArr['EmployeeName']) /*&& preg_match('/^[a-zA-Z]+$/', $searchDetails['Employee_Name'])*/ )
		{
			$employeeName = $compOffArr['EmployeeName'];	
			$qryCompOff->where($this->_db->quoteInto(new Zend_Db_Expr('Concat(P.Emp_First_Name," ",P.Emp_Last_Name) Like ?'),"%$employeeName%"));
		}
        
        if (!empty($compOffArr['CompensatedDateStart']))
        {
            //$qryEmployee->where('job.Department_Id = ?', $department);
            $qryCompOff->where($this->_db->quoteInto('CB.Worked_Date >= ?', $compOffArr['CompensatedDateStart']));
        }
		
        if (!empty($compOffArr['CompensatedDateEnd']))
        {
            $qryCompOff->where($this->_db->quoteInto('CB.Worked_Date <= ?', $compOffArr['CompensatedDateEnd']));
        }

        if (!empty($compOffArr['Duration']))
        {
            if($compOffArr['Duration'] == 0)
            {
               $qryCompOff->where('C.Duration IN (?)',array('0.5','1')); 
            }
            else{
                $qryCompOff->where('C.Duration = ?',$compOffArr['Duration']);
            }
        }

		if (!empty($compOffArr['CompOffDateStart']))
        {
            //$qryEmployee->where('job.Department_Id = ?', $department);
            $qryCompOff->where($this->_db->quoteInto('C.Compensatory_Date >= ?', $compOffArr['CompOffDateStart']));
        }
		
        if (!empty($compOffArr['CompOffDateEnd']))
        {
            $qryCompOff->where($this->_db->quoteInto('C.Compensatory_Date <= ?', $compOffArr['CompOffDateEnd']));
        }

        if (!empty($compOffArr['Location_Id']))
        {
            $qryCompOff->where('EJ.Location_Id = ?',$compOffArr['Location_Id']);
        }

        if (!empty($compOffArr['Department_Id']))
        {
            $qryCompOff->where('EJ.Department_Id = ?',$compOffArr['Department_Id']);
        }

        if (!empty($compOffArr['Manager_Id']))
        {
            $qryCompOff->where('C.Approver_Id = ?',$compOffArr['Manager_Id']);
        }
                
        if (!empty($compOffArr['Status']))
        {
            $qryCompOff->where('C.Approval_Status = ?', $compOffArr['Status']);
        } 
        
        if(!empty($compOffArr['serviceProviderId'])&& $this->_orgDetails['Field_Force']==1)
        {
            $qryCompOff->where('EJ.Service_Provider_Id = ?',$compOffArr['serviceProviderId']);
        }

		$qryCompOff = $this->_dbCommonFun->getDivisionDetails($qryCompOff,'EJ.Department_Id');

		if(!empty($compOffUser['Admin']))
		{
			$qryCompOff = $this->_dbCommonFun->formServiceProviderQuery($qryCompOff,'EJ.Service_Provider_Id',$sessionId);
		}

        $compOff = $this->_db->fetchAll($qryCompOff);        
        
        /* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
        /**
		 *	Add comments count for each leave record
		*/
		foreach ($compOff as $key => $row)
		{
            $compOff[$key]['Comment_Cnt'] = $this->_dbComment->countComment($row['Compensatory_Off_Id'], $compOffUser['Form_Name']);
        }
        
        
		/* Total data set length */
		
        $qryTotal = $this->_db->select()
								->from($this->_ehrTables->compOff, new Zend_Db_Expr('COUNT(Compensatory_Off_Id)'));
								
		if (empty($compOffUser['Admin']))
        {
            $getEmployeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
												  ->where('Manager_Id = ?', $sessionId));
            
			if ($compOffUser['Is_Manager'] == 1 && !empty($getEmployeeId))
			{
				if($this->_orgDetails['Immediate_Reportees_View_Only']==0)
				{
					$getEmployeeId = $this->_dbCommonFun->getMultiLevelManagerIds($sessionId, 1);
					array_push($getEmployeeId,$sessionId);
					$qryTotal->where('Employee_Id IN (?)', $getEmployeeId);
				}
				else
				{
					$qryTotal->where('Employee_Id = :EmpId or Approver_Id = :EmpId or Employee_Id IN (?)', $getEmployeeId)
																						->bind(array('EmpId'=>$sessionId));
				}
            }
            else
			{
                $qryTotal->where('Employee_Id = ?', $sessionId);
            }
        }
		$iTotal = $this->_db->fetchOne($qryTotal);
        
        /**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $compOff);
    }


    public function getCompensatedDate($empId)
    {
        $qryCompOffBal = $this->_db->fetchAll($this->_db->select()->distinct()
                                                        ->from(array('C'=>$this->_ehrTables->compOffBalance),
                                                               array('C.Comp_Off_Balance_Id','C.Worked_Date','C.Total_Days','C.Remaining_Days',
                                                                     new Zend_Db_Expr("DATE_FORMAT(C.Worked_Date,'".$this->_orgDF['sql']."') as Compensated_Date")))
                                                        ->where('C.Employee_Id =?',$empId)
                                                        ->where('C.Remaining_Days > 0'));
        return $qryCompOffBal;
    }
    
    public function updateCompensatoryOff($compOffData, $sessionId, $compOffId,$comment, $customFormName)
    {
        $qryCheckBalanceExist = $this->_db->fetchOne ($this->_db->select()
										->from($this->_ehrTables->compOffBalance, array('Comp_Off_Balance_Id'))
										->where('Comp_Off_Balance_Id = ?', $compOffData['Comp_Off_Balance_Id'])
                                         ->where('Remaining_Days > 0'));
        
        if($qryCheckBalanceExist)
        {
            /**
             *	Check compensatory off already exist or not
            */
           $qryCheckExist = $this->_db->select()
                                           ->from($this->_ehrTables->compOff, new Zend_Db_Expr('Count(Compensatory_Off_Id)'))
                                           ->where('Employee_Id = ?', $compOffData['Employee_Id'])
                                           ->where('Period = ?', $compOffData['Period'])
                                           ->where('Compensatory_Date = ?', $compOffData['Compensatory_Date'])
                                           ->where('Approval_Status NOT IN (?)', array('Rejected','Cancelled'));
           
           if (!empty($compOffData['Compensatory_Off_Id']))
           {
               $qryCheckExist->where('Compensatory_Off_Id != ?', $compOffId);
           }
           
           $checkExists = $this->_db->fetchOne ($qryCheckExist);
            
            if (!$checkExists )
            {
                
                $newWorkFlowInitiatedId = "";
                $enableworkflow =  $this->_db->fetchOne($this->_db->select()->from(array('CS'=>$this->_ehrTables->compOffSettings),array('Enable_Workflow')));
                $enableworkflow = $enableworkflow && strtolower($enableworkflow) == "yes";

                if($enableworkflow){
                    $workflowResult = $this->initiateCompOffWorkflow($compOffData, $sessionId, $compOffId);
                    if (!$workflowResult['success']) {
                        return $workflowResult;
                    } else {
                        $newWorkFlowInitiatedId = $workflowResult['newWorkFlowInitiatedId'];
                    }
                }
                
                        /**
                *	Check Comp Off id is greater than zero for run update process
               */
                if ($compOffId > 0)
                {
                    $action = 'Edit';
                    
                    $compOffData['Lock_Flag'] = 0;
                    $compOffData['Updated_By'] = $sessionId;
                    $compOffData['Updated_On'] = date('Y-m-d H:i:s');
                    
                    $updated = $this->_db->update($this->_ehrTables->compOff, $compOffData, array('Compensatory_Off_Id = ?'=>$compOffId));
                    $updated = 1;

                    if($enableworkflow && $newWorkFlowInitiatedId){
                       
                        $OldProcessInstanceId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->compOff, array('Process_Instance_Id'))
                                ->where('Compensatory_Off_Id = ?',$compOffId));
                        if(!empty($OldProcessInstanceId))
                            $this->deleteWorkflowDetails($OldProcessInstanceId);
                    }
                }
               /**
                *	If Comp Off id is empty then we process insertion
               */
                else
                {
                   $action = 'Add';
                   
                   $compOffData['Added_By'] = $sessionId;
                   $compOffData['Added_On'] = date('Y-m-d H:i:s');
                   
                   $updated = $this->_db->insert($this->_ehrTables->compOff, $compOffData);
                   
                   if ($updated)
                       $compOffId = $this->_db->lastInsertId();                       
                }

                if ($updated)
                {
                    if($enableworkflow && $newWorkFlowInitiatedId){
                        $newProcessInstanceIdUpdated = $this->_db->update($this->_ehrTables->compOff, array('Process_Instance_Id'=>$newWorkFlowInitiatedId), array('Compensatory_Off_Id = '. $compOffId));
                    }
                    $attendanceSummarySelectedDetails=[];
                    $attendanceSummarySelectedDetails[]=$compOffData;
                    $this->_dbCommonFun->triggerAttendanceSummaryStepFunction($attendanceSummarySelectedDetails,'compoff');
                    

                    if(!empty($comment)){
                        $this->_db->insert($this->_ehrTables->comment, array('Form_Id'         => $this->_dbComment->getFormId($customFormName),
                                                                            'Emp_Comment'     => $comment,
                                                                            'Parent_Id'       => $compOffId,
                                                                            'Approval_Status' => "Applied",
                                                                            'Employee_Id'     => $sessionId,
                                                                            'Added_On'        => date('Y-m-d H:i:s')));
                    }
                }
   
               /**
                *	this function will handle
                *		update system log function
                *		clear submit lock fucntion
                *		return success/failure array
               */
               return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
                                                               'action'         => $action,
                                                               'trackingColumn' => $compOffId,
                                                               'formName'       => $customFormName,
                                                               'sessionId'      => $sessionId,
                                                               'tableName'      => $this->_ehrTables->compOff));
            }
            else
            {
                return array('success' => false, 'msg' => 'You already applied compensatory off for the selected date', 'type' => 'info');
            }
           
        }
        else
        {
            return array('success' => false, 'msg' => "The Compensatory Off balance created for the worked date has been fully utilized and no days remain.", 'type' => 'info');
        }
    }

    public function initiateCompOffWorkflow($compOffData, $sessionId, $compOffId){
        $eventId = $this->_db->fetchOne(
        $this->_db->select()
            ->from(array('W' => $this->_ehrTables->workflows), array('W.Event_Id'))
            ->join(
                array('WM' => $this->_ehrTables->workflowModule),
                'W.Workflow_Module_Id = WM.Workflow_Module_Id',
                array() // No extra columns needed from workflowModule
            )
            ->where('W.Default_Workflow = ?', 1)
            ->where('WM.Form_Id = ?', 334)
        );

        if(empty($eventId)){
            return array('success' => false, 'msg' => 'The default workflow configuration does not exists', 'type' => 'warning', 'type' => 'warning');
        }
        
        $getWorkedDate = $this->_db->fetchOne($this->_db->select()
                    ->from($this->_ehrTables->compOffBalance, array('Worked_Date'))
                    ->where('Comp_Off_Balance_Id = ?',$compOffData['Comp_Off_Balance_Id']));
        
        /* Create an object */
        $instanceData = new \stdClass();
        $instanceData->formId = 334;
        $instanceData->employeeId =(int)$compOffData['Employee_Id'];
        $instanceData->Employee_Id =(int)$compOffData['Employee_Id'];
        $instanceData->initiatorId =(int)$sessionId;
        $instanceData->Reason=!empty($compOffData['Reason']) ? $compOffData['Reason'] : "" ;
        $instanceData->Duration=$compOffData['Duration'] == 1 ? 'Full Day' : 'Half Day';
        $instanceData->Period=$compOffData['Period'];
        $instanceData->Worked_Date=$getWorkedDate ? $getWorkedDate : "";
        $instanceData->Compensatory_Date=$compOffData['Compensatory_Date'];
        $instanceData->Approval_Status=$compOffData['Approval_Status'];
        $instanceData->Document_Names=empty($compOffData['Document_Names']) ? "" : $compOffData['Document_Names'];
        
        if($compOffId > 0){
            if (!array_key_exists('Added_On', $compOffData) || empty($compOffData['Added_On'])) {
                $compOffDetails = $this->_db->fetchRow(
                    $this->_db->select()
                        ->from(array('C' => $this->_ehrTables->compOff), array('*'))
                        ->where('Compensatory_Off_Id = ?', $compOffData['Compensatory_Off_Id'])
                );
                
                $instanceData->Added_On = $compOffDetails['Added_On'] ?? null;
                $instanceData->Added_By = $compOffDetails['Added_By'] ?? null;
            } else {            
                $instanceData->Added_On = $compOffData['Added_On'];
                $instanceData->Added_By = $compOffData['Added_By'];
            }
            
            $instanceData->Updated_On=date('Y-m-d H:i:s');
            $instanceData->Updated_By=$sessionId;
        } else {
            $instanceData->Added_On=date('Y-m-d H:i:s');
            $instanceData->Added_By=$sessionId;
        }

        $dbLeave = new Employees_Model_DbTable_Leave();
        $newWorkFlowInitiatedId=$dbLeave->initiateWorkflowEngine($eventId,$instanceData,$sessionId);  
        
        if(empty($newWorkFlowInitiatedId)){
            return array('success' => false, 'msg' => 'Sorry, an error occured while initiating the workflow', 'type' => 'warning');
        }
        return array('success' => true, 'msg' => 'Workflow initiated successfully.', 'type' => 'success', 'newWorkFlowInitiatedId'=>$newWorkFlowInitiatedId);

    }

    public function deleteWorkflowDetails($processInstanceId){
        $whereProcessInitiatedId['process_instance_id IN (?)'] = $processInstanceId;
		$this->_db->delete($this->_ehrTables->taUserTask,$whereProcessInitiatedId);
		$this->_db->delete($this->_ehrTables->taUserTaskHistory,$whereProcessInitiatedId);
		$this->_db->delete($this->_ehrTables->taProcessInstance,$whereProcessInitiatedId);
		$this->_db->delete($this->_ehrTables->taProcessInstanceHistory,$whereProcessInitiatedId);
		return 1;
	}
    
    public function compOffValidate($employeeId,$compOffDate,$period)
    {
        $workScheduleDetails = $this->_dbAttendance->getGraceTimeDetails($employeeId,$compOffDate);
        if(!empty($workScheduleDetails))
        {
            $checkShiftExist = $this->_dbAttendance->checkShiftEnabled($employeeId); 
	        if($checkShiftExist=='Shift Roster')
            {
                $businessWorkingDays = $this->_dbPayslip->getBusinessWorkingDays($compOffDate,$compOffDate, $employeeId, null, 1,'leaves');
            }
            else 
            {
                $businessWorkingDays = $this->_dbPayslip->getBusinessWorkingDays($compOffDate,$compOffDate, $employeeId, null, 1);
            }

            if($businessWorkingDays > 0)
            {
                $fullDayLeaveExist = $firstHalfLeaveExist = $secondHalfLeaveExist = $firstQuarterLeaveExist = $secondQuarterLeaveExist = $thirdQuarterLeaveExist = $fourthQuarterLeaveExist = 0;

                $dbHRReport          = new Reports_Model_DbTable_HrReports();
                $allLeaveDetails = $dbHRReport-> getLeaveDuration($employeeId,$compOffDate,NULL,NULL,'dashboardNoAttendance',NULL,'fetchAll');

                if (!empty($allLeaveDetails) && count($allLeaveDetails) > 0) {
                    foreach ($allLeaveDetails as $leaveDetail) {
                        if ($leaveDetail['Duration'] == 1) {
                            $fullDayLeaveExist = 1;
                        } else if ($leaveDetail['Duration'] == 0.5 && $leaveDetail['Leave_Period'] == 'First Half') {
                            $firstHalfLeaveExist = 1;
                        } else if ($leaveDetail['Duration'] == 0.5 && $leaveDetail['Leave_Period'] == 'Second Half') {
                            $secondHalfLeaveExist = 1;
                        } else if ($leaveDetail['Duration'] == 0.25 && $leaveDetail['Leave_Period'] == 'First Quarter') {
                            $firstQuarterLeaveExist = 1;
                        } else if ($leaveDetail['Duration'] == 0.25 && $leaveDetail['Leave_Period'] == 'Second Quarter') {
                            $secondQuarterLeaveExist = 1;
                        } else if ($leaveDetail['Duration'] == 0.25 && $leaveDetail['Leave_Period'] == 'Third Quarter') {
                            $thirdQuarterLeaveExist = 1;
                        } else if ($leaveDetail['Duration'] == 0.25 && $leaveDetail['Leave_Period'] == 'Fourth Quarter') {
                            $fourthQuarterLeaveExist = 1;
                        }
                    }
                }
               
                //If the comp off period is First Half then validate that the leave should not exist for first half or first quarter or second quarter or full day for the same date
                if($period == 'First Half' && (!empty($firstHalfLeaveExist) || !empty($firstQuarterLeaveExist) || !empty($secondQuarterLeaveExist) || !empty($fullDayLeaveExist)))
                {
                    return array('success' => false, 'msg' => 'Leave is added for this period', 'type' => 'info');
                }
                //If the comp off period is Second Half then validate that the leave should not exist for second half or third quarter or fourth quarter or full day for the same date
                else if($period == 'Second Half' && (!empty($secondHalfLeaveExist) || !empty($thirdQuarterLeaveExist) || !empty($fourthQuarterLeaveExist) || !empty($fullDayLeaveExist)))
                {
                    return array('success' => false, 'msg' => 'Leave is added for this period', 'type' => 'info');
                }
                elseif(empty($period))
                {
                    //If the comp off applied for full day then validate that the leave should not exist for any leave period for the same date
                    if(!empty($firstHalfLeaveExist) || !empty($firstQuarterLeaveExist) || !empty($secondQuarterLeaveExist) 
                    || !empty($secondHalfLeaveExist) || !empty($thirdQuarterLeaveExist) || !empty($fourthQuarterLeaveExist) 
                    || !empty($fullDayLeaveExist)){
                        return array('success' => false, 'msg' => 'Leave is added for this period', 'type' => 'info');
                    }else{
                        $getAttCount     = $dbHRReport->getAttendanceCount($employeeId,$workScheduleDetails,'compensatoryOff');
                        if($getAttCount>0)
                        {
                            return array('success' => false, 'msg' => 'You have added attendance on this date', 'type' => 'info');
                        }
                        else 
                        {
                        return true;  
                        }
                    }
                }
                else
                {
                    return true;        
                }
                
            }
            else 
            {
                return array('success' => false, 'msg' => 'You have selected the Holiday date.', 'type' => 'info');
            }
        }
        else 
        {
            return array('success' => false, 'msg' => 'Work Schedule/Shift is not scheduled for an employee on this date', 'type' => 'info');
        } 
    }
    
    public function getCompOffHours($empId, $compOffBalanceId,$validationOtherDetails)
    {
        $validationErrorMessage = '';
        extract($validationOtherDetails, EXTR_SKIP);

        $getCompOffHrs = $this->_db->fetchRow($this->_db->select()
                                      ->from($this->_ehrTables->compOffBalance, array('Remaining_Days','Expiry_Date','Total_Days','Worked_Date'))
                                      ->where('Comp_Off_Balance_Id = ?',$compOffBalanceId));
        
        $resignationDate = $this->_dbCommonFun->getEmployeeResignationDate($empId);
        
        // get holiday special wage configuration based on employeeId and workedDate
        $splWageConfiguration = $this->_dbPayslip->getSpecialWageConfiguration($empId, $getCompOffHrs['Worked_Date'],'CompoffUpdate');
        // in default comp off min date should be workedDate + 1
        $compOffMinDate = date('Y-m-d', strtotime("+1 day",strtotime($getCompOffHrs['Worked_Date'])));
        // when the special wage comp off expiry type as 'same payroll month', then we need to set min date as salary date of the month
        if(!empty($splWageConfiguration)) {
            if($splWageConfiguration["Comp_Off_Expiry_Type"] === "Same Payroll Month") {
                $salaryDateDetails  	=
                    $this->_dbPayslip->getSalaryDateRange(
                        date('m',strtotime($getCompOffHrs['Worked_Date'])), 
                        date('Y',strtotime($getCompOffHrs['Worked_Date'])),
                        strtotime($getCompOffHrs['Worked_Date'])
                    );
			    $compOffMinDate  	    	= $salaryDateDetails['Salary_Date'];
            }
        }
        $compOffExpiryDate = $getCompOffHrs['Expiry_Date'];
        if (!empty($resignationDate)) {
            $compOffMaxDate = date('Y-m-d', min(strtotime($compOffExpiryDate), strtotime($resignationDate)));
        } else {
            $compOffMaxDate = $compOffExpiryDate;
        }
        $compOffSettings = $this->getCompOffSettings();
        $advanceNotificationDetails = $this->_dbCommonFun->getAdvanceNotificationMinDate($compOffSettings,$adminRole,$sessionId,$compOffFormIds);
        if(!empty($advanceNotificationDetails['minimumDateToApply'])){
            //If the advance notification date is greater than the minimum date then advance notification date should be set as min date
            if(strtotime($advanceNotificationDetails['minimumDateToApply']) > strtotime($compOffMinDate)){
                $compOffMinDate = $advanceNotificationDetails['minimumDateToApply'];
            }
        }

        //when the payslip is generated we need to get the maximum date of the month after that only we can allow the employee to add compoff
        $maxPayslipMonthDetails = $this->_dbCommonFun->getMaxPayslipMonthByEmployeeId($empId);
        if(!empty($maxPayslipMonthDetails))
        {
            $maxPayslipMonthByEmployeeId 		= $this->_dbCommonFun->ensureArray($maxPayslipMonthDetails, $empId);
            if(!empty($maxPayslipMonthByEmployeeId))
            {
                $maxPayslipMonth 					= $maxPayslipMonthByEmployeeId[0]['Salary_Month'];     
                if(!empty($maxPayslipMonth))
                {
                    $maxPayslipMonth = explode('-',$maxPayslipMonth);
                    $lastSalaryDay = $this->_dbPayslip->getSalaryDay($maxPayslipMonth[1],$maxPayslipMonth[0], 31);
                    
                    $lastPayslipMonth = date('Y-m-d', strtotime($lastSalaryDay['Last_SalaryDate']));
                    /** Checked that last generated payslip month should be greater than or equal to the leave start date. If it is zero, leave record
                    will be allowed to delete **/
                    if( $lastPayslipMonth > $compOffMinDate )
                    {
                        $compOffMinDate = date('Y-m-d',strtotime($lastSalaryDay['Last_SalaryDate'] ." +1days"));
                    }
                }
            }
        }
        
        if(strtotime($compOffMinDate) > strtotime($compOffMaxDate)){
            $formattedMinDate = date('d M Y', strtotime($compOffMinDate)); // e.g. 10 Jan 2025
            $formattedMaxDate = date('d M Y', strtotime($compOffMaxDate)); // e.g. 08 Jan 2025

            $validationErrorMessage = "The minimum applicable date($formattedMinDate) for Compensatory Off cannot be later than the maximum applicable date($formattedMaxDate).";
        }
        return array('success' =>true,'Comp_Remaining_Days'=>$getCompOffHrs['Remaining_Days'],"resignationDate"=>$resignationDate,'compOff_Max_Date'=>$compOffMaxDate,
                     'Expiry_Date'=>$compOffExpiryDate, 'Compoff_Min_Date' => $compOffMinDate, 'Validation_Error' =>$validationErrorMessage);

    }

    public function getCompOffBalance($compensatoryOffId,$compOffBalanceId)
    {
        $compOffStatus = array('Applied','Returned','Approved','Cancel Applied');
        
        $totalCompOffDaysQry = $this->_db->select()->from($this->_ehrTables->compOff,array('Sum_Duration' =>new Zend_Db_Expr('SUM(Duration)')))
                                                        ->where('Approval_Status IN (?)',$compOffStatus)
                                                        ->where('Comp_Off_Balance_Id = ?', $compOffBalanceId);
        if(!empty($compensatoryOffId))
        {
            $totalCompOffDaysQry->where('Compensatory_Off_Id != ?', $compensatoryOffId);
        }                                                
        
        $totalCompOffDays = $this->_db->fetchOne($totalCompOffDaysQry);

        $remainingDays =  $this->_db->fetchOne($this->_db->select()->from(array('CB'=>$this->_ehrTables->compOffBalance),array('Total_Days'))
                                                                        ->where('CB.Comp_Off_Balance_Id = ?', $compOffBalanceId));

        $totalCompOffBalance =  $remainingDays - $totalCompOffDays;                                                               
              
        return $totalCompOffBalance;                                                                
    }
    
    /** Update Comp Off Status **/
    public function compOffStatusUpdate ($compOffData, $sessionId, $formName, $customFormName)
    {
        $compOffDetails =  $this->_db->fetchRow($this->_db->select()->from(array('C'=>$this->_ehrTables->compOff),array('*'))->where('Compensatory_Off_Id = ?', $compOffData['Compensatory_Off_Id']));
        $payslipExistForCompOffDate = $this->payslipExistForCompOffDate($compOffDetails['Employee_Id'],$compOffDetails['Compensatory_Date']);
        if($payslipExistForCompOffDate==1)
        {
            return array('success' => false, 'msg' => "Unable to proceed with status update. An existing payslip is found for the selected duration", 'type' => 'warning');
        }
        else
        {
            $compOffStatus = array('Approval_Status'=>$compOffData['Status']);

            $enableworkflow =  $this->_db->fetchOne($this->_db->select()->from(array('CS'=>$this->_ehrTables->compOffSettings),array('Enable_Workflow')));
            $enableworkflow = $enableworkflow && strtolower($enableworkflow) == "yes";

            if($enableworkflow && strtolower($compOffData['Status']) == "cancel applied") {
                $compOffDetails['Approval_Status'] = $compOffData['Status'];
                $workflowResult = $this->initiateCompOffWorkflow($compOffDetails, $sessionId, $compOffData['Compensatory_Off_Id']);
                if (!$workflowResult['success']) {
                    return $workflowResult;
                } else {
                    $newWorkFlowInitiatedId = $workflowResult['newWorkFlowInitiatedId'];
                    $this->_db->update($this->_ehrTables->compOff, array('Process_Instance_Id'=>$newWorkFlowInitiatedId), array('Compensatory_Off_Id = '. $compOffData['Compensatory_Off_Id']));
                }
            }
            
            /** Update Status **/
            $updated    = $this->_db->update($this->_ehrTables->compOff, $compOffStatus, 'Compensatory_Off_Id = ' . $compOffData['Compensatory_Off_Id']);
            
            /** If comments not empty, comments insertion **/
            if(!empty($compOffData['Comment']))
            {
                $dbComment    = new Payroll_Model_DbTable_PayrollComment();
                $formId       = $dbComment->getFormId($formName);
                $insertStatus = $this->_db->insert($this->_ehrTables->comment,array('Form_Id'=>$formId, 'Emp_Comment'=>$compOffData['Comment'],
                        'Approval_Status'=>$compOffData['Status'], 'Parent_Id'=>$compOffData['Compensatory_Off_Id'], 'Employee_Id'=>$sessionId, 'Added_On'=>date('Y-m-d H:i:s')));
            }
            
            /*when the compensatory off record status is chosen as cancel rejected in that time we are updating the value as approved.when cancel rejection 
            happened we should not update the compoff balance record */
            if($updated && (($compOffData['Status'] == 'Approved' && $compOffData['Status_Text']=='Approved') || ($compOffData['Status'] == 'Cancelled')))
            {
                $getCompBalance =  $this->_db->fetchRow($this->_db->select()->from(array('C'=>$this->_ehrTables->compOff),array('Duration'))
                                        ->joinInner(array('CB'=>$this->_ehrTables->compOffBalance),'C.Comp_Off_Balance_Id=CB.Comp_Off_Balance_Id',
                                                array('Comp_Off_Balance_Id','Total_Days','Remaining_Days'))
                                        ->where('C.Compensatory_Off_Id = ?', $compOffData['Compensatory_Off_Id']));
            
                if($compOffData['Status'] == 'Approved')
                {
                    $remainingDays = $getCompBalance['Remaining_Days'] - $getCompBalance['Duration'];
                }
                else if($compOffData['Status'] == 'Cancelled')
                {
                    $remainingDays = $getCompBalance['Remaining_Days'] + $getCompBalance['Duration'];
                }

                $updated    = $this->_db->update($this->_ehrTables->compOffBalance, array('Remaining_Days'=>$remainingDays), 'Comp_Off_Balance_Id = ' . $getCompBalance['Comp_Off_Balance_Id']);
                $updated    = 1;
            }
            
            /**
             *	this function will handle
            *		update system log function
            *		clear submit lock fucntion
            *		return success/failure array
            */
            return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
                                                            'action'         => 'Edit',
                                                            'trackingColumn' => $compOffData['Compensatory_Off_Id'],
                                                            'formName'       => 'Compensatory Off',
                                                            'sessionId'      => $sessionId,
                                                            'tableName'      => $this->_ehrTables->compOff));
        }
            
    }
    
    
    public function deleteCompOff ($compOffId, $logId, $formName, $customFormName)
    {
        if (!empty($compOffId))
        {
            $compOffRow = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->compOff, array('*'))
																	->where('Compensatory_Off_Id = ?', $compOffId));
            $deleted = 0;
           
            $payslipExist = $this->payslipExistForCompOffDate($compOffRow['Employee_Id'],$compOffRow['Compensatory_Date']);
            if(!empty($payslipExist))
            {
                return array('success' => false, 'msg' => 'Payslip already generated for the compensatory off date', 'type' => "info");
            }
            else
            {
                if($compOffRow['Lock_Flag'] == 0 && ($compOffRow['Approval_Status'] == 'Applied' || $compOffRow['Approval_Status'] == 'Rejected' || $compOffRow['Approval_Status'] == 'Cancelled' || $compOffRow['Approval_Status'] == 'Returned'))
                {
                    //$deleted= 1;
                    $compOffRow['Deleted_On'] = date('Y-m-d H:i:s');
                    $compOffRow['Deleted_By'] = $logId;
                    $inserted = $this->_db->insert($this->_ehrTables->archiveCompOff, $compOffRow);
                    $deleted = $this->_db->delete($this->_ehrTables->compOff, 'Compensatory_Off_Id='.(int)$compOffId);
                }
                
                if ($deleted)
                {
                    $this->_dbComment->deleteComment($compOffId, $formName);				
                    $attendanceSummarySelectedDetails=[];
                    $attendanceSummarySelectedDetails[]=$compOffRow;
                    $this->_dbCommonFun->triggerAttendanceSummaryStepFunction($attendanceSummarySelectedDetails,'compoff');

                    if(!empty($compOffRow['Process_Instance_Id']))            
                        $this->deleteWorkflowDetails($compOffRow['Process_Instance_Id']);
                }
                            
                return $this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
                                                                'tableName'      => $this->_ehrTables->compOff,
                                                                'lockFlag'       => $compOffRow['Lock_Flag'],
                                                                'formName'       => $customFormName,
                                                                'trackingColumn' => $compOffId,
                                                                'sessionId'      => $logId));
            }
		}
        else
        {
            return array('failure'=>'Unable to delete '.$customFormName.'. Please, contact system admin.');
        }
    }
    
    /**
	 *	to fetch comp-off request record based on compoff request id
	*/
    public function viewCompoffRequest ($compOffId)
    {
		return $this->_db->fetchRow($this->_db->select()
									->from(array('C'=>$this->_ehrTables->compOff),
										   array('C.Compensatory_Off_Id', 'C.Employee_Id', 'C.Approver_Id', 'C.Added_By', 
                                           'C.Comp_Off_Balance_Id', 'C.Duration', 'C.Compensatory_Date', 'C.Approval_Status'))
									
									->joinInner(array('E'=>$this->_ehrTables->empPersonal), 'E.Employee_Id=C.Employee_Id',
												array(new Zend_Db_Expr("CONCAT(E.Emp_First_Name, ' ', E.Emp_Last_Name) as Employee_Name")))
									
									->where('C.Compensatory_Off_Id = ?', $compOffId));
    }
    
/************************************* Compensatory Off Balance ********************************************/    
    public function listCompensatoryOffBalance($page, $rows, $sortField, $sortOrder, $sessionId, $searchAll,$compOffBalArr,$compOffBalUser)
    {
        switch ($sortField)
        {
            case 1: $sortField = 'C.Employee_Id'; break;
            case 2: $sortField = 'Employee_Name'; break;
            case 3: $sortField = 'C.Worked_Date'; break;
            case 4: $sortField = 'C.Expiry_Date'; break;
            case 5: $sortField = 'C.Total_Days'; break;
            case 6: $sortField = 'C.Remaining_Days'; break;
			default:
				$sortField = 'C.Worked_Date'; $sortOrder = 'desc'; break;
        }

        $qryCompOffBal = $this->_db->select()->distinct()
                                ->from(array('C'=>$this->_ehrTables->compOffBalance),
                                       array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS Comp_Off_Balance_Id as count'),
                                           'C.Comp_Off_Balance_Id','C.Employee_Id','C.Total_Days','C.Remaining_Days','C.Comp_Off_Balance_Rules','C.Worked_Date',
                                            'Total_Hours'=>new Zend_Db_Expr("CASE WHEN ET.Display_Total_Hours_In_Minutes='1' Then ( CONCAT(FLOOR(C.Total_Hours),':', LPAD(ROUND((C.Total_Hours - FLOOR(C.Total_Hours)) * 60) % 60,2,0)) ) else C.Total_Hours END  "),
                                            new Zend_Db_Expr("DATE_FORMAT(C.Expiry_Date,'".$this->_orgDF['sql']."') as Expiry_Date"),
                                            new Zend_Db_Expr("DATE_FORMAT(C.Worked_Date,'".$this->_orgDF['sql']."') as Compensatory_Date")))
                                ->joinInner(array('P'=>$this->_ehrTables->empPersonal),'P.Employee_Id=C.Employee_Id',
										   array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name, ' ', P.Emp_Last_Name)")))
                                ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'EJ.Employee_Id=C.Employee_Id',
                                           array('EJ.Location_Id','User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN EJ.Employee_Id ELSE EJ.User_Defined_EmpId END')))
                                ->joinInner(array('ET'=>$this->_ehrTables->empType), 'EJ.EmpType_Id=ET.EmpType_Id', array(''))
                                ->joinInner(array('DN'=>$this->_ehrTables->designation), 'DN.Designation_Id=EJ.Designation_Id', array(''))
                                ->joinInner(array('T'=>$this->_ehrTables->timesheetHrs), 'DN.Grade_Id=T.Grade_Id', array('T.Regular_Hours'))
                                ->order("$sortField $sortOrder")
                                ->limit($rows, $page);

        if (empty($compOffBalUser['Admin']))
        {
            $getEmployeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
												  ->where('Manager_Id = ?', $sessionId));
            
			if ( $compOffBalUser['Is_Manager'] == 1 && !empty($getEmployeeId))
			{
                $qryCompOffBal
                            ->where('P.Employee_Id = :EmpId or P.Employee_Id IN (?)', $getEmployeeId)
							->bind(array('EmpId'=>$sessionId));
	        }
            else
			{
                $qryCompOffBal->where('C.Employee_Id = ?', $sessionId);
            }
        }
        
        
        /**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
            $conditions = $this->_db->quoteInto(new Zend_Db_Expr('Concat(P.Emp_First_Name," ",P.Emp_Last_Name) Like ?'),"%$searchAll%");
			$conditions .= $this->_db->quoteInto('or C.Employee_Id Like ?', "%$searchAll%");
            $conditions .= $this->_db->quoteInto('or C.Worked_Date Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or C.Total_Days Like ?', "%$searchAll%");
            $conditions .= $this->_db->quoteInto('or C.Remaining_Days Like ?', "%$searchAll%");
			
			$qryCompOffBal->where($conditions);
		}
		
        /* Filter for Employee Name */
		if ( ! empty($compOffBalArr['EmployeeName']) /*&& preg_match('/^[a-zA-Z]+$/', $searchDetails['Employee_Name'])*/ )
		{
			$employeeName = $compOffBalArr['EmployeeName'];	
			$qryCompOffBal->where($this->_db->quoteInto(new Zend_Db_Expr('Concat(P.Emp_First_Name," ",P.Emp_Last_Name) Like ?'),"%$employeeName%"));
		}
        
		if (!empty($compOffBalArr['CompOffDateStart']))
        {
            $qryCompOffBal->where($this->_db->quoteInto('C.Worked_Date >= ?', $compOffBalArr['CompOffDateStart']));
        }
		
        if (!empty($compOffBalArr['CompOffDateEnd']))
        {
            $qryCompOffBal->where($this->_db->quoteInto('C.Worked_Date <= ?', $compOffBalArr['CompOffDateEnd']));
        }
    
        if (!empty($compOffBalArr['TotalDaysStart']))
        {
            $qryCompOffBal->where('C.Total_Days >= ?', $compOffBalArr['TotalDaysStart']);
        }
        
        if (!empty($compOffBalArr['TotalDaysEnd']))
        {
            $qryCompOffBal->where('C.Total_Days <= ?', $compOffBalArr['TotalDaysEnd']);
        }
            
		if (!empty($compOffBalArr['RemainingDaysStart']))
        {
            $qryCompOffBal->where('C.Remaining_Days >= ?', $compOffBalArr['RemainingDaysStart']);
        }
        
        if (!empty($compOffBalArr['RemainingDaysEnd']))
        {
            $qryCompOffBal->where('C.Remaining_Days <= ?', $compOffBalArr['RemainingDaysEnd']);
        }

        if (!empty($compOffBalArr['Location_Id']))
        {
            $qryCompOffBal->where('EJ.Location_Id = ?',$compOffBalArr['Location_Id']);
        }

        if (!empty($compOffBalArr['Department_Id']))
        {
            $qryCompOffBal->where('EJ.Department_Id = ?',$compOffBalArr['Department_Id']);
        }
        
        if(!empty($compOffBalArr['serviceProviderId'])&& $this->_orgDetails['Field_Force']==1)
        {
            $qryCompOffBal->where('EJ.Service_Provider_Id = ?',$compOffBalArr['serviceProviderId']);
        }

		$qryCompOffBal = $this->_dbCommonFun->getDivisionDetails($qryCompOffBal,'EJ.Department_Id');

		if(!empty($compOffBalUser['Admin']))
		{
			$qryCompOffBal = $this->_dbCommonFun->formServiceProviderQuery($qryCompOffBal,'EJ.Service_Provider_Id',$sessionId);
		}

        $compOffBal = $this->_db->fetchAll($qryCompOffBal);        

        foreach ($compOffBal as $key => $row)
		{
            if(empty($row['Comp_Off_Balance_Rules']))
            {
                $compOffConfiguration = $this->_dbPayslip->getSpecialWageConfiguration($row['Employee_Id'], $row['Worked_Date'],'CompoffUpdate');
                $compOffConfiguration['Regular_Hours'] = $row['Regular_Hours'];
                $compOffBalanceRules = $this->getCompOffBalanceRules($compOffConfiguration);
                $compOffData['Comp_Off_Balance_Rules'] = $compOffBalanceRules;
                $updated = $this->_db->update($this->_ehrTables->compOffBalance, $compOffData, array('Comp_Off_Balance_Id = ?'=>$row['Comp_Off_Balance_Id']));
            }
        }
        
        /* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		/* Total data set length */
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->compOffBalance, new Zend_Db_Expr('COUNT(Comp_Off_Balance_Id)')));

        /**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $compOffBal);
    }

    public function getCompOffBalanceRules($compOffConfiguration)
    {
        $compOffDetails = array();
        if($compOffConfiguration['Comp_Off']=='Yes')
        {
            if($compOffConfiguration['Work_Day_Type']!='Extra Work Hours(Weekday)')
            {
                if($compOffConfiguration['Comp_Off_Threshold']=='Fixed Hours')
                {
                    $compOffDetails['Full Day Comp Off Hours'] = $compOffConfiguration['Fixed_Regular_Hours'];
                    if($compOffConfiguration['Allow_Half_Day_Comp_Off_Credit']=='Yes')
                    {
                        $compOffDetails['Half Day Comp Off Hours'] = $compOffConfiguration['Minimum_Hours_For_Half_Day_Comp_Off'];
                    }
                    else
                    {
                        $compOffDetails['Half Day Comp Off Hours'] ='Not Applicable';
                    }
                }
                else
                {
                    $compOffDetails['Full Day Comp Off Hours'] = $compOffConfiguration['Regular_Hours'];
                    if($compOffConfiguration['Allow_Half_Day_Comp_Off_Credit']=='Yes')
                    {
                        $compOffDetails['Half Day Comp Off Hours'] = $compOffConfiguration['Regular_Hours']/2;
                    }
                    else
                    {
                        $compOffDetails['Half Day Comp Off Hours'] ='Not Applicable';
                    }
                }
            }

            $compOffApplicability = $compOffConfiguration['Comp_Off_Applicability_For_Overtime_Hours'];

            if (in_array($compOffApplicability, array('Full Day', 'Both Full Day & Half Day'))) {
                $compOffDetails['Minimum Overtime Hours For Full Day Comp Off'] = $compOffConfiguration['Minimum_OT_Hours_For_Full_Day_Comp_Off'];
            }
    
            if (in_array($compOffApplicability, array('Half Day', 'Both Full Day & Half Day'))) {
                $compOffDetails['Minimum Overtime Hours For Half Day Comp Off'] = $compOffConfiguration['Minimum_OT_Hours_For_Half_Day_Comp_Off'];
            }

            if($compOffApplicability=='Not Applicable')
            {
                $compOffDetails['Minimum Overtime Hours For Full Day Comp Off'] ='Not Applicable';
                $compOffDetails['Minimum Overtime Hours For Half Day Comp Off'] ='Not Applicable';
            }
        }
        else
        {
            $compOffDetails['Full Day Comp Off Hours'] ='Not Applicable';
            $compOffDetails['Half Day Comp Off Hours'] ='Not Applicable';
            $compOffDetails['Minimum Overtime Hours For Full Day Comp Off'] ='Not Applicable';
            $compOffDetails['Minimum Overtime Hours For Half Day Comp Off'] ='Not Applicable';
        }
        
        $compOffBalanceRules = json_encode($compOffDetails);
        return $compOffBalanceRules;
    }

    public function getCompOffSettings(){
       return ($this->_db->fetchRow($this->_db->select()->from(array('CS'=>$this->_ehrTables->compOffSettings),array('*'))));

    }

    public function payslipExistForCompOffDate($employeeId,$compOffDate)
	{
		$payslipExist = 0;
        if(!empty($employeeId) && !empty($compOffDate))
        {
            $salaryDateDetails = $this->_dbPayslip->getSalaryDateRange(date('m',strtotime($compOffDate)),date('Y',strtotime($compOffDate)),strtotime($compOffDate), 29);
            if(!empty($salaryDateDetails))
            {
                $salaryMonth = date('n,Y', strtotime($salaryDateDetails['Last_SalaryDate'])); 
                $payslipExist = $this->_dbCommonFun->payslipExist($employeeId,$salaryMonth);
            }
        }
		return $payslipExist;										
	}
    public function __destruct()
    {
        
    }
}

