<?php 
$payslipEmployeeIds = $this->payslipEmployeeIds;
$paycycleStartDate  = $this->paycycleStartDate;
$paycycleEndDate    = $this->paycycleEndDate;
$salaryMonth=$this->salaryMonth;
$salaryYear=$this->salaryYear;
$enableWorkFlow = $this->enableWorkFlow;



echo '<script>localStorage.setItem("paycycleStartDate", "'.$paycycleStartDate.'");</script>';
echo '<script>localStorage.setItem("paycycleEndDate", "'.$paycycleEndDate.'");</script>';
$notificationRedirection='No';
$params = 'salaryMonth='.$salaryMonth.'&salaryYear='.$salaryYear.'&notificationRedirection='.$notificationRedirection;
$encodedParams = base64_encode($params);

$assessmentYear  = $this->assessmentYear;

$taxReliefParams = 'assessmentYear='.$assessmentYear;
$encodedParamsTaxRelief = base64_encode($taxReliefParams);

$payslipEmployeeIdsStr = implode(',',$payslipEmployeeIds);
$salaryDateParams = 'salaryStartDate='.$paycycleStartDate.'&salaryEndDate='.$paycycleEndDate.'&payslipEmployeeIds='.$payslipEmployeeIdsStr;
$encodedSalaryDateParams = base64_encode($salaryDateParams);

if($enableWorkFlow=='Yes')
{
   $leaveUrl = $this->baseUrl('/v3/approvals/approval-management?form_id=31');
}
else
{
  $leaveUrl = $this->baseUrl('/employees/leaves');
}

$oldFormUrlRedirection='No';
if($oldFormUrlRedirection=='Yes')
{
  $noAttendanceFinalizationUrl = $this->baseUrl("/employees/attendance-finalization?pre-req=1&data=$encodedSalaryDateParams");
  $earlyCheckoutUrl            = $this->baseUrl("/v3/my-team/early-checkout?data=$encodedSalaryDateParams");
  $attendanceUrl               = $this->baseUrl("/employees/attendance-finalization?pre-req=1&data=$encodedSalaryDateParams");
}
else
{
  $noAttendanceFinalizationUrl = $this->baseUrl("/v3/my-team/attendance/attendance-finalization?tab=noAttendance&pre-req=1&data=$encodedSalaryDateParams");
  $earlyCheckoutUrl = $this->baseUrl("/v3/my-team/attendance/attendance-finalization?tab=earlyCheckout&pre-req=1&data=$encodedSalaryDateParams");
  $attendanceUrl = $this->baseUrl("/v3/my-team/attendance/attendance-finalization?tab=attendance&pre-req=1&data=$encodedSalaryDateParams");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <link rel="shortcut icon" href="<?php echo $this->baseUrl('/assets/global/images/favicon.png'); ?>" type="image/png">
    <title>Pre-Requisites</title>
    <link href="<?php echo $this->baseUrl('/assets/global/css/style.css?v=4'); ?>" rel="stylesheet">	 
    <link href="<?php echo $this->baseUrl('/assets/global/css/theme.css?v=6'); ?>" rel="stylesheet">
    <link href="<?php echo $this->baseUrl('/assets/global/css/ui.css?v=3'); ?>" rel="stylesheet">
    <link href="<?php echo $this->baseUrl('/assets/md-layout1/material-design/css/material.css'); ?>" rel="stylesheet">
    <link href="<?php echo $this->baseUrl('/assets/md-layout1/css/layout.css?v=1'); ?>" rel="stylesheet">
	 <link href="<?php echo $this->baseUrl('assets/global/css/custom.css?v=18'); ?>" rel="stylesheet"> <!-- CUSTOM CSS -->
    <script src="<?php echo $this->baseUrl('/assets/global/plugins/modernizr/modernizr-2.6.2-respond-1.1.0.min.js'); ?>"></script>
    <script type="text/javascript">
      var payslipEmployeeIds = <?php echo json_encode($payslipEmployeeIds); ?>;
      localStorage.setItem("payslipEmployeeIds", payslipEmployeeIds);
    </script>
  </head>
<body>
<div class="page-content" style="padding: 0px 18px !important;">
<div class="row">
   <div class="col-md-12 col-lg-12 col-sm-12 col-xs-12">
              <h3 class="m-t-30 m-b-20"><strong>Pending Approvals</strong></h3>
              <div class="widget-infobox">
                <a href="<?php echo $this->baseUrl('/payroll/advance-salary');?>" target="_blank">
                  <div class="infobox panel" style="min-width: 250px; min-height: 85px; padding-top: 15px;">
                     <div class="left">
                       <i class="hr-payroll-advance-salary bg-blue"></i>
                     </div>
                     <div class="right">
                       <div>
                         <span class="c-primary pull-left">
                              <?php if($this->advanceSalary != 0)
                              {
                                 echo $this->advanceSalary;
                              }
                              else if($this->advanceSalary == 0)
                              { ?>
                                 <i class="mdi-action-done"></i>
                              <?php }
                              ?>
                           </span>
                        </div>
                       <div class="txt" style="font-size: 15px;">Advance Salary</div>
                     </div>
                  </div>
               </a>
               <a href="<?php echo $this->baseUrl('/payroll/bonus');?>" target="_blank">
                <div class="infobox panel" style="min-width: 250px; min-height: 85px; padding-top: 15px;">
                  <div class="left">
                    <i class="hr-payroll-bonus bg-red"></i>
                  </div>
                  <div class="right">
                    <div class="clearfix">
                      <div>
                        <span class="c-red pull-left">
                           <?php if($this->bonus != 0)
                           {
                              echo $this->bonus;
                           }
                           else if($this->bonus == 0)
                           { ?>
                              <i class="mdi-action-done"></i>
                           <?php }
                           ?>
                        </span>
                      </div>
                      <div class="txt" style="font-size: 15px;">Bonus</div>
                    </div>
                  </div>
                </div>
               </a>
               <a href="<?php echo $this->baseUrl('/payroll/commission');?>" target="_blank">
                <div class="infobox panel" style="min-width: 250px; min-height: 85px; padding-top: 15px;">
                  <div class="left">
                    <i class="hr-payroll-commission bg-green"></i>
                  </div>
                  <div class="right">
                    <div class="clearfix">
                      <div>
                        <span class="c-green pull-left">
                           <?php if($this->commission != 0)
                           {
                              echo $this->commission;
                           }
                           else if($this->commission == 0)
                           { ?>
                              <i class="mdi-action-done"></i>
                           <?php }
                           ?>
                        </span>
                      </div>
                      <div class="txt" style="font-size: 15px;">Commission</div>
                    </div>
                  </div>
                </div>
               </a>
               <a href="<?php echo $this->baseUrl('/payroll/deductions');?>" target="_blank">
                <div class="infobox panel" style="min-width: 250px; min-height: 85px; padding-top: 15px;">
                  <div class="left">
                    <i class="hr-payroll-deductions bg-purple"></i>
                  </div>
                  <div class="right">
                    <div class="clearfix">
                      <div>
                        <span class="c-purple pull-left">
                           <?php if($this->deduction != 0)
                           {
                              echo $this->deduction;
                           }
                           else if($this->deduction == 0)
                           { ?>
                              <i class="mdi-action-done"></i>
                           <?php }
                           ?>
                        </span>
                      </div>
                      <div class="txt" style="font-size: 15px;">Deduction</div>
                    </div>
                  </div>
                </div>
               </a>
               <a href="<?php echo $this->baseUrl('/payroll/loan');?>" target="_blank">
                <div class="infobox panel" style="min-width: 250px; min-height: 85px; padding-top: 15px;">
                  <div class="left">
                    <i class="hr-payroll-loan bg-orange"></i>
                  </div>
                  <div class="right">
                    <div class="clearfix">
                      <div>
                        <span class="c-orange pull-left">
                           <?php if($this->loan != 0)
                           {
                              echo $this->loan;
                           }
                           else if($this->loan == 0)
                           { ?>
                              <i class="mdi-action-done"></i>
                           <?php }
                           ?>
                        </span>
                      </div>
                      <div class="txt" style="font-size: 15px;">Loan</div>
                    </div>
                  </div>
                </div>
               </a>
               <a href="<?php echo $this->baseUrl('/payroll/loan');?>" target="_blank">
                <div class="infobox panel" style="min-width: 250px; min-height: 85px; padding-top: 15px;">
                  <div class="left">
                    <i class="hr-payroll-loan bg-dark"></i>
                  </div>
                  <div class="right">
                    <div class="clearfix">
                      <div>
                        <span class="c-dark pull-left">
                           <?php if($this->deferredLoan != 0)
                           {
                              echo $this->deferredLoan;
                           }
                           else if($this->deferredLoan == 0)
                           { ?>
                              <i class="mdi-action-done"></i>
                           <?php }
                           ?>
                        </span>
                      </div>
                      <div class="txt" style="font-size: 15px;">Deferred Loan</div>
                    </div>
                  </div>
                </div>
               </a>
               <a href="<?php echo $this->baseUrl("/payroll/reimbursement?data=$encodedParams");?>" target="_blank">
                <div class="infobox panel" style="min-width: 250px; min-height: 85px; padding-top: 15px;">
                  <div class="left">
                    <i class="hr-payroll-reimbursement bg-yellow"></i>
                  </div>
                  <div class="right">
                    <div class="clearfix">
                      <div>
                        <span class="c-yellow pull-left">
                           <?php if($this->reimbursement != 0)
                           {
                              echo $this->reimbursement;
                           }
                           else if($this->reimbursement == 0)
                           { ?>
                              <i class="mdi-action-done"></i>
                           <?php }
                           ?>
                        </span>
                      </div>
                      <div class="txt" style="font-size: 15px;">Reimbursement</div>
                    </div>
                  </div>
                </div>
               </a>
               <a href="<?php echo $this->baseUrl('/v3/approvals/approval-management?form_id=34');?>" target="_blank">
                <div class="infobox panel" style="min-width: 250px; min-height: 85px; padding-top: 15px;">
                  <div class="left">
                    <i class="hr-employees-resignation bg-aero"></i>
                  </div>
                  <div class="right">
                    <div class="clearfix">
                      <div>
                        <span class="c-aero pull-left" style="color:#95b8bf;">
                           <?php if($this->resignation != 0)
                           {
                              echo $this->resignation;
                           }
                           else if($this->resignation == 0)
                           { ?>
                              <i class="mdi-action-done"></i>
                           <?php }
                           ?>
                        </span>
                      </div>
                      <div class="txt" style="font-size: 15px;">Resignation</div>
                    </div>
                  </div>
                </div>
               </a>
               <a href="<?php echo $this->baseUrl('/payroll/shift-allowance');?>" target="_blank">
                <div class="infobox panel" style="min-width: 250px; min-height: 85px; padding-top: 15px;">
                  <div class="left">
                    <i class="hr-payroll-shift-allowance bg-secondary"></i>
                  </div>
                  <div class="right">
                    <div class="clearfix">
                      <div>
                        <span class="c-red pull-left" style="color:#DC88E6;">
                           <?php if($this->shift != 0)
                           {
                              echo $this->shift;
                           }
                           else if($this->shift == 0)
                           { ?>
                              <i class="mdi-action-done"></i>
                           <?php }
                           ?>
                        </span>
                      </div>
                      <div class="txt" style="font-size: 15px;">Shift Allowance</div>
                    </div>
                  </div>
                </div>
               </a>
               
               <a href="<?php echo $attendanceUrl;?>" target="_blank">
                <div class="infobox panel" style="min-width: 250px; min-height: 85px; padding-top: 15px;">
                  <div class="left">
                    <i class="hr-employees-attendance bg-green"></i>
                  </div>
                  <div class="right">
                    <div class="clearfix">
                      <div>
                        <span class="c-green pull-left">
                           <?php if($this->attendance != 0)
                           {
                              echo $this->attendance;
                           }
                           else if($this->attendance == 0)
                           { ?>
                              <i class="mdi-action-done"></i>
                           <?php }
                           ?>
                        </span>
                      </div>
                      <div class="txt" style="font-size: 15px;">Attendance</div>
                    </div>
                  </div>
                </div>
               </a>
               <a href="<?php echo $this->baseUrl('/v3/approvals/approval-management?form_id=334');?>" target="_blank">
                <div class="infobox panel" style="min-width: 250px; min-height: 85px; padding-top: 15px;">
                  <div class="left">
                    <i class="hr-employees-compensatory-off bg-purple"></i>
                  </div>
                  <div class="right">
                    <div class="clearfix">
                      <div>
                        <span class="c-purple pull-left">
                           <?php if($this->compensatoryOff != 0)
                           {
                              echo $this->compensatoryOff;
                           }
                           else if($this->compensatoryOff == 0)
                           { ?>
                              <i class="mdi-action-done"></i>
                           <?php }
                           ?>
                        </span>
                      </div>
                      <div class="txt" style="font-size: 15px;">Compensatory Off</div>
                    </div>
                  </div>
                </div>
               </a>
               <a href="<?php echo $leaveUrl;?>" target="_blank">
                <div class="infobox panel" style="min-width: 250px; min-height: 85px; padding-top: 15px;">
                  <div class="left">
                    <i class="hr-employees-leaves bg-orange"></i>
                  </div>
                  <div class="right">
                    <div class="clearfix">
                      <div>
                        <span class="c-orange pull-left">
                           <?php if($this->leave != 0)
                           {
                              echo $this->leave;
                           }
                           else if($this->leave == 0)
                           { ?>
                              <i class="mdi-action-done"></i>
                           <?php }
                           ?>
                        </span>
                      </div>
                      <div class="txt" style="font-size: 15px;">Leaves</div>
                    </div>
                  </div>
                </div>
               </a>
               <a href="<?php echo $this->baseUrl("/v3/approvals/approval-management?form_id=352?data=$encodedSalaryDateParams");?>" target="_blank"> 
                <div class="infobox panel" style="min-width: 250px; min-height: 85px; padding-top: 15px;">
                  <div class="left">
                    <i class="hr-employees-leaves bg-orange"></i>
                  </div>
                  <div class="right">
                    <div class="clearfix">
                      <div>
                        <span class="c-orange pull-left">
                           <?php if($this->shortTimeOff != 0)
                           {
                              echo $this->shortTimeOff;
                           }
                           else if($this->shortTimeOff == 0)
                           { ?>
                              <i class="mdi-action-done"></i>
                           <?php }
                           ?>
                        </span>
                      </div>
                      <div class="txt" style="font-size: 15px;">Short Time Off</div>
                    </div>
                  </div>
                </div>
               </a>
               <a href="<?php echo $this->baseUrl('/in/payroll/additional-wage-claim');?>" target="_blank">
                <div class="infobox panel" style="min-width: 250px; min-height: 85px; padding-top: 15px;">
                  <div class="left">
                    <i class="hr-payroll-additional-wage-claim bg-dark"></i>
                  </div>
                  <div class="right">
                    <div class="clearfix">
                      <div>
                        <span class="c-dark pull-left">
                           <?php if($this->additionalWageClaimCount != 0)
                           {
                              echo $this->additionalWageClaimCount;
                           }
                           else if($this->additionalWageClaimCount == 0)
                           { ?>
                              <i class="mdi-action-done"></i>
                           <?php }
                           ?>
                        </span>
                      </div>
                      <div class="txt" style="font-size: 15px;">Additional Wage Claim</div>
                    </div>
                  </div>
                </div>
               </a>
              
              <a href="<?php echo $this->baseUrl("/v3/my-team/lop-recovery?data=$encodedParams");?>" target="_blank">
                <div class="infobox panel" style="min-width: 250px; min-height: 85px; padding-top: 15px;">
                  <div class="left">
                    <i class="hr-payroll-reimbursement bg-yellow"></i>
                  </div>
                  <div class="right">
                    <div class="clearfix">
                      <div>
                        <span class="c-yellow pull-left">
                           <?php if($this->employeeLopRecovery != 0)
                           {
                              echo count($this->employeeLopRecovery);
                           }
                           else if($this->employeeLopRecovery == 0)
                           { ?>
                              <i class="mdi-action-done"></i>
                           <?php }
                           ?>
                        </span>
                      </div>
                      <div class="txt" style="font-size: 15px;">LOP Recovery</div>
                    </div>
                  </div>
                </div>
              </a>

              <a href="<?php echo $this->baseUrl("/payroll/tax-declarations?data=$encodedParams");?>" target="_blank">
              <div class="infobox panel" style="min-width: 250px; min-height: 85px; padding-top: 15px;">
                <div class="left">
                  <i class="hr-tax-and-statutory-compliance-tax-declarations bg-blue"></i>
                </div>
                <div class="right">
                  <div class="clearfix">
                    <div>
                      <span class="c-red pull-left">
                          <?php if($this->taxDeclaration != 0)
                          {
                            echo count($this->taxDeclaration);
                          }
                          else if($this->taxDeclaration == 0)
                          { ?>
                            <i class="mdi-action-done"></i>
                          <?php }
                          ?>
                      </span>
                    </div>
                    <div class="txt" style="font-size: 15px;">Tax Declaration</div>
                  </div>
                </div>
              </div>
              </a>
              
              <a href="<?php echo $this->baseUrl("/payroll/tax-declarations?data=$encodedParams");?>" target="_blank">
                <div class="infobox panel" style="min-width: 250px; min-height: 85px; padding-top: 15px;">
                  <div class="left">
                    <i class="hr-tax-and-statutory-compliance-tax-declarations bg-red"></i>
                  </div>
                  <div class="right">
                    <div class="clearfix">
                      <div>
                        <span class="c-red pull-left">
                           <?php if($this->hraDeclaration != 0)
                           {
                              echo  count($this->hraDeclaration);
                           }
                           else if($this->hraDeclaration == 0)
                           { ?>
                              <i class="mdi-action-done"></i>
                           <?php }
                           ?>
                        </span>
                      </div>
                      <div class="txt" style="font-size: 15px;">Hra Declaration</div>
                    </div>
                  </div>
                </div>
               </a>
               
               <a href="<?php echo $this->baseUrl('/payroll/proof-of-investment');?>" target="_blank">
                  <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2">
                    <div class="panel">
                      <div class="panel-content widget-info">
                        <div class="row">
                          <div class="left">
                            <i class="hr-tax-and-statutory-compliance-tax-declarations bg-aero"></i>
                          </div>
                          <div class="right">
                            <p style="font-size:16px;line-height: 15px;">Income U/S 24</p>
                            <p style="font-size:16px;line-height: 19px;">
                                <?php if($this->incomeUnderSection24 != 0)
                                {
                          $incomeUnderSection24 = $this->incomeUnderSection24;
                          $countIncomeUnderSection24 = count($incomeUnderSection24);?>
                          <a class="col-lg-2 col-md-2 col-sm-2 col-xs-2 incomeUnderSection24 m-b-5" style="color:#5A5252; cursor: pointer;" id="incomeUnderSection24" onclick="fnShowReminder('incomeUnderSection24');"><u><?php echo $countIncomeUnderSection24; ?></u></a>
                                <?php }
                                else if($this->incomeUnderSection24 == 0)
                                { ?>
                                    <i class="mdi-action-done"></i>
                                <?php }
                                ?>
                        </p>
                      </div>
                      <div class="animated flipInX hiddenGrid" id="incomeUnderSection24Grid" style="overflow: hidden; outline: none; display: none;margin:0px;" tabindex="5002">
                        <ul>
                          <li class="hed">
                            <div class="gridText">Employee Id</div>
                            <div class="gridText">Employee Name</div>
                          </li>
                          <?php 
                          for($i=0;$i<$countIncomeUnderSection24;$i++)
                          {
                              echo '<li class="'.(($i % 2 == 0) ? 'row-even' : 'row-odd').'">
                              <div class="gridText">'.$incomeUnderSection24[$i]['Employee_Id'].'</div>
                              <div class="gridText">'.$incomeUnderSection24[$i]['Employee_Name'].'</div>
                              </li>';
                          }
                          ?>
                        </ul>	
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
              </a>

              <a href="<?php echo $this->baseUrl("/v3/tax-and-statutory-compliance/tax-relief?data=$encodedParamsTaxRelief");?>" target="_blank">
              <div class="infobox panel" style="min-width: 250px; min-height: 85px; padding-top: 15px;">
                <div class="left">
                  <i class="hr-tax-and-statutory-compliance-tax-declarations bg-blue"></i>
                </div>
                <div class="right">
                  <div class="clearfix">
                    <div>
                      <span class="c-red pull-left">
                          <?php if($this->taxReliefDeclaration != 0)
                          {
                            echo count($this->taxReliefDeclaration);
                          }
                          else if($this->taxReliefDeclaration == 0)
                          { ?>
                            <i class="mdi-action-done"></i>
                          <?php }
                          ?>
                      </span>
                    </div>
                    <div class="txt" style="font-size: 15px;">Tax Relief</div>
                  </div>
                </div>
              </div>
              </a>


              </div>
            </div>
              </div>
<div class="row">
   <div class="col-md-12 col-lg-12 col-sm-12 col-xs-12">

    <h3 class="m-t-30 m-b-10"><strong>Closure Processes</strong></h3>
       <div class="widget-infobox">
      <div class="row m-t-10">
         <a href="<?php echo $this->baseUrl('/payroll/tax-rules');?>" target="_blank">
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
              <div class="panel">
                <div class="panel-content widget-info">
                  <div class="row">
                    <div class="left">
                      <i class="hr-financeclosure bg-dark"></i>
                    </div>
                    <div class="right">
							<p style="font-size:16px; line-height: 9px;">Financial Closure</p>
                      <p style="font-size:16px; line-height: 4px;">
                           <?php if($this->financialClosure != 0)
                           {
                              echo $this->financialClosure;
                           }
                           else if($this->financialClosure == 0)
                           { ?>
                              <i class="mdi-action-done"></i>
                           <?php }
                           ?>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
         </a>
			   <a href="<?php echo $this->baseUrl('/employees/leaves');?>" target="_blank">
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
              <div class="panel">
                <div class="panel-content widget-info">
                  <div class="row">
                    <div class="left">
                      <i class="hr-lock-alt bg-yellow"></i>
                    </div>
                    <div class="right">
							<p style="font-size:16px;line-height: 9px;">Leave Closure</p>
							<p style="font-size:16px;line-height: 4px;">
                     <?php 
                        /* If leave closure or leave encashment is not run */
                        if ($this->leaveClosureEncashment == 1) {
                           echo "1";
                        }
                        else
                        { ?>
                            <i class="mdi-action-done"></i>
                      <?php }
                     ?>
							</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
         </a>
			   <a href="<?php echo $this->baseUrl('/payroll/salary');?>" target="_blank">
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
              <div class="panel">
                <div class="panel-content widget-info">
                  <div class="row">
                    <div class="left">
                      <i class="hr-calculator bg-aero"></i>
                    </div>
                    <div class="right">
							<p style="font-size:16px;line-height: 14px;">Salary Recalculation</p>
                      <p style="font-size:16px;line-height: 19px;">
                           <?php if($this->salaryRecalc != 0)
                           {
                              echo $this->salaryRecalc;
                           }
                           else if($this->salaryRecalc == 0)
                           { ?>
                              <i class="mdi-action-done"></i>
                           <?php }
                           ?>
                      </p>
                      
                    </div>
                  </div>
                </div>
              </div>
            </div>
         </a>
         
         <a href="<?php echo $noAttendanceFinalizationUrl;?>" target="_blank">
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
              <div class="panel">
                <div class="panel-content widget-info">
                  <div class="row">
                    <div class="left">
                      <i class="hr-employees-attendance bg-secondary"></i>
                    </div>
                    <div class="right">
							        <p style="font-size:16px;line-height: 15px;">Attendance Finalization</p>
                        <p style="font-size:16px;line-height: 19px;">
                          <?php if($this->attendanceEnforce != 0)
                          {
                            $attendanceEnforce = $this->attendanceEnforce;
                            $countAttendanceEnforce = (array_sum(array_column($attendanceEnforce, 'Count')));?>  
                            <a href="<?php echo $noAttendanceFinalizationUrl;?>" target="_blank"><u><?php echo $countAttendanceEnforce; ?></u></a>
                          <?php }
                          else if($this->attendanceEnforce == 0)
                          { ?>
                              <i class="mdi-action-done"></i>
                          <?php }?>
                      </p>
								    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
          </a>

          <a href="<?php echo $this->baseUrl('/in/roster-management/shift-scheduling');?>" target="_blank"> 
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
              <div class="panel">
                <div class="panel-content widget-info">
                  <div class="row">
                    <div class="left">
                      <i class="hr-roster-management-shift-scheduling bg-blue"></i>
                    </div>
                    <div class="right">
							        <p style="font-size:16px;line-height: 15px;">Unscheduled Shift</p>
                      <p style="font-size:16px;line-height: 19px;">
                           <?php if($this->nonShiftDays != 0)
                           {
                    $nonShiftDays = $this->nonShiftDays;
                		$countNonShiftDays = count($nonShiftDays);?>
										<a class="col-md-12 col-sm-12 col-xs-12 col-xl-12 shiftScheduling m-b-5" style="color:#5A5252; cursor: pointer;" id="shiftScheduling" onclick="fnShowReminder('shiftScheduling');"><u><?php echo $countNonShiftDays; ?></u></a>
                          <?php }
                           else if($this->nonShiftDays == 0)
                           { ?>
                              <i class="mdi-action-done"></i>
                           <?php }
                           ?>
									</p>
								 </div>
								<div class="animated flipInX hiddenGrid" id="shiftSchedulingGrid" style="overflow: hidden; outline: none; display: none;margin:0px;" tabindex="5002">
									<ul>
										<li class="hed">
                      <div class="gridText">Employee Name</div>
                      <div class="gridText">Unscheduled Date</div>
										</li>
										<?php 
										for($i=0;$i<$countNonShiftDays;$i++)
										{
								        echo '<li class="'.(($i % 2 == 0) ? 'row-even' : 'row-odd').'">
                        <div class="gridText">'.$nonShiftDays[$i]['Employee_Name'].'</div>
                        <div class="gridText">'.$nonShiftDays[$i]['Non_Scheduled_Date'].'</div>
                        </li>';
										}
										?>
									</ul>	
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
          </a>

         <a href="<?php echo $this->baseUrl('/v3/my-team/team-summary');?>" target="_blank">
           <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
              <div class="panel">
                <div class="panel-content widget-info">
                  <div class="row">
                    <div class="left">
                      <i class="hr-asset-management bg-red"></i>
                    </div>
                    <div class="right">
							<p style="font-size:16px;line-height: 15px;">Asset Management</p>
                      <p style="font-size:16px;line-height: 19px;">
                           <?php if($this->assetManagement != 0)
                           {
										$assetManagement = $this->assetManagement;
										$countassetManagement = count($assetManagement);?>
										<a class="col-md-12 col-sm-12 col-xs-12 col-xl-12 assetManagement m-b-5" style="color:#5A5252; cursor: pointer;" id="assetManagement" onclick="fnShowReminder('assetManagement');"><u><?php echo $countassetManagement; ?></u></a>
                          <?php }
                           else if($this->assetManagement == 0)
                           { ?>
                              <i class="mdi-action-done"></i>
                           <?php }
                           ?>
									</p>
								 </div>
								<div class="animated flipInX hiddenGrid" id="assetManagementGrid" style="overflow: hidden; outline: none; display: none;margin:0px;" tabindex="5002">
									<ul>
										<li class="hed">
                    <div class="gridText">Employee Id</div>
											<div class="gridText">Employee Name</div>
										</li>
										<?php 
										for($i=0;$i<$countassetManagement;$i++)
										{
												echo '<li class="'.(($i % 2 == 0) ? 'row-even' : 'row-odd').'">
                        <div class="gridText">'.$assetManagement[$i]['User_Defined_EmpId'].'</div>
												<div class="gridText">'.$assetManagement[$i]['Employee_Name'].'</div>
												</li>';
										}
										?>
									</ul>	
                    </div>
                  </div>
                </div>
              </div>
            </div>
         </a>

         <a href="<?php echo $this->baseUrl('/v3/my-team/team-summary');?>" target="_blank">
           <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
              <div class="panel">
                <div class="panel-content widget-info">
                  <div class="row">
                    <div class="left">
                      <i class="hr-employees-employees bg-aero"></i>
                    </div>
                    <div class="right">
							      <p style="font-size:16px;line-height: 15px;">Probation Employees</p>
                      <p style="font-size:16px;line-height: 19px;">
                      <?php
                          $probationEmployee    = $this->probationEmployee;
                          $countProbationEmployee = count($probationEmployee);
                          if($countProbationEmployee > 0)
                          {
                      ?>
										  <a class="col-md-12 col-sm-12 col-xs-12 col-xl-12 probationEmployee m-b-5" style="color:#5A5252; cursor: pointer;" id="probationEmployee" onclick="fnShowReminder('probationEmployee');"><u><?php echo $countProbationEmployee; ?></u></a>
                      <?php }
                            else if($countProbationEmployee == 0)
                            { ?>
                              <i class="mdi-action-done"></i>
                           <?php 
                            }
                           ?>
									</p>
								 </div>
								<div class="animated flipInX hiddenGrid" id="probationEmployeeGrid" style="overflow: hidden; outline: none; display: none;margin:0px;" tabindex="5002">
									<ul>
										<li class="hed">
											<div class="gridText">Employee Name</div>
                      <div class="gridText">Probation Date</div>
										</li>
                    <?php 
                  	for($i=0;$i<$countProbationEmployee;$i++)
										{
												echo '<li class="'.(($i % 2 == 0) ? 'row-even' : 'row-odd').'">
                        <div class="gridText">'.$probationEmployee[$i]['Employee_Name'].'</div>
                        <div class="gridText">'.$probationEmployee[$i]['Probation_Date'].'</div>
												</li>';
										}
										?>
									</ul>	
                    </div>
                  </div>
                </div>
              </div>
            </div>
         </a>
        <a href="<?php echo $this->baseUrl("/v3/payroll/full-and-final-settlement?salaryMonth=$salaryMonth&salaryYear=$salaryYear");?>" target="_blank">
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
            <div class="panel">
              <div class="panel-content widget-info">
                <div class="row">
                  <div class="left">
                    <i class="fa fa-handshake-o bg-aero"></i>
                  </div>
                  <div class="right">
                  <p style="font-size:16px;line-height: 15px;">Full & Final Settlement</p>
                  <p style="font-size:16px;line-height: 19px;"></p>
                    <?php
                        if($this->fullAndFinalSettlement != 0)
                        {
                          $fullAndFinalSettlementEmpDetails = $this->fullAndFinalSettlement;
                          $countFullAndFinalSettlement      = count($fullAndFinalSettlementEmpDetails);
                    ?>
                    <a class="col-md-12 col-sm-12 col-xs-12 col-xl-12 fullAndFinalSettlementEmployee m-b-5" style="color:#5A5252; cursor: pointer;" id="fullAndFinalSettlementEmployee" onclick="fnShowReminder('fullAndFinalSettlementEmployee');"><u><?php echo $countFullAndFinalSettlement; ?></u></a>
                    <?php } else if($this->fullAndFinalSettlement == 0) { ?>
                      <i class="mdi-action-done"></i>
                    <?php } ?>
              </div>
              <div class="animated flipInX hiddenGrid" id="fullAndFinalSettlementEmployeeGrid" style="overflow: hidden; outline: none; display: none;margin:0px;" tabindex="5002">
                <ul>
                  <li class="hed">
                    <div class="gridText">Employee Id</div>
                    <div class="gridText">Employee Name</div>
                  </li>
                  <?php 
                  for($i=0;$i<count($fullAndFinalSettlementEmpDetails);$i++)
                  {
                      echo '<li class="'.(($i % 2 == 0) ? 'row-even' : 'row-odd').'">
                      <div class="gridText">'.$fullAndFinalSettlementEmpDetails[$i]['Employee_Id'].'</div>
                      <div class="gridText">'.$fullAndFinalSettlementEmpDetails[$i]['Employee_Name'].'</div>
                      </li>';
                  }
                  ?>
                </ul>	
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a>

        <a href="<?php echo $earlyCheckoutUrl;?>" target="_blank">
            <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
              <div class="panel">
                <div class="panel-content widget-info">
                  <div class="row">
                    <div class="left">
                      <i class="hr-employees-attendance bg-aero"></i>
                    </div>
                    <div class="right">
							        <p style="font-size:16px;line-height: 15px;">Early Checkout</p>
                        <p style="font-size:16px;line-height: 19px;">
                        <p style="font-size:16px;line-height: 19px;">
                          <?php if($this->earlyCheckout != 0) {
                              echo $this->earlyCheckout;
                          } else { ?>
                              <i class="mdi-action-done"></i>
                          <?php } ?>
                        </p>
								    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </a>

      </div>
      </div>
      </div>
   </div>
    </div>
	</body>
</html>