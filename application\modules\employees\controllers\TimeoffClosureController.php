<?php
class Employees_TimeoffClosureController extends Zend_Controller_Action
{
	protected $_dbAccessRights = null;
	protected $_timeOffClosureAccessRights = null;
	protected $_logEmpId = null;
	protected $_hrappMobile = null;
	protected $_timeOffClosureAccess= null;
	protected $_timeOffClosureFormId = 359;

	public function init()
	{
		$this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
		if ($this->_hrappMobile->checkAuth())
        {
			$this->_dbCommonFun 	  = new Application_Model_DbTable_CommonFunction();
            $userSession        	  = $this->_dbCommonFun->getUserDetails ();
			$this->_logEmpId    	  = $userSession?$userSession['logUserId']:1;
			$this->_dbTimeoffClosure  = new Employees_Model_DbTable_TimeoffClosure();
			$this->_dbAccessRights    = new Default_Model_DbTable_AccessRights();
			$this->_timeOffClosureAccessRights      = $this->_dbAccessRights->employeeAccessRightsBasedOnFormId($this->_logEmpId,$this->_timeOffClosureFormId);		
			$this->_timeOffClosureAccess            = $this->_timeOffClosureAccessRights['Employee'];
			if (Zend_Registry::isRegistered('orgDetails'))
             $this->_orgDetails = Zend_Registry::get('orgDetails');
		}
		else
		{
			if (Zend_Session::namespaceIsset('lastRequest'))
				Zend_Session:: namespaceUnset('lastRequest');
			
			$session = new Zend_Session_Namespace('lastRequest');
			$session->lastRequestUri = 'payroll/salary-payslip';
			$this->_redirect('auth');
		}
	}
	
	/**
	 *
	 * This action shows the total monthly salary and total wages earned by an employee for a month.
	 * Payslip details are shown based on the access rights.
	 */
	public function indexAction()
	{
		$checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

		if ($checkSessionAuth)
		{
			$this->_helper->layout()->disableLayout()->setLayout('admin_layout');
		} else {
			$this->_redirect('auth');
		}
	}
	
	public function listTimeoffClosureEmployeesAction()
    {
		$this->_helper->layout()->disableLayout();
		if(isset($_SERVER['HTTP_REFERER']))
        {
			if($this->_timeOffClosureAccess['Add']==1)
			{
				$ajaxContext = $this->_helper->getHelper('AjaxContext');
				$ajaxContext->addActionContext('list-timeoff-closure-employees', 'json')->initContext();
				$formData = $this->getRequest()->getPost();
				$closureMonth   = $formData['closureMonth'];
				$closureMonth   = filter_var($closureMonth, FILTER_SANITIZE_STRIPPED);
				$designation 	= array_map('intval', $formData['designation']);
				$department 	= array_map('intval', $formData['department']);
				$location 		= array_map('intval', $formData['location']);
				$employeeType 	= array_map('intval', $formData['employeeType']);
				$businessUnit 	= array_map('intval', $formData['businessUnit']);
				$businessUnit 	= array_map('intval', $formData['serviceProvider']);
				$searchArray  = array('designationId'      => $designation,
									'departmentId'       => $department,
									'locationId'         => $location,
									'employeeTypeId'     => $employeeType,
									'businessUnitId'     => $businessUnit,
									'serviceProviderId'  => $serviceProvider);
				$employeeIds 	= array_map('intval', $formData['employeeId']);
				if(!empty($closureMonth))									
				{
					$this->view->result = $this->_dbTimeoffClosure->getTimeoffClosureDetails($searchArray,$employeeIds,$closureMonth);
				}
				else
				{
					$this->view->result = array('success'=>false, 'msg'=>'closure month is mandatory', 'type'=>'warning');
				}
			}
			else
			{
				$this->view->result = array('success'=>false, 'msg'=>'Access denied', 'type'=>'warning');
			}
		}
		else{
			$this->_helper->redirector('index', 'timeoff-closure', 'employees');
		}
	}
	
	public function generateTimeoffClosureAction()
    {
		$this->_helper->layout()->disableLayout();
		if(isset($_SERVER['HTTP_REFERER']))
        {
			if($this->_timeOffClosureAccess['Add']==1)
			{
				$ajaxContext = $this->_helper->getHelper('AjaxContext');
				$ajaxContext->addActionContext('generate-timeoff-closure', 'json')->initContext();
				$formData 		= $this->getRequest()->getPost();
				$closureMonth   = $formData['closureMonth'];
				$closureMonth   = filter_var($closureMonth, FILTER_SANITIZE_STRIPPED);
				$employeeIds 	= array_map('intval', $formData['employeeId']);
				if(!empty($closureMonth) && !empty($employeeIds))									
				{
					$getApprovalDetails = $this->_dbTimeoffClosure->generateTimeOffClosure($employeeIds,$closureMonth);
				}
				else
				{
					$this->view->result = array('success'=>false, 'msg'=>'employee id and closure month is mandatory', 'type'=>'warning');
				}
			}
			else
			{
				$this->view->result = array('success'=>false, 'msg'=>'Access denied', 'type'=>'warning');
			}
		}
		else{
			$this->_helper->redirector('index', 'timeoff-closure', 'employees');
		}
	}
	
	public function __destruct()
    {
        
    }
}