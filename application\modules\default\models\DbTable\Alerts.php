<?php
//=========================================================================================
//=========================================================================================
/* Program : Alerts.php									   				                 *
 * Property of Caprice Technologies Pvt Ltd,                                             *
* Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
* Coimbatore, Tamilnadu, India.														 *
* All Rights Reserved.            														 *
* Use of this material without the express consent of Caprice Technologies              *
* or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
*                                                                                    	 *
* Description : MQL Query to notify the employee for unread messages and other          *
* employee status waiting for his approval.							                 *
*                                                                                   	 *
*                                                                                    	 *
* Revisions :                                                                    	     *
*  Version    Date           Author                  Description                        *
*  0.1        30-May-2013    Narmadha                Initial Version         	         *
*  0.2        06-Jul-2014    Mahesh                  Added alert for award              *
*                                                                                    	 */
//=========================================================================================
//=========================================================================================
class Default_Model_DbTable_Alerts extends Zend_Db_Table_Abstract
{
	protected $_db = null;
	protected $_salesDb = null;
	protected $_ehrTables = null;
	protected $_hrappMobile = null;
	protected $_isMobile = null;
	protected $_orgDF=null;
	
	public function init()
	{
		$this->_db = Zend_Registry::get('subHrapp');
		$this->_salesDb = Zend_Registry::get('Hrapp');
		$this->_ehrTables = new Application_Model_DbTable_Ehr();
		$this->_accessRights = new Default_Model_DbTable_AccessRights();
		$this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
		$this->_isMobile = $this->_hrappMobile->checkDevice();
		$this->_orgDF    = $this->_ehrTables->orgDateformat();
	}

	/**
	 * Get bonus count send for approval to logged in employee
	 */
	public function countBonus($loginEmpId)
	{
		$countBonus = $this->_db->select()->from($this->_ehrTables->empBonus,new Zend_Db_Expr('count(Bonus_Id)'))
		->where('(Approver_Id = ? and Approval_Status = "Pending Approval") or (Payroll_Mid = ? and Approval_Status = "In Process")', (int)$loginEmpId);
		$rowCountBonus=$this->_db->fetchOne($countBonus);

		return $rowCountBonus;
	}
	 
	public function getAnnouncement($loginEmpId)
	{
		$currentDate = date('Y-m-d');
		$getCustomGroupAnnouncement = array();
		$dbPayslip = new Payroll_Model_DbTable_Payslip();																			
		$customGroupParentId = $dbPayslip->getCustomGroupParentId($loginEmpId,'157');

		$getAnnouncementQry = $this->_db->select()->from($this->_ehrTables->announcements,array('Announcement_Id','Announcement_Type','Title','Announcement_Text','Embed_Url','Flash_Content'))
																					->where('Start_Date <= ?',$currentDate)
																					->where('End_Date >= ?',$currentDate);
		if(!empty($customGroupParentId))
		{
			$getAnnouncementQry->where('Coverage="ORGANIZATION"'.' OR '.'(Coverage="CUSTOMGROUP"'.' AND '.'Announcement_Id IN (?))', $customGroupParentId);
		}
		else 
		{
			$getAnnouncementQry->where('Coverage = ?','ORGANIZATION');
		}
		$getAnnouncementDetails = $this->_db->fetchAll($getAnnouncementQry);	
		return $getAnnouncementDetails;
	}
	/**
	 * Get loan count send for approval to logged in employee
	 */
	public function countLoan($loginEmpId)
	{
		$countLoan = $this->_db->select()->from($this->_ehrTables->empLoan,new Zend_Db_Expr('count(Loan_Id)'))
		->where('(Approver_Id = ? and Approval_Status = "Pending Approval") or (Payroll_Mid = ? and Approval_Status = "In Process")', (int)$loginEmpId);
		$rowCountLoan=$this->_db->fetchOne($countLoan);
		return $rowCountLoan;
	}

	public function getHolidayEligiblity($loginEmpId)
	{
		$qryEmployeeType = $this->_db->select()->from(array('type'=>$this->_ehrTables->empType),array('type.Holiday_Eligiblity'))
								->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.EmpType_Id = type.EmpType_Id',
										   array(''))
								->where('Employee_Id = ?',$loginEmpId);
								
		$rowCountLoan=$this->_db->fetchOne($qryEmployeeType);
		return $rowCountLoan;						
		
	}
	/**
	 * Get paymentDueDetails Alert
	 */
	public function paymentDue($loginEmpId,$orgCode)
	{
		$emplole=$this->_accessRights->employeeAccessRights($loginEmpId,'Roles');
		if($emplole['Admin']=='admin')
		{
			$status = array('Initiated','Unpaid');
			$countPayementDue = $this->_salesDb->select()->from($this->_ehrTables->billingTransaction,array(new Zend_Db_Expr('count(Invoice_No)'),'Billing_Date','Total_Amount'))
			->where('Transaction_Status IN (?)', $status)
			->where('Org_Code = ?',$orgCode);
			 
			$rowCountPaymentDue=$this->_salesDb->fetchRow($countPayementDue);

			$paymentBufferDays = $this->_salesDb->select()->from($this->_ehrTables->orgChoiceRate,array('Payment_Buffer_days'))
			->where('Org_Code = ?',$orgCode);
			$rowCountBufferDays=$this->_salesDb->fetchOne($paymentBufferDays);
			if(!empty($rowCountPaymentDue['Billing_Date'])&&!empty($rowCountBufferDays))
			{
				$billingDate = date($this->_orgDF['php'], strtotime($rowCountPaymentDue['Billing_Date'] .' + '.$rowCountBufferDays.' days'));
				
				
				$billingDate1 = date('Y-m-d', strtotime($rowCountPaymentDue['Billing_Date'] .' + '.$rowCountBufferDays.' days'));
				
				$totalAmount =$rowCountPaymentDue['Total_Amount'];
				$basePath = new Zend_View_Helper_BaseUrl();
				if($billingDate1 >= date('Y-m-d'))
				{
					$msg = 'Payment of Rs'.' '.$totalAmount.' '.'due on '.$billingDate;
					$details = array('Payment_Cycle'=>'Due','Message'=>$msg);
					return  $details;
				}
				else
				{
					$msg = 'Payment of Rs'.' '.$totalAmount.' '.'over due on '.$billingDate;;
					$details = array('Payment_Cycle'=>'OverDue','Message'=>$msg);
					return $details;
				}
			}
		}
		else
		{
			return false;
		}
	}
	/* Get Enable Lincense Details*/
	public function enableLincensing($orgcode)
	{
		$sourceId= $this->_salesDb->fetchOne($this->_salesDb->select()->from($this->_ehrTables->registerUser,'Source_Id')->where('Org_Code = ?',$orgcode));
		$organizationName= $this->_salesDb->fetchOne($this->_salesDb->select()->from($this->_ehrTables->appLogin,'Organization_Name')->where('User_Id = ?',$sourceId)->where('Enable_Licensing = ?','1'));
		return $organizationName;
	}
	public function outageNotification()
	{
		$outageDetails = $this->_salesDb->select()->from($this->_ehrTables->settings,array('Deployment_Date','Deployment_Time',
																						   'Deployment_Duration','Advance_Notification_Days',
																						   'Outage_Notification'));
		$rowOutageDetails=$this->_salesDb->fetchRow($outageDetails);
		
		$concatDeploymentDate =strtotime($rowOutageDetails['Deployment_Date'].' '.$rowOutageDetails['Deployment_Time']);
		$deploymentStartDate1 =date('Y-m-d H:i:s',$concatDeploymentDate);
	
        $deploymentStartDate =date('d/m/Y H:i:s',$concatDeploymentDate);
		
		$duration=$rowOutageDetails['Deployment_Duration'];
		
		$deploymentEndDate= date("d/m/Y H:i:s", strtotime("$deploymentStartDate1 + {$duration} hours"));
	
		$msgDisplayFromDate = date('Y-m-d', strtotime($rowOutageDetails['Deployment_Date'] .' - '.$rowOutageDetails['Advance_Notification_Days'].' days'));
		
		$msg = str_ireplace("STARTDATE",$deploymentStartDate,$rowOutageDetails['Outage_Notification']);
		$msg = str_ireplace("ENDDATE",$deploymentEndDate,$msg);

		if (stripos($rowOutageDetails['Outage_Notification'],'Start Date') !== false)
		{
			$msg = str_ireplace("START DATE",$deploymentStartDate,$rowOutageDetails['Outage_Notification']);
		}

		if (stripos($rowOutageDetails['Outage_Notification'],'End Date') !== false)
		{
			$msg = str_ireplace("END DATE",$deploymentEndDate,$msg);
		}
		
        $deploymentEndTime = date("Y-m-d H:i:s", strtotime("$deploymentStartDate1 + {$duration} hours"));
		
		$deploymentStartDat =date('d/m/Y H:i:s',$concatDeploymentDate);
		$deployStartTime = date("H:i:s",strtotime($deploymentStartDat));
		$exactMsgShownStartTime = date('d/m/Y H:i:s', strtotime("$msgDisplayFromDate $deployStartTime"));
		
		
		
		if(date('d/m/Y H:i:00') >= $exactMsgShownStartTime && $deploymentEndDate >= date('d/m/Y H:i:00'))
		{
			return  $msg;
		}
		else
		{
			return false;
		}
		
		
//		$outageDetails = $this->_salesDb->select()->from($this->_ehrTables->settings,array('Deployment_Date','Deployment_Time','Deployment_Duration','Advance_Notification_Days','Outage_Notification'));
//		$rowOutageDetails=$this->_salesDb->fetchRow($outageDetails);
//
//		$concatDeploymentDate =strtotime($rowOutageDetails['Deployment_Date'].' '.$rowOutageDetails['Deployment_Time']);
//		$deploymentStartDate1 =date('Y-m-d h:i:s',$concatDeploymentDate);
//	
//        $deploymentStartDate =date('d/m/Y h:i:s',$concatDeploymentDate);
//		
//		$duration=$rowOutageDetails['Deployment_Duration'];
//		
//		$deploymentEndDate= date("d/m/Y H:i:s", strtotime("$deploymentStartDate1 + {$duration} hours"));
//	
//		$msgDisplayFromDate = date('Y-m-d', strtotime($rowOutageDetails['Deployment_Date'] .' - '.$rowOutageDetails['Advance_Notification_Days'].' days'));
//		
//		$msg = str_ireplace("STARTDATE",$deploymentStartDate,$rowOutageDetails['Outage_Notification']);
//		$msg = str_ireplace("ENDDATE",$deploymentEndDate,$msg);
//
//		if (stripos($rowOutageDetails['Outage_Notification'],'Start Date') !== false)
//		{
//			$msg = str_ireplace("START DATE",$deploymentStartDate,$rowOutageDetails['Outage_Notification']);
//		}
//
//		if (stripos($rowOutageDetails['Outage_Notification'],'End Date') !== false)
//		{
//			$msg = str_ireplace("END DATE",$deploymentEndDate,$msg);
//		}
//		
//        $deploymentEndTime = date("Y-m-d H:i:s", strtotime("$deploymentStartDate1 + {$duration} hours"));
//		
//		$deploymentStartDate4 =date('d/m/Y H:i:s',$concatDeploymentDate);
//		$deploymentEndDate8= date('d/m/Y H:i:s', strtotime("$deploymentStartDate4 + {$duration} hours"));
//		
//		//if(date('Y-m-d') >= $msgDisplayFromDate && $deploymentEndDate >= date('d/m/Y H:i:00'))
//		if(date('d/m/Y H:i:00') >= $deploymentStartDate4 && $deploymentEndDate8 >= date('d/m/Y H:i:00'))
//		{
//			return  $msg;
//		}
//		else
//		{
//			return false;
//		}
    }
	
	/**
	 * Get deferred loan count send for approval to logged in employee
	 */
	public function countDeferredLoan($loginEmpId)
	{
		$countLoan = $this->_db->select()->from($this->_ehrTables->deferredLoan,new Zend_Db_Expr('count(DeferredLoan_Id)'))
		->where('Approver_Id = ? and Defer_Status = "Applied"', (int)$loginEmpId);
		$rowCountLoan=$this->_db->fetchOne($countLoan);
		
		return $rowCountLoan;
	}

	/**
	 * Get travel count send for approval to logged in employee
	 */
	public function countTravel($loginEmpId)
	{
		$cntTravel = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empTravels,new Zend_Db_Expr('count(Request_Id)'))
		->where('(Approver_Id = ? and Approval_Status = "Pending Approval") or (Payroll_Mid = ? and Approval_Status = "In Process")', (int)$loginEmpId)
		->group('Request_Id'));
		$rowcntTravel = (!empty($cntTravel)) ? count($cntTravel) : 0;
		//$rowcntTravel=count($cntTravel);
		return $rowcntTravel;
	}

	/**
	 * Get reimbursement count send for approval to logged in employee
	 */
	public function countReimbursement($loginEmpId)
	{
		$cntReimburse = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->reimbursement,new Zend_Db_Expr('count(Request_Id)'))
		->where('(Approver_Id = ? and Approval_Status = "Pending Approval") or (Payroll_Mid = ? and Approval_Status = "In Process")', (int)$loginEmpId)
		->group('Request_Id'));
		//$rowcntReimburse = count($cntReimburse);
		$rowcntReimburse = (!empty($cntReimburse)) ? count($cntReimburse) : 0;
		return $rowcntReimburse;
	}

	/**
	 * Get tax declaration count send for approval to logged in employee
	 */
	public function countTaxDeclaration($loginEmpId)
	{
		$cntDeclare = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->taxDeclaration,new Zend_Db_Expr('count(Declaration_Id)'))
		->where('Approver_Id = ?', (int)$loginEmpId)->where('Approval_Status = "Applied"')
		->Group('Declaration_Id'));
		$rowcntDeclare = (!empty($cntDeclare)) ? count($cntDeclare) : 0;
		return $rowcntDeclare;
	}
	
	/**
	 * Get tax declaration count send for approval to logged in employee
	 */
	public function countTaxDeclarationUpload($loginEmpId,$isManager,$isAdmin)
	{
		$taxProofSubmissionMonth = $this->_db->fetchOne($this->_db->select()->from(array('F'=>$this->_ehrTables->orgDetails),array('F.Tax_Proof_Submission_Month')));
		$month = date('m');
		if($taxProofSubmissionMonth == $month)
		{
			if(!empty($isAdmin))
			{
				$cntDeclare = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->taxDeclaration,new Zend_Db_Expr('count(Declaration_Id)'))
																		->where('Approval_Status = "Declared"')
																		->Group('Declaration_Id'));
			}
			else
			{
				$cntDeclare = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->taxDeclaration,new Zend_Db_Expr('count(Declaration_Id)'))
																		->where('Employee_Id = ?', (int)$loginEmpId)->where('Approval_Status = "Declared"')
																		->Group('Declaration_Id'));
			}
			$rowcntDeclare = (!empty($cntDeclare)) ? count($cntDeclare) : 0;
		}
		else
		{
			$rowcntDeclare = 0;
		}
		
		return $rowcntDeclare;
	}

	/**
	 * Function for two cases
	 * 1. Get the house property record count for which the employee/admin has to upload the document and present when the tax proof submission month or
	 * current month is same
	 * 2. Get the house property record count for which the admin has to  do the income under section 24 declaration - status approval process
	 */
	public function countHousePropertyDocumentUpload($loginEmpId,$isAdmin,$isApprovalNotification,$housePropertyStatus)
	{
		$housePropertyRecordCount = 0;

		if (Zend_Registry::isRegistered('orgDetails'))
			$orgDetails = Zend_Registry::get('orgDetails');
		
		if(!empty($orgDetails)){
			$taxProofSubmissionMonth = $orgDetails['Tax_Proof_Submission_Month'];
			$currentAssessmentYear = $orgDetails['Assessment_Year'];;
		}else{
			$taxProofSubmissionMonth = $currentAssessmentYear = '';
		}

		if(!empty($currentAssessmentYear)){
			/** If it is approval notification */
			if(!empty($isApprovalNotification)){
				$isLoginEmpPayrollAdmin = $isLogEmpEligApprover = 0;

				/** 1. Validate the login employee is payroll admin. If payroll admin then approval notification should be notified to that employee
				 * 2. Get the employee ids who are having optional choice access income u/s 24 POI aprroval form. These employees can also do the house property
				 * status approval process
				 */
				$eligibleApproversList = $this->_accessRights->validateFormLevelApprovers('Proof Of Investment',$loginEmpId,'Payroll Admin');

				if(!empty($eligibleApproversList)){
					$isLoginEmpPayrollAdmin = $eligibleApproversList['isLogEmpPayrollAdmin'];
					$isLogEmpEligApprover = $eligibleApproversList['isLogEmpEligApprover'];
				}

				/** If the login employee is admin/ payroll admin or if the login employee has optional choice for income u/s 24 POI aprroval form
				 * then present the income u/s 24 pending approval notification in the dashboard
				 */
				if(!empty($isAdmin) || !empty($isLoginEmpPayrollAdmin) || !empty($isLogEmpEligApprover)){
					$housePropertyRecordCount = $this->getHousePropertyDetailsCount(0,$currentAssessmentYear,$housePropertyStatus);
				}

			}else{
				/** Show the notification to the employees when the document is not uploaded on the tax proof submission month */
				$month = date('m');

				if(!empty($taxProofSubmissionMonth) && $taxProofSubmissionMonth == $month)
				{
					$housePropLoginEmpId = (!empty($isAdmin)) ? 0 : $loginEmpId;
					$housePropertyRecordCount = $this->getHousePropertyDetailsCount($housePropLoginEmpId,$currentAssessmentYear,$housePropertyStatus);
				}
			}
		}
			
		return $housePropertyRecordCount;
	}

	/**
	 * Get attendance count send for approval to logged in employee
	 */
	public function countAttendance($loginEmpId)
	{
		$dbAccessRights 					 = new Default_Model_DbTable_AccessRights();	
		$attendanceApprovalFormId 			 = 313;	
		$attendanceApprovalAccessRights      = $dbAccessRights->employeeAccessRightsBasedOnFormId($loginEmpId,$attendanceApprovalFormId);
		if(isset($attendanceApprovalAccessRights['Employee']['View']) && $attendanceApprovalAccessRights['Employee']['View']==1)
		{
			$cntAttendance = $this->_db->select()->from($this->_ehrTables->attendance,new Zend_Db_Expr('count(Attendance_Id)'))
			->where('Approver_Id = ? and Approval_Status = "Applied"', (int)$loginEmpId);
			$rowCntAttendance=$this->_db->fetchOne($cntAttendance);
		}
		else
		{
			$rowCntAttendance = 0;
		}
		return $rowCntAttendance;
	}

	/**
	 * Get commission count send for approval to logged in employee
	 */
	public function countCommission($loginEmpId)
	{
		$cntCommission = $this->_db->select()->from($this->_ehrTables->commission,new Zend_Db_Expr('count(Commission_Id)'))
		->where('Approver_Id = ? and Commission_Status = "Applied"', (int)$loginEmpId);
		$rowCntCommission=$this->_db->fetchOne($cntCommission);
		return $rowCntCommission;
	}

	/**
	 * Get advance salary count send for approval to logged in employee
	 */
	public function countAdvanceSalary($loginEmpId)
	{
		$cntAdvSalary = $this->_db->select()->from($this->_ehrTables->advanceSalary,new Zend_Db_Expr('count(AdvSalary_Id)'))
		->where('Approver_Id = ? and Approval_Status = "Applied"', (int)$loginEmpId);
		$rowCntAdvSalary=$this->_db->fetchOne($cntAdvSalary);
		return $rowCntAdvSalary;
	}

	/**
	 * Get leave count send for approval to logged in employee
	 */
	public function countLeave($loginEmpId)
	{
		$commonFunction =  new Application_Model_DbTable_CommonFunction();
		$leaveSettings  =  $commonFunction->getLeaveSettings();
		$status = array('Applied','Cancel Applied');
		if($leaveSettings['Enable_Workflow'] === 'Yes') 
		{
			$cntLeave = $this->_db->select()->from(array('TUT'=>$this->_ehrTables->taUserTask),new Zend_Db_Expr('count(TUT.task_id)'))
										->joinLeft(array('TPI'=>$this->_ehrTables->taProcessInstance), 'TUT.process_instance_id = TPI.process_instance_id', array())
										->joinLeft(array('ER'=>$this->_ehrTables->empLeaves), 'TPI.process_instance_id = ER.Workflow_Instance_Id', array())
										->joinLeft(array('TWV'=>$this->_ehrTables->taWorkflowVersion), 'TPI.workflow_version_id = TWV.workflow_version_id', array())
										->joinLeft(array('TW'=>$this->_ehrTables->taWorkflow), 'TWV.workflow_id = TW.workflow_id', array())
										->joinLeft(array('TE'=>$this->_ehrTables->taEvent), 'TW.event_id = TE.event_id', array())
										->joinLeft(array('W'=>$this->_ehrTables->workflows), 'TE.event_id = W.Event_Id', array())
										->where('TUT.assignee = ? and W.Workflow_Module_Id = 3', (int)$loginEmpId)
										->where('Approval_Status IN (?)', $status);
				
			$rowCntLeave = $this->_db->fetchOne($cntLeave);
			return $rowCntLeave;
		}
		else
		{
			$cntLeave = $this->_db->select()->from($this->_ehrTables->empLeaves,new Zend_Db_Expr('count(Leave_Id)'))
			->where('Approver_Id = ?', (int)$loginEmpId)
			->where('Approval_Status IN (?)', $status);
			$rowCntLeave=$this->_db->fetchOne($cntLeave);
			return $rowCntLeave;
		}
	}

	/**
	 * Get compensatory off count send for approval to logged in employee
	 */
	public function countCompensatoryOff($loginEmpId)
	{
		$status = array('Applied','Cancel Applied');
		$countCompensatoryOff = $this->_db->select()->from(array('TUT'=>$this->_ehrTables->taUserTask),new Zend_Db_Expr('count(TUT.task_id)'))
										->joinLeft(array('TPI'=>$this->_ehrTables->taProcessInstance), 'TUT.process_instance_id = TPI.process_instance_id', array())
										->joinLeft(array('CO'=>$this->_ehrTables->compOff), 'TPI.process_instance_id = CO.Process_Instance_Id', array())
										->where('TUT.form_id = ?',334)
										->where('TUT.assignee = ?', (int)$loginEmpId)
										->where('CO.Approval_Status IN (?)', $status);
		$rowCountCompensatoryOff = $this->_db->fetchOne($countCompensatoryOff);
		return $rowCountCompensatoryOff;
	}
	
	/**
	 * Get short time off count send for approval to logged in employee
	 */
	public function countShortTimeOff($loginEmpId)
	{
		$status = array('Applied','Cancel Applied');
		$countShortTimeOff = $this->_db->select()->from(array('TUT'=>$this->_ehrTables->taUserTask),new Zend_Db_Expr('count(TUT.task_id)'))
										->joinLeft(array('TPI'=>$this->_ehrTables->taProcessInstance), 'TUT.process_instance_id = TPI.process_instance_id', array())
										->joinLeft(array('ST'=>$this->_ehrTables->shortTimeOff), 'TPI.process_instance_id = ST.Process_Instance_Id', array())
										->where('TUT.form_id = ?',352)
										->where('TUT.assignee = ?', (int)$loginEmpId)
										->where('ST.Approval_Status IN (?)', $status);
		$rowCountShortTimeOff = $this->_db->fetchOne($countShortTimeOff);
		return $rowCountShortTimeOff;
	}

	/**
	 * Get transfer count send for approval to logged in employee
	 */
	public function countTransfer($loginEmpId)
	{
		$cntTransfer = $this->_db->select()->from($this->_ehrTables->transfer,new Zend_Db_Expr('count(Transfer_Id)'))
		->where('Approver_Id = ? and Approval_Status = "Applied"', (int)$loginEmpId);
		$rowCntTransfer = $this->_db->fetchOne($cntTransfer);
		return $rowCntTransfer;
	}

	/**
	 * Get anniversary count to show in home page
	 */
	public function countAniversary()
	{		
		$BAlertDateWhere = $this->getAlertCondition('Birthday','DOB');
		$WAAlertDateWhere = $this->getAlertCondition('Work Anniversary','Date_Of_Join');
		$cntDOBAnniversary = $cntDOJAnniversary = '';
		
		if($BAlertDateWhere != ''){
			// cnt date of birth anniversary based on the alert settings no of days and period
			$cntDOBAnniversary = $this->_db->fetchOne($this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal), array(new Zend_Db_Expr('COUNT(P.Employee_Id)')))
					->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.Employee_Id = P.Employee_Id', array())
					->where($BAlertDateWhere)				
					->where('Emp_Status LIKE ?', 'Active')->where('Form_Status = 1'));
		}
		
		if($WAAlertDateWhere != ''){
			//cnt date of join anniversary based on the alert settings no of days and period
			$cntDOJAnniversary = $this->_db->fetchOne($this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal), array(new Zend_Db_Expr('COUNT(P.Employee_Id)')))
					->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.Employee_Id = P.Employee_Id', array())				
					->where($WAAlertDateWhere)
					->where('Emp_Status LIKE ?', 'Active')->where('Form_Status = 1'));			
		}
		
		return array($cntDOBAnniversary, $cntDOJAnniversary);
	}

	/**
	 * Get award count to show in home page
	 */
	public function countAward()
	{		
		$alertDateWhere = $this->getAlertCondition('Award Announcement','Award_Date');
		
		if($alertDateWhere != ''){
			//getting awards from award date based on the alert settings no of days and period
			$cntAward = $this->_db->select()->from($this->_ehrTables->awards,new Zend_Db_Expr('count(AwardTo_Id)'))
			->where($alertDateWhere);
			//->where(new Zend_Db_Expr("DATE_ADD(Award_Date, INTERVAL 5 DAY)").'>= ?',date('Y-m-d'))
			//->where('Award_Date  <= ?', date('Y-m-d'));			
			$rowCntAward = $this->_db->fetchOne($cntAward);
			return $rowCntAward;
		}
		else{
			return '';
		}		
	}

	/**
	 * Get awards details to show in home page
	 */
	public function getAwards()
	{		
		$alertDateWhere = $this->getAlertCondition('Award Announcement','Award_Date');
		
		if($alertDateWhere != ''){
			//getting awards from award date based on the alert settings no of days and period
			$cntAward = $this->_db->select()->from(array('A'=>$this->_ehrTables->awards),array())
			->joinInner(array('EP'=>$this->_ehrTables->empPersonal),'EP.Employee_Id = A.AwardTo_Id',array('Employee_Name' => new Zend_Db_Expr("CONCAT(EP.Emp_First_Name, ' ', EP.Emp_Last_Name)")))
			->joinInner(array('AT'=>$this->_ehrTables->awardtypes),'A.AwardType_Id = AT.AwardType_Id',array('AT.Award_Name'))
			->where($alertDateWhere);
			//->where(new Zend_Db_Expr("DATE_ADD(A.Award_Date, INTERVAL 5 DAY)").'>= ?',date('Y-m-d'))
			//->where('Award_Date  <= ?', date('Y-m-d'));
			
			$rowCntAward = $this->_db->fetchAll($cntAward);
			return $rowCntAward;
		}
		else{
			return '';		 
		}
	}

	/**
	 * Get birtday details to show in home page
	 */
	public function getBirthdays()
	{
		$BAlertDateWhere = $this->getAlertCondition('Birthday','DOB');		
		
		if($BAlertDateWhere != ''){
			// cnt date of birth anniversary based on the alert settings no of days and period
			$dobAnniversary = $this->_db->fetchAll(
				$this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal), array('Employee_Name' => new Zend_Db_Expr("CONCAT(Emp_First_Name, ' ', Emp_Last_Name)"),
					'Date_of_Birth' => new Zend_Db_Expr("DATE_FORMAT(DOB,'%D %M')")))
					->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.Employee_Id = P.Employee_Id', array())
					->where($BAlertDateWhere)
					->order(new Zend_Db_Expr("DATE_FORMAT(DOB,'%d')"))
					->where('Emp_Status LIKE ?', 'Active')->where('Form_Status = 1'));
					
			return $dobAnniversary;
		}
		else{
			return '';
		}
	}

	/**
	 * Get Date Of Join anniversary details to show in home page
	 */
	public function getDOJAnniversary()
	{
		$WAAlertDateWhere = $this->getAlertCondition('Work Anniversary','Date_Of_Join');		
		
		if($WAAlertDateWhere != ''){
			// cnt date of birth anniversary based on the alert settings no of days and period
			$dojAnniversary = $this->_db->fetchAll($this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal), array('Employee_Name' => new Zend_Db_Expr("CONCAT(Emp_First_Name, ' ', Emp_Last_Name)"),
					'Date_of_Join' => new Zend_Db_Expr("DATE_FORMAT(Date_Of_Join,'%D %M %Y')")))
					->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.Employee_Id = P.Employee_Id', array())
					->where($WAAlertDateWhere)
					->order(new Zend_Db_Expr("DATE_FORMAT(Date_Of_Join,'%d')"))
					->where('Emp_Status LIKE ?', 'Active')->where('Form_Status = 1'));
			
			return $dojAnniversary;
		}
		else{
			return '';
		}
	}

	/**
	 * Get resignation count send for approval to logged in employee
	 * to show in home page
	 */
	public function countResignation($loginEmpId)
	{
	    /*
	     count the total no.of tasks which is in the `Applied` resignation status and mapped with workflow for loggedIn user.
	     Workflow_Module_Id = 2 is for Resignation Module. Refer table `workflow_module`
	    */
		$cntResignation = $this->_db->select()->from(array('TUT'=>$this->_ehrTables->taUserTask),new Zend_Db_Expr('count(TUT.task_id)'))
		->joinLeft(array('TPI'=>$this->_ehrTables->taProcessInstance), 'TUT.process_instance_id = TPI.process_instance_id', array())
		->joinLeft(array('ER'=>$this->_ehrTables->resignation), 'TPI.process_instance_id = ER.Workflow_Instance_Id', array())
		->joinLeft(array('TWV'=>$this->_ehrTables->taWorkflowVersion), 'TPI.workflow_version_id = TWV.workflow_version_id', array())
		->joinLeft(array('TW'=>$this->_ehrTables->taWorkflow), 'TWV.workflow_id = TW.workflow_id', array())
		->joinLeft(array('TE'=>$this->_ehrTables->taEvent), 'TW.event_id = TE.event_id', array())
		->joinLeft(array('W'=>$this->_ehrTables->workflows), 'TE.event_id = W.Event_Id', array())
		->where('TUT.assignee = ? and W.Workflow_Module_Id = 2 and ER.Approval_Status = "Applied"', (int)$loginEmpId);
		$rowCntResignation = $this->_db->fetchOne($cntResignation);
		return $rowCntResignation;
	}

	/**
	 * Get deduction count send for approval to logged in employee
	 * to show in home page
	 */
	public function countDeduction($loginEmpId)
	{
		$cntDeduct = $this->_db->select()->from($this->_ehrTables->deductions,new Zend_Db_Expr('count(Deduction_Id)'))
		->where('Approver_Id = ? and Approval_Status = "Applied"', (int)$loginEmpId);
		$rowCntDeduct = $this->_db->fetchOne($cntDeduct);
		return $rowCntDeduct;
	}

	/**
	 * Get timesheet count send for approval to logged in employee
	 * to show in home page
	 */
	public function countTimesheet($loginEmpId)
	{
		$countTs = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empTimesheet,new Zend_Db_Expr('count(Request_Id)'))
		->where('Approver_Id = ?', (int)$loginEmpId)->where('Approval_Status = "Applied"')->group('Request_Id'));
		//$rowCountTs = count($countTs);
		$rowCountTs = (!empty($countTs)) ? count($countTs) : 0;		
		return $rowCountTs;
		
	}

	/**
	 * Get shift count send for approval to logged in employee
	 * to show in home page
	 */
	public function countShift($loginEmpId)
	{
		$countShift = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empShift, new Zend_Db_Expr('COUNT(Request_Id)'))
		->where('(Approver_Id = ? and Approval_Status = "Pending Approval") or (Payroll_Mid = ? and Approval_Status = "In Process")', (int)$loginEmpId)
		->group('Request_Id'));
		//$rowCountShift = $this->_db->fetchCol($countShift);
		$rowCountShift = (!empty($countShift)) ? count($countShift) : 0;
		return $rowCountShift;
		//return count($rowCountShift);

	}

	/**
	 * Get recruitment count send for approval to logged in employee
	 * to show in home page
	 */
	public function countRecruitment($loginEmpId)
	{
		$countCandidate = $this->_db->select()->from($this->_ehrTables->appliedCandidate, new Zend_Db_Expr('COUNT(Applied_Id)'))
		->where('Employee_Id = ?', (int)$loginEmpId)->where('Job_Status != ?', "Applied");
		$rowCountCandidate = $this->_db->fetchOne($countCandidate);
		return $rowCountCandidate;

	}

	/**
	 * to listout current month expiry employeename,expiry date
	 */
	public function listLicenseExpiry($managersEmployee, $lgnEmpId, $isManager, $isAdmin) {
		$orgDF = $this->_ehrTables->orgDateformat();		
		$alertDateWhere = $this->getAlertCondition('License Expiry','License_Expiry_Date');		
		//$monthAfter = date('Y-m-d', strtotime("+1 month",strtotime(date('Y-M-d'))));//day a month After		
		
		if($alertDateWhere != ''){
			$alertPeriod = $this->_db->fetchOne($this->_db->select()->from(array('AS'=>$this->_ehrTables->alertSettings),array('AS.Period'))
								->joinInner(array('AT'=>$this->_ehrTables->alertTypes), 'AT.Alert_Type_Id = AS.Alert_Type_Id', array(''))
								->where('AT.Alert_Type LIKE ?', 'License Expiry'));
			
			$listLicenseExpiry = $this->_db->select()->from(array('L'=>$this->_ehrTables->empDrivingLicense), array('DFLExpiryDate'=>'L.License_Expiry_Date','License_Expiry_Date' => new Zend_Db_Expr("DATE_FORMAT(L.License_Expiry_Date,'".$orgDF['sql']."')")))
			->joinInner(array('J'=>$this->_ehrTables->empJob), 'L.Employee_Id = J.Employee_Id', array())
			->joinInner(array('P'=>$this->_ehrTables->empPersonal),'L.Employee_Id = P.Employee_Id',array('Employee_Name' => new Zend_Db_Expr("CONCAT(P.Emp_First_Name, ' ', P.Emp_Last_Name)")))
			//->where('L.License_Expiry_Date <= ?', $monthAfter)
			->where($alertDateWhere)
			->where('P.Form_Status = 1 AND J.Emp_Status = "Active"')
			->order("L.License_Expiry_Date ASC");
			
			if (!$isAdmin && $isManager)
			{
				if(!empty($managersEmployee))
				{
					$arrEmpId = array($managersEmployee, $lgnEmpId);
				}
				else
				{
					$arrEmpId = $lgnEmpId;
				}
				$listLicenseExpiry->where('L.Employee_Id IN (?)',$arrEmpId);
			}
			else if(!$isAdmin && !$isManager)
			{
				$listLicenseExpiry->where('L.Employee_Id = ?', $lgnEmpId);
			}
			return array('LicenseExpiry'=>$this->_db->fetchAll($listLicenseExpiry),'AlertPeriod'=>$alertPeriod);
		}
		else{
			return '';
		}
	}

	/**
	 * Get passport expiry details to show in home page
	 */
	public function listPassportExpiry($managersEmployee, $lgnEmpId, $isManager, $isAdmin)
	{
		$orgDF = $this->_ehrTables->orgDateformat();
		$alertDateWhere = $this->getAlertCondition('Passport Expiry','Expiry_Date');		
		//$monthAfter = date('Y-m-d', strtotime("+1 month",strtotime(date('Y-M-d'))));//day a month After
		
		if($alertDateWhere != ''){
			$alertPeriod = $this->_db->fetchOne($this->_db->select()->from(array('AS'=>$this->_ehrTables->alertSettings),array('AS.Period'))
								->joinInner(array('AT'=>$this->_ehrTables->alertTypes), 'AT.Alert_Type_Id = AS.Alert_Type_Id', array(''))
								->where('AT.Alert_Type LIKE ?', 'Passport Expiry'));
			
			$listPassportExpiry = $this->_db->select()->from(array('PP'=>$this->_ehrTables->empPassport), array('DFPExpiryDate'=>'PP.Expiry_Date','Expiry_Date' => new Zend_Db_Expr("DATE_FORMAT(PP.Expiry_Date,'".$orgDF['sql']."')")))
			->joinInner(array('J'=>$this->_ehrTables->empJob), 'PP.Employee_Id = J.Employee_Id', array())
			->joinInner(array('P'=>$this->_ehrTables->empPersonal),'PP.Employee_Id = P.Employee_Id',array('Employee_Name' => new Zend_Db_Expr("CONCAT(P.Emp_First_Name, ' ', P.Emp_Last_Name)")))
			//->where('PP.Expiry_Date <= ?', $monthAfter)
			->where($alertDateWhere)
			->where('P.Form_Status = 1 AND J.Emp_Status = "Active"')
			->order("PP.Expiry_Date ASC");
			if (!$isAdmin && $isManager)
			{
				if(!empty($managersEmployee))
				{
					$arrEmpId = array($managersEmployee, $lgnEmpId);
				}
				else
				{
					$arrEmpId = $lgnEmpId;
				}
				$listPassportExpiry->where('PP.Employee_Id IN(?)',$arrEmpId);
			}
			else if(!$isAdmin && !$isManager)
			{
				$listPassportExpiry->where('PP.Employee_Id = ?', $lgnEmpId);
			}
			//return $this->_db->fetchAll($listPassportExpiry);
			return array('PassportExpiry'=>$this->_db->fetchAll($listPassportExpiry),'AlertPeriod'=>$alertPeriod);
		}
		else{
			return '';
		}
	}
	
	public function showReminder($receiverId)
	{
		$alertArray = array();
		$countAnniversary = $this->countAniversary();
		$countAward = $this->countAward();
		if ( $countAnniversary[0] > 0)
		{
			$BAlertPeriod = $this->_db->fetchOne($this->_db->select()->from(array('AS'=>$this->_ehrTables->alertSettings),array('AS.Period'))
									->joinInner(array('AT'=>$this->_ehrTables->alertTypes), 'AT.Alert_Type_Id = AS.Alert_Type_Id', array(''))
									->where('AT.Alert_Type LIKE ?', 'Birthday'));

			$alertArray[] = array('AlertName'=>'DOB','Count'=>$countAnniversary[0],'AlertPeriod'=>$BAlertPeriod);
			//$alertArray[] = array('DOB'=>$countAnniversary[0]);
			$result['birthdays'] = $this->getBirthdays();
            
		}

		if ( $countAnniversary[1] > 0)
		{
			$WOAAlertPeriod = $this->_db->fetchOne($this->_db->select()->from(array('AS'=>$this->_ehrTables->alertSettings),array('AS.Period'))
									->joinInner(array('AT'=>$this->_ehrTables->alertTypes), 'AT.Alert_Type_Id = AS.Alert_Type_Id', array(''))
									->where('AT.Alert_Type LIKE ?', 'Work Anniversary'));
			
			$alertArray[] = array('AlertName'=>'DOJ','Count'=>$countAnniversary[1],'AlertPeriod'=>$WOAAlertPeriod);
			//$alertArray[] = array('DOJ'=>$countAnniversary[1]);
			$result['dateofjoin'] = $this->getDOJAnniversary();
		}

		if ( $countAward > 0 )
		{
			$AAlertPeriod = $this->_db->fetchOne($this->_db->select()->from(array('AS'=>$this->_ehrTables->alertSettings),array('AS.Period'))
									->joinInner(array('AT'=>$this->_ehrTables->alertTypes), 'AT.Alert_Type_Id = AS.Alert_Type_Id', array(''))
									->where('AT.Alert_Type LIKE ?', 'Award Announcement'));
			
			$alertArray[] = array('AlertName'=>'Award','Count'=>$countAward,'AlertPeriod'=>$AAlertPeriod);
			//$alertArray[] = array('Award'=>$countAward);
			$result['awards'] = $this->getAwards();
		}
		
        $result['alerts'] = $alertArray;
	
		return 	$result;
	}

	/**
	 * Get home panel data by receiverId
	 */
	public function showHomePanelData ($receiverId)
	{
		$dbJobDetail = new Employees_Model_DbTable_JobDetail();
		$dbOrgDetail = new Organization_Model_DbTable_OrgSettings();
		$dbPersonal = new Employees_Model_DbTable_Personal();
		$dbAssignment = new Employees_Model_DbTable_Assignment();
		$dbSalary = new Payroll_Model_DbTable_Salary();
		$dbHRReport = new Reports_Model_DbTable_HrReports();
		$dbAccessRights = new Default_Model_DbTable_AccessRights();

	//	$countAnniversary = $this->countAniversary();
		$isManager = $dbJobDetail->isManager($receiverId);
		$employeeAccessRights = $dbAccessRights->employeeAccessRights($receiverId,'');
		$countShift = $this->countShift($receiverId);
		$countLoan = $this->countLoan($receiverId);
		$countDeferredLoan = $this->countDeferredLoan($receiverId);
		$countBonus = $this->countBonus($receiverId);
		$countCandidate = $this->countRecruitment($receiverId);
		$countTimesheet = $this->countTimesheet($receiverId);
		$countAttendance = $this->countAttendance($receiverId);
		$countLeave = $this->countLeave($receiverId);
		$countCompensatoryOff = $this->countCompensatoryOff($receiverId);
		$countShortTimeOff = $this->countShortTimeOff($receiverId);
		$countTransfer = $this->countTransfer($receiverId);
		$countResignation = $this->countResignation($receiverId);
		$countTravel = $this->countTravel($receiverId);
		$countReimburse = $this->countReimbursement($receiverId);
		$countCommission = $this->countCommission($receiverId);
		$countAdvsalary = $this->countAdvanceSalary($receiverId);
		$countDeduction = $this->countDeduction($receiverId);
		$countTaxDeclaration = $this->countTaxDeclaration($receiverId);
		$countTaxDeclarationUpload = $this->countTaxDeclarationUpload($receiverId,$isManager,$employeeAccessRights['Admin']);
		$countHousePropDocUploadNotification = $this->countHousePropertyDocumentUpload($receiverId,$employeeAccessRights['Admin'], 0, 'Declared');
		$countHousePropApprovalNotification = $this->countHousePropertyDocumentUpload($receiverId,$employeeAccessRights['Admin'], 1, 'Applied');

	//	$countAward = $this->countAward();
		$orgCode = $this->_ehrTables->getOrgCode();

		//getting the financialClosure value
		$fiscalClosure = $dbOrgDetail->checkFinancialClosure($orgCode);

		

		
		$alertArray = array();


		if (!empty($employeeAccessRights['Admin']) && $fiscalClosure > 0)
		{
			$alertArray[] = array('FinancialClosure'=>$fiscalClosure);
		}

		if ( $countTimesheet > 0 )
		{
			$alertArray[] = array('Timesheet'=>$countTimesheet);
		}

		if ( $countAttendance > 0 )
		{
			$alertArray[] = array('Attendance'=>$countAttendance);
		}

		if ( $countLeave > 0 )
		{
			$alertArray[] = array('Leave'=>$countLeave);
		}

		if ( $countCompensatoryOff > 0 )
		{
			$alertArray[] = array('CompensatoryOff'=>$countCompensatoryOff);
		}

		if ( $countTravel > 0 )
		{
			$alertArray[] = array('Travel'=>$countTravel);
		}

		if ( $countTransfer > 0 )
		{
			$alertArray[] = array('Transfer'=>$countTransfer);
		}

		if ( $countResignation > 0 )
		{
			$alertArray[] = array('Resignation'=>$countResignation);
		}

		if ( $countCandidate > 0 )
		{
			$alertArray[] = array('Recruitment'=>$countCandidate);
		}

		if ( $countBonus > 0 )
		{
			$alertArray[] = array('Bonus'=>$countBonus);
		}

		if ( $countCommission > 0 )
		{
			$alertArray[] = array('Commission'=>$countCommission);
		}

		if ( $countDeduction > 0 )
		{
			$alertArray[] = array('Deduction'=>$countDeduction);
		}


		if ( $countReimburse > 0 )
		{
			$alertArray[] = array('Reimbursement'=>$countReimburse);
		}

		if ( $countAdvsalary > 0 )
		{
			$alertArray[] = array('Advance Salary'=>$countAdvsalary);
		}

		if ( $countLoan > 0 )
		{
			$alertArray[] = array('Loan'=>$countLoan);
		}

		if ( $countDeferredLoan > 0 )
		{
			$alertArray[] = array('Deferred Loan'=>$countDeferredLoan);
		}

		if ( $countShift > 0 )
		{
			$alertArray[] = array('Shift Allowance'=>$countShift);
		}

		if ( $countTaxDeclaration > 0 )
		{
			$alertArray[] = array('Tax Declaration'=>$countTaxDeclaration);
		}
		if ( $countTaxDeclarationUpload > 0 )
		{
			$alertArray[] = array('Tax Declaration Upload'=>$countTaxDeclarationUpload);
		}
		
		if ( $countShortTimeOff > 0 )
		{
			$alertArray[] = array('Short Time off'=>$countShortTimeOff);
		}

		/** If the document is not uploaded for the section24 status approval in the tax proof submission month */
		if( $countHousePropDocUploadNotification > 0){
			$alertArray[] = array('Section24 Declaration Document Upload'=>$countHousePropDocUploadNotification);
		}

		/** If the section 24 pending approval notification count is greater than zero */
		if( $countHousePropApprovalNotification > 0){
			$alertArray[] = array('Section24 Declaration Approval'=>$countHousePropApprovalNotification);
		}		

		$result['Payment_Due'] = $this->paymentDue($receiverId,$orgCode);
		
		$result['Outage_Notification'] = $this->outageNotification();

		$result['empName'] = $dbPersonal->employeeName($receiverId);

		$result['empName']['Admin'] = $employeeAccessRights['Admin'];

		$result['empInfo'] = $dbJobDetail->empJobInfo($receiverId);

		//$result['reportees'] = $dbPersonal->reporteeName($dbJobDetail->managersReportee($receiverId));
		$result['reportees'] = $dbJobDetail->managersReporteesName($receiverId);
		
		$result['assignedTask'] = $dbAssignment->assignedToday($receiverId);
		
		$result['alerts'] = $alertArray;

		$result['managerName'] = $dbJobDetail->approverName($receiverId);
		
		$result['listLicenseExpiry'] = $this->listLicenseExpiry($result['managerName'], $receiverId, $isManager, $employeeAccessRights['Admin']);

		$result['listPassportExpiry'] = $this->listPassportExpiry($result['managerName'], $receiverId, $isManager, $employeeAccessRights['Admin']);
		
		$result['listProbationNotification'] = $this->listProbationNotification($result['managerName'], $receiverId,$isManager, $employeeAccessRights['Admin']);
		
		/**To Get the warning notification in Dashboard -> Alerts/Reminders**/		
		$result['listWarningsNotification'] = $this->listWarningsNotification($result['managerName'], $receiverId,$isManager, $employeeAccessRights['Admin']);

		$result['listConfirmationOfEmployment'] = $this->listConfirmationOfEmployment($result['managerName'], $receiverId,$isManager, $employeeAccessRights['Admin']);
		
		$result['listEmployeesOnNotice'] = $this->listEmployeesOnNotice($result['managerName'], $receiverId,$isManager, $employeeAccessRights['Admin']);
		
		$result['table_data'] = $dbHRReport->rowDriRep('Departments');
		
		if ($result['table_data']['Flag_Stack'] == 1)
		{
			$result['barField'] = $dbHRReport->barField($result['table_data']['Bar_Field_Tbl'], $result['table_data']['Stack_Categ_Name']);
			
			$result['data'] = $dbHRReport->stakedBarData(array('all', 'all', 'all'), array('', '', ''), $result['table_data'],'');
		}
		
		$result['logEmpId'] = $receiverId;
		
		if ($this->_isMobile)
		{
			$result['forms'] = $this->_ehrTables->listFormName('', 'false');
			$result['modules'] = $this->_ehrTables->listFormName('module', 'false');
		}
	
		return $result;
	}
	
	public function getEmployeeQuery ()
	{
		return $this->_db->select()
							->from(array('emp'=>$this->_ehrTables->empPersonal), array(new Zend_Db_Expr('count(emp.Employee_Id)')))
							
							->joinInner(array('job'=>$this->_ehrTables->empJob), 'emp.Employee_Id=job.Employee_Id', array(''));
	}
	
	public function employeesCount ()
	{
		$qryActive = $this->getEmployeeQuery ();
		
		$qryActive->where('job.Emp_Status=\'Active\'');
		
		$qryInactive = $this->getEmployeeQuery ();
		
		$qryInactive->where('job.Emp_Status=\'InActive\'');
		
		$result = array();
		
		$result['ActiveCnt'] = $this->_db->fetchOne($qryActive);
		
		$result['InActiveCnt'] = $this->_db->fetchOne($qryInactive);
		
		return $result;
	}
	
	public function isAdmin ($empId, $formName = '')
	{
		$dbAccessRights = new Default_Model_DbTable_AccessRights();
		$employeeAccessRights = $dbAccessRights->employeeAccessRights($empId, $formName);
	
		return $employeeAccessRights['Admin'];
	
	}
	
	public function isServiceProviderAdmin($empId)
	{
		$dbAccessRights = new Default_Model_DbTable_AccessRights();
		$serviceProviderAdminAccess = $dbAccessRights->employeeAccessRights($empId, 'Service Provider Admin');
		return !empty($serviceProviderAdminAccess) ? $serviceProviderAdminAccess['Employee']['Update'] : "";
	}
	
	public function getAlertCondition($alertName,$fieldLabel){
		/** get the number of days & before/after(period) for award alert**/
		$alertSettingsRow = $this->_db->fetchRow($this->_db->select()->from(array('AS'=>$this->_ehrTables->alertSettings),array('AS.No_Of_Days','AS.Period'))
								->joinInner(array('AT'=>$this->_ehrTables->alertTypes), 'AT.Alert_Type_Id = AS.Alert_Type_Id', array('AT.Alert_Type'))
								->where('AT.Alert_Type LIKE ?', $alertName));
		$alertDay = '';
		
		if($alertSettingsRow != ''){
			/** based on the alert period, alert day will be calculated. For ex, alert period before, alert day is +5 &&
				For alert period before, alert day is -5 **/		
			if($alertSettingsRow['Alert_Type'] == $alertName){
				if($alertSettingsRow['Period'] == 'Before'){
					$alertDay = '+'.$alertSettingsRow['No_Of_Days'];
				}
				else{
					$alertDay = '-'.$alertSettingsRow['No_Of_Days'];
				}
			}		
			
			/** Alert date based on the alert day **/
			$alertDate = date('Y-m-d', strtotime( $alertDay."days",strtotime(date('Y-m-d'))));
			
				/**To Get the below notifications in Dashboard -> Alerts/Reminders**/
			
			if($alertName == 'Confirmation Of Employment' ||$alertName == 'Award Announcement'|| 
				$alertName == 'Probation End Date' || $alertName == 'License Expiry' || $alertName == 'Passport Expiry' 
				|| $alertName == 'Warnings' ||$alertName  == 'Employees On Notice')
			{
				if($alertDate <= date('Y-m-d'))
				{
					/** If alert period is after **/
					$condition = $this->_db->quoteInto($fieldLabel .' >= ?', $alertDate) .
							     $this->_db->quoteInto('and '.$fieldLabel. ' <= ?', date('Y-m-d'));
				}
				else
				{
					/** If alert period is before **/
					$condition = $this->_db->quoteInto($fieldLabel. ' <= ?', $alertDate) .
								 $this->_db->quoteInto('and '.$fieldLabel. ' >= ?', date('Y-m-d'));
				}
			}
			else if($alertName == 'Work Anniversary'){
					// $yearOfFieldLabel = $this->_db->quoteInto(new Zend_Db_Expr("Year($fieldLabel)"));
					if($alertDate <= date('Y-m-d')){
						/** If alert period is after **/
						$condition =$this->_db->quote(new Zend_Db_Expr("Year(Current_DATE()) > Year($fieldLabel)")).
									$this->_db->quoteInto(new Zend_Db_Expr("and CONCAT(Year(Current_DATE()),'-', Date_format(".$fieldLabel.",'%m-%d'))"). ' >= ?', $alertDate) .
								   $this->_db->quoteInto(new Zend_Db_Expr("and CONCAT(Year(Current_DATE()),'-', Date_format(".$fieldLabel.", '%m-%d'))"). ' <= ?', date('Y-m-d'));		   
					}
					else{
						/** If alert period is before **/
						$condition = $this->_db->quote(new Zend_Db_Expr("Year(Current_DATE()) > Year($fieldLabel)")).
									$this->_db->quoteInto(new Zend_Db_Expr("and CONCAT(Year(Current_DATE()),'-', Date_format(".$fieldLabel.", '%m-%d'))"). ' <= ?', $alertDate) .
									$this->_db->quoteInto(new Zend_Db_Expr("and CONCAT(Year(Current_DATE()),'-', Date_format(".$fieldLabel.", '%m-%d'))"). ' >= ?', date('Y-m-d'));
					}
			}
			else 
			{
				if($alertDate <= date('Y-m-d')){
					/** If alert period is after **/
					$condition = $this->_db->quoteInto(new Zend_Db_Expr("CONCAT(Year(Current_DATE()),'-', Date_format(".$fieldLabel.",'%m-%d'))"). ' >= ?', $alertDate) .
							   $this->_db->quoteInto(new Zend_Db_Expr("and CONCAT(Year(Current_DATE()),'-', Date_format(".$fieldLabel.", '%m-%d'))"). ' <= ?', date('Y-m-d'));		   
				}
				else{
					/** If alert period is before **/
					$condition = $this->_db->quoteInto(new Zend_Db_Expr("CONCAT(Year(Current_DATE()),'-', Date_format(".$fieldLabel.", '%m-%d'))"). ' <= ?', $alertDate) .
								$this->_db->quoteInto(new Zend_Db_Expr("and CONCAT(Year(Current_DATE()),'-', Date_format(".$fieldLabel.", '%m-%d'))"). ' >= ?', date('Y-m-d'));
				}
			}
			
			// $condition .= $this->_db->quoteInto('and '.$fieldLabel. ' != ?', '0000-00-00');
			
			return $condition;
		}
		else{
			return '';
		}		
	}
	    


	/**To Get the warning notification in Dashboard -> Alerts/Reminders**/
	public function listWarningsNotification($managersEmployee, $logEmpId, $isManager, $isAdmin)
	{
		$orgDF = $this->_ehrTables->orgDateformat();
		$alertDateWhere = $this->getAlertCondition('Warnings','Warning_Date');
		
		if($alertDateWhere != '')
		{
			$alertPeriod = $this->_db->fetchOne($this->_db->select()->from(array('AS'=>$this->_ehrTables->alertSettings),array('AS.Period'))
									->joinInner(array('AT'=>$this->_ehrTables->alertTypes), 'AT.Alert_Type_Id = AS.Alert_Type_Id', array(''))
									->where('AT.Alert_Type LIKE ?', 'Warnings'));			
				
		    $listWarningsNotification = $this->_db->select()->from(array('W1'=>$this->_ehrTables->warnings),
												array('Warning_Date' => new Zend_Db_Expr("DATE_FORMAT(W1.Warning_Date,'".$orgDF['sql']."')"),'W1.WarningTo_Id'))
														->joinInner(array('P'=>$this->_ehrTables->empPersonal),'P.Employee_Id = W1.WarningTo_Id',
																array('Employee_Name' => new Zend_Db_Expr("CONCAT(P.Emp_First_Name, ' ', P.Emp_Last_Name)")))
														
														->joinInner(array('J'=>$this->_ehrTables->empJob),'J.Employee_Id = W1.WarningTo_Id',array(''))
																		
														->where($alertDateWhere)
														->where('P.Form_Status = 1')									
														->order("W1.Warning_Date ASC");

			/*** If logged In employee is not an Admin*/		
			if(!$isAdmin)
			{
				$qryEmployeeId = $this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))->where('Manager_Id = ?', $logEmpId);
				$getEmployeeId = $this->_db->fetchCol($qryEmployeeId);
				
				/*** If logged In employee is Manager*/		
				if ($isManager && !empty($getEmployeeId))
				{
					$listWarningsNotification->where('J.Employee_Id = :EmpId or J.Employee_Id IN (?)', $getEmployeeId)
						->bind(array('EmpId'=>$logEmpId));
				}
				else
				{
					/*** If logged In employee is not Admin or Manager*/		
					$listWarningsNotification->where('J.Employee_Id = ?', $logEmpId);
				}

			}
		
		    return array('Warnings'=>$this->_db->fetchAll($listWarningsNotification),'AlertPeriod'=>$alertPeriod);
		
		}
		else
		{
			return array();
		}
	}
	
	
	/**
	 * Get probation notification details to show in home page
	 */
	public function listProbationNotification($managersEmployee, $logEmpId, $isManager, $isAdmin)
	{
		$orgDF = $this->_ehrTables->orgDateformat();
		$alertDateWhere = $this->getAlertCondition('Probation End Date','Probation_Date');
		
		if ($alertDateWhere != '')
		{
			$alertPeriod = $this->_db->fetchOne($this->_db->select()->from(array('AS'=>$this->_ehrTables->alertSettings),array('AS.Period'))
								->joinInner(array('AT'=>$this->_ehrTables->alertTypes), 'AT.Alert_Type_Id = AS.Alert_Type_Id', array(''))
								->where('AT.Alert_Type LIKE ?', 'Probation End Date'));			
			
			$listProbationNotification = $this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),
										array('Probation_Date' => new Zend_Db_Expr("DATE_FORMAT(J.Probation_Date,'".$orgDF['sql']."')")))
									->joinInner(array('P'=>$this->_ehrTables->empPersonal),'P.Employee_Id = J.Employee_Id',
												array('Employee_Name' => new Zend_Db_Expr("CONCAT(P.Emp_First_Name, ' ', P.Emp_Last_Name)")))		
									->where($alertDateWhere)
									->where('P.Form_Status = 1 AND J.Confirmed != 1 AND J.Emp_Status = "Active"')									
									->where('J.Confirmation_Date IS NULL')
									->order("J.Probation_Date ASC");
			
		
			if(!$isAdmin)
			{
				$qryEmployeeId = $this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))->where('Manager_Id = ?', $logEmpId);
				$getEmployeeId = $this->_db->fetchCol($qryEmployeeId);
				
				/*** If logged In employee is Manager*/		
				if ($isManager && !empty($getEmployeeId))
				{
					$listProbationNotification->where('J.Employee_Id = :EmpId or J.Employee_Id IN (?)', $getEmployeeId)
						->bind(array('EmpId'=>$logEmpId));
				}
				else
				{
					/*** If logged In employee is not Admin or Manager*/		
					$listProbationNotification->where('J.Employee_Id = ?', $logEmpId);
				}

			}
			//return $this->_db->fetchAll($listProbationNotification);
			return array('ProbationExpiry'=>$this->_db->fetchAll($listProbationNotification),'AlertPeriod'=>$alertPeriod);
		}
		else{			
			return array();
		}
	}
	
	public function listConfirmationOfEmployment($managersEmployee, $logEmpId, $isManager, $isAdmin)
	{
		$orgDF = $this->_ehrTables->orgDateformat();
		$alertDateWhere = $this->getAlertCondition('Confirmation Of Employment','Confirmation_Date');
		
		if ($alertDateWhere != ''){
			$alertPeriod = $this->_db->fetchOne($this->_db->select()->from(array('AS'=>$this->_ehrTables->alertSettings),array('AS.Period'))
								->joinInner(array('AT'=>$this->_ehrTables->alertTypes), 'AT.Alert_Type_Id = AS.Alert_Type_Id', array(''))
								->where('AT.Alert_Type LIKE ?', 'Confirmation Of Employment'));
			
			$listConfirmationOfEmployment = $this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),
										array('Confirmation_Date' => new Zend_Db_Expr("DATE_FORMAT(J.Confirmation_Date,'".$orgDF['sql']."')")))
									->joinInner(array('P'=>$this->_ehrTables->empPersonal),'P.Employee_Id = J.Employee_Id',
												array('Employee_Name' => new Zend_Db_Expr("CONCAT(P.Emp_First_Name, ' ', P.Emp_Last_Name)")))		
									->where($alertDateWhere)
									->where('P.Form_Status = 1 AND J.Confirmed = 1 AND J.Emp_Status = "Active"')
									->order("J.Confirmation_Date ASC");

			if(!$isAdmin)
			{
				$qryEmployeeId = $this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))->where('Manager_Id = ?', $logEmpId);
				$getEmployeeId = $this->_db->fetchCol($qryEmployeeId);
				
				/*** If logged In employee is Manager*/		
				if ($isManager && !empty($getEmployeeId))
				{
					$listConfirmationOfEmployment->where('J.Employee_Id = :EmpId or J.Employee_Id IN (?)', $getEmployeeId)
						->bind(array('EmpId'=>$logEmpId));
				}
				else
				{
					/*** If logged In employee is not Admin or Manager*/		
					$listConfirmationOfEmployment->where('J.Employee_Id = ?', $logEmpId);
				}

			}
			//return $this->_db->fetchAll($listConfirmationOfEmployment);
			return array('ConfirmationList'=>$this->_db->fetchAll($listConfirmationOfEmployment),'AlertPeriod'=>$alertPeriod);
		}
		else{			
			return array();
		}
	}
	
	public function listEmployeesOnNotice($managersEmployee, $logEmpId, $isManager, $isAdmin)
	{
		$orgDF = $this->_ehrTables->orgDateformat();
		$alertDateWhere = $this->getAlertCondition('Employees On Notice','Resignation_Date');
		
		if( $alertDateWhere != '')
		{
			$alertPeriod = $this->_db->fetchOne($this->_db->select()->from(array('AS'=>$this->_ehrTables->alertSettings),array('AS.Period'))
								->joinInner(array('AT'=>$this->_ehrTables->alertTypes), 'AT.Alert_Type_Id = AS.Alert_Type_Id', array(''))
								->where('AT.Alert_Type LIKE ?', 'Employees On Notice'));
			
			$listEmployeesOnNotice = $this->_db->select()->from(array('R'=>$this->_ehrTables->resignation),
										array('Resignation_Date' => new Zend_Db_Expr("DATE_FORMAT(R.Resignation_Date,'".$orgDF['sql']."')")))
									->joinInner(array('P'=>$this->_ehrTables->empPersonal),'P.Employee_Id = R.Employee_Id',
												array('Employee_Name' => new Zend_Db_Expr("CONCAT(P.Emp_First_Name, ' ', P.Emp_Last_Name)")))
			                      ->joinInner(array('J'=>$this->_ehrTables->empJob),'P.Employee_Id = J.Employee_Id',array(''))
									->where($alertDateWhere)
									->where('P.Form_Status = 1 AND J.Emp_Status = "Active"')
									->where('R.Approval_Status LIKE ?', "Applied")
									->order("R.Resignation_Date ASC");
				
				if(!$isAdmin)
				{
					$qryEmployeeId = $this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))->where('Manager_Id = ?', $logEmpId);
					$getEmployeeId = $this->_db->fetchCol($qryEmployeeId);
					
					/*** If logged In employee is Manager*/		
					if ($isManager && !empty($getEmployeeId))
					{
						$listEmployeesOnNotice->where('J.Employee_Id = :EmpId or J.Employee_Id IN (?)', $getEmployeeId)
							->bind(array('EmpId'=>$logEmpId));
					}
					else
					{
						/*** If logged In employee is not Admin or Manager*/		
						$listEmployeesOnNotice->where('J.Employee_Id = ?', $logEmpId);
					}
	
				}

			return array('NoticeEmployees'=>$this->_db->fetchAll($listEmployeesOnNotice),'AlertPeriod'=>$alertPeriod);
		}
		else{			
			return array();
		}
	}

	/** Get the house property record count during approval notification and the document upload notification */
	public function getHousePropertyDetailsCount($loginEmpId,$currentAssessmentYear,$housePropertyStatus){
		$housePropertyRecordCount = 0;

		/** Fetch the self-occupied records */
		$qrySOPIncome = $this->_db->select()->distinct()->from(array('SO'=>$this->_ehrTables->selfOccupiedPropertyIncome), array(
							new Zend_Db_Expr('COUNT(SO.Self_Occupied_Property_Id) AS Count')))
						->where('SO.Assessment_Year = ?', $currentAssessmentYear)
						->where('SO.Approval_Status = ?', $housePropertyStatus);

		/** If the login employee is admin then login employee id will be send as 0 to this function */
		if(!empty($loginEmpId)){
			$qrySOPIncome->where('SO.Employee_Id = ?', (int)$loginEmpId);
		}
		$selfOccupiedIncomeRecCount = $this->_db->fetchOne($qrySOPIncome);

		/** If the self occupied records exists for the employee */
		if(!empty($selfOccupiedIncomeRecCount)){
			$housePropertyRecordCount += $selfOccupiedIncomeRecCount;
		}

		/** Fetch the rented property records */
		$qryRPIncome = $this->_db->select()->distinct()->from(array('RP'=>$this->_ehrTables->rentedPropertyIncome), array(
							new Zend_Db_Expr('COUNT(RP.Rented_Property_Id) AS Count')))
						->where('RP.Assessment_Year = ?', $currentAssessmentYear)
						->where('RP.Approval_Status = ?', $housePropertyStatus);

		if(!empty($loginEmpId)){
			$qryRPIncome->where('RP.Employee_Id = ?', (int)$loginEmpId);
		}

		$rentedPropertyRecCount = $this->_db->fetchOne($qryRPIncome);

		/** If the self occupied records exists for the employee */			
		if(!empty($rentedPropertyRecCount)){
			$housePropertyRecordCount += $rentedPropertyRecCount;
		}

		return $housePropertyRecordCount;
	}

	public function __destruct()
    {
        
    }
}