<?php
//=========================================================================================
//=========================================================================================
/* Program : TransferController.php												         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : A transfer is a relocation of an employee to the same class in a        *
 * different department or job site or to a related classification within the same       *
 * salary range. Transfer requests can be initiated by the employee, or by the manager   *
 * to the HR department, that can approve or reject the transfer request. 				 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Suresh Kumar            Initial Version         	         *
 *  0.2		   06-Jul-2014    Mahesh 				  Modified mail coding and used      *                                                                              	
 *                                                    the common mail function           *	                                                                                    	
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Prasanth                Changed in file for mobile app     *
 *                                                                                       *
 *  1.5        08-Mar-2016    Suresh               Changed in file for Bootstrap         *
 *							  				               		                         */
//=========================================================================================
//=========================================================================================

include APPLICATION_PATH."/validations/Validations.php";

class Employees_TransferController extends Zend_Controller_Action
{
	protected $_validation           = null;
    protected $_dbCommonFunction     = null;
	protected $_dbTransfer 			 = null;
    protected $_dbAccessRights       = null;
    protected $_transferAccessRights = null;
    protected $_logEmpId 			 = null;
    protected $_transferAccess		 = null;
	protected $_ehrTables 			 = null;
    protected $_formName 			 = 'Transfer';
	protected $_hrappMobile          = null;
	protected $_dbEmployee     = null;
	protected $_orgDetails			 = null;

	public function init()
    {
		$this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
	    if ($this->_hrappMobile->checkAuth())
        {
        	$this->_validation 	         = new Validations();
			$this->_dbCommonFunction     = new Application_Model_DbTable_CommonFunction();
            $this->_dbAccessRights       = new Default_Model_DbTable_AccessRights();
            $this->_dbTransfer 			 = new Employees_Model_DbTable_Transfer();
            $this->_ehrTables 			 = new Application_Model_DbTable_Ehr();
			$this->_dbEmployee     = new Employees_Model_DbTable_Employee();
            
			$userSession                 = $this->_dbCommonFunction->getUserDetails ();
			$this->_logEmpId             = $userSession['logUserId'];
            $this->_transferAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formName);
			$this->_transferAccess       = $this->_transferAccessRights['Employee'];
            //$this->_dbAccessRights->refreshUserSessionTimestamp($this->_logEmpId);
			if (Zend_Registry::isRegistered('orgDetails'))
			$this->_orgDetails = Zend_Registry::get('orgDetails');
		}
        else
        {
            if (Zend_Session::namespaceIsset('lastRequest'))
                Zend_Session:: namespaceUnset('lastRequest');
            
            $sessionUrl = new Zend_Session_Namespace('lastRequest');
            $sessionUrl->lastRequestUri = 'employees/transfer';
            $this->_redirect('auth');
        }
    }
    
    public function indexAction()
    {
		$checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

		if ($checkSessionAuth)
        {
			$this->_helper->layout()->disableLayout()->setLayout('admin_layout');
		
			$dbLocation   = new Organization_Model_DbTable_Location();
			$dbDepartment = new Organization_Model_DbTable_Department();
			$dbPersonal   = new Employees_Model_DbTable_Personal();
			
			$this->view->formNameA     = $this->_formName;
			$this->view->customFormNameA = $this->_ehrTables->getCustomForms($this->_formName);
			$this->view->locations    = $dbLocation->getLocationPair($this->_formName,$this->_logEmpId);
			$this->view->department   = $dbDepartment->getDeptPairs();
			
			//$this->view->employeeName = $this->_dbCommonFunction->listEmployeesDetails ('Transfer', 1, $this->_logEmpId);
			
			$this->view->employeeName = $dbPersonal->employeeDetail('', '', 'Emp_First_Name', 'ASC', '', '','', '', $this->_logEmpId,
																	  '', '', '', '', 'Transfer', '', 1, 1, '');
			
			
			$this->view->transferAccess = array('Is_Manager' => $this->_transferAccess['Is_Manager'],
												'View'       => $this->_transferAccess['View'],
												'Update'     => $this->_transferAccess['Update'],
												'Add'        => $this->_transferAccess['Add'],
												'Delete'     => $this->_transferAccess['Delete'],
												'Admin'      => $this->_transferAccessRights['Admin'],
												'TraEdit'    => $this->_dbTransfer->editOption($this->_logEmpId),
												'loginEmpId' => $this->_logEmpId);
			
			$this->view->dateformat = $this->_ehrTables->orgDateformat();
			$this->view->orgDetails      = $this->_orgDetails;
			$this->view->serviceProvider = $this->_dbEmployee->getEmployeeServiceProviderList($this->_logEmpId);
		} else {
			$this->_redirect('auth');
		}
    }

    /**
     * Get transfer details to show in grid
     */
    public function listTransferAction()
    {
	    $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-transfer', 'json')->initContext();
			
            if ($this->_transferAccess['View'] == 1)
            {
                $sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
                
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$employeeName = $this->_getParam('sSearch_0', null);
				$employeeName = filter_var($employeeName, FILTER_SANITIZE_STRIPPED);
				
				$departmentId = $this->_getParam('sSearch_1', null);
				$departmentId = filter_var($departmentId, FILTER_SANITIZE_NUMBER_INT);
				
				$locationId = $this->_getParam('sSearch_2', null);
				$locationId = filter_var($locationId, FILTER_SANITIZE_NUMBER_INT);
				
				$status = $this->_getParam('sSearch_3', null);
				$status = filter_var($status, FILTER_SANITIZE_STRIPPED);
				
				$transferBeginDate = $this->_getParam('sSearch_4', null);
				$transferBeginDate = filter_var($transferBeginDate, FILTER_SANITIZE_STRIPPED);
				
				$transferEndDate = $this->_getParam('sSearch_5', null);
				$transferEndDate = filter_var($transferEndDate, FILTER_SANITIZE_STRIPPED);

				$serviceProviderId = $this->_getParam('sSearch_6', null);
				$serviceProviderId = filter_var($serviceProviderId, FILTER_SANITIZE_NUMBER_INT);
				
                $transferAccess = array('Is_Manager' => $this->_transferAccess['Is_Manager'],
										'Update'     => $this->_transferAccess['Update'],
										'Admin'      => $this->_transferAccessRights['Admin'],
										'LogId'      => $this->_logEmpId,
										'formName'   => $this->_formName);
				
				$searchArr = array( 'employeeName'  	=> $employeeName,
								    'departmentId'  	=> $departmentId,
									'locationId'    	=> $locationId,
									'status'            => $status,
									'transferBeginDate' => $transferBeginDate,
									'transferEndDate'   => $transferEndDate,
									'serviceProviderId' => $serviceProviderId);
				//to display all the transfer records in the data grid
				$this->view->result = $this->_dbTransfer->listTransfer($page, $rows, $sortField, $sortOrder, $searchAll, $searchArr,
																	   $transferAccess);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'transfer', 'employees');
        }
    }
	
	/**
     * updateTransferAction() used to add/update transfer details
    */
    public function updateTransferAction()
    {
		$this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('update-transfer', 'json')->initContext();
			
			$transferId = $this->_getParam('transferId',null);
			$transferId = filter_var($transferId, FILTER_SANITIZE_NUMBER_INT);
			
			if ((empty($transferId) && $this->_transferAccess['Add'] == 1) || (!empty($transferId) && $this->_transferAccess['Update'] == 1))
			{
				if ($this->getRequest()->isPost())
				{
					$formData = $this->getRequest()->getPost();
					
					$access = array('success' => true);
					
					if ($transferId > 0)
					{
						$transferInfo = $this->_dbTransfer->viewTransfer($transferId);
						
						if (!((($transferInfo['Employee_Id'] == $this->_logEmpId || $transferInfo['Added_By'] == $this->_logEmpId) &&
							   $transferInfo['Status'] == 'Returned') || ($transferInfo['Approver_Id'] == $this->_logEmpId &&
																		  $transferInfo['Status'] == 'Applied')))
						{
							$access = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'warning');
						}
					}
					
					if ($access['success'])
					{
						$comment['value'] = $this->_validation->commonFilters($formData['comment']);
						$comment['valid'] = $this->_validation->lengthValidation($comment, 5, 600, false);
						
						
						$dbPersonal = new Employees_Model_DbTable_Personal();
						$ckAddedBy = $dbPersonal->ckAddedById($formData['employeeId']);
						
						if (!empty($formData['employeeId']) && !empty($formData['approverId']) && !empty($formData['departmentId']) &&
							!empty($formData['locationId']) && !empty($formData['transferDate']) && !empty($formData['status']) && $comment['valid'] &&
							($ckAddedBy == $this->_logEmpId || $this->_logEmpId == $formData['employeeId'] || $this->_transferAccessRights['Admin'] == 'admin')) 
						{
							if( date('m/d/Y') <= $formData['transferDate'] )
							{
								$formData['status'] = ($formData['status'] == "Returned") ? "Applied" : $formData['status'];
								
								$transfer = array('Transfer_Id'     => $transferId,
												  'Employee_Id'     => $formData['employeeId'],
												  'Approver_Id'     => $formData['approverId'],
												  'Approval_Status' => $formData['status'],
												  'Department_Id'   => $formData['departmentId'],
												  'Location_Id'     => $formData['locationId'],
												  'Transfer_Date'   => date('Y-m-d',strtotime($formData['transferDate'])));
								
								$commentArray = array('Emp_Comment'     => htmlentities($comment['value']),
													  'Approval_Status' => $formData['status'],
													  'Employee_Id'     => $this->_logEmpId);
								
                                $customFormName = $this->_ehrTables->getCustomForms($this->_formName);
                                $customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formName);
                                
								$result = $this->_dbTransfer->updateTransfer($transfer, $commentArray, $this->_logEmpId, $this->_formName,$customFormNamee);
								
								//$result = array('success'=>true, 'msg'=>'Invalid Data', 'type'=>'info')
								
								if( $result['success'])
								{
									$checkCondition = 1;
									
									if(empty($transferId))
									{
										$action = "added";
									}
									else
									{
										if($transferInfo['Status'] != 'Returned')
											$checkCondition = 0;
										
										$action = "Updated";
									}
									
									if ( $transfer['Approver_Id'] != $this->_logEmpId && $checkCondition == 1 )
									{
										$dbPersonal = new Employees_Model_DbTable_Personal();
										
										$transferEmpName = $dbPersonal->employeeName($transfer['Employee_Id']);
										
										if($transfer['Employee_Id'] != $this->_logEmpId)
										{
											$msgDescA = "<p>".$customFormNamee." Notification forwarded from ". $transferEmpName['Employee_Name'] ." is waiting for your approval.</p>";
										}
										else
										{
											$logEmpFullName = $dbPersonal->employeeName($this->_logEmpId);
											$msgDescA = "<p>".$customFormNamee." Notification forwarded from ". $logEmpFullName['Employee_Name'] ." for the employee ". $transferEmpName['Employee_Name'] ." is waiting for your approval.</p>";
										}
										
										$this->view->result = $this->_dbCommonFunction->communicateMail (array('employeeId'  => $transfer['Approver_Id'],
																											   'ModuleName'  => 'Employees',
																											   'formName'    => $this->_formName,
																											   'successMsg'  => $customFormNamee,
																											   'customFormName' => $customFormNamee,
																											   'formUrl'     => '/employees/transfer',
																											   'inboxTitle'  => $customFormNamee.' Notification',
																											   'mailContent' => $msgDescA,
																											   'action'      => $action));
									}
									else
									{
										$this->view->result = array('success' => true, 'msg' => $customFormNamee.' '.$action.' successfully', 'type' => 'success');
									}
								}
								else
								{
									$this->view->result = $result;
								}
							}
							else
							{
								$this->view->result = array('success'=>false, 'msg'=>$customFormNamee.' date should not be past', 'type'=>'info');
							}
						}
						else
						{
							$this->view->result = array('success'=>false, 'msg'=>'Invalid Data', 'type'=>'info');
						}
					}
					else
					{
						$this->view->result = $access;
					}
				}
			}
			else
            {
				$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'danger');
            }
		}
        else
        {
            $this->_helper->redirector('index', 'transfer', 'employees');
        }
	}
	
	/**
     *  deleteTransferAction() used to delete transfer details
     */
	public function deleteTransferAction()
    {
	    $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-transfer', 'json')->initContext();
            
			if ($this->_transferAccess['Delete'] == 1)
			{
				$transferId = $this->_getParam('transferId', null);
				$transferId = filter_var($transferId, FILTER_SANITIZE_NUMBER_INT);
				
				if (!empty($transferId) && $transferId > 0)
				{
                    $customFormName = $this->_ehrTables->getCustomForms($this->_formName);
                    $customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formName);
                    
					$this->view->result = $this->_dbTransfer->deleteTransfer($transferId, $this->_logEmpId, $this->_formName,$customFormNamee);
				}
				else
				{
					$this->view->result = array('success'=>false, 'msg'=>'Invalid data', 'type'=>'info');
				}
			}
			else
			{
				$this->view->result = array('success'=>false, 'msg'=>'Access denied', 'type'=>'danger');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'transfer', 'employees');
        }
    }
	
	/**
	 *	List approver names in drowdown
	*/
	public function listApproverNameAction()
    {
	    $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-approver-name', 'json')->initContext();
            
            $employeeId = $this->_getParam('employeeId', null);
            $employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
            
            if (!empty($employeeId) && $employeeId > 0 )
            {
				$dbJobDetail = new Employees_Model_DbTable_JobDetail();
				$isManager = $dbJobDetail->isManager($employeeId);
				
				$this->view->result = $this->_dbTransfer->srManagerName($employeeId, $isManager, '');
            }
        }
        else
        {
            $this->_helper->redirector('index', 'transfer', 'employees');
        }
    }
	
	/**
     * Update transfer status
     */
    public function transferStatusAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if(isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('transfer-status', 'json')->initContext();
			
            $transferId = $this->_getParam('transferId', null);
            $transferId = filter_var($transferId, FILTER_SANITIZE_NUMBER_INT);
			
			if(!empty($transferId))
            {
				$dbComment    = new Payroll_Model_DbTable_PayrollComment();
                $accessStatus = $dbComment->payrollStatus($transferId, $this->_formName);
                
				if($this->_transferAccess['Is_Manager'] == 1 && $accessStatus['Status'] == 'Applied' &&
				   $accessStatus['Approver_Id'] == $this->_logEmpId)
                {
                    if ( $this->getRequest()->isPost() )
                    {
                    	$formData = $this->getRequest()->getPost();
						
						$status   = $this->_getParam('status', null);
						$comments = $this->_getParam('comments', null);
						
						$transferEmp = $this->_dbTransfer->transferEmployee($transferId); //get employee name and employeeId
						
						if(((($status == 'Rejected' || $status == 'Returned') && !empty($comments)) || ($status == 'Approved')) &&
						   !empty($status))
						{
							$cntMgrStatus = $this->_dbTransfer->ckTransferStatus($transferId, $this->_logEmpId, $status);
							
                            $customFormName = $this->_ehrTables->getCustomForms($this->_formName);
                            $customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formName);
                            
							if ( $cntMgrStatus > 0 )
							{
								$this->view->result = array('success' => false, 'msg'=>"You have already updated the status for ".$customFormNamee, 'type'=>'warning');
							}
							else
							{
								$commentArray = array('Approval_Status' => $status,
													  'Emp_Comment'     => $comments,
													  'Parent_Id'       => $transferId );
								
								$statusReport = $this->_dbTransfer->statusReport($commentArray, $this->_logEmpId, $this->_formName,$customFormNamee);
								
								if ( $statusReport)
								{
									$statusMsg = 0;
									$result    = '';
									
									if($this->_logEmpId != $accessStatus['Added_By'])
									{
										if($accessStatus['Added_By'] == $transferEmp['Employee_Id'])
											$msgDescA = "<p>Your Transfer request has been ".strtolower($status)."</p>";
										else
											$msgDescA = "<p>Your Transfer request for the employee ". $transferEmp['Employee_Name'] ." has been ".strtolower($status)."</p>";
										
										$result = $this->_dbCommonFunction->communicateMail (array('employeeId'  => $accessStatus['Added_By'],
																								   'ModuleName'  => 'Employees',
																								   'formName'    => $this->_formName,
																								   'successMsg'  => $customFormNamee,
																								   'customFormName' => $customFormNamee,
																								   'formUrl'     => '/employees/transfer',
																								   'inboxTitle'  => $customFormNamee.' Notification',
																								   'mailContent' => $msgDescA,
																								   'action'      => 'updated'));
									}
									
									if($this->_logEmpId != $transferEmp['Employee_Id'] &&
									   $transferEmp['Employee_Id'] != $accessStatus['Added_By'] && $status == 'Approved')
									{
										$msgDescX = "<p>Your Transfer request has been ".strtolower($status)."</p>";
										
										$result = $this->_dbCommonFunction->communicateMail (array('employeeId'  => $accessStatus['Added_By'],
																								   'ModuleName'  => 'Employees',
																								   'formName'    => $this->_formName,
																								   'successMsg'  => $customFormNamee,
																								   'customFormName' => $customFormNamee,
																								   'formUrl'     => '/employees/transfer',
																								   'inboxTitle'  => $customFormNamee.' Notification',
																								   'mailContent' => $msgDescX,
																								   'action'      => 'updated'));
									}
									
									if (empty($result))
									{
										$this->view->result = array('success' => true, 'msg'=>"Status updated successfully", 'type'=>'success');
									}
									else
									{
										$this->view->result = $result;
									}
								}
								else
								{
									$this->view->result = array('success' => false, 'msg'=>"Unable to update status.Please, contact admin", 'type'=>'info');
								}
							}
						}
						else
						{
							$this->view->result = array('success' => false, 'msg'=>"Invalid data", 'type'=>'info');
						}
					}
                }
                else
                {
                    $this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'info');
                }
			}
			else{
				$this->view->result = array('success'=> false, 'msg' => 'Invalid Data', 'type' => 'warning');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'transfer', 'employees');
        }
    }
	
	public function getLocationPairAction()
    {
        $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('get-location-pair', 'json')->initContext();
            
            $employeeId = $this->_getParam('employeeId', null);
            $employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
			
			$formName = $this->_getParam('formName', null);
            $formName = filter_var($formName, FILTER_SANITIZE_STRIPPED);
            
            if (!empty($employeeId) && $employeeId > 0 && !empty($formName) )
            {
				$dbLocation   = new Organization_Model_DbTable_Location();
				$this->view->result = $dbLocation->getLocationPair($formName, $employeeId);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'transfer', 'employees');
        }
    }

	public function __destruct()
    {
        
    }
}