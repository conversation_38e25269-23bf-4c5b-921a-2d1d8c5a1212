# This is an appspec.yml template file for use with AWS CodeDeploy.
# The lines in this template starting with the hashtag symbol are 
#   instructional comments and can be safely left in the file or 
#   ignored.
# For help completing this file, see the "AppSpec File Reference" in the  
#   "AWS CodeDeploy User Guide" at
#   http://docs.aws.amazon.com/codedeploy/latest/userguide/app-spec-ref.html
version: 0.0
# Specify "os: linux" if this revision targets Amazon Linux, 
#   Red Hat Enterprise Linux (RHEL), or Ubuntu Server  
#   instances.
# Specify "os: windows" if this revision targets Windows Server instances.
# (You cannot specify both "os: linux" and "os: windows".)
os: linux 
# os: windows
# During the Install deployment lifecycle event (which occurs between the 
#   BeforeInstall and AfterInstall events), copy the specified files 
#   in "source" starting from the root of the revision's file bundle 
#   to "destination" on the Amazon EC2 instance.
# Specify multiple "source" and "destination" pairs if you want to copy 
#   from multiple sources or to multiple destinations.
# If you are not copying any files to the Amazon EC2 instance, then remove the
#   "files" section altogether. A blank or incomplete "files" section
#   may cause associated deployments to fail.
files:
  - source: /
    destination: /opt/bitnami/apps/upshothr
  
# For deployments to Amazon Linux, Ubuntu Server, or RHEL instances,
#   you can specify a "permissions" 
#   section here that describes special permissions to apply to the files 
#   in the "files" section as they are being copied over to 
#   the Amazon EC2 instance.
#   For more information, see the documentation.
# If you are deploying to Windows Server instances,
#   then remove the 
#   "permissions" section altogether. A blank or incomplete "permissions"
#   section may cause associated deployments to fail.

# If you are not running any commands on the Amazon EC2 instance, then remove 
#   the "hooks" section altogether. A blank or incomplete "hooks" section
#   may cause associated deployments to fail.

# For each deployment lifecycle event, specify multiple "location" entries 
#   if you want to run multiple scripts during that event.
# You can specify "timeout" as the number of seconds to wait until failing the deployment 
#   if the specified scripts do not run within the specified time limit for the 
#   specified event. For example, 900 seconds is 15 minutes. If not specified, 
#   the default is 1800 seconds (30 minutes).
#   Note that the maximum amount of time that all scripts must finish executing 
#   for each individual deployment lifecycle event is 3600 seconds (1 hour). 
#   Otherwise, the deployment will stop and AWS CodeDeploy will consider the deployment
#   to have failed to the Amazon EC2 instance. Make sure that the total number of seconds 
#   that are specified in "timeout" for all scripts in each individual deployment 
#   lifecycle event does not exceed a combined 3600 seconds (1 hour).
# For deployments to Amazon Linux, Ubuntu Server, or RHEL instances,
#   you can specify "runas" in an event to
#   run as the specified user. For more information, see the documentation.
#   If you are deploying to Windows Server instances,
#   remove "runas" altogether.
# If you do not want to run any commands during a particular deployment
#   lifecycle event, remove that event declaration altogether. Blank or 
#   incomplete event declarations may cause associated deployments to fail.
# During the ApplicationStop deployment lifecycle event, run the commands 
#   in the script specified in "location" starting from the root of the 
#   revision's file bundle.

hooks:
#  ApplicationStop:
#    - location: /codedeploy-scripts/applicationstop.sh
  BeforeInstall:
    - location: /codedeploy-scripts/beforeInstall.sh
  AfterInstall:
    - location: /codedeploy-scripts/afterInstall.sh

# During the BeforeInstall deployment lifecycle event, run the commands 
#   in the script specified in "location".
  
# During the AfterInstall deployment lifecycle event, run the commands 
#   in the script specified in "location".
  
# During the ApplicationInstall deployment lifecycle event, run the commands 
#   in the script specified in "location".
  
# During the ValidateService deployment lifecycle event, run the commands 
#   in the script specified in "location".
  
