<?php
//=========================================================================================
//=========================================================================================
/* Program : Manager.php											   				     *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MQL Query to get list of all manager details						     *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Narmadha                Initial Version         	         *
 *                                                                                    	 */
//=========================================================================================
//=========================================================================================
class Default_Model_DbTable_Manager extends Zend_Db_Table_Abstract
{

    protected $_db = null;

    protected $_ehrTables = null;

    public function init()
    {
         
        $this->_ehrTables = new Application_Model_DbTable_Ehr();
        $this->_db = Zend_Registry::get('subHrapp');
    }
    /**
     * to get manager details in a grid
     */
    public function managerName($page, $rows, $sort, $order, $first, $last, $dept, $mail, $designation, $formName, $empId='')
    {
        $qryManager = $this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),
                            array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS J.Employee_Id as count'),'J.Employee_Id', 'J.Emp_Email'))
                    
                    ->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id=J.Employee_Id',
                                array('Employee_Name'=>new Zend_Db_Expr("CASE WHEN J.User_Defined_EmpId!='' AND J.User_Defined_EmpId IS NOT NULL THEN CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name,'-',J.User_Defined_EmpId) ELSE CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name,'-',J.Employee_Id) END")))
                    
                    ->joinInner(array('D'=>$this->_ehrTables->dept), 'D.Department_Id=J.Department_Id',
                                array('D.Department_Name'))
                    
                    ->joinInner(array('Dn'=>$this->_ehrTables->designation), 'Dn.Designation_Id = J. Designation_Id',
                                array('Dn.Designation_Name'))
                    
                    ->where('P.Form_Status = 1')
                    ->where('J.Emp_Status Like ?', 'Active');
        
        if(!empty($empId))
        {
            $qryManager->where('P.Employee_Id != ?', $empId);
        }
        if(!empty($formName) && $formName == 'Billing')
        {
            $qryManager->where('J.Emp_Email IS NOT NULL and J.Emp_Email != ""');
        }
        if ( ! empty($first) && preg_match('/^[a-zA-Z]/', $first) )
        {
            $qryManager->where('P.Emp_First_Name LIKE ?', "$first%");
        }
        if ( ! empty($last) && preg_match('/^[a-zA-Z]/', $last) )
        {
            $qryManager->where('P.Emp_Last_Name LIKE ?', "$last%");
        }
        if ( ! empty($dept) && preg_match('/^[a-zA-Z]/', $dept) )
        {
            $qryManager->where('D.Department_Name LIKE ?', "$dept%");
        }
        if ( ! empty($designation) && preg_match('/^[a-zA-Z]/', $designation) )
        {
            $qryManager->where('Dn.Designation_Name LIKE ?', "$designation%");
        }
        if ( ! empty($mail) )
        {
            $qryManager->where('J.Emp_Email LIKE ?', "$mail%");
        }
        
        $qryManager->where('P.Is_Manager = 1');
        
        switch($sort)
        {
            case 'Department_Name':
                $qryManager->order("D.$sort $order");
            case 'Designation_Name':
                $qryManager->order("Dn.$sort $order");
            case 'Emp_Email':
                $qryManager->order("J.$sort $order");
            default:
                $qryManager->order("P.$sort $order");
        }
        
        if (!empty($formName) && ($formName == 'Billing' || $formName == 'Employees' || $formName == 'Job Post Requisition'))
        {
            return $this->_db->fetchAll($qryManager);
        }
        else
        {
            $qryManager->limit($rows, ( $page - 1 ) * $rows);
            
            $rowManager = $this->_db->fetchAll($qryManager);
            $countManager = $this->_db->fetchOne('select FOUND_ROWS()');
            $arrManager = array("total"=>$countManager, "rows"=>$rowManager);
            
            return Zend_Json::encode($arrManager);
        }
    }
    /**
     * to get upline manager details in a grid
     */
    public function srManagerName($page, $rows, $sort, $order, $first, $last, $dept, $mail, $designation, $empId, $isManager, $getEmpId, $isMobile)
    {
        if($isManager == 1 && empty($getEmpId))
        {
            $managerId = $empId;
        }
        elseif(!empty($getEmpId))
        {
            $dbPersonal = new Employees_Model_DbTable_Personal();
            $getManagerId = $dbPersonal->ckAddedById($getEmpId);
            $empId = !empty($getManagerId) ? $getManagerId : $getEmpId;
            $managerId = $empId;
        }
        else
        {
            $managerId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob, 'Manager_Id')->where('Employee_Id = ?', $empId));
        }
         
        $arrEmpId = array();
        while ( $managerId != NULL || $managerId != 0)
        {
            $qryManagers = $this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),array('J.Manager_Id'))
            ->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'J.Employee_Id=P.Employee_Id')
            ->where('P.Employee_Id = ?', $managerId)->where('P.Is_Manager =1')->where('J.Emp_Status Like ?', 'Active')
            ->where('P.Form_Status = 1');
            $rowManagers = $this->_db->fetchOne($qryManagers);
            if(in_array($rowManagers,$arrEmpId) || empty($rowManagers) || $empId == $rowManagers)
            {
                break;
            }
            else
            {
                array_push($arrEmpId,$rowManagers);
                $managerId = $rowManagers;
            }
        }

        if ( empty($arrEmpId) && empty($getEmpId))
        {
            array_push($arrEmpId, $empId);
             
        }
        if(!empty($getEmpId))
        {
            array_push($arrEmpId, $getManagerId);
        }
         
        $qrySrManager = $this->_db->select()
        ->from(array('J'=>$this->_ehrTables->empJob), array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS J.Employee_Id as count'), 'J.Emp_Email','Manager_Id' => 'J.Employee_Id'))
        ->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id=J.Employee_Id', array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name)")))
        ->joinInner(array('D'=>$this->_ehrTables->dept), 'D.Department_Id=J.Department_Id', array('D.Department_Name'))
        ->joinInner(array('Dn'=>$this->_ehrTables->designation), 'Dn.Designation_Id = J. Designation_Id', array('Dn.Designation_Name'))
        ->where('J.Employee_Id In (?)', $arrEmpId)->where('P.Form_Status = 1')->where('J.Emp_Status Like ?', 'Active');
        if ( ! empty($first) && preg_match('/^[a-zA-Z]/', $first) )
        {
            $qrySrManager->where('P.Emp_First_Name LIKE ?', "$first%");
        }
        if ( ! empty($last) && preg_match('/^[a-zA-Z]/', $last) ) {
            $qrySrManager->where('P.Emp_Last_Name LIKE ?', "$last%");
        }
        if ( ! empty($dept) && preg_match('/^[a-zA-Z]/', $dept) ) {
            $qrySrManager->where('D.Department_Name LIKE ?', "$dept%");
        }
        if ( ! empty($designation) && preg_match('/^[a-zA-Z]/', $designation)) {

            $qrySrManager->where('Dn.Designation_Name LIKE ?', "$designation%");
        }
        if ( ! empty($mail) ) {
            $qrySrManager->where('J.Emp_Email LIKE ?', "$mail%");
        }
        
        $qrySrManager->where('P.Is_Manager = 1');
        switch ($sort)
        {
            case 'Department_Name':
                $qrySrManager->order("D.$sort $order");
            case 'Emp_Email':
                $qrySrManager->order("J.$sort $order");
            default:
                $qrySrManager->order("P.$sort $order");
        }
        if(!$isMobile)
        	$qrySrManager->limit($rows, ( $page - 1 ) * $rows);

        $rowSrManager = $this->_db->fetchAll($qrySrManager);
        $countSrManager = $this->_db->fetchOne('select FOUND_ROWS()');
        $arrSrManager = array("total"=>$countSrManager, "rows"=>$rowSrManager);

        return Zend_Json::encode($arrSrManager);
    }
    /**
     * to get the manager id by the logged in employee name
     * if he is a manager
     */
    public function managerInfo($logEmpId)
    {
        $qryIsManager = $this->_db->select()->from($this->_ehrTables->empPersonal, array('Employee_Id'))->where('Is_Manager = 1')->where('Employee_Id = ?', $logEmpId)->where('Form_Status = 1');
        $qryEmpManager = $this->_db->select()->from(array('J'=>$this->_ehrTables->empJob), array('Manager_Id'))
        ->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'J.Manager_Id=P.Employee_Id',array())
        ->where('J.Employee_Id =('.$qryIsManager.')')->where('P.Form_Status = 1')->where('J.Emp_Status Like ?', 'Active');
        $rowEmpManager = $this->_db->fetchRow($qryEmpManager);
        return $rowEmpManager;
    }
    
    /**
     * Get manager details to show in popup to select approver
     */
    public function countManager($empId, $isManager)
    {
        $arrEmpId = array();
        
        if($isManager == 1)
        {
            $managerId = $empId;
            
            array_push($arrEmpId,$managerId);
        }
        else
        {
            $managerId = $this->_db->fetchOne($this->_db->select()->from(array('J'=>$this->_ehrTables->empJob), array('Manager_Id'))
            ->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'J.Manager_Id=P.Employee_Id',array())
            ->where('P.Form_Status = 1')
            ->where('J.Employee_Id = ?', $empId)
            ->where('J.Emp_Status Like ?', 'Active'));
            
            if(!empty($managerId))
            array_push($arrEmpId,$managerId);
        }
         
        while ( $managerId != NULL || $managerId != 0)
        {
            $qryManagers = $this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),array('J.Manager_Id'))
            ->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'J.Employee_Id=P.Employee_Id',array())
            ->where('P.Employee_Id = ?', $managerId)
            ->where('P.Is_Manager =1')
            ->where('P.Form_Status = 1')
            ->where('J.Emp_Status Like ?', 'Active');
            $rowManagers = $this->_db->fetchOne($qryManagers);
            if(in_array($rowManagers,$arrEmpId) || empty($rowManagers) || $empId == $rowManagers)
            {
                break;
            }
            elseif(!empty($rowManagers))
            {
                array_push($arrEmpId,$rowManagers);
                $managerId = $rowManagers;
            }
        }
        return $arrEmpId;

    }
    
    /**
     * to get number of employee id who works under a particular manager
     */
    public function employeeId($logId){
        $qryEmpId = $this->_db->select()->from(array('J'=>$this->_ehrTables->empJob), 'Employee_Id')
    				->joinInner(array('P'=>$this->_ehrTables->empPersonal),'P.Employee_Id=J.Employee_Id',array())
    				->where('J.Manager_Id = ?', $logId)->where('P.Form_Status = 1')->where('J.Emp_Status Like ?', 'Active');
        $rowEmpId = $this->_db->fetchCol($qryEmpId);
        return $rowEmpId;
    }
    
    /**
     * Get upline manager details by employeeId
     */
    public function countUplineManager($empId)
    {         
        $managerId = $empId;
        $arrEmpId=array();
        while ( $managerId != NULL )
        {
            $qryManagers=$this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),array('J.Manager_Id'))
            ->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'J.Employee_Id=P.Employee_Id', array())
            ->where('P.Employee_Id = ?', $managerId)
            ->where('P.Form_Status = 1')
            ->where('J.Emp_Status Like ?', 'Active');
             
            $rowManagers=$this->_db->fetchOne($qryManagers);
            if(in_array($rowManagers,$arrEmpId) || empty($rowManagers) || $empId == $rowManagers)
            {
                break;
            }
            else
            {
                array_push($arrEmpId,$rowManagers);
                $managerId= $rowManagers;
            }
        }
        if(empty($arrEmpId))
        {
            array_push($arrEmpId, $empId);
        }
        return $arrEmpId;
    }
    
    /**
     * Get reportee employee ids for given managerId(Logged in employee)
     */
    public function managerEmpId($logEmpId, $role)
    {
        if ($role == "admin")
        {
            $mgrEmpQry = $this->_db->select()
            ->from(array('emp'=>$this->_ehrTables->empPersonal),array('emp.Employee_Id'))
            ->where('emp.Form_Status =?',1);
             
        }
        else
        {
            $conditions = $this->_db->quoteInto('job.Manager_Id = ? or ', $logEmpId) .
            $this->_db->quoteInto('job.Employee_Id = ? ', $logEmpId);
             
            $mgrEmpQry = $this->_db->select()
            ->from(array('job'=>$this->_ehrTables->empJob),array('job.Employee_Id'))
            ->joinInner(array('emp'=>$this->_ehrTables->empPersonal), 'job.Employee_Id = emp.Employee_Id', array())
            ->where('emp.Form_Status =?',1)->where('job.Emp_Status Like ?', 'Active')
            ->where($conditions);
        }

        return $this->_db->fetchCol($mgrEmpQry);
    }
    
    //to fetch the employee and his manager details
    public function getEmployeeManager($employeeId)
    {

        $this->_db->setFetchMode(Zend_Db::FETCH_ASSOC);

        $getManager = $this->_db->select()
        ->from(array('emp' => $this->_ehrTables->empPersonal),
        array(new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as EmployeeName"),'emp.Employee_Id as Employee_Id'))
        ->joinLeft(array('job'=>$this->_ehrTables->empJob),'emp.Employee_Id=job.Employee_Id',
        array('job.Emp_Email as EmployeeEmail', 'job.Manager_Id'))
        ->joinLeft(array('man' => $this->_ehrTables->empPersonal), 'job.Manager_Id = man.Employee_Id',
        array(new Zend_Db_Expr("CONCAT(man.Emp_First_Name, ' ', man.Emp_Last_Name) as ManagerName")))
        ->joinLeft(array('job1'=>$this->_ehrTables->empJob),'job.Manager_Id=job1.Employee_Id',
        array('job1.Emp_Email as ManagerEmail'))
        ->joinLeft(array('des'=>$this->_ehrTables->designation),'des.Designation_Id=job.Designation_Id',array('des.Grade_Id'))
        ->where('emp.Employee_Id = ' . $employeeId);

        $employeeManager=$this->_db->fetchRow($getManager);

        return $employeeManager;
    }
    
    /**
     *
     * This function is to get Manager name and id for employee
     */
    public function getMgrJob($employeeId)
    {
        $this->_db->setFetchMode(Zend_Db::FETCH_ASSOC);
        $srMgrQry = $this->_db->select()->distinct()
        ->from(array('job'=>$this->_ehrTables->empJob),array('Manager_Id'))
        ->joinLeft(array('personal'=>$this->_ehrTables->empPersonal),'job.Manager_Id=personal.Employee_Id',
    				array('Employee_Name'=>new Zend_Db_Expr("CONCAT(personal.Emp_First_Name,' ',personal.Emp_Last_Name)")))
        ->where('job.Employee_Id = ?', $employeeId)->where('job.Emp_Status Like ?', 'Active');
        $srMgrResult = $this->_db->fetchRow($srMgrQry);
        return $srMgrResult;
    }
    
    //to list manager name in employee import inline edit
    public function getManagers()
    {
    	$qryManager = $this->_db->select()
    	->from(array('J'=>$this->_ehrTables->empJob),  array())
    	->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id=J.Employee_Id', array('value'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name)"), 'text'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name)")))
    	->where('P.Form_Status = 1')->where('J.Emp_Status Like ?', 'Active');
    	 
    	$qryManager->where('P.Is_Manager = 1');
    	return $this->_db->fetchAll($qryManager);
    
    }
    
    //to list manager name in employee import inline edit
    public function getManagerPair()
    {
        return $this->_db->fetchCol($this->_db->select()->from(array('J'=>$this->_ehrTables->empJob), array())
                                    
                                    ->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id=J.Employee_Id',
                                                array('value'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name)"), 'text'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name)")))
                                    ->where('P.Form_Status = 1')
                                    ->where('J.Emp_Status Like ?', 'Active')
                                    ->where('P.Is_Manager = 1'));
    }

    public function __destruct()
    {
        
    }

}


