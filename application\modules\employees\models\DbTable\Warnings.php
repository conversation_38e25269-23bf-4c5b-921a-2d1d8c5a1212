<?php
//=========================================================================================
//=========================================================================================
/* Program : Warnings.php											   			         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MQL Query to retrive, add, update and delete employee warnings.		 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                   Description                       *
 *  0.1        17-Aug-2013    Narmadha		           Initial Version        	         *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Devirani                 Changes in file for mobile app    *
 *                                                     1.Extra fields are added in       *
 *                                                     field list of list query.         */
//=========================================================================================
//=========================================================================================

class Employees_Model_DbTable_Warnings extends Zend_Db_Table_Abstract
{
	protected $_db          = null;
    protected $_ehrTables   = null;
    protected $_dbCommonFun = null;
	
    public function init()
    {
        $this->_ehrTables   = new Application_Model_DbTable_Ehr();
        $this->_db          = Zend_Registry::get('subHrapp');
        $this->_dbCommonFun = new Application_Model_DbTable_CommonFunction();
    }
	
	/**
	 * Get warning details to show in a grid
	 */
    public function listWarnings ($page, $rows, $sortField, $sortOrder, $searchAll, $searchArr, $warningUser)
    {
		/**
		 *	get organization date format
		*/
		$orgDF = $this->_ehrTables->orgDateformat();
		
		/**
		 *	declare array values
		*/
		$employeeName     = $searchArr['EmployeeName'];
		$warningBeginDate = $searchArr['WarningBeginDate'];
		$warningEndDate   = $searchArr['WarningEndDate'];
		
        /**
		 *	Sorting columns based on display column order in grid
		*/
		switch ($sortField)
		{
			//case 0: $sortField = 'W.Added_On';break;
			case 1: $sortField = 'EJ.User_Defined_EmpId'; break;
			case 2: $sortField = 'P.Emp_First_Name'; break;
			case 3: $sortField = 'W.Warning_Date'; break;
		}
		
		/**
		 *	Query to fetch data from various tables
		*/
        $qryWarning = $this->_db->select()
							->from(array('W'=>$this->_ehrTables->warnings),
								   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS W.Warning_Id as Count'), 'W.Warning_Id', 'W.Added_By',
										 'W.WarningTo_Id', 'W.Warning_Date', 'W.Description',
										 new Zend_Db_Expr("DATE_FORMAT(W.Added_On,'".$orgDF['sql']." %H:%i:%s') as Added_On"),
										 new Zend_Db_Expr("DATE_FORMAT(W.Updated_On,'".$orgDF['sql']." %H:%i:%s') as Updated_On"),
										 new Zend_Db_Expr("DATE_FORMAT(W.Warning_Date,'".$orgDF['sql']."') as WarningDate"),
										 'Log_Id' => new Zend_Db_Expr($warningUser['LogId']), 
										 'DT_RowClass' => new Zend_Db_Expr('"warnings"'), 
										 'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', W.Warning_Id)")))
							
							->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id = W.WarningTo_Id',
										array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name, ' ', P.Emp_Last_Name)")))
							
							 ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'P.Employee_Id=EJ.Employee_Id',
                                            array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN P.Employee_Id ELSE EJ.User_Defined_EmpId END')))

							
							->joinInner(array('PA'=>$this->_ehrTables->empPersonal), 'PA.Employee_Id = W.Added_By',
									   array('Added_By_Name'=>new Zend_Db_Expr("CONCAT(PA.Emp_First_Name, ' ', PA.Emp_Last_Name)")))
							
							->joinLeft(array('PB'=>$this->_ehrTables->empPersonal), 'PB.Employee_Id = W.Updated_By',
									   array('Updated_By_Name'=>new Zend_Db_Expr("CONCAT(PB.Emp_First_Name, ' ', PB.Emp_Last_Name)")))
							
							->order("$sortField $sortOrder")
							->limit($rows, $page);
        
        if (empty($warningUser['Admin']))
        {
            $employeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
												  ->where('Manager_Id = ?', $warningUser['LogId']));
            
			if ( $warningUser['Is_Manager'] == 1 && !empty($employeeId)) {
                $qryWarning
					->where('W.WarningTo_Id = :EmpId or W.WarningTo_Id IN (?)', $employeeId)
					->bind(array('EmpId' => $warningUser['LogId']));
            }
            else
			{
                $qryWarning->where('W.WarningTo_Id = ?', $warningUser['LogId']);
            }
        }
        
        /**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			//$conditions = $this->_db->quoteInto('P.Emp_First_Name Like ?', "%$searchAll%");
			//$conditions .= $this->_db->quoteInto('or P.Emp_Last_Name Like ?', "%$searchAll%");
            $conditions = $this->_db->quoteInto(new Zend_Db_Expr('Concat(P.Emp_First_Name," ",P.Emp_Last_Name) Like ?'),"%$searchAll%");
			$conditions .= $this->_db->quoteInto('or W.Warning_Date Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or EJ.User_Defined_EmpId Like ?', "%$searchAll%");
			
			$qryWarning->where($conditions);
		}
		
		/* Filter for Employee Name */
		if ($employeeName != '' && $employeeName != null && preg_match('/^[a-zA-Z]/', $employeeName)) {
			//$qryWarning->where($this->_db->quoteInto('P.Emp_First_Name Like ? or ', "%$employeeName%").
			//				   $this->_db->quoteInto('P.Emp_Last_Name Like ?', "%$employeeName%"));
            
            $qryWarning->where($this->_db->quoteInto(new Zend_Db_Expr('Concat(P.Emp_First_Name," ",P.Emp_Last_Name) Like ?'),"%$employeeName%"));
		}
		
		/* Filter for Warning Date */
		if ($warningBeginDate != '') {
			$qryWarning->where($this->_db->quoteInto('W.Warning_Date >= ?', $warningBeginDate));
		}
		
		if ($warningEndDate != '') {
			$qryWarning->where($this->_db->quoteInto('W.Warning_Date <= ?', $warningEndDate));
		}


		$qryWarning = $this->_dbCommonFun->getDivisionDetails($qryWarning,'EJ.Department_Id');
		/**
		 * SQL queries
		 * Get data to display
		*/
		$warnings = $this->_db->fetchAll($qryWarning);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		/* Total data set length */
		$qryWarningCnt = $this->_db->select()->from($this->_ehrTables->warnings, new Zend_Db_Expr('COUNT(Warning_Id)'));
		
		if (empty($warningUser['Admin']))
        {
            $employeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
												  ->where('Manager_Id = ?', $warningUser['LogId']));
			
			if ( $warningUser['Is_Manager'] == 1 && !empty($employeeId)) {
				$qryWarningCnt
					/** if alice name is not used in the select qry then in where condition also alice name should not be used hence W is removed from W.WarningTo_Id **/
					->where('WarningTo_Id = :EmpId or WarningTo_Id IN (?)', $employeeId)
					->bind(array('EmpId' => $warningUser['LogId']));
            }
            else
			{
				/** if alice name is not used in the select qry then in where condition also alice name should not be used hence W is removed from W.WarningTo_Id **/
                $qryWarningCnt->where('WarningTo_Id = ?', $warningUser['LogId']);
            }
        }
		
		$iTotal = $this->_db->fetchOne($qryWarningCnt);
		
		/**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $warnings);
    }
	
	/**
	 *	add/update Warning Details
	*/
	public function updateWarning ($warningData, $warningId, $sessionId,$customFormName)
	{
		/**
		 *	Check Warning is already exists or not in database
		*/
		$qryWarning = $this->_db->select()
									->from($this->_ehrTables->warnings, array(new Zend_Db_Expr('COUNT(Warning_Id)')))
									->where('WarningTo_Id = ?', $warningData['WarningTo_Id'])
									->where('Warning_Date = ?', $warningData['Warning_Date']);
        
		if (!empty($warningId))
        {
            $qryWarning->where('Warning_Id != ?', $warningId);
        }
        
		$checkExists = $this->_db->fetchOne($qryWarning);
		
		/**
		 *	Check warning details if exists return error message or process add/update function
		*/
		if ($checkExists == 0)
		{
			/**
			 *	Check warning id is greater than zero for run update process
			*/
			if ($warningId > 0)
			{
				$action = 'Edit';
				
				$warningData['Lock_Flag']  = 0;
				$warningData['Updated_By'] = $sessionId;
				$warningData['Updated_On'] = date('Y-m-d H:i:s');
				
				$updated = $this->_db->update($this->_ehrTables->warnings, $warningData, array('Warning_Id = ?'=>$warningId));
			}
			/**
			 *	If warning id is empty then we process insertion
			*/
			else
			{
				$action = 'Add';
				
				$warningData['Added_By'] = $sessionId;
				$warningData['Added_On'] = date('Y-m-d H:i:s');
				
				$updated = $this->_db->insert($this->_ehrTables->warnings, $warningData);
				
				if ($updated)
					$warningId = $this->_db->lastInsertId();
			}
			
			/**
			 *	this function will handle
			 *		update system log function
			 *		clear submit lock fucntion
			 *		return success/failure array
			*/
			$result = $this->_dbCommonFun->updateResult (array('updated'        => $updated,
															   'action'         => $action,
															   'trackingColumn' => $warningId,
															   'formName'       => $customFormName,
															   'sessionId'      => $sessionId,
															   'tableName'      => $this->_ehrTables->warnings));
			
			if ($result['success'] && $action == 'Add')
			{
				return $this->_dbCommonFun->communicateMail (array('employeeId' => $warningData['WarningTo_Id'],
																   'ModuleName' => 'Employees',
																   'formName'   => $customFormName,
																   'successMsg' => 'warning',
																   'formUrl'    => '/employees/warnings',
																   'inboxTitle' => 'Warning Notification',
																   'action'     => 'added',
																   'customFormName'   => $customFormName,));
			}
			else
			{
				return $result;
			}
		}
		else
		{
			return array('success' => false, 'msg' => 'Warning already exists', 'type' => 'info');
		}
	}
	
	/**
	 * Delete warning
	 */
    public function deleteWarning($warningId, $sessionId, $customFormName)
    {
		$deleted = 0;
		
		/**
		 *	Get Lock Flag for warning id
		*/
        $warningLock = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->warnings, array('Lock_Flag'))
											->where('Warning_Id = ?', $warningId));
		
		/**
		 *	Check Lock flag is empty or not.
		*/
		if ($warningLock == 0)
		{
			$deleted = $this->_db->delete($this->_ehrTables->warnings, 'Warning_Id='.$warningId);
		}
		
		/**
		 *	delete activity for common function
		 *		1)check lock is exist or not.
		 *			If lock is exist then show error message like employee is open record for update.
		 *		2)If No lockflag then process delete activity
		 *		3)Update delete activity in system log
		 *		4)return success/failure message
		*/
		return $this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
														'tableName'      => $this->_ehrTables->warnings,
														'lockFlag'       => $warningLock,
														'formName'       => $customFormName,
														'trackingColumn' => $warningId,
														'sessionId'      => $sessionId));
    }
	
	public function __destruct()
    {
        
    }		
	
}