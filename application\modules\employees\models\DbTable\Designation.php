<?php
//=========================================================================================
//=========================================================================================
/* Program : Designation.php											   			     *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MQL Query to retrive, add, update designations.						 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Shobana	              Initial Version        	         *
 *  0.2        19-Mar-2014    Sandhosh                Added: getEmployeeById    		 *
 *  0.3		   14-Apr -2014   Sandhosh                Added funtions:					 *
 *										     	      1.getAllowanceCoverageEmp          *
 *											          2.getDesigGradeEmp                 *
 * 													  3.gradeDifference					 *
 *  0.4   	   18-Sep-2014	  Mahesh				  Modified Function	                 *
 *													  1.getDesignationPairs				 *
 *																						 *
 *  1.0        02-Feb-2015    Nivethitha              Changes in file for mobile app     *
 *                                                    1.Extra fields are added in        *
 *                                                    field list of list query.          *
 *                                                                                       *
 *  1.4        25-Feb-2016    Deepak              Changes in file for Bootstrap          *
 *                                                                                       */
//=========================================================================================
//=========================================================================================
class Employees_Model_DbTable_Designation extends Zend_Db_Table_Abstract
{
    protected $_orgDF       = null;
    protected $_db          = null;
    protected $_ehrTables   = null;
	protected $_dbCommonFun = null;

    public function init()
    {
        $this->_ehrTables   = new Application_Model_DbTable_Ehr();
        $this->_dbCommonFun = new Application_Model_DbTable_CommonFunction();
		$this->_db          = Zend_Registry::get('subHrapp');
        $this->_orgDF       = $this->_ehrTables->orgDateformat();
	}
	
	//to list and search designations
    public function listDesignation($page, $rows, $sortField, $sortOrder, $searchAll=null, $searchArr, $isSuperAdmin)
    {
		$designationName    	= $searchArr['designationName'];
		$gradeType          	= $searchArr['gradeType'];
		$probationDaysStart 	= $searchArr['probationDaysStart'];
		$probationDaysEnd   	= $searchArr['probationDaysEnd'];
		$employeeConfirmation 	= $searchArr['employeeConfirmation'];
		$attendanceEnforce      = $searchArr['attendanceEnforce'];
		$attendanceEnforceGeoLocation      = $searchArr['attendanceEnforcedGeoLocation'];
		$noticePeriodPayByEmployer      = $searchArr['noticePeriodPayByEmployer'];
		$noticePeriodPayByEmployee      = $searchArr['noticePeriodPayByEmployee'];
		$status      = $searchArr['status'];

		/**
		 *	Sorting columns based on display column order in grid
		*/
		switch ($sortField)
		{
			case 1: $sortField = 'desig.Designation_Name'; break;
			case 2: $sortField = 'grade.Grade'; break;
			case 3: $sortField = 'desig.Probation_Days'; break;
			case 4: $sortField = 'desig.Employee_Confirmation'; break;
			case 5: $sortField = 'desig.Attendance_Enforced_Payment'; break;

		}
		
        $qryDesignation = $this->_db->select()->from(array('desig'=>$this->_ehrTables->designation),
													 array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS desig.Designation_Id as count'),
														   'desig.Designation_Id','desig.Designation_Name', 'desig.Probation_Days','desig.Employee_Confirmation',
														   'desig.Description', 'desig.Grade_Id','desig.Attendance_Enforced_Payment','desig.Notice_Period_Pay_By_Employer','desig.Notice_Period_Pay_By_Employee',
														   'Attendance_Enforced_Payment_Flag'=>new Zend_Db_Expr("CASE WHEN Attendance_Enforced_Payment='1' Then 'Yes' Else 'No' End"),
														   'desig.Attendance_Enforced_GeoLocation','desig.Notice_Period_Days_Within_Probation','desig.Notice_Period_Days_After_Probation','desig.Designation_Status as Status',
														   'Attendance_Enforced_GeoLocation_Flag'=>new Zend_Db_Expr("CASE WHEN Attendance_Enforced_GeoLocation='1' Then 'Yes' Else 'No' End"),
														   new Zend_Db_Expr("DATE_FORMAT(desig.Added_On,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
														   new Zend_Db_Expr("DATE_FORMAT(desig.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On"),
														   'SuperAdmin'=>new Zend_Db_Expr("'".$isSuperAdmin."'"),
														   'DT_RowClass' => new Zend_Db_Expr('"designation"'),
														   'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', desig.Designation_Id)")))
									
									->joinInner(array('grade'=>$this->_ehrTables->empGrade),'grade.Grade_Id=desig.Grade_Id ',
												array('grade.Grade'))
									
									->joinLeft(array('R'=>$this->_ehrTables->roles),'desig.Designation_Id=R.Designation_Id ',array('Roles_Exists' => new Zend_Db_Expr('count(R.Designation_Id)')))
									
									->joinInner(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=desig.Added_By',
												array(new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as Added_By_Name")))
									
									->joinLeft(array('emp3'=>$this->_ehrTables->empPersonal),'emp3.Employee_Id=desig.Updated_By',
												array(new Zend_Db_Expr("CONCAT(emp3.Emp_First_Name, ' ', emp3.Emp_Last_Name) as Updated_By_Name")))
									
									->group('desig.Designation_Id')
									->order("$sortField $sortOrder")
									->limit($rows, $page);
		
		/**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			$conditions  = $this->_db->quoteInto('desig.Designation_Name  Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or grade.Grade Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or desig.Probation_Days Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or desig.Employee_Confirmation Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or desig.Designation_Status Like ?', "%$searchAll%");
			if(substr_count("yes",strtolower($searchAll)) > 0)
			{
				$conditions .= $this->_db->quoteInto(' or desig.Attendance_Enforced_Payment = ?', 1);
				$conditions .= $this->_db->quoteInto(' or desig.Attendance_Enforced_GeoLocation = ?', 1);
			}
			if(substr_count("no",strtolower($searchAll)) > 0)
			{
				$conditions .= $this->_db->quoteInto(' or desig.Attendance_Enforced_Payment = ?', 0);
				$conditions .= $this->_db->quoteInto(' or desig.Attendance_Enforced_GeoLocation = ?', 0);
			}
			$qryDesignation->where($conditions);		
		}
		
		if (!empty($designationName))
        {
            $qryDesignation->where('desig.Designation_Name Like ?', "$designationName%");
        }
		
		if (!empty($gradeType))
        {
            $qryDesignation->where('desig.Grade_Id = ?', $gradeType);
        }
		
		if (!empty($probationDaysStart))
        {
            $qryDesignation->where('desig.Probation_Days >= ?', $probationDaysStart);
        }
		
		if (!empty($probationDaysEnd))
        {
            $qryDesignation->where('desig.Probation_Days <= ?', $probationDaysEnd);
		}
		

		if (!empty($employeeConfirmation))
        {
            $qryDesignation->where('desig.Employee_Confirmation Like ?', "$employeeConfirmation%");
		}
		
		if (!empty($attendanceEnforce))
        {
			if($attendanceEnforce == 2)
			{
				$attendanceEnforce = 0;
			}
            $qryDesignation->where('desig.Attendance_Enforced_Payment = ?', $attendanceEnforce);
		}
		
		if (!empty($attendanceEnforceGeoLocation))
        {
			if($attendanceEnforceGeoLocation == 2)
			{
				$attendanceEnforceGeoLocation = 0;
			}
			$qryDesignation->where('desig.Attendance_Enforced_GeoLocation = ?', $attendanceEnforceGeoLocation);
		} 

		if(!empty($noticePeriodPayByEmployer)){
			$qryDesignation->where('desig.Notice_Period_Pay_By_Employer = ?', $noticePeriodPayByEmployer);
		}

		if(!empty($noticePeriodPayByEmployee)){
			$qryDesignation->where('desig.Notice_Period_Pay_By_Employee = ?', $noticePeriodPayByEmployee);
		}

		if (!empty($status))
        {
            $qryDesignation->where('desig.Designation_Status = ?', "$status");
        }
		$designation = $this->_db->fetchAll($qryDesignation);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');


		/** get the current month salary date **/
		$dbPayslip  = new Payroll_Model_DbTable_Payslip();
		$currentDate = date('Y-m-d');
		$paycyleDate = $dbPayslip->getSalaryDateRange(date('m',strtotime($currentDate)), date('Y',strtotime($currentDate)),strtotime($currentDate));
		
		/** get salary start date based active employees **/
		if(!empty($paycyleDate))
		{
			$conditions = $this->_db->quoteInto('(EJ.Emp_Status LIKE ? and ', 'InActive') .
						$this->_db->quoteInto('EJ.Emp_InActive_Date >= ?)', $paycyleDate['Salary_Date']).
						' OR '. $this->_db->quoteInto('EJ.Emp_Status LIKE  ?','Active');
		}
		else{
			$conditions = $this->_db->quoteInto('EJ.Emp_Status LIKE ?','Active');
		}
		
		foreach ($designation as $key => $row)
		{
			/** get the salary employee for this designation **/
			$getDesignationBasedSalaryEmp = $this->_db->fetchCol($this->_db->select()->from(array('S'=>$this->_ehrTables->salary), 'S.Employee_Id')
								
											->joinInner(array('EJ'=>$this->_ehrTables->empJob), 'EJ.Employee_Id = S.Employee_Id', array())
											
											->joinInner(array('D'=>$this->_ehrTables->designation),'EJ.Designation_Id = D.Designation_Id', array())
											
											->joinInner(array('G'=>$this->_ehrTables->empGrade),'G.Grade_Id = D.Grade_Id', array())
											
											->where('D.Designation_Id = ?', $row['Designation_Id'])
											->where($conditions));
					
			/** Check that salary employee exists for this designation **/
			if($getDesignationBasedSalaryEmp != '' && count($getDesignationBasedSalaryEmp) > 0)
			{
				$designation[$key]['SalExists'] = 1;	
			}
			else
			{
				$designation[$key]['SalExists'] = 0;	
			}
		}		
		
		/* Total data set length */
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->designation, new Zend_Db_Expr('COUNT(Designation_Id)')));
		
		/**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $designation);
	}
	
	/**
	 *	Update designation details in designation table and update system log too
    */
	public function updateDesignation($desigantionArray, $sessionId, $formName,$oldStatus)
	{
		$qryDesignation = $this->_db->select()->from($this->_ehrTables->designation, new Zend_Db_Expr('Count(Designation_Id)'))
									->where('Designation_Name = ?', $desigantionArray['Designation_Name']);
		
		if ($desigantionArray['Designation_Id'])
		{
			$qryDesignation->where('Designation_Id != ?', $desigantionArray['Designation_Id']);
		}
		
        $isExist = $this->_db->fetchOne($qryDesignation);
		
		if ($isExist == 0)
		{
			if($desigantionArray['Designation_Status']=='InActive' && $oldStatus == 'Active'){
				$designationAssocResponse = $this->isDesignationAssociated($desigantionArray['Designation_Id'],$desigantionArray['Designation_Name'],'edit');
				$isDesignationAssociated = !($designationAssocResponse['Associated_In_Other_Forms'] == 0 && $designationAssocResponse['Associated_In_History'] == 0);
			}else{
				$isDesignationAssociated = 0;
			}
			if($isDesignationAssociated == 0){
				if (!empty($desigantionArray['Designation_Id']))
				{
					$desEmployeeSalaryExist = '';
					
					$oldGradeVal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->designation,'Grade_Id')
														->where('Designation_Id = ?', $desigantionArray['Designation_Id']));				
					
					$action = 'Edit';
					
					$desigantionArray['Updated_On'] = date('Y-m-d H:i:s');
					$desigantionArray['Updated_By'] = $sessionId;
					
					$updated = $this->_db->update($this->_ehrTables->designation, $desigantionArray, array('Designation_Id = '. $desigantionArray['Designation_Id']));
				}
				else
				{
					$action = 'Add';
					
					$desigantionArray['Added_On'] = date('Y-m-d H:i:s');
					$desigantionArray['Added_By'] = $sessionId;
					
					$updated = $this->_db->insert($this->_ehrTables->designation, $desigantionArray);
				}
				
				/** Update the DataSetup status **/
				if($updated)
				{
					$dataSetupStatus = $this->_db->fetchOne($this->_db->select()
														->from($this->_ehrTables->datasetupDashboard,
																		array('Status'))
														->where('Form_Id = ?',21));
					if($dataSetupStatus == 'Open')
					{
						$updated = $this->_db->update($this->_ehrTables->datasetupDashboard, array('Status'=> 'Completed'), array('Form_Id = '. '21'));
					}
				}
				
				/**
				 *	this function will handle
				*		update system log function
				*		clear submit lock fucntion
				*		return success/failure array
				*/
				$result = $this->_dbCommonFun->updateResult (array('updated'        => $updated,
																'action'         => $action,
																'trackingColumn' => $desigantionArray['Designation_Name'],
																'formName'       => $formName,
																'sessionId'      => $sessionId,															   
																'tableName'      => $this->_ehrTables->designation));
				
				/** While edit, If previous grade and new grade differs, set the salary recalculation flag for the employees based on designation **/
				if( !empty($desigantionArray['Designation_Id']) && $result['success'])
				{
					/** If previous and new grade differs **/
					if($oldGradeVal != $desigantionArray['Grade_Id'])
					{
						/** get the current month salary date **/
						$dbPayslip  = new Payroll_Model_DbTable_Payslip();
						
						$currentDate = date('Y-m-d');
						$paycyleDate = $dbPayslip->getSalaryDateRange(date('m',strtotime($currentDate)), date('Y',strtotime($currentDate)),strtotime($currentDate));
						
						/** get salary start date based active employees **/
						if(!empty($paycyleDate))
						{
							$conditions = $this->_db->quoteInto('(EJ.Emp_Status LIKE ? and ', 'InActive') .
										$this->_db->quoteInto('EJ.Emp_InActive_Date >= ?)', $paycyleDate['Salary_Date']).
										' OR '. $this->_db->quoteInto('EJ.Emp_Status LIKE  ?','Active');
						}
						else{
							$conditions = $this->_db->quoteInto('EJ.Emp_Status LIKE ?','Active');
						}
						
						/** get the salary employee for this designation **/
						$getDesignationBasedSalaryEmp = $this->_db->fetchCol($this->_db->select()->from(array('S'=>$this->_ehrTables->salary), 'S.Employee_Id')
											
														->joinInner(array('EJ'=>$this->_ehrTables->empJob), 'EJ.Employee_Id = S.Employee_Id', array())
														
														->joinInner(array('D'=>$this->_ehrTables->designation),'EJ.Designation_Id = D.Designation_Id', array())
														
														->joinInner(array('G'=>$this->_ehrTables->empGrade),'G.Grade_Id = D.Grade_Id', array())
														
														->where('D.Designation_Id = ?', $desigantionArray['Designation_Id'])
														->where($conditions));
						
						/** Check that salary employee exists for this designation **/
						if($getDesignationBasedSalaryEmp != '' && count($getDesignationBasedSalaryEmp) > 0)
						{
							/** update the salary recalculation flag for the salary employee **/
							foreach($getDesignationBasedSalaryEmp as $designationEmp)
							{
								$this->_db->update($this->_ehrTables->salary,array('Salary_Recalc'=>1),array('Employee_Id = "'. $designationEmp.'"'));
							}
							
							return array('success' => true, 'msg'=>$formName.' updated successfully. Please update the salary details for this changed designation employees', 'type'=>'success', 'comboPair' => $this->getDesignationPairs());
						}
						else
						{
							/** If salary employee does not exists for this designation **/
							$result['comboPair'] = $this->getDesignationPairs();
							return $result;	
						}
					}
					else
					{
						/** If previous and new grade does not differs **/
						$result['comboPair'] = $this->getDesignationPairs();
						return $result;
					}
				}
				else
				{
					/** while add designation **/
					$result['comboPair'] = $this->getDesignationPairs();
					return $result;
				}
			}else{
				return array('success'=>false, 'msg'=>'Unable to update '.$formName.' as it is associated in employee job, designation history, individuals, candidate, employee import,skill levels, labour welfare fund, expense type or custom employee group.', 'type'=>'info');
			}
		}
		else
		{
			return array('success' => false, 'msg'=>'Designation Name already exist', 'type'=>'info');
		}
	}

	public function isDesignationAssociated($designationId,$designationName,$source){
		/*check whether any employees designation is associated with this designation id*/
		$exInEmpJobQuery = $this->_db->select()->from($this->_ehrTables->empJob,new Zend_Db_Expr('COUNT(Designation_Id)'))
								->where('Designation_Id = ?', $designationId);

		if($source == 'edit'){
			$exInEmpJobQuery = $exInEmpJobQuery->where('Emp_Status = ?', 'Active');
		}
		$exInJob = $this->_db->fetchOne($exInEmpJobQuery);

		$exInDesignationHistory = 0;
		if($source == 'delete'){
			/*check whether any employees designation history is associated with this designation id*/
			$exInDesignationHistory = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empDesHistory,new Zend_Db_Expr('COUNT(Previous_Designation_Id)'))
											->where('Previous_Designation_Id = ?', $designationId));									
		}
			
		$exInCandidateQuery = $this->_db->select()->from($this->_ehrTables->candidateJob,
								new Zend_Db_Expr('COUNT(Designation_Id)'))
							->where('Designation_Id = ?', $designationId);
		if($source == 'edit'){
			$exInCandidateQuery = $exInCandidateQuery->where('Emp_Status = ?', 'Active');
		}
		$exInCandidate = $this->_db->fetchOne($exInCandidateQuery);

		$exInSkillLevels = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->skillLevels,
								new Zend_Db_Expr('COUNT(Designation_Id)'))
							->where('Designation_Id = ?', $designationId));

		$exInLwf = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->labourWelfareFundDesignation,
								new Zend_Db_Expr('COUNT(Designation_Id)'))
							->where('Designation_Id = ?', $designationId));

		$exInExpenseTypeQuery = $this->_db->select()->from($this->_ehrTables->expenseTypeGrade,
							new Zend_Db_Expr('COUNT(Designation_Id)'))
							->where('Designation_Id = ?', $designationId);
		if($source == 'edit'){
			$exInExpenseTypeQuery = $exInExpenseTypeQuery->where('Status = ?', 'Active');
		}
		$exInExpenseType = $this->_db->fetchOne($exInExpenseTypeQuery);

		// $exInCustomGroup = $this->_dbCommonFun->keyExistInCustomGroup('Designation_Id',$designationId);
		$exInCustomGroup=0;
		return array(
			'Associated_In_Other_Forms' => !($exInJob == 0
			&& $exInCandidate == 0 && $exInSkillLevels == 0 && $exInLwf ==0 && $exInExpenseType == 0 && $exInCustomGroup ==0),
			'Associated_In_History' => $exInDesignationHistory
		);
	}
	
	//delete designation
	public function deleteDesignation($designationId, $sessionId, $formName)
    {
		$designationRow = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->designation,
								array('Lock_Flag','Designation_Name'))
						->where('Designation_Id = ?', $designationId));

		$isDesignationAssociated = $this->isDesignationAssociated($designationId,$designationRow['Designation_Name'],'delete');
		if($isDesignationAssociated['Associated_In_Other_Forms'] == 0 && $isDesignationAssociated['Associated_In_History'] == 0){
			$deleted = 0;
			
            $rolesLock = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->roles, array('Lock_Flag'))
											  ->where('Designation_Id = ?', $designationId));
			
            if($designationRow['Lock_Flag'] == 0 && $rolesLock == 0)
            {
                $deleted=$this->_db->delete($this->_ehrTables->designation,'Designation_Id='.(int)$designationId);
                
                if ($deleted)
                {
					$this->_db->delete($this->_ehrTables->roles, 'Designation_Id='.(int)$designationId);
                }
			}
			
			/**
			 *	delete activity for common function
			 *		1)check lock is exist or not.
			 *			If lock is exist then show error message like employee is open record for update.
			 *		2)If No lockflag then process delete activity
			 *		3)Update delete activity in system log
			 *		4)return success/failure message
			*/
			return $this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
															'lockFlag'       => $designationRow['Lock_Flag'],
															'formName'       => $formName,
															'trackingColumn' => $designationRow['Designation_Name'],
															'sessionId'      => $sessionId,
															'comboPair'      => $this->getDesignationPairs()));
        }
        else
        {
            return array('success'=>false, 'msg'=>'Unable to delete '.$formName.' as it is associated in employees, designation history, individuals, candidate, employee import, skill levels, labour welfare fund, expense type or custom employee group.', 'type'=>'info');
        }
    }
    
	/**
	 * Get designation details to load in a combo
	 */
    public function getDesignationPairs($rolesForm = NULL, $designationId = NULL)
    {
        //$this->_db ->setFetchMode(Zend_Db::FETCH_ASSOC);
		
		$qryDesignation = $this->_db->select()->from(array('d'=>$this->_ehrTables->designation),
													  array('d.Designation_Id','d.Designation_Name'))
													  ->where('d.Designation_Status =?','Active');
													  
			$qryDesignationRoles= $this->_db->select()->from(array('A'=>$this->_ehrTables->roles),
			                           array('A.Designation_Id'))	
			                        ->where('A.Form_Id=?','147')
									->where('A.Role_Optional_Choice =?','1');						
			$getDesignationRoles=$this->_db->fetchCol($qryDesignationRoles);												
												
        //version 0.4=>for filling in Copy Roles drop down field in Emp Roles Form, the designations should have roles added
        if(!is_null($rolesForm))
        {
        	$qryDesignation->joinInner(array('R'=>$this->_ehrTables->roles),'d.Designation_Id=R.Designation_Id ',array(''));
        }	
        
		if(!is_null($designationId))
        {
			$qryDesignation->where('d.Designation_Id != ?', $designationId);
			if(!empty($getDesignationRoles))
			{
				$qryDesignation->where('d.Designation_Id NOT IN (?)',$getDesignationRoles) ;
			}	
		}
	  
		
	     $qryDesignation->order('d.Designation_Name ASC');
        return $this->_db->fetchPairs($qryDesignation);
	}
	
    //to list designations in employee import inline edit    
    public function getDesignations()
    {    
    	return $this->_db->fetchAll($this->_db ->select()->from($this->_ehrTables->designation,
									array('Designation_Name as value', 'Designation_Name as text'))
									->where('Designation_Status = ?','Active')		
									->order('Designation_Name ASC'));
    }
	
	/**
     * get employees for grade 
     * @param array $grades
     */
    public function getEmployeeById($grades)
    {
    	return $this->_db->fetchCol($this->_db->select()->from(array('des'=>$this->_ehrTables->designation),array())
									->joinInner(array('job'=>$this->_ehrTables->empJob),'job.Designation_Id=des.Designation_Id',
												'Employee_Id')
									
									->where('des.Grade_Id IN (?)', $grades));
    }
    
	/**
     * get the difference between 2 grades 
     * @param int $gradeId - exist grade
     * @param int $postGrade - chosen grade
     * @param int $desiId - designation id
     */
    public function gradeDifference($gradeId,$postGrade,$desiId)
    {
    	$dbInsurance = new Payroll_Model_DbTable_Insurance(); 
    	$dbAllowance = new Payroll_Model_DbTable_Allowances(); 
    	$dbGrade     = new Employees_Model_DbTable_Grade();
    	$dbAllowance = new Payroll_Model_DbTable_Allowances();
    	
    	$affectedEmp = $dbInsurance->getEmpLevelInsurance($gradeId,$postGrade);
    	$getPreviousOrgLevelInsurance = $dbInsurance->getOrgLevelInsurance($gradeId);
    	$getCurrentOrgLevelInsurance = $dbInsurance->getOrgLevelInsurance($postGrade);    	 
    	$affectedOrgEmp = array_merge(array_diff($getCurrentOrgLevelInsurance[0], $getPreviousOrgLevelInsurance[0]), array_diff($getPreviousOrgLevelInsurance[0], $getCurrentOrgLevelInsurance[0]));
    	$affOrgEmp = array_merge(array_diff($getCurrentOrgLevelInsurance[1], $getPreviousOrgLevelInsurance[1]),array_diff($getPreviousOrgLevelInsurance[1], $getCurrentOrgLevelInsurance[1]));
    	$affectedEmployee = array();
		
    	if(count($affectedOrgEmp) > 0 || count($affOrgEmp) > 0)
		{
			$affectedEmployee = $this->_db->fetchCol($this->_db->select()->from(array('des'=>$this->_ehrTables->designation),array())
													 
													 ->joinInner(array('job'=>$this->_ehrTables->empJob),
																 'job.Designation_Id = des.Designation_Id','Employee_Id')
													 
													 ->joinInner(array('S'=>$this->_ehrTables->salary),
																 'S.Employee_Id = job.Employee_Id',array())
													 
													 ->where('S.Is_InsuranceEmployee = ?', '1')
													 ->where('des.Designation_Id = ?', $desiId)
													 ->where('des.Grade_Id = ?', $gradeId));
    	}
		
    	$totalemp = array_unique(array_merge($affectedEmp,$affectedEmployee));
    	//allowance
    	$gradeAllowance = $dbAllowance->getGradeAllowances($gradeId);
    	$affectedGradeAllowanceEmp = $this->getAllowanceCoverageEmp($gradeId, $desiId, $gradeAllowance);
    	$gradeAllowance = $dbAllowance->getGradeAllowances($postGrade);
    	$affectedPostGradeAllowanceEmp = $this->getAllowanceCoverageEmp($postGrade, $desiId, $gradeAllowance);
    	$affectedAllowanceEmp = array_unique(array_merge($affectedGradeAllowanceEmp, $affectedPostGradeAllowanceEmp));
    	$totalemp = array_unique(array_merge($totalemp, $affectedAllowanceEmp));
    	$viewPostGrade = $dbGrade->viewGrade($postGrade);
		
		$getSalaryChange = $dbGrade->getSalaryChange (array('Grade_Id'          => $gradeId,
															'Min_AnnualSalary'  => $viewPostGrade['Min_AnnualSalary'],
															'Max_AnnualSalary'  => $viewPostGrade['Max_AnnualSalary'],
															'Min_HourlyWages'   => $viewPostGrade['Min_HourlyWages'],
															'Max_HourlyWages'   => $viewPostGrade['Max_HourlyWages'],
															'Min_OvertimeWages' => $viewPostGrade['Min_OvertimeWages'],
															'Max_OvertimeWages' => $viewPostGrade['Max_OvertimeWages'],
															'Designation_Id'    => $desiId));
		
    	$info = NULL;
    	
		if(count($getSalaryChange[0]) > 0 || count($getSalaryChange[1]) > 0)
    	{
    		$info = 'Please update the salary for this employee';
    		 
    	}
    	
		if(count($totalemp)>0)
    	{
    		$dbAllowance->setSalRecal($totalemp, 1);
    		$info = 'Please update the salary for this employee';
    	}
    	
		return $info;
    }
	
	//to fetch designation based on designation id
    public function viewDesignation($desigId)
    {
        if ($desigId != "")
        {
            $this->_db->setFetchMode(Zend_Db::FETCH_ASSOC);
             
            return $this->_db->fetchRow($this->_db->select()->from(array('des'=>$this->_ehrTables->designation),
																   array('des.Designation_Id','des.Designation_Name', 'des.Description',
																		 new Zend_Db_Expr("DATE_FORMAT(des.Added_On,'".$this->_orgDF['sql']." at %T') as Added_On"),
																		 new Zend_Db_Expr("DATE_FORMAT(des.Updated_On,'".$this->_orgDF['sql']." at %T') as Updated_On"),
																		 'des.Grade_Id','des.Updated_By'))
										
										->joinInner(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=des.Updated_By ',
													array(new Zend_Db_Expr("CONCAT(Emp_First_Name, ' ', Emp_Last_Name) as EmployeeName")))
										
										->joinInner(array('grade'=>$this->_ehrTables->empGrade),'grade.Grade_Id=des.Grade_Id ',
													array('grade.Grade'))
										
										->where('des.Designation_Id = ?', $desigId));
        }
    }
    
	/**
     * get employees based on grade,designation and allowance coverage
     * @param int $gradeId
     * @param int $desiId
     * @param array $gradeAllowance - array(coverage,locations);
     * @return array
     */
    public function getAllowanceCoverageEmp($gradeId, $desiId, $gradeAllowance) 
    {
    	if($gradeAllowance[0] == 'GRADE')
		{
    		return $this->_db->fetchCol($this->_db->select()->from(array('des'=>$this->_ehrTables->designation),array())
								 
								 ->joinInner(array('job'=>$this->_ehrTables->empJob),'job.Designation_Id = des.Designation_Id',
											 'Employee_Id')
								 
								 ->joinInner(array('S'=>$this->_ehrTables->salary),'S.Employee_Id = job.Employee_Id',array())
								 
								 ->where('S.Is_InsuranceEmployee = ?', '1')
								 ->where('des.Designation_Id = ?', $desiId)
								 ->where('des.Grade_Id = ?', $gradeId));
    	}
    	else if($gradeAllowance[0] == 'GRADE_LOC')
		{
    		if(count($gradeAllowance[1]) > 0 )
			{
    			return $this->_db->fetchCol($this->_db->select()->from(array('des'=>$this->_ehrTables->designation),array())
				
											->joinInner(array('job'=>$this->_ehrTables->empJob),
														'job.Designation_Id = des.Designation_Id','Employee_Id')
											
											->joinInner(array('S'=>$this->_ehrTables->salary),'S.Employee_Id = job.Employee_Id',array())
											
											->where('S.Is_InsuranceEmployee = ?', '1')
											->where('des.Designation_Id = ?', $desiId)
											->where('job.Location_Id IN (?)', $gradeAllowance[1])
											->where('des.Grade_Id = ?', $gradeId));
    		}
    		return array();
    	} 
    }
	
	/**
     * get Probation Date based on designation and date of join
     * @param int $designationId
     * @param date $dateofJoin
     * @return probationDate in org format
     */
    public function getProbationDate($designationId, $dateofJoin)
	{
		$dateformat = $this->_ehrTables->orgDateformat();
		
		$getprobationDays = $this->_db->fetchOne($this->_db->select()->from(array('des'=>$this->_ehrTables->designation),array('Probation_Days'))
											->where('des.Designation_Id = ?', $designationId));
		
		$probationDate = '';
		
		if(!empty($getprobationDays))
		{
			$probationDays = '+'.$getprobationDays;
			$probationDate = date('Y-m-d', strtotime( $probationDays."days",strtotime($dateofJoin)));			
		}
		else{
			$probationDate = date('Y-m-d',strtotime($dateofJoin));
		}
		
		return $probationDate;
	}

	public function __destruct()
    {
        
    }
}

