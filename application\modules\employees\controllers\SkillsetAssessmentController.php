<?php
//=========================================================================================
//=========================================================================================
/* Program : SkillsetAssessment.php									                     *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : Manager/Admin can rate the skill level for their reportee for the       *
 * assessment year. Skills are listed from the primary skills of employee details. 		 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        17-Oct-2013    Mahesh                  Initial Version  	                 *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Saranya                 Changed in file for mobile app     *
 *                                                                                       *
 *  1.5        09-Feb-2016    Prasanth               Changed in file for Bootstrap       *
 *							  				                		                     */
//=========================================================================================
//=========================================================================================

include APPLICATION_PATH."/validations/Validations.php";
class Employees_SkillsetAssessmentController extends Zend_Controller_Action
{
    protected $_dbSkillset           = null;
	protected $_dbPersonal           = null;
    protected $_dbAccessRights       = null;
    protected $_skillsetAccessRights = null;
    protected $_logEmpId             = null;
    protected $_ehrTables            = null;
    protected $_validation           = null;
	protected $_formName             = 'Skillset Assessment';
	protected $_hrappMobile = null;
	

    public function init()
    {
		$this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
		if ($this->_hrappMobile->checkAuth())
        {
			$this->_dbCommonFun    = new Application_Model_DbTable_CommonFunction();
			$this->_dbSkillset     = new Employees_Model_DbTable_SkillsAssessment();
            $this->_dbPersonal     = new Employees_Model_DbTable_Personal();
            $this->_dbAccessRights = new Default_Model_DbTable_AccessRights();
            $this->_ehrTables      = new Application_Model_DbTable_Ehr();
            $this->_validation 	   = new Validations();
			
            $userSession                 = $this->_dbCommonFun->getUserDetails ();
            $this->_logEmpId             = $userSession['logUserId'];
			$this->_skillsetAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formName);
			$this->_skillsetUser         = $this->_skillsetAccessRights['Employee'];
			//$this->_dbAccessRights->refreshUserSessionTimestamp($this->_logEmpId);
        }
        else
        {
            if (Zend_Session::namespaceIsset('lastRequest'))
                Zend_Session:: namespaceUnset('lastRequest');
            
            $session = new Zend_Session_Namespace('lastRequest');
            $session->lastRequestUri = 'employees/skillset-assessment';
            $this->_redirect('auth');
        }
    }
	
    public function indexAction()
    {
		$checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

		if ($checkSessionAuth)
		{
			$this->_helper->layout()->disableLayout()->setLayout('admin_layout');
		
			$dbSkilldef = new Employees_Model_DbTable_SkillDefinition();
			
			$this->view->formNameA         = $this->_formName;
			$this->view->customFormNameA = $this->_ehrTables->getCustomForms($this->_formName);
			
			$this->view->skillDefinition  = $dbSkilldef->getTechnicalSkills ();
			$this->view->employeeDetails  = $this->_dbCommonFun->listEmployeesDetails ('Skillset Assessment', '', $this->_logEmpId);
			$this->view->performanceMonth = $this->_dbSkillset->getPerformanceMonth();
			
			$this->view->skillsetUser =  array('Is_Manager'    => $this->_skillsetUser['Is_Manager'],
											   'View'          => $this->_skillsetUser['View'],
											   'Add'           => $this->_skillsetUser['Add'],
											   'Update'        => $this->_skillsetUser['Update'],
											   'Delete'        => $this->_skillsetUser['Delete'],
											   'Admin'         => $this->_skillsetAccessRights['Admin'],
											   'Employee_Name' => $this->_dbPersonal->employeeId($this->_logEmpId));
			$this->view->dateformat = $this->_ehrTables->orgDateformat();
		} else {
			$this->_redirect('auth');
		}
    }

	/**
	 * Get skillset details to show in grid
	 */
    public function listSkillsetAction()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('list-skillset', 'json')->initContext();
			
			if ($this->_skillsetUser['View'] == 1)
			{
				$sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
				
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$employeeName = $this->_getParam('Employee_Name', null);
				$employeeName = filter_var($employeeName, FILTER_SANITIZE_STRIPPED);
				
				$assessmentStartMonth = $this->_getParam('AssessmentStartMonth', null);
				$assessmentStartMonth = filter_var($assessmentStartMonth, FILTER_SANITIZE_STRIPPED);
				
				$assessmentEndMonth = $this->_getParam('AssessmentEndMonth', null);
				$assessmentEndMonth = filter_var($assessmentEndMonth, FILTER_SANITIZE_STRIPPED);				
				
				$skillLevelStart = $this->_getParam('SkillLevelStart', null);
				$skillLevelStart = filter_var($skillLevelStart, FILTER_SANITIZE_NUMBER_INT);
				
				$skillLevelEnd = $this->_getParam('SkillLevelEnd', null);
				$skillLevelEnd = filter_var($skillLevelEnd, FILTER_SANITIZE_NUMBER_INT);
				
				$skillDefinition = $this->_getParam('SkillDefinition', null);
				$skillDefinition = filter_var($skillDefinition, FILTER_SANITIZE_NUMBER_INT);
				
				$skillLevelBegin = $this->_getParam('SkillLevelBegin', null);
				$skillLevelBegin = filter_var($skillLevelBegin, FILTER_SANITIZE_NUMBER_INT);
				
				$skillLevelEnd = $this->_getParam('SkillLevelEnd', null);
				$skillLevelEnd = filter_var($skillLevelEnd, FILTER_SANITIZE_NUMBER_INT);
				
				$skillMatrixUser = array('Is_Manager' => $this->_skillsetUser['Is_Manager'],
										 'Admin'      => $this->_skillsetAccessRights['Admin'],
										 'LogId'      => $this->_logEmpId);
				
				$searchArr = array('Employee_Name'             => $employeeName,
								   'AssessmentStartMonth'      => $assessmentStartMonth,
								   'AssessmentEndMonth'        => $assessmentEndMonth,
								   'SkillLevel_Start'          => $skillLevelStart,
								   'SkillLevel_End'            => $skillLevelEnd,
								   'Skill_Definition'          => $skillDefinition,
								   'SkillLevelBegin'           => $skillLevelBegin,
								   'SkillLevelEnd'             => $skillLevelEnd);
				
				$this->view->result = $this->_dbSkillset->listSkillsetAssessment ($page, $rows, $sortField, $sortOrder, $searchAll,
																				  $searchArr, $skillMatrixUser);
			}
		}
		else
        {
            $this->_helper->redirector('index', 'skillset-assessment', 'employees');
        }
    }
	
	/**
	 * Check skillset exists or not for an employee
	 * and get skillset details if exists
	 */
    public function checkSkillExistAction()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('check-skill-exist', 'json')->initContext();
			
            $skillsetId = $this->_getParam('skillsetId', null);
			$skillsetId = filter_var($skillsetId, FILTER_SANITIZE_NUMBER_INT);
			
            $employeeId = $this->_getParam('employeeId', null);
            $employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
			
			$assessFrom = $this->_getParam('assessmentFrom', null);
			$assessFrom = filter_var($assessFrom, FILTER_SANITIZE_STRIPPED);
			
			if (!empty($skillsetId))
            {
                $clearlock = $this->_dbAccessRights->clearLockFlag($this->_ehrTables->skillset, 'Skillset_Id', $skillsetId);
				
                if ($clearlock)
                {
                    $this->_dbAccessRights->clearSubmitLock($this->_logEmpId, $this->_ehrTables->skillset);
                }
            }
			
			$skillId = $this->_dbSkillset->getSkillId ($employeeId, $assessFrom);
			
			if (!empty($skillId))
            {
                $checkFormLock = $this->_dbAccessRights->checkLockFlag ($skillId, $this->_ehrTables->skillset, 'Skillset_Id');
                
                if ($checkFormLock != 0 && $checkFormLock == $this->_logEmpId)
                {
                    $this->view->result = array('success' => false, 'msg' => 'Same record has been opened by your session in some other browser or system.<br/>If you still have problem in opening the form, contact System Admin', 'type' => 'warning');
                }
                elseif ($checkFormLock != 0 && $checkFormLock != $this->_logEmpId)
                {
                    $editEmpName = $this->_dbPersonal->employeeName ($checkFormLock);
					
					$this->view->result = array('success' => false, 'msg' => $editEmpName['Employee_Name'] . ' is updating this record. Please Wait...', 'type' => 'warning');
                }
                else
                {
					$recordLimit = $this->_dbAccessRights->checkOpenedRecordLimitJS ($this->_logEmpId);
					
                    if ($recordLimit[0])
                    {
                        $setLock = $this->_dbAccessRights->setLockFlag ($this->_logEmpId, $skillId, $this->_ehrTables->skillset, 'Skillset_Id');
                        
						if ($setLock)
                        {
							$isExists = $this->_dbSkillset->checkSkillMatrixExists ($skillId);
							
							$this->view->result = array('success' => true, 'isSkillMatrixExist' => $isExists, 'Skillset_Id' => $skillId);
                        }
						else
						{
							$this->view->result = array('success' => false, 'msg' => 'Unable to open this record for edit', 'type' => 'warning', 'Skillset_Id' => $skillId);
						}
                    }
                    else
					{
						$this->view->result = array('success' => false, 'msg' => 'You don\'t have rights to edit more than '. $recordLimit[1] .' records.', 'type' => 'warning', 'Skillset_Id' => $skillId);
					}
                }
            }
			else
			{
				$this->view->result = array('success' => true, 'msg' => 'No record for this employee id', 'type' => 'success');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'skillset-assessment', 'employees');
        }
    }
	
	/**
	 * Add/Edit skillset Assessment details
	 */
    public function updateSkillsetAssessmentAction()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('update-skillset-assessment', 'json')->initContext();
			
			$skillsetId = $this->_getParam('skillsetId', null);
			$skillsetId = filter_var($skillsetId, FILTER_SANITIZE_NUMBER_INT);
			$skillsetId = $this->_validation->intValidation($skillsetId);
			
            if (($this->_skillsetUser['Is_Manager'] == 1 || !empty($this->_skillsetAccessRights['Admin'])) &&
				(($this->_skillsetUser['Add'] == 1 && empty($skillsetId['value'])) ||
				 ($this->_skillsetUser['Update'] == 1 && !empty($skillsetId['value']) && $skillsetId['valid'])))
            {
                if ($this->getRequest()->isPost())
				{
                    $formData = $this->getRequest()->getPost();
					
					$employeeId = $this->_validation->intValidation($formData['employeeId']);
					$assessFrom = $this->_validation->dateValidation($formData['startDate']);
                    $assessTo = $this->_validation->dateValidation($formData['endDate']);
					$comments['value'] = $this->_validation->commonFilters($formData['comments']);
					$comments['valid'] = $this->_validation->lengthValidation($comments, 5, 500, false);
					
					if (!empty($employeeId['value']) && $employeeId['valid'] && !empty($assessFrom['value']) && $assessFrom['valid']
                        && !empty($assessTo['value']) && $assessTo['valid'] && $comments['valid'])
					{
						//$assessTo    = date('Y-m-d', strtotime('+'. (12-1) .' month', strtotime($assessFrom['value'])));
						$ckAddedBy   = $this->_dbPersonal->ckAddedById($employeeId['value']);
						$dbJobDetail = new Employees_Model_DbTable_JobDetail();
						$dateOfJoin  = $dbJobDetail->getDateOfJoin ($employeeId['value']);
                        
                        if ($employeeId['value'] != $this->_logEmpId && ($assessFrom['value'] > $dateOfJoin) &&
							($ckAddedBy == $this->_logEmpId || !empty($this->_skillsetAccessRights['Admin'])))
						{
							$skillsetData = array('Employee_Id'     => $employeeId['value'],
												  'Assessment_From' => $assessFrom['value'],
												  'Assessment_To'   => $assessTo['value']);
							
                            $customFormName = $this->_ehrTables->getCustomForms($this->_formName);
                            $customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formName);
                            
							$this->view->result = $this->_dbSkillset->updateSkillsetAssessment ($skillsetData,
																								$skillsetId['value'],
																								$this->_formName,
																								$this->_logEmpId,
																								$comments['value'],
                                                                                                $customFormNamee);
						}
						else
						{
							$this->view->result = array('success' => false, 'msg' => 'Invalid Data1', 'type' => 'info');
						}
					}
					else
					{
						$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
					}
                }
            }
            else
            {
                $this->view->result = array('success' => false, 'msg' => 'Sorry, Access Denied', 'type' => 'danger');
            }
        }
		else
		{
			$this->_helper->redirector('index', 'skillset-assessment', 'employees');
		}
    }
	
	/**
	 * Delete skillset
	 */
    public function deleteSkillsetAction()
    {
        $this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('delete-skillset', 'json')->initContext();
			
			if ($this->_skillsetUser['Delete'] == 1 && ($this->_skillsetUser['Is_Manager'] == 1 || !empty($this->_skillsetAccessRights['Admin'])))
            {
				$skillsetId = $this->_getParam('skillsetId', null);
				$skillsetId = filter_var($skillsetId, FILTER_SANITIZE_NUMBER_INT);
				
				if (!empty($skillsetId) && $skillsetId > 0)
				{
                    $customFormName = $this->_ehrTables->getCustomForms($this->_formName);
					$customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formName);
                    
					$this->view->result = $this->_dbSkillset->deleteSkillset ($skillsetId, $this->_logEmpId, $this->_formName,$customFormNamee);
				}
                else
				{
					$this->view->result = array('success' => false, 'msg'=>"Invalid data", 'type'=>'info');
				}
            }
			else
			{
				$this->view->result = array('success' => false, 'msg'=>"Access denied", 'type'=>'warning');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'skillset-assessment', 'employees');
        }
    }
	
	/**
	 * Get skill matrix details to show in subgrid
	 */
    public function listSkillMatrixAction()
    {
        $this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('list-skill-matrix', 'json')->initContext();
			
            if ($this->_skillsetUser['View'] == 1)
            {
                $requestId = $this->_getParam('requestId', null);
				$requestId = filter_var($requestId, FILTER_SANITIZE_NUMBER_INT);
				
                $sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
				
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$this->view->result = $this->_dbSkillset->listSkillMatrix ($page, $rows, $sortField, $sortOrder, $searchAll, $requestId);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'skillset-assessment', 'employees');
        }
    }
	
	/**
	 *	Add / Edit Skill Matrix
	*/
	public function updateSkillMatrixAction()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('update-skill-matrix', 'json')->initContext();
			
			$skillsetId = $this->_getParam('skillsetId', null);
			$skillsetId = filter_var($skillsetId, FILTER_SANITIZE_NUMBER_INT);
			$skillsetId = $this->_validation->intValidation($skillsetId);
			
			if (($this->_skillsetUser['Is_Manager'] == 1 || !empty($this->_skillsetAccessRights['Admin'])) &&
				($this->_skillsetUser['Add'] == 1 || $this->_skillsetUser['Update'] == 1) && /*!empty($skillsetId['value']) &&*/
				$skillsetId['valid'])
            {
                if ($this->getRequest()->isPost())
				{
                    $formData = $this->getRequest()->getPost();
					
					$skillMatrixId     = $this->_validation->intValidation($formData['skillMatrixId']);
					$skillDefinitionId = $this->_validation->intValidation($formData['skillDefinitionId']);
					$skillLevel        = $this->_validation->intValidation($formData['skillLevel']);
					
					if (!empty($skillDefinitionId['value']) && $skillDefinitionId['valid'] && !empty($skillLevel['value']) &&
						$skillLevel['valid'])
					{
						$skillMatrixData = array('Skillset_Id'        => $skillsetId['value'],
												 'SkillDefinition_Id' => $skillDefinitionId['value'],
												 'Skill_Level'        => $skillLevel['value']);
						
						$this->view->result = $this->_dbSkillset->updateSkillMatrix ($skillMatrixData, $skillMatrixId['value'], $this->_logEmpId);
					}
					else
					{
						$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
					}
                }
            }
            else
            {
                $this->view->result = array('success' => false, 'msg' => 'Sorry, Access Denied', 'type' => 'danger');
            }
        }
		else
		{
			$this->_helper->redirector('index', 'skillset-assessment', 'employees');
		}
    }
	
	/**
	 *	Delete Skill Matrix record
	*/
	public function deleteSkillMatrixAction()
    {
        $this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('delete-skill-matrix', 'json')->initContext();
			
			$skillMatrixId = $this->_getParam('skillMatrixId', null);
			$skillMatrixId = filter_var($skillMatrixId, FILTER_SANITIZE_NUMBER_INT);
			
			if (!empty($skillMatrixId) && $skillMatrixId > 0)
			{
				$this->view->result = $this->_dbSkillset->deleteSkillMatrix ($skillMatrixId, $this->_logEmpId);
			}
			else
			{
				$this->view->result = array('success' => false, 'msg'=>'Invalid data', 'type'=>'info');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'skillset-assessment', 'employees');
        }
    }

	public function __destruct()
    {
        
    }
}