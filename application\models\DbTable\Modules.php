<?php
//=========================================================================================
//=========================================================================================
/* Program        : Modules.php		 													 *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description    : Mysql query for retrieving modules and forms for main layout      	 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions      :                                                                    	 *
 *   Version         Date           Author                  Description                  *
 *     0.1        30-May-2013      Narmadha               Initial Version                *
 *                                                                                    	 *
 *                                                                                    	 */
//=========================================================================================
//=========================================================================================
class Application_Model_DbTable_Modules extends Zend_Db_Table_Abstract
{

    protected $_db = null;

    protected $_ehrTables      = null;
    protected $_dbAccessRights = null;

    public function init()
    {
         
        $this->_ehrTables = new Application_Model_DbTable_Ehr();
        $this->_dbAccessRights    = new Default_Model_DbTable_AccessRights();
        $this->_db = Zend_Registry::get('subHrapp');
    }
	
    // to get all the menus
    public function getModules($allForms=NULL)
	{
        $this->_appManagerDb = Zend_Registry::get('Hrapp');
        
        $orgcode = $this->_ehrTables->getOrgCode();
        
        $moduleId = $this->_appManagerDb->fetchCol($this->_appManagerDb->select()->from(array('RG'=>$this->_ehrTables->regUser),
                                                       array())
                                                ->joinLeft(array('BR'=>$this->_ehrTables->billingRate),'RG.Billing_Id=BR.Billing_Id',
                                                                array(''))
                                                ->joinLeft(array('PF'=>$this->_ehrTables->planforms),'PF.Plan_Id=BR.Plan_Id',
                                                                array(''))
                                                ->joinLeft(array('AF'=>$this->_ehrTables->appForms),'PF.Form_Id=AF.Form_Id',
                                                                array('Module_Id'))
                                                ->where('RG.Org_Code = ?',$orgcode)
                                                ->group('AF.Module_Id'));
        
		/** To get all modules **/
		if($allForms == 'All')
		{
			return $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->modules, array('Module_Id', 'Module_Name'))
                                    ->where('Module_Id IN (?)',$moduleId)									
									->order("Module_Sequence Asc"));	
		}
		/** To get all modules except admin roles **/
		else
		{
			return $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->modules, array('Module_Id', 'Module_Name'))
                                    ->where('Module_Id IN (?)',$moduleId)
									->where('Is_Visible = ?','1')
									->order("Module_Sequence Asc"));
		}
    }
	
    // to get all the submenus
    public function getForms($moduleId)
	{
        $this->_appManagerDb = Zend_Registry::get('Hrapp');
        
        $orgcode = $this->_ehrTables->getOrgCode();
        
        $formId = $this->_appManagerDb->fetchCol($this->_appManagerDb->select()->from(array('RG'=>$this->_ehrTables->regUser),
                                                       array())
                                                ->joinLeft(array('BR'=>$this->_ehrTables->billingRate),'RG.Billing_Id=BR.Billing_Id',
                                                                array(''))
                                                ->joinLeft(array('PF'=>$this->_ehrTables->planforms),'PF.Plan_Id=BR.Plan_Id',
                                                                array('Form_Id'))
                                                ->where('RG.Org_Code = ?',$orgcode));
        
        $hrForms = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->forms, array('Form_Name','Form_Id'))
                                    ->where('Module_Id = ?', $moduleId)
									->where('Sub_Form = 0')
                                    ->where('Form_Id IN (?)',$formId)
                                    ->order("Form_Name Asc"));

        foreach ($hrForms as $key => $row)
		{
            $customForm = $this->getCustomFormNameDetails($row['Form_Name']);
            if(!empty($customForm))
            {
                $hrForms[$key]['Enable'] = $customForm[0]['Enable'];
                $hrForms[$key]['New_Form_Name'] = $customForm[0]['New_Form_Name'];
            }
        }
        
        return $hrForms;
    }
    
	public function getAllForms()
    {
        $formsQry = $this->_db->select()
        ->from(array('f'=>$this->_ehrTables->forms));

        $forms = $this->_db->fetchAll($formsQry);
        return $forms;
    }
    //to list all the main forms
    public function mainForms()
    {
        $this->_appManagerDb = Zend_Registry::get('Hrapp');
        
        $orgcode = $this->_ehrTables->getOrgCode();
        
        $formId = $this->_appManagerDb->fetchCol($this->_appManagerDb->select()->from(array('RG'=>$this->_ehrTables->regUser),
                                                       array())
                                                ->joinLeft(array('BR'=>$this->_ehrTables->billingRate),'RG.Billing_Id=BR.Billing_Id',
                                                                array(''))
                                                ->joinLeft(array('PF'=>$this->_ehrTables->planforms),'PF.Plan_Id=BR.Plan_Id',
                                                                array('Form_Id'))
                                                ->where('RG.Org_Code = ?',$orgcode));
                                                
        $formsQry = $this->_db->select()
        ->from(array('m'=>$this->_ehrTables->modules),array('m.Module_Name'))
        ->joinInner(array('f'=>$this->_ehrTables->forms), 'f.Module_Id = m.Module_Id')
        ->where('f.Sub_Form = ?',0)
		->where('Form_Id IN (?)',$formId)
        ->order("f.Form_Id Asc");
        
        $forms = $this->_db->fetchAll($formsQry);
        
		
		
        foreach ($forms as $keyForm => $formName)
		{
            $customForm = $this->getCustomFormNameDetails($formName['Form_Name']);
            if(!empty($customForm))
            {
                $forms[$keyForm]['Enable'] = $customForm[0]['Enable'];
                $forms[$keyForm]['New_Form_Name'] = $customForm[0]['New_Form_Name'];
            }
        }
        
        return $forms;
    }
    // to list all subforms
    public function subForms()
    {
		$orgcode = $this->_ehrTables->getOrgCode();
        
        $formId = $this->_appManagerDb->fetchCol($this->_appManagerDb->select()->from(array('RG'=>$this->_ehrTables->regUser),
                                                       array())
                                                ->joinLeft(array('BR'=>$this->_ehrTables->billingRate),'RG.Billing_Id=BR.Billing_Id',
                                                                array(''))
                                                ->joinLeft(array('PF'=>$this->_ehrTables->planforms),'PF.Plan_Id=BR.Plan_Id',
                                                                array('Form_Id'))
                                                ->where('RG.Org_Code = ?',$orgcode));
		
        $subFormsQry = $this->_db->select()
        ->from(array('f'=>$this->_ehrTables->forms))
        ->where('f.Sub_Form > ?',0)
		->where('Form_Id IN (?)',$formId);
        
        $subForms = $this->_db->fetchAll($subFormsQry);		
		
		/** Custom form names has to be shown. So subforms has any custom form name is checked.**/
		foreach ($subForms as $keySubForm => $subFormName)
		{
            $customForm = $this->getCustomFormNameDetails($subFormName['Form_Name']);
            if(!empty($customForm))
            {
                $subForms[$keySubForm]['Enable'] = $customForm[0]['Enable'];
                $subForms[$keySubForm]['New_Form_Name'] = $customForm[0]['New_Form_Name'];
            }
        }
		
		
        return $subForms;
    }
    /**
     * to list all module forms
     */
    public function listAllForms()
    {
        $formsQry = $this->_db->select()
        ->from(array('m'=>$this->_ehrTables->modules),array('m.Module_Name'))
        ->joinInner(array('f'=>$this->_ehrTables->forms), 'f.Module_Id = m.Module_Id')
        ->order("f.Form_Id Asc");

        $forms = $this->_db->fetchAll($formsQry);
        return $forms;
    }
    /**
     * to list all reports submenu
     */
    public function reportsSubMenu($formName)
    {

        $reportsSubMenuQry = $this->_db->select()
        ->from($this->_ehrTables->hrReports,array('Report_SubMenu_ID' , 'Report_SubMenu_Title'))
        ->where('Report_SubMenu_Title = ?', $formName)
        ->group("Report_SubMenu_ID");

        $reportsSubMenu = $this->_db->fetchRow($reportsSubMenuQry);
        return $reportsSubMenu;

    }
    public function formByModuleName($moduleName)
    {
        $this->_appManagerDb = Zend_Registry::get('Hrapp');
        
        $orgcode = $this->_ehrTables->getOrgCode();
        
        $formId = $this->_appManagerDb->fetchCol($this->_appManagerDb->select()->from(array('RG'=>$this->_ehrTables->regUser),
                                                       array())
                                                ->joinLeft(array('BR'=>$this->_ehrTables->billingRate),'RG.Billing_Id=BR.Billing_Id',
                                                                array(''))
                                                ->joinLeft(array('PF'=>$this->_ehrTables->planforms),'PF.Plan_Id=BR.Plan_Id',
                                                                array('Form_Id'))
                                                ->where('RG.Org_Code = ?',$orgcode));
		
        $formsQry = $this->_db->select()
        ->from(array('f'=>$this->_ehrTables->forms),array('f.Form_Name','f.Form_Id'))
        ->joinInner(array('m'=>$this->_ehrTables->modules), 'f.Module_Id = m.Module_Id', array())
        ->where('f.Sub_Form = ?',0)->where('m.Module_Name LIKE ?', $moduleName)
		->where('Form_Id IN (?)',$formId)
        ->order("f.Form_Name Asc");

        $forms = $this->_db->fetchAll($formsQry);
        
        foreach ($forms as $key => $row)
		{
            $customForm = $this->getCustomFormNameDetails($row['Form_Name']);
            if(!empty($customForm))
            {
                $forms[$key]['Enable'] = $customForm[0]['Enable'];
                $forms[$key]['New_Form_Name'] = $customForm[0]['New_Form_Name'];
            }
        }
      
        
        return $forms;
    }
	
    public function formAccessRights ($empId, $formId)
    {
        $formName = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->forms, array('Form_Name'))
                                    ->where('Form_Id = ?', $formId));

        $employeeAccessRights = $this->_dbAccessRights->employeeAccessRights ($empId, $formName);

        $formAccess = $employeeAccessRights['Employee']['View'];
        if($formAccess > 0)
        {
            return $formAccess;
        }
        else
        {
            $subFormDetails = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->forms, array('Form_Name'))
                                    ->where('Sub_Form = ?', $formId));
            if(!empty($subFormDetails))                        
            {
                foreach($subFormDetails as $subFormName)                        
                {
                    $employeeAccessRights = $this->_dbAccessRights->employeeAccessRights ($empId, $subFormName);
                    $formAccess = $employeeAccessRights['Employee']['View'];
                    if($formAccess > 0)
                    {
                        return $formAccess;
                    }
                }
            }
            return 0;
        }
    }
    
    
    public function getTaxSubForms()
    {
        $formsQry = $this->_db->fetchAll($this->_db->select()
                        ->from(array('f'=>$this->_ehrTables->forms),array('f.Form_Name','f.Form_Id'))
                        ->where('f.Sub_Form = ?',39));
        
        foreach ($formsQry as $key => $row)
		{
            $customForm = $this->getCustomFormNameDetails($row['Form_Name']);
            if(!empty($customForm))
            {
                $formsQry[$key]['Enable'] = $customForm[0]['Enable'];
                $formsQry[$key]['New_Form_Name'] = $customForm[0]['New_Form_Name'];
            }
        }
        
        return $formsQry;
    }

    public function getCustomFormNameDetails($formName)
    {
        $orgCode       = $this->_ehrTables->getOrgCode(); 
        $qryCustomForm = $this->_appManagerDb->select()->from(array('CF'=>$this->_ehrTables->customForm),array('Form_Id','Enable', 'New_Form_Name' ))
                                                        ->joinLeft(array('F'=>$this->_ehrTables->appForms),'F.Form_Id=CF.Form_Id',array('Form_Name'))
                                                        ->where('F.Form_Name = ?',$formName)
														->order("F.Form_Name Asc");
        if(!empty($orgCode))
        {
            $qryCustomForm->where('CF.Org_Code = ?',$orgCode)
                          ->where('CF.Customization_Applicable_For = ?','Specific Organization');
        }
        $customForm = $this->_appManagerDb->fetchAll($qryCustomForm);

        if(!empty($customForm))
        {
            return $customForm;
        }
        else
        {
            $qryCustomForm = $this->_appManagerDb->select()->from(array('CF'=>$this->_ehrTables->customForm),array('Form_Id','Enable', 'New_Form_Name' ))
                                                        ->joinLeft(array('F'=>$this->_ehrTables->appForms),'F.Form_Id=CF.Form_Id',array('Form_Name'))
                                                        ->where('F.Form_Name = ?',$formName)
                                                        ->where('CF.Customization_Applicable_For = ?','All Organization')
														->order("F.Form_Name Asc");
            $customForm = $this->_appManagerDb->fetchAll($qryCustomForm);
            return $customForm;
        }
    }

    public function __destruct()
    {
        
    }
}