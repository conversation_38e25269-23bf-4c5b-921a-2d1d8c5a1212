//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tablealias');
//Require constants
const { formName } = require('../../common/appconstants');
const moment = require('moment');

module.exports.getEmployeesDetailsBasedOnRole = async (parent, args, context, info) => {
    let organizationDbConnection;
    let validationError = {};
    try {
        console.log("Inside getEmployeesDetailsBasedOnRole function()")
        let formToCheck = "";
        if (!args.formName) {
            validationError['IVE0256'] = commonLib.func.getError('', 'IVE0256').message1;
            throw ("IVE0000")
        }
        if (args.formName.toLowerCase() === "accreditation") {
            formToCheck = formName.accreditation;
        } else {
            formToCheck = args.formName;
        }
        
        let logInEmpId = context.Employee_Id;
        // get the organization database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);
        
        // check their rights
        let accessFormId= args.formId?args.formId : null;

        //For the job post we should consider the recruiter as admin
        let isRecruiter = 0;

        if(args.formId && args.formId == 15){
            const responseObject = await getIsRecruiterData(organizationDbConnection, logInEmpId);
            let recruiter = responseObject ? (responseObject.length? (responseObject[0].Is_Recruiter? (responseObject[0].Is_Recruiter): null): null): null;
            if(recruiter && recruiter.toLowerCase() === 'yes'){
                isRecruiter = 1;
            }
        }

        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId, formToCheck, '', 'UI', false, accessFormId);
        if (Object.keys(checkRights).length === 0) {
            throw ('_DB0100');
        }

        else {
            if (1|| checkRights.Role_View === 1) {
                //If the login employee is recruiter then we should list all the employee,
                //If the custom group is part of input request then we should list the employees based on the custom group
                if(!isRecruiter && !args.customGroupId && !args.serviceProviderId) {
                    // check loggedIn employee is  admin or not
                    if (checkRights.Employee_Role.toLowerCase() === 'admin') {
                        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId, formName.serviceProviderAdmin, '', 'UI');
                        if (checkRights.Role_Update === 1) {
                            let getServiceProviderEmployees = await commonLib.func.getServiceProviderEmployees(organizationDbConnection, logInEmpId)
                            getServiceProviderEmployees = getServiceProviderEmployees.map(row => row.Employee_Id);
                            var employeeIdsArray = getServiceProviderEmployees
                        }
                    } else if (checkRights.Is_Manager === 1) {
                        var { employeeIdsArray } = await commonLib.func.getEmployeeIdsOrManagerHierarchyBasedOnRole(organizationDbConnection, 1, logInEmpId, 0, 0);
                        // For the attendance list from employee self service. We need to include the login user id, As we are using data for filter
                        if(args.formId === 305){
                            employeeIdsArray.push(logInEmpId);
                        }
                      }else{
                        var employeeIdsArray = [logInEmpId]
                    }
                }

                // Get fiscal start date for salary form filtering
                let fiscalStartDate = null;
                if (args.formId === 207 && !args.isSalaryEdit) {
                    try {
                        // Get organization details to retrieve fiscal start month and assessment year
                        const orgDetails = await commonLib.func.getOrgDetails(context.Org_Code, organizationDbConnection, 0);

                        if (orgDetails && Object.keys(orgDetails).length > 0) {
                            const assessmentYear = orgDetails.Assessment_Year;
                            const fiscalStartMonth = orgDetails.Fiscal_StartMonth;

                            if (assessmentYear && fiscalStartMonth) {
                                // Calculate fiscal start year (assessment year - 1)
                                const fiscalStartYear = assessmentYear - 1;

                                // Get the salary day for the fiscal start month and year
                                const { Salary_Date } = await commonLib.func.getSalaryDay(
                                    context.Org_Code,
                                    organizationDbConnection,
                                    fiscalStartMonth,
                                    fiscalStartYear
                                );

                                fiscalStartDate = Salary_Date;
                            }
                        }
                    } catch (fiscalError) {
                        console.log('Error getting fiscal details, will use fallback condition:', fiscalError);
                    }
                }

                return (
                    organizationDbConnection(ehrTables.empPersonalInfo)
                        .select(
                            "EPI.Employee_Id as employeeId",
                            "EJ.Date_Of_Join",
                            "EJ.User_Defined_EmpId as userDefinedEmpId",
                            organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as employeeName"),
                            "EPI.Is_Recruiter as isRecruiter",
                             'R.Resignation_Date as resignationDate',
                             "ESC.Eligible_For_Pf as eligibleForPf",
                             "ESC.Eligible_For_Pension as eligibleForPension",
                             "ESC.Eligible_For_ESI as eligibleForESI",
                             "ESC.Eligible_For_Insurance as eligibleForInsurance",
                             "ESC.Eligible_For_Nps as eligibleForNps",
                             "ESC.Eligible_For_Gratuity as eligibleForGratuity",
                             "ESC.Minimum_Wage as minimumWage"
                        )
                        .from(ehrTables.empPersonalInfo + ' as EPI')
                        .leftJoin(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EPI.Employee_Id')
                        .leftJoin(
                     ehrTables.employeeType + ' as ET',
                     'EJ.EmpType_Id',
                     'ET.EmpType_Id'
                        )
                        .leftJoin(ehrTables.empResignation+' as R' , function () {
                            this.on('EJ.Employee_Id', '=', 'R.Employee_Id')
                              .onIn('R.Approval_Status', organizationDbConnection.raw('(?,?)', ['Applied', 'Approved']))
                          })
                          .leftJoin(ehrTables.employeeSalaryConfiguration + ' as ESC', function () {
                            this.on('EPI.Employee_Id', '=', 'ESC.Employee_Id')
                          })
                          .modify(function (queryBuilder) {
                            if ([304, 305].includes(args.formId)) {
                              const { filterType } = args;
                              if (args.filterType?.toLowerCase() === "weekly") {
                                if (!args.startDate || !args.endDate) {
                                    validationError['IVE0555'] = commonLib.func.getError('', 'IVE0555').message;
                                    throw ("IVE0000")
                                }
                              }
                              
                              if (args.filterType?.toLowerCase() === "monthly") {
                                if (!args.attendanceMonth || !args.attendanceYear) {
                                  validationError['IVE0556'] =commonLib.func.getError('', 'IVE0556').message;
                                  throw ("IVE0000");
                                }
                              }
                              queryBuilder.leftJoin(
                                  `${ehrTables.empAttendance} as AT`,
                                  function() {
                                      this.on('EJ.Employee_Id', '=', 'AT.Employee_Id');
                          
                                      if (filterType?.toLowerCase() === 'weekly') {
                                          this.andOn(
                                            organizationDbConnection.raw('?? BETWEEN ? AND ?', 
                                              ['AT.Attendance_Date', args.startDate, args.endDate])
                                          );
                                      } else if (filterType?.toLowerCase() === 'monthly') {
                                          this.andOn(
                                            organizationDbConnection.raw('MONTH(??) = ? AND YEAR(??) = ?', 
                                              ['AT.Attendance_Date', args.attendanceMonth, 
                                               'AT.Attendance_Date', args.attendanceYear])
                                          );
                                      }
                                  }
                              );
                          
                              queryBuilder.select(
                                  'EJ.Employee_Id',
                                  organizationDbConnection.raw('COALESCE(SUM(??), 0) AS ??', ['AT.Total_Hours', 'totalHours'])
                              )
                              .groupBy('EJ.Employee_Id');
                          }
                        })
                        .where(function () {
                            if(employeeIdsArray && employeeIdsArray.length){
                                this.whereIn('EPI.Employee_Id', employeeIdsArray)
                            }
                            if(formToCheck == formName.preApprovalSettings || args.formId ==341 || args.formId ==342){
                                this.where('EJ.Emp_Status', "Active")
                                this.where('EPI.Form_Status', 1)
                            }
                            if(args.formId===304 || args.formId===305){
                                this.where('EJ.Date_Of_Join', '<=', moment().format('YYYY-MM-DD'))
                            }
                            if(args.formId === 207 && !args.isSalaryEdit){
                              // Use a subquery to get employees without payslips
                              let subquery = organizationDbConnection
                                .select('ESD.Employee_Id')
                                .from('employee_salary_details' + ' as ESD')
                                .groupBy('ESD.Employee_Id');

                              // Filter the main query to only include these employees
                              this.whereNotIn('EPI.Employee_Id', subquery);

                              // Apply fiscal year based employee status filtering
                              if (fiscalStartDate) {
                                // Apply the condition: J.Emp_Status='Active' OR (J.Emp_Status='InActive' AND J.Emp_InActive_Date >= '$fiscalStartDate')
                                this.where(function() {
                                  this.where('EJ.Emp_Status', 'Active')
                                      .orWhere(function() {
                                        this.where('EJ.Emp_Status', 'InActive')
                                            .where('EJ.Emp_InActive_Date', '>=', fiscalStartDate);
                                      });
                                });
                              } else {
                                // Fallback to original condition if fiscal start date is not available
                                this.where('EJ.Emp_Status', 'Active');
                              }
                            }
                            if(args.serviceProviderId && !args.customGroupId){
                                this.where('EJ.Service_Provider_Id', args.serviceProviderId)
                            }
                        }).modify(async function (queryBuilder) {
                            if(args.formId){
                                queryBuilder.select(
                        "EJ.Designation_Id as designationId",
                        "EJ.Department_Id as departmentId",
                 
                        organizationDbConnection.raw(`
                          CASE 
                              WHEN DES.Designation_Code IS NOT NULL AND TRIM(DES.Designation_Code) != '' 
                              THEN CONCAT(DES.Designation_Code, ' - ', DES.Designation_Name) 
                              ELSE DES.Designation_Name 
                          END AS designationName
                      `),                      
                        organizationDbConnection.raw(`
                          CASE 
                              WHEN L.Location_Code IS NOT NULL AND TRIM(L.Location_Code) != '' 
                              THEN CONCAT(L.Location_Code, ' - ', L.Location_Name) 
                              ELSE L.Location_Name 
                          END AS locationName
                      `),
                      organizationDbConnection.raw(`
                        CASE 
                            WHEN DEP.Department_Code IS NOT NULL AND TRIM(DEP.Department_Code) != '' 
                            THEN CONCAT(DEP.Department_Code, ' - ', DEP.Department_Name) 
                            ELSE DEP.Department_Name 
                        END AS departmentName
                    `),
                    
                    organizationDbConnection.raw(`
                        CASE 
                            WHEN ET.Employee_Type_Code IS NOT NULL AND TRIM(ET.Employee_Type_Code) != '' 
                            THEN CONCAT(ET.Employee_Type_Code, ' - ', ET.Employee_Type) 
                            ELSE ET.Employee_Type 
                        END AS employeeType
                    `),
                    
                    organizationDbConnection.raw(`
                        CASE 
                            WHEN SP.Service_Provider_Code IS NOT NULL AND TRIM(SP.Service_Provider_Code) != '' 
                            THEN CONCAT(SP.Service_Provider_Code, ' - ', SP.Service_Provider_Name) 
                            ELSE SP.Service_Provider_Name 
                        END AS Service_Provider_Name
                    `),
                        "EJ.Location_Id as locationId", "EJ.Emp_Status as empStatus", "EJ.Emp_Email","EJ.Service_Provider_Id",
                        "EJ.Date_Of_Join as dateOfJoin", 
                        "EJ.Probation_Date as probationDate", "EJ.EmpType_Id as empTypeId", "EJ.Manager_Id as managerId",
                        "EJ.Work_Schedule as workSchedule",
                        "ET.Enable_Work_Place",
                    )
                        queryBuilder.leftJoin(ehrTables.empPersonalInfo + " as EP", "EP.Employee_Id", "EJ.Employee_Id")
                        queryBuilder.leftJoin(ehrTables.designation + " as DES", "DES.Designation_Id", "EJ.Designation_Id")
                        queryBuilder.leftJoin(ehrTables.department + " as DEP", "DEP.Department_Id", "EJ.Department_Id")
                        queryBuilder.leftJoin(ehrTables.location + " as L", "L.Location_Id", "EJ.Location_Id")
                        queryBuilder.leftJoin(ehrTables.serviceProvider + " as SP", "SP.Service_Provider_Id", "EJ.Service_Provider_Id")
                            }
                            if (args.customGroupId) {
                                queryBuilder.innerJoin(
                                  ehrTables.customGroupEmployees + ' as CGE',
                                  'CGE.Employee_Id',
                                  'EPI.Employee_Id'
                                )
                                queryBuilder.where('CGE.Group_ID', args.customGroupId)
                            } 
                            if(args.serviceProviderId && !args.customGroupId){ // If custom group is there we should consider custom group
                                queryBuilder.where('EJ.Service_Provider_Id', args.serviceProviderId)
                            }
                            
                        })
                        .then(async data => {
                            let updatedEmployeeDetails = data
                            if(args.flag && args.flag.toLowerCase() === 'payslipreq'){
                            const latestPayslips = await getLatestPayslips(organizationDbConnection) || [];
                            updatedEmployeeDetails = await processEmployeeSalaryDates(data, context, organizationDbConnection, latestPayslips);
                            }
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return success response
                            if (data.length == 0) {
                                return { errorCode: '', message: 'No employees details found.', employeeDetails: data };
                            }
                            return { errorCode: '', message: 'Employees details retrieved successfully.', employeeDetails: updatedEmployeeDetails };
                        })
                        .catch(e => {
                            console.log('Error in the getEmployeesDetailsBasedOnRole() function .catch block. ', e);
                            errResult = commonLib.func.getError(e, 'CDG0128');
                            // return response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )
            }
            else {
                throw '_DB0100';
            }
        }
    }
    catch (e) {
        console.log('Error in the getEmployeesDetailsBasedOnRole() function catch block. ', e);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (e === 'IVE0000') {
            errResult = commonLib.func.getError('', 'IVE0000');
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            errResult = commonLib.func.getError(e, 'CDG0128');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}

async function getIsRecruiterData(organizationDbConnection, logInEmpId){
    try{ 
            return (
                await organizationDbConnection(ehrTables.empPersonalInfo)
                .select(
                    'Is_Recruiter',
                  )
                  .where('Employee_Id', logInEmpId)
                    .then((data) => {
                        return data ;
                    })
                    .catch((err) => {
                        console.log('Error in getIsRecruiterData .catch() block', err);
                        throw err;                   
                })
            )
        

    } catch(err){
        console.log('Error in getIsRecruiterData main catch() block', err);
        throw err;
    }

}
const getLatestPayslips = async (organizationDbConnection) => {
    try {
      const results = await organizationDbConnection
        .select('sp.Employee_Id', 'sp.Salary_Month', 'sp.Payslip_Id')
        .from(ehrTables.salaryPayslip + ' as sp')
        .join(
          organizationDbConnection
            .select('Employee_Id')
            .max('Payslip_Id as MaxPayslipId')
            .from(ehrTables.salaryPayslip)
            .groupBy('Employee_Id')
            .as('max_payslip'),
          function () {
            this.on('sp.Employee_Id', '=', 'max_payslip.Employee_Id')
                .andOn('sp.Payslip_Id', '=', 'max_payslip.MaxPayslipId');
          }
        )
        .orderBy('sp.Payslip_Id', 'desc');
  
      return results;
    } catch (error) {
      console.error('Error in getLatestPayslips:', error); 
      throw error;
    }
  };

async function processEmployeeSalaryDates(data, context, organizationDbConnection,LatestPayslips) {
    try {
      data = data.map(obj => {
        // Find the payslip object that corresponds to the Employee_Id
        const payslip = LatestPayslips.find(payslip => payslip.Employee_Id === obj.employeeId);
      
        if (payslip) {
          obj.Salary_Month = payslip.Salary_Month;
        }
        else{
            obj.Salary_Month = null
        }
      
        return obj;
      });
      const uniqueSalaryMonths = [...new Set(data.map(item => item.Salary_Month).filter(Boolean))];
      if(!uniqueSalaryMonths || !uniqueSalaryMonths.length) return data
      // Step 2: Process each unique Salary_Month
      const salaryMonthData = await Promise.all(
        uniqueSalaryMonths.map(async salaryMonth => {
          const [month, year] = salaryMonth.split(','); // Split into month and year
          const { Last_SalaryDate } = await commonLib.func.getSalaryDay(
            context.Org_Code,
            organizationDbConnection,
            month,
            year
          );
          return { salaryMonth, Last_SalaryDate };
        })
      );
      const salaryMonthMap = salaryMonthData.reduce((acc, curr) => {
        acc[curr.salaryMonth] = curr.Last_SalaryDate;
        return acc;
      }, {});
      const updates = data.map(employee => {
        const lastSalaryDate = (employee?.Salary_Month && salaryMonthMap[employee.Salary_Month])
          ? salaryMonthMap[employee.Salary_Month]
          : employee.Date_Of_Join;
      
        return {
          ...employee,
          Last_SalaryDate: lastSalaryDate || null
        };
      });
  
      return updates;
    } catch (error) {
      console.error('Error processing employee salary dates:', error);
      throw error;
    }
  }
  