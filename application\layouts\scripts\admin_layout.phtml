<?php
//=========================================================================================
//=========================================================================================
/* Program        : layout.phtml														*
 * Property of Caprice Technologies Pvt Ltd,                                            *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                     *
 * Coimbatore, Tamilnadu, India.														*
 * All Rights Reserved.            														*
 * Use of this material without the express consent of Caprice Technologies             *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law.*
 *                                                                                    	*
 * Description    : Main Layout to decorate the web page		                       	*
 *                                                                                   	*
 *                                                                                    	*
 * Revisions      :                                                                    	*
 *   Version        Date           Author                  Description                  *
 *    0.1        30-May-2013      Narmadha                Initial Version               *
 *                                                                                    	*
 *    1.0        02-Feb-2015      Prasanth             Changes in file for mobiles app  *
 *                                                                                    	*/
//=========================================================================================
//=========================================================================================
$isProduction = Zend_Registry::get('Production');

$isDomain = Zend_Registry::get('Domain');
$isProductLogo = Zend_Registry::get('ProductLogo');

$ipAddressAPI = Zend_Registry::get('clientipUrl');

$isDomainArray = explode(".",$isDomain);

$firebaseApiKey = Zend_Registry::get('firebaseApiKey');
$firebaseAuthDomain = Zend_Registry::get('firebaseAuthDomain');
$firebaseDatabaseURL = Zend_Registry::get('firebaseDatabaseURL');
$firebaseProjectId = Zend_Registry::get('firebaseProjectId');
$firebaseStorageBucket = Zend_Registry::get('firebaseStorageBucket');
$firebaseMessagingSenderId = Zend_Registry::get('firebaseMessagingSenderId');
$firebaseAppId = Zend_Registry::get('firebaseAppId');

$countModuleTitle = 0;

$notificationRedirection='Yes';
$salaryMonth='';
$salaryYear='';
$params = 'salaryMonth='.$salaryMonth.'&salaryYear='.$salaryYear.'&notificationRedirection='.$notificationRedirection;
$encodedParams = base64_encode($params);

if (isset($_COOKIE['accessToken']) || isset($_COOKIE['refreshToken']))
{
	$controllerName = Zend_Controller_Front::getInstance()->getRequest()->getControllerName();
	$moduleName = Zend_Controller_Front::getInstance()->getRequest()->getModuleName();
	$moduleParams = Zend_Controller_Front::getInstance()->getRequest()->getParams();
	$payrollFormsUnderTaxStatutoryModule = array(
		"etf", "fixed-health-insurance", "insurance", "labour-welfare-fund", "perquisite-tracker", 
		"provident-fund", "tax-declarations", "tax-rules", "tds-history", "gratuity"
	);
	$formsManagerFormsUnderTaxStatutoryModule = array("form-downloads", "compliance-forms");
	$ehrTables = new Application_Model_DbTable_Ehr();
	$dbPersonal = new Employees_Model_DbTable_Personal();
	$dbEmpUser = new Auth_Model_DbTable_EmpUser();
	$dbModules = new Application_Model_DbTable_Modules();
	$dbAlerts = new Default_Model_DbTable_Alerts();
	$dbEmployee = new Employees_Model_DbTable_Employee();
	$dbDataSetup = new DatasetupDashboard_Model_DbTable_DataSetupDashboard();
	$orgSettings = new Organization_Model_DbTable_OrgSettings();
	$commonFunction = new Application_Model_DbTable_CommonFunction(); 
	$dbAccessRights = new Default_Model_DbTable_AccessRights();
     
    $mailDetails = $orgSettings->viewMailConfiguration();
	
	$orgCode = $ehrTables->getOrgCode();
	$departmentClassificaiton = $orgSettings->getDepartmentClassification($orgCode);
	$listDivisionDetails = $ehrTables->listDivisionDetails();
	$department      = new Organization_Model_DbTable_Department();
												
	//$departmentClassificaiton['Department_Classifications']=1;
	$orgName = $ehrTables->organizationName();
	$domainDetails = $ehrTables->domainDetails();
	$termsOfUse     = $domainDetails['Terms_Link'];
	$privacyPolicy  = $domainDetails['Privacy_Policy_Link'];
    $orgdtFrmt = $ehrTables->orgDateformat();
    $dateFormat = $orgdtFrmt['php'];

	$userSession = $commonFunction->getUserDetails ();
	$empId = $userSession['logUserId'];

	$orgDetails = Zend_Registry::get('orgDetails');
	if( !empty($orgDetails)){
		$disableLogout = $orgDetails["Disable_Logout"];
		if($orgDetails["Field_Force"] == 1){
			//Get camu url based on the login employee id
			$camuUrl = $commonFunction->getCamuUrlForServiceProvider($empId);
		}else{
			$camuUrl = $orgDetails["Camu_Base_Url"];
		}
	} else {
		$disableLogout = "No";
		$camuUrl = "";
	}

    $dataSetup = $dbDataSetup->dataSetupValue();
    $dataImport = $dbDataSetup->dataSetupImportValue();
    
	$timezoneDt = $ehrTables->gettimezoneDateTime();

	// get landed form access based on controller name
	$smallCaseFormName = str_replace('-', ' ', $controllerName); // replace - by space in controller name(ex: salary-payslip to salary payslip) 
	$accessFormName = ucwords($smallCaseFormName); // salary payslip - Salary Payslip
	$isLandedFormHaveAccess = "No";
	$isManager = 0;
	$isAdminForLoadedForm=0;
 
	$logEmpDetails = $dbPersonal->employeeName($empId);
	$employeeFirstname = $logEmpDetails['Emp_First_Name'];
	$employeename = $logEmpDetails['Employee_Name'];
	$designation = $logEmpDetails['Designation_Name'];
	$joinMonth = $logEmpDetails['Join_Month'];
	$empMail = $logEmpDetails['Emp_Email'];
	$empMobile = $logEmpDetails['Mobile_No'];
	$empGender = $logEmpDetails['Gender'];
	
    $payslipCnt = $dbDataSetup->getPayslipCount();
	$announcementDetails = $dbAlerts->getAnnouncement($empId);
    $countAnnouncementDetails = count($announcementDetails);
	
	// get payment and demo details of the organization.
	$orgPaymentDemoDetails = $commonFunction->getBillingSubscriptionDetails($orgCode);
	$billingSubscriptionDetails = "";
	$autoBilling = $orgPaymentDemoDetails['autoBilling'];

	$payrollSettings = $commonFunction->getPayrollSettings(); 
	$payrollCountry =  $payrollSettings['Payroll_Country'];
	// from the orgPaymentDemoDetails response, we can get the billingSubscriptionDetails, so checking the response is available or not
	if (
		$orgPaymentDemoDetails &&
		$orgPaymentDemoDetails["billingSubscriptionDetails"] &&
		count($orgPaymentDemoDetails["billingSubscriptionDetails"]) > 0
	) {
		$billingSubscriptionDetails = $orgPaymentDemoDetails["billingSubscriptionDetails"];
	}
	// based on the response we can decide to present the layout details.
	$layoutRestrictedFor = ""; // initially we can define it as empty, which means the layout is not restricted.
	// we don't need to restrict the layout except for the below status. so layoutRestrictedFor variable as empty for another status.
	if (
		$billingSubscriptionDetails &&
		$billingSubscriptionDetails["subscriptionAlertStatus"] === "present-demo-booking-card"
	) {
	  $layoutRestrictedFor = "demo-not-booked";
	} else if(
		$billingSubscriptionDetails &&
		$billingSubscriptionDetails["subscriptionAlertStatus"] === "present-red-payment-alert"
	) {
	  $layoutRestrictedFor = "payment-alert";
	}

	$dataSetupAccessRights = $dbAccessRights->employeeAccessRights($empId, 'Data Setup Dashboard');
	// if the layout is restricted, we can assign an empty array for modules
    $modules = empty($layoutRestrictedFor) ? $dbModules->getModules() : array();
    $isAnyAdmin = $commonFunction->checkAnyOneOfAdmin($empId);
	$signOffAlertDetails = array();
	$signOffAlertType = "";
	if($isAnyAdmin && ($accessFormName == "Attendance Finalization" || $accessFormName == "Salary Payslip" || $accessFormName == "Leaves" || $accessFormName == "Attendance" || $accessFormName == "Approval Management")) {
		$signOffDetailsResponse = $commonFunction->getSignOffNotificationDetails($orgCode);
		if (
			$signOffDetailsResponse &&
			$signOffDetailsResponse["signOffDetails"] &&
			count($signOffDetailsResponse["signOffDetails"]) > 0
		) {
			$signOffAlertDetails = $signOffDetailsResponse["signOffDetails"];
		}
		if(!empty($signOffAlertDetails) && $signOffAlertDetails["showSignOffAlert"] == "Yes") {
			$signOffAlertType =  $signOffAlertDetails["signOffAlertType"];
		}
	}

	$alerts = $dbAlerts->showHomePanelData($empId);	
	$notifications = $alerts['alerts'];
	$notificationsCnt = count($notifications);
	
	//Get the access rights for the loaded form
	$getLoadedFormAccess = $dbAccessRights->employeeAccessRights ($empId, $accessFormName);
	if(count($getLoadedFormAccess) > 0 && isset($getLoadedFormAccess['Admin'])
	&&($getLoadedFormAccess['Admin'] === 'admin')){
		$isAdminForLoadedForm=1;
	}
	//Get the login employee admin form role
	$isAdmin = $dbAlerts->isAdmin($empId, 'admin');
	$isServiceProviderAdmin = $dbAlerts->isServiceProviderAdmin($empId);
	$leaveSettings =$commonFunction->getLeaveSettings();
	$profilePath = $dbEmployee->getEmpPhotoPath($empId);
	$profilePicture = $this->baseUrl('images/defaultPhot.jpg');
	
	$defaultImage = '<span class="fa-stack fa-lg" style="top: 6px; right: 8px;">
						<i class="fa fa-circle fa-stack-2x"></i>
						<i class="fa fa-user fa-stack-1x fa-inverse" style="top: -5px;"></i>
					</span>';

	if (!empty($profilePath))
	{
		$commonFunction = new Application_Model_DbTable_CommonFunction();
		$signedUrl = $commonFunction->getAwsSignedUrl('EmployeeProfilePicture',$profilePath,'imageBucket');
		if($signedUrl !== ''){
			/* Set the width and height for the responsiveness */
			$profilePictureField =  '<img class="header-profile-img" src="'.$signedUrl .'" alt="user image" >';
		}
		else
		{
			$profilePictureField = $defaultImage;
		}
	}
	else
	{
		$profilePictureField = $defaultImage;
	}
	
	$employeeTypeCnt = $dbAlerts->employeesCount ();
	
	$totalEmployees = $employeeTypeCnt['ActiveCnt'] + $employeeTypeCnt['InActiveCnt'];
	$activePercentage = ($employeeTypeCnt['ActiveCnt'] / $totalEmployees) * 100;
	$inActivePercentage = ($employeeTypeCnt['InActiveCnt'] / $totalEmployees) * 100;
	
	$hrappForms = array();

	// find module id based on landed module name
	$replacedModuleName = str_replace("-", " ", $moduleName); // remove - and replace with space. (ex: roster-management -> Roster Management)
	$replacedModuleName = ucwords($replacedModuleName); // change it to capital letter of first letter of the word(Ex: Roster Management)

	if (in_array($controllerName, $payrollFormsUnderTaxStatutoryModule)) {
		$replacedModuleName = 'Tax and Statutory Compliance';
	} else if (in_array($controllerName, $formsManagerFormsUnderTaxStatutoryModule)) {
		$replacedModuleName = 'Tax and Statutory Compliance';
	}
	// filter the landed module from the modules list
	/** $modules - total modules list
	 * $value - each row of the array
	 * use(keyword) - for accessing external variables
	*/
	$landedModule = array_filter($modules, function($value) use ($replacedModuleName){
		return $value["Module_Name"] == $replacedModuleName;
	});
	/** 
	 * array_values - used to indexes the array numerically. 
	 * Because after filter we get the array key which is from original module list. ex: for employees module we get the array key as 2. 
	 * It may vary for different modules. If use this property then the array index is aligned from 0.
	 */
	$landedModule = array_values($landedModule);

	foreach ($modules as $key => $row)
	{
		$menuName = trim($row['Module_Name']);
		
		if ($menuName != 'Home')
		{
			$forms = $dbModules->getForms($row['Module_Id']);

            for($i=0;$i<count($forms);$i++)
            {
                $formName = trim($forms[$i]['Form_Name']);
                
                if(array_key_exists('Enable',$forms[$i]))
                {
                    if($forms[$i]['Enable'] == 1)
                    {
                        $formNewName =  trim($forms[$i]['New_Form_Name']);
						
                        $submenuAccess = $dbModules->formAccessRights ($empId, $forms[$i]['Form_Id']);
                        if ($submenuAccess > 0)
                        {
							// when form name in loop meets landed form name then we consider we have access for landed form, set it as 'yes'
							if($formName == $accessFormName && $isDomainArray[0] == 'hrapp') {
								$isLandedFormHaveAccess = "Yes";
							}
                            $hrappForms[$menuName][$i]['Form_Name'] = $formName;
                            $hrappForms[$menuName][$i]['Enable'] = $forms[$i]['Enable'];
                            $hrappForms[$menuName][$i]['New_Form_Name'] = ($forms[$i]['New_Form_Name'] != '' ? $forms[$i]['New_Form_Name'] :$forms[$i]['Form_Name']);
							$hrappForms[$menuName][$i]['Form_Id'] = $forms[$i]['Form_Id'];
                        }
                    }
                }
                else
                {
                    $submenuAccess = $dbModules->formAccessRights ($empId, $forms[$i]['Form_Id']);
                    if ($submenuAccess > 0)
                    {
						// when form name in loop meets landed form name then we consider we have access for landed form, set it as 'yes'
						if($formName == $accessFormName && $isDomainArray[0] == 'hrapp') {
							$isLandedFormHaveAccess = "Yes";
						}
                        $hrappForms[$menuName][$i]['Form_Name'] = $formName;
						$hrappForms[$menuName][$i]['Form_Id'] = $forms[$i]['Form_Id'];
                    }
                }
            }
		}
	}

	// if (isset($hrappForms['Recruitment']) && count($hrappForms['Recruitment']) > 0) {
	// 	// insert dashboard form in recruitment forms array without checking access
	// 	$dashboardFormObj = array(
	// 		'Form_Name' => 'Dashboard',
	// 		'Form_Id' => null,
	// 	);
	// 	array_unshift($hrappForms['Recruitment'], $dashboardFormObj);
	// }
 
	// for checking whether the login employee is manager or not, we need access for landed form/subForm
	if($isLandedFormHaveAccess == "Yes") {
		$dbJobDetail = new Employees_Model_DbTable_JobDetail();
		$isManager = $dbJobDetail->isManager($empId);
	}
 
	$paymentAlert = $dbAlerts->paymentDue($empId,$orgCode);
	$outageNotification = $dbAlerts->outageNotification();
	
	
	$licenseOrgName = $dbAlerts->enableLincensing($orgCode);
    
    $orgSettings = new Organization_Model_DbTable_OrgSettings();

    $mailDetails = $orgSettings->viewMailConfiguration();
    
    if(isset($_COOKIE['Appmailboxidentity']) && $moduleName == 'mailbox')
	{
        //$key = 'password to (en/de)crypt';
        $key = 'Peacock,Mango/07@03';//secret key
		
		$data = base64_decode($_COOKIE['Appmailboxidentity']);
		
		$iv = substr($data, 0, mcrypt_get_iv_size(MCRYPT_RIJNDAEL_128, MCRYPT_MODE_CBC));
		
		$decrypted = rtrim(
			mcrypt_decrypt(
				MCRYPT_RIJNDAEL_128,
				hash('sha256', $key, true),
				substr($data, mcrypt_get_iv_size(MCRYPT_RIJNDAEL_128, MCRYPT_MODE_CBC)),
				MCRYPT_MODE_CBC,
				$iv
			),
			"\0"
		);
		
		$decrypt = explode('###',$decrypted);
		
		$username = $decrypt[0];
		$pwd = $decrypt[1];
	}
    else
    {
        $username = '';
    }
}

$this->headTitle()->setSeparator(' - ');
$this->headTitle($orgName . ' | '.strtoupper($isDomainArray[0]));



?>

<!DOCTYPE html>
<html class="" lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5">
	<meta name="description" content="Human Resource Management System">
	<meta name="author" content="Caprice Technologies">
	
	<!--Favicon-->
	<link rel="apple-touch-icon" sizes="180x180" href="<?php echo $this->baseUrl('/apple-touch-icon.png?v=9B9bjrPr00'); ?>">
	<link rel="icon" type="image/png" sizes="32x32" href="<?php echo $this->baseUrl('/favicon-32x32.png?v=9B9bjrPr00'); ?>">
	<link rel="icon" type="image/png" sizes="16x16" href="<?php echo $this->baseUrl('/favicon-16x16.png?v=9B9bjrPr00'); ?>">
	<link rel="manifest" href="<?php echo $this->baseUrl('/site.webmanifest?v=9B9bjrPr00'); ?>">
	<link rel="mask-icon" href="<?php echo $this->baseUrl('/safari-pinned-tab.svg?v=9B9bjrPr00'); ?>" color="#5bbad5">
	<link rel="shortcut icon" href="<?php echo $this->baseUrl('/favicon.ico?v=9B9bjrPr00'); ?>">
	<meta name="apple-mobile-web-app-title" content="<?php echo strtoupper($isDomainArray[0]); ?>">
	<meta name="application-name" content="<?php echo strtoupper($isDomainArray[0]); ?>">
	<meta name="msapplication-TileColor" content="#da532c">
	<meta name="theme-color" content="#ffffff">

    <!--<link rel="stylesheet" type="text/css" href="demo_print.css" media="print">-->
    
    
    
	<meta name="msapplication-TileImage" content="<?php echo $this->baseUrl('/mstile-70x70.png'); ?>">
	<meta name="msapplication-TileImage" content="<?php echo $this->baseUrl('/mstile-144x144.png'); ?>">
	<meta name="msapplication-TileImage" content="<?php echo $this->baseUrl('/mstile-150x150.png'); ?>">
	<meta name="msapplication-TileImage" content="<?php echo $this->baseUrl('/mstile-310x150.png'); ?>">
	<meta name="msapplication-TileImage" content="<?php echo $this->baseUrl('/mstile-310x310.png'); ?>">
	<meta name="msapplication-TileImage" content="<?php echo $this->baseUrl('/browserconfig.xml'); ?>">
	
	<?php echo $this->headTitle(); ?>
	
	<link href="<?php echo $this->baseUrl('assets/global/css/style.css?v=4'); ?>" rel="stylesheet"> <!-- MANDATORY -->

	<link href="<?php echo $this->baseUrl('assets/global/css/theme.css?v=6'); ?>" rel="stylesheet"> <!-- MANDATORY -->
	<link href="<?php echo $this->baseUrl('assets/global/css/ui.css?v=3'); ?>" rel="stylesheet"> <!-- MANDATORY -->
	
	<link href="<?php echo $this->baseUrl('assets/md-layout1/material-design/css/material.css'); ?>" rel="stylesheet">
	<link href="<?php echo $this->baseUrl('assets/md-layout1/css/layout.css?v=1'); ?>" rel="stylesheet">

	<link rel="stylesheet" href="<?php echo $this->baseUrl('assets/global/css/custom.css?v=18'); ?>">

	<link rel="stylesheet" href="<?php echo $this->baseUrl('assets/global/css/iconstyles.css?v=27'); ?>">


	<!-- BEGIN PAGE STYLE -->
	<link href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css" rel="stylesheet">
	
	<!--Mailbox - Flaticon css-->
	<link href="<?php echo $this->baseUrl('assets/global/css/icons/flat-icons/flaticon.css') ?>" rel="stylesheet">
	
    <link href="<?php echo $this->baseUrl('assets/global/js/popup/style_popup.css') ?>" rel="stylesheet">
	<link href="<?php echo $this->baseUrl('assets/global/js/popup/magnific-popup.css') ?>"	rel="stylesheet">

	<script src="https://code.jquery.com/jquery-3.7.1.js" integrity="sha256-eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=" crossorigin="anonymous"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-migrate/3.5.2/jquery-migrate.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/ua-parser-js@0/dist/ua-parser.min.js"></script>
	<!-- Add Firebase products that you want to use. firebase-app.js should be required first -->
	<script src="https://www.gstatic.com/firebasejs/6.3.1/firebase-app.js"></script>
	<script src="https://www.gstatic.com/firebasejs/6.3.1/firebase-auth.js"></script>

	<!-- When the user clicks the logout button we should sign out the firebase current user. For that we need to initialize the Firebase -->
	<script>
		// Your web app's Firebase configuration
		var firebaseConfig = {
			apiKey: <?php echo json_encode($firebaseApiKey); ?>,
			authDomain: <?php echo json_encode($firebaseAuthDomain); ?>,
			databaseURL: <?php echo json_encode($firebaseDatabaseURL); ?>,
			projectId: <?php echo json_encode($firebaseProjectId); ?>,
			storageBucket: <?php echo json_encode($firebaseStorageBucket); ?>,
			messagingSenderId: <?php echo json_encode($firebaseMessagingSenderId); ?>,
			appId: <?php echo json_encode($firebaseAppId); ?>,
		};
		// Initialize Firebase
		firebase.initializeApp(firebaseConfig);
	</script>
	<!-- End Firebase -->


	<?php
	// load scripts when the layout is not restriced
	if(empty($layoutRestrictedFor)){
		switch ($controllerName)
		{
			case 'payout':
				if($controllerName == "payout"){
					echo '<link href="'.$this->baseUrl('assets/global/css/monthpicker.css').'" rel="stylesheet">';
					echo '<link href="'.$this->baseUrl('assets/global/css/connected-banking.css?v=1').'" rel="stylesheet">';
					echo '<script src="https://cdnjs.cloudflare.com/ajax/libs/es6-promise/4.1.1/es6-promise.min.js"></script>';
					echo '<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.5.3/jspdf.debug.js"></script>';
					echo '<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.9.1/html2pdf.bundle.min.js"></script>';
					echo '<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js" integrity="sha512-q+4liFwdPC/bNdhUpZx6aXDx/h77yEQtn4I1slHydcbZK34nLaR3cAeYSJshoxIOq3mjEf7xJE8YWIUHMn+oCQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>';
					echo '<link href="'.$this->baseUrl('assets/global/css/customPagination.css').'" rel="stylesheet">'; //css for card pagination
					echo '<link href="https://cdnjs.cloudflare.com/ajax/libs/ion-rangeslider/2.3.0/css/ion.rangeSlider.min.css" rel="stylesheet">'; 	

				}
				break;
			case 'compliance-forms':
				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/formTabStyle.css?v=4').'">';
				break;
			case 'tax-declarations':
				echo '<script src="'. $this->baseUrl('assets/global/plugins/dropzone/dropzone.min.js').'"></script>';
				echo '<link href="'. $this->baseUrl('assets/global/plugins/dropzone/dropzone.min.css').'" rel="stylesheet">';
				// load the tax-declarations css file 
				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/formTabStyle.css?v=4').'">';
				break;
			case 'organization-settings':
				echo '<script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/0.8.1/cropper.min.js"> </script>';
				echo '<link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/cropper/2.3.4/cropper.min.css"/>';

			case 'payout-history':
				echo '<link href="'.$this->baseUrl('assets/global/css/connected-banking.css?v=1').'" rel="stylesheet">';
				echo '<link href="'.$this->baseUrl('assets/global/css/customPagination.css').'" rel="stylesheet">'; //css for card pagination
				break;
			case 'data-import':
				echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/9.0.6/css/intlTelInput.css">';
				echo '<link href="'.$this->baseUrl('assets/global/css/organization/data-import.css').'" rel="stylesheet">';
				echo '<link href="'.$this->baseUrl('assets/global/css/payslip-template-preview-popup.css?v=1').'" rel="stylesheet">';
			break;
			
			case 'salary-payslip':
			case 'payslip-template':
				if($controllerName == "payslip-template"){
					echo '<script src="https://cdnjs.cloudflare.com/ajax/libs/grapesjs/0.16.22/grapes.min.js"></script>';
					echo '<script src="https://cdn.jsdelivr.net/npm/grapesjs-preset-webpage@0.1.11/dist/grapesjs-preset-webpage.min.js"></script>';
					echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/grapesjs/0.16.22/css/grapes.min.css" />';
				}
				if($controllerName == "salary-payslip"){
					echo '<link href="'. $this->baseUrl('assets/global/plugins/step-form-wizard/css/step-form-wizard.min.css').'" rel="stylesheet">'; 
					echo '<link href="'.$this->baseUrl('assets/global/css/monthpicker.css').'" rel="stylesheet">';
					echo '<link href="'.$this->baseUrl('assets/global/css/connected-banking.css?v=1').'" rel="stylesheet">';
					echo '<link href="https://cdnjs.cloudflare.com/ajax/libs/ion-rangeslider/2.3.0/css/ion.rangeSlider.min.css" rel="stylesheet">'; 	
					echo '<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />';
					echo '<link href="'.$this->baseUrl('assets/global/css/customPagination.css').'" rel="stylesheet">'; //css for card pagination		
					echo '<link href="'.$this->baseUrl('assets/global/css/payslip-template-preview-popup.css?v=1').'" rel="stylesheet">';
					echo '<link href="'.$this->baseUrl('assets/global/css/payroll/salary-payslip.css?v=2').'" rel="stylesheet">';
					echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/formTabStyle.css?v=4').'">';
				}
				echo '<script src="https://cdnjs.cloudflare.com/ajax/libs/es6-promise/4.1.1/es6-promise.min.js"></script>';
				echo '<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.5.3/jspdf.debug.js"></script>';
				echo '<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.9.1/html2pdf.bundle.min.js"></script>';
				echo '<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js" integrity="sha512-q+4liFwdPC/bNdhUpZx6aXDx/h77yEQtn4I1slHydcbZK34nLaR3cAeYSJshoxIOq3mjEf7xJE8YWIUHMn+oCQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>';
				break;
			case 'provident-fund':
			case 'etf':
			case 'insurance':
				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/formTabStyle.css?v=4').'">';
				break;
			case 'reimbursement':
				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/formTabStyle.css?v=4').'">';
				break;
			case 'salary': 
				echo '<link href="'.$this->baseUrl('assets/global/css/payslip-template-preview-popup.css?v=1').'" rel="stylesheet">';
			break;
			case 'rounds-management':
				echo '<link href="'.$this->baseUrl('assets/global/css/customPagination.css').'" rel="stylesheet">';
				break;
			case 'leaves':
				echo '<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.2/main.min.js"></script>';
				echo '<script src="https://cdn.jsdelivr.net/npm/exceljs@4.3.0/dist/exceljs.min.js"></script>';
				echo '<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.2/main.min.css">';
				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/formTabStyle.css?v=4').'">';
				echo '<link href="'.$this->baseUrl('assets/global/css/employees/employees.css?v=4').'" rel="stylesheet">';
				break;
			case 'holidays':
				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/formTabStyle.css?v=4').'">';
				break;
			case 'index' : 
				/**set employee id in localstorage */
				echo '<script>localStorage.setItem("LoginEmpId", '.$empId.');</script>';

				// api to get the client ip address
				$clientipUrl = Zend_Registry::get('clientipUrl');
				echo '<script>localStorage.setItem("clientipUrl", "'.$clientipUrl.'");</script>';
				// organization code
				echo '<script>localStorage.setItem("orgCode", "'.$orgCode.'");</script>';


		}
	}
	?>
<style>

/* scroll bar fixed */

	.mCSB_container {
		    width: auto;
		    margin-right: 0;
		    overflow-y: auto;
	}
	
	
	/*#style-4*/
	.mCSB_container::-webkit-scrollbar {
    width: 5px;
	}

	.mCSB_container::-webkit-scrollbar-track {
    background: #888;
	}
 
	.mCSB_container::-webkit-scrollbar-thumb {
    background: #f1f1f1; 
	}

	.mCSB_container::-webkit-scrollbar-thumb:hover {
    background: #f1f1f1; 
	}

	.right-side-sticky-bar {
		padding: 0px;
		margin: 0px;
		position: fixed;
		right: 0px;
		top: 45%;
		width: 42px;
		z-index: 5;
	}

	.right-side-sticky-bar-content {
		padding: 3px;
		background: white;
		position: absolute;
		min-width: 42px;
	}

	.right-side-sticky-bar-content li {
		list-style-type: none;
		background-color: #fff;
		height: 53px;
		cursor: pointer;
		padding: 5px 0px 5px;
		text-align: center;
	}

	.help-screen-panel-container {
		position: fixed;
		right: 0;
		bottom: 80px;
		transform: translateX(100%);
		transition: transform 0.4s ease-in-out;
		z-index: 5;
	}

	.help-screen-panel-container.visible {
		transform: translateX(-10px);
	}

	.help-screen-panel {
		background-color: #fff;
		border: 2px solid var(--primary-color);
		position: relative;
		min-height: 300px;
		width: 400px;
		max-width: calc(100% - 10px);
		border-radius: 15px;
		padding: 15px;
		box-shadow: ;
	}

	.help-screen-panel-close-btn {
		display: flex;
		justify-content: flex-end;
		padding: 5px;
	}

	.help-screen-panel-content {
		font-size: 15px;
		font-weight: bold;
		cursor: pointer;
		background: #FCE4EC;
		padding: 10px;
    	margin: 10px 0px;
		display: flex;
		align-items: center;
	}

	@media screen and (max-width: 480px) {
		.help-screen-panel-container.visible {
			transform: translateX(0px);
		}
		.help-screen-panel {
			width: 100% !important;
		}
	}
</style>
	
	
</head>
<!-- LAYOUT: Apply "submenu-hover" class to body element to have sidebar submenu show on mouse hover -->
<!-- LAYOUT: Apply "sidebar-collapsed" class to body element to have collapsed sidebar -->
<!-- LAYOUT: Apply "sidebar-top" class to body element to have sidebar on top of the page -->
<!-- LAYOUT: Apply "sidebar-hover" class to body element to show sidebar only when your mouse is on left / right corner -->
<!-- LAYOUT: Apply "submenu-hover" class to body element to show sidebar submenu on mouse hover -->
<!-- LAYOUT: Apply "fixed-sidebar" class to body to have fixed sidebar -->
<!-- LAYOUT: Apply "fixed-topbar" class to body to have fixed topbar -->
<!-- LAYOUT: Apply "rtl" class to body to put the sidebar on the right side -->
<!-- LAYOUT: Apply "boxed" class to body to have your page with 1200px max width -->
<!-- THEME STYLE: Apply "theme-sdtl" for Sidebar Dark / Topbar Light -->
<!-- THEME STYLE: Apply  "theme sdtd" for Sidebar Dark / Topbar Dark -->
<!-- THEME STYLE: Apply "theme sltd" for Sidebar Light / Topbar Dark -->
<!-- THEME COLOR: Apply "color-default" for dark color: #2B2E33 -->
<!-- THEME COLOR: Apply "color-primary" for primary color: #319DB5 -->
<!-- THEME COLOR: Apply "color-red" for red color: #C9625F -->
<!-- THEME STYLE: Apply "theme sltl" for Sidebar Light / Topbar Light -->
<!-- THEME COLOR: Apply "color-default" for green color: #18A689 -->
<!-- THEME COLOR: Apply "color-default" for orange color: #B66D39 -->
<!-- THEME COLOR: Apply "color-default" for purple color: #6E62B5 -->
<!-- THEME COLOR: Apply "color-default" for blue color: #4A89DC -->

<!-- BEGIN BODY -->
<body class="fixed-topbar fixed-sidebar bg-light-dark color-default theme-sdtl" data-page="<?php echo $controllerName; ?>">
	<section>
    <?php 	if((isset($_COOKIE['Appmailboxidentity'])) && $moduleName == 'mailbox')
            {
    ?>
	
	
    
    <!-- BEGIN SIDEBAR -->
      <div class="sidebar sidebar-mailbox">
        <div class="logopanel">
		<?php			
			$commonFunction = new Application_Model_DbTable_CommonFunction();
			$signedUrl = $commonFunction->getAwsSignedUrl('ProductLogo','','logoBucket');
			
			if($signedUrl !== ''){
				echo '<div style="text-align:center;">
							<img style="margin-bottom: 10px; max-height:100%; max-width:100%;" src="'.$signedUrl.'" alt="product logo"/>
						</div>';
			}
		?>
		<!--<div href="javascript:void(0);" class="logo" id="hrappLogo"><?php echo strtoupper($isDomainArray[0]); ?></div> -->
        </div>
        <div class="sidebar-inner">
            <div class="sidebar-top">
				<a href="<?php echo $this->baseUrl('/mailbox/index/compose'); ?>" style="width: 54%"
				class="btn btn-secondary btn-compose">Compose</a>
                <!--class="btn btn-secondary-default btn-compose">Compose</a>-->
            </div>
          

       
          <ul class="nav nav-sidebar">
            <?php
                $active = '';
                if(!isset($this->layout()->currFolder)){
                    $active = 'nav-active active';
                }
            ?>
            
			<li class="tm <?php echo $active; ?>"><a href="<?php echo $this->baseUrl('mailbox/index'); ?>"><i class="icons-office-28"></i><span>Inbox</span></a></li>
            
			<?php
            $hostName = "{".$mailDetails['Incoming_Mail_Server']."}";
            
            $connection = imap_open($hostName, $username, $pwd);
			$boxes = imap_list($connection,$hostName,'*');
                        
            $folderCnt = count($boxes);
			
            $availFolders = array('drafts'=>'icons-office-13','sent'=>'icons-chat-messages-14',
                                  'archive'=>'icons-office-19','archives'=>'icons-office-19','trash'=>'icons-office-57','junk'=>'icons-alerts-08');
            
            for ($i=1; $i<$folderCnt; $i++)
            {
                $folder = substr($boxes[$i],strrpos($boxes[$i], ".")+1, strlen($boxes[$i]));
                $active = '';
				$disp = '';
				
				if(isset($this->layout()->currFolder) && ($this->layout()->currFolder == $folder || is_numeric($folder))){
                    if(is_numeric($folder))
                    {
                        $fol = explode('.',$_SERVER['REQUEST_URI']);
                        
                        if($folder == $fol[1])
                        {
                            $active = 'nav-active active';
                        }
                    }
                    else
                        $active = 'nav-active active';
                }
                
                if(array_key_exists(strtolower($folder), $availFolders))
                {
                    $iconCls = $availFolders[strtolower($folder)];
					$style = '';
                }
                else
                {
					$iconCls = 'icons-office-55';
					$style = "padding-left:30px";
                    //$iconCls = 'common';
                }
				
				$fold = $folder;
				$hidecls = $folder;
				if(is_numeric($folder))
				{
					$folder = 'Archives.'.$folder;
					if($active)
                        $disp = '';
                    else
                        $disp = "display:none";
					$hidecls = "number";
				}
				
            ?>
			
            <li class="tm <?php echo $active;  echo ' '.$hidecls; ?>" style=" <?php echo $disp; ?>"><a href="<?php echo $this->baseUrl('mailbox/index/index/folder/'.$folder); ?>"><i class="<?php echo $iconCls; ?>" style="<?php echo $style; ?>"></i><span><?php echo $fold;?> </span></a></li>
            
            <?php
            
			}
			
            if($isProduction == 0)
                    $homeref = "/hrapp";
            else
                $homeref = "/";
            
            ?>

          </ul>
          <div class="sidebar-widgets m-b-30">
          </div>
          <div class="sidebar-footer clearfix">
            <a class="pull-left footer-settings"  href="<?php echo $homeref; ?>" data-rel="tooltip" data-original-title="Home">
            <i class="fa fa-home"  style="font-size: 20px"></i></a>
            <a class="pull-left toggle_fullscreen" href="#" data-rel="tooltip" data-original-title="Fullscreen">
            <i class="icon-size-fullscreen"></i></a>
            <a class="pull-left" href="#" data-rel="tooltip" data-original-title="Lockscreen">
            <i class="icon-lock"></i></a>
            <a class="pull-left btn-effect maillog" href="#" data-modal="modal-1" data-rel="tooltip" data-placement="top" data-original-title="Exit Mailbox">
                <i class="icon-power"></i>
            </a>
          </div>
        </div>
      </div>
    
    <?php } else
    {

    ?>
    	<!-- BEGIN SIDEBAR -->
		<div class="sidebar">
			<div class="logopanel">
				<?php
					$commonFunction = new Application_Model_DbTable_CommonFunction();
					$signedUrl = $commonFunction->getAwsSignedUrl('ProductLogo','','logoBucket');
					
					if($signedUrl !== ''){
						echo '<div style="text-align:center;">
									<img style="margin-bottom: 10px; max-height:100%; max-width:100%;" src="'.$signedUrl.'" alt="product logo"/>
								</div>';
					}
				?>
				<!--<div href="javascript:void(0);" class="logo" id="hrappLogo"><?php echo strtoupper($isDomainArray[0]); ?></div> -->
			</div>
			<div class="sidebar-inner" id="sidebar-scroll">
				<div class="sidebar-top">
					
				</div>
				<!-- if the layout is restricted we can add hidden class to sidebar menus to prevent presenting -->
				<div id="menu-wrapper" class="<?php echo (empty($layoutRestrictedFor)) ? '' : 'hidden'; ?>">
					<ul class="nav nav-sidebar">
					<li class="tm hrapp-sidebar-module-class hrapp-sidebar-modules-content">
						<a class="hrapp-sidebar-modules" href="<?php echo $this->baseUrl('/in/quick-menu'); ?>" id="quickMenuModule"><div class="row hrapp-module-icon"><i class="hr-quick-menu hrapp-icon-size"></i></div><div class="row hrapp-menu-text">Quick Menu</div>
						</a>
					</li>
					<?php
						$dashboardType = isset($_COOKIE['orgSubscribedPlan']) ? $_COOKIE['orgSubscribedPlan'] : 'HRMSDASHBOARD';
						//If the dashboard is 'HRMSDASHBOARD' present the dashboard in the sidebar
						if($dashboardType === 'HRMSDASHBOARD'){
					?>
					<li class="tm hrapp-sidebar-module-class <?php echo ($moduleName == 'default' && $controllerName == 'index') ? 'nav-active active hrapp-sidebar-active' : ''; ?> hrapp-sidebar-modules-content">
							<a class="hrapp-sidebar-modules" href="<?php echo $this->baseUrl('/'); ?>" id="dashboardModule"><div class="row hrapp-module-icon"><i class="hr-dashboard hrapp-icon-size"></i></div><div class="row hrapp-menu-text">Dashboard</div>
						</a>
					</li>
					<?php }
						foreach ($modules as $key => $row)
						{
							$menuName = trim($row['Module_Name']);
							if(($menuName != 'Home' && $menuName != 'Core HR' && $menuName != 'Billing' && $menuName != 'My Team')
							 	|| ($menuName == 'Billing' && $autoBilling == 1) 
								|| ($menuName == 'Core HR' && $isAnyAdmin) 
								|| ($menuName == 'My Team' && ($isManager || $isAnyAdmin)))
							{
								if (in_array($controllerName, $payrollFormsUnderTaxStatutoryModule)) {
									$activeModule = 'tax-and-statutory-compliance';
								} else if (in_array($controllerName, $formsManagerFormsUnderTaxStatutoryModule)) {
									$activeModule = 'tax-and-statutory-compliance';
								} else {
									$activeModule = $moduleName;
								}
								$activeModuleCls = (str_replace(' ', '-', strtolower($menuName))) == $activeModule ? 'nav-active active' : '';
								$activeSideBarModule = (str_replace(' ', '-', strtolower($menuName))) == $activeModule ? 'hrapp-sidebar-active' : '';

								// add this html tag after '<a class="hrapp-sidebar-modules"....' when module highlight label is required
								$moduleNewLabel = '<div class="row hrapp-module-new-label-outer"><div class="hrapp-module-new-label">New</div></div>';
								// add this class with 'hrapp-module-icon' when module highlight label is required
								$newLabelClass = 'hrapp-module-label-icon';
								
								if(array_key_exists($menuName,$hrappForms))
								{
									switch ($menuName)
									{
										case 'Organization' :
											echo '<li class="tm hrapp-sidebar-module-class nav-parent '.$activeSideBarModule.' '.$activeModuleCls.' hrapp-sidebar-modules-content">'.
													'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl(str_replace(' ', '-', strtolower($menuName)) . '/locations').'" id="'.$menuName.'"><div class="row hrapp-module-icon"><i class="icon hr-organization hrapp-icon-size"></i></div><div class="row hrapp-menu-text">'.$menuName.'</div></a>'.
													'<ul class="children collapse">';
											break;
										
										case 'Man Power Planning' :
											echo '<li class="tm hrapp-sidebar-module-class nav-parent '.$activeSideBarModule.' '.$activeModuleCls.' hrapp-sidebar-modules-content">'.
												'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl('/v3/man-power-planning/hiring-forecast').'" id="'.$menuName.'">'.
													'<div class="row hrapp-module-icon">
														<i class="icon hr-man-power-planning hrapp-icon-size"></i>
													</div>
													<div class="row hrapp-menu-text">'.$menuName.'</div>
												</a>'.
												'<ul class="children collapse">';
											break;
										case 'My Finance' :
											echo '<li class="tm hrapp-sidebar-module-class nav-parent '.$activeSideBarModule.' '.$activeModuleCls.' hrapp-sidebar-modules-content">'.
												'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl('/v3/my-finance/travel-and-expenses').'" id="'.$menuName.'">'.
													'<div class="row hrapp-module-icon">
														<i class="icon hr-my-finance hrapp-icon-size"></i>
													</div>
													<div class="row hrapp-menu-text">'.$menuName.'</div>
												</a>'.
												'<ul class="children collapse">';
											break;
											case 'Approvals' :
												echo '<li class="tm hrapp-sidebar-module-class nav-parent '.$activeSideBarModule.' '.$activeModuleCls.' hrapp-sidebar-modules-content">'.
													'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl('/v3/approvals/approval-management').'" id="'.$menuName.'">'.
														'<div class="row hrapp-module-icon">
															<i class="icon hr-approvals hrapp-icon-size"></i>
														</div>
														<div class="row hrapp-menu-text">'.$menuName.'</div>
													</a>'.
													'<ul class="children collapse">';
												break;
										case 'Recruitment':
											echo '<li class="tm hrapp-sidebar-module-class nav-parent '.$activeSideBarModule.' '.$activeModuleCls.' hrapp-sidebar-modules-content">'.
													'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl(str_replace(' ', '-', strtolower($menuName)) . '/job-post-requisition').'" id="'.$menuName.'">
													<div class="row hrapp-module-icon"><i class="icon hr-recuritment hrapp-icon-size"></i></div><div class="row hrapp-menu-text">'.$menuName.'</div></a>'.
													'<ul class="children collapse">';
											break;
										
										case 'Employees':
											echo '<li class="tm hrapp-sidebar-module-class nav-parent '.$activeSideBarModule.' '.$activeModuleCls.' hrapp-sidebar-modules-content">'.
													'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl(str_replace(' ', '-', strtolower($menuName)) . '/employees').'" id="'.$menuName.'"><div class="row hrapp-module-icon"><i class="icon hr-employee hrapp-icon-size"></i></div><div class="row hrapp-menu-text">'.$menuName.'</div></a>'.
													'<ul class="children collapse">';
											break;
										
										case 'Performance Management':
											echo '<li class="tm hrapp-sidebar-module-class nav-parent '.$activeSideBarModule.' '.$activeModuleCls.' hrapp-sidebar-modules-content">'.
													'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl(str_replace(' ', '-', strtolower($menuName)) . '/trulead').'" id="'.$menuName.'">
														<div class="row hrapp-module-icon">
															<i class="icon hr-performance-management hrapp-icon-size"></i>
														</div>
														<div class="row hrapp-menu-text">'.$menuName.'</div>
													</a>'.
													'<ul class="children collapse">';
											break;	
										
										case 'Payroll':
											echo '<li class="tm hrapp-sidebar-module-class nav-parent '.$activeSideBarModule.' '.$activeModuleCls.' hrapp-sidebar-modules-content">'.
													'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl(str_replace(' ', '-', strtolower($menuName)) . '/salary').'" id="'.$menuName.'"><div class="row hrapp-module-icon"><i class="icon hr-payroll-2 hrapp-icon-size"></i></div><div class="row hrapp-menu-text">'.$menuName.'</div></a>'.
													'<ul class="children collapse">';
											break;
										
										case 'Tax and Statutory Compliance':
											echo '<li class="tm hrapp-sidebar-module-class nav-parent '.$activeSideBarModule.' '.$activeModuleCls.' hrapp-sidebar-modules-content">'.
													'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl('payroll/tax-rules').'" id="'.$menuName.'">
													<div class="row hrapp-module-icon"><i class="icon hr-tax-and-statutory-compliance hrapp-icon-size"></i></div><div class="row hrapp-menu-text">'.$menuName.'</div></a>'.
													'<ul class="children collapse">';
											break;
										
										case 'Benefits':
											echo '<li class="tm hrapp-sidebar-module-class nav-parent '.$activeSideBarModule.' '.$activeModuleCls.' hrapp-sidebar-modules-content">'.
													'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl('/in/benefits/esop').'" id="'.$menuName.'">'.
													$moduleNewLabel.
													'<div class="row hrapp-module-icon '.$newLabelClass.'"><i class="icon hr-benefits hrapp-icon-size"></i></div><div class="row hrapp-menu-text">'.$menuName.'</div></a>'.
													'<ul class="children collapse">';
											break;
										
										case 'Core HR':
												echo '<li class="tm hrapp-sidebar-module-class nav-parent '.$activeSideBarModule.' '.$activeModuleCls.' hrapp-sidebar-modules-content">'.
														'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl('/in/core-hr/work-schedule').'" id="'.$menuName.'">'.
														'<div class="row hrapp-module-icon"><i class="icon hr-core-hr hrapp-icon-size"></i></div><div class="row hrapp-menu-text">'.$menuName.'</div></a>'.
														'<ul class="children collapse">';
											break;

										case 'Onboarding':
												echo '<li class="tm hrapp-sidebar-module-class nav-parent '.$activeSideBarModule.' '.$activeModuleCls.' hrapp-sidebar-modules-content">'.
														'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl('/in/onboarding/vendors').'" id="'.$menuName.'">'.
														'<div class="row hrapp-module-icon"><i class="icon hr-onboarding hrapp-icon-size"></i></div><div class="row hrapp-menu-text">'.$menuName.'</div></a>'.
														'<ul class="children collapse">';
											break;

										case 'Data Loss Prevention':
											echo '<li class="tm hrapp-sidebar-module-class nav-parent '.$activeSideBarModule.' '.$activeModuleCls.' hrapp-sidebar-modules-content">'.
													'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl('/in/data-loss-prevention/file-transfers').'" id="'.$menuName.'">'.
													'<div class="row hrapp-module-icon"><i class="icon hr-data-loss-prevention hrapp-icon-size"></i></div><div class="row hrapp-menu-text">'.$menuName.'</div></a>'.
													'<ul class="children collapse">';
										break;

										case 'Help':
											echo '<li class="tm hrapp-sidebar-module-class nav-parent '.$activeSideBarModule.' '.$activeModuleCls.' hrapp-sidebar-modules-content">'.
													'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl(str_replace(' ', '-', strtolower($menuName)) . '/help-topics').'" id="'.$menuName.'"><div class="row hrapp-module-icon"><i class="icon hr-help hrapp-icon-size"></i></div><div class="row hrapp-menu-text">'.$menuName.'</div></a>'.
													'<ul class="children collapse">';
											break;
										
										case 'Reports':
											echo '<li class="tm hrapp-sidebar-module-class nav-parent '.$activeSideBarModule.' '.$activeModuleCls.' hrapp-sidebar-modules-content">'.
													'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl(str_replace(' ', '-', strtolower($menuName)) . '/attendance-shortage-report').'" id="'.$menuName.'"><div class="row hrapp-module-icon"><i class="icon hr-report hrapp-icon-size"></i></div><div class="row hrapp-menu-text">'.$menuName.'</div></a>'.
													'<ul class="children collapse">';
													
											break;
										case 'Compliance Management':
											echo '<li class="tm hrapp-sidebar-module-class nav-parent '.$activeSideBarModule.' '.$activeModuleCls.' hrapp-sidebar-modules-content">'.
													'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl(str_replace(' ', '-', strtolower($menuName)) . '/compliance-management').'" id="'.$menuName.'">
													<div class="row hrapp-module-icon"><i class="icon hr-compliance-management hrapp-icon-size"></i></div><div class="row hrapp-menu-text">'.$menuName.'</div></a>'.
													'<ul class="children collapse">';
											break;
										case 'Roster Management':
											echo '<li class="tm hrapp-sidebar-module-class nav-parent '.$activeSideBarModule.' '.$activeModuleCls.' hrapp-sidebar-modules-content">'.
													'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl(str_replace(' ', '-', strtolower($menuName)) . '/calendar-view').'" id="'.$menuName.'"><div class="row hrapp-module-icon"><i class="icon hr-roster-management hrapp-icon-size"></i></div><div class="row hrapp-menu-text">'.$menuName.'</div></a>'.
													'<ul class="children collapse">';
											break;
										case 'Integration':
											echo '<li class="tm hrapp-sidebar-module-class nav-parent '.$activeSideBarModule.' '.$activeModuleCls.' hrapp-sidebar-modules-content">'.
													'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl(str_replace(' ', '-', strtolower($menuName)) . '/subscription/api-dashboard').'" id="'.$menuName.'"><div class="row hrapp-module-icon"><i class="icon hr-collapse hrapp-icon-size"></i></div><div class="row hrapp-menu-text">'.$menuName.'</div></a>'.
													'<ul class="children collapse">';
											break;
										case 'Workflow':
										echo '<li class="tm hrapp-sidebar-module-class nav-parent '.$activeSideBarModule.' '.$activeModuleCls.' hrapp-sidebar-modules-content">'.
												'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl(str_replace(' ', '-', strtolower($menuName)) . '/workflow/workflow').'" id="'.$menuName.'"><div class="row hrapp-module-icon"><i class="icon hr-workflow hrapp-icon-size"></i></div><div class="row hrapp-menu-text">'.$menuName.'</div></a>'.
												'<ul class="children collapse">';
											break;
										case 'Settings':
										
										echo '<li class="tm hrapp-sidebar-module-class nav-parent '.$activeSideBarModule.' '.$activeModuleCls.' hrapp-sidebar-modules-content">'.
												'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl(str_replace(' ', '-', strtolower($menuName)) . '/settings/ip-whitelisting').'" id="'.$menuName.'"><div class="row hrapp-module-icon"><i class="icon hr-settings hrapp-icon-size"></i></div><div class="row hrapp-menu-text">'.$menuName.'</div></a>'.
												'<ul class="children collapse">';
											break;
										case 'Productivity Monitoring':
											
											echo '<li class="tm hrapp-sidebar-module-class nav-parent '.$activeSideBarModule.' '.$activeModuleCls.' hrapp-sidebar-modules-content">'.
													'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl('/in/productivity-monitoring/activity-tracker').'" id="'.$menuName.'">'.
													'<div class="row hrapp-module-icon"><i class="icon hr-productivity-monitoring hrapp-icon-size"></i></div><div class="row hrapp-menu-text">'.$menuName.'</div></a>'.
													'<ul class="children collapse">';
										break;
										case 'Asset Management':
											
										echo '<li class="tm hrapp-sidebar-module-class nav-parent '.$activeSideBarModule.' '.$activeModuleCls.' hrapp-sidebar-modules-content">'.
												'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl('/in/asset-management/assets').'" id="'.$menuName.'">'.
												'<div class="row hrapp-module-icon"><i class="icon hr-asset-management hrapp-icon-size"></i></div><div class="row hrapp-menu-text">'.$menuName.'</div></a>'.
												'<ul class="children collapse">';
										break;

										case 'Employee Self Service':
											echo '<li class="tm hrapp-sidebar-module-class nav-parent '.$activeSideBarModule.' '.$activeModuleCls.' hrapp-sidebar-modules-content">'.
												'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl('/v3/employee-self-service/pre-approval').'" id="'.$menuName.'">'.
													'<div class="row hrapp-module-icon">
														<i class="icon hr-employee-self-service hrapp-icon-size"></i>
													</div>
													<div class="row hrapp-menu-text">'.$menuName.'</div>
												</a>'.
												'<ul class="children collapse">';
											break;

										case 'My Team':
											echo '<li class="tm hrapp-sidebar-module-class nav-parent '.$activeSideBarModule.' '.$activeModuleCls.' hrapp-sidebar-modules-content">'.
												'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl('/v3/my-team/team-summary').'" id="'.$menuName.'">'.
													'<div class="row hrapp-module-icon">
														<i class="icon hr-my-team hrapp-icon-size"></i>
													</div>
													<div class="row hrapp-menu-text">'.$menuName.'</div>
												</a>'.
												'<ul class="children collapse">';
											break;

										case 'Billing':
											echo '<li class="tm hrapp-sidebar-module-class hrapp-sidebar-modules-content">'.
													'<a class="hrapp-sidebar-modules" href="'.$this->baseUrl('/in/billing').'" id="'.$menuName.'">'.
														'<div class="row hrapp-module-icon">
															<i class="hr-billing hrapp-icon-size"></i>
														</div>
														<div class="row hrapp-menu-text">'.$menuName.'</div>
													</a>';
											break;
									}   
								
									if(!empty($hrappForms) && $menuName !== "Billing")
									{
										foreach ($hrappForms[$menuName] as $formsName)
										{
											$formName = $formsName['Form_Name'];
											$presentationFormName = isset($formsName['New_Form_Name']) ? $formsName['New_Form_Name'] :$formName;
											$actualFormId = $formsName['Form_Id'];
											$activeFormCls = (str_replace(' ', '-', strtolower(trim($formName))) == $controllerName) ? 'active' : '';
											
											if ($menuName == 'Reports' && $formName != 'Attendance Shortage Report' && $formName != 'Absentees Report'
												&& $formName != 'Monthly Shortage Report'&& $formName != 'Timeline' && $formName != 'Custom Report')
											{
												$reports = $dbModules->reportsSubMenu($formName);
												
												$modName = $reports['Report_SubMenu_Title'];
												$modId = $reports['Report_SubMenu_ID'];
												$dbHRReport = new Reports_Model_DbTable_HrReports();
												$repTitleArray = $dbHRReport->repTitles($modId , $modName);                                           
												$reportUrl  = $_SERVER['REQUEST_URI'];
												$formIcon  = '';
												$explodeUrl = explode("/",$reportUrl);
												for ($y=0; $y<count($explodeUrl); $y++) 
												{
													if ($explodeUrl[$y] == '_mId') 
													{
													$formIcon = $explodeUrl[$y+1];
													}
												}
												$activeFormCls = (str_replace(' ', '-', strtolower(trim($formName))) == $formIcon) ? 'active' : '';
												$iconsCls = str_replace(' ', '-', strtolower(trim($formName)));
												$linkValue = $repTitleArray[0]['Rep_Title'];
												$linkValue = str_replace(' ', '-', strtolower(trim($linkValue)));
												
												if($formName == 'Employees Reports')
												{												
													echo '<li class="'.$activeFormCls.'">
														<a href="'. $this->baseUrl(str_replace(' ', '-', strtolower($menuName)) . '/hr-reports/view-employees-reports/').'">
															<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
															'.$presentationFormName.'
														</a>
													</li>';
												}
												elseif($formName == 'HR Reports')
												{
													echo '<li class="'.$activeFormCls.'">
														<a href="'. $this->baseUrl(str_replace(' ', '-', strtolower($menuName)) . '/hr-reports/view-hrreports/').'">                                                
															<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
															'.$presentationFormName.'
														</a>
													</li>';
												}
												elseif($formName == 'Payroll Reports')
												{
													echo '<li class="'.$activeFormCls.'">
														<a href="'. $this->baseUrl(str_replace(' ', '-', strtolower($menuName)) . '/hr-reports/view-payroll-reports/').'">                                                
															<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
															'.$presentationFormName.'
														</a>
													</li>';
												}
												elseif($formName == 'Recruitment Reports')
												{
													echo '<li class="'.$activeFormCls.'">
														<a href="'. $this->baseUrl(str_replace(' ', '-', strtolower($menuName)) . '/hr-reports/view-recruitment-reports/').'">                                                
															<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
															'.$presentationFormName.'
														</a>
													</li>';
												}											
											}
											else if ($formName == 'Timeline' || $formName == 'Custom Report')
											{
												$reportsubmenu = trim($formName);
												$frmAccess = $dbModules->formAccessRights($empId, $actualFormId);
												if($frmAccess > 0)
												{
													$reportUrl  = $_SERVER['REQUEST_URI'];
													$formIcon  = '';
													$explodeUrl = explode("/",$reportUrl);
													for ($y=0; $y<count($explodeUrl); $y++) 
													{
														if ($explodeUrl[$y] == 'reports') 
														{
														$formIcon = $explodeUrl[$y+1];
														}
													}
													$activeFormCls = (str_replace(' ', '-', strtolower(trim($formName))) == $formIcon) ? 'active' : '';
													
													$iconsCls = ($formName == 'Timeline'?'timeline':'reports-custom-report');

													echo '<li class="'.$activeFormCls.'">
															<a href="'. $this->baseUrl(str_replace(' ', '-', strtolower($menuName)) . '/'. str_replace(' ', '-', strtolower($formName))) . '">
																<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>'.$presentationFormName.
																
															'</a>
														</li>';
												}
											}
											else if($menuName == 'Help' && $formName == 'Support')
											{
												echo '<li class="'.$activeFormCls.'">
														<a href="'.$domainDetails['Support_Link'].'">                                                
															<i class="icon hr-support-ticket hrapp-icon-size"></i>
															'.$presentationFormName.'                                                        
														</a>
													</li>';
											}
											else if($menuName == 'Help' && $formName == 'Contact Us')
											{
												echo '<li class="'.$activeFormCls.'">
														<a href="mailto:'.$domainDetails['Support_Email'].'">                                                
															<i class="icon hr-help-contact-us hrapp-icon-size"></i>
															'.$presentationFormName.'                                                        
														</a>
													</li>';
											}		
											else if($menuName == 'Integration')
											{
												if($formName == "API Dashboard")
													$iconsCls = "share";
												else if($formName == "GVP")
													$iconsCls = "document-text-accept";
												else if(($formName == "OCR"))
													$iconsCls = "zoom-in";
												echo '<li class="'.$activeFormCls.'">
														<a href="'. $this->baseUrl(str_replace(' ', '-', strtolower($menuName)) . '/subscription/'. str_replace(' ', '-', strtolower($formName))) . '">
															<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>'.$presentationFormName.
														
														'</a>
													</li>';
											}	
											else if($menuName === 'Productivity Monitoring' && $formName === 'Activity Tracker')
											{
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
												echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/in/productivity-monitoring/activity-tracker').'">                                                
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
												</li>';
											}
											else if($menuName === 'Productivity Monitoring' && $formName === 'Members')
											{
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
												$memberAccessRights = $dbAccessRights->employeeAccessRights($empId,  $formName);
												// we show members form only for admins
												if ($memberAccessRights['Admin'] === "admin") {
													echo '<li class="'.$activeFormCls.'">
														<a href="'. $this->baseUrl('/in/productivity-monitoring/members').'">                                                
															<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
															'.$presentationFormName.'
														</a>
													</li>';
												}
											}	
	
											else if($menuName === 'Productivity Monitoring' && $formName === 'Reports')
											{
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
												echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/in/productivity-monitoring/reports').'">                                                
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
												</li>';
											}
											else if($menuName === 'Productivity Monitoring' && $formName === 'Activity Dashboard')
											{
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
												echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/in/productivity-monitoring/activity-dashboard').'">                                                
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
												</li>';
											}
											else if($menuName === 'Productivity Monitoring' && $formName === 'Workforce Analytics')
											{
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
												echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/in/productivity-monitoring/workforce-analytics').'">                                                
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
												</li>';
											}	
											else if($menuName === 'Compliance Management' && $formName === 'DocuSign')
											{
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
												echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/in/compliance-management/docusign').'">                                                
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
												</li>';
											}				
											else if($menuName === 'Compliance Management' && $formName === 'Accreditation')
											{
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
												echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/in/compliance-management/accreditation').'">                                                
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
												</li>';
											}	
											else if($menuName === 'Settings' && $formName === 'Productivity Monitoring')
											{
												echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/in/settings/productivity-monitoring').'">                                                
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
												</li>';
											}				
											else if($menuName === 'Settings' && $formName === 'Core HR')
											{
												echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/in/settings/core-hr').'">                                                
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
												</li>';
											}
											else if($menuName === 'Data Loss Prevention' && $formName === 'File Transfers')
											{
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
												echo '<li class="'.$activeFormCls.'">
														<a href="'. $this->baseUrl('/in/data-loss-prevention/file-transfers').'">
															<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
															'.$presentationFormName.'
														</a>
													</li>';
											}
											else if($menuName === 'Settings' && $formName === 'Data Loss Prevention')
											{
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
												echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/in/settings/data-loss-prevention').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
												</li>';
											}
											else if($menuName === 'Settings' && $formName === 'Payroll')
                                            {
                                                echo '<li class="'.$activeFormCls.'">
                                                    <a href="'. $this->baseUrl('/v3/settings/payroll/general').'">
                                                        <i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
                                                        '.$presentationFormName.'
                                                    </a>
                                                </li>';
											}
											else if($menuName === 'Roster Management' && $formName === 'Shift Scheduling')
                                            {
                                                echo '<li class="'.$activeFormCls.'">
                                                    <a href="'. $this->baseUrl('/in/roster-management/shift-scheduling').'">
                                                        <i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
                                                        '.$presentationFormName.'
                                                    </a>
                                                </li>';
											}
											else if($menuName === 'Roster Management' && $formName === 'Shift Swap')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/roster-management/shift-swap').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a> 
                                                </li>';
											}
											else if(strtolower($menuName) === 'payroll' && strtolower($formName) === 'payroll management')		{
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
												$userName = isset($_COOKIE['entomoUserName']) ? $_COOKIE['entomoUserName'] : '';
												if($orgDetails["Payroll_Integration_Url"]) {
													$payrollUrl = $orgDetails["Payroll_Integration_Url"];
													if ($userName) {
														$payrollUrl .= $userName;
													}
													echo '<li class="'.$activeFormCls.'">
														<a href="'. $payrollUrl.'">
															<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
															'.$presentationFormName.'
														</a>
													</li>';
												} else {
												echo '<li class="'.$activeFormCls.'">
														<a href="'. $this->baseUrl('/in/payroll/payroll-management').'">
															<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
															'.$presentationFormName.'
														</a>
													</li>';
												}
											}
											else if($menuName === 'Payroll' && $formName === 'Salary Template')
											{
													echo '<li class="'.$activeFormCls.'">
															<a href="'. $this->baseUrl('/in/payroll/salary-template').'">
																	<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
																	'.$presentationFormName.'
															</a>
													</li>';
											}
											else if($menuName === 'Payroll' && $formName === 'Additional Wage Claim')
											{
													$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
													echo '<li class="'.$activeFormCls.'">
															<a href="'. $this->baseUrl('/in/payroll/additional-wage-claim').'">
																	<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
																	'.$presentationFormName.'
															</a>
													</li>';
											}
											else if($menuName === 'Benefits' && $formName === 'ESOP')
											{
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
												echo '<li class="'.$activeFormCls.'">
														<a href="'. $this->baseUrl('/in/benefits/esop').'">
															<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
															'.$presentationFormName.'
														</a>
													</li>';
											}
											else if($menuName === 'Settings' && $formName === 'Tax and Statutory Compliance')
											{
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
												echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/in/settings/tax-and-statutory-compliance').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
												</li>';
											}	
											else if($menuName === 'Asset Management' && $formName === 'Assets')
											{
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
												echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/in/asset-management/assets').'">                                                
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
												</li>';
											}
											else if($menuName === 'Settings' && $formName === 'Performance Management')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
                                                    <a href="'. $this->baseUrl('/in/settings/performance-management').'">
                                                        <i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
                                                        '.$presentationFormName.'
                                                    </a>
                                                </li>';
											}
											else if($menuName === 'Settings' && $formName === 'General')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
                                                    <a href="'. $this->baseUrl('v3/settings/general').'">
                                                        <i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
                                                        '.$presentationFormName.'
                                                    </a>
                                                </li>';
											}
											// setting up the redirection link for 'Integration' main form
											else if($menuName === 'Settings' && $formName === 'Integration')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
                                                    <a href="'. $this->baseUrl('/v3/settings/integration').'">
                                                        <i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
                                                        '.$presentationFormName.'
                                                    </a>
                                                </li>';
											}
											else if($menuName === 'Settings' && $formName === 'Recruitment')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
                                                    <a href="'. $this->baseUrl('/v3/settings/recruitment').'">
                                                        <i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
                                                        '.$presentationFormName.'
                                                    </a>
                                                </li>';
											}
											else if($menuName === 'Performance Management' && $formName === 'Trulead')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
                                                    <a id="truleadRedirection">
                                                        <i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
                                                        '.$presentationFormName.'
                                                    </a>
                                                </li>';
											} 
											else if($menuName === 'Core HR' && $formName === 'Register Face')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
                                                    <a href="'. $this->baseUrl('/in/core-hr/register-face').'">
                                                        <i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
                                                        '.$presentationFormName.'
                                                    </a>
                                                </li>';
											}
											else if($menuName === 'Onboarding' && $formName === 'Vendors')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
                                                    <a href="'. $this->baseUrl('/in/onboarding/vendors').'">
                                                        <i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
                                                        '.$presentationFormName.'
                                                    </a>
                                                </li>';
											}
											else if($menuName === 'Core HR' && $formName === 'Payroll Data Management')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/core-hr/payroll-data-management').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Core HR' && $formName === 'Employee Data Management')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/in/core-hr/employee-data-management').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Recruitment' && $formName === 'Job Posts')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/recruitment/job-posts').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Recruitment' && $formName === 'Dashboard')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/recruitment/dashboard').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Approvals' && $formName === 'Approval Management')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/approvals/approval-management').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Workflow' && $formName === 'Dynamic Form Builder')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/workflow/dynamic-form-builder').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Workflow' && $formName === 'Workflow Builder')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/workflow/workflow-builder').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Man Power Planning' && $formName === 'Settings')
                                            {
                                                $iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
                                                    <a href="'. $this->baseUrl('/v3/man-power-planning/settings').'">
                                                        <i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
                                                        '.$presentationFormName.'
                                                    </a>
                                                </li>';
                                            }
											else if($menuName === 'Man Power Planning' && $formName === 'Hiring Forecast')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/man-power-planning/hiring-forecast').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Man Power Planning' && $formName === 'Job Requisition')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/man-power-planning/job-requisition').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Man Power Planning' && $formName === 'Table of Organization')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/man-power-planning/table-of-organization').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Core HR' && $formName === 'Time Off Management')
                                            {
                                                $iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
                                                    <a href="'. $this->baseUrl('/v3/core-hr/time-off-management').'">
                                                        <i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
                                                        '.$presentationFormName.'
                                                    </a>
                                                </li>';
                                            }
											else if($menuName === 'Recruitment' && $formName === 'Interview Rounds Master')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/recruitment/interview-rounds-master').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Recruitment' && $formName === 'Job Candidates')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/recruitment/job-candidates').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Recruitment' && $formName === 'My Integration')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/recruitment/my-integration').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Recruitment' && $formName === 'Careers')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/careers').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Onboarding' && $formName === 'Individuals')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
                                                    <a href="'. $this->baseUrl('/v3/onboarding/individuals').'">
                                                        <i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
                                                        '.$presentationFormName.'
                                                    </a>
                                                </li>';
											}
											else if($menuName === 'Organization' && $formName === 'Special Wages')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
                                                    <a href="'. $this->baseUrl('/organization/holidays').'">
                                                        <i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
                                                        '.$presentationFormName.'
                                                    </a>
                                                </li>';
											}
											else if($menuName === 'Employee Self Service' && $formName === 'Pre Approval')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/employee-self-service/pre-approval').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Employee Self Service' && $formName === 'Organization Chart')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/employee-self-service/organization-chart').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'My Finance' && $formName === 'Travel and Expenses')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/my-finance/travel-and-expenses').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'My Finance' && $formName === 'My Pay')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/my-finance/my-pay').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Employee Self Service' && $formName === 'Attendance')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/employee-self-service/attendance').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Employee Self Service' && $formName === 'Time Off')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/employee-self-service/time-off').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Employee Self Service' && $formName === 'Organization Chart')
                                            {
												$iconsCls = str_replace(' ', '-', 'employees') . '-'. str_replace(' ', '-', 'organization-chart');
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/employees/organization-chart').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Employee Self Service' && $formName === 'My Profile')
                                            {
												$iconsCls = str_replace(' ', '-', 'employees') . '-'. str_replace(' ', '-', 'employees');
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/employee-self-service/my-profile').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'My Team' && $formName === 'Exit Management')
                                            {
                                                $iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
                                                    <a href="'. $this->baseUrl('/v3/my-team/exit-management').'">
                                                        <i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
                                                        '.$presentationFormName.'
                                                    </a>
                                                </li>';
                                            }
											else if($menuName === 'My Team' && $formName === 'Attendance')
                                            {
                                                $iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
                                                    <a href="'. $this->baseUrl('/v3/my-team/attendance').'">
                                                        <i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
                                                        '.$presentationFormName.'
                                                    </a>
                                                </li>';
                                            }
											else if($menuName === 'My Team' && $formName === 'Payroll Management')
                                            {
                                                $iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
                                                    <a href="'. $this->baseUrl('/v3/my-team/payroll-management').'">
                                                        <i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
                                                        '.$presentationFormName.'
                                                    </a>
                                                </li>';
                                            }
											else if($menuName === 'My Team' && $formName === 'Travel and Expenses')
                                            {
                                                $iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
                                                    <a href="'. $this->baseUrl('/v3/my-team/travel-and-expenses').'">
                                                        <i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
                                                        '.$presentationFormName.'
                                                    </a>
                                                </li>';
                                            }
											else if($menuName === 'My Team'  && $formName === 'Pre Approval')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/my-team/pre-approval').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Employee Self Service' && $formName === 'Compensatory Off Balance')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/employee-self-service/compensatory-off-balance').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'My Team'  && $formName === 'Compensatory Off Balance')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/my-team/compensatory-off-balance').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Employee Self Service' && $formName === 'LOP Recovery')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/employee-self-service/lop-recovery').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Tax and Statutory Compliance' && $formName === 'Statutory Components')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/tax-and-statutory-compliance/statutory-components').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Tax and Statutory Compliance' && $formName === 'Provident Fund')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/tax-and-statutory-compliance/provident-fund').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'My Team' && $formName === 'Team Summary')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/my-team/team-summary').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'My Team' && $formName === 'Time Off')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/my-team/time-off').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Core HR' && $formName === 'Org Structure')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/core-hr/org-structure').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a> 
                                                </li>';
											}
											else if($menuName === 'Payroll' && $formName === 'Electronic Fund Transfer')
                                            {
												if(strtolower($payrollCountry) == 'th')
												{
													$formGeneratorURL = '/v3/payroll/electronic-fund-transfer?payroll_country='.strtolower($payrollCountry);
												}
												else
												{
													$formGeneratorURL = '/v3/payroll/electronic-fund-transfer';
												}
												
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl($formGeneratorURL).'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a> 
                                                </li>';
											}
											else if($menuName === 'Payroll' && $formName === 'Payroll Reconciliation')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/payroll/payroll-reconciliation').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a> 
                                                </li>';
											}
											else if($menuName === 'Data Loss Prevention' && $formName === 'Location Intelligence')
                                            {
                                                $iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
                                                    <a href="'. $this->baseUrl('/v3/data-loss-prevention/location-intelligence').'">
                                                        <i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
                                                        '.$presentationFormName.'
                                                    </a>
                                                </li>';
                                            }
											else if($menuName === 'Data Loss Prevention' && $formName === 'Key Logger')
                                            {
                                                $iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
                                                    <a href="'. $this->baseUrl('/v3/data-loss-prevention/key-logger').'">
                                                        <i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
                                                        '.$presentationFormName.'
                                                    </a>
                                                </li>';
                                            }
											else if($menuName === 'Employees' && $formName === 'Timesheet Hours')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/employees/timesheets').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'My Team' && $formName === 'LOP Recovery')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/my-team/lop-recovery').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'My Team'  && $formName === 'Timesheets')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/my-team/timesheets').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Employee Self Service' && $formName === 'Timesheets')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/employee-self-service/timesheets').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else if($menuName === 'Tax and Statutory Compliance' && $formName === 'NPS')
                                            {
												$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
                                                echo '<li class="'.$activeFormCls.'">
													<a href="'. $this->baseUrl('/v3/tax-and-statutory-compliance/nps').'">
														<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
														'.$presentationFormName.'
													</a>
                                                </li>';
											}
											else
											{
												$reportsubmenu = trim($formName);
												
												$frmAccess = $dbModules->formAccessRights($empId, $actualFormId);
												
												if ($frmAccess > 0)
												{
													$reportUrl  = $_SERVER['REQUEST_URI'];
													$formIcon  = '';
													$explodeUrl = explode("/",$reportUrl);
													
													for ($y=0; $y<count($explodeUrl); $y++) 
													{
														if ($explodeUrl[$y] == 'reports') 
														{
														$formIcon = $explodeUrl[$y+1];
														}
													}
													$activeFormCls = (str_replace(' ', '-', strtolower(trim($formName))) == $controllerName) ? 'active' : '';
													
														if((strtolower($menuName)) == $moduleName)
															$countModuleTitle=$countModuleTitle+1;
													
													$iconsCls = str_replace(' ', '-', strtolower($menuName)) . '-'. str_replace(' ', '-', strtolower($formName));
													
													if(array_key_exists('Enable',$formsName))
													{
														if($formsName['Enable'] == 1)
														{
															$formsNameNew = $formsName['New_Form_Name'] ;                                                   
														}
													}
													else
													{
														$formsNameNew = $formsName['Form_Name'];
													}
													
													
													
													if( $formName != 'Absentees Report' &&  $formName != 'Monthly Shortage Report' &&  $formName != 'Attendance Shortage Report')
													{
														if($formName == 'Department Hierarchy')
														{
															echo '<li class="'.$activeFormCls.'">
																	<a href="'. $this->baseUrl(str_replace(' ', '-', strtolower($menuName)) . '/'. str_replace(' ', '-', strtolower('Departments'))) . '" id='.$iconsCls.'>
																		<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
																		'.$formsNameNew.'
																		</a>
																</li>';
														}
														else
														{
															if ($formName == 'Compliance Forms' && (strtolower($payrollCountry) == 'ph' || strtolower($payrollCountry) == 'th' || strtolower($payrollCountry) == 'id')) {
																$formGeneratorURL = 'v3/form-generator?payroll_country='.strtolower($payrollCountry);
																echo '<li class="'.$activeFormCls.'">
																	<a href="'. $this->baseUrl($formGeneratorURL) . '" id='.$iconsCls.'>
																		<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
																		'.$formsNameNew.'
																		</a>
																</li>';
															} else {
															$formattedFormName = str_replace(' ', '-', strtolower($formName));
															if (in_array($formattedFormName, $payrollFormsUnderTaxStatutoryModule)) {
																$redirectionTaxModule = "payroll";
															} else if (in_array($formattedFormName, $formsManagerFormsUnderTaxStatutoryModule)) {
																$redirectionTaxModule = "forms-manager";
															} else {
																$redirectionTaxModule = "tax-and-statutory-compliance";
															}
														  	$menuModuleName = $menuName == 'Tax and Statutory Compliance' ? $redirectionTaxModule : $menuName;
															echo '<li class="'.$activeFormCls.'">
																	<a href="'. $this->baseUrl(str_replace(' ', '-', strtolower($menuModuleName)) . '/'. str_replace(' ', '-', strtolower($formName))) . '" id='.$iconsCls.'>
																		<i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
																		'.$formsNameNew.'
																		</a>
																</li>';
															}
														}
													}
													
												}
											}
										}
									}
									echo '</ul></li>';
								}
							}
						}
						?>
					</ul>
				</div>
				<!-- <div class="charts-sidebar progress-chart">
					<div class="sidebar-charts-inner">
						<div class="hrapp-sidebar-active-progress">
							<div class="clearfix">
								<div class="sidebar-chart-title">Active Employees</div>
								
							</div>
							<div class="progress">
								<?php
									echo '<div aria-valuenow="'.$activePercentage.'" style="width: '.$activePercentage.'%;" class="progress-bar progress-bar-secondary stat3" data-transitiongoal="'.$activePercentage.'"></div>';
								?>
							</div>
							<div class="sidebar-chart-number" id="number-visits">
								<?php
									echo $employeeTypeCnt['ActiveCnt'] .' / '. $totalEmployees;
								?>
							</div>
						</div>
					</div>
				</div> -->
				<?php
				if($isAdmin =='admin')
				{ ?>
					<div style="" class="sidebar-widgets">
					
					</div>
				
				
				<?php
				}
                
				if($isProduction == 0)
                    $homeref = "/hrapp";
                else
                    $homeref = "/";
                ?>
				
				<div style="" class="sidebar-footer clearfix">
					
				</div>
			</div>
				<!-- END SIDEBAR FOOTER -->
			</div>
		</div>
		<!-- END SIDEBAR -->

    
      <?php }
            $sty = 'background:#F5F6FA'; /**default - background color  */
      ?>		
		
		<div id="wholepage" class="main-content <?php echo $controllerName == 'index' ? '' : 'adjustPadding'; ?>" style="<?php echo $sty; ?>">
			<!-- BEGIN TOPBAR -->
			<div class="topbar">
				<div class="header-left">
					<div class="topnav">
						<a class="menutoggle hide-show-sidebar-collapse" href="javascript:void(0);" data-toggle="sidebar-collapsed">
							<span class="menu__handle"><span>Menu</span></span>
						</a>
						<div class="row hrapp-organisation">
							<div class="hidden-sm hidden-xs hrapp-org-name">
								<span title="<?php echo $orgName ?>" class="hrapp-org-name-text"><?php echo (strlen($orgName) > 20) ? substr($orgName,0,20).'...' : $orgName; ?></span>
							</div>
							<div class="hrapp-org-search">
								<form action="javascript:void(0);" method="post" class="searchform hrapp-searchform" id="search-results">
									
										<label for="hrapp-submodule-search"><i class="fa fa-search hrapp-menu-search-icon"></i></label>

										<input id="hrapp-submodule-search" aria-label="hrapp-submodule-search" class="form-control hrapp-menu-search-input" name="keyword" placeholder="Search" type="text" autocomplete="off">
									
								</form>
							</div>
						</div>
					</div>
				</div>
				
				<!-- header-right -->
				<div class="header-right">
					<ul class="header-menu nav navbar-nav">
						<?php
							// if the layout should not be restricted for presenting the department selection field
							if (empty($layoutRestrictedFor)) {
						    		if($departmentClassificaiton==1)
									{
									  if($controllerName == 'assignments'|| $controllerName == 'attendance'|| $controllerName == 'awards'|| $controllerName == 'compensatory-off'||
									     $controllerName =='employee-bank-account'|| $controllerName == 'employee-travel'|| $controllerName == 'employees'||
										 $controllerName =='employees-document-upload'|| $controllerName =='memos'|| $controllerName == 'performance-evaluation'||
										 $controllerName == 'resignation'|| $controllerName == 'short-time-off'|| $controllerName == 'skillset-assessment'||
										 $controllerName == 'timesheets'|| $controllerName == 'transfer'|| $controllerName == 'warnings'|| $controllerName == 'leaves'||
										 $controllerName == 'advance-salary'|| $controllerName == 'allowances'|| $controllerName == 'bonus'|| $controllerName == 'commission'|| $controllerName == 'deductions'|| 
										 $controllerName == 'etf'|| $controllerName == 'final-settlement'|| $controllerName == 'fixed-health-insurance'|| $controllerName == 'flexi-benefit-declaration'||
										 $controllerName == 'gratuity'|| $controllerName == 'gratuity-nomination'|| $controllerName == 'insurance'||  $controllerName == 'loan'||
										 $controllerName == 'perquisite-tracker'|| $controllerName == 'provident-fund'|| $controllerName == 'reimbursement'|| $controllerName == 'shift-allowance'||
										 $controllerName == 'tax-declarations'|| $controllerName == 'salary' || $controllerName == 'salary-payslip'||$controllerName == 'tds-history'|| 
										 $controllerName == 'document-generator'|| $controllerName == 'complaints'|| $controllerName== 'compliance-forms')
									  {
											// department hierarchy could be presented for whom they are admin for landed page
											$employeeAccessRights = $dbAccessRights->employeeAccessRights($empId,$accessFormName);
											if($employeeAccessRights['Admin']==='admin')
											{
											  $deptHierarchy   = $department->getEmployeeDepartmentType($empId); ?>
											  <li class="m-t-5 m-r-10 f-left hidden-xs hidden-sm divisionPanel">
												   <div style="width:250px;">
													   <select class="form-control" id="formDivisionDetails" data-search=true >
															   <?php

																$deptHierarchyCount=$department->getDepHierarchyCount();
																$deptLevelCount=$department->getDeptLevelCount($empId);
																if(empty($deptHierarchy))
																 { ?>
																 		<script type="text/javascript">
																  			$(document).ready(function(){
																		jAlert({ msg : 'Please navigate to employee form and update the department level access for this employee', type : 'info'});
																 		 });
																		</script>
																<?php } 
																else
																{
																	if($deptHierarchyCount == $deptLevelCount)
																	{
																	   echo '<option value="0">All</option>';
																	}
																}
																foreach ($deptHierarchy as $key => $row) 
																{
																	if($row['Department_Id']==$_COOKIE['HrappDepartmentClassificationId'])
																	{
																		echo '<option value="'. $row['Department_Id'] .'" selected="selected">'. $row['Department_Name'] .'</option>';
																	}
																	else
																	{
																		/*when the cookie is not there we need to set the first value as default value*/
																		if($deptHierarchyCount != $deptLevelCount)
																		{
																			setcookie('HrappDepartmentClassificationId',$row['Department_Id'],time() +(8640000 * 30),'/', '', true);
																		}
																		
																		echo '<option value="'. $row['Department_Id'] .'">'. $row['Department_Name'] .'</option>';
																	}
																	foreach($row['Child'] as $val =>$name)
																	{
																		if($name['Parent_Type_Id'] == $row['Department_Id'])
																		{
																			if($name['Department_Id']==$_COOKIE['HrappDepartmentClassificationId'])
																			{
																				echo '<option value="'. $name['Department_Id'] .'" selected="selected">&nbsp;&nbsp;'. $name['Department_Name'] .'</option>';
																			}
																			else
																			{
																				/*when the cookie is not there we need to set the first value as default value*/
																				if($deptHierarchyCount != $deptLevelCount)
																				{
																					setcookie('HrappDepartmentClassificationId',$name['Department_Id'],time() +(8640000 * 30),'/','', true);
																				}
																				echo '<option value="'. $name['Department_Id'] .'">&nbsp;&nbsp;'. $name['Department_Name'] .'</option>';
																			}
																			
																			foreach($row['Child'] as $v =>$k)
																			{
																				if($k['Parent_Type_Id'] == $name['Department_Id'])
																				{
																					if($k['Department_Id']==$_COOKIE['HrappDepartmentClassificationId'])
																					{
																						echo '<option value="'. $k['Department_Id'] .'" selected="selected">&nbsp;&nbsp;&nbsp;&nbsp;'. $k['Department_Name'] .'</option>';
																					}
																					else
																					{
																						/*when the cookie is not there we need to set the first value as default value*/
																						if($deptHierarchyCount != $deptLevelCount)
																						{
																							setcookie('HrappDepartmentClassificationId',$k['Department_Id'],time() +(8640000 * 30),'/','', true);
																						}
																						echo '<option value="'. $k['Department_Id'] .'">&nbsp;&nbsp;&nbsp;&nbsp;'. $k['Department_Name'] .'</option>';
																					}
																				}
																			}
																		}
																	}    
																}
															   
															   ?>
													   </select>
												   </div>
											   </li>
											
									   <?php }
									  }
									 elseif($moduleName == 'reports' && $isAdmin =='admin')
									 {
											 $reportUrl  = $_SERVER['REQUEST_URI'];
											 $explodeUrl = array();
											 $linkValue  = '';
											 $explodeUrl = explode("/",$reportUrl);
											 for ($y=0; $y<count($explodeUrl); $y++) 
											 {
												 if ($explodeUrl[$y] == 'linkValue') 
												 {
												 	$linkValue = $explodeUrl[$y+1];
												 }
											 }
											if($linkValue=='absentees'||$linkValue=='attendance'||$linkValue == 'attendance-import'||$linkValue =='attendance-shortage'||
												$linkValue == 'attendance-without-grace-period'|| $linkValue =='holiday-attendance'||$linkValue == 'monthly-shortage'||
												$linkValue == 'overtime'||$linkValue == 'weekoff-attendance'||$linkValue == 'assignments'||$linkValue == 'resignation'||
												$linkValue == 'skill-assessment'|| $linkValue == 'transfer'|| $linkValue =='dependents'||$linkValue =='employee-education'||
												$linkValue =='new-joinees'||$linkValue =='probation'||$linkValue =='skills'||$linkValue == 'compensatory-off'||
												$linkValue =='compensatory-off-balance'||$linkValue =='leave-history'|| $linkValue =='leaves'|| $linkValue =='monthly-leave-balance'||
												$linkValue == 'short-time-off'||
												$linkValue == 'hourly-master-report'||$linkValue =='hourly-wage-payslip' ||$linkValue =='monthly-salary-payslip'||$linkValue =='reimbursement-allowances'||
												$linkValue =='monthly-master-report'||$linkValue == 'ecr-hourly'|| $linkValue == 'ecr-monthly' || $linkValue =='provident-fund'||
												$linkValue =='uan-based-ecr'|| 	$linkValue =='monthly-salary'||$linkValue == 'salary-increment'|| $linkValue =='pf-payment-tracker' || $linkValue == 'provident-fund'||
												$linkValue == 'deductions' || $linkValue =='deferred-loan' || $linkValue =='group-deduction'||
											    $linkValue == 'adhoc-allowance' || $linkValue == 'advance-salary' ||  $linkValue == 'bonus' || $linkValue == 'commission' || $linkValue == 'reimbursement' ||
											    $linkValue == 'shift-allowance' || $linkValue == 'reimbursement-bank-statement'|| $linkValue=='work-sheet')
											//	$linkValue =='esic-hourly' || $linkValue =='esic-monthly' || $linkValue == 'fixed-insurance' || $linkValue == 'variable-insurance' 
											//    || $linkValue =='insurance-payment-tracker'|| $linkValue =='Eft Monthly'||$linkValue =='tds'|| $controllerName == 'timeline'||  $linkValue == 'professional-tax-hourly' || $linkValue == 'professional-tax-monthly' ||
												//$linkValue =='loan' || $linkValue =='loan-amortization' || $linkValue == 'loan-balance' ||$linkValue == 'allowances' ||  $linkValue == 'license-expiry' ||
						
						     				
											    {
												 $deptHierarchy   = $department->getEmployeeDepartmentType($empId);?>

												 <div class="m-t-5 m-r-10 f-left hidden-xs hidden-sm divisionPanel">
													  <div style="width:250px;">
														  <select class="form-control" id="formDivisionDetails" data-search=true >
																 <?php
																	$deptHierarchyCount=$department->getDepHierarchyCount();
																 	$deptLevelCount=$department->getDeptLevelCount($empId);
																	 
																	if(empty($deptHierarchy))
																	{ ?>
																			<script type="text/javascript">
																				 $(document).ready(function(){
																		   jAlert({ msg : 'Please navigate to employee form and update the department level access for this employee', type : 'info'});
																			 });
																		   </script>
																   <?php } 
																   else
																   {
																	   if($deptHierarchyCount == $deptLevelCount)
																	   {
																		  echo '<option value="0">All</option>';
																	   }
																   }
																     
																	 foreach ($deptHierarchy as $key => $row)
																	 {
																		 if($row['Department_Id']==$_COOKIE['HrappDepartmentClassificationId'])
																		 {
																			 echo '<option value="'. $row['Department_Id'] .'" selected="selected">'. $row['Department_Name'] .'</option>';
																		 }
																		 else
																		 {
																			 /*when the cookie is not there we need to set the first value as default value*/
																			if($deptHierarchyCount != $deptLevelCount)
																			{
																				setcookie('HrappDepartmentClassificationId',$row['Department_Id'],time() +(8640000 * 30),'/','', true);
																			}
																			 echo '<option value="'. $row['Department_Id'] .'">'. $row['Department_Name'] .'</option>';
																		 }
																		 foreach($row['Child'] as $val =>$name)
																		 {
																			 if($name['Parent_Type_Id'] == $row['Department_Id'])
																			 {
																					 if($name['Department_Id']==$_COOKIE['HrappDepartmentClassificationId'])
																					 {
																						 echo '<option value="'. $name['Department_Id'] .'" selected="selected">&nbsp;&nbsp;'. $name['Department_Name'] .'</option>';
																					 }
																					 else
																					 {
																						 /*when the cookie is not there we need to set the first value as default value*/
																						if($deptHierarchyCount != $deptLevelCount)
																						{
																							setcookie('HrappDepartmentClassificationId',$name['Department_Id'],time() +(8640000 * 30),'/','', true);
																						}
																						 echo '<option value="'. $name['Department_Id'] .'">&nbsp;&nbsp;'. $name['Department_Name'] .'</option>';
																					 }
																				 
																				 foreach($row['Child'] as $v =>$k)
																				 {
																					 if($k['Parent_Type_Id'] == $name['Department_Id'])
																					 {
																						 if($k['Department_Id']==$_COOKIE['HrappDepartmentClassificationId'])
																						 {
																							 echo '<option value="'. $k['Department_Id'] .'" selected="selected">&nbsp;&nbsp;&nbsp;&nbsp;'. $k['Department_Name'] .'</option>';
																						 }
																						 else
																						 {
																							 /*when the cookie is not there we need to set the first value as default value*/
																							if($deptHierarchyCount != $deptLevelCount)
																							{
																								setcookie('HrappDepartmentClassificationId',$k['Department_Id'],time() +(8640000 * 30),'/','', true);
																							}
																							 echo '<option value="'. $k['Department_Id'] .'">&nbsp;&nbsp;&nbsp;&nbsp;'. $k['Department_Name'] .'</option>';
																						 }
																					 }
																				 }
																			 }
																		 }    
																	 }
																  
																 ?>
														  </select>
													  </div>
												  </div>
										   
									  <?php  }
									 }									 
								 
									}
									else
									{
										setcookie('HrappDepartmentClassificationId',0,time() +(8640000 * 30),'/','', true);
									}
							}
						?>
						<?php
						/** for presenting the Data setup dashboard form,
						 * if the login employee is admin and having access
						 * and layout should not be restricted
						*/
						if($dataSetupAccessRights['Admin'] == 'admin' && empty($layoutRestrictedFor))
						{
							/** If the Enable Datasetup Prereq flag is enabled in the organization settings form
							 * and all the mandatory prerequisite data are not filled, the DataSetup dashboard will be blinked 
							 * and highlighted in red color. */
							
							if((int)$dataSetup === 0) {
								//when all the mandatory prerequisite data are not filled, the icon will be blinking in red color.
								$dataSty = "-webkit-animation: blink .75s linear infinite;color: red;font-weight: bold;";
							}
							else
							{
								/** If the Enable Datasetup Prereq flag is not enabled in the organization settings form */
								$dataSty = '';
							}?>					
							<!-- BEGIN DataSetup Dashboard -->
							<li class="hidden-xs hidden-sm">
								<span class="fa-stack fa-lg" style="top:7px; width:100%;">
									<a style="color: #2E2727; font-size: 15px;" href="<?php echo $this->baseUrl('/datasetup-dashboard/index');?>">
										<i title="Data Setup Dashboard" class="hr-organization-organization-settings hrapp-datasetup-icon" style="<?php echo $dataSty; ?>"></i>
										<span class="hidden-xs hidden-sm" id="dataSetupDashboard"></span>
									</a>
								</span>
							</li>
							<!-- CAMU INTEGRATION BUTTON -->
							<?php if(isset($_COOKIE['partnerid']) && $_COOKIE['partnerid'] !== '-' && $_COOKIE['partnerid'] !== 'trulead' && $camuUrl && $orgCode !== 'camuhr') { ?>
							<li style="margin-top:8px">
								<span class="fa-lg" style="top:7px; width:80% line-height: 1em;">
									<a style="color: ; font-size: 10px;" href="<?php echo $camuUrl;?>">
										<button class="ma-2 white--text font-weight-bold v-btn v-btn--is-elevated v-btn--has-bg theme--light v-size--small" style="
										  -webkit-border-radius: 4;
											-moz-border-radius: 4;
											border-radius: 4px;
											color: #ffffff;
											font-size: 12px;
											background: #1D98D4;
											padding: 4px 8px 4px 8px;
											text-decoration: none;
											border-color: #1D98D4;
										"><strong style="font-weight: 900;"><?php echo htmlspecialchars($_COOKIE['partnerid'], ENT_QUOTES, 'UTF-8'); ?></strong></button>
										<span class="hidden-xs hidden-sm" id="dataSetupDashboard"></span>
									</a>
								</span>
							</li>
							<?php } ?>
							<!-- END DataSetup Dashboard -->
						<?php } ?>
						
                        <!-- BEGIN NOTIFICATION DROPDOWN -->
						<li class="<?php echo (empty($layoutRestrictedFor)) ? 'dropdown' : 'hidden'; ?>" id="notifications-header">
							<a href="javascript:void(0);" class="notificationpanel"  data-toggle="dropdown" data-hover="dropdown" data-close-others="true">
								<i class="icon-bell"></i>
								<span class="badge badge-danger badge-header" style="background-color: var(--primary-color);" id="notificationCount"><?php echo $notificationsCnt; ?></span>
							</a>
							<ul class="dropdown-menu">
								<li class="dropdown-header clearfix">
									<p class="pull-left"><span id="notificationSubCount"><?php echo $notificationsCnt; ?></span> Pending Notifications</p>
								</li>
								<li>
									<ul style="height: 220px;" class="dropdown-menu-list withScroll mCustomScrollbar _mCS_1" data-height="220">
										<div class="mCustomScrollBox mCS-light" id="mCSB_1" style="position:relative; height:100%; overflow:hidden; max-width:100%;">
											<div class="mCSB_container aabbcc mCS_no_scrollbar" style="position:relative; top:0;">
												<?php
												foreach ($notifications as $key => $row)
												{
													/*
													fa-star
													fa-heart
													fa-file-text
													fa-picture-o
													fa-bell
													fa-comment
													*/
													
													$keyNameArr = array_keys($row);
													$keyName = $keyNameArr[0];
													$alertCnt = $row[$keyName];
													if ($alertCnt > 0 && ($keyName != 'Leave' || $leaveSettings['Enable_Workflow'] === 'No'))
													{
														switch ($keyName)
														{
															case 'Timesheet':
																echo '<a href='.$this->baseUrl('v3/approvals/approval-management?form_id=268').'><li>'.
																		'<i class="hr-employees-timesheets p-r-10 f-18 c-orange"></i>'.
																		$alertCnt . ' Timesheet request'.(($alertCnt > 1) ? 's' : ''). ' waiting for your approval.'.
																	  '</li></a>';
																break;
															
															case 'Attendance':
																echo '<a href='.$this->baseUrl('v3/my-team/attendance/attendance-finalization?tab=attendance').'><li>'.
																		'<i class="hr-employees-attendance p-r-10 f-18 c-orange"></i>'.
																		$alertCnt . ' Attendance request'.(($alertCnt > 1) ? 's' : ''). ' waiting for your approval.'.
																	  '</li></a>';
																break;
															
															case 'Leave':
																echo '<a href='.$this->baseUrl('v3/approvals/approval-management?form_id=31').'><li>'.
																		'<i class="hr-employees-leaves p-r-10 f-18 c-orange"></i>'.
																		$alertCnt . ' Leave request'.(($alertCnt > 1) ? 's' : ''). ' waiting for your approval.'.
																	  '</li></a>';
																break;

															case 'CompensatoryOff':
																echo '<a href='.$this->baseUrl('v3/approvals/approval-management?form_id=334').'><li>'.
																		'<i class="hr-employees-compensatory-off p-r-10 f-18 c-orange"></i>'.
																		$alertCnt . ' Compensatory off request'.(($alertCnt > 1) ? 's' : ''). ' waiting for your approval.'.
																		'</li></a>';
																break;	
															
															case 'Short Time off':
																echo '<a href='.$this->baseUrl('v3/approvals/approval-management?form_id=352').'><li>'.
																		'<i class="hr-employees-short-time-off p-r-10 f-18 c-orange"></i>'.
																		$alertCnt . ' Short time off request'.(($alertCnt > 1) ? 's' : ''). ' waiting for your approval.'.
																	  '</li></a>';
																break;
															
															case 'Travel':
																echo '<a href='.$this->baseUrl('employees/employee-travel').'><li>'.
																		'<i class="hr-employees-employee-travel p-r-10 f-18 c-orange"></i>'.
																		$alertCnt . ' Travel request'.(($alertCnt > 1) ? 's' : ''). ' waiting for your approval.'.
																	  '</li></a>';
																break;
															
															case 'Transfer':
																echo '<a href='.$this->baseUrl('employees/transfer').'><li>'.
																		'<i class="hr-employees-transfer p-r-10 f-18 c-orange"></i>'.
																		$alertCnt . ' Transfer request'.(($alertCnt > 1) ? 's' : ''). ' waiting for your approval.'.
																	  '</li></a>';
																break;
															
															case 'Resignation':
																echo '<a href='.$this->baseUrl('v3/approvals/approval-management?form_id=34').'><li>'.
																		'<i class="hr-employees-resignation p-r-10 f-18 c-orange"></i>'.
																		$alertCnt . ' Resignation request'.(($alertCnt > 1) ? 's' : ''). ' waiting for your approval.'.
																	  '</li></a>';
																break;
															
															case 'Recruitment':
																echo '<a href='.$this->baseUrl('v3/recruitment/job-candidates').'><li>'.
																		'<i class="hr-recruitment-job-candidates p-r-10 f-18 c-orange"></i>'.
																		$alertCnt . ' Recruitment request'.(($alertCnt > 1) ? 's' : ''). ' waiting for your approval.'.
																	  '</li></a>';
																break;
															
															case 'Bonus':
																echo '<a href='.$this->baseUrl('payroll/bonus').'><li>'.
																		'<i class="hr-payroll-bonus p-r-10 f-18 c-orange"></i>'.
																		$alertCnt . ' Bonus request'.(($alertCnt > 1) ? 's' : ''). ' waiting for your approval.'.
																	  '</li></a>';
																break;
															
															case 'Commission':
																echo '<a href='.$this->baseUrl('payroll/commission').'><li>'.
																		'<i class="hr-payroll-commission p-r-10 f-18 c-orange"></i>'.
																		$alertCnt . ' Commission request'.(($alertCnt > 1) ? 's' : ''). ' waiting for your approval.'.
																	  '</li></a>';
																break;
															
															case 'Deduction':
																echo '<a href='.$this->baseUrl('payroll/deductions').'><li>'.
																		'<i class="hr-payroll-deductions p-r-10 f-18 c-orange"></i>'.
																		$alertCnt . ' Deduction request'.(($alertCnt > 1) ? 's' : ''). ' waiting for your approval.'.
																	  '</li></a>';
																break;
															
															
															case 'Reimbursement':
																echo '<a href="'.$this->baseUrl('payroll/reimbursement?data='.$encodedParams).'"><li>'.
																		'<i class="hr-payroll-reimbursement p-r-10 f-18 c-orange"></i>'.
																		$alertCnt . ' Reimbursement request'.(($alertCnt > 1) ? 's' : ''). ' waiting for your approval.'.
																	  '</li></a>';
																break;
															
															case 'Advance Salary':
																echo '<a href='.$this->baseUrl('payroll/advance-salary').'><li>'.
																		'<i class="hr-payroll-advance-salary p-r-10 f-18 c-orange"></i>'.
																		$alertCnt . ' Advance salary request'.(($alertCnt > 1) ? 's' : ''). ' waiting for your approval.'.
																	  '</li></a>';
																break;
															
															case 'Loan':
																echo '<a href='.$this->baseUrl('payroll/loan').'><li>'.
																		'<i class="hr-payroll-loan p-r-10 f-18 c-orange"></i>'.
																		$alertCnt . ' Loan request'.(($alertCnt > 1) ? 's' : ''). ' waiting for your approval.'.
																	  '</li></a>';
																break;
															
															case 'Deferred Loan':
																echo '<a href='.$this->baseUrl('payroll/loan').'><li>'.
																		'<i class="hr-payroll-loan p-r-10 f-18 c-orange"></i>'.
																		$alertCnt . ' Deferred loan request'.(($alertCnt > 1) ? 's' : ''). ' waiting for your approval.'.
																	  '</li></a>';
																break;
															
															case 'Shift Allowance':
																echo '<a href='.$this->baseUrl('payroll/shift-allowance').'><li>'.
																		'<i class="hr-payroll-shift-allowance p-r-10 f-18 c-orange"></i>'.
																		$alertCnt . ' Shift Allowance request'.(($alertCnt > 1) ? 's' : ''). ' waiting for your approval.'.
																	  '</li></a>';
																break;
															
															case 'Tax Declaration':
																echo '<a href="'.$this->baseUrl('payroll/tax-declarations?data='.$encodedParams).'"><li>'.
																		'<i class="hr-tax-and-statutory-compliance-tax-declarations p-r-10 f-18 c-orange"></i>'.
																		$alertCnt . ' Tax declaration request'.(($alertCnt > 1) ? 's' : ''). ' waiting for your approval.'.
																	  '</li></a>';
																break;
															
															case 'Tax Declaration Upload':
																echo '<a href="'.$this->baseUrl('payroll/tax-declarations?data='.$encodedParams).'"><li>'.
																		'<i class="hr-tax-and-statutory-compliance-tax-declarations p-r-10 f-18 c-orange"></i>'.
																		$alertCnt . ' Tax declaration request'.(($alertCnt > 1) ? 's' : ''). ' waiting for document upload.'.
																	  '</li></a>';
																break;
															
															case 'FinancialClosure':
																echo '<a href='.$this->baseUrl('payroll/tax-rules').'><li>'.
																		'<i class="hr-tax-and-statutory-compliance-tax-rules p-r-10 f-18 c-orange"></i>'.
																		'Execute the financial closure process.'.
																	  '</li></a>';
																break;
															
															case 'Labour Welfare Fund':
																echo '<a href='.$this->baseUrl('payroll/labour-welfare-fund').'><li>'.
																	'<i class="hr-payroll-shift-allowance p-r-10 f-18 c-orange"></i>'.
																	$alertCnt . ' Labour Welfare Fund request'.(($alertCnt > 1) ? 's.' : '.'). ' waiting for your approval.'.
																	  '</li></a>';
																break;
															
															case 'Section24 Declaration Document Upload':
																echo '<a href='.$this->baseUrl('payroll/income-under-section24').'><li>'.
																		'<i class="hr-tax-and-statutory-compliance-tax-declarations p-r-10 f-18 c-orange"></i>'.
																		$alertCnt . ' Income Under Section24 request'.(($alertCnt > 1) ? 's' : ''). ' waiting for document upload.'.
																	  '</li></a>';
																break;

															case 'Section24 Declaration Approval':
																echo '<a href='.$this->baseUrl('payroll/proof-of-investment').'><li>'.
																		'<i class="hr-tax-and-statutory-compliance-tax-declarations p-r-10 f-18 c-orange"></i>'.
																		$alertCnt . ' Income Under Section24 request'.(($alertCnt > 1) ? 's' : ''). ' waiting for your approval.'.
																	  '</li></a>';
																break;
														}
													}
												}
												if($leaveSettings['Enable_Workflow'] === 'Yes') {
													echo '<a id="leaveNotification" href='.$this->baseUrl('v3/approvals/approval-management?form_id=31').'><li style="display: flex">'.
															'<i class="hr-employees-leaves p-r-10 f-18 c-orange"></i>'.
															'<div id="leaveNotificationCount" style="margin-top: -5px;"															></div>'.
															'</li></a>';
												}
												?>
											</div>
											<div class="mCSB_scrollTools" style="position: absolute; display: none; opacity: 0;">
												<div class="mCSB_draggerContainer">
													<div class="mCSB_dragger" style="position: absolute; top: 0px; height: 141px;" oncontextmenu="return false;">
														<div class="mCSB_dragger_bar" style="position: relative; line-height: 141px;"></div>
													</div>
													<div class="mCSB_draggerRail"></div>
												</div>
											</div>
										</div>
									</ul>
								</li>
								
							</ul>
						</li>
						<!-- END NOTIFICATION DROPDOWN -->
						
						<!-- BEGIN NEWSFEED DROPDOWN -->
						<li class="<?php echo (empty($layoutRestrictedFor)) ? 'dropdown' : 'hidden'; ?>" id="messages-header">
							<a href="javascript:void(0);" class="notificationpanel" data-toggle="dropdown" data-hover="dropdown" data-close-others="true">
								<i class="fa fa-bullhorn"></i>
								<span class="badge badge-secondary badge-header"><?php echo $countAnnouncementDetails; ?></span>
							</a>
							<ul class="dropdown-menu">
								<li class="dropdown-header clearfix">
									<p class="pull-left"><?php echo $countAnnouncementDetails; ?> Announcement(s)</p>
								</li>
								<li>
									<ul style="height: 220px;" class="dropdown-menu-list withScroll mCustomScrollbar _mCS_1" data-height="220">
										<div class="mCustomScrollBox mCS-light" id="mCSB_1" style="position:relative; height:100%; overflow:hidden; max-width:100%;">
											<div class="mCSB_container aabbcc mCS_no_scrollbar" style="position:relative; top:0;">
												<?php			
														for($i=0;$i<$countAnnouncementDetails;$i++)
														{ ?>
															<a class="announcementElement" data-toggle="modal">
																<div class="hidden announcementSubElement" aTitle="<?php echo $announcementDetails[$i]['Title']; ?>">
																	<?php echo $announcementDetails[$i]['Announcement_Type'] === "Video" 
																	? $announcementDetails[$i]['Embed_Url'] : $announcementDetails[$i]['Announcement_Text']; ?>
																</div>
																<li><?php echo $announcementDetails[$i]['Title']; ?></li>
															</a>
												<?php	}
												?>
											</div>
											<div class="mCSB_scrollTools" style="position: absolute; display: none; opacity: 0;">
												<div class="mCSB_draggerContainer">
													<div class="mCSB_dragger" style="position: absolute; top: 0px; height: 141px;" oncontextmenu="return false;">
														<div class="mCSB_dragger_bar" style="position: relative; line-height: 141px;"></div>
													</div>
													<div class="mCSB_draggerRail"></div>
												</div>
											</div>
										</div>
									</ul>
								</li>
								
							</ul>
						</li>
	
						<!-- BEGIN USER DROPDOWN -->
						<li class="dropdown user-header" id="user-header">
							<a data-toggle="dropdown" data-hover="dropdown" data-close-others="true" href=<?php echo $this->baseUrl('v3/employee-self-service/my-profile'); ?>>
								<?php echo $profilePictureField; ?>
								<span class="username hidden-sm hidden-xs"><?php echo $employeeFirstname; ?></span>
							</a>
							
								<ul class="dropdown-menu">
									<?php if (empty($layoutRestrictedFor)) { ?>
									<li><a class="profile" href=<?php echo $this->baseUrl('v3/employee-self-service/my-profile'); ?>><i class="fa fa-user"></i><span>Profile</span></a></li>
									<?php } if($disableLogout === "No") { ?>
									<li><a class="logout"><i class="icon-logout"></i><span>Logout</span></a></li>
									<?php } ?>
								</ul>
						</li>
						<!-- END USER DROPDOWN -->
						<!-- CHAT BAR ICON -->
						<!--
						<li id="quickview-toggle"><a href="javascript:void(0);"><i class="icon-bubbles"></i></a></li>
						-->
					</ul>
					<div class="msgContentPanel"></div>
				</div>
				<!-- header-right -->
				
			</div>
			
			<!-- END TOPBAR -->
			
	<div id="announcementContentPopup" class="modal fade">
        <div class="modal-dialog">
            <div class="modal-content announcementWidth" style="border: 2px solid;border-color: var(--primary-color);">
                <div class="modal-header">
				    <img alt="announcement" style="width: 150px;height: auto;position: fixed;margin-top: -103px;margin-left: -12px;" src="<?php echo $this->baseUrl('images/announcement.png'); ?>" />
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true" style="color: #642138 !important;"><i class="icons-office-52" style="font-size:15px; !important"></i></button>
                    <h4 class="modal-title" style="text-align:center;font-weight:bold;color:var(--primary-color);" id="announcementPopupTitle"></h4>
                </div>
                <div id="announcementContent" style="padding: 20px;text-align: center;"></div>
            </div>
        </div>
    </div>

	<?php if (!empty($signOffAlertDetails) && $signOffAlertDetails["showSignOffAlert"] == "Yes") { ?>
		<div class="modal fade" id="signoffDueModal" aria-hidden="true" style="z-index: 2100 !important;">
			<div class="modal-dialog modal-lg">
				<div class="modal-content">
					<div class="modal-body">
						<div class="row" style="display: flex; justify-content: center;">
							<div class="col-xs-12 col-sm-12 col-md-4 hide-image-in-small-screens">
								<img width="100%" alt="warning-img-geo-accuracy" class="lazyload"
								src="<?php echo $this->baseUrl('images/payment-due-alert.gif'); ?>" />
							</div>
							<div class="col-xs-12 col-sm-12 col-md-8" id="payslipTemplateAddress">
								<div class="row" style="padding: 0px 20px;">
									<div class="class-xs-12 payment-reminder-header" style="font-size: 1.2em;text-align: start;">
										As a formal completion of implementation and training, we require your sign-off digitally. 
										We will continue to assist you with the payroll process for the next three months in shadow mode so 
										that the payroll team is comfortable with the product and the payroll processes. 
										We are looking forward to working with you. Thanks and Regards, HRAPP Implementation Team
									</div>
									<div class="col-xs-4 col-sm-3 payment-reminder-content" style="color: var(--primary-color);padding-left: 0px;margin-bottom: 20px;">
										Due Date
									</div>
									<div class="col-xs-1 payment-reminder-content">:</div>
									<div class="col-xs-4 payment-reminder-content" style="color: var(--primary-color)">
										<?php echo $signOffAlertDetails["signOffDueDate"]; ?>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-xs-12">
								<div class="<?php echo (!empty($signOffAlertDetails) &&!empty($signOffAlertDetails["spocSignOffDocumentLink"])) ? 'col-xs-12 col-sm-6' : 'hidden'; ?>" style="padding-left: 0px">
									<button class="btn btn-lg" style="border-radius: 15px !important;background: white;border: 2px solid var(--primary-color)">
										<a href="<?php echo $signOffAlertDetails["spocSignOffDocumentLink"]; ?>" rel="noopener noreferrer" target="_blank" style="text-transform: capitalize;">
											Implementation SPOC Signoff
										</a>
									</button>
								</div>
								<div class="<?php echo (!empty($signOffAlertDetails) &&!empty($signOffAlertDetails["coordinatorSignOffDocumentLink"])) ? 'col-xs-12 col-sm-6' : 'hidden'; ?>" style="padding-left: 0px;padding-bottom: 10px">
									<button class="btn btn-lg" style="border-radius: 15px !important;background: white;border: 2px solid var(--primary-color)">
										<a href="<?php echo $signOffAlertDetails["coordinatorSignOffDocumentLink"]; ?>" rel="noopener noreferrer" target="_blank" style="text-transform: capitalize;">
											Implementation Coordinator Signoff
										</a>
									</button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	<?php } ?>

	<div id="camuUrlWarningModal" class="modal fade">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
						<i class="icons-office-52" style="color: black"></i>
					</button>
                </div>
				<div class="modal-body" style="text-align: center;">
					<i class="fa fa-exclamation-triangle" style="font-size: 5em; color: #febf04;margin-bottom: 20px;"></i>
                    <h4 class="modal-title" style="text-align:center;font-weight:bold;color:var(--primary-color);margin: 0px 45px;">
						CAMU Integration is incomplete. Please review the API configuration
					</h4>
				</div>
				<div class="modal-footer text-center" style="margin: 15px 0px;">
					<button class="btn btn-secondary btn-embossed" id="camuUrlWarningModalCloseBtn" style="bottom: 5px;">
						Ok
					</button>
				</div>
            </div>
        </div>
    </div>

			<div class="modal fade" id="modalMenuFormChangePassword" aria-hidden="true">
				<div class="modal-dialog modal-lg">
					<div class="modal-content">
						<div class="modal-header bg-primary">
							<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true"><i class="mdi-hardware-keyboard-backspace"></i></button>
							
							<h4 class="modal-title">Change Password</h4>
						</div>
						<div class="modal-body">
							<form role="form" class="form-horizontal form-validation" id="editFormChangePassword" method="POST" action="">
								<input type="hidden" name="loginEmployeeId" id="loginEmpId" value="<?php echo $empId; ?>">
								<input type="hidden" name="Enable_Workflow" id="Enable_Workflow" value="<?php echo $leaveSettings['Enable_Workflow']; ?>">
								<input type="hidden" name="isServiceProviderAdmin" id="isServiceProviderAdmin" value="<?php echo $isServiceProviderAdmin; ?>">
								<input type="hidden" name="notificationsCnt" id="notificationsCnt" value="<?php echo $notificationsCnt; ?>">
								<div class="row">
									
									<div class="form-group">
										<label class="col-md-4 control-label">Username </label>
										<div class="col-md-8">
											<label class="control-label" id="formCPUserName" style="margin-top: 5px;"><?php echo $userSession['logUserName']; ?></label>
										</div>
									</div>
									
									<div class="form-group">
										<label class="col-md-4 control-label">Current Password <span class="short_explanation">*</span></label>
										<div class="col-md-8">
											<input type="password" class="form-control vRequired" minlength="1" maxlength="30" id="formCPOldPassword" name="Old_Password" placeholder="Old Password">
										</div>
									</div>
									
									<div class="form-group">
										<label class="col-md-4 control-label">New Password <span class="short_explanation">*</span></label>
										<div class="col-md-8">
											<input type="password" class="form-control" required=true minlength="1" maxlength="30" id="formCPNewPassword" name="New_Password" placeholder="New Password">
										</div>
									</div>
									
									<div class="form-group">
										<label class="col-md-4 control-label">Confirm New Password <span class="short_explanation">*</span></label>
										<div class="col-md-8">
											<input type="password" class="form-control" required=true minlength="1" maxlength="30" id="formCPConfirmPassword" name="Confirm_Password" placeholder="Confirm Password">
										</div>
									</div>
								
								</div>
							</form>
						</div>
						<div class="modal-footer text-center">
							
							<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetChangePassword" style="bottom: 5px;">
								<i class="mdi-action-restore"></i> Reset
							</button>
							<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitChangePassword" style="bottom: 5px;">
								<i class="mdi-content-send"></i> Submit
							</button>
							
						</div>
					</div>
				</div>
			</div>
   
			<?php		
                if((isset($_COOKIE['Appmailboxidentity'])) && $moduleName == 'mailbox')
                {
            ?>
            
            <!-- BEGIN PAGE CONTENT -->
            <div class="page-content page-app mailbox">
                <div class="row alertMsg" style="margin-top: 10px;">
                    <?php echo $this->layout()->content; ?>
                </div>
             
            </div>
            <!-- END PAGE CONTENT -->
            <?php
                }
                else
                {	                    
                    ?>
			<!-- BEGIN PAGE CONTENT -->
			<div class="page-content" style="<?php echo $sty; ?>">
				<div class="custom-page m-t-80 text-center">
					<input type="hidden" name="dataSetup" id="dataSetup" value="<?php echo $dataSetup;?>" />
                    <input type="hidden" name="dataSetupImport" id="dataSetupImport" value="<?php echo $dataImport;?>" />                    
					<input type="hidden" name="moduleId" id="moduleId" value="<?php echo $landedModule[0]["Module_Id"]; ?>"> 
					<input type="hidden" name="isAdmin" id="isAdmin" value="<?php echo $isAdmin; ?>"> 
					<input type="hidden" name="isAdminForLoadedForm" id="isAdminForLoadedForm" value="<?php echo $isAdminForLoadedForm; ?>">					
					<input type="hidden" name="isManager" id="isManager" value="<?php echo $isManager; ?>"> 
					<input type="hidden" name="landedFormAccess" id="landedFormAccess" value="<?php echo $isLandedFormHaveAccess; ?>"> 
					<input type="hidden" name="signOffAlertType" id="signOffAlertType" value="<?php echo $signOffAlertType; ?>"> 
					<input type="hidden" name="camuUrl" id="camuUrl" value="<?php echo $camuUrl; ?>"> 
             
					<!--For get Which device mode in js-->
					<div class="visible-xs" id="isExtraSmall"></div>
					<div class="visible-sm" id="isSmall"></div>
					<div class="visible-md"></div>
					<div class="visible-lg"></div>
					
					<!-- BEGIN SESSION EXPIRE MODAL -->
					<button type="button" id="sessionExpired" data-toggle="modal" data-target="#sessionExipredModel" style="display: none;" ></button>
					
					<div class="modal fade" id="sessionExipredModel" aria-hidden="true" style="z-index: 2100 !important;">
						<div class="modal-dialog">
							<div class="modal-content">
								<div class="modal-header">
									<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52"></i></button>
									<h4 class="modal-title"><strong>Session</strong> Expired..!</h4>
								</div>
								
								<div class="modal-body">
									Your Session has been expired. do you want redirect to Login Page ?<br>
								</div>
								
								<div class="modal-footer">
									<button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal">No</button>
									<button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="sessionExpiredConfirm">Yes</button>
								</div>
							</div>
						</div>
					</div>
					<!-- END SESSION EXPIRE MODAL -->
					
					<!-- BEGIN SUB MENU -->
						<?php
						
						// check the condition that the controller is tax-rules and show the submenu list at top
						if($controllerName == 'tax-rules') {
							echo '<div class="row hrsubmenu_img adjustMargin">';
						}
						else  {
							echo '<div class="row hrsubmenu_img adjustMargin hide-top-menus">';
						}
                        
						$moduleArr = Array('organization','employees', 'payroll', 'help', 'roster-management');
						
						if (in_array($moduleName, $moduleArr))
						{	
							$orgForm = $dbModules->formByModuleName($moduleName);
							
							$j=0;
							
							$halfModuleTitle = round($countModuleTitle/2);
							
							if ($countModuleTitle > 0)
							{
								echo '<ul class="hrappSubMenuList">';
								
								foreach ($orgForm as $suborgMenu)
								{
                                    if($countModuleTitle > 15 && $j == $halfModuleTitle)
									{
										echo '</ul>
											<ul>';
									}
									
									$subMenu = trim($suborgMenu['Form_Name']);
									$subMenuFormId = trim($suborgMenu['Form_Id']);
                                    
									$frmAccess = $dbModules->formAccessRights($empId, $subMenuFormId);
									if ($frmAccess > 0)
									{
										$j++;
										
										//if ($key == 'Reports' && $formName == 'Absentees Report')
										//{
										//	$iconsCls = 'absentees-report';
										//}
										//else
										//{
											$iconsCls = str_replace(' ', '-', strtolower($moduleName)) . '-'. str_replace(' ', '-', strtolower($subMenu));
										//}
										
										if( $controllerName == 'tax-rules')
										{
                                            $taxRulesSubMenuItems = $dbModules->getTaxSubForms();
											//$taxRulesSubMenuItems = array('Tax Slabs', 'Tax Sections', 'Section Investments', 'Tax Exemptions', 'Tax Rebates', 'Professional Tax', 'PT Payment Tracker','TDS Payment Tracker', 'Tax Section Allowance Mapping');
											$taxRulesSubMenuIcons = array('tax-slab', 'tax-section', 'section-investment', 'tax-exemptions', 'tax-rebates', 'professional-tax', 'payment-tracker', 'tds-payment-tracker', 'hr-tax-section-allowance-mapping');

											foreach($taxRulesSubMenuItems as $subKey => $subRow)
											{
                                                
												$taxRulesSubMenuAccessRights = $dbModules->formAccessRights($empId, $subRow['Form_Id']);
												if($taxRulesSubMenuAccessRights > 0)
												{
                                                    if(array_key_exists('Enable',$subRow))
                                                    {
                                                        if($subRow['Enable'] == 1)
                                                        {
                                                            $subRow['Form_Name'] = ($subRow['New_Form_Name'] !='' ? $subRow['New_Form_Name'] : $subRow['Form_Name']);
                                                            echo ' <li>
                                                                    <a class="anchorTag taxRulesSubMenu" id='.$taxRulesSubMenuIcons[$subKey].' title="'.$subRow['Form_Name'].'" style="cursor: pointer;">
                                                                        <i class="icon hr-'.$taxRulesSubMenuIcons[$subKey].' hrapp-icon-size"></i>
                                                                    </a> 
                                                                </li>';
                                                        }
                                                    }
                                                    else
                                                    {
                                                        echo ' <li>
                                                                <a class="anchorTag taxRulesSubMenu" id='.$taxRulesSubMenuIcons[$subKey].' title="'.$subRow['Form_Name'].'" style="cursor: pointer;">
                                                                    <i class="icon hr-'.$taxRulesSubMenuIcons[$subKey].' hrapp-icon-size"></i>
                                                                </a> 
                                                            </li>';
                                                    }
												}
											}
											break;
										}
										else
										{
											if(array_key_exists('Enable',$suborgMenu))
                                            {
                                                if($suborgMenu['Enable'] == 1)
                                                {
                                                    if ($controllerName == str_replace(' ', '-', strtolower($subMenu)))
                                                    {
                                                        echo '<li class="active-sub-menu">';
                                                    }
                                                    else
                                                    {
                                                        echo '<li>';
                                                    }
                                                    
                                                    $newTitle =  trim($suborgMenu['New_Form_Name']);
                                                    
                                                    if($newTitle == 'Support')
                                                    {
                                                        $href = $domainDetails['Support_Link'];
                                                        $icon = 'support-ticket';
                                                    }
                                                    else if($newTitle == 'Contact Us')
                                                    {
                                                        $href = "mailto:".$domainDetails['Support_Email'];
                                                        $icon = 'help-contact-us';
                                                    }
                                                    else if($newTitle == 'Department Hierarchy')
													{
														$href = $this->baseUrl(str_replace(' ', '-', strtolower($moduleName)) . '/'. str_replace(' ', '-', strtolower('Departments')));
                                                        $icon = $iconsCls;
													}
													else
                                                    {
                                                        $href = $this->baseUrl(str_replace(' ', '-', strtolower($moduleName)) . '/'. str_replace(' ', '-', strtolower($subMenu)));
                                                        $icon = $iconsCls;
                                                    } 
                                                 
                                                    echo '<a class="anchorTag" href="'. $href . '" title="'.$newTitle.'">
                                                            <i class="icon hr-'.$icon.' hrapp-icon-size"></i>
                                                          </a>';
                                                    echo '</li>';
                                                   
                                                }
                                            }
                                            else
                                            {
                                                if ($controllerName == str_replace(' ', '-', strtolower($subMenu)))
                                                {
                                                    echo '<li class="active-sub-menu">';
                                                }
                                                else
                                                {
                                                    echo '<li>';
                                                }                                                    
                                                
                                                $newTitle =  trim($suborgMenu['Form_Name']);
                                                
                                                if($newTitle == 'Support')
                                                {
                                                    $href = $domainDetails['Support_Link'];
                                                    $icon = 'support-ticket';
                                                }
                                                else if($newTitle == 'Contact Us')
                                                {
                                                    $href = "mailto:".$domainDetails['Support_Email'];
                                                    $icon = 'help-contact-us';
                                                }
												else if($newTitle == 'Department Hierarchy')
												{
													$href = $this->baseUrl(str_replace(' ', '-', strtolower($moduleName)) . '/'. str_replace(' ', '-', strtolower('Departments')));
													$icon = $iconsCls;
												}
												else
                                                {
                                                    $href = $this->baseUrl(str_replace(' ', '-', strtolower($moduleName)) . '/'. str_replace(' ', '-', strtolower($subMenu)));
                                                    $icon = $iconsCls;
                                                }
                                                
                                                echo '<a class="anchorTag" href="'. $href . '" title="'.$newTitle.'">
                                                        <i class="icon hr-'.$icon.' hrapp-icon-size"></i>
                                                      </a>';
                                                echo '</li>';                                                    
                                            }
												
										}
									}
								}
								
								echo '</ul>';
							}
						}
						elseif($moduleName == 'reports')
						{
                                $reportUrl  = $_SERVER['REQUEST_URI'];
								$explodeUrl = array();
								$linkValue  = '';
								$explodeUrl = explode("/",$reportUrl);
								for ($y=0; $y<count($explodeUrl); $y++) 
								{
								    if ($explodeUrl[$y] == 'linkValue') 
								    {
									   $linkValue = $explodeUrl[$y+1];
								    }
								}
                                
                                $modName = $modId= '';
                                if(!empty($moduleParams['_mId']))
								{
                                    $modName = $moduleParams['_mId'];
                                    $modId = $moduleParams['_mNme'];                                    
								}
                                
								$linkValue = ucwords(str_replace('-',' ',$linkValue));
								$modName = ucwords(str_replace('-', ' ', $modName));
								
                                $dbHRReport = new Reports_Model_DbTable_HrReports();
								$repTitleArray = $dbHRReport->repTitles($modId , $modName);
								$repTitles = array();
								
								for($i=0;$i<count($repTitleArray);$i++)
								{
									array_push($repTitles,$repTitleArray[$i]['Rep_Title']);
								}
					
								$countReportTitle = count($repTitles);
								
								if($countReportTitle > 0)
								{
									echo '<ul>';
									
									$i=0;
									$halfReportTitle = 	round(count($repTitles)/2);
									
									foreach ($repTitles as $repTitleMenu)
									{	
										if($countReportTitle > 16 && $i == $halfReportTitle)
										{
											echo '</ul>
												<ul>';
										}
										
										$i++;
										
										$iconCls = $repTitleMenu;
										
										if($repTitleMenu == 'Attendance' || $repTitleMenu == 'Leaves' || $repTitleMenu == 'Assignments' || $repTitleMenu == 'Transfer' || $repTitleMenu == 'Resignation'
										   || $repTitleMenu == 'Timesheet' || $repTitleMenu == 'Short Time Off' || $repTitleMenu == 'Probation')
										{
											if($repTitleMenu == 'Probation')
											{
												$module = 'employee';
											}
											else{
												$module = 'employees';
											}											
											
											if($repTitleMenu == 'Timesheet')
											{
												$iconCls = 'timesheets';												
											}
											
										}
										else if($repTitleMenu == 'Shift Scheduling')
										{
											$module ='roster-management';
										}
										else if($repTitleMenu == 'Locations' )
										{
											$module = 'organization';
										}
										else if($repTitleMenu == 'Skill Assessment')
										{
											$iconCls = 'Skillset Assessment';
										}
										else if($repTitleMenu == 'Employee Education' || $repTitleMenu == 'New Joinees')
										{
											$module = 'hr-report';
										}
										else if($repTitleMenu == 'Departments')
										{
											$module ='organization';
											$iconCls ='department-hierarchy';
										}
										else if($modName == 'Payroll Reports')
										{
											$repTitleMenuArray = array('Bonus','Commission','Deductions','Reimbursement','Reimbursement Bank Statement','Provident Fund','Advance salary','Loan','Shift Allowance','Allowances');
											if(in_array($repTitleMenu, $repTitleMenuArray))
											{
												$module = 'payroll';
											}
											else
											{
												$module = 'reports';
												if($repTitleMenu == 'Fixed Insurance')
												{
													$iconCls = 'Insurance Fixed';
													$module = 'report';
												}
												
											}
										}
										else if($modName == 'Recruitment Reports')
										{
											$module = 'recruitment';
										}
										else
										{
											$module = 'report';
										}										
										
										if (strtolower($linkValue) == strtolower($repTitleMenu))
										{
											echo '<li class="active-sub-menu">';
										}
										else
										{
											echo '<li>';
										}
										
										$repTitleMenuTitle =ucwords(strtolower(trim($repTitleMenu)));
										$repTitleMenu = str_replace(' ', '-', strtolower(trim($repTitleMenu)));										
										
                                        $iconsCls = $module .'-'. str_replace(' ', '-', strtolower($iconCls));
                                        echo	'<a class="anchorTag" href="'. $this->baseUrl(str_replace(' ', '-', strtolower($moduleName)) . '/hr-reports/index/_mId/'. str_replace(' ', '-', strtolower($modName)). '/_mNme/'.$modId.'/linkValue/'.$repTitleMenu) . '" title="'.$repTitleMenuTitle.'">
                                            <i class="icon hr-'.$iconsCls.' hrapp-icon-size"></i>
                                            </a>
                                        </li>';
										
									}
									echo '</ul>';
								}
						}
						?>
					</div>
					<!-- END SUB MENU -->
					<?php if ($controllerName === 'index') { ?>
						<div class="row" style="margin-top: 10px;">
							<?php echo $this->layout()->content; ?>
						</div>
					<?php } else if(!empty($layoutRestrictedFor) && $layoutRestrictedFor === "demo-not-booked") { ?>
						<div class="row" style="display: flex; justify-content: center; align-items: center;min-height: 550px">
							<div class="col-xs-12 col-md-10 col-lg-8">
								<div id="demoBookingDiv">
									<div class="row" style="font-size:2em; color: var(--primary-color); font-weight: bold;text-align: center;">
										We're delighted to have you onboard.
									</div>
									<div class="row" style="display: flex; justify-content: center; align-items: center; min-height: 500px">
										<div class="col-sm-3 hide-image-in-small-screens">
											<img alt="demo-booking-left" src="<?php echo $this->baseUrl('images/demo-booking-left-info.png'); ?>" />
										</div>
										<div class="col-xs-12 col-sm-6" style="display: flex; justify-content: center; align-items: center; flex-direction: column">
											<img alt="demo-booking" width="320" src="<?php echo $this->baseUrl('images/demo-booking.png'); ?>" />
											<div style="margin: 20px;color: #1dbed2; font-size:2em;font-weight:bold">
												Get 1-on-1 help
											</div>
											<div style="color: #542d56; text-align: center;font-size:1.1em;font-weight:bold;max-width: 300px">
												Book a free demo today and get personalized support as you set up HRAPP for your crew. We'll help you every step of the way.
											</div>
											<div style="margin-top: 20px;">
												<button class="btn btn-embossed" style="border-radius: 25px !important;background: #1dbed2;color: white;">
													<a href="<?php echo "https://meetings.hubspot.com/chandra3"; ?>" rel="noopener noreferrer" target="_blank" style="color: white !important;">
														Book A Demo
													</a>
												</button>
											</div>
										</div>
										<div class="col-sm-3 hide-image-in-small-screens">
											<img alt="demo-booking-right" src="<?php echo $this->baseUrl('images/demo-booking-right-info.png'); ?>" />
										</div>
									</div>
								</div>
							</div>
						</div>
					<?php } else if(!empty($layoutRestrictedFor) && $layoutRestrictedFor === "payment-alert") { ?>
						<div class="row" style="display: flex; justify-content: center; align-items: center;min-height: 550px">
							<div class="col-xs-12 col-md-10 col-lg-8">
								<div class="panel panel-default" id="paymentExceedCard"  style="margin-bottom:0px; border: 7px solid red" >
									<div class="row" style="display: flex; justify-content: center;">
										<div class="col-xs-12 col-sm-12 col-md-4 hide-image-in-small-screens">
											<img width="100%" alt="warning-img-geo-accuracy" class="lazyload"
											src="<?php echo $this->baseUrl('images/non-payment-alert.gif'); ?>" />
										</div>
										<div class="col-xs-12 col-sm-12 col-md-8" id="payslipTemplateAddress">
											<div class="row" style="padding: 0px 20px;">
												<div class="class-xs-12 payment-reminder-content payment-reminder-header" style="color: red;">
													Access is restricted due to non-payment
												</div>
												<div class="col-xs-7 payment-reminder-content" style="color: grey;margin-bottom: 20px;">
													Active Employee(s)
												</div>
												<div class="col-xs-1 payment-reminder-content">:</div>
												<div class="col-xs-4 payment-reminder-content" style="color: green">
													<?php echo $billingSubscriptionDetails["activeEmployeesCount"]; ?>
												</div>
												<div class="col-xs-7 payment-reminder-content" style="color: grey;   margin-bottom: 20px;">
													Inactive Employee(s)
												</div>
												<div class="col-xs-1 payment-reminder-content">:</div>
												<div class="col-xs-4 payment-reminder-content" style="color: red">
													<?php echo $billingSubscriptionDetails["inactiveEmployeesCount"]; ?>
												</div>
												<div class="col-xs-7 payment-reminder-content" style="color: grey;   margin-bottom: 20px;">
													Due Date
												</div>
												<div class="col-xs-1 payment-reminder-content">:</div>
												<div class="col-xs-4 payment-reminder-content" style="color: var(--primary-color)">
													<?php echo "Immediate"; ?>
												</div>
												<div class="col-xs-7 payment-reminder-content" style="color: grey;   margin-bottom: 20px;">
													Total Amount Due
												</div>
												<div class="col-xs-1 payment-reminder-content">:</div>
												<div class="col-xs-4 payment-reminder-content" style="color: var(--primary-color)">
													<?php echo $billingSubscriptionDetails["paymentAmount"] ? $billingSubscriptionDetails["currency"] . ' ' . $billingSubscriptionDetails["paymentAmount"] : "-"; ?>
												</div>
												<!-- if paymentUrl is empty, we can hide it -->
												<div class="<?php echo (!empty($billingSubscriptionDetails["paymentUrl"])) ? 'col-xs-12' : 'hidden'; ?>">
													<button class="btn btn-embossed" style="border-radius: 25px !important;background: #1ec50f;color: white;">
														<a href="<?php echo $billingSubscriptionDetails["paymentUrl"]; ?>" rel="noopener noreferrer" target="_blank" style="color: white !important;">
															Pay Now
														</a>
													</button>
												</div>
												<div class="col-xs-12">
													<div style="color: var(--primary-color); font-weight: bold;font-size: 1.2em; margin: 8px;">
														We'd be happy to discuss flexible payment options if
														settling the full amount immediately is challenging.
														Please let us know if you'd like to explore such options
													</div>													
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					<?php } else if($signOffAlertType == 'present-red-signoff-alert') { ?>
						<div class="row" style="display: flex; justify-content: center; align-items: center;min-height: 550px">
							<div class="col-xs-12 col-md-10 col-lg-8">
								<div class="panel panel-default" id="paymentExceedCard"  style="margin-bottom:0px; border: 7px solid red" >
									<div class="row" style="display: flex; justify-content: center;">
										<div class="col-xs-12 col-sm-12 col-md-4 hide-image-in-small-screens">
											<img width="100%" alt="warning-img-geo-accuracy" class="lazyload"
											src="<?php echo $this->baseUrl('images/non-payment-alert.gif'); ?>" />
										</div>
										<div class="col-xs-12 col-sm-12 col-md-8" id="payslipTemplateAddress">
											<div class="row" style="padding: 0px 20px;">
												<div class="class-xs-12 payment-reminder-header" style="font-size: 1.2em;text-align: start;">
													As a formal completion of implementation and training, we require your sign-off digitally. 
													We will continue to assist you with the payroll process for the next three months in shadow mode so 
													that the payroll team is comfortable with the product and the payroll processes. 
													We are looking forward to working with you. Thanks and Regards, HRAPP Implementation Team
												</div>
												<div class="col-xs-4 col-sm-3 payment-reminder-content" style="color: var(--primary-color);padding-left: 0px;margin-bottom: 20px;">
													Due Date
												</div>
												<div class="col-xs-1 payment-reminder-content">:</div>
												<div class="col-xs-4 payment-reminder-content" style="color: var(--primary-color)">
													<?php echo "Immediate"; ?>
												</div>
											</div>
											<div class="row">
												<div class="col-xs-12">
													<div class="<?php echo (!empty($signOffAlertDetails) &&!empty($signOffAlertDetails["spocSignOffDocumentLink"])) ? 'col-xs-12 col-sm-6' : 'hidden'; ?>" style="padding-left: 0px">
														<button class="btn btn-lg" style="border-radius: 15px !important;background: white;border: 2px solid var(--primary-color)">
															<a href="<?php echo $signOffAlertDetails["spocSignOffDocumentLink"]; ?>" rel="noopener noreferrer" target="_blank" style="text-transform: capitalize;">
																Implementation SPOC Signoff
															</a>
														</button>
													</div>
													<div class="<?php echo (!empty($signOffAlertDetails) &&!empty($signOffAlertDetails["coordinatorSignOffDocumentLink"])) ? 'col-xs-12 col-sm-6' : 'hidden'; ?>" style="padding-left: 0px;padding-bottom: 10px">
														<button class="btn btn-lg" style="border-radius: 15px !important;background: white;border: 2px solid var(--primary-color)">
															<a href="<?php echo $signOffAlertDetails["coordinatorSignOffDocumentLink"]; ?>" rel="noopener noreferrer" target="_blank" style="text-transform: capitalize;">
																Implementation Coordinator Signoff
															</a>
														</button>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					<?php } else { ?>
						<div class="row" style="margin-top: 10px;">
							<?php echo $this->layout()->content; ?>
						</div>
					<?php } ?>
				</div>
				<!-- check landed form view access before presenting the help screen -->
				<?php if ($isLandedFormHaveAccess == "Yes") { ?>
					<div id="helpOption" class="right-side-sticky-bar hidden">
						<ul class="right-side-sticky-bar-content">
							<li style="background: #ffe665">
								<img
									width="25px"
									height="auto"
									alt="help"
									onerror="this.onerror=null; this.src='<?php echo $this->baseUrl('images/help-icon.png'); ?>'" 
									src="<?php echo $this->baseUrl('images/help-icon.webp'); ?>"
								/>
								<div style="font-size: 12px; text-align: center;">Help</div>
							</li>
						</ul>
					</div>
					<div id="helpScreenPanel" class="help-screen-panel-container">
						<div class="help-screen-panel">
							<div class="help-screen-panel-close-btn">
								<i class="icons-office-52"
								id="helpScreenCloseBtn"
								style= "font-size: 14px;color: var(--primary-color);font-weight: bold;cursor: pointer"
								></i>
							</div>
							<div style="padding: 10px;overflow-y: scroll;max-height: 300px;" id="helpScreenContent"></div>
						</div>
					</div>
					<div id="helpScreenModal" class="modal fade">
						<div class="modal-dialog modal-lg">
							<div class="modal-content announcementWidth" style="border: 2px solid;border-color: var(--primary-color);">
								<div class="modal-header">
									<button type="button" class="close" data-dismiss="modal" aria-hidden="true" style="color: #642138 !important;">
										<i class="icons-office-52" style="font-size:15px; !important"></i>
									</button>
									<h4 class="modal-title" style="text-align:center;font-weight:bold;color:var(--primary-color);"></h4>
								</div>
								<div id="helpScreenModalContent" style="text-align: center;margin-bottom: 10px;"></div>
							</div>
						</div>
					</div>
				<?php } ?>
				<!-- BEGIN FOOTER -->
				<?php if($domainDetails['Copy_Right']==1) { ?>
						
				<div class="footer">
					<?php if (!empty($licenseOrgName)) { ?>
					<div class="copyright">
						<p class="pull-left sm-pull-reset">
							<span>&copy; 2013-<?php echo date("Y"); ?> </span>
							<a href="http://www.capricetech.com" rel="noopener noreferrer" target="_blank">Caprice Cloud Solutions Pvt Ltd</a>.
							<span>All rights reserved.</span>
						</p>
					<?php if ($isDomain=="hrapp.co") { ?>	
						<p class="pull-right sm-pull-reset"> <span>Licensed To: </span><a href="javascript:void(0);"><?php echo $licenseOrgName; ?></a></p>
					<?php }
					else
					{?>
					<p class="pull-right sm-pull-reset"> <span>Powered By: </span>HRAPP</p>
					<?php }
					?>
					</div>
					<div class="copyright" style="border-top: none; padding: 5px 0px;">
						<p class="sm-pull-reset" style="text-align: center;"> <span><span class="supportspan"><a href="<?php echo $domainDetails['Support_Link']; ?>" rel="noopener noreferrer" target="_blank" class="m-r-10">Support</a></span> | 
						<a href="<?php echo $termsOfUse; ?>" rel="noopener noreferrer" target="_blank" class="m-l-10 m-r-10">Terms of use</a> | 
						<a href="<?php echo $privacyPolicy; ?>"  rel="noopener noreferrer" target="_blank" class="m-l-10">Privacy Policy</a></span> </p>
					</div>
					<?php } else { ?>
					<div class="copyright">
						<p class="pull-left sm-pull-reset">
						<span>&copy; 2013-<?php echo date("Y"); ?> </span>
						<a href="http://www.capricetech.com" rel="noopener noreferrer" target="_blank">Caprice Cloud Solutions Pvt Ltd.</a>
						<span>All rights reserved.</span>
						</p>					
						<p class="pull-right sm-pull-reset"> <span><span class="supportspan"><a href="<?php echo $domainDetails['Support_Link']; ?>" rel="noopener noreferrer" target="_blank" class="m-r-10">Support</a></span> | 
						<a href="<?php echo $termsOfUse; ?>" rel="noopener noreferrer" target="_blank" class="m-l-10 m-r-10">Terms of use</a> | 
						<a href="<?php echo $privacyPolicy; ?>" rel="noopener noreferrer" target="_blank" class="m-l-10">Privacy Policy</a></span> </p>
					</div>
					<?php } ?>
					
				</div>
					<!-- END FOOTER -->
					<?php }  else { ?>
						<div class="footer">
						
						<div class="copyright">						
							<p class="sm-pull-reset" style="text-align: center;"> <span><span class="supportspan"><a href="<?php echo $domainDetails['Support_Link']; ?>" rel="noopener noreferrer" target="_blank" class="m-r-10">Support</a></span> | 
							<a href="<?php echo $termsOfUse; ?>" rel="noopener noreferrer" target="_blank" class="m-l-10 m-r-10">Terms of use</a> | 
							<a href="<?php echo $privacyPolicy; ?>" class="m-l-10">Privacy Policy</a></span> </p>
						</div>
						
					</div>
					<?php } ?>
			<!-- END PAGE CONTENT -->
			
            <?php } 
            ?>
            
            
		  </div>
      <!-- END MAIN CONTENT -->
    </section>
	
	<!--	Application Search Begin	-->
	<div id="morphsearch" class="morphsearch">
		<form class="morphsearch-form">
			<div class="prepend-icon">
				<input class="morphsearch-input" placeholder="Search..." type="search">
				<i class="morphsearch-submit" style="height: 55px; width: 60px; top: 50%; left: 3%;"></i>
			</div>
			<div id="searchMenuTemp" style="font-size: 35px;"></div>
		</form>
		<div style="height: auto;" class="morphsearch-content withScroll mCustomScrollbar _mCS_5">
			<div class="mCustomScrollBox mCS-light" id="mCSB_5" style="position:relative; height:100%; overflow:hidden; max-width:100%;">
				<div class="mCSB_container bbccdd mCS_no_scrollbar menu-search-container" style="position:relative; top:0;">
					<?php
                    
					foreach ($hrappForms as $key => $row)
					{
                        echo '<div class="dummy-column user-column module-level"><h2>'.$key.'</h2>';
						
						switch ($key)
						{
							case 'Organization' : $moduleCls = '_orgimg'; break;
							
							case 'Recruitment': $moduleCls = '_jobimg'; break;
							
							case 'Employees': $moduleCls = '_empimg'; break;
							
							case 'Payroll': $moduleCls = '_payrollimg'; break;
							
							case 'Reports': $moduleCls = '_reportimg'; break;
								
							case 'Help': $moduleCls = '_helpimg'; break;

							case 'Roster Management': $moduleCls = '_rosterimg'; break;
							
							case 'Integration': $moduleCls = '_collapse'; break;

							case 'Workflow': $moduleCls = '_collapse'; break;
						}
						
						foreach ($row as $formsName)
						{
                            $formName = $formsName['Form_Name'];
                            
                            if(array_key_exists('Enable',$formsName))
                            {
                                if($formsName['Enable'] == 1)
                                {
                                    $formNewName =  trim($formsName['New_Form_Name']);
                                }
                            }
                            else
                            {
                                $formNewName = $formsName['Form_Name'];
                            }
							if ($key == 'Reports' && $formName != 'Attendance Shortage Report' && $formName != 'Absentees Report'
								&& $formName != 'Monthly Shortage Report' && $formName != 'Timeline' && $formName != 'Custom Report')
							{
								$reports 	= $dbModules->reportsSubMenu($formName);
								
								$modId 			= $reports['Report_SubMenu_ID'];
								$modName 		= $reports['Report_SubMenu_Title'];
								$dbHRReport 	= new Reports_Model_DbTable_HrReports();
								$repTitleArray 	= $dbHRReport->repTitles($modId , $modName);
								$repTitleMenu 	= str_replace(' ', '-', strtolower($repTitleArray[0]['Rep_Title']));
								$keyValue 		= str_replace(' ', '-', strtolower($key));
								$formNames 		= str_replace(' ', '-', strtolower($formName));
								$iconsCls 		= str_replace(' ', '-', strtolower(trim($reports['Report_SubMenu_Title'])));
								$modName 		= str_replace(' ', '-', strtolower(trim($reports['Report_SubMenu_Title'])));
								
                                if($formName == 'Employees Reports')
                                {
                                    echo '<a class="search-menu dummy-media-object menu-'.str_replace(' ','-',strtolower($formNewName)).'" href="'. $this->baseUrl($keyValue.'/hr-reports/view-employees-reports/'). '">
										<i class="icon hr-'.$iconsCls.' hrapp-search-icons"></i>
										<h3>'.$formNewName.'</h3>
										</a>';
                                }
                                elseif($formName == 'HR Reports')
                                {
                                    echo '<a class="search-menu dummy-media-object menu-'.str_replace(' ','-',strtolower($formNewName)).'" href="'. $this->baseUrl($keyValue.'/hr-reports/view-hrreports/'). '">
										<i class="icon hr-'.$iconsCls.' hrapp-search-icons"></i>
										<h3>'.$formNewName.'</h3>
										</a>';
                                        
                                }
                                elseif($formName == 'Payroll Reports')
                                {
                                     echo '<a class="search-menu dummy-media-object menu-'.str_replace(' ','-',strtolower($formNewName)).'" href="'. $this->baseUrl($keyValue.'/hr-reports/view-payroll-reports/'). '">
										<i class="icon hr-'.$iconsCls.' hrapp-search-icons"></i>
										<h3>'.$formNewName.'</h3>
										</a>';
                                        
                                }
                                elseif($formName == 'Recruitment Reports')
                                {
                                    echo '<a class="search-menu dummy-media-object menu-'.str_replace(' ','-',strtolower($formNewName)).'" href="'. $this->baseUrl($keyValue.'/hr-reports/view-recruitment-reports/'). '">
										<i class="icon hr-'.$iconsCls.' hrapp-search-icons"></i>
										<h3>'.$formNewName.'</h3>
										</a>';                                        
                                }                                
							}
							else
							{
								if ($formName != 'Attendance Shortage Report' && $formName != 'Absentees Report'
									&& $formName != 'Monthly Shortage Report')
								{
									if($formName == 'Timeline')
									{
										$iconsCls = 'timeline';
									}
									else
									{
										$iconsCls = str_replace(' ', '-', strtolower($key)) . '-'. str_replace(' ', '-', strtolower($formName));
									}
									
									if($formName == 'Department Hierarchy')
									{
										echo '<a class="search-menu dummy-media-object menu-'.str_replace(' ','-',strtolower($formNewName)).'" href="'. $this->baseUrl(str_replace(' ', '-', strtolower($key)) . '/'. str_replace(' ', '-', strtolower('Departments'))) . '">
												<i class="icon hr-'.$iconsCls.' hrapp-search-icons"></i>
												<h3>'.$formNewName.'</h3>
												</a>';                   
									}
									else if($formName == 'API Dashboard')
									{
										echo '<a class="search-menu dummy-media-object menu-'.str_replace(' ','-',strtolower($formNewName)).'" href="'. $this->baseUrl(str_replace(' ', '-', strtolower($key)) . '/subscription/'. str_replace(' ', '-', strtolower('API Dashboard'))) . '">
												<i class="icon hr-share hrapp-search-icons"></i>
												<h3>'.$formNewName.'</h3>
												</a>';   
									}
									else if($formName == 'OCR')
									{
										echo '<a class="search-menu dummy-media-object menu-'.str_replace(' ','-',strtolower($formNewName)).'" href="'. $this->baseUrl(str_replace(' ', '-', strtolower($key)) . '/subscription/'. str_replace(' ', '-', strtolower('OCR'))) . '">
												<i class="icon hr-zoom-in hrapp-search-icons"></i>
												<h3>'.$formNewName.'</h3>
												</a>';   
									}
									else if($formName == 'GVP')
									{
										echo '<a class="search-menu dummy-media-object menu-'.str_replace(' ','-',strtolower($formNewName)).'" href="'. $this->baseUrl(str_replace(' ', '-', strtolower($key)) . '/subscription/'. str_replace(' ', '-', strtolower('GVP'))) . '">
												<i class="icon hr-document-text-accept hrapp-search-icons"></i>
												<h3>'.$formNewName.'</h3>
												</a>';   
									}
									else
									{
										echo '<a class="search-menu dummy-media-object menu-'.str_replace(' ','-',strtolower($formNewName)).'" href="'. $this->baseUrl(str_replace(' ', '-', strtolower($key)) . '/'. str_replace(' ', '-', strtolower($formName))) . '">
												<i class="icon hr-'.$iconsCls.' hrapp-search-icons"></i>
												<h3>'.$formNewName.'</h3>
												</a>';                                    
									}
									        
                                    if($key == 'Help' && $formName == 'Support')
                                    {
                                        echo '<a class="search-menu dummy-media-object menu-'.str_replace(' ','-',strtolower($formNewName)).'" href="'. $domainDetails['Support_Link'] . '">
											<i class="icon hr-'.$iconsCls.' hrapp-search-icons"></i>
											<h3>'.$formNewName.'</h3>
											</a>';
                                    }                                            
								}
							}
						}						
						
						echo '</div>';
					}
					?>
				</div>
				<div class="mCSB_scrollTools" style="position: absolute; display: none;">
					<div class="mCSB_draggerContainer">
						<div class="mCSB_dragger" style="position: absolute; top: 0px;" oncontextmenu="return false;">
							<div class="mCSB_dragger_bar" style="position:relative;"></div>
						</div>
						<div class="mCSB_draggerRail"></div>
					</div>
				</div>
			</div>
		</div>
		<!-- /morphsearch-content -->
		<span class="morphsearch-close"></span>
    </div>
	<!--	Application Search End	-->

	<!-- Preloader -->
	<div class="loader-overlay">
		<div class="spinner">
			<div class="bounce1"></div>
			<div class="bounce2"></div>
			<div class="bounce3"></div>
		</div>
	</div>
	
	<!-- Scroolup floting button -->
	<a href="javascript:void(0);" class="scrollup" aria-label="Scroll up"><i class="fa fa-angle-up"></i></a>
	
	<!-- Script -->
	<script src="https://cdn.jsdelivr.net/npm/moment@2/moment.min.js"></script>  
	
	<script src="https://code.jquery.com/jquery-3.7.1.js" integrity="sha256-eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=" crossorigin="anonymous"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-migrate/3.5.2/jquery-migrate.js"></script>

	
	
	<script src="<?php echo $this->baseUrl('assets/global/plugins/jquery-block-ui/jquery.blockUI.2.7.0.js?v=1'); ?>"></script> 
	

	<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js" integrity="sha256-lSjKY0/srUM9BE3dPm+c4fBo1dky2v27Gdjm2uoZaL0=" crossorigin="anonymous"></script>

	<script src="<?php echo $this->baseUrl('assets/global/plugins/mcustom-scrollbar/jquery.mCustomScrollbar.concat.min.js'); ?>"></script> <!-- Custom Scrollbar sidebar -->
	<script src="<?php echo $this->baseUrl('assets/global/plugins/jquery-cookies/jquery.cookies.js?v=1'); ?>"></script> <!-- Jquery Cookies, for theme -->
	<script src="<?php echo $this->baseUrl('assets/global/js/pages/timeline.js'); ?>"></script>

	<script src="<?php echo $this->baseUrl('assets/global/plugins/quicksearch/quicksearch.min.js') ?>"></script> <!-- Search Filter -->
	<script src="<?php echo $this->baseUrl('assets/global/plugins/bootstrap/js/bootstrap.min.js'); ?>"></script>
	<script src="<?php echo $this->baseUrl('assets/global/plugins/bootstrap-dropdown/bootstrap-hover-dropdown.min.js?v=1'); ?>"></script> <!-- Show Dropdown on Mouseover -->
	<script src="<?php echo $this->baseUrl('assets/global/plugins/bootstrap-loading/ladda.js'); ?>"></script> <!-- Buttons Loading State -->
	<script src="<?php echo $this->baseUrl('assets/global/plugins/bootstrap-context-menu/bootstrap-contextmenu.min.js'); ?>"></script> <!-- File Upload and Input Masks -->
	<script src="<?php echo $this->baseUrl('assets/global/plugins/noty/jquery.noty.packaged.js'); ?>"></script>  <!-- Notifications -->
	<script src="<?php echo $this->baseUrl('assets/global/plugins/countup/countUp.min.js'); ?>"></script> <!-- Animated Counter Number -->
	<!--<script src="<?php echo $this->baseUrl('assets/global/plugins/charts-chartjs/Chart.min.js'); ?>"></script> --> <!-- ChartJS Chart -->
	<script src="<?php echo $this->baseUrl('assets/global/plugins/icheck/icheck.min.js'); ?>"></script> <!-- Icheck -->
	<script src="<?php echo $this->baseUrl('assets/global/plugins/autosize/autosize.min.js'); ?>"></script> <!-- Textarea autoresize -->
	<script src="<?php echo $this->baseUrl('assets/global/plugins/bootstrap-editable/js/bootstrap-editable.min.js'); ?>"></script> <!-- Inline Edition X-editable -->
    <script src="<?php echo $this->baseUrl('assets/global/plugins/timepicker/jquery-ui-timepicker-addon.js?v=1'); ?>"></script> <!-- Time Picker -->
    <script src="<?php echo $this->baseUrl('assets/global/plugins/bootstrap/js/jasny-bootstrap.js'); ?>"></script> <!-- File Upload and Input Masks -->
	<script src="<?php echo $this->baseUrl('assets/global/plugins/bootstrap-tags-input/bootstrap-tagsinput.min.js'); ?>"></script> <!-- Select Inputs -->
	<script src="<?php echo $this->baseUrl('assets/global/js/popup/jquery.magnific-popup.min.js'); ?>"></script>
    <!--Mailbox-->
	
    
	
	<?php if ($controllerName == 'index') { ?>
	
	<!--DashBoard-->

	<!--<script src="<?php echo $this->baseUrl('assets/global/plugins/charts-highstock/js/highstock.js?v=1'); ?>"></script> --> <!-- High Stock Charts -->
	<!--<script src="<?php echo $this->baseUrl('assets/global/plugins/charts-sparkline/sparkline.min.js'); ?>"></script> --> <!-- Charts Sparkline -->

	<?php } else { ?>
	

	<script src="<?php echo $this->baseUrl('assets/global/plugins/select2/select2.js?v=1'); ?>"></script> 
	<!-- Select Inputs -->
	<script src="<?php echo $this->baseUrl('assets/global/js/pages/fullcalendar.js'); ?>"></script>
	<script src="<?php echo $this->baseUrl('assets/global/plugins/fullcalendar/js/fullcalendar.min.js?v=1'); ?>"></script>
	<link rel="preload" as="style" href="https://cdn.jsdelivr.net/npm/fullcalendar@2/dist/fullcalendar.min.css" onload="this.rel='stylesheet'">
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
	
	<script src="<?php echo $this->baseUrl('assets/global/plugins/bootstrap-datepicker/js/bootstrap-datepicker.js?v=1'); ?>"></script>
	
	<?php } ?>

	<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>

	
	<script src="<?php echo $this->baseUrl('assets/global/plugins/visible/jquery.visible.min.js'); ?>"></script> <!-- Visible in Viewport -->
	<script src="<?php echo $this->baseUrl('assets/global/js/builder.js'); ?>"></script>
	<script src="<?php echo $this->baseUrl('assets/global/js/sidebar_hover.js'); ?>"></script>
	<script src="<?php echo $this->baseUrl('assets/global/js/application.js?v=1'); ?>"></script> <!-- Main Application Script -->
	<script src="<?php echo $this->baseUrl('assets/global/js/plugins.js?v=1'); ?>"></script> <!-- Main Plugin Initialization Script -->
	<script src="<?php echo $this->baseUrl('assets/global/js/pages/search.js'); ?>"></script> <!-- Search Script -->
	<script src="<?php echo $this->baseUrl('assets/global/plugins/bootbox/bootbox.min.js'); ?>"></script> <!-- Delete whole panel -->
	<script src="<?php echo $this->baseUrl('assets/global/js/pages/table_dynamic.js'); ?>"></script>

	<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.20.0/jquery.validate.min.js" integrity="sha512-WMEKGZ7L5LWgaPeJtw9MBM4i5w5OSBlSjTjCtSnvFJGSVD26gE5+Td12qN5pvWXhuWaWcVwF++F7aqu9cvqP0A==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.20.0/additional-methods.min.js" integrity="sha512-TiQST7x/0aMjgVTcep29gi+q5Lk5gVTUPE9XgN0g96rwtjEjLpod4mlBRKWHeBcvGBAEvJBmfDqh2hfMMmg+5A==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
	<script src="<?php echo $this->baseUrl('assets/md-layout1/material-design/js/material.js?v=1'); ?>"></script>
	<script src="<?php echo $this->baseUrl('assets/layout1/js/layout.js?v=1'); ?>"></script>
	
	<!--<script src="<?php echo $this->baseUrl('assets/global/js/ultra-minify-js/ultra-minify-three.min.js'); ?>"></script>-->
	<script src="<?php echo $this->baseUrl('assets/global/js/custom/fnReloadAjax.js'); ?>"></script> <!-- Data table ajax reload -->
	<!-- <script src="https://cdn.ckeditor.com/4.6.2/standard-all/ckeditor.js"></script> -->
	<script src="https://cdnjs.cloudflare.com/ajax/libs/ckeditor/4.9.2/ckeditor.js" integrity="sha512-OF6VwfoBrM/wE3gt0I/lTh1ElROdq3etwAquhEm2YI45Um4ird+0ZFX1IwuBDBRufdXBuYoBb0mqXrmUA2VnOA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
	
	
	<!--  CUSTOM SCRIPTS  -->
	<script type="text/javascript">
	// <![CDATA[
		$(document).ready(function(){
			if (localStorage.getItem('production') === null) {
				localStorage.setItem('production', <?php echo $isProduction; ?>);
			}

			if (localStorage.getItem('ipAddressAPI') === null) {
				localStorage.setItem('ipAddressAPI', <?php echo "'$ipAddressAPI'"; ?>);
			}
			
			if (localStorage.getItem('productLogo') === null) {
				localStorage.setItem('productLogo', <?php echo $isProductLogo; ?>);
			}
			
			if (localStorage.getItem('domain') === null) {
				var domainName = "<?php echo $isDomain; ?>";
				localStorage.setItem('domain',domainName);
			}
			if(localStorage.getItem('showLeaveClosurePopup') === null){
				localStorage.setItem('showLeaveClosurePopup', 'show');
			}
            localStorage.setItem('TZDate', "<?php echo date('m/d/Y'); ?>"); //don't change the 'm/d/Y' format,since we use new Date(tzDate) in jquery, 'm/d/Y' has to be given.
            localStorage.setItem('TZTime', "<?php echo $timezoneDt['Time']; ?>");
                        
		});
		// let url = window.location.href;
		// if (url.includes("hrapp.co.in")) {
		// 	(function(w,d,s,r,k,h,m){
		// 		if(w.performance && w.performance.timing && w.performance.navigation) {
		// 			w[r] = w[r] || function(){(w[r].q = w[r].q || []).push(arguments)};
		// 			h=d.createElement('script');h.async=true;h.setAttribute('src',s+k);
		// 			d.getElementsByTagName('head')[0].appendChild(h);
		// 			(m = window.onerror),(window.onerror = function (b, c, d, f, g) {
		// 			m && m(b, c, d, f, g),g || (g = new Error(b)),(w[r].q = w[r].q || []).push(["captureException",g]);})
		// 		}
        // 	})(window,document,'//static.site24x7rum.in/beacon/site24x7rum-min.js?appKey=','s247r','8c102d223163526a04e9de649108ead1');
		// }
	// ]]>
	</script>
	
	<!-- load the custom.js file only when the layout is not restricted -->
	 <?php if(empty($layoutRestrictedFor)){?>
		<script src="<?php echo $this->baseUrl('assets/global/js/custom/custom.js?v=137'); ?>"></script>
		<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>		
	<?php	}?>
				
		
		         
	
	
	<!-- END PAGE SCRIPTS -->
	<script type="text/javascript">
		var field = document.getElementsByClassName('row');
		
		for (var x in field)
		{
			if (['number', 'function'].indexOf(typeof field[x]) == -1)
			{
				if (field[x].hasAttribute("data-page"))
				{
					checkControllerFuns (field[x].getAttribute("data-page"));
					break;
				}
			}
		}
	</script>
	
	<?php
	// load js files when the layout is not restriced
	if ($controllerName != 'index' && empty($layoutRestrictedFor) && $signOffAlertType != 'present-red-signoff-alert')
	{
		
		if ($controllerName == 'payout-history'){
			echo '<script src="'. $this->baseUrl('assets/global/js/customPagination.js').'"></script>';
		}

		if ($controllerName != 'help-topics')
		{
			switch($controllerName)
			{
				case 'announcements' : echo '<script src="'. $this->baseUrl('assets/global/js/custom/announcements.js?v=7').'"></script>'; break;
				case 'organization-settings': echo '<script src="'. $this->baseUrl('assets/global/js/custom/organization-settings.js?v=20').'"></script>'; break;
				case 'data-import': echo '<script src="'. $this->baseUrl('assets/global/js/custom/data-import.js?v=30').'"></script>'; break;
				case 'departments': echo '<script src="'. $this->baseUrl('assets/global/js/custom/departments.js?v=3').'"></script>'; break;
				case 'job-posts': echo '<script src="'. $this->baseUrl('assets/global/js/custom/job-posts.js?v=11').'"></script>'; break;
				case 'schedule-interviews': echo '<script src="'. $this->baseUrl('assets/global/js/custom/schedule-interviews.js?v=5').'"></script>'; break;
				case 'individuals': echo '<script src="'. $this->baseUrl('assets/global/js/custom/employee-onboard.js?v=20').'"></script>'; break;
				case 'employees': echo '<script src="'. $this->baseUrl('assets/global/js/custom/employees.js?v=67').'"></script>'; break;
				case 'attendance': echo '<script src="'. $this->baseUrl('assets/global/js/custom/attendance.js?v=47').'"></script>'; break;
				case 'insurance': echo '<script src="'. $this->baseUrl('assets/global/js/custom/insurance.js?v=8').'"></script>'; break;
				case 'payout': echo '<script src="'. $this->baseUrl('assets/global/js/custom/payout.js?v=7').'"></script>'; break;
				case 'salary-payslip': echo '<script src="'. $this->baseUrl('assets/global/js/custom/salary-payslip.js?v=25').'"></script>'; break;
				case 'salary': echo '<script src="'. $this->baseUrl('assets/global/js/custom/salary.js?v=27').'"></script>'; break;
				case 'allowances': echo '<script src="'. $this->baseUrl('assets/global/js/custom/allowances.js?v=16').'"></script>'; break;
				case 'reimbursement': echo '<script src="'. $this->baseUrl('assets/global/js/custom/reimbursement.js?v=16').'"></script>'; break;
				case 'attendance-finalization': echo '<script src="'. $this->baseUrl('assets/global/js/custom/attendance-finalization.js?v=1').'"></script>'; break;
				case 'custom-employee-groups': echo '<script src="'. $this->baseUrl('assets/global/js/custom/custom-employee-groups.js?v=1').'"></script>'; break;
				case 'ip-whitelisting': echo '<script src="'. $this->baseUrl('assets/global/js/custom/ip-whitelisting.js?v=1').'"></script>'; break;
				case 'tax-declarations': echo '<script src="'. $this->baseUrl('assets/global/js/custom/tax-declarations.js?v=23').'"></script>'; break;
				case 'hr-reports': echo '<script src="'. $this->baseUrl('assets/global/js/custom/hr-reports.js?v=68').'"></script>'; break;
				case 'leaves': echo '<script src="'. $this->baseUrl('assets/global/js/custom/leaves.js?v=117').'"></script>'; break;
				case 'memos': echo '<script src="'. $this->baseUrl('assets/global/js/custom/memos.js?v=2').'"></script>'; break;
				case 'locations': echo '<script src="'. $this->baseUrl('assets/global/js/custom/locations.js?v=2').'"></script>'; break;
				case 'shift-scheduling': echo '<script src="'. $this->baseUrl('assets/global/js/custom/shift-scheduling.js?v=3').'"></script>'; break;
				case 'tax-rules': echo '<script src="'. $this->baseUrl('assets/global/js/custom/tax-rules.js?v=9').'"></script>'; break;
				case 'compliance-forms': echo '<script src="'. $this->baseUrl('assets/global/js/custom/compliance-forms.js?v=4').'"></script>'; break;
				case 'payout-history': echo '<script src="'. $this->baseUrl('assets/global/js/custom/payout-history.js?v=5').'"></script>'; break;
				case 'payslip-template': echo '<script src="'. $this->baseUrl('assets/global/js/custom/payslip-template.js?v=16').'"></script>'; break;
				case 'interview-calendar' : echo '<script src="'. $this->baseUrl('assets/global/js/custom/interview-calendar.js?v=3').'"></script>'; break;
				case 'interview-rounds-master' : echo '<script src="'. $this->baseUrl('assets/global/js/custom/interview-rounds-master.js?v=7').'"></script>'; break;
				case 'job-candidates' : echo '<script src="'. $this->baseUrl('assets/global/js/custom/job-candidates.js?v=14').'"></script>'; break;
				case 'resignation' : echo '<script src="'. $this->baseUrl('assets/global/js/custom/resignation.js?v=23').'"></script>'; break;
				case 'system-log': echo '<script src="'. $this->baseUrl('assets/global/js/custom/system-log.js?v=1').'"></script>'; break;
				case 'proof-of-investment': echo '<script src="'. $this->baseUrl('assets/global/js/custom/proof-of-investment.js').'"></script>'; break;
				case 'tax': echo '<script src="'. $this->baseUrl('assets/global/js/custom/tax.js?v=1').'"></script>'; break;
				case 'projects' : echo '<script src="'. $this->baseUrl('assets/global/js/custom/projects.js?v=4').'"></script>'; break;
				case 'timesheets': echo '<script src="'. $this->baseUrl('assets/global/js/custom/timesheets.js?v=21').'"></script>'; break;
				case 'perquisite-tracker': echo '<script src="'. $this->baseUrl('assets/global/js/custom/perquisite-tracker.js?v=2').'"></script>'; break;
				case 'assignments': echo '<script src="'. $this->baseUrl('assets/global/js/custom/assignments.js?v=5').'"></script>'; break;
				case 'employees-document-upload': echo '<script src="'. $this->baseUrl('assets/global/js/custom/employees-document-upload.js?v=5').'"></script>'; break;
				case 'deductions': echo '<script src="'. $this->baseUrl('assets/global/js/custom/deductions.js?v=4').'"></script>'; break;
				case 'employee-bank-account': echo '<script src="'. $this->baseUrl('assets/global/js/custom/employee-bank-account.js?v=4').'"></script>'; break;
				case 'clients-master': echo '<script src="'. $this->baseUrl('assets/global/js/custom/clients-master.js?v=3').'"></script>'; break;
				case 'dashboard': echo '<script src="'. $this->baseUrl('assets/global/js/custom/dashboard.js?v=5').'"></script>'; break;
				case 'dynamic-form-builder': echo '<script src="'. $this->baseUrl('assets/global/js/custom/dynamic-form-builder.js?v=4').'"></script>'; break;
				case 'organization-chart': echo '<script src="'. $this->baseUrl('assets/global/js/custom/organization-chart.js?v=7').'"></script>'; break;
				case 'roles-template': echo '<script src="'. $this->baseUrl('assets/global/js/custom/roles-template.js?v=5').'"></script>'; break;
				case 'approval-management': echo '<script src="'. $this->baseUrl('assets/global/js/custom/approval-management.js?v=16').'"></script>'; break;
				case 'workflow-builder': echo '<script src="'. $this->baseUrl('assets/global/js/custom/workflow-builder.js?v=5').'"></script>'; break;
				case 'provident-fund': echo '<script src="'. $this->baseUrl('assets/global/js/custom/provident-fund.js?v=6').'"></script>'; break;
				case 'document-generator': echo '<script src="'. $this->baseUrl('assets/global/js/custom/document-generator.js?v=3').'"></script>'; break;
				case 'document-template-engine': echo '<script src="'. $this->baseUrl('assets/global/js/custom/document-template-engine.js?v=3').'"></script>'; break;
				case 'compensatory-off': echo '<script src="'. $this->baseUrl('assets/global/js/custom/compensatory-off.js?v=23').'"></script>'; break;
				case 'shift-allowance': echo '<script src="'. $this->baseUrl('assets/global/js/custom/shift-allowance.js?v=4').'"></script>'; break;
				case 'tds-history': echo '<script src="'. $this->baseUrl('assets/global/js/custom/tds-history.js?v=4').'"></script>'; break;
				case 'performance-evaluation': echo '<script src="'. $this->baseUrl('assets/global/js/custom/performance-evaluation.js?v=13').'"></script>'; break;
				case 'holidays': echo '<script src="'. $this->baseUrl('assets/global/js/custom/holidays.js?v=10').'"></script>'; break;
				case 'timeline': echo '<script src="'. $this->baseUrl('assets/global/js/custom/timeline.js?v=3').'"></script>'; break;
				case 'custom-report': echo '<script src="'. $this->baseUrl('assets/global/js/custom/custom-report.js?v=4').'"></script>'; break;
				case 'advance-salary' : echo '<script src="'. $this->baseUrl('assets/global/js/custom/advance-salary.js?v=4').'"></script>'; break;
				case 'bonus' : echo '<script src="'. $this->baseUrl('assets/global/js/custom/bonus.js?v=2').'"></script>'; break;
				case 'fixed-health-insurance' : echo '<script src="'. $this->baseUrl('assets/global/js/custom/fixed-health-insurance.js?v=3').'"></script>'; break;
				case 'loan' : echo '<script src="'. $this->baseUrl('assets/global/js/custom/loan.js?v=6').'"></script>'; break;
				case 'short-time-off' : echo '<script src="'. $this->baseUrl('assets/global/js/custom/short-time-off.js?v=31').'"></script>'; break;
				case 'etf': echo '<script src="'. $this->baseUrl('assets/global/js/custom/etf.js?v=6').'"></script>'; break;
				case 'billing': echo '<script src="'. $this->baseUrl('assets/global/js/custom/billing.js?v=2').'"></script>'; break;
				case 'employee-travel': echo '<script src="'. $this->baseUrl('assets/global/js/custom/employee-travel.js?v=4').'"></script>'; break;
				case 'labour-welfare-fund': echo '<script src="'. $this->baseUrl('assets/global/js/custom/labour-welfare-fund.js?v=3').'"></script>'; break;
				case 'organization-policies': echo '<script src="'. $this->baseUrl('assets/global/js/custom/organization-policies.js?v=2').'"></script>'; break;
				case 'final-settlement': echo '<script src="'. $this->baseUrl('assets/global/js/custom/final-settlement.js?v=2').'"></script>'; break;
				case 'transfer': echo '<script src="'. $this->baseUrl('assets/global/js/custom/transfer.js?v=2').'"></script>'; break;
				case 'flexi-benefit-declaration': echo '<script src="'. $this->baseUrl('assets/global/js/custom/flexi-benefit-declaration.js?v=4').'"></script>'; break;
				case 'attendance-shortage-report': echo '<script src="'. $this->baseUrl('assets/global/js/custom/attendance-shortage-report.js?v=1').'"></script>';break;
				case 'awards': echo '<script src="'. $this->baseUrl('assets/global/js/custom/awards.js?v=1').'"></script>';break;
				case 'commission': echo '<script src="'. $this->baseUrl('assets/global/js/custom/commission.js?v=1').'"></script>';break;
				case 'complaints': echo '<script src="'. $this->baseUrl('assets/global/js/custom/complaints.js?v=1').'"></script>';break;
				case 'eft-configuration': echo '<script src="'. $this->baseUrl('assets/global/js/custom/eft-configuration.js?v=1').'"></script>';break;
				case 'form-downloads': echo '<script src="'. $this->baseUrl('assets/global/js/custom/form-downloads.js?v=1').'"></script>';break;
				case 'gratuity-nomination': echo '<script src="'. $this->baseUrl('assets/global/js/custom/gratuity-nomination.js?v=1').'"></script>';break;
				case 'gratuity': echo '<script src="'. $this->baseUrl('assets/global/js/custom/gratuity.js?v=1').'"></script>';break;
				case "inbox": echo '<script src="'. $this->baseUrl('assets/global/js/custom/inbox.js?v=1').'"></script>';break;
				case "job-post-requisition": echo '<script src="'. $this->baseUrl('assets/global/js/custom/job-post-requisition.js?v=1').'"></script>';break;
				case "mailbox": echo '<script src="'. $this->baseUrl('assets/global/js/custom/mailbox.js?v=1').'"></script>';break;
				case "monthly-shortage-report":  echo '<script src="'. $this->baseUrl('assets/global/js/custom/monthly-shortage-report.js?v=1').'"></script>';break;
				case "organization-profile":  echo '<script src="'. $this->baseUrl('assets/global/js/custom/organization-profile.js?v=1').'"></script>';break;
				case "shift-type":  echo '<script src="'. $this->baseUrl('assets/global/js/custom/shift-type.js?v=1').'"></script>';break;
				case "shortlisted-candidates":  echo '<script src="'. $this->baseUrl('assets/global/js/custom/shortlisted-candidates.js?v=1').'"></script>';break;
				case "skillset-assessment":  echo '<script src="'. $this->baseUrl('assets/global/js/custom/skillset-assessment.js?v=1').'"></script>';break;
				case "warnings": echo '<script src="'. $this->baseUrl('assets/global/js/custom/warnings.js?v=1').'"></script>';break;
				default: echo '<script src="'. $this->baseUrl('assets/global/js/custom/'. $controllerName .'.js').'"></script>';break;
			}
		}
	}
	
	// load scripts when the layout is not restriced
	if(empty($layoutRestrictedFor)){
		switch ($controllerName)
		{
			case 'payout':
			case 'salary-payslip':
				echo '<script src="'. $this->baseUrl('assets/global/plugins/step-form-wizard/js/step-form-wizard.js?v=1').'"></script>'; //Step Form Validation
				if($controllerName == "payout"){
					echo '<script src="'. $this->baseUrl('assets/global/js/customPagination.js').'"></script>';
					echo '<script src="https://cdnjs.cloudflare.com/ajax/libs/ion-rangeslider/2.3.0/js/ion.rangeSlider.min.js"></script>';
				}
				if($controllerName == "salary-payslip"){
					echo '<script src="'. $this->baseUrl('assets/global/js/customPagination.js').'"></script>';
					echo '<script src="https://cdnjs.cloudflare.com/ajax/libs/ion-rangeslider/2.3.0/js/ion.rangeSlider.min.js"></script>';
					echo '<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>';
				}
				break;
			case 'hr-reports':
				//echo '<script src="'. $this->baseUrl('assets/global/plugins/charts-highstock/js/highstock.js?v=1').'"></script>';
				//echo '<script src="'. $this->baseUrl('assets/global/plugins/charts-highstock/js/modules/exporting.js').'"></script>';
				echo '<script src="https://cdn.jsdelivr.net/npm/exceljs@4.3.0/dist/exceljs.min.js"></script>';
				break;
			case 'data-import':
				echo '<script src="'.$this->baseUrl('assets/global/plugins/intlTelInput/js/intlTelInput.js?v=1').'"></script>';
				echo '<script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/10.0.2/js/utils.js"></script>';
			break;
			case 'workflow-builder' :

				//set employee id in localstorage 
				echo '<script>localStorage.setItem("LoginEmpId", '.$empId.');</script>';
				// api to get the client ip address
				$clientipUrl = Zend_Registry::get('clientipUrl');
				echo '<script>localStorage.setItem("clientipUrl", "'.$clientipUrl.'");</script>';			
				
				// workflow plugins js and css
				echo '<script src="'. $this->baseUrl('assets/global/plugins/workflow/expression-editor.js?v=2').'"></script>'; 
				echo '<script src="'. $this->baseUrl('assets/global/plugins/workflow/jsplumb.min.js').'"></script>'; 
				echo '<script src="'. $this->baseUrl('assets/global/plugins/workflow/taworkflow.js?v=3').'"></script>'; 
				echo '<script src="'. $this->baseUrl('assets/global/plugins/workflow/taworkflowModal.js?v=2').'"></script>'; 
				echo '<script src="'. $this->baseUrl('assets/global/plugins/workflow/tafbform.js').'"></script>'; 
				echo '<script src="'. $this->baseUrl('assets/global/plugins/workflow/taServiceProcessor.js?v=6').'"></script>'; 
				echo '<script src="'. $this->baseUrl('assets/global/plugins/workflow/q.js').'"></script>'; 


				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/workflow/taworkflow.css?v=1').'">';			
				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/workflow/expression-editor.css?v=1').'">';
				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/workflow/ta-tooltip.css').'">'; 
				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/workflow/tafbform.css').'">'; 

				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/recruitment/workflow-builder.css').'">'; 
				
				echo '<link href="'. $this->baseUrl('assets/global/css/recruitment/recruitment.css').'" rel="stylesheet">';



				// Image cropping
				echo '<script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/0.8.1/cropper.min.js"></script>'; 
				echo '<link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/cropper/2.3.4/cropper.min.css"/>';


				//  Material icon plugin 
				echo '<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">';

				// Custom pagination
				echo '<script src="'. $this->baseUrl('assets/global/js/customPagination.js').'"></script>';
				echo '<link href="'.$this->baseUrl('assets/global/css/customPagination.css').'" rel="stylesheet">';
				

				

				break;  
			case 'clients-master' : 
				/**set employee id in localstorage */
				echo '<script>localStorage.setItem("LoginEmpId", '.$empId.');</script>';

				// api to get the client ip address
				$clientipUrl = Zend_Registry::get('clientipUrl');
				echo '<script>localStorage.setItem("clientipUrl", "'.$clientipUrl.'");</script>';
							
				
				// clients submodule css
				echo '<link href="'. $this->baseUrl('assets/global/css/recruitment/clients-master.css').'" rel="stylesheet">';
				echo '<link href="'. $this->baseUrl('assets/global/css/recruitment/recruitment.css').'" rel="stylesheet">';
				// Image cropping
				echo '<script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/0.8.1/cropper.min.js"></script>'; 
				echo '<link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/cropper/2.3.4/cropper.min.css"/>';
			
				//  Material icon plugin 
				echo '<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">';
			
				// Custom pagination
				echo '<script src="'. $this->baseUrl('assets/global/js/customPagination.js').'"></script>';
				echo '<link href="'.$this->baseUrl('assets/global/css/customPagination.css').'" rel="stylesheet">';

			
				//  Google APIs
				echo '<script type="text/javascript" src="https://maps.googleapis.com/maps/api/js?key="'.Zend_Registry::get('googleAddressAPIKey').'"&libraries=places"></script>';
				echo '<script src="'. $this->baseUrl('assets/global/plugins/google-maps/gmaps.js').'"></script>'; 
				echo '<script src="http://code.jquerygeo.com/jquery.geo-1.1.0.min.js"></script>';

				break;
			case "compensatory-off":
				echo '<script src="https://cdn.jsdelivr.net/npm/exceljs@4.3.0/dist/exceljs.min.js"></script>';
				echo '<link href="'.$this->baseUrl('assets/global/css/employees/employees.css?v=4').'" rel="stylesheet">';
				break;


			case 'income-under-section24' :
				echo '<script src="'. $this->baseUrl('vue/custom/custom.vue.js?v=2').'"></script>';
				echo '<script src="'. $this->baseUrl('vue/components/fetchingErrorScreen.vue.js?v=4').'"></script>'; 
				echo '<script src="'. $this->baseUrl('vue/components/customSnackBar.vue.js?v=3').'"></script>'; 
				
				// plugin links
				echo '<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900">'; 
				echo '<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@mdi/font@4.x/css/materialdesignicons.min.css">'; 
				echo '<link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">'; 
				// vue component style tags
				echo '<link rel="stylesheet" href="'. $this->baseUrl('vue/assets/css/VueCommon.css?v=2').'">'; 
				echo '<link rel="stylesheet" href="'. $this->baseUrl('vue/assets/css/customTooltip.css?v=3').'">'; 
				echo '<link rel="stylesheet" href="'. $this->baseUrl('vue/assets/scss/dataTableStyle.scss?v=2').'">'; 
				// components
				echo '<script src="'. $this->baseUrl('vue/components/customLoading.vue.js?v=2').'"></script>';
				echo '<script src="'. $this->baseUrl('vue/components/customStatisticsCard.vue.js?v=8').'"></script>';
				echo '<script src="'. $this->baseurl('vue/views/income-under-section24/ListIncomeUnderSection24.vue.js?v=19').'"></script>';
				echo '<script src="'. $this->baseurl('vue/views/income-under-section24/ViewTaxSheetDetails.vue.js?v=7').'"></script>';
				echo '<script src="'. $this->baseUrl('vue/views/income-under-section24/RentedDataTable.vue.js?v=8').'"></script>';
				echo '<script src="'. $this->baseUrl('vue/views/income-under-section24/SelfOccupiedDataTable.vue.js?v=7').'"></script>';
				echo '<script src="'. $this->baseUrl('vue/views/income-under-section24/ViewRentedDetails.vue.js?v=10').'"></script>'; 
				echo '<script src="'. $this->baseUrl('vue/views/income-under-section24/ViewSelfOccupiedDetails.vue.js?v=11').'"></script>'; 
				echo '<script src="'. $this->baseUrl('vue/views/income-under-section24/ViewHousePropertyDocuments.vue.js?v=5').'"></script>'; 
				echo '<script src="'. $this->baseUrl('vue/views/income-under-section24/AddEditRentedProperty.vue.js?v=9').'"></script>';
				echo '<script src="'. $this->baseUrl('vue/views/income-under-section24/AddEditSelfOccupiedDetails.vue.js?v=9').'"></script>'; 
				echo '<script src="'. $this->baseUrl('vue/components/noRecordInitialScreen.vue.js?v=5').'"></script>'; 
				echo '<script src="'. $this->baseUrl('vue/components/customModal.vue.js?v=7').'"></script>';
				echo '<script src="'. $this->baseUrl('vue/components/customDeleteConfirmationModal.vue.js?v=7').'"></script>'; 
				echo '<script src="'. $this->baseUrl('vue/components/employeeListPopup.vue.js?v=4').'"></script>';
				echo '<script src="'. $this->baseurl('vue/views/income-under-section24/TaxRegimeComparisonModal.vue.js?v=1').'"></script>';
				
				// css file
				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/vueFormStyles.css').'">';
				echo '<link rel="stylesheet" href="'. $this->baseUrl('vue/assets/css/customFilePondDocUploader.css?v=1').'">';
			break; 

			case 'proof-of-investment' :
				echo '<script src="'. $this->baseUrl('vue/custom/custom.vue.js?v=2').'"></script>';
				// form css
				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/vueFormStyles.css').'">';

				// common components
				echo '<script src="'. $this->baseUrl('vue/components/customEmailTemplateModal.vue.js?v=3').'"></script>';
				echo '<script src="'. $this->baseUrl('vue/components/customLoading.vue.js?v=2').'"></script>';
				echo '<script src="'. $this->baseUrl('vue/components/accessDeniedScreen.vue.js?v=2').'"></script>';
				echo '<script src="'. $this->baseUrl('vue/components/fetchingErrorScreen.vue.js?v=4').'"></script>'; 
				echo '<script src="'. $this->baseUrl('vue/components/customSnackBar.vue.js?v=3').'"></script>'; 

				// vue component style tags
				echo '<link rel="stylesheet" href="'. $this->baseUrl('vue/assets/css/VueCommon.css?v=2').'">'; 
				echo '<link rel="stylesheet" href="'. $this->baseUrl('vue/assets/css/customTooltip.css?v=3').'">'; 
				echo '<link rel="stylesheet" href="'. $this->baseUrl('vue/assets/scss/dataTableStyle.scss?v=2').'">'; 
				
				// plugin links - fonts and icons
				echo '<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900">'; 
				echo '<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@mdi/font@4.x/css/materialdesignicons.min.css">'; 
				echo '<link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">'; 
				echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.12.1/css/all.min.css">'; 

				// POI form components
				echo '<script src="'. $this->baseUrl('vue/views/proof-of-investment/ViewPOIStatistics.vue.js?v=6').'"></script>';
				echo '<script src="'. $this->baseurl('vue/views/income-under-section24/ListIncomeUnderSection24.vue.js?v=18').'"></script>';
				echo '<script src="'. $this->baseUrl('vue/views/income-under-section24/RentedDataTable.vue.js?v=8').'"></script>';
				echo '<script src="'. $this->baseUrl('vue/views/income-under-section24/SelfOccupiedDataTable.vue.js?v=7').'"></script>';
				echo '<script src="'. $this->baseUrl('vue/views/income-under-section24/ViewRentedDetails.vue.js?v=10').'"></script>'; 
				echo '<script src="'. $this->baseUrl('vue/views/income-under-section24/ViewSelfOccupiedDetails.vue.js?v=7').'"></script>'; 
				echo '<script src="'. $this->baseUrl('vue/components/customStatisticsCard.vue.js?v=8').'"></script>';
				echo '<script src="'. $this->baseUrl('vue/components/customModal.vue.js?v=7').'"></script>';
				echo '<script src="'. $this->baseUrl('vue/components/customDeleteConfirmationModal.vue.js?v=7').'"></script>'; 
				echo '<script src="'. $this->baseUrl('vue/components/noRecordInitialScreen.vue.js?v=5').'"></script>'; 
				echo '<script src="'. $this->baseUrl('vue/components/employeeListPopup.vue.js?v=4').'"></script>';
				echo '<script src="'. $this->baseUrl('vue/views/income-under-section24/ViewHousePropertyDocuments.vue.js?v=5').'"></script>'; 
			break;
		
			case 'general':
				echo '<script src="'. $this->baseUrl('vue/custom/custom.vue.js?v=2').'"></script>';
				// form css
				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/vueFormStyles.css').'">';

				// common components
				echo '<script src="'. $this->baseUrl('vue/components/customLoading.vue.js?v=2').'"></script>';
				echo '<script src="'. $this->baseUrl('vue/components/accessDeniedScreen.vue.js?v=2').'"></script>';
				echo '<script src="'. $this->baseUrl('vue/components/fetchingErrorScreen.vue.js?v=4').'"></script>'; 
				echo '<script src="'. $this->baseUrl('vue/components/customSnackBar.vue.js?v=3').'"></script>'; 

				// vue component style tags
				echo '<link rel="stylesheet" href="'. $this->baseUrl('vue/assets/css/VueCommon.css?v=2').'">'; 
				echo '<link rel="stylesheet" href="'. $this->baseUrl('vue/assets/css/customTooltip.css?v=3').'">'; 

				// plugin links - fonts and icons
				echo '<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900">'; 
				echo '<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@mdi/font@4.x/css/materialdesignicons.min.css">'; 
				echo '<link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">';
				// general form Components
				echo '<script src="'. $this->baseUrl('vue/views/general/ViewOrganizationDetails.vue.js?v=5').'"></script>';
			break;

			case 'interview-rounds-master' : 
			/**set employee id in localstorage */
			echo '<script>localStorage.setItem("LoginEmpId", '.$empId.');</script>';
			
			// rounds submodule css
			echo '<link href="'. $this->baseUrl('assets/global/css/recruitment/interview-rounds-master.css').'" rel="stylesheet">';
			echo '<link href="'. $this->baseUrl('assets/global/css/recruitment/recruitment.css').'" rel="stylesheet">';

			//  Material icon plugin 
			echo '<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">';
			// Custom pagination
			echo '<script src="'. $this->baseUrl('assets/global/js/customPagination.js').'"></script>';
			echo '<link href="'.$this->baseUrl('assets/global/css/customPagination.css').'" rel="stylesheet">';
			
			break;
					
					

			case 'roles-template':
						
				/**set employee id in localstorage */
				echo '<script>localStorage.setItem("LoginEmpId", '.$empId.');</script>';

				$clientipUrl = Zend_Registry::get('clientipUrl');
				echo '<script>localStorage.setItem("clientipUrl", "'.$clientipUrl.'");</script>';
			

				//load css file
				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/employees/roles-template.css').'">'; 
				echo '<link href="'. $this->baseUrl('assets/global/css/recruitment/recruitment.css').'" rel="stylesheet">';

				echo '<link href="'. $this->baseUrl('assets/global/css/recruitment/recruitment.css').'" rel="stylesheet">';


				//load pagination file 
				echo '<script src="'. $this->baseUrl('assets/global/js/customPagination.js').'"></script>';
				echo '<link href="'.$this->baseUrl('assets/global/css/customPagination.css').'" rel="stylesheet">'; 

			
			break;

				
			case 'job-posts' :
				// save the base ATS base url in Local Storage
				echo '<script>localStorage.setItem("LoginEmpId", '.$empId.');</script>';

				// save the API to get client Ip address in local storage
				$clientipUrl = Zend_Registry::get('clientipUrl');
				echo '<script>localStorage.setItem("clientipUrl", "'.$clientipUrl.'");</script>';
			
				// css for Job post
				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/recruitment/job-post.css').'">'; 
				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/recruitment/recruitment.css').'">'; 
				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/formTabStyle.css?v=4').'">';

				// custom pagination
				echo '<script src="'. $this->baseUrl('assets/global/js/customPagination.js').'"></script>'; 
				echo '<link href="'.$this->baseUrl('assets/global/css/customPagination.css').'" rel="stylesheet">'; 

				//  Material icon plugin 
				echo '<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">';

				// load the range slider plugin
				echo '<link href="https://cdnjs.cloudflare.com/ajax/libs/ion-rangeslider/2.3.0/css/ion.rangeSlider.min.css" rel="stylesheet">'; 

				echo '<script src="https://cdnjs.cloudflare.com/ajax/libs/ion-rangeslider/2.3.0/js/ion.rangeSlider.min.js"></script>';
				// Worklow js files
				echo '<script src="'. $this->baseUrl('assets/global/plugins/workflow/expression-editor.js?v=2').'"></script>'; 
				echo '<script src="'. $this->baseUrl('assets/global/plugins/workflow/jsplumb.min.js').'"></script>'; 
				echo '<script src="'. $this->baseUrl('assets/global/plugins/workflow/taworkflow.js?v=3').'"></script>'; 
				echo '<script src="'. $this->baseUrl('assets/global/plugins/workflow/taworkflowModal.js?v=2').'"></script>'; 
				echo '<script src="'. $this->baseUrl('assets/global/plugins/workflow/tafbform.js').'"></script>'; 
				echo '<script src="'. $this->baseUrl('assets/global/plugins/workflow/taServiceProcessor.js?v=6').'"></script>'; 
				echo '<script src="'. $this->baseUrl('assets/global/plugins/workflow/q.js').'"></script>'; 
				break;
				case 'job-candidates' :
				/**set employee id in localstorage */
				echo '<script>localStorage.setItem("LoginEmpId", '.$empId.');</script>';
				$clientipUrl = Zend_Registry::get('clientipUrl');
				echo '<script>localStorage.setItem("clientipUrl", "'.$clientipUrl.'");</script>';
				
				//load css file
				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/recruitment/job-candidates.css').'">'; 
				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/recruitment/recruitment.css').'">'; 

				// echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/recruitment/clients.css').'">'; 
				//  Material icon plugin 
				echo '<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">';
				//load pagination file 
				echo '<script src="'. $this->baseUrl('assets/global/js/customPagination.js').'"></script>'; 
				echo '<link href="'.$this->baseUrl('assets/global/css/customPagination.css').'" rel="stylesheet">'; 

				// Image cropping package cdn
				echo '<script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/0.8.1/cropper.min.js""></script>'; 
				echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropper/2.3.4/cropper.min.css">'; 			

				echo '<script src="'. $this->baseUrl('assets/global/js/custom/job-candidates-scoreupdation.js?v=5').'"></script>';
				echo '<link href="'.$this->baseUrl('assets/global/css/recruitment/job-candidates-scoreupdation.css').'" rel="stylesheet">'; 


			break;



			case 'schedule-interviews':
				/**set employee id in localstorage */
				echo '<script>localStorage.setItem("LoginEmpId", '.$empId.');</script>';
			
				//load css file
				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/recruitment/schedule-interviews.css').'">'; 
				echo '<link href="'. $this->baseUrl('assets/global/css/recruitment/recruitment.css').'" rel="stylesheet">';

				
				//load pagination file 
				echo '<script src="'. $this->baseUrl('assets/global/js/customPagination.js').'"></script>'; 
				echo '<link href="'.$this->baseUrl('assets/global/css/customPagination.css').'" rel="stylesheet">'; 


				// Image cropping package cdn
				echo '<script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/0.8.1/cropper.min.js""></script>'; 
				echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropper/2.3.4/cropper.min.css">'; 

				//  Material icon plugin 
				echo '<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">';

				
			break;

			case 'interview-calendar':
				/**set employee id in localstorage */
				echo '<script>localStorage.setItem("LoginEmpId", '.$empId.');</script>';

				$clientipUrl = Zend_Registry::get('clientipUrl');
				echo '<script>localStorage.setItem("clientipUrl", "'.$clientipUrl.'");</script>';
			
				//load css file
				echo '<link href="'. $this->baseUrl('assets/global/css/recruitment/interview-calendar.css').'" rel="stylesheet">';
				echo '<link href="'. $this->baseUrl('assets/global/css/recruitment/recruitment.css').'" rel="stylesheet">';


				//load pagination file 
				echo '<script src="'. $this->baseUrl('assets/global/js/customPagination.js').'"></script>';
				echo '<link href="'.$this->baseUrl('assets/global/css/customPagination.css').'" rel="stylesheet">'; 
			
			break;

			
			
			case 'organization-chart':
				echo '<script>localStorage.setItem("LoginEmpId", '.$empId.');</script>';   
					
				
				// load css files with below link
				// used for local css file
				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/employees/organization-chart.css?v=2').'">'; 
				// used for load orgchart plugin related css files
				echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/normalize/5.0.0/normalize.min.css">';
				echo "<link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css'>";
				echo "<link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/orgchart/3.8.0/css/jquery.orgchart.min.css'>";
				// load script files
				// used for export the pdf files
				echo '<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.4.1/jspdf.min.js"></script>';
				// used for load orgchart related scripts files
				echo '<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/orgchart/3.8.0/js/jquery.orgchart.min.js"></script>';
				// used for convert html to canvas the particular content
				echo '<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/html2canvas@1.0.0-rc.4/dist/html2canvas.min.js"></script>';
				
			break;

			case 'eft-configuration':
				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/organization/eft-configuration.css').'">'; 
			break;

			case 'attendance' :
				// load the external css file 
				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/formTabStyle.css?v=4').'">';
				echo '<script src="https://cdn.jsdelivr.net/npm/lazysizes@5.2.0/lazysizes.min.js" async></script>';
				echo '<script src="https://cdn.jsdelivr.net/npm/exceljs@4.3.0/dist/exceljs.min.js"></script>';
				echo '<link href="'.$this->baseUrl('assets/global/css/employees/employees.css?v=4').'" rel="stylesheet">';
			break;
			case 'timesheets' :
				echo '<script src="https://cdn.jsdelivr.net/npm/exceljs@4.3.0/dist/exceljs.min.js"></script>';
				echo '<link href="'.$this->baseUrl('assets/global/css/employees/employees.css?v=4').'" rel="stylesheet">';
				break;
			case 'short-time-off':
				echo '<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.10.6/moment.min.js"></script>';
				echo '<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.43/js/bootstrap-datetimepicker.min.js"></script>';
				echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.43/css/bootstrap-datetimepicker.min.css' rel='stylesheet'>";
				echo '<link href="'.$this->baseUrl('assets/global/css/employees/short-time-off.css?v=1').'" rel="stylesheet">';
				echo '<script src="https://cdn.jsdelivr.net/npm/exceljs@4.3.0/dist/exceljs.min.js"></script>';
				echo '<link href="'.$this->baseUrl('assets/global/css/employees/employees.css?v=4').'" rel="stylesheet">';
				break;
			case 'attendance-finalization' : 			
				// log in employee id
				echo '<script>localStorage.setItem("LoginEmpId", '.$empId.');</script>';   

				// organization code
				echo '<script>localStorage.setItem("orgCode", "'.$orgCode.'");</script>';   
				// load the external css file 
				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/vueFormStyles.css').'">'; 
				// vue component style tags
				echo '<link rel="stylesheet" href="'. $this->baseUrl('vue/assets/css/VueCommon.css?v=2').'">'; 
				echo '<link rel="stylesheet" href="'. $this->baseUrl('vue/assets/css/customTooltip.css?v=3').'">'; 
				echo '<link rel="stylesheet" href="'. $this->baseUrl('vue/assets/scss/dataTableStyle.scss?v=2').'">';
				echo '<script src="'. $this->baseUrl('vue/components/customModal.vue.js?v=7').'"></script>';
				echo '<script src="'. $this->baseUrl('vue/components/customDeleteConfirmationModal.vue.js?v=7').'"></script>'; 
				echo '<script src="'. $this->baseUrl('vue/components/customNotesList.vue.js?v=1').'"></script>'; 
				echo '<script src="'. $this->baseUrl('vue/components/alertModal.vue.js?v=2').'"></script>';

				echo '<script src="'. $this->baseUrl('vue/components/accessDeniedScreen.vue.js?v=2').'"></script>';
				echo '<script src="'. $this->baseUrl('vue/components/customLoading.vue.js?v=2').'"></script>';
				echo '<script src="'. $this->baseUrl('vue/components/customSnackBar.vue.js?v=3').'"></script>'; 
				echo '<script src="'. $this->baseUrl('vue/components/noRecordInitialScreen.vue.js?v=5').'"></script>'; 
			break;
			case 'ip-whitelisting':
				echo '<script src="'. $this->baseUrl('vue/custom/custom.vue.js?v=2').'"></script>';
				// organization code
				echo '<script>localStorage.setItem("orgCode", "'.$orgCode.'");</script>';   

				//set ats base url values for get auth token in local storage variable
				echo '<script>localStorage.setItem("LoginEmpId", '.$empId.');</script>';
				// vue components scripts tags
				echo '<script src="'. $this->baseUrl('vue/views/ip-whitelisting/ipAddressViewCard.vue.js?v=1').'"></script>'; 
				echo '<script src="'. $this->baseUrl('vue/components/confirmationModel.vue.js?v=2').'"></script>'; 
				echo '<script src="'. $this->baseUrl('vue/components/noRecordInitialScreen.vue.js?v=5').'"></script>'; 
				echo '<script src="'. $this->baseUrl('vue/components/customLoading.vue.js?v=2').'"></script>'; 
				echo '<script src="'. $this->baseUrl('vue/components/accessDeniedScreen.vue.js?v=2').'"></script>'; 
				echo '<script src="'. $this->baseUrl('vue/components/fetchingErrorScreen.vue.js?v=4').'"></script>'; 
				echo '<script src="'. $this->baseUrl('vue/views/ip-whitelisting/addIpWhitlistingForm.vue.js?v=3').'"></script>'; 
				echo '<script src="'. $this->baseUrl('vue/views/ip-whitelisting/editIpWhitelistingForm.vue.js?v=3').'"></script>'; 
				echo '<script src="'. $this->baseUrl('vue/components/custom?v=1').'"></script>'; 
	
				echo '<link rel="stylesheet" href="'. $this->baseUrl('assets/global/css/vueFormStyles.css').'">'; 
				echo '<link rel="stylesheet" href="'. $this->baseUrl('vue/assets/css/VueCommon.css?v=2').'">'; 
				echo '<link rel="stylesheet" href="'. $this->baseUrl('vue/assets/css/customTooltip.css?v=3').'">'; 

			break;


		}
	}
	?>
	
	<!--  fix for scroll bar not appearing in side menu -->
	<script src="https://sdk.amazonaws.com/js/aws-sdk-2.32.0.min.js"></script>
	
	<script>
		$.material.init();
		
    </script>
	<script>
		
		jQuery('.svg').each(function() {
			var $img = jQuery(this);
			var imgID = $img.attr('id');
			var imgClass = $img.attr('class');
			var imgURL = $img.attr('src');
			
			jQuery.get(imgURL, function(data) {
				// Get the SVG tag, ignore the rest
				var $svg = jQuery(data).find('svg');
			
				// Add replaced image's ID to the new SVG
				if(typeof imgID !== 'undefined') {
					$svg = $svg.attr('id', imgID);
				}
				// Add replaced image's classes to the new SVG
				if(typeof imgClass !== 'undefined') {
					$svg = $svg.attr('class', imgClass+' replaced-svg');
				}
				
				// Remove any invalid XML tags as per http://validator.w3.org
				$svg = $svg.removeAttr('xmlns:a');
				
				$svg = $svg.attr('width','100');
				$svg = $svg.attr('height','100');
				
				// Check if the viewport is set, if the viewport is not set the SVG wont't scale.
				if(!$svg.attr('viewBox') && $svg.attr('height') && $svg.attr('width')) {
					$svg.attr('viewBox', '0 0 ' + $svg.attr('height') + ' ' + $svg.attr('width'));
				}
				
				// Replace image with new SVG
				$img.replaceWith($svg);
				
			}, 'xml');
		});
	</script>
	
	<!-- warning user about accuracy of geo co-ordinate -->
	<div class="modal fade" id="geoCoordinateAccuracyWarningModal" aria-hidden="true">
		<div class="modal-dialog geo-coordinates-modal">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true" style="color: var(--primary-color)">
						<i class="icons-office-52" id="geoCoordinateAccuracyWarningModalClose" style="font-size: 14px"></i>
					</button>
				</div>
				<div class="modal-body" style="text-align:center">
					<div style="justify-content: center; display: flex;">
						<img style="width: 300px" alt="warning-img-geo-accuracy" class="lazyload"
						onerror="this.onerror=null; this.src='<?php echo $this->baseUrl('images/geo-accuracy-warning-img.png'); ?>'" 
						src="<?php echo $this->baseUrl('images/geo-accuracy-warning-img.webp'); ?>" />
					</div>
					<div class="geo-coordinates-alert-content" id="geoCoordinatesAlertContent">
					</div>
					<button type="submit" class="btn btn-secondary btn-embossed" id="proceedGeoAccuracyBtn">
						Proceed Now
					</button>
					<br>
				</div>
			</div>
		</div>
	</div>

	<!-- IP address whitelisted warning page  -->
	<div id="ipAddressWhitelistingWarningModal" style="display:none">
		<div style=" background-color: rgb(39, 15, 42); color: beige; position: fixed; height: 100%; width: 100%; 
    		z-index: 5000; top: 0; left: 0; float: left; text-align: center; overflow-y : scroll">
			<div style="margin-top: 10%;">
				<div style="text-align:center">
					<div style="justify-content: center; display: flex;">
						<img alt="ip-address" height="350" class="lazyload" 
							onerror="this.onerror=null; this.src='<?php echo $this->baseUrl('assets/global/css/auth/ip-address-restriction.png'); ?>'"
							src="<?php echo $this->baseUrl('assets/global/css/auth/ip-address-restriction.webp'); ?>" />	
					</div>
					
					<div style="max-width: 700px; margin: 50px auto;">
						<h3 class="network-error-msg" id="error-msg1" style="text-align: center;">
							A network change was detected. Your current IP address is not whitelisted for application access. Please talk to your HR administrator or access from an authorized network.
						</h3>
					</div>

					<button type="submit" class="btn btn-secondary btn-lg btn-embossed" id="refreshIpAddress">
						Refresh
					</button>
				</div>
			</div>
		</div>
	</div>
    
	<input type="hidden" id="backbuttonrefresh" value="no">
	<!--<div class="pageLoading">Page Mask During Ajax Calls</div>	-->
	<!--  Included for corona awarness chatbot -->
	
	<?php if($domainDetails['Chat_Bot']==1) { ?>
		<!-- <script type="text/javascript" src="https://easychat.allincall.in/files/deploy/embed_chatbot_403.js"></script> -->
	<?php } ?>

	<!-- disabling quickbots temporarily as it takes more loading time -->
	<!-- <?php if($domainDetails['Chat_Bot']==1) { ?>
	<html>
		<head>
			<script>
				window.tcAsyncInit = function (a) {
					var userid = "anon:" + Math.floor(Math.random() * 100000); 
					var url = location.hostname;  // You will get this value inside bot code as event.senderobj.display 
					if (!(screen.width <= 640) || !(window.matchMedia && window.matchMedia('only screen and (max-width: 640px)').matches)) {
						a.init({
						botkey: "639b7924-6a72-4913-9a8d-c92377c68727",
						version: "3",
						appUISetting:"%7B%22openMode%22%3A%7B%22text%22%3A%22HR%20Bot%20-%20Alpha%20Release%22%2C%22height%22%3A390%2C%22width%22%3A320%7D%2C%22closeMode%22%3A%7B%22text%22%3A%22Talk%20to%20Bot%22%2C%22width%22%3A250%2C%22displayType%22%3A%22circular%22%7D%2C%22common%22%3A%7B%22logo%22%3A%22//www.buildquickbots.com/widget/bots/767ac5cf4800e0da009b8c047ca4037c/4ee2f9f9bf095502c868bdede92c0698/logo.png%22%2C%22imgDispType%22%3A%22rectangular%22%2C%22bgColor%22%3A%22%23ffffff%22%2C%22fontSize%22%3A16%2C%22fontColor%22%3A%22%23666666%22%2C%22textColor%22%3A%22%23565656%22%7D%2C%22content%22%3A%7B%22bot%22%3A%22//www.buildquickbots.com/widget/bots/767ac5cf4800e0da009b8c047ca4037c/4ee2f9f9bf095502c868bdede92c0698/bot.png%22%2C%22botBubbleColor%22%3A%22%23deeff0%22%2C%22botFontColor%22%3A%22%23666666%22%2C%22user%22%3A%22https%3A//www.gupshup.io/images/botwidget/ic_user.png%22%2C%22userBubbleColor%22%3A%22%23deeff0%22%2C%22userFontColor%22%3A%22%23666666%22%2C%22pageTitle%22%3A%22HR%20Bot%22%2C%22fontSize%22%3A14%2C%22fontFamily%22%3A%22sans-serif%22%7D%2C%22config%22%3A%7B%22persistenceMenu%22%3A%22%22%2C%22perMenuImg%22%3A%22%22%2C%22attachment%22%3A%22Yes%22%2C%22widgetType%22%3A%22Text%22%2C%22msgEnc%22%3A%22No%22%2C%22webView%22%3A%22Yes%22%2C%22RDStatus%22%3A%22No%22%2C%22isResponsive%22%3A%22Yes%22%2C%22allowHtmlFromBot%22%3A%22No%22%7D%7D",
						userObj: {
						"roomid": userid,
						"sender": {
						"id": userid,
						"name": url
						},
						"recipient": {
						"id": userid,
						"name": "Bot"
						} 
						}
						});
					}
				};
			</script>
		</head>
		<body>
			<script id="gs-sdk" src='//www.buildquickbots.com/botwidget/v3/demo/static/js/sdk.js?v=3' key="639b7924-6a72-4913-9a8d-c92377c68727" callback="tcAsyncInit" ></script>
		</body>
	</html>
	<?php } ?> -->
</body>
</html>
