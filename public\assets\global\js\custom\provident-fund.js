/*========================================================================================*/
/*========================================================================================*/
/* Program        : provident-fund.js													  */
/* Property of Caprice Technologies Pvt Ltd,
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,
 * Coimbatore, Tamilnadu, India.														  */
/* All Rights Reserved.            														  */
/* Use of this material without the express consent of Caprice Technologies
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law.  */
/*                                                                                    	  */
/* Description    :jquery for provident fund											  */
/*                                                                                   	  */
/*                                                                                    	  */
/*Revisions      :                                                                    	  */
/*Version    Date           Author                  Description                        	  */
/*0.1        30-May-2013    Suganya    			Initial Version         			      */
/*                                                                                        */
/*========================================================================================*/
/*========================================================================================*/
$(function () {
    $.fn.dataTable.ext.errMode = 'none';
    
    /* Alert when the DataSetup is not completed */
    dataSetup('datasetup');
    
    var isDirtyFormPfPaymentTracker = false,
        isDirtyFormProvidentFund    = false,
        isDirtyCopyProvidentFund    = false;
        
    $.validator.addMethod("checkPastDate", function (value, element) {
        if (Number($('#formPfPaymentTrackerId').val()) == 0 && (new Date(tzDate()).setHours(0,0,0,0) > new Date($("#formPfPaymentTrackerPaymentDate").datepicker("getDate")).setHours(0,0,0,0))) {
            return false;
        }
        
        return true;
    });
    
    /************************************* Provident Fund Grid ********************************************/
    
    /** Create Pf Contribution Grid**/ 
    var tableProvidentFund = $('#tableProvidentFund').dataTable({
        "lengthMenu"     : [ 5, 10, 25, 50, 100 ], 
        "iDisplayLength" : 10,
        "bDestroy"       : true,
        "bAutoWidth"     : false,
        "bServerSide"    : true,
        "bDeferRender"   : true,
        "sServerMethod"  : "POST", 
        "sAjaxSource"    : pageUrl() + "payroll/provident-fund/show-providentfund",
        "sAjaxDataProp"  : "aaData",
        "aaSorting"      : [[1, 'asc']],
        "aoColumnDefs"   : [{"targets": 0, "orderable": false},
                            { "sClass" : "visible-xs visible-sm  visible-md hidden-lg", "aTargets" : [0] },
                            { "sClass" : "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets" : [1] },
                            { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [4] },
                            { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [5] },
                            { "sClass" : "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets" : [6] },
                            { "sClass" : "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets" : [7] },
                            { "sClass" : "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets" : [8] },
                            { "sClass" : "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets" : [9] }],
        "fnCreatedRow": function( nRow, aData, iDataIndex ) {
            $(nRow).attr({"data-toggle":"context", "data-target":"#providentfund-context-menu" });
        },
        "aoColumns"      :[{
            "mData" : function (row, type, set) {
                return '<i class="fa fa-plus-square-o"></i>';
            }
        },
        {
            "mData"  : function (row, type, set) {                
                return '<div >'+ fnCheckNull(row['User_Defined_EmpId']) +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {                
                return '<div >'+ row['Coverage'] +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                return '<div class="text-center" >'+ fnCheckNull(row['Salary_Type']) +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                //var employeeName = (row['Employee_Name']==null || row['Employee_Name']=="")? '-' : row['Employee_Name'];
                             
                return '<div >'+ fnCheckNull(row['Employee_Name']) +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                var employeeShareAmount = (row['Employee_Share_Amount']==null || row['Employee_Share_Amount']=="")? '-' : row['Employee_Share_Amount'];
                             
                return '<div class="text-center" >'+ employeeShareAmount +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                var companyShareAmount = (row['Company_Share_Amount']==null || row['Company_Share_Amount']=="")? '-' : row['Company_Share_Amount'];
                             
                return '<div class="text-center" >'+ companyShareAmount +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                var employeeSharePercent = (row['Employee_Share']==null || row['Employee_Share']=="")? '-' : row['Employee_Share'];
                             
                return '<div class="text-center" >'+ employeeSharePercent +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {                
                var companySharePercent = (row['Company_Share']==null || row['Company_Share']=="")? '-' : row['Company_Share'];
                
                return '<div  class="text-center" >'+ companySharePercent +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                var salaryLimit = (row['Salary_Limit']==null || row['Salary_Limit']=="")? '-' : row['Salary_Limit'];
                return '<div>'+ salaryLimit +'</div>';
            }
        }]
    });
    
    
    //On + icon click in mobile & tablet view
    $(document).on('click', '#tableProvidentFund i', function () {
        var nTr = $(this).parents('tr')[0];
        
        fnFilterClose ('ProvidentFund');
        
        if ( tableProvidentFund.fnIsOpen(nTr) )
        {
            /* This row is already open - close it */
            $(this).removeClass().addClass('fa fa-plus-square-o');
            tableProvidentFund.fnClose(nTr);
        }
        else
        {
            var record = tableProvidentFund.fnGetData( nTr );
            var nRow =  $('#tableProvidentFund thead tr')[0];
            
            /* Open this row */
            $(this).removeClass().addClass('fa fa-minus-square-o');
            
            valueArray = [];
            headerArray=[];
        
            valueArray.Value_One = record.Employee_Name;
            valueArray.Value_Two = record.Employee_Share_Amount;
            valueArray.Value_Three = record.Company_Share_Amount;
            valueArray.Value_Four = record.Employee_Share;
            valueArray.Value_Five = record.Company_Share;
            valueArray.Value_Six = record.Salary_Limit;
            
            //get grid headers
            gridHeader(nRow.cells);
            
            tableProvidentFund.fnOpen(nTr, fnDeviceColumnDetails(headerArray,valueArray), 'details hidden-lg');
        }
    });
    
    /*  Add event listener for select and unselect details  */
    $(document).on('click contextmenu', '#tableProvidentFund tbody td div', function () {
        fnFilterClose ('ProvidentFund');
        
        var selectRow = $(this).parent().parent();
        
        tableProvidentFund.$('tr.row_selected').removeClass('row_selected');
        
        if (!selectRow.hasClass('row_selected'))
        {
            selectRow.addClass('row_selected');
            
            fnProvidentFundActionButtons (true);
        }
        else
        {
            fnProvidentFundActionButtons (false);
        }
    });
    
    //** Refresh The ProvidentFund Grid**/ 
    $('#gridPanelProvidentFund .panel-reload').on('click', function () {
        fnRefreshTable (tableProvidentFund);
    });
    
    /**
     *  close filter form and clear enabled buttons based on selection record
     *  while search all, sorting, pagination, redraw events
     *  it will work in search.dt order.dt page.dt, length.dt those events
    */
    $('#tableProvidentFund').on( 'draw.dt', function () {        
        fnProvidentFundActionButtons (false);

        fnFilterClose ('ProvidentFund');
    });
    
    /** View Provident Fund Form **/
    $('#viewProvidentFund,#viewContextProvidentFund').on('click', function () {       
        var selectedRow = fnGetSelected (tableProvidentFund);
        
        fnFilterClose ('ProvidentFund');
       
        if (selectedRow.length)
        {
            var record = tableProvidentFund.fnGetData(selectedRow[0]);
            
            if (record.PfOrg_Id > 0 || record.EmployeeId > 0 )
            {            
                $('#modalFormProvidentFund .modal-title').html("<strong>View<strong> "+$('#lblFormNameA').html());
                
                $('#viewPfCoverage').text(record.Coverage);
                $('#viewAllowEpfExcessContribution').text((record.Allow_Epf_Excess_Contribution == 1 ? 'Yes': 'No'));
                $('#viewAllowEpsExcessContribution').text((record.Allow_Eps_Excess_Contribution == 1 ? 'Yes': 'No'));
                $('#viewAdminChargePartOfCTC').text((record.Admin_Charge_Part_Of_Ctc == 1 ? 'Yes': 'No'));
                $('#viewEdliChargePartOfCTC').text((record.Edli_Charge_Part_Of_Ctc == 1 ? 'Yes': 'No'));
                if (record.Company_Share_Amount==null && record.Employee_Share_Amount==null ) {                   
                    $('#viewPfEmployeeShare').html(fnCheckNull (record.Employee_Share));
                    $('#viewPfCompanyShare').html(fnCheckNull (record.Company_Share));
                }
                else if ( record.Company_Share==null && record.Employee_Share==null ) {
                    $('#viewPfEmployeeShare').html(fnCheckNull (record.Employee_Share_Amount));
                    $('#viewPfCompanyShare').html(fnCheckNull (record.Company_Share_Amount));                
                }
                
                if (record.Coverage == 'Organization') {                    
                    $('#viewPfSalaryType').text(record.Salary_Type);
                    $('#viewPfStatutoryLimit').text(record.Salary_Limit);
                    $('#viewPfStatutorySalary').html(record.Statutory_Salary_Limit);                    
                   
                    var statutoryComparison = (record.Statutory_Limit_Comparison == 1 ) ? 'Greater than statutory Limit' : 'Less than or Equal to Statutory Limit';
                    $('#viewPfStatutoryLimitCompare').text(statutoryComparison);
                    
                    $('.employeeBasedHidden').hide();                    
                    $('.companyBasedHidden').show();
                    
                    if(record.Salary_Limit == 'No'){    
                        $('.SSalaryBasedHidden').hide();
                        $('.viewAllowEpsEpfExcessContribution').show();
                    }
                    else{
                        $('.SSalaryBasedHidden').show();
                        if(record.Statutory_Limit_Comparison == 1){
                            $('.viewAllowEpsEpfExcessContribution').show();
                        }
                        else
                        {
                            /*hide the EPS and EPF view field only if 'Define Statutory Salary Limit' flag is enabled and
                             'Statutory Limit Comparison' is 'Less than or Equal to Statutory Limit' */
                            $('.viewAllowEpsEpfExcessContribution').hide();     
                        }
                    }
                }
                else
                {                    
                    $('#viewPfEmployeeName').text(record.Employee_Name);
                    $('.companyBasedHidden').hide();
                    $('.SSalaryBasedHidden').hide();
                    $('.employeeBasedHidden').show();                    
                    $('.viewAllowEpsEpfExcessContribution').show();
                }
                
                $('#viewPfDescription').text(fnCheckNull(record.Description));
                $('#viewPfAdminChargePercent').text(fnCheckNull(record.Admin_Charge));
                $('#viewPfAdminMaxAmt').text(fnCheckNull(record.Admin_Charge_Max_Amount));
                $('#viewPfEDLIConfiguration').text(fnCheckNull(record.EDLI_Configuration_Employer));
                $('#viewPfEDLIMaxAmt').text(fnCheckNull(record.EDLI_Charge_Max_Amount));
                /**
                 *  Prefill addional information values based on record with form name
                */
                fnPreFillAdditionalPanel ('ProvidentFund', record);                              
                
                $('#editFormProvidentFund').hide();
                $('#formActionProvidentFund').hide();
                $('#viewFormProvidentFund,#editInViewProvidentFund').show();
                $('#modalFormProvidentFund').modal('toggle');
                
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+$('#lblFormNameA').html()+' record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameA').html()+' record', type : 'info' });
        }
    });
    
     /** 
     * Department wise segregation 
    */  
   $('#formDivisionDetails').on('change', function () { 
        var formDivisionId = $('#s2id_formDivisionDetails').select2('val'); 
        hrappDepartmentClassification(formDivisionId, tableProvidentFund,''); 
    }); 


    /** Add Provident Fund Form **/
    $('#addProvidentFund').on('click', function () {
        tableProvidentFund.$('tr.row_selected').removeClass('row_selected');
        
        fnProvidentFundActionButtons (false);
        fnFilterClose ('ProvidentFund');       
        
        $('#modalFormProvidentFund .modal-title').html("<strong>Add</strong> "+$('#lblFormNameA').html());

        fnPreFillFormValuesProvidentFund ('');        
        
        $('#viewFormProvidentFund, #editInViewProvidentFund').hide();
        $('#editFormProvidentFund, #formActionProvidentFund').show();
    });
 
    /** Edit Provident Fund Form **/
    $('#editProvidentFund, #editInViewProvidentFund, #editContextProvidentFund').on('click', function () {
        var button      = $(this).prop('id');
        var selectedRow = fnGetSelected (tableProvidentFund);
       
        fnFilterClose ('ProvidentFund');
        
        if (selectedRow.length)
        {
            var record       = tableProvidentFund.fnGetData(selectedRow[0]);
            var pfOrgId      = record.PfOrg_Id;
            var pfEmployeeId = record.EmployeeId;
            var uniqueId = (pfOrgId > 0 && !isNaN(pfOrgId)) ? pfOrgId : pfEmployeeId;
            var formName = (pfOrgId > 0 && !isNaN(pfOrgId)) ? 'Provident Fund' : 'Emp Provident Fund';
            
            if ((pfOrgId > 0 && !isNaN(pfOrgId)) || (pfEmployeeId > 0 && !isNaN(pfEmployeeId)))
            {
                setLock({
                    'formName'  : formName,
                    'uniqueId'  : uniqueId,
                    'callback'  : function(result)
                    {
                        if (button != 'editInViewProvidentFund')
                            $('#modalFormProvidentFund').modal('toggle');
                        
                        $('#modalFormProvidentFund .modal-title').html("<strong>Edit</strong> "+$('#lblFormNameA').html());
                        
                        fnPreFillFormValuesProvidentFund (record);                        
                        
                        $('#editInViewProvidentFund,#viewFormProvidentFund').hide();
                        $('#editFormProvidentFund, #formActionProvidentFund').show();
                       
                    }
                });
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+$('#lblFormNameA').html()+' record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameA').html()+' record', type : 'info' });
        }
    });    
    
     /** Change Event For Provident Fund Form **/
    $('#editFormProvidentFund').on('change', function () {        
        isDirtyFormProvidentFund = true;
    });
    
    /** Change event for coverage combo field **/
    $('#formCoverage').on('change', function () {
        var coverage = $(this).select2('val');        
        
        $("#formCompanyShare,#formEmployeeShare").addClass('vRequired');
        
        if (coverage == 'O') {
            $('.formCoverageEmpHidden').hide();
            $('.formCoverageOrgHidden').show();            
            
            $("#formSalaryType,#formStatutorySalaryLimit,#formStatutoryLimitCompare").addClass('vRequired');
            
            $('#formEmployeeName').select2('val','');
            $("#formEmployeeName").removeClass('vRequired');
        }
        else
        {
            $('.formCoverageEmpHidden').show();
          //  fnGetEmployeeName('Provident Fund','#formEmployeeName');
            $('.formCoverageOrgHidden').hide();
            $('.formStatutoryLimitHidden').hide();
           
            $("#formEmployeeName").addClass('vRequired');
            $("#formSalaryType,#formStatutorySalaryLimit,#formStatutoryLimitCompare").removeClass('vRequired');
            
            $('#formDefineStatutorySalary').prop('checked', false);
            $('.editAllowEpsEpfExcessContribution').show();             
        }
        
        /** While add, ORG coverage only will be checked that it is already exists or not. While choose employee name only , employee coverage will be checked.**/
        if (coverage == 'O' && $('#PfOrgId').val() == 0) {
            $.ajax ({
                type     : 'POST',
                async    : false,
                dataType : "json",
                url      : pageUrl ()+'payroll/provident-fund/check-pf-exists/coverage/O/checkExists/1', 
                success  : function(result)
                {                        
                    if (result !== '' && result !== null && result > 0) {
                        /** Confirmation msg will be asked here. If it is confirmed means org pf details will be prefilled.**/
                        $('#modalPFExistsConfirmation #modalPFExistsMsg').html("PF already exists for organization coverage. Are you sure you want to edit it?");
                        
                        $('#modalPFExistsConfirmation').modal('toggle');
                    }
                }
            });
        }
    });
    $('#formStatutoryLimitCompare').on('change', function () {
        var statutoryLimitCompare = $(this).select2('val');
        if(statutoryLimitCompare == 1){
            $('.editAllowEpsEpfExcessContribution').show();     /*show the EPS and EPF edit field if 'Statutory Limit Comparison' is 'Greater than statutory Limit' */
            $('#formAllowEpfExcessContribution,#formAllowEpsExcessContribution').prop('checked', false);
        } else {
            $('.editAllowEpsEpfExcessContribution').hide();     /*hide the EPS and EPF edit field if 'Statutory Limit Comparison' is 'Less than or Equal to Statutory Limit' */
            $('#formAllowEpfExcessContribution,#formAllowEpsExcessContribution').prop('checked', false);
        }
    });
    /** Change event for contribution combo field **/
    $('#formEmployeeName').on('change', function () {
        /** While add only it will be checked that emp pf exists or not **/
        if($('#EmployeeId').val() == 0)
        {
            $.ajax ({
                    type     : 'POST',
                    async    : false,
                    dataType : "json",
                    url      : pageUrl ()+'payroll/provident-fund/check-pf-exists/coverage/E/checkExists/1/empId/'+$('#formEmployeeName').select2('val'), 
                    success  : function(result)
                    {
                        if (result !== '' && result !== null) {
                            if (result.PfId > 0) {
                                /** Confirmation msg will be asked here. If it is confirmed means emp pf details will be prefilled.**/
                                $('#modalPFExistsConfirmation #modalPFExistsMsg').html("PF already exists for"+result.Emp_Name.Emp_First_Name+" "+result.Emp_Name.Emp_Last_Name+".Are you sure you want to edit it?");
                                
                                $('#modalPFExistsConfirmation').modal('toggle');
                            }
                        }
                    }
            });
        }
    });
    
    /** Org/Emp pf details will be prefilled.**/
    $('#editExistsPf').on('click', function () {
        /** While add if the org/emp pf already exists means, confirmation message will be asked. If it is confirmed means,
        pf details will prefill**/
        $.ajax ({
                type     : 'POST',
                async    : false,
                dataType : "json",
                url      : pageUrl ()+'payroll/provident-fund/check-pf-exists/coverage/'+$('#formCoverage').select2('val')+'/checkExists/0/empId/'+$('#formEmployeeName').select2('val'), 
                success  : function(record)
                {
                    if (record !== '' && record !== null) {
                        if (record.PfId > 0) {            
                            var formName = ($('#formCoverage').select2('val') == 'O') ? 'Provident Fund' : 'Emp Provident Fund';
                            
                            /** Set lock for the record **/
                            setLock({
                                'formName'  : formName,
                                'uniqueId'  : record.PfId,
                                'callback'  : function(result)
                                {
                                    /** Prefill the field values **/
                                    fnPreFillFormValuesProvidentFund (record.Pf_Details);
                                }
                            });                                
                        }
                    }
                }   
        });
    });
    /** Change event for contribution combo field **/
    $('#formContribution').on('change', function () {
        $('#formCompanyShare, #formEmployeeShare').val('');
        
        if ($(this).val() == 0) {
            $("#formCompanyShare, #formEmployeeShare").prop({
                 "min" : 0 ,
                 "max" : 9999999999999
            });            
        }
        else
        {
            $("#formCompanyShare, #formEmployeeShare").prop({
                 "min" : 0,
                 "max": 100
            });
            
            /** to check the company and employee share percent**/
            //fntotalPfShare();            
        }        
        $('#formCompanyShare, #formEmployeeShare').addClass('vRequired');
    });
    
    /** Change event for company share and employee share field in add,Edit form and copy form for emppf **/
    $('#formCompanyShare,#formEmployeeShare,#formCopyCompanyShare,#formCopyEmployeeShare').on('change keyup', function (e) {        
        if($('#formContribution').val() == 1 || ($("#formCopyContribution").val == 1)){            
            fntotalPfShare();
        }
        else
        {
            $('#formCompanyShare,#formEmployeeShare').removeClass("percentageError");
            $('#formCompanyShare,#formEmployeeShare').prop({"min":0,"max":9999999.99});
        }
    });
    
    /** Change event for Salary Type Combo field **/
    $('#formSalaryType').on('change', function () {        
        if ($('#formDefineStatutorySalary')[0].checked ==  true) {
            $('#formDefineStatutorySalary').prop('checked', false);
            //$('.formStatutoryLimitHidden').hide();
            $('#formDefineStatutorySalary').trigger('change');
        }
        else{
            $("#formStatutorySalaryLimit,#formStatutoryLimitCompare").removeClass('vRequired');
        }
    });
    
     /** Change event for Statuatory Salary Toggle field **/
    $('#formDefineStatutorySalary').on('change', function () {
        var coverage = $('#s2id_formCoverage').select2('val');
        var salarytype = $('#s2id_formSalaryType').select2('val');
        var statuatorySalary = $('#formStatutorySalaryLimit').val();
        
         if($('#formDefineStatutorySalary')[0].checked == false)
         {
            $("#formStatutorySalaryLimit,#formStatutoryLimitCompare").removeClass('vRequired');
            $("#s2id_formStatutoryLimitCompare").select2('val', '');
         }
         else
         {
            $("#formStatutorySalaryLimit,#formStatutoryLimitCompare").addClass('vRequired');
         }
        
        if(/*$('#formDefineStatutorySalary')[0].checked == false &&*/ salarytype != '')
        {            
            $.ajax ({
                    type     : 'POST',
                    dataType : 'json',
                    async    : false,
                    url      : pageUrl () +'payroll/provident-fund/pf-existence',
                    data     : { salaryType : salarytype },
                    success  : function (response)
                    {
                        if(response != undefined)
                        {                            
                            //while edit org pf
                            if($('#PfOrgId').val() != 0)
                            {
                                if( $('#formDefineStatutorySalary')[0].checked == false )
                                {
                                    //Confirmation modal to cancel the statuatory salary limit
                                    $('#statuatorySalaryIgnoreConfirmation').modal('toggle');
                                    
                                    /** while click ok in confirmation modal **/
                                    $('#closestatuatorySalaryConfirm').on('click', function () {                               
                                        $('#statuatorySalaryIgnoreConfirmation').modal('hide');
                                        $('.editAllowEpsEpfExcessContribution').show();
                                        $('#formAllowEpfExcessContribution,#formAllowEpsExcessContribution').prop('checked', false);
                                        
                                        salLimitDefined();
                                    });
                                    //
                                    /**while click cancel in confirmation modal **/
                                    $('#closestatuatorySalaryCancel').on('click', function () {                                    
                                        $('#formDefineStatutorySalary').prop('checked', true);
                                        
                                        $('#statuatorySalaryIgnoreConfirmation').modal('hide');
                                    });
                                }                                
                            }
                            else
                            {                                
                                //while add org pf
                                if( $('#formDefineStatutorySalary')[0].checked == false )
                                {
                                    $("#formStatutorySalaryLimit").val(0);                       
                                    $("#formStatutorySalaryLimit").prop('readOnly', false);
                                    $("#s2id_formStatutoryLimitCompare").select2('val', '');
                                    $(".formStatutoryLimitHidden").hide();                                    
                                    $('.editAllowEpsEpfExcessContribution').show();
                                    $('#formAllowEpfExcessContribution,#formAllowEpsExcessContribution').prop('checked', false);
                                }
                                else
                                {
                                    $("#formStatutorySalaryLimit").val(response[0]);
                                    $("#formStatutorySalaryLimit").prop('readOnly','readOnly');
                                    $("#s2id_formStatutoryLimitCompare").select2('val', 1);
                                    $(".formStatutoryLimitHidden").show();
                                }                             
                            }
                        }
                        else 
                        {                            
                            salLimitDefined();
                        }
                            
                    }
                });
        }
        else
        {
            salLimitDefined();
        }
    });
    
    /** Submit Provident Fund Form **/
    $('#formSubmitProvidentFund').on('click', function () {
        var l      = Ladda.create(this);
        var record = tableProvidentFund.fnGetData(fnGetSelected(tableProvidentFund)[0]);
        
        l.start();
                
        
        if($('#formDefineStatutorySalary')[0].checked == false)
        {
            $("#formStatutorySalaryLimit,#formStatutoryLimitCompare").removeClass('vRequired');
        }
        else
        {
            $("#formStatutorySalaryLimit,#formStatutoryLimitCompare").addClass('vRequired');
        }
        
        $('#s2id_formStatutoryLimitCompare, #s2id_formSalaryType, #s2id_formContribution, #s2id_formEmployeeName,#s2id_formCoverage').removeClass('form-error');
        
        if (isDirtyFormProvidentFund)
        {
            if ($('#editFormProvidentFund').valid())
            {                
                var definestatuatorysalary = ($('#formDefineStatutorySalary')[0].checked==true)?1:0;
                if (definestatuatorysalary == 0 /*|| $('#formCoverage').val() == 'E'*/) {
                    $("#formStatutorySalaryLimit").val('');
                    $("#s2id_formStatutoryLimitCompare").select2('val', '');
                }
                
                $.ajax ({
                    type     : 'POST',
                    dataType : 'json',
    //                async    : false,
                    url      : pageUrl() + "payroll/provident-fund/update-providentfund",
                    data     : {                        
                        empHidden                        : $('#EmployeeId').val(),
                        Coverage                         : $('#s2id_formCoverage').select2('val'),
                        PfOrg_Id                         : $('#PfOrgId').val(),
                        Employee_Id                      : $('#s2id_formEmployeeName').select2('val'),
                        Fixed_Variable_Flag              : $('#s2id_formContribution').select2('val'),
                        Employee_Share                   : $('#formEmployeeShare').val(),
                        Company_Share                    : $('#formCompanyShare').val(),
                        Salary_Type                      : $('#s2id_formSalaryType').select2('val'),
                        Statutory_Sal_Limit_Defined_Flag : definestatuatorysalary,
                        Statutory_Salary_Limit           : $('#formStatutorySalaryLimit').val(),
                        Statutory_Limit_Comparison       : $('#s2id_formStatutoryLimitCompare').select2('val'),
                        AdminChargePercent               : $('#formAdminChargePercent').val(),
                        AdminChargeAmt                   : $('#formAdminChargeAmt').val(),
                        EdliPercent                      : $('#formEdliPercent').val(),
                        EdliAmount                       : $('#formEdliLimitAmt').val(),
                        AllowEpfExcessContribution       : ($('#formAllowEpfExcessContribution').is(':checked') == true ? 1 : 0),
                        AllowEpsExcessContribution       : ($('#formAllowEpsExcessContribution').is(':checked') == true ? 1 : 0),
                        AdminChargePartOfCTC             : ($('#formAdminChargePartOfCTC').is(':checked') == true ? 1 : 0),
                        EdliChargePartOfCTC              : ($('#formEdliChargePartOfCTC').is(':checked') == true ? 1 : 0),
                        Description                      : $('#formEditDescription').val()
                    },
                    success  : function(result)
                    {
                        if (result.success)
                        {
                            isDirtyFormProvidentFund = false;
                            
                            $('#modalFormProvidentFund').modal('toggle');
                            
                            fnRefreshTable (tableProvidentFund);
                            
                            jAlert ({ msg : result.msg, type : result.type });
                        }
                        else
                        {
                            jAlert ({ panel : $('#editFormProvidentFund'), msg : result.msg, type : result.type });
                        }
                        
                        l.stop();
                    }
                });
            }
            else
            {
                l.stop();                
            }
        }
        else
        {
            l.stop();
            jAlert ({ panel : $('#editFormProvidentFund'), msg : 'Form has no changes', type : 'info' });
        }
    });
    
    /** Reset The Values In Provident Fund Form **/
    $('#formResetProvidentFund').on('click', function () {
        var l = Ladda.create(this);
        
        l.start();
        
        if ($('#PfOrgId').val() > 0 || $('#EmployeeId').val() > 0)
        {            
            fnPreFillFormValuesProvidentFund (tableProvidentFund.fnGetData (fnGetSelected (tableProvidentFund)[0]));
        }
        else
        {         
            fnPreFillFormValuesProvidentFund ('');
        }
            
        l.stop();
    });
    
    //Trigger delete confirmation popup in delete menu in context menu
    $('#deleteContextProvidentFund,#deleteProvidentFund').on('click', function () {
        var selectedRow = fnGetSelected (tableProvidentFund );
        
        fnFilterClose ('ProvidentFund');
        
        if (selectedRow.length)
        {           
            var orgPfId = tableProvidentFund.fnGetData (selectedRow[0]).PfOrg_Id;
            var empPfId = tableProvidentFund.fnGetData (selectedRow[0]).EmployeeId;
         
            if ((orgPfId > 0 && !isNaN(orgPfId)) || (empPfId > 0 && !isNaN(empPfId)))
            {         
                $('#modalDeleteProvidentFund').modal('toggle');
            }
            else
            {             
                jAlert ({ msg : 'Kindly select '+$('#lblFormNameA').html()+' record', type : 'info' });
            }
        }
        else
        {            
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameA').html()+' record', type : 'info' });
        }
    });
  
    //Delete Provident Fund
    $('#deleteConfirmProvidentFund').on('click', function () {
        var selectedRow = fnGetSelected (tableProvidentFund );
        
        if (selectedRow.length)
        {
            var  orgPfIdExists = tableProvidentFund.fnGetData (selectedRow[0]).PfOrg_Id;
            var  empIdExists = tableProvidentFund.fnGetData (selectedRow[0]).EmployeeId;
            
            var emppfid = (!isNaN(empIdExists)) ? empIdExists : 0 ;
            var orgpfid = (!isNaN(orgPfIdExists)) ? orgPfIdExists : 0;            
            
            if ((orgpfid > 0 && !isNaN(orgpfid)) || (emppfid > 0 && !isNaN(emppfid))) 
            {
                $.ajax ({
                    type     : 'POST',
                    dataType : 'json',
                    async    : false,
                    url      : pageUrl () +'payroll/provident-fund/delete-providentfund',
                    data     : { orgPfId : orgpfid,
                                 empPfId : emppfid},
                    success  : function (result)
                    {
                        if (isJson (result))
                        {
                            if (result.success)
                            {
                                fnRefreshTable (tableProvidentFund);
                                
                                jAlert ({ msg : result.msg, type : result.type });
                            }
                            else
                            {
                                jAlert ({ msg : result.msg, type : result.type });    
                            }
                        }
                        else
                        {
                            sessionExpired ();
                        }
                    }
                });
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+$('#lblFormNameA').html()+' record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameA').html()+' record', type : 'info' });
        }
    });
    
    /** Close Provident Fund Form **/
    $('#closeProvidentFund').on('click', function () {
        isDirtyFormProvidentFund = false;
        
        fnFormCloseProvidentFund (true);
    });
    
    /** Hide The Provident Fund Modal **/
    $('#modalFormProvidentFund').on('hide.bs.modal', function (e) {
        if (isDirtyFormProvidentFund)
        {
            e.preventDefault();
            e.stopImmediatePropagation();
            
            $('#dirtyProvidentFund').modal('toggle');
        }
        else
        {            
            fnFormCloseProvidentFund (false);
        }
    });
    
    /** On Copy Provident Fund modal open **/
    $('#copyProvidentFund, #copyContextProvidentFund').on('click', function(){
        var selectedRow = fnGetSelected (tableProvidentFund );
        
        fnFilterClose ('Provident Fund');
         
        if (selectedRow.length)
        {
            var employeeId = tableProvidentFund.fnGetData (selectedRow[0]).EmployeeId;
            
            if (employeeId > 0)
            {
                setLock({
                    'formName'  : 'Emp Provident Fund',
                    'uniqueId'  : employeeId,
                    'callback'  : function(result)
                    {
                        $('#modalcopyProvidentFund').modal('toggle');
                        $('#formCopyEmployeeId').val(employeeId);
                        
                        fnresetcopyprovidentfund();
                        $('#formActionCopyPf').show();
                    }
                });
                
            }
            else
            {
                jAlert ({ msg : 'Please select the employee coverage record to do a bulkcopy', type : 'info' });
            }
        }
        else{
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameA').html()+' record', type : 'info' });
        }
    });
    
    /** Change Event For Copy Provident Fund Form **/
    $('#copyFormProvidentFund').on('change', function () {        
        isDirtyCopyProvidentFund = true;
    });
    
    // Copy Provident Fund reset
    $('#formResetCopyPf').on('click', function(){
        var l = Ladda.create(this);
        
        l.start();
       
        fnresetcopyprovidentfund();
        
        l.stop();
    });
    
    // Copy Provident Fund submit
    $('#formCopyProvidentFund').on('click',function(){
        var l = Ladda.create(this);
        l.start();
        
        if (isDirtyCopyProvidentFund)
        {            
            if ($("#copyFormProvidentFund").valid() && $('#s2id_formcopyEmployeeName').select2('val') != '')
            {
                $.ajax ({
                    type     : 'POST',
                    dataType : 'json',
                    async    : false,
                    dataType : "json",                
                    url      : pageUrl ()+'payroll/provident-fund/copy-providentfund/',
                    data     : {
                        Employee_Id    : $('#formCopyEmployeeId').val(),
                        CopyTo         : $('#s2id_formcopyEmployeeName').select2('val'),
                        Employee_Share : $('#formCopyEmployeeShare').val(),
                        Company_Share  : $('#formCopyCompanyShare').val(),
                        AdminChargePercent   : $('#formCopyAdminChargePercent').val(),
                        AdminChargeAmt : $('#formCopyAdminChargeAmt').val(),
                        EdliPercent    : $('#formCopyEdliPercent').val(),
                        EdliAmount     : $('#formCopyEdliLimitAmt').val(),
                        Description    : $('#formCopyDescription').val()
                    },
                    success  : function(result)
                    {
                        if (isJson (result))
                        {                    
                            if (result.success)
                            {
                                isDirtyCopyProvidentFund = false;
                                
                                $('#modalcopyProvidentFund').modal('toggle');
                                
                                fnRefreshTable(tableProvidentFund);
                                
                                jAlert({ msg : result.msg, type : result.type });
                            }
                            else
                            {                            
                                jAlert ({ panel : $('#copyFormProvidentFund'), msg : result.msg, type : result.type });
                            }
                        }
                        else
                        {
                            sessionExpired ();
                        }
                        
                        l.stop();
                    }
                });          
            }
            else
            {
                l.stop();                
            }
        }
        else
        {
            l.stop();
            jAlert({ panel : $('#copyFormProvidentFund'),msg : 'Form has no changes', type : 'info' });
        }
    });
    
    /**
     *  click to close copy modal
    */
    $('#closeCopyProvidentFund').on('click', function () {
        isDirtyCopyProvidentFund = false;
        
        fnFormCloseCopyProvidentFund(true);
    });
    
    /** Close Copy Provident Fund Form **/
    $('#modalCopyFormClose').on('click', function () {        
       fnCheckCopyFormIsDirty();
    });
    
    /** Hide The Copy Provident Fund Modal **/
    $('#modalcopyProvidentFund').on('hide.bs.modal', function (e) {
        if (isDirtyCopyProvidentFund)
        {   
            e.preventDefault();
            e.stopImmediatePropagation();
            
            $('#dirtyCopyProvidentFund').modal('toggle');
        }
        else
        {         
            fnFormCloseCopyProvidentFund (false);
        }
    });
    
     /** Show&Hide the Provident Fund Filter **/
    $('#filterProvidentFund,#closeFilterProvidentFund').on('click', function() {
        if ($('#filterPanelProvidentFund').hasClass('open'))
        {
            $('#filterPanelProvidentFund').removeClass('open');
            $('#filterPanelProvidentFund').hide();
        }
        else
        {
            $('#filterPanelProvidentFund').addClass('open');
            $('#filterPanelProvidentFund').show();
        }
    });
    
    /** Reset The Values In Provident Fund Filter **/
    $('#cancelProvidentFund,#closeFilterProvidentFund').on('click', function () {        
        $('#filterEmployeeName,#filterpfempShareStart,#filterpfempShareEnd,#filterpfcomShareStart').val('');
        $('#filterpfcomShareEnd,#filterpfempShareAmountStart,#filterpfempShareAmountEnd,#filterComShareAmountStart,#filterComShareAmountEnd').val('');
        $('#filterCoverage').select2('val','');
        
        tableProvidentFund.fnReloadAjax( pageUrl() +'payroll/provident-fund/show-providentfund' );
    });
    
     /** Apply The Values In Provident Fund Filter **/
    $('#applyProvidentFund').on('click', function () {
        filterCoverage                = $('#filterCoverage').val();
        filterEmployeeName            = $('#filterEmployeeName').val();      
        filterEmployeeShareStart      = $('#filterpfempShareStart').val();
        filterEmployeeShareEnd        = $('#filterpfempShareEnd').val();
        filterCompanyShareStart       = $('#filterpfcomShareStart').val();
        filterCompanyShareEnd         = $('#filterpfcomShareEnd').val();      
        filterEmpShareAmountStart     = $('#filterpfempShareAmountStart').val();
        filterEmpShareAmountEnd       = $('#filterpfempShareAmountEnd').val();      
        filterCompanyShareAmountStart = $('#filterComShareAmountStart').val();
        filterCompanyShareAmountEnd   = $('#filterComShareAmountEnd').val();
        
        tableProvidentFund.fnReloadAjax(pageUrl() +'payroll/provident-fund/show-providentfund'+
                                                  '/coverage/'+ filterCoverage +
                                                  '/employeeName/'+ filterEmployeeName +
                                                  '/employeeSharePercentStart/'+ filterEmployeeShareStart +
                                                  '/employeeSharePercentTo/'+ filterEmployeeShareEnd +
                                                  '/companySharePercentStart/'+ filterCompanyShareStart+
                                                  '/companySharePercentTo/'+ filterCompanyShareEnd +
                                                  '/employeeShareAmountStart/'+ filterEmpShareAmountStart+
                                                  '/employeeShareAmountTo/'+ filterEmpShareAmountEnd +
                                                  '/companyShareAmountStart/'+ filterCompanyShareAmountStart +
                                                  '/companyShareAmountTo/'+ filterCompanyShareAmountEnd);
    });
    
    /**
     *  While close provident fund form check form is dirty or not.
    */
    function fnCheckCopyFormIsDirty() {
        //If form is dirty show confirmation to close status update form
        if (isDirtyCopyProvidentFund)
        {            
            $('#modalcopyProvidentFund').modal('toggle');
        }
        //If form isn't dirty then close the add/edit form
        else    
        {            
            fnFormCloseCopyProvidentFund(true);
        }
    }
   
     /** Close Function For All the Forms **/ 
    function fnFormCloseProvidentFund (hideAction) {
        var pforgId = $('#PfOrgId').val();
        var pfEmployeeId = $('#EmployeeId').val();
        
        var uniqueId = (pforgId > 0 && !isNaN(pforgId)) ? pforgId : pfEmployeeId;
        var formName = (pforgId > 0 && !isNaN(pforgId)) ? 'Provident Fund' : 'Emp Provident Fund';
        
        if (uniqueId > 0 && !isNaN(uniqueId))
        {
            
            clearLock ({
                'formName' : formName,
                'uniqueId' : uniqueId,
                'callback' : function ()
                {                    
                    var field = (pforgId > 0 && !isNaN(pforgId)) ? $('#PfOrgId') : $('#EmployeeId');
                    field.val(0);
                    
                    if (hideAction)
                        $('#modalFormProvidentFund').modal('hide');
                }
            });
        }
        else
        {
            if (hideAction)
                $('#modalFormProvidentFund').modal('hide');
        }
    }    
    
    /** Close Function For Copy Provident Fund Forms **/ 
    function fnFormCloseCopyProvidentFund (hideAction) {        
        var pfEmployeeId = $('#formCopyEmployeeId').val();
        
        if (pfEmployeeId > 0 && !isNaN(pfEmployeeId))
        {
            clearLock ({
                'formName' : 'Emp Provident Fund',
                'uniqueId' : pfEmployeeId,
                'callback' : function ()
                {                    
                    $('#EmployeeId').val(0);
                    
                    if (hideAction)
                        $('#modalcopyProvidentFund').modal('hide');
                }
            });
        }
        else
        {
            if (hideAction)
                $('#modalcopyProvidentFund').modal('hide');
        }
    }
    
    /** Close Function For statuatory salary confirmation modal **/ 
    function fnFormCloseStatuatorySalary (hideAction) {
        if (hideAction)
                $('#statuatorySalaryIgnoreConfirmation').modal('hide');
    }
    
    /** set the min and max value to employee and compare share percent **/
    function fntotalPfShare()
    {   
        /** set the min and max value **/
        $('#formCompanyShare,#formEmployeeShare').prop({"min":0,"max":100});
        if($("#formCopyContribution").val() == 1)
        {
            $('#formCopyCompanyShare,#formCopyEmployeeShare').prop({"min":0,"max":100});
        }
        
        if (($('#formCompanyShare').val() !='' && $('#formEmployeeShare').val() != '') ||
           ($('#formCopyCompanyShare').val() !='' && $('#formCopyEmployeeShare').val() != ''))
        {
            if($("#formCopyContribution").val() == 1 /*&& $("#formCopyCompanyShare").val() != ''
               && $("#formCopyCompanyShare").val() != ''*/)
            {         
                /** check the sum of company share percent and employee share percent **/
                pfpercentage = (parseFloat($('#formCopyCompanyShare').val())+parseFloat($('#formCopyEmployeeShare').val()))>100;
            
                if (pfpercentage == true) {
                    $('#formCopyCompanyShare,#formCopyEmployeeShare').addClass("percentageError");
                    
                    $('#formCopyCompanyShare,#formCopyEmployeeShare').addClass("min[1],max[100],percentageError");
                
                    $.validator.addMethod('percentageError', function () { return false; }, 'Employee and Company Share percentage should not exceed 100');
                    
                    $.validator.addClassRules("percentageError", { percentageError : true });
                }
                else
                {
                    $('#formCopyCompanyShare,#formCopyEmployeeShare').removeClass("percentageError");
                    $('#formCopyCompanyShare,#formCopyEmployeeShare').valid();
                }
            }
            else
            {             
                /** check the sum of company share percent and employee share percent **/
                pfpercentage = (parseFloat($('#formCompanyShare').val())+parseFloat($('#formEmployeeShare').val()))>100;
                
                if (pfpercentage == true) {
                    $('#formCompanyShare,#formEmployeeShare').addClass("percentageError");
                    
                    $('#formCompanyShare,#formEmployeeShare').addClass("min[0],max[100],percentageError");
                
                    $.validator.addMethod('percentageError', function () { return false; }, 'Employee and Company Share percentage should not exceed 100');
                    
                    $.validator.addClassRules("percentageError", { percentageError : true });
                }
                else
                {
                    $('#formCompanyShare,#formEmployeeShare').removeClass("percentageError");
                    $('#formCompanyShare,#formEmployeeShare').valid();
                }
            }
        }
    }
    
    /** show and hide statuatory limit salary and comparison based on statuatory salary limit defined flag **/
    function salLimitDefined()
    {
        if ($("#formStatutorySalaryLimit").val() != '' ) {     
             $("#formStatutorySalaryLimit").val('');
             $("#formStatutorySalaryLimit").prop('readOnly', false);
             $("#s2id_formStatutoryLimitCompare").select2('val', '');   
        }
        
        var salaryDefined = ($('#formDefineStatutorySalary')[0].checked ==  true)? 1:0;
        if(salaryDefined == 0)
        {            
            $(".formStatutoryLimitHidden").hide();
            //$("#formStatutorySalaryLimit").removeAttr();         
            
        }
        else if(salaryDefined == 1)
        {
            $(".formStatutoryLimitHidden").show();
            $("#formStatutorySalaryLimit").prop({"min":1,"max":9999999.99});
        }
    }
    
     /**
     *  Prefill payment tracker form values in add, edit, reset events
    */
    function fnPreFillFormValuesProvidentFund (record) {
        $('#s2id_formCoverage,#s2id_formEmployeeName,#s2id_formContribution,#s2id_formSalaryType,#s2id_formStatutoryLimitCompare').removeClass('form-error');
        
        if (record != '')
        {
            $('#formCoverage').prop('readonly', true);

            /** check the coverage **/ 
            if (record.Coverage == 'Organization') {
                $('#PfOrgId').val(record.PfOrg_Id);
                $('#EmployeeId').val(0);
                $('#s2id_formCoverage').select2('val', 'O');
                
                /** append only the record value to salary type select field **/ 
                var salaryType = (record.Salary_Type=='Monthly') ? 'MON':'HOU';
                $('#formSalaryType').find('option').remove();
                $('#formSalaryType').append("<option value='" + salaryType + "'>" + record.Salary_Type + "</option>");                 
                $('#s2id_formSalaryType').select2('val', salaryType);               
                
                /** check Statutory_Sal_Limit_Defined_Flag is empty or not**/ 
                $('#formDefineStatutorySalary').prop('checked', (record.Statutory_Sal_Limit_Defined_Flag == 1 ? true : false));
                if ($('#formDefineStatutorySalary')[0].checked == true) {
                    $("#formStatutorySalaryLimit").val(record.Statutory_Salary_Limit);
                    $("#s2id_formStatutoryLimitCompare").select2('val', record.Statutory_Limit_Comparison);
                    $('.formStatutoryLimitHidden').show();

                    if(record.Statutory_Limit_Comparison == 0 ){
                        $('.editAllowEpsEpfExcessContribution').hide();             /*hide the EPS and EPF edit field only if 'Define Statutory Salary Limit' flag is enabled and
                                                                                'Statutory Limit Comparison' is 'Less than or Equal to Statutory Limit' */
                        $('#formAllowEpfExcessContribution,#formAllowEpsExcessContribution').prop('checked', false);
                    }
                    else 
                    {
                        $('.editAllowEpsEpfExcessContribution').show();
                        $('#formAllowEpfExcessContribution').prop('checked', (record.Allow_Epf_Excess_Contribution == 0 ? false : true));
                        $('#formAllowEpsExcessContribution').prop('checked', (record.Allow_Eps_Excess_Contribution == 0 ? false : true));
                    }
                }
                else
                {
                    $("#formStatutorySalaryLimit").val('');
                    $("#s2id_formStatutoryLimitCompare").select2('val', '');
                    $('.formStatutoryLimitHidden').hide();
                    $('.editAllowEpsEpfExcessContribution').show();
                    $('#formAllowEpfExcessContribution').prop('checked', (record.Allow_Epf_Excess_Contribution == 0 ? false : true));
                    $('#formAllowEpsExcessContribution').prop('checked', (record.Allow_Eps_Excess_Contribution == 0 ? false : true));
                }
                
                if ($('#formStatutorySalaryLimit').is('[readonly]')) {
                    $('#formStatutorySalaryLimit').prop('readOnly', false);
                }                
            }
            else if (record.Coverage == 'Employee') {
                $('#EmployeeId').val(record.EmployeeId);
                $('#PfOrgId').val(0);
                $('#s2id_formEmployeeName').select2('val', record.EmployeeId);                
                $('#formEmployeeName').prop('readonly', true);
                $('#s2id_formCoverage').select2('val', 'E');
                $('.editAllowEpsEpfExcessContribution').show();
                $('#formAllowEpfExcessContribution').prop('checked', (record.Allow_Epf_Excess_Contribution == 0 ? false : true));
                $('#formAllowEpsExcessContribution').prop('checked', (record.Allow_Eps_Excess_Contribution == 0 ? false : true));
            }            
           
            if (record.Company_Share_Amount==null && record.Employee_Share_Amount==null ) {
                $("#formCompanyShare").val(record.Company_Share);
                $("#formEmployeeShare").val(record.Employee_Share);
                $('#s2id_formContribution').select2('val', 1);
                $("#formCompanyShare, #formEmployeeShare").prop({
                 "min" : 0,
                 "max": 100
                });
            }
            else if ( record.Company_Share==null && record.Employee_Share==null ) {
                $("#formCompanyShare").val(record.Company_Share_Amount);
                $("#formEmployeeShare").val(record.Employee_Share_Amount);
                $('#s2id_formContribution').select2('val', 0);
                $("#formCompanyShare, #formEmployeeShare").prop({
                 "min" : 0 ,
                 "max" : 9999999.99
                 });       
            }
            
            $("#formEditDescription").val(record.Description);
            $('#formAdminChargePercent').val(fnCheckNull(record.Admin_Charge));
            $('#formAdminChargeAmt').val(fnCheckNull(record.Admin_Charge_Max_Amount));
            $('#formEdliPercent').val(fnCheckNull(record.EDLI_Configuration_Employer));
            $('#formEdliLimitAmt').val(fnCheckNull(record.EDLI_Charge_Max_Amount));

            $('#formAdminChargePartOfCTC').prop('checked', (record.Admin_Charge_Part_Of_Ctc == 0 ? false : true));
            $('#formEdliChargePartOfCTC').prop('checked', (record.Edli_Charge_Part_Of_Ctc == 0 ? false : true));
        }
        else
        {
            $('#formProvidentFundReset').trigger('click');
            
            $('#EmployeeId').val(0);
            $('#PfOrgId').val(0);
            $('#formCoverage,#formStatutorySalaryLimit,#formEmployeeName').prop('readOnly', false);
           
            //$("#s2id_formCoverage").select2('val', 'O');
            $('#s2id_formContribution').select2('val', 1);            
            
            $('#formSalaryType').find('option').remove();
            $('#formSalaryType').append( "<option value=''>-- Select --</option>");
                  var salaryType = ['Monthly','Hourly'];
                  var salaryValue = ['MON','HOU'];
                  
            for (var x in salaryType)
            {
                $('#formSalaryType').append("<option value='" + salaryValue[x] + "'>" + salaryType[x] + "</option>");
            }
            
            $('#s2id_formEmployeeName,#s2id_formSalaryType,#s2id_formStatutoryLimitCompare').select2('val', '');
            
            $('#formDefineStatutorySalary').prop('checked', false);
            
            $('.editAllowEpsEpfExcessContribution').show();
            $('#formAllowEpfExcessContribution,#formAllowEpsExcessContribution').prop('checked', false);

            $('#formAdminChargePartOfCTC').prop('checked', false);
            $('#formEdliChargePartOfCTC').prop('checked', false);
            $('.formStatutoryLimitHidden').hide();
            
            $("#formCompanyShare,#formEmployeeShare,#formEditDescription,#formStatutorySalaryLimit,#formAdminChargePercent,#formAdminChargeAmt,#formEdliPercent,#formEdliLimitAmt").val('');    
        }
       
        $('#formCoverage').trigger('change');
        $( "#editFormProvidentFund").validate().resetForm();
        
        isDirtyFormProvidentFund = false;
    }
    
    /**
     * Reset copy provident fund form
    */
    function fnresetcopyprovidentfund()
    {
        if ( ($('#formCopyEmployeeId').val()) > 0)
        {            
            var record = tableProvidentFund.fnGetData(fnGetSelected(tableProvidentFund)[0]);
           
            $('#s2id_formcopyEmployeeName').removeClass('form-error');
            
            $('#s2id_formcopyEmployeeName').select2('val','');
            
            $("#formCopyCoverage").val(record.Coverage);
            
            if (record.Company_Share_Amount==null && record.Employee_Share_Amount==null ) {
                $("#formCopyCompanyShare").val(record.Company_Share);
                $("#formCopyEmployeeShare").val(record.Employee_Share);
                $("#formCopyContribution").val(1);
            }
            else if ( record.Company_Share==null && record.Employee_Share==null ) {
                $("#formCopyCompanyShare").val(record.Company_Share_Amount);
                $("#formCopyEmployeeShare").val(record.Employee_Share_Amount);
                $("#formCopyContribution").val(record.Employee_Share);
                $("#formCopyContribution").val(0);
            }            
            $('#formCopyAdminChargePercent').val(record.Admin_Charge);
            $('#formCopyAdminChargeAmt').val(record.Admin_Charge_Max_Amount);
            $('#formCopyEdliPercent').val(record.EDLI_Configuration_Employer);
            $('#formCopyEdliLimitAmt').val(record.EDLI_Charge_Max_Amount);
            $('#formCopyDescription').val('');
        }
        
        isDirtyCopyProvidentFund = false;
    }
    
    /** Function To Show And Hide Action Buttons  For All the Forms **/ 
    function fnProvidentFundActionButtons (action) {
        if (action)
        {
            var record = tableProvidentFund.fnGetData (fnGetSelected (tableProvidentFund)[0]);
            
            fnGridButtons ($('#viewProvidentFund'), true);
            fnGridButtons ($('#editProvidentFund'), true);
            fnGridButtons ($('#deleteProvidentFund'), true);
            
            if ($.inArray(record.EmployeeId,[null,undefined,'','-']) == -1){
                fnGridButtons ($('#copyProvidentFund'), true);
                $('#copyContextProvidentFund').parent().show();
            }
            else{
                fnGridButtons ($('#copyProvidentFund'), false);
                $('#copyContextProvidentFund').parent().hide();
            }            
        }
        else
        {
            fnGridButtons ($('#viewProvidentFund,#editProvidentFund,#copyProvidentFund,#deleteProvidentFund'), false);
        }     
    }
    
    /************************************* Payment Tracker Grid ********************************************/
    
    // Adding Validation to check max length for amount field
    $.validator.addMethod("amountMaxLength", function(value, element) {
        return (value.length <= 12);
    }, 'Length should be maximum 12 characters');
    
    $.validator.addClassRules({
        amountMaxLength : {
            amountMaxLength : true
        }
    });
    
    /** Create Payment Tracker Grid**/ 
    var tablePfPaymentTracker = $('#tablePfPaymentTracker').dataTable({
        "lengthMenu"     : [ 5, 10, 25, 50, 100 ], 
        "iDisplayLength" : 10,
        "bDestroy"       : true,
        "bAutoWidth"     : false,
        "bServerSide"    : true,
        "bDeferRender"   : true,
        "sServerMethod"  : "POST", 
        "sAjaxSource"    : pageUrl() + "payroll/provident-fund/show-pf-payment",
        "sAjaxDataProp"  : "aaData",
        "aaSorting"      : [],
        "aoColumnDefs"   : [{"targets": 0, "orderable": false},
                            { "sClass" : "visible-xs visible-sm  visible-md hidden-lg", "aTargets" : [0] },
                            { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [3] },
                            { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [4] },
                            { "sClass" : "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets" : [5] },
                            { "sClass" : "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets" : [6] }],
        "fnCreatedRow": function( nRow, aData, iDataIndex ) {
            $(nRow).attr({"data-toggle":"context", "data-target":"#pfPaymentTracker-context-menu" });
        },
        "aoColumns"      :[{
            "mData" : function (row, type, set) {
                return '<i class="fa fa-plus-square-o"></i>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                return '<div class="text-center" >'+ row['SalaryMonth'] +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                return '<div class="text-center" >'+ row['Emp_ShareAmount'] +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                return '<div class="text-center" >'+ row['Org_ShareAmount'] +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                return '<div class="text-center" >'+ row['Outstanding_Amt'] +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {                
                return '<div class="text-center" >'+ fnCheckNull(row['Amount_Paid']) +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                return '<div>'+ row['Payment_Status'] +'</div>';
            }
        }]
    });
    
    
    //On + icon click in mobile & tablet view
    $(document).on('click', '#tablePfPaymentTracker i', function () {
        var nTr = $(this).parents('tr')[0];
        
        fnFilterClose ('PfPaymentTracker');
        
        if ( tablePfPaymentTracker.fnIsOpen(nTr) )
        {
            /* This row is already open - close it */
            $(this).removeClass().addClass('fa fa-plus-square-o');
            tablePfPaymentTracker.fnClose(nTr);
        }
        else
        {
            var record = tablePfPaymentTracker.fnGetData( nTr );
            var nRow =  $('#tablePfPaymentTracker thead tr')[0];
            
            /* Open this row */
            $(this).removeClass().addClass('fa fa-minus-square-o');
            
            valueArray = [];
            headerArray=[];
        
            valueArray.Value_One = record.Org_ShareAmount;
            valueArray.Value_Two = record.Outstanding_Amt;
            valueArray.Value_Three = record.Amount_Paid;
            valueArray.Value_Four = record.Payment_Status;
            
            $.each(nRow.cells, function(i,v) {
                headerArray['Header'+i] = v.innerText;
            });
            
            tablePfPaymentTracker.fnOpen(nTr, fnDeviceColumnDetails(headerArray,valueArray), 'details hidden-lg');
        }
    });
    
    $('#paymentTrackerClose').on('click',function() {
        fnRefreshTable (tablePfPaymentTracker);
    });
    
    
    /*  Add event listener for select and unselect details  */
    $(document).on('click contextmenu', '#tablePfPaymentTracker tbody td div', function () {
        fnFilterClose ('PfPaymentTracker');
        
        var selectRow = $(this).parent().parent();
        
        tablePfPaymentTracker.$('tr.row_selected').removeClass('row_selected');
        
        if (!selectRow.hasClass('row_selected'))
        {
            selectRow.addClass('row_selected');
            
            fnPaymentTrackerActionButtons (true);
        }
        else
        {
            fnPaymentTrackerActionButtons (false);
        }
    });
    
    //** Refresh The Payment Tracker Grid**/ 
    $('#gridPanelPaymentTracker .panel-reload').on('click', function () {
        fnRefreshTable (tablePfPaymentTracker)
    });
    
    /**
     *  close filter form and clear enabled buttons based on selection record
     *  while search all, sorting, pagination, redraw events
     *  it will work in search.dt order.dt page.dt, length.dt those events
    */
    $('#tablePfPaymentTracker').on( 'draw.dt', function () {        
        fnPaymentTrackerActionButtons (false);

        fnFilterClose ('PfPaymentTracker');
    });
    
    //View Payment Tracker
    $('#viewPfPaymentTracker, #viewContextPfPaymentTracker').on('click', function () {
        var selectedRow = fnGetSelected ( tablePfPaymentTracker );
        
        fnFilterClose ('PfPaymentTracker');
        
        if (selectedRow.length)
        {
            var record = tablePfPaymentTracker.fnGetData (selectedRow[0]),
                paymentId = record.Payment_Id;
                
            if (paymentId > 0)
            {
                $('#modalFormPfPaymentTracker .modal-title').html("<strong>View</strong> "+$('#lblFormNameB').html());
                
                $('#viewPaymentTrackerPayslipMonth').text(record.SalaryMonth);
                $('#viewPaymentTrackerEmpShareAmount').text(record.Emp_ShareAmount);
                $('#viewPaymentTrackerOrgShareAmount').text(record.Org_ShareAmount);
                $('#viewPaymentTrackerTotalAmount').text( record.Outstanding_Amt );
                $('#viewPaymentTrackerStatus').text(record.Payment_Status);
                
                tablePaymentTrackerSubGridView.fnReloadAjax( pageUrl () + "payroll/provident-fund/payment-subgrid/paymentId/" + paymentId );
                
                $('#viewFormPfPaymentTracker').show();
                $('#editFormPfPaymentTracker, #pfPaymentTrackerGrid, #addPFPaymentTracker').hide();
                $('#modalFormPfPaymentTracker').modal('toggle');
                
                if (record.Payment_Status == 'Paid')
                {
                    $('#editInViewPfPaymentTracker').hide();
                }
                else
                {
                    $('#editInViewPfPaymentTracker').show();
                }
                
                $('#tablePFPaymentTrackerSubGridView').parent().removeClass('force-table-responsive');
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+$('#lblFormNameB').html()+' record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameB').html()+' record', type : 'info' });
        }
    });
    
    //Edit Payment Tracker
    $('#editPfPaymentTracker, #editContextPfPaymentTracker, #editInViewPfPaymentTracker').on('click', function () {        
        var selectedRow = fnGetSelected (tablePfPaymentTracker );
        
        fnFilterClose ('PfPaymentTracker');
        
        if (selectedRow.length)
        {
            var buttonId = $(this).prop('id');
            
            var record    = tablePfPaymentTracker.fnGetData (selectedRow[0]);
                paymentId = record.Payment_Id;
                
            if (paymentId > 0 && !isNaN(paymentId) && record.Payment_Status != 'Paid')
            {
                // force-table-responsive class is removed because when we open form scroll bar is set
                $('#tablePfPaymentTrackerSubGrid').parent().removeClass('force-table-responsive');
                
                // Lock is not set becoz no lock flag to main grid
                
                $('#modalFormPfPaymentTracker .modal-title').html("<strong>Edit</strong> "+$('#lblFormNameB').html());
                
                $('#formPfPaymentTrackerPayslipMonth').html(record.SalaryMonth);
                $('#formPfPaymentTrackerEmpShareAmount').html(record.Emp_ShareAmount);
                $('#formPfPaymentTrackerTotalAmount').html(record.Outstanding_Amt);
                $('#formPfPaymentTrackerOrgShareAmount').html(record.Org_ShareAmount);
                $('#formPfPaymentTrackerStatus').html(record.Payment_Status);
                $('#formPfPaymentTrackerOutstandingAmount').html((record.Bal_Amount != null) ? record.Bal_Amount : record.Outstanding_Amt );
                
                fnPreFillFormValuesPfPaymentTracker (record, '', 0);                
                
                /** get the payment tracker min date **/
                $.ajax ({
                    type     : 'POST',
                    async    : false,
                    dataType : 'json',
                    url      : pageUrl() + 'default/employee-info/get-payment-tracker-date/salaryMonth/'+record.SalaryMonth,
                    success  : function (result)
                    {
                        if (isJson (result))
                        {
                            if(result != null)
                            {
                                $('#formPfPaymentTrackerPaymentDate').datepicker('option', 'minDate',result);
                            }
                            else{
                                $('#formPfPaymentTrackerPaymentDate').datepicker('option', 'minDate',new Date(tzDate()));
                            }
                        }
                        else{
                            sessionExpired ();
                        }
                    }
                });
                
                $('#editInViewPfPaymentTracker, #viewFormPfPaymentTracker').hide();
                $('#editFormPfPaymentTracker, #pfPaymentTrackerGrid, #addPFPaymentTracker').show();
                
                if (buttonId != 'editInViewPfPaymentTracker')
                    $('#modalFormPfPaymentTracker').modal('toggle');
                    
                if (!($("#onPFAddCollapse").hasClass('collapsed')))
                {
                    $( "#onPFAddCollapse" ).trigger( "click" );
                }
                
                if ($("#pfPaymentSummaryPanel").hasClass('collapsed'))
                {
                    $( "#pfPaymentSummaryPanel" ).trigger( "click" );
                }
            
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+$('#lblFormNameB').html()+' record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameB').html()+' record', type : 'info' });
        }
    });
    
    $('#formPfPaymentTrackerAmountPaid').on('change',function(){
        $('#formPfPaymentTrackerAmountPaid').removeClass("amountError");
        
        var balanceAmount =  $('#formPfPaymentTrackerOutstandingAmount').html();
        
        if($('#formPfPaymentTrackerId').val()>0){
            var subGridRecord = tablePfPaymentTrackerSubGrid.fnGetData (fnGetSelected (tablePfPaymentTrackerSubGrid)[0]);
           
            balanceAmount = parseFloat(balanceAmount) + parseFloat(subGridRecord.Amount_Paid);
           
            $('#formPfPaymentTrackerAmountPaid').prop({'min':1,'max':balanceAmount});            
        }
        else
        {
            if(balanceAmount >= 0)
            {
                $('#formPfPaymentTrackerAmountPaid').prop({'min':1,'max':balanceAmount});
            }
            else{
                $('#formPfPaymentTrackerAmountPaid').addClass("amountError");
                
                $.validator.addMethod('amountError', function () { return false; }, 'There is no outstanding amount to be paid');
                    
                $.validator.addClassRules("amountError", { amountError : true });
            }
        }        
    });
    
    
    //Form reset - Payment tracker
    $('#formResetPfPaymentTracker').on('click', function (e) {
        var l = Ladda.create(this);
        
        l.start();
        
        var mainGridRecord = tablePfPaymentTracker.fnGetData (fnGetSelected (tablePfPaymentTracker)[0]);
        
        if ($('#formPfPaymentTrackerId').val() > 0)
        {
            var record = tablePfPaymentTrackerSubGrid.fnGetData (tablePfPaymentTrackerSubGrid.$('tr.paymentTracker_editing_row'));
                
            if(tablePfPaymentTrackerSubGrid.$('tr.paymentTracker_editing_row').length == 1)
            {
                fnPreFillFormValuesPfPaymentTracker (mainGridRecord, record, 1);
            }
            else
            {
                var subGridRecord = tablePfPaymentTrackerSubGrid.fnGetData (fnGetSelected (tablePfPaymentTrackerSubGrid)[0]);
                
                if(subGridRecord != '')
                {
                    fnPreFillFormValuesPfPaymentTracker (mainGridRecord, subGridRecord, 1);
                }
                else
                {
                    fnPreFillFormValuesPfPaymentTracker (mainGridRecord, '', 0);
                }
            }            
        }
        else
        {
            fnPreFillFormValuesPfPaymentTracker (mainGridRecord, '', 0);
            
            e.preventDefault();
        }
        
        l.stop();
    });
        
    //Form submit - payment tracker
    $('#formSubmitPfPaymentTracker').on('click', function (e) {        
        var l = Ladda.create(this);
        
        l.start();
        
        if (isDirtyFormPfPaymentTracker)
        {
            $("#s2id_formPfPaymentTrackerModeOfPayment").removeClass('form-error');            
           
            if ($("#editFormPfPaymentTracker").valid())
            {
                var paymentModeId=  $('#s2id_formPfPaymentTrackerModeOfPayment').select2('val');
                
                if (paymentModeId != 1 && paymentModeId != 4 && paymentModeId != 5 && paymentModeId != 6)
                {                    
                    var documentNo  = $('#formPfPaymentTrackerDocumentNo').val(),
                        bankName    = $('#formPfPaymentTrackerBankName').val(),
                        branchName  = $('#formPfPaymentTrackerBranchName').val();
                }
                else
                {
                    var  documentNo = bankName = branchName = '';
                }
                
                $.ajax ({
                    type     : 'POST',
                    dataType : 'json',    
                    url      : pageUrl () +'payroll/provident-fund/edit-pf-payment',
                    data     : {
                        salaryMonth      : $('#formPfPaymentTrackerPayslipMonth').html(),
                        paymentId        : $('#formPfPaymentId').val(),
                        paymentTrackerId : $('#formPfPaymentTrackerId').val(),
                        paymentMode      : paymentModeId,
                        paymentDate      : fnServerDateFormatter ($('#formPfPaymentTrackerPaymentDate').datepicker('getDate')),
                        documentNo       : documentNo,
                        bankName         : bankName,
                        branchName       : branchName,
                        amountPaid       : $('#formPfPaymentTrackerAmountPaid').val(),
                        description      : $('#formPfPaymentTrackerDescription').val(),
                    },
                    success  : function (result)
                    {
                        if (isJson (result))
                        {
                            if (result.success)
                            {
                                lockClear = 0;
                                
                                if ($('#formPfPaymentTrackerId').val() > 0)
                                {
                                    clearLock ({
                                        'formName' : 'PF Payment Tracker',
                                        'uniqueId' :  $('#formPfPaymentTrackerId').val(),
                                        'callback' : function ()
                                        {
                                            lockClear = 1;
                                        }
                                    });
                                }
                                else
                                {
                                    lockClear = 1;
                                }
                                
                                if (lockClear == 1)
                                {
                                    $("#formSubmitPfPaymentTracker").html('<i class="mdi-content-send"></i> Add');
                                    
                                    $('#formPfPaymentTrackerId').val(0);
                                    
                                    $('#formResetPfPaymentTracker').trigger('click');
                                    
                                    isDirtyFormPfPaymentTracker = false;                                    
                                    
                                    $('#formPfPaymentTrackerOutstandingAmount').html(fnCheckNull(result.Bal_Amount));
                                    $('#formPfPaymentTrackerStatus').html(result.status);
                                    
                                    if(result.status=='Paid'){                                        
                                        $('#modalFormPfPaymentTracker').modal('toggle');
                                        
                                        fnRefreshTable (tablePfPaymentTracker);
                                        
                                        jAlert ({ msg : result.msg, type : result.type });
                                    }
                                    else
                                    {                                        
                                        tablePfPaymentTrackerSubGrid.fnReloadAjax( pageUrl () + "payroll/provident-fund/payment-subgrid/paymentId/" + $('#formPfPaymentId').val() );
                                       
                                        fnModalScrollTop('modalFormPfPaymentTracker');
                                       
                                        jAlert ({ panel : $('#editFormPfPaymentTracker'), msg : result.msg, type : result.type });
                                    }                                    
                                    
                                    $('#ui-datepicker-div').hide();
                                    
                                    e.preventDefault();
                                    
                                    //$('#formPaymentTrackerAmountPaid, #formInsPaymentTrackerPaymentDate').removeClass('form-error');
                                    //
                                    //$('#formInsPaymentTrackerPaymentDate-error').css('display','none');
                                }
                            }
                            else
                            {
                                jAlert ({ panel : $('#editFormPfPaymentTracker'), msg : result.msg, type : result.type });
                            }
                        }
                        else
                        {
                            sessionExpired ();
                        }
                        
                        l.stop();
                    }
                });
            }
            else
            {
                l.stop();                
            }
        }
        else
        {   
            l.stop();
            fnModalScrollTop('modalFormPfPaymentTracker');
            setTimeout(function() { $('#ui-datepicker-div').hide(); },50);
            jAlert ({ panel : $('#editFormPfPaymentTracker'), msg : 'Form has no changes', type : 'info' });
        }
    });   
        
    /************************************** Pf Payment Tracker Sub Grid **********************************************/    
    
    /*  Initialse DataTables, with no sorting on the 'details' column  */
    tablePaymentTrackerSubGridView = $('#tablePFPaymentTrackerSubGridView').dataTable({
        "iDisplayLength" : 10,
        "lengthMenu"     : [ 5, 10, 25, 50, 100 ], 
        "bDestroy"       : true,
        "bAutoWidth"     : false,
        "bServerSide"    : true,
        "bDeferRender"   : true,
        "sServerMethod"  : 'POST',
        "sAjaxSource"    : pageUrl () + 'payroll/provident-fund/payment-subgrid',
        "sAjaxDataProp"  : 'aaData',
        "info"           : false,
        "bPaginate"      : false, // Remove pagination
        "bFilter"        : false,
        "aaSorting"      : [],
        "aoColumnDefs"   : [{ 'bSortable': false, 'aTargets': ['_all'] },
                            { "sClass" : "hidden-xs hidden-sm visible-md visible-lg", "aTargets" : [3] },
                            { "sClass" : "hidden-xs hidden-sm visible-md visible-lg", "aTargets" : [4] }],
        "aoColumns"      : [{
            "mData" : function (row, type, set) {
                return '<i class="fa fa-plus-square-o"></i>';
            }
        },
        {
            "mData" : function (row, type, set) {
                return '<div >'+ row['Payment_Type'] +'</div>';
            }
        },
        {
            "mData" : function (row, type, set) {
                return '<div class="text-center" >'+ row['Payment_Date'] +'</div>';
            }
        },
        {
            "mData" : function (row, type, set) {
                return '<div class="text-center" >'+ row['Amount_Paid'] +'</div>';
            }
        },
        {
            "mData" : function (row, type, set) {
                return '<div>'+ fnCheckNull(row['Description']) +'</div>';
            }
        }]
    });
    
    /*  Initialse DataTables, with no sorting on the 'details' column  */
    tablePfPaymentTrackerSubGrid = $('#tablePfPaymentTrackerSubGrid').dataTable({
        "iDisplayLength" : 10,
        "lengthMenu"     : [ 5, 10, 25, 50, 100 ], 
        "bDestroy"       : true,
        "bAutoWidth"     : false,
        "bServerSide"    : true,
        "bDeferRender"   : true,
        "sServerMethod"  : 'POST',
        "sAjaxSource"    : pageUrl () + 'payroll/provident-fund/payment-subgrid',
        "sAjaxDataProp"  : 'aaData',
        "aoColumnDefs"   : [{ 'bSortable': false, 'aTargets': ['_all'] },{ 'sClass' : 'text-center', 'aTargets' : [2, 3] }],
        "aaSorting"      : [],
        "info"           : false,
        "bPaginate"      : false, // Remove pagination
        "bFilter"        : false,
        "aoColumns"      : [{
            "mData" : function (row, type, set) {
                if (tablePfPaymentTracker.fnGetData (fnGetSelected (tablePfPaymentTracker)[0]).Payment_Status != 'Paid')
                {
                    return '<div class="btn btn-sm btn-embossed btn-white editPfPaymentTrackerSubgrid" title="Edit" id="pfPaymentTracker_'+row['PF_Payment_Tracker_Id']+'"><i class="mdi-editor-mode-edit"></i></div>';
                }
            }
        },
        {
            "mData" : function (row, type, set) {
                return '<div >'+ row['Payment_Type'] +'</div>';
            }
        },
        {
            "mData" : function (row, type, set) {
                return '<div class="text-center" >'+ row['Payment_Date'] +'</div>';
            }
        },
        {
            "mData" : function (row, type, set) {
                return '<div class="text-center" >'+ row['Amount_Paid'] +'</div>';
            }
        }]
    });
    
    // Payment tracker view - On + button click - get more information by form expand
    $(document).on('click', '#tablePFPaymentTrackerSubGridView i', function () {
        var nTr = $(this).parents('tr')[0];
        
        if ( tablePaymentTrackerSubGridView.fnIsOpen(nTr) )
        {
            /* This row is already open - close it */
            $(this).removeClass().addClass('fa fa-plus-square-o');
            
            tablePaymentTrackerSubGridView.fnClose(nTr);
        }
        else
        {
            /* Open this row */
            $(this).removeClass().addClass('fa fa-minus-square-o');
            
            tablePaymentTrackerSubGridView.fnOpen(nTr, fnShowHiddenPaymentTrackerDetails(nTr), 'details');
        }
    });
    
    /*  Add event listener for select and unselect details  */
    $(document).on('click','#tablePfPaymentTrackerSubGrid tbody td div', function () {
        var selectRow = $(this).parent().parent();
        
        tablePfPaymentTrackerSubGrid.$('tr.row_selected').removeClass('row_selected');
        
        if (!selectRow.hasClass('row_selected'))
        {
            selectRow.addClass('row_selected');
        }
    });
    
    // On payment tracker add button click
    $('#addPFPaymentTracker').on('click', function(){
        $("#formSubmitPfPaymentTracker").html('<i class="mdi-content-send"></i> Add');
        
        if ($("#onPFAddCollapse").hasClass('collapsed'))
        {
            $( "#onPFAddCollapse" ).trigger( "click" );
        }
        
        var mainGridRecord = tablePfPaymentTracker.fnGetData (fnGetSelected ( tablePfPaymentTracker )[0]);
        
        var paymentTrackerId = $('#formPfPaymentTrackerId').val();
             
        if (paymentTrackerId > 0 && !isNaN(paymentTrackerId))
        {
            clearLock ({
                'formName' : 'PF Payment Tracker',
                'uniqueId' : paymentTrackerId,
                'callback' : function ()
                {
                    $('#formPfPaymentTrackerId').val(0);
                }
            });
        }
        
        fnPreFillFormValuesPfPaymentTracker (mainGridRecord, '', 0); 
    });
        
    $(document).on('click', '.viewPfPaymentTrackerSubgrid', function(){        
        var selectedRow = fnGetSelected ( tablePfPaymentTrackerSubGrid );
        
        if (selectedRow.length)
        {
            var record = tablePfPaymentTrackerSubGrid.fnGetData (selectedRow[0]);
            
            var mainGridRecord = tablePfPaymentTracker.fnGetData (fnGetSelected ( tablePfPaymentTracker )[0]);
            
            if (record.PF_Payment_Tracker_Id > 0)
            {
                $('#modalFormPfPaymentTracker .modal-title').html("<strong>View</strong> PF Payment");
                
                $('#viewPaymentTrackerPayslipMonth').text(record.SalaryMonth);
                $('#viewPaymentTrackerEmpShareAmount').text(mainGridRecord.Emp_ShareAmount);                
                $('#viewPaymentTrackerTotalAmount').text(mainGridRecord.Outstanding_Amt);
                $('#viewPaymentTrackerOrgShareAmount').text(mainGridRecord.Org_ShareAmount);
                $('#viewPaymentTrackerStatus').text(record.Payment_Status);
                $('#viewPaymentTrackerModeOfPayment').text( record.Payment_Type );
                $('#viewPaymentTrackerPaymentDate').text(record.Payment_Date);
                $('#viewPaymentTrackerDocumentNo').text(fnCheckNull (record.Document_No));
                $('#viewPaymentTrackerBankName').text(fnCheckNull (record.Bank_Name));
                $('#viewPaymentTrackerBranchName').text(fnCheckNull (record.Branch_Name));
                $('#viewPaymentTrackerAmountPaid').text(fnCheckNull (record.Amount_Paid));
                $('#viewPaymentTrackerOutstandingAmount').text(fnCheckNull (mainGridRecord.Bal_Amount));
                $('#viewPaymentTrackerDescription').text(fnCheckNull (record.Description));
                
                fnPreFillAdditionalPanel ('PfPaymentTracker', record);
                
                if (record.Payment_Status=="Paid")
                {
                    $('#editInViewPfPaymentTracker').hide();
                }
                else
                {
                    $('#editInViewPfPaymentTracker').show();
                }
                
                $('#modalFormPfPaymentTracker').modal('toggle');
                $('#editFormPfPaymentTracker, #formActionPfPaymentTracker').hide();
                $('#viewFormPfPaymentTracker').show();
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+$('#lblFormNameB').html()+' subgrid record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameB').html()+' subgrid record', type : 'info' });
        }
    });
    
    // Edit Payment tracker subgrid
    $(document).on('click', '.editPfPaymentTrackerSubgrid', function(){
        $("#formSubmitPfPaymentTracker").html('<i class="mdi-content-send"></i> Update');
        
        var buttonId    = $(this).prop('id');
        var selectedRow = fnGetSelected ( tablePfPaymentTrackerSubGrid );
        
        if (selectedRow.length)
        {
            var mainGridRecord = tablePfPaymentTracker.fnGetData (fnGetSelected ( tablePfPaymentTracker )[0]),            
                record = tablePfPaymentTrackerSubGrid.fnGetData (selectedRow[0]);
            
            if (mainGridRecord.Payment_Status != 'Paid')
            {
                if (record.PF_Payment_Tracker_Id > 0 && mainGridRecord.Payment_Id > 0) 
                {
                    clearLockFlag = 0;
                    
                    if ($('#formPfPaymentTrackerId').val() > 0)
                    {
                        clearLock ({
                            'formName' : 'PF Payment Tracker',
                            'uniqueId' :  $('#formPfPaymentTrackerId').val(),
                            'callback' : function ()
                            {
                                clearLockFlag = 1;
                                
                                tablePfPaymentTrackerSubGrid.$('tr.paymentTracker_editing_row').removeClass('paymentTracker_editing_row');
                                
                                $('#formPfPaymentTrackerId').val(0);
                                fnPreFillFormValuesPfPaymentTracker (mainGridRecord, '', 1);
                            }
                        });
                    }
                    else
                    {
                        clearLockFlag = 1;
                    }
                    
                    if (clearLockFlag == 1)
                    {
                        setLock ({
                            'formName' : 'PF Payment Tracker',
                            'uniqueId' : record.PF_Payment_Tracker_Id,
                            'panel'    : $('#editFormPfPaymentTracker'),
                            'callback' : function (result)
                            {
                                if ($("#onPFAddCollapse").hasClass('collapsed'))
                                {
                                    $( "#onPFAddCollapse" ).trigger( "click" );
                                }
                                
                                var rows = $('tbody td div#pfPaymentTracker_'+record.PF_Payment_Tracker_Id);
                                 
                                var editRow = rows.parent().parent();
                                
                                if(!(editRow.hasClass('paymentTracker_editing_row')))
                                {
                                    editRow.addClass('paymentTracker_editing_row');
                                }
                                fnPreFillFormValuesPfPaymentTracker (mainGridRecord, record,1);
                                
                                if(!(editRow.hasClass('row_selected')))
                                {
                                    editRow.addClass('row_selected');
                                }
                            }
                        });
                    }
                }
                else
                {
                    jAlert ({ msg : 'Kindly select '+$('#lblFormNameB').html()+' subgrid record', type : 'info' });
                }
            }
            else
            {
                jAlert ({ panel: $('#tablePaymentTrackerSubGrid'), msg : 'Payment is done', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameB').html()+' subgrid record', type : 'info' });
        }
    });
    
    // On payment mode change
    $('#formPfPaymentTrackerModeOfPayment').on('change', function (){
        var paymentModeId = $(this).val();
        
        //paymentMode id for Cash,Debit Card,Credit Card
        if (paymentModeId != 1 && paymentModeId != 4 && paymentModeId != 5 && paymentModeId != 6)
        {         
            $('.paymentModeBasedHidden').show();
            $('#formPfPaymentTrackerDocumentNo, #formPfPaymentTrackerBankName, #formPfPaymentTrackerBranchName').addClass('vRequired');            
        }
        else
        {         
            $('.paymentModeBasedHidden').hide();
            $('#formPfPaymentTrackerDocumentNo, #formPfPaymentTrackerBankName, #formPfPaymentTrackerBranchName').removeClass('vRequired');
            $('#formPfPaymentTrackerDocumentNo, #formPfPaymentTrackerBankName, #formPfPaymentTrackerBranchName').val('');
        }
    });
    
    // On form field change
    $('#editFormPfPaymentTracker').on('change', function() {        
        isDirtyFormPfPaymentTracker = true;
    });
    
    /**
     *  click to close add/edit modal
    */
    $('#editCloseConfirmPfPaymentTracker').on('click', function () {
        isDirtyFormPfPaymentTracker = false;
        
        fnFormClosePfPaymentTracker(true);
    });
    
    /**
     *  Add,Edit form modal hide event
    */
    $('#modalFormPfPaymentTracker').on('hide.bs.modal', function (e) {
        if (isDirtyFormPfPaymentTracker)
        {
            e.preventDefault();
            e.stopImmediatePropagation();
            
            $('#modalDirtyPfPaymentTracker').modal('toggle');
        }
        else
        {
            fnFormClosePfPaymentTracker(false);
        }
    });
    
     /** Show&Hide the Payment Tracker Filter **/
    $('#filterPaymentTracker,#closeFilterPfPaymentTracker').on('click', function() {
        if ($('#filterPanelPfPaymentTracker').hasClass('open'))
        {
            $('#filterPanelPfPaymentTracker').removeClass('open');
            $('#filterPanelPfPaymentTracker').hide();
        }
        else
        {
            $('#filterPanelPfPaymentTracker').addClass('open');
            $('#filterPanelPfPaymentTracker').show();
        }
    });
    
    /** Reset The Values In Payment Tracker Filter **/
    $('#cancelPaymentTracker,#closeFilterPfPaymentTracker').on('click', function () {
        $('#filterStatus').select2('val', '');
        $('#filterPaySlipMonth,#filterempShareAmountStart,#filterempShareAmountEnd,#filterTotalAmountStart').val('');
        $('#filterTotalAmountEnd,#filterOrgShareAmountStart,#filterOrgShareAmountEnd,#filterPaidAmountStart,#filterPaidAmountEnd').val('');
        
        tablePfPaymentTracker.fnReloadAjax( pageUrl() +'payroll/provident-fund/show-pf-payment' );
    });
    
     /** Apply The Values In Payment Tracker Filter **/
    $('#applyPaymentTracker').on('click', function () {
        filterSalaryMonth              = fnServerMonthFormatter($('#filterPaySlipMonth').val());      
        filterEmployeeShareAmountStart = $('#filterempShareAmountStart').val();
        filterEmployeeShareAmountEnd   = $('#filterempShareAmountEnd').val();
        filterTotalAmountStart         = $('#filterTotalAmountStart').val();
        filterTotalAmountEnd           = $('#filterTotalAmountEnd').val();      
        filterOrgShareAmountStart      = $('#filterOrgShareAmountStart').val();
        filterOrgShareAmountEnd        = $('#filterOrgShareAmountEnd').val();      
        filterPaidAmountStart          = $('#filterPaidAmountStart').val();
        filterPaidAmountEnd            = $('#filterPaidAmountEnd').val();
        filterStatus                   = $('#s2id_filterStatus').select2('val');
        
        tablePfPaymentTracker.fnReloadAjax(pageUrl() +'payroll/provident-fund/show-pf-payment'+
                                                  '/salaryMonth/'+ filterSalaryMonth +
                                                  '/employeeShareAmountStart/'+ filterEmployeeShareAmountStart +
                                                  '/employeeShareAmountEnd/'+ filterEmployeeShareAmountEnd +
                                                  '/organizationAmountStart/'+ filterOrgShareAmountStart+
                                                  '/organizationAmountEnd/'+ filterOrgShareAmountEnd +
                                                  '/totalAmountStart/'+ filterTotalAmountStart+
                                                  '/totalAmountEnd/'+ filterTotalAmountEnd +
                                                  '/paidAmountStart/'+ filterPaidAmountStart +
                                                  '/paidAmountEnd/'+ filterPaidAmountEnd +
                                                  '/status/'+ filterStatus);
    });
    
    /** Close Function For All the Forms **/ 
    function fnFormClosePfPaymentTracker (hideAction) {
        var uniqueId = $('#formPfPaymentTrackerId').val();
        
        if (uniqueId > 0 && !isNaN(uniqueId))
        {
            clearLock ({
                'formName' : 'PF Payment Tracker',
                'uniqueId' : uniqueId,
                'callback' : function ()
                {
                    $('#formPfPaymentTrackerId').val(0);
                    
                    if (hideAction)
                        $('#modalFormPfPaymentTracker').modal('hide');
                }
            });
        }
        else
        {
            if (hideAction)
                $('#modalFormPfPaymentTracker').modal('hide');
        }
    }
    
     /**
     *  Prefill payment tracker form values in add, edit, reset events
    */
    function fnPreFillFormValuesPfPaymentTracker (mainGridRecord, subGridRecord, isSubGridNeedToRefresh) {
        $('#s2id_formPfPaymentTrackerModeOfPayment').removeClass('form-error');
        
        if (mainGridRecord != '')
        {
            if (subGridRecord != '')
            {
                $('#pfPaymentSummaryPanel')[0].scrollIntoView();
                
                $('#formPfPaymentId').val(mainGridRecord.Payment_Id);
                $('#formPfPaymentTrackerId').val(subGridRecord.PF_Payment_Tracker_Id);
                
                $('#s2id_formPfPaymentTrackerModeOfPayment').select2('val', subGridRecord.PaymentMode_Id);
                //$('#formPfPaymentTrackerPaymentDate').datepicker('setDate', fnDateFormatter (subGridRecord.Payment_Date));
                //$('#formPfPaymentTrackerPaymentDate').datepicker('setDate', new Date(subGridRecord.Payment_Date));
                $('#formPfPaymentTrackerPaymentDate').datepicker('setDate', new Date(subGridRecord.Mob_Payment_Date));                
                $('#formPfPaymentTrackerDocumentNo').val(subGridRecord.Document_No);
                $('#formPfPaymentTrackerBankName').val(subGridRecord.Bank_Name);
                $('#formPfPaymentTrackerBranchName').val(subGridRecord.Branch_Name);
                $('#formPfPaymentTrackerAmountPaid').val(subGridRecord.Amount_Paid);
                $('#formPfPaymentTrackerDescription').val(subGridRecord.Description);                
             
                $('#formPfPaymentTrackerModeOfPayment').trigger('change');
            }
            else
            {
                $('#formPfPaymentTrackerReset').trigger('click');
                
                $('#formPfPaymentId').val(mainGridRecord.Payment_Id);
                $('#formPfPaymentTrackerId').val(0);
                
                $("#s2id_formPfPaymentTrackerModeOfPayment").select2('val', 1);
                
                $('#formPfPaymentTrackerModeOfPayment').trigger('change');
            }            
            
            if (isSubGridNeedToRefresh == 0)
            {
                tablePfPaymentTrackerSubGrid.fnReloadAjax( pageUrl () + "payroll/provident-fund/payment-subgrid/paymentId/" + mainGridRecord.Payment_Id );
            }
        }
        
        $( "#editPfPaymentTracker").validate().resetForm();
        
        isDirtyFormPfPaymentTracker = false;
    }
    
    /** Function To Show And Hide Action Buttons  For All the Forms **/ 
    function fnPaymentTrackerActionButtons (action) {
        if (action)
        {
            var record = tablePfPaymentTracker.fnGetData (fnGetSelected (tablePfPaymentTracker)[0]);
            
            fnGridButtons ($('#viewPfPaymentTracker'), true);
            if (record.Payment_Status=="Paid")
            {
                $('#editContextPfPaymentTracker').parent().hide();
                fnGridButtons ($('#editPfPaymentTracker'), false);
            }
            else
            {
                $('#editContextPfPaymentTracker').parent().show();
                fnGridButtons ($('#editPfPaymentTracker'), true);
            }
        }
        else
        {
            fnGridButtons ($('#viewPfPaymentTracker,#editPfPaymentTracker'), false);
        }     
    }
    
    //On second modal close, first modal scroll will not work. To avoid that,using this
    $('#dirtyProvidentFund,#modalDirtyPfPaymentTracker,#dirtyCopyProvidentFund,#statuatorySalaryIgnoreConfirmation,#modalPFExistsConfirmation').on('hidden.bs.modal', function (e) {
        if($('#modalFormProvidentFund').is(':visible') || $('#modalFormPfPaymentTracker').is(':visible') ||
           $('#modalcopyProvidentFund').is(':visible'))
        {
            $('body').addClass('modal-open');
        }
    });

    $('.page-content').addClass('custom-tab');
    $('.add-panel-padding').addClass('padding-class');
    
    // to check tab is clicked
    var tabClicked = false;

    // when the pf tab is hovered
    $('#pfFormTab').on('mouseenter', function () {
        fnTabHighlight("#pfFormTab", "#pfTab", "#formTabLink1");
    });

    // when the Pf rules tab is hovered
    $('#pfRulesFormTab').on('mouseenter', function () {
        fnTabHighlight("#pfRulesFormTab", "#pfRulesTab", "#formTabLink2");
    });

    // tab highlight function
    function fnTabHighlight(tabId, tabBorderId, formLinkId){
        $('#pfTrackerFormTab').removeClass('tab-active-text text-secondary');
        $('#pfTrackerTab').removeClass('tab-border-cls');
        $(tabId).addClass('tab-active-text text-secondary');
        $(tabBorderId).addClass('tab-border-cls')
        $(formLinkId).removeClass('tab-a-tag-color');
        $(formLinkId).addClass('text-secondary');
    };
    
    // when mouse is out of provident-fund tab we need to remove highlight property
    $('#pfFormTab').on('mouseleave', function () {
        // to check tab is clicked. If yes we don't remove tab active class
        if(!tabClicked){
            $("#pfTrackerFormTab").addClass('tab-active-text text-secondary');
            $('#pfTrackerTab').addClass('tab-border-cls');
            $('#pfFormTab').removeClass('tab-active-text text-secondary');
            $('#pfTab').removeClass('tab-border-cls');
            $('#formTabLink1').addClass('tab-a-tag-color');
            $('#formTabLink1').removeClass('text-secondary');
        }
    });

    // when mouse is out of provident-fund tab we need to remove highlight property
    $('#pfRulesFormTab').on('mouseleave', function () {
        // to check tab is clicked. If yes we don't remove tab active class
        if(!tabClicked){
            $("#pfTrackerFormTab").addClass('tab-active-text text-secondary');
            $('#pfTrackerTab').addClass('tab-border-cls');
            $('#pfRulesFormTab').removeClass('tab-active-text text-secondary');
            $('#pfRulesTab').removeClass('tab-border-cls');
            $('#formTabLink2').addClass('tab-a-tag-color');
            $('#formTabLink2').removeClass('text-secondary');
        }
    });

    // provident-fund tab onclick function
    $('#pfTab').on('click', function () {
        tabClicked = true;
        fnTabHighlight();
        setMask('#wholepage');
        // redirect to the provident-fund form
        window.location.href = pageUrl() + "v3/tax-and-statutory-compliance/statutory-components/provident-fund?tab=providentFund";
    });

    // provident-fund-rules tab onclick function
    $('#pfRulesTab').on('click', function () {
        tabClicked = true;
        fnTabHighlight();
        setMask('#wholepage');
        // redirect to the provident-fund form
        window.location.href = pageUrl() + "v3/tax-and-statutory-compliance/statutory-components/provident-fund?tab=providentFundRules";
    });
});