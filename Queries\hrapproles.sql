-- 5 Aug 2023 Deployment Queries
-- Suganya - #7590
-- INSERT INTO `emp_accessrights` (`Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`, `Form_Id`, `Employee_Id`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES ('1', '0', '1', '0', '0', '0', '0', '248', '1', '0', '1', NOW(), NULL, NULL);

-- Suhan - #7681
-- INSERT INTO `emp_accessrights` (`Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`, `Form_Id`, `Employee_Id`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES ('1', '0', '1', '0', '0', '0', '0', '249', '1', '0', '', '', NULL, NULL);

-- Shyam -#7692
-- INSERT INTO `emp_accessrights` (`Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`, `Form_Id`, `Employee_Id`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES ('1', '1', '1', '0', '0', '0', '0', '250', '1', '0', '', '', NULL, NULL);

-- Suhan - #7723 -- Run During 17thSep2023Deployment

-- INSERT INTO `form_level_coverage` (`Form_Id`, `Coverage`,`Coverage_Id`)
-- VALUES
--   (250, 'Organization', 1),
--   (85, 'Organization', 2);

-- Shyam - #7695
-- UPDATE `emp_accessrights` SET `Role_Add` = '1' WHERE `emp_accessrights`.`Form_Id` = 85 AND `emp_accessrights`.`Employee_Id` = 1;

-- Suganya #7755
-- INSERT INTO `emp_accessrights` (`Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`, `Form_Id`, `Employee_Id`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES ('1', '1', '1', '1', '0', '0', '0', '252', '1', '0', '1', NOW(), NULL, NULL), ('1', '1', '1', '1', '0', '0', '0', '253', '1', '0', '1', NOW(), NULL, NULL);
-- INSERT INTO `emp_accessrights` (`Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`, `Form_Id`, `Employee_Id`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES ('1', '1', '1', '0', '0', '0', '0', '251', '1', '0', '1', NOW(), NULL, NULL);

-- 11November2023

-- INSERT INTO `emp_accessrights` (`Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`, `Form_Id`, `Employee_Id`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES ('1', '1', '1', '0', '0', '0', '0', '256', '1', '0', '1', NOW(), NULL, NULL);

-- -- Revathi #7904
-- INSERT INTO `emp_accessrights` (`Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`, `Form_Id`, `Employee_Id`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES ('1', '0', '1', '0', '0', '0', '0', '257', '1', '0', '1', NOW(), NULL, NULL);

-- -- Suganya - update access for form id 253, 256
-- UPDATE `emp_accessrights` SET `Role_Delete` = '0' WHERE `emp_accessrights`.`Form_Id` = 253 AND `emp_accessrights`.`Employee_Id` = 1;
-- UPDATE `emp_accessrights` SET `Role_Delete` = '1' WHERE `emp_accessrights`.`Form_Id` = 256 AND `emp_accessrights`.`Employee_Id` = 1;

-- -- Pasha
-- UPDATE `emp_job` SET `Roles_Id` = '1' WHERE `emp_job`.`Employee_Id` = 1;
-- INSERT INTO `emp_accessrights` (`Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`, `Form_Id`, `Employee_Id`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES ('1', '1', '1', '1', '0', '0', '0', '258', '1', '0', '1', NOW(), NULL, NULL);

-- 30Nov2023 DeploymentQueries
-- Abhishek
-- INSERT INTO `emp_accessrights` (`Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`, `Form_Id`, `Employee_Id`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES ('1', '0', '1', '0', '0', '0', '0', '259', '1', '0', '1', NOW(), NULL, NULL);

-- -- -- suhan #7969
-- INSERT INTO `emp_accessrights` (`Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`, `Form_Id`, `Employee_Id`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES ('1', '0', '1', '0', '0', '0', '0', '260', '1', '0', '1', NOW(), NULL, NULL);

-- 30Nov2023 DeploymentQueries

-- 02Jan2023 Deployment Queries
-- INSERT INTO `emp_accessrights` (`Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`, `Form_Id`, `Employee_Id`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES ('1', '0', '1', '0', '0', '0', '0', '261', '1', '0', '1', NOW(), NULL, NULL);

-- INSERT INTO `emp_accessrights` (`Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`, `Form_Id`, `Employee_Id`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES ('1', '0', '1', '0', '0', '0', '0', '262', '1', '0', '1', NOW(), NULL, NULL);

-- 07Jan2023 Deployment Queries
-- -- Pasha #8043
-- INSERT INTO `emp_accessrights` (`Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`, `Form_Id`, `Employee_Id`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES ('1', '0', '1', '0', '0', '0', '0', '263', '1', '0', '1', NOW(), NULL, NULL);

-- -- suganya #8098
-- UPDATE `emp_accessrights` SET `Role_Update` = '1' WHERE `emp_accessrights`.`Form_Id` = 140 AND `emp_accessrights`.`Employee_Id` = 1;
-- INSERT INTO `emp_accessrights` (`Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`, `Form_Id`, `Employee_Id`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES ('1', '0', '1', '0', '0', '0', '0', '264', '1', '0', '1', NOW(), NULL, NULL);
-- 18Jan2024
-- Converting emp_accessrights to rolesbased_access_control.here after for providing universal access we need to prepare a query for rolesbased_access_control
-- DELETE FROM `rolesbased_access_control` WHERE Roles_Id=1;

-- INSERT INTO rolesbased_access_control (Roles_Id, Form_Id, Role_View, Role_Add, Role_Update, Role_Delete, Role_Optional_Choice, Role_Hr_Group, Role_Payroll_Group) SELECT 1, Form_Id, Role_View, Role_Add, Role_Update, Role_Delete, Role_Optional_Choice, Role_Hr_Group, Role_Payroll_Group FROM emp_accessrights WHERE Employee_Id = 1;

-- 02Feb2024 Deployment Queries
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '265', '1', '0', '1', '1', '0', '0', '0');

-- 10Feb2024 Deployment Queries
-- DELETE FROM `rolesbased_access_control` WHERE Form_Id=262 AND Roles_Id=1;
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '262', '1', '0', '1', '0', '0', '0', '0');

                                                  -- 24Feb2024 Deployment Queries
-- Abhishek #8461
-- INSERT INTO `emp_accessrights` (`Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`, `Form_Id`, `Employee_Id`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES ('1', '0', '0', '0', '0', '0', '0', '266', '1', '0', '1', NOW(), NULL, NULL);
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '266', '1', '0', '0', '0', '0', '0', '0');

-- -- 17Mar2024 Deployment Queries
-- -- suganya - #8597
-- INSERT INTO `emp_accessrights` (`Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`, `Form_Id`, `Employee_Id`, `Lock_Flag`, `Added_By`, `Added_On`, `Updated_By`, `Updated_On`) VALUES ('1', '0', '1', '0', '0', '0', '0', '267', '1', '0', '1', NOW(), NULL, NULL);
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '267', '1', '0', '1', '0', '0', '0', '0');

-- 25March2024 Deployment Queries
-- suganya #8597
-- run this query only in template DB and capricecloud production domain
-- UPDATE `reimbursement_settings` SET `Enable_Workflow` = 'Yes' WHERE 1;

                                            -- 29March2024 Deployment Queries
-- #8618 added on mar 23 timesheet workflow
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '268', '1', '0', '0', '0', '0', '0', '0');

-- 02April2024 Deployment Queries
-- DELETE FROM `rolesbased_access_control` WHERE Form_Id=262 AND Roles_Id=1;
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '262', '1', '0', '1', '0', '0', '0', '0');
-- 02April2024 Deployment Queries

-- 12April2024 Deployment Queries
-- DELETE FROM `rolesbased_access_control` WHERE Form_Id=262 AND Roles_Id=1;
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '262', '1', '1', '1', '1', '0', '0', '0');
-- 12April2024 Deployment Queries

-- suhan organization group 29th april #8785
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '269', '1', '1', '1', '1', '0', '0', '0');

-- -- Abhishek #8861
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '275', '1', '1', '0', '0', '0', '0', '0');
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '271', '1', '0', '1', '0', '0', '0', '0');
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '272', '1', '1', '0', '0', '0', '0', '0');
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '273', '1', '0', '1', '0', '0', '0', '0');
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '274', '1', '0', '1', '0', '0', '0', '0');

-- suganya - #8928
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '276', '1', '0', '0', '0', '0', '0', '0'), ('1', '277', '1', '0', '1', '0', '0', '0', '0');

-- -- revathi - #9036
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '278', '1', '0', '0', '0', '0', '0', '0');

-- -- suhan july 9
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '279', '1', '0', '1', '0', '0', '0', '0');

-- -- Revathi #9081 - July 11
-- DELETE FROM `rolesbased_access_control` WHERE `rolesbased_access_control`.`Form_Id` = 186;
-- DELETE FROM `rolesbased_access_control` WHERE `rolesbased_access_control`.`Form_Id` = 278;

-- Pasha #9246
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '280', '1', '1', '1', '1', '0', '0', '0');


-- Aug 27 Soundar #9257
-- -- Super Admin Role
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '281', '1', '1', '1', '1', '1', '0', '0');

-- -- Super Admin Role
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '282', '1', '1', '1', '1', '1', '0', '0');

-- -- Super Admin Role
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '283', '1', '1', '1', '1', '1', '0', '0');

-- -- suhan 9325 ip address tracking
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '284', '1', '1', '1', '1', '0', '0', '0');

-- -- September 10 Soundar #9298
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '285', '1', '1', '1', '1', '0', '0', '0');
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '286', '1', '1', '1', '0', '0', '0', '0');

-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '287', '1', '1', '1', '0', '0', '0', '0');

-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '288', '1', '0', '0', '0', '0', '0', '0');

-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '289', '1', '0', '0', '0', '0', '0', '0');

-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '290', '1', '1', '1', '1', '0', '0', '0');

-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '291', '1', '1', '1', '0', '0', '0', '0');

-- Separation Form (Run on 20th September 2024)
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '292', '1', '1', '1', '1', '0', '0', '0');

-- -- Pasha #9350
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '293', '1', '1', '1', '1', '0', '0', '0');
-- -- suhan #9341
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '294', '1', '0', '0', '0', '1', '0', '0');

-- -- suhan #9341
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '295', '1', '0', '0', '0', '0', '0', '0');


-- -- Soundar #9435
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '296', '1', '0', '1', '0', '0', '0', '0');
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '297', '1', '0', '0', '0', '0', '0', '0');


-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '298', '1', '1', '1', '0', '0', '0', '0');
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '299', '1', '1', '1', '0', '0', '0', '0');

-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '300', '1', '1', '1', '0', '0', '0', '0');

-- -- Pasha #9474
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '301', '1', '1', '1', '0', '0', '0', '0');
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '302', '1', '1', '1', '0', '0', '0', '0');

-- Suhan Candidate withdrawal
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '301', '1', '0', '1', '0', '0', '0', '0');

-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '303', '1', '0', '0', '0', '0', '0', '0');

-- 30 Nov 2024 Deployment Queries
-- -- #9617 Adding default access form attendance form in employee self service and my team module
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) 
-- VALUES ('1', '304', '1', '1', '1', '0', '0', '0', '0');

-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) 
-- VALUES ('1', '305', '1', '1', '1', '1', '1', '1', '0');

-- -- suhan #9647
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '306', '1', '1', '1', '0', '0', '0', '0');

-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '307', '1', '1', '1', '0', '0', '0', '0');

-- --Soundar Dec-16 #9704
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '308', '1', '1', '1', '1', '0', '0', '0');

-- -- Pasha #9719
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '309', '1', '1', '1', '0', '0', '0', '0');

-- -- suhan #9756
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '310', '1', '1', '1', '1', '0', '0', '0');

-- -- Pasha #9612
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '311', '1', '1', '1', '1', '0', '0', '0');


-- -- Pasha #9612
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '312', '1', '0', '0', '0', '0', '0', '0');

-- -- 9514 Adding attendance approval form in my team module
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '313', '1', '1', '1', '1', '0', '0', '0');

-- soundar
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '314', '1', '0', '0', '0', '0', '0', '0');

-- DELETE FROM rolesbased_access_control where Form_Id IN('304','305','313')

-- -- -- #9617 Adding default access form attendance form in employee self service and my team module
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) 
-- VALUES ('1', '304', '1', '1', '1', '0', '0', '0', '0');

-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) 
-- VALUES ('1', '305', '1', '1', '1', '1', '1', '1', '0');
-- -- 9514 Adding attendance approval form in my team module
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '313', '1', '1', '1', '1', '0', '0', '0');

-- -- Pasha #9890
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '315', '1', '1', '1', '1', '0', '0', '0');
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '316', '1', '1', '1', '1', '0', '0', '0');

-- -- suganya - #9908
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '317', '1', '1', '1', '1', '1', '0', '0');
-- suhan #9922
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '318', '1', '1', '1', '1', '0', '0', '0');
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '319', '1', '1', '1', '1', '0', '0', '0');

-- -- suhan#9617
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '320', '1', '1', '1', '1', '0', '0', '0');
-- -- suhan #9943
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '321', '1', '1', '1', '1', '0', '0', '0');

-- -- suhan #9943
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '322', '1', '1', '1', '1', '0', '0', '0');
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '323', '1', '1', '1', '1', '0', '0', '0');
-- suganya #9708
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '324', '1', '1', '1', '1', '1', '0', '0');


-- -- FEB 22 Deployment Queries
-- -- Shanthi #9979
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '325', '1', '0', '0', '0', '0', '0', '0');

-- -- Pasha #10031
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '329', '1', '1', '1', '1', '0', '0', '0');

-- -- Pasha #10056
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES 
-- ('1', '330', '1', '1', '1', '1', '0', '0', '0'),
-- ('1', '331', '1', '1', '1', '1', '0', '0', '0'),
-- ('1', '332', '1', '1', '1', '1', '0', '0', '0'),
-- ('1', '333', '1', '1', '1', '1', '0', '0', '0'),
-- ('1', '334', '1', '1', '1', '1', '0', '0', '0'),
-- ('1', '335', '1', '1', '1', '1', '0', '0', '0');

--  -- Sanket #10059
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES 
-- ('1', '336', '1', '1', '1', '1', '0', '0', '0'),
-- ('1', '337', '1', '1', '1', '1', '0', '0', '0'),
-- ('1', '338', '1', '1', '1', '1', '0', '0', '0'),
-- ('1', '339', '1', '1', '1', '1', '0', '0', '0');

-- -- suhan#10074
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES 
-- ('1', '340', '1', '1', '1', '1', '0', '0', '0'),
-- ('1', '341', '1', '1', '1', '1', '0', '0', '0'),
-- ('1', '342', '1', '1', '1', '1', '0', '0', '0'),
-- ('1', '343', '1', '1', '1', '1', '0', '0', '0'),
-- ('1', '344', '1', '1', '1', '1', '0', '0', '0');

-- Pasha #10100
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES 
-- ('1', '345', '1', '1', '1', '1', '0', '0', '0'),
-- ('1', '346', '1', '1', '1', '1', '0', '0', '0');

-- SURESH #10123
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES 
-- ('1', '348', '1', '0', '0', '0', '0', '0', '0');

-- -- Pasha #10114
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES 
-- ('1', '347', '1', '1', '1', '1', '0', '0', '0');

-- SURESH #10123
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES 
-- ('1', '350', '1', '0', '0', '0', '0', '0', '0');

-- -- suhan #10139
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES 
-- ('1', '349', '1', '1', '1', '1', '0', '0', '0');

-- SURESH #10152
-- DELETE FROM marginal_releif WHERE Tax_Regime='New Regime';

-- INSERT INTO `marginal_releif` (`Min_Range`, `Max_Range`, `Threshold`, `Tax_Regime`) VALUES
-- (120000,1270588,60000,'New Regime');

-- -- Pasha #10144
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES
-- ('1', '351', '1', '1', '1', '1', '0', '0', '0');

-- -- suhan#10209
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES
-- ('1', '352', '1', '1', '1', '1', '0', '0', '0'),
-- ('1', '353', '1', '1', '1', '1', '0', '0', '0');

-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES
-- ('1', '354', '1', '1', '1', '1', '0', '0', '0'),
-- ('1', '355', '1', '1', '1', '1', '0', '0', '0');

-- -- sai#10058
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES
-- ('1', '356', '1', '1', '1', '1', '0', '0', '0');

-- -- sanket#10012
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES
-- ('1', '357', '1', '1', '1', '1', '0', '0', '0');
-- UPDATE `rolesbased_access_control` SET `Role_Optional_Choice` = '1' WHERE `Roles_Id` = '1' AND `Form_Id` = '357';

-- -- suhan#10243
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES
-- ('1', '359', '1', '1', '1', '1', '0', '0', '0');

-- -- suhan#10342
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES
-- ('1', '359', '1', '1', '1', '1', '0', '0', '0');
-- -- suhan#10342
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES
-- ('1', '359', '1', '1', '1', '1', '0', '0', '0');

-- -- suhan##10387
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES
-- ('1', '360', '1', '1', '1', '1', '0', '0', '0');

-- delete from rolesbased_access_control where Form_Id in (206,207);

-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES
-- ('1', '361', '1', '1', '1', '1', '0', '0', '0');

-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES
-- ('1', '206', '1', '1', '1', '1', '0', '0', '0');

-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES
-- ('1', '207', '1', '1', '1', '1', '0', '0', '0');

-- -- sai#10115
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES
-- ('1', '362', '1', '1', '1', '1', '0', '0', '0');

-- -- suhan#10446
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES
-- ('1', '363', '1', '1', '1', '1', '0', '0', '0');


-- -- suhan#10446
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES
-- ('1', '364', '1', '1', '1', '1', '0', '0', '0'),
-- ('1', '365', '1', '1', '1', '1', '0', '0', '0');

-- -- suhan#10472
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES
-- ('1', '366', '1', '1', '1', '1', '0', '0', '0');

-- -- Sanket  July 1st 2025 #10012
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES
-- ('1', '367', '1', '1', '1', '1', '0', '0', '0'),
-- ('1', '368', '1', '1', '1', '1', '0', '0', '0'),
-- ('1', '369', '1', '1', '1', '1', '0', '0', '0');

-- -- Abhishek #10510
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '370', '1', '0', '0', '0', '0', '0', '0');

-- -- Pasha
-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '371', '1', '1', '1', '1', '0', '0', '0');

-- -- Soundar July 11 2025 #10577
-- INSERT INTO rolesbased_access_control (Roles_Id, Form_Id, Role_View, Role_Add, Role_Update, Role_Delete, Role_Optional_Choice, Role_Hr_Group, Role_Payroll_Group) VALUES ('1', '372', '1', '1', '1', '0', '0', '0', '0');

-- -- soundar July 18 #10602
-- INSERT INTO rolesbased_access_control (Roles_Id, Form_Id, Role_View, Role_Add, Role_Update, Role_Delete, Role_Optional_Choice, Role_Hr_Group, Role_Payroll_Group) 
-- VALUES ('1', '374', '1', '0', '0', '0', '0', '0', '0'),('1', '375', '1', '0', '0', '0', '0', '0', '0'),('1', '376', '1', '0', '0', '0', '0', '0', '0');

-- Sanket July 22 2025 #10609
-- INSERT INTO rolesbased_access_control (Roles_Id, Form_Id, Role_View, Role_Add, Role_Update, Role_Delete, Role_Optional_Choice, Role_Hr_Group, Role_Payroll_Group) VALUES ('1', '373', '1', '0', '1', '0', '0', '0', '0');

-- AUG3 Deployment Queries
-- -- suhan#10698
-- UPDATE payroll_general_settings set Enable_Salary_Template=1;

-- -- suhan#10387
-- INSERT INTO rolesbased_access_control (Roles_Id, Form_Id, Role_View, Role_Add, Role_Update, Role_Delete, Role_Optional_Choice, Role_Hr_Group, Role_Payroll_Group) VALUES ('1', '377', '1', '1', '1', '1', '0', '0', '0');
-- INSERT INTO rolesbased_access_control (Roles_Id, Form_Id, Role_View, Role_Add, Role_Update, Role_Delete, Role_Optional_Choice, Role_Hr_Group, Role_Payroll_Group) VALUES ('1', '378', '1', '1', '1', '1', '0', '0', '0');

-- -- Pasha #10735 - Per Diem Configuration

-- INSERT INTO `rolesbased_access_control` (`Roles_Id`, `Form_Id`, `Role_View`, `Role_Add`, `Role_Update`, `Role_Delete`, `Role_Optional_Choice`, `Role_Hr_Group`, `Role_Payroll_Group`) VALUES ('1', '379', '1', '1', '1', '1', '0', '0', '0');

-- -- Soundar Aguest 07 #10713
-- INSERT INTO rolesbased_access_control (Roles_Id, Form_Id, Role_View, Role_Add, Role_Update, Role_Delete, Role_Optional_Choice, Role_Hr_Group, Role_Payroll_Group) 
-- VALUES ('1', '380', '1', '1', '1', '1', '0', '0', '0');

-- AUG15 Deployment Queries
-- suhan#10769
-- INSERT INTO rolesbased_access_control (Roles_Id, Form_Id, Role_View, Role_Add, Role_Update, Role_Delete, Role_Optional_Choice, Role_Hr_Group, Role_Payroll_Group) 
-- VALUES ('1', '381', '1', '1', '1', '1', '0', '0', '0'),
-- ('1', '382', '1', '1', '1', '1', '0', '0', '0'),
-- ('1', '383', '1', '1', '1', '1', '0', '0', '0');

-- Pasha #10796
INSERT INTO rolesbased_access_control (Roles_Id, Form_Id, Role_View, Role_Add, Role_Update, Role_Delete, Role_Optional_Choice, Role_Hr_Group, Role_Payroll_Group) VALUES ('1', '384', '1', '1', '1', '1', '0', '0', '0');
INSERT INTO rolesbased_access_control (Roles_Id, Form_Id, Role_View, Role_Add, Role_Update, Role_Delete, Role_Optional_Choice, Role_Hr_Group, Role_Payroll_Group) VALUES ('1', '385', '1', '1', '1', '1', '0', '0', '0');
INSERT INTO rolesbased_access_control (Roles_Id, Form_Id, Role_View, Role_Add, Role_Update, Role_Delete, Role_Optional_Choice, Role_Hr_Group, Role_Payroll_Group) VALUES ('1', '386', '1', '1', '1', '1', '0', '0', '0');