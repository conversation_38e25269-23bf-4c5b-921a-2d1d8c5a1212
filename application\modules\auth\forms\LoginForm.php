<?php
//=========================================================================================
//=========================================================================================
/* Program        : LoginForm.php													    */
/* Property of Caprice Technologies Pvt Ltd,
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,
* Coimbatore, Tamilnadu, India.															*/
/* All Rights Reserved.            														*/
/* Use of this material without the express consent of Caprice Technologies
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law.*/
/*                                                                                    	*/
/* Description    : Form for Employee Login												*/
/*                                                                                   	*/
/*                                                                                    	*/
/*Revisions      :                                                                    	*/
/*Version    Date           Authors                  Description                       	*/
/*0.1        30-May-2013    Narmadha        		Initial Version       	            */
/*                                                                                    	*/
/*                                                                                    	*/
/*                                                                                    	*/
//=========================================================================================
//=========================================================================================
class Auth_Form_LoginForm extends Zend_Form
{

    protected $_basePath = null;
    public function init()
    {
        $this->_basePath = new Zend_View_Helper_BaseUrl();
        
        $this->setAttribs(array('id'=>'HrappLoginForm', 'class'=>'hrapp_validation'));
        
        $tdDecorator = array('ViewHelper',
        array('Description', array('escape' => false, 'tag' => false),array('placement' => Zend_Form_Decorator_Abstract::APPEND, 'tag' => 'em')),
        array(array('data'=>'HtmlTag'), array('tag' => 'td' )),
        array('Label', array('escape'=>false,'tag' => 'td')),
        array(array('row'=>'HtmlTag'),array('tag'=>'tr')));
        
        $hrappText = new Zend_Form_Element_Text('appLbl');
        $hrappText->setValue('')->setDescription('<br/>')
        ->setDecorators(array('ViewHelper',
        array('Description', array('escape' => false, 'tag' => false),array('placement' => Zend_Form_Decorator_Abstract::APPEND, 'tag' => 'em')),
        array(array('data'=>'HtmlTag'), array('tag' => 'td' , 'colspan'=>'2')),
        array(array('row'=>'HtmlTag'),array('tag'=>'tr'))));
        
        $username = $this->createElement('text','username');
        $username->setLabel('Username <label class="short_explanation">*</label>')
        ->addFilters(array('StringTrim', 'StripTags'))
        ->setAttribs(array('class'=>'validate[required] text-input text_fullname'))
        ->setRequired(true)->setDecorators($tdDecorator);
        
        $password = $this->createElement('password','password');
        $password->setLabel('Password <label class="short_explanation">*</label>')
        ->setAttribs(array('class'=>'validate[required] text-input text_fullname'))
        ->setRequired(true)->setDecorators($tdDecorator);
        
        $rememberMe = $this->createElement('checkbox','remember_me');
        $rememberMe->setLabel('')->setDescription('Remember Me')->setDecorators($tdDecorator);
        
        $signin = $this->createElement('submit','signin');
        $signin->setLabel('Sign In')
        ->setIgnore(true)->setDecorators(array('ViewHelper',
        array('Description', array('escape' => false, 'tag' => false),array('placement' => Zend_Form_Decorator_Abstract::APPEND, 'tag' => 'em')),
        array(array('data'=>'HtmlTag'), array('tag' => 'td', 'colspan'=>2)),
        array(array('row'=>'HtmlTag'),array('tag'=>'tr'))));
        
        $hrappForgotPwd = new Zend_Form_Element_Text('appPwd');
        $hrappForgotPwd->setValue('')->setDescription('<div style="margin-top:-5%"><a id="lnk_fgPwd" href="'. $this->_basePath->baseUrl('auth/index/lost') .'">Forgot your password?</a></div>')
        ->setDecorators(array('ViewHelper',
        array('Description', array('escape' => false, 'tag' => false),array('placement' => Zend_Form_Decorator_Abstract::APPEND, 'tag' => 'em')),
        array(array('data'=>'HtmlTag'), array('tag' => 'td' , 'colspan'=>'2')),
        array(array('row'=>'HtmlTag'),array('tag'=>'tr', 'style'=>'height:40px;'))));
        
        $formDecoration = array('FormElements', array(array('data'=>'HtmlTag'), array('tag'=>'table')), 'Form');
        
        $this->addElements(array($hrappText, $username, $password, $rememberMe, $signin, $hrappForgotPwd))->setDecorators($formDecoration);
    }
}