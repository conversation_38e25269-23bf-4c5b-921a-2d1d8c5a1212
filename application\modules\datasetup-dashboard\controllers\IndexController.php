<?php
//==========================================================================================
//==========================================================================================
/* Program : IndexController.php												          *
 * Property of Caprice Technologies Pvt Ltd,											  *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,										  *
 * Coimbatore, Tamilnadu, India.														  *
 * All Rights Reserved.            														  *
 * Use of this material without the express consent of Caprice Technologies				  *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law.  *
 *                                                                                    	  *
 * Description : DataSetup Dashboard IndexController maintains the whether the            *
 *     Prerequisite Data has been added or not and Status will be added based on it.      *
 * 																						  *
 * Revisions :                                                                    	      *
 *  Version    Date                Author                  Description                    *
 *  0.1      22-Sep-2017        Nivethitha                Initial Version         	      */
//=========================================================================================
//=========================================================================================

include APPLICATION_PATH."/validations/Validations.php"; 
class DatasetupDashboard_IndexController extends Zend_Controller_Action
{

    protected $_formName = 'Data Setup Dashboard';

    protected $_dbCommonFunction = null;

    protected $_hrappMobile = null;

    public function init()
    {
        $this->_hrappMobile = new Application_Model_DbTable_HrappMobile();

        if ($this->_hrappMobile->checkAuth())
        {
            $this->_dbAccessRights = new Default_Model_DbTable_AccessRights();
            $this->_dbDataSetup = new DatasetupDashboard_Model_DbTable_DataSetupDashboard();
            $this->_dbCommonFunction   = new Application_Model_DbTable_CommonFunction();
            
            $userSession     = $this->_dbCommonFunction->getUserDetails ();
            $this->_logEmpId = $userSession['logUserId'];
            
            $this->_datasetupAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, 'Data Setup Dashboard');
        }
        else
        {
            if (Zend_Session::namespaceIsset('lastRequest'))
                Zend_Session:: namespaceUnset('lastRequest');
            
            $session = new Zend_Session_Namespace('lastRequest');
            $session->lastRequestUri = 'datasetup-dashboard/index';
            $this->_redirect('auth');
        }

    }

    public function indexAction()
    {
        $checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();
        
        if ($checkSessionAuth)
        {
            $this->_helper->layout->disableLayout();
            $this->_helper->layout->setLayout('admin_layout');
            if($this->_datasetupAccessRights['Admin'] == 'admin')
            {
                // To Mask/Unmask panel in datasetup dashboard when the Data setup dashboard page is loaded
                $this->view->datasetupForms = $this->_dbDataSetup->getDataSetupForms();
            }
            else
            {
                $this->view->datasetupForms = '';
            }
        } else {
			$this->_redirect('auth');
		}
    }

    // To get Datasetup details for setting mask
    public function setMaskPrerequisitesAction()
    {
        $this->_helper->layout->disableLayout();
        
        $ajaxContext = $this->_helper->getHelper('AjaxContext');
        $ajaxContext->addActionContext('set-mask-prerequisites', 'json')->initContext();

        $this->view->result = $this->_dbDataSetup->validateRetrieveDataSetupForms();
    }
}



