<?php
//=========================================================================================
//=========================================================================================
/* Program        : BillingController.php												 *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description    : Details of invoice billing for the registered subdomain which will   *
 * 					be visible only for registered user.								 *
 *                                                                                   	 *
 *                                                                                    	 *
 *	Revisions      :                                                                     *
 *		Version         Date             Author               Description                *
 *		  0.1        30-May-2013        Narmadha            Initial Version         	 *
 *                                                                                    	 *
 *                                                                                    	 */
//=========================================================================================
//=========================================================================================
//include APPLICATION_PATH."/PHP_Kit/IFRAME_KIT/Crypto.php";
class Default_BillingController extends Zend_Controller_Action
{
	protected $_session = null;
	protected $_logEmpId = null;
	protected $_formName = 'Billing';
	protected $_dbBilling = null;
	protected $_billAccess = null;
	protected $_orgCode = null;
	protected $_ehrTables = null;
	protected $_dbPersonal = null;
	protected $_basePath = null;
	protected $_dbAccessRights = null;
    protected $_hrappMobile = null;
	
	public function init()
	{
		$this->_hrappMobile = new Application_Model_DbTable_HrappMobile();

		if ( $this->_hrappMobile->checkAuth() )
		{
			//      	$this->_crypto = new crypto();
			$this->_basePath       = new Zend_View_Helper_BaseUrl();
			$this->_dbBilling      = new Default_Model_DbTable_Billing();
			$this->_ehrTables      = new Application_Model_DbTable_Ehr();
			$this->_dbPersonal     = new Employees_Model_DbTable_Personal();
			$this->_dbAccessRights = new Default_Model_DbTable_AccessRights();
			$this->_dbCommonFun    = new Application_Model_DbTable_CommonFunction();
			$userSession       = $this->_dbCommonFun->getUserDetails ();
			$this->_logEmpId   = $userSession['logUserId'];
			$this->_orgCode    = $this->_ehrTables->getOrgCode();
			$this->_billAccess = $this->_dbBilling->billingAccess($this->_logEmpId, $this->_orgCode);
			//$this->_dbAccessRights->refreshUserSessionTimestamp($this->_logEmpId);
		}
		else
		{
			if(Zend_Session::namespaceIsset('lastRequest'))
			{
				Zend_Session:: namespaceUnset('lastRequest');
			}
			$session = new Zend_Session_Namespace('lastRequest');
			$session->lastRequestUri = 'default/billing';
			$this->_redirect('auth');
		}
	}

	public function indexAction()
	{
        $checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

        if ($checkSessionAuth)
		{
			$this->_helper->layout()->disableLayout()->setLayout('admin_layout');
		
			$this->view->formName = $this->_formName;
			$this->view->billingAccess = array('View' => $this->_billAccess);
			
			if ($this->_billAccess >= 1 && !empty($this->_billAccess))
			{
				$this->view->ratePlan = $this->_dbBilling->ratePlan($this->_orgCode);
				
				$dbOrgSettings = new Organization_Model_DbTable_OrgSettings();
				$this->view->contactInfo = $dbOrgSettings->viewOrgContactDetail($this->_orgCode);
				
				$dbManager = new Default_Model_DbTable_Manager();
				$this->view->managersInfo = $dbManager->managerName(0, 0, 'Emp_First_Name', 'ASC', '', '', '', '', '', 'Billing', '');
			}
			
			$this->view->dateformat = $this->_ehrTables->orgDateformat();
		} else {
			$this->_redirect('auth');
		}
	}
	
	/**
	 *	listBillingAction() used to list billing details in grid
	*/
	public function listBillingAction()
	{
		$this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
		{
			if ($this->_billAccess>=1 && !empty($this->_billAccess))
			{
				$ajaxContext = $this->_helper->getHelper('AjaxContext');
				$ajaxContext->addActionContext('list-billing', 'json')->initContext();
				
				$sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
				
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
                $billingDateStart = $this->_getParam('filterBillingDateStart',null);
				$billingDateStart = filter_var($billingDateStart, FILTER_SANITIZE_STRIPPED);
							
				$billingDateEnd = $this->_getParam('filterBillingDateEnd', null);
				$billingDateEnd = filter_var($billingDateEnd, FILTER_SANITIZE_STRIPPED);
				
				$activeEmpAmtStart = $this->_getParam('filterActiveEmpAmtStart', null);
				$activeEmpAmtStart = filter_var($activeEmpAmtStart, FILTER_SANITIZE_STRIPPED);
                
                $activeEmpAmtEnd = $this->_getParam('filterActiveEmpAmtEnd', null);
				$activeEmpAmtEnd = filter_var($activeEmpAmtEnd, FILTER_SANITIZE_STRIPPED);
				
				$inActiveEmpAmtStart = $this->_getParam('filterInActiveEmpAmtStart', null);
				$inActiveEmpAmtStart = filter_var($inActiveEmpAmtStart, FILTER_SANITIZE_STRIPPED);
                
                $inActiveEmpAmtEnd = $this->_getParam('filterInActiveEmpAmtEnd', null);
				$inActiveEmpAmtEnd = filter_var($inActiveEmpAmtEnd, FILTER_SANITIZE_STRIPPED);
				
				$discountAmtStart = $this->_getParam('filterDiscountAmtStart', null);
				$discountAmtStart = filter_var($discountAmtStart, FILTER_SANITIZE_STRIPPED);
                
                $discountAmtEnd = $this->_getParam('filterDiscountAmtEnd', null);
				$discountAmtEnd = filter_var($discountAmtEnd, FILTER_SANITIZE_STRIPPED);
				
				$totalAmtStart = $this->_getParam('filterTotalAmtStart', null);
				$totalAmtStart = filter_var($totalAmtStart, FILTER_SANITIZE_STRIPPED);
                
                $totalAmtEnd = $this->_getParam('filterTotalAmtEnd', null);
				$totalAmtEnd = filter_var($totalAmtEnd, FILTER_SANITIZE_STRIPPED);
                
                $billingStatus = $this->_getParam('filterBillingStatus', null);
				$billingStatus = filter_var($billingStatus, FILTER_SANITIZE_STRIPPED);
				
				$searchArray  = array('Billing_Start_Date'             => $billingDateStart,
                                      'Billing_End_Date'               => $billingDateEnd,
                                      'Active_Employee_Amount_Start'   => $activeEmpAmtStart,
                                      'Active_Employee_Amount_End'     => $activeEmpAmtEnd,
									  'InActive_Employee_Amount_Start' => $inActiveEmpAmtStart,
                                      'InActive_Employee_Amount_End'   => $inActiveEmpAmtEnd,
									  'Discount_Amount_Start'          => $discountAmtStart,
                                      'Discount_Amount_End'            => $discountAmtEnd,
									  'Total_Amount_Start'             => $totalAmtStart,
                                      'Total_Amount_End'               => $totalAmtEnd,
									  'Billing_Status'                 => $billingStatus);
                
                $this->view->result = $this->_dbBilling->listBilling($page, $rows, $sortField, $sortOrder, $searchAll,
																						    $searchArray,$this->_logEmpId);
                
				//$this->view->result = $this->_dbBilling->listBilling($sortField, $sortOrder, $page, $rows, $searchAll, $this->_logEmpId);
			}
		}
		else
		{
			$this->_helper->redirector('index', 'billing', 'default');
		}
	}
	
	/**
	 *	showAuditPaymentAction() used to list payment history details based on invoice no
	*/
	public function showAuditPaymentAction()
	{
		$this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('show-audit-payment', 'json')->initContext();
			
			$invoiceNo = $this->_getParam('Invoice_No', null);
			$invoiceNo = filter_var($invoiceNo, FILTER_SANITIZE_NUMBER_INT);
			
			if (!empty($invoiceNo) && $this->_billAccess >= 1 && !empty($this->_billAccess))
			{
				$sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
				
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$this->view->result = $this->_dbBilling->listPaymentHistory($invoiceNo, $sortField, $sortOrder, $page, $rows, $searchAll);
			}
		}
		else
		{
			$this->_helper->redirector('index', 'billing', 'default');
		}
	}
	
	/**
	 *	Check Email Report record exists
	*/
	public function checkreportAction()
	{
		$this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
		{
			if ($this->_billAccess >= 1 && !empty($this->_billAccess))
			{
				$ajaxContext = $this->_helper->getHelper('AjaxContext');
				$ajaxContext->addActionContext('checkreport', 'html')->initContext();
				
				$billDate = $this->_getParam('bill_date', null);
				
				$discount = $this->_getParam('discount', null);
				$discount = filter_var($discount, FILTER_SANITIZE_STRIPPED);
				
				$transMonth = $this->_getParam('month', null);
				$transMonth = filter_var($transMonth, FILTER_SANITIZE_STRIPPED);
				
				$activeAmt = $this->_getParam('activeAmt', null);
				$activeAmt = filter_var($activeAmt, FILTER_SANITIZE_STRIPPED);
				
				$inactiveAmt = $this->_getParam('inactiveAmt', null);
				$inactiveAmt = filter_var($inactiveAmt, FILTER_SANITIZE_STRIPPED);
				
				$totalAmt = $this->_getParam('totalAmt', null);
				$totalAmt = filter_var($totalAmt, FILTER_SANITIZE_STRIPPED);
				
				$status = $this->_getParam('status', null);
				$status = filter_var($status, FILTER_SANITIZE_STRIPPED);
				
				$transA = $this->_getParam('trans1', null);
				$transB = $this->_getParam('trans2', null);
				$transC = $this->_getParam('trans3', null);
				$transD = $this->_getParam('trans4', null);
				$transE = $this->_getParam('trans5', null);
				
				$this->view->billingCount = $this->_dbBilling->billingReportCount($discount, $transMonth, $activeAmt, $inactiveAmt,
																				  $totalAmt, $status, $transA, $transB, $transC, $transD,
																				  $transE, $this->_orgCode, $billDate);
			}
		}
		else
		{
			$this->_helper->redirector('index', 'billing', 'default');
		}
	}
	
	/**
	 *	emailBillingAction() used to send email to our receipent  with billing transaction details
	*/
	public function emailBillingAction()
	{
		$this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
		{
			$billingDate = $this->_getParam('bill_date', null);
			$billingDate = filter_var($billingDate, FILTER_SANITIZE_STRIPPED);
			
			$invoiceNo = $this->_getParam('Invoice_No', null);
			$invoiceNo = filter_var($invoiceNo, FILTER_SANITIZE_STRIPPED);			
			
			$dbOrgSettings = new Organization_Model_DbTable_OrgSettings();
			$contactInfo = $dbOrgSettings->viewOrgContactDetail($this->_orgCode);
			$fromName  = $contactInfo['First_Name'].' '.$contactInfo['Last_Name'];
			$fromEmail = $contactInfo['Email_Id'];			
			
			if ( $this->getRequest()->isPost() )
			{
				$formData = $this->getRequest()->getPost();
				
				$recipientsTo = $this->_getParam('recipients', null);
				//$recipientsTo = filter_var($recipientsTo, FILTER_SANITIZE_STRIPPED);
				
				$externalRecipients = $this->_getParam('externalRecipients', null);
				//$externalRecipients = filter_var($externalRecipients, FILTER_SANITIZE_STRIPPED);
			
				$subject = $this->_getParam('subject', null);
				$subject = filter_var($subject, FILTER_SANITIZE_STRIPPED);
				
				$message = $this->_getParam('message', null);
				$message = filter_var($message, FILTER_SANITIZE_STRIPPED);
			
					if(!empty($externalRecipients))
					{
						//emailValidation
						$externalRecipients = explode(',', $externalRecipients);
						$exRecipientValid = array_filter(filter_var_array($externalRecipients, FILTER_VALIDATE_EMAIL));
					}
					else
					{
						$externalRecipients = array();
					}					
			
					if((empty($exRecipient) || (count($exRecipient)==count($exRecipientValid))) && $recipientsTo!='' && $subject != '')
					{
						//$billingReport = $this->_dbBilling->viewTransactionDetails($invoiceNo, $this->_logEmpId);
						
						$billingReport = $this->_dbBilling->mailBillingExport($invoiceNo);						
				
						if(!empty($billingReport) && count($billingReport)>0)
						{
							$setBodyMessage = $this->sendEmailReport($billingReport, $billingDate);
							
							//$setBodyMessage='';
							if(!empty($setBodyMessage))
							{
								try{
									$sendMail = new Zend_Mail('utf-8');
									foreach ($recipientsTo as $recipientsTo)
									{
										$sendMail->addTo($recipientsTo, $recipientsTo);
									}
									
									foreach ($exRecipientValid as $exrecipientTo)
									{
										$sendMail->addTo($exrecipientTo, $exrecipientTo);
									}
									$sendMail->setFrom($fromEmail, $fromName);
									//
									$sendMail->setSubject($subject)
									->setBodyHtml($setBodyMessage);
								
									$resultMail = $sendMail->send();
									
									$this->view->result = array('success'=> true, 'msg' => 'Email has been sent successfully', 'type' => 'info');
								}
								catch(Zend_Mail_Exception $mailEx)
								{
									$this->view->result = array('success'=> false, 'msg' => 'Unable to send email. Try Again!', 'type' => 'info');									
								}
							}
							else
							{
								$this->view->result = array('success'=> false, 'msg' => 'No Record Exists to send email', 'type' => 'info');								
							}
						}
						else
						{
							$this->view->result = array('success'=> false, 'msg' => 'No Record Exists to send email', 'type' => 'info');							
						}
					}
					else
					{
						$this->view->result = array('success'=> false, 'msg' => 'Invalid Data!', 'type' => 'info');						
					}
			}
		}
		else
		{
			$this->_helper->redirector('index', 'billing', 'default');
		}
	}
	
	

	public function sendEmailReport($billing, $billingDate)
	{
		$messageContent = '';
		if(!empty($billing))
		{	
			$getOrg = $this->_dbBilling->getOrgName($this->_orgCode);
			//$logoPath = $this->_ehrTables->uploadPath.'logos/';
			if(empty($billingDate) && count($billing)>1)
			{
				$messageContent = '';
				if(!empty($getOrg) && count($getOrg)>0)
				{
					$messageContent .=  '<table style="border-bottom:1px solid #ccc;width:100%;"><tr>';
					//if(!empty($getOrg['RegOrg']['Report_LogoPath']))
					//{
					//	if(file_exists($_SERVER['DOCUMENT_ROOT'].'/'.$logoPath.$getOrg['RegOrg']['Report_LogoPath']))
					//	{
					//		$messageContent .=  '<td style="width:140px;"><img style="width:120px;height:50px;" src="'.$_SERVER['HTTP_HOST'].$this->_basePath->baseUrl($logoPath.$getOrg['RegOrg']['Report_LogoPath']).'"></td>';
					//	}
					//}
					$messageContent .=  '<td><div style="font-weight:bold;font-size:25px;">'.$getOrg['RegOrg']['Org_Name'].'</div>
					<div> generated by '.$getOrg['Org_Name'].'</div></td></tr></table>';
				}
				$messageContent .= "<table style='border:1px solid #000;width:100%;border-collapse:collapse;' align='center'>
				<tr>
				<th style='border:1px solid #000;background:#ddd;'>Billing Date</th><th style='border:1px solid #000;background:#ddd;'>No. Of Active Employees</th>
				<th style='border:1px solid #000;background:#ddd;'>No. Of InActive Employees</th><th style='border:1px solid #000;background:#ddd;'>Total Amount for Active Employees</th>
				<th style='border:1px solid #000;background:#ddd;'>Total Amount for Inactive Employees</th><th style='border:1px solid #000;background:#ddd;'>Discount Amount</th>
				<th style='border:1px solid #000;background:#ddd;'>Outstanding Balance</th>
				<th style='border:1px solid #000;background:#ddd;'>Total Amount</th><th style='border:1px solid #000;background:#ddd;'>Status</th>
				</tr>";
				foreach($billing as $orgBilling)
				{
					$messageContent .= "<tr><td style='border:1px solid #000;'>".$orgBilling['Generated_Date']."</td><td style='border:1px solid #000;'>".$orgBilling['No_Active_Employees']."</td>
					<td style='border:1px solid #000;'>".$orgBilling['No_Inactive_Employees']."</td><td style='border:1px solid #000;'>".$orgBilling['Amt_Active_Employees']."</td>
					<td style='border:1px solid #000;'>".$orgBilling['Amt_Inactive_Employees']."</td><td style='border:1px solid #000;'>".$orgBilling['Discount_Amount']."</td>
					<td style='border:1px solid #000;'>".$orgBilling['Outstanding_Balance']."</td>
					<td style='border:1px solid #000;'>".$orgBilling['Total_Amount']."</td><td style='border:1px solid #000;'>".$orgBilling['Status']."</td></tr>";
				}
				$messageContent .= "</table><br/>";
			}
			else
			{
				$messageContent = '';
				if(!empty($getOrg) && count($getOrg)>0)
				{
					$messageContent .=  '<table style="border-bottom:1px solid #ccc;width:100%;"><tr>';
					//if(!empty($getOrg['RegOrg']['Report_LogoPath']))
					//{
					//	if(file_exists($_SERVER['DOCUMENT_ROOT'].'/'.$logoPath.$getOrg['RegOrg']['Report_LogoPath']))
					//	{
					//		$messageContent .=  '<td style="width:140px;"><img style="width:120px;height:50px;" src="'.$_SERVER['HTTP_HOST'].$this->_basePath->baseUrl($logoPath.$getOrg['RegOrg']['Report_LogoPath']).'"></td>';
					//	}
					//}
					$messageContent .=  '<td><div style="font-weight:bold;font-size:25px;">'.$getOrg['RegOrg']['Org_Name'].'</div>
					<div> generated by '.$getOrg['Org_Name'].'</div></td></tr></table>';

				}
				foreach($billing as $orgBilling)
				{
					$messageContent .= "<br/>
					<table style='width:38%;' align='center'>
					<tr><td>Billing Date</td><td>".$orgBilling['Generated_Date']."</td></tr>
					<tr><td>Status</td><td>".$orgBilling['Status']."</td></tr>
					<tr><td>No. Of Active Employees</td><td>".$orgBilling['No_Active_Employees']."</td></tr>
					<tr><td>No. Of InActive Employees</td><td>".$orgBilling['No_Inactive_Employees']."</td></tr></table>

					<table  align='center' style='width:80%;margin-left:40px;border-spacing:10px;border:1px solid #000;border-collapse:collapse;'>
					<tr><th style='border:1px solid #000'>Description</th><th style='border:1px solid #000'>Amount</th></tr>
					<tr>


					<tr><td>Total Amount for Active Employees</td><td style='border-left:1px solid #000;text-align:right;'>".$orgBilling['Amt_Active_Employees']."</td></tr>
					<tr><td>Total Amount for Inactive Employees</td><td style='border-left:1px solid #000;text-align:right;'>".$orgBilling['Amt_Inactive_Employees']."</td></tr>
					<tr><td>Discount Amount</td><td style='border-left:1px solid #000;text-align:right;'>".$orgBilling['Discount_Amount']."</td></tr>";
					if(!empty($orgBilling['Outstanding_Balance'])) {
						$messageContent .= "<tr><td>Outstanding Balance</td><td style='border-left:1px solid #000;text-align:right;'>".$orgBilling['Outstanding_Balance']."</td></tr>";
					}
					$messageContent .= "<tr><td style='border:1px solid #000'>Total Amount</td><td style='border:1px solid #000;text-align:right;'>".$orgBilling['Total_Amount']."</td></tr>
					</table><br/>";
				}
			}
		}
		if(!empty($messageContent))
		{
			$urlString = $_SERVER['HTTP_HOST'].$this->_basePath->baseUrl('default/billing');
			$empName = $this->_dbPersonal->empFirstName(1);
			return $this->_ehrTables->mailLayout($messageContent, $empName['Emp_First_Name'],$urlString);
		}

	}

	public function csvBillingAction()
	{
		$this->_helper->layout()->disableLayout();
		
		if(isset($_SERVER['HTTP_REFERER']) || $_COOKIE['isNativeMobileApp'] == 1)
		{
			if ($this->_billAccess>=1 && !empty($this->_billAccess))
			{
				$billDate = $this->_getParam('bill_date', null);
				$formData = $this->getRequest()->getPost();
				if(isset($formData))
				{
					//$formData = Zend_Json::decode(urldecode($formData['frmData']),true);
					//$discount = $formData[7]['value'];
					//$transMonth = $formData[1]['value'];
					//$activeAmt = $formData[3]['value'];
					//$inactiveAmt = $formData[9]['value'];
					//$totalAmt = $formData[5]['value'];
					//$status = $formData[10]['value'];
					//$transA = $formData[0]['value'];
					//$transB = $formData[2]['value'];
					//$transC = $formData[8]['value'];
					//$transD = $formData[4]['value'];
					//$transE = $formData[6]['value'];
					//$discount = filter_var($discount, FILTER_SANITIZE_STRIPPED);
					//$transMonth = filter_var($transMonth, FILTER_SANITIZE_STRIPPED);
					//$activeAmt = filter_var($activeAmt, FILTER_SANITIZE_STRIPPED);
					//$inactiveAmt = filter_var($inactiveAmt, FILTER_SANITIZE_STRIPPED);
					//$totalAmt = filter_var($totalAmt, FILTER_SANITIZE_STRIPPED);
					//$status = filter_var($status, FILTER_SANITIZE_STRIPPED);
					//$billingReport=$this->_dbBilling->billingReportExport($discount, $transMonth, $activeAmt, $inactiveAmt, $totalAmt, $status,
					//		$transA, $transB, $transC, $transD, $transE, $this->_orgCode, $billDate);
					
					$billingReport=$this->_dbBilling->billingReportExport($formData,$this->_orgCode);

					if(!empty($billingReport) && count($billingReport)>0)
					{
						header('Content-Type: text/csv; charset=utf-8');
						header('Content-Disposition: attachment; filename=BillingReport.csv');
						$output = fopen('php://output', 'w');
						$getOrg = $this->_dbBilling->getOrgName($this->_orgCode);
						if(!empty($getOrg) && count($getOrg)>0)
						{
							fputcsv($output, array($getOrg['RegOrg']['Org_Name']),';');
							fputcsv($output, array(''));
						}
						$logEmpName = $this->_dbPersonal->employeeName($this->_logEmpId);
						if(empty($billDate) && count($billingReport)>1)
						{
							fputcsv($output, array('Generated By - '.$getOrg['Org_Name']),';');
							fputcsv($output, array(''));
							fputcsv($output, array('Billing Date','No. of Active Employees','No. of Inactive Employees', 'Total Amount for Active Employees',
									'Total Amount for Inactive Employees', 'Discount Amount', 'Outstanding Balance', 'Total Amount', 'Status'),';');
					
							foreach($billingReport as $report)
							{
								fputcsv($output, array($report['Generated_Date'],$report['No_Active_Employees'],$report['No_Inactive_Employees'], $report['Amt_Active_Employees'],
										$report['Amt_Inactive_Employees'], $report['Discount_Amount'], $report['Outstanding_Balance'], $report['Total_Amount'], $report['Status']),';');
							}
						}
						else
						{
					
							foreach($billingReport as $report)
							{
								fputcsv($output, array('Generated By - '.$getOrg['Org_Name']),';');
								fputcsv($output, array('Billing Date - '.$report['Generated_Date']),';');
								fputcsv($output, array('Status - '.$report['Status']),';');
								fputcsv($output, array(''));
								fputcsv($output, array('Title', 'Amount'),';');
					
								fputcsv($output, array('No. of Active Employees',$report['No_Active_Employees']),';');
								fputcsv($output, array('No of Inactive Employees',$report['No_Inactive_Employees']),';');
								fputcsv($output, array('Total Amount for Active Employees', $report['Amt_Active_Employees']),';');
								fputcsv($output, array('Total Amount for Inactive Employees', $report['Amt_Inactive_Employees']),';');
								if(!empty($report['Discount_Amount']))
									fputcsv($output, array('Discount Amount', $report['Discount_Amount']),';');
								if(!empty($report['Outstanding_Balance']))
									fputcsv($output, array('Outstanding Balance', $report['Outstanding_Balance']),';');
								fputcsv($output, array('Total Amount',$report['Total_Amount']),';');
					
							}
						}
					}
				}
			}
			else {

				$this->_helper->redirector('index', 'billing', 'default');
			}
		}
		else
		{
			$this->_helper->redirector('index', 'billing', 'default');
		}
	}
	
	public function printBillingAction()
	{
		$this->_helper->layout()->disableLayout();
        if(isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('print-billing', 'json')->initContext();
			$invoiceNo = $this->_getParam('Invoice_No', null);
			$invoiceNo = filter_var($invoiceNo, FILTER_SANITIZE_NUMBER_INT);
			$windowWidth = $this->_getParam('windowWidth', null);
			$windowWidth = filter_var($windowWidth, FILTER_SANITIZE_NUMBER_INT);
			if(!empty($invoiceNo))
			{
				$this->view->transactionInfo = $this->_dbBilling->viewTransactionDetails($invoiceNo, $this->_logEmpId);
				
				$this->view->doimanDetails = $this->_ehrTables->domainDetails();
				
				//$transactionInfo = $this->_dbBilling->viewTransactionDetails($invoiceNo, $this->_logEmpId);
				//$finalpdf = $this->view->transactionInfo =$transactionInfo;			
				//
				//$exportStr = $this->transactionHTML($transactionInfo,'print',$windowWidth);				
				//$this->view->result = $exportStr;				
			}
		}
		else
		{
			$this->_helper->redirector('index', 'billing', 'default');
		}
	}

	public function pdfBillingAction()
	{
		 
		$this->_helper->layout()->disableLayout();
		if(isset($_SERVER['HTTP_REFERER']) || $_COOKIE['isNativeMobileApp'] == 1)
		{
			$exportStr = " ";
			$invoiceNo = $this->_getParam('Invoice_No', null);
			$invoiceNo = filter_var($invoiceNo, FILTER_SANITIZE_NUMBER_INT);
			 
			if(!empty($invoiceNo))
			{
				$transactionpdf = $this->_dbBilling->viewTransactionDetails($invoiceNo, $this->_logEmpId);
				$finalpdf = $this->view->transactionInfo =$transactionpdf;
			}
			$exportStr .= $this->transactionHTML($finalpdf,'pdf');
            
		//	require_once('MPDF/mpdf.php');
		    require_once realpath(APPLICATION_PATH . '/../vendor/mpdf/mpdf/src/Mpdf.php');
			$mpdf=new \Mpdf\Mpdf();
			$mpdf->SetHTMLHeader('');
			$mpdf->writeHTML($exportStr);
			$mpdf->Output('Transaction.pdf', 'D');
		}
		else
		{
			$this->_helper->redirector('index', 'billing', 'default');
		}
	}
	 
	public function transactionHTML($finalpdf,$format,$windowWidth=null)
	{
		// require_once('tcpdf/config/lang/eng.php');
		// require_once('tcpdf/tcpdf.php');

		require_once realpath(APPLICATION_PATH . '/../vendor/tecnickcom/tcpdf/examples/tcpdf_include.php');
		require_once realpath(APPLICATION_PATH . '/../vendor/tecnickcom/tcpdf/examples/lang/eng.php');
		require_once realpath(APPLICATION_PATH . '/../vendor/tecnickcom/tcpdf/tcpdf.php');
		
		$subTotal =$finalpdf['Transaction']['Amt_Active_Employees']+$finalpdf['Transaction']['Amt_Inactive_Employees'];
		$discount =$finalpdf['Transaction']['Discount_Amount'];
		$amount = $subTotal-$discount;
		$taxClassCalculation=($finalpdf['Transaction']['Tax_Class_Percentage']*$amount)/100;
		 
		$netAmount1 =$subTotal+$taxClassCalculation;
		$netAmount =$subTotal+$taxClassCalculation;
		$tscDetails1 =0;
		
		$exportStr = $exportStrPrint = '';
		
		$doimanDetails = $this->_ehrTables->domainDetails();
		
		if($format=='pdf'){
			$exportStr .= '<div style = "width: 98%;margin-left: 10px;page-break-inside: avoid">';		
			$exportStr .= '<div style="border: 1px solid;height:690px;padding: 10px 40px;border-radius: 25px;">';
			
			$exportStr .= '<table style ="font-size:15px;margin-top:30px;text-align:left;width:98%;">';
			$exportStr .= '<tr><td><b>From</b></td><td style="text-align:right;"><b>To</b></td></tr>';
			$exportStr .= '<tr><td style ="font-size:16px;"><b>'.$doimanDetails['Organization_Name'].'</b></td><td style="text-align:right; font-size:16px;"><b>'.$finalpdf['Transaction']['Org_Name'].'</b></td></tr>';
			$exportStr .= '<tr><td>'.$doimanDetails['Street1'].' '.$doimanDetails['Street2'].'</td><td style="text-align:right;"> '.$finalpdf['Address']['Street1'].', '.$finalpdf['Address']['Street2'].'</td></tr>';
			$exportStr .= '<tr><td>'.$doimanDetails['City'].' '.$doimanDetails['Pincode'].'</td><td style="text-align:right;"> '.$finalpdf['Address']['City'].', '.$finalpdf['Address']['Pincode'].'</td></tr>';
			$exportStr .= '<tr><td >Mobile No: '.$doimanDetails['Contact_No'].'</td><td style="text-align:right;">Mobile No: '.$finalpdf['Address']['Phone'].'</td></tr>';
			$exportStr .= '<tr><td >Website: '.$doimanDetails['Talk_Link'].'</td><td style="text-align:right;">Email: '.$finalpdf['Transaction']['Email_Id'].'</td></tr>';
			$exportStr .= '</table>';
			
			$exportStr .= '<table style ="font-size:15px;margin-top:30px;text-align:left;width:25%;">';
			$exportStr .= '<tr><td>Billing Date: </td><td>'.$finalpdf['Transaction']['Billing_Date'].'</td></tr>';
			$exportStr .= '<tr><td>Billing Period: </td><td>'.$finalpdf['Transaction']['Frequency'].'</td></tr>';
			$exportStr .= '<tr><td>Due Date: </td><td>'.$finalpdf['Transaction']['Due_Date'].'</td></tr>';
			$exportStr .= '</table>';
			
			$exportStr .= '<table style="width:98%;" cellpadding="5"><thead>';
			$exportStr .= '<tr bgcolor="#BDBDBD"><th style="border-bottom:1px solid #BDBDBD; font-size:16px; font-weight:bold;">Service Description</th>
					<th style="border-bottom:1px solid #BDBDBD; font-size:16px; font-weight:bold;">Total No Of Employee</th>
					<th style="border-bottom:1px solid #BDBDBD; font-size:16px; font-weight:bold;">Amount Per Employee</th>
					<th style="border-bottom:1px solid #BDBDBD; font-size:16px; font-weight:bold;" align="center">Amount</th></tr></thead>';
			
			$exportStr .= '<tr><td height="40" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">Active</td>
			<td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['No_Active_Employees'].'</td>
			<td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['Rate_Active_Employees'].'</td>
			<td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['Amt_Active_Employees'].'</td>
			</tr>';
			$exportStr .= '<tr><td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">Inactive</td>
			<td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['No_Inactive_Employees'].'</td>
			<td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['Rate_Inactive_Employees'].'</td>
			<td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['Amt_Inactive_Employees'].'</td>
			</tr>';
			
			$exportStr .= '<tr><td colspan="2"></td><td style="text-align:right;"><b>SubTotal:</b></td><td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.number_format($subTotal,2,'.','').'</td></tr>';
			if($discount>0)
				$exportStr .= '<tr><td colspan="2"></td><td style="text-align:right;"><b>Discount:</b></td><td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.number_format($discount,2,'.','').'</td></tr>';
			$exportStr .= '<tr><td colspan="2"></td><td style="text-align:right;"><b>'.$finalpdf['Transaction']['Tax_Class'].'('.$finalpdf['Transaction']['Tax_Class_Percentage'].'%):</b></td><td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.number_format($taxClassCalculation,2,'.','').'</td></tr>';			 
			
			$tscDetails1=0;$tscDetails2=0;$tscDetails3=0;$tscDetails4=0;
			$viewTransaction =$finalpdf['Transaction'];
			if(!empty($viewTransaction['Tax_SubClass1']))
			{
				$tscDetails1 = ($viewTransaction['Tax_SubClass1_Percentage']*$taxClassCalculation)/100;
				$exportStr .= '<tr><td colspan="2"></td><td style="text-align:right"><b>'.$viewTransaction['Tax_SubClass1'].'('.$viewTransaction['Tax_SubClass1_Percentage'].'%):</b></td><td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.number_format($tscDetails1,2,'.','').'</td></tr>';			
			}
			if(!empty($viewTransaction['Tax_SubClass2']))
			{
				$tscDetails2 = ($viewTransaction['Tax_SubClass2_Percentage']*$taxClassCalculation)/100;
				$exportStr .= '<tr><td colspan="2"></td><td style="text-align:right"><b>'.$viewTransaction['Tax_SubClass2'].'('.$viewTransaction['Tax_SubClass2_Percentage'].'%):</b></td><td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.number_format($tscDetails2,2,'.','').'</td></tr>';
			}
			if(!empty($viewTransaction['Tax_SubClass3']))
			{
				$tscDetails1 = ($viewTransaction['Tax_SubClass1_Percentage']*$taxClassCalculation)/100;
				$exportStr .= '<tr><td colspan="2"></td><td style="text-align:right"><b>'.$viewTransaction['Tax_SubClass3'].'('.$viewTransaction['Tax_SubClass3_Percentage'].'%):</b></td><td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.number_format($tscDetails3,2,'.','').'</td></tr>';
			}
			if(!empty($viewTransaction['Tax_SubClass4']))
			{
				$tscDetails1 = ($viewTransaction['Tax_SubClass1_Percentage']*$taxClassCalculation)/100;
				$exportStr .= '<tr><td colspan="2"></td><td style="text-align:right"><b>'.$viewTransaction['Tax_SubClass4'].'('.$viewTransaction['Tax_SubClass4_Percentage'].'%):</b></td><td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.number_format($tscDetails4,2,'.','').'</td></tr>';
			}
			$tscDetails = $tscDetails1+$tscDetails2+$tscDetails3+$tscDetails4;
			$netAmount  = $netAmount1+$tscDetails;
			
			 if($discount>0)
			{
				$netAmount =round($netAmount-$discount).'.00';
			}
			else
			{
				$netAmount =round($netAmount).'.00';
			}
			 
			$exportStr .= '<tr><td colspan="2"></td><td style="text-align:right;"><b>NetAmount:</b></td><td style="text-align:center;">'.$netAmount.'</td></tr>';
			$exportStr .= '</table>';
			
			$exportStr .= '</div>';
			$exportStr .= '<div style=\'page-break-after:always;\'>&nbsp;</div>';
			$exportStr .='<br><br><br>';
		}
		elseif($format=='print'){
			//org start
			//$exportStr .= '<table  style ="font-size:12px;margin-top:20px;margin-left:15px;text-align:left;width:90%;">';//org			
			//$exportStr .= '<tr><td class="fontSize"><b>From</b></td><td class="fontSize" style="text-align:right;"><b style="margin-right:15px;">To</b></td></tr>';
			//$exportStr .= '<tr><td class="fontSize" style ="font-size:14px;"><b>Caprice Technologies Pvt Ltd</b></td><td class="fontSize" style="text-align:right; font-size:14px;"><b style="margin-right:15px;">'.$finalpdf['Transaction']['Org_Name'].'</b></td></tr>';
			//$exportStr .= '<tr><td class="fontSize">1/424, Avinashi Road,Chinniyampalayam</td><td class="fontSize" style="text-align:right;"><span style="margin-right:15px;"> '.$finalpdf['Address']['Street1'].', '.$finalpdf['Address']['Street2'].'</span></td></tr>';
			//$exportStr .= '<tr><td class="fontSize">Coimbatore-641001</td><td class="fontSize" style="text-align:right;"><span style="margin-right:15px;"> '.$finalpdf['Address']['City'].', '.$finalpdf['Address']['Pincode'].'</span></td></tr>';
			//$exportStr .= '<tr><td class="fontSize">Mobile No: 8754355556</td><td class="fontSize" style="text-align:right;">Mobile No: <span style="margin-right:15px;">'.$finalpdf['Address']['Phone'].'</span></td></tr>';
			//$exportStr .= '<tr><td class="fontSize">Website: www.capricetech.com</td><td class="fontSize" style="text-align:right;">Email: <span style="margin-right:15px;">'.$finalpdf['Transaction']['Email_Id'].'</span></td></tr>';
			//$exportStr .= '</table>';
			//
			//$exportStr .= '<table style ="font-size:15px;margin-top:30px;text-align:left;width:25%;">';
			//$exportStr .= '<tr><td class="fontSize">Billing Date: </td><td class="fontSize">'.$finalpdf['Transaction']['Billing_Date'].'</td></tr>';
			//$exportStr .= '<tr><td class="fontSize">Billing Period: </td><td class="fontSize">'.$finalpdf['Transaction']['Frequency'].'</td></tr>';
			//$exportStr .= '<tr><td class="fontSize">Due Date: </td><td class="fontSize">'.$finalpdf['Transaction']['Due_Date'].'</td></tr>';
			//
			//$exportStr .= '</table>';
			//
			//$exportStr .= '<table style="width:98%;margin-top: 1%;" class="billingGrid" border="0">';
			//$exportStr .= '<tr bgcolor="#BDBDBD"><th class="fontSize" height="40" style="border-bottom:1px solid #BDBDBD; font-size:14px; font-weight:bold; text-align:center;">Service Description</th>
			//		<th height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:14px; font-weight:bold; text-align:center;">Total No Of Employee</th>
			//		<th height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:14px; font-weight:bold; text-align:center;">Amount Per Employee</th>
			//		<th height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:14px; font-weight:bold; text-align:center;">Amount</th></tr>';					
			//
			//$exportStr .= '<tr><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">Active</td>
			//<td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['No_Active_Employees'].'</td>
			//<td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['Rate_Active_Employees'].'</td>
			//<td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['Amt_Active_Employees'].'</td>
			//</tr>';
			//$exportStr .= '<tr><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">Inactive</td>
			//<td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['No_Inactive_Employees'].'</td>
			//<td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['Rate_Inactive_Employees'].'</td>
			//<td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['Amt_Inactive_Employees'].'</td>
			//</tr>';
			//
			//$exportStr .= '<tr><td colspan="2"></td><td height="40" class="fontSize" style="text-align:right;"><b>SubTotal:</b></td><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$subTotal.'</td></tr>';
			//if($discount>0)
			//	$exportStr .= '<tr><td colspan="2"></td><td height="40" class="fontSize" style="text-align:right;"><b>Discount:</b></td><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$discount.'</td></tr>';
			//$exportStr .= '<tr><td colspan="2"></td><td height="40" class="fontSize" style="text-align:right;"><b>'.$finalpdf['Transaction']['Tax_Class'].'('.$finalpdf['Transaction']['Tax_Class_Percentage'].'%):</b></td><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$taxClassCalculation.'</td></tr>';			 
			// 
			//$tscDetails1=0;$tscDetails2=0;$tscDetails3=0;$tscDetails4=0;
			//$viewTransaction =$finalpdf['Transaction'];
			//if(!empty($viewTransaction['Tax_SubClass1']))
			//{
			//	$tscDetails1 = ($viewTransaction['Tax_SubClass1_Percentage']*$taxClassCalculation)/100;
			//	$exportStr .= '<tr><td colspan="2"></td><td height="40" class="fontSize" style="text-align:right"><b>'.$viewTransaction['Tax_SubClass1'].'('.$viewTransaction['Tax_SubClass1_Percentage'].'%):</b></td><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$tscDetails1.'</td></tr>';			
			//}
			//if(!empty($viewTransaction['Tax_SubClass2']))
			//{
			//	$tscDetails2 = ($viewTransaction['Tax_SubClass2_Percentage']*$taxClassCalculation)/100;
			//	$exportStr .= '<tr><td colspan="2"></td><td height="40" class="fontSize" style="text-align:right"><b>'.$viewTransaction['Tax_SubClass2'].'('.$viewTransaction['Tax_SubClass2_Percentage'].'%):</b></td><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$tscDetails2.'</td></tr>';
			//}
			//if(!empty($viewTransaction['Tax_SubClass3']))
			//{
			//	$tscDetails1 = ($viewTransaction['Tax_SubClass1_Percentage']*$taxClassCalculation)/100;
			//	$exportStr .= '<tr><td colspan="2"></td><td height="40" class="fontSize" style="text-align:right"><b>'.$viewTransaction['Tax_SubClass3'].'('.$viewTransaction['Tax_SubClass3_Percentage'].'%):</b></td><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$tscDetails3.'</td></tr>';
			//}
			//if(!empty($viewTransaction['Tax_SubClass4']))
			//{
			//	$tscDetails1 = ($viewTransaction['Tax_SubClass1_Percentage']*$taxClassCalculation)/100;
			//	$exportStr .= '<tr><td colspan="2"></td><td height="40" class="fontSize" style="text-align:right"><b>'.$viewTransaction['Tax_SubClass4'].'('.$viewTransaction['Tax_SubClass4_Percentage'].'%):</b></td><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$tscDetails4.'</td></tr>';
			//}
			//$tscDetails = $tscDetails1+$tscDetails2+$tscDetails3+$tscDetails4;
			//$netAmount  = $netAmount1+$tscDetails;
			//
			// if($discount>0)
			//{
			//	$netAmount =round($netAmount-$discount).'.00';
			//}
			//else
			//{
			//	$netAmount =round($netAmount).'.00';
			//}
			// 
			//$exportStr .= '<tr><td colspan="2"></td><td height="40" class="fontSize" style="text-align:right;"><b>NetAmount:</b></td><td height="40" class="fontSize" style="text-align:center;">'.$netAmount.'</td></tr>';
			//$exportStr .= '</table>';
			//
			//$exportStr .= '</div>';
			//$exportStr .= '<div style=\'page-break-after:always;\'>&nbsp;</div>';
			//$exportStr .='<br><br><br>';
			//org end
			
			//$exportStr .= '<div style = "width: 98%;margin-left: 10px;page-break-inside: avoid">';		
			//$exportStr .= '<div style="border: 1px solid;height:690px;padding: 10px 40px;border-radius: 25px;">';
			
			//$exportStr .= '<table class="billMargin" style ="font-size:12px;margin-top:20px;margin-left:15px;text-align:left;width:97%; !important">';//org			
			//$exportStr .= '<tr><td class="fontSize"><b>From</b></td><td class="fontSize" style="text-align:right;"><b style="margin-right:15px;">To</b></td></tr>';
			//$exportStr .= '<tr><td class="fontSize" style ="font-size:14px;"><b>Caprice Technologies Pvt Ltd</b></td><td class="fontSize" style="text-align:right; font-size:14px;"><b style="margin-right:15px;">'.$finalpdf['Transaction']['Org_Name'].'</b></td></tr>';
			//$exportStr .= '<tr><td class="fontSize">1/424, Avinashi Road,Chinniyampalayam</td><td class="fontSize" style="text-align:right;"><span style="margin-right:15px;"> '.$finalpdf['Address']['Street1'].', '.$finalpdf['Address']['Street2'].'</span></td></tr>';
			//$exportStr .= '<tr><td class="fontSize">Coimbatore-641001</td><td class="fontSize" style="text-align:right;"><span style="margin-right:15px;"> '.$finalpdf['Address']['City'].', '.$finalpdf['Address']['Pincode'].'</span></td></tr>';
			//$exportStr .= '<tr><td class="fontSize">Mobile No: 8754355556</td><td class="fontSize" style="text-align:right;">Mobile No: <span style="margin-right:15px;">'.$finalpdf['Address']['Phone'].'</span></td></tr>';
			//$exportStr .= '<tr><td class="fontSize">Website: www.capricetech.com</td><td class="fontSize" style="text-align:right;">Email: <span style="margin-right:15px;">'.$finalpdf['Transaction']['Email_Id'].'</span></td></tr>';
			//$exportStr .= '</table>';
			
			if($windowWidth >= 768){
			
			//$exportStr .= '<div class="form-group">';//style="font-size:15px;margin-top:20px;margin-left:15px;text-align:left;"
			//$exportStr .= '<div class="col-md-12 addrpanel" style="font-size:15px;margin-top:20px;text-align:left;">';
			//$exportStr .= '<div class="col-md-6"><b>From </b></br>';
			//$exportStr .= '<b>Caprice Technologies Pvt Ltd</b></br>';
			//$exportStr .= '<span>1/424, Avinashi Road,Chinniyampalayam</span></br>';
			//$exportStr .= '<span>Coimbatore-641001</span></br>';
			//$exportStr .= '<span>Mobile No: 8754355556</span></br>';
			//$exportStr .= '<span>Website: www.capricetech.com</span></br></br></div>';
			//
			//$exportStr .= '<div class="col-md-6"><b>To </b></br>';
			//$exportStr .= '<b>'.$finalpdf['Transaction']['Org_Name'].'</b></br>';
			//$exportStr .= '<span>'.$finalpdf['Address']['Street1'].', '.$finalpdf['Address']['Street2'].'</span></br>';
			//$exportStr .= '<span>'.$finalpdf['Address']['City'].', '.$finalpdf['Address']['Pincode'].'</span></br>';
			//$exportStr .= '<span>Mobile No: '.$finalpdf['Address']['Phone'].'</span></br>';
			//$exportStr .= '<span>Email: '.$finalpdf['Transaction']['Email_Id'].'</span></br></div>';
			//$exportStr .= '</div>';			
			
			$exportStr .= '<table class="billingPrintMargin" style ="font-size:15px;margin-top:20px;margin-left:15px;text-align:left;width:97%;">';//org			
			$exportStr .= '<tr><td class="fontSize"><b>From</b></td><td class="fontSize" style="text-align:right;"><b>To</b></td><td colspan=2>too</td></tr>';
			$exportStr .= '<tr><td class="fontSize" style ="font-size:14px;"><b>Caprice Technologies Pvt Ltd</b></td><td class="fontSize" style="text-align:right; font-size:14px;"><b>'.$finalpdf['Transaction']['Org_Name'].'</b></td></tr>';
			$exportStr .= '<tr><td class="fontSize">1/424, Avinashi Road,Chinniyampalayam</td><td class="fontSize" style="text-align:right;"> '.$finalpdf['Address']['Street1'].', '.$finalpdf['Address']['Street2'].'</td></tr>';
			$exportStr .= '<tr><td class="fontSize">Coimbatore-641001</td><td class="fontSize" style="text-align:right;"> '.$finalpdf['Address']['City'].', '.$finalpdf['Address']['Pincode'].'</td></tr>';
			$exportStr .= '<tr><td class="fontSize">Mobile No: 8754355556</td><td class="fontSize" style="text-align:right;">Mobile No: '.$finalpdf['Address']['Phone'].'</td></tr>';
			$exportStr .= '<tr><td class="fontSize">Website: www.capricetech.com</td><td class="fontSize" style="text-align:right;">Email: '.$finalpdf['Transaction']['Email_Id'].'</td></tr>';
			$exportStr .= '</table>';
			}
			else{
				$exportStr .= '<table style ="font-size:15px;margin-top:20px;margin-left:15px;text-align:left;width:97%;">';
				$exportStr .= '<tr><td class="fontSize">From </td></tr>';
				$exportStr .= '<tr><td class="fontSize"><b>Caprice Technologies Pvt Ltd</b> </td></tr>';
				$exportStr .= '<tr><td class="fontSize">1/424, Avinashi Road,Chinniyampalayam </td></tr>';
				$exportStr .= '<tr><td class="fontSize">Coimbatore-641001 </td></tr>';
				$exportStr .= '<tr><td class="fontSize">Mobile No: 8754355556 </td></tr>';
				$exportStr .= '<tr><td class="fontSize">Website: www.capricetech.com </td></tr></table>';
				
				$exportStr .= '<table style ="font-size:15px;margin-top:20px;margin-left:15px;text-align:left;width:97%;">';
				$exportStr .= '<tr><td class="fontSize">To </td></tr>';
				$exportStr .= '<tr><td class="fontSize"><b>'.$finalpdf['Transaction']['Org_Name'].'</b> </td></tr>';
				$exportStr .= '<tr><td class="fontSize">'.$finalpdf['Address']['Street1'].', '.$finalpdf['Address']['Street2'].' </td></tr>';
				$exportStr .= '<tr><td class="fontSize">'.$finalpdf['Address']['City'].', '.$finalpdf['Address']['Pincode'].' </td></tr>';
				$exportStr .= '<tr><td class="fontSize">Mobile No: '.$finalpdf['Address']['Phone'].' </td></tr>';
				$exportStr .= '<tr><td class="fontSize">Email: '.$finalpdf['Transaction']['Email_Id'].' </td></tr>';				
				$exportStr .= '</table>';
			}
			
			$exportStr .= '<table style ="font-size:15px;margin-top:20px;margin-left:15px;text-align:left;width:25%;">';
			$exportStr .= '<tr><td class="fontSize">Billing Date: </td><td class="fontSize">'.$finalpdf['Transaction']['Billing_Date'].'</td></tr>';
			$exportStr .= '<tr><td class="fontSize">Billing Period: </td><td class="fontSize">'.$finalpdf['Transaction']['Frequency'].'</td></tr>';
			$exportStr .= '<tr><td class="fontSize">Due Date: </td><td class="fontSize">'.$finalpdf['Transaction']['Due_Date'].'</td></tr>';
			
			$exportStr .= '</table>';
			
			$exportStr .= '<table class="billingPrintMargin" style="width:97%;margin-left:15px;margin-top: 1%;" class="billingGrid" border="0">';
			$exportStr .= '<tr bgcolor="#BDBDBD"><th class="fontSize" height="40" style="border-bottom:1px solid #BDBDBD; font-size:14px; font-weight:bold; text-align:center;">Service Description</th>
					<th height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:14px; font-weight:bold; text-align:center;">Total No Of Employee</th>
					<th height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:14px; font-weight:bold; text-align:center;">Amount Per Employee</th>
					<th height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:14px; font-weight:bold; text-align:center;">Amount</th></tr>';					
			
			$exportStr .= '<tr><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">Active</td>
			<td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['No_Active_Employees'].'</td>
			<td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['Rate_Active_Employees'].'</td>
			<td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['Amt_Active_Employees'].'</td>
			</tr>';
			$exportStr .= '<tr><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">Inactive</td>
			<td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['No_Inactive_Employees'].'</td>
			<td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['Rate_Inactive_Employees'].'</td>
			<td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['Amt_Inactive_Employees'].'</td>
			</tr>';
			
			$exportStr .= '<tr><td colspan="2"></td><td height="40" class="fontSize" style="text-align:right;"><b>SubTotal:</b></td><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$subTotal.'</td></tr>';
			if($discount>0)
				$exportStr .= '<tr><td colspan="2"></td><td height="40" class="fontSize" style="text-align:right;"><b>Discount:</b></td><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$discount.'</td></tr>';
			$exportStr .= '<tr><td colspan="2"></td><td height="40" class="fontSize" style="text-align:right;"><b>'.$finalpdf['Transaction']['Tax_Class'].'('.$finalpdf['Transaction']['Tax_Class_Percentage'].'%):</b></td><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$taxClassCalculation.'</td></tr>';			 
			 
			$tscDetails1=0;$tscDetails2=0;$tscDetails3=0;$tscDetails4=0;
			$viewTransaction =$finalpdf['Transaction'];
			if(!empty($viewTransaction['Tax_SubClass1']))
			{
				$tscDetails1 = ($viewTransaction['Tax_SubClass1_Percentage']*$taxClassCalculation)/100;
				$exportStr .= '<tr><td colspan="2"></td><td height="40" class="fontSize" style="text-align:right"><b>'.$viewTransaction['Tax_SubClass1'].'('.$viewTransaction['Tax_SubClass1_Percentage'].'%):</b></td><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$tscDetails1.'</td></tr>';			
			}
			if(!empty($viewTransaction['Tax_SubClass2']))
			{
				$tscDetails2 = ($viewTransaction['Tax_SubClass2_Percentage']*$taxClassCalculation)/100;
				$exportStr .= '<tr><td colspan="2"></td><td height="40" class="fontSize" style="text-align:right"><b>'.$viewTransaction['Tax_SubClass2'].'('.$viewTransaction['Tax_SubClass2_Percentage'].'%):</b></td><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$tscDetails2.'</td></tr>';
			}
			if(!empty($viewTransaction['Tax_SubClass3']))
			{
				$tscDetails1 = ($viewTransaction['Tax_SubClass1_Percentage']*$taxClassCalculation)/100;
				$exportStr .= '<tr><td colspan="2"></td><td height="40" class="fontSize" style="text-align:right"><b>'.$viewTransaction['Tax_SubClass3'].'('.$viewTransaction['Tax_SubClass3_Percentage'].'%):</b></td><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$tscDetails3.'</td></tr>';
			}
			if(!empty($viewTransaction['Tax_SubClass4']))
			{
				$tscDetails1 = ($viewTransaction['Tax_SubClass1_Percentage']*$taxClassCalculation)/100;
				$exportStr .= '<tr><td colspan="2"></td><td height="40" class="fontSize" style="text-align:right"><b>'.$viewTransaction['Tax_SubClass4'].'('.$viewTransaction['Tax_SubClass4_Percentage'].'%):</b></td><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$tscDetails4.'</td></tr>';
			}
			$tscDetails = $tscDetails1+$tscDetails2+$tscDetails3+$tscDetails4;
			$netAmount  = $netAmount1+$tscDetails;
			
			 if($discount>0)
			{
				$netAmount =round($netAmount-$discount).'.00';
			}
			else
			{
				$netAmount =round($netAmount).'.00';
			}
			 
			$exportStr .= '<tr><td colspan="2"></td><td height="40" class="fontSize" style="text-align:right;"><b>NetAmount:</b></td><td height="40" class="fontSize" style="text-align:center;">'.$netAmount.'</td></tr>';
			$exportStr .= '</table>';
			
			$exportStr .= '</div>';
			$exportStr .= '<div style=\'page-break-after:always;\'>&nbsp;</div>';
			$exportStr .='<br><br><br>';
			
			$exportStrPrint .= '<table class="billingPrintMargin" style ="font-size:15px;margin-top:20px;margin-left:15px;text-align:left;width:97%;">';//org			
			$exportStrPrint .= '<tr><td class="fontSize"><b>From</b></td><td class="fontSize" style="text-align:right;"><b>To</b></td><td colspan=2>too</td></tr>';
			$exportStrPrint .= '<tr><td class="fontSize" style ="font-size:14px;"><b>Caprice Technologies Pvt Ltd</b></td><td class="fontSize" style="text-align:right; font-size:14px;"><b>'.$finalpdf['Transaction']['Org_Name'].'</b></td></tr>';
			$exportStrPrint .= '<tr><td class="fontSize">1/424, Avinashi Road,Chinniyampalayam</td><td class="fontSize" style="text-align:right;"> '.$finalpdf['Address']['Street1'].', '.$finalpdf['Address']['Street2'].'</td></tr>';
			$exportStrPrint .= '<tr><td class="fontSize">Coimbatore-641001</td><td class="fontSize" style="text-align:right;"> '.$finalpdf['Address']['City'].', '.$finalpdf['Address']['Pincode'].'</td></tr>';
			$exportStrPrint .= '<tr><td class="fontSize">Mobile No: 8754355556</td><td class="fontSize" style="text-align:right;">Mobile No: '.$finalpdf['Address']['Phone'].'</td></tr>';
			$exportStrPrint .= '<tr><td class="fontSize">Website: www.capricetech.com</td><td class="fontSize" style="text-align:right;">Email: '.$finalpdf['Transaction']['Email_Id'].'</td></tr>';
			$exportStrPrint .= '</table>';
			$exportStrPrint .= '<table style ="font-size:15px;margin-top:20px;margin-left:15px;text-align:left;width:25%;">';
			$exportStrPrint .= '<tr><td class="fontSize">Billing Date: </td><td class="fontSize">'.$finalpdf['Transaction']['Billing_Date'].'</td></tr>';
			$exportStrPrint .= '<tr><td class="fontSize">Billing Period: </td><td class="fontSize">'.$finalpdf['Transaction']['Frequency'].'</td></tr>';
			$exportStrPrint .= '<tr><td class="fontSize">Due Date: </td><td class="fontSize">'.$finalpdf['Transaction']['Due_Date'].'</td></tr>';
			
			$exportStrPrint .= '</table>';
			
			$exportStrPrint .= '<table class="billingPrintMargin" style="width:97%;margin-left:15px;margin-top: 1%;" class="billingGrid" border="0">';
			$exportStrPrint .= '<tr bgcolor="#BDBDBD"><th class="fontSize" height="40" style="border-bottom:1px solid #BDBDBD; font-size:14px; font-weight:bold; text-align:center;">Service Description</th>
					<th height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:14px; font-weight:bold; text-align:center;">Total No Of Employee</th>
					<th height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:14px; font-weight:bold; text-align:center;">Amount Per Employee</th>
					<th height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:14px; font-weight:bold; text-align:center;">Amount</th></tr>';					
			
			$exportStrPrint .= '<tr><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">Active</td>
			<td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['No_Active_Employees'].'</td>
			<td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['Rate_Active_Employees'].'</td>
			<td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['Amt_Active_Employees'].'</td>
			</tr>';
			$exportStrPrint .= '<tr><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">Inactive</td>
			<td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['No_Inactive_Employees'].'</td>
			<td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['Rate_Inactive_Employees'].'</td>
			<td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['Amt_Inactive_Employees'].'</td>
			</tr>';
			
			$exportStrPrint .= '<tr><td colspan="2"></td><td height="40" class="fontSize" style="text-align:right;"><b>SubTotal:</b></td><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$subTotal.'</td></tr>';
			if($discount>0)
				$exportStrPrint .= '<tr><td colspan="2"></td><td height="40" class="fontSize" style="text-align:right;"><b>Discount:</b></td><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$discount.'</td></tr>';
			$exportStrPrint .= '<tr><td colspan="2"></td><td height="40" class="fontSize" style="text-align:right;"><b>'.$finalpdf['Transaction']['Tax_Class'].'('.$finalpdf['Transaction']['Tax_Class_Percentage'].'%):</b></td><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$taxClassCalculation.'</td></tr>';			 
			 
			$tscDetails1=0;$tscDetails2=0;$tscDetails3=0;$tscDetails4=0;
			$viewTransaction =$finalpdf['Transaction'];
			if(!empty($viewTransaction['Tax_SubClass1']))
			{
				$tscDetails1 = ($viewTransaction['Tax_SubClass1_Percentage']*$taxClassCalculation)/100;
				$exportStrPrint .= '<tr><td colspan="2"></td><td height="40" class="fontSize" style="text-align:right"><b>'.$viewTransaction['Tax_SubClass1'].'('.$viewTransaction['Tax_SubClass1_Percentage'].'%):</b></td><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$tscDetails1.'</td></tr>';			
			}
			if(!empty($viewTransaction['Tax_SubClass2']))
			{
				$tscDetails2 = ($viewTransaction['Tax_SubClass2_Percentage']*$taxClassCalculation)/100;
				$exportStrPrint .= '<tr><td colspan="2"></td><td height="40" class="fontSize" style="text-align:right"><b>'.$viewTransaction['Tax_SubClass2'].'('.$viewTransaction['Tax_SubClass2_Percentage'].'%):</b></td><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$tscDetails2.'</td></tr>';
			}
			if(!empty($viewTransaction['Tax_SubClass3']))
			{
				$tscDetails1 = ($viewTransaction['Tax_SubClass1_Percentage']*$taxClassCalculation)/100;
				$exportStrPrint .= '<tr><td colspan="2"></td><td height="40" class="fontSize" style="text-align:right"><b>'.$viewTransaction['Tax_SubClass3'].'('.$viewTransaction['Tax_SubClass3_Percentage'].'%):</b></td><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$tscDetails3.'</td></tr>';
			}
			if(!empty($viewTransaction['Tax_SubClass4']))
			{
				$tscDetails1 = ($viewTransaction['Tax_SubClass1_Percentage']*$taxClassCalculation)/100;
				$exportStrPrint .= '<tr><td colspan="2"></td><td height="40" class="fontSize" style="text-align:right"><b>'.$viewTransaction['Tax_SubClass4'].'('.$viewTransaction['Tax_SubClass4_Percentage'].'%):</b></td><td height="40" class="fontSize" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$tscDetails4.'</td></tr>';
			}
			$tscDetails = $tscDetails1+$tscDetails2+$tscDetails3+$tscDetails4;
			$netAmount  = $netAmount1+$tscDetails;
			
			 if($discount>0)
			{
				$netAmount =round($netAmount-$discount).'.00';
			}
			else
			{
				$netAmount =round($netAmount).'.00';
			}
			 
			$exportStrPrint .= '<tr><td colspan="2"></td><td height="40" class="fontSize" style="text-align:right;"><b>NetAmount:</b></td><td height="40" class="fontSize" style="text-align:center;">'.$netAmount.'</td></tr>';
			$exportStrPrint .= '</table>';
			
			$exportStrPrint .= '</div>';
			$exportStrPrint .= '<div style=\'page-break-after:always;\'>&nbsp;</div>';
			$exportStrPrint .='<br><br><br>';
		}
		elseif($format=='mail'){			
			$exportStr .= '<div style = "width: 98%;margin-left: 10px;page-break-inside: avoid">';		
			$exportStr .= '<div style="border: 1px solid;height:690px;padding: 10px 40px;border-radius: 25px;">';
			
			$exportStr .= '<table style ="font-size:15px;margin-top:30px;text-align:left;width:98%;">';
			$exportStr .= '<tr><td><b>From</b></td><td style="text-align:right;"><b>To</b></td></tr>';
			$exportStr .= '<tr><td style ="font-size:16px;"><b>Caprice Technologies Pvt Ltd</b></td><td style="text-align:right; font-size:16px;"><b>'.$finalpdf['Transaction']['Org_Name'].'</b></td></tr>';
			$exportStr .= '<tr><td>1/424, Avinashi Road,Chinniyampalayam</td><td style="text-align:right;"> '.$finalpdf['Address']['Street1'].', '.$finalpdf['Address']['Street2'].'</td></tr>';
			$exportStr .= '<tr><td>Coimbatore-641001</td><td style="text-align:right;"> '.$finalpdf['Address']['City'].', '.$finalpdf['Address']['Pincode'].'</td></tr>';
			$exportStr .= '<tr><td >Mobile No: 8754355556</td><td style="text-align:right;">Mobile No: '.$finalpdf['Address']['Phone'].'</td></tr>';
			$exportStr .= '<tr><td >Website: www.capricetech.com</td><td style="text-align:right;">Email: '.$finalpdf['Transaction']['Email_Id'].'</td></tr>';
			$exportStr .= '</table>';
			
			$exportStr .= '<table style ="font-size:15px;margin-top:30px;text-align:left;width:25%;">';
			$exportStr .= '<tr><td>Billing Date: </td><td>'.$finalpdf['Transaction']['Billing_Date'].'</td></tr>';
			$exportStr .= '<tr><td>Billing Period: </td><td>'.$finalpdf['Transaction']['Frequency'].'</td></tr>';
			$exportStr .= '<tr><td>Due Date: </td><td>'.$finalpdf['Transaction']['Due_Date'].'</td></tr>';
			$exportStr .= '</table>';
			
			$exportStr .= '<table style="width:98%;" cellpadding="5"><thead>';
			$exportStr .= '<tr bgcolor="#BDBDBD"><th style="border-bottom:1px solid #BDBDBD; font-size:16px; font-weight:bold;">Service Description</th>
					<th style="border-bottom:1px solid #BDBDBD; font-size:16px; font-weight:bold;">Total No Of Employee</th>
					<th style="border-bottom:1px solid #BDBDBD; font-size:16px; font-weight:bold;">Amount Per Employee</th>
					<th style="border-bottom:1px solid #BDBDBD; font-size:16px; font-weight:bold;" align="center">Amount</th></tr></thead>';
			
			$exportStr .= '<tr><td height="40" style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">Active</td>
			<td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['No_Active_Employees'].'</td>
			<td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['Rate_Active_Employees'].'</td>
			<td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['Amt_Active_Employees'].'</td>
			</tr>';
			$exportStr .= '<tr><td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">Inactive</td>
			<td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['No_Inactive_Employees'].'</td>
			<td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['Rate_Inactive_Employees'].'</td>
			<td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$finalpdf['Transaction']['Amt_Inactive_Employees'].'</td>
			</tr>';
			
			$exportStr .= '<tr><td colspan="2"></td><td style="text-align:right;"><b>SubTotal:</b></td><td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$subTotal.'</td></tr>';
			if($discount>0)
				$exportStr .= '<tr><td colspan="2"></td><td style="text-align:right;"><b>Discount:</b></td><td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$discount.'</td></tr>';
			$exportStr .= '<tr><td colspan="2"></td><td style="text-align:right;"><b>'.$finalpdf['Transaction']['Tax_Class'].'('.$finalpdf['Transaction']['Tax_Class_Percentage'].'%):</b></td><td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$taxClassCalculation.'</td></tr>';			 
			 
			$tscDetails1=0;$tscDetails2=0;$tscDetails3=0;$tscDetails4=0;
			$viewTransaction =$finalpdf['Transaction'];
			if(!empty($viewTransaction['Tax_SubClass1']))
			{
				$tscDetails1 = ($viewTransaction['Tax_SubClass1_Percentage']*$taxClassCalculation)/100;
				$exportStr .= '<tr><td colspan="2"></td><td style="text-align:right"><b>'.$viewTransaction['Tax_SubClass1'].'('.$viewTransaction['Tax_SubClass1_Percentage'].'%):</b></td><td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$tscDetails1.'</td></tr>';			
			}
			if(!empty($viewTransaction['Tax_SubClass2']))
			{
				$tscDetails2 = ($viewTransaction['Tax_SubClass2_Percentage']*$taxClassCalculation)/100;
				$exportStr .= '<tr><td colspan="2"></td><td style="text-align:right"><b>'.$viewTransaction['Tax_SubClass2'].'('.$viewTransaction['Tax_SubClass2_Percentage'].'%):</b></td><td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$tscDetails2.'</td></tr>';
			}
			if(!empty($viewTransaction['Tax_SubClass3']))
			{
				$tscDetails1 = ($viewTransaction['Tax_SubClass1_Percentage']*$taxClassCalculation)/100;
				$exportStr .= '<tr><td colspan="2"></td><td style="text-align:right"><b>'.$viewTransaction['Tax_SubClass3'].'('.$viewTransaction['Tax_SubClass3_Percentage'].'%):</b></td><td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$tscDetails3.'</td></tr>';
			}
			if(!empty($viewTransaction['Tax_SubClass4']))
			{
				$tscDetails1 = ($viewTransaction['Tax_SubClass1_Percentage']*$taxClassCalculation)/100;
				$exportStr .= '<tr><td colspan="2"></td><td style="text-align:right"><b>'.$viewTransaction['Tax_SubClass4'].'('.$viewTransaction['Tax_SubClass4_Percentage'].'%):</b></td><td style="border-bottom:1px solid #BDBDBD; font-size:15px; text-align:center;">'.$tscDetails4.'</td></tr>';
			}
			$tscDetails = $tscDetails1+$tscDetails2+$tscDetails3+$tscDetails4;
			$netAmount  = $netAmount1+$tscDetails;
			
			 if($discount>0)
			{
				$netAmount =round($netAmount-$discount).'.00';
			}
			else
			{
				$netAmount =round($netAmount).'.00';
			}
			 
			$exportStr .= '<tr><td colspan="2"></td><td style="text-align:right;"><b>NetAmount:</b></td><td style="text-align:center;">'.$netAmount.'</td></tr>';
			$exportStr .= '</table>';
			
			$exportStr .= '</div>';
			$exportStr .= '<div style=\'page-break-after:always;\'>&nbsp;</div>';
			$exportStr .='<br><br><br>';
		}
		
		//$exportStr .= '<tr><td>Billing Date: '.$finalpdf['Transaction']['Billing_Date'].'</td></tr>';
		//$exportStr .= '<tr><td>Billing Period: '.$finalpdf['Transaction']['Frequency'].'</td></tr>';
		//$exportStr .= '<tr><td>Due Date: '.$finalpdf['Transaction']['Due_Date'].'</td></tr>';
		//$exportStr .= '</table>';
		//
		//if($format=='pdf'){
		//	$exportStr .= '<table style="width:98%;" cellpadding="5"><thead>';
		//	$exportStr .= '<tr bgcolor="#BDBDBD"><th style="border-bottom:1px solid #BDBDBD; font-size:16px; font-weight:bold;">Service Description</th>
		//			<th style="border-bottom:1px solid #BDBDBD; font-size:16px; font-weight:bold;">Total No Of Employee</th>
		//			<th style="border-bottom:1px solid #BDBDBD; font-size:16px; font-weight:bold;">Amount Per Employee</th>
		//			<th style="border-bottom:1px solid #BDBDBD; font-size:16px; font-weight:bold;" align="center">Amount</th></tr></thead>';
		//}
		//elseif($format=='print'){
		//	$exportStr .= '<table style="width:98%;" class="billingGrid" border="0">';
		//	$exportStr .= '<tr bgcolor="#BDBDBD"><th height="40" style="border-bottom:1px solid #BDBDBD; font-size:14px; font-weight:bold;">Service Description</th>
		//			<th height="40" style="border-bottom:1px solid #BDBDBD; font-size:14px; font-weight:bold;">Total No Of Employee</th>
		//			<th height="40" style="border-bottom:1px solid #BDBDBD; font-size:14px; font-weight:bold;">Amount Per Employee</th>
		//			<th height="40" style="border-bottom:1px solid #BDBDBD; font-size:14px; font-weight:bold;" align="center">Amount</th></tr>';
		//}
		//else{
		//	$exportStr .= '<table style="width:98%;" cellpadding="5" border="0"><thead>';
		//	$exportStr .= '<tr bgcolor="#BDBDBD"><th style="border-bottom:1px solid #BDBDBD; font-size:16px; font-weight:bold;">Service Description</th>
		//			<th style="border-bottom:1px solid #BDBDBD; font-size:16px; font-weight:bold;">Total No Of Employee</th>
		//			<th style="border-bottom:1px solid #BDBDBD; font-size:16px; font-weight:bold;">Amount Per Employee</th>
		//			<th style="border-bottom:1px solid #BDBDBD; font-size:16px; font-weight:bold;" align="center">Amount</th></tr></thead>';
		//			
		//}
		
		//$exportStr .= '<tr bgcolor="#BDBDBD"><th style="border-bottom:1px solid #BDBDBD; font-size:16px; font-weight:bold;">Service Description</th>
		//			<th style="border-bottom:1px solid #BDBDBD; font-size:16px; font-weight:bold;">Total No Of Employee</th>
		//			<th style="border-bottom:1px solid #BDBDBD; font-size:16px; font-weight:bold;">Amount Per Employee</th>
		//			<th style="border-bottom:1px solid #BDBDBD; font-size:16px; font-weight:bold;" align="center">Amount</th></tr></thead>';
		
		
		
		//end
		 
		//$exportStr = '';//<table style = "page-break-inside: avoid;width:100%"><tr><td style = "border:1px solid;">
		//$exportStr .= '<div style = "width: 96%;page-break-inside: avoid">';
		////$exportStr .= '<div style="border: 1px solid blue;height:690px;padding: 10px 40px;border-radius: 25px;background-image:url("/images/noise4.png");">';
		//$exportStr .= '<div style="border: 1px solid blue;height:690px;padding: 10px 40px;border-radius: 25px;">';
		// 
		//$exportStr .= '<table style ="font-size:15px;text-align:center;color:rgb(9, 9, 159);width:100%;">';
		//$exportStr .= '<tr><td ><b>Caprice Technologies Pvt Ltd</b></td></tr>';
		//$exportStr .= '<tr><td >1/424, Avinashi Road,Chinniyampalayam</td></tr>';
		//$exportStr .= '<tr><td >Coimbatore-641001</td></tr>';
		//$exportStr .= '<tr><td >Mobile No:8754355556,Website:www.capricetech.com</td></tr>';
		//$exportStr .= '</table>';
		// 
		//$exportStr .= '<table style ="font-size:15px;margin-top:30px;text-align:left;color:rgb(9, 9, 159);width:100%;">';
		//$exportStr .= '<tr><td><b>'.$finalpdf['Transaction']['Org_Name'].'</b></td><td>Billing Date:</td><td> '.$finalpdf['Transaction']['Billing_Date'].'</td></tr>';
		//$exportStr .= '<tr><td>'.$finalpdf['Address']['Street1'].', '.$finalpdf['Address']['Street2'].'</td><td>Billing Period:</td><td> '.$finalpdf['Transaction']['Frequency'].'</td></tr>';
		//$exportStr .= '<tr><td>'.$finalpdf['Address']['City'].', '.$finalpdf['Address']['Pincode'].'</td><td>Due Date:</td><td> '.$finalpdf['Transaction']['Due_Date'].'</td></tr>';
		//$exportStr .= '<tr><td>Mobile No: '.$finalpdf['Address']['Phone'].'</td></tr>';
		//$exportStr .= '<tr><td>Email: '.$finalpdf['Transaction']['Email_Id'].'</td></tr>';
		//$exportStr .= '</table>';
		// 
		// 
		//$exportStr .= '<table border = "1" style="border-collapse: collapse;text-align:center;width:100%;">';
		//$exportStr .= '<tr><th>Service Description</th><th>Total No Of Employee</th><th>Amount Per Employee</th><th>Amount</th></tr>';
		//$exportStr .= '<tr><td>Active</td>
		//<td>'.$finalpdf['Transaction']['No_Active_Employees'].'</td>
		//<td>'.$finalpdf['Transaction']['Rate_Active_Employees'].'</td>
		//<td>'.$finalpdf['Transaction']['Amt_Active_Employees'].'</td>
		//</tr>';
		//$exportStr .= '<tr><td>Inactive</td>
		//<td>'.$finalpdf['Transaction']['No_Inactive_Employees'].'</td>
		//<td>'.$finalpdf['Transaction']['Rate_Inactive_Employees'].'</td>
		//<td>'.$finalpdf['Transaction']['Amt_Inactive_Employees'].'</td>
		//</tr>';
		//$exportStr .= '<tr><td colspan="2"></td><td style="text-align:right;"><b>SubTotal:</b></td><td>'.$subTotal.'</td></tr>';
		//if($discount>0)
		//	$exportStr .= '<tr><td colspan="2"></td><td style="text-align:right;"><b>Discount:</b></td><td>'.$discount.'</td></tr>';
		//$exportStr .= '<tr><td colspan="2"></td><td style="text-align:right;"><b>'.$finalpdf['Transaction']['Tax_Class'].'('.$finalpdf['Transaction']['Tax_Class_Percentage'].'%):</b></td><td>'.$taxClassCalculation.'</td></tr>';
		// 
		// 
		// 
		//$tscDetails1=0;$tscDetails2=0;$tscDetails3=0;$tscDetails4=0;
		//$viewTransaction =$finalpdf['Transaction'];
		//if(!empty($viewTransaction['Tax_SubClass1']))
		//{
		//	$tscDetails1 = ($viewTransaction['Tax_SubClass1_Percentage']*$taxClassCalculation)/100;
		//	$exportStr .= '<tr><td colspan="2"></td><td style="text-align:right"><b>'.$viewTransaction['Tax_SubClass1'].'('.$viewTransaction['Tax_SubClass1_Percentage'].'%):</b></td><td>'.$tscDetails1.'</td></tr>';
		//}
		//if(!empty($viewTransaction['Tax_SubClass2']))
		//{
		//	$tscDetails2 = ($viewTransaction['Tax_SubClass2_Percentage']*$taxClassCalculation)/100;
		//	$exportStr .= '<tr><td colspan="2"></td><td style="text-align:right"><b>'.$viewTransaction['Tax_SubClass2'].'('.$viewTransaction['Tax_SubClass2_Percentage'].'%):</b></td><td>'.$tscDetails2.'</td></tr>';
		//}
		//if(!empty($viewTransaction['Tax_SubClass3']))
		//{
		//	$tscDetails1 = ($viewTransaction['Tax_SubClass1_Percentage']*$taxClassCalculation)/100;
		//	$exportStr .= '<tr><td colspan="2"></td><td style="text-align:right"><b>'.$viewTransaction['Tax_SubClass3'].'('.$viewTransaction['Tax_SubClass3_Percentage'].'%):</b></td><td>'.$tscDetails3.'</td></tr>';
		//}
		//if(!empty($viewTransaction['Tax_SubClass4']))
		//{
		//	$tscDetails1 = ($viewTransaction['Tax_SubClass1_Percentage']*$taxClassCalculation)/100;
		//	$exportStr .= '<tr><td colspan="2"></td><td style="text-align:right"><b>'.$viewTransaction['Tax_SubClass4'].'('.$viewTransaction['Tax_SubClass4_Percentage'].'%):</b></td><td>'.$tscDetails4.'</td></tr>';
		//}
		//$tscDetails = $tscDetails1+$tscDetails2+$tscDetails3+$tscDetails4;
		//$netAmount  = $netAmount1+$tscDetails;
		//
		// if($discount>0)
		//{
		//    $netAmount =round($netAmount-$discount).'.00';
		//}
		//else
		//{
		//    $netAmount =round($netAmount).'.00';
		//}
		// 
		//$exportStr .= '<tr><td colspan="2"></td><td style="text-align:right;"><b>NetAmount:</b></td><td>'.$netAmount.'</td></tr>';
		//$exportStr .= '</table>';
		// 
		// 
		// 
		//$exportStr .= '</div>';
		//$exportStr .= '<div style=\'page-break-after:always;\'>&nbsp;</div>';
		//$exportStr .='<br><br><br>';
		if($format=='print'){
			return array($exportStr,$exportStrPrint);	
		}else{		
			return $exportStr;
		}
	}


	

	

	

	public function addRecipientAction()
	{
		$this->_helper->layout()->disableLayout();
		
		if(isset($_SERVER['HTTP_REFERER']))
		{
			$layout->setLayout('tab_layout');
			
			$this->view->fwdForm = new Default_Form_Manager();
		}
		else
		{
			$this->_helper->redirector('index', 'billing', 'default');
		}

	}

	public function auditPaymentAction()
	{
		$this->_helper->layout()->disableLayout();
		
		if(isset($_SERVER['HTTP_REFERER']))
		{
			if($this->_billAccess>=1 && !empty($this->_billAccess))
			{
				$layout->setLayout('tab_layout');
			}
			else
			{
				$this->view->access = 'Sorry, Access Denied...';
			}
		}
		else
		{
			$this->_helper->redirector('index', 'billing', 'default');
		}
	}

	public function __destruct()
    {
        
    }
}


?>