<?php
//===========================================================================================
//===========================================================================================
/* Program : Attendance.php											   			           *
 * Property of Caprice Technologies Pvt Ltd,                                               *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                        *
 * Coimbatore, Tamilnadu, India.														   *
 * All Rights Reserved.            														   *
 * Use of this material without the express consent of Caprice Technologies                *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law.   *
 *                                                                                    	   *
 * Description : MQL Query to retrive, add, update attendance details and also to          *
 * update status reports. Mysql Query to retrive imported attendance and attendance import *
 * format, process imported attendance and to update attendance status and rollup status   *
 *                                                                                   	   *
 *                                                                                    	   *
 * Revisions :                                                                    	       *
 *  Version    Date           Author                  Description                          *
 *  1.0        02-Feb-2015    Prasanth                Initial Version                      *
 *													  Changes in file for mobile app       *
 *                                                    1.addAttendanceForUsers()            *
 *                                                    2.employeeName().                    */
//===========================================================================================
//===========================================================================================

class Employees_Model_DbTable_FaceRecognize extends Zend_Db_Table_Abstract
{

    protected $_name = 'FaceRecognize';
    protected $_db = null;
    protected $_ehrTables = null;
    protected $_attendance = null;
	
    public function init()
    {
        $this->_db 			= Zend_Registry::get('subHrapp');
        $this->_ehrTables   = new Application_Model_DbTable_Ehr();
		$this->_dbAttendance = new Employees_Model_DbTable_Attendance();
    }
    
	/**
	 * Add attendance for users
	 */
    public function addAttendanceForUsers ($userArr,$type,$logEmpId)
	{
		//$noMathch = array();
		//$empDetails = array();
		//$notfound = 0;
		//$noMathchCnt = 0;
		//$matchCnt = 0;
		//
		//foreach ($userArr as $key => $row)
		//{
		//	$exist = /*$this->_db->fetchAll(*/$this->_db->select()->from(array('D' =>$this->_ehrTables->empPersonal),
		//																				array(new Zend_Db_Expr('count(Employee_Id)'),'Employee_Id'))
		//											->joinInner(array('F'=>$this->_ehrTables->empJob), 'F.Employee_Id=D.Employee_Id',array('F.Manager_Id'))
		//											->where('D.Employee_Id = ?', $row)/*)*/;
		//	
		//	$exist = $this->_db->fetchAll($this->_db->select()->from(array('D' =>$this->_ehrTables->empJob),
		//																				array(/*new Zend_Db_Expr('count(Employee_Id)'),*/'D.Employee_Id','D.Manager_Id'))
		//											->where('D.Employee_Id IN (?)', $userArr));
		//	
		//	if (!$exist)
		//	{
		//		$noMathch[$noMathchCnt]['Employee_Id'] = $row;
		//		
		//		++$noMathchCnt;
		//	}
		//	else
		//	{
		//		$empDetails[$key]['Employee_Id'] = $row;
		//		
		//		++$matchCnt;
		//	}
		//}
		
		$exist = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->empJob,
																 array('Employee_Id','Manager_Id'))
									->where('Employee_Id IN (?)', $userArr));
		
		if ($exist)
		{
			$empId = '';
			$managerId = '';
			$date = date('Y-m-d');
			$time = date('H:i:s');
			$attendExist = '';
			$checkleaveApplied = '';
			$added =array();
			$updated =array();
			$addcount = 0;
				
			for ($i = 0; $i < sizeof($exist); ++$i)
			{
				$existemp = $this->_db->fetchRow($this->_db->select()
												 ->from($this->_ehrTables->attendance,
														array(new Zend_Db_Expr('count(Employee_Id)'),
															  'Employee_Id','Attendance_Id','PunchIn_Date','PunchIn_Time'))
												 
												 ->where('Employee_Id = ?', $exist[$i]['Employee_Id'])
												 ->where('Approval_Status = ?', 'Draft')
												 ->where('PunchIn_Date = ?', $date));
				
				if ($type=='Check_In' && $existemp['count(Employee_Id)'] == 0)
				{
					$empId = $exist[$i]['Employee_Id'];
					$managerId = $exist[$i]['Manager_Id'];
					$workedHours=0;
					
					$insertAttendance = array('Employee_Id'=> $empId,
											  'Approver_Id' => $managerId,
											  'Approval_Status'=> 'Draft',
											  'PunchIn_Date'=> $date,
											  'PunchIn_Time'=> $time,
											  'Total_Hours'  => $workedHours,
											  'Exclude_Break_Hours'  => 0,
											  'Added_Date'=> date('Y-m-d H:i:s'),
											  'Added_By'=>$logEmpId);
									
					$checkleaveApplied = $this->_dbAttendance->getLeaveApplied($empId,$insertAttendance['PunchIn_Date']);
					
					if($checkleaveApplied==0)
					{
						$inserted = $this->_db->insert($this->_ehrTables->attendance,$insertAttendance);
						 
						 if($inserted)
						 {
							$empName = $this->employeeName($insertAttendance['Employee_Id']);
							$addcount = ++$addcount;
							$added = array('Emp_Id'=>$insertAttendance['Employee_Id'],'Emp_Name'=>$empName);
						 }
					}
					
					//else /*if($checkleaveApplied !=0)*/
					//	return Zend_Json::encode(array('success' => true, 'msg' => 'You have applied leave on this date.'));
					//
					//if($inserted)
					//	return Zend_Json::encode(array('success' => true, 'msg' => 'Attendance added successfully.'));
					//else
					//	return Zend_Json::encode(array('success' => true, 'msg' => 'Cant add Attendance'));
				}
				else if($type=='Check_Out' && $existemp['Attendance_Id']!= 0 && $existemp['Attendance_Id'] !='')
				{
					
					$empId = $exist[$i]['Employee_Id'];
					$managerId = $exist[$i]['Manager_Id'];
										
					$updateAttendance = array(/*'Attendance_Id'=>,*/
											  'Employee_Id'=> $empId,
											  'Approver_Id' => $managerId,
											  'Approval_Status'=> 'Applied',
											  'PunchIn_Date'=>$existemp['PunchIn_Date'],
											  'PunchIn_Time'=>$existemp['PunchIn_Time'],
											  'PunchOut_Date'=> $date,
											  'PunchOut_Time'=> $time,
											  'Modified_Date'=> date('Y-m-d H:i:s'),
											  'Added_By'=>$logEmpId);
					
					$updateAttendance['Attendance_Id'] = $existemp['Attendance_Id'];
					
					$workedHours= strtotime($updateAttendance['PunchOut_Time']) - strtotime($existemp['PunchIn_Time']);

					$updateAttendance['Total_Hours']=$workedHours/60/60;
					
					$attendExist = $this->_dbAttendance->getAttendanceExist($updateAttendance); 
				
					if($attendExist == 0)
					{
						$updated = $this->_db->update($this->_ehrTables->attendance, $updateAttendance, array('Attendance_Id = '.$updateAttendance['Attendance_Id']));
						if($updated)
						{
							$empName = $this->employeeName($updateAttendance['Employee_Id']);
							$updateCount  = ++$updateCount;
							$updated = array('Emp_Id'=>$updateAttendance['Employee_Id'],'Emp_Name'=>$empName);
						}
					}	
						
					//else /*if($attendExist!=0)*/
					//	return Zend_Json::encode(array('success' => true, 'msg' => 'You have already applied attendance for this duration.'));
					//
					//if($updated)
					//	return Zend_Json::encode(array('success' => true, 'msg' => 'Attendance updated successfully.'));
					//else
					//	return Zend_Json::encode(array('success' => true, 'msg' => 'Cant update Attendance'));
					
					
					
					////$inserted = $this->_db->insert($this->_ehrTables->attendance,$insertAttendance);
					//
					////$diff=strtotime($formdata['PunchOut_Time']) - strtotime($formdata['PunchIn_Time']);
				}
				//else
				//{
				//	return Zend_Json::encode(array('success' => true, 'msg' => 'Invalid Employee.'));
				//}
			}
			
			if ($type == 'Check_In')
			{
				$failed = sizeof($userArr) - $addcount;
				$details = array('Added'=>$added,'Failed'=>$failed);
				return $details;
			}
			else
			{
				$failed = sizeof($userArr) - $updateCount;
				$details = array('Updated'=>$updated,'Failed'=>$failed);
				return $details;
			}
			
			//$attApprover = $this->_dbJobDetail->approverName($this->_logEmpId);
			
			//if no draft record exists for the employee
			//addAttendance($formdata, $formName, $sessionEmp)
		}
		else
		{
			return Zend_Json::encode(array('success' => false, 'msg' => 'Unable to make attendance'));
		}
		
		//return Zend_Json::encode(array('success' => true, 'msg' => 'Successfully Update Attendance'));
	}
	
	/**
	 * Get employee name by employeeId
	 */
	public function employeeName($empId)
	{
		return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empPersonal,array('Employee_Name'=>new Zend_Db_Expr("CONCAT(Emp_First_Name, ' ', Emp_Last_Name)")))
									->where('Employee_Id=?',$empId));
	}
	
}