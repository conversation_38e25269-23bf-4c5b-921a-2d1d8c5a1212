:root {
  --primary-color: #260029;
  --secondary-color: #ec407a;
  --hover-color: #FAD4E1;
}

body {
  height: 100%;
  background: #F5F5F5;
  color: #5B5B5B;
  font-family: 'Lato', 'Open Sans', Helvetica, sans-serif !important;
  line-height: 1.42857143;
  -webkit-font-smoothing: antialiased;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

.custom-loading-cls {
  background: #dad8d8;
  color: #666666;
  position: fixed;
  height: 100%;
  width: 100%;
  z-index: 5000;
  top: 0;
  left: 0;
  float: left;
  text-align: center;
  opacity: .80;
}

.custom_spinner {
    margin: 0 auto;
    text-align: center;
    margin-top: 50vh;
}
.custom-loading-cls {
  background: white;
  position: fixed;
  height: 100%;
  width: 100%;
  z-index: 5000;
  top: 0;
  left: 0;
  float: left;
  text-align: center;
  background-repeat: no-repeat;
  background-size: cover;
  /* background-image: url('./app-loader-bg.png'), linear-gradient(white,white); */
  background-blend-mode: darken;
  opacity: 0.85;
}

@media screen and (max-width: 960px){
  .custom-loading-cls {
    background-image: none !important;
  }
}

.loader {
  height: 20px;
  width: 450px;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
}
.loader--dot {
  animation-name: loader;
  animation-timing-function: ease-in-out;
  animation-duration: 3s;
  animation-iteration-count: infinite;
  height: 20px;
  width: 20px;
  border-radius: 100%;
  background-color: black;
  position: absolute;
  border: 2px solid white;
}
.loader--dot:first-child {
  background-color: #84db39;
  animation-delay: 0.5s;
}
.loader--dot:nth-child(2) {
  background-color: #804bbc;
  animation-delay: 0.4s;
}
.loader--dot:nth-child(3) {
  background-color: #fa3c5a;
  animation-delay: 0.3s;
}
.loader--dot:nth-child(4) {
  background-color: #e58315;
  animation-delay: 0.2s;
}
.loader--dot:nth-child(5) {
  background-color: #3bade3;
  animation-delay: 0.1s;
}
.loader--dot:nth-child(6) {
  background-color: #f6e61a;
  animation-delay: 0s;
}
.custom-loading-cls {
  background: white;
  position: fixed;
  height: 100%;
  width: 100%;
  z-index: 5000;
  top: 0;
  left: 0;
  float: left;
  text-align: center;
  opacity: 0.8;
}

@keyframes loader {
  15% {
    transform: translateX(0);
  }
  45% {
    transform: translateX(230px);
  }
  65% {
    transform: translateX(230px);
  }
  95% {
    transform: translateX(0);
  }
}
.dot {
  background: var(--secondary-color);
}
.dot, .dot:after {
  display: inline-block;
  width: 2em;
  height: 2em;
  border-radius: 50%;
  animation: a 1.5s calc(((var(--i) + var(--o, 0))/var(--n) - 1)*1.5s) infinite;
}
.dot:after {
  --o: 1;
  background: #400059;
  content: "";
}

@keyframes a {
  0%, 50% {
    transform: scale(0);
  }
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

  label.error {
    color: red !important;
    margin-top: 2%;
  }
  div.form-error{
    color: red !important;
    font-size: 13px !important;
    font-style: normal !important;
  }
  .networkErrorImgDiv, .hr-app-error-page-logo, .shake-mail-div{
    margin-top: 4%;
    text-align: center;
  }
  .footer p{
    color: gray;
  }
  .network-error{
    text-align: center;
    color: white;
  }
  .network-error-msg{
    margin-top: 4%;
  }

input.form-error, input.form-success, input.form-error:focus, input.form-success:focus {
    font-style: normal !important;
    font-family: 'Lato', 'Open Sans', Helvetica, sans-serif !important
  }
.loginImgDiv{
    padding: 2%;
    padding-left: 10% !important;
}

.loginImg{
    margin-top: 8%
}
.footer p{
    text-align: center;
    font-size: 12px;
}
.hr-app-login-Types{
    margin: auto;
    padding-top: 10%;
    padding-right: 17%;
    padding-left: 17%;
}
.hr-app-logo{
    text-align: center;
}
.sign-in-card{
    width: 100%;
    border-radius: 15px;
    padding-top: 20px;
    padding-bottom: 20px;
    padding-left: 25px;
    background: white;
}
  .card-signin-enabled{
    color: var(--primary-color);
    border: solid 2px var(--primary-color);
    border-radius: 15px;
    padding-top: 23px;
    padding-bottom: 23px;
    padding-left: 25px;
  }

  .card-signin-test {
    display: block;
  }

  .card-signin-disabled{
    color: #989898 !important;
    border: solid 2px #989898 !important;
    border-radius: 15px;
    padding-top: 23px;
    padding-bottom: 23px;
    padding-left: 25px;
    cursor:not-allowed;
  }

  .card-signin-warning{
    color: chocolate !important;
    border: solid 2px #989898 !important;
    border-radius: 15px;
    padding-top: 23px;
    padding-bottom: 23px;
    padding-left: 25px;
    cursor:not-allowed;
    font-size: 0.8em;
  }

  .card-sigin-warning-icon {
    width: 25px;
    height: 25px;
    margin-top: 10px;
  }
  .card-signin-center{
    display: flex;
    justify-content: center;
    flex-direction: column;
    height: 95px;
  }

  .hidden{display:none!important;visibility:hidden!important}
  .card-login, .card-reset, .card-new-password, .card-mail-alert, .card-verification-mail-alert, .card-mail-failure, .card-sent-link-succuss, .card-mobile-login {
    border-radius: 15px;
    padding-top: 20px;
    padding-bottom: 20px;
    padding-left: 50px;
    padding-right: 50px;
    cursor: default;
    background: white;
  }
  
  .card-new-password-form {
    min-height: 150px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .signInEmailLink{
    font-size: 13px;
  }
  a, i{
    cursor: pointer; 
  }
  .card-signin-enabled:hover{
    box-shadow: 0 9px 23px 0 grey;
    cursor: pointer;
  }
  .hideLoginCard{
    cursor: pointer;
  }
  .card-tag{
    font-size: 19px;
    border-radius: 20px;
    box-shadow: 0 4px 3px 0 rgba(0,0,0,0.2);
    transition: 0.3s;
    background-color: white;
  }
  .card-tag input{
    font-size: 19px;
  }
  .container {
    padding: 7px 16px;
    width: 100% !important;
  }
  .card-signin-text{
    line-height: 1.5;
  }
  .card-back i{
    font-size: 25px;
  }

  .rememberPassword{
    width: 15px; 
    height: 15px; 
  }
  .refresh-div{
    width: 30%;
    margin-left: 35%;
    margin-top: 2%;
    margin-bottom: 2%;
  }
  .success-icon{
    color: orange !important;
  }
  .wrong-icon{
    color: red !important;
  }
  .email-submit-button, .email-reset-button, .refresh-btn, .email-confirm-button,.resend-btn {
    background-color: var(--primary-color); 
    color: white;
    border: none;
    padding: 11px 48px;
    text-align: center;
    text-decoration: none;
    display: block;
    cursor: pointer;
    margin: 0 auto;
    border-radius: 30px;
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
  }
  .email-submit-button:hover, .email-reset-button:hover, .email-submit-button:focus, .email-reset-button:focus{
    box-shadow: 0 9px 23px 0 grey
  
  }

  .btn-disabled{
    color: #989898 !important;
    background-color: white !important;
    cursor:not-allowed;
  }
  .refresh-btn:hover , .resend-btn:hover, .refresh-btn:focus , .resend-btn:focus{
    box-shadow: 0 9px 46px 0 black;
  }

/* Ripple effect */
button {
  background-position: center;
  transition: background 0.8s;
}
button:hover {
  background: var(--hover-color) radial-gradient(circle, transparent 1%, var(--hover-color) 1%) center/15000%;
}
button:active {
  background-color: white;
  background-size: 100%;
  transition: background 0s;
}

/* Button style */



  .forgotPassword{
    font-size: 13px;
  }
  .forgotPasswordDiv{
    text-align: center;
  }
  .login-card{
    margin-bottom: 9% !important;
  }

  input[type=checkbox] + label {
    display: block;
    margin: 0.2em;
    cursor: pointer;
    padding: 0.2em;
  }
  
  input[type=checkbox] {
    display: none;
  }
  
  input[type=checkbox] + label:before {
    content: "\2714";
    border: 0.1em solid gray;
    border-radius: 0.2em;
    display: inline-block;
    width: 16px;
    height: 16px;
    padding:2px;
    padding-bottom: 0.3em;
    margin-right: 3%;
    vertical-align: bottom;
    color: transparent;
    transition: .2s;
  }
  
  input[type=checkbox] + label:active:before {
    transform: scale(0);
  }
  
  input[type=checkbox]:checked + label:before {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #fff;
  }
  
  input:focus{
    background: white !important;
    background-image:linear-gradient(#0097a7,#0097a7),linear-gradient(#d2d2d2,#d2d2d2);
    border-bottom: 1px solid var(--primary-color) !important;
    color: black !important;
  }
  label{
    font-size: 13px !important;
    font-weight: normal !important;
  }

  a.google-signin-error {
    color: var(--primary-color) !important;
  }

  a{
    color: gray !important;
  }
  a:hover {
    color: var(--primary-color) !important;
    font-weight: 500;
  }
  div.form-error{
    font-style: italic !important;
  }
  p {
    line-height: 13px !important;
  }
  .append-icon i{
    color: darkgrey;
    font-size: 100% !important;
  }

  h2{
    font-size: 25px !important;
  }
  .hideLoginCard, .hideMblLoginCard{
    font-size: 15px !important;
    color: gray;
  }

  .getOtpLink{
    color: var(--primary-color) !important;
    font-size: 16px !important;
  }

  .otp-timmer{
    color: var(--primary-color);
    font-size: 20px !important;
  }
  div.form-group .iti{
    width: 100%;
    height: 32.9844px;
  }
  .iti .iti__country-list{
    width: 392px;
  }

  .resendOtp{
    right: 32px !important;
    color: var(--primary-color) !important;
    display: none;
  }
  .iti--separate-dial-code .iti__selected-flag {
    background-color: white !important;
  }
  .formMobileNo{
    margin: 2% !important;
  }

  .user-success-input{
    border-radius: 20px;
    background-color: whitesmoke;
    padding: 1%;

    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;

  }
  .warning-icon{
    color: red !important;
  }
  .user-welcome{
    font-size: 25px;
  }

  .append-icon .warning-icon{
    position: initial !important;
  }

  div.form-error:not(:empty):before {
    font-style: normal;
    font-family: FontAwesome;
    display: inline-block;
    padding-right: 6px;
    vertical-align: middle;
    content: "\f071";
 }

 .mailAlertContent {
  text-align: center;
  color: gray;
  font-weight: 800;
  letter-spacing: normal;
  line-height: normal;
  font-size: 1.1em;
 }

 .icon-padding {
    padding-left: 0px; 
    padding-right: 50px !important;
  }
  @media screen and (max-width: 575px){
  .icon-padding {
    padding-right: 0px !important;
  }
}

 .mailAlertSpamContent{
   color: gray;
   text-align: center;
   font-size: 0.9em;
   padding: 0px 15px;
 }

  /* Mobile View CSS */
@media screen and (min-width: 1px) and (max-width: 1100px) {
  .success-gif{
    width: auto;
    height: 30%;
  }
  .networkErrorImg{
    height: 200px;
  }
  .network-error-msg{
    font-size: 15px;
  }
  .append-icon i{ 
     font-size: 20px !important;
  }
  .loginImgDiv {
      display: none;
  }
  .getOtpLink{
    color: var(--primary-color) !important;
    font-size: 10px !important;
  }
  .otp-timmer{
    font-size: 10px !important;
  }
  .hr-app-login-Types{
    padding-left: 1%;
    padding-right: 1%;
    padding-top: 10%;
  }
  .hr-app-logo{
    text-align: center;
  }
  .hideLoginCard{
    font-size: 30px;
  }
  body {
    background-size: cover  !important;
    background-repeat:no-repeat !important;
    width: auto !important;
    font-family: Roboto !important;
    background-attachment: fixed !important;
    background-color:#FEF8F5 !important;

  }

  .sign-in-card{  
    width: 100%;
    border-radius: 15px;
    padding-top: 20px !important;
    padding-bottom: 20px !important;
    padding-left: 20px;
  }
  
  .card-login, .card-reset, .card-new-password, .card-mail-alert, .card-verification-mail-alert, .card-mail-failure, .card-sent-link-succuss, .card-mobile-login {
    padding-left: 30px !important;
    padding-right: 30px !important;
  }
  .card-tag,.card-tag input, .login-card {
    font-size: 14px !important;
    
  }
  .card-tag{
    margin-bottom: 40px;
    border-radius: 15px;
    z-index: 1000;

  }
  label, a, h4 {
    font-size: 12px !important;
  }
  .card-tag input, .card-tag input:focus{
    
    padding-bottom: 4%;
    padding-top: 4%;
  }
  footer {
    /* position: fixed; */
    bottom: 0;
    width: 100%;
    z-index: 2000;
  
  }
  .footer {
    bottom: 0;
    width: 100%;
    z-index: 2000;
    /* position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    color: gray;
    text-align: center; */
    display: block !important;
 }
  .footer p, .footer p a {
    font-size: 11px !important;
    /* line-height: 5px !important; */
  }
  .workflow-select-fields{
    margin-bottom: 80px !important;
  }

  .rememberPassword{
    width: 1px; 
    height: 1px; 
  }
  .logo-img{
    height: 5rem;
    width: 20rem;
  }
  .mail-logo{
    height: 20px;
    width: 20px;
    margin-right: 10px !important;
  }
  .mobile-logo{
    height: 30px;
    width: 20px;
    margin-right: 10px !important;
  }
  .timer-logo{
    height: 30px;
    width: auto;
    margin-right: 10px !important;
  }
  .login-card {
    margin-bottom: 10% !important;
  }
  h2{
    font-size: 45px !important;
  }
  h5{
    font-size: 17px !important;
  }
  .container, .resendOTP{
    font-size: 13px !important;
  }
  .email-submit-button, .email-reset-button {
    border: none;
    padding-top: 7%;
    padding-bottom: 7%;
    width: 60%;
    border-radius: 70px;
     box-shadow: 0 4px 28px 0 rgba(0,0,0,0.2);
  }
  .forRememberPassword{
    font-weight: 300 !important;
  }
  input[type=checkbox] + label:before {
    width: 16px !important;
    height: 16px !important;
  }
  .forgotPasswordDiv h3{
    font-size: 20px;
  }
  .forgotPasswordDiv h5{
    line-height: 20px;
    font-size: 14px !important;
  }
  div.form-error{
    font-size: 13px !important;
  }
  .networkErrorImgDiv{
    margin-top: 100px;
  }

  .refresh-btn,.resend-btn   {
    padding: 11px 48px;
    font-size: 17px;
    border-radius: 30px;
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
  }

  .refresh-div{
    width: 40%;
    margin-left: 30%;
    margin-top: 2%;
    margin-bottom: 2%;
  }

  .card-signin-center{
    display: flex;
    justify-content: center;
    flex-direction: column;
    height: 85px;
  }

}

