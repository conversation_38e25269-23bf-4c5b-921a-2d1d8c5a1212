<?php

class Employees_Model_DbTable_EmployeeBankAccount extends Zend_Db_Table_Abstract
{

    protected $_db          = null;
    protected $_ehrTables   = null;
    protected $_dbCommonFun = null;
	protected $_orgDF       = null;
    protected $_orgDetails    = null;
    
    public function init()
    {
        $this->_ehrTables   = new Application_Model_DbTable_Ehr();
        $this->_dbCommonFun = new Application_Model_DbTable_CommonFunction();
		$this->_db          = Zend_Registry::get('subHrapp');
        $this->_orgDF       = $this->_ehrTables->orgDateformat();
        $this->_orgDetails  = Zend_Registry::get('orgDetails');
    }
	
    public function listEmployeeBankAccountDetails($page, $rows, $logEmpId, $searchAll=null,$employeeId)
    {		
		/**
		 *	Query to fetch data from various tables
		*/
       $qryEmployeeBankAccount = $this->_db->select()
								->from(array('bank'=>$this->_ehrTables->empBank),
									   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS bank.Employee_Id as count'),'bank.Bank_Id','bank.Employee_Id',
											 'bank.Bank_Account_Number','bank.Branch_Name','bank.IFSC_Code',
                                             'bank.Street','bank.City','bank.State','bank.Zip','bank.Account_Type_Id','bank.Credit_Account','bank.Emp_Bank_Id','bank.Bank_Name',
                                             'bank.Beneficiary_Id','bank.Status','Log_Id'=>new Zend_Db_Expr($logEmpId),                                      
											 'DT_RowClass' => new Zend_Db_Expr('"employeeBankAccount"'),
											 'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', bank.Employee_Id)")))
								
                                 ->joinInner(array('EP'=>$this->_ehrTables->empPersonal),'bank.Employee_Id=EP.Employee_Id',
								             array('Employee_Name'=>new Zend_Db_Expr("CONCAT(EP.Emp_First_Name,' ',EP.Emp_Last_Name)")))
                                 
                                 ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'EP.Employee_Id=EJ.Employee_Id',
                                             array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN EP.Employee_Id ELSE EJ.User_Defined_EmpId END')))
                           
                                 ->joinInner(array('ACT'=>$this->_ehrTables->accountType),'bank.Account_Type_Id=ACT.Account_Type_Id',
                                             array('ACT.Account_Type'))
                                 
                                 ->joinLeft(array('BD' => $this->_ehrTables->bankDetails),'bank.Emp_Bank_Id = BD.Bank_Id',array('BD.Bank_Name as Emp_Bank'))
                                  
                                 ->limit($rows, $page);
		
		/**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			$conditions  = $this->_db->quoteInto('bank.Employee_Id  Like ?', "%$searchAll%");
            $conditions .= $this->_db->quoteInto('or EP.Emp_First_Name Like ?', "%$searchAll%");
           	$conditions .= $this->_db->quoteInto('or EJ.User_Defined_EmpId Like ?', "%$searchAll%");
 	
			$qryEmployeeBankAccount->where($conditions);		
        }
        
            $qryEmployeeBankAccount->where('bank.Employee_Id = ?',$employeeId);
		/**
		 * SQL queries
		 * Get data to display
		*/
         
		$employeeBankAccount = $this->_db->fetchAll($qryEmployeeBankAccount);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
       
		/* Total data set length */
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empBank, new Zend_Db_Expr('COUNT(Employee_Id)')));
		
		/**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $employeeBankAccount);       
	}
	

    public function listEmployeeDetails($page, $rows, $sortField, $sortOrder, $logEmpId, $searchAll=null, $employeeBankAccess)
    {
     switch ($sortField)
		{
			case 0: $sortField = 'EJ.User_Defined_EmpId'; break;
            case 1: $sortField = 'EP.Emp_First_Name'; break;
        }
        
        $qryEmployeeBankAccount = $this->_db->select()
								->from(array('bank'=>$this->_ehrTables->empBank),
									   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS bank.Employee_Id as count'),'bank.Employee_Id'))
								
                                 ->joinInner(array('EP'=>$this->_ehrTables->empPersonal),'bank.Employee_Id=EP.Employee_Id',
								    array('Employee_Name'=>new Zend_Db_Expr("CONCAT(EP.Emp_First_Name,' ',EP.Emp_Last_Name)")))
                                 
						
                                ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'EP.Employee_Id=EJ.Employee_Id',
                                            array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN EP.Employee_Id ELSE EJ.User_Defined_EmpId END')))

                                ->group('bank.Employee_Id')                     
								->order("$sortField $sortOrder")
								->limit($rows, $page);
		
		/**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
             $conditions = $this->_db->quoteInto(new Zend_Db_Expr('Concat(EP.Emp_First_Name," ",EP.Emp_Last_Name) Like ?'),"%$searchAll%");
			    $conditions .= $this->_db->quoteInto('or bank.Employee_Id Like ?', "%$searchAll%");
             $conditions .= $this->_db->quoteInto('or EJ.User_Defined_EmpId Like ?', "%$searchAll%");
			
			    $qryEmployeeBankAccount->where($conditions);		
        }

        if(empty($employeeBankAccess['Admin']))
		{
			$getEmployeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
                                                  ->where('Manager_Id = ?', $employeeBankAccess['LogId']));
                 
            /*
            To get employee access for manager. If it returns 1, then they can't access employees bank account details.
            */
            $restrictEmpAccessForManager = $this->_orgDetails['Restrict_Emp_Access_For_Manager'];

            if ( $employeeBankAccess['Is_Manager'] == 1 && !empty($getEmployeeId) && $restrictEmpAccessForManager != 1)
			{
				$qryEmployeeBankAccount->where('bank.Employee_Id = :EmpId or bank.Employee_Id IN (?)', $getEmployeeId)
							  ->bind(array('EmpId'=>$employeeBankAccess['LogId']));
			}
			else
			{
				$qryEmployeeBankAccount->where('bank.Employee_Id = ?', $employeeBankAccess['LogId']);
			}
		}

        if(!empty($employeeBankAccess['Admin']))
		{
			$qryEmployeeBankAccount = $this->_dbCommonFun->formServiceProviderQuery($qryEmployeeBankAccount,'EJ.Service_Provider_Id',$employeeBankAccess['LogId']);
		}

        $qryEmployeeBankAccount = $this->_dbCommonFun->getDivisionDetails($qryEmployeeBankAccount,'EJ.Department_Id');

        $employeeBankAccount = $this->_db->fetchAll($qryEmployeeBankAccount);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
        /* Total data set length */
        
		$iTotalQry = $this->_db->select()->from($this->_ehrTables->empBank, new Zend_Db_Expr('COUNT(Employee_Id)'))
                     ->group('Employee_Id');
         
        if(empty($employeeBankAccess['Admin']))
        {
            $getEmployeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
                                                ->where('Manager_Id = ?', $employeeBankAccess['LogId']));
        
            if ( $employeeBankAccess['Is_Manager'] == 1 && !empty($getEmployeeId))
            {

                $iTotalQry->where('Employee_Id = :EmpId  or Employee_Id IN (?)', $getEmployeeId)
                          ->bind(array('EmpId'=>$employeeBankAccess['LogId']));
                
            }
            else
            {
                $iTotalQry->where('Employee_Id = ?', $employeeBankAccess['LogId']);
            }
        }  
       
        $iTotal = $this->_db->fetchCol($iTotalQry);
		
    	$iTotal = empty($iTotal) ? 0 : count($iTotal);  
    
		/**     
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $employeeBankAccount);	
    }
    //Get already exist employee bank account
    public function existBankAccount($employeeBankAccount)
    {
        $qryEmployeeBankAccountStatus = $this->_db->select()->from($this->_ehrTables->empBank, new Zend_Db_Expr('count(Bank_Id)'))
                                        ->where('Employee_Id = ?', $employeeBankAccount['Employee_Id'])
                                        ->where('Status = ?', $employeeBankAccount['Status'])
                                        ->where('Credit_Account = ?',$employeeBankAccount['Credit_Account']);

        if (!empty($employeeBankAccount['Bank_Id']))
        {
        $qryEmployeeBankAccountStatus->where('Bank_Id != ?', $employeeBankAccount['Bank_Id']);
        }      
        $employeeBankAccountStatusExists = $this->_db->fetchOne($qryEmployeeBankAccountStatus);
        return $employeeBankAccountStatusExists;
    }
	/**
	 *	Update EmployeeBankAccount details in EmployeeBankAccount table and update system log too
    */
    public function updateEmployeeBankAccount($employeeBankAccount, $logEmpId, $formName)
	{
        $qryEmployeeBankAccount = $this->_db->select()->from(array('EB'=>$this->_ehrTables->empBank),new Zend_Db_Expr('Count(Bank_Id)'))
                                                    ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'EB.Employee_Id=EJ.Employee_Id',array(''))
                                                    ->where('Bank_Account_Number = ?', $employeeBankAccount['Bank_Account_Number'])
                                                    ->where('EJ.Emp_Status=?','Active');


        if (!empty($employeeBankAccount['Bank_Id']))
        {
            $qryEmployeeBankAccount->where('Bank_Id != ?', $employeeBankAccount['Bank_Id']);
        }

        $isExist = $this->_db->fetchOne($qryEmployeeBankAccount);
 		
             
         /**
		 *	For one Employee Status should be Active only for one record
		*/
    
        if($employeeBankAccount['Status']=='Active')
        {
            if($employeeBankAccount['Credit_Account']=='Salary Account')
            {
               $employeeSalaryAccountStatusExists = $this->existBankAccount($employeeBankAccount);

               if(!empty($employeeSalaryAccountStatusExists))
               {
                return array('success'=>false, 'msg'=>'Bank Account Status Should be Active only for one salary account', 'type'=>'info');
               }
            }
           else if($employeeBankAccount['Credit_Account']=='Reimbursement Account')
           {
               $employeeReimbursementAccountStatusExists = $this->existBankAccount($employeeBankAccount);

               if(!empty($employeeReimbursementAccountStatusExists ))
                { 
                  return array('success'=>false, 'msg'=>'Bank Account Status Should be Active only for one Reimbursement account', 'type'=>'info');
                }
            }
                
        }
        else
        {
            $employeeSalaryAccountStatusExists='';
            $employeeReimbursementAccountStatusExists='';
        }
           
         /**
		 *	If EmployeeBankAccount Status isn't Active for more than one record for one employee then process add/update
		*/
        
         if(empty($employeeSalaryAccountStatusExists) || empty($employeeReimbursementAccountStatusExists))
         {
              /**
             *	If EmployeeBankAccount name isn't exist then process add/update 
            */
           if ($isExist == 0)
           {
                /**
                 *	If Bank id exist then process update action
                */
        
               if(($employeeBankAccount['Bank_Id'] >0 ))
                {
                    $action = 'Edit';
                  
                    $updated = $this->_db->update($this->_ehrTables->empBank, $employeeBankAccount, array('Bank_Id = '. $employeeBankAccount['Bank_Id']));
             
                    $updated = 1;
                    $employeeBankAccount['Bank_Id'] = 'Bank_Id';

               }
                else
              {
                    /**
                     *	If Bank id is empty then process add action
                    */
                   $action = 'Add';
                    
                   $updated = $this->_db->insert($this->_ehrTables->empBank, $employeeBankAccount);
               }
               
                /**
                 *	this function will handle
                 *		update system log function
                 *		clear submit lock fucntion
                 *		return success/failure array
                */

                $trackSysLog= $this->_dbCommonFun->updateResult (array('updated' => $updated,
                                                                'action'         => $action,
                                                                'trackingColumn' => $employeeBankAccount['Bank_Id'],
                                                                'formName'       => $formName,
                                                                'sessionId'      => $logEmpId,
                                                                'tableName'      => $this->_ehrTables->empBank));
                $trackSysLog['Bank_Id']    = $employeeBankAccount['Bank_Id'];
            
                return $trackSysLog;    
           }  
           else
           {
                return array('success' => false, 'msg'=>'Bank Account number already exist!' , 'type'=>'info');
           }
        }
        
              
    }


    public function deleteEmployeeBankAccount( $bankId, $logEmpId, $formName )
	{

	    if (!empty($bankId) && !empty($logEmpId) && !empty($formName))
        {
			$deleted = 0;
			
			/**
			 *	Get the EmployeeBankAccount name, lock flag value from EmployeeBankAccount table using bank id
			*/
            $projRow = $this->_db->fetchRow($this->_db->select()
											 ->from($this->_ehrTables->empBank, array('Lock_Flag'))
											 ->where('Bank_Id = ?', $bankId));	
            /**
			 *	Check lockflag is empty continue to delete or not show error message like employee is editing that record
			 */
			if ($projRow['Lock_Flag'] == 0)
			{
				/* Delete activity */
				$deleted = $this->_db->delete($this->_ehrTables->empBank, 'Bank_Id='.(int)$bankId);
			}
			
			/**
			 *	delete activity for common function
			 *		1)check lock is exist or not.
			 *			If lock is exist then show error message like employee is open record for update.
			 *		2)If No lockflag then process delete activity
			 *		3)Update delete activity in system log
			 *		4)return success/failure message
			*/
			return $this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
															'lockFlag'       => $projRow['Lock_Flag'],
															'formName'       => $formName,
        													'trackingColumn' => $bankId,
															'sessionId'      => $logEmpId));
		}
        else
        {
            return array('success'=>false, 'msg'=>'Unable to delete '.$formName.'. Please, contact system admin', 'type'=>'info');
        }
     }

    public function __destruct()
    {
        
    }

 }



