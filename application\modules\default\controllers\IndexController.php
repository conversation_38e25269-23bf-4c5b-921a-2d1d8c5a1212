<?php
//===========================================================================================
//===========================================================================================
/* Program : IndexController.php												           *
 * Property of Caprice Technologies Pvt Ltd,                                               *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                        *
 * Coimbatore, Tamilnadu, India.														   *
 * All Rights Reserved.            														   *
 * Use of this material without the express consent of Caprice Technologies                *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law.   * 
 *                                                                                    	   *
 * Description : Home Page to list employee task, reporting employee, alerts, employee     *
 * statistics in the organization and employee profile summary              			   *
 *                                                                                   	   *
 *                                                                                    	   *
 * Revisions :                                                                    	       *
 *  Version    Date           Authors                 Description                          *
 *  0.1        30-May-2013    <PERSON>rmadha,Sandhosh       Initial Version  		               *
 *  0.2        06-Jul-2014    Mahesh                  Added alert for award and finclosure *                                                                                    	
 *                                                                                    	   *
 *  1.0        02-Feb-2015    Prasanth                Changes in file for mobiles app      *
 *                                                    Added actions :                      *
 *                                                    1.listFormNamesAction                *
 *                                                    2.mobileHomeAction                   */
//===========================================================================================
//===========================================================================================

include APPLICATION_PATH.'/validations/Validations.php';
class Default_IndexController extends Zend_Controller_Action
{
    protected $_logEmpId = null;
    protected $_dbAccessRights = null;
    protected $_isMobile = null;
    protected $_isDevice = null;
    protected $_hrappMobile = null;
    protected $_dbAlerts = null;
    protected $_checkSession = null;
	protected $_dbAttendance = null;
	protected $_dbComment = null;
    protected $_validation = null;
    protected $_dbEftConfiguration =null;
    protected $_basePath = null;
    
    public function init()
    {
        $this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
        $this->_dbEftConfiguration  = new Organization_Model_DbTable_EftConfiguration();
		$this->_isMobile = $this->_hrappMobile->checkDevice();
		$this->_checkSession = $this->_hrappMobile->checkAuth ();
        $this->_isDevice = Zend_Registry::get('DeviceType');
        $userSession = $this->_hrappMobile->getUserDetails ();
        
        if ($this->_checkSession && !empty($userSession))
        {
			$this->_validation 	   = new Validations();
            $this->_dbAccessRights = new Default_Model_DbTable_AccessRights();
            $this->_dbAlerts       = new Default_Model_DbTable_Alerts();
            $this->_dbPersonal     = new Employees_Model_DbTable_Personal();
            $this->_ehrTables      = new Application_Model_DbTable_Ehr();
            $this->_dbAttendance   = new Employees_Model_DbTable_Attendance();
            $this->_dbComment      = new Payroll_Model_DbTable_PayrollComment();
            $this->_dbEftConfiguration   = new Organization_Model_DbTable_EftConfiguration();
            $this->_basePath          = new Zend_View_Helper_BaseUrl();
			
            $this->_logEmpId = $userSession['logUserId'];
            //$this->_dbAccessRights->refreshUserSessionTimestamp($this->_logEmpId);

            $this->_attendanceAccessRights          = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, 'Attendance');
            $this->_dashboardAttendanceAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, 'Dashboard Attendance');
		    $this->_attendanceBoxAccessRights       = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, 'Attendance Box');
	        $this->_assignmentAccessRights          = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, 'Assignments');
            $this->_orgAccountAccessRights          = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, 'Organization Account');
        }
        else
        {
            $this->_redirect('auth');
        } 
    }

    public function indexAction()
    {
        $checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();
        // get org code
        $orgCode = $this->_ehrTables->getOrgCode();
        // function to get organisation subscribe plan
        $orgSubscribedPlan = $this->_hrappMobile->checkOrganisationSubscribedPlan($orgCode);
        // Check value exists or not
        if(isset($orgSubscribedPlan)){
            $orgSubscribedPlan = $orgSubscribedPlan;
            setcookie('orgSubscribedPlan',$orgSubscribedPlan,time() +(8640000 * 30),'/','', true);
        }
        if ($this->_checkSession && $checkSessionAuth)
        {
			$this->_helper->layout->disableLayout();
            $this->_helper->layout->setLayout('admin_layout');

            $protocol = stripos($_SERVER['SERVER_PROTOCOL'],'https') === 0 ? 'https://' : 'http://';

            // If the organization subscribed only Employee monitoring module then during login we have to
            // redirect them to employee monitoring dashboard
            if($orgSubscribedPlan === 'EMPLOYEEMONITORINGDASHBOARD'){
                if (Zend_Registry::get('Production')) {
                    $redirectionurl = $protocol.$_SERVER['HTTP_HOST'].'/in/productivity-monitoring/activity-dashboard';
                } else {
                    $redirectionurl = 'http://localhost/hrapponline/in/productivity-monitoring/activity-dashboard';
                }
            }
            // If the organization subscribe whole Recruitment Dashboard plan then they will be redirected to recruitment dashboard
            else if($orgSubscribedPlan === 'RECRUITMENTDASHBOARD'){
                if (Zend_Registry::get('Production')) {
                    $redirectionurl = $protocol.$_SERVER['HTTP_HOST'].'/v3/recruitment/dashboard';
                } else {
                    $redirectionurl = 'http://localhost/hrapponline/v3/recruitment/dashboard';
                }
            }
            // If the organization subscribe whole HRMS plan then they will be redirected to main dashboard
            else {
                if (Zend_Registry::get('Production')) {
                    $redirectionurl = $protocol.$_SERVER['HTTP_HOST'].'/in/';
                } else {
                    $redirectionurl = 'http://localhost/hrapponline/in/';
                }
            }
            
            $front = Zend_Controller_Front::getInstance();
            $response = new Zend_Controller_Response_Http();
            $response->setRedirect($redirectionurl);
            $front->setResponse($response);
        } else {
			$this->_redirect('auth');
		}
    }
    
    public function anniversaryAction()
    {
        // action body
    }
    
    /**
     * Get home panel data to show in mobile home page
     */
    public function mobileHomeAction()
    {
        $this->_helper->layout->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('mobile-home', 'json')->initContext();
            
			$this->_dbAccessRights->clearUserSession($this->_logEmpId);
			
            $result = $this->_dbAlerts->showHomePanelData($this->_logEmpId);
            
            $this->view->result = Zend_Json::encode($result);
        }
        else
        {
            $this->_redirect('auth');
        }
    }
    
    /**
     *  mobileSwitchViewAction used to switch view between mobile to desktop &
     *  desktop to mobile only in Mobile device.
    */
    public function mobileSwitchViewAction()
    {
        $this->_helper->layout->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('mobile-switch-view', 'json')->initContext();
            
            $switchDevice = $this->_getParam('Switch_Device', null);
            $switchDevice = filter_var($switchDevice, FILTER_SANITIZE_STRIPPED);
            
            if ($this->_isDevice != 'Desktop')
            {
                $session = Zend_Session::getId();
                
                $switchView = new Zend_Session_Namespace('Switch_View_'.$session);
                
                $switchView->Switch_View = $switchDevice;
                
                $result = $this->_hrappMobile->checkDevice();
            }
            
            $this->_redirect('');
        }
        else
        {
            $this->_redirect('auth');
        }
    }
    
    /**
     *  checkLockFormAction used to check lock for forms
    */
    public function checkLockFormAction()
    {
        $this->_helper->layout->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('check-lock-form', 'json')->initContext();
            
            if (!empty($this->_logEmpId))
            {
                $formName = $this->_getParam('formName', null);
                $formName = filter_var($formName, FILTER_SANITIZE_STRIPPED);
                
                $uniqueId = $this->_getParam('uniqueId', null);
                
                if($formName == 'Contact Details' || $formName == 'Organization Details' || $formName == 'Mail Client Configuration'){
                    $uniqueId = filter_var($uniqueId, FILTER_SANITIZE_STRIPPED);
                }
                else{
                    $uniqueId = filter_var($uniqueId, FILTER_SANITIZE_NUMBER_INT);
                }
                
                if (!empty($formName) && !empty($uniqueId))
                {
                    $recordLimit = $this->_dbAccessRights->checkOpenedRecordLimit($this->_logEmpId);
                   
                    if ($recordLimit[0])
                    {
                        $formDetails = $this->_dbAccessRights->formDetails ($formName);

						$isFormLock = $this->_dbAccessRights->checkLockFlag($uniqueId, $formDetails['tableName'], $formDetails['fieldName']);
                        
                        if ($isFormLock == 0)
                        {
                            $setFormLock = $this->_dbAccessRights->setLockFlag($this->_logEmpId, $uniqueId, $formDetails['tableName'], $formDetails['fieldName']);
                            
                            //if ($setFormLock)
                            //{
                                $this->view->access = Zend_Json::encode(array('success'=>true, 'msg'=>"Success", 'width'=>'200', 'type'=>'success'));
                            //}
                            //else
                            //{
                            //    $this->view->access = Zend_Json::encode(array('success'=>false, 'msg'=>"Unable to setLock", 'width'=>'300', 'type'=>'warning'));
                            //}
                        }
                        else
                        {
                            /** Validate the table name exist in the user session throw lock table for the lock flag-employee id. If exist, return the record is in edit.
                             * If does not exist, reset the lock flag to 0 for the unique id in the table. */
                            $sessionLockValidateAndResetResponse = $this->_dbAccessRights->validateSessionLockExistAndClearLock($isFormLock,$formDetails['tableName'],$formDetails['fieldName'],$uniqueId);
                            /** If the reponse is returned */
                            if($sessionLockValidateAndResetResponse){
                                /** If the table name exist in the user session throw lock table for the lock flag-employee id */
                                if($sessionLockValidateAndResetResponse === 'edited'){
                                    /** If the login employee id and the lock flag - employee id is same. */
                                    if ($isFormLock == $this->_logEmpId){
                                        $this->view->access = Zend_Json::encode(array('success'=>true, 'msg'=>"Success", 'width'=>'200', 'type'=>'success'));
                                    }else{
                                        $editEmpName = $this->_dbPersonal->employeeName($isFormLock);
                                        $this->view->access = Zend_Json::encode(array('success'=>false, 'msg'=>$editEmpName['Employee_Name'] . ' is updating this record. Please Wait...', 'width'=>'300', 'type'=>'warning'));
                                    }
                                }
                                else{
                                    /** If the table name does not exist in the user session throw lock table then set the lock flag for the record. */
                                    $setFormLock = $this->_dbAccessRights->setLockFlag($this->_logEmpId, $uniqueId, $formDetails['tableName'], $formDetails['fieldName']);
                                    $this->view->access = Zend_Json::encode(array('success'=>true, 'msg'=>"Success", 'width'=>'200', 'type'=>'success'));
                                }
                            }else{
                                $this->view->access = Zend_Json::encode(array('success'=>false, 'msg'=>'There seem to be some technical difficulties. Please contact the system administrator', 'width'=>'300', 'type'=>'warning'));
                            }
                        }
                    }
                    else
                    {
                        $this->view->access = Zend_Json::encode(array('success'=>false, 'msg'=>"You don't have rights to edit more than $recordLimit[1] records.", 'width'=>'300', 'type'=>'warning'));
                    }
                }
                else
                {
                    $this->view->access = Zend_Json::encode(array('success'=>false, 'msg'=>"Invalid data", 'width'=>'300', 'type'=>'warning'));
                }
            }
            else
            {
                $this->view->access = Zend_Json::encode(array('success'=>false, 'msg'=>"Expired", 'width'=>'300', 'type'=>'warning'));
            }
        }
        else
        {
            $this->_redirect('auth');
        }
    }
    
    /**
     *  checkUserLockFormAction() used to check user have lock flag for that form.
    */
    public function checkUserLockFormAction()
    {
        $this->_helper->layout->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('check-user-lock-form', 'json')->initContext();
            
            if (!empty($this->_logEmpId))
            {
                $formName = $this->_getParam('formName', null);
                $formName = filter_var($formName, FILTER_SANITIZE_STRIPPED);
                
                if (!empty($formName))
                {
                    $formDetails = $this->_dbAccessRights->formDetails ($formName);
                    
                    $isFormLock = $this->_dbAccessRights->checkUserLockFlag($this->_logEmpId, $formDetails['tableName'], $formDetails['fieldName']);
                    
                    $this->view->access = Zend_Json::encode(array('success'=>true, 'formLock'=>$isFormLock));
                }
                else
                {
                    $this->view->access = Zend_Json::encode(array('success'=>false, 'msg'=>'Invalid data', 'type'=>'warning', 'width'=>'250'));
                }
            }
            else
            {
                $this->view->access = Zend_Json::encode(array('success'=>false, 'msg'=>'Expired'));
            }
        }
        else
        {
            $this->_redirect('auth');
        }
    }
    
    /**
     *  clearSessionAction() - used to clear user session after form is submitted
    */
    public function clearSessionAction()
    {
        $this->_helper->layout->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('clear-session', 'json')->initContext();
            
            if (!empty($this->_logEmpId))
            {
                $formName = $this->_getParam('formName', null);
                $formName = filter_var($formName, FILTER_SANITIZE_STRIPPED);
                
                $uniqueId = $this->_getParam('uniqueId', null);
                
                if($formName == 'Contact Details' || $formName == 'Organization Details' || $formName == 'Mail Client Configuration' ){
                    $uniqueId = filter_var($uniqueId, FILTER_SANITIZE_STRIPPED);
                }
                else{
                    $uniqueId = filter_var($uniqueId, FILTER_SANITIZE_NUMBER_INT);
                }
                
                if (!empty($formName) && !empty($uniqueId))
                {
                    $formDetails = $this->_dbAccessRights->formDetails ($formName);
                    
                    /** Clear the lock flag from the table */
                    $this->_dbAccessRights->clearLockFlag($formDetails['tableName'], $formDetails['fieldName'], $uniqueId);
                    /** Update/Delete the table name for the login employee id in the user session throw lock table */
                    $this->_dbAccessRights->clearSubmitLock($this->_logEmpId, $formDetails['tableName']);
                    $this->view->access = Zend_Json::encode(array('success'=>true));
                }
                else
                {
                    $this->view->access = Zend_Json::encode(array('success'=>false, 'msg'=>'Invalid data', 'type'=>'warning', 'width'=>'250'));
                }
            }
            else
            {
                $this->view->access = Zend_Json::encode(array('success'=>false, 'msg'=>'Expired'));
            }
        }
        else
        {
            $this->_redirect('auth');
        }
    }
    
    /**
     *  Called during the page load to clear user session - lock flag
    */
    public function clearUserSessionAction()
    {
        $this->_helper->layout->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('clear-user-session', 'json')->initContext();
            
            if (!empty($this->_logEmpId))
            {
                /** Clear the user session from the respective table and from the user session throw lock table. */
                $this->_ehrTables->clearUserSessionLockFlag($this->_logEmpId);
                
                $this->view->access = Zend_Json::encode(array('success'=>true, 'msg'=>'success', 'type'=>'success', 'width'=>'250'));
            }
            else
            {
                $this->view->access = Zend_Json::encode(array('success'=>false, 'msg'=>'Expired'));
            }
        }
        else
        {
            $this->_redirect('auth');
		}
    }
	
	/**
     *  listCommentsAction() used to list comments.
    */
	public function listCommentsAction()
    {
        $this->_helper->layout->disableLayout();
		
		if(isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-comments', 'json')->initContext();
			
            $formName = $this->_getParam('formName', null);
			$formName = filter_var($formName, FILTER_SANITIZE_STRIPPED);
            
			$parentId = $this->_getParam('parentId', null);
			$parentId = filter_var($parentId, FILTER_SANITIZE_NUMBER_INT);
			
			$this->view->result = $this->_dbComment->searchComment($parentId, $formName);
        }
        else
        {
            $this->_redirect('/');
        }
    }
	
	/**
	 *	Change password used to change user password
	*/
	public function changePasswordAction()
    {
        $this->_helper->layout->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('change-password', 'json')->initContext();
			
            if ( $this->getRequest()->isPost() )
			{
				$formData = $this->getRequest()->getPost();
				
				$employeeId = $this->_validation->intValidation($formData['employeeId']);
				
				$currentPassword          = $this->_validation->pwdValidation($formData['currentPassword']);
				$currentPassword['valid'] = $this->_validation->lengthValidation($currentPassword, 6, 30, true);
				
				$newPassword          = $this->_validation->pwdValidation($formData['newPassword']);
				$newPassword['valid'] = $this->_validation->lengthValidation($newPassword, 6, 30, true);
				
				$confirmPassword          = $this->_validation->pwdValidation($formData['confirmPassword']);
				$confirmPassword['valid'] = $this->_validation->lengthValidation($confirmPassword, 6, 30, true);
				
				if (!empty($employeeId['value']) && $employeeId['valid'] && !empty($currentPassword['value']) && $currentPassword['valid'] &&
					!empty($newPassword['value']) && $newPassword['valid'] && !empty($confirmPassword['value']) && $confirmPassword['valid'])
				{
					if ($currentPassword['value'] != $newPassword['value'])
					{
						if ($newPassword['value'] == $confirmPassword['value'])
						{
							$dbEmpUser = new Auth_Model_DbTable_EmpUser ();
							
							$password = array('Hrapp_Password' => new Zend_Db_Expr('CONCAT("'. md5 ($newPassword['value']) .'",Random_Salt)'));
							
							$this->view->result = $dbEmpUser->updateUserPassword ($password, $employeeId['value']);
						}
						else
						{
							$this->view->result = array('success' => false, 'msg' => 'Confirm password doesn\'t match with new password', 'type' => 'info');
						}
					}
					else
					{
						$this->view->result = array('success' => false, 'msg' => 'You can\'t use the password that have been used earlier', 'type' => 'info');
					}
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
				}
			}
		}
        else
        {
            $this->_redirect('/');
        }
    }
	
	public function getOrgCodeAction()
    {
        $this->_helper->layout->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('get-org-code', 'json')->initContext();
                
            $this->view->result = $this->_ehrTables->getOrgCode();
        }
        else
        {
            $this->_redirect('auth');
		}
    }

    //retrive the registered bank account status
    public function retrieveBankAccountStatusAction()
    {
        $this->_helper->layout->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            if ($this->_orgAccountAccessRights['Employee']['Optional_Choice'] == 1)
            {
                $ajaxContext = $this->_helper->getHelper('AjaxContext');
                $ajaxContext->addActionContext('retrieve-bank-account-status', 'json')->initContext();

                $this->view->result = $this->_dbEftConfiguration->checkBankRegistrationStatus(1);  
            }
            else
            {
                $this->view->result = array('isRegistrationRetrievedStatus'=>0,'msg'=>'Sorry, Access Denied...');
            }
        }
        else
        {
            $this->_redirect('auth');
        }
    }

    public function __destruct()
    {
        
    }
}