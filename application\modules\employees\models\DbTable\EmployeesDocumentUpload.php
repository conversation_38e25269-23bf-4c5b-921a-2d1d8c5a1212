<?php

class Employees_Model_DbTable_EmployeesDocumentUpload extends Zend_Db_Table_Abstract
{
    protected $_db          = null;
    protected $_ehrTables   = null;
    protected $_orgDF       = null;
	protected $_dbCommonFun = null;
	protected $_orgDetails    = null;
	
    public function init()
    {
        $this->_ehrTables   = new Application_Model_DbTable_Ehr();
        $this->_dbCommonFun = new Application_Model_DbTable_CommonFunction ();
		$this->_db          = Zend_Registry::get('subHrapp');
		$this->_orgDF       = $this->_ehrTables->orgDateformat();
		$this->_orgDetails  = Zend_Registry::get('orgDetails');
	}
	
    
    public function getCategory()
    {
        $qryCategory = $this->_db->select()->from($this->_ehrTables->documentCategory,array('Category_Id' ,'Category_Fields'))
                                    ->order('Category_Fields ASC');
                                    
        $rowCategory=$this->_db->fetchPairs($qryCategory);
        
        return $rowCategory;
	}

	public function getDocumentTypes($categoryId)
	{
		$qryDocType = $this->_db->select()->from($this->_ehrTables->documentType,array('Document_Type_Id','Document_Type'))
								->order('Document_Type ASC');
								
		if($categoryId > 0)
		{
			$conditions = $this->_db->quoteInto('Category_Id = ?', $categoryId);
			$qryDocType->where($conditions);
		}
        
        $rowDocType=$this->_db->fetchPairs($qryDocType);
        
        return $rowDocType;
	}
	
	public function getDocumentSubTypes($docTypeId)
	{
		$qryDocType = $this->_db->select()->from($this->_ehrTables->documentSubType,array('Document_Sub_Type_Id','Document_Sub_Type'))
                                ->order('Document_Sub_Type ASC');
                                    						
		if($docTypeId > 0)
		{
			$docTypeConditions = $this->_db->quoteInto('Document_Type_Id = ?', $docTypeId);
			$qryDocType->where($docTypeConditions);
		}

        $rowDocType=$this->_db->fetchPairs($qryDocType);
        
        return $rowDocType;
	}
	 
	public function listEmployeesDocumentUpload ($page, $rows, $sortField, $sortOrder, $searchAll, $userDetails, $formName)
    {	
		/**
		 *	Sorting columns based on display column order in grid
		*/
		switch ($sortField)
		{
			case 0: $sortField = 'EJ.User_Defined_EmpId'; break;
			case 1: $sortField = 'emp.Emp_First_Name'; break;	
			default :  $sortField = 'EJ.User_Defined_EmpId'; break;
		}
		
        $qryEmployeesDocumentUpload = $this->_db->select()
								->from(array('edu'=>$this->_ehrTables->empDocumentCategory),
									   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS edu.Document_Id as count'),
											 'edu.Document_Id','edu.Employee_Id','edu.Document_Name','Employee_Id',
											 new Zend_Db_Expr("DATE_FORMAT(edu.Added_On,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
											 'DT_RowClass' => new Zend_Db_Expr('"emp_document_category"'),
											 'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', edu.Document_Id)"), 'edu.Added_By',
											  'Log_Id'=> new Zend_Db_Expr("'".$userDetails['logEmpId']."'"),
											  /*'Update' => new Zend_Db_Expr("'".$userDetails['Update']."'"),
											  'Delete' => new Zend_Db_Expr("'".$userDetails['Delete']."'")*/))
								
								->joinLeft(array('AB'=>$this->_ehrTables->empPersonal),'AB.Employee_Id=edu.Added_By',
										   array('Added_By_Name'=>new Zend_Db_Expr("CONCAT(AB.Emp_First_Name, ' ', AB.Emp_Last_Name)")))
								
								->joinLeft(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id = edu.Lock_Flag',
										   array('Lock_EmpName'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name, ' ', P.Emp_Last_Name)")))
								
								->joinLeft(array('emp'=>$this->_ehrTables->empPersonal),'edu.Employee_Id = emp.Employee_Id',
										   array('Employee_Name'=>new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name)")))
                                
                                ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'emp.Employee_Id=EJ.Employee_Id',
                                            array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN emp.Employee_Id ELSE EJ.User_Defined_EmpId END')))


								->joinLeft(array('docST'=>$this->_ehrTables->documentSubType),'edu.Document_Sub_Type_Id = docST.Document_Sub_Type_Id',
										array('docST.Document_Sub_Type'))
	
								->joinLeft(array('docT'=>$this->_ehrTables->documentType),'docST.Document_Type_Id = docT.Document_Type_Id',
										array('docT.Document_Type', 'docT.Document_Type_Id'))
								
								->joinLeft(array('doc'=>$this->_ehrTables->documentCategory),'docT.Category_Id = doc.Category_Id',
										array('doc.Category_Fields', 'doc.Category_Id'))
								
								->group('edu.Employee_Id')
								
								->order("$sortField $sortOrder")
		
								->limit($rows, $page);
		/**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			$conditions = $this->_db->quoteInto(new Zend_Db_Expr('Concat(emp.Emp_First_Name," ",emp.Emp_Last_Name) Like ?'),"%$searchAll%");
             $conditions .= $this->_db->quoteInto('or EJ.User_Defined_EmpId Like ?', "%$searchAll%");
			
			$qryEmployeesDocumentUpload->where($conditions);
		}
			
		if (empty($userDetails['Admin']))
        {
			/*
            To get employee access for manager. If it returns 1, then they can't access employees bank account details.
            */
			$restrictEmpAccessForManager = $this->_orgDetails['Restrict_Emp_Access_For_Manager'];

			if ( $userDetails['Is_Manager'] == 1 && $restrictEmpAccessForManager != 1)
			{
				$qryEmployeeId = $this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
								->where('Manager_Id = ?', $userDetails['logEmpId']);
				$getEmployeeId = $this->_db->fetchCol($qryEmployeeId);
				
				$qryEmployeesDocumentUpload->where('edu.Employee_Id = :EmpId')->bind(array('EmpId'=>$userDetails['logEmpId']));
                
                if(!empty($getEmployeeId))
                {
                   $qryEmployeesDocumentUpload->orwhere('edu.Employee_Id IN (?)', $getEmployeeId);
                }
            }
            else
			{
                $qryEmployeesDocumentUpload->where('edu.Employee_Id = ?', $userDetails['logEmpId']);
            }
        }
		
		if(!empty($userDetails['Admin']))
		{
			$qryEmployeesDocumentUpload = $this->_dbCommonFun->formServiceProviderQuery($qryEmployeesDocumentUpload,'EJ.Service_Provider_Id',$userDetails['logEmpId']);
		}

		$qryEmployeesDocumentUpload = $this->_dbCommonFun->getDivisionDetails($qryEmployeesDocumentUpload,'EJ.Department_Id');

		/**
		 * SQL queries
		 * Get data to displa
		*/
		$employeesDocumentUpload = $this->_db->fetchAll($qryEmployeesDocumentUpload);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		$iTotalQry = $this->_db->select()->from($this->_ehrTables->empDocumentCategory, new Zend_Db_Expr('COUNT(Employee_Id)'))
									   ->group('Employee_Id');
		
		if (empty($userDetails['Admin']))
        {
			if ( $userDetails['Is_Manager'] == 1)
			{
				$qryEmployeeId = $this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
								->where('Manager_Id = ?', $userDetails['logEmpId']);
				$getEmployeeId = $this->_db->fetchCol($qryEmployeeId);
				
				$iTotalQry->where('Employee_Id = :EmpId')->bind(array('EmpId'=>$userDetails['logEmpId']));
                
                if(!empty($getEmployeeId))
                {
                   $iTotalQry->orwhere('Employee_Id IN (?)', $getEmployeeId);
                }
            }
            else
			{
                $iTotalQry->where('Employee_Id = ?', $userDetails['logEmpId']);
            }
        }
		
		$iTotal = $this->_db->fetchCol($iTotalQry);
		
		$iTotal = empty($iTotal) ? 0 : count($iTotal);
		
		/**
		 * Output array with Json encode
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $employeesDocumentUpload);
    }
	
	/** subgrid list **/
	public function searchEmployeeData($page, $rows, $sortField, $sortOrder,$employeeId)
	{
		$qryRequest = $this->_db->select()
							->from(array('edu'=>$this->_ehrTables->empDocumentCategory),
									array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS edu.Document_Id as Count'),
								   'Document_Id','Document_Sub_Type_Id','Document_Name')) 

							->joinLeft(array('dst'=>$this->_ehrTables->documentSubType),'edu.Document_Sub_Type_Id = dst.Document_Sub_Type_Id',
									array('dst.Document_Sub_Type'))

							->joinLeft(array('dt'=>$this->_ehrTables->documentType),'dst.Document_Type_Id = dt.Document_Type_Id',
									array('dt.Document_Type', 'dt.Document_Type_Id'))
							
							->joinLeft(array('d'=>$this->_ehrTables->documentCategory),'dt.Category_Id = d.Category_Id',
									array('d.Category_Fields', 'd.Category_Id'))
				
							->where('Employee_Id = ?',$employeeId);

        /**
		 * SQL queries
		 * Get data to display
		*/
		$subGridDataQryResult = $this->_db->fetchAll($qryRequest);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		/* Total data set length */
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empDocumentCategory,
																  new Zend_Db_Expr('COUNT(Document_Id)')));
	
		if(!empty($iTotal)){
			for($l=0;$l<count($subGridDataQryResult);$l++)
			{
				/** get uploaded files **/				
				$employeesDocumentUploadFiles =  $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->empDocuments,array('File_Name','File_Size'))
					->where('Document_Id = ?', $subGridDataQryResult[$l]['Document_Id']));
					
				$subGridDataQryResult[$l]['Employees_Document_Upload_File_Path'] = $employeesDocumentUploadFiles;
																  
			}
		}
		
		/**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $subGridDataQryResult);
			
	}    
    
	/** add/edit employee documents**/
    public function updateEmployeesDocumentUpload ($employeesDocumentUploadDetails, $employeesDocumentUploadFilesArr, $documentId, $logEmpId, $formName)
    {
		/** Check that document name already exists for the employee **/
		 $qryExists = $this->_db->select()->from($this->_ehrTables->empDocumentCategory, new Zend_Db_Expr('count(Document_Id)'))			
			->where('Employee_Id = ?', $employeesDocumentUploadDetails['Employee_Id'])
            ->where('Document_Name = ?', $employeesDocumentUploadDetails['Document_Name']);
			
		if(!empty($documentId))
        {				
            $qryExists->where('Document_Id != ?',$documentId);
        }
			
        $rowDocument = $this->_db->fetchOne($qryExists);
		if(empty($rowDocument))
		{
            if($documentId >0 )
            {
               $action = 'Edit';
         
               $updated = $this->_db->update($this->_ehrTables->empDocumentCategory,$employeesDocumentUploadDetails,'Document_Id = ' .$documentId);
               
			   $updated = 1;
			   
               $employeesDocumentUploadDetails['Document_Id'] = $documentId;
            }
            else
            {
				
                $action = 'Add';
                $employeesDocumentUploadDetails['Added_By']  = $logEmpId;
                $employeesDocumentUploadDetails['Added_On']  = date('Y-m-d H:i:s');
         
                $updated = $this->_db->insert($this->_ehrTables->empDocumentCategory,$employeesDocumentUploadDetails);
         
                if ($updated)
                    $employeesDocumentUploadDetails['Document_Id'] = $this->_db->lastInsertId();
            }
            
			
			if ($updated){
				if(!empty($employeesDocumentUploadFilesArr))
				{
					foreach($employeesDocumentUploadFilesArr as $key=>$row)
					{
						$employeesDocumentUploadFilesArr[$key]['Document_Id'] = $employeesDocumentUploadDetails['Document_Id'];	
					}
					
					$fUpdated =$this->_ehrTables->insertMultiple($this->_ehrTables->empDocuments, $employeesDocumentUploadFilesArr);				
				}
			}
            
            $trackSysLog = $this->_dbCommonFun->updateResult (array('updated'        => $updated,
                                                                    'action'         => $action,
                                                                    'trackingColumn' => $employeesDocumentUploadDetails['Document_Id'],
                                                                    'formName'       => $formName,
                                                                    'sessionId'      => $logEmpId,
                                                                    'tableName'      => $this->_ehrTables->empDocumentCategory));
            
            
            $trackSysLog['Document_Id']    = $employeesDocumentUploadDetails['Document_Id'];
            
            return $trackSysLog;                  
	    }
        else
        {
            return array('success' => false, 'msg'=>'Same kind of record already exist!', 'type'=>'warning');
        }  
    }
    
	/** delete sub grid document **/
    public function deleteEmployeesDocumentUpload ($documentId, $docExists, $deleteFiles, $logEmpId, $formName)
    {
		$empIdExists = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empDocumentCategory, 'Employee_Id')
										->where('Document_Id = ?', $documentId));
		
		/** check whether emp exists for the document id **/
		if( $empIdExists )
		{
			$empDocLockFlag = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empDocumentCategory, 'Lock_Flag')
										->where('Document_Id = ?', $documentId));
			
			$deleted = 0;
			
			if($empDocLockFlag == 0)
			{
				/** If any of the documents exists in table **/
				if ($docExists)
				{
					/** If files are deleted from s3 bucket it has to deleted from table **/
					if(count($deleteFiles) > 0)
					{
						/** delete employees documents **/
						foreach($deleteFiles as $key=>$file)
						{
							$where = '';				
							$where = array('Document_Id = ?' => $documentId,'File_Name = ?' => $file['Name']);
							
							$this->_db->delete($this->_ehrTables->empDocuments, $where);	
						}
					}
					
					$documentExists = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empDocuments, new Zend_Db_Expr('count(Document_Id)'))
												->where('Document_Id = ?', $documentId));
					
					/** If all the documents are deleted for the document Id from s3 bucket**/
					if(empty($documentExists))
					{
						$deleted = $this->_db->delete($this->_ehrTables->empDocumentCategory,'Document_Id ='.$documentId);
					}
				}
				else
				{
					/** delete the document rec **/
					$deleted = $this->_db->delete($this->_ehrTables->empDocumentCategory,'Document_Id ='.$documentId);
				}
			}
			
			/** get doc count for the employee, to close the modal form. If no docs exists, then modal will be closed **/
			$empDocExists = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empDocumentCategory, new Zend_Db_Expr('count(Document_Id)'))
												->where('Employee_Id = ?', $empIdExists));
			
			$result = $this->_dbCommonFun->deleteRecord (array('deleted'         => $deleted,
																'tableName'      => $this->_ehrTables->empDocumentCategory,
																'lockFlag'       => $empDocLockFlag,
																'formName'       => $formName,
																'trackingColumn' => $documentId,
																'sessionId'      => $logEmpId));
				
			$result['empDocExists'] = $empDocExists;
			
			return $result;
		}
		else
		{	
			return array('success'=> false, 'msg' => $formName.' deleted already', 'type' => 'warning');
		}
    }
	
	/** delete file in edit form **/
	public function deleteEmployeesDocumentUploadFiles($documentId,$employeesDocumentUploadFileName)
	{
		$deleted = 0;
		
		if(!empty($documentId))
		{
			$where = $this->_db->quoteInto('Document_Id = ? AND ', $documentId).
								 $this->_db->quoteInto('File_Name = ?', $employeesDocumentUploadFileName);
			
			$deleted = $this->_db->delete($this->_ehrTables->empDocuments, $where);
		}
		
		if($deleted)
		{
			return array('success'=> true, 'msg' => 'Document deleted successfully', 'comboPair' => '', 'type' => 'info');
		}
		else{
			return array('success'=> false, 'msg' => 'Unable to delete Document', 'type' => 'warning');	
		}
	}	
	
	public function checkEmpUploadFilesExists($documentId){
		return $this->_db->fetchone($this->_db->select()->from($this->_ehrTables->empDocuments,'COUNT(Document_Id)')
														->where('Document_Id   = ?', $documentId));
	}

	/**
	 * List Document Sub Type Details
	 */
	public function listEmployeeDocumentSubType($page, $rows, $sortField, $sortOrder, $searchAll, $logEmpId, $formName)
	{
		/**
		 *	Sorting columns based on display column order in grid
		*/
		switch ($sortField)
		{
			case 1: $sortField = 'dc.Category_Fields'; break;
            case 2: $sortField = 'dt.Document_Type'; break;			
			case 3: $sortField = 'dst.Document_Sub_Type'; break;	
			default: $sortField = 'dt.Document_Type'; break;	
		}

		$qryDocumentSubType = $this->_db->select()
								->from(array('dst'=>$this->_ehrTables->documentSubType),
									   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS dst.Document_Sub_Type_Id as count'),
											 'dc.Category_Id','dc.Category_Fields','dt.Document_Type_Id','dt.Document_Type','dst.Document_Sub_Type_Id','dst.Document_Sub_Type','Updated_By',
											 new Zend_Db_Expr("DATE_FORMAT(dst.Added_On,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
											 new Zend_Db_Expr("DATE_FORMAT(dst.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On"),
											 'DT_RowClass' => new Zend_Db_Expr('"document_sub_type"'),
											 'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', dst.Document_Sub_Type_Id)"), 'dst.Added_By',
											  'Log_Id'=> new Zend_Db_Expr("'".$logEmpId."'")))
								
								->joinLeft(array('AB'=>$this->_ehrTables->empPersonal),'AB.Employee_Id=dst.Added_By',
											array('Added_By_Name'=>new Zend_Db_Expr("CONCAT(AB.Emp_First_Name, ' ', AB.Emp_Last_Name)")))

								->joinLeft(array('EP'=>$this->_ehrTables->empPersonal),'EP.Employee_Id=dst.Updated_By',
											array('Updated_By_Name'=>new Zend_Db_Expr("CONCAT(EP.Emp_First_Name, ' ', EP.Emp_Last_Name)")))

								->joinLeft(array('dt'=>$this->_ehrTables->documentType),'dt.Document_Type_Id=dst.Document_Type_Id',
										   array('dt.Document_Type'))
								
								->joinLeft(array('dc'=>$this->_ehrTables->documentCategory),'dc.Category_Id = dt.Category_Id',
										   array('dc.Category_Fields'))
								
								->order("$sortField $sortOrder")
		
								->limit($rows, $page);
		
		/**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			$conditions = $this->_db->quoteInto('dc.Category_Fields Like ?',"%$searchAll%");
             $conditions .= $this->_db->quoteInto('or dt.Document_Type Like ?', "%$searchAll%");
             $conditions .= $this->_db->quoteInto('or dst.Document_Sub_Type Like ?', "%$searchAll%");
			
			$qryDocumentSubType->where($conditions);
		}

		/**
		 * SQL queries
		 * Get data to display
		*/
		$employeesDocumentSubType = $this->_db->fetchAll($qryDocumentSubType);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		$iTotalQry = $this->_db->select()->from($this->_ehrTables->documentSubType, new Zend_Db_Expr('COUNT(Document_Sub_Type_Id)'))
									   ->group('Document_Sub_Type_Id');
		
		$iTotal = $this->_db->fetchCol($iTotalQry);
		
		$iTotal = empty($iTotal) ? 0 : count($iTotal);
		
		/**
		 * Output array with Json encode
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $employeesDocumentSubType);
	}

	/**
	 * Add or Update Document Sub Type
	 */
	public function updateDocumentSubType($documentSubTypeDetails, $logEmpId, $subFormName)
	{
		/** Check that document sub type name already exists for the employee **/
		$qryExists = $this->_db->select()->from($this->_ehrTables->documentSubType, new Zend_Db_Expr('count(Document_Sub_Type_Id)'))			
										->where('Document_Type_Id = ?', $documentSubTypeDetails['Document_Type_Id'])
										->where('Document_Sub_Type = ?', $documentSubTypeDetails['Document_Sub_Type']);

		
		if(!empty($documentSubTypeDetails['Document_Sub_Type_Id']))
		{				
			$qryExists->where('Document_Sub_Type_Id != ?', $documentSubTypeDetails['Document_Sub_Type_Id']);
		}
			
		$rowDocument = $this->_db->fetchOne($qryExists);

		if(empty($rowDocument))
		{
			if($documentSubTypeDetails['Document_Sub_Type_Id'] > 0 )
            {
				$action = 'Edit';
                $documentSubTypeDetails['Updated_By']  = $logEmpId;
				$documentSubTypeDetails['Updated_On']  = date('Y-m-d H:i:s');
				
				$updated = $this->_db->update($this->_ehrTables->documentSubType, $documentSubTypeDetails,'Document_Sub_Type_Id = ' .$documentSubTypeDetails['Document_Sub_Type_Id']);
				
				$updated = 1;
            }
            else
            {
				$action = 'Add';
                $documentSubTypeDetails['Added_By']  = $logEmpId;
                $documentSubTypeDetails['Added_On']  = date('Y-m-d H:i:s');
				
                $updated = $this->_db->insert($this->_ehrTables->documentSubType,$documentSubTypeDetails);
         
                if ($updated)
                    $documentSubTypeDetails['Document_Sub_Type_Id'] = $this->_db->lastInsertId();
			}

			$trackSysLog = $this->_dbCommonFun->updateResult (array('updated'        => $updated,
                                                                    'action'         => $action,
                                                                    'trackingColumn' => $documentSubTypeDetails['Document_Sub_Type_Id'],
                                                                    'formName'       => $subFormName,
                                                                    'sessionId'      => $logEmpId,
                                                                    'tableName'      => $this->_ehrTables->documentSubType));
             
            return $trackSysLog;
		}
        else
        {
            return array('success' => false, 'msg'=>'Same kind of record already exist!', 'type'=>'warning');
        }
	}

	/**
	 * Delete Document Sub Type in form Document sub type form
	 */
	public function deleteDocumentSubType($documentSubTypeId, $logEmpId, $subFormName)
	{
		if($documentSubTypeId > 0)
		{
			$docSubTypeLockFlag = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->documentSubType, 'Lock_Flag')
										->where('Document_Sub_Type_Id = ?', $documentSubTypeId));
			
			$deleted = 0;
			
			if($docSubTypeLockFlag == 0)
			{
				/** Check that document sub type name already exists in employee document upload **/
				$docSubTypeExists = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empDocumentCategory, new Zend_Db_Expr('count(Document_Sub_Type_Id)'))
											->where('Document_Sub_Type_Id = ?', $documentSubTypeId));

				if(empty($docSubTypeExists))
				{
					
					$deleted = $this->_db->delete($this->_ehrTables->documentSubType,'Document_Sub_Type_Id ='.$documentSubTypeId);
				}
				
				if($docSubTypeExists > 0)
				{
					return array('success' => false, 'msg'=>'This document sub type is in use by an employee for his/her document. Please alter them to delete this document sub type', 'type'=>'warning');
				}
			}

			$result = $this->_dbCommonFun->deleteRecord (array( 'deleted'        => $deleted,
																'tableName'      => $this->_ehrTables->documentSubType,
																'lockFlag'       => $docSubTypeLockFlag,
																'formName'       => $subFormName,
																'trackingColumn' => $documentSubTypeId,
																'sessionId'      => $logEmpId));

			return $result;
		}
		else
		{
			return array('success' => false, 'msg'=>'Unable to Delete Document Sub Type', 'type'=>'warning');
		}
	}

	public function __destruct()
    {
        
    }
}
