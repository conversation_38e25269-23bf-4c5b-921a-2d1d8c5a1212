<?php
	$this->headTitle($this->formName);
	$formName = $this->formName;
	$billingAccess = $this->billingAccess;
	$contactInfo = $this->contactInfo;
	$managerInfo = $this->managersInfo;
	
	$dateformat = $this->dateformat;
	
	if(!empty($dateformat))
	{
		$dformat = $dateformat['bs'];		
	}
	else
	{
		$dformat = 'dd/mm/yyyy';
	}
	
	if ($billingAccess['View'] >= 1) {
?>

<div class="row">
		<div class="hidden col-md-12" id="printPanel" style="padding: 20px 0px 10px 10px">			
			<!--<div class="panel-content pagination2 table-responsive">			-->
				<div class="col-md-12" style="margin-top: 20px">
					<button type="submit" class="btn btn-secondary btn-embossed ladda-button" aria-hidden="true" id="PrintScreen" >
						<i class=""></i> Print
					</button>
					<button type="submit" class="btn btn-secondary btn-embossed ladda-button" aria-hidden="true" id="exitPrint" >
						<i class=""></i> Back
					</button>
				</div>
				<div class="preview_header" name="printable"></div>
				<div class="printable_portion" name="printable"></div>
			<!--</div>-->		
	</div>
</div>

<!-- Billing Grid Panel -->
<div class="col-md-12 portlets">
	<div class="panel gridPanelBilling">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <?php echo $this->formName; ?></h3>
		</div>
		<div class="panel-content">
				<div class="m-b-10">
					
					<button type="button" class="btn btn-secondary-default btn-embossed btn-off disabled toolbar-icons paymentBilling" title="Payment" >
						<i class="mdi-action-credit-card"></i><span class="hidden-xs hidden-sm"> Pay Now</span>
					</button>
					
					<button type="button" class="btn btn-secondary-default btn-embossed btn-off disabled toolbar-icons historyBilling" title="History" >
						<i class="mdi-action-restore"></i><span class="hidden-xs hidden-sm"> History</span>
					</button>
					
					<a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons filterBilling">
						<i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm"> Filter</span>
					</a>
					
					<!--Export Buttons-->
					<button type="button" class="btn btn-white btn-embossed print-toolbar-icons pull-right" title="Export as CSV" id="csvBilling">
						<i class="fa fa-file-excel-o"></i>
					</button>
					<button type="button" class="btn btn-white btn-embossed print-toolbar-icons pull-right" title="Export as PDF" id="pdfBilling">
						<i class="fa fa-file-pdf-o"></i>
					</button>
					<button type="button" class="btn btn-white btn-embossed print-toolbar-icons pull-right" title="Print" id="printBilling">
						<i class="fa fa-print fa-2x"></i>
					</button>
					<button type="button" class="btn btn-white btn-embossed print-toolbar-icons pull-right" title="Email" id="emailBilling">
						<i class="mdi-content-mail"></i>
					</button>
					
				</div>
			
			<table id="tableBilling" class="table table-hover table-dynamic table-striped tableBilling display select">
				<thead>
					<tr>
						<th></th>
						<th>Billing Date</th>
						<th>Active Employees</th>
						<th>Inactive Employees</th>
						<th>Total Amount</th>
						<th>Status</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
		</div>
	</div>
</div>

<!--Notes Panel-->
<?php if (!empty($this->ratePlan)) { ?>
<div class="col-md-12 portlets">
	<div class="panel gridPanelNotes">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-note"></i> <strong>Notes</strong></h3>
		</div>
		<div class="panel-content">
			<table class="table table-striped table-hover">
				<thead>
					<tr>
						<th>Rate for Active Employees</th>
						<th>Rate for Inactive Employees</th>
						<th class="hidden-xs hidden-sm">Discount (%)</th>
						<th class="hidden-xs hidden-sm">Plan Status</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td><?php echo $this->ratePlan['Active_Rate']; ?></td>
						<td><?php echo $this->ratePlan['Inactive_Rate']; ?></td>
						<td class="hidden-xs hidden-sm"><?php echo (empty($this->ratePlan['Discount']) ? '-' : $this->ratePlan['Discount'] ); ?></td>
						<td class="hidden-xs hidden-sm"><?php echo $this->ratePlan['Status']; ?></td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</div>
<?php } ?>

<!-- Your custom menu with dropdown-menu as default styling -->
<div id="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" class="paymentContextBilling"><i class="mdi-action-credit-card"></i> Payment</a></li>
		<li><a tabindex="-1" class="historyContextBilling"><i class="mdi-action-restore"></i> History</a></li>
	</ul>
</div>

<!--Filter Form-->
<div class="builder" id="filterPanelBilling">
	<!--<a class="filter-toggle"><i class="glyphicon glyphicon-filter"></i></a>-->
	<div id="closefilterBilling"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel filterReset"  style="width: 100%;" id="filterResetBilling" style="width: 100%;">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed"  style="width: 100%;" id="filterApplyBilling" style="width: 100%;">Apply</button>
			
			<div class="form-group">
				<label>Billing Date</label>
				<div class="input-daterange b-datepicker input-group" data-date-format="<?php echo $dformat; ?>" id="datepicker">
					<input type="text" class="input-sm form-control" name="start" id="filterBillingDateStart" placeholder="Beginning..."/>
					<span class="input-group-addon">to</span>
					<input type="text" class="input-sm form-control" name="end" id="filterBillingDateEnd" placeholder="Ending..."/>
				</div>
				<!--<div class="row">
					<div class="col-md-4 col-sm-4 col-xs-4" style="padding-right: 0px;">
						<select class="form-control">
							<option value="=">=</option>
							<option value=">">></option>
							<option value="<"><</option>
							<option value=">=">>=</option>
							<option value="<="><+</option>
							<option value="!=">!=</option>
						</select>
					</div>
					<div class="col-md-8 col-sm-8 col-xs-8" style="padding-left: 0px;">
						<div class="prepend-icon">
							<input type="text" name="datepicker" class="date-picker form-control" placeholder="Select a date...">
							<i class="icon-calendar"></i>
						</div>
					</div>
				</div>-->
			</div>
			
			<div class="form-group">
				<label>Total Amount for Active Employees</label>
				<div class="input-group">
					<input type="number" class="form-control" name="totalAmountActiveEmpStart" id="filterTotalAmountactiveEmpStart" min="0" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="form-control" name="totalAmountActiveEmpEnd" id="filterTotalAmountactiveEmpEnd" min="0" placeholder="End"/>
				</div>
                <!--<input type="number" class="form-control" id="filterTotalAmountactiveEmp" min=0>-->
				<!--<div id="slider" class="noUi-target noUi-ltr noUi-horizontal noUi-background">-->
					<!--<div class="noUi-base">-->
					<!--	<div class="noUi-origin noUi-connect" style="left: 20%;">-->
					<!--		<div class="noUi-handle noUi-handle-lower"></div>-->
					<!--	</div>-->
					<!--	<div class="noUi-origin noUi-background" style="left: 80%;">-->
					<!--		<div class="noUi-handle noUi-handle-upper"></div>-->
					<!--	</div>-->
					<!--</div>-->
				<!--</div>-->
				<!--<b>10</b> <input id="slider-snap" type="text" class="span2" value="" data-slider-min="10" data-slider-max="1000" data-slider-step="5" data-slider-value="[250,450]"/> <b> 1000</b>-->
				<!--<div class="input-group" id="filterTotalAmtActiveEmployee">
					<input type="number" class="input-sm form-control" name="startTotalAmtActiveEmp" placeholder="Beginning..."/>
					<span class="input-group-addon">to</span>
					<input type="number" class="input-sm form-control" name="endTotalAmtActiveEmp" placeholder="Ending..."/>
				</div>-->
				<!--<div style="width:300px" class="danger m-b-40">-->
				<!--	<input class="range-slider" type="text" data-postfix="km" data-type="double" data-from="0" data-to="450" data-max="1000" data-hideMinMax="true" data-hideFromTo="false" data-hasGrid="true"/>-->
				<!--</div>-->
			</div>
			
			<div class="form-group">
				<label>Total Amount for Inactive Employees</label>
				<div class="input-group">
					<input type="number" class="form-control" name="totalAmountInActiveEmpStart" id="filterTotalAmountInactiveEmpStart" min="0" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="form-control" name="totalAmountInActiveEmpEnd" id="filterTotalAmountInactiveEmpEnd" min="0" placeholder="End"/>
				</div>
                <!--<input type="number" class="form-control" id="filterTotalAmountInactiveEmp" min=0>-->
				<!--<div style="width:300px" class="danger m-b-30">
					<div class="slide-ios" data-slider-max="100" data-slider-value="70"></div>
				</div>-->
			</div>
			
			<div class="form-group">
				<label>Discount Amount</label>
				<div class="input-group">
					<input type="number" class="form-control" name="discountAmountStart" id="filterDiscountAmountStart" min="0" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="form-control" name="discountAmountEnd" id="filterDiscountAmountEnd" min="0" placeholder="End"/>
				</div>
                <!--<input type="number" class="form-control" id="filterDiscountAmount" min=0>-->
				<!--<input type="text" class="span2" value="" data-slider-min="-20" data-slider-max="20" data-slider-step="1" data-slider-value="-14" data-slider-orientation="vertical" data-slider-selection="after"data-slider-tooltip="hide">-->
				<!--<input type="text" class="form-control" id="filterDepartmentName" >-->
			</div>
			
			<div class="form-group">
				<label>Total Amount</label>
				<div class="input-group">
					<input type="number" class="form-control" name="totalAmountStart" id="filterTotalAmountStart" min="0" placeholder="Begin"/>
					<span class="input-group-addon">to</span>
					<input type="number" class="form-control" name="totalAmountEnd" id="filterTotalAmountEnd" min="0" placeholder="End"/>
				</div>
                <!--<input type="number" class="form-control" id="filterTotalAmount" min=0>-->
				<!--<div class="slider slider-horizontal" style="width: 414px;"><div class="slider-track"><div class="slider-selection" style="left: 16.1616%; width: 28.2828%;"></div><div class="slider-handle round" style="left: 16.1616%;"></div><div class="slider-handle round" style="left: 44.4444%;"></div></div><div class="tooltip top" style="top: -40px; left: 94.4545px;"><div class="tooltip-arrow"></div><div class="tooltip-inner">165 : 450</div></div><input type="text" class="span2" value="" data-slider-min="10" data-slider-max="1000" data-slider-step="5" data-slider-value="[250,450]" id="sl2"></div>-->
				<!--<input type="text" class="form-control" id="filterDepartmentName" >-->
			</div>
			
			<div class="form-group">
				<label>Billing Status</label>
				<!--<div class="slider slider-horizontal">
					<input type="text" class="span2" value="" data-slider-min="10" data-slider-max="1000" data-slider-step="5" data-slider-value="[250,450]" id="sl2">
				</div>-->
				<select class="form-control" data-search="true" id="filterBillingStatus" >
					<option value="">All</option>
					<option value="Paid">Paid</option>
					<option value="Unpaid">Unpaid</option>
				</select>
			</div>
		</div>
	</div>
</div>

<!-- Billing History Modal -->
<div class="modal fade" id="modalBillingHistory" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52"></i></button>
				<h4 class="modal-title"><strong>Billing</strong> history</h4>
			</div>
			<div class="modal-body">
				<table class="table dataTable table-striped table-dynamic table-hover tableBillingHistory">
					<thead>
						<tr>
							<th></th>
							<th>Order Id</th>
							<th>Tracking Id</th>
							<th>Bank Reference No</th>
							<th>Order Status</th>
							<th>Failure Message</th>
							<th>Payment Mode</th>
							<th>Card Name</th>
							<th>Amount</th>
						</tr>
					</thead>
					<tbody>
					
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>

<!--Email Form Modal-->
<div class="modal fade" id="modalEmailBilling" aria-hidden="true">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left modalEmailCloseBilling" style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace"></i>
				</button>
				
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<!--Billing Email Report Form-->
				<form role="form" class="form-horizontal form-validation" id="emailFormBilling" method="POST" action="">
					<div class="row">
						
						<!--Start From Name Field Set-->
						<div class="form-group">
							<label class="col-md-4 control-label">From Name</label>
							<label class="col-md-8 control-label" id="fromName"><?php echo $contactInfo['First_Name'].' '.$contactInfo['Last_Name']; ?></label>
						</div>
						<!--End From Name Field Set-->
						
						<!--Start From Email Field Set-->
						<div class="form-group">
							<label class="col-md-4 control-label">From Email</label>
							<label class="col-md-8 control-label" id="toName"><?php echo $contactInfo['Email_Id']; ?></label>
						</div>
						<!--End From Email Field Set-->
						
						<!--Start Recipients Field Set-->
						<div class="form-group">
							<label class="col-md-4 control-label vRequired">Recipients <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<select multiple class="col-md-12 selectAlll" style="padding-left: 0px; padding-right: 0px; margin-left: 0px; margin-right: 0px;" data-search="true" id="Recipients" name="Recipients" data-placeholder="Select Recipients ..." required>
									<option value="selectAll">--Select all--</option>
									<option value="clearAll">--Clear all--</option>
									<?php
									foreach ($managerInfo as $row)
									{
										echo '<option value="'.$row['Emp_Email'].'">'. $row['Employee_Name'] .'</option>';
									}
									?>
								</select>
							</div>
                        </div>
						<!--End Recipients Field Set-->
						
						<!--Start External Recipients Field Set-->
						<div class="form-group">
							<label class="col-md-4 control-label">External Recipients</label>
							<div class="col-md-8">
								<input type="text" class="form-control" id="External_Recipients" name="External_Recipients" placeholder="External Recipients" data-hint="Email Addresses separated by comma.">
							</div>
						 </div>
						<!--End External Recipients Field Set-->
						
						<!--Start Subject Field Set-->
						<div class="form-group">
							<label class="col-md-4 control-label vRequired">Subject <span class="short_explanation">*</span></label>
							<div class="col-md-8">
								<input type="text" class="form-control" id="Subject" name="Subject" placeholder="Your subject here..." required>
							</div>
						 </div>
						<!--End Subject Field Set-->
						
						<!--Start Description Field Set-->
						<div class="form-group">
							<label class="col-md-4 control-label">Message</label>
							<div class="col-md-8">
								<textarea name="Message" id="Message" rows="5" class="form-control vDescription" placeholder="Write your message here..." ></textarea>
							</div>
                        </div>
						<!--End Message Field Set-->
						
					</div>
					
					<button type="reset" class="cancel" id="resetBilling" style="display: none;" >Reset</button>
				</form>
			</div>
			<div class="modal-footer text-center">
				
				<button type="reset" class="btn btn-secondary-default btn-embossed" style="bottom: 5px;" id="emailFormResetBilling">
					<i class="mdi-content-send"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed" style="bottom: 5px;" id="emailFormSubmitBilling">
					<i class="mdi-content-send"></i> Send
				</button>
				
			</div>
		</div>
	</div>
</div>

<?php } else { ?>

<div class="col-md-12 portlets">
	<div class="txt_center">Sorry, Access Denied...</div>
</div>

<?php } ?>