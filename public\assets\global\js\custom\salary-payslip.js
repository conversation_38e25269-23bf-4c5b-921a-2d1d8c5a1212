//=========================================================================================
//=========================================================================================
/* Program        : salary-payslip.js													 *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description    :jquery for monthly salary payslip and hourly wages payslip 			 *
 *                                                                                   	 *
 *                                                                                    	 *
 *Revisions      :                                                                    	 *
 *Version    Date           Author                  Description                       	 *
 *0.1        22-Apr-2016    Nivethitha       	    Initial Version          		     *
 *                                                                                    	 */
//=========================================================================================
//=========================================================================================
$(function () {
    
    $.fn.dataTable.ext.errMode = 'none';
    
    /* Alert when the DataSetup is not completed */
    dataSetup('datasetup');
    
    var isDirtyFormHourlyWagesPayslip    = false,
        isDirtyFormMonthlySalary  = false,
        statusFormisDirty         = false,
        isDirtyFormInvoiceDetails = false,
        isDirtyQuarterClosure = false,
        reviewFormisDirty = false;
    var winSize;
    var payslipData     = ignoredPayslipData = employeeIds = [],pendingApprovalEmpIds,generatedEmpIds,readyToGenEmpIds,minSalRange = 0;
    maxSalRange = $('#maxMonthSalary').val(),maxSalRangeLast = $('#maxMonthSalary').val(),minSalRangeLast = 0; 
    var readyToGenerate = payslipGenerated = pendingApproval = 'checked';
    var emp_wise = false,month_year, isUserInitiated = preventStep2 = 0;
    var payslipSubGrid  = null,
        hourlyPayslipSubGrid = null;
    
    // list of location, department and employeetype
    var location_list = [] , dept_list = [] , emp_type_list = [],
         month_year_List = []; 
    //Month Picker Max validation.
    var months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
    var mnthyr = months[new Date(tzDate()).getMonth()-1]+'-'+new Date(tzDate()).getFullYear();
    // get domainName and orgCode
    var domainName = fnIsDomain(),
        orgCode = fngetOrgCode(),
        domainName = domainName.split('.')[0];
    var isKeyPressed = false;
    var initiatedPayslipType = ''; // to trigger monthly/hourly payslip initiation based on this type after default payslip is added

    var paymentDay = function()
    {
        $.ajax ({
                type     : 'POST',
                dataType : 'json',
                async    : false,
                url      : pageUrl () +'payroll/salary-payslip/get-payment-day',
                success  : function (result)
                {
                    var paymentDay = (("0"+result).slice(-2))+"-"+(new Date(tzDate()).getMonth()+1)+"-"+new Date(tzDate()).getFullYear();
                    var curDate = new Date(tzDate()).getDate()+"-"+(new Date(tzDate()).getMonth()+1)+"-"+new Date(tzDate()).getFullYear();
                    var mnth = ("0" + ((new Date(tzDate()).getMonth())+1)).slice(-2);
                    
                    if(paymentDay > curDate) {
                        
                         msg = "Please enter a value less than or equal to "+ (months[(new Date(tzDate()).getMonth())-1]) +"-" + (new Date(tzDate()).getFullYear());
                         //return false;
                     }
                     else if((paymentDay <= curDate) && (fnServerMonthFormatter ($('#salaryMonth').val())) > (new Date(tzDate()).getFullYear()+'-'+mnth))
                     {
                         msg = "Please enter a value less than or equal to "+ (months[(new Date(tzDate()).getMonth())]) +"-" + (new Date(tzDate()).getFullYear());
                     }
                }
        });
        return msg;
    };
  
    $('#salaryMonth').on('changeDate', function () {
        var salMnth = fnServerMonthFormatter ($('#salaryMonth').val());
        var val=0;
        if(salMnth!='')
        {
            $.ajax ({
                type     : 'POST',
                dataType : 'json',
                async    : false,
                url      : pageUrl () +'payroll/salary-payslip/get-closure-month/payslipMnth/'+salMnth,
                success  : function (result)
                {
                    if(result == 'true')
                    {
                        val = 1;
                    }
                }
            });
            
            $.validator.addMethod('vMonthClosure', function () {

                if(val === 0)
                    return true;
                else
                    return false;
        
            },"You can't generate for Closure Month.");  
        }
    });

    /************************************* MonthlySalary Grid ********************************************/
    
    /** Create MonthlySalary Grid**/ 
    var tableMonthlySalary = $('#tableMonthlySalary').dataTable({
        "lengthMenu"     : [ 5, 10, 25, 50, 100, 200 ], 
        "iDisplayLength" : 10,
        "bDestroy"       : true,
        "bAutoWidth"     : false,
        "bServerSide"    : true,
        "bDeferRender"   : true,
        "sServerMethod"  : "POST", 
        "sAjaxSource"    : pageUrl() + "payroll/salary-payslip/monthly-salary-payslip",
        "sAjaxDataProp"  : "aaData",
        "aaSorting"      : [[3, 'desc']],
        "aoColumnDefs"   : [{"targets": 0, "orderable": false},
                            { "sClass" : "visible-xs visible-sm  hidden-md hidden-lg", "aTargets" : [0] },
                            { "sClass" : "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets" : [1] },
                            { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [4] }],
        "fnDrawCallback": function (oSettings) {
            if(oSettings._iRecordsDisplay > 1)
            {
                $("#monthlyCheckAll").show();
            }
            else
            {
                $("#monthlyCheckAll").hide();    
            }
        },
        "fnCreatedRow": function( nRow, aData, iDataIndex ) {
            $(nRow).attr({"data-toggle":"context",
                         "data-target":"#monthly-payslip-context-menu"
                         });
        },
        "aoColumns"      :[{
            "mData" : function (row, type, set) {
                return '<i class="fa fa-plus-square-o"></i>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                return '<div>'+ row['User_Defined_EmpId'] +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                return '<div>'+ row['Employee_Name'] +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                return '<div class="text-center" >'+ row['Salary_Month'] +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                //return '<div>'+ row['Total_Salary'] +'</div>';
                return '<div>'+ row['Total_Salary'] +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                return '<div>'+ row['Payment_Status'] +'</div>';
            }
        }
    ]
    });
    
   
    // we need to display payment status column only for admin
    if($('#adminRightsCheck').val() === "admin")
    {
        tableMonthlySalary.fnSetColumnVis(5, true);
    }
    else
    {
        tableMonthlySalary.fnSetColumnVis(5, false);
    }

    
    //On + icon click in mobile & tablet view
    $(document).on('click', '#tableMonthlySalary i', function () {
        var nTr = $(this).parents('tr')[0];
        
        fnFilterClose ('MonthlyPayslip');
        
        if ( tableMonthlySalary.fnIsOpen(nTr) )
        {
            /* This row is already open - close it */
            $(this).removeClass().addClass('fa fa-plus-square-o');
            tableMonthlySalary.fnClose(nTr);
        }
        else
        {
            var record = tableMonthlySalary.fnGetData( nTr );
            var nRow =  $('#tableMonthlySalary thead tr')[0];
            
            /* Open this row */
            $(this).removeClass().addClass('fa fa-minus-square-o');
            
            valueArray = [];
            headerArray=[];
        
            valueArray.Value_One = record.Total_Salary;
            
            //get grid headers
            gridHeader(nRow.cells);
            
            tableMonthlySalary.fnOpen(nTr, fnDeviceColumnDetails(headerArray,valueArray), 'details hidden-lg hidden-md');
        }
    });

    $(document).ready(function() {
        // check the user is admin, bacause we show the estimated payroll only for admins
        if($('#adminRightsCheck').val() === "admin"){
            fnGetPayrollEstimation();
        }
        // hide generate payslip wizard when page initiated
        if($(window).width() > 991 )
        {            
            $('#hideDiv,#form16HideDiv,#hideDivHrly,#contributionHideDiv,#contributionHideDivHourly').prop({'style':'display:none'});
            $('#divTable,#divTableHrly').prop({'style':'border:1px solid'});
            $('#earndiv,#deductdiv,#earnDivHrly,#deductDivHrly').prop({'style':''});
            $('#earningsTab').prop({'style':'border-right:1px solid;width:100%'});
            $('#earningsHourlyTab').prop({'style':'border-right:1px solid'});            
            $('#formHeader,#formHourlyHeader').prop({'style':'font-weight:bold;text-align:right'});
            $('#formAddress,#formAddress1,#formHourlyAddress,#formHourlyAddress1').prop({'style':'text-align:right'});
        }
        else
        {            
            $('#hideDiv,#form16HideDiv,#hideDivHrly,#contributionHideDiv,#contributionHideDivHourly').prop({'style':'height:20px'});
            $('#divTable,#divTableHrly').prop({'style':''});
            $('#earndiv,#deductdiv,#earnDivHrly,#deductDivHrly').prop({'style':'border:1px solid'});
            $('#earningsTab').prop({'style':''});
            $('#earningsHourlyTab').prop({'style':''});            
            $('#formHeader,#formHourlyHeader').prop({'style':'font-weight:bold;text-align:left'});
            $('#formAddress,#formAddress1,#formHourlyAddress,#formHourlyAddress1').prop({'style':'text-align:left'});
            $('#incent').prop({'style':''});
        }

        $(window).resize(function() {
        
        winSize = $(window).width();
            if($(window).width() > 991)
            {                
                $('#hideDiv,#form16HideDiv,#hideDivHrly,#contributionHideDiv,#contributionHideDivHourly').prop({'style':'display:none'});
                $('#divTable,#divTableHrly').prop({'style':'border:1px solid'});
                $('#earndiv,#deductdiv,#earnDivHrly,#deductDivHrly').prop({'style':''});
                $('#earningsTab').prop({'style':'border-right:1px solid;width:100%'});
                $('#earningsHourlyTab').prop({'style':'border-right:1px solid'});                
                $('#formHeader,#formHourlyHeader').prop({'style':'font-weight:bold;text-align:right'});
                $('#formAddress,#formAddress1,#formHourlyAddress,#formHourlyAddress1').prop({'style':'text-align:right'});
            }
            else
            {                
                $('#hideDiv,#form16HideDiv,#hideDivHrly,#contributionHideDiv,#contributionHideDivHourly').prop({'style':'height:20px'});
                $('#divTable,#divTableHrly').prop({'style':''});
                $('#earndiv,#deductdiv,#earnDivHrly,#deductDivHrly').prop({'style':'border:1px solid'});
                $('#earningsTab').prop({'style':''});
                $('#earningsHourlyTab').prop({'style':''});
                $('#incent').prop({'style':''});                
                $('#formHeader,#formHourlyHeader').prop({'style':'font-weight:bold;text-align:left'});
                $('#formAddress,#formAddress1,#formHourlyAddress,#formHourlyAddress1').prop({'style':'text-align:left'});
            }
        });
    });
    // function to get estimated payroll details
    function fnGetPayrollEstimation() {
        $.ajax ({
            type     : 'POST',
            dataType : 'json',
            // async    : false,
            url      : pageUrl () +'payroll/salary-payslip/get-payroll-estimation',
            success  : function (result) {
                if (isJson (result))  {
                    if(result.isAdmin === "admin") {
                        fnFillValuesOnEstimatedPayroll(result, result.payrollEstimation);
                        $('#estimatePayrollPanel').removeClass('hidden');
                    } else {
                        $('#estimatePayrollPanel').addClass('hidden');
                    }
                } else {
                    $('#estimatePayrollPanel').addClass('hidden');
                    jAlert({msg : "Something went wrong. Please contact system admin", type : "warning" });
                }
            },error : function(estimatedPayrollError) {
                if(parseInt(estimatedPayrollError.status, 10) === 200) {
                    sessionExpired();
                } else {
                    /* To handle internal server error */
                    jAlert({ msg : "Something went wrong. Please contact system admin", type : "warning" });
                }
                $('#estimatePayrollPanel').addClass('hidden');
            }
        });
    }


    // onclick function of recalculation button in estimated payroll summary
    $('#recalculationLarge, #recalculationSmall, #calculateEstimation').on('click', function () {
        $('#modalRecalculationConfirmation').modal('toggle');   
    });

    // clicking the proceed button in recalculation popup
    $('#proceedRecalculation').on('click', function (e) {
        setMask('#wholepage');
        $('#modalRecalculationConfirmation').modal('toggle');
        $.ajax ({
            type     : 'POST',
            dataType : 'json',
            async    : true,
            url      : pageUrl () +'payroll/salary-payslip/calculate-payroll-estimation',
            success  : function (result) {
                if (isJson (result))  {
                    if(result.success) {
                        fnFillValuesOnEstimatedPayroll(result, result.payrollEstimation);
                    }
                    jAlert({msg : result.msg, type : result.type });
                } else {
                    jAlert({msg : "Payroll estimation couldn't be completed due to a technical error, please try after some time.", type : "warning" });
                }
                removeMask();
            },error : function(estimatedPayrollError) {
                if(parseInt(estimatedPayrollError.status, 10) === 200) {
                    sessionExpired();
                } else {
                    /* To handle internal server error */
                    jAlert({ msg : "Payroll estimation couldn't be completed due to a technical error, please try after some time.", type : "warning" });
                }
                removeMask();
            }
        });
    })

    function fnFillValuesOnEstimatedPayroll(result, payrollEstimationArray) {
        let totalEstimatedAmount= 0,
            currencySymbol = result.currencySymbol;
        if(payrollEstimationArray.length > 0){
            for(i = 0;  i < payrollEstimationArray.length; i++) {
                // check the availability of monthly payslip type
                if(payrollEstimationArray[i].Payslip_Type === "Monthly") {
                    $('#monthlyPayslipMonth').text(payrollEstimationArray[i].Payslip_Month);
                    $('#monthlyEarnings').text(currencySymbol + ' ' + payrollEstimationArray[i].Earnings);
                    $('#monthlyContribution').text(currencySymbol + ' ' + payrollEstimationArray[i].Organization_Contributions);
                    $('#monthlyDeduction').text(currencySymbol + ' ' + payrollEstimationArray[i].Deductions);
                    $('#monthlyPayrollCost').text(currencySymbol + ' ' + payrollEstimationArray[i].Payroll_Cost);
                    $('#estimatedPayrollUpdatedBy, #estimatedPayrollUpdatedByMobile').text(payrollEstimationArray[i].Updated_By_Name);
                    $('#estimatedPayrollUpdatedOn, #estimatedPayrollUpdatedOnMobile').text(payrollEstimationArray[i].Updated_On);
                    $('#monthlyEstimatedColumn').removeClass('hidden');
                }
                // check the availability of hourly payslip type
                if(payrollEstimationArray[i].Payslip_Type === "Hourly") {
                    $('#hourlyPayslipMonth').text(payrollEstimationArray[i].Payslip_Month);
                    $('#hourlyEarnings').text(currencySymbol + ' ' + payrollEstimationArray[i].Earnings);
                    $('#hourlyContribution').text(currencySymbol + ' ' + payrollEstimationArray[i].Organization_Contributions);
                    $('#hourlyDeduction').text(currencySymbol + ' ' + payrollEstimationArray[i].Deductions);
                    $('#hourlyPayrollCost').text(currencySymbol + ' ' + payrollEstimationArray[i].Payroll_Cost);
                    $('#hourlyEstimatedColumn').removeClass('hidden');
                }
                // adding monthly and hourly payroll cost for total
                totalEstimatedAmount += parseFloat(payrollEstimationArray[i].Payroll_Cost);
            }
            $('#calculationContent').addClass('hidden');
            $('#recalculationContent').removeClass('hidden');
            $('#estimatedAmount, #estimatedAmountMobile').text(currencySymbol + ' '+ totalEstimatedAmount);
        }else{
            $('#monthlyPayslipMonth').text(result.payslipMonth);
            $('#monthlyEarnings').text(currencySymbol + ' -');
            $('#monthlyContribution').text(currencySymbol + ' -');
            $('#monthlyDeduction').text(currencySymbol + ' -');
            $('#monthlyPayrollCost').text(currencySymbol + ' -');
            $('#recalculationContent').addClass('hidden');
            $('#monthlyEstimatedColumn, #calculationContent').removeClass('hidden');
        }
    }
    /*  Add event listener for select and unselect details  */    
    $(document).on('click', '#tableMonthlySalary tbody td div', function (e) {
        fnFilterClose ('MonthlySalary');
        var selectRow = $(this).parent().parent();
                       
            if (!selectRow.hasClass('row_selected'))
            {
                selectRow.addClass('row_selected');
            }
            else
            {
                if(e.which == 1)
                {
                    selectRow.removeClass('row_selected');
                }
            }
   
        fnMonthlySalaryActionButtons ();
    });
   
    //select all Monthly Payslip record
    $("#monthlyCheckAll").on('click', function() {
        var datatableMenuLength = tableMonthlySalary.fnGetData().length;
        var selectedRowLength = fnGetSelected(tableMonthlySalary).length;
        var datatableRows = $('#tableMonthlySalary tbody td div').parent().parent();
        
        if (datatableMenuLength == selectedRowLength)
        {
            datatableRows.removeClass('row_selected');
        }
        else
        {
            datatableRows.addClass('row_selected');
        }
        
        fnMonthlySalaryActionButtons();
    });
    
    //Trigger delete confirmation popup in delete menu in context menu
    $('#deleteContextMonthlyPayslip,#deleteMonthlyPayslip').on('click', function () {
        var selectedRow = fnGetSelected (tableMonthlySalary );
        
        fnFilterClose ('MonthlyPayslip');
        
        if (selectedRow.length)
        {
            var payslipId = tableMonthlySalary.fnGetData (selectedRow[0]).Payslip_Id;
            
            if (payslipId > 0 && !isNaN(payslipId))
            {
                $('#modalDeleteMonthlyPayslip').modal('toggle');
            }
            else
            {
                jAlert ({ msg : 'Kindly select Monthly Salary Payslip', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select Monthly Salary Payslip', type : 'info' });
        }
    });  
  
    //Delete Monthly Salary Payslip
    $('#deleteConfirmMonthlyPayslip').on('click', function () {
        $('#custom-loading-payslip').prop('style','display:block');//set the mask

        var selectedRow = fnGetSelected (tableMonthlySalary );
        var payslipIdArray = [];
        
        if (selectedRow.length)
        {
            for( i=0; i<selectedRow.length; i++)
            {
                payslipIdArray.push(tableMonthlySalary.fnGetData (fnGetSelected(tableMonthlySalary)[i]).Payslip_Id);
            }
            
            if (payslipIdArray.length > 0 && !isNaN(payslipIdArray.length))
            {
                $.ajax ({
                    type     : 'POST',
                    dataType : 'json',
                    // async    : false,
                    url      : pageUrl () +'payroll/salary-payslip/delete-salary-payslip',
                    data     : { payslipId : payslipIdArray },
                    success  : function (result)
                    {
                        if (isJson (result))
                        {
                            fnRefreshTable (tableMonthlySalary);
                            if (result.success)
                            {
                                jAlert ({ msg : result.msg, type : result.type });
                            }
                            else
                            {
                                jAlert ({ msg : result.msg, type : result.type });    
                            }
                        }
                        else
                        {
                            jAlert({msg : "Something went wrong. Please contact system admin", type : "warning" });
                        }
                        $('#custom-loading-payslip').prop('style','display:none');//remove the mask
                    },
                    error : function(deleteMonthlyPayslipErrorResult){
                        $('#custom-loading-payslip').prop('style','display:none');//remove the mask
                        if(deleteMonthlyPayslipErrorResult.status == 200){
                            sessionExpired();
                        }else{
                            /* To handle internal server error */
                            jAlert({ msg : "Something went wrong. Please contact system admin", type : "warning" });
                        }
                    }
                });
            }
            else
            {
                $('#custom-loading-payslip').prop('style','display:none');//remove the mask
                jAlert ({ msg : 'Kindly select Monthly Salary Payslip', type : 'info' });
            }
        }
        else
        {
            $('#custom-loading-payslip').prop('style','display:none');//remove the mask
            jAlert ({ msg : 'Kindly select Monthly Salary Payslip', type : 'info' });
        }
    });   

    //Generate Monthly Salary Payslip.    
    $('#generateMonthlyPayslip').on('click', function () {
        getSalaryDayFunction(true);
    });
    /* Sidebar toggle event */
    $('[data-toggle]').on('click', function(event) {
        /* Trigger the window resize function to update the width of the payout page.
        Fix for the payout wizard width while minimizing the side bar.  */
        $(window).trigger('resize');
    });
    // payslipGeneration wizard
    var modalpayslipGenerationformWizard = $("#payslipGenerationForm").stepFormWizard({
        height: 'auto',
        theme: "simple",
        startStep: 0,
        onSlideChanged: function (from, to) {

        },
        onNext: function (i) { /* hide the footer in the payout page */
            switch (i) {
                case 0:
                    if(isUserInitiated == 0){
                        $('#custom-loading-payslip').prop('style', 'display:block');
                        /** we have removed href property in the wizard next button. 
                         * Because when we click the next button in the first step, it is focusable. 
                         * So at the time of focus, enter is working on this 'a' tag. 
                         * So we removed this to avoid payslip generation on entering.
                         **/ 
                        $('#payslipGenerationForm .sf-btn-next').removeAttr("href");
                        checkPayslipTemplateExist()
                        return false;
                    }
                    break;
                case 1:
                    if (preventStep2 == 0) {
                        // prevent on enter key press on search field
                        if (!isKeyPressed) {  
                                var payslipMonYear = $('#payslipMonthYear').val().split(',');
                                var payMon = payslipMonYear[0] < 10 ? ('0'+payslipMonYear[0]) : payslipMonYear[0];
                                month_year = payslipMonYear[1] + '-' +  payMon ;
                                employeeIds = getSelectedEmployeeId();
                                if(payslipData.length > 0){
                                    if(employeeIds.length > 0 ){
                                        $('#custom-loading-payslip').prop('style','display:block');
                                        if($('#payslipGenerationForm .sf-btn-next').text() == "Generate Payslip"){
                                            $('#showgeneratePayslip').trigger('click');
                                        }else{
                                            checkPreRequisite();
                                        }
                                    }
                                    else{
                                        $('#modalPayslipGenerationError').modal('toggle');
                                        if($('#payslipGenerationForm .sf-btn-next').text() == "Generate Payslip"){
                                            $('#payslipErrorContent').html( "Please select employees to generate payslip");
                                        }else{
                                            $('#payslipErrorContent').html( "Please select employees to view approval");
                                        }
                                        
                                    }
                                }
                                else{
                                    $('#modalPayslipGenerationError').modal('toggle');
                                    $('#payslipErrorContent').html("Payslip already generated for all the employees");
                                
                                }
                            }
                            return false;
                        }
                    break;
                default:
                    break;
            }
        },
        onPrev: function (i) {
            switch (i) {
                case 0:
                    $('#generatePayslipGrid').addClass('hidden');
                    $('#gridPanelMonthlySalary, #estimatePayrollPanel').removeClass('hidden'); 
                    $('#gridPanelHourlyWagesPayslip').removeClass('hidden'); 
                    // set first month in select field  
                    $('#payslipMonthYear').select2('val',month_year_List[0]);
                    // hide salary ranges details  
                    $('#payslipSalaryRange , #salRangeTitle').addClass('hidden');

                    if ($('#fieldForce').val() == 1) 
                     $('#serviceProviderTitle').addClass('hidden');

                    if ($('#payrollPeriod').val() === 'bimonthly') 
                     $('#payPeriodTitle').addClass('hidden');
                
                    // force-table-responsive class is removed because when we open form scroll bar is set
                    $('#tableMonthlySalary_wrapper').removeClass('force-table-responsive');
                    $('#tableHourlyWagesPayslip_wrapper').removeClass('force-table-responsive');
                    emp_wise = false;
                    return false;
                    break;
                case 1:
                    // reset values when click to previous page
                    resetPayslipEmployeeValues();
                    $('.txn-type-footer').hide();
                    $('#payslipGenerationForm .sf-btn-finish').removeClass('payslip-visible-button');
                    $('#payslipGenerationForm .sf-btn-next').addClass('payslip-visible-button');
                    $('#payslipGenerationForm .sf-btn-next').text('Next');
                    break;  
                case 2:
                    preventStep2 = 0;
                    $('#payslipGenerationForm .sf-btn-finish').removeClass('payslip-visible-button');
                    $("#payslipGenerationForm .sf-btn-next").addClass("payslipWizardDisableButton")
                                                            .removeClass('bg-primary');
                    $('#payslipGenerationForm .sf-btn-next').text('Generate Payslip');
                    resetPayslipEmployeeValues();
                    listPayslipEmployees(1);
                    break;                             
                default:
                    break;
            }
        },
        onFinish: function () {
            window.location.reload();
        }
    });
    // function to reset filter, search, select
    function resetPayslipEmployeeValues(){
        location_list = []; dept_list = []; emp_type_list = [];
        readyToGenerate = payslipGenerated = pendingApproval = 'checked';
        isUserInitiated = 0;
        $('#pendingApprovalSelect, #generatedSelect, #readyToGenSelect').removeClass('hidden');
        $('#pendingApprovalSelect, #generatedSelect, #readyToGenSelect').removeClass('payslipStatusList');
        $('#s2id_filterPayslipStatus').select2('val', "All");
        $("#payslipGenerationForm .sf-btn-next").removeClass("payslipWizardDisableButton")
                                                .addClass('bg-primary');

        updateIgnorePayslipId(0);
        resetFilterValues();
        resetFilterValuesLastStep();
    }
    $('.resetPayoutFilter').on('click',function(){
        resetFilterValues();

        listPayslipEmployees();
    });
    $('.resetPayoutFilterLast').on('click',function(){
        resetFilterValuesLastStep();

        listPayslipGeneratedEmployees();
    });
    function resetFilterValues(){
        $('#s2id_filterPayslipGenerationEmployeeType,#s2id_filterPayslipGenerationLocation,#s2id_filterPayslipGenerationDepartment').select2('val', '');
        $('#locationChip').html(`<div class="chip" >
            <span class="payslip-multi-chip-name" id="payslipGenerationLocationFilterChipName">All</span>
        </div>`);
        $('#deptChip').html(`<div class="chip">
            <span class="payslip-multi-chip-name" id="payslipGenerationDepartmentFilterChipName">All</span>
        </div>`);
        $('#empTypeChip').html(`<div class="chip">
            <span class="payslip-multi-chip-name" id="payslipGenerationEmployeeTypeFilterChipName">All</span>
        </div>`);
        $('#searchPayslipGenerationTransaction').val('');
        $('#searchPayslipTransactionDetails').val('');
        salarySlider.update({
            from: 0,
            to: $('#maxMonthSalary').val(),
        });
        minSalRange = 0;
        maxSalRange = $('#maxMonthSalary').val();
    }
    function resetFilterValuesLastStep(){
        $('#s2id_filterPayslipGenerationEmployeeTypeLastStep,#s2id_filterPayslipGenerationLocationLastStep,#s2id_filterPayslipGenerationDepartmentLastStep').select2('val', '');
        $('#locationChipLastStep').html(`<div class="chip" >
            <span class="payslip-multi-chip-name" id="payslipGenerationLocationFilterChipName">All</span>
        </div>`);
        $('#deptChipLastStep').html(`<div class="chip">
            <span class="payslip-multi-chip-name" id="payslipGenerationDepartmentFilterChipName">All</span>
        </div>`);
        $('#empTypeChipLastStep').html(`<div class="chip">
            <span class="payslip-multi-chip-name" id="payslipGenerationEmployeeTypeFilterChipName">All</span>
        </div>`);
        $('#searchPayslipGenerationTransactionLastStep').val('');
        $('#searchPayslipTransactionDetailsLastStep').val('');
        salarySliderLastStep.update({
            from: 0,
            to: $('#maxMonthSalary').val(),
        });
        minSalRangeLast = 0;
        maxSalRangeLast = $('#maxMonthSalary').val();

    }
    $("#salaryRangeSlider").ionRangeSlider({
        skin: "round",
        type: "double",
        min: 0,
        max: $('#maxMonthSalary').val(),
        from: 0,
        to: $('#maxMonthSalary').val(),
        step:0.1,
        onChange: function (data) {
            maxSalRange = data.to;
            minSalRange = data.from; 
            $('#salaryrangeSelectBtn').removeClass('hidden');
        }
    });

    var salarySlider = $("#salaryRangeSlider").data("ionRangeSlider");

    $("#salaryRangeSliderLastStep").ionRangeSlider({
        skin: "round",
        type: "double",
        min: 0,
        max: $('#maxMonthSalary').val(),
        from: 0,
        to: $('#maxMonthSalary').val(),
        step:0.1,
        onChange: function (data) {
            maxSalRangeLast = data.to;
            minSalRangeLast = data.from; 
            $('#salaryrangeSelectBtnLastStep').removeClass('hidden');
        }
    });

    var salarySliderLastStep = $("#salaryRangeSliderLastStep").data("ionRangeSlider");

    // on click function for to get salary ranges
    $('#salaryrangeSelectBtn').on('click',function(){
        $('#salaryrangeSelectBtn').addClass('hidden');
        listPayslipEmployees();
    })
    // function to filter salary ranges in last step
    $('#salaryrangeSelectBtnLastStep').on('click',function(){
        $('#salaryrangeSelectBtnLastStep').addClass('hidden');
        listPayslipGeneratedEmployees();
    })
    // datepicker dispaly
    $('#payslipSalaryRange').daterangepicker({
        opens: 'center',
        "autoApply": true
      });
    // function to enable wizard button
    function enableWizardButton(){
        $("#payslipGenerationForm .sf-btn-next").removeClass("payslipWizardDisableButton")
        .addClass('bg-primary');

        $('#payslipGenerationForm .sf-btn-next').text('Generate Payslip');
    }
    // to get list of employees for the salary month
    function listPayslipEmployees(isFirstCall, isSearch = 0) {
        $('#custom-loading-payslip').prop('style','display:block');
        location_list = [], dept_list = [], emp_type_list=[];
        // list of locations for display all location
        $('#filterPayslipGenerationLocation option').each(function() { 
            if($(this).prop('value') != 'selectAll' && $(this).prop('value') != 'clearAll'){
                location_list.push ($(this).prop('value'));
            }
        });
        // list of department for display all departments
        $('#filterPayslipGenerationDepartment option').each(function() { 
            if($(this).prop('value') != 'selectAll' && $(this).prop('value') != 'clearAll'){
                dept_list.push ($(this).prop('value'));
            }
        });
        // list of employee type for display all employee types
        $('#filterPayslipGenerationEmployeeType option').each(function() { 
            if($(this).prop('value') != 'selectAll' && $(this).prop('value') != 'clearAll'){
                emp_type_list.push ($(this).prop('value'));
            }
        });
        if (isFirstCall == 1) {
            var filterPayslipEmployeeType = emp_type_list,
                filterPayslipLocation = location_list,
                filterPayslipDepartment = dept_list,
                searchPayslipTransactionDetails = '';
        } else {
            var filterPayslipEmployeeType = $('#s2id_filterPayslipGenerationEmployeeType').select2('val').length > 0 ? $('#s2id_filterPayslipGenerationEmployeeType').select2('val') :emp_type_list;
            var filterPayslipLocation = $('#s2id_filterPayslipGenerationLocation').select2('val').length > 0 ? $('#s2id_filterPayslipGenerationLocation').select2('val') : location_list ;
            var filterPayslipDepartment = $('#s2id_filterPayslipGenerationDepartment').select2('val').length > 0 ? $('#s2id_filterPayslipGenerationDepartment').select2('val') : dept_list ;
            var searchPayslipTransactionDetails = $('#searchPayslipTransactionDetails').val() ? $('#searchPayslipTransactionDetails').val() : $('#searchPayslipGenerationTransaction').val();
        }

        if ($('#fieldForce').val() == 1)
        {
            var serviceProviderId = $('#s2id_monthlyServiceProvider').select2('val');
        }
        else
        {
            var serviceProviderId = 0;
        }

        if ($('#payrollPeriod').val() === 'bimonthly') 
        {
            var payPeriod = $('#payPeriod').val();
        }
        else
        {
            var payPeriod = '';
        }

        $.ajax({
            type: 'POST',
            async: true,
            dataType: 'json',
            url: pageUrl() + 'payroll/salary-payslip/list-payslip-employees',
            data: {
                Salary_Month: $('#payslipMonthYear').val(),
                Paysource_Type: 'Monthly Payslip',
                Employee_Type: filterPayslipEmployeeType.join(),
                location: filterPayslipLocation.join(),
                department: filterPayslipDepartment.join(),
                searchTransactionDetails: searchPayslipTransactionDetails,
                salaryRangeFrom : minSalRange,
                salaryRangeTo : maxSalRange,
                readyToGenerate : readyToGenerate,
                payslipGenerated : payslipGenerated,
                pendingApproval: pendingApproval,
                serviceProviderId: serviceProviderId,
                payPeriod: payPeriod
            },
            success: function (listResult) {
                if (isJson (listResult))
                {
                    if(listResult.success){
                        var viewCard = listResult.viewCard;
                        payslipData = listResult.payslipPendingEmployeeIds;
                        readyToGenEmpIds = listResult.payslipPendingEmployeeIds;
                        pendingApprovalEmpIds = listResult.pendingApprovalEmployeeIds;
                        generatedEmpIds = listResult.payslipGeneratedEmployeeIds;
                        if(readyToGenEmpIds.length <= 0 && pendingApprovalEmpIds.length <= 0 && generatedEmpIds.length <= 0 && isFirstCall == 1){
                            $('#modalPayslipGenerationError').modal('toggle');
                            $('#payslipErrorContent').html( "Please configure the salary master for the employee before initiating the payroll");
                        }
                        else{
                            payslipData = payslipData.concat(pendingApprovalEmpIds);

                            // Uncheck the select all and deselect all
                            $(".selectPayoutCheckbox,.deselectPayoutCheckbox").prop("checked", false);
                        
                            $("#payslipEmployeesViewCard").html('');
                            $("#payslipEmployeesViewCard").append(viewCard);
                            
                            //If record exists, then check and uncheck the cards based on the user selection
                            if(payslipData.length > 0){
                                $('#payslipSelectCheckbox, #payslipDeSelectCheckbox').removeClass('hidden');
                                checkUncheckCards();
                            }else{
                                $('#payslipSelectCheckbox, #payslipDeSelectCheckbox').addClass('hidden');
                            }
                            isUserInitiated = 1;
                            $('.sf-nav-step-1').trigger('click');
                            $('.txn-type-footer').hide();
                            $('#payslipGenerationForm .sf-btn-finish').removeClass('payslip-visible-button');
                            $('#payslipGenerationForm .sf-btn-next').addClass('payslip-visible-button');
                            $('.custom-pagination').html('');
                            $('#paginationLastStep').removeClass('custom-pagination');
                            $('#paginationStep2').addClass('custom-pagination');
                            if($('#s2id_filterPayslipStatus').select2('val') == 'All'){
                                $('#payslipGenerationForm .sf-btn-next').text('Generate Payslip');
                                $("#payslipGenerationForm .sf-btn-next").addClass("payslipWizardDisableButton")
                                .removeClass('bg-primary');
                                $('.txn-type-footer').show();
                                if(pendingApprovalEmpIds.length > 0 && readyToGenEmpIds.length <= 0){
                                    $('#payslipGenerationForm .sf-btn-next').text('View Approval');
                                    $("#payslipGenerationForm .sf-btn-next").removeClass("payslipWizardDisableButton")
                                    .addClass('bg-primary');
                                    $('.txn-type-footer').hide();
                                }else if(readyToGenEmpIds.length > 0 && pendingApprovalEmpIds.length <= 0){
                                    $("#payslipGenerationForm .sf-btn-next").removeClass("payslipWizardDisableButton")
                                    .addClass('bg-primary');
                                    $('.txn-type-footer').hide();
                                }else if(generatedEmpIds.length > 0 && readyToGenEmpIds.length <= 0 && pendingApprovalEmpIds.length <= 0){
                                    $('.txn-type-footer').hide();
                                }
                                if(readyToGenEmpIds.length == 0 && pendingApprovalEmpIds.length == 0 && generatedEmpIds.length == 0){
                                    $('#pendingApprovalSelect, #readyToGenSelect, #generatedSelect').addClass('hidden');
                                    $('.txn-type-footer').hide();
                                }else{
                                    $('#pendingApprovalSelect, #readyToGenSelect, #generatedSelect').removeClass('hidden');
                                }
                            }
                            else if(pendingApprovalEmpIds.length <= 0 && readyToGenEmpIds.length <= 0){
                                $('#payslipGenerationForm .sf-btn-next').addClass("payslipWizardDisableButton")
                                .removeClass('bg-primary');
                            }
                            else{
                                $("#payslipGenerationForm .sf-btn-next").removeClass("payslipWizardDisableButton")
                                .addClass('bg-primary');
                            }
                            if(isFirstCall != 1 && $('#s2id_filterPayslipStatus').select2('val') != 'All'){
                                $('#generatedSelect, #readyToGenSelect, #pendingApprovalSelect').addClass('hidden');
                                if(generatedEmpIds.length > 0 && $('#s2id_filterPayslipStatus').select2('val') == 'Payslip Generated'){
                                    $('#generatedSelect').removeClass('hidden');
                                }
                                if(readyToGenEmpIds.length > 0 && $('#s2id_filterPayslipStatus').select2('val') == 'Ready To Generate'){
                                    $('#readyToGenSelect').removeClass('hidden');
                                }
                                if(pendingApprovalEmpIds.length > 0 && $('#s2id_filterPayslipStatus').select2('val') == 'Pending Approval'){
                                    $('#pendingApprovalSelect').removeClass('hidden');
                                }
                            }
                        }
                    } else{
                        isUserInitiated = 0;
                        jAlert({ msg: "Payslip can't be generated for future month", type: "warning" });
                    }
                }else{
                    isUserInitiated = 0;
                    sessionExpired ();
                }
                $('#custom-loading-payslip').prop('style','display:none');
                createPagination();
            },
            error : function (listPayslipEmployeesErrorResult) {
                $('#custom-loading-payslip').prop('style','display:none');
                isUserInitiated = 0;
                if (listPayslipEmployeesErrorResult.status == 200) {
                  sessionExpired();
                } else {
                    /* To handle internal server error */
                    jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                }
            },
        });
    }
    // function to list payslip generated emplyees
    function listPayslipGeneratedEmployees(isFirstCall,payslipTemplateId,payPeriod=NULL){
        $('#custom-loading-payslip').prop('style','display:block');
        var filterPayslipEmployeeType = $('#s2id_filterPayslipGenerationEmployeeTypeLastStep').select2('val').length > 0 ? $('#s2id_filterPayslipGenerationEmployeeTypeLastStep').select2('val') :emp_type_list;
            var filterPayslipLocation = $('#s2id_filterPayslipGenerationLocationLastStep').select2('val').length > 0 ? $('#s2id_filterPayslipGenerationLocationLastStep').select2('val') : location_list ;
            var filterPayslipDepartment = $('#s2id_filterPayslipGenerationDepartmentLastStep').select2('val').length > 0 ? $('#s2id_filterPayslipGenerationDepartmentLastStep').select2('val') : dept_list ;
            var searchPayslipTransactionDetails = $('#searchPayslipTransactionDetailsLastStep').val() ? $('#searchPayslipTransactionDetailsLastStep').val() : $('#searchPayslipGenerationTransactionLastStep').val();

        $.ajax({
            type: 'POST',
            async: true,
            dataType: 'json',
            url: pageUrl() + 'payroll/salary-payslip/list-payslip-generated-employees',
            data: {
                Employee_Type           : filterPayslipEmployeeType.join(),
                location                : filterPayslipLocation.join(),
                department              : filterPayslipDepartment.join(),
                searchTransactionDetails: searchPayslipTransactionDetails,
                salaryRangeFrom         : minSalRangeLast,
                salaryRangeTo           : maxSalRangeLast,
                Salary_Month            : $('#payslipMonthYear').val(),
                employeeIds             : employeeIds,
                payPeriod               : payPeriod
            },
            success: function (listResult) {
                if (isJson (listResult))
                {
                    if(listResult.success){
                        var viewCard = listResult.viewCard;
                        $("#payslipGeneratedEmployeesViewCard").html('');
                        $("#payslipGeneratedEmployeesViewCard").append(viewCard);
                        $('#payslipGenerationForm .sf-btn-finish').addClass('payslip-visible-button');
                        $('#payslipGenerationForm .sf-btn-next').removeClass('payslip-visible-button');
                        if(isFirstCall == 1){
                            if(listResult.PayslipGeneratedEmployees)
                            {
                                var resultLength=listResult.PayslipGeneratedEmployees.length;
                                var payslipIdArray=[];
                                //Check whether payslip generated or not
                                if(resultLength>0)
                                {
                                    //get a payslipId as a array
                                    for(i=0;i<resultLength;i++)
                                    {
                                        payslipIdArray.push(parseInt(listResult.PayslipGeneratedEmployees[i].Payslip_Id))
                                    }

                                    //invoke function to create payslip pdf
                                    if(payPeriod)
                                    {
                                        if(payPeriod.toLowerCase()=='first half')
                                        {
                                            payslipType='BiMonthly';
                                            createPayslipPdf(payslipType,payslipIdArray,orgCode,"Pdf_Generation",parseInt($('#logEmpId').val(),10),parseInt($('#formId').val(),10),parseInt(payslipTemplateId,10));
                                        }
                                        else if(payPeriod.toLowerCase()=='second half')
                                        {
                                            //need to create payslip pdf for both monthly and bimonthly
                                            payslipType='BiMonthly';
                                            createPayslipPdf(payslipType,payslipIdArray,orgCode,"Pdf_Generation",parseInt($('#logEmpId').val(),10),parseInt($('#formId').val(),10),parseInt(payslipTemplateId,10));
                                        }
                                    }
                                    else
                                    {
                                        payslipType='Monthly';
                                        createPayslipPdf(payslipType,payslipIdArray,orgCode,"Pdf_Generation",parseInt($('#logEmpId').val(),10),parseInt($('#formId').val(),10),parseInt(payslipTemplateId,10));
                                    }
                                }
                            }
                            $('.custom-pagination').html('');
                            $('#paginationStep2').removeClass('custom-pagination');
                            $('#paginationLastStep').addClass('custom-pagination');
                            $('.sf-nav-step-2').trigger('click');
                            $('.txn-type-footer').hide();
                        }
                        if(listResult.PayslipGeneratedEmployees.length <= 0){
                            $('#generatedSelectLastStep').addClass('hidden');
                        }else{
                            $('#generatedSelectLastStep').removeClass('hidden');
                        }
                    }else{
                        preventStep2 = 0;
                        enableWizardButton();
                        sessionExpired ();
                    }
                    preventStep2 = 1;
                }else{
                    preventStep2 = 0;
                    enableWizardButton();
                    sessionExpired ();
                }
                $('#custom-loading-payslip').prop('style','display:none');
                createPaginationLastStep();       
            },
            error : function (listPayslipEmployeesErrorResult) {
                preventStep2 = 0;
                enableWizardButton();
                $('#custom-loading-payslip').prop('style','display:none');
                isUserInitiated = 0;
                if (listPayslipEmployeesErrorResult.status == 200) {
                  sessionExpired();
                } else {
                    /* To handle internal server error */
                    jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                }
            },
        });
    }
    /* View monthly and hourly payslip */
    $(document).on('click','.viewPayslipFromPayout .spViewPayslipFrom',function(){
         //Remove the earnings and deduction tab content
         $("#earningsTab").find("tr:gt(0)").remove();
         $("#deductTab").find("tr:gt(0)").remove();
         // Also clear the combined table if it exists
         $("#earningsDeductionsTab").empty();
         view_pdf_payslip_template($(this).attr('vpPayslipId'), $(this).attr('vpEmployeeName'), $(this).attr('vpTemplateId'), 'view',null,'salary payslip',"Monthly",domainName, orgCode)
    });
    // onchange function for payslip status
    $('#filterPayslipStatus').on('change',function(){
        readyToGenerate = pendingApproval = payslipGenerated = '';
        $("#payslipGenerationForm .sf-btn-next").addClass("payslipWizardDisableButton")
        .removeClass('bg-primary');
        $('#payslipGenerationForm .sf-btn-next').text('Generate Payslip');
        $('#pendingApprovalSelect, #generatedSelect, #readyToGenSelect').addClass('hidden');
        $('.txn-type-footer').hide();
        if( $(this).val() == "Payslip Generated"){
            payslipGenerated = 'checked';
            $('#generatedSelect').addClass('payslipStatusList');
        }
        else if($(this).val() == "Pending Approval"){
            pendingApproval = 'checked';
            $('#pendingApprovalSelect').addClass('payslipStatusList');
            $('#payslipGenerationForm .sf-btn-next').text('View Approval');
        }
        else if($(this).val() == "Ready To Generate") {
            readyToGenerate = 'checked';
            $('#readyToGenSelect').addClass('payslipStatusList');
        }
        else{
            readyToGenerate = pendingApproval = payslipGenerated = 'checked'; 
            $('#pendingApprovalSelect, #generatedSelect, #readyToGenSelect').removeClass('payslipStatusList');
        }
        updateIgnorePayslipId();
        listPayslipEmployees();
    })
    /* When checkbox is checked or unchecked */
    $(document).on('click','.payoutCheckbox',function(){
        var checkBoxId = $(this).prop('id');
        var splitCheckBox = checkBoxId.split('-');
        // Uncheck the deselect all
        $(".deselectPayoutCheckbox").prop("checked", false);

        if ($(this).prop('checked'))
        {
            $("#"+checkBoxId).prop("checked", true);
            $.each(ignoredPayslipData, function(key, row){
                /* If payslip id is checked, remove it in this array */
                if(row == splitCheckBox[1]){
                    ignoredPayslipData.splice(key,1);
                    return;
                }
            });

            checkUncheckSelectAll();
        }
        else
        {
            $("#"+checkBoxId).prop("checked", false);

            /* If payslip id is unchecked, push it in this array */
            if($.inArray(splitCheckBox[1],ignoredPayslipData) == -1){
                ignoredPayslipData.push(splitCheckBox[1]);
            }
            checkUncheckSelectAll();
        }
        if($('#s2id_filterPayslipStatus').select2('val') == 'All'){
            if(pendingApprovalEmpIds.length > 0 && readyToGenEmpIds.length > 0){
                var empId = getSelectedEmployeeId();
                var PendingApprovalExist = [], readyToGenExist=[];
                // compare two array and find same elements in array
                $.grep(pendingApprovalEmpIds, function(el) {
                    if ($.inArray(el, empId) != -1) {
                        PendingApprovalExist.push(el);
                    }   
                });
                $.grep(readyToGenEmpIds, function(el) {
                    if ($.inArray(el, empId) != -1) {
                        readyToGenExist.push(el);
                    }   
                });
                $('#payslipGenerationForm .sf-btn-next').text('Generate Payslip');
                $("#payslipGenerationForm .sf-btn-next").addClass("payslipWizardDisableButton")
                .removeClass('bg-primary');
                if(PendingApprovalExist.length > 0 && readyToGenExist.length > 0){
                    $('.txn-type-footer').show();
                }
                else if(PendingApprovalExist.length > 0){
                    $('.txn-type-footer').hide();
                    $("#payslipGenerationForm .sf-btn-next").removeClass("payslipWizardDisableButton")
                    .addClass('bg-primary');
                    $('#payslipGenerationForm .sf-btn-next').text('View Approval');
                }else if(readyToGenExist.length > 0){
                    $('.txn-type-footer').hide();
                    $("#payslipGenerationForm .sf-btn-next").removeClass("payslipWizardDisableButton")
                    .addClass('bg-primary');
                }else{
                    $('.txn-type-footer').show();
                }
            }
        }
    });
    
    /* When 'select all' checkbox is checked or unchecked */
    $(document).on('click','.selectPayoutCheckbox',function(){
        if ($(this).prop('checked'))
        {
            // Uncheck the deselect all
            $(".deselectPayoutCheckbox").prop("checked", false);

            updateIgnorePayslipId(0);
            if($('#s2id_filterPayslipStatus').select2('val') == 'All'){
                showNote();
            }  
            // enable button when all cards are deselected
            $("#payslipGenerationForm .sf-btn-next").removeClass("payslipWizardDisableButton")
            .addClass('bg-primary');
         
        }
        else
        {
            //check the deselect all
            $(".deselectPayoutCheckbox").prop("checked", true);
             // disable button when all cards are deselected
             $("#payslipGenerationForm .sf-btn-next").addClass("payslipWizardDisableButton")
             .removeClass('bg-primary');
            updateIgnorePayslipId(1);
            if($('#s2id_filterPayslipStatus').select2('val') == 'All'){
                hideNote();
            }
        }
    });
    function showNote(){
        if(pendingApprovalEmpIds.length > 0 && readyToGenEmpIds.length > 0){
            $('.txn-type-footer').show();
            $('#payslipGenerationForm .sf-btn-next').text('Generate Payslip');
            $("#payslipGenerationForm .sf-btn-next").addClass("payslipWizardDisableButton")
            .removeClass('bg-primary');
        }  
    }
    function hideNote(){
        if(pendingApprovalEmpIds.length > 0 && readyToGenEmpIds.length > 0){
            $('.txn-type-footer').hide();
            $('#payslipGenerationForm .sf-btn-next').text('Generate Payslip');
            $("#payslipGenerationForm .sf-btn-next").addClass("payslipWizardDisableButton")
            .removeClass('bg-primary');
        }  
    }
    /* When 'Deselect all' checkbox is checked or unchecked */
    $(document).on('click','.deselectPayoutCheckbox',function(){
        if ($(this).prop('checked'))
        {
            //Uncheck the select all
            $(".selectPayoutCheckbox").prop("checked", false);

            /* Remove all the index and add */
            ignoredPayslipData = [];

            updateIgnorePayslipId(1);
            if($('#s2id_filterPayslipStatus').select2('val') == 'All'){
                hideNote();
            }
            // disable button when all cards are deselected
            $("#payslipGenerationForm .sf-btn-next").addClass("payslipWizardDisableButton")
            .removeClass('bg-primary');
        }
        else
        {
             // enable button when all cards are deselected
             $("#payslipGenerationForm .sf-btn-next").removeClass("payslipWizardDisableButton")
             .addClass('bg-primary');
            //check the select checkbox
            $(".selectPayoutCheckbox").prop("checked", true);
            updateIgnorePayslipId(0);
            if($('#s2id_filterPayslipStatus').select2('val') == 'All'){
                showNote();
            }
        }
    });


    /* Function used to check and uncheck the payslip employee card and the deselectall checkbox */
    function updateIgnorePayslipId(isInsert){
        if(isInsert == 1){
            //Uncheck the all the payslip ids
            $(".payoutCheckbox").prop("checked", false);
                // If payslip data exists
                if(payslipData.length > 0){
                    for (var payslipIndex=0; payslipIndex<payslipData.length; payslipIndex++){
                        ignoredPayslipData.push(payslipData[payslipIndex]);
                    }
                }
        }else{
            //Uncheck the all the payslip ids
            $(".payoutCheckbox").prop("checked", true);            

            /* Empty the ignored payslip id array */
            ignoredPayslipData = [];
        }
    }
 
    /*
    Function to check and uncheck the select all and deselect all checkbox
    based on the employee card check and uncheck
    */
    function checkUncheckSelectAll(){       
        //If all the records are unchecked, then uncheck the select all and check the deselect all
        if(payslipData.length == ignoredPayslipData.length){
            // Uncheck the select all
            $(".selectPayoutCheckbox").prop("checked", false);

            // check the deselect all
            $(".deselectPayoutCheckbox").prop("checked", true);

            // disable button when all cards are deselected
            $("#payslipGenerationForm .sf-btn-next").addClass("payslipWizardDisableButton")
            .removeClass('bg-primary');
        }else if(ignoredPayslipData.length == 0){
            // If all the records are checked,
            // Check the select all
            $(".selectPayoutCheckbox").prop("checked", true);

            // Uncheck the deselect all
            $(".deselectPayoutCheckbox").prop("checked", false);

            // enable button when all cards are selected
            $("#payslipGenerationForm .sf-btn-next").removeClass("payslipWizardDisableButton")
            .addClass('bg-primary');
        }else{
            // If partial records are checked,
            // Uncheck the select all
            $(".selectPayoutCheckbox").prop("checked", false);

            // Uncheck the deselect all
            $(".deselectPayoutCheckbox").prop("checked", false);

            // enable button when any cards are selected
            $("#payslipGenerationForm .sf-btn-next").removeClass("payslipWizardDisableButton")
            .addClass('bg-primary');
        }
    }

    /* Check and uncheck cards based on the ignored employee payslip during list */
    function checkUncheckCards(){
        for (var payslipIndex=0; payslipIndex<payslipData.length; payslipIndex++){
            /* If employees is already unchecked, then it should be unchecked while reset */
            if($.inArray(payslipData[payslipIndex], ignoredPayslipData) == -1){   
                if (!$('#payslipSelect-' + payslipData[payslipIndex]).prop('checked'))
                {
                    $('#payslipSelect-'+payslipData[payslipIndex]).prop('disabled',true);
                }
            }else{
                if ($('#payslipSelect-' + payslipData[payslipIndex]).prop('checked'))
                {
                    $('#payslipSelect-'+payslipData[payslipIndex]).prop('checked',false);
                }
            }

            /* Check and Uncheck the Select all and deselect all checkbox based on the card 
            checkbox selection */
            if(payslipIndex == payslipData.length-1){
                checkUncheckSelectAll();
            }
        }
    }

    // funtion get selected payslip employee ids
    function getSelectedEmployeeId(){
    employeeIds = [];
    // If payslip data exists
    if(payslipData.length > 0){
        for (var payslipIndex=0; payslipIndex<payslipData.length; payslipIndex++){
            /* If payslip id is unchecked, then it should not be considered for the payout */
            if($.inArray(payslipData[payslipIndex], ignoredPayslipData) == -1){
                employeeIds.push(payslipData[payslipIndex]);
            }
        }
    }
        return employeeIds;
    }
    // find salary ranges when monthyear selection triggers
    $('#payslipMonthYear').on('change',function(){
        getSalaryDayFunction();
    })

    // find salary ranges when monthyear selection triggers
    $('#payPeriod').on('change',function(){
        getSalaryDayFunction();
    })

    // function to get slary day
    function getSalaryDayFunction(firstcall){
        if ($('#payrollPeriod').val() === 'bimonthly') 
        {
            var payPeriod = $('#payPeriod').val();
        }
        else
        {
            var payPeriod = '';
        }
                var date = new Date();
                var curr_month_yr = (date.getMonth()+1) +','+date.getFullYear();
                setMask('#wholepage');
                $.ajax ({
                    type     : 'POST',
                    dataType : 'json',
                    async    : true,
                    url      : pageUrl () +'payroll/salary-payslip/get-salary-day',
                    data     : {
                        salaryMonthYear     : firstcall ? curr_month_yr : $('#payslipMonthYear').val(),
                        payPeriod           : payPeriod,
                    },
                    success  : function (result)
                    {
                        if(result)
                        {
                            /* If the financial closure needs to be executed then do not allow the user to generate the payslip */
                            if(parseInt(result.financialClosureTracking, 10)==1)
                            {
                                $("#warningMessage").html(`<span>Financial closure process requires to be executed before initiating the payslip generation for new financial year. Please execute the financial closure here at </span><a href="${pageUrl()}payroll/tax-rules" target="_blank"><u>Execute financial closure</u></a>`);
                                $('#salaryPayslipPrereqTitle').html('<strong>Execute Financial Closure</strong>');
                                $('#modalSalaryPayslipPrereq').modal('toggle');
                                removeMask();
                            }
                            else
                            {
                                if(firstcall && result.salaryDates.Salary_Date.split('-')[1] == result.salaryDates.Last_SalaryDate.split('-')[1]){
                                    $('#payslipSalaryRange').daterangepicker({
                                        "singleDatePicker": true,
                                        "autoApply": true,
                                    });
                                }
                                if(firstcall){
                                    emp_wise = true;
                                    $('#generatePayslipGrid').removeClass('hidden');
                                    $('#gridPanelMonthlySalary, #estimatePayrollPanel').addClass('hidden');
                                    $('#gridPanelHourlyWagesPayslip').addClass('hidden');
                                    $('.txn-type-footer').hide();
                                    /* hide the wizard title(tab) here */
                                    $('div .sf-nav-wrap').addClass('hidden');
                                    // add class and custom text for wizard button
                                    $('#payslipGenerationForm .sf-btn-next').addClass('payslip-visible-button')
                                                                            .addClass('bg-primary');
                                    $('#payslipGenerationForm .sf-btn-prev').addClass('payslip-visible-button')
                                                                            .addClass('bg-primary');
                                    $('#payslipGenerationForm .sf-btn-prev').text('Back');
                                    // add the primary buttons for wizard
                                    $('#payslipGenerationForm .sf-btn-finish').addClass('bg-primary');

                    

                                    // close filter form when it is opened
                                    fnFilterClose ('MonthlySalaryPayslip');
                                    $(window).trigger('resize');
                                    // list of monthYear
                                    $('#payslipMonthYear option').each(function() { 
                                            month_year_List.push ($(this).prop('value'));
                                    });
                                    $('#s2id_payslipMonthYear').select2('val', curr_month_yr );
                                }
                                $('#salRangeTitle, #payslipSalaryRange').removeClass('hidden');

                                if ($('#fieldForce').val() == 1)
                                    $('#serviceProviderTitle').removeClass('hidden');

                                if ($('#payrollPeriod').val() === 'bimonthly') 
                                    $('#payPeriodTitle').removeClass('hidden');

                                var startDate = moment(result.salaryDates.Salary_Date, 'YYYY-MM-DD').format("MM/DD/YYYY");
                                var endDate = moment(result.salaryDates.Last_SalaryDate, 'YYYY-MM-DD').format("MM/DD/YYYY");
                                // set date ranges in datepicker
                                $('#payslipSalaryRange').data('daterangepicker').setStartDate(startDate);
                                $('#payslipSalaryRange').data('daterangepicker').setEndDate(endDate);
                                // display dates in oraganization date format
                                $('#payslipSalaryRange').text(result.salaryStartDate + '  -  ' + result.salaryLastDate);
                                // avoid selecting date ranges, so add pointer events none class
                                $('.daterangepicker').addClass('payslip-daterange-pointer-cls');
                            }
                        }
                        removeMask();
                    },
                    error: function(error){
                        if (error.status == 200) {
                            sessionExpired();
                        } 
                        else {
                            /* To handle internal server error */
                            jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                        }
                        removeMask();
                    }
                });
    }
    // when click continue button in default payslip template added success modal
    $('#continueBtnInSuccessMdl').on('click', function () {
        // trigger when continue btn clicks
        if (initiatedPayslipType === 'Monthly') {
            listPayslipEmployees(1);
        } else {
            generateHourlyPayslip();
        }
        // close success message popup while clicking the continue button 
        $('#modalShowSuccessMsg').modal('toggle');
    });
    /* Apply filter in the payslip generation employees list page */
    $('#filterPayslipGenerationLocation,#filterPayslipGenerationDepartment,#filterPayslipGenerationEmployeeType,#filterPayslipGenerationLocationXS,#filterPayslipGenerationDepartmentXS,#filterPayslipGenerationEmployeeTypeXS').on('change', function () {
        var filterId = $(this).prop('id');

        if (filterId == "filterPayslipGenerationLocation" || filterId == "filterPayslipGenerationLocationXS") {
            $('#s2id_filterPayslipGenerationLocation, #s2id_filterPayslipGenerationLocationXS').select2('val', $(this).val())
        } else if (filterId == "filterPayslipGenerationDepartment" || filterId == "filterPayslipGenerationDepartmentXS") {
            $('#s2id_filterPayslipGenerationDepartment, #s2id_filterPayslipGenerationDepartmentXS').select2('val', $(this).val())
        } else {
            $('#s2id_filterPayslipGenerationEmployeeType, #s2id_filterPayslipGenerationEmployeeTypeXS').select2('val', $(this).val());
        }
        filterLocationChip();

        filterDepartmentChip();

        filterEmployeeTypeChip();

        listPayslipEmployees();
    });
    // add or removal of filter location chips
    function filterLocationChip(){
        if ($("#s2id_filterPayslipGenerationLocation").select2('val').length > 0) {
            var location_len = $("#s2id_filterPayslipGenerationLocation").select2('val').length, 
                location_html = [],
                location_name = $("#s2id_filterPayslipGenerationLocation").text().replace(/\s+/g,',').split(',');
            for(var i=0; i< location_len; i++){
             location_html += `<div class="chip">
				               <span class="payslip-multi-chip-name" id="payslipGenerationLocationFilterChipName${i}">${location_name[i+1]}</span>
                               <span class="chip-button-close chip-button-close-location" value="${[i]}" id="payslipGenerationLocationFilterChipClose${i}" role="button">x</span>
                               </div>`;
            }
            $('#locationChip').html(location_html);
        } else {
            $('#locationChip').html(`<div class="chip" >
			    <span class="payslip-multi-chip-name" id="payslipGenerationLocationFilterChipName">All</span>
		    </div>`);
        }
    }
    // add or removal of filter department chip
    function filterDepartmentChip(){
        if ($("#s2id_filterPayslipGenerationDepartment").select2('val').length > 0) {
            var dept_len = $("#s2id_filterPayslipGenerationDepartment").select2('val').length, 
               dept_html = [],
               dept_name = $("#s2id_filterPayslipGenerationDepartment").text().replace(/\s+/g,',').split(',');
           for(var i=0; i< dept_len; i++){
            dept_html += `<div class="chip">
               <span class="payslip-multi-chip-name" id="payslipGenerationDepartmentFilterChipName${i}">${dept_name[i+1]}</span><span class="chip-button-close chip-button-close-department" id="payslipGenerationDepartmentFilterChipClose${i}" value=${i} role="button">x</span>
           </div>`;
           }
           $('#deptChip').html(dept_html);
       } else {
           $('#deptChip').html(`<div class="chip">
               <span class="payslip-multi-chip-name" id="payslipGenerationDepartmentFilterChipName">All</span>
           </div>`);
       }
    }
    // add or or removal of filter employee type chip
    function filterEmployeeTypeChip(){
        if ($("#s2id_filterPayslipGenerationEmployeeType").select2('val').length > 0) {
            var emp_type_len = $("#s2id_filterPayslipGenerationEmployeeType").select2('val').length, 
                emp_type_html = [],
                emp_type_name = $("#s2id_filterPayslipGenerationEmployeeType").text().replace(/\s+/g,',').split(',');
            for(var i=0; i< emp_type_len; i++){
             emp_type_html += `<div class="chip">
				<span class="payslip-multi-chip-name" id="payslipGenerationEmployeeTypeFilterChipName${i}">${emp_type_name[i+1]}</span><span class="chip-button-close chip-button-close-employee-type" value=${i} id="payslipGenerationEmployeeTypeFilterChipClose${i}" role="button">x</span>
			</div>`;
            }
            $('#empTypeChip').html(emp_type_html);
        } else {
             $('#empTypeChip').html(`<div class="chip">
				<span class="payslip-multi-chip-name" id="payslipGenerationEmployeeTypeFilterChipName">All</span>
			</div>`);
        }
    }
    /** Reset the location fileter chip when close button is clicked */
    $(document).on('click', '.chip-button-close-location', function() {
        var filterLocationUnremovedValue = $("#s2id_filterPayslipGenerationLocation").select2('val');
        filterLocationUnremovedValue.splice($(this).prop('value'),1);
        $("#s2id_filterPayslipGenerationLocation").select2('val', filterLocationUnremovedValue);
        filterLocationChip();
        listPayslipEmployees();
    });

    /** Reset the department fileter chip when close button is clicked */
    $(document).on('click', '.chip-button-close-department', function() {
        var filterDepartmentUnremovedValue = $("#s2id_filterPayslipGenerationDepartment").select2('val');
        filterDepartmentUnremovedValue.splice($(this).prop('value'),1);
        $("#s2id_filterPayslipGenerationDepartment").select2('val', filterDepartmentUnremovedValue);
        filterDepartmentChip();
        listPayslipEmployees();
    });

    /** Reset the Employee Type fileter chip when close button is clicked */
    $(document).on('click', '.chip-button-close-employee-type', function() {
        var filterEmpTypeUnremovedValue = $("#s2id_filterPayslipGenerationEmployeeType").select2('val');
        filterEmpTypeUnremovedValue.splice($(this).prop('value'),1);
        $("#s2id_filterPayslipGenerationEmployeeType").select2('val', filterEmpTypeUnremovedValue);
        filterEmployeeTypeChip();
        listPayslipEmployees();
    });

    var timer = null;
    /** Get payslip employee details based on the value entered in the serach input. */
    /** the browser only recognises a change when the field blurs, so we are using the input event of the search field */
    $('#searchPayslipTransactionDetails,#searchPayslipGenerationTransaction').on('input', function () {
        $('#searchPayslipTransactionDetails,#searchPayslipGenerationTransaction').val($(this).val());
        if (timer) {
            clearTimeout(timer);
        }
        /**The search filter will be applied only after the user stops entering the value in the search field */
        timer = setTimeout(function() {
            listPayslipEmployees(0,1);
        }, 1000);
    });
    // Triggered when any key press event is triggered on this search input
    // $('#searchPayslipTransactionDetails,#searchPayslipGenerationTransaction').keydown(function (e) {
    $('#searchPayslipTransactionDetails,#searchPayslipGenerationTransaction').on('keydown', function (e) {
        if (e.keyCode == 13) {
            /** 13 represents enter key. So at this time we set the value as true.
             * Because we won't allow to initiate payslip on-enter key pressed 
             **/
            isKeyPressed = true;
            e.preventDefault();
        } else {
            isKeyPressed = false;
        }
    });
    $('#searchPayslipTransactionDetails,#searchPayslipGenerationTransaction').on('blur', function () {
        /** After enter key pressed, the values is set as true
         * So, at this time the payslip generation/ view approval button in second step is not clickable. So, in on-blur event we change this as false.
         **/
        isKeyPressed = false;
    });
    /* Apply filter in the payslip generation last page */
    $('#filterPayslipGenerationLocationLastStep,#filterPayslipGenerationDepartmentLastStep,#filterPayslipGenerationEmployeeTypeLastStep,#filterPayslipGenerationLocationXSLastStep,#filterPayslipGenerationDepartmentXSLastStep,#filterPayslipGenerationEmployeeTypeXSLastStep').on('change', function () {
        var filterId = $(this).prop('id');

        if (filterId == "filterPayslipGenerationLocationLastStep" || filterId == "filterPayslipGenerationLocationXSLastStep") {
            $('#s2id_filterPayslipGenerationLocationLastStep, #s2id_filterPayslipGenerationLocationXSLastStep').select2('val', $(this).val())
        } else if (filterId == "filterPayslipGenerationDepartmentLastStep" || filterId == "filterPayslipGenerationDepartmentXSLastStep") {
            $('#s2id_filterPayslipGenerationDepartmentLastStep, #s2id_filterPayslipGenerationDepartmentXSLastStep').select2('val', $(this).val())
        } else {
            $('#s2id_filterPayslipGenerationEmployeeTypeLastStep, #s2id_filterPayslipGenerationEmployeeTypeXSLastStep').select2('val', $(this).val());
        }
        filterLocationChipLastStep();

        filterDepartmentChipLastStep();

        filterEmployeeTypeChipLastStep();

        listPayslipGeneratedEmployees();
    });
    // add or removal of filter location chips
    function filterLocationChipLastStep(){
        if ($("#s2id_filterPayslipGenerationLocationLastStep").select2('val').length > 0) {
            var location_len = $("#s2id_filterPayslipGenerationLocationLastStep").select2('val').length, 
                location_html = [],
                location_name = $("#s2id_filterPayslipGenerationLocationLastStep").text().replace(/\s+/g,',').split(',');
            for(var i=0; i< location_len; i++){
             location_html += `<div class="chip">
				               <span class="payslip-multi-chip-name" id="payslipGenerationLocationFilterChipName${i}">${location_name[i+1]}</span>
                               <span class="chip-button-close chip-button-close-location-last" value="${[i]}" id="payslipGenerationLocationFilterChipClose${i}" role="button">x</span>
                               </div>`;
            }
            $('#locationChipLastStep').html(location_html);
        } else {
            $('#locationChipLastStep').html(`<div class="chip" >
			    <span class="payslip-multi-chip-name" id="payslipGenerationLocationFilterChipName">All</span>
		    </div>`);
        }
    }
    // add or removal of filter department chip
    function filterDepartmentChipLastStep(){
        if ($("#s2id_filterPayslipGenerationDepartmentLastStep").select2('val').length > 0) {
            var dept_len = $("#s2id_filterPayslipGenerationDepartmentLastStep").select2('val').length, 
               dept_html = [],
               dept_name = $("#s2id_filterPayslipGenerationDepartmentLastStep").text().replace(/\s+/g,',').split(',');
           for(var i=0; i< dept_len; i++){
            dept_html += `<div class="chip">
               <span class="payslip-multi-chip-name" id="payslipGenerationDepartmentFilterChipName${i}">${dept_name[i+1]}</span>
               <span class="chip-button-close chip-button-close-department-last" value="${i}" id="payslipGenerationDepartmentFilterChipClose${i}"  role="button">x</span>
           </div>`;
           }
           $('#deptChipLastStep').html(dept_html);
       } else {
           $('#deptChipLastStep').html(`<div class="chip">
               <span class="payslip-multi-chip-name" id="payslipGenerationDepartmentFilterChipName">All</span>
           </div>`);
       }
    }
    // add or or removal of filter employee type chip
    function filterEmployeeTypeChipLastStep(){
        if ($("#s2id_filterPayslipGenerationEmployeeTypeLastStep").select2('val').length > 0) {
            var emp_type_len = $("#s2id_filterPayslipGenerationEmployeeTypeLastStep").select2('val').length, 
                emp_type_html = [],
                emp_type_name = $("#s2id_filterPayslipGenerationEmployeeTypeLastStep").text().replace(/\s+/g,',').split(',');
            for(var i=0; i< emp_type_len; i++){
             emp_type_html += `<div class="chip">
				<span class="payslip-multi-chip-name" id="payslipGenerationEmployeeTypeFilterChipName${i}">${emp_type_name[i+1]}</span><span class="chip-button-close chip-button-close-employee-type-last" value=${i} id="payslipGenerationEmployeeTypeFilterChipClose${i}" role="button">x</span>
			</div>`;
            }
            $('#empTypeChipLastStep').html(emp_type_html);
        } else {
             $('#empTypeChipLastStep').html(`<div class="chip">
				<span class="payslip-multi-chip-name" id="payslipGenerationEmployeeTypeFilterChipName">All</span>
			</div>`);
        }
    }
    /** Reset the location fileter chip when close button is clicked */
    $(document).on('click', '.chip-button-close-location-last', function() {
        var filterLocationUnremovedValue = $("#s2id_filterPayslipGenerationLocationLastStep").select2('val');
        filterLocationUnremovedValue.splice($(this).prop('value'),1);
        $("#s2id_filterPayslipGenerationLocationLastStep").select2('val', filterLocationUnremovedValue);
        filterLocationChipLastStep();
        listPayslipGeneratedEmployees();
    });

    /** Reset the department fileter chip when close button is clicked */
    $(document).on('click', '.chip-button-close-department-last', function() {
        var filterDepartmentUnremovedValue = $("#s2id_filterPayslipGenerationDepartmentLastStep").select2('val');
        filterDepartmentUnremovedValue.splice($(this).prop('value'),1);
        $("#s2id_filterPayslipGenerationDepartmentLastStep").select2('val', filterDepartmentUnremovedValue);
        filterDepartmentChipLastStep();
        listPayslipGeneratedEmployees();
    });

    /** Reset the Employee Type fileter chip when close button is clicked */
    $(document).on('click', '.chip-button-close-employee-type-last', function() {
        var filterEmpTypeUnremovedValue = $("#s2id_filterPayslipGenerationEmployeeTypeLastStep").select2('val');
        filterEmpTypeUnremovedValue.splice($(this).prop('value'),1);
        $("#s2id_filterPayslipGenerationEmployeeTypeLastStep").select2('val', filterEmpTypeUnremovedValue);
        filterEmployeeTypeChipLastStep();
        listPayslipGeneratedEmployees();
    });

    var timer = null;
    /** Get payslip employee details based on the value entered in the serach input. */
    /** the browser only recognises a change when the field blurs, so we are using the input event of the search field */
    $('#searchPayslipTransactionDetailsLastStep,#searchPayslipGenerationTransactionLastStep').on('input', function () {
        $('#searchPayslipTransactionDetailsLastStep,#searchPayslipGenerationTransactionLastStep').val($(this).val());
        if (timer) {
            clearTimeout(timer);
        }
        /**The search filter will be applied only after the user stops entering the value in the search field */
        timer = setTimeout(function() {
            listPayslipGeneratedEmployees(0);
        }, 1000);
    });

    /* Function to create the pagination during page load, search, and filter in payout employees list page*/
    function createPagination(){
        var paginationId = '#payslipEmployeesViewCard'; // Id of payout employees list card
        var dataclass = '.payslip-card-child'; // class of the each payout payslip cards

        $('.custom-pagination').html('');
        
        var paginationSize = 5;//set the no. of pages to be shown at a time
        /* Call the function to create/update the pagination 
        Params - Length of the payslip employees records, Max record to be filled in a page, id of the employees list card, class of the each payslip cards */
        $.pagination($('#payslipEmployeesViewCard .payslip-card-child').length, 8, paginationId, dataclass, paginationSize); 
            
    }
    // function to create pagination in last step
    function createPaginationLastStep(){
        var paginationId = '#payslipGeneratedEmployeesViewCard'; // Id of payout employees list card
        var dataclass = '.payslip-card-child-last'; // class of the each payout payslip cards

        $('.custom-pagination').html('');
        
        var paginationSize = 5;//set the no. of pages to be shown at a time
        /* Call the function to create/update the pagination 
        Params - Length of the payslip employees records, Max record to be filled in a page, id of the employees list card, class of the each payslip cards */
        $.pagination($('#payslipGeneratedEmployeesViewCard .payslip-card-child-last').length, 8, paginationId, dataclass, paginationSize);
    }

    

    $('#filterReportTitle').on('change',function()
    {
        $('#s2id_filterPayslipMonth').select2('val','');
        $('#s2id_filterBusinessUnit').select2('val','');
        $('#s2id_filterEmployeeType').select2('val','');
        $('#s2id_filterGroupBy').select2('val','');
        if($('#s2id_filterReportTitle').select2('val')=='Consolidated Salary Statement')
        {
            $('.groupBy').show();
        }
        else if($('#s2id_filterReportTitle').select2('val')=='Consolidated Pay Report')
        {
            $('.groupBy').hide();
        }
    });

    $('#exportExcel').on('click',function()
    {
        $('#s2id_filterReportTitle').select2('val','');
        $('#s2id_filterPayslipMonth').select2('val','');
        $('#s2id_filterBusinessUnit').select2('val','');
        $('#s2id_filterEmployeeType').select2('val','');
        $('#s2id_filterGroupBy').select2('val','');
        $('#modalFormExportCsv').modal('toggle');
    })

    $('#formSubmitExportCsv').on('click',function()
    {
        var filterReportTitle =  $('#s2id_filterReportTitle').select2('val');
        var filterPayslipMonth = $('#s2id_filterPayslipMonth').select2('val');
        var filterBusinessUnit = $('#s2id_filterBusinessUnit').select2('val');
        var filterEmployeeType = $('#s2id_filterEmployeeType').select2('val');
        var filterGroupBy      = $('#s2id_filterGroupBy').select2('val');
        
        if($('#formExportCsv').valid() && filterReportTitle!=''&& filterReportTitle!=null && filterPayslipMonth!=''&& filterPayslipMonth!=null)
        {
            if(filterReportTitle=='Consolidated Salary Statement'&& filterGroupBy!=''&& filterGroupBy!=null)
            {
                var url = pageUrl () + 'payroll/salary-payslip/export-payslip-csv/Report_Title/'+filterReportTitle+'/Payslip_Month/'+filterPayslipMonth+'/Business_Unit_Id/'+filterBusinessUnit+'/Employee_Type_Id/'+filterEmployeeType+'/Group_By_Id/'+filterGroupBy;
                document.location.href = url;
                $('#modalFormExportCsv').modal('toggle');
            }
            else if(filterReportTitle=='Consolidated Pay Report')
            {
                var url = pageUrl() + 'payroll/salary-payslip/export-payslip-csv/Report_Title/'+filterReportTitle +'/Payslip_Month/' +filterPayslipMonth +'/Business_Unit_Id/' + filterBusinessUnit + '/Employee_Type_Id/' + filterEmployeeType;
                document.location.href = url;
                $('#modalFormExportCsv').modal('toggle');
            }
            else
            {
                jAlert ({ panel : $('#formExportCsv'), msg : "Please fill the mandatory fields.", type : "info" });
            }

            // $.ajax({
            //     type: 'GET', // or 'POST' depending on your API
            //     url: url,
            //     success: function(data) {
            //         // Handle the successful response here, if needed
            //         console.log('Success:', data);
            //     },
            //     error: function(xhr, status, error) {
            //         // Handle any errors that occur during the request
            //         console.error('Error:', error);
            //     }
            // });
            
        }
        else
        {
            jAlert ({ panel : $('#formExportCsv'), msg : "Please fill the mandatory fields.", type : "info" });
        }
    });
    

    //Generate Quarter Closure.   
    $('#quarterClosureMonthlyPayslip').on('click',function()
    {
        $('#modalFormQuarterClosure .modal-title').html("Quarter Closure");
        $('#modalFormQuarterClosure').modal('toggle');
        $('#formSubmitRevertClosure').hide();
        $('#formSubmitQuarterClosure').show();
        
        $('#s2id_formQuarterMnth').select2('val','');
    });
    
    $('#formSubmitQuarterClosure').on('click',function()
    {
        var l = Ladda.create(this);
        l.start();
        
        $('#s2id_formQuarterMnth').removeClass('form-error');
        
        if(isDirtyQuarterClosure)
        {
            if($('#formGenerateFormQuarterClosure').valid())
            {
                $.ajax ({
                        type     : 'POST',
                        dataType : 'json',
                        async    : false,
                        url      : pageUrl () +'payroll/salary-payslip/update-quarter-closure',
                        data     : {
                            assessYr     : $('#assessmentYr').val(),
                            quarterMnth  : $('#formQuarterMnth').val()
                        },
                        success  : function (result)
                        {
                            if(result.success)
                            {
                                isDirtyQuarterClosure = false;
                                $('#modalFormQuarterClosure').modal('toggle');
                                jAlert ({ msg : result.msg, type : result.type });
                            }
                            else
                            {
                                jAlert ({ panel : $('#formGenerateFormQuarterClosure'), msg : result.msg, type : result.type });
                            }
                            
                            l.stop();
                        }
                    });
            }
        }
        else
        {
             jAlert ({ panel : $('#formGenerateFormQuarterClosure'), msg : "Form has no changes.", type : "info" });
             l.stop();
        }
    });
    
    
    $('#formCancelQuarterClosure').on('click',function()
    {
        $('#s2id_assessmentYr,#assessmentYr,#s2id_formQuarterMnth,#formQuarterMnth').removeClass('form-error');
        
        $('#s2id_formQuarterMnth').select2('val','');
        
        $( '#formGenerateFormQuarterClosure').validate().resetForm();
    });
    
    //Generate Quarter Closure.   
    $('#revertClosureMonthlyPayslip').on('click',function()
    {
        $('#modalFormQuarterClosure .modal-title').html("<strong>Revert</strong> Quarter Closure");
        $('#modalFormQuarterClosure').modal('toggle');
        $('#formSubmitRevertClosure').show();
        $('#formSubmitQuarterClosure').hide();
        
        $('#s2id_formQuarterMnth').select2('val','');
        
    });
    
    $('#formSubmitRevertClosure').on('click',function()
    {
        if($('#formGenerateFormQuarterClosure').valid())
        {
            $.ajax ({
                type     : 'POST',
                dataType : 'json',
                async    : false,
                url      : pageUrl () +'payroll/salary-payslip/revert-quarter-closure',
                data     : {
                    assessYr     : $('#assessmentYr').val(),
                    quarterMnth  : $('#formQuarterMnth').val()
                },
                success  : function (result)
                {
                    if(result.success)
                    {
                        isDirtyQuarterClosure = false;
                        $('#modalFormQuarterClosure').modal('toggle');
                        jAlert ({ msg : result.msg, type : result.type });
                    }
                    else
                    {
                        jAlert ({ panel : $('#formGenerateFormQuarterClosure'), msg : result.msg, type : result.type });
                    }
                }
            });
        }
    });
    
    $('#tdsOverride').on('click',function()
    {
        var selectedRow = fnGetSelected (tableMonthlySalary);
        fnFilterClose ('MonthlySalary');
        if (selectedRow.length)
        {
            var record = tableMonthlySalary.fnGetData(selectedRow[0]);
            if (record.Payslip_Id > 0)
            {
                var checkEligibleForTdsOverride =`query checkEligibleForTdsOverride($payslipId:Int!) { checkEligibleForTdsOverride(payslipId:$payslipId) { errorCode message actualTax overRideTaxMaximumValue tdsOverrideEligiblity} }`;
                let tdsOverRideVariables = {
                    payslipId : parseInt(record.Payslip_Id, 10)
                }
                setMask('#wholepage');
                $.ajax({
                    method: 'POST',
                    url: getApiCustomDomains('payrollAdminRo'),
                    headers: getGraphqlAPIHeaders(),
                    data: JSON.stringify({
                        query: checkEligibleForTdsOverride,
                        variables : tdsOverRideVariables                            
                    }),
                    success: function(result) {
                        if(result.data)
                        {
                            var tdsOverrideEligiblity = result.data.checkEligibleForTdsOverride.tdsOverrideEligiblity;
                            if(tdsOverrideEligiblity==='Yes')
                            {
                                var actualTax               = result.data.checkEligibleForTdsOverride.actualTax;
                                var overRideTaxMaximumValue = result.data.checkEligibleForTdsOverride.overRideTaxMaximumValue;
                                $('#modalFormTdsOverride').modal('toggle');
                                $('#viewActualTax').text(actualTax);
                                /** Field validation error should be removed **/
                                $('#overrideTax').removeAttr('required');
                                $('#overrideTax').removeAttr('max');
                                $('#overrideTax').valid();
                                $('#overrideTax').val(0);
                                $('#overrideTax').prop("required", true);
                                $("#overrideTax").prop({"max" : overRideTaxMaximumValue});
                            }
                            else if(tdsOverrideEligiblity==='No')
                            {
                                var msg = '<div>We are unable to override the TDS due to one of the following reasons.'+
                                '<li>	1) TDS for the months other than the last payroll month cannot be modified.</li>'+
                                '<li>	2) Overriding TDS is not allowed for employees during the last month of the notice period.</li>'+
                                '<li>	3) TDS override not allowed for the last month of the financial year.</li>'+
                                '<li>	4) TDS payment status is paid for the current payroll month.</li></div>';
                                jAlertLong ({ msg : msg, type : 'info' });
                            }
                            else
                            {
                                jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                            }
                        }
                        else
                        {
                            fnRefreshTable (tableMonthlySalary);
                            var error = result.errors[0].extensions;
                            var errorCode = error.code;
                            switch (errorCode) {
                                case "BAD_USER_INPUT": // if any input validation error occurs, BAD_USER_INPUT was returned as error code from backend
                                case "IVE0010": // invalid input request
                                case 'IVE0000':
                                var validationErrors = error.validationError;
                                // add all the backend validation error messages as single sentence to present it to the users
                                var validationMessages = "";
                                if (validationErrors) {
                                    for (var errCode in validationErrors) {
                                        // Please provide a valid group name.
                                        if (errCode === "IVE0010") {
                                            validationMessages += validationErrors[errCode];
                                        }
                                    }
                                }
                                // checking the validation message availability
                                if (validationMessages) {
                                    jAlert({ msg:validationMessages,type: 'warning'});
                                }
                                // other validation errors are not handled by users. So as of now it was considers as common error.
                                else {
                                   jAlert({ msg: 'Something went wrong. Please contact system administrator.',type: 'warning'});
                                }
                                break;
                                case '_DB0105':
                                case 'PSP0003':  
                                    jAlert({ msg: result.errors[0].message,type: 'warning'});
                                    break;
                                default:
                                    jAlert({ msg: 'Something went wrong. Please contact system administrator.',type: 'warning'});
                                    break;
                            }
                        }
                        removeMask();
                    },
                    error: function(error){
                        fnRefreshTable (tableMonthlySalary);
                        if (error.status == 200) {
                            sessionExpired();
                        } 
                        else {
                            /* To handle internal server error */
                            jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                        }
                        removeMask();
                    }
                });
            }
            else
            {
                jAlert ({ msg : 'Kindly select monthly salary payslip record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select monthly salary payslip record', type : 'info' });
        }
    });

    $('#formSubmitTdsOverride').on('click',function()
    {
        var selectedRow = fnGetSelected (tableMonthlySalary);
        fnFilterClose ('MonthlySalary');
        if (selectedRow.length)
        {
            var record = tableMonthlySalary.fnGetData(selectedRow[0]);
            if (record.Payslip_Id > 0)
            {
                let viewActualTax = parseFloat($('#viewActualTax').text());
                let overrideTax   =  parseFloat($('#overrideTax').val());
                if(viewActualTax!=overrideTax)
                {
                    var updateEmployeeTax =`mutation updateEmployeeTax($payslipId:Int!,$overrideTax:Float!) { updateEmployeeTax(payslipId:$payslipId,overrideTax:$overrideTax) { errorCode message } }`;
                    let updateEmployeeTaxVariables = {
                        payslipId   : parseInt(record.Payslip_Id, 10),
                        overrideTax : overrideTax
                    }
                    setMask('#wholepage');
                    $.ajax({
                        method: 'POST',
                        url: getApiCustomDomains('payrollAdminWo'),
                        headers: getGraphqlAPIHeaders(),
                        data: JSON.stringify({
                            query: updateEmployeeTax,
                            variables : updateEmployeeTaxVariables                            
                        }),
                        success: function(result) {
                            $('#modalFormTdsOverride').modal('toggle');
                            if(result.data)
                            {
                                fnRefreshTable (tableMonthlySalary);
                                var msg = result.data.updateEmployeeTax.message;
                                jAlert ({ msg : msg, type : 'info' });
                            }
                            else
                            {
                                fnRefreshTable (tableMonthlySalary);
                                var error = result.errors[0].extensions;
                                var errorCode = error.code;
                            
                                switch (errorCode) {
                                    case "BAD_USER_INPUT": // if any input validation error occurs, BAD_USER_INPUT was returned as error code from backend
                                    case "IVE0010": // invalid input request
                                    case 'IVE0000':
                                    case 'IVE0206':  
                                    var validationErrors = error.validationError;
                                    // add all the backend validation error messages as single sentence to present it to the users
                                    var validationMessages = "";
                                    if (validationErrors) {
                                        for (var errCode in validationErrors) {
                                            if (errCode === "IVE0010" || errCode === "IVE0206") 
                                            {
                                                validationMessages += validationErrors[errCode];
                                            }
                                        }
                                    }
                                    // checking the validation message availability
                                    if (validationMessages) {
                                        jAlert({ msg:validationMessages,type: 'warning'});
                                    }
                                    // other validation errors are not handled by users. So as of now it was considers as common error.
                                    else {
                                    jAlert({ msg: 'Something went wrong. Please contact system administrator.',type: 'warning'});
                                    }
                                    break;
                                    case '_DB0105':
                                    case 'PSP0004':    
                                    case 'PSP0101':
                                    case 'PSP0102':  
                                        jAlert({ msg: result.errors[0].message,type: 'warning'});
                                        break;
                                    default:
                                        jAlert({ msg: 'Something went wrong. Please contact system administrator.',type: 'warning'});
                                        break;
                                }
                            }
                            removeMask();
                        },
                        error: function(error){
                            if (error.status == 200) {
                                sessionExpired();
                            } 
                            else {
                                /* To handle internal server error */
                                jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                            }
                            removeMask();
                        }
                    });
                }
                else
                {
                    jAlert({ panel : $('#formTdsOverride'),msg: "Override tax should not be equal to actual tax", type: "warning" });
                    removeMask();
                }
            }
            else
            {
                jAlert ({ msg : 'Kindly select monthly salary payslip record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select monthly salary payslip record', type : 'info' });
        }
    });
    
    /** Change event for Payslip Type combo field **/
    $('#payslipType').on('change', function () {
        var payslipType = $('#s2id_payslipType').select2('val');
        isDirtyFormMonthlySalary = true;
        if (payslipType == 'HourlyWages') {
            $('#monthlyEmployeeType').hide();
        }
        else
        {
            $('#monthlyEmployeeType').show();
        }
    });
    
    
    $('#salaryMonth,#formEmployeeType,#formLocation,#formDepartment').on('change',function() {
        isDirtyFormMonthlySalary = true;
    });
    
    /* Cancel the Monthly Payslip Form.*/
    $('#formCancelMonthlyPayslip').on('click',function()
    {
        var l         = Ladda.create(this);
        l.start();
        $('#salaryMonth,#s2id_formEmployeeType,#s2id_formLocation,#s2id_formDepartment').removeClass('form-error');
        
        $('#salaryMonth').val('');
        $('#s2id_formEmployeeType').select2('val','');
        $('#s2id_formLocation').select2('val','');
        $('#s2id_formDepartment').select2('val','');
       
        $( '#formGeneratePayslip').validate().resetForm();
        
        isDirtyFormMonthlySalary  = false;
        l.stop();        
    });
    
    // Monthly Payslip view - On + button click - get more information by form expand
    $(document).on('click', '#tablePayslipSubGrid i', function ()
    {
        var nTr = $(this).parents('tr')[0];
        if (payslipSubGrid.fnIsOpen(nTr))
        {
            /* This row is already open - close it */
            $(this).removeClass().addClass('fa fa-plus-square-o');
            payslipSubGrid.fnClose(nTr);
        }
        else
        {
            /* Open this row */
            $(this).removeClass().addClass('fa fa-minus-square-o');
            payslipSubGrid.fnOpen(nTr, fnShowHiddenPayslipDetails(nTr), 'details');
        }
    });
    
    
    /*Check for pending pre requiste for generating Payslip*/
    function checkPreRequisite(){
        if ($('#payrollPeriod').val() === 'bimonthly') 
        {
            var payPeriod = $('#payPeriod').val();
        }
        else
        {
            var payPeriod = '';
        }
        $.ajax ({
            type     : 'POST',
            dataType : 'json',
            async    : true,
            url      : pageUrl () +'payroll/salary-payslip/check-pre-requisite',
            data     : {
                month      : emp_wise ? month_year : fnServerMonthFormatter ($('#salaryMonth').val()) ,
                location   : emp_wise ? location_list.join() : $('#formLocation').val().join(),
                department : emp_wise ? dept_list.join() : $('#formDepartment').val().join(),
                empType    : emp_wise ? emp_type_list.join() : $('#formEmployeeType').val().join(),
                employeeId : employeeIds,
                payPeriod: payPeriod
            },
            success  : function (result)
                {
                    
                    if(result)
                    {   
                        $('#showPreRequisite').trigger('click');
                        $('#custom-loading-payslip').prop('style','display:none');
                    }
                },
            error: function(error){
                $('#custom-loading-payslip').prop('style','display:none');
                if (error.status == 200) {
                    sessionExpired();
                  } else {
                      /* To handle internal server error */
                      jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                  }
            }
            });
    }
    /*Show pending pre requiste for approvals before generating Payslip*/
    $('#showPreRequisite').on('click',function()
    {
        
            var month = emp_wise ? month_year : fnServerMonthFormatter ($('#salaryMonth').val());
            var empType = emp_wise ? emp_type_list.join() : $('#formEmployeeType').val().join();
            var location =  emp_wise ? location_list.join() : $('#formLocation').val().join();
            var department = emp_wise ? dept_list.join() : $('#formDepartment').val().join();
            if ($('#payrollPeriod').val() === 'bimonthly') 
            {
                var payPeriod = $('#payPeriod').val();
            }
            else
            {
                var payPeriod = '';
            }
            $("body").append("<form class='prerequisite' style='display:none;' target='_blank'></form>");
            $('.prerequisite').prop('method','POST');
            $('.prerequisite').prop('action',pageUrl() + 'payroll/salary-payslip/pre-requisite');
            
            $('.prerequisite').append('<input type="hidden" name = "month" value= "'+month+'" />');
            $('.prerequisite').append('<input type="hidden" name = "empType" value= "'+empType+'" />');
            $('.prerequisite').append('<input type="hidden" name = "location" value= "'+location+'" />');
            $('.prerequisite').append('<input type="hidden" name = "department" value= "'+department+'" />');
            $('.prerequisite').append('<input type="hidden" name = "employeeId" value= "'+employeeIds+'" />');
            $('.prerequisite').append('<input type="hidden" name = "payPeriod" value= "'+payPeriod+'" />');
            // $('.prerequisite').submit();
            $('.prerequisite').trigger('submit');
            $('.prerequisite').remove();       
            $('#custom-loading-payslip').prop('style', 'display:none');
    });
    
    /*Generate Payslip after selecting the Employee*/
    $('#showgeneratePayslip').on('click',function()
    {
        if ($('#payrollPeriod').val() === 'bimonthly') 
        {
            var payPeriod = $('#payPeriod').val();
        }
        else
        {
            var payPeriod = '';
        }
        $('#dirtyPayslipPreRequisite').modal('hide');
        var l         = Ladda.create(this);
        var serviceProviderId = 0;
        if ($('#payslipType').val() != 'HourlyWages' && !emp_wise) {
            var empType = $('#formEmployeeType').val().join();
            if ($('#fieldForce').val() == 1) {
                var serviceProviderId = $('#s2id_monthlyServiceProvider').select2('val');
            }
        }
        else
        {
            var empType = '';
            if ($('#fieldForce').val() == 1) {
                var serviceProviderId = $('#s2id_formHourlyServiceProvider').select2('val');
            }
        }
        
        l.start();
        $.ajax ({
            type     : 'POST',
            dataType : 'json',
            async    : true,
            url      : pageUrl () +'payroll/salary-payslip/salary-wage-payslip',
            data     : {
                type       : emp_wise ? emp_type_list.join() : empType,
                location   : emp_wise ? location_list.join() : $('#formLocation').val().join(),
                dept       : emp_wise ? dept_list.join() :  $('#formDepartment').val().join(),
                month      : emp_wise? month_year : fnServerMonthFormatter ($('#salaryMonth').val()),
                employeeId : employeeIds,
                serviceProviderId : serviceProviderId,
                payPeriod: payPeriod
            },
            success  : function (resultpayslip)
            {
                if (isJson (resultpayslip))
                {
                    if (resultpayslip.success)
                    {
                        jAlert ({ msg : resultpayslip.msg, type : resultpayslip.type });
                    }
                    else
                    {                          
                        jAlert ({ msg : resultpayslip.msg, type : resultpayslip.type });
                    }
 
                    preventStep2 = 1;
                    if(resultpayslip.payslipGeneratedId.length > 0){
                        listPayslipGeneratedEmployees(1, resultpayslip.payslipTemplateId,payPeriod);
                    }else{
                        preventStep2 = 0;
                        enableWizardButton();
                    }
                    tableMonthlySalary.fnReloadAjax( pageUrl() +'payroll/salary-payslip/monthly-salary-payslip');
                    l.stop();
                        isDirtyFormMonthlySalary = false;
                }
                else
                {
                    preventStep2 = 0;
                    enableWizardButton();
                    sessionExpired ();
                }
                $('#custom-loading-payslip').prop('style','display:none');
            },
            error: function(error){
                $('#custom-loading-payslip').prop('style','display:none');
                preventStep2 = 0;
                enableWizardButton();
                if (error.status == 200) {
                    sessionExpired();
                  } 
                else {
                    /* To handle internal server error */
                    jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                }
            }
        });
        l.stop();
    });
    
     /*Generate Payslip after selecting the Employee*/
    $('#formCommunicateEmployee').on('click',function()
    {
        sendEmailNotification(tableMonthlySalary,'Monthly');
    });
    
    //** Refresh The Bonus Grid**/ 
    $('#gridPanelMonthlySalary .panel-reload').on('click', function () {
        fnRefreshTable (tableMonthlySalary);        
    });
    
    /**
     *  close filter form and clear enabled buttons based on selection record
     *  while search all, sorting, pagination, redraw events
     *  it will work in search.dt order.dt page.dt, length.dt those events
    */
    $('#tableMonthlySalary').on( 'draw.dt', function () {
        fnMonthlySalaryActionButtons (false);
        
        fnFilterClose ('MonthlySalaryPayslip');
    });
    
    
    /** View MonthlySalary Form **/
    $('#viewMonthlySalary,#viewContextMonthlySalary').on('click', function () {
        var selectedRow = fnGetSelected (tableMonthlySalary);
        
        fnFilterClose ('MonthlySalary');
        
        $("#earningsTab").find("tr:gt(0)").remove();
        $("#deductTab").find("tr:gt(0)").remove();
        // Also clear the combined table if it exists
        $("#earningsDeductionsTab").empty();
       
        if (selectedRow.length)
        {
            var record = tableMonthlySalary.fnGetData(selectedRow[0]);
            if (record.Payslip_Id > 0)
            {
                view_pdf_payslip_template(record.Payslip_Id, record.Employee_Name,record.Template_Id, 'view',null,"salary payslip","Monthly",domainName, orgCode)
            }
            else
            {
                jAlert ({ msg : 'Kindly select MonthlySalary', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select MonthlySalary', type : 'info' });
        }
    });
    
    
    $('#exportPrint,#printContextMonthlySalary').on('click',function(){
     var selectedRow = fnGetSelected (tableMonthlySalary);
        
        fnFilterClose ('MonthlySalary');
    
        var payslipIdArray = [], incentiveAmtArray = [],
            formData = {};
       
        if (selectedRow.length)
        {
            for( i=0; i<selectedRow.length; i++)
            {
                payslipIdArray.push(tableMonthlySalary.fnGetData (fnGetSelected(tableMonthlySalary)[i]).Payslip_Id);
                var IncenAmt = tableMonthlySalary.fnGetData (fnGetSelected(tableMonthlySalary)[i]).Incentive_Amount;
                incentiveAmtArray.push(number_to_words(IncenAmt));
            }
            
            formData['Payslip_Id'] = payslipIdArray;
            formData['Incentive_Amount_Words'] = incentiveAmtArray;
            formData['format'] = 'print';
            
            var record = tableMonthlySalary.fnGetData(selectedRow[0]);
            
            if (record.Payslip_Id > 0)
            {
                $('#PrintScreen').addClass('hidden');
                $('#exitPrint').removeClass('hidden');
                $(".printable_portion").html('');
                $('<style>@media print{@page {size: A4;}}</style>').appendTo('.printable_portion');
                view_pdf_payslip_template(record.Payslip_Id, record.Employee_Name,record.Template_Id, 'print',null,"salary payslip","Monthly",domainName, orgCode)
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select MonthlySalary Payslip', type : 'info' });
        }
    });    
    
    $('#viewPageClose').on('click',function(e) {
         $("#earningsTab").find("tr:gt(0)").remove();
         $("#deductTab").find("tr:gt(0)").remove();
         // Also clear the combined table if it exists
         $("#earningsDeductionsTab").empty();
         //$('#formCancelMonthlyPayslip').trigger('click');
         
        if (isDirtyFormMonthlySalary)
        {
            e.preventDefault();
            e.stopImmediatePropagation();
            
            $('#dirtyMonthlySalary').modal('toggle');
        }
        else
        {            
            fnFormCloseMonthlySalary (false);
        }
    });
    
    $('#closeMonthly').on('click',function(e) {
        isDirtyFormMonthlySalary = false;
        
        fnFormCloseMonthlySalary (true);
    });
    
   $('#viewHrlyPageClose').on('click',function() {
         $("#earningsHourlyTab").find("tr:gt(0)").remove();
         $("#deductHourlyTab").find("tr:gt(0)").remove();
    });   
   
   /** Reset The Values In Monthly Salary Payslip Form **/
    $('#formResetMonthlySalary').on('click', function () {
        var l = Ladda.create(this);
        
        l.start();
        
        if ($('#RequestId').val() > 0)
        {            
            fnPreFillValuesFormMonthlySalary (tableMonthlySalary.fnGetData (fnGetSelected (tableMonthlySalary)[0]));
        }
        else
        {         
            fnPreFillValuesFormMonthlySalary('');
        }
            
        l.stop();
    });    
    
    /** Hide Monthly Salary Payslip Modal **/
    $('#modalFormMonthlySalary').on('hide.bs.modal', function (e) {
        if (isDirtyFormMonthlySalary)
        {
            e.preventDefault();
            e.stopImmediatePropagation();
            
    //        $('#dirtyMonthlySalary').modal('toggle');
        }
        else
        {            
            fnFormCloseMonthlySalary (false);
        }
    });
    
    $('#formGenerateFormQuarterClosure').on('change', function () {
        isDirtyQuarterClosure = true;
    });
    
    /** Hide Quarter Closure Modal **/
    $('#modalFormQuarterClosure').on('hide.bs.modal', function (e) {
        if (isDirtyQuarterClosure)
        {
            e.preventDefault();
            e.stopImmediatePropagation();
            
            $('#dirtyQuarterClosure').modal('toggle');
        }
        else
        {
            //$('#modalFormQuarterClosure').modal('hide');
            //fnFormCloseMonthlySalary (false);
        }
    });
    
    $('#closeQuarterClosure').on('click', function () {
        isDirtyQuarterClosure = false;
        
        $('#modalFormQuarterClosure').modal('hide');
    });
    
    
     /** Show & Hide the MonthlySalary Payslip Filter **/
    $('#filterMonthlySalaryPayslip,#closeFilterMonthlySalaryPayslip').on('click', function() {
        if ($('#filterPanelMonthlySalaryPayslip').hasClass('open'))
        {
            $('#filterPanelMonthlySalaryPayslip').removeClass('open');
            $('#filterPanelMonthlySalaryPayslip').hide();
        }
        else
        {
            $('#filterPanelMonthlySalaryPayslip').addClass('open');
            $('#filterPanelMonthlySalaryPayslip').show();
        }
    });
    
    /** Reset The Values In MonthlySalary Filter **/
    $('#cancelMonthlySalary,#closeFilterMonthlySalaryPayslip').on('click', function () {
         if (!$('#filterEmployeeName').is('[readonly]')) {
            $('#filterEmployeeName').val('');
        }
        
        $('#filterTotalSalaryStart,#filterTotalSalaryEnd,#filterSalaryMonth').val('');
        
        $('#s2id_filterMonEmployeeType,#s2id_filterMonLocations,#s2id_filterMonDepartment').select2('val','');
        $('#s2id_filterMonPaymentStatus').select2('val','');
        if ($('#fieldForce').val() == 1) {
            $('#s2id_filterMonthlyServiceProvider').select2('val', $('#defaultServiceProviderId').val());
        }

        tableMonthlySalary.fnReloadAjax( pageUrl() +'payroll/salary-payslip/monthly-salary-payslip' );
    });
    
     /** Apply The Values In MonthlySalary Filter **/
    $('#applyMonthlySalaryPayslip').on('click', function () {
        filterEmployeeName        = $('#filterEmployeeName').val();
        filterSalaryMonth         = fnServerMonthFormatter ($('#filterSalaryMonth').val());
        filterTotalSalaryStart    = $('#filterTotalSalaryStart').val();      
        filterTotalSalaryEnd      = $('#filterTotalSalaryEnd').val();
        filterEmployeeType        = $('#s2id_filterMonEmployeeType').select2('val');
        filterLocation            = $('#s2id_filterMonLocations').select2('val');
        filterDepartment          = $('#s2id_filterMonDepartment').select2('val');
        ftPaymentStatus           = $('#s2id_filterMonPaymentStatus').select2('val');
        if ($('#fieldForce').val() == 1) {
            var ftServiceProvider = $('#s2id_filterMonthlyServiceProvider').select2('val');
        }
        else {
            var ftServiceProvider = '';
        }

        tableMonthlySalary.fnReloadAjax(pageUrl() +'payroll/salary-payslip/monthly-salary-payslip'+
                                                  '/employeeName/'+ filterEmployeeName +
                                                  '/salaryMonth/'+ filterSalaryMonth +
                                                  '/totalSalaryStart/'+ filterTotalSalaryStart +
                                                  '/totalSalaryEnd/'+ filterTotalSalaryEnd+
                                                  '/employeeType/'+filterEmployeeType+
                                                  '/location/'+filterLocation+
                                                  '/department/' + filterDepartment +
                                                  '/paymentStatus/' + ftPaymentStatus +
                                                  '/serviceProviderId/' + ftServiceProvider);
    });
   
   
    //Export Monthly Salary Payslip Pdf
    $("#exportPdf,#pdfContextMonthlySalary").on('click', function(){
        fnFilterClose ('MonthlySalary');
        /* PDF will not be downloaded in the mobile app, so we need to present a message to the user. */

        var mobileApp = localStorage.getItem('isNativeMobileApp');
        var mobileApp = 0;
        if(mobileApp == 1) {
            $('#modalPayslipPDFMobileApp').modal('toggle');
        } else {
            var selectedRow = fnGetSelected (tableMonthlySalary );
            var record = tableMonthlySalary.fnGetData(selectedRow[0]);
            if (record.Payslip_Id > 0)
            {
                view_pdf_payslip_template(record.Payslip_Id, record.Employee_Name, record.Template_Id, 'download',null,"salary payslip","Monthly",domainName, orgCode)
            }
            else
            {
                jAlert ({ msg : 'Kindly select MonthlySalary Payslip', type : 'info' });
            }
        }
    });
    
    $("#bulkPDFMonthlyPayslip").on('click', function(){
        $('#modalBulkPDFMonthlyPayslip').modal('toggle');
        exportpdf();
    });

    $("#deleteConfirmPdfExport").on('click', function(){
        $('#modalDeletePdfExport').modal('toggle');
        deletePdfFolder();
    });

    function deletePdfFolder(){

        $.ajax ({
            type     : 'POST',
            async    : true,     
            dataType : "json",           
            url      : pageUrl ()+'payroll/salary-payslip/delete-pdf-folder',
            success  : function(result)
            {
                if (isJson (result))
                {
                    if(result.success){
                        exportpdf();
                    }
                    else{
                        jAlert ({ msg : result.msg, type : result.type });
                    }
                }
                else
                {
                    sessionExpired ();
                }
            }
        });
    }
    
    function exportpdf(callback){
        setMask('#wholepage');
        var selectedRow = fnGetSelected (tableMonthlySalary );
        var payslipId = tableMonthlySalary.fnGetData (selectedRow[0]).Payslip_Id;
        var payslipIdArray=[], incentiveAmtArrayPDF = [];
        
        for( i=0; i<selectedRow.length; i++)
        {
            payslipIdArray.push(tableMonthlySalary.fnGetData (fnGetSelected(tableMonthlySalary)[i]).Payslip_Id);
            var netPay = tableMonthlySalary.fnGetData (fnGetSelected(tableMonthlySalary)[i]).Incentive_Amount;
            incentiveAmtArrayPDF.push(number_to_words(netPay));
        }
        
        $.ajax ({
            type     : 'POST',
            //dataType : 'json',
            async    : true,                
            url      : pageUrl ()+'payroll/salary-payslip/export-monthly-payslip',
            data     : {
                payslipId    : payslipIdArray.toString(),
                Incentive_Amount_Words : incentiveAmtArrayPDF.toString()
            },
            success  : function(usrDir)
            {
                if (!(usrDir.trim() === "exists"))
                {
                    //////https://stackoverflow.com/questions/36036280/base64-representing-pdf-to-blob-javascript
                    //////https://stackoverflow.com/questions/21797299/convert-base64-string-to-arraybuffer
                    var binary_string =  window.atob(usrDir);
                    var len = binary_string.length;
                    var bytes = new Uint8Array( len );
                    for (var i = 0; i < len; i++)        {
                        bytes[i] = binary_string.charCodeAt(i);
                    }
                    
                    var blob = new Blob([bytes], {type: 'application/pdf'});
                    
                    var fileURL = URL.createObjectURL(blob);
                    var link=document.createElement('a');
                    link.href=fileURL;
                    link.download="Payslip.pdf";
                    //link.click();
                    document.body.appendChild(link);
                    link.click();
                    removeMask();
                }
                else
                {
                    $('#modalDeletePdfExport').modal('toggle');
                    removeMask();
                }
            }
        });
    }
    
    /** On Status approval modal open **/
    $('#statusApprovalMonthlySalary, #statusUpdateContextMonthlySalary').on('click', function(){
        var selectedRow = fnGetSelected (tableMonthlySalary );
        
        fnFilterClose ('MonthlySalary');
         
        if (selectedRow.length)
        {
            var requestId = tableMonthlySalary.fnGetData (selectedRow[0]).Request_Id;
            
            if (requestId > 0)
            {
                setLock({
                    'formName'  : 'MonthlySalary',
                    'uniqueId'  : requestId,
                    'callback'  : function(result)
                    {
                        $('#modalStatusMonthlySalary').modal('toggle');
                        $('#formStatusRequestId').val(requestId);
            
                        fnresetStatusApproval();
                    }
                });
            }
            else
            {
                jAlert ({ msg : 'Kindly select MonthlySalary', type : 'info' });
            }
        }
        else{
            jAlert ({ msg : 'Kindly select MonthlySalary', type : 'info' });           
        }
    });
    
    $('#statusFormMonthlySalary').on('change', function(){        
        statusFormisDirty = true;
    });
    
    // On approval field change event (need to add validation for comment field)
    $('#statusApproval').on('change',function(){
        statusFormisDirty = true;
        
        $("#statusFormMonthlySalary").validate();
        
        if ($(this).val() == "Approved" || $(this).val() == "Complete")
        {
            if($(this).val() == "Approved")
            {
                $('#approvalFormForwardTo').show();
                $('#formStatusForwardTo').addClass('vRequired');
            }
            else
            {
                $('#approvalFormForwardTo').hide();
                $('#formStatusForwardTo').removeClass('vRequired');
            }
            
            $('#labelStatusComment').html('Comment');
            
            $('#formStatusComment').rules('add', {
                required: false
            });
        }
        else
        {
            $('#approvalFormForwardTo').hide();
            $('#formStatusForwardTo').removeClass('vRequired');
            
            $('#labelStatusComment').html('Comment<span class="short_explanation">*</span>');
            
            $('#formStatusComment').rules('add', {
                required: true
            });
        }
    });
    
    // Status Approval reset
    $('#formResetStatusMonthlySalary').on('click', function(){
        var l = Ladda.create(this);
        
        l.start();
       
        fnresetStatusApproval();
        
        l.stop();
    });
    
    // Status Approval submit
    $('#formStatusMonthlySalary').on('click',function(){
        var selectedRow = fnGetSelected (tableMonthlySalary);
        var record = tableMonthlySalary.fnGetData (selectedRow[0]);
        
        var l = Ladda.create(this);
        l.start();
        
        if ($("#statusFormMonthlySalary").valid())
        {
            var status = ($('#s2id_statusApproval').select2('val') == "Approved") ? "In Process" : $('#s2id_statusApproval').select2('val');
            
            $.ajax ({
                type     : 'POST',
                dataType : 'json',
                async    : false,                
                url      : pageUrl ()+'payroll/reimbursement/reimbursement-status/requestId/'+$('#formStatusRequestId').val(),
                data     : {
                    status    : status,
                    forwardTo : $('#formStatusForwardTo').val(),
                    comments  : $('#formStatusComment').val(),
                },
                success  : function(result)
                {
                    if (isJson (result))
                    {                    
                        if (result.success)
                        {
                            statusFormisDirty = false;
                            var forwardTo = $('#formStatusForwardTo').val();
                            
                            if (status!= 'In Process' || (status!= 'In Process' && record.Log_Id != forwardTo )) {
                                 $('#modalStatusMonthlySalary').modal('toggle');
                            }

                            fnsetStatusApprovalSelectField(status);
                            $('#s2id_statusApproval').select2('val','Complete');
                            
                            fnRefreshTable(tableMonthlySalary);
                         
                            jAlert({ msg : result.msg, type : result.type });
                        }
                        else
                        {
                            jAlert ({ panel : $('#statusFormMonthlySalary'), msg : result.msg, type : result.type });
                        }
                    }
                    else
                    {
                        sessionExpired ();
                    }
                    
                    l.stop();
                }
            });          
        }
        else
        {
            l.stop();            
        }
    });
    
    /**
     *  click to close add/edit modal
    */
    $('#editCloseConformStatusMonthlySalary').on('click', function () {
        statusFormisDirty = false;        
        fnFormStatusClose(true);
    });
    
    /**
     *  Click to close add/edit modal form back button
    */
    $('#modalStatusFormClose').on('click', function () {        
        fnCheckFormStatusIsDirty();        
    });
    
    // Add button for MonthlySalary Sub-grid in Add form.
    $('#addMonthlySalarySubGrid').on('click', function(){
        if ($("#onAddCollapse").hasClass('collapsed'))
        {
            $( "#onAddCollapse" ).trigger( "click" );
        }
        
        var requestId = $('#RequestId').val();
        var empId = $('#formEmployeeName').val();
             
        if (requestId > 0 && !isNaN(requestId))
        {
            clearLock ({
                'formName' : 'MonthlySalary',
                'uniqueId' : requestId,
                'callback' : function ()
                {                    
                    $('#lineItemId').val(0);
                }
            });
        }
        fnPreFillFormValuesInvoiceDetails('');        
    });
    
    
    
     /**
     *  Status form modal hide event
    */
    $('#modalStatusMonthlySalary').on('hide.bs.modal', function (e) {        
        if (statusFormisDirty)
        {
            e.preventDefault();
            e.stopImmediatePropagation();
            
            $('#modalDirtyStatusMonthlySalary').modal('toggle');
        }
        else
        {            
            fnFormStatusClose(false);
        }
    });
    
    /**
     *  Show history details for selection record
    */
    $('#historyMonthlySalary, #historyContextMonthlySalary').on('click', function(){
        var selectedRow = fnGetSelected ( tableMonthlySalary );
        
        fnFilterClose ('MonthlySalary');
        
        if (selectedRow.length)
        {            
            var record = tableMonthlySalary.fnGetData (selectedRow[0]);
            
            if (record.Request_Id > 0)
            {
                $('#modalHistoryMonthlySalary').modal('toggle');                
                
                $('#tableMonthlySalaryHistory').dataTable ({
                    "bPaginate"      : false,
                    "bLengthChange"  : false,
                    "bFilter"        : false,
                    "bSort"          : false,
                    "bDestroy"       : true,
                    "bAutoWidth"     : false,
                    "bServerSide"    : true,
                    "bDeferRender"   : true,
                    "sServerMethod"  : "POST",
                    "sAjaxSource"    : pageUrl ()+'payroll/reimbursement/show-audit-reimbursement/requestId/'+record.Request_Id,
                    "sAjaxDataProp"  : "aaData",
                    "aoColumnDefs"   : [{ "sClass" : "text-center", "aTargets" : [0, 1, 2, 3] }],
                    "aaSorting"      : [[0, 'asc']],
                    "aoColumns"      : [{
                        "mData" : function (row, type, set) {
                            return '<div data-toggle="context" data-target="#context-menu" >'+ row['Employee_Name'] +'</div>';
                        }
                    },
                     {
                        "mData" : function (row, type, set) {
                            return '<div data-toggle="context" data-target="#context-menu" >'+ row['Old_Expense'] +'</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            return '<div data-toggle="context" data-target="#context-menu">'+ row['New_Expense'] +'</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            return '<div data-toggle="context" data-target="#context-menu" >'+ row['Old_Amount'] +'</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            return '<div data-toggle="context" data-target="#context-menu">'+ row['New_Amount'] +'</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            return '<div data-toggle="context" data-target="#context-menu">'+ row['ModifiedBy'] +'</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            return '<div data-toggle="context" data-target="#context-menu">'+ row['Modified_Date'] +'</div>';
                        }
                    }]
                });
            }
            else
            {
                jAlert ({ msg : 'Kindly select MonthlySalary', type : 'info' });
            }
        }
        else
        {            
            jAlert ({ msg : 'Kindly select MonthlySalary', type : 'info' });
        }
    });
    
    
    
    
    /**
     *  Click to close add/edit modal form back button
    */
    $('#modalFormCloseMonthlySalaryHistory').on('click', function () {        
         $('#modalHistoryMonthlySalary').modal('hide');    
    });
     
     /**
     *  Show comments details for selection record
    */
    $('#commentMonthlySalary, #commentContextMonthlySalary').on('click', function(){
        var selectedRow = fnGetSelected (tableMonthlySalary );
        
        fnFilterClose ('MonthlySalary');
         
        if (selectedRow.length)
        {            
            var record = tableMonthlySalary.fnGetData (fnGetSelected(tableMonthlySalary)[0]);
        
            if (record.Request_Id > 0)
            {
                $('#modalCommentMonthlySalary').modal('toggle');
                
                $('#tableCommentMonthlySalary').dataTable ({
                    "bPaginate"      : false,
                    "bLengthChange"  : false,
                    "bFilter"        : false,
                    "bSort"          : false,
                    "bDestroy"       : true,
                    "bAutoWidth"     : false,
                    "bServerSide"    : true,
                    "bDeferRender"   : true,
                    "sServerMethod"  : "POST",
                    "sAjaxSource"    : pageUrl ()+'default/index/list-comments/formName/MonthlySalary/parentId/'+record.Request_Id,
                    "sAjaxDataProp"  : "aaData",
                    "aoColumnDefs"   : [{ "sClass" : "text-center", "aTargets" : [0, 1, 2, 3] }],
                    "aaSorting"      : [[0, 'asc']],
                    "aoColumns"      : [{
                        "mData" : function (row, type, set) {
                            return '<div data-toggle="context" data-target="#context-menu" class="'+ row['DT_RowId'] +'" >'+ row['Employee_Name'] +'</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            return '<div data-toggle="context" data-target="#context-menu" class="'+ row['DT_RowId'] +'" >'+ row['Emp_Comment'] +'</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            return '<div data-toggle="context" data-target="#context-menu" class="'+ row['DT_RowId'] +'" >'+ row['Approval_Status'] +'</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            return '<div data-toggle="context" data-target="#context-menu" class="'+ row['DT_RowId'] +'text-center" >'+ row['Added_On'] +'</div>';
                        }
                    }]
                });                    
            }
            else{
                jAlert ({ msg : 'Kindly select MonthlySalary', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select MonthlySalary', type : 'info' });   
        }

    });

    /* Function to send the email notification */
    function sendEmailNotification(payslipTable, payslipType){
        var selectedRow = fnGetSelected (payslipTable);
        var payslipIdArray = [];
        if (selectedRow.length)
        {
            for( i=0; i<selectedRow.length; i++)
            {
                var record = payslipTable.fnGetData (fnGetSelected(payslipTable)[i]);
                var paymentStatus = record.Payment_Status;
                var payslipId     = record.Payslip_Id;
                if(paymentStatus==='Paid' || paymentStatus==='Employee Owes' || paymentStatus==='Nil Payment')
                {
                    payslipIdArray.push(parseInt(payslipId,10));
                }
            }

            if (payslipIdArray.length > 0 && !isNaN(payslipIdArray.length))
            {
                templateId = parseInt(payslipTable.fnGetData(fnGetSelected(payslipTable)[0]).Template_Id, 10);
                /* Email notification will be sent only if the payslip template is addded by the organization.
                So show popup to the user if the payslip template is not exists */
                createPayslipPdf(payslipType, payslipIdArray, orgCode, "Email", parseInt($('#logEmpId').val(), 10), parseInt($('#formId').val(), 10),templateId);
            }
            else 
            {
                infoMessage = 'Please select records with either of the following statuses "Paid", "Employee Owes", "Nil Payment" for sending the mail notification';
                jAlert ({ nonCaseConversionMsg :infoMessage, type : 'info' });
            }
        } 
        else 
        {
            jAlert ({ msg : 'Please select atleast one record.', type : 'info' });                                
        }
    }
    
    /** Close Function For All the Forms **/ 
    function fnFormCloseMonthlySalary (hideAction) {
        var uniqueId = $('#RequestId').val();
       
        if (uniqueId > 0 && !isNaN(uniqueId))
        {
            clearLock ({
                'formName' : 'MonthlySalary',
                'uniqueId' : uniqueId,
                'callback' : function ()
                {
                    $('#formRequestId').val(0);
                    
                    if (hideAction)
                        $('#modalFormMonthlySalary').modal('hide');
                }
            });
        }
        else
        {
            if (hideAction)
                $('#modalFormMonthlySalary').modal('hide');
        }        
    }
    
    
    /**
     *  Prefill Invoice Details form values in edit, reset events
    */
    function fnPreFillValuesFormInvoiceDetails(record) {
        $('#s2id_HourlyWagesPayslip').removeClass('form-error');
        
        if (record != '')
        {            
            $('#RequestId').val(record.Request_Id);
            
            $('#lineItemId').val(record.Line_Item_Id);
            
            $('#formInvoiceNo').val(record.Invoice_No);
            
            $('#formInvoiceDate').val(record.Invoice_Date);
            
            $("#s2id_formHourlyWagesPayslip").select2('val', record.Expense_Type_Id);
            
            $('#formInvoiceAmount').val(record.Invoice_Amount);
        }
        else
        {            
            
            $('#formInvoiceNo,#formInvoiceDate,#formInvoiceAmount').val('');
            
            $('#s2id_formHourlyWagesPayslip').select2('val', '');
            
            $('#formApprovalStatus').text('New');
        }
        
        $( "#editFormInvoiceDetails").validate().resetForm();
        
        isDirtyFormInvoiceDetails = false;
    }
    
     /**
     * Set status approval select field
    */    
    function fnsetStatusApprovalSelectField(approvalStatus){
        var field  = $("#statusApproval");
                    
        field.prop('disabled', false).find('option').remove();
        
        var statusArray=[];
        
        if (approvalStatus=='Approved' || approvalStatus=='In Process') {
            statusArray = ['Complete','Returned','Rejected'];
            
            $('#approvalFormForwardTo').hide();
        }
        else
        {
            statusArray = ['Approved','Returned','Rejected'];
            
            $('#formStatusForwardTo').addClass('vRequired');
            $('#approvalFormForwardTo').show();
        }
        
        field.append( "<option value=''>-- Select --</option>");
        
        for (var x in statusArray)
        {
            field.append("<option value='" + statusArray[x] + "'>" + statusArray[x] + "</option>");
        }
        
        field.select2( 'val', statusArray[0]);
    }
    
    /**
     *  Prefill Bonus form values in add, edit, reset events
    */
    function fnPreFillFormValuesInvoiceDetails(record) {
        $('#s2id_Expense_Type').removeClass('form-error');
        
        if (record != '')
        {            
            $('#RequestId').val(record.Request_Id);
            
            $("#s2id_Expense_Type").select2('val', record.Employee_Id);
            
            $('#formHourlyWagesPayslip').trigger('change');
            
            $('#formHourlyWagesPayslip').prop('readonly', true);
        }
        else
        {            
            $('#s2id_formHourlyWagesPayslip').select2('val', '');
            
            $('#formInvoiceAmount').val('');
            
            $('#formInvoiceDate').val('');
            
            $('#formInvoiceNo').val('');
        }
        
        $( "#editFormInvoiceDetails").validate().resetForm();
        
        isDirtyFormInvoiceDetails = false;
    }   
    
    
    /**
     * Reset status update form
    */
    function fnresetStatusApproval()
    {
        if ( ($('#formStatusRequestId').val()) > 0)
        {            
            var record = tableMonthlySalary.fnGetData(fnGetSelected(tableMonthlySalary)[0]);
            
            if (record != '') {
                if (record.Approval_Status != '') {
                    fnsetStatusApprovalSelectField(record.Approval_Status);                   
                }                
            }
            $('#s2id_statusApproval,#s2id_formStatusForwardTo').removeClass('form-error');
            
            $('#formStatusComment').val('');
        }
        
        statusFormisDirty = false;
    }
    
    /**
     *  While close status update form check form is dirty or not.
    */
    function fnCheckFormStatusIsDirty() {
        //If form is dirty show confirmation to close status update form
        if (statusFormisDirty)
        {
            $('#modalStatusMonthlySalary').modal('toggle');
        }
        //If form isn't dirty then close the add/edit form
        else    
        {            
            fnFormStatusClose(true);
        }
    }
    
    /**
     *  When form isn't dirty then clear the lock flag and then close that status update form
    */
    function fnFormStatusClose(hideAction) {
        var requestId = $('#formStatusRequestId').val();
          
        $( "#statusFormMonthlySalary").validate().resetForm();
         
        if (requestId > 0 && !isNaN(requestId))
        {
            clearLock({
                'formName' : 'MonthlySalary',
                'uniqueId' : requestId,
                'callback' : function ()
                {
                    if (hideAction)
                        $('#modalStatusMonthlySalary').modal('hide');
                }
            });
        }
    }
    
    /** Function To Show And Hide Action Buttons  For All the Forms **/ 
    function fnMonthlySalaryActionButtons (action) {        
        var selectedRowLength = fnGetSelected(tableMonthlySalary).length;
        
        if (selectedRowLength == 0)
        {
            $('#monthlyCheckAll').html('<i class="hr-check-all"></i><span class="hidden-xs hidden-sm">Check All</span>');
            
            fnGridButtons ($('#viewMonthlySalary, #exportPrint, #exportPdf, #deleteMonthlyPayslip,#formCommunicateEmployee,#tdsOverride,#buttonPaymentStatusMonthly,#updatePayslipReviewStatus'), false);
        }
        else if(selectedRowLength == 1)
        {
            $('#monthlyCheckAll').html('<i class="hr-check-all"></i><span class="hidden-xs hidden-sm">Check All</span>');
            
            var record = tableMonthlySalary.fnGetData (fnGetSelected(tableMonthlySalary)[0]);
            var eligibleForContractorTds = parseInt(record.Eligible_For_Contractor_Tds, 10);
            if(eligibleForContractorTds===1)
            {
                fnGridButtons ($('#tdsOverride'), false);
            }
            else
            {
                fnGridButtons ($('#tdsOverride'), true);
            }
            var paymentStatus = record.Payment_Status;
            if(paymentStatus==='Paid' || paymentStatus==='Employee Owes' || paymentStatus==='Nil Payment')
            {
                fnGridButtons ($('#formCommunicateEmployee'), true);
            }
            else
            {
                fnGridButtons ($('#formCommunicateEmployee'), false);
            }
            
            fnGridButtons ($('#viewMonthlySalary, #exportPrint, #exportPdf, #deleteMonthlyPayslip,#buttonPaymentStatusMonthly,#updatePayslipReviewStatus'), true);
            $('#viewContextMonthlySalary, #printContextMonthlySalary, #pdfContextMonthlySalary, #deleteContextMonthlyPayslip').parent().show();
            $('#buttonPaymentStatusMonthly').removeClass().addClass('btn-group');//due to button alignment this class is added
        }
        else
        {
            var datatableMenuLength = tableMonthlySalary.fnGetData().length;
            
            if (datatableMenuLength == selectedRowLength)
            {
                $('#monthlyCheckAll').html('<i class="hr-check-all"></i><span class="hidden-xs hidden-sm">Uncheck All</span>');
            }
            else
            {
                $('#monthlyCheckAll').html('<i class="hr-check-all"></i><span class="hidden-xs hidden-sm">Check All</span>');
            }
            
            fnGridButtons ($('#viewMonthlySalary'), false);
            $('#viewContextMonthlySalary').parent().hide();

            fnGridButtons ($('#exportPrint, #exportPdf,#tdsOverride'), false);
            //When check all is clicked "Notify In Email" should be displayed
            fnGridButtons ($('#deleteMonthlyPayslip,#formCommunicateEmployee,#buttonPaymentStatusMonthly,#updatePayslipReviewStatus'), true);
            $('#printContextMonthlySalary, #pdfContextMonthlySalary').parent().hide();
            $('#deleteContextMonthlyPayslip').parent().show();
            $('#buttonPaymentStatusMonthly').removeClass().addClass('btn-group');//due to button alignment this class is added
        }
    }

    /************************************* Hourly Wages Payslip Grid ********************************************/    
    
    /** Create Hourly Wages Payslip Grid**/ 
    var tableHourlyWagesPayslip = $('#tableHourlyWagesPayslip').dataTable({
        "lengthMenu"     : [ 5, 10, 25, 50, 100 ], 
        "iDisplayLength" : 10,
        "bDestroy"       : true,
        "bAutoWidth"     : false,
        "bServerSide"    : true,
        "bDeferRender"   : true,
        "sServerMethod"  : "POST", 
        "sAjaxSource"    : pageUrl() + "payroll/salary-payslip/hourly-wages-payslip",
        "sAjaxDataProp"  : "aaData",
        "aaSorting"      : [[3, 'desc']],
        "aoColumnDefs"   : [{"targets": 0, "orderable": false},
                            { "sClass" : "visible-xs visible-sm  hidden-md hidden-lg", "aTargets" : [0] },
                            { "sClass" : "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets" : [1] },
                            { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [4] }],
        "fnDrawCallback": function (oSettings) {
            if(oSettings._iRecordsDisplay > 1)
            {
                $("#hourlyCheckAll").show();
            }
            else
            {
                $("#hourlyCheckAll").hide();    
            }
        },
        "fnCreatedRow": function( nRow, aData, iDataIndex ) {
            $(nRow).attr({"data-toggle":"context",
                         "data-target":"#hourly-payslip-context-menu"
                         //"data-delay":'{"show":"1000"}'
                         });
        },
        "aoColumns"      :[{
            "mData" : function (row, type, set) {
                return '<i class="fa fa-plus-square-o"></i>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                return '<div>'+ row['User_Defined_EmpId'] +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                return '<div>'+ row['Employee_Name'] +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                return '<div class="text-center" >'+ row['Salary_Month'] +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                return '<div>'+ row['Total_Salary'] +'</div>';
            }
        },
        {
            "mData"  : function (row, type, set) {
                return '<div>'+ row['Payment_Status'] +'</div>';
            }
        }]
    });

    // we need to display payment status column only for admin
    if($('#adminRightsCheck').val() === "admin")
    {
        tableHourlyWagesPayslip.fnSetColumnVis(5, true);
    }
    else
    {
        tableHourlyWagesPayslip.fnSetColumnVis(5, false);
    }


    
      $('#formDivisionDetails').on('change',function() {
        var formDivisionId = $('#s2id_formDivisionDetails').select2('val');
        hrappDepartmentClassification(formDivisionId,tableMonthlySalary,tableHourlyWagesPayslip);
        
    });
       
    //On + icon click in mobile & tablet view
    $(document).on('click', '#tableHourlyWagesPayslip i', function () {
        var nTr = $(this).parents('tr')[0];
        
        fnFilterClose ('HourlyWagesPayslip');
        
        if ( tableHourlyWagesPayslip.fnIsOpen(nTr) )
        {
            /* This row is already open - close it */
            $(this).removeClass().addClass('fa fa-plus-square-o');
            tableHourlyWagesPayslip.fnClose(nTr);
        }
        else
        {
            var record = tableHourlyWagesPayslip.fnGetData( nTr );
            var nRow =  $('#tableHourlyWagesPayslip thead tr')[0];
            
            /* Open this row */
            $(this).removeClass().addClass('fa fa-minus-square-o');
            
            valueArray = [];
            headerArray=[];
            
            valueArray.Value_One = record.Total_Salary;
            
            //get grid headers
            gridHeader(nRow.cells);
            
            tableHourlyWagesPayslip.fnOpen(nTr, fnDeviceColumnDetails(headerArray,valueArray), 'details hidden-lg hidden-md');
        }
    });
    
    /*  Add event listener for select and unselect details  */
    $(document).on('click', '#tableHourlyWagesPayslip tbody td div', function (e) {
        fnFilterClose ('HourlyWagesPayslip');
        
        var selectRow = $(this).parent().parent();
        
        if($('#loginEmpHWPayslipDeleteRights').val() == 1)
        {
            if (!selectRow.hasClass('row_selected'))
            {
                selectRow.addClass('row_selected');
            }
            else
            {
                if(e.which == 1)
                {
                    selectRow.removeClass('row_selected');
                }
            }
        }
        else
        {
            tableHourlyWagesPayslip.$('tr.row_selected').removeClass('row_selected');
            
            if (!selectRow.hasClass('row_selected'))
            {
                selectRow.addClass('row_selected');
            }
        }        
        
        fnHourlyPayslipActionButtons ();
    });
    
    
    //select all Hourly Payslip record
    $("#hourlyCheckAll").on('click', function() {
        var datatableMenuLength = tableHourlyWagesPayslip.fnGetData().length;
        var selectedRowLength = fnGetSelected(tableHourlyWagesPayslip).length;
        var datatableRows = $('#tableHourlyWagesPayslip tbody td div').parent().parent();
        
        if (datatableMenuLength == selectedRowLength)
        {
            datatableRows.removeClass('row_selected');
        }
        else
        {
            datatableRows.addClass('row_selected');
        }
        
        fnHourlyPayslipActionButtons();
    });
    
    /** Refresh Hourly Wages Payslip Grid**/ 
    $('#gridPanelHourlyWagesPayslip .panel-reload').on('click', function () {
        fnRefreshTable (tableHourlyWagesPayslip)
    });
    
    /**
     *  close filter form and clear enabled buttons based on selection record
     *  while search all, sorting, pagination, redraw events
     *  it will work in search.dt order.dt page.dt, length.dt those events
    */
    $('#tableHourlyWagesPayslip').on( 'draw.dt', function () {
        fnHourlyPayslipActionButtons (false);
        
        fnFilterClose ('HourlyWagesPayslip');
    });
   
   $('#initiateHourlyWagesPayslip').on('click',function(){
        generateHourlyPayslip();
    });
    // function called when click initiate hourly payslip based on scenarios
    function generateHourlyPayslip() {
         /* If the financial closure needs to be executed then do not allow the user to generate the payslip */
         if(parseInt($('#financialClosureTracking').val(), 10) !== 1){

            fnPreFillValuesFormHourlyWagesPayslip ('');
        
            $('#modalFormHourlyWages .modal-title').html("<strong>Generate</strong> Hourly Wages Payslip");
            $('#modalFormHourlyWages').modal('toggle');
            $('#formGenerateHourlyPayslip').show();
            $('#formActionHourlyPayslip').show();
            $('#hourlyPayslipType').trigger('change');
            $('#formGenerateHourlyPayslipSubGrid').hide();
            $('#formActionHourlyPayslipSubGrid').hide();
    
            var formDivisionId = $("#formDivisionDetails").find("option:selected").val();
            fnGetDepartmentName('Salary Payslip','formHourlyDepartment',formDivisionId);
            $('#formCancelHourlyPayslip').trigger('click'); 

        } else {
            $("#warningMessage").html(`<span>Financial closure process requires to be executed before initiating the payslip generation for new financial year. Please execute the financial closure here at </span><a href="${pageUrl()}payroll/tax-rules" target="_blank"><u>Execute financial closure</u></a>`);
            $('#salaryPayslipPrereqTitle').html('<strong>Execute Financial Closure</strong>');
            $('#modalSalaryPayslipPrereq').modal('toggle');
        }
    }
    
    $('#formVerifyHourlyPayslip').on('click',function()
    {
        var l = Ladda.create(this);
        if ($('#fieldForce').val() == 1) {
            var serviceProviderId = parseInt($('#s2id_formHourlyServiceProvider').select2('val'), 10);
            var serviceProviderName = $('#s2id_formHourlyServiceProvider').text().trim();
        }
        else {
            var serviceProviderId = 0;
            var serviceProviderName = '';
        }

        // payslip template exist, then call this function to display initiate hourly payslip popup
        $('#s2id_formHourlyLocation,#s2id_formHourlyDepartment').removeClass('form-error');
        l.start();

        if ($('#formGenerateHourlyPayslip').valid()) {
            //to check payslip template exist or not
            fnGetDefaultPayslipTemplateList('If you want to initiate hourly wage payslip you have to create a payslip template', 'Hourly', serviceProviderId, serviceProviderName, function (result) {
                if (result !== 'error') 
                {
                    if (result === 'payslipTemplateExist') 
                    {
                        $.ajax({
                            type: 'POST',
                            dataType: 'json',
                            async: false,
                            url: pageUrl() + 'payroll/salary-payslip/verify-salary-wage-payslip',
                            data: {
                                payslipType: $('#hourlyPayslipType').val(),
                                salaryMonth: fnServerMonthFormatter($('#salaryMonthHourly').val()),
                                location: $('#formHourlyLocation').val(),
                                department: $('#formHourlyDepartment').val(),
                                serviceProviderId: serviceProviderId
                            },
                            success: function (result) {
                                if (isJson(result)) {
                                    if (result.success) {
                                        $('#formGenerateHourlyPayslip').hide();
                                        $('#formActionHourlyPayslip').hide();

                                        if ($('#hourlyPayslipType').val() == 'HourlyWages') {
                                            $('#modalFormHourlyWages .modal-title').html("<strong>Generate</strong> Hourly Wages Payslip");
                                            $('#monthHourlyTitle').text("Verify Hourly Wages Payslip for the month of " + result.month);
                                        }
                                        else {
                                            $('#modalFormHourlyWages .modal-title').html("<strong>Generate</strong> Monthly Salary Payslip");
                                            $('#monthHourlyTitle').text("Verify Monthly Salary Payslip for the month of " + result.month);
                                        }

                                        $('#tableHourlyPayslipSubGrid thead tr th').removeClass('sorting_asc');
                                        hourlyPayslipSubGrid = $('#tableHourlyPayslipSubGrid').dataTable({
                                            "bPaginate": false,
                                            "bLengthChange": false,
                                            "bFilter": false,
                                            "bInfo": false,
                                            "bDestroy": true,
                                            "bAutoWidth": false,
                                            "aaData": result.aaData,
                                            "aaSorting": [],
                                            "aoColumnDefs": [{ 'bSortable': false, 'aTargets': ['_all'] }],
                                            "aoColumns": [{
                                                "mData": function (row, type, set) {
                                                    return '<i class="fa fa-plus-square-o"></i>';
                                                }
                                            },
                                            {
                                                "mData": function (row, type, set) {
                                                    return '<div data-toggle="context" data-target="#context-menu" >' + row['Employee_Name'] + '</div>';
                                                }
                                            },
                                            {
                                                "mData": function (row, type, set) {
                                                    return '<div data-toggle="context" data-target="#context-menu" >' + row['Total_Salary'] + '</div>';
                                                }
                                            }]
                                        });

                                        $('#formGenerateHourlyPayslipSubGrid').show();
                                        $('#formActionHourlyPayslipSubGrid').show();

                                        // force-table-responsive class is removed because when we open form scroll bar is set
                                        $('#tableHourlyPayslipSubGrid').parent().removeClass('force-table-responsive');
                                    }
                                    else {
                                        jAlert({ panel: $('#editFormInvoiceDetails'), msg: result.msg, type: result.type });
                                    }
                                }
                                else {
                                    sessionExpired();
                                }
                            }
                        });
                    } 
                    else 
                    {
                        $('#modalFormHourlyWages').modal('toggle');
                        // if not exist, show preview popup
                        initiatedPayslipType = 'Hourly';
                        $('#modalPayslipTemplatePreview').modal('toggle');
                    }
            }
            });
        }
        l.stop();

    });
    
    /* Cancel the Hourly Payslip Form.*/
    $('#formCancelHourlyPayslip').on('click',function()
    {
        var l = Ladda.create(this);
        l.start();
        $('#salaryMonthHourly,#s2id_formHourlyLocation,#s2id_formHourlyDepartment').removeClass('form-error');
        
        $('#salaryMonthHourly').val('');
        $('#s2id_formHourlyLocation').select2('val','');
        $('#s2id_formHourlyDepartment').select2('val','');
        
        $('#formGenerateHourlyPayslip').validate().resetForm();
        l.stop();        
    });
    
    
    // Hourly Wages Payslip view - On + button click - get more information by form expand
    $(document).on('click', '#tableHourlyPayslipSubGrid i', function () {
        var nTr = $(this).parents('tr')[0];
        
        if ( hourlyPayslipSubGrid.fnIsOpen(nTr) )
        {
            /* This row is already open - close it */
            $(this).removeClass().addClass('fa fa-plus-square-o');
            
            hourlyPayslipSubGrid.fnClose(nTr);
        }
        else
        {
            /* Open this row */
            $(this).removeClass().addClass('fa fa-minus-square-o');
            
            hourlyPayslipSubGrid.fnOpen(nTr, fnShowHiddenHourlyPayslipDetails(nTr), 'details');
        }
    });
    
    
    
    
    /*Generate Payslip after selecting the Employee*/
    $('#formsubmitHourlyPayslip').on('click',function()
    {
        if ($('#fieldForce').val() == 1) {
            var serviceProviderId = $('#s2id_formHourlyServiceProvider').select2('val');
        }
        else {
            var serviceProviderId = 0;
        }

        setMask('#wholepage');
        $.ajax ({
        type     : 'POST',
        dataType : 'json',
        async    : true,
        url      : pageUrl () +'payroll/salary-payslip/salary-wage-payslip',
        data     : {
            location   : $('#formHourlyLocation').val().join(),
            dept       : $('#formHourlyDepartment').val().join(),
            month      : fnServerMonthFormatter($('#salaryMonthHourly').val()),
            serviceProviderId: serviceProviderId
        },
        success  : function (result)
        {
            if (isJson (result))
            {
                if (result.success)
                {   
                    //for now payslipIds are not returned so this condition is checked
                    var payslipIdArray=result.payslipIds?result.payslipIds:[];
                    var payslipTemplateId = result.payslipTemplateId;
                    jAlert ({ panel : $('#hourlyPayslipGridMsgPanel'), msg : result.msg, type : result.type });  
                    //function to create payslip pdf for hourly payslip
                    createPayslipPdf("Hourly",payslipIdArray,orgCode,"Pdf_Generation",parseInt($('#logEmpId').val(),10),parseInt($('#formId').val(),10),parseInt(payslipTemplateId,10));              
                }
                else
                {
                    jAlert ({ panel : $('#hourlyPayslipGridMsgPanel'), msg : result.msg, type : result.type });
                }
                
                tableHourlyWagesPayslip.fnReloadAjax( pageUrl() +'payroll/salary-payslip/hourly-wages-payslip' );
                
                $.ajax ({
                    type     : 'POST',
                    dataType : 'json',
                    async    : true,
                    url      : pageUrl () +'payroll/salary-payslip/verify-salary-wage-payslip',
                    data     : {
                        payslipType   : $('#hourlyPayslipType').val(),
                        salaryMonth   : fnServerMonthFormatter($('#salaryMonthHourly').val()),
                        location      : $('#formHourlyLocation').val(),
                        department    : $('#formHourlyDepartment').val(),
                        serviceProviderId : serviceProviderId
                    },
                    success  : function (result)
                    {
                        if (isJson (result))
                        {
                            if (result.success)
                            {
                                $('#tableHourlyPayslipSubGrid thead tr th').removeClass('sorting_asc');
                                hourlyPayslipSubGrid = $('#tableHourlyPayslipSubGrid').dataTable({
                                                            "bPaginate"     : false,
                                                            "bLengthChange" : false,
                                                            "bFilter"       : false,
                                                            "bSort"         : false,
                                                            "bInfo"         : false,
                                                            "bDestroy"      : true,
                                                            "bAutoWidth"    : false,
                                                            "aaData"        : result.aaData,
                                                            "aoColumns"      :[{
                                                                "mData" : function (row, type, set) {
                                                                    return '<i class="fa fa-plus-square-o"></i>';
                                                                }
                                                            },
                                                            {
                                                                "mData"  : function (row, type, set) {
                                                                    return '<div data-toggle="context" data-target="#context-menu" >'+ row['Employee_Name'] +'</div>';
                                                                }
                                                            },
                                                            {
                                                                "mData"  : function (row, type, set) {
                                                                    return '<div data-toggle="context" data-target="#context-menu" >'+ row['Total_Salary'] +'</div>';
                                                                }
                                                            }]
                                                        });
                            }
                        }
                    }
                });
            }
            else
            {
                jAlert ({ panel : $('#hourlyPayslipGridMsgPanel'), msg : 'Something went wrong. Please contact system admin', type : 'warning' });
            }
            removeMask();
        },
        error  : function (hourlyPayslipErrorResult){
            removeMask();
            if(hourlyPayslipErrorResult.status == 200){
                sessionExpired();
            }else{
                /* To handle internal server error */
                jAlert({ panel : $('#hourlyPayslipGridMsgPanel'), msg : 'Something went wrong. Please contact system admin', type : 'warning' });
            }
        }
        });  
    });
    
    
    /*Generate Payslip after selecting the Employee*/
    $('#formHourlycommunicateEmployee').on('click',function(){
        sendEmailNotification(tableHourlyWagesPayslip,'Hourly');
    });
   
   /** View MonthlySalary Form **/
    $('#viewHourlyWagesPayslip,#viewContextHourlyWagesPayslip').on('click', function () {              
        var selectedRow = fnGetSelected (tableHourlyWagesPayslip);
        fnFilterClose ('HourlyWages');
        
        $("#earningsHourlyTab").find("tr:gt(0)").remove();
        $("#deductHourlyTab").find("tr:gt(0)").remove();
         
        if (selectedRow.length)
        {
            var record = tableHourlyWagesPayslip.fnGetData(selectedRow[0]);
            
            if (record.Payslip_Id > 0)
            {
                view_pdf_payslip_template(record.Payslip_Id,record.Employee_Name,record.Template_Id,'view',null,'salary payslip',"Hourly",domainName, orgCode);
            }
            else
            {
                jAlert ({ msg : 'Kindly select Hourly Wages Payslip', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select Hourly Wages Payslip', type : 'info' });
        }
    });
    
     /** Add Hourly Wages Payslip **/
    $('#addHourlyWagesPayslip').on('click', function () {
        tableHourlyWagesPayslip.$('tr.row_selected').removeClass('row_selected');
        
        fnHourlyPayslipActionButtons (false);
        fnFilterClose ('HourlyWagesPayslip');       
        
        $('#modalFormHourlyWagesPayslip .modal-title').html("<strong>Add</strong> Hourly Wages Payslip");
        
        fnPreFillValuesFormHourlyWagesPayslip ('');
        
        $('#viewFormHourlyWagesPayslip, #editInViewHourlyWagesPayslip').hide();
        $('#editFormHourlyWagesPayslip, #formActionHourlyWagesPayslip').show();
    });
   
    /** Submit Hourly Wages Payslip Form **/
    $('#formSubmitHourlyWagesPayslip').on('click', function () {
        var l      = Ladda.create(this);
        var record = tableHourlyWagesPayslip.fnGetData(fnGetSelected(tableHourlyWagesPayslip)[0]);
        
        l.start();
        
        if (isDirtyFormHourlyWagesPayslip)
        {
            if ($('#editFormHourlyWagesPayslip').valid())
            {
                $.ajax ({
                    type     : 'POST',
                    dataType : 'json',
                    async    : false,
                    url      : pageUrl() + "payroll/reimbursement/update-expensetypes",
                    data     : {
                        expenseId      : $('#ExpenseId').val(),
                        expenseTitle   : $('#formEditExpenseTitle').val(),
                        maximumAmount  : $('#formEditMaximumAmount').val(),                        
                        description    : $('#formEditDescription').val()          
                    },
                    success  : function(result)
                    {
                        if (result.success)
                        {
                            isDirtyFormHourlyWagesPayslip = false;
                            
                            $('#modalFormHourlyWagesPayslip').modal('toggle');
                            
                            fnRefreshTable (tableHourlyWagesPayslip);
                            
                            jAlert ({ msg : result.msg, type : result.type });
                        }
                        else
                        {
                            jAlert ({ panel : $('#editFormHourlyWagesPayslip'), msg : result.msg, type : result.type });
                        }
                        
                        l.stop();
                    }
                });
            }
            else
            {
                l.stop();                
            }
        }
        else
        {
            l.stop();
            jAlert ({ panel : $('#editFormHourlyWagesPayslip'), msg : 'Form has no changes', type : 'info' });
        }
    });
   
   //Trigger delete confirmation popup in delete menu in context menu
    $('#deleteContextHourlyWagesPayslip,#deleteHourlyWagesPayslip').on('click', function () {
        var selectedRow = fnGetSelected (tableHourlyWagesPayslip );
        
        fnFilterClose ('HourlyWagesPayslip');
        
        if (selectedRow.length)
        {
            var payslipId = tableHourlyWagesPayslip.fnGetData (selectedRow[0]).Payslip_Id;
            
            if (payslipId > 0 && !isNaN(payslipId))
            {
                $('#modalDeleteHourlyWagesPayslip').modal('toggle');
            }
            else
            {
                jAlert ({ msg : 'Kindly select Hourly Wages Payslip', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select Hourly Wages Payslip', type : 'info' });
        }
    });  
  
    //Delete Hourly Wages Payslip
    $('#deleteConfirmHourlyWagesPayslip').on('click', function () {
        $('#custom-loading-payslip').prop('style','display:block'); //set the mask

        var selectedRow = fnGetSelected (tableHourlyWagesPayslip );
        var payslipIdArray = [];
        
        if (selectedRow.length)
        {            
            for( i=0; i<selectedRow.length; i++)
            {
                payslipIdArray.push(tableHourlyWagesPayslip.fnGetData (fnGetSelected(tableHourlyWagesPayslip)[i]).Payslip_Id);
            }            
            
            if (payslipIdArray.length > 0 && !isNaN(payslipIdArray.length))
            {
                $.ajax ({
                    type     : 'POST',
                    dataType : 'json',
                    // async    : false,
                    url      : pageUrl () +'payroll/salary-payslip/delete-wage-payslip',
                    data     : { payslipId : payslipIdArray },
                    success  : function (result)
                    {
                        if (isJson (result))
                        {
                            fnRefreshTable (tableHourlyWagesPayslip);
                            jAlert ({ msg : result.msg, type : result.type });
                        }
                        else
                        {
                            jAlert({ msg : "Something went wrong. Please contact system admin", type : "warning" });
                        }
                        $('#custom-loading-payslip').prop('style','display:none'); //remove the mask
                    },
                    error : function(deleteHourlyPayslipErrorResult){
                        $('#custom-loading-payslip').prop('style','display:none'); //remove the mask
                        if(deleteHourlyPayslipErrorResult.status == 200){
                            sessionExpired();
                        }else{
                            /* To handle internal server error */
                            jAlert({msg : "Something went wrong. Please contact system admin", type : "warning" });
                        }
                    }
                });
            }
            else
            {
                $('#custom-loading-payslip').prop('style','display:none'); //remove the mask
                jAlert ({ msg : 'Kindly select Hourly Wages Payslip', type : 'info' });
            }
        }
        else
        {
            $('#custom-loading-payslip').prop('style','display:none'); //remove the mask
            jAlert ({ msg : 'Kindly select Hourly Wages Payslip', type : 'info' });
        }
    });    
   
     /** Show&Hide the Hourly Wages Payslip Filter **/
    $('#filterHourlyWagesPayslip,#closeFilterHourlyWagesPayslip').on('click', function() {
        if ($('#filterPanelHourlyWagesPayslip').hasClass('open'))
        {
            $('#filterPanelHourlyWagesPayslip').removeClass('open');
            $('#filterPanelHourlyWagesPayslip').hide();
        }
        else
        {
            $('#filterPanelHourlyWagesPayslip').addClass('open');
            $('#filterPanelHourlyWagesPayslip').show();
        }
    });
    
    /** Reset The Values In Hourly Wages Payslip Filter **/
    $('#cancelHourlyWagesPayslip,#closeFilterHourlyWagesPayslip').on('click', function () {
         if (!$('#filterhourlyEmployeeName').is('[readonly]')) {
            $('#filterhourlyEmployeeName').val('');
        }
        
        $('#filterhourlyTotalSalaryStart,#filterhourlyTotalSalaryEnd,#filterhourlySalaryMonth').val('');
        $('#s2id_filterHouPaymentStatus').select2('val','');

        if ($('#fieldForce').val() == 1) {
            $('#s2id_filterHourlyServiceProvider').select2('val', $('#defaultServiceProviderId').val());
        }
      
        tableHourlyWagesPayslip.fnReloadAjax( pageUrl() +'payroll/salary-payslip/hourly-wages-payslip' );
    });
    
     /** Apply The Values In Hourly Wages Payslip Filter **/
    $('#applyHourlyWagesPayslip').on('click', function () {   
        filterEmployeeName        = $('#filterhourlyEmployeeName').val();      
        filterSalaryMonth         = fnServerMonthFormatter($('#filterhourlySalaryMonth').val());
        filterTotalSalaryStart    = $('#filterhourlyTotalSalaryStart').val();      
        filterTotalSalaryEnd      = $('#filterhourlyTotalSalaryEnd').val();
        ftPaymentStatus           = $('#s2id_filterHouPaymentStatus').select2('val');

        if ($('#fieldForce').val() == 1) {
            var ftServiceProvider = $('#s2id_filterHourlyServiceProvider').select2('val');
        }
        else {
            var ftServiceProvider = '';
        }
        
        tableHourlyWagesPayslip.fnReloadAjax(pageUrl() +'payroll/salary-payslip/hourly-wages-payslip'+
                                                  '/employeeName/'+ filterEmployeeName +
                                                  '/salaryMonth/'+ filterSalaryMonth +
                                                  '/totalSalaryStart/'+ filterTotalSalaryStart +
                                                  '/totalSalaryEnd/' + filterTotalSalaryEnd +
                                                  '/paymentStatus/' + ftPaymentStatus +
                                                  '/serviceProviderId/' + ftServiceProvider);
    });
    
    //Export Hourly Wages Payslip Pdf
    $("#exportHourlyPdf,#pdfContextHourlyWagesPayslip").on('click', function(){
        fnFilterClose ('HourlyWages');
        var mobileApp = localStorage.getItem('isNativeMobileApp');
        var mobileApp = 0;
        if(mobileApp == 1) {
            $('#modalPayslipPDFMobileApp').modal('toggle');
        } else {
            var selectedRow = fnGetSelected (tableHourlyWagesPayslip );
           
            if(selectedRow.length > 0)
            {
                var record = tableHourlyWagesPayslip.fnGetData(selectedRow[0]);
                view_pdf_payslip_template(record.Payslip_Id, record.Employee_Name, record.Template_Id, 'download',null,'salary payslip',"Hourly",domainName, orgCode);
            }
            else
            {
                jAlert ({ msg : 'Kindly select Hourly Wages Payslip', type : 'info' });           
            }
        }
    });
    
    
    //Hourly Wages Payslip Print
    $('#exportHourlyPrint,#printContextHourlyWagesPayslip').on('click',function(){
     var selectedRow = fnGetSelected (tableHourlyWagesPayslip);
        
        fnFilterClose ('HourlyWages');
       
        var payslipIdArray = [], incentiveAmtArray = [],
            formData = {};
        
        if (selectedRow.length)
        {
            for( i=0; i<selectedRow.length; i++)
            {
                payslipIdArray.push(tableHourlyWagesPayslip.fnGetData (fnGetSelected(tableHourlyWagesPayslip)[i]).Payslip_Id);
                var IncenAmt = tableHourlyWagesPayslip.fnGetData (fnGetSelected(tableHourlyWagesPayslip)[i]).Total_Salary;
                incentiveAmtArray.push(number_to_words(IncenAmt));
            }
            
            formData['Payslip_Id'] = payslipIdArray;
            formData['Incentive_Amount_Words'] = incentiveAmtArray;
            formData['format'] = 'print';
            
            if (payslipIdArray.length > 0)
            {
                var record = tableHourlyWagesPayslip.fnGetData(selectedRow[0]);
                view_pdf_payslip_template(record.Payslip_Id, record.Employee_Name, record.Template_Id, 'print',null,'salary payslip',"Hourly",domainName, orgCode);
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select Hourly Wages Payslip', type : 'info' });           
        }
    });
    
    //When the approveAndReleaseMonthly action is triggered payslip status should be updated as Nil Payment,Unpaid,Employee Owes
    $('#approveAndReleaseMonthly').on('click', function () {
        updatePaymentMessage (tableMonthlySalary,'Approve And Release','Monthly')
    });

    //When the paidMonthly action is triggered payslip status should be updated as Paid
    $('#paidMonthly').on('click', function () {
        updatePaymentMessage (tableMonthlySalary,'Paid','Monthly')
    });

    //When the salaryHoldMonthly action is triggered payslip status should be updated as Salary Hold
    $('#salaryHoldMonthly').on('click', function () {
        updatePaymentMessage (tableMonthlySalary,'Salary Hold','Monthly')
    });

    //When the revertSalaryWithholdMonthly action is triggered payslip status should be updated as Yet To Finalize
    $('#revertSalaryWithholdMonthly').on('click', function () {
        updatePaymentMessage (tableMonthlySalary,'Revert Salary Withhold','Monthly')
    });

    //When the revertPaidMonthly action is triggered payslip status should be updated as Unpaid
    $('#revertPaidMonthly').on('click', function () {
        updatePaymentMessage (tableMonthlySalary,'Revert Paid','Monthly')
    });

    //When the approveAndReleaseHourly action is triggered payslip status should be updated as Nil Payment,Unpaid,Employee Owes
    $('#approveAndReleaseHourly').on('click', function () {
        updatePaymentMessage (tableHourlyWagesPayslip,'Approve And Release','Hourly')
    });

    //When the paidHourly action is triggered payslip status should be updated as Paid
    $('#paidHourly').on('click', function () {
        updatePaymentMessage (tableHourlyWagesPayslip,'Paid','Hourly')
    });

    //When the salaryHoldHourly action is triggered payslip status should be updated as Salary Hold
    $('#salaryHoldHourly').on('click', function () {
        updatePaymentMessage (tableHourlyWagesPayslip,'Salary Hold','Hourly')
    });

    //When the revertSalaryWithholdHourly action is triggered payslip status should be updated as Yet To Finalize
    $('#revertSalaryWithholdHourly').on('click', function () {
        updatePaymentMessage (tableHourlyWagesPayslip,'Revert Salary Withhold','Hourly')
    });

    //When the revertPaidHourly action is triggered payslip status should be updated as Unpaid
    $('#revertPaidHourly').on('click', function () {
        updatePaymentMessage (tableHourlyWagesPayslip,'Revert Paid','Hourly')
    });
    
    function updatePaymentMessage (tableName,updatedPaymentStatus,payslipType) {
        var selectedRow = fnGetSelected (tableName);
        if (selectedRow.length)
        {
            var record = tableName.fnGetData(selectedRow[0]);
            if (record.Payslip_Id > 0)
            {
                $('#updatedPaymentStatus','#payslipType').val('');
                $('#paymentStatusMsg').empty();

                if(updatedPaymentStatus==='Approve And Release')
                {
                    $('#paymentStatusMsg').append('Approve and release will publish the payslip to the employee and update the status as either "Unpaid" or "Employee Owes" or "NIL Payment". Are you sure to action approve and release?');
                }
                else if(updatedPaymentStatus==='Paid')
                {
                    $('#paymentStatusMsg').append('Updating the status as '+"<b>"+"paid"+"</b>"+' will publish the payslip to the employee, and the payment details cannot be amended for this payslip. Are you sure to update the status as paid?');
                }
                else if(updatedPaymentStatus==='Revert Paid')
                {
                    $('#paymentStatusMsg').append('Revert payment will change the status of the payslip as '+"<b>"+"unpaid"+"</b>"+'. Are you sure to action revert payment?');
                }
                else if(updatedPaymentStatus==='Salary Hold')
                {
                    $('#paymentStatusMsg').append('Updating the status as withhold salary will limit access to the employee to view the payslip and cannot be paid out until the status changed to unpaid. Are you sure to update the status as withhold?');
                }
                else if(updatedPaymentStatus==='Revert Salary Withhold')
                {
                    $('#paymentStatusMsg').append('Revert salary withhold will change the status of the payslip as yet to finalize. Are you sure to revert salary withhold?');
                }
                $('#updatedPaymentStatus').val(updatedPaymentStatus);
                $('#payslipType').val(payslipType);
                
                $('#modalPaymentStatus').modal('toggle');
            }
        }
        else
        {
            jAlert({msg: 'Kindly select salary payslip record',type: 'info'});
        }
    }

    $('#paymentStatusYes').on('click', function () {
        var payslipType          = $('#payslipType').val();
        var updatedPaymentStatus = $('#updatedPaymentStatus').val();
        
        if(payslipType==='Monthly')
        {
            tableName = tableMonthlySalary;
        }
        else if(payslipType==='Hourly')
        {
            tableName = tableHourlyWagesPayslip;
        }
        
        var selectedRow    = fnGetSelected (tableName);
        var payslipIdArray = [];
        if (selectedRow.length)
        {
            for( i=0; i<selectedRow.length; i++)
            {
                var record = tableName.fnGetData (fnGetSelected(tableName)[i]);
                var currentPaymentStatus = record.Payment_Status;
                
                if(currentPaymentStatus==='Yet To Finalize' && updatedPaymentStatus==='Approve And Release')
                {
                    payslipIdArray.push(parseInt(record.Payslip_Id));
                }
                else if(currentPaymentStatus==='Yet To Finalize' && updatedPaymentStatus==='Salary Hold')
                {
                    payslipIdArray.push(parseInt(record.Payslip_Id));
                }
                else if(currentPaymentStatus==='Salary Hold' && updatedPaymentStatus==='Revert Salary Withhold')
                {
                    payslipIdArray.push(parseInt(record.Payslip_Id));
                }
                else if(currentPaymentStatus==='Unpaid' && updatedPaymentStatus==='Paid')
                {
                    payslipIdArray.push(parseInt(record.Payslip_Id));
                }
                else if(currentPaymentStatus==='Paid' && updatedPaymentStatus==='Revert Paid')
                {
                    payslipIdArray.push(parseInt(record.Payslip_Id));
                }
            }
            
            if (payslipIdArray.length > 0 && !isNaN(payslipIdArray.length))
            {
                var updateSalaryPayslipStatus =`mutation updateSalaryPayslipStatus($payslipId:[Int]!,$paymentStatus:String!,$payslipType:String!) { updateSalaryPayslipStatus(payslipId:$payslipId,paymentStatus:$paymentStatus,payslipType:$payslipType) { errorCode message } }`;
                let updateSalaryPayslipStatusVariables = {
                    payslipId     : payslipIdArray,
                    paymentStatus : updatedPaymentStatus,
                    payslipType   : payslipType
                }
                setMask('#wholepage');
                $.ajax({
                    method: 'POST',
                    url: getApiCustomDomains('payrollAdminWo'),
                    headers: getGraphqlAPIHeaders(),
                    data: JSON.stringify({
                        query: updateSalaryPayslipStatus,
                        variables : updateSalaryPayslipStatusVariables                            
                    }),
                    success: function(result) {
                        
                        if(result.data)
                        {
                            fnRefreshTable (tableName);
                            var msg = result.data.updateSalaryPayslipStatus.message;
                            jAlert ({ msg : msg, type : 'info' });
                        }
                        else
                        {
                            fnRefreshTable (tableName);
                            var error = result.errors[0].extensions;
                            var errorCode = error.code;
                        
                            switch (errorCode) {
                                case "BAD_USER_INPUT": // if any input validation error occurs, BAD_USER_INPUT was returned as error code from backend
                                case 'IVE0000':
                                jAlert({ msg: 'Something went wrong. Please contact system administrator.',type: 'warning'});
                                break;
                                case '_DB0105':
                                case 'PSP0005':    
                                case 'PSP0103':
                                    jAlert({ msg: result.errors[0].message,type: 'warning'});
                                    break;
                                default:
                                    jAlert({ msg: 'Something went wrong. Please contact system administrator.',type: 'warning'});
                                    break;
                            }
                        }
                        removeMask();
                    },
                    error: function(error){
                        if (error.status == 200) {
                            sessionExpired();
                        } 
                        else {
                            /* To handle internal server error */
                            jAlert({ msg: "Something went wrong. Please contact system admin", type: "warning" });
                        }
                        removeMask();
                    }
                });
            }
            else
            {
                removeMask();
                var infoMessage;
                if(updatedPaymentStatus==='Approve And Release')
                {
                    infoMessage = "Please select at least one record in yet to finalize status in order to update the payment status as approve and release";
                }
                else if(updatedPaymentStatus==='Salary Hold')
                {
                    infoMessage = "Please select at least one record in yet to finalize status in order to update the payment status as withhold";
                }
                else if(updatedPaymentStatus==='Revert Salary Withhold')
                {
                    infoMessage = "Please select at least one record in withhold status in order to revert salary withhold";
                }
                else if(updatedPaymentStatus==='Paid')
                {
                    infoMessage = "Please select at least one record in unpaid status in order to update the payment status as paid";
                }
                else if(updatedPaymentStatus==='Revert Paid')
                {
                    infoMessage = "Please select at least one record in paid status in order to update the payment status as revert paid";
                }
                jAlert ({ msg :infoMessage, type : 'info' });
            }
        }
        else
        {
            removeMask();
            jAlert ({ msg : 'Kindly select Monthly Salary Payslip', type : 'info' });
        }
    });
  
    /** Close Function For All the Forms **/ 
    function fnFormCloseHourlyWagesPayslip (hideAction) {
        var uniqueId = $('#formHourlyWagesPayslipId').val();
        
        if (uniqueId > 0 && !isNaN(uniqueId))
        {
            clearLock ({
                'formName' : 'Hourly Wages Payslip',
                'uniqueId' : uniqueId,
                'callback' : function ()
                {
                    $('#formHourlyWagesPayslipId').val(0);
                    
                    if (hideAction)
                        $('#modalFormHourlyWagesPayslip').modal('hide');
                }
            });
        }
        else
        {
            if (hideAction)
                $('#modalFormHourlyWages').modal('hide');
        }
        
        tableHourlyWagesPayslip.fnReloadAjax( pageUrl() +'payroll/salary-payslip/hourly-wages-payslip' );
        //fnRefreshTable (tableHourlyWagesPayslip);
    }
    
    /**
     *  Prefill Hourly Wages Payslip form values in add, edit, reset events
    */
    function fnPreFillValuesFormHourlyWagesPayslip (record) {        
        
        $('#s2id_hourlyPayslipType').select2('val','HourlyWages');
        
        $('#salaryMonthHourly').val('');
        
        $('#s2id_formHourlyLocation,#s2id_formHourlyDepartment').select2('val', '');
    }   
   
    /** Function To Show And Hide Action Buttons  For All the Forms **/ 
    function fnHourlyPayslipActionButtons (action) {        
        var selectedRowLength = fnGetSelected(tableHourlyWagesPayslip).length;
        
        if (selectedRowLength == 0)
        {
            $('#hourlyCheckAll').html('<i class="hr-check-all"></i><span class="hidden-xs hidden-sm">Check All</span>');
            
            fnGridButtons ($('#viewHourlyWagesPayslip, #exportHourlyPrint, #exportHourlyPdf, #deleteHourlyWagesPayslip, #formHourlycommunicateEmployee,#buttonPaymentStatusHourly'), false);
        }
        else if(selectedRowLength == 1)
        {   
            $('#hourlyCheckAll').html('<i class="hr-check-all"></i><span class="hidden-xs hidden-sm">Check All</span>');

            var record        = tableHourlyWagesPayslip.fnGetData (fnGetSelected(tableHourlyWagesPayslip)[0]);
            var paymentStatus = record.Payment_Status;
            if(paymentStatus==='Paid' || paymentStatus==='Employee Owes' || paymentStatus==='Nil Payment')
            {
                fnGridButtons ($('#formHourlycommunicateEmployee'), true);
            }
            else
            {
                fnGridButtons ($('#formHourlycommunicateEmployee'), false);
            }
            
            fnGridButtons ($('#viewHourlyWagesPayslip, #exportHourlyPrint, #exportHourlyPdf, #deleteHourlyWagesPayslip,#buttonPaymentStatusHourly'), true);
            $('#viewContextHourlyWagesPayslip, #printContextHourlyWagesPayslip, #pdfContextHourlyWagesPayslip, #deleteContextHourlyWagesPayslip').parent().show();
            $('#buttonPaymentStatusHourly').removeClass().addClass('btn-group');
        }
        else
        {
            var datatableMenuLength = tableHourlyWagesPayslip.fnGetData().length;
            
            if (datatableMenuLength == selectedRowLength)
            {
                $('#hourlyCheckAll').html('<i class="hr-check-all"></i><span class="hidden-xs hidden-sm">Uncheck All</span>');
            }
            else
            {
                $('#hourlyCheckAll').html('<i class="hr-check-all"></i><span class="hidden-xs hidden-sm">Check All</span>');
            }
            
            fnGridButtons ($('#viewHourlyWagesPayslip'), false);
            $('#viewContextHourlyWagesPayslip').parent().hide();
            
            fnGridButtons ($('#exportHourlyPrint, #exportHourlyPdf'), false);
            //When user selects checkall option need to show "Notify In Mail"
            fnGridButtons ($('#deleteHourlyWagesPayslip,#formHourlycommunicateEmployee,#buttonPaymentStatusHourly'), true);
            $('#printContextHourlyWagesPayslip, #pdfContextHourlyWagesPayslip').parent().hide();
            $('#deleteContextHourlyWagesPayslip').parent().show();
            $('#buttonPaymentStatusHourly').removeClass().addClass('btn-group');
        }
    }
    
    
    function fnShowHiddenPayslipDetails ( nTr ) {
            var record = payslipSubGrid.fnGetData( nTr );
                  sOut = ''
            sOut +=  '<div class="row hidden-md hidden-lg" style="margin: 5px 0px;">'+
                            '<div class="col-xs-3">Basic Pay</div>'+
                            '<div class="col-xs-1"> : </div>'+
                            '<div class="col-xs-8">'+ fnCheckNull (record.Basic_Salary) +'</div>'+
                        '</div>';
            sOut +=  '<div class="row hidden-md hidden-lg" style="margin: 5px 0px;">'+
                        '<div class="col-xs-3">Total Salary</div>'+
                        '<div class="col-xs-1"> : </div>'+
                        '<div class="col-xs-8">'+ fnCheckNull (record.Total_Salary) +'</div>'+
                     '</div>';
        
            return sOut;
        }
        
        function fnShowHiddenHourlyPayslipDetails ( nTr ) {
            var record = hourlyPayslipSubGrid.fnGetData( nTr );
                sOut = ''
                aData = '';
            
            var payslipType = $('#s2id_hourlyPayslipType').select2('val');
            $.ajax ({
                    type     : 'POST',
                    dataType : 'json',
                    async    : false,
                    url      : pageUrl()+'payroll/salary-payslip/payslip-detailed-view/param/'+payslipType+'/payslipId/'+ record.Payslip_Id,
                    success  : function (result)
                    {
                        aData = result;
                    }
            });
            
            sOut +=  '<div class="row hidden-md hidden-lg" style="margin: 5px 0px;">'+
                            '<div class="col-xs-3">Basic Pay</div>'+
                            '<div class="col-xs-1"> : </div>'+
                            '<div class="col-xs-8">'+ fnCheckNull (aData.Salary['Total_Salary']) +'</div>'+
                        '</div>';
            
            for(var i=0;i<(aData.Incentive).length;i++)
            {
                if(aData.Incentive)
                {
                    if (aData.Incentive[i]['Incentive_Name']!=undefined)
                    {
                        if (aData.Incentive[i]['Description'])
                        {
                            sOut +=  '<div class="row" style="margin: 5px 0px;">'+
                                        '<div class="col-xs-3">'+aData.Incentive[i]['Incentive_Name']+' - '+aData.Incentive[i]['Description']+'</div>'+
                                        '<div class="col-xs-1"> : </div>'+
                                        '<div class="col-xs-8">'+ fnCheckNull (aData.Incentive[i]['Incentive_Amount']) +'</div>'+
                                    '</div>';
                        }
                        else
                        {
                            sOut +=  '<div class="row" style="margin: 5px 0px;">'+
                                        '<div class="col-xs-3">'+aData.Incentive[i]['Incentive_Name']+'</div>'+
                                        '<div class="col-xs-1"> : </div>'+
                                        '<div class="col-xs-8">'+ fnCheckNull (aData.Incentive[i]['Incentive_Amount']) +'</div>'+
                                    '</div>';
                        }
                    }
                }
            }
            
            for(var i=0;i< (aData.Deduction).length;i++)
            {
                if (aData.Deduction)
                {
                    if(aData.Deduction[i]['Deduction_Name']!=undefined)
                    { 
                        if(aData.Deduction[i]['Description'])
                        {
                           sOut +=  '<div class="row hidden-md hidden-lg" style="margin: 5px 0px;">'+
                                        '<div class="col-xs-3">'+aData.Deduction[i]['Deduction_Name']+' - '+aData.Deduction[i]['Description']+'</div>'+
                                        '<div class="col-xs-1"> : </div>'+
                                        '<div class="col-xs-8">'+ fnCheckNull (aData.Deduction[i]['Deduction_Amount']) +'</div>'+
                                    '</div>';
                        }
                        else
                        {
                            sOut +=  '<div class="row hidden-md hidden-lg" style="margin: 5px 0px;">'+
                                        '<div class="col-xs-3">'+aData.Deduction[i]['Deduction_Name']+'</div>'+
                                        '<div class="col-xs-1"> : </div>'+
                                        '<div class="col-xs-8">'+ fnCheckNull (aData.Deduction[i]['Deduction_Amount']) +'</div>'+
                                    '</div>';
                        }
                    }
                }
            }
   
            sOut +=  '<div class="row hidden-md hidden-lg" style="margin: 5px 0px;">'+
                        '<div class="col-xs-3">Total Salary</div>'+
                        '<div class="col-xs-1"> : </div>'+
                        '<div class="col-xs-8">'+ fnCheckNull (aData.Salary['Total_Salary']) +'</div>'+
                    '</div>';
        
            return sOut;
        }

        //Function to create payslip pdf and store in s3
        function createPayslipPdf(payslipType,payslipIdArray,orgCode,jobType,empId,formId,templateId){
            setMask('#wholepage');
            var queryNotifyInEmail = `query initiateStepFunction($employeeId: Int! $formId:Int! $jobType:String! $payslipType:String! 
                $payslipId:[Int] $templateId:Int! $requestId:Int!){ initiateStepFunction(employeeId:$employeeId formId:$formId jobType:$jobType  
                payslipType:$payslipType payslipId:$payslipId templateId:$templateId requestId:$requestId) {errorCode message}}`;
            
            var bulkProcessingURL = 'https://api.'+fnIsDomain()+'/bulkProcessing/graphql';
        
            var variables = { "employeeId"      : empId,
                                "formId"        : formId,
                                "jobType"       : jobType,
                                "payslipType"   : payslipType,
                                "payslipId"     : payslipIdArray,
                                "requestId"     : 0, //requestId need to be passed for retrying the same request otherwise it is 0
                                "templateId"    : templateId
                            };
            $.ajax({
                method: 'POST',
                url: bulkProcessingURL,
                headers: getGraphqlAPIHeaders(),
                data: JSON.stringify({
                    query: queryNotifyInEmail,
                    variables: variables
                }),
            
                success: function(result) { 
                    if (!result.data && jobType=="Email") {
                        if(result.errors && result.errors[0]){
                            /**failure response 
                             * As of now we are presenting the same message for all the users, once we finalized the messages
                             *  we can use the error message receiving from the backend.
                            */
                            jAlert ({ msg : 'Sorry, Could not initiate the email notification. Please try after some time.', type : 'warning' });
                        } else {
                            jAlert ({ msg : 'Sorry, Could not initiate the email notification. Please try after some time.', type : 'warning' });
                        }
                    } else {
                        if(jobType=="Email")
                        {
                            jAlert ({ msg : 'Email notification initiated successfully.', type : 'success' });
                        }
                    }
                    removeMask();
                }, error: function (error) {
                    removeMask();
                    if(jobType=="Email")
                    {
                        jAlert ({ msg : 'Sorry, Could not initiate the email notification. Please try after some time.', type : 'warning' });
                    }
                } 
            });
        }

    function checkPayslipTemplateExist()
    {
        if ($('#fieldForce').val() == 1) {
            var serviceProviderId = parseInt($('#s2id_monthlyServiceProvider').select2('val'), 10);
            var serviceProviderName = $('#s2id_monthlyServiceProvider').text().trim();
        }
        else {
            var serviceProviderId = 0;
            var serviceProviderName ='';
        }
       
        //to check payslip template exist or not
        fnGetDefaultPayslipTemplateList('If you want to initiate monthly salary payslip you have to create a payslip template', 'Monthly', serviceProviderId, serviceProviderName,function (result) {
            if (result !== 'error') {
                if (result === 'payslipTemplateExist') {
                    listPayslipEmployees(1);
                }
                else {
                    $('#custom-loading-payslip').prop('style', 'display:none');
                    // if not exist, show preview popup
                    initiatedPayslipType = 'Monthly';
                    $('#modalPayslipTemplatePreview').modal('toggle');
                }
            }
            else
            {
                $('#custom-loading-payslip').prop('style', 'display:none');
            }
        });
    }    
       
    // Payslip Review Status click event
    $('#updatePayslipReviewStatus, #updateContextPayslipReviewStatus').on('click', function () {
        $('#custom-loading-payslip').prop('style','display:block');//set the mask
        var selectedRow = fnGetSelected (tableMonthlySalary );
        var payslipIdArray = [];
        
        if (selectedRow.length)
        {
            for( i=0; i<selectedRow.length; i++)
            {
                payslipIdArray.push(tableMonthlySalary.fnGetData (fnGetSelected(tableMonthlySalary)[i]).Payslip_Id);
            }
            
            if (payslipIdArray.length > 0 && !isNaN(payslipIdArray.length)) {
                resetReviewStatusApproval();
                $('#modalPayslipReviewStatus').modal('toggle');
                $('#custom-loading-payslip').prop('style','display:none');//remove the mask
            }else {
                $('#custom-loading-payslip').prop('style','display:none');//remove the mask
                jAlert ({ msg : 'Kindly select Monthly Salary Payslip', type : 'info' });
            }
        }else{
            $('#custom-loading-payslip').prop('style','display:none');//remove the mask
            jAlert ({ msg : 'Kindly select Monthly Salary Payslip', type : 'info' });
        }
    });

    $('#payslipReviewStatusForm').on('change', function () {
        reviewFormisDirty = true;
    });

    // Status Approval reset
    $('#formResetReviewStatus').on('click', function () {
        var l = Ladda.create(this);

        l.start();

        resetReviewStatusApproval();

        l.stop();
    });

    // Status Approval submit
    $('#formSubmitPayslipReview').on('click', function () {
        setMask('#wholepage');
        var selectedRow = fnGetSelected (tableMonthlySalary );
        var payslipIdArray = [];
        
        if (selectedRow.length)
        {
            for( i=0; i<selectedRow.length; i++)
            {
                payslipIdArray.push(parseInt(tableMonthlySalary.fnGetData (fnGetSelected(tableMonthlySalary)[i]).Payslip_Id));
            }
            
            if (payslipIdArray.length > 0 && !isNaN(payslipIdArray.length)) {
                var l = Ladda.create(this);
                l.start();

                $('#s2id_payslipReviewStatus').removeClass('form-error');

                if ($("#payslipReviewStatusForm").valid()) {
                    let updateReviewStatusQuery = `
                        mutation updatePayslipReviewStatus($payslipId: [Int]!,$payslipReviewStatus:String!) {
                            updatePayslipReviewStatus(payslipId: $payslipId, payslipReviewStatus: $payslipReviewStatus) {
                                errorCode
                                message
                            }
                        }
                    `;

                    let payrollAdminVariables = { payslipId: payslipIdArray, payslipReviewStatus: $('#s2id_payslipReviewStatus').select2('val') };
                   
                    $.ajax({
                    method: 'POST',
                    url: getApiCustomDomains('payrollAdminWo'),
                    headers: getGraphqlAPIHeaders(),
                    data: JSON.stringify({
                        query: updateReviewStatusQuery,
                        variables: payrollAdminVariables
                    }),
                    success: function(result) {
                        if (result.data !== null) {
                            reviewFormisDirty = false;
                            removeMask();
                            $('#modalPayslipReviewStatus').modal('toggle');
                            fnRefreshTable(tableMonthlySalary);
                            jAlert ({ msg : 'Salary payslip review status updated successfully.', type : 'info' });
                        } else {
                            let errorCode = "", reviewStatusUpdateErrorMessage = "";
                            if(result && result.errors && result.errors.length > 0) {
                                errorCode = result.errors[0].extensions.code;
                                reviewStatusUpdateErrorMessage = result.errors[0].message
                            }
                            
                            removeMask();
                            $(window).scrollTop(0);
                            switch (errorCode) {
                                case 'PSP0104'://Sorry, an error occurred while updating the payslip review status. Please contact the platform administrator.
                                case 'PSP0008'://'No payslip records are available for updating the review status.
                                case 'PSP0105'://Payslip review cannot proceed as the review process is not enabled.
                                case '_DB0105'://This employee does not have an optional choice access rights.
                                case 'PSP0007'://Oops! Something went wrong while updating salary payslip review status, please contact the platform administrator.
                                    jAlert({
                                        panel: $('#payslipReviewStatusForm'),
                                        msg: reviewStatusUpdateErrorMessage,
                                        type: 'warning'
                                    });
                                    break;
                                case "BAD_USER_INPUT":
                                    var validationErrors = result.errors[0].extensions.validationError.validationError;
                                    let validationErrorMessage;
                                    if (validationErrors) {
                                        for (var errCode in validationErrors) {
                                            if( errCode === "IVE0531") {
                                                // message: "Payslip review status can be either Review Completed or Rejected"
                                                validationErrorMessage = validationErrors[errCode];
                                            }else{
                                                //_EC0007 - Invalid input field(s).
                                                validationErrorMessage = 'Sorry! The update process could not be processed due to invalid input. Please contact your platform administrator.'
                                            }
                                        }
                                    } else {
                                        validationErrorMessage =
                                        "Something went wrong. If you continue to see this issue, please contact the platform administrator.";
                                    }
                                    jAlert({
                                        panel: $('#payslipReviewStatusForm'),
                                        msg: validationErrorMessage,
                                        type: 'warning'
                                    });
                                    break;
                                default:
                                    jAlert({
                                        panel: $('#payslipReviewStatusForm'),
                                        msg: 'Sorry, an error occurred while updating the payslip review status. Please contact the platform administrator.',
                                        type: 'warning'
                                    });
                                    break;
                            }
                        }
                    },
                    error: function() {
                        $(window).scrollTop(0);
                        removeMask();
                        jAlert({
                            panel: $('#payslipReviewStatusForm'),
                            msg: 'There seems to be some technical issues. Please try after sometime.',
                            type: 'info'
                        });
                    }
                    });
                }else{
                    l.stop();
                    $(window).scrollTop(0);
                    removeMask();
                    jAlert ({  panel: $('#payslipReviewStatusForm'),  msg : 'Kindly select review status', type : 'info' });
                }
            }else {
                l.stop();
                $(window).scrollTop(0);
                removeMask();
                jAlert ({  panel: $('#payslipReviewStatusForm'),  msg : 'Kindly select Monthly Salary Payslip', type : 'info' });
            }
        }
        else {
            l.stop();
            $(window).scrollTop(0);
            removeMask();
            jAlert ({  panel: $('#payslipReviewStatusForm'),  msg : 'Kindly select Monthly Salary Payslip', type : 'info' });
        }
    });

    //Reset status update form
    function resetReviewStatusApproval() {
        $('#s2id_payslipReviewStatus').select2('val', '');

        $('#s2id_payslipReviewStatus').removeClass('form-error');

        $('#payslipReviewStatusForm').validate().resetForm();
        reviewFormisDirty = false;
    }
    /**
     *  click to close add/edit modal
    */
    $('#editCloseConfirmPayslipReview').on('click', function () {
        reviewFormisDirty = false;
        reviewStatusFormClose();
    });

    /**
     *  Click to close add/edit modal form back button
    */
    $('#modalPayslipReviewFormClose').on('click', function () {
        //If form is dirty show confirmation to close status update form
        if (reviewFormisDirty) {
            $('#modalPayslipReviewStatus').modal('toggle');
        }
        //If form isn't dirty then close it
        else {        
            reviewStatusFormClose();
        }
    });

    //Review Status form modal hide event
    $('#modalPayslipReviewStatus').on('hide.bs.modal', function (e) {
        if (reviewFormisDirty) {
            e.preventDefault();
            e.stopImmediatePropagation();

            $('#modalDirtyPayslipReviewStatus').modal('toggle');
        }
    });

    $('#reviewAndReconcileRedirect').on('click', function () {
        window.open(pageUrl()+"v3/payroll/payroll-reconciliation",'_blank');
    });

    function reviewStatusFormClose() {
        $('#modalPayslipReviewStatus').modal('hide');
    }
    //On second modal close, first modal scroll will not work. To avoid that,using this
    $('#dirtyHourlyWagesPayslip,#dirtyMonthlySalary,#modalDirtyStatusMonthlySalary,#dirtyPayslipPreRequisite,#sessionExipredModel,#modalFormHourlyWages,#modalDirtyPayslipReviewStatus').on('hidden.bs.modal', function () {
        if ($('#modalFormMonthlySalary').is(':visible') || $('#modalStatusMonthlySalary').is(':visible') || $('#modalFormHourlyWages').is(':visible') || $('#modalPayslipTemplatePreview').is(':visible')
        || $('#modalPayslipReviewStatus').is(':visible'))
            $('body').addClass('modal-open');
    });

    // check if the full and final settlement tab is presented on the screen, if yes the we need to apply some classes to adjust it for tab view
    if ($('#fullFinalSettlementTab').length) {
        $('.page-content').addClass('custom-tab');
        $('.add-panel-padding').addClass('padding-class');
    }
    // to check tab is clicked
    var tabClicked = false;
    var subTabClicked = false;
        
    // when the full and final settlement tab is hovered we need to highlight the tab
    $('#fullFinalSettlementTab').on('mouseenter', function () {
        fnTabHighlight();
    });
    // tab highlight function
    function fnTabHighlight(){
        $('#salaryPayslipFormTab').removeClass('tab-active-text text-secondary');
        $('#salaryPayslipTab').removeClass('tab-border-cls');
        $('#fullFinalSettlementFormTab').addClass('tab-active-text text-secondary');
        $('#fullFinalSettlementTab').addClass('tab-border-cls')
        $('#formTabLink').removeClass('tab-a-tag-color');
        $('#formTabLink').addClass('text-secondary');
    };
    $('#biMonthlyTab').on('mouseenter', function () {
        fnSubTabHighlight();
    });
    function fnSubTabHighlight() {
        $('#monthlyFormTab').removeClass('tab-active-text text-secondary');
        $('#monthlyTab').removeClass('tab-border-cls');
        $('#bimonthlyFormTab').addClass('tab-active-text text-secondary');
        $('#biMonthlyTab').addClass('tab-border-cls');
        $('#subFormTabLink').removeClass('tab-a-tag-color');
        $('#subFormTabLink').addClass('text-secondary');
    }
    // when mouse is out of full and final settlement tab we need to remove highlight property
    $('#fullFinalSettlementTab').on('mouseleave', function () {
        // to check tab is cliked. If yes we don't remove tab active class
        if(!tabClicked){
            $("#salaryPayslipFormTab").addClass('tab-active-text text-secondary');
            $('#salaryPayslipTab').addClass('tab-border-cls');
            $('#fullFinalSettlementFormTab').removeClass('tab-active-text text-secondary');
            $('#fullFinalSettlementTab').removeClass('tab-border-cls');
            $('#formTabLink').addClass('tab-a-tag-color');
            $('#formTabLink').removeClass('text-secondary');
        }
    });
    $('#biMonthlyTab').on('mouseleave', function () {
        // to check tab is cliked. If yes we don't remove tab active class
        if(!subTabClicked){
            $("#monthlyFormTab").addClass('tab-active-text text-secondary');
            $('#monthlyTab').addClass('tab-border-cls');
            $('#bimonthlyFormTab').removeClass('tab-active-text text-secondary');
            $('#biMonthlyTab').removeClass('tab-border-cls');
            $('#subFormTabLink').addClass('tab-a-tag-color');
            $('#subFormTabLink').removeClass('text-secondary');
        }
    });

    // full and final settlement tab onclick function
    $('#fullFinalSettlementTab').on('click', function () {
        tabClicked = true;
        fnTabHighlight();
        // redirect to the full and final settlement form
        window.location.href = pageUrl() + "v3/payroll/full-and-final-settlement";
    });
    $('#biMonthlyTab').on('click', function () {
        subTabClicked = true;
        fnSubTabHighlight();
        // redirect to the bi-monthly form
        window.location.href = pageUrl() + "v3/payroll/salary-payslip/bi-monthly";
    });
});



