<!DOCTYPE html>
<html>

<head>
	<!-- common scripts -->
	<script src="https://cdn.jsdelivr.net/npm/vue@2/dist/vue.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/vuetify@2.2.3/dist/vuetify.min.js"></script>
    <!-- plugin styles -->
	<link href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900"
		rel="stylesheet" />
	<link href="https://cdn.jsdelivr.net/npm/@mdi/font@4.x/css/materialdesignicons.min.css"
		rel="stylesheet" />
	<link href="https://cdn.jsdelivr.net/npm/vuetify@2.2.3/dist/vuetify.min.css" rel="stylesheet" />
	<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.12.1/css/all.min.css"
		rel="stylesheet" />
	<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
	<!--  styles -->
	<style scoped lang="css">
		.active_tab_bg {
			background:#f9f9f9 !important;
		}
		.custom-top-bar {
			height: 5em;
			position: fixed !important;
			width: 100% !important;
			z-index : 2000;
			top : 50px;
		}
		.action-header{
			display: flex;
            flex-direction: row-reverse;
			margin-top: 2em !important;
		}
		.list-card-view-row {
			padding: 3.5em !important;
			padding-top: 0px !important;
		}
		.list-card-col-view {
			padding: 2.5em !important;
            padding-top: 1.5em !important;
		}
		.add-icon{
            font-weight: bold !important;
            font-size: 1.8em !important;
		}	
		.tooltip .tooltip-inner{
			max-width:220px !important;
		}
		@media screen and (max-width:960px)  {
		.list-card-view-row {
			padding: 0em !important;
			margin-top: 1em !important;
		}
		}
	</style>
</head>

<body>
	<script>	
		// base url
		function base_url() {
			var pathParts = location.pathname.split('/');
			if (localStorage.getItem('production') == 0) {
				var url = location.origin + '/' + pathParts[1].trim('/') + '/'; // http://localhost/hrapponline/
			} else {
				var url = location.origin + '/'; // http://subdomain.hrapp.co
			}
			return url;
		}
		var base_url_final = base_url();
		addScript(base_url_final);

		function addScript(src, callback) {
			var s = document.createElement('base');
			s.setAttribute('src', src);
			s.onload = callback;
			document.head.appendChild(s);
		}
	</script>
	<!--  html content -->
	<div id="ipWhitelist" class="col-md-12 portlets p-10">
		<template>
			<v-app class="bg_grey_lighten1">
					<!--  Topbar design -->
					<v-card class="bg_grey_lighten1" class="custom-top-bar" flat height="4em">
						<v-toolbar color="grey lighten-5">
							<span class="padding-2em"></span>
							<v-tabs v-model="currentTab" background-color="#f9f9f9">
									<v-tab href="#tab-1" class="active_tab_bg" >
										<div style="text-transform: capitalize !important;" class="font-weight-bold">IP Whitelisting</div>
									</v-tab>
							</v-tabs>
						</v-toolbar> 
					</v-card>
					 <div v-if="!isAccessDenied && !whiteListedIpFetchError">
						<!-- Whitlisted Ip List exist -->
						<div v-if="ipWhitelistedArray.length >= 1">
							<v-row class="action-header">
								<v-col xl="2" lg="2" md="3" sm="12" cols="12" class="d-flex justify-center">
									<v-btn class="m-t-5" rounded color="primary" @click="addButtonClick" dark>
										<v-icon class="add-icon">
											add
										</v-icon>Add IP Address
									</v-btn>
								</v-col>
							</v-row>
							<v-row class="list-card-view-row">
								<v-col
									v-for="(Whitelisted, index) in ipWhitelistedArray"
									:key="index"
									xl="3" lg="3" md="4" sm="6" xs="12" cols="12"
									class="list-card-col-view"
								>
									<ip-address-view-card :index="index" :edit-rights="canEdit" :delete-rights="canDelete" :ip-whitelisted-list="ipWhitelistedArray[index]"  @delete="deleteWhitelistedIPLocation" @update-ip-whitelisted="editWhitelistedIps"></ip-addres-view-card>
								</v-col>
							</v-row>
						</div>
						<div v-else>
							<!--Initial no record-->
							<no-record-initial-screen v-show="!loadingScreen" button-text="Add IP address" :add-rights="canAdd" :content="noRecordContent" 
							:main-title="noRecordMainTitle" image-name="ipwhitelisted_newuser_image" 
							@button-click="addButtonClick"></no-record-initial-screen>
						</div>
						<!--Add Whitelist IP-->
						<div v-if="showAddIpwhitelistingForm">
							<add-ip-whitlisting-form @close-add-ip-form="showAddIpwhitelistingForm=false" @handle-error="handleError" @enable-loading="setLoading" @add-success="fnAddIpSuccess" @close-confirm="openConfirmModal" @no-location-available="noLocationAvailabe" :current-ip="currentLocationIP" :api-headers="apiHeaders" :whitelisted-ip="allWhitelistedIps" :list-whitelisted-ip-detail="ipWhitelistedArray"></add-ip-whitlisting-form>
						</div>
						<!--Update Whitelist IP-->
						<edit-ip-whitlisting-form v-if="updateIpwhitelistedForm" @close-edit-ip-form="closeEditForm" @handle-error="handleUpdateError" @enable-loading="setLoading" @edit-success="fnUpdateIpSuccess"  @close-confirm="editFormCloseConfirm" @no-change="noChangeAlert" :current-ip="currentLocationIP" :api-headers="apiHeaders" :whitelisted-ip="allWhitelistedIps" :list-whitelisted-ip-detail="ipWhitelistedArray" :whitelisted-data="selectedLocationIPs"></edit-ip-whitlisting-form>
						
						<confirmation-model :open-confirmation-modal="openCloseConfirmation" content="Your changes won't be saved.Are you sure you want to close this form?" @confirm="closeConfirmationModel" @cancel="openCloseConfirmation = false"></confirmation-model>
						<!-- Delete Confirmation modal -->
						<custom-warning-modal :open-warning-model="openDeleteConfirmation" :image-path="deleteImage" :base-path="basePath" :modal-title="deleteModelTitle" :content="deleteModelContent"
							@close-warning-modal="closeDeleteModel" @on-button-click="deleteWhitelistedLocation">
						</custom-warning-modal>
						<custom-warning-modal :open-warning-model="openLocationNoAvailableModel" button-text="Locations" image-path="location-map" :base-path="basePath" modal-title="No locations to whitelist IP address" content="You have covered all available locations to whitelist the IP addresses. Either you can whitelist the IP address in the existing location or add a new “location” by clicking below button, to whitelist new set of IP addresses."
							@close-warning-modal="openLocationNoAvailableModel = false" @on-button-click="redirectToLocation">
						</custom-warning-modal>
					</div>
					<!--Access Denied-->
					<access-denied-screen v-if="isAccessDenied"></access-denied-screen> 
					<!--Fetching Error-->
					<div v-if="whiteListedIpFetchError">
						<fetching-error-screen button-text="Retry" :content="errorContent" 
						:main-title="errorTitle" image-name="initial-fetch-error-image" 
						@button-click="retryFetching"></fetching-error-screen>
					</div>
				<!-- snack bars for handle success and error messages -->
				<v-snackbar v-model="snackbar" :color="snackbarColor">
					{{ snackBarMsg }}
					<v-btn color="white" text @click="snackbar = false">
						X
					</v-btn>
				</v-snackbar>
			    <!-- Loading -->
				<custom-loading-screen v-if="loadingScreen"></custom-loading-screen>
			</v-app>
		</template>
	</div>

   
	<script src="https://cdn.jsdelivr.net/gh/f/graphql.js@master/graphql.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/vue-cookies@1.6.1/vue-cookies.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/v-tooltip@2.0.2"></script>

	<script>
		let primaryColor, secondaryColor;
		if (!localStorage.getItem("brand_color")) {
        	const { Primary_Color, Secondary_Color} = JSON.parse(localStorage.getItem("brand_color"));
			primaryColor = Primary_Color;
			secondaryColor = Secondary_Color;
		} else {
			primaryColor = '#260029';
			secondaryColor = '#ec407a';
		}
		// vue instance declarations
		var app = new Vue({
			el: '#ipWhitelist',
			vuetify: new Vuetify(
				{
					theme: {
						options: {
							customProperties: true,
						},
						themes: {
							light: {
								primary: primaryColor,
								secondary: secondaryColor,
								grey: '#9E9E9E',
								green : '#41ae57',
								blue : '#63759f'
							}
						}
					}
				}
			),
			data() {
				return {
					hrappBeBaseURL : 'https://api.'+localStorage.getItem('domain')+'/hrappBe/graphql',
                    atsBaseURL : 'https://api.'+localStorage.getItem('domain')+'/ats/graphql',
					partnerid: $cookies.get("partnerid"),
					dCode: $cookies.get("d_code"),
					bCode: $cookies.get("b_code"),
					employeeId: parseInt(localStorage.getItem('LoginEmpId'), 10),
					loadingScreen:false,
					currentTab:"tab-1",
					currentLocationIP:'',
					//no initial record props
					noRecordContent:'IP restriction will enable you to provide application access from a known source, either within your organization or from a known partner or user IP addresses.',
					noRecordMainTitle:'Enable IP restriction within your organization network or individual IP Addresses',
					
					fetchErrorAccessRights:false,
					//fetching error screen props
					errorTitle:'',
					errorContent:'',
					whiteListedIpFetchError:false,
					//delete modal props
					deleteImage:'',
					deleteModelTitle:'',
					deleteModelContent:'',
					
					ipAddressResctionflag:0,
					showAddIpwhitelistingForm:false,
					updateIpwhitelistedForm:false,
					
					allWhitelistedIps:[],
					ipWhitelistedArray:[],
					
					selectedLocationIPs:'',
					//rights
					canView:0,
					canEdit:0,
					canDelete:0,
					canAdd:0,
					isAccessDenied:false,
					// delete confirmation
					openDeleteConfirmation : false,
					deleteLocationId:'',
					openCloseConfirmation:false,
					openLocationNoAvailableModel:false,
					//snackbar props
					snackbar :false,
					snackbarColor:'',
					snackBarMsg :'',
				    
				}
			},
			computed: {
				isLocalEnv() {
					let currentUrl = window.location.href;
					if (
						parseInt(localStorage.getItem("isProduction"), 10) === 0 ||
						currentUrl.includes("hrapponline")
					) {
						return true;
					} else {
						return false;
					}
				},
				//to get orgCode dynamically from the current url
				orgCode() {
					if (this.isLocalEnv) {
						return "happy"; // local db connection
					} else {
						let oCode1 = localStorage.getItem("orgCode");
						if (oCode1) {
							return oCode1;
						} else {
							let url = window.location.href;
							let urlNoProtocol = url.replace(/^https?:\/\//i, "");
							let oCode = urlNoProtocol.split(".");
							oCode = oCode[0];
							return oCode;
						}
					}
				},
				basePath() {
					if (localStorage.getItem('production') == 0) {
						return '/hrapponline/'
					} else {
						return '/'
					}
				},
				apiHeaders() {
					let authorizationHeader = $cookies.get("accessToken") ? $cookies.get("accessToken") : null;
					let refreshTokenHeader = $cookies.get("refreshToken") ? $cookies.get("refreshToken") : null;
					return {
						'Content-Type': 'application/json',
						org_code: this.orgCode,
						Authorization: authorizationHeader,
						refresh_token: refreshTokenHeader,
						user_ip: this.currentLocationIP,
						partnerid: this.partnerid ? this.partnerid : "-",
						additional_headers: JSON.stringify(
							{
								org_code: this.orgCode,
								Authorization: authorizationHeader,
								refresh_token: refreshTokenHeader,
								user_ip: this.ipAddress,
								partnerid: this.partnerid ? this.partnerid : "-",
								d_code: this.dCode,
								b_code: this.bCode,
							}
						)
					}						
				},
			},
			
			methods:{
				editFormCloseConfirm(){
                    this.openCloseConfirmation = true;
				},
				noChangeAlert(){
					this.snackbar =true;
					this.snackbarColor = "orange lighten-1"
					this.snackBarMsg ="Form has no changes.";
				},
				handleUpdateError(err){
					var self = this
                    clearLockAPI('IP Whitelisting',self.selectedLocationIPs.Location_Id,self.employeeId,self.orgCode,self.atsBaseURL,function(error, data)
					{
						if(data) {
							self.handleError(err);
						}
						else{
							self.updateIpwhitelistedForm = false;
							self.handleError(error);
						}
					})
				},
				noLocationAvailabe(){
					 this.loadingScreen = false;
					 this.showAddIpwhitelistingForm=false;
					 this.openLocationNoAvailableModel = true
				},
				redirectToLocation(){
				   this.openLocationNoAvailableModel = false
				   window.open(base_url_final + "organization/locations");
				},
				openConfirmModal(){
					this.openCloseConfirmation =true
				},
				closeConfirmationModel(){
					this.showAddIpwhitelistingForm=false;
					this.openCloseConfirmation =false;
				},
				addButtonClick(){
					if(this.canAdd){
						this.showAddIpwhitelistingForm = true
					}
					else{
						this.snackbar = true;
						this.snackbarColor = "orange lighten-1";
						this.snackBarMsg = "Sorry, You don't have access rights to whitelist IP address. Please contact system administrator."
							
					}
				},
				closeDeleteModel(){
					var self = this;
					clearLockAPI('IP Whitelisting',self.deleteLocationId,self.employeeId,self.orgCode,self.atsBaseURL,function(error, data)
					{
						if(data) {
							self.openDeleteConfirmation = false;
						}
						else{
							self.openDeleteConfirmation = false;
							self.handleError(error);
						}
					})
				},
				closeEditForm(){
					var self = this;

					clearLockAPI('IP Whitelisting',self.selectedLocationIPs.Location_Id,self.employeeId,self.orgCode,self.atsBaseURL,function(error, data)
					{
						if(data) {
							self.updateIpwhitelistedForm = false;
							self.listWhitelistedIP()
						}
						else{
							self.updateIpwhitelistedForm = false;
							self.handleError(error);
						}
					})
				},
				retryFetching(){
					this.whiteListedIpFetchError = false;
					
				  //retry from fetching rigths if error occured at retriving access rights
                  if(this.fetchErrorAccessRights){
                      this.fnCheckAccessRights()
				  }
				  else{
                    this.listWhitelistedIP()  //otherwise retrive only whitlisted ips list
				  }
				},
				deleteWhitelistedIPLocation(location_id){
					var self = this;
					if(self.canDelete){
						// set lock to delete
							setlockAPI('IP Whitelisting',location_id,self.employeeId,self.orgCode,self.atsBaseURL,function(error, data)
							{
								if(data) {
									if(self.ipWhitelistedArray.length == 1 && self.ipAddressResctionflag == 1){
										self.deleteImage="ip-delete-confirm-image"
										self.deleteModelTitle = "Are you sure about deleting this record ?"
										self.deleteModelContent = "By deleting this last whitelisted IP address, you will be opening up access to the application from all networks.Now the IP restriction will be automatically removed from the settings. Do you want to proceed?"
									}
									else{
                                        self.deleteImage="delete-bin-image"
										self.deleteModelTitle = "Are you sure you want to delete this IP whitelisting record"
										self.deleteModelContent = "Deleting the white listed IP address will restrict users to access the app from these IP addresses"
								
									}
									
									self.openDeleteConfirmation = true
									self.deleteLocationId = location_id
								}
								else{
									self.handleError(error);
								}
							})		
					}
					else{
						self.snackbar = true;
						self.snackbarColor = "orange lighten-1";
						self.snackBarMsg = "Sorry, You don't have access rights to delete whitelisted IP address. Please contact system administrator."
					}
								
				},
				editWhitelistedIps(whitelisted_data){
					var self = this;
					if(self.canEdit){
						
						setlockAPI('IP Whitelisting',whitelisted_data.Location_Id,self.employeeId,self.orgCode,self.atsBaseURL,function(error, data)
						{
							if(data) {
								self.selectedLocationIPs = whitelisted_data
								self.updateIpwhitelistedForm = true
							}
							else{
								self.handleError(error);
							}
						})	
					}
					else{
						self.snackbar = true;
						self.snackbarColor = "orange lighten-1";
						self.snackBarMsg = "Sorry, You don't have access rights to update whitelisted IP address. Please contact system administrator."

					}										
				},
				setLoading(val){
                   this.loadingScreen = val
				},
				handleError(Err,isList = 0){
                    var self = this;
					self.loadingScreen = false;
						if (Err[0]) {
							var accessRightsErr = JSON.parse(Err[0].message);
							var errorCode = accessRightsErr.errorCode;
							var errorMessage = accessRightsErr.message;
							switch (errorCode) {
								//validation errors
								case 'IVE0000':
									var validationCode = accessRightsErr.validationError;
									if(validationCode.IVE0009) {
										self.snackbar = true;
										self.snackbarColor = "orange lighten-1";
										self.snackBarMsg = validationCode.IVE0009; 
									}
								break;
								//technical errors 
							    case 714:
								case 715:
								   self.snackbar = true;
								   self.snackbarColor = "orange lighten-1";
								   self.snackBarMsg = errorMessage
								break;
								
								case 712:
								   self.snackbar = true;
								   self.snackbarColor = "orange lighten-1";
								   self.snackBarMsg = "You dont have rights to edit more than 5 records."
								break;
								
								case 705:
								case 751:
								case 706:
								case '_UH0001':
									if(isList){
										self.whiteListedIpFetchError = true;
										self.errorTitle = "Oops";
										self.errorContent = "Something went wrong while fetching the whitelisted IP addresses.Please try after some time."									
									}
									else{
										self.snackbar = true;
										self.snackbarColor = "orange lighten-1";
										self.snackBarMsg = "There seems to be some technical issue. Please try after some time."
									}
								break;
								case 717:
								case 'SE0002':
                                case 'SE0102':
								case 'SE0003':
								case 'SE0103':
								case 'SE0102':
								case 'SE0004':
								case 'SE0104':
								case '_DB0001':		
                                    self.snackbar = true;
								    self.snackbarColor = "orange lighten-1";
								    self.snackBarMsg = "There seems to be some technical issue. Please try after some time."
								break;
								 
								case 'SE0001':
								case 'SE0101':
								case '_DB0000': 
								   if(isList){
										self.whiteListedIpFetchError = true;
										self.errorTitle = "Oops";
										self.errorContent = "It’s us ! There seems to be some technical difficulties while fetching the whitelisted IP addresses.Please try after some time."
									}
								   else{
									    self.snackbar = true;
										self.snackbarColor = "orange lighten-1";
										self.snackBarMsg = "There seems to be some technical issue. Please try after some time."
								   }
								break;
								case '_EC0001':
								   self.snackbar = true;
								   self.snackbarColor = "orange lighten-1";
								   self.snackBarMsg = "Record Not Found."
								break;
								
								case '_DB0101':
									self.snackbar = true;
									self.snackbarColor = "orange lighten-1";
									self.snackBarMsg = "Sorry,You don't have access to whitelist IP Address.Please contact system administrator."
								break;
								case '_DB0103':
									self.snackbar = true;
									self.snackbarColor = "orange lighten-1";
									self.snackBarMsg = "Sorry,You don't have access to delete whitelisted IP Address.Please contact system administrator."
						        break;
								case '_DB0102':
									self.snackbar = true;
									self.snackbarColor = "orange lighten-1";
									self.snackBarMsg = "Sorry,You don't have access to update whitelisted IP Address.Please contact system administrator."
								break;
								case 'SE0105':
									self.snackbar = true;
									self.snackbarColor = "orange lighten-1";
									self.snackBarMsg = "Already you have whitelisted IP Address for this location."
								break;
								case 'SE0106':
									self.snackbar = true;
									self.snackbarColor = "orange lighten-1";
									self.snackBarMsg = "Some of the IP Address are whitelisted already.Please validate your IP Address."
								break;
								
								case 752:
								case '_DB0100': 
								   self.isAccessDenied = true;
								break;
								default:
								    self.snackbar = true;
								    self.snackbarColor = "orange lighten-1";
								    self.snackBarMsg = "There seems to be some technical issue. Please try after some time."
								break;
								  
							}
						}
				},
				// check access rights
				fnCheckAccessRights() {
					var self = this;
					self.loadingScreen = true;
					var graph1 = graphql(self.atsBaseURL, {
						method: 'POST',
						headers: this.apiHeaders,
						asJSON: true
					});
					var accessRights = graph1(`mutation(
                    $formName: String,
                    $employeeId: Int!) {
						getAccessRights
							(
								formName: $formName,
								employeeId:$employeeId
							) 
							{
								errorCode message rights {
									Role_View Role_Add Role_Update Role_Delete Role_Optional_Choice Role_Hr_Group Role_Payroll_Group Is_Manager
							    }
							}
						}
                	`);
					accessRights({
						formName: 'IP Whitelisting',
						employeeId: self.employeeId
					})
					.then(function (response) {
						if (response) {
							// rights retrieved successfully
							var response = response.getAccessRights.rights;
							self.canView = response.Role_View;
							self.canAdd = response.Role_Add;
							self.canEdit = response.Role_Update;
							self.canDelete = response.Role_Delete;
							self.loadingScreen = false;
							// check if the user has view access for Whitelisted IP
							if(response.Role_View) {
								self.isAccessDenied = false;
								self.listWhitelistedIP(); //call list whitelisted IP
							}
							else {
								self.loadingScreen = false;
								self.isAccessDenied = true; //set access denied
							}
						}
					})
					.catch(function (Err) {
						 self.fetchErrorAccessRights = true
						 self.handleError(Err,1)
					});
				},
				// list all Whitelisted IP for the particular location
				listWhitelistedIP() {						
						var self = this;
						self.loadingScreen = true;
						// graphql url configuration
						var graph1 = graphql(self.hrappBeBaseURL, {
							method: 'POST',
							headers: this.apiHeaders,
							asJSON: true
						});

						// query to list Whitelisted IP
						var listwhitelistedIp = graph1.query(`query { listWhitelistedIp { errorCode message whiteListedIpAddress allConfiguredIPAddressed ipAddressResction}}`);

						// call listwhitelistedIp to get the list of Whitelisted IP
						listwhitelistedIp().then(function (response){
						   var responseData = response.listWhitelistedIp.whiteListedIpAddress
						   var sample = JSON.parse(responseData);
						   self.ipWhitelistedArray = sample;
						   var allWhitelistedIp = response.listWhitelistedIp.allConfiguredIPAddressed;
						   self.allWhitelistedIps = allWhitelistedIp;
						   self.ipAddressResctionflag = response.listWhitelistedIp.ipAddressResction;
						   self.loadingScreen = false;
						})
						.catch(function (Err) {
							self.handleError(Err,1);
						});
						
				},
				deleteWhitelistedLocation(){
                     var self = this;
						self.loadingScreen = true;
						// graphql url configuration
						var graph1 = graphql(self.hrappBeBaseURL, {
							method: 'POST',
							headers: this.apiHeaders,
							asJSON: true
						});
						var deleteWhitelistedIp = graph1(`mutation deletewhitelistedIPAddress($locationId:Int!) { deletewhitelistedIPAddress (locationId:$locationId) { errorCode message}}`);
				         deleteWhitelistedIp({
                          "locationId" : self.deleteLocationId,
							}).then(response => {
								clearLockAPI('IP Whitelisting',self.deleteLocationId,self.employeeId,self.orgCode,self.atsBaseURL,function(error, data)
								{
									if(data) {
										self.openDeleteConfirmation = false;
										self.snackbar = true;
									    self.snackbarColor = "success lighten-1";
									    self.snackBarMsg = "Whitelisted IP address deleted successfully."
								        self.listWhitelistedIP()
									}
									else{ 
										self.openDeleteConfirmation = false;
								        self.listWhitelistedIP();
										self.handleError(error);
									}
								})									
							})
							.catch((error)=>{
								clearLockAPI('IP Whitelisting',self.deleteLocationId,self.employeeId,self.orgCode,self.atsBaseURL,function(lockerror, data)
								{
									if(data) {
										
								        self.handleError(error)
									}
									else{ 
										self.openDeleteConfirmation = false;
										self.handleError(lockerror)
									}
								})
								
							})
				},
					fnAddIpSuccess(){
						this.showAddIpwhitelistingForm = false;
						this.snackbar = true;
						this.snackbarColor = "success lighten-1";
						this.snackBarMsg ="IP Whitelisted successfully.";
						this.listWhitelistedIP()	
					},
					fnUpdateIpSuccess(){
						var self =this;
						clearLockAPI('IP Whitelisting',self.selectedLocationIPs.Location_Id,self.employeeId,self.orgCode,self.atsBaseURL,function(error, data)
						{
							if(data) {
								self.updateIpwhitelistedForm = false;
								self.showAddIpwhitelistingForm = false;
								self.snackbar = true;
								self.snackbarColor = "success lighten-1";
								self.snackBarMsg ="IP Whitelist updated successfully.";
								self.listWhitelistedIP()	
							}
							else{
								self.updateIpwhitelistedForm = false;
								self.showAddIpwhitelistingForm = false;
								self.listWhitelistedIP()
								self.handleError(error);
							}
						})
					}
				
			},
			mounted() {				
				setTimeout(()=>{
					this.loadingScreen = true
                    this.fnCheckAccessRights()
				},2000)											
			},
			created() {
				// function to get ip
				try{
					axios.get('https://api.ipify.org?format=json').then(response => { 
						this.currentLocationIP = response.data.ip;
					}).catch(error => {
						/* If the IP address API is not available, API URL is wrong or internet connection is not available,
						then the error.readyState will be 0 or 4  */
						if((error.readyState === 0 || error.readyState === 4) && ipAddressRestriction == 1) {
							this.snackBarMsg = "Unable to get the IP address. Please contact system Administrator.";
							this.snackbarColor = "warning";
							this.snackbar = true;
						} else {
							this.currentLocationIP = "IP Blocked by user";
						}
					})
				}catch{
					this.currentLocationIP = "";
				}
				window.$cookies.set("userIpAddress", this.ipAddress);
			},
		})
		</script>
		
</body>
</html>
