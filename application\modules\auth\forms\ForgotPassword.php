<?php
//=========================================================================================
//=========================================================================================
/* Program        : ForgotPassword.php													*/
/* Property of Caprice Technologies Pvt Ltd,
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,
* Coimbatore, Tamilnadu, India.															*/
/* All Rights Reserved.            														*/
/* Use of this material without the express consent of Caprice Technologies
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law.*/
/*                                                                                    	*/
/* Description    : Form for Forgot Password											*/
/*                                                                                   	*/
/*                                                                                    	*/
/*Revisions      :                                                                    	*/
/*Version    Date           Authors                  Description                       	*/
/*0.1        30-May-2013    Sandhosh        		Initial Version       	            */
/*                                                                                    	*/
/*                                                                                    	*/
/*                                                                                    	*/
//=========================================================================================
//=========================================================================================
class Auth_Form_ForgotPassword extends Zend_Form
{

    public function init()
    {
        $this->setAttribs(array('id'=>'fg-pwd', 'autocomplete'=>'off'));
        $tdDecorator = array('ViewHelper',

        array('Description', array('escape' => false, 'tag' => false),array('placement' => Zend_Form_Decorator_Abstract::APPEND, 'tag' => 'em')),

        array(array('data'=>'HtmlTag'), array('tag' => 'td' )),
         
        array(array('row1'=>'HtmlTag'),array('tag'=>'tr')),
         
        array('Label', array('escape'=>false,'tag' => 'td')),
         
        array(array('row'=>'HtmlTag'),array('tag'=>'tr')));
        $forgotPwd = $this->createElement('text','forgotPwd');
        $forgotPwd->setLabel('Enter Your Email Address ')
        ->setAttribs(array('class'=>'validate[required,custom[email]] text-input','style'=>'margin-left:20px'))
        ->addFilters(array('StringTrim', 'StripTags'))
        ->addValidator('EmailAddress',  TRUE  )
        ->setRequired(true)->setDecorators($tdDecorator);
        $fgSubmit = $this->createElement('submit','fgSubmit');
        $fgSubmit->setLabel('Submit')
        ->setIgnore(true)->setDecorators(array('ViewHelper',

        array('Description', array('escape' => false, 'tag' => false),array('placement' => Zend_Form_Decorator_Abstract::APPEND, 'tag' => 'em')),

        array(array('data'=>'HtmlTag'), array('tag' => 'td')),
         
        array(array('row'=>'HtmlTag'),array('tag'=>'tr'))));
        $formDecoration = array('FormElements', array(array('data'=>'HtmlTag'), array('tag'=>'table','id'=>'load')), 'Form');
        $this->addElements(array($forgotPwd, $fgSubmit))->setDecorators($formDecoration);
    }


}

