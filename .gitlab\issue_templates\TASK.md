DEVELOPMENT TEAM :

- Requirement:

- Existing System:

- Proposed System:

## Repository Name:

## Project Name:

## Resolver function name:

## Request model:

* Query:
* Variables:

## Response model:

* Validation error response:
* Success response:
* Error response

## **Impact Analysis:**  
> **Note:** Copy and paste this template for each form if multiple forms are being analyzed.

#### **Form Name**  
_(Provide the name of the form you are analyzing here.)_

#### **URL**  
_(Insert the URL of the form or application page related to this analysis.)_

#### **Description of Change**  
_(Briefly describe the proposed change or enhancement.)_

#### **Areas Impacted**  
_(List all the components, modules, or systems that will be affected by this change.)_

#### **Risk Assessment**  
1. **High**: _(Indicate any high-risk areas or components.)_  
2. **Medium**: _(Mention medium-risk impacts.)_  
3. **Low**: _(Identify low-risk elements.)_

#### **Dependencies**  
_(Mention any dependencies, such as other modules, APIs, or systems.)_


## Access Checks:
✅  -   Represents 'Yes'
⬜  -  Represents 'No'

**Replace the following according to the task.**

| Role                       | List/View | Add | Update | Delete |
|----------------------------|-----------|-----|--------|--------|
| **Super Admin**            | ⬜        | ⬜   | ⬜      | ⬜      |
| **Admin**                  | ⬜         | ⬜   | ⬜      | ⬜      |
| **Employee Admin**         | ⬜         | ⬜   | ⬜      | ⬜      |
| **Service Provider Admin** | ⬜         | ⬜   | ⬜      | ⬜      |
| **Payroll Admin**          | ⬜         | ⬜   | ⬜      | ⬜      |
| **Benefits Admin**         | ⬜         | ⬜   | ⬜      | ⬜      |
| **Roster Admin**           | ⬜         | ⬜   | ⬜      | ⬜      |
| **Productivity Monitoring Admin** | ⬜         | ⬜   | ⬜      | ⬜      |
| **Employee**               | ⬜         | ⬜   | ⬜      | ⬜      |
| **Manager**                | ⬜         | ⬜   | ⬜      | ⬜      |


## Query/Mutation Details

| Query/Mutation | Request Params | Validation | Min Validation | Max Validation |
|--------------- |--------------- |------------|--------------- |--------------- |
|                |                |            |                |                |
|                |                |            |                |                |

## Error codes:
 
* Error codes Link: The error codes are updated for the above resolver function name in the following link, https://gitlab.com/cksiva09/hrapp-corehr-ms/-/wikis/Error-Codes-Based-On-Modules

- [ ] I prepared the MySQL query and attached it here:

          [ ] I updated the access rights query when the new form is added or removed :

          [ ] I updated the dependent form query when the new form is added or removed for plan details :

          [ ] I created the index for necessary fields :
          
- [ ] Concurrent update in the same table should be performed synchronously 

- [ ] I checked the impact on payslip:

- [ ] I checked the impact on existing reports:

- [ ] I checked the impact on data import modules:

- [ ] I checked the impact on data import template files:

- [ ] I checked the impacted module other than the above forms/steps:

- [ ] I checked the impact on access flow:  

       * Employee level access change :
       * Manager  level access change :
       * Admin    level access change :

- [ ] I checked the design in the user interface:

      * Universal Filters and form level filters in that form:
      * Default Sorting for that grid:
      * Validations For Each Fields:

- [ ] I checked the impact on service provider changes:

- [ ] I checked the response payload size by having 1500 records:

- [ ] I prepared the wireFrame and attached it here:

- [ ] I prepared the mind map and attached it here:

- [ ] I updated/uploaded the reference link and other requirement files:

- [ ] I have done the code level analysis and updated the estimation:

- [ ] I have verified that, for the service provider admin, we should list only service provider employees.

- [ ] I have confirmed that the system log messages are meaningful.

- [ ] I have added a condition to bypass access checks for external endpoints (unauthenticated endpoints).

Whenever we have a form removed:

- [ ] I have confirmed that the notification is removed in all PHP, Vue2, and Vue 3 layouts.
- [ ] I have confirmed that the Form ID/Records are removed (App manager, All customer instances).
- [ ] I have confirmed that form-related details are updated/removed in payroll prerequisites.
- [ ] I have confirmed that form-related details are updated/removed in the datasetup dashboard.
- [ ] I have confirmed that form-related details are updated/removed in email templates.
- [ ] I have confirmed that form-related details are updated/removed in redirections from other forms.
- [ ] I have confirmed that form-related details are updated/removed in the Module and Forms table.
- [ ] I have confirmed that form-related details are updated/removed in URLs from the API (Refer to forms table cleanup).
- [ ] I have confirmed that form that following form is subform that form is added in appmanager plan details code. 
- [ ] Identify the data type for each column in the Excel sheet.
- [ ] Set the identified data type as the default data type for the respective column.
- [ ] Implement horizontal and vertical scroll for the Excel sheet.
- [ ] I confirmed that archived tables should not have unique keys because the same kind of records can be deleted multiple times.

Reviewer Checklist:
- [ ]  I reviewed the analysis document, it has documented all the information.
- [ ]  I understood this task and its impacts
- [ ]  Developer followed the coding guidelines
- [ ]  Error codes are documented
- [ ]  Test scenarios are documented
- [ ]  Review comments are fixed

Table Changes:

- [ ] Upon adding a new column to the candidate table, I have validated the "Convert to Employee" action within the Individual Candidate process.

Job Candidate / Onboarding / Team Summary Changes:

- [ ] I ensured that when introducing new fields to any of the forms (Job Candidates, Onboarding, Team Summary), the changes are validated and included in the other related forms.
- [ ] I ensured that when introducing a new field for the Job Candidate, corresponding changes are made in the Talent Pool as well.

Unit testing and testing :

- [ ]  I analyze this task and its impacts

- [ ]  I validate the analysis and prepare the test cases(#Description Mandatory)

- [ ]  I update the testCases in ticket and respective document(with ticket number)

- [ ] These test cases reviewed by the lead

- [ ]  I test all the test cases which you documented

- [ ]  I test the dependent changes of this task

- [ ]  I test the application in all(Chrome,Edge,FireFox,IE,Safari) the browser

- [ ]  The functionality covers the requirement

- [ ]  I closed this issue after completing the testing

- [ ] I tested the task for test suit(n-1) preparation.

- [ ] I (Backend Developer) have tested the integration and validated the schema alignment.