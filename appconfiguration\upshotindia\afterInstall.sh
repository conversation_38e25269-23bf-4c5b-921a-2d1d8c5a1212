`chown -R daemon:daemon /opt/bitnami/apps/upshothr`
`find /opt/bitnami/apps/upshothr -type d -exec chmod 755 {} +`
`find /opt/bitnami/apps/upshothr -type f -exec chmod 644 {} +`
`touch /opt/bitnami/apache2/var/cache/mod_pagespeed/cache.flush`
`rm -rf /opt/bitnami/DeploymentQueries`
`mkdir /opt/bitnami/DeploymentQueries`
`cp /opt/bitnami/apps/upshothr/Queries/hrapp.sql /opt/bitnami/DeploymentQueries/`
`cp /opt/bitnami/apps/upshothr/Queries/hrapproles.sql /opt/bitnami/DeploymentQueries/`
`cp /opt/bitnami/apps/upshothr/Queries/appmanager.sql /opt/bitnami/DeploymentQueries/`
`cp /opt/bitnami/apps/upshothr/Queries/events.sql /opt/bitnami/DeploymentQueries/`
`cp /opt/bitnami/apps/upshothr/Queries/UPSHOTHR/dbupdate.sh /opt/bitnami/DeploymentQueries/`
`cp /opt/bitnami/apps/upshothr/Queries/UPSHOTHR/dbupdate.config /opt/bitnami/DeploymentQueries/`
`chmod 777 -R /opt/bitnami/DeploymentQueries/`
echo "service codedeploy-agent restart" | at -M now + 2 minute;
atq;
