<?php
//=========================================================================================
//=========================================================================================
/* Program        : ResetPassword.php	 												*/
/* Property of Caprice Technologies Pvt Ltd,
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,
* Coimbatore, Tamilnadu, India.															*/
/* All Rights Reserved.            														*/
/* Use of this material without the express consent of Caprice Technologies
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law.*/
/*                                                                                    	*/
/* Description    : Form for reset password												*/
/*                                                                                   	*/
/*                                                                                    	*/
/*Revisions      :                                                                    	*/
/*Version    Date           Authors                  Description                       	*/
/*0.1        30-May-2013    Sandhosh        		Initial Version       	            */
/*                                                                                    	*/
/*                                                                                    	*/
/*                                                                                    	*/
//=========================================================================================
//=========================================================================================
class Auth_Form_ResetPassword extends Zend_Form
{

    public function init()
    {
        $this->setAttribs(array('id'=>'Reset', 'autocomplete'=>'off', 'class'=>'hrapp_validation'));
        $tdDecorator = array('ViewHelper',
         
        array('Description', array('escape' => false, 'tag' => false),array('placement' => Zend_Form_Decorator_Abstract::APPEND, 'tag' => 'em')),
         
        array(array('data'=>'HtmlTag'), array('tag' => 'td' )),

        array('Label', array('escape'=>false,'tag' => 'td')),

        array(array('row'=>'HtmlTag'),array('tag'=>'tr')));
        $heading = new Zend_Form_Element_Text('heading');
        $heading->setOrder(0)->setDecorators(array('ViewHelper',
         
        array('Description', array('escape' => false, 'tag' => false),array('placement' => Zend_Form_Decorator_Abstract::APPEND, 'tag' => 'em')),
         
        array(array('data'=>'HtmlTag'), array('tag' => 'td', 'colspan'=>2, 'class'=>'Heading' )),

        array(array('row'=>'HtmlTag'),array('tag'=>'tr'))));
        $lblRequired = new Zend_Form_Element_Text('lblRequired');
        $lblRequired->setValue('')->setOrder(1)
        ->setDescription('<span class="short_explanation">*<span style="color:#000;"> required fields</span></span>');
        $lblRequired->setDecorators(array('ViewHelper',
         
        array('Description', array('escape' => false, 'tag' => false)),
         
        array(array('data'=>'HtmlTag'), array('tag' => 'td', 'colspan'=>'2')),
         
        array(array('row'=>'HtmlTag'), array('tag'=>'tr'))));
        $lblUser = new Zend_Form_Element_Text('lblUser');
        $user = new Zend_Form_Element_Text('User');
        $lblUser->setValue('User Name')->setOrder(2)
        ->setDecorators(array('ViewHelper',array('Description', array('escape' => false, 'tag' => false),array('placement' => Zend_Form_Decorator_Abstract::APPEND, 'tag' => 'em')),array(array('data'=>'HtmlTag'), array('tag' => 'td','style'=>'white-space:nowrap;'))));
        $user->setRequired(true)->setOrder(3)
        ->setDecorators(array('ViewHelper',array('Description', array('escape' => false, 'tag' => false),array('placement' => Zend_Form_Decorator_Abstract::APPEND, 'tag' => 'em')),array(array('data'=>'HtmlTag'), array('tag' => 'td'))));
        $newPwd = $this->createElement('password','newPwd');
        $newPwd->setLabel('New Password <label class="short_explanation">*</label>')
        ->setAttribs(array('class'=>'validate[required,minSize[6],maxSize[30],custom[pwdChar]] text-input text_fullname','placeholder'=>"Enter New Password"))
        ->addFilters(array('StringTrim', 'StripTags'))->setOrder(6)
        ->addValidator('regex',false,array('/^[\w\#\.\/\-\!\@\$\%\*\&\_\+\=\?]+$/'))
        ->addValidator(new Zend_Validate_StringLength(array('min' => 6, 'max' => 30)))
        ->setRequired(true)->setDecorators($tdDecorator);
        $confirmPwd = $this->createElement('password','confirmPwd');
        $confirmPwd->setLabel('Confirm New Password <label class="short_explanation">*</label>')
        ->setAttribs(array('class'=>'validate[required,equals[newPwd],minSize[6],maxSize[30],custom[pwdChar]] text-input text_fullname','placeholder'=>"Retype New Password"))
        ->addFilters(array('StringTrim', 'StripTags'))->setOrder(7)
        ->addValidator('regex',false,array('/^[\w\#\.\/\-\!\@\$\%\*\&\_\+\=\?]+$/'))
        ->addValidator(new Zend_Validate_StringLength(array('min' => 6, 'max' => 30)))
        ->addValidator(new Zend_Validate_Identical(array('token' => 'newPwd')))
        ->setRequired(true)->setDecorators($tdDecorator);
        $fgSubmit = $this->createElement('submit','fgSubmit');
        $fgSubmit->setLabel('Change Password')->setOrder(8)
        ->setAttrib('style','margin-left:30%;')
        ->setIgnore(true)->setDecorators(array('ViewHelper',
         
        array('Description', array('escape' => false, 'tag' => false),array('placement' => Zend_Form_Decorator_Abstract::APPEND, 'tag' => 'em')),
         
        array(array('data'=>'HtmlTag'), array('tag' => 'td','colspan'=>2)),

        array(array('row'=>'HtmlTag'),array('tag'=>'tr'))));
        
        $formDecoration = array('FormElements', array(array('data'=>'HtmlTag'), array('tag'=>'table','id'=>'load')), 'Form');
        
        $this->addElements(array($heading, $lblRequired, $lblUser, $user, $newPwd, $confirmPwd, $fgSubmit))->setDecorators($formDecoration);
    }
}