<?php

include APPLICATION_PATH."/validations/Validations.php"; 

class Employees_EmployeeBankAccountController extends Zend_Controller_Action
{
    protected $_validation                      = null;
    protected $_dbEmployeeBankAccount           = null;
    protected $_dbAccessRights                  = null;
    protected $_employeeBankAccountAccessRights = null;
	protected $_employeeBankAccountAccess       = null;
    protected $_logEmpId                        = null;
    protected $_dbCommonFunction                = null;
	protected $_ehrTables                       = null;
    protected $_formName                        = 'Employee Bank Account';
    protected $_hrappMobile                     = null;
    protected $_orgDetails                      = null;
    protected $_customField                     = null;

    public function init()
    {
        $this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
        if ($this->_hrappMobile->checkAuth())
        {
        	$this->_validation 	           = new Validations();
			$this->_dbCommonFunction       = new Application_Model_DbTable_CommonFunction();
            $this->_dbEmployeeBankAccount  = new Employees_Model_DbTable_EmployeeBankAccount();
            $this->_dbAccessRights         = new Default_Model_DbTable_AccessRights();
            
            $this->_dbPersonal             = new Employees_Model_DbTable_Personal();
            $this->_dbEmployee             = new Employees_Model_DbTable_Employee();
            $this->_orgDetails             = Zend_Registry::get('orgDetails');
			$this->_ehrTables              = new Application_Model_DbTable_Ehr();
            
			$userSession                   = $this->_dbCommonFunction->getUserDetails ();
			$this->_logEmpId               = $userSession['logUserId'];
            $this->_employeeBankAccountAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formName);
			$this->_employeeBankAccountAccess       = $this->_employeeBankAccountAccessRights['Employee'];
            $this->_customField                     = $this->_ehrTables->getCustomFields('IFSC Code');
            //$this->_dbAccessRights->refreshUserSessionTimestamp($this->_logEmpId);
		}
         else
        {
            if (Zend_Session::namespaceIsset('lastRequest'))
                Zend_Session::namespaceUnset('lastRequest');
            
            $session = new Zend_Session_Namespace('lastRequest');
            $session->lastRequestUri = 'employees/employee-bank-account';
            $this->_redirect('auth');
        }
    }
        /* Initialize action controller here */
    

    public function indexAction()
    {
        $checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

        if ($checkSessionAuth)
        {
            $this->_helper->layout()->disableLayout()->setLayout('admin_layout');
		
            $this->view->formName = $this->_formName;
            $this->view->bankName          =   $this->_dbCommonFunction->getBankName();
            
            $this->view->customFormName = $this->_ehrTables->getCustomForms($this->_formName);
            $this->view->customField    = $this->_customField;
            $this->view->empBankAccType = $this->_dbEmployee->getBankAccountType();
            $this->view->restrictEmpAccessForManager = $this->_orgDetails['Restrict_Emp_Access_For_Manager'];
            $this->view->employeeName   = $this->_dbPersonal->employeeDetail('', '', 'Emp_First_Name', 'ASC', '', '','', '',$this->_logEmpId,
                                                                    '', '', '', '', $this->_formName, '', 1, 1, '');
            
              $this->view->employeeBankAccountAccess =  array('Is_Manager'   => $this->_employeeBankAccountAccess['Is_Manager'],
                                                            'View'         => $this->_employeeBankAccountAccess['View'],
                                                            'Add'          => $this->_employeeBankAccountAccess['Add'],
                                                            'Update'       => $this->_employeeBankAccountAccess['Update'],
                                                            'Delete'       => $this->_employeeBankAccountAccess['Delete'],
                                                            'Admin'        => $this->_employeeBankAccountAccessRights['Admin'],
                                                            'Employee_Name'=> $this->_dbPersonal->employeeId($this->_logEmpId),
                                                            'logEmpId'     => $this->_logEmpId);
            $this->view->dateformat          = $this->_ehrTables->orgDateformat();
        } else {
			$this->_redirect('auth');
		}
    }

    public function listEmployeeBankAccountDetailsAction()
    {
        $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-employee-bank-account-details', 'json')->initContext();
			
            if ($this->_employeeBankAccountAccess['View'] == 1)
            {
 				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
                
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);				
				
                $employeeId = $this->_getParam('employeeId', null);
			    $employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
                
                //to display all the EmployeeBankAccount records in the data grid
				$this->view->result = $this->_dbEmployeeBankAccount->listEmployeeBankAccountDetails($page, $rows, $this->_logEmpId, $searchAll,$employeeId);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'employee-bank-account', 'employees');
        }
     }

    public function updateEmployeeBankAccountAction()
    {
          $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
		{
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('update-employee-bank-account', 'json')->initContext();
            
            $employeeId = $this->_getParam('employeeId', null);
			$employeeId = filter_var($employeeId, FILTER_SANITIZE_NUMBER_INT);
            
            $bankId = $this->_getParam('bankId', null);
            $bankId = filter_var($bankId, FILTER_SANITIZE_NUMBER_INT);
             
			if ((empty($bankId) && $this->_employeeBankAccountAccess['Add'] == 1) || (!empty($bankId) && $this->_employeeBankAccountAccess['Update'] == 1))
			{
		 		if ($this->getRequest()->isPost())
				{
         
				$formData = $this->getRequest()->getPost();       
                    
                        $accountNumber  = $this->_validation->onlyLetterNumberValidation($formData['accountNumber']);
                        //$bankName       = $this->_validation->onlyLetterSpaceValidation($formData['bankName']);
                        $empBankName    = $this->_validation->intValidation($formData['empBankName']);
                        $branchName     = $this->_validation->alphaNumSpCDotHySlashValidation($formData['branchName']);
						$street         = $this->_validation->alphaNumSpCDotHySlashValidation($formData['street']);
						$city           = $this->_validation->cityValidation($formData['city']);
						$state          = $this->_validation->stateValidation($formData['state']);
                        $accountType    = $this->_validation->intValidation($formData['accountType']);
                        $creditAccount  = $this->_validation->alphaNumSpDotValidation($formData['creditAccount']);
                        $ifscCode       = $this->_validation->onlyLetterNumberValidation($formData['ifscCode']);
                        $beneficiaryId  = $this->_validation->onlyLetterNumberValidation($formData['beneficiaryId']);

                        $pinCode        = $this->_validation->alphaNumSpHyValidation($formData['pinCode']);
                        $status         = $this->_validation->alphaNumSpDotValidation($formData['status']);
                       
						
						$accountNumber['valid']  = $this->_validation->lengthValidation($accountNumber, 1, 50, false);
						$branchName['valid']     = $this->_validation->lengthValidation($branchName, 1, 50, false);
						$street['valid']         = $this->_validation->lengthValidation($street, 1, 100, false);
						$city['valid']           = $this->_validation->lengthValidation($city, 1, 50, false);
						$state['valid']          = $this->_validation->lengthValidation($state, 1, 50, false);
                        $ifscCode['valid']       = $this->_validation->lengthValidation($ifscCode, 1, 50, false);
                        $beneficiaryId['valid']  = $this->_validation->lengthValidation($beneficiaryId, 1, 50, false);
                        $pinCode['valid']        = $this->_validation->lengthValidation($pinCode, 1, 50, false);

                        if(!empty($this->_customField['Required']))
                        {
                            $ifscCodeValidation = (!empty($ifscCode['value']) && $ifscCode['valid']);
                        }
                        else
                        {
                            $ifscCodeValidation = (empty($ifscCode['value']) || (!empty($ifscCode['value']) && $ifscCode['valid']));
                        }
                        
					if( !empty($employeeId) && 
                        !empty($accountNumber['value']) && $accountNumber['valid'] &&
                        !empty($empBankName['value']) &&  $empBankName['valid'] && 
                        !empty($branchName['value']) && $branchName['valid'] &&
                        (empty($street['value']) || (!empty($street['value']) && $street['valid']) ) &&
                        (empty($city['value']) || (!empty($city['value']) && $city['valid'])) &&
                        (empty($state['value']) || (!empty($state['value']) && $state['valid']) ) &&
                        (empty($pinCode['value']) || (!empty($pinCode['value']) && $pinCode['valid'])) &&
                        !empty($accountType['value']) && $accountType['valid'] &&
                        !empty($creditAccount['value']) &&  $creditAccount['valid']&&
                        ($ifscCodeValidation) &&
                        $beneficiaryId['valid'] &&
                        !empty($status['value']) && $status['valid'])
                        
					{
                        /** if value is not given in beneficicary id then in table null will be saved **/
                        if(empty($beneficiaryId['value']))
                        {
                            $beneficiaryId['value'] = new Zend_Db_Expr('NULL');
                        }

                         $employeeBankAccount = array(
                                        'Bank_Id'             => $bankId,   
                                        'Employee_Id'         => $employeeId,
										'Bank_Account_Number' => $accountNumber['value'],                                                   
                                        'Emp_Bank_Id'         => $empBankName['value'],
										'Branch_Name'         => $branchName['value'],
										'Street'              => $street['value'],
										'City'                => $city['value'],
                                        'State'               => $state['value'],
                                        'Zip'                 => $pinCode['value'],
                                        'Account_Type_Id'     => $accountType['value'],
                                        'Credit_Account'      => $creditAccount['value'],
                                        'IFSC_Code'           => $ifscCode['value'],
                                        'Beneficiary_Id'      => $beneficiaryId['value'],
                                        'Status'              => $status['value']);
                                       
									
						$customFormName = $this->_ehrTables->getCustomForms($this->_formName);
						$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formName);
 						$this->view->result = $this->_dbEmployeeBankAccount->updateEmployeeBankAccount($employeeBankAccount ,$this->_logEmpId, $formName);
					}
					 else
					{
                       
						$this->view->result = array('success'=>false, 'msg'=>'Invalid Data', 'type'=>'warning');
					}
				}
			}
            else
            {
				$this->view->result = array('success' => false, 'msg'=>"Sorry, Access Denied", 'type'=>'info');
            }
        }
        else
        {
            $this->_helper->redirector('index', 'employee-bank-account', 'employees');
        }
     }
	    

    public function deleteEmployeeBankAccountAction()
    {
        
         $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
            $ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-employee-bank-account', 'json')->initContext();
			
			if ($this->getRequest()->isPost())
			{
				$formData = $this->getRequest()->getPost();
				
				$bankId = $this->_validation->intValidation($formData['bankId']);
				
			
				if (!empty($bankId['value']) && $bankId['valid'])
				{		
					$customFormName = $this->_ehrTables->getCustomForms($this->_formName);
					$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formName);
						
					$this->view->result = $this->_dbEmployeeBankAccount->deleteEmployeeBankAccount($bankId['value'],  $this->_logEmpId, $formName);
				}
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
				}
			}
        }
        else
        {
            $this->_helper->redirector('index', 'employee-bank-account', 'employees');
		}     
    }
    
     public function listEmployeeDetailsAction()
    {
             $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-employee-details', 'json')->initContext();
			
            if ($this->_employeeBankAccountAccess['View'] == 1)
            {
                $sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
  
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
                  
                $employeeBankAccess = array( 'Is_Manager' => $this->_employeeBankAccountAccessRights['Employee']['Is_Manager'],
                'Update'      => $this->_employeeBankAccountAccessRights['Employee']['Update'],
                'Admin'       => $this->_employeeBankAccountAccessRights['Admin'],
                'LogId'       => $this->_logEmpId);

                
                $this->view->result = $this->_dbEmployeeBankAccount->listEmployeeDetails($page, $rows, $sortField, $sortOrder,$this->_logEmpId,$searchAll,$employeeBankAccess);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'employee-bank-account', 'employees');
        }
        
    }

    public function __destruct()
    {
        
    }
     
  }







