<?php

class  Employees_Model_DbTable_MaternitySlabs extends Zend_Db_Table_Abstract
{
    protected $_dbPersonal    = null;
    protected $_dbFinancialYr = null;
    protected $_dbComment     = null;
    protected $_orgDF         = null;
    protected $_db            = null;
    protected $_ehrTables     = null;
    protected $_dbJob         = null;
	protected $_dbCommonFun   = null;
	protected $_finYear       = null;
    public function init()
    {
        $this->_ehrTables     = new Application_Model_DbTable_Ehr();
        $this->_db            = Zend_Registry::get('subHrapp');
        $this->_dbPersonal    = new Employees_Model_DbTable_Personal();
        $this->_dbFinancialYr = new Default_Model_DbTable_FinancialYear();
        $this->_dbComment     = new Payroll_Model_DbTable_PayrollComment();
        $this->_dbJob         = new Employees_Model_DbTable_JobDetail();
		$this->_dbCommonFun   = new Application_Model_DbTable_CommonFunction();	
    }

    public function maternityLeaveSlab($employeeId,$leaveTypeId,$childRange)
    {
        if($childRange>=0)
        {
            $maternity = $this->_db->select()->from(array('M'=>$this->_ehrTables->maternitySlab),array('Total_Days'))
                                                    ->where('Child_From <=?',$childRange)
                                                    ->where('Child_To is NULL or Child_To >=?',$childRange)
                                                    ->where('LeaveType_Id =?',$leaveTypeId); 
            
                                          
            $totaldays=$this->_db->fetchOne($maternity);
            return $totaldays;
        }   
    }
    public function getChildDetails($employeeId)
    {
        $dependent=$this->_db->select()->from(array('ED'=>$this->_ehrTables->empDependent),array(new Zend_Db_Expr('count(Dependent_Id)')))
                            ->where('Employee_Id =?', $employeeId)
                            ->where('Relationship IN (?)',array('Son ','Daughter'));
        $rowdep = $this->_db->fetchOne($dependent);

        return $rowdep; 
    }
    public function searchMaternityLeave($leaveTypeId)
    {
        $qryMaternitySlab = $this->_db->select()->from(array('SL'=>$this->_ehrTables->maternitySlab),
                                                    array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS SL.Maternity_Slab_Id as count'),'SL.Maternity_Slab_Id',
                                                                  'SL.LeaveType_Id','SL.Child_From','SL.Child_To','SL.Total_Days',
                                                                  'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', SL.Maternity_Slab_Id)")))
                                                    ->joinInner(array('LT'=>$this->_ehrTables->leavetype),"LT.LeaveType_Id = SL.LeaveType_Id", array('LT.Leave_Enforcement_Configuration'))
									  
                                                    ->where('SL.LeaveType_Id = ?', $leaveTypeId);
 
        	
        $employeeServiceDetails = $this->_db->fetchAll($qryMaternitySlab);
                
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
                
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->maternitySlab, new Zend_Db_Expr('COUNT(Maternity_Slab_Id)'))->where('LeaveType_Id = ?', $leaveTypeId));
                
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $employeeServiceDetails);
    }
    public function updateMaternitySlab($maternitySlab,$sessionId, $formName)
    {
            $qryMaternitySlab = $this->_db->select()->from($this->_ehrTables->maternitySlab, new Zend_Db_Expr('COUNT(Maternity_Slab_Id)'))
                                                                            ->where('LeaveType_Id = ?', $maternitySlab['LeaveType_Id']);
                                                                         
            if (!empty($maternitySlab['Maternity_Slab_Id']))
            {
                $qryMaternitySlab->where('Maternity_Slab_Id != ?', $maternitySlab['Maternity_Slab_Id']);
            }
        
            if ($maternitySlab['Child_To'] != '' && !empty($maternitySlab['Child_To']) && preg_match('/^[0-9*\.]/', $maternitySlab['Child_To']))
            {
                	$rangeConditions = $this->_db->quoteInto(new Zend_Db_Expr('( Child_From BETWEEN ?'),$maternitySlab['Child_From']);
                    $rangeConditions .= $this->_db->quoteInto(new Zend_Db_Expr(' AND ? )'), $maternitySlab['Child_To']);
                    $rangeConditions .= ' OR '. $this->_db->quoteInto(new Zend_Db_Expr('( Child_To BETWEEN  ?'),$maternitySlab['Child_From']);
                    $rangeConditions .= $this->_db->quoteInto(new Zend_Db_Expr(' AND ? )'), $maternitySlab['Child_To']);
                    $rangeConditions .= ' OR '. $this->_db->quoteInto(new Zend_Db_Expr('( ? BETWEEN Child_From AND Child_To )'),$maternitySlab['Child_From']);
                    $rangeConditions .= ' OR '. $this->_db->quoteInto(new Zend_Db_Expr('( ? BETWEEN Child_From AND Child_To )'),$maternitySlab['Child_To']);
                    $qryMaternitySlab->where($rangeConditions);
            }
            else
            {
                $qryMaternitySlab ->where($this->_db->quoteInto(new Zend_Db_Expr('( ? BETWEEN Child_From AND Child_To )'),$maternitySlab['Child_From']));
            }

            $maternitySlabExists = $this->_db->fetchOne($qryMaternitySlab);
		
            if(empty($maternitySlabExists))
			{
					if(!empty($maternitySlab['Maternity_Slab_Id']))
					{
							$action = 'Edit';
							$updated = $this->_db->update($this->_ehrTables->maternitySlab, $maternitySlab, array('Maternity_Slab_Id = '.$maternitySlab['Maternity_Slab_Id']));
                            $maternityId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->maternitySlab,new Zend_Db_Expr('Max(Maternity_Slab_Id)')));
                            $updated = $this->getEmployeeMaternityRange($maternityId,$maternitySlab);
                    }
					else
					{
							$action  = 'Add';
                            $updated = $this->_db->insert($this->_ehrTables->maternitySlab, $maternitySlab);
                            $maternityId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->maternitySlab,new Zend_Db_Expr('Max(Maternity_Slab_Id)')));
                            $updated = $this->getEmployeeMaternityRange($maternityId,$maternitySlab);
                    }
                    $updated=1;
                    $result = $this->_dbCommonFun->updateResult (array('updated'    => $updated,
                                                                        'action'         => $action,
                                                                        'trackingColumn' => $maternitySlab['Maternity_Slab_Id'],
                                                                        'formName'       => $formName,
                                                                        'sessionId'      => $sessionId,
                                                                        'tableName'      => $this->_ehrTables->maternitySlab));
                
                return $result;
			}
			else
			{
					return array('success'=>false, 'msg'=>'Maternity Slab Range Is Already Exist', 'type'=>'info');
			}
		
    }
    
    public function deleteEligibleMaternityLeave($maternityId,$leaveTypeId)
    {
        $maternityDetailQry     = $this->_db->select()->from($this->_ehrTables->maternitySlab,array('Child_From','Child_To'))
                                                                                ->where('Maternity_Slab_Id = ?',$maternityId);
        $maternityDetail        = $this->_db->fetchRow($maternityDetailQry);
        $dbLeave                = new Employees_Model_DbTable_Leave();
        $leaveTypeDetails       = $dbLeave->getLeaveTypeRow($leaveTypeId);
        $activeEmployeeDetails  = $dbLeave->getActiveEmployeesDetails($leaveTypeDetails);	

        $maxChildFrom = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->maternitySlab,array(new Zend_Db_Expr('Max(Child_From)')))
                        ->where('LeaveType_Id = ?',$leaveTypeId));
        if($maxChildFrom==$maternityDetail['Child_From'])
        {
            foreach($activeEmployeeDetails as $activeEmployee)
            {
                $childRange=$activeEmployee['Child_Count'];
            
                if($maternityDetail['Child_From']<= $childRange &&($maternityDetail['Child_To'] >= $childRange|| is_null($maternityDetail['Child_To'])))
                {
                    $whereCondition['Employee_Id = ?']  = $activeEmployee['Employee_Id'];
                    $whereCondition['LeaveType_Id = ?'] = $activeEmployee['LeaveType_Id'];
                    $deleted=$this->_db->delete($this->_ehrTables->empEligbleLeave,$whereCondition);
                }
            }
            $deleted = $this->_db->delete($this->_ehrTables->maternitySlab, 'Maternity_Slab_Id='.(int)$maternityId);
		    return $deleted;
        }
        else
        {
            return 0;
        }
    }

    public function getEmployeeMaternityRange($maternityId,$empMaternityLeave)
    {
        $employeeEligibleLeave  = array();
        $dbLeave                = new Employees_Model_DbTable_Leave();
        if(!empty($empMaternityLeave['LeaveType_Id']))
        {
            $updated     = $dbLeave->empDOJUpdateEligibleDays(NULL,'update-materntiy-leave',NULL,$empMaternityLeave['LeaveType_Id'],0,'Yes');
        }
        else
        {
            $updated =  0;
        }
        return $updated;
    }
    
    public function __destruct()
    {
        
    }
}
?>



