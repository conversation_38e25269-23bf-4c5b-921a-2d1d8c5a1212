[production]
phpSettings.display_startup_errors = 0
phpSettings.display_errors = 0
includePaths.library = APPLICATION_PATH "/../library"
bootstrap.path = APPLICATION_PATH "/Bootstrap.php"
bootstrap.class = "Bootstrap"
appnamespace = "Application"
resources.frontController.params.displayExceptions = 0

resources.modules[]=""
resources.frontController.params.prefixDefaultModule = "1"
resources.frontController.defaultModule = "default"
resources.view.doctype = "XHTML1_TRANSITIONAL"

resources.db.adapter = "PDO_MYSQL"
resources.db.params.charset = "utf8"
resources.db.params.port = 3306

resources.mail.transport.type     = smtp
resources.mail.transport.auth     = "login"
resources.mail.transport.ssl      = "ssl"
resources.mail.transport.port     = 465
resources.mail.transport.register = true

resources.session.cache_limiter = must-revalidate
resources.view.charset = "UTF-8"
resources.view.helperPath.View_Helper = APPLICATION_PATH "/views/helpers"
resources.layout.layoutPath = APPLICATION_PATH "/layouts/scripts/"

mobileapps.production = 1
resources.frontController.moduleDirectory = APPLICATION_PATH "/modules"

mobileapps.domain = upshothr.uk
mobileapps.productlogo=0

mobileapps.region = eu-west-2
mobileapps.bucketName = s3.taxdocs.upshothr.uk
mobileapps.version = 2006-03-01
mobileapps.imageBucket = s3.images.upshothr.uk
mobileapps.logoBucket = s3.logos.upshothr.uk
mobileapps.smregion = eu-west-2
mobileapps.signedURLValidity = '1200 seconds'
mobileapps.reimbursementSignedURLValidity = '604800 seconds'
mobileapps.secretname = PROD/UPSHOT/PGACCESS

mobileapps.ocrapiurlprefix = ""
mobileapps.iciciApiBaseUrl = ""
mobileapps.iciciCIBBaseURL = ""
mobileapps.ccAvenueWorkingKey =""
mobileapps.ccAvenueAccessCode = ""
mobileapps.ccAvenueURL = ""
mobileapps.facebookURL = ""
mobileapps.twitterURL = ""
mobileapps.linkedinURL = ""
mobileapps.googleURL = ""
mobileapps.websiteURL = ""
mobileapps.atsBaseURL= https://api.upshothr.uk/ats/graphql
mobileapps.clientipUrl= "https://api.ipify.org?format=json"
mobileapps.integrationBaseURL= https://api.upshothr.uk/integration/rographql
mobileapps.trstscoreBaseURL= https://api.upshothr.uk/trstscore/wographql
mobileapps.workflowEngineInitiateBaseUrl=https://api.upshothr.uk/workflowEngine/workflow/initiate
mobileapps.coreHrRoBaseUrl=https://api.upshothr.uk/coreHr/rographql
mobileapps.hrappBeRoBaseUrl=https://api.upshothr.uk/hrappBe/roGraphql
mobileapps.employeeSelfServiceRoBaseURL=https://api.upshothr.uk/employeeSelfService/rographql

mobileapps.firebaseApiKey = AIzaSyCw5pu5_1SEdhJm__K-kHWMdzQbZi4tEs4
mobileapps.firebaseAuthDomain = upshot-f5883.firebaseapp.com
mobileapps.firebaseDatabaseURL = https://upshot-f5883.firebaseio.com
mobileapps.firebaseProjectId = upshot-f5883
mobileapps.firebaseStorageBucket = upshot-f5883.appspot.com
mobileapps.firebaseMessagingSenderId = 965669962064
mobileapps.firebaseAppId = 1:965669962064:web:3676c4229c6d8e32336c55
mobileapps.refreshTokenAPIUrl = https://securetoken.googleapis.com/v1/



mobileapps.appSecretKey = a60be24833cbd6f0e424e133b0f55c96
mobileapps.appVersion[] = 120201912
mobileapps.appVersion[] = 121202001
mobileapps.appVersion[] = 122202002
mobileapps.appVersion[] = 123202002
mobileapps.appVersion[] = 124202003
mobileapps.appVersion[] = 125202003
mobileapps.appVersion[] = 132200000
mobileapps.appVersion[] = 132300000
mobileapps.appVersion[] = 133000000
mobileapps.appVersion[] = 134000000
mobileapps.appVersion[] = 135000000
mobileapps.appVersion[] = 136000000
mobileapps.appVersion[] = 137000000

mobileapps.redirectionurl= https://www.upshothr.uk/appmanager/mobilemanager
resources.db.params.username = ""
resources.db.params.encrypt = 0
resources.db.params.password = ""
resources.db.params.dbname = upshothr_managerdb
resources.db.profiler.enabled = false
resources.mail.transport.host     = ""
resources.mail.defaultfrom.email = "<EMAIL>"
