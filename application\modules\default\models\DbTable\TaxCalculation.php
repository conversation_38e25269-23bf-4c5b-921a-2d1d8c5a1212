
<?php
//=========================================================================================
//=========================================================================================
/* Program : TaxCalculation.php									   				                 *
 * Property of Caprice Technologies Pvt Ltd,                                             *
* Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                       *
* Coimbatore, Tamilnadu, India.														     *
* All Rights Reserved.            														 *
* Use of this material without the express consent of Caprice Technologies               *
* or assignees is unlawful and subject to prosecution to the fullest extent of the law.  *
*                                                                                    	 *
* Description : All the tax calculations  related function will be added here            *
*                                                                                   	 *
*                                                                                    	 *
* Revisions :                                                                    	     *
*  Version    Date           Author                  Description                         *
*  0.1        24-June-2020    Suresh                Initial Version         	             *
*                                                                                    	 */
//=========================================================================================
//=========================================================================================
class Default_Model_DbTable_TaxCalculation extends Zend_Db_Table_Abstract
{
	protected $_db = null;
	protected $_ehrTables = null;
	protected $_orgDF=null;
  	public function init()
	{
		$this->_db = Zend_Registry::get('subHrapp');
		$this->_ehrTables = new Application_Model_DbTable_Ehr();
        $this->_orgDF    = $this->_ehrTables->orgDateformat();
    }

    /*This function allow us to identify whether the tax declaration allowance is exist or not*/
    public function isTaxDeclarationAllowanceExist($employeeId,$empTaxCalculationDetails)
    {
        $whereCondTaxStatus = $empTaxCalculationDetails['WhereCond_Tax_Status'];
       
        $whereCondAssYrBasedOperator = $empTaxCalculationDetails['Assessment_Year_Operator'];
       
        $qryDeclaration = $this->_db->select()->distinct()->from(array('D'=>$this->_ehrTables->taxDeclaration), array('Declaration_Amount'=>
				new Zend_Db_Expr('Case when D.Approval_Status="Approved" then Amount_Approved
                           when D.Approval_Status = "Applied" then Amount_Declared
                           when D.Approval_Status = "Declared" then Amount_Declared End')))
				->joinInner(array('I'=>$this->_ehrTables->sectionsInvestment), 'I.Investment_Cat_Id=D.Investment_Cat_Id',array('I.Allowance_Type_Id'))
                ->joinInner(array('E'=>$this->_ehrTables->taxExemptions), 'E.Tax_Section_ID = I.Tax_Section_ID', array(''))
                ->joinInner(array('AT'=>$this->_ehrTables->allowanceTypes),'I.Allowance_Type_Id = AT.Allowance_Type_Id', array('AT.Allowance_Name'))
                ->joinInner(array('TA'=>$this->_ehrTables->taxSectionAllowance),'TA.Allowance_Type_Id = AT.Allowance_Type_Id', array(''))
                ->joinInner(array('TS'=>$this->_ehrTables->taxSections), 'TS.Section_Id = TA.Tax_Section_Id', array(''))
                ->where('I.Assessment_Year '.$whereCondAssYrBasedOperator.'?', $empTaxCalculationDetails['Assessment_Year'])
                ->where('D.Assessment_Year = ?', $empTaxCalculationDetails['Assessment_Year'])
                ->where('I.Tax_Status IN (?)',$whereCondTaxStatus)
                ->where('I.Allowance_Type_Id IS NOT NULL')
                ->where('AT.AllowanceType_Status = ?', 'Active')
                ->where('D.Employee_Id = ?', $employeeId);
        
         /*We need to get the details for only those section id*/
        if(!empty($empTaxCalculationDetails['Tax_Section_Id']))
        {
            $qryDeclaration->where('TS.Tax_Section_ID IN (?)', $empTaxCalculationDetails['Tax_Section_Id']);
        }

        $taxDeclarationAllowances = $this->_db->fetchAll($qryDeclaration);

        return $taxDeclarationAllowances;
    } 

    /*This function allow us to compare tax declaration allowance with employee taxable allowance(should be a tax declared allowance) for this financial year */
    public function getTaxDeclarationAllowanceDetails($employeeId,$allowanceActualTaxIncentive,$unpaidArray,$taxDeclarationAllowances,$finStartMonthYearArr,$futureMonthCount,$midJoinedNextSalMonthDet, $paycyleDate)
    {
       $section10Allowance = $section10AllowanceDetails = array();
       $totalDeclaredAllowanceAmount = 0;
       $pastMonthAllowanceAmount = 0;
       $currentMonthAllowanceAmount = 0;
       $forecastAllowanceAmount = 0;
       $dbPayslip = new Payroll_Model_DbTable_Payslip();     

       $nxtSalMonthAllowanceDetails = isset($midJoinedNextSalMonthDet['Next_Month_Allowance_Det']) ? ($midJoinedNextSalMonthDet['Next_Month_Allowance_Det']) : array();
       $taxDeclarationAllowanceId = array();
       foreach ($taxDeclarationAllowances as $declaredAllowance) 
       {
           /*employee has taxable allowance for for current month payslip*/
           if(!empty($allowanceActualTaxIncentive))
           {
               foreach ($allowanceActualTaxIncentive as $taxableAllowance) 
               {
                   /*Based on the taxable allowance description(allowance id) we need to get the allowance type id*/
                   $getTaxableAllowanceTypeId = $this->getAllowanceTypeId($taxableAllowance['Description']);

                   if(!empty($getTaxableAllowanceTypeId))
                   {
                        if($declaredAllowance['Allowance_Type_Id']==$getTaxableAllowanceTypeId)
                        {
                            $pastMonthAllowanceDetails = $this->getPastMonthAllowanceDetails($employeeId,$declaredAllowance['Allowance_Type_Id'],$finStartMonthYearArr);
                            
                            /*Get the next salary month allowance amount for the mid joined employees */
                            if(!empty($nxtSalMonthAllowanceDetails) && count($nxtSalMonthAllowanceDetails) > 0){
                                foreach($nxtSalMonthAllowanceDetails as $midJoinedNextSalMonthAllowance){
                                    /** Get the allowance type id from the allowance id */
                                    $empMidJoinedAllowanceTypeId = $this->getAllowanceTypeId($midJoinedNextSalMonthAllowance['Description']);

                                    if($empMidJoinedAllowanceTypeId == $getTaxableAllowanceTypeId){
                                        $nextSalMonthAllowanceAmt = $midJoinedNextSalMonthAllowance['Incentive_Amount'];
                                        break;
                                    }else{
                                        $nextSalMonthAllowanceAmt = 0;
                                    }
                                }
                            }else{
                                $nextSalMonthAllowanceAmt = $taxableAllowance['Incentive_Amount'];
                            }

                            /*before reducing the unpaid leave from current month allowance we need to do the forecast*/
                            $forecastAllowanceAmount = $nextSalMonthAllowanceAmt*$futureMonthCount;
                            /*unpaid leave amount should be reduced for current month allowance*/
                            $currentMonthAllowanceAmount = $dbPayslip->getAllowanceAmount($taxableAllowance,$unpaidArray,0, $paycyleDate);
                            $yearlyAllowanceAmount = $pastMonthAllowanceDetails['Incentive_Amount']+$currentMonthAllowanceAmount+$forecastAllowanceAmount;
                            $section10Allowance['Payslip_Id']=$taxableAllowance['Payslip_Id'];
                            $section10Allowance['Allowance_Id']=$taxableAllowance['Description'];
                            $section10Allowance['Allowance_Amount'] = min(array($yearlyAllowanceAmount,$declaredAllowance['Declaration_Amount']));
                            $section10AllowanceDetails[] = $section10Allowance;
                            $totalDeclaredAllowanceAmount +=$section10Allowance['Allowance_Amount'];
                            array_push($taxDeclarationAllowanceId,$declaredAllowance['Allowance_Type_Id']);
                        }
                    }
               }
           }
       }

       /*when there is allowance not exist in current month payslip but it might be present in previous payslip*/
       foreach($taxDeclarationAllowances as $declaredAllowance)
       {
           if(!in_array($declaredAllowance['Allowance_Type_Id'], $taxDeclarationAllowanceId))
           {
                $pastMonthAllowanceDetails = $this->getPastMonthAllowanceDetails($employeeId,$declaredAllowance['Allowance_Type_Id'],$finStartMonthYearArr);
                if($pastMonthAllowanceDetails['Incentive_Amount'] > 0 && $pastMonthAllowanceDetails['Allowance_Id'] > 0)
                {
                    $yearlyAllowanceAmount = $pastMonthAllowanceDetails['Incentive_Amount'];
                    $section10Allowance['Allowance_Amount'] = min(array($yearlyAllowanceAmount,$declaredAllowance['Declaration_Amount']));
                    $section10Allowance['Payslip_Id']=$unpaidArray['Payslip_Id'];
                    $section10Allowance['Allowance_Id']=$pastMonthAllowanceDetails['Allowance_Id'];
                    $section10AllowanceDetails[] = $section10Allowance;      
                    $totalDeclaredAllowanceAmount +=$section10Allowance['Allowance_Amount'];
                }
           }
       }

        return array('Section_Allowance'=>$section10AllowanceDetails,'Total_Declared_Allowance_Amount'=>$totalDeclaredAllowanceAmount);
    }


    public function getPastMonthAllowanceDetails($employeeId,$allowanceTypeId,$finStartMonthYearArr)
    {
        $pastMonthAllowanceAmount = 0;
        $allowanceId = 0;
        $dbPayslip = new Payroll_Model_DbTable_Payslip();
        /*based on past month employee payslip we need to get the details from taxdeclaration_allowance_form16_snapshot table*/
        if(!empty($finStartMonthYearArr))
        {
            foreach ($finStartMonthYearArr as $salaryMonth)
            {
                $salaryMonth = date('n,Y',strtotime($salaryMonth));
                $pastMonthAllowanceQry = $this->_db->select()->from(array('S'=>$this->_ehrTables->monthlyPayslip),array('ATA.Incentive_Amount','ATA.Payslip_Id','ATA.Description'))
                                ->joinInner(array('ATA'=>$this->_ehrTables->actualTaxAllowance), 'ATA.Payslip_Id = S.Payslip_Id',array(''))
                                ->joinInner(array('A'=>$this->_ehrTables->allowances), 'A.Allowance_Id = ATA.Description',array(''))
                                ->joinInner(array('I'=>$this->_ehrTables->sectionsInvestment), 'I.Allowance_Type_Id=A.Allowance_Type_Id',array(''))
                                ->where('I.Allowance_Type_Id IS NOT NULL')
                                ->where('S.Salary_Month = ?', $salaryMonth)
                                ->where('A.Allowance_Type_Id = ?', $allowanceTypeId)
                                ->where('S.Employee_Id = ?', $employeeId);    

                $pastMonthAllowanceDetails = $this->_db->fetchRow($pastMonthAllowanceQry);                
           
                if(!empty($pastMonthAllowanceDetails))
                {
                    $midJoin            = 0;
                    $month               = explode(',', $salaryMonth);
                    $paycyleDate         = $dbPayslip->getSalaryDay($month[0], $month[1]);                            
                    $getTotalWorkingDays = $dbPayslip->calcTotWorkingDaysInMonth($month[0], $month[1], $paycyleDate['Salary_Date'], $paycyleDate['Last_SalaryDate'], $employeeId);
                    $empWorkeddays       = $dbPayslip->getBusinessWorkingDays($paycyleDate['Salary_Date'], $paycyleDate['Last_SalaryDate'], $employeeId);
                    $totalWorkingdays    = $getTotalWorkingDays[0];
                    $empFixedWorkingDays = $workingDays = $getTotalWorkingDays[1];
                    $getValidEmployee    = $dbPayslip->getActiveInactiveEmployee($employeeId, $paycyleDate['Salary_Date'], $paycyleDate['Last_SalaryDate']);
                        
                    if(!empty($getValidEmployee[1]))
                    {
                        $lastSalaryDate = $getValidEmployee[1]; // resignation date will be last salary date no need to consider after resignation
                    }
                    else
                    {
                        $lastSalaryDate = $paycyleDate['Last_SalaryDate'];
                    }

                    //if employee working days less than total working days of the month, then employee can be midjoin or resigned during the payslip month
                    if($empWorkeddays < $totalWorkingdays)
                    {
                        $midJoin = array($totalWorkingdays,$empWorkeddays);
                    }
                    
                    $pastMonthUnpaidArray = array();
                    $pastMonthUnpaidArray['Employee_Id']     = $employeeId;
                    $pastMonthUnpaidArray['Salary_Date']     = $paycyleDate['Salary_Date'];
                    $pastMonthUnpaidArray['Last_Salary_Date']= $lastSalaryDate; 
                    $pastMonthUnpaidArray['Payslip_Id']      = $pastMonthAllowanceDetails['Payslip_Id']; 
                    $pastMonthUnpaidArray['Working_Days']    = $workingDays;
                    $pastMonthUnpaidArray['Mid_Join']        = $midJoin;
                                            
                    $allowanceId = $pastMonthAllowanceDetails['Description'];

                    $pastMonthAllowanceAmountDetails['Incentive_Amount'] = $dbPayslip->getAllowanceAmount($pastMonthAllowanceDetails,$pastMonthUnpaidArray,0, $paycyleDate);

                    $pastMonthAllowanceAmount +=  $pastMonthAllowanceDetails['Incentive_Amount'];                          
                }                            
            }
        }

        $allowanceDetails = array('Incentive_Amount'=>$pastMonthAllowanceAmount,'Allowance_Id'=>$allowanceId);
        
        return $allowanceDetails;
    }

    /*This function allow us to find the form16 taxdeclaration allowance for particular month*/
    public function getForm16TaxDeclarationAllowance($employeeId,$salaryMonth)
    {
        $taxDeclarationAllowanceQry = $this->_db->select()->from(array('S'=>$this->_ehrTables->monthlyPayslip),array('AT.Allowance_Name as Investment_Category','TFS.Allowance_Amount as ExemptAmt'))
                                                ->joinInner(array('TFS'=>$this->_ehrTables->taxDeclarationAllowanceForm16Snapshot), 'TFS.Payslip_Id = S.Payslip_Id',array(''))
                                                ->joinInner(array('A'=>$this->_ehrTables->allowances), 'A.Allowance_Id = TFS.Allowance_Id',array(''))
                                                ->joinInner(array('AT'=>$this->_ehrTables->allowanceTypes),'AT.Allowance_Type_Id = A.Allowance_Type_Id', array(''))
                                                ->where('S.Salary_Month = ?', $salaryMonth)
                                                ->where('S.Employee_Id = ?', $employeeId);
        $taxDeclarationAllowance =$this->_db->fetchAll($taxDeclarationAllowanceQry);    
        return $taxDeclarationAllowance;
    }

    /** Calculate the annual pre tax deduction amount to deduct from the taxable salary before calculating tax */
    public function calcAnnualPreTaxDeductionAmount($employeeId,$empPayMonthDetails,$empTaxCalculationDetails){

        /*when the pretax deduction is not exist for the given payroll month we need to defined that as empty and allow the system to calculate the 
        pretax deduction for previous payroll month*/
        if(isset($empPayMonthDetails['Emp_Deduction_Details'])) 
        {
            $empDeductionDetails = $empPayMonthDetails['Emp_Deduction_Details'];
        }
        else 
        {
            $empDeductionDetails = array();
        }
        
        $getValidEmployee = $empPayMonthDetails['Resignation_Details'];
        $fiscalMonthYear = $empTaxCalculationDetails['Fiscal_Month_Year'];
        $fiscalEndDate = $empTaxCalculationDetails['Fiscal_Start_End_Dates']['finend'];
        $paycycleStartDate = $empPayMonthDetails['Paycycle_Dates']['Salary_Date'];
        $currentFutPayMonthDeductionAmount = 0;

        /** Iterate the employee deduction to find the employee current and future pre tax deduction amount */
        foreach($empDeductionDetails as $empDeduction){
            /** If pre tax deduction flag is enabled */
            if((int)$empDeduction['Pre_Tax_Deduction'] === 1){
                $empDeductionAmount = $empDeduction['Deduction_Amount'];
                /** If the deduction is one month deduction then consider the amount only for the current month deduction */
                if($empDeduction['Deduction_Name'] === 'One-Month Deduction'){
                    $currentFutPayMonthDeductionAmount += $empDeductionAmount;
                }else{
                    /** If the deduction is recurring deduction then consider the amount only from the current month 
                     * till the recurrinng end date or the employee resignation date whichever is lesser */
                    $newEffectiveDate = $paycycleStartDate;

                    /** If employee resigned for the current pay month then recurring deduction future months amount should not be considered */
                    if(!empty($getValidEmployee[1]))
                    {
                        $recurringDedTotalMonths = 1;
                    }
                    else
                    {
                        /** If employee not resigned for the current pay month then recurring deduction future months amount can be considered */

                        $newEndDate = $empDeduction['End_Date']; // recurring deduction end date

                        /** If the recurring deduction end date is greater than fiscal end date then 
                         * fiscal end date should be considered as the recurring end date */
                        if(strtotime($newEndDate) > strtotime($fiscalEndDate)){
                            $newEndDate = $fiscalEndDate; // recurring deduction end date
                        }

                        /** Get the total number of months between the start and end date based on the paycycle type */
                        $recurringDedTotalMonths = $this->calcMonthBetweenDates($newEffectiveDate,$newEndDate);
                    }

                    $currentFutPayMonthDeductionAmount += ($recurringDedTotalMonths * $empDeductionAmount);
                }
            }
        }

        /** Get the past payslip months pre tax deduction amount for the current fiscal year */
        $empPastMonthDeductionQry =  $this->_db->select()->from(array('MSP'=>$this->_ehrTables->monthlyPayslip), array('IFNULL(SUM(SD.Deduction_Amount),0)'))
                                        ->joinLeft(array('SD'=>$this->_ehrTables->salaryDeduction),'SD.Payslip_Id=MSP.Payslip_Id AND (SD.Deduction_Name="One-Month Deduction" OR SD.Deduction_Name="Recurring Deduction")',
                                            array(''))
                                        ->joinLeft(array('D'=>$this->_ehrTables->deductions), 'D.Deduction_Id=SD.Description', array(''))
                                        ->where('D.Pre_Tax_Deduction = ?',1)
                                        ->where('MSP.Salary_Month IN (?)',$fiscalMonthYear)
                                        ->where('MSP.Employee_Id = ?',$employeeId);

        $pastPayMonthDeductionAmount = $this->_db->fetchOne($empPastMonthDeductionQry);

        // Sum the past, current and the future month deduction amount
        $fiscalyrTotDeductionAmount = $currentFutPayMonthDeductionAmount + $pastPayMonthDeductionAmount;

        return $fiscalyrTotDeductionAmount;
    }

    /** Calculate the total number of months between two dates based on the paycycle date */
    public function calcMonthBetweenDates($startDate,$endDate){
        $noOfMonths = 0;

        /* Get the total number of loan months between the paycycle end date and the loan end date */
        while(strtotime($startDate) <= strtotime($endDate)){
            $noOfMonths++;
            $startDate = date('Y-m-d', strtotime('+1 month', strtotime($startDate)));
        }

        return $noOfMonths;
    }

    /** Get the employee allowance id based on the allowance type id */
    public function getAllowanceTypeId($allowanceId){
        return $this->_db->fetchOne($this->_db->select()->from(array('A'=>$this->_ehrTables->allowances),array('A.Allowance_Type_Id'))
                            ->joinInner(array('I'=>$this->_ehrTables->sectionsInvestment), 'I.Allowance_Type_Id=A.Allowance_Type_Id',array(''))
                            ->where('I.Allowance_Type_Id IS NOT NULL')
                            ->where('A.Allowance_Id = ?', $allowanceId));
    }

    public function __destruct()
    {
        
    }
}
?>