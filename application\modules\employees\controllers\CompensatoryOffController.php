<?php
include APPLICATION_PATH."/validations/Validations.php";

class Employees_CompensatoryOffController extends Zend_Controller_Action
{
    protected $_loginEmpUser = null;
	protected $_dbCompOff = null;
    protected $_dbCommonFun = null;
	protected $_dbAccessRights = null;
	protected $_compOffAccessRights = null;
	protected $_dbPersonal = null;
	protected $_loginEmpUserId = null;
	protected $_logEmpId = null;
	protected $_ehrTables = null;
	protected $_dbOrgSettings = null;
    protected $_validation = null;
	protected $_formNameA = 'Compensatory Off';
    protected $_formNameB = 'Compensatory Off Balance';
    protected $_formIdB = 140;
    protected $_formIdC = 264;
    protected $_hrappMobile = null;
    protected $_dbEmployee  = null;
    protected $_orgDetails  = null;
    protected $_dbDept  = null;
    protected $_dbManager  = null;
    protected $_dbLocation  = null;
    protected $_myTeamAccess = null;
    protected $_selfServiceAccess = null;
    protected $_myTeamCompOffFormId = 334;
    protected $_selfServiceCompOffFormId = 335;
    
    public function init()
    {        
        $this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
        if ($this->_hrappMobile->checkAuth())
        {
			$this->_dbCommonFun = new Application_Model_DbTable_CommonFunction();
            $userSession        = $this->_dbCommonFun->getUserDetails ();
			$this->_logEmpId    = $userSession['logUserId'];
			
			$this->_dbCompOff  = new Employees_Model_DbTable_CompensatoryOff();
			$this->_dbPersonal = new Employees_Model_DbTable_Personal();
            $this->_basePath          = new Zend_View_Helper_BaseUrl();
			$this->_ehrTables  = new Application_Model_DbTable_Ehr();
			$this->_dbAccessRights = new Default_Model_DbTable_AccessRights();			
			$this->_dbOrgSettings  = new Organization_Model_DbTable_OrgSettings();
            $this->_validation 	   = new Validations();
            $this->_dbPayslip = new Payroll_Model_DbTable_Payslip();
			
			$this->_compOffAccessRights   = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameA);
			$this->_compOffEmployeeAccess = $this->_compOffAccessRights['Employee'];
            
            $this->_compOffBalAccessRights   = $this->_dbAccessRights->employeeAccessRightsBasedOnFormId($this->_logEmpId, $this->_formIdB);
            $this->_compOffBalAccessRights1   = $this->_dbAccessRights->employeeAccessRightsBasedOnFormId($this->_logEmpId, $this->_formIdC);
			$this->_compOffBalEmployeeAccess = $this->_compOffBalAccessRights['Employee'];

            $this->_myTeamAccessRights      = $this->_dbAccessRights->employeeAccessRightsBasedOnFormId($this->_logEmpId,$this->_myTeamCompOffFormId);
            $this->_selfServiceAccessRights = $this->_dbAccessRights->employeeAccessRightsBasedOnFormId($this->_logEmpId,$this->_selfServiceCompOffFormId);

            $this->_myTeamAccess           = $this->_myTeamAccessRights['Employee'];
            $this->_selfServiceAccess      = $this->_selfServiceAccessRights['Employee'];

            $this->_dbLocation  = new Organization_Model_DbTable_Location();
            $this->_dbDept      = new Organization_Model_DbTable_Department();
            $this->_dbManager   = new Default_Model_DbTable_Manager();
		
			$this->_dbEmployee     = new Employees_Model_DbTable_Employee();
			if (Zend_Registry::isRegistered('orgDetails'))
				$this->_orgDetails = Zend_Registry::get('orgDetails');
		}
		else
		{
			if (Zend_Session::namespaceIsset('lastRequest'))
				Zend_Session:: namespaceUnset('lastRequest');
			
			$session = new Zend_Session_Namespace('lastRequest');
			$session->lastRequestUri = 'v3/employee-self-service/compensatory-off';
			$this->_redirect('auth');
		}
    }

    public function indexAction()
    {
        $checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

        if ($checkSessionAuth)
        {
            $this->_helper->layout()->disableLayout()->setLayout('admin_layout');
		
            $this->view->formNameA = $this->_formNameA;
            $this->view->formNameB = $this->_formNameB;
            
            $this->view->employeeDetails = $this->_dbCommonFun->listEmployeesDetails ('CompensatoryOff', '', $this->_logEmpId);

            $this->view->deptHierarchy= $this->_dbDept->getDepartmentTypes();
            $this->view->managerNames = $this->_dbManager->managerName('', '', 'Emp_First_Name', 'ASC', '', '', '', '', '', 'Employees', 0);
            $this->view->empLocation  = $this->_dbLocation->getLocationPair();
			$this->view->newImg       = $this->_basePath->baseUrl('images/new.png');
            $this->view->ideaBulb     = $this->_basePath->baseUrl('images/idea-bulb.webp');
            
            $this->view->compOffUser =  array('Is_Manager'=>$this->_compOffAccessRights['Employee']['Is_Manager'],
                                              'Add' => $this->_compOffAccessRights['Employee']['Add'],
                                              'View'=>$this->_compOffAccessRights['Employee']['View'],
                                              'Update'=>$this->_compOffAccessRights['Employee']['Update'],
                                              'Delete'=>$this->_compOffAccessRights['Employee']['Delete'],
                                              'Op_Choice'=>$this->_compOffAccessRights['Employee']['Optional_Choice'],
                                              'Admin'=>$this->_compOffAccessRights['Admin'],
                                              'Session_Id'    => $this->_logEmpId,
                                              'Employee_Name' => $this->_dbPersonal->employeeId($this->_logEmpId));
            
            $this->view->compOffBalUser =  array('Is_Manager'=>$this->_compOffBalAccessRights['Employee']['Is_Manager'],
                                              'View'=>$this->_compOffBalAccessRights['Employee']['View'],
                                              'Admin'=>$this->_compOffBalAccessRights['Admin'],
                                              'Employee_Name' => $this->_dbPersonal->employeeId($this->_logEmpId));		
            $this->view->compOffBalUser1 =  array('Is_Manager'=>$this->_compOffBalAccessRights1['Employee']['Is_Manager'],
                                              'View'=>$this->_compOffBalAccessRights1['Employee']['View'],
                                              'Admin'=>$this->_compOffBalAccessRights1['Admin'],
                                              'Employee_Name' => $this->_dbPersonal->employeeId($this->_logEmpId));		

            $this->view->orgDetails      = $this->_orgDetails;
            $this->view->leaveSettings   = $this->_dbCommonFun->getLeaveSettings();
			$this->view->serviceProvider = $this->_dbEmployee->getEmployeeServiceProviderList($this->_logEmpId);                                  
        
        } else {
			$this->_redirect('auth');
		}
    }
    
    public function listCompensatoryOffAction()
    {
        $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('list-compensatory-off', 'json')->initContext();
			
            if ($this->_compOffEmployeeAccess['View'] == 1)
            {
                $sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
				
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$empName = $this->_getParam('EmployeeName', null);
				$empName = filter_var($empName, FILTER_SANITIZE_STRIPPED);
                
                $compensatedDateStart = $this->_getParam('CompensatedDateStart', null);
				$compensatedDateStart = filter_var($compensatedDateStart, FILTER_SANITIZE_STRIPPED);
                
                $compensatedDateEnd = $this->_getParam('CompensatedDateEnd', null);
				$compensatedDateEnd = filter_var($compensatedDateEnd, FILTER_SANITIZE_STRIPPED);
                
                $duration = $this->_getParam('Duration', null);
				$duration = filter_var($duration, FILTER_SANITIZE_STRIPPED);
                
                $compOffDateStart = $this->_getParam('CompensatoryDateStart', null);
				$compOffDateStart = filter_var($compOffDateStart, FILTER_SANITIZE_STRIPPED);
                
                $compOffDateEnd = $this->_getParam('CompensatoryDateEnd', null);
				$compOffDateEnd = filter_var($compOffDateEnd, FILTER_SANITIZE_STRIPPED);

                $location = $this->_getParam('Location',null);
                $location = filter_var($location, FILTER_SANITIZE_NUMBER_INT);

                $department = $this->_getParam('Department',null);
                $department = filter_var($department, FILTER_SANITIZE_NUMBER_INT);
                
                $managerId = $this->_getParam('Manager_Id', null);
				$managerId = filter_var($managerId, FILTER_SANITIZE_NUMBER_INT);
                
                $status = $this->_getParam('Status', null);
				$status = filter_var($status, FILTER_SANITIZE_STRIPPED);

                $serviceProviderId = $this->_getParam('ServiceProviderId', null);
				$serviceProviderId = filter_var($serviceProviderId, FILTER_SANITIZE_NUMBER_INT);
                
                $compOffUser = array('Admin'    => $this->_compOffAccessRights['Admin'],
								   'Is_Manager' => $this->_compOffAccessRights['Employee']['Is_Manager'],
								   'Update'     => $this->_compOffAccessRights['Employee']['Update'],
								   'SessionId'  => $this->_logEmpId,
								   'Form_Name'  => $this->_formNameA);
                
                $compOff = array('EmployeeName'=>$empName,
                                'CompensatedDateStart'=> $compensatedDateStart,
                                'CompensatedDateEnd' => $compensatedDateEnd,
                                'Duration' => $duration,
                                'CompOffDateStart'=> $compOffDateStart,
                                'CompOffDateEnd' => $compOffDateEnd,
                                'Status' => $status,
                                'serviceProviderId'=> $serviceProviderId,
                                'Location_Id'    => $location,
                                'Department_Id'  => $department,
                                'Manager_Id'     => $managerId);
				
				$this->view->result = $this->_dbCompOff->listCompensatoryOff ($page, $rows, $sortField, $sortOrder,
                                                                                         $this->_logEmpId, $searchAll, $compOff,$compOffUser);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'compensatory-off', 'employees');
        }
    }
    
    
    public function getCompensatedDateAction()
    {
            $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('get-compensated-date', 'json')->initContext();
			
            if ($this->_compOffEmployeeAccess['View'] == 1 || $this->_myTeamAccess['View'] == 1 || $this->_selfServiceAccess['View'] == 1)
            {
                $empId = $this->_getParam('Employee_Id', null);
                
                $this->view->result = $this->_dbCompOff->getCompensatedDate ($empId);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'compensatory-off', 'employees');
        }
    }
    
    public function updateCompensatoryOffAction()
    {
        $this->_helper->layout()->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
        {
        	$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('update-compensatory-off', 'json')->initContext();
            
			$compOffId = $this->_getParam('Compensatory_Off_Id', null);
	 		$compOffId = $this->_validation->intValidation($compOffId);
    

            if ((empty($compOffId['value']) && ($this->_compOffEmployeeAccess['Add'] == 1 || $this->_myTeamAccess['Add'] == 1 || $this->_selfServiceAccess['Add'] == 1)) ||
				(!empty($compOffId['value']) && ($this->_compOffEmployeeAccess['Update'] == 1 || $this->_myTeamAccess['Update'] == 1 || $this->_selfServiceAccess['Update'] == 1)))
			{
                if ($this->getRequest()->isPost())
				{
                    $formData = $this->getRequest()->getPost();
                    
                    $employeeId        = $this->_validation->intValidation($formData['Employee_Id']);
                    $forwardTo         = $this->_validation->intValidation($formData['Forward_To']);
                    $compOffBalanceId  = $this->_validation->intValidation($formData['Comp_Off_Balance_Id']);
                    $duration          = $this->_validation->amountValidation($formData['Duration']);
                    $period          = $this->_validation->alphaValidation($formData['Period']);
                    $compFrom          = $this->_validation->dateValidation($formData['Compensatory_Date']);
                    $compStatus        = $this->_validation->alphaValidation($formData['Approval_Status']);
                    $comments           = $this->_validation->alphaNumSpCDotHySlashNewLineValidation($formData['Comment']);
                    $comments['valid']  = $this->_validation->lengthValidation($comments, 5, 3000, false);

                    $reason = isset($formData['reason']) ? $formData['reason'] : '';
                    $reason = $this->_validation->commonInputAlphaNumericValidation($reason);
                    $reason['valid']  = $this->_validation->lengthValidation($reason, 2, 500, false);

                    $documentNames = isset($formData['documentNames']) ? json_encode($formData['documentNames']) : NULL;

                    if($this->_myTeamAccessRights['Admin']=="admin" || $this->_compOffAccessRights['Admin']=="admin")
                    {
                        $adminRole = "admin";
                    }
                    else
                    {
                        $adminRole = '';
                    }
                    $validationOtherDetails = array(
                        'adminRole' => $adminRole,
                        'sessionId' => $this->_logEmpId,
                        'compOffFormIds' => array()
                    );
							
                    $checkCompOffHrs = $this->_dbCompOff->getCompOffHours($employeeId['value'], $compOffBalanceId['value'],$validationOtherDetails);
                    
                    $expireDt = $checkCompOffHrs['Expiry_Date'];
					$resignationDate = $checkCompOffHrs['resignationDate'];
                
                    if (!empty($employeeId['value']) && $employeeId['valid'] && !empty($forwardTo['value']) && $forwardTo['valid'] &&
                        !empty($compOffBalanceId['value']) && $compOffBalanceId['valid'] && !empty($duration['value']) && $duration['valid'] &&
                        !empty($compFrom['value']) && $compFrom['valid'] &&
                        (empty($comments['value']) || $comments['valid']) &&
                        (empty($reason['value']) || $reason['valid']))
                    {
                        if(empty($checkCompOffHrs['Validation_Error'])){
                            $compOffMinApplicableDate = strtotime($checkCompOffHrs['Compoff_Min_Date']);
                            $compOffMaxApplicableDate = strtotime($checkCompOffHrs['compOff_Max_Date']);
                           
                            if((strtotime($compFrom['value']) >= $compOffMinApplicableDate) && strtotime($compFrom['value']) <= $compOffMaxApplicableDate){
                                $leave = $this->_dbCompOff->compOffValidate($employeeId['value'],$compFrom['value'],$period['value']);
                                if($leave == 1)
                                {
                                    $customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
                                    $customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);

                                    $totalCompOffBalance = $this->_dbCompOff->getCompOffBalance($compOffId['value'],$compOffBalanceId['value']);
                                    
                                    
                                    if($totalCompOffBalance >= $duration['value'])
                                    {
                                        $payslipExist = $this->_dbCompOff->payslipExistForCompOffDate($employeeId['value'],$compFrom['value']);
                                        if($payslipExist == 1)
                                        {
                                            $this->view->result = array('success' => false, 'msg' => 'Payslip already generated for the compensatory off date', 'type' => "info");
                                        }
                                        else
                                        {
                                            $compOffData = array('Compensatory_Off_Id' => $compOffId['value'],
                                                            'Employee_Id' => $employeeId['value'],
                                                            'Approver_Id' => $forwardTo['value'],
                                                            'Comp_Off_Balance_Id' => $compOffBalanceId['value'],
                                                            'Duration' => $duration['value'],
                                                            'Period' => $period['value'],
                                                            'Compensatory_Date' => $compFrom['value'],
                                                            'Approval_Status' => $compStatus['value'],
                                                            'Reason'=> $reason['value'],
                                                            'Document_Names'=>$documentNames
                                                        );                                    
                                                                                            
                                            $result = $this->_dbCompOff->updateCompensatoryOff ($compOffData, $this->_logEmpId, $compOffId['value'],
                                                                                    $comments['value'], $customFormNamee);
                                            
                                            // mail notification
                                            if ($result['success']) {
                                                $compOffRecord = array();
                                                if($compOffId['value'] != 0) {
                                                    $compOffRecord  = $this->_dbCompOff->viewCompoffRequest($compOffId['value']);
                                                }                                    
                                                $mailCommunicationData = array(
                                                    'sessionId'         => $this->_logEmpId,
                                                    'employeeId'        => $employeeId['value'],
                                                    'customFormName'    => $customFormNamee,
                                                    'moduleName'        => 'Employees',
                                                    'formName'          => $this->_formNameA,
                                                    'formUrl'           => '/v3/employee-self-service/compensatory-off',
                                                    'alternatePerson'   => '',
                                                    'leaveFrom'         => '',
                                                    'leaveTo'           => '',
                                                    'empIdToBeMailed'   => $forwardTo['value'],
                                                    'action'            => $compOffId['value'] != 0 ? 'update' : 'add',
                                                    'record'            => $compOffRecord,
                                                );
                                                $result = $this->_dbCommonFun->addUpdateActionMailCommunication($mailCommunicationData);
                                            }
                                            $this->view->result = $result;
                                        }
                                    }
                                    else 
                                    {
                                        $durationValue = $duration['value'];
                                        $this->view->result = array('success' => false, 'msg' => "$customFormNamee request cannot be processed as the requested duration of $durationValue day(s) exceeds available balance of $totalCompOffBalance day(s).", 'type' => "info");
                                    }
                                }
                                else
                                {
                                    $this->view->result = $leave;
                                }
                            }else{
                                $minDateFormatted = date('d M Y', $compOffMinApplicableDate);
                                $maxDateFormatted = date('d M Y', $compOffMaxApplicableDate);

                                $this->view->result = array('success' => false, 'msg' => "Compensatory Off Date must be between the minimum date ({$minDateFormatted}) and the maximum date ({$maxDateFormatted}).", 'type' => 'info');   
                            }
                        }else{
                            $this->view->result = array('success' => false, 'msg' => $checkCompOffHrs['Validation_Error'], 'type' => 'info');   
                        }
                    }
                    else
                    {
                        $this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
                    }
                }
			}
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Sorry, Access denied', 'type' => 'warning');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'compensatory-off', 'employees');
        }
    }
    
    public function updateCompOffStatusAction()
    {
        $this->_helper->layout->disableLayout();
		
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('update-comp-off-status', 'json')->initContext();
			
            $compOffId = $this->_getParam('Compensatory_Off_Id', null);
            $compOffId = filter_var($compOffId, FILTER_SANITIZE_NUMBER_INT);
            $compOffId = $this->_validation->intValidation($compOffId);
            
            if (!empty($compOffId['value']) && $compOffId['valid'])
            {
                if ( $this->getRequest()->isPost() )
				{
					$formData          = $this->getRequest()->getPost();
					$status            = $this->_validation->alphaValidation($formData['Status']);
                    $comments          = $this->_validation->commonInputAlphaNumericValidation($formData['Comments']);
                    $comments['valid'] = $this->_validation->lengthValidation($comments, 2, 3000, false);
                    $employeeId        = $this->_validation->intValidation($formData['Employee_Id']);
                    $statusText        = $this->_validation->alphaNumSpValidation($formData['Status_Text']);
                    
					if (!empty($status['value']) && $status['valid'] && $comments['valid'])
					{
						$compOffData = array('Compensatory_Off_Id' => $compOffId['value'],
										   'Employee_Id'  => $employeeId['value'],
                                           'Status'       => $status['value'],
                                           'Status_Text'  => $statusText['value'],
										   'Comment'      => $comments['value']);
						
						$customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
						$customFormNamee = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
						
						$result = $this->_dbCompOff->compOffStatusUpdate ($compOffData, $this->_logEmpId, $this->_formNameA, $customFormNamee);
                        
                        if ($result['success']) {
                            $compOffRecord  = $this->_dbCompOff->viewCompoffRequest($compOffId['value']);

                            $attendanceSummaryTriggerStatus = array('Rejected','Cancelled');
                            if(in_array($compOffData['Status'],$attendanceSummaryTriggerStatus)){
                                $attendanceSummarySelectedDetails=[];
                                $attendanceSummarySelectedDetails[]=$compOffRecord;
                                $this->_dbCommonFun->triggerAttendanceSummaryStepFunction($attendanceSummarySelectedDetails,'compoff');
                            }

                            $mailCommunicationData = array(
                                'sessionId'         => $this->_logEmpId,
                                'addedBy'           => $compOffRecord['Added_By'],
                                'employeeId'        => $employeeId['value'],
                                'customFormName'    => $customFormNamee,
                                'status'            => $status['value'],
                                'employeeName'      => $compOffRecord['Employee_Name'],
                                'formName'          => $this->_formNameA,
                                'actionName'        => $status === 'Cancel Applied' ? 'cancel request applied' : 'status updated',
                                'approverEmpId'     => $compOffRecord['Approver_Id'],
                                'leaveAction'       => '',
                                'moduleName'        => 'Employees',
                                'formUrl'           => '/v3/employee-self-service/compensatory-off',
                            );
                            $result = $this->_dbCommonFun->statusUpdateMailCommunication($mailCommunicationData);
                        }
                        $this->view->result = $result;
                    }
					else
					{
						$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
					}
				}
            }
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Sorry, Access denied', 'type' => 'warning');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'compensatory-off', 'employees');
        }
    }
    
    
    public function deleteCompensatoryOffAction()
    {
        $this->_helper->layout->disableLayout();
        
        if(isset($_SERVER['HTTP_REFERER']))
        {
            $compOffId = $this->_getParam('compOffId');
            $compOffId = filter_var($compOffId, FILTER_SANITIZE_NUMBER_INT);
            
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('delete-compensatory-off', 'json')->initContext();
            
			if ($this->_compOffEmployeeAccess['Delete'] == 1 || $this->_myTeamAccess['Delete'] == 1)
            {
                if ($compOffId > 0)
                {
					$customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
					$formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
				
                    $this->view->result = $this->_dbCompOff->deleteCompOff($compOffId, $this->_logEmpId, $this->_formNameA, $formName);
                }
                else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
				}
            }
            else
			{
				$this->view->result = array('success' => false, 'msg' => 'Access denied', 'type' => 'warning');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'compensatory-off', 'employees');
        }
    }
    
    public function getCompoffHoursAction()
    {
        $this->_helper->layout->disableLayout();
        
        if(isset($_SERVER['HTTP_REFERER']))
        {
            $empId = $this->_getParam('Employee_Id');
            $empId = filter_var($empId, FILTER_SANITIZE_NUMBER_INT);
            
            $compOffBalanceId = $this->_getParam('Comp_Off_Balance_Id');
            $compOffBalanceId = filter_var($compOffBalanceId, FILTER_SANITIZE_NUMBER_INT);
            
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('get-compoff-hours', 'json')->initContext();
            
			if (!empty($empId) && !empty($compOffBalanceId))
            {
               	$customFormName = $this->_ehrTables->getCustomForms($this->_formNameA);
                $formName = ((!empty($customFormName) && !empty($customFormName['New_Form_Name'])) ? $customFormName['New_Form_Name'] : $this->_formNameA);
                 
                $validationOtherDetails = array(
                    'adminRole' => '',
                    'sessionId' => $this->_logEmpId,
                    'compOffFormIds' => array($this->_myTeamCompOffFormId,139)
                );

                $this->view->result = $this->_dbCompOff->getCompOffHours($empId, $compOffBalanceId,$validationOtherDetails);               
            }
            else
			{
				$this->view->result = array('success' => false, 'msg' => 'Access denied', 'type' => 'warning');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'compensatory-off', 'employees');
        }
    }
    
    public function listCompOffEmployeesAction()
    {
        $this->_helper->layout->disableLayout();
        
        if(isset($_SERVER['HTTP_REFERER']))
        {
         	$ajaxContext = $this->_helper->getHelper('AjaxContext');
            $ajaxContext->addActionContext('list-comp-off-employees', 'json')->initContext();
            
			$this->view->result = $this->_dbCommonFun->listEmployeesDetails ('CompensatoryOff', '', $this->_logEmpId);
        }
        else
        {
            $this->_helper->redirector('index', 'compensatory-off', 'employees');
        }
    }
    
    
    
    /***********************Compensatory Off Balance *******************/
    public function listCompOffBalanceAction()
    {
        $this->_helper->layout()->disableLayout();
        
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('list-comp-off-balance', 'json')->initContext();
			
            if ($this->_compOffBalEmployeeAccess['View'] == 1)
            {
                $sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
				
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$empName = $this->_getParam('EmployeeName', null);
				$empName = filter_var($empName, FILTER_SANITIZE_STRIPPED);

                $compOffDateStart = $this->_getParam('CompOffDateStart', null);
				$compOffDateStart = filter_var($compOffDateStart, FILTER_SANITIZE_STRIPPED);
                
                $compOffDateEnd = $this->_getParam('CompOffDateEnd', null);
				$compOffDateEnd = filter_var($compOffDateEnd, FILTER_SANITIZE_STRIPPED);
                
                $totalDaysStart = $this->_getParam('TotalDaysStart', null);
				$totalDaysStart = filter_var($totalDaysStart, FILTER_SANITIZE_STRIPPED);
                
                $totalDaysEnd = $this->_getParam('TotalDaysEnd', null);
				$totalDaysEnd = filter_var($totalDaysEnd, FILTER_SANITIZE_STRIPPED);
                
                $remainingDaysStart = $this->_getParam('RemainingDaysStart', null);
				$remainingDaysStart = filter_var($remainingDaysStart, FILTER_SANITIZE_STRIPPED);
                
                $remainingDaysEnd = $this->_getParam('RemainingDaysEnd', null);
				$remainingDaysEnd = filter_var($remainingDaysStart, FILTER_SANITIZE_STRIPPED);

                $serviceProviderId = $this->_getParam('ServiceProviderId', null);
				$serviceProviderId = filter_var($serviceProviderId, FILTER_SANITIZE_NUMBER_INT);

                $location = $this->_getParam('Location',null);
                $location = filter_var($location, FILTER_SANITIZE_NUMBER_INT);

                $department = $this->_getParam('Department',null);
                $department = filter_var($department, FILTER_SANITIZE_NUMBER_INT);
                
                $compOffBalUser = array('Admin'    => $this->_compOffBalAccessRights['Admin'],
								   'Is_Manager' => $this->_compOffBalAccessRights['Employee']['Is_Manager'],
								   'SessionId'  => $this->_logEmpId,
								   'Form_Name'  => $this->_formNameB);
                
                $compOffBalArr = array('EmployeeName' => $empName,
                                       'CompOffDateStart' => $compOffDateStart,
                                       'CompOffDateEnd' => $compOffDateEnd,
                                       'TotalDaysStart' => $totalDaysStart,
                                       'TotalDaysEnd' => $totalDaysEnd,
                                       'RemainingDaysStart' => $remainingDaysStart,
                                       'RemainingDaysEnd' => $remainingDaysEnd,
                                       'serviceProviderId'=> $serviceProviderId,
                                       'Location_Id'    => $location,
                                       'Department_Id'  => $department                          
                                    );
				
				$this->view->result = $this->_dbCompOff->listCompensatoryOffBalance ($page, $rows, $sortField, $sortOrder,
                                                                                         $this->_logEmpId, $searchAll, $compOffBalArr,$compOffBalUser);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'document-template-engine', 'forms-manager');
        }
    }
    
    public function __destruct()
    {
        
    }

}

