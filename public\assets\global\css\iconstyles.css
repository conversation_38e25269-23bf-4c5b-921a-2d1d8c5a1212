@font-face {
  font-family: 'hrapp';
  src:  url('fonts/hrapp.eot?491sq2');
  src:  url('fonts/hrapp.eot?491sq2#iefix') format('embedded-opentype'),
    url('fonts/hrapp.ttf?491sq2') format('truetype'),
    url('fonts/hrapp.woff?491sq2') format('woff'),
    url('fonts/hrapp.svg?491sq2#hrapp') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="hr-"], [class*=" hr-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'hrapp' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.hr-tax-and-statutory-compliance-statutory-components:before {
  content: "\edd6";
}
.hr-employee-self-service-organization-chart:before {
  content: "\edd5";
}
.hr-asset-management-asset-management:before {
  content: "\edd4";
}
.hr-approvals:before {
  content: "\e900";
}
.hr-approvals-approval-management:before {
  content: "\e901";
}
.hr-my-finance-my-pay:before {
  content: "\e902";
}
.hr-my-finance-travel-and-expenses:before {
  content: "\e903";
}
.hr-my-finance:before {
  content: "\e904";
}
.hr-payroll-payroll-management:before {
  content: "\e905";
}
.hr-my-team-travel-and-expenses:before {
  content: "\e906";
}
.hr-my-team-payroll-management:before {
  content: "\e907";
}
.hr-employee-self-service-time-off:before {
  content: "\e908";
}
.hr-my-team-time-off:before {
  content: "\e909";
}
.hr-core-hr-payroll-data-management:before {
  content: "\e90a";
}
.hr-employee-self-service-attendance:before {
  content: "\e90b";
}
.hr-my-team-attendance:before {
  content: "\e90c";
}
.hr-my-team-exit-management:before {
  content: "\e90d";
}
.hr-px:before {
  content: "\e90e";
}
.hr-account-code:before {
  content: "\e90f";
}
.hr-account-schema-mapping:before {
  content: "\e910";
}
.hr-account-type:before {
  content: "\e911";
}
.hr-add:before {
  content: "\e912";
}
.hr-address-book:before {
  content: "\e913";
}
.hr-address-book-o:before {
  content: "\e914";
}
.hr-address-card:before {
  content: "\e915";
}
.hr-address-card-o:before {
  content: "\e916";
}
.hr-adjust:before {
  content: "\e917";
}
.hr-admin-roles:before {
  content: "\e918";
}
.hr-adn:before {
  content: "\e919";
}
.hr-airport-plane:before {
  content: "\e91a";
}
.hr-alert:before {
  content: "\e91b";
}
.hr-alert-1:before {
  content: "\e91c";
}
.hr-alert-2:before {
  content: "\e91d";
}
.hr-alert-3:before {
  content: "\e91e";
}
.hr-alert-circled:before {
  content: "\e91f";
}
.hr-align-center:before {
  content: "\e920";
}
.hr-align-justify:before {
  content: "\e921";
}
.hr-align-left:before {
  content: "\e922";
}
.hr-align-right:before {
  content: "\e923";
}
.hr-amazon:before {
  content: "\e924";
}
.hr-ambulance:before {
  content: "\e925";
}
.hr-american-sign-language-interpreting:before {
  content: "\e926";
}
.hr-analytics-file-1:before {
  content: "\e927";
}
.hr-anchor:before {
  content: "\e928";
}
.hr-android:before {
  content: "\e929";
}
.hr-android-attach:before {
  content: "\e92a";
}
.hr-android-bulb:before {
  content: "\e92b";
}
.hr-android-done:before {
  content: "\e92c";
}
.hr-android-done-all:before {
  content: "\e92d";
}
.hr-angellist:before {
  content: "\e92e";
}
.hr-angle-double-down:before {
  content: "\e92f";
}
.hr-angle-double-left:before {
  content: "\e930";
}
.hr-angle-double-right:before {
  content: "\e931";
}
.hr-angle-double-up:before {
  content: "\e932";
}
.hr-angle-down:before {
  content: "\e933";
}
.hr-angle-left:before {
  content: "\e934";
}
.hr-angle-right:before {
  content: "\e935";
}
.hr-angle-up:before {
  content: "\e936";
}
.hr-apple:before {
  content: "\e937";
}
.hr-archive:before {
  content: "\e938";
}
.hr-area-chart:before {
  content: "\e939";
}
.hr-arrow-circle-down:before {
  content: "\e93a";
}
.hr-arrow-circle-left:before {
  content: "\e93b";
}
.hr-arrow-circle-o-down:before {
  content: "\e93c";
}
.hr-arrow-circle-o-left:before {
  content: "\e93d";
}
.hr-arrow-circle-o-right:before {
  content: "\e93e";
}
.hr-arrow-circle-o-up:before {
  content: "\e93f";
}
.hr-arrow-circle-right:before {
  content: "\e940";
}
.hr-arrow-circle-up:before {
  content: "\e941";
}
.hr-arrow-down:before {
  content: "\e942";
}
.hr-arrow-left:before {
  content: "\e943";
}
.hr-arrow-right:before {
  content: "\e944";
}
.hr-arrows:before {
  content: "\e945";
}
.hr-arrows-alt:before {
  content: "\e946";
}
.hr-arrows-h:before {
  content: "\e947";
}
.hr-arrows-v:before {
  content: "\e948";
}
.hr-arrow-up:before {
  content: "\e949";
}
.hr-asset-management:before {
  content: "\e94a";
}
.hr-asset-management-assets:before {
  content: "\e94b";
}
.hr-assistive-listening-systems:before {
  content: "\e94c";
}
.hr-asterisk:before {
  content: "\e94d";
}
.hr-at:before {
  content: "\e94e";
}
.hr-attendance-box:before {
  content: "\e94f";
}
.hr-audio-description:before {
  content: "\e950";
}
.hr-automobile:before {
  content: "\e951";
}
.hr-backward:before {
  content: "\e952";
}
.hr-balance-scale:before {
  content: "\e953";
}
.hr-ban:before {
  content: "\e954";
}
.hr-ban1:before {
  content: "\e955";
}
.hr-bandcamp:before {
  content: "\e956";
}
.hr-bank:before {
  content: "\e957";
}
.hr-bar-chart:before {
  content: "\e958";
}
.hr-barcode:before {
  content: "\e959";
}
.hr-bars:before {
  content: "\e95a";
}
.hr-bath:before {
  content: "\e95b";
}
.hr-battery:before {
  content: "\e95c";
}
.hr-battery-0:before {
  content: "\e95d";
}
.hr-battery-1:before {
  content: "\e95e";
}
.hr-battery-2:before {
  content: "\e95f";
}
.hr-battery-3:before {
  content: "\e960";
}
.hr-bed:before {
  content: "\e961";
}
.hr-beer:before {
  content: "\e962";
}
.hr-behance:before {
  content: "\e963";
}
.hr-behance-square:before {
  content: "\e964";
}
.hr-bell:before {
  content: "\e965";
}
.hr-bell1:before {
  content: "\e966";
}
.hr-bell-1:before {
  content: "\e967";
}
.hr-bell-o:before {
  content: "\e968";
}
.hr-bell-o1:before {
  content: "\e969";
}
.hr-bell-slash:before {
  content: "\e96a";
}
.hr-bell-slash-o:before {
  content: "\e96b";
}
.hr-bell-two:before {
  content: "\e96c";
}
.hr-bicycle:before {
  content: "\e96d";
}
.hr-billing:before {
  content: "\e96e";
}
.hr-billing-billing:before {
  content: "\e96f";
}
.hr-binoculars:before {
  content: "\e970";
}
.hr-birthday-cake:before {
  content: "\e971";
}
.hr-birthday-cake1:before {
  content: "\e972";
}
.hr-bitbucket:before {
  content: "\e973";
}
.hr-bitbucket-square:before {
  content: "\e974";
}
.hr-bitcoin:before {
  content: "\e975";
}
.hr-black-tie:before {
  content: "\e976";
}
.hr-blind:before {
  content: "\e977";
}
.hr-bluetooth:before {
  content: "\e978";
}
.hr-bluetooth-b:before {
  content: "\e979";
}
.hr-bold:before {
  content: "\e97a";
}
.hr-bolt:before {
  content: "\e97b";
}
.hr-bomb:before {
  content: "\e97c";
}
.hr-book:before {
  content: "\e97d";
}
.hr-bookmark:before {
  content: "\e97e";
}
.hr-bookmark-o:before {
  content: "\e97f";
}
.hr-braille:before {
  content: "\e980";
}
.hr-briefcase:before {
  content: "\e981";
}
.hr-broadcast:before {
  content: "\e982";
}
.hr-bug:before {
  content: "\e983";
}
.hr-bug1:before {
  content: "\e984";
}
.hr-building:before {
  content: "\e985";
}
.hr-building-o:before {
  content: "\e986";
}
.hr-bulk-copy:before {
  content: "\e987";
}
.hr-bullhorn:before {
  content: "\e988";
}
.hr-bullseye:before {
  content: "\e989";
}
.hr-bus:before {
  content: "\e98a";
}
.hr-buysellads:before {
  content: "\e98b";
}
.hr-cab:before {
  content: "\e98c";
}
.hr-calculator:before {
  content: "\e98d";
}
.hr-calculator1:before {
  content: "\e98e";
}
.hr-calendar:before {
  content: "\e98f";
}
.hr-calendar1:before {
  content: "\e990";
}
.hr-calendar-1:before {
  content: "\e991";
}
.hr-calendar-alt-fill:before {
  content: "\e992";
}
.hr-calendar-check-o:before {
  content: "\e993";
}
.hr-calendar-minus-o:before {
  content: "\e994";
}
.hr-calendar-o:before {
  content: "\e995";
}
.hr-calendar-plus-o:before {
  content: "\e996";
}
.hr-calendar-times-o:before {
  content: "\e997";
}
.hr-camera:before {
  content: "\e998";
}
.hr-camera-retro:before {
  content: "\e999";
}
.hr-cancel-circle:before {
  content: "\e99a";
}
.hr-caret-down:before {
  content: "\e99b";
}
.hr-caret-left:before {
  content: "\e99c";
}
.hr-caret-right:before {
  content: "\e99d";
}
.hr-caret-square-o-down:before {
  content: "\e99e";
}
.hr-caret-square-o-left:before {
  content: "\e99f";
}
.hr-caret-square-o-right:before {
  content: "\e9a0";
}
.hr-caret-square-o-up:before {
  content: "\e9a1";
}
.hr-caret-up:before {
  content: "\e9a2";
}
.hr-cart-arrow-down:before {
  content: "\e9a3";
}
.hr-cart-plus:before {
  content: "\e9a4";
}
.hr-cc:before {
  content: "\e9a5";
}
.hr-cc-amex:before {
  content: "\e9a6";
}
.hr-cc-diners-club:before {
  content: "\e9a7";
}
.hr-cc-discover:before {
  content: "\e9a8";
}
.hr-cc-jcb:before {
  content: "\e9a9";
}
.hr-cc-mastercard:before {
  content: "\e9aa";
}
.hr-cc-paypal:before {
  content: "\e9ab";
}
.hr-cc-stripe:before {
  content: "\e9ac";
}
.hr-cc-visa:before {
  content: "\e9ad";
}
.hr-certificate:before {
  content: "\e9ae";
}
.hr-chain:before {
  content: "\e9af";
}
.hr-chain-broken:before {
  content: "\e9b0";
}
.hr-check:before {
  content: "\e9b1";
}
.hr-check1:before {
  content: "\e9b2";
}
.hr-check-all:before {
  content: "\e9b3";
}
.hr-check-circle:before {
  content: "\e9b4";
}
.hr-check-circle1:before {
  content: "\e9b5";
}
.hr-check-circle-o:before {
  content: "\e9b6";
}
.hr-check-square:before {
  content: "\e9b7";
}
.hr-check-square-o:before {
  content: "\e9b8";
}
.hr-chevron-circle-down:before {
  content: "\e9b9";
}
.hr-chevron-circle-left:before {
  content: "\e9ba";
}
.hr-chevron-circle-right:before {
  content: "\e9bb";
}
.hr-chevron-circle-up:before {
  content: "\e9bc";
}
.hr-chevron-down:before {
  content: "\e9bd";
}
.hr-chevron-left:before {
  content: "\e9be";
}
.hr-chevron-right:before {
  content: "\e9bf";
}
.hr-chevron-up:before {
  content: "\e9c0";
}
.hr-child:before {
  content: "\e9c1";
}
.hr-chrome:before {
  content: "\e9c2";
}
.hr-circle:before {
  content: "\e9c3";
}
.hr-circle-o:before {
  content: "\e9c4";
}
.hr-circle-o-notch:before {
  content: "\e9c5";
}
.hr-circle-thin:before {
  content: "\e9c6";
}
.hr-clipboard:before {
  content: "\e9c7";
}
.hr-clock-o:before {
  content: "\e9c8";
}
.hr-clone:before {
  content: "\e9c9";
}
.hr-clone-roles:before {
  content: "\e9ca";
}
.hr-close:before {
  content: "\e9cb";
}
.hr-close1:before {
  content: "\e9cc";
}
.hr-close-1:before {
  content: "\e9cd";
}
.hr-close-round:before {
  content: "\e9ce";
}
.hr-cloud:before {
  content: "\e9cf";
}
.hr-cloud-download:before {
  content: "\e9d0";
}
.hr-cloud-download1:before {
  content: "\e9d1";
}
.hr-cloud-upload:before {
  content: "\e9d2";
}
.hr-cloud-upload1:before {
  content: "\e9d3";
}
.hr-cny:before {
  content: "\e9d4";
}
.hr-code:before {
  content: "\e9d5";
}
.hr-code-fork:before {
  content: "\e9d6";
}
.hr-codepen:before {
  content: "\e9d7";
}
.hr-codiepie:before {
  content: "\e9d8";
}
.hr-coffee:before {
  content: "\e9d9";
}
.hr-cog:before {
  content: "\e9da";
}
.hr-cogs:before {
  content: "\e9db";
}
.hr-cog-solid:before {
  content: "\e9dc";
}
.hr-collapse:before {
  content: "\e9dd";
}
.hr-columns:before {
  content: "\e9de";
}
.hr-comment:before {
  content: "\e9df";
}
.hr-commenting:before {
  content: "\e9e0";
}
.hr-commenting-o:before {
  content: "\e9e1";
}
.hr-comment-o:before {
  content: "\e9e2";
}
.hr-comments:before {
  content: "\e9e3";
}
.hr-comments-o:before {
  content: "\e9e4";
}
.hr-compass:before {
  content: "\e9e5";
}
.hr-compress:before {
  content: "\e9e6";
}
.hr-connectdevelop:before {
  content: "\e9e7";
}
.hr-contacs:before {
  content: "\e9e8";
}
.hr-contact:before {
  content: "\e9e9";
}
.hr-contact-2:before {
  content: "\e9ea";
}
.hr-contact-add:before {
  content: "\e9eb";
}
.hr-contact-add-2:before {
  content: "\e9ec";
}
.hr-contact-add-3:before {
  content: "\e9ed";
}
.hr-contact-big:before {
  content: "\e9ee";
}
.hr-contact-details:before {
  content: "\e9ef";
}
.hr-contacts:before {
  content: "\e9f0";
}
.hr-contao:before {
  content: "\e9f1";
}
.hr-copy:before {
  content: "\e9f2";
}
.hr-copy1:before {
  content: "\e9f3";
}
.hr-copyright:before {
  content: "\e9f4";
}
.hr-creative-commons:before {
  content: "\e9f5";
}
.hr-credit-card:before {
  content: "\e9f6";
}
.hr-credit-card-alt:before {
  content: "\e9f7";
}
.hr-crop:before {
  content: "\e9f8";
}
.hr-crosshairs:before {
  content: "\e9f9";
}
.hr-cross-mark:before {
  content: "\e9fa";
}
.hr-crown-king-streamline:before {
  content: "\e9fb";
}
.hr-css3:before {
  content: "\e9fc";
}
.hr-csv:before {
  content: "\e9fd";
}
.hr-cube:before {
  content: "\e9fe";
}
.hr-cubes:before {
  content: "\e9ff";
}
.hr-cut:before {
  content: "\ea00";
}
.hr-cutlery:before {
  content: "\ea01";
}
.hr-danger:before {
  content: "\ea02";
}
.hr-dashboard:before {
  content: "\ea03";
}
.hr-dashcube:before {
  content: "\ea04";
}
.hr-database:before {
  content: "\ea05";
}
.hr-database1:before {
  content: "\ea06";
}
.hr-database-add:before {
  content: "\ea07";
}
.hr-database-edit:before {
  content: "\ea08";
}
.hr-database-information:before {
  content: "\ea09";
}
.hr-database-remove:before {
  content: "\ea0a";
}
.hr-database-run:before {
  content: "\ea0b";
}
.hr-database-security:before {
  content: "\ea0c";
}
.hr-deaf:before {
  content: "\ea0d";
}
.hr-dedent:before {
  content: "\ea0e";
}
.hr-delete:before {
  content: "\ea0f";
}
.hr-delicious:before {
  content: "\ea10";
}
.hr-department:before {
  content: "\ea11";
}
.hr-desktop:before {
  content: "\ea12";
}
.hr-deviantart:before {
  content: "\ea13";
}
.hr-diamond:before {
  content: "\ea14";
}
.hr-diff:before {
  content: "\ea15";
}
.hr-digg:before {
  content: "\ea16";
}
.hr-document-sans-accept:before {
  content: "\ea17";
}
.hr-document-sans-add:before {
  content: "\ea18";
}
.hr-document-sans-cancel:before {
  content: "\ea19";
}
.hr-document-sans-down:before {
  content: "\ea1a";
}
.hr-document-sans-edit:before {
  content: "\ea1b";
}
.hr-document-sans-information:before {
  content: "\ea1c";
}
.hr-document-sans-remove:before {
  content: "\ea1d";
}
.hr-document-sans-run:before {
  content: "\ea1e";
}
.hr-document-sans-security:before {
  content: "\ea1f";
}
.hr-document-sans-settings:before {
  content: "\ea20";
}
.hr-document-sans-up:before {
  content: "\ea21";
}
.hr-document-text:before {
  content: "\ea22";
}
.hr-document-text-accept:before {
  content: "\ea23";
}
.hr-document-text-add:before {
  content: "\ea24";
}
.hr-document-text-down:before {
  content: "\ea25";
}
.hr-document-text-information:before {
  content: "\ea26";
}
.hr-document-text-remove:before {
  content: "\ea27";
}
.hr-document-text-run:before {
  content: "\ea28";
}
.hr-document-text-security:before {
  content: "\ea29";
}
.hr-document-text-up:before {
  content: "\ea2a";
}
.hr-dollar:before {
  content: "\ea2b";
}
.hr-donate:before {
  content: "\ea2c";
}
.hr-dot-circle-o:before {
  content: "\ea2d";
}
.hr-download:before {
  content: "\ea2e";
}
.hr-download1:before {
  content: "\ea2f";
}
.hr-download-1:before {
  content: "\ea30";
}
.hr-download-2:before {
  content: "\ea31";
}
.hr-download-accept:before {
  content: "\ea32";
}
.hr-download-cancel:before {
  content: "\ea33";
}
.hr-download-information:before {
  content: "\ea34";
}
.hr-dribbble:before {
  content: "\ea35";
}
.hr-drivers-license:before {
  content: "\ea36";
}
.hr-drivers-license-o:before {
  content: "\ea37";
}
.hr-dropbox:before {
  content: "\ea38";
}
.hr-drupal:before {
  content: "\ea39";
}
.hr-edge:before {
  content: "\ea3a";
}
.hr-edit:before {
  content: "\ea3b";
}
.hr-edit1:before {
  content: "\ea3c";
}
.hr-eercast:before {
  content: "\ea3d";
}
.hr-eject:before {
  content: "\ea3e";
}
.hr-electric-no-off:before {
  content: "\ea3f";
}
.hr-ellipsis-h:before {
  content: "\ea40";
}
.hr-ellipsis-v:before {
  content: "\ea41";
}
.hr-empire:before {
  content: "\ea42";
}
.hr-employee:before {
  content: "\ea43";
}
.hr-employee-awards-1:before {
  content: "\ea44";
}
.hr-employee-clone:before {
  content: "\ea45";
}
.hr-employee-confirmation:before {
  content: "\ea46";
}
.hr-employee-designation:before {
  content: "\ea47";
}
.hr-employee-probation:before {
  content: "\ea48";
}
.hr-employees-assignments:before {
  content: "\ea49";
}
.hr-employees-attendance:before {
  content: "\ea4a";
}
.hr-employees-awards:before {
  content: "\ea4b";
}
.hr-employees-compensatory-off:before {
  content: "\ea4c";
}
.hr-employees-complaints:before {
  content: "\ea4d";
}
.hr-employees-custom-employee-groups:before {
  content: "\ea4e";
}
.hr-employees-designations:before {
  content: "\ea4f";
}
.hr-employees-employee-bank-account:before {
  content: "\ea50";
}
.hr-employees-employees:before {
  content: "\ea51";
}
.hr-employees-employees-document-upload:before {
  content: "\ea52";
}
.hr-employees-employee-travel:before {
  content: "\ea53";
}
.hr-employees-employee-type:before {
  content: "\ea54";
}
.hr-employees-grades:before {
  content: "\ea55";
}
.hr-employees-inbox:before {
  content: "\ea56";
}
.hr-employee-skillset:before {
  content: "\ea57";
}
.hr-employee-skillset-1:before {
  content: "\ea58";
}
.hr-employees-leaves:before {
  content: "\ea59";
}
.hr-employees-leave-types:before {
  content: "\ea5a";
}
.hr-employees-memos:before {
  content: "\ea5b";
}
.hr-employees-organization-chart:before {
  content: "\ea5c";
}
.hr-employees-organization-chart-fit-horizontal:before {
  content: "\ea5d";
}
.hr-employees-organization-chart-fit-reset:before {
  content: "\ea5e";
}
.hr-employees-organization-chart-fit-vertical:before {
  content: "\ea5f";
}
.hr-employees-organization-chart-reset-hierarchy-path:before {
  content: "\ea60";
}
.hr-employees-organization-chart-view-hierarchy-path:before {
  content: "\ea61";
}
.hr-performance-management-performance-evaluation:before {
  content: "\ea62";
}
.hr-employees-reports:before {
  content: "\ea63";
}
.hr-employees-resignation:before {
  content: "\ea64";
}
.hr-employees-roles-template:before {
  content: "\ea65";
}
.hr-employees-short-time-off:before {
  content: "\ea66";
}
.hr-employees-skillset-assessment:before {
  content: "\ea67";
}
.hr-employees-timesheet-hours:before {
  content: "\ea68";
}
.hr-employee-self-service-timesheets:before {
  content: "\ea69";
}
.hr-employees-transfer:before {
  content: "\ea6a";
}
.hr-employees-warnings:before {
  content: "\ea6b";
}
.hr-enterprise:before {
  content: "\ea6c";
}
.hr-entrance:before {
  content: "\ea6d";
}
.hr-envelope:before {
  content: "\ea6e";
}
.hr-envelope-o:before {
  content: "\ea6f";
}
.hr-envelope-open:before {
  content: "\ea70";
}
.hr-envelope-open-o:before {
  content: "\ea71";
}
.hr-envelope-square:before {
  content: "\ea72";
}
.hr-envira:before {
  content: "\ea73";
}
.hr-eraser:before {
  content: "\ea74";
}
.hr-etsy:before {
  content: "\ea75";
}
.hr-eur:before {
  content: "\ea76";
}
.hr-exchange:before {
  content: "\ea77";
}
.hr-exclamation:before {
  content: "\ea78";
}
.hr-exclamation1:before {
  content: "\ea79";
}
.hr-exclamation-1:before {
  content: "\ea7a";
}
.hr-exclamation-2:before {
  content: "\ea7b";
}
.hr-exclamation-3:before {
  content: "\ea7c";
}
.hr-exclamation-circle:before {
  content: "\ea7d";
}
.hr-exclamation-circle1:before {
  content: "\ea7e";
}
.hr-exclamation-circle-1:before {
  content: "\ea7f";
}
.hr-exclamation-triangle:before {
  content: "\ea80";
}
.hr-exclamation-triangle1:before {
  content: "\ea81";
}
.hr-exclamation-triangle-1:before {
  content: "\ea82";
}
.hr-exclude-break-hours:before {
  content: "\ea83";
}
.hr-expand:before {
  content: "\ea84";
}
.hr-expand1:before {
  content: "\ea85";
}
.hr-expeditedssl:before {
  content: "\ea86";
}
.hr-external-link:before {
  content: "\ea87";
}
.hr-external-link-square:before {
  content: "\ea88";
}
.hr-eye:before {
  content: "\ea89";
}
.hr-eyedropper:before {
  content: "\ea8a";
}
.hr-eye-slash:before {
  content: "\ea8b";
}
.hr-fa:before {
  content: "\ea8c";
}
.hr-facebook:before {
  content: "\ea8d";
}
.hr-facebook-official:before {
  content: "\ea8e";
}
.hr-facebook-square:before {
  content: "\ea8f";
}
.hr-fast-backward:before {
  content: "\ea90";
}
.hr-fast-forward:before {
  content: "\ea91";
}
.hr-fax:before {
  content: "\ea92";
}
.hr-feed:before {
  content: "\ea93";
}
.hr-female:before {
  content: "\ea94";
}
.hr-female1:before {
  content: "\ea95";
}
.hr-female-rounded-1:before {
  content: "\ea96";
}
.hr-fighter-jet:before {
  content: "\ea97";
}
.hr-file:before {
  content: "\ea98";
}
.hr-file-archive-o:before {
  content: "\ea99";
}
.hr-file-audio-o:before {
  content: "\ea9a";
}
.hr-file-code-o:before {
  content: "\ea9b";
}
.hr-file-excel-o:before {
  content: "\ea9c";
}
.hr-file-image-o:before {
  content: "\ea9d";
}
.hr-file-movie-o:before {
  content: "\ea9e";
}
.hr-file-o:before {
  content: "\ea9f";
}
.hr-file-pdf-o:before {
  content: "\eaa0";
}
.hr-file-powerpoint-o:before {
  content: "\eaa1";
}
.hr-file-submodule:before {
  content: "\eaa2";
}
.hr-file-symlink-directory:before {
  content: "\eaa3";
}
.hr-file-text:before {
  content: "\eaa4";
}
.hr-file-text-o:before {
  content: "\eaa5";
}
.hr-file-word-o:before {
  content: "\eaa6";
}
.hr-film:before {
  content: "\eaa7";
}
.hr-filter:before {
  content: "\eaa8";
}
.hr-financeclosure:before {
  content: "\eaa9";
}
.hr-financial-closure:before {
  content: "\eaaa";
}
.hr-fire:before {
  content: "\eaab";
}
.hr-fire-extinguisher:before {
  content: "\eaac";
}
.hr-firefox:before {
  content: "\eaad";
}
.hr-first-order:before {
  content: "\eaae";
}
.hr-flag:before {
  content: "\eaaf";
}
.hr-flag-checkered:before {
  content: "\eab0";
}
.hr-flag-o:before {
  content: "\eab1";
}
.hr-flask:before {
  content: "\eab2";
}
.hr-flickr:before {
  content: "\eab3";
}
.hr-floppy-disk:before {
  content: "\eab4";
}
.hr-floppy-o:before {
  content: "\eab5";
}
.hr-folder:before {
  content: "\eab6";
}
.hr-folder-downloads:before {
  content: "\eab7";
}
.hr-folder-image:before {
  content: "\eab8";
}
.hr-folder-music:before {
  content: "\eab9";
}
.hr-folder-o:before {
  content: "\eaba";
}
.hr-folder-open:before {
  content: "\eabb";
}
.hr-folder-open-o:before {
  content: "\eabc";
}
.hr-folder-sans:before {
  content: "\eabd";
}
.hr-folder-sans-accept:before {
  content: "\eabe";
}
.hr-folder-sans-add:before {
  content: "\eabf";
}
.hr-folder-sans-cancel:before {
  content: "\eac0";
}
.hr-folder-sans-down:before {
  content: "\eac1";
}
.hr-folder-sans-edit:before {
  content: "\eac2";
}
.hr-folder-sans-information:before {
  content: "\eac3";
}
.hr-folder-sans-remove:before {
  content: "\eac4";
}
.hr-folder-sans-run:before {
  content: "\eac5";
}
.hr-folder-sans-security:before {
  content: "\eac6";
}
.hr-folder-sans-settings:before {
  content: "\eac7";
}
.hr-folder-sans-up:before {
  content: "\eac8";
}
.hr-folder-text:before {
  content: "\eac9";
}
.hr-folder-video:before {
  content: "\eaca";
}
.hr-font:before {
  content: "\eacb";
}
.hr-fonticons:before {
  content: "\eacc";
}
.hr-tax-and-statutory-compliance:before {
  content: "\eacd";
}
.hr-tax-and-statutory-compliance-compliance-forms:before {
  content: "\eace";
}
.hr-core-hr-register-face:before {
  content: "\eacf";
}
.hr-forms-manager-document-template-engine:before {
  content: "\ead0";
}
.hr-workflow-dynamic-form-builder:before {
  content: "\ead1";
}
.hr-forms-manager-form16:before {
  content: "\ead2";
}
.hr-tax-and-statutory-compliance-form-downloads:before {
  content: "\ead3";
}
.hr-forms-manager-form-f:before {
  content: "\ead4";
}
.hr-forms-manager-form-g:before {
  content: "\ead5";
}
.hr-fort-awesome:before {
  content: "\ead6";
}
.hr-forumbee:before {
  content: "\ead7";
}
.hr-forward:before {
  content: "\ead8";
}
.hr-foursquare:before {
  content: "\ead9";
}
.hr-free-code-camp:before {
  content: "\eada";
}
.hr-friends:before {
  content: "\eadb";
}
.hr-frown-o:before {
  content: "\eadc";
}
.hr-futbol-o:before {
  content: "\eadd";
}
.hr-gamepad:before {
  content: "\eade";
}
.hr-gavel:before {
  content: "\eadf";
}
.hr-gbp:before {
  content: "\eae0";
}
.hr-genderless:before {
  content: "\eae1";
}
.hr-get-pocket:before {
  content: "\eae2";
}
.hr-gg:before {
  content: "\eae3";
}
.hr-gg-circle:before {
  content: "\eae4";
}
.hr-gift:before {
  content: "\eae5";
}
.hr-git:before {
  content: "\eae6";
}
.hr-github:before {
  content: "\eae7";
}
.hr-github-alt:before {
  content: "\eae8";
}
.hr-github-square:before {
  content: "\eae9";
}
.hr-gitlab:before {
  content: "\eaea";
}
.hr-git-square:before {
  content: "\eaeb";
}
.hr-gittip:before {
  content: "\eaec";
}
.hr-glass:before {
  content: "\eaed";
}
.hr-glide:before {
  content: "\eaee";
}
.hr-glide-g:before {
  content: "\eaef";
}
.hr-globe:before {
  content: "\eaf0";
}
.hr-google:before {
  content: "\eaf1";
}
.hr-google-plus:before {
  content: "\eaf2";
}
.hr-google-plus-circle:before {
  content: "\eaf3";
}
.hr-google-plus-square:before {
  content: "\eaf4";
}
.hr-google-wallet:before {
  content: "\eaf5";
}
.hr-graduation-cap:before {
  content: "\eaf6";
}
.hr-grav:before {
  content: "\eaf7";
}
.hr-group:before {
  content: "\eaf8";
}
.hr-group-full:before {
  content: "\eaf9";
}
.hr-group-full-edit:before {
  content: "\eafa";
}
.hr-group-full-security:before {
  content: "\eafb";
}
.hr-group-half:before {
  content: "\eafc";
}
.hr-group-half-edit:before {
  content: "\eafd";
}
.hr-hacker-news:before {
  content: "\eafe";
}
.hr-hand-grab-o:before {
  content: "\eaff";
}
.hr-hand-lizard-o:before {
  content: "\eb00";
}
.hr-hand-o-down:before {
  content: "\eb01";
}
.hr-hand-o-left:before {
  content: "\eb02";
}
.hr-hand-o-right:before {
  content: "\eb03";
}
.hr-hand-o-up:before {
  content: "\eb04";
}
.hr-hand-paper-o:before {
  content: "\eb05";
}
.hr-hand-peace-o:before {
  content: "\eb06";
}
.hr-hand-pointer-o:before {
  content: "\eb07";
}
.hr-hand-scissors-o:before {
  content: "\eb08";
}
.hr-handshake-o:before {
  content: "\eb09";
}
.hr-hand-spock-o:before {
  content: "\eb0a";
}
.hr-hashtag:before {
  content: "\eb0b";
}
.hr-hdd-o:before {
  content: "\eb0c";
}
.hr-header:before {
  content: "\eb0d";
}
.hr-headphones:before {
  content: "\eb0e";
}
.hr-heart:before {
  content: "\eb0f";
}
.hr-heartbeat:before {
  content: "\eb10";
}
.hr-heart-o:before {
  content: "\eb11";
}
.hr-help:before {
  content: "\eb12";
}
.hr-help-contact-us:before {
  content: "\eb13";
}
.hr-help-help-topics:before {
  content: "\eb14";
}
.hr-history:before {
  content: "\eb15";
}
.hr-history1:before {
  content: "\eb16";
}
.hr-home:before {
  content: "\eb17";
}
.hr-home1:before {
  content: "\eb18";
}
.hr-horizontal-rule:before {
  content: "\eb19";
}
.hr-hospital-o:before {
  content: "\eb1a";
}
.hr-hourglass:before {
  content: "\eb1b";
}
.hr-hourglass-1:before {
  content: "\eb1c";
}
.hr-hourglass-2:before {
  content: "\eb1d";
}
.hr-hourglass-3:before {
  content: "\eb1e";
}
.hr-hourglass-o:before {
  content: "\eb1f";
}
.hr-hourly-master-report:before {
  content: "\eb20";
}
.hr-hourly-payment:before {
  content: "\eb21";
}
.hr-houzz:before {
  content: "\eb22";
}
.hr-hr-group:before {
  content: "\eb23";
}
.hr-hr-report-employee-education:before {
  content: "\eb24";
}
.hr-hr-report-new-joinees:before {
  content: "\eb25";
}
.hr-hr-reports:before {
  content: "\eb26";
}
.hr-hr-tax-section-allowance-mapping:before {
  content: "\eb27";
}
.hr-h-square:before {
  content: "\eb28";
}
.hr-html5:before {
  content: "\eb29";
}
.hr-i-cursor:before {
  content: "\eb2a";
}
.hr-id-badge:before {
  content: "\eb2b";
}
.hr-ils:before {
  content: "\eb2c";
}
.hr-image:before {
  content: "\eb2d";
}
.hr-imdb:before {
  content: "\eb2e";
}
.hr-inbox:before {
  content: "\eb2f";
}
.hr-include-break-hours:before {
  content: "\eb30";
}
.hr-indent:before {
  content: "\eb31";
}
.hr-industry:before {
  content: "\eb32";
}
.hr-info:before {
  content: "\eb33";
}
.hr-info1:before {
  content: "\eb34";
}
.hr-info-circle:before {
  content: "\eb35";
}
.hr-inr:before {
  content: "\eb36";
}
.hr-instagram:before {
  content: "\eb37";
}
.hr-insurance-payment-tracker:before {
  content: "\eb38";
}
.hr-insurance-variable:before {
  content: "\eb39";
}
.hr-internet-explorer:before {
  content: "\eb3a";
}
.hr-intersex:before {
  content: "\eb3b";
}
.hr-ios-refresh-outline:before {
  content: "\eb3c";
}
.hr-ios-upload:before {
  content: "\eb3d";
}
.hr-ios-upload-outline:before {
  content: "\eb3e";
}
.hr-ioxhost:before {
  content: "\eb3f";
}
.hr-issue-closed:before {
  content: "\eb40";
}
.hr-issue-reopened:before {
  content: "\eb41";
}
.hr-italic:before {
  content: "\eb42";
}
.hr-joomla:before {
  content: "\eb43";
}
.hr-jsfiddle:before {
  content: "\eb44";
}
.hr-key:before {
  content: "\eb45";
}
.hr-key1:before {
  content: "\eb46";
}
.hr-key-1:before {
  content: "\eb47";
}
.hr-key-2:before {
  content: "\eb48";
}
.hr-key-3:before {
  content: "\eb49";
}
.hr-key-4:before {
  content: "\eb4a";
}
.hr-key-5:before {
  content: "\eb4b";
}
.hr-key-6:before {
  content: "\eb4c";
}
.hr-keyboard-o:before {
  content: "\eb4d";
}
.hr-key-fill:before {
  content: "\eb4e";
}
.hr-key-stroke:before {
  content: "\eb4f";
}
.hr-krw:before {
  content: "\eb50";
}
.hr-language:before {
  content: "\eb51";
}
.hr-laptop:before {
  content: "\eb52";
}
.hr-lastfm:before {
  content: "\eb53";
}
.hr-lastfm-square:before {
  content: "\eb54";
}
.hr-leaf:before {
  content: "\eb55";
}
.hr-leanpub:before {
  content: "\eb56";
}
.hr-lemon-o:before {
  content: "\eb57";
}
.hr-level-down:before {
  content: "\eb58";
}
.hr-level-up:before {
  content: "\eb59";
}
.hr-life-bouy:before {
  content: "\eb5a";
}
.hr-light-bulb:before {
  content: "\eb5b";
}
.hr-lightbulb-o:before {
  content: "\eb5c";
}
.hr-line-chart:before {
  content: "\eb5d";
}
.hr-linkedin:before {
  content: "\eb5e";
}
.hr-linkedin-square:before {
  content: "\eb5f";
}
.hr-linode:before {
  content: "\eb60";
}
.hr-linux:before {
  content: "\eb61";
}
.hr-list:before {
  content: "\eb62";
}
.hr-list-add:before {
  content: "\eb63";
}
.hr-list-alt:before {
  content: "\eb64";
}
.hr-list-ol:before {
  content: "\eb65";
}
.hr-list-ul:before {
  content: "\eb66";
}
.hr-location-arrow:before {
  content: "\eb67";
}
.hr-lock:before {
  content: "\eb68";
}
.hr-lock-alt:before {
  content: "\eb69";
}
.hr-long-arrow-down:before {
  content: "\eb6a";
}
.hr-long-arrow-left:before {
  content: "\eb6b";
}
.hr-long-arrow-right:before {
  content: "\eb6c";
}
.hr-long-arrow-up:before {
  content: "\eb6d";
}
.hr-low-vision:before {
  content: "\eb6e";
}
.hr-magic:before {
  content: "\eb6f";
}
.hr-magic-wand:before {
  content: "\eb70";
}
.hr-magnet:before {
  content: "\eb71";
}
.hr-mail:before {
  content: "\eb72";
}
.hr-mail-forward:before {
  content: "\eb73";
}
.hr-mail-read:before {
  content: "\eb74";
}
.hr-mail-reply:before {
  content: "\eb75";
}
.hr-mail-reply1:before {
  content: "\eb76";
}
.hr-mail-reply-all:before {
  content: "\eb77";
}
.hr-male:before {
  content: "\eb78";
}
.hr-male1:before {
  content: "\eb79";
}
.hr-male-rounded-1:before {
  content: "\eb7a";
}
.hr-male-user-1:before {
  content: "\eb7b";
}
.hr-map:before {
  content: "\eb7c";
}
.hr-map-marker:before {
  content: "\eb7d";
}
.hr-map-o:before {
  content: "\eb7e";
}
.hr-map-pin:before {
  content: "\eb7f";
}
.hr-map-signs:before {
  content: "\eb80";
}
.hr-mars:before {
  content: "\eb81";
}
.hr-mars-double:before {
  content: "\eb82";
}
.hr-mars-stroke:before {
  content: "\eb83";
}
.hr-mars-stroke-h:before {
  content: "\eb84";
}
.hr-mars-stroke-v:before {
  content: "\eb85";
}
.hr-maxcdn:before {
  content: "\eb86";
}
.hr-meanpath:before {
  content: "\eb87";
}
.hr-medium:before {
  content: "\eb88";
}
.hr-medkit:before {
  content: "\eb89";
}
.hr-meetup:before {
  content: "\eb8a";
}
.hr-meh-o:before {
  content: "\eb8b";
}
.hr-menu:before {
  content: "\eb8c";
}
.hr-mercury:before {
  content: "\eb8d";
}
.hr-microchip:before {
  content: "\eb8e";
}
.hr-microphone:before {
  content: "\eb8f";
}
.hr-microphone-slash:before {
  content: "\eb90";
}
.hr-minus:before {
  content: "\eb91";
}
.hr-minus-circle:before {
  content: "\eb92";
}
.hr-minus-square:before {
  content: "\eb93";
}
.hr-minus-square-o:before {
  content: "\eb94";
}
.hr-mixcloud:before {
  content: "\eb95";
}
.hr-mobile:before {
  content: "\eb96";
}
.hr-modx:before {
  content: "\eb97";
}
.hr-money:before {
  content: "\eb98";
}
.hr-monthly-payment:before {
  content: "\eb99";
}
.hr-montly-master-report:before {
  content: "\eb9a";
}
.hr-moon-o:before {
  content: "\eb9b";
}
.hr-motorcycle:before {
  content: "\eb9c";
}
.hr-mouse-pointer:before {
  content: "\eb9d";
}
.hr-music:before {
  content: "\eb9e";
}
.hr-neuter:before {
  content: "\eb9f";
}
.hr-newspaper-o:before {
  content: "\eba0";
}
.hr-object-group:before {
  content: "\eba1";
}
.hr-object-ungroup:before {
  content: "\eba2";
}
.hr-odnoklassniki:before {
  content: "\eba3";
}
.hr-odnoklassniki-square:before {
  content: "\eba4";
}
.hr-opencart:before {
  content: "\eba5";
}
.hr-openid:before {
  content: "\eba6";
}
.hr-opera:before {
  content: "\eba7";
}
.hr-optin-monster:before {
  content: "\eba8";
}
.hr-optional-choice:before {
  content: "\eba9";
}
.hr-organization:before {
  content: "\ebaa";
}
.hr-organization-1:before {
  content: "\ebab";
}
.hr-organization-announcements:before {
  content: "\ebac";
}
.hr-organization-data-import:before {
  content: "\ebad";
}
.hr-organization-data-import-1-1:before {
  content: "\ebae";
}
.hr-organization-department-hierarchy:before {
  content: "\ebaf";
}
.hr-organization-details:before {
  content: "\ebb0";
}
.hr-organization-eft-configuration:before {
  content: "\ebb1";
}
.hr-organization-eft-configuration-deregister:before {
  content: "\ebb2";
}
.hr-organization-special-wages:before {
  content: "\ebb3";
}
.hr-organization-holiday-types:before {
  content: "\ebb4";
}
.hr-organization-locations:before {
  content: "\ebb5";
}
.hr-organization-organization-policies:before {
  content: "\ebb6";
}
.hr-organization-organization-profile:before {
  content: "\ebb7";
}
.hr-organization-organization-settings:before {
  content: "\ebb8";
}
.hr-core-hr-employee-data-management:before {
  content: "\ebb9";
}
.hr-organization-projects-1:before {
  content: "\ebba";
}
.hr-organization-system-log:before {
  content: "\ebbb";
}
.hr-organization-work-schedule:before {
  content: "\ebbc";
}
.hr-pagelines:before {
  content: "\ebbd";
}
.hr-page-pdf:before {
  content: "\ebbe";
}
.hr-paint-brush:before {
  content: "\ebbf";
}
.hr-paperclip:before {
  content: "\ebc0";
}
.hr-paper-plane:before {
  content: "\ebc1";
}
.hr-paper-plane-o:before {
  content: "\ebc2";
}
.hr-paragraph:before {
  content: "\ebc3";
}
.hr-pause:before {
  content: "\ebc4";
}
.hr-pause-circle:before {
  content: "\ebc5";
}
.hr-pause-circle-o:before {
  content: "\ebc6";
}
.hr-paw:before {
  content: "\ebc7";
}
.hr-payment-tracker:before {
  content: "\ebc8";
}
.hr-paypal:before {
  content: "\ebc9";
}
.hr-payroll:before {
  content: "\ebca";
}
.hr-payroll-2:before {
  content: "\ebcb";
}
.hr-payroll-advance-salary:before {
  content: "\ebcc";
}
.hr-payroll-allowances:before {
  content: "\ebcd";
}
.hr-payroll-bonus:before {
  content: "\ebce";
}
.hr-payroll-commission:before {
  content: "\ebcf";
}
.hr-payroll-deductions:before {
  content: "\ebd0";
}
.hr-tax-and-statutory-compliance-nps:before {
  content: "\ebd1";
}
.hr-payroll-final-settlement:before {
  content: "\ebd2";
}
.hr-tax-and-statutory-compliance-fixed-health-insurance:before {
  content: "\ebd3";
}
.hr-payroll-flexi-benefit-declaration:before {
  content: "\ebd4";
}
.hr-tax-and-statutory-compliance-gratuity:before {
  content: "\ebd5";
}
.hr-payroll-gratuity-nomination:before {
  content: "\ebd6";
}
.hr-payroll-group:before {
  content: "\ebd7";
}
.hr-tax-and-statutory-compliance-insurance:before {
  content: "\ebd8";
}
.hr-tax-and-statutory-compliance-labour-welfare-fund:before {
  content: "\ebd9";
}
.hr-payroll-loan:before {
  content: "\ebda";
}
.hr-payroll-additional-wage-claim:before {
  content: "\ebdb";
}
.hr-payroll-payout:before {
  content: "\ebdc";
}
.hr-payroll-payout-history:before {
  content: "\ebdd";
}
.hr-payroll-payslip-template:before {
  content: "\ebde";
}
.hr-tax-and-statutory-compliance-perquisite-tracker:before {
  content: "\ebdf";
}
.hr-payroll-proof-of-investment:before {
  content: "\ebe0";
}
.hr-tax-and-statutory-compliance-provident-fund:before {
  content: "\ebe1";
}
.hr-payroll-reimbursement:before {
  content: "\ebe2";
}
.hr-payroll-reimbursement-bank-statement:before {
  content: "\ebe3";
}
.hr-payroll-reports:before {
  content: "\ebe4";
}
.hr-payroll-salary:before {
  content: "\ebe5";
}
.hr-payroll-salary-payslip:before {
  content: "\ebe6";
}
.hr-payroll-salary-template:before {
  content: "\ebe7";
}
.hr-payroll-shift-allowance:before {
  content: "\ebe8";
}
.hr-tax-and-statutory-compliance-tax-declarations:before {
  content: "\ebe9";
}
.hr-payroll-tax-declarations-1:before {
  content: "\ebea";
}
.hr-tax-and-statutory-compliance-tax-rules:before {
  content: "\ebeb";
}
.hr-tax-and-statutory-compliance-tds-history:before {
  content: "\ebec";
}
.hr-pencil:before {
  content: "\ebed";
}
.hr-pencil-square:before {
  content: "\ebee";
}
.hr-percent:before {
  content: "\ebef";
}
.hr-performance-evaluation:before {
  content: "\ebf0";
}
.hr-phone:before {
  content: "\ebf1";
}
.hr-phone-square:before {
  content: "\ebf2";
}
.hr-pie-chart:before {
  content: "\ebf3";
}
.hr-pied-piper:before {
  content: "\ebf4";
}
.hr-pied-piper-alt:before {
  content: "\ebf5";
}
.hr-pied-piper-pp:before {
  content: "\ebf6";
}
.hr-pinterest:before {
  content: "\ebf7";
}
.hr-pinterest-p:before {
  content: "\ebf8";
}
.hr-pinterest-square:before {
  content: "\ebf9";
}
.hr-plane:before {
  content: "\ebfa";
}
.hr-play:before {
  content: "\ebfb";
}
.hr-play-circle:before {
  content: "\ebfc";
}
.hr-play-circle-o:before {
  content: "\ebfd";
}
.hr-plug:before {
  content: "\ebfe";
}
.hr-plus:before {
  content: "\ebff";
}
.hr-plus-circle:before {
  content: "\ec00";
}
.hr-plus-square:before {
  content: "\ec01";
}
.hr-plus-square-o:before {
  content: "\ec02";
}
.hr-podcast:before {
  content: "\ec03";
}
.hr-power-off:before {
  content: "\ec04";
}
.hr-print:before {
  content: "\ec05";
}
.hr-print1:before {
  content: "\ec06";
}
.hr-product-hunt:before {
  content: "\ec07";
}
.hr-productivity-monitoring:before {
  content: "\ec08";
}
.hr-productivity-monitoring-activity-dashboard:before {
  content: "\ec09";
}
.hr-productivity-monitoring-activity-tracker:before {
  content: "\ec0a";
}
.hr-productivity-monitoring-members:before {
  content: "\ec0b";
}
.hr-productivity-monitoring-reports:before {
  content: "\ec0c";
}
.hr-professional-tax:before {
  content: "\ec0d";
}
.hr-puzzle-piece:before {
  content: "\ec0e";
}
.hr-qq:before {
  content: "\ec0f";
}
.hr-qrcode:before {
  content: "\ec10";
}
.hr-question:before {
  content: "\ec11";
}
.hr-question-circle:before {
  content: "\ec12";
}
.hr-question-circle-o:before {
  content: "\ec13";
}
.hr-quick-menu:before {
  content: "\ec14";
}
.hr-quora:before {
  content: "\ec15";
}
.hr-quote-left:before {
  content: "\ec16";
}
.hr-quote-right:before {
  content: "\ec17";
}
.hr-ra:before {
  content: "\ec18";
}
.hr-random:before {
  content: "\ec19";
}
.hr-ravelry:before {
  content: "\ec1a";
}
.hr-recruitment_clients_chat:before {
  content: "\ec1b";
}
.hr-recruitment_clients_import:before {
  content: "\ec1c";
}
.hr-recruitment-clients-campaign:before {
  content: "\ec1d";
}
.hr-recruitment-clients-campaign1:before {
  content: "\ec1e";
}
.hr-recruitment-clients-export:before {
  content: "\ec1f";
}
.hr-recruitment-clients-import:before {
  content: "\ec20";
}
.hr-recruitment-clients-master:before {
  content: "\ec21";
}
.hr-recruitment-clients-print:before {
  content: "\ec22";
}
.hr-recruitment-dashboard:before {
  content: "\ec23";
}
.hr-recruitment-dashboard-applied:before {
  content: "\ec24";
}
.hr-recruitment-dashboard-rejected:before {
  content: "\ec25";
}
.hr-recruitment-dashboard-scheduled:before {
  content: "\ec26";
}
.hr-recruitment-dashboard-shortlisted:before {
  content: "\ec27";
}
.hr-onboarding-individuals:before {
  content: "\ec28";
}
.hr-recruitment-interview-calendar:before {
  content: "\ec29";
}
.hr-recruitment-interview-rounds-master:before {
  content: "\ec2a";
}
.hr-recruitment-job-candidates:before {
  content: "\ec2b";
}
.hr-recruitment-job-post-requisition:before {
  content: "\ec2c";
}
.hr-recruitment-job-posts:before {
  content: "\ec2d";
}
.hr-recruitment-reports:before {
  content: "\ec2e";
}
.hr-recruitment-schedule-interviews:before {
  content: "\ec2f";
}
.hr-recruitment-shortlisted-candidates:before {
  content: "\ec30";
}
.hr-recuritment:before {
  content: "\ec31";
}
.hr-recycle:before {
  content: "\ec32";
}
.hr-reddit:before {
  content: "\ec33";
}
.hr-reddit-alien:before {
  content: "\ec34";
}
.hr-reddit-square:before {
  content: "\ec35";
}
.hr-refresh:before {
  content: "\ec36";
}
.hr-refresh1:before {
  content: "\ec37";
}
.hr-registered:before {
  content: "\ec38";
}
.hr-renren:before {
  content: "\ec39";
}
.hr-repeat:before {
  content: "\ec3a";
}
.hr-repo:before {
  content: "\ec3b";
}
.hr-repo-clone:before {
  content: "\ec3c";
}
.hr-repo-force-push:before {
  content: "\ec3d";
}
.hr-report:before {
  content: "\ec3e";
}
.hr-report-absentees:before {
  content: "\ec3f";
}
.hr-report-assignment:before {
  content: "\ec40";
}
.hr-report-attendance-import:before {
  content: "\ec41";
}
.hr-report-attendance-muster-info:before {
  content: "\ec42";
}
.hr-report-attendance-shortage:before {
  content: "\ec43";
}
.hr-report-attendance-summary-hourly:before {
  content: "\ec44";
}
.hr-report-attendance-summary-monthly:before {
  content: "\ec45";
}
.hr-report-attendance-without-grace-period:before {
  content: "\ec46";
}
.hr-report-attendance-with-out-grace-period:before {
  content: "\ec47";
}
.hr-report-biometric-error-log:before {
  content: "\ec48";
}
.hr-report-biometric-sync-history:before {
  content: "\ec49";
}
.hr-report-certifications:before {
  content: "\ec4a";
}
.hr-report-compensatory-off:before {
  content: "\ec4b";
}
.hr-report-compensatory-off-balance:before {
  content: "\ec4c";
}
.hr-report-dependents:before {
  content: "\ec4d";
}
.hr-report-designations:before {
  content: "\ec4e";
}
.hr-report-device-management:before {
  content: "\ec4f";
}
.hr-report-educational-qualifications:before {
  content: "\ec50";
}
.hr-report-gender:before {
  content: "\ec51";
}
.hr-report-holiday-attendance:before {
  content: "\ec52";
}
.hr-report-insurance:before {
  content: "\ec53";
}
.hr-report-insurance-fixed:before {
  content: "\ec54";
}
.hr-report-leave-history:before {
  content: "\ec55";
}
.hr-report-license-expiry:before {
  content: "\ec56";
}
.hr-report-loan:before {
  content: "\ec57";
}
.hr-report-logo:before {
  content: "\ec58";
}
.hr-report-monthly-leave-balance:before {
  content: "\ec59";
}
.hr-report-monthly-shortage:before {
  content: "\ec5a";
}
.hr-report-overtime:before {
  content: "\ec5b";
}
.hr-report-project:before {
  content: "\ec5c";
}
.hr-report-report:before {
  content: "\ec5d";
}
.hr-reports-adhoc-allowance:before {
  content: "\ec5e";
}
.hr-reports-custom-report:before {
  content: "\ec5f";
}
.hr-reports-deferred-loan:before {
  content: "\ec60";
}
.hr-reports-ecr-hourly:before {
  content: "\ec61";
}
.hr-reports-ecr-monthly:before {
  content: "\ec62";
}
.hr-reports-eft:before {
  content: "\ec63";
}
.hr-reports-eft-hourly:before {
  content: "\ec64";
}
.hr-reports-eft-monthly:before {
  content: "\ec65";
}
.hr-reports-employee-wise-expenses:before {
  content: "\ec66";
}
.hr-reports-esic-hourly:before {
  content: "\ec67";
}
.hr-reports-esic-monthly:before {
  content: "\ec68";
}
.hr-reports-esi-hourly:before {
  content: "\ec69";
}
.hr-reports-esi-monthly:before {
  content: "\ec6a";
}
.hr-reports-group-deductions:before {
  content: "\ec6b";
}
.hr-report-short:before {
  content: "\ec6c";
}
.hr-reports-hourly-master-report:before {
  content: "\ec6d";
}
.hr-reports-hourly-wage-payslip:before {
  content: "\ec6e";
}
.hr-reports-insurance-fixed:before {
  content: "\ec6f";
}
.hr-reports-insurance-payment-tracker:before {
  content: "\ec70";
}
.hr-report-skills:before {
  content: "\ec71";
}
.hr-reports-loan-amortization:before {
  content: "\ec72";
}
.hr-reports-loan-balance:before {
  content: "\ec73";
}
.hr-reports-monthly-master-report:before {
  content: "\ec74";
}
.hr-reports-monthly-salary:before {
  content: "\ec75";
}
.hr-reports-monthly-salary-payslip:before {
  content: "\ec76";
}
.hr-reports-pf-hourly:before {
  content: "\ec77";
}
.hr-reports-pf-monthly:before {
  content: "\ec78";
}
.hr-reports-pf-payment-tracker:before {
  content: "\ec79";
}
.hr-reports-professional-tax-hourly:before {
  content: "\ec7a";
}
.hr-reports-professional-tax-monthly:before {
  content: "\ec7b";
}
.hr-reports-salary-increment:before {
  content: "\ec7c";
}
.hr-report-status:before {
  content: "\ec7d";
}
.hr-reports-tds:before {
  content: "\ec7e";
}
.hr-reports-uan-based-ecr:before {
  content: "\ec7f";
}
.hr-reports-uan-based-ecr-hourly:before {
  content: "\ec80";
}
.hr-reports-variable-insurance:before {
  content: "\ec81";
}
.hr-reports-work-sheet:before {
  content: "\ec82";
}
.hr-report-tax:before {
  content: "\ec83";
}
.hr-report-taxdeduct:before {
  content: "\ec84";
}
.hr-report-trainings:before {
  content: "\ec85";
}
.hr-report-weekoff-attendance:before {
  content: "\ec86";
}
.hr-report-year-of-join:before {
  content: "\ec87";
}
.hr-reset:before {
  content: "\ec88";
}
.hr-retweet:before {
  content: "\ec89";
}
.hr-road:before {
  content: "\ec8a";
}
.hr-rocket:before {
  content: "\ec8b";
}
.hr-roster-management:before {
  content: "\ec8c";
}
.hr-roster-management-calendar-view:before {
  content: "\ec8d";
}
.hr-roster-management-custom-group:before {
  content: "\ec8e";
}
.hr-roster-management-custom-group-settings:before {
  content: "\ec8f";
}
.hr-roster-management-shift-scheduling:before {
  content: "\ec90";
}
.hr-roster-management-shift-type:before {
  content: "\ec91";
}
.hr-rotate-left:before {
  content: "\ec92";
}
.hr-rouble:before {
  content: "\ec93";
}
.hr-rss-square:before {
  content: "\ec94";
}
.hr-ruler:before {
  content: "\ec95";
}
.hr-safari:before {
  content: "\ec96";
}
.hr-scribd:before {
  content: "\ec97";
}
.hr-search:before {
  content: "\ec98";
}
.hr-search1:before {
  content: "\ec99";
}
.hr-search-minus:before {
  content: "\ec9a";
}
.hr-search-plus:before {
  content: "\ec9b";
}
.hr-section-investment:before {
  content: "\ec9c";
}
.hr-sellsy:before {
  content: "\ec9d";
}
.hr-server:before {
  content: "\ec9e";
}
.hr-settings:before {
  content: "\ec9f";
}
.hr-settings-ip-whitelisting:before {
  content: "\eca0";
}
.hr-settings-general:before {
  content: "\eca1";
}
.hr-settings-payroll:before {
  content: "\eca2";
}
.hr-settings-performance-management:before {
  content: "\eca3";
}
.hr-settings-productivity-monitoring:before {
  content: "\eca4";
}
.hr-settings-tax-and-statutory-compliance:before {
  content: "\eca5";
}
.hr-share:before {
  content: "\eca6";
}
.hr-share-alt:before {
  content: "\eca7";
}
.hr-share-alt-square:before {
  content: "\eca8";
}
.hr-share-square:before {
  content: "\eca9";
}
.hr-share-square-o:before {
  content: "\ecaa";
}
.hr-shield:before {
  content: "\ecab";
}
.hr-ship:before {
  content: "\ecac";
}
.hr-shirtsinbulk:before {
  content: "\ecad";
}
.hr-shopping-bag:before {
  content: "\ecae";
}
.hr-shopping-basket:before {
  content: "\ecaf";
}
.hr-shopping-cart:before {
  content: "\ecb0";
}
.hr-shower:before {
  content: "\ecb1";
}
.hr-shrink:before {
  content: "\ecb2";
}
.hr-signal:before {
  content: "\ecb3";
}
.hr-sign-in:before {
  content: "\ecb4";
}
.hr-sign-in1:before {
  content: "\ecb5";
}
.hr-sign-in-1:before {
  content: "\ecb6";
}
.hr-sign-language:before {
  content: "\ecb7";
}
.hr-signout:before {
  content: "\ecb8";
}
.hr-sign-out:before {
  content: "\ecb9";
}
.hr-sign-out1:before {
  content: "\ecba";
}
.hr-simplybuilt:before {
  content: "\ecbb";
}
.hr-sitemap:before {
  content: "\ecbc";
}
.hr-skill-definition:before {
  content: "\ecbd";
}
.hr-skill-level-association:before {
  content: "\ecbe";
}
.hr-skyatlas:before {
  content: "\ecbf";
}
.hr-skype:before {
  content: "\ecc0";
}
.hr-slack:before {
  content: "\ecc1";
}
.hr-sliders:before {
  content: "\ecc2";
}
.hr-slideshare:before {
  content: "\ecc3";
}
.hr-smile-o:before {
  content: "\ecc4";
}
.hr-snapchat:before {
  content: "\ecc5";
}
.hr-snapchat-ghost:before {
  content: "\ecc6";
}
.hr-snapchat-square:before {
  content: "\ecc7";
}
.hr-snowflake-o:before {
  content: "\ecc8";
}
.hr-sort:before {
  content: "\ecc9";
}
.hr-sort-alpha-asc:before {
  content: "\ecca";
}
.hr-sort-alpha-desc:before {
  content: "\eccb";
}
.hr-sort-amount-asc:before {
  content: "\eccc";
}
.hr-sort-amount-desc:before {
  content: "\eccd";
}
.hr-sort-asc:before {
  content: "\ecce";
}
.hr-sort-desc:before {
  content: "\eccf";
}
.hr-sort-numeric-asc:before {
  content: "\ecd0";
}
.hr-sort-numeric-desc:before {
  content: "\ecd1";
}
.hr-soundcloud:before {
  content: "\ecd2";
}
.hr-space-shuttle:before {
  content: "\ecd3";
}
.hr-spinner:before {
  content: "\ecd4";
}
.hr-spoon:before {
  content: "\ecd5";
}
.hr-spotify:before {
  content: "\ecd6";
}
.hr-square:before {
  content: "\ecd7";
}
.hr-square-o:before {
  content: "\ecd8";
}
.hr-stack-exchange:before {
  content: "\ecd9";
}
.hr-stack-overflow:before {
  content: "\ecda";
}
.hr-star:before {
  content: "\ecdb";
}
.hr-star1:before {
  content: "\ecdc";
}
.hr-star-half:before {
  content: "\ecdd";
}
.hr-star-half-empty:before {
  content: "\ecde";
}
.hr-star-o:before {
  content: "\ecdf";
}
.hr-statistics:before {
  content: "\ece0";
}
.hr-status-approval:before {
  content: "\ece1";
}
.hr-status-update:before {
  content: "\ece2";
}
.hr-steam:before {
  content: "\ece3";
}
.hr-steam-square:before {
  content: "\ece4";
}
.hr-step-backward:before {
  content: "\ece5";
}
.hr-step-forward:before {
  content: "\ece6";
}
.hr-stethoscope:before {
  content: "\ece7";
}
.hr-sticky-note:before {
  content: "\ece8";
}
.hr-sticky-note-o:before {
  content: "\ece9";
}
.hr-stop:before {
  content: "\ecea";
}
.hr-stop1:before {
  content: "\eceb";
}
.hr-stop-circle:before {
  content: "\ecec";
}
.hr-stop-circle-o:before {
  content: "\eced";
}
.hr-street-view:before {
  content: "\ecee";
}
.hr-strikethrough:before {
  content: "\ecef";
}
.hr-stumbleupon:before {
  content: "\ecf0";
}
.hr-stumbleupon-circle:before {
  content: "\ecf1";
}
.hr-subscript:before {
  content: "\ecf2";
}
.hr-subway:before {
  content: "\ecf3";
}
.hr-suitcase:before {
  content: "\ecf4";
}
.hr-sun-o:before {
  content: "\ecf5";
}
.hr-superpowers:before {
  content: "\ecf6";
}
.hr-superscript:before {
  content: "\ecf7";
}
.hr-support-ticket:before {
  content: "\ecf8";
}
.hr-sync:before {
  content: "\ecf9";
}
.hr-sync-1:before {
  content: "\ecfa";
}
.hr-table:before {
  content: "\ecfb";
}
.hr-tablet:before {
  content: "\ecfc";
}
.hr-tag:before {
  content: "\ecfd";
}
.hr-tags:before {
  content: "\ecfe";
}
.hr-tasks:before {
  content: "\ecff";
}
.hr-tax-calculation:before {
  content: "\ed00";
}
.hr-tax-entities:before {
  content: "\ed01";
}
.hr-tax-exemptions:before {
  content: "\ed02";
}
.hr-tax-rebates:before {
  content: "\ed03";
}
.hr-tax-section:before {
  content: "\ed04";
}
.hr-tax-section-1:before {
  content: "\ed05";
}
.hr-tax-slab:before {
  content: "\ed06";
}
.hr-tax-slab-1:before {
  content: "\ed07";
}
.hr-tds-payment-tracker:before {
  content: "\ed08";
}
.hr-tds-submission:before {
  content: "\ed09";
}
.hr-telegram:before {
  content: "\ed0a";
}
.hr-telescope:before {
  content: "\ed0b";
}
.hr-television:before {
  content: "\ed0c";
}
.hr-tencent-weibo:before {
  content: "\ed0d";
}
.hr-terminal:before {
  content: "\ed0e";
}
.hr-text-height:before {
  content: "\ed0f";
}
.hr-text-width:before {
  content: "\ed10";
}
.hr-th:before {
  content: "\ed11";
}
.hr-themeisle:before {
  content: "\ed12";
}
.hr-thermometer:before {
  content: "\ed13";
}
.hr-thermometer-0:before {
  content: "\ed14";
}
.hr-thermometer-1:before {
  content: "\ed15";
}
.hr-thermometer-2:before {
  content: "\ed16";
}
.hr-thermometer-3:before {
  content: "\ed17";
}
.hr-th-large:before {
  content: "\ed18";
}
.hr-th-list:before {
  content: "\ed19";
}
.hr-thumbs-down:before {
  content: "\ed1a";
}
.hr-thumbs-o-down:before {
  content: "\ed1b";
}
.hr-thumbs-o-up:before {
  content: "\ed1c";
}
.hr-thumbs-up:before {
  content: "\ed1d";
}
.hr-thumb-tack:before {
  content: "\ed1e";
}
.hr-ticket:before {
  content: "\ed1f";
}
.hr-timeline:before {
  content: "\ed20";
}
.hr-times:before {
  content: "\ed21";
}
.hr-times-circle:before {
  content: "\ed22";
}
.hr-times-circle-o:before {
  content: "\ed23";
}
.hr-timesheet:before {
  content: "\ed24";
}
.hr-timesheet-activity:before {
  content: "\ed25";
}
.hr-timesheet-hours:before {
  content: "\ed26";
}
.hr-times-rectangle:before {
  content: "\ed27";
}
.hr-times-rectangle-o:before {
  content: "\ed28";
}
.hr-tint:before {
  content: "\ed29";
}
.hr-toggle-off:before {
  content: "\ed2a";
}
.hr-toggle-on:before {
  content: "\ed2b";
}
.hr-trademark:before {
  content: "\ed2c";
}
.hr-train:before {
  content: "\ed2d";
}
.hr-transgender-alt:before {
  content: "\ed2e";
}
.hr-trash:before {
  content: "\ed2f";
}
.hr-trash-o:before {
  content: "\ed30";
}
.hr-tree:before {
  content: "\ed31";
}
.hr-trello:before {
  content: "\ed32";
}
.hr-tripadvisor:before {
  content: "\ed33";
}
.hr-trophy:before {
  content: "\ed34";
}
.hr-truck:before {
  content: "\ed35";
}
.hr-try:before {
  content: "\ed36";
}
.hr-tty:before {
  content: "\ed37";
}
.hr-tumblr:before {
  content: "\ed38";
}
.hr-tumblr-square:before {
  content: "\ed39";
}
.hr-twitch:before {
  content: "\ed3a";
}
.hr-twitter:before {
  content: "\ed3b";
}
.hr-twitter-square:before {
  content: "\ed3c";
}
.hr-umbrella:before {
  content: "\ed3d";
}
.hr-underline:before {
  content: "\ed3e";
}
.hr-universal-access:before {
  content: "\ed3f";
}
.hr-unlock:before {
  content: "\ed40";
}
.hr-unlock-alt:before {
  content: "\ed41";
}
.hr-Untitled:before {
  content: "\ed42";
}
.hr-Untitled-2:before {
  content: "\ed43";
}
.hr-Untitled-3:before {
  content: "\ed44";
}
.hr-Untitled-4:before {
  content: "\ed45";
}
.hr-Untitled-5:before {
  content: "\ed46";
}
.hr-Untitled-6:before {
  content: "\ed47";
}
.hr-upload:before {
  content: "\ed48";
}
.hr-upload1:before {
  content: "\ed49";
}
.hr-usb:before {
  content: "\ed4a";
}
.hr-user:before {
  content: "\ed4b";
}
.hr-user1:before {
  content: "\ed4c";
}
.hr-user-1:before {
  content: "\ed4d";
}
.hr-user-2:before {
  content: "\ed4e";
}
.hr-user-circle:before {
  content: "\ed4f";
}
.hr-user-circle-o:before {
  content: "\ed50";
}
.hr-user-close:before {
  content: "\ed51";
}
.hr-user-close-add:before {
  content: "\ed52";
}
.hr-user-close-edit:before {
  content: "\ed53";
}
.hr-user-close-information:before {
  content: "\ed54";
}
.hr-user-close-remove:before {
  content: "\ed55";
}
.hr-user-close-settings:before {
  content: "\ed56";
}
.hr-user-full:before {
  content: "\ed57";
}
.hr-user-full-add:before {
  content: "\ed58";
}
.hr-user-full-edit:before {
  content: "\ed59";
}
.hr-user-full-remove:before {
  content: "\ed5a";
}
.hr-user-full-security:before {
  content: "\ed5b";
}
.hr-user-full-settings:before {
  content: "\ed5c";
}
.hr-user-half:before {
  content: "\ed5d";
}
.hr-user-half-add:before {
  content: "\ed5e";
}
.hr-user-half-edit:before {
  content: "\ed5f";
}
.hr-user-half-information:before {
  content: "\ed60";
}
.hr-user-half-remove:before {
  content: "\ed61";
}
.hr-user-half-security:before {
  content: "\ed62";
}
.hr-user-half-settings:before {
  content: "\ed63";
}
.hr-user-md:before {
  content: "\ed64";
}
.hr-user-o:before {
  content: "\ed65";
}
.hr-user-outline:before {
  content: "\ed66";
}
.hr-onboarding:before {
  content: "\ed67";
}
.hr-user-profile:before {
  content: "\ed68";
}
.hr-user-profile-edit:before {
  content: "\ed69";
}
.hr-user-secret:before {
  content: "\ed6a";
}
.hr-user-times:before {
  content: "\ed6b";
}
.hr-venus:before {
  content: "\ed6c";
}
.hr-venus1:before {
  content: "\ed6d";
}
.hr-venus-double:before {
  content: "\ed6e";
}
.hr-venus-double1:before {
  content: "\ed6f";
}
.hr-venus-mars:before {
  content: "\ed70";
}
.hr-viacoin:before {
  content: "\ed71";
}
.hr-viadeo:before {
  content: "\ed72";
}
.hr-viadeo-square:before {
  content: "\ed73";
}
.hr-video-camera:before {
  content: "\ed74";
}
.hr-view:before {
  content: "\ed75";
}
.hr-view-disable:before {
  content: "\ed76";
}
.hr-vimeo:before {
  content: "\ed77";
}
.hr-vimeo-square:before {
  content: "\ed78";
}
.hr-vine:before {
  content: "\ed79";
}
.hr-vk:before {
  content: "\ed7a";
}
.hr-volume-control-phone:before {
  content: "\ed7b";
}
.hr-volume-down:before {
  content: "\ed7c";
}
.hr-volume-off:before {
  content: "\ed7d";
}
.hr-volume-up:before {
  content: "\ed7e";
}
.hr-wechat:before {
  content: "\ed7f";
}
.hr-weibo:before {
  content: "\ed80";
}
.hr-whatsapp:before {
  content: "\ed81";
}
.hr-wheelchair:before {
  content: "\ed82";
}
.hr-wheelchair-alt:before {
  content: "\ed83";
}
.hr-wifi:before {
  content: "\ed84";
}
.hr-wikipedia-w:before {
  content: "\ed85";
}
.hr-window-maximize:before {
  content: "\ed86";
}
.hr-window-minimize:before {
  content: "\ed87";
}
.hr-window-restore:before {
  content: "\ed88";
}
.hr-windows:before {
  content: "\ed89";
}
.hr-wordpress:before {
  content: "\ed8a";
}
.hr-workflow:before {
  content: "\ed8b";
}
.hr-workflow-approval-management:before {
  content: "\ed8c";
}
.hr-workflow-task-management-approve:before {
  content: "\ed8d";
}
.hr-workflow-task-management-approvedtask:before {
  content: "\ed8e";
}
.hr-workflow-task-management-claim:before {
  content: "\ed8f";
}
.hr-workflow-task-management-claim-override:before {
  content: "\ed90";
}
.hr-workflow-task-management-form:before {
  content: "\ed91";
}
.hr-workflow-task-management-reject:before {
  content: "\ed92";
}
.hr-workflow-task-management-surrender:before {
  content: "\ed93";
}
.hr-workflow-task-management-view:before {
  content: "\ed94";
}
.hr-workflow-task-management-waitingforapprovaltask:before {
  content: "\ed95";
}
.hr-workflow-workflow-builder:before {
  content: "\ed96";
}
.hr-wpbeginner:before {
  content: "\ed97";
}
.hr-wpexplorer:before {
  content: "\ed98";
}
.hr-wpforms:before {
  content: "\ed99";
}
.hr-wrench:before {
  content: "\ed9a";
}
.hr-x:before {
  content: "\ed9b";
}
.hr-xing:before {
  content: "\ed9c";
}
.hr-xing-square:before {
  content: "\ed9d";
}
.hr-yahoo:before {
  content: "\ed9e";
}
.hr-y-combinator:before {
  content: "\ed9f";
}
.hr-yelp:before {
  content: "\eda0";
}
.hr-yoast:before {
  content: "\eda1";
}
.hr-youtube:before {
  content: "\eda2";
}
.hr-youtube-play:before {
  content: "\eda3";
}
.hr-youtube-play1:before {
  content: "\eda4";
}
.hr-youtube-square:before {
  content: "\eda5";
}
.hr-zap:before {
  content: "\eda6";
}
.hr-zoom-in:before {
  content: "\eda7";
}
.hr-zoom-out:before {
  content: "\eda8";
}
.hr-benefits-esop:before {
  content: "\eda9";
}
.hr-benefits:before {
  content: "\edaa";
}
.hr-performance-management:before {
  content: "\edab";
}
.hr-performance-management-trulead:before {
  content: "\edac";
}
.hr-core-hr-work-schedule:before {
  content: "\edad";
}
.hr-core-hr:before {
  content: "\edae";
}
.hr-productivity-monitoring-workforce-analytics:before {
  content: "\edaf";
}
.hr-compliance-management:before {
  content: "\edb0";
}
.hr-compliance-management-docusign:before {
  content: "\edb1";
}
.hr-settings-attendance-configuration:before {
  content: "\edb2";
}
.hr-data-loss-prevention-file-transfers:before {
  content: "\edb3";
}
.hr-data-loss-prevention:before {
  content: "\edb4";
}
.hr-settings-data-loss-prevention:before {
  content: "\edb5";
}
.hr-compliance-management-accreditation:before {
  content: "\edb6";
}
.hr-onboarding-vendors:before {
  content: "\edb7";
}
.hr-settings-core-hr:before {
  content: "\edb8";
}
.hr-settings-integration:before {
  content: "\edb9";
}
.hr-employee-self-service:before {
  content: "\edba";
}
.hr-my-team:before {
  content: "\edbb";
}
.hr-my-team-lop-recovery:before {
  content: "\edbc";
}
.hr-my-team-team-summary:before {
  content: "\edbd";
}
.hr-employee-self-service-lop-recovery:before {
  content: "\edbe";
}
.hr-employee-self-service-pre-approval:before {
  content: "\edbf";
}
.hr-employee-self-service-compensatory-off-balance:before {
  content: "\edc0";
}
.hr-my-team-compensatory-off-balance:before {
  content: "\edc1";
}
.hr-my-team-pre-approval:before {
  content: "\edc2";
}
.hr-my-team-timesheets:before {
  content: "\edc3";
}
.hr-core-hr-time-off-management:before {
  content: "\edc4";
}
.hr-recruitment-recruitment-dashboard:before {
  content: "\edc5";
}
.hr-core-hr-org-structure:before {
  content: "\edc6";
}
.hr-settings-recruitment:before {
  content: "\edc7";
}
.hr-man-power-planning-job-requisition:before {
  content: "\edc8";
}
.hr-man-power-planning-hiring-forecast:before {
  content: "\edc9";
}
.hr-man-power-planning:before {
  content: "\edca";
}
.hr-man-power-planning-settings:before {
  content: "\edcb";
}
.hr-man-power-planning-table-of-organization:before {
  content: "\edcc";
}
.hr-roster-management-shift-swap:before {
  content: "\edcd";
}
.hr-data-loss-prevention-key-logger:before {
  content: "\edce";
}
.hr-data-loss-prevention-location-intelligence:before {
  content: "\edcf";
}
.hr-recruitment-my-integration:before {
  content: "\edd0";
}
.hr-recruitment-careers:before {
  content: "\edd1";
}
.hr-payroll-payroll-reconciliation:before {
  content: "\edd2";
}
.hr-payroll-electronic-fund-transfer:before {
  content: "\edd3";
}
