<?php
//=========================================================================================
//=========================================================================================
/* Program : OrgSettings.php                                                             *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.                                                         *
 * All Rights Reserved.                                                                     *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                         *
 * Description : MYSQL query for Organization and Registered User contact details         *
 *                                                                                        *
 *                                                                                         *
 * Revisions :                                                                             *
 *     Version    Date           Author                Description                            *
 *  0.1        30-May-2013    Sandhosh              Initial Version                      *
 *  0.2        29-Jan-2014    Mahesh                Added Allow Basic Overwrite field    *
 *  0.3        08-Apr-2014    Mahesh                Added Salary calculation days field  *
 *  0.4        21-Jul-2014    Mahesh                Added Functions                         *
 *                                                    1.mailGroup                         *
 *                                                    2.communicateMail                     *
 *                                                   3.checkFinancialClosure               *
 *  0.5          18-Sep-2014     Mahesh                Modified Function                    *
 *                                                     1.viewOrgDetail                        *
 *                                                                                       *
 *  1.0       02-Feb-2015     Saranya               Changes in file for mobile app       *
 *                                                   1.Extra fields are added in         *
 *                                                   field list of list query.           *
 *  1.5       12-Aug-2016     Shobana               Added PAN and TAN                    */
//=========================================================================================
//=========================================================================================
class Organization_Model_DbTable_OrgSettings extends Zend_Db_Table_Abstract
{
    protected $_db = null;

    protected $_salesDb = null;

    protected $_ehrTables = null;

    protected $_dbPersonal = null;

    protected $_commonFunction = null;

    protected $_orgDF = null;

    public function init()
    {
        $this->_ehrTables = new Application_Model_DbTable_Ehr();
        $this->_db = Zend_Registry::get('subHrapp');
        $this->_salesDb = Zend_Registry::get('Hrapp');
        $this->_dbPersonal = new Employees_Model_DbTable_Personal();
        $this->_commonFunction = new Application_Model_DbTable_CommonFunction();
        $this->_orgDF = $this->_ehrTables->orgDateformat();
    }
    /*
     * retreive specific record from orgInfo table
     *
     * @param str $orgCode - Org_Code
     * @return null/row
     */
    public function viewOrgDetail($orgCode = null)
    {
        $orgDetatils = $this->_db->fetchRow($this->_db->select()
                ->from(
                    array('ORGINFO' => $this->_ehrTables->orgDetails),
                    array('Org_Code', 'Org_Name','Org_Description', 'User_Name', 'Start_Year', 'Fiscal_StartMonth','Attendance_Process_Type_Id','Payslip_Password_Protected','IP_Address_Restriction',
                        'Performance_StartMonth', 'Date_Format', 'Employee_Edit','Display_Payslip_Address', 'Display_Reimbursement_Payslip','Represent_Basic_As_Multi_Components',
                        'Attendance_Enforced_Payment', 'Performance_Enforced_Payment', 'Show_Report_Creator', 'Report_LogoPath', 'System_LogoPath','Payslip_Address_Choice','Location_Id',
                        'Basic_Overwrite', 'Payment_Day', 'Cutoff_Day', 'Tax_Proof_Submission_Month', 'Display_UnpaidLeave_Deduction','Multiple_Source_Attendance_Same_Time',
                        'Financial_Closure_Tracking', 'Retirals_Based_On_Basic', 'Gross_Annual_Definition', 'Department_Classifications','Roundoff_Gross_Monthly_Salary', 'Resignation_Date_Override',
                        'FBP_Applicable', 'TDS_Exemption', 'Data_Setup_Prerequisites', 'Attendance_Log_Retention_Period','Location_Translation','Consider_Cutoff_Days_For_Attendance_And_Timeoff',
                        'Restrict_Financial_Access_For_Manager','Paycycle','Paycycle_End_Day','Roundoff_Netpay','HR_Admin_Email_Address','Restrict_Emp_Access_For_Manager','Field_Force', 'Use_Report_Logo_As_Product_Logo')
                )
                 ->joinLeft(
                array('L' => $this->_ehrTables->location),
                'ORGINFO.Location_Id=L.Location_Id',
                array('Location_Name')
            )
                ->where('ORGINFO.Org_Code = ?', $orgCode));

        $orgDetatils['eStartYear'] = '';
        if (!empty($orgDetatils['Start_Year'])) {
            $startYear = explode(',', $orgDetatils['Start_Year']);
            $startMonth = date('F', strtotime($startYear[0]));
            $orgDetatils['eStartYear'] = $startMonth . ' ' . str_replace(' ', '', $startYear[1]);
        }

        return $orgDetatils;
    }

    public function getDepartmentClassification($orgCode = null)
    {
        $orgDetatils = $this->_db->fetchOne($this->_db->select()
                ->from(array('ORGINFO' => $this->_ehrTables->orgDetails), array('Department_Classifications'))
                ->where('ORGINFO.Org_Code = ?', $orgCode));

        return $orgDetatils;
    }

    /*
     * retreive specific record from OrgConPerson table
     *
     * @param str $orgCode - Org_Code
     * @return null/row
     *
     */
    public function viewOrgContactDetail($orgCode)
    {
        $fetchQry = $this->_db->select()->from(array('ORGPER' => $this->_ehrTables->orgContact, array('Contact_Id', 'Employee_Id')))
            ->joinInner(
                array('P' => $this->_ehrTables->empPersonal),
                'P.Employee_Id=ORGPER.Employee_Id',
                array(new Zend_Db_Expr("CONCAT(P.Emp_First_Name, ' ', P.Emp_Last_Name) as Employee_Name"), 'Emp_First_Name as First_Name', 'Emp_Last_Name as Last_Name')
            )
            ->joinInner(
                array('J' => $this->_ehrTables->empJob),
                'J.Employee_Id=ORGPER.Employee_Id',
                array('Emp_Email as Email_Id')
            )
            ->joinInner(
                array('EC' => $this->_ehrTables->empContacts),
                'EC.Employee_Id=ORGPER.Employee_Id',
                array('Land_Line_No as Landline_No', 'Mobile_No')
            )

            ->joinInner(
                array('EU' => $this->_ehrTables->empLogin),
                'EU.Employee_Id=ORGPER.Employee_Id',
                array('User_Name')
            )

            ->joinInner(
                array('L' => $this->_ehrTables->location),
                'J.Location_Id=L.Location_Id',
                array('Country_Code as Country_Id')
            )

            ->joinInner(array('C' => $this->_ehrTables->country), 'C.Country_Code=L.Country_Code', array('C.Country_Name'))
            ->where('ORGPER.Contact_Id = ?', $orgCode);

        return $this->_db->fetchRow($fetchQry);
    }
    public function viewEmployeeDetails($employeeId)
    {
        $fetchQry = $this->_db->select()->from(
            array('J' => $this->_ehrTables->empJob),
            array('Emp_Email as Email_Id')
        )
            ->joinInner(
                array('EC' => $this->_ehrTables->empContacts),
                'EC.Employee_Id=J.Employee_Id',
                array('Land_Line_No as Landline_No', 'Mobile_No')
            )

            ->joinInner(
                array('L' => $this->_ehrTables->location),
                'J.Location_Id=L.Location_Id',
                array('Country_Code as Country_Id')
            )

            ->joinInner(array('C' => $this->_ehrTables->country), 'C.Country_Code=L.Country_Code', array('C.Country_Name'))
            ->where('J.Employee_Id = ?', $employeeId);

        return $this->_db->fetchRow($fetchQry);
    }

    /*
     * returns array Country_Code as key,country_name as value
     */
    public function getCountryName($countryCode)
    {
        $countryName = $this->_db->select()->distinct()->from(
            $this->_ehrTables->country,
            'Country_Name'
        )->where('Country_Code =?', $countryCode);
        return $this->_db->fetchOne($countryName);
    }

    /**
     * Update contact details
     */
    public function updateContactDetails($formData, $sessionId)
    {
        $formData['Lock_Flag'] = 0;
        //$whereEmployeeId['Employee_Id = ?'] = $formData['Employee_Id'];
        $whereEmployeeId['Contact_Id = ?'] = $formData['Contact_Id'];
        $updated = $this->_db->update($this->_ehrTables->orgContact, $formData, $whereEmployeeId);

        if ($updated) {
            $whereCode['Org_Code = ?'] = $formData['Contact_Id'];

            $registerUserDetails = $this->viewOrgContactDetail($formData['Contact_Id']);

            $registerUser = array('First_Name' => $registerUserDetails['First_Name'],
                'Last_Name' => $registerUserDetails['Last_Name'],
                'User_Name' => $registerUserDetails['User_Name'],
                'Email_Id' => $registerUserDetails['Email_Id'],
                'Country_Id' => $registerUserDetails['Country_Id'],
                'Landline_No' => $registerUserDetails['Landline_No'],
                'Mobile_No' => $registerUserDetails['Mobile_No']);

            $updated = $this->_salesDb->update($this->_ehrTables->regUser, $registerUser, $whereCode);

            return $this->_commonFunction->updateResult(array('updated' => $updated,
                'action' => 'Edit',
                'trackingColumn' => $formData['Contact_Id'],
                'formName' => 'Contact Details',
                'sessionId' => $sessionId,
                'tableName' => $this->_ehrTables->orgContact));
        }
    }

    /**
     * Update organization info
     */
    public function updateOrgInfo($orgDetArray, $regUserTabUpdateArr, $orgCode, $logEmpId, $salaryRecalc)
    {
        $whereOrg['Org_Code = ?'] = $orgCode;

        $updated = $this->_db->update($this->_ehrTables->orgDetails, $orgDetArray, $whereOrg);

        if ($updated) {
            $this->_ehrTables->trackEmpSystemAction('Edit Organization Details - ' . $orgCode, $logEmpId);

            $this->_salesDb->update($this->_ehrTables->regUser, $regUserTabUpdateArr, $whereOrg);

            //if salary calculation method is changed, then we are setting the salary recalculation process
            if ($salaryRecalc == 1) {
                $this->_db->update($this->_ehrTables->salary, array('Salary_Recalc' => 1));
            }

            return true;
        }
    }
    
    public function updateOrganizationDetails($formData, $hrGroup, $payrollGroup, $mailCommForm,$sessionId, $formName)
    {
        
            $whereOrg['Org_Code = ?'] = $formData['Org_Code'];

            //update organization name and description in manager db
            $managerDbUpdate = array('Org_Name' => $formData['Org_Name'],'Org_Description' => $formData['Org_Description']);

            $startYear = explode('-', $formData['Start_Year']);
            $monthName = date("F", strtotime($formData['Start_Year']));
            $monthName = substr($monthName, 0, 3);

            $formData['Start_Year'] = $monthName . ', ' . $startYear[0];
            $formData['Lock_Flag'] = 0;

            $getOrgDetails = $this->viewOrgDetail($formData['Org_Code']);

            if(strtolower($getOrgDetails['Consider_Cutoff_Days_For_Attendance_And_Timeoff']) === 'yes' && 
            ($getOrgDetails['Cutoff_Day'] != $formData['Cutoff_Day'])){
                $isPayslipExists = $this->_commonFunction->checkPayslipExistsInOrgLevel();
                if($isPayslipExists === 1){
                    return array('success' => false, 'msg' => 'You are not allowed to update the cutoff days, since you have enabled the advance payroll and generated the payslip.', 'type' => 'warning');
                }
            }

            if (!empty($getOrgDetails)) {
                //Get the rating published record count only when the performance start month is changed
                $isEmpRatingsPublished = ($getOrgDetails['Performance_StartMonth'] != $formData['Performance_StartMonth']) ? $this->getRatingPublishedCount() : 0;
                
                // if the ratings are not published
                if(empty($isEmpRatingsPublished)){
                    $dbLeaves               = new Employees_Model_DbTable_Leave();
                    $getLeaveTypeCount      = $dbLeaves->leavetypeCount();
                    $updatePaycycleDetails  = 0;

                    /* If old and the new paycyle same */
                    if($getOrgDetails['Paycycle'] == $formData['Paycycle']){
                        if($formData['Paycycle'] == 'Calendar Month'){
                            $updatePaycycleDetails = 0;
                        }else{
                            /* Call the function to check the whether monthly or hourly payslip exists in the organization level */
                            $isPayslipExists = $this->_commonFunction->checkPayslipExistsInOrgLevel();

                            /* If old and new paycycle end day is same */
                            if(($getOrgDetails['Paycycle_End_Day'] == $formData['Paycycle_End_Day'])){
                                $updatePaycycleDetails = 0;
                            }else if(($getOrgDetails['Paycycle_End_Day'] != $formData['Paycycle_End_Day']) && empty($isPayslipExists) && empty($getLeaveTypeCount)){
                                /* If old and new paycycle end day differ but payslip not exists and leave type not exist then paycycle end day can be changed. */
                                $updatePaycycleDetails = 1;
                            }else{
                                /* If old and new paycycle end day differ and payslip exists then paycycle end day cannot be changed. */
                                if(!empty($isPayslipExists)){
                                    return array('success' => false, 'msg' => 'Paycycle end day cannot be changed as there are payslips already generated using this configuration.', 'type' => 'warning');
                                }else{
                                    /* If old and new paycycle end day differ and leave type exists then paycycle end day cannot be changed. */
                                    return array('success' => false, 'msg' => 'Paycycle end day cannot be changed as there is leave type(s) already created and leave balance updated using this configuration.', 'type' => 'warning');
                                }
                            }
                        }
                    }else{
                        /* Call the function to check the whether monthly or hourly payslip exists in the organization level */
                        $isPayslipExists = $this->_commonFunction->checkPayslipExistsInOrgLevel();

                        /* If monthly or hourly payslip not exists and leave type not exist the paycycle type can be changed*/
                        if(empty($isPayslipExists) && empty($getLeaveTypeCount)){
                            $updatePaycycleDetails = 1;
                        }else{
                            /* If old and new paycycle end day differ and payslip exists then paycycle type cannot be changed. */
                            if(!empty($isPayslipExists)){
                                return array('success' => false, 'msg' => 'Paycycle cannot be changed as there are payslips already generated using this configuration.', 'type' => 'warning');
                            }else{
                                /* If old and new paycycle end day differ and leave type exists then paycycle cannot be changed. */
                                return array('success' => false, 'msg' => 'Paycycle cannot be changed as there is leave type(s) already created and leave balance updated using this configuration.', 'type' => 'warning');
                            }
                        }
                    }
                    
                    if($getOrgDetails['Field_Force'] != $formData['Field_Force'])
                    {
                        $employeeServiceProviderExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob, array('Service_Provider_Id'))
                                                                    ->where('Service_Provider_Id > 0'));

                        $dataImportServiceProviderExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->employeeImport, array('Service_Provider_Name'))
                                                                    ->where('Service_Provider_Name IS NOT NULL OR Service_Provider_Name !=?',''));
                                                                    
                        $candidateServiceProviderExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->candidateJob, array('Service_Provider_Id'))
                                                                    ->where('Service_Provider_Id > 0'));                                            

                        //when the service provider is associated with employee,data import,self onboarding we should not allow the user to update the field force configuration.
                        if(!empty($employeeServiceProviderExist) || !empty($dataImportServiceProviderExist) || !empty($candidateServiceProviderExist))
                        {
                            return array('success' => false, 'msg' => 'Field Force cannot be changed as there are employees already associated with the service provider in the following forms employees, employee import, employee onboarding', 'type' => 'warning');
                        }
                    }    
                    
                    /* check that if org form data and org details data are same and check whether the paycycle and
                    paycycle end day has to be updated or not */
                    if (empty($updatePaycycleDetails) && ($getOrgDetails['Start_Year'] == $formData['Start_Year']
                        && $getOrgDetails['Performance_StartMonth'] == $formData['Performance_StartMonth'] 
                        && $getOrgDetails['Field_Force'] == $formData['Field_Force'] 
                        && $getOrgDetails['Tax_Proof_Submission_Month'] == $formData['Tax_Proof_Submission_Month']
                        && $getOrgDetails['Date_Format'] == $formData['Date_Format'] && $getOrgDetails['Employee_Edit'] == $formData['Employee_Edit']
                        && $getOrgDetails['Attendance_Enforced_Payment'] == $formData['Attendance_Enforced_Payment']
                        && $getOrgDetails['Performance_Enforced_Payment'] == $formData['Performance_Enforced_Payment']
                        && $getOrgDetails['Department_Classifications'] == $formData['Department_Classifications']
                        && $getOrgDetails['Show_Report_Creator'] == $formData['Show_Report_Creator']
                        && $getOrgDetails['Data_Setup_Prerequisites'] == $formData['Data_Setup_Prerequisites']
                        && $getOrgDetails['Attendance_Process_Type_Id'] == $formData['Attendance_Process_Type_Id']
                        && $getOrgDetails['Multiple_Source_Attendance_Same_Time'] == $formData['Multiple_Source_Attendance_Same_Time']
                        && $getOrgDetails['Display_Payslip_Address'] == $formData['Display_Payslip_Address']
                        && $getOrgDetails['Restrict_Emp_Access_For_Manager'] == $formData['Restrict_Emp_Access_For_Manager']
                        && $getOrgDetails['Payslip_Address_Choice'] == $formData['Payslip_Address_Choice']
                        && $getOrgDetails['Location_Id'] == $formData['Location_Id']
                        && $getOrgDetails['Payslip_Password_Protected'] == $formData['Payslip_Password_Protected']
                        && $getOrgDetails['Display_Reimbursement_Payslip'] == $formData['Display_Reimbursement_Payslip']
                        && $getOrgDetails['Display_UnpaidLeave_Deduction'] == $formData['Display_UnpaidLeave_Deduction']
                        && $getOrgDetails['Roundoff_Netpay'] == $formData['Roundoff_Netpay']
                        && $getOrgDetails['Consider_Cutoff_Days_For_Attendance_And_Timeoff'] == $formData['Consider_Cutoff_Days_For_Attendance_And_Timeoff']
                        && $getOrgDetails['Roundoff_Gross_Monthly_Salary'] == $formData['Roundoff_Gross_Monthly_Salary']
                        && $getOrgDetails['Location_Translation'] == $formData['Location_Translation']
                        && $getOrgDetails['IP_Address_Restriction'] == $formData['IP_Address_Restriction']
                        && $getOrgDetails['Resignation_Date_Override'] == $formData['Resignation_Date_Override']
                        && $getOrgDetails['Attendance_Log_Retention_Period'] == $formData['Attendance_Log_Retention_Period']
                        && $getOrgDetails['Payment_Day'] == $formData['Payment_Day'] && $getOrgDetails['Cutoff_Day'] == $formData['Cutoff_Day']
                        && $getOrgDetails['Org_Name'] == $formData['Org_Name'] 
                        && $getOrgDetails['Org_Description'] == $formData['Org_Description']&& $getOrgDetails['TDS_Exemption'] == $formData['TDS_Exemption']
                        && $getOrgDetails['Restrict_Financial_Access_For_Manager'] == $formData['Restrict_Financial_Access_For_Manager'] 
                        && $getOrgDetails['Represent_Basic_As_Multi_Components'] == $formData['Represent_Basic_As_Multi_Components'] && 
                        $getOrgDetails['HR_Admin_Email_Address'] == $formData['HR_Admin_Email_Address'])) {
                        /** no need to update if org form data and org details data are same. so set the orgupdated to 1 **/
                        $orgUpdated = 1;
                    } else {
                        $orgUpdated = $this->_db->update($this->_ehrTables->orgDetails, $formData, $whereOrg);

                        /** If organization settings details updated in the table then set in the zend registry */
                        if($orgUpdated){
                            $newOrgDetails = $this->viewOrgDetail($formData['Org_Code']);
                            
                            Zend_Registry::set('orgDetails', $newOrgDetails);
                        }
                        
                        /* If the Ip address restriction flag is enabled remove firebase Uid associated with the employee id to remove the user session */
                        /* if($orgUpdated && ($getOrgDetails['IP_Address_Restriction'] == 0 && $formData['IP_Address_Restriction'] == 1)){
                            $whitelistedIPAddress = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->whitelistedIpAddresses, array("Ip_Address")));
                            if(!empty($whitelistedIPAddress)){
                                $removeFirbaseUid = $this->_db->update($this->_ehrTables->empLogin, array('Firebase_Uid' => NULL));
                            }
                        } */
                    }                
                }else{
                    return array('success' => false, 'msg' => 'Performance start month cannot be changed as the employee ratings are already published using this configuration', 'type' => 'warning');
                }
            } else {
                $orgUpdated = 0;
            }

            $whereGrp = 1;

            /** check that hr group employees exists **/
            $getHRGroupCount = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->hrGroup, 'COUNT(HR_Group_Id)'));
            if ($getHRGroupCount > 0) {
                $hupdated = $this->_db->delete($this->_ehrTables->hrGroup, $whereGrp);
            } else {
                $hupdated = 1;
            }

            /** If Hr group exists, then insert **/
            if (!empty($hrGroup)) {
                $hrGroup = explode(',', $hrGroup);
                foreach ($hrGroup as $hr) {
                    $hrGroupData['HR_Group_Id'] = $hr;
                    $this->_db->insert($this->_ehrTables->hrGroup, $hrGroupData);
                }
            }

            /** check that payroll group employees exists **/
            $getPayrollGroupCount = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->payrollGroup, 'COUNT(Payroll_Group_Id)'));
            if ($getPayrollGroupCount > 0) {
                $pupdated = $this->_db->delete($this->_ehrTables->payrollGroup, $whereGrp);
            } else {
                $pupdated = 1;
            }

            /** If Payroll group exists, then insert **/
            if (!empty($payrollGroup)) {
                $payrollGroup = explode(',', $payrollGroup);
                foreach ($payrollGroup as $payrollGrp) {
                    $payrollGroupData['Payroll_Group_Id'] = $payrollGrp;
                    $this->_db->insert($this->_ehrTables->payrollGroup, $payrollGroupData);
                }
            }

            /** check that mail group employees exists **/
            $getmailGroupCount = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->mailCommunicationForms, 'COUNT(Form_Id)'));
            if ($getmailGroupCount > 0) {
                $mupdated = $this->_db->delete($this->_ehrTables->mailCommunicationForms, $whereGrp);
            } else {
                $mupdated = 1;
            }

            /** If mail group exists, then insert **/
            if (!empty($mailCommForm)) {
                $mailGroup = explode(',', $mailCommForm);
                foreach ($mailGroup as $mailGrp) {
                    $mailGroupData['Form_Id'] = $mailGrp;
                    $this->_db->insert($this->_ehrTables->mailCommunicationForms, $mailGroupData);
                }
            }

            if ($orgUpdated && $hupdated && $pupdated && $mupdated) {
                //$this->_ehrTables->trackEmpSystemAction('Edit Organization Details - '.$orgCode, $logEmpId);
                $this->_salesDb->update($this->_ehrTables->regUser,$managerDbUpdate,$whereOrg);

                /** Update the DataSetup status **/
                if ($orgUpdated) {
                    $orgUpdated = $this->_commonFunction->updateDataSetupDashboard('Completed', '10');
                }

                //return true;
                $result = $this->_commonFunction->updateResult(array('updated' => $orgUpdated,
                    'action' => 'Edit',
                    'trackingColumn' => $formData['Org_Code'],
                    'formName' => $formName,
                    'sessionId' => $sessionId,
                    'tableName' => $this->_ehrTables->orgDetails));
                if ($result) {
                    return array('success' => true, 'msg' => $formName . ' updated successfully', 'type' => 'success');
                } else {
                    return array('success' => false, 'msg' => 'Unable to update ' . $formName, 'type' => 'warning');
                }
            } else {
                return array('success' => false, 'msg' => 'Unable to update ' . $formName, 'type' => 'warning');
            }
        
    }

    /**
     * Delete image path
     */
    public function deleteImgPath($orgCode)
    {
        //$where = array('Org_Code = ?' => $orgCode,    $fieldName.' = ?' => $imgPathName);
        $where = array('Org_Code = ?' => $orgCode);

        $updated = $this->_db->update($this->_ehrTables->orgDetails, array('Report_LogoPath' => ''), $where);

        $this->_salesDb->update($this->_ehrTables->regUser, array('Org_Logo' => ''),$where);

        if ($updated == 1) {
            return array('success' => true, 'msg' => 'Report logo deleted successfully', 'ImageExists' => 0, 'type' => 'info');
        } else {
            return array('success' => false, 'msg' => 'Unable to delete report logo', 'ImageExists' => 1, 'type' => 'info');
        }
    }

    /**
     * Check whether the image path exists or not
     */
    public function isImgPathExist($imgPathName, $orgCode, $fieldName)
    {
        return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->orgDetails, $fieldName)->where('Org_Code =' . $this->_db->quote($orgCode))
                ->where($fieldName . ' =' . $this->_db->quote($imgPathName)));
    }

    /**
     * Check whether the image path exists or not
     */
    public function ImageExists($formName, $uniqueId)
    {
        if (formName == "Organization Settings") {
            return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->orgDetails, 'Report_LogoPath')
                    ->where('Org_Code =' . $this->_db->quote($uniqueId)));
        } elseif ($formName == "Employees") {
            return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empPersonal, 'Photo_Path')
                    ->where('Employee_Id =' . $this->_db->quote($uniqueId)));
        } else {
            return 0;
        }
    }

//    //version 0.4=>common function for sending mail with CC added.
    //    public function communicateMail($content, $subject, $sendMessage,$formName)
    //    {
    //        $getMailCommForms = $this->getMailCommunicationForms();
    //
    //        $successMsg = 0;
    //
    //        //if the form is choosen  MailCommunicationForm field (in orgdetail form),then only the mail communication is eligible for that form
    //        if (in_array($formName,$getMailCommForms))
    //        {
    //            $sendMail = new Zend_Mail('utf-8');
    //            $accessRights = new Default_Model_DbTable_AccessRights();
    //            $mailGroup = 'Hr_Group';
    //            if(in_array($formName,array('Timesheets','Attendance','Leaves','Transfer','Resignation','Employee Travel','Awards','Complaints','Performance Evaluation')))
    //            {
    //                $mailGroup = 'Hr_Group';
    //            }
    //            elseif(in_array($formName,array('Bonus','Commission','Deductions','Reimbursement','Advance Salary','Loan','Shift','Tax Declarations','Salary Payslip')))
    //            {
    //                $mailGroup = 'Payroll_Group';
    //            }
    //
    //            $getMailDetail = $this->mailGroup($mailGroup);
    //
    //            if(!empty($getMailDetail))
    //            {
    //                foreach($getMailDetail as $mailDetail)
    //                {
    //                    $statusAccessRights = $accessRights->employeeAccessRights($mailDetail['Employee_Id'], $formName);
    //
    //                    if ($statusAccessRights['Employee'][$mailGroup] == 1 && ($mailDetail['Emp_Email'] !=  $sendMessage[1]['Emp_Email'])
    //                    && ($mailDetail['Emp_Email'] !=  $sendMessage[0]['Emp_Email'])
    //                    && !empty($mailDetail['Emp_Email']))
    //                    {
    //                       //adding employee emailid in CC field
    //                       $sendMail->addCC($mailDetail['Emp_Email'], $mailDetail['EmployeeName']);
    //                    }
    //                }
    //            }
    //
    //            try
    //            {
    //                //for payslip there is no need to set mail layout since we have separate code for payslip design
    //                if ($formName == 'Salary Payslip')
    //                {
    //                    $msgContent = $content;
    //                }
    //                else
    //                {
    //                    $msgContent = $this->_ehrTables->mailLayout("$content", $sendMessage[1]['Employee_Name']);
    //                }
    //
    //                if(!empty($msgContent))
    //                {
    //                    $sendMail
    //                    ->addTo($sendMessage[1]['Emp_Email'], $sendMessage[1]['Employee_Name'])
    //                    ->setFrom($sendMessage[0]['Emp_Email'], $sendMessage[0]['Employee_Name'])
    //                    ->setSubject("$subject")
    //                    ->setBodyHtml($msgContent);
    //                    $resultMail = $sendMail->send();
    //                    $successMsg = 1;
    //                }
    //            }
    //            catch(Zend_Mail_Exception $mailEx)
    //            {
    //
    //            }
    //        }
    //        else
    //        {
    //            $successMsg = 1;
    //        }
    //
    //        return $successMsg;
    //    }

    //checking whether the financial closure has set
    public function checkFinancialClosure($orgCode)
    {
        return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->orgDetails, 'Financial_Closure_Tracking')
                ->where('Org_Code = ?', $orgCode));
    }

    public function updateMailConfiguration($formData, $sessionId, $formName)
    {
        $formData['Lock_Flag'] = 0;

        $exist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->mailConf, 'Incoming_Mail_Server')
                ->where('Mail_Account_Type = ?', $formData['Mail_Account_Type']));

        if ($exist) {
            $updated = $this->_db->update($this->_ehrTables->mailConf, $formData, array('Mail_Account_Type = "' . $formData['Mail_Account_Type'] . '"'));
        } else {
            $updated = $this->_db->insert($this->_ehrTables->mailConf, $formData);
        }

        //if($updated)
        //{
        return $this->_commonFunction->updateResult(array('updated' => $updated,
            'action' => 'Edit',
            'trackingColumn' => $formData['Mail_Account_Type'],
            'formName' => $formName,
            'sessionId' => $sessionId,
            'tableName' => $this->_ehrTables->mailConf));
        //}
    }

    public function viewMailConfiguration()
    {
        return ($this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->mailConf, array('Mail_Account_Type', 'Incoming_Mail_Server', 'Incoming_Server_Port',
            'Incoming_Connection_Security', 'Incoming_Authentication_Method',
            'Outgoing_Mail_Server', 'Outgoing_Server_Port', 'Outgoing_Connection_Security',
            'Outgoing_Authentication_Method', 'Maximum_Attachment_Size'))));
    }

    public function viewTaxConfigurationDetail()
    {
        $taxConfigurationDetail = $this->_db->fetchRow($this->_db->select()->from(
            array('TC' => $this->_ehrTables->taxConfiguration),
            array('Tax_Configuration_Id', 'Org_Type', 'PAN', 'TAN', 'PT_No', 'TDS_Deposit_Method', 'TDS_Payment_Frequency', 'CIT',
                'TDS_Assessment_Range', 'Form16_Signatory')
        )
                ->joinLeft(
                    array('P' => $this->_ehrTables->empPersonal),
                    'P.Employee_Id=TC.Form16_Signatory',
                    array('Employee_Name' => new Zend_Db_Expr("CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name)"))
                )

                ->joinLeft(
                    array('DE' => $this->_ehrTables->empDependent),
                    'DE.Employee_Id=TC.Form16_Signatory AND DE.Relationship= "Father"',
                    array('DE.Dependent_First_Name', 'DE.Dependent_Last_Name', 'Dependent_Name' => new Zend_Db_Expr("CONCAT(DE.Dependent_First_Name,' ',DE.Dependent_Last_Name)"))
                ));

        $form16TaxSection = $this->_db->fetchAll($this->_db->select()->from(
            array('TSM' => $this->_ehrTables->taxFormSectionMapping),
            array('TSM.Tax_Form_Id', 'TSM.Tax_Section_Id')
        )
//                                        ->joinLeft(array('F'=>$this->_ehrTables->forms), 'F.Form_Id=TSM.Tax_Form_Id',
            //                                        array('F.Form_Name'))
                ->joinLeft(
                    array('TS' => $this->_ehrTables->taxSections),
                    'TS.Tax_Section_ID=TSM.Tax_Section_Id',
                    array( /*'TS.Tax_Section_Name'*/)
                )
                ->joinLeft(
                    array('GT' => $this->_ehrTables->govtTaxSec),
                    'TS.Section_Id=GT.Section_Id',
                    array('GT.Section_Name as Tax_Section_Name')
                ));
        $form16TaxSectionNameArr = $form16TaxSectionIdArr = array();
        foreach ($form16TaxSection as $form16Tax) {
            array_push($form16TaxSectionNameArr, $form16Tax['Tax_Section_Name']);
            array_push($form16TaxSectionIdArr, $form16Tax['Tax_Section_Id']);
        }
        $taxSectionName = implode(",", $form16TaxSectionNameArr);
        $taxSectionId = implode(",", $form16TaxSectionIdArr);
        $taxConfigurationDetail['Form16TaxSectionName'] = $taxSectionName;
        $taxConfigurationDetail['Form16TaxSectionId'] = $taxSectionId;
        //return array('TaxConfigDetails'=>$taxConfigurationDetail,'Form16TaxSectionName'=>$taxSectionName);
        return $taxConfigurationDetail;
    }

    /** Function to update the tax configuration in the DB */
    public function updateTaxConfiguration($taxConfigData, $form16SectionMapping, $form16SigFatherFName, $form16SigFatherLName, $sessionId, $formName){
        $updated = $this->_db->update($this->_ehrTables->taxConfiguration, $taxConfigData);

        /** If the tax configuration updated.
         *  Or if the tax configuration not updated then either form 16 signatory or form 16 section mapping should exists
         */
        if($updated || (empty($updated) && (!empty($taxConfigData['Form16_Signatory']) || !empty($form16SectionMapping)))){
            $isEmpDependentAdded = 0;
            $oldTaxSectionDeleted = $taxsectionupdate = $sectionMappingExist = 0;
            $employeeName = $taxSectionName = $trackingMsg = $responseMsg = '';

            $form16Id = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->forms, 'Form_Id')
                    ->where('Form_Name = ?', 'Form16'));
            
            $employeeName = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empPersonal, new Zend_Db_Expr("CONCAT(Emp_First_Name,' ',Emp_Last_Name)"))
                                    ->where('Employee_Id = ?', $taxConfigData['Form16_Signatory']));

            /** Get the form16 tax section ids */
            $updatedForm16TaxSectionIds = $form16TaxSectionIds = $this->taxConfigsectionIds($form16Id);

            /** If the form 16 signatory exists */
            if (!empty($taxConfigData['Form16_Signatory'])) {
                $form16SignatoryFatherNameExists = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empDependent, 'COUNT(Dependent_Id)')
                                                    ->where('Employee_Id = ?', $taxConfigData['Form16_Signatory'])
                                                    ->where('Relationship = ?', 'Father'));

                /** If the form 16 signatory father name not exists in the table */
                if (empty($form16SignatoryFatherNameExists)) {
                    $dependentData = array('Employee_Id' => $taxConfigData['Form16_Signatory'],
                        'Dependent_First_Name' => $form16SigFatherFName,
                        'Gender' => 'male',
                        'Relationship' => 'Father');
                    if(!empty($form16SigFatherLName)){
                        $dependentData['Dependent_Last_Name'] = $form16SigFatherLName;
                    }
                    $isEmpDependentAdded = $this->_db->insert($this->_ehrTables->empDependent, $dependentData);

                    $this->_commonFunction->updateResult (array('updated'        => $isEmpDependentAdded,
                    'action'         => 'Add',
                    'trackingColumn' => $this->_db->lastInsertId(),//get last inserted id
                    'formName'       => 'Employee dependent',
                    'sessionId'      => $sessionId));
                }else{
                    $isEmpDependentAdded = 1;
                }
            }
          
            /** If the form16 signatory not exists or if the form 16 signatory dependent details is added when the
             *  form16 signatory exist */
            if(empty($taxConfigData['Form16_Signatory']) || (!empty($taxConfigData['Form16_Signatory']) && $isEmpDependentAdded)){
                /** If the form16 section exists */
                if (!empty($form16SectionMapping)) {
                    $sectionMappingExist = 1;

                    /** If the tax section mapping not exists */
                    if (count($form16TaxSectionIds) === 0) {
                        $insertTaxSectionMapping = 1;
                    } else {
                        /** If the tax section mapping exists delete it. If deleted then insert the new sections */
                        $oldTaxSectionDeleted = $this->_db->delete($this->_ehrTables->taxFormSectionMapping, 'Tax_Form_Id = "' . $form16Id . '"');

                        /** If the old tax section(s) deleted then set the value to 1 to insert the new tax section(s). If old tax 
                         * section(s) not deleted then 0 */
                        $insertTaxSectionMapping = ($oldTaxSectionDeleted) ? 1 : 0;
                    }
                    
                    /** If form16 tax section can be inserted */
                    if(!empty($insertTaxSectionMapping)){
                        $taxSectionTotInsertionCount = 0;

                        $taxSectionMappingData = array('Tax_Form_Id' => $form16Id);

                        /** Insert the form16 and the tax section in a mapping table using iteration  */
                        foreach ($form16SectionMapping as $taxSection) {
                            $taxSectionMappingData['Tax_Section_Id'] = $taxSection;

                            /** Insert the form16 tax section in the table */
                            $taxSectionInserted = $this->_db->insert($this->_ehrTables->taxFormSectionMapping, $taxSectionMappingData);

                            $taxSectionTotInsertionCount += $taxSectionInserted;
                        }

                        $taxsectionupdate = (count($form16SectionMapping) === $taxSectionTotInsertionCount) ? 1 : 0;

                        /** If the tax section fully updated or partially updated */
                        if(empty($taxsectionupdate)){
                            $trackingMsg = 'Edit '.$formName.' - Form16 tax section(s) not updated - ';
                        }
                    }else{
                        $trackingMsg = 'Edit '.$formName.' - Form16 tax section(s) not updated - ';
                    }
                } else {
                    if (count($form16TaxSectionIds) > 0) {
                        $oldTaxSectionDeleted = $this->_db->delete($this->_ehrTables->taxFormSectionMapping, 'Tax_Form_Id = "' . $form16Id . '"');

                        /** If the old tax section not deleted */
                        if(empty($oldTaxSectionDeleted)){
                            $trackingMsg = 'Edit '.$formName.' - Form16 old tax section(s) not deleted - ';
                        }
                    }else{
                        $oldTaxSectionDeleted = 1;
                    }
                }

                /** Get the form16 new tax section ids */
                $updatedForm16TaxSectionIds = $this->taxConfigsectionIds($form16Id);

                /** If the old tax section are deleted or if the old tax section deleted and new tax sections added */
                if ((($sectionMappingExist && $taxsectionupdate) || (empty($sectionMappingExist) && $oldTaxSectionDeleted))) {
                    $updated = 1; //if the tax configuration updated
                } else if((($sectionMappingExist && !$taxsectionupdate) || (empty($sectionMappingExist) && !$oldTaxSectionDeleted))){
                    /** If the new tax sections are included but not updated in the table. Or if the old tax sections not deleted
                     * from the table
                     */
                    $responseMsg = $formName.' updated successfully but unable to update form 16 section mapping.';
                }               
                else {
                    $responseMsg = 'Unable to update ' . $formName;
                }
            }else{
                /** If the form16 signatory dependent details not updated in the table */
                $trackingMsg = 'Edit '.$formName.' - Form16 signatory dependent details & new section(s) not updated - ';
                $responseMsg = $formName.' updated successfully but unable to update form16 signatory dependent details and new section(s) if added.';
            }

            /** If the form 16 section ids exist */
            if(!empty($updatedForm16TaxSectionIds)){
                $form16TaxSection = $this->_db->fetchAll($this->_db->select()->from(array('TS' => $this->_ehrTables->taxSections),array())
                                        ->joinInner(array('GT' => $this->_ehrTables->govtTaxSec),'TS.Section_Id = GT.Section_Id', 
                                            array('Section_Name as Tax_Section_Name'))
                                        ->where('TS.Tax_Section_ID IN (?)', $updatedForm16TaxSectionIds));

                $form16TaxSectionNameArr = array();
                foreach ($form16TaxSection as $form16Tax) {
                    array_push($form16TaxSectionNameArr, $form16Tax['Tax_Section_Name']);
                }
                $taxSectionName = implode(",", $form16TaxSectionNameArr);
            }

            /** If the tax configuration updated or if the dependent details not updated
             * or if the new tax sections are included but not updated in the table or if the old
             *  tax sections not deleted from the table then log in the system log
            */
            $updated = ($updated || $trackingMsg) ? 1 : 0;

            $systemLogParams = array('updated' => $updated,
            'action' => 'Edit',
            'trackingColumn' => $taxConfigData['Org_Type'],
            'formName' => $formName,
            'sessionId' => $sessionId,
            'tableName' => $this->_ehrTables->taxConfiguration);

            /** If the custom system log message exists then include it in the system log params*/
            if($trackingMsg){
                $systemLogParams['trackingMsg'] = $trackingMsg;
            }            

            /** log in the system log if tax configuration updated */
            $trackSysLog = $this->_commonFunction->updateResult($systemLogParams);

            $trackSysLog['success'] = true;//return success as true to close edit form as we do not know which table(s) are updated
            $trackSysLog['employeeName'] = $employeeName;
            $trackSysLog['TaxSectionName'] = $taxSectionName;
            $trackSysLog['form16SignatoryDependantUpdated'] = $isEmpDependentAdded;
            $trackSysLog['form16TaxSectionIds'] = $updatedForm16TaxSectionIds;
            $trackSysLog['msg'] = ($responseMsg) ? $responseMsg : $trackSysLog['msg'];

            return $trackSysLog;
        }else{
            return array('success' => 'false', 'msg' => 'Unable to update '.$formName.' as there are no form changes or may be due to some technical difficulties. Please try after sometime.', 'type' => 'warning');
        }
    }

    public function viewGrauitySettingsDetail()
    {
        $gratuitySettingDetail = $this->_db->fetchRow($this->_db->select()->from(
            $this->_ehrTables->gratuitySettings,
            array('Part_Of_GratuityAct' => new Zend_Db_Expr('CASE
																							WHEN Part_Of_GratuityAct = "1"
																								THEN "Yes"
																							ELSE "No" END'),
                'Minimum_Tenure', 'Tax_Exemption_Limit',
                'Working_Days', 'Gov_Salary_Days', 'Org_Salary_Days')
        ));

        return $gratuitySettingDetail;
    }

    public function updateGratuitySettings($gratuityData, $sessionId, $formName)
    {
        $exist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->gratuitySettings, 'Minimum_Tenure'));

        $updated = 0;

        if (empty($exist)) {
            $updated = $this->_db->insert($this->_ehrTables->gratuitySettings, $gratuityData);
        } else {
            $salDays = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->gratuitySettings, array('Working_Days', 'Org_Salary_Days')));

            $updated = $this->_db->update($this->_ehrTables->gratuitySettings, $gratuityData);
            $updated = 1;
            if ($salDays['Working_Days'] != $gratuityData['Working_Days'] || $salDays['Org_Salary_Days'] != $gratuityData['Org_Salary_Days']) {
                $grat = $this->_commonFunction->getGratuityAmount('', '', '', 'GRAT');
            }
        }

        if ($updated) {
            return $this->_commonFunction->updateResult(array('updated' => $updated,
                'action' => 'Edit',
                'trackingColumn' => $gratuityData['Minimum_Tenure'],
                'formName' => $formName,
                'sessionId' => $sessionId,
                'tableName' => $this->_ehrTables->gratuitySettings));
        } else {
            return array('success' => false, 'msg' => 'Unable to update ' . $formName, 'type' => 'warning');
        }
    }

    

    public function getAlertTypes($fetchType = null)
    {
        $alertTypes = $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->alertTypes, array('Alert_Type_Id', 'Alert_Type'))
                ->order('Alert_Type ASC'));

        if ($fetchType == 'combo') {
            $alertTypeIds = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->alertSettings, 'Alert_Type_Id'));

            if (count($alertTypeIds) > 0) {
                foreach ($alertTypes as $key => $row) {
                    if (in_array($key, $alertTypeIds)) {
                        unset($alertTypes[$key]);
                    }
                }
            }
            return $alertTypes;
        } else {
            return array('success' => false, 'msg' => 'All Alert Type is exist', 'type' => 'info');
        }
    }
    /** this function is used to load the filter combo - Alert Type **/
    public function comboAlertTypes()
    {
        $alertTypes = $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->alertTypes, array('Alert_Type_Id', 'Alert_Type'))
                ->order('Alert_Type ASC'));

        return $alertTypes;
    }

    public function getAlertTypeName($alerttypId = null)
    {
        $alertTypes = $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->alertTypes, array('Alert_Type_Id', 'Alert_Type'))
                ->order('Alert_Type ASC'));

        $alertTypeIds = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->alertSettings, 'Alert_Type_Id'));

        if (count($alertTypeIds) > 0) {
            foreach ($alertTypes as $key => $row) {
                if (in_array($key, $alertTypeIds)) {
                    unset($alertTypes[$key]);
                }
            }
        } else {
        }
        return $alertTypes;
    }

    public function searchAlertSettings($page, $rows, $sortField, $sortOrder, $logEmpId, $searchAll = null, $searchArray)
    {
        switch ($sortField) {
            case 1:$sortField = 'AT.Alert_Type';
                break;
            case 2:$sortField = 'AS.Period';
                break;
            case 3:$sortField = 'AS.No_Of_Days';
                break;
        }

        $qryAlertTypes = $this->_db->select()
            ->from(
                array('AS' => $this->_ehrTables->alertSettings),
                array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS AS.Alert_Type_Id as count'),
                    'Alert_Type_Id', 'AS.No_Of_Days', 'AS.Period',
                    new Zend_Db_Expr("DATE_FORMAT(AS.Added_On,'" . $this->_orgDF['sql'] . " %H:%i:%s') as Added_On"),
                    new Zend_Db_Expr("DATE_FORMAT(AS.Updated_On,'" . $this->_orgDF['sql'] . " %H:%i:%s') as Updated_On"))
            )

            ->joinInner(
                array('AT' => $this->_ehrTables->alertTypes),
                'AT.Alert_Type_Id=AS.Alert_Type_Id',
                array('Alert_Type')
            )

            ->joinLeft(
                array('emp' => $this->_ehrTables->empPersonal),
                'emp.Employee_Id=AS.Added_By',
                array(new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as Added_By_Name"))
            )

            ->joinLeft(
                array('emp2' => $this->_ehrTables->empPersonal),
                'emp2.Employee_Id=AS.Updated_By',
                array(new Zend_Db_Expr("CONCAT(emp2.Emp_First_Name, ' ', emp2.Emp_Last_Name) as Updated_By_Name"))
            )

            ->order("$sortField $sortOrder")
            ->limit($rows, $page);

        /**
         *    Search All columns using single input
         */
        if (!empty($searchAll) && $searchAll != null) {
            $qryAlertTypes->where($this->_db->quoteInto('AT.Alert_Type Like ?', "%$searchAll%"));
            $qryAlertTypes->orwhere($this->_db->quoteInto('AS.Period Like ?', "%$searchAll%"));
            $qryAlertTypes->orwhere($this->_db->quoteInto('AS.No_Of_Days Like ?', "%$searchAll%"));
        }

        if (!empty($searchArray['Alert_Type'])) {
            $qryAlertTypes->where('AT.Alert_Type_Id Like ?', $searchArray['Alert_Type']);
        }

        if (!empty($searchArray['Period'])) {
            $qryAlertTypes->where('AS.Period Like ?', $searchArray['Period']);
        }

        if (!empty($searchArray['No_Of_Days_Start'])) {
            $qryAlertTypes->where('AS.No_Of_Days >= ?', $searchArray['No_Of_Days_Start']);
        }
        if (!empty($searchArray['No_Of_Days_End'])) {
            $qryAlertTypes->where('AS.No_Of_Days <= ?', $searchArray['No_Of_Days_End']);
        }

        /**
         * SQL queries
         * Get data to display
         */
        $alertTypes = $this->_db->fetchAll($qryAlertTypes);

        /* Data set length after filtering */
        $iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');

        /* Total data set length */
        $iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->alertSettings, new Zend_Db_Expr('COUNT(Alert_Type_Id)')));

        /**
         * Output array
         */
        return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $alertTypes);
    }

    public function updateAlertSettings($alertSettingsData, $alertTypeId, $sessionId, $formName)
    {
        if (empty($alertTypeId)) {
            $alertTypeExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->alertSettings, 'Alert_Type_Id')
                    ->where('Alert_Type_Id = ?', $alertSettingsData['Alert_Type_Id']));
        } else {
            $getAlertTypeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->alertSettings, 'Alert_Type_Id'));

            if ($alertTypeId == $alertSettingsData['Alert_Type_Id']) {
                $alertTypeExist = 0;
            } else {
                if (in_array($alertSettingsData['Alert_Type_Id'], $getAlertTypeId)) {
                    $alertTypeExist = 1;
                } else {
                    $alertTypeExist = 0;
                }
            }
        }

        if ($alertTypeExist == 0) {
            if ($alertTypeId > 0) {
                $action = 'Edit';

                $alertSettingsData['Updated_On'] = date('Y-m-d H:i:s');
                $alertSettingsData['Updated_By'] = $sessionId;
                $alertSettingsData['Lock_Flag'] = 0;

                $updated = $this->_db->update($this->_ehrTables->alertSettings, $alertSettingsData, 'Alert_Type_Id = ' . $alertTypeId);
            } else {
                $action = 'Add';

                $alertSettingsData['Added_On'] = date('Y-m-d H:i:s');
                $alertSettingsData['Added_By'] = $sessionId;

                $updated = $this->_db->insert($this->_ehrTables->alertSettings, $alertSettingsData);

                if ($updated) {
                    $alertTypeId = $this->_db->lastInsertId();
                }
            }

            $alertTypeIds = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->alertSettings, 'Alert_Type_Id'));
            $alertTypes = $this->getAlertTypes();

            if (count($alertTypeIds) > 0) {
                foreach ($alertTypes as $key => $row) {
                    if (in_array($key, $alertTypeIds)) {
                        unset($alertTypes[$key]);
                    }
                }
            }

            return $this->_commonFunction->updateResult(array('updated' => $updated,
                'action' => $action,
                'trackingColumn' => $alertTypeId,
                'formName' => $formName,
                'sessionId' => $sessionId,
                'comboPair' => $alertTypes,
                'tableName' => $this->_ehrTables->alertSettings));
        } else {
            return array('success' => false, 'msg' => 'Alert type already exist', 'type' => 'warning');
        }
    }

    public function getForm16SigFatherNameExists($employeeId)
    {
        $form16SignatoryFatherNameExists = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->empDependent,
                                            array('Dependent_First_Name', 'Dependent_Last_Name'))
                                            ->where('Relationship = ?', 'Father')
                                            ->where('Employee_Id = ?', $employeeId));
        return $form16SignatoryFatherNameExists;
    }

    /**
     * update Logo Name
     */
    public function updateLogoName($logoNameArr, $orgCode,$serviceProviderId=NULL)
    {
        $organizationDetail=$this->viewOrgDetail($orgCode);
        
        if($organizationDetail['Field_Force']==1 && !empty($serviceProviderId))
        {
            $where = array('Service_Provider_Id = ?' => $serviceProviderId);
            $logoNameArray['Service_Provider_Logo'] = $logoNameArr['Report_LogoPath'];
            $this->_db->update($this->_ehrTables->serviceProvider, $logoNameArray, $where);
        }
        else
        {
            $where = array('Org_Code = ?' => $orgCode);    
            $this->_db->update($this->_ehrTables->orgDetails, $logoNameArr, $where);
            $logoNameArray['Org_Logo'] = $logoNameArr['Report_LogoPath'];
            $this->_salesDb->update($this->_ehrTables->regUser,$logoNameArray,$where);
        }
      

        return array('success' => true, 'msg' => 'Report logo uploaded successfully', 'LogoPath' => $logoNameArr['Report_LogoPath'], 'type' => 'info');
    }

    // Add child in multi company configuration
    public function multiCompanyAuth($orgCode, $companyCode, $userName, $password)
    {                                  
        $isDomain     = Zend_Registry::get('Domain');
        $isDomainArray = explode(".",$isDomain);

        $checkDomainParent = $this->_salesDb->fetchRow($this->_salesDb->select()->from($this->_ehrTables->regUser)->where('Org_Code = ?',$orgCode));

        if($checkDomainParent['Parent_Org_Code'] == Null || $checkDomainParent['Parent_Org_Code'] == ''){
            $parentCheck = $this->_salesDb->fetchRow($this->_salesDb->select()->from($this->_ehrTables->regUser)->where('Org_Code = ?',$companyCode));
            //confirm given domain don't have the parent 
            if($parentCheck){
                if($parentCheck['Parent_Org_Code'] == Null || $parentCheck['Parent_Org_Code'] == ''){
                    
                    $childCheck = $this->_salesDb->fetchRow($this->_salesDb->select()->from($this->_ehrTables->regUser)->where('Parent_Org_Code = ?',$companyCode));
                    //confirm given domain is not a parent any other domain
                    if(! $childCheck){
                        $params = array('host'       => Zend_Registry::get('dbHost'),
                                        'username'   => Zend_Registry::get('dbUserName'),
                                        'password'   => Zend_Registry::get('dbPassword'),
                                        // 'encrypt'	 => $dbParams['encrypt'],
                                        // 'charset'	 => $dbParams['charset'],
                                        'port'       => Zend_Registry::get('dbPort'),
                                        'dbname'     => $isDomainArray[0].'_'.$companyCode
                                        );

                        $orgDbConn = null;
                        try {
                            $orgDbConn = Zend_Db::factory('PDO_MYSQL', $params);

                            $qryUser = $orgDbConn->select()->from(array('EU'=>$this->_ehrTables->empLogin))
                            ->joinInner(array('EJ'=>$this->_ehrTables->empJob), 'EU.Employee_Id = EJ.Employee_Id', array('Designation_Id'))
                            ->where('EU.User_Name LIKE ?', $userName)
                            ->where('EU.Hrapp_Password = CONCAT(?,Random_Salt)',md5($password) )
                            ->where('EJ.Emp_Status LIKE ?', 'Active');

                            $empDetails = $orgDbConn->fetchRow($qryUser);

                            $isGivenUserHaveRights = 0;

                            if(!empty($empDetails)){

                                $empId = $empDetails['Employee_Id'];

                                $designationId = $empDetails['Designation_Id'];

                                $isGivenUserHaveRights = $this->_dbPersonal->checkPayrollEmployee($empId,147);

                                if($isGivenUserHaveRights > 0){
                                    //update the parent name for the given domain
                                    $updated = $this->_salesDb->update($this->_ehrTables->regUser, array('Parent_Org_Code'=> $orgCode), array('Org_Code = ?'=> $companyCode));
                                    return array('success' => true, 'msg'=>'success', 'type'=>'success');
                                } else {
                                    return array('success' => false, 'msg'=>"Given user doesn't have admin rights", 'type'=>'info');
                                }
                            } else {
                                return array('success' => false, 'msg'=>"Invalid username and password", 'type'=>'info');
                            }
                        } catch (Exception $e) {
                            error_log('OrgSettings: Database connection error - ' . $e->getMessage());
                            return array('success' => false, 'msg'=>'Database connection error', 'type'=>'error');
                        } finally {
                            // Always clean up the connection
                            if ($orgDbConn) {
                                try {
                                    $orgDbConn->closeConnection();
                                } catch (Exception $closeEx) {
                                    error_log('OrgSettings: Error closing connection - ' . $closeEx->getMessage());
                                }
                                $orgDbConn = null;
                            }
                        }
                    } else {
                        return array('success' => false, 'msg'=>"Given organization is parent of another organization", 'type'=>'info');
                    }
                } else {
                    if($parentCheck['Parent_Org_Code'] == $orgCode) {
                        return array('success' => false, 'msg'=>"Given organization was already mapped with current organization", 'type'=>'info');
                    } else {
                        return array('success' => false, 'msg'=>"Given organization was already mapped with another organization", 'type'=>'info');
                    }
                }
            } else {
                return array('success' => false, 'msg'=>"Invalid organization url", 'type'=>'info');
            }
        } else {
            return array('success' => false, 'msg'=>"Can't add organization because current organization is the child of another organization", 'type'=>'info');
        }
    }

    public function listMultiCompany($orgCode,$sortField, $sortOrder, $page, $rows, $searchAll=null){

        switch ($sortField)
		{
			case 1: $sortField = 'Org_Code'; break;
            case 2: $sortField = 'Org_Name'; break;
            default: $sortField = 'Org_Code';
        }

        $data = $this->_salesDb->select()->from($this->_ehrTables->regUser, array("Org_Code","Org_Name"))
                                ->where('Parent_Org_Code = ?',$orgCode)
                                ->order("$sortField $sortOrder")
								->limit($rows, $page);

        /**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			$conditions  = $this->_salesDb->quoteInto('Org_Code  Like ?', "%$searchAll%");
            $conditions .= $this->_salesDb->quoteInto('or Org_Name  Like ?', "%$searchAll%");
            
			$data->where($conditions);	
        }
        
		$result = $this->_salesDb->fetchAll($data);

        /* Data set length after filtering */
		$iTotalDisplay = $this->_salesDb->fetchOne('select FOUND_ROWS()');
		
        /* Total data set length */
		$iTotal = $this->_salesDb->fetchOne($this->_salesDb->select()->from($this->_ehrTables->regUser, new Zend_Db_Expr('COUNT(Org_Code)'))->where('Parent_Org_Code = ?',$orgCode));
        
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $result);
    }
    public function deleteMultiCompany($childOrg, $orgCode ){

        $updated = $this->_salesDb->update($this->_ehrTables->regUser, array('Parent_Org_Code'=> NULL), array('Org_Code = ?'=> $childOrg));
        
        return array('success' => true, 'msg'=>"Organization deleted successfully", 'type'=>'info');
    }

    /** Get the TDS payment frequency from the tax configuration */
	public function getTaxConfigTDSPaymentFrequency(){
		return $this->_db->fetchOne($this->_db->select()->from(array('TC' => $this->_ehrTables->taxConfiguration),
					array('TDS_Payment_Frequency')));
    }
    
    /** Get the tax configuration - form 16 section ids */
    public function taxConfigsectionIds($formId){
        return $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->taxFormSectionMapping, array('Tax_Section_Id'))
                    ->where('Tax_Form_Id = ?', $formId));
    }

    //Get the rating published record count
    public function getRatingPublishedCount(){
        return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->performanceGoalAchievement, array('COUNT(Performance_Assessment_Id) AS Rating_Published_Count'))
        ->where('Rating_Publish_Status = ?', 'Published'));
    }

    public function __destruct()
    {
        
    }
}