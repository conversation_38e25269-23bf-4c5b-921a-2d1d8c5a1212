<div class='formspace'>

<?php if(empty($this->access))
{
    ?>
	<div class='shift_msg infoBox'></div>
	<br />
	
	<div id="upt_status">

	<?php
	echo "<div class='statuspadding'><div>" . $this->empName['Emp_First_Name'] . " " . $this->empName['Emp_Last_Name'] .
					    " [" . date($this->orgDF.' \a\t H:i:s') . "]</div>";
	echo $this->status . "</div>";
	?>
	</div>
	<br />	
	
			<?php if(count($this->comment)>0)
				 {?>
			<div style="padding-left:50px;padding-right:50px;">
				<a href='#' id='slide_status' class='viewComment'><img src='<?php echo $this->baseUrl('images/comment.png');?>' />Comment</a>
				<div id='status_comment'>
						 <table class='showComment'>
						 <tr><th>Employee Name</th><th>Comment</th><th>Status</th><th>Added On</th></tr>
						 <?php foreach ($this->comment as $comment)
						 {?>
						 	<tr><td><div style='text-align: center;display:block;'><?php echo $comment['Employee_Name']?></div> </td>
						 	<td><div style='display:block;'><?php echo nl2br(html_entity_decode(wordwrap($comment['Emp_Comment'], 40, '<br/>', TRUE)));?></div> </td>
		 					<td><div style='text-align: center;display:block;'><?php echo $comment['Approval_Status']?></div></td>
						 	<td><div style='text-align: center;display:block;'><?php echo $comment['Added_On']?></div> </td></tr>
						 <?php }?>
						 </table>
				</div>
			</div>
		<?php }
		}
		else{
			echo "<div class='txt_center'>".$this->access."</div>";
		}
?>
</div>
