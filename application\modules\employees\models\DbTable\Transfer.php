<?php
//=========================================================================================
//=========================================================================================
/* Program : Transfer.php										   				         *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MQL Query to retrive, add, update transfer details and also to update   *
 * status report.													 					 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Suresh Kumar            Initial Version        	         *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Prasanth                 Changes in file for mobile app    *
 *                                                     1.Extra fields are added in       *
 *                                                     field list of list query.         */
//=========================================================================================
//=========================================================================================
class Employees_Model_DbTable_Transfer extends Zend_Db_Table_Abstract
{
    protected $_db          = null;
	protected $_ehrTables   = null;
    protected $_dbComment   = null;
    protected $_orgDF       = null;
	protected $_dbCommonFun = null;
	protected $_orgDetails    = null;

    public function init()
    {
        $this->_ehrTables   = new Application_Model_DbTable_Ehr();
        $this->_db          = Zend_Registry::get('subHrapp');
        $this->_dbComment   = new Payroll_Model_DbTable_PayrollComment();
        $this->_orgDF       = $this->_ehrTables->orgDateformat();
		$this->_dbCommonFun = new Application_Model_DbTable_CommonFunction();
		if (Zend_Registry::isRegistered('orgDetails'))
			$this->_orgDetails = Zend_Registry::get('orgDetails');
    }
	
	/**
	 * to list and search transfer
	 */
	public function listTransfer($page, $rows, $sortField, $sortOrder, $searchAll=null, $searchArr, $transferAccess)
	{
        
		$employeeName      = $searchArr['employeeName'];
		$departmentId      = $searchArr['departmentId'];
		$location          = $searchArr['locationId'];
		$status            = $searchArr['status'];
		$transferBeginDate = $searchArr['transferBeginDate'];
		$transferEndDate   = $searchArr['transferEndDate'];
		$serviceProviderId = $searchArr['serviceProviderId'];
		
		/**
		 *	Sorting columns based on display column order in grid
		*/
    	switch ($sortField)
		{
			case 1: $sortField = 'EJ.User_Defined_EmpId'; break;
            case 2: $sortField = 'Employee_Name'; break;
			case 3: $sortField = 'Actual_Department'; break;
			case 4: $sortField = 'Department_Name'; break;
			case 5: $sortField = 'L.Location_Name'; break;
			case 6: $sortField = 'T.Approval_Status'; break;
			default:
				$sortField = 'T.Added_On'; $sortOrder = 'desc'; break;
		}
        
		/**
		 *	Comments Query for transfer
		*/
        if (!empty($transferAccess['formName']))
        {
        	$formId = $this->_dbComment->getFormId($transferAccess['formName']);
        	 
        	$qryComment = $this->_db->select()->distinct()->from(array('Cm'=>$this->_ehrTables->comment), 'Parent_Id')
												->where('Cm.Parent_Id = T.Transfer_Id AND Cm.Form_Id='.$formId);
        }
		
		/**
		 *	Query for get transfer details and employee details
		*/
		$qryTransfer = $this->_db->select()
								->from(array('T'=>$this->_ehrTables->transfer),
									   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS T.Transfer_Id as Count'),'T.Transfer_Id',
											 'Added_By', 'Approval_Status', 'Added_On', 'Approver_Id', 'Employee_Id',
											 new Zend_Db_Expr("DATE_FORMAT(T.Transfer_Date,'".$this->_orgDF['sql']."') as Transfer_Date"),
											 'Log_Id'=>new Zend_Db_Expr($transferAccess['LogId']),
											 'Update'=>new Zend_Db_Expr("'".$transferAccess['Update']."'"),
											 'Comment'=> new Zend_Db_Expr('('.$qryComment.')'),
											 'User'=>new Zend_Db_Expr("'".$transferAccess['Admin']."'"),
											 'DT_RowClass' => new Zend_Db_Expr('"transfer"'),
											 'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', T.Transfer_Id)"),
											 'Department_Id','Location_Id', 'Transfer_Date as edit_Transfer_Date',
											 new Zend_Db_Expr("DATE_FORMAT(T.Added_On,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
											 new Zend_Db_Expr("DATE_FORMAT(T.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On"),
											 'DT_RowClass' => new Zend_Db_Expr('"transfer"'),
											 'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', T.Transfer_Id)")))
								
								->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id = T.Employee_Id',
											array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name, ' ', P.Emp_Last_Name)")))
                                
                                 ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'P.Employee_Id=EJ.Employee_Id',
                                            array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN P.Employee_Id ELSE EJ.User_Defined_EmpId END')))


								
								->joinInner(array('D'=>$this->_ehrTables->dept), 'D.Department_Id=T.Department_Id',array('Department_Name'))

								->joinInner(array('D1'=>$this->_ehrTables->dept), 'D1.Department_Id=EJ.Department_Id',array('D1.Department_Name as Actual_Department'))
								
								->joinInner(array('L'=>$this->_ehrTables->location), 'L.Location_Id=T.Location_Id', array('Location_Name'))
								
								->joinInner(array('A'=>$this->_ehrTables->empPersonal), 'A.Employee_Id = T.Approver_Id',
											array('Approver_Name' => new Zend_Db_Expr("CONCAT(A.Emp_First_Name, ' ', A.Emp_Last_Name)")))
								
								->joinLeft(array('E'=>$this->_ehrTables->empPersonal), 'E.Employee_Id = T.Added_By',
										   array('Added_By_Name' => new Zend_Db_Expr("CONCAT(E.Emp_First_Name, ' ', E.Emp_Last_Name)")))
								
								->order("$sortField $sortOrder")
								->limit($rows, $page);
        
        if(empty($transferAccess['Admin']))
        {
            $employeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
											   ->where('Manager_Id = ?', $transferAccess['LogId']));
			
            if ( $transferAccess['Is_Manager'] == 1 && !empty($employeeId))
			{
                $qryTransfer->where('T.Employee_Id = :EmpId or T.Approver_Id = :EmpId or T.Employee_Id IN (?)', $employeeId)
							->bind(array('EmpId'=>$transferAccess['LogId']));
            }
            else
			{
                $qryTransfer->where('T.Employee_Id = ?', $transferAccess['LogId']);
            }
        }
        
		/**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			$conditions  = $this->_db->quoteInto(new Zend_Db_Expr('Concat(P.Emp_First_Name," ",P.Emp_Last_Name) Like ?'),"%$searchAll%");
			$conditions .= $this->_db->quoteInto('or D.Department_Name  Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or L.Location_Name  Like ?', "%$searchAll%");
			//$conditions .= $this->_db->quoteInto('or P.Emp_First_Name Like ?', "%$searchAll%");
			//$conditions .= $this->_db->quoteInto('or P.Emp_Last_Name Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or T.Transfer_Date Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or T.Approval_Status Like ?', "%$searchAll%");
             $conditions .= $this->_db->quoteInto('or EJ.User_Defined_EmpId Like ?', "%$searchAll%");
			
			$qryTransfer->where($conditions);		
		}
		
		/* Filter for Employee Name */ 
        if ( ! empty($employeeName) )
		{
			$qryTransfer->where(new Zend_Db_Expr('Concat(P.Emp_First_Name," ",P.Emp_Last_Name) Like ?'),"%$employeeName%");
            //->where('T.Employee_Id = ?', $employeeName);
        }
         
		/* Filter for transfer start Date */
		if ($transferBeginDate != '')
		{
		    $qryTransfer->where($this->_db->quoteInto('T.Transfer_Date >= ?', $transferBeginDate));
		}
		
		/* Filter for transfer end Date */
		if ($transferEndDate != '')
		{	
			$qryTransfer->where($this->_db->quoteInto('T.Transfer_Date <= ?', $transferEndDate));
		}
         
		/* Filter for department */
        if ( ! empty($departmentId) )
		{
            $qryTransfer->where('T.Department_Id = ?', $departmentId);
        }
		
		/* Filter for location */
        if ( ! empty($location) )
		{
            $qryTransfer->where('T.Location_Id = ?', $location);
        }
		
		/* Filter for status */
        if (!empty($status) )
		{
			if($status == 'AlertsApplied')
			{
				$qryTransfer->where('T.Approval_Status = ?', 'Applied')
				            ->where('T.Approver_Id = ?', $transferAccess['LogId']);
			}
			else
			{
				$qryTransfer->where('T.Approval_Status = ?', $status);
			}
		}
		if(!empty($serviceProviderId)&& $this->_orgDetails['Field_Force']==1)
        {
            $qryTransfer->where('EJ.Service_Provider_Id = ?',$serviceProviderId);
        }
		
		$qryTransfer = $this->_dbCommonFun->getDivisionDetails($qryTransfer,'EJ.Department_Id');
		
		if(!empty($transferAccess['Admin']))
		{
			$qryTransfer = $this->_dbCommonFun->formServiceProviderQuery($qryTransfer,'EJ.Service_Provider_Id',$transferAccess['LogId']);
		}
		
		/**
		 * SQL queries
		 * Get data to display
		*/
		$transfer = $this->_db->fetchAll($qryTransfer);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		/* Total data set length */
		$qryTransferCnt = $this->_db->select()->from($this->_ehrTables->transfer, new Zend_Db_Expr('COUNT(Transfer_Id)'));
		
		if(empty($transferAccess['Admin']))
        {
            $employeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
											   ->where('Manager_Id = ?', $transferAccess['LogId']));
			
            if ( $transferAccess['Is_Manager'] == 1 && !empty($employeeId))
			{
                $qryTransferCnt->where('Employee_Id = :EmpId or Approver_Id = :EmpId or Employee_Id IN (?)', $employeeId)
							->bind(array('EmpId'=>$transferAccess['LogId']));
            }
            else
			{
                $qryTransferCnt->where('Employee_Id = ?', $transferAccess['LogId']);
            }
        }
		
		$iTotal = $this->_db->fetchOne($qryTransferCnt);
		
		/**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $transfer);
	}
	
	/**
	 *	Update transfer details in transfer table and update comment too if exist
    */
	public function updateTransfer($transferArray, $commentArray, $sessionId, $formName,$customFormName)
    {
		/** Transfer cannot be applied for same location and same department. So while add/update, message will be shown if
		transfer is applied for same department/ same location **/
		$getEmpDepLoc = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->empJob, array('Location_Id','Department_Id'))
													->where('Employee_Id = ?', $transferArray['Employee_Id']));		
		
		if(($getEmpDepLoc['Location_Id'] == $transferArray['Location_Id']) && ($getEmpDepLoc['Department_Id'] == $transferArray['Department_Id']))
		{
			return array('success' => false, 'msg'=>"You can't apply transfer for same department and same location", 'type'=>'info');
		}
		else
		{
			// Check whether updating record is a approved record
			$ckApprovedRecord = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->transfer, new Zend_Db_Expr('count(Employee_Id)'))
														->where("Approval_Status = 'Approved'")
														->where('Transfer_Date >= ?', $transferArray['Transfer_Date'])
														->where('Employee_Id = ?', $transferArray['Employee_Id']));
	
	
														
			if($ckApprovedRecord == 0)
			{
				// Check whether already record exist for employee in applied or returned status
				$qryCkTransfer= $this->_db->select()
											->from($this->_ehrTables->transfer,
												   array(new Zend_Db_Expr('count(Employee_Id)'), 'Employee_Id', 'Added_By', 'Approval_Status' ))
											
											->where("Approval_Status = 'Applied' or Approval_Status = 'Returned'")
											->where('Employee_Id = ?', $transferArray['Employee_Id']);
				
				if (!empty($transferArray['Transfer_Id']))
				{
					$qryCkTransfer->where('Transfer_Id != ?', $transferArray['Transfer_Id']);
				}
				
				$isExist = $this->_db->fetchOne($qryCkTransfer);
				
				if( $isExist == 0)
				{
					/**
					 *	If Transfer id exist then process update action
					*/
					if (!empty($transferArray['Transfer_Id']))
					{
						$action = 'Edit';
						$transferId = $transferArray['Transfer_Id'];
						
						$transferArray['Updated_On'] = date('Y-m-d H:i:s');
						$transferArray['Updated_By'] = $sessionId;
						
						$updated = $this->_db->update($this->_ehrTables->transfer, $transferArray, array('Transfer_Id = '. $transferArray['Transfer_Id']));	
					}
					else
					{
						/**
						 *	If Transfer id is empty then process add action
						*/
						$action = 'Add';
						
						$transferArray['Added_On'] = date('Y-m-d H:i:s');
						$transferArray['Added_By'] = $sessionId;
						
						$updated = $this->_db->insert($this->_ehrTables->transfer, $transferArray);
						
						if($updated)
						{
							$transferId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->transfer, array(new Zend_Db_Expr('Max(Transfer_Id)'))));	
						}
					}
					
					if($updated)
					{
						if ( ! empty($commentArray['Emp_Comment']) )
						{
							$commentArray['Form_Id']   = $this->_dbComment->getFormId($formName);
							$commentArray['Parent_Id'] = $transferId;
							$commentArray['Added_On']  = date('Y-m-d H:i:s');
							
							$this->_db->insert($this->_ehrTables->comment, $commentArray);
						}
					}
					
					/**
					 *	this function will handle
					 *		update system log function
					 *		clear submit lock fucntion
					 *		return success/failure array
					*/
					return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
																	'action'         => $action,
																	'trackingColumn' => $transferArray['Transfer_Id'],
																	'formName'       => $customFormName,
																	'sessionId'      => $sessionId,
																	'tableName'      => $this->_ehrTables->transfer));
				}
				else
				{
					return array('success' => false, 'msg'=>'You can apply only if existing transfer is approved', 'type'=>'info');
				}
			}
			else
			{
				return array('success' => false, 'msg'=>'Record already exist', 'type'=>'info');
			}
		}
	}
	
	/**
	 *	deleteTransfer function used to delete project record based on Transfer_Id
	 *		@transferId	-	(int)
	 *		@sessionId	-	(int)
	*/
    public function deleteTransfer($transferId, $sessionId, $formName,$customFormName)
    {
		$deleted = 0;
		
		$transferRow = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->transfer, array('Approval_Status', 'Lock_Flag'))
											->where('Transfer_Id = ?', $transferId));
		
		$condition = ($transferRow['Lock_Flag'] == 0 && $transferRow['Approval_Status'] == 'Rejected');
		
		if ($condition) 
		{
			$deleted=$this->_db->delete($this->_ehrTables->transfer,'Transfer_Id ='.$transferId);
			
			if ($deleted)
			{
				$this->_dbComment->deleteComment($transferId, $formName);
			}
		}
		
		/**
		*	delete activity for common function
		*		1)check lock is exist or not.
		*			If lock is exist then show error message like employee is open record for update.
		*		2)If No lockflag then process delete activity
		*		3)Update delete activity in system log
		*		4)return success/failure message
	   */
	   return $this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
													   'lockFlag'       => $transferRow['Lock_Flag'],
													   'formName'       => $customFormName,
													   'trackingColumn' => $transferId,
													   'sessionId'      => $sessionId,
													   'deleteCondition' => $condition));
	}
	
	/**
	 * Update transfer status
	 */
    public function statusReport($commentArray, $sessionId, $formName,$customFormName)
    {
         
        if($commentArray['Approval_Status']=='Approved')
        {
            $employeeId  = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->transfer, array('Employee_Id'))
                                                       ->where('Transfer_Id = ?', $commentArray['Parent_Id']));
             
           $empJobDetails = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->transfer, array('Location_Id','Department_Id'))
                                                       ->where('Transfer_Id = ?', $commentArray['Parent_Id']));
          
         
           $updateJobDetails = $this->_db->update($this->_ehrTables->empJob, $empJobDetails, 'Employee_Id = ' . $employeeId);
        }
       

         
        $transferStatus = array('Approval_Status'=>$commentArray['Approval_Status']);
        $updateStatus = $this->_db->update($this->_ehrTables->transfer, $transferStatus, 'Transfer_Id = ' . $commentArray['Parent_Id']);
        $formId = $this->_dbComment->getFormId($formName);
		
		if(!empty($commentArray['Emp_Comment']))
        {
			$commentArray['Form_Id']     = $formId;
			$commentArray['Employee_Id'] = $sessionId;
			$commentArray['Added_On']    = date('Y-m-d H:i:s');
			
            $insertStatus = $this->_db->insert($this->_ehrTables->comment,$commentArray);
        }
		
		if ($updateStatus)
		{	
			$this->_ehrTables->trackEmpSystemAction('Update '.$customFormName.' Status - '.$commentArray['Parent_Id'], $sessionId);
			return true;
		}
		else
		{
            return false;
        }
    }
	
	/**
     *  Get forward to manager
     */
    public function srManagerName($empId, $isManager, $getEmpId)
    {
        if($isManager == 1 && empty($getEmpId))
        {
            $managerId = $empId;
        }
        elseif(!empty($getEmpId))
        {
            $dbPersonal = new Employees_Model_DbTable_Personal();
            $getManagerId = $dbPersonal->ckAddedById($getEmpId);
            $empId = !empty($getManagerId) ? $getManagerId : $getEmpId;
            $managerId = $empId;
        }
        else
        {
            $managerId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob, 'Manager_Id')->where('Employee_Id = ?', $empId));
        }
         
        $arrEmpId = array();
     
	    while ( $managerId != NULL || $managerId != 0)
        {
            $rowManagers = $this->_db->fetchOne($this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),array('J.Manager_Id'))
											
											->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'J.Employee_Id=P.Employee_Id')
											
											->where('P.Employee_Id = ?', $managerId)
											->where('P.Is_Manager =1')
											->where('J.Emp_Status Like ?', 'Active')
											->where('P.Form_Status = 1'));
											
            if(in_array($rowManagers,$arrEmpId) || empty($rowManagers) || $empId == $rowManagers)
            {
                break;
            }
            else
            {
                array_push($arrEmpId,$rowManagers);
                $managerId = $rowManagers;
            }
        }

        if ( empty($arrEmpId) && empty($getEmpId))
        {
            array_push($arrEmpId, $empId);
             
        }
        if(!empty($getEmpId))
        {
            array_push($arrEmpId, $getManagerId);
        }
         
        return $this->_db->fetchPairs($this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),
												   array('Manager_Id' => 'J.Employee_Id'))
											
											->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id=J.Employee_Id',
														array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name)")))
											
											->where('J.Employee_Id In (?)', $arrEmpId)
											->where('P.Form_Status = 1')
											->where('J.Emp_Status Like ?', 'Active')
											->where('P.Is_Manager = 1')
											->order("P.Emp_First_Name ASC"));
    }
	
	/**
	 * Check whether transfer already exists or not for a given data
	 */
    public function ckTransferStatus($transferId, $logEmpId, $reportStatus)
    {
        return $this->_db->fetchOne($this->_db->select()
									->from($this->_ehrTables->transfer, new Zend_Db_Expr('count(Employee_Id)'))
									
									->where('Approval_Status = ?', $reportStatus)
									->where('Transfer_Id = ?', $transferId)
									->where('Approver_Id = ?', $logEmpId));
    }
	
	/**
	 * Get transfer details by transferId to view
	 */
    public function viewTransfer($transferId)
    {
        return $this->_db->fetchRow($this->_db->select()->from(array('Tra'=>$this->_ehrTables->transfer),
													array('Tra.Transfer_Id', 'Tra.Employee_Id','Status'=>'Tra.Approval_Status',
														  'Tra.Department_Id','Tra.Location_Id', 'Tra.Approver_Id','Tra.Added_By'))
										->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.Employee_Id = Tra.Employee_Id', array())
										->where('Tra.Transfer_Id = ?', $transferId));
    }

	/**
	 * Get transfer request count send for approval to a logeed in employee
	 */
    public function editOption($logEmpId)
    {
        return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->transfer, new Zend_Db_Expr('count(Transfer_Id)'))
											->where('Approver_Id = ?', $logEmpId));
    }

	/**
	 * Get transfer employee details by transferId
	 */
    public function transferEmployee($transferId)
    {
        $qryEmpId = $this->_db->select()->from($this->_ehrTables->transfer, array('Employee_Id'))
															->where('Transfer_Id = ?', $transferId);
        return $this->_db->fetchRow($this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal),
												 array('Employee_Id','Employee_Name'=>new Zend_Db_Expr("CONCAT(Emp_First_Name, ' ', Emp_Last_Name)")))
									
									->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.Employee_Id=P.Employee_Id', array())
									->where('P.Employee_Id = ?', $qryEmpId)
									->where('P.Form_Status = 1')
									->where('J.Emp_Status Like ?', 'Active'));
    }

	public function __destruct()
    {
        
    }	
}

