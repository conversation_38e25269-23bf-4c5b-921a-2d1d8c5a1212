<?php
	$this->headTitle($this->salaryFormName);
	
	$salaryAccess = $this->salaryAccess;
	$salaryRecalcCount = $this->salaryRecalcCount;
	$salaryDefinition = $this->salaryDefinition;
    $eligiblePf = $this->customFieldPf;
    $eligibleIns = $this->customFieldIns;
    $cusPfAmt = $this->customFieldPfAmt;
    $eligibleETF = $this->customFieldEtf;
    $cusEtfAmt = $this->customFieldEtfAmt;
	$firstSalaryDate = $this->salaryDate;
    $customFieldExemptEdli = $this->customFieldExemptEdli;

    $customFieldEligibleForGratuity = $this->customFieldEligibleForGratuity;
	
    $pfEligible = $insEligible = $pfAmt = $etfEligible = $etfAmt = $exemptEdliFieldDetails = $gratuityEligibleFieldDetails = array();
    $pfEligible['Enable'] = $insEligible['Enable'] = $pfAmt['Enable'] = $etfEligible['Enable'] = $etfAmt['Enable'] = $exemptEdliFieldDetails['Enable'] = $gratuityEligibleFieldDetails['Enable']= 1;
    $pfEligible['Required'] = $insEligible['Required'] = $pfAmt['Required'] = $etfEligible['Required'] = $etfAmt['Required'] = $exemptEdliFieldDetails['Required'] = $gratuityEligibleFieldDetails['Required'] = 0;
    $pfEligible['Field_Name'] = 'Eligible for Pf';
    $insEligible['Field_Name'] = 'Eligible for Insurance';
    $pfAmt['Field_Name'] = 'PF Amount';
    $etfAmt['Field_Name'] = 'ETF Amount';
    $etfEligible['Field_Name'] = 'Eligible For NPS';
	$exemptEdliFieldDetails['Field_Name'] = 'Exempt EDLI';
    $gratuityEligibleFieldDetails['Field_Name'] = 'Eligible For Gratuity';
	
    if(!empty($eligiblePf))
    {
        $pfEligible['Enable']     = $eligiblePf['Enable'];
        $pfEligible['Required']   = $eligiblePf['Required'];
        $pfEligible['Field_Name'] = ($eligiblePf['New_Field_Name'] != '' ?  $eligiblePf['New_Field_Name'] : $eligiblePf['Field_Name']);
    }
    
    if(!empty($eligibleIns))
    {
        $insEligible['Enable']     = $eligibleIns['Enable'];
        $insEligible['Required']   = $eligibleIns['Required'];
        $insEligible['Field_Name'] = ($eligibleIns['New_Field_Name'] != '' ?  $eligibleIns['New_Field_Name'] : $eligibleIns['Field_Name']);
    }
    
    if(!empty($cusPfAmt))
    {
        $pfAmt['Enable']     = $cusPfAmt['Enable'];
        $pfAmt['Required']   = $cusPfAmt['Required'];
        $pfAmt['Field_Name'] = ($cusPfAmt['New_Field_Name'] != '' ?  $cusPfAmt['New_Field_Name'] : $cusPfAmt['Field_Name']);
    }
    
    if(!empty($cusEtfAmt))
    {
        $etfAmt['Enable']     = $cusEtfAmt['Enable'];
        $etfAmt['Required']   = $cusEtfAmt['Required'];
        $etfAmt['Field_Name'] = ($cusEtfAmt['New_Field_Name'] != '' ?  $cusEtfAmt['New_Field_Name'] : $cusEtfAmt['Field_Name']);
    }
    
    if(!empty($eligibleETF))
    {
        $etfEligible['Enable']     = $eligibleETF['Enable'];
        $etfEligible['Required']   = $eligibleETF['Required'];
        $etfEligible['Field_Name'] = ($eligibleETF['New_Field_Name'] != '' ?  $eligibleETF['New_Field_Name'] : $eligibleETF['Field_Name']);
    }

	if(!empty($customFieldExemptEdli))
    {
        $exemptEdliFieldDetails['Enable']     = $customFieldExemptEdli['Enable'];
        $exemptEdliFieldDetails['Required']   = $customFieldExemptEdli['Required'];
        $exemptEdliFieldDetails['Field_Name'] = ($customFieldExemptEdli['New_Field_Name'] != '' ?  $customFieldExemptEdli['New_Field_Name'] : $customFieldExemptEdli['Field_Name']);
    }

    if(!empty($customFieldEligibleForGratuity))
    {
        $gratuityEligibleFieldDetails['Enable']     = $customFieldEligibleForGratuity['Enable'];
        $gratuityEligibleFieldDetails['Required']   = $customFieldEligibleForGratuity['Required'];
        $gratuityEligibleFieldDetails['Field_Name'] = ($customFieldEligibleForGratuity['New_Field_Name'] != '' ?  $customFieldEligibleForGratuity['New_Field_Name'] : $customFieldEligibleForGratuity['Field_Name']);
    }
	$dateformat = $this->dateformat;
	if(!empty($dateformat))
	{
		$dformat = $dateformat['bs'];		
	}
	else
	{
		$dformat = 'dd/mm/yyyy';
	}

	$restrictFinancialAccessForManager = 0;

	if(!empty($this->orgDetails)){
		$restrictFinancialAccessForManager = $this->orgDetails['Restrict_Financial_Access_For_Manager'];
	}

	$orgDetails		 		= $this->orgDetails;
	$serviceProvider 		= $this->serviceProvider;
	$providentFundDetails   = $this->providentFundDetails;
	$basicComponentsDetails = $this->basicComponentsDetails;
	$basicComponentsDetails = array_reverse($basicComponentsDetails);
	if(!empty($providentFundDetails))
	{
		$employeeContributionRate = $providentFundDetails['Employee_Contribution_Rate'];
		$employerContributionRate = $providentFundDetails['Employer_Contribution_Rate'];
	}
	else
	{
		$employeeContributionRate = '';
		$employerContributionRate = '';
	}

	$payrollSettings =  $this->payrollSettings;
	$payrollCountry  =  $payrollSettings['Payroll_Country'];
	?>
	<input type="hidden" name="payrollCountry" id="payrollCountry" value="<?php echo $payrollCountry; ?>" />
	<input type="hidden" name="fieldForce" id="fieldForce" value="<?php echo $orgDetails['Field_Force']; ?>" />
	<input type="hidden" name="teamSummaryEmployeeId" id="teamSummaryEmployeeId"/>
	<input type="hidden" name="representBasicAsMultiComponents" id="representBasicAsMultiComponents" value="<?php echo $orgDetails['Represent_Basic_As_Multi_Components']; ?>" />
	<input type="hidden" name="providentFundConfiguration" id="providentFundConfiguration" value="<?php echo $orgDetails['Provident_Fund_Configuration']; ?>" />
	<input type="hidden" name="employeeContributionRate" id="employeeContributionRate" value="<?php echo $employeeContributionRate; ?>" />
	<input type="hidden" name="employerContributionRate" id="employerContributionRate" value="<?php echo $employerContributionRate; ?>" />
	<?php
	if ($salaryAccess['View'] ) {
		
?>

	<!--Monthly Salary Grid Panel-->
	<div class="col-md-12 portlets">
		<div class="panel" id="gridPanelMonthlySalary">
			<div class="panel-header md-panel-controls">
				<h3>
					<i class="icon-list"></i>
					<strong>Monthly Salary</strong>
				</h3>
			</div>
			<div class="panel-content">
				<input type="hidden" name="first_salary_date" id="firstSalaryDate" value="<?php echo $firstSalaryDate['Salary_Date'];?>" />
				
				<!--Monthly Salary Grid Toolbar Icons-->
				<div class="m-b-10">
					<?php if ($salaryAccess['Admin'] == 'admin') { ?>

					<button type="button" class="btn btn-white btn-embossed toolbar-icons" title="Check All" id="monthlySalaryCheckAll">
						<i class="hr-check-all"></i>
						<span class="hidden-xs hidden-sm">Check All</span>
					</button>

					<?php } if ($salaryAccess['Add'] && ((empty($salaryAccess['Admin']) && $salaryAccess['Is_Manager'] == 1 
					&& empty($restrictFinancialAccessForManager)) ||  ($salaryAccess['Admin'] == 'admin' || 
					$salaryAccess['Is_Manager'] == 0))) { ?>
					<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="addMonthlySalary" title="Add">
						<i class="mdi-content-add"></i>
						<span class="hidden-xs hidden-sm"> Add</span>
					</button>
					<?php  } ?>

					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="viewMonthlySalary" title="View">
						<i class="mdi-action-visibility"></i>
						<span class="hidden-xs hidden-sm"> View</span>
					</button>

					<?php if ($salaryAccess['Update'] && ((empty($salaryAccess['Admin']) && $salaryAccess['Is_Manager'] == 1 
					&& empty($restrictFinancialAccessForManager)) ||  ($salaryAccess['Admin'] == 'admin' || 
					$salaryAccess['Is_Manager'] == 0))) { ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="editMonthlySalary" title="Edit">
						<i class="mdi-editor-mode-edit"></i>
						<span class="hidden-xs hidden-sm"> Edit</span>
					</button>
					<?php } if ($salaryAccess['Delete'] && ((empty($salaryAccess['Admin']) && $salaryAccess['Is_Manager'] == 1 
					&& empty($restrictFinancialAccessForManager)) ||  ($salaryAccess['Admin'] == 'admin' || 
					$salaryAccess['Is_Manager'] == 0))) { ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="deleteMonthlySalary" title="Delete">
						<i class="mdi-action-delete"></i>
						<span class="hidden-xs hidden-sm"> Delete</span>
					</button>
					<?php } if ($salaryAccess['Admin'] == 'admin') { ?>
					<div class="btn-group" style="margin-top: 0px;" id="btnSalaryExport">
						<button type="button" class="btn btn-whitep dropdown-toggle" data-toggle="dropdown" aria-expanded="true">
							<i class="mdi-file-cloud-download"></i>
							<span class="hidden-xs hidden-sm">Export</span>
							<span class="caret"></span>
							<div class="ripple-wrapper"></div>
						</button>
						<span class="dropdown-arrow dropdown-arrow-inverse"></span>
						<ul class="dropdown-menu dropdown-inverse" role="menu">
							<li>
								<a id="exportAllSalaryDetails" style="cursor: pointer;" data-toggle="modal" data-target="#modalExportAllMonthlySalary">Export All</a>
							</li>
							<li>
								<a id="exportSelectedSalaryDetails" style="cursor: pointer;">Export Selected</a>
							</li>
						</ul>
					</div>
					<?php } if ($salaryAccess['Op_Choice'] == 1 && $salaryRecalcCount > 0) { ?>
					<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="recalculationMonthlySalary" title="Monthly Salary Recalculation">
						<i class="hr-calculator"></i>
						<span class="hidden-xs hidden-sm"> Salary Recalculation</span>
					</button>
					<?php } if (strtolower($orgDetails['Org_Code']) == 'capricetest' || strtolower($orgDetails['Org_Code']) == 'noncalendartest1') { ?>
					<!-- <button type="button" class="btn btn-white btn-embossed toolbar-icons" id="updateEmployeeCTC" title="Update Employee CTC">
						<i class="hr-calculator"></i>
						<span class="hidden-xs hidden-sm"> Update CTC</span>
					</button> -->
					<!-- we need to open this button in production after completion salary template.-->
					<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="migrateEmployeeSalary" title="Migrate Employee Salary">
						<i class="hr-calculator"></i>
						<span class="hidden-xs hidden-sm">Migrate Salary</span>
					</button> 
					<?php }?>


					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="History" id="historyMonthlySalary">
						<i class="mdi-action-history"></i>
						<span class="hidden-xs hidden-sm"> History</span>
					</button>

					<a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterMonthlySalary">
						<i class="glyphicon glyphicon-filter"></i>
						<span class="hidden-xs hidden-sm"> Filter</span>
					</a>
				</div>

				<!-- Monthly Salary Grid Table -->
				<table class="table dataTable table-striped table-dynamic table-hover" id="tableMonthlySalary">
					<thead>
						<tr>
							<th></th>
							<th id="monthlySalaryEmployeeId">Employee Id</th>
							<th id="monthlySalaryEmployeeName">Employee Name</th>
							<th id="monthlySalaryGrossAnnualSalary">Gross Annual Salary</th>
							<th id="monthlySalaryGrossMonthlySalary">Gross Monthly Salary</th>
							<th id="monthlySalaryBasicSalary">Monthly Basic Salary</th>
							<th id="monthlySalaryEffectiveDate">Effective Date</th>
						</tr>
					</thead>
					<tbody>

					</tbody>
				</table>
			</div>
		</div>
	</div>

	<!-- Your custom menu with dropdown-menu as default styling -->
	<div id="context-menu-monthly" class="context-menu">
		<ul class="dropdown-menu" role="menu">
			<li>
				<a tabindex="-1" id="viewContextMonthlySalary">
					<i class="mdi-action-visibility"></i> View</a>
			</li>
			<?php if ($salaryAccess['Update'] && ((empty($salaryAccess['Admin']) && $salaryAccess['Is_Manager'] == 1 
					&& empty($restrictFinancialAccessForManager)) ||  ($salaryAccess['Admin'] == 'admin' || 
					$salaryAccess['Is_Manager'] == 0))) { ?>
			<li>
				<a tabindex="-1" id="editContextMonthlySalary">
					<i class="mdi-editor-mode-edit"></i> Edit</a>
			</li>
			<?php } if($salaryAccess ['Delete'] && ((empty($salaryAccess['Admin']) && $salaryAccess['Is_Manager'] == 1 
					&& empty($restrictFinancialAccessForManager)) ||  ($salaryAccess['Admin'] == 'admin' || 
					$salaryAccess['Is_Manager'] == 0))) { ?>
			<li>
				<a tabindex="-1" id="deleteContextMonthlySalary">
					<i class="mdi-action-delete"></i> Delete</a>
			</li>
			<?php } ?>
			<li>
				<a tabindex="-1" id="historyContextMonthlySalary">
					<i class="mdi-action-history"></i> History</a>
			</li>
		</ul>
	</div>

	<?php if ($salaryAccess['Admin'] == 'admin') { ?>
	<div class="modal fade" id="modalExportAllMonthlySalary" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
						<i class="icons-office-52" id="closeExportAllConfSalary"></i>
					</button>
					<h4 class="modal-title">
						<strong>Export</strong> Confirmation</h4>
				</div>

				<div class="modal-body">Are you sure want to export all salary record ?
					<br>
				</div>

				<div class="modal-footer">
					<button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noExportAllConfSalary">No</button>
					<button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="exportAllConfirmMonthlySalary">Yes</button>
				</div>
			</div>
		</div>
	</div>

	<div class="modal fade" id="modalExportSelectedMonthlySalary" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
						<i class="icons-office-52" id="closeExportSelectedConfSalary"></i>
					</button>
					<h4 class="modal-title">
						<strong>Export</strong> Confirmation</h4>
				</div>

				<div class="modal-body">Are you sure want to export selected salary record ?
					<br>
				</div>

				<div class="modal-footer">
					<button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noExportSelectedConfSalary">No</button>
					<button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="exportSelectedConfirmMonthlySalary">Yes</button>
				</div>
			</div>
		</div>
	</div>

	<?php } ?>

	<!--Add, Edit, View FOrm modal for salary-->
	<div class="modal fade" id="modalFormMonthlySalary" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header bg-primary">
					<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
						<i class="mdi-hardware-keyboard-backspace" id="backMonthlySalary"></i>
					</button>

					<?php if ($salaryAccess['Update'] && ((empty($salaryAccess['Admin']) && $salaryAccess['Is_Manager'] == 1 
					&& empty($restrictFinancialAccessForManager)) ||  ($salaryAccess['Admin'] == 'admin' || 
					$salaryAccess['Is_Manager'] == 0))) { ?>
					<button type="button" class="close form-icons" aria-hidden="true" id="editInViewMonthlySalary">
						<i class="mdi-editor-mode-edit"></i>
					</button>
					<?php } ?>

					<h4 class="modal-title"></h4>
				</div>
				<div class="modal-body">
					<input type="hidden" name="PF_Amount" id="pfAmount" value="<?php echo $pfAmt['Enable'].'$'.$pfAmt['Field_Name'];?>" />
					<input type="hidden" name="ETF_Amount" id="etfAmount" value="<?php echo $etfAmt['Enable'].'$'.$etfAmt['Field_Name'];?>" />
					<!--View Monthly Salary Form-->
					<form role="form" id="viewFormMonthlySalary">
						<div class="row">
							<div class="form-group">
								<div class="col-md-5">
									<label class="control-label">Employee Name</label>
								</div>
								<div class="col-md-7">
									<p id="viewMSEmployeeName"></p>
								</div>
							</div>
							<?php if($orgDetails['Represent_Basic_As_Multi_Components']=='Yes')
							{?>
							<div class="form-group representBasicAsMultiComponents">
								<div class="col-md-5">
									<label class="control-label">Represent Basic As Multi Components</label>
								</div>
								<div class="col-md-7">
									<p id="viewRepresentBasicAsMultiComponents"></p>
								</div>
							</div>
							<?php
							$component=1;
							foreach($basicComponentsDetails as $basicComponent)
							{ 
							?>
							<div class="form-group basicComponentsDetails">
								<div class="col-md-5">
									<label class="control-label"><?php echo $basicComponent['Custom_Label_Name']; ?></label>
								</div>
								<div class="col-md-7">
									<p id="viewBasicComponent<?php echo $component;?>"></p>
								</div>
							</div>
							<?php 
							$component++;
							} ?>
							<?php } ?>
							<div class="form-group">
								<div class="col-md-5">
									<label class="control-label">Effective Date</label>
								</div>
								<div class="col-md-7">
									<p id="viewMSEffectiveDate"></p>
								</div>
							</div>
							<div class="form-group">
								<div class="col-md-5">
									<label class="control-label">Gross Annual Salary</label>
								</div>
								<div class="col-md-7">
									<p id="viewMSGrossAnnualSalary"></p>
								</div>
							</div>
							<div class="form-group">
								<div class="col-md-5">
									<label class="control-label">Gross Monthly Salary</label>
								</div>
								<div class="col-md-7">
									<p id="viewMSGrossMonthlySalary"></p>
								</div>
							</div>
							<div class="form-group">
								<div class="col-md-5">
									<label class="control-label">Monthly Basic Salary</label>
								</div>
								<div class="col-md-7">
									<p id="viewMSMonthlyBasicSalary"></p>
								</div>
							</div>

							<div class="form-group">
								<div class="col-md-5"><label class="control-label">Overtime Level</label></div>
								<div class="col-md-7"><p id="viewEligibleForOvertime"></p></div>
							</div>

							<div class="form-group" id="overtimeAllocationPanel">
								<div class="col-md-5"><label class="control-label">Overtime Allocation</label></div>
								<div class="col-md-7"><p id="viewOvertimeAllocation"></p></div>
							</div>

							<div class="form-group" id="overtimeWageIndexPanel">
								<div class="col-md-5"><label class="control-label">Overtime Wage Index(Per Hour)</label></div>
								<div class="col-md-7"><p id="viewOvertimeWageIndex"></p></div>
							</div>

							<div class="form-group" id="overtimeFixedAmountPanel">
								<div class="col-md-5"><label class="control-label">Overtime Fixed Amount(Per Hour)</label></div>
								<div class="col-md-7"><p id="viewOvertimeFixedAmount"></p></div>
							</div>

							<div class="form-group FlexiBenefitExists">
								<div class="col-md-5">
									<label class="control-label">Flexi Benefit Plan Type</label>
								</div>
								<div class="col-md-7">
									<p id="viewFlexiBenefitPlanType"></p>
								</div>
							</div>
							<div class="form-group vFlexiBenefitAmount">
								<div class="col-md-5">
									<label class="control-label">Flexi Benefit Amount</label>
								</div>
								<div class="col-md-7">
									<p id="viewFlexiBenefitPlanAmount"></p>
								</div>
							</div>
							<div class="form-group vFlexiBenefitPercentage">
								<div class="col-md-5">
									<label class="control-label">Flexi Benefit Percentage</label>
								</div>
								<div class="col-md-7">
									<p id="viewFlexiBenefitPlanPercentage"></p>
								</div>
							</div>
							
							
							<?php
                        if($pfEligible['Enable'] ==1)
                        {
                        ?>
							<div class="form-group">
								<div class="col-md-5">
									<label class="control-label">
										<?php echo $pfEligible['Field_Name'];?>
									</label>
								</div>
								<div class="col-md-7">
									<p id="viewMSEligibleForPF"></p>
								</div>
							</div>
							<?php
                          }
                        ?>
							<?php if ($exemptEdliFieldDetails['Enable'] ==1) { ?>
							<div class="form-group viewExemptEdliPanel">
								<div class="col-md-5">
									<label class="control-label"><?php echo $exemptEdliFieldDetails['Field_Name'];?></label>
								</div>
								<div class="col-md-7">
									<p id="viewExemptEdli"></p>
								</div>
							</div>
							<?php } if ($orgDetails['Provident_Fund_Configuration'] == 'Current') { ?>
							<div class="form-group viewExemptEdliPanel">
								<div class="col-md-5">
									<label class="control-label">Employee Contribution Rate</label>
								</div>
								<div class="col-md-7">
									<p id="viewEmployeeContributionRate"></p>
								</div>
							</div>

							<div class="form-group viewExemptEdliPanel">
								<div class="col-md-5">
									<label class="control-label">Employer Contribution Rate</label>
								</div>
								<div class="col-md-7">
									<p id="viewEmployerContributionRate"></p>
								</div>
							</div>
							<?php } ?>
									
									<?php
                        if($insEligible['Enable'] ==1)
                        {
                        ?>
										<div class="form-group">
											<div class="col-md-5">
												<label class="control-label">
													<?php echo $insEligible['Field_Name'];?>
												</label>
											</div>
											<div class="col-md-7">
												<p id="viewMSEligibleForInsurance"></p>
											</div>
										</div>

										
										<?php
                        }?>
						<div class="form-group viewEsiContributionEndDatePanel">
								<div class="col-md-5">
									<label class="control-label">ESI Contribution End Date</label>
								</div>
								<div class="col-md-7">
									<p id="viewEsiContributionEndDate"></p>
								</div>
						</div>

						
                        <?php if($etfEligible['Enable'] ==1)
                        {
                        ?>
											<div class="form-group">
												<div class="col-md-5">
													<label class="control-label">
														<?php echo $etfEligible['Field_Name'];?>
													</label>
												</div>
												<div class="col-md-7">
													<p id="viewMSEligibleForETF"></p>
												</div>
											</div>
											<?php
                          }
                        ?>
						<?php if($gratuityEligibleFieldDetails['Enable'] == 1) { ?>
							<div class="form-group">
								<div class="col-md-5">
									<label class="control-label"><?php echo $gratuityEligibleFieldDetails['Field_Name'];?></label>
								</div>
								<div class="col-md-7">
									<p id="viewMSEligibleForGratuity"></p>
								</div>
							</div>
						<?php } ?>
												<div id="viewMSAllowancePanel"></div>
												<div id="viewMSFlexiBenefit"></div>
												<div id="viewMSBonusPanel"></div>
												<div id="viewMSInsurancePanel"></div>
												<div id="viewMSGratuityPanel"></div>

												<div class="form-group">
													<div class="col-md-5" style="font-size: large;margin-top:10px;">
														<label class="control-label">CTC</label>
													</div>
													<div class="col-md-7">
														<p id="viewMSCTC" style="font-size: large;margin-top:10px;"></p>
													</div>
												</div>
												
						</div>


						<div class="row">
							<hr class="view-hr" />

							<div class="form-group" style="font-size: large;margin-left: 13px;">
								<label class="control-label text-center">Additional Information</label>
							</div>

							<div class="form-group">
								<div class="col-md-5">
									<label class="control-label">Added On</label>
								</div>
								<div class="col-md-7">
									<p id="addedOnMonthlySalary"></p>
								</div>
							</div>

							<div class="form-group">
								<div class="col-md-5">
									<label class="control-label">Added By</label>
								</div>
								<div class="col-md-7">
									<p id="addedByMonthlySalary"></p>
								</div>
							</div>

							<div class="form-group updatedPanel">
								<div class="col-md-5">
									<label class="control-label">Updated On</label>
								</div>
								<div class="col-md-7">
									<p id="updatedOnMonthlySalary"></p>
								</div>
							</div>

							<div class="form-group updatedPanel">
								<div class="col-md-5">
									<label class="control-label">Updated By</label>
								</div>
								<div class="col-md-7">
									<p id="updatedByMonthlySalary"></p>
								</div>
							</div>
						</div>
					</form>

					<?php if ($salaryAccess['Add'] == 1 || $salaryAccess['Update'] == 1) { ?>

					<!--Add/Edit Monthly Salary Form-->
					<form role="form" class="form-horizontal form-validation" id="editFormMonthlySalary" method="POST" action="">
						<input type="hidden" name="MS_Employee_Id" id="formMSEmployeeId" />
						<input type="hidden" name="isBenefitsApplicable" id="isBenefitsApplicable" value='<?php echo 0; ?>' />
						<input type="hidden" name="FlexiBenefitPlanFlag" id="FlexiBenefitPlanFlag" value='<?php echo (empty($this->flexiBenefitPlanFlag)) ? 0 : $this->flexiBenefitPlanFlag; ?>'
						/>
						<input type="hidden" name="employeeAnnualCtc" id="employeeAnnualCtc"/>
				
						<div class="row">
							<div class="form-group">
								<label class="col-md-3 control-label">Employee Name
									<span class="short_explanation">*</span>
								</label>
								<div class="col-md-9">
									<select class="form-control vRequired" data-search="true" id="formMSEmployeeName" name="MS_Employee_Name">

									</select>
								</div>
							</div>

							<div class="form-group">
								<label class="col-md-3 control-label" id="lblMSEffectiveFrom">Effective From
									<span class="short_explanation">*</span>
								</label>
								<div class="col-md-9">
									<input type="text" id="formMSEffectiveFrom" class="form-control vRequired datePickerRead" disabled="true" name="MS_Effective_Date"
									    placeholder="Effective From">
								</div>
							</div>
							
							<div id="incrementAmountPanel">
								<div class="form-group">
									<label class="col-md-3 control-label">Increment Type</label>
									<div class="col-md-9">
										<select class="form-control" id="formIncrementType" name="formIncrementType">
											<option value="" selected="selected">--Select--</option>
											<option value="Basic_Pay">Basic Pay</option>
											<option value="Gross_Monthly_Salary">Gross Monthly Salary</option>
											<option value="Gross_Annual_Salary">Gross Annual Salary</option>
										</select>
									</div>
								</div>
								<div class="form-group">
									<label class="col-md-3 control-label">Increment Amount</label>
									<div class="col-md-9">
										<input type="number" class="form-control" step="0.01" id="formIncrementAmount" name="Increment Amount" placeholder="Increment Amount">
									</div>
								</div>
							</div>
					
							<div class="form-group">
								<label class="col-md-3 control-label" id="formMSGrossAnnualSalaryLabel">Gross Annual Salary</label>
								<div class="col-md-9">
									<input type="number" class="form-control convertDecimalPos1" step="0.01" min="1" max="9999999999999" id="formMSGrossAnnualSalary"
									    name="MS_Gross_Annual_Salary" placeholder="Gross Annual Salary">
								</div>
							</div>

							<?php if($orgDetails['Represent_Basic_As_Multi_Components']=='Yes')
							{?>
								<div class="form-group representBasicAsMultiComponents">
									<label class="col-md-3 control-label">Represent Basic As Multi Components</label>
									<div class="col-md-9">
										<div class="form-group togglebutton" style="margin-top: 15px; margin-left: 15px; height: 20px">
											<label>
												<input type="checkbox" class="col-sm-9 md-checkbox" name="Represent_Basic_As_Multi_Components" id="formRepresentBasicAsMultiComponents">
											</label>
										</div>
									</div>
								</div>
							<?php
							$component=1;
							foreach($basicComponentsDetails as $basicComponent)
							{ 
							?>
							<div class="form-group basicComponentsDetails">
								<label class="col-md-3 control-label"><?php echo $basicComponent['Custom_Label_Name']; ?></label>
								<div class="col-md-9">
									<input type="number" class="form-control convertDecimalPos1" step="0.01" min="0" max="9999999999999" id="formBasicComponent<?php echo $component;?>"
									    name="<?php echo $basicComponent['Custom_Label_Name'];?>" placeholder="<?php echo $basicComponent['Custom_Label_Name']; ?>">
								</div>
							</div>
							<?php 
							$component++;
							} ?>
							<?php } ?>

							<div class="form-group">
								<label class="col-md-3 control-label" id="formMSMonthlyBasicSalaryLabel">Monthly Basic Salary </label>
								<div class="col-md-9">
									<input type="number" class="form-control convertDecimalPos1" step="0.01" min="1" max="9999999999999" id="formMSMonthlyBasicSalary"
									    name="MS_Monthly_Basic_Salary" placeholder="Monthly Basic Salary">
								</div>
							</div>

							<div class="form-group">
								<label class="col-md-3 control-label">Overtime Level</label>
								<div class="col-md-9">
									<select class="form-control vRequired" data-search="true" id="formMSOvertimeLevel" name="MS_Overtime_Level">
										<option value="No" selected="selected">No overtime</option>
										<option value="GRA">Grade</option>
										<option value="EMP">Employee</option>
									</select>
								</div>
							</div>

						
							<!--Start Overtime Allocation Field Set-->
							<div class="form-group employeeOvertimeCoverage" id="formOvertimeAllocationPanel">
								<label class="col-md-3 control-label">Overtime Allocation<span class="short_explanation">*</span>
								</label>
								<div class="col-md-9">
									<select class="form-control" data-search="true" id="formOvertimeAllocation" name="Overtime_Allocation">
										<option value="Wage Index" selected="selected">Wage Index</option>
										<option value="Fixed Amount">Fixed Amount</option>
									</select>
								</div>
								
							</div>
							<!--End Overtime Allocation Field Set-->

							<!--Start Wage Factor Field Set-->
							<div class="form-group employeeOvertimeCoverage" id="formWageFactorPanel">
								<label class="col-md-3 control-label">Wage Index<span class="short_explanation">*</span>
									<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
                                    data-placement="top" data-content="Wage Index allows the user to enter the  times of salary to be provided for overtime.  overtime wages may be double or triple their per hour wages. Eg, 2 for twice the hour wages"   >
									</i>
								</label>	
								<div class="col-md-6">
									<input type="number" class="form-control convertDecimalPos1" step="0.25" id="formWageFactor" name="Wage_Factor" placeholder="Wage Factor">
								</div>
								<div class="col-md-3">
									<label class="control-label" style="margin-top:10px">Per Hour</label>
								</div>
							</div>
							<!--End Wage Factor Field Set-->

							<div class="form-group overtimeAmountPanel employeeOvertimeCoverage">
								<label class="col-md-3 control-label">Overtime Amount
									<span class="short_explanation">*</span>
								</label>
								<div class="col-md-6">
									<input type="number" class="form-control" step="0.25" id="formMSOvertimeAmount" name="MS_Ovetime_Amount" placeholder="Overtime Amount">
								</div>
								<div class="col-md-3">
									<label class="control-label" style="margin-top:10px">Per Hour</label>
								</div>
							</div>
						
							<!--Start Overtime Allocation Field Set-->
							<div class="form-group gradeOvertimeCoverage" id="gradeOvertimeAllocationPanel">
								<div class="col-md-3"><label class="control-label">Overtime Allocation</label></div>
								<div class="col-md-9">
										<p id="gradeOvertimeAllocation"></p>
								</div>
								
							</div>
							<!--End Overtime Allocation Field Set-->

							<!--Start Wage Factor Field Set-->
							<div class="form-group gradeOvertimeCoverage" id="gradeWageFactorPanel">
								<div class="col-md-3"><label class="control-label">Wage Index(Per Hour)
								<i class="icon-info" rel="popover" data-container="body" data-toggle="popover"
								data-placement="top" data-content="Wage Index allows the user to enter the  times of salary to be provided for overtime.  overtime wages may be double or triple their per hour wages. Eg, 2 for twice the hour wages"   >
								</i>
								</label>
								</div>
								<div class="col-md-9">
									<p id="gradeWageFactor"></p>
								</div>
							</div>
							<!--End Wage Factor Field Set-->

							<!--Start Overtime Amount Field Set-->
							<div class="form-group gradeOvertimeCoverage" id="gradeOvertimeAmountPanel">
								<div class="col-md-3"><label class="control-label">Overtime Amount(Per Hour)</label></div>
								<div class="col-md-9">
									<p id="gradeOvertimeAmount"></p>
								</div>
							</div>
							<!--End Overtime Amount Field Set-->


							
						
							<div class="form-group" id="editFBPTypePanel">
								<label class="col-md-3 control-label labelEditFlexi">Flexible Benefit Plan Type
									<span class="short_explanation">*</span>
								</label>
								<div class="col-md-9">
									<select class="form-control" id="formFlexiBenefitPlanType" name="formFlexiBenefitPlanType">
										<option value="" selected="selected">--Select--</option>
										<option value="Amount">Amount</option>
										<option value="Percentage">Percentage</option>
									</select>
								</div>
							</div>

							<div class="form-group" id="editFBPercentagePanel">
								<label class="col-md-3 control-label labelFlexipercentage">Percentage (%)
									<span class="short_explanation">*</span>
								</label>
								<div class="col-md-9">
									<input type="number" class="form-control convertDecimalPos1" id="formFlexiBenefitPlanPercentage" min="1" name="formFBPPercentage"
									    placeholder="Percentage">
								</div>
							</div>

							<div class="form-group" id="editFBAmountPanel">
								<label class="col-md-3 control-label labelFlexiamount">Amount
									<span class="short_explanation">*</span>
								</label>
								<div class="col-md-9">
									<input type="number" class="form-control convertDecimalPos1" id="formFlexiBenefitPlanAmount" step="0.01" min="1" name="formFBPAmount"
									    placeholder="Amount" max="9999999999999">
								</div>
							</div>

							<div id="formMSAllowancePanel"></div>
							<div id="formMSBonusPanel"></div>
							<?php
                        if($pfEligible['Enable'] == 1)
                        {
                        ?>
								<div class="form-group">
									<!--<label class="col-md-3 control-label">Eligible for Pf</label>-->
									<label class="col-md-3 control-label">
										<?php echo $pfEligible['Field_Name'];?>
									</label>
									<div class="col-md-9">
										<div class="form-group togglebutton" style="margin-top: 15px; margin-left: 15px; height: 20px">
											<label>
												<input type="checkbox" class="col-sm-9 md-checkbox" name="MS_Eligible_For_Pf" id="formMSEligibleForPf">
											</label>
										</div>
									</div>
								</div>
								<?php
                        }  if ($exemptEdliFieldDetails['Enable'] ==1) {
                        ?>
								<div class="form-group exemptEdliPanel">
									<label class="col-md-3 control-label"><?php echo $exemptEdliFieldDetails['Field_Name'];?></label>
									<div class="col-md-9">
										<div class="form-group togglebutton" style="margin-top: 15px; margin-left: 15px; height: 20px">
											<label>
												<input type="checkbox" class="col-sm-9 md-checkbox" name="MS_Exempt_Edli" id="formExemptEdli">
											</label>
										</div>
									</div>
								</div>
								
								<?php } if ($orgDetails['Provident_Fund_Configuration'] == 'Current') { ?>
								<div class="form-group exemptEdliPanel">
									<label class="col-md-3 control-label">Employee Contribution Rate
									<span class="short_explanation">*</span>		
									</label>
									<div class="col-md-9">
										<select class="form-control vRequired" id="formEmployeeContributionRate" name="formEmployeeContributionRate">
											<option value="Restrict Contribution to ₹15,000 of PF Wage">Restrict Contribution to ₹15,000 of PF Wage</option>
											<option value="12% of Actual PF Wage">12% of Actual PF Wage</option>
										</select>
									</div>
								</div>

								<div class="form-group exemptEdliPanel">
									<label class="col-md-3 control-label">Employer Contribution Rate
									<span class="short_explanation">*</span>	
									</label>
									<div class="col-md-9">
										<select class="form-control vRequired" id="formEmployerContributionRate" name="formEmployerContributionRate">
											<option value="Restrict Contribution to ₹15,000 of PF Wage">Restrict Contribution to ₹15,000 of PF Wage</option>
											<option value="12% of Actual PF Wage">12% of Actual PF Wage</option>
										</select>
									</div>
								</div>
								<?php } ?>

									<?php
                        if($insEligible['Enable'] == 1)
                        {
                        ?>
										<div class="form-group">
											<label class="col-md-3 control-label">
												<?php echo $insEligible['Field_Name'];?>
											</label>
											<div class="col-md-9">
												<div class="form-group togglebutton" style="margin-top: 15px; margin-left: 15px;height:20px ">
													<label>
														<input type="checkbox" class="col-sm-9 md-checkbox" name="MS_Eligible_For_Insurance" id="formMSEligibleForInsurance">
													</label>
												</div>
											</div>
										</div>
										<?php
                        }
                        ?>
											<?php
                        if($etfEligible['Enable'] == 1)
                        {
                        ?>
												<div class="form-group">
													<!--<label class="col-md-3 control-label">Eligible for Pf</label>-->
													<label class="col-md-3 control-label">
														<?php echo $etfEligible['Field_Name'];?>
													</label>
													<div class="col-md-9">
														<div class="form-group togglebutton" style="margin-top: 15px; margin-left: 15px; height: 20px">
															<label>
																<input type="checkbox" class="col-sm-9 md-checkbox" name="MS_Eligible_For_ETF" id="formMSEligibleForETF">
															</label>
														</div>
													</div>
												</div>
												<?php
                        }
                        ?>

						<?php if($gratuityEligibleFieldDetails['Enable'] == 1) { ?>
							<div class="form-group">
								<label class="col-md-3 control-label"><?php echo $gratuityEligibleFieldDetails['Field_Name'];?></label>
								<div class="col-md-9">
									<div class="form-group togglebutton" style="margin-top: 15px; margin-left: 15px; height: 20px">
										<label>
											<input type="checkbox" class="col-sm-9 md-checkbox" name="MS_Eligible_For_Gratuity" id="formMSEligibleForGratuity">
										</label>
									</div>
								</div>
							</div>
						<?php } ?>
						
													<div id="formMSInsurancePanel"></div>

													<div class="form-group">
														<label class="col-md-3 control-label">Gross Monthly Salary </label>
														<div class="col-md-9">
															<label class="control-label" id="formMSGrossMonthlySalary" style="margin-top: 10px;" name="MS_Gross_Monthly_Salary">-</label>
															<label class="control-label" id="lblformMSGRS"></label>
														</div>
													</div>
													
													<div class="form-group">
														<label class="col-md-3 control-label">CTC</label>
														<div class="col-md-9">
															<label class="control-label" id="formMSCTC">-</label>
														</div>
													</div>

													<div class="form-group">
														<label class="col-md-6 control-label" id="formMSGrossMonthlySalarylabel"></label>
													</div>

						</div>

						<button type="reset" class="cancel" id="formMSReset" style="display: none;"></button>
					</form>

					<?php } ?>

				</div>
				<div class="modal-footer text-center" id="formActionMonthlySalary">

					<?php if ($salaryAccess['Add'] == 1 || $salaryAccess['Update'] == 1) { ?>
					<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetMonthlySalary"
					    style="bottom: 5px;">
						<i class="mdi-action-restore"></i> Reset
					</button>
					<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitMonthlySalary"
					    style="bottom: 5px;">
						<i class="mdi-content-send"></i> Submit
					</button>
					<?php } ?>

				</div>
			</div>
		</div>
	</div>

	<?php if ($salaryAccess['Op_Choice'] ) { ?>
	<!-- Modal for monthly salary recaluation -->
	<div class="modal fade" id="modalReCalculationMonthlySalary" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header bg-primary">
					<button type="button" class="close form-icons pull-left" id="modalFormCloseSalaryRecalc" style="margin-right: 10px;" aria-hidden="true">
						<i class="mdi-hardware-keyboard-backspace" id="backMonthlySalaryReCalculation"></i>
					</button>

					<h4 class="modal-title"></h4>
				</div>
				<div class="modal-body">
					<form role="form" class="form-horizontal form-validation" id="editFormMonthlySalaryRecalc" method="POST" action="">
						<div class="form-group">
							<label class="col-md-3 control-label">Salary Effective Date
								<span class="short_explanation">*</span>
							</label>
							<div class="col-md-4">
								<input type="text" id="formMSRecalcEffectiveDate" class="date-picker form-control vRequired datePickerRead" name="Recalc_Effective_Date"
								    placeholder="Salary Effective Date" data-rule-checkRecalcEffectiveDate="true" data-msg-checkRecalcEffectiveDate="Should be lesser than current date + 15 days">
							</div>
							<div class="col-md-1"></div>
							<div class="col-md-4">
								<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitRecalculation"
								    style="top: 5px;">
									<i class="mdi-content-send"></i> Submit
								</button>
							</div>
						</div>
					</form>

					<table class="table dataTable table-striped table-dynamic table-hover" id="tableMonthlySalarReCalculation">
						<thead>
							<tr>
								<th id="salReCalculationEmployeeName">Employee name</th>
							</tr>
						</thead>
						<tbody>

						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>

	<?php } ?>

	<!-- Modal for view monthly salary history -->
	<div class="modal fade" id="modalHistoryMonthlySalary" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header bg-primary">
					<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
						<i class="mdi-hardware-keyboard-backspace" id="backMonthlySalaryHistory"></i>
					</button>

					<h4 class="modal-title"></h4>
				</div>
				<div class="modal-body">
					<table class="table dataTable table-striped table-dynamic table-hover" id="tableMonthlySalaryHistory">
						<thead>
							<tr>
								<th></th>
								<th id="monthlySalaryHistoryGrossAnnualSalary">Annual Gross Salary</th>
								<th id="monthlySalaryHistoryGrossMonthlySalary">Monthly Gross Salary</th>
								<th id="monthlySalaryHistoryBasicSalary">Monthly Basic Salary</th>
								<th id="monthlySalaryHistoryModifiedBy">Modified By</th>
							</tr>
						</thead>
						<tbody>

						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>

	<!--Filter Form-->
	<div class="builder" id="filterPanelMonthlySalary">
		<div id="closeFilterMonthlySalary">
			<a class="builder-toggle">
				<i class="icons-office-52"></i>
			</a>
		</div>
		<div class="inner">
			<div class="builder-container">
				<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel" style="width: 100%;" id="filterResetMonthlySalary">Reset</button>
				<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="filterApplyMonthlySalary">Apply</button>

				<?php if ($salaryAccess['Is_Manager'] == 0 && empty($salaryAccess['Admin'])) { ?>

				<div class="form-group">
					<label>Employee Name</label>
					<!--<input type="text" class="form-control" id="filterMSEmployeeName" readonly="readonly" value="<php echo $salaryAccess['Employee_Name']['Emp_First_Name'] .' '. $salaryAccess['Employee_Name']['Emp_Last_Name']; ?>">-->
					<input type="text" class="form-control" id="filterMSEmployeeName" readonly="readonly" value="<?php echo $salaryAccess['Employee_Name']; ?>">
				</div>

				<?php } else { ?>

				<div class="form-group">
					<label>Employee Name</label>
					<input type="text" class="form-control" id="filterMSEmployeeName" placeholder="Employee Name">
				</div>

				<?php } ?>

				<div class="form-group">
					<label>Annual Gross Salary</label>
					<div class="input-group">
						<input type="number" class="input-md form-control" name="filterMSAnnualGrossSalaryStart" id="filterMSAnnualGrossSalaryStart"
						    data-orientation="top" placeholder="Start" />
						<span class="input-group-addon">to</span>
						<input type="number" class="input-md form-control" name="filterMSAnnualGrossSalaryEnd" id="filterMSAnnualGrossSalaryEnd"
						    data-orientation="top" placeholder="End" />
					</div>
				</div>

				<div class="form-group">
					<label>Gross Monthly Salary</label>
					<div class="input-group">
						<input type="number" class="input-md form-control" name="filterMSGrossMonthlySalaryStart" id="filterMSGrossMonthlySalaryStart"
						    data-orientation="top" placeholder="Start" />
						<span class="input-group-addon">to</span>
						<input type="number" class="input-md form-control" name="filterMSGrossMonthlySalaryEnd" id="filterMSGrossMonthlySalaryEnd"
						    data-orientation="top" placeholder="End" />
					</div>
				</div>

				<div class="form-group">
					<label>Monthly Basic Salary</label>
					<div class="input-group">
						<input type="number" class="input-md form-control" name="filterMSMonthlyBasicSalaryStart" id="filterMSMonthlyBasicSalaryStart"
						    data-orientation="top" placeholder="Start" />
						<span class="input-group-addon">to</span>
						<input type="number" class="input-md form-control" name="filterMSMonthlyBasicSalaryEnd" id="filterMSMonthlyBasicSalaryEnd"
						    data-orientation="top" placeholder="End" />
					</div>
				</div>

				<div class="form-group">
					<label>Effective Date</label>
					<div class="input-daterange b-datepicker input-group" data-date-format="<?php echo $dformat; ?>" id="datepicker">
						<input type="text" class="input-md form-control" name="filterMSEffectiveDateStart" id="filterMSEffectiveDateStart" data-orientation="top"
						    placeholder="Start" />
						<span class="input-group-addon">to</span>
						<input type="text" class="input-md form-control" name="filterMSEffectiveDateEnd" id="filterMSEffectiveDateEnd" data-orientation="top"
						    placeholder="End" />
					</div>
				</div>

				<div class="form-group">
					<label>Employee Status</label>
					<select class="form-control" data-search="true" id="filterMSStatus">
						<option value="">All</option>
						<option value="Active">Active</option>
						<option value="InActive">InActive</option>
					</select>
				</div>

				<?php if ($orgDetails['Field_Force'] == 1) { ?>
				<div class="form-group">
					<label>Service Provider</label>
					<select class="form-control" data-search="true" id="filterServiceProvider">
						<option value="">All</option>
						<?php
						foreach ($serviceProvider as $key => $row)
						{
							echo '<option value="'. $key .'">'. $row .'</option>';
						}
						?>
					</select>												
				</div>
				<?php } ?>

			</div>
		</div>
	</div>

	<!--HourlyWages Grid Panel-->
	<div class="col-md-12 portlets">
		<div class="panel" id="gridPanelHourlyWages">
			<div class="panel-header md-panel-controls">
				<h3>
					<i class="icon-list"></i>
					<strong>Hourly Wages</strong>
				</h3>
			</div>
			<div class="panel-content">
				<!--HourlyWages Grid Toolbar Icons-->
				<div class="m-b-10">
					<?php if ($salaryAccess['Add'] && ((empty($salaryAccess['Admin']) && $salaryAccess['Is_Manager'] == 1 
					&& empty($restrictFinancialAccessForManager)) ||  ($salaryAccess['Admin'] == 'admin' || 
					$salaryAccess['Is_Manager'] == 0))) { ?>
					<button type="button" class="btn btn-white btn-embossed toolbar-icons" id="addHourlyWages" title="Add">
						<i class="mdi-content-add"></i>
						<span class="hidden-xs hidden-sm"> Add</span>
					</button>
					<?php  } ?>

					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="viewHourlyWages" title="View">
						<i class="mdi-action-visibility"></i>
						<span class="hidden-xs hidden-sm"> View</span>
					</button>

					<?php if ($salaryAccess['Update'] && ((empty($salaryAccess['Admin']) && $salaryAccess['Is_Manager'] == 1 
					&& empty($restrictFinancialAccessForManager)) ||  ($salaryAccess['Admin'] == 'admin' || 
					$salaryAccess['Is_Manager'] == 0))) { ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="editHourlyWages" title="Edit">
						<i class="mdi-editor-mode-edit"></i>
						<span class="hidden-xs hidden-sm"> Edit</span>
					</button>
					<?php } if ($salaryAccess['Delete'] && ((empty($salaryAccess['Admin']) && $salaryAccess['Is_Manager'] == 1 
					&& empty($restrictFinancialAccessForManager)) ||  ($salaryAccess['Admin'] == 'admin' || 
					$salaryAccess['Is_Manager'] == 0))) { ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="deleteHourlyWages" title="Delete">
						<i class="mdi-action-delete"></i>
						<span class="hidden-xs hidden-sm"> Delete</span>
					</button>
					<?php } ?>

					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="History" id="historyHourlyWages">
						<i class="mdi-action-history"></i>
						<span class="hidden-xs hidden-sm"> History</span>
					</button>

					<a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterHourlyWages">
						<i class="glyphicon glyphicon-filter"></i>
						<span class="hidden-xs hidden-sm"> Filter</span>
					</a>
				</div>

				<!-- Hourly Wages Grid Table -->
				<table class="table dataTable table-striped table-dynamic table-hover" id="tableHourlyWages">
					<thead>
						<tr>
							<th></th>
							<th id="hourlyWagesEmployeeId">Employee Id</th>
							<th id="hourlyWagesEmployeeName">Employee Name</th>
							<th id="hourlyWagesRegularWages">Regular Wages</th>
							<th id="hourlyWagesOvertimeWages">Overtime Wages</th>
							<th id="hourlyWagesEffectiveDate">Effective Date</th>
						</tr>
					</thead>
					<tbody>

					</tbody>
				</table>
			</div>
		</div>
	</div>

	<!-- Your custom menu with dropdown-menu as default styling -->
	<div id="context-menu-hourly" class="context-menu">
		<ul class="dropdown-menu" role="menu">
			<li>
				<a tabindex="-1" id="viewContextHourlyWages">
					<i class="mdi-action-visibility"></i> View</a>
			</li>
			<?php if ($salaryAccess['Update'] && ((empty($salaryAccess['Admin']) && $salaryAccess['Is_Manager'] == 1 
					&& empty($restrictFinancialAccessForManager)) ||  ($salaryAccess['Admin'] == 'admin' || 
					$salaryAccess['Is_Manager'] == 0))) { ?>
			<li>
				<a tabindex="-1" id="editContextHourlyWages">
					<i class="mdi-editor-mode-edit"></i> Edit</a>
			</li>
			<?php } if($salaryAccess ['Delete'] && ((empty($salaryAccess['Admin']) && $salaryAccess['Is_Manager'] == 1 
					&& empty($restrictFinancialAccessForManager)) ||  ($salaryAccess['Admin'] == 'admin' || 
					$salaryAccess['Is_Manager'] == 0))) { ?>
			<li>
				<a tabindex="-1" id="deleteContextHourlyWages">
					<i class="mdi-action-delete"></i> Delete</a>
			</li>
			<?php } ?>
			<li>
				<a tabindex="-1" id="historyContextHourlyWages">
					<i class="mdi-action-history"></i> History</a>
			</li>
		</ul>
	</div>

	<!--Add, Edit, View Form modal for hourly wages-->
	<div class="modal fade" id="modalFormHourlyWages" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header bg-primary">
					<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
						<i class="mdi-hardware-keyboard-backspace" id="backHourlyWages"></i>
					</button>

					<?php if ($salaryAccess['Update'] &&((empty($salaryAccess['Admin']) && $salaryAccess['Is_Manager'] == 1 
					&& empty($restrictFinancialAccessForManager)) ||  ($salaryAccess['Admin'] == 'admin' || 
					$salaryAccess['Is_Manager'] == 0))) { ?>
					<button type="button" class="close form-icons" aria-hidden="true" id="editInViewHourlyWages">
						<i class="mdi-editor-mode-edit"></i>
					</button>
					<?php } ?>

					<h4 class="modal-title"></h4>
				</div>
				<div class="modal-body">
					<!--View Hourly Wages Form-->
					<form role="form" id="viewFormHourlyWages">
						<div class="row">
							<div class="form-group">
								<div class="col-md-5">
									<label class="control-label">Employee Name</label>
								</div>
								<div class="col-md-7">
									<p id="viewHWEmployeeName"></p>
								</div>
							</div>
							<div class="form-group">
								<div class="col-md-5">
									<label class="control-label">Effective Date</label>
								</div>
								<div class="col-md-7">
									<p id="viewHWEffectiveDate"></p>
								</div>
							</div>
							<div class="form-group">
								<div class="col-md-5">
									<label class="control-label">Regular Hourly Wages</label>
								</div>
								<div class="col-md-7">
									<p id="viewHWRegularHourlyWages"></p>
								</div>
							</div>
							<div class="form-group">
								<div class="col-md-5">
									<label class="control-label">Overtime Hourly Wages</label>
								</div>
								<div class="col-md-7">
									<p id="viewHWOvertimeHourlyWages"></p>
								</div>
							</div>
							<div class="form-group">
								<div class="col-md-5">
									<label class="control-label">Eligible For PF</label>
								</div>
								<div class="col-md-7">
									<p id="viewHWEligibleForPF"></p>
								</div>
							</div>
							<div class="form-group">
								<div class="col-md-5">
									<label class="control-label">Eligible For Insurance</label>
								</div>
								<div class="col-md-7">
									<p id="viewHWEligibleForInsurance"></p>
								</div>
							</div>
							<div class="form-group">
								<div class="col-md-5">
									<label class="control-label">Eligible For Gratuity</label>
								</div>
								<div class="col-md-7">
									<p id="viewHWEligibleForGratuity"></p>
								</div>
							</div>
							<div id="viewHWAllowancePanel"></div>
							<div id="viewHWInsurancePanel"></div>
						</div>

						<div class="row">
							<hr class="view-hr" />

							<div class="form-group" style="font-size: large;margin-left: 13px;">
								<label class="control-label text-center">Additional Information</label>
							</div>

							<div class="form-group">
								<div class="col-md-5">
									<label class="control-label">Added On</label>
								</div>
								<div class="col-md-7">
									<p id="addedOnHourlyWages"></p>
								</div>
							</div>

							<div class="form-group">
								<div class="col-md-5">
									<label class="control-label">Added By</label>
								</div>
								<div class="col-md-7">
									<p id="addedByHourlyWages"></p>
								</div>
							</div>

							<div class="form-group updatedPanel">
								<div class="col-md-5">
									<label class="control-label">Updated On</label>
								</div>
								<div class="col-md-7">
									<p id="updatedOnHourlyWages"></p>
								</div>
							</div>

							<div class="form-group updatedPanel">
								<div class="col-md-5">
									<label class="control-label">Updated By</label>
								</div>
								<div class="col-md-7">
									<p id="updatedByHourlyWages"></p>
								</div>
							</div>
						</div>
					</form>

					<?php if ($salaryAccess['Add'] == 1 || $salaryAccess['Update'] == 1) { ?>

					<!--Add/Edit Hourly Wages Form-->
					<form role="form" class="form-horizontal form-validation" id="editFormHourlyWages" method="POST" action="">
						<input type="hidden" name="HW_Employee_Id" id="formHWEmployeeId" />

						<div class="row">
							<div class="form-group">
								<label class="col-md-3 control-label">Employee Name
									<span class="short_explanation">*</span>
								</label>
								<div class="col-md-9">
									<select class="form-control vRequired" data-search="true" id="formHWEmployeeName" name="HW_Employee_Name">

									</select>
								</div>
							</div>

							<div class="form-group">
								<label class="col-md-3 control-label" id="lblHWEffectiveFrom">Effective From
									<span class="short_explanation">*</span>
								</label>
								<div class="col-md-9">
									<input type="text" id="formHWEffectiveFrom" class="date-picker form-control vRequired datePickerRead" disabled="true" name="HW_Effective_Date"
									    placeholder="Effective From">
								</div>
							</div>

							<div class="form-group">
								<label class="col-md-3 control-label">Regular Hourly Wage
									<span class="short_explanation">*</span>
								</label>
								<div class="col-md-9">
									<input type="number" class="form-control vRequired" step="0.01" min="1" id="formHWRegularHourlyWage" name="HW_Regular_Hourly_Wage"
									    placeholder="Regular Hourly Wage">
								</div>
							</div>

							<div class="form-group">
								<label class="col-md-3 control-label">Overtime Hourly Wage
									<span class="short_explanation">*</span>
								</label>
								<div class="col-md-9">
									<input type="number" class="form-control vRequired" step="0.01" min="0" id="formHWOvertimeHourlyWage" name="HW_Overtime_Hourly_Wage"
									    placeholder="Overtime Hourly Wage">
								</div>
							</div>

							<div id="formHWAllowancePanel"></div>

							<div class="form-group">
								<label class="col-md-3 control-label">Eligible for Pf</label>
								<div class="col-md-9">
									<div class="form-group togglebutton" style="margin-top: 15px; margin-left: 15px; height: 20px">
										<label>
											<input type="checkbox" class="col-sm-9 md-checkbox" name="HW_Eligible_For_Pf" id="formHWEligibleForPf">
										</label>
									</div>
								</div>
							</div>

							<div class="form-group">
								<label class="col-md-3 control-label">Eligible for Insurance</label>
								<div class="col-md-9">
									<div class="form-group togglebutton" style="margin-top: 15px; margin-left: 15px; height: 20px">
										<label>
											<input type="checkbox" class="col-sm-9 md-checkbox" name="HW_Eligible_For_Insurance" id="formHWEligibleForInsurance">
										</label>
									</div>
								</div>
							</div>

							<div class="form-group">
								<label class="col-md-3 control-label">Eligible for Gratuity</label>
								<div class="col-md-9">
									<div class="form-group togglebutton" style="margin-top: 15px; margin-left: 15px; height: 20px">
										<label>
											<input type="checkbox" class="col-sm-9 md-checkbox" name="HW_Eligible_For_Gratuity" id="formHWEligibleForGratuity">
										</label>
									</div>
								</div>
							</div>

							<div id="formHWInsurancePanel"></div>
						</div>

						<button type="reset" class="cancel" id="formHWReset" style="display: none;"></button>
					</form>

					<?php } ?>

				</div>
				<div class="modal-footer text-center" id="formActionHourlyWages">

					<?php if ($salaryAccess['Add'] == 1 || $salaryAccess['Update'] == 1) { ?>
					<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetHourlyWages"
					    style="bottom: 5px;">
						<i class="mdi-action-restore"></i> Reset
					</button>
					<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitHourlyWages"
					    style="bottom: 5px;">
						<i class="mdi-content-send"></i> Submit
					</button>
					<?php } ?>

				</div>
			</div>
		</div>
	</div>

	<!--Filter Form-->
	<div class="builder" id="filterPanelHourlyWages">
		<div id="closeFilterHourlyWages">
			<a class="builder-toggle">
				<i class="icons-office-52"></i>
			</a>
		</div>
		<div class="inner">
			<div class="builder-container">
				<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel" style="width: 100%;" id="filterResetHourlyWages">Reset</button>
				<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="filterApplyHourlyWages">Apply</button>

				<?php if ($salaryAccess['Is_Manager'] == 0 && empty($salaryAccess['Admin'])) { ?>

				<div class="form-group">
					<label>Employee Name</label>
					<!--<input type="text" class="form-control" id="filterHourlyWagesEmployeeName" readonly="readonly" value="<php echo $salaryAccess['Employee_Name']['Emp_First_Name'] .' '. $salaryAccess['Employee_Name']['Emp_Last_Name']; ?>">-->
					<input type="text" class="form-control" id="filterHourlyWagesEmployeeName" readonly="readonly" value="<?php echo $salaryAccess['Employee_Name']; ?>">
				</div>

				<?php } else { ?>

				<div class="form-group">
					<label>Employee Name</label>
					<input type="text" class="form-control" id="filterHourlyWagesEmployeeName" placeholder="Employee Name">
				</div>

				<?php } ?>

				<div class="form-group">
					<label>Regular Wage</label>
					<div class="input-group">
						<input type="number" class="input-md form-control" name="filterHourlyWagesRegularWagesStart" id="filterHourlyWagesRegularWagesStart"
						    data-orientation="top" placeholder="Start" />
						<span class="input-group-addon">to</span>
						<input type="number" class="input-md form-control" name="filterHourlyWagesRegularWagesEnd" id="filterHourlyWagesRegularWagesEnd"
						    data-orientation="top" placeholder="End" />
					</div>
				</div>

				<div class="form-group">
					<label>Overtime Wage</label>
					<div class="input-group">
						<input type="number" class="input-md form-control" name="filterHourlyWagesOvertimeWageStart" id="filterHourlyWagesOvertimeWageStart"
						    data-orientation="top" placeholder="Start" />
						<span class="input-group-addon">to</span>
						<input type="number" class="input-md form-control" name="filterHourlyWagesOvertimeWageEnd" id="filterHourlyWagesOvertimeWageEnd"
						    data-orientation="top" placeholder="End" />
					</div>
				</div>

				<div class="form-group">
					<label>Effective Date</label>
					<div class="input-daterange b-datepicker input-group" data-date-format="<?php echo $dformat; ?>" id="datepicker">
						<input type="text" class="input-md form-control" name="start" id="filterHourlyWagesEffectiveDateStart" data-orientation="top"
						    placeholder="Start" />
						<span class="input-group-addon">to</span>
						<input type="text" class="input-md form-control" name="end" id="filterHourlyWagesEffectiveDateEnd" data-orientation="top"
						    placeholder="End" />
					</div>
				</div>

				<div class="form-group">
					<label>Employee Status</label>
					<select class="form-control" data-search="true" id="filterHourlyWagesStatus">
						<option value="">All</option>
						<option value="Active">Active</option>
						<option value="InActive">InActive</option>
					</select>
				</div>

				<?php if ($orgDetails['Field_Force'] == 1) { ?>
				<div class="form-group">
					<label>Service Provider</label>
					<select class="form-control" data-search="true" id="filterHourlyWagesServiceProvider">
						<option value="">All</option>
						<?php
						foreach ($serviceProvider as $key => $row)
						{
							echo '<option value="'. $key .'">'. $row .'</option>';
						}
						?>
					</select>												
				</div>
				<?php } ?>

			</div>
		</div>
	</div>

	<!-- Modal for view hourly wages history -->
	<div class="modal fade" id="modalHistoryHourlyWages" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header bg-primary">
					<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true">
						<i class="mdi-hardware-keyboard-backspace" id="backHourlyWagesHistory"></i>
					</button>

					<h4 class="modal-title">View History</h4>
				</div>
				<div class="modal-body">
					<table class="table dataTable table-striped table-dynamic table-hover" id="tableHourlyWagesHistory">
						<thead>
							<tr>
								<th></th>
								<th id="hourlyWagesHistoryRegularWages">Regular Wages</th>
								<th id="hourlyWagesHistoryOvertimeWages">Overtime Wages</th>
								<th id="hourlyWagesHistoryEligibleForPF">Eligible For Pf</th>
								<th id="hourlyWagesHistoryModifiedBy">Modified By</th>
							</tr>
						</thead>
						<tbody>

						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>

	<!-- Form Dirty Confirmation Modal -->
	<?php if ($salaryAccess['Add'] || $salaryAccess['Update']) { ?>

	<div class="modal fade" id="modalDirtyMonthlySalary" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
						<i class="icons-office-52" id="closeEditConfSalary"></i>
					</button>
					<h4 class="modal-title">
						<strong>Close</strong> Confirmation</h4>
				</div>

				<div class="modal-body">Are you sure want to close this form ?
					<br>
				</div>

				<div class="modal-footer">
					<button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfSalary">No</button>
					<button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseConfirmMonthlySalary">Yes</button>
				</div>
			</div>
		</div>
	</div>

	<div class="modal fade" id="modalCompoffWarning" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
						<i class="icons-office-52" id="closeEditCompoffWarning"></i>
					</button>
					<h4 class="modal-title">
						<strong>Compensatory Off</strong> Balance Confirmation</h4>
				</div>

				<div class="modal-body">The employee has got compensatory off balance. Do you like to remove the compensatory off record before creating an hourly salary record to avoid dual benefits for the employee?
					<br>
				</div>

				<div class="modal-footer">
					<button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditcloseEditCompoffWarning">Ignore</button>
					<button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseConfirmCompoffWarning">Take Me There</button>
				</div>
			</div>
		</div>
	</div>

	<div class="modal fade" id="modalDirtyHourlyWages" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
						<i class="icons-office-52" id="closeEditConfHourlyWages"></i>
					</button>
					<h4 class="modal-title">
						<strong>Close</strong> Confirmation</h4>
				</div>

				<div class="modal-body">Are you sure want to close this form ?
					<br>
				</div>

				<div class="modal-footer">
					<button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfHourlyWages">No</button>
					<button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseConfirmHourlyWages">Yes</button>
				</div>
			</div>
		</div>
	</div>

	<?php } if($salaryAccess['Delete']==1)  { ?>
	<!-- Delete COnfirmation Modal -->

	<!-- Monthly salary Modal -->
	<div class="modal fade" id="modalDeleteMonthlySalary" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
						<i class="icons-office-52" id="closeDeleteConfSalary"></i>
					</button>
					<h4 class="modal-title">
						<strong>Delete</strong> Confirmation</h4>
				</div>

				<div class="modal-body">Are you sure want to delete ?
					<br>
				</div>

				<div class="modal-footer">
					<button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfSalary">No</button>
					<button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConfirmMonthlySalary">Yes</button>
				</div>
			</div>
		</div>
	</div>

	<!-- Hourly wages Modal -->
	<div class="modal fade" id="modalDeleteHourlyWages" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
						<i class="icons-office-52" id="closeDeleteConfHourlyWages"></i>
					</button>
					<h4 class="modal-title">
						<strong>Delete</strong> Confirmation</h4>
				</div>

				<div class="modal-body">Are you sure want to delete ?
					<br>
				</div>

				<div class="modal-footer">
					<button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfHourlyWages">No</button>
					<button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConfirmHourlyWages">Yes</button>
				</div>
			</div>
		</div>
	</div>



	<div class="modal fade" id="modalEmployeeSalaryConfiguration" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
						<i class="icons-office-52" id="closeEditSalaryConfigurationWarning"></i>
					</button>
					<h4 class="modal-title">
						<strong>Employee Salary Configuration</strong> Redirection Confirmation</h4>
				</div>

				<div class="modal-body">
					<br>
				</div>

				<div class="modal-footer">
					<button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditcloseEditSalaryConfigurationWarning">Ignore</button>
					<button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseConfirmSalaryConfigurationWarning">Take Me There</button>
				</div>
			</div>
		</div>
	</div>

	<?php } } else { ?>
	<div class="col-md-12 portlets">
		<div class="txt_center">Sorry, Access Denied...</div>
	</div>
	<?php } ?>