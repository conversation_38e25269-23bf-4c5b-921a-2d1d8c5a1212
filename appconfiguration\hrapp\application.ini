[production]
phpSettings.display_startup_errors = 0
phpSettings.display_errors = 0
includePaths.library = APPLICATION_PATH "/../library"
bootstrap.path = APPLICATION_PATH "/Bootstrap.php"
bootstrap.class = "Bootstrap"
appnamespace = "Application"
resources.frontController.params.displayExceptions = 0

resources.modules[]=""
resources.frontController.params.prefixDefaultModule = "1"
resources.frontController.defaultModule = "default"
resources.view.doctype = "XHTML1_TRANSITIONAL"

resources.db.adapter = "PDO_MYSQL"
resources.db.params.charset = "utf8"
resources.db.params.port = 3306

resources.db.params.persistent = true
resources.db.params.compress = true

resources.mail.transport.type     = smtp
resources.mail.transport.auth     = "login"
resources.mail.transport.ssl      = "ssl"
resources.mail.transport.port     = 465
resources.mail.transport.register = true

resources.session.cache_limiter = must-revalidate
resources.view.charset = "UTF-8"
resources.view.helperPath.View_Helper = APPLICATION_PATH "/views/helpers"
resources.layout.layoutPath = APPLICATION_PATH "/layouts/scripts/"

mobileapps.production = 1
resources.frontController.moduleDirectory = APPLICATION_PATH "/modules"

mobileapps.domain = hrapp.co
mobileapps.productlogo=0

mobileapps.region = ap-south-1
mobileapps.bucketName = s3.taxdocs.hrapp.co
mobileapps.version = 2006-03-01
mobileapps.imageBucket = s3.images.hrapp.co
mobileapps.logoBucket = s3.logos.hrapp.co
mobileapps.smregion = ap-south-1
mobileapps.signedURLValidity = '1200 seconds'
mobileapps.reimbursementSignedURLValidity = '604800 seconds'

mobileapps.secretname = prod/hrapp/pgaccess
mobileapps.ocrapiurlprefix = https://c47a1p7m59.execute-api.ap-south-1.amazonaws.com/prod/
mobileapps.iciciApiBaseUrl = https://66glb9z3yk.execute-api.ap-south-1.amazonaws.com/prod/
mobileapps.iciciCIBBaseURL = https://cibnext.icicibank.com/corp/AuthenticationController
mobileapps.ccAvenueWorkingKey = 2E8DCB1A5DF6FB257CF6ECED787F5166
mobileapps.ccAvenueAccessCode = AVGH85GE18AM29HGMA
mobileapps.ccAvenueURL = "https://secure.ccavenue.com/transaction/transaction.do?command=initiateTransaction"
mobileapps.facebookURL = https://www.facebook.com/HRAPPONCLOUD/
mobileapps.twitterURL = https://twitter.com/hrapponcloud
mobileapps.linkedinURL = https://www.linkedin.com/showcase/********
mobileapps.googleURL = "https://www.google.com/maps/dir//hrapp/data=!4m6!4m5!1m1!4e2!1m2!1m1!1s0x3ba8562e7d3f6c1b:0xb8f93ff5aa0725c8?sa=X&ved=2ahUKEwiw59nourbiAhVZT30KHVnQBjsQ9RcwFXoECAkQDg"
mobileapps.websiteURL = https://hrapp.in/
mobileapps.atsBaseURL= https://api.hrapp.co/ats/graphql
mobileapps.clientipUrl= "https://api.ipify.org?format=json"
mobileapps.redirectionurl= https://www.hrapp.co/appmanager/mobilemanager
mobileapps.integrationBaseURL= https://api.hrapp.co/integration/rographql
mobileapps.trstscoreBaseURL= https://api.hrapp.co/trstscore/wographql
mobileapps.workflowEngineInitiateBaseUrl=https://api.hrapp.co/workflowEngine/workflow/initiate
mobileapps.coreHrRoBaseUrl=https://api.hrapp.co/coreHr/rographql
mobileapps.hrappBeRoBaseUrl=https://api.hrapp.co/hrappBe/roGraphql
mobileapps.employeeSelfServiceRoBaseURL=https://api.hrapp.co/employeeSelfService/rographql
mobileapps.employeeSelfServiceWoBaseURL=https://api.hrapp.co/employeeSelfService/wographql
mobileapps.coreHrWoBaseUrl=https://api.hrapp.co/coreHr/wographql
mobileapps.payrollAdminWoBaseUrl= https://api.hrapp.co/payrollAdmin/woGraphql
mobileapps.integrationWoExternalBaseURL= https://api.hrapp.co/integration/externalauth
mobileapps.batchProcessingExternalBaseURL=https://api.hrapp.co/batchProcessing/external

mobileapps.firebaseApiKey = AIzaSyAupi9_2ATYi05M7hfgO3pZFqF1dNGK7tk
mobileapps.firebaseAuthDomain = hrappidentity.firebaseapp.com
mobileapps.firebaseDatabaseURL = https://hrappidentity.firebaseio.com
mobileapps.firebaseProjectId = hrappidentity
mobileapps.firebaseStorageBucket = hrappidentity.appspot.com
mobileapps.firebaseMessagingSenderId = 887685568909
mobileapps.firebaseAppId = 1:887685568909:web:a920736acb16b2f62b45c4
mobileapps.refreshTokenAPIUrl = https://securetoken.googleapis.com/v1/

mobileapps.truleadFirebaseApiKey = AIzaSyCUfF3bLGc3yU_qBK91Tx72_3HaWf3cTYc
mobileapps.truleadFirebaseAuthDomain = hrapptruleadidentity.firebaseapp.com
mobileapps.truleadFirebaseDatabaseURL = https://hrapptruleadidentity-default-rtdb.asia-southeast1.firebasedatabase.app
mobileapps.truleadFirebaseProjectId = hrapptruleadidentity
mobileapps.truleadFirebaseStorageBucket = hrapptruleadidentity.appspot.com
mobileapps.truleadFirebaseMessagingSenderId = 265861830197
mobileapps.truleadFirebaseAppId = 1:265861830197:web:55f986543ec96ae15ec37c

mobileapps.entomoAccessTokenUrl=epms/noAuth/token
mobileapps.entomoLoginUrl=epms/oatlogin
mobileapps.entomoRefreshTokenUrl=epms/refresh

mobileapps.appSecretKey = a60be24833cbd6f0e424e133b0f55c96
mobileapps.appVersion[] = 120201912
mobileapps.appVersion[] = 121202001
mobileapps.appVersion[] = 122202002
mobileapps.appVersion[] = 123202002
mobileapps.appVersion[] = 124202003
mobileapps.appVersion[] = 125202003
mobileapps.appVersion[] = 132200000
mobileapps.appVersion[] = 132300000
mobileapps.appVersion[] = 133000000
mobileapps.appVersion[] = 134000000
mobileapps.appVersion[] = 135000000
mobileapps.appVersion[] = 136000000
mobileapps.appVersion[] = 137000000

resources.db.params.username = ""
resources.db.params.encrypt = 0
resources.db.params.password = ""
resources.db.params.dbname = hrapp_managerdb
resources.db.profiler.enabled = false
resources.mail.transport.host     = ""
resources.mail.defaultfrom.email = "<EMAIL>"