<?php
//=========================================================================================
//=========================================================================================
/* Program : ServiceBasedLeave.php									                                 *		   				
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.							                             *								
 * All Rights Reserved.            							                             *							
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MQL Query to retrive, add, update employee leaves and also to           *
 * update status reports.								                                 *								
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *                                                                                       *
 *                                                                                       */
//=========================================================================================
//=========================================================================================


class Employees_Model_DbTable_ServiceBasedLeave extends Zend_Db_Table_Abstract
{

//    protected $_name = '1';
    protected $_dbPersonal    = null;
    protected $_dbFinancialYr = null;
    protected $_dbComment     = null;
    protected $_orgDF         = null;
    protected $_db            = null;
    protected $_ehrTables     = null;
    protected $_dbJob         = null;
    protected $_dbCommonFun   = null;
    protected $_dbLeave       = null;
  
    public function init()
    {
        $this->_ehrTables     = new Application_Model_DbTable_Ehr();
        $this->_db            = Zend_Registry::get('subHrapp');
        $this->_dbPersonal    = new Employees_Model_DbTable_Personal();
        $this->_dbFinancialYr = new Default_Model_DbTable_FinancialYear();
        $this->_dbComment     = new Payroll_Model_DbTable_PayrollComment();
        $this->_dbJob         = new Employees_Model_DbTable_JobDetail();
        $this->_dbCommonFun   = new Application_Model_DbTable_CommonFunction();
        $this->_orgDF         = $this->_ehrTables->orgDateformat();
    }

    /**
     * Service Based Leave get from Employee experience based.
     */
    public function getServiceLeaveTotalDays($leaveTypeId,$employementYear)
    {
        $totalDays= $this->_db->select()->from(array('SL'=>$this->_ehrTables->empServiceLeave),array('SL.Total_Days'))
                        ->where('SL.Service_From < ?',$employementYear )
                        ->where('SL.Service_To is NULL or SL.Service_To >= ?', $employementYear)
                        ->where('SL.LeaveType_Id = ?',$leaveTypeId);
        $serviceTotalDays =$this->_db->fetchOne($totalDays);
        
        return $serviceTotalDays;
          
    }

    public function getEmploymentYear($activeEmployee)
    {
        $employmentStartDate = $activeEmployee['Date_Of_Join'];
        $employmentEndDate   = $activeEmployee['finend'];

        $startDate = new DateTime($employmentStartDate);
        $endDate   = new DateTime($employmentEndDate);
        
        if ($startDate > $endDate) {
            return 0; // Or handle the error appropriately
        }

        $diff = $endDate->diff($startDate);
  
        $employmentYear=$diff->y+1;
        
        return $employmentYear;
    }
// get start and end date
    public function startEndDate($start, $end,$date=null)
    {
        if(empty($date))
        {
            $date = date('Y-m-d');
        }
        $d = date('t',strtotime($date));
        $m = date("m",strtotime($date));
        $y = date("Y",strtotime($date));
        
        if ($m < $start)
        {
            $finStart = "1-".$start."-".(--$y);
        }
        else
        {
            $finStart = "1-".$start."-".$y;
        }
        
        $days = date('t',strtotime("1-".$end."-".$y));
        
        if ($start > $end)
        {
            $startYear = date("Y",strtotime($finStart));
            if ($y > $startYear)
            {
                $finEnd = $days."-".$end."-".$y;
            }
            else
            {
                $y++;
                $days = date('t',strtotime("1-".$end."-".$y));
                $finEnd = $days."-".$end."-".$y;
            }
        }
        else
        {
            $finEnd = $days."-".$end."-".$y;
        }
        
        $startEndDates = array();
        $startEndDates['finstart'] = $finStart;
        $startEndDates['finend'] = $finEnd;

        return $startEndDates;
    }
    public function getEmployeeServiceStartFrom($leaveTypeId, $serviceFrom=null,$serviceTo=null)
	{
		$isnullServiceTo = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empServiceLeave,array('(Service_To)'))
							->where('LeaveType_Id = ?',$leaveTypeId)
							->where('Service_To is NULL'));
		
		if(is_null($isnullServiceTo))
			{
				return array('success'=>false, 'msg'=>'Total days added for all the Experience range', 'type'=>'info');
			}
			else
			{

				$maxServiceTo = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empServiceLeave,array(new Zend_Db_Expr('Max(Service_To)')))
							->where('LeaveType_Id = ?',$leaveTypeId));
							
				if(empty($maxServiceTo))
				{
					$serviceFrom=0;
				}
				else
				{
					$serviceFrom=$maxServiceTo;
				}
				return $serviceFrom;
				
			}
    }
    

    public function getEmployeeExperienceStartFrom($leaveTypeId, $experienceFrom=null,$experienceTo=null)
	{
		$isnullExperienceTo = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empExperienceLeave,array('(Experience_To)'))
							->where('LeaveType_Id = ?',$leaveTypeId)
							->where('Experience_To is NULL'));
		
		   if(is_null($isnullExperienceTo))
			{
				return array('success'=>false, 'msg'=>'Total days added for all the Experience range', 'type'=>'info');
			}
			else
			{

				$maxExperienceTo = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empExperienceLeave,array(new Zend_Db_Expr('Max(Experience_To)')))
                            ->where('LeaveType_Id = ?',$leaveTypeId));

				if(empty($maxExperienceTo))
				{
					$ExperienceFrom=0;
				}
				else
				{
					$ExperienceFrom=$maxExperienceTo+1;
				}
				return $ExperienceFrom;
				
			}
	}
    
    public function getEmployeeMaternityStartFrom($leaveTypeId, $childFrom, $childTo)	
    {
       $isnullChildTo = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->maternitySlab,array('(Child_To)'))
                        ->where('LeaveType_Id = ?',$leaveTypeId)
                        ->where('Child_To is NULL'));
        if(is_null($isnullChildTo))
        {
            return array('success'=>false, 'msg'=>'Total days added for all the child range', 'type'=>'info');
        }
        else
        {
            $maxChildTo = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->maternitySlab,array(new Zend_Db_Expr('Max(Child_To)')))
                          ->where('LeaveType_Id = ?',$leaveTypeId));
            if(empty($maxChildTo))
            {
               $childFrom=0;
            }
            else
            {
                $childFrom=$maxChildTo+1;
            }
            return $childFrom;
        }

    }
    public function getEmployeeQuarterStartFrom($leaveTypeId, $Employment_Year_From, $Employment_Year_To)
    {
        $isnullEmploymentTo = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->leaveQuarter,array('(Employment_Year_To)'))
                        ->where('LeaveType_Id = ?',$leaveTypeId)
                        ->where('Employment_Year_To is NULL'));
        if(is_null($isnullEmploymentTo))
        {
            return array('success'=>false, 'msg'=>'Total days added for all the quarter range', 'type'=>'info');
        }
        else
        {
            $maxEmploymentTo = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->leaveQuarter,array(new Zend_Db_Expr('Max(Employment_Year_To)')))
                          ->where('LeaveType_Id = ?',$leaveTypeId));
            if(empty($maxEmploymentTo))
            {
               $Employment_Year_From=0;
            }
            else
            {
                $Employment_Year_From=$maxEmploymentTo;
            }
            return $Employment_Year_From;
        }

    }

    public function searchEmployeeService($leaveTypeId)
    {
        $qryEmployeeService = $this->_db->select()->from(array('SL'=>$this->_ehrTables->empServiceLeave),
                                                    array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS SL.Service_Id as count'),'SL.Service_Id',
                                                                  'SL.LeaveType_Id','SL.Service_From','SL.Service_To','SL.Total_Days',
                                                                  'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', SL.Service_Id)")))
                                                   
                                                   	->joinInner(array('LT'=>$this->_ehrTables->leavetype),"LT.LeaveType_Id = SL.LeaveType_Id", array('LT.Leave_Enforcement_Configuration'))
									  
                                                      
                                                    ->where('SL.LeaveType_Id = ?', $leaveTypeId);
 
        	
        $employeeServiceDetails = $this->_db->fetchAll($qryEmployeeService);
                
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
                
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empServiceLeave, new Zend_Db_Expr('COUNT(Service_Id)'))->where('LeaveType_Id = ?', $leaveTypeId));
                
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $employeeServiceDetails);
    }
    
    public function updateEmployeeService($empServiceLeave,$sessionId, $formName)
    {
        $qryEmployeeServiceSlab = $this->_db->select()->from($this->_ehrTables->empServiceLeave, new Zend_Db_Expr('COUNT(Service_Id)'))
                                    ->where('LeaveType_Id = ?', $empServiceLeave['LeaveType_Id'])
                                    ->where('Service_From < ?',$empServiceLeave['Service_To'])
                                    ->where('Service_To  >= ?', $empServiceLeave['Service_To']);

        if (!empty($empServiceLeave['Service_Id']))
        {
          $qryEmployeeServiceSlab->where('Service_Id != ?', $empServiceLeave['Service_Id']);
        }

        $empServiceLeaveExists =$this->_db->fetchOne($qryEmployeeServiceSlab);

        if(empty($empServiceLeaveExists))
            {
				if(!empty($empServiceLeave['Service_Id']))
				{
                        $action = 'Edit';
                        $updated = $this->_db->update($this->_ehrTables->empServiceLeave, $empServiceLeave, array('Service_Id = '.$empServiceLeave['Service_Id'])); 
                        $serviceId = $empServiceLeave['Service_Id'];   
                        $updated   = $this->getEmployeeServiceRange($serviceId,$empServiceLeave);
            
				}
				else
				{
                        $action = 'Add';
                        $updated =  $this->_db->insert($this->_ehrTables->empServiceLeave, $empServiceLeave);
                        $serviceId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empServiceLeave,new Zend_Db_Expr('Max(Service_Id)')));
                        $updated   = $this->getEmployeeServiceRange($serviceId,$empServiceLeave);
                       
             	}
				$updated = 1;
                $result = $this->_dbCommonFun->updateResult (array('updated'    => $updated,
                                    'action'         => $action,
                                    'trackingColumn' => $empServiceLeave['Service_Id'],
                                    'formName'       => $formName,
                                    'sessionId'      => $sessionId,
                                    'tableName'      => $this->_ehrTables->empServiceLeave));
    
            return $result;
		}
		else
		{
				return array('success'=>false, 'msg'=>'Employee Service is Already Exist for this level', 'type'=>'info');
		}
	
    }

    public function getEmployeeServiceRange($serviceId,$empServiceLeave)
    {
        $employeeEligibleLeave  = array();
        $dbLeave                = new Employees_Model_DbTable_Leave();
        if(!empty($empServiceLeave['LeaveType_Id']))
        {
            $updated     = $dbLeave->empDOJUpdateEligibleDays(NULL,'update-service-based-leave',NULL,$empServiceLeave['LeaveType_Id'],0,'Yes');
        }
        else
        {
            $updated =  0;
        }
        return $updated;
    }

    public function deleteEligibleServiceLeave($serviceId,$leaveTypeId)
    {
        $serviceDetailQry =$this->_db->select()->from($this->_ehrTables->empServiceLeave,array('Service_From','Service_To'))
                         ->where('Service_Id = ?',$serviceId);
        $serviceDetail = $this->_db->fetchRow($serviceDetailQry);

        $dbLeave                = new Employees_Model_DbTable_Leave();
        $leaveTypeDetails       = $dbLeave->getLeaveTypeRow($leaveTypeId);
        $activeEmployeeDetails  = $dbLeave->getActiveEmployeesDetails($leaveTypeDetails);

        $maxServiceFrom = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empServiceLeave,array(new Zend_Db_Expr('Max(Service_From)')))
                            ->where('LeaveType_Id = ?',$leaveTypeId));
        if($maxServiceFrom == $serviceDetail['Service_From']) 
        {  
            foreach($activeEmployeeDetails as $activeEmployee)
            {
                $employmentYear = $activeEmployee['Employment_Year'];
                if($serviceDetail['Service_From'] < $employmentYear && ($serviceDetail['Service_To'] >=$employmentYear || is_null($serviceDetail['Service_To'])))
                {
                        $whereCondition['Employee_Id = ?']  = $activeEmployee['Employee_Id'];
                        $whereCondition['LeaveType_Id = ?'] = $activeEmployee['LeaveType_Id'];
                        $deleted=$this->_db->delete($this->_ehrTables->empEligbleLeave,$whereCondition);
                }
            }
            $deleted = $this->_db->delete($this->_ehrTables->empServiceLeave, 'Service_Id='.(int)$serviceId);            
            return $deleted;
        }
        else
        {
            return 0;
        } 
    }

    public function getQuarterWiseLeaveTotalDays($employeeId,$leaveTypeId,$employmentYear,$empDOJ=null)
    {
        if(is_null($empDOJ))
        {
             $empDOJ = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob,array('Date_Of_Join'))
                        ->where('Employee_Id =?',$employeeId));
        }
        $empDOJ = explode('-', $empDOJ);
        $joinMonth = $empDOJ[1];

        $totalDays= $this->_db->select()->from(array('LQ'=>$this->_ehrTables->leaveQuarter),array('LJ.Total_Days'))
                    ->joinInner(array('LJ'=>$this->_ehrTables->leaveJoinQuarter),"LJ.Leave_Quarter_Id = LQ.Leave_Quarter_Id",array(''))
                    ->where('LJ.Join_From <= ?',$joinMonth)
                    ->where('LJ.Join_To >= ?',$joinMonth)
                    ->where('LQ.Employment_Year_From < ?',$employmentYear)
                    ->where('LQ.Employment_Year_To is NULL or LQ.Employment_Year_To >= ?', $employmentYear)
                    ->where('LQ.LeaveType_Id = ?',$leaveTypeId);
        $quarterTotalDays =$this->_db->fetchOne($totalDays);
        return $quarterTotalDays;
    }

    /**
     * Gets the employee service leave based on experience.
     * 
     * @return array An array of employee service leave details organized by leave type id.
     */
    public function getEmployeeServiceLeave()
    {
        $qryServiceDetails= $this->_db->select()->from(array('ESL'=>$this->_ehrTables->empServiceLeave),array('ESL.*'))
                    ->joinInner(array('LT'=>$this->_ehrTables->leavetype),"LT.LeaveType_Id = ESL.LeaveType_Id",array(''))
                    ->where('LT.Leave_Status = ?', 'Active');
        $serviceDetails =$this->_db->fetchAll($qryServiceDetails);
        if(!empty($serviceDetails))
        {
            $organizeServiceDetails = $this->_dbCommonFun->organizeDataByEmployeeIdAndDate($serviceDetails,'LeaveType_Id');
            return $organizeServiceDetails;
        }
        return [];
    }

    /**
     * Gets the employee service leave based on experience for a given employment year.
     * 
     * @param array $organizeServiceDetails An array of employee service leave details organized by leave type id
     * @param int $employementYear The employment year for which the service leave details are to be found
     * @param int $leaveTypeId The leave type id for which the service leave details are to be found
     * 
     * @return array An array of employee service leave details that match the given employment year, or an empty array if no matching service period is found.
     */
    public function getEmployeeServiceLeaveBasedOnExperience($organizeServiceDetails, $employementYear,$leaveTypeId)
    {
        if(!empty($organizeServiceDetails))
        {
            $employeeServiceDetails = $this->_dbCommonFun->ensureArray($organizeServiceDetails, $leaveTypeId);
            if(!empty($employeeServiceDetails))
            {
                foreach ($employeeServiceDetails as $employeeService) {
                    $serviceFrom = (int) $employeeService['Service_From'];
                    $serviceTo = isset($employeeService['Service_To']) ? (int) $employeeService['Service_To'] : 100;
                    $employeeService['Service_To'] = $serviceTo;
                    if ($serviceFrom <= $employementYear && $serviceTo >= $employementYear) {
                        return $employeeService;
                    }
                }
            }
        }
        return []; // Return an empty array if no matching service period is found.
    }


    public function __destruct()
    {
        
    }	
     
}

