<?php
//=========================================================================================
//=========================================================================================
/* Program : Travels.php											   				     *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : MQL Query to retrive, add, update travel details and also to update     *
 * status report.													 					 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                   Description                       *
 *  0.1        30-May-2013    Sandhosh                 Initial Version         	         *
 *                                                                                   	 *
 *  1.0        02-Feb-2015    Sandhosh                 Changes in file for mobile app    *
 *                                                     1.Extra fields are added in       *
 *                                                     field list of list query.         */
//=========================================================================================
//=========================================================================================
class Employees_Model_DbTable_Travels extends Zend_Db_Table_Abstract
{

    protected $_db = null;

    protected $_ehrTables = null;

    protected $_dbComment = null;

    protected $_orgDF = null;
    
     protected $_commonFunction = null;

    public function init()
    {
        $this->_ehrTables = new Application_Model_DbTable_Ehr();
        $this->_db = Zend_Registry::get('subHrapp');
        $this->_dbComment = new Payroll_Model_DbTable_PayrollComment();
        $this->_orgDF = $this->_ehrTables->orgDateformat();
	$this->_commonFunction = new Application_Model_DbTable_CommonFunction();
	$this->_eftConfiguration =new Organization_Model_DbTable_EftConfiguration();
    }
      
    public function searchEmployeeTravel($page,$rows,$sortField,$sortOrder,$searchAll=null,$searchDetails,$userDetails)
    {
        switch ($sortField)
        {
			case 1: $sortField = 'EJ.User_Defined_EmpId'; break;
            case 2: $sortField = 'EP2.Emp_First_Name'; break;
			case 3: $sortField = 'ET.Budget'; break;
			case 4: $sortField = 'ET.Start_Date'; break;
			case 5: $sortField = 'ET.End_Date'; break;
			case 6: $sortField = 'Status'; break;
			default:
				$sortField = 'ET.Added_On'; $sortOrder = 'desc'; break;
        }
        
        if (!empty($userDetails['Form_Name']))
        {
        	$formId = $this->_dbComment->getFormId($userDetails['Form_Name']);
        	 
        	$qryComment = $this->_db->select()->distinct()->from(array('Cm'=>$this->_ehrTables->comment), 'Parent_Id')
												->where('Cm.Parent_Id = ET.Request_Id AND Cm.Form_Id='.$formId);
        }
	
	
        $qryEmployeeTravel = $this->_db->select()->from(array('ET'=>$this->_ehrTables->empTravels),
								array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS ET.Request_Id as count'),'ET.Request_Id','ET.Employee_Id',
									  'ET.Approver_Id','ET.Start_Date','ET.End_Date','ET.Budget','ET.Purpose','ET.Payroll_Mid','ET.Added_By as AddedBy',
								new Zend_Db_Expr("DATE_FORMAT(ET.End_Date,'".$this->_orgDF['sql']."') as EndDate"),
								new Zend_Db_Expr("DATE_FORMAT(ET.Start_Date,'".$this->_orgDF['sql']."') as StartDate"),
								new Zend_Db_Expr("DATE_FORMAT(ET.Added_On,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
								'ET.Approval_Status',
								'Status'=>new Zend_Db_Expr('(CASE WHEN ET.Approval_Status = \'In Process\' THEN \'Approved\' ELSE ET.Approval_Status END)'),
								'Audit'=>new Zend_Db_Expr("(".$this->_db->select()->distinct()
															->from(array('AUDITRAV'=>$this->_ehrTables->auditEmpTravels),'Request_Id')
															->where('AUDITRAV.Request_Id = ET.Request_Id').")"),
								'Comment'=> new Zend_Db_Expr('('.$qryComment.')'),
								'Session_Id'=>new Zend_Db_Expr($userDetails['Session_Id']),
								'Admin' => new Zend_Db_Expr("'".$userDetails['Admin']."'"),
								'Op_Choice'=>new Zend_Db_Expr($userDetails['Op_Choice']),
                                'DT_RowClass' => new Zend_Db_Expr('"EmployeeTravel"'),
                                'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', ET.Request_Id)")))
        
                                ->joinInner(array('EP'=>$this->_ehrTables->empPersonal),'ET.Added_By=EP.Employee_Id',
                                        array(new Zend_Db_Expr("CONCAT(EP.Emp_First_Name, ' ', EP.Emp_Last_Name) as Added_By")))
                                
                                 
                                ->joinLeft(array('EP2'=>$this->_ehrTables->empPersonal),'ET.Employee_Id=EP2.Employee_Id',
									    array(new Zend_Db_Expr("CONCAT(EP2.Emp_First_Name, ' ', EP2.Emp_Last_Name) as Employee_Name")))
                                
                                 ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'EP2.Employee_Id=EJ.Employee_Id',
                                            array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN EP2.Employee_Id ELSE EJ.User_Defined_EmpId END')))
                                
						        ->joinLeft(array('EP3'=>$this->_ehrTables->empPersonal),'ET.Approver_Id=EP3.Employee_Id',
									    array(new Zend_Db_Expr("CONCAT(EP3.Emp_First_Name, ' ', EP3.Emp_Last_Name) as Approver_Name")))
                                
								->group('ET.Request_Id')
								->order("$sortField $sortOrder")
								->limit($rows, $page);


	    if(empty($userDetails['Admin']))
	    {
		    $qryEmployeeId = $this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))->where('Manager_Id = ?', $userDetails['Session_Id']);
		    $getEmployeeId = $this->_db->fetchCol($qryEmployeeId);
		    if ( $userDetails['Is_Manager'] == 1 && $userDetails['Op_Choice'] == 1  && !empty($getEmployeeId))
            {
			    $qryEmployeeTravel->where('ET.Employee_Id = :EmpId or ET.Approver_Id = :EmpId or ET.Payroll_Mid = :EmpId or ET.Employee_Id IN (?)', $getEmployeeId)
                                  ->bind(array('EmpId'=>$userDetails['Session_Id']));
		    }
		    elseif ( $userDetails['Is_Manager'] == 1 && $userDetails['Op_Choice'] == 0  && !empty($getEmployeeId))
            {
			    $qryEmployeeTravel->where('ET.Employee_Id = :EmpId or ET.Approver_Id = :EmpId or ET.Employee_Id IN (?)', $getEmployeeId)
                                  ->bind(array('EmpId'=>$userDetails['Session_Id']));
		    }
		    elseif (  $userDetails['Is_Manager'] == 0 && $userDetails['Op_Choice'] == 1 )
		    {
			    $qryEmployeeTravel->where('ET.Employee_Id = ? or ET.Payroll_Mid = ?', $userDetails['Session_Id']);
		    }
		    else
            {
			    $qryEmployeeTravel->where('ET.Employee_Id = ?', $userDetails['Session_Id']);
		    }
	    }
						    
						    
        
        if (!empty($searchAll) && $searchAll != null)
		{
//			$qryEmployeeTravel->where(new Zend_Db_Expr('Concat(EP2.Emp_First_Name," ",EP2.Emp_Last_Name) Like ?'),"%$searchAll%")
//            //->where('EP2.Emp_First_Name Like ?', "%$searchAll%")
//            //                        ->orwhere('EP2.Emp_Last_Name Like ?', "%$searchAll%")
//				    ->orwhere('ET.End_Date = ?', $searchAll)
//				    ->orwhere('ET.Start_Date = ?', $searchAll)
//				    ->orwhere('ET.Budget Like ?', "%$searchAll%")
//                                    ->orwhere('ET.Approval_Status Like ?', "%$searchAll%");
									
			$conditions  = $this->_db->quoteInto(new Zend_Db_Expr('Concat(EP2.Emp_First_Name," ",EP2.Emp_Last_Name) Like ?'),"%$searchAll%");
			$conditions .= $this->_db->quoteInto('or ET.End_Date Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or ET.Start_Date Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or ET.Budget Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or ET.Approval_Status Like ?', "%$searchAll%");
            $conditions .= $this->_db->quoteInto('or EJ.User_Defined_EmpId Like ?', "%$searchAll%");
			
			$qryEmployeeTravel->where($conditions);
		}
        
        $employeeName=$searchDetails['Employee_Name'];
        $startDate =$searchDetails['Start_Date'];
        $endDate =$searchDetails['End_Date'];
        $approvalStatus = $searchDetails['Approval_Status'];
	
	
		if ($employeeName != '' && $employeeName != null && preg_match('/^[a-zA-Z]/', $employeeName))
		{
				$qryEmployeeTravel->where($this->_db->quoteInto(new Zend_Db_Expr('Concat(EP2.Emp_First_Name," ",EP2.Emp_Last_Name) Like ?'),"%$employeeName%"));
//                ->where($this->_db->quoteInto('EP2.Emp_First_Name Like ? or ', "%$employeeName%").
//								   $this->_db->quoteInto('EP2.Emp_Last_Name Like ?', "%$employeeName%"));
		}
		if ($startDate != '' && $endDate != '')
		{
			$qryEmployeeTravel->where($this->_db->quoteInto('ET.Start_Date >= ?', $startDate));
			$qryEmployeeTravel->where($this->_db->quoteInto('ET.End_Date <= ?', $endDate));
		}
		 if (!empty($approvalStatus))
		{
			$qryEmployeeTravel->where($this->_db->quoteInto('ET.Approval_Status = ?',$approvalStatus));
		}
		
		$qryEmployeeTravel = $this->_commonFunction->getDivisionDetails($qryEmployeeTravel,'EJ.Department_Id');

		$accountCodeDetails = $this->_db->fetchAll($qryEmployeeTravel);
                
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		if(empty($userDetails['Admin']))
        {
			$iTotalqry = $this->_db->select()->from($this->_ehrTables->empTravels, new Zend_Db_Expr('COUNT(Request_Id)'));
			
            $qryEmployeeId = $this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
			->where('Manager_Id = ?', $userDetails['Session_Id']);
            $getEmployeeId = $this->_db->fetchCol($qryEmployeeId);
			
		    if ( $userDetails['Is_Manager'] == 1 && $userDetails['Op_Choice'] == 1  && !empty($getEmployeeId))
            {
			    $iTotalqry->where('Employee_Id = :EmpId or Approver_Id = :EmpId or Payroll_Mid = :EmpId or Employee_Id IN (?)', $getEmployeeId)
                                  ->bind(array('EmpId'=>$userDetails['Session_Id']));
		    }
		    elseif ( $userDetails['Is_Manager'] == 1 && $userDetails['Op_Choice'] == 0  && !empty($getEmployeeId))
            {
			    $iTotalqry->where('Employee_Id = :EmpId or Approver_Id = :EmpId or Employee_Id IN (?)', $getEmployeeId)
                                  ->bind(array('EmpId'=>$userDetails['Session_Id']));
		    }
		    elseif (  $userDetails['Is_Manager'] == 0 && $userDetails['Op_Choice'] == 1 )
		    {
			    $iTotalqry->where('Employee_Id = ? or Payroll_Mid = ?', $userDetails['Session_Id']);
		    }
		    else
            {
			    $iTotalqry->where('Employee_Id = ?', $userDetails['Session_Id']);
		    }
			
			$iTotal = $this->_db->fetchOne($iTotalqry);
        }
		else{
			$iTotal = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->empTravels, new Zend_Db_Expr('Request_Id'))
									   ->group('Request_Id'));
			
			$iTotal = count($iTotal);
			
			//$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empTravels, new Zend_Db_Expr('COUNT(Request_Id)'))
			//							   ->group('Request_Id'));
		}		
                
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $accountCodeDetails);
    }
    
    public function updateEmployeeTravel($employeeTravel,$sessionId,$comments,$auditTravelData,$ret=null,$customFormName)
    {
		$isRowExist = $this->isTravelDateExist($employeeTravel['Employee_Id'], $employeeTravel['Start_Date'], $employeeTravel['End_Date'],$employeeTravel['Request_Id']);
		 
		if(!$isRowExist)
		{
				if(!empty($employeeTravel['Itinerary_Id']))
				{
					$action = 'Edit';
					$employeeTravel['Lock_Flag']  = 0;
					$updated = $this->_db->update($this->_ehrTables->empTravels, $employeeTravel, array('Itinerary_Id = '.$employeeTravel['Itinerary_Id']));
					$requestId = $employeeTravel['Request_Id'];
					
					if ($updated)
					{							
						if($auditTravelData['Update'] != 0)
						{								
							$this->addTravelHistory($auditTravelData['Data']);
						}						
				
						if(!empty($employeeTravel['Request_Id'])){
							$travelData = array('Purpose'      	    => $employeeTravel['Purpose'],
											'Start_Date'   	    => $employeeTravel['Start_Date'],
											'End_Date' 		    => $employeeTravel['End_Date'],
											'Budget'    	    => $employeeTravel['Budget']);
						
							$this->_db->update($this->_ehrTables->empTravels, $travelData, array('Request_Id = '.$employeeTravel['Request_Id']));
						}				
					}
				}
				else if(empty($employeeTravel['Itinerary_Id']) && !empty($employeeTravel['Request_Id']) && $ret)
				{
					
					$action = 'Edit';
					$employeeTravel['Lock_Flag']  = 0;
					$updated = $this->_db->update($this->_ehrTables->empTravels, $employeeTravel, array('Request_Id = '.$employeeTravel['Request_Id']));
					$requestId = $employeeTravel['Request_Id'];
					
					//if ($updated)
					//{							
					//	if($auditTravelData['Update'] != 0)
					//	{								
					//		$this->addTravelHistory($auditTravelData['Data']);
					//	}
					//}		
				}
				else
				{
					if(empty($employeeTravel['Request_Id'])){
						$maxRequestId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empTravels, array('MaxId' => new Zend_Db_Expr('Max(Request_Id)'))));
						$employeeTravel['Request_Id'] = $maxRequestId + 1;
					}
					
					$maxItineraryId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empTravels, array('MaxId' => new Zend_Db_Expr('Max(Itinerary_Id)'))));
					$employeeTravel['Itinerary_Id'] = $maxItineraryId + 1;
					
					$action = 'Add';
					$employeeTravel['Added_On'] = date('Y-m-d H:i:s');
					$employeeTravel['Added_By'] = $sessionId;
					$updated =  $this->_db->insert($this->_ehrTables->empTravels, $employeeTravel);
					
					if($updated){
						if(!empty($employeeTravel['Request_Id'])){
							$travelData = array('Purpose'      	    => $employeeTravel['Purpose'],
											'Start_Date'   	    => $employeeTravel['Start_Date'],
											'End_Date' 		    => $employeeTravel['End_Date'],
											'Budget'    	    => $employeeTravel['Budget']);
						
							$this->_db->update($this->_ehrTables->empTravels, $travelData, array('Request_Id = '.$employeeTravel['Request_Id']));
						}
					}
					//$requestId=$this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empTravels,new Zend_Db_Expr('Max(Request_Id)')));
				}
				
				if(!empty($comments))
				{
					$formId = $this->_dbComment->getFormId('Employee Travel');
					$addComment = array('Form_Id'=>$formId,
							'Emp_Comment'=>$comments,
							'Approval_Status'=>$employeeTravel['Approval_Status'],
							'Parent_Id'=>$employeeTravel['Request_Id'],
							'Employee_Id'=>$sessionId,
							'Added_On'=>date('Y-m-d H:i:s'));
					$this->_db->insert($this->_ehrTables->comment, $addComment);
				}    
					
				return $this->_eftConfiguration->updateResult (array('updated'    => $updated,
									'action'         => $action,
									'trackingColumn' => $employeeTravel['Request_Id'],
									'formName'       => $customFormName,
									'sessionId'      => $sessionId,
									'tableName'      => $this->_ehrTables->empTravels));
			
		}
		else{
			return array('success'=> false, 'msg' => 'Travel details already exist for this duration!', 'type' => 'info');	
		}
    }


     public function deleteEmployeeTravel($requestId, $logEmpId,$customFormName)
    {
       if (!empty($requestId))
       {
		    $employeeTravelLock = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->empTravels, array('Lock_Flag', 'Approval_Status'))
		     ->where('Request_Id = ?', $requestId));
			
		    if ($employeeTravelLock['Lock_Flag'] == 0 && $employeeTravelLock['Approval_Status'] == 'Pending Approval' && $employeeTravelLock['Approval_Status'] == 'Rejected')
		    {
			    $deleted = $this->_db->delete($this->_ehrTables->empTravels, 'Request_Id='.(int)$requestId);
			    $deleted1 = $this->_db->delete($this->_ehrTables->destination, 'Request_Id='.(int)$requestId);
			    if ($deleted)
	                    {
    					$this->_dbComment->deleteComment($requestId,'Employee Travel');
			    }
			      return $this->_commonFunction->deleteRecord (array('deleted'        => $deleted,
								    'tableName'      => $this->_ehrTables->empTravels,
								    'lockFlag'       => $employeeTravelLock['Lock_Flag'],
								    'formName'       => $customFormName,
								    'trackingColumn' => $employeeTravelLock['Approval_Status'],
								    'sessionId'      => $logEmpId));
			    
		    }
		    else
		    {
			return array('success'=>false, 'msg'=>'Unable to delete '.$customFormName, 'type'=>'info');
		    }
	}
        else
        {
            return array('success'=>false, 'msg'=>'Unable to delete '.$customFormName.'. Please, contact system admin', 'type'=>'info');
        }
    }
    public function searchDestinationDetails($requestId)
    {
        
 
        $qryDestination = $this->_db->select()->from(array('ET'=>$this->_ehrTables->empTravels),
                                                    array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS ET.Itinerary_Id as count'),
                                                                  'ET.Itinerary_Id','ET.Request_Id','ET.Place','ET.Travel_Mode','ET.Arrange',
                                                                  'DT_RowClass' => new Zend_Db_Expr('"destination"'),
                                                                  'DT_RowId' => new Zend_Db_Expr("CONCAT('row_', ET.Itinerary_Id)")))
			                                        ->where('ET.Request_Id = ?', $requestId);
 
        	
        	$DestinationDetails = $this->_db->fetchAll($qryDestination);
                
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
                
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empTravels, new Zend_Db_Expr('COUNT(Itinerary_Id)'))->where('Request_Id = ?', $requestId));
                
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $DestinationDetails);
    }

    
      public function updateDestinationDetails($destinationDetails,$auditTravelData,$sessionId)
    {
    $qryDestination = $this->_db->select()->from($this->_ehrTables->destination, new Zend_Db_Expr('count(LineItem_Id)'))
                                        ->where("Place = ?",$destinationDetails['Place'])
					->where("Travel_Mode = ?",$destinationDetails['Travel_Mode'])
                                        ->where("Request_Id = ?",$destinationDetails['Request_Id'])
                                        ->where("Arrange = ?",$destinationDetails['Arrange']);


         if (!empty($destinationDetails['LineItem_Id']))
                $qryDestination->where('LineItem_Id != ?', $destinationDetails['LineItem_Id']);

        $DestinationExists = $this->_db->fetchOne($qryDestination);
        if(empty($DestinationExists))
        {
                if(!empty($destinationDetails['LineItem_Id']))
                {
                        $action = 'Edit';
                        $updated = $this->_db->update($this->_ehrTables->destination, $destinationDetails, array('LineItem_Id = '.$destinationDetails['LineItem_Id']));
                        $lineItemId = $destinationDetails['LineItem_Id'];
                        //$updated =1;
						
						/** add Audit Travel **/
						if ($updated)
						{							
							if($auditTravelData['Update'] != 0){								
								$this->addTravelHistory($auditTravelData['Data']);
							}
						}
                }
                else
                {
                        $action = 'Add';
                        $updated =  $this->_db->insert($this->_ehrTables->destination, $destinationDetails);
						$lineItemId = $this->_db->lastInsertId();
                }
                return $this->_commonFunction->updateResult (array('updated'    => $updated,
								'action'         => $action,
								'trackingColumn' => $lineItemId,
								'formName'       => 'Destination Details',
								'sessionId'      => $sessionId,
								'tableName'      => $this->_ehrTables->destination));
        }
        else
        {
            return array('success' => false, 'msg'=>'Destination Details Already Exist For This Employee', 'type'=>'info');
        } 
    }
    public function deleteDestinationDetails($lineItemId,$logEmpId)
    {
        if (!empty($lineItemId))
        {
			$deleted = $this->_db->delete($this->_ehrTables->empTravels, 'Itinerary_Id='.(int)$lineItemId);
            return $this->_commonFunction->deleteRecord (array( 'deleted'        => $deleted,
                                                                'tableName'      => $this->_ehrTables->empTravels,
                                                                'lockFlag'       => 0,
                                                                'formName'       => 'Employee Travel',
                                                                'trackingColumn' => $lineItemId,
                                                                'sessionId'      => $logEmpId));
		  
		  
            //$deleted = $this->_db->delete($this->_ehrTables->destination, 'LineItem_Id='.(int)$lineItemId);
            //return $this->_commonFunction->deleteRecord (array( 'deleted'        => $deleted,
            //                                                    'tableName'      => $this->_ehrTables->destination,
            //                                                    'lockFlag'       => 0,
            //                                                    'formName'       => 'Destination Details',
            //                                                    'trackingColumn' => $lineItemId,
            //                                                    'sessionId'      => $logEmpId));
            //
		}
        else
        {
            return array('success'=>false, 'msg'=>'Unable to delete Destination Details. Please, contact system admin', 'type'=>'info');
        }
    }
	
	/**
	 * Get employee travel audit details to show in a grid
	*/
    public function empTravelAudit($request)
    {
        $showTravAuditResult = $this->_db->select()->from(array('AuditTravel'=>$this->_ehrTables->auditEmpTravels), array('Audit_Id','Request_Id','Previous_Place','Updated_Place','Previous_Mode','Updated_Mode',
    				'Previous_Arrange','Updated_Arrange','Modified_By','ModifiedDate'=>new Zend_Db_Expr("DATE_FORMAT(AuditTravel.Modified_On,'".$this->_orgDF['sql']." %T')")))
        ->joinInner(array('TRAV'=>$this->_ehrTables->empTravels), 'TRAV.Request_Id = AuditTravel.Request_Id', array('Employee_Id'))
        ->joinInner(array('PER'=>$this->_ehrTables->empPersonal), 'PER.Employee_Id = TRAV.Employee_Id', array('employeeName' => new Zend_Db_Expr("CONCAT(PER.Emp_First_Name, ' ', PER.Emp_Last_Name)")))
        ->joinInner(array('PER1'=>$this->_ehrTables->empPersonal), 'PER1.Employee_Id = AuditTravel.Modified_By', array('ModifiedBy' => new Zend_Db_Expr("CONCAT(PER1.Emp_First_Name, ' ', PER1.Emp_Last_Name)")))
        ->where('AuditTravel.Request_Id = ?',$request)->group('AuditTravel.Audit_Id');
        
		//$showTravAuditResult = $this->_db->fetchAll($showTravAuditQry);
        //return Zend_Json::encode($showTravAuditResult);
		
		/**
		 * SQL queries
		 * Get data to display
		*/
		$auditTravelDetails = $this->_db->fetchAll($showTravAuditResult);
		
		/* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
        
		/* Total data set length */
		$iTotal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->auditEmpTravels, new Zend_Db_Expr('COUNT(Audit_Id)')));
		
		/**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $auditTravelDetails);
    }
	
	/**
	 * Get travel employee details by requestId
	*/
    public function travelEmployee($requestId)
    {
        $qryEmpId = $this->_db->select()->from($this->_ehrTables->empTravels, array('Employee_Id'))
        ->group('Request_Id')->having('Request_Id = ?', $requestId);
        $qryEmpName = $this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal), array('Employee_Id','Employee_Name'=>new Zend_Db_Expr("CONCAT(Emp_First_Name, ' ', Emp_Last_Name)")))
        ->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.Employee_Id=P.Employee_Id', array())
        ->where('P.Employee_Id = ?', $qryEmpId)->where('P.Form_Status = 1')->where('J.Emp_Status Like ?', 'Active');
        $rowEmpName = $this->_db->fetchRow($qryEmpName);
        return $rowEmpName;
    }
	
	/**
	 * Get travel details by requestId, loginId, status, flag
	*/
    public function ckTravelStatus($requestId, $loginId, $status, $flag)
    {
        $qryCountEmployee = $this->_db->select()->from($this->_ehrTables->empTravels, new Zend_Db_Expr('count(Employee_Id)'))
        ->where('Approval_Status = ?', $status)
        ->group('Request_Id')->having('Request_Id = ?',$requestId);
        switch($flag)
        {
            case '1': // status = 'inprocess'
                $qryCountEmployee->where('Approver_Id = ?', $loginId);
                break;
            case '2':// status = 'complete'
                $qryCountEmployee->where('Payroll_Mid = ?', $loginId);
                break;
            case '3'://// status = 'rejected or returned'
                $qryCountEmployee->where('Payroll_Mid = ? or Approver_Id = ?', $loginId);
                break;
        }
        $rowCountEmployee = $this->_db->fetchOne($qryCountEmployee);
        return $rowCountEmployee;
    }
	
		/**
	 * Get employee travel details to show in grid
	 */
    public function viewEmpTravel($itineraryId)
    {
        /*fetching specific row to view details   */
    //    $fetchExistTravQry = $this->_db->select()
    //    ->from(array('TRAV'=>$this->_ehrTables->empTravels),array('addedOn' => new Zend_Db_Expr("DATE_FORMAT(Added_On,'".$this->_orgDF['sql']." %T')"), 'FormatStart_Date' => new Zend_Db_Expr("DATE_FORMAT(Start_Date,'".$this->_orgDF['sql']."')"),
    //			'FormatEnd_Date' => new Zend_Db_Expr("DATE_FORMAT(End_Date,'".$this->_orgDF['sql']."')"),'submissionDate' => new Zend_Db_Expr("DATE_FORMAT(Submission_Date,'".$this->_orgDF['sql']."')"),'Itinerary_Id','Request_Id','Employee_Id','Purpose',
    //			'Start_Date','End_Date','Budget','Place','Travel_Mode','Arrange','Added_On','Approver_Id','Payroll_Mid',
    //			'Status'=>new Zend_Db_Expr('(CASE WHEN `Approval_Status` = \'In Process\' THEN \'Approved\' ELSE `Approval_Status` END)'),'Added_By','Submission_Date'))
    //    ->joinInner(array('PER'=>$this->_ehrTables->empPersonal),'TRAV.Employee_Id = PER.Employee_Id',array('employeeName' => new Zend_Db_Expr("CONCAT(PER.Emp_First_Name, ' ', PER.Emp_Last_Name)")))
    //    ->joinInner(array('PER1'=>$this->_ehrTables->empPersonal),'TRAV.Approver_Id = PER1.Employee_Id',array('approverName' => new Zend_Db_Expr("CONCAT(PER1.Emp_First_Name, ' ', PER1.Emp_Last_Name)")))
    //    ->joinInner(array('PER2'=>$this->_ehrTables->empPersonal), 'TRAV.Added_By = PER2.Employee_Id',array('AddedBy' => new Zend_Db_Expr("CONCAT(PER2.Emp_First_Name, ' ', PER2.Emp_Last_Name)")))
    //    ->where('TRAV.Request_Id = ?', $requestId)
    //    ->order("Itinerary_Id ASC");
    //    $fetchExistTravDet = $this->_db->fetchAll($fetchExistTravQry);
    //    return $fetchExistTravDet;
	
		$fetchExistTravQry = $this->_db->select()->from(array('ET'=>$this->_ehrTables->empTravels),
									array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS ET.Itinerary_Id as count'),
                                        'ET.Itinerary_Id','ET.Request_Id','ET.Place','ET.Travel_Mode','ET.Arrange','ET.Approval_Status'))
									->where('ET.Itinerary_Id = ?', $itineraryId);
        $fetchExistTravDet = $this->_db->fetchAll($fetchExistTravQry);
        return $fetchExistTravDet;
    }
	
	/**
	 * Add travel history
	*/
    public function addTravelHistory($arrTravelHistory)
    {
        //$this->_ehrTables->insertMultiple($this->_ehrTables->auditEmpTravels, $arrTravelHistory);
		$this->_db->insert($this->_ehrTables->auditEmpTravels, $arrTravelHistory);
    }

    
	
//	/**
//	 * Get count of travel request send for approval to logged in employee
//	 */
//    public function editOption($logEmpId)
//    {
//        $qryEmpCount = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empTravels, new Zend_Db_Expr('count(Request_Id)'))
//        ->where($this->_db->quoteInto('Approver_Id = ? or ', $logEmpId) . $this->_db->quoteInto('Payroll_Mid = ?', $logEmpId)));
//        return $qryEmpCount;
//    }
//	
//	/**
//	 * Get employee travels details to show in a grid
//	 */
//    public function searchEmpTravels($firstName,$lastName,$amountCon,$amounttxt,$status,$page, $rows, $sortField, $sortOrder,$travelUser,$startDateCon,$startDatetxt,$endDateCon,$endDatetxt,$formName)
//    {
//        /*//by default fill datagrid of reimbursement  */
//        switch ($sortField)
//		{
//            case 'Employee_Name':
//                $sortField = "PER.Emp_First_Name";
//                break;
//            
//			case 'Status':
//                $sortField = 'TRAV.Approval_Status';
//                break;
//            
//			case 'Total_Amount';
//				$sortField = 'Budget';
//				break;
//            
//			default:
//                $sortField = "TRAV.$sortField";
//				break;
//		}
//		
//		$empTravelQry = $this->_db->select();
//		
//		$empTravelQry->from(array('TRAV'=>$this->_ehrTables->empTravels),
//							array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS TRAV.Request_Id as Count'), 'Total_Amount'=>'Budget', 'Request_Id',
//								  'Employee_Id','Purpose', 'Approver_Id','Payroll_Mid','Added_By', 'Comment' => 0,
//								  'Status'=>new Zend_Db_Expr('(CASE WHEN TRAV.Approval_Status = \'In Process\' THEN \'Approved\' ELSE TRAV.Approval_Status END)'),
//								  'TRAV.Added_On','Log_Id'=>new Zend_Db_Expr($travelUser['LogId']),
//								  'Update'=>new Zend_Db_Expr("'".$travelUser['Update']."'"),
//								  'Op_Choice'=>new Zend_Db_Expr("'".$travelUser['Op_Choice']."'"),
//								  'User'=>new Zend_Db_Expr("'".$travelUser['Admin']."'"),
//								  'Start_Date as Mob_Start_Date', 'End_Date as Mob_End_Date',
//								  'Start_Date' => new Zend_Db_Expr("DATE_FORMAT(Start_Date,'".$this->_orgDF['sql']."')"),
//								  'End_Date' => new Zend_Db_Expr("DATE_FORMAT(End_Date,'".$this->_orgDF['sql']."')"),
//								  'Audit'=>new Zend_Db_Expr("(".$this->_db->select()->distinct()
//															->from(array('AUDITRAV'=>$this->_ehrTables->auditEmpTravels),'Request_Id')
//															->where('AUDITRAV.Request_Id = TRAV.Request_Id').")")));
//		
//		$empTravelQry->joinLeft(array('EJob'=>$this->_ehrTables->empJob), 'EJob.Employee_Id=TRAV.Employee_Id', array())
//					->joinLeft(array('Loc'=>$this->_ehrTables->location), 'Loc.Location_Id=EJob.Location_Id', array('Loc.Currency_Symbol'));
//		
//		$empTravelQry->joinInner(array('PER'=>$this->_ehrTables->empPersonal),'PER.Employee_Id = TRAV.Employee_Id',array(new Zend_Db_Expr("CONCAT(PER.Emp_First_Name, ' ', PER.Emp_Last_Name) as Employee_Name")))
//		->joinLeft(array('PER1'=>$this->_ehrTables->empPersonal),'PER1.Employee_Id = TRAV.Approver_Id',array( 'Approver_Name' => new Zend_Db_Expr("CONCAT(PER1.Emp_First_Name, ' ', PER1.Emp_Last_Name)")))
//		->joinLeft(array('PER2'=>$this->_ehrTables->empPersonal),'TRAV.Added_By = PER2.Employee_Id',array('Added_By_Name' => new Zend_Db_Expr("CONCAT(PER2.Emp_First_Name, ' ', PER2.Emp_Last_Name)")))
//		->where('PER.Form_Status = 1')
//		->group('TRAV.Request_Id');
//		
//		if (!empty($formName))
//		{
//			$formId = $this->_dbComment->getFormId($formName);
//		
//			$empTravelQry->joinLeft(array('Cm'=>$this->_ehrTables->comment),'Cm.Parent_Id=TRAV.Request_Id AND Cm.Form_Id='.$formId,
//					array('Comment' => new Zend_Db_Expr('Count(Cm.Comment_Id)')));
//		}
//	
//		($firstName != "" && preg_match('/^[a-zA-Z]/', $firstName)) ? $empTravelQry->where('PER.Emp_First_Name Like ?', "$firstName%") : $empTravelQry = $empTravelQry;
//		($lastName != "" && preg_match('/^[a-zA-Z]/', $lastName)) ? $empTravelQry->where('PER.Emp_Last_Name Like ?', "$lastName%") : $empTravelQry = $empTravelQry;
//		($amounttxt != "" && $amounttxt>=0 && preg_match('/^[0-9*\.]/', $amounttxt) && !empty($amountCon)) ? $empTravelQry->having('Total_Amount '.$amountCon.' ?', "$amounttxt") : $empTravelQry = $empTravelQry;
//		if($status != "" && preg_match('/^[a-zA-Z]/', $status))
//		{
//			if($status == 'AlertsApplied')
//			{
//				$empTravelQry->where('(TRAV.Approver_Id = ? and TRAV.Approval_Status = "Pending Approval") or (TRAV.Payroll_Mid = ? and TRAV.Approval_Status = "In Process")', $travelUser['LogId'])
//				             ->group('TRAV.Request_Id');
//				//$empTravelQry->where('TRAV.Approval_Status Like ?', "Pending Approval")
//				//             ->where('TRAV.Approver_Id = ?', $travelUser['LogId']);
//			}
//			else
//			{
//				$empTravelQry->where('TRAV.Approval_Status Like ?', "$status%");
//			}
//		}
//		else
//		{
//			$empTravelQry = $empTravelQry;
//		}
//		if($endDatetxt != "" && preg_match($this->_orgDF['regex'], $endDatetxt) && !empty($endDateCon))
//		{
//			$endDatetxt = new Zend_Date(strtotime($this->_ehrTables->changeDateformat($endDatetxt)));
//			$empTravelQry->where('End_Date '.$endDateCon.' ?', $endDatetxt->get('yyyy-MM-dd'));
//		}
//		if($startDatetxt != "" && preg_match($this->_orgDF['regex'], $startDatetxt) && !empty($startDateCon))
//		{
//	
//			$startDatetxt = new Zend_Date(strtotime($this->_ehrTables->changeDateformat($startDatetxt)));
//			$empTravelQry->where('Start_Date '.$startDateCon.' ?', $startDatetxt->get('yyyy-MM-dd'));
//		}
//		if(empty($travelUser['Admin']))
//		{
//			$qryEmployeeId = $this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))->where('Manager_Id = ?', $travelUser['LogId']);
//			$getEmployeeId = $this->_db->fetchCol($qryEmployeeId);
//			if ( $travelUser['Is_Manager'] == 1 && $travelUser['Op_Choice'] == 1  && !empty($getEmployeeId)) {
//				 
//				$empTravelQry->where('TRAV.Employee_Id = :EmpId or TRAV.Approver_Id = :EmpId or TRAV.Payroll_Mid = :EmpId or TRAV.Employee_Id IN (?)', $getEmployeeId)
//				->bind(array('EmpId'=>$travelUser['LogId']));
//			}
//			elseif ( $travelUser['Is_Manager'] == 1 && $travelUser['Op_Choice'] == 0  && !empty($getEmployeeId)) {
//				 
//	
//				$empTravelQry->where('TRAV.Employee_Id = :EmpId or TRAV.Approver_Id = :EmpId or TRAV.Employee_Id IN (?)', $getEmployeeId)
//				->bind(array('EmpId'=>$travelUser['LogId']));
//				 
//				 
//			}
//			elseif (  $travelUser['Is_Manager'] == 0 && $travelUser['Op_Choice'] == 1 )
//			{
//				$empTravelQry->where('TRAV.Employee_Id = ? or TRAV.Payroll_Mid = ?', $travelUser['LogId']);
//				 
//			}
//			else {
//				$empTravelQry->where('TRAV.Employee_Id = ?', $travelUser['LogId']);
//			}
//		}
//		$empTravelQry->order("$sortField $sortOrder")->limit($rows,($page-1)*$rows);
//		$result = $this->_db->fetchAll($empTravelQry);
//		
//		$countTravel = $this->_db->fetchOne('select FOUND_ROWS()');
//	
//		$arrTravel = array("total"=>$countTravel,"rows"=>$result);
//	
//		return Zend_Json::encode($arrTravel);
//     
//    }
//	
//	/**
//	 * Get travel details by id to show in a subgrid
//	 */
//    public function travelSubGrid($requestId, $sort, $order, $isMobile = null, $page = null, $rows = null, $isView = null)
//    {
//        $qryRequest = $this->_db->select();
//		
//        if ($isMobile)
//		{
//			$qryRequest->from($this->_ehrTables->empTravels,
//							  array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS Itinerary_Id as Count'),
//									'Itinerary_Id','Place','Mode'=>'Travel_Mode','Arrange'));
//		}
//		else
//		{
//			$qryRequest->from($this->_ehrTables->empTravels,
//							  array('Itinerary_Id','Place','Mode'=>'Travel_Mode','Arrange'));
//		}
//		
//		$qryRequest->where('Request_Id = ?',$requestId)
//					->order("$sort $order");
//		
//		if ($isMobile && !$isView)
//		{
//			$qryRequest->limit($rows,($page-1)*$rows);
//		}
//		
//		$rowRequest = $this->_db->fetchAll($qryRequest);
//		
//        $countTravel = $this->_db->fetchOne('select FOUND_ROWS()');
//		
//        return Zend_Json::encode(array("total"=>$countTravel,"rows"=>$rowRequest));
//    }
//	
//	/**
//	 * Get max requestId
//	 */
//    public function maxRequestId()
//    {
//        $qryRequest = $this->_db->select()->from($this->_ehrTables->empTravels, array(new Zend_Db_Expr('Max(Request_Id) as MaxId')));
//        $rowRequest = $this->_db->fetchOne($qryRequest);
//        return $rowRequest;
//    }
//	

//    /*
//     * get the last inserted Itinerary_Id
//     * $return int maximum value of Itinerary_Id
//     */
//    public function maxItineraryId()
//    {
//         
//        $maxIdReqQry = $this->_db->select()->from($this->_ehrTables->empTravels, array(new Zend_Db_Expr('Max(Itinerary_Id) as MaxId')));
//        $maxId = $this->_db->fetchOne($maxIdReqQry);
//        return $maxId;
//    }
//	
//	/**
//	 * Get audit count by travelId
//	 */
//    public function auditTravel($requestId)
//    {
//        $qryAudit = $this->_db->select()->from($this->_ehrTables->auditEmpTravels, new Zend_Db_Expr('count(Audit_Id)'))
//        ->where('Request_Id = ?', $requestId);
//        $rowAudit = $this->_db->fetchOne($qryAudit);
//        return $rowAudit;
//    }
//	
//    /*
//     * insert and Update multiple record at one insert query
//    * @param array $travInseDataArr multidimentional Associative array to insert
//    * @return array
//    *
//    */
//    public function addTravels($travInseDataArr, $comment, $requestId, $logEmpId, $formName, $status)
//    {
//         
//        $travelsInseRes = $this->_ehrTables->insertMultiple($this->_ehrTables->empTravels,$travInseDataArr);
//        $firstRw = current($travInseDataArr);
//        if($travelsInseRes)
//        {
//            $returnArr = $this->_db->fetchRow($this->_db->select()
//            ->from(array('TRAV'=>$this->_ehrTables->empTravels),array( 'Employee_Id','Approver_Id','Added_By', 'Purpose'))
//            ->joinInner(array('PER'=>$this->_ehrTables->empPersonal),'TRAV.Employee_Id = PER.Employee_Id',array('employeeName' => new Zend_Db_Expr("CONCAT(PER.Emp_First_Name, ' ', PER.Emp_Last_Name)")))
//            ->joinInner(array('PER1'=>$this->_ehrTables->empPersonal),'TRAV.Approver_Id = PER1.Employee_Id',array('approverName' => new Zend_Db_Expr("CONCAT(PER1.Emp_First_Name, ' ', PER1.Emp_Last_Name)")))
//            ->joinInner(array('PER2'=>$this->_ehrTables->empPersonal), 'TRAV.Added_By = PER2.Employee_Id',array('addedByName' => new Zend_Db_Expr("CONCAT(PER2.Emp_First_Name, ' ', PER2.Emp_Last_Name)")))
//            ->joinLeft(array('PER3'=>$this->_ehrTables->empPersonal), 'TRAV.Payroll_Mid = PER3.Employee_Id',array('payrollName' => new Zend_Db_Expr("CONCAT(PER3.Emp_First_Name, ' ', PER3.Emp_Last_Name)")))
//            ->where('TRAV.Request_Id = ?', $firstRw['Request_Id']));
//        }
//        if ( ! empty($comment) ) {
//            $formId = $this->_dbComment->getFormId($formName);
//            $addComment = array('Form_Id'=>$formId, 'Emp_Comment'=>$comment, 'Approval_Status'=>$status,'Parent_Id'=>$requestId, 'Employee_Id'=>$logEmpId, 'Added_On'=>date('Y-m-d H:i:s'));
//            $this->_db->insert($this->_ehrTables->comment, $addComment);
//        }
//        if($travelsInseRes)
//        {
//            $this->_ehrTables->trackEmpSystemAction('Add Employee Travels - '.$returnArr['Purpose'], $logEmpId);
//            return $returnArr;
//        }
//        else
//        return null;
//    }
//	
//	/**
//	 * Delete travel request
//	 */
//    public function travelsRequestDelete($requestId)
//    {
//         
//        $deleteTravels = $this->_db->delete($this->_ehrTables->empTravels,'Request_Id = '.(int)$requestId);
//        return ($deleteTravels) ? true : false;
//    }
//    
//	/*
//     * before insert checking each row of posted row is already exist or not
//    * @param array $travInseDataArr multidimentional Associative array to insert
//    * @return int r
//    *
//    */
//    public function isTravelExist($travInseDataArr)
//    {
//		foreach ($travInseDataArr as $row)
//        {
//            $existQry = $this->_db->select()->from($this->_ehrTables->empTravels, 'Request_Id')
//            ->where('Employee_Id = ?', $row['Employee_Id'])
//            ->where('Purpose = ?', $row['Purpose'])
//            ->where('Start_Date = ?', $row['Start_Date'])
//            ->where('End_Date = ?', $row['End_Date'])
//            ->where('Budget = ?', $row['Budget'])
//            ->where('Place = ?', $row['Place'])
//            ->where('Travel_Mode = ?', $row['Travel_Mode'])
//            ->where('Arrange = ?', $row['Arrange'])
//            ->where('Request_Id != ?', $row['Request_Id']);
//            $exist = $this->_db->fetchOne($existQry);
//            if($exist > 0)
//            return $exist;
//        }
//        return $exist;
//    }
	
	/**
	 * Check whether travel exists or not for given date range to an employee
	 */
    public function isTravelDateExist($empId, $startDate, $endDate, $travId = null)
    {
        
    	$statusConditions = $this->_db->quoteInto('T.Approval_Status != ? ', "Rejected");
    	$dateConditions = $this->_db->quoteInto(new Zend_Db_Expr(' ? BETWEEN Start_Date AND End_Date'), $startDate).' OR '.
                          $this->_db->quoteInto(new Zend_Db_Expr(' ? BETWEEN Start_Date AND End_Date'), $endDate);
    	
        
        
        $qryTravelsCnt = $this->_db->select()->from(array('T'=>$this->_ehrTables->empTravels), array('Count(Request_Id)'))
                                            ->where('Employee_Id = ?', $empId)
                                            ->where($statusConditions)
                                            ->where($dateConditions);
        
    	if($travId != null)
    	{
    		$qryTravelsCnt->where('Request_Id != ?', $travId);
    	}
		
    	return $this->_db->fetchOne($qryTravelsCnt);
    	
    }

	public function __destruct()
    {
        
    }	
}

