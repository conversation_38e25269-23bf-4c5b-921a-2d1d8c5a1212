/*==================================================================================================*/
/* Program        : insurance.js																	*/
/* Property of Caprice Technologies Pvt Ltd,
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,
 * Coimbatore, Tamilnadu, India.												                    */
/* All Rights Reserved.            																	*/
/* Use of this material without the express consent of Caprice Technologies
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law.			*/
/*                                                                                    				*/
/* Description    :jquery for insurance																*/
/*                                                                                   				*/
/*                                                                                    				*/
/*Revisions      :                                                                    				*/
/*Version    Date           Author                  Description                       				*/
/*0.1        30-Mar-2016    Deepak    			    Initial Version         						*/
/*                                                                                    				*/
/*                                                                                    				*/
/*                                                                                    				*/
/*==================================================================================================*/
$(function () {
    $.fn.dataTable.ext.errMode = 'none';
    
    var currentDate = new Date(tzDate());
    var monthArray  = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    
    /* Alert when the DataSetup is not completed */
    dataSetup('datasetup');
    
    var isDirtyFormFixedInsurance    = false;
    var isDirtyFormVariableInsurance = false;
    var isDirtyFormInsuranceType     = false;
    var isDirtyFormInsuranceGrade    = false;
    var isDirtyFormPaymentTracker    = false;
    
    $.validator.addMethod("checkPastDate", function (value, element) {
        if (Number($('#formPaymentTrackerId').val()) == 0 && (Date.parse(new Date(new Date(tzDate()).setHours(0, 0, 0, 0))) > Date.parse(new Date(new Date(value).setHours(0, 0, 0, 0))))) {
            return false;
        }
        
        return true;
    });
    
    /*************************************** Fixed Insurance ************************************************************************/
    
    /*  Initialse DataTables, with no sorting on the 'details' column  */
    var tableFixedInsurance = $('#tableFixedInsurance').dataTable({
       "iDisplayLength" : 10,
       "lengthMenu"     : [ 5, 10, 25, 50, 100 ], 
       "bDestroy"       : true,
       "bAutoWidth"     : false,
       "bServerSide"    : true,
       "bDeferRender"   : true,
       "sServerMethod"  : 'POST',
       "sAjaxSource"    : pageUrl () + 'payroll/insurance/list-fixed-insurance',
       "sAjaxDataProp"  : 'aaData',
       //"aaSorting"      : [[1, 'asc']],
        "aaSorting"      : [],
        "aoColumnDefs"   : [{"targets": 0, "orderable": false},
                            { "sClass" : "visible-xs visible-sm  visible-md hidden-lg", "aTargets" : [0] },
                            { "sClass" : "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets" : [1] },
                            { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [4] },
                            { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [5] },
                            { "sClass" : "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets" : [6] },
                            { "sClass" : "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets" : [7] },
                            { "sClass" : "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets" : [8] }],
       "fnCreatedRow": function( nRow, aData, iDataIndex ) {
            $(nRow).attr({"data-toggle":"context", "data-target":"#fixedInsurance-context-menu" });
        },
       "aoColumns"      : [{
            "mData" : function (row, type, set) {
                return '<i class="fa fa-plus-square-o"></i>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div >'+ fnCheckNull(row['User_Defined_EmpId']) +'</div>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div>'+ row['Coverage'] +'</div>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div >'+ fnCheckNull(row['Employee_Name']) +'</div>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div style="word-break: break-all;" >'+ row['Insurance_Name'] +'</div>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div >'+ row['Insurance_Amount'] +'</div>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div>'+ row['Duration'] +'</div>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div >'+ row['Payment_Frequency'] +'</div>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div>'+ row['Premium'] +'</div>';
            }
        }]
    });
    
    //On + icon click in mobile & tablet view
    $(document).on('click', '#tableFixedInsurance i', function () {
        var nTr = $(this).parents('tr')[0];
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        if ( tableFixedInsurance.fnIsOpen(nTr) )
        {
            /* This row is already open - close it */
            $(this).removeClass().addClass('fa fa-plus-square-o');
            tableFixedInsurance.fnClose(nTr);
        }
        else
        {
            var record = tableFixedInsurance.fnGetData( nTr );
            var nRow =  $('#tableFixedInsurance thead tr')[0];
            
            /* Open this row */
            $(this).removeClass().addClass('fa fa-minus-square-o');
            
            valueArray = [];
            headerArray=[];
            
            valueArray.Value_One = record.Insurance_Name;
            valueArray.Value_Two = record.Insurance_Amount;
            valueArray.Value_Three = record.Duration;
            valueArray.Value_Four = record.Payment_Frequency;
            valueArray.Value_Five = record.Premium;
            
            //get grid headers
            gridHeader(nRow.cells);
            
            tableFixedInsurance.fnOpen(nTr, fnDeviceColumnDetails(headerArray,valueArray), 'details hidden-lg');
        }
    });
    
    /*  Add event listener for select and unselect details  */
    $(document).on('click contextmenu', '#tableFixedInsurance tbody td div', function () {
        var selectRow = $(this).parent().parent();
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        tableFixedInsurance.$('tr.row_selected').removeClass('row_selected');
        
        if (!selectRow.hasClass('row_selected'))
        {
            selectRow.addClass('row_selected');
            
            fnActionButtonsFixedInsurance (tableFixedInsurance.fnGetData (fnGetSelected ( tableFixedInsurance )[0]), true);
        }
        else
        {
            fnActionButtonsFixedInsurance ('',false);
        }
    });
    
    /**
     *  Grid Refresh
    */
    $('#gridPanelFixedInsurance .panel-reload').on('click', function () {
        fnRefreshTable (tableFixedInsurance);
    });
    
    // On whole grid filter event (to disable action icon)
    $('#tableFixedInsurance').on( 'draw.dt', function () {
        fnActionButtonsFixedInsurance ('',false);
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
    });
    
    /**
     * Department wise segregation
    */ 

   $('#formDivisionDetails').on('change', function () {
    var formDivisionId = $('#s2id_formDivisionDetails').select2('val');
    hrappDepartmentClassification(formDivisionId, tableFixedInsurance,tableVariableInsurance);
    });

    //Add Fixed Insurance
    $('#addFixedInsurance').on('click', function () {
        tableFixedInsurance.$('tr.row_selected').removeClass('row_selected');
      
        fnActionButtonsFixedInsurance ('',false);
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        $('#modalFormFixedInsurance .modal-title').html("<strong>Add<strong> "+$('#lblFormNameF').html());
        
        fnPreFillFormValuesFixedInsurance ('');
        
        $('#viewFormFixedInsurance, #editInViewFixedInsurance').hide();
        $('#editFormFixedInsurance, #formActionFixedInsurance').show();
    });
    
    //View FixedInsurance
    $('#viewFixedInsurance, #viewContextFixedInsurance').on('click', function () {
        var selectedRow = fnGetSelected ( tableFixedInsurance );
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        if (selectedRow.length)
        {
            var record = tableFixedInsurance.fnGetData (selectedRow[0]);
            if (record.FixedInsurance_Id > 0)
            {
                $('#modalFormFixedInsurance .modal-title').html("<strong>View<strong> "+$('#lblFormNameF').html());
                $('#viewFICoverage').text(record.Coverage);
                
                if (record.Coverage == 'Organization')
                {
                    $('.vCoverageEmployee').hide();
                }
                else
                {
                    $('.vCoverageEmployee').show();
                    
                    $('#viewFIEmployeeName').text(record.Employee_Name);
                    $('#viewFIStartMonth').text(record.Insurance_Start_Date);
                }
                
                $('#viewFIInsuranceType').text( record.Insurance_Name );
                $('#viewFIPaymentFrequency').text(record.Payment_Frequency);
                $('#viewFIInsuranceDuration').text(record.Duration);
                $('#viewFIInsuranceAmount').text(record.Insurance_Amount);
                $('#viewFIPremium').text(record.Premium);
                $('#viewFIDescription').text( fnCheckNull(record.Description));
                
                /**
                 *  Prefill addional information values based on record with form name
                */
                fnPreFillAdditionalPanel ('FixedInsurance', record);
                
                $('#modalFormFixedInsurance').modal('toggle');
                $('#editFormFixedInsurance, #formActionFixedInsurance').hide();
                $('#viewFormFixedInsurance, #editInViewFixedInsurance').show();
            }
            else
            {
                jAlert ({ msg : 'Kindly select fixed insurance', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select fixed insurance', type : 'info' });
        }
    });
    
    //Edit Fixed Insurance
    $('#editFixedInsurance, #editContextFixedInsurance, #editInViewFixedInsurance').on('click', function () {
        var selectedRow = fnGetSelected (tableFixedInsurance );
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        if (selectedRow.length)
        {
            var record = tableFixedInsurance.fnGetData (selectedRow[0]);
                fixedInsuranceId = record.FixedInsurance_Id;
                
            if (fixedInsuranceId > 0 && !isNaN(fixedInsuranceId))
            {
                var buttonId = $(this).prop('id');
                
                setLock ({
                    'formName' : 'Fixed Insurance',
                    'uniqueId' : fixedInsuranceId,
                    'callback' : function (result)
                    {
                        if (buttonId != 'editInViewFixedInsurance')
                            $('#modalFormFixedInsurance').modal('toggle');
                        
                        $('#modalFormFixedInsurance .modal-title').html("<strong>Edit<strong> "+$('#lblFormNameF').html());
                        
                        fnPreFillFormValuesFixedInsurance (record);
                        
                        $('#editInViewFixedInsurance, #viewFormFixedInsurance').hide();
                        $('#editFormFixedInsurance, #formActionFixedInsurance').show();
                    }
                });
            }
            else
            {
                jAlert ({ msg : 'Kindly select fixed insurance', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select fixed insurance', type : 'info' });
        }
    });
    
    //Trigger delete confirmation popup in delete menu in context menu
    $('#deleteFixedInsurance, #deleteContextFixedInsurance').on('click', function () {
        var selectedRow = fnGetSelected (tableFixedInsurance );
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        if (selectedRow.length)
        {
            var fixedInsuranceId = tableFixedInsurance.fnGetData (selectedRow[0]).FixedInsurance_Id;
            
            if (fixedInsuranceId > 0 && !isNaN(fixedInsuranceId))
            {
                $('#modalDeleteFixedInsurance').modal('toggle');
            }
            else
            {
                jAlert ({ msg : 'Kindly select fixed insurance', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select fixed insurance', type : 'info' });
        }
    });
    
    //Delete FixedInsurance
    $('#deleteConfirmFixedInsurance').on('click', function () {
        var selectedRow = fnGetSelected (tableFixedInsurance );
        
        if (selectedRow.length)
        {
            var fixedInsuranceId = tableFixedInsurance.fnGetData (selectedRow[0]).FixedInsurance_Id;
            
            if (fixedInsuranceId > 0 && !isNaN(fixedInsuranceId))
            {
                $.ajax ({
                    type     : 'POST',
                    dataType : 'json',
                    async    : false,
                    url      : pageUrl () +'payroll/insurance/delete-fixed-insurance/fixedInsuranceId/'+ fixedInsuranceId,
                    success  : function (result)
                    {
                        if (isJson (result))
                        {
                            if (result.success)
                            {
                                fnRefreshTable (tableFixedInsurance);
                            }
                            
                            jAlert ({ msg : result.msg, type : result.type });
                        }
                        else
                        {
                            sessionExpired ();
                        }
                    }
                });
            }
            else
            {
                jAlert ({ msg : 'Kindly select fixed insurance', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select fixed insurance', type : 'info' });
        }
    });
    
    // On form field change
    $('#editFormFixedInsurance').on('change', function() {
        isDirtyFormFixedInsurance = true;
    });
    
    //Form reset - FixedInsurance
    $('#formResetFixedInsurance').on('click', function () {
        var l = Ladda.create(this);
        var selectedRow = fnGetSelected (tableFixedInsurance );
        l.start();
        
        if (selectedRow.length == 1)
        {
            fnPreFillFormValuesFixedInsurance (tableFixedInsurance.fnGetData (fnGetSelected (tableFixedInsurance)[0]));
        }
        else
        {
            fnPreFillFormValuesFixedInsurance ('');
        }
        
        l.stop();
    });
    
    //Form submit - FixedInsurance
    $('#formSubmitFixedInsurance').on('click', function () {
        var l = Ladda.create(this);
        
        l.start();
        $('#s2id_editformFICoverage, #s2id_editformFIEmployeeName, #s2id_editformFIInsuranceType, #s2id_editformFIPaymentFrequency, #s2id_editformFIInsuranceDuration').removeClass('form-error');
        
        if (isDirtyFormFixedInsurance)
        {
            if ($("#editFormFixedInsurance").valid())
            {
                if ($('#s2id_editformFICoverage').select2('val') == 1)
                {
                    startMonth = fnServerDateFormatter (new Date($('#editformFIStartMonth').html()));
                }
                else
                {
                    startMonth = "NULL";
                }
                
                $.ajax ({
                    type     : 'POST',
                    dataType : 'json',
        //            async    : false,
                    url      : pageUrl () +'payroll/insurance/update-fixedinsurance',
                    data     : {
                        fixedInsuranceId    : $('#formFixedInsuranceId').val(),
                        coverage            : $('#s2id_editformFICoverage').select2('val'),
                        employeeId          : $('#s2id_editformFIEmployeeName').select2('val'),
                        insuranceStartDate  : startMonth,
                        insuranceTypeId     : $('#s2id_editformFIInsuranceType').select2('val'),
                        paymentModeId       : $('#s2id_editformFIPaymentFrequency').select2('val'),
                        insuranceDurationId : $('#s2id_editformFIInsuranceDuration').select2('val'),
                        insuranceAmount     : $('#editformFIInsuranceAmount').val(),
                        premium             : $('#editformFIPremium').val(),
                        description         : $('#editformFIDescription').val()
                    },
                    success  : function (result)
                    {
                        if (isJson (result))
                        {
                            if (result.success)
                            {
                                isDirtyFormFixedInsurance = false;
                                
                                $('#modalFormFixedInsurance').modal('toggle');
                                
                                fnRefreshTable (tableFixedInsurance);
                                
                                jAlert ({ msg : result.msg, type : result.type });
                            }
                            else
                            {
                                jAlert ({ panel : $('#editFormFixedInsurance'), msg : result.msg, type : result.type });
                            }
                        }
                        else
                        {
                            sessionExpired ();
                        }
                        
                        l.stop();
                    }
                });    
            }
            else
            {
                l.stop();
            }
        }
        else
        {
            l.stop();
            jAlert ({ panel : $('#editFormFixedInsurance'), msg : 'Form has no changes', type : 'info' });
        }
    });
        
    /**
     *  Show history details for selection record
    */
    $('#historyFixedInsurance, #historyContextFixedInsurance').on('click', function(){
        var selectedRow = fnGetSelected ( tableFixedInsurance );
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        if (selectedRow.length)
        {
            var record = tableFixedInsurance.fnGetData (selectedRow[0]);
            
            if (record.FixedInsurance_Id > 0)
            {
                $('#modalHistoryFixedInsurance').modal('toggle');
                
                tableFixedInsuranceHistory = $('#tableFixedInsuranceHistory').dataTable ({
                    "bPaginate"      : false,
                    "bLengthChange"  : false,
                    "bFilter"        : false,
                    "bDestroy"       : true,
                    "bAutoWidth"     : false,
                    "bServerSide"    : true,
                    "bDeferRender"   : true,
                    "sServerMethod"  : "POST",
                    "sAjaxSource"    : pageUrl ()+'payroll/insurance/list-fixed-insurance-history/fixedInsuranceId/'+record.FixedInsurance_Id,
                    "sAjaxDataProp"  : "aaData",
                    "aaSorting"      : [],
                    "aoColumnDefs"   : [{ 'bSortable': false, 'aTargets': ['_all'] },
                                        { "sClass" : "visible-xs visible-sm  visible-md hidden-lg", "aTargets" : [0] },
                                        { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [3] },
                                        { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [4] },
                                        { "sClass" : "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets" : [5] },
                                        { "sClass" : "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets" : [6] }],
                    "aoColumns"      : [{
                        "mData" : function (row, type, set) {
                            return '<i class="fa fa-plus-square-o"></i>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            return '<div>'+ row['Insurance_Amount_Old'] +'</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            return '<div>'+ row['Insurance_Amount_New'] +'</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            return '<div>'+ row['PaymentMode_Old'] +'</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            return '<div>'+ row['PaymentMode_New'] +'</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            return '<div>'+ row['Modified_On'] +'</div>';
                        }
                    },
                    {
                        "mData" : function (row, type, set) {
                            return '<div>'+ row['ChangedBy'] +'</div>';
                        }
                    }]
                });
            }
            else
            {
                jAlert ({ msg : 'Kindly select fixed insurance', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select fixed insurance', type : 'info' });
        }
    });
    
    //On + icon click in mobile & tablet view
    $(document).on('click', '#tableFixedInsuranceHistory i', function () {
        var nTr = $(this).parents('tr')[0];
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        if ( tableFixedInsuranceHistory.fnIsOpen(nTr) )
        {
            /* This row is already open - close it */
            $(this).removeClass().addClass('fa fa-plus-square-o');
            tableFixedInsuranceHistory.fnClose(nTr);
        }
        else
        {
            var record = tableFixedInsuranceHistory.fnGetData( nTr );
            var nRow =  $('#tableFixedInsuranceHistory thead tr')[0];
            
            /* Open this row */
            $(this).removeClass().addClass('fa fa-minus-square-o');
            
            valueArray = [];
            headerArray=[];
        
            valueArray.Value_One = record.PaymentMode_Old;
            valueArray.Value_Two = record.PaymentMode_New;
            valueArray.Value_Three = record.Modified_On;
            valueArray.Value_Four = record.ChangedBy;
            
            $.each(nRow.cells, function(i,v) {
                headerArray['Header'+i] = v.innerText;
            });
            
            tableFixedInsuranceHistory.fnOpen(nTr, fnDeviceColumnDetails(headerArray,valueArray), 'details hidden-lg');
        }
    });
    
    /**
     *  Add,Edit form modal hide event
    */
    $('#modalFormFixedInsurance').on('hide.bs.modal', function (e) {
        if (isDirtyFormFixedInsurance)
        {
            e.preventDefault();
            e.stopImmediatePropagation();
           
            $('#modalDirtyFixedInsurance').modal('toggle');
        }
        else
        {
            fnCloseFormFixedInsurance (false);
        }
    });
    
    /**
     *  click to close add/edit modal
    */
    $('#editCloseConfirmFixedInsurance').on('click', function () {
        isDirtyFormFixedInsurance = false;
        
        fnCloseFormFixedInsurance (true);
    });
    
    // On fixed insurance coverage value change
    $('#editformFICoverage').on('change', function(){
        var coverageVal = $(this).val();
        
        $('#s2id_editformFIEmployeeName').select2('val','');
        
        if( coverageVal == 0 )
        {
            $('#editformFIEmployeeName').removeClass('vRequired');
            $('.coverageBasedHidden').hide();
            
            $('#editformFIStartMonth').html('');
        }
        else
        {
            fnGetEmployeeName('Insurance','#editformFIEmployeeName');
            $('#editformFIEmployeeName').addClass('vRequired');
            
            $('#editformFIStartMonth').html(monthArray[currentDate.getMonth()] + ", " + currentDate.getFullYear());
            
            $('.coverageBasedHidden').show();
        }
        
        $.ajax ({
            type     : 'POST',
            dataType : 'json',
            async    : false,
            url      : pageUrl () +'payroll/insurance/get-insurancetypes',
            data     : {
                mode     : 'F',
                coverage : coverageVal
            },
            success  : function (result)
            {
                if (isJson (result))
                {
                    var insuranceTypeField  = $('#editformFIInsuranceType');
                    var insuranceTypeOption = ['<option value="">--Select--</option>'];
                    
                    insuranceTypeField.find('option').remove();
                    
                    for (var i in result)
                    {
                        insuranceTypeOption.push('<option value=' + i + '>'+ result[i] +'</option>');
                    }
                    
                    insuranceTypeField.append(insuranceTypeOption);
                    insuranceTypeField.select2('val','');
                }
                else
                {
                    sessionExpired ();
                }
            }
        });
        
        // To check fixed insurance already exist
        fnCheckFixedInsuranceExists();
    });
    
    // On fixed insurance type change
    $('#editformFIInsuranceType').on('change', function(){
        fnCheckFixedInsuranceExists();
        fnFixedEmpOrgShare();
    });
    
    // On fixed insurance employee value change
    $('#editformFIEmployeeName').on('change', function(){
        
        var employeeId = $(this).select2('val');
        
        if (employeeId > 0)
        {
            $.ajax ({
                type     : 'POST',
                dataType : 'json',
                async    : false,
                url      : pageUrl () +'payroll/insurance/get-insurancetypes',
                data     : {
                    mode     : 'F',
                    coverage : 1,
                    employeeId : employeeId
                },
                success  : function (result)
                {
                    if (isJson (result))
                    {
                        var insuranceTypeField  = $('#editformFIInsuranceType');
                        var insuranceTypeOption = ['<option value="">--Select--</option>'];
                        
                        insuranceTypeField.find('option').remove();
                        
                        for (var i in result)
                        {
                            insuranceTypeOption.push('<option value=' + i + '>'+ result[i] +'</option>');
                        }
                        
                        insuranceTypeField.append(insuranceTypeOption);
                    }
                    else
                    {
                        sessionExpired ();
                    }
                }
            });
        }
    });
    
    // Setting value to preimum on duration, payment mode
    $("#editformFIInsuranceDuration,#editformFIPaymentFrequency").on('change keyup',function(e) {							
		fnCalculatePremiumVal();
	});
    
    // On insurance amount change
    $('#editformFIInsuranceAmount').on('change', function(){
        fnCalculatePremiumVal();
        fnFixedEmpOrgShare();
    });
    
    /** Filter Form **/
    $('#filterFixedInsurance,#closeFilterFixedInsurance').on('click', function(){
        if ($('#filterPanelFixedInsurance').hasClass('open')){
            $('#filterPanelFixedInsurance').removeClass('open');
            $('#filterPanelFixedInsurance').hide();
        }
        else{
            $('#filterPanelFixedInsurance').addClass('open');
            $('#filterPanelFixedInsurance').show();
        }
    });
    
    /**
     *  Reset Filter Form
    */
    $('#filterResetFixedInsurance,#closeFilterFixedInsurance').on('click', function () {
        $('#filterEmployeeName, #filterInsAmountBegin, #filterInsAmountEnd, #filterInsDurationBegin, #filterInsDurationEnd, '+
          '#filterInsPremiumBegin, #filterInsPremiumEnd').val('');
        
        $('#s2id_filterInsuranceTypeId, #s2id_filterInsPaymentFrequency').select2('val', '');
       
        tableFixedInsurance.fnReloadAjax( pageUrl () + "payroll/insurance/list-fixed-insurance" );
    });
    
    /**
     *  Apply filter to fixed insurance grid
    */
    $('#filterApplyFixedInsurance').on('click', function () {
        ftEmployeeName        = $('#filterEmployeeName').val();
        ftInsType             = $('#s2id_filterInsuranceTypeId').select2('val');
        ftInsAmountBegin      = $('#filterInsAmountBegin').val();
        ftInsAmountEnd        = $('#filterInsAmountEnd').val();
        ftInsDurationBegin    = $('#filterInsDurationBegin').val();
        ftInsDurationEnd      = $('#filterInsDurationEnd').val();
        ftInsPremiumBegin     = $('#filterInsPremiumBegin').val();
        ftInsPremiumEnd       = $('#filterInsPremiumEnd').val();
        ftInsPaymentFrequency = $('#s2id_filterInsPaymentFrequency').select2('val');
        
        tableFixedInsurance.fnReloadAjax(pageUrl () +'payroll/insurance/list-fixed-insurance/sSearch_0/'+ ftEmployeeName
                                     +'/sSearch_1/'+ ftInsType +'/sSearch_2/'+ ftInsAmountBegin +'/sSearch_3/'+ ftInsAmountEnd
                                     +'/sSearch_4/'+ ftInsDurationBegin +'/sSearch_5/'+ ftInsDurationEnd
                                     +'/sSearch_6/'+ ftInsPremiumBegin +'/sSearch_7/'+ ftInsPremiumEnd
                                     +'/sSearch_8/'+ ftInsPaymentFrequency);
    });
    
    /**
     *  When form isn't dirty then clear the lock flag and then close that add/edit form
    */
    function fnCloseFormFixedInsurance (hideAction) {
        var fixedInsuranceId = $('#formFixedInsuranceId').val();
        
        if (fixedInsuranceId > 0 && !isNaN(fixedInsuranceId))
        {
            clearLock ({
                'formName' : 'Fixed Insurance',
                'uniqueId' : fixedInsuranceId,
                'callback' : function ()
                {
                    $('#formFixedInsuranceId').val(0);
                    
                    if (hideAction)
                        $('#modalFormFixedInsurance').modal('hide');
                }
            });
        }
        else
        {
            if (hideAction)
                $('#modalFormFixedInsurance').modal('hide');
        }
    }
    
    /**
     *  Prefill fixed insurance form values in add, edit, reset events
    */
    function fnPreFillFormValuesFixedInsurance (record) {
        $('#s2id_editformFICoverage, #s2id_editformFIEmployeeName, #s2id_editformFIInsuranceType, #s2id_editformFIPaymentFrequency, #s2id_editformFIInsuranceDuration').removeClass('form-error');
        
        if (record != '')
        {
            $('#s2id_editformFICoverage').select2('val', record.Fixed_Insurance_Coverage);
            
            $('#editformFICoverage').trigger('change');
            
            $('#editformFICoverage').prop('readOnly', true);
            
            $('#formFixedInsuranceId').val(record.FixedInsurance_Id);
            
            if (record.Fixed_Insurance_Coverage == 1)
            {
                $('#s2id_editformFIEmployeeName').select2('val', record.Employee_Id);
                $('#editformFIStartMonth').html(record.Insurance_Start_Date);
                
                $('#editformFIEmployeeName').prop('readOnly', true);
            }
            
            $('#s2id_editformFIInsuranceType').select2('val', record.InsuranceType_Id);
            $('#s2id_editformFIPaymentFrequency').select2('val', record.PaymentMode_Id);
            $('#s2id_editformFIInsuranceDuration').select2('val', record.Duration);
            
            $('#editformFIInsuranceAmount').val(record.Insurance_Amount);
            $('#editformFIPremium').val(record.Premium);
            $('#editformFIDescription').val(record.Description);
        }
        else
        {
            $('#editformFICoverage, #editformFIEmployeeName').prop('readOnly', false);
            $('#formFixedInsuranceReset').trigger('click');
            
            $('#formFixedInsuranceId').val(0);
            
            $('#s2id_editformFICoverage').select2('val', 0);
            $('#s2id_editformFIPaymentFrequency').select2('val', 12);
            
            $('#editformFICoverage').trigger('change');
            
            $('#s2id_editformFIInsuranceType, #s2id_editformFIInsuranceDuration').select2('val', '');
        }
        
        $( "#editFormFixedInsurance").validate().resetForm();
        
        isDirtyFormFixedInsurance = false;
    }
    
    //Check whether fixed insurance is exist. If exist fill the value else empty other field value
    function fnCheckFixedInsuranceExists() 
    {
        var fixedInsuranceId = $('#formFixedInsuranceId').val();
        var coverageId       = $("#editformFICoverage").select2('val');
        var insurancetypeId  = $("#editformFIInsuranceType").select2('val');
        var employeeId       = $("#editformFIEmployeeName").select2('val');
        
        $.ajax({
            type     : "POST",
            async    : false,
            datatype :'json',
            url      : pageUrl()+"payroll/insurance/check-fixedinsurance",
            data     : {
                'empId'   : employeeId,
                'typeId'  : insurancetypeId,
                'fixedId' : fixedInsuranceId,
                'coverage': coverageId
            },
            success  : function(value) 
            {
                value = JSON.parse(value);
            
                if (value!=[] && value!=null)
                {
                    if($.inArray(value[0], ['a', 'n']) != -1)
                    {
                        $('#formFixedInsuranceId').val(0);
                    }
                    
                    if(value[0] == 'n1' && value[1]!=[] && value[1]!='null')
                    {
                        fixedInsuranceId = $('#formFixedInsuranceId').val();
                        
                        if (fixedInsuranceId > 0 && !isNaN(fixedInsuranceId))
                        {
                            clearLock ({
                                'formName' : 'Fixed Insurance',
                                'uniqueId' : fixedInsuranceId,
                                'callback' : function ()
                                {
                                    fnSetFixedInsuranceVal(value[1]);
                                }
                            });
                        }
                        else
                        {
                            fnSetFixedInsuranceVal(value[1]);    
                        }
                    }
                    
                    if(value[0] == 'n2' && value[1]!=[] && value[1]!='null')
                    {
                        fixedInsuranceId = $('#formFixedInsuranceId').val();
                        
                        if (fixedInsuranceId > 0 && !isNaN(fixedInsuranceId))
                        {
                            clearLock ({
                                'formName' : 'Fixed Insurance',
                                'uniqueId' : fixedInsuranceId,
                                'callback' : function ()
                                {
                                    fnSetFixedInsuranceVal(value[1]);
                                }
                            });
                        }
                        else
                        {
                            fnSetFixedInsuranceVal(value[1]);    
                        }
                    }
                    else if (value[0] == 'm')
                    {
                        jAlert ({ panel : $('#editFormFixedInsurance'), msg : value[1], type : 'warning' });
                        
                        $('#formFixedInsuranceId').val(0);
                        $('#editformFIInsuranceType, #editformFIEmployeeName, #editformFIInsuranceDuration, #editformFIPaymentFrequency').select2('val', '');
                        $('#editformFIStartMonth').html('');
                        $('#editformFIInsuranceAmount, #editformFIPremium, #editformFIDescription').val('');
                    }
                }
                else
                {
                    $('#formFixedInsuranceId').val(0);
                    $('#editformFIStartMonth').html('');
                    $('#editformFIInsuranceDuration, #editformFIPaymentFrequency').select2('val', '');
                    $('#editformFIInsuranceAmount, #editformFIPremium, #editformFIDescription').val('');
                }
            }
        });
    }
    
    //Set value if it already exists
    function fnSetFixedInsuranceVal(record)
    {
        if (record != '')
        {
            //setLock ({
            //    'formName' : 'Fixed Insurance',
            //    'uniqueId' : record.Insurance_Id,
            //    'panel'    : $('#editFormFixedInsurance'),
            //    'callback' : function (result)
            //    {
                    $('#formFixedInsuranceId').val(record.Insurance_Id);
                    $('#editformFIInsuranceType').select2('val', record.InsuranceType_Id);
                    $('#editformFIEmployeeName').select2('val', record.Employee_Id);
                    $('#editformFIStartMonth').html(record.Insurance_Start_Date);
                    $('#editformFIInsuranceDuration').select2('val', record.Duration);
                    $('#editformFIPaymentFrequency').select2('val', record.PaymentMode_Id);
                    $('#editformFIInsuranceAmount').val(record.Insurance_Amount);
                    $('#editformFIPremium').val(record.Premium);
                    $('#editformFIDescription').val(record.Description);
            //    }
            //});
        }
    }
    
    // Setting value to preimum on duration, payment mode
    function fnCalculatePremiumVal()
    {
        var insuranceAmt = $("#editformFIInsuranceAmount").val();
		var pmtMode      = $("#editformFIPaymentFrequency").select2('val');
		var insDuration  = $("#editformFIInsuranceDuration").select2('val');
		
		if (insuranceAmt != "" && /[0-9\.]/.test(insuranceAmt) && pmtMode != "" && insDuration != "")
		{
        	insuranceAmt = insuranceAmt.replace(/[^0-9\.]/g, '');
			
			$("#editformFIPremium").val((insuranceAmt / (insDuration * 12 / pmtMode)).toFixed(2));
		}
		else
		{
        	$("#editformFIPremium").val('');
		}
    }
    
    // on insurance type & amount change - to check premium amount
    function fnFixedEmpOrgShare()
    {
        if($('#editformFIInsuranceAmount').val() != '' && $('#s2id_editformFIInsuranceType').select2('val') != '' && $('#s2id_editformFIInsuranceDuration').select2('val') != '')
        {
            if($('#editformFICoverage').select2('val') == 1 && $('#s2id_editformFIEmployeeName').select2('val') != '')
            {
                var msg = 0;
                
                $.ajax({
                    type     : 'POST',
                    async    : false,
                    datatype : 'json',
                    url      : pageUrl()+'payroll/insurance/orgshareamt', 
                    data     : {
                        empId  : $('#s2id_editformFIEmployeeName').select2('val'),
                        typeId : $('#s2id_editformFIInsuranceType').select2('val')
                    },
                    success:function(valid)
                    {
                        if (isJson (valid))
                        {
                            var result = parseFloat(valid);
                            
                            if( result >= 0 && (result > $('#editformFIPremium').val()) )
                            {
                                // TO CALCULATE INSURANCE AMT
                                msg = (result*(parseFloat($('#s2id_editformFIInsuranceDuration').select2('val'))*12))/parseFloat($('#s2id_editformFIPaymentFrequency').select2('val'));
                                
                                
                                a=3;
                                
                               
                                
                                if(msg !== '' && !(isNaN(msg)))
                                {
                                    $('#editformFIInsuranceAmount').addClass("insAmtError");
                                    
                                    $.validator.addMethod('vInsAmtError', function () { return false; }, 'Minimum insurance amount is '+ msg);
                                    
                                    $.validator.addClassRules("insAmtError", { vInsAmtError : true });
                                }
                                else
                                {
                                    $('#editformFIInsuranceAmount').removeClass("insAmtError");
                                }
                            }
                            else
                            {
                                $('#editformFIInsuranceAmount').removeClass("insAmtError");
                            }
                        }
                        else
                        {
                            sessionExpired ();
                        }
                    }
                });
            }
            else
            {
                var msg = 0;
                
                $.ajax({
                    type     : 'POST',
                    async    : false,
                    datatype : 'json',
                    url      : pageUrl()+'payroll/insurance/orgshareamt', 
                    data     : {
                        typeId : $('#s2id_editformFIInsuranceType').select2('val')
                    },
                    success:function(valid)
                    {
                        if (isJson (valid))
                        {
                            var result = parseFloat(valid);
                            
                            if( result >= 0 && (result > $('#editformFIPremium').val()) )
                            {
                                // TO CALCULATE INSURANCE AMT
                                msg = (result*(parseFloat($('#s2id_editformFIInsuranceDuration').select2('val'))*12))/parseFloat($('#s2id_editformFIPaymentFrequency').select2('val'));
                                
                                $('#editformFIInsuranceAmount').addClass("insAmtError");
                                
                                $.validator.addMethod('vInsAmtError', function () { return false; }, 'Minimum insurance amount is '+ msg);
                                
                                $.validator.addClassRules("insAmtError", { vInsAmtError : true });
                            }
                            else
                            {
                                $('#editformFIInsuranceAmount').removeClass("insAmtError");
                            }
                        }
                        else
                        {
                            sessionExpired ();
                        }
                    }
                });
            }
        }
    }
    
    /* Set enable / disable toolbar icons */
    function fnActionButtonsFixedInsurance (record, action) {
        if (action == true && record != '')
        {
            fnGridButtons ($('#viewFixedInsurance, #editFixedInsurance, #deleteFixedInsurance'), true);
            
            // Checking history permission
            if ($.inArray(record.Audit, [null, undefined]) == -1)
            {
                $('#historyContextFixedInsurance').parent().show();
                fnGridButtons ($('#historyFixedInsurance'), true);
            }
            else
            {
                $('#historyContextFixedInsurance').parent().hide();
                fnGridButtons ($('#historyFixedInsurance'), false);
            }
        }
        else
        {
            fnGridButtons ($('#viewFixedInsurance, #editFixedInsurance, #deleteFixedInsurance, #historyFixedInsurance'), false);
        }
    }
    
    /*************************************** Variable insurance *******************************************************************/
    
     /*  Initialse DataTables, with no sorting on the 'details' column  */
    var tableVariableInsurance = $('#tableVariableInsurance').dataTable({
       "iDisplayLength" : 10,
       "lengthMenu"     : [ 5, 10, 25, 50, 100 ], 
       "bDestroy"       : true,
       "bAutoWidth"     : false,
       "bServerSide"    : true,
       "bDeferRender"   : true,
       "sServerMethod"  : 'POST',
       "sAjaxSource"    : pageUrl () + 'payroll/insurance/list-variable-insurance',
       "sAjaxDataProp"  : 'aaData',
       //"aaSorting"      : [[1, 'asc']],
       "aaSorting"      : [],
        "aoColumnDefs"   : [{"targets": 0, "orderable": false},
                            { "sClass" : "visible-xs visible-sm  hidden-md hidden-lg", "aTargets" : [0] },
                            { "sClass" : "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets" : [1] },
                            { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [4] },
                            { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [5] }],
        "fnCreatedRow": function( nRow, aData, iDataIndex ) {
            $(nRow).attr({"data-toggle":"context", "data-target":"#variable-insurance-context-menu" });
        },
       "aoColumns"      : [{
            "mData" : function (row, type, set) {
                return '<i class="fa fa-plus-square-o"></i>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div>'+ fnCheckNull(row['User_Defined_EmpId']) +'</div>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div>'+ row['Variable_Coverage'] +'</div>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div>'+ fnCheckNull(row['Employee_Name']) +'</div>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div style="word-break: break-all;" >'+ row['Insurance_Name'] +'</div>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div  >'+ row['Payment_Frequency'] +'</div>';
            }
        }]
    });
    
    //On + icon click in mobile & tablet view
    $(document).on('click', '#tableVariableInsurance i', function () {
        var nTr = $(this).parents('tr')[0];
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
    
        if ( tableVariableInsurance.fnIsOpen(nTr) )
        {
            /* This row is already open - close it */
            $(this).removeClass().addClass('fa fa-plus-square-o');
            tableVariableInsurance.fnClose(nTr);
        }
        else
        {
            var record = tableVariableInsurance.fnGetData( nTr );
            var nRow =  $('#tableVariableInsurance thead tr')[0];
            
            /* Open this row */
            $(this).removeClass().addClass('fa fa-minus-square-o');
            
            valueArray = [];
            headerArray=[];
            
            valueArray.Value_One = record.Insurance_Name;
            valueArray.Value_Two = record.Payment_Frequency;
            
            //get grid headers
            gridHeader(nRow.cells);
            
            tableVariableInsurance.fnOpen(nTr, fnDeviceColumnDetails(headerArray,valueArray), 'details hidden-lg');
        }
    });
    
    /*  Add event listener for select and unselect details  */
    $(document).on('click contextmenu', '#tableVariableInsurance tbody td div', function () {
        var selectRow = $(this).parent().parent();
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        tableVariableInsurance.$('tr.row_selected').removeClass('row_selected');
        
        if (!selectRow.hasClass('row_selected'))
        {
            selectRow.addClass('row_selected');
            
            fnActionButtonsVariableInsurance (true);
        }
        else
        {
            fnActionButtonsVariableInsurance (false);
        }
    });
    
    /**
     *  Grid Refresh
    */
    $('#gridPanelVariableInsurance .panel-reload').on('click', function () {
        fnRefreshTable (tableVariableInsurance);
    });
    
    // On whole grid filter event (to disable action icon)
    $('#tableVariableInsurance').on( 'draw.dt', function () {
        fnActionButtonsVariableInsurance (false);
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
    });
    
    //Add Variable Insurance
    $('#addVariableInsurance').on('click', function () {
        tableVariableInsurance.$('tr.row_selected').removeClass('row_selected');
        
        fnActionButtonsVariableInsurance (false);
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        $('#modalFormVariableInsurance .modal-title').html("<strong>Add<strong> "+$('#lblFormNameE').html());
        
        fnPreFillFormValuesVariableInsurance ('');
        
        $('#viewFormVariableInsurance, #editInViewVariableInsurance').hide();
        $('#editFormVariableInsurance, #formActionVariableInsurance').show();
    });
    
    //View Variable Insurance
    $('#viewVariableInsurance, #viewContextVariableInsurance').on('click', function () {
        var selectedRow = fnGetSelected ( tableVariableInsurance );
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        if (selectedRow.length)
        {
            var record = tableVariableInsurance.fnGetData (selectedRow[0]);
    
            if (record.VariableInsurance_Id > 0)
            {
                $('#modalFormVariableInsurance .modal-title').html("<strong>View<strong> "+$('#lblFormNameE').html());
                
                $('#viewVICoverage').text(record.Variable_Coverage);
        
                if (record.Variable_Coverage == 'Organization')
                {
                    $('.vICoverageEmployee').hide();
                    $('.vICoverageOrganization').show();
                    
                    if (record.Sal_Range_Defined_Flag == 1)
                    {
                        $('.vISalRange').show();
                        
                        $('#viewVIDefineSalaryRange').text('Yes');
                        
                        $('#viewVIMinimumSalary').text(record.Min_Salary);
                        $('#viewVIMaximumSalary').text(record.Max_Salary);
                        $('#viewVIContributionPeriod').text(record.Contribution_Period);
                    }
                    else
                    {
                        $('.vISalRange').hide();
                        
                        $('#viewVIDefineSalaryRange').text('Ignore');
                    }
                }
                else
                {
                    $('.vICoverageEmployee').show();
                    $('.vICoverageOrganization').hide();
                    
                    $('#viewVIEmployeeName').text(record.Employee_Name);
                    $('#viewVIOrganisationShare').text(fnCheckNull(record.Org_Share));
                    $('#viewVIEmployeeShare').text(fnCheckNull(record.Emp_Share));
                }
                
                $('#viewVIInsuranceType').text( record.Insurance_Name );
                $('#viewVIPaymentFrequency').text(record.Payment_Frequency);

                $('#viewVIDescription').text(fnCheckNull(record.Description));
                $('#viewWageInclusion').text(fnCheckNull(record.Wage_Name));
                
                /**
                 *  Prefill addional information values based on record with form name
                */
                fnPreFillAdditionalPanel ('VariableInsurance', record);
                
                $('#modalFormVariableInsurance').modal('toggle');
                $('#editFormVariableInsurance, #formActionVariableInsurance').hide();
                $('#viewFormVariableInsurance, #editInViewVariableInsurance').show();
            }
            else
            {
                jAlert ({ msg : 'Kindly select variable insurance', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select variable insurance', type : 'info' });
        }
    });
    
    //Edit Variable Insurance
    $('#editVariableInsurance, #editContextVariableInsurance, #editInViewVariableInsurance').on('click', function () {
        var selectedRow = fnGetSelected (tableVariableInsurance );
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        if (selectedRow.length)
        {
            var record = tableVariableInsurance.fnGetData (selectedRow[0]);
                variableInsuranceId = record.VariableInsurance_Id;
                
            if (variableInsuranceId > 0 && !isNaN(variableInsuranceId))
            {
                var buttonId = $(this).prop('id');
                
                setLock ({
                    'formName' : 'Insurance',
                    'uniqueId' : variableInsuranceId,
                    'callback' : function (result)
                    {
                        if (buttonId != 'editInViewVariableInsurance')
                            $('#modalFormVariableInsurance').modal('toggle');
                        
                        $('#modalFormVariableInsurance .modal-title').html("<strong>Edit<strong> "+$('#lblFormNameE').html());
                        
                        fnPreFillFormValuesVariableInsurance (record);
                        
                        $('#editInViewVariableInsurance, #viewFormVariableInsurance').hide();
                        $('#editFormVariableInsurance, #formActionVariableInsurance').show();
                    }
                });
            }
            else
            {
                jAlert ({ msg : 'Kindly select variable insurance', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select variable insurance', type : 'info' });
        }
    });
    
    //Trigger delete confirmation popup in delete menu in context menu
    $('#deleteVariableInsurance, #deleteContextVariableInsurance').on('click', function () {
        var selectedRow = fnGetSelected (tableVariableInsurance );
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        if (selectedRow.length)
        {
            var variableInsuranceId = tableVariableInsurance.fnGetData (selectedRow[0]).VariableInsurance_Id;
            
            if (variableInsuranceId > 0 && !isNaN(variableInsuranceId))
            {
                $('#modalDeleteVariableInsurance').modal('toggle');
            }
            else
            {
                jAlert ({ msg : 'Kindly select variable insurance', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select variable insurance', type : 'info' });
        }
    });
    
    //Delete Variable Insurance
    $('#deleteConfirmVariableInsurance').on('click', function () {
        var selectedRow = fnGetSelected (tableVariableInsurance );
        
        if (selectedRow.length)
        {
            var variableInsuranceId = tableVariableInsurance.fnGetData (selectedRow[0]).VariableInsurance_Id;
            
            if (variableInsuranceId > 0 && !isNaN(variableInsuranceId))
            {
                $.ajax ({
                    type     : 'POST',
                    dataType : 'json',
                    async    : false,
                    url      : pageUrl () +'payroll/insurance/delete-variable-insurance/variableInsuranceId/'+ variableInsuranceId,
                    success  : function (result)
                    {
                        if (isJson (result))
                        {
                            if (result.success)
                            {
                                fnRefreshTable (tableVariableInsurance);
                            }
                            
                            jAlert ({ msg : result.msg, type : result.type });
                        }
                        else
                        {
                            sessionExpired ();
                        }
                    }
                });
            }
            else
            {
                jAlert ({ msg : 'Kindly select variable insurance', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select variable insurance', type : 'info' });
        }
    });
    
    //Form reset - Variable Insurance
    $('#formResetVariableInsurance').on('click', function () {
        var l = Ladda.create(this);
        
        l.start();
        
        if ($('#formVariableInsuranceId').val() > 0)
        {
            fnPreFillFormValuesVariableInsurance (tableVariableInsurance.fnGetData (fnGetSelected (tableVariableInsurance)[0]));
        }
        else
        {
            fnPreFillFormValuesVariableInsurance ('');
        }
        
        l.stop();
    });
    
    //Form submit - Variable Insurance
    $('#formSubmitVariableInsurance').on('click', function () {
        setMask('#wholepage');

        var l = Ladda.create(this);
        
        l.start();
        
        $("#s2id_editformVIEmployeeName, #s2id_editformVICoverage, #s2id_editformVIPaymentFrequency, #s2id_editformVISalRange, #s2id_editformVIInsuranceType, #s2id_editformVIContributionPeriod").removeClass('form-error');
        
        if (isDirtyFormVariableInsurance)
        {
            if ($("#editFormVariableInsurance").valid())
            {
                $.ajax ({
                    type     : 'POST',
                    dataType : 'json',    
                    url      : pageUrl () +'payroll/insurance/update-variableinsurance',
                    data     : {
                        variableInsuranceId   : $('#formVariableInsuranceId').val(),
                        coverage              : $('#s2id_editformVICoverage').select2('val'),
                        employeeId            : $('#s2id_editformVIEmployeeName').select2('val'),
                        insuranceTypeId       : $('#s2id_editformVIInsuranceType').select2('val'),
                        paymentModeId         : $('#s2id_editformVIPaymentFrequency').select2('val'),
                        orgSalaryRange        : $('#s2id_editformVISalRange').select2('val'),
                        orgMinSalary          : $('#editformVIMinSalary').val(),
                        orgMaxSalary          : $('#editformVIMaxSalary').val(),
                        orgContributionPeriod : $('#s2id_editformVIContributionPeriod').select2('val'),
                        formWageInclusionId   : $("#s2id_formWageInclusion").select2("val"),
                        description           : $('#editformVIDescription').val()
                    },
                    success  : function (result)
                    {
                        if (isJson (result))
                        {
                            if (result.success)
                            {
                                isDirtyFormVariableInsurance = false;
                                
                                $('#modalFormVariableInsurance').modal('toggle');
                                
                                fnRefreshTable (tableVariableInsurance);
                                
                                jAlert ({ msg : result.msg, type : result.type });
                            }
                            else
                            {
                                jAlert ({ panel : $('#editFormVariableInsurance'), msg : result.msg, type : result.type });
                            }
                        }
                        else
                        {
                            /* To handle internal server error */
                            jAlert({  panel : $('#editFormVariableInsurance'), msg : "Something went wrong. Please contact system admin", type : "warning" });
                        }
                        
                        l.stop();
                        removeMask();
                    },
                    error: function (varInsUpdateErrorResult){
                        l.stop();
                        removeMask();
                        if(varInsUpdateErrorResult.status == 200){
                            sessionExpired();
                        }else{
                            /* To handle internal server error */
                            jAlert({  panel : $('#editFormVariableInsurance'), msg : "Something went wrong. Please contact system admin", type : "warning" });
                        }
                    }
                });    
            }
            else
            {
                l.stop();
                removeMask();
            }
        }
        else
        {
            l.stop();
            removeMask();
            jAlert ({ panel : $('#editFormVariableInsurance'), msg : 'Form has no changes', type : 'info' });
        }
    });
    
    // On form field change
    $('#editFormVariableInsurance').on('change', function() {
        isDirtyFormVariableInsurance = true;
    });
    
    /**
     *  Add,Edit form modal hide event
    */
    $('#modalFormVariableInsurance').on('hide.bs.modal', function (e) {
        if (isDirtyFormVariableInsurance)
        {
            e.preventDefault();
            e.stopImmediatePropagation();
           
            $('#modalDirtyVariableInsurance').modal('toggle');
        }
        else
        {
            fnCloseFormVariableInsurance (false);
        }
    });
    
    /**
     *  click to close add/edit modal
    */
    $('#editCloseConfirmVariableInsurance').on('click', function () {
        isDirtyFormVariableInsurance = false;
        
        fnCloseFormVariableInsurance (true);
    });
    
    // On variable insurance coverage value change
    $('#editformVICoverage').on('change', function(){
        var coverageVal = $(this).val();
        
        $('#s2id_editformVIEmployeeName').select2('val','');
        $('#s2id_editformVIPaymentFrequency').select2('val',12);
        
        if( coverageVal == 0 )
        {
            $('#editformVIEmployeeName').removeClass('vRequired');
            $('.viCoverageBasedHidden').hide();
        }
        else
        {
            fnGetEmployeeName('Insurance','#editformVIEmployeeName');
            $('#editformVIEmployeeName').addClass('vRequired');
            $('.viCoverageBasedHidden').show();
        }
        
        $('#editformVIMinSalary, #editformVIMaxSalary, #editformVIContributionPeriod').removeClass('vRequired');
        $('.viPaymentFreqBasedHidden, .viSalRangeBasedHidden, .viContributionPeriodBasedHidden').hide();
        
        $.ajax ({
            type     : 'POST',
            dataType : 'json',
            async    : false,
            url      : pageUrl () +'payroll/insurance/get-insurancetypes',
            data     : {
                mode     : 'V',
                coverage : coverageVal
            },
            success  : function (result)
            {
                if (isJson (result))
                {
                    var insuranceTypeField  = $('#editformVIInsuranceType');
                    var insuranceTypeOption = ['<option value="">--Select--</option>'];
                    
                    insuranceTypeField.find('option').remove();
                    
                    for (var i in result)
                    {
                        insuranceTypeOption.push('<option value=' + i + '>'+ result[i] +'</option>');
                    }
                    
                    insuranceTypeField.append(insuranceTypeOption);
                    insuranceTypeField.select2('val','');
                }
                else
                {
                    sessionExpired ();
                }
            }
        });
    });
    
    // On payment frequency value change
    $('#editformVIPaymentFrequency').on('change', function(){
        var paymentFreq = $(this).select2('val');
        
        if (paymentFreq == 1 && $('#s2id_editformVICoverage').select2('val') == 0)
        {
            $('#editformVISalRange').select2('val', 0);
            $('.viPaymentFreqBasedHidden').show();
        }
        else
        {
            $('#editformVISalRange').select2('val', 1);
            $('#editformVIMinSalary, #editformVIMaxSalary, #editformVIContributionPeriod').removeClass('vRequired');
            $('.viPaymentFreqBasedHidden, .viSalRangeBasedHidden, .viContributionPeriodBasedHidden').hide();
        }
    });
    
    // On salary range change
    $('#editformVISalRange').on('change', function(){
        $('#editformVIMinSalary, #editformVIMaxSalary').val('');
        $('#editformVIContributionPeriod').select2('val', '');
        
        if ($(this).val() == 1)
        {
            $('#editformVIMinSalary, #editformVIMaxSalary, #editformVIContributionPeriod').addClass('vRequired');
            $('.viSalRangeBasedHidden').show();
        }
        else
        {
            $('#editformVIMinSalary, #editformVIMaxSalary, #editformVIContributionPeriod').removeClass('vRequired');
            $('.viSalRangeBasedHidden, .viContributionPeriodBasedHidden').hide();
        }
    });
    
    // On min and max salary value change
    $('#editformVIMinSalary, #editformVIMaxSalary').on('change', function(){
        var minSalary = $('#editformVIMinSalary').val();
        var maxSalary = $('#editformVIMaxSalary').val();
        
        if (minSalary != '' && maxSalary != '')
        {
            if (minSalary > maxSalary)
            {
                $('#editformVIMaxSalary').addClass("maxSalaryError");
                
                $.validator.addMethod('vMaxSalaryError', function () { return false; }, 'Maximum salary should be less than or equal to minimum salary');
                
                $.validator.addClassRules("maxSalaryError", { vMaxSalaryError : true });
            }
            else
            {
                $('#editformVIMaxSalary').removeClass("maxSalaryError");
            }
        }
    });
    
    // On contribution period change
    $('#editformVIContributionPeriod').on('change', function(){
        var contributionPeriod = $(this).select2('val');
            fiscalStartMonth = $('#formFiscalStartMonth').val();
            rowCount = (12/contributionPeriod);
        
        var startMonth = fiscalStartMonth - 1;
            endMonth = 0;
            incrementMonth = Number(contributionPeriod) - 1;
            dataSetArray = [];
        
        for(var i=1;i<=rowCount;i++)
        {
            if (startMonth == 0 && endMonth == 0 && i!=1)
            {
                startMonth = endMonth + 1;
            }
            else
            {
                if (endMonth >= 11)
                {
                    startMonth = endMonth - 11;
                }
                else if (endMonth >= 0 && i!=1)
                {
                    startMonth = endMonth + 1;
                }
            }
            
            endMonth = startMonth + incrementMonth;
            
            if (endMonth > 11)
            {
                endMonth = endMonth - 12;
            }
            
            dataSetArray.push([i,monthArray[startMonth], monthArray[endMonth]]);
        }
        
        $('.viContributionPeriodBasedHidden').show();
        
        // display contribution period datatable month
        $('#tableContributionPeriod').DataTable( {
            "bDestroy"     : true,
            "bAutoWidth"   : false,
            "aoColumnDefs" : [{ "sClass" : "text-center", "aTargets" : [0, 1, 2] }],
            "bFilter"      : false,    // to disable the common search
            "bPaginate"    : false, // Remove pagination
            "bInfo"        : false,
            "bSort"        : false,
            data           : dataSetArray
        });
    });
    
    /** Filter Form **/
    $('#filterVariableInsurance,#closeFilterVariableInsurance').on('click', function(){
        if ($('#filterPanelVariableInsurance').hasClass('open')){
            $('#filterPanelVariableInsurance').removeClass('open');
            $('#filterPanelVariableInsurance').hide();
        }
        else{
            $('#filterPanelVariableInsurance').addClass('open');
            $('#filterPanelVariableInsurance').show();
        }
    });
    
    /**
     *  Reset Filter Form
    */
    $('#filterResetVariableInsurance,#closeFilterVariableInsurance').on('click', function () {
        if (!$('#filterVIEmployeeName').is('[readonly]')) {
            $('#filterVIEmployeeName').val('');
        }
        
        $('#s2id_filterVIInsuranceType, #filterVIPaymentFrequency').select2('val', '');
       
        tableVariableInsurance.fnReloadAjax( pageUrl () + "payroll/insurance/list-variable-insurance/vEmployeeName/"+ $('#filterVIEmployeeName').val() );
    });
    
    /**
     *  Apply filter to variable insurance grid
    */
    $('#filterApplyVariableInsurance').on('click', function () {
        ftEmployeeName        = $('#filterVIEmployeeName').val();
        ftInsType             = $('#s2id_filterVIInsuranceType').select2('val');
        ftInsPaymentFrequency = $('#s2id_filterVIPaymentFrequency').select2('val');
        
        ftInsType = (isNaN(ftInsType)) ? '' : ftInsType;
        ftInsPaymentFrequency = (isNaN(ftInsPaymentFrequency)) ? '' : ftInsPaymentFrequency;
       
        tableVariableInsurance.fnReloadAjax(pageUrl () +'payroll/insurance/list-variable-insurance/vEmployeeName/'+ ftEmployeeName
                                     +'/vInsuranceType/'+ ftInsType +'/vInsPaymentFrequency/'+ ftInsPaymentFrequency);
    });
    
    /**
     *  When form isn't dirty then clear the lock flag and then close that add/edit form
    */
    function fnCloseFormVariableInsurance (hideAction) {
        var variableInsuranceId = $('#formVariableInsuranceId').val();
        
        if (variableInsuranceId > 0 && !isNaN(variableInsuranceId))
        {
            clearLock ({
                'formName' : 'Insurance',
                'uniqueId' : variableInsuranceId,
                'callback' : function ()
                {
                    $('#formVariableInsuranceId').val(0);
                    
                    if (hideAction)
                        $('#modalFormVariableInsurance').modal('hide');
                }
            });
        }
        else
        {
            if (hideAction)
                $('#modalFormVariableInsurance').modal('hide');
        }
    }
    
    /**
     *  Prefill variable insurance form values in add, edit, reset events
    */
    function fnPreFillFormValuesVariableInsurance (record) {
        $("#s2id_editformVIEmployeeName, #s2id_editformVICoverage, #s2id_editformVIPaymentFrequency, #s2id_editformVISalRange, #s2id_editformVIInsuranceType, #s2id_editformVIContributionPeriod").removeClass('form-error');
        
        if (record != '')
        {
            $('#s2id_editformVICoverage').select2('val', record.Coverage);
            
            $('#editformVICoverage').trigger('change');
            
            $('#editformVICoverage').prop('readOnly', true);
            
            $('#formVariableInsuranceId').val(record.VariableInsurance_Id);
            
            if (record.Coverage == 1)
            {
                $('#s2id_editformVIEmployeeName').select2('val', record.Employee_Id);
                $('#editformVIEmployeeName').prop('readOnly', true);
            }
            
            $('#s2id_editformVIInsuranceType').select2('val', record.InsuranceType_Id);
            $('#s2id_editformVIPaymentFrequency').select2('val', record.PaymentMode_Id);
            
            $('#editformVIPaymentFrequency').trigger('change');
            
            if (record.Coverage == 0 && record.PaymentMode_Id == 1)
            {
                $('#s2id_editformVISalRange').select2('val', record.Sal_Range_Defined_Flag);
                
                $('#editformVISalRange').trigger('change');
                
                if (record.Sal_Range_Defined_Flag == 1)
                {
                    $('#editformVIMinSalary').val(record.Min_Salary);
                    $('#editformVIMaxSalary').val(record.Max_Salary);
                    $('#s2id_editformVIContributionPeriod').select2('val',record.Contribution_Period);
                    
                    $('#editformVIContributionPeriod').trigger('change');
                }
            }

            if (record.Wage_Id != "-") {
                $('#s2id_formWageInclusion').select2("val", (record.Wage_Id).split(','));
            }
            $('#editformVIDescription').val(record.Description);
        }
        else
        {
            $('#editformVICoverage, #editformVIEmployeeName').prop('readOnly', false);
            
            $('#formVariableInsuranceReset').trigger('click');
            
            $('#formVariableInsuranceId').val(0);
            
            $('#s2id_editformVICoverage').select2('val', 0);
            $('#s2id_editformVIPaymentFrequency').select2('val', 12);
            $('#s2id_editformVISalRange').select2('val', 0);
            
            $('#editformVICoverage, #editformVISalRange').trigger('change');
            
            $('#s2id_editformVIInsuranceType').select2('val', '');

            $('#s2id_formWageInclusion').select2('val', '');

            
            $('#editVIValRoundedOffInPayroll').prop('checked',false);
        }
        
        $( "#editFormVariableInsurance").validate().resetForm();
        
        isDirtyFormVariableInsurance = false;
    }
    
    /* Set enable / disable toolbar icons */
    function fnActionButtonsVariableInsurance (action) {
        fnGridButtons ($('#viewVariableInsurance, #editVariableInsurance, #deleteVariableInsurance'), action);
    }
    
    /*************************************** Insurance Types ************************************************************************/
    
    /*  Initialse DataTables, with no sorting on the 'details' column  */
    var tableInsuranceType = $('#tableInsuranceType').dataTable({
       "iDisplayLength" : 10,
       "lengthMenu"     : [ 5, 10, 25, 50, 100 ], 
       "bDestroy"       : true,
       "bAutoWidth"     : false,
       "bServerSide"    : true,
       "bDeferRender"   : true,
       "sServerMethod"  : 'POST',
       "sAjaxSource"    : pageUrl () + 'payroll/insurance/list-insurance-type',
       "sAjaxDataProp"  : 'aaData',
       "aaSorting"      : [[0, 'asc']],
       "fnCreatedRow": function( nRow, aData, iDataIndex ) {
         $(nRow).attr({"data-toggle":"context", "data-target":"#context-menu2" });
        },
       "aoColumns"      : [{
           "mData" : function (row, type, set) {
               return '<div>'+ row['Insurance_Name'] +'</div>';
            }
        },
        {
            "mData": function (row, type, set) {
                return '<div>' + row['EmployeeStateInsurance'] + '</div>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div>'+ row['InsuranceType_Status'] +'</div>';
            }
        }]
    });
    
    /*  Add event listener for select and unselect details  */
    $(document).on('click contextmenu', '#tableInsuranceType tbody td div', function () {
        var selectRow = $(this).parent().parent();
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        tableInsuranceType.$('tr.row_selected').removeClass('row_selected');
        
        if (!selectRow.hasClass('row_selected'))
        {
            selectRow.addClass('row_selected');
            
            fnActionButtonsInsuranceType (true);
        }
        else
        {
            fnActionButtonsInsuranceType (false);
        }
    });
    
    /**
     *  Grid Refresh
    */
    $('#gridPanelInsuranceType .panel-reload').on('click', function () {
        fnRefreshTable (tableInsuranceType);
    });
    
    // On whole grid filter event (to disable action icon)
    $('#tableInsuranceType').on( 'draw.dt', function () {
        fnActionButtonsInsuranceType (false);
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
    });
    
    //Add Insurance Type
    $('#addInsuranceType').on('click', function () {
        tableInsuranceType.$('tr.row_selected').removeClass('row_selected');
        
        fnActionButtonsInsuranceType (false);
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        $('#modalFormInsuranceType .modal-title').html('<strong>Add</strong> '+$('#lblFormNameB').html());
        
        fnPreFillFormValuesInsuranceType ('');
        
        $('#viewFormInsuranceType, #editInViewInsuranceType').hide();
        $('#editFormInsuranceType, #formActionInsuranceType').show();
    });
    
    //View Insurance Type
    $('#viewInsuranceType, #viewContextInsuranceType').on('click', function () {
        var selectedRow = fnGetSelected ( tableInsuranceType );
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        if (selectedRow.length)
        {
            var record = tableInsuranceType.fnGetData (selectedRow[0]);
            
            if (record.InsuranceType_Id > 0)
            {
                $('#modalFormInsuranceType .modal-title').html('<strong>View</strong> '+$('#lblFormNameB').html());
                
                $('#viewITInsuranceName').text(record.Insurance_Name);
                $('#viewEmployeeStateInsurance').text(record.EmployeeStateInsurance);
                $('#viewITStatus').text(record.InsuranceType_Status);
                $('#viewITDescription').text( fnCheckNull(record.Description));
                
                /**
                 *  Prefill addional information values based on record with form name
                */
                fnPreFillAdditionalPanel ('InsuranceType', record);
                
                $('#modalFormInsuranceType').modal('toggle');
                $('#editFormInsuranceType, #formActionInsuranceType').hide();
                $('#viewFormInsuranceType, #editInViewInsuranceType').show();
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+$('#lblFormNameB').html()+' record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameB').html()+' record', type : 'info' });
        }
    });
    
    //Edit Insurance Type
    $('#editInsuranceType, #editContextInsuranceType, #editInViewInsuranceType').on('click', function () {
        var selectedRow = fnGetSelected (tableInsuranceType );
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        if (selectedRow.length)
        {
            var record = tableInsuranceType.fnGetData (selectedRow[0]);
                insuranceTypeId = record.InsuranceType_Id;
                
            if (insuranceTypeId > 0 && !isNaN(insuranceTypeId))
            {
                var buttonId = $(this).prop('id');
                
                setLock ({
                    'formName' : 'Insurance Types',
                    'uniqueId' : insuranceTypeId,
                    'callback' : function (result)
                    {
                        if (buttonId != 'editInViewInsuranceType')
                            $('#modalFormInsuranceType').modal('toggle');
                        
                        $('#modalFormInsuranceType .modal-title').html('<strong>Edit</strong> '+$('#lblFormNameB').html());
                        
                        fnPreFillFormValuesInsuranceType (record);
                        
                        $('#editInViewInsuranceType, #viewFormInsuranceType').hide();
                        $('#editFormInsuranceType, #formActionInsuranceType').show();
                    }
                });
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+$('#lblFormNameB').html()+' record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameB').html()+' record', type : 'info' });
        }
    });
    
    //Trigger delete confirmation popup in delete menu in context menu
    $('#deleteInsuranceType, #deleteContextInsuranceType').on('click', function () {
        var selectedRow = fnGetSelected (tableInsuranceType );
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        if (selectedRow.length)
        {
            var insuranceTypeId = tableInsuranceType.fnGetData (selectedRow[0]).InsuranceType_Id;
            
            if (insuranceTypeId > 0 && !isNaN(insuranceTypeId))
            {
                $('#modalDeleteInsuranceType').modal('toggle');
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+$('#lblFormNameB').html()+' record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameB').html()+' record', type : 'info' });
        }
    });
    
    //Delete Insurance Type
    $('#deleteConfirmInsuranceType').on('click', function () {
        var selectedRow = fnGetSelected (tableInsuranceType );
        
        if (selectedRow.length)
        {
            var insuranceTypeId = tableInsuranceType.fnGetData (selectedRow[0]).InsuranceType_Id;
            
            if (insuranceTypeId > 0 && !isNaN(insuranceTypeId))
            {
                $.ajax ({
                    type     : 'POST',
                    dataType : 'json',
                    async    : false,
                    url      : pageUrl () +'payroll/insurance/delete-insurance-type/insuranceTypeId/'+ insuranceTypeId,
                    success  : function (result)
                    {
                        if (isJson (result))
                        {
                            if (result.success)
                            {
                                fnRefreshTable (tableInsuranceType);
                                
                                //Call Reload combo 
                                Combofieldpush(result);
                                fnRefreshTable(tableFixedInsurance);
                                fnRefreshTable(tableVariableInsurance);
                                fnRefreshTable(tableInsuranceGrade);
                            }
                            
                            jAlert ({ msg : result.msg, type : result.type });
                        }
                        else
                        {
                            sessionExpired ();
                        }
                    }
                });
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+$('#lblFormNameB').html()+' record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameB').html()+' record', type : 'info' });
        }
    });
    
    //Form reset - Insurance Type
    $('#formResetInsuranceType').on('click', function () {
        var l = Ladda.create(this);
        
        l.start();
        
        if ($('#formInsuranceTypeId').val() > 0)
        {
            fnPreFillFormValuesInsuranceType (tableInsuranceType.fnGetData (fnGetSelected (tableInsuranceType)[0]));
        }
        else
        {
            fnPreFillFormValuesInsuranceType ('');
        }
        
        l.stop();
    });
    
    //Form submit - Insurance Type
    $('#formSubmitInsuranceType').on('click', function () {
        var l = Ladda.create(this);
        
        l.start();
        
        $("#s2id_editformITStatus").removeClass('form-error');
        
        if (isDirtyFormInsuranceType)
        {
            if ($("#editFormInsuranceType").valid())
            {
                $.ajax ({
                    type     : 'POST',
                    dataType : 'json',    
                    url      : pageUrl () +'payroll/insurance/update-insurance-type',
                    data     : {
                        insuranceTypeId : $('#formInsuranceTypeId').val(),
                        insuranceName   : $('#editformITInsuranceName').val(),
                        employeeStateInsurance: $('#editEmployeeStateInsurance').is(":checked"),
                        insuranceStatus : $('#s2id_editformITStatus').select2('val'),
                        description     : $('#editformITDescription').val()
                    },
                    success  : function (result)
                    {
                        if (isJson (result))
                        {
                            if (result.success)
                            {
                                isDirtyFormInsuranceType = false;
                                
                                $('#modalFormInsuranceType').modal('toggle');
                                
                                fnRefreshTable (tableInsuranceType);
                                                                
                                //Call Reload combo 
                                Combofieldpush(result);
                            
                                fnRefreshTable(tableFixedInsurance);
                                fnRefreshTable(tableVariableInsurance);
                                fnRefreshTable(tableInsuranceGrade);
                                
                                jAlert ({ msg : result.msg, type : result.type });
                            }
                            else
                            {
                                jAlert ({ panel : $('#editFormInsuranceType'), msg : result.msg, type : result.type });
                            }
                        }
                        else
                        {
                            sessionExpired ();
                        }
                        
                        l.stop();
                    }
                });    
            }
            else
            {
                l.stop();
            }
        }
        else
        {
            l.stop();
            jAlert ({ panel : $('#editFormInsuranceType'), msg : 'Form has no changes', type : 'info' });
        }
    });
    
    // On form field change
    $('#editFormInsuranceType').on('change', function() {
        isDirtyFormInsuranceType = true;
    });
    
    /**
     *  Add,Edit form modal hide event
    */
    $('#modalFormInsuranceType').on('hide.bs.modal', function (e) {
        if (isDirtyFormInsuranceType)
        {
            e.preventDefault();
            e.stopImmediatePropagation();
           
            $('#modalDirtyInsuranceType').modal('toggle');
        }
        else
        {
            fnCloseFormInsuranceType (false);
        }
    });
    
    /**
     *  click to close add/edit modal
    */
    $('#editCloseConfirmInsuranceType').on('click', function () {
        isDirtyFormInsuranceType = false;
        
        fnCloseFormInsuranceType (true);
    });
    
    /** Filter Form **/
    $('#filterInsuranceType,#closeFilterInsuranceType').on('click', function(){
        if ($('#filterPanelInsuranceType').hasClass('open')){
            $('#filterPanelInsuranceType').removeClass('open');
            $('#filterPanelInsuranceType').hide();
        }
        else{
            $('#filterPanelInsuranceType').addClass('open');
            $('#filterPanelInsuranceType').show();
        }
    });
    
    /**
     *  Reset Filter Form
    */
    $('#filterResetInsuranceType,#closeFilterInsuranceType').on('click', function () {
        $('#filterITInsuranceName').val('');
        
        $('#s2id_filterITInsuranceStatus ,#s2id_filterEmployeeStateInsurance').select2('val', '');
       
        tableInsuranceType.fnReloadAjax( pageUrl () + "payroll/insurance/list-insurance-type" );
    });
    
    /**
     *  Apply filter to fixed insurance grid
    */
    $('#filterApplyInsuranceType').on('click', function () {
        ftInsuranceName   = $('#filterITInsuranceName').val();

        ftEmployeeStateInsurance = $('#s2id_filterEmployeeStateInsurance').select2('val');

        ftInsuranceStatus = $('#s2id_filterITInsuranceStatus').select2('val');
        
        tableInsuranceType.fnReloadAjax(pageUrl () +'payroll/insurance/list-insurance-type/insuranceName/'+ ftInsuranceName
            + '/employeeStateInsurance/' + ftEmployeeStateInsurance+'/status/'+ ftInsuranceStatus );
    });
    
    /**
     *  When form isn't dirty then clear the lock flag and then close that add/edit form
    */
    function fnCloseFormInsuranceType (hideAction) {
        var insuranceTypeId = $('#formInsuranceTypeId').val();
        
        if (insuranceTypeId > 0 && !isNaN(insuranceTypeId))
        {
            clearLock ({
                'formName' : 'Insurance Types',
                'uniqueId' : insuranceTypeId,
                'callback' : function ()
                {
                    $('#formInsuranceTypeId').val(0);
                    
                    if (hideAction)
                        $('#modalFormInsuranceType').modal('hide');
                }
            });
        }
        else
        {
            if (hideAction)
                $('#modalFormInsuranceType').modal('hide');
        }
    }
    
    /**
     *  Prefill insurance type form values in add, edit, reset events
    */
    function fnPreFillFormValuesInsuranceType (record) {
        $("#s2id_editformITStatus").removeClass('form-error');
        
        var insuranceStatusField = $("#editformITStatus");
        
        insuranceStatusField.find('option').remove();
        
        if (record != '')
        {
            $('#formInsuranceTypeId').val(record.InsuranceType_Id);

            if (parseInt(record.Employee_State_Insurance) == 1) {
                $('#editEmployeeStateInsurance').prop('checked', true);
            } else {
                $('#editEmployeeStateInsurance').prop('checked', false);
            }

            $('#editformITInsuranceName').val(record.Insurance_Name);
            
            insuranceStatusField.append('<option value="Active">Active</option>'+
                                          '<option value="Inactive">Inactive</option>');
             
            insuranceStatusField.select2('val', record.InsuranceType_Status);
            
            $('#editformITDescription').val(record.Description);
        }
        else
        {
            $('#formInsuranceTypeReset').trigger('click');
            
            $('#formInsuranceTypeId').val(0);
            $('#editEmployeeStateInsurance').prop('checked', false);
            insuranceStatusField.append('<option value="Active">Active</option>');
            
            insuranceStatusField.select2('val', 'Active');
        }
        
        $( "#editFormInsuranceType").validate().resetForm();
        
        isDirtyFormInsuranceType = false;
    }
    
    /* Set enable / disable toolbar icons */
    function fnActionButtonsInsuranceType (action) {
        fnGridButtons ($('#viewInsuranceType, #editInsuranceType, #deleteInsuranceType'), action);
    }
    
    /*************************************** Insurance Grade ************************************************************************/
    
    /*  Initialse DataTables, with no sorting on the 'details' column  */
    var tableInsuranceGrade = $('#tableInsuranceGrade').dataTable({
       "iDisplayLength" : 10,
       "lengthMenu"     : [ 5, 10, 25, 50, 100 ], 
       "bDestroy"       : true,
       "bAutoWidth"     : false,
       "bServerSide"    : true,
       "bDeferRender"   : true,
       "sServerMethod"  : 'POST',
       "sAjaxSource"    : pageUrl () + 'payroll/insurance/list-insurance-grade',
       "sAjaxDataProp"  : 'aaData',
        "aoColumnDefs"   : [{"targets": 0, "orderable": false},
                            { "sClass" : "visible-xs visible-sm  visible-md hidden-lg", "aTargets" : [0] },
                            { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [3] },
                            { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [4] },
                            { "sClass" : "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets" : [5] }],
       "aaSorting"      : [[1, 'desc']],       
       "fnCreatedRow": function( nRow, aData, iDataIndex ) {
        $(nRow).attr({"data-toggle":"context", "data-target":"#insurance-grade-context-menu" });
       },
       "aoColumns"      : [{
            "mData" : function (row, type, set) {
                return '<i class="fa fa-plus-square-o"></i>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div>'+ row['Insurance_Name'] +'</div>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div>'+ row['Grade_Name'] +'</div>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div class="text-center" >'+ fnCheckNull(row['Org_ShareAmount']) +'</div>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div class="text-center" >'+ fnCheckNull(row['Org_SharePercent']) +'</div>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div class="text-center" >'+ fnCheckNull(row['Emp_SharePercent']) +'</div>';
            }
        }]
    });
    
    //On + icon click in mobile & tablet view
    $(document).on('click', '#tableInsuranceGrade i', function () {
        var nTr = $(this).parents('tr')[0];
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        if ( tableInsuranceGrade.fnIsOpen(nTr) )
        {
            /* This row is already open - close it */
            $(this).removeClass().addClass('fa fa-plus-square-o');
            tableInsuranceGrade.fnClose(nTr);
        }
        else
        {
            var record = tableInsuranceGrade.fnGetData( nTr );
            var nRow =  $('#tableInsuranceGrade thead tr')[0];
            
            /* Open this row */
            $(this).removeClass().addClass('fa fa-minus-square-o');
            
            valueArray = [];
            headerArray=[];
        
            valueArray.Value_One = record.Org_ShareAmount;
            valueArray.Value_Two = record.Org_SharePercent;
            valueArray.Value_Three = record.Emp_SharePercent;
            
            $.each(nRow.cells, function(i,v) {
                headerArray['Header'+i] = v.innerText;
            });
            
            tableInsuranceGrade.fnOpen(nTr, fnDeviceColumnDetails(headerArray,valueArray), 'details hidden-lg');
        }
    });
    
    /*  Add event listener for select and unselect details  */
    $(document).on('click contextmenu', '#tableInsuranceGrade tbody td div', function () {
        var selectRow = $(this).parent().parent();
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        tableInsuranceGrade.$('tr.row_selected').removeClass('row_selected');
        
        if (!selectRow.hasClass('row_selected'))
        {
            selectRow.addClass('row_selected');
            
            fnActionButtonsInsuranceGrade (true);
        }
        else
        {
            fnActionButtonsInsuranceGrade (false);
        }
    });
    
    /**
     *  Grid Refresh
    */
    $('#gridPanelInsuranceGrade .panel-reload').on('click', function () {
        fnRefreshTable (tableInsuranceGrade);
    });
    
    // On whole grid filter event (to disable action icon)
    $('#tableInsuranceGrade').on( 'draw.dt', function () {
        fnActionButtonsInsuranceGrade (false);
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
    });
    
    //Add Insurance Grade
    $('#addInsuranceGrade').on('click', function () {
        tableInsuranceGrade.$('tr.row_selected').removeClass('row_selected');
        
        fnActionButtonsInsuranceGrade (false);
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        $('#modalFormInsuranceGrade .modal-title').html('<strong>Add</strong> '+$('#lblFormNameC').html());
        
        fnPreFillFormValuesInsuranceGrade ('');
        
        $('#viewFormInsuranceGrade, #editInViewInsuranceGrade').hide();
        $('#editFormInsuranceGrade, #formActionInsuranceGrade').show();
    });
    
    //View Insurance Grade
    $('#viewInsuranceGrade, #viewContextInsuranceGrade').on('click', function () {
        var selectedRow = fnGetSelected ( tableInsuranceGrade );
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        if (selectedRow.length)
        {
            var record = tableInsuranceGrade.fnGetData (selectedRow[0]);
            
            if (record.Insurance_Grade_Id > 0)
            {
                $('#modalFormInsuranceGrade .modal-title').html('<strong>View</strong> '+$('#lblFormNameC').html());
                
                $('#viewIGInsuranceType').text(record.Insurance_Name);
                $('#viewIGGrade').text(record.Grade_Name);
                $('#viewIGOrgShareAmount').text( fnCheckNull(record.Org_ShareAmount));
                $('#viewIGOrgSharePercent').text( fnCheckNull(record.Org_SharePercent));
                $('#viewIGEmpSharePercent').text( fnCheckNull(record.Emp_SharePercent));
                $('#viewIGDescription').text( fnCheckNull(record.Description));
                
                /**
                 *  Prefill addional information values based on record with form name
                */
                fnPreFillAdditionalPanel ('InsuranceGrade', record);
                
                $('#modalFormInsuranceGrade').modal('toggle');
                $('#editFormInsuranceGrade, #formActionInsuranceGrade').hide();
                $('#viewFormInsuranceGrade, #editInViewInsuranceGrade').show();
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+$('#lblFormNameC').html()+' record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameC').html()+' record', type : 'info' });
        }
    });
    
    //Edit Insurance Grade
    $('#editInsuranceGrade, #editContextInsuranceGrade, #editInViewInsuranceGrade').on('click', function () {
        var selectedRow = fnGetSelected (tableInsuranceGrade );
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        if (selectedRow.length)
        {
            var record = tableInsuranceGrade.fnGetData (selectedRow[0]);
                insuranceGradeId = record.Insurance_Grade_Id;
                
            if (insuranceGradeId > 0 && !isNaN(insuranceGradeId))
            {
                var buttonId = $(this).prop('id');
                
                setLock ({
                    'formName' : 'Premium Contribution',
                    'uniqueId' : insuranceGradeId,
                    'callback' : function (result)
                    {
                        if (buttonId != 'editInViewInsuranceGrade')
                            $('#modalFormInsuranceGrade').modal('toggle');
                        
                        $('#modalFormInsuranceGrade .modal-title').html('<strong>Edit</strong> '+$('#lblFormNameC').html());
                        
                        fnPreFillFormValuesInsuranceGrade (record);
                        
                        $('#editInViewInsuranceGrade, #viewFormInsuranceGrade').hide();
                        $('#editFormInsuranceGrade, #formActionInsuranceGrade').show();
                    }
                });
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+$('#lblFormNameC').html()+' record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameC').html()+' record', type : 'info' });
        }
    });
    
    //Trigger delete confirmation popup in delete menu in context menu
    $('#deleteInsuranceGrade, #deleteContextInsuranceGrade').on('click', function () {
        var selectedRow = fnGetSelected (tableInsuranceGrade );
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        if (selectedRow.length)
        {
            var insuranceGradeId = tableInsuranceGrade.fnGetData (selectedRow[0]).Insurance_Grade_Id;
            
            if (insuranceGradeId > 0 && !isNaN(insuranceGradeId))
            {
                $('#modalDeleteInsuranceGrade').modal('toggle');
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+$('#lblFormNameC').html()+' record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameC').html()+' record', type : 'info' });
        }
    });
    
    //Delete Insurance Grade
    $('#deleteConfirmInsuranceGrade').on('click', function () {
        var selectedRow = fnGetSelected (tableInsuranceGrade );
        
        if (selectedRow.length)
        {
            var insuranceGradeId = tableInsuranceGrade.fnGetData (selectedRow[0]).Insurance_Grade_Id;
            
            if (insuranceGradeId > 0 && !isNaN(insuranceGradeId))
            {
                $.ajax ({
                    type     : 'POST',
                    dataType : 'json',
                    async    : false,
                    url      : pageUrl () +'payroll/insurance/delete-insurancegrade/insuranceGradeId/'+ insuranceGradeId,
                    success  : function (result)
                    {
                        if (isJson (result))
                        {
                            if (result.success)
                            {
                                fnRefreshTable (tableInsuranceGrade);
                            }
                            
                            jAlert ({ msg : result.msg, type : result.type });
                        }
                        else
                        {
                            sessionExpired ();
                        }
                    }
                });
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+$('#lblFormNameC').html()+' record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameC').html()+' record', type : 'info' });
        }
    });
    
    //Form reset - Insurance Grade
    $('#formResetInsuranceGrade').on('click', function () {
        var l = Ladda.create(this);
        
        l.start();
        
        if ($('#formInsuranceGradeId').val() > 0)
        {
            fnPreFillFormValuesInsuranceGrade (tableInsuranceGrade.fnGetData (fnGetSelected (tableInsuranceGrade)[0]));
        }
        else
        {
            fnPreFillFormValuesInsuranceGrade ('');
        }
        
        l.stop();
    });
    
    //Form submit - Insurance Grade
    $('#formSubmitInsuranceGrade').on('click', function () {
        var l = Ladda.create(this);
        
        l.start();
        
        $("#s2id_editformIGInsType, #s2id_editformIGGrade, #s2id_editformIGCoverage").removeClass('form-error');
        
        if (isDirtyFormInsuranceGrade)
        {
            if ($("#editFormInsuranceGrade").valid())
            {
                if ($('#s2id_editformIGCoverage').select2('val') == 0)
                {
                    orgShareAmount  = $('#editformIGOrgShareAmount').val();
                    orgSharePercent = empSharePercent = 'NULL';
                }
                else
                {
                    orgShareAmount  = 'NULL';
                    orgSharePercent = $('#editformIGOrgSharePercent').val();
                    empSharePercent = $('#editformIGEmpSharePercent').val();
                }
                
                $.ajax ({
                    type     : 'POST',
                    dataType : 'json',
    //              async    : false,
                    url      : pageUrl () +'payroll/insurance/update-insurancegrade',
                    data     : {
                        insuranceGradeId : $('#formInsuranceGradeId').val(),
                        insuranceTypeId  : $('#s2id_editformIGInsType').select2('val'),
                        grade            : $('#s2id_editformIGGrade').select2('val'),
                        coverage         : $('#s2id_editformIGCoverage').select2('val'),                       
                        orgShareAmount   : orgShareAmount,
                        orgSharePercent  : orgSharePercent,
                        empSharePercent  : empSharePercent,
                        description      : $('#editformIGDescription').val()
                    },
                    success  : function (result)
                    {
                        if (isJson (result))
                        {
                            if (result.success)
                            {
                                isDirtyFormInsuranceGrade = false;
                                
                                $('#modalFormInsuranceGrade').modal('toggle');
                                
                                fnRefreshTable (tableInsuranceGrade);
                                
                                jAlert ({ msg : result.msg, type : result.type });
                            }
                            else
                            {
                                jAlert ({ panel : $('#editFormInsuranceGrade'), msg : result.msg, type : result.type });
                            }
                        }
                        else
                        {
                            sessionExpired ();
                        }
                        
                        l.stop();
                    }
                });    
            }
            else
            {
                l.stop();
            }
        }
        else
        {
            l.stop();
            jAlert ({ panel : $('#editFormInsuranceGrade'), msg : 'Form has no changes', type : 'info' });
        }
    });
    
    // On form field change
    $('#editFormInsuranceGrade').on('change', function() {
        isDirtyFormInsuranceGrade = true;
    });
    
    /**
     *  Add,Edit form modal hide event
    */
    $('#modalFormInsuranceGrade').on('hide.bs.modal', function (e) {
        if (isDirtyFormInsuranceGrade)
        {
            e.preventDefault();
            e.stopImmediatePropagation();
           
            $('#modalDirtyInsuranceGrade').modal('toggle');
        }
        else
        {
            fnCloseFormInsuranceGrade (false);
        }
    });
    
    /**
     *  click to close add/edit modal
    */
    $('#editCloseConfirmInsuranceGrade').on('click', function () {
        isDirtyFormInsuranceGrade = false;
        
        fnCloseFormInsuranceGrade (true);
    });
    
    //On insurance grade coverage value change
    $('#editformIGCoverage').on('change', function(){
        //$('#editformIGEmpSharePercent, #editformIGOrgSharePercent, #editformIGOrgShareAmount').val(0);
        
        if ($(this).val() == 1)
        {
            $('.premiumFixedCoverageBasedHidden').show();
            $('.premiumVariableCoverageBasedHidden').hide();
            $('#editformIGEmpSharePercent,#editformIGOrgSharePercent').val('');
            $('#editformIGEmpSharePercent,#editformIGOrgSharePercent').valid();

            $('#editformIGEmpSharePercent').prop({'min' : 0, 'max' : 100});
            $('#editformIGOrgSharePercent').prop({'min' : 0, 'max' : 100});
            $('#editformIGOrgShareAmount').removeAttr('min');
            
            $('#editformIGEmpSharePercent, #editformIGOrgSharePercent').addClass('vRequired');
            $('#editformIGOrgShareAmount').removeClass('vRequired');
        }
        else
        {
            $('.premiumVariableCoverageBasedHidden').show();
            $('.premiumFixedCoverageBasedHidden').hide();
            
            $('#editformIGEmpSharePercent, #editformIGOrgSharePercent').removeAttr('min');
            $('#editformIGEmpSharePercent, #editformIGOrgSharePercent').removeAttr('max');
            
            $('#editformIGOrgShareAmount').val('');
            $('#editformIGOrgShareAmount').valid();

            $('#editformIGOrgShareAmount').prop('min', 0);
            
            $('#editformIGOrgShareAmount').addClass('vRequired');
            $('#editformIGEmpSharePercent, #editformIGOrgSharePercent').removeClass('vRequired');
            
            $('#editformIGOrgSharePercent').removeClass('varSharePercentError');
        }
        
        $('#editformIGEmpSharePercent, #editformIGOrgSharePercent, #editformIGOrgShareAmount').removeClass('error');
    });
    
    //On organization and employee share percent change validation - sum of their value should not exceed 100.
    $('#editformIGEmpSharePercent, #editformIGOrgSharePercent').on('change', function(){
        var empSharePercent = $('#editformIGEmpSharePercent').val();
        var orgSharePercent = $('#editformIGOrgSharePercent').val();
        
        $('#editformIGOrgSharePercent').removeClass("varSharePercentError");
        
        if ($('#s2id_editformIGCoverage').select2('val') == 1)
        {
            if (empSharePercent != '' && orgSharePercent != '')
            {
                var sumofShare = parseFloat(empSharePercent) + parseFloat(orgSharePercent) ; 
                if ((sumofShare <= 0) || (sumofShare > 100))
                {
                    $('#editformIGOrgSharePercent').addClass("varSharePercentError");
                    $.validator.addMethod('vVarSharePercentError', function () { return false; }, 'Sum of employee and organization share(%) should be greater than zero and less than are equal to 100');
                    $.validator.addClassRules("varSharePercentError", { vVarSharePercentError : true });
                }
            }
        }
        
    });
    
    /** Filter Form **/
    $('#filterInsuranceGrade,#closeFilterInsuranceGrade').on('click', function(){
        if ($('#filterPanelInsuranceGrade').hasClass('open')){
            $('#filterPanelInsuranceGrade').removeClass('open');
            $('#filterPanelInsuranceGrade').hide();
        }
        else{
            $('#filterPanelInsuranceGrade').addClass('open');
            $('#filterPanelInsuranceGrade').show();
        }
    });
    
    /**
     *  Reset Filter Form
    */
    $('#filterResetInsuranceGrade,#closeFilterInsuranceGrade').on('click', function () {
        $('#filterIGGrade, #filterOrgShareAmountStart, #filterOrgShareAmountEnd, #filterOrgSharePercentStart, #filterOrgSharePercentEnd, #filterEmpSharePercentStart, #filterEmpSharePercentEnd').val('');
        
        $('#s2id_filterIGInsuranceTypeId').select2('val', '');
       
        tableInsuranceGrade.fnReloadAjax( pageUrl () + "payroll/insurance/list-insurance-grade" );
    });
    
    /**
     *  Apply filter to insurance grade grid
    */
    $('#filterApplyInsuranceGrade').on('click', function () {
        ftInsuranceGrade       = $('#filterIGGrade').val();
        ftInsuranceTypeId      = $('#s2id_filterIGInsuranceTypeId').select2('val');
        ftOrgShareAmountStart  = $('#filterOrgShareAmountStart').val();
        ftOrgShareAmountEnd    = $('#filterOrgShareAmountEnd').val();
        ftOrgSharePercentStart = $('#filterOrgSharePercentStart').val();
        ftOrgSharePercentEnd   = $('#filterOrgSharePercentEnd').val();
        ftEmpSharePercentStart = $('#filterEmpSharePercentStart').val();
        ftEmpSharePercentEnd   = $('#filterEmpSharePercentEnd').val();
        
        tableInsuranceGrade.fnReloadAjax(pageUrl () +'payroll/insurance/list-insurance-grade/insuranceType/'+ ftInsuranceTypeId
                                     +'/grade/'+ ftInsuranceGrade +'/orgSharePercentStart/'+ ftOrgSharePercentStart
                                     +'/orgSharePercentEnd/'+ ftOrgSharePercentEnd +'/orgShareAmountStart/'+ ftOrgShareAmountStart
                                     +'/orgShareAmountEnd/'+ ftOrgShareAmountEnd +'/empSharePercentStart/'+ ftEmpSharePercentStart
                                     +'/empSharePercentEnd/'+ ftEmpSharePercentEnd);
    });
    
    /**
     *  When form isn't dirty then clear the lock flag and then close that add/edit form
    */
    function fnCloseFormInsuranceGrade (hideAction) {
        var insuranceGradeId = $('#formInsuranceGradeId').val();
        
        if (insuranceGradeId > 0 && !isNaN(insuranceGradeId))
        {
            clearLock ({
                'formName' : 'Premium Contribution',
                'uniqueId' : insuranceGradeId,
                'callback' : function ()
                {
                    $('#formInsuranceGradeId').val(0);
                    
                    if (hideAction)
                        $('#modalFormInsuranceGrade').modal('hide');
                }
            });
        }
        else
        {
            if (hideAction)
                $('#modalFormInsuranceGrade').modal('hide');
        }
    }
    
    /**
     *  Prefill insurance grade form values in add, edit, reset events
    */
    function fnPreFillFormValuesInsuranceGrade (record) {
        $("#s2id_editformIGInsType, #s2id_editformIGGrade, #s2id_editformIGCoverage").removeClass('form-error');
        
        var insuranceGradeField = $('#editformIGInsType');
        
        insuranceGradeField.find('option').remove();
        
        if (record != '')
        {
            $('#formInsuranceGradeId').val(record.Insurance_Grade_Id);
            
            insuranceGradeField.append('<option value=' + record.InsuranceType_Id + ' selected >'+ record.Insurance_Name +'</option>');
            
            insuranceGradeField.select2('val', record.InsuranceType_Id);
            
            $('#s2id_editformIGGrade').select2("val",(record.Grade_Ids).split(','));
           
            if (record.Org_ShareAmount != null)
            {
                $('#s2id_editformIGCoverage').select2('val', 0);
                
                $('#editformIGCoverage').trigger('change');
                
                $('#editformIGOrgShareAmount').val(record.Org_ShareAmount).prop('readOnly', true);
            }
            else
            {
                $('#s2id_editformIGCoverage').select2('val', 1);
                
                $('#editformIGCoverage').trigger('change');
                
                $('#editformIGEmpSharePercent').val(record.Emp_SharePercent);
                $('#editformIGOrgSharePercent').val(record.Org_SharePercent);
            }
            
            $('#editformIGCoverage').prop('readOnly', true);
            
            $('#editformIGDescription').val(record.Description);
        }
        else
        {
            $('#editformIGCoverage, #editformIGOrgShareAmount').prop('readOnly', false);
            
            $('#formInsuranceGradeReset').trigger('click');
            
            $('#formInsuranceGradeId').val(0);
            
            $('#s2id_editformIGGrade').select2('val', 0);
            
            var insuranceTypeOption = ['<option value="">--Select--</option>'];
            
            $.ajax ({
                type     : 'POST',
                dataType : 'json',
                async    : false,
                url      : pageUrl () +'payroll/insurance/get-active-insurance-type',
                success  : function (result)
                {
                    if (isJson (result))
                    {
                        for (var i in result)
                        {
                            insuranceTypeOption.push('<option value=' + i + '>'+ result[i] +'</option>');
                        }
                    }
                    else
                    {
                        sessionExpired ();
                    }
                }
            });    
            
            $('#editformIGInsType').append(insuranceTypeOption);
            
            $('#s2id_editformIGInsType').select2('val', '');
            
            $('#s2id_editformIGCoverage').select2('val', 0);
            
            $('#editformIGCoverage').trigger('change');
        }
        
        $( "#editFormInsuranceGrade").validate().resetForm();
        
        isDirtyFormInsuranceGrade = false;
    }
    
    /* Set enable / disable toolbar icons */
    function fnActionButtonsInsuranceGrade (action) {
        fnGridButtons ($('#viewInsuranceGrade, #editInsuranceGrade, #deleteInsuranceGrade'), action);
    }
    
    /*************************************** Payment Tracker ************************************************************************/
    
    /*  Initialse DataTables, with no sorting on the 'details' column  */
    var tablePaymentTracker = $('#tablePaymentTracker').dataTable({
       "iDisplayLength" : 10,
       "lengthMenu"     : [ 5, 10, 25, 50, 100 ], 
       "bDestroy"       : true,
       "bAutoWidth"     : false,
       "bServerSide"    : true,
       "bDeferRender"   : true,
       "sServerMethod"  : 'POST',
       "sAjaxSource"    : pageUrl () + 'payroll/insurance/list-insurance-payment-tracker',
       "sAjaxDataProp"  : 'aaData',
        "aoColumnDefs"   : [{"targets": 0, "orderable": false},
                            { "sClass" : "visible-xs visible-sm  visible-md hidden-lg", "aTargets" : [0] },
                            { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [3] },
                            { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [4] },
                            { "sClass" : "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets" : [5] },
                            { "sClass" : "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets" : [6] },
                            { "sClass" : "hidden-xs hidden-sm  hidden-md visible-lg", "aTargets" : [7] }],
       "aaSorting"      : [[1, 'desc']],
       "fnCreatedRow": function( nRow, aData, iDataIndex ) {
        $(nRow).attr({"data-toggle":"context", "data-target":"#context-menu4" });
       },
       "aoColumns"      : [{
            "mData" : function (row, type, set) {
                return '<i class="fa fa-plus-square-o"></i>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div>'+ row['Salary_Month_Val'] +'</div>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div>'+ row['Insurance_Name'] +'</div>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div>'+ row['Emp_ShareAmount'] +'</div>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div>'+ row['Org_ShareAmount'] +'</div>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div>'+ row['Outstanding_Amt'] +'</div>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div>'+ fnCheckNull(row['Amount_Paid']) +'</div>';
            }
        },
        {
           "mData" : function (row, type, set) {
               return '<div>'+ row['Payment_Status'] +'</div>';
            }
        }]
    });
    
    //On + icon click in mobile & tablet view
    $(document).on('click', '#tablePaymentTracker i', function () {
        var nTr = $(this).parents('tr')[0];
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        if ( tablePaymentTracker.fnIsOpen(nTr) )
        {
            /* This row is already open - close it */
            $(this).removeClass().addClass('fa fa-plus-square-o');
            tablePaymentTracker.fnClose(nTr);
        }
        else
        {
            var record = tablePaymentTracker.fnGetData( nTr );
            var nRow =  $('#tablePaymentTracker thead tr')[0];
            
            /* Open this row */
            $(this).removeClass().addClass('fa fa-minus-square-o');
            
            valueArray = [];
            headerArray=[];
        
            valueArray.Value_One = record.Emp_ShareAmount;
            valueArray.Value_Two = record.Org_ShareAmount;
            valueArray.Value_Three = record.Outstanding_Amt;
            valueArray.Value_Four = record.Amount_Paid;
            valueArray.Value_Five = record.Payment_Status;
            
            $.each(nRow.cells, function(i,v) {
                headerArray['Header'+i] = v.innerText;
            });
            
            tablePaymentTracker.fnOpen(nTr, fnDeviceColumnDetails(headerArray,valueArray), 'details hidden-lg');
        }
    });
    
    /*  Add event listener for select and unselect details  */
    $(document).on('click contextmenu', '#tablePaymentTracker tbody td div', function () {
        var selectRow = $(this).parent().parent();
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        tablePaymentTracker.$('tr.row_selected').removeClass('row_selected');
        
        if (!selectRow.hasClass('row_selected'))
        {
            selectRow.addClass('row_selected');
            
            fnActionButtonsPaymentTracker (true);
        }
        else
        {
            fnActionButtonsPaymentTracker (false);
        }
    });
    
    /**
     *  Grid Refresh
    */
    $('#gridPanelPaymentTracker .panel-reload').on('click', function () {
        fnRefreshTable (tablePaymentTracker);
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
    });
    
    // On whole grid filter event (to disable action icon)
    $('#tablePaymentTracker').on('draw.dt', function () {
        fnActionButtonsPaymentTracker (false);
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
    });
    
    //View Payment Tracker
    $('#viewPaymentTracker, #viewContextPaymentTracker').on('click', function () {
        var selectedRow = fnGetSelected ( tablePaymentTracker );
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        if (selectedRow.length)
        {
            var record = tablePaymentTracker.fnGetData (selectedRow[0]);
                paymentId = record.Payment_Id;
                
            if (paymentId > 0)
            {
                $('#modalFormPaymentTracker .modal-title').html('<strong>View</strong> '+$('#lblFormNameD').html());
                
                $('#viewPTPayslipMonth').text(record.Salary_Month_Val);
                $('#viewPTEmpShareAmount').text(record.Emp_ShareAmount);
                $('#viewPTOrgShareAmount').text(record.Org_ShareAmount);
                $('#viewPTTotalAmount').text( record.Outstanding_Amt );
                $('#viewPTStatus').text(record.Payment_Status);
                
                tablePaymentTrackerSubGridView.fnReloadAjax( pageUrl () + "payroll/insurance/list-payment-tracker-subgrid/paymentId/" + paymentId );
                
                if (record.Payment_Status == 'Unpaid'|| record.Payment_Status == 'Partially Paid')
                {
                    $('#editInViewPaymentTracker').show();
                }
                else
                {
                    $('#editInViewPaymentTracker').hide();
                }
                
                $('#viewFormPaymentTracker').show();
                $('#editFormPaymentTracker, #paymentTrackerGrid, #addPaymentTracker').hide();
                $('#modalFormPaymentTracker').modal('toggle');
                
                $('#tablePaymentTrackerSubGridView').parent().removeClass('force-table-responsive');
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+$('#lblFormNameD').html()+' record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameD').html()+' record', type : 'info' });
        }
    });
    
    //Edit Payment Tracker
    $('#editPaymentTracker, #editContextPaymentTracker, #editInViewPaymentTracker').on('click', function () {
        var selectedRow = fnGetSelected (tablePaymentTracker );
        
        fnFilterClose ('FixedInsurance');
        fnFilterClose ('VariableInsurance');
        fnFilterClose ('PaymentTracker');
        fnFilterClose ('InsuranceType');
        fnFilterClose ('InsuranceGrade');
        
        if (selectedRow.length)
        {
            var buttonId = $(this).prop('id');
            
            var record    = tablePaymentTracker.fnGetData (selectedRow[0]);
                paymentId = record.Payment_Id;
                
            if (paymentId > 0 && !isNaN(paymentId))
            {
                // force-table-responsive class is removed because when we open form scroll bar is set
                $('#tablePaymentTrackerSubGrid').parent().removeClass('force-table-responsive');
                
                // Lock is not set becoz no lock flag to main grid
                
                $('#modalFormPaymentTracker .modal-title').html('<strong>Edit</strong> '+$('#lblFormNameD').html());
                
                $('#formPaymentTrackerPayslipMonth').html(record.Salary_Month_Val);
                $('#formPTPayslipMonth').val(record.Salary_Month);
                $('#formPaymentTrackerInsuranceName').html(record.Insurance_Name);
                $('#formPaymentTrackerEmpShareAmount').html(record.Emp_ShareAmount);
                $('#formPaymentTrackerOrgShareAmount').html(record.Org_ShareAmount);
                $('#formPaymentTrackerTotalAmount').html(record.Outstanding_Amt);
                $('#formPaymentTrackerStatus').html(record.Payment_Status);
                //$('#formPaymentTrackerOutstandingAmount').html((record.Bal_Amount != null) ? record.Bal_Amount : record.Total_Amount );
                $('#formPaymentTrackerOutstandingAmount').html((record.Bal_Amount != null) ? record.Bal_Amount : record.Outstanding_Amt );
                
                fnPreFillFormValuesPaymentTracker (record, '', 0);
                
                $.ajax ({
                    type     : 'POST',
                    async    : false,
                    dataType : 'json',
                    url      : pageUrl() + 'default/employee-info/get-payment-tracker-date/salaryMonth/'+record.Salary_Month_Val,
                    success  : function (result)
                    {
                        if (isJson (result))
                        {
                            if(result != null)
                            {
                                $('#formInsPaymentTrackerPaymentDate').datepicker('option', 'minDate',result);
                            }
                            else{
                                $('#formInsPaymentTrackerPaymentDate').datepicker('option', 'minDate',new Date(tzDate()));
                            }
                        }
                        else{
                            sessionExpired ();
                        }
                    }
                });
                
                $('#editInViewPaymentTracker, #viewFormPaymentTracker').hide();
                $('#editFormPaymentTracker, #paymentTrackerGrid, #addPaymentTracker').show();
                
                if (buttonId != 'editInViewPaymentTracker')
                    $('#modalFormPaymentTracker').modal('toggle');
                    
                if (!($("#onAddCollapse").hasClass('collapsed')))
                {
                    $( "#onAddCollapse" ).trigger( "click" );
                }
                
                if ($("#taxRulesPaymentSummaryPanel").hasClass('collapsed'))
                {
                    $( "#taxRulesPaymentSummaryPanel" ).trigger( "click" );
                }
            }
            else
            {
                jAlert ({ msg : 'Kindly select '+$('#lblFormNameD').html()+' record', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select '+$('#lblFormNameD').html()+' record', type : 'info' });
        }
    });
    
    $('#formPaymentTrackerAmountPaid').on('change',function(){
        $('#formPaymentTrackerAmountPaid').removeClass("amountError");
        
        var balanceAmount =  $('#formPaymentTrackerOutstandingAmount').html();

        if($('#formPaymentTrackerId').val()>0){
            var subGridRecord = tablePaymentTrackerSubGrid.fnGetData (fnGetSelected (tablePaymentTrackerSubGrid)[0]);
           
            balanceAmount = parseFloat(balanceAmount) + parseFloat(subGridRecord.Amount_Paid);
           
            $('#formPaymentTrackerAmountPaid').prop({'max':balanceAmount});            
        }
        else{
            if(balanceAmount >= 0)
            {
                $('#formPaymentTrackerAmountPaid').prop({'max':balanceAmount});
            }
            else{
                $('#formPaymentTrackerAmountPaid').addClass("amountError");
                
                $.validator.addMethod('amountError', function () { return false; }, 'There is no outstanding amount to be paid');
                    
                $.validator.addClassRules("amountError", { amountError : true });
            }
        }        
    });
    
    //Form reset - Payment tracker
    $('#formResetPaymentTracker').on('click', function (e) {
        var l = Ladda.create(this);
        
        l.start();
        
        var mainGridRecord = tablePaymentTracker.fnGetData (fnGetSelected (tablePaymentTracker)[0]);
        
        var paymentTrackerId = $('#formPaymentTrackerId').val();
        
        if (paymentTrackerId > 0)
        {
            var record = tablePaymentTrackerSubGrid.fnGetData (tablePaymentTrackerSubGrid.$('tr.paymentTracker_editing_row'));
                
            if(tablePaymentTrackerSubGrid.$('tr.paymentTracker_editing_row').length == 1)
            {
                fnPreFillFormValuesPaymentTracker (mainGridRecord, record, 1);
            }
            else
            {
                var subGridRecord = tablePaymentTrackerSubGrid.fnGetData (fnGetSelected (tablePaymentTrackerSubGrid)[0]);
                
                if(subGridRecord != '')
                {
                    fnPreFillFormValuesPaymentTracker (mainGridRecord, subGridRecord, 1);
                }
                else
                {
                    fnPreFillFormValuesPaymentTracker (mainGridRecord, '', 0);
                }
            }
        }
        else
        {
            fnPreFillFormValuesPaymentTracker (mainGridRecord, '', 0);
            
            e.preventDefault();
        }
        
        l.stop();
    });
    
    //Form submit - payment tracker
    $('#formSubmitPaymentTracker').on('click', function (e) {
        var l = Ladda.create(this);
       
        l.start();
        
        if (isDirtyFormPaymentTracker)
        {
            $("#s2id_formPaymentTrackerModeOfPayment").removeClass('form-error');
            
            if ($("#editFormPaymentTracker").valid())
            {
                if (($('#formPaymentTrackerStatus'). html()) == "Unpaid")
                {
                    balanceAmount = $('#formPaymentTrackerTotalAmount').html();
                }
                else
                {
                    balanceAmount = $('#formPaymentTrackerOutstandingAmount').html();
                }
                
                $.ajax ({
                    type     : 'POST',
                    dataType : 'json',
    //                async    : false,
                    url      : pageUrl () +'payroll/insurance/edit-insurance-payment',
                    data     : {
                        salaryMonth      : $('#formPTPayslipMonth').val(),
                        balanceAmount    : balanceAmount,
                        totalAmount      : $('#formPaymentTrackerTotalAmount').html(),
                        paymentId        : $('#formPaymentTrackerPaymentId').val(),
                        paymentTrackerId : $('#formPaymentTrackerId').val(),
                        paymentMode      : $('#s2id_formPaymentTrackerModeOfPayment').select2('val'),
                        paymentDate      : fnServerDateFormatter ($('#formInsPaymentTrackerPaymentDate').datepicker('getDate')),
                        documentNo       : $('#formPaymentTrackerDocumentNo').val(),
                        bankName         : $('#formPaymentTrackerBankName').val(),
                        branchName       : $('#formPaymentTrackerBranchName').val(),
                        amountPaid       : $('#formPaymentTrackerAmountPaid').val(),
                        description      : $('#formPaymentTrackerDescription').val(),
                    },
                    success  : function (result)
                    {
                        if (isJson (result))
                        {
                            if (result.success)
                            {
                                lockClear = 0;
                                
                                if ($('#formPaymentTrackerId').val() > 0)
                                {
                                    clearLock ({
                                        'formName' : 'Insurance Payment Tracker',
                                        'uniqueId' :  $('#formPaymentTrackerId').val(),
                                        'callback' : function ()
                                        {
                                            lockClear = 1;
                                        }
                                    });
                                }
                                else
                                {
                                    lockClear = 1;
                                }
                                
                                if (lockClear == 1)
                                {
                                    $("#formSubmitPaymentTracker").html('<i class="mdi-content-send"></i> Add');
                                    
                                    $('#formPaymentTrackerId').val(0);
                                    
                                    $('#formResetPaymentTracker').trigger('click');
                                    
                                    $('#formPaymentTrackerOutstandingAmount').html(fnCheckNull(result.Bal_Amount));
                                    $('#formPaymentTrackerStatus').html(result.status);
                                    
                                    isDirtyFormPaymentTracker = false;
                                    
                                    if(result.status=='Paid'){                                        
                                        $('#modalFormPaymentTracker').modal('toggle');
                                        
                                        fnRefreshTable (tablePaymentTracker);
                                        
                                        jAlert ({ msg : result.msg, type : result.type });
                                    }
                                    else{                                    
                                        tablePaymentTrackerSubGrid.fnReloadAjax( pageUrl () + "payroll/insurance/list-payment-tracker-subgrid/paymentId/" + $('#formPaymentTrackerPaymentId').val() );
                                       
                                        fnModalScrollTop('modalFormPaymentTracker');
                                       
                                        jAlert ({ panel : $('#editFormPaymentTracker'), msg : result.msg, type : result.type });                                        
                                    }
                                    
                                    $('#ui-datepicker-div').hide();
                                    
                                    e.preventDefault();
                                }
                            }
                            else
                            {
                                fnModalScrollTop('modalFormPaymentTracker');
                                jAlert ({ panel : $('#editFormPaymentTracker'), msg : result.msg, type : result.type });
                            }
                        }
                        else
                        {
                            sessionExpired ();
                        }
                        
                        l.stop();
                    }
                });    
            }
            else
            {
                l.stop();
            }
        }
        else
        {
            l.stop();
            fnModalScrollTop('modalFormPaymentTracker');
            setTimeout(function() { $('#ui-datepicker-div').hide(); },50);
            jAlert ({ panel : $('#editFormPaymentTracker'), msg : 'Form has no changes', type : 'info' });
        }
    });
    
    // On payment mode change
    $('#formPaymentTrackerModeOfPayment').on('change', function (){
        var paymentModeId = $('#formPaymentTrackerModeOfPayment').select2('val');
        
        $('#editFormPaymentTracker').validate().resetForm();
        
        //paymentMode id for Cash,Debit Card,Credit Card
        if (paymentModeId != 1 && paymentModeId != 4 && paymentModeId != 5 && paymentModeId != 6)
        {
            $('.paymentModeBasedHidden').show();
            $('#formPaymentTrackerDocumentNo, #formPaymentTrackerBankName, #formPaymentTrackerBranchName').addClass('vRequired');            
        }
        else
        {
            $('.paymentModeBasedHidden').hide();
            $('#formPaymentTrackerDocumentNo, #formPaymentTrackerBankName, #formPaymentTrackerBranchName').removeClass('vRequired');
        }
        
        $('#formPaymentTrackerDocumentNo, #formPaymentTrackerBankName, #formPaymentTrackerBranchName').val('');
    });
    
    /** Filter Form **/
    $('#filterPaymentTracker,#closeFilterPaymentTracker').on('click', function(){
        if ($('#filterPanelPaymentTracker').hasClass('open')){
            $('#filterPanelPaymentTracker').removeClass('open');
            $('#filterPanelPaymentTracker').hide();
        }
        else{
            $('#filterPanelPaymentTracker').addClass('open');
            $('#filterPanelPaymentTracker').show();
        }
    });
    
    /**
     *  Reset Filter Form
    */
    $('#filterResetPaymentTracker,#closeFilterPaymentTracker').on('click', function () {
        $('#filterPTSalaryMonth, #filterPTEmpShareAmountStart, #filterPTEmpShareAmountEnd, #filterPTOrgShareAmountStart,'+
          '#filterPTOrgShareAmountEnd, #filterPTTotalAmountStart, #filterPTTotalAmountEnd, #filterPTAmountPaidStart, #filterPTAmountPaidEnd').val('');
        $('#s2id_filterPTStatus, #s2id_filterPTInsuranceTypeId').select2('val', '');
        
        tablePaymentTracker.fnReloadAjax( pageUrl () + "payroll/insurance/list-insurance-payment-tracker" );
    });
    
    /**
     *  Apply filter to payment tracker grid
    */
    $('#filterApplyPaymentTracker').on('click', function () {
        ftSalaryMonth         = fnServerMonthFormatter($('#filterPTSalaryMonth').val());
        ftInsuranceTypeId     = $('#s2id_filterPTInsuranceTypeId').select2('val');
        ftEmpShareAmountStart = $('#filterPTEmpShareAmountStart').val();
        ftEmpShareAmountEnd   = $('#filterPTEmpShareAmountEnd').val();
        ftOrgShareAmountStart = $('#filterPTOrgShareAmountStart').val();
        ftOrgShareAmountEnd   = $('#filterPTOrgShareAmountEnd').val();
        ftTotalAmountStart    = $('#filterPTTotalAmountStart').val();
        ftTotalAmountEnd      = $('#filterPTTotalAmountEnd').val();
        ftAmountPaidBegin     = $('#filterPTAmountPaidStart').val();
        ftAmountPaidEnd       = $('#filterPTAmountPaidEnd').val();
        ftStatus              = $('#s2id_filterPTStatus').select2('val');
        
        tablePaymentTracker.fnReloadAjax( pageUrl () + 'payroll/insurance/list-insurance-payment-tracker/salaryMonth/'+ ftSalaryMonth
                                         +'/insuranceTypeId/'+ ftInsuranceTypeId +'/empShareAmountStart/'+ ftEmpShareAmountStart
                                         +'/empShareAmountEnd/'+ ftEmpShareAmountEnd +'/orgShareAmountStart/'+ ftOrgShareAmountStart
                                         +'/orgShareAmountEnd/'+ ftOrgShareAmountEnd +'/amountPaidStart/'+ ftAmountPaidBegin
                                         +'/amountPaidEnd/'+ ftAmountPaidEnd +'/totalAmountStart/'+ ftTotalAmountStart
                                         +'/totalAmountEnd/'+ ftTotalAmountEnd +'/status/'+ ftStatus);
    });
    
    /* Set enable / disable toolbar icons */
    function fnActionButtonsPaymentTracker (action) {
        if (action)
        {
            fnGridButtons ($('#viewPaymentTracker'), true);
            
            var paymentStatus = tablePaymentTracker.fnGetData (fnGetSelected (tablePaymentTracker)[0]).Payment_Status;
            
            if (paymentStatus == 'Unpaid'|| paymentStatus == 'Partially Paid')
            {
                $('#editContextPaymentTracker').parent().show();
                fnGridButtons ($('#editPaymentTracker'), true);
            }
            else
            {
                $('#editContextPaymentTracker').parent().hide();
                fnGridButtons ($('#editPaymentTracker'), false);
            }
        }
        else
        {
            fnGridButtons ($('#viewPaymentTracker, #editPaymentTracker'), action);
        }
    }
    
    /*************************************** Payment Tracker sub Grid ************************************************************************/
        
    /*  Initialse DataTables, with no sorting on the 'details' column  */
    tablePaymentTrackerSubGridView = $('#tablePaymentTrackerSubGridView').dataTable({
        "iDisplayLength" : 10,
        "lengthMenu"     : [ 5, 10, 25, 50, 100 ], 
        "bDestroy"       : true,
        "bAutoWidth"     : false,
        "bServerSide"    : true,
        "bDeferRender"   : true,
        "sServerMethod"  : 'POST',
        "sAjaxSource"    : pageUrl () + 'payroll/insurance/list-payment-tracker-subgrid',
        "sAjaxDataProp"  : 'aaData',
        "info"           : false,
        "bPaginate"      : false, // Remove pagination
        "bFilter"        : false,
        "aaSorting"      : [],
        "aoColumnDefs"   : [{ 'bSortable': false, 'aTargets': ['_all'] },
                            { "sClass" : "hidden-xs hidden-sm visible-md visible-lg", "aTargets" : [3] },
                            { "sClass" : "hidden-xs hidden-sm visible-md visible-lg", "aTargets" : [4] }],
        "aoColumns"      : [{
            "mData" : function (row, type, set) {
                return '<i class="fa fa-plus-square-o"></i>';
            }
        },
        {
            "mData" : function (row, type, set) {
                return '<div >'+ row['Payment_Type'] +'</div>';
            }
        },
        {
            "mData" : function (row, type, set) {
                return '<div class="text-center" >'+ row['Payment_Date'] +'</div>';
            }
        },
        {
            "mData" : function (row, type, set) {
                return '<div class="text-center" >'+ row['Amount_Paid'] +'</div>';
            }
        },
        {
            "mData" : function (row, type, set) {
                return '<div>'+ fnCheckNull(row['Description']) +'</div>';
            }
        }]
    });
    
    // Payment tracker view - On + button click - get more information by form expand
    $(document).on('click', '#tablePaymentTrackerSubGridView i', function () {
        var nTr = $(this).parents('tr')[0];
        
        if ( tablePaymentTrackerSubGridView.fnIsOpen(nTr) )
        {
            /* This row is already open - close it */
            $(this).removeClass().addClass('fa fa-plus-square-o');
            
            tablePaymentTrackerSubGridView.fnClose(nTr);
        }
        else
        {
            /* Open this row */
            $(this).removeClass().addClass('fa fa-minus-square-o');
            
            tablePaymentTrackerSubGridView.fnOpen(nTr, fnShowHiddenPaymentTrackerDetails(nTr), 'details');
        }
    });
    
    /*  Initialse DataTables, with no sorting on the 'details' column  */
    tablePaymentTrackerSubGrid = $('#tablePaymentTrackerSubGrid').dataTable({
        "iDisplayLength" : 10,
        "lengthMenu"     : [ 5, 10, 25, 50, 100 ], 
        "bDestroy"       : true,
        "bAutoWidth"     : false,
        "bServerSide"    : true,
        "bDeferRender"   : true,
        "sServerMethod"  : 'POST',
        "sAjaxSource"    : pageUrl () + 'payroll/insurance/list-payment-tracker-subgrid',
        "sAjaxDataProp"  : 'aaData',
        "info"           : false,
        "bPaginate"      : false, // Remove pagination
        "bFilter"        : false,
        "aaSorting"      : [],
        "aoColumnDefs"   : [{ 'bSortable': false, 'aTargets': ['_all'] },
                            { "sClass" : "visible-xs visible-sm  hidden-md hidden-lg", "aTargets" : [0] },
                            { "sClass" : "hidden-xs hidden-sm  visible-md visible-lg", "aTargets" : [4] }],
        "aoColumns"      : [{
            "mData" : function (row, type, set) {
                return '<i class="fa fa-plus-square-o"></i>';
            }
        },
        {
            "mData" : function (row, type, set) {
                if (tablePaymentTracker.fnGetData (fnGetSelected (tablePaymentTracker)[0]).Payment_Status != 'Paid')
                {
                    return '<div class="btn btn-sm btn-embossed btn-white editPaymentTrackerSubgrid" title="Edit" id="insPaymentTracker_'+row['Ins_Pay_Tracker_Id']+'"><i class="mdi-editor-mode-edit"></i></div>';
                }
            }
        },
        {
            "mData" : function (row, type, set) {
                return '<div >'+ row['Payment_Type'] +'</div>';
            }
        },
        {
            "mData" : function (row, type, set) {
                return '<div class="text-center" >'+ row['Payment_Date'] +'</div>';
            }
        },
        {
            "mData" : function (row, type, set) {
                return '<div class="text-center" >'+ row['Amount_Paid'] +'</div>';
            }
        }]
    });
    
    //On + icon click in mobile & tablet view
    $(document).on('click', '#tablePaymentTrackerSubGrid i', function () {
        var nTr = $(this).parents('tr')[0];
        
        if ( tablePaymentTrackerSubGrid.fnIsOpen(nTr) )
        {
            if($(this).hasClass('fa-minus-square-o'))
            {
                /* This row is already open - close it */
                $(this).removeClass().addClass('fa fa-plus-square-o');
                tablePaymentTrackerSubGrid.fnClose(nTr);
            }
        }
        else
        {
            /* Open this row */
            if($(this).hasClass('fa-plus-square-o'))
            {
                var record = tablePaymentTrackerSubGrid.fnGetData( nTr );
                var nRow =  $('#tablePaymentTrackerSubGrid thead tr')[0];
                
                $(this).removeClass().addClass('fa fa-minus-square-o');
                
                valueArray = [];
                headerArray=[];
            
                valueArray.Value_One = record.Amount_Paid;
    
                headerArray['Header3'] = 'Amount Paid';
                
                tablePaymentTrackerSubGrid.fnOpen(nTr, fnDeviceColumnDetails(headerArray,valueArray), 'details hidden-lg hidden-md');
            }
        }
    });
    
    /*  Add event listener for select and unselect details  */
    $(document).on('click','#tablePaymentTrackerSubGrid tbody td div', function () {
        var selectRow = $(this).parent().parent();
        
        tablePaymentTrackerSubGrid.$('tr.row_selected').removeClass('row_selected');
        
        if (!selectRow.hasClass('row_selected'))
        {
            selectRow.addClass('row_selected');
        }
    });
    
    // Edit Payment tracker subgrid
    $(document).on('click', '.editPaymentTrackerSubgrid', function(){
        $("#formSubmitPaymentTracker").html('<i class="mdi-content-send"></i> Update');
        
        var selectedRow = fnGetSelected ( tablePaymentTrackerSubGrid );
       
        if (selectedRow.length)
        {
            var mainGridRecord = tablePaymentTracker.fnGetData (fnGetSelected (tablePaymentTracker)[0]);
                record = tablePaymentTrackerSubGrid.fnGetData (selectedRow[0]);
            
            if (mainGridRecord.Payment_Status != 'Paid')
            {
                if (record.Ins_Pay_Tracker_Id > 0 && mainGridRecord.Payment_Id > 0) 
                {
                    clearLockFlag = 0;
                    
                    if ($('#formPaymentTrackerId').val() > 0)
                    {
                        clearLock ({
                            'formName' : 'Insurance Payment Tracker',
                            'uniqueId' :  $('#formPaymentTrackerId').val(),
                            'callback' : function ()
                            {
                                clearLockFlag = 1;
                                
                                tablePaymentTrackerSubGrid.$('tr.paymentTracker_editing_row').removeClass('paymentTracker_editing_row');
                                
                                $('#formPaymentTrackerId').val(0);
                                
                                fnPreFillFormValuesPaymentTracker (mainGridRecord, '', 1);
                            }
                        });
                    }
                    else
                    {
                        clearLockFlag = 1;
                    }
                    
                    if (clearLockFlag == 1)
                    {
                        setLock ({
                            'formName' : 'Insurance Payment Tracker',
                            'uniqueId' : record.Ins_Pay_Tracker_Id,
                            'panel'    : $('#editFormPaymentTracker'),
                            'callback' : function (result)
                            {
                                if ($("#onAddCollapse").hasClass('collapsed'))
                                {
                                    $( "#onAddCollapse" ).trigger( "click" );
                                }
                                
                                var rows = $('tbody td div#insPaymentTracker_'+record.Ins_Pay_Tracker_Id);
                                 
                                var editRow = rows.parent().parent();
                                
                                if(!(editRow.hasClass('paymentTracker_editing_row')))
                                {
                                    editRow.addClass('paymentTracker_editing_row');
                                }
                                
                                fnPreFillFormValuesPaymentTracker (mainGridRecord, record,1);
                                
                                if(!(editRow.hasClass('row_selected')))
                                {
                                    editRow.addClass('row_selected');
                                }
                                
                            }
                        });
                    }
                }
                else
                {
                    jAlert ({ msg : 'Kindly select payment tracker sub grid', type : 'info' });
                }
            }
            else
            {
                jAlert ({ panel: $('#tablePaymentTrackerSubGrid'), msg : 'Payment is done', type : 'info' });
            }
        }
        else
        {
            jAlert ({ msg : 'Kindly select payment tracker sub grid', type : 'info' });
        }
    });
    
    // On show edit payment tracker reset error message
    $('#editPaymentTrackerDetails').on('show.bs.collapse', function () {
        $( "#editFormPaymentTracker").validate().resetForm();
    });
    
    // On payment tracker add button click
    $('#addPaymentTracker').on('click', function(){
        $("#formSubmitPaymentTracker").html('<i class="mdi-content-send"></i> Add');
        
        if ($("#onAddCollapse").hasClass('collapsed'))
        {
            $( "#onAddCollapse" ).trigger( "click" );
        }
        
        var mainGridRecord = tablePaymentTracker.fnGetData (fnGetSelected (tablePaymentTracker)[0]);
        
        var paymentTrackerId = $('#formPaymentTrackerId').val();
             
        if (paymentTrackerId > 0 && !isNaN(paymentTrackerId))
        {
            clearLock ({
                'formName' : 'PT Payment Tracker',
                'uniqueId' : paymentTrackerId,
                'callback' : function ()
                {
                    $('#formPaymentTrackerId').val(0);
                }
            });
        }
        
        fnPreFillFormValuesPaymentTracker (mainGridRecord, '', 0);    
    });
    
    // On form field change
    $('#editFormPaymentTracker').on('change', function() {
        isDirtyFormPaymentTracker = true;
    });
    
    /**
     *  click to close add/edit modal
    */
    $('#editCloseConfirmPaymentTracker').on('click', function () {
        isDirtyFormPaymentTracker = false;
        
        fnFormClosePaymentTracker(true);
    });
    
    /**
     *  Add,Edit form modal hide event
    */
    $('#modalFormPaymentTracker').on('hide.bs.modal', function (e) {
        if (isDirtyFormPaymentTracker)
        {
            e.preventDefault();
            e.stopImmediatePropagation();
            
            $('#modalDirtyPaymentTracker').modal('toggle');
        }
        else
        {
            fnFormClosePaymentTracker(false);
        }
    });
    
   /**
     *  Prefill payment tracker form values in add, edit, reset events
    */
    function fnPreFillFormValuesPaymentTracker (mainGridRecord, subGridRecord, isSubGridNeedToRefresh ) {
        $('#s2id_formPaymentTrackerModeOfPayment').removeClass('form-error');
        
        if (mainGridRecord != '')
        {
            if (subGridRecord != '')
            {
                $('#paymentSummaryPanel')[0].scrollIntoView();
                
                $('#formPaymentTrackerPaymentId').val(mainGridRecord.Payment_Id);
                $('#formPaymentTrackerId').val(subGridRecord.Ins_Pay_Tracker_Id);
                
                $('#s2id_formPaymentTrackerModeOfPayment').select2('val', subGridRecord.PaymentMode_Id);
                
                $('#formPaymentTrackerModeOfPayment').trigger('change');
                
                //$('#formInsPaymentTrackerPaymentDate').datepicker('setDate', fnDateFormatter (subGridRecord.edit_Payment_Date));
                $('#formInsPaymentTrackerPaymentDate').datepicker('setDate', new Date(subGridRecord.edit_Payment_Date));
                $('#formPaymentTrackerDocumentNo').val(subGridRecord.Document_No);
                $('#formPaymentTrackerBankName').val(subGridRecord.Bank_Name);
                $('#formPaymentTrackerBranchName').val(subGridRecord.Branch_Name);
                $('#formPaymentTrackerAmountPaid').val(subGridRecord.Amount_Paid);
                $('#formPaymentTrackerDescription').val(subGridRecord.Description);
            }
            else
            {
                $('#formPaymentTrackerReset').trigger('click');
                
                $('#formPaymentTrackerPaymentId').val(mainGridRecord.Payment_Id);
                $('#formPaymentTrackerId').val(0);
                
                $("#s2id_formPaymentTrackerModeOfPayment").select2('val', 1);
                
                $('#formPaymentTrackerModeOfPayment').trigger('change');
            }
            
            if (isSubGridNeedToRefresh == 0)
            {
                tablePaymentTrackerSubGrid.fnReloadAjax( pageUrl () + "payroll/insurance/list-payment-tracker-subgrid/paymentId/" + mainGridRecord.Payment_Id );
            }
        }
        
        $( "#editFormPaymentTracker").validate().resetForm();
        
        isDirtyFormPaymentTracker = false;
    }
    
    /**
     *  When form isn't dirty then clear the lock flag and then close that add/edit form
    */
    function fnFormClosePaymentTracker(hideAction) {
        var paymentTrackerId = $('#formPaymentTrackerId').val();
         
         fnRefreshTable (tablePaymentTracker);
         
        if (paymentTrackerId > 0 && !isNaN(paymentTrackerId))
        {
            clearLock ({
                'formName' : 'Insurance Payment Tracker',
                'uniqueId' : paymentTrackerId,
                'callback' : function ()
                {
                    $('#formPaymentTrackerId').val(0);
                    
                    if (hideAction)
                        $('#modalFormPaymentTracker').modal('hide');
                }
            });
        }
        else
        {
            if (hideAction)
                $('#modalFormPaymentTracker').modal('hide');
        }
        fnRefreshTable (tablePaymentTracker);
    }
    
    /*************************************** Common to all insurance forms *********************************************************/
    
    //On second modal close, first modal scroll will not work. To avoid that,using this
    $('#modalDirtyVariableInsurance, #modalDirtyFixedInsurance, #modalDirtyInsuranceType, #modalDirtyInsuranceGrade, #modalDirtyPaymentTracker, #sessionExipredModel').on('hidden.bs.modal', function () {
        
        if($('#modalFormFixedInsurance').is(':visible') || $('#modalFormVariableInsurance').is(':visible') ||
           $('#modalFormInsuranceType').is(':visible') || $('#modalFormInsuranceGrade').is(':visible') || $('#modalFormPaymentTracker').is(':visible'))
        {
            $('body').addClass('modal-open');
        }
    });
    
    $('#filterApplyVariableInsurance').trigger('click');
    
    //Reload combo without refreshing page
    function Combofieldpush(result) 
    {
        var comboField = [];
        comboField.push({
            'id':$('#filterVIInsuranceType'),
            'purpose':'filter'
        });
        
        comboField.push({
            'id':$('#filterInsuranceTypeId'),
            'purpose':'filter'
        });
        
        comboField.push({
            'id':$('#filterIGInsuranceTypeId'),
            'purpose':'filter'
        });

        for (var x in comboField)
        {
           fnReloadCombo(comboField[x]['id'],result.comboPair,'',comboField[x]['purpose']);
        }
                                
    }
    
    if ($('#insuranceTypeTab').length) {
        $('.page-content').addClass('custom-tab');
        $('.add-panel-padding').addClass('padding-class');
        $('#insuranceForm').removeClass('hidden');
    }

    // to check tab is clicked
    var tabClicked = false;
        
    // when the insurance tab is hovered we need to highlight the tab
    $('#insuranceTypeTab').on('mouseenter', function () {
        fnTabHighlight();
    });

    // tab highlight function
    function fnTabHighlight(){
        $('#insuranceFormTab').removeClass('tab-active-text text-secondary');
        $('#insuranceTab').removeClass('tab-border-cls');
        $('#insuranceTypeFormTab').addClass('tab-active-text text-secondary');
        $('#insuranceTypeTab').addClass('tab-border-cls')
        $('#formTabLink').removeClass('tab-a-tag-color');
        $('#formTabLink').addClass('text-secondary');
    };
    
    // when mouse is out of insurance tab we need to remove highlight property
    $('#insuranceTypeTab').on('mouseleave', function () {
        // to check tab is clicked. If yes we don't remove tab active class
        if(!tabClicked){
            $("#insuranceFormTab").addClass('tab-active-text text-secondary');
            $('#insuranceTab').addClass('tab-border-cls');
            $('#insuranceTypeFormTab').removeClass('tab-active-text text-secondary');
            $('#insuranceTypeTab').removeClass('tab-border-cls');
            $('#formTabLink').addClass('tab-a-tag-color');
            $('#formTabLink').removeClass('text-secondary');
        }
    });

    // insurance tab onclick function
    $('#insuranceTypeTab').on('click', function () {
        tabClicked = true;
        fnTabHighlight();
        // redirect to the insurance form
        window.location.href = pageUrl() + "v3/tax-and-statutory-compliance/insurance";
    });
    
});