<?php
//==========================================================================================
//==========================================================================================
/* Program : EmployeesController.php											          *
 * Property of Caprice Technologies Pvt Ltd,                                              *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                       *
 * Coimbatore, Tamilnadu, India.														  *
 * All Rights Reserved.            														  *
 * Use of this material without the express consent of Caprice Technologies               *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law.  *
 *                                                                                    	  *
 * Description : The Face Recognize Form used for add attendance using face recognize     *
 * bio-metric APK . Here for add attendance , user photo taken , image uploaded in server *
 * and uploaded image path send to face recognize bio-metric APK , image validated in APK *
 * and send a UserId if its valid image , search this userId in employee table if its     *
 * valid userId then attendance added with current time and current date. If no           *
 * attendance added for that id to current date then attendance type is Punch-In ,        *
 * else attendance type is Punch-Out.                                                     * 
 *                                                                                   	  *
 *                                                                                    	  *
 * Revisions :                                                                    	      *
 *  Version    Date           Author                  Description                         *
 *  1.0        02-Feb-2015    Prasanth                Add attendance using face recognize *
 *  												  bio-metric APK.                     *
 *  												  Added Actions :                     *
 *  												  1.imageUpload()                     *
 *  												  2.updateEmployeeAttendance()        *
 *							  							        	                      */
//==========================================================================================
//==========================================================================================

class Employees_FaceRecognizeController extends Zend_Controller_Action
{
    protected $_session = null;
    protected $_loginEmpUser = null;
    protected $_basePath = null;
    protected $_dbAccessRights = null;
    protected $_logEmpId = null;
    protected $_ehrTables = null;
    protected $_isMobile = null;
    protected $_hrappMobile = null;
	protected $_formNameA = 'Attendance';
    
    public function init()
    {
        $this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
        
        if($this->_hrappMobile->checkAuth())
        {
            $commonFunction = new Application_Model_DbTable_CommonFunction(); 
            $userSession = $commonFunction->getUserDetails();
            $this->_logEmpId = $userSession['logUserId'];

            $this->_basePath        = new Zend_View_Helper_BaseUrl();
            $this->_loginEmpUser    = new Auth_Model_DbTable_EmpUser();
            $this->_dbFaceRecognize = new Employees_Model_DbTable_FaceRecognize();
            
			$this->_dbAccessRights = new Default_Model_DbTable_AccessRights();
            $this->_empAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameA);
            //$this->_dbAccessRights->refreshUserSessionTimestamp($this->_logEmpId);
            $this->_isMobile = $this->_hrappMobile->checkDevice();
        }
        else
        {
            if(Zend_Session::namespaceIsset('lastRequest'))
            {
                Zend_Session:: namespaceUnset('lastRequest');
            }
            $session = new Zend_Session_Namespace('lastRequest');
            $session->lastRequestUri = 'employees/employees';
            $this->_redirect('auth');
        }
    }

    public function indexAction()
    {
		$checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

		if ($checkSessionAuth)
        {
        	$this->view->isMobile = $this->_hrappMobile->findMobile($this->_helper->layout);
        } else {
			$this->_redirect('auth');
		}
    }
    
	/**
	 * Upload user image to add attendance using bio-metric
	 * Face Recognization APK.
	 */
    public function imageUploadAction()
    {
        $this->_helper->layout->disableLayout();
        
        $ajaxContext = $this->_helper->getHelper('AjaxContext');
        $ajaxContext->addActionContext('image-upload', 'json')->initContext();
        
        if ($this->_isMobile)//mobile
        {
            $fileName = $this->_getParam('name', null);
            
            $upload = new Zend_File_Transfer_Adapter_Http();
            $files  = $upload->getFileInfo();
            
            $basePath = '/opt/bitnami/lampstack-5.4.26-2/apps/projects/hrapp/hrappmobile/public/';
            $fullPath = $basePath.'hrapp_upload/capricetech_temp/attendance/';
            
            if (!empty($files))
            {
                foreach($files as $file => $fileInfo)
                {
                    if ($upload->isUploaded($file))
                    {
                        $info = $upload->getFileInfo($file);
                        
                        $upload->setDestination($fullPath);
                        
                        $empPhotoFName = $upload->getFileName($file);
                        
                        $empPhotoSize = $upload->getFileSize($file);
                        
                        $fext_tmp = explode('.',$empPhotoFName);
                        
                        $file_ext = $fext_tmp[(count($fext_tmp) - 1)];
                        
                        $filePath = $fullPath.$fileName.$file_ext;
                        
                        $upload->addFilter('Rename', array('target' => $filePath, 'overwrite' => true));
                        
                        try
                        {
                            $upload->receive($file);
                            
                            $this->view->result = Zend_Json::encode(array('success' => true, 'msg'=> 'Uploaded Successfully', 'path'=>$filePath));
                        }
                        catch(Zend_File_Transfer_Exception $fileEx)
                        {
                            $isUploaded = false;
                            
                            $this->view->result = Zend_Json::encode(array('success' => false, 'msg'=> $filePath));
                        }
                    }
                }
            }
            else
            {
                $this->view->result = Zend_Json::encode(array('success' => false, 'msg'=> 'No File'));
            }
        }
        else
        {
            $this->view->result = Zend_Json::encode(array('success' => false, 'msg'=> 'Only Accees in Mobile Device'));
        }
    }
    
	/**
	 * Update employee attendance for userId returned by bio-metric APK.
	 */
    public function updateEmployeeAttendanceAction()
    {
        $this->_helper->layout->disableLayout();
        
        $ajaxContext = $this->_helper->getHelper('AjaxContext');
        $ajaxContext->addActionContext('update-employee-attendance', 'json')->initContext();
        
        $userIds = $this->_getParam('User_Ids', null);
        $userArr = explode(',',$userIds);
		
		$type = $this->_getParam('Mode', null);
		
        if (count($userArr))
        {
            $this->view->result = $this->_dbFaceRecognize->addAttendanceForUsers($userArr,$type,$this->_logEmpId);
        }
        else
        {
            $this->view->result = Zend_Json::encode(array('success' => false, 'msg'=> 'Invalid Data', 'type'=> 'fatal', 'width'=>'200'));
        }
    }


}