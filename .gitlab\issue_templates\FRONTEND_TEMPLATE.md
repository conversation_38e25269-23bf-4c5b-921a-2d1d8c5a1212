FRONTEND DEVELOPMENT TEAM :

1.  Requirement:

2.  Existing System :

3.  Proposed System :

4.  [ ] I prepared the MySQL query and attached it here :

          [ ] I updated the access rights query when the new form is added or removed :

          [ ] I updated the dependent form query when the new form is added or removed for plan details :


5.  [ ] I am reusing the existing components designed
        
        * Component Name: 

        * Module Name:


6.  [ ] I am creating New components for this task and attached them here

        * Component Name:


7. [ ] I checked the impact on access flow :  

       * Employee level access change : 
       * Manager  level access change :
       * Admin    level access change :

8. [ ] I checked the design in the user interface :

       * Form names: 
       * Fields names :
       * Grid header names:
       * Search :
       * Filter :
       * Sorting :
       * Message :
       * Tooltip :
       * Validations (Min, Max, Alphanumeric) :  
 
  
9. [ ] I checked Responsiveness for my design
         
        [ ] xs  (< 600px)
        [ ] sm  (600px > < 960px)
        [ ] md  (960px > < 1264px*)   
        [ ] lg  (1264px* > < 1904px*)
        [ ] xl  (> 1904px*)  

10. [ ] Is any difference in mobile and desktop view designs 
 
          * Description:

11. [ ] I have designed based on the wireFrame given and attached the UI screens here :


12. [ ] Reusing Existing Endpoint
            
          * Endpoint Name :

13. [ ] Endpoints need to create
       
          * Endpoint Name : 

14. [ ] I have attached the wireframe.

15. [ ] I have covered all scenarios and done unit testing.

16. [ ] I have done error handling for all API calls.

17. [ ] I updated/uploaded the reference link and other required files.

18. [ ] I have done the Design and Integration level analysis and updated the estimation.

19. [ ] I have added/updated all the IDs related to testing.

20. [ ] I have handled the set lock and clear lock wherever necessary.

21. [ ] I have self reviewed my code before sending the code to review.

22. [ ] I have covered everything listed in the [checklist document](https://docs.google.com/document/d/1MeaE6wAWzimJGC7X3grNoI7UNW4UDE31bsaU7WjxrRg)

23. [ ] I have run the Lighthouse audit for my screen.

       * Updated the fixed issue and unfixed error in the [Lighthouse Document](https://docs.google.com/document/d/1Muz75nu4DR86rUpcTj6sab8gFdHvpMun4ryhPEMwcoE)

24. [ ] I handled the empty scenarios based on below role:  

       * Employee: For employee placing any request, when there is a configuration (Pre-approval, LOP Recovery etc), we should present the help text and an option to add the request. Here you can present the Add option and the refresh icon to refresh the screen. When there is a NO configuration (Pre-approval, LOP Recovery etc), we should present a message saying there is no organization policy support or configuration do not exist.
       * Manager: For request approvals, when there are no configuration exist, we should present the business process message and refresh button. No buttons to configure the policy
       * Admin: For request approvals, when there are no configuration exist, we should present the business process message and refresh button. Also a “Configure Now” button to configure the policy in the settings module and admin will be redirected there. 

25. [ ] I checked the settings module is enabled only for admin based on specific form access

26. [ ] I checked in the form that all the dropdown fields should have add & refresh buttons (if specific form exist)

10. [ ] Is any difference in split/full view presentation for view, add, and edit form
 
          * Description:

28. [ ] I checked below items has covered in data table component:

       * Search:
       * Filter(Discuss if this filter requires to be linked to an API for different result set):
       * Only one button with secondary(pink) color and all additional user actions has to be in vertical ellipsis menu or in the data table itself
       * Data table headers with info icon and information messages
       * Sort for each column in data table
       * Sticky header for the data table scroll, View, Add and Edit form scroll

26. [ ] I checked in the form redirection & icon changes are done in hrapponline, vue 2 and vue 3 repos

27. [ ] I have added the mixpanel tracking on every user action (refer this documentation for more info #7746)

28. [ ] I've done the video recording of my task

29. [ ] I've done the unit testing for redirection in all 3 layouts, quick menu, notification (if approval is there & if workflow enabled)
30. [ ] **The Grid Headers & API Keys:**

    | Grid Headers | Employee Name | ... | ... |
    |--------------|---------------|-----|-----|
    | API Key |  |  |  |

31. [ ] **The Add/ Edit Form Fields:**

    | Fields Label | Field Type | Custom Field Id | Validations | Dropdown list APIs |
    |--------------|------------|-----------------|-------------|--------------------|
    |  |  |  |  |  |

32. [ ] **The Filter Fields:**

    | Field Label | Field Type | Filter Above Grid? |
    |-------------|------------|--------------------|
    |  |  |  |

33. [ ] **The Form Access:**

    | Form Name | Form ID | Employee | Admin | Manager | Super Admin | Productivity Monitoring Admin | Payroll Admin | Service Provider Admin |
    |-----------|---------|----------|-------|---------|-------------|-------------------------------|-------------------------------|------------------------|
    |  |  |  |  |  |  |  |  |  |

34. [ ] **List API Response:**

    ```
    "response": {
                   "errorCode": "",
                   "message": "API retrieved successfully.",
                   "data": [],
                }
    ```

**TESTING TEAM** :

1. [ ]  I analyze this task and its impacts

2. [ ]  I validate the analysis and prepare the test cases(#Description Mandatory)

3. [ ]  I update the test cases in ticket and respective document(with ticket number):

4. [ ] These test cases reviewed by the lead 

5. [ ]  I test all the test cases which you documented

6. [ ]  I test the dependent changes of this task

7. [ ]  I test the application in all(Chrome,Edge,FireFox,IE,Safari) the browser

8. [ ]  I checked Responsiveness for this screen
         
        [ ] xs  (< 600px)
        [ ] sm  (600px > < 960px)
        [ ] md  (960px > < 1264px*)   
        [ ] lg  (1264px* > < 1904px*)
        [ ] xl  (> 1904px*)  

9. [ ]  The functionality covers the requirement

10. [ ] I tested the task for test suit(n-1) preparation.

11. [ ] I tested the Lighthouse audit performance report.

12. [ ] I have tested all scenarios listed in the [checklist document](https://docs.google.com/document/d/1MeaE6wAWzimJGC7X3grNoI7UNW4UDE31bsaU7WjxrRg)

13. [ ] I closed this issue after completed the testing.

14. [ ] I've done the testing for redirection in all 3 layouts, quick menu, notification (if approval is there & if workflow enabled)
