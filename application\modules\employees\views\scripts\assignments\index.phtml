<?php
	
	$assignmentAccess = $this->assignmentAccess;
	$projects         = $this->projects;
	$assignee         = $this->assignee;
	$logEmployeeName  = $this->logEmployeeName;
	$logEmployeeId    = $this->logEmployeeId;
	
	$customFormNameA = $this->customFormNameA;

	$finalFormName = ((!empty($customFormNameA) && !empty($customFormNameA['New_Form_Name'])) ? $customFormNameA['New_Form_Name'] : $this->formNameA);
	
	$this->headTitle($finalFormName);	  
    
    $empDepartment = array();
	
	foreach ($assignee as $key => $row) {
		if (!in_array($row['Department_Name'], $empDepartment)) {
			array_push($empDepartment, $row['Department_Name']);
		}
	}
    
	$dateformat = $this->dateformat;
	
	if(!empty($dateformat))
	{
		$dformat = $dateformat['bs'];
		//$dformat = 'yyyy/dd/mm';
	}
	else
	{
		$dformat = 'dd/mm/yyyy';
	}
	$orgDetails		 = $this->orgDetails;
	$serviceProvider = $this->serviceProvider;
?>
	<input type="hidden" name="fieldForce" id="fieldForce" value="<?php echo $orgDetails['Field_Force']; ?>" />
<?php
	if ($assignmentAccess['View'] && ((!empty($customFormNameA) && array_key_exists("Enable",$customFormNameA) && $customFormNameA['Enable'] == 1) || empty($customFormNameA))) {
	
?>

<!--Assignment Grid Panel-->
<div class="col-md-12 portlets">
	<div class="panel" id="gridPanelAssignment">
		<div class="panel-header md-panel-controls">
			<h3><i class="icon-list"></i> <strong id="lblFormNameA"><?php echo $finalFormName;?></strong></h3>
		</div>
		<div class="panel-content">
			<!--Assignment Grid Toolbar Icons-->
				<div class="m-b-10">
					
					<?php if ($assignmentAccess['Add']) { ?>
					<button type="button" class="btn btn-white btn-embossed toolbar-icons" title="Add" data-toggle="modal" data-target="#modalFormAssignment" id="addAssignment">
						<i class="mdi-content-add"></i><span class="hidden-xs hidden-sm"> Add</span>
					</button>
					<?php } ?>
					
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" id="viewAssignment" title="View">
						<i class="mdi-action-visibility"></i><span class="hidden-xs hidden-sm"> View</span>
					</button>
					
					<?php if ($assignmentAccess['Update'] == 1 ) { ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="Edit" id="editAssignment">
						<i class="mdi-editor-mode-edit"></i><span class="hidden-xs hidden-sm"> Edit</span>
					</button>
					<?php } if ($assignmentAccess['Delete']) { ?>
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="Delete" id="deleteAssignment">
						<i class="mdi-action-delete"></i><span class="hidden-xs hidden-sm"> Delete</span>
					</button>
					<?php }?>
					
					<button type="button" class="btn btn-secondary-default btn-embossed toolbar-icons disabled btn-off" title="History" id="historyAssignment">
						<i class="mdi-action-history"></i><span class="hidden-xs hidden-sm"> History</span>
					</button>
					
					<a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="filterAssignment">
						<i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm"> Filter</span>
					</a>
					
					<!--<a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="btn1">
						<i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm"> btn1</span>
					</a>
					
					
					<a class="filter-toggle btn btn-white btn-embossed visible-lg-* toolbar-icons" id="btn2">
						<i class="glyphicon glyphicon-filter"></i><span class="hidden-xs hidden-sm"> btn2</span>
					</a>-->
					
				</div>
			
			<!-- Assignments Grid Table -->
			<table class="table dataTable table-striped table-dynamic table-hover" id="tableAssignment">
				<thead>
					<tr>
						<th></th>
						<th id="assignmentsTask">Task</th>
						<th id="assignmentsProject">Project</th>
						<th id="assignmentsActivity">Activity</th>
						<th id="assignmentsStartDate">Start Date</th>
						<th id="assignmentsDueDate">Due Date</th>
						<th id="assignmentsStatus">Status</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
			
			<!--<table class="table dataTable table-striped table-dynamic table-hover" id="tableExampless">
				<thead>
					<th>asdasdsad</th>
						<th>Task</th>
						<th>Project</th>
						<th>Activity</th>
						<th>Start Date</th>
				
						
				</thead>
			</table>-->
		</div>
	</div>
</div>



<!-- Your custom menu with dropdown-menu as default styling -->
<div id="assignment-context-menu" class="context-menu">
	<ul class="dropdown-menu" role="menu">
		<li><a tabindex="-1" id="viewContextAssignment"><i class="mdi-action-visibility"></i> View</a></li>
		<?php if ($assignmentAccess['Update'] == 1 || $assignmentAccess['TraEdit'] > 0) { ?>
		<li><a tabindex="-1" id="editContextAssignment"><i class="mdi-editor-mode-edit"></i> Edit</a></li>
		<?php } if ($assignmentAccess['Delete']) { ?>
		<li><a tabindex="-1" id="deleteContextAssignment"><i class="mdi-action-delete"></i> Delete</a></li>
		<?php } ?>
		<li><a tabindex="-1" id="historyContextAssignment"><i class="mdi-action-history"></i> History</a></li>
	</ul>
</div>

<!-- Modal for Add Form, View Form, Edit Form -->
<div class="modal fade" id="modalFormAssignment" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal" style="margin-right: 10px;" aria-hidden="true"><i class="mdi-hardware-keyboard-backspace" id="backAssignments"></i></button>
				
				<?php if ($assignmentAccess['Update']) { ?>
				<button type="button" class="close form-icons" aria-hidden="true" id="editInViewAssignment">
					<i class="mdi-editor-mode-edit"></i>
				</button>
				<?php } ?>
				
				<h4 class="modal-title" id="modalFormTitleAssignment"></h4>
			</div>
			<div class="modal-body">
				<!--View Assignment Form-->
				<form role="form" id="viewFormAssignment" >
					<div class="row">
						<div class="col-md-6 form-group">
							<div class="col-md-5 col-sm-5"><label class="control-label">Task Name</label></div>
							<div class="col-md-7 col-sm-7"><p id="viewTaskName"></p></div>
						</div>
						
						<div class="col-md-6 form-group">
							<div class="col-md-5 col-sm-5"><label class="control-label">Creator</label></div>
							<div class="col-md-7 col-sm-7"><p id="viewCreator"></p></div>
						</div>
						
						<div class="col-md-6 form-group">
							<div class="col-md-5 col-sm-5"><label class="control-label">Assignor</label></div>
							<div class="col-md-7 col-sm-7"><p id="viewAssignor"></p></div>
						</div>
						
						<div class="col-md-6 form-group">
							<div class="col-md-5 col-sm-5"><label class="control-label">Assigned To</label></div>
							<div class="col-md-7 col-sm-7"><p id="viewAssignedTo"></p></div>
						</div>
						
						<div class="col-md-6 form-group">
							<div class="col-md-5 col-sm-5"><label class="control-label">Start Date</label></div>
							<div class="col-md-7 col-sm-7"><p id="viewStartDate"></p></div>
						</div>
						
						<div class="col-md-6 form-group">
							<div class="col-md-5 col-sm-5"><label class="control-label">Due Date</label></div>
							<div class="col-md-7 col-sm-7"><p id="viewDueDate">-</p></div>
						</div>
						
						<div class="col-md-6 form-group">
							<div class="col-md-5 col-sm-5"><label class="control-label">Project Name</label></div>
							<div class="col-md-7 col-sm-7"><p id="viewProjectName">-</p></div>
						</div>
						
						<div class="col-md-6 form-group">
							<div class="col-md-5 col-sm-5"><label class="control-label">Activity</label></div>
							<div class="col-md-7 col-sm-7"><p id="viewActivity">-</p></div>
						</div>
						
						<div class="col-md-6 form-group">
							<div class="col-md-5 col-sm-5"><label class="control-label">Priority</label></div>
							<div class="col-md-7 col-sm-7"><p id="viewPriority">-</p></div>
						</div>
						
						<div class="col-md-6 form-group">
							<div class="col-md-5 col-sm-5"><label class="control-label">Status</label></div>
							<div class="col-md-7 col-sm-7"><p id="viewStatus">-</p></div>
						</div>
						
						<div class="col-md-6 form-group">
							<div class="col-md-5 col-sm-5"><label class="control-label">Description</label></div>
							<div class="col-md-7 col-sm-7"><p id="viewDescription">-</p></div>	
						</div>
					</div>
					
					<div class="row additionalInfoPanel">
						<hr class="view-hr"/>
						
						<div class="form-group" style="font-size: large;margin-left: 13px;">
							<label class="control-label text-center">Additional Information</label>
						</div>
						
						<div class="row">
							<div class="col-md-6 form-group additionalInformation">
								<div class="col-md-5 col-sm-5"><label class="control-label">Added On</label></div>
								<div class="col-md-7 col-sm-7"><p id="viewAddedOn"></p></div>
							</div>
							
							<div class="col-md-6 form-group additionalInformation">
								<div class="col-md-5 col-sm-5"><label class="control-label">Added By</label></div>
								<div class="col-md-7 col-sm-7"><p id="viewAddedBy"></p></div>
							</div>
						</div>
						
						<div class="row updatedPanel">
							<div class="col-md-6 form-group additionalInformation">
								<div class="col-md-5 col-sm-5"><label class="control-label">Updated On</label></div>
								<div class="col-md-7 col-sm-7"><p id="viewUpdatedOn"></p></div>
							</div>
							
							<div class="col-md-6 form-group additionalInformation">
								<div class="col-md-5 col-sm-5"><label class="control-label">Updated By</label></div>
								<div class="col-md-7 col-sm-7"><p id="viewUpdatedBy"></p></div>
							</div>
						</div>
					</div>
				</form>
				
				<?php if ($assignmentAccess['Add'] == 1 || $assignmentAccess['Update'] == 1) { ?>
				
				<!--Add/Edit Transfer Form-->
				<form role="form" class="form-horizontal form-validation" id="editFormAssignment" method="POST" action="">
					<input type="hidden" name="Task_Id" id="formTaskId" />
					<input type="hidden" name="Creator_Id" id="formCreatorId" value="<?php echo $logEmployeeId; ?>"/>
					<input type="hidden" name="Assignor_Id" id="formAssignorId" value="<?php echo $logEmployeeId; ?>" />
					<div class="row">
						
						<!--Start Task Name Field Set-->
						<div class="form-group col-md-6 col-sm-6">
							<label class="col-md-5 col-sm-5 control-label">Task Name <span class="short_explanation">*</span></label>
							<div class="col-md-7 col-sm-7">
								<input type="text" class="form-control vName alphaNumSpCDotHySlash" id="formTaskName" name="Task_Name" placeholder="Task Name">
							</div>
                        </div>
						<!--End Task Name Field Set-->
						
						<!--Start Creator Field Set-->
						<div class="form-group col-md-6 col-sm-6">
							<label class="col-md-5 col-sm-5 control-label">Creator </label>
							<div class="col-md-7 col-sm-7">
								<label class="control-label" style="margin-top: 10px" id="formCreator"><?php echo $logEmployeeName; ?></label>
								
							</div>
                        </div>
						<!--End Creator Field Set-->
					</div>
					
					<div class="row">
						<!--Start Assignor Field Set-->
						<div class="form-group col-md-6 col-sm-6">
							<label class="col-md-5 col-sm-5 control-label">Assignor </label>
							<div class="col-md-7 col-sm-7">
								<label class="control-label" id="formAssignor" style="margin-top: 10px"><?php echo $logEmployeeName; ?></label>
								
							</div>
                        </div>
						<!--End Assignor Field Set-->
						
						<!--Start Assignee Field Set-->
						<div class="form-group col-md-6 col-sm-6">
							<label class="col-md-5 col-sm-5 control-label">Assignee <span class="short_explanation">*</span></label>
							<div class="col-md-7 col-sm-7">
								<select multiple="multiple" class="form-control vRequired selectAlll" data-search="true" id="formAssigneeId"
										name="AssigneeId" data-placeholder="-- Select --">
									<option value="selectAll">--Select all--</option>
									<option value="clearAll">--Clear all--</option>
                                    <?php
										foreach ($empDepartment as $key => $row) {
											echo '<optgroup label="'. $row .'">';
											
											foreach ($assignee as $empKey => $empRow) {
												if ($row == $empRow['Department_Name']) {
													echo '<option value="'. $empRow['Employee_Id'] .'">'. $empRow['Employee_Name'] .'</option>';
												}
											}
											
											echo '</optgroup>';
										}
									?>
								</select>
							</div>
                        </div>
						<!--End Assignee Field Set-->
					</div>

					<div class="row">
						<!--Start start date Field Set-->
						<div class="form-group col-md-6 col-sm-6">
							<label class="col-md-5 col-sm-5 control-label">Start Date <span class="short_explanation">*</span></label>
							<div class="col-md-7 col-sm-7">
								<input type="text" name="StartDate" class="date-picker form-control vRequired datePickerRead" id="formAssignmentStartDate" data-rule-AssigmentStartDateIsPast="true" data-msg-AssigmentStartDateIsPast="Date is in past">
							</div>
                        </div>
						<!--End start date Field Set-->
						
						<!--Start end date Field Set-->
						<div class="form-group col-md-6 col-sm-6">
							<label class="col-md-5 col-sm-5 control-label">End Date <span class="short_explanation">*</span></label>
							<div class="col-md-7 col-sm-7">
								<input type="text" name="EndDate" class="date-picker form-control vRequired datePickerRead" id="formAssignmentEndDate" data-rule-AssigmentEndDateIsPast="true" data-msg-AssigmentEndDateIsPast="Date is in past">
							</div>
                        </div>
						<!--End end date Field Set-->
					</div>
					
					<div class="row">
						<!--Start Project Field Set-->
						<div class="form-group col-md-6 col-sm-6">
							<label class="col-md-5 col-sm-5 control-label">Project Name <span class="short_explanation">*</span></label>
							<div class="col-md-7 col-sm-7">
								<select class="form-control vRequired" data-search="true" id="formProjectId" name="Project_Id" >
									<option value="">-- Select --</option>
									<?php
									foreach ($projects as $key => $row)
									{
										echo '<option value="'.$key.'">'.$row.'</option>';
									}
									?>
								</select>
							</div>
                        </div>
						<!--End Project Field Set-->
						
						<!--Start Activity Field Set-->
						<div class="form-group col-md-6 col-sm-6">
							<label class="col-md-5 col-sm-5 control-label" id="labelFormActivity">Activity </label>
							<div class="col-md-7 col-sm-7">
								<select class="form-control vRequired" data-search="true" id="formActivityId" name="Activity_Id"
										disabled="true" data-placeholder="-- Select --">
									<option value="">-- Select --</option>
									
								</select>
							</div>
                        </div>
						<!--End Activity Field Set-->
					</div>
					
					<div class="row">
						<!--Start Priority Field Set-->
						<div class="form-group col-md-6 col-sm-6">
							<label class="col-md-5 col-sm-5 control-label">Priority <span class="short_explanation">*</span></label>
							<div class="col-md-7 col-sm-7">
								<select class="form-control vRequired" data-search="true" id="formPriority" name="Priority" >
									<option value="">-- Select --</option>
    								<option value="High" >High</option>
									<option value="Medium" >Medium</option>
									<option value="Low" >Low</option>
								</select>
							</div>
                        </div>
						<!--End Priority Field Set-->
						
						<!--Start Status Field Set-->
						<div class="form-group col-md-6 col-sm-6">
							<label class="col-md-5 col-sm-5 control-label">Status </label>
							<div class="col-md-7 col-sm-7">
								<select class="form-control vRequired" id="formStatus" name="Status" >
									<option value="Open" >Open</option>
								</select>
							</div>
                        </div>
						<!--End Status Field Set-->
						
					</div>
					
					<div class="row">
						
						<!--Start Description Field Set-->
						<div class="form-group col-md-6 col-sm-6">
							<label class="col-md-5 col-sm-5 control-label">Description </label>
							<div class="col-md-7 col-sm-7">
								<textarea name="description" id="formDescription" rows="5" class="form-control vDescription" placeholder="Write your Description..." ></textarea>
							</div>
                        </div>
						<!--End Description Field Set-->
						
					</div>

					<button type="reset" class="cancel" id="formReset" style="display: none;" >Reset</button>
				</form>
				
				<?php } ?>
				
			</div>
			<div class="modal-footer text-center" id="formActionAssignment">
				
				<?php if ($assignmentAccess['Add'] == 1 || $assignmentAccess['Update'] == 1) { ?>
				<button type="reset" class="btn btn-secondary-default btn-embossed ladda-button" data-style="expand-left" id="formResetAssignment" style="bottom: 5px;">
					<i class="mdi-action-restore"></i> Reset
				</button>
				<button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formSubmitAssignment" style="bottom: 5px;">
					<i class="mdi-content-send"></i> Submit
				</button>
				<?php } ?>
				
			</div>
		</div>
	</div>
</div>

<!--Filter Form-->
<div class="builder" id="filterPanelAssignment">
	<div id="closefilterAssignment"><a class="builder-toggle"><i class="icons-office-52"></i></a></div>
	<div class="inner">
		<div class="builder-container">
			<button type="button" class="btn btn-sm btn-secondary-default btn-embossed cancel" style="width: 100%;" id="filterResetAssignment">Reset</button>
			<button type="button" class="btn btn-sm btn-secondary btn-embossed" style="width: 100%;" id="filterApplyAssignment">Apply</button>
			
			<div class="form-group">
				<label>Task Name</label>
				<input type="text" class="form-control" id="filterTaskName" >
			</div>
			
			<div class="form-group">
				<label>Assignor Name</label>
				<input type="text" class="form-control" id="filterAssignorName" >
			</div>
			
			<div class="form-group">
				<label>Assignee Name</label>
				<input type="text" class="form-control" id="filterAssigneeName" >
			</div>
			
			<div class="form-group">
				<label>Project Name</label>
				<select class="form-control" data-search="true" id="filterProject" >
					<option value="">All</option>
					<?php
					foreach ($projects as $key => $row)
					{
						echo '<option value="'.$key.'">'.$row.'</option>';
					}
					?>
				</select>
			</div>
			
			<div class="form-group">
				<label>Assignment Date</label>
				<div class="input-daterange b-datepicker input-group" data-date-format="<?php echo $dformat; ?>" id="datepicker">
					<input type="text" class="input-md form-control" name="start" id="filterAssignmentDateBegin" data-orientation="top" placeholder="Start"/>
					<span class="input-group-addon">to</span>
					<input type="text" class="input-md form-control" name="end" id="filterAssignmentDateEnd" data-orientation="top" placeholder="End"/>
				</div>
			</div>
			
			<div class="form-group">
				<label>Status</label>
				<select class="form-control" data-search="true" id="filterStatus" >
					<option value="">All</option>
					<option value="Assigned" >Assigned</option>
					<option value="InProgress">InProgress</option>
					<option value="InReview" >InReview</option>
					<option value="Completed">Completed</option>
					<option value="Closed" >Closed</option>
					<option value="ReOpened" >ReOpened</option>
					<option value="Rejected" >Rejected</option>
				</select>
			</div>

			<?php if ($orgDetails['Field_Force'] == 1) { ?>
				<div class="form-group">
					<label>Service Provider</label>
					<select class="form-control" data-search="true" id="filterServiceProvider">
						<option value="">All</option>
						<?php
						foreach ($serviceProvider as $key => $row)
						{
							echo '<option value="'. $key .'">'. $row .'</option>';
						}
						?>
					</select>												
				</div>
			<?php } ?>
			
		</div>
	</div>
</div>

<!-- Modal for view assignment history -->
<div class="modal fade" id="modalHistoryAssignment" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header bg-primary">
				<button type="button" class="close form-icons pull-left" data-dismiss="modal"
						style="margin-right: 10px;" aria-hidden="true">
					<i class="mdi-hardware-keyboard-backspace" id="backAssignmentsHistory"></i></button>
				<h4 class="modal-title">View History</h4>
			</div>
			<div class="modal-body">
				<table class="table dataTable table-striped table-dynamic table-hover" id="tableAssignmentHistory">
				<thead>
					<tr>
						<th></th>
						<th id="assignmentHistoryTaskName">Task Name</th>
						<th id="assignmentHistoryStatus">Status</th>
						<th id="assignmentHistoryModified">Modified By</th>
					</tr>
				</thead>
				<tbody>
				
				</tbody>
			</table>
			</div>
		</div>
	</div>
</div>


<!-- Form Dirty Confirmation Modal -->
<?php if ($assignmentAccess['Add'] || $assignmentAccess['Update']) { ?>
<div class="modal fade" id="modalDirtyAssignment" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditConfAssignments"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditConfAssignments">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseConformAssignment">Yes</button>
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="modalDirtyStatusAssignment" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeEditStatusConfAssignments"></i></button>
				<h4 class="modal-title"><strong>Close</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to close this form?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noEditStatusConfAssignments" >No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="editCloseConformStatusAssignment">Yes</button>
			</div>
		</div>
	</div>
</div>


<?php } if ($assignmentAccess['Delete']) { ?>
<!-- Delete COnfirmation Modal -->
<div class="modal fade" id="modalDeleteAssignment" aria-hidden="true" >
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icons-office-52" id="closeDeleteConfirmationAssignments"></i></button>
				<h4 class="modal-title"><strong>Delete</strong> Confirmation</h4>
			</div>
			
			<div class="modal-body">Are you sure want to delete ?<br></div>
			
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary-default btn-embossed" data-dismiss="modal" id="noDeleteConfirmationAssignments">No</button>
			  <button type="button" class="btn btn-secondary btn-embossed" data-dismiss="modal" id="deleteConformAssignment">Yes</button>
			</div>
		</div>
	</div>
</div>

<?php  } } else { ?>

<div class="col-md-12 portlets">
	<div class="txt_center">Sorry, Access Denied...</div>
</div>

<?php } ?>