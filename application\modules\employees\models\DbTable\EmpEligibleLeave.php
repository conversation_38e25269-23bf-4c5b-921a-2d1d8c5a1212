<?php
class Employees_Model_DbTable_EmpEligibleLeave extends Zend_Db_Table_Abstract
{

    protected $_name = 'emp_eligible_leave';
    
    protected $_dbPersonal = null;
     
    protected $_dbFinancialYr = null;

    protected $_dbComment = null;

    protected $_orgDF = null;

    protected $_db = null;

    protected $_ehrTables = null;
    
    public $_dbJob = null;
    
    protected $_dbLeave = null;

    public function init()
    {
        $this->_ehrTables = new Application_Model_DbTable_Ehr();
        $this->_db = Zend_Registry::get('subHrapp');
        $this->_dbPersonal = new Employees_Model_DbTable_Personal();
        $this->_dbFinancialYr = new Default_Model_DbTable_FinancialYear();
        $this->_dbComment = new Payroll_Model_DbTable_PayrollComment();
        $this->_dbJob = new Employees_Model_DbTable_JobDetail();
        $this->_dbLeave = new Employees_Model_DbTable_Leave();
        $this->_orgDF = $this->_ehrTables->orgDateformat();
    }
    
}

