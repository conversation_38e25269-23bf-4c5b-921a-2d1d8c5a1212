<?php

class FormsManager_Model_DbTable_Form12ba extends Zend_Db_Table_Abstract
{
    protected $_db             = null;
    protected $_ehrTables      = null;
    protected $_commonFunction = null;
    protected $_dbFinancialYr = null;
    protected $_dbPaySlip = null;
    
    public function init()
    {
        $this->_ehrTables      = new Application_Model_DbTable_Ehr();
        $this->_db             = Zend_Registry::get('subHrapp');
		$this->_dbBilling      = new Default_Model_DbTable_Billing();
        $this->_dbCommonFun    = new Application_Model_DbTable_CommonFunction();
		$this->_dbFinancialYr  = new Default_Model_DbTable_FinancialYear();
		$this->_dbPaySlip      = new Payroll_Model_DbTable_Payslip();
        $this->_dbOrgSettings  = new Organization_Model_DbTable_OrgSettings();
    }
    
    /** Generate Form12BA for the Employees who have TDS **/
    public function generateForm12BA($valArr,$sessionId,$formName)
    {
		$financialYearStart = ($valArr['Financial_Year']-1); $financialYearEnd = $valArr['Financial_Year'];
				
        /** get the financial year salary months in an array. For ex, (4,2016)...(3,2017) **/		
        $salaryMonths = $this->_dbFinancialYr->getFiscalMonthYear(null,$financialYearEnd,'Monthly',1);
		
        $empDet =  $this->_db->fetchAll($this->_db->select()->from(array('SI'=>$this->_ehrTables->salaryDeduction),
                                                        array(''))
                                            ->joinLeft(array('MP'=>$this->_ehrTables->monthlyPayslip),'MP.Payslip_Id=SI.Payslip_Id',
                                                            array('MP.Employee_Id'))
                                            ->joinLeft(array('EJ'=>$this->_ehrTables->empJob),'EJ.Employee_Id=MP.Employee_Id',
                                                       array(''))
                                            ->joinLeft(array('Dep'=>$this->_ehrTables->dept),'EJ.Department_Id=Dep.Department_Id',
                                                       array(''))
                                            ->joinLeft(array('ET'=>$this->_ehrTables->empType),'EJ.EmpType_Id=ET.EmpType_Id',
                                                       array(''))
                                            ->joinLeft(array('Loc'=>$this->_ehrTables->location),'EJ.Location_Id=Loc.Location_Id',
                                                       array(''))
                                            ->where('Dep.Department_Id IN (?)',$valArr['Emp_Department'])
                                            ->where('ET.EmpType_Id IN (?)',$valArr['Emp_Type'])
                                            ->where('Loc.Location_Id IN (?)',$valArr['Emp_Location'])
											->where('MP.Salary_Month IN (?)',$salaryMonths)
                                            ->where('SI.Deduction_Name = ?','Tax')
                                            ->where('SI.Description != ?','Contractor_Tax')
                                            ->group('MP.Employee_Id'));

        if(!empty($empDet))
        {
            $empArr = array();
            foreach ($empDet as $key => $value) { 
                array_push($empArr, $value['Employee_Id']);
            } 
            
            $qryEmpExist = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->form12ba,
                                                                           array('Employee_Id'))
                                                            ->where('Employee_Id IN (?)', $empArr)
															->where('Financial_Year IN (?)',$valArr['Financial_Year']));
            
            if(count($qryEmpExist) != count($empDet) )
            {
                $empDetails = array();
                
                $getPerquisiteDet = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->perquisites,
                                                                           array('Perquisites_Id','Perquisites_Name')));
                
                $getPerquisite = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->perquisiteTracker,
                                                                           array('Employee_Id','Perquisite_Tracker_Id')));
                
                $form12baPerqDetails = $form12baPerquisite =  array();
                
                for($i=0;$i<count($empDet);$i++)
                {
                    $empDet[$i]['Financial_Year'] = $valArr['Financial_Year'];
                    $empDet[$i]['Added_On'] = date('Y-m-d H:i:s');
                    $empDet[$i]['Added_By'] = $sessionId;
                    
                    if(!in_array($empDet[$i]['Employee_Id'],$qryEmpExist))
                    {
                        array_push($empDetails,$empDet[$i]);
                    }
                }
                    
                if(!empty($empDetails))
                {
                    $updated = 1;
                    
                    $updated = $this->_ehrTables->insertMultiple($this->_ehrTables->form12ba, $empDetails);
                }                
                    
                for($a=0;$a<count($empDetails);$a++)
                {
                    $getPerqTracker = $this->_db->fetchAll($this->_db->select()->from(array('PTA'=>$this->_ehrTables->prequisiteTrackerAmt),
                                                                       array('Perquisite_Id','Perquisite_Amount'))
                                                                ->joinLeft(array('P'=>$this->_ehrTables->perquisiteTracker),'P.Perquisite_Tracker_Id=PTA.Perquisite_Tracker_Id',
                                                                        array(''))
                                                                ->where('P.Employee_Id = ?',$empDetails[$a]['Employee_Id']) );
                    
                    $getForm12BAId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->form12ba,
                                                                   array('Form12BA_Id'))
                                                            ->where('Employee_Id = ?',$empDetails[$a]['Employee_Id'])
                                                            ->where('Financial_Year = ?',$valArr['Financial_Year']));
                    
                    for($k=0;$k<count($getPerquisiteDet);$k++)
                    {
                        $getPerquisiteDet[$k]['Perquisite_Amount']  = 0;
                        
                        $form12baPerqDetails[$k]['Form12BA_Id'] = $getForm12BAId;
                        $form12baPerqDetails[$k]['Perquisite_Id'] = $getPerquisiteDet[$k]['Perquisites_Id'];
                        $form12baPerqDetails[$k]['Perquisite_Value'] = 0;
                        $form12baPerqDetails[$k]['Recovered_From_Employee'] = 0;
                        $form12baPerqDetails[$k]['Taxable_Perquisite'] = 0;
                        
                        for($l=0;$l<count($getPerqTracker);$l++)
                        {
                            if($getPerquisiteDet[$k]['Perquisites_Id'] == $getPerqTracker[$l]['Perquisite_Id'])
                            {
                                $getPerquisiteDet[$k]['Perquisite_Amount'] = $getPerqTracker[$l]['Perquisite_Amount'];
                                
                                $form12baPerqDetails[$k]['Form12BA_Id'] = $getForm12BAId;
                                $form12baPerqDetails[$k]['Perquisite_Id'] = $getPerqTracker[$l]['Perquisite_Id'];
                                $form12baPerqDetails[$k]['Perquisite_Value'] = $getPerqTracker[$l]['Perquisite_Amount'];
                                $form12baPerqDetails[$k]['Recovered_From_Employee'] = 0;
                                $form12baPerqDetails[$k]['Taxable_Perquisite'] = $form12baPerqDetails[$k]['Perquisite_Value'] - $form12baPerqDetails[$k]['Recovered_From_Employee'];
                                
                            }
                        }
                        
                        array_push($form12baPerquisite,$form12baPerqDetails[$k]);                                    
                    }
                }
                        
                $updated = $this->_ehrTables->insertMultiple($this->_ehrTables->form12baPerquisites, $form12baPerquisite);
                
                return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
                                                                'action'         => 'generated',
                                                                'trackingColumn' => $valArr['Financial_Year'],
                                                                'formName'       => $formName,
                                                                'sessionId'      => $sessionId,
                                                                'tableName'      => $this->_ehrTables->form12ba));
            }
            else
            {
                return array('success' => false, 'msg' => $formName.' already generated', 'type' => 'warning');
            }
        }
        else
        {
            return array('success' => false, 'msg' => 'No Employees found to generate Form12BA', 'type' => 'warning');
        }
    }

    /** List Form12BA details**/
    public function listForm12BA($page, $rows, $sortField, $sortOrder, $searchAll=null, $searchArr, $form12baUser)
    {
        $employeeName      = $searchArr['employeeName'];
		$assessmentYrBegin = $searchArr['assessmentYrBegin'];
		$assessmentYrEnd   = $searchArr['assessmentYrEnd'];
		
        switch($sortField)
		{
			case 0: $sortField = 'EP.Emp_First_Name'; break;
			case 1: $sortField = 'form.Financial_Year'; break;
		}
        
        $qryForm12BA = $this->_db->select()->from(array('form'=>$this->_ehrTables->form12ba),
                                                array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS form.Form12BA_Id as Count'),
                                                'Employee_Id','Form12BA_Id','Financial_Year','Added_On',
                                                'Admin'=>new Zend_Db_Expr("'".$form12baUser['Admin']."'"),
                                                'Payroll_Group'=>new Zend_Db_Expr("'".$form12baUser['Payroll_Group']."'")))
                                            ->joinInner(array('EP'=>$this->_ehrTables->empPersonal), 'EP.Employee_Id=form.Employee_Id',
                                                            array('Employee_Name'=>new Zend_Db_Expr("CONCAT(EP.Emp_First_Name, ' ',EP.Emp_Last_Name)")))
                                            ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'EP.Employee_Id=EJ.Employee_Id',
                                                            array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN EP.Employee_Id ELSE EJ.User_Defined_EmpId END')))
                                            
                                            ->order("$sortField $sortOrder")
                                            ->limit($rows, $page);
        
		if(empty($form12baUser['Admin']) && empty($form12baUser['Payroll_Group']))
		{
			$qryEmployeeId = $this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
								->where('Manager_Id = ?', $form12baUser['LogId']);
			$getEmployeeId = $this->_db->fetchCol($qryEmployeeId);
        	if( $form12baUser['Is_Manager'] == 1 && !empty($getEmployeeId))
			{
				$qryForm12BA->where('form.Employee_Id = :EmpId or form.Employee_Id IN (?)', $getEmployeeId)
                                ->bind(array('EmpId'=>$form12baUser['LogId']));
            }	
			else
			{
				$qryForm12BA->where('form.Employee_Id = ?', $form12baUser['LogId']);
			}
		}
		
        /**
		 *	Search All columns using single input
		*/
		if (!empty($searchAll) && $searchAll != null)
		{
			$conditions  = $this->_db->quoteInto(new Zend_Db_Expr('Concat(EP.Emp_First_Name," ",EP.Emp_Last_Name) Like ?'),"%$searchAll%");
			$conditions .= $this->_db->quoteInto('or form.Financial_Year Like ?', "%$searchAll%");
			
			$qryForm12BA->where($conditions);		
		}
        
        /* Filter for employee */       
        if (!empty($employeeName) && preg_match('/^[a-zA-Z]/', $employeeName))
        {
			$qryForm12BA->where($this->_db->quoteInto(new Zend_Db_Expr('Concat(EP.Emp_First_Name," ",EP.Emp_Last_Name) Like ?'),"%$employeeName%"));
        }
        
        if (($assessmentYrBegin >=0) && preg_match('/^[0-9*\.]/', $assessmentYrBegin) && !empty($assessmentYrBegin))
		{
		    $qryForm12BA->where($this->_db->quoteInto('form.Financial_Year >= ?', $assessmentYrBegin));
		}
		
		if (($assessmentYrEnd >=0) && preg_match('/^[0-9*\.]/', $assessmentYrEnd) && !empty($assessmentYrEnd))
		{
		    $qryForm12BA->where($this->_db->quoteInto('form.Financial_Year <= ?', $assessmentYrEnd));
        }
        
        $qryForm12BA = $this->_dbCommonFun->getDivisionDetails($qryForm12BA,'EJ.Department_Id');
        
        $form12BADetails = $this->_db->fetchAll($qryForm12BA);
        
        /* Data set length after filtering */
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
        
		/** get financial start year **/
		$financialStartYear = $this->_dbFinancialYr->financialStartYear(date('n'),date('Y'));		
		
		/** Financial Year 2016-2017 records will be generated and deleted only in the financial year 2017-2018. **/
		foreach($form12BADetails as $key=>$row){
         	if(($row['Financial_Year'] == $financialStartYear) && ($row['Admin']=='admin' || $row['Payroll_Group'] == 1)){
				$form12BADetails[$key]['Delete_Form12BA'] = 1;
			}
			else
            {
				$form12BADetails[$key]['Delete_Form12BA'] = 0;
			} 
		}
        
        /* Total data set length */
		$qryGetCount = $this->_db->select()->from($this->_ehrTables->form12ba, new Zend_Db_Expr('COUNT(Form12BA_Id)'));
		if (empty($form12baUser['Admin'])){
			$getEmployeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
												  ->where('Manager_Id = ?', $form12baUser['LogId']));
			
            if($form12baUser['Is_Manager'] == 1 && !empty($getEmployeeId)){
                $qryGetCount->where('Employee_Id = :EmpId or Employee_Id IN (?)', $getEmployeeId)
                                ->bind(array('EmpId'=>$form12baUser['LogId']));
            }else{
                $qryGetCount->where('Employee_Id = ?', $form12baUser['LogId']);
            }
		}
		$iTotal = $this->_db->fetchOne($qryGetCount);
        /**
		 * Output array
		*/
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $form12BADetails);
    }
    
    /** View Form12BA based on Employee Id & Assessment Year**/
    public function viewForm12BA($empId,$assessYr)
    {
        $orgCode = $this->_ehrTables->getOrgCode(); 
        $orgView = $this->_dbOrgSettings->viewOrgDetail($orgCode);
        $orgName = $orgView['Org_Name'];
        
        $orgAddress = $this->_db->fetchRow($this->_db->select()->from(array('LOC'=>$this->_ehrTables->location),
                                                        array('Location_Name','Street1','Street2','City_Id','State_Id','Country_Code','Pincode'))
                                            ->joinLeft(array('S'=>$this->_ehrTables->state),'S.State_Id=LOC.State_Id',
                                                            array('S.State_Name as State'))
                                            ->joinLeft(array('C'=>$this->_ehrTables->city),'C.City_Id=LOC.City_Id',
                                                       array('C.City_Name as City'))
                                            ->joinLeft(array('co'=>$this->_ehrTables->country),'co.Country_Code=LOC.Country_Code',
                                                       array('co.Country_Name as Country'))
                                            ->where('LOC.Location_Type=?','MainBranch'));
        
        $orgAdd = '<br><b>'.$orgName.'</b>, <br>'.$orgAddress['Street1'].', '.$orgAddress['Street2'].',<br> '.$orgAddress['City'].', <br>'.
                     $orgAddress['State'].', <br>'.$orgAddress['Country'].'-'.$orgAddress['Pincode'];
        
        $tanQry = $this->_db->fetchRow($this->_db->select()
                        ->from(array('l'=>$this->_ehrTables->taxConfiguration),
                               array('TAN','TDS_Assessment_Range')));
        
        $tan = ($tanQry['TAN'] !=''?$tanQry['TAN'] : '-');
        $tdsAssess = ($tanQry['TDS_Assessment_Range'] != ''?$tanQry['TDS_Assessment_Range'] :'-');
        
        $empDet =  $this->_db->fetchRow($this->_db->select()->from(array('EP'=>$this->_ehrTables->empPersonal),
                                                        array('Employee_Name'=>new Zend_Db_Expr("CONCAT(EP.Emp_First_Name, ' ',EP.Emp_Last_Name)"),'PAN'))
                                            ->joinLeft(array('EJ'=>$this->_ehrTables->empJob),'EJ.Employee_Id=EP.Employee_Id',
                                                       array('Location_Id'))
                                            ->joinInner(array('EPr'=>$this->_ehrTables->empProfession), 'EPr.Profession_Id = EJ.Emp_Profession',
                                                        array('Profession_Name as Emp_Profession'))
                                            ->joinLeft(array('Des'=>$this->_ehrTables->designation),'EJ.Designation_Id=Des.Designation_Id',
                                                       array('Designation_Name','Grade_Id'))
                                            ->joinLeft(array('MP'=>$this->_ehrTables->monthlyPayslip),'MP.Employee_Id=EP.Employee_Id',
                                                       array('Total_Income' => new Zend_Db_Expr("SUM(MP.Total_Salary)")))
                                            ->where('EP.Employee_Id = ?',$empId));
        
		$taxSalary = 0;
		
		$financialYearStart = ($assessYr-1); $financialYearEnd = $assessYr;
		
        /** get the financial year salary months in an array. For ex, (4,2016)...(3,2017) **/
        $tdsMonths = $this->_dbFinancialYr->getFiscalMonthYear(null,$financialYearEnd,'Monthly',1);
        
		foreach ($tdsMonths as $key => $row)
		{
			$getTaxableIncome = $this->_dbPaySlip->getTaxableIncome($row,$empId);
			$taxSalary += $getTaxableIncome[0];
		}
		
        /** Including current employment TDS history details in Form12BA **/		
        $dbForm16       = new FormsManager_Model_DbTable_Form16();
		$tdsHistoryForNewReg = $dbForm16->getTdsHistoryDetails($empId,$assessYr);
		
		if(!empty($tdsHistoryForNewReg))
		{
			$taxSalary += $tdsHistoryForNewReg['Taxable_Salary'];
		}
		
		$empDet['Total_Income'] = $taxSalary;
		
        $empDetails = $empDet['Employee_Name'].', '.$empDet['Designation_Name'].', '.$empDet['PAN'];
        
        //$director = ($empDet['Is_Director'] != 0 ? 'Yes' : 'No');
        $director = ($empDet['Emp_Profession'] == 'Director' ? 'Yes' : 'No');
        
        $perquisiteTracker = $this->_db->fetchAll($this->_db->select()->from(array('PTA'=>$this->_ehrTables->prequisiteTrackerAmt),
                                                        array('Perquisites_Id'=>'PTA.Perquisite_Id','Perquisite_Amount'))
                                            ->joinLeft(array('PT'=>$this->_ehrTables->perquisiteTracker),'PT.Perquisite_Tracker_Id=PTA.Perquisite_Tracker_Id',
                                                 array(''))
                                            ->joinLeft(array('P'=>$this->_ehrTables->perquisites),'P.Perquisites_Id=PTA.Perquisite_Id',
                                                       array('Perquisites_Name'))
                                            ->where('Employee_Id = ?',$empId)
											->where('PT.Assessment_Year = ?', $assessYr));
                
        $reimbursement =  $this->_db->fetchAll($this->_db->select()->from(array('ET'=>$this->_ehrTables->expenseTypes),
                                                        array('Perquisites_Id'))
                                            ->joinLeft(array('RE'=>$this->_ehrTables->reimbursement),'RE.Expense_Id=ET.Expense_Id',
                                                 array('Request_Id','Amount'/*, new Zend_Db_Expr("DATE_FORMAT(RE.Submission_Date,'".$this->_orgDF['sql']."') as Sub_Date"  )*/  ))
                                            ->joinLeft(array('P'=>$this->_ehrTables->perquisites),'P.Perquisites_Id=ET.Perquisites_Id',
                                                       array('Perquisites_Name'))
                                            ->where(new Zend_Db_Expr("DATE_FORMAT(RE.Submission_Date, '%Y') = ?")  ,$assessYr)
                                            ->where('RE.Employee_Id = ?',$empId));
            
        $deductions = $this->_db->fetchAll($this->_db->select()->from(array('D'=>$this->_ehrTables->deductions),
                                                        array('D.Amount'))
                                            ->joinInner(array('DT'=>$this->_ehrTables->deductionTitle),'D.Title_Id=DT.Title_Id',
                                                       array('DT.Perquisites_Id'))
                                            ->joinInner(array('P'=>$this->_ehrTables->perquisites),'P.Perquisites_Id=DT.Perquisites_Id',
                                                       array('P.Perquisites_Name'))
                                            ->where(new Zend_Db_Expr("DATE_FORMAT(D.Effective_Date, '%Y') = ?")  ,$assessYr)
                                            ->where('D.Employee_Id = ?',$empId));
        
        $payslipTabtdodd = "height:2%;padding-left: 1%;padding-right: 1%;width:50%";
                
        $exportStr = '';
        $exportStr .= '<style>
                /* Fix styles here - more targeted to avoid removing necessary borders */
                #modalForm12ba #form12BAView .modal-content,
                #modalForm12ba #form12BAView .modal-header,
                #modalForm12ba #form12BAView .modal-body,
                #modalForm12ba #form12BAView .modal-footer {
                    background-color: transparent !important;
                    color: black !important;
                    transition: none !important;
                }

                /* Fix hover issue - only apply to modal elements, not table content */
                #modalForm12ba #form12BAView .modal-content:hover,
                #modalForm12ba #form12BAView .modal-header:hover,
                #modalForm12ba #form12BAView .modal-body:hover,
                #modalForm12ba #form12BAView .modal-footer:hover {
                    background-color: transparent !important;
                }

                /* Ensure table content remains visible on hover - improved visibility (exclude header table) */
                #modalForm12ba #form12BAView table:not(.form12ba-header-table) tr:hover td,
                #modalForm12ba #form12BAView table:not(.form12ba-header-table) tr:hover th {
                    background-color: rgba(245, 245, 245, 0.8) !important;
                    color: #000 !important;
                    opacity: 1 !important;
                    visibility: visible !important;
                }

                /* Completely disable hover effects for header table - no background changes */
                #modalForm12ba #form12BAView table.form12ba-header-table tr:hover td,
                #modalForm12ba #form12BAView table.form12ba-header-table tr:hover th,
                #modalForm12ba #form12BAView table.form12ba-header-table:hover td,
                #modalForm12ba #form12BAView table.form12ba-header-table:hover th,
                #modalForm12ba #form12BAView div.formLVHead table.form12ba-header-table tr:hover td,
                #modalForm12ba #form12BAView div.formLVHead table.form12ba-header-table tr:hover th,
                #modalForm12ba #form12BAView div.formLVHead table.form12ba-header-table td:hover,
                #modalForm12ba #form12BAView div.formLVHead table.form12ba-header-table th:hover {
                    background: transparent !important;
                    background-color: transparent !important;
                    background-image: none !important;
                    color: #000 !important;
                    text-align: center !important;
                    text-align: -webkit-center !important;
                    text-align: -moz-center !important;
                }

                /* Override any potential hover effects from other CSS rules */
                table.form12ba-header-table tr:hover td,
                table.form12ba-header-table tr:hover th,
                table.form12ba-header-table td:hover,
                table.form12ba-header-table th:hover {
                    background: transparent !important;
                    background-color: transparent !important;
                    background-image: none !important;
                }

                /* Fix center alignment ONLY for Form 12BA header table - maximum specificity */
                #modalForm12ba #form12BAView div.formLVHead table.form12ba-header-table {
                    border: none !important;
                    border-collapse: collapse;
                    margin: 0 auto !important;
                    width: 100% !important;
                }
                #modalForm12ba #form12BAView div.formLVHead table.form12ba-header-table td {
                    text-align: center !important;
                    text-align: -webkit-center !important;
                    text-align: -moz-center !important;
                    border: none !important;
                    padding: 5px;
                    display: table-cell !important;
                    vertical-align: middle !important;
                    color: #000 !important;
                    opacity: 1 !important;
                    visibility: visible !important;
                    background-color: transparent !important;
                }
                #modalForm12ba #form12BAView table.form12ba-header-table td {
                    text-align: center !important;
                    text-align: -webkit-center !important;
                    text-align: -moz-center !important;
                    border: none !important;
                    padding: 5px;
                    color: #000 !important;
                    opacity: 1 !important;
                    visibility: visible !important;
                    background-color: transparent !important;
                }
                /* Additional fallback for header table cells */
                table.form12ba-header-table td {
                    text-align: center !important;
                    text-align: -webkit-center !important;
                    text-align: -moz-center !important;
                    border: none !important;
                    padding: 5px;
                    color: #000 !important;
                    opacity: 1 !important;
                    visibility: visible !important;
                    background-color: transparent !important;
                }

                /* Remove borders from employee details table (rows 1-8) */
                #modalForm12ba #form12BAView div.formLVHead table.form12ba-employee-details-table {
                    border: none !important;
                    border-collapse: collapse !important;
                    width: 100% !important;
                }
                #modalForm12ba #form12BAView div.formLVHead table.form12ba-employee-details-table td {
                    border: none !important;
                    padding: 8px !important;
                    text-align: left !important;
                    background-color: transparent !important;
                    color: black !important;
                }
                #modalForm12ba #form12BAView div.formLVHead table.form12ba-employee-details-table tr:hover td {
                    background-color: rgba(240, 240, 240, 0.5) !important;
                    color: #000 !important;
                    opacity: 1 !important;
                    visibility: visible !important;
                }

                /* Target ONLY the perquisites table after "8. Valuation of Perquisites" section */
                #modalForm12ba #form12BAView div.formLVHead table.form12ba-perquisites-table {
                    border-collapse: collapse !important;
                    border: 1px solid #000 !important;
                    margin: 0 auto;
                    width: 100% !important;
                }
                #modalForm12ba #form12BAView div.formLVHead table.form12ba-perquisites-table td,
                #modalForm12ba #form12BAView div.formLVHead table.form12ba-perquisites-table th {
                    border: 1px solid #000 !important;
                    padding: 8px !important;
                    text-align: center !important;
                    background-color: white !important;
                    color: black !important;
                    opacity: 1 !important;
                    visibility: visible !important;
                }
                /* Ensure perquisites table content is visible on hover */
                #modalForm12ba #form12BAView div.formLVHead table.form12ba-perquisites-table tr:hover td,
                #modalForm12ba #form12BAView div.formLVHead table.form12ba-perquisites-table tr:hover th {
                    background-color: #f0f0f0 !important;
                    color: #000 !important;
                    opacity: 1 !important;
                    visibility: visible !important;
                }
                /* Override for data rows in perquisites table - left align for nature column */
                #modalForm12ba #form12BAView div.formLVHead table.form12ba-perquisites-table td:nth-child(2) {
                    text-align: left !important;
                    padding-left: 10px !important;
                }
                #modalForm12ba #form12BAView div.formLVHead table.form12ba-perquisites-table td:nth-child(2) {
                    text-align: left !important;
                    padding-left: 10px !important;
                }
            </style>';
        $exportStr .= '<div class="formLVHead" style = "margin-left:2.5%;margin-right:2.5%;width: 95%;page-break-inside: avoid;margin-top:0%;">';
		$exportStr .= '<table style="width:100%;margin:0 auto;border:none;border-collapse:collapse;" class="form12ba-header-table">';
        $exportStr .= '<tr><td style="font-size:15px;text-align:center !important;text-align:-webkit-center !important;text-align:-moz-center !important;width:100%;border:none;padding:5px;"> <b>FORM NO. 12BA</b> </td></tr>';
        $exportStr .= '<tr><td style="font-size:12px;text-align:center !important;text-align:-webkit-center !important;text-align:-moz-center !important;width:100%;border:none;padding:5px;">See rule 26A(2)(6) </td></tr>';
        $exportStr .= '<tr><td style="font-size:12px;text-align:center !important;text-align:-webkit-center !important;text-align:-moz-center !important;width:100%;border:none;padding:5px;"><b>Statement showing particulars of perquisites, other fringe benefits or </b></td></tr>';
        $exportStr .= '<tr><td style="font-size:12px;text-align:center !important;text-align:-webkit-center !important;text-align:-moz-center !important;width:100%;border:none;padding:5px;"><b> amenities and profits in lieu of salary with value thereof </b></td></tr>';
        $exportStr .= '</table>';
        $exportStr .= '<table style="width:100%;height:350px" class="form12ba-employee-details-table">';
        $exportStr .= '<tr><td style="'. $payslipTabtdodd .'">';
        $exportStr .= '1. Name and address of employer </td>
                        <td style="width:2%"> : </td>
                        <td style="width:48%">'.$orgAdd.'</td></tr>';
        $exportStr .= '<tr><td style="'. $payslipTabtdodd .'">';
        $exportStr .= '2. TAN </td>
                        <td style="width:2%">:</td>
                        <td style="width:48%">'.$tan.'</td></tr>';
        $exportStr .= '<tr><td style="'. $payslipTabtdodd .'">';                        
        $exportStr .= '3. TDS Assessment Range of the employer </td>
                            <td style="width:2%">:</td>
                            <td style="width:48%">'.$tdsAssess.'</td></tr>';
        $exportStr .= '<tr><td style="'. $payslipTabtdodd .'">';                        
        $exportStr .= '4. Name,designation,PAN of employee </td>
                            <td style="width:2%">:</td>
                            <td style="width:48%">'.$empDetails.'</td></tr>';
        $exportStr .= '<tr><td style="'. $payslipTabtdodd .'">';                        
        $exportStr .= '5. Is the employee a director or a person with substantial interest in the company (where the employer is the company) </td>
                            <td style="width:2%">:</td>
                            <td style="width:48%">'.$director.'</td></tr>';
        $exportStr .= '<tr><td style="'. $payslipTabtdodd .'">';                        
        $exportStr .= '6. Income under the head "Salaries" of the employee (other than from perquisites)</td>
                            <td style="width:2%">:</td>
                            <td style="width:48%">'.$empDet['Total_Income'].'</td></tr>';
        $exportStr .= '<tr><td style="'. $payslipTabtdodd .'">';                        
        $exportStr .= '7. Financial Year</td>
                            <td style="width:2%">:</td>
                            <td style="width:48%">'.$assessYr.'</td></tr>';
        $exportStr .= '<tr><td style="'. $payslipTabtdodd .'">';                        
        $exportStr .= '8. Valuation of Perquisites</td>
                            <td style="width:2%">:</td>
                            <td style="width:48%"></td></tr>';
        $exportStr .= '</table>';
        $exportStr .= '<div style="height:20px"></div>';
        
        $exportStr .= '<table style="width:100%;" border="1" class="form12ba-perquisites-table">';
        $exportStr .= '<tr><td style="width:5%;text-align:center;">S.No</td>';
        $exportStr .= '<td style="width:36%;text-align:center;">Nature of perquisites(see rule 3)</td>';
        $exportStr .= '<td style="width:20%;text-align:center;">Value of <br> perquisite as per rules (Rs.)</td>';
        $exportStr .= '<td style="width:20%;text-align:center;">Amount, if any, recovered from the employee (Rs.)</td>';
        $exportStr .= '<td style="width:20%;text-align:center;">Amount of perquisite chargable to tax Col.(3) - Col.(4) (Rs.)</td></tr>';
        $exportStr .= '<tr><td style="width:5%;text-align:center;">(1)</td>';
        $exportStr .= '<td style="width:36%;text-align:center;">(2)</td>';
        $exportStr .= '<td style="width:20%;text-align:center;">(3)</td>';
        $exportStr .= '<td style="width:20%;text-align:center;">(4)</td>';
        $exportStr .= '<td style="width:20%;text-align:center;">(5)</td></tr>';
        
        //$valArr = array('Accommodation','Cars/Other Automotive','Sweeper,gardener,watchman or personal attendant','Gas,electricity,water',
        //                'Interest free or concessional loans','Holiday expenses','Free or concessional travel','Free meals','Free education',
        //                'Gifts,vouchers,etc','Credit card expenses','Club Expenses','Use of movable assets by employees','Transfer of assets to employees',
        //                'Value of any other benefit/amenity/service/previlege','Stock options(non-qualified options)','Other benefits or amenities',
        //                'Total value of perquisites','Total value of profits in lieu of salary as per section17(3)');
        
        $perquisiteForm12ba = $this->_db->fetchAll($this->_db->select()->from(array('D'=>$this->_ehrTables->form12baPerquisites),
                                                        array('Form12BA_Id','Perquisite_Id','Perquisite_Value',
                                                              'Recovered_From_Employee','Taxable_Perquisite'))
                                                    ->joinLeft(array('P'=>$this->_ehrTables->perquisites),'P.Perquisites_Id=D.Perquisite_Id',
                                                       array('Perquisites_Name'))
                                                    ->joinInner(array('12BA'=>$this->_ehrTables->form12ba),'12BA.Form12BA_Id=D.Form12BA_Id',
                                                       array(''))
                                                    ->where('12BA.Employee_Id = ?',$empId)
													->where('12BA.Financial_Year = ?', $assessYr));
		
		$cnt = count($perquisiteForm12ba);
        $perquisiteForm12ba[$cnt]['Perquisite_Id'] =  $cnt;
        $perquisiteForm12ba[$cnt]['Perquisites_Name'] =  'Total value of perquisites';
        $perquisiteForm12ba[$cnt]['Perquisite_Value'] =  0;
        $perquisiteForm12ba[$cnt]['Recovered_From_Employee'] =  0;
        $perquisiteForm12ba[$cnt]['Taxable_Perquisite'] = 0;
        
        $perquisiteForm12ba[$cnt+1]['Perquisite_Id'] =  $cnt+1;
        $perquisiteForm12ba[$cnt+1]['Perquisites_Name'] =  'Total value of profits in lieu of salary as per section17(3)';
        $perquisiteForm12ba[$cnt+1]['Perquisite_Value'] =  0;
        $perquisiteForm12ba[$cnt+1]['Recovered_From_Employee'] =  0;
        $perquisiteForm12ba[$cnt+1]['Taxable_Perquisite'] = 0;
        
        $perqAmt = $recoverAmt = $taxPerq = 0;
        
        for($i=0;$i<count($perquisiteForm12ba);$i++)
        {
            $perquisiteVal = $perqDedVal = $perqGross = 0;
            
            foreach ($reimbursement as $row)
			{
                if($perquisiteForm12ba[$i]['Perquisites_Name'] == $row['Perquisites_Name'])
                {
                    $perquisiteVal = $row['Amount'];
                    foreach($deductions as $ded)
                    {
                        if($ded['Perquisites_Name'] == $row['Perquisites_Name'])
                        {
                           $perqDedVal = $row['Amount'] - $ded['Amount'];
                        }
                    }
                }
            }
            
            foreach($perquisiteTracker as $preq)
            {
                if($perquisiteForm12ba[$i]['Perquisites_Name'] == $preq['Perquisites_Name'])
                {
                    if($perquisiteVal!='')
                        $perquisiteVal +=$preq['Perquisite_Amount'];
                    else
                        $perquisiteVal = round($preq['Perquisite_Amount'],2);
                }
            }
            
            $perqGross = $perquisiteVal - $perqDedVal;
            $perqGross = ($perqGross != 0  ? $perqGross :0);
            
            $preqGross = ($perquisiteVal == 0 ? 0 : 0);
            
            $perqAmt += $perquisiteForm12ba[$i]['Perquisite_Value'];
            $recoverAmt += $perquisiteForm12ba[$i]['Recovered_From_Employee'];
            $taxPerq += $perquisiteForm12ba[$i]['Taxable_Perquisite'];
            
            if($i == (count($perquisiteForm12ba)- 2) || $i == (count($perquisiteForm12ba)-2))
            {
                $perquisite = $perqAmt;
                $recoverd = $recoverAmt;
                $taxPerquiste = $taxPerq;
            }
            else
            {
                $perquisite = $perquisiteForm12ba[$i]['Perquisite_Value'];
                $recoverd = $perquisiteForm12ba[$i]['Recovered_From_Employee'];
                $taxPerquiste = $perquisiteForm12ba[$i]['Taxable_Perquisite'];
            }    
            
            $a = $i+1;
            $exportStr .= '<tr><td style="width:5%;text-align:left;padding-left:0.5%;">'.$a.'.</td>';
            $exportStr .= '<td style="width:36%;text-align:left;padding-left:0.5%;">'.$perquisiteForm12ba[$i]['Perquisites_Name'].'</td>';
            $exportStr .= '<td style="width:20%;text-align:center;padding-left:0.5%;">'.$perquisite.'</td>';
            $exportStr .= '<td style="width:20%;text-align:center;padding-left:0.5%;">'.$recoverd.'</td>';
            $exportStr .= '<td style="width:20%;text-align:center;padding-left:0.5%;">'.$taxPerquiste.'</td></tr>';
        }
        
        $exportStr .= '</table></div>';
        return $exportStr;
    }

    /** Delete Form12BA **/
    public function deleteForm12BA($form12baId, $logEmpId, $formName)
    {
        $assessYr =  $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->form12ba,
                                                              array('Employee_Id','Financial_Year'))
                                                    ->where('Form12BA_Id = ?',$form12baId));
            
        $form16Exist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->form16,
                                                              array('Form_Id'))
                                                    ->where('Employee_Id = ?',$assessYr['Employee_Id'])
                                                    ->where('Assessment_Year = ?',$assessYr['Financial_Year']));
        
        if(!$form16Exist)
        {
            $deleted=$this->_db->delete($this->_ehrTables->form12ba,'Form12BA_Id='.$form12baId);
            
            if($deleted)
                $this->_db->delete($this->_ehrTables->form12baPerquisites,'Form12BA_Id='.$form12baId);
            
            return $this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
                                                            'lockFlag'       => 0,
                                                            'formName'       => $formName,
                                                            'trackingColumn' => $form12baId,
                                                            'sessionId'      => $logEmpId));
        }
        else
        {
            return array('success' => false, 'msg' => "Unable to delete Form12BA,since Form16 is generated", 'type' => 'warning');
        }
    }
    

}

