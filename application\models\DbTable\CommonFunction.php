<?php
use Kreait\Firebase\Factory;
use PhpOffice\PhpSpreadsheet\Helper\Sample;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;

class Application_Model_DbTable_CommonFunction extends Zend_Db_Table_Abstract
{
    protected $_db             = null;
    protected $_salesDb        = null;
    protected $_ehrTables      = null;
	protected $_loginEmpUser   = null;
	protected $_dbPersonal     = null;
	protected $_dbAccessRights = null;
	protected $_orgDF          = null;
	protected $_orgDetails     = null;
	
    public function init()
    {
        $this->_ehrTables = new Application_Model_DbTable_Ehr();
        $this->_salesDb = Zend_Registry::get('Hrapp');
		if (Zend_Registry::isRegistered('subHrapp'))
		{
			$this->_db             = Zend_Registry::get('subHrapp');
			$this->_loginEmpUser   = new Auth_Model_DbTable_EmpUser();
			$this->_dbPersonal     = new Employees_Model_DbTable_Personal();
			$this->_dbAccessRights = new Default_Model_DbTable_AccessRights();
			$this->_orgDF          = $this->_ehrTables->orgDateformat();

			if (Zend_Registry::isRegistered('orgDetails'))
			$this->_orgDetails = Zend_Registry::get('orgDetails');
		}

    }
    
	/**
	 *	getting the employee id, employee name and email id for the hr group and payroll group
     * @param unknown_type $groupName => hr group or payroll group
     */
    public function mailGroup ($groupName)
    {
		if($groupName=='Hr_Group'){
			$tableName= 'hrGroup';
			$groupName = 'Hr_Group_Id';
		}
		elseif($groupName=='Payroll_Group'){
			$tableName= 'payrollGroup';
			$groupName = 'Payroll_Group_Id';
		}
		
		return $this->_db->fetchAll($this->_db->select()
									->from(array('O'=>$this->_ehrTables->$tableName),
										   array(new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as EmployeeName"),
												 'J.Emp_Email', 'J.Employee_Id'))
									
									->joinInner(array('emp'=>$this->_ehrTables->empPersonal),
												'FIND_IN_SET(emp.Employee_Id,O.'.$groupName.')', array(''))
									
									->joinInner(array('J'=>$this->_ehrTables->empJob), 'emp.Employee_Id=J.Employee_Id', array(''))
									->where('J.Emp_Status Like ?', 'Active'));
	}
	
	//function to get organization description
	public function orgDescription () 
	{
		return $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->orgDetails, array("Org_Description"))->where('Org_Code = ?',$this->_ehrTables->getOrgCode()));
	}
	
	
	/**
	 *	Get mail communication form from organization setting table
	*/
	public function getMailCommunicationForms ($view=null)
    {
		$fetchVal = ($view == 1) ? 'fetchAll' : 'fetchCol';
		
		return $this->_db->$fetchVal($this->_db->select()
								->from(array('m'=>$this->_ehrTables->mailCommunicationForms), array('F.Form_Name','F.Form_Id'))
								
								->joinInner(array('F'=>$this->_ehrTables->forms), 'FIND_IN_SET(F.Form_Id,m.Form_Id)', array('')));
		
    }
    
	/**
	 *	updateResult () used to format to add/update response
	*/
	public function updateResult ($dataArr)
	{
		/**
		 *	Declare array variables for quicly understand
		*/
		$updated     = $dataArr['updated'];
		$action      = $dataArr['action'];
		$formName    = $dataArr['formName'];
		$sessionId   = $dataArr['sessionId'];
		$columnName  = $dataArr['trackingColumn'];
		
		/**
		 *	allow If record is successfully added/updated
		*/
		if ($updated)
		{
			/**
			 *	update this action in system log
			 *	Eg:	Edit Department - xyz 
			*/
			$trackingMsg = $action.' '. $formName .' - ';
			
			if (array_key_exists("trackingMsg", $dataArr))
			{
				$trackingMsg = $dataArr['trackingMsg'];
			}
			
			$this->trackEmpSystemAction($trackingMsg.$columnName, $sessionId);
			
			if (array_key_exists("tableName", $dataArr))
			{
				/**
				 *	clear lock flag when update is success
				*/
				if ($action == 'Edit')
					$this->_dbAccessRights->clearSubmitLock ($sessionId, $dataArr['tableName']);
			}
			
			$comboPair = '';
			if (array_key_exists("comboPair", $dataArr))
			{
				$comboPair   = $dataArr['comboPair'];
			}
			
			/**
			 *	success array based on add/update action
			*/

			return array('success' => true, 'msg'=>$formName.' '. ((strtolower($action) === 'status updated') ? ('status updated') : ((strtolower($action) === 'edit') ? 'updated' : 'added')) .' successfully', 'type'=>'success', 'comboPair'=>$comboPair);
		}
		else
		{
			/**
			 *	failure array based on add/update action
			*/
			return array('success' => false, 'msg'=>'Unable to '. ((strtolower($action) === 'status updated') ? ('update '.strtolower($formName).' status') : (((strtolower($action) === 'edit') ? 'update ' : 'add ') .strtolower($formName))), 'type'=>'warning');
		}
	}

	/**
	 *	Delete record used to delete function
	*/
	public function deleteRecord ($dataArr)
	{
		/**
		 *	Variable declaration
		*/
		$deleted      = $dataArr['deleted'];
		$lockFlag     = $dataArr['lockFlag'];
		$formName     = $dataArr['formName'];
		$trackingName = $dataArr['trackingColumn'];
		$sessionId    = $dataArr['sessionId'];
		
		/**
		 *	Check If any other condition have to check with lockflag before delete
		*/
		if (array_key_exists('deleteCondition', $dataArr))
			$condition = $dataArr['deleteCondition'];
		else
			$condition = ($lockFlag == 0);
		
		/**
		 *	Check lockflag is empty continue to delete or not show error message like employee is editing that record
		 */
		if ($condition)
		{
			if ( $deleted )
			{
				if ($formName == 'Inbox')
				{
					$msg = 'Delete Messages in Inbox';
				}
				else
				{
					/** To log the customised message */
					if (array_key_exists("trackingMsg", $dataArr)){
						$msg = $dataArr['trackingMsg'];
					}else{
						$msg = 'Delete '. $formName;
						
						if (!empty($trackingName))
							$msg .= ' - '. $trackingName;
					}
				}
				
				/* Update delete activity in systemlog */
				$this->trackEmpSystemAction($msg, $sessionId);
				
				$comboPair = '';
				if (array_key_exists("comboPair", $dataArr))
				{
					$comboPair   = $dataArr['comboPair'];
				}
				
				if ($formName == 'Inbox')
					$formName = 'Inbox Message(s)';
				
				return array('success'=>true, 'msg'=>$formName .' deleted successfully', 'type'=>'success', 'comboPair'=>$comboPair);
			}
			else
			{
				return array('success'=>false, 'msg'=>'Unable to delete the '. strtolower($formName), 'type'=>'warning');
			}
		}
		else
		{
			/**
			 *	Check that lockflag is login employee or some other employee and
			 *	show error message that employee is open this record for update
			*/
			if ($lockFlag == $sessionId)
			{
				$message = 'Unable to delete '. $formName .'. You have opened this record to update';
			}
			else
			{
				$editEmpName = $this->_dbPersonal->employeeName($lockFlag);
				$message = 'Unable to delete '. $formName.'.'. $editEmpName['Employee_Name'] .' has opened this record to update';
			}
			
			return array('success'=>false, 'msg'=>$message, 'type'=>'info');
		}
	}
	
	/**
	 *	mail COmmunication Settings
	*/
	public function communicateMail ($mailData)
    {
		
        /**
		 *	Declare Array variables
		*/
		$successMsg = $mailData['successMsg'];
		$employeeId = $mailData['employeeId'];
		$moduleName = $mailData['ModuleName'];
		$formName   = $mailData['formName'];
		$formUrl    = $mailData['formUrl'];
		$inboxTitle = $mailData['inboxTitle'];
		$action     = $mailData['action'];
		$customFormName = $mailData['customFormName'];
		$urlString = '';
		$ccEmployeeEmail = '';

		/** If SMTP mail communication credentials exist in the zend registry */
		if (Zend_Registry::isRegistered('smtpHostName') && Zend_Registry::isRegistered('mailUserName') && Zend_Registry::isRegistered('mailPassword')){
			$smtpHostName = Zend_Registry::get('smtpHostName');
			$smtpUserName = Zend_Registry::get('mailUserName');
			$smtpUserPassword = Zend_Registry::get('mailPassword');
			
			/** If SMTP mail communication credentials exist */
			if(!empty($smtpHostName) && !empty($smtpUserName) && !empty($smtpUserPassword)){
				if(!empty($employeeId))
				{
					/**
				 	*	Get all mail communication forms from organization settings 
					*/
					$getMailForms = $this->getMailCommunicationForms ();
					
					/**
					 *	Check this form is exist in that communication form array.
					*	If form exist then only we allow to send mail otherwise we just return success message alone
					*/
					if (in_array($formName, $getMailForms))
					{
						$sendMail    = new Zend_Mail('utf-8');
						$mailGroup   = 'Hr_Group';
						$userSession = $this->getUserDetails ();
						$sessionId   = $userSession['logUserId'];
						$logEmpName  = $this->_dbPersonal->employeeName ($sessionId);
						
						/**
						 *	Check Form Name is Salary Payslip.
						*	Because we will sent the entire mail content otherwise we will make the mail content.
						*/
						if ($formName == 'Salary Payslip')
						{
							$mailContent = $mailData['mailContent'];
						}
						else
						{
							/**
							 *	For status update we will send the mail content from controller itself.
							*/
							if ( array_key_exists ( 'mailContent', $mailData) )
							{
								$mailContent = $mailData['mailContent'];
							}
							else
							{
								$mailContent = "<p>".$logEmpName['Employee_Name'] ." has added a ". $successMsg ." to you.</p>";
							}
							
							//$mailContent .= "<p>Please go to ". $moduleName ." -&gt; ". $formName ." to check it out.<br/>";
						}
						
						$senderEmployeeId	= $sessionId;
						$receiverEmployeeId = $employeeId;
						
						if($formName == 'Resignation' && array_key_exists("Approver_Id", $mailData)){
							$senderEmployeeId = $sessionId;
							if($employeeId == $sessionId){
								$receiverEmployeeId = $mailData['Approver_Id'];
								$mailContent = "<p>".$logEmpName['Employee_Name'] ." has submitted a ". $successMsg ." for your approval.</p>";
							} elseif($mailData['Approver_Id'] == $sessionId){
								$receiverEmployeeId = $employeeId;
							} 
							else{
								$receiverEmployeeId = $mailData['Approver_Id'];
								$resignationEmpName  = $this->_dbPersonal->employeeName ($employeeId);
								$mailContent = "<p>".$logEmpName['Employee_Name'] ." has submitted a ". $successMsg ." of ".$resignationEmpName['Employee_Name']." for your approval.</p>";
							}
						}
						
						/**
						 *	Get Sender and receiver email address from employee details
						*/
						$sendMessage = $this->_dbPersonal->emailAddress($senderEmployeeId, $receiverEmployeeId);
						
						/**
						 *	Check Sender and receiver mail addresses are not empty
						*/
						if (!empty($sendMessage[1]['Emp_Email']) && !empty($sendMessage[0]['Emp_Email']))
						{                
							try {
								/**
								 *	Create final mail content layout
								*/
								if ($formName == 'Salary Payslip')
								{
									//for payslip there is no need to set mail layout since we have separate code for payslip design
									$msgContent = $mailContent;
								}
								else
								{                        
									$basePath = new Zend_View_Helper_BaseUrl();
									//$mailContent .= "<a href = '". $_SERVER['HTTP_HOST'].$basePath->baseUrl($formUrl) ."'>".$_SERVER['HTTP_HOST']."</a></p>";
									$urlString = $_SERVER['HTTP_HOST'].$basePath->baseUrl($formUrl);
									
									$msgContent = $this->_ehrTables->mailLayout("$mailContent", $sendMessage[1]['Employee_Name'],$urlString);
								}
								
								if (!empty($msgContent))
								{
									/**
									 *	Check mail group for adding cc to that group of mail id with this mail
									*/
									if (in_array ($formName, $this->listGroups('Hr_Group')))
										$mailGroup = 'Hr_Group';
									
									if (in_array ($formName, $this->listGroups('Payroll_Group')))
										$mailGroup = 'Payroll_Group';
									
									$mailDetails = $this->mailGroup($mailGroup);

									if (!empty($mailDetails))
									{
										foreach ($mailDetails as $row)
										{
											$statusAccessRights = $this->_dbAccessRights->employeeAccessRights($row['Employee_Id'], $formName);
											
											if ($statusAccessRights['Employee'][$mailGroup] == 1 && ($row['Emp_Email'] != $sendMessage[1]['Emp_Email']) &&
												($row['Emp_Email'] != $sendMessage[0]['Emp_Email']) && !empty($row['Emp_Email']))
											{
												$ccEmployeeEmail = $row['Emp_Email'];
												//adding employee email id in CC field
												$sendMail->addCC($row['Emp_Email'], $row['EmployeeName']);
											}
										}
									}
									
									$orgName = $this->_ehrTables->organizationName();
									
									/**
									 * Amazon SES expects mail id to be a verified. So the email-id configured in application.ini file and change the reply to and from  mail id.
									 */
									$managersemail = $sendMessage[0]['Emp_Email'];
									$sesDefaultEmail = $sendMail->getDefaultFrom('email');//get default from email from application.ini
									$sendMail->setDefaultReplyTo($managersemail);

									if(in_array($formName,array('Compensatory Off','Short Time Off','Leaves'))) {
										$buttonText = 'Review '.$customFormName.' request';
									}else {
										$buttonText = 'Review '.$customFormName;
									}
									$allEmailInputs = array(
										'emailSubject'=> $inboxTitle,
										'ToAddresses'=>$sendMessage[1]['Emp_Email'],
										'CcAddresses'=>$ccEmployeeEmail,
										'supportEmail'=> $this->_orgDetails['HR_Admin_Email_Address'],
										'buttonText'=> $buttonText,
										'subtitle'=>$mailContent,
										'redirectionUrl'=>$urlString
									);

									$emailResponseMesssage = $this->sendBulkTemplateEmail($allEmailInputs);
									if(!empty($emailResponseMesssage)){
										return array('success' => true, 'msg' => $customFormName.' '. $action .' successfully but unable to send an email due to some technical difficulties', 'type' => 'success');
									}else{
										return array('success' => true, 'msg' => $customFormName.' '. $action .' successfully', 'type' => 'success');
									}
									
								}
							}
							/**
							 *	Return exception error message if mail didn't send
							*/
							catch (Zend_Mail_Exception $mailEx)
							{
								return array('success' => true, 'msg' => $customFormName.' '. $action .' successfully but unable to send an email due to some technical difficulties', 'type' => 'success');
							}
						}
						else
						{
							return array('success' => true, 'msg' => $customFormName.' '. $action .' successfully but unable to send an email as the email address is not configured', 'type' => 'success');
						}
					}
					else
					{		
						return array('success' => true, 'msg' => $customFormName.' '. $action .' successfully', 'type' => 'success');
					}
				}
				else
				{
					return array('success' => true, 'msg' => $customFormName.' '. $action .' successfully', 'type' => 'success');
				}
			}else{
				return array('success' => true, 'msg' => $customFormName.' '. $action .' successfully but unable to send an email due to some technical difficulties', 'type' => 'success');
			}
		}else{
			return array('success' => true, 'msg' => $customFormName.' '. $action .' successfully but unable to send an email due to some technical difficulties', 'type' => 'success');
		}
		
    }
	
	/**
	 *	update system about user action
	*/
	public function trackEmpSystemAction($action, $employeeId)
	{
		$ipAddress = isset($_COOKIE['userIpAddress']) ? $_COOKIE['userIpAddress'] : '';
		
		$checkExist = $this->_db->fetchOne($this->_db->select()
										   ->from($this->_ehrTables->systemLog, new Zend_Db_Expr('COUNT(Employee_Id)'))
										   
										   ->where('Employee_Id = ?', $employeeId)
										   ->where('Ip_Address = ?', $ipAddress)
										   ->where('Log_Timestamp = ?', date('Y-m-d H:i:s'))
										   ->where('User_Action LIKE ?', htmlentities(strip_tags(trim($action)))));
		
		if ($checkExist == 0)
		{
			$this->_db->insert($this->_ehrTables->systemLog, array('Employee_Id'   => $employeeId,
																   'Ip_Address'    => $ipAddress,
																   'Log_Timestamp' => date('Y-m-d H:i:s'),
																   'User_Action'   => htmlentities(strip_tags(trim($action)))));
		}
	}
	
	
	
	
    /*Success and failure Alert for insert and update the form details*/
	public function updateSuccessAlert ($trackEmpSection, $formName)
	{
        if ($trackEmpSection['Updated'] == 0)
		{
			$trackEmpSection['Action'] ='Add';
			$trackEmpSection['Message'] = 'Added';
		}
		else
		{
			$trackEmpSection['Action'] ='Edit';
			$trackEmpSection['Message'] = 'Updated';
		}
		
		if ($trackEmpSection['Updated'] > 0 || $trackEmpSection['Updated'] == 0)
		{
			$this->trackEmpSystemAction($trackEmpSection['Action'].' '.$formName.'-'.$trackEmpSection['ColumnName'], $trackEmpSection['SessionId']);
			
			return array('success'=>true, 'msg'=>$formName.' '.$trackEmpSection['Message'].' Successfully', 'type'=>'success');
		}
		else
		{
			return array('success'=>false, 'msg'=>'Unable to '.$trackEmpSection['Action'].' '.$formName, 'type'=>'danger');
		}
	}
    
	/**
	 *	 getUserDetails used to get session user id and user name.
	 */
	public function getUserDetails ()
	{
		$userDetails = array();
		
		if(isset($_COOKIE['empUid'])) {
			$userDetails['logUserName'] = $_COOKIE['empUid'];
			$userDetails['logUserId'] = $this->_loginEmpUser->employeeId($userDetails['logUserName']);
			$userDetails['domain'] = $this->_ehrTables->getOrgCode();
		}

		return $userDetails;

	}

	public function getBankName($formName=null)
	{
		$payrollGeneralSettings    = $this->getPayrollSettings();
        if(!empty($payrollGeneralSettings['Payroll_Country']))
        {
	    	$payrollCountry = $payrollGeneralSettings['Payroll_Country'];
        }
        else
        {
            $payrollCountry = 'IN';
        }

		$qryBankName = $this->_db->select()->from(array('BD'=>$this->_ehrTables->bankDetails), array('Bank_Id','Bank_Name'))
													->where("BD.Country_Code =?",$payrollCountry);
		
		if($formName=='Account Code'||$formName=='Account Schema')
		{
			$qryBankName->joinInner(array('OA'=>$this->_ehrTables->organizationAccount), 'OA.Bank_Id = BD.Bank_Id')
					  ->where('OA.Account_Status Like ?', 'Active');
		}
		
        return $this->_db->fetchPairs($qryBankName);
    }
	
    public function getAccountType()
	{
                return  $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->accountType, array('Account_Type_Id','Account_Type'))->order('Account_Type_Id ASC'));
        }
        public function showCountryDetails()
        {
                return $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->country, array('Country_Code', 'Country_Name'))->order('Country_Name ASC'));
        }
        public function getMonth()
       {
                return $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->month,array('Month_Id','Month_Name'))->order('Month_Id ASC'));
                                                                                
	   }

	   public function getAttendanceYear()
	   {
			$currentYear = date('Y');
			$attendanceYear = array($currentYear-1=>$currentYear-1,$currentYear=>$currentYear,$currentYear+1=>$currentYear+1);
			return $attendanceYear;   
	   }

	   public function getPayslipMonth()
	   {
			$select = $this->_db->select()->from($this->_ehrTables->monthlyPayslip, array('Salary_Month',
													'Formatted_Salary_Month' => new Zend_Db_Expr('CONCAT_WS(",", DATE_FORMAT(STR_TO_DATE(Salary_Month, "%c,%Y"), "%M"), YEAR(STR_TO_DATE(Salary_Month, "%c,%Y")))')))
													->group('Salary_Month')
													->order('STR_TO_DATE(Salary_Month,"%m,%Y") DESC');
			$result = $this->_db->fetchPairs($select);
			return $result;
	   }

	// get combination of month and year(ex: january, yyyy)
    public function getPayslipMonthYear($employeeId=NULL){
		$monthYear = array();
		$monthYearPrev = array();
		$month = $this->getMonth();
		$dbFinanYr = new Default_Model_DbTable_FinancialYear();
		$assYr = $dbFinanYr->getAssessmentYr();
		for($i=1;$i<=count($month);$i++)
        {
			// month 1 -3 having current assesment year
            if($i <= 3){
				$monthYear[$i] =  $month[$i].",".$assYr;
			}
			// month 4-12 having current assesment year - 1
			else{
				$monthYearPrev[$i] =   $month[$i].",".($assYr-1);
			}
		}		
		$monthYearCombine = $monthYearPrev + $monthYear;

		
		if(!empty($employeeId))
		{
			$dateOfJoin = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob, array('Date_Of_Join'))
																					->where('Employee_Id = ?',$employeeId));
			$perquisteMonth = array();
			if(!empty($dateOfJoin))
			{
			   $dbPayslip = new Payroll_Model_DbTable_Payslip();
			   foreach ($monthYearCombine as $key => $payslipMonth) 
			   {
				  /*Based on employee id we need to list the payroll months in that assessment year.
				  we should not list the payroll month which is before employee date of join in that assessment year*/	   
				   $salaryMonthArr = explode(',', $payslipMonth);
				   $salaryDateDetails = $dbPayslip->getSalaryDay(date('m',strtotime($salaryMonthArr[0])),$salaryMonthArr[1]);
			
				   if($salaryDateDetails['Last_SalaryDate'] >= $dateOfJoin)
				   {
						array_push($perquisteMonth,$payslipMonth);
				   } 
			   }
			}
			return $perquisteMonth;
		}
		else 
		{
		    return $monthYearCombine;	
		}
	}
    public function getDesignationId($employeeId)
    {
        return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob,'Designation_Id')->where('Employee_Id = ?', $employeeId));
    }
    
	/**
	 *	List group of form names like HR Group or Payroll Group
	*/
	public function listGroups ($groupName)
    {
		if($groupName=='Hr_Group'){
			$tableName= 'hrGroup';
			$groupName = 'Hr_Group_Id';
		}
		elseif($groupName=='Payroll_Group'){
			$tableName= 'payrollGroup';
			$groupName = 'Payroll_Group_Id';
		}
		
    	return $this->_db->fetchPairs($this->_db->select()
									  ->from(array('O'=>$this->_ehrTables->$tableName),
											 array('J.Employee_Id',
												   new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as EmployeeName")))
									  
									  ->joinInner(array('emp'=>$this->_ehrTables->empPersonal),
												  'FIND_IN_SET(emp.Employee_Id,O.'.$groupName.')', array(''))
									  
									  ->joinInner(array('J'=>$this->_ehrTables->empJob),'emp.Employee_Id=J.Employee_Id', array(''))
									  ->where('J.Emp_Status Like ?', 'Active'));
			
    }
    
    public function listMailForms()
    {
           $getMailForm= array(23=>'Timesheets',31=>'Leaves',139=>'Compensatory Off',
                               33=>'Transfer',34=>'Resignation',35=>'Employee Travel',77=>'Awards',
                               79=>'Complaints',81=>'Performance Evaluation',
                               46=>'Bonus',48=>'Commission',49=>'Deductions',
                               50=>'Reimbursement',53=>'Advance Salary',54=>'Loan',
                               56=>'Shift Allowance',61=>'Tax Declarations',119=>'HRA Decalarations',
							   128=>'Short Time Off',75=>'Warnings');
           
           return $getMailForm;
     }
     
     public function getskillDefinition()
    {
        return $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->skillDefinition,array('SkillDefinition_Id', 'Title'))->order('Title ASC'));
            
            
        ////$qryBankName = $this->_db->select()->from(array('BD'=>$this->_ehrTables->skillDefinition), array('SkillDefinition_Id','Title'));
        //     if($formName=='Account Code'||$formName=='Account Schema')
        //     {
        //           $qryBankName->joinInner(array('P'=>$this->_ehrTables->performance), array(''))
        //           
        //                       ->joinInner(array('SL'=>$this->_ehrTables->skillLevels), 'P.Designation_Id=SL.Designation_Id',
        //                                                        array(''))
        //                       
        //                       ->joinInner(array('SD'=>$this->_ehrTables->skillDefinition),'SD.SkillDefinition_Id=SL.SkillDefinition_Id',
        //                                                        array(''));
        //           
        //     }
        //     return $this->_db->fetchPairs($qryBankName);    
         
        
    }
   
    public function getExpectedSkillLevel()
    {
        $getExpectedSkillLevel=array();
        for($i=1;$i<=100;$i++)
        {
            $getExpectedSkillLevel[$i]=$i;
        }
        return $getExpectedSkillLevel;

  
     }
	
	/**
	 *	List Employee Details based on forms
	*/ 
	public function listEmployeesDetails ($formName, $manager, $employeeId, $punchInDate='',$tdsHistoryType=null,$filterArray=null,$formId=0)
	{
		$adminAccess = '';
		$isManager = '';
		
		if (!empty($formName) && $formName != 'Employee Roles' && $formName != 'noAttendance')
		{
			if (!empty($manager) && ($formName == 'Assignments' /*|| $formName == 'Commission'*/ || $formName == 'Advance Salary'))
			{
				$isManager = $manager;
			}
			else
			{
				if($formName == 'Bulk Attendance Update')
				{
					$accessRights = $this->_dbAccessRights->employeeAccessRights($employeeId,'Attendance');	
				}
				else 
				{
					$accessRights = $this->_dbAccessRights->employeeAccessRights($employeeId, $formName);
				}
		
				$adminAccess  = $accessRights['Admin'];
				$isManager    = $accessRights['Employee']['Is_Manager'];
				/*when the employee is manager he/she can add the performance record for any employee in that organization*/
				if($formName=='Performance Evaluation'&&$isManager==1)
				{
					$adminAccess='admin';
				}
			}
		}

		if($formName == 'noAttendance'){
			$fetchArr = array('J.Employee_Id as Employee_Id',
						  new Zend_Db_Expr("CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name) as Employee_Name"),
						  new Zend_Db_Expr("CASE WHEN J.User_Defined_EmpId IS NULL THEN P.Employee_Id ELSE J.User_Defined_EmpId END as User_Defined_EmpId"),
						  'P.Is_Manager','P.Photo_Path');
		}else{
			$fetchArr = array('J.Employee_Id as value','J.User_Defined_EmpId',
						  new Zend_Db_Expr("CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name,'-',CASE WHEN J.User_Defined_EmpId IS NULL THEN P.Employee_Id ELSE J.User_Defined_EmpId END) as text"),
						  new Zend_Db_Expr("CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name) as Employee_Name"),
						  'D.Department_Name');
		}
		
		switch ($formName)
		{
			case 'Warnings' :
			case 'Memos' :
			case 'Performance Evaluation':
				array_push($fetchArr, 'Dn.Designation_Name');
				break;
			
			case 'Leaves':
            case 'Compensatory Off':
			case 'Short Time Off':
				array_push($fetchArr, 'J.Manager_Id');
				break;
			
			case 'Skillset Assessment':
				array_push($fetchArr, 'J.Date_Of_Join');
				break;
			
			case 'Salary' :
			case 'Salary Import' :
				array_push($fetchArr, new Zend_Db_Expr("DATE_FORMAT(J.Date_Of_Join,'".$this->_orgDF['sql']."') as Effective_Date"));
				break;
		}
		
		$qryManager = $this->_db->select()
							->from(array('J'=>$this->_ehrTables->empJob), $fetchArr)
							
							->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id=J.Employee_Id', array('J.Date_Of_Join'))
							
							->joinInner(array('D'=>$this->_ehrTables->dept), 'D.Department_Id=J.Department_Id', array('D.Department_Name'))
							
							->joinInner(array('Dn'=>$this->_ehrTables->designation), 'Dn.Designation_Id=J.Designation_Id', array('Dn.Designation_Name'));
		
		switch ($formName)
		{
			case 'Warnings':
			case 'Memos':
			case 'Awards':
			case 'Attendance' :
				$qryManager->joinInner(array('hrs'=>$this->_ehrTables->timesheetHrs),'hrs.Grade_Id=Dn.Grade_Id',array());
				break;
			
			case 'Leaves':
			case 'Short Time Off':
				$qryManager
					->joinInner(array('C'=>$this->_ehrTables->empContacts), 'C.Employee_Id=J.Employee_Id', array('C.Mobile_No'))
					
					->joinLeft(array('M'=>$this->_ehrTables->empPersonal), 'M.Employee_Id=J.Manager_Id',
							   array('Manager_Name'=>new Zend_Db_Expr("CONCAT(M.Emp_First_Name,' ',M.Emp_Last_Name)")))
					
					->joinInner(array('WS'=>$this->_ehrTables->workSchedule), 'WS.WorkSchedule_Id = J.Work_Schedule', array(''))
					
					->joinInner(array('L'=>$this->_ehrTables->location), 'L.Location_Id = J.Location_Id', array('L.Location_Name'))
					
					->joinInner(array('T'=>$this->_ehrTables->timesheetHrs), 'T.Grade_Id = Dn.Grade_Id', array('Hrs'=>'T.Regular_Hours'))			
				
					->joinLeft(array('ET'=>$this->_ehrTables->empType), 'ET.EmpType_Id=J.EmpType_Id', array('ET.Employee_Type'));
				$qryManager-> where('J.Date_Of_Join IS NOT NULL');
				
				break;

			/* No attendance employee list. If any of the changes done in leave employee list queries it should be updated in noAttendance list */
			case 'noAttendance':
					$qryManager
						->joinInner(array('C'=>$this->_ehrTables->empContacts), 'C.Employee_Id=J.Employee_Id', array('C.Mobile_No'))
						
						->joinInner(array('WS'=>$this->_ehrTables->workSchedule), 'WS.WorkSchedule_Id = J.Work_Schedule', array(''))
						
						->joinInner(array('L'=>$this->_ehrTables->location), 'L.Location_Id = J.Location_Id', array('L.Location_Name'))
						
						->joinInner(array('T'=>$this->_ehrTables->timesheetHrs), 'T.Grade_Id = Dn.Grade_Id', array('T.Regular_Hours'))
					
						->joinInner(array('ET'=>$this->_ehrTables->empType), 'ET.EmpType_Id=J.EmpType_Id', array());
					
					$qryManager-> where('J.Date_Of_Join IS NOT NULL');
					
					break;				
			
			/* we need to list the employee who doesn't have the leave and the compensatory off for that particular day*/		
            case 'Bulk Attendance Update':
				$qryManager->joinInner(array('hrs'=>$this->_ehrTables->timesheetHrs),'hrs.Grade_Id=Dn.Grade_Id',array())
							->where('J.Employee_Id != ?', $employeeId)
							->where('J.Date_Of_Join <= ?', $punchInDate)
							->where("J.Emp_Status='Active' OR (J.Emp_Status='InActive' AND J.Emp_InActive_Date >= '$punchInDate')")
							->group('J.Employee_Id');
				break;                    
			case 'Commission':
				$qryManager->joinInner(array('SA'=>$this->_ehrTables->salary), 'SA.Employee_Id = J.Employee_Id',
									   array('SA.Basic_Pay'));
				break;
			
			case 'Fixed Insurance' :
			case 'Variable Insurance' :
			case 'Provident Fund' :	
			case 'Bonus' :
			case 'Flexi Benefit Declaration' :
				$qryManager->joinInner(array('ET'=>$this->_ehrTables->empType), 'ET.EmpType_Id=J.EmpType_Id', array())
							->where('ET.Benefits_Applicable = 1');
				
				if($formName == 'Fixed Insurance' || $formName == 'Variable Insurance')
				{
					$qryManager->joinInner(array('IG'=>$this->_ehrTables->insuranceGrade),'IG.Grade_Id=Dn.Grade_Id',array())
					->joinInner(array('I'=>$this->_ehrTables->insurancetypeGrade),'I.Insurance_Grade_Id=IG.Insurance_Grade_Id',array())
					->group('J.Employee_Id');
					
					if($formName == 'Fixed Insurance')
					{
						$qryManager->where('I.Org_ShareAmount IS NOT NULL');
					}
					else
					{
						$qryManager->where('I.Org_ShareAmount IS NULL');
					}
				}
				
				break;
			
			case 'Salary':
			case 'Salary Import' :
				$qryManager->where('J.Employee_Id NOT IN (?)', $this->_db->select()->from($this->_ehrTables->salary, 'Employee_Id'))
							->where('J.Employee_Id NOT IN (?)', $this->_db->select()->from($this->_ehrTables->hourlyWages, 'Employee_Id'));
				
				break;
        }
        
		if($formName == 'Deferred Loan')
        {
            $qryManager->joinInner(array('EL'=>$this->_ehrTables->empLoan), 'EL.Employee_Id=J.Employee_Id', array())
            ->joinInner(array('ET'=>$this->_ehrTables->empType), 'ET.EmpType_Id=J.EmpType_Id', array())
            ->where('ET.Benefits_Applicable = 1')->where('EL.Approval_Status = "Paid"')->group('EL.Employee_Id');
        }
		
		if($formName == 'Employee Roles')
        {
            $rolesEmpQry = $this->_db->select()->from($this->_ehrTables->empAccessRights, array('Employee_Id'))->group('Employee_Id');
            $rolesEmp = $this->_db->fetchCol($rolesEmpQry);
			
			if(!empty($rolesEmp))
			{
				$qryManager->where('J.Employee_Id IN (?)', $rolesEmp);
			}
        }
		
		if($formName == 'Bonus' || $formName == 'Loan' || $formName == 'Shift Allowance' || $formName == 'Flexi Benefit Declaration' || $formName == 'Deductions')
		{
			$managerId = $this->_dbPersonal->ckAddedById($employeeId);	
            
			if(empty($adminAccess) && $isManager == 1)
            {
                $qryManager->where('J.Manager_Id=? or J.Employee_Id = ?', $employeeId);
            }
            //elseif(!empty($managerId))
            //{
            //    $qryManager->where('J.Manager_Id = ?', $managerId);
            //}
			
			$getSalaryWageEmpId = $this->_db->fetchCol($this->_db->select()
                            ->union(array($this->_db->select()->from($this->_ehrTables->salary, array('Employee_Id')),
                            $this->_db->select()->from($this->_ehrTables->hourlyWages, array('Employee_Id')))));
			
			$qryManager->where('P.Employee_Id IN (?)', !empty($getSalaryWageEmpId)?$getSalaryWageEmpId:array(0));
		}
		
		if($formName == 'TDS History')
		{
			$qryManager->joinInner(array('SA'=>$this->_ehrTables->salary), 'SA.Employee_Id = J.Employee_Id', array(''));
			
			$dbFinanYr = new Default_Model_DbTable_FinancialYear();
			$fiscalstartEndDates   = $dbFinanYr->fiscalStartEndDate('PHP');
			
			if($tdsHistoryType == 'New Joinees With Previous Employment')
			{
				$qryManager->where('J.Date_Of_Join > ?', $fiscalstartEndDates['finstart']);
			}
			
			/** TDS History details can be added till employee have the payslip without tax deduction.So Employee details has to be shown
			based on the payslip without tax deduction**/
			$getPayslipEmpId = $this->_db->fetchCol($this->_db->select()->from(array('SP'=>$this->_ehrTables->monthlyPayslip), array('SP.Employee_Id'))
									->joinInner(array('SD'=>$this->_ehrTables->salaryDeduction), 'SD.Payslip_Id=SP.Payslip_Id', '')
									->where('SD.Order_By = 7')
									->where('SD.Deduction_Name = "Tax"')
									->where('SD.Deduction_Amount > 0')
									->group('SP.Employee_Id'));
			
			if(!empty($getPayslipEmpId))
			{
				$qryManager->where('SA.Employee_Id NOT IN (?)',$getPayslipEmpId);
			}
    	}
		
		
		//When the Login Employee is a Manager and not an Admin
        if(empty($adminAccess) && $isManager == 1 && $formName != 'Tax Declarations' &&$formName != 'Employee Roles' && $formName != 'noAttendance')
        {
			if($formName == 'Commission')
			{
				$dbManager=new Default_Model_DbTable_Manager();
				$countManager = $dbManager->countManager($employeeId, $isManager);
				
				if(empty($countManager))
					$qryManager->where('J.Manager_Id = ? or J.Employee_Id = ?', (int)$employeeId);
				else
					$qryManager->where('J.Manager_Id = ?', (int)$employeeId);
			}
			else
			{				
				$qryManager->where('J.Manager_Id=? or J.Employee_Id = ?', (int)$employeeId);
			}
		}
        //When the Login Employee is not a manager & not an Admin
        else if(empty($adminAccess) && $isManager == 0 && $formName != 'Employee Roles' && $formName != 'noAttendance')
        {
            $qryManager->where('J.Employee_Id = ?', (int)$employeeId);
        }
        
		if($formName == 'Commission')
		{
			$qryManager->where('J.Commission_Employee = 1');
		}
		
		$qryManager->where('P.Form_Status = 1');

		if($formName == 'noAttendance'){
			// Filter for employee status in attendance finalization form
			if(!empty($filterArray['employeeStatus'])){
				$qryManager->where('J.Emp_Status IN (?)', $filterArray['employeeStatus']);
			}else{
				$qryManager->where('J.Emp_Status Like ?', 'Active');	
			}
		}
		elseif($formName=='Absentees Report'){
			$qryManager->where('J.Emp_Status Like ?', 'Active');
		}
		elseif(in_array($formName,array('Leaves','Attendance','Short Time Off','Compensatory Off'))){
			$currentDate = date('Y-m-d');
			$previousYear = date('Y-m-d', strtotime('-1 year', strtotime($currentDate)));
			$qryManager->where("J.Emp_Status='Active' OR (J.Emp_Status='InActive' AND J.Emp_InActive_Date >= '$previousYear')");
		}
		elseif($formName=="Salary" || $formName=="Salary Import"){
			$dbFinanYr = new Default_Model_DbTable_FinancialYear();
			$fiscalStartEndDates   = $dbFinanYr->fiscalStartEndDate('PHP');
			$fiscalStartDate=$fiscalStartEndDates['finstart'];
			$qryManager->where("J.Emp_Status='Active' OR (J.Emp_Status='InActive' AND J.Emp_InActive_Date >= '$fiscalStartDate')");
		}
		else{
			$qryManager->where('J.Emp_Status IN (?)',array('Active','InActive'));
		}

		$qryManager->order('P.Emp_First_Name ASC');

		 if($formName=='Attendance' || $formName=='Leaves' || $formName=='Salary' || $formName=='Salary Import'
		 || $formName=='Assignments'|| $formName=='Awards'|| $formName=='Compensatory Off'|| $formName=='Employee Bank Account'
		 || $formName=='Employee Travel'|| $formName=='Employees Document Upload'|| $formName=='Memos'
		 || $formName=='Resignation'|| $formName=='Short Time Off'|| $formName=='Skillset Assessment'|| $formName=='Timesheets'
		 || $formName=='Transfer' || $formName=='Warnings' || $formName=='Allowances' || $formName=='Performance Evaluation'
		 || $formName=='Bonus'|| $formName=='Commission'|| $formName=='Deductions'|| $formName=='NPS'
		 || $formName=='Final Settlement'|| $formName=='Fixed Health Insurance'
		 ||$formName=='Gratuity Nomination'|| $formName=='Insurance' || $formName=='Loan' 
		 ||$formName=='Perquisite Tracker'|| $formName=='Provident Fund' || $formName=='Reimbursement'
		 || $formName=='Shift Allowance' ||$formName=='TDS History' || $formName == 'Compliance Forms'|| $formName  == 'Document Generator' || $formName == 'Complaints')
        {
           $qryManager = $this->getDivisionDetails($qryManager,'J.Department_Id');        
        }    
		
		/* Attendance finalization- no attendance list filters */
		if($formName == 'noAttendance'){
			//Filter for Employee Type
			if(!empty($filterArray['employeeType']))
			{ 
				$qryManager->where('ET.EmpType_Id IN (?)', $filterArray['employeeType']);
			}

			//Filter for Department  
			if(!empty($filterArray['department']))
			{
				$qryManager->where('D.Department_Id IN (?)', $filterArray['department']);
			}
			//Filter for Location 
			if (!empty($filterArray['location']))
			{
				$qryManager->where('L.Location_Id IN (?)', $filterArray['location']);
			}

			//Filter by employee id when the attendance finalization is triggered from salary payslip generation
			if (!empty($filterArray['payslipEmployeeIds']))
			{
				$qryManager->where('J.Employee_Id IN (?)', $filterArray['payslipEmployeeIds']);
			}

			if (isset($filterArray['serviceProviderId'])&&!empty($filterArray['serviceProviderId']))
			{
				$qryManager->where('J.Service_Provider_Id = ?', $filterArray['serviceProviderId']);
			}

			
			$attendanceAccess = $this->_dbAccessRights->employeeAccessRights($employeeId,'Attendance Finalization');
			$isManager    = $attendanceAccess['Employee']['Is_Manager'];	
			if(empty($attendanceAccess['Admin']))
			{
				$getEmployeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
													->where('Manager_Id = ?', $employeeId));
				
				if ($isManager == 1 && !empty($getEmployeeId))
				{
					if($this->_orgDetails['Immediate_Reportees_View_Only']==0)
					{
						$getEmployeeId = $this->getMultiLevelManagerIds($employeeId,1);
						array_push($getEmployeeId,$employeeId);
						$qryManager->where('J.Employee_Id IN (?)', $getEmployeeId);
					}
					else
					{
						$qryManager->where('J.Employee_Id IN (?)', $getEmployeeId);
					}
				}
				else
				{
					$qryManager->where('J.Employee_Id = ?', $employeeId);
				}
			}

			if(!empty($attendanceAccess['Admin']))
			{
				$qryManager = $this->formServiceProviderQuery($qryManager,'J.Service_Provider_Id',$employeeId);
			}

		}

		if(in_array($formName, array('Leaves','Attendance','Bulk Attendance Update','Employee Bank Account','Employees Document Upload','Reimbursement',
		'Salary','Salary Import','TDS History','Bonus','Deductions','Fixed Health Insurance','Loan','Deferred Loan','Compensatory Off','Short Time Off','Assignments','Timesheets','Performance Evaluation','Transfer')))
		{
			$qryManager = $this->formServiceProviderQuery($qryManager,'J.Service_Provider_Id',$employeeId);
		}

		

		// if (!empty($formId)&& in_array($formId, array(332,334,339,352))) 
		// {
		// 	$employeeId = (int) $employeeId; // Ensure it's an integer
		// 	// Exclude the logged-in employee
		// 	$qryManager->where('J.Employee_Id != ?', $employeeId);
		// }
		
		$employeeList = $this->_db->fetchAll($qryManager);
		
		if(!empty($employeeList))
		{
		  $employeeList = $this->listInactiveEmployeeTillFinalSettlement($employeeList);
		}

		return $employeeList;
	}

	public function listInactiveEmployeeTillFinalSettlement($employeeList)
	{
		$dbPayslip = new Payroll_Model_DbTable_Payslip();
		if(isset($employeeList[0]['value']))
		{
			$employeeId  = array_column($employeeList,'value');
		}
		else
		{
			$employeeId  = array_column($employeeList,'Employee_Id');
		}

		if(!empty($employeeId))
		{
			$resignationEmployeeDetails = $this->_db->fetchAll($this->_db->select()->from(array($this->_ehrTables->resignation),array('Employee_Id','Resignation_Date'))
																															->where('Employee_Id IN (?)',$employeeId)
																															->where('Approval_Status = ?', 'Approved'));
			if(!empty($resignationEmployeeDetails))
			{
				$resignationEmployeeId  	  = array_column($resignationEmployeeDetails,'Employee_Id');
				$monthlySalaryEmployeeDetails = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->monthlyPayslip, array('Employee_Id',new Zend_Db_Expr('MAX(STR_TO_DATE(Salary_Month,"%m,%Y")) as Salary_Month')))
															->where('Employee_Id IN (?)',$employeeId)
															->group('Employee_Id'));

				$hourlySalaryEmployeeDetails  = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->wagePayslip, array('Employee_Id',new Zend_Db_Expr('MAX(STR_TO_DATE(Salary_Month,"%m,%Y")) as Salary_Month')))
															->where('Employee_Id IN (?)',$employeeId)
															->group('Employee_Id'));
				$salaryPayslipEmployeeDetails = array();
				if(!empty($monthlySalaryEmployeeDetails) || !empty($hourlySalaryEmployeeDetails))
				{
					array_push($salaryPayslipEmployeeDetails,$monthlySalaryEmployeeDetails);
					array_push($salaryPayslipEmployeeDetails,$hourlySalaryEmployeeDetails);
					$salaryPayslipEmployeeDetails = array_reduce($salaryPayslipEmployeeDetails, 'array_merge', array()); 	
				}
						
				$unsetEmployeeIds = array();
				foreach ($resignationEmployeeDetails as $resignationEmployee)
				{
					$resignationYear 	= date('Y',strtotime($resignationEmployee['Resignation_Date']));
					$resignationMonth 	= date('m',strtotime($resignationEmployee['Resignation_Date']));
					$salaryDateDetails 	= $dbPayslip->getSalaryDateRange($resignationMonth,$resignationYear,strtotime($resignationEmployee['Resignation_Date']));
					$payslipMonth 		= date('Y-m', strtotime($salaryDateDetails['Last_SalaryDate']));
					foreach ($salaryPayslipEmployeeDetails as $salaryPayslipEmployee)
					{
						$salaryMonthArr 	= explode('-', $salaryPayslipEmployee['Salary_Month']);
						$salaryPayslipMonth = date('Y-m', strtotime($salaryMonthArr[0].'-'.$salaryMonthArr[1]));

						if($salaryPayslipEmployee['Employee_Id']==$resignationEmployee['Employee_Id'] && strtotime($payslipMonth)==strtotime($salaryPayslipMonth))
						{
							array_push($unsetEmployeeIds,$resignationEmployee['Employee_Id']);
						}
					}
				}

				foreach ($employeeList as $key => $row)
				{
					foreach ($unsetEmployeeIds as $unsetEmployeeId)
					{
						if(isset($row['value']))
						{
							$employeeId  = $row['value'];
						}
						else
						{
							$employeeId  = $row['Employee_Id'];
						}
						if($unsetEmployeeId==$employeeId)
						{
							unset($employeeList[$key]);
						}
					}
				}
			}
			$employeeList = array_values($employeeList);
		}
		return $employeeList;
	}

	//to get only the monthly salaried employee details so that one can select the employee name from popup
    public function listSalaryEmployee($formName, $isManager, $adminAccess, $empId)
    {
        $assYr = '0';
     
	    if($formName == 'Tax Declarations' || $formName =='HRA Declarations')// for mobile tax declaration add form 
        {
        	$dbFinanYr = new Default_Model_DbTable_FinancialYear();
        	$assYr = $dbFinanYr->getAssessmentYr();
		}
     
	    $qryManager = $this->_db->select()->from(array('S'=>$this->_ehrTables->salary),
												 array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS S.Employee_Id as count'),
													   'Basic_Pay', 'Employee_Id','S.Employee_Id as value'))
		
							->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id=S.Employee_Id',
										array('Employee_Name'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name,'-',CASE WHEN J.User_Defined_EmpId IS NULL THEN P.Employee_Id ELSE J.User_Defined_EmpId END)"),
											  'Employee'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name)"),
											  'Assessment_Year'=>new Zend_Db_Expr($assYr)))
							
							->joinInner(array('J'=>$this->_ehrTables->empJob), 'S.Employee_Id=J.Employee_Id',
										array('J.Emp_Email', 'J.Manager_Id', 'J.User_Defined_EmpId'))
							
							->joinLeft(array('M'=>$this->_ehrTables->empPersonal), 'M.Employee_Id=J.Manager_Id',
									   array('Manager_Name'=>new Zend_Db_Expr("CONCAT(M.Emp_First_Name,' ',M.Emp_Last_Name)")))
							
							->joinInner(array('D'=>$this->_ehrTables->dept), 'D.Department_Id=J.Department_Id',
										array('D.Department_Name'))
							
							->joinInner(array('Dn'=>$this->_ehrTables->designation), 'Dn.Designation_Id = J. Designation_Id',
										array('Dn.Designation_Name'))
							
							->joinInner(array('L'=>$this->_ehrTables->location), 'J.Location_Id = L.Location_Id', 'L.Currency_Symbol')
							
							->where('P.Form_Status = 1')
							->where('J.Emp_Status IN (?)',array('Active','InActive'));									
		
        if($formName == 'Tax Declarations' || $formName =='HRA Declarations')
        {
            $managerId = $this->_dbPersonal->ckAddedById($empId);			
			
			if(empty($adminAccess))
            {
                if($isManager == 1)
				{
					$qryManager->where('J.Manager_Id=? or J.Employee_Id = ?', $empId);	
				}
				elseif(!empty($managerId))
				{
					$qryManager->where('J.Manager_Id = ?', $managerId);
				}
            }
        }
        
		if(empty($adminAccess) && $isManager == 1 && $formName != 'Tax Declarations' && $formName != 'HRA Declarations')
        {
            if($formName == 'Commission')
            {
                $countManager = $this->_dbManager->countManager($empId, $isManager);
				
				if($formName == 'Bonus')
                {
                    $qryManager->where('D.BonusType_Id IS NOT NULL and D.BonusType_Id != 0');
                }
				
				if(empty($countManager))
			        $qryManager->where('J.Manager_Id = ? or J.Employee_Id = ?', (int)$empId);
                else
			        $qryManager->where('J.Manager_Id = ?', (int)$empId);
            }
            else
			    $qryManager->where('J.Manager_Id=? or J.Employee_Id = ?', (int)$empId);
        }
		
        if($formName == 'Commission')
        {
            $qryManager->where('J.Commission_Employee = 1');
		}
	
		if($formName=='Advance Salary' || $formName =='Tax Declarations' || $formName =='HRA Declarations')
		{
			$qryManager = $this->getDivisionDetails($qryManager,'J.Department_Id');
		}

		if(in_array($formName, array('Tax Declarations','HRA Declarations','Advance Salary')))
		{
			$qryManager = $this->formServiceProviderQuery($qryManager,'J.Service_Provider_Id',$empId);
		}
		
		$employeeList= $this->_db->fetchAll($qryManager);

		if(!empty($employeeList))
		{
		  $employeeList = $this->listInactiveEmployeeTillFinalSettlement($employeeList);
		}

		return $employeeList;		
	}
	
	public function listApproverDetails ($employeeId, $isManager='', $logEmpId, $formName=NULL,$isValidMgrList = 0,$approverId=0)
	{
		$approverDetailsList = $validApproverDetails = array();

		if($isManager == 1 && empty($employeeId))
        {
            $managerId = $logEmpId;
        }
        elseif(!empty($employeeId))
        {
            $getManagerId = $this->_dbPersonal->ckAddedById($employeeId);
            $logEmpId = !empty($getManagerId) ? $getManagerId : $employeeId;
            $managerId = $logEmpId;
        }
        else
        {
            $managerId = $this->getManagerOfEmployee($employeeId);
        }
         
        $arrEmpId = array();

		$uplineApprovalFormNameList = array('Short Time Off','Compensatory Off');
		
		if(!IS_NULL($formName) && $formName === 'leaves'){
			$leaveSettingsDet = $this->getLeaveSettings();

			if(!empty($leaveSettingsDet['Allow_Upline_Managers_Approval'])){
				$allowUplineManagersForApproval = 1;
			}else{
				/** If the Allow_Upline_Managers_Approval is disabled, then only the reporting manager should be listed */
				$allowUplineManagersForApproval = 0;
			}
		}else if(!IS_NULL($formName) && in_array($formName,$uplineApprovalFormNameList)){
			if($formName == 'Short Time Off'){
				$formId=129;//Form id for short time off(permission) it will be applicable for on duty as well
			}else if($formName == 'Compensatory Off'){
				$formId=250;
			}
			$allowUplineManagerResult= $this->getUplineManagerFlag($formId);

			if($allowUplineManagerResult == 'Yes'){
				$allowUplineManagersForApproval = 1;
			}else{
				/** If the Allow_Upline_Managers_Approval is disabled, then only the reporting manager should be listed */
				$allowUplineManagersForApproval = 0;
			}
		}else{
			$allowUplineManagersForApproval = 1;
		}		

		/** If the allowUplineManagersForApproval is 1 then get upline managers */
		if(!empty($allowUplineManagersForApproval)){
			while ( $managerId != NULL || $managerId != 0)
			{
				$rowManagers = $this->_db->fetchOne($this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),array('J.Manager_Id'))
												->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'J.Employee_Id=P.Employee_Id')
												->where('P.Employee_Id = ?', $managerId)
												->where('P.Is_Manager =1')
												->where('J.Emp_Status Like ?', 'Active')
												->where('P.Form_Status = 1'));
				/** If the employee id/employee reporting manager does not have the manager
				 *  Or if the employee id/employee reporting manager already pushed in the arrEmpId array
				 *  Or if the login employee id/employee id and the login employee id/employee id-reporting manager are same */				
				if(in_array($rowManagers,$arrEmpId) || empty($rowManagers) || $logEmpId == $rowManagers)
				{
					break;
				}
				else
				{
					array_push($arrEmpId,$rowManagers);
					$managerId = $rowManagers;
				}
			}
		}
		
		if ( empty($arrEmpId) && empty($employeeId))
        {
            array_push($arrEmpId, $logEmpId);
        }
		
        if (!empty($employeeId))
        {
            array_push($arrEmpId, $getManagerId);
        }
		
		/** Get the manager details */
		$qryManager = $this->_db->select()
									->from(array('J'=>$this->_ehrTables->empJob),
										   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS J.Employee_Id as count'),
												 'J.Emp_Email','Manager_Id' => 'J.Employee_Id'))
									
									->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id=J.Employee_Id',
												array('Employee_Name'=>new Zend_Db_Expr("CASE WHEN J.User_Defined_EmpId!='' AND J.User_Defined_EmpId IS NOT NULL THEN CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name,'-',J.User_Defined_EmpId) ELSE CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name,'-',J.Employee_Id) END")))												
									
									->where('J.Employee_Id In (?)', $arrEmpId)
									->where('P.Form_Status = 1')
									->where('J.Emp_Status Like ?', 'Active')
									->where('P.Is_Manager = 1');
        
		if (!empty($employeeId))
		{
			/**
			 *	Get Timesheet hours and Mobile no for leaves 
			*/
			$qryEmployee = $this->_db->select()
										->from(array('J'=>$this->_ehrTables->empJob), array('J.Employee_Id'))
										
										->joinInner(array('Dn'=>$this->_ehrTables->designation), 'Dn.Designation_Id=J.Designation_Id', array())
										
										->joinInner(array('T'=>$this->_ehrTables->timesheetHrs), 'T.Grade_Id = Dn.Grade_Id',
													array('T.Regular_Hours'))
										
										->joinInner(array('C'=>$this->_ehrTables->empContacts), 'C.Employee_Id=J.Employee_Id',
													array('C.Mobile_No'))
										
										->where('J.Employee_Id = ?', $employeeId);
			
			$qryManager
				->joinLeft(array('L'=>$qryEmployee), 'L.Employee_Id='.$employeeId, array('L.Regular_Hours', 'L.Mobile_No'));
		}

        $manager = $this->_db->fetchAll($qryManager);
        
        $actManager = '';
		$empRole = '';
		/** If the manager exist then get the actual manager details */
		/*if  one manager exist for the performance evaluvation in that time also we need to get the actual manager details*/
        if(count($manager) > 1 || ($formName==='Performance Evaluation'&&count($manager) >= 1))
        {			
            $actManager = $this->_db->select()
                                           ->from(array('J'=>$this->_ehrTables->empJob),array('J.Emp_Email','J.Manager_Id'))
                                            
                                           ->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id=J.Manager_Id',
										   			   array('Employee_Name'=>new Zend_Db_Expr("CASE WHEN J.User_Defined_EmpId!='' AND J.User_Defined_EmpId IS NOT NULL THEN CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name,'-',J.User_Defined_EmpId) ELSE CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name,'-',J.Employee_Id) END")));                                                       
                                            
                                           
            
            $actManager->joinLeft(array('L'=>$qryEmployee), 'L.Employee_Id='.$employeeId, array('L.Regular_Hours', 'L.Mobile_No'));
                
            $actManager->where('J.Employee_Id In (?)', $employeeId);

            $actManager = $this->_db->fetchAll($actManager);			
		}
		elseif(count($manager) == 0 && $formName==='Performance Evaluation')
		{
			$actManager = $this->_db->fetchAll($this->_db->select()
                                                        ->from(array('J'=>$this->_ehrTables->empJob),array('J.Emp_Email'))
                                                         
                                                        ->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id=J.Employee_Id',
														array('Employee_Name'=>new Zend_Db_Expr("CASE WHEN J.User_Defined_EmpId!='' AND J.User_Defined_EmpId IS NOT NULL THEN CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name,'-',J.User_Defined_EmpId) ELSE CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name,'-',J.Employee_Id) END"),
                                                             'Manager_Id'=>'P.Employee_Id'))
                                                         ->joinLeft(array('L'=>$qryEmployee), 'L.Employee_Id='.$employeeId, array('L.Regular_Hours', 'L.Mobile_No'))
														->where('J.Employee_Id In (?)', $employeeId));

		}
		
		/** If the manager does not exist then get the admin employee details and consider the employee as manager */
        if(count($manager) == 0 && $formName !='Performance Evaluation')
        {
			$adminEmployeeId = $this->listAdmins();

            $manager = $this->_db->fetchAll($this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),array('J.Emp_Email'))
                                                         
                                                        ->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id=J.Employee_Id',
																	array('Employee_Name'=>new Zend_Db_Expr("CASE WHEN J.User_Defined_EmpId!='' AND J.User_Defined_EmpId IS NOT NULL THEN CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name,'-',J.User_Defined_EmpId) ELSE CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name,'-',J.Employee_Id) END"),
                                                                          'Manager_Id'=>'P.Employee_Id'))
                                                        
                                                        ->joinInner(array('Dn'=>$this->_ehrTables->designation), 'Dn.Designation_Id=J.Designation_Id', array())
                                                        
                                                        ->joinLeft(array('T'=>$this->_ehrTables->timesheetHrs), 'T.Grade_Id = Dn.Grade_Id',
                                                                    array('T.Regular_Hours'))
                                                        
                                                        ->joinInner(array('C'=>$this->_ehrTables->empContacts), 'C.Employee_Id=J.Employee_Id',
                                                                    array('C.Mobile_No'))
                                                         
												        ->where('J.Employee_Id In (?)', $adminEmployeeId));

        }
		
		if($formName==='Performance Evaluation')
		{
			$manager = $this->listEmployeeAndManagerDetails('listManagerDetails');
			
		}
		
        $empSal = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->salary, 'Basic_Pay')
											  ->where('Employee_Id = ?', $employeeId));
	
		if((int)$isValidMgrList === 1){
			if(count($manager) >= 1)
			{
				$approverDetailsList = $manager;
			}
			else
			{
				if($empRole[0] && !IS_NULL($empRole[0])){
					$approverDetailsList = $empRole;
				}
			}

			/** Push the valid approver(manger id) in an array */
			if(count($approverDetailsList) >0){
				foreach($approverDetailsList as $empValidApproverDetail){
					if(!empty($empValidApproverDetail)){
						$validApproverDetails[] = $empValidApproverDetail;
					}
				}
			}
		}

		/* check whether the given approverId is active and the approverId should be manager.
			when the approverId is not manager or not active we need to list the active manager details.
			As of now its implemented only on edit timesheet */
		if(!empty($approverId))
		{
			if(!empty($manager))
			{
				$managerId = array_column($manager,'Manager_Id');
				//we need to check whether the given approver id is part of employee's manager group
				if(!(in_array($approverId,$managerId)))
				{
					$approvalManager = $this->listEmployeeAndManagerDetails('listTimesheetManagerDetails',$approverId);
					
					/*when the approverId is not part employee manager group and approverId is active 
					we need to consider the approverId as manager group */
					if(!empty($approvalManager))
					{
						$manager 	= $approvalManager;
						$actManager = $approvalManager;
					}
				}
			}
		}

        return array('Manager'=>$manager,'ActualManager'=> $actManager, "IsAdmin"=>$empRole,'EmpSal'=> $empSal,'validApproverDetails'=>$validApproverDetails);
	}

	public function executeCommonLibraryFunction($formId = null, $typeOfFunction, $functionName, $inputs = null) {
        $apiBaseUrl = Zend_Registry::get('employeeSelfServiceRoBaseURL');
		$orgCode = $this->_ehrTables->getOrgCode();
        $apiHeaders = $this->getApiHeaders($orgCode);
		$errorCode = null;
		$data = array(
			'query' => 'query retrieveCommonLibCommonFunctions($formId: Int, $typeOfFunction: String!, $functionName: String!, $inputs: String) {
				retrieveCommonLibCommonFunctions(formId: $formId, typeOfFunction: $typeOfFunction, functionName: $functionName, inputs: $inputs) {
					errorCode
					message
					response
				}
			}',
			'variables' => array(
				'formId' => $formId,
				'typeOfFunction' => $typeOfFunction,
				'functionName' => $functionName,
				'inputs' => $inputs
			)
		);
	
		$ch = curl_init($apiBaseUrl);
	
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $apiHeaders);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
	
		$response = curl_exec($ch);
		curl_close($ch);

		// Convert the API response to an associative array
		$responseArray = json_decode($response, true);

		if (isset($responseArray['data']['retrieveCommonLibCommonFunctions']['errorCode'])) {
			$errorCode = $responseArray['data']['retrieveCommonLibCommonFunctions']['errorCode'];
		} else {
			return false;
		}
    
		if (empty($errorCode) || $errorCode === null) {
			// No error, proceed with processing the data

			// Extract the "response" value from the array
			$responseData = $responseArray['data']['retrieveCommonLibCommonFunctions']['response'];

			// Convert the response data to an array
			$responseArray = json_decode($responseData, true);
			return $responseArray;
		} else {
			return false;
		}
	}	

	public function listAdmins() {
        $response = $this->executeCommonLibraryFunction(null, 'func', 'listAdmins', null);
        return $response;
	}

	public function getManagerOfEmployee($employeeId) {
		return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob, 'Manager_Id')
											  ->where('Employee_Id = ?', $employeeId));
	}

	/*list all the manager in that organization*/
	public function listEmployeeAndManagerDetails($action,$employeeId=null,$coverageForAlternatePerson=null)
	{
		
		$qryEmployee = $this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),array('J.Emp_Email'))
                                                         
                                                        ->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id=J.Employee_Id',
																	array('Employee_Name'=>new Zend_Db_Expr("CASE WHEN J.User_Defined_EmpId!='' AND J.User_Defined_EmpId IS NOT NULL THEN CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name,'-',J.User_Defined_EmpId) ELSE CONCAT(P.Emp_First_Name,' ',P.Emp_Last_Name,'-',J.Employee_Id) END"),                                                                    
                                                                          'Manager_Id'=>'P.Employee_Id'))
                                                        
                                                        ->joinInner(array('Dn'=>$this->_ehrTables->designation), 'Dn.Designation_Id=J.Designation_Id', array())
                                                        
                                                        ->joinLeft(array('T'=>$this->_ehrTables->timesheetHrs), 'T.Grade_Id = Dn.Grade_Id',
                                                                    array('T.Regular_Hours'))
                                                        
                                                        ->joinInner(array('C'=>$this->_ehrTables->empContacts), 'C.Employee_Id=J.Employee_Id',
																	array('C.Mobile_No'))
														->where('P.Form_Status = 1')
														->where('J.Emp_Status Like ?', 'Active');		
		if($action==='listManagerDetails')
		{
			$qryEmployee->where('P.Is_Manager = 1');
		}
		elseif(!empty($coverageForAlternatePerson)&&!empty($employeeId)) 
		{
			if($coverageForAlternatePerson==='Department')
			{
				$departmentId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob,array('Department_Id'))
																	->where('Employee_Id = ?',$employeeId));
				if(!empty($departmentId))
				{
					$qryEmployee = $this->listDeparmentWiseEmployeeDetails($qryEmployee,'J.Department_Id',$departmentId);	
				}
			}
			$qryEmployee = $this->formServiceProviderQuery($qryEmployee,'J.Service_Provider_Id',$employeeId);

			$qryEmployee->where('P.Employee_Id !=?',$employeeId);
		}
		elseif($action==='listTimesheetManagerDetails'&&!empty($employeeId))
		{
			$qryEmployee->where('P.Is_Manager = 1')
						->where('P.Employee_Id = ?',$employeeId);
		}
														
		return $this->_db->fetchAll($qryEmployee);														
	}

	
	/**
     * Get tax entity details to load in filter combo
     */
    public function allTaxEntityPair()
    {
        return $this->_db->fetchPairs($this->_db->select()->from($this->_ehrTables->taxEntity, array('Tax_Entity_Id', 'Tax_Entity_Name'))
						->order('Tax_Entity_Name ASC'));        
    }
    
    
    public function getGratuityAmount($employeeId=null,$basicSal=null,$allowanceId=null,$gratuity=null)
    {
        if(empty($employeeId) && (!empty($allowanceId)))
        {
			$getAllowancesBenefits = $this->getAllowanceBenefitAssociation($allowanceId);
			if(in_array('Gratuity',$getAllowancesBenefits))
			{
				$employeeId =  $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->allowances,array('Employee_Id'))
                                		->where('Gratuity_Applicable = ?',1));
      		}
        }   
        
        if(empty($employeeId))
        {
            $employeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->salary,
                                               array('Employee_Id'))
                                        ->where('Is_GratuityEmployee = ?',1));
            if($gratuity != 'SAL')
            {
                $hrlyEmp = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->hourlyWages,
                                               array('Employee_Id'))
                                        ->where('Is_GratuityEmployee = ?',1));
                $employeeId = array_merge($employeeId,$hrlyEmp);
            }
        }
        
        if(count($employeeId) > 1)
        {
            for($emp = 0;$emp <count($employeeId);$emp++)
            {
               $gratAmount =  $this->updateGratuityAmount($employeeId[$emp],$allowanceId,$gratuity,$basicSal);
               
            }    
        }
        else
        {
            
            $gratAmount =  $this->updateGratuityAmount($employeeId,$allowanceId,$gratuity,$basicSal);
        }
        return $gratAmount;
    }
    
    public function updateGratuityAmount($employeeId,$allowanceId=null,$grat=null,$basicSal=null)
    {
        if($grat == 'SAL')
        {
            $basicPay = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->salary,
                                                           array('Basic_Pay','Location_Id','Grade_Id','Is_GratuityEmployee'))
                                                    ->where('Employee_Id IN (?)',$employeeId));
        }
        else
        {
            $basicPay = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->hourlyWages,
                                                           array('Regular_Hourly_Wages','Location_Id','Grade_Id','Is_GratuityEmployee'))
                                                    ->where('Employee_Id IN (?)',$employeeId));
        }
            
        if($basicSal == '' && $grat == 'SAL')
            $basicSal = $basicPay['Basic_Pay'];
        elseif($grat == 'HWSAL')
           $basicSal = $basicPay['Regular_Hourly_Wages']; 
		
		$gratuityAmt = 0;
        
        $empLocGrade = $this->_db->quoteInto("(Location_Id = ?", $basicPay['Location_Id']).
                       $this->_db->quoteInto("and Grade_Id = ? ) OR ", $basicPay['Grade_Id'] ).
                       $this->_db->quoteInto('Employee_Id IN (?)) or (', $employeeId).
                       $this->_db->quoteInto(" (Location_Id IS NULL and Grade_Id IS NULL and Employee_Id IS NULL) OR  (Location_Id IS NULL and Grade_Id = ?) ", $basicPay['Grade_Id']);
        
        $qryGratuity = $this->_db->fetchAll($this->_db->select()->from(array('A'=>$this->_ehrTables->allowances),array(''))
											->joinLeft(array('AT'=>$this->_ehrTables->allowanceTypes),'AT.Allowance_Type_Id=A.Allowance_Type_Id',array('AT.Period','A.Percentage','A.Amount'))
											->joinInner(array('ABA'=>$this->_ehrTables->allowanceBenefitAssoc),'ABA.Allowance_Type_Id = AT.Allowance_Type_Id',array(''))
											->joinInner(array('BF'=>$this->_ehrTables->benefitForms),'BF.Form_Id = ABA.Form_Id',array(''))
											->where('BF.Form_Name = ?', 'Gratuity')
											->where('AT.Allowance_Mode = ?', 'Non Bonus')
                                            ->where('A.Allowance_Status LIKE ?', 'Active')
											->where($empLocGrade)
                                            ->group('A.Allowance_Id')
                                            ->bind(array('location'=>$basicPay['Location_Id'],'grade'=>$basicPay['Grade_Id'])));

		/* avoid non numeric value encountered error*/									
		$basicSal = (float)$basicSal;
											
        for($i=0;$i<count($qryGratuity);$i++)
        {
            if($qryGratuity[$i]['Percentage'] != '')
            {
                if($qryGratuity[$i]['Period'] == 'Monthly')
                    $gratuity = round(($basicSal*($qryGratuity[$i]['Percentage']/100)),2);
                elseif($qryGratuity[$i]['Period'] == 'Quarterly')
                    $gratuity = round(($basicSal*($qryGratuity[$i]['Percentage']/100))/3,2);
                elseif($qryGratuity[$i]['Period'] == 'HalfYearly')
                    $gratuity = round(($basicSal*($qryGratuity[$i]['Percentage']/100))/6,2);
                else
                    $gratuity = round($basicSal*($qryGratuity[$i]['Percentage']/100)/12,2);
                
                $gratuityAmt += number_format($gratuity,2,'.','');
            }
            else
            {
                if($qryGratuity[$i]['Period'] == 'Monthly')
                    $gratuity = $qryGratuity[$i]['Amount'];
                elseif($qryGratuity[$i]['Period'] == 'Quarterly')
                    $gratuity = $qryGratuity[$i]['Amount']/3;
                elseif($qryGratuity[$i]['Period'] == 'HalfYearly')
                    $gratuity = $qryGratuity[$i]['Amount']/6;
                else
                    $gratuity = $qryGratuity[$i]['Amount']/12;
                
                $gratuityAmt += number_format($gratuity,2,'.','');
            }
        }
        
        $getGratuitySett = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->gratuitySettings,
                                               array('Working_Days','Org_Salary_Days','Part_Of_GratuityAct')));
        
        if(!empty($getGratuitySett))
        {
		
			$getGratuitySett['Org_Salary_Days'] = (int)$getGratuitySett['Org_Salary_Days'];
			
			$gratAmt = 0;
            if($grat != 'HWSAL')
                $gratAmt = round(($basicSal + $gratuityAmt) * ($getGratuitySett['Org_Salary_Days']/$getGratuitySett['Working_Days']),2);
            else
                $gratAmt = round($basicSal * $getGratuitySett['Org_Salary_Days'],2);
     		
            if(!empty($allowanceId) && $basicPay['Is_GratuityEmployee'])
            {
				if(!empty($employeeId))
				{
    				$updated = $this->_db->update($this->_ehrTables->salary,array('Salary_Recalc'=>1),array('Employee_Id ='.$employeeId));
         		}
                return true;
            }
            elseif($grat == 'GRAT')
            {
				if(!empty($employeeId))
				{
                	$updated = $this->_db->update($this->_ehrTables->salary,array('Salary_Recalc'=>1),array('Employee_Id='.$employeeId));
                }
                	$gratAmt = round(($basicSal * $getGratuitySett['Org_Salary_Days']),2);
                return true;
            }
            elseif($grat == 'SAL' || $grat == 'HWSAL')
            {
                return $gratAmt;
            }
        }
        else
        {
            return false;
        }
    }
	/* when editing the monthly salary we are calling this function to calculate the gratuity amount*/
	public function calculateGratuityAmount($basicPay,$allowanceDetails)
	{
		$getGratuitySettings = $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->gratuitySettings,
											array('Working_Days','Org_Salary_Days','Part_Of_GratuityAct')));

		if(!empty($getGratuitySettings))
		{
			$gratuityEnabledAllowance = 0;
			if(!empty($allowanceDetails))
			{
				for($i=0;$i<count($allowanceDetails);$i++)
				{
					$getAllowancesBenefits = $this->getAllowanceBenefitAssociation($allowanceDetails[$i][4]);
					
					if(in_array('Gratuity',$getAllowancesBenefits))
					{
							/*gratuityApplicable allowance comes in different period we need to make that as monthly allowance and calculate the gratuity amount*/						
		
						   if($allowanceDetails[$i][2] == 'Monthly')
								$gratuityAllowancePerMonth = $allowanceDetails[$i][1];
							elseif($allowanceDetails[$i][2] == 'Quarterly')
								$gratuityAllowancePerMonth = $allowanceDetails[$i][1]/3;
							elseif($allowanceDetails[$i][2] == 'HalfYearly')
								$gratuityAllowancePerMonth = $allowanceDetails[$i][1]/6;
							else
								$gratuityAllowancePerMonth = $allowanceDetails[$i][1]/12;
							
							$gratuityEnabledAllowance += number_format($gratuityAllowancePerMonth,2,'.','');	
					}
				}	
			}

			$gratuityAmount = number_format(($basicPay + $gratuityEnabledAllowance) * ($getGratuitySettings['Org_Salary_Days']/$getGratuitySettings['Working_Days']),2,'.','');
		
			return $gratuityAmount;
		}
		else 
		{
			return 0;
		}									   
  	}
	
	
//Get last payslip month for the employee
public function getLastPayslipMonth($employeeId=null, $salaryType=null,$formName=null, $formId = 0)
{
	// Resignation_Date_Override flag in Org Settings
	$resignationDateOverride = ''; 
	// as default have payslip generated as 0
	$payslipGenerated  = 0;

	$dbPayslip = new Payroll_Model_DbTable_Payslip();
	//If employee id exsit, then find the maxminum payslip month to calculate the cutoff date. 
	//Incase no payslip exsit for this employee id, to find employee date of join for calculating the cutoff date.
	if($employeeId > 0)
	{
		//get employee salary type
		$empsalaryType = $dbPayslip->getEmpSalaryType($employeeId);
		if(!empty($empsalaryType))
		{
			if($empsalaryType=='Monthly')
			{
				$tableName = $this->_ehrTables->monthlyPayslip; 
			}
			else
			{
				$tableName = $this->_ehrTables->wagePayslip;
			}
			$payslipId = $this->_db->fetchOne($this->_db->select()->from($tableName,array('Payslip_Id'))
							->where('Employee_Id = ?',$employeeId));		
		}
		else
		{
			$payslipId = null;
		}
		
		//check whether atleast single payslip exist whether employee level or organization level based on Employee Id Parameter.
		if($payslipId != '' && $payslipId != null)
		{
			$qrySalaryMonth = $this->_db->select()->from($tableName,array(new Zend_Db_Expr('MAX(STR_TO_DATE(Salary_Month,"%m,%Y")) as SalaryMonth')))
							->where('Employee_Id = ?',$employeeId);

			$salaryMonth = $this->_db->fetchOne($qrySalaryMonth);
			
			$salaryMonthArr = explode('-', $salaryMonth);

			//If payslip month is december, then add month and year to get next payslip month
			if($salaryMonthArr[1] == 12)
			{
				$payslipMonth = 1 ;
				$payslipYr = $salaryMonthArr[0] + 1;
			}
			else
			{
				//If payslip month is not equal to december, then add month
				$payslipMonth = $salaryMonthArr[1]+1;
				$payslipYr =  $salaryMonthArr[0];
			}



			//Based on consideration day and payment day to set minimum date.
			$getSalaryDay = $dbPayslip->getSalaryDay($payslipMonth, $payslipYr, $formId);
			
			$previousCutoffDate = date('Y-m-d', strtotime($getSalaryDay['Prev_CutoffDate']));
			$cutoffDate = date('Y-m-d', strtotime($getSalaryDay['Cutoff_Date']));
			
			//get employee resignation date. 
			$empResignationDate = $this->getEmployeeResignationDate($employeeId);
				
			if(!empty($empResignationDate) && !empty($cutoffDate))
			{
				//If employee resignation date should be lessser then previous cut-off date and greater then cut-off date means to consider cutoff date as employee resignation date
				if(($previousCutoffDate <= $empResignationDate) && ($cutoffDate >= $empResignationDate ))
				{
					$cutoffDate = date('Y-m-d', strtotime($empResignationDate));
				}
				else
				{
					$cutoffDate = date('Y-m-d', strtotime($getSalaryDay['Cutoff_Date']));
				}
			}
			else
			{
				$cutoffDate = date('Y-m-d', strtotime($getSalaryDay['Cutoff_Date']));	
			}	
			$salaryDate =  date('Y-m-d', strtotime($getSalaryDay['Salary_Date']));
			$lastSalaryDate =  date('Y-m-d', strtotime($getSalaryDay['Last_SalaryDate'])); 

			// check whether the form name is available and the form name is resignation
			if($formName && $formName == 'Resignation') {
				$resignationDateOverride = (int)$this->_orgDetails['Resignation_Date_Override'];
			}
			$payslipGenerated = 1;

			return array('previousCutoffDate' => $previousCutoffDate,'cutoffDate' => $cutoffDate, 'salaryDate' => $salaryDate, 'lastSalaryDate' => $lastSalaryDate,'resignationDateOverride'=> $resignationDateOverride,'payslipGenerated' => $payslipGenerated);
		}
		else
		{
			//Get Employee date of join
			$dateOfJoin = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob, array('Date_Of_Join'))
										->where('Employee_Id = ?',$employeeId));
			//get employee resignation date
			$empResignationDate = $this->getEmployeeResignationDate($employeeId);
			
			//In initial state of setup, consider the assessment year and fiscal month for calculating cutoff date.	
			$currentAssessmentYear =  $this->_orgDetails['Assessment_Year'];
			$assessmentYr = $currentAssessmentYear - 1;
			$fiscalMonth =  $this->_orgDetails['Fiscal_StartMonth'];

			$getSalaryDay = $dbPayslip->getSalaryDay($fiscalMonth, $assessmentYr, $formId);
			
			if($formName === 'additionalWageClaim'){
				/** If employee joined before the financial start month we need to return the financial start month - salary start date
				 * to validate payslip generation in the UI.*/ 
                if(strtotime($dateOfJoin) < strtotime($getSalaryDay['Salary_Date'])){
					$salaryDate =  date('Y-m-d', strtotime($getSalaryDay['Salary_Date']));
				}else{
					$salaryDate = date('Y-m-d', strtotime($dateOfJoin));//employee date of join
				}
			}else{
				//Employee date of join is consider as set to minimum date of shift scheduling
				$salaryDate = date('Y-m-d', strtotime($dateOfJoin));
			}
			$lastSalaryDate =  date('Y-m-d', strtotime($getSalaryDay['Last_SalaryDate']));
			
			//If employee date of join is leaser then previous cutoff date, then set previous cut-off date as getsalaryday pervious cut-off date
			if((strtotime($dateOfJoin)) <= (strtotime($getSalaryDay['Prev_CutoffDate'])))
			{
				$previousCutoffDate = date('Y-m-d', strtotime($getSalaryDay['Prev_CutoffDate']));
				$cutoffDate = date('Y-m-d', strtotime($getSalaryDay['Cutoff_Date']));
			}
			else
			{
				$dateOfJoinYear = date('Y',strtotime($dateOfJoin));
				$dateOfJoinMonth = date('m',strtotime($dateOfJoin));
				
				/* Get the paycycle start and end date for the date of join month and year */
				$getDateOfJoinSalaryDay = $dbPayslip->getSalaryDateRange($dateOfJoinMonth,$dateOfJoinYear,strtotime($dateOfJoin));
				
				$lastSalaryDate =  date('Y-m-d', strtotime($getDateOfJoinSalaryDay['Last_SalaryDate']));
				$payslipPreviousCutoffDate = date('Y-m-d', strtotime($getDateOfJoinSalaryDay['Prev_CutoffDate']));
				$payslipcutoffDate = date('Y-m-d', strtotime($getDateOfJoinSalaryDay['Cutoff_Date']));
				$currentDate = date('Y-m-d');
				//If employee date of join is greater then previous cutoff date, then set previous cut-off date as employee date of join
				if(strtotime($dateOfJoin) >  strtotime($payslipPreviousCutoffDate))
				{
					$previousCutoffDate = date('Y-m-d', strtotime($dateOfJoin));
				}
				else
				{
					$previousCutoffDate = date('Y-m-d', strtotime($payslipPreviousCutoffDate));
				}
				//If current date is greater then  cutoff date, then set  cut-off date as cutoff date
				//If employee have no payslip to allow the cut-off date as current date
				if(strtotime($currentDate) >  strtotime($payslipcutoffDate))
				{
					$cutoffDate = date('Y-m-d', strtotime($currentDate));
				}
				else
				{
					$cutoffDate = date('Y-m-d', strtotime($payslipcutoffDate));
				}
			}
			
			if(!empty($empResignationDate) && !empty($cutoffDate))
			{
				//If employee resignation date between the previous cutoff date and cut-off date then set the cutoff date as employee resignation date. 
				if(($previousCutoffDate <= $empResignationDate) && ($cutoffDate >= $empResignationDate ))
				{
					$cutoffDate = date('Y-m-d', strtotime($empResignationDate));
				}
				//If employee resignation date is greater then previous and current cut off date, then set cut-off date as current cut-off date.
				elseif(($previousCutoffDate < $empResignationDate) && ($cutoffDate < $empResignationDate ))
				{
					$cutoffDate = date('Y-m-d', strtotime($cutoffDate));
				}
				else
				{
					$cutoffDate = $empResignationDate;
				}
			}
			else
			{
				$cutoffDate = date('Y-m-d');  
			}  
			
			// check whether the form name is available and the form name is resignation
			if($formName && $formName == 'Resignation') {
				$resignationDateOverride = (int)$this->_orgDetails['Resignation_Date_Override'];
			}
			$payslipGenerated = 0;

			return array('previousCutoffDate' => $previousCutoffDate,'cutoffDate' => $cutoffDate, 'salaryDate' => $salaryDate,'lastSalaryDate' => $lastSalaryDate,'resignationDateOverride'=> $resignationDateOverride,'payslipGenerated' => $payslipGenerated);
		}
	}
	else
	{
		//check whether atleast single payslip exist whether employee level or organization level of monthly or hourly wages 
		if($salaryType == 'Monthly')
		{
			$qryMonthlysalaryMonth = $this->_db->select()->from($this->_ehrTables->monthlyPayslip, array(new Zend_Db_Expr('MAX(STR_TO_DATE(Salary_Month,"%m,%Y")) as SalaryMonth')));
			$monthlysalaryMonth = $this->_db->fetchOne($qryMonthlysalaryMonth);

			$hourlySalaryMonth = '';
		}
		elseif($salaryType == 'Hourly')
		{
			$qryhourlySalaryMonth = $this->_db->select()->from($this->_ehrTables->wagePayslip, array(new Zend_Db_Expr('MAX(STR_TO_DATE(Salary_Month,"%m,%Y")) as SalaryMonth')));
			$hourlySalaryMonth = $this->_db->fetchOne($qryhourlySalaryMonth);
			
			$monthlysalaryMonth = '';
		}
		else
		{
			$qryMonthlysalaryMonth = $this->_db->select()->from($this->_ehrTables->monthlyPayslip, array(new Zend_Db_Expr('MAX(STR_TO_DATE(Salary_Month,"%m,%Y")) as SalaryMonth')));
			$monthlysalaryMonth = $this->_db->fetchOne($qryMonthlysalaryMonth);
			
			$qryhourlySalaryMonth = $this->_db->select()->from($this->_ehrTables->wagePayslip, array(new Zend_Db_Expr('MAX(STR_TO_DATE(Salary_Month,"%m,%Y")) as SalaryMonth')));
			$hourlySalaryMonth = $this->_db->fetchOne($qryhourlySalaryMonth);	
		}
		

		if(!empty($monthlysalaryMonth) || !empty($hourlySalaryMonth))
		{
						
			//If Monthly paylsip month is grater than hourly payslip month, then should be consider to calculate the cutoff days based on hourly salary month.
			if((strtotime($monthlysalaryMonth)) <= (strtotime($hourlySalaryMonth)))
			{
				$payslipMonth = $hourlySalaryMonth;
			}
			else
			{
				$payslipMonth = $monthlysalaryMonth;	
			}   
			$payslipMonthArr = explode('-', $payslipMonth);
			
			//If payslip month is december, then add month and year to get next payslip month
			if($payslipMonthArr[1] == 12)
			{
				$payslipMonth = 1 ;
				$payslipYr = $payslipMonthArr[0] + 1;
			}
			else
			{
				//If payslip month is not equal to december, then add month
				$payslipMonth = $payslipMonthArr[1]+1;
				$payslipYr =  $payslipMonthArr[0];
			}

			$getSalaryDay = $dbPayslip->getSalaryDay($payslipMonth, $payslipYr, $formId);

			$previousCutoffDate = date('Y-m-d', strtotime($getSalaryDay['Prev_CutoffDate']));
			$cutoffDate = date('Y-m-d', strtotime($getSalaryDay['Cutoff_Date']));

			return array('previousCutoffDate' => $previousCutoffDate,'cutoffDate' => $cutoffDate, 'salaryDate' => $getSalaryDay['Salary_Date'], 'lastSalaryDate' => $getSalaryDay['Last_SalaryDate'],'resignationDateOverride'=> $resignationDateOverride,'payslipGenerated' => $payslipGenerated);
	   } 
	   else
	   {
			//In inital state of setup, consider the assessment year and fiscal month for calculating cutoff date.	
			$currentAssessmentYear =  $this->_orgDetails['Assessment_Year'];;
			$assessmentYr = $currentAssessmentYear - 1;
			$fiscalMonth =  $this->_orgDetails['Fiscal_StartMonth'];

			//when its attendance finalization when there is no payslip generated we need to list the current month data
			if($formId==190)
			{
				$currentDate 	= date('Y-m-d');
				$paycyleDate 	= $dbPayslip->getSalaryDateRange(date('m',strtotime($currentDate)), date('Y',strtotime($currentDate)),strtotime($currentDate));
				$lastSalaryDate = $paycyleDate['Last_SalaryDate'];
				$assessmentYr 	= date('Y',strtotime($lastSalaryDate));
				$fiscalMonth  	= date('m',strtotime($lastSalaryDate));
			}

			$getSalaryDay   = $dbPayslip->getSalaryDay($fiscalMonth, $assessmentYr, $formId);
			$lastSalaryDate = $getSalaryDay['Last_SalaryDate'];

			//get minimum date of join
			$dateOfJoin = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob, array(new Zend_Db_Expr('Min(Date_Of_Join)'))));
			
			//if the employee date of join lesser then or equal to previous cutoff date, 
			//then set previous cutoff date as previous cutoff date and cutoff date as current date. 
			if((strtotime($dateOfJoin)) <= (strtotime($getSalaryDay['Prev_CutoffDate'])))
			{
				$previousCutoffDate = date('Y-m-d', strtotime($getSalaryDay['Prev_CutoffDate']));
				$cutoffDate = date('Y-m-d', strtotime($getSalaryDay['Cutoff_Date']));
			}
			else
			{
				$dateOfJoinYear = date('Y',strtotime($dateOfJoin));
				$dateOfJoinMonth = date('m',strtotime($dateOfJoin));

				/* Get the paycycle start and end date for the date of join month and year */
				$getDateOfJoinSalaryDay = $dbPayslip->getSalaryDateRange($dateOfJoinMonth,$dateOfJoinYear,strtotime($dateOfJoin));
				$lastSalaryDate =  $getDateOfJoinSalaryDay['Last_SalaryDate'];

				$payslipPreviousCutoffDate = date('Y-m-d', strtotime($getDateOfJoinSalaryDay['Prev_CutoffDate']));
				$payslipcutoffDate = date('Y-m-d', strtotime($getDateOfJoinSalaryDay['Cutoff_Date']));
				$currentDate = date('Y-m-d');

				//If  minimum date of join is greater then previous cutoff date, then set previous cut-off date as minimum date of join
				if(strtotime($dateOfJoin) >  strtotime($payslipPreviousCutoffDate))
				{
					$previousCutoffDate = date('Y-m-d', strtotime($dateOfJoin));
				}
				else
				{
					$previousCutoffDate = date('Y-m-d', strtotime($payslipPreviousCutoffDate));
				}
				//If current date is greater then  cutoff date, then set  cut-off date as cutoff date
				if(strtotime($currentDate) > strtotime($payslipcutoffDate))
				{
					$cutoffDate = date('Y-m-d', strtotime($currentDate));
				}
				else
				{
					$cutoffDate = date('Y-m-d', strtotime($payslipcutoffDate));
				}
			}

			return array('previousCutoffDate' => $previousCutoffDate,'cutoffDate' => $cutoffDate,'salaryDate' => $getSalaryDay['Salary_Date'],'lastSalaryDate' => $lastSalaryDate,'resignationDateOverride'=> $resignationDateOverride,'payslipGenerated' => $payslipGenerated);

		}   
	}
}


public function getDeductionSettings()
{
	return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->deductionSettings,array('Allow_Future_Month_Calendar')));
}

public function getAdhocAllowanceSettings()
{
	return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->adhocAllowanceSettings,array('Allow_Future_Month_Calendar')));
}

//Get payment min date based on salary month and return last salary date
public function getPaymentTrackerMinDate($salaryMonth)
{
	$salMonthYear 	= explode(',', $salaryMonth);
	$dbPayslip = new Payroll_Model_DbTable_Payslip();
	$salaryDates 	= $dbPayslip->getSalaryDay($salMonthYear[0],$salMonthYear[1]);		
	$lastSalaryDate = $salaryDates['Last_SalaryDate'];

	return date('Y-m-d', strtotime($lastSalaryDate));
}
	
	/** get salary start date based active employees **/
	public function getActiveEmployees($salaryStartDate)
	{
		if(!is_null($salaryStartDate) && !empty($salaryStartDate))
		{
			$conditions = $this->_db->quoteInto('(Emp_Status LIKE ? and ', 'InActive') .
                        $this->_db->quoteInto('Emp_InActive_Date >= ?)', $salaryStartDate).
						' OR '. $this->_db->quoteInto('Emp_Status LIKE  ?','Active');
		}
		else{
			$conditions = $this->_db->quoteInto('Emp_Status LIKE ?','Active');
		}
		
		return $conditions;
	}
    
    /** Update DataSetup Dashboard Status**/
    public function updateDataSetupDashboard($status, $formId)
    {
        $dataSetupStatus = $this->_db->fetchOne($this->_db->select()
                                                ->from($this->_ehrTables->datasetupDashboard,
                                                                array('Status'))
                                                ->where('Form_Id = ?',$formId));

            $updated = 1;

        if($dataSetupStatus == 'Open' || ($formId == '18' && $dataSetupStatus == 'Inprogress' && $status == 'Completed'))
        {
            $updated = $this->_db->update($this->_ehrTables->datasetupDashboard, array('Status'=> $status), array('Form_Id = '.$formId));
        }
        
        return $updated;
	}
	
	public function s3initialize($bucket) {
		require_once APPLICATION_PATH."/../vendor/autoload.php";

		$dbFormDownlaod   = new FormsManager_Model_DbTable_FormDownloads();
		$keys = $dbFormDownlaod->getAwsKeys();
		
		$s3Client = new Aws\S3\S3Client([
			'region'  => $keys['region'],
            'version' => $keys['version'],
				'credentials' => [
				'key' => $keys['key1'],
				'secret' => $keys['key2'],
			],
		]);

		if($bucket == 'imageBucket') {
			$bucketName = $keys['imageBucket'];
		} else if($bucket == 'logoBucket') {
			$bucketName = $keys['logoBucket'];
		} else {
			$bucketName = $keys['bucketName'];
		}

		return array('s3Config'=> $s3Client, 'bucketName'=> $bucketName);
	}
	/** Based on the logo/image, check whether it exists in s3bucket or not **/
	public function getAwsSignedUrl($type,$empImageName=NULL,$bucket=NULL,$module=NULL,$serviceProviderId=NULL)
	{
		$picturePath = '';
		
		$config = $this->s3initialize($bucket);

		if($type == 'ReportLogo')
		{
			$orgCode = $this->_ehrTables->getOrgCode();
			if(!empty($serviceProviderId))
			{
				$reportLogoName = $this->_ehrTables->getOrgReportLogo($orgCode,$serviceProviderId);
				if(!empty($reportLogoName))
				{
					$picturePath    = $this->_ehrTables->uploadPath.'logos/service_provider/'.$reportLogoName;
				}
			}
			else
			{
				$reportLogoName = $this->_ehrTables->getOrgReportLogo($orgCode);
				if(!empty($reportLogoName))
				{
					$picturePath    = $this->_ehrTables->uploadPath.'logos/'.$reportLogoName;
				}
			}
		}
		else if($type == 'ProductLogo')
		{
			$domainName = Zend_Registry::get('Domain');
			
			$domain = explode('.', $domainName);
			
			$picturePath = 'hrapp_upload/'.$domain[0].'_productLogo.svg';
		}
		else if($type == 'EmployeeProfilePicture')
		{
			$picturePath = $this->_ehrTables->uploadPath.'images/'.$empImageName;

        }
        else if($type == 'Employee Document Upload' || $type == 'LandingPageOrgLogo')
        {
            $picturePath = $empImageName;
        }
		
		/** If picture path is not empty **/
		if(!empty($picturePath))
		{
            /** to check that the image exists in s3bucket or not **/
            $imgExists = $config['s3Config']->doesObjectExist($config['bucketName'],$picturePath);

			if($imgExists)
			{
                //Creating a presigned URL
                $cmd = $config['s3Config']->getCommand('GetObject', [
                    'Bucket' => $config['bucketName'],
                    'Key' => $picturePath
                ]);

				try
				{
					if($module == 'reimbursementReport')
					{
						$validity = Zend_Registry::get('reimbursementSignedURLValidity');
					}
					else
					{
						$validity = Zend_Registry::get('signedURLValidity');
					}
					$request = $config['s3Config']->createPresignedRequest($cmd, $validity);
					// Get the actual presigned-url
					$url = (string)$request->getUri();
					if($type == 'ReportLogo' || $type == 'ProductLogo')
					{
						$url = strstr($url,'?',true);
					}
				}
				catch(S3Exception $e){
					$url = '';
				}
				catch (AwsException $e) {
					$url = '';
				}
			}
			else
			{
				$url = '';
			}
		}
		else
		{
			$url = '';
        }
        
		return $url;
    }
// To create a URL (from s3) for the file to upload 
	public function getAwsSignedPutUrl($empImageName,$bucket=NULL)
    {
		$config = $this->s3initialize($bucket);

        $cmd = $config['s3Config']->getCommand('putObject', [
            'Bucket' => $config['bucketName'],
            'Key' => $empImageName
        ]);

        $request = $config['s3Config']->createPresignedRequest($cmd, Zend_Registry::get('signedURLValidity'));

        // Get the actual presigned-url
        $url = (string)$request->getUri();

        return $url;

    }
// To delete the document in the s3
	public function deleteS3Document($empImageName,$bucket)
    {
		$config = $this->s3initialize($bucket);

        $picturePath = $empImageName;

        // Delete an object from the bucket.
        $config['s3Config']->deleteObject([
            'Bucket' => $config['bucketName'],
            'Key'    => $picturePath
        ]);

        return "Document deleted";

	}
	// To get the file content from s3 to download the file
	public function getFileContent($empImageName,$bucket)
    {
		$config = $this->s3initialize($bucket);

        $picturePath = $empImageName;

        // Get the object.
        $result = $config['s3Config']->getObject([
            'Bucket' => $config['bucketName'],
            'Key'    => $picturePath
        ]);

        // Display the object in the browser.
        header("Content-Type: {$result['ContentType']}");

        return  (string) $result['Body'];

	}
	// The string to form the query to get the employee name
	public function getEmployeeNameQuery($alias)
	{
		// return "CONCAT(". $alias .".Emp_First_Name, ' ',IFNULL(CONCAT(". $alias .".Emp_Middle_Name,' '),''), ". $alias .".Emp_Last_Name)";
		return "CONCAT(" . $alias . ".Emp_First_Name, ' ', IF(" . $alias . ".Emp_Middle_Name IS NOT NULL AND " . $alias . ".Emp_Middle_Name != '', CONCAT(" . $alias . ".Emp_Middle_Name, ' '), ''), " . $alias . ".Emp_Last_Name)";
	}
	/** Get employee resignation date for set maximum date in all payroll forms **/
	public function getEmployeeResignationDate($employeeId)
    {
        return $this->_db->fetchOne($this->_db->select()
					->from(array($this->_ehrTables->resignation),
					array("Resignation_Date"))
					//array(new Zend_Db_Expr("DATE_FORMAT(Resignation_Date,'".$this->_orgDF['sql']."') as ResignationDate")))
					->where('Employee_Id = ?', $employeeId)
					->where('Approval_Status = ?', 'Approved'));
		
    }
	
	/** Get employee resignation month and year(yyyy-mm) for set maximum month in all payroll forms **/
	public function getEmployeeResignationMonth($employeeId)
    {
        return $this->_db->fetchOne($this->_db->select()
					->from(array($this->_ehrTables->resignation),
					array(new Zend_Db_Expr("date_format(Resignation_Date, '%Y-%m') as Resignation_Date")))
					->where('Employee_Id = ?', $employeeId)
					->where('Approval_Status = ?', 'Approved'));
		
    }
 
	/*Based on cookie value employee details should be listed in the grid*/
    public function getDivisionDetails($qryEmployee,$fieldName)
    {
        if (isset($_COOKIE['HrappDepartmentClassificationId']) && $_COOKIE['HrappDepartmentClassificationId'] > 0)
        {
            $department  = $_COOKIE['HrappDepartmentClassificationId'];
            $qryEmployee = $this->listDeparmentWiseEmployeeDetails($qryEmployee,$fieldName,$department);
        }
        return $qryEmployee;
    }

	/*List the employee based on division/department based on parent and child details */
	public function listDeparmentWiseEmployeeDetails($qryEmployee,$fieldName,$department)
    {
		//Based on the department id we need to get the child and its child id consecutively.
		$departmentParentChildQry = "SELECT Department_Id FROM(SELECT * from department ORDER BY Parent_Type_Id, Department_Id) department, (SELECT @pv := '$department') initialisation WHERE   find_in_set(Parent_Type_Id, @pv) > 0 AND @pv := concat(@pv, ',', Department_Id)";
		$query = $this->_db->query($departmentParentChildQry);
		$rows = $query->fetchAll();
		$getDepChildIds = array_column($rows,'Department_Id');

		if(!empty($getDepChildIds))
		{
			array_push($getDepChildIds,$department);
			$qryEmployee->where($fieldName.' IN (?)',$getDepChildIds);
		}
		else 
		{
		    $qryEmployee->where($fieldName.' = ?', $department);	
		}
	
		return $qryEmployee; 
    }

	/** get the fiscal months in an array by frequency **/
	public function getFiscalMonthByFrequency($paymentFrequency)
	{
		$dbFinanYr = new Default_Model_DbTable_FinancialYear();
		
		/** Get all fiscal months **/
        $fiscalAllMonths = $dbFinanYr->financialYr(); //array(4,5...12,1,2,3)
		
		/** get the index of the jan month **/
    	$janIndex = array_keys($fiscalAllMonths,1);
		
		if($paymentFrequency == 'Annually')
		{
			$fiscalMonthArr = array();
			
			/** get the index of the jan month **/
			$lastIndex = end($fiscalAllMonths);
			
			$dateObj   = DateTime::createFromFormat('!m', $lastIndex);
			$monthName = $dateObj->format('F');
			
			$fiscalMonthArr[$lastIndex] = $monthName;
			//$fiscalMonthArr	=3;
			
			//return $fiscalMonthArr;
            return $lastIndex;
		}
		else
		{
			$fMonthKeyArr = $fiscalMonthKeyArr = $fiscalMonthValArr = $fMonthValArr = array();
			
			/** Get all the fiscal months till end month of financial start year  **/
			$uptoDec = array_slice($fiscalAllMonths,0,$janIndex[0]);//array(4,5,...12)
			
			/** Get all the fiscal months till end month of financial End year  **/
			$uptoEnd = array_slice($fiscalAllMonths,$janIndex[0]);//array(1,2,3)
			
			foreach($uptoDec as $key=>$row){
				$fiscalStartMonthVal = '';
				
				$fiscalStartMonthVal = $row;				
				
				/** get month name for all the months **/
				if($paymentFrequency == 'Monthly')
				{
					$dateObj   = DateTime::createFromFormat('!m', $fiscalStartMonthVal);
					$monthName = $dateObj->format('F');
					
					array_push($fiscalMonthValArr,$monthName);
				}
				/** push month number in an array **/
				array_push($fiscalMonthKeyArr,$fiscalStartMonthVal);				
			}
			
			foreach($uptoEnd as $key=>$row){
				$fiscalEndMonthVal = '';
				
				$fiscalEndMonthVal = $row;				
				
				/** get month name for all the months **/
				if($paymentFrequency == 'Monthly')
				{
					$dateObj   = DateTime::createFromFormat('!m', $fiscalEndMonthVal);
					$monthName = $dateObj->format('F');
					
					array_push($fiscalMonthValArr,$monthName);	
				}				
				
				/** push month number in an array **/
				array_push($fiscalMonthKeyArr,$fiscalEndMonthVal);
				
			}
			
			if($paymentFrequency == 'Monthly')
			{
				/** combine key and row values to form an array **/
				//$fiscalMonthArr = array_combine($fiscalMonthKeyArr,$fiscalMonthValArr);
				$fiscalMonthArr	= implode(',',$fMonthKeyArr);

				return $fiscalMonthArr;
			}
			else
			{
				$monthFiscal= array();
				if($paymentFrequency == 'Quarterly'){
					/** split the array into arrays by 3 index  **/
					$fMonthArr = array_chunk($fiscalMonthKeyArr,3,true);
				}
				else{
					/** split the array into arrays by 6 index  **/
					$fMonthArr = array_chunk($fiscalMonthKeyArr,6,true);
				}
				
				foreach($fMonthArr as $fiscalArr)
				{
					/** get the last index from each array **/
					$quarterMonthVal = end($fiscalArr);
					
					/** get month name for the last index **/
					$dateObj   = DateTime::createFromFormat('!m', $quarterMonthVal);
					$monthName = $dateObj->format('F');

					
					array_push($fMonthKeyArr,$quarterMonthVal);
					array_push($fMonthValArr,$monthName);
				}
				
				/** combine key and row values to form an array **/
				//$fiscalMonthArr = array_combine($fMonthKeyArr,$fMonthValArr);
				$fiscalMonthArr	= implode(',',$fMonthKeyArr);

				return $fiscalMonthArr;
			}
		}
	}

	/* Check whether monthly or hourly payslip exists in the organization */
	public function checkPayslipExistsInOrgLevel(){
		$monthlyPayslipCount = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->monthlyPayslip, 
										array(new Zend_Db_Expr('COUNT(Payslip_Id)'))));
		
		$hourlyPayslipCount = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->wagePayslip, 
										array(new Zend_Db_Expr('COUNT(Payslip_Id)'))));

		/* If monthly or hourly payslip exists in the organization */
		if(!empty($monthlyPayslipCount) || !empty($hourlyPayslipCount)){
			return 1;
		}else{
			return 0;
		}		
	}

	/* Function to decrypt the email */
	public function fnDecrypt($sValue)
	{
		// $sSecretKey = "PeacockMango/07@";

		// $data = base64_decode($sValue);
                                                
		// $iv = substr($data, 0, mcrypt_get_iv_size(MCRYPT_RIJNDAEL_128, MCRYPT_MODE_CBC));

		// 	$decrypted = rtrim(
		// 		mcrypt_decrypt(
		// 			MCRYPT_RIJNDAEL_128,
		// 			hash('sha256', $sSecretKey, true),
		// 			substr($data, mcrypt_get_iv_size(MCRYPT_RIJNDAEL_128, MCRYPT_MODE_CBC)),
		// 			MCRYPT_MODE_CBC,
		// 			$iv
		// 		),
		// 		"\0"
		// 	);
		return $sValue;
	}

	/**
	 * For check the request is from mobile app or not
	 * Here we check the header contains the secret key & app version match with the configurations
	 */
	public function isRequestFromMobileApp() {
        $request = new Zend_Controller_Request_Http();
        $key = $request->getHeader('appSecretKey');
        $version = $request->getHeader('appVersion');
		$versions = Zend_Registry::get('appVersion');
		if (Zend_Registry::get('appSecretKey') == $key && in_array($version, Zend_Registry::get('appVersion'))) {
			setcookie("isRequestFromMobile", 1, 0, "/");
			if($version === end($versions)){
				setcookie("isAppUptoDate", 1, 0, "/");
			}else{
				setcookie("isAppUptoDate", 0, 0, "/");
			}
			return true;
		} else {
			return false;
		}
	}

	// get employee names
	public function getEmpName($employeeId){
		$empNames = array();
		
		if(!empty($employeeId)){
			$qryEmpNames = $this->_db->select()->from(array('ep'=>$this->_ehrTables->empPersonal), 
							array('Employee_Name'=>new Zend_Db_Expr('GROUP_CONCAT(DISTINCT ep.Emp_First_Name, " ", ep.Emp_Last_Name) ')))
							->where('ep.Employee_Id IN (?)', $employeeId);
			$empNames = $this->_db->fetchCol($qryEmpNames);
		}
		
		return $empNames;
	}

	/* Function to get the IP address restriction flag value and whitelisted IP address */
	public function getWhitelistedIpAddress($orgCode = null)
	{
		$whitelistedIPAddress = array();

		/* if the request is received from the mobile app then we will get the orgcode as input */
		if($orgCode == null){
			$ipAddressRestriction = $this->_orgDetails['IP_Address_Restriction'];
		} else {
			$ipAddressRestriction = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->orgDetails, array("IP_Address_Restriction"))->where('Org_Code = ?',$orgCode));
		}
		
		/* If the IP address restriction is enabled then retrieve the white listed IP address. */
		if($ipAddressRestriction == 1){
			$whitelistedIPAddress = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->whitelistedIpAddresses, array("Ip_Address")));
		} 

		/* Return response */
		return array('IP_Address_Restriction' 	=> $ipAddressRestriction,
				 		'Whitelisted_IP_Address'=> $whitelistedIPAddress);
	}

	public function mapCustomGroup($parentId, $formId, $customGroupId, $action){

		switch($action)
		{
			case 'insert':
				$insertGroupDetails = array('Parent_Id' => $parentId,
											'Form_Id' => $formId,
											'Custom_Group_Id' => $customGroupId);

				return $this->_db->insert($this->_ehrTables->customGroupAssociateForm, $insertGroupDetails );

			case 'update':
				$updateGroupDetails = array('Custom_Group_Id' => $customGroupId);

				return $this->_db->update($this->_ehrTables->customGroupAssociateForm, $updateGroupDetails, 'Parent_Id='.$parentId.' AND Form_Id = '. $formId);

			case 'delete':
				$whereCondition['Parent_Id = ?'] = $parentId;
				$whereCondition['Form_Id = ?'] = $formId;

				if(!empty($customGroupId))
				{
					$whereCondition['Custom_Group_Id = ?'] = $customGroupId;
				}
				return $this->_db->delete($this->_ehrTables->customGroupAssociateForm,$whereCondition);

			default:
				return 0;
		}
	}

	/**Return salutation to be used in the employee import, employee form, candidates(employee onboard) form */
	public function getSalutation(){
		$salutation = array('Mr'=>'Mr',
							'Miss'=>'Miss',
							'Mrs'=>'Mrs',
							'Ms'=>'Ms',
							'Dr'=>'Dr',
							'Prof'=>'Prof');
							
		return $salutation;
	}

	/** Get the salutation based on the employee gender, marital status */
	public function getEmpSalutationBasedGenderMaritalStatus($salutation,$gender,$maritalStatus){
		$newSalutation = $salutation;
		
		if(!empty($salutation) && !empty($gender) && !empty($maritalStatus)) {
			$vSalutation = strtolower($salutation);
			$vGender = strtolower($gender);

			/** If the marital status is not number, fetch the marital status id */
			if(!is_numeric($maritalStatus)){
				$maritalStatusIdQry = $this->_db->select()->from(array('ep'=>$this->_ehrTables->maritalStatus), array('Marital_Status_Id'))
										->where('Marital_Status = ?', $maritalStatus);

				$maritalStatus = $this->_db->fetchOne($maritalStatusIdQry);
			}

			if($vSalutation !== 'dr' && $vSalutation !== 'prof'){
				if($vGender == "male")
				{
					$newSalutation = 'Mr';
				}
				else if($vGender == "female" && $maritalStatus == 1)
				{
					$newSalutation =  'Miss';
				}
				else if($vGender == "female" && $maritalStatus !== 1)
				{
					$newSalutation = 'Mrs';
				}

				if($vGender == "female" && $vSalutation=='ms')
				{
					$newSalutation = 'Ms';
				}
			}


		}

		return $newSalutation;
	}

	/** Return the app configuration details */
	public function getAppConfiguration(){
		return $this->_db->fetchRow($this->_db->select()->from(array('AP'=>$this->_ehrTables->appConfiguration), array('*')));
	}

	// Round off the given value based on settings
	public function getRoundOffValue($roundOffFor, $value ,$roundOffSettings='',$semiValue=2)
	{
		if(empty($roundOffSettings))
		{
			$roundOffSettings = $this->getRoundOffSettings($roundOffFor);
		}

		if(!empty($roundOffSettings))
		{
			if($roundOffSettings['Multiples_Of']=='0.5')
			{
				switch($roundOffSettings['Round_Off_Settings_Id'])
				{
					case 1: return round($value * $semiValue) / $semiValue; // 1 means round to nearest,round to nearest 0.5 or 1
					case 2: return ceil($value * $semiValue) / $semiValue; // 2 means round up to next digit,round upto next 0.5 or 1
					case 3: return floor($value * $semiValue) / $semiValue; // 3 means round down to previous digit, round upto previous 0.5 or 1
					default: return $value; // 0 means do not round off return value as is
				}
			}
			else
			{
				switch($roundOffSettings['Round_Off_Settings_Id'])
				{
					case 1: return round($value); // 1 means round to nearest, If the value is 6444.44 then it will be rounded to 6444 and 6444.78 will be rounded to 6445
					case 2: return ceil($value); // 2 means round up to next digit,If the value is 6444.44 then it will be rounded to 6445 and 6444.78 will also be rounded to 6445
					case 3: return floor($value); // 3 means round down to previous digit, If the value is 6444.44 then it will be rounded to 6444 and 6444.78 will also be rounded to 6444
					default: return $value; // 0 means do not round off return value as is
				}
			}
		}

		return $value;
	}

	public function getRoundOffSettings($roundOffFor)
	{
		$qryPayrollRoundOffSettings = $this->_db->select()->from($this->_ehrTables->payrollRoundOffSettings, array('Round_Off_Settings_Id','Multiples_Of'))
													->where('Round_Off_For = ?',$roundOffFor);

		$roundOffSettings = $this->_db->fetchRow($qryPayrollRoundOffSettings);

		return $roundOffSettings;
	}

	public function getPayrollSettings()
	{
		return $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->payrollGeneralSettings, array('*')));
	}

	/** Get the leave settings details */
	public function getLeaveSettings(){
		return $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->leaveSettings, array('*')));
	}

	/** Get the holiday settings */
	public function getHolidaySettings() {
		return $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->holidaySettings, array('Holiday_Settings_Type as Holiday_Settings')));
    }

	/** Validate the adhoc allowance record title when the form is submitted */
	public function validateEarningsDeductionTitle($title,$formName){
		$reservedKeywordsArr = $this->getReserveKeyWordDetails($formName);
		/** If the reserved keywords are given then return false */
		if(in_array($title,$reservedKeywordsArr)){
			return false;
		}else{
			return true;
		}
	}

	public function getReserveKeyWordDetails($formName)
	{
		$hrReports 			   = new Reports_Model_DbTable_HrReports();
		$reserveKeyWordDetails = $hrReports->getReportHeaders('Salary Register',NULL,$formName);
		// Replace underscore with space for each element in the array
		$reservedKeywordsArr = array_map(function ($value) {
			return str_replace('_', ' ', $value);
		}, $reserveKeyWordDetails);
		return $reservedKeywordsArr;
	}

	/** Function to get the billing subscription details for the organization code based on the employee admin access */
	public function getBillingSubscriptionDetails($orgCode){
		$billingResponse = array(
			'message' => '',
			'billingSubscriptionDetails' => array(),
			'autoBilling' => 0
		);

		$apiCustomDomainUrl = $this->_ehrTables->getApiCustomDomainUrlPath('billing');

		if(!empty($apiCustomDomainUrl)){
			$curl = curl_init();
			$method = "POST";

			$requestBody = '{ 
				"query": "query getOrganizationBillingDetails { getOrganizationBillingDetails { errorCode message autoBilling manualBillingDetails autoBillingDetails }}"
			}';
			//Set API headers
			$apiHeaders = $this->getApiHeaders($orgCode);
        	curl_setopt($curl, CURLOPT_HTTPHEADER, $apiHeaders);
			
			curl_setopt($curl, CURLOPT_POST, 1);
			curl_setopt($curl, CURLOPT_POSTFIELDS, $requestBody);
			curl_setopt($curl, CURLOPT_URL, $apiCustomDomainUrl);
			curl_setopt($curl, CURLOPT_HTTPHEADER, $apiHeaders);
			curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
	
			// Call the endpoint
			$getOrganizationBillingDetails = curl_exec($curl);

			if(!$getOrganizationBillingDetails)
			{
				curl_close($curl);
				$billingResponse['message'] = 'Billing Subscription details does not retrieved.';
				return $billingResponse;
			}else{
				curl_close($curl);
				$getOrganizationBillingDetails = json_decode($getOrganizationBillingDetails,true);
				if(isset($getOrganizationBillingDetails['errors'])){
					$billingResponse['message'] =
					((count($getOrganizationBillingDetails['errors']) > 0) && $getOrganizationBillingDetails['errors'][0]['message']) ?
						$getOrganizationBillingDetails['errors'][0]['message'] :
							'Something went wrong while processing the request to retrieve the billing subscription details.';
				}else if(isset($getOrganizationBillingDetails['data'])){
					$billingSubscriptionSuccessResponse = $getOrganizationBillingDetails['data']['getOrganizationBillingDetails'];
					if($billingSubscriptionSuccessResponse['errorCode']){
						$billingResponse['message'] =
							($billingSubscriptionSuccessResponse['message']) ?
								$billingSubscriptionSuccessResponse['message'] :
									'Something went wrong while retrieving the billing subscription details.';
					}else{
						$billingResponse['billingSubscriptionDetails'] =
							($billingSubscriptionSuccessResponse['manualBillingDetails']) ?
								(json_decode($billingSubscriptionSuccessResponse['manualBillingDetails'],true)) :
									array();
						$billingResponse['autoBilling'] = $billingSubscriptionSuccessResponse['autoBilling'];
					}
				}else if(isset($getOrganizationBillingDetails['message'])){
					$billingResponse['message'] =
						($getOrganizationBillingDetails['message'] && count($getOrganizationBillingDetails['message']) > 0) ?
							$getOrganizationBillingDetails['message']['message'] :
								'Something went wrong while retrieving the billing subscription details.Please try after some time.';
				}else{
					$billingResponse['message'] = 'Something went wrong while retrieving the billing subscription details.Please contact system admin.';
				}
				return $billingResponse;
			}
		}else{
			$billingResponse['message'] = 'URL not found.';
			return $billingResponse;
		}
	}

	/** Function to get the billing subscription details for the organization code based on the employee admin access */
	public function getSignOffNotificationDetails($orgCode){
		$signOffResponse = array(
			'message' => '',
			'signOffDetails' => array()
		);

		$apiCustomDomainUrl = $this->_ehrTables->getApiCustomDomainUrlPath('billingRead');

		if(!empty($apiCustomDomainUrl)){
			$curl = curl_init();
			$method = "POST";

			$requestBody = '{ 
				"query": "query getSignOffNotificationDetails{ getSignOffNotificationDetails{ errorCode message signOffDetails{ signOffDueDate showSignOffAlert signOffNotificationDays signOffAlertType spocSignOffDocumentLink coordinatorSignOffDocumentLink } } }"
			}';
			//Set API headers
			
			curl_setopt($curl, CURLOPT_POST, 1);
			curl_setopt($curl, CURLOPT_POSTFIELDS, $requestBody);
			curl_setopt($curl, CURLOPT_URL, $apiCustomDomainUrl);
			$apiHeaders = $this->getApiHeaders($orgCode);
			curl_setopt($curl, CURLOPT_HTTPHEADER, $apiHeaders);
			curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
	
			// Call the endpoint
			$getSignOffNotificationDetails = curl_exec($curl);

			if(!$getSignOffNotificationDetails)
			{
				curl_close($curl);
				$signOffResponse['message'] = 'Sign off details does not retrieved.';
				return $signOffResponse;
			}else{
				curl_close($curl);
				$getSignOffNotificationDetails = json_decode($getSignOffNotificationDetails,true);
				if(isset($getSignOffNotificationDetails['errors'])){
					$signOffResponse['message'] =
					((count($getSignOffNotificationDetails['errors']) > 0) && $getSignOffNotificationDetails['errors'][0]['message']) ?
						$getSignOffNotificationDetails['errors'][0]['message'] :
							'Something went wrong while processing the request to retrieve the sign off details.';
				}else if(isset($getSignOffNotificationDetails['data'])){
					$signOffSuccessResponse = $getSignOffNotificationDetails['data']['getSignOffNotificationDetails'];
					if($signOffSuccessResponse['errorCode']){
						$signOffResponse['message'] =
							($signOffSuccessResponse['message']) ?
								$signOffSuccessResponse['message'] :
									'Something went wrong while retrieving the sign off details.';
					}else{
						$signOffResponse['signOffDetails'] =
							$signOffSuccessResponse['signOffDetails'] ?
								$signOffSuccessResponse['signOffDetails'] :
									array();
					}
				}else if(isset($getSignOffNotificationDetails['message'])){
					$signOffResponse['message'] =
						($getSignOffNotificationDetails['message'] && count($getSignOffNotificationDetails['message']) > 0) ?
							$getSignOffNotificationDetails['message']['message'] :
								'Something went wrong while retrieving the sign off details.Please try after some time.';
				}else{
					$signOffResponse['message'] = 'Something went wrong while retrieving the sign off details.Please contact system admin.';
				}
				return $signOffResponse;
			}
		}else{
			$signOffResponse['message'] = 'URL not found.';
			return $signOffResponse;
		}
	}

	// Based on service provider id this function form query to list employee details in the grid and combobox 
    public function formServiceProviderQuery($qryEmployee,$fieldName,$sessionId)
    {
		$serviceProviderId = $this->getEmployeeServiceProviderId($sessionId,0);
		if(!empty($serviceProviderId))
		{
			$qryEmployee->where($fieldName.'= ?',$serviceProviderId);							
		}
        return $qryEmployee; 
    }

	// get the employee service provider id based on session id
	public function getEmployeeServiceProviderId($sessionId,$isEmployee)
	{
		$serviceProviderId = '';
		if($this->_orgDetails['Field_Force']==1)
		{
			$serviceProviderAdmin = $this->_dbAccessRights->employeeAccessRights($sessionId, 'Service Provider Admin');

			/* when the logged in user is service provider admin or normal employee
			(when the Update Employee Detail Flag is enabled we are allowing the employees to update his record) in that situation 
			 we need to list his service provider alone that's the reason isEmployee Flag is used */
			if(!empty($serviceProviderAdmin['Employee']['Update']) || !empty($isEmployee))
			{
				$serviceProviderId  = $this->_db->fetchOne($this->_db->select()->from(array('EJ'=>$this->_ehrTables->empJob), array('Service_Provider_Id'))
											->where('EJ.Employee_Id = ?', $sessionId));
			}
		}
		return $serviceProviderId;
	}

	//Get the API headers
	public function getApiHeaders($orgCode=''){
		if(!$orgCode){
			$orgCode = $this->_ehrTables->getOrgCode();
		}
		
		$accessToken=isset($_COOKIE['accessToken']) ? $_COOKIE['accessToken'] : null;
		$refreshToken=isset($_COOKIE['refreshToken']) ? $_COOKIE['refreshToken'] : null; 
		$userIpAddress=isset($_COOKIE['userIpAddress']) ? $_COOKIE['userIpAddress'] : ''; 
		$partnerid=isset($_COOKIE['partnerid']) ? $_COOKIE['partnerid'] : '-';
		$dataRegion=isset($_COOKIE['d_code']) ? $_COOKIE['d_code'] : '-';
		$bucketRegion=isset($_COOKIE['b_code']) ? $_COOKIE['b_code'] : '-';
		$empUid=isset($_COOKIE['empUid']) ? $_COOKIE['empUid'] : '';

		$additionalHeaderDetails 				= new \stdClass();
		$additionalHeaderDetails->org_code 		= $orgCode;
		$additionalHeaderDetails->Authorization = $accessToken;
		$additionalHeaderDetails->refresh_token = $refreshToken;
		$additionalHeaderDetails->partnerid 	= $partnerid;
		$additionalHeaderDetails->d_code 		= $dataRegion;
		$additionalHeaderDetails->b_code 		= $bucketRegion;
		$additionalHeaderDetails->user_ip		= $userIpAddress;
		$additionalHeaderDetails->emp_uid		= $empUid;
		/* Convert the object to JSON */
		$additionalHeader = json_encode($additionalHeaderDetails);
		$headersArray = array(
			'content-type:application/json',
			'org_code:'.$orgCode,
			'Authorization:'.$accessToken,
			'refresh_token:'.$refreshToken,
			'partnerid:'.$partnerid,
			'd_code:'.$dataRegion,
			'b_code:'.$bucketRegion,
			'user_ip:'.$userIpAddress,
			'additional_headers:'.$additionalHeader);

		return $headersArray;
	}

	public function getVoluntaryProvidentFund($payslipId,$salaryType)
	{
		if($salaryType==="MON")
		{
			$voluntaryProvidentFund =  $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->salaryDeduction,array('Deduction_Amount'))
                                                            ->where('Payslip_Id = ?', $payslipId)
                                                            ->where('Deduction_Name = ?', 'Voluntary Provident Fund'));
		}
		else 
		{
			$voluntaryProvidentFund =  $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->wageDeduction,array('Deduction_Amount'))
                                                            ->where('Payslip_Id = ?', $payslipId)
                                                            ->where('Deduction_Name = ?', 'Voluntary Provident Fund'));
			
		}
		
		return $voluntaryProvidentFund;	
	}

	public function getEmployeeSalaryConfiguration($employeeId)
	{
		$employeeSalaryConfiguration = $this->_db->fetchRow($this->_db->select()->from(array('SC'=>$this->_ehrTables->employeeSalaryConfiguration), array('Eligible_For_PT','Eligible_For_Pension','Eligible_For_Vpf'))
																	->where('Employee_Id = ?', $employeeId));

		//when there is no record in employee salary configuration table we need to consider eligible for pension flag as 1 and eligible for pt as 1.
		if(empty($employeeSalaryConfiguration))
		{
			$employeeSalaryConfiguration['Eligible_For_Pension'] = 1;
			$employeeSalaryConfiguration['Eligible_For_PT']      = 1;
			$employeeSalaryConfiguration['Eligible_For_Vpf']     = 0;
		}

		return $employeeSalaryConfiguration;
	}

	/*this function convert the decimal value to time format*/
	public function convertTime($dec)
	{
		$h = intval($dec);
		$m = round((((($dec - $h) / 100.0) * 60.0) * 100), 0);
		if ($m == 60)
		{
			$h++;
			$m = 0;
		}
		$retval = sprintf("%02d:%02d", $h, $m);
		return $retval;
	}

	function hoursToDecimal($time, $decimals = 2)
	{
		$convertedValue = 0;
		if(!empty($time)){	
			$hms = explode(":", $time);
			if(count($hms) > 0) { // hours
			$convertedValue = $convertedValue  + $hms[0];
			}
			if(count($hms) > 1) { // minutes
				$convertedValue = $convertedValue  + $hms[1]/60;
			}
			if(count($hms) > 2) { // seconds
				$convertedValue = $convertedValue  + $hms[2]/3600;
			}
		}
		return number_format($convertedValue,$decimals,'.','');
	}

	// convert time(hh:mm:ss) to secondas
	function timeToSec($time) {
		$convertedValue = 0;
		if(!empty($time)){	
			$hms = explode(":", $time);
			if(count($hms) > 0) { // hours
			$convertedValue = $convertedValue  + $hms[0]*3600;
			}
			if(count($hms) > 1) { // minutes
				$convertedValue = $convertedValue  + $hms[1]*60;
			}
			if(count($hms) > 2) { // seconds
				$convertedValue = $convertedValue  + $hms[2];
			}
		}
		return $convertedValue;
	}

	public function convertMinutesToHoursMins($minutes) {
		$hours = floor($minutes / 60);
		$mins = $minutes % 60;
		return "{$hours} hr {$mins} mins";
	}

	/**
	 *	Check user defined employee id exists on blur in clone employee form
	*/
	public function checkUserDefinedEmployeeIdExist ($userDefinedEmpId,$employeeId=NULL)
	{
		$qryUserDefinedEmpIdExist = $this->_db->select()->from($this->_ehrTables->empJob, new Zend_Db_Expr('COUNT(Employee_Id)'))
									->where('User_Defined_EmpId = ?', $userDefinedEmpId);

		if (!empty($employeeId))
		{
			$qryUserDefinedEmpIdExist->where('Employee_Id != ?', $employeeId);
		}
		
		$userDefinedEmpIdExist = $this->_db->fetchOne($qryUserDefinedEmpIdExist);
		
		return $userDefinedEmpIdExist;
	}


	/**
	 *	Check biometric integration id exists 
	*/
	public function checkBiometricIntegrationIdExist ($biometricIntegrationId,$employeeId=NULL)
	{
		$biometricIntegrationIdExist = 0;
		if(!empty($biometricIntegrationId))
		{
			$qryBiometricIntegrationIdExist = $this->_db->select()->from($this->_ehrTables->empJob, new Zend_Db_Expr('COUNT(Employee_Id)'))
									->where('External_EmpId = ?', $biometricIntegrationId);

			if (!empty($employeeId))
			{
				$qryBiometricIntegrationIdExist->where('Employee_Id != ?', $employeeId);
			}
			
			$biometricIntegrationIdExist = $this->_db->fetchOne($qryBiometricIntegrationIdExist);
		}
		return $biometricIntegrationIdExist;
	}
	

	public function getAllowanceBenefitAssociation($allowanceId)
	{
		$allowancesBenefits = array();
		if(!empty($allowanceId))
		{
			$qryAllowancesBenefits = $this->_db->select()->from(array('A'=>$this->_ehrTables->allowances),array(''))
										->joinInner(array('AT'=>$this->_ehrTables->allowanceTypes),'AT.Allowance_Type_Id=A.Allowance_Type_Id',array(''))
										->joinInner(array('ABA'=>$this->_ehrTables->allowanceBenefitAssoc),'ABA.Allowance_Type_Id = AT.Allowance_Type_Id',array('BF.Form_Name'))
										->joinLeft(array('BF'=>$this->_ehrTables->benefitForms), 'BF.Form_Id = ABA.Form_Id', array(''))
										->where('A.Allowance_Status LIKE ?', 'Active')
										->where('AT.Allowance_Mode = ?', 'Non Bonus')
										->where('A.Allowance_Id = ?', $allowanceId);
			$allowancesBenefits = $this->_db->fetchCol($qryAllowancesBenefits);	
		}

		return $allowancesBenefits;
	}

	public function getAllowanceNameBasedOnBenefit($allowanceId,$benefitAssociation)
	{
		$allowanceName = '';
		if(!empty($allowanceId))
		{
			$qryAllowanceName = $this->_db->select()->from(array('A'=>$this->_ehrTables->allowances),array('AT.Allowance_Name'))
										->joinInner(array('AT'=>$this->_ehrTables->allowanceTypes),'AT.Allowance_Type_Id=A.Allowance_Type_Id',array(''))
										->where('AT.Allowance_Mode = ?', 'Non Bonus')
										->where('A.Allowance_Id IN (?)', $allowanceId);
			if(!empty($benefitAssociation))
			{
				$qryAllowanceName->joinInner(array('ABA'=>$this->_ehrTables->allowanceBenefitAssoc),'ABA.Allowance_Type_Id = AT.Allowance_Type_Id',array(''))
								->joinLeft(array('BF'=>$this->_ehrTables->benefitForms), 'BF.Form_Id = ABA.Form_Id', array(''))
								->where('BF.Form_Name = ?', $benefitAssociation);
			}							
			$allowanceName = $this->_db->fetchCol($qryAllowanceName);	
		}

		return $allowanceName;
	}

	public function getAdhocAllowanceNameBasedOnBenefit($adhocAllowanceId,$benefitAssociation)
	{
		$adhocAllowanceName = '';
		if(!empty($adhocAllowanceId))
		{
			$qryAdhocAllowanceName = $this->_db->select()->from(array('ADL'=>$this->_ehrTables->adhocAllowance),array('ADL.Title')) 
										->where('ADL.Adhoc_Allowance_Id IN (?)', $adhocAllowanceId);
			if(!empty($benefitAssociation))
			{
				$qryAdhocAllowanceName->joinInner(array('ADB'=>$this->_ehrTables->adhocAllowBenefitAssoc),'ADL.Adhoc_Allowance_Id = ADB.Adhoc_Allowance_Id',array())
								->joinLeft(array('BF'=>$this->_ehrTables->benefitForms), 'BF.Form_Id = ADB.Form_Id', array(''))
								->where('BF.Form_Name = ?', $benefitAssociation);
			}							
			$adhocAllowanceName = $this->_db->fetchCol($qryAdhocAllowanceName);	
		}
		return $adhocAllowanceName;
	}

	public function getPayslipIncentiveId($salaryType,$payslipId,$incentiveName)
	{
		$payslipIncentiveId = array();

		if($salaryType==='Monthly')
		{
			$taxableAllowanceTableName = $this->_ehrTables->salaryIncentive;

			$qryNonTaxableAllowance = $this->_db->select()->from($this->_ehrTables->nontaxEarnings, array('Description'))
																			->where('Incentive_Name = ?',$incentiveName)
																			->group('Description');
			if(!empty($payslipId))                        
			{
				$qryNonTaxableAllowance->where('Payslip_Id IN (?)',$payslipId);
			}

			$nonTaxableAllowance = $this->_db->fetchCol($qryNonTaxableAllowance);
		}
		else
		{
			$taxableAllowanceTableName = $this->_ehrTables->wageIncentive;

			$nonTaxableAllowance = array();
		}

    	$qryTaxableAllowance = $this->_db->select()->from($taxableAllowanceTableName, array('Description'))
															   ->where('Incentive_Name = ?',$incentiveName)
															   ->group('Description');
		if(!empty($payslipId))                        
		{
			$qryTaxableAllowance->where('Payslip_Id IN (?)',$payslipId);
		}

		$taxableAllowance = $this->_db->fetchCol($qryTaxableAllowance);

		if(!empty($taxableAllowance) || !empty($nonTaxableAllowance))
		{
			$payslipIncentiveId = array_unique(array_merge($taxableAllowance,$nonTaxableAllowance));
		}

		return $payslipIncentiveId;
	}

	// function to communicate mail while doing status approvals
	public function statusUpdateMailCommunication($mailDataArray) {
		$mailCommounicationResult = "";
		if(!empty($mailDataArray)) {
			$sessionId 			= $mailDataArray["sessionId"];
			$addedBy 			= $mailDataArray["addedBy"];
			$employeeId 		= $mailDataArray["employeeId"];
			$customFormName 	= $mailDataArray["customFormName"];
			$status 			= $mailDataArray["status"];
			$employeeName 		= $mailDataArray["employeeName"];
			$formName 			= $mailDataArray["formName"];
			$actionName 		= $mailDataArray["actionName"];
			$approverEmpId 		= $mailDataArray["approverEmpId"];
			$moduleName     	= $mailDataArray["moduleName"];
			$formUrl			= $mailDataArray["formUrl"];
			$leaveAction		= $mailDataArray['leaveAction'];
			$mailContent    	= "";
			$empIdToBeMailed 	= "";
			$inboxTitle      	= $customFormName.' Notification';

			/** For returned, rejected, cancel approved, cancel rejected request mail will be sent here */
			if ($sessionId != $addedBy && $status !='Cancel Applied' && ($formName != 'Leaves' || $leaveAction != 'CancelAppliedLeave')) {
				if ($addedBy == $employeeId) {
					$mailContent = "<p>Your ".$customFormName." request has been ".strtolower($status).".</p>";
				}
				else {
					$mailContent = "<p>Your ".$customFormName." request for the employee ". $employeeName ." has been ".strtolower($status).".</p>";
				}
				$empIdToBeMailed = $addedBy;
			}
			
			/** If the status is 'Cancel Applied' */
			if($status === 'Cancel Applied')
			{
				/** If the compoff record approver does not made the request to cancel the compoff then send the email to the approver */
				if ($approverEmpId != $sessionId){
					$mailContent = "<p>".$customFormName." cancellation request for the employee ".$employeeName." is waiting for your approval.</p>";
					$empIdToBeMailed = $approverEmpId;
				}
	
				/** If the compoff record employee does not made a cancel request then send an email to the
				*  compoff record employee when a cancellation request is made by other employees */
				if($sessionId != $employeeId){	
					$mailContent = "<p>Cancellation is applied for your ".$customFormName." request.</p>";
					$empIdToBeMailed = $employeeId;
				}
	
				/** If the compoff record employee does not added this compoff record and if the compoff record-added by employee does
				* not made this cancel request then we can send an email to the added by employee. 
				* Otherwise we should not sent an email as the compoff record-added by employee made this cancel compoff request or 
				* the compoff record-added by employee id and the compoff record-employee id are same */
				if(!in_array($addedBy, array($employeeId, $sessionId))){
					$mailContent = "<p>Cancellation is applied for the employee ".$employeeName."</p>";
					$empIdToBeMailed = $addedBy;
				}
			}
	
			/** If the employee cancel the applied status compoff then 'updated by' and 'updated on' can be updated. */
			if(($formName != 'Leaves' || $leaveAction === 'CancelAppliedLeave') && $status === 'Cancelled')
			{
				$sessionEmployeeName = $this->_dbPersonal->employeeName($sessionId);
				$sessionEmployeeName = $sessionEmployeeName['Employee_Name'];
	
				/** If the compoff record approver does not made the request to cancel the compoff then send the email to the approver */
				if ($approverEmpId != $sessionId){
					if ((int)$employeeId === (int)$sessionId){
						$mailContent = "<p>".$sessionEmployeeName." cancel the ".$customFormName.". No action required.</p>";
					}else{
						$mailContent = "<p>".$sessionEmployeeName." cancel the ".$customFormName." for the employee ".$employeeName.". No action required.</p>";
					}
					$empIdToBeMailed = $approverEmpId;
					$inboxTitle = $customFormName.' Notification for '.$employeeName;
				}
	
				/** If the compoff record employee does not made a cancel request then send an email to the
				*  compoff record employee when a cancellation request is made by other employees */
				if($sessionId != $employeeId){
					$mailContent = "<p>".$sessionEmployeeName." cancel your ".$customFormName." request.</p>";
					$empIdToBeMailed = $employeeId;
				}
	
				/** If the compoff record employee does not added this compoff record and if the compoff record-added by employee does
				* not made this cancel request then we can send an email to the added by employee. 
				* Otherwise we should not sent an email as the compoff record-added by employee made this cancel compoff request or 
				* the compoff record-added by employee id and the compoff record-employee id are same */
				if(!in_array($addedBy, array($employeeId, $sessionId))){
					if ((int)$employeeId === (int)$sessionId){
						$mailContent =  "<p>".$sessionEmployeeName." cancel the ".$customFormName." request.</p>";
					}else{
						$mailContent =  "<p>".$sessionEmployeeName." cancel the ".$customFormName." request for the employee ".$employeeName.".</p>";
					}
					$empIdToBeMailed = $addedBy;
				}
			}
	
			if ($status == 'Approved' && $sessionId != $employeeId && $employeeId != $addedBy)
			{
				$mailContent = "<p>Your ".$customFormName." request has been ".strtolower($status)."</p>";
				$empIdToBeMailed = $employeeId;
			}

			if(!empty($mailContent)){
				// call communicateMail function to send mail notifications
				$mailCommounicationResult = $this->communicateMail(
					array(
						'employeeId'  		=> $empIdToBeMailed,
						'ModuleName'  		=> $moduleName,
						'formName'    		=> $formName,
						'successMsg'  		=> $customFormName,
						'customFormName' 	=> $customFormName,
						'formUrl'     		=> $formUrl,
						'inboxTitle'  		=> $inboxTitle,
						'mailContent' 		=> $mailContent,
						'action'      		=> $actionName
					)
				);
			}else{
				$responseAction = $actionName;
				return array('success' => true, 'msg' => $customFormName.' '. $responseAction .' successfully', 'type' => 'success');
			}
		}
		return $mailCommounicationResult;
	}

	public function addUpdateActionMailCommunication($mailDataArray) {
		$mailCommounicationResult = "";
		if(!empty($mailDataArray)) {
			$sessionId 			= $mailDataArray["sessionId"];
			$employeeId 		= $mailDataArray["employeeId"];
			$customFormName 	= $mailDataArray["customFormName"];
			$moduleName     	= $mailDataArray["moduleName"];
			$formName			= $mailDataArray["formName"];
			$formUrl			= $mailDataArray["formUrl"];
			$action				= $mailDataArray['action'];
			$empIdToBeMailed 	= $mailDataArray['empIdToBeMailed'];
			$alternatePerson 	= $mailDataArray['alternatePerson'];
			$record 			= $mailDataArray['record'];
			$mailContent    	= "";
			$employeeName        = $this->_dbPersonal->employeeName($employeeId);
			$sessionEmployeeName = $this->_dbPersonal->employeeName($sessionId);
			// add action mail notification
			if ($action === 'add') {
				if ($employeeId == $sessionId) {
					$mailContent = "<p>".$customFormName." Notification forwarded from ". $employeeName['Employee_Name'] ." is waiting for your approval.</p>";
				} else{
					$mailContent = "<p>".$customFormName." Notification forwarded from ". $sessionEmployeeName['Employee_Name'] ." for the employee ".
								$employeeName['Employee_Name'] ." is waiting for your approval.</p>";
				}
				if (!empty($alternatePerson) && $alternatePerson != $employeeId && $alternatePerson != $sessionId) { 
					$mailCommounicationResult = $this->sendMailToAlternatePerson($mailDataArray, $employeeName['Employee_Name']);
				}
			} else {
				if(!empty($record)) {
					if ( $empIdToBeMailed != $record['Approver_Id'] || $record['Approval_Status'] == "Approved" ) {
						if ($record['Approval_Status'] != "Approved") {
							if ($employeeId == $sessionId) {
								$mailContent = "<p>".$customFormName." Notification forwarded from ". $employeeName['Employee_Name'] ." is waiting for your approval.</p>";
							} else {
								$mailContent = "<p>".$customFormName." Notification forwarded from ". $sessionEmployeeName['Employee_Name'] ." for the employee ".
											$employeeName['Employee_Name'] ." is waiting for your approval.</p>";
							}
						} else {
							if ($employeeId == $sessionId) {
								$mailContent = "<p>".$customFormName." Notification forwarded from ". $employeeName['Employee_Name'] ." is modified and waiting for your approval.</p>";
							} else {
								$mailContent = "<p>".$customFormName." Notification forwarded from ". $sessionEmployeeName['Employee_Name'] ." for the employee ".
											$employeeName['Employee_Name'] ." is modified and waiting for your approval.</p>";
							}
						}
					}
					if (!empty($alternatePerson) && $alternatePerson != $employeeId && $alternatePerson != $sessionId) { 
						$mailCommounicationResult = $this->sendMailToAlternatePerson($mailDataArray, $employeeName['Employee_Name']);
					}
				}
			}
			// call this function to trigger notification when the compoff mail notification is enabled
			if(!empty($mailContent)){
				$mailCommounicationResult = $this->communicateMail(
					array(
						'employeeId'     => $empIdToBeMailed,
						'ModuleName'     => $moduleName,
						'formName'       => $formName,
						'successMsg'     => $customFormName,
						'customFormName' => $customFormName,
						'formUrl'        => $formUrl,
						'inboxTitle'     => $customFormName.' Notification for '.$employeeName['Employee_Name'],
						'inboxCondition' => $empIdToBeMailed != $sessionId && $empIdToBeMailed != $employeeId,
						'mailContent'    => $mailContent,
						'action'         => $action == 'update' ? 'updated' : 'added'
					)
				);
			}else{
				$responseAction = ($action == 'update') ? 'updated' : 'added';
				return array('success' => true, 'msg' => $customFormName.' '. $responseAction .' successfully', 'type' => 'success');
			}
		}
		return $mailCommounicationResult;
	}

	// send email notifications to alternate person
	public function sendMailToAlternatePerson($mailDataArray, $employeeName) {
		$mailCommounicationResult = "";
		if(!empty($mailDataArray)) {
			$customFormName 	= $mailDataArray["customFormName"];
			$moduleName     	= $mailDataArray["moduleName"];
			$formName     		= $mailDataArray["formName"];
			$formUrl			= $mailDataArray["formUrl"];
			$action				= $mailDataArray['action'];
			$alternatePerson 	= $mailDataArray['alternatePerson'];
			$leaveFrom			= $mailDataArray['leaveFrom'];
			$leaveTo			= $mailDataArray['leaveTo'];		
			$mailContent 		=
				"<p>". $employeeName ."  who is on leave has assigned you as backup from ". $leaveFrom ." to ". $leaveTo .".</p>";

			$mailCommounicationResult = $this->communicateMail(
				array(
					'employeeId'     => $alternatePerson,
					'ModuleName'     => $moduleName,
					'formName'       => $formName,
					'successMsg'     => $customFormName,
					'customFormName' => $customFormName,
					'formUrl'        => $formUrl,
					'inboxTitle'     => $customFormName.' Notification from '. $employeeName,
					'mailContent'    => $mailContent,
					'action'         => $action == 'update' ? 'updated' : 'added'
				)
			);
		}
		return $mailCommounicationResult;
	}

	// check whether attendance import data is already exist or not.
	public function attendanceImportAlreadyExist($importData)
	{
		$attendanceImportExist = 0;
		if(isset($importData['Employee_Id']) && isset($importData['Added_On']) && isset($importData['Attendance_Status']))
		{
			$attendanceConfiguration = $this->getAttendanceGeneralConfiguration();
			if($attendanceConfiguration['Restrict_Multiple_Biometric_Swipe']=='Yes')
			{
				$timeLimit = $attendanceConfiguration['Allowed_Time_Interval_for_Multiple_Swipes'];
			}
			else
			{
				$timeLimit = 0;
			}
			$minTime   = date('Y-m-d H:i:s',strtotime('-'.$timeLimit.'minutes',strtotime($importData['Added_On'])));									
			$maxTime   = date('Y-m-d H:i:s',strtotime('+'.$timeLimit.'minutes',strtotime($importData['Added_On'])));	
			$attendanceImportExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendanceImport, array(new Zend_Db_Expr('count(Attendance_Id)')))
																										->where('Employee_Id = ?', $importData['Employee_Id'])
																										->where('Attendance_Status = ?',$importData['Attendance_Status'])
																										->where('Added_On >= ?', $minTime)
																										->where('Added_On <= ?', $maxTime));
		}
		return $attendanceImportExist;																							
	}

	public function getAttendanceGeneralConfiguration(){
		return ($this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->attendanceGeneralConfiguration, 
				array('Attendance_Regularization_Cut_Off_Days_For_Employee','Attendance_Regularization_Request_Limit_For_Employee',
				'Attendance_Approval_Cut_Off_Days_For_Manager','Late_Attendance_Email_Notification','Attendance_Regularization_After',
				'Restrict_Multiple_Biometric_Swipe','Allowed_Time_Interval_for_Multiple_Swipes'))));

	}

	// list blood group details in combo box
	public function getBloodGroup()
	{
		$bloodGroup = array('O+'=>'O+',
							'O-'=>'O-',
							'A+'=>'A+',
							'A-'=>'A-',
							'B+'=>'B+',
							'B-'=>'B-',
							'AB+'=>'AB+',
							'AB-'=>'AB-',
							'A1+'=>'A1+',
							'A1-'=>'A1-',
							'A1B+'=>'A1B+',
							'A1B-'=>'A1B-',
							'Unknown'=>'Unknown');
							
		return $bloodGroup;
	}

	public function getDateOfJoin($employeeId){
		return($this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob,'Date_Of_Join')
                                                ->where('Employee_Id = ?', $employeeId)));
	}

	public function getAllowanceName($allowanceMode)
	{
		$qryAllowanceName = $this->_db->select()->from(array('A'=>$this->_ehrTables->allowances),array('AT.Allowance_Name'))
										->joinInner(array('AT'=>$this->_ehrTables->allowanceTypes),'AT.Allowance_Type_Id=A.Allowance_Type_Id',array(''))
										->where('AT.Allowance_Mode = ?',$allowanceMode);

		$allowanceName = $this->_db->fetchCol($qryAllowanceName);

		$getIncentiveDetails 	= array();
		if(!empty($allowanceName))
		{
			$sortedIncentiveDetails = array_unique($allowanceName, SORT_REGULAR);
			foreach($sortedIncentiveDetails as $incentiveHeader)
			{
				$getIncentiveDetails[] = str_replace(" ","_",$incentiveHeader);
			}
		}
		return $getIncentiveDetails;
	}

	public function getFixedHelathInsuranceName($payslipId=NULL)
	{
		$qryInsuranceName = $this->_db->select()->from(array('FHT'=>$this->_ehrTables->fixedHealthInsType),array('FHT.Title'));

		if(!empty($payslipId))                        
		{
			$qryInsuranceName->joinInner(array('FH' => $this->_ehrTables->fixedHealthIns),'FHT.Insurance_Type_Id=FH.Insurance_Type_Id',array(''))
							 ->joinInner(array('SD'=>$this->_ehrTables->salaryDeduction),'SD.Description = FHT.Insurance_Type_Id', array(''))
							 ->group('FHT.Insurance_Type_Id')
							 ->where('SD.Deduction_Name = "Fixed Health Insurance"')
							 ->where('SD.Payslip_Id IN (?)',$payslipId);
		}

		$fixedHealthInsuranceName = $this->_db->fetchCol($qryInsuranceName);								

		$getFixedHealthInsuranceDetails 	= array();
		if(!empty($fixedHealthInsuranceName))
		{
			$sortedInsuranceDetails = array_unique($fixedHealthInsuranceName, SORT_REGULAR);
			foreach($sortedInsuranceDetails as $insuranceHeader)
			{
				$getFixedHealthInsuranceDetails[] = str_replace(" ","_",$insuranceHeader);
			}
		}
		return $getFixedHealthInsuranceDetails;								
	}

	public function getVariableInsuranceName($payslipId=NULL)
    {
        $qryInsuranceName = $this->_db->select()->from(array('IT'=>$this->_ehrTables->insuranceType), array('IT.Insurance_Name'))
										->order('IT.Insurance_Name ASC');
		
		if(!empty($payslipId))                        
		{
			$qryInsuranceName->joinInner(array('ITG'=>$this->_ehrTables->insurancetypeGrade),'ITG.InsuranceType_Id = IT.InsuranceType_Id', array(''))
							->joinInner(array('IG'=>$this->_ehrTables->insuranceGrade),'IG.Insurance_Grade_Id = ITG.Insurance_Grade_Id', array(''))
							->joinInner(array('VI'=>$this->_ehrTables->variableInsurance),'VI.InsuranceType_Id = IT.InsuranceType_Id', array(''))
							->joinInner(array('SD'=>$this->_ehrTables->salaryDeduction),'SD.Description = IT.InsuranceType_Id', array(''))
							->group('VI.InsuranceType_Id')
							->where('SD.Deduction_Name = "Insurance"')
							->where('SD.Payslip_Id IN (?)',$payslipId);
		}

		$insuranceName = $this->_db->fetchCol($qryInsuranceName);														
		$getInsuranceDetails 	= array();
		if(!empty($insuranceName))
		{
			$sortedInsuranceDetails = array_unique($insuranceName, SORT_REGULAR);
			foreach($sortedInsuranceDetails as $insuranceHeader)
			{
				$getInsuranceDetails[] = str_replace(" ","_",$insuranceHeader);
			}
		}
		return $getInsuranceDetails;
	}


	public function getFixedInsuranceName($payslipId=NULL)
    {
        $qryInsuranceName = $this->_db->select()->from(array('IT'=>$this->_ehrTables->insuranceType), array('IT.Insurance_Name'))
										->order('Insurance_Name ASC');
		
		if(!empty($payslipId))                        
		{
			$qryInsuranceName->joinInner(array('ITG'=>$this->_ehrTables->insurancetypeGrade),'ITG.InsuranceType_Id = IT.InsuranceType_Id', array(''))
							->joinInner(array('IG'=>$this->_ehrTables->insuranceGrade),'IG.Insurance_Grade_Id = ITG.Insurance_Grade_Id', array(''))
							->joinInner(array('FI'=>$this->_ehrTables->fixedInsurance),'FI.InsuranceType_Id = IT.InsuranceType_Id', array(''))
							->joinInner(array('SD'=>$this->_ehrTables->salaryDeduction),'SD.Description = IT.InsuranceType_Id', array(''))
							->where('ITG.Emp_SharePercent = 0')
							->where('ITG.Org_SharePercent = 0')
							->group('FI.InsuranceType_Id')
							->where('SD.Deduction_Name = "Insurance"')
							->where('SD.Payslip_Id IN (?)',$payslipId);
		}
		
		$insuranceName = $this->_db->fetchCol($qryInsuranceName);
		$getInsuranceDetails 	= array();
		if(!empty($insuranceName))
		{
			$sortedInsuranceDetails = array_unique($insuranceName, SORT_REGULAR);
			foreach($sortedInsuranceDetails as $insuranceHeader)
			{
				$getInsuranceDetails[] = str_replace(" ","_",$insuranceHeader);
			}
		}
		return $getInsuranceDetails;
	}


	public function payslipExist($employeeId,$salaryMonth)
	{
		$payslipExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->monthlyPayslip, array('Payslip_Id'))            
														->where('Employee_Id = ?', $employeeId)							
														->where('Salary_Month = ?', $salaryMonth));

		if(empty($payslipExist))
		{
			$payslipExist = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->wagePayslip, array('Payslip_Id'))            
													->where('Employee_Id = ?', $employeeId)							
													->where('Salary_Month = ?', $salaryMonth));
		}

		return $payslipExist;
	}

	public function getWorkflowsBasedOnForm($formId)
    {
		$workflows = $this->_db->fetchAll($this->_db->select()
									->from(array('WF'=>$this->_ehrTables->workflows),
										   array('WF.Workflow_Id','Workflow_Name'=> 'WF.Workflow_Name'))
									->joinInner(array('WFM'=>$this->_ehrTables->workflowModule), 'WF.Workflow_Module_Id=WFM.Workflow_Module_Id', array())
									->where('WFM.Form_Id = ?', $formId));
		return $workflows;
    }

	public function getDefaultWorkflowBasedOnForm($formId)
    {
		$defaultWorkflow = $this->_db->fetchOne($this->_db->select()
									->from(array('WF'=>$this->_ehrTables->workflows),
										   array('WF.Workflow_Id'))
									->joinInner(array('WFM'=>$this->_ehrTables->workflowModule), 'WF.Workflow_Module_Id=WFM.Workflow_Module_Id', array())
									->where('WF.Default_Workflow = 1')
									->where('WFM.Form_Id = ?', $formId));
		return $defaultWorkflow;
    }

	public function getDateRangeBasedOnDuration($startDate,$duration)
    {
		$this->_dbPayslip 	= new Payroll_Model_DbTable_Payslip();
		$punchInMonth 		= date("m", strtotime($startDate));// get month from the punch-in date
		$punchInYear  		= date("Y", strtotime($startDate));// get year from the punch-in date
		$paycyledate 		= $this->_dbPayslip->getSalaryDateRange($punchInMonth,$punchInYear,strtotime($startDate));
		$currentYear 		= date('Y',strtotime($paycyledate['Salary_Date']));
		$dateRangeDetails   = $this->getDateRanges($currentYear,$duration);

		if(!empty($dateRangeDetails))
		{
			foreach($dateRangeDetails as $dateRange)
			{
				$fromDate 	= $dateRange['Start_Date'];
				$toDate 	= $dateRange['End_Date'];
				if(strtotime($startDate) >= strtotime($fromDate) && strtotime($startDate) <= strtotime($toDate))
				{
					return array('From_Date'=>$fromDate,'To_Date'=>$toDate);
				}
			}
		}
	}

	function getDateRanges($currentYear,$interval) {
		$result 	 = array();
		$fromDate	 = date('Y-m-d',strtotime($currentYear.'-01-01'));
		switch ($interval) {
			case "Per Quarter":
				for ($quarter = 1; $quarter <= 4; $quarter++) {
					$startDate  = $fromDate;
					$fromDate	= date('Y-m-d', strtotime('+3 month', strtotime($startDate)));
					$endDate	= date('Y-m-d', strtotime('-1 day', strtotime($fromDate)));
					array_push($result, array("Start_Date" => $startDate, "End_Date" => $endDate));
				}
				break;
				
			case "Per Half Year":
				for ($half = 1; $half <= 2; $half++) {
					$startDate  = $fromDate;
					$fromDate	= date('Y-m-d', strtotime('+6 month', strtotime($startDate)));
					$endDate	= date('Y-m-d', strtotime('-1 day', strtotime($fromDate)));
					array_push($result, array("Start_Date" => $startDate, "End_Date" => $endDate));
				}
				break;
	
			case "Per Year":
				$startDate  = $fromDate;
				$fromDate	= date('Y-m-d', strtotime('+12 month', strtotime($startDate)));
				$endDate	= date('Y-m-d', strtotime('-1 day', strtotime($fromDate)));
				array_push($result,array("Start_Date" => $startDate, "End_Date" => $endDate));
				break;
	
			default:
				return "Invalid interval. Supported intervals: per quarter, per half-year, per year, per month.";
				break;
		}
		return $result;
	}
	
	
	
	// check the login employee is any one of admin or payroll admin or employee admin or service provider admin
	public function checkAnyOneOfAdmin($loginEmpId)
	{
		$isOneOfAnyAdmin = false;
		$adminForms = ["Admin", "Payroll Admin", "Employee Admin", "Service Provider Admin","Productivity Monitoring Admin"];
		for($formCount = 0;$formCount <count($adminForms);$formCount++)
		{
			$adminFormAccess = $this->_dbAccessRights->employeeAccessRights($loginEmpId, $adminForms[$formCount]);
			if (!empty($adminFormAccess['Employee']['Update'])) {
				$isOneOfAnyAdmin = true;
				break;
			}
		} 
		return $isOneOfAnyAdmin;
	}

	//Function to check location, department, employee type exists in custom group or not
	public function keyExistInCustomGroup($key,$value){
		$exists = 0;
		$getCGRules = $this->_db->fetchAll($this->_db->select()
									->from(array('CEG'=>$this->_ehrTables->customEmployeeGroup),
										   array('Filter_Vue')));
		if(!empty($getCGRules)){
			foreach($getCGRules as $rule)
			{ 
				if(!empty($rule)){
					$childrenNode = json_decode(json_encode($rule['Filter_Vue']), true);
					if (count($childrenNode) > 0){
						$childrenNode = json_decode($childrenNode,true);
						$childrenNode = $childrenNode['children'];
						if ($childrenNode[0]['type'] === 'rule') {
							if( isset($childrenNode[0]['query']['operand']) && isset($childrenNode[0]['query']['value'][0])
								&& $childrenNode[0]['query']['operand'] == $key && $childrenNode[0]['query']['value'][0] == $value){
								$exists+=1;
							}
						}
					}
				}
			}	
		}
		return $exists;
	}

	public function getBondRecoveryEmpDetails($employeeId){
        return $this->_db->fetchAll($this->_db->select()->from(array('S'=>$this->_ehrTables->employeeSalaryConfiguration),
                            array('J.Employee_Id','J.Date_Of_Join','S.Bond_Recovery_Applicable','S.Minimum_Months_To_Be_Served','S.Bond_Value'))
							->joinInner(array('J'=>$this->_ehrTables->empJob), 'J.Employee_Id=S.Employee_Id',array(''))
                            ->where('S.Employee_Id IN (?)',$employeeId));
    }

    public function getNoticePayEmpDetails($employeeId){
        return $this->_db->fetchAll($this->_db->select()->from(array('D'=>$this->_ehrTables->designation),
                            array('J.Employee_Id','D.Notice_Period_Days_Within_Probation','D.Notice_Period_Days_After_Probation','D.Notice_Period_Pay_By_Employer','Notice_Period_Pay_By_Employee','J.Reason_Id','J.Confirmed'))
                            ->joinLeft(array('J'=>$this->_ehrTables->empJob), 'J.Designation_Id=D.Designation_Id',array('J.Confirmation_Date'))
                            ->where('Employee_Id IN (?)',$employeeId)
                            ->group('Employee_Id'));
    }

	public function getNoticePayBasicPlusAllowance($empSalaryDetails,$basicPay,$salaryMonthYear,$workingDays,$midJoin){
		$basicPayPerDay = 0;
		$dbPayslip 	= new Payroll_Model_DbTable_Payslip();
		$noticePayAllowance = $dbPayslip->allowances($empSalaryDetails['Employee_Id'], $empSalaryDetails['Grade_Id'], $empSalaryDetails['Location_Id'], 0, $basicPay, $salaryMonthYear,'m', 'Notice Pay',$midJoin);

		if(!empty($noticePayAllowance))
		{
			$noticePayBasicPlusAllowance = $basicPay+$noticePayAllowance[0]+$noticePayAllowance[1];
		}
		else 
		{
			$noticePayBasicPlusAllowance = $basicPay;
		}
		
		if(($noticePayBasicPlusAllowance > 0) && ($workingDays > 0))
		{
			$basicPayPerDay = $noticePayBasicPlusAllowance/$workingDays; //basic pay divided by no of working days
		}

		return $basicPayPerDay;
	}
    public function calculateEmployeeNoticePay($employeeId,$designationDetails,$resignationDate,$noticeDate,$salaryMonthYear,$empSalaryDetails,$basicPay,$workingDays,$midJoin){
        $employeeHoldsNoticePay = $noticePayAmount = $noticePayRemainingDays = $basicPayPerDay = $empTotalNoticePeriod=0;
		$noticePayMode = '';
        
        if(!empty($designationDetails)){
			$resignationReasonId=$designationDetails['Reason_Id'];
			if($designationDetails['Confirmed']==1 && !empty($designationDetails['Confirmation_Date'])){
				$noticePeriodDays = $designationDetails['Notice_Period_Days_After_Probation'];
			}else{
				$noticePeriodDays = $designationDetails['Notice_Period_Days_Within_Probation'];
			}
			
			$strStart=date_create($resignationDate);
			$strEnd=date_create($noticeDate);
			$numOfDays=date_diff($strStart,$strEnd);
			$numOfDays = $numOfDays->format("%a")+1;
			
			if($noticePeriodDays > $numOfDays){
				if($designationDetails['Notice_Period_Pay_By_Employee'] === 'Yes'){
					if(!empty($empSalaryDetails)){
						$basicPayPerDay = $this->getNoticePayBasicPlusAllowance($empSalaryDetails,$basicPay,$salaryMonthYear,$workingDays,$midJoin);
					}else{
						$basicPayPerDay = 0;
					}
					if($basicPayPerDay > 0){
						$employeeHoldsNoticePay = 1;
						$noticePayMode = 'Deductions';
						$noticePayRemainingDays = $noticePeriodDays - $numOfDays;
						$noticePayAmount = $noticePayRemainingDays * $basicPayPerDay;
						$noticePayAmount = number_format(round($noticePayAmount,2),2,'.','');
					}
				}
			}else if($noticePeriodDays < $numOfDays){
				if($designationDetails['Notice_Period_Pay_By_Employer'] === 'Yes'){
					if(!empty($empSalaryDetails)){
						$basicPayPerDay = $this->getNoticePayBasicPlusAllowance($empSalaryDetails,$basicPay,$salaryMonthYear,$workingDays,$midJoin);
					}else{
						$basicPayPerDay = 0;
					}
					if($basicPayPerDay > 0){
						$employeeHoldsNoticePay = 1;
						$noticePayMode = 'Earnings';
						$noticePayRemainingDays = $numOfDays-$noticePeriodDays;
						$noticePayAmount = $noticePayRemainingDays * $basicPayPerDay;
						$noticePayAmount = number_format(round($noticePayAmount,2),2,'.','');
					}
				}
			}
        }
        return array(
            'employeeHoldsNoticePay' => $employeeHoldsNoticePay,
			'noticePayMode' => $noticePayMode,
            'noticePayRemainingDays' => $noticePayRemainingDays,
            'noticePayPerDayAmount' => $basicPayPerDay,
            'noticePayAmount' => $noticePayAmount,
			'empTotalNoticePeriod' => $noticePeriodDays
        );
    }

    public function calcEmpBondRecovery($sEmpId,$empBondDetails,$resignationDate){
        $bondRecoveryApplicable = $bondRecoveryAmount = $bondRecoveryRemainingDays = $bondValue = $bondValuePerDay = $minimumMonthsToBeServed = 0;
        $dateOfJoin = '';
        if(!empty($empBondDetails)){
            if($empBondDetails['Bond_Recovery_Applicable'] === 'Yes'){
                $dateOfJoin = $empBondDetails['Date_Of_Join'];
                $minimumMonthsToBeServed = $empBondDetails['Minimum_Months_To_Be_Served'];
                $bondValue = $empBondDetails['Bond_Value'];

                $strStart = new DateTime($resignationDate);
                $strEnd = new DateTime($dateOfJoin);
				$numOfDays=date_diff($strStart,$strEnd);
				$numOfDays = $numOfDays->format("%a")+1;
				//Convert month to days
				$minDaysToBeServed = $minimumMonthsToBeServed * 30.417;
                if($minDaysToBeServed > $numOfDays){
                    $bondRecoveryApplicable = 1;
					$bondValuePerDay = number_format(round($bondValue/$minDaysToBeServed,2),2,'.','');
                    $bondRecoveryRemainingDays = round($minDaysToBeServed - $numOfDays);
                    $bondRecoveryAmount = number_format(round($bondRecoveryRemainingDays * $bondValuePerDay,2),2,'.','');
                }
            }
        }
        
        return array(
            'bondRecoveryApplicable' => $bondRecoveryApplicable,
			'dateOfJoin' => $dateOfJoin,
			'totalBondValue' => $bondValue,
			'minimumMonthsToBeServed' => $minimumMonthsToBeServed,
            'bondRecoveryRemainingDays' => $bondRecoveryRemainingDays,
            'bondValue' => $bondValuePerDay,
            'bondRecoveryAmount' => $bondRecoveryAmount
        );
    }

	//Get the employee settlement status
    public function empFinalSettlementInitiated($employeeId){
		$isSettlementInitiated=0;
        $settlementQuery = $this->_db->select()->from($this->_ehrTables->employeeFullAndFinalSettlement, array('Settlement_Status'))
                            ->where('Employee_Id = ?', $employeeId);
        $settlementStatus = $this->_db->fetchOne($settlementQuery);
		if(!empty($settlementStatus)){
			$isSettlementInitiated = 1;
		}
        return $isSettlementInitiated;
    }

	public function getAllowanceType()
    {
        return $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->allowanceTypes,array(new Zend_Db_Expr("REPLACE(Allowance_Name, ' ', '_') AS Allowance_Name")))
												->order('Allowance_Name ASC')
												->where('AllowanceType_Status = "Active"'));
    }

	public function getArrearAllowanceType()
    {
        $allowanceTypes = $this->getAllowanceType();
		$arrearAllowanceType = array();
		foreach($allowanceTypes as $allowanceType)
		{
			$allowanceType = "Arrear_".$allowanceType;
			$arrearAllowanceType[] = $allowanceType;
		}
		return $arrearAllowanceType;
    }

	public function getAdhocAllowanceTitle()
    {
        return $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->adhocAllowance,array(new Zend_Db_Expr("REPLACE(Title, ' ', '_') AS Title")))
												->order('Title ASC'));
    }

	public function getDeductionTitle()
    {
        return $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->deductions,array(new Zend_Db_Expr("REPLACE(Deduction_Title, ' ', '_') AS Deduction_Title")))
												->order('Deduction_Title ASC'));
    }

	public function getPayslipEmployeeDetails($payrollMonth,$payslipYear)
	{
		$payslipEmployeeDetails = array();
		if(!empty($payrollMonth) && !empty($payslipYear))
        {
            $dbPayslip      = new Payroll_Model_DbTable_Payslip();
            $dbSalary       = new Payroll_Model_DbTable_Salary();
            $dbPrerequisite = new Payroll_Model_DbTable_Prerequisite();
            $payslipMonth   = $payrollMonth.','.$payslipYear;
            $month          = explode(',', $payslipMonth);
            $paycyleDate    = $dbPayslip->getSalaryDay($month[0], $month[1]);
            $empLocation    = $empdept = $empType = array();
            #If the Consider cutoff day for attendance and timeoff is enabled, then attendance and timeoff should be considered based on the cutoff day
            $paycyleDateWithCutoff =  $dbPayslip->getSalaryDay($month[0], $month[1], 31);
            /** To get employees based on the choosed fields in salary payslip form **/ 
            $arrayEmployee = $dbSalary->monthlySalaryEmployee(array(),NULL,NULL,NULL,$paycyleDate['Salary_Date'],$paycyleDate['Last_SalaryDate']);
            if(!empty($arrayEmployee))
            {
                $payslipEmployeeDetails = $dbPrerequisite->getPayslipEmployeeDetails($payslipMonth,$arrayEmployee);        
            }
        }
		return $payslipEmployeeDetails;
	}

	public function exportCsv($reportDetails,$payslipMonth,$reportName,$locale)
	{
		$reportHeader 	= array();
		list($month,$year) = explode(',', $payslipMonth);
		$date = new DateTime("$year-$month-01");
		$salaryMonthFrom = $date->format('F Y');
		$reportTitle 	 = strtoupper($reportName.' FOR THE MONTH OF '.$salaryMonthFrom);

		$titleStyle = array(
			'font' => array(
				'bold' => true
			),
			'borders' => array(
				'allBorders' => array(
					'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
					'color' => array('argb' => '00000000'),
				),
			),
		);

		$dataBorder = array(
			'borders' => array(
				'allBorders' => array(
					'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
					'color' => array('argb' => '00000000'),
				),
			)
		);

		$headerStyle = array(
			'font' => array(
				'bold' => true
			),
			'alignment' => array(
				'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,	
			),
			'borders' => array(
				'allBorders' => array(
					'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
					'color' => array('argb' => '00000000'),
				),
			),
			'fill' => array(
				'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
				'startColor' => array('argb' => '92CDDC')
			)
		);

		$spreadsheet = new Spreadsheet();
		$activeSheet = $spreadsheet->getActiveSheet();
		$fontSize = array('font'  => array('size'  => 10));
		$spreadsheet->getDefaultStyle()->applyFromArray($fontSize);

		if($reportName=='Consolidated Pay Report')
		{
			$data  			= $reportDetails['Payslip_Summary'];
			if(count($data) > 0)
			{
				$reportHeader 	= array_keys($data[0]);
			}
		}
		else
		{
			$data         = $reportDetails;
			if(count($data) > 0)
			{

				$reportHeader = array_keys($data[0]);
				// Find the index of "Net Pay" column name
				$netPayIndex = array_search('Net Pay', $reportHeader);

				// If "Net Pay" is found, remove it and add it back to the end
				if ($netPayIndex !== false) {
					unset($reportHeader[$netPayIndex]);
					$reportHeader[] = 'Net Pay';
				}
			}
		}

			
		if(count($data) > 0)
		{
			$organizationDetail 		= $this->_orgDetails;
			$showReportCreator 			= $organizationDetail['Show_Report_Creator'];
			$organizationName 			= strtoupper($organizationDetail['Org_Name']);
			$dbHRReport 				= new Reports_Model_DbTable_HrReports();
			$startColumnName 			= 0;
			$endColumnNamePosition   	= count($reportHeader);
			$startColumnName 			= $dbHRReport->getNameFromNumber($startColumnName);
			$endColumnName   			= $dbHRReport->getNameFromNumber($endColumnNamePosition-1);
			$autoResizeColumnName   	= $dbHRReport->getNameFromNumber($endColumnNamePosition);
			$freezeFrom  			  	= 2;   
			$freezePositionColumnName 	= $dbHRReport->getNameFromNumber($freezeFrom);

			$rowStart 						= 1;
			$organizationNameStartPosition 	= $startColumnName.$rowStart;
			$organizationNameEndPosition 	= $endColumnName.$rowStart;
			$rowStart++;
			$reportNameStartPosition 		= $startColumnName.$rowStart;
			$reportNameEndPosition 		    = $endColumnName.$rowStart;
			$rowStart++;

			$spreadsheet->setActiveSheetIndex(0)->setCellValue("$organizationNameStartPosition", $organizationName)->setCellValue("$reportNameStartPosition", $reportTitle);
			$activeSheet->mergeCells("$organizationNameStartPosition:$organizationNameEndPosition");
			$activeSheet->mergeCells("$reportNameStartPosition:$reportNameEndPosition");
			
		
			if($showReportCreator==1)
			{
				$userSession = $this->getUserDetails ();
				$sessionId   = $userSession['logUserId'];
				$createdBy  = $this->_dbPersonal->employeeName($sessionId);
				$createdBy 	= 'Created by - '.$createdBy['Employee_Name'];
				$createdOn 	= 'Created On - '.date($this->_orgDF['php'] .' \a\t H:i:s');

				$createdByStartPosition = $startColumnName.$rowStart;
				$createdByEndPosition 	= $endColumnName.$rowStart;
				$rowStart++;
				$createdOnStartPosition = $startColumnName.$rowStart;
				$createdOnEndPosition 	= $endColumnName.$rowStart;
				$rowStart++;
				$spreadsheet->setActiveSheetIndex(0)->setCellValue("$createdByStartPosition", $createdBy)->setCellValue("$createdOnStartPosition", $createdOn);
				$activeSheet->mergeCells("$createdByStartPosition:$createdByEndPosition");
				$activeSheet->mergeCells("$createdOnStartPosition:$createdOnEndPosition");
				$activeSheet->getStyle("$createdByStartPosition:$createdByEndPosition")->applyFromArray($titleStyle);
				$activeSheet->getStyle("$createdOnStartPosition:$createdOnEndPosition")->applyFromArray($titleStyle);
			}
			$rowStart++;
			$header	= array();
			foreach($reportHeader as $keys)
			{
				$keyVal = str_replace('_', ' ', $keys);
				$header[] = $keyVal; 	
			}
			$activeSheet->fromArray($header, null, $startColumnName.$rowStart);	
			$headerFrom = $startColumnName.$rowStart;
			$headerTo   = $endColumnName.$rowStart; 
			$activeSheet->getStyle("$headerFrom:$headerTo")->applyFromArray($headerStyle);
			$activeSheet->getStyle("$organizationNameStartPosition:$organizationNameEndPosition")->applyFromArray($titleStyle);
			$activeSheet->getStyle("$reportNameStartPosition:$reportNameStartPosition")->applyFromArray($titleStyle);
			$rowStart++;
			if($reportName!='Consolidated Pay Report' || $reportName!='Consolidated Salary Statement')
			{
				$freezePosition = $freezePositionColumnName.$rowStart;
				$activeSheet->freezePane($freezePosition); 
			}

			foreach ($data as $row)
			{
				$newRow = array();
				foreach($reportHeader as $k)
				{
					if(isset($row[$k]))
					{
						if($row[$k] == '0')
						{
							$newRow[$k] = '0.00';
						}
						elseif($row[$k] == '-')
						{
							$newRow[$k] = '';
						}
						else
						{
							$row[$k] = str_replace("\n", '', $row[$k]);
							if (is_numeric($row[$k])) 
							{
								$newRow[$k]=$this->formatCurrency($row[$k], $locale);
							}
							else
							{
								$newRow[$k] = $row[$k];
							}
						}
					}
					else 
					{
						$newRow[$k] = '';
					}
					$dataFrom = $startColumnName.$rowStart;
					$dataTo   = $endColumnName.$rowStart; 
					$activeSheet->getStyle("$dataFrom:$dataTo")->applyFromArray($dataBorder);
				}
				$activeSheet->fromArray($newRow, null, $startColumnName.$rowStart);
				$dbHRReport->convertExcelSheetNumberFormatToString($reportHeader,$activeSheet,$rowStart);
				$rowStart++;	
			}

			if($reportName=='Consolidated Pay Report' && count($reportDetails['NetPay_Details']) > 0)
			{
				$data  = $reportDetails['NetPay_Details'];
				foreach ($data as $row)
				{
					$newRow = array();
					foreach($reportHeader as $k)
					{
						if(isset($row[$k]))
						{
							if($row[$k] == '0')
							{
								$newRow[$k] = '0.00';
							}
							elseif($row[$k] == '-')
							{
								$newRow[$k] = '';
							}
							else
							{
								$row[$k] = str_replace("\n", '', $row[$k]);
								if (is_numeric($row[$k])) 
								{
									$newRow[$k]=$this->formatCurrency($row[$k], $locale);
								}
								else
								{
									$newRow[$k] = $row[$k];
								}
								
							}
						}
						else 
						{
							$newRow[$k] = '';
						}
						$dataFrom = $startColumnName.$rowStart;
						$dataTo   = $endColumnName.$rowStart; 
						$activeSheet->getStyle("$dataFrom:$dataTo")->applyFromArray($titleStyle);
					}
					$activeSheet->fromArray($newRow, null, $startColumnName.$rowStart);
					$dbHRReport->convertExcelSheetNumberFormatToString($reportHeader,$activeSheet,$rowStart);
					$rowStart++;	
				}
			}		

			foreach ($footer as $foot)
			{
				$footerRow = array();
				foreach($reportHeader as $k)
				{
					if(!isset($foot[$k]) || $foot[$k] == '-')
					{
						$footerRow[$k] = '--';
					}
					elseif($foot[$k] == '')
					{
						$footerRow[$k] = '';
					}
					else
					{
						if (is_numeric($foot[$k])) 
						{
							$footerRow[$k]=$this->formatCurrency($foot[$k], $locale);
						}
						else
						{
							$footerRow[$k] = $foot[$k];
						}
					}
				}
				$footerFrom = $startColumnName.$rowStart;
				$footerTo   = $endColumnName.$rowStart;
				$activeSheet->getStyle("$footerFrom:$footerTo")->applyFromArray($titleStyle);	
				$activeSheet->fromArray($footerRow, null, $startColumnName.$rowStart);
			}
	    }
		else
		{
			$emptyScenarioMessage = 'No Records Found';
			$emptyScenarioStartPosition  = 'A1';
			$emptyScenarioEndPosition 	 = 'F1';
			$spreadsheet->setActiveSheetIndex(0)->setCellValue("$emptyScenarioStartPosition", $emptyScenarioMessage);
			$activeSheet->mergeCells("$emptyScenarioStartPosition:$emptyScenarioEndPosition");
			$activeSheet->getStyle("$emptyScenarioStartPosition:$emptyScenarioEndPosition")->applyFromArray($titleStyle);
			
		}
		$activeSheetTitleLength = strlen($reportTitle);
		if($activeSheetTitleLength > 31)
		{
			$activeSheetTitle = substr($reportTitle, 0, 31);
		}
		else
		{
			$activeSheetTitle = $reportTitle;
		}

		$activeSheet->setTitle($activeSheetTitle);
		// Set active sheet index to the first sheet, so Excel opens this as the first sheet
		$spreadsheet->setActiveSheetIndex(0);

		for($col = $startColumnName ; $col !== $autoResizeColumnName; $col++)
		{
			$activeSheet->getColumnDimension($col)->setAutoSize(true);
		}

		ob_end_clean();
		// Redirect output to a client’s web browser (Xlsx)
		header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
		header('Content-Disposition: attachment;filename='.$reportTitle.'.xlsx');
		header('Cache-Control: max-age=0');
		// If you're serving to IE 9, then the following may be needed
		header('Cache-Control: max-age=1');

		// If you're serving to IE over SSL, then the following may be needed
		header('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // Date in the past
		header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // always modified
		header('Cache-Control: cache, must-revalidate'); // HTTP/1.1
		header('Pragma: public'); // HTTP/1.0
		ob_end_clean();
		$writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
		$writer->save('php://output');
		exit;
	}

	//Get the business unit id and business unit
	public function getBusinessUnitPairs(){
		return $this->_db->fetchPairs($this->_db ->select()->from($this->_ehrTables->businessUnit,array('Business_Unit_Id','Business_Unit'))
									->where('Business_Unit_Status = ?','Active')
									->order('Business_Unit ASC'));
	}

	public function getMultiLevelManagerIds($employeeIds, $level)
	{
		$maxLevels = 5;

		if ($level > $maxLevels) {
			return []; // Reached the maximum depth
		}

		if (!empty($employeeIds)) {
			$managerIds = $this->_db->fetchCol(
				$this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
					->where('Manager_Id IN (?)', $employeeIds)
			);
		} else {
			$managerIds = [];
		}

		// Recursively get managerIds at the next level
		$nextLevelManagerIds = $this->getMultiLevelManagerIds($managerIds, $level + 1, $maxLevels);

		// Merge and deduplicate the managerIds from the current and next levels
		$allManagerIds = array_merge($managerIds, $nextLevelManagerIds);
		$uniqueManagerIds = array_unique($allManagerIds);

		return $uniqueManagerIds;
	}

	public function getEmployeeLocale($sessionId)
	{
		return $this->_db->fetchone($this->_db->select()->from(array('EJ'=>$this->_ehrTables->empJob),array('CO.Locale'))
										->joinInner(array('EL'=>$this->_ehrTables->location), 'EL.Location_Id =EJ.Location_Id',array(''))
										->joinInner(array('CO'=>$this->_ehrTables->country),  'CO.Country_Code=EL.Country_Code',array(''))
									    ->where('EJ.Employee_Id Like ?',$sessionId));
	}

	public function formatCurrency($number, $locale)
	{
		if(!empty($locale))
		{
			$style = NumberFormatter::DECIMAL; // Set the number format style to decimal
			$decimals = 2; // Set the number of decimal places
			$formatter = new NumberFormatter($locale, $style);
			$formatter->setAttribute(NumberFormatter::FRACTION_DIGITS, $decimals);
			$formattedNumber = $formatter->format($number);
			return $formattedNumber;
		}
		else
		{
			return $number;
		}
	}

	public function getWorkScheduleAndShiftRosterDetails($employeeIds,$startDate,$endDate)
    {
        $dbAttendance              = new Employees_Model_DbTable_Attendance();
        $dbPayslip                 = new Payroll_Model_DbTable_Payslip();
	    $employeeDetails 		   = $dbPayslip->getSalaryPayslipEmployeeDetails($employeeIds,$startDate,$endDate);
        $rosterManagementSettings  = $dbPayslip->getRosterManagementSettings();
        $allWorkScheduleDetails    = $dbAttendance->getAllWorkScheduleDetails($startDate,$endDate);
        $allEmployeeShiftDetails   = $dbAttendance->getAllEmployeeShiftDetails($employeeDetails,$startDate,$endDate);
        $allHolidayDetails         = $dbPayslip->getAllHolidayDates($startDate,$endDate);
	
		return array('employeeDetails'=>$employeeDetails,'rosterManagementSettings'=>$rosterManagementSettings,'allWorkScheduleDetails'=>$allWorkScheduleDetails,'allEmployeeShiftDetails'=>$allEmployeeShiftDetails,'allHolidayDetails'=>$allHolidayDetails);
    }

    public function getWorkScheduleDetailsByDate($employeeId,$startDate,$employeeWorkScheduleShiftDetails)
    {
		$workScheduleDetails       = array();
        $organizeDataByEmployeeId  = $this->organizeDataByEmployeeIdAndDate($employeeWorkScheduleShiftDetails['employeeDetails'],'Employee_Id');
		$ensureArray           	   = $this->ensureArray($organizeDataByEmployeeId, $employeeId);
		$employeeDetails           = $ensureArray[0];
		$rosterManagementSettings  = $employeeWorkScheduleShiftDetails['rosterManagementSettings'];
		$allWorkScheduleDetails    = $employeeWorkScheduleShiftDetails['allWorkScheduleDetails'];
		$allEmployeeShiftDetails   = $employeeWorkScheduleShiftDetails['allEmployeeShiftDetails'];
		$allHolidayDetails         = $employeeWorkScheduleShiftDetails['allHolidayDetails'];
        $employeeId                = $employeeDetails['Employee_Id']; 
        $workScheduleId            = $employeeDetails['WorkSchedule_Id'];
        $employeeHolidayDetails    = $allHolidayDetails[$employeeId]?? null;
        if($employeeDetails['Work_Schedule']=='Shift Roster')
        {
            $employeeShiftDetails = $allEmployeeShiftDetails[$employeeId]?? null;
			$workSchedule = $this->getWorkScheduleDetailsByDateForShiftRosterEmployees($employeeId,$startDate,$employeeShiftDetails,$allWorkScheduleDetails,$rosterManagementSettings);
			if(!empty($workSchedule))
			{
				$checkWorkingDay     = $this->checkWorkingDay($employeeId,$startDate,$employeeHolidayDetails,$workSchedule);
				$workScheduleDetails = $checkWorkingDay['Work_Schedule_Details'];
			}
        }
        else
        {
            $employeeWorkScheduleDetails = $allWorkScheduleDetails[$workScheduleId]?? null;
            $workSchedule = $employeeWorkScheduleDetails[$startDate] ?? null;
            if ($workSchedule !== null)
            {
                $checkWorkingDay     = $this->checkWorkingDay($employeeId,$startDate,$employeeHolidayDetails,$workSchedule);
                $workScheduleDetails = $checkWorkingDay['Work_Schedule_Details'];
            }
        }
        return $workScheduleDetails;
    }

	public function getBusinessWorkingDays($salaryDate, $lastSalaryDate,$employeeId,$totalWorkingDays = NULL,$leaveCalculationDays = NULL,$formName = NULL,$compOffCalculationMethod=null,$employeeWorkScheduleShiftDetails=NULL)
	{
		$workScheduleDetails       = array();
		$workingDays 	           = 0;
		$busineesWorkingDays 	   = 0;
        $dbAttendance              = new Employees_Model_DbTable_Attendance();
        $dbPayslip                 = new Payroll_Model_DbTable_Payslip();
		$dbHRReport                = new Reports_Model_DbTable_HrReports();
		$organizeDataByEmployeeId  = $dbHRReport->organizeDataByEmployeeIdAndDate($employeeWorkScheduleShiftDetails['employeeDetails'],'Employee_Id');
		$ensureArray           	   = $dbHRReport->ensureArray($organizeDataByEmployeeId, $employeeId);
		$employeeDetails           = $ensureArray[0];
		$employeeId                = $employeeDetails['Employee_Id'];

		if( $employeeDetails['Salary_Date'] && (strtotime($employeeDetails['Salary_Date']) > strtotime($salaryDate)) && is_null($totalWorkingDays))
		{
			$salaryDate = $employeeDetails['Salary_Date'];
		}
		
		if( $employeeDetails['Last_Salary_Date'] && (strtotime($employeeDetails['Last_Salary_Date']) < strtotime($lastSalaryDate)) && is_null($totalWorkingDays))
		{
			$lastSalaryDate = $employeeDetails['Last_Salary_Date'];
		}

		if($compOffCalculationMethod==='Yes')
        {
            $employeeDetails['Salary_Calc_Days'] = $employeeDetails['Comp_Off_Days'];
            $employeeDetails['Fixed_Days'] = $employeeDetails['Comp_Off_Fixed_Days'];
        }
		
		if($employeeDetails['Salary_Calc_Days'] == 0 || $leaveCalculationDays == 1)
        {
			$last = strtotime($employeeDetails['Last_Salary_Date']);
			$current = strtotime($employeeDetails['Salary_Date']);
			$step                      = '+1 day'; /**decrementor in while loop**/
			//looping each date and finding whether the employee is absent or not for that particular day.
			while( $current <= $last )
			{
				$startDate                 = date('Y-m-d',$current);
				$workScheduleDetails       = $this->getWorkScheduleDetailsByDate($employeeId,$startDate,$employeeWorkScheduleShiftDetails);

				if(!empty($workScheduleDetails))
				{
					if($workScheduleDetails['Business_Working_Days'] > 0)
					{
						$workingDays += $workScheduleDetails['Business_Working_Days'];
					}
				}
				else if($formName=='leaves' && $employeeDetails['Work_Schedule']=='Shift Roster')
				{
					return 'shiftnotscheduled';
				}

				$current = strtotime($step, $current); /**incrementor in while loop**/
			}
		}
		elseif($employeeDetails['Salary_Calc_Days'] == 1 || $employeeDetails['Salary_Calc_Days'] == 2)
        {
            $date1 = new DateTime(date('Y-m-d', strtotime($this->_ehrTables->changeDateformat($salaryDate))));
			$date2 = new DateTime($lastSalaryDate);
				
			$workingDays = ($date2->diff($date1)->format("%a"))+1;
        }
         //if $employeeDetails == 3, then we are considering the fixed days of the month
        elseif($employeeDetails['Salary_Calc_Days'] == 3)
        {//To Do
            $date1 = new DateTime(date('Y-m-d', strtotime($this->_ehrTables->changeDateformat($salaryDate))));
            $date2 = new DateTime($lastSalaryDate);
            
            $workingDays = ($date2->diff($date1)->format("%a"))+1;
        
			if(!empty($employeeDetails['Fixed_Days']))
			{
				$fixedDays = $employeeDetails['Fixed_Days'];        
			}
			else
			{
				$fixedDays =0;
			}
            
            
            if($workingDays < $fixedDays)
            {
                $workingDays = $workingDays;
            }
            else
            {
                $workingDays = $fixedDays;
            }
           
        }
		return $workingDays;
	}

	public function checkWorkingDay($employeeId,$startDate,$holidayDetailsByDateAndEmployee,$workScheduleDetails)
    {
        $businessWorkingDays = 0;
        if(!empty($workScheduleDetails))
        {
            $holidayExist = $holidayDetailsByDateAndEmployee[$startDate] ?? null;
            // If a holiday exists, update $workScheduleDetails with holiday details.
            if ($holidayExist !== null){
                $workScheduleDetails['Holiday_Exist']   = 'Yes';
                $workScheduleDetails['Mandatory']       = $holidayExist['Mandatory'];
                $workScheduleDetails['Personal_Choice'] = $holidayExist['Personal_Choice'];
                $workScheduleDetails['Holiday']         = $holidayExist['Holiday'];
            } else {
                // No holiday found, set default values.
                $workScheduleDetails['Holiday_Exist']   = 'No';
                $workScheduleDetails['Mandatory']       = 0;
                $workScheduleDetails['Personal_Choice'] = 0;
                $workScheduleDetails['Holiday']         = 0;
            }

            if ($workScheduleDetails['Holiday_Exist'] == 'Yes') {
                $workScheduleDetails['Business_Working_Days'] = 0;
            }
            else if ($workScheduleDetails['Week_Off_Exist'] == 'Yes') {
                // Check if it's a half-day week off
                if (isset($workScheduleDetails['Week_Off_Duration']) && $workScheduleDetails['Week_Off_Duration'] == 0.5) {
                    $workScheduleDetails['Business_Working_Days'] = 0.5;
                    $businessWorkingDays = 0.5;
                } else {
                    $workScheduleDetails['Business_Working_Days'] = 0;
                }
            }
            else
            {
                $workScheduleDetails['Business_Working_Days'] = 1;
                $businessWorkingDays = 1;
            }
        }
        // Check if there's a holiday or week off, and update Business_Working_Day accordingly.
        return array('Business_Working_Days'=>$businessWorkingDays,'Work_Schedule_Details'=>$workScheduleDetails);
    }

	public function getWorkScheduleDetailsByDateForShiftRosterEmployees($employeeId,$startDate,$allShiftDetails,$allWorkScheduleDetails,$rosterManagementSettings)
	{
		$workScheduleDetails = array();
		$shiftDetails = $allShiftDetails[$startDate] ?? null;
		if ($shiftDetails !== null)
		{
			$workScheduleId = $shiftDetails['WorkSchedule_Id'];
			$workScheduleDetails = $allWorkScheduleDetails[$workScheduleId][$startDate]?? null;
			if ($workScheduleDetails !== null)
			{
				if($rosterManagementSettings['Overlap_Shift_Schedule']==1) 
				{ 
					$workScheduleDetails = $this->getOverLapShiftSchedulingDetails($allShiftDetails,$allWorkScheduleDetails,$startDate,$workScheduleDetails); 
				} 
				if(!empty($workScheduleDetails)) 
				{ 
					if($rosterManagementSettings['Dynamic_Week_Off']==1)
					{
						if(!empty($shiftDetails['Week_Off']))
						{
							$workScheduleDetails['Week_Off_Exist']   	= 'Yes';
							$workScheduleDetails['Week_Off_Duration'] 	= 1;
						}
						else
						{
							$workScheduleDetails['Week_Off_Exist']     	= 'No';
							$workScheduleDetails['Week_Off_Duration']   = 0;
						}
					}
			    }
			}
		}
		return $workScheduleDetails;
	}

	//Function to send the email
	public function sendBulkTemplateEmail($allEmailInputs)
	{
		$message = '';

		$orgCode = $this->_ehrTables->getOrgCode();
		$curl = curl_init();
		$method = "POST";
		$apiBaseUrl = Zend_Registry::get('coreHrWoBaseUrl');
		if(!empty($apiBaseUrl))
		{
			$url = $apiBaseUrl;
		}	
		else
		{
			curl_close($curl);
			return "Oops! Something went wrong while sending the email. Please contact the platform administrator.";
		}

		//Form input JSON
		$defaultTemplateDataJson = new \stdClass();
		$defaultTemplateDataJson->emailSubject= $allEmailInputs['emailSubject'];
		$defaultTemplateDataJson->orgLogo= '';
		$defaultTemplateDataJson->title1= '';
		$defaultTemplateDataJson->title2= '';
		$defaultTemplateDataJson->centerImage= '';
		$defaultTemplateDataJson->subTitle= strip_tags($allEmailInputs['subtitle']);
		$defaultTemplateDataJson->redirectionUrl= $allEmailInputs['redirectionUrl'];
		$defaultTemplateDataJson->buttonText= $allEmailInputs['buttonText'];
		$defaultTemplateDataJson->footer= 'Yes';//Send footer value to present the footer text with support email
		$defaultTemplateDataJson->supportEmail= $allEmailInputs['supportEmail'];
		$defaultTemplateDataJson = json_encode($defaultTemplateDataJson);

		$emailInputs = new \stdClass();
		$emailInputs->DefaultTemplateData = $defaultTemplateDataJson;

		$destinationEmailDetails = array('ToAddresses' => $allEmailInputs['ToAddresses']);
		if(!empty($allEmailInputs['CcAddresses'])){
			$destinationEmailDetails['CcAddresses'] = $allEmailInputs['CcAddresses'];
		}
		$emailInputs->Destinations = array(
			'Destination' => $destinationEmailDetails,
			'ReplacementTemplateData'=> $defaultTemplateDataJson
		);

		$apiInputsJSON = json_encode($emailInputs);
		$requestBody = '{
			"variables" : '.$apiInputsJSON.',
			"query": "mutation sendCommonEmail($Template: String, $Destinations: [DestinationEmail!]!, $DefaultTemplateData: String){ sendCommonEmail(Template: $Template, Destinations: $Destinations, DefaultTemplateData: $DefaultTemplateData) { errorCode message } }"
		}';

		curl_setopt($curl, CURLOPT_POST, 1);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $requestBody);
		curl_setopt($curl, CURLOPT_URL, $url);
		
		//Set API headers
		$apiHeaders = $this->getApiHeaders($orgCode);
		curl_setopt($curl, CURLOPT_HTTPHEADER, $apiHeaders);

		curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

		// EXECUTE:
		$result = curl_exec($curl);
		curl_close($curl);
		
		if($result)
		{
			$responseData = json_decode($result,true);

			if(isset($responseData['message']) && (isset($responseData['message']['message']) && $responseData['message']['message'] == 'API gateway timeout.')){
				$message = 'Oops! Something went wrong while sending the email. Please contact the platform administrator.';
			}else {
				if (isset($responseData['data']) && isset($responseData['data']['sendCommonEmail'])
				&& !empty($responseData['data']['sendCommonEmail'])){
					$message = '';
				} else if(isset($responseData['errors']) && count($responseData['errors']) > 0) {
					if(isset($responseData['errors'][0]['message'])){
						$errorResponse = $responseData['errors'][0];
						$errorCode = $responseData['errors'][0]['extensions']['code'];
						
						switch($errorCode){
							case 'CCH0008'://'Oops! Something went wrong while sending the email. Please contact the platform administrator.'
								$message =  $responseData['errors'][0]['message'];
								break;
							default:
								// "_UH0001": Something went wrong! Please contact system admin.
								$message =  'Oops! Something went wrong while sending the email. Please contact the platform administrator.';
								break;
						}
					}else{
						$message = 'Oops! Something went wrong while sending the email. Please contact the platform administrator.';
					}
				} else {
					$message = 'Oops! Something went wrong while sending the email. Please contact the platform administrator.';
				}
			}
		}else{
			$message = 'Oops! Something went wrong while sending the email. Please contact the platform administrator.';
		}
		return $message;
	}


	public function organizeDataByEmployeeIdAndDate($data, $employeeIdKey, $dateKey=NULL) {
		$organizedData = [];
		if(!empty($data))
		{
			foreach ($data as $item) {
				$employeeId = $item[$employeeIdKey];
				if(!empty($dateKey))
				{
					$date = $item[$dateKey];
					$key = "$employeeId|$date";
				}
				else
				{
					$key = $employeeId;
				}

				if (!isset($organizedData[$key])) {
					$organizedData[$key] = [];
				}
		
				$organizedData[$key][] = $item;
			}
		}
		unset($data);
		return $organizedData;
	}

	function ensureArray($array, $key) {
		return isset($array[$key]) ? (is_array($array[$key]) ? $array[$key] : [$array[$key]]) : null;
	}

	function valueExistsInArray($array,$value)
	{
		if (in_array($value, $array))
		{
			return 1;
		}
		else
		{
			return 0;
		}
	}

	public function defaultFilterData($searchArray)
	{
		$salaryMonth             = NULL;
		$salaryYear              = NULL;
		$notificationRedirection = NULL;
		if(isset($searchArray['encodedParams']))
		{
			$decodedParams = base64_decode($searchArray['encodedParams']);
			parse_str($decodedParams, $params);
			if(isset($params['salaryMonth']) && isset($params['salaryYear']) && isset($params['notificationRedirection']))
			{
				$salaryMonth 			 = $params['salaryMonth'];
				$salaryYear 			 = $params['salaryYear'];
				$notificationRedirection = $params['notificationRedirection'];
			}
		}
		$searchArray['Salary_Month']=$salaryMonth;
		$searchArray['Salary_Year']=$salaryYear;
		$searchArray['Notification_Redirection']=$notificationRedirection;
		return $searchArray;
	}

	public function payslipGeneratedResignedMonthFinancialEndMonth($employeeId)
    {
        $dbPayslip       = new Payroll_Model_DbTable_Payslip();
        $salaryType      = $dbPayslip->getEmpSalaryType($employeeId);
        $maxPayslipMonth = $dbPayslip->maxPayslipMonth($employeeId,$salaryType);
		if(!empty($maxPayslipMonth))
        {
            $resignationDate = $this->getEmployeeResignationDate($employeeId);
            if(!empty($resignationDate))
            {
                $resignationMonth = date("m", strtotime($resignationDate));// get month from the punch-in date
                $resignationYear  = date("Y", strtotime($resignationDate));// get year from the punch-in date
                /* Get the paycycle start and end date for the punch in month and year */
                $paycyledate 	  = $dbPayslip->getSalaryDateRange($resignationMonth,$resignationYear,strtotime($resignationDate));
                $resignationMonth = date('m,Y', strtotime($paycyledate['Last_SalaryDate']));
                if($resignationMonth==$maxPayslipMonth)
                {
					return array('success'=>false, 'msg'=>"A payslip has been generated for the month of the employee's resignation, hence, we are unable to change the declaration status. Please delete the payslip and revert the declaration", 'type'=>"warning");
                }
				else
                {
					return array('success'=>true, 'msg'=>"",'type'=>"success");
                }
            }
			else
			{
				$dbFinancialYr     = new Default_Model_DbTable_FinancialYear();
				$fiscalMonthArray  = $dbFinancialYr->getFiscalMonthYear(null,$this->_orgDetails['Assessment_Year']);
				$financialEndMonthExplode 	= explode(',',$fiscalMonthArray[11]);
				$month = $financialEndMonthExplode[0];
				$year = $financialEndMonthExplode[1];

				// Adding leading zero to month if necessary
				$month = str_pad($month, 2, "0", STR_PAD_LEFT);

				$dateString = $year . '-' . $month . '-01';
				// Format the date
				$financialEndMonth = date('m,Y', strtotime($dateString));
				if($financialEndMonth==$maxPayslipMonth)
				{
					return array('success'=>false, 'msg'=>"A payslip has been generated for the end of the fiscal month, hence, we are unable to change the declaration status.Please delete the payslip and revert the declaration.", 'type'=>"warning");
				}
				else
				{
					return array('success'=>true, 'msg'=>"",'type'=>"success");
				}
			}
        }
        else
        {
            return array('success'=>true, 'msg'=>"",'type'=>"success");
        }
    }

	public function getEmployeeTaxRegime($employeeId,$sessionId)
	{
		$dbPayslip 				   = new Payroll_Model_DbTable_Payslip();
		$isContractorTdsApplicable = $dbPayslip->isContractorTdsApplicable($employeeId);
		$eligibleForContractorTds  = $isContractorTdsApplicable['Eligible_For_Contractor_Tds'];
		if((int)$eligibleForContractorTds===1)
		{
			if($employeeId==$sessionId)
			{
				$message ="Welcome! Please be advised that as a contractor employee, you do not have the eligibility to make declarations at this time. If you have any inquiries or require assistance, please don't hesitate to contact admin.";
			}
			else
			{
				$message = "Contractor employees are not permitted to make declarations.";
			}
			return array('success'=>false, 'msg'=>$message,"taxRegime"=>'','type'=>"warning");
		}
		else
		{
			$dbFinancialYr     = new Default_Model_DbTable_FinancialYear();
			$fiscalMonthArray  = $dbFinancialYr->getFiscalMonthYear(null,$this->_orgDetails['Assessment_Year']);
			$payslipDetails    = array();
			if(!empty($fiscalMonthArray))
			{
				/** get last payslip id which exist in between the financial start and the end date */
				$payslipDetails =  $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->monthlyPayslip,array('Payslip_Id',
															new Zend_Db_Expr('MAX(STR_TO_DATE(Salary_Month,"%m,%Y")) as Salary_Month')))
															->where('Salary_Month IN (?)',$fiscalMonthArray)
															->where('Employee_Id = ?',$employeeId));
			}

			if(!empty($payslipDetails) && isset($payslipDetails['Payslip_Id']))
			{
				$payslipId = $payslipDetails['Payslip_Id'];
				$currentAssessmentYearPayslipExist=1;
			}
			else
			{
				$payslipId = 0;
				$currentAssessmentYearPayslipExist=0;
			}
			$taxRegime = $dbPayslip->getTaxRegime($employeeId,$payslipId,$currentAssessmentYearPayslipExist);
			
			if($taxRegime=='New Regime')
			{
				if($employeeId==$sessionId)
				{
					$message = "Welcome! As you've recently selected a new regime, any declaration you add will not be considered for tax calculation. If you have any questions or require assistance, feel free to reach out to the admin.";
				}
				else
				{
					$message = 'The employee has opted for a new regime.any declaration added will not be considered for tax calculation.';
				}
			}
			else
			{
				$message = '';
			}
			return array('success'=>false, 'msg'=>$message,"taxRegime"=>$taxRegime,'eligibleForContractorTds'=>$eligibleForContractorTds,'type'=>"warning");
		}
	}

	public function getReimbursementSettings(){
		return $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->reimbursementSettings, array('Enable_Workflow','Workflow_Id')));
	}

	public function getWorkflowEventId($workflowId){
		return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->workflows, array('Event_Id'))
				->where('Workflow_Id = ?',$workflowId));
	}

	public function getProcessInstanceId($requestId){
		return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->reimbursement, array('Process_Instance_Id'))
				->where('Request_Id = ?',$requestId));
	}

	public function getEmployeeResignationDetails($employeeId)
	{
		$resignationEmployeeDetails = $this->_db->fetchAll($this->_db->select()->from(array($this->_ehrTables->resignation),array('Employee_Id','Resignation_Date'))
																														->where('Employee_Id IN (?)',$employeeId)
																														->where('Approval_Status = ?', 'Approved'));

		$resignationDataByEmployeeId  = $this->organizeDataByEmployeeIdAndDate($resignationEmployeeDetails,'Employee_Id');
        return $resignationDataByEmployeeId;  																																
	}

	public function getCamuUrlForServiceProvider($empId){
		return $this->_db->fetchOne($this->_db->select()->from(array('J'=>$this->_ehrTables->empJob), array('S.Camu_Base_Url'))
					->joinLeft(array('S'=>$this->_ehrTables->serviceProvider), 'J.Service_Provider_Id=S.Service_Provider_Id',array(''))
					->where('Employee_Id = ?',$empId));
	}

	public function getCoverageForFormId($formId){
		return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->formLevelCoverage, array('Coverage'))
                                                        ->where('Form_Id = ?', $formId));
	}

	/**
	 * Function to get whether quarter duration is configured for any active leave types
	*/
	public function isQuarterDurationConfigured()
	{
		$quarterDaysConfigured = $this->_db->fetchOne($this->_db->select()->from(array('L' => $this->_ehrTables->leavetype), array('COUNT(L.LeaveType_Id)'))
				->joinInner(array('LD' => $this->_ehrTables->leaveTypesDuration),'L.LeaveType_Id = LD.LeaveType_Id',array())
				->where('L.Leave_Status = ?', 'Active')
				->where('LD.Duration_Value = ?','0.25'));
		$quarterDaysConfigured = ($quarterDaysConfigured > 0) ? 1 : 0;
		return $quarterDaysConfigured;
	}

	//Function to get the allow upline manager flag based on the form id
	public function getUplineManagerFlag($formId)
	{
		return $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->formUplineApprovals, array('Allow_Upline_Manager_For_Approval'))
				->where('Form_Id = ?', $formId));
	}

	/*
	* Function to trigger the attendance summary step for the following actions:
	* - Attendance: add, update, delete, status update (reject), and attendance import process
	* - Leaves: add, update, delete, cancel, and reject
	* - Comp Off: add, update, delete, cancel, and reject
	* - Short Time Off (Permission): add, update, delete, cancel, and reject
	*/
	public function triggerAttendanceSummaryStepFunction($employeeArray,$triggeredFrom)
	{
		$responseData = '';
		$message = '';
		$inputDetailsLog = '';
		$apiHeadersLog = '';
		if(!empty($employeeArray) && count($employeeArray) > 0){
			$selectedDetails = [];
			//Attendance, Leaves, Comp Off and Short Time Off details will be send in the employeeArray
			foreach ($employeeArray as $empRecord) {
				$employeeId = $empRecord["Employee_Id"];

				switch ($triggeredFrom) {
					case 'attendance':
						$attendanceStatus = array('Applied','Approved','Returned','Rejected');
						if ((in_array($empRecord['Approval_Status'],$attendanceStatus))) {
							$selectedDetails[] = [
								"Employee_Id" => $employeeId,
								"Summary_Date" => $empRecord["Attendance_Date"]
							];
						}
						break;
					case 'leaves':
						$leaveFromDate = $empRecord["Start_Date"];
						$leaveToDate = $empRecord["End_Date"];

						$startDate = $leaveFromDate;
						while(strtotime($leaveToDate) >= strtotime($startDate)){
							$selectedDetails[] = [
								"Employee_Id" => $employeeId,
								"Summary_Date" => $startDate
							];
							$startDate = date("Y-m-d", strtotime('+1 days',strtotime($startDate)));
						}
						break;

					case 'shorttimeoff':
						$selectedDetails[] = [
							"Employee_Id" => $employeeId,
							"Summary_Date" => $empRecord["Short_Time_Off_Date"]
						];
						break;

					case 'compoff':
						$selectedDetails[] = [
							"Employee_Id" => $employeeId,
							"Summary_Date" => $empRecord["Compensatory_Date"]
						];
						break;
				}
			}
			if($selectedDetails && count($selectedDetails) > 0){
				$orgCode = $this->_ehrTables->getOrgCode();
				$curl = curl_init();
				$method = "POST";
				$apiBaseUrl = Zend_Registry::get('batchProcessingExternalBaseURL');
				if(!empty($apiBaseUrl))
				{
					$url = $apiBaseUrl;
				}	
				else
				{
					curl_close($curl);
					return array('success' => false, 'message' => 'URL not found to trigger the attendance summary step function. Please contact system admin.');
				}
				
				$apiInputs = new \stdClass();
				$selectedDetailsJSON = json_encode($selectedDetails);
				$apiInputs->employeeAttendanceDetails = $selectedDetailsJSON;
				$apiInputs->orgCode = $orgCode;

				// Convert to JSON
				$apiInputsJSON = json_encode($apiInputs);
				$requestBody = '{
					"variables" : '.$apiInputsJSON.',
					"query":"query triggerAttendanceSummaryProcess($orgCode: String!,$employeeAttendanceDetails: String!) { triggerAttendanceSummaryProcess(orgCode: $orgCode,employeeAttendanceDetails: $employeeAttendanceDetails ) { errorCode message }}"
				}';

				curl_setopt($curl, CURLOPT_POST, 1);
				curl_setopt($curl, CURLOPT_POSTFIELDS, $requestBody);
				curl_setopt($curl, CURLOPT_URL, $url);
				
				//Set API headers
				$apiHeaders = $this->getApiHeaders($orgCode);
				curl_setopt($curl, CURLOPT_HTTPHEADER, $apiHeaders);
				curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

				//EXECUTE
				$result = curl_exec($curl);
				curl_close($curl);

				$inputDetailsLog = $apiInputsJSON;
				$apiHeadersLog = json_encode($apiHeaders);
				if(!$result){
					$message = 'No response was received from the execution endpoint.';
				}else{
					$responseData = json_decode($result,true);
				}
			}
		}
		return array('message' => $message, 'responseData' => $responseData, 'inputDetailsLog' => $inputDetailsLog, 'apiHeadersLog' => $apiHeadersLog);
	}
	/**
	 * Function to trigger the syntrum integration endpoint for the following actions
	 * - Unpaid and Sick leave - approve, cancel
	*/
	public function triggerSyntrumExecutionRequest($syntrumInputDetails,$sessionId)
	{
		extract($syntrumInputDetails, EXTR_SKIP);	
		$errorLogResponse = array();
		if((!empty($uniqueIds) && count($uniqueIds) > 0) || (!empty($uniqueIdDetails) && count($uniqueIdDetails) > 0)){
			$syncDirection = 'Push';
			if(!empty($elementHeader)){
				$entityType = array($elementHeader);
			}else{
				$entityType = $leaveTypeCode;
			}
			$isIntegrationApplicable = $this->_db->fetchOne($this->_db->select()->from(array('E' => $this->_ehrTables->externalApiSyncDetails), 
										array('COUNT(E.Integration_Type)'))
										->where('E.Integration_Type = ?', 'Syntrum')
										->where('E.Entity_Type IN (?)', $entityType)
										->where('E.Sync_Direction = ?', $syncDirection)
										->where('E.Action = ?', $action)
										->where('E.Status = ?','Active'));
			if($isIntegrationApplicable > 0){
				$orgCode = $this->_ehrTables->getOrgCode();

 				$apiInputs = new \stdClass();
				$apiInputs->action = $action;
				$apiInputs->orgCode = $orgCode;
				$apiInputs->entityType = $elementHeader;//SICK LEAVE WILL NOT BE SEND
				$apiInputs->uniqueIds = $uniqueIds;
				$apiInputs->uniqueIdDetails= (count($uniqueIdDetails) > 0) ? json_encode($uniqueIdDetails) : '';
				
				// Convert to JSON
				$apiInputsJSON = json_encode($apiInputs);

				$variables = array(
					"inputPayload" => $apiInputsJSON,
					"stateMachineName" => "syntrumLeaveIntegrationStateMachineArn"
				);

				$variablesJSON = json_encode($variables);

				$otherInputs = array(
					'logError' => 1,
					'logTable' => 'externalAPISyncStatus',
					'sessionId' => $sessionId
				);
				$this->triggerStepFunction($orgCode,$variablesJSON,$otherInputs);
			}
		}	
		return 'success';
	}

	/**
	 * Function to trigger the step function for the following actions
	 * - Unpaid and Sick leave - approve, cancel
	*/
	public function triggerStepFunction($orgCode,$inputVariableJSON,$otherInputs){
		extract($otherInputs, EXTR_SKIP);
		$curl = curl_init();
		$method = "POST";
		$apiBaseUrl = Zend_Registry::get('batchProcessingExternalBaseURL');
		if(!empty($apiBaseUrl))
		{
			$url = $apiBaseUrl;
		}
		else
		{
			curl_close($curl);
			return array('success' => false, 'message' => 'The required URL for triggering the request is missing. Please contact the system administrator','response'=>null);
		}
		
		$requestBody = '{
			"variables" : '.$inputVariableJSON.',
			"query":"query triggerStepFunction($inputPayload:String!, $stateMachineName:String!) { triggerStepFunction(inputPayload: $inputPayload, stateMachineName: $stateMachineName) { errorCode message } }"
		}';

		curl_setopt($curl, CURLOPT_POST, 1);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $requestBody);
		curl_setopt($curl, CURLOPT_URL, $url);
		
		//Set API headers
		$apiHeaders = $this->getApiHeaders($orgCode);
		curl_setopt($curl, CURLOPT_HTTPHEADER, $apiHeaders);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

		//EXECUTE
		$result = curl_exec($curl);
		curl_close($curl);
		if(!$result) {
			$errorLogResponse = array('status' => false, 'message' => 'No response was received from the execution endpoint.', 'response' => null);
		}else{
			$responseData = json_decode($result,true);											
			if(isset($responseData['message']) && (isset($responseData['message']['message']) && $responseData['message']['message'] == 'API gateway timeout.')){
				$errorLogResponse = array('status' => false, 'message' => 'Request Timeout – The server did not respond within the configured time limit. Please try again later. If the issue persists, contact support.', 'response' => $result);
			}else {
				if (isset($responseData['data']) && isset($responseData['data']['triggerStepFunction'])
				&& !empty($responseData['data']['triggerStepFunction'])){
					$errorLogResponse = array();
				} else if(isset($responseData['errors']) && count($responseData['errors']) > 0) {
					if(isset($responseData['errors'][0]['message'])){
						$errorResponse = $responseData['errors'][0];
						
						$errorCode = $errorResponse['extensions']['code'];
						
						$errorMessage =  $errorResponse['message'];

						if (isset($errorResponse['extensions']) && isset($errorResponse['extensions']['exception']) && 
						isset($errorResponse['extensions']['exception']['stacktrace'])) {
							$stacktrace = json_encode($errorResponse['extensions']['exception']['stacktrace']);
						}else{
							$stacktrace = null;
						}
						$errorLogResponse = array('status' => false, 'message' => $errorCode.'-'.$errorMessage, 'response' => $stacktrace);
					}else{
						$errorLogResponse = array('status' => false, 'message' => 'An error occurred while triggering the request.', 'response' => $responseData['errors']);
					}
				} else {
					$errorLogResponse = array('status' => false, 'message' => 'An unexpected error occurred while triggering the request.', 'response' => $responseData);
				}
			}
		}
	
		if($logError == 1 && count($errorLogResponse) > 0){
			if($logTable == 'externalAPISyncStatus'){
				// Convert to JSON
				$decodeVariablesJSON = json_decode($inputVariableJSON);
				$decodeInputPayload = json_decode($decodeVariablesJSON->inputPayload);

				$utcDateTime = new DateTime('now', new DateTimeZone('UTC'));
				$currentUtcDateTime = $utcDateTime->format('Y-m-d H:i:s');// 2025-03-17 10:45:00
				$failureLogInputs =  array(
					'Status' =>'Failed', 
					'Action' => $decodeInputPayload->action, 
					'Entity_Type' => json_encode($decodeInputPayload->entityType),
					'Entity_Id' => $decodeInputPayload->uniqueIds[0],
					'Form_Data' => count($decodeInputPayload->uniqueIdDetails) > 0 ? json_encode($decodeInputPayload->uniqueIdDetails) : json_encode(new stdClass()),
					'Added_On'=> $currentUtcDateTime,
					'Added_By'=> $sessionId,
					'Failure_Reason'=> json_encode($errorLogResponse),
					'Integration_Type'=> 'Syntrum'
				);
				$this->_db->insert($this->_ehrTables->externalAPISyncStatus, $failureLogInputs);
			}
		}
			
		return 'success';
	}


/**
 * Retrieves and organizes leave document details by Leave ID.
 *
 * This function retrieves all leave documents associated with the given Leave ID(s) and
 * organizes them by Leave ID. If no leave documents are found, an empty array is returned.
 *
 * @param mixed $leaveId The Leave ID(s) to filter documents by. Can be a single ID or an array of IDs.
 * @return array An array of organized leave document details by Leave ID, or an empty array if no documents are found.
 */
	public function getLeaveDocumentDetails($leaveId)
	{
		$empDocumentFiles =  $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->empLeaveDocuments,array('File_Name','File_Size','Leave_Id'))
												->where('Leave_Id IN (?)', $leaveId));
		if(!empty($empDocumentFiles))
		{
			$organizeLeaveDocumentByLeaveId  = $this->organizeDataByEmployeeIdAndDate($empDocumentFiles,'Leave_Id');
			return $organizeLeaveDocumentByLeaveId;
		}
		return $empDocumentFiles;
	}

	public function getRegularHoursAndContactDetails($employeeId)
	{
		$regularHourDetails = $this->_db->fetchRow($this->_db->select()->from(array('J'=>$this->_ehrTables->empJob),array('J.Emp_Email'))
                                                         
                                                        ->joinInner(array('P'=>$this->_ehrTables->empPersonal), 'P.Employee_Id=J.Employee_Id',array(''))
                                                        
                                                        ->joinInner(array('Dn'=>$this->_ehrTables->designation), 'Dn.Designation_Id=J.Designation_Id', array())
                                                        
                                                        ->joinInner(array('T'=>$this->_ehrTables->timesheetHrs), 'T.Grade_Id = Dn.Grade_Id',array('T.Regular_Hours'))
                                                        
                                                        ->joinInner(array('C'=>$this->_ehrTables->empContacts), 'C.Employee_Id=J.Employee_Id',array('C.Mobile_No'))
														->where('P.Form_Status = 1')
														->where('P.Employee_Id =?',$employeeId));		
		return $regularHourDetails;
	}


    /**
     * Fetches and organizes comment details by Parent ID for a given form name.
     *
     * This function retrieves all comments that are not empty or null, associated with the specified
     * Parent ID(s) and form name. It joins the comments table with the forms table to ensure the form 
     * name matches. The function then organizes the resulting comment details by Parent ID using an 
     * internal organization method.
     *
     * @param mixed $parentId The parent ID(s) to filter comments by. Can be a single ID or an array of IDs.
     * @param string $formName The name of the form to filter comments by.
     * @return array An array of organized comment details by Parent ID, or an empty array if no comments are found.
     */

	public function countCommentDetails($parentId, $formName)
    {
		$commentDetails = $this->_db->fetchAll($this->_db->select()->from(array('C'=>$this->_ehrTables->comment),array('C.Comment_Id','C.Parent_Id'))
						 	->joinInner(array('F'=>$this->_ehrTables->forms),"C.Form_Id = F.Form_Id",array())
							 ->where('C.Emp_Comment<>?', "")
							 ->where('C.Emp_Comment IS NOT NULL')
							 ->where('C.Parent_Id IN (?)', $parentId)
							 ->where('F.Form_Name = ?', $formName));

		if(!empty($commentDetails))
		{
			$organizeCommentByLeaveId  = $this->organizeDataByEmployeeIdAndDate($commentDetails,'Parent_Id');
			return $organizeCommentByLeaveId;
		}
		return $commentDetails;
    }
/**
 * Retrieves the maximum payslip month for the given employee(s).
 *
 * This function queries both the monthly and hourly payslip tables to find
 * the latest salary month for each specified employee. It combines the results 
 * from both tables and organizes the data by Employee ID.
 *
 * @param mixed $employeeId A single Employee ID or an array of Employee IDs to fetch the payslip details for.
 * @return array An associative array with Employee IDs as keys and their respective latest salary month.
 */


	public function getMaxPayslipMonthByEmployeeId($employeeId)
	{
		$monthlySalaryEmployeeDetails = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->monthlyPayslip, array('Employee_Id',new Zend_Db_Expr('MAX(STR_TO_DATE(Salary_Month,"%m,%Y")) as Salary_Month')))
													->where('Employee_Id IN (?)',$employeeId)
													->group('Employee_Id'));

		$hourlySalaryEmployeeDetails  = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->wagePayslip, array('Employee_Id',new Zend_Db_Expr('MAX(STR_TO_DATE(Salary_Month,"%m,%Y")) as Salary_Month')))
													->where('Employee_Id IN (?)',$employeeId)
													->group('Employee_Id'));
		$salaryPayslipEmployeeDetails = array();
		if(!empty($monthlySalaryEmployeeDetails) || !empty($hourlySalaryEmployeeDetails))
		{
			array_push($salaryPayslipEmployeeDetails,$monthlySalaryEmployeeDetails);
			array_push($salaryPayslipEmployeeDetails,$hourlySalaryEmployeeDetails);
			$salaryPayslipEmployeeDetails = array_reduce($salaryPayslipEmployeeDetails, 'array_merge', array()); 	
		}

		if(!empty($salaryPayslipEmployeeDetails))
		{
			$organizePayslipByEmployeeId  = $this->organizeDataByEmployeeIdAndDate($salaryPayslipEmployeeDetails,'Employee_Id');
			return $organizePayslipByEmployeeId;
		}
		return $salaryPayslipEmployeeDetails;
	}

	//Function to get the event id based on form id for the default workflow
	public function getEventId($formId){
		$eventId = $this->_db->fetchOne(
			$this->_db->select()
				->from(array('W' => $this->_ehrTables->workflows), array('W.Event_Id'))
				->join(
					array('WM' => $this->_ehrTables->workflowModule),
					'W.Workflow_Module_Id = WM.Workflow_Module_Id',
					array() // No extra columns needed from workflowModule
				)
				->where('W.Default_Workflow = ?', 1)
				->where('WM.Form_Id = ?', $formId)
			);
		return $eventId;
	}

	public function deleteWorkflowDetails($processInstanceId){
        $whereProcessInitiatedId['process_instance_id IN (?)'] = $processInstanceId;
		$this->_db->delete($this->_ehrTables->taUserTask,$whereProcessInitiatedId);
		$this->_db->delete($this->_ehrTables->taUserTaskHistory,$whereProcessInitiatedId);
		$this->_db->delete($this->_ehrTables->taProcessInstance,$whereProcessInitiatedId);
		$this->_db->delete($this->_ehrTables->taProcessInstanceHistory,$whereProcessInitiatedId);
		return 1;
	}

	public function getAdvanceNotificationMinDate($settings,$adminRole,$logEmpId,$formIds=array()){
		$minimumDateToApply = '';
		$skipAdvanceNotificationValidation=0;
		$advanceNotificationDays = 0;

		if(isset($settings['Advance_Notification']) && $settings['Advance_Notification'] == 'Yes'){
			if(!empty($formIds)){
				$formAdminRole = '';
				foreach ($formIds as $formId) {
					$accessRights = $this->_dbAccessRights->employeeAccessRightsBasedOnFormId($logEmpId, $formId);
					
					if($accessRights['Admin']=="admin") {
						$formAdminRole = "admin";
						break;
					}
				}
				$adminRole = $formAdminRole;
			}
			if($adminRole == "admin") {
				$skipAdvanceNotificationValidation=1;
			}
			
			if((int)$skipAdvanceNotificationValidation === 0){
				$advanceNotificationDays = $settings['Advance_Notification_Days'];

				// If 'Include_Current_Date_In_Advance_Notification' is set to 'Yes', subtract one day from the advance notification days to allow applying from the current date.
				if ($settings['Include_Current_Date_In_Advance_Notification'] == 'Yes') {
					$advanceNotificationDays--;
				}

				$minimumDateToApply = date('Y-m-d', strtotime("+" . $advanceNotificationDays . " days"));
			}
		}
		return array('minimumDateToApply' => $minimumDateToApply);
	}

	public function getPayslipComponentName($organizeData=NULL)
    {
        $payslipComponentDetails = $this->_db->fetchAll($this->_db->select()->from(array('EJ'=>$this->_ehrTables->payslipComponentName), array('*')));
        if($organizeData=='Yes')
		{
			$organizePayslipComponentDetails = $this->organizeDataByEmployeeIdAndDate($payslipComponentDetails,'Form_Id');
            unset($payslipComponentDetails);
            return $organizePayslipComponentDetails;
		}
        return $payslipComponentDetails; 
    }

	public function __destruct()
    {
        
    }
}
