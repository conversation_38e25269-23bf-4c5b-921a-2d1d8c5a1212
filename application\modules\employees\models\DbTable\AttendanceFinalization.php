<?php
//===========================================================================================
//===========================================================================================
/* Program : AttendanceFinalization.php											   			           *
 * Property of Caprice Technologies Pvt Ltd,                                               *
* Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                         *
* Coimbatore, Tamilnadu, India.														       *
* All Rights Reserved.            														   *
* Use of this material without the express consent of Caprice Technologies                 *
* or assignees is unlawful and subject to prosecution to the fullest extent of the law.    * 
*                                                                                    	   *
* Description : MQL Query to retrive the attendance details which are not approved,        *
* calculate and update checkout time based on the workschedule endtime if the attendance   *
* record is in draft status and approve that record, approve the applied records           *
* Retrieve the no attendance data, list LOP leave types, apply auto LOP                    *
*                                                                                   	   *
*                                                                                    	   *
* Revisions :                                                                    	       *
*  Version    Date           Author                  Description                           *
*  0.1        26-Dec-2019    Shanthi            	  Initial Version        	           *
*  0.2        27-Dec-2019    Shanthi                  listAttendanceFinalization           *
*                                                                                          */
//===========================================================================================
//===========================================================================================

class Employees_Model_DbTable_AttendanceFinalization extends Zend_Db_Table_Abstract
{

    protected $_db          = null;
	protected $_ehrTables   = null;
    protected $_orgDF       = null;
    protected $_dbCommonFunction = null;
    protected $_dbLeaves = null;
    protected $_dbAttendance = null;
    
    public function init()
	{
		$this->_db          = Zend_Registry::get('subHrapp');
        $this->_ehrTables   = new Application_Model_DbTable_Ehr();
        $this->_dbCommonFunction = new Application_Model_DbTable_CommonFunction();
        $this->_orgDF       = $this->_ehrTables->orgDateformat();
		$this->_dbLeaves = new Employees_Model_DbTable_Leave();
        $this->_dbAttendance = new Employees_Model_DbTable_Attendance();
		if (Zend_Registry::isRegistered('orgDetails'))
             $this->_orgDetails = Zend_Registry::get('orgDetails');
	}

    /* function to retrieve the attendance records which are Draft and Applied status*/
	public function listAttendanceFinalization($filterArray, $finalizationMethod,$logEmpId,$attendanceFinalizationAccess)
	{ 

		/* Filter Data */
		$startDate        	= $filterArray['startDate'];
		$endDate        	= $filterArray['endDate'];
		$status			  	= $filterArray['status'];
		$employeeType    	= $filterArray['employeeType'];
		$location 			= $filterArray['location'];
		$department   		= $filterArray['department'];

		$employeeDetails     = $this->_dbAttendance->getEmployeeAttendanceDetails($logEmpId);
		
		if($finalizationMethod=='dashboardAttendance' && $this->_orgDetails['Multiple_Source_Attendance_Same_Time']=='Yes' && !empty($employeeDetails) && !empty($employeeDetails['External_EmpId']))
		{

			$qryAttendance = $this->_db->select()->from(array('AI'=>$this->_ehrTables->attendanceImport),array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS AI.Attendance_Id as count'),'AI.Attendance_Id','AI.Added_On','EJ.Employee_Id','AI.Attendance_Status'))
								->joinInner(array('EJ'=>$this->_ehrTables->empJob),'AI.Employee_Id=EJ.External_EmpId',array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN EJ.Employee_Id ELSE EJ.User_Defined_EmpId END'))) 
								->joinInner(array('EP'=>$this->_ehrTables->empPersonal),'EP.Employee_Id=EJ.Employee_Id', 
										array(new Zend_Db_Expr("CONCAT(EP.Emp_First_Name, ' ', EP.Emp_Last_Name) as Employee_Name"))) 
								->joinInner(array('ET'=>$this->_ehrTables->empType), 'EJ.EmpType_Id=ET.EmpType_Id', array('Display_Total_Hours_In_Minutes')) 
								->joinInner(array('D'=>$this->_ehrTables->dept),'EJ.Department_Id = D.Department_Id',array(''))
								->joinInner(array('L'=>$this->_ehrTables->location),'EJ.Location_Id=L.Location_Id',array(''))
								->where('AI.Rollup_Flag != ?',1)
								->order('AI.Attendance_Status ASC','AI.Added_On DESC');

			/* Filter for attendance punchIn start Date */
			if ($startDate != '')
			{
				$qryAttendance->where($this->_db->quoteInto('Date_Format(AI.Added_On, "%Y-%m-%d") >= ?', $startDate));
			}

			/* Filter for attendance punchIn end Date */
			if ($endDate != '')
			{	
				$qryAttendance->where($this->_db->quoteInto('Date_Format(AI.Added_On, "%Y-%m-%d") <= ?', $endDate));
			}

			/* Filter for attendance status */	
			// if (!empty($status))
			// {
			// 	$qryAttendance->where('att.Approval_Status IN (?)', $status);
			// }

			//Filter for Employee Type
			if(!empty($employeeType))
			{ 
				$qryAttendance->where('ET.EmpType_Id IN (?)', $employeeType);
			}

			//Filter for Department  
			if(!empty($department))
			{
				$qryAttendance->where('D.Department_Id IN (?)', $department);
			}
			//Filter for Location 
			if (!empty($location))
			{
				$qryAttendance->where('L.Location_Id IN (?)', $location);
			}

			//Filter by employee id when the attendance finalization is triggered from salary payslip generation
			if (!empty($filterArray['payslipEmployeeIds']))
			{
				$qryAttendance->where('EJ.Employee_Id IN (?)', $filterArray['payslipEmployeeIds']);
			}

			$attendance = $this->_db->fetchAll($qryAttendance);
			/* Data set length after filtering */ 
			$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()'); 	
			
			$responseArray = $attendanceInRecords = array();

			if(!empty($attendance))
			{
				$employeeIds = array_unique(array_column($attendance, 'Employee_Id'));
				$workScheduleAndShiftRosterDetails = $this->_dbCommonFunction->getWorkScheduleAndShiftRosterDetails($employeeIds,$startDate,$endDate);
				foreach($attendance as $key => $attendanceRecord) { 
					$employeeId          = $attendanceRecord['Employee_Id'];
					$startDate           = date("Y-m-d",strtotime($attendanceRecord['Added_On']));
					$workScheduleDetails = $this->_dbCommonFunction->getWorkScheduleDetailsByDate($employeeId,$startDate,$workScheduleAndShiftRosterDetails);
					if(!empty($workScheduleDetails))
					{
						$attendanceRecord['Consideration_From']    = $workScheduleDetails['Consideration_From'];
						$attendanceRecord['Consideration_To']      = $workScheduleDetails['Consideration_To'];
						$attendanceRecord['Regular_From']          = $workScheduleDetails['Regular_From'];
						$attendanceRecord['Regular_To']            = $workScheduleDetails['Regular_To'];
						$attendanceRecord['Work_Schedule_Date']    = $workScheduleDetails['Work_Schedule_Date'];
						$addedOn                                   = $attendanceRecord['Added_On'];
						$attendanceStatus                          = $attendanceRecord['Attendance_Status'];
						if ($workScheduleDetails['Consideration_From'] <= $addedOn && $workScheduleDetails['Consideration_To'] >= $addedOn) {
							// Create a unique key for each combination of work_schedule_date and employee_Id
							$key = $employeeId . '_' . $attendanceRecord['Work_Schedule_Date'];
				
							if ($attendanceStatus == 'C/In') {
								if (!isset($attendanceInRecords[$key]) || $addedOn < $attendanceInRecords[$key]['Added_On']) {
									$attendanceInRecords[$key] = $attendanceRecord;
								} 
							}

							if($attendanceStatus == 'C/Out') {
								// Remove the corresponding C/In record from the attendanceInRecords array
								unset($attendanceInRecords[$key]);
							}
						}
					}
				}

				
				foreach($attendanceInRecords as $key => $attendanceRecord) 
				{
					$validRecord = 0;
					/* Should not list the Draft record if the Consideration To is less than the current time */
					
					if($attendanceRecord['Consideration_To'] < date("Y-m-d H:i:s")){
						$validRecord = 1;
					}
					

					if($validRecord) {
						$punchInDateYMD       = date("Y-m-d",strtotime($attendanceRecord['Added_On']));
						$punchInDateOrgFormat = date($this->_orgDF['php'], strtotime($attendanceRecord['Added_On']));
						/* the datatable sorting, pagination and search will work only if the keys are in lowercase */
						$attendanceResponseData = Array ('count' => $attendanceRecord['count'],
						'attendance_id' 	=> 0,
						'total_hours' 		=> 0,
						'employee_id' 		=> $attendanceRecord['Employee_Id'],
						'approval_status' 	=> 'Draft',
						'punchin_date' 		=> $punchInDateOrgFormat,
						'punchin_date_ymd'  => $punchInDateYMD,
						'punchout_date' 	=> NULL,
						'punchin_time' 		=> date("H:i:s",strtotime($attendanceRecord['Added_On'])),
						'total_hours1' 		=> 0,
						'punchout_time'		=> NULL,
						'employee_name' 	=> $attendanceRecord['Employee_Name'],
						'user_defined_empid' => $attendanceRecord['User_Defined_EmpId'],
						'consideration_from' => $attendanceRecord['Consideration_From'],
						'consideration_to'  => $attendanceRecord['Consideration_To'],
						'regular_to'  => $attendanceRecord['Regular_To'],
						'regular_form'  => $attendanceRecord['Work_Schedule_Date'],
						'display_total_hours_in_minutes' => $attendanceRecord['Display_Total_Hours_In_Minutes']);
					
						array_push($responseArray,$attendanceResponseData);
					} 
				}
			}
		}
		else
		{
			$qryAttendance = $this->_db->select()->from(array('att'=>$this->_ehrTables->attendance), 
													array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS att.Attendance_Id as count'),'att.Attendance_Id','att.Total_Hours','att.Employee_Id', 'att.Approval_Status',
														new Zend_Db_Expr("DATE_FORMAT(att.PunchIn_Date,'".$this->_orgDF['sql']."') as PunchIn_Date"), 'att.PunchIn_Date as PunchIn_Date_YMD',
														new Zend_Db_Expr("DATE_FORMAT(att.PunchOut_Date,'".$this->_orgDF['sql']."') as PunchOut_Date"), 
														new Zend_Db_Expr("DATE_FORMAT(att.PunchIn_Time,'%H:%i:%s') as PunchIn_Time"), 
														'Total_Hours1'=>new Zend_Db_Expr("CASE WHEN ET.Display_Total_Hours_In_Minutes='1' Then ( CONCAT(FLOOR(att.Total_Hours),':', LPAD(ROUND((att.Total_Hours - FLOOR(att.Total_Hours)) * 60) % 60,2,0)) ) else att.Total_Hours END  "), 
														new Zend_Db_Expr("DATE_FORMAT(att.PunchOut_Time,'%H:%i:%s') as PunchOut_Time"))) 
														  
							->joinLeft(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=att.Employee_Id', 
									   array(new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as Employee_Name"))) 
							 
						   ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'emp.Employee_Id=EJ.Employee_Id', 
                                            array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN emp.Employee_Id ELSE EJ.User_Defined_EmpId END'))) 
											
							->joinLeft(array('ET'=>$this->_ehrTables->empType), 'EJ.EmpType_Id=ET.EmpType_Id', array('Display_Total_Hours_In_Minutes')) 

							->joinInner(array('D'=>$this->_ehrTables->dept),'EJ.Department_Id = D.Department_Id',
                                            array('D.Department_Name'))

                        	->joinInner(array('L'=>$this->_ehrTables->location),'EJ.Location_Id=L.Location_Id',
											array('L.Location_Name'))
							->joinInner(array('Des'=>$this->_ehrTables->designation),'EJ.Designation_Id = Des.Designation_Id',array('Des.Designation_Name'))
							->order('att.Attendance_Date DESC');

			/* Filter for attendance punchIn start Date */
			if ($startDate != '')
			{
				$qryAttendance->where($this->_db->quoteInto('att.Attendance_Date >= ?', $startDate));
			}

			/* Filter for attendance punchIn end Date */
			if ($endDate != '')
			{	
				$qryAttendance->where($this->_db->quoteInto('att.Attendance_Date <= ?', $endDate));
			}

			/* Filter for attendance status */	
			if (!empty($status))
			{
				$qryAttendance->where('att.Approval_Status IN (?)', $status);
			}

			//Filter for Employee Type
			if(!empty($employeeType))
			{ 
				$qryAttendance->where('ET.EmpType_Id IN (?)', $employeeType);
			}

			//Filter for Department  
			if(!empty($department))
			{
				$qryAttendance->where('D.Department_Id IN (?)', $department);
			}
			//Filter for Location 
			if (!empty($location))
			{
				$qryAttendance->where('L.Location_Id IN (?)', $location);
			}

			//Filter by employee id when the attendance finalization is triggered from salary payslip generation
			if (!empty($filterArray['payslipEmployeeIds']))
			{
				$qryAttendance->where('att.Employee_Id IN (?)', $filterArray['payslipEmployeeIds']);
			}

			if (isset($filterArray['serviceProviderId'])&&!empty($filterArray['serviceProviderId']))
			{
				$qryAttendance->where('EJ.Service_Provider_Id = ?', $filterArray['serviceProviderId']);
			}

			if($finalizationMethod=='attendance'){
				if(empty($attendanceFinalizationAccess['Admin']))
				{
					$getEmployeeId = $this->_db->fetchCol($this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))
														->where('Manager_Id = ?', $logEmpId));
					if ($attendanceFinalizationAccess['Is_Manager'] == 1 && !empty($getEmployeeId))
					{
						if($this->_orgDetails['Immediate_Reportees_View_Only']==0)
						{
							$getEmployeeId = $this->_dbCommonFunction->getMultiLevelManagerIds($logEmpId,1);
							array_push($getEmployeeId,$employeeId);
							$qryAttendance->where('EJ.Employee_Id IN (?)', $getEmployeeId);
						}
						else
						{
							$qryAttendance->where('EJ.Employee_Id IN (?)', $getEmployeeId);
						}
					}
					else
					{
						$qryAttendance->where('EJ.Employee_Id = ?', $logEmpId);
					}
				}

				if(!empty($attendanceFinalizationAccess['Admin']))
				{
					$qryAttendance = $this->_dbCommonFunction->formServiceProviderQuery($qryAttendance,'EJ.Service_Provider_Id',$logEmpId);
				}
			}

			$attendance = $this->_db->fetchAll($qryAttendance);
			/* Data set length after filtering */ 
			$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');  							

			$responseArray = array();

			foreach($attendance as $key => $attendanceRecord) { 
				$validRecord = 0;
				$workScheduleDetails = array();
				/* Should not list the Draft record if the Consideration To is less than the current time */
				if($attendanceRecord['Approval_Status'] === 'Draft'){
					$checkinDateTime = date("Y-m-d H:i:s",strtotime($attendanceRecord['PunchIn_Date_YMD']." ".$attendanceRecord['PunchIn_Time']));
					$workScheduleDetails = $this->_dbAttendance->getCurrentWorkScheduleDetails($attendanceRecord['Employee_Id'],$checkinDateTime);
					if(!empty($workScheduleDetails) && $workScheduleDetails['Consideration_To'] < date("Y-m-d H:i:s")){
						$validRecord = 1;
					}
				} else {
					$validRecord = 1;
				}

				if($validRecord) {				
					/* the datatable sorting, pagination and search will work only if the keys are in lowercase */
					$attendanceResponseData = Array ('count' => $attendanceRecord['count'],
					'attendance_id' 	=> $attendanceRecord['Attendance_Id'],
					'total_hours' 		=> $attendanceRecord['Total_Hours'],
					'employee_id' 		=> $attendanceRecord['Employee_Id'],
					'approval_status' 	=> $attendanceRecord['Approval_Status'],
					'punchin_date' 		=> $attendanceRecord['PunchIn_Date'],
					'punchin_date_ymd'  => $attendanceRecord['PunchIn_Date_YMD'],
					'punchout_date' 	=> $attendanceRecord['PunchOut_Date'],
					'punchin_time' 		=> $attendanceRecord['PunchIn_Time'],
					'total_hours1' 		=> $attendanceRecord['Total_Hours1'],
					'punchout_time'		=> $attendanceRecord['PunchOut_Time'],
					'employee_name' 	=> $attendanceRecord['Employee_Name'],
					'user_defined_empid' => $attendanceRecord['User_Defined_EmpId'],
					'consideration_from' => (isset($workScheduleDetails['Consideration_From']) && $workScheduleDetails['Consideration_From']) ? $workScheduleDetails['Consideration_From']: '',
					'consideration_to'  => (isset($workScheduleDetails['Consideration_To']) && $workScheduleDetails['Consideration_To']) ? $workScheduleDetails['Consideration_To']: '',
					'regular_to'  => (isset($workScheduleDetails['Regular_To']) && $workScheduleDetails['Regular_To']) ? $workScheduleDetails['Regular_To']: '',
					'regular_form'  => (isset($workScheduleDetails['Regular_From']) && $workScheduleDetails['Regular_From']) ? date("Y-m-d",strtotime($workScheduleDetails['Regular_From'])): '',
					'display_total_hours_in_minutes' => $attendanceRecord['Display_Total_Hours_In_Minutes'],
					'designation_name'=>$attendanceRecord['Designation_Name'],
					'location_name'=>$attendanceRecord['Location_Name'],
					'department_name'=>$attendanceRecord['Department_Name']);
				
					array_push($responseArray,$attendanceResponseData);
				}
			}
		}

        $attendanceEnforcedPayment = 'No';
		if($finalizationMethod === 'attendance')
		{
			$filterArray['employeeStatus'] = array('Active','InActive');
			$finalizationMethod = 'noAttendance';
			/* Fetch employee details by filtering the given location, department, employee type, employee status and the payslip employee ids */
			$employeeDetails = $this->_dbCommonFunction->listEmployeesDetails($finalizationMethod, '', $logEmpId, '', null,$filterArray);

			if(count($employeeDetails) > 0){
				$dbPayslip  = new Payroll_Model_DbTable_Payslip();
				// we are getting the absent employee details at one time
				$attendanceEnforcedEmployeeId = array_column($employeeDetails,'Employee_Id');
				$attendanceEnforcedEmployeeDetails = $dbPayslip->getAttendanceEnforcePaymentEnabledEmployeeId($attendanceEnforcedEmployeeId,$startDate,$endDate,$finalizationMethod);
				if(count($attendanceEnforcedEmployeeDetails)>0)
				{
					$attendanceEnforcedPayment = 'Yes';
				}
			}
		}

        /** 
		 * Output array 
		*/ 
		return array("iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $responseArray,'attendanceEnforcedPayment'=>$attendanceEnforcedPayment); 
 
    }

    
    /* Function to validate the attendance records before attendance closure */
    public function validateAttendanceFinalization($attendanceId){
        
        /* If we return the valid attendance data, that will be used to initiate the auto closure and approval process  */
        $validAttendanceIds = array();

        /* Will be used to Auto close the draft records */
        $validRecords = array();

        /* We should present the invalid attendance deatails to the user, so push the invalid data in the array */
		$invalidRecords = array();
		$invalidReason = array();
        
        $getAttendanceData = $this->_db->fetchAll($this->_db->select()->from(array('att'=>$this->_ehrTables->attendance), 
								array('att.Attendance_Id', 'att.Employee_Id', 'att.Added_By', 'att.PunchIn_Date', 
								'att.Actual_Punch_In_Time', 'att.PunchIn_Time', 'att.Approval_Status'))
                            ->joinInner(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=att.Employee_Id', 
                                array(new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as Employee_Name"))) 
                            ->where('att.Attendance_Id IN (?)', $attendanceId));
                            
        foreach ($getAttendanceData as $key => $attendanceRecord) {
            $isValid    = 0;
			$reason     = '';
			$workScheduleconsiderationFromTime = '';
            $workScheduleEndTime = '';
            $workScheduleDetails = array();
            $workScheduleconsiderationFromDate = '';
            $workScheduleconsiderationFromTime = '';
            $workScheduleEndDate = '';
            $workScheduleEndTime = '';

            $checkinDateTime = date("Y-m-d H:i:s",strtotime($attendanceRecord['PunchIn_Date']." ".$attendanceRecord['PunchIn_Time']));
			
			$isEmpFinalSettlementInitiated = $this->_dbCommonFunction->empFinalSettlementInitiated($attendanceRecord['Employee_Id']);

			if($isEmpFinalSettlementInitiated == 1){
				$reason = 'Attendance record(s) cannot be added or updated as the full and final settlement is initiated or settled for the employee.Kindly delete the F & F settlement in order to make the necessary modifications.';
				if(!in_array($reason, $invalidReason)){
					array_push($invalidReason,$reason);
				}
			}else{
				if($attendanceRecord['Approval_Status'] == 'Draft' || $attendanceRecord['Approval_Status'] == 'Applied'){
					
					/** Get the workschedule details for the checkin date */
					$workScheduleDetails = $this->_dbAttendance->getCurrentWorkScheduleDetails($attendanceRecord['Employee_Id'],$checkinDateTime);
					
					if(!empty($workScheduleDetails)){
						/** If the attendance record is in Draft status then we need to validate that record.
						 * Consideration_To time should be less than the current time.
						 * Checkin time should be greater than the Consideration_From and less than the Workschedule end time. 
						 * The leave/Comp off is finalised for the employee for the attendance date(workschedule start date). 
						 * Otherwise we will not process the record. User should update it mannualy 
						 */
						if($attendanceRecord['Approval_Status'] == 'Draft') {
							if($workScheduleDetails['Consideration_To'] > date("Y-m-d H:i:s")){
								$reason = 'The Attendance record is added for the current date.';
								if(!in_array($reason, $invalidReason)){
									array_push($invalidReason,$reason);
								}
							}
							else if($checkinDateTime >= $workScheduleDetails['Consideration_From'] && $checkinDateTime < $workScheduleDetails['Regular_To']){
								$isValid = 1;
								/* Consideration From Time and Workschedule end time will be used in Auto closure */
								$workScheduleconsiderationFromDate = explode(" ",$workScheduleDetails['Consideration_From'])[0];
								$workScheduleconsiderationFromTime = explode(" ",$workScheduleDetails['Consideration_From'])[1];
								$workScheduleEndDate = explode(" ",$workScheduleDetails['Regular_To'])[0];
								$workScheduleEndTime = explode(" ",$workScheduleDetails['Regular_To'])[1];
							}  
							else {
								$reason = 'Checkin time is greater than the work schedule end time.';
								if(!in_array($reason, $invalidReason)){
									array_push($invalidReason,$reason);
								}
							}

							/* Check the leave/comp off is finalised for the employee for the attendance date(workschedule start date) */
							$isLeavesCompOffOpen = $this->validateAttendanceFinalizationPrereq(array($attendanceRecord['Employee_Id']),explode(" ",$workScheduleDetails['Regular_From'])[0],explode(" ",$workScheduleDetails['Regular_From'])[0],'attendance');
								
							if($isLeavesCompOffOpen){
								$isValid = 0;
								$reason = 'Leaves or compensatory off are not finalised for the day.';
								if(!in_array($reason, $invalidReason)){
									array_push($invalidReason,$reason);
								}
							}
						} else {
							$isValid = 1;
							/* Consideration From Time and Workschedule end time will be used in Auto closure */
							$workScheduleconsiderationFromDate = explode(" ",$workScheduleDetails['Consideration_From'])[0];
							$workScheduleconsiderationFromTime = explode(" ",$workScheduleDetails['Consideration_From'])[1];
							$workScheduleEndDate = explode(" ",$workScheduleDetails['Regular_To'])[0];
							$workScheduleEndTime = explode(" ",$workScheduleDetails['Regular_To'])[1];
						}
					} else {
						$reason = 'Work schedule is not present or configured.';
						if(!in_array($reason, $invalidReason)){
							array_push($invalidReason,$reason);
						}
					}
				}
				else {
					$reason = 'Selected Attendance record(s) are already closed.';
					if(!in_array($reason, $invalidReason)){
						array_push($invalidReason,$reason);
					}
				}
			}
			
            /* If the record is valid, then push the attendance id in the validRecords array, 
			 * else push the details in the invalidRecords array */
			if($isValid == 1) {
				
                array_push($validAttendanceIds,$attendanceRecord['Attendance_Id']); 

                $validDraftRecord = array('Attendance_Id'  => $attendanceRecord['Attendance_Id'],
									'Employee_Id'              => $attendanceRecord['Employee_Id'],
									'Employee_Name'            => $attendanceRecord['Employee_Name'],
                                    'Added_By'                 => $attendanceRecord['Added_By'],
                                    'PunchIn_Date_Time'        => $checkinDateTime,
                                    'Workschedule_Data'        => $workScheduleDetails,
                                    'Consideration_From_Date'  => $workScheduleconsiderationFromDate,
                                    'Consideration_From_Time'  => $workScheduleconsiderationFromTime,
                                    'PunchIn_Date'             => $attendanceRecord['PunchIn_Date'],
									'PunchIn_Time'             => $attendanceRecord['PunchIn_Time'],
									'Actual_Punch_In_Time' 	   => $attendanceRecord['Actual_Punch_In_Time'],
                                    'Workschedule_End_Date'    => $workScheduleEndDate,
                                    'Workschedule_End_Time'    => $workScheduleEndTime,
                                    'Approval_Status'          => $attendanceRecord['Approval_Status']); 
                    
                array_push($validRecords,$validDraftRecord);
            } else {
                $invalidRecord = array('Employee_Id'    => $attendanceRecord['Employee_Id'],
                                        'Employee_Name' => $attendanceRecord['Employee_Name'],
                                        'PunchIn_Date'  => date($this->_orgDF['php'], strtotime($attendanceRecord['PunchIn_Date'])),
                                        'PunchIn_Time'  => $attendanceRecord['PunchIn_Time'],
                                        'Reason'        => $reason);
                                  
                array_push($invalidRecords,$invalidRecord);
            }
        }

        return array('validAttendanceIds'=>$validAttendanceIds, 'invalidRecords'=>$invalidRecords, 'validRecords'=>$validRecords, 'invalidReason'=>$invalidReason);
	}
	
    /* Get employee absent details for a given date range */
    public function listNoAttendance($filterArray,$logEmpId, $finalizationMethod, $actualSubTab=''){
        $startDate = $filterArray['startDate'];
        $endDate = $filterArray['endDate'];
        $isLeavesCompOffAttendanceOpen = 0;
		$attendanceEnforcedPayment = 'No';
		$filterArray['employeeStatus'] = array('Active','InActive');
		
		if($finalizationMethod === 'noAttendance')
		{
			/* Fetch employee details by filtering the given location, department, employee type, employee status and the payslip employee ids */
			$employeeDetails = $this->_dbCommonFunction->listEmployeesDetails($finalizationMethod, '', $logEmpId, '', null,$filterArray);
		}
		else
		{
			/** For the finalization method dashboard no attendance, we need to list only the login employee id no attendance records. In this
			 * case, $filterArray['payslipEmployeeIds'] & logEmpId should be same for the dashboard no attendance
			 */
			$employeeDetails = array();
			$default = array('Employee_Id'=>$logEmpId);
			array_push($employeeDetails,$default);
		}

        /* If employees exists */
        if(count($employeeDetails) > 0){
            $dbPayslip  = new Payroll_Model_DbTable_Payslip();

            $resultAllEmpAbsentDetails = array();

			$recordIndex = 0; //initialize the variable to return the index(unique number) for the absent records

			// we are getting the absent employee details at one time
			$attendanceEnforcedEmployeeId = array_column($employeeDetails,'Employee_Id');
			$attendanceEnforcedEmployeeDetails = $dbPayslip->getAttendanceEnforcePaymentEnabledEmployeeId($attendanceEnforcedEmployeeId,$startDate,$endDate,$finalizationMethod);
			if(count($attendanceEnforcedEmployeeDetails)>0)
			{
				$empAbsentDetails = $dbPayslip->getAbsentEmployeeDetails($attendanceEnforcedEmployeeDetails,$startDate,$endDate,$finalizationMethod,$actualSubTab);
				$empAbsentDetails = array_reduce($empAbsentDetails, 'array_merge', array());
				foreach($employeeDetails as $absentInitEmployeeDetails){
					$empProfilePicUrl = '';
					foreach($empAbsentDetails as $empAbsentDates){
						if($empAbsentDates['Employee_Id'] == $absentInitEmployeeDetails['Employee_Id'])
						{
							$resultEmpDetails = array();
							if($finalizationMethod === 'noAttendance')
							{
								
								$resultEmpDetails = array( 'employee_name' => $absentInitEmployeeDetails['Employee_Name'],
															'user_defined_empid' => $absentInitEmployeeDetails['User_Defined_EmpId'],
															'contact_details' => $absentInitEmployeeDetails['Mobile_No'],
															'hours' => $absentInitEmployeeDetails['Regular_Hours'],
															'is_manager' => $absentInitEmployeeDetails['Is_Manager'],
															'department_name' => $absentInitEmployeeDetails['Department_Name'],
															'location_name' => $absentInitEmployeeDetails['Location_Name'],
															'designation_name' => $absentInitEmployeeDetails['Designation_Name']);
							}
							// append the employee details to all the absent dates list and push it an result array
							$empAbsentDates = array_change_key_case($empAbsentDates,CASE_LOWER);

							//Convert the absent date to the organization date format
							$displayAbsentDate = date($this->_orgDF['php'], strtotime($empAbsentDates['absent_date']));

							$empAbsentDates['d_absent_date'] = $displayAbsentDate;

							$recordIndex += 1;
							$empAbsentDates['absent_rec_index'] = $recordIndex;
							
							$resultEmpAbsentDetails = array_merge($empAbsentDates, $resultEmpDetails);
							array_push($resultAllEmpAbsentDetails,$resultEmpAbsentDetails);
						}
					}
				}
				$attendanceEnforcedPayment = 'Yes';
			}

			$resultAllEmpAbsentDetailsCount = count($resultAllEmpAbsentDetails);
			
			if($finalizationMethod === 'noAttendance')
			{
				/* If the absent record exist in the given range */
				if(!empty($resultAllEmpAbsentDetails) && count($resultAllEmpAbsentDetails)>0){
					$uniqueEmployeeIds = array_unique(array_column($resultAllEmpAbsentDetails, 'employee_id'));
					
					if(!empty($uniqueEmployeeIds)){
						/* Validate if attendance/comp off/leave are in pending approval for the employees */
						$isLeavesCompOffAttendanceOpen = $this->validateAttendanceFinalizationPrereq($uniqueEmployeeIds,$startDate,$endDate,'noAttendance');
					}
				}
			}

            return array("iTotalDisplayRecords" => $resultAllEmpAbsentDetailsCount, 'isLeavesCompOffAttendanceOpen'=>$isLeavesCompOffAttendanceOpen,"aaData" => $resultAllEmpAbsentDetails,'attendanceEnforcedPayment'=>$attendanceEnforcedPayment);
        }else{
            return array("iTotalDisplayRecords" => 0, 'isLeavesCompOffAttendanceOpen'=>$isLeavesCompOffAttendanceOpen, "aaData" => array(),'attendanceEnforcedPayment'=>$attendanceEnforcedPayment);
        }
    }

	public function getUnpaidLeaves()
    {
		return $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->leavetype, array('LeaveType_Id', 'Leave_Name'))
														->where('Leave_Enforcement_Configuration = ?', 1)
                                                        ->where('Leave_Type = ?', "Unpaid Leave")
														->where('Leave_Closure_Based_On = ?', "Selected Month")
                                                        ->where('Leave_Status = ?', "Active"));
    }

	/* Function to calculate the checkout time and approve the draft attendance record and approve the applied attendance record*/
    public function initiateAutoClosureAndApproval($attendanceId, $logEmpId, $formName, $customFormName)
    {
        /* Validate the attendance records before initiating the auto closure */
        $validateAttendanceData = $this->validateAttendanceFinalization($attendanceId);
		$dbHrReports = new Reports_Model_DbTable_HrReports();
		$dbWorkSchedule = new Organization_Model_DbTable_WorkSchedule();

        if(!empty($validateAttendanceData)){
			$validatedResult = array(
				'totalRecords'=>count($attendanceId),
				'validAttendanceIds'=>$validateAttendanceData['validAttendanceIds'],
				'invalidRecords'=>$validateAttendanceData['invalidRecords']);

            if(!empty($validateAttendanceData['validRecords'])){
                /** Check if the attendance record exists between the checkin time and Workschedule end time.
                 * If exists, then get the checkin time of the next record and minus 1 minute.
                 */
				foreach ($validateAttendanceData['validRecords'] as $key => $attendanceRecord) {
					$updated = 0;
                    if($attendanceRecord['Approval_Status'] == 'Draft') {
                        $maxCheckOutDate = $attendanceRecord['Workschedule_End_Date'];
                        $maxCheckOutTime = $attendanceRecord['Workschedule_End_Time'];
						$workScheduleDetails = $attendanceRecord['Workschedule_Data'];
						$punchDateTime = date('Y-m-d H:i',strtotime($attendanceRecord['PunchIn_Date']." ".$attendanceRecord['PunchIn_Time']));
						$workScheduleEndDateTime = date('Y-m-d H:i',strtotime($workScheduleDetails['Regular_To']));
						
						$nextCheckinRecord = $this->_db->fetchOne($this->_db->select()->from(array($this->_ehrTables->attendance), 
												array('Next_PunchIn_Date_Time'=>new Zend_Db_Expr('MIN(Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %T")) ')))
												->where('Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %H-%i") > ?', $punchDateTime )
												->where('Date_Format(Concat(PunchIn_Date," ",PunchIn_Time), "%Y-%m-%d %H-%i") < ?', $workScheduleEndDateTime )
												->where('Employee_Id = ?',$attendanceRecord['Employee_Id']));
												
						if(!empty($nextCheckinRecord)){
							$nextPunchinDate = explode(" ",$nextCheckinRecord)[0];
							$nextPunchinTime = explode(" ",$nextCheckinRecord)[1];

							$maxCheckOutDateTime = date("Y-m-d H:i:s", strtotime("-1 minutes", strtotime($nextCheckinRecord)));

                            $maxCheckOutDate = explode(" ",$maxCheckOutDateTime)[0];
                            $maxCheckOutTime = explode(" ",$maxCheckOutDateTime)[1];
						}
						
                        /** leave can't be added for full day if the checkin exists.
                         * If the leave exists for first then the maxCheckout time will be updated as Checkout time so we do not consider the first half leave.
                         * if the leave record exists for second half, 
                         * then the we need to compare maxCheckout time or (workschedule time divided by 2 + workschedule start time). The smallest time will be updated as checkout time
                         */
                        $workscheduleStartDate = explode(" ",$workScheduleDetails['Regular_From'])[0];
						$allLeaveDetails = $dbHrReports-> getLeaveDuration($attendanceRecord['Employee_Id'],$workscheduleStartDate,NULL,NULL,NULL,NULL,'fetchAll');
						if (!empty($allLeaveDetails) && count($allLeaveDetails) > 0) {
							foreach ($allLeaveDetails as $leaveDetail) {
								if ($leaveDetail['Duration'] == 0.5 && $leaveDetail['Leave_Period'] == 'Second Half') {
									$secondHalfLeaveExist = 1;
								} else if ($leaveDetail['Duration'] == 0.25 && $leaveDetail['Leave_Period'] == 'Third Quarter') {
									$thirdQuarterLeaveExist = 1;
								} else if ($leaveDetail['Duration'] == 0.25 && $leaveDetail['Leave_Period'] == 'Fourth Quarter') {
									$fourthQuarterLeaveExist = 1;
								}
							}
						}
						$secondHalfCompensatoryExist = $dbHrReports->getCompensatoryOff($attendanceRecord['Employee_Id'],$workscheduleStartDate,0.5,'Second Half');
						
                        if(!empty($secondHalfLeaveExist) || !empty($secondHalfCompensatoryExist) || !empty($thirdQuarterLeaveExist) || !empty($fourthQuarterfLeaveExist)){
                            $workScheduleStartsAt = new DateTime($workScheduleDetails['Regular_From']);
                            $workScheduleEndsAt = new DateTime($workScheduleDetails['Regular_To']);
                            /* Hours difference between workshedule start and end time */
                            $interval = $workScheduleEndsAt->diff($workScheduleStartsAt);
                            $diffrenceInMins = $interval->format('%h')*60;
                            $halfDayMins = $diffrenceInMins / 2;
                            /* Workschedule start time + Workschedule hours for half day*/
                            $workscheduleEndDateTimeHalfDay = date_modify($workScheduleStartsAt, "+".$halfDayMins." minutes");
                            $halfDayWorkscheduleEndDateTime = $workscheduleEndDateTimeHalfDay->format('Y-m-d H:i:s');
							
                            if(new DateTime($maxCheckOutDate." ".$maxCheckOutTime) > new DateTime($halfDayWorkscheduleEndDateTime)){
                                $maxCheckOutDate = $workscheduleEndDateTimeHalfDay->format('Y-m-d');
                                $maxCheckOutTime = $workscheduleEndDateTimeHalfDay->format('H:i:s');
                            }
                        }

                        $punchOutDateTime   = new DateTime($maxCheckOutDate." ".$maxCheckOutTime);
						
						/* Hours difference between punchin date time and punch out date time */
                        $totalHrsArray = array( 'PunchIn_Time' => $attendanceRecord['PunchIn_Time'],
												'PunchIn_Date' => $attendanceRecord['PunchIn_Date'],
												'PunchOut_Time' => $maxCheckOutTime,
												'PunchOut_Date' => $maxCheckOutDate);

						$totalHrs = $this->_dbAttendance->getTotalHours($totalHrsArray);

						//change punch out time based on work schedule check out time buffer
						$actualPunchOutInput = array('PunchOut_Time' => $maxCheckOutTime);
						
						if($workScheduleDetails['Check_Out_Time_Buffer'] > 0) {
							$getActualPunchOut=$dbWorkSchedule->getActualPunchOutDetails($actualPunchOutInput,$workScheduleDetails);
						
							$actualPunchOutTime = $getActualPunchOut['Actual_PunchOut_Time'];
							$maxCheckOutTime = $getActualPunchOut['PunchOut_Time'];
						} else {
							$actualPunchOutTime = date('H:i:s',strtotime($maxCheckOutTime));
							$maxCheckOutTime = date('H:i:s',strtotime($maxCheckOutTime));
						}

						/* Get Actual total hours */
						$actualTotalHoursInput = array('Approval_Status' => 'Approved',
										'PunchOut_Time' => $maxCheckOutTime,
										'Actual_Punch_In_Time' => $attendanceRecord['Actual_Punch_In_Time'],
										'PunchOut_Date' => $maxCheckOutDate,
										'PunchIn_Date' => $attendanceRecord['PunchIn_Date'],
										'Actual_PunchOut_Time' => $actualPunchOutTime,
										'Total_Hours' => $totalHrs );

						$actualTotalHours   = $dbWorkSchedule->getActualTotalHours($actualTotalHoursInput);

                        /* Since it is a auto closure we can update the Status as Approved */
                        $checkoutDetails = array('PunchOut_Date' 		=> $maxCheckOutDate,
												'PunchOut_Time'  		=> $maxCheckOutTime,
												'Actual_PunchOut_Time' 	=> $actualPunchOutTime,
												'Actual_Total_Hours' 	=> $actualTotalHours,
                                                'Checkout_Data_Source' 	=> 'Auto Closure',
                                                'Total_Hours'    => $totalHrs,
                                                'Approval_Status'=> 'Approved',
                                                'Updated_By'     => $logEmpId,
                                                'Approved_By'    => $logEmpId,
												'Approved_On'    =>date('Y-m-d H:i:s'),
												'Modified_Date'  => date('Y-m-d H:i:s'));
						if($totalHrs > 0){
							$updated = $this->_db->update($this->_ehrTables->attendance, $checkoutDetails, array('Attendance_Id = '. $attendanceRecord['Attendance_Id']));	
						} 
                    } else if($attendanceRecord['Approval_Status'] == 'Applied') {
                        $approvalData = array('Approval_Status'=> 'Approved',
                                            'Updated_By'     => $logEmpId,
                                            'Approved_By'    => $logEmpId,
											'Approved_On'    =>date('Y-m-d H:i:s'));
											
                        $updated = $this->_db->update($this->_ehrTables->attendance, $approvalData, array('Attendance_Id = '. $attendanceRecord['Attendance_Id']));	
                    }

                    $compOffData = array('Parent_Id'    => $attendanceRecord['Attendance_Id'],
                                    'Approval_Status'   => 'Approved'  );

					/* Function to validate and update the compOff */
                    if ($updated)
                    {
                        $this->_dbAttendance->updateCompOffBalance($compOffData);
					}
                }
                
                /** Add system log */
                $this->_dbCommonFunction->updateResult (array('updated'  => 1,
                                                        'action'         => 'Auto Closure',
                                                        'trackingColumn' => '',
                                                        'sessionId'      => $logEmpId,
                                                        'trackingMsg'    => " Auto Closure is Initaited by ".$logEmpId,
                                                        'columnName'     => '',
                                                        'formName'       => $customFormName
													));
													
				return array('success' => true, 'type'=>'success', 'msg'=>"Auto Closure and Approval done successfully", 'resultData'=>$validatedResult);
            } else {
                return array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! There is no valid records to Initiate the Auto Closure", 'resultData'=>$validatedResult);
            }
        } else {
            return array('success' => false, 'type'=>'warning', 'msg'=>"Sorry! There is no valid records to Initiate the Auto Closure", 'resultData'=>null);
        }

    }
	
	/* validate the employee auto LOP dates during validate and apply leave */
	public function validateAutoLop($allEmpLeaveDetails,$leaveTypeId,$filterStartDate,$filterEndDate,$source=NULL) { 
		$totalAutoLOPRecords = 0;
		$validAutoLopRecords = $invalidAutoLopRecords = $allEmpleaveDetailsArray = array();

		if(count($allEmpLeaveDetails) > 0){
			$totalAutoLOPRecords = count($allEmpLeaveDetails);

			/* Get all unique the employee ids from the allEmpLeaveDetails array */
			$allEmployeeIds = array_unique(array_column($allEmpLeaveDetails, 'Employee_Id'));

            if(count($allEmployeeIds) > 0){
				/* Validate comp off, leave, attendance is in applied, returned, cancel applied status */
				// $isPendingApprovalRec = $this->validateAttendanceFinalizationPrereq($allEmployeeIds,$filterStartDate,$filterEndDate,'noAttendance');
				$isPendingApprovalRec = array();
				/* If all the attendance/ comp off/ leaves for all the employees are approved */
				if(empty($isPendingApprovalRec)){
					$getAllEmpEligibleLvTypesAndBal = array();

					$dbPayslip = new Payroll_Model_DbTable_Payslip();

					//push all the employee ids availed leave types and leave balance in an array
					foreach($allEmployeeIds as $leaveEmpId){
						/* Get the employee leave balance for the leave type */
						$leaveClosureDetails 	= $this->_dbLeaves->getLeaveClosureDetails($leaveEmpId,$leaveTypeId);
						$empLeaveTypeBalance 	= $leaveClosureDetails['Leave_Balance'];

						/* Get the unpaid - normal leave type for an employee */
						$empUnpaidNormalLeaveTypes =$this->_dbLeaves->getEmpAvailLeaveType ($leaveEmpId,'noAttendance');

						/* If unpaid - normal leave type is available for an employee */
						if(count($empUnpaidNormalLeaveTypes) > 0){
							/* Get all the employee ids from the empUnpaidNormalLeaveTypes array */
							$allEligibleEmpLeaveTypeIds = array_column($empUnpaidNormalLeaveTypes, 'LeaveType_Id');
						}else{
							$allEligibleEmpLeaveTypeIds = array();
						}

						$empAvailedLvTypesAndBal = array('Employee_Id'=> $leaveEmpId,
													'Leave_Types'=> $allEligibleEmpLeaveTypeIds,
													'Total_Leave_Balance'=>$empLeaveTypeBalance,
													'Leave_Bal_After_Validate'=>$empLeaveTypeBalance
													);

						array_push($getAllEmpEligibleLvTypesAndBal,$empAvailedLvTypesAndBal);
					}

					//iterate employee auto LOP details to validate				
					foreach($allEmpLeaveDetails as $empLeaveDetails){
						$isLeaveTypeValid = $isInvalid = $getAllEmpEligibleLvTypesAndBalKey = 0;
						$empLeaveBalAftValidate = $empNewLeaveBalAftValidate =0;
						
						$leaveDetailsArray = array();

						$autoLOPEmpId = $empLeaveDetails['Employee_Id'];
						$autoLOPAbsentDate = $empLeaveDetails['Absent_Date'];
						$leaveReghours = $empLeaveDetails['Hours'];

						/* Validate either leave/comp off/attendance is already added for an employee on the given absent date */
						// $validateEmpIsAbsent = $dbPayslip->checkAttendanceLack($autoLOPEmpId, $autoLOPAbsentDate, $autoLOPAbsentDate, 'validate-absent-dates');

						$validateEmpIsAbsent = array();
						/* If leave/comp off/attendance is not exists for an employee in the approved status for the given absent date */
						// if(!empty($validateEmpIsAbsent) && in_array($autoLOPAbsentDate,$validateEmpIsAbsent['Absent_Dates'])){
							if(empty($validateEmpIsAbsent)){
							// Validate the leave type is eligible for an employee
							foreach($getAllEmpEligibleLvTypesAndBal as $key=>$empAvailedLeaveTypes){
								if($empAvailedLeaveTypes['Employee_Id'] == $autoLOPEmpId){
									if(count($empAvailedLeaveTypes['Leave_Types']) > 0 && in_array($leaveTypeId,$empAvailedLeaveTypes['Leave_Types'])){
										$isLeaveTypeValid = 1;
										$getAllEmpEligibleLvTypesAndBalKey = $key;
										break;
									}
								}
							}

							// If the leave type is eligible for an employee
							if($isLeaveTypeValid == 1 && $getAllEmpEligibleLvTypesAndBalKey >= 0){
								/** Send the employee id and leave type to fetch the leave activation date. Send the last param as 0 as we do not need
								 *  to consider the advance notification days while applying LOP from the attendance finalization form. */
								$leaveTypeEmpActivationDetails = $this->_dbLeaves->getLeaveTypeActivationDate($autoLOPEmpId,$leaveTypeId,0);
								
								/* If leave type activation date is greater than or equal to the absent date */
								if(strtotime($autoLOPAbsentDate) >= strtotime($leaveTypeEmpActivationDetails['activationDate'])){							
									/* If leave is applied after the employee resignation */
									if((empty($leaveTypeEmpActivationDetails['resignationDate'])) || (!empty($leaveTypeEmpActivationDetails['resignationDate']) 
									&& strtotime($autoLOPAbsentDate) <= strtotime($leaveTypeEmpActivationDetails['resignationDate']))){
										/* If payslip is generated for the past months and leave is added from the next payslip start date */
										if(strtotime($autoLOPAbsentDate) >= strtotime($leaveTypeEmpActivationDetails['maxPayslipMnth'])){
											$leaveDurationVal = ($empLeaveDetails['Leave_Duration'] == 'Full Day') ? 1 : (($empLeaveDetails['Leave_Duration'] == 'Half Day') ? 0.5 : '0.25');

											/* Get the employee available leave balance. The available leave balance will be updated after
												each auto LOP records are validated */
											$empLeaveBalAftValidate = $getAllEmpEligibleLvTypesAndBal[$getAllEmpEligibleLvTypesAndBalKey]['Leave_Bal_After_Validate'];
											
											/* If leave balance is available */
											if($empLeaveBalAftValidate > 0){	
												/* If leave calculation days is 'All days of month' then from the getLeaveDuration function will be returned as 1. 
												So for half-day leave duration is calculated separately */
												if(!is_null($leaveDurationVal) && ($leaveDurationVal == '0.5' || $leaveDurationVal == '0.25')){
													/* If haly day leave is applied, then duration should be 0.5. */
													$currentLeaveDuration = $leaveDurationVal;
												}else{
													/* Calculate the duration based on the leave type - leave calculation days */
													$currentLeaveDuration = $this->_dbLeaves-> getLeaveDuration($leaveTypeId,$autoLOPAbsentDate,$autoLOPAbsentDate,$autoLOPEmpId);
												}

												/* If shift is not scheduled to apply the leave */
												if(gettype($currentLeaveDuration) =='string' && $currentLeaveDuration == 'shiftnotscheduled'){
													$isInvalid = 1;
													$invalidReason = 'Shift is not scheduled for an employee on this date.';
												}else if($currentLeaveDuration <= 0){
													/* If leave is applied on holiday */
													$isInvalid =1;
													$invalidReason = 'Leave cannot be applied as holiday exists for this date.';
												}else{
													/* Available balance should be greater than or equal to the current auto LOP record duration */
													if($empLeaveBalAftValidate >= $currentLeaveDuration){
														/* subtract the available balance from the current auto LOP record duration */
														$empNewLeaveBalAftValidate = $empLeaveBalAftValidate - $currentLeaveDuration;
														$getAllEmpEligibleLvTypesAndBal[$getAllEmpEligibleLvTypesAndBalKey]['Leave_Bal_After_Validate'] = $empNewLeaveBalAftValidate;
														
														/* Push the valid LOP records to add leave */
														array_push($validAutoLopRecords,$empLeaveDetails);	
														
														/* Push the leave details when LOP is applied */
														if($source=='applyAutoLOP'){
															/* Hours calculation for full day leave*/
															if($currentLeaveDuration == 1){
																$leaveReghours = $empLeaveDetails['Hours'];
															}
															else{
																/* Hours calculation for half day leave*/
																$leaveReghours = ($currentLeaveDuration * $empLeaveDetails['Hours']);
															}

															$leaveDetailsArray = array('Reason'           => "Auto LOP",
																	'Duration'         => $leaveDurationVal,
																	'Start_Date'       => $autoLOPAbsentDate,
																	'End_Date'         => $autoLOPAbsentDate,
																	'Total_Days'       => $currentLeaveDuration,
																	'Hours'            => $leaveReghours,
																	'Contact_Details'  => $empLeaveDetails['Contact_Details'],
																	'Employee_Id'      => $autoLOPEmpId,
																	'LeaveType_Id'     => $leaveTypeId,
																	'Approval_Status'  => "Approved",
																	'Reason_Id'        => 2,//Reason is 'On Leave'
																	'Late_Attendance'  => 0);

															if($currentLeaveDuration == 0.25){
																$splitLeavePeriod = explode(",",$empLeaveDetails['Leave_Period']);
																foreach($splitLeavePeriod as $leavePeriod){
																	$leaveDetailsArray['Leave_Period'] = $leavePeriod;
																	array_push($allEmpleaveDetailsArray,$leaveDetailsArray);
																}
															}else{
																// If the leave duration is half day, leave period will have First half or Second half
																$leavePeriodVal = (!empty($empLeaveDetails['Leave_Period'])) ? $empLeaveDetails['Leave_Period'] : '';
																$leaveDetailsArray['Leave_Period'] = $leavePeriodVal;
																array_push($allEmpleaveDetailsArray,$leaveDetailsArray);
															}
														}

													}else{
														$isInvalid = 1;
														$invalidReason = 'Leave quota for this leave type has been completed.';
													}
												}													
											}else{
												$isInvalid = 1;
												$invalidReason = 'Leave quota for this leave type has been completed.';
											}
											
										}else{
											$isInvalid = 1;
											$invalidReason = 'Leave cannot be applied as the payslip is already generated for the leave month.';
										}
									}else{
										$isInvalid = 1;
										$invalidReason = 'Leave cannot be applied after the employee resigned.';
									}
								}else{
									$isInvalid = 1;
									$invalidReason = 'Leave Type will be activated after '.date('d M Y', strtotime($leaveTypeEmpActivationDetails['activationDate'])).'.';
								}
							}else{
								$isInvalid = 1;
								$invalidReason = 'Leave Type is not applicable to the employee.';
							}
						}else{
							$isInvalid = 1;
							$invalidReason = 'Either Attendance/Leave/Comp Off is approved on this date.';
						}
						
						/* If the auto LOP record is invalid */
						if($isInvalid == 1){
							/* Append the reason key in the invalid auto lop record */
							$empLeaveDetails['Reason'] = $invalidReason;

							/* Push the invalid LOP records */
							array_push($invalidAutoLopRecords,$empLeaveDetails);
						}
					}
				
					return array('Custom_Msg'=> '','Tot_Auto_LOP_Record_Count'=> $totalAutoLOPRecords, 'Valid_Auto_LOP_Records'=>$validAutoLopRecords, 'Invalid_Auto_LOP_Records'=>$invalidAutoLopRecords, 'Emp_Leave_Details'=>$allEmpleaveDetailsArray);
				}else{
					return array('Custom_Msg'=> 'Please finalise the attendance/leaves/compensatory off for the employees','Tot_Auto_LOP_Record_Count'=> $totalAutoLOPRecords, 'Valid_Auto_LOP_Records'=>$validAutoLopRecords, 'Invalid_Auto_LOP_Records'=>$invalidAutoLopRecords, 'Emp_Leave_Details'=>$allEmpleaveDetailsArray);
				}
            }else{
				return array('Custom_Msg'=> '','Tot_Auto_LOP_Record_Count'=> $totalAutoLOPRecords, 'Valid_Auto_LOP_Records'=>$validAutoLopRecords, 'Invalid_Auto_LOP_Records'=>$invalidAutoLopRecords,'Emp_Leave_Details'=>$allEmpleaveDetailsArray);
            }
        }else{
			return array('Custom_Msg'=> '','Tot_Auto_LOP_Record_Count'=> $totalAutoLOPRecords, 'Valid_Auto_LOP_Records'=>$validAutoLopRecords, 'Invalid_Auto_LOP_Records'=>$invalidAutoLopRecords, 'Emp_Leave_Details'=>$allEmpleaveDetailsArray);
		}
	}
	
	/* Validate the attendance/comp off/ leaves is in pending approval for the given date range */
	public function validateAttendanceFinalizationPrereq($employeeIds,$filterStartDate,$filterEndDate,$action){
		
		$exist = 1;
		$attendanceExistsCount = 0;

		/* To check whether compoff is in pending approval or not */
		$compOffStatus = array('Approved','Rejected','Cancelled');
		
		$compOffExistsCount = $this->_db->fetchOne($this->_db->select()->from(array('C'=>$this->_ehrTables->compOff, array(new Zend_Db_Expr('COUNT(C.Compensatory_Off_Id)'))))
									->where('C.Approval_Status NOT IN (?)', $compOffStatus)									
									->where('C.Employee_Id IN (?)', $employeeIds)
									->where('C.Compensatory_Date >= ?', $filterStartDate)
									->where('C.Compensatory_Date <= ?', $filterEndDate));

		/* To check whether leave is in pending approval or not */		
		$leaveApprovalStatus = array('Approved','Rejected','Cancelled');


		$startcondition = $this->_db->quoteInto('L.Start_Date >= ? and ',date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($filterStartDate)))) .
							$this->_db->quoteInto('L.Start_Date <= ? ', date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($filterEndDate))));

		$endcondition = $this->_db->quoteInto('L.End_Date >= ? and ', date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($filterStartDate)))) .
						$this->_db->quoteInto('L.End_Date <= ?  ', date('Y-m-d',strtotime($this->_ehrTables->changeDateformat($filterEndDate))));

		$getLeaveExistCondition = "(".$startcondition.") or (".$endcondition.")";

		$leaveExistsCount = $this->_db->fetchOne($this->_db->select()->from(array('L'=>$this->_ehrTables->empLeaves),array(new Zend_Db_Expr('COUNT(L.Leave_Id)')))
									->joinInner(array('LT'=>$this->_ehrTables->leavetype),'L.LeaveType_Id = LT.LeaveType_Id',array())
									->where('L.Approval_Status NOT IN (?)',$leaveApprovalStatus)
									->where('L.Employee_Id IN (?)', $employeeIds)
									->where($getLeaveExistCondition));

		/* We need to check whether the attendance records are finalised or not for no attendance action */
		if($action ==  'noAttendance'){						

			/* To check whether attendance is in pending approval or not */		
			$attendanceApprovalStatus = array('Approved','Rejected');

			$attendanceExistsCount = $this->_db->fetchOne($this->_db->select()->from(array('att'=>$this->_ehrTables->attendance),array(new Zend_Db_Expr('COUNT(att.Attendance_Id)')))
										->where('att.Employee_Id IN (?)',$employeeIds)
										->where('att.Approval_Status NOT IN (?)',$attendanceApprovalStatus)
										->where('att.Attendance_Date >= ?', $filterStartDate)
										->where('att.Attendance_Date <= ?', $filterEndDate));
		}

		/* If attendance/comp off/leaves is in pending approval */
		if(!empty($compOffExistsCount) || !empty($leaveExistsCount) || !empty($attendanceExistsCount)){
			$exist = 1;
		}else{
			$exist = 0;
		}

		return $exist;
	}    

	/* Validate and apply auto Lop for all the employees */
	public function getAutoLopEmpLeaveDetails($allEmpLeaveDetails,$leaveTypeId,$filterStartDate,$filterEndDate, $sessionId, $formName, $customFormName){
		$maxDate = $allEmpLeaveDetails[0]["Absent_Date"];
		foreach ($allEmpLeaveDetails as $absentDate)
		{
			if ($absentDate["Absent_Date"] > $maxDate) 
			{
				$maxDate = $absentDate["Absent_Date"];
			}
		}
		$leaveClosureExist  = $this->_dbLeaves->getLeaveClosureExist($maxDate);
		if(!empty($leaveClosureExist))
		{
			return array('success' =>false, 'msg'=>'Before processing auto lop for the selected records, please ensure that the leave closure or leave encashment is completed', 'type'=>'warning');
		}
		else
		{
			$validateAllEmpAutoLOP = $this->validateAutoLop($allEmpLeaveDetails,$leaveTypeId,$filterStartDate,$filterEndDate,'applyAutoLOP');

			if(!empty($validateAllEmpAutoLOP)){
				if(empty($validateAllEmpAutoLOP['Custom_Msg'])){
					if(count($validateAllEmpAutoLOP['Emp_Leave_Details']) > 0){
						$applyEmpAutoLOP = $this->applyAutoLOP($validateAllEmpAutoLOP['Emp_Leave_Details'], $sessionId, $formName, $customFormName);

						if(!empty($applyEmpAutoLOP)){
							/* If all the validated records are added in the leaves */
							if(count($validateAllEmpAutoLOP['Emp_Leave_Details']) == $applyEmpAutoLOP['Records_Added_Count']){
								return array('success' => true, 'msg'=>"Auto LOP is initiated for all the employees.", 'type'=>'success');
							}else{
								/* If availed leaves is updated but leave record is not inserted */
								if(count($applyEmpAutoLOP['Revert_Emp_Leave_Bal']) > 0){
									return array('success' => true, 'msg'=>"Auto LOP is initiated partially. But something went wrong, please contact system admin.", 'type'=>'warning');
								}
								else if(count($applyEmpAutoLOP['Revert_Emp_Leave_Bal']) == 0 && $applyEmpAutoLOP['Records_Added_Count'] == 0 &&
								$applyEmpAutoLOP['Records_Not_Added_Count'] > 0){
									return array('success' => false, 'msg'=>"Auto LOP is not initiated for the employees. Please validate again.", 'type'=>'warning');
								}else{
									return array('success' => false, 'msg'=>"Auto LOP is initiated partially. Please validate again.", 'type'=>'warning');
								}
							}
						}else{
							return array('success' => false, 'msg'=>"Sorry! Something went wrong. Please contact system admin.", 'type'=>'warning');
						}
					}else{
						return array('success' => false, 'msg'=> 'Auto LOP cannot be initiated for the employee(s) with this leave type. Please validate again.', 'type'=>'warning');
					}
				}else{
					return array('success' => false, 'msg'=> $validateAllEmpAutoLOP['Custom_Msg'], 'type'=>'warning');
				}
			}else{
				return array('success' => false, 'msg'=>"Sorry! Something went wrong. Please contact system admin.", 'type'=>'warning');
			}
		}
	}

	//	Multiple Leave insert with approved status while attendance closure
	public function applyAutoLOP ($leaveArr, $sessionId, $formName, $customFormName)
	{
		$successLOPCount = 0;
		$failureLOPCount = 0;
		$leaveAlreadyAppliedTotCount = 0;
        $failureLOPEmpIds = array();
		
		foreach ($leaveArr as $record)
		{
			$leaveAlreadyAppliedCount = $result = 0;
			
			$recEmployeeId  = $record['Employee_Id'];
			$recLeaveTypeId = $record['LeaveType_Id'];
			$recTotalDays   = $record['Total_Days'];
			$recStartDate   = $record['Start_Date'];
			$recEndDate     = $record['End_Date'];
			
            //get the leave balance same way like leave history
            $availedLeaves       = $this->_dbLeaves->getLeaveClosureDetails ($recEmployeeId, $recLeaveTypeId);
			$empLeaveTypeBalance = $availedLeaves['Leave_Balance'];
            $leaveBalance        = $empLeaveTypeBalance - $recTotalDays;
            
            $leavesTaken = 0;			

			//leave start date should be fall inside leave closure start date and end date
            if ($availedLeaves && $availedLeaves['finstart'] <= $recStartDate && $availedLeaves['finend'] >= $recStartDate)
            {
                $leavesTaken  = $availedLeaves['Leaves_Taken'] + $recTotalDays;
                
                if ($availedLeaves['Leave_Balance'] >= $recTotalDays)
                {
					/* Validate the leave already exists for the given duration */
					$isLeaveAlreadyapplied = $this->_dbLeaves->leaveExists($recEmployeeId, 0, $recStartDate, $recEndDate,NULL, $record['Leave_Period'], $record['Duration']);
					/* If leave not exists for this duration */
					if(empty($isLeaveAlreadyapplied)){
						$availInserted = $this->_dbLeaves->updateLeaveBalance ($availedLeaves['Eligible_Leave_Id'],$leavesTaken,$leaveBalance);
						
						if($availInserted){
							$record['Added_By']    = $sessionId;
							$record['Added_On'] = date('Y-m-d H:i:s');
							$record['Approver_Id'] = $sessionId;
							$record['Approved_By']    = $sessionId;
							$record['Approved_On'] = date('Y-m-d H:i:s');
						
							$inserted = $this->_db->insert($this->_ehrTables->empLeaves, $record);
							if($inserted)
							{
								$result = 1;
							}
							else
							{
								$result = 0;
								array_push($failureLOPEmpIds,$recEmployeeId);
							}
						}
						else
						{
							$result = 0;
						}
					}else{
						$result = 0;
						$leaveAlreadyAppliedCount = 1;
					}
                }
                else
                {
                    $result = 0;
                }
            }
            else
            {
				$result = 0;
            }
            
            if ($result)
            {
                ++$successLOPCount;
            
                $msgDescA = "<p> Loss of Pay(Unpaid Leave) record has been created and approved for you. </p>";
                
                if($sessionId != $recEmployeeId)
                {
                    $this->_dbCommonFunction->communicateMail (array('employeeId'  => $recEmployeeId,
                                                                    'ModuleName'  => 'Employees',
                                                                    'formName'    => 'Leaves',
                                                                    'successMsg'  => $customFormName,
                                                                    'customFormName' => $customFormName,
                                                                    'formUrl'     => '/employees/leaves',
                                                                    'inboxTitle'  => $customFormName.' Notification',
                                                                    'mailContent' => $msgDescA,
                                                                    'action'      => 'updated'));
                }
            }
            else
            {
				/* If leave is not already applied on this date */
				if($leaveAlreadyAppliedCount == 0){
					++$failureLOPCount;
				}
            }
        }
        $this->_dbCommonFunction->updateResult (array('updated'        => 1,
                                                        'action'         => 'Initiate LOP',
                                                        'trackingColumn' => '',
                                                        'sessionId'      => $sessionId,
                                                        'trackingMsg'    => $successLOPCount." LOP record(s) are created and approved by ".$sessionId,
                                                        'columnName'      => '',
                                                        'formName'       => $customFormName
                                                    ));
		
        return array('Revert_Emp_Leave_Bal'=> $failureLOPEmpIds, 'Records_Added_Count' => $successLOPCount, 'Records_Not_Added_Count' => $failureLOPCount);
		
	}

	public function InitiateLeaveAsPerAttendanceShortage($noAttendanceDetails,$sessionId)
	{
		$attendanceEnforcedEmployeeId   	= array_unique(array_column($noAttendanceDetails,'Employee_Id'));
		$dbHrReports 						= new Reports_Model_DbTable_HrReports();
		$dbLeave 							= new Employees_Model_DbTable_Leave();
		$attendanceShortageEmployeeDetails 	= $dbHrReports->getStrictModeEmployees($attendanceEnforcedEmployeeId,'Yes');
		$insertAttendanceShortageLeave 		= 0;
		$noAttendanceDetailsCount 			= count($noAttendanceDetails);

		//Get all the employee full and final settlement status
		$allEmployeeFFStatus = $this->_db->fetchAll($this->_db->select()->from($this->_ehrTables->employeeFullAndFinalSettlement, array('Employee_Id','Settlement_Status'))
									->where('Employee_Id IN (?)', $attendanceEnforcedEmployeeId));

		$maxDate 			= $noAttendanceDetails[0]["Absent_Date"];
		foreach ($noAttendanceDetails as $absentDate)
		{
			if ($absentDate["Absent_Date"] > $maxDate) 
			{
				$maxDate = $absentDate["Absent_Date"];
			}
		}
		$leaveClosureExist  = $this->_dbLeaves->getLeaveClosureExist($maxDate);
		if(!empty($leaveClosureExist))
		{
			return array('success' =>false, 'msg'=>'Before processing attendance shortage for the selected records, please ensure that the leave closure or leave encashment is completed', 'type'=>'warning');
		}
		else
		{
			$quarterDaysNoAttendanceDetails = array();
			foreach($noAttendanceDetails as $key => $noAttendanceData)
			{
				foreach($attendanceShortageEmployeeDetails as $attendanceShortageEmployee)
				{
					if($attendanceShortageEmployee['Employee_Id']== $noAttendanceData['Employee_Id'])
					{
						$attendanceShortageEmployeeId = $attendanceShortageEmployee['Employee_Id'];
						$empSettlementDetail = array_filter($allEmployeeFFStatus, function($value) use ($attendanceShortageEmployeeId){
							return $value['Employee_Id'] == $attendanceShortageEmployeeId;
						});
						$empSettlementInitiated = (!empty($empSettlementDetail) && count($empSettlementDetail) > 0) ? 1 : 0;
						//If the full and final settlement is initiated, unset the employee
						if($empSettlementInitiated == 1){
							unset($noAttendanceDetails[$key]);
							break;
						}else{
							if($noAttendanceData['Leave_Duration']=='Quarter Day')
							{
								$leaveDuration=0.25;
							}
							else if($noAttendanceData['Leave_Duration']=='Half Day')
							{
								$leaveDuration=0.5;
							}
							else
							{
								$leaveDuration=1;
							}
							//Validate the employee is eligible to apply the leave type
							$validationDetails = array('leaveTypeId' 	=> $attendanceShortageEmployee['LeaveType_Id'],
														'leaveId' 		=> 0,
														'leaveFrom' 	=> $noAttendanceData['Absent_Date'],
														'leaveTo' 		=> $noAttendanceData['Absent_Date'],
														'employeeId' 	=> $attendanceShortageEmployee['Employee_Id'],
														'duration' 		=> $leaveDuration,
														'leavePeriod' 	=> $noAttendanceData['Leave_Period']);

							if($leaveDuration == '0.25'){
								$leaveDateValid = 0;
								$splitLeavePeriod = explode(",",$noAttendanceData['Leave_Period']);
								foreach($splitLeavePeriod as $leavePeriod){
									$validationDetails['Leave_Period'] = $leavePeriod;
									$initiateShortageLeaveResult = $this->validateInitiateAttendanceShortageLeaves($validationDetails,$noAttendanceData,$attendanceShortageEmployee,$sessionId);
									$insertAttendanceShortageLeave+= $initiateShortageLeaveResult['insertShortageLeaveCount'];
									if($initiateShortageLeaveResult['isLeaveDateValid'] == 1){
										$leaveDateValid += 1;
									}else{
										//if quarter leave is not inserted then add the quarter details with respective leave period in an array and merge with no attendance details
										$noAttendanceDetails[$key]['Leave_Period'] = $leavePeriod;
										$quarterDurationNoAttendanceArray = $noAttendanceDetails[$key];
										$quarterDaysNoAttendanceDetails[] = $quarterDurationNoAttendanceArray;
									}
								}
								
								unset($noAttendanceDetails[$key]);
								break;
							}else{
								$validationDetails['Leave_Period'] = $noAttendanceData['Leave_Period'];
								$initiateShortageLeaveResult = $this->validateInitiateAttendanceShortageLeaves($validationDetails,$noAttendanceData,$attendanceShortageEmployee,$sessionId);
								$insertAttendanceShortageLeave+= $initiateShortageLeaveResult['insertShortageLeaveCount'];
								if($initiateShortageLeaveResult['isLeaveDateValid'] == 1){
									unset($noAttendanceDetails[$key]);
									break;
								}
							}
						}
					}
				}
			}
			$noAttendanceDetails = array_merge($noAttendanceDetails,$quarterDaysNoAttendanceDetails);
			foreach($noAttendanceDetails as $key => $noAttendanceData)
			{
				foreach($attendanceShortageEmployeeDetails as $attendanceShortageEmployee)
				{
					if($attendanceShortageEmployee['Employee_Id']== $noAttendanceData['Employee_Id'])
					{
						if(!empty($attendanceShortageEmployee['Manager_Id']))
						{
							$approverId = $attendanceShortageEmployee['Manager_Id'];
						}
						else
						{
							$approverId = $attendanceShortageEmployee['Employee_Id'];
						}
						$noAttendanceData['Approver_Id']	= $approverId;
						$noAttendanceData['LeaveType_Id'] 	= $attendanceShortageEmployee['Alternate_LeaveType_Id'];
						$noAttendanceData['Added_By']		= $sessionId;			
						$applyAttendanceShortageLeave 		= $this->applyAttendanceShortageLeave($noAttendanceData);
						if(!empty($applyAttendanceShortageLeave))
						{
							$insertAttendanceShortageLeave++;
						}
						break;
					}
				}
			}

			/* If all the validated records are added in the leaves */
			if($noAttendanceDetailsCount == $insertAttendanceShortageLeave)
			{
				return array('success' => true, 'msg'=>"Attendance Shortage Leave is initiated for all the employees.", 'type'=>'success');
			}
			elseif($insertAttendanceShortageLeave > 0) 
			{
				return array('success' => true, 'msg'=>"Attendance Shortage Leave is initiated partially", 'type'=>'warning');
			}
			else
			{
				return array('success' => false, 'msg'=>"Attendance Shortage Leave is  not initiated.please contact system admin", 'type'=>'warning');
			}
		}
	}

	public function validateInitiateAttendanceShortageLeaves($validationDetails,$noAttendanceData,$attendanceShortageEmployee,$sessionId){
		$insertShortageLeaveCount = 0;
		$result = $this->_dbLeaves->validateEmployeeLeave($validationDetails);
		$isLeaveDateValid = 1;
		if(!empty($result)){
			if(!is_null($result['leaveFreeze']) && $result['leaveFreeze']>0)
			{
				$isLeaveDateValid = 0;
			}

			if (!is_null($result['frequency']) && !empty($result['frequency']) && !is_null($result['maxlimit']))
			{
				$errorCodestartMsg = $result['frequency']['startMsg'];
				if (!is_null($errorCodestartMsg) && !empty($errorCodestartMsg) && !empty($noAttendanceData['Absent_Date']))
				{
					$isLeaveDateValid = 0;
				}
			}

			if (!is_null($result['maxlimit']) && !empty($noAttendanceData['Absent_Date']))
			{
				if (!is_null($result['maxlimit']['endMsg']) && !empty($result['maxlimit']['endMsg']) && !empty($noAttendanceData['Absent_Date']))
				{
					$isLeaveDateValid = 0;
				}
			}

			if(!empty($result['maxlimit']['Leave_Period_Lapsed_Days']))
			{
				$isLeaveDateValid = 0;
			}
			
		}

		if($isLeaveDateValid == 1){
			if(!empty($attendanceShortageEmployee['Manager_Id']))
			{
				$approverId = $attendanceShortageEmployee['Manager_Id'];
			}
			else
			{
				$approverId = $attendanceShortageEmployee['Employee_Id'];
			}
			$noAttendanceData['Approver_Id']	= $approverId;
			$noAttendanceData['LeaveType_Id'] 	= $attendanceShortageEmployee['LeaveType_Id'];		
			$noAttendanceData['Added_By']		= $sessionId;
			$applyAttendanceShortageLeave 		= $this->applyAttendanceShortageLeave($noAttendanceData);
			if(!empty($applyAttendanceShortageLeave))
			{
				$insertShortageLeaveCount++;
			}
		}

		return array('isLeaveDateValid' => $isLeaveDateValid,'insertShortageLeaveCount' => $insertShortageLeaveCount);
	}

	public function applyAttendanceShortageLeave($noAttendanceData)
	{
		if(!empty($noAttendanceData))
		{
			if($noAttendanceData['Leave_Duration']=='Full Day')
			{
                $totalDays = 1;
			}
			else if($noAttendanceData['Leave_Duration']=='Half Day')
			{
                $totalDays = 0.5;
			}
			else
			{
				$totalDays = 0.25;
			}
			$leaveDetails = array('Reason'     => "Due to attendance hours shortage",
							'Duration'         => $totalDays,
							'Start_Date'       => $noAttendanceData['Absent_Date'],
							'End_Date'         => $noAttendanceData['Absent_Date'],
							'Total_Days'       => $totalDays,
							'Hours'            => $noAttendanceData['Hours'],
							'Contact_Details'  => $noAttendanceData['Contact_Details'],
							'Employee_Id'      => $noAttendanceData['Employee_Id'],
							'Approver_Id'      => $noAttendanceData['Approver_Id'],
							'Leave_Period'	   => $noAttendanceData['Leave_Period'],
							'LeaveType_Id'     => $noAttendanceData['LeaveType_Id'],
							'Alternate_Person' => array(),
							'Reason_Id'        => 0,
							'Approval_Status'  => "Applied",
							'Added_By'         => $noAttendanceData['Added_By'],
							'Added_On'         => date('Y-m-d H:i:s'),
							'Attendance_Shortage'  => 1);

			$attendanceShortageLeaveExist = $this->_dbAttendance->getAttendanceShortageLeaveExist($noAttendanceData['Employee_Id'],$noAttendanceData['Absent_Date'],$noAttendanceData['Absent_Date'],'insertAttendanceShortage');
			if(empty($attendanceShortageLeaveExist))
			{
				$result = $this->_dbLeaves->updateLeave($leaveDetails,array(), $noAttendanceData['Added_By'], "", 0,"", array(), "","","","No");
				if($result['success'])
				{
					return 1;
				}
				else
				{
					return 0;
				}
			}
			else
			{
				return 0;
			}
		}
	}

	public function initiateIgnoreAttendanceShortage($attendanceShortageEmpDetails, $logEmpId){
		for($i=0;$i<count($attendanceShortageEmpDetails);$i++){
            $attendanceShortageEmpDetails[$i]['Added_On'] = date('Y-m-d H:i:s');
			$attendanceShortageEmpDetails[$i]['Added_By'] = $logEmpId;
        }
		$this->_ehrTables->insertMultiple($this->_ehrTables->ignoreEmpAttshortagelist,$attendanceShortageEmpDetails);

		return array('success' => true, 'msg'=>"Attendance shortage records ignored successfully.", 'type'=>'warning');
	}

	public function getNewMinDateByRegularizationCutOff($filterMinDate,$currentDate,$attendanceGeneralConfiguration=''){
		$newFilterMinDate = '';
		$displayCutOffDate = '';
		$currentDateRegularizationCutOffDate = '';
		if(empty($attendanceGeneralConfiguration)){
			$attendanceGeneralConfiguration = $this->_dbCommonFunction->getAttendanceGeneralConfiguration();
		}
		if(!empty($attendanceGeneralConfiguration) && count($attendanceGeneralConfiguration) > 0){
			$attendanceRegularizationCutOffDays = $attendanceGeneralConfiguration['Attendance_Regularization_Cut_Off_Days_For_Employee'];
			if(!IS_NULL($attendanceRegularizationCutOffDays) && $attendanceRegularizationCutOffDays > 0){
				$attendanceRegularizationAfter = $attendanceGeneralConfiguration['Attendance_Regularization_After'];
				$attendanceRegularizationAfter = strtolower($attendanceRegularizationAfter);

				if($attendanceRegularizationAfter === 'last day of payroll'){
					$dbPayslip  = new Payroll_Model_DbTable_Payslip();
					$minDateStrtotime=strtotime($filterMinDate);
					$payslipMonth=date("m",$minDateStrtotime);
					$payslipYear=date("Y",$minDateStrtotime);
					
					$salaryDateRange = $dbPayslip->getSalaryDateRange($payslipMonth, $payslipYear, strtotime($filterMinDate),203);
					$lastSalaryDate = $salaryDateRange['Last_SalaryDate'];
					$cutOffDate = date('Y-m-d', strtotime('+'.$attendanceRegularizationCutOffDays.' day', strtotime($lastSalaryDate)));
					
					if(strtotime($cutOffDate) >= strtotime($currentDate)){
						$newFilterMinDate = '';
					}else{
						$displayCutOffDate =  date('d M Y',strtotime($cutOffDate));
						$newFilterMinDate = date('Y-m-d', strtotime('+1 day', strtotime($lastSalaryDate)));
					}
				}else{
					//Subtract the cut off days from the current date
					$currentDateRegularizationCutOffDate = $newFilterMinDate = date('Y-m-d', strtotime('-'.$attendanceRegularizationCutOffDays.' day', strtotime($currentDate)));
					/** Consider the current date is 27 Jun 2024. If the cut off day is 1 then new filter min date will be 26 Jun 2024. 
					 * 1. If the payslip is generated for Jun 2024 then input filter min date will be 1 jul 2024. As the payslip is
					 * generated the employee cannot regularize attendance for june month. So the below condition is checked to change 
					 * the min date to 1 jul 2024.
					 * 2. If the payslip is not generated for jun 2024 or if the employee joined before 26 Jun 2024 then newFilterMinDate
					 * value will remain the same
					 * 3. If the employee joined on 2 Jul 2024 then new filter min date value will be changed to 2 Jul 2024  */
					if(strtotime($newFilterMinDate) < strtotime($filterMinDate)){
						$newFilterMinDate = $filterMinDate;
					}
				}
			}
		}
		
		return array('currentDateRegularizationCutOffDate'=>$currentDateRegularizationCutOffDate,'regularizationNextMonthStartDate'=>$newFilterMinDate,'displayCutOffDate'=>$displayCutOffDate,'attendanceRegularizationAfter'=>$attendanceRegularizationAfter);
	}

	//Function to validate the attendance regularization request limit and return the response to UI
	public function formRegularizationRequestLimitResponse($employeeId,$startDate,$endDate){
		$responseArray = array();

		$attendanceGeneralConfig = $this->_dbCommonFunction->getAttendanceGeneralConfiguration();
		if(!empty($attendanceGeneralConfig) && $attendanceGeneralConfig['Attendance_Regularization_Request_Limit_For_Employee'] > 0){	
			$employeeRegularizationRequestLimit = $attendanceGeneralConfig['Attendance_Regularization_Request_Limit_For_Employee'];
			$startDateStrtotime=strtotime($startDate);
			$startDateMonth=date("m",$startDateStrtotime);
			$startDateYear=date("Y",$startDateStrtotime);
					
			$dbPayslip  = new Payroll_Model_DbTable_Payslip();
			$startMonthSalaryDateRange = $dbPayslip->getSalaryDateRange($startDateMonth, $startDateYear, strtotime($startDateStrtotime),203);
			$endMonthSalaryDateRange = array();

			if(!empty($startMonthSalaryDateRange)){
				$salaryDate = $startMonthSalaryDateRange['Salary_Date'];
				$lastSalaryDate = $startMonthSalaryDateRange['Last_SalaryDate'];
				$endDateStrtotime=strtotime($endDate);

				if($endDateStrtotime >= strtotime($salaryDate) && $endDateStrtotime <= strtotime($lastSalaryDate)){
					$endMonthSalaryDateRange = array();
				}else{
					$endDateMonth=date("m",$endDateStrtotime);
					$endDateYear=date("Y",$endDateStrtotime);
				
					$endMonthSalaryDateRange = $dbPayslip->getSalaryDateRange($endDateMonth, $endDateYear, strtotime($endDateStrtotime),203);
				}
	
				$regularizationRequestLimitExceed = $this->validateRegularizationRequestLimit($employeeId,$startMonthSalaryDateRange,$employeeRegularizationRequestLimit);
				if($regularizationRequestLimitExceed > 0){
					$responseObject = array(
						'salaryDate' => $startMonthSalaryDateRange['Salary_Date'],
						'lastSalaryDate' => $startMonthSalaryDateRange['Last_SalaryDate'],
						'regularizationRequestLimitExceed' => $regularizationRequestLimitExceed
					);
					array_push($responseArray,$responseObject);
				}
			}

			if(!empty($endMonthSalaryDateRange)){
				$salaryDate = $endMonthSalaryDateRange['Salary_Date'];
				$lastSalaryDate = $endMonthSalaryDateRange['Last_SalaryDate'];
				$regularizationRequestLimitExceed = $this->validateRegularizationRequestLimit($employeeId,$endMonthSalaryDateRange,$employeeRegularizationRequestLimit);

				if($regularizationRequestLimitExceed > 0){
					$responseObject = array(
						'salaryDate' => $endMonthSalaryDateRange['Salary_Date'],
						'lastSalaryDate' => $endMonthSalaryDateRange['Last_SalaryDate'],
						'regularizationRequestLimitExceed' => $regularizationRequestLimitExceed
					);
					array_push($responseArray,$responseObject);
				}
			}
		}
		return $responseArray;
	}

	//Function to validate the attendance regularization request limit exceed or not
	public function validateRegularizationRequestLimit($employeeId,$salaryDateDetails,$employeeRegularizationRequestLimit){
		$regularizationRequestLimitExceed = 0;

		$conditions = $this->_db->quoteInto('Checkin_Form_Source = ?', 203);
		$conditions .= $this->_db->quoteInto(' or Checkout_Form_Source = ?', 203);

		$qryAttendanceCount = $this->_db->select()->from($this->_ehrTables->attendance, array(new Zend_Db_Expr('COUNT(Attendance_Id)')))
							->where('Attendance_Date >= ?', $salaryDateDetails['Salary_Date'])
							->where('Attendance_Date <= ?', $salaryDateDetails['Last_SalaryDate'])
							->where('Employee_Id = ?', $employeeId)
							->where($conditions)
							->where('Approval_Status IN (?)', array('Applied','Approved','Returned'))
							->group('Attendance_Date');
		$regularizationAttendanceCountDetails = $this->_db->fetchAll($qryAttendanceCount);
		if(!empty($regularizationAttendanceCountDetails) && count($regularizationAttendanceCountDetails) > 0){							
			$regularizationAttendanceCount = count($regularizationAttendanceCountDetails);
			if($employeeRegularizationRequestLimit > $regularizationAttendanceCount){
				$regularizationRequestLimitExceed = 0;
			}else{
				$regularizationRequestLimitExceed = 1;
			}
		}else{
			$regularizationRequestLimitExceed = 0;
		}

		return $regularizationRequestLimitExceed;
	}

	public function initiateAutoAttendance($autoAttendanceDetails,$sessionId)
	{
		$insertAttendanceDetails   = 0;
		$attendanceDetails         = array();
		$autoAttendanceEmployeeIds = array_unique(array_column($autoAttendanceDetails,'Employee_Id'));
		$autoAttendanceCount 	   = count($autoAttendanceEmployeeIds);
		if($autoAttendanceCount > 0)
		{
			$autoAttendanceDate  	 	       = array_unique(array_column($autoAttendanceDetails,'Absent_Date'));
			$autoAttendanceStartDate 	       = min($autoAttendanceDate);
			$autoAttendanceEndDate   		   = max($autoAttendanceDate);
			$dbPayslip               		   = new Payroll_Model_DbTable_Payslip();
			$rosterManagementSettings          = $dbPayslip->getRosterManagementSettings();
			if($rosterManagementSettings['Overlap_Shift_Schedule']==1) 
			{
			   $autoAttendanceEndDate = date("Y-m-d", strtotime('+1 days',strtotime($autoAttendanceEndDate))); 
			}
			
			$attendanceEnforcedEmployeeDetails = $dbPayslip->getSalaryPayslipEmployeeDetails($autoAttendanceEmployeeIds,$autoAttendanceStartDate,$autoAttendanceEndDate);
			$allWorkScheduleDetails            = $this->_dbAttendance->getAllWorkScheduleDetails($autoAttendanceStartDate,$autoAttendanceEndDate);
        	$allEmployeeShiftDetails           = $this->_dbAttendance->getAllEmployeeShiftDetails($attendanceEnforcedEmployeeDetails,$autoAttendanceStartDate,$autoAttendanceEndDate);
			$workPlaceId                       = $this->_dbAttendance->getOfficeWorkPlaceId();
			$organizePayslipEmployeeDetails    = $this->_dbCommonFunction->organizeDataByEmployeeIdAndDate($attendanceEnforcedEmployeeDetails,'Employee_Id');
			foreach($autoAttendanceDetails as $autoAttendanceEmployeeDetails)
			{
				$employeeData  = $this->_dbCommonFunction->ensureArray($organizePayslipEmployeeDetails,$autoAttendanceEmployeeDetails['Employee_Id']);
				if(!empty($employeeData[0]))
				{
					$employeeDetails   	 = $employeeData[0];
					$workScheduleDetails = array();
					$employeeId          = $employeeDetails['Employee_Id']; 
					$workScheduleId      = $employeeDetails['WorkSchedule_Id'];
					$startDate 		     = $autoAttendanceEmployeeDetails['Absent_Date'];
					
					if(!empty($employeeDetails['Manager_Id']))
					{
                        $approverId = $employeeDetails['Manager_Id'];
					}
					else
					{
						$approverId = $sessionId;
					}

					if($employeeDetails['Work_Schedule']=='Shift Roster')
					{
						$employeeShiftDetails = $allEmployeeShiftDetails[$employeeId]?? null;
						$workScheduleDetails = $this->_dbCommonFunction->getWorkScheduleDetailsByDateForShiftRosterEmployees($employeeId,$startDate,$employeeShiftDetails,$allWorkScheduleDetails,$rosterManagementSettings);
					}
					else
					{
						$employeeWorkScheduleDetails = $allWorkScheduleDetails[$workScheduleId]?? null;
						$workScheduleDetails         = $employeeWorkScheduleDetails[$startDate] ?? null;
					}
	
					if(!empty($workScheduleDetails))
					{
						$attendanceArray = array('Attendance_Id'       	=> '',
												'Employee_Id'         	=> $employeeId,
												'Approver_Id'         	=> $approverId,
												'Approval_Status'     	=> 'Approved',
												'Attendance_Date'     	=> $workScheduleDetails['Work_Schedule_Date'],
												'PunchIn_Date'        	=> $workScheduleDetails['Work_Schedule_Date'],
												'Checkin_Work_Place_Id' => $workPlaceId,
												'PunchOut_Date'       	=> date('Y-m-d',strtotime($workScheduleDetails['Regular_To'])),
												'PunchIn_Time'        	=> $workScheduleDetails['Regular_Work_Start_Time'],
												'PunchOut_Time'       	=> $workScheduleDetails['Regular_Work_End_Time'],
												'Checkout_Work_Place_Id'=> $workPlaceId,
												'Exclude_Break_Hours' 	=> 1,
												'Late_Attendance'     	=> 0,
												'Checkin_Data_Source'	=>'Auto Attendance',
												'Checkout_Data_Source'  => 'Auto Attendance',
												'Actual_Punch_In_Time'  =>$workScheduleDetails['Regular_Work_Start_Time'],
												'Actual_Total_Hours'    => 0,
												'Actual_PunchOut_Time'  => $workScheduleDetails['Regular_Work_End_Time'],
												'Total_Hours'           =>0,
												'Added_Date'           =>date('Y-m-d H:i:s'),
												'Added_By'             =>$sessionId);

					   $attendanceArray['Total_Hours']        = $this->_dbAttendance->getTotalHours($attendanceArray);
					   $attendanceArray['Actual_Total_Hours'] = $attendanceArray['Total_Hours'];
					   array_push($attendanceDetails,$attendanceArray);
					}
				}
			}
		}
		if(!empty($attendanceDetails))
		{
			$insertAttendanceDetails = $this->_ehrTables->insertMultiple($this->_ehrTables->attendance,$attendanceDetails);	
			$this->_dbCommonFunction->triggerAttendanceSummaryStepFunction($attendanceDetails,'attendance');
		}

		$insertedAttendanceCount = count($attendanceDetails);
		if($insertedAttendanceCount==$autoAttendanceCount)
		{
			$attendanceInsertion = 'complete';
		}
		else
		{
			$attendanceInsertion = 'partial';
		}
		
		return array('insertAttendanceDetails'=>$insertAttendanceDetails,'attendanceInsertion'=>$attendanceInsertion);
	}


	public function __destruct()
    {
        
    }
}

