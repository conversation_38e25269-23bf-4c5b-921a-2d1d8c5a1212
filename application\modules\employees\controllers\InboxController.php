<?php
//=========================================================================================
//=========================================================================================
/* Program : InboxController.php													     *
 * Property of Caprice Technologies Pvt Ltd,                                             *
 * Copyright (c) 2013 Caprice Technologies Pvt Ltd,                                      *
 * Coimbatore, Tamilnadu, India.														 *
 * All Rights Reserved.            														 *
 * Use of this material without the express consent of Caprice Technologies              *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law. *
 *                                                                                    	 *
 * Description : An inbox is the main folder that your incoming mail gets stored in.     *
 * Inbox is a built-in messaging system to allow communication in form of short messages *
 * between the employees within the organization. After a task is completed successfuly, *
 * mail is sent to related persons . When employee logs in, the mail alerts are shown    *
 * to him from Inbox. 																	 *
 *                                                                                   	 *
 *                                                                                    	 *
 * Revisions :                                                                    	     *
 *  Version    Date           Author                  Description                        *
 *  0.1        30-May-2013    Narmadha	              Initial Version         	         *
 *                                                                                    	 *
 *  1.0        02-Feb-2015    Dhanabal                Changed in file for mobile app     *
 *                                                                                       *
 *  1.5        07-Jan-2016    Deepak               Changed in file for Bootstrap         *
 *							  				               		                         */
//=========================================================================================
//=========================================================================================
class Employees_InboxController extends Zend_Controller_Action
{
    protected $_dbInbox           = null;
    protected $_dbCommonFun       = null;
	protected $_dbAccessRights    = null;
    protected $_inboxAccessRights = null;
    protected $_logEmpId          = null;
	protected $_inboxUser         = null;
	protected $_ehrTables         = null;
	protected $_formName          = 'Inbox';
    protected $_hrappMobile = null;
    
    public function init()
    {
        $this->_hrappMobile = new Application_Model_DbTable_HrappMobile();
        if ($this->_hrappMobile->checkAuth())
        {
            $this->_dbCommonFun    = new Application_Model_DbTable_CommonFunction();
            $this->_dbInbox        = new Employees_Model_DbTable_Inbox();
            $this->_dbAccessRights = new Default_Model_DbTable_AccessRights();
			$this->_ehrTables 	   = new Application_Model_DbTable_Ehr();
			
			$userSession              = $this->_dbCommonFun->getUserDetails ();
            $this->_logEmpId          = $userSession['logUserId'];
            $this->_inboxAccessRights = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formName);
			$this->_inboxUser         = $this->_inboxAccessRights['Employee'];
            //$this->_dbAccessRights->refreshUserSessionTimestamp($this->_logEmpId);
        }
        else
        {
            if (Zend_Session::namespaceIsset('lastRequest'))
                Zend_Session:: namespaceUnset('lastRequest');
            
            $session = new Zend_Session_Namespace('lastRequest');
            $session->lastRequestUri = 'employees/inbox';
            $this->_redirect('auth');
        }
    }
	
    /**
     * Displays all the message for the logged in user
     */
    public function indexAction()
    {
        $checkSessionAuth = $this->_hrappMobile->checkSessionAuth ();

        if ($checkSessionAuth)
        {
            $this->_helper->layout()->disableLayout()->setLayout('admin_layout');
		
            $this->view->formNameA = $this->_formName;
            $this->view->customFormNameA = $this->_ehrTables->getCustomForms($this->_formName);
            
            $this->view->inboxUser =  array('View'      => $this->_inboxUser['View'],
                                            'Op_Choice' => $this->_inboxUser['Optional_Choice'],
                                            'Delete'    => $this->_inboxUser['Delete'],
                                            'Admin'     => $this->_inboxAccessRights['Admin']);
        } else {
			$this->_redirect('auth');
		}
	}
    /**
     * Inbox records which is shown in the grid
     */
    public function listInboxAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('list-inbox', 'json')->initContext();
			
            if ($this->_inboxUser['View'] == 1)
            {
                $sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
				
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$inboxUser = array('Delete'    => $this->_inboxUser['Delete'],
								   'Op_Choice' => $this->_inboxUser['Optional_Choice'],
								   'Admin'     => $this->_inboxAccessRights['Admin'],
								   'LogId'     => $this->_logEmpId);
				
				$this->view->result = $this->_dbInbox->listInbox ($page, $rows, $sortField, $sortOrder, $searchAll, $inboxUser);
            }
        }
        else
        {
            $this->_helper->redirector('index', 'inbox', 'employees');
        }
    }
	
	/**
     * Used to view logged in employee's message
     */
    public function viewMessageAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('view-message', 'json')->initContext();
			
            if ($this->_inboxUser['View'] == 1)
            {
                $inboxId = $this->_getParam('inboxId', null);
                $inboxId = filter_var($inboxId, FILTER_SANITIZE_NUMBER_INT);
				
                if (!empty($inboxId))
                {
					$this->view->result = $this->_dbInbox->viewMessage ($inboxId);
                }
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
				}
            }
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Access denied', 'type' => 'warning');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'inbox', 'employees');
        }
    }
	
	/**
	 *	Delete Inbox message(s)
	*/
	public function deleteMessageAction()
    {
        $this->_helper->layout()->disableLayout();
		
        if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('delete-message', 'json')->initContext();
			
            if ($this->_inboxUser['Delete'] == 1)
            {
                $inboxIds = $this->_getParam('inboxIds', null);
                $inboxIds = filter_var($inboxIds, FILTER_SANITIZE_NUMBER_INT, FILTER_REQUIRE_ARRAY);
				
				if (!empty($inboxIds))
                {
					$this->view->result = $this->_dbInbox->deleteMessage ($inboxIds, $this->_logEmpId);
                }
				else
				{
					$this->view->result = array('success' => false, 'msg' => 'Invalid data', 'type' => 'info');
				}
            }
			else
			{
				$this->view->result = array('success' => false, 'msg' => 'Access denied', 'type' => 'warning');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'inbox', 'employees');
        }
    }
	
    /**
     * option to mark the message as read, unread and delete.
     */
    public function readUnreadMessageAction()
    {
        $this->_helper->layout()->disableLayout();
        
		if (isset($_SERVER['HTTP_REFERER']))
        {
			$ajaxContext = $this->_helper->getHelper('AjaxContext');
			$ajaxContext->addActionContext('read-unread-message', 'json')->initContext();
			
            if ($this->_inboxUser['Optional_Choice'] == 1)
            {
                $inboxIds = $this->_getParam('inboxIds', null);
				$inboxIds = filter_var($inboxIds, FILTER_SANITIZE_NUMBER_INT, FILTER_REQUIRE_ARRAY);
				
				$changeAction = $this->_getParam('changeAction', null);
				$changeAction = filter_var($changeAction, FILTER_SANITIZE_STRIPPED);
				
				if (!empty($inboxIds) && count($inboxIds) > 0)
                {
					$this->view->result = $this->_dbInbox->markMessage ($inboxIds, $changeAction);
                }
				else
				{
					$this->view->result = array('success'=>false, 'msg' => 'Invalid data', 'type' => 'info');
				}
            }
			else
			{
				$this->view->result = array('success'=>false, 'msg' => 'Sorry, Access denied', 'type' => 'danger');
			}
        }
        else
        {
            $this->_helper->redirector('index', 'inbox', 'employees');
        }
    }
 
    public function __destruct()
    {
        
    }
	
}