<?php

class Employees_Model_DbTable_ShortTimeOff extends Zend_Db_Table_Abstract
{
    protected $_dbPersonal    = null;
    protected $_dbFinancialYr = null;
    protected $_dbComment     = null;
    protected $_orgDF         = null;
    protected $_db            = null;
    protected $_ehrTables     = null;
	protected $_dbJob         = null;
	protected $_dbAttendance  = null;
	protected $_dbCommonFun   = null;
	protected $_dbPayslip     = null;
	protected $_orgDetails    = null;
	protected $_dbHrReports   = null;
    protected $_dbLeave        = null;
    
    public function init()
    {
        $this->_ehrTables     = new Application_Model_DbTable_Ehr();
        $this->_db            = Zend_Registry::get('subHrapp');
        $this->_dbPersonal    = new Employees_Model_DbTable_Personal();
        $this->_dbFinancialYr = new Default_Model_DbTable_FinancialYear();
        $this->_dbComment     = new Payroll_Model_DbTable_PayrollComment();
        $this->_dbJob         = new Employees_Model_DbTable_JobDetail();
		$this->_dbCommonFun   = new Application_Model_DbTable_CommonFunction();	
		$this->_dbAttendance  = new Employees_Model_DbTable_Attendance();	
		$this->_dbPayslip 	  = new Payroll_Model_DbTable_Payslip();
		$this->_dbHrReports   = new Reports_Model_DbTable_HrReports();
		$this->_dbLeave       = new Employees_Model_DbTable_Leave();
		
		$this->_orgDF         = $this->_ehrTables->orgDateformat();
	    if (Zend_Registry::isRegistered('orgDetails'))
			$this->_orgDetails = Zend_Registry::get('orgDetails');
    }

    /*******************************************Short Leave request *****************************************************************/
	
	public function listShortTimeOff($page, $rows, $sortField, $sortOrder, $searchAll, $searchArr, $shortTimeOffAccess)
	{
		$employeeName   = $searchArr['employeeName'];
		$requestFor		= $searchArr['requestFor'];
		$status         = $searchArr['status'];
		$startDateBegin = $searchArr['startDateBegin'];
		$startDateEnd   = $searchArr['startDateEnd'];
		$endDateBegin   = $searchArr['endDateBegin'];
		$endDateEnd     = $searchArr['endDateEnd'];
		$locationId 	= $searchArr['Location_Id'];
		$departmentId   = $searchArr['Department_Id'];
		$managerId 		= $searchArr['Manager_Id'];
		$serviceProviderId = $searchArr['serviceProviderId'];
		$salaryStartDate = $searchArr['salaryStartDate'];
		$salaryEndDate = $searchArr['salaryEndDate'];
		$payslipEmployeeIds = $searchArr['payslipEmployeeIds'];
		$earlyCheckoutShortTimeOff = $searchArr['Early_Checkout_Short_Time_Off'];
		$lateAttendanceShortTimeOff = $searchArr['Late_Attendance_Short_Time_Off'];
		
		switch ($sortField)
		{
			case 1: $sortField = 'EJ.User_Defined_EmpId'; break;
			case 2: $sortField = 'emp.Emp_First_Name'; break;
			case 3: $sortField = 'SLR.Request_For';break;
			case 4: $sortField = 'SLR.Start_Date_Time'; break;
			case 5: $sortField = 'SLR.End_Date_Time'; break;			
			case 6: $sortField = 'SLR.Approval_Status'; break;
			default: $sortField = 'SLR.Start_Date_Time'; break;
		}
		
		if (!empty($shortTimeOffAccess['FormName']))
        {
        	$formId = $this->_dbComment->getFormId($shortTimeOffAccess['FormName']);
        	 
        	$qryComment = $this->_db->select()->distinct()->from(array('Cm'=>$this->_ehrTables->comment), 'Parent_Id')
												->where('Cm.Parent_Id = SLR.Short_Time_Off_Id AND Cm.Form_Id='.$formId);
        }
		
		$qryShortLeaveRequest = $this->_db->select()->from(array('SLR'=>$this->_ehrTables->shortTimeOff),
									   array(new Zend_Db_Expr('SQL_CALC_FOUND_ROWS SLR.Short_Time_Off_Id as count'), 'SLR.Short_Time_Off_Id',
											 'SLR.Start_Date_Time', 'SLR.End_Date_Time', 'SLR.Total_Hours', 'SLR.Reason', 'SLR.Request_For',
											 'SLR.Alternate_Person', 'SLR.Contact_Details', 'SLR.Approval_Status', 'SLR.Added_By', 
											 'SLR.Approver_Id', 'Comment'=> new Zend_Db_Expr('('.$qryComment.')'),
											 'Log_Id'=>new Zend_Db_Expr("'".$shortTimeOffAccess['LogId']."'"),
											 'Admin'=>new Zend_Db_Expr("'".$shortTimeOffAccess['Admin']."'"),
											 new Zend_Db_Expr("DATE_FORMAT(SLR.Start_Date_Time,'".$this->_orgDF['sql']." %H:%i:%s') as View_Start_Date_Time"),
											 new Zend_Db_Expr("DATE_FORMAT(SLR.End_Date_Time,'".$this->_orgDF['sql']." %H:%i:%s') as View_End_Date_Time"),
											 new Zend_Db_Expr("DATE_FORMAT(SLR.Added_On,'".$this->_orgDF['sql']." %H:%i:%s') as Added_On"),
											 new Zend_Db_Expr("DATE_FORMAT(SLR.Updated_On,'".$this->_orgDF['sql']." %H:%i:%s') as Updated_On"),
											 new Zend_Db_Expr("DATE_FORMAT(SLR.Approved_On,'".$this->_orgDF['sql']." %H:%i:%s') as Approved_On")))
		
							->joinInner(array('emp'=>$this->_ehrTables->empPersonal),'emp.Employee_Id=SLR.Employee_Id',
										array(new Zend_Db_Expr("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) as Employee_Name"),
											  'emp.Employee_Id'))
	  
							->joinLeft(array('EA'=>$this->_ehrTables->attendance),'SLR.Employee_Id=EA.Employee_Id AND SLR.Short_Time_Off_Date = EA.Attendance_Date AND EA.Auto_Short_Time_Off = "Yes"',
										array('Late_Attendance' => new Zend_Db_Expr('CASE WHEN SLR.Reason = "Late Attendance" THEN "Yes" ELSE "No" END')))

							->joinLeft(array('EAS'=>$this->_ehrTables->employeeAttendanceSummary),'SLR.Short_Time_Off_Id = EAS.Early_Checkout_Short_Time_Off_Id',
										array('Early_Checkout' => new Zend_Db_Expr('CASE WHEN EAS.Early_Checkout_Short_Time_Off_Id > 0 THEN "Yes" ELSE "No" END'),'EAS.Early_Checkout_Hours'))
							
							->joinInner(array('emp1'=>$this->_ehrTables->empPersonal),'emp1.Employee_Id=SLR.Approver_Id',
										array(new Zend_Db_Expr("CONCAT(emp1.Emp_First_Name, ' ', emp1.Emp_Last_Name) as ApproverName")))
							
							->joinLeft(array('emp2'=>$this->_ehrTables->empPersonal),'emp2.Employee_Id=SLR.Alternate_Person',
										array(new Zend_Db_Expr("CONCAT(emp2.Emp_First_Name, ' ', emp2.Emp_Last_Name) as AlternatePersonName")))
							
							->joinLeft(array('EB'=>$this->_ehrTables->empPersonal),'EB.Employee_Id=SLR.Added_By',
									   array('Added_By_Name'=>new Zend_Db_Expr("CONCAT(EB.Emp_First_Name, ' ', EB.Emp_Last_Name)")))
							
							->joinLeft(array('UE'=>$this->_ehrTables->empPersonal),'UE.Employee_Id=SLR.Updated_By',
									   array('Updated_By_Name'=>new Zend_Db_Expr("CONCAT(UE.Emp_First_Name, ' ', UE.Emp_Last_Name)")))

							->joinLeft(array('emp3'=>$this->_ehrTables->empPersonal),'emp3.Employee_Id=SLR.Approved_By',
									   array(new Zend_Db_Expr("CONCAT(emp3.Emp_First_Name, ' ', emp3.Emp_Last_Name) as Approved_By_Name")))

						    ->joinInner(array('EJ'=>$this->_ehrTables->empJob),'emp.Employee_Id=EJ.Employee_Id',
									   array('User_Defined_EmpId' => new Zend_Db_Expr('CASE WHEN EJ.User_Defined_EmpId IS NULL THEN emp.Employee_Id ELSE EJ.User_Defined_EmpId END')))
							
							->order("$sortField $sortOrder")
							->limit($rows, $page);

		if(empty($shortTimeOffAccess['Admin']))
		{
			$qryEmployeeId = $this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))->where('Manager_Id = ?', $shortTimeOffAccess['LogId']);
			$getEmployeeId = $this->_db->fetchCol($qryEmployeeId);
			
			if ( $shortTimeOffAccess['Is_Manager'] == 1 && !empty($getEmployeeId))
			{
				if($this->_orgDetails['Immediate_Reportees_View_Only']==0)
				{
					$getEmployeeId = $this->_dbCommonFun->getMultiLevelManagerIds($shortTimeOffAccess['LogId'],1);
					array_push($getEmployeeId,$shortTimeOffAccess['LogId']);
					$qryShortLeaveRequest->where('SLR.Employee_Id IN (?)', $getEmployeeId);
				}
				else
				{
					$qryShortLeaveRequest->where('SLR.Employee_Id = :EmpId or SLR.Approver_Id = :EmpId or SLR.Employee_Id IN (?)', $getEmployeeId)
										->bind(array('EmpId'=>$shortTimeOffAccess['LogId']));
				}
			}
			else
			{
				$qryShortLeaveRequest->where('SLR.Employee_Id = ?', $shortTimeOffAccess['LogId']);
			}
		}
		
		if (!empty($searchAll) && $searchAll != null)
		{
		    $conditions = $this->_db->quoteInto(new Zend_Db_Expr('Concat(emp.Emp_First_Name," ",emp.Emp_Last_Name) Like ?'),"%$searchAll%");
			$conditions .= $this->_db->quoteInto('or EJ.User_Defined_EmpId Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or SLR.Request_For Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or SLR.Start_Date_Time Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or SLR.End_Date_Time Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or SLR.Total_Hours Like ?', "%$searchAll%");
			$conditions .= $this->_db->quoteInto('or SLR.Approval_Status Like ?', "%$searchAll%");
			
			$qryShortLeaveRequest->where($conditions);
		}
		
		if (! empty($employeeName) && preg_match('/^[a-zA-Z]/', $employeeName))
		{
            $qryShortLeaveRequest->where($this->_db->quoteInto(new Zend_Db_Expr('Concat(emp.Emp_First_Name," ",emp.Emp_Last_Name) Like ?'),"%$employeeName%"));
		}
        
		if (!empty($requestFor))
        {
			$qryShortLeaveRequest->where('SLR.Request_For = ?', $requestFor);
		}

		if (!empty($startDateBegin))
			$qryShortLeaveRequest->where('Date(SLR.Start_Date_Time) >= ?', $startDateBegin);
		
		if (!empty($startDateEnd))
			$qryShortLeaveRequest->where('Date(SLR.Start_Date_Time) <= ?', $startDateEnd);
			
		if (!empty($endDateBegin))
			$qryShortLeaveRequest->where('Date(SLR.End_Date_Time) >= ?', $endDateBegin);
		
		if (!empty($endDateEnd))
			$qryShortLeaveRequest->where('Date(SLR.End_Date_Time) <= ?', $endDateEnd);
		
        if (!empty($status))
        {
			if(is_array($status)){
				$qryShortLeaveRequest->where('SLR.Approval_Status IN (?)', $status);
			}else{
				$qryShortLeaveRequest->where('SLR.Approval_Status = ?', $status);
			}
		}
		
		if(!empty($serviceProviderId)&& $this->_orgDetails['Field_Force']==1)
        {
            $qryShortLeaveRequest->where('EJ.Service_Provider_Id = ?',$serviceProviderId);
        }

		if(!empty($locationId))
		{
			$qryShortLeaveRequest->where('EJ.Location_Id = ?',$locationId);
		}

		if(!empty($departmentId))
		{
			$qryShortLeaveRequest->where('EJ.Department_Id = ?',$departmentId);
		}

		if (!empty($managerId))
		{
			$qryShortLeaveRequest->where('SLR.Approver_Id = ?',$managerId);
		}

		if (!empty($earlyCheckoutShortTimeOff))
		{
			if($earlyCheckoutShortTimeOff == 2)
			{
				$qryShortLeaveRequest->where('EAS.Early_Checkout_Short_Time_Off_Id IS NULL or EAS.Early_Checkout_Short_Time_Off_Id = ?', 0);
			}else{
				$qryShortLeaveRequest->where('EAS.Early_Checkout_Short_Time_Off_Id > ?', 0);
			}
		}
		
		if (!empty($lateAttendanceShortTimeOff))
		{
			if($lateAttendanceShortTimeOff == 1)
			{
				$qryShortLeaveRequest->where('EA.Auto_Short_Time_Off = "Yes" AND SLR.Reason="Late Attendance"');
			}else{
				$qryShortLeaveRequest->where('EA.Auto_Short_Time_Off IS NULL OR EA.Auto_Short_Time_Off != "Yes"');
			}
		}

		$qryShortLeaveRequest = $this->_dbCommonFun->getDivisionDetails($qryShortLeaveRequest,'EJ.Department_Id');

		if(!empty($shortTimeOffAccess['Admin']))
		{
			$qryShortLeaveRequest = $this->_dbCommonFun->formServiceProviderQuery($qryShortLeaveRequest,'EJ.Service_Provider_Id',$shortTimeOffAccess['LogId']);
		}

		if(!empty($salaryStartDate) && !empty($salaryEndDate) && !empty($payslipEmployeeIds) && !empty($shortTimeOffAccess['Admin']))
        {
			$qryShortLeaveRequest->where('SLR.Employee_Id IN (?)', $payslipEmployeeIds);
			$qryShortLeaveRequest->where('SLR.Short_Time_Off_Date >= ?', $salaryStartDate);
			$qryShortLeaveRequest->where('SLR.Short_Time_Off_Date <= ?', $salaryEndDate);
        }

		$shortLeaveRequest = $this->_db->fetchAll($qryShortLeaveRequest);
		
		$iTotalDisplay = $this->_db->fetchOne('select FOUND_ROWS()');
		
		$qryITotal = $this->_db->select()->from($this->_ehrTables->shortTimeOff,
																  new Zend_Db_Expr('COUNT(Short_Time_Off_Id)'));
		
		if(empty($shortTimeOffAccess['Admin']))
		{
			$qryEmployeeId = $this->_db->select()->from($this->_ehrTables->empJob, array('Employee_Id'))->where('Manager_Id = ?', $shortTimeOffAccess['LogId']);
			$getEmployeeId = $this->_db->fetchCol($qryEmployeeId);

			if ($shortTimeOffAccess['Is_Manager'] == 1 && !empty($getEmployeeId))
			{
				if($this->_orgDetails['Immediate_Reportees_View_Only']==0)
				{
					$getEmployeeId = $this->_dbCommonFun->getMultiLevelManagerIds($shortTimeOffAccess['LogId'], 1);
					array_push($getEmployeeId,$shortTimeOffAccess['LogId']);
					$qryITotal->where('Employee_Id IN (?)', $getEmployeeId);
				}
				else
				{
					$qryITotal->where('Employee_Id = :EmpId or Approver_Id = :EmpId or Employee_Id IN (?)', $getEmployeeId)
							->bind(array('EmpId'=>$shortTimeOffAccess['LogId']));
				}
			}
			else
			{
				$qryITotal->where('Employee_Id = ?', $shortTimeOffAccess['LogId']);
			}
		}
		
		$iTotal = $this->_db->fetchOne($qryITotal);

		
		return array("iTotalRecords" => $iTotal, "iTotalDisplayRecords" => $iTotalDisplay, "aaData" => $shortLeaveRequest);
	}
	
	public function updateShortTimeOff($shortTimeOffArr, $comments, $otherDetailsForUpdate)
	{
		$sessionId = $otherDetailsForUpdate['sessionId'];
		$formName = $otherDetailsForUpdate['formName'];
		$customFormName = $otherDetailsForUpdate['customFormName'];
		$adminRole = $otherDetailsForUpdate['adminRole'];
		$shortTimeOffSettings = $otherDetailsForUpdate['shortTimeOffSettings'];		
		$shiftDetails = $otherDetailsForUpdate['shiftDetails'];

		if(strtolower($shortTimeOffArr['Request_For']) === 'on duty' && isset($shortTimeOffSettings['Shift_Margin'])
		&& strtolower($shortTimeOffSettings['Shift_Margin']) === 'yes'){
			$scheduleType = 'margin';
			$shortTimeOffMinTime = $shiftDetails['Consideration_From'];
			$shortTimeOffMaxTime = $shiftDetails['Consideration_To'];
		}else{
			$scheduleType = 'workschedule';
			$shortTimeOffMinTime = $shiftDetails['Regular_From'];
			$shortTimeOffMaxTime = $shiftDetails['Regular_To'];
		}
		if($shortTimeOffMinTime <= $shortTimeOffArr['Start_Date_Time'] && $shortTimeOffMaxTime >= $shortTimeOffArr['Start_Date_Time'] &&
			$shortTimeOffMinTime <= $shortTimeOffArr['End_Date_Time'] && $shortTimeOffMaxTime >= $shortTimeOffArr['End_Date_Time'])
		{
			
			$betweenDates = $this->_db->quoteInto('(Start_Date_Time <= ? AND ', $shortTimeOffArr['Start_Date_Time']).
							$this->_db->quoteInto(' End_Date_Time >= ?) OR ', $shortTimeOffArr['Start_Date_Time']);
			
			$betweenDates .= $this->_db->quoteInto('(Start_Date_Time <= ? AND ', $shortTimeOffArr['End_Date_Time']).
								$this->_db->quoteInto(' End_Date_Time >= ?)', $shortTimeOffArr['End_Date_Time']);
			
			$qryShortTimeOff = $this->_db->select()->from($this->_ehrTables->shortTimeOff, new Zend_Db_Expr('Count(Short_Time_Off_Id)'))
										->where('Employee_Id = ?', $shortTimeOffArr['Employee_Id'])
										->where('Approval_Status NOT IN (?)', array('Rejected','Cancelled'))
										->where($betweenDates);
			
			if (!empty($shortTimeOffArr['Short_Time_Off_Id']))
			{
				$qryShortTimeOff->where('Short_Time_Off_Id != ?', $shortTimeOffArr['Short_Time_Off_Id']);
			}
			
			$isExist = $this->_db->fetchOne($qryShortTimeOff);
			
			if($isExist == 0)
			{
				$startDate = date('Y-m-d', strtotime($shiftDetails['Regular_From']));

				$checkShiftExist =  $this->_dbAttendance->checkShiftEnabled($shortTimeOffArr['Employee_Id']); 
				
				if($checkShiftExist=='Shift Roster')
				{
					$businessWorkingDays = $this->_dbPayslip->getBusinessWorkingDays($startDate, $startDate, $shortTimeOffArr['Employee_Id'],NULL,1,'leaves');
				}
				else 
				{
					$businessWorkingDays = $this->_dbPayslip->getBusinessWorkingDays($startDate, $startDate, $shortTimeOffArr['Employee_Id'],'',1);
				}


				if($businessWorkingDays == 0)
				{
					$orgHolidayDates = $this->_dbPayslip->orgHolidayDates($shortTimeOffArr['Employee_Id'], $startDate, $startDate,'fetchCol');
					
					if(count($orgHolidayDates) > 0)
					{
						return array('success' => false, 'msg'=>"Short time off can't be applied on holiday", 'type'=>'info');		
					}
					else 
					{
						return array('success' => false, 'msg'=>"Short time off can't be applied on Week off", 'type'=>'info');		
					}
				}
				else 
				{
					$regularFrom 	= date($this->_orgDF['php'].' H:i:s',strtotime($shiftDetails['Regular_From']));
					$lunchBreakFrom = date($this->_orgDF['php'].' H:i:s',strtotime($shiftDetails['Lunch_Break_From']));
					$lunchBreakTo 	= date($this->_orgDF['php'].' H:i:s',strtotime($shiftDetails['Lunch_Break_To']));
					$regularTo 		= date($this->_orgDF['php'].' H:i:s',strtotime($shiftDetails['Regular_To']));
					$regularFromDate = date('Y-m-d',strtotime($shiftDetails['Regular_From']));

					$shortTimeOffAdvanceNotificationSettings = isset($shortTimeOffSettings['Settings_Details']) ? $shortTimeOffSettings['Settings_Details'] : $shortTimeOffSettings;

					$advanceNotificationDetails = $this->_dbCommonFun->getAdvanceNotificationMinDate($shortTimeOffAdvanceNotificationSettings,$adminRole,$sessionId);
					if(empty($advanceNotificationDetails['minimumDateToApply']) || (!empty($advanceNotificationDetails['minimumDateToApply']) && strtotime($regularFromDate) >= strtotime($advanceNotificationDetails['minimumDateToApply']))){
						$lateAttendanceExist= $attendanceShortageIgnored = 0;
						if (!empty($shortTimeOffArr['Short_Time_Off_Id'])){
							$attendanceStatus=array('Draft', 'Applied', 'Returned', 'Approved');
							$lateAttendanceCount =  $this->_dbAttendance->getLateAttendanceCount($shortTimeOffArr['Employee_Id'],$regularFromDate,$regularFromDate,array(1,2),$attendanceStatus);
							$ignoredAttendanceShortageRecords =  $this->_dbHrReports->getAllIgnoreAttendanceShortageDetails(array($shortTimeOffArr['Employee_Id']),$regularFromDate,$regularFromDate);

							if(!empty($lateAttendanceCount) && count($lateAttendanceCount) > 0){
								$lateAttendanceExist = 1;
							}
							if(!empty($ignoredAttendanceShortageRecords) && count($ignoredAttendanceShortageRecords) > 0){
								$attendanceShortageIgnored = 1;
							}
						}else{
							$lateAttendanceExist= $attendanceShortageIgnored = 0;
						}
						if(!empty($lateAttendanceExist)){
							if($adminRole == "admin"){
								$lateAttendanceErrorMessage = "Late attendance exist for the regular duration from ".$regularFrom.' To: '.$regularTo.'.Please delete the attendance record to update the '.$formName;
							}else{
								$lateAttendanceErrorMessage = "Late attendance exist for the regular duration from ".$regularFrom.' To: '.$regularTo.'.Please contact your HR admin';
							}
							return array('success' => false, 'msg'=> $lateAttendanceErrorMessage, 'type'=>'info');			
						}else if(!empty($attendanceShortageIgnored)){
							if($adminRole == "admin"){
								$attendanceShortageErrorMessage = "Attendance shortage record ignored for the regular duration from ".$regularFrom.' To: '.$regularTo.'.Please delete the attendance record to update the '.$formName;
							}else{
								$attendanceShortageErrorMessage = "Attendance shortage record ignored for the regular duration from ".$regularFrom.' To: '.$regularTo.'.Please contact your HR admin';
							}
							return array('success' => false, 'msg'=>$attendanceShortageErrorMessage, 'type'=>'info');			
						}else{
							$leaveDetails           = $this->_dbHrReports->getLeaveDetails($shortTimeOffArr['Employee_Id'],$startDate,$startDate,'dashboardNoAttendance');
							$compensatoryOffDetails = $this->_dbHrReports->getCompensatoryOffDetails($shortTimeOffArr['Employee_Id'],$startDate,$startDate,'dashboardNoAttendance');
							$totalLeaveDuration 	= 0;
							$totalCompOffDuration 	= 0;
							$totalDuration 			= 0;

							foreach($leaveDetails as $leave)
							{
								$totalLeaveDuration += $leave['Duration'];
							}

							foreach($compensatoryOffDetails as $compOff)
							{
								$totalCompOffDuration += $compOff['Duration'];
							}

							$totalDuration  = $totalCompOffDuration + $totalLeaveDuration;

							if(!empty($compensatoryOffDetails) && $totalCompOffDuration==1)
							{
								return array('success' => false, 'msg'=>"Comp off is applied for full day From :".$regularFrom.' To: '.$regularTo, 'type'=>'info');			
							}
							elseif(!empty($leaveDetails)  && $totalLeaveDuration==1)
							{
								return array('success' => false, 'msg'=>'Leave is applied for full day From :'.$regularFrom.' To: '.$regularTo, 'type'=>'info');	
							}
							elseif($totalDuration==1)
							{
								return array('success' => false, 'msg'=>'Comp off and Leave is applied on this day From :'.$regularFrom.' To: '.$regularTo, 'type'=>'info');	
							}
							else 
							{
								if($totalDuration==0.5)
								{
									foreach($leaveDetails as $leave)
									{
										$leaveCompOffMessage = $this->leaveCompOffMessage($shortTimeOffArr,$shiftDetails,'Leave',$leave['Leave_Period']);
										
										if($leaveCompOffMessage['success']==false)
										{
											return $leaveCompOffMessage;
											break;
										}
									}

									foreach($compensatoryOffDetails as $compOff)
									{
										$leaveCompOffMessage = $this->leaveCompOffMessage($shortTimeOffArr,$shiftDetails,'Comp off',$compOff['Period']);
										if($leaveCompOffMessage['success']==false)
										{
											return $leaveCompOffMessage;
											break;
										}
									}
								}else if($totalDuration==0.25)
								{
									foreach($leaveDetails as $leave)
									{
										$leaveCompOffMessage = $this->leaveCompOffMessage($shortTimeOffArr,$shiftDetails,'Leave',$leave['Leave_Period']);
										
										if($leaveCompOffMessage['success']==false)
										{
											return $leaveCompOffMessage;
											break;
										}
									}
								}
								
								$shortTimeOffArr['Short_Time_Off_Date'] = date('Y-m-d',strtotime($shiftDetails['Regular_From']));
								if (!empty($shortTimeOffArr['Short_Time_Off_Id']))
								{
									$shortTimeOffArr['Updated_On'] = date('Y-m-d H:i:s');
									$shortTimeOffArr['Updated_By'] = $sessionId;
								}else
								{									
									$shortTimeOffArr['Added_On'] = date('Y-m-d H:i:s');
									$shortTimeOffArr['Added_By'] = $sessionId;
								}

								$enableworkflow = $this->validateShortTimeOffWorkflowEnabled();
								if($enableworkflow){
									$workflowResult = $this->initiateShortTimeOffWorkflow($shortTimeOffArr, $sessionId);
									
									if (!$workflowResult['success']) {
										return $workflowResult;
									} else {
										$newWorkFlowInitiatedId = $workflowResult['newWorkFlowInitiatedId'];
										$shortTimeOffArr['Process_Instance_Id'] = $newWorkFlowInitiatedId;
									}
								}else{
									$shortTimeOffArr['Process_Instance_Id'] = null;
								}

								if (!empty($shortTimeOffArr['Short_Time_Off_Id']))
								{
									$shortTimeOffId = $shortTimeOffArr['Short_Time_Off_Id'];

									$oldProcessInstanceId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->shortTimeOff, array('Process_Instance_Id'))
																->where('Short_Time_Off_Id = ?',$shortTimeOffId));

									$action = 'Edit';
									
									
									$updated = $this->_db->update($this->_ehrTables->shortTimeOff, $shortTimeOffArr, array('Short_Time_Off_Id = '. $shortTimeOffArr['Short_Time_Off_Id']));	

									if(!empty($oldProcessInstanceId))
										$this->_dbCommonFun->deleteWorkflowDetails($oldProcessInstanceId);
								}
								else
								{
									$action = 'Add';
									
									$updated = $this->_db->insert($this->_ehrTables->shortTimeOff, $shortTimeOffArr);
									
									if($updated)
									{
										$shortTimeOffId = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->attendance, array(new Zend_Db_Expr('Max(Attendance_Id)'))));	
									}
								}
								
								if($updated)
								{
									if (!empty($comments))
									{
										$addComment = array('Form_Id'         => $this->_dbComment->getFormId($formName),
															'Emp_Comment'     => htmlentities(strip_tags(trim($comments))),
															'Parent_Id'       => $shortTimeOffId,
															'Approval_Status' => $shortTimeOffArr['Approval_Status'],
															'Employee_Id'     => $sessionId,
															'Added_On'        => date('Y-m-d H:i:s'));
										
										$this->_db->insert($this->_ehrTables->comment,$addComment);
									}

									if($shortTimeOffArr['Request_For'] == 'Permission'){
										$attendanceSummaryDetails = [];
										$attendanceSummaryDetails[] = $shortTimeOffArr;
										$this->_dbCommonFun->triggerAttendanceSummaryStepFunction($attendanceSummaryDetails,'shorttimeoff');
									}
								}
								
								$result = $this->_dbCommonFun->updateResult (array('updated'    => $updated,
																			'action'         => $action,
																			'trackingColumn' => $shortTimeOffId,
																			'formName'       => $formName,
																			'sessionId'      => $sessionId,
																			'tableName'      => $this->_ehrTables->shortTimeOff));
								$result['Process_Instance_Id'] = $shortTimeOffArr['Process_Instance_Id'];
								return $result;
							}
						}
					}else{
						return array('success' => false, 'msg'=>"You can apply for ".$customFormName." starting from ".date('d F Y', strtotime($regularFromDate))." as per the advance notification rule.", 'type'=>'info');// Output date example: 18 May 2025
					}
				}
			}
			else
			{
				return array('success' => false, 'msg'=>'Short time off already exist on this duration', 'type'=>'info');
			}
		}
		else
		{
			$regularFrom = date($this->_orgDF['php'].' H:i:s' ,strtotime($shortTimeOffMinTime));
			$regularTo   = date($this->_orgDF['php'].' H:i:s' ,strtotime($shortTimeOffMaxTime));

			return array('success' => false, 'msg'=>'Short time off start and end time should be in '.$scheduleType.' hours('.$regularFrom.' to '.$regularTo.')', 'type'=>'info');
		}
	}

	// Delete short time off
	public function deleteShortTimeOff ($shortTimeOffId, $sessionId, $formName, $customFormName)
    {
		$deleted = 0;
		
		$shortTimeOffRow = $this->_db->fetchRow($this->_db->select()->from(array('SLR'=>$this->_ehrTables->shortTimeOff),array('SLR.*'))
															->joinLeft(array('EA'=>$this->_ehrTables->attendance),'SLR.Employee_Id=EA.Employee_Id AND SLR.Short_Time_Off_Date = EA.Attendance_Date AND EA.Auto_Short_Time_Off = "Yes"',array('Late_Attendance' => new Zend_Db_Expr('CASE WHEN SLR.Reason = "Late Attendance" THEN "Yes" ELSE "No" END')))
															->joinLeft(array('EAS'=>$this->_ehrTables->employeeAttendanceSummary),'SLR.Short_Time_Off_Id = EAS.Early_Checkout_Short_Time_Off_Id',array('Early_Checkout' => new Zend_Db_Expr('CASE WHEN EAS.Early_Checkout_Short_Time_Off_Id > 0 THEN "Yes" ELSE "No" END')))
															->where('SLR.Short_Time_Off_Id = ?', $shortTimeOffId));
		
		if ($shortTimeOffRow['Lock_Flag'] == 0)
		{
			if($shortTimeOffRow['Approval_Status'] != 'Approved' && $shortTimeOffRow['Approval_Status'] != 'Cancel Applied')
			{
				if($shortTimeOffRow['Late_Attendance'] = 'No')
				{
					if($shortTimeOffRow['Early_Checkout'] = 'No')
					{
						unset($shortTimeOffRow['Late_Attendance']);
						unset($shortTimeOffRow['Early_Checkout']);
						$shortTimeOffRow['Deleted_On'] = date('Y-m-d H:i:s');
						$shortTimeOffRow['Deleted_By'] = $sessionId;
						$inserted 				       = $this->_db->insert($this->_ehrTables->archiveShortTimeOff, $shortTimeOffRow);

						$deleted = $this->_db->delete($this->_ehrTables->shortTimeOff, 'Short_Time_Off_Id ='.(int)$shortTimeOffId);
						
						if ($deleted)
						{
							if(!empty($shortTimeOffRow['Process_Instance_Id']))
								$this->_dbCommonFun->deleteWorkflowDetails($shortTimeOffRow['Process_Instance_Id']);

							$this->_dbComment->deleteComment($shortTimeOffId, $formName);
						}
					}
					else
					{
						return array('success' => false, 'msg'=>"Unable to delete ".$customFormName." record.Early checkout is associated with this record", 'type'=>'info');
					}
				}
				else
				{
					return array('success' => false, 'msg'=>"Unable to delete ".$customFormName." record.Late attendance is associated with this record", 'type'=>'info');
				}
			}
			else
			{
				return array('success' => false, 'msg'=>"Unable to delete ".$customFormName." record.Approved or cancel applied record cannot be deleted", 'type'=>'info');
			}
		}
		
		$result = $this->_dbCommonFun->deleteRecord (array('deleted'        => $deleted,
														'tableName'      => $this->_ehrTables->shortTimeOff,
														'lockFlag'       => $shortTimeOffRow['Lock_Flag'],
														'formName'       => $customFormName,
														'trackingColumn' => $shortTimeOffId,
														'sessionId'      => $sessionId));

		$result['shortTimeOffDetails'] = $shortTimeOffRow;
		return $result;
    }
	
	public function shortTimeOffEmployee($shortTimeOffId)
	{
		$qryEmpId = $this->_db->select()->from($this->_ehrTables->shortTimeOff, array('Employee_Id'))
								->where('Short_Time_Off_Id = ?', $shortTimeOffId);
		
		return $this->_db->fetchRow($this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal),
												 array('P.Employee_Id','Employee_Name'=>new Zend_Db_Expr("CONCAT(P.Emp_First_Name, ' ', P.Emp_Last_Name)")))
									
									->joinInner(array('J'=>$this->_ehrTables->empJob), 'P.Employee_Id=J.Employee_Id', array())
									
									->where('J.Emp_Status Like ?', 'Active')
									->where('P.Employee_Id = ?', $qryEmpId)
									->where('P.Form_Status = 1'));
	}
	
	public function getValidateShortTimeOff($employeeId)
	{
		$shortTimeOffSettingsResponse = $this->listShortLeaveRequestSettings($employeeId);
		$onDutySettings 			 = $this->listOnDutySettings();

		$shortTimeOffSettingsArray   = array();

		$resultArr = array('success' => false,
						   'Msg' => '');
		
		// check on-duty settings is configured or not
		if(!empty($onDutySettings)) {
			if($onDutySettings['Coverage'] === 'Custom Group') {
				$customGroupRow = $this->_db->fetchRow($this->_db->select()
											->from($this->_ehrTables->customEmployeeGroupEmployees)
											->where('Group_Id = ?', $onDutySettings['Custom_Group_Id'])
											->where('Employee_Id = ?', $employeeId)
											->where("Type IN (?)", array('Default','AdditionalInclusion')));
				if(!empty($customGroupRow)) {
					array_push($shortTimeOffSettingsArray, 'On Duty');
				}
			} else {
				// when coverage is not custom group then obviously it was organization coverage, then we can assume on-duty was exist
				array_push($shortTimeOffSettingsArray, 'On Duty');
			}
		}
		$shortTimeOffSettingsDetails = $shortTimeOffSettingsResponse['Settings_Details'];
		// check permission settings is configured or not
		if(!empty($shortTimeOffSettingsDetails)) {
			$empDetails = $this->getEmployeeDetails($employeeId);
			$dateDiff = (strtotime((date('Y-m-d'))) - strtotime($empDetails['Date_Of_Join']));
			$totalDateDifference = floor($dateDiff / (60 * 60 * 24));				
			$resultArr['PermissionSettings'] = array(
				'MinDurationPerRequest' =>  date('H:i', mktime(0,$shortTimeOffSettingsDetails['Min_Short_Time_Per_Request'])),
				'MaxDurationPerRequest' =>  date('H:i', mktime(0,$shortTimeOffSettingsDetails['Max_Short_Time_Per_Request'])),
				'GenderValid' => $shortTimeOffSettingsDetails['Gender'] == "All" || ($shortTimeOffSettingsDetails['Gender'] == $empDetails['Gender']),
				'Gender' => $shortTimeOffSettingsDetails['Gender'],
				'LeaveActivationDaysValid' => $shortTimeOffSettingsDetails['Leave_Activation_Days'] < $totalDateDifference,
				'LeaveActivationDays' => $shortTimeOffSettingsDetails['Leave_Activation_Days'],
			);
			array_push($shortTimeOffSettingsArray, 'Permission');
		}
		$resultArr['onDutySettings'] = $onDutySettings;
		if(count($shortTimeOffSettingsArray) > 0) {
			$resultArr['success'] = true;
			$resultArr['SettingsList'] = $shortTimeOffSettingsArray;
		} else {
			if (empty($onDutySettings)) {
				$resultArr['Msg'] = "Kindly update the on duty settings";
			} else {
				$resultArr['Msg'] = "Kindly update the permission settings and verify that the employee is part of the short time off custom group if the coverage applies to a custom group";
			}
		}
		
		return $resultArr;
	}
	
	public function getEmployeeDetails($employeeId)
	{
		return $this->_db->fetchRow($this->_db->select()->from(array('P'=>$this->_ehrTables->empPersonal), array('P.Gender'))
									
									->joinInner(array('J'=>$this->_ehrTables->empJob), 'P.Employee_Id=J.Employee_Id', array('J.Date_Of_Join'))
									
									->where('P.Employee_Id = ?', $employeeId)
									->where('P.Form_Status = 1')
									->where('J.Emp_Status Like ?', 'Active'));
	}
	
	public function statusUpdateShortTimeOff($commentArray, $formName, $customFormName)
	{
		$shortTimeOffDetails = $this->getShortTimeOffDetails(array($commentArray['Parent_Id']));
		$ckManagerStatus = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->shortTimeOff, new Zend_Db_Expr('count(Employee_Id)'))
													->where('Approval_Status = ?', $commentArray['Approval_Status'])
													->where('Short_Time_Off_Id = ?', $commentArray['Parent_Id'])
													->where('Approver_Id = ?', $commentArray['Employee_Id']));
		$enableworkflow = $this->validateShortTimeOffWorkflowEnabled();
		if($ckManagerStatus <= 0 || $enableworkflow)
		{
			$isShortTimeStatusUpdated = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->shortTimeOff, new Zend_Db_Expr('count(Short_Time_Off_Id)'))
													->where('Approval_Status = ?', $commentArray['Approval_Status'])
													->where('Short_Time_Off_Id = ?', $commentArray['Parent_Id']));

			if(!empty($isShortTimeStatusUpdated)){
				return array('success' => false, 'msg'=>'Status already updated', 'type'=>'info');
			} else {
				$statusUpdateDetails = array(
					'Approval_Status' => $commentArray['Approval_Status'],
					'Approved_By' => $commentArray['Employee_Id'],
					'Approved_On' => date('Y-m-d H:i:s')
				);

				if($enableworkflow && strtolower($statusUpdateDetails['Approval_Status']) == "cancel applied") {
					$shortTimeOffDetails[0]['Approval_Status'] = $statusUpdateDetails['Approval_Status'];
					$shortTimeOffDetails[0]['Approved_By'] = $statusUpdateDetails['Approved_By'];
					$shortTimeOffDetails[0]['Approved_On'] = $statusUpdateDetails['Approved_On'];

					$workflowResult = $this->initiateShortTimeOffWorkflow($shortTimeOffDetails[0], $commentArray['Employee_Id']);
					if (!$workflowResult['success']) {
						return $workflowResult;
					} else {
						$newWorkFlowInitiatedId = $workflowResult['newWorkFlowInitiatedId'];
						$statusUpdateDetails['Process_Instance_Id'] = $newWorkFlowInitiatedId;
					}
				}
				
				$updated = $this->_db->update($this->_ehrTables->shortTimeOff, $statusUpdateDetails ,
												'Short_Time_Off_Id = ' . $commentArray['Parent_Id']);
				
				if(!empty($commentArray['Emp_Comment']) && $updated)
				{
					$commentArray['Form_Id']  = $this->_dbComment->getFormId($formName);
					$commentArray['Added_On'] = date('Y-m-d H:i:s');
					
					$this->_db->insert($this->_ehrTables->comment,$commentArray);
				}
				
				$result = $this->_dbCommonFun->updateResult (array('updated'        => $updated,
														'action'         => 'Edit',
														'trackingColumn' => $commentArray['Parent_Id'].','.$commentArray['Approval_Status'],
														'formName'       => $customFormName.' Status',
														'sessionId'      => $commentArray['Employee_Id'],
														'tableName'      => $this->_ehrTables->shortTimeOff));
				$result['Enable_Workflow'] = $enableworkflow;
				return $result;
			}
		}
		else
		{
			return array('success' => false, 'msg'=>'Status cannot be updated', 'type'=>'info');
		}
	}
	
	public function getShortTimeOffEmpDetails($shortTimeOffArr,$shortTimeOffPeriod,$shortTimeOffBalancePresentation)
	{
		$employeeId         = $shortTimeOffArr['Employee_Id']; 
		$startDateTime      = $shortTimeOffArr['Start_Date_Time']; 
		$shortTimeOffId     = $shortTimeOffArr['Short_Time_Off_Id']; 
		// convert applied total hours as secs
		$appliedTotalHoursInSecs = $this->_dbCommonFun->timeToSec($shortTimeOffArr['Total_Hours']);

		$startDate = "";
		$endDate = "";
		// convert the applied short time off date as per the work schedule
		$shiftDetails = $this->_dbAttendance->getCurrentWorkScheduleDetails($employeeId, $startDateTime);

		//echo $shiftDetails['Regular_From'];
		// If we get 'Regular_From' from the above function we can use that, otherwise we can use whatever user applied
		$shortTimeOffAppliedDate  = !empty($shiftDetails) ? date('Y-m-d',strtotime($shiftDetails['Regular_From'])) : date('Y-m-d',strtotime($startDateTime));

		// check short time configuration period
		if ($shortTimeOffPeriod === "Per Month") {
			//get the salary start date and end date based on short time off start date
			$salaryDateDetails  	= $this->_dbPayslip->getSalaryDateRange(date('m',strtotime($shortTimeOffAppliedDate)), date('Y',strtotime($shortTimeOffAppliedDate)),strtotime($shortTimeOffAppliedDate));
			$startDate  	    	= $salaryDateDetails['Salary_Date'];
			$endDate  				= $salaryDateDetails['Last_SalaryDate'];
		} else {
			$yearRange 	= $this->_dbCommonFun->getDateRangeBasedOnDuration($shortTimeOffAppliedDate,$shortTimeOffPeriod);
			$startDate 	= $yearRange["From_Date"];
			$endDate 	= $yearRange["To_Date"];
		}
		$startDate = date('Y-m-d H:i:s',strtotime("$startDate 00:00:00"));
		$endDate   = date('Y-m-d H:i:s',strtotime("$endDate 23:59:59"));

		$startWhereQry = $this->_db->quoteInto('Start_Date_Time >= ? AND ', date('Y-m-d H:i:s', strtotime($startDate)))
							.$this->_db->quoteInto('End_Date_Time <= ?', date('Y-m-d H:i:s', strtotime($endDate)));
						
		$qryShortTimeDetails = $this->_db->select()->from($this->_ehrTables->shortTimeOff,array(new Zend_Db_Expr('COUNT(Short_Time_Off_Id) as TotalCount'),
															   		new Zend_Db_Expr('SUM( TIME_TO_SEC(Total_Hours)) as TotalTime')))
										->where('Employee_Id = ?', $employeeId)
										->where('Request_For = ?', "Permission")
										->where('Approval_Status IN (?)', array('Applied', 'Approved', 'Cancel Applied','Returned'))
										->where($startWhereQry);

		if (!empty($shortTimeOffId))
		{
			$qryShortTimeDetails->where('Short_Time_Off_Id != ?', $shortTimeOffId);
		}								
		
        $shortTimeOffDetails = $this->_db->fetchRow($qryShortTimeDetails);
		
		if(!empty($shortTimeOffDetails))
		{
			$shortTimeOffCount = $shortTimeOffDetails['TotalCount'];
			$shortTimeOffTime  = $shortTimeOffDetails['TotalTime'];
		}
		else
		{
			$shortTimeOffCount = 0;
			$shortTimeOffTime  = 0;
		}

		if($shortTimeOffBalancePresentation==='Yes')
		{
			$totalCount 	 = $shortTimeOffCount;
			$totalTimeInSecs = $shortTimeOffTime;
		}
		else
		{
			$totalCount = $shortTimeOffCount + 1;
			// add the current applied total hours with all the applied total hours of the employee in salary date range
			$totalTimeInSecs = $shortTimeOffTime + $appliedTotalHoursInSecs;
		}
		
		$employeeDateOfJoin = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->empJob,array('Date_Of_Join'))
										->where('Employee_Id = ?', $employeeId));

        return array('totalTimeInSecs'=>$totalTimeInSecs,'totalCount'=>$totalCount,'appliedTotalHoursInSecs'=>$appliedTotalHoursInSecs,'employeeDateOfJoin'=>$employeeDateOfJoin);
	}
	
	public function getShortTimeOffApproval($shortTimeOffId)
	{
		return $this->_db->fetchRow($this->_db->select()->from($this->_ehrTables->shortTimeOff,array('Approver_Id','Approval_Status'))
									->where('Employee_Id = ?', $shortTimeOffId));
	}
	
	/*******************************************Short Leave request setting*********************************************************/
	
	//list short leave request settings
	public function listShortLeaveRequestSettings($employeeId)
	{
		$shortTimeOffPermissionFormId = 129;
		$shortTimeOffCoverage = $this->_dbCommonFun->getCoverageForFormId($shortTimeOffPermissionFormId);
		$shortTimeOffDetailsQuery = $this->_db->select()->from(array('SL'=>$this->_ehrTables->shortLeaveRequestSettings),
								array('SL.Maximum_Limit','SL.Min_Short_Time_Per_Request','SL.Max_Short_Time_Per_Request',
										'SL.Frequency','SL.Gender', 'SL.Leave_Activation_Days','SL.Coverage_For_Alternate_Person',
										'SL.Period','SL.Minimum_Duration','SL.Maximum_Duration','SL.Limit_By','Short_Time_Off_Activation_After','SL.Total_Duration',
										'SL.Advance_Notification','SL.Advance_Notification_Days','SL.Include_Current_Date_In_Advance_Notification'
									));
		if(strtolower($shortTimeOffCoverage) === 'custom group'){
			$customGroupParentId = $this->_dbPayslip->getCustomGroupParentId($employeeId, $shortTimeOffPermissionFormId);
			if (!empty($customGroupParentId)) {
				$shortTimeOffDetailsQuery->where('SL.Short_Leave_settings_Id IN (?)', $customGroupParentId);
				$result = $this->_db->fetchRow($shortTimeOffDetailsQuery);
			}else{
				$result = '';
			}
		}else{
			$result = $this->_db->fetchRow($shortTimeOffDetailsQuery);	
		}
		$settingsResponse = array(
			'Coverage' => $shortTimeOffCoverage,
			'Settings_Details' => $result
		);
		return $settingsResponse;
    }

	// list on duty settings
	public function listOnDutySettings()
	{
		return $this->_db->fetchRow($this->_db->select()->from(array('ODS'=>$this->_ehrTables->onDutySettings),
															   array('ODS.Coverage','ODS.Custom_Group_Id','ODS.Shift_Margin')));
    }
	
	//update short leave request settings
	public function updateShortTimeOffSettings($shortLeaveSettingsArr, $sessionId, $formName)
	{
		$shortLeaveSettingsCount = $this->_db->fetchOne($this->_db->select()->from($this->_ehrTables->shortLeaveRequestSettings,
																	new Zend_Db_Expr('COUNT(Short_Leave_settings_Id)')));

		$shortLeaveSettingsArr['Updated_By'] = $sessionId;
		$shortLeaveSettingsArr['Updated_On'] = date('Y-m-d H:i:s');
		if($shortLeaveSettingsCount == 1)
		{
			$action = 'Edit';
			$updated = $this->_db->update($this->_ehrTables->shortLeaveRequestSettings, $shortLeaveSettingsArr, array('Short_Leave_settings_Id = '. 1));	
		}
		else
		{
			$action = 'Add';
			$updated = $this->_db->insert($this->_ehrTables->shortLeaveRequestSettings, $shortLeaveSettingsArr);
		}
		
		return $this->_dbCommonFun->updateResult (array('updated'        => $updated,
														'action'         => $action,
														'trackingColumn' => $shortLeaveSettingsArr['Short_Leave_settings_Id'],
														'formName'       => $formName,
														'sessionId'      => $sessionId,
														'tableName'      => $this->_ehrTables->shortLeaveRequestSettings));
	}

	public function leaveCompOffMessage($shortTimeOffArr,$shiftDetails,$formName,$period)
	{
		$regularFrom 	= date($this->_orgDF['php'].' H:i:s',strtotime($shiftDetails['Regular_From']));
		$lunchBreakFrom = date($this->_orgDF['php'].' H:i:s',strtotime($shiftDetails['Lunch_Break_From']));
		$lunchBreakTo 	= date($this->_orgDF['php'].' H:i:s',strtotime($shiftDetails['Lunch_Break_To']));
		$regularTo 		= date($this->_orgDF['php'].' H:i:s',strtotime($shiftDetails['Regular_To']));
		$quarterLeaveDurationConfigured = $this->_dbCommonFun->isQuarterDurationConfigured();


		if($period=='First Half' && $shortTimeOffArr['Start_Date_Time'] >= $shiftDetails['Regular_From'] && $shortTimeOffArr['Start_Date_Time'] <= $shiftDetails['Lunch_Break_From'])
		{
			return array('success' => false, 'msg'=>$formName.' is applied for '.$period.' From :'.$regularFrom.' To: '.$lunchBreakFrom, 'type'=>'info');
		}
		else if($period=='Second Half' && $shortTimeOffArr['Start_Date_Time'] >= $shiftDetails['Lunch_Break_To'] && $shortTimeOffArr['Start_Date_Time'] <= $shiftDetails['Regular_To'])
		{
			return array('success' => false, 'msg'=>$formName.' is applied for '.$period.' From :'.$lunchBreakTo.' To: '.$regularTo, 'type'=>'info');
		}
		else if($period=='First Half' && $shortTimeOffArr['End_Date_Time'] >= $shiftDetails['Regular_From'] && $shortTimeOffArr['End_Date_Time'] <= $shiftDetails['Lunch_Break_From'])
		{
			return array('success' => false, 'msg'=>$formName.' is applied for '.$period.' From :'.$regularFrom.' To: '.$lunchBreakFrom, 'type'=>'info');
		}
		else if($period=='Second Half' && $shortTimeOffArr['End_Date_Time'] >= $shiftDetails['Lunch_Break_To'] && $shortTimeOffArr['End_Date_Time'] <= $shiftDetails['Regular_To'])
		{
			return array('success' => false, 'msg'=>$formName.' is applied for '.$period.' From :'.$lunchBreakTo.' To: '.$regularTo, 'type'=>'info');
		}
		else if(!empty($quarterLeaveDurationConfigured)){
			$midWorkTimeResult = $this->_dbAttendance->getLunchBreakHours(null,$shiftDetails['Regular_From'],$shiftDetails['Lunch_Break_From']);
			$firstHalfMidWorkTimeFrom 	= $midWorkTimeResult['Lunch_Break_From'];
			$firstHalfMidWorkTimeTo 	= $midWorkTimeResult['Lunch_Break_To'];

			$secondHalfMidWorkTimeResult = $this->_dbAttendance->getLunchBreakHours(null,$shiftDetails['Lunch_Break_To'],$shiftDetails['Regular_To']);
			$secondHalfMidWorkTimeFrom 	= $secondHalfMidWorkTimeResult['Lunch_Break_From'];
			$secondHalfMidWorkTimeTo 	= $secondHalfMidWorkTimeResult['Lunch_Break_To'];
			
			if($period=='First Quarter' && $shortTimeOffArr['Start_Date_Time'] >= $shiftDetails['Regular_From'] && $shortTimeOffArr['Start_Date_Time'] <= $firstHalfMidWorkTimeFrom)
			{
				return array('success' => false, 'msg'=>$formName.' is applied for '.$period.' From :'.$regularFrom.' To: '.$firstHalfMidWorkTimeFrom, 'type'=>'info');
			}
			else if($period=='Second Quarter' && $shortTimeOffArr['Start_Date_Time'] >= $firstHalfMidWorkTimeTo && $shortTimeOffArr['Start_Date_Time'] <= $shiftDetails['Lunch_Break_From'])
			{
				return array('success' => false, 'msg'=>$formName.' is applied for '.$period.' From :'.$firstHalfMidWorkTimeTo.' To: '.$lunchBreakFrom, 'type'=>'info');
			}
			else if($period=='Third Quarter' && $shortTimeOffArr['Start_Date_Time'] >= $shiftDetails['Lunch_Break_To'] && $shortTimeOffArr['Start_Date_Time'] <= $secondHalfMidWorkTimeFrom)
			{
				return array('success' => false, 'msg'=>$formName.' is applied for '.$period.' From :'.$lunchBreakTo.' To: '.$secondHalfMidWorkTimeFrom, 'type'=>'info');
			}
			else if($period=='Fourth Quarter' && $shortTimeOffArr['Start_Date_Time'] >= $secondHalfMidWorkTimeTo && $shortTimeOffArr['Start_Date_Time'] <= $shiftDetails['Regular_To'])
			{
				return array('success' => false, 'msg'=>$formName.' is applied for '.$period.' From :'.$secondHalfMidWorkTimeTo.' To: '.$regularTo, 'type'=>'info');
			}			
			else if($period=='First Quarter' && $shortTimeOffArr['End_Date_Time'] >= $shiftDetails['Regular_From'] && $shortTimeOffArr['End_Date_Time'] <= $firstHalfMidWorkTimeFrom)
			{
				return array('success' => false, 'msg'=>$formName.' is applied for '.$period.' From :'.$regularFrom.' To: '.$firstHalfMidWorkTimeFrom, 'type'=>'info');
			}
			else if($period=='Second Quarter' && $shortTimeOffArr['End_Date_Time'] >= $firstHalfMidWorkTimeTo && $shortTimeOffArr['End_Date_Time'] <= $shiftDetails['Lunch_Break_From'])
			{
				return array('success' => false, 'msg'=>$formName.' is applied for '.$period.' From :'.$firstHalfMidWorkTimeTo.' To: '.$lunchBreakFrom, 'type'=>'info');
			}
			else if($period=='Third Quarter' && $shortTimeOffArr['End_Date_Time'] >= $shiftDetails['Lunch_Break_To'] && $shortTimeOffArr['End_Date_Time'] <= $secondHalfMidWorkTimeFrom)
			{
				return array('success' => false, 'msg'=>$formName.' is applied for '.$period.' From :'.$lunchBreakTo.' To: '.$secondHalfMidWorkTimeFrom, 'type'=>'info');
			}
			else if($period=='Fourth Quarter' && $shortTimeOffArr['End_Date_Time'] >= $secondHalfMidWorkTimeTo && $shortTimeOffArr['End_Date_Time'] <= $shiftDetails['Regular_To'])
			{
				return array('success' => false, 'msg'=>$formName.' is applied for '.$period.' From :'.$secondHalfMidWorkTimeTo.' To: '.$regularTo, 'type'=>'info');
			}
			else
			{
				return  array('success' => true, 'msg'=>$formName.' is not applied for this duration', 'type'=>'success');
			}
		}
		else
		{
			return  array('success' => true, 'msg'=>$formName.' is not applied for this duration', 'type'=>'success');
		}
	}
	
	public function calculateShortTimeOffHours($startDateTime,$endDateTime)
	{
		$startDateTime 		= new DateTime($startDateTime); // First date and time
		$endDateTime		= new DateTime($endDateTime); // Second date and time
		$interval  		    = $startDateTime->diff($endDateTime); // Get the difference between the two dates
		$time_diff 			= $interval->format('%H:%I'); // Get the time difference in hours, minutes, and seconds format
		return $time_diff;
	}

	public function getShortTimeOffDetails($shortTimeOffIds){
		return $this->_db->fetchAll($this->_db->select()->from(array('STO'=>$this->_ehrTables->shortTimeOff),array('STO.*'))
				->joinLeft(array('EA'=>$this->_ehrTables->attendance),'STO.Employee_Id=EA.Employee_Id AND STO.Short_Time_Off_Date = EA.Attendance_Date AND EA.Auto_Short_Time_Off = "Yes"',
					array('Late_Attendance' => new Zend_Db_Expr('CASE WHEN STO.Reason = "Late Attendance" THEN "Yes" ELSE "No" END')))

				->joinLeft(array('EAS'=>$this->_ehrTables->employeeAttendanceSummary),'STO.Short_Time_Off_Id = EAS.Early_Checkout_Short_Time_Off_Id',
					array('Early_Checkout' => new Zend_Db_Expr('CASE WHEN EAS.Early_Checkout_Short_Time_Off_Id > 0 THEN "Yes" ELSE "No" END'),'EAS.Early_Checkout_Hours'))

				->where('STO.Short_Time_Off_Id IN (?)', $shortTimeOffIds));
	}

	public function initiateShortTimeOffWorkflow($shortTimeOffDetails, $sessionId){
		$shortTimeOffFormId = 352;
       	$eventId = $this->_dbCommonFun->getEventId($shortTimeOffFormId);

        if(empty($eventId)){
            return array('success' => false, 'msg' => 'The default workflow configuration does not exists', 'type' => 'warning', 'type' => 'warning');
        }

        /* Create an object */
        $instanceData = new \stdClass();
        $instanceData->formId = $shortTimeOffFormId;
        $instanceData->employeeId =(int)$shortTimeOffDetails['Employee_Id'];
        $instanceData->Employee_Id =(int)$shortTimeOffDetails['Employee_Id'];
        $instanceData->initiatorId =(int)$sessionId;
		$instanceData->Request_For=$shortTimeOffDetails['Request_For'];
		$instanceData->Short_Time_Off_Date=$shortTimeOffDetails['Short_Time_Off_Date'];
		$instanceData->Start_Date_Time=$shortTimeOffDetails['Start_Date_Time'];
        $instanceData->End_Date_Time=$shortTimeOffDetails['End_Date_Time'];
        $instanceData->Total_Hours=$shortTimeOffDetails['Total_Hours'];
        $instanceData->Reason= $shortTimeOffDetails['Reason'];
		$instanceData->Alternate_Person= $shortTimeOffDetails['Alternate_Person'];
		$instanceData->Contact_Details=$shortTimeOffDetails['Contact_Details'];
        $instanceData->Approval_Status=$shortTimeOffDetails['Approval_Status'];
		$instanceData->Added_On = $shortTimeOffDetails['Added_On'];
		$instanceData->Added_By = $shortTimeOffDetails['Added_By'];
        $instanceData->Updated_On=$shortTimeOffDetails['Updated_On'] ?? null;
        $instanceData->Updated_By=$shortTimeOffDetails['Updated_By'] ?? null;
		$instanceData->Late_Attendance = $shortTimeOffDetails['Late_Attendance'] ?? null;
        $instanceData->Early_Checkout=$shortTimeOffDetails['Early_Checkout'] ?? null;
        $instanceData->Early_Checkout_Hours=$shortTimeOffDetails['Early_Checkout_Hours'] ?? null;

        $dbLeave = new Employees_Model_DbTable_Leave();
        $newWorkFlowInitiatedId=$dbLeave->initiateWorkflowEngine($eventId,$instanceData,$sessionId);  
        
        if(empty($newWorkFlowInitiatedId)){
            return array('success' => false, 'msg' => 'Sorry, an error occured while initiating the workflow', 'type' => 'warning');
        }
        return array('success' => true, 'msg' => 'Workflow initiated successfully.', 'type' => 'success', 'newWorkFlowInitiatedId'=>$newWorkFlowInitiatedId);
    }

	public function validateShortTimeOffWorkflowEnabled(){
		$enableworkflow =  $this->_db->fetchOne($this->_db->select()->from(array('CS'=>$this->_ehrTables->compOffSettings),array('Enable_Workflow_For_Short_Time_Off')));
        $enableworkflow = $enableworkflow && strtolower($enableworkflow) == "yes";
		return $enableworkflow;
	}

	/**
	 * Calculate the short time off activation date for an employee
	 *
	 * This method determines when an employee becomes eligible to apply for short time off
	 * based on their date of joining and the configured activation period.
	 *
	 * @param int $employeeId Employee ID for whom to calculate activation date
	 * @param array $shortTimeOffSettings Short time off configuration settings
	 * @param string $employeeDateOfJoin Optional employee date of join (Y-m-d format)
	 * @param string $requestDate Optional specific request date for validation (Y-m-d format)
	 * @return array Activation date information with success status and calculated date
	 */
	public function getShortTimeOffActivationDate($employeeId, $shortTimeOffSettings, $employeeDateOfJoin = '', $requestDate = null)
	{
		// Extract activation days from settings with proper validation
		$shortTimeOffActivationDays = 0;
		if (isset($shortTimeOffSettings['Short_Time_Off_Activation_After'])) {
			$activationDays = $shortTimeOffSettings['Short_Time_Off_Activation_After'];
			if (is_numeric($activationDays) && $activationDays >= 0) {
				$shortTimeOffActivationDays = (int) $activationDays;
			}
		}
		
		// Get employee date of join if not provided
		if (empty($employeeDateOfJoin)) {
			try {
				$employeeDateOfJoin = $this->_db->fetchOne(
					$this->_db->select()
						->from($this->_ehrTables->empJob, array('Date_Of_Join'))
						->where('Employee_Id = ?', $employeeId)
				);
			} catch (Exception $e) {
				return array(
					'success' => false,
					'activationDate' => null,
					'message' => 'Error retrieving employee information',
					'isActivated' => false
				);
			}
		}

		// Validate employee date of join
		if (empty($employeeDateOfJoin)) {
			return array(
				'success' => false,
				'activationDate' => null,
				'message' => 'Employee date of join not found or employee is not active',
				'isActivated' => false
			);
		}

		// Calculate activation date using optimized date calculation
		try {
			$dateOfJoinTimestamp = strtotime($employeeDateOfJoin);
			$activationDate = date('Y-m-d', strtotime('+' . $shortTimeOffActivationDays . ' day', $dateOfJoinTimestamp));

			// Context-aware activation validation
			$isActivated = false;
			$contextualMessage = '';

			if (!empty($requestDate)) {
				// Request-specific validation: Compare request date against activation date
				$requestTimestamp = strtotime($requestDate);
				$isActivated = $requestTimestamp >= strtotime($activationDate);
				$activationDateFormatted = date('j M Y', strtotime($activationDate));
				$contextualMessage = $isActivated
					? ''
					: 'Short time off will be activated from ' . $activationDateFormatted . ' as per the rule';
			} 

			return array(
				'success' => true,
				'activationDate' => $activationDate,
				'message' => $contextualMessage,
				'isActivated' => $isActivated,
			);
		} catch (Exception $e) {
			return array(
				'success' => false,
				'activationDate' => null,
				'message' => 'Error in calculating short time off activation date. Please contact system administrator.',
				'isActivated' => false
			);
		}
	}
	
	public function __destruct()
    {
        
    }	

}

