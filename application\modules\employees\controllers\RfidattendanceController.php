<?php
//==========================================================================================
//==========================================================================================
/* Program : RfidattendanceController.php											      *
 * Property of Caprice Technologies Pvt Ltd,                                              *
 * Copyright (c) 2013-15 Caprice Technologies Pvt Ltd,                                    *
 * Coimbatore, Tamilnadu, India.														  *
 * All Rights Reserved.            														  *
 * Use of this material without the express consent of Caprice Technologies               *
 * or assignees is unlawful and subject to prosecution to the fullest extent of the law.  *
 *                                                                                    	  *
 * Description    : Rfidattendance is used for update employee's attendance through       *
 * Card scanner device or Finger Print scanner.                                           *
 *                                                                                   	  *
 *                                                                                    	  *
 * Revisions :                                                                    	      *
 *      Version       Date             Author               Description                   *
 *        1.0        08-April-2015    Prasanth            Initial Version         	      *
 *															                              */	
//==========================================================================================
//==========================================================================================
include APPLICATION_PATH."/validations/Validations.php";
class Employees_RfidattendanceController extends Zend_Controller_Action
{
    protected $_validation              = null;
    protected $_dbRfidAttendance        = null;
    protected $_importAccessRights    	= null;
    protected $_dbAccessRights          = null;
    protected $_logEmpId                = null;
    protected $_dbCommonFunction        = null;
    protected $_dbAttendance            = null;
	protected $_db  					= null;
    protected $_formNameA               = 'Attendance';

    public function init()
    {
        $this->_validation 	            = new Validations();
        $this->_dbRfidAttendance        = new Employees_Model_DbTable_Rfidattendance();
        $this->_dbAccessRights          = new Default_Model_DbTable_AccessRights();
        $this->_dbCommonFunction        = new Application_Model_DbTable_CommonFunction();
        $this->_dbAttendance            = new Employees_Model_DbTable_Attendance();
		$this->_db 						= Zend_Registry::get('subHrapp');
        $userSession                    = $this->_dbCommonFunction->getUserDetails ();
		//$this->_logEmpId                = 1;
		$this->_logEmpId                = 1;
        //$this->_importAccessRights  = $this->_dbAccessRights->employeeAccessRights($this->_logEmpId, $this->_formNameA);
        
    }

    /**
     *  index function used to 
     *
     */
    public function indexAction()
    {
        $this->_helper->layout()->disableLayout();
        
        $queryString = $_SERVER['QUERY_STRING'];
        $queryArr    = explode('$', $queryString);
        $queryArr    = explode('*', $queryArr[1]);
        $queryRecord = explode(',', $queryArr[0]);
        $requestURL  = $_SERVER['REDIRECT_URL'];
        $updateData  = array();
        $machineId   = $d = $orgId = 0;
        
        foreach ($queryRecord as $key => $row)
        {
            $queryData = explode('&', $row);
            switch (count($queryData))
            {
                case 4 :
                    $orgId     = $queryData[0];
                    $machineId = $queryData[1];
                    
                    $updateData[$d]['Request_String'] = $requestURL.'?$'.$orgId.'&'.$machineId.'&'.$queryData[2].'&'.$queryData[3].'*';
                    $updateData[$d]['ExternalEmpId'] = $queryData[2];
                    $updateData[$d]['DOT'] = $queryData[3];
                    break;
                
                case 2:
                    $updateData[$d]['Request_String'] = $requestURL.'?$'.$orgId.'&'.$machineId.'&'.$queryData[0].'&'.$queryData[1].'*';
                    $updateData[$d]['ExternalEmpId'] = $queryData[0];
                    $updateData[$d]['DOT'] = $queryData[1];
                    break;
            }
            
            ++$d;
        }
        $this->view->result = $this->_dbRfidAttendance->updateAttendance($orgId, $machineId, $updateData);
    }
    
    public function camsAttendanceAction()
    {
        $this->_helper->layout()->disableLayout();
        
        $stgId              = $_POST['stgid'];
        $employeeId         = $_POST['userid'];
        $addedOn            = $_POST['att_time'];
        $attendanceStatus   = $_POST['att_type'];
        
        $addedOn = date('Y-m-d H:i:s',$addedOn);
        
        if(!empty($stgId))
        {
            if(!empty($employeeId))
            {
                if(!empty($addedOn))
                {
                    if(!empty($attendanceStatus))
                    {
                        $attendanceDetails = array('Request_String'   =>$stgId.$employeeId.$attendanceStatus.$addedOn,
                                                   'Employee_Id'      =>$employeeId,
                                                   'Attendance_Status'=>$attendanceStatus,
                                                   'Added_On'         =>$addedOn);
                     
                        $this->view->result = $this->_dbRfidAttendance->updateCamAttendance($attendanceDetails);
                    }
                    else
                    {
                       $this->view->result = 'att_type is not received';
                    }
                }
                else
                {
                   $this->view->result = 'att_time is not received';
                }
            }
            else
            {
               $this->view->result = 'userid is not received';
            }
        }    
        else
        {
            $this->view->result = 'stgid is not received';
        }
    }

    public function showAttendanceMobileAction()
    {
        $this->_helper->layout()->disableLayout();
		$employeeId = $this->_getParam('employeeId', null);
		$employeeId = $this->_dbRfidAttendance->getEmployeeId($employeeId); 
		
		$this->_importAccessRights  = $this->_dbAccessRights->employeeAccessRights($employeeId, $this->_formNameA);
		
        if($this->_importAccessRights['Employee']['View']==1)
		{
                $sortField = $this->_getParam('iSortCol_0', null);
				$sortField = filter_var($sortField, FILTER_SANITIZE_NUMBER_INT);
				
				$sortOrder = $this->_getParam('sSortDir_0', null);
				$sortOrder = filter_var($sortOrder, FILTER_SANITIZE_STRIPPED);
				
				$page = $this->_getParam('iDisplayStart', null);
				$page = isset($page) ? intval($page) : 0;
				$page = filter_var($page, FILTER_SANITIZE_NUMBER_INT);
				
				$rows = $this->_getParam('iDisplayLength', null);
				$rows = isset($rows) ? intval($rows) : 10;
				$rows = filter_var($rows, FILTER_SANITIZE_NUMBER_INT);
                
				$searchAll = $this->_getParam('sSearch', null);
				$searchAll = filter_var($searchAll, FILTER_SANITIZE_STRIPPED);
				
				$employeeName = $this->_getParam('sSearch_0', null);
				$employeeName = filter_var($employeeName, FILTER_SANITIZE_STRIPPED);
				
				$employeeStartId = $this->_getParam('sSearch_1', null);
				$employeeStartId = filter_var($employeeStartId, FILTER_SANITIZE_STRIPPED);
				
				$employeeEndId = $this->_getParam('sSearch_2', null);
				$employeeEndId = filter_var($employeeEndId, FILTER_SANITIZE_STRIPPED);
				
				$beginDate = $this->_getParam('sSearch_3', null);
				$beginDate = filter_var($beginDate, FILTER_SANITIZE_STRIPPED);
				
				$endDate = $this->_getParam('sSearch_4', null);
				$endDate = filter_var($endDate, FILTER_SANITIZE_STRIPPED);
				
				$attendanceStatus = $this->_getParam('sSearch_5', null);
				$attendanceStatus = filter_var($attendanceStatus, FILTER_SANITIZE_STRIPPED);
				
				$rollupStatus = $this->_getParam('sSearch_6', null);
				$rollupStatus = filter_var($rollupStatus, FILTER_SANITIZE_STRIPPED);
				
				$finalExtEmpId = $this->_getParam('sSearch_7', null);
				$finalExtEmpId = filter_var($finalExtEmpId, FILTER_SANITIZE_STRIPPED);
				
				$searchArr = array( 'employeeName'     => $employeeName,
								    'employeeStartId'  => $employeeStartId,
									'employeeEndId'    => $employeeEndId,
									'beginDate'        => $beginDate,
									'endDate'          => $endDate,
									'attendanceStatus' => $attendanceStatus,
									'rollupStatus'     => $rollupStatus,
									'finalExtEmpId'    => $finalExtEmpId);
				
				$importAccess = array('Is_Manager' => $this->_importAccessRights['Employee']['Is_Manager'],
									  'Admin'      => $this->_importAccessRights['Admin'],
									  'LogId'      => $employeeId);
				
				//to display all the attendance records in the data grid
				
				//$this->view->result = $this->_dbAttendance->listAttendanceImport($page, $rows, $sortField, $sortOrder, $searchAll,
				//																 $searchArr, $importAccess);
				$this->view->result = $this->_dbAttendance->listAttendanceImport($page, $rows, 2, 'asc', $searchAll,
																				 $searchArr, $importAccess);
        } else {
			$this->view->result = array("iTotalRecords" => 0, "iTotalDisplayRecords" => 0, "aaData" => array());
		}
    }
	public function updateAttendanceImportAction()
    {
		$this->_helper->layout()->disableLayout();
		
		$body = $this->getRequest()->getRawBody();
		$queryRecord = Zend_Json::decode($body);
		
        $requestURL  = $_SERVER['REDIRECT_URL'];
        $updateData  = array();
        $machineId   = $d = $orgId = 0;
        
		if(!empty($queryRecord)){
			foreach ($queryRecord as $key => $queryData)
			{
				$date1 = new DateTime($queryData["PunchTime"]);
				$result1 = $date1->format('Y-m-d H:i:s');
			
				$orgId     = 1;
				$machineId = $queryData["MachineId"];
				$updateData[$d]['Request_String'] = $requestURL.'?$'.$orgId.'&'.$machineId.'&'.$queryData["EmployeeId"].'&'.$queryData["PunchTime"].'*';
				$updateData[$d]['ExternalEmpId'] = $queryData["EmployeeId"];
				$updateData[$d]['DOT'] = $result1;
				$updateData[$d]['id'] = $queryData["id"];
				$updateData[$d]['Status'] = $queryData["Status"];
				
				++$d;
			}
			
			$this->view->result = $this->_dbRfidAttendance->updateAttendance($orgId, $machineId, $updateData, 'api');
		} else{
			$this->view->result =  array();
		}
	}
	public function updateAttendanceFromDeviceAction()
    {
		$this->_helper->layout()->disableLayout();
		
		$body = $this->getRequest()->getRawBody();
		$queryRecord = Zend_Json::decode($body);
		
        $requestURL  = $_SERVER['REDIRECT_URL'];
        $updateData  = array();
        $d = 0;
		$machineId   = $orgId = 1;
		
        if(!empty($queryRecord)){
			foreach ($queryRecord as $key => $queryData)
			{
				$date1 = new DateTime($queryData["PunchTime"]);
				$result1 = $date1->format('Y-m-d H:i:s');
				
				$updateData[$d]['Request_String'] = $requestURL.'?$'.$orgId.'&'.$machineId.'&'.$queryData["EmployeeId"].'&'.$queryData["PunchTime"].'*';
				$updateData[$d]['ExternalEmpId'] = $queryData["EmployeeId"];
				$updateData[$d]['DOT'] = $result1;
				$updateData[$d]['Status'] = $queryData["Status"];
				
				++$d;
			}
			
			$this->view->result = $this->_dbRfidAttendance->updateAttendance($orgId, $machineId, $updateData, 'device');
		} else {
			$this->view->result =  array('FailedRecords'=>array());
		}
	}

    public function processAttendanceAction()
	{
		$this->_helper->layout()->disableLayout();
        $ajaxContext = $this->_helper->getHelper('AjaxContext');
        $ajaxContext->addActionContext('process-attendance', 'json')->initContext();
        if($this->getRequest()->isPost())
        {
            $formData = $this->getRequest()->getPost();
            if(empty($formData )){
                $requestBody =$this->getRequest()->getRawBody();
                if ($requestBody)
                {
                    $formData = Zend_Json::decode($requestBody);
                }
                else{								
                    $this->view->result = array('success'=>false, 'msg'=>'Invalid Data', 'type'=>'warning','additionalInfo'=>$requestBody);				
                }						
            }
            $superAdminId = $formData['superAdminId'];
            if(!empty($superAdminId))
            {
                $processMethod = $formData['Process_Method'];
                $processMethod = filter_var($processMethod, FILTER_SANITIZE_STRIPPED);

                $processFrom = $formData['Process_From'];
                $processFrom = filter_var($processFrom, FILTER_SANITIZE_STRIPPED);

                $processTo = $formData['Process_To'];
                $processTo = filter_var($processTo, FILTER_SANITIZE_STRIPPED);

                // Optional work schedule IDs for filtering
                $workScheduleIds = $formData['workScheduleIds'];

                $startDate = $this->_validation->dateValidation($processFrom);
                $endDate = $this->_validation->dateValidation($processTo);
                if(!empty($startDate['value']) && $startDate['valid'] && !empty($endDate['value']) && $endDate['valid'])
                {
                    $processedInput = array('Process_Method'=>$processMethod,
                                            'Process_From'=>$startDate['value'],
                                            'Process_To'=>$endDate['value']);

                    // Add work schedule IDs if provided
                    if(!empty($workScheduleIds) && is_array($workScheduleIds))
                    {
                        $sanitizedWorkScheduleIds = array();
                        foreach($workScheduleIds as $workScheduleId)
                        {
                            $sanitizedId = filter_var($workScheduleId, FILTER_SANITIZE_NUMBER_INT);
                            if(!empty($sanitizedId) && is_numeric($sanitizedId))
                            {
                                $sanitizedWorkScheduleIds[] = (int)$sanitizedId;
                            }
                        }

                        if(!empty($sanitizedWorkScheduleIds))
                        {
                            $processedInput['workScheduleIds'] = $sanitizedWorkScheduleIds;
                        }
                    }

                    $this->view->result = $this->_dbAttendance->processAttendanceImportData($processedInput, $superAdminId);
                }
                else
                {
                    $this->view->result = array('success'=>false, 'msg'=>'Please choose from and to date', 'type'=>'warning','additionalInfo'=>$processFrom.'-'.$processTo);
                }
            }
            else
            {
                $this->view->result = array('success'=>false, 'msg'=>'Super admin employee id should not be passed as empty', 'type'=>'danger','additionalInfo'=>$superAdminId);
            }
        }
        else
        {
            $this->view->result = array('success'=>false, 'msg'=>'Invalid request', 'type'=>'danger','additionalInfo'=>$this->getRequest()->isPost());
        }
	}
	
}

